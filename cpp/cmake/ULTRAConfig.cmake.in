# ULTRA Configuration CMake Template
#
# This template is used to create the ULTRAConfig.cmake file
# that is used by client projects to find and use ULTRA.

@PACKAGE_INIT@

include(CMakeFindDependencyMacro)

# Find the same dependencies that ULTRA needs
find_dependency(Eigen3 3.3 REQUIRED NO_MODULE)
find_dependency(FFTW3 REQUIRED)
find_dependency(Boost 1.65 REQUIRED COMPONENTS system filesystem program_options serialization)
find_dependency(TBB REQUIRED)

# Optional dependencies
if(@CUDA_FOUND@)
    find_dependency(CUDA)
endif()

if(@MPI_FOUND@)
    find_dependency(MPI)
endif()

# Include the exported targets
include("${CMAKE_CURRENT_LIST_DIR}/ULTRATargets.cmake")

# Set variables for the libraries
set(ULTRA_INCLUDE_DIRS "@PACKAGE_CMAKE_INSTALL_INCLUDEDIR@/ultra")
set(ULTRA_LIBRARIES ULTRA::ultra)

# Component-specific libraries
set(ULTRA_CORE_NEURAL_LIBRARY ULTRA::core_neural)
set(ULTRA_HYPER_TRANSFORMER_LIBRARY ULTRA::hyper_transformer)
set(ULTRA_DIFFUSION_REASONING_LIBRARY ULTRA::diffusion_reasoning)
set(ULTRA_META_COGNITIVE_LIBRARY ULTRA::meta_cognitive)
set(ULTRA_NEUROMORPHIC_PROCESSING_LIBRARY ULTRA::neuromorphic_processing)
set(ULTRA_EMERGENT_CONSCIOUSNESS_LIBRARY ULTRA::emergent_consciousness)
set(ULTRA_NEURO_SYMBOLIC_LIBRARY ULTRA::neuro_symbolic)
set(ULTRA_SELF_EVOLUTION_LIBRARY ULTRA::self_evolution)
set(ULTRA_UTILS_LIBRARY ULTRA::utils)
set(ULTRA_INTEGRATION_LIBRARY ULTRA::integration)

# Check if the package was found
check_required_components(ULTRA)