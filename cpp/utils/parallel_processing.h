// Ultra/cpp/utils/parallel_processing.h

#ifndef ULTRA_UTILS_PARALLEL_PROCESSING_H
#define ULTRA_UTILS_PARALLEL_PROCESSING_H

#include <thread>
#include <future>
#include <atomic>
#include <mutex>
#include <shared_mutex>
#include <condition_variable>
#include <queue>
#include <deque>
#include <vector>
#include <array>
#include <memory>
#include <functional>
#include <algorithm>
#include <numeric>
#include <chrono>
#include <random>
#include <exception>
#include <type_traits>
#include <utility>
#include <tuple>
#include <map>
#include <unordered_map>
#include <set>
#include <string>

#include <Eigen/Dense>
#include <Eigen/Sparse>

#ifdef ULTRA_USE_OPENMP
#include <omp.h>
#endif

#ifdef ULTRA_USE_TBB
#include <tbb/parallel_for.h>
#include <tbb/parallel_reduce.h>
#include <tbb/parallel_scan.h>
#include <tbb/parallel_sort.h>
#include <tbb/task_scheduler_init.h>
#include <tbb/task_group.h>
#include <tbb/flow_graph.h>
#include <tbb/concurrent_queue.h>
#include <tbb/concurrent_vector.h>
#include <tbb/concurrent_hash_map.h>
#include <tbb/spin_mutex.h>
#include <tbb/spin_rw_mutex.h>
#include <tbb/cache_aligned_allocator.h>
#endif

#ifdef ULTRA_USE_MPI
#include <mpi.h>
#endif

#ifdef ULTRA_USE_CUDA
#include <cuda_runtime.h>
#include <thrust/device_vector.h>
#include <thrust/host_vector.h>
#include <thrust/transform.h>
#include <thrust/reduce.h>
#include <thrust/scan.h>
#include <thrust/sort.h>
#include <thrust/execution_policy.h>
#include <cooperative_groups.h>
#endif

// SIMD intrinsics
#if defined(__AVX512F__)
#include <immintrin.h>
#define ULTRA_SIMD_WIDTH 16
#elif defined(__AVX2__)
#include <immintrin.h>
#define ULTRA_SIMD_WIDTH 8
#elif defined(__AVX__)
#include <immintrin.h>
#define ULTRA_SIMD_WIDTH 8
#elif defined(__SSE4_2__)
#include <nmmintrin.h>
#define ULTRA_SIMD_WIDTH 4
#elif defined(__SSE2__)
#include <emmintrin.h>
#define ULTRA_SIMD_WIDTH 4
#elif defined(__ARM_NEON)
#include <arm_neon.h>
#define ULTRA_SIMD_WIDTH 4
#else
#define ULTRA_SIMD_WIDTH 1
#endif

namespace ultra {
namespace utils {
namespace parallel_processing {

// Forward declarations
class ThreadPool;
class WorkStealingQueue;
class LockFreeQueue;
class TaskScheduler;
class ParallelAlgorithms;
class DistributedComputing;
class GPUParallelProcessing;
class SIMDOperations;
class MemoryPool;
class LoadBalancer;
class PipelineProcessor;
class EventDrivenProcessor;
class RealtimeScheduler;

/**
 * @brief Enumeration of parallel execution policies
 */
enum class ExecutionPolicy {
    SEQUENTIAL,           // Sequential execution
    PARALLEL_STD,         // Standard library parallel algorithms
    PARALLEL_OPENMP,      // OpenMP parallel execution
    PARALLEL_TBB,         // Intel TBB parallel execution
    PARALLEL_CUDA,        // CUDA GPU parallel execution
    PARALLEL_DISTRIBUTED, // Distributed parallel execution (MPI)
    PARALLEL_HYBRID,      // Hybrid CPU-GPU execution
    PARALLEL_ADAPTIVE     // Adaptive execution policy selection
};

/**
 * @brief Enumeration of scheduling policies
 */
enum class SchedulingPolicy {
    ROUND_ROBIN,          // Round-robin scheduling
    WORK_STEALING,        // Work-stealing scheduling
    PRIORITY_BASED,       // Priority-based scheduling
    LOAD_BALANCING,       // Load balancing scheduling
    LOCALITY_AWARE,       // Cache/memory locality aware
    ENERGY_EFFICIENT,     // Energy-efficient scheduling
    REAL_TIME,           // Real-time scheduling
    ADAPTIVE             // Adaptive scheduling
};

/**
 * @brief Enumeration of synchronization primitives
 */
enum class SynchronizationType {
    MUTEX,               // Standard mutex
    SHARED_MUTEX,        // Shared mutex (reader-writer)
    SPIN_LOCK,           // Spin lock
    ATOMIC,              // Atomic operations
    CONDITION_VARIABLE,  // Condition variable
    BARRIER,             // Thread barrier
    SEMAPHORE,           // Semaphore
    LOCK_FREE            // Lock-free synchronization
};

/**
 * @brief Configuration for parallel processing
 */
struct ParallelConfig {
    ExecutionPolicy execution_policy = ExecutionPolicy::PARALLEL_ADAPTIVE;
    SchedulingPolicy scheduling_policy = SchedulingPolicy::WORK_STEALING;
    SynchronizationType sync_type = SynchronizationType::LOCK_FREE;
    
    // Thread configuration
    int num_threads = 0;              // 0 = hardware concurrency
    int max_threads = 1024;           // Maximum number of threads
    bool hyperthreading = true;       // Use hyperthreading
    bool numa_aware = true;           // NUMA-aware allocation
    
    // Task configuration
    size_t min_task_size = 1000;      // Minimum task size for parallelization
    size_t max_task_size = 1000000;   // Maximum task size
    int task_queue_size = 10000;      // Task queue size
    bool work_stealing = true;        // Enable work stealing
    
    // Memory configuration
    size_t memory_pool_size = 1024 * 1024 * 1024; // 1GB memory pool
    bool cache_aligned = true;        // Use cache-aligned allocation
    int cache_line_size = 64;         // Cache line size in bytes
    
    // Performance configuration
    bool auto_vectorization = true;   // Enable auto-vectorization
    bool prefetching = true;          // Enable memory prefetching
    bool branch_prediction = true;    // Optimize branch prediction
    
    // GPU configuration
    int gpu_device_id = 0;            // GPU device ID
    size_t gpu_memory_limit = 0;      // GPU memory limit (0 = auto)
    bool unified_memory = false;      // Use CUDA unified memory
    
    // Distributed configuration
    int mpi_rank = 0;                 // MPI rank
    int mpi_size = 1;                 // MPI size
    std::string mpi_communicator = "MPI_COMM_WORLD"; // MPI communicator
    
    // Profiling and debugging
    bool enable_profiling = false;    // Enable performance profiling
    bool enable_debugging = false;    // Enable debugging features
    std::string log_level = "INFO";   // Logging level
    
    ParallelConfig() = default;
};

/**
 * @brief Task representation for parallel execution
 */
template<typename Result = void>
class Task {
public:
    using TaskFunction = std::function<Result()>;
    using Priority = int;
    
    /**
     * @brief Constructor
     * 
     * @param func Task function
     * @param priority Task priority (higher = more important)
     * @param dependencies Task dependencies
     */
    Task(TaskFunction func, Priority priority = 0, 
         const std::vector<std::shared_ptr<Task>>& dependencies = {})
        : function_(std::move(func)), priority_(priority), 
          dependencies_(dependencies), id_(generate_id()) {}
    
    /**
     * @brief Execute the task
     * 
     * @return Task result
     */
    Result execute() {
        if constexpr (std::is_void_v<Result>) {
            function_();
            executed_.store(true);
        } else {
            auto result = function_();
            executed_.store(true);
            return result;
        }
    }
    
    /**
     * @brief Check if task is ready to execute
     * 
     * @return True if all dependencies are satisfied
     */
    bool is_ready() const {
        return std::all_of(dependencies_.begin(), dependencies_.end(),
                          [](const auto& dep) { return dep->is_executed(); });
    }
    
    /**
     * @brief Check if task has been executed
     * 
     * @return True if executed
     */
    bool is_executed() const {
        return executed_.load();
    }
    
    /**
     * @brief Get task priority
     * 
     * @return Priority value
     */
    Priority get_priority() const {
        return priority_;
    }
    
    /**
     * @brief Get task ID
     * 
     * @return Unique task ID
     */
    uint64_t get_id() const {
        return id_;
    }
    
    /**
     * @brief Get dependencies
     * 
     * @return Vector of task dependencies
     */
    const std::vector<std::shared_ptr<Task>>& get_dependencies() const {
        return dependencies_;
    }
    
    /**
     * @brief Set completion callback
     * 
     * @param callback Callback function
     */
    void set_completion_callback(std::function<void()> callback) {
        completion_callback_ = std::move(callback);
    }
    
    /**
     * @brief Call completion callback
     */
    void notify_completion() {
        if (completion_callback_) {
            completion_callback_();
        }
    }

private:
    TaskFunction function_;
    Priority priority_;
    std::vector<std::shared_ptr<Task>> dependencies_;
    std::atomic<bool> executed_{false};
    uint64_t id_;
    std::function<void()> completion_callback_;
    
    static uint64_t generate_id() {
        static std::atomic<uint64_t> counter{0};
        return counter.fetch_add(1);
    }
};

/**
 * @brief Lock-free queue implementation for high-performance task scheduling
 */
template<typename T>
class LockFreeQueue {
public:
    /**
     * @brief Constructor
     * 
     * @param capacity Queue capacity (must be power of 2)
     */
    explicit LockFreeQueue(size_t capacity = 1024)
        : capacity_(next_power_of_two(capacity)), mask_(capacity_ - 1),
          buffer_(new Node[capacity_]), head_(0), tail_(0) {
        
        // Initialize all nodes
        for (size_t i = 0; i < capacity_; ++i) {
            buffer_[i].sequence.store(i, std::memory_order_relaxed);
        }
    }
    
    /**
     * @brief Destructor
     */
    ~LockFreeQueue() {
        delete[] buffer_;
    }
    
    /**
     * @brief Try to enqueue an item
     * 
     * @param item Item to enqueue
     * @return True if successful
     */
    bool try_enqueue(T item) {
        Node* node;
        size_t pos = tail_.load(std::memory_order_relaxed);
        
        while (true) {
            node = &buffer_[pos & mask_];
            size_t seq = node->sequence.load(std::memory_order_acquire);
            intptr_t diff = (intptr_t)seq - (intptr_t)pos;
            
            if (diff == 0) {
                if (tail_.compare_exchange_weak(pos, pos + 1, std::memory_order_relaxed)) {
                    break;
                }
            } else if (diff < 0) {
                return false; // Queue is full
            } else {
                pos = tail_.load(std::memory_order_relaxed);
            }
        }
        
        node->data = std::move(item);
        node->sequence.store(pos + 1, std::memory_order_release);
        return true;
    }
    
    /**
     * @brief Try to dequeue an item
     * 
     * @param item Output item
     * @return True if successful
     */
    bool try_dequeue(T& item) {
        Node* node;
        size_t pos = head_.load(std::memory_order_relaxed);
        
        while (true) {
            node = &buffer_[pos & mask_];
            size_t seq = node->sequence.load(std::memory_order_acquire);
            intptr_t diff = (intptr_t)seq - (intptr_t)(pos + 1);
            
            if (diff == 0) {
                if (head_.compare_exchange_weak(pos, pos + 1, std::memory_order_relaxed)) {
                    break;
                }
            } else if (diff < 0) {
                return false; // Queue is empty
            } else {
                pos = head_.load(std::memory_order_relaxed);
            }
        }
        
        item = std::move(node->data);
        node->sequence.store(pos + mask_ + 1, std::memory_order_release);
        return true;
    }
    
    /**
     * @brief Check if queue is empty
     * 
     * @return True if empty
     */
    bool empty() const {
        return head_.load(std::memory_order_acquire) == 
               tail_.load(std::memory_order_acquire);
    }
    
    /**
     * @brief Get approximate size
     * 
     * @return Approximate queue size
     */
    size_t size() const {
        size_t head = head_.load(std::memory_order_acquire);
        size_t tail = tail_.load(std::memory_order_acquire);
        return tail >= head ? tail - head : 0;
    }

private:
    struct Node {
        std::atomic<size_t> sequence;
        T data;
    };
    
    static size_t next_power_of_two(size_t n) {
        n--;
        n |= n >> 1;
        n |= n >> 2;
        n |= n >> 4;
        n |= n >> 8;
        n |= n >> 16;
        n |= n >> 32;
        return ++n;
    }
    
    const size_t capacity_;
    const size_t mask_;
    Node* const buffer_;
    
    alignas(64) std::atomic<size_t> head_;
    alignas(64) std::atomic<size_t> tail_;
};

/**
 * @brief Work-stealing queue for task scheduling
 */
template<typename T>
class WorkStealingQueue {
public:
    /**
     * @brief Constructor
     * 
     * @param capacity Initial capacity
     */
    explicit WorkStealingQueue(size_t capacity = 1024)
        : capacity_(capacity), mask_(capacity - 1), buffer_(new T[capacity]),
          top_(0), bottom_(0) {}
    
    /**
     * @brief Destructor
     */
    ~WorkStealingQueue() {
        delete[] buffer_;
    }
    
    /**
     * @brief Push item to bottom of queue (owner thread only)
     * 
     * @param item Item to push
     */
    void push(T item) {
        size_t b = bottom_.load(std::memory_order_relaxed);
        size_t t = top_.load(std::memory_order_acquire);
        
        if (b - t > mask_) {
            // Queue is full, resize
            resize();
            b = bottom_.load(std::memory_order_relaxed);
            t = top_.load(std::memory_order_acquire);
        }
        
        buffer_[b & mask_] = std::move(item);
        std::atomic_thread_fence(std::memory_order_release);
        bottom_.store(b + 1, std::memory_order_relaxed);
    }
    
    /**
     * @brief Pop item from bottom of queue (owner thread only)
     * 
     * @param item Output item
     * @return True if successful
     */
    bool pop(T& item) {
        size_t b = bottom_.load(std::memory_order_relaxed) - 1;
        bottom_.store(b, std::memory_order_relaxed);
        std::atomic_thread_fence(std::memory_order_seq_cst);
        
        size_t t = top_.load(std::memory_order_relaxed);
        
        if (t <= b) {
            item = std::move(buffer_[b & mask_]);
            if (t == b) {
                if (!top_.compare_exchange_strong(t, t + 1, 
                    std::memory_order_seq_cst, std::memory_order_relaxed)) {
                    bottom_.store(b + 1, std::memory_order_relaxed);
                    return false;
                }
                bottom_.store(b + 1, std::memory_order_relaxed);
            }
            return true;
        } else {
            bottom_.store(b + 1, std::memory_order_relaxed);
            return false;
        }
    }
    
    /**
     * @brief Steal item from top of queue (worker threads)
     * 
     * @param item Output item
     * @return True if successful
     */
    bool steal(T& item) {
        size_t t = top_.load(std::memory_order_acquire);
        std::atomic_thread_fence(std::memory_order_seq_cst);
        size_t b = bottom_.load(std::memory_order_acquire);
        
        if (t < b) {
            item = std::move(buffer_[t & mask_]);
            if (!top_.compare_exchange_strong(t, t + 1,
                std::memory_order_seq_cst, std::memory_order_relaxed)) {
                return false;
            }
            return true;
        }
        
        return false;
    }
    
    /**
     * @brief Check if queue is empty
     * 
     * @return True if empty
     */
    bool empty() const {
        size_t b = bottom_.load(std::memory_order_relaxed);
        size_t t = top_.load(std::memory_order_relaxed);
        return b <= t;
    }
    
    /**
     * @brief Get approximate size
     * 
     * @return Approximate queue size
     */
    size_t size() const {
        size_t b = bottom_.load(std::memory_order_relaxed);
        size_t t = top_.load(std::memory_order_relaxed);
        return b >= t ? b - t : 0;
    }

private:
    void resize() {
        std::lock_guard<std::mutex> lock(resize_mutex_);
        
        size_t new_capacity = capacity_ * 2;
        T* new_buffer = new T[new_capacity];
        size_t new_mask = new_capacity - 1;
        
        size_t b = bottom_.load(std::memory_order_relaxed);
        size_t t = top_.load(std::memory_order_relaxed);
        
        for (size_t i = t; i < b; ++i) {
            new_buffer[i & new_mask] = std::move(buffer_[i & mask_]);
        }
        
        delete[] buffer_;
        buffer_ = new_buffer;
        capacity_ = new_capacity;
        mask_ = new_mask;
    }
    
    size_t capacity_;
    size_t mask_;
    T* buffer_;
    
    alignas(64) std::atomic<size_t> top_;
    alignas(64) std::atomic<size_t> bottom_;
    std::mutex resize_mutex_;
};

/**
 * @brief Advanced thread pool with work stealing and priority scheduling
 */
class ThreadPool {
public:
    using Task = std::function<void()>;
    using TaskPtr = std::shared_ptr<Task>;
    
    /**
     * @brief Constructor
     * 
     * @param config Parallel processing configuration
     */
    explicit ThreadPool(const ParallelConfig& config = ParallelConfig());
    
    /**
     * @brief Destructor
     */
    ~ThreadPool();
    
    /**
     * @brief Initialize the thread pool
     * 
     * @return True if successful
     */
    bool initialize();
    
    /**
     * @brief Shutdown the thread pool
     */
    void shutdown();
    
    /**
     * @brief Submit a task for execution
     * 
     * @tparam F Function type
     * @tparam Args Argument types
     * @param f Function to execute
     * @param args Function arguments
     * @return Future for the result
     */
    template<typename F, typename... Args>
    auto submit(F&& f, Args&&... args) -> std::future<std::invoke_result_t<F, Args...>> {
        using ReturnType = std::invoke_result_t<F, Args...>;
        
        auto task = std::make_shared<std::packaged_task<ReturnType()>>(
            std::bind(std::forward<F>(f), std::forward<Args>(args)...)
        );
        
        auto future = task->get_future();
        
        auto work = [task]() { (*task)(); };
        
        if (!enqueue_task(std::move(work))) {
            throw std::runtime_error("Failed to enqueue task");
        }
        
        return future;
    }
    
    /**
     * @brief Submit a priority task
     * 
     * @tparam F Function type
     * @tparam Args Argument types
     * @param priority Task priority
     * @param f Function to execute
     * @param args Function arguments
     * @return Future for the result
     */
    template<typename F, typename... Args>
    auto submit_priority(int priority, F&& f, Args&&... args) 
        -> std::future<std::invoke_result_t<F, Args...>> {
        
        using ReturnType = std::invoke_result_t<F, Args...>;
        
        auto task = std::make_shared<std::packaged_task<ReturnType()>>(
            std::bind(std::forward<F>(f), std::forward<Args>(args)...)
        );
        
        auto future = task->get_future();
        
        auto work = [task]() { (*task)(); };
        
        if (!enqueue_priority_task(priority, std::move(work))) {
            throw std::runtime_error("Failed to enqueue priority task");
        }
        
        return future;
    }
    
    /**
     * @brief Submit a batch of tasks
     * 
     * @param tasks Vector of tasks
     * @return Vector of futures
     */
    std::vector<std::future<void>> submit_batch(const std::vector<Task>& tasks);
    
    /**
     * @brief Wait for all pending tasks to complete
     */
    void wait_for_all();
    
    /**
     * @brief Get number of worker threads
     * 
     * @return Number of threads
     */
    size_t get_thread_count() const;
    
    /**
     * @brief Get number of pending tasks
     * 
     * @return Number of pending tasks
     */
    size_t get_pending_tasks() const;
    
    /**
     * @brief Get thread pool statistics
     * 
     * @return Statistics map
     */
    std::map<std::string, double> get_statistics() const;
    
    /**
     * @brief Set thread affinity
     * 
     * @param thread_id Thread ID
     * @param cpu_set CPU set for affinity
     */
    void set_thread_affinity(size_t thread_id, const std::vector<int>& cpu_set);
    
    /**
     * @brief Enable/disable work stealing
     * 
     * @param enable Enable work stealing
     */
    void set_work_stealing(bool enable);
    
    /**
     * @brief Resize thread pool
     * 
     * @param new_size New number of threads
     */
    void resize(size_t new_size);

private:
    struct PriorityTask {
        int priority;
        Task task;
        
        bool operator<(const PriorityTask& other) const {
            return priority < other.priority; // Higher priority = lower value
        }
    };
    
    struct WorkerThread {
        std::thread thread;
        WorkStealingQueue<Task> local_queue;
        std::atomic<bool> active{true};
        std::atomic<size_t> tasks_executed{0};
        std::atomic<size_t> tasks_stolen{0};
        std::atomic<size_t> steal_attempts{0};
        
        WorkerThread() : local_queue(1024) {}
    };
    
    ParallelConfig config_;
    std::vector<std::unique_ptr<WorkerThread>> workers_;
    std::priority_queue<PriorityTask> priority_queue_;
    LockFreeQueue<Task> global_queue_;
    
    std::atomic<bool> shutdown_requested_{false};
    std::atomic<size_t> active_tasks_{0};
    std::condition_variable completion_cv_;
    std::mutex completion_mutex_;
    std::mutex priority_queue_mutex_;
    
    // Performance counters
    std::atomic<size_t> total_tasks_submitted_{0};
    std::atomic<size_t> total_tasks_completed_{0};
    std::atomic<size_t> total_steals_{0};
    std::chrono::high_resolution_clock::time_point start_time_;
    
    // Thread management
    void worker_loop(size_t worker_id);
    bool try_execute_task(size_t worker_id);
    bool try_steal_task(size_t worker_id);
    bool enqueue_task(Task task);
    bool enqueue_priority_task(int priority, Task task);
    void notify_task_completion();
    
    // NUMA-aware initialization
    void setup_numa_topology();
    void bind_thread_to_numa_node(std::thread& thread, int numa_node);
};

/**
 * @brief Task scheduler with dependency management and topological sorting
 */
class TaskScheduler {
public:
    using TaskNode = std::shared_ptr<Task<void>>;
    using TaskGraph = std::map<uint64_t, TaskNode>;
    using DependencyGraph = std::map<uint64_t, std::vector<uint64_t>>;
    
    /**
     * @brief Constructor
     * 
     * @param thread_pool Thread pool for task execution
     */
    explicit TaskScheduler(std::shared_ptr<ThreadPool> thread_pool);
    
    /**
     * @brief Add task to scheduler
     * 
     * @param task Task to add
     * @return Task ID
     */
    uint64_t add_task(TaskNode task);
    
    /**
     * @brief Add dependency between tasks
     * 
     * @param dependent_id ID of dependent task
     * @param dependency_id ID of dependency task
     */
    void add_dependency(uint64_t dependent_id, uint64_t dependency_id);
    
    /**
     * @brief Schedule and execute all tasks
     * 
     * @return True if all tasks completed successfully
     */
    bool execute_all();
    
    /**
     * @brief Schedule tasks using topological sort
     * 
     * @return Execution order of task IDs
     */
    std::vector<uint64_t> topological_sort();
    
    /**
     * @brief Get critical path through task graph
     * 
     * @return Critical path task IDs and total time
     */
    std::pair<std::vector<uint64_t>, double> get_critical_path();
    
    /**
     * @brief Optimize task scheduling
     * 
     * @param optimization_type Type of optimization ("makespan", "energy", "memory")
     */
    void optimize_schedule(const std::string& optimization_type = "makespan");
    
    /**
     * @brief Clear all tasks and dependencies
     */
    void clear();
    
    /**
     * @brief Get task execution statistics
     * 
     * @return Statistics map
     */
    std::map<std::string, double> get_execution_statistics() const;
    
    /**
     * @brief Visualize task dependency graph
     * 
     * @param output_file Output file for graph visualization
     */
    void visualize_dependency_graph(const std::string& output_file) const;

private:
    std::shared_ptr<ThreadPool> thread_pool_;
    TaskGraph tasks_;
    DependencyGraph dependencies_;
    DependencyGraph reverse_dependencies_;
    std::mutex scheduler_mutex_;
    
    // Task execution tracking
    std::map<uint64_t, std::chrono::high_resolution_clock::time_point> start_times_;
    std::map<uint64_t, std::chrono::high_resolution_clock::time_point> end_times_;
    std::map<uint64_t, double> execution_costs_;
    
    // Helper methods
    bool has_cycle() const;
    std::vector<uint64_t> kahn_topological_sort() const;
    double estimate_task_cost(uint64_t task_id) const;
    void update_execution_statistics(uint64_t task_id, 
                                   const std::chrono::high_resolution_clock::time_point& start,
                                   const std::chrono::high_resolution_clock::time_point& end);
};

/**
 * @brief Parallel algorithms for common operations
 */
class ParallelAlgorithms {
public:
    /**
     * @brief Constructor
     * 
     * @param config Parallel processing configuration
     */
    explicit ParallelAlgorithms(const ParallelConfig& config = ParallelConfig());
    
    // Parallel reduction operations
    
    /**
     * @brief Parallel reduce with custom binary operation
     * 
     * @tparam Iterator Iterator type
     * @tparam T Value type
     * @tparam BinaryOp Binary operation type
     * @param first Begin iterator
     * @param last End iterator
     * @param init Initial value
     * @param binary_op Binary operation
     * @return Reduced value
     */
    template<typename Iterator, typename T, typename BinaryOp>
    T parallel_reduce(Iterator first, Iterator last, T init, BinaryOp binary_op) {
        const size_t length = std::distance(first, last);
        
        if (length < config_.min_task_size) {
            return std::reduce(first, last, init, binary_op);
        }
        
        const size_t num_threads = std::min(
            static_cast<size_t>(config_.num_threads > 0 ? config_.num_threads : std::thread::hardware_concurrency()),
            length / config_.min_task_size
        );
        
        if (num_threads <= 1) {
            return std::reduce(first, last, init, binary_op);
        }
        
        const size_t chunk_size = length / num_threads;
        std::vector<std::future<T>> futures;
        futures.reserve(num_threads);
        
        // Launch parallel tasks
        auto current = first;
        for (size_t i = 0; i < num_threads - 1; ++i) {
            auto chunk_end = std::next(current, chunk_size);
            futures.emplace_back(std::async(std::launch::async, [=]() {
                return std::reduce(current, chunk_end, T{}, binary_op);
            }));
            current = chunk_end;
        }
        
        // Handle last chunk (may be larger due to remainder)
        futures.emplace_back(std::async(std::launch::async, [=]() {
            return std::reduce(current, last, T{}, binary_op);
        }));
        
        // Collect results
        T result = init;
        for (auto& future : futures) {
            result = binary_op(result, future.get());
        }
        
        return result;
    }
    
    /**
     * @brief Parallel sum
     * 
     * @tparam Iterator Iterator type
     * @param first Begin iterator
     * @param last End iterator
     * @return Sum of elements
     */
    template<typename Iterator>
    auto parallel_sum(Iterator first, Iterator last) {
        using ValueType = typename std::iterator_traits<Iterator>::value_type;
        return parallel_reduce(first, last, ValueType{}, std::plus<ValueType>{});
    }
    
    /**
     * @brief Parallel dot product
     * 
     * @param a First vector
     * @param b Second vector
     * @return Dot product
     */
    double parallel_dot_product(const Eigen::VectorXd& a, const Eigen::VectorXd& b);
    
    /**
     * @brief Parallel matrix-vector multiplication
     * 
     * @param matrix Input matrix
     * @param vector Input vector
     * @return Result vector
     */
    Eigen::VectorXd parallel_matrix_vector_multiply(const Eigen::MatrixXd& matrix, 
                                                   const Eigen::VectorXd& vector);
    
    /**
     * @brief Parallel matrix multiplication
     * 
     * @param a First matrix
     * @param b Second matrix
     * @return Product matrix
     */
    Eigen::MatrixXd parallel_matrix_multiply(const Eigen::MatrixXd& a, const Eigen::MatrixXd& b);
    
    // Parallel transformation operations
    
    /**
     * @brief Parallel transform with unary operation
     * 
     * @tparam InputIterator Input iterator type
     * @tparam OutputIterator Output iterator type
     * @tparam UnaryOp Unary operation type
     * @param first Begin input iterator
     * @param last End input iterator
     * @param result Begin output iterator
     * @param unary_op Unary operation
     * @return End output iterator
     */
    template<typename InputIterator, typename OutputIterator, typename UnaryOp>
    OutputIterator parallel_transform(InputIterator first, InputIterator last,
                                    OutputIterator result, UnaryOp unary_op) {
        const size_t length = std::distance(first, last);
        
        if (length < config_.min_task_size) {
            return std::transform(first, last, result, unary_op);
        }
        
        const size_t num_threads = std::min(
            static_cast<size_t>(config_.num_threads > 0 ? config_.num_threads : std::thread::hardware_concurrency()),
            length / config_.min_task_size
        );
        
        if (num_threads <= 1) {
            return std::transform(first, last, result, unary_op);
        }
        
        const size_t chunk_size = length / num_threads;
        std::vector<std::future<void>> futures;
        futures.reserve(num_threads);
        
        // Launch parallel tasks
        auto input_it = first;
        auto output_it = result;
        
        for (size_t i = 0; i < num_threads - 1; ++i) {
            auto input_end = std::next(input_it, chunk_size);
            auto output_end = std::next(output_it, chunk_size);
            
            futures.emplace_back(std::async(std::launch::async, [=]() mutable {
                std::transform(input_it, input_end, output_it, unary_op);
            }));
            
            input_it = input_end;
            output_it = output_end;
        }
        
        // Handle last chunk
        futures.emplace_back(std::async(std::launch::async, [=]() mutable {
            std::transform(input_it, last, output_it, unary_op);
        }));
        
        // Wait for completion
        for (auto& future : futures) {
            future.wait();
        }
        
        return std::next(result, length);
    }
    
    /**
     * @brief Parallel for-each operation
     * 
     * @tparam Iterator Iterator type
     * @tparam Function Function type
     * @param first Begin iterator
     * @param last End iterator
     * @param func Function to apply
     */
    template<typename Iterator, typename Function>
    void parallel_for_each(Iterator first, Iterator last, Function func) {
        parallel_transform(first, last, first, [func](const auto& item) {
            func(item);
            return item;
        });
    }
    
    // Parallel sorting and searching
    
    /**
     * @brief Parallel sort
     * 
     * @tparam Iterator Iterator type
     * @tparam Compare Comparison function type
     * @param first Begin iterator
     * @param last End iterator
     * @param comp Comparison function
     */
    template<typename Iterator, typename Compare>
    void parallel_sort(Iterator first, Iterator last, Compare comp) {
        const size_t length = std::distance(first, last);
        
        if (length < config_.min_task_size) {
            std::sort(first, last, comp);
            return;
        }
        
        // Use merge sort for parallel implementation
        parallel_merge_sort(first, last, comp);
    }
    
    /**
     * @brief Parallel binary search
     * 
     * @tparam Iterator Iterator type
     * @tparam T Value type
     * @param first Begin iterator
     * @param last End iterator
     * @param value Value to search for
     * @return Iterator to found element or last if not found
     */
    template<typename Iterator, typename T>
    Iterator parallel_binary_search(Iterator first, Iterator last, const T& value) {
        const size_t length = std::distance(first, last);
        
        if (length < config_.min_task_size) {
            auto it = std::lower_bound(first, last, value);
            return (it != last && *it == value) ? it : last;
        }
        
        return parallel_binary_search_impl(first, last, value);
    }
    
    // Parallel prefix operations
    
    /**
     * @brief Parallel inclusive scan (prefix sum)
     * 
     * @tparam InputIterator Input iterator type
     * @tparam OutputIterator Output iterator type
     * @tparam BinaryOp Binary operation type
     * @param first Begin input iterator
     * @param last End input iterator
     * @param result Begin output iterator
     * @param binary_op Binary operation
     * @return End output iterator
     */
    template<typename InputIterator, typename OutputIterator, typename BinaryOp>
    OutputIterator parallel_inclusive_scan(InputIterator first, InputIterator last,
                                         OutputIterator result, BinaryOp binary_op) {
        const size_t length = std::distance(first, last);
        
        if (length < config_.min_task_size) {
            return std::inclusive_scan(first, last, result, binary_op);
        }
        
        return parallel_scan_impl(first, last, result, binary_op, false);
    }
    
    /**
     * @brief Parallel exclusive scan
     * 
     * @tparam InputIterator Input iterator type
     * @tparam OutputIterator Output iterator type
     * @tparam T Value type
     * @tparam BinaryOp Binary operation type
     * @param first Begin input iterator
     * @param last End input iterator
     * @param result Begin output iterator
     * @param init Initial value
     * @param binary_op Binary operation
     * @return End output iterator
     */
    template<typename InputIterator, typename OutputIterator, typename T, typename BinaryOp>
    OutputIterator parallel_exclusive_scan(InputIterator first, InputIterator last,
                                         OutputIterator result, T init, BinaryOp binary_op) {
        const size_t length = std::distance(first, last);
        
        if (length < config_.min_task_size) {
            return std::exclusive_scan(first, last, result, init, binary_op);
        }
        
        return parallel_scan_impl(first, last, result, binary_op, true, init);
    }

private:
    ParallelConfig config_;
    
    // Helper methods for parallel algorithms
    template<typename Iterator, typename Compare>
    void parallel_merge_sort(Iterator first, Iterator last, Compare comp);
    
    template<typename Iterator, typename T>
    Iterator parallel_binary_search_impl(Iterator first, Iterator last, const T& value);
    
    template<typename InputIterator, typename OutputIterator, typename BinaryOp, typename T = void>
    OutputIterator parallel_scan_impl(InputIterator first, InputIterator last,
                                     OutputIterator result, BinaryOp binary_op,
                                     bool exclusive, const T& init = T{});
};

/**
 * @brief SIMD operations for vectorized computation
 */
class SIMDOperations {
public:
    /**
     * @brief Constructor
     */
    SIMDOperations();
    
    /**
     * @brief Check SIMD capability
     * 
     * @param instruction_set Instruction set to check ("SSE2", "AVX", "AVX2", "AVX512")
     * @return True if supported
     */
    static bool supports_simd(const std::string& instruction_set);
    
    /**
     * @brief Get SIMD width for double precision
     * 
     * @return Number of doubles that fit in SIMD register
     */
    static int get_simd_width();
    
    // Vectorized arithmetic operations
    
    /**
     * @brief Vectorized addition
     * 
     * @param a First array
     * @param b Second array
     * @param result Result array
     * @param size Array size
     */
    void vectorized_add(const double* a, const double* b, double* result, size_t size);
    
    /**
     * @brief Vectorized subtraction
     * 
     * @param a First array
     * @param b Second array
     * @param result Result array
     * @param size Array size
     */
    void vectorized_subtract(const double* a, const double* b, double* result, size_t size);
    
    /**
     * @brief Vectorized multiplication
     * 
     * @param a First array
     * @param b Second array
     * @param result Result array
     * @param size Array size
     */
    void vectorized_multiply(const double* a, const double* b, double* result, size_t size);
    
    /**
     * @brief Vectorized division
     * 
     * @param a First array
     * @param b Second array
     * @param result Result array
     * @param size Array size
     */
    void vectorized_divide(const double* a, const double* b, double* result, size_t size);
    
    /**
     * @brief Vectorized fused multiply-add (FMA)
     * 
     * @param a First array
     * @param b Second array
     * @param c Third array
     * @param result Result array (a * b + c)
     * @param size Array size
     */
    void vectorized_fma(const double* a, const double* b, const double* c, 
                       double* result, size_t size);
    
    // Vectorized mathematical functions
    
    /**
     * @brief Vectorized exponential
     * 
     * @param input Input array
     * @param result Result array
     * @param size Array size
     */
    void vectorized_exp(const double* input, double* result, size_t size);
    
    /**
     * @brief Vectorized logarithm
     * 
     * @param input Input array
     * @param result Result array
     * @param size Array size
     */
    void vectorized_log(const double* input, double* result, size_t size);
    
    /**
     * @brief Vectorized sine
     * 
     * @param input Input array
     * @param result Result array
     * @param size Array size
     */
    void vectorized_sin(const double* input, double* result, size_t size);
    
    /**
     * @brief Vectorized cosine
     * 
     * @param input Input array
     * @param result Result array
     * @param size Array size
     */
    void vectorized_cos(const double* input, double* result, size_t size);
    
    /**
     * @brief Vectorized hyperbolic tangent
     * 
     * @param input Input array
     * @param result Result array
     * @param size Array size
     */
    void vectorized_tanh(const double* input, double* result, size_t size);
    
    // Vectorized neural network operations
    
    /**
     * @brief Vectorized sigmoid activation
     * 
     * @param input Input array
     * @param result Result array
     * @param size Array size
     */
    void vectorized_sigmoid(const double* input, double* result, size_t size);
    
    /**
     * @brief Vectorized ReLU activation
     * 
     * @param input Input array
     * @param result Result array
     * @param size Array size
     */
    void vectorized_relu(const double* input, double* result, size_t size);
    
    /**
     * @brief Vectorized softmax computation
     * 
     * @param input Input array
     * @param result Result array
     * @param size Array size
     */
    void vectorized_softmax(const double* input, double* result, size_t size);
    
    // Vectorized reductions
    
    /**
     * @brief Vectorized sum
     * 
     * @param input Input array
     * @param size Array size
     * @return Sum of elements
     */
    double vectorized_sum(const double* input, size_t size);
    
    /**
     * @brief Vectorized dot product
     * 
     * @param a First array
     * @param b Second array
     * @param size Array size
     * @return Dot product
     */
    double vectorized_dot_product(const double* a, const double* b, size_t size);
    
    /**
     * @brief Vectorized L2 norm
     * 
     * @param input Input array
     * @param size Array size
     * @return L2 norm
     */
    double vectorized_l2_norm(const double* input, size_t size);
    
    // Memory operations
    
    /**
     * @brief Vectorized memory copy
     * 
     * @param src Source array
     * @param dst Destination array
     * @param size Array size
     */
    void vectorized_copy(const double* src, double* dst, size_t size);
    
    /**
     * @brief Vectorized memory fill
     * 
     * @param dst Destination array
     * @param value Fill value
     * @param size Array size
     */
    void vectorized_fill(double* dst, double value, size_t size);

private:
    bool avx512_supported_;
    bool avx2_supported_;
    bool avx_supported_;
    bool sse2_supported_;
    
    // SIMD implementation helpers
    void detect_simd_capabilities();
    
#if defined(__AVX512F__)
    void avx512_add(const double* a, const double* b, double* result, size_t size);
    void avx512_multiply(const double* a, const double* b, double* result, size_t size);
    double avx512_sum(const double* input, size_t size);
#endif
    
#if defined(__AVX2__) || defined(__AVX__)
    void avx_add(const double* a, const double* b, double* result, size_t size);
    void avx_multiply(const double* a, const double* b, double* result, size_t size);
    double avx_sum(const double* input, size_t size);
#endif
    
#if defined(__SSE2__)
    void sse_add(const double* a, const double* b, double* result, size_t size);
    void sse_multiply(const double* a, const double* b, double* result, size_t size);
    double sse_sum(const double* input, size_t size);
#endif
    
    // Fallback scalar implementations
    void scalar_add(const double* a, const double* b, double* result, size_t size);
    void scalar_multiply(const double* a, const double* b, double* result, size_t size);
    double scalar_sum(const double* input, size_t size);
};

/**
 * @brief GPU parallel processing using CUDA
 */
#ifdef ULTRA_USE_CUDA
class GPUParallelProcessing {
public:
    /**
     * @brief Constructor
     * 
     * @param device_id GPU device ID
     */
    explicit GPUParallelProcessing(int device_id = 0);
    
    /**
     * @brief Destructor
     */
    ~GPUParallelProcessing();
    
    /**
     * @brief Initialize GPU resources
     * 
     * @return True if successful
     */
    bool initialize();
    
    /**
     * @brief Cleanup GPU resources
     */
    void cleanup();
    
    /**
     * @brief GPU parallel reduction
     * 
     * @param input Input data on host
     * @param size Input size
     * @param operation Reduction operation ("sum", "max", "min", "product")
     * @return Reduced value
     */
    double gpu_reduce(const std::vector<double>& input, const std::string& operation = "sum");
    
    /**
     * @brief GPU parallel transform
     * 
     * @param input Input data
     * @param operation Transform operation ("exp", "log", "sin", "cos", "sigmoid", "relu")
     * @return Transformed data
     */
    std::vector<double> gpu_transform(const std::vector<double>& input, 
                                    const std::string& operation);
    
    /**
     * @brief GPU matrix multiplication
     * 
     * @param a First matrix
     * @param b Second matrix
     * @return Product matrix
     */
    Eigen::MatrixXd gpu_matrix_multiply(const Eigen::MatrixXd& a, const Eigen::MatrixXd& b);
    
    /**
     * @brief GPU convolution
     * 
     * @param input Input tensor
     * @param kernel Convolution kernel
     * @param stride Stride value
     * @param padding Padding value
     * @return Convolution result
     */
    std::vector<double> gpu_convolution(const std::vector<double>& input,
                                      const std::vector<double>& kernel,
                                      int stride = 1, int padding = 0);
    
    /**
     * @brief GPU parallel sort
     * 
     * @param input Input data
     * @param ascending Sort in ascending order
     * @return Sorted data
     */
    std::vector<double> gpu_sort(const std::vector<double>& input, bool ascending = true);
    
    /**
     * @brief GPU parallel scan (prefix sum)
     * 
     * @param input Input data
     * @param inclusive Inclusive scan if true, exclusive if false
     * @return Scanned data
     */
    std::vector<double> gpu_scan(const std::vector<double>& input, bool inclusive = true);
    
    /**
     * @brief Launch custom CUDA kernel
     * 
     * @param kernel_code CUDA kernel code
     * @param grid_size Grid size
     * @param block_size Block size
     * @param args Kernel arguments
     * @return True if successful
     */
    bool launch_custom_kernel(const std::string& kernel_code,
                            dim3 grid_size, dim3 block_size,
                            const std::vector<void*>& args);
    
    /**
     * @brief Get GPU memory info
     * 
     * @return Pair of (free_memory, total_memory) in bytes
     */
    std::pair<size_t, size_t> get_memory_info() const;
    
    /**
     * @brief Set GPU memory pool size
     * 
     * @param size Memory pool size in bytes
     */
    void set_memory_pool_size(size_t size);
    
    /**
     * @brief Synchronize GPU operations
     */
    void synchronize();

private:
    int device_id_;
    cudaStream_t stream_;
    size_t memory_pool_size_;
    
    // Memory management
    std::vector<void*> allocated_pointers_;
    
    // Helper methods
    void* allocate_device_memory(size_t size);
    void free_device_memory(void* ptr);
    void check_cuda_error(cudaError_t error, const std::string& operation);
    
    // Kernel implementations
    void launch_reduce_kernel(double* input, double* output, size_t size, 
                            const std::string& operation);
    void launch_transform_kernel(double* input, double* output, size_t size,
                               const std::string& operation);
};
#endif // ULTRA_USE_CUDA

/**
 * @brief Distributed computing using MPI
 */
#ifdef ULTRA_USE_MPI
class DistributedComputing {
public:
    /**
     * @brief Constructor
     */
    DistributedComputing();
    
    /**
     * @brief Destructor
     */
    ~DistributedComputing();
    
    /**
     * @brief Initialize MPI
     * 
     * @param argc Command line argument count
     * @param argv Command line arguments
     * @return True if successful
     */
    bool initialize(int* argc, char*** argv);
    
    /**
     * @brief Finalize MPI
     */
    void finalize();
    
    /**
     * @brief Get MPI rank
     * 
     * @return Process rank
     */
    int get_rank() const;
    
    /**
     * @brief Get MPI size
     * 
     * @return Number of processes
     */
    int get_size() const;
    
    /**
     * @brief Broadcast data from root to all processes
     * 
     * @tparam T Data type
     * @param data Data to broadcast
     * @param root Root process rank
     */
    template<typename T>
    void broadcast(T& data, int root = 0) {
        MPI_Bcast(&data, sizeof(T), MPI_BYTE, root, MPI_COMM_WORLD);
    }
    
    /**
     * @brief Broadcast vector from root to all processes
     * 
     * @tparam T Data type
     * @param data Vector to broadcast
     * @param root Root process rank
     */
    template<typename T>
    void broadcast_vector(std::vector<T>& data, int root = 0) {
        int size = data.size();
        MPI_Bcast(&size, 1, MPI_INT, root, MPI_COMM_WORLD);
        
        if (get_rank() != root) {
            data.resize(size);
        }
        
        if (size > 0) {
            MPI_Bcast(data.data(), size * sizeof(T), MPI_BYTE, root, MPI_COMM_WORLD);
        }
    }
    
    /**
     * @brief All-reduce operation
     * 
     * @tparam T Data type
     * @param sendbuf Send buffer
     * @param recvbuf Receive buffer
     * @param count Number of elements
     * @param operation MPI operation
     */
    template<typename T>
    void all_reduce(const T* sendbuf, T* recvbuf, int count, MPI_Op operation) {
        MPI_Datatype datatype = get_mpi_datatype<T>();
        MPI_Allreduce(sendbuf, recvbuf, count, datatype, operation, MPI_COMM_WORLD);
    }
    
    /**
     * @brief All-gather operation
     * 
     * @tparam T Data type
     * @param sendbuf Send buffer
     * @param sendcount Send count
     * @param recvbuf Receive buffer
     * @param recvcount Receive count per process
     */
    template<typename T>
    void all_gather(const T* sendbuf, int sendcount, T* recvbuf, int recvcount) {
        MPI_Datatype datatype = get_mpi_datatype<T>();
        MPI_Allgather(sendbuf, sendcount, datatype, recvbuf, recvcount, datatype, MPI_COMM_WORLD);
    }
    
    /**
     * @brief Scatter data from root to all processes
     * 
     * @tparam T Data type
     * @param sendbuf Send buffer (root only)
     * @param sendcount Send count per process
     * @param recvbuf Receive buffer
     * @param recvcount Receive count
     * @param root Root process rank
     */
    template<typename T>
    void scatter(const T* sendbuf, int sendcount, T* recvbuf, int recvcount, int root = 0) {
        MPI_Datatype datatype = get_mpi_datatype<T>();
        MPI_Scatter(sendbuf, sendcount, datatype, recvbuf, recvcount, datatype, root, MPI_COMM_WORLD);
    }
    
    /**
     * @brief Gather data from all processes to root
     * 
     * @tparam T Data type
     * @param sendbuf Send buffer
     * @param sendcount Send count
     * @param recvbuf Receive buffer (root only)
     * @param recvcount Receive count per process
     * @param root Root process rank
     */
    template<typename T>
    void gather(const T* sendbuf, int sendcount, T* recvbuf, int recvcount, int root = 0) {
        MPI_Datatype datatype = get_mpi_datatype<T>();
        MPI_Gather(sendbuf, sendcount, datatype, recvbuf, recvcount, datatype, root, MPI_COMM_WORLD);
    }
    
    /**
     * @brief Distributed matrix multiplication
     * 
     * @param a First matrix
     * @param b Second matrix
     * @return Product matrix
     */
    Eigen::MatrixXd distributed_matrix_multiply(const Eigen::MatrixXd& a, const Eigen::MatrixXd& b);
    
    /**
     * @brief Distributed parallel reduce
     * 
     * @param local_data Local data
     * @param operation Reduction operation
     * @return Reduced result (on root process)
     */
    double distributed_reduce(const std::vector<double>& local_data, const std::string& operation = "sum");
    
    /**
     * @brief Distributed parallel sort
     * 
     * @param local_data Local data to sort
     * @return Globally sorted data
     */
    std::vector<double> distributed_sort(const std::vector<double>& local_data);
    
    /**
     * @brief Barrier synchronization
     */
    void barrier();
    
    /**
     * @brief Get wall time
     * 
     * @return Current wall time
     */
    double get_time() const;

private:
    bool initialized_;
    int rank_;
    int size_;
    
    // Helper methods
    template<typename T>
    MPI_Datatype get_mpi_datatype() {
        if constexpr (std::is_same_v<T, int>) return MPI_INT;
        else if constexpr (std::is_same_v<T, double>) return MPI_DOUBLE;
        else if constexpr (std::is_same_v<T, float>) return MPI_FLOAT;
        else if constexpr (std::is_same_v<T, char>) return MPI_CHAR;
        else return MPI_BYTE;
    }
    
    void check_mpi_error(int error_code, const std::string& operation);
};
#endif // ULTRA_USE_MPI

/**
 * @brief Memory pool for efficient parallel memory management
 */
class MemoryPool {
public:
    /**
     * @brief Constructor
     * 
     * @param pool_size Total pool size in bytes
     * @param alignment Memory alignment (must be power of 2)
     */
    MemoryPool(size_t pool_size, size_t alignment = 64);
    
    /**
     * @brief Destructor
     */
    ~MemoryPool();
    
    /**
     * @brief Allocate memory from pool
     * 
     * @param size Size in bytes
     * @return Pointer to allocated memory or nullptr if failed
     */
    void* allocate(size_t size);
    
    /**
     * @brief Deallocate memory back to pool
     * 
     * @param ptr Pointer to deallocate
     */
    void deallocate(void* ptr);
    
    /**
     * @brief Allocate aligned memory
     * 
     * @param size Size in bytes
     * @param alignment Alignment requirement
     * @return Pointer to allocated memory or nullptr if failed
     */
    void* allocate_aligned(size_t size, size_t alignment);
    
    /**
     * @brief Reset pool (deallocate all memory)
     */
    void reset();
    
    /**
     * @brief Get total pool size
     * 
     * @return Pool size in bytes
     */
    size_t get_pool_size() const;
    
    /**
     * @brief Get used memory
     * 
     * @return Used memory in bytes
     */
    size_t get_used_memory() const;
    
    /**
     * @brief Get free memory
     * 
     * @return Free memory in bytes
     */
    size_t get_free_memory() const;
    
    /**
     * @brief Get memory utilization
     * 
     * @return Utilization ratio (0.0 to 1.0)
     */
    double get_utilization() const;
    
    /**
     * @brief Check if pointer belongs to this pool
     * 
     * @param ptr Pointer to check
     * @return True if pointer is from this pool
     */
    bool owns_pointer(void* ptr) const;
    
    /**
     * @brief Get allocation statistics
     * 
     * @return Statistics map
     */
    std::map<std::string, size_t> get_statistics() const;

private:
    struct Block {
        size_t size;
        bool free;
        Block* next;
        Block* prev;
    };
    
    void* pool_memory_;
    size_t pool_size_;
    size_t alignment_;
    size_t used_memory_;
    
    Block* free_list_;
    Block* used_list_;
    
    std::mutex allocation_mutex_;
    
    // Statistics
    std::atomic<size_t> total_allocations_{0};
    std::atomic<size_t> total_deallocations_{0};
    std::atomic<size_t> allocation_failures_{0};
    
    // Helper methods
    void* align_pointer(void* ptr, size_t alignment);
    size_t align_size(size_t size, size_t alignment);
    Block* find_free_block(size_t size);
    void split_block(Block* block, size_t size);
    void merge_adjacent_blocks();
    void add_to_free_list(Block* block);
    void remove_from_free_list(Block* block);
    void add_to_used_list(Block* block);
    void remove_from_used_list(Block* block);
};

/**
 * @brief Pipeline processor for streaming parallel computation
 */
class PipelineProcessor {
public:
    using Stage = std::function<void(void*)>;
    using StageId = size_t;
    
    /**
     * @brief Constructor
     * 
     * @param config Parallel processing configuration
     */
    explicit PipelineProcessor(const ParallelConfig& config = ParallelConfig());
    
    /**
     * @brief Destructor
     */
    ~PipelineProcessor();
    
    /**
     * @brief Add processing stage
     * 
     * @param stage Stage function
     * @param buffer_size Buffer size for this stage
     * @return Stage ID
     */
    StageId add_stage(Stage stage, size_t buffer_size = 1000);
    
    /**
     * @brief Remove processing stage
     * 
     * @param stage_id Stage ID to remove
     */
    void remove_stage(StageId stage_id);
    
    /**
     * @brief Start pipeline processing
     * 
     * @return True if successful
     */
    bool start();
    
    /**
     * @brief Stop pipeline processing
     */
    void stop();
    
    /**
     * @brief Submit data to pipeline
     * 
     * @param data Data to process
     * @return True if successful
     */
    bool submit(void* data);
    
    /**
     * @brief Get processed data
     * 
     * @param data Output data
     * @return True if data available
     */
    bool get_result(void*& data);
    
    /**
     * @brief Wait for pipeline to drain
     */
    void wait_for_completion();
    
    /**
     * @brief Get pipeline statistics
     * 
     * @return Statistics map
     */
    std::map<std::string, double> get_statistics() const;
    
    /**
     * @brief Set stage parallelism
     * 
     * @param stage_id Stage ID
     * @param num_threads Number of threads for this stage
     */
    void set_stage_parallelism(StageId stage_id, int num_threads);
    
    /**
     * @brief Get pipeline throughput
     * 
     * @return Items processed per second
     */
    double get_throughput() const;
    
    /**
     * @brief Get pipeline latency
     * 
     * @return Average latency in milliseconds
     */
    double get_latency() const;

private:
    struct PipelineStage {
        StageId id;
        Stage function;
        LockFreeQueue<void*> input_queue;
        LockFreeQueue<void*> output_queue;
        std::vector<std::thread> workers;
        std::atomic<bool> active{true};
        
        // Statistics
        std::atomic<size_t> items_processed{0};
        std::atomic<size_t> processing_time_ns{0};
        
        PipelineStage(StageId stage_id, Stage func, size_t buffer_size)
            : id(stage_id), function(std::move(func)),
              input_queue(buffer_size), output_queue(buffer_size) {}
    };
    
    ParallelConfig config_;
    std::vector<std::unique_ptr<PipelineStage>> stages_;
    std::atomic<bool> running_{false};
    std::atomic<StageId> next_stage_id_{0};
    
    // Input/output queues
    LockFreeQueue<void*> input_queue_;
    LockFreeQueue<void*> output_queue_;
    
    // Statistics
    std::atomic<size_t> total_items_submitted_{0};
    std::atomic<size_t> total_items_completed_{0};
    std::chrono::high_resolution_clock::time_point start_time_;
    
    // Worker management
    void stage_worker(PipelineStage* stage, int worker_id);
    void input_distributor();
    void output_collector();
    
    std::thread input_distributor_thread_;
    std::thread output_collector_thread_;
};

/**
 * @brief Load balancer for distributing work across resources
 */
class LoadBalancer {
public:
    enum class BalancingStrategy {
        ROUND_ROBIN,
        LEAST_LOADED,
        WEIGHTED_ROUND_ROBIN,
        RANDOM,
        CONSISTENT_HASHING
    };
    
    /**
     * @brief Constructor
     * 
     * @param strategy Load balancing strategy
     */
    explicit LoadBalancer(BalancingStrategy strategy = BalancingStrategy::LEAST_LOADED);
    
    /**
     * @brief Add resource to load balancer
     * 
     * @param resource_id Resource identifier
     * @param weight Resource weight (for weighted strategies)
     * @param capacity Resource capacity
     */
    void add_resource(int resource_id, double weight = 1.0, size_t capacity = SIZE_MAX);
    
    /**
     * @brief Remove resource from load balancer
     * 
     * @param resource_id Resource identifier
     */
    void remove_resource(int resource_id);
    
    /**
     * @brief Select resource for new task
     * 
     * @param task_weight Task weight/size
     * @return Selected resource ID or -1 if none available
     */
    int select_resource(double task_weight = 1.0);
    
    /**
     * @brief Update resource load
     * 
     * @param resource_id Resource identifier
     * @param load_delta Change in load (positive = increase, negative = decrease)
     */
    void update_resource_load(int resource_id, double load_delta);
    
    /**
     * @brief Get resource load
     * 
     * @param resource_id Resource identifier
     * @return Current load
     */
    double get_resource_load(int resource_id) const;
    
    /**
     * @brief Get load balancing statistics
     * 
     * @return Statistics map
     */
    std::map<std::string, double> get_statistics() const;
    
    /**
     * @brief Set balancing strategy
     * 
     * @param strategy New balancing strategy
     */
    void set_strategy(BalancingStrategy strategy);
    
    /**
     * @brief Rebalance existing loads
     * 
     * @return Vector of (from_resource, to_resource, amount) transfers
     */
    std::vector<std::tuple<int, int, double>> rebalance();

private:
    struct Resource {
        int id;
        double weight;
        size_t capacity;
        std::atomic<double> current_load{0.0};
        std::atomic<size_t> task_count{0};
        
        Resource(int res_id, double w, size_t cap)
            : id(res_id), weight(w), capacity(cap) {}
    };
    
    BalancingStrategy strategy_;
    std::vector<std::unique_ptr<Resource>> resources_;
    std::mutex resources_mutex_;
    
    // Round-robin state
    std::atomic<size_t> round_robin_index_{0};
    
    // Random number generation
    std::mt19937 random_generator_;
    std::uniform_real_distribution<double> random_distribution_;
    
    // Helper methods
    int select_round_robin();
    int select_least_loaded(double task_weight);
    int select_weighted_round_robin();
    int select_random();
    int select_consistent_hash(double task_weight);
    
    Resource* find_resource(int resource_id);
    const Resource* find_resource(int resource_id) const;
};

} // namespace parallel_processing
} // namespace utils
} // namespace ultra

#endif // ULTRA_UTILS_PARALLEL_PROCESSING_H
