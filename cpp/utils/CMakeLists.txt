cmake_minimum_required(VERSION 3.14)

project(Ultra-Utils VERSION 1.0.0 LANGUAGES CXX)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# Options
option(ULTRA_BUILD_SHARED_LIBS "Build shared libraries instead of static" OFF)
option(ULTRA_ENABLE_TESTING "Enable testing for Ultra utils" ON)
option(ULTRA_USE_CUDA "Enable CUDA support for GPU acceleration" ON)
option(ULTRA_USE_MKL "Use Intel MKL for optimized linear algebra" OFF)
option(ULTRA_USE_OPENMP "Enable OpenMP for parallel computing" ON)

# Dependencies
find_package(Eigen3 3.3 REQUIRED)
find_package(Boost REQUIRED COMPONENTS system filesystem thread serialization math graph)
find_package(fmt REQUIRED)
find_package(spdlog REQUIRED)
find_package(nlohmann_json REQUIRED)
find_package(TBB REQUIRED)
find_package(range-v3 REQUIRED)

# CUDA setup
if(ULTRA_USE_CUDA)
    find_package(CUDA REQUIRED)
    include_directories(${CUDA_INCLUDE_DIRS})
    set(CUDA_NVCC_FLAGS "${CUDA_NVCC_FLAGS} -std=c++14 -O3 -arch=sm_70")
    enable_language(CUDA)
endif()

# MKL setup
if(ULTRA_USE_MKL)
    find_package(MKL REQUIRED)
    include_directories(${MKL_INCLUDE_DIRS})
    add_definitions(-DULTRA_USE_MKL)
endif()

# OpenMP setup
if(ULTRA_USE_OPENMP)
    find_package(OpenMP REQUIRED)
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} ${OpenMP_CXX_FLAGS}")
endif()

# Determine library type
if(ULTRA_BUILD_SHARED_LIBS)
    set(ULTRA_LIBRARY_TYPE SHARED)
else()
    set(ULTRA_LIBRARY_TYPE STATIC)
endif()

# Source files for utils
set(ULTRA_UTILS_SOURCES
    # Core utility classes
    src/config.cpp
    src/logging.cpp
    src/profiling.cpp
    src/file_utils.cpp
    src/string_utils.cpp
    src/tensor_utils.cpp
    src/random.cpp
    src/parallel.cpp
    src/memory_pool.cpp
    src/serialization.cpp
    src/threadpool.cpp
    src/timer.cpp
    
    # Math utilities
    src/math/matrix_operations.cpp
    src/math/differential_equations.cpp
    src/math/probability.cpp
    src/math/statistics.cpp
    src/math/optimization.cpp
    src/math/information_theory.cpp
    src/math/graph_theory.cpp
    src/math/fourier_transforms.cpp
    
    # Neural computation utilities
    src/neural/activation_functions.cpp
    src/neural/attention_mechanisms.cpp
    src/neural/diffusion_processes.cpp
    src/neural/spiking_models.cpp
    src/neural/transformer_utils.cpp
    src/neural/reservoir_computing.cpp
    
    # CUDA implementations if enabled
    $<$<BOOL:${ULTRA_USE_CUDA}>:src/cuda/cuda_matrix_ops.cu>
    $<$<BOOL:${ULTRA_USE_CUDA}>:src/cuda/cuda_activation.cu>
    $<$<BOOL:${ULTRA_USE_CUDA}>:src/cuda/cuda_attention.cu>
    $<$<BOOL:${ULTRA_USE_CUDA}>:src/cuda/cuda_diffusion.cu>
    $<$<BOOL:${ULTRA_USE_CUDA}>:src/cuda/cuda_spiking.cu>
)

# Header files
set(ULTRA_UTILS_HEADERS
    # Core utility headers
    include/ultra/utils/config.hpp
    include/ultra/utils/logging.hpp
    include/ultra/utils/profiling.hpp
    include/ultra/utils/file_utils.hpp
    include/ultra/utils/string_utils.hpp
    include/ultra/utils/tensor_utils.hpp
    include/ultra/utils/random.hpp
    include/ultra/utils/parallel.hpp
    include/ultra/utils/memory_pool.hpp
    include/ultra/utils/serialization.hpp
    include/ultra/utils/threadpool.hpp
    include/ultra/utils/timer.hpp
    include/ultra/utils/macros.hpp
    include/ultra/utils/error_handling.hpp
    
    # Math utility headers
    include/ultra/utils/math/matrix_operations.hpp
    include/ultra/utils/math/differential_equations.hpp
    include/ultra/utils/math/probability.hpp
    include/ultra/utils/math/statistics.hpp
    include/ultra/utils/math/optimization.hpp
    include/ultra/utils/math/information_theory.hpp
    include/ultra/utils/math/graph_theory.hpp
    include/ultra/utils/math/fourier_transforms.hpp
    
    # Neural computation utility headers
    include/ultra/utils/neural/activation_functions.hpp
    include/ultra/utils/neural/attention_mechanisms.hpp
    include/ultra/utils/neural/diffusion_processes.hpp
    include/ultra/utils/neural/spiking_models.hpp
    include/ultra/utils/neural/transformer_utils.hpp
    include/ultra/utils/neural/reservoir_computing.hpp
    
    # CUDA headers if enabled
    $<$<BOOL:${ULTRA_USE_CUDA}>:include/ultra/utils/cuda/cuda_matrix_ops.cuh>
    $<$<BOOL:${ULTRA_USE_CUDA}>:include/ultra/utils/cuda/cuda_activation.cuh>
    $<$<BOOL:${ULTRA_USE_CUDA}>:include/ultra/utils/cuda/cuda_attention.cuh>
    $<$<BOOL:${ULTRA_USE_CUDA}>:include/ultra/utils/cuda/cuda_diffusion.cuh>
    $<$<BOOL:${ULTRA_USE_CUDA}>:include/ultra/utils/cuda/cuda_spiking.cuh>
)

# Create library target
add_library(ultra_utils ${ULTRA_LIBRARY_TYPE} ${ULTRA_UTILS_SOURCES} ${ULTRA_UTILS_HEADERS})

# Set target properties
set_target_properties(ultra_utils PROPERTIES
    VERSION ${PROJECT_VERSION}
    SOVERSION ${PROJECT_VERSION_MAJOR}
    POSITION_INDEPENDENT_CODE ON
    CXX_VISIBILITY_PRESET hidden
    VISIBILITY_INLINES_HIDDEN ON
    EXPORT_NAME Utils
)

# Define include directories
target_include_directories(ultra_utils
    PUBLIC
        $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
        $<INSTALL_INTERFACE:include>
    PRIVATE
        ${CMAKE_CURRENT_SOURCE_DIR}/src
)

# Link dependencies
target_link_libraries(ultra_utils
    PUBLIC
        Eigen3::Eigen
        Boost::boost
        Boost::system
        Boost::filesystem
        Boost::thread
        Boost::serialization
        fmt::fmt
        spdlog::spdlog
        nlohmann_json::nlohmann_json
        TBB::tbb
        range-v3::range-v3
    PRIVATE
        $<$<BOOL:${ULTRA_USE_CUDA}>:${CUDA_LIBRARIES}>
        $<$<BOOL:${ULTRA_USE_MKL}>:${MKL_LIBRARIES}>
        $<$<BOOL:${ULTRA_USE_OPENMP}>:OpenMP::OpenMP_CXX>
)

# Add compile definitions
target_compile_definitions(ultra_utils
    PRIVATE
        ULTRA_VERSION="${PROJECT_VERSION}"
        $<$<CONFIG:Debug>:ULTRA_DEBUG>
        $<$<BOOL:${ULTRA_USE_CUDA}>:ULTRA_USE_CUDA>
)

# Compiler options
target_compile_options(ultra_utils
    PRIVATE
        $<$<CXX_COMPILER_ID:MSVC>:/W4 /WX>
        $<$<NOT:$<CXX_COMPILER_ID:MSVC>>:-Wall -Wextra -Wpedantic -Werror>
        $<$<CONFIG:Release>:-O3>
        $<$<CONFIG:Debug>:-g -O0>
)

# Installation
install(TARGETS ultra_utils
    EXPORT ultra-utils-targets
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
    RUNTIME DESTINATION bin
    INCLUDES DESTINATION include
)

install(DIRECTORY include/ultra
    DESTINATION include
    FILES_MATCHING PATTERN "*.hpp" PATTERN "*.h" PATTERN "*.cuh"
)

install(EXPORT ultra-utils-targets
    FILE UltraUtilsTargets.cmake
    NAMESPACE Ultra::
    DESTINATION lib/cmake/Ultra
)

# Config version file
include(CMakePackageConfigHelpers)
write_basic_package_version_file(
    "${CMAKE_CURRENT_BINARY_DIR}/UltraUtilsConfigVersion.cmake"
    VERSION ${PROJECT_VERSION}
    COMPATIBILITY SameMajorVersion
)

# Config file
configure_package_config_file(
    "${CMAKE_CURRENT_SOURCE_DIR}/cmake/UltraUtilsConfig.cmake.in"
    "${CMAKE_CURRENT_BINARY_DIR}/UltraUtilsConfig.cmake"
    INSTALL_DESTINATION lib/cmake/Ultra
)

install(FILES
    "${CMAKE_CURRENT_BINARY_DIR}/UltraUtilsConfig.cmake"
    "${CMAKE_CURRENT_BINARY_DIR}/UltraUtilsConfigVersion.cmake"
    DESTINATION lib/cmake/Ultra
)

# Testing setup
if(ULTRA_ENABLE_TESTING)
    enable_testing()
    add_subdirectory(tests)
endif()

# Export package for use in build tree
export(EXPORT ultra-utils-targets
    FILE "${CMAKE_CURRENT_BINARY_DIR}/UltraUtilsTargets.cmake"
    NAMESPACE Ultra::
)

export(PACKAGE Ultra-Utils)