// Ultra/cpp/utils/matrix_operations.h

#ifndef ULTRA_UTILS_MATRIX_OPERATIONS_H
#define ULTRA_UTILS_MATRIX_OPERATIONS_H

#include <Eigen/Dense>
#include <Eigen/Sparse>
#include <Eigen/Eigenvalues>
#include <Eigen/SVD>
#include <Eigen/QR>
#include <Eigen/LU>
#include <Eigen/Cholesky>
#include <unsupported/Eigen/MatrixFunctions>
#include <unsupported/Eigen/KroneckerProduct>
#include <unsupported/Eigen/FFT>

#include <vector>
#include <memory>
#include <functional>
#include <algorithm>
#include <numeric>
#include <cmath>
#include <complex>
#include <random>
#include <thread>
#include <future>
#include <mutex>
#include <atomic>
#include <chrono>
#include <stdexcept>
#include <type_traits>

#ifdef ULTRA_USE_CUDA
#include <cuda_runtime.h>
#include <cublas_v2.h>
#include <cusolver_common.h>
#include <cusolverDn.h>
#include <curand.h>
#include <thrust/device_vector.h>
#include <thrust/host_vector.h>
#include <thrust/transform.h>
#include <thrust/reduce.h>
#endif

#ifdef ULTRA_USE_MKL
#include <mkl.h>
#include <mkl_lapacke.h>
#include <mkl_blas.h>
#include <mkl_sparse.h>
#include <mkl_vml.h>
#endif

#ifdef ULTRA_USE_OPENMP
#include <omp.h>
#endif

namespace ultra {
namespace utils {
namespace matrix_operations {

// Mathematical constants
constexpr double MACHINE_EPSILON = std::numeric_limits<double>::epsilon();
constexpr double PI = 3.14159265358979323846;
constexpr double SQRT_2PI = 2.50662827463100050241;
constexpr double LOG_2 = 0.693147180559945309417232121458;

// Forward declarations
class MatrixFactorization;
class SparseMatrixOperations;
class TensorOperations;
class ParallelMatrixOperations;
class GPUMatrixOperations;
class MemoryEfficientOperations;
class NeuralNetworkOperations;
class AdvancedDecompositions;
class MatrixDifferentialCalculus;
class StochasticMatrixOperations;

/**
 * @brief Enumeration of matrix decomposition types
 */
enum class DecompositionType {
    LU,                    // LU decomposition
    QR,                    // QR decomposition
    SVD,                   // Singular Value Decomposition
    EIGEN,                 // Eigenvalue decomposition
    CHOLESKY,              // Cholesky decomposition
    SCHUR,                 // Schur decomposition
    HESSENBERG,            // Hessenberg decomposition
    JORDAN,                // Jordan normal form
    POLAR,                 // Polar decomposition
    SYMMETRIC_INDEFINITE,  // Symmetric indefinite factorization
    BAND_LU,              // Banded LU decomposition
    SPARSE_LU,            // Sparse LU decomposition
    INCOMPLETE_LU,        // Incomplete LU factorization
    INCOMPLETE_CHOLESKY   // Incomplete Cholesky factorization
};

/**
 * @brief Enumeration of matrix norms
 */
enum class MatrixNorm {
    FROBENIUS,            // Frobenius norm
    SPECTRAL,             // Spectral norm (largest singular value)
    NUCLEAR,              // Nuclear norm (sum of singular values)
    ONE_NORM,             // 1-norm (maximum absolute column sum)
    INFINITY_NORM,        // Infinity norm (maximum absolute row sum)
    P_NORM,               // p-norm (requires parameter p)
    OPERATOR_NORM,        // Operator norm
    SCHATTEN_P           // Schatten p-norm
};

/**
 * @brief Enumeration of matrix multiplication algorithms
 */
enum class MultiplicationAlgorithm {
    STANDARD,             // Standard O(n^3) algorithm
    STRASSEN,             // Strassen's algorithm O(n^2.807)
    COPPERSMITH_WINOGRAD, // Coppersmith-Winograd O(n^2.376)
    BLOCKED,              // Block matrix multiplication
    PARALLEL,             // Parallel multiplication
    GPU_ACCELERATED,      // GPU-accelerated multiplication
    SPARSE_OPTIMIZED,     // Optimized for sparse matrices
    LOW_RANK             // Optimized for low-rank matrices
};

/**
 * @brief Structure for matrix operation configuration
 */
struct MatrixConfig {
    bool use_parallel = true;           // Use parallel processing
    bool use_gpu = false;              // Use GPU acceleration
    bool use_mkl = false;              // Use Intel MKL
    int num_threads = 0;               // Number of threads (0 = auto)
    double numerical_tolerance = 1e-12; // Numerical tolerance
    bool enable_caching = true;        // Enable result caching
    size_t max_cache_size = 1000000;   // Maximum cache size in bytes
    MultiplicationAlgorithm mult_algorithm = MultiplicationAlgorithm::STANDARD;
    
    // GPU-specific settings
    int gpu_device_id = 0;             // GPU device ID
    size_t gpu_memory_limit = 0;       // GPU memory limit (0 = auto)
    
    // Sparse matrix settings
    double sparsity_threshold = 0.1;   // Threshold for sparse conversion
    bool auto_sparse_conversion = true; // Automatic sparse conversion
    
    MatrixConfig() = default;
};

/**
 * @brief Base class for matrix operations with common functionality
 */
class MatrixOperationsBase {
public:
    /**
     * @brief Constructor
     * 
     * @param config Configuration for matrix operations
     */
    explicit MatrixOperationsBase(const MatrixConfig& config = MatrixConfig());
    
    /**
     * @brief Virtual destructor
     */
    virtual ~MatrixOperationsBase() = default;
    
    /**
     * @brief Set configuration
     * 
     * @param config New configuration
     */
    void set_config(const MatrixConfig& config);
    
    /**
     * @brief Get current configuration
     * 
     * @return Current configuration
     */
    const MatrixConfig& get_config() const;
    
    /**
     * @brief Check if matrix is numerically singular
     * 
     * @param matrix Input matrix
     * @return True if singular
     */
    static bool is_singular(const Eigen::MatrixXd& matrix);
    
    /**
     * @brief Check if matrix is positive definite
     * 
     * @param matrix Input matrix
     * @return True if positive definite
     */
    static bool is_positive_definite(const Eigen::MatrixXd& matrix);
    
    /**
     * @brief Check if matrix is symmetric
     * 
     * @param matrix Input matrix
     * @param tolerance Numerical tolerance
     * @return True if symmetric
     */
    static bool is_symmetric(const Eigen::MatrixXd& matrix, double tolerance = MACHINE_EPSILON);
    
    /**
     * @brief Check if matrix is orthogonal
     * 
     * @param matrix Input matrix
     * @param tolerance Numerical tolerance
     * @return True if orthogonal
     */
    static bool is_orthogonal(const Eigen::MatrixXd& matrix, double tolerance = MACHINE_EPSILON);
    
    /**
     * @brief Check if matrix is sparse
     * 
     * @param matrix Input matrix
     * @param threshold Sparsity threshold
     * @return True if sparse
     */
    static bool is_sparse(const Eigen::MatrixXd& matrix, double threshold = 0.1);
    
    /**
     * @brief Compute condition number of a matrix
     * 
     * @param matrix Input matrix
     * @param norm_type Type of norm to use
     * @return Condition number
     */
    static double condition_number(const Eigen::MatrixXd& matrix, MatrixNorm norm_type = MatrixNorm::SPECTRAL);
    
    /**
     * @brief Compute rank of a matrix
     * 
     * @param matrix Input matrix
     * @param tolerance Numerical tolerance
     * @return Matrix rank
     */
    static int rank(const Eigen::MatrixXd& matrix, double tolerance = MACHINE_EPSILON);

protected:
    MatrixConfig config_;
    mutable std::mutex cache_mutex_;
    mutable std::unordered_map<size_t, std::shared_ptr<void>> operation_cache_;
    
    // Helper methods
    size_t compute_hash(const Eigen::MatrixXd& matrix) const;
    void clear_cache_if_needed() const;
    template<typename T>
    std::shared_ptr<T> get_cached_result(size_t hash) const;
    template<typename T>
    void cache_result(size_t hash, std::shared_ptr<T> result) const;
};

/**
 * @brief Core linear algebra operations
 */
class LinearAlgebra : public MatrixOperationsBase {
public:
    /**
     * @brief Constructor
     * 
     * @param config Configuration for operations
     */
    explicit LinearAlgebra(const MatrixConfig& config = MatrixConfig());
    
    // Matrix multiplication operations
    
    /**
     * @brief Matrix multiplication with algorithm selection
     * 
     * @param A First matrix
     * @param B Second matrix
     * @param algorithm Multiplication algorithm to use
     * @return Product A * B
     */
    Eigen::MatrixXd multiply(const Eigen::MatrixXd& A, const Eigen::MatrixXd& B,
                           MultiplicationAlgorithm algorithm = MultiplicationAlgorithm::STANDARD);
    
    /**
     * @brief Element-wise matrix multiplication (Hadamard product)
     * 
     * @param A First matrix
     * @param B Second matrix
     * @return Element-wise product
     */
    Eigen::MatrixXd element_wise_multiply(const Eigen::MatrixXd& A, const Eigen::MatrixXd& B);
    
    /**
     * @brief Kronecker product of two matrices
     * 
     * @param A First matrix
     * @param B Second matrix
     * @return Kronecker product A ⊗ B
     */
    Eigen::MatrixXd kronecker_product(const Eigen::MatrixXd& A, const Eigen::MatrixXd& B);
    
    /**
     * @brief Outer product of two vectors
     * 
     * @param a First vector
     * @param b Second vector
     * @return Outer product a ⊗ b
     */
    Eigen::MatrixXd outer_product(const Eigen::VectorXd& a, const Eigen::VectorXd& b);
    
    /**
     * @brief Block matrix multiplication
     * 
     * @param A First block matrix
     * @param B Second block matrix
     * @param block_size Size of blocks
     * @return Block matrix product
     */
    Eigen::MatrixXd block_multiply(const Eigen::MatrixXd& A, const Eigen::MatrixXd& B, int block_size);
    
    // Matrix inversion and solving
    
    /**
     * @brief Compute matrix inverse
     * 
     * @param matrix Input matrix
     * @param method Inversion method ("lu", "qr", "svd", "cholesky")
     * @return Matrix inverse
     */
    Eigen::MatrixXd inverse(const Eigen::MatrixXd& matrix, const std::string& method = "lu");
    
    /**
     * @brief Compute pseudo-inverse using SVD
     * 
     * @param matrix Input matrix
     * @param tolerance Singular value tolerance
     * @return Moore-Penrose pseudo-inverse
     */
    Eigen::MatrixXd pseudo_inverse(const Eigen::MatrixXd& matrix, double tolerance = MACHINE_EPSILON);
    
    /**
     * @brief Solve linear system Ax = b
     * 
     * @param A Coefficient matrix
     * @param b Right-hand side
     * @param method Solution method ("lu", "qr", "ldlt", "llt", "svd")
     * @return Solution vector x
     */
    Eigen::VectorXd solve(const Eigen::MatrixXd& A, const Eigen::VectorXd& b, 
                         const std::string& method = "lu");
    
    /**
     * @brief Solve multiple linear systems Ax = B
     * 
     * @param A Coefficient matrix
     * @param B Right-hand side matrix
     * @param method Solution method
     * @return Solution matrix X
     */
    Eigen::MatrixXd solve_multiple(const Eigen::MatrixXd& A, const Eigen::MatrixXd& B,
                                  const std::string& method = "lu");
    
    /**
     * @brief Solve least squares problem min ||Ax - b||
     * 
     * @param A Coefficient matrix
     * @param b Right-hand side
     * @return Least squares solution
     */
    Eigen::VectorXd least_squares(const Eigen::MatrixXd& A, const Eigen::VectorXd& b);
    
    /**
     * @brief Solve regularized least squares (Ridge regression)
     * 
     * @param A Coefficient matrix
     * @param b Right-hand side
     * @param lambda Regularization parameter
     * @return Regularized solution
     */
    Eigen::VectorXd ridge_regression(const Eigen::MatrixXd& A, const Eigen::VectorXd& b, double lambda);
    
    // Matrix norms and distances
    
    /**
     * @brief Compute matrix norm
     * 
     * @param matrix Input matrix
     * @param norm_type Type of norm
     * @param p Parameter for p-norm (if applicable)
     * @return Norm value
     */
    double norm(const Eigen::MatrixXd& matrix, MatrixNorm norm_type, double p = 2.0);
    
    /**
     * @brief Compute Frobenius inner product of two matrices
     * 
     * @param A First matrix
     * @param B Second matrix
     * @return Frobenius inner product tr(A^T B)
     */
    double frobenius_inner_product(const Eigen::MatrixXd& A, const Eigen::MatrixXd& B);
    
    /**
     * @brief Compute distance between matrices
     * 
     * @param A First matrix
     * @param B Second matrix
     * @param norm_type Type of norm for distance
     * @return Distance ||A - B||
     */
    double distance(const Eigen::MatrixXd& A, const Eigen::MatrixXd& B, MatrixNorm norm_type);
    
    // Matrix transformations
    
    /**
     * @brief Compute matrix transpose
     * 
     * @param matrix Input matrix
     * @return Transposed matrix
     */
    Eigen::MatrixXd transpose(const Eigen::MatrixXd& matrix);
    
    /**
     * @brief Compute conjugate transpose (adjoint)
     * 
     * @param matrix Input complex matrix
     * @return Conjugate transpose
     */
    Eigen::MatrixXcd adjoint(const Eigen::MatrixXcd& matrix);
    
    /**
     * @brief Vectorize matrix (column-major order)
     * 
     * @param matrix Input matrix
     * @return Vectorized matrix
     */
    Eigen::VectorXd vectorize(const Eigen::MatrixXd& matrix);
    
    /**
     * @brief Reshape vector into matrix
     * 
     * @param vector Input vector
     * @param rows Number of rows
     * @param cols Number of columns
     * @return Reshaped matrix
     */
    Eigen::MatrixXd reshape(const Eigen::VectorXd& vector, int rows, int cols);
    
    /**
     * @brief Permute rows of a matrix
     * 
     * @param matrix Input matrix
     * @param permutation Permutation vector
     * @return Permuted matrix
     */
    Eigen::MatrixXd permute_rows(const Eigen::MatrixXd& matrix, const Eigen::VectorXi& permutation);
    
    /**
     * @brief Permute columns of a matrix
     * 
     * @param matrix Input matrix
     * @param permutation Permutation vector
     * @return Permuted matrix
     */
    Eigen::MatrixXd permute_columns(const Eigen::MatrixXd& matrix, const Eigen::VectorXi& permutation);

private:
    // Implementation of different multiplication algorithms
    Eigen::MatrixXd standard_multiply(const Eigen::MatrixXd& A, const Eigen::MatrixXd& B);
    Eigen::MatrixXd strassen_multiply(const Eigen::MatrixXd& A, const Eigen::MatrixXd& B);
    Eigen::MatrixXd parallel_multiply(const Eigen::MatrixXd& A, const Eigen::MatrixXd& B);
    
    // Helper functions
    bool can_use_strassen(const Eigen::MatrixXd& A, const Eigen::MatrixXd& B);
    std::pair<int, int> optimal_block_size(int m, int n, int k);
};

/**
 * @brief Advanced matrix decompositions
 */
class AdvancedDecompositions : public MatrixOperationsBase {
public:
    /**
     * @brief Constructor
     * 
     * @param config Configuration for operations
     */
    explicit AdvancedDecompositions(const MatrixConfig& config = MatrixConfig());
    
    // Standard decompositions
    
    /**
     * @brief LU decomposition with partial pivoting
     * 
     * @param matrix Input matrix
     * @return Tuple of (L, U, P) matrices
     */
    std::tuple<Eigen::MatrixXd, Eigen::MatrixXd, Eigen::PermutationMatrix<Eigen::Dynamic>> 
    lu_decomposition(const Eigen::MatrixXd& matrix);
    
    /**
     * @brief QR decomposition
     * 
     * @param matrix Input matrix
     * @param method QR method ("householder", "givens", "gram_schmidt")
     * @return Pair of (Q, R) matrices
     */
    std::pair<Eigen::MatrixXd, Eigen::MatrixXd> 
    qr_decomposition(const Eigen::MatrixXd& matrix, const std::string& method = "householder");
    
    /**
     * @brief Singular Value Decomposition
     * 
     * @param matrix Input matrix
     * @param compute_thin_u Compute thin U matrix
     * @param compute_thin_v Compute thin V matrix
     * @return Tuple of (U, S, V) where A = U * S * V^T
     */
    std::tuple<Eigen::MatrixXd, Eigen::VectorXd, Eigen::MatrixXd>
    svd(const Eigen::MatrixXd& matrix, bool compute_thin_u = false, bool compute_thin_v = false);
    
    /**
     * @brief Eigenvalue decomposition for symmetric matrices
     * 
     * @param matrix Input symmetric matrix
     * @return Pair of (eigenvalues, eigenvectors)
     */
    std::pair<Eigen::VectorXd, Eigen::MatrixXd> 
    symmetric_eigen_decomposition(const Eigen::MatrixXd& matrix);
    
    /**
     * @brief Eigenvalue decomposition for general matrices
     * 
     * @param matrix Input matrix
     * @return Tuple of (real_eigenvalues, imaginary_eigenvalues, eigenvectors)
     */
    std::tuple<Eigen::VectorXd, Eigen::VectorXd, Eigen::MatrixXcd>
    general_eigen_decomposition(const Eigen::MatrixXd& matrix);
    
    /**
     * @brief Cholesky decomposition
     * 
     * @param matrix Input positive definite matrix
     * @return Lower triangular matrix L such that A = L * L^T
     */
    Eigen::MatrixXd cholesky_decomposition(const Eigen::MatrixXd& matrix);
    
    /**
     * @brief LDLT decomposition for symmetric indefinite matrices
     * 
     * @param matrix Input symmetric matrix
     * @return Tuple of (L, D, P) where A = P^T * L * D * L^T * P
     */
    std::tuple<Eigen::MatrixXd, Eigen::VectorXd, Eigen::PermutationMatrix<Eigen::Dynamic>>
    ldlt_decomposition(const Eigen::MatrixXd& matrix);
    
    // Advanced decompositions
    
    /**
     * @brief Schur decomposition
     * 
     * @param matrix Input matrix
     * @return Pair of (Q, T) where A = Q * T * Q^T
     */
    std::pair<Eigen::MatrixXd, Eigen::MatrixXd> schur_decomposition(const Eigen::MatrixXd& matrix);
    
    /**
     * @brief Generalized Schur decomposition
     * 
     * @param A First matrix
     * @param B Second matrix
     * @return Tuple of (Q, Z, S, T) where A = Q * S * Z^T, B = Q * T * Z^T
     */
    std::tuple<Eigen::MatrixXd, Eigen::MatrixXd, Eigen::MatrixXd, Eigen::MatrixXd>
    generalized_schur_decomposition(const Eigen::MatrixXd& A, const Eigen::MatrixXd& B);
    
    /**
     * @brief Polar decomposition
     * 
     * @param matrix Input matrix
     * @return Pair of (U, P) where A = U * P, U is unitary, P is positive semidefinite
     */
    std::pair<Eigen::MatrixXd, Eigen::MatrixXd> polar_decomposition(const Eigen::MatrixXd& matrix);
    
    /**
     * @brief Jordan normal form
     * 
     * @param matrix Input matrix
     * @return Pair of (P, J) where A = P * J * P^(-1)
     */
    std::pair<Eigen::MatrixXcd, Eigen::MatrixXcd> jordan_normal_form(const Eigen::MatrixXd& matrix);
    
    /**
     * @brief Hessenberg decomposition
     * 
     * @param matrix Input matrix
     * @return Pair of (Q, H) where A = Q * H * Q^T
     */
    std::pair<Eigen::MatrixXd, Eigen::MatrixXd> hessenberg_decomposition(const Eigen::MatrixXd& matrix);
    
    /**
     * @brief Generalized eigenvalue decomposition
     * 
     * @param A First matrix
     * @param B Second matrix
     * @return Tuple of (alpha, beta, V) where A * V = lambda * B * V, lambda = alpha/beta
     */
    std::tuple<Eigen::VectorXcd, Eigen::VectorXcd, Eigen::MatrixXcd>
    generalized_eigenvalue_decomposition(const Eigen::MatrixXd& A, const Eigen::MatrixXd& B);
    
    // Low-rank decompositions
    
    /**
     * @brief Truncated SVD (low-rank approximation)
     * 
     * @param matrix Input matrix
     * @param rank Target rank
     * @return Tuple of (U, S, V) for rank-r approximation
     */
    std::tuple<Eigen::MatrixXd, Eigen::VectorXd, Eigen::MatrixXd>
    truncated_svd(const Eigen::MatrixXd& matrix, int rank);
    
    /**
     * @brief Randomized SVD for large matrices
     * 
     * @param matrix Input matrix
     * @param rank Target rank
     * @param oversampling Oversampling parameter
     * @param n_iter Number of power iterations
     * @return Tuple of (U, S, V) for approximate SVD
     */
    std::tuple<Eigen::MatrixXd, Eigen::VectorXd, Eigen::MatrixXd>
    randomized_svd(const Eigen::MatrixXd& matrix, int rank, int oversampling = 10, int n_iter = 2);
    
    /**
     * @brief Non-negative matrix factorization
     * 
     * @param matrix Input non-negative matrix
     * @param rank Target rank
     * @param max_iter Maximum iterations
     * @param tolerance Convergence tolerance
     * @return Pair of (W, H) where A ≈ W * H
     */
    std::pair<Eigen::MatrixXd, Eigen::MatrixXd>
    nnmf(const Eigen::MatrixXd& matrix, int rank, int max_iter = 100, double tolerance = 1e-6);
    
    /**
     * @brief Principal Component Analysis
     * 
     * @param data Input data matrix (rows: samples, cols: features)
     * @param n_components Number of components to keep
     * @param center Whether to center the data
     * @return Tuple of (components, explained_variance, mean)
     */
    std::tuple<Eigen::MatrixXd, Eigen::VectorXd, Eigen::VectorXd>
    pca(const Eigen::MatrixXd& data, int n_components, bool center = true);
    
    /**
     * @brief Independent Component Analysis using FastICA
     * 
     * @param data Input data matrix
     * @param n_components Number of independent components
     * @param max_iter Maximum iterations
     * @param tolerance Convergence tolerance
     * @return Tuple of (sources, mixing_matrix, demixing_matrix)
     */
    std::tuple<Eigen::MatrixXd, Eigen::MatrixXd, Eigen::MatrixXd>
    fast_ica(const Eigen::MatrixXd& data, int n_components, int max_iter = 200, double tolerance = 1e-4);

private:
    // Helper methods for various decompositions
    Eigen::MatrixXd givens_qr(const Eigen::MatrixXd& matrix);
    Eigen::MatrixXd gram_schmidt_qr(const Eigen::MatrixXd& matrix);
    std::pair<Eigen::MatrixXd, Eigen::MatrixXd> nnmf_update_step(
        const Eigen::MatrixXd& V, const Eigen::MatrixXd& W, const Eigen::MatrixXd& H);
    Eigen::VectorXd ica_contrast_function(const Eigen::VectorXd& u, const std::string& fun = "logcosh");
    Eigen::VectorXd ica_contrast_derivative(const Eigen::VectorXd& u, const std::string& fun = "logcosh");
};

/**
 * @brief Neural network specific matrix operations
 */
class NeuralNetworkOperations : public MatrixOperationsBase {
public:
    /**
     * @brief Constructor
     * 
     * @param config Configuration for operations
     */
    explicit NeuralNetworkOperations(const MatrixConfig& config = MatrixConfig());
    
    // Activation functions (applied element-wise)
    
    /**
     * @brief Apply activation function element-wise
     * 
     * @param matrix Input matrix
     * @param activation Activation function name
     * @param params Additional parameters for activation
     * @return Matrix with activation applied
     */
    Eigen::MatrixXd apply_activation(const Eigen::MatrixXd& matrix, 
                                   const std::string& activation,
                                   const std::map<std::string, double>& params = {});
    
    /**
     * @brief Compute derivative of activation function
     * 
     * @param matrix Input matrix (pre-activation)
     * @param activation Activation function name
     * @param params Additional parameters for activation
     * @return Derivative matrix
     */
    Eigen::MatrixXd activation_derivative(const Eigen::MatrixXd& matrix,
                                        const std::string& activation,
                                        const std::map<std::string, double>& params = {});
    
    /**
     * @brief Sigmoid activation function
     * 
     * @param matrix Input matrix
     * @return Sigmoid applied element-wise
     */
    Eigen::MatrixXd sigmoid(const Eigen::MatrixXd& matrix);
    
    /**
     * @brief Hyperbolic tangent activation function
     * 
     * @param matrix Input matrix
     * @return Tanh applied element-wise
     */
    Eigen::MatrixXd tanh(const Eigen::MatrixXd& matrix);
    
    /**
     * @brief ReLU activation function
     * 
     * @param matrix Input matrix
     * @return ReLU applied element-wise
     */
    Eigen::MatrixXd relu(const Eigen::MatrixXd& matrix);
    
    /**
     * @brief Leaky ReLU activation function
     * 
     * @param matrix Input matrix
     * @param alpha Slope for negative values
     * @return Leaky ReLU applied element-wise
     */
    Eigen::MatrixXd leaky_relu(const Eigen::MatrixXd& matrix, double alpha = 0.01);
    
    /**
     * @brief ELU (Exponential Linear Unit) activation function
     * 
     * @param matrix Input matrix
     * @param alpha ELU parameter
     * @return ELU applied element-wise
     */
    Eigen::MatrixXd elu(const Eigen::MatrixXd& matrix, double alpha = 1.0);
    
    /**
     * @brief GELU (Gaussian Error Linear Unit) activation function
     * 
     * @param matrix Input matrix
     * @return GELU applied element-wise
     */
    Eigen::MatrixXd gelu(const Eigen::MatrixXd& matrix);
    
    /**
     * @brief Swish activation function
     * 
     * @param matrix Input matrix
     * @param beta Swish parameter
     * @return Swish applied element-wise
     */
    Eigen::MatrixXd swish(const Eigen::MatrixXd& matrix, double beta = 1.0);
    
    /**
     * @brief Softmax function (applied along rows)
     * 
     * @param matrix Input matrix
     * @param axis Axis along which to apply softmax (0 = rows, 1 = columns)
     * @return Softmax probabilities
     */
    Eigen::MatrixXd softmax(const Eigen::MatrixXd& matrix, int axis = 1);
    
    /**
     * @brief Log softmax function (numerically stable)
     * 
     * @param matrix Input matrix
     * @param axis Axis along which to apply log softmax
     * @return Log softmax values
     */
    Eigen::MatrixXd log_softmax(const Eigen::MatrixXd& matrix, int axis = 1);
    
    // Attention mechanisms
    
    /**
     * @brief Scaled dot-product attention
     * 
     * @param query Query matrix [batch_size, seq_len, d_k]
     * @param key Key matrix [batch_size, seq_len, d_k]
     * @param value Value matrix [batch_size, seq_len, d_v]
     * @param mask Optional attention mask
     * @param dropout_rate Dropout rate for attention weights
     * @return Attention output and weights
     */
    std::pair<Eigen::MatrixXd, Eigen::MatrixXd> scaled_dot_product_attention(
        const Eigen::MatrixXd& query,
        const Eigen::MatrixXd& key,
        const Eigen::MatrixXd& value,
        const Eigen::MatrixXd* mask = nullptr,
        double dropout_rate = 0.0);
    
    /**
     * @brief Multi-head attention
     * 
     * @param query Query matrix
     * @param key Key matrix
     * @param value Value matrix
     * @param num_heads Number of attention heads
     * @param d_model Model dimension
     * @param mask Optional attention mask
     * @return Multi-head attention output
     */
    Eigen::MatrixXd multi_head_attention(
        const Eigen::MatrixXd& query,
        const Eigen::MatrixXd& key,
        const Eigen::MatrixXd& value,
        int num_heads,
        int d_model,
        const Eigen::MatrixXd* mask = nullptr);
    
    /**
     * @brief Positional encoding for transformers
     * 
     * @param sequence_length Length of sequence
     * @param d_model Model dimension
     * @param max_len Maximum sequence length for precomputation
     * @return Positional encoding matrix
     */
    Eigen::MatrixXd positional_encoding(int sequence_length, int d_model, int max_len = 10000);
    
    /**
     * @brief Relative positional encoding
     * 
     * @param sequence_length Length of sequence
     * @param d_model Model dimension
     * @param max_relative_distance Maximum relative distance
     * @return Relative positional encoding
     */
    Eigen::MatrixXd relative_positional_encoding(int sequence_length, int d_model, int max_relative_distance = 128);
    
    // Convolution operations
    
    /**
     * @brief 1D convolution
     * 
     * @param input Input signal [batch_size, in_channels, length]
     * @param kernel Convolution kernel [out_channels, in_channels, kernel_size]
     * @param stride Stride value
     * @param padding Padding value
     * @return Convolution output
     */
    Eigen::MatrixXd conv1d(const Eigen::MatrixXd& input,
                         const Eigen::MatrixXd& kernel,
                         int stride = 1,
                         int padding = 0);
    
    /**
     * @brief 2D convolution
     * 
     * @param input Input tensor [batch_size, in_channels, height, width]
     * @param kernel Convolution kernel [out_channels, in_channels, kernel_h, kernel_w]
     * @param stride_h Stride in height
     * @param stride_w Stride in width
     * @param padding_h Padding in height
     * @param padding_w Padding in width
     * @return Convolution output
     */
    Eigen::MatrixXd conv2d(const Eigen::MatrixXd& input,
                         const Eigen::MatrixXd& kernel,
                         int stride_h = 1, int stride_w = 1,
                         int padding_h = 0, int padding_w = 0);
    
    /**
     * @brief Depthwise separable convolution
     * 
     * @param input Input tensor
     * @param depthwise_kernel Depthwise kernel
     * @param pointwise_kernel Pointwise kernel
     * @param stride Stride value
     * @param padding Padding value
     * @return Convolution output
     */
    Eigen::MatrixXd depthwise_separable_conv(const Eigen::MatrixXd& input,
                                           const Eigen::MatrixXd& depthwise_kernel,
                                           const Eigen::MatrixXd& pointwise_kernel,
                                           int stride = 1,
                                           int padding = 0);
    
    // Normalization operations
    
    /**
     * @brief Batch normalization
     * 
     * @param input Input matrix [batch_size, features]
     * @param gamma Scale parameter
     * @param beta Shift parameter
     * @param epsilon Small constant for numerical stability
     * @return Normalized output
     */
    Eigen::MatrixXd batch_normalization(const Eigen::MatrixXd& input,
                                      const Eigen::VectorXd& gamma,
                                      const Eigen::VectorXd& beta,
                                      double epsilon = 1e-5);
    
    /**
     * @brief Layer normalization
     * 
     * @param input Input matrix
     * @param gamma Scale parameter
     * @param beta Shift parameter
     * @param epsilon Small constant for numerical stability
     * @return Normalized output
     */
    Eigen::MatrixXd layer_normalization(const Eigen::MatrixXd& input,
                                      const Eigen::VectorXd& gamma,
                                      const Eigen::VectorXd& beta,
                                      double epsilon = 1e-5);
    
    /**
     * @brief Group normalization
     * 
     * @param input Input matrix
     * @param num_groups Number of groups
     * @param gamma Scale parameter
     * @param beta Shift parameter
     * @param epsilon Small constant for numerical stability
     * @return Normalized output
     */
    Eigen::MatrixXd group_normalization(const Eigen::MatrixXd& input,
                                      int num_groups,
                                      const Eigen::VectorXd& gamma,
                                      const Eigen::VectorXd& beta,
                                      double epsilon = 1e-5);
    
    // Pooling operations
    
    /**
     * @brief Max pooling 1D
     * 
     * @param input Input tensor
     * @param pool_size Pooling window size
     * @param stride Stride value
     * @return Pooled output
     */
    Eigen::MatrixXd max_pool1d(const Eigen::MatrixXd& input, int pool_size, int stride);
    
    /**
     * @brief Average pooling 1D
     * 
     * @param input Input tensor
     * @param pool_size Pooling window size
     * @param stride Stride value
     * @return Pooled output
     */
    Eigen::MatrixXd avg_pool1d(const Eigen::MatrixXd& input, int pool_size, int stride);
    
    /**
     * @brief Global average pooling
     * 
     * @param input Input tensor
     * @return Globally averaged output
     */
    Eigen::VectorXd global_avg_pool(const Eigen::MatrixXd& input);
    
    /**
     * @brief Global max pooling
     * 
     * @param input Input tensor
     * @return Globally max pooled output
     */
    Eigen::VectorXd global_max_pool(const Eigen::MatrixXd& input);
    
    // Loss functions
    
    /**
     * @brief Mean squared error loss
     * 
     * @param predictions Predicted values
     * @param targets Target values
     * @return MSE loss
     */
    double mse_loss(const Eigen::MatrixXd& predictions, const Eigen::MatrixXd& targets);
    
    /**
     * @brief Cross-entropy loss
     * 
     * @param predictions Predicted probabilities
     * @param targets Target labels (one-hot encoded)
     * @return Cross-entropy loss
     */
    double cross_entropy_loss(const Eigen::MatrixXd& predictions, const Eigen::MatrixXd& targets);
    
    /**
     * @brief Binary cross-entropy loss
     * 
     * @param predictions Predicted probabilities
     * @param targets Binary targets
     * @return Binary cross-entropy loss
     */
    double binary_cross_entropy_loss(const Eigen::MatrixXd& predictions, const Eigen::MatrixXd& targets);
    
    /**
     * @brief Huber loss (smooth L1 loss)
     * 
     * @param predictions Predicted values
     * @param targets Target values
     * @param delta Huber loss parameter
     * @return Huber loss
     */
    double huber_loss(const Eigen::MatrixXd& predictions, const Eigen::MatrixXd& targets, double delta = 1.0);
    
    // Dropout and regularization
    
    /**
     * @brief Apply dropout
     * 
     * @param input Input matrix
     * @param dropout_rate Dropout rate
     * @param training Whether in training mode
     * @param seed Random seed
     * @return Matrix with dropout applied
     */
    Eigen::MatrixXd dropout(const Eigen::MatrixXd& input, 
                          double dropout_rate, 
                          bool training = true,
                          unsigned int seed = 0);
    
    /**
     * @brief Apply spatial dropout (for CNN features)
     * 
     * @param input Input tensor
     * @param dropout_rate Dropout rate
     * @param training Whether in training mode
     * @param seed Random seed
     * @return Tensor with spatial dropout applied
     */
    Eigen::MatrixXd spatial_dropout(const Eigen::MatrixXd& input,
                                  double dropout_rate,
                                  bool training = true,
                                  unsigned int seed = 0);

private:
    std::mt19937 rng_;
    
    // Helper methods
    Eigen::MatrixXd apply_mask(const Eigen::MatrixXd& matrix, const Eigen::MatrixXd& mask, double mask_value = -1e9);
    Eigen::MatrixXd create_causal_mask(int size);
    std::pair<Eigen::MatrixXd, Eigen::MatrixXd> compute_batch_stats(const Eigen::MatrixXd& input);
    Eigen::MatrixXd im2col(const Eigen::MatrixXd& input, int kernel_h, int kernel_w, 
                         int stride_h, int stride_w, int padding_h, int padding_w);
};

/**
 * @brief Sparse matrix operations
 */
class SparseMatrixOperations : public MatrixOperationsBase {
public:
    using SparseMatrix = Eigen::SparseMatrix<double>;
    using SparseLU = Eigen::SparseLU<SparseMatrix>;
    using SparseQR = Eigen::SparseQR<SparseMatrix, Eigen::COLAMDOrdering<int>>;
    
    /**
     * @brief Constructor
     * 
     * @param config Configuration for operations
     */
    explicit SparseMatrixOperations(const MatrixConfig& config = MatrixConfig());
    
    /**
     * @brief Convert dense matrix to sparse
     * 
     * @param dense_matrix Input dense matrix
     * @param threshold Threshold for considering elements as zero
     * @return Sparse matrix
     */
    SparseMatrix to_sparse(const Eigen::MatrixXd& dense_matrix, double threshold = 1e-12);
    
    /**
     * @brief Convert sparse matrix to dense
     * 
     * @param sparse_matrix Input sparse matrix
     * @return Dense matrix
     */
    Eigen::MatrixXd to_dense(const SparseMatrix& sparse_matrix);
    
    /**
     * @brief Sparse matrix multiplication
     * 
     * @param A First sparse matrix
     * @param B Second sparse matrix
     * @return Product A * B
     */
    SparseMatrix multiply(const SparseMatrix& A, const SparseMatrix& B);
    
    /**
     * @brief Sparse matrix-dense vector multiplication
     * 
     * @param A Sparse matrix
     * @param b Dense vector
     * @return Product A * b
     */
    Eigen::VectorXd multiply(const SparseMatrix& A, const Eigen::VectorXd& b);
    
    /**
     * @brief Sparse LU factorization
     * 
     * @param matrix Input sparse matrix
     * @return LU factorization object
     */
    std::unique_ptr<SparseLU> lu_factorization(const SparseMatrix& matrix);
    
    /**
     * @brief Sparse QR factorization
     * 
     * @param matrix Input sparse matrix
     * @return QR factorization object
     */
    std::unique_ptr<SparseQR> qr_factorization(const SparseMatrix& matrix);
    
    /**
     * @brief Solve sparse linear system using LU
     * 
     * @param A Coefficient matrix
     * @param b Right-hand side
     * @return Solution vector
     */
    Eigen::VectorXd solve_lu(const SparseMatrix& A, const Eigen::VectorXd& b);
    
    /**
     * @brief Solve sparse linear system using QR
     * 
     * @param A Coefficient matrix
     * @param b Right-hand side
     * @return Solution vector
     */
    Eigen::VectorXd solve_qr(const SparseMatrix& A, const Eigen::VectorXd& b);
    
    /**
     * @brief Conjugate gradient solver for sparse symmetric positive definite systems
     * 
     * @param A Coefficient matrix
     * @param b Right-hand side
     * @param x0 Initial guess
     * @param max_iter Maximum iterations
     * @param tolerance Convergence tolerance
     * @return Solution vector and convergence info
     */
    std::pair<Eigen::VectorXd, bool> conjugate_gradient(
        const SparseMatrix& A, 
        const Eigen::VectorXd& b,
        const Eigen::VectorXd& x0 = Eigen::VectorXd(),
        int max_iter = 1000,
        double tolerance = 1e-6);
    
    /**
     * @brief BiCGSTAB solver for sparse non-symmetric systems
     * 
     * @param A Coefficient matrix
     * @param b Right-hand side
     * @param x0 Initial guess
     * @param max_iter Maximum iterations
     * @param tolerance Convergence tolerance
     * @return Solution vector and convergence info
     */
    std::pair<Eigen::VectorXd, bool> bicgstab(
        const SparseMatrix& A,
        const Eigen::VectorXd& b,
        const Eigen::VectorXd& x0 = Eigen::VectorXd(),
        int max_iter = 1000,
        double tolerance = 1e-6);
    
    /**
     * @brief GMRES solver for sparse systems
     * 
     * @param A Coefficient matrix
     * @param b Right-hand side
     * @param restart Restart parameter
     * @param max_iter Maximum iterations
     * @param tolerance Convergence tolerance
     * @return Solution vector and convergence info
     */
    std::pair<Eigen::VectorXd, bool> gmres(
        const SparseMatrix& A,
        const Eigen::VectorXd& b,
        int restart = 30,
        int max_iter = 1000,
        double tolerance = 1e-6);
    
    /**
     * @brief Sparse matrix power
     * 
     * @param matrix Input sparse matrix
     * @param power Power to raise matrix to
     * @return Matrix raised to the given power
     */
    SparseMatrix matrix_power(const SparseMatrix& matrix, int power);
    
    /**
     * @brief Sparse matrix exponential (for small matrices)
     * 
     * @param matrix Input sparse matrix
     * @param num_terms Number of terms in Taylor series
     * @return Matrix exponential
     */
    SparseMatrix matrix_exponential(const SparseMatrix& matrix, int num_terms = 20);
    
    /**
     * @brief Compute sparse matrix norm
     * 
     * @param matrix Input sparse matrix
     * @param norm_type Type of norm
     * @return Norm value
     */
    double norm(const SparseMatrix& matrix, MatrixNorm norm_type);
    
    /**
     * @brief Sparse matrix transpose
     * 
     * @param matrix Input sparse matrix
     * @return Transposed matrix
     */
    SparseMatrix transpose(const SparseMatrix& matrix);
    
    /**
     * @brief Get number of non-zero elements
     * 
     * @param matrix Input sparse matrix
     * @return Number of non-zeros
     */
    int nnz(const SparseMatrix& matrix);
    
    /**
     * @brief Compute sparsity ratio
     * 
     * @param matrix Input sparse matrix
     * @return Sparsity ratio (fraction of zero elements)
     */
    double sparsity_ratio(const SparseMatrix& matrix);
    
    /**
     * @brief Extract diagonal of sparse matrix
     * 
     * @param matrix Input sparse matrix
     * @return Diagonal vector
     */
    Eigen::VectorXd diagonal(const SparseMatrix& matrix);
    
    /**
     * @brief Create sparse diagonal matrix
     * 
     * @param diagonal_values Diagonal values
     * @return Sparse diagonal matrix
     */
    SparseMatrix create_diagonal(const Eigen::VectorXd& diagonal_values);
    
    /**
     * @brief Prune small elements from sparse matrix
     * 
     * @param matrix Input sparse matrix
     * @param threshold Threshold for pruning
     * @return Pruned sparse matrix
     */
    SparseMatrix prune(const SparseMatrix& matrix, double threshold);

private:
    // Helper methods for iterative solvers
    double vector_dot(const Eigen::VectorXd& a, const Eigen::VectorXd& b);
    Eigen::VectorXd precondition(const SparseMatrix& A, const Eigen::VectorXd& r);
    void arnoldi_iteration(const SparseMatrix& A, std::vector<Eigen::VectorXd>& Q, 
                          Eigen::MatrixXd& H, int k);
};

/**
 * @brief Tensor operations for multi-dimensional arrays
 */
class TensorOperations : public MatrixOperationsBase {
public:
    using Tensor3d = std::vector<Eigen::MatrixXd>;
    using Tensor4d = std::vector<std::vector<Eigen::MatrixXd>>;
    
    /**
     * @brief Constructor
     * 
     * @param config Configuration for operations
     */
    explicit TensorOperations(const MatrixConfig& config = MatrixConfig());
    
    /**
     * @brief Create 3D tensor
     * 
     * @param dim1 First dimension
     * @param dim2 Second dimension
     * @param dim3 Third dimension
     * @return 3D tensor initialized to zero
     */
    Tensor3d create_tensor3d(int dim1, int dim2, int dim3);
    
    /**
     * @brief Create 4D tensor
     * 
     * @param dim1 First dimension
     * @param dim2 Second dimension
     * @param dim3 Third dimension
     * @param dim4 Fourth dimension
     * @return 4D tensor initialized to zero
     */
    Tensor4d create_tensor4d(int dim1, int dim2, int dim3, int dim4);
    
    /**
     * @brief Tensor contraction (generalized matrix multiplication)
     * 
     * @param tensor1 First tensor
     * @param tensor2 Second tensor
     * @param axes1 Axes to contract in first tensor
     * @param axes2 Axes to contract in second tensor
     * @return Contracted tensor
     */
    Tensor3d tensor_contraction(const Tensor3d& tensor1, const Tensor3d& tensor2,
                               const std::vector<int>& axes1, const std::vector<int>& axes2);
    
    /**
     * @brief Tensor mode-n multiplication
     * 
     * @param tensor Input tensor
     * @param matrix Matrix to multiply with
     * @param mode Mode (dimension) to multiply along
     * @return Resulting tensor
     */
    Tensor3d mode_n_product(const Tensor3d& tensor, const Eigen::MatrixXd& matrix, int mode);
    
    /**
     * @brief Tucker decomposition
     * 
     * @param tensor Input tensor
     * @param ranks Target ranks for each mode
     * @param max_iter Maximum iterations
     * @param tolerance Convergence tolerance
     * @return Core tensor and factor matrices
     */
    std::tuple<Tensor3d, std::vector<Eigen::MatrixXd>> tucker_decomposition(
        const Tensor3d& tensor, const std::vector<int>& ranks,
        int max_iter = 100, double tolerance = 1e-6);
    
    /**
     * @brief CANDECOMP/PARAFAC (CP) decomposition
     * 
     * @param tensor Input tensor
     * @param rank Target rank
     * @param max_iter Maximum iterations
     * @param tolerance Convergence tolerance
     * @return Factor matrices
     */
    std::vector<Eigen::MatrixXd> cp_decomposition(
        const Tensor3d& tensor, int rank,
        int max_iter = 100, double tolerance = 1e-6);
    
    /**
     * @brief Tensor train (TT) decomposition
     * 
     * @param tensor Input tensor (as vectorized form)
     * @param dimensions Original tensor dimensions
     * @param max_rank Maximum TT rank
     * @param tolerance Tolerance for rank selection
     * @return TT cores
     */
    std::vector<Tensor3d> tensor_train_decomposition(
        const Eigen::VectorXd& tensor, const std::vector<int>& dimensions,
        int max_rank = 10, double tolerance = 1e-12);
    
    /**
     * @brief Tensor SVD (higher-order SVD)
     * 
     * @param tensor Input tensor
     * @return Core tensor and mode matrices (U matrices)
     */
    std::tuple<Tensor3d, std::vector<Eigen::MatrixXd>> tensor_svd(const Tensor3d& tensor);
    
    /**
     * @brief Flatten tensor to matrix (matricization)
     * 
     * @param tensor Input tensor
     * @param mode Mode along which to unfold
     * @return Unfolded matrix
     */
    Eigen::MatrixXd unfold(const Tensor3d& tensor, int mode);
    
    /**
     * @brief Fold matrix back to tensor
     * 
     * @param matrix Input matrix
     * @param mode Mode along which the matrix was unfolded
     * @param tensor_dims Original tensor dimensions
     * @return Folded tensor
     */
    Tensor3d fold(const Eigen::MatrixXd& matrix, int mode, const std::vector<int>& tensor_dims);
    
    /**
     * @brief Compute tensor norm
     * 
     * @param tensor Input tensor
     * @param norm_type Type of norm (Frobenius is default for tensors)
     * @return Norm value
     */
    double tensor_norm(const Tensor3d& tensor, MatrixNorm norm_type = MatrixNorm::FROBENIUS);
    
    /**
     * @brief Tensor addition
     * 
     * @param tensor1 First tensor
     * @param tensor2 Second tensor
     * @return Sum tensor
     */
    Tensor3d tensor_add(const Tensor3d& tensor1, const Tensor3d& tensor2);
    
    /**
     * @brief Tensor scalar multiplication
     * 
     * @param tensor Input tensor
     * @param scalar Scalar value
     * @return Scaled tensor
     */
    Tensor3d tensor_scale(const Tensor3d& tensor, double scalar);
    
    /**
     * @brief Tensor inner product
     * 
     * @param tensor1 First tensor
     * @param tensor2 Second tensor
     * @return Inner product value
     */
    double tensor_inner_product(const Tensor3d& tensor1, const Tensor3d& tensor2);

private:
    // Helper methods for tensor decompositions
    std::vector<Eigen::MatrixXd> initialize_cp_factors(const std::vector<int>& dimensions, int rank);
    Eigen::MatrixXd khatri_rao_product(const Eigen::MatrixXd& A, const Eigen::MatrixXd& B);
    Eigen::MatrixXd pseudo_inverse_khatri_rao(const std::vector<Eigen::MatrixXd>& factors, int skip_mode);
    double cp_reconstruction_error(const Tensor3d& original, const std::vector<Eigen::MatrixXd>& factors);
    std::vector<int> compute_tensor_dimensions(const Tensor3d& tensor);
};

/**
 * @brief GPU-accelerated matrix operations
 */
#ifdef ULTRA_USE_CUDA
class GPUMatrixOperations : public MatrixOperationsBase {
public:
    /**
     * @brief Constructor
     * 
     * @param config Configuration for operations
     * @param device_id GPU device ID
     */
    explicit GPUMatrixOperations(const MatrixConfig& config = MatrixConfig(), int device_id = 0);
    
    /**
     * @brief Destructor
     */
    ~GPUMatrixOperations();
    
    /**
     * @brief Initialize GPU resources
     * 
     * @return True if successful
     */
    bool initialize();
    
    /**
     * @brief Cleanup GPU resources
     */
    void cleanup();
    
    /**
     * @brief Transfer matrix from host to device
     * 
     * @param host_matrix Host matrix
     * @return Device pointer
     */
    double* transfer_to_device(const Eigen::MatrixXd& host_matrix);
    
    /**
     * @brief Transfer matrix from device to host
     * 
     * @param device_ptr Device pointer
     * @param rows Number of rows
     * @param cols Number of columns
     * @return Host matrix
     */
    Eigen::MatrixXd transfer_from_device(double* device_ptr, int rows, int cols);
    
    /**
     * @brief GPU matrix multiplication using cuBLAS
     * 
     * @param A First matrix
     * @param B Second matrix
     * @return Product A * B
     */
    Eigen::MatrixXd gpu_multiply(const Eigen::MatrixXd& A, const Eigen::MatrixXd& B);
    
    /**
     * @brief GPU matrix inversion using cuSOLVER
     * 
     * @param matrix Input matrix
     * @return Matrix inverse
     */
    Eigen::MatrixXd gpu_inverse(const Eigen::MatrixXd& matrix);
    
    /**
     * @brief GPU SVD using cuSOLVER
     * 
     * @param matrix Input matrix
     * @return Tuple of (U, S, VT)
     */
    std::tuple<Eigen::MatrixXd, Eigen::VectorXd, Eigen::MatrixXd> gpu_svd(const Eigen::MatrixXd& matrix);
    
    /**
     * @brief GPU eigenvalue decomposition using cuSOLVER
     * 
     * @param matrix Input symmetric matrix
     * @return Pair of (eigenvalues, eigenvectors)
     */
    std::pair<Eigen::VectorXd, Eigen::MatrixXd> gpu_eigen_decomposition(const Eigen::MatrixXd& matrix);
    
    /**
     * @brief GPU Cholesky decomposition using cuSOLVER
     * 
     * @param matrix Input positive definite matrix
     * @return Lower triangular matrix L
     */
    Eigen::MatrixXd gpu_cholesky(const Eigen::MatrixXd& matrix);
    
    /**
     * @brief GPU QR decomposition using cuSOLVER
     * 
     * @param matrix Input matrix
     * @return Pair of (Q, R)
     */
    std::pair<Eigen::MatrixXd, Eigen::MatrixXd> gpu_qr(const Eigen::MatrixXd& matrix);
    
    /**
     * @brief GPU matrix addition
     * 
     * @param A First matrix
     * @param B Second matrix
     * @return Sum A + B
     */
    Eigen::MatrixXd gpu_add(const Eigen::MatrixXd& A, const Eigen::MatrixXd& B);
    
    /**
     * @brief GPU element-wise operations
     * 
     * @param matrix Input matrix
     * @param operation Operation name ("exp", "log", "sqrt", "sin", "cos", etc.)
     * @return Result matrix
     */
    Eigen::MatrixXd gpu_element_wise(const Eigen::MatrixXd& matrix, const std::string& operation);
    
    /**
     * @brief GPU reduction operations
     * 
     * @param matrix Input matrix
     * @param operation Reduction operation ("sum", "max", "min", "mean")
     * @param axis Axis for reduction (-1 for all elements)
     * @return Reduction result
     */
    Eigen::VectorXd gpu_reduce(const Eigen::MatrixXd& matrix, const std::string& operation, int axis = -1);
    
    /**
     * @brief Get GPU memory usage
     * 
     * @return Pair of (used_memory, total_memory) in bytes
     */
    std::pair<size_t, size_t> get_memory_usage() const;
    
    /**
     * @brief Set GPU memory pool size
     * 
     * @param size Memory pool size in bytes
     */
    void set_memory_pool_size(size_t size);

private:
    int device_id_;
    cublasHandle_t cublas_handle_;
    cusolverDnHandle_t cusolver_handle_;
    curandGenerator_t curand_generator_;
    
    // Memory management
    std::vector<void*> allocated_pointers_;
    size_t memory_pool_size_;
    size_t current_memory_usage_;
    
    // Helper methods
    void check_cublas_error(cublasStatus_t status, const std::string& operation);
    void check_cusolver_error(cusolverStatus_t status, const std::string& operation);
    void check_cuda_error(cudaError_t error, const std::string& operation);
    double* allocate_device_memory(size_t size);
    void free_device_memory(double* ptr);
    void cleanup_allocated_memory();
};
#endif // ULTRA_USE_CUDA

/**
 * @brief Parallel matrix operations using OpenMP and threading
 */
class ParallelMatrixOperations : public MatrixOperationsBase {
public:
    /**
     * @brief Constructor
     * 
     * @param config Configuration for operations
     */
    explicit ParallelMatrixOperations(const MatrixConfig& config = MatrixConfig());
    
    /**
     * @brief Set number of threads
     * 
     * @param num_threads Number of threads to use (0 = hardware concurrency)
     */
    void set_num_threads(int num_threads);
    
    /**
     * @brief Get number of threads being used
     * 
     * @return Number of threads
     */
    int get_num_threads() const;
    
    /**
     * @brief Parallel matrix multiplication
     * 
     * @param A First matrix
     * @param B Second matrix
     * @return Product A * B
     */
    Eigen::MatrixXd parallel_multiply(const Eigen::MatrixXd& A, const Eigen::MatrixXd& B);
    
    /**
     * @brief Parallel block matrix multiplication
     * 
     * @param A First matrix
     * @param B Second matrix
     * @param block_size Block size for partitioning
     * @return Product A * B
     */
    Eigen::MatrixXd parallel_block_multiply(const Eigen::MatrixXd& A, const Eigen::MatrixXd& B, int block_size);
    
    /**
     * @brief Parallel matrix addition
     * 
     * @param A First matrix
     * @param B Second matrix
     * @return Sum A + B
     */
    Eigen::MatrixXd parallel_add(const Eigen::MatrixXd& A, const Eigen::MatrixXd& B);
    
    /**
     * @brief Parallel element-wise operations
     * 
     * @param matrix Input matrix
     * @param operation Function to apply element-wise
     * @return Result matrix
     */
    Eigen::MatrixXd parallel_element_wise(const Eigen::MatrixXd& matrix, 
                                        std::function<double(double)> operation);
    
    /**
     * @brief Parallel matrix norm computation
     * 
     * @param matrix Input matrix
     * @param norm_type Type of norm
     * @return Norm value
     */
    double parallel_norm(const Eigen::MatrixXd& matrix, MatrixNorm norm_type);
    
    /**
     * @brief Parallel LU decomposition
     * 
     * @param matrix Input matrix
     * @return Tuple of (L, U, P)
     */
    std::tuple<Eigen::MatrixXd, Eigen::MatrixXd, Eigen::PermutationMatrix<Eigen::Dynamic>>
    parallel_lu_decomposition(const Eigen::MatrixXd& matrix);
    
    /**
     * @brief Parallel QR decomposition
     * 
     * @param matrix Input matrix
     * @return Pair of (Q, R)
     */
    std::pair<Eigen::MatrixXd, Eigen::MatrixXd> parallel_qr_decomposition(const Eigen::MatrixXd& matrix);
    
    /**
     * @brief Parallel matrix transpose
     * 
     * @param matrix Input matrix
     * @return Transposed matrix
     */
    Eigen::MatrixXd parallel_transpose(const Eigen::MatrixXd& matrix);
    
    /**
     * @brief Parallel reduction operations
     * 
     * @param matrix Input matrix
     * @param operation Reduction operation
     * @param axis Axis for reduction
     * @return Reduction result
     */
    Eigen::VectorXd parallel_reduce(const Eigen::MatrixXd& matrix,
                                  std::function<double(double, double)> operation,
                                  int axis = -1);
    
    /**
     * @brief Parallel matrix power
     * 
     * @param matrix Input matrix
     * @param power Power to raise matrix to
     * @return Matrix raised to the given power
     */
    Eigen::MatrixXd parallel_matrix_power(const Eigen::MatrixXd& matrix, int power);

private:
    int num_threads_;
    
    // Helper methods
    void parallel_multiply_block(const Eigen::MatrixXd& A, const Eigen::MatrixXd& B, 
                               Eigen::MatrixXd& C, int start_row, int end_row);
    std::pair<int, int> get_optimal_block_dimensions(int m, int n, int k, int num_threads);
};

/**
 * @brief Memory-efficient operations for large matrices
 */
class MemoryEfficientOperations : public MatrixOperationsBase {
public:
    /**
     * @brief Constructor
     * 
     * @param config Configuration for operations
     */
    explicit MemoryEfficientOperations(const MatrixConfig& config = MatrixConfig());
    
    /**
     * @brief Out-of-core matrix multiplication
     * 
     * @param A First matrix (file path or memory)
     * @param B Second matrix (file path or memory)
     * @param output_path Output file path
     * @param block_size Block size for processing
     * @return True if successful
     */
    bool out_of_core_multiply(const std::string& A_path, const std::string& B_path,
                            const std::string& output_path, int block_size = 1000);
    
    /**
     * @brief Streaming SVD for large matrices
     * 
     * @param matrix_stream Stream of matrix blocks
     * @param target_rank Target rank
     * @param block_size Size of each block
     * @return Approximate SVD components
     */
    std::tuple<Eigen::MatrixXd, Eigen::VectorXd, Eigen::MatrixXd>
    streaming_svd(std::function<Eigen::MatrixXd()> matrix_stream, int target_rank, int block_size);
    
    /**
     * @brief Incremental PCA for streaming data
     * 
     * @param new_data New data batch
     * @param components Current PCA components
     * @param mean Current mean
     * @param n_samples Current number of samples
     * @param n_components Number of components to keep
     * @return Updated (components, mean, n_samples)
     */
    std::tuple<Eigen::MatrixXd, Eigen::VectorXd, int>
    incremental_pca(const Eigen::MatrixXd& new_data, const Eigen::MatrixXd& components,
                   const Eigen::VectorXd& mean, int n_samples, int n_components);
    
    /**
     * @brief Low-rank matrix completion
     * 
     * @param incomplete_matrix Matrix with missing values (NaN)
     * @param rank Target rank
     * @param max_iter Maximum iterations
     * @param tolerance Convergence tolerance
     * @return Completed matrix
     */
    Eigen::MatrixXd low_rank_completion(const Eigen::MatrixXd& incomplete_matrix, int rank,
                                      int max_iter = 100, double tolerance = 1e-6);
    
    /**
     * @brief Matrix approximation using random sampling
     * 
     * @param matrix Input matrix
     * @param target_rank Target rank
     * @param oversampling Oversampling parameter
     * @param n_iter Number of power iterations
     * @return Low-rank approximation
     */
    Eigen::MatrixXd randomized_approximation(const Eigen::MatrixXd& matrix, int target_rank,
                                           int oversampling = 10, int n_iter = 2);
    
    /**
     * @brief Sketched matrix multiplication
     * 
     * @param A First matrix
     * @param B Second matrix
     * @param sketch_size Size of sketch
     * @return Approximate product
     */
    Eigen::MatrixXd sketched_multiply(const Eigen::MatrixXd& A, const Eigen::MatrixXd& B, int sketch_size);
    
    /**
     * @brief Memory-mapped matrix operations
     * 
     * @param file_path Path to matrix file
     * @param rows Number of rows
     * @param cols Number of columns
     * @param operation Operation to perform on matrix
     * @return Result of operation
     */
    Eigen::MatrixXd memory_mapped_operation(const std::string& file_path, int rows, int cols,
                                          std::function<Eigen::MatrixXd(const Eigen::MatrixXd&)> operation);
    
    /**
     * @brief Adaptive precision arithmetic
     * 
     * @param matrix Input matrix
     * @param operation Operation to perform
     * @param target_accuracy Target accuracy
     * @return Result with adaptive precision
     */
    Eigen::MatrixXd adaptive_precision_operation(const Eigen::MatrixXd& matrix,
                                               std::function<Eigen::MatrixXd(const Eigen::MatrixXd&)> operation,
                                               double target_accuracy = 1e-12);

private:
    // Helper methods
    void save_matrix_to_file(const Eigen::MatrixXd& matrix, const std::string& file_path);
    Eigen::MatrixXd load_matrix_from_file(const std::string& file_path, int rows, int cols);
    Eigen::MatrixXd read_matrix_block(const std::string& file_path, int start_row, int start_col,
                                    int block_rows, int block_cols);
    void write_matrix_block(const std::string& file_path, const Eigen::MatrixXd& block,
                          int start_row, int start_col);
    Eigen::MatrixXd generate_random_matrix(int rows, int cols, const std::string& distribution = "gaussian");
};

/**
 * @brief Matrix differential calculus operations
 */
class MatrixDifferentialCalculus : public MatrixOperationsBase {
public:
    /**
     * @brief Constructor
     * 
     * @param config Configuration for operations
     */
    explicit MatrixDifferentialCalculus(const MatrixConfig& config = MatrixConfig());
    
    /**
     * @brief Matrix exponential using Padé approximation
     * 
     * @param matrix Input matrix
     * @param order Order of Padé approximation
     * @return Matrix exponential exp(A)
     */
    Eigen::MatrixXd matrix_exponential(const Eigen::MatrixXd& matrix, int order = 6);
    
    /**
     * @brief Matrix logarithm
     * 
     * @param matrix Input matrix (must be non-singular)
     * @return Matrix logarithm log(A)
     */
    Eigen::MatrixXd matrix_logarithm(const Eigen::MatrixXd& matrix);
    
    /**
     * @brief Matrix square root
     * 
     * @param matrix Input matrix (must be positive definite)
     * @return Matrix square root sqrt(A)
     */
    Eigen::MatrixXd matrix_square_root(const Eigen::MatrixXd& matrix);
    
    /**
     * @brief Matrix power for real exponent
     * 
     * @param matrix Input matrix
     * @param exponent Real exponent
     * @return Matrix power A^p
     */
    Eigen::MatrixXd matrix_power(const Eigen::MatrixXd& matrix, double exponent);
    
    /**
     * @brief Matrix sine
     * 
     * @param matrix Input matrix
     * @return sin(A)
     */
    Eigen::MatrixXd matrix_sine(const Eigen::MatrixXd& matrix);
    
    /**
     * @brief Matrix cosine
     * 
     * @param matrix Input matrix
     * @return cos(A)
     */
    Eigen::MatrixXd matrix_cosine(const Eigen::MatrixXd& matrix);
    
    /**
     * @brief Matrix hyperbolic sine
     * 
     * @param matrix Input matrix
     * @return sinh(A)
     */
    Eigen::MatrixXd matrix_sinh(const Eigen::MatrixXd& matrix);
    
    /**
     * @brief Matrix hyperbolic cosine
     * 
     * @param matrix Input matrix
     * @return cosh(A)
     */
    Eigen::MatrixXd matrix_cosh(const Eigen::MatrixXd& matrix);
    
    /**
     * @brief Numerical differentiation of matrix function
     * 
     * @param matrix_function Function that takes matrix and returns matrix
     * @param matrix Input matrix
     * @param direction Direction vector for directional derivative
     * @param h Step size for finite differences
     * @return Directional derivative
     */
    Eigen::MatrixXd numerical_derivative(
        std::function<Eigen::MatrixXd(const Eigen::MatrixXd&)> matrix_function,
        const Eigen::MatrixXd& matrix,
        const Eigen::MatrixXd& direction,
        double h = 1e-8);
    
    /**
     * @brief Fréchet derivative of matrix exponential
     * 
     * @param matrix Input matrix A
     * @param direction Direction matrix E
     * @return Fréchet derivative of exp at A in direction E
     */
    Eigen::MatrixXd frechet_derivative_exp(const Eigen::MatrixXd& matrix, const Eigen::MatrixXd& direction);
    
    /**
     * @brief Fréchet derivative of matrix logarithm
     * 
     * @param matrix Input matrix A
     * @param direction Direction matrix E
     * @return Fréchet derivative of log at A in direction E
     */
    Eigen::MatrixXd frechet_derivative_log(const Eigen::MatrixXd& matrix, const Eigen::MatrixXd& direction);
    
    /**
     * @brief Lyapunov equation solver: AX + XB = C
     * 
     * @param A First coefficient matrix
     * @param B Second coefficient matrix
     * @param C Right-hand side matrix
     * @return Solution matrix X
     */
    Eigen::MatrixXd solve_lyapunov(const Eigen::MatrixXd& A, const Eigen::MatrixXd& B, const Eigen::MatrixXd& C);
    
    /**
     * @brief Sylvester equation solver: AX + XB = C
     * 
     * @param A First coefficient matrix
     * @param B Second coefficient matrix
     * @param C Right-hand side matrix
     * @return Solution matrix X
     */
    Eigen::MatrixXd solve_sylvester(const Eigen::MatrixXd& A, const Eigen::MatrixXd& B, const Eigen::MatrixXd& C);
    
    /**
     * @brief Riccati equation solver: A^T X + X A - X B R^(-1) B^T X + Q = 0
     * 
     * @param A System matrix
     * @param B Input matrix
     * @param Q State cost matrix
     * @param R Input cost matrix
     * @return Solution matrix X
     */
    Eigen::MatrixXd solve_riccati(const Eigen::MatrixXd& A, const Eigen::MatrixXd& B,
                                const Eigen::MatrixXd& Q, const Eigen::MatrixXd& R);

private:
    // Helper methods for matrix functions
    Eigen::MatrixXd pade_approximation(const Eigen::MatrixXd& matrix, int order);
    std::pair<Eigen::MatrixXd, int> matrix_exponential_pade(const Eigen::MatrixXd& matrix);
    Eigen::MatrixXd schur_parlett_algorithm(const Eigen::MatrixXd& matrix, 
                                          std::function<std::complex<double>(std::complex<double>)> f);
    double matrix_one_norm_estimate(const Eigen::MatrixXd& matrix);
};

/**
 * @brief Stochastic matrix operations and random matrix theory
 */
class StochasticMatrixOperations : public MatrixOperationsBase {
public:
    /**
     * @brief Constructor
     * 
     * @param config Configuration for operations
     * @param seed Random seed
     */
    explicit StochasticMatrixOperations(const MatrixConfig& config = MatrixConfig(), unsigned int seed = 0);
    
    /**
     * @brief Generate random matrix from specified distribution
     * 
     * @param rows Number of rows
     * @param cols Number of columns
     * @param distribution Distribution type ("gaussian", "uniform", "bernoulli", etc.)
     * @param params Distribution parameters
     * @return Random matrix
     */
    Eigen::MatrixXd random_matrix(int rows, int cols, const std::string& distribution = "gaussian",
                                const std::map<std::string, double>& params = {});
    
    /**
     * @brief Generate random orthogonal matrix
     * 
     * @param size Matrix size
     * @return Random orthogonal matrix
     */
    Eigen::MatrixXd random_orthogonal_matrix(int size);
    
    /**
     * @brief Generate random positive definite matrix
     * 
     * @param size Matrix size
     * @param condition_number Target condition number
     * @return Random positive definite matrix
     */
    Eigen::MatrixXd random_positive_definite_matrix(int size, double condition_number = 10.0);
    
    /**
     * @brief Generate random sparse matrix
     * 
     * @param rows Number of rows
     * @param cols Number of columns
     * @param density Density of non-zero elements
     * @param distribution Distribution for non-zero elements
     * @return Random sparse matrix
     */
    Eigen::SparseMatrix<double> random_sparse_matrix(int rows, int cols, double density = 0.1,
                                                    const std::string& distribution = "gaussian");
    
    /**
     * @brief Generate Wishart matrix
     * 
     * @param dimension Matrix dimension
     * @param degrees_freedom Degrees of freedom
     * @param scale_matrix Scale matrix
     * @return Wishart random matrix
     */
    Eigen::MatrixXd wishart_matrix(int dimension, int degrees_freedom, const Eigen::MatrixXd& scale_matrix);
    
    /**
     * @brief Generate random matrix from Gaussian Orthogonal Ensemble (GOE)
     * 
     * @param size Matrix size
     * @param variance Variance parameter
     * @return GOE random matrix
     */
    Eigen::MatrixXd goe_matrix(int size, double variance = 1.0);
    
    /**
     * @brief Generate random matrix from Gaussian Unitary Ensemble (GUE)
     * 
     * @param size Matrix size
     * @param variance Variance parameter
     * @return GUE random matrix
     */
    Eigen::MatrixXcd gue_matrix(int size, double variance = 1.0);
    
    /**
     * @brief Compute empirical spectral distribution
     * 
     * @param matrix Input matrix
     * @param num_bins Number of histogram bins
     * @return Pair of (bin_centers, densities)
     */
    std::pair<Eigen::VectorXd, Eigen::VectorXd> empirical_spectral_distribution(
        const Eigen::MatrixXd& matrix, int num_bins = 100);
    
    /**
     * @brief Test for circular law (random matrix theory)
     * 
     * @param matrix Input matrix
     * @return P-value for circular law test
     */
    double circular_law_test(const Eigen::MatrixXd& matrix);
    
    /**
     * @brief Test for semicircle law (Wigner's theorem)
     * 
     * @param matrix Input symmetric matrix
     * @return P-value for semicircle law test
     */
    double semicircle_law_test(const Eigen::MatrixXd& matrix);
    
    /**
     * @brief Marchenko-Pastur law test
     * 
     * @param matrix Input matrix
     * @param aspect_ratio Aspect ratio (p/n)
     * @return P-value for Marchenko-Pastur law test
     */
    double marchenko_pastur_test(const Eigen::MatrixXd& matrix, double aspect_ratio);
    
    /**
     * @brief Compute Tracy-Widom distribution statistics
     * 
     * @param matrix Input matrix
     * @param ensemble Ensemble type ("GOE", "GUE", "GSE")
     * @return Tracy-Widom statistic
     */
    double tracy_widom_statistic(const Eigen::MatrixXd& matrix, const std::string& ensemble = "GOE");
    
    /**
     * @brief Random matrix denoising using RMT
     * 
     * @param noisy_matrix Input noisy matrix
     * @param noise_variance Estimated noise variance
     * @return Denoised matrix
     */
    Eigen::MatrixXd rmt_denoising(const Eigen::MatrixXd& noisy_matrix, double noise_variance);
    
    /**
     * @brief Optimal shrinkage estimation (Ledoit-Wolf)
     * 
     * @param sample_covariance Sample covariance matrix
     * @param num_samples Number of samples
     * @return Shrunk covariance matrix
     */
    Eigen::MatrixXd ledoit_wolf_shrinkage(const Eigen::MatrixXd& sample_covariance, int num_samples);
    
    /**
     * @brief Bootstrap matrix sampling
     * 
     * @param matrix Input matrix
     * @param num_bootstrap Number of bootstrap samples
     * @param block_size Block size for block bootstrap
     * @return Vector of bootstrap matrices
     */
    std::vector<Eigen::MatrixXd> bootstrap_sampling(const Eigen::MatrixXd& matrix, 
                                                   int num_bootstrap, 
                                                   int block_size = 1);

private:
    std::mt19937 rng_;
    std::uniform_real_distribution<double> uniform_dist_;
    std::normal_distribution<double> normal_dist_;
    
    // Helper methods
    Eigen::MatrixXd householder_reflection(const Eigen::VectorXd& v);
    double kolmogorov_smirnov_test(const Eigen::VectorXd& sample, 
                                 std::function<double(double)> cdf);
    Eigen::VectorXd wigner_semicircle_cdf(const Eigen::VectorXd& x, double radius = 2.0);
    Eigen::VectorXd marchenko_pastur_cdf(const Eigen::VectorXd& x, double gamma);
};

} // namespace matrix_operations
} // namespace utils
} // namespace ultra

#endif // ULTRA_UTILS_MATRIX_OPERATIONS_H
