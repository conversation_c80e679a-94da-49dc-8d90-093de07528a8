int max_iter = 1000,
                   double t_initial = 100.0,
                   double t_final = 1e-8,
                   double alpha = 0.95,
                   unsigned int seed = 0) {
    
    int n = x0.size();
    
    // Initialize random number generator
    std::mt19937 gen(seed == 0 ? std::random_device{}() : seed);
    std::uniform_real_distribution<double> dist(0.0, 1.0);
    
    // Initialize current solution
    Eigen::VectorXd x_current = x0;
    double f_current = f(x_current);
    
    // Initialize best solution
    Eigen::VectorXd x_best = x_current;
    double f_best = f_current;
    
    // Initialize temperature
    double temperature = t_initial;
    
    // Main loop
    for (int iter = 0; iter < max_iter && temperature > t_final; ++iter) {
        // Generate new solution
        Eigen::VectorXd x_new = x_current;
        
        for (int j = 0; j < n; ++j) {
            double range = bounds[j].second - bounds[j].first;
            double step = range * 0.1 * (dist(gen) * 2.0 - 1.0) * temperature / t_initial;
            x_new(j) = std::max(bounds[j].first, std::min(bounds[j].second, x_new(j) + step));
        }
        
        // Evaluate new solution
        double f_new = f(x_new);
        
        // Decide whether to accept the new solution
        bool accept = false;
        
        if (f_new < f_current) {
            // Accept better solution
            accept = true;
        } else {
            // Accept worse solution with probability based on temperature
            double acceptance_prob = std::exp((f_current - f_new) / temperature);
            accept = dist(gen) < acceptance_prob;
        }
        
        if (accept) {
            x_current = x_new;
            f_current = f_new;
            
            // Update best solution
            if (f_new < f_best) {
                x_best = x_new;
                f_best = f_new;
            }
        }
        
        // Cool down
        temperature *= alpha;
    }
    
    return x_best;
}

/**
 * @brief Differential Evolution for global optimization
 * 
 * @param f Objective function
 * @param bounds Vector of pairs (lower, upper) for each dimension
 * @param population_size Population size (default: 10 * dimensions)
 * @param max_iter Maximum number of iterations
 * @param F Mutation factor
 * @param CR Crossover probability
 * @param seed Random seed
 * @return Optimized parameters
 */
inline Eigen::VectorXd
differential_evolution(const std::function<double(const Eigen::VectorXd&)>& f,
                      const std::vector<std::pair<double, double>>& bounds,
                      int population_size = 0,
                      int max_iter = 100,
                      double F = 0.8,
                      double CR = 0.7,
                      unsigned int seed = 0) {
    
    int n = bounds.size();
    
    // Set default population size if not specified
    if (population_size <= 0) {
        population_size = 10 * n;
    }
    
    // Initialize random number generator
    std::mt19937 gen(seed == 0 ? std::random_device{}() : seed);
    std::uniform_real_distribution<double> dist(0.0, 1.0);
    std::uniform_int_distribution<int> int_dist(0, n - 1);
    
    // Initialize population
    std::vector<Eigen::VectorXd> population(population_size);
    std::vector<double> fitness(population_size);
    
    for (int i = 0; i < population_size; ++i) {
        population[i] = Eigen::VectorXd(n);
        
        for (int j = 0; j < n; ++j) {
            double range = bounds[j].second - bounds[j].first;
            population[i](j) = bounds[j].first + dist(gen) * range;
        }
        
        fitness[i] = f(population[i]);
    }
    
    // Main loop
    for (int iter = 0; iter < max_iter; ++iter) {
        for (int i = 0; i < population_size; ++i) {
            // Select three random distinct individuals
            int a, b, c;
            do {
                a = std::uniform_int_distribution<int>(0, population_size - 1)(gen);
            } while (a == i);
            
            do {
                b = std::uniform_int_distribution<int>(0, population_size - 1)(gen);
            } while (b == i || b == a);
            
            do {
                c = std::uniform_int_distribution<int>(0, population_size - 1)(gen);
            } while (c == i || c == a || c == b);
            
            // Create mutant vector
            Eigen::VectorXd mutant = population[a] + F * (population[b] - population[c]);
            
            // Ensure mutant is within bounds
            for (int j = 0; j < n; ++j) {
                mutant(j) = std::max(bounds[j].first, std::min(bounds[j].second, mutant(j)));
            }
            
            // Create trial vector through crossover
            Eigen::VectorXd trial = population[i];
            int j_rand = int_dist(gen); // Ensure at least one parameter from mutant
            
            for (int j = 0; j < n; ++j) {
                if (j == j_rand || dist(gen) < CR) {
                    trial(j) = mutant(j);
                }
            }
            
            // Evaluate trial vector
            double trial_fitness = f(trial);
            
            // Selection
            if (trial_fitness < fitness[i]) {
                population[i] = trial;
                fitness[i] = trial_fitness;
            }
        }
    }
    
    // Find best solution
    int best_idx = 0;
    for (int i = 1; i < population_size; ++i) {
        if (fitness[i] < fitness[best_idx]) {
            best_idx = i;
        }
    }
    
    return population[best_idx];
}

/**
 * @brief Sequential Least Squares Programming (SLSQP) for constrained optimization
 * 
 * This is a simplified version that handles equality and inequality constraints
 * 
 * @param f Objective function
 * @param grad Gradient function
 * @param x0 Initial point
 * @param eq_constraints Vector of equality constraint functions (each returns a scalar)
 * @param eq_jacs Vector of Jacobians for equality constraints
 * @param ineq_constraints Vector of inequality constraint functions (g(x) <= 0)
 * @param ineq_jacs Vector of Jacobians for inequality constraints
 * @param max_iter Maximum number of iterations
 * @param tolerance Convergence tolerance
 * @return Optimized parameters
 */
inline Eigen::VectorXd
slsqp(const std::function<double(const Eigen::VectorXd&)>& f,
     const std::function<Eigen::VectorXd(const Eigen::VectorXd&)>& grad,
     const Eigen::VectorXd& x0,
     const std::vector<std::function<double(const Eigen::VectorXd&)>>& eq_constraints,
     const std::vector<std::function<Eigen::VectorXd(const Eigen::VectorXd&)>>& eq_jacs,
     const std::vector<std::function<double(const Eigen::VectorXd&)>>& ineq_constraints,
     const std::vector<std::function<Eigen::VectorXd(const Eigen::VectorXd&)>>& ineq_jacs,
     int max_iter = 100,
     double tolerance = 1e-6) {
    
    int n = x0.size();
    int m_eq = eq_constraints.size();
    int m_ineq = ineq_constraints.size();
    
    Eigen::VectorXd x = x0;
    Eigen::MatrixXd B = Eigen::MatrixXd::Identity(n, n); // Approximate Hessian
    
    for (int iter = 0; iter < max_iter; ++iter) {
        // Evaluate objective and constraints
        double fx = f(x);
        Eigen::VectorXd g = grad(x);
        
        std::vector<double> c_eq_vals(m_eq);
        std::vector<Eigen::VectorXd> c_eq_grads(m_eq);
        for (int i = 0; i < m_eq; ++i) {
            c_eq_vals[i] = eq_constraints[i](x);
            c_eq_grads[i] = eq_jacs[i](x);
        }
        
        std::vector<double> c_ineq_vals(m_ineq);
        std::vector<Eigen::VectorXd> c_ineq_grads(m_ineq);
        std::vector<int> active_ineq;
        
        for (int i = 0; i < m_ineq; ++i) {
            c_ineq_vals[i] = ineq_constraints[i](x);
            c_ineq_grads[i] = ineq_jacs[i](x);
            
            // Check if inequality constraint is active
            if (c_ineq_vals[i] > -tolerance) {
                active_ineq.push_back(i);
            }
        }
        
        int m_active = m_eq + active_ineq.size();
        
        // Check feasibility
        bool feasible = true;
        for (double c_eq : c_eq_vals) {
            if (std::abs(c_eq) > tolerance) {
                feasible = false;
                break;
            }
        }
        
        if (feasible) {
            // Check convergence
            double opt_norm = g.norm();
            for (int i = 0; i < m_eq; ++i) {
                opt_norm -= c_eq_grads[i].norm() * std::abs(c_eq_vals[i]);
            }
            
            for (int idx : active_ineq) {
                opt_norm -= c_ineq_grads[idx].norm() * std::abs(c_ineq_vals[idx]);
            }
            
            if (opt_norm < tolerance) {
                break;
            }
        }
        
        // Set up QP subproblem
        Eigen::MatrixXd A(m_active, n);
        Eigen::VectorXd b(m_active);
        
        for (int i = 0; i < m_eq; ++i) {
            A.row(i) = c_eq_grads[i].transpose();
            b(i) = -c_eq_vals[i];
        }
        
        for (int i = 0; i < active_ineq.size(); ++i) {
            int idx = active_ineq[i];
            A.row(m_eq + i) = c_ineq_grads[idx].transpose();
            b(m_eq + i) = -c_ineq_vals[idx];
        }
        
        // Solve QP subproblem (simplified using Lagrangian method)
        Eigen::MatrixXd H(n + m_active, n + m_active);
        Eigen::VectorXd c(n + m_active);
        
        H.setZero();
        H.block(0, 0, n, n) = B;
        H.block(0, n, n, m_active) = -A.transpose();
        H.block(n, 0, m_active, n) = A;
        
        c.setZero();
        c.head(n) = g;
        c.tail(m_active) = b;
        
        Eigen::VectorXd sol = H.partialPivLu().solve(-c);
        Eigen::VectorXd p = sol.head(n);
        
        // Line search to find step size
        double alpha = 1.0;
        double c1 = 1e-4;
        double rho = 0.5;
        
        auto merit_function = [&](const Eigen::VectorXd& x_val) {
            double result = f(x_val);
            
            for (int i = 0; i < m_eq; ++i) {
                result += 100.0 * std::abs(eq_constraints[i](x_val));
            }
            
            for (int i = 0; i < m_ineq; ++i) {
                result += 100.0 * std::max(0.0, ineq_constraints[i](x_val));
            }
            
            return result;
        };
        
        double phi_0 = merit_function(x);
        double dphi_0 = g.dot(p);
        
        while (alpha > 1e-10) {
            Eigen::VectorXd x_new = x + alpha * p;
            double phi = merit_function(x_new);
            
            if (phi <= phi_0 + c1 * alpha * dphi_0) {
                x = x_new;
                break;
            }
            
            alpha *= rho;
        }
        
        // Update approximate Hessian using BFGS
        Eigen::VectorXd g_new = grad(x);
        Eigen::VectorXd y = g_new - g;
        double yp = y.dot(p);
        
        if (yp > 0) {
            Eigen::VectorXd Bp = B * p;
            B += (y * y.transpose()) / yp - (Bp * Bp.transpose()) / (p.dot(Bp));
        }
    }
    
    return x;
}

} // namespace optimization

/**
 * @brief Interpolation and curve fitting
 */
namespace interpolation {

/**
 * @brief Linear interpolation on 1D data
 * 
 * @param x Known x-coordinates
 * @param y Known y-coordinates
 * @param x_new Points at which to interpolate
 * @return Interpolated values
 */
template <typename Derived1, typename Derived2, typename Derived3>
Eigen::VectorXd
linear_interpolation(const Eigen::MatrixBase<Derived1>& x,
                    const Eigen::MatrixBase<Derived2>& y,
                    const Eigen::MatrixBase<Derived3>& x_new) {
    
    if (x.size() != y.size()) {
        throw std::invalid_argument("Input vectors x and y must have the same size");
    }
    
    int n = x.size();
    int m = x_new.size();
    
    Eigen::VectorXd result(m);
    
    for (int i = 0; i < m; ++i) {
        double x_val = x_new(i);
        
        // Handle extrapolation for x_val outside the range of x
        if (x_val <= x(0)) {
            result(i) = y(0);
            continue;
        } else if (x_val >= x(n - 1)) {
            result(i) = y(n - 1);
            continue;
        }
        
        // Find the interval [x(j), x(j+1)] containing x_val
        int j = 0;
        while (j < n - 1 && x(j + 1) < x_val) {
            ++j;
        }
        
        // Linear interpolation formula
        double t = (x_val - x(j)) / (x(j + 1) - x(j));
        result(i) = (1.0 - t) * y(j) + t * y(j + 1);
    }
    
    return result;
}

/**
 * @brief Cubic spline interpolation on 1D data
 * 
 * @param x Known x-coordinates
 * @param y Known y-coordinates
 * @param x_new Points at which to interpolate
 * @param boundary_condition Boundary condition type ("natural", "clamped", "not-a-knot")
 * @param bc_values Boundary condition values (for "clamped")
 * @return Interpolated values
 */
template <typename Derived1, typename Derived2, typename Derived3>
Eigen::VectorXd
cubic_spline_interpolation(const Eigen::MatrixBase<Derived1>& x,
                          const Eigen::MatrixBase<Derived2>& y,
                          const Eigen::MatrixBase<Derived3>& x_new,
                          const std::string& boundary_condition = "natural",
                          const std::pair<double, double>& bc_values = {0.0, 0.0}) {
    
    if (x.size() != y.size()) {
        throw std::invalid_argument("Input vectors x and y must have the same size");
    }
    
    int n = x.size();
    int m = x_new.size();
    
    if (n < 3) {
        return linear_interpolation(x, y, x_new);
    }
    
    // Compute the tridiagonal system
    Eigen::VectorXd h(n - 1);
    Eigen::VectorXd alpha(n - 1);
    
    for (int i = 0; i < n - 1; ++i) {
        h(i) = x(i + 1) - x(i);
        if (h(i) <= 0) {
            throw std::invalid_argument("x must be strictly increasing");
        }
    }
    
    for (int i = 1; i < n - 1; ++i) {
        alpha(i) = 3.0 * ((y(i + 1) - y(i)) / h(i) - (y(i) - y(i - 1)) / h(i - 1));
    }
    
    // Set up the linear system with tridiagonal matrix A
    Eigen::VectorXd l(n);
    Eigen::VectorXd mu(n);
    Eigen::VectorXd z(n);
    Eigen::VectorXd c(n);
    Eigen::VectorXd b(n - 1);
    Eigen::VectorXd d(n - 1);
    
    // Apply boundary conditions
    if (boundary_condition == "natural") {
        // Natural spline: second derivatives at endpoints are zero
        l(0) = 1.0;
        mu(0) = 0.0;
        z(0) = 0.0;
        l(n - 1) = 1.0;
        z(n - 1) = 0.0;
    } else if (boundary_condition == "clamped") {
        // Clamped spline: first derivatives at endpoints are specified
        l(0) = 2.0 * h(0);
        mu(0) = 0.5;
        z(0) = (3.0 * (y(1) - y(0)) / h(0) - 3.0 * bc_values.first) / l(0);
        l(n - 1) = 2.0 * h(n - 2);
        z(n - 1) = (3.0 * bc_values.second - 3.0 * (y(n - 1) - y(n - 2)) / h(n - 2)) / l(n - 1);
    } else if (boundary_condition == "not-a-knot") {
        // Not-a-knot: third derivatives are continuous at x(1) and x(n-2)
        if (n < 4) {
            throw std::invalid_argument("Not-a-knot requires at least 4 points");
        }
        
        double h0 = x(1) - x(0);
        double h1 = x(2) - x(1);
        l(0) = h1;
        mu(0) = h0 / (h0 + h1);
        z(0) = ((y(2) - y(1)) / h1 - (y(1) - y(0)) / h0) * 3.0 / (h0 + h1);
        
        double hn_2 = x(n - 1) - x(n - 2);
        double hn_3 = x(n - 2) - x(n - 3);
        l(n - 1) = hn_3;
        z(n - 1) = ((y(n - 1) - y(n - 2)) / hn_2 - (y(n - 2) - y(n - 3)) / hn_3) * 3.0 / (hn_2 + hn_3);
    } else {
        throw std::invalid_argument("Unsupported boundary condition");
    }
    
    // Solve the tridiagonal system
    for (int i = 1; i < n - 1; ++i) {
        l(i) = 2.0 * (x(i + 1) - x(i - 1)) - h(i - 1) * mu(i - 1);
        mu(i) = h(i) / l(i);
        z(i) = (alpha(i) - h(i - 1) * z(i - 1)) / l(i);
    }
    
    // Compute the coefficients of the cubic splines
    c(n - 1) = z(n - 1);
    
    for (int j = n - 2; j >= 0; --j) {
        c(j) = z(j) - mu(j) * c(j + 1);
        b(j) = (y(j + 1) - y(j)) / h(j) - h(j) * (c(j + 1) + 2.0 * c(j)) / 3.0;
        d(j) = (c(j + 1) - c(j)) / (3.0 * h(j));
    }
    
    // Interpolate at the new points
    Eigen::VectorXd result(m);
    
    for (int i = 0; i < m; ++i) {
        double x_val = x_new(i);
        
        // Handle extrapolation
        if (x_val <= x(0)) {
            result(i) = y(0);
            continue;
        } else if (x_val >= x(n - 1)) {
            result(i) = y(n - 1);
            continue;
        }
        
        // Find the interval [x(j), x(j+1)] containing x_val
        int j = 0;
        while (j < n - 1 && x(j + 1) < x_val) {
            ++j;
        }
        
        // Compute the interpolated value
        double dx = x_val - x(j);
        result(i) = y(j) + b(j) * dx + c(j) * dx * dx + d(j) * dx * dx * dx;
    }
    
    return result;
}

/**
 * @brief Polynomial interpolation using Lagrange polynomials
 * 
 * @param x Known x-coordinates
 * @param y Known y-coordinates
 * @param x_new Points at which to interpolate
 * @return Interpolated values
 */
template <typename Derived1, typename Derived2, typename Derived3>
Eigen::VectorXd
lagrange_interpolation(const Eigen::MatrixBase<Derived1>& x,
                      const Eigen::MatrixBase<Derived2>& y,
                      const Eigen::MatrixBase<Derived3>& x_new) {
    
    if (x.size() != y.size()) {
        throw std::invalid_argument("Input vectors x and y must have the same size");
    }
    
    int n = x.size();
    int m = x_new.size();
    
    Eigen::VectorXd result(m);
    result.setZero();
    
    for (int i = 0; i < m; ++i) {
        double x_val = x_new(i);
        
        for (int j = 0; j < n; ++j) {
            double basis = 1.0;
            
            for (int k = 0; k < n; ++k) {
                if (k != j) {
                    basis *= (x_val - x(k)) / (x(j) - x(k));
                }
            }
            
            result(i) += y(j) * basis;
        }
    }
    
    return result;
}

/**
 * @brief Bilinear interpolation on a 2D grid
 * 
 * @param x Grid x-coordinates
 * @param y Grid y-coordinates
 * @param z Function values at grid points (z[i,j] = f(x[i], y[j]))
 * @param x_new New x-coordinates
 * @param y_new New y-coordinates
 * @return Interpolated values at (x_new, y_new)
 */
template <typename Derived1, typename Derived2, typename Derived3, 
          typename Derived4, typename Derived5>
Eigen::MatrixXd
bilinear_interpolation(const Eigen::MatrixBase<Derived1>& x,
                      const Eigen::MatrixBase<Derived2>& y,
                      const Eigen::MatrixBase<Derived3>& z,
                      const Eigen::MatrixBase<Derived4>& x_new,
                      const Eigen::MatrixBase<Derived5>& y_new) {
    
    int nx = x.size();
    int ny = y.size();
    
    if (z.rows() != nx || z.cols() != ny) {
        throw std::invalid_argument("z must have size [nx, ny]");
    }
    
    int n_new = x_new.size();
    int m_new = y_new.size();
    
    Eigen::MatrixXd result(n_new, m_new);
    
    for (int i = 0; i < n_new; ++i) {
        double x_val = x_new(i);
        
        // Handle x extrapolation
        if (x_val <= x(0)) {
            x_val = x(0);
        } else if (x_val >= x(nx - 1)) {
            x_val = x(nx - 1);
        }
        
        // Find x interval
        int i_x = 0;
        while (i_x < nx - 1 && x(i_x + 1) < x_val) {
            ++i_x;
        }
        
        double x1 = x(i_x);
        double x2 = x(i_x + 1);
        
        for (int j = 0; j < m_new; ++j) {
            double y_val = y_new(j);
            
            // Handle y extrapolation
            if (y_val <= y(0)) {
                y_val = y(0);
            } else if (y_val >= y(ny - 1)) {
                y_val = y(ny - 1);
            }
            
            // Find y interval
            int i_y = 0;
            while (i_y < ny - 1 && y(i_y + 1) < y_val) {
                ++i_y;
            }
            
            double y1 = y(i_y);
            double y2 = y(i_y + 1);
            
            // Bilinear interpolation
            double t_x = (x_val - x1) / (x2 - x1);
            double t_y = (y_val - y1) / (y2 - y1);
            
            double z11 = z(i_x, i_y);
            double z12 = z(i_x, i_y + 1);
            double z21 = z(i_x + 1, i_y);
            double z22 = z(i_x + 1, i_y + 1);
            
            result(i, j) = (1.0 - t_x) * (1.0 - t_y) * z11 +
                          t_x * (1.0 - t_y) * z21 +
                          (1.0 - t_x) * t_y * z12 +
                          t_x * t_y * z22;
        }
    }
    
    return result;
}

/**
 * @brief Radial Basis Function interpolation
 * 
 * @param points Input data points (rows: points, cols: coordinates)
 * @param values Function values at input points
 * @param query_points Points at which to interpolate
 * @param kernel_type RBF kernel type ("gaussian", "linear", "cubic", "multiquadric", etc.)
 * @param epsilon Kernel parameter
 * @return Interpolated values
 */
inline Eigen::VectorXd
rbf_interpolation(const Eigen::MatrixXd& points,
                 const Eigen::VectorXd& values,
                 const Eigen::MatrixXd& query_points,
                 const std::string& kernel_type = "gaussian",
                 double epsilon = 1.0) {
    
    int n = points.rows();
    int m = query_points.rows();
    int d = points.cols();
    
    if (values.size() != n) {
        throw std::invalid_argument("Number of values must match number of points");
    }
    
    if (query_points.cols() != d) {
        throw std::invalid_argument("Query points must have same dimension as input points");
    }
    
    // Define RBF kernel function
    std::function<double(const Eigen::VectorXd&, const Eigen::VectorXd&)> kernel;
    
    if (kernel_type == "gaussian") {
        kernel = [epsilon](const Eigen::VectorXd& x, const Eigen::VectorXd& y) {
            return std::exp(-epsilon * (x - y).squaredNorm());
        };
    } else if (kernel_type == "linear") {
        kernel = [](const Eigen::VectorXd& x, const Eigen::VectorXd& y) {
            return (x - y).norm();
        };
    } else if (kernel_type == "cubic") {
        kernel = [](const Eigen::VectorXd& x, const Eigen::VectorXd& y) {
            double r = (x - y).norm();
            return r * r * r;
        };
    } else if (kernel_type == "multiquadric") {
        kernel = [epsilon](const Eigen::VectorXd& x, const Eigen::VectorXd& y) {
            return std::sqrt((x - y).squaredNorm() + epsilon * epsilon);
        };
    } else if (kernel_type == "inverse_multiquadric") {
        kernel = [epsilon](const Eigen::VectorXd& x, const Eigen::VectorXd& y) {
            return 1.0 / std::sqrt((x - y).squaredNorm() + epsilon * epsilon);
        };
    } else if (kernel_type == "thin_plate") {
        kernel = [](const Eigen::VectorXd& x, const Eigen::VectorXd& y) {
            double r = (x - y).norm();
            return r <= 0.0 ? 0.0 : r * r * std::log(r);
        };
    } else {
        throw std::invalid_argument("Unsupported kernel type");
    }
    
    // Compute interpolation matrix
    Eigen::MatrixXd interp_matrix(n, n);
    
    for (int i = 0; i < n; ++i) {
        for (int j = 0; j < n; ++j) {
            interp_matrix(i, j) = kernel(points.row(i), points.row(j));
        }
    }
    
    // Solve for weights
    Eigen::VectorXd weights = interp_matrix.partialPivLu().solve(values);
    
    // Compute interpolated values
    Eigen::VectorXd result(m);
    
    for (int i = 0; i < m; ++i) {
        result(i) = 0.0;
        
        for (int j = 0; j < n; ++j) {
            result(i) += weights(j) * kernel(query_points.row(i), points.row(j));
        }
    }
    
    return result;
}

/**
 * @brief Polynomial curve fitting (least squares)
 * 
 * @param x X-coordinates
 * @param y Y-coordinates
 * @param degree Polynomial degree
 * @param x_new Points at which to evaluate the fitted polynomial
 * @return Fitted values at x_new
 */
template <typename Derived1, typename Derived2, typename Derived3>
Eigen::VectorXd
polynomial_fit(const Eigen::MatrixBase<Derived1>& x,
              const Eigen::MatrixBase<Derived2>& y,
              int degree,
              const Eigen::MatrixBase<Derived3>& x_new) {
    
    if (x.size() != y.size()) {
        throw std::invalid_argument("Input vectors x and y must have the same size");
    }
    
    if (degree < 0) {
        throw std::invalid_argument("Polynomial degree must be non-negative");
    }
    
    int n = x.size();
    
    // Construct Vandermonde matrix
    Eigen::MatrixXd V(n, degree + 1);
    
    for (int i = 0; i < n; ++i) {
        V(i, 0) = 1.0;
        for (int j = 1; j <= degree; ++j) {
            V(i, j) = V(i, j - 1) * x(i);
        }
    }
    
    // Solve the least squares problem
    Eigen::VectorXd coeffs = V.householderQr().solve(y);
    
    // Evaluate the polynomial at the new points
    int m = x_new.size();
    Eigen::VectorXd result(m);
    
    for (int i = 0; i < m; ++i) {
        double x_val = x_new(i);
        double y_val = 0.0;
        double x_pow = 1.0;
        
        for (int j = 0; j <= degree; ++j) {
            y_val += coeffs(j) * x_pow;
            x_pow *= x_val;
        }
        
        result(i) = y_val;
    }
    
    return result;
}

/**
 * @brief Monotonic cubic interpolation that preserves monotonicity
 * 
 * @param x Known x-coordinates (must be strictly increasing)
 * @param y Known y-coordinates
 * @param x_new Points at which to interpolate
 * @return Interpolated values
 */
template <typename Derived1, typename Derived2, typename Derived3>
Eigen::VectorXd
pchip_interpolation(const Eigen::MatrixBase<Derived1>& x,
                   const Eigen::MatrixBase<Derived2>& y,
                   const Eigen::MatrixBase<Derived3>& x_new) {
    
    if (x.size() != y.size()) {
        throw std::invalid_argument("Input vectors x and y must have the same size");
    }
    
    int n = x.size();
    int m = x_new.size();
    
    if (n < 2) {
        throw std::invalid_argument("At least two points are required for interpolation");
    }
    
    // Check that x is strictly increasing
    for (int i = 0; i < n - 1; ++i) {
        if (x(i + 1) <= x(i)) {
            throw std::invalid_argument("x must be strictly increasing");
        }
    }
    
    // Compute slopes
    Eigen::VectorXd h(n - 1);
    Eigen::VectorXd delta(n - 1);
    
    for (int i = 0; i < n - 1; ++i) {
        h(i) = x(i + 1) - x(i);
        delta(i) = (y(i + 1) - y(i)) / h(i);
    }
    
    // Initialize derivatives
    Eigen::VectorXd d(n);
    
    // Set interior derivatives using harmonic mean
    for (int i = 1; i < n - 1; ++i) {
        if (delta(i - 1) * delta(i) > 0) {
            // Ensure monotonicity
            double w1 = 2 * h(i) + h(i - 1);
            double w2 = h(i) + 2 * h(i - 1);
            d(i) = (w1 + w2) / (w1 / delta(i - 1) + w2 / delta(i));
        } else {
            // Different sign or zero; use zero derivative
            d(i) = 0.0;
        }
    }
    
    // Set endpoint derivatives
    if (n > 2) {
        // Use non-centered three-point formula
        d(0) = ((2 * h(0) + h(1)) * delta(0) - h(0) * delta(1)) / (h(0) + h(1));
        d(n - 1) = ((2 * h(n - 2) + h(n - 3)) * delta(n - 2) - h(n - 2) * delta(n - 3)) / (h(n - 2) + h(n - 3));
        
        // Adjust if not monotonic
        if (d(0) * delta(0) <= 0) {
            d(0) = 0.0;
        } else if (delta(0) * d(0) > 3 * delta(0) * delta(0)) {
            d(0) = 3 * delta(0);
        }
        
        if (d(n - 1) * delta(n - 2) <= 0) {
            d(n - 1) = 0.0;
        } else if (delta(n - 2) * d(n - 1) > 3 * delta(n - 2) * delta(n - 2)) {
            d(n - 1) = 3 * delta(n - 2);
        }
    } else {
        // Linear interpolation for n = 2
        d(0) = delta(0);
        d(n - 1) = delta(0);
    }
    
    // Compute polynomial coefficients
    Eigen::VectorXd a(n - 1);
    Eigen::VectorXd b(n - 1);
    Eigen::VectorXd c(n - 1);
    
    for (int i = 0; i < n - 1; ++i) {
        a(i) = (d(i) + d(i + 1) - 2 * delta(i)) / (h(i) * h(i));
        b(i) = (3 * delta(i) - 2 * d(i) - d(i + 1)) / h(i);
        c(i) = d(i);
    }
    
    // Perform interpolation
    Eigen::VectorXd result(m);
    
    for (int i = 0; i < m; ++i) {
        double x_val = x_new(i);
        
        // Handle extrapolation
        if (x_val <= x(0)) {
            result(i) = y(0);
            continue;
        } else if (x_val >= x(n - 1)) {
            result(i) = y(n - 1);
            continue;
        }
        
        // Find the interval [x(j), x(j+1)] containing x_val
        int j = 0;
        while (j < n - 1 && x(j + 1) < x_val) {
            ++j;
        }
        
        // Compute the interpolated value
        double dx = x_val - x(j);
        result(i) = y(j) + (c(j) + (b(j) + a(j) * dx) * dx) * dx;
    }
    
    return result;
}

} // namespace interpolation

/**
 * @brief Numerical integration methods
 */
namespace integration {

/**
 * @brief Trapezoidal rule for numerical integration
 * 
 * @param f Function to integrate
 * @param a Lower bound
 * @param b Upper bound
 * @param n Number of intervals
 * @return Approximation of the integral
 */
template <typename Func>
double trapezoidal_rule(const Func& f, double a, double b, int n = 100) {
    if (n <= 0) {
        throw std::invalid_argument("Number of intervals must be positive");
    }
    
    if (a > b) {
        return -trapezoidal_rule(f, b, a, n);
    }
    
    double h = (b - a) / n;
    double result = 0.5 * (f(a) + f(b));
    
    for (int i = 1; i < n; ++i) {
        result += f(a + i * h);
    }
    
    return result * h;
}

/**
 * @brief Simpson's rule for numerical integration
 * 
 * @param f Function to integrate
 * @param a Lower bound
 * @param b Upper bound
 * @param n Number of intervals (must be even)
 * @return Approximation of the integral
 */
template <typename Func>
double simpsons_rule(const Func& f, double a, double b, int n = 100) {
    if (n <= 0 || n % 2 != 0) {
        // Ensure n is positive and even
        n = (n <= 0) ? 100 : (n + n % 2);
    }
    
    if (a > b) {
        return -simpsons_rule(f, b, a, n);
    }
    
    double h = (b - a) / n;
    double result = f(a) + f(b);
    
    for (int i = 1; i < n; i += 2) {
        result += 4 * f(a + i * h);
    }
    
    for (int i = 2; i < n - 1; i += 2) {
        result += 2 * f(a + i * h);
    }
    
    return result * h / 3;
}

/**
 * @brief Adaptive Simpson's rule for numerical integration
 * 
 * @param f Function to integrate
 * @param a Lower bound
 * @param b Upper bound
 * @param epsilon Error tolerance
 * @param max_depth Maximum recursion depth
 * @return Approximation of the integral
 */
template <typename Func>
double adaptive_simpsons_rule(const Func& f, double a, double b, 
                            double epsilon = 1e-8, int max_depth = 20) {
    
    // Helper function for recursive integration
    std::function<double(const Func&, double, double, double, double, double, double, double, int)> 
    adaptive_simpson_recursive = 
        [&adaptive_simpson_recursive, epsilon, max_depth]
        (const Func& f, double a, double b, double fa, double fb, double fc, double integral, 
         double error_est, int depth) {
            
            double c = (a + b) / 2;
            double h = (b - a) / 4;
            double d = (a + c) / 2;
            double e = (c + b) / 2;
            double fd = f(d);
            double fe = f(e);
            
            double left_integral = (fa + 4 * fd + fc) * h / 3;
            double right_integral = (fc + 4 * fe + fb) * h / 3;
            double whole_integral = left_integral + right_integral;
            
            double error = std::abs(whole_integral - integral) / 15;
            
            if (error < epsilon || depth >= max_depth) {
                return whole_integral + (whole_integral - integral) / 15;
            } else {
                double left_result = adaptive_simpson_recursive(f, a, c, fa, fc, fd, left_integral, 
                                                             error/2, depth + 1);
                double right_result = adaptive_simpson_recursive(f, c, b, fc, fb, fe, right_integral, 
                                                              error/2, depth + 1);
                return left_result + right_result;
            }
        };
    
    if (a > b) {
        return -adaptive_simpsons_rule(f, b, a, epsilon, max_depth);
    }
    
    double c = (a + b) / 2;
    double h = (b - a) / 4;
    double fa = f(a);
    double fb = f(b);
    double fc = f(c);
    
    double integral = (fa + 4 * fc + fb) * (b - a) / 6;
    
    return adaptive_simpson_recursive(f, a, b, fa, fb, fc, integral, 
                                    std::numeric_limits<double>::max(), 0);
}

/**
 * @brief Gauss-Legendre quadrature for numerical integration
 * 
 * @param f Function to integrate
 * @param a Lower bound
 * @param b Upper bound
 * @param n Number of points
 * @return Approximation of the integral
 */
template <typename Func>
double gauss_legendre(const Func& f, double a, double b, int n = 5) {
    if (n <= 0) {
        throw std::invalid_argument("Number of points must be positive");
    }
    
    if (a > b) {
        return -gauss_legendre(f, b, a, n);
    }
    
    // Precomputed abscissas and weights for Gauss-Legendre quadrature
    std::vector<std::vector<double>> abscissas = {
        {0.0},
        {-0.5773502691896257, 0.5773502691896257},
        {-0.7745966692414834, 0.0, 0.7745966692414834},
        {-0.8611363115940526, -0.3399810435848563, 0.3399810435848563, 0.8611363115940526},
        {-0.9061798459386640, -0.5384693101056831, 0.0, 0.5384693101056831, 0.9061798459386640}
    };
    
    std::vector<std::vector<double>> weights = {
        {2.0},
        {1.0, 1.0},
        {0.5555555555555556, 0.8888888888888888, 0.5555555555555556},
        {0.3478548451374538, 0.6521451548625461, 0.6521451548625461, 0.3478548451374538},
        {0.2369268850561891, 0.4786286704993665, 0.5688888888888889, 0.4786286704993665, 0.2369268850561891}
    };
    
    if (n > 5) {
        // For n > 5, use a simple trapezoidal rule
        return trapezoidal_rule(f, a, b, n);
    }
    
    double result = 0.0;
    double mid = (a + b) / 2;
    double half_length = (b - a) / 2;
    
    for (int i = 0; i < n; ++i) {
        double x = mid + half_length * abscissas[n-1][i];
        double weight = weights[n-1][i];
        result += weight * f(x);
    }
    
    return result * half_length;
}

/**
 * @brief Monte Carlo integration for multidimensional integrals
 * 
 * @param f Function to integrate
 * @param lower_bounds Lower bounds for each dimension
 * @param upper_bounds Upper bounds for each dimension
 * @param n_samples Number of random samples
 * @param seed Random seed
 * @return Approximation of the integral and estimated error
 */
template <typename Func>
std::pair<double, double>
monte_carlo_integration(const Func& f,
                       const Eigen::VectorXd& lower_bounds,
                       const Eigen::VectorXd& upper_bounds,
                       int n_samples = 10000,
                       unsigned int seed = 0) {
    
    int d = lower_bounds.size();
    
    if (d != upper_bounds.size()) {
        throw std::invalid_argument("Lower and upper bounds must have the same dimension");
    }
    
    if (n_samples <= 0) {
        throw std::invalid_argument("Number of samples must be positive");
    }
    
    // Initialize random number generator
    std::mt19937 gen(seed == 0 ? std::random_device{}() : seed);
    std::vector<std::uniform_real_distribution<double>> dists;
    
    for (int i = 0; i < d; ++i) {
        if (lower_bounds(i) >= upper_bounds(i)) {
            throw std::invalid_argument("Lower bounds must be less than upper bounds");
        }
        
        dists.emplace_back(lower_bounds(i), upper_bounds(i));
    }
    
    // Compute volume of integration region
    double volume = 1.0;
    for (int i = 0; i < d; ++i) {
        volume *= (upper_bounds(i) - lower_bounds(i));
    }
    
    // Perform Monte Carlo integration
    double sum = 0.0;
    double sum_squared = 0.0;
    
    Eigen::VectorXd x(d);
    
    for (int i = 0; i < n_samples; ++i) {
        for (int j = 0; j < d; ++j) {
            x(j) = dists[j](gen);
        }
        
        double fx = f(x);
        sum += fx;
        sum_squared += fx * fx;
    }
    
    // Compute result and error estimate
    double result = volume * sum / n_samples;
    double variance = volume * volume * (sum_squared / n_samples - (sum / n_samples) * (sum / n_samples)) / n_samples;
    double error = std::sqrt(variance);
    
    return {result, error};
}

/**
 * @brief Romberg integration for high-precision numerical integration
 * 
 * @param f Function to integrate
 * @param a Lower bound
 * @param b Upper bound
 * @param max_iter Maximum number of iterations
 * @param epsilon Error tolerance
 * @return Approximation of the integral
 */
template <typename Func>
double romberg_integration(const Func& f, double a, double b, 
                         int max_iter = 10, double epsilon = 1e-10) {
    
    if (max_iter <= 0) {
        throw std::invalid_argument("Maximum iterations must be positive");
    }
    
    if (a > b) {
        return -romberg_integration(f, b, a, max_iter, epsilon);
    }
    
    // Initialize Romberg table
    std::vector<std::vector<double>> R(max_iter, std::vector<double>(max_iter, 0.0));
    
    // Initial trapezoidal approximation
    R[0][0] = 0.5 * (b - a) * (f(a) + f(b));
    
    for (int i = 1; i < max_iter; ++i) {
        // Compute next row of Romberg table
        
        // First, compute the trapezoidal approximation with 2^i intervals
        double h = (b - a) / std::pow(2, i);
        double sum = 0.0;
        
        for (int k = 1; k < std::pow(2, i); k += 2) {
            sum += f(a + k * h);
        }
        
        R[i][0] = 0.5 * R[i-1][0] + h * sum;
        
        // Compute higher-order approximations using Richardson extrapolation
        for (int j = 1; j <= i; ++j) {
            R[i][j] = R[i][j-1] + (R[i][j-1] - R[i-1][j-1]) / (std::pow(4, j) - 1);
        }
        
        // Check for convergence
        if (i > 0 && std::abs(R[i][i] - R[i-1][i-1]) < epsilon) {
            return R[i][i];
        }
    }
    
    return R[max_iter-1][max_iter-1];
}

/**
 * @brief Numerical integration using Runge-Kutta methods for ordinary differential equations
 * 
 * @param f Function defining the ODE: y' = f(t, y)
 * @param y0 Initial condition
 * @param t0 Initial time
 * @param tf Final time
 * @param h Step size
 * @param method RK method order (1 = Euler, 2 = Midpoint, 4 = RK4)
 * @return Pair of time points and solution values
 */
template <typename Func>
std::pair<Eigen::VectorXd, Eigen::MatrixXd>
ode_integration(const Func& f, const Eigen::VectorXd& y0, 
               double t0, double tf, double h = 0.01, int method = 4) {
    
    if (h <= 0) {
        throw std::invalid_argument("Step size must be positive");
    }
    
    if (t0 > tf) {
        return ode_integration(f, y0, tf, t0, h, method);
    }
    
    int n = static_cast<int>((tf - t0) / h) + 1;
    double h_actual = (tf - t0) / (n - 1);
    
    int dim = y0.size();
    
    Eigen::VectorXd t = Eigen::VectorXd::LinSpaced(n, t0, tf);
    Eigen::MatrixXd y(n, dim);
    
    y.row(0) = y0;
    
    for (int i = 0; i < n - 1; ++i) {
        double ti = t(i);
        Eigen::VectorXd yi = y.row(i);
        
        if (method == 1) {
            // Euler method
            y.row(i + 1) = yi + h_actual * f(ti, yi);
        } else if (method == 2) {
            // Midpoint method
            Eigen::VectorXd k1 = f(ti, yi);
            Eigen::VectorXd k2 = f(ti + 0.5 * h_actual, yi + 0.5 * h_actual * k1);
            
            y.row(i + 1) = yi + h_actual * k2;
        } else if (method == 4) {
            // RK4 method
            Eigen::VectorXd k1 = f(ti, yi);
            Eigen::VectorXd k2 = f(ti + 0.5 * h_actual, yi + 0.5 * h_actual * k1);
            Eigen::VectorXd k3 = f(ti + 0.5 * h_actual, yi + 0.5 * h_actual * k2);
            Eigen::VectorXd k4 = f(ti + h_actual, yi + h_actual * k3);
            
            y.row(i + 1) = yi + h_actual * (k1 + 2.0 * k2 + 2.0 * k3 + k4) / 6.0;
        } else {
            throw std::invalid_argument("Unsupported method order");
        }
    }
    
    return {t, y};
}

} // namespace integration

/**
 * @brief Graph theory and network science
 */
namespace graph {

/**
 * @brief Graph structure for representing networks
 */
class Graph {
public:
    /**
     * @brief Constructor for empty graph
     * 
     * @param directed Whether the graph is directed
     * @param weighted Whether the graph has edge weights
     */
    Graph(bool directed = false, bool weighted = false)
        : directed_(directed), weighted_(weighted) {}
    
    /**
     * @brief Constructor from adjacency matrix
     * 
     * @param adj_matrix Adjacency matrix
     * @param directed Whether the graph is directed
     * @param weighted Whether the graph has edge weights
     */
    Graph(const Eigen::MatrixXd& adj_matrix, bool directed = false, bool weighted = false)
        : directed_(directed), weighted_(weighted) {
        
        int n = adj_matrix.rows();
        
        if (adj_matrix.cols() != n) {
            throw std::invalid_argument("Adjacency matrix must be square");
        }
        
        for (int i = 0; i < n; ++i) {
            add_vertex(i);
        }
        
        for (int i = 0; i < n; ++i) {
            for (int j = 0; j < n; ++j) {
                if (adj_matrix(i, j) != 0) {
                    if (weighted) {
                        add_edge(i, j, adj_matrix(i, j));
                    } else {
                        add_edge(i, j);
                    }
                }
            }
        }
    }
    
    /**
     * @brief Add a vertex to the graph
     * 
     * @param id Vertex ID
     * @param properties Optional properties map
     * @return Whether the vertex was added
     */
    bool add_vertex(int id, const std::map<std::string, double>& properties = {}) {
        if (vertices_.find(id) != vertices_.end()) {
            return false;
        }
        
        vertices_[id] = properties;
        adjacency_list_[id] = {};
        
        return true;
    }
    
    /**
     * @brief Add an edge to the graph
     * 
     * @param source Source vertex ID
     * @param target Target vertex ID
     * @param weight Edge weight (default: 1.0)
     * @param properties Optional properties map
     * @return Whether the edge was added
     */
    bool add_edge(int source, int target, double weight = 1.0, 
                 const std::map<std::string, double>& properties = {}) {
        
        if (vertices_.find(source) == vertices_.end() || 
            vertices_.find(target) == vertices_.end()) {
            return false;
        }
        
        // Check if edge already exists
        for (const auto& edge : adjacency_list_[source]) {
            if (edge.target == target) {
                return false;
            }
        }
        
        // Add edge
        Edge edge;
        edge.source = source;
        edge.target = target;
        edge.weight = weight;
        edge.properties = properties;
        
        adjacency_list_[source].push_back(edge);
        
        if (!directed_) {
            Edge reverse_edge;
            reverse_edge.source = target;
            reverse_edge.target = source;
            reverse_edge.weight = weight;
            reverse_edge.properties = properties;
            
            adjacency_list_[target].push_back(reverse_edge);
        }
        
        return true;
    }
    
    /**
     * @brief Remove a vertex and all its incident edges
     * 
     * @param id Vertex ID
     * @return Whether the vertex was removed
     */
    bool remove_vertex(int id) {
        if (vertices_.find(id) == vertices_.end()) {
            return false;
        }
        
        // Remove edges incident to the vertex
        for (auto& [vid, edges] : adjacency_list_) {
            edges.erase(
                std::remove_if(edges.begin(), edges.end(), 
                              [id](const Edge& e) { return e.target == id; }),
                edges.end());
        }
        
        // Remove the vertex
        vertices_.erase(id);
        adjacency_list_.erase(id);
        
        return true;
    }
    
    /**
     * @brief Remove an edge from the graph
     * 
     * @param source Source vertex ID
     * @param target Target vertex ID
     * @return Whether the edge was removed
     */
    bool remove_edge(int source, int target) {
        if (vertices_.find(source) == vertices_.end() || 
            vertices_.find(target) == vertices_.end()) {
            return false;
        }
        
        // Remove the edge
        auto& edges = adjacency_list_[source];
        size_t initial_size = edges.size();
        
        edges.erase(
            std::remove_if(edges.begin(), edges.end(), 
                          [target](const Edge& e) { return e.target == target; }),
            edges.end());
        
        bool removed = (edges.size() < initial_size);
        
        if (!directed_ && removed) {
            auto& reverse_edges = adjacency_list_[target];
            reverse_edges.erase(
                std::remove_if(reverse_edges.begin(), reverse_edges.end(), 
                              [source](const Edge& e) { return e.target == source; }),
                reverse_edges.end());
        }
        
        return removed;
    }
    
    /**
     * @brief Get all vertices in the graph
     * 
     * @return Vector of vertex IDs
     */
    std::vector<int> get_vertices() const {
        std::vector<int> result;
        for (const auto& [id, _] : vertices_) {
            result.push_back(id);
        }
        return result;
    }
    
    /**
     * @brief Get all edges in the graph
     * 
     * @return Vector of edges
     */
    std::vector<Edge> get_edges() const {
        std::vector<Edge> result;
        
        for (const auto& [source, edges] : adjacency_list_) {
            for (const auto& edge : edges) {
                if (directed_ || edge.source < edge.target) {
                    result.push_back(edge);
                }
            }
        }
        
        return result;
    }
    
    /**
     * @brief Get neighbors of a vertex
     * 
     * @param id Vertex ID
     * @return Vector of neighbor vertex IDs
     */
    std::vector<int> get_neighbors(int id) const {
        std::vector<int> result;
        
        auto it = adjacency_list_.find(id);
        if (it != adjacency_list_.end()) {
            for (const auto& edge : it->second) {
                result.push_back(edge.target);
            }
        }
        
        return result;
    }
    
    /**
     * @brief Get edges incident to a vertex
     * 
     * @param id Vertex ID
     * @return Vector of incident edges
     */
    std::vector<Edge> get_incident_edges(int id) const {
        std::vector<Edge> result;
        
        auto it = adjacency_list_.find(id);
        if (it != adjacency_list_.end()) {
            result = it->second;
        }
        
        return result;
    }
    
    /**
     * @brief Get the adjacency matrix of the graph
     * 
     * @return Adjacency matrix
     */
    Eigen::MatrixXd get_adjacency_matrix() const {
        int n = vertices_.size();
        
        // Create a mapping from vertex IDs to matrix indices
        std::map<int, int> id_to_index;
        int index = 0;
        
        for (const auto& [id, _] : vertices_) {
            id_to_index[id] = index++;
        }
        
        // Build the adjacency matrix
        Eigen::MatrixXd adj_matrix = Eigen::MatrixXd::Zero(n, n);
        
        for (const auto& [source, edges] : adjacency_list_) {
            for (const auto& edge : edges) {
                int i = id_to_index[source];
                int j = id_to_index[edge.target];
                
                if (weighted_) {
                    adj_matrix(i, j) = edge.weight;
                } else {
                    adj_matrix(i, j) = 1.0;
                }
            }
        }
        
        return adj_matrix;
    }
    
    /**
     * @brief Compute the Laplacian matrix of the graph
     * 
     * @return Laplacian matrix
     */
    Eigen::MatrixXd get_laplacian_matrix() const {
        Eigen::MatrixXd adj_matrix = get_adjacency_matrix();
        int n = adj_matrix.rows();
        
        // Compute degree matrix
        Eigen::VectorXd degrees = adj_matrix.rowwise().sum();
        Eigen::MatrixXd degree_matrix = Eigen::MatrixXd::Zero(n, n);
        
        for (int i = 0; i < n; ++i) {
            degree_matrix(i, i) = degrees(i);
        }
        
        // Compute Laplacian matrix
        return degree_matrix - adj_matrix;
    }
    
    /**
     * @brief Compute the normalized Laplacian matrix of the graph
     * 
     * @return Normalized Laplacian matrix
     */
    Eigen::MatrixXd get_normalized_laplacian() const {
        Eigen::MatrixXd adj_matrix = get_adjacency_matrix();
        int n = adj_matrix.rows();
        
        // Compute degree matrix
        Eigen::VectorXd degrees = adj_matrix.rowwise().sum();
        Eigen::MatrixXd degree_matrix_inv_sqrt = Eigen::MatrixXd::Zero(n, n);
        
        for (int i = 0; i < n; ++i) {
            if (degrees(i) > 0) {
                degree_matrix_inv_sqrt(i, i) = 1.0 / std::sqrt(degrees(i));
            }
        }
        
        // Compute normalized Laplacian
        Eigen::MatrixXd I = Eigen::MatrixXd::Identity(n, n);
        return I - degree_matrix_inv_sqrt * adj_matrix * degree_matrix_inv_sqrt;
    }
    
    /**
     * @brief Compute shortest paths from source to all other vertices
     * 
     * @param source Source vertex ID
     * @return Pair of (distances, predecessors)
     */
    std::pair<std::map<int, double>, std::map<int, int>>
    shortest_paths(int source) const {
        std::map<int, double> distances;
        std::map<int, int> predecessors;
        
        // Initialize distances and predecessors
        for (const auto& [id, _] : vertices_) {
            distances[id] = std::numeric_limits<double>::infinity();
            predecessors[id] = -1;
        }
        
        distances[source] = 0.0;
        
        // Implement Dijkstra's algorithm
        std::set<std::pair<double, int>> queue;
        queue.insert({0.0, source});
        
        while (!queue.empty()) {
            int u = queue.begin()->second;
            queue.erase(queue.begin());
            
            for (const auto& edge : adjacency_list_.at(u)) {
                int v = edge.target;
                double weight = weighted_ ? edge.weight : 1.0;
                
                if (distances[u] + weight < distances[v]) {
                    queue.erase({distances[v], v});
                    distances[v] = distances[u] + weight;
                    predecessors[v] = u;
                    queue.insert({distances[v], v});
                }
            }
        }
        
        return {distances, predecessors};
    }
    
    /**
     * @brief Compute the shortest path between two vertices
     * 
     * @param source Source vertex ID
     * @param target Target vertex ID
     * @return Pair of (distance, path)
     */
    std::pair<double, std::vector<int>>
    shortest_path(int source, int target) const {
        auto [distances, predecessors] = shortest_paths(source);
        
        if (distances[target] == std::numeric_limits<double>::infinity()) {
            return {std::numeric_limits<double>::infinity(), {}};
        }
        
        std::vector<int> path;
        int current = target;
        
        while (current != -1) {
            path.push_back(current);
            current = predecessors[current];
        }
        
        std::reverse(path.begin(), path.end());
        
        return {distances[target], path};
    }
    
    /**
     * @brief Compute connected components of the graph
     * 
     * @return Vector of component membership
     */
    std::vector<int> connected_components() const {
        std::vector<int> vertex_ids;
        for (const auto& [id, _] : vertices_) {
            vertex_ids.push_back(id);
        }
        
        std::sort(vertex_ids.begin(), vertex_ids.end());
        
        std::vector<int> component(vertex_ids.size(), -1);
        std::map<int, int> id_to_index;
        
        for (int i = 0; i < vertex_ids.size(); ++i) {
            id_to_index[vertex_ids[i]] = i;
        }
        
        int comp_id = 0;
        
        for (int i = 0; i < vertex_ids.size(); ++i) {
            if (component[i] == -1) {
                // Start a new component
                std::queue<int> queue;
                queue.push(vertex_ids[i]);
                component[i] = comp_id;
                
                while (!queue.empty()) {
                    int u = queue.front();
                    queue.pop();
                    
                    for (int v : get_neighbors(u)) {
                        int v_idx = id_to_index[v];
                        if (component[v_idx] == -1) {
                            component[v_idx] = comp_id;
                            queue.push(v);
                        }
                    }
                }
                
                ++comp_id;
            }
        }
        
        return component;
    }
    
    /**
     * @brief Compute the degree of a vertex
     * 
     * @param id Vertex ID
     * @return Degree of the vertex
     */
    int degree(int id) const {
        auto it = adjacency_list_.find(id);
        if (it != adjacency_list_.end()) {
            return it->second.size();
        }
        return 0;
    }
    
    /**
     * @brief Compute the in-degree of a vertex (for directed graphs)
     * 
     * @param id Vertex ID
     * @return In-degree of the vertex
     */
    int in_degree(int id) const {
        if (!directed_) {
            return degree(id);
        }
        
        int count = 0;
        for (const auto& [source, edges] : adjacency_list_) {
            for (const auto& edge : edges) {
                if (edge.target == id) {
                    ++count;
                }
            }
        }
        
        return count;
    }
    
    /**
     * @brief Compute the out-degree of a vertex (for directed graphs)
     * 
     * @param id Vertex ID
     * @return Out-degree of the vertex
     */
    int out_degree(int id) const {
        if (!directed_) {
            return degree(id);
        }
        
        auto it = adjacency_list_.find(id);
        if (it != adjacency_list_.end()) {
            return it->second.size();
        }
        
        return 0;
    }
    
    /**
     * @brief Compute the clustering coefficient of a vertex
     * 
     * @param id Vertex ID
     * @return Clustering coefficient
     */
    double clustering_coefficient(int id) const {
        auto neighbors = get_neighbors(id);
        int n = neighbors.size();
        
        if (n < 2) {
            return 0.0;
        }
        
        int edge_count = 0;
        
        for (int i = 0; i < n; ++i) {
            for (int j = i + 1; j < n; ++j) {
                for (const auto& edge : adjacency_list_.at(neighbors[i])) {
                    if (edge.target == neighbors[j]) {
                        ++edge_count;
                        break;
                    }
                }
            }
        }
        
        return 2.0 * edge_count / (n * (n - 1));
    }
    
    /**
     * @brief Compute the average clustering coefficient of the graph
     * 
     * @return Average clustering coefficient
     */
    double average_clustering_coefficient() const {
        double sum = 0.0;
        int count = 0;
        
        for (const auto& [id, _] : vertices_) {
            sum += clustering_coefficient(id);
            ++count;
        }
        
        return count > 0 ? sum / count : 0.0;
    }
    
    /**
     * @brief Compute the global efficiency of the graph
     * 
     * @return Global efficiency
     */
    double global_efficiency() const {
        int n = vertices_.size();
        
        if (n <= 1) {
            return 0.0;
        }
        
        double sum = 0.0;
        
        for (const auto& [source, _] : vertices_) {
            auto [distances, _] = shortest_paths(source);
            
            for (const auto& [target, dist] : distances) {
                if (source != target) {
                    if (dist != std::numeric_limits<double>::infinity()) {
                        sum += 1.0 / dist;
                    }
                }
            }
        }
        
        return sum / (n * (n - 1));
    }
    
    /**
     * @brief Compute the local efficiency of a vertex
     * 
     * @param id Vertex ID
     * @return Local efficiency
     */
    double local_efficiency(int id) const {
        auto neighbors = get_neighbors(id);
        int n = neighbors.size();
        
        if (n <= 1) {
            return 0.0;
        }
        
        // Create a subgraph of the neighbors
        Graph subgraph(directed_, weighted_);
        
        for (int v : neighbors) {
            subgraph.add_vertex(v);
        }
        
        for (int v1 : neighbors) {
            for (int v2 : neighbors) {
                if (v1 != v2) {
                    for (const auto& edge : get_incident_edges(v1)) {
                        if (edge.target == v2) {
                            subgraph.add_edge(v1, v2, edge.weight);
                            break;
                        }
                    }
                }
            }
        }
        
        return subgraph.global_efficiency();
    }
    
    /**
     * @brief Compute the average local efficiency of the graph
     * 
     * @return Average local efficiency
     */
    double average_local_efficiency() const {
        double sum = 0.0;
        int count = 0;
        
        for (const auto& [id, _] : vertices_) {
            sum += local_efficiency(id);
            ++count;
        }
        
        return count > 0 ? sum / count : 0.0;
    }
    
    /**
     * @brief Compute betweenness centrality for all vertices
     * 
     * @return Map of vertex ID to betweenness centrality
     */
    std::map<int, double> betweenness_centrality() const {
        std::map<int, double> result;
        std::vector<int> vertices = get_vertices();
        
        for (int v : vertices) {
            result[v] = 0.0;
        }
        
        for (int s : vertices) {
            // Single-source shortest paths
            std::map<int, std::vector<int>> predecessors;
            std::map<int, double> distances;
            std::map<int, int> num_paths;
            
            for (int v : vertices) {
                predecessors[v] = {};
                distances[v] = std::numeric_limits<double>::infinity();
                num_paths[v] = 0;
            }
            
            distances[s] = 0.0;
            num_paths[s] = 1;
            
            std::queue<int> queue;
            queue.push(s);
            
            while (!queue.empty()) {
                int v = queue.front();
                queue.pop();
                
                for (const auto& edge : adjacency_list_.at(v)) {
                    int w = edge.target;
                    double weight = weighted_ ? edge.weight : 1.0;
                    
                    // Path discovery
                    if (distances[w] == std::numeric_limits<double>::infinity()) {
                        distances[w] = distances[v] + weight;
                        queue.push(w);
                    }
                    
                    // Path counting
                    if (distances[w] == distances[v] + weight) {
                        num_paths[w] += num_paths[v];
                        predecessors[w].push_back(v);
                    }
                }
            }
            
            // Dependency accumulation
            std::map<int, double> dependency;
            for (int v : vertices) {
                dependency[v] = 0.0;
            }
            
            std::vector<int> stack;
            for (int v : vertices) {
                if (v != s) {
                    stack.push_back(v);
                }
            }
            
            std::sort(stack.begin(), stack.end(), 
                     [&distances](int a, int b) { return distances[a] > distances[b]; });
            
            for (int w : stack) {
                for (int v : predecessors[w]) {
                    dependency[v] += (num_paths[v] * 1.0 / num_paths[w]) * (1.0 + dependency[w]);
                }
                
                if (w != s) {
                    result[w] += dependency[w];
                }
            }
        }
        
        int n = vertices.size();
        double normalization = (n - 1) * (n - 2);
        
        if (normalization > 0) {
            for (auto& [id, centrality] : result) {
                centrality /= normalization;
            }
        }
        
        return result;
    }
    
    /**
     * @brief Compute closeness centrality for all vertices
     * 
     * @return Map of vertex ID to closeness centrality
     */
    std::map<int, double> closeness_centrality() const {
        std::map<int, double> result;
        
        for (const auto& [id, _] : vertices_) {
            auto [distances, _] = shortest_paths(id);
            
            double sum = 0.0;
            int reachable = 0;
            
            for (const auto& [v, dist] : distances) {
                if (v != id && dist != std::numeric_limits<double>::infinity()) {
                    sum += dist;
                    ++reachable;
                }
            }
            
            int n = vertices_.size() - 1;
            
            if (reachable > 0 && n > 0) {
                result[id] = (reachable * reachable) / (n * sum);
            } else {
                result[id] = 0.0;
            }
        }
        
        return result;
    }
    
    /**
     * @brief Compute eigenvector centrality for all vertices
     * 
     * @param max_iter Maximum number of iterations
     * @param tolerance Convergence tolerance
     * @return Map of vertex ID to eigenvector centrality
     */
    std::map<int, double> eigenvector_centrality(int max_iter = 100, double tolerance = 1e-6) const {
        std::map<int, double> result;
        std::vector<int> vertices = get_vertices();
        int n = vertices.size();
        
        if (n == 0) {
            return result;
        }
        
        // Create a mapping from vertex IDs to indices
        std::map<int, int> id_to_index;
        for (int i = 0; i < n; ++i) {
            id_to_index[vertices[i]] = i;
        }
        
        Eigen::MatrixXd adj_matrix = get_adjacency_matrix();
        
        // Power iteration method
        Eigen::VectorXd x = Eigen::VectorXd::Ones(n);
        Eigen::VectorXd x_prev;
        
        for (int iter = 0; iter < max_iter; ++iter) {
            x_prev = x;
            
            // Update eigenvector
            x = adj_matrix * x;
            
            // Normalize
            double norm = x.norm();
            if (norm > 0) {
                x /= norm;
            }
            
            // Check convergence
            if ((x - x_prev).norm() < tolerance) {
                break;
            }
        }
        
        // Map back to vertex IDs
        for (int i = 0; i < n; ++i) {
            result[vertices[i]] = x(i);
        }
        
        return result;
    }
    
    /**
     * @brief Compute PageRank for all vertices
     * 
     * @param alpha Damping factor
     * @param max_iter Maximum number of iterations
     * @param tolerance Convergence tolerance
     * @return Map of vertex ID to PageRank
     */
    std::map<int, double> pagerank(double alpha = 0.85, int max_iter = 100, double tolerance = 1e-6) const {
        std::map<int, double> result;
        std::vector<int> vertices = get_vertices();
        int n = vertices.size();
        
        if (n == 0) {
            return result;
        }
        
        // Initialize PageRank
        for (int v : vertices) {
            result[v] = 1.0 / n;
        }
        
        for (int iter = 0; iter < max_iter; ++iter) {
            std::map<int, double> next_rank;
            double dangling_sum = 0.0;
            
            // Initialize next ranks
            for (int v : vertices) {
                next_rank[v] = 0.0;
                
                // Check for dangling nodes
                if (degree(v) == 0) {
                    dangling_sum += result[v];
                }
            }
            
            // Distribute dangling node PageRank
            double dangling_contribution = alpha * dangling_sum / n;
            
            for (int v : vertices) {
                next_rank[v] += dangling_contribution;
            }
            
            // Update PageRank
            for (int u : vertices) {
                for (const auto& edge : adjacency_list_.at(u)) {
                    int v = edge.target;
                    
                    if (weighted_) {
                        double weight = edge.weight;
                        double total_weight = 0.0;
                        
                        for (const auto& e : adjacency_list_.at(u)) {
                            total_weight += e.weight;
                        }
                        
                        if (total_weight > 0) {
                            next_rank[v] += alpha * result[u] * weight / total_weight;
                        }
                    } else {
                        int deg = degree(u);
                        if (deg > 0) {
                            next_rank[v] += alpha * result[u] / deg;
                        }
                    }
                }
            }
            
            // Add teleportation
            double teleport = (1.0 - alpha) / n;
            
            for (int v : vertices) {
                next_rank[v] += teleport;
            }
            
            // Check convergence
            double diff = 0.0;
            for (int v : vertices) {
                diff += std::abs(next_rank[v] - result[v]);
            }
            
            result = next_rank;
            
            if (diff < tolerance) {
                break;
            }
        }
        
        return result;
    }
    
    /**
     * @brief Compute HITS algorithm (Hyperlink-Induced Topic Search)
     * 
     * @param max_iter Maximum number of iterations
     * @param tolerance Convergence tolerance
     * @return Pair of (authority scores, hub scores)
     */
    std::pair<std::map<int, double>, std::map<int, double>>
    hits(int max_iter = 100, double tolerance = 1e-6) const {
        std::map<int, double> authority_scores;
        std::map<int, double> hub_scores;
        std::vector<int> vertices = get_vertices();
        
        // Initialize scores
        for (int v : vertices) {
            authority_scores[v] = 1.0;
            hub_scores[v] = 1.0;
        }
        
        for (int iter = 0; iter < max_iter; ++iter) {
            // Update authority scores
            std::map<int, double> new_authority_scores;
            for (int v : vertices) {
                new_authority_scores[v] = 0.0;
                
                for (const auto& [u, edges] : adjacency_list_) {
                    for (const auto& edge : edges) {
                        if (edge.target == v) {
                            new_authority_scores[v] += hub_scores[u];
                        }
                    }
                }
            }
            
            // Normalize authority scores
            double auth_norm = 0.0;
            for (const auto& [_, score] : new_authority_scores) {
                auth_norm += score * score;
            }
            
            auth_norm = std::sqrt(auth_norm);
            
            if (auth_norm > 0) {
                for (auto& [v, score] : new_authority_scores) {
                    score /= auth_norm;
                }
            }
            
            // Update hub scores
            std::map<int, double> new_hub_scores;
            for (int u : vertices) {
                new_hub_scores[u] = 0.0;
                
                for (const auto& edge : adjacency_list_.at(u)) {
                    new_hub_scores[u] += new_authority_scores[edge.target];
                }
            }
            
            // Normalize hub scores
            double hub_norm = 0.0;
            for (const auto& [_, score] : new_hub_scores) {
                hub_norm += score * score;
            }
            
            hub_norm = std::sqrt(hub_norm);
            
            if (hub_norm > 0) {
                for (auto& [v, score] : new_hub_scores) {
                    score /= hub_norm;
                }
            }
            
            // Check convergence
            double auth_diff = 0.0;
            double hub_diff = 0.0;
            
            for (int v : vertices) {
                auth_diff += std::abs(new_authority_scores[v] - authority_scores[v]);
                hub_diff += std::abs(new_hub_scores[v] - hub_scores[v]);
            }
            
            authority_scores = new_authority_scores;
            hub_scores = new_hub_scores;
            
            if (auth_diff < tolerance && hub_diff < tolerance) {
                break;
            }
        }
        
        return {authority_scores, hub_scores};
    }
    
    struct Edge {
        int source;
        int target;
        double weight;
        std::map<std::string, double> properties;
    };
    
private:
    bool directed_;
    bool weighted_;
    std::map<int, std::map<std::string, double>> vertices_;
    std::map<int, std::vector<Edge>> adjacency_list_;
};

/**
 * @brief Complex network analysis and generation
 */
class ComplexNetwork {
public:
    /**
     * @brief Generate an Erdős-Rényi random graph
     * 
     * @param n Number of vertices
     * @param p Edge probability
     * @param directed Whether the graph is directed
     * @param seed Random seed
     * @return Random graph
     */
    static Graph erdos_renyi(int n, double p, bool directed = false, unsigned int seed = 0) {
        if (n <= 0) {
            throw std::invalid_argument("Number of vertices must be positive");
        }
        
        if (p < 0.0 || p > 1.0) {
            throw std::invalid_argument("Edge probability must be between 0 and 1");
        }
        
        Graph graph(directed);
        
        // Initialize random number generator
        std::mt19937 gen(seed == 0 ? std::random_device{}() : seed);
        std::uniform_real_distribution<double> dist(0.0, 1.0);
        
        // Add vertices
        for (int i = 0; i < n; ++i) {
            graph.add_vertex(i);
        }
        
        // Add edges with probability p
        for (int i = 0; i < n; ++i) {
            for (int j = directed ? 0 : i + 1; j < n; ++j) {
                if (i != j && dist(gen) < p) {
                    graph.add_edge(i, j);
                }
            }
        }
        
        return graph;
    }
    
    /**
     * @brief Generate a Barabási-Albert scale-free network
     * 
     * @param n Number of vertices
     * @param m Number of edges to add per new vertex
     * @param directed Whether the graph is directed
     * @param seed Random seed
     * @return Scale-free network
     */
    static Graph barabasi_albert(int n, int m, bool directed = false, unsigned int seed = 0) {
        if (n <= 0) {
            throw std::invalid_argument("Number of vertices must be positive");
        }
        
        if (m <= 0 || m >= n) {
            throw std::invalid_argument("m must be positive and less than n");
        }
        
        Graph graph(directed);
        
        // Initialize random number generator
        std::mt19937 gen(seed == 0 ? std::random_device{}() : seed);
        
        // Add initial vertices
        for (int i = 0; i < m; ++i) {
            graph.add_vertex(i);
        }
        
        // Create complete graph of initial vertices
        for (int i = 0; i < m; ++i) {
            for (int j = i + 1; j < m; ++j) {
                graph.add_edge(i, j);
            }
        }
        
        // Add remaining vertices
        for (int i = m; i < n; ++i) {
            graph.add_vertex(i);
            
            // Compute degrees for preferential attachment
            std::vector<int> degrees;
            std::vector<int> vertices;
            int total_degree = 0;
            
            for (int j = 0; j < i; ++j) {
                int deg = graph.degree(j);
                degrees.push_back(deg);
                vertices.push_back(j);
                total_degree += deg;
            }
            
            // Add m edges using preferential attachment
            std::set<int> connected;
            
            for (int j = 0; j < m; ++j) {
                int target;
                
                do {
                    // Select vertex with probability proportional to degree
                    std::discrete_distribution<int> dist(degrees.begin(), degrees.end());
                    target = vertices[dist(gen)];
                } while (connected.find(target) != connected.end());
                
                graph.add_edge(i, target);
                connected.insert(target);
            }
        }
        
        return graph;
    }
    
    /**
     * @brief Generate a Watts-Strogatz small-world network
     * 
     * @param n Number of vertices
     * @param k Mean degree (must be even)
     * @param beta Rewiring probability
     * @param directed Whether the graph is directed
     * @param seed Random seed
     * @return Small-world network
     */
    static Graph watts_strogatz(int n, int k, double beta, bool directed = false, unsigned int seed = 0) {
        if (n <= 0) {
            throw std::invalid_argument("Number of vertices must be positive");
        }
        
        if (k <= 0 || k >= n || k % 2 != 0) {
            throw std::invalid_argument("k must be positive, even, and less than n");
        }
        
        if (beta < 0.0 || beta > 1.0) {
            throw std::invalid_argument("Beta must be between 0 and 1");
        }
        
        Graph graph(directed);
        
        // Initialize random number generator
        std::mt19937 gen(seed == 0 ? std::random_device{}() : seed);
        std::uniform_real_distribution<double> dist(0.0, 1.0);
        std::uniform_int_distribution<int> int_dist(0, n - 1);
        
        // Add vertices
        for (int i = 0; i < n; ++i) {
            graph.add_vertex(i);
        }
        
        // Create ring lattice
        int k_half = k / 2;
        
        for (int i = 0; i < n; ++i) {
            for (int j = 1; j <= k_half; ++j) {
                int target = (i + j) % n;
                graph.add_edge(i, target);
            }
        }
        
        // Rewire edges
        for (int i = 0; i < n; ++i) {
            for (int j = 1; j <= k_half; ++j) {
                if (dist(gen) < beta) {
                    int old_target = (i + j) % n;
                    
                    // Find a new target that is not already connected
                    int new_target;
                    do {
                        new_target = int_dist(gen);
                    } while (new_target == i || new_target == old_target || 
                             graph.get_neighbors(i).end() != 
                             std::find(graph.get_neighbors(i).begin(), 
                                     graph.get_neighbors(i).end(), new_target));
                    
                    // Rewire the edge
                    graph.remove_edge(i, old_target);
                    graph.add_edge(i, new_target);
                }
            }
        }
        
        return graph;
    }
    
    /**
     * @brief Generate a complete graph
     * 
     * @param n Number of vertices
     * @param directed Whether the graph is directed
     * @return Complete graph
     */
    static Graph complete_graph(int n, bool directed = false) {
        if (n <= 0) {
            throw std::invalid_argument("Number of vertices must be positive");
        }
        
        Graph graph(directed);
        
        // Add vertices
        for (int i = 0; i < n; ++i) {
            graph.add_vertex(i);
        }
        
        // Add all possible edges
        for (int i = 0; i < n; ++i) {
            for (int j = directed ? 0 : i + 1; j < n; ++j) {
                if (i != j) {
                    graph.add_edge(i, j);
                }
            }
        }
        
        return graph;
    }
    
    /**
     * @brief Generate a star graph
     * 
     * @param n Number of vertices
     * @param directed Whether the graph is directed
     * @return Star graph
     */
    static Graph star_graph(int n, bool directed = false) {
        if (n <= 1) {
            throw std::invalid_argument("Number of vertices must be greater than 1");
        }
        
        Graph graph(directed);
        
        // Add vertices
        for (int i = 0; i < n; ++i) {
            graph.add_vertex(i);
        }
        
        // Add edges from center (vertex 0) to all other vertices
        for (int i = 1; i < n; ++i) {
            graph.add_edge(0, i);
        }
        
        return graph;
    }
    
    /**
     * @brief Generate a random geometric graph
     * 
     * @param n Number of vertices
     * @param radius Connection radius
     * @param dim Dimension of the space
     * @param directed Whether the graph is directed
     * @param seed Random seed
     * @return Random geometric graph
     */
    static Graph random_geometric_graph(int n, double radius, int dim = 2, 
                                      bool directed = false, unsigned int seed = 0) {
        if (n <= 0) {
            throw std::invalid_argument("Number of vertices must be positive");
        }
        
        if (radius <= 0.0) {
            throw std::invalid_argument("Radius must be positive");
        }
        
        if (dim <= 0) {
            throw std::invalid_argument("Dimension must be positive");
        }
        
        Graph graph(directed);
        
        // Initialize random number generator
        std::mt19937 gen(seed == 0 ? std::random_device{}() : seed);
        std::uniform_real_distribution<double> dist(0.0, 1.0);
        
        // Generate random positions
        std::vector<Eigen::VectorXd> positions;
        
        for (int i = 0; i < n; ++i) {
            Eigen::VectorXd pos(dim);
            for (int j = 0; j < dim; ++j) {
                pos(j) = dist(gen);
            }
            positions.push_back(pos);
            
            // Add vertex with position as property
            std::map<std::string, double> props;
            for (int j = 0; j < dim; ++j) {
                props["pos_" + std::to_string(j)] = pos(j);
            }
            
            graph.add_vertex(i, props);
        }
        
        // Connect vertices within radius
        double radius_squared = radius * radius;
        
        for (int i = 0; i < n; ++i) {
            for (int j = directed ? 0 : i + 1; j < n; ++j) {
                if (i != j) {
                    double dist_squared = (positions[i] - positions[j]).squaredNorm();
                    
                    if (dist_squared <= radius_squared) {
                        graph.add_edge(i, j);
                    }
                }
            }
        }
        
        return graph;
    }
    
    /**
     * @brief Compute the rich club coefficient of a graph
     * 
     * @param graph Input graph
     * @param k Degree threshold
     * @return Rich club coefficient
     */
    static double rich_club_coefficient(const Graph& graph, int k) {
        std::vector<int> vertices = graph.get_vertices();
        
        // Find vertices with degree greater than k
        std::vector<int> rich_vertices;
        
        for (int v : vertices) {
            if (graph.degree(v) > k) {
                rich_vertices.push_back(v);
            }
        }
        
        int n_rich = rich_vertices.size();
        
        if (n_rich <= 1) {
            return 0.0;
        }
        
        // Count edges between rich vertices
        int edge_count = 0;
        
        for (int i = 0; i < n_rich; ++i) {
            for (int j = i + 1; j < n_rich; ++j) {
                for (const auto& edge : graph.get_incident_edges(rich_vertices[i])) {
                    if (edge.target == rich_vertices[j]) {
                        ++edge_count;
                        break;
                    }
                }
            }
        }
        
        // Compute rich club coefficient
        int max_edges = (n_rich * (n_rich - 1)) / 2;
        
        return max_edges > 0 ? static_cast<double>(edge_count) / max_edges : 0.0;
    }
    
    /**
     * @brief Compute the assortativity coefficient of a graph
     * 
     * @param graph Input graph
     * @return Assortativity coefficient
     */
    static double assortativity_coefficient(const Graph& graph) {
        std::vector<std::pair<int, int>> degree_pairs;
        std::vector<int> vertices = graph.get_vertices();
        
        for (int u : vertices) {
            int deg_u = graph.degree(u);
            
            for (const auto& edge : graph.get_incident_edges(u)) {
                int v = edge.target;
                
                if (u < v || graph.get_incident_edges(u).size() < graph.get_incident_edges(v).size()) {
                    int deg_v = graph.degree(v);
                    degree_pairs.push_back({deg_u, deg_v});
                }
            }
        }
        
        if (degree_pairs.empty()) {
            return 0.0;
        }
        
        // Compute mean and variance
        double sum_x = 0.0, sum_y = 0.0;
        double sum_xy = 0.0, sum_x2 = 0.0, sum_y2 = 0.0;
        int m = degree_pairs.size();
        
        for (const auto& [x, y] : degree_pairs) {
            sum_x += x;
            sum_y += y;
            sum_xy += x * y;
            sum_x2 += x * x;
            sum_y2 += y * y;
        }
        
        double mean_x = sum_x / m;
        double mean_y = sum_y / m;
        double var_x = sum_x2 / m - mean_x * mean_x;
        double var_y = sum_y2 / m - mean_y * mean_y;
        
        if (var_x * var_y == 0.0) {
            return 0.0;
        }
        
        // Compute assortativity
        double cov_xy = sum_xy / m - mean_x * mean_y;
        return cov_xy / std::sqrt(var_x * var_y);
    }
    
    /**
     * @brief Compute the modularity of a graph with respect to a partition
     * 
     * @param graph Input graph
     * @param partition Mapping from vertex ID to community ID
     * @return Modularity value
     */
    static double modularity(const Graph& graph, const std::map<int, int>& partition) {
        Eigen::MatrixXd adj_matrix = graph.get_adjacency_matrix();
        std::vector<int> vertices = graph.get_vertices();
        int n = vertices.size();
        
        // Create a mapping from vertex IDs to indices
        std::map<int, int> id_to_index;
        for (int i = 0; i < n; ++i) {
            id_to_index[vertices[i]] = i;
        }
        
        // Compute total edge weight
        double total_weight = adj_matrix.sum();
        
        if (total_weight == 0.0) {
            return 0.0;
        }
        
        // Compute degrees
        Eigen::VectorXd degrees = adj_matrix.rowwise().sum();
        
        // Compute modularity
        double modularity = 0.0;
        
        for (int i = 0; i < n; ++i) {
            for (int j = 0; j < n; ++j) {
                if (partition.at(vertices[i]) == partition.at(vertices[j])) {
                    modularity += adj_matrix(i, j) - degrees(i) * degrees(j) / total_weight;
                }
            }
        }
        
        return modularity / total_weight;
    }
    
    /**
     * @brief Detect communities using the Louvain algorithm
     * 
     * @param graph Input graph
     * @param resolution Resolution parameter (higher values lead to smaller communities)
     * @return Mapping from vertex ID to community ID
     */
    static std::map<int, int> louvain_communities(const Graph& graph, double resolution = 1.0) {
        std::vector<int> vertices = graph.get_vertices();
        int n = vertices.size();
        
        // Initialize each vertex to its own community
        std::map<int, int> partition;
        for (int v : vertices) {
            partition[v] = v;
        }
        
        bool improvement = true;
        
        while (improvement) {
            improvement = false;
            
            // First phase: Optimize modularity by moving individual vertices
            bool local_improvement = true;
            
            while (local_improvement) {
                local_improvement = false;
                
                for (int v : vertices) {
                    int current_community = partition[v];
                    std::map<int, double> community_to_gain;
                    
                    // Compute gain for each possible community
                    for (const auto& edge : graph.get_incident_edges(v)) {
                        int neighbor = edge.target;
                        int neighbor_community = partition[neighbor];
                        
                        if (community_to_gain.find(neighbor_community) == community_to_gain.end()) {
                            // Compute modularity gain if v moves to neighbor_community
                            double gain = compute_modularity_gain(graph, v, current_community, 
                                                                neighbor_community, partition, resolution);
                            
                            community_to_gain[neighbor_community] = gain;
                        }
                    }
                    
                    // Find best community to move to
                    int best_community = current_community;
                    double best_gain = 0.0;
                    
                    for (const auto& [community, gain] : community_to_gain) {
                        if (gain > best_gain) {
                            best_gain = gain;
                            best_community = community;
                        }
                    }
                    
                    // Move v to best community if it improves modularity
                    if (best_community != current_community) {
                        partition[v] = best_community;
                        local_improvement = true;
                        improvement = true;
                    }
                }
            }
            
            // Second phase: Aggregate communities into super-vertices
            if (improvement) {
                // Create a new graph where communities are vertices
                Graph new_graph(graph.get_adjacency_matrix().rows() > 0 && 
                             graph.get_adjacency_matrix().cols() > 0);
                
                std::map<int, int> community_to_vertex;
                int next_id = 0;
                
                // Add vertices for each community
                for (const auto& [v, community] : partition) {
                    if (community_to_vertex.find(community) == community_to_vertex.end()) {
                        community_to_vertex[community] = next_id++;
                        new_graph.add_vertex(community_to_vertex[community]);
                    }
                }
                
                // Add edges between communities
                for (int u : vertices) {
                    int u_community = community_to_vertex[partition[u]];
                    
                    for (const auto& edge : graph.get_incident_edges(u)) {
                        int v = edge.target;
                        int v_community = community_to_vertex[partition[v]];
                        
                        if (u_community != v_community) {
                            // Add or update edge weight between communities
                            bool edge_exists = false;
                            for (const auto& comm_edge : new_graph.get_incident_edges(u_community)) {
                                if (comm_edge.target == v_community) {
                                    edge_exists = true;
                                    break;
                                }
                            }
                            
                            if (!edge_exists) {
                                new_graph.add_edge(u_community, v_community, edge.weight);
                            }
                        }
                    }
                }
                
                // Update partition mapping
                std::map<int, int> new_partition;
                for (const auto& [v, old_community] : partition) {
                    new_partition[v] = community_to_vertex[old_community];
                }
                
                partition = new_partition;
            }
        }
        
        return partition;
    }

private:
    /**
     * @brief Compute modularity gain for moving a vertex to a different community
     * 
     * @param graph Input graph
     * @param vertex Vertex to move
     * @param from_community Current community
     * @param to_community Target community
     * @param partition Current partition
     * @param resolution Resolution parameter
     * @return Modularity gain
     */
    static double compute_modularity_gain(const Graph& graph, int vertex,
                                        int from_community, int to_community,
                                        const std::map<int, int>& partition,
                                        double resolution);
};

} // namespace graph

/**
 * @brief Fast Fourier Transform operations
 */
namespace fft {

/**
 * @brief FFT configuration structure
 */
struct FFTConfig {
    int size;                    // FFT size (must be power of 2)
    bool forward;               // True for forward FFT, false for inverse
    bool normalized;            // Whether to normalize the result
    std::string window_type;    // Window function type ("none", "hanning", "hamming", "blackman", etc.)
    double zero_padding_factor; // Zero padding factor (1.0 = no padding, 2.0 = double size)
};

/**
 * @brief Complex FFT using FFTW
 * 
 * @param input Input signal (real or complex)
 * @param config FFT configuration
 * @return FFT result
 */
std::vector<std::complex<double>>
fft_complex(const std::vector<std::complex<double>>& input,
           const FFTConfig& config = {0, true, true, "none", 1.0});

/**
 * @brief Real FFT using FFTW
 * 
 * @param input Input real signal
 * @param config FFT configuration
 * @return FFT result (complex)
 */
std::vector<std::complex<double>>
fft_real(const std::vector<double>& input,
        const FFTConfig& config = {0, true, true, "none", 1.0});

/**
 * @brief Inverse FFT using FFTW
 * 
 * @param input Input FFT coefficients
 * @param config FFT configuration
 * @return Inverse FFT result
 */
std::vector<std::complex<double>>
ifft_complex(const std::vector<std::complex<double>>& input,
            const FFTConfig& config = {0, false, true, "none", 1.0});

/**
 * @brief Real inverse FFT using FFTW
 * 
 * @param input Input FFT coefficients
 * @param config FFT configuration
 * @return Real inverse FFT result
 */
std::vector<double>
ifft_real(const std::vector<std::complex<double>>& input,
         const FFTConfig& config = {0, false, true, "none", 1.0});

/**
 * @brief 2D FFT using FFTW
 * 
 * @param input Input 2D signal
 * @param config FFT configuration
 * @return 2D FFT result
 */
Eigen::MatrixXcd
fft2d(const Eigen::MatrixXcd& input,
     const FFTConfig& config = {0, true, true, "none", 1.0});

/**
 * @brief 2D real FFT using FFTW
 * 
 * @param input Input 2D real signal
 * @param config FFT configuration
 * @return 2D FFT result (complex)
 */
Eigen::MatrixXcd
fft2d_real(const Eigen::MatrixXd& input,
          const FFTConfig& config = {0, true, true, "none", 1.0});

/**
 * @brief 2D inverse FFT using FFTW
 * 
 * @param input Input 2D FFT coefficients
 * @param config FFT configuration
 * @return 2D inverse FFT result
 */
Eigen::MatrixXcd
ifft2d(const Eigen::MatrixXcd& input,
      const FFTConfig& config = {0, false, true, "none", 1.0});

/**
 * @brief Apply window function to signal
 * 
 * @param signal Input signal
 * @param window_type Window function type
 * @return Windowed signal
 */
template <typename T>
std::vector<T> apply_window(const std::vector<T>& signal, const std::string& window_type);

/**
 * @brief Compute power spectral density
 * 
 * @param signal Input signal
 * @param sampling_rate Sampling rate
 * @param window_type Window function type
 * @param overlap_ratio Overlap ratio for segments
 * @param nfft FFT size
 * @return Pair of (frequencies, power spectral density)
 */
std::pair<std::vector<double>, std::vector<double>>
power_spectral_density(const std::vector<double>& signal,
                      double sampling_rate = 1.0,
                      const std::string& window_type = "hanning",
                      double overlap_ratio = 0.5,
                      int nfft = 0);

/**
 * @brief Compute spectrogram
 * 
 * @param signal Input signal
 * @param sampling_rate Sampling rate
 * @param window_size Window size
 * @param overlap_ratio Overlap ratio
 * @param window_type Window function type
 * @return Tuple of (time_axis, frequency_axis, spectrogram_matrix)
 */
std::tuple<std::vector<double>, std::vector<double>, Eigen::MatrixXd>
spectrogram(const std::vector<double>& signal,
           double sampling_rate = 1.0,
           int window_size = 256,
           double overlap_ratio = 0.5,
           const std::string& window_type = "hanning");

/**
 * @brief Compute cross-correlation using FFT
 * 
 * @param signal1 First signal
 * @param signal2 Second signal
 * @param mode Correlation mode ("full", "valid", "same")
 * @return Cross-correlation result
 */
std::vector<double>
cross_correlation_fft(const std::vector<double>& signal1,
                     const std::vector<double>& signal2,
                     const std::string& mode = "full");

/**
 * @brief Compute convolution using FFT
 * 
 * @param signal First signal
 * @param kernel Convolution kernel
 * @param mode Convolution mode ("full", "valid", "same")
 * @return Convolution result
 */
std::vector<double>
convolution_fft(const std::vector<double>& signal,
               const std::vector<double>& kernel,
               const std::string& mode = "full");

/**
 * @brief Compute coherence between two signals
 * 
 * @param signal1 First signal
 * @param signal2 Second signal
 * @param sampling_rate Sampling rate
 * @param window_size Window size
 * @param overlap_ratio Overlap ratio
 * @return Pair of (frequencies, coherence)
 */
std::pair<std::vector<double>, std::vector<double>>
coherence(const std::vector<double>& signal1,
         const std::vector<double>& signal2,
         double sampling_rate = 1.0,
         int window_size = 256,
         double overlap_ratio = 0.5);

/**
 * @brief Compute phase spectrum
 * 
 * @param signal Input signal
 * @param sampling_rate Sampling rate
 * @param window_type Window function type
 * @return Pair of (frequencies, phase)
 */
std::pair<std::vector<double>, std::vector<double>>
phase_spectrum(const std::vector<double>& signal,
              double sampling_rate = 1.0,
              const std::string& window_type = "hanning");

/**
 * @brief Compute group delay
 * 
 * @param signal Input signal
 * @param sampling_rate Sampling rate
 * @return Pair of (frequencies, group_delay)
 */
std::pair<std::vector<double>, std::vector<double>>
group_delay(const std::vector<double>& signal, double sampling_rate = 1.0);

} // namespace fft

/**
 * @brief Random number generation and sampling
 */
namespace random {

/**
 * @brief Random number generator class
 */
class RandomGenerator {
public:
    /**
     * @brief Constructor
     * 
     * @param seed Random seed (0 for random seed)
     */
    explicit RandomGenerator(unsigned int seed = 0);
    
    /**
     * @brief Set random seed
     * 
     * @param seed New seed
     */
    void set_seed(unsigned int seed);
    
    /**
     * @brief Generate uniform random number
     * 
     * @param min Minimum value
     * @param max Maximum value
     * @return Random number in [min, max)
     */
    double uniform(double min = 0.0, double max = 1.0);
    
    /**
     * @brief Generate normal random number
     * 
     * @param mean Mean
     * @param std_dev Standard deviation
     * @return Normal random number
     */
    double normal(double mean = 0.0, double std_dev = 1.0);
    
    /**
     * @brief Generate exponential random number
     * 
     * @param lambda Rate parameter
     * @return Exponential random number
     */
    double exponential(double lambda = 1.0);
    
    /**
     * @brief Generate gamma random number
     * 
     * @param alpha Shape parameter
     * @param beta Rate parameter
     * @return Gamma random number
     */
    double gamma(double alpha, double beta = 1.0);
    
    /**
     * @brief Generate beta random number
     * 
     * @param alpha Alpha parameter
     * @param beta Beta parameter
     * @return Beta random number
     */
    double beta(double alpha, double beta);
    
    /**
     * @brief Generate Poisson random number
     * 
     * @param lambda Mean parameter
     * @return Poisson random number
     */
    int poisson(double lambda);
    
    /**
     * @brief Generate binomial random number
     * 
     * @param n Number of trials
     * @param p Success probability
     * @return Binomial random number
     */
    int binomial(int n, double p);
    
    /**
     * @brief Generate geometric random number
     * 
     * @param p Success probability
     * @return Geometric random number
     */
    int geometric(double p);
    
    /**
     * @brief Generate random integers
     * 
     * @param min Minimum value (inclusive)
     * @param max Maximum value (inclusive)
     * @return Random integer
     */
    int randint(int min, int max);
    
    /**
     * @brief Generate uniform random vector
     * 
     * @param size Vector size
     * @param min Minimum value
     * @param max Maximum value
     * @return Random vector
     */
    Eigen::VectorXd uniform_vector(int size, double min = 0.0, double max = 1.0);
    
    /**
     * @brief Generate normal random vector
     * 
     * @param size Vector size
     * @param mean Mean
     * @param std_dev Standard deviation
     * @return Random vector
     */
    Eigen::VectorXd normal_vector(int size, double mean = 0.0, double std_dev = 1.0);
    
    /**
     * @brief Generate multivariate normal random vector
     * 
     * @param mean Mean vector
     * @param covariance Covariance matrix
     * @return Random vector
     */
    Eigen::VectorXd multivariate_normal(const Eigen::VectorXd& mean,
                                       const Eigen::MatrixXd& covariance);
    
    /**
     * @brief Generate uniform random matrix
     * 
     * @param rows Number of rows
     * @param cols Number of columns
     * @param min Minimum value
     * @param max Maximum value
     * @return Random matrix
     */
    Eigen::MatrixXd uniform_matrix(int rows, int cols, double min = 0.0, double max = 1.0);
    
    /**
     * @brief Generate normal random matrix
     * 
     * @param rows Number of rows
     * @param cols Number of columns
     * @param mean Mean
     * @param std_dev Standard deviation
     * @return Random matrix
     */
    Eigen::MatrixXd normal_matrix(int rows, int cols, double mean = 0.0, double std_dev = 1.0);
    
    /**
     * @brief Generate random orthogonal matrix
     * 
     * @param size Matrix size
     * @return Random orthogonal matrix
     */
    Eigen::MatrixXd orthogonal_matrix(int size);
    
    /**
     * @brief Generate random positive definite matrix
     * 
     * @param size Matrix size
     * @param condition_number Desired condition number
     * @return Random positive definite matrix
     */
    Eigen::MatrixXd positive_definite_matrix(int size, double condition_number = 1.0);
    
    /**
     * @brief Sample without replacement
     * 
     * @param population Population to sample from
     * @param k Number of samples
     * @return Sampled indices
     */
    template <typename T>
    std::vector<int> sample_without_replacement(const std::vector<T>& population, int k);
    
    /**
     * @brief Sample with replacement
     * 
     * @param population Population to sample from
     * @param k Number of samples
     * @return Sampled indices
     */
    template <typename T>
    std::vector<int> sample_with_replacement(const std::vector<T>& population, int k);
    
    /**
     * @brief Shuffle a vector in place
     * 
     * @param vec Vector to shuffle
     */
    template <typename T>
    void shuffle(std::vector<T>& vec);
    
    /**
     * @brief Generate random permutation
     * 
     * @param n Size of permutation
     * @return Random permutation as vector of indices
     */
    std::vector<int> permutation(int n);
    
    /**
     * @brief Generate random choice from discrete distribution
     * 
     * @param probabilities Probability weights
     * @return Chosen index
     */
    int choice(const std::vector<double>& probabilities);
    
    /**
     * @brief Generate random samples from discrete distribution
     * 
     * @param probabilities Probability weights
     * @param n Number of samples
     * @return Vector of chosen indices
     */
    std::vector<int> choice_multiple(const std::vector<double>& probabilities, int n);

private:
    std::mt19937 generator_;
    std::uniform_real_distribution<double> uniform_dist_;
    std::normal_distribution<double> normal_dist_;
};

/**
 * @brief Generate Latin Hypercube samples
 * 
 * @param n_samples Number of samples
 * @param n_dimensions Number of dimensions
 * @param criterion Sampling criterion ("center", "maximin", "correlation", "random")
 * @param seed Random seed
 * @return Latin Hypercube samples
 */
Eigen::MatrixXd latin_hypercube_sampling(int n_samples, int n_dimensions,
                                        const std::string& criterion = "center",
                                        unsigned int seed = 0);

/**
 * @brief Generate Sobol sequence samples
 * 
 * @param n_samples Number of samples
 * @param n_dimensions Number of dimensions
 * @param skip Number of initial samples to skip
 * @return Sobol sequence samples
 */
Eigen::MatrixXd sobol_sequence(int n_samples, int n_dimensions, int skip = 0);

/**
 * @brief Generate Halton sequence samples
 * 
 * @param n_samples Number of samples
 * @param n_dimensions Number of dimensions
 * @param scramble Whether to scramble the sequence
 * @param seed Random seed for scrambling
 * @return Halton sequence samples
 */
Eigen::MatrixXd halton_sequence(int n_samples, int n_dimensions,
                               bool scramble = false, unsigned int seed = 0);

/**
 * @brief Generate Hammersley sequence samples
 * 
 * @param n_samples Number of samples
 * @param n_dimensions Number of dimensions
 * @return Hammersley sequence samples
 */
Eigen::MatrixXd hammersley_sequence(int n_samples, int n_dimensions);

/**
 * @brief Rejection sampling
 * 
 * @param target_pdf Target probability density function
 * @param proposal_sampler Function that samples from proposal distribution
 * @param proposal_pdf Proposal probability density function
 * @param M Upper bound such that target_pdf(x) <= M * proposal_pdf(x)
 * @param n_samples Number of samples to generate
 * @param max_attempts Maximum attempts per sample
 * @param seed Random seed
 * @return Samples from target distribution
 */
std::vector<double> rejection_sampling(
    const std::function<double(double)>& target_pdf,
    const std::function<double()>& proposal_sampler,
    const std::function<double(double)>& proposal_pdf,
    double M,
    int n_samples,
    int max_attempts = 1000,
    unsigned int seed = 0);

/**
 * @brief Metropolis-Hastings MCMC sampling
 * 
 * @param log_pdf Log probability density function
 * @param proposal_cov Proposal covariance matrix
 * @param initial_state Initial state
 * @param n_samples Number of samples
 * @param burn_in Number of burn-in samples
 * @param thin Thinning factor
 * @param seed Random seed
 * @return MCMC samples
 */
Eigen::MatrixXd metropolis_hastings(
    const std::function<double(const Eigen::VectorXd&)>& log_pdf,
    const Eigen::MatrixXd& proposal_cov,
    const Eigen::VectorXd& initial_state,
    int n_samples,
    int burn_in = 1000,
    int thin = 1,
    unsigned int seed = 0);

/**
 * @brief Gibbs sampling for multivariate distributions
 * 
 * @param conditional_samplers Vector of conditional sampling functions
 * @param initial_state Initial state
 * @param n_samples Number of samples
 * @param burn_in Number of burn-in samples
 * @param thin Thinning factor
 * @param seed Random seed
 * @return Gibbs samples
 */
Eigen::MatrixXd gibbs_sampling(
    const std::vector<std::function<double(const Eigen::VectorXd&, int)>>& conditional_samplers,
    const Eigen::VectorXd& initial_state,
    int n_samples,
    int burn_in = 1000,
    int thin = 1,
    unsigned int seed = 0);

} // namespace random

/**
 * @brief Special mathematical functions
 */
namespace special {

/**
 * @brief Gamma function
 * 
 * @param x Input value
 * @return Gamma(x)
 */
double gamma(double x);

/**
 * @brief Log gamma function
 * 
 * @param x Input value
 * @return log(Gamma(x))
 */
double log_gamma(double x);

/**
 * @brief Digamma function (derivative of log gamma)
 * 
 * @param x Input value
 * @return Digamma(x)
 */
double digamma(double x);

/**
 * @brief Polygamma function
 * 
 * @param n Order of the polygamma function
 * @param x Input value
 * @return Polygamma_n(x)
 */
double polygamma(int n, double x);

/**
 * @brief Beta function
 * 
 * @param a First parameter
 * @param b Second parameter
 * @return Beta(a, b)
 */
double beta(double a, double b);

/**
 * @brief Incomplete gamma function
 * 
 * @param a Shape parameter
 * @param x Input value
 * @return Lower incomplete gamma function
 */
double incomplete_gamma(double a, double x);

/**
 * @brief Regularized incomplete gamma function
 * 
 * @param a Shape parameter
 * @param x Input value
 * @return Regularized lower incomplete gamma function
 */
double regularized_incomplete_gamma(double a, double x);

/**
 * @brief Incomplete beta function
 * 
 * @param a First parameter
 * @param b Second parameter
 * @param x Input value
 * @return Incomplete beta function
 */
double incomplete_beta(double a, double b, double x);

/**
 * @brief Regularized incomplete beta function
 * 
 * @param a First parameter
 * @param b Second parameter
 * @param x Input value
 * @return Regularized incomplete beta function
 */
double regularized_incomplete_beta(double a, double b, double x);

/**
 * @brief Error function
 * 
 * @param x Input value
 * @return erf(x)
 */
double erf(double x);

/**
 * @brief Complementary error function
 * 
 * @param x Input value
 * @return erfc(x) = 1 - erf(x)
 */
double erfc(double x);

/**
 * @brief Inverse error function
 * 
 * @param x Input value
 * @return erf^(-1)(x)
 */
double erf_inv(double x);

/**
 * @brief Bessel function of the first kind
 * 
 * @param nu Order
 * @param x Input value
 * @return J_nu(x)
 */
double bessel_j(double nu, double x);

/**
 * @brief Bessel function of the second kind
 * 
 * @param nu Order
 * @param x Input value
 * @return Y_nu(x)
 */
double bessel_y(double nu, double x);

/**
 * @brief Modified Bessel function of the first kind
 * 
 * @param nu Order
 * @param x Input value
 * @return I_nu(x)
 */
double bessel_i(double nu, double x);

/**
 * @brief Modified Bessel function of the second kind
 * 
 * @param nu Order
 * @param x Input value
 * @return K_nu(x)
 */
double bessel_k(double nu, double x);

/**
 * @brief Spherical Bessel function of the first kind
 * 
 * @param n Order
 * @param x Input value
 * @return j_n(x)
 */
double spherical_bessel_j(int n, double x);

/**
 * @brief Spherical Bessel function of the second kind
 * 
 * @param n Order
 * @param x Input value
 * @return y_n(x)
 */
double spherical_bessel_y(int n, double x);

/**
 * @brief Airy function Ai
 * 
 * @param x Input value
 * @return Ai(x)
 */
double airy_ai(double x);

/**
 * @brief Airy function Bi
 * 
 * @param x Input value
 * @return Bi(x)
 */
double airy_bi(double x);

/**
 * @brief Hypergeometric function 1F1
 * 
 * @param a Parameter a
 * @param b Parameter b
 * @param x Input value
 * @return 1F1(a; b; x)
 */
double hypergeometric_1f1(double a, double b, double x);

/**
 * @brief Hypergeometric function 2F1
 * 
 * @param a Parameter a
 * @param b Parameter b
 * @param c Parameter c
 * @param x Input value
 * @return 2F1(a, b; c; x)
 */
double hypergeometric_2f1(double a, double b, double c, double x);

/**
 * @brief Elliptic integral of the first kind
 * 
 * @param k Modulus
 * @param phi Amplitude
 * @return F(phi, k)
 */
double elliptic_f(double phi, double k);

/**
 * @brief Elliptic integral of the second kind
 * 
 * @param k Modulus
 * @param phi Amplitude
 * @return E(phi, k)
 */
double elliptic_e(double phi, double k);

/**
 * @brief Elliptic integral of the third kind
 * 
 * @param n Characteristic
 * @param k Modulus
 * @param phi Amplitude
 * @return Pi(n, phi, k)
 */
double elliptic_pi(double n, double phi, double k);

/**
 * @brief Complete elliptic integral of the first kind
 * 
 * @param k Modulus
 * @return K(k)
 */
double elliptic_k(double k);

/**
 * @brief Complete elliptic integral of the second kind
 * 
 * @param k Modulus
 * @return E(k)
 */
double elliptic_e_complete(double k);

/**
 * @brief Jacobi elliptic functions
 * 
 * @param u Argument
 * @param m Parameter
 * @return Tuple of (sn, cn, dn)
 */
std::tuple<double, double, double> jacobi_elliptic(double u, double m);

/**
 * @brief Zeta function
 * 
 * @param s Input value
 * @return Zeta(s)
 */
double zeta(double s);

/**
 * @brief Hurwitz zeta function
 * 
 * @param s First parameter
 * @param a Second parameter
 * @return Zeta(s, a)
 */
double hurwitz_zeta(double s, double a);

/**
 * @brief Dirichlet eta function
 * 
 * @param s Input value
 * @return Eta(s)
 */
double dirichlet_eta(double s);

/**
 * @brief Lambert W function (principal branch)
 * 
 * @param x Input value
 * @return W_0(x)
 */
double lambert_w0(double x);

/**
 * @brief Lambert W function (second real branch)
 * 
 * @param x Input value
 * @return W_{-1}(x)
 */
double lambert_wm1(double x);

/**
 * @brief Legendre polynomial
 * 
 * @param n Degree
 * @param x Input value
 * @return P_n(x)
 */
double legendre_p(int n, double x);

/**
 * @brief Associated Legendre polynomial
 * 
 * @param n Degree
 * @param m Order
 * @param x Input value
 * @return P_n^m(x)
 */
double legendre_p_associated(int n, int m, double x);

/**
 * @brief Chebyshev polynomial of the first kind
 * 
 * @param n Degree
 * @param x Input value
 * @return T_n(x)
 */
double chebyshev_t(int n, double x);

/**
 * @brief Chebyshev polynomial of the second kind
 * 
 * @param n Degree
 * @param x Input value
 * @return U_n(x)
 */
double chebyshev_u(int n, double x);

/**
 * @brief Hermite polynomial (physicist's)
 * 
 * @param n Degree
 * @param x Input value
 * @return H_n(x)
 */
double hermite_h(int n, double x);

/**
 * @brief Hermite polynomial (probabilist's)
 * 
 * @param n Degree
 * @param x Input value
 * @return He_n(x)
 */
double hermite_he(int n, double x);

/**
 * @brief Laguerre polynomial
 * 
 * @param n Degree
 * @param x Input value
 * @return L_n(x)
 */
double laguerre_l(int n, double x);

/**
 * @brief Associated Laguerre polynomial
 * 
 * @param n Degree
 * @param alpha Parameter
 * @param x Input value
 * @return L_n^(alpha)(x)
 */
double laguerre_l_associated(int n, double alpha, double x);

/**
 * @brief Spherical harmonic
 * 
 * @param l Degree
 * @param m Order
 * @param theta Polar angle
 * @param phi Azimuthal angle
 * @return Y_l^m(theta, phi)
 */
std::complex<double> spherical_harmonic(int l, int m, double theta, double phi);

} // namespace special

/**
 * @brief Utility functions for mathematical operations
 */
namespace utils {

/**
 * @brief Check if a number is finite
 * 
 * @param x Input value
 * @return True if finite
 */
inline bool is_finite(double x) {
    return std::isfinite(x);
}

/**
 * @brief Check if a number is NaN
 * 
 * @param x Input value
 * @return True if NaN
 */
inline bool is_nan(double x) {
    return std::isnan(x);
}

/**
 * @brief Check if a number is infinite
 * 
 * @param x Input value
 * @return True if infinite
 */
inline bool is_inf(double x) {
    return std::isinf(x);
}

/**
 * @brief Safe division with check for zero denominator
 * 
 * @param numerator Numerator
 * @param denominator Denominator
 * @param default_value Value to return if denominator is zero
 * @return Division result or default value
 */
inline double safe_divide(double numerator, double denominator, double default_value = 0.0) {
    return (std::abs(denominator) < constants::EPSILON) ? default_value : numerator / denominator;
}

/**
 * @brief Clamp value to range
 * 
 * @param value Input value
 * @param min_val Minimum value
 * @param max_val Maximum value
 * @return Clamped value
 */
template <typename T>
constexpr T clamp(T value, T min_val, T max_val) {
    return std::max(min_val, std::min(value, max_val));
}

/**
 * @brief Linear map from one range to another
 * 
 * @param value Input value
 * @param from_min Source range minimum
 * @param from_max Source range maximum
 * @param to_min Target range minimum
 * @param to_max Target range maximum
 * @return Mapped value
 */
inline double linear_map(double value, double from_min, double from_max, 
                        double to_min, double to_max) {
    return to_min + (value - from_min) * (to_max - to_min) / (from_max - from_min);
}

/**
 * @brief Check if value is approximately equal to another value
 * 
 * @param a First value
 * @param b Second value
 * @param tolerance Tolerance for comparison
 * @return True if approximately equal
 */
inline bool approx_equal(double a, double b, double tolerance = constants::EPSILON) {
    return std::abs(a - b) < tolerance;
}

/**
 * @brief Sign function
 * 
 * @param x Input value
 * @return Sign of x (-1, 0, or 1)
 */
template <typename T>
constexpr int sign(T x) {
    return (T(0) < x) - (x < T(0));
}

/**
 * @brief Greatest common divisor
 * 
 * @param a First integer
 * @param b Second integer
 * @return GCD of a and b
 */
int gcd(int a, int b);

/**
 * @brief Least common multiple
 * 
 * @param a First integer
 * @param b Second integer
 * @return LCM of a and b
 */
int lcm(int a, int b);

/**
 * @brief Factorial function
 * 
 * @param n Input value
 * @return n!
 */
double factorial(int n);

/**
 * @brief Binomial coefficient
 * 
 * @param n Number of items
 * @param k Number of choices
 * @return C(n, k) = n! / (k! * (n-k)!)
 */
double binomial_coefficient(int n, int k);

/**
 * @brief Check if a number is prime
 * 
 * @param n Number to check
 * @return True if prime
 */
bool is_prime(int n);

/**
 * @brief Generate prime numbers up to n using Sieve of Eratosthenes
 * 
 * @param n Upper limit
 * @return Vector of prime numbers
 */
std::vector<int> sieve_of_eratosthenes(int n);

/**
 * @brief Prime factorization
 * 
 * @param n Number to factorize
 * @return Vector of prime factors
 */
std::vector<int> prime_factorization(int n);

/**
 * @brief Fibonacci number
 * 
 * @param n Index
 * @return n-th Fibonacci number
 */
int fibonacci(int n);

/**
 * @brief Check if matrix is positive definite
 * 
 * @param matrix Input matrix
 * @return True if positive definite
 */
bool is_positive_definite(const Eigen::MatrixXd& matrix);

/**
 * @brief Check if matrix is symmetric
 * 
 * @param matrix Input matrix
 * @param tolerance Tolerance for symmetry check
 * @return True if symmetric
 */
bool is_symmetric(const Eigen::MatrixXd& matrix, double tolerance = constants::EPSILON);

/**
 * @brief Make matrix symmetric by averaging with its transpose
 * 
 * @param matrix Input matrix
 * @return Symmetric matrix
 */
Eigen::MatrixXd make_symmetric(const Eigen::MatrixXd& matrix);

/**
 * @brief Regularize matrix by adding small value to diagonal
 * 
 * @param matrix Input matrix
 * @param regularization Regularization parameter
 * @return Regularized matrix
 */
Eigen::MatrixXd regularize_matrix(const Eigen::MatrixXd& matrix, 
                                 double regularization = constants::EPSILON);

} // namespace utils

} // namespace math
} // namespace utils
} // namespace ultra

#endif // ULTRA_UTILS_MATH_OPERATIONS_H