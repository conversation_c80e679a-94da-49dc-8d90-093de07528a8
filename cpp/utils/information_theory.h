// Ultra/cpp/utils/information_theory.h

#ifndef ULTRA_UTILS_INFORMATION_THEORY_H
#define ULTRA_UTILS_INFORMATION_THEORY_H

#include <Eigen/Dense>
#include <Eigen/Eigenvalues>
#include <vector>
#include <cmath>
#include <random>
#include <stdexcept>
#include <functional>
#include <map>
#include <set>
#include <memory>
#include <algorithm>
#include <iostream>
#include <limits>
#include <numeric>
#include <tuple>
#include <boost/math/special_functions/digamma.hpp>
#include <boost/math/special_functions/polygamma.hpp>

namespace ultra {
namespace utils {
namespace information_theory {

// Mathematical constants
constexpr double LOG_2 = 0.693147180559945309417232121458;
constexpr double EULER_GAMMA = 0.57721566490153286060651209008;
constexpr double EPSILON = 1e-10; // Small value to avoid log(0)

/**
 * @brief Enumeration of different entropy types
 */
enum class EntropyType {
    SHANNON,        // Shannon entropy for discrete distributions
    DIFFERENTIAL,   // Differential entropy for continuous distributions
    RENYI,          // Renyi entropy (parameterized)
    TSALLIS,        // Tsallis entropy (non-additive)
    VON_NEUMANN     // Von Neumann entropy for quantum states
};

/**
 * @brief Enumeration of different divergence measures
 */
enum class DivergenceType {
    KULLBACK_LEIBLER,   // Kullback-Leibler divergence
    JENSEN_SHANNON,     // Jensen-Shannon divergence
    HELLINGER,          // Hellinger distance
    TOTAL_VARIATION,    // Total variation distance
    WASSERSTEIN,        // Wasserstein distance (Earth Mover's)
    F_DIVERGENCE        // f-divergence (parameterized)
};

/**
 * @brief Enumeration of different integrated information theory measures
 */
enum class IITMeasure {
    PHI_DISCRETE,       // Discrete integrated information
    PHI_CONTINUOUS,     // Continuous integrated information
    PHI_STAR,           // Φ* (Phi-star) measure
    PHI_MAX,            // Maximum integrated information
    SMALL_PHI,          // Small-phi (φ) measure for subsystems
    EXCLUSION           // Information exclusion principle measure
};

// Forward declarations
class MutualInformation;
class IntegratedInformation;
class InformationGeometry;
class TransferEntropy;
class InformationBottleneck;
class KernelInformation;

/**
 * @brief Class for probability distribution operations
 */
class ProbabilityDistribution {
public:
    /**
     * @brief Constructor for discrete probability distribution
     * 
     * @param probabilities Vector of probability values
     * @param validate Whether to validate that probabilities sum to 1
     * @throws std::invalid_argument if probabilities don't sum to 1 (within tolerance)
     */
    explicit ProbabilityDistribution(const Eigen::VectorXd& probabilities, bool validate = true);
    
    /**
     * @brief Constructor for discrete joint probability distribution
     * 
     * @param joint_probabilities Matrix of joint probability values
     * @param validate Whether to validate that probabilities sum to 1
     * @throws std::invalid_argument if probabilities don't sum to 1 (within tolerance)
     */
    explicit ProbabilityDistribution(const Eigen::MatrixXd& joint_probabilities, bool validate = true);
    
    /**
     * @brief Constructor for multivariate continuous distribution
     * 
     * @param mean Mean vector
     * @param covariance Covariance matrix
     * @param distribution_type Type of continuous distribution ("gaussian", "student_t", etc.)
     */
    ProbabilityDistribution(const Eigen::VectorXd& mean, const Eigen::MatrixXd& covariance, 
                           const std::string& distribution_type = "gaussian");
    
    /**
     * @brief Create a discrete uniform distribution
     * 
     * @param size Number of elements in the distribution
     * @return Uniform probability distribution
     */
    static ProbabilityDistribution uniform_discrete(int size);
    
    /**
     * @brief Create a continuous uniform distribution
     * 
     * @param lower Lower bounds
     * @param upper Upper bounds
     * @return Uniform probability distribution
     */
    static ProbabilityDistribution uniform_continuous(const Eigen::VectorXd& lower, 
                                                     const Eigen::VectorXd& upper);
    
    /**
     * @brief Create a multivariate normal distribution
     * 
     * @param mean Mean vector
     * @param covariance Covariance matrix
     * @return Multivariate normal distribution
     */
    static ProbabilityDistribution multivariate_normal(const Eigen::VectorXd& mean, 
                                                      const Eigen::MatrixXd& covariance);
    
    /**
     * @brief Get marginal distribution
     * 
     * @param variables Indices of variables to keep
     * @return Marginal probability distribution
     */
    ProbabilityDistribution marginalize(const std::vector<int>& variables) const;
    
    /**
     * @brief Get conditional distribution
     * 
     * @param given_variables Indices of variables to condition on
     * @param given_values Values of variables to condition on
     * @return Conditional probability distribution
     */
    ProbabilityDistribution condition(const std::vector<int>& given_variables,
                                     const std::vector<double>& given_values) const;
    
    /**
     * @brief Compute entropy of the distribution
     * 
     * @param type Type of entropy to compute
     * @param alpha Parameter for parameterized entropy types (e.g., Renyi)
     * @return Entropy value in bits
     */
    double entropy(EntropyType type = EntropyType::SHANNON, double alpha = 1.0) const;
    
    /**
     * @brief Check if distribution is discrete
     * 
     * @return True if discrete, false otherwise
     */
    bool is_discrete() const;
    
    /**
     * @brief Get dimension of the distribution
     * 
     * @return Number of variables
     */
    int dimension() const;
    
    /**
     * @brief Get probability mass/density function
     * 
     * @param x Point at which to evaluate PMF/PDF
     * @return Probability mass/density
     */
    double pdf(const Eigen::VectorXd& x) const;
    
    /**
     * @brief Get cumulative distribution function
     * 
     * @param x Point at which to evaluate CDF
     * @return Cumulative probability
     */
    double cdf(const Eigen::VectorXd& x) const;
    
    /**
     * @brief Sample from the distribution
     * 
     * @param n_samples Number of samples
     * @param seed Random seed (0 for random)
     * @return Matrix of samples (rows: samples, cols: variables)
     */
    Eigen::MatrixXd sample(int n_samples, unsigned int seed = 0) const;
    
    /**
     * @brief Get mean of the distribution
     * 
     * @return Mean vector
     */
    Eigen::VectorXd mean() const;
    
    /**
     * @brief Get covariance of the distribution
     * 
     * @return Covariance matrix
     */
    Eigen::MatrixXd covariance() const;
    
    /**
     * @brief Get probability array for discrete distribution
     * 
     * @return Probability array
     */
    const Eigen::VectorXd& get_probabilities() const;
    
    /**
     * @brief Get joint probability matrix for discrete distribution
     * 
     * @return Joint probability matrix
     */
    const Eigen::MatrixXd& get_joint_probabilities() const;
    
    /**
     * @brief Get distribution type
     * 
     * @return String describing the distribution type
     */
    const std::string& get_distribution_type() const;
    
    /**
     * @brief Create product distribution of two independent distributions
     * 
     * @param other Other distribution
     * @return Product distribution
     */
    ProbabilityDistribution operator*(const ProbabilityDistribution& other) const;
    
    /**
     * @brief Compute KL divergence from this distribution to another
     * 
     * @param other Target distribution
     * @return KL divergence value
     */
    double kl_divergence_to(const ProbabilityDistribution& other) const;
    
    /**
     * @brief Compute Jensen-Shannon divergence between distributions
     * 
     * @param other Other distribution
     * @return Jensen-Shannon divergence value
     */
    double jensen_shannon_divergence(const ProbabilityDistribution& other) const;

private:
    bool discrete_; // Whether distribution is discrete
    std::string distribution_type_; // Type of distribution
    
    // For discrete distributions
    Eigen::VectorXd probabilities_; // 1D probability vector
    Eigen::MatrixXd joint_probabilities_; // 2D joint probability matrix
    
    // For continuous distributions
    Eigen::VectorXd mean_; // Mean vector
    Eigen::MatrixXd covariance_; // Covariance matrix
    Eigen::MatrixXd precision_; // Precision matrix (inverse of covariance)
    double normalization_factor_; // Normalization factor for PDF
    
    // Internal helper methods
    void validate_discrete_probabilities();
    void validate_continuous_parameters();
    void compute_normalization_factor();
    double multivariate_normal_pdf(const Eigen::VectorXd& x) const;
};

/**
 * @brief Entropy calculations for various distribution types
 */
class Entropy {
public:
    /**
     * @brief Compute Shannon entropy of a discrete distribution
     * 
     * @param probabilities Probability vector
     * @return Entropy value in bits
     */
    static double shannon(const Eigen::VectorXd& probabilities);
    
    /**
     * @brief Compute joint Shannon entropy of a joint discrete distribution
     * 
     * @param joint_probabilities Joint probability matrix
     * @return Joint entropy value in bits
     */
    static double shannon_joint(const Eigen::MatrixXd& joint_probabilities);
    
    /**
     * @brief Compute conditional Shannon entropy
     * 
     * @param joint_probabilities Joint probability matrix
     * @return Conditional entropy value in bits
     */
    static double shannon_conditional(const Eigen::MatrixXd& joint_probabilities);
    
    /**
     * @brief Compute differential entropy of a continuous distribution
     * 
     * @param distribution Continuous probability distribution
     * @return Differential entropy value in bits
     */
    static double differential(const ProbabilityDistribution& distribution);
    
    /**
     * @brief Compute differential entropy of a multivariate normal distribution
     * 
     * @param covariance Covariance matrix
     * @return Differential entropy value in bits
     */
    static double differential_multivariate_normal(const Eigen::MatrixXd& covariance);
    
    /**
     * @brief Compute Renyi entropy of a distribution
     * 
     * @param probabilities Probability vector
     * @param alpha Renyi entropy order parameter
     * @return Renyi entropy value
     */
    static double renyi(const Eigen::VectorXd& probabilities, double alpha);
    
    /**
     * @brief Compute Tsallis entropy of a distribution
     * 
     * @param probabilities Probability vector
     * @param q Non-extensivity parameter
     * @return Tsallis entropy value
     */
    static double tsallis(const Eigen::VectorXd& probabilities, double q);
    
    /**
     * @brief Compute von Neumann entropy of a quantum density matrix
     * 
     * @param density_matrix Quantum density matrix
     * @return von Neumann entropy value
     */
    static double von_neumann(const Eigen::MatrixXcd& density_matrix);
    
    /**
     * @brief Compute sample entropy of a time series
     * 
     * @param time_series Time series data
     * @param embedding_dim Embedding dimension
     * @param tolerance Tolerance for matching
     * @return Sample entropy value
     */
    static double sample_entropy(const Eigen::VectorXd& time_series, int embedding_dim, double tolerance);
    
    /**
     * @brief Compute approximate entropy of a time series
     * 
     * @param time_series Time series data
     * @param embedding_dim Embedding dimension
     * @param tolerance Tolerance for matching
     * @return Approximate entropy value
     */
    static double approximate_entropy(const Eigen::VectorXd& time_series, int embedding_dim, double tolerance);
    
    /**
     * @brief Compute permutation entropy of a time series
     * 
     * @param time_series Time series data
     * @param order Permutation order
     * @param delay Delay parameter
     * @return Permutation entropy value
     */
    static double permutation_entropy(const Eigen::VectorXd& time_series, int order, int delay = 1);
    
    /**
     * @brief Compute cross-entropy between two distributions
     * 
     * @param p First probability distribution
     * @param q Second probability distribution
     * @return Cross-entropy value
     */
    static double cross_entropy(const ProbabilityDistribution& p, const ProbabilityDistribution& q);
    
    /**
     * @brief Compute Kullback-Leibler divergence between two distributions
     * 
     * @param p First probability distribution
     * @param q Second probability distribution
     * @return KL divergence value
     */
    static double kl_divergence(const ProbabilityDistribution& p, const ProbabilityDistribution& q);
    
    /**
     * @brief Compute Jensen-Shannon divergence between two distributions
     * 
     * @param p First probability distribution
     * @param q Second probability distribution
     * @return Jensen-Shannon divergence value
     */
    static double jensen_shannon_divergence(const ProbabilityDistribution& p, const ProbabilityDistribution& q);

private:
    // Utility functions
    static double log2(double x);
    static double log_sum(const Eigen::VectorXd& log_values);
    static Eigen::VectorXd log_probabilities(const Eigen::VectorXd& probabilities);
};

/**
 * @brief Mutual information calculations
 */
class MutualInformation {
public:
    /**
     * @brief Compute mutual information between two discrete random variables
     * 
     * @param joint_probabilities Joint probability matrix
     * @return Mutual information value in bits
     */
    static double discrete(const Eigen::MatrixXd& joint_probabilities);
    
    /**
     * @brief Compute mutual information between two continuous random variables
     * 
     * @param joint_distribution Joint probability distribution
     * @return Mutual information value in bits
     */
    static double continuous(const ProbabilityDistribution& joint_distribution);
    
    /**
     * @brief Compute mutual information between two Gaussian random variables
     * 
     * @param covariance Joint covariance matrix
     * @return Mutual information value in bits
     */
    static double gaussian(const Eigen::MatrixXd& covariance);
    
    /**
     * @brief Compute conditional mutual information I(X;Y|Z)
     * 
     * @param joint_distribution Joint distribution of X, Y, and Z
     * @param x_indices Indices of X variables
     * @param y_indices Indices of Y variables
     * @param z_indices Indices of Z variables
     * @return Conditional mutual information value
     */
    static double conditional(const ProbabilityDistribution& joint_distribution,
                             const std::vector<int>& x_indices,
                             const std::vector<int>& y_indices,
                             const std::vector<int>& z_indices);
    
    /**
     * @brief Compute normalized mutual information
     * 
     * @param joint_probabilities Joint probability matrix
     * @param normalization Normalization method ("arithmetic", "geometric", "min", "max")
     * @return Normalized mutual information value
     */
    static double normalized(const Eigen::MatrixXd& joint_probabilities, 
                           const std::string& normalization = "geometric");
    
    /**
     * @brief Estimate mutual information from samples using k-nearest neighbors
     * 
     * @param x_samples Samples from X
     * @param y_samples Samples from Y
     * @param k Number of nearest neighbors
     * @return Estimated mutual information value
     */
    static double knn_estimate(const Eigen::MatrixXd& x_samples, 
                             const Eigen::MatrixXd& y_samples,
                             int k = 3);
    
    /**
     * @brief Compute multivariate mutual information (interaction information)
     * 
     * @param joint_distribution Joint distribution of multiple variables
     * @param variable_sets Vector of sets of variable indices
     * @return Multivariate mutual information value
     */
    static double multivariate(const ProbabilityDistribution& joint_distribution,
                             const std::vector<std::vector<int>>& variable_sets);
    
    /**
     * @brief Compute the mutual information between multiple variables
     * 
     * @param joint_distribution Joint distribution of all variables
     * @return Matrix of pairwise mutual information values
     */
    static Eigen::MatrixXd pairwise_matrix(const ProbabilityDistribution& joint_distribution);
};

/**
 * @brief Transfer entropy calculations
 */
class TransferEntropy {
public:
    /**
     * @brief Compute transfer entropy from source to target
     * 
     * @param source_series Source time series
     * @param target_series Target time series
     * @param k History length for target
     * @param l History length for source
     * @return Transfer entropy value
     */
    static double compute(const Eigen::VectorXd& source_series,
                        const Eigen::VectorXd& target_series,
                        int k = 1, int l = 1);
    
    /**
     * @brief Compute conditional transfer entropy
     * 
     * @param source_series Source time series
     * @param target_series Target time series
     * @param conditioning_series Conditioning time series
     * @param k History length for target
     * @param l History length for source
     * @param m History length for conditioning
     * @return Conditional transfer entropy value
     */
    static double conditional(const Eigen::VectorXd& source_series,
                            const Eigen::VectorXd& target_series,
                            const Eigen::VectorXd& conditioning_series,
                            int k = 1, int l = 1, int m = 1);
    
    /**
     * @brief Compute multivariate transfer entropy
     * 
     * @param source_series_set Set of source time series
     * @param target_series Target time series
     * @param k History length for target
     * @param l History length for sources
     * @return Multivariate transfer entropy value
     */
    static double multivariate(const std::vector<Eigen::VectorXd>& source_series_set,
                             const Eigen::VectorXd& target_series,
                             int k = 1, int l = 1);
    
    /**
     * @brief Compute symbolic transfer entropy
     * 
     * @param source_series Source time series
     * @param target_series Target time series
     * @param embedding_dim Embedding dimension
     * @param delay Delay parameter
     * @return Symbolic transfer entropy value
     */
    static double symbolic(const Eigen::VectorXd& source_series,
                         const Eigen::VectorXd& target_series,
                         int embedding_dim = 3, int delay = 1);
    
    /**
     * @brief Compute effective transfer entropy (corrected for small sample effects)
     * 
     * @param source_series Source time series
     * @param target_series Target time series
     * @param k History length for target
     * @param l History length for source
     * @param num_surrogate Number of surrogate datasets
     * @return Effective transfer entropy value
     */
    static double effective(const Eigen::VectorXd& source_series,
                          const Eigen::VectorXd& target_series,
                          int k = 1, int l = 1,
                          int num_surrogate = 100);
    
    /**
     * @brief Compute transfer entropy matrix for multiple time series
     * 
     * @param time_series_set Set of time series
     * @param k History length for targets
     * @param l History length for sources
     * @return Matrix of transfer entropy values
     */
    static Eigen::MatrixXd matrix(const std::vector<Eigen::VectorXd>& time_series_set,
                               int k = 1, int l = 1);

private:
    // Helper methods
    static Eigen::MatrixXd embed_time_series(const Eigen::VectorXd& time_series, 
                                          int embedding_dim, int delay);
    static Eigen::VectorXd symbolize_time_series(const Eigen::VectorXd& time_series, 
                                             int embedding_dim, int delay);
    static Eigen::VectorXd get_history(const Eigen::VectorXd& time_series, 
                                    int t, int history_length);
    static ProbabilityDistribution estimate_joint_distribution(
        const Eigen::MatrixXd& data, int bin_count = 0);
};

/**
 * @brief Integrated Information Theory (IIT) calculations
 */
class IntegratedInformation {
public:
    /**
     * @brief Constructor
     * 
     * @param data Time series data (rows: time steps, cols: variables)
     * @param tau Time lag
     */
    IntegratedInformation(const Eigen::MatrixXd& data, int tau = 1);
    
    /**
     * @brief Constructor with transition probability matrix
     * 
     * @param transition_matrix Transition probability matrix
     * @param state_probabilities Stationary distribution of states
     */
    IntegratedInformation(const Eigen::MatrixXd& transition_matrix, 
                         const Eigen::VectorXd& state_probabilities);
    
    /**
     * @brief Compute Φ (Phi) using the specified measure
     * 
     * @param measure IIT measure to use
     * @return Phi value
     */
    double compute_phi(IITMeasure measure = IITMeasure::PHI_DISCRETE);
    
    /**
     * @brief Get minimal information partition (MIP)
     * 
     * @return Partition that minimizes integrated information
     */
    std::pair<std::vector<int>, std::vector<int>> get_minimal_information_partition();
    
    /**
     * @brief Compute Φ for all possible bipartitions
     * 
     * @param measure IIT measure to use
     * @return Map of bipartition to Phi value
     */
    std::map<std::pair<std::vector<int>, std::vector<int>>, double> 
    compute_phi_for_all_bipartitions(IITMeasure measure = IITMeasure::PHI_DISCRETE);
    
    /**
     * @brief Compute Earth Mover's Distance (EMD) for state transitions
     * 
     * @param partition System partition
     * @return EMD value
     */
    double compute_emd(const std::pair<std::vector<int>, std::vector<int>>& partition);
    
    /**
     * @brief Compute effective information for a partition
     * 
     * @param partition System partition
     * @return Effective information value
     */
    double compute_effective_information(
        const std::pair<std::vector<int>, std::vector<int>>& partition);
    
    /**
     * @brief Compute causal density of the system
     * 
     * @return Causal density value
     */
    double compute_causal_density();
    
    /**
     * @brief Generate integrated information structures
     * 
     * @return Structures with corresponding Phi values
     */
    std::vector<std::pair<std::vector<int>, double>> generate_structures();
    
    /**
     * @brief Set complexity measure for Phi calculation
     * 
     * @param measure Complexity measure ("KLD", "EMD", "Wasserstein", "MMD", etc.)
     */
    void set_complexity_measure(const std::string& measure);
    
    /**
     * @brief Get integrated information scaling profile
     * 
     * @return Vector of Phi values at different scales
     */
    std::vector<double> get_scaling_profile();

private:
    Eigen::MatrixXd data_;  // Time series data
    Eigen::MatrixXd transition_matrix_;  // Transition probability matrix
    Eigen::VectorXd state_probabilities_;  // Stationary distribution
    int num_variables_;  // Number of variables/nodes
    int tau_;  // Time lag
    std::string complexity_measure_;  // Complexity measure for Phi
    
    // Cache for computations
    std::map<std::pair<std::vector<int>, std::vector<int>>, double> partition_phi_cache_;
    
    // Helper methods
    double phi_discrete_bipartition(const std::pair<std::vector<int>, std::vector<int>>& partition);
    double phi_continuous_bipartition(const std::pair<std::vector<int>, std::vector<int>>& partition);
    double phi_star_bipartition(const std::pair<std::vector<int>, std::vector<int>>& partition);
    double wasserstein_distance(const ProbabilityDistribution& p, 
                               const ProbabilityDistribution& q);
    Eigen::MatrixXd compute_joint_distribution(const Eigen::MatrixXd& data);
    std::vector<std::pair<std::vector<int>, std::vector<int>>> generate_all_bipartitions();
    ProbabilityDistribution get_transition_distribution(const std::vector<int>& subsystem);
};

/**
 * @brief Information geometry calculations
 */
class InformationGeometry {
public:
    /**
     * @brief Constructor
     * 
     * @param manifold_type Type of statistical manifold ("exponential", "mixture", etc.)
     */
    explicit InformationGeometry(const std::string& manifold_type = "exponential");
    
    /**
     * @brief Compute Fisher information matrix
     * 
     * @param distribution Probability distribution
     * @return Fisher information matrix
     */
    Eigen::MatrixXd fisher_information(const ProbabilityDistribution& distribution);
    
    /**
     * @brief Compute Fisher information distance between distributions
     * 
     * @param p First probability distribution
     * @param q Second probability distribution
     * @return Fisher information distance
     */
    double fisher_distance(const ProbabilityDistribution& p, const ProbabilityDistribution& q);
    
    /**
     * @brief Compute geodesic between distributions in information space
     * 
     * @param p First probability distribution
     * @param q Second probability distribution
     * @param num_points Number of points along geodesic
     * @return Vector of distributions along geodesic
     */
    std::vector<ProbabilityDistribution> geodesic(const ProbabilityDistribution& p,
                                               const ProbabilityDistribution& q,
                                               int num_points = 10);
    
    /**
     * @brief Compute Riemannian metric tensor at a point
     * 
     * @param distribution Probability distribution
     * @return Metric tensor
     */
    Eigen::MatrixXd metric_tensor(const ProbabilityDistribution& distribution);
    
    /**
     * @brief Compute Christoffel symbols at a point
     * 
     * @param distribution Probability distribution
     * @return Christoffel symbols (3D tensor)
     */
    std::vector<Eigen::MatrixXd> christoffel_symbols(const ProbabilityDistribution& distribution);
    
    /**
     * @brief Compute Riemann curvature tensor at a point
     * 
     * @param distribution Probability distribution
     * @return Riemann curvature tensor (4D tensor)
     */
    std::vector<std::vector<Eigen::MatrixXd>> riemann_curvature_tensor(
        const ProbabilityDistribution& distribution);
    
    /**
     * @brief Project distribution onto statistical manifold
     * 
     * @param empirical_distribution Empirical distribution
     * @param manifold_family Family of distributions defining the manifold
     * @return Projected distribution
     */
    ProbabilityDistribution project_onto_manifold(
        const ProbabilityDistribution& empirical_distribution,
        const std::string& manifold_family);
    
    /**
     * @brief Compute natural gradient for parameter optimization
     * 
     * @param loss_gradient Euclidean gradient of loss function
     * @param distribution Current distribution
     * @return Natural gradient
     */
    Eigen::VectorXd natural_gradient(const Eigen::VectorXd& loss_gradient,
                                   const ProbabilityDistribution& distribution);

private:
    std::string manifold_type_;
    
    // Helper methods
    Eigen::MatrixXd compute_fisher_information_exponential(
        const ProbabilityDistribution& distribution);
    Eigen::MatrixXd compute_fisher_information_mixture(
        const ProbabilityDistribution& distribution);
    std::vector<ProbabilityDistribution> compute_geodesic_exponential(
        const ProbabilityDistribution& p,
        const ProbabilityDistribution& q,
        int num_points);
    Eigen::MatrixXd compute_metric_tensor_for_family(
        const std::string& family, const Eigen::VectorXd& params);
};

/**
 * @brief Information bottleneck method
 */
class InformationBottleneck {
public:
    /**
     * @brief Constructor
     * 
     * @param joint_distribution Joint distribution of X and Y
     * @param compression_level Target compression level (bits)
     */
    InformationBottleneck(const ProbabilityDistribution& joint_distribution,
                         double compression_level);
    
    /**
     * @brief Find optimal encoding distribution
     * 
     * @param beta Trade-off parameter
     * @param max_iter Maximum iterations
     * @param tolerance Convergence tolerance
     * @return Encoding distribution p(t|x)
     */
    Eigen::MatrixXd find_optimal_encoding(double beta, int max_iter = 100, double tolerance = 1e-6);
    
    /**
     * @brief Compute information curve
     * 
     * @param beta_values Vector of trade-off parameters
     * @return Vector of (I(T;X), I(T;Y)) pairs
     */
    std::vector<std::pair<double, double>> compute_information_curve(
        const std::vector<double>& beta_values);
    
    /**
     * @brief Extract relevant features
     * 
     * @param x_samples Sample data for X
     * @param encoding_matrix Encoding matrix p(t|x)
     * @return Extracted features
     */
    Eigen::MatrixXd extract_features(const Eigen::MatrixXd& x_samples,
                                   const Eigen::MatrixXd& encoding_matrix);
    
    /**
     * @brief Compute optimal beta for target compression level
     * 
     * @return Optimal beta value
     */
    double find_optimal_beta();
    
    /**
     * @brief Determine optimal number of clusters/features
     * 
     * @param max_clusters Maximum number of clusters to consider
     * @return Optimal number of clusters
     */
    int find_optimal_num_clusters(int max_clusters = 10);
    
    /**
     * @brief Perform deterministic information bottleneck clustering
     * 
     * @param num_clusters Number of clusters
     * @return Cluster assignments
     */
    Eigen::VectorXi deterministic_clustering(int num_clusters);

private:
    ProbabilityDistribution joint_distribution_;
    double compression_level_;
    
    // Matrices for the algorithm
    Eigen::MatrixXd p_x_y_; // p(x,y)
    Eigen::VectorXd p_x_;   // p(x)
    Eigen::VectorXd p_y_;   // p(y)
    
    // Helper methods
    Eigen::MatrixXd initialize_encoding(int cardinality_t);
    double compute_functional(const Eigen::MatrixXd& p_t_given_x, double beta);
    Eigen::MatrixXd update_encoding(const Eigen::MatrixXd& p_t_given_x, double beta);
    std::pair<double, double> compute_mutual_information(const Eigen::MatrixXd& p_t_given_x);
};

/**
 * @brief Kernel-based information theoretic measures
 */
class KernelInformation {
public:
    /**
     * @brief Constructor
     * 
     * @param kernel_type Type of kernel ("gaussian", "laplacian", "polynomial", etc.)
     * @param kernel_params Kernel parameters
     */
    KernelInformation(const std::string& kernel_type = "gaussian",
                     const std::map<std::string, double>& kernel_params = {{"sigma", 1.0}});
    
    /**
     * @brief Compute Renyi's alpha-entropy using kernels
     * 
     * @param samples Sample data
     * @param alpha Entropy order
     * @return Renyi entropy estimate
     */
    double renyi_entropy(const Eigen::MatrixXd& samples, double alpha = 1.0);
    
    /**
     * @brief Compute kernel mutual information
     * 
     * @param x_samples Samples from X
     * @param y_samples Samples from Y
     * @return Mutual information estimate
     */
    double mutual_information(const Eigen::MatrixXd& x_samples,
                            const Eigen::MatrixXd& y_samples);
    
    /**
     * @brief Compute Maximum Mean Discrepancy (MMD)
     * 
     * @param x_samples Samples from first distribution
     * @param y_samples Samples from second distribution
     * @return MMD value
     */
    double maximum_mean_discrepancy(const Eigen::MatrixXd& x_samples,
                                  const Eigen::MatrixXd& y_samples);
    
    /**
     * @brief Compute kernel conditional independence test statistic
     * 
     * @param x_samples Samples from X
     * @param y_samples Samples from Y
     * @param z_samples Samples from Z
     * @return Test statistic value
     */
    double conditional_independence_test(const Eigen::MatrixXd& x_samples,
                                       const Eigen::MatrixXd& y_samples,
                                       const Eigen::MatrixXd& z_samples);
    
    /**
     * @brief Compute kernel canonical correlation analysis (KCCA)
     * 
     * @param x_samples Samples from X
     * @param y_samples Samples from Y
     * @param regularization Regularization parameter
     * @return Vector of canonical correlations
     */
    Eigen::VectorXd canonical_correlation_analysis(const Eigen::MatrixXd& x_samples,
                                               const Eigen::MatrixXd& y_samples,
                                               double regularization = 0.01);
    
    /**
     * @brief Set kernel type and parameters
     * 
     * @param kernel_type Type of kernel
     * @param kernel_params Kernel parameters
     */
    void set_kernel(const std::string& kernel_type,
                   const std::map<std::string, double>& kernel_params);
    
    /**
     * @brief Compute kernel bandwidth using median heuristic
     * 
     * @param samples Sample data
     * @return Optimal bandwidth
     */
    static double compute_median_bandwidth(const Eigen::MatrixXd& samples);

private:
    std::string kernel_type_;
    std::map<std::string, double> kernel_params_;
    
    // Kernel functions
    double kernel(const Eigen::VectorXd& x, const Eigen::VectorXd& y);
    Eigen::MatrixXd kernel_matrix(const Eigen::MatrixXd& samples);
    Eigen::MatrixXd centered_kernel_matrix(const Eigen::MatrixXd& kernel_mat);
    
    // Helper methods
    double gaussian_kernel(const Eigen::VectorXd& x, const Eigen::VectorXd& y, double sigma);
    double laplacian_kernel(const Eigen::VectorXd& x, const Eigen::VectorXd& y, double sigma);
    double polynomial_kernel(const Eigen::VectorXd& x, const Eigen::VectorXd& y, 
                           double degree, double coef0);
};

/**
 * @brief Utility functions for information theory calculations
 */
namespace utils {

/**
 * @brief Compute log base 2 of a value
 * 
 * @param x Input value
 * @return log2(x)
 */
inline double log2(double x) {
    return std::log(x) / LOG_2;
}

/**
 * @brief Compute entropy of a discrete distribution
 * 
 * @param probabilities Probability vector
 * @return Entropy value in bits
 */
double entropy(const Eigen::VectorXd& probabilities);

/**
 * @brief Compute Kullback-Leibler divergence between distributions
 * 
 * @param p First probability vector
 * @param q Second probability vector
 * @return KL divergence value
 */
double kl_divergence(const Eigen::VectorXd& p, const Eigen::VectorXd& q);

/**
 * @brief Compute Jensen-Shannon divergence between distributions
 * 
 * @param p First probability vector
 * @param q Second probability vector
 * @return JS divergence value
 */
double jensen_shannon_divergence(const Eigen::VectorXd& p, const Eigen::VectorXd& q);

/**
 * @brief Compute mutual information from joint probability matrix
 * 
 * @param joint_probabilities Joint probability matrix
 * @return Mutual information value
 */
double mutual_information(const Eigen::MatrixXd& joint_probabilities);

/**
 * @brief Estimate probability distribution from samples
 * 
 * @param samples Sample data
 * @param method Estimation method ("histogram", "kernel", "knn", etc.)
 * @param params Additional parameters for estimation
 * @return Estimated probability distribution
 */
ProbabilityDistribution estimate_distribution(
    const Eigen::MatrixXd& samples,
    const std::string& method = "histogram",
    const std::map<std::string, double>& params = {});

/**
 * @brief Compute all bipartitions of a set
 * 
 * @param n Set size
 * @return Vector of all bipartitions
 */
std::vector<std::pair<std::vector<int>, std::vector<int>>> all_bipartitions(int n);

/**
 * @brief Generate distance matrix from sample points
 * 
 * @param samples Sample points
 * @param metric Distance metric ("euclidean", "manhattan", "chebyshev", etc.)
 * @return Distance matrix
 */
Eigen::MatrixXd distance_matrix(
    const Eigen::MatrixXd& samples,
    const std::string& metric = "euclidean");

/**
 * @brief Generate k-nearest neighbors graph
 * 
 * @param distance_matrix Distance matrix
 * @param k Number of neighbors
 * @return Adjacency matrix of kNN graph
 */
Eigen::MatrixXi knn_graph(const Eigen::MatrixXd& distance_matrix, int k);

/**
 * @brief Compute eigendecomposition of a symmetric matrix
 * 
 * @param matrix Input symmetric matrix
 * @return Pair of eigenvalues and eigenvectors
 */
std::pair<Eigen::VectorXd, Eigen::MatrixXd> eigen_decomposition(const Eigen::MatrixXd& matrix);

/**
 * @brief Generate bootstrap samples
 * 
 * @param data Original data
 * @param num_samples Number of bootstrap samples
 * @param sample_size Size of each bootstrap sample (-1 for same as original)
 * @return Vector of bootstrap samples
 */
std::vector<Eigen::MatrixXd> bootstrap_samples(
    const Eigen::MatrixXd& data,
    int num_samples,
    int sample_size = -1);

} // namespace utils

/**
 * @brief Class for implementing integrated information across multiple scales of a system
 */
class IntegratedInformationScale {
public:
    /**
     * @brief Constructor
     * 
     * @param data Time series data (rows: time steps, columns: variables)
     * @param max_scale Maximum scale to consider
     * @param tau Time lag
     */
    IntegratedInformationScale(const Eigen::MatrixXd& data, int max_scale, int tau = 1);
    
    /**
     * @brief Compute integrated information across scales
     * 
     * @param measure IIT measure to use
     * @return Vector of Phi values at each scale
     */
    std::vector<double> compute_across_scales(IITMeasure measure = IITMeasure::PHI_DISCRETE);
    
    /**
     * @brief Find scale with maximum integrated information
     * 
     * @param measure IIT measure to use
     * @return Pair of (scale, phi value)
     */
    std::pair<int, double> find_scale_of_maximum_phi(IITMeasure measure = IITMeasure::PHI_DISCRETE);
    
    /**
     * @brief Compute multiscale complexity measure
     * 
     * @return Complexity value
     */
    double multiscale_complexity();
    
    /**
     * @brief Generate hierarchical partitions of the system
     * 
     * @return Hierarchical partition structure
     */
    std::vector<std::vector<std::vector<int>>> generate_hierarchical_partitions();
    
    /**
     * @brief Compute causal emergence across scales
     * 
     * @return Vector of causal emergence values
     */
    std::vector<double> causal_emergence();
    
    /**
     * @brief Get coarse-grained data at a specific scale
     * 
     * @param scale Scale level
     * @param method Coarse-graining method ("mean", "majority", "custom")
     * @return Coarse-grained data
     */
    Eigen::MatrixXd get_coarse_grained_data(int scale, const std::string& method = "mean");

private:
    Eigen::MatrixXd data_;
    int max_scale_;
    int tau_;
    int num_variables_;
    
    // Helper methods
    Eigen::MatrixXd coarse_grain(const Eigen::MatrixXd& data, int scale, const std::string& method);
    double compute_phi_at_scale(int scale, IITMeasure measure);
    std::vector<std::vector<int>> generate_partitions_at_scale(int scale);
};

/**
 * @brief Advanced calculations for information geometry
 */
class DifferentialGeometryInformation {
public:
    /**
     * @brief Constructor
     * 
     * @param metric_type Type of information metric ("fisher", "wasserstein", "alpha-divergence")
     * @param alpha Parameter for alpha-divergence (if applicable)
     */
    DifferentialGeometryInformation(const std::string& metric_type = "fisher", double alpha = 0.5);
    
    /**
     * @brief Compute parallel transport of a tangent vector
     * 
     * @param p_start Starting distribution
     * @param p_end Ending distribution
     * @param tangent_vector Tangent vector at starting point
     * @return Transported tangent vector
     */
    Eigen::VectorXd parallel_transport(const ProbabilityDistribution& p_start,
                                    const ProbabilityDistribution& p_end,
                                    const Eigen::VectorXd& tangent_vector);
    
    /**
     * @brief Compute sectional curvature at a point
     * 
     * @param distribution Probability distribution
     * @param v First tangent vector
     * @param w Second tangent vector
     * @return Sectional curvature
     */
    double sectional_curvature(const ProbabilityDistribution& distribution,
                              const Eigen::VectorXd& v,
                              const Eigen::VectorXd& w);
    
    /**
     * @brief Compute scalar curvature at a point
     * 
     * @param distribution Probability distribution
     * @return Scalar curvature
     */
    double scalar_curvature(const ProbabilityDistribution& distribution);
    
    /**
     * @brief Calculate the Levi-Civita connection
     * 
     * @param distribution Probability distribution
     * @param v First tangent vector
     * @param w Second tangent vector
     * @return Connection result
     */
    Eigen::VectorXd levi_civita_connection(const ProbabilityDistribution& distribution,
                                        const Eigen::VectorXd& v,
                                        const Eigen::VectorXd& w);
    
    /**
     * @brief Compute exponential map at a point
     * 
     * @param distribution Starting distribution
     * @param tangent_vector Tangent vector
     * @param t Parameter value
     * @return Resulting distribution
     */
    ProbabilityDistribution exponential_map(const ProbabilityDistribution& distribution,
                                          const Eigen::VectorXd& tangent_vector,
                                          double t = 1.0);
    
    /**
     * @brief Compute logarithmic map between distributions
     * 
     * @param p_start Starting distribution
     * @param p_end Ending distribution
     * @return Tangent vector
     */
    Eigen::VectorXd logarithmic_map(const ProbabilityDistribution& p_start,
                                 const ProbabilityDistribution& p_end);
    
    /**
     * @brief Compute Fréchet mean of distributions
     * 
     * @param distributions Vector of distributions
     * @param weights Vector of weights
     * @return Fréchet mean distribution
     */
    ProbabilityDistribution frechet_mean(
        const std::vector<ProbabilityDistribution>& distributions,
        const Eigen::VectorXd& weights);

private:
    std::string metric_type_;
    double alpha_;
    
    // Helper methods
    Eigen::MatrixXd compute_metric_tensor(const ProbabilityDistribution& distribution);
    std::vector<Eigen::MatrixXd> compute_christoffel_symbols(
        const ProbabilityDistribution& distribution);
    std::vector<std::vector<Eigen::MatrixXd>> compute_riemann_tensor(
        const ProbabilityDistribution& distribution);
};

/**
 * @brief Implementation of the Global Workspace Theory
 */
class GlobalWorkspaceTheory {
public:
    /**
     * @brief Constructor
     * 
     * @param num_modules Number of specialist modules
     * @param workspace_dim Dimension of the global workspace
     */
    GlobalWorkspaceTheory(int num_modules, int workspace_dim);
    
    /**
     * @brief Set module states
     * 
     * @param module_states Vector of module state vectors
     */
    void set_module_states(const std::vector<Eigen::VectorXd>& module_states);
    
    /**
     * @brief Compute competition for workspace access
     * 
     * @return Vector of access probabilities for each module
     */
    Eigen::VectorXd compute_access_competition();
    
    /**
     * @brief Update global workspace state
     * 
     * @param access_probabilities Vector of access probabilities
     * @return Updated workspace state
     */
    Eigen::VectorXd update_workspace(const Eigen::VectorXd& access_probabilities);
    
    /**
     * @brief Broadcast workspace content to modules
     * 
     * @param workspace_state Current workspace state
     * @return Updated module states
     */
    std::vector<Eigen::VectorXd> broadcast_to_modules(const Eigen::VectorXd& workspace_state);
    
    /**
     * @brief Compute integrated information of the workspace
     * 
     * @return Phi value of the workspace
     */
    double compute_workspace_phi();
    
    /**
     * @brief Check if current state is "conscious" according to GWT
     * 
     * @param threshold Phi threshold for consciousness
     * @return True if state is "conscious"
     */
    bool is_conscious_state(double threshold = 0.3);
    
    /**
     * @brief Compute influence matrix between modules
     * 
     * @return Influence matrix
     */
    Eigen::MatrixXd compute_module_influences();
    
    /**
     * @brief Get current global workspace state
     * 
     * @return Workspace state vector
     */
    const Eigen::VectorXd& get_workspace_state() const;
    
    /**
     * @brief Get current module states
     * 
     * @return Vector of module state vectors
     */
    const std::vector<Eigen::VectorXd>& get_module_states() const;

private:
    int num_modules_;
    int workspace_dim_;
    std::vector<Eigen::VectorXd> module_states_;
    Eigen::VectorXd workspace_state_;
    Eigen::MatrixXd module_workspace_weights_;
    Eigen::MatrixXd workspace_module_weights_;
    
    // Helper methods
    double compute_module_salience(const Eigen::VectorXd& module_state);
    Eigen::VectorXd integrate_module_contributions(
        const std::vector<Eigen::VectorXd>& contributions,
        const Eigen::VectorXd& weights);
    Eigen::VectorXd module_contribution_to_workspace(
        int module_idx, const Eigen::VectorXd& module_state);
};

// Implement core utility functions inline

namespace utils {

inline double entropy(const Eigen::VectorXd& probabilities) {
    double entropy_val = 0.0;
    for (int i = 0; i < probabilities.size(); ++i) {
        if (probabilities(i) > EPSILON) {
            entropy_val -= probabilities(i) * log2(probabilities(i));
        }
    }
    return entropy_val;
}

inline double kl_divergence(const Eigen::VectorXd& p, const Eigen::VectorXd& q) {
    if (p.size() != q.size()) {
        throw std::invalid_argument("Probability vectors must have the same size");
    }
    
    double kl = 0.0;
    for (int i = 0; i < p.size(); ++i) {
        if (p(i) > EPSILON) {
            // Avoid division by zero or log(0)
            if (q(i) <= EPSILON) {
                return std::numeric_limits<double>::infinity();
            }
            kl += p(i) * log2(p(i) / q(i));
        }
    }
    return kl;
}

inline double jensen_shannon_divergence(const Eigen::VectorXd& p, const Eigen::VectorXd& q) {
    if (p.size() != q.size()) {
        throw std::invalid_argument("Probability vectors must have the same size");
    }
    
    // Compute the midpoint distribution
    Eigen::VectorXd m = 0.5 * (p + q);
    
    // Compute the average KL divergence
    return 0.5 * (kl_divergence(p, m) + kl_divergence(q, m));
}

inline double mutual_information(const Eigen::MatrixXd& joint_probabilities) {
    // Compute marginal distributions
    Eigen::VectorXd p_x = joint_probabilities.rowwise().sum();
    Eigen::VectorXd p_y = joint_probabilities.colwise().sum();
    
    double mi = 0.0;
    for (int i = 0; i < joint_probabilities.rows(); ++i) {
        for (int j = 0; j < joint_probabilities.cols(); ++j) {
            if (joint_probabilities(i, j) > EPSILON) {
                mi += joint_probabilities(i, j) * log2(joint_probabilities(i, j) / (p_x(i) * p_y(j)));
            }
        }
    }
    return mi;
}

} // namespace utils

} // namespace information_theory
} // namespace utils
} // namespace ultra

#endif // ULTRA_UTILS_INFORMATION_THEORY_H