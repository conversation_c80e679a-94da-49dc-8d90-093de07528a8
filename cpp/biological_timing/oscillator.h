/**
 * @file oscillator.h
 * @brief Header file for neural oscillator models in biological timing circuits
 * 
 * This file defines oscillator classes that simulate brain rhythms such as delta,
 * theta, alpha, beta, and gamma waves, with support for coupling mechanisms,
 * phase-amplitude interactions, and other neurophysiologically relevant phenomena.
 * 
 * <AUTHOR> Team
 * @date 2025
 */

#ifndef ULTRA_CORE_NEURAL_BIOLOGICAL_TIMING_OSCILLATOR_H_
#define ULTRA_CORE_NEURAL_BIOLOGICAL_TIMING_OSCILLATOR_H_

#include <vector>
#include <memory>
#include <complex>
#include <cmath>

namespace ultra {
namespace core_neural {
namespace biological_timing {

/**
 * @brief Enumeration of brain frequency bands
 */
enum class FrequencyBand {
    DELTA,  ///< 1-4 Hz, deep sleep, unconscious states
    THETA,  ///< 4-8 Hz, drowsiness, memory, spatial navigation
    ALPHA,  ///< 8-12 Hz, relaxed wakefulness, attention
    BETA,   ///< 13-30 Hz, active thinking, focused attention
    GAMMA   ///< 30-100 Hz, perception binding, conscious awareness
};

/**
 * @brief Enumeration of oscillator model types
 */
enum class OscillatorType {
    VAN_DER_POL,   ///< Van der Pol oscillator model
    KURAMOTO,      ///< <PERSON><PERSON>oto oscillator model
    WILSON_COWAN   ///< Wilson-Cowan neural mass model
};

/**
 * @brief Base class for neural oscillators
 * 
 * Abstract base class defining common interface for various oscillator types
 */
class BaseOscillator {
public:
    /**
     * @brief Constructor
     * 
     * @param frequency Natural frequency in Hz
     * @param amplitude Initial amplitude (0.0-5.0)
     * @param phase Initial phase in radians
     * @param decayRate Natural amplitude decay rate
     */
    BaseOscillator(double frequency, double amplitude = 1.0, 
                   double phase = 0.0, double decayRate = 0.1);
    
    /**
     * @brief Virtual destructor
     */
    virtual ~BaseOscillator();
    
    /**
     * @brief Update oscillator state
     * 
     * @param dt Time step in seconds
     * @param externalDrive External drive input (0.0-1.0)
     */
    void update(double dt, double externalDrive = 0.0);
    
    /**
     * @brief Get current oscillator value
     * 
     * @return Current oscillation amplitude
     */
    virtual double getValue() const;
    
    /**
     * @brief Get current amplitude
     * 
     * @return Current amplitude envelope
     */
    double getAmplitude() const;
    
    /**
     * @brief Get current phase
     * 
     * @return Current phase in radians (0-2π)
     */
    double getPhase() const;
    
    /**
     * @brief Get oscillator frequency
     * 
     * @return Natural frequency in Hz
     */
    double getFrequency() const;
    
    /**
     * @brief Get instantaneous frequency
     * 
     * @return Current instantaneous frequency in Hz
     */
    double getInstantaneousFrequency() const;
    
    /**
     * @brief Get amplitude change rate
     * 
     * @return Current rate of amplitude change
     */
    double getAmplitudeChangeRate() const;
    
    /**
     * @brief Set amplitude envelope
     * 
     * @param amplitude New amplitude value (0.0-5.0)
     */
    void setAmplitude(double amplitude);
    
    /**
     * @brief Set oscillator phase
     * 
     * @param phase New phase in radians
     */
    void setPhase(double phase);
    
    /**
     * @brief Set oscillator frequency
     * 
     * @param frequency New frequency in Hz
     */
    void setFrequency(double frequency);
    
    /**
     * @brief Set decay rate
     * 
     * @param decayRate New decay rate value
     */
    void setDecayRate(double decayRate);
    
    /**
     * @brief Enable oscillator
     */
    void enable();
    
    /**
     * @brief Disable oscillator
     */
    void disable();
    
    /**
     * @brief Check if oscillator is enabled
     * 
     * @return True if enabled, false otherwise
     */
    bool isEnabled() const;
    
    /**
     * @brief Reset history buffers
     */
    void resetHistory();
    
    /**
     * @brief Set maximum history length
     * 
     * @param length New length for history buffers
     */
    void setHistoryLength(size_t length);
    
    /**
     * @brief Get time history
     * 
     * @return Vector of time points
     */
    const std::vector<double>& getTimeHistory() const;
    
    /**
     * @brief Get amplitude history
     * 
     * @return Vector of amplitude values
     */
    const std::vector<double>& getAmplitudeHistory() const;
    
    /**
     * @brief Get phase history
     * 
     * @return Vector of phase values
     */
    const std::vector<double>& getPhaseHistory() const;
    
    /**
     * @brief Get oscillation value history
     * 
     * @return Vector of oscillation values
     */
    const std::vector<double>& getValueHistory() const;
    
    /**
     * @brief Calculate coherence with another oscillator
     * 
     * @param other Other oscillator
     * @return Coherence value (0.0-1.0)
     */
    double getCoherenceWith(const BaseOscillator& other) const;
    
    /**
     * @brief Calculate phase-amplitude coupling within the oscillator
     * 
     * @return Modulation index (0.0-1.0)
     */
    double getPhaseAmplitudeCoupling() const;
    
protected:
    /**
     * @brief Update oscillator-specific dynamics
     * 
     * Virtual method to be implemented by derived classes.
     * 
     * @param dt Time step in seconds
     * @param externalDrive External drive input
     */
    virtual void updateDynamics(double dt, double externalDrive) = 0;
    
    /**
     * @brief Normalize phase to [0, 2π] range
     */
    void normalizePhase();
    
    /**
     * @brief Store current state in history buffers
     */
    void storeStateInHistory();
    
    // Default parameters
    static constexpr double MAX_AMPLITUDE = 5.0;        ///< Maximum allowed amplitude
    static constexpr size_t DEFAULT_HISTORY_LENGTH = 1000; ///< Default history buffer length
    
    // Core state variables
    double frequency_;           ///< Natural frequency in Hz
    double amplitude_;           ///< Current amplitude envelope
    double phase_;               ///< Current phase in radians
    double decayRate_;           ///< Natural amplitude decay rate
    bool enabled_;               ///< Whether oscillator is active
    
    // Derived metrics
    double timeStep_;                ///< Current simulation time
    double instantaneousFrequency_;  ///< Current instantaneous frequency
    double amplitudeChangeRate_;     ///< Current rate of amplitude change
    
    // History buffers
    size_t historyLength_;          ///< Maximum history length
    std::vector<double> timeHistory_;      ///< History of time points
    std::vector<double> amplitudeHistory_; ///< History of amplitude values
    std::vector<double> phaseHistory_;     ///< History of phase values
    std::vector<double> valueHistory_;     ///< History of oscillation values
};

/**
 * @brief Van der Pol oscillator model
 * 
 * Implements a nonlinear oscillator with limit cycle behavior
 */
class VanDerPolOscillator : public BaseOscillator {
public:
    /**
     * @brief Constructor
     * 
     * @param frequency Natural frequency in Hz
     * @param amplitude Initial amplitude
     * @param phase Initial phase in radians
     * @param mu Nonlinearity parameter
     * @param decayRate Natural amplitude decay rate
     */
    VanDerPolOscillator(double frequency, double amplitude = 1.0, 
                        double phase = 0.0, double mu = 1.0,
                        double decayRate = 0.1);
    
    /**
     * @brief Get oscillator value
     * 
     * @return Current position value
     */
    double getValue() const override;
    
    /**
     * @brief Get nonlinearity parameter
     * 
     * @return Current mu value
     */
    double getMu() const;
    
    /**
     * @brief Set nonlinearity parameter
     * 
     * @param mu New mu value
     */
    void setMu(double mu);
    
    /**
     * @brief Get velocity
     * 
     * @return Current velocity value
     */
    double getVelocity() const;
    
    /**
     * @brief Get position
     * 
     * @return Current position value
     */
    double getPosition() const;
    
protected:
    /**
     * @brief Update oscillator dynamics
     * 
     * Implements the Van der Pol oscillator equations
     * 
     * @param dt Time step in seconds
     * @param externalDrive External drive input
     */
    void updateDynamics(double dt, double externalDrive) override;
    
private:
    double mu_;        ///< Nonlinearity parameter
    double position_;  ///< Current position
    double velocity_;  ///< Current velocity
};

/**
 * @brief Kuramoto oscillator model
 * 
 * Implements a phase oscillator with coupling capabilities
 */
class KuramotoOscillator : public BaseOscillator {
public:
    /**
     * @brief Coupling structure
     */
    struct Coupling {
        BaseOscillator* oscillator;  ///< Pointer to coupled oscillator
        double strength;             ///< Coupling strength
    };
    
    /**
     * @brief Constructor
     * 
     * @param frequency Natural frequency in Hz
     * @param amplitude Initial amplitude
     * @param phase Initial phase in radians
     * @param decayRate Natural amplitude decay rate
     */
    KuramotoOscillator(double frequency, double amplitude = 1.0, 
                      double phase = 0.0, double decayRate = 0.1);
    
    /**
     * @brief Add coupling to another oscillator
     * 
     * @param oscillator Pointer to oscillator to couple with
     * @param strength Coupling strength
     */
    void addCoupling(BaseOscillator* oscillator, double strength);
    
    /**
     * @brief Remove coupling to an oscillator
     * 
     * @param oscillator Pointer to oscillator to decouple from
     */
    void removeCoupling(BaseOscillator* oscillator);
    
    /**
     * @brief Update coupling strength to an oscillator
     * 
     * @param oscillator Pointer to oscillator
     * @param strength New coupling strength
     */
    void updateCouplingStrength(BaseOscillator* oscillator, double strength);
    
    /**
     * @brief Get all couplings
     * 
     * @return Vector of couplings
     */
    const std::vector<Coupling>& getCouplings() const;
    
protected:
    /**
     * @brief Update oscillator dynamics
     * 
     * Implements the Kuramoto oscillator equations
     * 
     * @param dt Time step in seconds
     * @param externalDrive External drive input
     */
    void updateDynamics(double dt, double externalDrive) override;
    
private:
    std::vector<Coupling> couplings_;  ///< Couplings to other oscillators
};

/**
 * @brief Wilson-Cowan neural mass oscillator
 * 
 * Implements a neural mass model with excitatory and inhibitory populations
 */
class WilsonCowanOscillator : public BaseOscillator {
public:
    /**
     * @brief Constructor
     * 
     * @param frequency Estimated oscillation frequency
     * @param amplitude Initial amplitude
     * @param phase Initial phase
     * @param excitationStrength Strength of excitatory connections
     * @param inhibitionStrength Strength of inhibitory connections
     * @param decayRate Natural amplitude decay rate
     */
    WilsonCowanOscillator(double frequency, double amplitude = 1.0, 
                         double phase = 0.0, double excitationStrength = 1.2,
                         double inhibitionStrength = 1.0, double decayRate = 0.1);
    
    /**
     * @brief Get oscillator value
     * 
     * @return Current excitation level
     */
    double getValue() const override;
    
    /**
     * @brief Get excitation level
     * 
     * @return Current excitatory population activity
     */
    double getExcitation() const;
    
    /**
     * @brief Get inhibition level
     * 
     * @return Current inhibitory population activity
     */
    double getInhibition() const;
    
    /**
     * @brief Set excitation level
     * 
     * @param excitation New excitation level (0.0-1.0)
     */
    void setExcitation(double excitation);
    
    /**
     * @brief Set inhibition level
     * 
     * @param inhibition New inhibition level (0.0-1.0)
     */
    void setInhibition(double inhibition);
    
    /**
     * @brief Get excitation strength
     * 
     * @return Current excitatory connection strength
     */
    double getExcitationStrength() const;
    
    /**
     * @brief Get inhibition strength
     * 
     * @return Current inhibitory connection strength
     */
    double getInhibitionStrength() const;
    
    /**
     * @brief Set excitation strength
     * 
     * @param strength New excitatory connection strength
     */
    void setExcitationStrength(double strength);
    
    /**
     * @brief Set inhibition strength
     * 
     * @param strength New inhibitory connection strength
     */
    void setInhibitionStrength(double strength);
    
    /**
     * @brief Set time constants
     * 
     * @param excitationTC Excitatory population time constant
     * @param inhibitionTC Inhibitory population time constant
     */
    void setTimeConstants(double excitationTC, double inhibitionTC);
    
    /**
     * @brief Set sigmoid slopes
     * 
     * @param excitationSlope Excitatory sigmoid slope
     * @param inhibitionSlope Inhibitory sigmoid slope
     */
    void setSigmoidSlopes(double excitationSlope, double inhibitionSlope);
    
protected:
    /**
     * @brief Update oscillator dynamics
     * 
     * Implements the Wilson-Cowan neural mass model equations
     * 
     * @param dt Time step in seconds
     * @param externalDrive External drive input
     */
    void updateDynamics(double dt, double externalDrive) override;
    
private:
    double excitationStrength_;   ///< Strength of excitatory connections
    double inhibitionStrength_;   ///< Strength of inhibitory connections
    double excitation_;           ///< Current excitatory population activity
    double inhibition_;           ///< Current inhibitory population activity
    double timeConstantE_;        ///< Excitatory population time constant
    double timeConstantI_;        ///< Inhibitory population time constant
    double sigmoidSlopeE_;        ///< Excitatory sigmoid slope
    double sigmoidSlopeI_;        ///< Inhibitory sigmoid slope
};

/**
 * @brief Brain wave oscillator
 * 
 * Specialized oscillator that simulates realistic brain rhythms in different frequency bands
 */
class BrainWaveOscillator : public BaseOscillator {
public:
    /**
     * @brief Constructor
     * 
     * @param band Frequency band (delta, theta, alpha, beta, gamma)
     * @param amplitude Initial amplitude
     * @param phase Initial phase in radians
     * @param decayRate Natural amplitude decay rate
     */
    BrainWaveOscillator(FrequencyBand band, double amplitude = 1.0, 
                       double phase = 0.0, double decayRate = 0.1);
    
    /**
     * @brief Get oscillator value
     * 
     * @return Current value with realistic brain wave characteristics
     */
    double getValue() const override;
    
    /**
     * @brief Get frequency band
     * 
     * @return Current frequency band
     */
    FrequencyBand getBand() const;
    
    /**
     * @brief Set bandwidth factor
     * 
     * @param factor New bandwidth factor (0.0-0.5)
     */
    void setBandwidthFactor(double factor);
    
    /**
     * @brief Get bandwidth factor
     * 
     * @return Current bandwidth factor
     */
    double getBandwidthFactor() const;
    
    /**
     * @brief Set harmonic strength
     * 
     * @param strength New harmonic strength (0.0-1.0)
     */
    void setHarmonicStrength(double strength);
    
    /**
     * @brief Get harmonic strength
     * 
     * @return Current harmonic strength
     */
    double getHarmonicStrength() const;
    
    /**
     * @brief Set irregularity factor
     * 
     * @param factor New irregularity factor (0.0-0.5)
     */
    void setIrregularityFactor(double factor);
    
    /**
     * @brief Get irregularity factor
     * 
     * @return Current irregularity factor
     */
    double getIrregularityFactor() const;
    
    /**
     * @brief Get default frequency for a given band
     * 
     * @param band Frequency band
     * @return Default frequency in Hz
     */
    static double getDefaultFrequency(FrequencyBand band);
    
protected:
    /**
     * @brief Update oscillator dynamics
     * 
     * Implements realistic brain wave dynamics with frequency band properties
     * 
     * @param dt Time step in seconds
     * @param externalDrive External drive input
     */
    void updateDynamics(double dt, double externalDrive) override;
    
private:
    FrequencyBand band_;             ///< Frequency band
    double bandwidthFactor_;         ///< Bandwidth as fraction of center frequency
    double harmonicStrength_;        ///< Strength of harmonics
    double irregularityFactor_;      ///< Amount of physiological irregularity
};

/**
 * @brief Factory class for creating oscillators
 */
class OscillatorFactory {
public:
    /**
     * @brief Create an oscillator of the specified type
     * 
     * @param type Oscillator model type
     * @param frequency Natural frequency in Hz
     * @param amplitude Initial amplitude
     * @param phase Initial phase in radians
     * @param decayRate Natural amplitude decay rate
     * @return Unique pointer to created oscillator
     */
    static std::unique_ptr<BaseOscillator> createOscillator(
        OscillatorType type, 
        double frequency,
        double amplitude = 1.0,
        double phase = 0.0,
        double decayRate = 0.1);
    
    /**
     * @brief Create a brain wave oscillator for the specified band
     * 
     * @param band Frequency band
     * @param amplitude Initial amplitude
     * @param phase Initial phase in radians
     * @param decayRate Natural amplitude decay rate
     * @return Unique pointer to created brain wave oscillator
     */
    static std::unique_ptr<BrainWaveOscillator> createBrainWaveOscillator(
        FrequencyBand band,
        double amplitude = 1.0,
        double phase = 0.0,
        double decayRate = 0.1);
};

} // namespace biological_timing
} // namespace core_neural
} // namespace ultra

#endif // ULTRA_CORE_NEURAL_BIOLOGICAL_TIMING_OSCILLATOR_H_