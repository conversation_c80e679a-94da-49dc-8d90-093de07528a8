/**
 * @file biological_timing.cpp
 * @brief Implementation of the Biological Timing Circuits component of the ULTRA system
 * 
 * This file implements oscillatory dynamics inspired by neural oscillations 
 * in the brain such as theta, alpha, beta, and gamma rhythms. These oscillations
 * coordinate processing across different brain regions and are involved in 
 * functions such as attention, memory encoding, and multi-regional communication.
 * 
 * <AUTHOR> Team
 * @date 2025
 */

#include "biological_timing.h"
#include <algorithm>
#include <cmath>
#include <numeric>
#include <random>
#include <vector>
#include <Eigen/Dense>
#include <Eigen/Sparse>
#include <chrono>
#include <functional>
#include <unordered_map>
#include <memory>
#include <iostream>

namespace ultra {
namespace core_neural {

// -----------------------------------------------------------------------------
// Helper Functions
// -----------------------------------------------------------------------------

/**
 * @brief Generate white noise for oscillatory dynamics
 * 
 * @param mean Mean of the noise distribution
 * @param stddev Standard deviation of the noise distribution
 * @return Noise value sampled from normal distribution
 */
double generateWhiteNoise(double mean = 0.0, double stddev = 1.0) {
    static std::random_device rd;
    static std::mt19937 gen(rd());
    static std::normal_distribution<double> dist(mean, stddev);
    
    return dist(gen);
}

/**
 * @brief Calculate phase difference between two phases (returns value between -π and π)
 * 
 * @param phase1 First phase value in radians
 * @param phase2 Second phase value in radians
 * @return Phase difference in radians, normalized to [-π, π]
 */
double phaseDifference(double phase1, double phase2) {
    double diff = phase1 - phase2;
    
    // Normalize to [-π, π]
    while (diff > M_PI) diff -= 2.0 * M_PI;
    while (diff < -M_PI) diff += 2.0 * M_PI;
    
    return diff;
}

/**
 * @brief Hilbert transform to compute analytic signal (amplitude and phase)
 * Used to extract instantaneous amplitude and phase from oscillatory signals
 * 
 * @param signal Input signal to transform
 * @param amplitudes Output vector for instantaneous amplitudes
 * @param phases Output vector for instantaneous phases
 */
void hilbertTransform(const std::vector<double>& signal, 
                      std::vector<double>& amplitudes,
                      std::vector<double>& phases) {
    // Implementation using Fast Fourier Transform
    size_t n = signal.size();
    amplitudes.resize(n);
    phases.resize(n);
    
    // FFT of the input signal
    Eigen::FFT<double> fft;
    std::vector<std::complex<double>> freqDomain;
    fft.fwd(freqDomain, signal);
    
    // Create analytic signal in frequency domain
    std::vector<std::complex<double>> analyticFreq(n);
    for (size_t i = 0; i < n; i++) {
        if (i == 0 || i == n / 2) {
            analyticFreq[i] = freqDomain[i];
        } else if (i < n / 2) {
            analyticFreq[i] = 2.0 * freqDomain[i];
        } else {
            analyticFreq[i] = 0.0;
        }
    }
    
    // Convert back to time domain
    std::vector<std::complex<double>> analyticSignal;
    fft.inv(analyticSignal, analyticFreq);
    
    // Extract amplitude and phase
    for (size_t i = 0; i < n; i++) {
        amplitudes[i] = std::abs(analyticSignal[i]);
        phases[i] = std::arg(analyticSignal[i]);
    }
}

// -----------------------------------------------------------------------------
// NeuralOscillator Implementation
// -----------------------------------------------------------------------------

NeuralOscillator::NeuralOscillator(FrequencyBand band, 
                                   double baseFrequency, 
                                   double amplitude, 
                                   double phase,
                                   double decayRate)
    : band_(band), 
      baseFrequency_(baseFrequency), 
      amplitude_(amplitude), 
      phase_(phase),
      decayRate_(decayRate),
      currentFrequency_(baseFrequency),
      enabled_(true) {
    
    // Initialize history buffers
    amplitudeHistory_.reserve(HISTORY_SIZE);
    phaseHistory_.reserve(HISTORY_SIZE);
    timeHistory_.reserve(HISTORY_SIZE);
    
    // Record initial state
    amplitudeHistory_.push_back(amplitude);
    phaseHistory_.push_back(phase);
    timeHistory_.push_back(0.0);
    
    // Initialize coupled oscillators
    coupledOscillators_.clear();
}

void NeuralOscillator::update(double dt, double externalDrive) {
    if (!enabled_) return;
    
    // Update phase based on frequency
    phase_ += 2.0 * M_PI * currentFrequency_ * dt;
    
    // Normalize phase to [0, 2π]
    while (phase_ >= 2.0 * M_PI) phase_ -= 2.0 * M_PI;
    while (phase_ < 0.0) phase_ += 2.0 * M_PI;
    
    // Calculate contributions from coupled oscillators
    double couplingContribution = 0.0;
    for (const auto& coupling : coupledOscillators_) {
        NeuralOscillator* coupled = coupling.oscillator;
        double strength = coupling.strength;
        double phaseDiff = phaseDifference(coupled->phase_, phase_);
        
        couplingContribution += strength * std::sin(phaseDiff);
    }
    
    // Apply amplitude dynamics based on coupled oscillators, external drive, and decay
    double dA = -decayRate_ * amplitude_ + externalDrive + couplingContribution;
    amplitude_ += dA * dt;
    
    // Apply random fluctuations (stochastic dynamics)
    double noise = generateWhiteNoise(0.0, 0.01);
    amplitude_ += noise * dt;
    
    // Ensure amplitude stays positive and within reasonable bounds
    amplitude_ = std::max(0.0, std::min(amplitude_, MAX_AMPLITUDE));
    
    // Store values in history buffers (with circular buffer behavior)
    if (amplitudeHistory_.size() >= HISTORY_SIZE) {
        amplitudeHistory_.erase(amplitudeHistory_.begin());
        phaseHistory_.erase(phaseHistory_.begin());
        timeHistory_.erase(timeHistory_.begin());
    }
    
    amplitudeHistory_.push_back(amplitude_);
    phaseHistory_.push_back(phase_);
    timeHistory_.push_back(timeHistory_.back() + dt);
}

double NeuralOscillator::getValue(double currentTime) const {
    return amplitude_ * std::sin(phase_);
}

double NeuralOscillator::getAmplitude() const {
    return amplitude_;
}

double NeuralOscillator::getPhase() const {
    return phase_;
}

void NeuralOscillator::setPhase(double phase) {
    phase_ = phase;
    while (phase_ >= 2.0 * M_PI) phase_ -= 2.0 * M_PI;
    while (phase_ < 0.0) phase_ += 2.0 * M_PI;
}

void NeuralOscillator::setAmplitude(double amplitude) {
    amplitude_ = std::max(0.0, std::min(amplitude, MAX_AMPLITUDE));
}

void NeuralOscillator::setFrequency(double frequency) {
    currentFrequency_ = frequency;
}

void NeuralOscillator::modulateFrequency(double modulationFactor) {
    currentFrequency_ = baseFrequency_ * modulationFactor;
}

void NeuralOscillator::resetToBaseFrequency() {
    currentFrequency_ = baseFrequency_;
}

void NeuralOscillator::enable() {
    enabled_ = true;
}

void NeuralOscillator::disable() {
    enabled_ = false;
}

bool NeuralOscillator::isEnabled() const {
    return enabled_;
}

void NeuralOscillator::addCoupling(NeuralOscillator* oscillator, double strength) {
    // Check if oscillator is already coupled
    for (auto& coupling : coupledOscillators_) {
        if (coupling.oscillator == oscillator) {
            coupling.strength = strength; // Update strength if already exists
            return;
        }
    }
    
    // Add new coupling
    OscillatorCoupling coupling;
    coupling.oscillator = oscillator;
    coupling.strength = strength;
    coupledOscillators_.push_back(coupling);
}

void NeuralOscillator::removeCoupling(NeuralOscillator* oscillator) {
    auto it = std::remove_if(coupledOscillators_.begin(), coupledOscillators_.end(),
                            [oscillator](const OscillatorCoupling& coupling) {
                                return coupling.oscillator == oscillator;
                            });
    
    if (it != coupledOscillators_.end()) {
        coupledOscillators_.erase(it, coupledOscillators_.end());
    }
}

void NeuralOscillator::updateCouplingStrength(NeuralOscillator* oscillator, double strength) {
    for (auto& coupling : coupledOscillators_) {
        if (coupling.oscillator == oscillator) {
            coupling.strength = strength;
            return;
        }
    }
}

std::vector<double> NeuralOscillator::getAmplitudeHistory() const {
    return amplitudeHistory_;
}

std::vector<double> NeuralOscillator::getPhaseHistory() const {
    return phaseHistory_;
}

std::vector<double> NeuralOscillator::getTimeHistory() const {
    return timeHistory_;
}

FrequencyBand NeuralOscillator::getBand() const {
    return band_;
}

double NeuralOscillator::getCoherenceWith(const NeuralOscillator& other) const {
    // Calculate phase coherence between two oscillators over recent history
    
    if (phaseHistory_.size() < 10 || other.phaseHistory_.size() < 10) {
        return 0.0; // Not enough history to calculate coherence
    }
    
    // Use only the overlapping time period
    size_t n = std::min(phaseHistory_.size(), other.phaseHistory_.size());
    
    // Calculate mean resultant length (MRL) as a measure of phase coherence
    double sumSin = 0.0;
    double sumCos = 0.0;
    
    for (size_t i = 0; i < n; i++) {
        double phaseDiff = phaseDifference(phaseHistory_[phaseHistory_.size() - 1 - i], 
                                          other.phaseHistory_[other.phaseHistory_.size() - 1 - i]);
        sumSin += std::sin(phaseDiff);
        sumCos += std::cos(phaseDiff);
    }
    
    // Mean resultant length (MRL) is the magnitude of the mean phase difference vector
    double mrl = std::sqrt(sumSin * sumSin + sumCos * sumCos) / static_cast<double>(n);
    
    return mrl; // Value between 0 (no coherence) and 1 (perfect coherence)
}

// -----------------------------------------------------------------------------
// OscillatorEnsemble Implementation
// -----------------------------------------------------------------------------

OscillatorEnsemble::OscillatorEnsemble() {
    // Create oscillators for each frequency band with appropriate parameters
    oscillators_[FrequencyBand::DELTA] = std::make_unique<NeuralOscillator>(
        FrequencyBand::DELTA, 2.0, 1.0, 0.0, 0.1);  // 1-4 Hz
    
    oscillators_[FrequencyBand::THETA] = std::make_unique<NeuralOscillator>(
        FrequencyBand::THETA, 6.0, 1.0, 0.0, 0.15); // 4-8 Hz
    
    oscillators_[FrequencyBand::ALPHA] = std::make_unique<NeuralOscillator>(
        FrequencyBand::ALPHA, 10.0, 1.0, 0.0, 0.2); // 8-12 Hz
    
    oscillators_[FrequencyBand::BETA] = std::make_unique<NeuralOscillator>(
        FrequencyBand::BETA, 20.0, 0.8, 0.0, 0.3);  // 12-30 Hz
    
    oscillators_[FrequencyBand::GAMMA] = std::make_unique<NeuralOscillator>(
        FrequencyBand::GAMMA, 40.0, 0.6, 0.0, 0.4); // 30-100 Hz
    
    // Default cross-frequency coupling setup
    setupDefaultCoupling();
}

void OscillatorEnsemble::setupDefaultCoupling() {
    // Set up cross-frequency coupling based on neuroscientific findings
    
    // Theta-Gamma coupling (Theta phase modulates Gamma amplitude)
    // This is one of the most well-established forms of cross-frequency coupling in the brain
    oscillators_[FrequencyBand::THETA]->addCoupling(
        oscillators_[FrequencyBand::GAMMA].get(), 0.3);
    
    // Alpha-Beta coupling
    oscillators_[FrequencyBand::ALPHA]->addCoupling(
        oscillators_[FrequencyBand::BETA].get(), 0.2);
    
    // Delta-Theta coupling
    oscillators_[FrequencyBand::DELTA]->addCoupling(
        oscillators_[FrequencyBand::THETA].get(), 0.15);
    
    // Other bidirectional couplings with smaller strengths
    oscillators_[FrequencyBand::THETA]->addCoupling(
        oscillators_[FrequencyBand::BETA].get(), 0.1);
    
    oscillators_[FrequencyBand::ALPHA]->addCoupling(
        oscillators_[FrequencyBand::GAMMA].get(), 0.1);
}

void OscillatorEnsemble::update(double dt, 
                               const std::unordered_map<FrequencyBand, double>& externalDrives) {
    // Update each oscillator with appropriate external drive
    for (const auto& oscillatorPair : oscillators_) {
        double drive = 0.0;
        auto it = externalDrives.find(oscillatorPair.first);
        if (it != externalDrives.end()) {
            drive = it->second;
        }
        
        oscillatorPair.second->update(dt, drive);
    }
    
    // Apply phase-amplitude coupling (PAC)
    applyPhaseAmplitudeCoupling();
}

void OscillatorEnsemble::applyPhaseAmplitudeCoupling() {
    // Apply phase-amplitude coupling based on current oscillator states
    
    // Theta phase modulates Gamma amplitude
    // (one of the most well-established PAC relationships in neuroscience)
    if (oscillators_[FrequencyBand::THETA] && oscillators_[FrequencyBand::GAMMA]) {
        double thetaPhase = oscillators_[FrequencyBand::THETA]->getPhase();
        double gammaAmp = oscillators_[FrequencyBand::GAMMA]->getAmplitude();
        
        // Modulate gamma amplitude based on theta phase
        // Maximum gamma amplitude at theta phase of π/2 (cosine peak)
        double modulationFactor = 0.5 * (1.0 + std::cos(thetaPhase));
        double newGammaAmp = gammaAmp * (0.5 + 0.5 * modulationFactor);
        
        oscillators_[FrequencyBand::GAMMA]->setAmplitude(newGammaAmp);
    }
    
    // Delta phase modulates theta amplitude
    if (oscillators_[FrequencyBand::DELTA] && oscillators_[FrequencyBand::THETA]) {
        double deltaPhase = oscillators_[FrequencyBand::DELTA]->getPhase();
        double thetaAmp = oscillators_[FrequencyBand::THETA]->getAmplitude();
        
        double modulationFactor = 0.3 * (1.0 + std::cos(deltaPhase));
        double newThetaAmp = thetaAmp * (0.7 + modulationFactor);
        
        oscillators_[FrequencyBand::THETA]->setAmplitude(newThetaAmp);
    }
    
    // Alpha phase modulates beta amplitude
    if (oscillators_[FrequencyBand::ALPHA] && oscillators_[FrequencyBand::BETA]) {
        double alphaPhase = oscillators_[FrequencyBand::ALPHA]->getPhase();
        double betaAmp = oscillators_[FrequencyBand::BETA]->getAmplitude();
        
        double modulationFactor = 0.2 * (1.0 + std::cos(alphaPhase));
        double newBetaAmp = betaAmp * (0.8 + modulationFactor);
        
        oscillators_[FrequencyBand::BETA]->setAmplitude(newBetaAmp);
    }
}

std::unordered_map<FrequencyBand, double> OscillatorEnsemble::getCurrentValues() const {
    std::unordered_map<FrequencyBand, double> values;
    for (const auto& oscillatorPair : oscillators_) {
        values[oscillatorPair.first] = oscillatorPair.second->getValue(0.0);
    }
    return values;
}

std::unordered_map<FrequencyBand, double> OscillatorEnsemble::getCurrentAmplitudes() const {
    std::unordered_map<FrequencyBand, double> amplitudes;
    for (const auto& oscillatorPair : oscillators_) {
        amplitudes[oscillatorPair.first] = oscillatorPair.second->getAmplitude();
    }
    return amplitudes;
}

std::unordered_map<FrequencyBand, double> OscillatorEnsemble::getCurrentPhases() const {
    std::unordered_map<FrequencyBand, double> phases;
    for (const auto& oscillatorPair : oscillators_) {
        phases[oscillatorPair.first] = oscillatorPair.second->getPhase();
    }
    return phases;
}

NeuralOscillator* OscillatorEnsemble::getOscillator(FrequencyBand band) {
    auto it = oscillators_.find(band);
    if (it != oscillators_.end()) {
        return it->second.get();
    }
    return nullptr;
}

void OscillatorEnsemble::enableOscillator(FrequencyBand band) {
    auto it = oscillators_.find(band);
    if (it != oscillators_.end()) {
        it->second->enable();
    }
}

void OscillatorEnsemble::disableOscillator(FrequencyBand band) {
    auto it = oscillators_.find(band);
    if (it != oscillators_.end()) {
        it->second->disable();
    }
}

void OscillatorEnsemble::setCoupling(FrequencyBand sourceBand, 
                                    FrequencyBand targetBand, 
                                    double strength) {
    auto sourceIt = oscillators_.find(sourceBand);
    auto targetIt = oscillators_.find(targetBand);
    
    if (sourceIt != oscillators_.end() && targetIt != oscillators_.end()) {
        sourceIt->second->addCoupling(targetIt->second.get(), strength);
    }
}

double OscillatorEnsemble::getCoherence(FrequencyBand band1, FrequencyBand band2) const {
    auto it1 = oscillators_.find(band1);
    auto it2 = oscillators_.find(band2);
    
    if (it1 != oscillators_.end() && it2 != oscillators_.end()) {
        return it1->second->getCoherenceWith(*(it2->second));
    }
    
    return 0.0;
}

// -----------------------------------------------------------------------------
// BiologicalTimingCircuits Implementation
// -----------------------------------------------------------------------------

BiologicalTimingCircuits::BiologicalTimingCircuits()
    : time_(0.0),
      lastUpdateTime_(0.0) {
    
    // Initialize regions with oscillator ensembles
    initializeRegions();
    
    // Set up default inter-regional coherence connections
    setupDefaultCoherenceConnections();
}

void BiologicalTimingCircuits::initializeRegions() {
    // Create oscillator ensembles for different brain-inspired regions
    regions_["prefrontal"] = std::make_unique<OscillatorEnsemble>();
    regions_["temporal"] = std::make_unique<OscillatorEnsemble>();
    regions_["parietal"] = std::make_unique<OscillatorEnsemble>();
    regions_["occipital"] = std::make_unique<OscillatorEnsemble>();
    regions_["hippocampal"] = std::make_unique<OscillatorEnsemble>();
    regions_["thalamic"] = std::make_unique<OscillatorEnsemble>();
    
    // Set up region-specific oscillatory characteristics
    
    // Prefrontal: Strong theta and beta for working memory and executive function
    regions_["prefrontal"]->getOscillator(FrequencyBand::THETA)->setAmplitude(1.2);
    regions_["prefrontal"]->getOscillator(FrequencyBand::BETA)->setAmplitude(1.1);
    
    // Temporal: Strong theta and alpha for memory processing
    regions_["temporal"]->getOscillator(FrequencyBand::THETA)->setAmplitude(1.1);
    regions_["temporal"]->getOscillator(FrequencyBand::ALPHA)->setAmplitude(1.2);
    
    // Parietal: Balanced across frequencies for sensory integration
    // (Uses default amplitudes)
    
    // Occipital: Strong alpha and gamma for visual processing
    regions_["occipital"]->getOscillator(FrequencyBand::ALPHA)->setAmplitude(1.3);
    regions_["occipital"]->getOscillator(FrequencyBand::GAMMA)->setAmplitude(1.0);
    
    // Hippocampal: Strong theta for memory formation
    regions_["hippocampal"]->getOscillator(FrequencyBand::THETA)->setAmplitude(1.4);
    regions_["hippocampal"]->getOscillator(FrequencyBand::GAMMA)->setAmplitude(1.1);
    
    // Thalamic: Rhythmic delta and alpha for sensory gating
    regions_["thalamic"]->getOscillator(FrequencyBand::DELTA)->setAmplitude(1.2);
    regions_["thalamic"]->getOscillator(FrequencyBand::ALPHA)->setAmplitude(1.1);
}

void BiologicalTimingCircuits::setupDefaultCoherenceConnections() {
    // Set up biologically-inspired coherence connections between regions
    // Based on known patterns of neural synchronization in the brain
    
    // Prefrontal-hippocampal theta synchronization (critical for memory and planning)
    coherenceConnections_.push_back({
        "prefrontal", "hippocampal", FrequencyBand::THETA, 0.4
    });
    
    // Prefrontal-parietal beta synchronization (attention and working memory)
    coherenceConnections_.push_back({
        "prefrontal", "parietal", FrequencyBand::BETA, 0.3
    });
    
    // Thalamic-occipital alpha synchronization (visual attention)
    coherenceConnections_.push_back({
        "thalamic", "occipital", FrequencyBand::ALPHA, 0.5
    });
    
    // Hippocampal-temporal theta synchronization (memory formation)
    coherenceConnections_.push_back({
        "hippocampal", "temporal", FrequencyBand::THETA, 0.4
    });
    
    // Prefrontal-temporal gamma synchronization (language and executive control)
    coherenceConnections_.push_back({
        "prefrontal", "temporal", FrequencyBand::GAMMA, 0.25
    });
    
    // Parietal-occipital gamma synchronization (visual-spatial integration)
    coherenceConnections_.push_back({
        "parietal", "occipital", FrequencyBand::GAMMA, 0.3
    });
}

void BiologicalTimingCircuits::update(double dt) {
    // Update time
    time_ += dt;
    double elapsed = time_ - lastUpdateTime_;
    lastUpdateTime_ = time_;
    
    // 1. Update each region's oscillator ensemble with appropriate external drives
    for (const auto& regionPair : regions_) {
        // Get region-specific drive values (could be affected by neuromodulators or sensory input)
        std::unordered_map<FrequencyBand, double> externalDrives = 
            getExternalDrives(regionPair.first);
        
        // Update oscillator ensemble
        regionPair.second->update(dt, externalDrives);
    }
    
    // 2. Apply cross-regional coherence (similar to how brain regions synchronize)
    applyCoherenceConnections(dt);
    
    // 3. Calculate coherence metrics across the system
    updateCoherenceMetrics();
}

std::unordered_map<FrequencyBand, double> BiologicalTimingCircuits::getExternalDrives(
    const std::string& region) const {
    
    std::unordered_map<FrequencyBand, double> drives;
    
    // Set default low baseline drive for all frequency bands
    drives[FrequencyBand::DELTA] = 0.05;
    drives[FrequencyBand::THETA] = 0.05;
    drives[FrequencyBand::ALPHA] = 0.05;
    drives[FrequencyBand::BETA] = 0.05;
    drives[FrequencyBand::GAMMA] = 0.05;
    
    // Apply region-specific drives based on hypothetical inputs
    // In a full implementation, these would come from the neuromodulation system
    // or other external components
    
    if (region == "prefrontal") {
        // Prefrontal cortex often shows strong theta and beta rhythms during 
        // executive function and working memory tasks
        drives[FrequencyBand::THETA] += 0.2;
        drives[FrequencyBand::BETA] += 0.3;
    }
    else if (region == "hippocampal") {
        // Hippocampus shows prominent theta rhythms, especially during 
        // memory formation and spatial navigation
        drives[FrequencyBand::THETA] += 0.4;
    }
    else if (region == "occipital") {
        // Visual cortex often shows alpha rhythms, especially when not processing 
        // visual input, and gamma during active visual processing
        drives[FrequencyBand::ALPHA] += 0.3;
        drives[FrequencyBand::GAMMA] += 0.2;
    }
    else if (region == "thalamic") {
        // Thalamus often generates delta rhythms during sleep/unconsciousness 
        // and alpha rhythms for sensory gating
        drives[FrequencyBand::DELTA] += 0.25;
        drives[FrequencyBand::ALPHA] += 0.2;
    }
    
    return drives;
}

void BiologicalTimingCircuits::applyCoherenceConnections(double dt) {
    // Apply the influence of coherence connections
    // This models how oscillations in one brain region can influence oscillations in another
    
    for (const auto& connection : coherenceConnections_) {
        auto sourceRegionIt = regions_.find(connection.sourceRegion);
        auto targetRegionIt = regions_.find(connection.targetRegion);
        
        if (sourceRegionIt != regions_.end() && targetRegionIt != regions_.end()) {
            // Get source and target oscillators
            NeuralOscillator* sourceOsc = 
                sourceRegionIt->second->getOscillator(connection.band);
            
            NeuralOscillator* targetOsc = 
                targetRegionIt->second->getOscillator(connection.band);
            
            if (sourceOsc && targetOsc) {
                // Calculate phase difference
                double sourcePh = sourceOsc->getPhase();
                double targetPh = targetOsc->getPhase();
                double phaseDiff = phaseDifference(sourcePh, targetPh);
                
                // Calculate how much to adjust the target phase to move toward the source phase
                double phaseAdjustment = connection.strength * std::sin(phaseDiff) * dt;
                
                // Apply adjustment to target phase
                targetOsc->setPhase(targetPh + phaseAdjustment);
            }
        }
    }
}

void BiologicalTimingCircuits::updateCoherenceMetrics() {
    // Calculate and store coherence metrics between regions for each frequency band
    
    for (const auto& connection : coherenceConnections_) {
        auto sourceRegionIt = regions_.find(connection.sourceRegion);
        auto targetRegionIt = regions_.find(connection.targetRegion);
        
        if (sourceRegionIt != regions_.end() && targetRegionIt != regions_.end()) {
            // Calculate coherence for this connection's band
            double coherence = 
                sourceRegionIt->second->getCoherence(connection.band, connection.band);
            
            // Store the coherence metric
            std::string key = connection.sourceRegion + ":" + 
                              connection.targetRegion + ":" + 
                              std::to_string(static_cast<int>(connection.band));
            
            coherenceMetrics_[key] = coherence;
        }
    }
}

void BiologicalTimingCircuits::addCoherenceConnection(const std::string& sourceRegion, 
                                                     const std::string& targetRegion,
                                                     FrequencyBand band, 
                                                     double strength) {
    // Add new coherence connection
    CoherenceConnection connection;
    connection.sourceRegion = sourceRegion;
    connection.targetRegion = targetRegion;
    connection.band = band;
    connection.strength = strength;
    
    coherenceConnections_.push_back(connection);
}

void BiologicalTimingCircuits::removeCoherenceConnection(const std::string& sourceRegion, 
                                                        const std::string& targetRegion,
                                                        FrequencyBand band) {
    // Remove matching coherence connections
    coherenceConnections_.erase(
        std::remove_if(coherenceConnections_.begin(), coherenceConnections_.end(),
                      [&](const CoherenceConnection& conn) {
                          return conn.sourceRegion == sourceRegion &&
                                 conn.targetRegion == targetRegion &&
                                 conn.band == band;
                      }),
        coherenceConnections_.end());
}

double BiologicalTimingCircuits::getCoherence(const std::string& sourceRegion,
                                            const std::string& targetRegion,
                                            FrequencyBand band) const {
    std::string key = sourceRegion + ":" + 
                      targetRegion + ":" + 
                      std::to_string(static_cast<int>(band));
    
    auto it = coherenceMetrics_.find(key);
    if (it != coherenceMetrics_.end()) {
        return it->second;
    }
    
    return 0.0;
}

void BiologicalTimingCircuits::modulateFrequency(const std::string& region, 
                                               FrequencyBand band, 
                                               double modulationFactor) {
    auto regionIt = regions_.find(region);
    if (regionIt != regions_.end()) {
        NeuralOscillator* osc = regionIt->second->getOscillator(band);
        if (osc) {
            osc->modulateFrequency(modulationFactor);
        }
    }
}

void BiologicalTimingCircuits::modifyAmplitude(const std::string& region, 
                                             FrequencyBand band, 
                                             double newAmplitude) {
    auto regionIt = regions_.find(region);
    if (regionIt != regions_.end()) {
        NeuralOscillator* osc = regionIt->second->getOscillator(band);
        if (osc) {
            osc->setAmplitude(newAmplitude);
        }
    }
}

double BiologicalTimingCircuits::modulatePhaseBasedPlasticity(double stdpValue, 
                                                            const std::string& region, 
                                                            FrequencyBand band) {
    // Modulate synaptic plasticity based on the phase of an oscillation
    // This implements the equation: Δw_{ij} = STDP(Δt) · (1 + β · cos(φ_f(t)))
    
    double modulation = 1.0; // Default: no modulation
    
    auto regionIt = regions_.find(region);
    if (regionIt != regions_.end()) {
        NeuralOscillator* osc = regionIt->second->getOscillator(band);
        if (osc) {
            // Get current phase of the oscillator
            double phase = osc->getPhase();
            
            // Calculate modulation factor
            // Beta controls the strength of modulation (chosen based on physiological data)
            double beta = 0.5;
            modulation = 1.0 + beta * std::cos(phase);
        }
    }
    
    // Apply modulation to STDP value
    return stdpValue * modulation;
}

double BiologicalTimingCircuits::calculateCoherenceBasedConnectivity(
    const std::string& sourceRegion, 
    const std::string& targetRegion, 
    double baseConnectivity) {
    
    // Implements the equation: E_{ij} = E_{ij}^0 · (1 + γ · cos(φ_i(t) - φ_j(t)))
    // This models how coherence between regions affects effective connectivity
    
    double modulation = 1.0; // Default: no modulation
    
    // Check if both regions exist
    auto sourceIt = regions_.find(sourceRegion);
    auto targetIt = regions_.find(targetRegion);
    
    if (sourceIt != regions_.end() && targetIt != regions_.end()) {
        // Calculate modulation based on theta coherence (most influential for connectivity)
        double coherence = getCoherence(sourceRegion, targetRegion, FrequencyBand::THETA);
        
        // The parameter gamma controls how strongly coherence affects connectivity
        double gamma = 0.6;
        
        // More coherent regions have stronger connectivity
        modulation = 1.0 + gamma * coherence;
    }
    
    // Apply modulation to base connectivity
    return baseConnectivity * modulation;
}

std::vector<double> BiologicalTimingCircuits::getMultiplexedInput(const std::string& region, 
                                                                double baseInput) {
    // Implements multiplexing where different frequency bands carry different information
    // Formula: I_i(t) = ∑_f I_i^f(t) · sin(ω_f t + φ_f)
    
    std::vector<double> multiplexedInput(5, 0.0); // One value per frequency band
    
    auto regionIt = regions_.find(region);
    if (regionIt != regions_.end()) {
        // Get current oscillator values for each frequency band
        auto values = regionIt->second->getCurrentValues();
        
        // For each frequency band, modulate a component of the input
        int i = 0;
        for (const auto& bandValuePair : values) {
            // The oscillation value acts as a carrier for band-specific information
            multiplexedInput[i] = baseInput * (0.8 + 0.2 * bandValuePair.second);
            i++;
        }
    }
    else {
        // If region not found, return uniform input across bands
        std::fill(multiplexedInput.begin(), multiplexedInput.end(), baseInput);
    }
    
    return multiplexedInput;
}

void BiologicalTimingCircuits::applyNeuromodulationEffect(
    const std::string& neuromodulator, double level) {
    
    // Apply effects of neuromodulators on biological timing circuits
    // This models how neurotransmitters like dopamine, acetylcholine, etc.
    // affect oscillatory dynamics
    
    if (neuromodulator == "acetylcholine") {
        // Acetylcholine increases theta and gamma power, reduces alpha
        for (auto& regionPair : regions_) {
            NeuralOscillator* thetaOsc = regionPair.second->getOscillator(FrequencyBand::THETA);
            NeuralOscillator* gammaOsc = regionPair.second->getOscillator(FrequencyBand::GAMMA);
            NeuralOscillator* alphaOsc = regionPair.second->getOscillator(FrequencyBand::ALPHA);
            
            if (thetaOsc) thetaOsc->setAmplitude(thetaOsc->getAmplitude() * (1.0 + 0.3 * level));
            if (gammaOsc) gammaOsc->setAmplitude(gammaOsc->getAmplitude() * (1.0 + 0.4 * level));
            if (alphaOsc) alphaOsc->setAmplitude(alphaOsc->getAmplitude() * (1.0 - 0.2 * level));
        }
    }
    else if (neuromodulator == "dopamine") {
        // Dopamine increases beta and gamma power
        for (auto& regionPair : regions_) {
            NeuralOscillator* betaOsc = regionPair.second->getOscillator(FrequencyBand::BETA);
            NeuralOscillator* gammaOsc = regionPair.second->getOscillator(FrequencyBand::GAMMA);
            
            if (betaOsc) betaOsc->setAmplitude(betaOsc->getAmplitude() * (1.0 + 0.3 * level));
            if (gammaOsc) gammaOsc->setAmplitude(gammaOsc->getAmplitude() * (1.0 + 0.2 * level));
        }
    }
    else if (neuromodulator == "norepinephrine") {
        // Norepinephrine increases theta and beta frequency (alertness)
        for (auto& regionPair : regions_) {
            NeuralOscillator* thetaOsc = regionPair.second->getOscillator(FrequencyBand::THETA);
            NeuralOscillator* betaOsc = regionPair.second->getOscillator(FrequencyBand::BETA);
            
            if (thetaOsc) thetaOsc->modulateFrequency(1.0 + 0.2 * level);
            if (betaOsc) betaOsc->modulateFrequency(1.0 + 0.3 * level);
        }
    }
    else if (neuromodulator == "serotonin") {
        // Serotonin increases alpha power and theta-alpha coherence
        for (auto& regionPair : regions_) {
            NeuralOscillator* alphaOsc = regionPair.second->getOscillator(FrequencyBand::ALPHA);
            
            if (alphaOsc) alphaOsc->setAmplitude(alphaOsc->getAmplitude() * (1.0 + 0.3 * level));
        }
        
        // Increase theta-alpha coherence connections
        for (auto& conn : coherenceConnections_) {
            if (conn.band == FrequencyBand::ALPHA || conn.band == FrequencyBand::THETA) {
                conn.strength *= (1.0 + 0.2 * level);
            }
        }
    }
}

OscillatorEnsemble* BiologicalTimingCircuits::getRegionOscillators(const std::string& region) {
    auto it = regions_.find(region);
    if (it != regions_.end()) {
        return it->second.get();
    }
    return nullptr;
}

std::vector<std::string> BiologicalTimingCircuits::getRegionNames() const {
    std::vector<std::string> names;
    for (const auto& regionPair : regions_) {
        names.push_back(regionPair.first);
    }
    return names;
}

std::unordered_map<FrequencyBand, double> BiologicalTimingCircuits::getRegionAmplitudes(
    const std::string& region) const {
    
    auto it = regions_.find(region);
    if (it != regions_.end()) {
        return it->second->getCurrentAmplitudes();
    }
    
    return std::unordered_map<FrequencyBand, double>();
}

std::unordered_map<FrequencyBand, double> BiologicalTimingCircuits::getRegionPhases(
    const std::string& region) const {
    
    auto it = regions_.find(region);
    if (it != regions_.end()) {
        return it->second->getCurrentPhases();
    }
    
    return std::unordered_map<FrequencyBand, double>();
}

double BiologicalTimingCircuits::getCurrentTime() const {
    return time_;
}

} // namespace core_neural
} // namespace ultra