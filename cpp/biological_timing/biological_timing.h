/**
 * @file biological_timing.h
 * @brief Header for the Biological Timing Circuits component of the ULTRA system
 * 
 * This file contains the declarations for oscillatory dynamics inspired by neural
 * oscillations in the brain such as theta, alpha, beta, and gamma rhythms. These
 * oscillations coordinate processing across different brain regions and are involved
 * in functions such as attention, memory encoding, and multi-regional communication.
 * 
 * <AUTHOR> Team
 * @date 2025
 */

#ifndef ULTRA_CORE_NEURAL_BIOLOGICAL_TIMING_H_
#define ULTRA_CORE_NEURAL_BIOLOGICAL_TIMING_H_

#include <vector>
#include <string>
#include <unordered_map>
#include <memory>
#include <cmath>

namespace ultra {
namespace core_neural {

/**
 * @brief Enumeration of brain frequency bands
 */
enum class FrequencyBand {
    DELTA = 0,  // 1-4 Hz
    THETA = 1,  // 4-8 Hz
    ALPHA = 2,  // 8-12 Hz
    BETA = 3,   // 12-30 Hz
    GAMMA = 4   // 30-100 Hz
};

/**
 * @brief Class representing a neural oscillator for a specific frequency band
 * 
 * This class implements a model of oscillatory dynamics inspired by neural
 * oscillations in the brain.
 */
class NeuralOscillator {
public:
    /**
     * @brief Structure for oscillator coupling
     */
    struct OscillatorCoupling {
        NeuralOscillator* oscillator;
        double strength;
    };

    /**
     * @brief Constructor
     * 
     * @param band Frequency band of this oscillator
     * @param baseFrequency Base frequency in Hz
     * @param amplitude Initial amplitude
     * @param phase Initial phase in radians
     * @param decayRate Natural decay rate of oscillation
     */
    NeuralOscillator(FrequencyBand band, 
                     double baseFrequency, 
                     double amplitude = 1.0, 
                     double phase = 0.0,
                     double decayRate = 0.1);

    /**
     * @brief Update oscillator state
     * 
     * @param dt Time step in seconds
     * @param externalDrive External driving force value
     */
    void update(double dt, double externalDrive = 0.0);

    /**
     * @brief Get current oscillator value
     * 
     * @param currentTime Current time (used for phase-based calculation)
     * @return Current value of the oscillation
     */
    double getValue(double currentTime) const;

    /**
     * @brief Get current amplitude
     * @return Current amplitude value
     */
    double getAmplitude() const;

    /**
     * @brief Get current phase
     * @return Current phase in radians
     */
    double getPhase() const;

    /**
     * @brief Set oscillator phase
     * @param phase New phase value in radians
     */
    void setPhase(double phase);

    /**
     * @brief Set oscillator amplitude
     * @param amplitude New amplitude value
     */
    void setAmplitude(double amplitude);

    /**
     * @brief Set oscillator frequency
     * @param frequency New frequency in Hz
     */
    void setFrequency(double frequency);

    /**
     * @brief Modulate frequency by a factor
     * @param modulationFactor Multiplication factor for base frequency
     */
    void modulateFrequency(double modulationFactor);

    /**
     * @brief Reset frequency to base value
     */
    void resetToBaseFrequency();

    /**
     * @brief Enable the oscillator
     */
    void enable();

    /**
     * @brief Disable the oscillator
     */
    void disable();

    /**
     * @brief Check if oscillator is enabled
     * @return True if enabled, false otherwise
     */
    bool isEnabled() const;

    /**
     * @brief Add a coupling to another oscillator
     * 
     * @param oscillator Pointer to oscillator to couple with
     * @param strength Coupling strength (0.0 - 1.0)
     */
    void addCoupling(NeuralOscillator* oscillator, double strength);

    /**
     * @brief Remove coupling to an oscillator
     * @param oscillator Pointer to oscillator to decouple from
     */
    void removeCoupling(NeuralOscillator* oscillator);

    /**
     * @brief Update coupling strength to an oscillator
     * 
     * @param oscillator Pointer to oscillator to update
     * @param strength New coupling strength
     */
    void updateCouplingStrength(NeuralOscillator* oscillator, double strength);

    /**
     * @brief Get history of amplitude values
     * @return Vector of historical amplitude values
     */
    std::vector<double> getAmplitudeHistory() const;

    /**
     * @brief Get history of phase values
     * @return Vector of historical phase values
     */
    std::vector<double> getPhaseHistory() const;

    /**
     * @brief Get history of timestamp values
     * @return Vector of historical timestamps
     */
    std::vector<double> getTimeHistory() const;

    /**
     * @brief Get frequency band of this oscillator
     * @return Frequency band enum value
     */
    FrequencyBand getBand() const;

    /**
     * @brief Calculate coherence with another oscillator
     * 
     * @param other Other oscillator to compare with
     * @return Coherence value (0.0 - 1.0)
     */
    double getCoherenceWith(const NeuralOscillator& other) const;

private:
    static constexpr size_t HISTORY_SIZE = 100;  ///< Maximum history buffer size
    static constexpr double MAX_AMPLITUDE = 5.0; ///< Maximum allowed amplitude

    FrequencyBand band_;              ///< Frequency band of this oscillator
    double baseFrequency_;            ///< Base frequency in Hz
    double currentFrequency_;         ///< Current frequency (may be modulated)
    double amplitude_;                ///< Current amplitude
    double phase_;                    ///< Current phase in radians
    double decayRate_;                ///< Natural decay rate
    bool enabled_;                    ///< Whether oscillator is active

    std::vector<double> amplitudeHistory_; ///< History of amplitude values
    std::vector<double> phaseHistory_;     ///< History of phase values
    std::vector<double> timeHistory_;      ///< History of timestamps

    std::vector<OscillatorCoupling> coupledOscillators_; ///< Coupled oscillators
};

/**
 * @brief Class that manages a set of neural oscillators for a brain region
 * 
 * This class implements an ensemble of oscillators across different frequency
 * bands (delta, theta, alpha, beta, gamma) with coupling between them.
 */
class OscillatorEnsemble {
public:
    /**
     * @brief Constructor
     */
    OscillatorEnsemble();

    /**
     * @brief Update all oscillators in the ensemble
     * 
     * @param dt Time step in seconds
     * @param externalDrives Map of external drive values by frequency band
     */
    void update(double dt, 
               const std::unordered_map<FrequencyBand, double>& externalDrives);

    /**
     * @brief Get current values for all oscillators
     * @return Map of current values by frequency band
     */
    std::unordered_map<FrequencyBand, double> getCurrentValues() const;

    /**
     * @brief Get current amplitudes for all oscillators
     * @return Map of current amplitudes by frequency band
     */
    std::unordered_map<FrequencyBand, double> getCurrentAmplitudes() const;

    /**
     * @brief Get current phases for all oscillators
     * @return Map of current phases by frequency band
     */
    std::unordered_map<FrequencyBand, double> getCurrentPhases() const;

    /**
     * @brief Get pointer to specific oscillator
     * 
     * @param band Frequency band of desired oscillator
     * @return Pointer to oscillator or nullptr if not found
     */
    NeuralOscillator* getOscillator(FrequencyBand band);

    /**
     * @brief Enable specific oscillator
     * @param band Frequency band to enable
     */
    void enableOscillator(FrequencyBand band);

    /**
     * @brief Disable specific oscillator
     * @param band Frequency band to disable
     */
    void disableOscillator(FrequencyBand band);

    /**
     * @brief Set coupling between oscillators
     * 
     * @param sourceBand Source frequency band
     * @param targetBand Target frequency band
     * @param strength Coupling strength
     */
    void setCoupling(FrequencyBand sourceBand, 
                    FrequencyBand targetBand, 
                    double strength);

    /**
     * @brief Get coherence between two frequency bands
     * 
     * @param band1 First frequency band
     * @param band2 Second frequency band
     * @return Coherence value (0.0 - 1.0)
     */
    double getCoherence(FrequencyBand band1, FrequencyBand band2) const;

private:
    /**
     * @brief Set up default cross-frequency coupling
     */
    void setupDefaultCoupling();

    /**
     * @brief Apply phase-amplitude coupling between oscillators
     */
    void applyPhaseAmplitudeCoupling();

    std::unordered_map<FrequencyBand, std::unique_ptr<NeuralOscillator>> oscillators_;
};

/**
 * @brief Structure for coherence connections between brain regions
 */
struct CoherenceConnection {
    std::string sourceRegion;
    std::string targetRegion;
    FrequencyBand band;
    double strength;
};

/**
 * @brief Main class for biological timing circuits in the ULTRA system
 * 
 * This class implements brain-inspired oscillatory dynamics across multiple
 * simulated brain regions with coherence connections between them.
 */
class BiologicalTimingCircuits {
public:
    /**
     * @brief Constructor
     */
    BiologicalTimingCircuits();

    /**
     * @brief Update all oscillatory dynamics
     * @param dt Time step in seconds
     */
    void update(double dt);

    /**
     * @brief Add a coherence connection between regions
     * 
     * @param sourceRegion Source region name
     * @param targetRegion Target region name
     * @param band Frequency band for coherence
     * @param strength Connection strength
     */
    void addCoherenceConnection(const std::string& sourceRegion, 
                               const std::string& targetRegion,
                               FrequencyBand band, 
                               double strength);

    /**
     * @brief Remove a coherence connection
     * 
     * @param sourceRegion Source region name
     * @param targetRegion Target region name
     * @param band Frequency band for coherence
     */
    void removeCoherenceConnection(const std::string& sourceRegion, 
                                  const std::string& targetRegion,
                                  FrequencyBand band);

    /**
     * @brief Get coherence between regions for a specific band
     * 
     * @param sourceRegion Source region name
     * @param targetRegion Target region name
     * @param band Frequency band
     * @return Coherence value (0.0 - 1.0)
     */
    double getCoherence(const std::string& sourceRegion,
                       const std::string& targetRegion,
                       FrequencyBand band) const;

    /**
     * @brief Modulate frequency of oscillator in a region
     * 
     * @param region Region name
     * @param band Frequency band
     * @param modulationFactor Factor to multiply base frequency by
     */
    void modulateFrequency(const std::string& region, 
                          FrequencyBand band, 
                          double modulationFactor);

    /**
     * @brief Modify amplitude of oscillator in a region
     * 
     * @param region Region name
     * @param band Frequency band
     * @param newAmplitude New amplitude value
     */
    void modifyAmplitude(const std::string& region, 
                        FrequencyBand band, 
                        double newAmplitude);

    /**
     * @brief Modulate synaptic plasticity based on oscillation phase
     * 
     * Implements the equation: Δw_{ij} = STDP(Δt) · (1 + β · cos(φ_f(t)))
     * 
     * @param stdpValue Base STDP value to modulate
     * @param region Region name
     * @param band Frequency band to use for modulation
     * @return Modulated STDP value
     */
    double modulatePhaseBasedPlasticity(double stdpValue, 
                                      const std::string& region, 
                                      FrequencyBand band);

    /**
     * @brief Calculate connectivity based on oscillatory coherence
     * 
     * Implements the equation: E_{ij} = E_{ij}^0 · (1 + γ · cos(φ_i(t) - φ_j(t)))
     * 
     * @param sourceRegion Source region name
     * @param targetRegion Target region name
     * @param baseConnectivity Base connectivity value
     * @return Modulated connectivity value
     */
    double calculateCoherenceBasedConnectivity(const std::string& sourceRegion, 
                                            const std::string& targetRegion, 
                                            double baseConnectivity);

    /**
     * @brief Get multiplexed input across frequency bands
     * 
     * Implements information multiplexing: I_i(t) = ∑_f I_i^f(t) · sin(ω_f t + φ_f)
     * 
     * @param region Region name
     * @param baseInput Base input value to multiplex
     * @return Vector of input values for each frequency band
     */
    std::vector<double> getMultiplexedInput(const std::string& region, 
                                         double baseInput);

    /**
     * @brief Apply effect of a neuromodulator on oscillatory dynamics
     * 
     * @param neuromodulator Neuromodulator name (e.g., "dopamine", "acetylcholine")
     * @param level Neuromodulator level (0.0 - 1.0)
     */
    void applyNeuromodulationEffect(const std::string& neuromodulator, double level);

    /**
     * @brief Get the oscillator ensemble for a specific region
     * 
     * @param region Region name
     * @return Pointer to oscillator ensemble or nullptr if not found
     */
    OscillatorEnsemble* getRegionOscillators(const std::string& region);

    /**
     * @brief Get names of all brain regions
     * @return Vector of region names
     */
    std::vector<std::string> getRegionNames() const;

    /**
     * @brief Get oscillator amplitudes for all bands in a region
     * 
     * @param region Region name
     * @return Map of amplitudes by frequency band
     */
    std::unordered_map<FrequencyBand, double> getRegionAmplitudes(
        const std::string& region) const;

    /**
     * @brief Get oscillator phases for all bands in a region
     * 
     * @param region Region name
     * @return Map of phases by frequency band
     */
    std::unordered_map<FrequencyBand, double> getRegionPhases(
        const std::string& region) const;

    /**
     * @brief Get current simulation time
     * @return Current time in seconds
     */
    double getCurrentTime() const;

private:
    /**
     * @brief Initialize brain regions with oscillator ensembles
     */
    void initializeRegions();

    /**
     * @brief Set up default coherence connections between regions
     */
    void setupDefaultCoherenceConnections();

    /**
     * @brief Get external drive values for a specific region
     * 
     * @param region Region name
     * @return Map of drive values by frequency band
     */
    std::unordered_map<FrequencyBand, double> getExternalDrives(
        const std::string& region) const;

    /**
     * @brief Apply the effect of coherence connections between regions
     * @param dt Time step in seconds
     */
    void applyCoherenceConnections(double dt);

    /**
     * @brief Update coherence metrics across the system
     */
    void updateCoherenceMetrics();

    std::unordered_map<std::string, std::unique_ptr<OscillatorEnsemble>> regions_;
    std::vector<CoherenceConnection> coherenceConnections_;
    std::unordered_map<std::string, double> coherenceMetrics_;
    
    double time_;             ///< Current simulation time
    double lastUpdateTime_;   ///< Time of last update
};

} // namespace core_neural
} // namespace ultra

#endif // ULTRA_CORE_NEURAL_BIOLOGICAL_TIMING_H_