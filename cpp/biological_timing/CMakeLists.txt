cmake_minimum_required(VERSION 3.15)

# Set project name and version
project(ULTRA VERSION 1.0.0 LANGUAGES CXX)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# Set build type if not specified
if(NOT CMAKE_BUILD_TYPE)
  set(CMAKE_BUILD_TYPE Release)
endif()

# Output directories
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)

# Include directories
set(ULTRA_INCLUDE_DIR ${CMAKE_CURRENT_SOURCE_DIR})
include_directories(${ULTRA_INCLUDE_DIR})

# Define compile options
if(CMAKE_CXX_COMPILER_ID MATCHES "GNU|Clang")
  add_compile_options(-Wall -Wextra -Wpedantic -march=native)
  
  # Optimization flags
  set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} -O3")
  set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} -O0 -g")
  
  # Enable OpenMP if available
  find_package(OpenMP)
  if(OpenMP_CXX_FOUND)
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} ${OpenMP_CXX_FLAGS}")
  endif()
elseif(MSVC)
  add_compile_options(/W4 /MP /EHsc /arch:AVX2)
  
  # Optimization flags
  set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} /O2")
  set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} /Od /Zi")
  
  # Allow for big object files (needed for template-heavy code)
  add_compile_options(/bigobj)
  
  # Enable OpenMP for MSVC
  add_compile_options(/openmp)
endif()

# =================== Required Dependencies ===================

# Find Eigen (required for linear algebra operations)
find_package(Eigen3 3.3 REQUIRED NO_MODULE)

# Find FFTW (required for signal processing)
find_package(FFTW3 REQUIRED)
include_directories(${FFTW3_INCLUDE_DIRS})

# Find Boost (required for various utilities)
find_package(Boost 1.65 REQUIRED COMPONENTS system filesystem program_options serialization)
include_directories(${Boost_INCLUDE_DIRS})

# Find TBB (for parallel algorithms)
find_package(TBB REQUIRED)

# Find CUDA (optional but recommended for GPU acceleration)
option(ULTRA_USE_CUDA "Enable CUDA support for GPU acceleration" ON)
if(ULTRA_USE_CUDA)
  find_package(CUDA)
  if(CUDA_FOUND)
    enable_language(CUDA)
    set(CUDA_NVCC_FLAGS "${CUDA_NVCC_FLAGS} -std=c++14 -O3 -arch=sm_60")
    add_definitions(-DULTRA_HAS_CUDA)
    include_directories(${CUDA_INCLUDE_DIRS})
  else()
    message(STATUS "CUDA not found, GPU acceleration will be disabled")
  endif()
endif()

# Find MPI (optional but recommended for distributed computing)
option(ULTRA_USE_MPI "Enable MPI support for distributed computing" ON)
if(ULTRA_USE_MPI)
  find_package(MPI)
  if(MPI_FOUND)
    include_directories(${MPI_INCLUDE_PATH})
    add_definitions(-DULTRA_HAS_MPI)
  else()
    message(STATUS "MPI not found, distributed computing will be disabled")
  endif()
endif()

# Optional: Find GTest for unit testing
find_package(GTest)
if(GTEST_FOUND)
  include_directories(${GTEST_INCLUDE_DIRS})
  enable_testing()
else()
  message(STATUS "GTest not found, unit tests will be disabled")
endif()

# =================== Source Organization ===================

# Define source directories
set(CORE_NEURAL_DIR ${CMAKE_CURRENT_SOURCE_DIR}/core_neural)
set(HYPER_TRANSFORMER_DIR ${CMAKE_CURRENT_SOURCE_DIR}/hyper_transformer)
set(DIFFUSION_REASONING_DIR ${CMAKE_CURRENT_SOURCE_DIR}/diffusion_reasoning)
set(META_COGNITIVE_DIR ${CMAKE_CURRENT_SOURCE_DIR}/meta_cognitive)
set(NEUROMORPHIC_DIR ${CMAKE_CURRENT_SOURCE_DIR}/neuromorphic_processing)
set(CONSCIOUSNESS_DIR ${CMAKE_CURRENT_SOURCE_DIR}/emergent_consciousness)
set(NEURO_SYMBOLIC_DIR ${CMAKE_CURRENT_SOURCE_DIR}/neuro_symbolic)
set(SELF_EVOLUTION_DIR ${CMAKE_CURRENT_SOURCE_DIR}/self_evolution)
set(UTILS_DIR ${CMAKE_CURRENT_SOURCE_DIR}/utils)
set(INTEGRATION_DIR ${CMAKE_CURRENT_SOURCE_DIR}/integration)

# Create these directories if they don't exist
file(MAKE_DIRECTORY ${CORE_NEURAL_DIR})
file(MAKE_DIRECTORY ${HYPER_TRANSFORMER_DIR})
file(MAKE_DIRECTORY ${DIFFUSION_REASONING_DIR})
file(MAKE_DIRECTORY ${META_COGNITIVE_DIR})
file(MAKE_DIRECTORY ${NEUROMORPHIC_DIR})
file(MAKE_DIRECTORY ${CONSCIOUSNESS_DIR})
file(MAKE_DIRECTORY ${NEURO_SYMBOLIC_DIR})
file(MAKE_DIRECTORY ${SELF_EVOLUTION_DIR})
file(MAKE_DIRECTORY ${UTILS_DIR})
file(MAKE_DIRECTORY ${INTEGRATION_DIR})

# =================== Core Neural Architecture ===================

# Core Neural Architecture library
set(CORE_NEURAL_SOURCES
    ${CORE_NEURAL_DIR}/neuromorphic_core.cpp
    ${CORE_NEURAL_DIR}/neuroplasticity_engine.cpp
    ${CORE_NEURAL_DIR}/synaptic_pruning.cpp
    ${CORE_NEURAL_DIR}/neuromodulation.cpp
    ${CORE_NEURAL_DIR}/biological_timing.cpp
)

add_library(core_neural STATIC ${CORE_NEURAL_SOURCES})
target_include_directories(core_neural PUBLIC
    ${CORE_NEURAL_DIR}
    ${UTILS_DIR}
)
target_link_libraries(core_neural PUBLIC
    Eigen3::Eigen
    ${FFTW3_LIBRARIES}
    ${Boost_LIBRARIES}
    TBB::tbb
)

# =================== Hyper-Dimensional Transformer ===================

# Hyper-Dimensional Transformer library
set(HYPER_TRANSFORMER_SOURCES
    ${HYPER_TRANSFORMER_DIR}/dynamic_attention.cpp
    ${HYPER_TRANSFORMER_DIR}/contextual_bias.cpp
    ${HYPER_TRANSFORMER_DIR}/recursive_transformer.cpp
    ${HYPER_TRANSFORMER_DIR}/temporal_causal.cpp
    ${HYPER_TRANSFORMER_DIR}/multiscale_embedding.cpp
    ${HYPER_TRANSFORMER_DIR}/cross_modal_mapper.cpp
)

add_library(hyper_transformer STATIC ${HYPER_TRANSFORMER_SOURCES})
target_include_directories(hyper_transformer PUBLIC
    ${HYPER_TRANSFORMER_DIR}
    ${UTILS_DIR}
)
target_link_libraries(hyper_transformer PUBLIC
    Eigen3::Eigen
    ${Boost_LIBRARIES}
    TBB::tbb
)

if(CUDA_FOUND)
    target_link_libraries(hyper_transformer PUBLIC ${CUDA_LIBRARIES})
endif()

# =================== Diffusion-Based Reasoning ===================

# Diffusion-Based Reasoning library
set(DIFFUSION_REASONING_SOURCES
    ${DIFFUSION_REASONING_DIR}/conceptual_diffusion.cpp
    ${DIFFUSION_REASONING_DIR}/thought_latent_space.cpp
    ${DIFFUSION_REASONING_DIR}/reverse_diffusion.cpp
    ${DIFFUSION_REASONING_DIR}/bayesian_uncertainty.cpp
    ${DIFFUSION_REASONING_DIR}/probabilistic_inference.cpp
)

add_library(diffusion_reasoning STATIC ${DIFFUSION_REASONING_SOURCES})
target_include_directories(diffusion_reasoning PUBLIC
    ${DIFFUSION_REASONING_DIR}
    ${UTILS_DIR}
)
target_link_libraries(diffusion_reasoning PUBLIC
    Eigen3::Eigen
    ${Boost_LIBRARIES}
    TBB::tbb
)

if(CUDA_FOUND)
    target_link_libraries(diffusion_reasoning PUBLIC ${CUDA_LIBRARIES})
endif()

# =================== Meta-Cognitive System ===================

# Meta-Cognitive System library
set(META_COGNITIVE_SOURCES
    ${META_COGNITIVE_DIR}/chain_of_thought.cpp
    ${META_COGNITIVE_DIR}/tree_of_thought.cpp
    ${META_COGNITIVE_DIR}/reasoning_graphs.cpp
    ${META_COGNITIVE_DIR}/self_critique.cpp
    ${META_COGNITIVE_DIR}/bias_detection.cpp
    ${META_COGNITIVE_DIR}/meta_learning.cpp
)

add_library(meta_cognitive STATIC ${META_COGNITIVE_SOURCES})
target_include_directories(meta_cognitive PUBLIC
    ${META_COGNITIVE_DIR}
    ${UTILS_DIR}
)
target_link_libraries(meta_cognitive PUBLIC
    Eigen3::Eigen
    ${Boost_LIBRARIES}
    TBB::tbb
)

# =================== Neuromorphic Processing Layer ===================

# Neuromorphic Processing Layer library
set(NEUROMORPHIC_SOURCES
    ${NEUROMORPHIC_DIR}/spiking_networks.cpp
    ${NEUROMORPHIC_DIR}/event_based_computing.cpp
    ${NEUROMORPHIC_DIR}/memristor_array.cpp
    ${NEUROMORPHIC_DIR}/reservoir_computing.cpp
    ${NEUROMORPHIC_DIR}/brain_region_emulation.cpp
)

add_library(neuromorphic_processing STATIC ${NEUROMORPHIC_SOURCES})
target_include_directories(neuromorphic_processing PUBLIC
    ${NEUROMORPHIC_DIR}
    ${UTILS_DIR}
)
target_link_libraries(neuromorphic_processing PUBLIC
    Eigen3::Eigen
    ${FFTW3_LIBRARIES}
    ${Boost_LIBRARIES}
    TBB::tbb
)

if(CUDA_FOUND)
    target_link_libraries(neuromorphic_processing PUBLIC ${CUDA_LIBRARIES})
endif()

# =================== Emergent Consciousness Lattice ===================

# Emergent Consciousness Lattice library
set(CONSCIOUSNESS_SOURCES
    ${CONSCIOUSNESS_DIR}/self_awareness.cpp
    ${CONSCIOUSNESS_DIR}/intentionality.cpp
    ${CONSCIOUSNESS_DIR}/integrated_information.cpp
    ${CONSCIOUSNESS_DIR}/attentional_awareness.cpp
    ${CONSCIOUSNESS_DIR}/global_workspace.cpp
)

add_library(emergent_consciousness STATIC ${CONSCIOUSNESS_SOURCES})
target_include_directories(emergent_consciousness PUBLIC
    ${CONSCIOUSNESS_DIR}
    ${UTILS_DIR}
)
target_link_libraries(emergent_consciousness PUBLIC
    Eigen3::Eigen
    ${Boost_LIBRARIES}
    TBB::tbb
)

# =================== Neuro-Symbolic Integration ===================

# Neuro-Symbolic Integration library
set(NEURO_SYMBOLIC_SOURCES
    ${NEURO_SYMBOLIC_DIR}/logical_reasoning.cpp
    ${NEURO_SYMBOLIC_DIR}/symbolic_representation.cpp
    ${NEURO_SYMBOLIC_DIR}/neuro_symbolic_bridge.cpp
    ${NEURO_SYMBOLIC_DIR}/program_synthesis.cpp
    ${NEURO_SYMBOLIC_DIR}/rule_extraction.cpp
)

add_library(neuro_symbolic STATIC ${NEURO_SYMBOLIC_SOURCES})
target_include_directories(neuro_symbolic PUBLIC
    ${NEURO_SYMBOLIC_DIR}
    ${UTILS_DIR}
)
target_link_libraries(neuro_symbolic PUBLIC
    Eigen3::Eigen
    ${Boost_LIBRARIES}
    TBB::tbb
)

# =================== Self-Evolution System ===================

# Self-Evolution System library
set(SELF_EVOLUTION_SOURCES
    ${SELF_EVOLUTION_DIR}/architecture_search.cpp
    ${SELF_EVOLUTION_DIR}/self_modification.cpp
    ${SELF_EVOLUTION_DIR}/computational_reflection.cpp
    ${SELF_EVOLUTION_DIR}/evolutionary_steering.cpp
    ${SELF_EVOLUTION_DIR}/innovation_detection.cpp
)

add_library(self_evolution STATIC ${SELF_EVOLUTION_SOURCES})
target_include_directories(self_evolution PUBLIC
    ${SELF_EVOLUTION_DIR}
    ${UTILS_DIR}
)
target_link_libraries(self_evolution PUBLIC
    Eigen3::Eigen
    ${Boost_LIBRARIES}
    TBB::tbb
)

# =================== Utilities ===================

# Utilities library
set(UTILS_SOURCES
    ${UTILS_DIR}/logging.cpp
    ${UTILS_DIR}/config.cpp
    ${UTILS_DIR}/visualization.cpp
    ${UTILS_DIR}/monitoring.cpp
    ${UTILS_DIR}/profiling.cpp
    ${UTILS_DIR}/random.cpp
    ${UTILS_DIR}/serialization.cpp
)

add_library(utils STATIC ${UTILS_SOURCES})
target_include_directories(utils PUBLIC
    ${UTILS_DIR}
)
target_link_libraries(utils PUBLIC
    Eigen3::Eigen
    ${Boost_LIBRARIES}
    TBB::tbb
)

# =================== ULTRA Main System Integration ===================

# Integration library
set(INTEGRATION_SOURCES
    ${INTEGRATION_DIR}/system_manager.cpp
    ${INTEGRATION_DIR}/component_factory.cpp
    ${INTEGRATION_DIR}/communication_bus.cpp
    ${INTEGRATION_DIR}/scheduler.cpp
    ${INTEGRATION_DIR}/memory_manager.cpp
)

add_library(integration STATIC ${INTEGRATION_SOURCES})
target_include_directories(integration PUBLIC
    ${INTEGRATION_DIR}
    ${UTILS_DIR}
)
target_link_libraries(integration PUBLIC
    core_neural
    hyper_transformer
    diffusion_reasoning
    meta_cognitive
    neuromorphic_processing
    emergent_consciousness
    neuro_symbolic
    self_evolution
    utils
)

# Main ULTRA library (combines all components)
add_library(ultra SHARED
    ${CMAKE_CURRENT_SOURCE_DIR}/ultra.cpp
)
target_include_directories(ultra PUBLIC
    ${CMAKE_CURRENT_SOURCE_DIR}
)
target_link_libraries(ultra PUBLIC
    integration
)

# Create a main executable for demonstration/testing
add_executable(ultra_main ${CMAKE_CURRENT_SOURCE_DIR}/main.cpp)
target_link_libraries(ultra_main PRIVATE ultra)

# =================== Unit Tests ===================

if(GTEST_FOUND)
    # Core Neural Architecture tests
    add_executable(test_core_neural
        ${CMAKE_CURRENT_SOURCE_DIR}/tests/test_core_neural.cpp
    )
    target_link_libraries(test_core_neural PRIVATE
        core_neural
        ${GTEST_LIBRARIES}
        ${GTEST_MAIN_LIBRARIES}
        pthread
    )
    add_test(NAME CoreNeuralTests COMMAND test_core_neural)
    
    # Biological Timing Circuits tests
    add_executable(test_biological_timing
        ${CMAKE_CURRENT_SOURCE_DIR}/tests/test_biological_timing.cpp
    )
    target_link_libraries(test_biological_timing PRIVATE
        core_neural
        ${GTEST_LIBRARIES}
        ${GTEST_MAIN_LIBRARIES}
        pthread
    )
    add_test(NAME BiologicalTimingTests COMMAND test_biological_timing)
    
    # Hyper-Dimensional Transformer tests
    add_executable(test_hyper_transformer
        ${CMAKE_CURRENT_SOURCE_DIR}/tests/test_hyper_transformer.cpp
    )
    target_link_libraries(test_hyper_transformer PRIVATE
        hyper_transformer
        ${GTEST_LIBRARIES}
        ${GTEST_MAIN_LIBRARIES}
        pthread
    )
    add_test(NAME HyperTransformerTests COMMAND test_hyper_transformer)
    
    # Add more test targets for other components...
endif()

# =================== Installation ===================

include(GNUInstallDirs)

# Install libraries
install(TARGETS 
    ultra
    core_neural
    hyper_transformer
    diffusion_reasoning
    meta_cognitive
    neuromorphic_processing
    emergent_consciousness
    neuro_symbolic
    self_evolution
    utils
    integration
    LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
    ARCHIVE DESTINATION ${CMAKE_INSTALL_LIBDIR}
)

# Install headers
install(DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/
    DESTINATION ${CMAKE_INSTALL_INCLUDEDIR}/ultra
    FILES_MATCHING PATTERN "*.h"
)

# Install executable
install(TARGETS ultra_main
    RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
)

# =================== Configuration Summary ===================

message(STATUS "")
message(STATUS "ULTRA Configuration Summary:")
message(STATUS "  CMake build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "  C++ Compiler: ${CMAKE_CXX_COMPILER_ID} ${CMAKE_CXX_COMPILER_VERSION}")

message(STATUS "  Dependencies:")
message(STATUS "    Eigen: ${EIGEN3_VERSION}")
message(STATUS "    Boost: ${Boost_VERSION}")
message(STATUS "    FFTW: ${FFTW3_VERSION}")
message(STATUS "    TBB: ${TBB_VERSION}")

if(CUDA_FOUND)
    message(STATUS "    CUDA: ${CUDA_VERSION}")
    message(STATUS "    CUDA architecture: ${CUDA_NVCC_FLAGS}")
else()
    message(STATUS "    CUDA: Not found/disabled")
endif()

if(MPI_FOUND)
    message(STATUS "    MPI: ${MPI_VERSION}")
else()
    message(STATUS "    MPI: Not found/disabled")
endif()

if(GTEST_FOUND)
    message(STATUS "    GTest: Found (unit tests enabled)")
else()
    message(STATUS "    GTest: Not found (unit tests disabled)")
endif()

message(STATUS "")
message(STATUS "  Installation paths:")
message(STATUS "    Libraries: ${CMAKE_INSTALL_FULL_LIBDIR}")
message(STATUS "    Headers: ${CMAKE_INSTALL_FULL_INCLUDEDIR}/ultra")
message(STATUS "    Executables: ${CMAKE_INSTALL_FULL_BINDIR}")
message(STATUS "")

# Export package for use with find_package
include(CMakePackageConfigHelpers)
write_basic_package_version_file(
    "${CMAKE_CURRENT_BINARY_DIR}/ULTRAConfigVersion.cmake"
    VERSION ${PROJECT_VERSION}
    COMPATIBILITY SameMajorVersion
)

configure_package_config_file(
    "${CMAKE_CURRENT_SOURCE_DIR}/cmake/ULTRAConfig.cmake.in"
    "${CMAKE_CURRENT_BINARY_DIR}/ULTRAConfig.cmake"
    INSTALL_DESTINATION ${CMAKE_INSTALL_DATAROOTDIR}/ULTRA/cmake
)

install(FILES
    "${CMAKE_CURRENT_BINARY_DIR}/ULTRAConfig.cmake"
    "${CMAKE_CURRENT_BINARY_DIR}/ULTRAConfigVersion.cmake"
    DESTINATION ${CMAKE_INSTALL_DATAROOTDIR}/ULTRA/cmake
)

export(EXPORT ULTRATargets
    FILE "${CMAKE_CURRENT_BINARY_DIR}/ULTRATargets.cmake"
    NAMESPACE ULTRA::
)

install(EXPORT ULTRATargets
    FILE ULTRATargets.cmake
    NAMESPACE ULTRA::
    DESTINATION ${CMAKE_INSTALL_DATAROOTDIR}/ULTRA/cmake
)