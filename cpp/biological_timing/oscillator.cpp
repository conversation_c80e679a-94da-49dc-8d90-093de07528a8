/**
 * @file oscillator.cpp
 * @brief Implementation of neural oscillator models for biological timing circuits
 * 
 * This file implements detailed oscillator models that form the foundation of
 * the biological timing circuits in the ULTRA system. These oscillators simulate
 * brain rhythms such as delta, theta, alpha, beta, and gamma waves, with
 * support for phase-phase and phase-amplitude coupling, entrainment, and other
 * neurophysiologically relevant phenomena.
 * 
 * <AUTHOR> Team
 * @date 2025
 */

#include "oscillator.h"
#include <algorithm>
#include <cmath>
#include <complex>
#include <random>
#include <iostream>
#include <Eigen/Dense>
#include <unsupported/Eigen/FFT>

namespace ultra {
namespace core_neural {
namespace biological_timing {

//-----------------------------------------------------------------------------
// Utility Functions
//-----------------------------------------------------------------------------

namespace {
    /**
     * @brief Generate white noise for stochastic oscillator dynamics
     * 
     * @param mean Mean of Gaussian distribution
     * @param stddev Standard deviation of Gaussian distribution
     * @return Random value from normal distribution
     */
    double generateNoise(double mean = 0.0, double stddev = 1.0) {
        static std::random_device rd;
        static std::mt19937 gen(rd());
        static std::normal_distribution<double> dist(mean, stddev);
        return dist(gen);
    }

    /**
     * @brief Calculate phase difference between two phases
     * 
     * Returns the shortest angular distance between two phase angles
     * 
     * @param phase1 First phase in radians
     * @param phase2 Second phase in radians
     * @return Phase difference normalized to [-π, π]
     */
    double phaseDifference(double phase1, double phase2) {
        double diff = phase1 - phase2;
        // Normalize to [-π, π]
        while (diff > M_PI) diff -= 2.0 * M_PI;
        while (diff < -M_PI) diff += 2.0 * M_PI;
        return diff;
    }

    /**
     * @brief Compute analytic signal using Hilbert transform
     * 
     * The Hilbert transform converts a real signal to a complex analytic signal,
     * allowing extraction of instantaneous amplitude and phase.
     * 
     * @param signal Input signal vector
     * @param analytic_signal Output complex analytic signal vector
     */
    void hilbertTransform(const std::vector<double>& signal, 
                          std::vector<std::complex<double>>& analytic_signal) {
        size_t n = signal.size();
        analytic_signal.resize(n);
        
        // Perform FFT on input signal
        Eigen::FFT<double> fft;
        std::vector<std::complex<double>> freqDomain;
        fft.fwd(freqDomain, signal);
        
        // Create analytic signal in frequency domain
        // (zero out negative frequencies, double positive frequencies)
        std::vector<std::complex<double>> analyticFreq(n);
        for (size_t i = 0; i < n; i++) {
            if (i == 0 || (n % 2 == 0 && i == n/2)) {
                // DC and Nyquist components remain unchanged
                analyticFreq[i] = freqDomain[i];
            } else if (i < n/2) {
                // Positive frequencies are doubled
                analyticFreq[i] = 2.0 * freqDomain[i];
            } else {
                // Negative frequencies are zeroed
                analyticFreq[i] = 0.0;
            }
        }
        
        // Inverse FFT to get analytic signal in time domain
        fft.inv(analytic_signal, analyticFreq);
    }
}

//-----------------------------------------------------------------------------
// BaseOscillator Implementation
//-----------------------------------------------------------------------------

BaseOscillator::BaseOscillator(double frequency, double amplitude, double phase, double decayRate)
    : frequency_(frequency),
      amplitude_(amplitude),
      phase_(phase),
      decayRate_(decayRate),
      enabled_(true),
      timeStep_(0.0),
      historyLength_(DEFAULT_HISTORY_LENGTH) {
    
    // Initialize history buffers
    timeHistory_.reserve(historyLength_);
    amplitudeHistory_.reserve(historyLength_);
    phaseHistory_.reserve(historyLength_);
    valueHistory_.reserve(historyLength_);
    
    // Record initial state
    timeHistory_.push_back(0.0);
    amplitudeHistory_.push_back(amplitude);
    phaseHistory_.push_back(phase);
    valueHistory_.push_back(amplitude * std::sin(phase));
}

BaseOscillator::~BaseOscillator() = default;

void BaseOscillator::update(double dt, double externalDrive) {
    if (!enabled_) return;
    
    // Update simulation time
    timeStep_ += dt;
    
    // Store pre-update state for analysis
    double prevPhase = phase_;
    double prevAmplitude = amplitude_;
    
    // Perform oscillator-specific update
    updateDynamics(dt, externalDrive);
    
    // Normalize phase to [0, 2π]
    normalizePhase();
    
    // Apply amplitude constraints
    if (amplitude_ < 0.0) {
        amplitude_ = 0.0;
    } else if (amplitude_ > MAX_AMPLITUDE) {
        amplitude_ = MAX_AMPLITUDE;
    }
    
    // Store state in history
    storeStateInHistory();
    
    // Calculate and store instantaneous metrics
    instantaneousFrequency_ = (phase_ - prevPhase) / dt / (2.0 * M_PI);
    if (instantaneousFrequency_ < 0) {
        instantaneousFrequency_ += 1.0 / dt; // Adjust for phase wrap-around
    }
    
    amplitudeChangeRate_ = (amplitude_ - prevAmplitude) / dt;
}

void BaseOscillator::normalizePhase() {
    while (phase_ >= 2.0 * M_PI) phase_ -= 2.0 * M_PI;
    while (phase_ < 0.0) phase_ += 2.0 * M_PI;
}

void BaseOscillator::storeStateInHistory() {
    // Use circular buffer behavior when history exceeds capacity
    if (timeHistory_.size() >= historyLength_) {
        timeHistory_.erase(timeHistory_.begin());
        amplitudeHistory_.erase(amplitudeHistory_.begin());
        phaseHistory_.erase(phaseHistory_.begin());
        valueHistory_.erase(valueHistory_.begin());
    }
    
    // Store current state
    timeHistory_.push_back(timeStep_);
    amplitudeHistory_.push_back(amplitude_);
    phaseHistory_.push_back(phase_);
    valueHistory_.push_back(getValue());
}

double BaseOscillator::getValue() const {
    return amplitude_ * std::sin(phase_);
}

double BaseOscillator::getAmplitude() const {
    return amplitude_;
}

double BaseOscillator::getPhase() const {
    return phase_;
}

double BaseOscillator::getFrequency() const {
    return frequency_;
}

double BaseOscillator::getInstantaneousFrequency() const {
    return instantaneousFrequency_;
}

double BaseOscillator::getAmplitudeChangeRate() const {
    return amplitudeChangeRate_;
}

void BaseOscillator::setAmplitude(double amplitude) {
    amplitude_ = std::max(0.0, std::min(amplitude, MAX_AMPLITUDE));
}

void BaseOscillator::setPhase(double phase) {
    phase_ = phase;
    normalizePhase();
}

void BaseOscillator::setFrequency(double frequency) {
    frequency_ = frequency;
}

void BaseOscillator::setDecayRate(double decayRate) {
    decayRate_ = decayRate;
}

void BaseOscillator::enable() {
    enabled_ = true;
}

void BaseOscillator::disable() {
    enabled_ = false;
}

bool BaseOscillator::isEnabled() const {
    return enabled_;
}

void BaseOscillator::resetHistory() {
    timeHistory_.clear();
    amplitudeHistory_.clear();
    phaseHistory_.clear();
    valueHistory_.clear();
    
    // Store current state
    timeHistory_.push_back(timeStep_);
    amplitudeHistory_.push_back(amplitude_);
    phaseHistory_.push_back(phase_);
    valueHistory_.push_back(getValue());
}

void BaseOscillator::setHistoryLength(size_t length) {
    historyLength_ = length;
    
    // Trim history if necessary
    while (timeHistory_.size() > historyLength_) {
        timeHistory_.erase(timeHistory_.begin());
        amplitudeHistory_.erase(amplitudeHistory_.begin());
        phaseHistory_.erase(phaseHistory_.begin());
        valueHistory_.erase(valueHistory_.begin());
    }
}

const std::vector<double>& BaseOscillator::getTimeHistory() const {
    return timeHistory_;
}

const std::vector<double>& BaseOscillator::getAmplitudeHistory() const {
    return amplitudeHistory_;
}

const std::vector<double>& BaseOscillator::getPhaseHistory() const {
    return phaseHistory_;
}

const std::vector<double>& BaseOscillator::getValueHistory() const {
    return valueHistory_;
}

double BaseOscillator::getCoherenceWith(const BaseOscillator& other) const {
    if (phaseHistory_.size() < 2 || other.phaseHistory_.size() < 2) {
        return 0.0; // Not enough history for coherence calculation
    }
    
    // Calculate phase coherence over history using circular mean
    size_t n = std::min(phaseHistory_.size(), other.phaseHistory_.size());
    double sumSin = 0.0;
    double sumCos = 0.0;
    
    for (size_t i = 0; i < n; i++) {
        // Use phase difference to get relative phase
        double phaseDiff = phaseDifference(
            phaseHistory_[phaseHistory_.size() - 1 - i],
            other.phaseHistory_[other.phaseHistory_.size() - 1 - i]
        );
        
        // Accumulate sin and cos components
        sumSin += std::sin(phaseDiff);
        sumCos += std::cos(phaseDiff);
    }
    
    // Mean resultant length (MRL) - measure of phase coherence
    double mrl = std::sqrt(sumSin * sumSin + sumCos * sumCos) / static_cast<double>(n);
    
    return mrl; // Value between 0 (random) and 1 (perfectly coherent)
}

double BaseOscillator::getPhaseAmplitudeCoupling() const {
    if (phaseHistory_.size() < 10 || amplitudeHistory_.size() < 10) {
        return 0.0; // Not enough history for PAC calculation
    }
    
    // Calculate phase-amplitude coupling using modulation index
    // This measures how well amplitude at one frequency is modulated by
    // phase at another frequency (often used in neuroscience for cross-frequency coupling)
    
    size_t n = phaseHistory_.size();
    
    // Create phase bins
    const int numBins = 18; // 20-degree bins
    std::vector<double> amplitudeMeans(numBins, 0.0);
    std::vector<int> binCounts(numBins, 0);
    
    // Assign amplitude values to phase bins
    for (size_t i = 0; i < n; i++) {
        double phase = phaseHistory_[i];
        double amplitude = amplitudeHistory_[i];
        
        // Convert phase (0 to 2π) to bin index (0 to numBins-1)
        int binIndex = static_cast<int>(phase / (2.0 * M_PI) * numBins) % numBins;
        
        amplitudeMeans[binIndex] += amplitude;
        binCounts[binIndex]++;
    }
    
    // Calculate mean amplitude for each phase bin
    for (int i = 0; i < numBins; i++) {
        if (binCounts[i] > 0) {
            amplitudeMeans[i] /= binCounts[i];
        }
    }
    
    // Normalize the distribution
    double sumAmplitudes = std::accumulate(amplitudeMeans.begin(), amplitudeMeans.end(), 0.0);
    if (sumAmplitudes > 0) {
        for (int i = 0; i < numBins; i++) {
            amplitudeMeans[i] /= sumAmplitudes;
        }
    }
    
    // Calculate modulation index (Kullback-Leibler divergence from uniform distribution)
    // MI = 0 means no modulation, higher values mean stronger modulation
    double uniformProb = 1.0 / numBins;
    double kl = 0.0;
    
    for (int i = 0; i < numBins; i++) {
        if (amplitudeMeans[i] > 0) {
            kl += amplitudeMeans[i] * std::log(amplitudeMeans[i] / uniformProb);
        }
    }
    
    // Normalize to [0, 1] range
    double modulationIndex = kl / std::log(numBins);
    
    return modulationIndex;
}

//-----------------------------------------------------------------------------
// VanDerPolOscillator Implementation
//-----------------------------------------------------------------------------

VanDerPolOscillator::VanDerPolOscillator(double frequency, double amplitude, double phase,
                                        double mu, double decayRate)
    : BaseOscillator(frequency, amplitude, phase, decayRate),
      mu_(mu),
      position_(amplitude * std::sin(phase)),
      velocity_(amplitude * frequency * std::cos(phase)) {
}

void VanDerPolOscillator::updateDynamics(double dt, double externalDrive) {
    // Van der Pol oscillator is typically defined by the second-order ODE:
    // ẍ - μ(1 - x²)ẋ + ω²x = F(t)
    // where:
    // - x is position (value of oscillator)
    // - μ controls the nonlinearity and limit cycle behavior
    // - ω = 2πf is the angular frequency
    // - F(t) is external drive
    
    double omega = 2.0 * M_PI * frequency_;
    
    // Update position and velocity using Runge-Kutta 4th order method
    double x = position_;
    double v = velocity_;
    
    // k1
    double k1_x = v;
    double k1_v = mu_ * (1.0 - x*x) * v - omega*omega * x + externalDrive;
    
    // k2
    double k2_x = v + 0.5 * dt * k1_v;
    double x_mid = x + 0.5 * dt * k1_x;
    double k2_v = mu_ * (1.0 - x_mid*x_mid) * k2_x - omega*omega * x_mid + externalDrive;
    
    // k3
    double k3_x = v + 0.5 * dt * k2_v;
    x_mid = x + 0.5 * dt * k2_x;
    double k3_v = mu_ * (1.0 - x_mid*x_mid) * k3_x - omega*omega * x_mid + externalDrive;
    
    // k4
    double k4_x = v + dt * k3_v;
    double x_end = x + dt * k3_x;
    double k4_v = mu_ * (1.0 - x_end*x_end) * k4_x - omega*omega * x_end + externalDrive;
    
    // Update state
    position_ = x + (dt/6.0) * (k1_x + 2.0*k2_x + 2.0*k3_x + k4_x);
    velocity_ = v + (dt/6.0) * (k1_v + 2.0*k2_v + 2.0*k3_v + k4_v);
    
    // Add stochastic component
    double noise = generateNoise(0.0, 0.01);
    velocity_ += noise;
    
    // Apply natural decay
    velocity_ *= (1.0 - decayRate_ * dt);
    
    // Extract amplitude and phase from position and velocity
    amplitude_ = std::sqrt(position_*position_ + (velocity_/(omega))*(velocity_/(omega)));
    phase_ = std::atan2(velocity_/omega, position_);
    
    // Apply amplitude damping if above limit
    if (amplitude_ > MAX_AMPLITUDE) {
        double dampingFactor = MAX_AMPLITUDE / amplitude_;
        position_ *= dampingFactor;
        velocity_ *= dampingFactor;
        amplitude_ = MAX_AMPLITUDE;
    }
}

double VanDerPolOscillator::getValue() const {
    return position_;
}

double VanDerPolOscillator::getMu() const {
    return mu_;
}

void VanDerPolOscillator::setMu(double mu) {
    mu_ = mu;
}

double VanDerPolOscillator::getVelocity() const {
    return velocity_;
}

double VanDerPolOscillator::getPosition() const {
    return position_;
}

//-----------------------------------------------------------------------------
// KuramotoOscillator Implementation
//-----------------------------------------------------------------------------

KuramotoOscillator::KuramotoOscillator(double frequency, double amplitude, double phase,
                                      double decayRate)
    : BaseOscillator(frequency, amplitude, phase, decayRate) {
}

void KuramotoOscillator::updateDynamics(double dt, double externalDrive) {
    // Basic Kuramoto model: dθ/dt = ω + K/N Σ_j sin(θ_j - θ) + external
    // Here we implement a single oscillator that can be coupled to others
    
    // Natural frequency contribution (in radians/sec)
    double dphase = 2.0 * M_PI * frequency_ * dt;
    
    // Coupling contributions from other oscillators
    double couplingSum = 0.0;
    for (const auto& coupling : couplings_) {
        double phaseOther = coupling.oscillator->getPhase();
        double K = coupling.strength;
        
        couplingSum += K * std::sin(phaseOther - phase_);
    }
    
    // External drive contribution
    double externalContribution = 0.1 * externalDrive * dt;
    
    // Update phase
    phase_ += dphase + couplingSum * dt + externalContribution;
    
    // Update amplitude based on external drive and natural decay
    double damp = 1.0 - decayRate_ * dt;
    amplitude_ = damp * amplitude_ + 0.02 * externalDrive * dt;
    
    // Add stochastic component
    phase_ += generateNoise(0.0, 0.01) * dt;
    amplitude_ += generateNoise(0.0, 0.005) * dt;
}

void KuramotoOscillator::addCoupling(BaseOscillator* oscillator, double strength) {
    // Check if oscillator already exists in couplings
    for (auto& coupling : couplings_) {
        if (coupling.oscillator == oscillator) {
            coupling.strength = strength; // Update strength
            return;
        }
    }
    
    // Add new coupling
    couplings_.push_back({oscillator, strength});
}

void KuramotoOscillator::removeCoupling(BaseOscillator* oscillator) {
    couplings_.erase(
        std::remove_if(couplings_.begin(), couplings_.end(),
                      [oscillator](const Coupling& c) { return c.oscillator == oscillator; }),
        couplings_.end()
    );
}

void KuramotoOscillator::updateCouplingStrength(BaseOscillator* oscillator, double strength) {
    for (auto& coupling : couplings_) {
        if (coupling.oscillator == oscillator) {
            coupling.strength = strength;
            return;
        }
    }
}

const std::vector<KuramotoOscillator::Coupling>& KuramotoOscillator::getCouplings() const {
    return couplings_;
}

//-----------------------------------------------------------------------------
// WilsonCowanOscillator Implementation
//-----------------------------------------------------------------------------

WilsonCowanOscillator::WilsonCowanOscillator(double frequency, double amplitude, double phase,
                                           double excitationStrength, double inhibitionStrength,
                                           double decayRate)
    : BaseOscillator(frequency, amplitude, phase, decayRate),
      excitationStrength_(excitationStrength),
      inhibitionStrength_(inhibitionStrength),
      excitation_(0.2),
      inhibition_(0.2),
      timeConstantE_(0.01),
      timeConstantI_(0.02),
      sigmoidSlopeE_(1.0),
      sigmoidSlopeI_(1.0) {
}

void WilsonCowanOscillator::updateDynamics(double dt, double externalDrive) {
    // Wilson-Cowan neural mass model: 
    // dE/dt = -E/τ_E + (1-E) * S_E(c_EE*E - c_IE*I + external)
    // dI/dt = -I/τ_I + (1-I) * S_I(c_EI*E - c_II*I)
    // where S_x(u) = 1/(1+exp(-a_x*u)) is a sigmoid function
    
    // Calculate inputs to E and I populations
    double inputE = excitationStrength_ * excitation_ - 
                    inhibitionStrength_ * inhibition_ + 
                    externalDrive;
    
    double inputI = excitationStrength_ * excitation_ - 
                    0.2 * inhibition_; // Weaker self-inhibition than inhibition of excitatory cells
    
    // Apply sigmoid functions
    double sigmoidE = 1.0 / (1.0 + std::exp(-sigmoidSlopeE_ * inputE));
    double sigmoidI = 1.0 / (1.0 + std::exp(-sigmoidSlopeI_ * inputI));
    
    // Update E and I using Euler method
    double dE = (-excitation_ / timeConstantE_ + (1.0 - excitation_) * sigmoidE) * dt;
    double dI = (-inhibition_ / timeConstantI_ + (1.0 - inhibition_) * sigmoidI) * dt;
    
    excitation_ += dE;
    inhibition_ += dI;
    
    // Ensure E and I remain in [0, 1]
    excitation_ = std::max(0.0, std::min(1.0, excitation_));
    inhibition_ = std::max(0.0, std::min(1.0, inhibition_));
    
    // Add stochastic component
    excitation_ += generateNoise(0.0, 0.01) * dt;
    inhibition_ += generateNoise(0.0, 0.005) * dt;
    
    // Recalculate amplitude and phase based on E and I
    // Amplitude is derived from the magnitude of oscillation
    amplitude_ = std::sqrt(excitation_*excitation_ + inhibition_*inhibition_);
    
    // Phase is derived from the relationship between E and I
    phase_ = std::atan2(inhibition_, excitation_);
    
    // Apply natural decay to amplitude
    amplitude_ *= (1.0 - decayRate_ * dt);
}

double WilsonCowanOscillator::getValue() const {
    // Output value is the excitatory population activity
    return excitation_;
}

double WilsonCowanOscillator::getExcitation() const {
    return excitation_;
}

double WilsonCowanOscillator::getInhibition() const {
    return inhibition_;
}

void WilsonCowanOscillator::setExcitation(double excitation) {
    excitation_ = std::max(0.0, std::min(1.0, excitation));
}

void WilsonCowanOscillator::setInhibition(double inhibition) {
    inhibition_ = std::max(0.0, std::min(1.0, inhibition));
}

double WilsonCowanOscillator::getExcitationStrength() const {
    return excitationStrength_;
}

double WilsonCowanOscillator::getInhibitionStrength() const {
    return inhibitionStrength_;
}

void WilsonCowanOscillator::setExcitationStrength(double strength) {
    excitationStrength_ = strength;
}

void WilsonCowanOscillator::setInhibitionStrength(double strength) {
    inhibitionStrength_ = strength;
}

void WilsonCowanOscillator::setTimeConstants(double excitationTC, double inhibitionTC) {
    timeConstantE_ = excitationTC;
    timeConstantI_ = inhibitionTC;
}

void WilsonCowanOscillator::setSigmoidSlopes(double excitationSlope, double inhibitionSlope) {
    sigmoidSlopeE_ = excitationSlope;
    sigmoidSlopeI_ = inhibitionSlope;
}

//-----------------------------------------------------------------------------
// BrainWaveOscillator Implementation
//-----------------------------------------------------------------------------

BrainWaveOscillator::BrainWaveOscillator(FrequencyBand band, double amplitude, double phase,
                                       double decayRate)
    : BaseOscillator(getDefaultFrequency(band), amplitude, phase, decayRate),
      band_(band),
      bandwidthFactor_(0.1), // 10% of center frequency
      harmonicStrength_(0.2),
      irregularityFactor_(0.05) {
}

void BrainWaveOscillator::updateDynamics(double dt, double externalDrive) {
    // This oscillator models realistic brain waves with:
    // 1. Central frequency with bandwidth
    // 2. Harmonics
    // 3. Physiological irregularity
    // 4. Amplitude modulation
    
    // Calculate basic phase progression based on frequency
    double baseOmega = 2.0 * M_PI * frequency_;
    
    // Add bandwidth - frequency varies around the central frequency
    // Bandwidth increases with frequency (alpha has wider band than delta)
    double bandwidth = frequency_ * bandwidthFactor_;
    double frequencyVariation = bandwidth * (2.0 * (std::sin(0.1 * timeStep_) + 0.5) - 1.0);
    double omega = baseOmega + 2.0 * M_PI * frequencyVariation;
    
    // Base phase update with natural frequency and drive
    double dphase = omega * dt + 0.2 * externalDrive * dt;
    
    // Add irregularity (small random variations in rhythm)
    dphase += irregularityFactor_ * generateNoise(0.0, 1.0) * dt;
    
    // Update phase
    phase_ += dphase;
    
    // Calculate amplitude modulation (slow fluctuations in amplitude)
    double amplitudeModulation = 0.98 + 0.04 * std::sin(0.2 * timeStep_);
    
    // External drive effect on amplitude
    double driveEffect = 0.1 * externalDrive * dt;
    
    // Apply natural decay and external influences to amplitude
    amplitude_ = amplitude_ * (1.0 - decayRate_ * dt) * amplitudeModulation + driveEffect;
    
    // Add stochastic component to amplitude
    amplitude_ += generateNoise(0.0, 0.02) * dt;
}

double BrainWaveOscillator::getValue() const {
    // Generate a more complex waveform than simple sine wave
    // by adding harmonics and phase-amplitude coupling
    
    // Fundamental component
    double value = amplitude_ * std::sin(phase_);
    
    // Add second harmonic (twice the frequency)
    value += amplitude_ * harmonicStrength_ * std::sin(2.0 * phase_);
    
    // Add third harmonic (three times the frequency)
    value += amplitude_ * harmonicStrength_ * 0.5 * std::sin(3.0 * phase_);
    
    return value;
}

FrequencyBand BrainWaveOscillator::getBand() const {
    return band_;
}

void BrainWaveOscillator::setBandwidthFactor(double factor) {
    bandwidthFactor_ = std::max(0.0, std::min(0.5, factor));
}

double BrainWaveOscillator::getBandwidthFactor() const {
    return bandwidthFactor_;
}

void BrainWaveOscillator::setHarmonicStrength(double strength) {
    harmonicStrength_ = std::max(0.0, std::min(1.0, strength));
}

double BrainWaveOscillator::getHarmonicStrength() const {
    return harmonicStrength_;
}

void BrainWaveOscillator::setIrregularityFactor(double factor) {
    irregularityFactor_ = std::max(0.0, std::min(0.5, factor));
}

double BrainWaveOscillator::getIrregularityFactor() const {
    return irregularityFactor_;
}

double BrainWaveOscillator::getDefaultFrequency(FrequencyBand band) {
    switch (band) {
        case FrequencyBand::DELTA:
            return 2.0;  // 1-4 Hz, center at 2 Hz
        case FrequencyBand::THETA:
            return 6.0;  // 4-8 Hz, center at 6 Hz
        case FrequencyBand::ALPHA:
            return 10.0; // 8-12 Hz, center at 10 Hz
        case FrequencyBand::BETA:
            return 20.0; // 13-30 Hz, center at 20 Hz
        case FrequencyBand::GAMMA:
            return 40.0; // 30-100 Hz, center at 40 Hz
        default:
            return 10.0; // Default to alpha
    }
}

//-----------------------------------------------------------------------------
// OscillatorFactory Implementation
//-----------------------------------------------------------------------------

std::unique_ptr<BaseOscillator> OscillatorFactory::createOscillator(
    OscillatorType type, 
    double frequency,
    double amplitude,
    double phase,
    double decayRate) {
    
    switch (type) {
        case OscillatorType::VAN_DER_POL:
            return std::make_unique<VanDerPolOscillator>(
                frequency, amplitude, phase, 1.0, decayRate);
            
        case OscillatorType::KURAMOTO:
            return std::make_unique<KuramotoOscillator>(
                frequency, amplitude, phase, decayRate);
            
        case OscillatorType::WILSON_COWAN:
            return std::make_unique<WilsonCowanOscillator>(
                frequency, amplitude, phase, 1.2, 1.0, decayRate);
            
        default:
            // Default to Kuramoto as it's simplest
            return std::make_unique<KuramotoOscillator>(
                frequency, amplitude, phase, decayRate);
    }
}

std::unique_ptr<BrainWaveOscillator> OscillatorFactory::createBrainWaveOscillator(
    FrequencyBand band,
    double amplitude,
    double phase,
    double decayRate) {
    
    return std::make_unique<BrainWaveOscillator>(
        band, amplitude, phase, decayRate);
}

} // namespace biological_timing
} // namespace core_neural
} // namespace ultra