/**
 * @file phase_coupling.cpp
 * @brief Implementation of phase coupling mechanisms for biological timing circuits
 * 
 * This file implements various coupling mechanisms that simulate how different
 * brain oscillations interact and synchronize, including phase-phase coupling,
 * phase-amplitude coupling, cross-frequency coupling, and other neurophysiologically
 * relevant phenomena that enable coordinated activity across neural ensembles.
 * 
 * <AUTHOR> Team
 * @date 2025
 */

#include "phase_coupling.h"
#include "oscillator.h"

#include <algorithm>
#include <cmath>
#include <complex>
#include <future>
#include <numeric>
#include <random>
#include <vector>

#include <Eigen/Dense>
#include <Eigen/Eigenvalues>
#include <unsupported/Eigen/FFT>

namespace ultra {
namespace core_neural {
namespace biological_timing {

//-----------------------------------------------------------------------------
// Utility Functions
//-----------------------------------------------------------------------------

namespace {
    /**
     * @brief Create complex exponential from phase
     * 
     * @param phase Phase angle in radians
     * @return Complex exponential e^(i*phase)
     */
    std::complex<double> phaseToComplex(double phase) {
        return std::complex<double>(std::cos(phase), std::sin(phase));
    }
    
    /**
     * @brief Extract phase from complex number
     * 
     * @param z Complex number
     * @return Phase angle in radians
     */
    double complexToPhase(const std::complex<double>& z) {
        return std::arg(z);
    }
    
    /**
     * @brief Calculate circular mean of phase angles
     * 
     * @param phases Vector of phase angles in radians
     * @return Mean phase angle in radians
     */
    double circularMean(const std::vector<double>& phases) {
        if (phases.empty()) {
            return 0.0;
        }
        
        double sumSin = 0.0;
        double sumCos = 0.0;
        
        for (double phase : phases) {
            sumSin += std::sin(phase);
            sumCos += std::cos(phase);
        }
        
        return std::atan2(sumSin, sumCos);
    }
    
    /**
     * @brief Calculate circular variance of phase angles
     * 
     * @param phases Vector of phase angles in radians
     * @return Circular variance (0-1)
     */
    double circularVariance(const std::vector<double>& phases) {
        if (phases.empty()) {
            return 1.0; // Maximum variance
        }
        
        double sumSin = 0.0;
        double sumCos = 0.0;
        
        for (double phase : phases) {
            sumSin += std::sin(phase);
            sumCos += std::cos(phase);
        }
        
        // Calculate resultant vector length R
        double R = std::sqrt(sumSin*sumSin + sumCos*sumCos) / phases.size();
        
        // Circular variance is 1 - R
        return 1.0 - R;
    }
    
    /**
     * @brief Compute n:m phase locking value
     * 
     * @param phases1 Phases of first oscillator
     * @param phases2 Phases of second oscillator
     * @param n First integer for n:m ratio
     * @param m Second integer for n:m ratio
     * @return Phase locking value (0-1)
     */
    double phaseLockingValue(const std::vector<double>& phases1, 
                           const std::vector<double>& phases2,
                           int n, int m) {
        size_t minSize = std::min(phases1.size(), phases2.size());
        
        if (minSize == 0) {
            return 0.0;
        }
        
        std::complex<double> sum(0.0, 0.0);
        
        for (size_t i = 0; i < minSize; i++) {
            // Calculate n:m phase difference
            double phaseDiff = n * phases1[i] - m * phases2[i];
            sum += phaseToComplex(phaseDiff);
        }
        
        // Magnitude of the average complex vector gives the PLV
        return std::abs(sum) / static_cast<double>(minSize);
    }
    
    /**
     * @brief Compute phase-amplitude coupling index
     * 
     * @param phases Phases of slow oscillator
     * @param amplitudes Amplitudes of fast oscillator
     * @param numBins Number of phase bins
     * @return Modulation index (0-1)
     */
    double modulationIndex(const std::vector<double>& phases, 
                          const std::vector<double>& amplitudes,
                          unsigned int numBins = 18) {
        size_t minSize = std::min(phases.size(), amplitudes.size());
        
        if (minSize == 0) {
            return 0.0;
        }
        
        // Create phase bins
        std::vector<double> binAmplitudes(numBins, 0.0);
        std::vector<unsigned int> binCounts(numBins, 0);
        
        // Assign amplitude values to phase bins
        for (size_t i = 0; i < minSize; i++) {
            // Convert phase (0 to 2π) to bin index (0 to numBins-1)
            unsigned int binIndex = static_cast<unsigned int>(
                std::fmod(phases[i] / (2.0 * M_PI) * numBins, numBins));
            
            binAmplitudes[binIndex] += amplitudes[i];
            binCounts[binIndex]++;
        }
        
        // Calculate mean amplitude for each phase bin
        for (unsigned int i = 0; i < numBins; i++) {
            if (binCounts[i] > 0) {
                binAmplitudes[i] /= binCounts[i];
            }
        }
        
        // Normalize the distribution
        double sumAmplitudes = std::accumulate(binAmplitudes.begin(), binAmplitudes.end(), 0.0);
        if (sumAmplitudes > 0.0) {
            for (unsigned int i = 0; i < numBins; i++) {
                binAmplitudes[i] /= sumAmplitudes;
            }
        }
        
        // Calculate Kullback-Leibler divergence from uniform distribution
        double uniformProb = 1.0 / numBins;
        double klDivergence = 0.0;
        
        for (unsigned int i = 0; i < numBins; i++) {
            if (binAmplitudes[i] > 0.0) {
                klDivergence += binAmplitudes[i] * std::log(binAmplitudes[i] / uniformProb);
            }
        }
        
        // Normalize to [0, 1] range
        double modIndex = klDivergence / std::log(numBins);
        return modIndex;
    }
    
    /**
     * @brief Compute wavelet transform for time-frequency analysis
     * 
     * @param signal Input signal vector
     * @param fs Sampling frequency
     * @param freqMin Minimum frequency of interest
     * @param freqMax Maximum frequency of interest
     * @param numFreqs Number of frequency bins
     * @return Matrix of complex wavelet coefficients (time x frequency)
     */
    Eigen::MatrixXcd computeWaveletTransform(const std::vector<double>& signal,
                                           double fs,
                                           double freqMin,
                                           double freqMax,
                                           unsigned int numFreqs) {
        // Calculate frequencies logarithmically spaced
        std::vector<double> frequencies(numFreqs);
        double logFreqMin = std::log(freqMin);
        double logFreqMax = std::log(freqMax);
        double logStep = (logFreqMax - logFreqMin) / (numFreqs - 1);
        
        for (unsigned int i = 0; i < numFreqs; i++) {
            frequencies[i] = std::exp(logFreqMin + i * logStep);
        }
        
        // Create wavelet transform matrix
        Eigen::MatrixXcd waveletMatrix(signal.size(), numFreqs);
        
        // Process each frequency
        #pragma omp parallel for
        for (unsigned int fi = 0; fi < numFreqs; fi++) {
            double freq = frequencies[fi];
            
            // Determine wavelet parameters
            double omega0 = 6.0; // Morlet wavelet parameter
            double dt = 1.0 / fs;
            
            // Calculate appropriate time width for this frequency
            double sigma_t = 1.0 / (2.0 * M_PI * freq);
            
            // Create wavelet in time domain
            int halfWidth = static_cast<int>(6.0 * sigma_t * fs);
            std::vector<std::complex<double>> wavelet(2 * halfWidth + 1);
            
            for (int t = -halfWidth; t <= halfWidth; t++) {
                double t_val = t * dt;
                // Morlet wavelet formula
                double gaussian = std::exp(-0.5 * (t_val / sigma_t) * (t_val / sigma_t));
                std::complex<double> oscillation = std::exp(std::complex<double>(0, 2.0 * M_PI * freq * t_val));
                wavelet[t + halfWidth] = gaussian * oscillation;
            }
            
            // Normalize wavelet
            double sumSquared = 0.0;
            for (const auto& w : wavelet) {
                sumSquared += std::norm(w);
            }
            double norm = std::sqrt(sumSquared * dt);
            
            for (auto& w : wavelet) {
                w /= norm;
            }
            
            // Convolve signal with wavelet
            for (size_t ti = 0; ti < signal.size(); ti++) {
                std::complex<double> sum(0.0, 0.0);
                
                for (int k = -halfWidth; k <= halfWidth; k++) {
                    int idx = static_cast<int>(ti) + k;
                    if (idx >= 0 && idx < static_cast<int>(signal.size())) {
                        sum += signal[idx] * std::conj(wavelet[k + halfWidth]);
                    }
                }
                
                waveletMatrix(ti, fi) = sum;
            }
        }
        
        return waveletMatrix;
    }
}

//-----------------------------------------------------------------------------
// BaseCouplingMechanism Implementation
//-----------------------------------------------------------------------------

BaseCouplingMechanism::BaseCouplingMechanism(double strength)
    : strength_(strength),
      enabled_(true) {
}

BaseCouplingMechanism::~BaseCouplingMechanism() = default;

double BaseCouplingMechanism::getStrength() const {
    return strength_;
}

void BaseCouplingMechanism::setStrength(double strength) {
    strength_ = strength;
}

bool BaseCouplingMechanism::isEnabled() const {
    return enabled_;
}

void BaseCouplingMechanism::enable() {
    enabled_ = true;
}

void BaseCouplingMechanism::disable() {
    enabled_ = false;
}

//-----------------------------------------------------------------------------
// PhaseToPhase Implementation
//-----------------------------------------------------------------------------

PhaseToPhase::PhaseToPhase(BaseOscillator* source, BaseOscillator* target, 
                         double strength, int n, int m)
    : BaseCouplingMechanism(strength),
      source_(source),
      target_(target),
      n_(n),
      m_(m) {
}

void PhaseToPhase::apply(double dt) {
    if (!enabled_ || !source_ || !target_) {
        return;
    }
    
    // Get current phases
    double sourcePhase = source_->getPhase();
    double targetPhase = target_->getPhase();
    
    // Calculate phase difference (n:m phase locking)
    double phaseDiff = (n_ * sourcePhase) - (m_ * targetPhase);
    while (phaseDiff > M_PI) phaseDiff -= 2.0 * M_PI;
    while (phaseDiff < -M_PI) phaseDiff += 2.0 * M_PI;
    
    // Apply coupling - move target phase toward n:m phase-locked state
    double phaseAdjustment = strength_ * std::sin(phaseDiff) * dt;
    double newPhase = targetPhase + phaseAdjustment / m_; // Divide by m for n:m ratio
    
    // Apply the new phase to target oscillator
    target_->setPhase(newPhase);
}

double PhaseToPhase::measure() const {
    if (!enabled_ || !source_ || !target_) {
        return 0.0;
    }
    
    // Get phase histories
    const std::vector<double>& sourcePhases = source_->getPhaseHistory();
    const std::vector<double>& targetPhases = target_->getPhaseHistory();
    
    // Calculate n:m phase locking value
    return phaseLockingValue(sourcePhases, targetPhases, n_, m_);
}

BaseOscillator* PhaseToPhase::getSource() const {
    return source_;
}

BaseOscillator* PhaseToPhase::getTarget() const {
    return target_;
}

int PhaseToPhase::getN() const {
    return n_;
}

int PhaseToPhase::getM() const {
    return m_;
}

void PhaseToPhase::setNM(int n, int m) {
    if (n > 0 && m > 0) {
        n_ = n;
        m_ = m;
    }
}

//-----------------------------------------------------------------------------
// PhaseToAmplitude Implementation
//-----------------------------------------------------------------------------

PhaseToAmplitude::PhaseToAmplitude(BaseOscillator* phaseSource, BaseOscillator* amplitudeTarget, 
                                 double strength, double phaseOffset)
    : BaseCouplingMechanism(strength),
      phaseSource_(phaseSource),
      amplitudeTarget_(amplitudeTarget),
      phaseOffset_(phaseOffset),
      modIndex_(0.0),
      preferredPhase_(0.0) {
}

void PhaseToAmplitude::apply(double dt) {
    if (!enabled_ || !phaseSource_ || !amplitudeTarget_) {
        return;
    }
    
    // Get current phase from phase source
    double phase = phaseSource_->getPhase();
    
    // Get current amplitude from amplitude target
    double amplitude = amplitudeTarget_->getAmplitude();
    
    // Calculate modulation factor (1 + strength * cos(phase - preferredPhase))
    double modulation = 1.0 + strength_ * std::cos(phase - preferredPhase_ - phaseOffset_);
    
    // Apply modulation to target's amplitude
    amplitudeTarget_->setAmplitude(amplitude * modulation);
}

double PhaseToAmplitude::measure() const {
    if (!enabled_ || !phaseSource_ || !amplitudeTarget_) {
        return 0.0;
    }
    
    // Get phase history from phase source
    const std::vector<double>& phases = phaseSource_->getPhaseHistory();
    
    // Get amplitude history from amplitude target
    const std::vector<double>& amplitudes = amplitudeTarget_->getAmplitudeHistory();
    
    // Calculate modulation index
    return modulationIndex(phases, amplitudes);
}

void PhaseToAmplitude::estimatePreferredPhase() {
    if (!enabled_ || !phaseSource_ || !amplitudeTarget_) {
        return;
    }
    
    // Get phase history from phase source
    const std::vector<double>& phases = phaseSource_->getPhaseHistory();
    
    // Get amplitude history from amplitude target
    const std::vector<double>& amplitudes = amplitudeTarget_->getAmplitudeHistory();
    
    size_t minSize = std::min(phases.size(), amplitudes.size());
    
    if (minSize < 10) {
        return; // Not enough data
    }
    
    // Calculate sum of unit vectors weighted by amplitude
    double sumSin = 0.0;
    double sumCos = 0.0;
    
    for (size_t i = 0; i < minSize; i++) {
        sumSin += amplitudes[i] * std::sin(phases[i]);
        sumCos += amplitudes[i] * std::cos(phases[i]);
    }
    
    // Preferred phase is the phase of the resultant vector
    preferredPhase_ = std::atan2(sumSin, sumCos);
    
    // Also calculate modulation index
    modIndex_ = modulationIndex(phases, amplitudes);
}

BaseOscillator* PhaseToAmplitude::getPhaseSource() const {
    return phaseSource_;
}

BaseOscillator* PhaseToAmplitude::getAmplitudeTarget() const {
    return amplitudeTarget_;
}

double PhaseToAmplitude::getPhaseOffset() const {
    return phaseOffset_;
}

void PhaseToAmplitude::setPhaseOffset(double offset) {
    phaseOffset_ = offset;
    while (phaseOffset_ > M_PI) phaseOffset_ -= 2.0 * M_PI;
    while (phaseOffset_ < -M_PI) phaseOffset_ += 2.0 * M_PI;
}

double PhaseToAmplitude::getPreferredPhase() const {
    return preferredPhase_;
}

void PhaseToAmplitude::setPreferredPhase(double phase) {
    preferredPhase_ = phase;
    while (preferredPhase_ > M_PI) preferredPhase_ -= 2.0 * M_PI;
    while (preferredPhase_ < -M_PI) preferredPhase_ += 2.0 * M_PI;
}

double PhaseToAmplitude::getModulationIndex() const {
    return modIndex_;
}

//-----------------------------------------------------------------------------
// FrequencyPullCoupling Implementation
//-----------------------------------------------------------------------------

FrequencyPullCoupling::FrequencyPullCoupling(BaseOscillator* source, BaseOscillator* target, 
                                           double strength, double pullFactor)
    : BaseCouplingMechanism(strength),
      source_(source),
      target_(target),
      pullFactor_(pullFactor),
      maxPullHz_(5.0) {
}

void FrequencyPullCoupling::apply(double dt) {
    if (!enabled_ || !source_ || !target_) {
        return;
    }
    
    // Get current frequencies
    double sourceFreq = source_->getFrequency();
    double targetFreq = target_->getFrequency();
    
    // Calculate frequency difference
    double freqDiff = sourceFreq - targetFreq;
    
    // Limit maximum frequency pull to prevent instability
    if (freqDiff > maxPullHz_) freqDiff = maxPullHz_;
    if (freqDiff < -maxPullHz_) freqDiff = -maxPullHz_;
    
    // Calculate frequency adjustment based on difference and coupling strength
    double freqAdjustment = strength_ * pullFactor_ * freqDiff;
    
    // Apply the new frequency to target oscillator
    double newFreq = targetFreq + freqAdjustment;
    
    // Ensure frequency stays positive
    if (newFreq < 0.1) newFreq = 0.1;
    
    target_->setFrequency(newFreq);
}

double FrequencyPullCoupling::measure() const {
    if (!enabled_ || !source_ || !target_) {
        return 0.0;
    }
    
    // Get current frequencies
    double sourceFreq = source_->getFrequency();
    double targetFreq = target_->getFrequency();
    
    // Return 1.0 for perfect synchronization, 0.0 for no synchronization
    double freqRatio = std::min(sourceFreq, targetFreq) / std::max(sourceFreq, targetFreq);
    
    return freqRatio;
}

BaseOscillator* FrequencyPullCoupling::getSource() const {
    return source_;
}

BaseOscillator* FrequencyPullCoupling::getTarget() const {
    return target_;
}

double FrequencyPullCoupling::getPullFactor() const {
    return pullFactor_;
}

void FrequencyPullCoupling::setPullFactor(double factor) {
    pullFactor_ = factor;
}

double FrequencyPullCoupling::getMaxPullHz() const {
    return maxPullHz_;
}

void FrequencyPullCoupling::setMaxPullHz(double maxPull) {
    if (maxPull > 0.0) {
        maxPullHz_ = maxPull;
    }
}

//-----------------------------------------------------------------------------
// AmplitudeEnvelope Implementation
//-----------------------------------------------------------------------------

AmplitudeEnvelope::AmplitudeEnvelope(BaseOscillator* source, BaseOscillator* target, 
                                   double strength, double baseline, double modulationDepth)
    : BaseCouplingMechanism(strength),
      source_(source),
      target_(target),
      baseline_(baseline),
      modulationDepth_(modulationDepth),
      updateFactor_(0.1) {
}

void AmplitudeEnvelope::apply(double dt) {
    if (!enabled_ || !source_ || !target_) {
        return;
    }
    
    // Get source oscillator value (not amplitude but actual oscillation)
    double sourceValue = source_->getValue();
    
    // Normalize source value to [-1, 1] range
    double normalizedValue = sourceValue / source_->getAmplitude();
    
    // Calculate envelope modulation factor
    double modulation = baseline_ + modulationDepth_ * (0.5 + 0.5 * normalizedValue);
    
    // Apply envelope to target amplitude with smoothing to prevent sharp changes
    double currentTargetAmplitude = target_->getAmplitude();
    double newTargetAmplitude = currentTargetAmplitude * (1.0 - updateFactor_) + 
                                (modulation * strength_) * updateFactor_;
    
    // Set new target amplitude
    target_->setAmplitude(newTargetAmplitude);
}

double AmplitudeEnvelope::measure() const {
    if (!enabled_ || !source_ || !target_) {
        return 0.0;
    }
    
    // Get value histories
    const std::vector<double>& sourceValues = source_->getValueHistory();
    const std::vector<double>& targetAmplitudes = target_->getAmplitudeHistory();
    
    size_t minSize = std::min(sourceValues.size(), targetAmplitudes.size());
    
    if (minSize < 10) {
        return 0.0; // Not enough data
    }
    
    // Calculate correlation between source value and target amplitude
    double sumSourceValue = 0.0;
    double sumTargetAmplitude = 0.0;
    double sumSourceValueSq = 0.0;
    double sumTargetAmplitudeSq = 0.0;
    double sumProduct = 0.0;
    
    for (size_t i = 0; i < minSize; i++) {
        sumSourceValue += sourceValues[i];
        sumTargetAmplitude += targetAmplitudes[i];
        sumSourceValueSq += sourceValues[i] * sourceValues[i];
        sumTargetAmplitudeSq += targetAmplitudes[i] * targetAmplitudes[i];
        sumProduct += sourceValues[i] * targetAmplitudes[i];
    }
    
    double meanSourceValue = sumSourceValue / minSize;
    double meanTargetAmplitude = sumTargetAmplitude / minSize;
    
    double covariance = sumProduct / minSize - meanSourceValue * meanTargetAmplitude;
    double varianceSource = sumSourceValueSq / minSize - meanSourceValue * meanSourceValue;
    double varianceTarget = sumTargetAmplitudeSq / minSize - meanTargetAmplitude * meanTargetAmplitude;
    
    if (varianceSource <= 0.0 || varianceTarget <= 0.0) {
        return 0.0;
    }
    
    double correlation = covariance / (std::sqrt(varianceSource) * std::sqrt(varianceTarget));
    
    // Convert to absolute value as we're interested in the strength, not direction
    return std::abs(correlation);
}

BaseOscillator* AmplitudeEnvelope::getSource() const {
    return source_;
}

BaseOscillator* AmplitudeEnvelope::getTarget() const {
    return target_;
}

double AmplitudeEnvelope::getBaseline() const {
    return baseline_;
}

void AmplitudeEnvelope::setBaseline(double baseline) {
    baseline_ = baseline;
}

double AmplitudeEnvelope::getModulationDepth() const {
    return modulationDepth_;
}

void AmplitudeEnvelope::setModulationDepth(double depth) {
    modulationDepth_ = depth;
}

double AmplitudeEnvelope::getUpdateFactor() const {
    return updateFactor_;
}

void AmplitudeEnvelope::setUpdateFactor(double factor) {
    if (factor > 0.0 && factor <= 1.0) {
        updateFactor_ = factor;
    }
}

//-----------------------------------------------------------------------------
// CrossFrequencyAnalyzer Implementation
//-----------------------------------------------------------------------------

CrossFrequencyAnalyzer::CrossFrequencyAnalyzer(BaseOscillator* slowOscillator, 
                                             BaseOscillator* fastOscillator)
    : slowOscillator_(slowOscillator),
      fastOscillator_(fastOscillator),
      numPhaseBins_(18),
      preferredPhase_(0.0),
      modulationIndex_(0.0),
      coherence_(0.0) {
}

void CrossFrequencyAnalyzer::analyze() {
    if (!slowOscillator_ || !fastOscillator_) {
        return;
    }
    
    // Get data from oscillators
    const std::vector<double>& slowPhases = slowOscillator_->getPhaseHistory();
    const std::vector<double>& fastAmplitudes = fastOscillator_->getAmplitudeHistory();
    const std::vector<double>& fastPhases = fastOscillator_->getPhaseHistory();
    
    // Calculate phase-amplitude coupling
    calculatePAC(slowPhases, fastAmplitudes);
    
    // Calculate phase-phase coupling
    calculatePPC(slowPhases, fastPhases);
}

void CrossFrequencyAnalyzer::calculatePAC(const std::vector<double>& slowPhases, 
                                        const std::vector<double>& fastAmplitudes) {
    size_t minSize = std::min(slowPhases.size(), fastAmplitudes.size());
    
    if (minSize < 10) {
        modulationIndex_ = 0.0;
        preferredPhase_ = 0.0;
        amplitudeDistribution_.clear();
        return;
    }
    
    // Create phase bins for modulation index calculation
    amplitudeDistribution_.resize(numPhaseBins_, 0.0);
    std::vector<unsigned int> binCounts(numPhaseBins_, 0);
    
    // Assign amplitude values to phase bins
    for (size_t i = 0; i < minSize; i++) {
        // Convert phase (0 to 2π) to bin index (0 to numPhaseBins_-1)
        unsigned int binIndex = static_cast<unsigned int>(
            std::fmod(slowPhases[i] / (2.0 * M_PI) * numPhaseBins_, numPhaseBins_));
        
        amplitudeDistribution_[binIndex] += fastAmplitudes[i];
        binCounts[binIndex]++;
    }
    
    // Calculate mean amplitude for each phase bin
    for (unsigned int i = 0; i < numPhaseBins_; i++) {
        if (binCounts[i] > 0) {
            amplitudeDistribution_[i] /= binCounts[i];
        }
    }
    
    // Find preferred phase - bin with maximum amplitude
    auto maxBin = std::max_element(amplitudeDistribution_.begin(), amplitudeDistribution_.end());
    if (maxBin != amplitudeDistribution_.end()) {
        preferredPhase_ = (std::distance(amplitudeDistribution_.begin(), maxBin) + 0.5) 
                         * 2.0 * M_PI / numPhaseBins_;
    }
    
    // Normalize the distribution for modulation index calculation
    double sumAmplitudes = std::accumulate(amplitudeDistribution_.begin(), 
                                         amplitudeDistribution_.end(), 0.0);
    if (sumAmplitudes > 0.0) {
        for (unsigned int i = 0; i < numPhaseBins_; i++) {
            amplitudeDistribution_[i] /= sumAmplitudes;
        }
    }
    
    // Calculate Kullback-Leibler divergence from uniform distribution
    double uniformProb = 1.0 / numPhaseBins_;
    double klDivergence = 0.0;
    
    for (unsigned int i = 0; i < numPhaseBins_; i++) {
        if (amplitudeDistribution_[i] > 0.0) {
            klDivergence += amplitudeDistribution_[i] 
                         * std::log(amplitudeDistribution_[i] / uniformProb);
        }
    }
    
    // Normalize to [0, 1] range for modulation index
    modulationIndex_ = klDivergence / std::log(numPhaseBins_);
}

void CrossFrequencyAnalyzer::calculatePPC(const std::vector<double>& slowPhases, 
                                        const std::vector<double>& fastPhases) {
    // Calculate phase-phase coupling for most likely n:m ratio
    
    // Common n:m ratios to check (from most common to least common in the brain)
    const std::vector<std::pair<int, int>> ratios = {
        {1, 1}, // 1:1 coupling
        {1, 2}, // 1:2 coupling
        {2, 3}, // 2:3 coupling
        {1, 3}, // 1:3 coupling
        {1, 4}, // 1:4 coupling
        {2, 5}  // 2:5 coupling
    };
    
    double bestPLV = 0.0;
    std::pair<int, int> bestRatio = {1, 1};
    
    // Find the best n:m ratio
    for (const auto& ratio : ratios) {
        int n = ratio.first;
        int m = ratio.second;
        
        double plv = phaseLockingValue(slowPhases, fastPhases, n, m);
        
        if (plv > bestPLV) {
            bestPLV = plv;
            bestRatio = ratio;
        }
    }
    
    nToMRatio_ = bestRatio;
    coherence_ = bestPLV;
}

double CrossFrequencyAnalyzer::getModulationIndex() const {
    return modulationIndex_;
}

double CrossFrequencyAnalyzer::getPreferredPhase() const {
    return preferredPhase_;
}

double CrossFrequencyAnalyzer::getCoherence() const {
    return coherence_;
}

std::pair<int, int> CrossFrequencyAnalyzer::getNToMRatio() const {
    return nToMRatio_;
}

const std::vector<double>& CrossFrequencyAnalyzer::getAmplitudeDistribution() const {
    return amplitudeDistribution_;
}

void CrossFrequencyAnalyzer::setNumPhaseBins(unsigned int bins) {
    if (bins > 0) {
        numPhaseBins_ = bins;
    }
}

unsigned int CrossFrequencyAnalyzer::getNumPhaseBins() const {
    return numPhaseBins_;
}

BaseOscillator* CrossFrequencyAnalyzer::getSlowOscillator() const {
    return slowOscillator_;
}

BaseOscillator* CrossFrequencyAnalyzer::getFastOscillator() const {
    return fastOscillator_;
}

//-----------------------------------------------------------------------------
// CouplingManager Implementation
//-----------------------------------------------------------------------------

CouplingManager::CouplingManager() {
}

CouplingManager::~CouplingManager() {
    // Clear all coupling mechanisms
    couplings_.clear();
}

void CouplingManager::addCoupling(std::unique_ptr<BaseCouplingMechanism> coupling) {
    if (coupling) {
        couplings_.push_back(std::move(coupling));
    }
}

void CouplingManager::applyCouplings(double dt) {
    for (auto& coupling : couplings_) {
        if (coupling && coupling->isEnabled()) {
            coupling->apply(dt);
        }
    }
}

void CouplingManager::updateCouplingStrengths(double globalFactor) {
    for (auto& coupling : couplings_) {
        if (coupling) {
            double currentStrength = coupling->getStrength();
            coupling->setStrength(currentStrength * globalFactor);
        }
    }
}

size_t CouplingManager::getNumCouplings() const {
    return couplings_.size();
}

void CouplingManager::removeCoupling(size_t index) {
    if (index < couplings_.size()) {
        couplings_.erase(couplings_.begin() + index);
    }
}

void CouplingManager::enableCoupling(size_t index, bool enable) {
    if (index < couplings_.size()) {
        if (enable) {
            couplings_[index]->enable();
        } else {
            couplings_[index]->disable();
        }
    }
}

std::vector<double> CouplingManager::measureAllCouplings() const {
    std::vector<double> measurements;
    measurements.reserve(couplings_.size());
    
    for (const auto& coupling : couplings_) {
        if (coupling) {
            measurements.push_back(coupling->measure());
        } else {
            measurements.push_back(0.0);
        }
    }
    
    return measurements;
}

BaseCouplingMechanism* CouplingManager::getCoupling(size_t index) {
    if (index < couplings_.size()) {
        return couplings_[index].get();
    }
    return nullptr;
}

void CouplingManager::clearAllCouplings() {
    couplings_.clear();
}

std::unique_ptr<PhaseToPhase> CouplingManager::createPhaseToPhase(
    BaseOscillator* source, 
    BaseOscillator* target, 
    double strength, 
    int n, 
    int m) {
    
    return std::make_unique<PhaseToPhase>(source, target, strength, n, m);
}

std::unique_ptr<PhaseToAmplitude> CouplingManager::createPhaseToAmplitude(
    BaseOscillator* phaseSource, 
    BaseOscillator* amplitudeTarget, 
    double strength, 
    double phaseOffset) {
    
    return std::make_unique<PhaseToAmplitude>(phaseSource, amplitudeTarget, strength, phaseOffset);
}

std::unique_ptr<FrequencyPullCoupling> CouplingManager::createFrequencyPullCoupling(
    BaseOscillator* source, 
    BaseOscillator* target, 
    double strength, 
    double pullFactor) {
    
    return std::make_unique<FrequencyPullCoupling>(source, target, strength, pullFactor);
}

std::unique_ptr<AmplitudeEnvelope> CouplingManager::createAmplitudeEnvelope(
    BaseOscillator* source, 
    BaseOscillator* target, 
    double strength, 
    double baseline, 
    double modulationDepth) {
    
    return std::make_unique<AmplitudeEnvelope>(source, target, strength, baseline, modulationDepth);
}

//-----------------------------------------------------------------------------
// OscillatorNetwork Implementation
//-----------------------------------------------------------------------------

OscillatorNetwork::OscillatorNetwork()
    : timeStep_(0.0) {
}

OscillatorNetwork::~OscillatorNetwork() {
    // Clear all oscillators
    oscillators_.clear();
}

void OscillatorNetwork::addOscillator(std::unique_ptr<BaseOscillator> oscillator, 
                                     const std::string& name) {
    if (oscillator) {
        // Check if name already exists
        if (oscillatorIndices_.find(name) != oscillatorIndices_.end()) {
            // Append a number to make the name unique
            int suffix = 1;
            std::string newName = name;
            while (oscillatorIndices_.find(newName) != oscillatorIndices_.end()) {
                newName = name + "_" + std::to_string(suffix);
                suffix++;
            }
            oscillatorIndices_[newName] = oscillators_.size();
        } else {
            oscillatorIndices_[name] = oscillators_.size();
        }
        
        oscillators_.push_back(std::move(oscillator));
    }
}

void OscillatorNetwork::update(double dt, 
                              const std::unordered_map<std::string, double>& externalDrives) {
    // First, apply couplings
    couplingManager_.applyCouplings(dt);
    
    // Then, update all oscillators
    for (size_t i = 0; i < oscillators_.size(); i++) {
        auto& oscillator = oscillators_[i];
        
        // Find external drive for this oscillator
        double drive = 0.0;
        for (const auto& nameIndex : oscillatorIndices_) {
            if (nameIndex.second == i) {
                // Found the name for this oscillator
                auto driveIt = externalDrives.find(nameIndex.first);
                if (driveIt != externalDrives.end()) {
                    drive = driveIt->second;
                }
                break;
            }
        }
        
        // Update the oscillator
        oscillator->update(dt, drive);
    }
    
    // Update time
    timeStep_ += dt;
}

void OscillatorNetwork::addPhaseToPhase(const std::string& sourceName, 
                                       const std::string& targetName, 
                                       double strength, 
                                       int n, 
                                       int m) {
    auto source = getOscillator(sourceName);
    auto target = getOscillator(targetName);
    
    if (source && target) {
        auto coupling = couplingManager_.createPhaseToPhase(source, target, strength, n, m);
        couplingManager_.addCoupling(std::move(coupling));
    }
}

void OscillatorNetwork::addPhaseToAmplitude(const std::string& sourceName, 
                                          const std::string& targetName, 
                                          double strength, 
                                          double phaseOffset) {
    auto source = getOscillator(sourceName);
    auto target = getOscillator(targetName);
    
    if (source && target) {
        auto coupling = couplingManager_.createPhaseToAmplitude(source, target, strength, phaseOffset);
        couplingManager_.addCoupling(std::move(coupling));
    }
}

void OscillatorNetwork::addFrequencyPull(const std::string& sourceName, 
                                        const std::string& targetName, 
                                        double strength, 
                                        double pullFactor) {
    auto source = getOscillator(sourceName);
    auto target = getOscillator(targetName);
    
    if (source && target) {
        auto coupling = couplingManager_.createFrequencyPullCoupling(source, target, strength, pullFactor);
        couplingManager_.addCoupling(std::move(coupling));
    }
}

void OscillatorNetwork::addAmplitudeEnvelope(const std::string& sourceName, 
                                           const std::string& targetName, 
                                           double strength, 
                                           double baseline, 
                                           double modulationDepth) {
    auto source = getOscillator(sourceName);
    auto target = getOscillator(targetName);
    
    if (source && target) {
        auto coupling = couplingManager_.createAmplitudeEnvelope(source, target, strength, baseline, modulationDepth);
        couplingManager_.addCoupling(std::move(coupling));
    }
}

BaseOscillator* OscillatorNetwork::getOscillator(const std::string& name) {
    auto it = oscillatorIndices_.find(name);
    if (it != oscillatorIndices_.end()) {
        size_t index = it->second;
        if (index < oscillators_.size()) {
            return oscillators_[index].get();
        }
    }
    return nullptr;
}

std::vector<std::string> OscillatorNetwork::getOscillatorNames() const {
    std::vector<std::string> names;
    names.reserve(oscillatorIndices_.size());
    
    // Create a map from index to name
    std::map<size_t, std::string> indexToName;
    for (const auto& pair : oscillatorIndices_) {
        indexToName[pair.second] = pair.first;
    }
    
    // Insert in order of index
    for (size_t i = 0; i < oscillators_.size(); i++) {
        auto it = indexToName.find(i);
        if (it != indexToName.end()) {
            names.push_back(it->second);
        }
    }
    
    return names;
}

double OscillatorNetwork::getTimeStep() const {
    return timeStep_;
}

size_t OscillatorNetwork::getNumOscillators() const {
    return oscillators_.size();
}

size_t OscillatorNetwork::getNumCouplings() const {
    return couplingManager_.getNumCouplings();
}

void OscillatorNetwork::analyzeCouplings() {
    // Analyze all possible cross-frequency couplings
    couplingAnalyses_.clear();
    
    // For each pair of oscillators
    for (size_t i = 0; i < oscillators_.size(); i++) {
        for (size_t j = 0; j < oscillators_.size(); j++) {
            if (i != j) {
                // Create analyzer with oscillator i as slow and j as fast
                CrossFrequencyAnalyzer analyzer(oscillators_[i].get(), oscillators_[j].get());
                analyzer.analyze();
                
                // Store results if significant coupling is found
                if (analyzer.getModulationIndex() > 0.05 || analyzer.getCoherence() > 0.1) {
                    couplingAnalyses_.push_back(analyzer);
                }
            }
        }
    }
}

const std::vector<CrossFrequencyAnalyzer>& OscillatorNetwork::getCouplingAnalyses() const {
    return couplingAnalyses_;
}

void OscillatorNetwork::resetNetwork() {
    // Reset time
    timeStep_ = 0.0;
    
    // Clear all oscillators
    oscillators_.clear();
    oscillatorIndices_.clear();
    
    // Clear all couplings
    couplingManager_.clearAllCouplings();
    
    // Clear analyses
    couplingAnalyses_.clear();
}

CouplingManager& OscillatorNetwork::getCouplingManager() {
    return couplingManager_;
}

} // namespace biological_timing
} // namespace core_neural
} // namespace ultra