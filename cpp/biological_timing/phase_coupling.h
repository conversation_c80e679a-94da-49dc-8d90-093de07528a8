/**
 * @file phase_coupling.h
 * @brief Header for phase coupling mechanisms in biological timing circuits
 * 
 * This file declares classes for simulating various coupling mechanisms between neural
 * oscillators, including phase-phase coupling, phase-amplitude coupling, frequency
 * pulling, and amplitude modulation. These mechanisms model how brain rhythms interact
 * and synchronize to coordinate processing across neural regions.
 * 
 * <AUTHOR> Team
 * @date 2025
 */

#ifndef ULTRA_CORE_NEURAL_BIOLOGICAL_TIMING_PHASE_COUPLING_H_
#define ULTRA_CORE_NEURAL_BIOLOGICAL_TIMING_PHASE_COUPLING_H_

#include <memory>
#include <string>
#include <unordered_map>
#include <vector>
#include <utility>

namespace ultra {
namespace core_neural {
namespace biological_timing {

// Forward declarations
class BaseOscillator;

/**
 * @brief Base class for all coupling mechanisms
 * 
 * Abstract base class that defines common interface for various coupling types
 */
class BaseCouplingMechanism {
public:
    /**
     * @brief Constructor
     * 
     * @param strength Coupling strength (0.0-1.0)
     */
    explicit BaseCouplingMechanism(double strength = 0.1);
    
    /**
     * @brief Virtual destructor
     */
    virtual ~BaseCouplingMechanism();
    
    /**
     * @brief Apply coupling effect for current time step
     * 
     * @param dt Time step in seconds
     */
    virtual void apply(double dt) = 0;
    
    /**
     * @brief Measure coupling strength from oscillator data
     * 
     * @return Measured coupling value (0.0-1.0)
     */
    virtual double measure() const = 0;
    
    /**
     * @brief Get coupling strength parameter
     * 
     * @return Current coupling strength
     */
    double getStrength() const;
    
    /**
     * @brief Set coupling strength parameter
     * 
     * @param strength New coupling strength (0.0-1.0)
     */
    void setStrength(double strength);
    
    /**
     * @brief Check if coupling is enabled
     * 
     * @return True if enabled, false otherwise
     */
    bool isEnabled() const;
    
    /**
     * @brief Enable coupling
     */
    void enable();
    
    /**
     * @brief Disable coupling
     */
    void disable();
    
protected:
    double strength_; ///< Coupling strength parameter
    bool enabled_;    ///< Whether coupling is active
};

/**
 * @brief Phase-to-phase coupling mechanism
 * 
 * Implements n:m phase locking between two oscillators
 */
class PhaseToPhase : public BaseCouplingMechanism {
public:
    /**
     * @brief Constructor
     * 
     * @param source Source oscillator
     * @param target Target oscillator affected by coupling
     * @param strength Coupling strength (0.0-1.0)
     * @param n First integer in n:m ratio
     * @param m Second integer in n:m ratio
     */
    PhaseToPhase(BaseOscillator* source, BaseOscillator* target, 
                double strength = 0.1, int n = 1, int m = 1);
    
    /**
     * @brief Apply phase coupling for current time step
     * 
     * @param dt Time step in seconds
     */
    void apply(double dt) override;
    
    /**
     * @brief Measure phase locking value
     * 
     * @return Phase locking value (0.0-1.0)
     */
    double measure() const override;
    
    /**
     * @brief Get source oscillator
     * 
     * @return Pointer to source oscillator
     */
    BaseOscillator* getSource() const;
    
    /**
     * @brief Get target oscillator
     * 
     * @return Pointer to target oscillator
     */
    BaseOscillator* getTarget() const;
    
    /**
     * @brief Get n value for n:m coupling
     * 
     * @return n value
     */
    int getN() const;
    
    /**
     * @brief Get m value for n:m coupling
     * 
     * @return m value
     */
    int getM() const;
    
    /**
     * @brief Set n:m ratio values
     * 
     * @param n First integer (must be positive)
     * @param m Second integer (must be positive)
     */
    void setNM(int n, int m);
    
private:
    BaseOscillator* source_; ///< Source oscillator
    BaseOscillator* target_; ///< Target oscillator
    int n_;                  ///< n value for n:m coupling
    int m_;                  ///< m value for n:m coupling
};

/**
 * @brief Phase-to-amplitude coupling mechanism
 * 
 * Implements modulation of one oscillator's amplitude by another's phase
 */
class PhaseToAmplitude : public BaseCouplingMechanism {
public:
    /**
     * @brief Constructor
     * 
     * @param phaseSource Oscillator providing phase
     * @param amplitudeTarget Oscillator whose amplitude is modulated
     * @param strength Coupling strength (0.0-1.0)
     * @param phaseOffset Phase offset in radians
     */
    PhaseToAmplitude(BaseOscillator* phaseSource, BaseOscillator* amplitudeTarget, 
                    double strength = 0.1, double phaseOffset = 0.0);
    
    /**
     * @brief Apply amplitude modulation for current time step
     * 
     * @param dt Time step in seconds
     */
    void apply(double dt) override;
    
    /**
     * @brief Measure modulation index
     * 
     * @return Modulation index (0.0-1.0)
     */
    double measure() const override;
    
    /**
     * @brief Estimate preferred phase from oscillator data
     * 
     * Analyzes oscillator history to determine at which phase
     * of the source oscillator the target amplitude is maximal
     */
    void estimatePreferredPhase();
    
    /**
     * @brief Get phase source oscillator
     * 
     * @return Pointer to phase source oscillator
     */
    BaseOscillator* getPhaseSource() const;
    
    /**
     * @brief Get amplitude target oscillator
     * 
     * @return Pointer to amplitude target oscillator
     */
    BaseOscillator* getAmplitudeTarget() const;
    
    /**
     * @brief Get phase offset
     * 
     * @return Phase offset in radians
     */
    double getPhaseOffset() const;
    
    /**
     * @brief Set phase offset
     * 
     * @param offset New phase offset in radians
     */
    void setPhaseOffset(double offset);
    
    /**
     * @brief Get preferred phase
     * 
     * @return Preferred phase in radians
     */
    double getPreferredPhase() const;
    
    /**
     * @brief Set preferred phase
     * 
     * @param phase New preferred phase in radians
     */
    void setPreferredPhase(double phase);
    
    /**
     * @brief Get modulation index
     * 
     * @return Current modulation index
     */
    double getModulationIndex() const;
    
private:
    BaseOscillator* phaseSource_;     ///< Oscillator providing phase
    BaseOscillator* amplitudeTarget_; ///< Oscillator whose amplitude is modulated
    double phaseOffset_;              ///< Phase offset in radians
    double modIndex_;                 ///< Calculated modulation index
    double preferredPhase_;           ///< Phase at which amplitude is maximal
};

/**
 * @brief Frequency pull coupling mechanism
 * 
 * Implements gradual frequency synchronization between oscillators
 */
class FrequencyPullCoupling : public BaseCouplingMechanism {
public:
    /**
     * @brief Constructor
     * 
     * @param source Leader oscillator
     * @param target Follower oscillator
     * @param strength Coupling strength (0.0-1.0)
     * @param pullFactor Rate of frequency convergence (0.0-1.0)
     */
    FrequencyPullCoupling(BaseOscillator* source, BaseOscillator* target, 
                         double strength = 0.1, double pullFactor = 0.2);
    
    /**
     * @brief Apply frequency pull for current time step
     * 
     * @param dt Time step in seconds
     */
    void apply(double dt) override;
    
    /**
     * @brief Measure frequency synchronization
     * 
     * @return Frequency ratio (0.0-1.0)
     */
    double measure() const override;
    
    /**
     * @brief Get source oscillator
     * 
     * @return Pointer to source oscillator
     */
    BaseOscillator* getSource() const;
    
    /**
     * @brief Get target oscillator
     * 
     * @return Pointer to target oscillator
     */
    BaseOscillator* getTarget() const;
    
    /**
     * @brief Get pull factor
     * 
     * @return Current pull factor
     */
    double getPullFactor() const;
    
    /**
     * @brief Set pull factor
     * 
     * @param factor New pull factor
     */
    void setPullFactor(double factor);
    
    /**
     * @brief Get maximum pull in Hz
     * 
     * @return Maximum frequency change per update
     */
    double getMaxPullHz() const;
    
    /**
     * @brief Set maximum pull in Hz
     * 
     * @param maxPull New maximum pull value
     */
    void setMaxPullHz(double maxPull);
    
private:
    BaseOscillator* source_;  ///< Source oscillator
    BaseOscillator* target_;  ///< Target oscillator
    double pullFactor_;       ///< Rate of frequency convergence
    double maxPullHz_;        ///< Maximum frequency change per update
};

/**
 * @brief Amplitude envelope coupling mechanism
 * 
 * Implements modulation of target amplitude by source value envelope
 */
class AmplitudeEnvelope : public BaseCouplingMechanism {
public:
    /**
     * @brief Constructor
     * 
     * @param source Modulating oscillator
     * @param target Modulated oscillator
     * @param strength Coupling strength (0.0-1.0)
     * @param baseline Baseline amplitude factor (0.0-1.0)
     * @param modulationDepth Depth of modulation (0.0-1.0)
     */
    AmplitudeEnvelope(BaseOscillator* source, BaseOscillator* target, 
                     double strength = 0.1, double baseline = 0.5, 
                     double modulationDepth = 0.5);
    
    /**
     * @brief Apply amplitude modulation for current time step
     * 
     * @param dt Time step in seconds
     */
    void apply(double dt) override;
    
    /**
     * @brief Measure amplitude correlation
     * 
     * @return Correlation coefficient (0.0-1.0)
     */
    double measure() const override;
    
    /**
     * @brief Get source oscillator
     * 
     * @return Pointer to source oscillator
     */
    BaseOscillator* getSource() const;
    
    /**
     * @brief Get target oscillator
     * 
     * @return Pointer to target oscillator
     */
    BaseOscillator* getTarget() const;
    
    /**
     * @brief Get baseline amplitude factor
     * 
     * @return Current baseline value
     */
    double getBaseline() const;
    
    /**
     * @brief Set baseline amplitude factor
     * 
     * @param baseline New baseline value
     */
    void setBaseline(double baseline);
    
    /**
     * @brief Get modulation depth
     * 
     * @return Current modulation depth
     */
    double getModulationDepth() const;
    
    /**
     * @brief Set modulation depth
     * 
     * @param depth New modulation depth
     */
    void setModulationDepth(double depth);
    
    /**
     * @brief Get update factor
     * 
     * @return Current update factor
     */
    double getUpdateFactor() const;
    
    /**
     * @brief Set update factor
     * 
     * @param factor New update factor (0.0-1.0)
     */
    void setUpdateFactor(double factor);
    
private:
    BaseOscillator* source_;       ///< Source oscillator
    BaseOscillator* target_;       ///< Target oscillator
    double baseline_;              ///< Baseline amplitude factor
    double modulationDepth_;       ///< Depth of modulation
    double updateFactor_;          ///< Smoothing factor for updates
};

/**
 * @brief Cross-frequency coupling analyzer
 * 
 * Tool for analyzing coupling patterns between oscillators
 */
class CrossFrequencyAnalyzer {
public:
    /**
     * @brief Constructor
     * 
     * @param slowOscillator Lower frequency oscillator
     * @param fastOscillator Higher frequency oscillator
     */
    CrossFrequencyAnalyzer(BaseOscillator* slowOscillator, BaseOscillator* fastOscillator);
    
    /**
     * @brief Analyze coupling between oscillators
     * 
     * Performs phase-amplitude coupling and phase-phase coupling analysis
     */
    void analyze();
    
    /**
     * @brief Calculate phase-amplitude coupling
     * 
     * @param slowPhases Phase history of slow oscillator
     * @param fastAmplitudes Amplitude history of fast oscillator
     */
    void calculatePAC(const std::vector<double>& slowPhases, 
                     const std::vector<double>& fastAmplitudes);
    
    /**
     * @brief Calculate phase-phase coupling
     * 
     * @param slowPhases Phase history of slow oscillator
     * @param fastPhases Phase history of fast oscillator
     */
    void calculatePPC(const std::vector<double>& slowPhases, 
                     const std::vector<double>& fastPhases);
    
    /**
     * @brief Get modulation index
     * 
     * @return Phase-amplitude coupling modulation index
     */
    double getModulationIndex() const;
    
    /**
     * @brief Get preferred phase
     * 
     * @return Phase at which amplitude is maximal
     */
    double getPreferredPhase() const;
    
    /**
     * @brief Get phase-phase coherence
     * 
     * @return Phase locking value
     */
    double getCoherence() const;
    
    /**
     * @brief Get n:m ratio with highest coherence
     * 
     * @return Pair of integers representing n:m ratio
     */
    std::pair<int, int> getNToMRatio() const;
    
    /**
     * @brief Get amplitude distribution across phase bins
     * 
     * @return Vector of mean amplitudes for each phase bin
     */
    const std::vector<double>& getAmplitudeDistribution() const;
    
    /**
     * @brief Set number of phase bins
     * 
     * @param bins Number of phase bins for PAC analysis
     */
    void setNumPhaseBins(unsigned int bins);
    
    /**
     * @brief Get number of phase bins
     * 
     * @return Current number of phase bins
     */
    unsigned int getNumPhaseBins() const;
    
    /**
     * @brief Get slow oscillator
     * 
     * @return Pointer to slow oscillator
     */
    BaseOscillator* getSlowOscillator() const;
    
    /**
     * @brief Get fast oscillator
     * 
     * @return Pointer to fast oscillator
     */
    BaseOscillator* getFastOscillator() const;
    
private:
    BaseOscillator* slowOscillator_;       ///< Lower frequency oscillator
    BaseOscillator* fastOscillator_;       ///< Higher frequency oscillator
    unsigned int numPhaseBins_;            ///< Number of phase bins for PAC analysis
    double preferredPhase_;                ///< Phase at which amplitude is maximal
    double modulationIndex_;               ///< Phase-amplitude coupling strength
    double coherence_;                     ///< Phase-phase coupling strength
    std::pair<int, int> nToMRatio_;        ///< Best n:m coupling ratio
    std::vector<double> amplitudeDistribution_;  ///< Mean amplitude by phase bin
};

/**
 * @brief Manager for all coupling mechanisms
 * 
 * Centralizes handling of multiple coupling mechanisms
 */
class CouplingManager {
public:
    /**
     * @brief Constructor
     */
    CouplingManager();
    
    /**
     * @brief Destructor
     */
    ~CouplingManager();
    
    /**
     * @brief Add a coupling mechanism
     * 
     * @param coupling Unique pointer to coupling mechanism
     */
    void addCoupling(std::unique_ptr<BaseCouplingMechanism> coupling);
    
    /**
     * @brief Apply all coupling mechanisms
     * 
     * @param dt Time step in seconds
     */
    void applyCouplings(double dt);
    
    /**
     * @brief Update all coupling strengths
     * 
     * @param globalFactor Multiplication factor for all strengths
     */
    void updateCouplingStrengths(double globalFactor);
    
    /**
     * @brief Get number of coupling mechanisms
     * 
     * @return Count of coupling mechanisms
     */
    size_t getNumCouplings() const;
    
    /**
     * @brief Remove a coupling mechanism
     * 
     * @param index Index of mechanism to remove
     */
    void removeCoupling(size_t index);
    
    /**
     * @brief Enable or disable a coupling mechanism
     * 
     * @param index Index of mechanism to modify
     * @param enable Whether to enable or disable
     */
    void enableCoupling(size_t index, bool enable);
    
    /**
     * @brief Measure all coupling mechanisms
     * 
     * @return Vector of coupling measurements
     */
    std::vector<double> measureAllCouplings() const;
    
    /**
     * @brief Get a specific coupling mechanism
     * 
     * @param index Index of mechanism to retrieve
     * @return Pointer to coupling mechanism or nullptr
     */
    BaseCouplingMechanism* getCoupling(size_t index);
    
    /**
     * @brief Clear all coupling mechanisms
     */
    void clearAllCouplings();
    
    /**
     * @brief Create a phase-to-phase coupling
     * 
     * Factory method for creating PhaseToPhase instances
     * 
     * @param source Source oscillator
     * @param target Target oscillator
     * @param strength Coupling strength
     * @param n First integer for n:m ratio
     * @param m Second integer for n:m ratio
     * @return Unique pointer to created coupling
     */
    std::unique_ptr<PhaseToPhase> createPhaseToPhase(
        BaseOscillator* source, 
        BaseOscillator* target, 
        double strength = 0.1, 
        int n = 1, 
        int m = 1);
    
    /**
     * @brief Create a phase-to-amplitude coupling
     * 
     * Factory method for creating PhaseToAmplitude instances
     * 
     * @param phaseSource Phase source oscillator
     * @param amplitudeTarget Amplitude target oscillator
     * @param strength Coupling strength
     * @param phaseOffset Phase offset in radians
     * @return Unique pointer to created coupling
     */
    std::unique_ptr<PhaseToAmplitude> createPhaseToAmplitude(
        BaseOscillator* phaseSource, 
        BaseOscillator* amplitudeTarget, 
        double strength = 0.1, 
        double phaseOffset = 0.0);
    
    /**
     * @brief Create a frequency pull coupling
     * 
     * Factory method for creating FrequencyPullCoupling instances
     * 
     * @param source Source oscillator
     * @param target Target oscillator
     * @param strength Coupling strength
     * @param pullFactor Rate of frequency convergence
     * @return Unique pointer to created coupling
     */
    std::unique_ptr<FrequencyPullCoupling> createFrequencyPullCoupling(
        BaseOscillator* source, 
        BaseOscillator* target, 
        double strength = 0.1, 
        double pullFactor = 0.2);
    
    /**
     * @brief Create an amplitude envelope coupling
     * 
     * Factory method for creating AmplitudeEnvelope instances
     * 
     * @param source Source oscillator
     * @param target Target oscillator
     * @param strength Coupling strength
     * @param baseline Baseline amplitude factor
     * @param modulationDepth Depth of modulation
     * @return Unique pointer to created coupling
     */
    std::unique_ptr<AmplitudeEnvelope> createAmplitudeEnvelope(
        BaseOscillator* source, 
        BaseOscillator* target, 
        double strength = 0.1, 
        double baseline = 0.5, 
        double modulationDepth = 0.5);
    
private:
    std::vector<std::unique_ptr<BaseCouplingMechanism>> couplings_; ///< All coupling mechanisms
};

/**
 * @brief Network of coupled oscillators
 * 
 * Manages a collection of oscillators with various coupling mechanisms
 */
class OscillatorNetwork {
public:
    /**
     * @brief Constructor
     */
    OscillatorNetwork();
    
    /**
     * @brief Destructor
     */
    ~OscillatorNetwork();
    
    /**
     * @brief Add an oscillator to the network
     * 
     * @param oscillator Unique pointer to oscillator
     * @param name Name identifier for the oscillator
     */
    void addOscillator(std::unique_ptr<BaseOscillator> oscillator, const std::string& name);
    
    /**
     * @brief Update all oscillators and apply couplings
     * 
     * @param dt Time step in seconds
     * @param externalDrives Map of oscillator names to drive values
     */
    void update(double dt, const std::unordered_map<std::string, double>& externalDrives = {});
    
    /**
     * @brief Add phase-to-phase coupling between named oscillators
     * 
     * @param sourceName Name of source oscillator
     * @param targetName Name of target oscillator
     * @param strength Coupling strength
     * @param n First integer for n:m ratio
     * @param m Second integer for n:m ratio
     */
    void addPhaseToPhase(const std::string& sourceName, const std::string& targetName, 
                        double strength = 0.1, int n = 1, int m = 1);
    
    /**
     * @brief Add phase-to-amplitude coupling between named oscillators
     * 
     * @param sourceName Name of phase source oscillator
     * @param targetName Name of amplitude target oscillator
     * @param strength Coupling strength
     * @param phaseOffset Phase offset in radians
     */
    void addPhaseToAmplitude(const std::string& sourceName, const std::string& targetName, 
                           double strength = 0.1, double phaseOffset = 0.0);
    
    /**
     * @brief Add frequency pull coupling between named oscillators
     * 
     * @param sourceName Name of source oscillator
     * @param targetName Name of target oscillator
     * @param strength Coupling strength
     * @param pullFactor Rate of frequency convergence
     */
    void addFrequencyPull(const std::string& sourceName, const std::string& targetName, 
                         double strength = 0.1, double pullFactor = 0.2);
    
    /**
     * @brief Add amplitude envelope coupling between named oscillators
     * 
     * @param sourceName Name of source oscillator
     * @param targetName Name of target oscillator
     * @param strength Coupling strength
     * @param baseline Baseline amplitude factor
     * @param modulationDepth Depth of modulation
     */
    void addAmplitudeEnvelope(const std::string& sourceName, const std::string& targetName, 
                            double strength = 0.1, double baseline = 0.5, double modulationDepth = 0.5);
    
    /**
     * @brief Get oscillator by name
     * 
     * @param name Name identifier
     * @return Pointer to oscillator or nullptr if not found
     */
    BaseOscillator* getOscillator(const std::string& name);
    
    /**
     * @brief Get all oscillator names
     * 
     * @return Vector of oscillator names
     */
    std::vector<std::string> getOscillatorNames() const;
    
    /**
     * @brief Get current simulation time
     * 
     * @return Time in seconds
     */
    double getTimeStep() const;
    
    /**
     * @brief Get number of oscillators
     * 
     * @return Count of oscillators
     */
    size_t getNumOscillators() const;
    
    /**
     * @brief Get number of couplings
     * 
     * @return Count of coupling mechanisms
     */
    size_t getNumCouplings() const;
    
    /**
     * @brief Analyze coupling patterns between oscillators
     * 
     * Performs comprehensive analysis of all potential couplings
     */
    void analyzeCouplings();
    
    /**
     * @brief Get results of coupling analysis
     * 
     * @return Vector of cross-frequency analyzer results
     */
    const std::vector<CrossFrequencyAnalyzer>& getCouplingAnalyses() const;
    
    /**
     * @brief Reset the entire network
     * 
     * Clears all oscillators and couplings
     */
    void resetNetwork();
    
    /**
     * @brief Get coupling manager
     * 
     * @return Reference to coupling manager
     */
    CouplingManager& getCouplingManager();
    
private:
    double timeStep_;                   ///< Current simulation time
    std::vector<std::unique_ptr<BaseOscillator>> oscillators_;   ///< All oscillators
    std::unordered_map<std::string, size_t> oscillatorIndices_;  ///< Map from name to index
    CouplingManager couplingManager_;   ///< Manager for all couplings
    std::vector<CrossFrequencyAnalyzer> couplingAnalyses_;      ///< Results of coupling analysis
};

} // namespace biological_timing
} // namespace core_neural
} // namespace ultra

#endif // ULTRA_CORE_NEURAL_BIOLOGICAL_TIMING_PHASE_COUPLING_H_