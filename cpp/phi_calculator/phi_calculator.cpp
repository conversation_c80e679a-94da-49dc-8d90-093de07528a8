/**
 * @file phi_computation.cpp
 * @brief Implementation of Phi (Φ) computation algorithms for integrated information theory
 * 
 * This file implements the core mathematical algorithms for calculating integrated
 * information (Phi) based on Integrated Information Theory (IIT). It provides
 * efficient implementations of various Phi measures, including system-level Phi,
 * state-specific Phi, and the Earth Mover's Distance-based Phi 2.0 measure.
 * 
 * <AUTHOR> Team
 * @date 2025
 */

#include "phi_computation.h"
#include "causal_analysis.h"
#include "information_geometry.h"
#include "partition_search.h"
#include "causal_repertoire.h"
#include "../utils/math_operations.h"
#include "../utils/parallel_processing.h"

#include <algorithm>
#include <cassert>
#include <cmath>
#include <execution>
#include <future>
#include <iomanip>
#include <iostream>
#include <limits>
#include <map>
#include <numeric>
#include <optional>
#include <random>
#include <set>
#include <string>
#include <tuple>
#include <unordered_map>

#include <Eigen/Dense>
#include <Eigen/Eigenvalues>
#include <Eigen/Sparse>
#include <boost/graph/adjacency_list.hpp>
#include <boost/graph/connected_components.hpp>
#include <boost/graph/graph_traits.hpp>
#include <boost/math/constants/constants.hpp>
#include <tbb/parallel_for_each.h>
#include <tbb/parallel_reduce.h>
#include <tbb/task_arena.h>

// If CUDA support is available
#ifdef ULTRA_HAS_CUDA
#include <cuda_runtime.h>
#include "../utils/cuda_helpers.h"
#endif

// Include generated optimization configurations if available
#if __has_include("generated/phi_kernels_config.h")
#include "generated/phi_kernels_config.h"
#endif

namespace ultra {
namespace phi_calculator {

using namespace Eigen;
using namespace std;
using namespace ultra::utils;

//=============================================================================
// Helper Functions and Constants
//=============================================================================

namespace {
    // Constants for phi computation
    constexpr double PHI_EPSILON = 1e-10;         // Small value for numerical stability
    constexpr size_t MAX_ITERATIONS = 100;         // Max iterations for iterative algorithms
    constexpr double CONVERGENCE_THRESHOLD = 1e-6; // Convergence threshold
    
    // Hash function for system states to enable caching
    size_t hashSystemState(const VectorXd& state) {
        size_t seed = 0;
        for (int i = 0; i < state.size(); ++i) {
            seed ^= std::hash<double>{}(state(i)) + 0x9e3779b9 + (seed << 6) + (seed >> 2);
        }
        return seed;
    }
    
    // Helper function to check if a cache entry exists and is valid
    template<typename CacheType, typename KeyType>
    bool checkCache(const CacheType& cache, const KeyType& key, double& result) {
        auto it = cache.find(key);
        if (it != cache.end()) {
            result = it->second;
            return true;
        }
        return false;
    }
    
    // Validation function for system parameters
    bool validateSystemParameters(size_t systemSize, const MatrixXd& transitionMatrix) {
        if (systemSize == 0) {
            cerr << "Error: System size must be greater than 0" << endl;
            return false;
        }
        
        if (transitionMatrix.rows() != transitionMatrix.cols()) {
            cerr << "Error: Transition matrix must be square" << endl;
            return false;
        }
        
        if (transitionMatrix.rows() != pow(2, systemSize)) {
            cerr << "Error: Transition matrix dimensions do not match system size" << endl;
            return false;
        }
        
        // Check if transition matrix is a valid stochastic matrix
        for (int i = 0; i < transitionMatrix.rows(); ++i) {
            double rowSum = transitionMatrix.row(i).sum();
            if (std::abs(rowSum - 1.0) > PHI_EPSILON) {
                cerr << "Warning: Row " << i << " of transition matrix does not sum to 1 (sum = " 
                     << rowSum << ")" << endl;
                return false;
            }
        }
        
        return true;
    }
    
    // Convert binary index to state vector
    VectorXd indexToState(size_t index, size_t systemSize) {
        VectorXd state = VectorXd::Zero(systemSize);
        for (size_t i = 0; i < systemSize; ++i) {
            state(i) = (index & (1ULL << i)) ? 1.0 : 0.0;
        }
        return state;
    }
    
    // Convert state vector to binary index
    size_t stateToIndex(const VectorXd& state) {
        size_t index = 0;
        for (int i = 0; i < state.size(); ++i) {
            if (state(i) > 0.5) {
                index |= (1ULL << i);
            }
        }
        return index;
    }
    
    // Calculate Kullback-Leibler divergence between two probability distributions
    double klDivergence(const VectorXd& p, const VectorXd& q) {
        double kl = 0.0;
        for (int i = 0; i < p.size(); ++i) {
            if (p(i) > PHI_EPSILON) {
                if (q(i) > PHI_EPSILON) {
                    kl += p(i) * log2(p(i) / q(i));
                } else {
                    kl = std::numeric_limits<double>::infinity();
                    break;
                }
            }
        }
        return kl;
    }
    
    // Calculate Jensen-Shannon divergence between two probability distributions
    double jsDivergence(const VectorXd& p, const VectorXd& q) {
        VectorXd m = 0.5 * (p + q);
        return 0.5 * klDivergence(p, m) + 0.5 * klDivergence(q, m);
    }
    
    // Earth Mover's Distance (Wasserstein-1) implementation
    double earthMoversDistance(const VectorXd& p, const VectorXd& q) {
        assert(p.size() == q.size());
        
        // For 1D distributions, EMD is the L1 norm of the CDFs
        VectorXd p_cdf = VectorXd::Zero(p.size());
        VectorXd q_cdf = VectorXd::Zero(q.size());
        
        p_cdf(0) = p(0);
        q_cdf(0) = q(0);
        
        for (int i = 1; i < p.size(); ++i) {
            p_cdf(i) = p_cdf(i - 1) + p(i);
            q_cdf(i) = q_cdf(i - 1) + q(i);
        }
        
        double emd = 0.0;
        for (int i = 0; i < p.size(); ++i) {
            emd += std::abs(p_cdf(i) - q_cdf(i));
        }
        
        return emd;
    }
    
    // Generate all bipartitions of a system
    vector<Bipartition> generateBipartitions(size_t systemSize) {
        if (systemSize <= 1) {
            return {};
        }
        
        vector<Bipartition> partitions;
        
        // Generate all bipartitions (excluding empty partitions)
        for (size_t i = 1; i < (1 << systemSize) - 1; ++i) {
            vector<size_t> part1, part2;
            
            for (size_t j = 0; j < systemSize; ++j) {
                if (i & (1 << j)) {
                    part1.push_back(j);
                } else {
                    part2.push_back(j);
                }
            }
            
            // Only consider non-trivial partitions
            if (!part1.empty() && !part2.empty()) {
                partitions.push_back({part1, part2});
            }
        }
        
        return partitions;
    }
    
    // Create a custom metric tensor for information geometry calculations
    MatrixXd createFisherMetricTensor(const VectorXd& distribution) {
        int n = distribution.size();
        MatrixXd metric = MatrixXd::Zero(n, n);
        
        // Fisher information metric for multinomial distribution
        for (int i = 0; i < n; ++i) {
            if (distribution(i) > PHI_EPSILON) {
                metric(i, i) = 1.0 / distribution(i);
            } else {
                metric(i, i) = 1.0 / PHI_EPSILON;  // Regularization
            }
        }
        
        return metric;
    }
    
    // Calculate the information distance based on a metric tensor
    double informationDistance(const VectorXd& p, const VectorXd& q, const MatrixXd& metricTensor) {
        VectorXd diff = p - q;
        return sqrt(diff.transpose() * metricTensor * diff);
    }
    
    // Calculate the effective information for a specific partition
    double calculateEffectiveInformation(const shared_ptr<CausalGraph>& graph, 
                                       const Bipartition& partition) {
        // Extract the subgraphs for each part
        vector<shared_ptr<CausalState>> part1States, part2States;
        
        for (size_t idx : partition.first) {
            part1States.push_back(graph->getNodeState(idx));
        }
        
        for (size_t idx : partition.second) {
            part2States.push_back(graph->getNodeState(idx));
        }
        
        // Calculate joint entropy for each part
        double part1Entropy = 0.0;
        double part2Entropy = 0.0;
        
        if (part1States.size() > 1) {
            CausalAnalyzer analyzer1(make_shared<CausalGraph>(part1States.size()));
            part1Entropy = analyzer1.calculateJointEntropy(part1States);
        } else if (part1States.size() == 1) {
            part1Entropy = part1States[0]->entropy();
        }
        
        if (part2States.size() > 1) {
            CausalAnalyzer analyzer2(make_shared<CausalGraph>(part2States.size()));
            part2Entropy = analyzer2.calculateJointEntropy(part2States);
        } else if (part2States.size() == 1) {
            part2Entropy = part2States[0]->entropy();
        }
        
        // Calculate effective information as mutual information
        CausalAnalyzer fullAnalyzer(graph);
        double systemEntropy = fullAnalyzer.calculateJointEntropy(
            [&]() -> vector<shared_ptr<CausalState>> {
                vector<shared_ptr<CausalState>> allStates;
                for (size_t i = 0; i < graph->getNumNodes(); ++i) {
                    allStates.push_back(graph->getNodeState(i));
                }
                return allStates;
            }()
        );
        
        // Mutual information = H(X) + H(Y) - H(X,Y)
        double mi = part1Entropy + part2Entropy - systemEntropy;
        
        return max(0.0, mi);  // Ensure non-negative
    }
}

//=============================================================================
// PhiComputer Implementation
//=============================================================================

PhiComputer::PhiComputer(size_t systemSize)
    : systemSize_(systemSize),
      useParallelization_(true),
      phiVersion_(PhiVersion::PHI_3_0),
      cacheEnabled_(true) {
    
    // Initialize system with default values
    initialize();
}

PhiComputer::PhiComputer(size_t systemSize, const MatrixXd& transitionMatrix)
    : systemSize_(systemSize),
      transitionMatrix_(transitionMatrix),
      useParallelization_(true),
      phiVersion_(PhiVersion::PHI_3_0),
      cacheEnabled_(true) {
    
    // Validate the transition matrix
    if (!validateSystemParameters(systemSize, transitionMatrix)) {
        cerr << "Warning: Invalid transition matrix provided. Using identity matrix instead." << endl;
        transitionMatrix_ = MatrixXd::Identity(pow(2, systemSize), pow(2, systemSize));
    }
    
    // Complete initialization
    initialize();
}

void PhiComputer::initialize() {
    // Create default transition matrix if not provided
    if (transitionMatrix_.size() == 0) {
        size_t stateCount = pow(2, systemSize_);
        transitionMatrix_ = MatrixXd::Identity(stateCount, stateCount);
    }
    
    // Initialize the causal graph
    initializeCausalGraph();
    
    // Clear caches
    clearCaches();
    
    // Initialize partition search
    partitionSearcher_ = make_unique<PartitionSearcher>(systemSize_);
}

void PhiComputer::initializeCausalGraph() {
    // Create a causal graph based on the transition matrix
    causalGraph_ = make_shared<CausalGraph>(systemSize_);
    
    // Analyze transition matrix to determine causal connections
    size_t stateCount = pow(2, systemSize_);
    
    // Create connection matrix based on influence analysis
    MatrixXd connectionMatrix = MatrixXd::Zero(systemSize_, systemSize_);
    
    // For each node pair, determine if there's a causal influence
    for (size_t i = 0; i < systemSize_; ++i) {
        for (size_t j = 0; j < systemSize_; ++j) {
            if (i == j) continue;  // Skip self-connections initially
            
            // Calculate influence of node i on node j
            double influence = calculateNodeInfluence(i, j);
            
            if (influence > PHI_EPSILON) {
                connectionMatrix(i, j) = influence;
                causalGraph_->addLink(i, j, influence);
            }
        }
    }
    
    // Initialize node states
    for (size_t i = 0; i < systemSize_; ++i) {
        auto state = make_shared<CausalState>(systemSize_);
        causalGraph_->setNodeState(i, state);
    }
}

double PhiComputer::calculateNodeInfluence(size_t sourceNode, size_t targetNode) {
    size_t stateCount = pow(2, systemSize_);
    double influence = 0.0;
    
    // Calculate how much sourceNode influences targetNode
    // by comparing state transitions with and without sourceNode intervention
    for (size_t stateIdx = 0; stateIdx < stateCount; ++stateIdx) {
        // Get the current state as a vector
        VectorXd currentState = indexToState(stateIdx, systemSize_);
        
        // Calculate probabilities of target node being 0 or 1 in next state
        VectorXd nextStateProbs = transitionMatrix_.row(stateIdx);
        
        // Calculate probabilities when source node is intervened (both 0 and 1)
        VectorXd nextStateProbsSourceTo0 = VectorXd::Zero(stateCount);
        VectorXd nextStateProbsSourceTo1 = VectorXd::Zero(stateCount);
        
        // For each possible next state
        for (size_t nextStateIdx = 0; nextStateIdx < stateCount; ++nextStateIdx) {
            // Probability of this transition
            double transProb = transitionMatrix_(stateIdx, nextStateIdx);
            
            // Next state as vector
            VectorXd nextState = indexToState(nextStateIdx, systemSize_);
            
            // Create states with source node set to 0 and 1
            size_t modifiedState0Idx = stateIdx & ~(1ULL << sourceNode);
            size_t modifiedState1Idx = stateIdx | (1ULL << sourceNode);
            
            nextStateProbsSourceTo0 += transitionMatrix_.row(modifiedState0Idx) * transProb;
            nextStateProbsSourceTo1 += transitionMatrix_.row(modifiedState1Idx) * transProb;
        }
        
        // Calculate difference in target node probabilities
        double probTargetWithSource0 = 0.0;
        double probTargetWithSource1 = 0.0;
        double probTargetNoIntervention = 0.0;
        
        for (size_t nextStateIdx = 0; nextStateIdx < stateCount; ++nextStateIdx) {
            VectorXd nextState = indexToState(nextStateIdx, systemSize_);
            
            if (nextState(targetNode) > 0.5) {
                probTargetWithSource0 += nextStateProbsSourceTo0(nextStateIdx);
                probTargetWithSource1 += nextStateProbsSourceTo1(nextStateIdx);
                probTargetNoIntervention += nextStateProbs(nextStateIdx);
            }
        }
        
        // Calculate influence as maximum deviation caused by intervention
        double deviation0 = std::abs(probTargetWithSource0 - probTargetNoIntervention);
        double deviation1 = std::abs(probTargetWithSource1 - probTargetNoIntervention);
        influence += std::max(deviation0, deviation1);
    }
    
    // Normalize influence
    influence /= stateCount;
    
    return influence;
}

void PhiComputer::setTransitionMatrix(const MatrixXd& transitionMatrix) {
    if (!validateSystemParameters(systemSize_, transitionMatrix)) {
        cerr << "Error: Invalid transition matrix provided. Not updating." << endl;
        return;
    }
    
    transitionMatrix_ = transitionMatrix;
    
    // Reinitialize the system with the new transition matrix
    initializeCausalGraph();
    clearCaches();
}

void PhiComputer::setSystemSize(size_t systemSize) {
    if (systemSize == 0) {
        cerr << "Error: System size must be greater than 0" << endl;
        return;
    }
    
    systemSize_ = systemSize;
    
    // Create a new transition matrix
    size_t stateCount = pow(2, systemSize_);
    transitionMatrix_ = MatrixXd::Identity(stateCount, stateCount);
    
    // Reinitialize
    initialize();
}

void PhiComputer::setSystemState(const VectorXd& state) {
    if (state.size() != systemSize_) {
        cerr << "Error: State vector size must match system size" << endl;
        return;
    }
    
    currentState_ = state;
    
    // Update node states in the causal graph
    for (size_t i = 0; i < systemSize_; ++i) {
        VectorXd nodeState = VectorXd::Zero(systemSize_);
        nodeState(i) = state(i);
        
        auto stateObj = make_shared<CausalState>(nodeState);
        causalGraph_->setNodeState(i, stateObj);
    }
    
    // Clear state-specific caches
    clearStateCaches();
}

void PhiComputer::setPhiVersion(PhiVersion version) {
    phiVersion_ = version;
    clearCaches();
}

void PhiComputer::enableParallelization(bool enable) {
    useParallelization_ = enable;
}

void PhiComputer::enableCaching(bool enable) {
    cacheEnabled_ = enable;
    if (!enable) {
        clearCaches();
    }
}

void PhiComputer::clearCaches() {
    phiCache_.clear();
    effectiveInfoCache_.clear();
    mipCache_.clear();
    clearStateCaches();
}

void PhiComputer::clearStateCaches() {
    statePhiCache_.clear();
    stateMipCache_.clear();
}

double PhiComputer::calculatePhi() {
    // If cache is enabled, check for cached result
    size_t cacheKey = hashSystemState(currentState_);
    if (cacheEnabled_) {
        double cachedPhi;
        if (checkCache(phiCache_, cacheKey, cachedPhi)) {
            return cachedPhi;
        }
    }
    
    double phi = 0.0;
    
    switch (phiVersion_) {
        case PhiVersion::PHI_1_0:
            phi = calculatePhi1();
            break;
            
        case PhiVersion::PHI_2_0:
            phi = calculatePhi2();
            break;
            
        case PhiVersion::PHI_3_0:
            phi = calculatePhi3();
            break;
            
        default:
            cerr << "Unknown Phi version. Using Phi 3.0." << endl;
            phi = calculatePhi3();
            break;
    }
    
    // Cache the result
    if (cacheEnabled_) {
        phiCache_[cacheKey] = phi;
    }
    
    return phi;
}

double PhiComputer::calculatePhi1() {
    // Phi 1.0: Based on effective information
    
    // Get CausalAnalyzer for the graph
    CausalAnalyzer analyzer(causalGraph_);
    
    // Calculate effective information for the whole system
    double systemEI = analyzer.calculateEffectiveInformation();
    
    // Find the minimum information partition (MIP)
    Partition mip = findMinimumInformationPartition(PhiVersion::PHI_1_0);
    
    // Calculate effective information for the MIP
    double mipEI = 0.0;
    if (!mip.empty()) {
        // For bipartitions, we use the sum of effective information within each part
        for (const auto& part : mip) {
            if (part.size() > 1) {
                // Create a subgraph for this part
                MatrixXd subMatrix = MatrixXd::Zero(part.size(), part.size());
                
                // Copy connections within this part
                for (size_t i = 0; i < part.size(); ++i) {
                    for (size_t j = 0; j < part.size(); ++j) {
                        subMatrix(i, j) = causalGraph_->getAdjacencyMatrix()(part[i], part[j]);
                    }
                }
                
                auto subGraph = make_shared<CausalGraph>(subMatrix);
                
                // Copy node states
                for (size_t i = 0; i < part.size(); ++i) {
                    subGraph->setNodeState(i, causalGraph_->getNodeState(part[i]));
                }
                
                // Calculate effective information for this subgraph
                CausalAnalyzer subAnalyzer(subGraph);
                mipEI += subAnalyzer.calculateEffectiveInformation();
            }
        }
    }
    
    // Phi is the difference between system EI and sum of EI in MIP parts
    double phi = systemEI - mipEI;
    return max(0.0, phi);  // Ensure non-negative
}

double PhiComputer::calculatePhi2() {
    // Phi 2.0: Based on Earth Mover's Distance
    
    // Get the current system state
    if (currentState_.size() != systemSize_) {
        currentState_ = VectorXd::Zero(systemSize_);
    }
    
    // Calculate cause and effect repertoires for the whole system
    CausalRepertoire causalRep(causalGraph_, transitionMatrix_);
    
    VectorXd causeRepertoire = causalRep.calculateCauseRepertoire(currentState_);
    VectorXd effectRepertoire = causalRep.calculateEffectRepertoire(currentState_);
    
    // Find the minimum information partition for state-specific phi
    Bipartition mip = findStateMip(PhiVersion::PHI_2_0);
    
    if (mip.first.empty() || mip.second.empty()) {
        return 0.0;  // No non-trivial partition
    }
    
    // Calculate cause and effect repertoires for the partitioned system
    VectorXd partitionedCauseRepertoire = causalRep.calculatePartitionedCauseRepertoire(
        currentState_, mip);
    VectorXd partitionedEffectRepertoire = causalRep.calculatePartitionedEffectRepertoire(
        currentState_, mip);
    
    // Calculate Earth Mover's Distance for cause and effect
    double causeDist = earthMoversDistance(causeRepertoire, partitionedCauseRepertoire);
    double effectDist = earthMoversDistance(effectRepertoire, partitionedEffectRepertoire);
    
    // Phi is the minimum of cause and effect distances
    return min(causeDist, effectDist);
}

double PhiComputer::calculatePhi3() {
    // Phi 3.0: Based on integrated information measures with information geometry
    
    // Get the current system state
    if (currentState_.size() != systemSize_) {
        currentState_ = VectorXd::Zero(systemSize_);
    }
    
    // Calculate the system's causal information
    InformationGeometry geometry(causalGraph_, transitionMatrix_);
    
    // Calculate information-geometric integrated information
    double phi = geometry.calculateIntegratedInformation(currentState_);
    
    return phi;
}

Partition PhiComputer::findMinimumInformationPartition(PhiVersion phiVersion) {
    // Check cache first
    if (cacheEnabled_) {
        auto it = mipCache_.find(phiVersion);
        if (it != mipCache_.end()) {
            return it->second;
        }
    }
    
    Partition mip;
    
    // For Phi 1.0, we need to find the partition that minimizes normalized effective information
    if (phiVersion == PhiVersion::PHI_1_0) {
        // We'll implement the partition search here
        vector<Partition> partitions = partitionSearcher_->generatePartitions(systemSize_);
        
        // Find partition with minimum normalized effective information
        double minNormalizedEI = std::numeric_limits<double>::max();
        
        // Process partitions
        auto processPartition = [&](const Partition& partition) {
            // Calculate effective information for this partition
            double partitionEI = 0.0;
            
            // For each part in the partition
            for (const auto& part : partition) {
                // Skip single-element parts (no internal EI)
                if (part.size() <= 1) continue;
                
                // Create a bipartition for each part vs. rest
                vector<size_t> restIndices;
                for (size_t i = 0; i < systemSize_; ++i) {
                    if (std::find(part.begin(), part.end(), i) == part.end()) {
                        restIndices.push_back(i);
                    }
                }
                
                Bipartition bipart = make_pair(part, restIndices);
                
                // Calculate effective information for this bipartition
                double ei = calculateEffectiveInformation(causalGraph_, bipart);
                partitionEI += ei;
            }
            
            // Normalize by the partition complexity
            double normalizationFactor = partitionSearcher_->calculateNormalizationFactor(partition);
            double normalizedEI = partitionEI / normalizationFactor;
            
            // Update MIP if this is better
            if (normalizedEI < minNormalizedEI) {
                minNormalizedEI = normalizedEI;
                mip = partition;
            }
        };
        
        if (useParallelization_ && partitions.size() > 100) {
            // Parallel processing for large numbers of partitions
            tbb::parallel_for_each(partitions.begin(), partitions.end(), processPartition);
        } else {
            // Sequential processing for small numbers of partitions
            for (const auto& partition : partitions) {
                processPartition(partition);
            }
        }
    } else {
        // For other Phi versions, we generate a simple bipartition using causal analysis
        CausalAnalyzer analyzer(causalGraph_);
        mip = analyzer.findMinimumInformationPartition();
    }
    
    // Cache the result
    if (cacheEnabled_) {
        mipCache_[phiVersion] = mip;
    }
    
    return mip;
}

Bipartition PhiComputer::findStateMip(PhiVersion phiVersion) {
    // Check cache first
    size_t cacheKey = hashSystemState(currentState_);
    if (cacheEnabled_) {
        auto& cache = stateMipCache_[phiVersion];
        auto it = cache.find(cacheKey);
        if (it != cache.end()) {
            return it->second;
        }
    }
    
    // Generate all possible bipartitions
    vector<Bipartition> bipartitions = generateBipartitions(systemSize_);
    
    // Initialize with default bipartition and max value
    Bipartition mip = bipartitions[0];
    double minDistance = std::numeric_limits<double>::max();
    
    // Setup causal repertoire calculator
    CausalRepertoire causalRep(causalGraph_, transitionMatrix_);
    
    // Calculate repertoires for the unpartitioned system
    VectorXd causeRepertoire = causalRep.calculateCauseRepertoire(currentState_);
    VectorXd effectRepertoire = causalRep.calculateEffectRepertoire(currentState_);
    
    // Process each bipartition to find the MIP
    auto processBipartition = [&](const Bipartition& bipart) {
        // Calculate partitioned repertoires
        VectorXd partCauseRepertoire = causalRep.calculatePartitionedCauseRepertoire(
            currentState_, bipart);
        VectorXd partEffectRepertoire = causalRep.calculatePartitionedEffectRepertoire(
            currentState_, bipart);
        
        // Calculate distances
        double dist = 0.0;
        
        if (phiVersion == PhiVersion::PHI_2_0) {
            // For Phi 2.0, use Earth Mover's Distance
            double causeDist = earthMoversDistance(causeRepertoire, partCauseRepertoire);
            double effectDist = earthMoversDistance(effectRepertoire, partEffectRepertoire);
            dist = min(causeDist, effectDist);
        } else {
            // For Phi 3.0, use information-geometric distance
            InformationGeometry geometry(causalGraph_, transitionMatrix_);
            
            // Calculate distance between original and partitioned repertoires
            double causeDist = geometry.calculateRepertoireDistance(
                causeRepertoire, partCauseRepertoire);
            double effectDist = geometry.calculateRepertoireDistance(
                effectRepertoire, partEffectRepertoire);
            
            dist = min(causeDist, effectDist);
        }
        
        // Normalize by partition size (to favor balanced partitions)
        double normalization = (double)(bipart.first.size() * bipart.second.size()) / 
                             (double)(systemSize_ * systemSize_);
        double normalizedDist = dist * normalization;
        
        // Update MIP if this distance is smaller
        return pair<double, Bipartition>(normalizedDist, bipart);
    };
    
    // Process bipartitions in parallel or sequentially
    if (useParallelization_ && bipartitions.size() > 16) {
        vector<future<pair<double, Bipartition>>> futures;
        futures.reserve(bipartitions.size());
        
        for (const auto& bipart : bipartitions) {
            futures.push_back(async(launch::async, processBipartition, bipart));
        }
        
        // Collect results and find minimum
        for (auto& future : futures) {
            auto result = future.get();
            if (result.first < minDistance) {
                minDistance = result.first;
                mip = result.second;
            }
        }
    } else {
        for (const auto& bipart : bipartitions) {
            auto result = processBipartition(bipart);
            if (result.first < minDistance) {
                minDistance = result.first;
                mip = result.second;
            }
        }
    }
    
    // Cache the result
    if (cacheEnabled_) {
        stateMipCache_[phiVersion][cacheKey] = mip;
    }
    
    return mip;
}

double PhiComputer::calculateConceptualInformation() {
    // Conceptual information (CI) measures the sum of conceptual distinctions
    // in the system, weighted by their contribution to integrated information
    
    // Calculate all small phi values for each subsystem (concepts)
    vector<pair<set<size_t>, double>> concepts = identifyConcepts();
    
    // Calculate total conceptual information
    double ci = 0.0;
    for (const auto& concept : concepts) {
        ci += concept.second;
    }
    
    return ci;
}

double PhiComputer::calculateConceptualIntegration() {
    // Conceptual Integration (CI) measures how much the concepts in the system
    // are integrated beyond their individual contributions
    
    // Calculate all concepts for the intact system
    vector<pair<set<size_t>, double>> intactConcepts = identifyConcepts();
    
    double intactCI = 0.0;
    for (const auto& concept : intactConcepts) {
        intactCI += concept.second;
    }
    
    // Find MIP for the current state
    Bipartition mip = findStateMip(phiVersion_);
    
    // Calculate concepts for the partitioned system
    vector<pair<set<size_t>, double>> mipConcepts;
    
    // Calculate concepts for each part of the partition
    auto part1Concepts = identifyConcepts(mip.first);
    auto part2Concepts = identifyConcepts(mip.second);
    
    // Combine concepts from both parts
    double partitionedCI = 0.0;
    for (const auto& concept : part1Concepts) {
        partitionedCI += concept.second;
    }
    for (const auto& concept : part2Concepts) {
        partitionedCI += concept.second;
    }
    
    // Conceptual Integration is the difference
    return max(0.0, intactCI - partitionedCI);
}

vector<pair<set<size_t>, double>> PhiComputer::identifyConcepts(
    const vector<size_t>& subsystemElements) {
    
    vector<pair<set<size_t>, double>> concepts;
    
    // If no subsystem specified, use all elements
    vector<size_t> elements = subsystemElements;
    if (elements.empty()) {
        elements.resize(systemSize_);
        iota(elements.begin(), elements.end(), 0);
    }
    
    // Generate all possible groupings of elements (power set excluding empty set)
    size_t numElements = elements.size();
    size_t numSubsets = (1 << numElements) - 1;  // 2^n - 1 non-empty subsets
    
    // Create a phiComputer for smaller subsystems
    auto processMechanism = [&](size_t subsetIndex) {
        set<size_t> mechanism;
        
        // Convert subset index to mechanism
        for (size_t i = 0; i < numElements; ++i) {
            if (subsetIndex & (1 << i)) {
                mechanism.insert(elements[i]);
            }
        }
        
        // Skip empty mechanism
        if (mechanism.empty()) {
            return pair<set<size_t>, double>(mechanism, 0.0);
        }
        
        // Calculate small phi for this mechanism
        double smallPhi = calculateSmallPhi(mechanism);
        
        return pair<set<size_t>, double>(mechanism, smallPhi);
    };
    
    // Process all possible mechanisms in parallel or sequentially
    vector<pair<set<size_t>, double>> allConcepts;
    
    if (useParallelization_ && numSubsets > 16) {
        vector<future<pair<set<size_t>, double>>> futures;
        futures.reserve(numSubsets);
        
        for (size_t i = 1; i <= numSubsets; ++i) {
            futures.push_back(async(launch::async, processMechanism, i));
        }
        
        // Collect results
        for (auto& future : futures) {
            auto result = future.get();
            if (result.second > PHI_EPSILON) {
                allConcepts.push_back(result);
            }
        }
    } else {
        for (size_t i = 1; i <= numSubsets; ++i) {
            auto result = processMechanism(i);
            if (result.second > PHI_EPSILON) {
                allConcepts.push_back(result);
            }
        }
    }
    
    // Sort concepts by phi value (descending)
    sort(allConcepts.begin(), allConcepts.end(), 
        [](const auto& a, const auto& b) {
            return a.second > b.second;
        });
    
    return allConcepts;
}

double PhiComputer::calculateSmallPhi(const set<size_t>& mechanism) {
    // Calculate the small phi value for a specific mechanism
    // This measures how irreducible the causal role of the mechanism is
    
    // Convert set to vector for easier handling
    vector<size_t> mechVec(mechanism.begin(), mechanism.end());
    
    // Skip if mechanism is too small
    if (mechVec.size() <= 1) {
        return 0.0;
    }
    
    // Create a causal repertoire calculator
    CausalRepertoire causalRep(causalGraph_, transitionMatrix_);
    
    // Calculate cause and effect repertoires for the mechanism
    VectorXd causeRepertoire = causalRep.calculateMechanismCauseRepertoire(
        currentState_, mechVec);
    VectorXd effectRepertoire = causalRep.calculateMechanismEffectRepertoire(
        currentState_, mechVec);
    
    // Find the minimum information partition of the mechanism
    Bipartition mechMip = findMechanismMip(mechVec);
    
    // Calculate partitioned cause and effect repertoires
    VectorXd partCauseRepertoire = causalRep.calculatePartitionedMechanismCauseRepertoire(
        currentState_, mechVec, mechMip);
    VectorXd partEffectRepertoire = causalRep.calculatePartitionedMechanismEffectRepertoire(
        currentState_, mechVec, mechMip);
    
    // Calculate distances based on the phi version
    double causeDist = 0.0;
    double effectDist = 0.0;
    
    if (phiVersion_ == PhiVersion::PHI_2_0) {
        // For Phi 2.0, use Earth Mover's Distance
        causeDist = earthMoversDistance(causeRepertoire, partCauseRepertoire);
        effectDist = earthMoversDistance(effectRepertoire, partEffectRepertoire);
    } else {
        // For Phi 3.0, use information-geometric distance
        InformationGeometry geometry(causalGraph_, transitionMatrix_);
        
        causeDist = geometry.calculateRepertoireDistance(
            causeRepertoire, partCauseRepertoire);
        effectDist = geometry.calculateRepertoireDistance(
            effectRepertoire, partEffectRepertoire);
    }
    
    // Small phi is the minimum of cause and effect distances
    return min(causeDist, effectDist);
}

Bipartition PhiComputer::findMechanismMip(const vector<size_t>& mechanism) {
    // Find the minimum information partition of a mechanism
    
    // Generate all possible bipartitions of the mechanism
    vector<Bipartition> bipartitions;
    size_t n = mechanism.size();
    
    // Skip trivial case
    if (n <= 1) {
        return {{}, {}};
    }
    
    // Generate all bipartitions
    for (size_t i = 1; i < (1 << n) - 1; ++i) {
        vector<size_t> part1, part2;
        
        for (size_t j = 0; j < n; ++j) {
            if (i & (1 << j)) {
                part1.push_back(mechanism[j]);
            } else {
                part2.push_back(mechanism[j]);
            }
        }
        
        // Only consider non-trivial partitions
        if (!part1.empty() && !part2.empty()) {
            bipartitions.push_back({part1, part2});
        }
    }
    
    // Initialize with first bipartition and max value
    Bipartition mip = bipartitions[0];
    double minDistance = std::numeric_limits<double>::max();
    
    // Create causal repertoire calculator
    CausalRepertoire causalRep(causalGraph_, transitionMatrix_);
    
    // Calculate unpartitioned repertoires
    VectorXd causeRepertoire = causalRep.calculateMechanismCauseRepertoire(
        currentState_, mechanism);
    VectorXd effectRepertoire = causalRep.calculateMechanismEffectRepertoire(
        currentState_, mechanism);
    
    // Process each bipartition to find MIP
    for (const auto& bipart : bipartitions) {
        // Calculate partitioned repertoires
        VectorXd partCauseRepertoire = causalRep.calculatePartitionedMechanismCauseRepertoire(
            currentState_, mechanism, bipart);
        VectorXd partEffectRepertoire = causalRep.calculatePartitionedMechanismEffectRepertoire(
            currentState_, mechanism, bipart);
        
        // Calculate distances
        double dist = 0.0;
        
        if (phiVersion_ == PhiVersion::PHI_2_0) {
            // For Phi 2.0, use Earth Mover's Distance
            double causeDist = earthMoversDistance(causeRepertoire, partCauseRepertoire);
            double effectDist = earthMoversDistance(effectRepertoire, partEffectRepertoire);
            dist = min(causeDist, effectDist);
        } else {
            // For Phi 3.0, use information-geometric distance
            InformationGeometry geometry(causalGraph_, transitionMatrix_);
            
            double causeDist = geometry.calculateRepertoireDistance(
                causeRepertoire, partCauseRepertoire);
            double effectDist = geometry.calculateRepertoireDistance(
                effectRepertoire, partEffectRepertoire);
            
            dist = min(causeDist, effectDist);
        }
        
        // Normalize by partition size
        double normalization = (double)(bipart.first.size() * bipart.second.size()) / 
                             (double)(n * n);
        double normalizedDist = dist * normalization;
        
        // Update MIP if distance is smaller
        if (normalizedDist < minDistance) {
            minDistance = normalizedDist;
            mip = bipart;
        }
    }
    
    return mip;
}

vector<PhiCandidate> PhiComputer::findPhiComplexes() {
    // Find all maximally irreducible structures (phi complexes) in the system
    
    vector<PhiCandidate> complexes;
    
    // The system itself is the first candidate
    vector<size_t> allElements(systemSize_);
    iota(allElements.begin(), allElements.end(), 0);
    
    PhiCandidate systemComplex;
    systemComplex.elements = allElements;
    systemComplex.phi = calculatePhi();
    complexes.push_back(systemComplex);
    
    // Generate all possible subsystems (excluding singletons and full system)
    vector<vector<size_t>> subsystems;
    
    // For small systems, generate all subsystems
    if (systemSize_ <= 10) {
        for (size_t size = 2; size < systemSize_; ++size) {
            vector<bool> v(systemSize_);
            fill(v.begin(), v.begin() + size, true);
            
            do {
                vector<size_t> subsystem;
                for (size_t i = 0; i < systemSize_; ++i) {
                    if (v[i]) subsystem.push_back(i);
                }
                subsystems.push_back(subsystem);
            } while (prev_permutation(v.begin(), v.end()));
        }
    } else {
        // For larger systems, use a heuristic approach
        // Focus on strongly connected components
        
        // Find connected components in the causal graph
        auto components = findConnectedComponents();
        
        // Add each component as a candidate subsystem
        for (const auto& component : components) {
            if (component.size() >= 2 && component.size() < systemSize_) {
                subsystems.push_back(component);
            }
        }
        
        // Also add some combinations of components
        if (components.size() > 1) {
            for (size_t i = 0; i < components.size(); ++i) {
                for (size_t j = i + 1; j < components.size(); ++j) {
                    vector<size_t> combined;
                    combined.insert(combined.end(), components[i].begin(), components[i].end());
                    combined.insert(combined.end(), components[j].begin(), components[j].end());
                    
                    if (combined.size() < systemSize_) {
                        subsystems.push_back(combined);
                    }
                }
            }
        }
    }
    
    // Calculate phi for each subsystem
    for (const auto& subsystem : subsystems) {
        // Create a new phi computer for this subsystem
        size_t subSize = subsystem.size();
        MatrixXd subTransMatrix = extractSubsystemTransitionMatrix(subsystem);
        
        PhiComputer subComputer(subSize, subTransMatrix);
        
        // Set subsystem state
        VectorXd subState = VectorXd::Zero(subSize);
        for (size_t i = 0; i < subSize; ++i) {
            subState(i) = currentState_(subsystem[i]);
        }
        subComputer.setSystemState(subState);
        
        // Calculate phi for this subsystem
        double phi = subComputer.calculatePhi();
        
        // Add to complexes if phi is significant
        if (phi > PHI_EPSILON) {
            PhiCandidate complex;
            complex.elements = subsystem;
            complex.phi = phi;
            complexes.push_back(complex);
        }
    }
    
    // Sort complexes by phi value (descending)
    sort(complexes.begin(), complexes.end(), 
        [](const auto& a, const auto& b) {
            return a.phi > b.phi;
        });
    
    // Filter to keep only maximally irreducible complexes
    vector<PhiCandidate> maximalComplexes;
    
    for (const auto& complex : complexes) {
        // Check if this complex is a sub-complex of any higher-phi complex
        bool isSubmaximal = false;
        
        for (const auto& maxComplex : maximalComplexes) {
            if (maxComplex.phi > complex.phi) {
                // Check if complex.elements is a subset of maxComplex.elements
                bool isSubset = true;
                for (size_t elem : complex.elements) {
                    if (find(maxComplex.elements.begin(), maxComplex.elements.end(), elem) == 
                        maxComplex.elements.end()) {
                        isSubset = false;
                        break;
                    }
                }
                
                if (isSubset) {
                    isSubmaximal = true;
                    break;
                }
            }
        }
        
        if (!isSubmaximal) {
            maximalComplexes.push_back(complex);
        }
    }
    
    return maximalComplexes;
}

vector<vector<size_t>> PhiComputer::findConnectedComponents() {
    // Find strongly connected components in the causal graph
    
    // Convert adjacency matrix to boost graph
    using Graph = boost::adjacency_list<boost::vecS, boost::vecS, boost::directedS>;
    Graph g(systemSize_);
    
    for (size_t i = 0; i < systemSize_; ++i) {
        for (size_t j = 0; j < systemSize_; ++j) {
            if (causalGraph_->getAdjacencyMatrix()(i, j) > PHI_EPSILON) {
                boost::add_edge(i, j, g);
            }
        }
    }
    
    // Compute strongly connected components
    vector<int> component(boost::num_vertices(g));
    int num_components = boost::strong_components(g, &component[0]);
    
    // Group nodes by component
    vector<vector<size_t>> components(num_components);
    for (size_t i = 0; i < systemSize_; ++i) {
        components[component[i]].push_back(i);
    }
    
    // Filter out empty components
    components.erase(
        remove_if(components.begin(), components.end(),
                 [](const auto& comp) { return comp.empty(); }),
        components.end()
    );
    
    return components;
}

MatrixXd PhiComputer::extractSubsystemTransitionMatrix(const vector<size_t>& subsystem) {
    size_t subSize = subsystem.size();
    size_t subStateCount = 1 << subSize;
    
    MatrixXd subMatrix = MatrixXd::Zero(subStateCount, subStateCount);
    
    // For each pair of subsystem states
    for (size_t i = 0; i < subStateCount; ++i) {
        for (size_t j = 0; j < subStateCount; ++j) {
            // Map subsystem states to corresponding system states
            double prob = calculateSubsystemTransitionProbability(subsystem, i, j);
            subMatrix(i, j) = prob;
        }
    }
    
    // Normalize rows to ensure proper probability distribution
    for (size_t i = 0; i < subStateCount; ++i) {
        double rowSum = subMatrix.row(i).sum();
        if (rowSum > PHI_EPSILON) {
            subMatrix.row(i) /= rowSum;
        } else {
            // If row sums to zero, make it uniform
            subMatrix.row(i).fill(1.0 / subStateCount);
        }
    }
    
    return subMatrix;
}

double PhiComputer::calculateSubsystemTransitionProbability(
    const vector<size_t>& subsystem, size_t fromSubState, size_t toSubState) {
    
    // Convert subsystem states to full system state indices
    vector<size_t> fromFullStates;
    vector<size_t> toFullStates;
    
    size_t fullStateCount = 1 << systemSize_;
    
    // Generate all full system states corresponding to the subsystem states
    for (size_t fullState = 0; fullState < fullStateCount; ++fullState) {
        // Check if this full state matches the subsystem's "from" state
        bool matchesFrom = true;
        for (size_t i = 0; i < subsystem.size(); ++i) {
            bool bitIsSet = (fullState & (1ULL << subsystem[i])) != 0;
            bool shouldBeSet = (fromSubState & (1ULL << i)) != 0;
            
            if (bitIsSet != shouldBeSet) {
                matchesFrom = false;
                break;
            }
        }
        
        if (matchesFrom) {
            fromFullStates.push_back(fullState);
        }
        
        // Check if this full state matches the subsystem's "to" state
        bool matchesTo = true;
        for (size_t i = 0; i < subsystem.size(); ++i) {
            bool bitIsSet = (fullState & (1ULL << subsystem[i])) != 0;
            bool shouldBeSet = (toSubState & (1ULL << i)) != 0;
            
            if (bitIsSet != shouldBeSet) {
                matchesTo = false;
                break;
            }
        }
        
        if (matchesTo) {
            toFullStates.push_back(fullState);
        }
    }
    
    // Calculate transition probability by averaging over all matching transitions
    double totalProb = 0.0;
    
    for (size_t fromFull : fromFullStates) {
        double fromProb = 1.0 / fromFullStates.size();  // Equal probability for each matching state
        
        for (size_t toFull : toFullStates) {
            totalProb += fromProb * transitionMatrix_(fromFull, toFull);
        }
    }
    
    return totalProb;
}

} // namespace phi_calculator
} // namespace ultra 