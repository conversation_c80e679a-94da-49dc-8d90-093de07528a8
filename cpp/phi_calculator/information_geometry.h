/**
 * @file information_geometry.h
 * @brief Geometric methods for information theory and distance calculations
 * 
 * This file provides implementations of information-geometric methods used
 * in Integrated Information Theory calculations. It includes various distance
 * measures between probability distributions, transformations in probability
 * spaces, and geometric operations on information manifolds.
 * 
 * <AUTHOR> Team
 * @date 2025
 */

#ifndef ULTRA_PHI_CALCULATOR_INFORMATION_GEOMETRY_H_
#define ULTRA_PHI_CALCULATOR_INFORMATION_GEOMETRY_H_

#include <Eigen/Dense>
#include <vector>
#include <functional>
#include <memory>
#include <optional>
#include <tuple>

namespace ultra {
namespace phi_calculator {

// Forward declarations
class CausalState;
enum class DistanceMetric;

/**
 * @brief Probability distribution representation
 * 
 * Represents a discrete probability distribution with support and probabilities
 */
class ProbabilityDistribution {
public:
    /**
     * @brief Constructor for uniform distribution
     * 
     * @param size Size of the distribution
     */
    explicit ProbabilityDistribution(size_t size);
    
    /**
     * @brief Constructor with probability vector
     * 
     * @param probabilities Vector of probabilities
     */
    explicit ProbabilityDistribution(const Eigen::VectorXd& probabilities);
    
    /**
     * @brief Constructor with values and probabilities
     * 
     * @param values Vector of values
     * @param probabilities Vector of probabilities
     */
    ProbabilityDistribution(const Eigen::VectorXd& values, 
                            const Eigen::VectorXd& probabilities);
    
    /**
     * @brief Get size of the distribution
     * 
     * @return Size of distribution
     */
    size_t getSize() const;
    
    /**
     * @brief Get values vector
     * 
     * @return Vector of values
     */
    const Eigen::VectorXd& getValues() const;
    
    /**
     * @brief Get probabilities vector
     * 
     * @return Vector of probabilities
     */
    const Eigen::VectorXd& getProbabilities() const;
    
    /**
     * @brief Set values vector
     * 
     * @param values New values vector
     */
    void setValues(const Eigen::VectorXd& values);
    
    /**
     * @brief Set probabilities vector
     * 
     * @param probabilities New probabilities vector
     */
    void setProbabilities(const Eigen::VectorXd& probabilities);
    
    /**
     * @brief Create from causal state
     * 
     * @param state Causal state
     * @return ProbabilityDistribution instance
     */
    static ProbabilityDistribution fromCausalState(const CausalState& state);
    
    /**
     * @brief Calculate entropy of the distribution
     * 
     * @return Entropy value
     */
    double entropy() const;
    
    /**
     * @brief Sample from the distribution
     * 
     * @param numSamples Number of samples
     * @return Vector of sampled values
     */
    Eigen::VectorXd sample(size_t numSamples) const;
    
    /**
     * @brief Create product distribution
     * 
     * @param other Other distribution to combine with
     * @return Product distribution
     */
    ProbabilityDistribution product(const ProbabilityDistribution& other) const;
    
    /**
     * @brief Create marginal distribution
     * 
     * @param indices Indices to marginalize over
     * @return Marginal distribution
     */
    ProbabilityDistribution marginalize(const std::vector<size_t>& indices) const;
    
    /**
     * @brief Check if distribution is valid
     * 
     * @return True if distribution is valid
     */
    bool isValid() const;
    
    /**
     * @brief Normalize distribution
     */
    void normalize();
    
    /**
     * @brief Get expectation of a function
     * 
     * @param function Function to apply to values
     * @return Expectation value
     */
    double expectation(std::function<double(double)> function) const;
    
    /**
     * @brief Get covariance matrix
     * 
     * @return Covariance matrix
     */
    Eigen::MatrixXd covariance() const;
    
    /**
     * @brief Create Gaussian approximation
     * 
     * @return Gaussian approximation of the distribution
     */
    ProbabilityDistribution toGaussian() const;
    
    /**
     * @brief Calculate Kullback-Leibler divergence
     * 
     * @param other Target distribution
     * @return KL divergence value
     */
    double klDivergence(const ProbabilityDistribution& other) const;
    
    /**
     * @brief Calculate Jensen-Shannon divergence
     * 
     * @param other Target distribution
     * @return JS divergence value
     */
    double jsDistance(const ProbabilityDistribution& other) const;
    
    /**
     * @brief Calculate Wasserstein distance
     * 
     * @param other Target distribution
     * @param p Order of distance (default 2)
     * @return Wasserstein distance value
     */
    double wassersteinDistance(const ProbabilityDistribution& other, int p = 2) const;
    
    /**
     * @brief Calculate Hellinger distance
     * 
     * @param other Target distribution
     * @return Hellinger distance value
     */
    double hellingerDistance(const ProbabilityDistribution& other) const;
    
    /**
     * @brief Calculate total variation distance
     * 
     * @param other Target distribution
     * @return Total variation distance value
     */
    double totalVariationDistance(const ProbabilityDistribution& other) const;
    
    /**
     * @brief Calculate Fisher information matrix
     * 
     * @return Fisher information matrix
     */
    Eigen::MatrixXd fisherInformation() const;
    
    /**
     * @brief Calculate distance using specified metric
     * 
     * @param other Target distribution
     * @param metric Distance metric to use
     * @return Distance value
     */
    double distance(const ProbabilityDistribution& other, DistanceMetric metric) const;
    
private:
    size_t size_;                     ///< Size of the distribution
    Eigen::VectorXd values_;          ///< Vector of values
    Eigen::VectorXd probabilities_;   ///< Vector of probabilities
};

/**
 * @brief Represents a point on the statistical manifold
 * 
 * Encapsulates a probability distribution with geometric properties
 * on the information manifold
 */
class ManifoldPoint {
public:
    /**
     * @brief Constructor
     * 
     * @param distribution Underlying probability distribution
     */
    explicit ManifoldPoint(const ProbabilityDistribution& distribution);
    
    /**
     * @brief Get probability distribution
     * 
     * @return Reference to probability distribution
     */
    const ProbabilityDistribution& getDistribution() const;
    
    /**
     * @brief Get Fisher metric at this point
     * 
     * @return Fisher information matrix
     */
    Eigen::MatrixXd getFisherMetric() const;
    
    /**
     * @brief Calculate geodesic distance to another point
     * 
     * @param other Target point
     * @return Geodesic distance value
     */
    double geodesicDistance(const ManifoldPoint& other) const;
    
    /**
     * @brief Create a point along the geodesic
     * 
     * @param other Target point
     * @param t Parameter value (0-1)
     * @return New point along geodesic
     */
    ManifoldPoint geodesicPoint(const ManifoldPoint& other, double t) const;
    
    /**
     * @brief Calculate alpha-connection at this point
     * 
     * @param alpha Connection parameter
     * @return Connection coefficients
     */
    std::vector<Eigen::MatrixXd> alphaConnection(double alpha) const;
    
    /**
     * @brief Calculate exponential map
     * 
     * @param tangentVector Tangent vector
     * @return New point after exponential map
     */
    ManifoldPoint exponentialMap(const Eigen::VectorXd& tangentVector) const;
    
    /**
     * @brief Calculate logarithmic map
     * 
     * @param other Target point
     * @return Tangent vector from logarithmic map
     */
    Eigen::VectorXd logarithmicMap(const ManifoldPoint& other) const;
    
private:
    ProbabilityDistribution distribution_; ///< Underlying probability distribution
    
    /**
     * @brief Calculate Christoffel symbols
     * 
     * @param alpha Connection parameter
     * @return Christoffel symbols
     */
    std::vector<Eigen::MatrixXd> christoffelSymbols(double alpha) const;
};

/**
 * @brief Represents a manifold of probability distributions
 * 
 * Provides operations on the statistical manifold of probability distributions
 */
class InformationManifold {
public:
    /**
     * @brief Constructor
     * 
     * @param dimension Dimension of the manifold
     */
    explicit InformationManifold(size_t dimension);
    
    /**
     * @brief Get manifold dimension
     * 
     * @return Dimension value
     */
    size_t getDimension() const;
    
    /**
     * @brief Create a point on the manifold
     * 
     * @param distribution Probability distribution
     * @return ManifoldPoint instance
     */
    ManifoldPoint createPoint(const ProbabilityDistribution& distribution) const;
    
    /**
     * @brief Calculate geodesic between points
     * 
     * @param start Start point
     * @param end End point
     * @param numPoints Number of points along geodesic
     * @return Vector of points along geodesic
     */
    std::vector<ManifoldPoint> calculateGeodesic(const ManifoldPoint& start, 
                                               const ManifoldPoint& end, 
                                               size_t numPoints = 10) const;
    
    /**
     * @brief Calculate parallel transport
     * 
     * @param vector Vector to transport
     * @param start Start point
     * @param end End point
     * @return Transported vector
     */
    Eigen::VectorXd parallelTransport(const Eigen::VectorXd& vector, 
                                     const ManifoldPoint& start, 
                                     const ManifoldPoint& end) const;
    
    /**
     * @brief Calculate Riemannian curvature tensor
     * 
     * @param point Manifold point
     * @return Curvature tensor
     */
    std::vector<std::vector<Eigen::MatrixXd>> riemannianCurvature(
        const ManifoldPoint& point) const;
    
    /**
     * @brief Calculate sectional curvature
     * 
     * @param point Manifold point
     * @param v1 First vector
     * @param v2 Second vector
     * @return Sectional curvature value
     */
    double sectionalCurvature(const ManifoldPoint& point, 
                             const Eigen::VectorXd& v1, 
                             const Eigen::VectorXd& v2) const;
    
    /**
     * @brief Calculate scalar curvature
     * 
     * @param point Manifold point
     * @return Scalar curvature value
     */
    double scalarCurvature(const ManifoldPoint& point) const;
    
private:
    size_t dimension_; ///< Dimension of the manifold
    
    /**
     * @brief Solve geodesic equation
     * 
     * @param start Start point
     * @param end End point
     * @param numPoints Number of points to generate
     * @return Solution points
     */
    std::vector<ManifoldPoint> solveGeodesicEquation(const ManifoldPoint& start, 
                                                   const ManifoldPoint& end, 
                                                   size_t numPoints) const;
};

/**
 * @brief Calculates information-theoretic measures
 */
class InformationCalculator {
public:
    /**
     * @brief Constructor
     */
    InformationCalculator();
    
    /**
     * @brief Calculate entropy
     * 
     * @param distribution Probability distribution
     * @return Entropy value
     */
    double calculateEntropy(const ProbabilityDistribution& distribution) const;
    
    /**
     * @brief Calculate joint entropy
     * 
     * @param distributions Vector of probability distributions
     * @return Joint entropy value
     */
    double calculateJointEntropy(const std::vector<ProbabilityDistribution>& distributions) const;
    
    /**
     * @brief Calculate mutual information
     * 
     * @param dist1 First distribution
     * @param dist2 Second distribution
     * @return Mutual information value
     */
    double calculateMutualInformation(const ProbabilityDistribution& dist1, 
                                    const ProbabilityDistribution& dist2) const;
    
    /**
     * @brief Calculate conditional entropy
     * 
     * @param conditioned Distribution to be conditioned
     * @param condition Conditioning distribution
     * @return Conditional entropy value
     */
    double calculateConditionalEntropy(const ProbabilityDistribution& conditioned, 
                                     const ProbabilityDistribution& condition) const;
    
    /**
     * @brief Calculate conditional mutual information
     * 
     * @param dist1 First distribution
     * @param dist2 Second distribution
     * @param condition Conditioning distribution
     * @return Conditional mutual information value
     */
    double calculateConditionalMutualInformation(
        const ProbabilityDistribution& dist1, 
        const ProbabilityDistribution& dist2, 
        const ProbabilityDistribution& condition) const;
    
    /**
     * @brief Calculate multi-information
     * 
     * @param distributions Vector of probability distributions
     * @return Multi-information value
     */
    double calculateMultiInformation(
        const std::vector<ProbabilityDistribution>& distributions) const;
    
    /**
     * @brief Calculate transfer entropy
     * 
     * @param source Source distribution
     * @param target Target distribution
     * @param targetPast Past target distribution
     * @return Transfer entropy value
     */
    double calculateTransferEntropy(const ProbabilityDistribution& source, 
                                  const ProbabilityDistribution& target, 
                                  const ProbabilityDistribution& targetPast) const;
    
    /**
     * @brief Calculate differential entropy
     * 
     * @param mean Mean vector
     * @param covariance Covariance matrix
     * @return Differential entropy value
     */
    double calculateDifferentialEntropy(const Eigen::VectorXd& mean, 
                                      const Eigen::MatrixXd& covariance) const;
    
    /**
     * @brief Calculate partial information decomposition
     * 
     * @param target Target distribution
     * @param source1 First source distribution
     * @param source2 Second source distribution
     * @return Tuple of unique, redundant, and synergistic information
     */
    std::tuple<double, double, double> calculatePartialInformationDecomposition(
        const ProbabilityDistribution& target, 
        const ProbabilityDistribution& source1, 
        const ProbabilityDistribution& source2) const;
    
    /**
     * @brief Calculate stochastic interaction
     * 
     * @param distributions Vector of probability distributions
     * @return Stochastic interaction value
     */
    double calculateStochasticInteraction(
        const std::vector<ProbabilityDistribution>& distributions) const;
    
    /**
     * @brief Calculate excess entropy
     * 
     * @param past Past distribution
     * @param future Future distribution
     * @return Excess entropy value
     */
    double calculateExcessEntropy(const ProbabilityDistribution& past, 
                                const ProbabilityDistribution& future) const;
};

/**
 * @brief Utility class for information-geometric operations
 */
class InformationGeometryUtils {
public:
    /**
     * @brief Calculate distance between distributions
     * 
     * @param dist1 First distribution
     * @param dist2 Second distribution
     * @param metric Distance metric to use
     * @return Distance value
     */
    static double calculateDistance(const ProbabilityDistribution& dist1, 
                                  const ProbabilityDistribution& dist2, 
                                  DistanceMetric metric);
    
    /**
     * @brief Calculate Fisher information matrix
     * 
     * @param distribution Probability distribution
     * @return Fisher information matrix
     */
    static Eigen::MatrixXd calculateFisherInformation(
        const ProbabilityDistribution& distribution);
    
    /**
     * @brief Calculate Wasserstein distance
     * 
     * @param dist1 First distribution
     * @param dist2 Second distribution
     * @param p Order of distance (default 2)
     * @return Wasserstein distance value
     */
    static double calculateWassersteinDistance(const ProbabilityDistribution& dist1, 
                                             const ProbabilityDistribution& dist2, 
                                             int p = 2);
    
    /**
     * @brief Calculate KL divergence
     * 
     * @param dist1 First distribution
     * @param dist2 Second distribution
     * @return KL divergence value
     */
    static double calculateKLDivergence(const ProbabilityDistribution& dist1, 
                                      const ProbabilityDistribution& dist2);
    
    /**
     * @brief Calculate JS distance
     * 
     * @param dist1 First distribution
     * @param dist2 Second distribution
     * @return JS distance value
     */
    static double calculateJSDistance(const ProbabilityDistribution& dist1, 
                                    const ProbabilityDistribution& dist2);
    
    /**
     * @brief Create Gaussian approximation
     * 
     * @param distribution Original distribution
     * @return Gaussian approximation
     */
    static ProbabilityDistribution createGaussianApproximation(
        const ProbabilityDistribution& distribution);
    
    /**
     * @brief Create point on statistical manifold
     * 
     * @param distribution Probability distribution
     * @return ManifoldPoint instance
     */
    static ManifoldPoint createManifoldPoint(const ProbabilityDistribution& distribution);
    
    /**
     * @brief Create information manifold
     * 
     * @param dimension Dimension of manifold
     * @return InformationManifold instance
     */
    static InformationManifold createManifold(size_t dimension);
};

} // namespace phi_calculator
} // namespace ultra

#endif // ULTRA_PHI_CALCULATOR_INFORMATION_GEOMETRY_H_