/**
 * @file partition_search.h
 * @brief Algorithms for finding minimum information partitions
 * 
 * This file provides implementations of various algorithms for finding
 * minimum information partitions (MIPs) in complex systems. These partitions
 * are crucial for calculating integrated information (Φ) in the ULTRA system.
 * 
 * <AUTHOR> Team
 * @date 2025
 */

#ifndef ULTRA_PHI_CALCULATOR_PARTITION_SEARCH_H_
#define ULTRA_PHI_CALCULATOR_PARTITION_SEARCH_H_

#include "causal_analysis.h"

#include <Eigen/Dense>
#include <memory>
#include <vector>
#include <functional>
#include <optional>

namespace ultra {
namespace phi_calculator {

// Forward declarations
class PhiContext;
enum class PartitionSearchStrategy;
struct PhiCalculatorConfig;

/**
 * @brief Base class for partition search algorithms
 * 
 * Abstract base class that defines the interface for finding
 * minimum information partitions.
 */
class PartitionSearcher {
public:
    /**
     * @brief Constructor
     * 
     * @param context Shared pointer to calculation context
     */
    explicit PartitionSearcher(std::shared_ptr<PhiContext> context);
    
    /**
     * @brief Destructor
     */
    virtual ~PartitionSearcher();
    
    /**
     * @brief Find minimum information partition
     * 
     * @param subsystem Vector of element indices
     * @param evaluator Function to evaluate partitions
     * @return Minimum information partition
     */
    virtual Partition findMinimumPartition(
        const std::vector<size_t>& subsystem,
        std::function<double(const Partition&)> evaluator) = 0;
    
    /**
     * @brief Get calculation context
     * 
     * @return Shared pointer to context
     */
    std::shared_ptr<PhiContext> getContext() const;
    
protected:
    std::shared_ptr<PhiContext> context_;  ///< Calculation context
    
    /**
     * @brief Check if search should be canceled
     * 
     * @return True if search should be canceled
     */
    bool shouldCancel() const;
    
    /**
     * @brief Update progress
     * 
     * @param progress Progress value (0.0-1.0)
     */
    void updateProgress(double progress) const;
    
    /**
     * @brief Generate partition key
     * 
     * @param partition Partition to generate key for
     * @return String key
     */
    std::string generatePartitionKey(const Partition& partition) const;
};

/**
 * @brief Exhaustive partition searcher
 * 
 * Implements exhaustive search of all possible partitions.
 */
class ExhaustivePartitionSearcher : public PartitionSearcher {
public:
    /**
     * @brief Constructor
     * 
     * @param context Shared pointer to calculation context
     */
    explicit ExhaustivePartitionSearcher(std::shared_ptr<PhiContext> context);
    
    /**
     * @brief Find minimum information partition by exhaustive search
     * 
     * @param subsystem Vector of element indices
     * @param evaluator Function to evaluate partitions
     * @return Minimum information partition
     */
    Partition findMinimumPartition(
        const std::vector<size_t>& subsystem,
        std::function<double(const Partition&)> evaluator) override;
    
private:
    /**
     * @brief Generate all possible partitions
     * 
     * @param n Size of the set
     * @param maxParts Maximum number of parts (0 for unlimited)
     * @return Vector of all possible partitions
     */
    std::vector<Partition> generateAllPartitions(size_t n, size_t maxParts = 0) const;
    
    /**
     * @brief Generate possible set partitions recursively
     * 
     * @param elements Vector of element indices
     * @param currentPartition Current partition being built
     * @param maxParts Maximum number of parts
     * @param partitions Vector to store generated partitions
     */
    void generatePartitionsRecursive(
        const std::vector<size_t>& elements,
        Partition& currentPartition,
        size_t maxParts,
        std::vector<Partition>& partitions) const;
};

/**
 * @brief Iterative greedy partition searcher
 * 
 * Implements iterative greedy search for finding partitions.
 */
class IterativeGreedyPartitionSearcher : public PartitionSearcher {
public:
    /**
     * @brief Constructor
     * 
     * @param context Shared pointer to calculation context
     */
    explicit IterativeGreedyPartitionSearcher(std::shared_ptr<PhiContext> context);
    
    /**
     * @brief Find minimum information partition by iterative greedy search
     * 
     * @param subsystem Vector of element indices
     * @param evaluator Function to evaluate partitions
     * @return Minimum information partition
     */
    Partition findMinimumPartition(
        const std::vector<size_t>& subsystem,
        std::function<double(const Partition&)> evaluator) override;
    
private:
    /**
     * @brief Try to improve partition by moving elements
     * 
     * @param partition Current partition
     * @param evaluator Function to evaluate partitions
     * @return True if partition was improved
     */
    bool improvePartition(
        Partition& partition,
        std::function<double(const Partition&)> evaluator) const;
    
    /**
     * @brief Find best part for element
     * 
     * @param element Element index
     * @param partition Current partition
     * @param evaluator Function to evaluate partitions
     * @return Best part index
     */
    size_t findBestPart(
        size_t element,
        const Partition& partition,
        std::function<double(const Partition&)> evaluator) const;
    
    /**
     * @brief Try to split parts
     * 
     * @param partition Current partition
     * @param evaluator Function to evaluate partitions
     * @return True if partition was improved
     */
    bool trySplitParts(
        Partition& partition,
        std::function<double(const Partition&)> evaluator) const;
    
    /**
     * @brief Try to merge parts
     * 
     * @param partition Current partition
     * @param evaluator Function to evaluate partitions
     * @return True if partition was improved
     */
    bool tryMergeParts(
        Partition& partition,
        std::function<double(const Partition&)> evaluator) const;
};

/**
 * @brief Heuristic partition searcher
 * 
 * Implements heuristic search based on graph topology.
 */
class HeuristicPartitionSearcher : public PartitionSearcher {
public:
    /**
     * @brief Constructor
     * 
     * @param context Shared pointer to calculation context
     */
    explicit HeuristicPartitionSearcher(std::shared_ptr<PhiContext> context);
    
    /**
     * @brief Find minimum information partition by heuristic search
     * 
     * @param subsystem Vector of element indices
     * @param evaluator Function to evaluate partitions
     * @return Minimum information partition
     */
    Partition findMinimumPartition(
        const std::vector<size_t>& subsystem,
        std::function<double(const Partition&)> evaluator) override;
    
private:
    /**
     * @brief Generate candidate partitions based on graph structure
     * 
     * @param subsystem Vector of element indices
     * @return Vector of candidate partitions
     */
    std::vector<Partition> generateCandidatePartitions(
        const std::vector<size_t>& subsystem) const;
    
    /**
     * @brief Find communities in the causal graph
     * 
     * @param subsystem Vector of element indices
     * @return Vector of communities
     */
    std::vector<std::vector<size_t>> findCommunities(
        const std::vector<size_t>& subsystem) const;
    
    /**
     * @brief Create partitions from communities
     * 
     * @param communities Vector of communities
     * @return Vector of partitions
     */
    std::vector<Partition> createPartitionsFromCommunities(
        const std::vector<std::vector<size_t>>& communities) const;
    
    /**
     * @brief Create variants of partitions
     * 
     * @param basePartition Base partition
     * @param numVariants Number of variants to create
     * @return Vector of variant partitions
     */
    std::vector<Partition> createPartitionVariants(
        const Partition& basePartition,
        size_t numVariants) const;
};

/**
 * @brief Atomic bipartition searcher
 * 
 * Implements partition search using only atomic bipartitions (IIT 3.0).
 */
class AtomicBipartitionSearcher : public PartitionSearcher {
public:
    /**
     * @brief Constructor
     * 
     * @param context Shared pointer to calculation context
     */
    explicit AtomicBipartitionSearcher(std::shared_ptr<PhiContext> context);
    
    /**
     * @brief Find minimum information partition using atomic bipartitions
     * 
     * @param subsystem Vector of element indices
     * @param evaluator Function to evaluate partitions
     * @return Minimum information partition
     */
    Partition findMinimumPartition(
        const std::vector<size_t>& subsystem,
        std::function<double(const Partition&)> evaluator) override;
    
private:
    /**
     * @brief Generate all atomic bipartitions
     * 
     * @param subsystem Vector of element indices
     * @return Vector of atomic bipartitions
     */
    std::vector<Partition> generateAtomicBipartitions(
        const std::vector<size_t>& subsystem) const;
    
    /**
     * @brief Check if bipartition is atomic
     * 
     * @param part1 First part
     * @param part2 Second part
     * @return True if bipartition is atomic
     */
    bool isAtomicBipartition(
        const std::vector<size_t>& part1,
        const std::vector<size_t>& part2) const;
};

/**
 * @brief Hierarchical partition searcher
 * 
 * Implements hierarchical clustering-based partition search.
 */
class HierarchicalPartitionSearcher : public PartitionSearcher {
public:
    /**
     * @brief Constructor
     * 
     * @param context Shared pointer to calculation context
     */
    explicit HierarchicalPartitionSearcher(std::shared_ptr<PhiContext> context);
    
    /**
     * @brief Find minimum information partition using hierarchical clustering
     * 
     * @param subsystem Vector of element indices
     * @param evaluator Function to evaluate partitions
     * @return Minimum information partition
     */
    Partition findMinimumPartition(
        const std::vector<size_t>& subsystem,
        std::function<double(const Partition&)> evaluator) override;
    
private:
    /**
     * @brief Build distance matrix
     * 
     * @param subsystem Vector of element indices
     * @return Distance matrix
     */
    Eigen::MatrixXd buildDistanceMatrix(const std::vector<size_t>& subsystem) const;
    
    /**
     * @brief Perform hierarchical clustering
     * 
     * @param distanceMatrix Distance matrix
     * @param subsystem Vector of element indices
     * @return Vector of clustering results at different levels
     */
    std::vector<Partition> performHierarchicalClustering(
        const Eigen::MatrixXd& distanceMatrix,
        const std::vector<size_t>& subsystem) const;
    
    /**
     * @brief Merge closest clusters
     * 
     * @param clusters Current clusters
     * @param distanceMatrix Distance matrix
     * @return New clusters after merging
     */
    std::vector<std::vector<size_t>> mergeClosestClusters(
        const std::vector<std::vector<size_t>>& clusters,
        const Eigen::MatrixXd& distanceMatrix) const;
    
    /**
     * @brief Calculate cluster distances
     * 
     * @param clusters Vector of clusters
     * @param distanceMatrix Distance matrix
     * @return Matrix of cluster distances
     */
    Eigen::MatrixXd calculateClusterDistances(
        const std::vector<std::vector<size_t>>& clusters,
        const Eigen::MatrixXd& distanceMatrix) const;
};

/**
 * @brief MIP approximation searcher
 * 
 * Implements approximation of MIP using spectral methods.
 */
class MipApproximationSearcher : public PartitionSearcher {
public:
    /**
     * @brief Constructor
     * 
     * @param context Shared pointer to calculation context
     */
    explicit MipApproximationSearcher(std::shared_ptr<PhiContext> context);
    
    /**
     * @brief Find approximate minimum information partition
     * 
     * @param subsystem Vector of element indices
     * @param evaluator Function to evaluate partitions
     * @return Approximate minimum information partition
     */
    Partition findMinimumPartition(
        const std::vector<size_t>& subsystem,
        std::function<double(const Partition&)> evaluator) override;
    
private:
    /**
     * @brief Calculate normalized Laplacian
     * 
     * @param subsystem Vector of element indices
     * @return Normalized Laplacian matrix
     */
    Eigen::MatrixXd calculateNormalizedLaplacian(
        const std::vector<size_t>& subsystem) const;
    
    /**
     * @brief Perform spectral clustering
     * 
     * @param laplacian Laplacian matrix
     * @param subsystem Vector of element indices
     * @param numClusters Number of clusters
     * @return Clustering result
     */
    Partition performSpectralClustering(
        const Eigen::MatrixXd& laplacian,
        const std::vector<size_t>& subsystem,
        size_t numClusters) const;
    
    /**
     * @brief Refine partition
     * 
     * @param partition Initial partition
     * @param evaluator Function to evaluate partitions
     * @return Refined partition
     */
    Partition refinePartition(
        const Partition& partition,
        std::function<double(const Partition&)> evaluator) const;
    
    /**
     * @brief Try different cluster counts
     * 
     * @param laplacian Laplacian matrix
     * @param subsystem Vector of element indices
     * @param evaluator Function to evaluate partitions
     * @return Best partition
     */
    Partition tryDifferentClusterCounts(
        const Eigen::MatrixXd& laplacian,
        const std::vector<size_t>& subsystem,
        std::function<double(const Partition&)> evaluator) const;
};

/**
 * @brief Factory for creating partition searchers
 */
class PartitionSearcherFactory {
public:
    /**
     * @brief Create a partition searcher for a specific strategy
     * 
     * @param strategy Partition search strategy
     * @param context Shared pointer to calculation context
     * @return Unique pointer to partition searcher
     */
    static std::unique_ptr<PartitionSearcher> create(
        PartitionSearchStrategy strategy,
        std::shared_ptr<PhiContext> context);
    
    /**
     * @brief Check if a strategy is supported
     * 
     * @param strategy Partition search strategy
     * @return True if strategy is supported
     */
    static bool isSupported(PartitionSearchStrategy strategy);
    
    /**
     * @brief Get all supported strategies
     * 
     * @return Vector of supported strategies
     */
    static std::vector<PartitionSearchStrategy> getSupportedStrategies();
    
    /**
     * @brief Get strategy name
     * 
     * @param strategy Partition search strategy
     * @return Name string
     */
    static std::string getStrategyName(PartitionSearchStrategy strategy);
    
    /**
     * @brief Get strategy description
     * 
     * @param strategy Partition search strategy
     * @return Description string
     */
    static std::string getStrategyDescription(PartitionSearchStrategy strategy);
    
    /**
     * @brief Get recommended strategy for system size
     * 
     * @param systemSize Size of the system
     * @return Recommended strategy
     */
    static PartitionSearchStrategy getRecommendedStrategy(size_t systemSize);
};

/**
 * @brief Utility functions for partition operations
 */
class PartitionUtils {
public:
    /**
     * @brief Check if partition is valid
     * 
     * @param partition Partition to check
     * @param systemSize Total system size
     * @return True if partition is valid
     */
    static bool isValidPartition(const Partition& partition, size_t systemSize);
    
    /**
     * @brief Get all elements in a partition
     * 
     * @param partition Partition to analyze
     * @return Vector of all element indices
     */
    static std::vector<size_t> getAllElements(const Partition& partition);
    
    /**
     * @brief Generate a random partition
     * 
     * @param systemSize System size
     * @param numParts Number of parts
     * @return Random partition
     */
    static Partition generateRandomPartition(size_t systemSize, size_t numParts);
    
    /**
     * @brief Calculate partition distance
     * 
     * @param partition1 First partition
     * @param partition2 Second partition
     * @return Distance value
     */
    static double calculatePartitionDistance(
        const Partition& partition1,
        const Partition& partition2);
    
    /**
     * @brief Find nearest part for element
     * 
     * @param element Element index
     * @param partition Current partition
     * @param distanceMatrix Distance matrix
     * @return Part index
     */
    static size_t findNearestPart(
        size_t element,
        const Partition& partition,
        const Eigen::MatrixXd& distanceMatrix);
    
    /**
     * @brief Convert bipartition to partition
     * 
     * @param bipartition Bipartition to convert
     * @return Partition representation
     */
    static Partition bipartitionToPartition(const Bipartition& bipartition);
    
    /**
     * @brief Convert partition to string
     * 
     * @param partition Partition to convert
     * @return String representation
     */
    static std::string partitionToString(const Partition& partition);
    
    /**
     * @brief Parse partition from string
     * 
     * @param str String representation
     * @return Partition object
     */
    static Partition parsePartition(const std::string& str);
};

} // namespace phi_calculator
} // namespace ultra

#endif // ULTRA_PHI_CALCULATOR_PARTITION_SEARCH_H_