/**
 * @file consciousness_measures.h
 * @brief Measures related to consciousness in the ULTRA system
 * 
 * This file provides implementations of various consciousness-related measures
 * based on Integrated Information Theory and related frameworks. These measures
 * are used to analyze awareness, integration, and other consciousness-like properties
 * in the ULTRA system.
 * 
 * <AUTHOR> Team
 * @date 2025
 */

#ifndef ULTRA_PHI_CALCULATOR_CONSCIOUSNESS_MEASURES_H_
#define ULTRA_PHI_CALCULATOR_CONSCIOUSNESS_MEASURES_H_

#include "causal_analysis.h"
#include "phi_computation.h"

#include <Eigen/Dense>
#include <memory>
#include <vector>
#include <unordered_map>
#include <string>
#include <optional>

namespace ultra {
namespace phi_calculator {

// Forward declarations
class PhiContext;
class PhiResults;

/**
 * @brief Enumeration of consciousness dimensions
 */
enum class ConsciousnessDimension {
    INTEGRATION,             ///< Integration of information
    DIFFERENTIATION,         ///< Differentiation of information
    CAUSALITY,               ///< Causal power
    INTENTIONALITY,          ///< Directedness toward contents
    GLOBAL_ACCESSIBILITY,    ///< Global availability of information
    INFORMATION_RICHNESS,    ///< Richness of information content
    UNITY,                   ///< Unity of experience
    BOUNDEDNESS,             ///< Boundedness of conscious content
    SELF_REFLEXIVITY,        ///< Self-reflective capability
    TEMPORAL_PERSISTENCE     ///< Persistence over time
};

/**
 * @brief Results of consciousness analysis
 */
class ConsciousnessAnalysisResults {
public:
    /**
     * @brief Constructor
     */
    ConsciousnessAnalysisResults();
    
    /**
     * @brief Set dimension score
     * 
     * @param dimension Consciousness dimension
     * @param score Score value (0.0-1.0)
     */
    void setDimensionScore(ConsciousnessDimension dimension, double score);
    
    /**
     * @brief Get dimension score
     * 
     * @param dimension Consciousness dimension
     * @return Score value
     */
    double getDimensionScore(ConsciousnessDimension dimension) const;
    
    /**
     * @brief Set overall consciousness score
     * 
     * @param score Overall score value (0.0-1.0)
     */
    void setOverallScore(double score);
    
    /**
     * @brief Get overall consciousness score
     * 
     * @return Overall score value
     */
    double getOverallScore() const;
    
    /**
     * @brief Set Phi value
     * 
     * @param phi Phi value
     */
    void setPhi(double phi);
    
    /**
     * @brief Get Phi value
     * 
     * @return Phi value
     */
    double getPhi() const;
    
    /**
     * @brief Set complexes
     * 
     * @param complexes Vector of PhiResults for complexes
     */
    void setComplexes(const std::vector<PhiResults>& complexes);
    
    /**
     * @brief Get complexes
     * 
     * @return Vector of PhiResults for complexes
     */
    const std::vector<PhiResults>& getComplexes() const;
    
    /**
     * @brief Add additional measure
     * 
     * @param name Measure name
     * @param value Measure value
     */
    void addMeasure(const std::string& name, double value);
    
    /**
     * @brief Get additional measure
     * 
     * @param name Measure name
     * @return Optional measure value
     */
    std::optional<double> getMeasure(const std::string& name) const;
    
    /**
     * @brief Get all additional measures
     * 
     * @return Map of measure names to values
     */
    const std::unordered_map<std::string, double>& getAllMeasures() const;
    
    /**
     * @brief Generate report
     * 
     * @param format Report format ("text", "json", "xml")
     * @return Report string
     */
    std::string generateReport(const std::string& format = "text") const;
    
private:
    std::unordered_map<ConsciousnessDimension, double> dimensionScores_; ///< Scores by dimension
    double overallScore_;                                               ///< Overall score
    double phi_;                                                        ///< Phi value
    std::vector<PhiResults> complexes_;                                 ///< Phi complexes
    std::unordered_map<std::string, double> additionalMeasures_;        ///< Additional measures
    
    /**
     * @brief Generate text report
     * 
     * @return Text report
     */
    std::string generateTextReport() const;
    
    /**
     * @brief Generate JSON report
     * 
     * @return JSON report
     */
    std::string generateJsonReport() const;
    
    /**
     * @brief Generate XML report
     * 
     * @return XML report
     */
    std::string generateXmlReport() const;
};

/**
 * @brief Analyzer for consciousness-related measures
 */
class ConsciousnessAnalyzer {
public:
    /**
     * @brief Constructor
     * 
     * @param context Shared pointer to calculation context
     */
    explicit ConsciousnessAnalyzer(std::shared_ptr<PhiContext> context);
    
    /**
     * @brief Analyze consciousness of the system
     * 
     * @return ConsciousnessAnalysisResults object
     */
    ConsciousnessAnalysisResults analyze();
    
    /**
     * @brief Analyze integration
     * 
     * @return Integration score (0.0-1.0)
     */
    double analyzeIntegration();
    
    /**
     * @brief Analyze differentiation
     * 
     * @return Differentiation score (0.0-1.0)
     */
    double analyzeDifferentiation();
    
    /**
     * @brief Analyze causality
     * 
     * @return Causality score (0.0-1.0)
     */
    double analyzeCausality();
    
    /**
     * @brief Analyze intentionality
     * 
     * @return Intentionality score (0.0-1.0)
     */
    double analyzeIntentionality();
    
    /**
     * @brief Analyze global accessibility
     * 
     * @return Global accessibility score (0.0-1.0)
     */
    double analyzeGlobalAccessibility();
    
    /**
     * @brief Analyze information richness
     * 
     * @return Information richness score (0.0-1.0)
     */
    double analyzeInformationRichness();
    
    /**
     * @brief Analyze unity
     * 
     * @return Unity score (0.0-1.0)
     */
    double analyzeUnity();
    
    /**
     * @brief Analyze boundedness
     * 
     * @return Boundedness score (0.0-1.0)
     */
    double analyzeBoundedness();
    
    /**
     * @brief Analyze self-reflexivity
     * 
     * @return Self-reflexivity score (0.0-1.0)
     */
    double analyzeSelfReflexivity();
    
    /**
     * @brief Analyze temporal persistence
     * 
     * @return Temporal persistence score (0.0-1.0)
     */
    double analyzeTemporalPersistence();
    
    /**
     * @brief Calculate causal emergence
     * 
     * @return Causal emergence value
     */
    double calculateCausalEmergence();
    
    /**
     * @brief Calculate causal density
     * 
     * @return Causal density value
     */
    double calculateCausalDensity();
    
    /**
     * @brief Calculate causal dilution
     * 
     * @return Causal dilution value
     */
    double calculateCausalDilution();
    
    /**
     * @brief Calculate causal asymmetry
     * 
     * @return Causal asymmetry value
     */
    double calculateCausalAsymmetry();
    
    /**
     * @brief Calculate autonomy
     * 
     * @return Autonomy value
     */
    double calculateAutonomy();
    
    /**
     * @brief Calculate self-organization
     * 
     * @return Self-organization value
     */
    double calculateSelfOrganization();
    
    /**
     * @brief Calculate autopoiesis
     * 
     * @return Autopoiesis value
     */
    double calculateAutopoiesis();
    
    /**
     * @brief Calculate self-maintenance
     * 
     * @return Self-maintenance value
     */
    double calculateSelfMaintenance();
    
    /**
     * @brief Calculate global workspace capacity
     * 
     * @return Global workspace capacity value
     */
    double calculateGlobalWorkspaceCapacity();
    
    /**
     * @brief Calculate metastability
     * 
     * @return Metastability value
     */
    double calculateMetastability();
    
    /**
     * @brief Calculate complexity
     * 
     * @return Complexity value
     */
    double calculateComplexity();
    
    /**
     * @brief Calculate criticality
     * 
     * @return Criticality value
     */
    double calculateCriticality();
    
    /**
     * @brief Calculate small-worldness
     * 
     * @return Small-worldness value
     */
    double calculateSmallWorldness();
    
    /**
     * @brief Calculate modularity
     * 
     * @return Modularity value
     */
    double calculateModularity();
    
    /**
     * @brief Calculate rich club coefficient
     * 
     * @return Rich club coefficient value
     */
    double calculateRichClubCoefficient();
    
    /**
     * @brief Set recalculation flag
     * 
     * @param recalculate Whether to recalculate values
     */
    void setRecalculate(bool recalculate);
    
    /**
     * @brief Get recalculation flag
     * 
     * @return Whether recalculation is enabled
     */
    bool getRecalculate() const;
    
private:
    std::shared_ptr<PhiContext> context_;                      ///< Calculation context
    std::shared_ptr<CausalGraph> graph_;                       ///< Causal graph
    ConsciousnessAnalysisResults results_;                     ///< Analysis results
    bool recalculate_ = true;                                 ///< Whether to recalculate values
    std::unordered_map<std::string, double> cachedValues_;     ///< Cached calculation values
    
    /**
     * @brief Get cached value
     * 
     * @param key Cache key
     * @return Optional cached value
     */
    std::optional<double> getCachedValue(const std::string& key) const;
    
    /**
     * @brief Set cached value
     * 
     * @param key Cache key
     * @param value Value to cache
     */
    void setCachedValue(const std::string& key, double value);
    
    /**
     * @brief Calculate overall consciousness score
     * 
     * @return Overall score value
     */
    double calculateOverallScore();
    
    /**
     * @brief Get dimension weight
     * 
     * @param dimension Consciousness dimension
     * @return Weight value
     */
    double getDimensionWeight(ConsciousnessDimension dimension) const;
};

/**
 * @brief Measurer for various consciousness-related properties
 */
class ConsciousnessMeasurer {
public:
    /**
     * @brief Constructor
     */
    ConsciousnessMeasurer();
    
    /**
     * @brief Measure IIT-based consciousness
     * 
     * @param results Phi calculation results
     * @return Consciousness measure value
     */
    double measureIITConsciousness(const PhiResults& results) const;
    
    /**
     * @brief Measure Global Workspace-based consciousness
     * 
     * @param accessibility Global accessibility value
     * @param integration Integration value
     * @return Consciousness measure value
     */
    double measureGWConsciousness(double accessibility, double integration) const;
    
    /**
     * @brief Measure Higher-Order Thought-based consciousness
     * 
     * @param reflexivity Self-reflexivity value
     * @param metaRepresentation Meta-representation capability
     * @return Consciousness measure value
     */
    double measureHOTConsciousness(double reflexivity, double metaRepresentation) const;
    
    /**
     * @brief Measure Predictive Processing-based consciousness
     * 
     * @param predictionError Prediction error value
     * @param predictionCapacity Prediction capacity value
     * @return Consciousness measure value
     */
    double measurePPConsciousness(double predictionError, double predictionCapacity) const;
    
    /**
     * @brief Measure Recurrent Processing-based consciousness
     * 
     * @param recurrence Recurrence value
     * @param stability Stability value
     * @return Consciousness measure value
     */
    double measureRPConsciousness(double recurrence, double stability) const;
    
    /**
     * @brief Measure Attention Schema-based consciousness
     * 
     * @param attentionControl Attention control value
     * @param selfModel Self-model value
     * @return Consciousness measure value
     */
    double measureASConsciousness(double attentionControl, double selfModel) const;
    
    /**
     * @brief Combine multiple consciousness measures
     * 
     * @param measures Vector of measure values
     * @param weights Vector of measure weights
     * @return Combined measure value
     */
    double combineMeasures(
        const std::vector<double>& measures,
        const std::vector<double>& weights) const;
};

/**
 * @brief Generator for consciousness-related reports
 */
class ConsciousnessReportGenerator {
public:
    /**
     * @brief Constructor
     * 
     * @param analysis Analysis results
     */
    explicit ConsciousnessReportGenerator(const ConsciousnessAnalysisResults& analysis);
    
    /**
     * @brief Generate summary report
     * 
     * @return Summary report string
     */
    std::string generateSummaryReport() const;
    
    /**
     * @brief Generate detailed report
     * 
     * @return Detailed report string
     */
    std::string generateDetailedReport() const;
    
    /**
     * @brief Generate technical report
     * 
     * @return Technical report string
     */
    std::string generateTechnicalReport() const;
    
    /**
     * @brief Generate comparison report
     * 
     * @param otherAnalysis Other analysis results to compare with
     * @return Comparison report string
     */
    std::string generateComparisonReport(
        const ConsciousnessAnalysisResults& otherAnalysis) const;
    
    /**
     * @brief Generate JSON report
     * 
     * @param includeComplexes Whether to include complex details
     * @return JSON report string
     */
    std::string generateJsonReport(bool includeComplexes = true) const;
    
    /**
     * @brief Generate XML report
     * 
     * @param includeComplexes Whether to include complex details
     * @return XML report string
     */
    std::string generateXmlReport(bool includeComplexes = true) const;
    
    /**
     * @brief Generate CSV report
     * 
     * @return CSV report string
     */
    std::string generateCsvReport() const;
    
    /**
     * @brief Generate visualization data
     * 
     * @param format Visualization format
     * @return Visualization data string
     */
    std::string generateVisualizationData(const std::string& format) const;
    
private:
    const ConsciousnessAnalysisResults& analysis_; ///< Analysis results
    
    /**
     * @brief Generate dimension section
     * 
     * @param dimension Consciousness dimension
     * @return Report section string
     */
    std::string generateDimensionSection(ConsciousnessDimension dimension) const;
    
    /**
     * @brief Generate complexes section
     * 
     * @return Report section string
     */
    std::string generateComplexesSection() const;
    
    /**
     * @brief Generate measures section
     * 
     * @return Report section string
     */
    std::string generateMeasuresSection() const;
    
    /**
     * @brief Get dimension name
     * 
     * @param dimension Consciousness dimension
     * @return Name string
     */
    std::string getDimensionName(ConsciousnessDimension dimension) const;
    
    /**
     * @brief Get dimension description
     * 
     * @param dimension Consciousness dimension
     * @return Description string
     */
    std::string getDimensionDescription(ConsciousnessDimension dimension) const;
};

} // namespace phi_calculator
} // namespace ultra

#endif // ULTRA_PHI_CALCULATOR_CONSCIOUSNESS_MEASURES_H_