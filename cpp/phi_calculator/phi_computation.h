/**
 * @file phi_computation.h
 * @brief Core algorithms for Phi computation based on Integrated Information Theory
 * 
 * This file provides implementations of various Phi (Φ) measures from Integrated
 * Information Theory and related frameworks. It includes algorithms for 
 * calculating different formulations of integrated information and the 
 * supporting mathematical operations needed for these calculations.
 * 
 * <AUTHOR> Team
 * @date 2025
 */

#ifndef ULTRA_PHI_CALCULATOR_PHI_COMPUTATION_H_
#define ULTRA_PHI_CALCULATOR_PHI_COMPUTATION_H_

#include "causal_analysis.h"
#include "information_geometry.h"

#include <Eigen/Dense>
#include <memory>
#include <vector>
#include <unordered_map>
#include <functional>
#include <optional>

namespace ultra {
namespace phi_calculator {

// Forward declarations
class PhiContext;
class PhiResults;
struct PhiCalculatorConfig;

/**
 * @brief Base class for Phi computation algorithms
 * 
 * Abstract base class that defines the interface for calculating
 * different Phi measures.
 */
class PhiComputer {
public:
    /**
     * @brief Constructor
     * 
     * @param context Shared pointer to calculation context
     */
    explicit PhiComputer(std::shared_ptr<PhiContext> context);
    
    /**
     * @brief Destructor
     */
    virtual ~PhiComputer();
    
    /**
     * @brief Calculate Phi for a subsystem
     * 
     * @param subsystem Vector of element indices
     * @return PhiResults object
     */
    virtual PhiResults calculate(const std::vector<size_t>& subsystem) = 0;
    
    /**
     * @brief Get calculation context
     * 
     * @return Shared pointer to context
     */
    std::shared_ptr<PhiContext> getContext() const;
    
protected:
    std::shared_ptr<PhiContext> context_;  ///< Calculation context
    
    /**
     * @brief Create a subgraph for a subsystem
     * 
     * @param subsystem Vector of element indices
     * @return Shared pointer to subgraph
     */
    std::shared_ptr<CausalGraph> createSubgraph(const std::vector<size_t>& subsystem) const;
    
    /**
     * @brief Get a causal analyzer for a subsystem
     * 
     * @param subsystem Vector of element indices
     * @return Shared pointer to analyzer
     */
    std::shared_ptr<CausalAnalyzer> getAnalyzer(const std::vector<size_t>& subsystem);
    
    /**
     * @brief Check if calculation should be canceled
     * 
     * @return True if calculation should be canceled
     */
    bool shouldCancel() const;
    
    /**
     * @brief Update progress
     * 
     * @param progress Progress value (0.0-1.0)
     */
    void updateProgress(double progress) const;
};

/**
 * @brief Phi computer for the Φ_max measure from IIT 3.0
 * 
 * Implements the Φ_max measure, which is the maximum Φ
 * across temporal scales.
 */
class PhiMaxComputer : public PhiComputer {
public:
    /**
     * @brief Constructor
     * 
     * @param context Shared pointer to calculation context
     */
    explicit PhiMaxComputer(std::shared_ptr<PhiContext> context);
    
    /**
     * @brief Calculate Φ_max for a subsystem
     * 
     * @param subsystem Vector of element indices
     * @return PhiResults object
     */
    PhiResults calculate(const std::vector<size_t>& subsystem) override;
    
private:
    /**
     * @brief Calculate Φ for a specific time scale
     * 
     * @param subsystem Vector of element indices
     * @param timeScale Time scale to consider
     * @return PhiResults object
     */
    PhiResults calculateForTimeScale(const std::vector<size_t>& subsystem, size_t timeScale);
};

/**
 * @brief Phi computer for the Φ^E (effective information) measure from IIT 3.0
 */
class PhiEffectiveComputer : public PhiComputer {
public:
    /**
     * @brief Constructor
     * 
     * @param context Shared pointer to calculation context
     */
    explicit PhiEffectiveComputer(std::shared_ptr<PhiContext> context);
    
    /**
     * @brief Calculate Φ^E for a subsystem
     * 
     * @param subsystem Vector of element indices
     * @return PhiResults object
     */
    PhiResults calculate(const std::vector<size_t>& subsystem) override;
    
private:
    /**
     * @brief Calculate effective information
     * 
     * @param analyzer Causal analyzer
     * @return Effective information value
     */
    double calculateEffectiveInformation(std::shared_ptr<CausalAnalyzer> analyzer);
};

/**
 * @brief Phi computer for the Φ^AR (atomic Φ using Φ^R for parts) measure from IIT 3.0
 */
class PhiAtomicComputer : public PhiComputer {
public:
    /**
     * @brief Constructor
     * 
     * @param context Shared pointer to calculation context
     */
    explicit PhiAtomicComputer(std::shared_ptr<PhiContext> context);
    
    /**
     * @brief Calculate Φ^AR for a subsystem
     * 
     * @param subsystem Vector of element indices
     * @return PhiResults object
     */
    PhiResults calculate(const std::vector<size_t>& subsystem) override;
    
private:
    /**
     * @brief Calculate MIP using atomic bipartitions
     * 
     * @param analyzer Causal analyzer
     * @return Minimum information partition
     */
    Partition findAtomicPartition(std::shared_ptr<CausalAnalyzer> analyzer);
    
    /**
     * @brief Calculate Φ for a partition
     * 
     * @param analyzer Causal analyzer
     * @param partition System partition
     * @return Φ value
     */
    double calculatePartitionPhi(std::shared_ptr<CausalAnalyzer> analyzer, const Partition& partition);
};

/**
 * @brief Phi computer for the Φ* (stochastic interaction) measure
 */
class PhiStarComputer : public PhiComputer {
public:
    /**
     * @brief Constructor
     * 
     * @param context Shared pointer to calculation context
     */
    explicit PhiStarComputer(std::shared_ptr<PhiContext> context);
    
    /**
     * @brief Calculate Φ* for a subsystem
     * 
     * @param subsystem Vector of element indices
     * @return PhiResults object
     */
    PhiResults calculate(const std::vector<size_t>& subsystem) override;
    
private:
    /**
     * @brief Calculate stochastic interaction
     * 
     * @param subsystem Vector of element indices
     * @return Stochastic interaction value
     */
    double calculateStochasticInteraction(const std::vector<size_t>& subsystem);
    
    /**
     * @brief Find partition minimizing stochastic interaction
     * 
     * @param subsystem Vector of element indices
     * @return Minimum partition
     */
    Partition findMinimumStochasticPartition(const std::vector<size_t>& subsystem);
};

/**
 * @brief Phi computer for the ΦE (excess information) measure
 */
class ExcessInformationComputer : public PhiComputer {
public:
    /**
     * @brief Constructor
     * 
     * @param context Shared pointer to calculation context
     */
    explicit ExcessInformationComputer(std::shared_ptr<PhiContext> context);
    
    /**
     * @brief Calculate ΦE for a subsystem
     * 
     * @param subsystem Vector of element indices
     * @return PhiResults object
     */
    PhiResults calculate(const std::vector<size_t>& subsystem) override;
    
private:
    /**
     * @brief Calculate whole system mutual information
     * 
     * @param subsystem Vector of element indices
     * @return Mutual information value
     */
    double calculateWholeMutualInformation(const std::vector<size_t>& subsystem);
    
    /**
     * @brief Calculate sum of partition mutual information
     * 
     * @param subsystem Vector of element indices
     * @param partition System partition
     * @return Sum of mutual information
     */
    double calculatePartitionMutualInformation(const std::vector<size_t>& subsystem, 
                                             const Partition& partition);
};

/**
 * @brief Phi computer for the S (synergistic information) measure
 */
class SynergyComputer : public PhiComputer {
public:
    /**
     * @brief Constructor
     * 
     * @param context Shared pointer to calculation context
     */
    explicit SynergyComputer(std::shared_ptr<PhiContext> context);
    
    /**
     * @brief Calculate S for a subsystem
     * 
     * @param subsystem Vector of element indices
     * @return PhiResults object
     */
    PhiResults calculate(const std::vector<size_t>& subsystem) override;
    
private:
    /**
     * @brief Calculate synergistic information
     * 
     * @param subsystem Vector of element indices
     * @return Synergistic information value
     */
    double calculateSynergy(const std::vector<size_t>& subsystem);
    
    /**
     * @brief Calculate minimum synergy partition
     * 
     * @param subsystem Vector of element indices
     * @return Minimum partition
     */
    Partition findMinimumSynergyPartition(const std::vector<size_t>& subsystem);
};

/**
 * @brief Phi computer for the SI (stochastic interaction) alternate measure
 */
class StochasticInteractionComputer : public PhiComputer {
public:
    /**
     * @brief Constructor
     * 
     * @param context Shared pointer to calculation context
     */
    explicit StochasticInteractionComputer(std::shared_ptr<PhiContext> context);
    
    /**
     * @brief Calculate SI for a subsystem
     * 
     * @param subsystem Vector of element indices
     * @return PhiResults object
     */
    PhiResults calculate(const std::vector<size_t>& subsystem) override;
    
private:
    /**
     * @brief Calculate entropy for system with time lag
     * 
     * @param subsystem Vector of element indices
     * @param timeLag Time lag to consider
     * @return Entropy value
     */
    double calculateTimeLaggedEntropy(const std::vector<size_t>& subsystem, size_t timeLag);
    
    /**
     * @brief Calculate minimum entropy partition
     * 
     * @param subsystem Vector of element indices
     * @param timeLag Time lag to consider
     * @return Minimum partition
     */
    Partition findMinimumEntropyPartition(const std::vector<size_t>& subsystem, size_t timeLag);
};

/**
 * @brief Phi computer for the CD (causal density) measure
 */
class CausalDensityComputer : public PhiComputer {
public:
    /**
     * @brief Constructor
     * 
     * @param context Shared pointer to calculation context
     */
    explicit CausalDensityComputer(std::shared_ptr<PhiContext> context);
    
    /**
     * @brief Calculate CD for a subsystem
     * 
     * @param subsystem Vector of element indices
     * @return PhiResults object
     */
    PhiResults calculate(const std::vector<size_t>& subsystem) override;
    
private:
    /**
     * @brief Calculate causal density
     * 
     * @param subsystem Vector of element indices
     * @return Causal density value
     */
    double calculateCausalDensity(const std::vector<size_t>& subsystem);
    
    /**
     * @brief Calculate Granger causality between elements
     * 
     * @param sourceIdx Source element index
     * @param targetIdx Target element index
     * @param subsystem Full subsystem containing elements
     * @return Granger causality value
     */
    double calculateGrangerCausality(size_t sourceIdx, size_t targetIdx, 
                                   const std::vector<size_t>& subsystem);
};

/**
 * @brief Phi computer for the Φ_DKL (integrated information using KL divergence) measure
 */
class PhiDklComputer : public PhiComputer {
public:
    /**
     * @brief Constructor
     * 
     * @param context Shared pointer to calculation context
     */
    explicit PhiDklComputer(std::shared_ptr<PhiContext> context);
    
    /**
     * @brief Calculate Φ_DKL for a subsystem
     * 
     * @param subsystem Vector of element indices
     * @return PhiResults object
     */
    PhiResults calculate(const std::vector<size_t>& subsystem) override;
    
private:
    /**
     * @brief Calculate effective information using KL divergence
     * 
     * @param analyzer Causal analyzer
     * @return Effective information value
     */
    double calculateKLEffectiveInformation(std::shared_ptr<CausalAnalyzer> analyzer);
    
    /**
     * @brief Find MIP using KL divergence
     * 
     * @param analyzer Causal analyzer
     * @return Minimum information partition
     */
    Partition findKLMinimumPartition(std::shared_ptr<CausalAnalyzer> analyzer);
};

/**
 * @brief Phi computer for the Φ_G (geometric integrated information) measure
 */
class PhiGeometricComputer : public PhiComputer {
public:
    /**
     * @brief Constructor
     * 
     * @param context Shared pointer to calculation context
     */
    explicit PhiGeometricComputer(std::shared_ptr<PhiContext> context);
    
    /**
     * @brief Calculate Φ_G for a subsystem
     * 
     * @param subsystem Vector of element indices
     * @return PhiResults object
     */
    PhiResults calculate(const std::vector<size_t>& subsystem) override;
    
private:
    /**
     * @brief Calculate geometric effective information
     * 
     * @param analyzer Causal analyzer
     * @return Effective information value
     */
    double calculateGeometricInformation(std::shared_ptr<CausalAnalyzer> analyzer);
    
    /**
     * @brief Find MIP using Wasserstein distance
     * 
     * @param analyzer Causal analyzer
     * @return Minimum information partition
     */
    Partition findWassersteinMinimumPartition(std::shared_ptr<CausalAnalyzer> analyzer);
};

/**
 * @brief Phi computer for the Φ_WMS (whole-minus-sum integrated information) measure
 */
class PhiWmsComputer : public PhiComputer {
public:
    /**
     * @brief Constructor
     * 
     * @param context Shared pointer to calculation context
     */
    explicit PhiWmsComputer(std::shared_ptr<PhiContext> context);
    
    /**
     * @brief Calculate Φ_WMS for a subsystem
     * 
     * @param subsystem Vector of element indices
     * @return PhiResults object
     */
    PhiResults calculate(const std::vector<size_t>& subsystem) override;
    
private:
    /**
     * @brief Calculate whole system information
     * 
     * @param analyzer Causal analyzer
     * @return Whole system information value
     */
    double calculateWholeInformation(std::shared_ptr<CausalAnalyzer> analyzer);
    
    /**
     * @brief Calculate sum of part information
     * 
     * @param analyzer Causal analyzer
     * @param partition System partition
     * @return Sum of part information values
     */
    double calculateSumPartInformation(std::shared_ptr<CausalAnalyzer> analyzer, 
                                     const Partition& partition);
};

/**
 * @brief Factory for creating Phi computers
 */
class PhiComputerFactory {
public:
    /**
     * @brief Create a Phi computer for a specific measure
     * 
     * @param measure Phi measure to use
     * @param context Shared pointer to calculation context
     * @return Unique pointer to Phi computer
     */
    static std::unique_ptr<PhiComputer> create(PhiMeasure measure, 
                                            std::shared_ptr<PhiContext> context);
    
    /**
     * @brief Check if a measure is supported
     * 
     * @param measure Phi measure to check
     * @return True if measure is supported
     */
    static bool isSupported(PhiMeasure measure);
    
    /**
     * @brief Get all supported measures
     * 
     * @return Vector of supported measures
     */
    static std::vector<PhiMeasure> getSupportedMeasures();
    
    /**
     * @brief Get measure name
     * 
     * @param measure Phi measure
     * @return Name string
     */
    static std::string getMeasureName(PhiMeasure measure);
    
    /**
     * @brief Get measure description
     * 
     * @param measure Phi measure
     * @return Description string
     */
    static std::string getMeasureDescription(PhiMeasure measure);
};

} // namespace phi_calculator
} // namespace ultra

#endif // ULTRA_PHI_CALCULATOR_PHI_COMPUTATION_H_