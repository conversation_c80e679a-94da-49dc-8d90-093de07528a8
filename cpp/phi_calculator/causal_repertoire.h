/**
 * @file causal_repertoire.h
 * @brief Implementation of cause and effect repertoires from IIT
 * 
 * This file provides implementations of cause and effect repertoires
 * as defined in Integrated Information Theory (IIT). These repertoires
 * represent the causal constraints that a system or its parts impose
 * on its own past and future states.
 * 
 * <AUTHOR> Team
 * @date 2025
 */

#ifndef ULTRA_PHI_CALCULATOR_CAUSAL_REPERTOIRE_H_
#define ULTRA_PHI_CALCULATOR_CAUSAL_REPERTOIRE_H_

#include "causal_analysis.h"
#include "information_geometry.h"

#include <Eigen/Dense>
#include <memory>
#include <vector>
#include <unordered_map>
#include <functional>
#include <string>

namespace ultra {
namespace phi_calculator {

// Forward declarations
class PhiContext;
class ProbabilityDistribution;

/**
 * @brief Enumeration of repertoire types
 */
enum class RepertoireType {
    CAUSE,      ///< Cause repertoire (past-oriented)
    EFFECT,     ///< Effect repertoire (future-oriented)
    CAUSAL_INFO ///< Causal information (bidirectional)
};

/**
 * @brief Causal repertoire representing system constraints
 * 
 * Implements cause and effect repertoires as probability distributions
 * over possible system states, given causal constraints
 */
class CausalRepertoire {
public:
    /**
     * @brief Constructor
     * 
     * @param distribution Probability distribution
     * @param type Repertoire type
     */
    CausalRepertoire(const ProbabilityDistribution& distribution, 
                    RepertoireType type = RepertoireType::EFFECT);
    
    /**
     * @brief Get repertoire type
     * 
     * @return Repertoire type
     */
    RepertoireType getType() const;
    
    /**
     * @brief Get probability distribution
     * 
     * @return Reference to probability distribution
     */
    const ProbabilityDistribution& getDistribution() const;
    
    /**
     * @brief Calculate entropy of the repertoire
     * 
     * @return Entropy value
     */
    double entropy() const;
    
    /**
     * @brief Calculate distance to another repertoire
     * 
     * @param other Other repertoire
     * @param metric Distance metric to use
     * @return Distance value
     */
    double distance(const CausalRepertoire& other, DistanceMetric metric) const;
    
    /**
     * @brief Calculate Earth Mover's Distance to another repertoire
     * 
     * @param other Other repertoire
     * @return EMD value
     */
    double earthMoversDistance(const CausalRepertoire& other) const;
    
    /**
     * @brief Calculate KL divergence to another repertoire
     * 
     * @param other Other repertoire
     * @return KL divergence value
     */
    double klDivergence(const CausalRepertoire& other) const;
    
    /**
     * @brief Create a marginalized repertoire
     * 
     * @param indices Indices to marginalize over
     * @return Marginalized repertoire
     */
    CausalRepertoire marginalize(const std::vector<size_t>& indices) const;
    
    /**
     * @brief Create a conditional repertoire
     * 
     * @param indices Indices to condition on
     * @param values Values to condition on
     * @return Conditional repertoire
     */
    CausalRepertoire condition(const std::vector<size_t>& indices, 
                             const Eigen::VectorXd& values) const;
    
    /**
     * @brief Create product of repertoires
     * 
     * @param other Other repertoire
     * @return Product repertoire
     */
    CausalRepertoire product(const CausalRepertoire& other) const;
    
private:
    ProbabilityDistribution distribution_; ///< Underlying probability distribution
    RepertoireType type_;                  ///< Repertoire type
};

/**
 * @brief Generator for causal repertoires
 * 
 * Implements algorithms for generating cause and effect repertoires
 * from causal graphs and system states
 */
class CausalRepertoireGenerator {
public:
    /**
     * @brief Constructor
     * 
     * @param context Shared pointer to calculation context
     */
    explicit CausalRepertoireGenerator(std::shared_ptr<PhiContext> context);
    
    /**
     * @brief Generate cause repertoire
     * 
     * @param mechanism Mechanism elements indices
     * @param purview Purview elements indices
     * @param state Current system state
     * @return Cause repertoire
     */
    CausalRepertoire generateCauseRepertoire(
        const std::vector<size_t>& mechanism,
        const std::vector<size_t>& purview,
        const std::shared_ptr<CausalState>& state);
    
    /**
     * @brief Generate effect repertoire
     * 
     * @param mechanism Mechanism elements indices
     * @param purview Purview elements indices
     * @param state Current system state
     * @return Effect repertoire
     */
    CausalRepertoire generateEffectRepertoire(
        const std::vector<size_t>& mechanism,
        const std::vector<size_t>& purview,
        const std::shared_ptr<CausalState>& state);
    
    /**
     * @brief Generate unconstrained cause repertoire
     * 
     * @param purview Purview elements indices
     * @return Unconstrained cause repertoire
     */
    CausalRepertoire generateUnconstrainedCauseRepertoire(
        const std::vector<size_t>& purview);
    
    /**
     * @brief Generate unconstrained effect repertoire
     * 
     * @param purview Purview elements indices
     * @return Unconstrained effect repertoire
     */
    CausalRepertoire generateUnconstrainedEffectRepertoire(
        const std::vector<size_t>& purview);
    
    /**
     * @brief Calculate cause information
     * 
     * @param mechanism Mechanism elements indices
     * @param purview Purview elements indices
     * @param state Current system state
     * @return Cause information value
     */
    double calculateCauseInformation(
        const std::vector<size_t>& mechanism,
        const std::vector<size_t>& purview,
        const std::shared_ptr<CausalState>& state);
    
    /**
     * @brief Calculate effect information
     * 
     * @param mechanism Mechanism elements indices
     * @param purview Purview elements indices
     * @param state Current system state
     * @return Effect information value
     */
    double calculateEffectInformation(
        const std::vector<size_t>& mechanism,
        const std::vector<size_t>& purview,
        const std::shared_ptr<CausalState>& state);
    
    /**
     * @brief Calculate integrated cause information
     * 
     * @param mechanism Mechanism elements indices
     * @param purview Purview elements indices
     * @param state Current system state
     * @return Integrated cause information value
     */
    double calculateIntegratedCauseInformation(
        const std::vector<size_t>& mechanism,
        const std::vector<size_t>& purview,
        const std::shared_ptr<CausalState>& state);
    
    /**
     * @brief Calculate integrated effect information
     * 
     * @param mechanism Mechanism elements indices
     * @param purview Purview elements indices
     * @param state Current system state
     * @return Integrated effect information value
     */
    double calculateIntegratedEffectInformation(
        const std::vector<size_t>& mechanism,
        const std::vector<size_t>& purview,
        const std::shared_ptr<CausalState>& state);
    
    /**
     * @brief Find minimum information partition for cause
     * 
     * @param mechanism Mechanism elements indices
     * @param purview Purview elements indices
     * @param state Current system state
     * @return Minimum information partition
     */
    Partition findCauseMIP(
        const std::vector<size_t>& mechanism,
        const std::vector<size_t>& purview,
        const std::shared_ptr<CausalState>& state);
    
    /**
     * @brief Find minimum information partition for effect
     * 
     * @param mechanism Mechanism elements indices
     * @param purview Purview elements indices
     * @param state Current system state
     * @return Minimum information partition
     */
    Partition findEffectMIP(
        const std::vector<size_t>& mechanism,
        const std::vector<size_t>& purview,
        const std::shared_ptr<CausalState>& state);
    
    /**
     * @brief Calculate cause-effect information
     * 
     * @param mechanism Mechanism elements indices
     * @param state Current system state
     * @return Cause-effect information value
     */
    double calculateCauseEffectInformation(
        const std::vector<size_t>& mechanism,
        const std::shared_ptr<CausalState>& state);
    
    /**
     * @brief Calculate integrated information for mechanism
     * 
     * @param mechanism Mechanism elements indices
     * @param state Current system state
     * @return Integrated information value
     */
    double calculateMechanismIntegratedInformation(
        const std::vector<size_t>& mechanism,
        const std::shared_ptr<CausalState>& state);
    
    /**
     * @brief Set distance metric
     * 
     * @param metric Distance metric to use
     */
    void setDistanceMetric(DistanceMetric metric);
    
    /**
     * @brief Get distance metric
     * 
     * @return Current distance metric
     */
    DistanceMetric getDistanceMetric() const;
    
private:
    std::shared_ptr<PhiContext> context_;        ///< Calculation context
    std::shared_ptr<CausalGraph> graph_;         ///< Causal graph
    DistanceMetric distanceMetric_;              ///< Distance metric for repertoire comparison
    
    // Cache for repertoires to avoid redundant calculations
    using RepertoireKey = std::string;
    std::unordered_map<RepertoireKey, CausalRepertoire> repertoireCache_;
    
    /**
     * @brief Generate repertoire key
     * 
     * @param type Repertoire type
     * @param mechanism Mechanism elements indices
     * @param purview Purview elements indices
     * @param state Current system state
     * @return Cache key string
     */
    RepertoireKey generateRepertoireKey(
        RepertoireType type,
        const std::vector<size_t>& mechanism,
        const std::vector<size_t>& purview,
        const std::shared_ptr<CausalState>& state) const;
    
    /**
     * @brief Look up repertoire in cache
     * 
     * @param key Cache key
     * @return Optional repertoire
     */
    std::optional<CausalRepertoire> lookupRepertoire(const RepertoireKey& key) const;
    
    /**
     * @brief Store repertoire in cache
     * 
     * @param key Cache key
     * @param repertoire Repertoire to store
     */
    void storeRepertoire(const RepertoireKey& key, const CausalRepertoire& repertoire);
    
    /**
     * @brief Calculate transition probability
     * 
     * @param currentState Current state
     * @param nextState Next state
     * @param mechanismIndices Mechanism element indices
     * @return Transition probability
     */
    double calculateTransitionProbability(
        const Eigen::VectorXd& currentState,
        const Eigen::VectorXd& nextState,
        const std::vector<size_t>& mechanismIndices) const;
    
    /**
     * @brief Calculate perturbation distribution
     * 
     * @param purview Purview elements indices
     * @return Probability distribution over perturbations
     */
    ProbabilityDistribution calculatePerturbationDistribution(
        const std::vector<size_t>& purview) const;
    
    /**
     * @brief Generate all possible states
     * 
     * @param size State vector size
     * @return Matrix of all possible states (rows = states)
     */
    Eigen::MatrixXd generateAllPossibleStates(size_t size) const;
};

/**
 * @brief Conceptual structure from IIT
 * 
 * Implements the concept of a "concept" from IIT 3.0, which
 * represents the causal role of a mechanism
 */
class Concept {
public:
    /**
     * @brief Constructor
     * 
     * @param mechanism Mechanism elements indices
     * @param causeRepertoire Cause repertoire
     * @param effectRepertoire Effect repertoire
     * @param causeInformation Cause information value
     * @param effectInformation Effect information value
     * @param integratedInformation Integrated information value
     */
    Concept(
        const std::vector<size_t>& mechanism,
        const CausalRepertoire& causeRepertoire,
        const CausalRepertoire& effectRepertoire,
        double causeInformation,
        double effectInformation,
        double integratedInformation);
    
    /**
     * @brief Get mechanism elements
     * 
     * @return Vector of element indices
     */
    const std::vector<size_t>& getMechanism() const;
    
    /**
     * @brief Get cause repertoire
     * 
     * @return Cause repertoire
     */
    const CausalRepertoire& getCauseRepertoire() const;
    
    /**
     * @brief Get effect repertoire
     * 
     * @return Effect repertoire
     */
    const CausalRepertoire& getEffectRepertoire() const;
    
    /**
     * @brief Get cause information
     * 
     * @return Cause information value
     */
    double getCauseInformation() const;
    
    /**
     * @brief Get effect information
     * 
     * @return Effect information value
     */
    double getEffectInformation() const;
    
    /**
     * @brief Get integrated information
     * 
     * @return Integrated information value
     */
    double getIntegratedInformation() const;
    
    /**
     * @brief Calculate distance to another concept
     * 
     * @param other Other concept
     * @param metric Distance metric to use
     * @return Distance value
     */
    double distance(const Concept& other, DistanceMetric metric) const;
    
    /**
     * @brief Calculate Earth Mover's Distance to another concept
     * 
     * @param other Other concept
     * @return EMD value
     */
    double earthMoversDistance(const Concept& other) const;
    
    /**
     * @brief Convert to string representation
     * 
     * @return String representation
     */
    std::string toString() const;
    
private:
    std::vector<size_t> mechanism_;        ///< Mechanism elements indices
    CausalRepertoire causeRepertoire_;     ///< Cause repertoire
    CausalRepertoire effectRepertoire_;    ///< Effect repertoire
    double causeInformation_;              ///< Cause information value
    double effectInformation_;             ///< Effect information value
    double integratedInformation_;         ///< Integrated information value
};

/**
 * @brief Generator for concepts
 * 
 * Implements algorithms for generating concepts from mechanisms
 */
class ConceptGenerator {
public:
    /**
     * @brief Constructor
     * 
     * @param context Shared pointer to calculation context
     * @param repertoireGenerator Shared pointer to repertoire generator
     */
    ConceptGenerator(
        std::shared_ptr<PhiContext> context,
        std::shared_ptr<CausalRepertoireGenerator> repertoireGenerator);
    
    /**
     * @brief Generate concept for mechanism
     * 
     * @param mechanism Mechanism elements indices
     * @param state Current system state
     * @return Generated concept
     */
    Concept generateConcept(
        const std::vector<size_t>& mechanism,
        const std::shared_ptr<CausalState>& state);
    
    /**
     * @brief Generate all concepts for a system
     * 
     * @param system System elements indices
     * @param state Current system state
     * @return Vector of generated concepts
     */
    std::vector<Concept> generateAllConcepts(
        const std::vector<size_t>& system,
        const std::shared_ptr<CausalState>& state);
    
    /**
     * @brief Generate conceptual structure
     * 
     * @param system System elements indices
     * @param state Current system state
     * @return Vector of concepts forming the conceptual structure
     */
    std::vector<Concept> generateConceptualStructure(
        const std::vector<size_t>& system,
        const std::shared_ptr<CausalState>& state);
    
    /**
     * @brief Calculate conceptual information (Φ)
     * 
     * @param system System elements indices
     * @param state Current system state
     * @return Conceptual information value
     */
    double calculateConceptualInformation(
        const std::vector<size_t>& system,
        const std::shared_ptr<CausalState>& state);
    
    /**
     * @brief Set minimum information threshold
     * 
     * @param threshold Minimum information threshold
     */
    void setMinimumInformationThreshold(double threshold);
    
    /**
     * @brief Get minimum information threshold
     * 
     * @return Minimum information threshold
     */
    double getMinimumInformationThreshold() const;
    
private:
    std::shared_ptr<PhiContext> context_;                          ///< Calculation context
    std::shared_ptr<CausalRepertoireGenerator> repertoireGenerator_; ///< Repertoire generator
    double minimumInformationThreshold_ = 1e-6;                    ///< Minimum information threshold
    
    // Cache for concepts to avoid redundant calculations
    using ConceptKey = std::string;
    std::unordered_map<ConceptKey, Concept> conceptCache_;
    
    /**
     * @brief Generate concept key
     * 
     * @param mechanism Mechanism elements indices
     * @param state Current system state
     * @return Cache key string
     */
    ConceptKey generateConceptKey(
        const std::vector<size_t>& mechanism,
        const std::shared_ptr<CausalState>& state) const;
    
    /**
     * @brief Look up concept in cache
     * 
     * @param key Cache key
     * @return Optional concept
     */
    std::optional<Concept> lookupConcept(const ConceptKey& key) const;
    
    /**
     * @brief Store concept in cache
     * 
     * @param key Cache key
     * @param concept Concept to store
     */
    void storeConcept(const ConceptKey& key, const Concept& concept);
    
    /**
     * @brief Generate all possible mechanisms
     * 
     * @param system System elements indices
     * @return Vector of all possible mechanisms
     */
    std::vector<std::vector<size_t>> generateAllMechanisms(
        const std::vector<size_t>& system) const;
    
    /**
     * @brief Find maximum information purview for cause
     * 
     * @param mechanism Mechanism elements indices
     * @param state Current system state
     * @param allPurviews Vector of all possible purviews
     * @return Tuple of max purview, repertoire, and information
     */
    std::tuple<std::vector<size_t>, CausalRepertoire, double> findMaxCausePurview(
        const std::vector<size_t>& mechanism,
        const std::shared_ptr<CausalState>& state,
        const std::vector<std::vector<size_t>>& allPurviews);
    
    /**
     * @brief Find maximum information purview for effect
     * 
     * @param mechanism Mechanism elements indices
     * @param state Current system state
     * @param allPurviews Vector of all possible purviews
     * @return Tuple of max purview, repertoire, and information
     */
    std::tuple<std::vector<size_t>, CausalRepertoire, double> findMaxEffectPurview(
        const std::vector<size_t>& mechanism,
        const std::shared_ptr<CausalState>& state,
        const std::vector<std::vector<size_t>>& allPurviews);
};

} // namespace phi_calculator
} // namespace ultra

#endif // ULTRA_PHI_CALCULATOR_CAUSAL_REPERTOIRE_H_