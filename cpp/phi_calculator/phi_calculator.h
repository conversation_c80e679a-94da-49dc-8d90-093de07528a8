/**
 * @file phi_calculator.h
 * @brief Main header for Phi Calculator component based on Integrated Information Theory
 * 
 * This file provides the primary interface for the Phi Calculator component,
 * which implements Integrated Information Theory (IIT) to measure and analyze
 * consciousness and integration in complex systems. The component calculates
 * various metrics of integrated information (Φ) and related measures, identifies
 * complexes of high integration, and analyzes causal structures within the ULTRA system.
 * 
 * <AUTHOR> Team
 * @date 2025
 */

#ifndef ULTRA_PHI_CALCULATOR_PHI_CALCULATOR_H_
#define ULTRA_PHI_CALCULATOR_PHI_CALCULATOR_H_

#include "causal_analysis.h"
#include "phi_computation.h"
#include "information_geometry.h"
#include "partition_search.h"
#include "consciousness_measures.h"
#include "causal_repertoire.h"

#include <Eigen/Dense>
#include <memory>
#include <string>
#include <vector>
#include <unordered_map>
#include <functional>
#include <tuple>
#include <future>
#include <optional>

namespace ultra {
namespace phi_calculator {

// Forward declarations
class PhiCalculatorImpl;
class PhiContext;
class PhiResults;
class PhiComplexRegistry;

/**
 * @brief Enumeration of different Phi measures from IIT and related theories
 */
enum class PhiMeasure {
    PHI_MAX,              ///< Φ_max from IIT 3.0 (maximum Φ across temporal scales)
    PHI_E,                ///< Φ^E (effective information) from IIT 3.0
    PHI_AR,               ///< Φ^AR (atomic Φ using Φ^R for parts) from IIT 3.0
    PHI_STAR,             ///< Φ* (stochastic interaction) from information theory
    EXCESS_INFORMATION,   ///< ΦE (excess information) from information decomposition
    SYNERGY,              ///< S (synergistic information) from partial information decomposition
    STOCHASTIC_INTERACTION, ///< SI (stochastic interaction) alternate measure
    CAUSAL_DENSITY,       ///< CD (causal density) from Granger causality
    INTEGRATED_INFORMATION_DKL, ///< Φ_DKL (integrated information using KL divergence)
    PHI_G,                ///< Φ_G (geometric integrated information) based on Wasserstein distance
    PHI_WMS               ///< Φ_WMS (whole-minus-sum integrated information)
};

/**
 * @brief Enumeration of strategies for partition search
 */
enum class PartitionSearchStrategy {
    EXHAUSTIVE,          ///< Search all possible partitions (exponential complexity)
    ITERATIVE_GREEDY,    ///< Iterative greedy search (polynomial complexity)
    HEURISTIC,           ///< Heuristic search based on graph topology
    ATOMIC_BIPARTITION,  ///< Only consider atomic bipartitions (IIT 3.0)
    HIERARCHICAL,        ///< Hierarchical clustering-based search
    MIP_APPROXIMATION    ///< Approximation of MIP using spectral methods
};

/**
 * @brief Enumeration of distance metrics for comparing states/distributions
 */
enum class DistanceMetric {
    KL_DIVERGENCE,        ///< Kullback-Leibler divergence
    WASSERSTEIN,          ///< Wasserstein (Earth Mover's) distance
    JENSEN_SHANNON,       ///< Jensen-Shannon divergence
    HELLINGER,            ///< Hellinger distance
    EUCLIDEAN,            ///< Euclidean distance
    NORMALIZED_L1,        ///< Normalized L1 distance
    EARTH_MOVERS          ///< Earth Mover's Distance (EMD)
};

/**
 * @brief Configuration options for Phi calculation
 */
struct PhiCalculatorConfig {
    PhiMeasure measure = PhiMeasure::PHI_MAX;         ///< Which Phi measure to calculate
    PartitionSearchStrategy searchStrategy = 
        PartitionSearchStrategy::ITERATIVE_GREEDY;    ///< Partition search strategy
    DistanceMetric distanceMetric = 
        DistanceMetric::WASSERSTEIN;                  ///< Distance metric for state comparison
    
    double convergenceThreshold = 1e-6;               ///< Convergence threshold for iterative methods
    size_t maxIterations = 100;                       ///< Maximum iterations for iterative methods
    size_t numSamples = 1000;                         ///< Number of samples for stochastic methods
    double perturbationStrength = 0.1;                ///< Strength of perturbations in causal analysis
    bool useParallelComputation = true;               ///< Whether to use parallel computation
    bool cacheIntermediateResults = true;             ///< Whether to cache intermediate results
    size_t maxComplexSize = 0;                        ///< Maximum complex size (0 = no limit)
    double phiThreshold = 1e-3;                       ///< Minimum Phi value to consider significant
    
    bool enableTemporalIntegration = false;           ///< Enable integration across time
    size_t temporalWindowSize = 3;                    ///< Size of temporal window (if enabled)
    
    /**
     * @brief Create a config optimized for speed
     * @return PhiCalculatorConfig optimized for speed
     */
    static PhiCalculatorConfig forSpeed() {
        PhiCalculatorConfig config;
        config.measure = PhiMeasure::PHI_WMS;
        config.searchStrategy = PartitionSearchStrategy::HEURISTIC;
        config.distanceMetric = DistanceMetric::EUCLIDEAN;
        config.numSamples = 500;
        config.convergenceThreshold = 1e-4;
        return config;
    }
    
    /**
     * @brief Create a config optimized for accuracy
     * @return PhiCalculatorConfig optimized for accuracy
     */
    static PhiCalculatorConfig forAccuracy() {
        PhiCalculatorConfig config;
        config.measure = PhiMeasure::PHI_MAX;
        config.searchStrategy = PartitionSearchStrategy::EXHAUSTIVE;
        config.distanceMetric = DistanceMetric::WASSERSTEIN;
        config.numSamples = 5000;
        config.convergenceThreshold = 1e-8;
        return config;
    }
};

/**
 * @brief Results of Phi calculation for a system or subsystem
 */
class PhiResults {
public:
    /**
     * @brief Constructor
     * 
     * @param phi Phi value
     * @param elements Elements in the system
     * @param partition Minimum information partition
     */
    PhiResults(double phi, std::vector<size_t> elements, Partition partition);
    
    /**
     * @brief Get Phi value
     * 
     * @return Phi value
     */
    double getPhi() const;
    
    /**
     * @brief Get system elements
     * 
     * @return Vector of element indices
     */
    const std::vector<size_t>& getElements() const;
    
    /**
     * @brief Get minimum information partition
     * 
     * @return Minimum information partition
     */
    const Partition& getPartition() const;
    
    /**
     * @brief Get normalization factor
     * 
     * @return Normalization factor used
     */
    double getNormalizationFactor() const;
    
    /**
     * @brief Get effective information
     * 
     * @return Effective information value
     */
    double getEffectiveInformation() const;
    
    /**
     * @brief Get MIP effective information
     * 
     * @return MIP effective information value
     */
    double getMipEffectiveInformation() const;
    
    /**
     * @brief Get element contributions to Phi
     * 
     * @return Map of element indices to contribution values
     */
    const std::unordered_map<size_t, double>& getElementContributions() const;
    
    /**
     * @brief Set normalization factor
     * 
     * @param factor Normalization factor
     */
    void setNormalizationFactor(double factor);
    
    /**
     * @brief Set effective information
     * 
     * @param ei Effective information value
     */
    void setEffectiveInformation(double ei);
    
    /**
     * @brief Set MIP effective information
     * 
     * @param mipEi MIP effective information value
     */
    void setMipEffectiveInformation(double mipEi);
    
    /**
     * @brief Set element contributions
     * 
     * @param contributions Map of contributions
     */
    void setElementContributions(const std::unordered_map<size_t, double>& contributions);
    
    /**
     * @brief Check if results are valid
     * 
     * @return True if results are valid
     */
    bool isValid() const;
    
    /**
     * @brief Compare with another PhiResults
     * 
     * @param other Other PhiResults to compare with
     * @return True if this has higher Phi
     */
    bool operator>(const PhiResults& other) const;
    
    /**
     * @brief Convert to string representation
     * 
     * @return String representation of results
     */
    std::string toString() const;

private:
    double phi_;                                   ///< Phi value
    std::vector<size_t> elements_;                 ///< System elements
    Partition partition_;                          ///< Minimum information partition
    double normalizationFactor_ = 1.0;             ///< Normalization factor
    double effectiveInformation_ = 0.0;            ///< Effective information
    double mipEffectiveInformation_ = 0.0;         ///< MIP effective information
    std::unordered_map<size_t, double> elementContributions_; ///< Element contributions
};

/**
 * @brief Manager for Phi complexes identified in the system
 */
class PhiComplexRegistry {
public:
    /**
     * @brief Constructor
     */
    PhiComplexRegistry();
    
    /**
     * @brief Add a Phi complex
     * 
     * @param results PhiResults for the complex
     * @return ID of the complex
     */
    size_t addComplex(const PhiResults& results);
    
    /**
     * @brief Get a Phi complex by ID
     * 
     * @param id Complex ID
     * @return PhiResults for the complex
     */
    const PhiResults& getComplex(size_t id) const;
    
    /**
     * @brief Get all Phi complexes
     * 
     * @return Vector of all PhiResults
     */
    std::vector<PhiResults> getAllComplexes() const;
    
    /**
     * @brief Get top N Phi complexes by Phi value
     * 
     * @param n Number of complexes to return
     * @return Vector of top PhiResults
     */
    std::vector<PhiResults> getTopComplexes(size_t n) const;
    
    /**
     * @brief Check if an element is part of any complex
     * 
     * @param elementIndex Element index
     * @return True if element is in a complex
     */
    bool isElementInComplex(size_t elementIndex) const;
    
    /**
     * @brief Find all complexes containing an element
     * 
     * @param elementIndex Element index
     * @return Vector of complex IDs
     */
    std::vector<size_t> findComplexesWithElement(size_t elementIndex) const;
    
    /**
     * @brief Clear all complexes
     */
    void clear();
    
    /**
     * @brief Get number of complexes
     * 
     * @return Complex count
     */
    size_t size() const;
    
private:
    std::vector<PhiResults> complexes_;               ///< All Phi complexes
    std::unordered_map<size_t, std::vector<size_t>> elementToComplexMap_; ///< Map from elements to complexes
};

/**
 * @brief Execution context for Phi calculation
 * 
 * Maintains state and configuration for a single Phi calculation session
 */
class PhiContext {
public:
    /**
     * @brief Constructor
     * 
     * @param config Configuration options
     */
    explicit PhiContext(const PhiCalculatorConfig& config = PhiCalculatorConfig());
    
    /**
     * @brief Get configuration
     * 
     * @return Current configuration
     */
    const PhiCalculatorConfig& getConfig() const;
    
    /**
     * @brief Set causal graph
     * 
     * @param graph Shared pointer to causal graph
     */
    void setCausalGraph(std::shared_ptr<CausalGraph> graph);
    
    /**
     * @brief Get causal graph
     * 
     * @return Shared pointer to causal graph
     */
    std::shared_ptr<CausalGraph> getCausalGraph() const;
    
    /**
     * @brief Add a causal analyzer result
     * 
     * @param elements System elements
     * @param result Analyzer result
     */
    void addAnalyzerResult(const std::vector<size_t>& elements, 
                          std::shared_ptr<CausalAnalyzer> result);
    
    /**
     * @brief Get a causal analyzer result
     * 
     * @param elements System elements
     * @return Shared pointer to analyzer or nullptr if not found
     */
    std::shared_ptr<CausalAnalyzer> getAnalyzerResult(const std::vector<size_t>& elements) const;
    
    /**
     * @brief Add a Phi result
     * 
     * @param elements System elements
     * @param result Phi result
     */
    void addPhiResult(const std::vector<size_t>& elements, const PhiResults& result);
    
    /**
     * @brief Get a Phi result
     * 
     * @param elements System elements
     * @return Optional PhiResults (empty if not found)
     */
    std::optional<PhiResults> getPhiResult(const std::vector<size_t>& elements) const;
    
    /**
     * @brief Add candidate subsystem
     * 
     * @param elements Subsystem elements
     * @param priority Priority value (higher = higher priority)
     */
    void addCandidateSubsystem(const std::vector<size_t>& elements, double priority = 0.0);
    
    /**
     * @brief Get next candidate subsystem to process
     * 
     * @return Optional vector of element indices (empty if no more candidates)
     */
    std::optional<std::vector<size_t>> getNextCandidateSubsystem();
    
    /**
     * @brief Get complex registry
     * 
     * @return Reference to complex registry
     */
    PhiComplexRegistry& getComplexRegistry();
    
    /**
     * @brief Clear calculation cache
     */
    void clearCache();
    
    /**
     * @brief Check if execution is canceled
     * 
     * @return True if execution is canceled
     */
    bool isCanceled() const;
    
    /**
     * @brief Request cancellation of execution
     */
    void cancel();
    
    /**
     * @brief Set progress callback
     * 
     * @param callback Function to call with progress updates
     */
    void setProgressCallback(std::function<void(double)> callback);
    
    /**
     * @brief Update progress
     * 
     * @param progress Progress value (0.0-1.0)
     */
    void updateProgress(double progress);

private:
    PhiCalculatorConfig config_;                              ///< Configuration options
    std::shared_ptr<CausalGraph> graph_;                      ///< Causal graph
    
    // Result caches
    std::unordered_map<std::string, std::shared_ptr<CausalAnalyzer>> analyzerCache_; ///< Cache of analyzers
    std::unordered_map<std::string, PhiResults> phiResultsCache_;                    ///< Cache of Phi results
    
    // Candidate subsystems prioritized by potential Phi value
    struct SubsystemCandidate {
        std::vector<size_t> elements;
        double priority;
        
        bool operator<(const SubsystemCandidate& other) const {
            return priority < other.priority;
        }
    };
    std::vector<SubsystemCandidate> candidates_;              ///< Candidate subsystems
    
    PhiComplexRegistry complexRegistry_;                      ///< Registry of identified complexes
    bool canceled_ = false;                                   ///< Whether execution is canceled
    std::function<void(double)> progressCallback_;            ///< Progress callback function
    
    /**
     * @brief Generate key for element vector
     * 
     * @param elements Vector of element indices
     * @return String key
     */
    std::string generateKey(const std::vector<size_t>& elements) const;
};

/**
 * @brief Main interface for Phi calculation
 * 
 * This class provides methods for calculating Phi (Φ) and related measures
 * for systems and subsystems, identifying Phi complexes, and analyzing
 * causal structures.
 */
class PhiCalculator {
public:
    /**
     * @brief Constructor
     * 
     * @param config Configuration options
     */
    explicit PhiCalculator(const PhiCalculatorConfig& config = PhiCalculatorConfig());
    
    /**
     * @brief Destructor
     */
    ~PhiCalculator();
    
    /**
     * @brief Set configuration
     * 
     * @param config New configuration options
     */
    void setConfig(const PhiCalculatorConfig& config);
    
    /**
     * @brief Get current configuration
     * 
     * @return Current configuration
     */
    const PhiCalculatorConfig& getConfig() const;
    
    /**
     * @brief Initialize with a causal graph
     * 
     * @param graph Shared pointer to causal graph
     */
    void initialize(std::shared_ptr<CausalGraph> graph);
    
    /**
     * @brief Initialize with a connectivity matrix
     * 
     * @param connectivityMatrix Matrix of connection strengths
     */
    void initialize(const Eigen::MatrixXd& connectivityMatrix);
    
    /**
     * @brief Set node states
     * 
     * @param nodeStates Vector of node states
     */
    void setNodeStates(const std::vector<std::shared_ptr<CausalState>>& nodeStates);
    
    /**
     * @brief Set node state
     * 
     * @param nodeIndex Node index
     * @param state Node state
     */
    void setNodeState(size_t nodeIndex, std::shared_ptr<CausalState> state);
    
    /**
     * @brief Get causal graph
     * 
     * @return Shared pointer to causal graph
     */
    std::shared_ptr<CausalGraph> getCausalGraph() const;
    
    /**
     * @brief Calculate Phi for the whole system
     * 
     * @return Phi value
     */
    double calculatePhi();
    
    /**
     * @brief Calculate Phi for a subsystem
     * 
     * @param subsystem Vector of element indices
     * @return Phi value
     */
    double calculateSubsystemPhi(const std::vector<size_t>& subsystem);
    
    /**
     * @brief Calculate detailed Phi results for a subsystem
     * 
     * @param subsystem Vector of element indices
     * @return PhiResults object
     */
    PhiResults calculatePhiResults(const std::vector<size_t>& subsystem);
    
    /**
     * @brief Calculate Phi spectrum across subsystems
     * 
     * @return Vector of Phi values for different scales
     */
    std::vector<double> calculatePhiSpectrum();
    
    /**
     * @brief Identify all Phi complexes in the system
     * 
     * @return Vector of PhiResults for complexes
     */
    std::vector<PhiResults> identifyComplexes();
    
    /**
     * @brief Calculate Phi value using a specific measure
     * 
     * @param subsystem Vector of element indices
     * @param measure Phi measure to use
     * @return Phi value
     */
    double calculatePhiWithMeasure(const std::vector<size_t>& subsystem, 
                                  PhiMeasure measure);
    
    /**
     * @brief Calculate causal flow between elements
     * 
     * @return Matrix of causal flow values
     */
    Eigen::MatrixXd calculateCausalFlow();
    
    /**
     * @brief Calculate causal emergence
     * 
     * @return Causal emergence value
     */
    double calculateCausalEmergence();
    
    /**
     * @brief Calculate element contributions to Phi
     * 
     * @return Map of element indices to contribution values
     */
    std::unordered_map<size_t, double> calculateElementContributions();
    
    /**
     * @brief Generate causal analysis report
     * 
     * @param format Report format ("text", "json", "xml")
     * @return Report string
     */
    std::string generateReport(const std::string& format = "text");
    
    /**
     * @brief Cancel any ongoing calculation
     */
    void cancelCalculation();
    
    /**
     * @brief Set progress callback
     * 
     * @param callback Function to call with progress updates
     */
    void setProgressCallback(std::function<void(double)> callback);
    
    /**
     * @brief Get complex registry
     * 
     * @return Reference to complex registry
     */
    const PhiComplexRegistry& getComplexRegistry() const;
    
    /**
     * @brief Clear calculation cache
     */
    void clearCache();
    
    /**
     * @brief Get library version
     * 
     * @return Version string
     */
    static std::string getVersion();
    
private:
    std::unique_ptr<PhiCalculatorImpl> impl_;  ///< Implementation pointer (PIMPL pattern)
    std::shared_ptr<PhiContext> context_;      ///< Calculation context
    
    /**
     * @brief Run calculation with given measure
     * 
     * @param subsystem Vector of element indices
     * @param measure Phi measure to use
     * @return PhiResults object
     */
    PhiResults runCalculation(const std::vector<size_t>& subsystem, PhiMeasure measure);
};

/**
 * @brief Factory for creating Phi calculators with specific configurations
 */
class PhiCalculatorFactory {
public:
    /**
     * @brief Create a standard Phi calculator
     * 
     * @return PhiCalculator instance
     */
    static std::unique_ptr<PhiCalculator> createStandard();
    
    /**
     * @brief Create a high-performance Phi calculator
     * 
     * @return PhiCalculator instance
     */
    static std::unique_ptr<PhiCalculator> createHighPerformance();
    
    /**
     * @brief Create a high-precision Phi calculator
     * 
     * @return PhiCalculator instance
     */
    static std::unique_ptr<PhiCalculator> createHighPrecision();
    
    /**
     * @brief Create a custom Phi calculator
     * 
     * @param config Custom configuration
     * @return PhiCalculator instance
     */
    static std::unique_ptr<PhiCalculator> createCustom(const PhiCalculatorConfig& config);
};

} // namespace phi_calculator
} // namespace ultra

#endif // ULTRA_PHI_CALCULATOR_PHI_CALCULATOR_H_