/**
 * @file causal_repertoire.cpp
 * @brief Implementation of cause and effect repertoires from IIT
 * 
 * This file implements the cause and effect repertoires as defined in
 * Integrated Information Theory (IIT). These repertoires represent the
 * causal constraints that a system or its parts impose on its own
 * past and future states.
 * 
 * <AUTHOR> Team
 * @date 2025
 */

#include "causal_repertoire.h"
#include "phi_calculator.h"
#include "../utils/matrix_operations.h"
#include "../utils/information_theory.h"
#include "../utils/parallel_processing.h"

#include <algorithm>
#include <cmath>
#include <iomanip>
#include <limits>
#include <numeric>
#include <random>
#include <sstream>
#include <thread>

#include <Eigen/Dense>
#include <boost/functional/hash.hpp>

namespace ultra {
namespace phi_calculator {

using namespace Eigen;
using namespace std;
using namespace ultra::utils;

//=============================================================================
// CausalRepertoire Implementation
//=============================================================================

CausalRepertoire::CausalRepertoire(const ProbabilityDistribution& distribution, 
                                 RepertoireType type)
    : distribution_(distribution),
      type_(type) {
}

RepertoireType CausalRepertoire::getType() const {
    return type_;
}

const ProbabilityDistribution& CausalRepertoire::getDistribution() const {
    return distribution_;
}

double CausalRepertoire::entropy() const {
    return distribution_.entropy();
}

double CausalRepertoire::distance(const CausalRepertoire& other, DistanceMetric metric) const {
    return distribution_.distance(other.distribution_, metric);
}

double CausalRepertoire::earthMoversDistance(const CausalRepertoire& other) const {
    return distribution_.wassersteinDistance(other.distribution_, 1);
}

double CausalRepertoire::klDivergence(const CausalRepertoire& other) const {
    return distribution_.klDivergence(other.distribution_);
}

CausalRepertoire CausalRepertoire::marginalize(const std::vector<size_t>& indices) const {
    ProbabilityDistribution marginal = distribution_.marginalize(indices);
    return CausalRepertoire(marginal, type_);
}

CausalRepertoire CausalRepertoire::condition(const std::vector<size_t>& indices, 
                                          const VectorXd& values) const {
    // Implementation for conditioning a distribution on specific values
    // This is a simplification and would need to be expanded for a full implementation
    
    const VectorXd& distValues = distribution_.getValues();
    const VectorXd& distProbs = distribution_.getProbabilities();
    
    // Create masks for the conditioning
    vector<bool> matches(distValues.size(), true);
    for (size_t i = 0; i < indices.size(); ++i) {
        size_t idx = indices[i];
        double value = values(i);
        
        for (int j = 0; j < distValues.size(); ++j) {
            if (abs(distValues(j * distValues.size() + idx) - value) > 1e-6) {
                matches[j] = false;
            }
        }
    }
    
    // Count matching entries and create new distributions
    int matchCount = count(matches.begin(), matches.end(), true);
    
    if (matchCount == 0) {
        // No matches, return uniform distribution
        return CausalRepertoire(
            ProbabilityDistribution(distribution_.getSize()),
            type_
        );
    }
    
    VectorXd newValues(matchCount);
    VectorXd newProbs(matchCount);
    
    double totalProb = 0.0;
    int idx = 0;
    for (size_t i = 0; i < matches.size(); ++i) {
        if (matches[i]) {
            newValues(idx) = distValues(i);
            newProbs(idx) = distProbs(i);
            totalProb += distProbs(i);
            idx++;
        }
    }
    
    // Normalize probabilities
    if (totalProb > 0) {
        newProbs /= totalProb;
    } else {
        // If no probability mass, use uniform distribution
        newProbs = VectorXd::Constant(matchCount, 1.0 / matchCount);
    }
    
    return CausalRepertoire(
        ProbabilityDistribution(newValues, newProbs),
        type_
    );
}

CausalRepertoire CausalRepertoire::product(const CausalRepertoire& other) const {
    if (type_ != other.type_) {
        throw runtime_error("Cannot compute product of different repertoire types");
    }
    
    ProbabilityDistribution productDist = distribution_.product(other.distribution_);
    return CausalRepertoire(productDist, type_);
}

//=============================================================================
// CausalRepertoireGenerator Implementation
//=============================================================================

CausalRepertoireGenerator::CausalRepertoireGenerator(std::shared_ptr<PhiContext> context)
    : context_(context),
      graph_(context->getCausalGraph()),
      distanceMetric_(DistanceMetric::WASSERSTEIN) {
    
    if (!graph_) {
        throw runtime_error("No causal graph available in context");
    }
}

CausalRepertoire CausalRepertoireGenerator::generateCauseRepertoire(
    const std::vector<size_t>& mechanism,
    const std::vector<size_t>& purview,
    const std::shared_ptr<CausalState>& state) {
    
    // Check for cached result
    RepertoireKey key = generateRepertoireKey(
        RepertoireType::CAUSE, mechanism, purview, state);
    
    auto cachedResult = lookupRepertoire(key);
    if (cachedResult) {
        return *cachedResult;
    }
    
    // Calculate the cause repertoire using Bayes' rule
    // P(purview_past | mechanism_current) = P(mechanism_current | purview_past) * P(purview_past) / P(mechanism_current)
    
    // Get current state values
    const VectorXd& currentState = state->getValues();
    
    // Generate all possible past states for the purview
    MatrixXd possiblePastStates = generateAllPossibleStates(purview.size());
    
    // Calculate probabilities for each possible past state
    VectorXd pastProbs(possiblePastStates.rows());
    
    // Get prior probabilities of the purview states (uniform if no information)
    VectorXd priorProbs = VectorXd::Constant(possiblePastStates.rows(), 1.0 / possiblePastStates.rows());
    
    // Calculate likelihoods and posterior probabilities
    for (int i = 0; i < possiblePastStates.rows(); ++i) {
        // Create full past state from the purview state
        VectorXd pastState = VectorXd::Zero(graph_->getNumNodes());
        for (size_t j = 0; j < purview.size(); ++j) {
            pastState(purview[j]) = possiblePastStates(i, j);
        }
        
        // Calculate likelihood P(mechanism_current | purview_past)
        double likelihood = calculateTransitionProbability(pastState, currentState, mechanism);
        
        // Update probability using Bayes' rule
        pastProbs(i) = likelihood * priorProbs(i);
    }
    
    // Normalize probabilities
    double sum = pastProbs.sum();
    if (sum > 1e-10) {
        pastProbs /= sum;
    } else {
        pastProbs = VectorXd::Constant(pastProbs.size(), 1.0 / pastProbs.size());
    }
    
    // Create cause repertoire
    CausalRepertoire repertoire(
        ProbabilityDistribution(possiblePastStates, pastProbs),
        RepertoireType::CAUSE
    );
    
    // Cache the result
    storeRepertoire(key, repertoire);
    
    return repertoire;
}

CausalRepertoire CausalRepertoireGenerator::generateEffectRepertoire(
    const std::vector<size_t>& mechanism,
    const std::vector<size_t>& purview,
    const std::shared_ptr<CausalState>& state) {
    
    // Check for cached result
    RepertoireKey key = generateRepertoireKey(
        RepertoireType::EFFECT, mechanism, purview, state);
    
    auto cachedResult = lookupRepertoire(key);
    if (cachedResult) {
        return *cachedResult;
    }
    
    // Calculate the effect repertoire
    // P(purview_future | mechanism_current) = direct calculation of transition probabilities
    
    // Get current state values
    const VectorXd& currentState = state->getValues();
    
    // Generate all possible future states for the purview
    MatrixXd possibleFutureStates = generateAllPossibleStates(purview.size());
    
    // Calculate probabilities for each possible future state
    VectorXd futureProbs(possibleFutureStates.rows());
    
    for (int i = 0; i < possibleFutureStates.rows(); ++i) {
        // Create a full future state from the purview state
        VectorXd futureState = VectorXd::Zero(graph_->getNumNodes());
        for (size_t j = 0; j < purview.size(); ++j) {
            futureState(purview[j]) = possibleFutureStates(i, j);
        }
        
        // Calculate transition probability from current state to this future state
        futureProbs(i) = calculateTransitionProbability(currentState, futureState, mechanism);
    }
    
    // Normalize probabilities
    double sum = futureProbs.sum();
    if (sum > 1e-10) {
        futureProbs /= sum;
    } else {
        futureProbs = VectorXd::Constant(futureProbs.size(), 1.0 / futureProbs.size());
    }
    
    // Create effect repertoire
    CausalRepertoire repertoire(
        ProbabilityDistribution(possibleFutureStates, futureProbs),
        RepertoireType::EFFECT
    );
    
    // Cache the result
    storeRepertoire(key, repertoire);
    
    return repertoire;
}

CausalRepertoire CausalRepertoireGenerator::generateUnconstrainedCauseRepertoire(
    const std::vector<size_t>& purview) {
    
    // Generate a uniform distribution over all possible past states
    MatrixXd possibleStates = generateAllPossibleStates(purview.size());
    VectorXd probs = VectorXd::Constant(possibleStates.rows(), 1.0 / possibleStates.rows());
    
    return CausalRepertoire(
        ProbabilityDistribution(possibleStates, probs),
        RepertoireType::CAUSE
    );
}

CausalRepertoire CausalRepertoireGenerator::generateUnconstrainedEffectRepertoire(
    const std::vector<size_t>& purview) {
    
    // Generate a uniform distribution over all possible future states
    MatrixXd possibleStates = generateAllPossibleStates(purview.size());
    VectorXd probs = VectorXd::Constant(possibleStates.rows(), 1.0 / possibleStates.rows());
    
    return CausalRepertoire(
        ProbabilityDistribution(possibleStates, probs),
        RepertoireType::EFFECT
    );
}

double CausalRepertoireGenerator::calculateCauseInformation(
    const std::vector<size_t>& mechanism,
    const std::vector<size_t>& purview,
    const std::shared_ptr<CausalState>& state) {
    
    // Calculate cause information as the distance between the cause repertoire
    // and the unconstrained cause repertoire
    
    CausalRepertoire causeRepertoire = generateCauseRepertoire(mechanism, purview, state);
    CausalRepertoire unconstrainedRepertoire = generateUnconstrainedCauseRepertoire(purview);
    
    return causeRepertoire.distance(unconstrainedRepertoire, distanceMetric_);
}

double CausalRepertoireGenerator::calculateEffectInformation(
    const std::vector<size_t>& mechanism,
    const std::vector<size_t>& purview,
    const std::shared_ptr<CausalState>& state) {
    
    // Calculate effect information as the distance between the effect repertoire
    // and the unconstrained effect repertoire
    
    CausalRepertoire effectRepertoire = generateEffectRepertoire(mechanism, purview, state);
    CausalRepertoire unconstrainedRepertoire = generateUnconstrainedEffectRepertoire(purview);
    
    return effectRepertoire.distance(unconstrainedRepertoire, distanceMetric_);
}

double CausalRepertoireGenerator::calculateIntegratedCauseInformation(
    const std::vector<size_t>& mechanism,
    const std::vector<size_t>& purview,
    const std::shared_ptr<CausalState>& state) {
    
    // If mechanism is empty or has only one element, there is no integration
    if (mechanism.size() <= 1) {
        return 0.0;
    }
    
    // Find the minimum information partition
    Partition mip = findCauseMIP(mechanism, purview, state);
    
    // Calculate the whole mechanism's cause information
    double wholeInfo = calculateCauseInformation(mechanism, purview, state);
    
    // Calculate the cause information for each part of the MIP
    double partInfo = 0.0;
    
    for (const auto& part : mip) {
        if (!part.empty()) {
            partInfo += calculateCauseInformation(part, purview, state);
        }
    }
    
    // Integrated information is the difference
    return wholeInfo - partInfo;
}

double CausalRepertoireGenerator::calculateIntegratedEffectInformation(
    const std::vector<size_t>& mechanism,
    const std::vector<size_t>& purview,
    const std::shared_ptr<CausalState>& state) {
    
    // If mechanism is empty or has only one element, there is no integration
    if (mechanism.size() <= 1) {
        return 0.0;
    }
    
    // Find the minimum information partition
    Partition mip = findEffectMIP(mechanism, purview, state);
    
    // Calculate the whole mechanism's effect information
    double wholeInfo = calculateEffectInformation(mechanism, purview, state);
    
    // Calculate the effect information for each part of the MIP
    double partInfo = 0.0;
    
    for (const auto& part : mip) {
        if (!part.empty()) {
            partInfo += calculateEffectInformation(part, purview, state);
        }
    }
    
    // Integrated information is the difference
    return wholeInfo - partInfo;
}

Partition CausalRepertoireGenerator::findCauseMIP(
    const std::vector<size_t>& mechanism,
    const std::vector<size_t>& purview,
    const std::shared_ptr<CausalState>& state) {
    
    // For simplicity, we'll consider all possible bipartitions of the mechanism
    // In a full implementation, more efficient methods would be used
    
    double minPhiValue = numeric_limits<double>::max();
    Partition minPartition;
    
    // Generate all possible non-trivial bipartitions
    vector<bool> partition(mechanism.size(), false);
    
    // Start with some elements in the first part
    size_t midpoint = mechanism.size() / 2;
    fill(partition.begin(), partition.begin() + midpoint, true);
    
    do {
        // Create the two parts of the bipartition
        vector<size_t> part1, part2;
        for (size_t i = 0; i < mechanism.size(); ++i) {
            if (partition[i]) {
                part1.push_back(mechanism[i]);
            } else {
                part2.push_back(mechanism[i]);
            }
        }
        
        // Skip trivial partitions
        if (part1.empty() || part2.empty()) {
            continue;
        }
        
        // Calculate cause information for the whole and parts
        double wholeInfo = calculateCauseInformation(mechanism, purview, state);
        double part1Info = calculateCauseInformation(part1, purview, state);
        double part2Info = calculateCauseInformation(part2, purview, state);
        
        // Calculate phi value for this partition
        double phiValue = wholeInfo - (part1Info + part2Info);
        
        // Update minimum if this is lower
        if (phiValue < minPhiValue) {
            minPhiValue = phiValue;
            minPartition = {part1, part2};
        }
        
    } while (next_permutation(partition.begin(), partition.end()));
    
    return minPartition;
}

Partition CausalRepertoireGenerator::findEffectMIP(
    const std::vector<size_t>& mechanism,
    const std::vector<size_t>& purview,
    const std::shared_ptr<CausalState>& state) {
    
    // Similar approach as findCauseMIP but for effect information
    
    double minPhiValue = numeric_limits<double>::max();
    Partition minPartition;
    
    // Generate all possible non-trivial bipartitions
    vector<bool> partition(mechanism.size(), false);
    
    // Start with some elements in the first part
    size_t midpoint = mechanism.size() / 2;
    fill(partition.begin(), partition.begin() + midpoint, true);
    
    do {
        // Create the two parts of the bipartition
        vector<size_t> part1, part2;
        for (size_t i = 0; i < mechanism.size(); ++i) {
            if (partition[i]) {
                part1.push_back(mechanism[i]);
            } else {
                part2.push_back(mechanism[i]);
            }
        }
        
        // Skip trivial partitions
        if (part1.empty() || part2.empty()) {
            continue;
        }
        
        // Calculate effect information for the whole and parts
        double wholeInfo = calculateEffectInformation(mechanism, purview, state);
        double part1Info = calculateEffectInformation(part1, purview, state);
        double part2Info = calculateEffectInformation(part2, purview, state);
        
        // Calculate phi value for this partition
        double phiValue = wholeInfo - (part1Info + part2Info);
        
        // Update minimum if this is lower
        if (phiValue < minPhiValue) {
            minPhiValue = phiValue;
            minPartition = {part1, part2};
        }
        
    } while (next_permutation(partition.begin(), partition.end()));
    
    return minPartition;
}

double CausalRepertoireGenerator::calculateCauseEffectInformation(
    const std::vector<size_t>& mechanism,
    const std::shared_ptr<CausalState>& state) {
    
    // Find the purview that maximizes cause information
    double maxCauseInfo = 0.0;
    vector<size_t> maxCausePurview;
    
    // Try all possible subsets of the system as purviews
    size_t numNodes = graph_->getNumNodes();
    vector<bool> selection(numNodes, false);
    
    // Start with smallest purviews
    for (size_t purviewSize = 1; purviewSize <= numNodes; ++purviewSize) {
        // Reset selection and set first purviewSize elements to true
        fill(selection.begin(), selection.end(), false);
        fill(selection.begin(), selection.begin() + purviewSize, true);
        
        do {
            // Create purview from selection
            vector<size_t> purview;
            for (size_t i = 0; i < numNodes; ++i) {
                if (selection[i]) {
                    purview.push_back(i);
                }
            }
            
            // Calculate cause information
            double causeInfo = calculateCauseInformation(mechanism, purview, state);
            
            // Update maximum if higher
            if (causeInfo > maxCauseInfo) {
                maxCauseInfo = causeInfo;
                maxCausePurview = purview;
            }
            
        } while (next_permutation(selection.begin(), selection.end()));
        
        // If we found a non-zero maximum, stop searching
        if (maxCauseInfo > 1e-6) {
            break;
        }
    }
    
    // Find the purview that maximizes effect information
    double maxEffectInfo = 0.0;
    vector<size_t> maxEffectPurview;
    
    // Reset for effect calculation
    fill(selection.begin(), selection.end(), false);
    
    // Try all possible subsets of the system as purviews
    for (size_t purviewSize = 1; purviewSize <= numNodes; ++purviewSize) {
        // Reset selection and set first purviewSize elements to true
        fill(selection.begin(), selection.end(), false);
        fill(selection.begin(), selection.begin() + purviewSize, true);
        
        do {
            // Create purview from selection
            vector<size_t> purview;
            for (size_t i = 0; i < numNodes; ++i) {
                if (selection[i]) {
                    purview.push_back(i);
                }
            }
            
            // Calculate effect information
            double effectInfo = calculateEffectInformation(mechanism, purview, state);
            
            // Update maximum if higher
            if (effectInfo > maxEffectInfo) {
                maxEffectInfo = effectInfo;
                maxEffectPurview = purview;
            }
            
        } while (next_permutation(selection.begin(), selection.end()));
        
        // If we found a non-zero maximum, stop searching
        if (maxEffectInfo > 1e-6) {
            break;
        }
    }
    
    // Return minimum of maximized cause and effect information
    return min(maxCauseInfo, maxEffectInfo);
}

double CausalRepertoireGenerator::calculateMechanismIntegratedInformation(
    const std::vector<size_t>& mechanism,
    const std::shared_ptr<CausalState>& state) {
    
    // Find the purview that maximizes integrated cause information
    double maxCausePhi = 0.0;
    vector<size_t> maxCausePurview;
    
    // Try all possible subsets of the system as purviews
    size_t numNodes = graph_->getNumNodes();
    vector<bool> selection(numNodes, false);
    
    // Start with smallest purviews
    for (size_t purviewSize = 1; purviewSize <= numNodes; ++purviewSize) {
        // Reset selection and set first purviewSize elements to true
        fill(selection.begin(), selection.end(), false);
        fill(selection.begin(), selection.begin() + purviewSize, true);
        
        do {
            // Create purview from selection
            vector<size_t> purview;
            for (size_t i = 0; i < numNodes; ++i) {
                if (selection[i]) {
                    purview.push_back(i);
                }
            }
            
            // Calculate integrated cause information
            double causePhi = calculateIntegratedCauseInformation(mechanism, purview, state);
            
            // Update maximum if higher
            if (causePhi > maxCausePhi) {
                maxCausePhi = causePhi;
                maxCausePurview = purview;
            }
            
        } while (next_permutation(selection.begin(), selection.end()));
        
        // If we found a non-zero maximum, stop searching
        if (maxCausePhi > 1e-6) {
            break;
        }
    }
    
    // Find the purview that maximizes integrated effect information
    double maxEffectPhi = 0.0;
    vector<size_t> maxEffectPurview;
    
    // Reset for effect calculation
    fill(selection.begin(), selection.end(), false);
    
    // Try all possible subsets of the system as purviews
    for (size_t purviewSize = 1; purviewSize <= numNodes; ++purviewSize) {
        // Reset selection and set first purviewSize elements to true
        fill(selection.begin(), selection.end(), false);
        fill(selection.begin(), selection.begin() + purviewSize, true);
        
        do {
            // Create purview from selection
            vector<size_t> purview;
            for (size_t i = 0; i < numNodes; ++i) {
                if (selection[i]) {
                    purview.push_back(i);
                }
            }
            
            // Calculate integrated effect information
            double effectPhi = calculateIntegratedEffectInformation(mechanism, purview, state);
            
            // Update maximum if higher
            if (effectPhi > maxEffectPhi) {
                maxEffectPhi = effectPhi;
                maxEffectPurview = purview;
            }
            
        } while (next_permutation(selection.begin(), selection.end()));
        
        // If we found a non-zero maximum, stop searching
        if (maxEffectPhi > 1e-6) {
            break;
        }
    }
    
    // Return minimum of maximized integrated cause and effect information
    return min(maxCausePhi, maxEffectPhi);
}

void CausalRepertoireGenerator::setDistanceMetric(DistanceMetric metric) {
    distanceMetric_ = metric;
}

DistanceMetric CausalRepertoireGenerator::getDistanceMetric() const {
    return distanceMetric_;
}

CausalRepertoireGenerator::RepertoireKey CausalRepertoireGenerator::generateRepertoireKey(
    RepertoireType type,
    const std::vector<size_t>& mechanism,
    const std::vector<size_t>& purview,
    const std::shared_ptr<CausalState>& state) const {
    
    // Create a string key based on the inputs
    stringstream ss;
    ss << static_cast<int>(type) << "_";
    
    // Add mechanism elements
    ss << "M[";
    for (size_t elem : mechanism) {
        ss << elem << ",";
    }
    ss << "]_";
    
    // Add purview elements
    ss << "P[";
    for (size_t elem : purview) {
        ss << elem << ",";
    }
    ss << "]_";
    
    // Add state hash
    size_t stateHash = 0;
    const VectorXd& stateValues = state->getValues();
    for (int i = 0; i < stateValues.size(); ++i) {
        boost::hash_combine(stateHash, hash<double>{}(stateValues(i)));
    }
    ss << "S" << stateHash;
    
    return ss.str();
}

std::optional<CausalRepertoire> CausalRepertoireGenerator::lookupRepertoire(
    const RepertoireKey& key) const {
    
    auto it = repertoireCache_.find(key);
    if (it != repertoireCache_.end()) {
        return it->second;
    }
    return std::nullopt;
}

void CausalRepertoireGenerator::storeRepertoire(
    const RepertoireKey& key,
    const CausalRepertoire& repertoire) {
    
    // Only store if caching is enabled in the context
    if (context_->getConfig().cacheIntermediateResults) {
        repertoireCache_[key] = repertoire;
    }
}

double CausalRepertoireGenerator::calculateTransitionProbability(
    const VectorXd& currentState,
    const VectorXd& nextState,
    const std::vector<size_t>& mechanismIndices) const {
    
    // In a real implementation, this would use the actual transition probabilities
    // from the causal graph. For simplicity, we'll use a deterministic model
    // based on the adjacency matrix.
    
    const MatrixXd& adjacencyMatrix = graph_->getAdjacencyMatrix();
    
    // Calculate expected next state from current state
    VectorXd expectedNextState = adjacencyMatrix * currentState;
    
    // If the mechanism is specified, only calculate for those elements
    if (!mechanismIndices.empty()) {
        // Calculate probability only for the mechanism's contribution
        double probability = 1.0;
        
        for (size_t idx : mechanismIndices) {
            // For each mechanism element, calculate its influence on the next state
            double expected = 0.0;
            for (int i = 0; i < currentState.size(); ++i) {
                expected += adjacencyMatrix(idx, i) * currentState(i);
            }
            
            // Compare expected with actual next state
            double actual = nextState(idx);
            double diff = abs(actual - expected);
            
            // Calculate probability based on closeness (Gaussian-like)
            double sigma = 0.1; // Controls the "sharpness" of the probability
            probability *= exp(-diff*diff / (2*sigma*sigma));
        }
        
        return probability;
    } else {
        // Calculate overall transition probability
        double diff = (nextState - expectedNextState).norm();
        double sigma = 0.1 * sqrt(nextState.size()); // Scale with system size
        return exp(-diff*diff / (2*sigma*sigma));
    }
}

ProbabilityDistribution CausalRepertoireGenerator::calculatePerturbationDistribution(
    const std::vector<size_t>& purview) const {
    
    // Generate a uniform distribution over all possible states of the purview
    MatrixXd possibleStates = generateAllPossibleStates(purview.size());
    VectorXd probs = VectorXd::Constant(possibleStates.rows(), 1.0 / possibleStates.rows());
    
    return ProbabilityDistribution(possibleStates, probs);
}

MatrixXd CausalRepertoireGenerator::generateAllPossibleStates(size_t size) const {
    // Generate all possible binary states for a system of given size
    // Each row represents a different state
    
    size_t numStates = 1 << size; // 2^size states
    MatrixXd states(numStates, size);
    
    for (size_t i = 0; i < numStates; ++i) {
        for (size_t j = 0; j < size; ++j) {
            // Extract j-th bit of i
            states(i, j) = (i >> j) & 1;
        }
    }
    
    return states;
}

//=============================================================================
// Concept Implementation
//=============================================================================

Concept::Concept(
    const std::vector<size_t>& mechanism,
    const CausalRepertoire& causeRepertoire,
    const CausalRepertoire& effectRepertoire,
    double causeInformation,
    double effectInformation,
    double integratedInformation)
    : mechanism_(mechanism),
      causeRepertoire_(causeRepertoire),
      effectRepertoire_(effectRepertoire),
      causeInformation_(causeInformation),
      effectInformation_(effectInformation),
      integratedInformation_(integratedInformation) {
}

const std::vector<size_t>& Concept::getMechanism() const {
    return mechanism_;
}

const CausalRepertoire& Concept::getCauseRepertoire() const {
    return causeRepertoire_;
}

const CausalRepertoire& Concept::getEffectRepertoire() const {
    return effectRepertoire_;
}

double Concept::getCauseInformation() const {
    return causeInformation_;
}

double Concept::getEffectInformation() const {
    return effectInformation_;
}

double Concept::getIntegratedInformation() const {
    return integratedInformation_;
}

double Concept::distance(const Concept& other, DistanceMetric metric) const {
    // Calculate distance between concepts as a combination of
    // the distances between their cause and effect repertoires
    
    double causeDistance = causeRepertoire_.distance(other.causeRepertoire_, metric);
    double effectDistance = effectRepertoire_.distance(other.effectRepertoire_, metric);
    
    // Return the maximum distance (extended EMD)
    return max(causeDistance, effectDistance);
}

double Concept::earthMoversDistance(const Concept& other) const {
    // Calculate EMD between concepts
    double causeEMD = causeRepertoire_.earthMoversDistance(other.causeRepertoire_);
    double effectEMD = effectRepertoire_.earthMoversDistance(other.effectRepertoire_);
    
    // Return the maximum distance (extended EMD)
    return max(causeEMD, effectEMD);
}

std::string Concept::toString() const {
    stringstream ss;
    
    ss << "Concept[Mechanism: {";
    for (size_t i = 0; i < mechanism_.size(); ++i) {
        ss << mechanism_[i];
        if (i < mechanism_.size() - 1) {
            ss << ", ";
        }
    }
    ss << "}, Phi: " << fixed << setprecision(4) << integratedInformation_
       << ", Cause Info: " << causeInformation_
       << ", Effect Info: " << effectInformation_ << "]";
    
    return ss.str();
}

//=============================================================================
// ConceptGenerator Implementation
//=============================================================================

ConceptGenerator::ConceptGenerator(
    std::shared_ptr<PhiContext> context,
    std::shared_ptr<CausalRepertoireGenerator> repertoireGenerator)
    : context_(context),
      repertoireGenerator_(repertoireGenerator),
      minimumInformationThreshold_(1e-6) {
    
    if (!repertoireGenerator_) {
        throw runtime_error("No repertoire generator provided");
    }
}

Concept ConceptGenerator::generateConcept(
    const std::vector<size_t>& mechanism,
    const std::shared_ptr<CausalState>& state) {
    
    // Check for cached result
    ConceptKey key = generateConceptKey(mechanism, state);
    auto cachedResult = lookupConcept(key);
    if (cachedResult) {
        return *cachedResult;
    }
    
    // Generate all possible purviews for the system
    size_t numNodes = context_->getCausalGraph()->getNumNodes();
    vector<vector<size_t>> allPurviews;
    
    // Start with single-element purviews and build up
    for (size_t purviewSize = 1; purviewSize <= numNodes; ++purviewSize) {
        vector<bool> selection(numNodes, false);
        fill(selection.begin(), selection.begin() + purviewSize, true);
        
        do {
            vector<size_t> purview;
            for (size_t i = 0; i < numNodes; ++i) {
                if (selection[i]) {
                    purview.push_back(i);
                }
            }
            allPurviews.push_back(purview);
            
        } while (next_permutation(selection.begin(), selection.end()));
    }
    
    // Find purviews with maximum cause and effect information
    auto [maxCausePurview, maxCauseRepertoire, maxCauseInfo] = 
        findMaxCausePurview(mechanism, state, allPurviews);
    
    auto [maxEffectPurview, maxEffectRepertoire, maxEffectInfo] = 
        findMaxEffectPurview(mechanism, state, allPurviews);
    
    // Calculate integrated information for the mechanism
    double phiCause = repertoireGenerator_->calculateIntegratedCauseInformation(
        mechanism, maxCausePurview, state);
    
    double phiEffect = repertoireGenerator_->calculateIntegratedEffectInformation(
        mechanism, maxEffectPurview, state);
    
    // Integrated information is the minimum of the cause and effect values
    double phi = min(phiCause, phiEffect);
    
    // Create concept
    Concept concept(
        mechanism,
        maxCauseRepertoire,
        maxEffectRepertoire,
        maxCauseInfo,
        maxEffectInfo,
        phi
    );
    
    // Cache the result
    storeConcept(key, concept);
    
    return concept;
}

std::vector<Concept> ConceptGenerator::generateAllConcepts(
    const std::vector<size_t>& system,
    const std::shared_ptr<CausalState>& state) {
    
    // Generate all possible mechanisms from the system elements
    vector<vector<size_t>> allMechanisms = generateAllMechanisms(system);
    
    // Generate concepts for each mechanism
    vector<Concept> concepts;
    
    for (const auto& mechanism : allMechanisms) {
        // Skip empty mechanism
        if (mechanism.empty()) {
            continue;
        }
        
        Concept concept = generateConcept(mechanism, state);
        
        // Only include concepts with sufficient integrated information
        if (concept.getIntegratedInformation() > minimumInformationThreshold_) {
            concepts.push_back(concept);
        }
    }
    
    return concepts;
}

std::vector<Concept> ConceptGenerator::generateConceptualStructure(
    const std::vector<size_t>& system,
    const std::shared_ptr<CausalState>& state) {
    
    // Generate all concepts for the system
    vector<Concept> concepts = generateAllConcepts(system, state);
    
    // Sort concepts by integrated information (descending)
    sort(concepts.begin(), concepts.end(), 
        [](const Concept& a, const Concept& b) {
            return a.getIntegratedInformation() > b.getIntegratedInformation();
        }
    );
    
    return concepts;
}

double ConceptGenerator::calculateConceptualInformation(
    const std::vector<size_t>& system,
    const std::shared_ptr<CausalState>& state) {
    
    // Generate conceptual structure
    vector<Concept> concepts = generateConceptualStructure(system, state);
    
    // Sum integrated information across all concepts
    double conceptualInfo = 0.0;
    for (const auto& concept : concepts) {
        conceptualInfo += concept.getIntegratedInformation();
    }
    
    return conceptualInfo;
}

void ConceptGenerator::setMinimumInformationThreshold(double threshold) {
    minimumInformationThreshold_ = threshold;
}

double ConceptGenerator::getMinimumInformationThreshold() const {
    return minimumInformationThreshold_;
}

ConceptGenerator::ConceptKey ConceptGenerator::generateConceptKey(
    const std::vector<size_t>& mechanism,
    const std::shared_ptr<CausalState>& state) const {
    
    // Create a string key based on the inputs
    stringstream ss;
    
    // Add mechanism elements
    ss << "M[";
    for (size_t elem : mechanism) {
        ss << elem << ",";
    }
    ss << "]_";
    
    // Add state hash
    size_t stateHash = 0;
    const VectorXd& stateValues = state->getValues();
    for (int i = 0; i < stateValues.size(); ++i) {
        boost::hash_combine(stateHash, hash<double>{}(stateValues(i)));
    }
    ss << "S" << stateHash;
    
    return ss.str();
}

std::optional<Concept> ConceptGenerator::lookupConcept(const ConceptKey& key) const {
    auto it = conceptCache_.find(key);
    if (it != conceptCache_.end()) {
        return it->second;
    }
    return std::nullopt;
}

void ConceptGenerator::storeConcept(const ConceptKey& key, const Concept& concept) {
    // Only store if caching is enabled in the context
    if (context_->getConfig().cacheIntermediateResults) {
        conceptCache_[key] = concept;
    }
}

std::vector<std::vector<size_t>> ConceptGenerator::generateAllMechanisms(
    const std::vector<size_t>& system) const {
    
    // Generate all possible subsets of the system elements
    vector<vector<size_t>> mechanisms;
    
    size_t numElements = system.size();
    size_t numMechanisms = 1 << numElements; // 2^n subsets
    
    for (size_t i = 0; i < numMechanisms; ++i) {
        vector<size_t> mechanism;
        
        for (size_t j = 0; j < numElements; ++j) {
            if ((i >> j) & 1) {
                mechanism.push_back(system[j]);
            }
        }
        
        mechanisms.push_back(mechanism);
    }
    
    return mechanisms;
}

std::tuple<std::vector<size_t>, CausalRepertoire, double> ConceptGenerator::findMaxCausePurview(
    const std::vector<size_t>& mechanism,
    const std::shared_ptr<CausalState>& state,
    const std::vector<std::vector<size_t>>& allPurviews) {
    
    double maxInfo = 0.0;
    vector<size_t> maxPurview;
    CausalRepertoire maxRepertoire = repertoireGenerator_->generateUnconstrainedCauseRepertoire({});
    
    for (const auto& purview : allPurviews) {
        // Calculate cause information for this purview
        double info = repertoireGenerator_->calculateCauseInformation(mechanism, purview, state);
        
        // Update maximum if higher
        if (info > maxInfo) {
            maxInfo = info;
            maxPurview = purview;
            maxRepertoire = repertoireGenerator_->generateCauseRepertoire(mechanism, purview, state);
        }
    }
    
    return {maxPurview, maxRepertoire, maxInfo};
}

std::tuple<std::vector<size_t>, CausalRepertoire, double> ConceptGenerator::findMaxEffectPurview(
    const std::vector<size_t>& mechanism,
    const std::shared_ptr<CausalState>& state,
    const std::vector<std::vector<size_t>>& allPurviews) {
    
    double maxInfo = 0.0;
    vector<size_t> maxPurview;
    CausalRepertoire maxRepertoire = repertoireGenerator_->generateUnconstrainedEffectRepertoire({});
    
    for (const auto& purview : allPurviews) {
        // Calculate effect information for this purview
        double info = repertoireGenerator_->calculateEffectInformation(mechanism, purview, state);
        
        // Update maximum if higher
        if (info > maxInfo) {
            maxInfo = info;
            maxPurview = purview;
            maxRepertoire = repertoireGenerator_->generateEffectRepertoire(mechanism, purview, state);
        }
    }
    
    return {maxPurview, maxRepertoire, maxInfo};
}

} // namespace phi_calculator
} // namespace ultra