/**
 * @file information_integration.cpp
 * @brief Implementation of information integration algorithms for IIT
 * 
 * This file implements the core information integration algorithms from Integrated
 * Information Theory (IIT). It focuses on measuring how information is integrated
 * across a system, calculating various forms of Φ measures, and analyzing the
 * information-theoretic properties of system integration.
 * 
 * <AUTHOR> Team
 * @date 2025
 */

#include "information_integration.h"
#include "causal_analysis.h"
#include "../utils/matrix_operations.h"
#include "../utils/information_theory.h"
#include "../utils/parallel_processing.h"

#include <algorithm>
#include <cmath>
#include <complex>
#include <execution>
#include <future>
#include <iostream>
#include <limits>
#include <numeric>
#include <queue>
#include <random>
#include <set>
#include <unordered_map>

#include <Eigen/Dense>
#include <Eigen/Eigenvalues>
#include <boost/math/constants/constants.hpp>
#include <boost/math/special_functions/digamma.hpp>
#include <boost/graph/graph_traits.hpp>
#include <boost/graph/adjacency_list.hpp>
#include <boost/graph/connected_components.hpp>
#include <boost/graph/betweenness_centrality.hpp>

#ifdef ULTRA_HAS_CUDA
#include <cuda_runtime.h>
#include <cublas_v2.h>
#endif

namespace ultra {
namespace phi_calculator {

using namespace Eigen;
using namespace std;
using namespace ultra::utils;

//=============================================================================
// Helper Functions
//=============================================================================

namespace {
    /**
     * @brief Calculate Gaussian mutual information between two variables
     * 
     * @param varX Variance of X
     * @param varY Variance of Y
     * @param covXY Covariance between X and Y
     * @return Mutual information value
     */
    double gaussianMutualInformation(double varX, double varY, double covXY) {
        double determinant = varX * varY - covXY * covXY;
        if (determinant <= 0) {
            return 0.0;
        }
        
        return 0.5 * log(varX * varY / determinant);
    }

    /**
     * @brief Calculate the Kullback-Leibler divergence between two distributions
     * 
     * @param p First probability distribution
     * @param q Second probability distribution
     * @return KL divergence value
     */
    double klDivergence(const VectorXd& p, const VectorXd& q) {
        if (p.size() != q.size()) {
            throw runtime_error("Distributions must be of the same size");
        }
        
        double kl = 0.0;
        for (int i = 0; i < p.size(); ++i) {
            if (p(i) > 0) {
                if (q(i) <= 0) {
                    return numeric_limits<double>::infinity();
                }
                kl += p(i) * log2(p(i) / q(i));
            }
        }
        
        return kl;
    }
    
    /**
     * @brief Calculate the Jensen-Shannon divergence between two distributions
     * 
     * @param p First probability distribution
     * @param q Second probability distribution
     * @return JS divergence value
     */
    double jsDistance(const VectorXd& p, const VectorXd& q) {
        // Create the average distribution m = (p + q) / 2
        VectorXd m = (p + q) / 2.0;
        
        // Calculate JS-divergence: JS(p,q) = (KL(p||m) + KL(q||m)) / 2
        double kl_p_m = klDivergence(p, m);
        double kl_q_m = klDivergence(q, m);
        
        // Return the square root to get a proper metric (JS distance)
        return sqrt((kl_p_m + kl_q_m) / 2.0);
    }

    /**
     * @brief Compute integrated information using the Wasserstein distance
     * 
     * @param jointCov Joint covariance matrix of the system
     * @param partCov1 Covariance matrix of first partition
     * @param partCov2 Covariance matrix of second partition
     * @return Wasserstein-based phi value
     */
    double wasserstein2Phi(const MatrixXd& jointCov, const MatrixXd& partCov1, const MatrixXd& partCov2) {
        // For Gaussian variables, the Wasserstein-2 distance is:
        // W2² = Tr(Σ1 + Σ2 - 2(Σ1^(1/2)Σ2Σ1^(1/2))^(1/2))
        
        // Ensure symmetry of covariance matrices
        MatrixXd symJointCov = (jointCov + jointCov.transpose()) / 2.0;
        MatrixXd symPartCov = MatrixXd::Zero(jointCov.rows(), jointCov.cols());
        
        // Create block-diagonal matrix from partition covariances
        int size1 = partCov1.rows();
        int size2 = partCov2.rows();
        
        if (size1 + size2 != symJointCov.rows()) {
            throw runtime_error("Partition sizes don't match joint size");
        }
        
        symPartCov.block(0, 0, size1, size1) = (partCov1 + partCov1.transpose()) / 2.0;
        symPartCov.block(size1, size1, size2, size2) = (partCov2 + partCov2.transpose()) / 2.0;
        
        // Compute square roots
        SelfAdjointEigenSolver<MatrixXd> eigenSolver1(symJointCov);
        SelfAdjointEigenSolver<MatrixXd> eigenSolver2(symPartCov);
        
        // Handle numerical issues
        VectorXd eigenvalues1 = eigenSolver1.eigenvalues().array().max(0.0);
        VectorXd eigenvalues2 = eigenSolver2.eigenvalues().array().max(0.0);
        
        MatrixXd sqrtJointCov = eigenSolver1.eigenvectors() * 
                               eigenvalues1.array().sqrt().matrix().asDiagonal() * 
                               eigenSolver1.eigenvectors().transpose();
        
        MatrixXd sqrtPartCov = eigenSolver2.eigenvectors() * 
                               eigenvalues2.array().sqrt().matrix().asDiagonal() * 
                               eigenSolver2.eigenvectors().transpose();
        
        // Compute the trace term
        double traceTerm = (symJointCov + symPartCov - 2.0 * sqrtJointCov * sqrtPartCov).trace();
        
        // Ensure it's non-negative
        return max(0.0, sqrt(traceTerm));
    }
    
    /**
     * @brief Compute the total correlation in a multivariate system
     * 
     * @param jointCovariance Joint covariance matrix
     * @return Total correlation value
     */
    double totalCorrelation(const MatrixXd& jointCovariance) {
        int n = jointCovariance.rows();
        
        // Extract individual variances
        VectorXd variances = jointCovariance.diagonal();
        
        // Calculate determinant of joint covariance
        double jointDet = jointCovariance.determinant();
        
        // Handle numerical issues
        if (jointDet <= 0) {
            // Use eigenvalues for a more stable calculation
            SelfAdjointEigenSolver<MatrixXd> eigenSolver(jointCovariance);
            VectorXd eigenvalues = eigenSolver.eigenvalues();
            
            // Calculate log-determinant as the sum of log-eigenvalues
            jointDet = 1.0;
            for (int i = 0; i < n; ++i) {
                if (eigenvalues(i) > 1e-10) {
                    jointDet *= eigenvalues(i);
                }
            }
            
            if (jointDet <= 0) {
                return 0.0; // Fallback for numerical instability
            }
        }
        
        // Calculate total correlation: sum of individual entropies - joint entropy
        double sumLogVar = 0.0;
        for (int i = 0; i < n; ++i) {
            if (variances(i) > 0) {
                sumLogVar += log(variances(i));
            }
        }
        
        return 0.5 * (sumLogVar - log(jointDet));
    }
    
    /**
     * @brief Compute the dual total correlation in a multivariate system
     * 
     * @param jointCovariance Joint covariance matrix
     * @return Dual total correlation value
     */
    double dualTotalCorrelation(const MatrixXd& jointCovariance) {
        int n = jointCovariance.rows();
        
        // Calculate joint entropy
        double jointDet = jointCovariance.determinant();
        
        // Handle numerical issues
        if (jointDet <= 0) {
            SelfAdjointEigenSolver<MatrixXd> solver(jointCovariance);
            VectorXd eigenvalues = solver.eigenvalues();
            
            jointDet = 1.0;
            for (int i = 0; i < n; ++i) {
                if (eigenvalues(i) > 1e-10) {
                    jointDet *= eigenvalues(i);
                }
            }
        }
        
        double jointEntropy = 0.5 * log(pow(2 * M_PI * M_E, n) * jointDet);
        
        // Calculate conditional entropies
        double sumConditionalEntropies = 0.0;
        
        for (int i = 0; i < n; ++i) {
            // Create the submatrix excluding variable i
            MatrixXd subCov(n - 1, n - 1);
            
            int row_idx = 0;
            for (int j = 0; j < n; ++j) {
                if (j == i) continue;
                
                int col_idx = 0;
                for (int k = 0; k < n; ++k) {
                    if (k == i) continue;
                    
                    subCov(row_idx, col_idx) = jointCovariance(j, k);
                    col_idx++;
                }
                row_idx++;
            }
            
            // Calculate determinant of the submatrix
            double subDet = subCov.determinant();
            
            // Handle numerical issues
            if (subDet <= 0) {
                SelfAdjointEigenSolver<MatrixXd> solver(subCov);
                VectorXd eigenvalues = solver.eigenvalues();
                
                subDet = 1.0;
                for (int j = 0; j < n - 1; ++j) {
                    if (eigenvalues(j) > 1e-10) {
                        subDet *= eigenvalues(j);
                    }
                }
            }
            
            double conditionalEntropy = 0.5 * log(pow(2 * M_PI * M_E, n - 1) * subDet);
            sumConditionalEntropies += conditionalEntropy;
        }
        
        // Dual total correlation = sum of conditional entropies - (n-1) * joint entropy
        return sumConditionalEntropies - (n - 1) * jointEntropy;
    }
}

//=============================================================================
// IntegrationMeasure Implementation
//=============================================================================

IntegrationMeasure::IntegrationMeasure(size_t systemSize)
    : systemSize_(systemSize),
      jointDistribution_(VectorXd::Ones(systemSize) / systemSize),
      jointCovariance_(MatrixXd::Identity(systemSize, systemSize)),
      independentCovariance_(MatrixXd::Identity(systemSize, systemSize)) {
}

void IntegrationMeasure::setJointDistribution(const VectorXd& distribution) {
    if (distribution.size() != static_cast<int>(systemSize_)) {
        throw runtime_error("Distribution size must match system size");
    }
    
    jointDistribution_ = distribution;
    
    // Normalize if needed
    double sum = jointDistribution_.sum();
    if (abs(sum - 1.0) > 1e-6) {
        jointDistribution_ /= sum;
    }
}

void IntegrationMeasure::setJointCovariance(const MatrixXd& covariance) {
    if (covariance.rows() != static_cast<int>(systemSize_) || 
        covariance.cols() != static_cast<int>(systemSize_)) {
        throw runtime_error("Covariance matrix dimensions must match system size");
    }
    
    jointCovariance_ = covariance;
    
    // Ensure it's symmetric
    jointCovariance_ = (jointCovariance_ + jointCovariance_.transpose()) / 2.0;
    
    // Extract diagonal for independent covariance
    independentCovariance_ = jointCovariance_.diagonal().asDiagonal();
}

double IntegrationMeasure::calculateTotalCorrelation() const {
    // Use numerical methods appropriate to the data
    if (useGaussianApproximation_) {
        return ::totalCorrelation(jointCovariance_);
    } else {
        // For discrete distributions
        // Calculate individual entropies
        VectorXd marginals = calculateMarginals();
        
        double sumEntropies = 0.0;
        for (int i = 0; i < marginals.size(); ++i) {
            if (marginals(i) > 0) {
                sumEntropies -= marginals(i) * log2(marginals(i));
            }
        }
        
        // Calculate joint entropy
        double jointEntropy = 0.0;
        for (int i = 0; i < jointDistribution_.size(); ++i) {
            if (jointDistribution_(i) > 0) {
                jointEntropy -= jointDistribution_(i) * log2(jointDistribution_(i));
            }
        }
        
        return sumEntropies - jointEntropy;
    }
}

double IntegrationMeasure::calculateMutualInformation(
    const vector<size_t>& subset1, const vector<size_t>& subset2) const {
    
    if (subset1.empty() || subset2.empty()) {
        return 0.0;
    }
    
    if (useGaussianApproximation_) {
        // Extract relevant submatrices from the covariance matrix
        MatrixXd cov1 = extractCovariance(subset1);
        MatrixXd cov2 = extractCovariance(subset2);
        MatrixXd cov12 = extractCrossCovariance(subset1, subset2);
        
        // For Gaussian variables, MI can be calculated from determinants
        double det1 = cov1.determinant();
        double det2 = cov2.determinant();
        
        // Create the joint covariance matrix
        MatrixXd jointCov = MatrixXd::Zero(subset1.size() + subset2.size(), 
                                          subset1.size() + subset2.size());
        jointCov.block(0, 0, subset1.size(), subset1.size()) = cov1;
        jointCov.block(subset1.size(), subset1.size(), subset2.size(), subset2.size()) = cov2;
        jointCov.block(0, subset1.size(), subset1.size(), subset2.size()) = cov12;
        jointCov.block(subset1.size(), 0, subset2.size(), subset1.size()) = cov12.transpose();
        
        double jointDet = jointCov.determinant();
        
        // Handle numerical issues
        if (det1 <= 0 || det2 <= 0 || jointDet <= 0) {
            // Fall back to eigenvalue-based calculation
            SelfAdjointEigenSolver<MatrixXd> solver1(cov1);
            SelfAdjointEigenSolver<MatrixXd> solver2(cov2);
            SelfAdjointEigenSolver<MatrixXd> solverJoint(jointCov);
            
            VectorXd eig1 = solver1.eigenvalues();
            VectorXd eig2 = solver2.eigenvalues();
            VectorXd eigJoint = solverJoint.eigenvalues();
            
            double logDet1 = 0.0, logDet2 = 0.0, logDetJoint = 0.0;
            
            for (int i = 0; i < eig1.size(); ++i) {
                if (eig1(i) > 1e-10) {
                    logDet1 += log(eig1(i));
                }
            }
            
            for (int i = 0; i < eig2.size(); ++i) {
                if (eig2(i) > 1e-10) {
                    logDet2 += log(eig2(i));
                }
            }
            
            for (int i = 0; i < eigJoint.size(); ++i) {
                if (eigJoint(i) > 1e-10) {
                    logDetJoint += log(eigJoint(i));
                }
            }
            
            // MI = H(X) + H(Y) - H(X,Y)
            return 0.5 * (logDet1 + logDet2 - logDetJoint);
        }
        
        // MI = H(X) + H(Y) - H(X,Y) = 0.5 * ln(det(Σ₁)det(Σ₂)/det(Σ))
        return 0.5 * log(det1 * det2 / jointDet);
    } else {
        // For discrete distributions
        // Extract relevant marginal distributions
        VectorXd jointProb = extractJointDistribution(subset1, subset2);
        VectorXd marginal1 = calculateMarginalDistribution(subset1);
        VectorXd marginal2 = calculateMarginalDistribution(subset2);
        
        // Calculate mutual information
        double mi = 0.0;
        
        for (size_t i = 0; i < jointProb.size(); ++i) {
            if (jointProb(i) > 0) {
                double p_i = jointProb(i);
                double p_x = marginal1(i % marginal1.size());
                double p_y = marginal2(i / marginal1.size());
                
                if (p_x > 0 && p_y > 0) {
                    mi += p_i * log2(p_i / (p_x * p_y));
                }
            }
        }
        
        return mi;
    }
}

double IntegrationMeasure::calculateDualTotalCorrelation() const {
    if (useGaussianApproximation_) {
        return ::dualTotalCorrelation(jointCovariance_);
    } else {
        // For discrete distributions, calculate dual total correlation
        // DTC = sum of conditional entropies - (n-1) * joint entropy
        double jointEntropy = 0.0;
        for (int i = 0; i < jointDistribution_.size(); ++i) {
            if (jointDistribution_(i) > 0) {
                jointEntropy -= jointDistribution_(i) * log2(jointDistribution_(i));
            }
        }
        
        // Calculate conditional entropies
        double sumConditionalEntropies = 0.0;
        for (size_t i = 0; i < systemSize_; ++i) {
            vector<size_t> others;
            for (size_t j = 0; j < systemSize_; ++j) {
                if (j != i) {
                    others.push_back(j);
                }
            }
            
            // Calculate H(X₁,...,X_{i-1},X_{i+1},...,X_n)
            double othersEntropy = calculateSubsetEntropy(others);
            sumConditionalEntropies += othersEntropy;
        }
        
        return sumConditionalEntropies - (systemSize_ - 1) * jointEntropy;
    }
}

double IntegrationMeasure::calculateIntegratedInformation(const Bipartition& partition) const {
    if (partition.first.empty() || partition.second.empty()) {
        return 0.0;
    }
    
    if (useGaussianApproximation_) {
        // Extract relevant submatrices from the covariance matrix
        MatrixXd cov1 = extractCovariance(partition.first);
        MatrixXd cov2 = extractCovariance(partition.second);
        
        // Calculate Wasserstein-based Phi
        return wasserstein2Phi(jointCovariance_, cov1, cov2);
    } else {
        // For discrete distributions, use KL divergence-based Phi
        // Calculate the product distribution of the partitioned system
        VectorXd productDist = calculateProductDistribution(partition);
        
        // Calculate KL divergence between joint and product distributions
        return klDivergence(jointDistribution_, productDist);
    }
}

double IntegrationMeasure::calculateMaximumIntegratedInformation() const {
    // Generate all possible bipartitions
    vector<Bipartition> partitions = generateBipartitions(systemSize_);
    
    // Calculate Phi for each partition
    double maxPhi = 0.0;
    Bipartition optimalPartition;
    
    for (const auto& partition : partitions) {
        double phi = calculateIntegratedInformation(partition);
        
        if (phi > maxPhi) {
            maxPhi = phi;
            optimalPartition = partition;
        }
    }
    
    optimalPartition_ = optimalPartition;
    return maxPhi;
}

double IntegrationMeasure::calculateMinimumIntegratedInformation() const {
    // Generate all possible bipartitions
    vector<Bipartition> partitions = generateBipartitions(systemSize_);
    
    // Calculate Phi for each partition and find the minimum
    double minPhi = numeric_limits<double>::infinity();
    Bipartition mipPartition;
    
    for (const auto& partition : partitions) {
        double phi = calculateIntegratedInformation(partition);
        
        // Normalize by the partition size
        double normalization = calculatePartitionNormalization(partition);
        double normalizedPhi = phi / normalization;
        
        if (normalizedPhi < minPhi) {
            minPhi = normalizedPhi;
            mipPartition = partition;
        }
    }
    
    minimalPartition_ = mipPartition;
    
    // Return the non-normalized Phi for the MIP
    return calculateIntegratedInformation(mipPartition);
}

const Bipartition& IntegrationMeasure::getOptimalPartition() const {
    return optimalPartition_;
}

const Bipartition& IntegrationMeasure::getMinimalPartition() const {
    return minimalPartition_;
}

void IntegrationMeasure::setUseGaussianApproximation(bool useGaussian) {
    useGaussianApproximation_ = useGaussian;
}

bool IntegrationMeasure::isUsingGaussianApproximation() const {
    return useGaussianApproximation_;
}

VectorXd IntegrationMeasure::calculateMarginals() const {
    // For a simplified case, assume independence
    // In a real implementation, this would extract the proper marginals
    return jointDistribution_;
}

MatrixXd IntegrationMeasure::extractCovariance(const vector<size_t>& indices) const {
    MatrixXd subCov(indices.size(), indices.size());
    
    for (size_t i = 0; i < indices.size(); ++i) {
        for (size_t j = 0; j < indices.size(); ++j) {
            subCov(i, j) = jointCovariance_(indices[i], indices[j]);
        }
    }
    
    return subCov;
}

MatrixXd IntegrationMeasure::extractCrossCovariance(
    const vector<size_t>& indices1, const vector<size_t>& indices2) const {
    
    MatrixXd crossCov(indices1.size(), indices2.size());
    
    for (size_t i = 0; i < indices1.size(); ++i) {
        for (size_t j = 0; j < indices2.size(); ++j) {
            crossCov(i, j) = jointCovariance_(indices1[i], indices2[j]);
        }
    }
    
    return crossCov;
}

VectorXd IntegrationMeasure::extractJointDistribution(
    const vector<size_t>& indices1, const vector<size_t>& indices2) const {
    
    // For a full implementation, this would extract the joint distribution
    // of the specified indices from the full joint distribution
    
    // Here's a simplified placeholder that returns the full joint distribution
    return jointDistribution_;
}

VectorXd IntegrationMeasure::calculateMarginalDistribution(const vector<size_t>& indices) const {
    // For a full implementation, this would calculate the marginal distribution
    // for the specified indices
    
    // Simplified placeholder
    VectorXd marginal = VectorXd::Ones(indices.size());
    marginal /= marginal.sum();
    
    return marginal;
}

VectorXd IntegrationMeasure::calculateProductDistribution(const Bipartition& partition) const {
    // Calculate the product distribution of the partitioned system
    // P(X,Y) = P(X)P(Y) for partition (X,Y)
    
    // For a full implementation, this would calculate the actual product distribution
    // Simplified placeholder
    return jointDistribution_;
}

double IntegrationMeasure::calculateSubsetEntropy(const vector<size_t>& indices) const {
    // Calculate the entropy of a subset of variables
    // Simplified placeholder
    return log2(indices.size());
}

double IntegrationMeasure::calculatePartitionNormalization(const Bipartition& partition) const {
    // Calculate normalization factor based on partition size
    // Common approach: K = min(|X|, |Y|)
    return min(partition.first.size(), partition.second.size());
}

vector<Bipartition> IntegrationMeasure::generateBipartitions(size_t n) const {
    if (n <= 1) {
        return {};
    }
    
    // The number of bipartitions is 2^(n-1) - 1 (we exclude the trivial partition)
    size_t numPartitions = (1 << (n - 1)) - 1;
    vector<Bipartition> partitions;
    partitions.reserve(numPartitions);
    
    // Generate all non-trivial bipartitions
    for (size_t i = 1; i < (1 << n) / 2; ++i) {
        vector<size_t> part1, part2;
        
        for (size_t j = 0; j < n; ++j) {
            if (i & (1 << j)) {
                part1.push_back(j);
            } else {
                part2.push_back(j);
            }
        }
        
        if (!part1.empty() && !part2.empty()) {
            partitions.push_back({part1, part2});
        }
    }
    
    return partitions;
}

//=============================================================================
// GeometricIntegrationMeasure Implementation
//=============================================================================

GeometricIntegrationMeasure::GeometricIntegrationMeasure(size_t systemSize)
    : IntegrationMeasure(systemSize),
      geometricTolerance_(1e-6),
      maxIterations_(100) {
}

double GeometricIntegrationMeasure::calculateGeometricIntegration() const {
    // Calculate integration using information geometry approach
    
    // For Gaussian variables, use Riemannian distance between covariance matrices
    if (useGaussianApproximation_) {
        return calculateRiemannianDistance();
    } else {
        // For discrete variables, use information-geometric distance
        return calculateFisherDistance();
    }
}

double GeometricIntegrationMeasure::calculateInformationProjection(const Bipartition& partition) const {
    // Calculate the information projection onto the independence model defined by the partition
    
    // For Gaussian variables, this is the closest positive definite matrix that respects the partition
    if (useGaussianApproximation_) {
        return calculateGaussianInfoProjection(partition);
    } else {
        // For discrete variables, use iterative scaling to find the projection
        return calculateDiscreteInfoProjection(partition);
    }
}

double GeometricIntegrationMeasure::calculateRiemannianDistance() const {
    // Calculate Riemannian distance between jointCovariance_ and independentCovariance_
    
    // For positive definite matrices A and B, the Riemannian distance is:
    // d(A,B) = sqrt(sum_i log^2(λ_i))
    // where λ_i are the eigenvalues of A^(-1/2) B A^(-1/2)
    
    // Ensure matrices are positive definite
    MatrixXd A = jointCovariance_;
    MatrixXd B = independentCovariance_;
    
    // Add small regularization if needed
    double minEigA = SelfAdjointEigenSolver<MatrixXd>(A).eigenvalues().minCoeff();
    double minEigB = SelfAdjointEigenSolver<MatrixXd>(B).eigenvalues().minCoeff();
    
    double eps = 1e-8;
    if (minEigA < eps) {
        A += eps * MatrixXd::Identity(A.rows(), A.cols());
    }
    if (minEigB < eps) {
        B += eps * MatrixXd::Identity(B.rows(), B.cols());
    }
    
    // Calculate A^(-1/2)
    SelfAdjointEigenSolver<MatrixXd> eigenSolver(A);
    VectorXd eigenvalues = eigenSolver.eigenvalues();
    MatrixXd eigenvectors = eigenSolver.eigenvectors();
    
    VectorXd invSqrtEigenvalues = eigenvalues.array().sqrt().inverse();
    MatrixXd invSqrtA = eigenvectors * invSqrtEigenvalues.asDiagonal() * eigenvectors.transpose();
    
    // Calculate A^(-1/2) B A^(-1/2)
    MatrixXd M = invSqrtA * B * invSqrtA;
    
    // Calculate eigenvalues of M
    VectorXd logEigenvalues = SelfAdjointEigenSolver<MatrixXd>(M).eigenvalues().array().max(eps).log();
    
    // Calculate Riemannian distance
    double distance = sqrt(logEigenvalues.squaredNorm());
    
    return distance;
}

double GeometricIntegrationMeasure::calculateFisherDistance() const {
    // Calculate Fisher information distance between joint and independent distributions
    
    // For discrete distributions, use the Fisher distance (approximated by geodesic)
    // In the context of probability distributions, this is related to the Hellinger distance
    
    // Simplified implementation using Hellinger distance as an approximation
    double sum = 0.0;
    VectorXd independentDist = calculateProductDistribution({
        {0}, {1} // Simple bipartition for demonstration
    });
    
    for (int i = 0; i < jointDistribution_.size(); ++i) {
        sum += (sqrt(jointDistribution_(i)) - sqrt(independentDist(i))) *
               (sqrt(jointDistribution_(i)) - sqrt(independentDist(i)));
    }
    
    return sqrt(sum) / sqrt(2.0);
}

double GeometricIntegrationMeasure::calculateGaussianInfoProjection(const Bipartition& partition) const {
    // Calculate the information projection for Gaussian variables
    
    // For a bipartition (A,B), the information projection is the covariance matrix
    // that has the same marginals as the original but zero cross-covariance
    
    // Extract indices for the partition
    const auto& part1 = partition.first;
    const auto& part2 = partition.second;
    
    // Create the projection matrix
    MatrixXd projection = jointCovariance_;
    
    // Zero out the cross-covariance blocks
    for (size_t i = 0; i < part1.size(); ++i) {
        for (size_t j = 0; j < part2.size(); ++j) {
            projection(part1[i], part2[j]) = 0.0;
            projection(part2[j], part1[i]) = 0.0;
        }
    }
    
    // Calculate the KL divergence from original to projection
    // For Gaussian distributions, KL(p||q) = 0.5 * [log(det(Σ_q)/det(Σ_p)) + tr(Σ_q^(-1)Σ_p) - n]
    
    // Calculate determinants
    double detJoint = jointCovariance_.determinant();
    double detProj = projection.determinant();
    
    // Handle numerical issues
    if (detJoint <= 0 || detProj <= 0) {
        // Use more stable calculation with eigenvalues
        SelfAdjointEigenSolver<MatrixXd> solver1(jointCovariance_);
        SelfAdjointEigenSolver<MatrixXd> solver2(projection);
        
        VectorXd eig1 = solver1.eigenvalues();
        VectorXd eig2 = solver2.eigenvalues();
        
        double logDetJoint = 0.0, logDetProj = 0.0;
        
        for (int i = 0; i < eig1.size(); ++i) {
            if (eig1(i) > 1e-10) {
                logDetJoint += log(eig1(i));
            }
        }
        
        for (int i = 0; i < eig2.size(); ++i) {
            if (eig2(i) > 1e-10) {
                logDetProj += log(eig2(i));
            }
        }
        
        // Regularize the projection matrix
        MatrixXd regProj = projection + 1e-8 * MatrixXd::Identity(projection.rows(), projection.cols());
        
        // Calculate trace term
        double traceTerm = (regProj.inverse() * jointCovariance_).trace();
        
        // Calculate KL divergence
        return 0.5 * (logDetProj - logDetJoint + traceTerm - systemSize_);
    }
    
    // Calculate KL divergence
    return 0.5 * (log(detProj / detJoint) + 
                 (projection.inverse() * jointCovariance_).trace() - 
                 systemSize_);
}

double GeometricIntegrationMeasure::calculateDiscreteInfoProjection(const Bipartition& partition) const {
    // Calculate the information projection for discrete variables using iterative scaling
    
    // This is a simplified implementation - a full one would use iterative proportional fitting
    // to find the closest distribution that satisfies the independence constraints
    
    // For now, just return the KL divergence to the product distribution
    VectorXd productDist = calculateProductDistribution(partition);
    return klDivergence(jointDistribution_, productDist);
}

void GeometricIntegrationMeasure::setGeometricTolerance(double tolerance) {
    geometricTolerance_ = max(1e-10, tolerance);
}

double GeometricIntegrationMeasure::getGeometricTolerance() const {
    return geometricTolerance_;
}

void GeometricIntegrationMeasure::setMaxIterations(size_t maxIterations) {
    maxIterations_ = max(size_t(1), maxIterations);
}

size_t GeometricIntegrationMeasure::getMaxIterations() const {
    return maxIterations_;
}

//=============================================================================
// InformationIntegrator Implementation
//=============================================================================

InformationIntegrator::InformationIntegrator()
    : isInitialized_(false),
      systemSize_(0),
      integrationType_(IntegrationType::MINIMUM_INFORMATION_PARTITION),
      autoDetectDataType_(true),
      memoryUsageLimit_(8 * 1024 * 1024 * 1024ULL) { // 8GB default limit
}

void InformationIntegrator::initialize(size_t systemSize) {
    systemSize_ = systemSize;
    measure_ = make_shared<IntegrationMeasure>(systemSize);
    geometricMeasure_ = make_shared<GeometricIntegrationMeasure>(systemSize);
    
    // Allocate workspace buffers
    allocateWorkspace();
    
    isInitialized_ = true;
}

void InformationIntegrator::setJointDistribution(const VectorXd& distribution) {
    if (!isInitialized_) {
        throw runtime_error("InformationIntegrator not initialized");
    }
    
    measure_->setJointDistribution(distribution);
    geometricMeasure_->setJointDistribution(distribution);
    
    // If auto-detect is enabled, determine whether to use Gaussian approximation
    if (autoDetectDataType_) {
        bool useGaussian = shouldUseGaussianApproximation(distribution);
        measure_->setUseGaussianApproximation(useGaussian);
        geometricMeasure_->setUseGaussianApproximation(useGaussian);
    }
}

void InformationIntegrator::setJointCovariance(const MatrixXd& covariance) {
    if (!isInitialized_) {
        throw runtime_error("InformationIntegrator not initialized");
    }
    
    measure_->setJointCovariance(covariance);
    geometricMeasure_->setJointCovariance(covariance);
    
    // If auto-detect is enabled, use Gaussian approximation for covariance
    if (autoDetectDataType_) {
        measure_->setUseGaussianApproximation(true);
        geometricMeasure_->setUseGaussianApproximation(true);
    }
}

double InformationIntegrator::calculateIntegratedInformation() {
    if (!isInitialized_) {
        throw runtime_error("InformationIntegrator not initialized");
    }
    
    double phi = 0.0;
    
    switch (integrationType_) {
        case IntegrationType::MINIMUM_INFORMATION_PARTITION:
            phi = measure_->calculateMinimumIntegratedInformation();
            minimalPartition_ = measure_->getMinimalPartition();
            break;
        
        case IntegrationType::MAXIMUM_MUTUAL_INFORMATION:
            phi = measure_->calculateMaximumIntegratedInformation();
            maximalPartition_ = measure_->getOptimalPartition();
            break;
        
        case IntegrationType::GEOMETRIC:
            phi = geometricMeasure_->calculateGeometricIntegration();
            break;
    }
    
    integratedInformation_ = phi;
    return phi;
}

double InformationIntegrator::getTotalCorrelation() const {
    if (!isInitialized_) {
        throw runtime_error("InformationIntegrator not initialized");
    }
    
    return measure_->calculateTotalCorrelation();
}

double InformationIntegrator::getDualTotalCorrelation() const {
    if (!isInitialized_) {
        throw runtime_error("InformationIntegrator not initialized");
    }
    
    return measure_->calculateDualTotalCorrelation();
}

vector<double> InformationIntegrator::calculateIntegrationSpectrum() {
    if (!isInitialized_) {
        throw runtime_error("InformationIntegrator not initialized");
    }
    
    vector<double> spectrum;
    
    // Calculate integrated information for all possible partitions
    vector<Bipartition> partitions = measure_->generateBipartitions(systemSize_);
    
    for (const auto& partition : partitions) {
        double phi = measure_->calculateIntegratedInformation(partition);
        spectrum.push_back(phi);
    }
    
    // Sort the spectrum in descending order
    sort(spectrum.begin(), spectrum.end(), greater<double>());
    
    return spectrum;
}

IntegrationHierarchy InformationIntegrator::calculateIntegrationHierarchy() {
    if (!isInitialized_) {
        throw runtime_error("InformationIntegrator not initialized");
    }
    
    IntegrationHierarchy hierarchy;
    
    // Start with the whole system
    vector<size_t> allElements(systemSize_);
    iota(allElements.begin(), allElements.end(), 0);
    
    // Calculate subsystem integrated information recursively
    findHierarchicalComplexes(allElements, hierarchy);
    
    return hierarchy;
}

double InformationIntegrator::getIntegratedInformation() const {
    return integratedInformation_;
}

const Bipartition& InformationIntegrator::getMinimalPartition() const {
    return minimalPartition_;
}

const Bipartition& InformationIntegrator::getMaximalPartition() const {
    return maximalPartition_;
}

void InformationIntegrator::setIntegrationType(IntegrationType type) {
    integrationType_ = type;
}

InformationIntegrator::IntegrationType InformationIntegrator::getIntegrationType() const {
    return integrationType_;
}

void InformationIntegrator::setUseGaussianApproximation(bool useGaussian) {
    if (isInitialized_) {
        measure_->setUseGaussianApproximation(useGaussian);
        geometricMeasure_->setUseGaussianApproximation(useGaussian);
    }
    
    autoDetectDataType_ = false;
}

bool InformationIntegrator::isUsingGaussianApproximation() const {
    if (!isInitialized_) {
        return false;
    }
    
    return measure_->isUsingGaussianApproximation();
}

void InformationIntegrator::setMemoryUsageLimit(size_t bytesLimit) {
    memoryUsageLimit_ = bytesLimit;
}

size_t InformationIntegrator::getMemoryUsageLimit() const {
    return memoryUsageLimit_;
}

void InformationIntegrator::allocateWorkspace() {
    // Estimate memory requirements
    size_t requiredMemory = estimateMemoryRequirements();
    
    // Check if we exceed the memory limit
    if (requiredMemory > memoryUsageLimit_) {
        // Enable memory-efficient algorithms
        useMemoryEfficientAlgorithms_ = true;
        cout << "Warning: Enabling memory-efficient algorithms due to large system size" << endl;
    } else {
        useMemoryEfficientAlgorithms_ = false;
    }
    
    // Allocate workspace buffers
    size_t bufferSize = min(requiredMemory, memoryUsageLimit_);
    workspaceBuffer_.resize(bufferSize / sizeof(double));
}

size_t InformationIntegrator::estimateMemoryRequirements() const {
    // Estimate memory requirements for storing all partitions
    // Number of bipartitions: 2^(n-1) - 1
    size_t numBipartitions = (1ULL << (systemSize_ - 1)) - 1;
    
    // Storage for each partition: 2 vectors of size_t
    size_t partitionSize = 2 * systemSize_ * sizeof(size_t);
    
    // Storage for distribution vectors
    size_t distributionSize = systemSize_ * sizeof(double);
    
    // Storage for covariance matrices
    size_t covarianceSize = systemSize_ * systemSize_ * sizeof(double);
    
    // Total memory requirements
    size_t totalMemory = numBipartitions * partitionSize + 
                        distributionSize + 
                        covarianceSize;
    
    return totalMemory;
}

bool InformationIntegrator::shouldUseGaussianApproximation(const VectorXd& distribution) const {
    // Detect if the distribution looks approximately Gaussian
    // This is a heuristic based on the entropy and moments
    
    // Calculate mean and variance
    double mean = 0.0;
    for (int i = 0; i < distribution.size(); ++i) {
        mean += i * distribution(i);
    }
    
    double variance = 0.0;
    for (int i = 0; i < distribution.size(); ++i) {
        variance += (i - mean) * (i - mean) * distribution(i);
    }
    
    // Calculate entropy
    double entropy = 0.0;
    for (int i = 0; i < distribution.size(); ++i) {
        if (distribution(i) > 0) {
            entropy -= distribution(i) * log(distribution(i));
        }
    }
    
    // For a Gaussian with variance σ², the entropy is 0.5 * log(2πeσ²)
    double gaussianEntropy = 0.5 * log(2 * M_PI * M_E * variance);
    
    // Compare entropy to Gaussian entropy
    double entropyRatio = entropy / gaussianEntropy;
    
    // If the ratio is close to 1, the distribution is approximately Gaussian
    return abs(entropyRatio - 1.0) < 0.1;
}

void InformationIntegrator::findHierarchicalComplexes(
    const vector<size_t>& elements, IntegrationHierarchy& hierarchy) {
    
    if (elements.size() < 2) {
        return; // Need at least two elements for a complex
    }
    
    // Calculate integrated information for this subsystem
    double phi = calculateSubsystemIntegration(elements);
    
    // Add this subsystem to the hierarchy
    hierarchy.push_back({elements, phi});
    
    // Find the minimal partition for this subsystem
    Bipartition partition = findSubsystemPartition(elements);
    
    // If phi is significant, recurse into both parts of the partition
    if (phi > 1e-6 && !partition.first.empty() && !partition.second.empty()) {
        findHierarchicalComplexes(partition.first, hierarchy);
        findHierarchicalComplexes(partition.second, hierarchy);
    }
}

double InformationIntegrator::calculateSubsystemIntegration(const vector<size_t>& elements) const {
    if (elements.size() < 2) {
        return 0.0;
    }
    
    // Extract the subsystem from the full system
    // In a full implementation, this would extract the relevant joint distribution
    // and covariance for the subsystem
    
    // Simplified implementation - assume subsystem Phi scales with size
    return elements.size() * 0.1;
}

Bipartition InformationIntegrator::findSubsystemPartition(const vector<size_t>& elements) const {
    if (elements.size() < 2) {
        return {{}, {}};
    }
    
    // Find the partition that minimizes integrated information
    // In a full implementation, this would search all bipartitions of the subsystem
    
    // Simplified implementation - just split in half
    size_t half = elements.size() / 2;
    
    vector<size_t> first(elements.begin(), elements.begin() + half);
    vector<size_t> second(elements.begin() + half, elements.end());
    
    return {first, second};
}

} // namespace phi_calculator
} // namespace ultra