/**
 * @file causal_analysis.cpp
 * @brief Implementation of causal analysis algorithms for integrated information theory
 * 
 * This file implements causal analysis mechanisms based on Integrated Information
 * Theory (IIT) for measuring and analyzing cause-effect structures within the ULTRA
 * system. It calculates causal relationships, effective information, and minimum
 * information partitions used in Phi (Φ) calculation - the central measure of
 * integrated information and consciousness in the system.
 * 
 * <AUTHOR> Team
 * @date 2025
 */

#include "causal_analysis.h"
#include "../utils/matrix_operations.h"
#include "../utils/information_theory.h"
#include "../utils/parallel_processing.h"

#include <algorithm>
#include <cmath>
#include <complex>
#include <execution>
#include <future>
#include <iostream>
#include <limits>
#include <numeric>
#include <queue>
#include <random>
#include <set>
#include <unordered_map>

#include <Eigen/Dense>
#include <Eigen/Eigenvalues>
#include <boost/math/constants/constants.hpp>

namespace ultra {
namespace phi_calculator {

using namespace Eigen;
using namespace std;
using namespace ultra::utils;

//=============================================================================
// Helper Functions
//=============================================================================

namespace {
    /**
     * @brief Generate all possible bipartitions of a set
     * 
     * @param n Size of the set
     * @return Vector of all possible bipartitions
     */
    vector<Bipartition> generateBipartitions(size_t n) {
        if (n <= 1) {
            return {};
        }
        
        // The number of bipartitions is 2^(n-1) - 1 (we exclude the trivial partition)
        size_t numPartitions = (1 << (n - 1)) - 1;
        vector<Bipartition> partitions;
        partitions.reserve(numPartitions);
        
        // Generate all non-trivial bipartitions
        for (size_t i = 1; i < (1 << n) / 2; ++i) {
            vector<size_t> part1, part2;
            
            for (size_t j = 0; j < n; ++j) {
                if (i & (1 << j)) {
                    part1.push_back(j);
                } else {
                    part2.push_back(j);
                }
            }
            
            if (!part1.empty() && !part2.empty()) {
                partitions.push_back({part1, part2});
            }
        }
        
        return partitions;
    }
    
    /**
     * @brief Generate all possible partitions of a set
     * 
     * @param n Size of the set
     * @param maxParts Maximum number of parts in a partition
     * @return Vector of all possible partitions
     */
    vector<Partition> generatePartitions(size_t n, size_t maxParts = 0) {
        if (n == 0) {
            return {};
        }
        
        if (maxParts == 0) {
            maxParts = n;
        }
        
        vector<Partition> result;
        
        // Bellman numbers get very large, so we need to limit partitions for larger n
        if (n > 10) {
            // For large n, we'll only generate balanced partitions with at most 4 parts
            size_t actualMaxParts = min(maxParts, size_t(4));
            
            for (size_t k = 2; k <= actualMaxParts; ++k) {
                // Generate approximately equal sized partitions
                vector<vector<size_t>> parts(k);
                size_t elementsPerPart = n / k;
                size_t remainder = n % k;
                
                size_t index = 0;
                for (size_t i = 0; i < k; ++i) {
                    size_t partSize = elementsPerPart + (i < remainder ? 1 : 0);
                    for (size_t j = 0; j < partSize; ++j) {
                        parts[i].push_back(index++);
                    }
                }
                
                result.push_back(parts);
                
                // Add some random variations for diversity
                for (size_t i = 0; i < 5; ++i) {
                    auto partsCopy = parts;
                    // Randomly move a few elements between parts
                    random_device rd;
                    mt19937 gen(rd());
                    uniform_int_distribution<size_t> partDist(0, k - 1);
                    uniform_int_distribution<size_t> elementDist(0, n - 1);
                    
                    for (size_t j = 0; j < n / 4; ++j) {
                        size_t element = elementDist(gen);
                        // Find which part contains this element
                        for (size_t p1 = 0; p1 < k; ++p1) {
                            auto& part = partsCopy[p1];
                            auto it = find(part.begin(), part.end(), element);
                            if (it != part.end()) {
                                // Move to a different part
                                size_t p2 = partDist(gen);
                                while (p2 == p1) {
                                    p2 = partDist(gen);
                                }
                                part.erase(it);
                                partsCopy[p2].push_back(element);
                                break;
                            }
                        }
                    }
                    
                    // Ensure no parts are empty
                    bool hasEmpty = false;
                    for (const auto& part : partsCopy) {
                        if (part.empty()) {
                            hasEmpty = true;
                            break;
                        }
                    }
                    
                    if (!hasEmpty) {
                        result.push_back(partsCopy);
                    }
                }
            }
        } else {
            // For small n, generate all partitions
            vector<vector<size_t>> parts;
            vector<size_t> elements(n);
            iota(elements.begin(), elements.end(), 0);
            
            // Use Bell numbers algorithm to generate all partitions
            function<void(size_t, vector<vector<size_t>>&)> generateAllPartitions;
            generateAllPartitions = [&](size_t pos, vector<vector<size_t>>& currentParts) {
                if (pos == n) {
                    if (currentParts.size() >= 2 && currentParts.size() <= maxParts) {
                        result.push_back(currentParts);
                    }
                    return;
                }
                
                // Add to an existing part
                for (size_t i = 0; i < currentParts.size(); ++i) {
                    currentParts[i].push_back(elements[pos]);
                    generateAllPartitions(pos + 1, currentParts);
                    currentParts[i].pop_back();
                }
                
                // Start a new part if we haven't reached the max
                if (currentParts.size() < maxParts) {
                    currentParts.push_back({elements[pos]});
                    generateAllPartitions(pos + 1, currentParts);
                    currentParts.pop_back();
                }
            };
            
            vector<vector<size_t>> initialParts;
            generateAllPartitions(0, initialParts);
        }
        
        return result;
    }
    
    /**
     * @brief Calculate Earth Mover's Distance between two distributions
     * 
     * @param dist1 First probability distribution
     * @param dist2 Second probability distribution
     * @return Earth Mover's Distance value
     */
    double earthMoversDistance(const VectorXd& dist1, const VectorXd& dist2) {
        if (dist1.size() != dist2.size()) {
            throw runtime_error("Distributions must have the same size for EMD calculation");
        }
        
        size_t n = dist1.size();
        
        // Calculate cumulative distributions
        VectorXd cdf1 = VectorXd::Zero(n);
        VectorXd cdf2 = VectorXd::Zero(n);
        
        cdf1(0) = dist1(0);
        cdf2(0) = dist2(0);
        
        for (size_t i = 1; i < n; ++i) {
            cdf1(i) = cdf1(i - 1) + dist1(i);
            cdf2(i) = cdf2(i - 1) + dist2(i);
        }
        
        // Calculate EMD as the L1 norm (sum of absolute differences) of CDFs
        double emd = 0.0;
        for (size_t i = 0; i < n; ++i) {
            emd += abs(cdf1(i) - cdf2(i));
        }
        
        return emd;
    }
    
    /**
     * @brief Calculate Wasserstein distance between two distributions
     * 
     * Implements 2-Wasserstein distance (optimal transport)
     * 
     * @param mean1 Mean of first distribution
     * @param cov1 Covariance of first distribution
     * @param mean2 Mean of second distribution
     * @param cov2 Covariance of second distribution
     * @return Wasserstein distance value
     */
    double wasserstein2Distance(const VectorXd& mean1, const MatrixXd& cov1,
                               const VectorXd& mean2, const MatrixXd& cov2) {
        if (mean1.size() != mean2.size() || 
            cov1.rows() != cov2.rows() || 
            cov1.cols() != cov2.cols()) {
            throw runtime_error("Distribution dimensions must match for Wasserstein distance");
        }
        
        // For multivariate Gaussian distributions, the 2-Wasserstein distance is:
        // W2² = ||μ₁ - μ₂||² + Tr(Σ₁ + Σ₂ - 2(Σ₂^(1/2) Σ₁ Σ₂^(1/2))^(1/2))
        
        // Calculate squared Euclidean distance between means
        double meanDist = (mean1 - mean2).squaredNorm();
        
        // Calculate square root of covariance matrices
        SelfAdjointEigenSolver<MatrixXd> solver1(cov1);
        SelfAdjointEigenSolver<MatrixXd> solver2(cov2);
        
        MatrixXd sqrtCov1 = solver1.operatorSqrt();
        MatrixXd sqrtCov2 = solver2.operatorSqrt();
        
        // Calculate the product term
        MatrixXd product = sqrtCov2 * cov1 * sqrtCov2;
        SelfAdjointEigenSolver<MatrixXd> solverProd(product);
        MatrixXd sqrtProduct = solverProd.operatorSqrt();
        
        // Calculate trace term
        double traceTerm = (cov1 + cov2 - 2.0 * sqrtProduct).trace();
        
        // Combine terms
        double wasserstein = meanDist + traceTerm;
        
        return sqrt(max(0.0, wasserstein));
    }
}

//=============================================================================
// CausalState Implementation
//=============================================================================

CausalState::CausalState(size_t size) 
    : size_(size), 
      values_(VectorXd::Zero(size)),
      probabilities_(VectorXd::Ones(size) / static_cast<double>(size)) {
}

CausalState::CausalState(const VectorXd& values)
    : size_(values.size()),
      values_(values),
      probabilities_(VectorXd::Ones(size_) / static_cast<double>(size_)) {
}

CausalState::CausalState(const VectorXd& values, const VectorXd& probabilities)
    : size_(values.size()),
      values_(values),
      probabilities_(probabilities) {
    
    // Normalize probabilities if needed
    double sum = probabilities_.sum();
    if (abs(sum - 1.0) > 1e-6) {
        probabilities_ /= sum;
    }
}

size_t CausalState::getSize() const {
    return size_;
}

const VectorXd& CausalState::getValues() const {
    return values_;
}

const VectorXd& CausalState::getProbabilities() const {
    return probabilities_;
}

void CausalState::setValues(const VectorXd& values) {
    if (values.size() != size_) {
        throw runtime_error("Values vector size must match state size");
    }
    values_ = values;
}

void CausalState::setProbabilities(const VectorXd& probabilities) {
    if (probabilities.size() != size_) {
        throw runtime_error("Probabilities vector size must match state size");
    }
    
    probabilities_ = probabilities;
    
    // Normalize probabilities
    double sum = probabilities_.sum();
    if (abs(sum - 1.0) > 1e-6) {
        probabilities_ /= sum;
    }
}

void CausalState::perturb(double amount) {
    // Generate random perturbations
    random_device rd;
    mt19937 gen(rd());
    normal_distribution<double> dist(0.0, amount);
    
    // Apply perturbations to values
    for (int i = 0; i < values_.size(); ++i) {
        values_(i) += dist(gen);
    }
}

VectorXd CausalState::sample(size_t numSamples) const {
    // Sample from the distribution
    random_device rd;
    mt19937 gen(rd());
    
    // Prepare discrete distribution based on probabilities
    vector<double> probVector(probabilities_.data(), probabilities_.data() + probabilities_.size());
    discrete_distribution<int> distribution(probVector.begin(), probVector.end());
    
    // Sample indices
    vector<int> indices(numSamples);
    for (size_t i = 0; i < numSamples; ++i) {
        indices[i] = distribution(gen);
    }
    
    // Count occurrences
    VectorXd result = VectorXd::Zero(size_);
    for (int idx : indices) {
        result(idx) += 1.0;
    }
    
    // Normalize to get probabilities
    result /= static_cast<double>(numSamples);
    
    return result;
}

double CausalState::distance(const CausalState& other) const {
    if (size_ != other.size_) {
        throw runtime_error("Cannot calculate distance between states of different sizes");
    }
    
    // Calculate Wasserstein distance between value distributions
    MatrixXd cov1 = MatrixXd::Identity(size_, size_) * 0.01;  // Small covariance as regularization
    MatrixXd cov2 = MatrixXd::Identity(size_, size_) * 0.01;
    
    double dist = wasserstein2Distance(values_, cov1, other.values_, cov2);
    
    // Also consider KL divergence between probability distributions
    double kl = 0.0;
    for (int i = 0; i < size_; ++i) {
        if (probabilities_(i) > 0 && other.probabilities_(i) > 0) {
            kl += probabilities_(i) * log(probabilities_(i) / other.probabilities_(i));
        }
    }
    
    // Combine the distances
    return dist + kl;
}

double CausalState::entropy() const {
    double entropy = 0.0;
    for (int i = 0; i < probabilities_.size(); ++i) {
        if (probabilities_(i) > 0) {
            entropy -= probabilities_(i) * log2(probabilities_(i));
        }
    }
    return entropy;
}

//=============================================================================
// CausalLink Implementation
//=============================================================================

CausalLink::CausalLink(
    size_t sourceIndex, 
    size_t targetIndex, 
    double strength, 
    LinkType type)
    : sourceIndex_(sourceIndex),
      targetIndex_(targetIndex),
      strength_(strength),
      type_(type) {
}

size_t CausalLink::getSourceIndex() const {
    return sourceIndex_;
}

size_t CausalLink::getTargetIndex() const {
    return targetIndex_;
}

double CausalLink::getStrength() const {
    return strength_;
}

CausalLink::LinkType CausalLink::getType() const {
    return type_;
}

void CausalLink::setStrength(double strength) {
    strength_ = strength;
}

void CausalLink::setType(LinkType type) {
    type_ = type;
}

//=============================================================================
// CausalGraph Implementation
//=============================================================================

CausalGraph::CausalGraph(size_t numNodes)
    : numNodes_(numNodes),
      adjacencyMatrix_(MatrixXd::Zero(numNodes, numNodes)),
      nodes_(numNodes),
      nodeStates_(numNodes) {
    
    // Initialize nodes with default states
    for (size_t i = 0; i < numNodes; ++i) {
        nodes_[i] = make_shared<CausalState>(numNodes);
        nodeStates_[i] = make_shared<CausalState>(numNodes);
    }
}

CausalGraph::CausalGraph(const MatrixXd& adjacencyMatrix)
    : numNodes_(adjacencyMatrix.rows()),
      adjacencyMatrix_(adjacencyMatrix),
      nodes_(numNodes_),
      nodeStates_(numNodes_) {
    
    if (adjacencyMatrix.rows() != adjacencyMatrix.cols()) {
        throw runtime_error("Adjacency matrix must be square");
    }
    
    // Initialize nodes with default states
    for (size_t i = 0; i < numNodes_; ++i) {
        nodes_[i] = make_shared<CausalState>(numNodes_);
        nodeStates_[i] = make_shared<CausalState>(numNodes_);
    }
    
    // Create links based on adjacency matrix
    for (size_t i = 0; i < numNodes_; ++i) {
        for (size_t j = 0; j < numNodes_; ++j) {
            if (adjacencyMatrix(i, j) > 0) {
                CausalLink link(i, j, adjacencyMatrix(i, j));
                links_.push_back(link);
            }
        }
    }
}

size_t CausalGraph::getNumNodes() const {
    return numNodes_;
}

const MatrixXd& CausalGraph::getAdjacencyMatrix() const {
    return adjacencyMatrix_;
}

const vector<CausalLink>& CausalGraph::getLinks() const {
    return links_;
}

void CausalGraph::addLink(size_t source, size_t target, double strength, CausalLink::LinkType type) {
    if (source >= numNodes_ || target >= numNodes_) {
        throw runtime_error("Node index out of bounds");
    }
    
    // Update adjacency matrix
    adjacencyMatrix_(source, target) = strength;
    
    // Add link to list
    links_.emplace_back(source, target, strength, type);
}

void CausalGraph::removeLink(size_t source, size_t target) {
    if (source >= numNodes_ || target >= numNodes_) {
        throw runtime_error("Node index out of bounds");
    }
    
    // Update adjacency matrix
    adjacencyMatrix_(source, target) = 0.0;
    
    // Remove link from list
    links_.erase(
        remove_if(links_.begin(), links_.end(), 
                 [source, target](const CausalLink& link) {
                     return link.getSourceIndex() == source && 
                            link.getTargetIndex() == target;
                 }),
        links_.end()
    );
}

void CausalGraph::setNodeState(size_t index, shared_ptr<CausalState> state) {
    if (index >= numNodes_) {
        throw runtime_error("Node index out of bounds");
    }
    
    nodeStates_[index] = state;
}

shared_ptr<CausalState> CausalGraph::getNodeState(size_t index) const {
    if (index >= numNodes_) {
        throw runtime_error("Node index out of bounds");
    }
    
    return nodeStates_[index];
}

vector<size_t> CausalGraph::getIncomingConnections(size_t nodeIndex) const {
    if (nodeIndex >= numNodes_) {
        throw runtime_error("Node index out of bounds");
    }
    
    vector<size_t> connections;
    for (size_t i = 0; i < numNodes_; ++i) {
        if (adjacencyMatrix_(i, nodeIndex) > 0) {
            connections.push_back(i);
        }
    }
    
    return connections;
}

vector<size_t> CausalGraph::getOutgoingConnections(size_t nodeIndex) const {
    if (nodeIndex >= numNodes_) {
        throw runtime_error("Node index out of bounds");
    }
    
    vector<size_t> connections;
    for (size_t j = 0; j < numNodes_; ++j) {
        if (adjacencyMatrix_(nodeIndex, j) > 0) {
            connections.push_back(j);
        }
    }
    
    return connections;
}

VectorXd CausalGraph::propagateState(const VectorXd& state) const {
    if (state.size() != numNodes_) {
        throw runtime_error("State vector size must match number of nodes");
    }
    
    // Simple linear propagation model
    VectorXd newState = adjacencyMatrix_ * state;
    
    // Normalize to prevent explosion
    newState = newState.array().tanh();
    
    return newState;
}

MatrixXd CausalGraph::computeTransferEntropy() const {
    MatrixXd transferEntropy = MatrixXd::Zero(numNodes_, numNodes_);
    
    // For each potential causal pair
    for (size_t source = 0; source < numNodes_; ++source) {
        for (size_t target = 0; target < numNodes_; ++target) {
            if (source != target) {
                // Get history data for both nodes
                const auto& sourceState = nodeStates_[source]->getValues();
                const auto& targetState = nodeStates_[target]->getValues();
                
                // Calculate transfer entropy
                double te = calculateTransferEntropy(sourceState, targetState);
                transferEntropy(source, target) = te;
            }
        }
    }
    
    return transferEntropy;
}

double CausalGraph::calculateTransferEntropy(const VectorXd& source, const VectorXd& target) const {
    // This is a simplified implementation of transfer entropy
    // In a real implementation, you would need time series data
    
    // For the purpose of this example, we'll use a proxy based on mutual information
    double mutualInfo = 0.0;
    
    // Calculate mean and standard deviation for both vectors
    double sourceMean = source.mean();
    double sourceStd = sqrt((source.array() - sourceMean).square().sum() / (source.size() - 1));
    
    double targetMean = target.mean();
    double targetStd = sqrt((target.array() - targetMean).square().sum() / (target.size() - 1));
    
    // Standardize vectors
    VectorXd sourceStandardized = (source.array() - sourceMean) / sourceStd;
    VectorXd targetStandardized = (target.array() - targetMean) / targetStd;
    
    // Calculate correlation
    double correlation = (sourceStandardized.array() * targetStandardized.array()).sum() / source.size();
    correlation = max(-1.0, min(1.0, correlation));  // Clamp to [-1, 1]
    
    // Convert correlation to mutual information for Gaussian variables
    if (abs(correlation) > 1e-10) {
        mutualInfo = -0.5 * log(1 - correlation * correlation);
    }
    
    return mutualInfo;
}

//=============================================================================
// CausalAnalyzer Implementation
//=============================================================================

CausalAnalyzer::CausalAnalyzer(shared_ptr<CausalGraph> graph)
    : graph_(graph),
      numNodes_(graph->getNumNodes()),
      effectiveInformation_(0.0),
      integratedInformation_(0.0) {
}

double CausalAnalyzer::calculateEffectiveInformation() {
    if (!graph_) {
        throw runtime_error("No causal graph provided");
    }
    
    // Calculate effective information as the KL divergence between
    // the actual causal distribution and the independent distribution
    
    // Get all node states
    vector<shared_ptr<CausalState>> states;
    for (size_t i = 0; i < numNodes_; ++i) {
        states.push_back(graph_->getNodeState(i));
    }
    
    // Calculate joint entropy of the system
    double jointEntropy = calculateJointEntropy(states);
    
    // Calculate sum of individual entropies
    double sumIndividualEntropies = 0.0;
    for (const auto& state : states) {
        sumIndividualEntropies += state->entropy();
    }
    
    // Effective information is the difference (mutual information)
    effectiveInformation_ = sumIndividualEntropies - jointEntropy;
    
    return effectiveInformation_;
}

double CausalAnalyzer::calculateJointEntropy(const vector<shared_ptr<CausalState>>& states) const {
    // This is a simplification for the example
    // In a real implementation, you would need to consider the joint distribution
    
    // Here we'll estimate joint entropy using the sum of individual entropies
    // adjusted by a correlation factor
    
    double sumEntropies = 0.0;
    for (const auto& state : states) {
        sumEntropies += state->entropy();
    }
    
    // Calculate average correlation between states
    double avgCorrelation = 0.0;
    int count = 0;
    
    for (size_t i = 0; i < states.size(); ++i) {
        for (size_t j = i + 1; j < states.size(); ++j) {
            const VectorXd& values1 = states[i]->getValues();
            const VectorXd& values2 = states[j]->getValues();
            
            // Calculate correlation coefficient
            double mean1 = values1.mean();
            double mean2 = values2.mean();
            
            double cov = 0.0;
            double var1 = 0.0;
            double var2 = 0.0;
            
            for (int k = 0; k < values1.size(); ++k) {
                double d1 = values1(k) - mean1;
                double d2 = values2(k) - mean2;
                cov += d1 * d2;
                var1 += d1 * d1;
                var2 += d2 * d2;
            }
            
            cov /= values1.size();
            var1 /= values1.size();
            var2 /= values1.size();
            
            double corr = 0.0;
            if (var1 > 0 && var2 > 0) {
                corr = cov / sqrt(var1 * var2);
                corr = max(-1.0, min(1.0, corr));
            }
            
            avgCorrelation += abs(corr);
            count++;
        }
    }
    
    if (count > 0) {
        avgCorrelation /= count;
    }
    
    // Adjust joint entropy based on correlation
    // High correlation -> lower joint entropy
    double jointEntropy = sumEntropies * (1.0 - 0.5 * avgCorrelation);
    
    return jointEntropy;
}

double CausalAnalyzer::calculateIntegratedInformation() {
    if (!graph_) {
        throw runtime_error("No causal graph provided");
    }
    
    // Calculate whole system effective information
    double wholeSystemEI = calculateEffectiveInformation();
    
    // Calculate minimum information partition
    Partition minInfoPartition = findMinimumInformationPartition();
    
    // Calculate effective information for the partition
    double partitionEI = calculatePartitionEffectiveInformation(minInfoPartition);
    
    // Integrated information is the difference
    integratedInformation_ = wholeSystemEI - partitionEI;
    
    return integratedInformation_;
}

Partition CausalAnalyzer::findMinimumInformationPartition() {
    // Generate all possible partitions
    vector<Partition> partitions = generatePartitions(numNodes_);
    
    // Find the partition with minimum normalized effective information
    double minNormalizedEI = numeric_limits<double>::max();
    Partition minPartition;
    
    for (const auto& partition : partitions) {
        double partitionEI = calculatePartitionEffectiveInformation(partition);
        
        // Normalize by partition size
        double normalizationFactor = calculateNormalizationFactor(partition);
        double normalizedEI = partitionEI / normalizationFactor;
        
        if (normalizedEI < minNormalizedEI) {
            minNormalizedEI = normalizedEI;
            minPartition = partition;
        }
    }
    
    return minPartition;
}

double CausalAnalyzer::calculatePartitionEffectiveInformation(const Partition& partition) const {
    // Calculate sum of effective information within each part
    double sumEI = 0.0;
    
    for (const auto& part : partition) {
        // Skip single node parts (no internal EI)
        if (part.size() <= 1) {
            continue;
        }
        
        // Extract states for this part
        vector<shared_ptr<CausalState>> partStates;
        for (size_t nodeIdx : part) {
            partStates.push_back(graph_->getNodeState(nodeIdx));
        }
        
        // Calculate joint entropy for this part
        double jointEntropy = calculateJointEntropy(partStates);
        
        // Calculate sum of individual entropies
        double sumIndividualEntropies = 0.0;
        for (const auto& state : partStates) {
            sumIndividualEntropies += state->entropy();
        }
        
        // Effective information for this part
        double partEI = sumIndividualEntropies - jointEntropy;
        sumEI += partEI;
    }
    
    return sumEI;
}

double CausalAnalyzer::calculateNormalizationFactor(const Partition& partition) const {
    // Normalization factor based on partition size
    double K = 0.0;
    
    for (const auto& part : partition) {
        // K scales with the size of each part
        K += part.size() * log2(part.size());
    }
    
    return max(1.0, K);
}

double CausalAnalyzer::getEffectiveInformation() const {
    return effectiveInformation_;
}

double CausalAnalyzer::getIntegratedInformation() const {
    return integratedInformation_;
}

vector<double> CausalAnalyzer::calculateNodeContributions() const {
    vector<double> contributions(numNodes_, 0.0);
    
    // Calculate baseline integrated information
    double baselinePhi = integratedInformation_;
    
    // Calculate integrated information with each node removed
    for (size_t i = 0; i < numNodes_; ++i) {
        // Create a temporary graph with node i disabled
        shared_ptr<CausalGraph> tempGraph = make_shared<CausalGraph>(graph_->getAdjacencyMatrix());
        
        // Zero out all connections to/from node i
        for (size_t j = 0; j < numNodes_; ++j) {
            tempGraph->removeLink(i, j);
            tempGraph->removeLink(j, i);
        }
        
        // Calculate integrated information for modified graph
        CausalAnalyzer tempAnalyzer(tempGraph);
        double modifiedPhi = tempAnalyzer.calculateIntegratedInformation();
        
        // Node contribution is the decrease in Phi when it's removed
        contributions[i] = baselinePhi - modifiedPhi;
    }
    
    return contributions;
}

vector<CausalPath> CausalAnalyzer::findCausalPaths(size_t sourceIdx, size_t targetIdx, size_t maxLength) const {
    if (sourceIdx >= numNodes_ || targetIdx >= numNodes_) {
        throw runtime_error("Node index out of bounds");
    }
    
    vector<CausalPath> paths;
    
    // BFS to find all paths from source to target
    queue<CausalPath> pathQueue;
    
    // Start with a path containing just the source
    pathQueue.push({{sourceIdx}});
    
    while (!pathQueue.empty()) {
        CausalPath currentPath = pathQueue.front();
        pathQueue.pop();
        
        size_t lastNode = currentPath.back();
        
        // If we reached the target, add path to result
        if (lastNode == targetIdx) {
            paths.push_back(currentPath);
            continue;
        }
        
        // Don't extend paths that are already at max length
        if (currentPath.size() >= maxLength) {
            continue;
        }
        
        // Get outgoing connections
        vector<size_t> outgoing = graph_->getOutgoingConnections(lastNode);
        
        // Extend path with each outgoing connection
        for (size_t next : outgoing) {
            // Skip if this would create a cycle
            if (find(currentPath.begin(), currentPath.end(), next) != currentPath.end()) {
                continue;
            }
            
            // Create new path
            CausalPath newPath = currentPath;
            newPath.push_back(next);
            pathQueue.push(newPath);
        }
    }
    
    return paths;
}

map<size_t, double> CausalAnalyzer::calculateCausalInfluence() const {
    map<size_t, double> influence;
    
    // Initialize influence for each node
    for (size_t i = 0; i < numNodes_; ++i) {
        influence[i] = 0.0;
    }
    
    // Calculate causal influence as the sum of outgoing connection strengths
    // weighted by the target node's centrality
    
    // First, calculate eigenvector centrality
    MatrixXd adj = graph_->getAdjacencyMatrix();
    
    // Ensure adjacency matrix is non-negative
    for (int i = 0; i < adj.rows(); ++i) {
        for (int j = 0; j < adj.cols(); ++j) {
            adj(i, j) = abs(adj(i, j));
        }
    }
    
    // Power iteration method to find eigenvector centrality
    VectorXd centrality = VectorXd::Ones(numNodes_);
    const int maxIter = 100;
    const double tolerance = 1e-6;
    
    for (int iter = 0; iter < maxIter; ++iter) {
        VectorXd newCentrality = adj * centrality;
        double norm = newCentrality.norm();
        
        if (norm < 1e-10) {
            break;  // No significant centrality
        }
        
        newCentrality /= norm;
        
        // Check convergence
        if ((newCentrality - centrality).norm() < tolerance) {
            centrality = newCentrality;
            break;
        }
        
        centrality = newCentrality;
    }
    
    // Normalize centrality
    centrality = centrality.array() / centrality.sum();
    
    // Calculate influence for each node
    for (size_t i = 0; i < numNodes_; ++i) {
        double totalInfluence = 0.0;
        
        for (size_t j = 0; j < numNodes_; ++j) {
            if (i != j) {
                double connectionStrength = adj(i, j);
                totalInfluence += connectionStrength * centrality(j);
            }
        }
        
        influence[i] = totalInfluence;
    }
    
    return influence;
}

//=============================================================================
// PhiCalculator Implementation
//=============================================================================

PhiCalculator::PhiCalculator()
    : isInitialized_(false) {
}

void PhiCalculator::initialize(size_t systemSize) {
    systemSize_ = systemSize;
    graph_ = make_shared<CausalGraph>(systemSize);
    analyzer_ = make_shared<CausalAnalyzer>(graph_);
    isInitialized_ = true;
}

void PhiCalculator::setGraph(shared_ptr<CausalGraph> graph) {
    graph_ = graph;
    systemSize_ = graph->getNumNodes();
    analyzer_ = make_shared<CausalAnalyzer>(graph);
    isInitialized_ = true;
}

shared_ptr<CausalGraph> PhiCalculator::getGraph() const {
    return graph_;
}

double PhiCalculator::calculatePhi() {
    if (!isInitialized_) {
        throw runtime_error("PhiCalculator not initialized");
    }
    
    // Calculate integrated information
    double phi = analyzer_->calculateIntegratedInformation();
    
    return phi;
}

vector<double> PhiCalculator::calculatePhiSpectrum() {
    if (!isInitialized_) {
        throw runtime_error("PhiCalculator not initialized");
    }
    
    vector<double> phiSpectrum;
    
    // Calculate Phi for the whole system
    double systemPhi = calculatePhi();
    phiSpectrum.push_back(systemPhi);
    
    // Calculate Phi for increasingly smaller subsystems
    // Start with all possible (n-1)-size subsystems
    for (size_t subSize = systemSize_ - 1; subSize >= 2; --subSize) {
        // Find maximum Phi across all subsystems of this size
        double maxSubPhi = 0.0;
        
        // Generate all possible subsystems of size 'subSize'
        vector<vector<size_t>> subsystems;
        vector<size_t> allNodes(systemSize_);
        iota(allNodes.begin(), allNodes.end(), 0);
        
        // Simple approach for small systems - generate all combinations
        if (systemSize_ <= 10) {
            vector<bool> selection(systemSize_, false);
            fill(selection.begin(), selection.begin() + subSize, true);
            
            do {
                vector<size_t> subsystem;
                for (size_t i = 0; i < systemSize_; ++i) {
                    if (selection[i]) {
                        subsystem.push_back(i);
                    }
                }
                subsystems.push_back(subsystem);
            } while (prev_permutation(selection.begin(), selection.end()));
        } else {
            // For larger systems, just sample a reasonable number of subsystems
            const size_t maxSamples = 20;
            random_device rd;
            mt19937 gen(rd());
            
            for (size_t sample = 0; sample < maxSamples; ++sample) {
                // Randomly select a subsystem
                vector<size_t> nodes = allNodes;
                shuffle(nodes.begin(), nodes.end(), gen);
                vector<size_t> subsystem(nodes.begin(), nodes.begin() + subSize);
                subsystems.push_back(subsystem);
            }
        }
        
        // Calculate Phi for each subsystem
        vector<double> subPhiValues = calculateSubsystemPhiValues(subsystems);
        
        // Find maximum Phi for this subsystem size
        if (!subPhiValues.empty()) {
            maxSubPhi = *max_element(subPhiValues.begin(), subPhiValues.end());
        }
        
        phiSpectrum.push_back(maxSubPhi);
    }
    
    return phiSpectrum;
}

vector<double> PhiCalculator::calculateSubsystemPhiValues(const vector<vector<size_t>>& subsystems) {
    vector<double> phiValues;
    phiValues.reserve(subsystems.size());
    
    for (const auto& subsystem : subsystems) {
        // Create a new graph for this subsystem
        MatrixXd subAdjMatrix = MatrixXd::Zero(subsystem.size(), subsystem.size());
        
        // Copy relevant connections
        for (size_t i = 0; i < subsystem.size(); ++i) {
            for (size_t j = 0; j < subsystem.size(); ++j) {
                subAdjMatrix(i, j) = graph_->getAdjacencyMatrix()(subsystem[i], subsystem[j]);
            }
        }
        
        auto subGraph = make_shared<CausalGraph>(subAdjMatrix);
        
        // Copy node states
        for (size_t i = 0; i < subsystem.size(); ++i) {
            shared_ptr<CausalState> origState = graph_->getNodeState(subsystem[i]);
            
            // Create a new state with appropriate size
            auto newState = make_shared<CausalState>(subsystem.size());
            
            // Copy values and probabilities if sizes match
            if (origState->getSize() == subsystem.size()) {
                newState->setValues(origState->getValues());
                newState->setProbabilities(origState->getProbabilities());
            }
            
            subGraph->setNodeState(i, newState);
        }
        
        // Calculate Phi for this subsystem
        CausalAnalyzer subAnalyzer(subGraph);
        double phi = subAnalyzer.calculateIntegratedInformation();
        phiValues.push_back(phi);
    }
    
    return phiValues;
}

map<size_t, double> PhiCalculator::calculateNodePhiContributions() {
    if (!isInitialized_) {
        throw runtime_error("PhiCalculator not initialized");
    }
    
    // Calculate phi contributions for each node
    vector<double> contributions = analyzer_->calculateNodeContributions();
    
    // Convert to map
    map<size_t, double> contributionMap;
    for (size_t i = 0; i < contributions.size(); ++i) {
        contributionMap[i] = contributions[i];
    }
    
    return contributionMap;
}

PhiComplexes PhiCalculator::identifyComplexes() {
    if (!isInitialized_) {
        throw runtime_error("PhiCalculator not initialized");
    }
    
    PhiComplexes complexes;
    
    // Start with the whole system
    vector<size_t> allNodes(systemSize_);
    iota(allNodes.begin(), allNodes.end(), 0);
    
    // Calculate Phi for the whole system
    double systemPhi = calculatePhi();
    complexes.push_back({allNodes, systemPhi});
    
    // Identify local maxima in Phi
    const size_t minComplexSize = 2;  // Minimum complex size to consider
    const double phiThreshold = 0.01; // Minimum Phi value to consider
    
    // For small systems, check all possible subsystems
    if (systemSize_ <= 8) {
        for (size_t size = systemSize_ - 1; size >= minComplexSize; --size) {
            // Generate all subsystems of this size
            vector<bool> selection(systemSize_, false);
            fill(selection.begin(), selection.begin() + size, true);
            
            do {
                vector<size_t> subsystem;
                for (size_t i = 0; i < systemSize_; ++i) {
                    if (selection[i]) {
                        subsystem.push_back(i);
                    }
                }
                
                // Calculate Phi for this subsystem
                double phiValue = calculateSubsystemPhi(subsystem);
                
                // Check if this is a local maximum in Phi
                if (phiValue > phiThreshold && isLocalMaximum(subsystem, phiValue)) {
                    complexes.push_back({subsystem, phiValue});
                }
            } while (prev_permutation(selection.begin(), selection.end()));
        }
    } else {
        // For larger systems, use a more efficient approach
        // Like hierarchical clustering or other heuristics
        // This is a simplified example
        
        // Start with some promising candidate subsystems
        vector<vector<size_t>> candidates = identifyCandidateSubsystems();
        
        // Evaluate each candidate
        for (const auto& subsystem : candidates) {
            if (subsystem.size() >= minComplexSize) {
                double phiValue = calculateSubsystemPhi(subsystem);
                
                if (phiValue > phiThreshold && isLocalMaximum(subsystem, phiValue)) {
                    complexes.push_back({subsystem, phiValue});
                }
            }
        }
    }
    
    // Sort complexes by Phi value
    sort(complexes.begin(), complexes.end(), 
        [](const pair<vector<size_t>, double>& a, const pair<vector<size_t>, double>& b) {
            return a.second > b.second;
        });
    
    return complexes;
}

double PhiCalculator::calculateSubsystemPhi(const vector<size_t>& subsystem) {
    // Create a new graph for this subsystem
    MatrixXd subAdjMatrix = MatrixXd::Zero(subsystem.size(), subsystem.size());
    
    // Copy relevant connections
    for (size_t i = 0; i < subsystem.size(); ++i) {
        for (size_t j = 0; j < subsystem.size(); ++j) {
            subAdjMatrix(i, j) = graph_->getAdjacencyMatrix()(subsystem[i], subsystem[j]);
        }
    }
    
    auto subGraph = make_shared<CausalGraph>(subAdjMatrix);
    
    // Copy node states
    for (size_t i = 0; i < subsystem.size(); ++i) {
        shared_ptr<CausalState> origState = graph_->getNodeState(subsystem[i]);
        
        // Create a new state with appropriate size
        auto newState = make_shared<CausalState>(subsystem.size());
        
        // Copy values and probabilities if sizes match
        if (origState->getSize() == subsystem.size()) {
            newState->setValues(origState->getValues());
            newState->setProbabilities(origState->getProbabilities());
        }
        
        subGraph->setNodeState(i, newState);
    }
    
    // Calculate Phi for this subsystem
    CausalAnalyzer subAnalyzer(subGraph);
    return subAnalyzer.calculateIntegratedInformation();
}

bool PhiCalculator::isLocalMaximum(const vector<size_t>& subsystem, double phiValue) {
    // Check if adding or removing a single element decreases Phi
    
    // Check supersets (adding one element)
    for (size_t i = 0; i < systemSize_; ++i) {
        // Skip if i is already in the subsystem
        if (find(subsystem.begin(), subsystem.end(), i) != subsystem.end()) {
            continue;
        }
        
        // Add i to create a superset
        vector<size_t> superset = subsystem;
        superset.push_back(i);
        
        // Calculate Phi for the superset
        double supersetPhi = calculateSubsystemPhi(superset);
        
        // If superset has higher Phi, this is not a local maximum
        if (supersetPhi > phiValue) {
            return false;
        }
    }
    
    // Check subsets (removing one element)
    if (subsystem.size() > 1) {
        for (size_t i = 0; i < subsystem.size(); ++i) {
            // Create subset by removing element i
            vector<size_t> subset = subsystem;
            subset.erase(subset.begin() + i);
            
            // Calculate Phi for the subset
            double subsetPhi = calculateSubsystemPhi(subset);
            
            // If subset has higher Phi, this is not a local maximum
            if (subsetPhi > phiValue) {
                return false;
            }
        }
    }
    
    // If we get here, this is a local maximum
    return true;
}

vector<vector<size_t>> PhiCalculator::identifyCandidateSubsystems() {
    vector<vector<size_t>> candidates;
    
    // One approach: use strongly connected components
    MatrixXd adjMatrix = graph_->getAdjacencyMatrix();
    
    // Threshold adjacency matrix
    double threshold = adjMatrix.mean();
    MatrixXd thresholdedMatrix = (adjMatrix.array() > threshold).cast<double>();
    
    // Find connected components (simplified)
    vector<bool> visited(systemSize_, false);
    
    function<void(size_t, vector<size_t>&)> dfs = [&](size_t node, vector<size_t>& component) {
        visited[node] = true;
        component.push_back(node);
        
        for (size_t i = 0; i < systemSize_; ++i) {
            if (thresholdedMatrix(node, i) > 0 && !visited[i]) {
                dfs(i, component);
            }
        }
    };
    
    for (size_t i = 0; i < systemSize_; ++i) {
        if (!visited[i]) {
            vector<size_t> component;
            dfs(i, component);
            
            if (component.size() >= 2) {
                candidates.push_back(component);
            }
        }
    }
    
    // Another approach: use community detection based on modularity
    // This is a simplified version
    auto communities = detectCommunities();
    for (const auto& community : communities) {
        candidates.push_back(community);
    }
    
    return candidates;
}

vector<vector<size_t>> PhiCalculator::detectCommunities() {
    // This is a simplified implementation of the Louvain algorithm
    // for community detection
    
    MatrixXd adjMatrix = graph_->getAdjacencyMatrix();
    
    // Initialize each node in its own community
    vector<size_t> communities(systemSize_);
    for (size_t i = 0; i < systemSize_; ++i) {
        communities[i] = i;
    }
    
    // Calculate initial modularity
    double m = adjMatrix.sum();
    if (m < 1e-10) {
        // No significant connections
        return {{}}; // Return a single empty community
    }
    
    adjMatrix /= m; // Normalize
    
    bool improvement = true;
    const int maxIterations = 10;
    int iteration = 0;
    
    while (improvement && iteration < maxIterations) {
        improvement = false;
        ++iteration;
        
        // For each node, try moving it to each neighbor's community
        for (size_t i = 0; i < systemSize_; ++i) {
            size_t currentCommunity = communities[i];
            
            // Find neighboring communities
            set<size_t> neighborCommunities;
            for (size_t j = 0; j < systemSize_; ++j) {
                if (adjMatrix(i, j) > 0) {
                    neighborCommunities.insert(communities[j]);
                }
            }
            
            // Try moving to each neighboring community
            double bestGain = 0.0;
            size_t bestCommunity = currentCommunity;
            
            for (size_t community : neighborCommunities) {
                if (community == currentCommunity) {
                    continue;
                }
                
                // Calculate gain in modularity
                double gain = 0.0;
                
                // Calculate k_i (sum of edge weights connected to node i)
                double k_i = 0.0;
                for (size_t j = 0; j < systemSize_; ++j) {
                    k_i += adjMatrix(i, j);
                }
                
                // Calculate Σ_in (sum of edge weights within the community)
                double sigma_in = 0.0;
                for (size_t j = 0; j < systemSize_; ++j) {
                    if (communities[j] == community) {
                        for (size_t k = 0; k < systemSize_; ++k) {
                            if (communities[k] == community) {
                                sigma_in += adjMatrix(j, k);
                            }
                        }
                    }
                }
                
                // Calculate Σ_tot (sum of edge weights connected to nodes in the community)
                double sigma_tot = 0.0;
                for (size_t j = 0; j < systemSize_; ++j) {
                    if (communities[j] == community) {
                        for (size_t k = 0; k < systemSize_; ++k) {
                            sigma_tot += adjMatrix(j, k);
                        }
                    }
                }
                
                // Calculate k_i_in (sum of edge weights connecting i to community)
                double k_i_in = 0.0;
                for (size_t j = 0; j < systemSize_; ++j) {
                    if (communities[j] == community) {
                        k_i_in += adjMatrix(i, j);
                    }
                }
                
                // Apply the formula for modularity gain
                gain = (k_i_in - sigma_tot * k_i / m) / m;
                
                if (gain > bestGain) {
                    bestGain = gain;
                    bestCommunity = community;
                }
            }
            
            // If there's an improvement, move the node
            if (bestCommunity != currentCommunity) {
                communities[i] = bestCommunity;
                improvement = true;
            }
        }
    }
    
    // Convert communities to the expected format
    map<size_t, vector<size_t>> communityMap;
    for (size_t i = 0; i < systemSize_; ++i) {
        communityMap[communities[i]].push_back(i);
    }
    
    vector<vector<size_t>> result;
    for (const auto& [_, nodes] : communityMap) {
        if (nodes.size() >= 2) {
            result.push_back(nodes);
        }
    }
    
    return result;
}

} // namespace phi_calculator
} // namespace ultra