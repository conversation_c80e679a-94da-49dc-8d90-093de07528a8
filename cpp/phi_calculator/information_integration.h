/**
 * @file information_integration.h
 * @brief Core algorithms for measuring information integration in complex systems
 * 
 * This file provides implementations of various information integration measures
 * beyond the standard Phi calculations, including partial information decomposition,
 * synergy, redundancy, and other information-theoretic metrics for analyzing
 * integrated information in the ULTRA system.
 * 
 * <AUTHOR> Team
 * @date 2025
 */

#ifndef ULTRA_PHI_CALCULATOR_INFORMATION_INTEGRATION_H_
#define ULTRA_PHI_CALCULATOR_INFORMATION_INTEGRATION_H_

#include "causal_analysis.h"
#include "information_geometry.h"
#include "phi_computation.h"

#include <Eigen/Dense>
#include <memory>
#include <vector>
#include <unordered_map>
#include <functional>
#include <optional>
#include <tuple>
#include <set>

namespace ultra {
namespace phi_calculator {

// Forward declarations
class PhiContext;
class ProbabilityDistribution;
class CausalRepertoire;

/**
 * @brief Enumeration of information decomposition components
 */
enum class InfoComponent {
    UNIQUE,          ///< Unique information provided by a source
    REDUNDANT,       ///< Redundant information shared among sources
    SYNERGISTIC,     ///< Synergistic information requiring multiple sources
    TOTAL_MUTUAL,    ///< Total mutual information
    WHOLE_MINUS_SUM, ///< Integration as whole minus sum
    EXCESS_ENTROPY,  ///< Excess entropy (predictive information)
    BINDING,         ///< Information binding
    INTEGRATION,     ///< Information integration
    TRANSFER_ENTROPY ///< Transfer entropy (directed information flow)
};

/**
 * @brief Structure representing a lattice node in the redundancy lattice
 */
struct RedundancyLatticeNode {
    std::vector<std::set<size_t>> antichain;  ///< Antichain representing the node
    double value = 0.0;                      ///< Information value at this node
    std::vector<size_t> children;            ///< Indices of child nodes
    std::vector<size_t> parents;             ///< Indices of parent nodes
};

/**
 * @brief Structure for partial information decomposition results
 */
struct PIDResult {
    std::unordered_map<InfoComponent, double> components;  ///< Component values
    std::vector<RedundancyLatticeNode> lattice;           ///< Redundancy lattice
    double uniqueX = 0.0;                                ///< Unique information from X
    double uniqueY = 0.0;                                ///< Unique information from Y
    double redundancy = 0.0;                             ///< Redundant information
    double synergy = 0.0;                                ///< Synergistic information
    double totalInformation = 0.0;                       ///< Total mutual information
};

/**
 * @brief Class for partial information decomposition analysis
 * 
 * Implements algorithms for decomposing multivariate information into
 * unique, redundant, and synergistic components.
 */
class PartialInformationDecomposition {
public:
    /**
     * @brief Constructor
     * 
     * @param context Shared pointer to calculation context
     */
    explicit PartialInformationDecomposition(std::shared_ptr<PhiContext> context);
    
    /**
     * @brief Analyze PID for two sources and one target
     * 
     * @param targetIdx Target variable index
     * @param sourceXIdx First source variable index
     * @param sourceYIdx Second source variable index
     * @param state System state
     * @return PID result structure
     */
    PIDResult analyze(size_t targetIdx, 
                     size_t sourceXIdx, 
                     size_t sourceYIdx,
                     const std::shared_ptr<CausalState>& state);
    
    /**
     * @brief Analyze PID for multiple sources and one target
     * 
     * @param targetIdx Target variable index
     * @param sourceIndices Vector of source variable indices
     * @param state System state
     * @return PID result structure
     */
    PIDResult analyzeMultiSource(size_t targetIdx,
                               const std::vector<size_t>& sourceIndices,
                               const std::shared_ptr<CausalState>& state);
    
    /**
     * @brief Calculate redundancy using specific measure
     * 
     * @param target Target probability distribution
     * @param sources Vector of source probability distributions
     * @param state System state
     * @return Redundancy value
     */
    double calculateRedundancy(const ProbabilityDistribution& target,
                             const std::vector<ProbabilityDistribution>& sources,
                             const std::shared_ptr<CausalState>& state);
    
    /**
     * @brief Calculate minimum specific information redundancy
     * 
     * @param target Target probability distribution
     * @param sources Vector of source probability distributions
     * @param state System state
     * @return Redundancy value using minimum specific information
     */
    double calculateMinimumSpecificInfo(const ProbabilityDistribution& target,
                                      const std::vector<ProbabilityDistribution>& sources,
                                      const std::shared_ptr<CausalState>& state);
    
    /**
     * @brief Calculate IBROJA redundancy
     * 
     * @param target Target probability distribution
     * @param sources Vector of source probability distributions
     * @param state System state
     * @return Redundancy value using IBROJA method
     */
    double calculateIBROJA(const ProbabilityDistribution& target,
                         const std::vector<ProbabilityDistribution>& sources,
                         const std::shared_ptr<CausalState>& state);
    
    /**
     * @brief Calculate synergy using specific measure
     * 
     * @param target Target probability distribution
     * @param sources Vector of source probability distributions
     * @param state System state
     * @return Synergy value
     */
    double calculateSynergy(const ProbabilityDistribution& target,
                          const std::vector<ProbabilityDistribution>& sources,
                          const std::shared_ptr<CausalState>& state);
    
    /**
     * @brief Calculate unique information for a source
     * 
     * @param target Target probability distribution
     * @param source Source probability distribution
     * @param otherSources Vector of other source probability distributions
     * @param state System state
     * @return Unique information value
     */
    double calculateUniqueInformation(const ProbabilityDistribution& target,
                                    const ProbabilityDistribution& source,
                                    const std::vector<ProbabilityDistribution>& otherSources,
                                    const std::shared_ptr<CausalState>& state);
    
    /**
     * @brief Build redundancy lattice
     * 
     * @param sourceIndices Vector of source variable indices
     * @return Vector of lattice nodes
     */
    std::vector<RedundancyLatticeNode> buildRedundancyLattice(
        const std::vector<size_t>& sourceIndices);
    
    /**
     * @brief Calculate Möbius inversion on the redundancy lattice
     * 
     * @param lattice Redundancy lattice with redundancy values
     * @return Lattice with PID values
     */
    std::vector<RedundancyLatticeNode> calculateMobiusInversion(
        const std::vector<RedundancyLatticeNode>& lattice);
    
    /**
     * @brief Set redundancy measure
     * 
     * @param useIBROJA Whether to use IBROJA measure
     */
    void setRedundancyMeasure(bool useIBROJA);
    
    /**
     * @brief Get current redundancy measure
     * 
     * @return True if using IBROJA, false if using minimum specific information
     */
    bool isUsingIBROJA() const;
    
private:
    std::shared_ptr<PhiContext> context_;  ///< Calculation context
    bool useIBROJA_ = true;                ///< Whether to use IBROJA measure
    
    /**
     * @brief Check if one antichain is a subset of another
     * 
     * @param a1 First antichain
     * @param a2 Second antichain
     * @return True if a1 is a subset of a2
     */
    bool isSubsetAntichain(const std::vector<std::set<size_t>>& a1,
                          const std::vector<std::set<size_t>>& a2) const;
    
    /**
     * @brief Compare two antichains in the redundancy lattice
     * 
     * @param a1 First antichain
     * @param a2 Second antichain
     * @return True if a1 <= a2 in the lattice
     */
    bool compareAntichains(const std::vector<std::set<size_t>>& a1,
                          const std::vector<std::set<size_t>>& a2) const;
    
    /**
     * @brief Calculate joint probability distribution
     * 
     * @param distributions Vector of individual distributions
     * @return Joint probability distribution
     */
    ProbabilityDistribution calculateJointDistribution(
        const std::vector<ProbabilityDistribution>& distributions) const;
};

/**
 * @brief Class for calculating integrated information measures
 * 
 * Implements various measures of information integration beyond standard Phi
 */
class IntegratedInformationCalculator {
public:
    /**
     * @brief Constructor
     * 
     * @param context Shared pointer to calculation context
     */
    explicit IntegratedInformationCalculator(std::shared_ptr<PhiContext> context);
    
    /**
     * @brief Calculate whole-minus-sum integration
     * 
     * @param subsystem Vector of element indices
     * @param state System state
     * @return Whole-minus-sum integration value
     */
    double calculateWholeMinus(const std::vector<size_t>& subsystem,
                             const std::shared_ptr<CausalState>& state);
    
    /**
     * @brief Calculate interaction information
     * 
     * @param subsystem Vector of element indices
     * @param state System state
     * @return Interaction information value
     */
    double calculateInteractionInformation(const std::vector<size_t>& subsystem,
                                         const std::shared_ptr<CausalState>& state);
    
    /**
     * @brief Calculate binding information
     * 
     * @param subsystem Vector of element indices
     * @param state System state
     * @return Binding information value
     */
    double calculateBindingInformation(const std::vector<size_t>& subsystem,
                                     const std::shared_ptr<CausalState>& state);
    
    /**
     * @brief Calculate total correlation
     * 
     * @param subsystem Vector of element indices
     * @param state System state
     * @return Total correlation value
     */
    double calculateTotalCorrelation(const std::vector<size_t>& subsystem,
                                   const std::shared_ptr<CausalState>& state);
    
    /**
     * @brief Calculate dual total correlation
     * 
     * @param subsystem Vector of element indices
     * @param state System state
     * @return Dual total correlation value
     */
    double calculateDualTotalCorrelation(const std::vector<size_t>& subsystem,
                                       const std::shared_ptr<CausalState>& state);
    
    /**
     * @brief Calculate O-information (integration-segregation balance)
     * 
     * @param subsystem Vector of element indices
     * @param state System state
     * @return O-information value
     */
    double calculateOInformation(const std::vector<size_t>& subsystem,
                               const std::shared_ptr<CausalState>& state);
    
    /**
     * @brief Calculate S-information (integrated synergy)
     * 
     * @param subsystem Vector of element indices
     * @param state System state
     * @return S-information value
     */
    double calculateSInformation(const std::vector<size_t>& subsystem,
                               const std::shared_ptr<CausalState>& state);
    
    /**
     * @brief Calculate excess entropy (predictive information)
     * 
     * @param subsystem Vector of element indices
     * @param state System state
     * @param timeHorizon Time horizon for prediction
     * @return Excess entropy value
     */
    double calculateExcessEntropy(const std::vector<size_t>& subsystem,
                                const std::shared_ptr<CausalState>& state,
                                size_t timeHorizon);
    
    /**
     * @brief Calculate predictive information flow
     * 
     * @param sourceIdx Source element index
     * @param targetIdx Target element index
     * @param state System state
     * @param timeHorizon Time horizon for prediction
     * @return Predictive information flow value
     */
    double calculatePredictiveInfoFlow(size_t sourceIdx,
                                     size_t targetIdx,
                                     const std::shared_ptr<CausalState>& state,
                                     size_t timeHorizon);
    
    /**
     * @brief Calculate information bottleneck
     * 
     * @param sourceIdx Source element index
     * @param targetIdx Target element index
     * @param compressionLevel Compression level (0.0-1.0)
     * @param state System state
     * @return Information bottleneck value
     */
    double calculateInfoBottleneck(size_t sourceIdx,
                                 size_t targetIdx,
                                 double compressionLevel,
                                 const std::shared_ptr<CausalState>& state);
    
    /**
     * @brief Calculate active information storage
     * 
     * @param elementIdx Element index
     * @param state System state
     * @param timeHorizon Time horizon for history
     * @return Active information storage value
     */
    double calculateActiveInfoStorage(size_t elementIdx,
                                    const std::shared_ptr<CausalState>& state,
                                    size_t timeHorizon);
    
    /**
     * @brief Calculate transfer entropy
     * 
     * @param sourceIdx Source element index
     * @param targetIdx Target element index
     * @param state System state
     * @param timeHorizon Time horizon for history
     * @return Transfer entropy value
     */
    double calculateTransferEntropy(size_t sourceIdx,
                                  size_t targetIdx,
                                  const std::shared_ptr<CausalState>& state,
                                  size_t timeHorizon);
    
    /**
     * @brief Calculate directed information
     * 
     * @param sourceIdx Source element index
     * @param targetIdx Target element index
     * @param state System state
     * @param timeHorizon Time horizon for history
     * @return Directed information value
     */
    double calculateDirectedInformation(size_t sourceIdx,
                                      size_t targetIdx,
                                      const std::shared_ptr<CausalState>& state,
                                      size_t timeHorizon);
    
    /**
     * @brief Calculate separable information
     * 
     * @param subsystem Vector of element indices
     * @param state System state
     * @return Separable information value
     */
    double calculateSeparableInformation(const std::vector<size_t>& subsystem,
                                      const std::shared_ptr<CausalState>& state);
    
    /**
     * @brief Calculate information theoretic causality
     * 
     * @param sourceIdx Source element index
     * @param targetIdx Target element index
     * @param state System state
     * @param timeHorizon Time horizon for history
     * @return Causal strength value
     */
    double calculateInfoTheoreticCausality(size_t sourceIdx,
                                         size_t targetIdx,
                                         const std::shared_ptr<CausalState>& state,
                                         size_t timeHorizon);
    
private:
    std::shared_ptr<PhiContext> context_;                ///< Calculation context
    std::shared_ptr<CausalGraph> graph_;                 ///< Causal graph
    std::unordered_map<std::string, double> resultCache_; ///< Cache for results
    
    /**
     * @brief Calculate entropy of a subsystem
     * 
     * @param subsystem Vector of element indices
     * @param state System state
     * @return Entropy value
     */
    double calculateEntropy(const std::vector<size_t>& subsystem,
                          const std::shared_ptr<CausalState>& state);
    
    /**
     * @brief Calculate joint entropy of multiple subsystems
     * 
     * @param subsystems Vector of subsystems
     * @param state System state
     * @return Joint entropy value
     */
    double calculateJointEntropy(const std::vector<std::vector<size_t>>& subsystems,
                               const std::shared_ptr<CausalState>& state);
    
    /**
     * @brief Calculate conditional entropy
     * 
     * @param subsystem1 First subsystem
     * @param subsystem2 Second subsystem
     * @param state System state
     * @return Conditional entropy value
     */
    double calculateConditionalEntropy(const std::vector<size_t>& subsystem1,
                                     const std::vector<size_t>& subsystem2,
                                     const std::shared_ptr<CausalState>& state);
    
    /**
     * @brief Calculate mutual information
     * 
     * @param subsystem1 First subsystem
     * @param subsystem2 Second subsystem
     * @param state System state
     * @return Mutual information value
     */
    double calculateMutualInformation(const std::vector<size_t>& subsystem1,
                                    const std::vector<size_t>& subsystem2,
                                    const std::shared_ptr<CausalState>& state);
    
    /**
     * @brief Calculate conditional mutual information
     * 
     * @param subsystem1 First subsystem
     * @param subsystem2 Second subsystem
     * @param conditioning Conditioning subsystem
     * @param state System state
     * @return Conditional mutual information value
     */
    double calculateConditionalMutualInformation(const std::vector<size_t>& subsystem1,
                                              const std::vector<size_t>& subsystem2,
                                              const std::vector<size_t>& conditioning,
                                              const std::shared_ptr<CausalState>& state);
    
    /**
     * @brief Generate cache key for calculations
     * 
     * @param type Calculation type
     * @param elements Vector of element indices
     * @param state System state
     * @return Cache key string
     */
    std::string generateCacheKey(const std::string& type,
                              const std::vector<size_t>& elements,
                              const std::shared_ptr<CausalState>& state) const;
    
    /**
     * @brief Look up cached value
     * 
     * @param key Cache key
     * @return Optional cached value
     */
    std::optional<double> getCachedValue(const std::string& key) const;
    
    /**
     * @brief Store value in cache
     * 
     * @param key Cache key
     * @param value Value to cache
     */
    void setCachedValue(const std::string& key, double value);
};

/**
 * @brief Class for temporally extended information measures
 * 
 * Implements information-theoretic measures that span multiple time steps
 */
class TemporalInformationCalculator {
public:
    /**
     * @brief Constructor
     * 
     * @param context Shared pointer to calculation context
     */
    explicit TemporalInformationCalculator(std::shared_ptr<PhiContext> context);
    
    /**
     * @brief Calculate temporal Phi for a subsystem
     * 
     * @param subsystem Vector of element indices
     * @param states Vector of system states over time
     * @param tau Time delay
     * @return Temporal Phi value
     */
    double calculateTemporalPhi(const std::vector<size_t>& subsystem,
                              const std::vector<std::shared_ptr<CausalState>>& states,
                              size_t tau = 1);
    
    /**
     * @brief Calculate information integration across time
     * 
     * @param subsystem Vector of element indices
     * @param states Vector of system states over time
     * @param tau Time delay
     * @return Temporal integration value
     */
    double calculateTemporalIntegration(const std::vector<size_t>& subsystem,
                                      const std::vector<std::shared_ptr<CausalState>>& states,
                                      size_t tau = 1);
    
    /**
     * @brief Calculate causal emergence across time
     * 
     * @param subsystem Vector of element indices
     * @param states Vector of system states over time
     * @param tau Time delay
     * @return Causal emergence value
     */
    double calculateCausalEmergence(const std::vector<size_t>& subsystem,
                                  const std::vector<std::shared_ptr<CausalState>>& states,
                                  size_t tau = 1);
    
    /**
     * @brief Calculate integrated information flow
     * 
     * @param sourceSubsystem Source subsystem
     * @param targetSubsystem Target subsystem
     * @param states Vector of system states over time
     * @param tau Time delay
     * @return Integrated information flow value
     */
    double calculateIntegratedInfoFlow(const std::vector<size_t>& sourceSubsystem,
                                     const std::vector<size_t>& targetSubsystem,
                                     const std::vector<std::shared_ptr<CausalState>>& states,
                                     size_t tau = 1);
    
    /**
     * @brief Calculate effective information across time
     * 
     * @param subsystem Vector of element indices
     * @param states Vector of system states over time
     * @param tau Time delay
     * @return Effective information value
     */
    double calculateTemporalEffectiveInfo(const std::vector<size_t>& subsystem,
                                        const std::vector<std::shared_ptr<CausalState>>& states,
                                        size_t tau = 1);
    
    /**
     * @brief Calculate predictive information
     * 
     * @param subsystem Vector of element indices
     * @param states Vector of system states over time
     * @param pastHorizon Past time horizon
     * @param futureHorizon Future time horizon
     * @return Predictive information value
     */
    double calculatePredictiveInformation(const std::vector<size_t>& subsystem,
                                        const std::vector<std::shared_ptr<CausalState>>& states,
                                        size_t pastHorizon,
                                        size_t futureHorizon);
    
    /**
     * @brief Calculate statistical complexity
     * 
     * @param subsystem Vector of element indices
     * @param states Vector of system states over time
     * @return Statistical complexity value
     */
    double calculateStatisticalComplexity(const std::vector<size_t>& subsystem,
                                        const std::vector<std::shared_ptr<CausalState>>& states);
    
    /**
     * @brief Calculate information dynamics
     * 
     * @param subsystem Vector of element indices
     * @param states Vector of system states over time
     * @return Tuple of storage, transfer, and modification
     */
    std::tuple<double, double, double> calculateInfoDynamics(
        const std::vector<size_t>& subsystem,
        const std::vector<std::shared_ptr<CausalState>>& states);
    
    /**
     * @brief Calculate dynamic integration
     * 
     * @param subsystem Vector of element indices
     * @param states Vector of system states over time
     * @param windowSize Window size for dynamic calculation
     * @return Vector of integration values over time
     */
    std::vector<double> calculateDynamicIntegration(
        const std::vector<size_t>& subsystem,
        const std::vector<std::shared_ptr<CausalState>>& states,
        size_t windowSize);
    
    /**
     * @brief Calculate wavelet-based integration
     * 
     * @param subsystem Vector of element indices
     * @param states Vector of system states over time
     * @param scales Vector of time scales to analyze
     * @return Matrix of integration values (time x scale)
     */
    Eigen::MatrixXd calculateWaveletIntegration(
        const std::vector<size_t>& subsystem,
        const std::vector<std::shared_ptr<CausalState>>& states,
        const std::vector<double>& scales);
    
private:
    std::shared_ptr<PhiContext> context_;  ///< Calculation context
    
    /**
     * @brief Get state at a specific time
     * 
     * @param states Vector of system states over time
     * @param timeIndex Time index
     * @return State at that time or nullptr if out of range
     */
    std::shared_ptr<CausalState> getStateAtTime(
        const std::vector<std::shared_ptr<CausalState>>& states,
        int timeIndex) const;
    
    /**
     * @brief Calculate transition probability
     * 
     * @param sourceState Source state
     * @param targetState Target state
     * @param subsystem Subsystem elements
     * @return Transition probability
     */
    double calculateTransitionProbability(
        const std::shared_ptr<CausalState>& sourceState,
        const std::shared_ptr<CausalState>& targetState,
        const std::vector<size_t>& subsystem) const;
    
    /**
     * @brief Build transition matrix
     * 
     * @param subsystem Subsystem elements
     * @param states Vector of system states over time
     * @return Transition probability matrix
     */
    Eigen::MatrixXd buildTransitionMatrix(
        const std::vector<size_t>& subsystem,
        const std::vector<std::shared_ptr<CausalState>>& states) const;
};

/**
 * @brief Class for multi-modal information integration
 * 
 * Implements information integration measures across different modalities or subsystems
 */
class MultimodalIntegrationCalculator {
public:
    /**
     * @brief Constructor
     * 
     * @param context Shared pointer to calculation context
     */
    explicit MultimodalIntegrationCalculator(std::shared_ptr<PhiContext> context);
    
    /**
     * @brief Calculate integration between subsystems
     * 
     * @param subsystem1 First subsystem
     * @param subsystem2 Second subsystem
     * @param state System state
     * @return Integration value
     */
    double calculateIntegrationBetween(
        const std::vector<size_t>& subsystem1,
        const std::vector<size_t>& subsystem2,
        const std::shared_ptr<CausalState>& state);
    
    /**
     * @brief Calculate integration among multiple subsystems
     * 
     * @param subsystems Vector of subsystems
     * @param state System state
     * @return Integration value
     */
    double calculateMultiSubsystemIntegration(
        const std::vector<std::vector<size_t>>& subsystems,
        const std::shared_ptr<CausalState>& state);
    
    /**
     * @brief Calculate integration-segregation balance
     * 
     * @param subsystems Vector of subsystems
     * @param state System state
     * @return Balance value (-1 to 1, negative = segregation dominant)
     */
    double calculateIntegrationSegregationBalance(
        const std::vector<std::vector<size_t>>& subsystems,
        const std::shared_ptr<CausalState>& state);
    
    /**
     * @brief Calculate integration hierarchy
     * 
     * @param subsystems Vector of subsystems at different levels
     * @param state System state
     * @return Vector of integration values at each level
     */
    std::vector<double> calculateIntegrationHierarchy(
        const std::vector<std::vector<std::vector<size_t>>>& subsystems,
        const std::shared_ptr<CausalState>& state);
    
    /**
     * @brief Calculate integration complexity
     * 
     * @param subsystem Vector of element indices
     * @param state System state
     * @return Integration complexity value
     */
    double calculateIntegrationComplexity(
        const std::vector<size_t>& subsystem,
        const std::shared_ptr<CausalState>& state);
    
    /**
     * @brief Calculate information integration profile
     * 
     * @param subsystem Vector of element indices
     * @param state System state
     * @param numScales Number of scales to analyze
     * @return Vector of integration values at different scales
     */
    std::vector<double> calculateIntegrationProfile(
        const std::vector<size_t>& subsystem,
        const std::shared_ptr<CausalState>& state,
        size_t numScales);
    
    /**
     * @brief Calculate cross-modal binding
     * 
     * @param modalitySubsystems Vector of subsystems representing modalities
     * @param state System state
     * @return Binding value
     */
    double calculateCrossModalBinding(
        const std::vector<std::vector<size_t>>& modalitySubsystems,
        const std::shared_ptr<CausalState>& state);
    
    /**
     * @brief Calculate modality dominance
     * 
     * @param modalitySubsystems Vector of subsystems representing modalities
     * @param state System state
     * @return Vector of dominance values for each modality
     */
    std::vector<double> calculateModalityDominance(
        const std::vector<std::vector<size_t>>& modalitySubsystems,
        const std::shared_ptr<CausalState>& state);
    
    /**
     * @brief Calculate nested integration
     * 
     * @param subsystem Vector of element indices
     * @param state System state
     * @param maxDepth Maximum recursion depth
     * @return Nested integration value
     */
    double calculateNestedIntegration(
        const std::vector<size_t>& subsystem,
        const std::shared_ptr<CausalState>& state,
        size_t maxDepth);
    
private:
    std::shared_ptr<PhiContext> context_;                ///< Calculation context
    std::shared_ptr<CausalGraph> graph_;                 ///< Causal graph
    std::shared_ptr<IntegratedInformationCalculator> infoCalc_; ///< Info calculator
    
    /**
     * @brief Calculate modularity of a subsystem
     * 
     * @param subsystem Vector of element indices
     * @param state System state
     * @return Modularity value
     */
    double calculateModularity(
        const std::vector<size_t>& subsystem,
        const std::shared_ptr<CausalState>& state);
    
    /**
     * @brief Find optimal decomposition
     * 
     * @param subsystem Vector of element indices
     * @param state System state
     * @param numParts Number of parts
     * @return Optimal partition
     */
    Partition findOptimalDecomposition(
        const std::vector<size_t>& subsystem,
        const std::shared_ptr<CausalState>& state,
        size_t numParts);
};

/**
 * @brief Manager for information integration analysis
 * 
 * Provides a unified interface for all information integration measures
 */
class InformationIntegrationManager {
public:
    /**
     * @brief Constructor
     * 
     * @param context Shared pointer to calculation context
     */
    explicit InformationIntegrationManager(std::shared_ptr<PhiContext> context);
    
    /**
     * @brief Get partial information decomposition analyzer
     * 
     * @return Reference to PID analyzer
     */
    PartialInformationDecomposition& getPID();
    
    /**
     * @brief Get integrated information calculator
     * 
     * @return Reference to information calculator
     */
    IntegratedInformationCalculator& getInfoCalculator();
    
    /**
     * @brief Get temporal information calculator
     * 
     * @return Reference to temporal calculator
     */
    TemporalInformationCalculator& getTemporalCalculator();
    
    /**
     * @brief Get multimodal integration calculator
     * 
     * @return Reference to multimodal calculator
     */
    MultimodalIntegrationCalculator& getMultimodalCalculator();
    
    /**
     * @brief Calculate comprehensive integration profile
     * 
     * @param subsystem Vector of element indices
     * @param state System state
     * @return Map of measure names to values
     */
    std::unordered_map<std::string, double> calculateIntegrationProfile(
        const std::vector<size_t>& subsystem,
        const std::shared_ptr<CausalState>& state);
    
    /**
     * @brief Calculate integration between subsystems
     * 
     * @param subsystems Vector of subsystems
     * @param state System state
     * @return Matrix of integration values between subsystems
     */
    Eigen::MatrixXd calculateIntegrationMatrix(
        const std::vector<std::vector<size_t>>& subsystems,
        const std::shared_ptr<CausalState>& state);
    
    /**
     * @brief Calculate top integrated groups
     * 
     * @param state System state
     * @param maxGroups Maximum number of groups to return
     * @param minSize Minimum group size
     * @param maxSize Maximum group size
     * @return Vector of (subsystem, integration value) pairs
     */
    std::vector<std::pair<std::vector<size_t>, double>> findTopIntegratedGroups(
        const std::shared_ptr<CausalState>& state,
        size_t maxGroups = 5,
        size_t minSize = 2,
        size_t maxSize = 0);
    
    /**
     * @brief Generate integration report
     * 
     * @param subsystem Vector of element indices
     * @param state System state
     * @param format Report format ("text", "json", "xml")
     * @return Report string
     */
    std::string generateIntegrationReport(
        const std::vector<size_t>& subsystem,
        const std::shared_ptr<CausalState>& state,
        const std::string& format = "text");
    
    /**
     * @brief Calculate integration compared to baseline
     * 
     * @param subsystem Vector of element indices
     * @param state System state
     * @param baselineType Type of baseline ("random", "shuffle", "independent")
     * @param numSamples Number of samples for baseline
     * @return Normalized integration value
     */
    double calculateNormalizedIntegration(
        const std::vector<size_t>& subsystem,
        const std::shared_ptr<CausalState>& state,
        const std::string& baselineType = "independent",
        size_t numSamples = 100);
    
private:
    std::shared_ptr<PhiContext> context_;                          ///< Calculation context
    std::unique_ptr<PartialInformationDecomposition> pid_;         ///< PID analyzer
    std::unique_ptr<IntegratedInformationCalculator> infoCalc_;    ///< Info calculator
    std::unique_ptr<TemporalInformationCalculator> tempCalc_;      ///< Temporal calculator
    std::unique_ptr<MultimodalIntegrationCalculator> multiCalc_;   ///< Multimodal calculator
    
    /**
     * @brief Generate random state for baseline
     * 
     * @param subsystem Vector of element indices
     * @param type Baseline type
     * @param originalState Original system state
     * @return Random state
     */
    std::shared_ptr<CausalState> generateBaselineState(
        const std::vector<size_t>& subsystem,
        const std::string& type,
        const std::shared_ptr<CausalState>& originalState);
    
    /**
     * @brief Generate text report
     * 
     * @param subsystem Vector of element indices
     * @param state System state
     * @param measures Map of measure names to values
     * @return Text report
     */
    std::string generateTextReport(
        const std::vector<size_t>& subsystem,
        const std::shared_ptr<CausalState>& state,
        const std::unordered_map<std::string, double>& measures);
    
    /**
     * @brief Generate JSON report
     * 
     * @param subsystem Vector of element indices
     * @param state System state
     * @param measures Map of measure names to values
     * @return JSON report
     */
    std::string generateJsonReport(
        const std::vector<size_t>& subsystem,
        const std::shared_ptr<CausalState>& state,
        const std::unordered_map<std::string, double>& measures);
    
    /**
     * @brief Generate XML report
     * 
     * @param subsystem Vector of element indices
     * @param state System state
     * @param measures Map of measure names to values
     * @return XML report
     */
    std::string generateXmlReport(
        const std::vector<size_t>& subsystem,
        const std::shared_ptr<CausalState>& state,
        const std::unordered_map<std::string, double>& measures);
};

} // namespace phi_calculator
} // namespace ultra

#endif // ULTRA_PHI_CALCULATOR_INFORMATION_INTEGRATION_H_