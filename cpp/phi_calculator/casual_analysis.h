/**
 * @file causal_analysis.h
 * @brief Header for causal analysis algorithms for integrated information theory
 * 
 * This file declares causal analysis mechanisms based on Integrated Information
 * Theory (IIT) for measuring and analyzing cause-effect structures within the ULTRA
 * system. It defines classes for calculating causal relationships, effective information,
 * and minimum information partitions used in Phi (Φ) calculation - the central measure of
 * integrated information and consciousness in the system.
 * 
 * <AUTHOR> Team
 * @date 2025
 */

#ifndef ULTRA_PHI_CALCULATOR_CAUSAL_ANALYSIS_H_
#define ULTRA_PHI_CALCULATOR_CAUSAL_ANALYSIS_H_

#include <Eigen/Dense>
#include <map>
#include <memory>
#include <vector>
#include <utility>

namespace ultra {
namespace phi_calculator {

// Type definitions for commonly used structures
using Bipartition = std::pair<std::vector<size_t>, std::vector<size_t>>;
using Partition = std::vector<std::vector<size_t>>;
using CausalPath = std::vector<size_t>;
using PhiComplexes = std::vector<std::pair<std::vector<size_t>, double>>;

/**
 * @brief Represents a causal state of a node or system
 * 
 * A causal state encompasses both the values (activations) and
 * probability distribution over those values
 */
class CausalState {
public:
    /**
     * @brief Constructor with size
     * 
     * @param size Size of the causal state vector
     */
    explicit CausalState(size_t size);
    
    /**
     * @brief Constructor with values
     * 
     * @param values Vector of state values
     */
    explicit CausalState(const Eigen::VectorXd& values);
    
    /**
     * @brief Constructor with values and probabilities
     * 
     * @param values Vector of state values
     * @param probabilities Vector of state probabilities
     */
    CausalState(const Eigen::VectorXd& values, const Eigen::VectorXd& probabilities);
    
    /**
     * @brief Get size of the state vector
     * 
     * @return Size of state vector
     */
    size_t getSize() const;
    
    /**
     * @brief Get state values
     * 
     * @return Vector of state values
     */
    const Eigen::VectorXd& getValues() const;
    
    /**
     * @brief Get state probabilities
     * 
     * @return Vector of state probabilities
     */
    const Eigen::VectorXd& getProbabilities() const;
    
    /**
     * @brief Set state values
     * 
     * @param values New state values
     */
    void setValues(const Eigen::VectorXd& values);
    
    /**
     * @brief Set state probabilities
     * 
     * @param probabilities New state probabilities
     */
    void setProbabilities(const Eigen::VectorXd& probabilities);
    
    /**
     * @brief Add random perturbation to state
     * 
     * @param amount Magnitude of perturbation
     */
    void perturb(double amount);
    
    /**
     * @brief Sample from the state distribution
     * 
     * @param numSamples Number of samples to generate
     * @return Vector of sampled probabilities
     */
    Eigen::VectorXd sample(size_t numSamples) const;
    
    /**
     * @brief Calculate distance between this state and another
     * 
     * @param other Other causal state to compare with
     * @return Distance metric value
     */
    double distance(const CausalState& other) const;
    
    /**
     * @brief Calculate entropy of the state
     * 
     * @return Entropy value
     */
    double entropy() const;
    
private:
    size_t size_;                     ///< Size of the state vector
    Eigen::VectorXd values_;          ///< Vector of state values
    Eigen::VectorXd probabilities_;   ///< Probability distribution over state values
};

/**
 * @brief Represents a causal link between nodes
 * 
 * A causal link defines a directed connection with a strength and type
 */
class CausalLink {
public:
    /**
     * @brief Link type enumeration
     */
    enum class LinkType {
        EXCITATORY,     ///< Positive causal influence
        INHIBITORY,     ///< Negative causal influence
        BIDIRECTIONAL   ///< Bidirectional influence
    };
    
    /**
     * @brief Constructor
     * 
     * @param sourceIndex Index of source node
     * @param targetIndex Index of target node
     * @param strength Link strength
     * @param type Link type
     */
    CausalLink(size_t sourceIndex, size_t targetIndex, 
               double strength = 1.0, 
               LinkType type = LinkType::EXCITATORY);
    
    /**
     * @brief Get source node index
     * 
     * @return Source node index
     */
    size_t getSourceIndex() const;
    
    /**
     * @brief Get target node index
     * 
     * @return Target node index
     */
    size_t getTargetIndex() const;
    
    /**
     * @brief Get link strength
     * 
     * @return Link strength value
     */
    double getStrength() const;
    
    /**
     * @brief Get link type
     * 
     * @return Link type enum value
     */
    LinkType getType() const;
    
    /**
     * @brief Set link strength
     * 
     * @param strength New link strength
     */
    void setStrength(double strength);
    
    /**
     * @brief Set link type
     * 
     * @param type New link type
     */
    void setType(LinkType type);
    
private:
    size_t sourceIndex_;  ///< Index of source node
    size_t targetIndex_;  ///< Index of target node
    double strength_;     ///< Link strength
    LinkType type_;       ///< Link type
};

/**
 * @brief Represents a causal graph of interacting nodes
 * 
 * A causal graph defines a network of nodes with causal links between them
 */
class CausalGraph {
public:
    /**
     * @brief Constructor with size
     * 
     * @param numNodes Number of nodes in the graph
     */
    explicit CausalGraph(size_t numNodes);
    
    /**
     * @brief Constructor with adjacency matrix
     * 
     * @param adjacencyMatrix Adjacency matrix defining connections
     */
    explicit CausalGraph(const Eigen::MatrixXd& adjacencyMatrix);
    
    /**
     * @brief Get number of nodes
     * 
     * @return Number of nodes in the graph
     */
    size_t getNumNodes() const;
    
    /**
     * @brief Get adjacency matrix
     * 
     * @return Adjacency matrix of connections
     */
    const Eigen::MatrixXd& getAdjacencyMatrix() const;
    
    /**
     * @brief Get all causal links
     * 
     * @return Vector of causal links
     */
    const std::vector<CausalLink>& getLinks() const;
    
    /**
     * @brief Add a causal link
     * 
     * @param source Source node index
     * @param target Target node index
     * @param strength Link strength
     * @param type Link type
     */
    void addLink(size_t source, size_t target, double strength = 1.0, 
                CausalLink::LinkType type = CausalLink::LinkType::EXCITATORY);
    
    /**
     * @brief Remove a causal link
     * 
     * @param source Source node index
     * @param target Target node index
     */
    void removeLink(size_t source, size_t target);
    
    /**
     * @brief Set state for a node
     * 
     * @param index Node index
     * @param state Causal state to assign
     */
    void setNodeState(size_t index, std::shared_ptr<CausalState> state);
    
    /**
     * @brief Get state of a node
     * 
     * @param index Node index
     * @return Shared pointer to causal state
     */
    std::shared_ptr<CausalState> getNodeState(size_t index) const;
    
    /**
     * @brief Get all incoming connections to a node
     * 
     * @param nodeIndex Node index
     * @return Vector of indices for connected nodes
     */
    std::vector<size_t> getIncomingConnections(size_t nodeIndex) const;
    
    /**
     * @brief Get all outgoing connections from a node
     * 
     * @param nodeIndex Node index
     * @return Vector of indices for connected nodes
     */
    std::vector<size_t> getOutgoingConnections(size_t nodeIndex) const;
    
    /**
     * @brief Propagate state through the causal graph
     * 
     * @param state Initial state vector
     * @return Resulting state after propagation
     */
    Eigen::VectorXd propagateState(const Eigen::VectorXd& state) const;
    
    /**
     * @brief Compute transfer entropy between all node pairs
     * 
     * @return Matrix of transfer entropy values
     */
    Eigen::MatrixXd computeTransferEntropy() const;
    
    /**
     * @brief Calculate transfer entropy between two node states
     * 
     * @param source Source state vector
     * @param target Target state vector
     * @return Transfer entropy value
     */
    double calculateTransferEntropy(const Eigen::VectorXd& source, 
                                   const Eigen::VectorXd& target) const;
    
private:
    size_t numNodes_;                             ///< Number of nodes in graph
    Eigen::MatrixXd adjacencyMatrix_;             ///< Adjacency matrix of connections
    std::vector<CausalLink> links_;               ///< List of causal links
    std::vector<std::shared_ptr<CausalState>> nodes_;        ///< Node definitions
    std::vector<std::shared_ptr<CausalState>> nodeStates_;   ///< Current node states
};

/**
 * @brief Analyzer for causal relationships and integrated information
 * 
 * This class analyzes causal graphs to calculate effective information
 * and integrated information (Phi)
 */
class CausalAnalyzer {
public:
    /**
     * @brief Constructor
     * 
     * @param graph Shared pointer to causal graph
     */
    explicit CausalAnalyzer(std::shared_ptr<CausalGraph> graph);
    
    /**
     * @brief Calculate effective information
     * 
     * @return Effective information value
     */
    double calculateEffectiveInformation();
    
    /**
     * @brief Calculate joint entropy of multiple states
     * 
     * @param states Vector of causal states
     * @return Joint entropy value
     */
    double calculateJointEntropy(const std::vector<std::shared_ptr<CausalState>>& states) const;
    
    /**
     * @brief Calculate integrated information (Phi)
     * 
     * @return Integrated information value
     */
    double calculateIntegratedInformation();
    
    /**
     * @brief Find the minimum information partition
     * 
     * @return Partition that minimizes integrated information
     */
    Partition findMinimumInformationPartition();
    
    /**
     * @brief Calculate effective information for a partition
     * 
     * @param partition System partition
     * @return Effective information value
     */
    double calculatePartitionEffectiveInformation(const Partition& partition) const;
    
    /**
     * @brief Calculate normalization factor for a partition
     * 
     * @param partition System partition
     * @return Normalization factor
     */
    double calculateNormalizationFactor(const Partition& partition) const;
    
    /**
     * @brief Get calculated effective information
     * 
     * @return Effective information value
     */
    double getEffectiveInformation() const;
    
    /**
     * @brief Get calculated integrated information
     * 
     * @return Integrated information value
     */
    double getIntegratedInformation() const;
    
    /**
     * @brief Calculate contribution of each node to integrated information
     * 
     * @return Vector of node contributions
     */
    std::vector<double> calculateNodeContributions() const;
    
    /**
     * @brief Find all causal paths between two nodes
     * 
     * @param sourceIdx Source node index
     * @param targetIdx Target node index
     * @param maxLength Maximum path length
     * @return Vector of causal paths
     */
    std::vector<CausalPath> findCausalPaths(size_t sourceIdx, size_t targetIdx, 
                                          size_t maxLength = 5) const;
    
    /**
     * @brief Calculate causal influence of each node
     * 
     * @return Map of node indices to influence values
     */
    std::map<size_t, double> calculateCausalInfluence() const;
    
private:
    std::shared_ptr<CausalGraph> graph_;  ///< Causal graph being analyzed
    size_t numNodes_;                     ///< Number of nodes in the graph
    double effectiveInformation_;         ///< Calculated effective information
    double integratedInformation_;        ///< Calculated integrated information
};

/**
 * @brief Calculator for integrated information (Phi)
 * 
 * This class provides the main interface for calculating Phi and
 * identifying phi-complexes within the system
 */
class PhiCalculator {
public:
    /**
     * @brief Constructor
     */
    PhiCalculator();
    
    /**
     * @brief Initialize calculator with system size
     * 
     * @param systemSize Number of elements in the system
     */
    void initialize(size_t systemSize);
    
    /**
     * @brief Set causal graph
     * 
     * @param graph Shared pointer to causal graph
     */
    void setGraph(std::shared_ptr<CausalGraph> graph);
    
    /**
     * @brief Get causal graph
     * 
     * @return Shared pointer to causal graph
     */
    std::shared_ptr<CausalGraph> getGraph() const;
    
    /**
     * @brief Calculate Phi (integrated information)
     * 
     * @return Phi value
     */
    double calculatePhi();
    
    /**
     * @brief Calculate Phi spectrum across subsystems
     * 
     * @return Vector of Phi values for different scales
     */
    std::vector<double> calculatePhiSpectrum();
    
    /**
     * @brief Calculate Phi values for multiple subsystems
     * 
     * @param subsystems Vector of subsystem node indices
     * @return Vector of Phi values
     */
    std::vector<double> calculateSubsystemPhiValues(
        const std::vector<std::vector<size_t>>& subsystems);
    
    /**
     * @brief Calculate node contributions to Phi
     * 
     * @return Map of node indices to contribution values
     */
    std::map<size_t, double> calculateNodePhiContributions();
    
    /**
     * @brief Identify Phi-complexes in the system
     * 
     * @return Vector of complexes with their Phi values
     */
    PhiComplexes identifyComplexes();
    
    /**
     * @brief Calculate Phi for a subsystem
     * 
     * @param subsystem Vector of node indices
     * @return Phi value
     */
    double calculateSubsystemPhi(const std::vector<size_t>& subsystem);
    
    /**
     * @brief Check if a subsystem is a local maximum in Phi
     * 
     * @param subsystem Vector of node indices
     * @param phiValue Phi value for this subsystem
     * @return True if this is a local maximum
     */
    bool isLocalMaximum(const std::vector<size_t>& subsystem, double phiValue);
    
    /**
     * @brief Identify candidate subsystems for Phi analysis
     * 
     * @return Vector of candidate subsystems
     */
    std::vector<std::vector<size_t>> identifyCandidateSubsystems();
    
    /**
     * @brief Detect communities in the causal graph
     * 
     * @return Vector of community node indices
     */
    std::vector<std::vector<size_t>> detectCommunities();
    
private:
    bool isInitialized_;                       ///< Whether calculator is initialized
    size_t systemSize_;                         ///< Size of the system
    std::shared_ptr<CausalGraph> graph_;        ///< Causal graph
    std::shared_ptr<CausalAnalyzer> analyzer_;  ///< Causal analyzer
};

} // namespace phi_calculator
} // namespace ultra

#endif // ULTRA_PHI_CALCULATOR_CAUSAL_ANALYSIS_H_