cmake_minimum_required(VERSION 3.15)

###############################################################################
# phi_calculator CMakeLists.txt
#
# This file configures the build for the ULTRA Phi Calculator component, which
# implements integrated information theory (IIT) calculations and causal analysis.
###############################################################################

# Find Boost components if not already found
if(NOT Boost_FOUND)
    find_package(Boost REQUIRED COMPONENTS math graph)
    if(Boost_FOUND)
        message(STATUS "Found Boost: ${Boost_VERSION}")
    else()
        message(FATAL_ERROR "Boost libraries not found. Please install Boost.")
    endif()
endif()

# Define the phi_calculator library
set(PHI_CALCULATOR_SOURCES
    causal_analysis.cpp
    phi_computation.cpp
    information_geometry.cpp
    partition_search.cpp
    consciousness_measures.cpp
    causal_repertoire.cpp
)

# Define the phi_calculator headers
set(PHI_CALCULATOR_HEADERS
    causal_analysis.h
    phi_computation.h
    information_geometry.h
    partition_search.h
    consciousness_measures.h
    causal_repertoire.h
)

# Create the phi_calculator library
add_library(phi_calculator STATIC 
    ${PHI_CALCULATOR_SOURCES}
    ${PHI_CALCULATOR_HEADERS}
)

# Set include directories
target_include_directories(phi_calculator PUBLIC
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_CURRENT_BINARY_DIR}
    ${ULTRA_INCLUDE_DIR}
    ${Boost_INCLUDE_DIRS}  # Added Boost include directories
)

# Set compiler features
target_compile_features(phi_calculator PUBLIC cxx_std_17)

# Compiler-specific options
if(CMAKE_CXX_COMPILER_ID MATCHES "GNU|Clang")
    target_compile_options(phi_calculator PRIVATE 
        -Wall 
        -Wextra 
        -Wpedantic
        -Wno-unused-parameter
    )
elseif(MSVC)
    target_compile_options(phi_calculator PRIVATE 
        /W4 
        /MP 
        /EHsc
        /wd4100 # Suppress unused parameter warnings
    )
endif()

# Dependencies
target_link_libraries(phi_calculator PUBLIC
    utils
    Eigen3::Eigen
    ${Boost_LIBRARIES}  # Boost libraries were already here in your original script
    TBB::tbb
)

# Enable OpenMP support if available
if(OpenMP_CXX_FOUND)
    target_link_libraries(phi_calculator PUBLIC OpenMP::OpenMP_CXX)
endif()

# Enable CUDA support if available
if(CUDA_FOUND)
    target_compile_definitions(phi_calculator PUBLIC ULTRA_HAS_CUDA)
    target_link_libraries(phi_calculator PUBLIC ${CUDA_LIBRARIES})
endif()

# Install headers and library
install(
    TARGETS phi_calculator
    LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
    ARCHIVE DESTINATION ${CMAKE_INSTALL_LIBDIR}
)

install(
    FILES ${PHI_CALCULATOR_HEADERS}
    DESTINATION ${CMAKE_INSTALL_INCLUDEDIR}/ultra/phi_calculator
)

# Unit tests for phi_calculator
if(GTEST_FOUND)
    add_executable(test_phi_calculator
        tests/test_causal_analysis.cpp
        tests/test_phi_computation.cpp
        tests/test_information_geometry.cpp
    )
    
    target_link_libraries(test_phi_calculator PRIVATE
        phi_calculator
        utils
        ${GTEST_LIBRARIES}
        ${GTEST_MAIN_LIBRARIES}
        ${Boost_LIBRARIES}  # Added Boost libraries to tests
        pthread
    )
    
    add_test(NAME PhiCalculatorTests COMMAND test_phi_calculator)
    
    # This macro adds compile-time format string checking if supported
    if(CMAKE_CXX_COMPILER_ID MATCHES "GNU|Clang")
        target_compile_options(test_phi_calculator PRIVATE -Wformat -Werror=format-security)
    endif()
endif()

# Documentation
find_package(Doxygen)
if(DOXYGEN_FOUND)
    set(DOXYGEN_INPUT_DIR ${CMAKE_CURRENT_SOURCE_DIR})
    set(DOXYGEN_OUTPUT_DIR ${CMAKE_CURRENT_BINARY_DIR}/docs)
    set(DOXYGEN_INDEX_FILE ${DOXYGEN_OUTPUT_DIR}/html/index.html)
    set(DOXYGEN_CFG_FILE ${CMAKE_CURRENT_BINARY_DIR}/Doxyfile)

    # Configure the Doxyfile
    configure_file(
        ${ULTRA_SOURCE_DIR}/docs/Doxyfile.in
        ${DOXYGEN_CFG_FILE}
        @ONLY
    )

    # Add documentation build target
    add_custom_command(
        OUTPUT ${DOXYGEN_INDEX_FILE}
        COMMAND ${DOXYGEN_EXECUTABLE} ${DOXYGEN_CFG_FILE}
        MAIN_DEPENDENCY ${DOXYGEN_CFG_FILE} ${PHI_CALCULATOR_HEADERS}
        COMMENT "Generating phi_calculator documentation with Doxygen"
        VERBATIM
    )

    add_custom_target(phi_calculator_docs DEPENDS ${DOXYGEN_INDEX_FILE})
endif()

# Generated code configuration
set(GENERATED_DIR ${CMAKE_CURRENT_BINARY_DIR}/generated)
file(MAKE_DIRECTORY ${GENERATED_DIR})

# Generate performance profiles and optimization configuration
find_package(Python3 COMPONENTS Interpreter)
if(Python3_FOUND)
    # Generate optimization configs for phi computation kernels
    add_custom_command(
        OUTPUT ${GENERATED_DIR}/phi_kernels_config.h
        COMMAND ${Python3_EXECUTABLE} 
                ${CMAKE_CURRENT_SOURCE_DIR}/tools/generate_kernel_configs.py
                --output ${GENERATED_DIR}/phi_kernels_config.h
        COMMENT "Generating optimized kernel configurations for phi calculator"
        VERBATIM
    )

    add_custom_target(phi_calculator_generated_code DEPENDS ${GENERATED_DIR}/phi_kernels_config.h)
    add_dependencies(phi_calculator phi_calculator_generated_code)
    target_include_directories(phi_calculator PRIVATE ${GENERATED_DIR})
endif()

# Performance metrics collection (if enabled)
option(PHI_CALCULATOR_PERF_METRICS "Enable performance metrics collection in Phi Calculator" OFF)
if(PHI_CALCULATOR_PERF_METRICS)
    target_compile_definitions(phi_calculator PRIVATE PHI_CALCULATOR_COLLECT_PERF_METRICS)
    
    # Add performance test executable
    add_executable(phi_calculator_perf_test
        tests/performance/perf_test_phi.cpp
    )
    
    target_link_libraries(phi_calculator_perf_test PRIVATE
        phi_calculator
        utils
        ${Boost_LIBRARIES}  # Added Boost libraries to performance tests
    )
endif()

# Print configuration summary
message(STATUS "Configured phi_calculator with the following options:")
message(STATUS "  - Performance metrics collection: ${PHI_CALCULATOR_PERF_METRICS}")
message(STATUS "  - OpenMP support: ${OpenMP_CXX_FOUND}")
message(STATUS "  - CUDA support: ${CUDA_FOUND}")
message(STATUS "  - Boost support: ${Boost_FOUND}")  # Added Boost status
if(GTEST_FOUND)
    message(STATUS "  - Unit tests: Enabled")
else()
    message(STATUS "  - Unit tests: Disabled (GTest not found)")
endif()
if(DOXYGEN_FOUND)
    message(STATUS "  - Documentation: Enabled")
else()
    message(STATUS "  - Documentation: Disabled (Doxygen not found)")
endif()