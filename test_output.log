test_01_basic_goal_reasoning (__main__.VerifiableTestSuite.test_01_basic_goal_reasoning)
Test 1: Basic reasoning from one concept to another. ... ok
test_02_analogical_reasoning (__main__.VerifiableTestSuite.test_02_analogical_reasoning)
Test 2: Analogical reasoning (A:B :: C:?). ... ok
test_03_interpolation_reasoning (__main__.VerifiableTestSuite.test_03_interpolation_reasoning)
Test 3: Interpolation between concepts. ... ok
test_04_multi_constraint_reasoning (__main__.VerifiableTestSuite.test_04_multi_constraint_reasoning)
Test 4: Reasoning with multiple constraints. ... ok
test_05_concept_clustering_verification (__main__.VerifiableTestSuite.test_05_concept_clustering_verification)
Test 5: Verify that the concept space has meaningful structure. ... FAIL
test_06_reasoning_consistency (__main__.VerifiableTestSuite.test_06_reasoning_consistency)
Test 6: Verify reasoning consistency across multiple runs. ... ok
test_07_performance_benchmarking (__main__.VerifiableTestSuite.test_07_performance_benchmarking)
Test 7: Performance benchmarking with different scales. ... ERROR

======================================================================
ERROR: test_07_performance_benchmarking (__main__.VerifiableTestSuite.test_07_performance_benchmarking)
Test 7: Performance benchmarking with different scales.
----------------------------------------------------------------------
Traceback (most recent call last):
  File "/workspaces/Ultra/ultra/ultra/diffusion_reasoning/test_reverse_diffusion_reasoning_Real_example.py", line 686, in test_07_performance_benchmarking
    _ = self.reasoning_system.reason_to_goal(cat, dog, num_steps=num_steps)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/workspaces/Ultra/ultra/ultra/diffusion_reasoning/test_reverse_diffusion_reasoning_Real_example.py", line 232, in reason_to_goal
    z_prev = self.guided_reverse_step(z_t.unsqueeze(0), t, [goal_guidance], [guidance_scale])
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/workspaces/Ultra/ultra/ultra/diffusion_reasoning/test_reverse_diffusion_reasoning_Real_example.py", line 184, in guided_reverse_step
    alpha_t = self.alphas[t.long()]
              ~~~~~~~~~~~^^^^^^^^^^
IndexError: index 19 is out of bounds for dimension 0 with size 15

======================================================================
FAIL: test_05_concept_clustering_verification (__main__.VerifiableTestSuite.test_05_concept_clustering_verification)
Test 5: Verify that the concept space has meaningful structure.
----------------------------------------------------------------------
Traceback (most recent call last):
  File "/workspaces/Ultra/ultra/ultra/diffusion_reasoning/test_reverse_diffusion_reasoning_Real_example.py", line 597, in test_05_concept_clustering_verification
    self.assertLess(avg_between, 0.6,
AssertionError: 0.7320607577761015 not less than 0.6 : Between-category similarities should be reasonably low

----------------------------------------------------------------------
Ran 7 tests in 3.476s

FAILED (failures=1, errors=1)
