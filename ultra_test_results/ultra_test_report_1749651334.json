{"overall_score": 0.3999011247418821, "tests_passed": 8, "total_tests": 18, "execution_time": 0.591813325881958, "category_results": {"Basic Functionality": {"average_score": 0.3994067484512925, "tests_passed": 1, "total_tests": 3}, "Mathematical Rigor": {"average_score": 0.3333333333333333, "tests_passed": 1, "total_tests": 3}, "Reasoning Capabilities": {"average_score": 0.26666666666666666, "tests_passed": 1, "total_tests": 3}, "Performance & Stress": {"average_score": 0.6333333333333333, "tests_passed": 2, "total_tests": 3}, "Edge Cases & Robustness": {"average_score": 0.7666666666666667, "tests_passed": 3, "total_tests": 3}, "Integration Tests": {"average_score": 0.0, "tests_passed": 0, "total_tests": 2}, "Advanced Reasoning": {"average_score": 0.0, "tests_passed": 0, "total_tests": 1}}, "assessment": "❌ POOR - Major Issues", "detailed_results": [{"test_name": "System Initialization", "category": "Basic Functionality", "passed": false, "score": 0.2, "execution_time": 7.3909759521484375e-06, "difficulty": "easy", "error_message": null}, {"test_name": "ThoughtVector Operations", "category": "Basic Functionality", "passed": true, "score": 0.9999999999999999, "execution_time": 0.0288999080657959, "difficulty": "easy", "error_message": null}, {"test_name": "Encode-Decode Cycle", "category": "Basic Functionality", "passed": false, "score": -0.0017797546461224556, "execution_time": 0.047609567642211914, "difficulty": "easy", "error_message": null}, {"test_name": "Semantic Similarity Properties", "category": "Mathematical Rigor", "passed": true, "score": 1.0, "execution_time": 0.04114675521850586, "difficulty": "medium", "error_message": null}, {"test_name": "Manifold Geometry", "category": "Mathematical Rigor", "passed": false, "score": 0.0, "execution_time": 0.0510258674621582, "difficulty": "hard", "error_message": null}, {"test_name": "Information Theoretic Measures", "category": "Mathematical Rigor", "passed": false, "score": 0.0, "execution_time": 0.02944040298461914, "difficulty": "hard", "error_message": null}, {"test_name": "Analogical Reasoning", "category": "Reasoning Capabilities", "passed": true, "score": 0.8, "execution_time": 0.0013260841369628906, "difficulty": "hard", "error_message": null}, {"test_name": "Compositional Reasoning", "category": "Reasoning Capabilities", "passed": false, "score": 0.0, "execution_time": 4.220008850097656e-05, "difficulty": "medium", "error_message": null}, {"test_name": "Causal Reasoning", "category": "Reasoning Capabilities", "passed": false, "score": 0.0, "execution_time": 3.2901763916015625e-05, "difficulty": "extreme", "error_message": null}, {"test_name": "Scalability Stress", "category": "Performance & Stress", "passed": false, "score": 0.5, "execution_time": 0.2046041488647461, "difficulty": "medium", "error_message": null}, {"test_name": "Memory Efficiency", "category": "Performance & Stress", "passed": true, "score": 0.7, "execution_time": 0.09914350509643555, "difficulty": "medium", "error_message": null}, {"test_name": "Numerical Stability", "category": "Performance & Stress", "passed": true, "score": 0.7, "execution_time": 0.0074236392974853516, "difficulty": "hard", "error_message": null}, {"test_name": "Erro<PERSON>", "category": "Edge Cases & Robustness", "passed": true, "score": 0.9000000000000001, "execution_time": 0.00077056884765625, "difficulty": "medium", "error_message": null}, {"test_name": "Boundary Conditions", "category": "Edge Cases & Robustness", "passed": true, "score": 0.4, "execution_time": 0.0027730464935302734, "difficulty": "hard", "error_message": null}, {"test_name": "Consistency Under Perturbation", "category": "Edge Cases & Robustness", "passed": true, "score": 1.0, "execution_time": 0.008024454116821289, "difficulty": "hard", "error_message": null}, {"test_name": "End-to-End Reasoning Pipeline", "category": "Integration Tests", "passed": false, "score": 0.0, "execution_time": 0.00012373924255371094, "difficulty": "extreme", "error_message": null}, {"test_name": "Cross-Module Integration", "category": "Integration Tests", "passed": false, "score": 0.0, "execution_time": 5.0067901611328125e-06, "difficulty": "extreme", "error_message": null}, {"test_name": "Meta-Reasoning", "category": "Advanced Reasoning", "passed": false, "score": 0.0, "execution_time": 7.414817810058594e-05, "difficulty": "extreme", "error_message": null}]}