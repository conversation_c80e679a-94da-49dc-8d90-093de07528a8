{"overall_score": 0.39996024986871315, "tests_passed": 8, "total_tests": 18, "execution_time": 2.7603471279144287, "category_results": {"Basic Functionality": {"average_score": 0.39976149921227866, "tests_passed": 1, "total_tests": 3}, "Mathematical Rigor": {"average_score": 0.3333333333333333, "tests_passed": 1, "total_tests": 3}, "Reasoning Capabilities": {"average_score": 0.26666666666666666, "tests_passed": 1, "total_tests": 3}, "Performance & Stress": {"average_score": 0.6333333333333333, "tests_passed": 2, "total_tests": 3}, "Edge Cases & Robustness": {"average_score": 0.7666666666666667, "tests_passed": 3, "total_tests": 3}, "Integration Tests": {"average_score": 0.0, "tests_passed": 0, "total_tests": 2}, "Advanced Reasoning": {"average_score": 0.0, "tests_passed": 0, "total_tests": 1}}, "assessment": "❌ POOR - Major Issues", "detailed_results": [{"test_name": "System Initialization", "category": "Basic Functionality", "passed": false, "score": 0.2, "execution_time": 1.5020370483398438e-05, "difficulty": "easy", "error_message": null}, {"test_name": "ThoughtVector Operations", "category": "Basic Functionality", "passed": true, "score": 0.9999999999999999, "execution_time": 0.23102831840515137, "difficulty": "easy", "error_message": null}, {"test_name": "Encode-Decode Cycle", "category": "Basic Functionality", "passed": false, "score": -0.0007155023631639779, "execution_time": 0.4994528293609619, "difficulty": "easy", "error_message": null}, {"test_name": "Semantic Similarity Properties", "category": "Mathematical Rigor", "passed": true, "score": 1.0, "execution_time": 0.0872802734375, "difficulty": "medium", "error_message": null}, {"test_name": "Manifold Geometry", "category": "Mathematical Rigor", "passed": false, "score": 0.0, "execution_time": 0.09734225273132324, "difficulty": "hard", "error_message": null}, {"test_name": "Information Theoretic Measures", "category": "Mathematical Rigor", "passed": false, "score": 0.0, "execution_time": 0.10225749015808105, "difficulty": "hard", "error_message": null}, {"test_name": "Analogical Reasoning", "category": "Reasoning Capabilities", "passed": true, "score": 0.8, "execution_time": 0.008309364318847656, "difficulty": "hard", "error_message": null}, {"test_name": "Compositional Reasoning", "category": "Reasoning Capabilities", "passed": false, "score": 0.0, "execution_time": 0.00010228157043457031, "difficulty": "medium", "error_message": null}, {"test_name": "Causal Reasoning", "category": "Reasoning Capabilities", "passed": false, "score": 0.0, "execution_time": 7.62939453125e-05, "difficulty": "extreme", "error_message": null}, {"test_name": "Scalability Stress", "category": "Performance & Stress", "passed": false, "score": 0.5, "execution_time": 0.6732046604156494, "difficulty": "medium", "error_message": null}, {"test_name": "Memory Efficiency", "category": "Performance & Stress", "passed": true, "score": 0.7, "execution_time": 0.21732687950134277, "difficulty": "medium", "error_message": null}, {"test_name": "Numerical Stability", "category": "Performance & Stress", "passed": true, "score": 0.7, "execution_time": 0.014541864395141602, "difficulty": "hard", "error_message": null}, {"test_name": "Erro<PERSON>", "category": "Edge Cases & Robustness", "passed": true, "score": 0.9000000000000001, "execution_time": 0.0032334327697753906, "difficulty": "medium", "error_message": null}, {"test_name": "Boundary Conditions", "category": "Edge Cases & Robustness", "passed": true, "score": 0.4, "execution_time": 0.01321101188659668, "difficulty": "hard", "error_message": null}, {"test_name": "Consistency Under Perturbation", "category": "Edge Cases & Robustness", "passed": true, "score": 1.0, "execution_time": 0.03503131866455078, "difficulty": "hard", "error_message": null}, {"test_name": "End-to-End Reasoning Pipeline", "category": "Integration Tests", "passed": false, "score": 0.0, "execution_time": 0.000202178955078125, "difficulty": "extreme", "error_message": null}, {"test_name": "Cross-Module Integration", "category": "Integration Tests", "passed": false, "score": 0.0, "execution_time": 4.0531158447265625e-06, "difficulty": "extreme", "error_message": null}, {"test_name": "Meta-Reasoning", "category": "Advanced Reasoning", "passed": false, "score": 0.0, "execution_time": 0.00014257431030273438, "difficulty": "extreme", "error_message": null}]}