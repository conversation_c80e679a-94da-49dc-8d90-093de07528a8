#!/usr/bin/env python3
"""
ULTRA Import Structure Fix
=========================

This script fixes all import structure issues throughout the ULTRA system
by updating import statements to work with the current repository structure.
"""

import os
import re
import sys
from pathlib import Path
from typing import List, Dict, Set

def find_python_files(root_dir: Path) -> List[Path]:
    """Find all Python files in the directory tree."""
    python_files = []
    for root, dirs, files in os.walk(root_dir):
        # Skip __pycache__ directories
        dirs[:] = [d for d in dirs if d != '__pycache__']
        
        for file in files:
            if file.endswith('.py'):
                python_files.append(Path(root) / file)
    
    return python_files

def fix_imports_in_file(file_path: Path, ultra_root: Path) -> bool:
    """Fix import statements in a single file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Fix common import patterns
        patterns_and_replacements = [
            # Fix ultra.ultra.module patterns to ultra.module
            (r'from ultra\.ultra\.([a-zA-Z_][a-zA-Z0-9_]*)', r'from ultra.\1'),
            (r'import ultra\.ultra\.([a-zA-Z_][a-zA-Z0-9_]*)', r'import ultra.\1'),
            
            # Fix specific known imports
            (r'from ultra\.ultra\.config', r'from ultra.config'),
            (r'from ultra\.ultra\.utils', r'from ultra.utils'),
            (r'from ultra\.ultra\.core_neural', r'from ultra.core_neural'),
            (r'from ultra\.ultra\.hyper_transformer', r'from ultra.hyper_transformer'),
            (r'from ultra\.ultra\.diffusion_reasoning', r'from ultra.diffusion_reasoning'),
            (r'from ultra\.ultra\.meta_cognitive', r'from ultra.meta_cognitive'),
            (r'from ultra\.ultra\.emergent_consciousness', r'from ultra.emergent_consciousness'),
            (r'from ultra\.ultra\.integration', r'from ultra.integration'),
            (r'from ultra\.ultra\.neuro_symbolic', r'from ultra.neuro_symbolic'),
            (r'from ultra\.ultra\.self_evolution', r'from ultra.self_evolution'),
            (r'from ultra\.ultra\.neuromorphic_processing', r'from ultra.neuromorphic_processing'),
            (r'from ultra\.ultra\.autonomous_learning', r'from ultra.autonomous_learning'),
            (r'from ultra\.ultra\.safety', r'from ultra.safety'),
            (r'from ultra\.ultra\.knowledge_management', r'from ultra.knowledge_management'),
            (r'from ultra\.ultra\.input_processing', r'from ultra.input_processing'),
            (r'from ultra\.ultra\.output_generation', r'from ultra.output_generation'),
            (r'from ultra\.ultra\.tool_interface', r'from ultra.tool_interface'),
            
            # Fix relative imports that should be absolute
            (r'from \.\.([a-zA-Z_][a-zA-Z0-9_./]*)', r'from ultra.\1'),
            (r'from \.([a-zA-Z_][a-zA-Z0-9_./]*)', r'from ultra.\1'),
        ]
        
        for pattern, replacement in patterns_and_replacements:
            content = re.sub(pattern, replacement, content)
        
        # Only write if content changed
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            return True
        
        return False
        
    except Exception as e:
        print(f"Error processing {file_path}: {e}")
        return False

def main():
    """Main function to fix all imports."""
    # Find ULTRA root directory
    script_dir = Path(__file__).parent
    ultra_root = script_dir / "ultra" / "ultra"
    
    if not ultra_root.exists():
        print(f"ULTRA root directory not found at {ultra_root}")
        return 1
    
    print(f"Fixing imports in ULTRA system at {ultra_root}")
    
    # Find all Python files
    python_files = find_python_files(ultra_root)
    print(f"Found {len(python_files)} Python files to process")
    
    # Fix imports in each file
    fixed_count = 0
    for file_path in python_files:
        try:
            if fix_imports_in_file(file_path, ultra_root):
                fixed_count += 1
                print(f"Fixed: {file_path.relative_to(ultra_root)}")
        except Exception as e:
            print(f"Error processing {file_path}: {e}")
    
    print(f"\nCompleted: Fixed imports in {fixed_count} files out of {len(python_files)} total")
    return 0

if __name__ == "__main__":
    sys.exit(main())
