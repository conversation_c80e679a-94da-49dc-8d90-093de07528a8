#!/usr/bin/env python3
"""
Test script to verify all imports work correctly
"""

import sys
import os

# Add the project to path
sys.path.insert(0, '/workspaces/Ultra')
sys.path.insert(0, '/workspaces/Ultra/ultra')

def test_imports():
    """Test all critical imports"""
    print("🧪 Testing ULTRA imports...")
    
    try:
        print("Testing config import...")
        from ultra.config import get_config
        config = get_config()
        print("✅ Config import successful!")
    except Exception as e:
        print(f"❌ Config import failed: {e}")
    
    try:
        print("Testing utils import...")
        from ultra.utils import get_system_monitor
        print("✅ Utils import successful!")
    except Exception as e:
        print(f"❌ Utils import failed: {e}")
    
    try:
        print("Testing meta_cognitive import...")
        from ultra.meta_cognitive import bias_detection
        print("✅ Meta-cognitive import successful!")
    except Exception as e:
        print(f"❌ Meta-cognitive import failed: {e}")
    
    try:
        print("Testing core_neural import...")
        from ultra.core_neural import neuromorphic_core
        print("✅ Core neural import successful!")
    except Exception as e:
        print(f"❌ Core neural import failed: {e}")
    
    print("\n🎉 Import testing complete!")

if __name__ == "__main__":
    test_imports()