#!/usr/bin/env python3
"""
Comprehensive import diagnosis for ULTRA system
"""

import sys
import traceback
import os

# Add ULTRA to path
sys.path.insert(0, '/workspaces/Ultra')
sys.path.insert(0, '/workspaces/Ultra/ultra')

def test_imports():
    """Test all critical imports and diagnose issues"""
    print('ULTRA Import Diagnosis')
    print('='*50)
    
    # Test 1: Core Config Module
    print("\n1. Testing ultra.config...")
    try:
        from ultra.config import get_config
        print('✅ ultra.config - SUCCESS')
    except Exception as e:
        print(f'❌ ultra.config - FAILED: {e}')
        traceback.print_exc()
    
    # Test 2: Utils Module
    print("\n2. Testing ultra.utils...")
    try:
        from ultra.utils import get_system_monitor
        print('✅ ultra.utils - SUCCESS')
    except Exception as e:
        print(f'❌ ultra.utils - FAILED: {e}')
        traceback.print_exc()
    
    # Test 3: System Init
    print("\n3. Testing ultra.system_init...")
    try:
        from ultra.system_init import ULTRASystemInitializer
        print('✅ ultra.system_init - SUCCESS')
    except Exception as e:
        print(f'❌ ultra.system_init - FAILED: {e}')
        traceback.print_exc()
    
    # Test 4: Core Neural
    print("\n4. Testing ultra.core_neural...")
    try:
        from ultra.core_neural import NeuromorphicCore
        print('✅ ultra.core_neural - SUCCESS')
    except Exception as e:
        print(f'❌ ultra.core_neural - FAILED: {e}')
        traceback.print_exc()
    
    # Test 5: Integration
    print("\n5. Testing ultra.integration...")
    try:
        from ultra.integration import NeuromorphicTransformerBridge
        print('✅ ultra.integration - SUCCESS')
    except Exception as e:
        print(f'❌ ultra.integration - FAILED: {e}')
        traceback.print_exc()

if __name__ == "__main__":
    test_imports()
