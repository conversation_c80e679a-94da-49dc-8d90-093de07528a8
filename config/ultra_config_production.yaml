batch_size: 32
checkpoint_directory: ./checkpoints
checkpoint_enabled: true
checkpoint_interval: 1000
core_neural_config:
  adaptive_ratio: 0.03
  connection_probability: 0.1
  connectivity_pattern: small_world
  created_at: 2025-06-05 08:08:21.663274+00:00
  description: null
  dt: 0.1
  excitatory_ratio: 0.8
  inhibitory_ratio: 0.15
  layer_sizes:
  - 2000
  - 1800
  - 1600
  - 1400
  - 1200
  - 2000
  neuromodulation_config:
    acetylcholine_baseline: 0.4
    acetylcholine_enabled: true
    acetylcholine_focus: 2.0
    acetylcholine_tau: 150.0
    created_at: 2025-06-05 08:08:21.663251+00:00
    cross_modulation: true
    description: null
    dopamine_baseline: 0.5
    dopamine_enabled: true
    dopamine_lr_modulation: 2.0
    dopamine_tau: 100.0
    modulation_strength: 1.0
    norepinephrine_attention: 1.5
    norepinephrine_baseline: 0.2
    norepinephrine_enabled: true
    norepinephrine_tau: 50.0
    serotonin_baseline: 0.3
    serotonin_enabled: true
    serotonin_inhibition: 0.5
    serotonin_tau: 200.0
    tags: []
    updated_at: 2025-06-05 08:08:21.663251+00:00
    version: 1.0.0
  neuromodulatory_ratio: 0.02
  neuron_config:
    a: 2.0
    a_izh: 0.02
    b: 60.0
    b_izh: 0.2
    c_izh: -65.0
    created_at: 2025-06-05 08:08:21.663225+00:00
    d_izh: 8.0
    delta_t: 2.0
    description: null
    e_l: -70.0
    membrane_resistance: 100.0
    model_type: !!python/object/apply:__main__.NeuronModelType
    - lif
    noise_amplitude: 0.1
    noise_correlation: 0.0
    refractory_period: 2.0
    tags: []
    tau_m: 20.0
    tau_w: 30.0
    updated_at: 2025-06-05 08:08:21.663230+00:00
    v_reset: -80.0
    v_rest: -70.0
    v_t: -50.0
    v_threshold: -50.0
    version: 1.0.0
  num_layers: 6
  num_neurons: 10000
  plasticity_config:
    a_minus: 0.105
    a_plus: 0.1
    connection_probability: 0.1
    created_at: 2025-06-05 08:08:21.663241+00:00
    description: null
    growth_rate: 0.001
    homeostatic_enabled: true
    homeostatic_tau: 1000.0
    max_connections: 100
    meta_tau: 10000.0
    meta_threshold: 0.5
    metaplastic_enabled: false
    pruning_threshold: 0.1
    scaling_factor: 0.01
    stdp_enabled: true
    structural_enabled: true
    tags: []
    target_rate: 10.0
    tau_minus: 20.0
    tau_plus: 20.0
    updated_at: 2025-06-05 08:08:21.663242+00:00
    version: 1.0.0
    w_max: 1.0
    w_min: 0.0
  recording_enabled: true
  recording_interval: 1.0
  simulation_time: 1000.0
  tags: []
  timing_config:
    alpha_beta_coupling: 0.2
    alpha_freq: !!python/tuple
    - 8.0
    - 12.0
    alpha_strength: 0.6
    beta_freq: !!python/tuple
    - 12.0
    - 30.0
    beta_strength: 0.4
    created_at: 2025-06-05 08:08:21.663266+00:00
    delta_freq: !!python/tuple
    - 1.0
    - 4.0
    delta_strength: 0.5
    description: null
    gamma_freq: !!python/tuple
    - 30.0
    - 100.0
    gamma_strength: 0.8
    global_sync_strength: 0.1
    local_sync_radius: 10.0
    phase_coupling_enabled: true
    phase_precision: 0.01
    tags: []
    temporal_resolution: 0.1
    theta_freq: !!python/tuple
    - 4.0
    - 8.0
    theta_gamma_coupling: 0.3
    theta_strength: 0.7
    updated_at: 2025-06-05 08:08:21.663266+00:00
    version: 1.0.0
  updated_at: 2025-06-05 08:08:21.663275+00:00
  version: 1.0.0
  weight_initialization: normal
  weight_scale: 0.1
created_at: '2025-06-05T08:08:21.666150+00:00'
description: null
diffusion_config:
  concept_editing_enabled: true
  constraint_weight: 0.5
  created_at: 2025-06-05 08:08:21.663611+00:00
  description: null
  editing_strength: 0.5
  eta: 0.0
  goal_conditioning: true
  goal_weight: 1.0
  guidance_end_step: null
  guidance_start_step: 0
  num_inference_steps: 50
  preservation_mask: null
  reverse_guidance_scale: 7.5
  sampler_type: ddpm
  schedule_config:
    beta_end: 0.02
    beta_start: 0.0001
    cosine_s: 0.008
    created_at: 2025-06-05 08:08:21.663591+00:00
    custom_betas: null
    description: null
    exponential_decay: 0.999
    num_timesteps: 1000
    schedule_type: !!python/object/apply:__main__.DiffusionSchedule
    - linear
    tags: []
    updated_at: 2025-06-05 08:08:21.663592+00:00
    variance_type: fixed_small
    version: 1.0.0
  tags: []
  thought_space_config:
    cluster_method: kmeans
    clustering_enabled: true
    composition_method: weighted_sum
    composition_weights: null
    created_at: 2025-06-05 08:08:21.663599+00:00
    description: null
    distance_metric: cosine
    exploration_radius: 0.1
    hierarchical_levels: 3
    interpolation_method: slerp
    num_clusters: 100
    num_concepts: 10000
    relation_types:
    - similarity
    - analogy
    - causality
    - temporal
    - spatial
    semantic_continuity: true
    similarity_threshold: 0.7
    tags: []
    thought_dim: 512
    updated_at: 2025-06-05 08:08:21.663599+00:00
    version: 1.0.0
  uncertainty_config:
    aleatoric_enabled: true
    calibration_enabled: true
    calibration_method: isotonic
    calibration_validation_split: 0.2
    created_at: 2025-06-05 08:08:21.663607+00:00
    description: null
    ensemble_method: deep
    ensemble_size: 5
    epistemic_enabled: true
    kl_weight: 1.0
    mc_dropout: true
    mc_dropout_rate: 0.1
    mc_samples: 50
    num_samples: 100
    prior_mean: 0.0
    prior_std: 1.0
    prior_type: normal
    tags: []
    updated_at: 2025-06-05 08:08:21.663607+00:00
    variational_inference: true
    version: 1.0.0
  updated_at: 2025-06-05 08:08:21.663612+00:00
  version: 1.0.0
distributed_processing: false
environment: !!python/object/apply:__main__.Environment
- production
experimental_features_enabled: false
feature_flags: {}
global_state_synchronization: true
hardware_config:
  auto_detect_hardware: true
  cpu_affinity: null
  created_at: 2025-06-05 08:08:21.666116+00:00
  cuda_benchmark: true
  cuda_deterministic: false
  cuda_enabled: false
  description: null
  device_ids:
  - 0
  gradient_accumulation_steps: 1
  memory_fragmentation_threshold: 0.8
  memory_growth: true
  memory_limit_gb: 2.024261474609375
  mixed_precision: true
  multi_gpu_strategy: data_parallel
  neuromorphic_config: {}
  neuromorphic_device: null
  num_cpu_cores: 1
  preferred_device: !!python/object/apply:__main__.HardwareType
  - cuda
  tags: []
  updated_at: 2025-06-05 08:08:21.666118+00:00
  version: 1.0.0
inter_component_communication: true
logging_config:
  alert_thresholds:
    cpu_usage: 0.8
    error_rate: 0.1
    memory_usage: 0.9
    response_time: 2.0
  alerting_enabled: true
  component_log_levels:
    consciousness: INFO
    core_neural: INFO
    diffusion: INFO
    meta_cognitive: INFO
    neuro_symbolic: INFO
    neuromorphic: DEBUG
    self_evolution: WARNING
    transformer: INFO
  created_at: 2025-06-05 08:08:21.666142+00:00
  description: null
  include_process_info: true
  include_thread_info: true
  include_timestamps: true
  log_file_path: ultra_system.log
  log_format: json
  log_level: WARNING
  log_rotation_enabled: true
  log_to_console: true
  log_to_file: true
  max_log_file_size_mb: 100
  max_log_files: 5
  metrics_collection_interval: 1.0
  metrics_retention_hours: 24
  performance_logging: true
  structured_logging: true
  tags: []
  updated_at: 2025-06-05 08:08:21.666142+00:00
  version: 1.0.0
max_checkpoints_to_keep: 5
meta_cognitive_config:
  attention_control_strength: 1.0
  bias_detection_config:
    bias_detection_threshold: 0.7
    bias_evaluation_method: statistical
    bias_types:
    - confirmation_bias
    - availability_bias
    - anchoring_bias
    - overconfidence_bias
    - attribution_error
    - hindsight_bias
    - framing_effect
    - sunk_cost_fallacy
    - representativeness_heuristic
    - base_rate_fallacy
    correction_enabled: true
    correction_strength: 0.5
    created_at: 2025-06-05 08:08:21.663669+00:00
    description: null
    detection_confidence_threshold: 0.8
    evaluation_window_size: 100
    max_correction_attempts: 3
    tags: []
    updated_at: 2025-06-05 08:08:21.663670+00:00
    version: 1.0.0
  computational_budget: 1.0
  confidence_calibration: true
  created_at: 2025-06-05 08:08:21.663681+00:00
  description: null
  energy_budget: 1.0
  executive_control_enabled: true
  meta_learning_config:
    adaptation_rate: 0.1
    adaptation_trigger_threshold: 0.1
    created_at: 2025-06-05 08:08:21.663676+00:00
    description: null
    inner_lr: 0.01
    inner_steps: 5
    meta_algorithm: maml
    num_query_examples: 15
    num_support_examples: 5
    outer_lr: 0.001
    outer_steps: 1000
    performance_history_size: 1000
    performance_threshold: 0.8
    strategy_adaptation_enabled: true
    strategy_evaluation_period: 100
    tags: []
    task_distribution: uniform
    updated_at: 2025-06-05 08:08:21.663677+00:00
    version: 1.0.0
  monitoring_enabled: true
  reasoning_config:
    coherence_weight: 0.3
    completeness_weight: 0.3
    correctness_weight: 0.2
    created_at: 2025-06-05 08:08:21.663623+00:00
    critique_enabled: true
    critique_improvement_threshold: 0.05
    depth_weight: 0.2
    description: null
    graph_max_edges: 5000
    graph_max_nodes: 1000
    graph_reasoning_timeout: 30.0
    max_critique_iterations: 3
    max_reasoning_paths: 5
    message_passing_iterations: 5
    path_beam_width: 3
    path_pruning_threshold: 0.1
    resource_allocation_method: temperature
    tags: []
    tree_branching_factor: 3
    tree_exploration_param: 1.414
    tree_max_depth: 10
    tree_pruning_enabled: true
    updated_at: 2025-06-05 08:08:21.663623+00:00
    version: 1.0.0
  strategy_selection_method: epsilon_greedy
  tags: []
  time_budget: 60.0
  updated_at: 2025-06-05 08:08:21.663682+00:00
  version: 1.0.0
  working_memory_capacity: 7
num_workers: 4
optimization_level: !!python/object/apply:__main__.OptimizationLevel
- development
pin_memory: true
prefetch_factor: 2
security_config:
  adversarial_detection: true
  api_rate_limiting: true
  audit_log_retention_days: 90
  audit_logging_enabled: true
  authentication_required: true
  authorization_levels:
  - read
  - write
  - admin
  created_at: 2025-06-05 08:08:21.666130+00:00
  description: null
  differential_privacy_enabled: false
  dp_delta: 1.0e-05
  dp_epsilon: 1.0
  dp_noise_multiplier: 1.0
  encryption_algorithm: aes256
  encryption_enabled: false
  input_validation_strict: true
  key_rotation_interval: 86400
  max_requests_per_minute: 100
  model_signing_enabled: false
  sensitive_data_masking: true
  tags: []
  updated_at: 2025-06-05 08:08:21.666131+00:00
  version: 1.0.0
system_name: ULTRA
system_version: 1.0.0
tags: []
transformer_config:
  attention_config:
    adaptive_temperature: true
    attention_dropout: 0.1
    attention_type: !!python/object/apply:__main__.AttentionType
    - dynamic
    bias_matrix_enabled: true
    context_window: 128
    created_at: 2025-06-05 08:08:21.663452+00:00
    d_k: 64
    d_model: 512
    d_v: 64
    description: null
    evolution_rate: 0.01
    layer_norm_eps: 1.0e-06
    local_window_size: 32
    mask_evolution_enabled: true
    num_heads: 8
    num_scales: 3
    output_dropout: 0.1
    scale_factors:
    - 1.0
    - 0.5
    - 0.25
    scale_weights:
    - 0.5
    - 0.3
    - 0.2
    sparsity_pattern: local
    sparsity_ratio: 0.1
    stride_size: 16
    tags: []
    temperature: 1.0
    temperature_max: 10.0
    temperature_min: 0.1
    updated_at: 2025-06-05 08:08:21.663453+00:00
    version: 1.0.0
  beam_size: 5
  created_at: 2025-06-05 08:08:21.663525+00:00
  description: null
  early_stopping: true
  embedding_config:
    created_at: 2025-06-05 08:08:21.663519+00:00
    d_model: 512
    description: null
    embedding_dropout: 0.1
    embedding_layer_norm: true
    embedding_scales:
    - 512
    - 256
    - 128
    joint_embedding_dim: 512
    max_position_embeddings: 1024
    modalities:
    - text
    - vision
    - audio
    modality_dims:
      audio: 256
      text: 512
      vision: 512
    num_embedding_scales: 3
    position_encoding_max_len: 5000
    position_encoding_type: sinusoidal
    scale_fusion_method: weighted_sum
    tags: []
    updated_at: 2025-06-05 08:08:21.663519+00:00
    version: 1.0.0
    vocab_size: 50000
    weight_tying: true
  gradient_checkpointing: false
  layer_config:
    causal_enabled: true
    created_at: 2025-06-05 08:08:21.663508+00:00
    d_ff: 2048
    d_model: 512
    description: null
    dropconnect: 0.0
    dropout: 0.1
    ff_activation: gelu
    gate_activation: sigmoid
    halting_epsilon: 0.01
    halting_threshold: 0.9
    layer_drop: 0.0
    max_recursion_depth: 5
    max_sequence_length: 1024
    norm_eps: 1.0e-06
    pre_norm: true
    recursive_enabled: true
    tags: []
    temporal_encoding_enabled: true
    updated_at: 2025-06-05 08:08:21.663508+00:00
    version: 1.0.0
  learning_rate_schedule: cosine
  length_penalty: 1.0
  max_grad_norm: 1.0
  num_decoder_layers: 6
  num_encoder_layers: 6
  num_layers: 6
  share_embeddings: true
  tags: []
  tie_weights: true
  updated_at: 2025-06-05 08:08:21.663526+00:00
  version: 1.0.0
  warmup_steps: 4000
updated_at: '2025-06-05T08:08:21.666150+00:00'
version: 1.0.0
