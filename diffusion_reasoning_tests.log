2025-06-16 09:41:24,025 - __main__ - INFO - Setting up test environment...
2025-06-16 09:41:24,036 - __main__ - INFO - System info: {'cpu_count': 2, 'cpu_percent': 72.8, 'memory_total_gb': 7.751644134521484, 'memory_available_gb': 3.333454132080078, 'memory_percent': 57.0, 'gpu_available': False, 'gpu_count': 0, 'gpu_memory_gb': 0}
2025-06-16 09:41:24,036 - __main__ - INFO - Using device: cpu
2025-06-16 09:41:24,036 - __main__ - INFO - Test environment setup complete
2025-06-16 09:41:24,036 - __main__ - INFO - Initialized test orchestrator with 6 test suites
2025-06-16 09:41:24,036 - __main__ - INFO - ================================================================================
2025-06-16 09:41:24,037 - __main__ - INFO - STARTING ULTRA DIFFUSION REASONING MODULE TESTS
2025-06-16 09:41:24,037 - __main__ - INFO - ================================================================================
2025-06-16 09:41:24,037 - __main__ - INFO - Estimated total execution time: 43 minutes 20 seconds
2025-06-16 09:41:24,037 - __main__ - INFO - Running tests in parallel with 4 workers
2025-06-16 09:41:24,037 - __main__ - INFO - Executing priority 1 tests (3 suites)
2025-06-16 09:41:24,092 - __main__ - INFO - Executing test suite: conceptual_diffusion
2025-06-16 09:41:24,098 - __main__ - INFO - Executing test suite: thought_latent_space
2025-06-16 09:41:35,500 - test_thought_latent_space - INFO - Testing compositional operations...
2025-06-16 09:41:37,975 - test_thought_latent_space - INFO - Compositional operations tests passed
2025-06-16 09:41:38,029 - test_thought_latent_space - INFO - Testing encoding-decoding consistency...
2025-06-16 09:41:38,154 - test_thought_latent_space - INFO - Reconstruction error: 0.175261
2025-06-16 09:41:38,195 - test_thought_latent_space - INFO - Testing hierarchical structure preservation...
2025-06-16 09:41:38,281 - test_thought_latent_space - INFO - Training hierarchical structure classifier...
2025-06-16 09:41:38,681 - test_conceptual_diffusion - INFO - Testing conceptual space properties...
2025-06-16 09:41:38,703 - test_conceptual_diffusion - INFO - Within-level similarity: 0.186
2025-06-16 09:41:38,703 - test_conceptual_diffusion - INFO - Across-level similarity: 0.186
2025-06-16 09:41:38,703 - test_conceptual_diffusion - INFO - Semantic similarity preservation: 0.993
2025-06-16 09:41:38,730 - test_conceptual_diffusion - INFO - Testing forward diffusion process...
2025-06-16 09:41:38,743 - test_conceptual_diffusion - INFO - Forward diffusion process tests passed
2025-06-16 09:41:38,786 - test_conceptual_diffusion - INFO - Testing loss computation...
2025-06-16 09:41:39,058 - test_conceptual_diffusion - INFO - Standard loss: 1.520735
2025-06-16 09:41:39,059 - test_conceptual_diffusion - INFO - VLB loss: 105.592812
2025-06-16 09:41:39,118 - test_conceptual_diffusion - INFO - Testing mathematical consistency...
2025-06-16 09:41:39,145 - test_conceptual_diffusion - INFO - Reconstruction error: 0.698847
2025-06-16 09:41:39,172 - test_conceptual_diffusion - INFO - Testing noise schedule properties...
2025-06-16 09:41:39,176 - test_conceptual_diffusion - INFO - Beta range: [0.000100, 0.050000]
2025-06-16 09:41:39,177 - test_conceptual_diffusion - INFO - Alpha cumprod range: [0.999900, 0.078234]
2025-06-16 09:41:39,221 - test_conceptual_diffusion - INFO - Testing performance benchmarks...
2025-06-16 09:41:39,672 - test_conceptual_diffusion - INFO - Forward diffusion time: 0.09ms
2025-06-16 09:41:39,673 - test_conceptual_diffusion - INFO - Reverse diffusion time: 3.17ms
2025-06-16 09:41:39,673 - test_conceptual_diffusion - INFO - Loss computation time: 1.24ms
2025-06-16 09:41:39,702 - test_conceptual_diffusion - INFO - Testing reverse diffusion step...
2025-06-16 09:41:39,705 - test_conceptual_diffusion - INFO - Noise reduction ratio: 0.993
2025-06-16 09:41:39,730 - test_conceptual_diffusion - INFO - Testing sampling process...
2025-06-16 09:41:40,081 - test_conceptual_diffusion - INFO - Initial noise level: 7.932
2025-06-16 09:41:40,081 - test_conceptual_diffusion - INFO - Final noise level: 2.216
2025-06-16 09:41:40,081 - test_conceptual_diffusion - INFO - Noise reduction: 0.721
2025-06-16 09:41:40,114 - test_conceptual_diffusion - INFO - Testing score network properties...
2025-06-16 09:41:40,289 - test_conceptual_diffusion - INFO - Score network output range: [0.000, 0.000]
2025-06-16 09:41:40,358 - test_conceptual_diffusion - INFO - Testing memory efficiency...
2025-06-16 09:41:40,358 - test_conceptual_diffusion - INFO - CUDA not available, skipping memory test
2025-06-16 09:41:40,440 - test_conceptual_diffusion - INFO - Testing training integration...
2025-06-16 09:41:51,456 - test_conceptual_diffusion - INFO - Initial loss: 1.111388
2025-06-16 09:41:51,460 - test_conceptual_diffusion - INFO - Final loss: 1.063309
2025-06-16 09:41:51,460 - test_conceptual_diffusion - INFO - Relative change: 0.043
2025-06-16 09:41:51,470 - test_conceptual_diffusion - INFO - ================================================================================
2025-06-16 09:41:51,471 - test_conceptual_diffusion - INFO - CONCEPTUAL DIFFUSION TEST SUMMARY
2025-06-16 09:41:51,471 - test_conceptual_diffusion - INFO - ================================================================================
2025-06-16 09:41:51,471 - test_conceptual_diffusion - INFO - Tests run: 11
2025-06-16 09:41:51,471 - test_conceptual_diffusion - INFO - Failures: 0
2025-06-16 09:41:51,471 - test_conceptual_diffusion - INFO - Errors: 0
2025-06-16 09:41:51,471 - test_conceptual_diffusion - INFO - Success rate: 100.00%
2025-06-16 09:41:51,488 - __main__ - INFO - Suite conceptual_diffusion completed in 27.39s - PASSED
2025-06-16 09:41:51,676 - test_thought_latent_space - INFO - Epoch 0: Loss=2.3002, Accuracy=0.609
2025-06-16 09:41:53,140 - test_thought_latent_space - INFO - Epoch 1: Loss=1.4890, Accuracy=1.000
2025-06-16 09:41:53,561 - test_thought_latent_space - INFO - Epoch 2: Loss=1.1369, Accuracy=1.000
2025-06-16 09:41:54,048 - test_thought_latent_space - INFO - Epoch 3: Loss=0.9498, Accuracy=1.000
2025-06-16 09:41:54,618 - test_thought_latent_space - INFO - Epoch 4: Loss=0.8738, Accuracy=1.000
2025-06-16 09:41:55,017 - test_thought_latent_space - INFO - Epoch 5: Loss=0.7458, Accuracy=1.000
2025-06-16 09:41:55,449 - test_thought_latent_space - INFO - Epoch 6: Loss=0.7269, Accuracy=1.000
2025-06-16 09:41:55,887 - test_thought_latent_space - INFO - Epoch 7: Loss=0.6739, Accuracy=1.000
2025-06-16 09:41:56,433 - test_thought_latent_space - INFO - Epoch 8: Loss=0.6483, Accuracy=1.000
2025-06-16 09:41:56,941 - test_thought_latent_space - INFO - Epoch 9: Loss=0.6335, Accuracy=1.000
2025-06-16 09:42:01,609 - test_thought_latent_space - INFO - Epoch 20: Loss=0.5786, Accuracy=1.000
2025-06-16 09:42:06,767 - test_thought_latent_space - INFO - Achieved high accuracy 1.000 at epoch 31
2025-06-16 09:42:06,767 - test_thought_latent_space - INFO - Testing trained hierarchical structure...
2025-06-16 09:42:07,017 - test_thought_latent_space - INFO - Final test accuracy: 1.000
2025-06-16 09:42:07,018 - test_thought_latent_space - INFO - Training achieved best accuracy: 1.000
2025-06-16 09:42:07,018 - test_thought_latent_space - INFO - Hierarchical coherence: 0.935
2025-06-16 09:42:07,018 - test_thought_latent_space - INFO - Level prediction accuracy: 1.000
2025-06-16 09:42:07,067 - test_thought_latent_space - INFO - Testing interpolation properties...
2025-06-16 09:42:07,098 - test_thought_latent_space - INFO - Interpolation tests passed
2025-06-16 09:42:07,156 - test_thought_latent_space - INFO - Testing loss computation...
2025-06-16 09:42:07,207 - test_thought_latent_space - INFO - Total loss: 1.015770
2025-06-16 09:42:07,208 - test_thought_latent_space - INFO - Reconstruction loss: 0.152321
2025-06-16 09:42:07,208 - test_thought_latent_space - INFO - Hierarchical loss: 1.072037
2025-06-16 09:42:07,254 - test_thought_latent_space - INFO - Testing manifold structure analysis...
2025-06-16 09:42:08,512 - test_thought_latent_space - INFO - Intrinsic dimensionality: 37
2025-06-16 09:42:08,512 - test_thought_latent_space - INFO - Explained variance ratio: 0.953
2025-06-16 09:42:08,513 - test_thought_latent_space - INFO - Local smoothness: 9.454
2025-06-16 09:42:08,514 - test_thought_latent_space - INFO - Condition number: 999.000
2025-06-16 09:42:08,562 - test_thought_latent_space - INFO - Testing memory operations...
2025-06-16 09:42:08,636 - test_thought_latent_space - INFO - Memory operations tests passed
2025-06-16 09:42:08,659 - test_thought_latent_space - INFO - Testing performance benchmarks...
2025-06-16 09:42:22,888 - test_thought_latent_space - INFO - Single encoding time: 3.51ms
2025-06-16 09:42:22,889 - test_thought_latent_space - INFO - Batch processing time: 57.99ms
2025-06-16 09:42:22,916 - test_thought_latent_space - INFO - Testing relational vector computation...
2025-06-16 09:42:22,952 - test_thought_latent_space - INFO - Relational vector tests passed
2025-06-16 09:42:22,984 - test_thought_latent_space - INFO - Testing semantic similarity computation...
2025-06-16 09:42:23,147 - test_thought_latent_space - INFO - Same cluster similarity: 0.513
2025-06-16 09:42:23,148 - test_thought_latent_space - INFO - Different cluster similarity: 0.516
2025-06-16 09:42:23,172 - test_thought_latent_space - INFO - Testing integration with diffusion process...
2025-06-16 09:42:23,245 - test_thought_latent_space - INFO - Noise level 0.1: degradation 0.1821
2025-06-16 09:42:23,251 - test_thought_latent_space - INFO - Noise level 0.3: degradation 0.1843
2025-06-16 09:42:23,259 - test_thought_latent_space - INFO - Noise level 0.5: degradation 0.1816
2025-06-16 09:42:23,263 - test_thought_latent_space - INFO - Noise level 0.8: degradation 0.1748
2025-06-16 09:42:23,263 - test_thought_latent_space - INFO - Diffusion integration test passed
2025-06-16 09:42:23,284 - test_thought_latent_space - INFO - Testing training integration...
2025-06-16 09:42:24,255 - test_thought_latent_space - INFO - Initial loss: 0.986326
2025-06-16 09:42:24,255 - test_thought_latent_space - INFO - Final loss: 0.624556
2025-06-16 09:42:24,255 - test_thought_latent_space - INFO - Loss ratio: 0.633
2025-06-16 09:42:24,257 - test_thought_latent_space - INFO - ================================================================================
2025-06-16 09:42:24,258 - test_thought_latent_space - INFO - THOUGHT LATENT SPACE TEST SUMMARY
2025-06-16 09:42:24,258 - test_thought_latent_space - INFO - ================================================================================
2025-06-16 09:42:24,258 - test_thought_latent_space - INFO - Tests run: 12
2025-06-16 09:42:24,258 - test_thought_latent_space - INFO - Failures: 0
2025-06-16 09:42:24,258 - test_thought_latent_space - INFO - Errors: 0
2025-06-16 09:42:24,258 - test_thought_latent_space - INFO - Success rate: 100.00%
2025-06-16 09:42:24,260 - __main__ - INFO - Suite thought_latent_space completed in 60.16s - PASSED
2025-06-16 09:42:24,312 - __main__ - INFO - Executing test suite: bayesian_uncertainty
2025-06-16 09:42:24,313 - __main__ - ERROR - Suite bayesian_uncertainty failed with exception: No module named 'test_bayesian_uncertainty_quantification'
2025-06-16 09:42:24,313 - __main__ - ERROR - Traceback (most recent call last):
  File "/workspaces/Ultra/ultra/ultra/diffusion_reasoning/run_all_reasoning_diffusion_tests.py", line 339, in execute_test_suite
    module = __import__(module_name)
             ^^^^^^^^^^^^^^^^^^^^^^^
ModuleNotFoundError: No module named 'test_bayesian_uncertainty_quantification'

2025-06-16 09:42:24,389 - __main__ - INFO - Executing priority 2 tests (2 suites)
2025-06-16 09:42:24,418 - __main__ - INFO - Executing test suite: reverse_diffusion_reasoning
2025-06-16 09:42:24,421 - __main__ - INFO - Executing test suite: probabilistic_inference
2025-06-16 09:42:27,237 - test_reverse_diffusion_reasoning - INFO - Testing analogical reasoning...
2025-06-16 09:42:27,246 - test_reverse_diffusion_reasoning - INFO - Analogical relation similarity: 0.999
2025-06-16 09:42:27,258 - test_reverse_diffusion_reasoning - INFO - Testing constraint satisfaction scoring...
2025-06-16 09:42:27,260 - test_reverse_diffusion_reasoning - INFO - Constraint satisfaction score: 0.000
2025-06-16 09:42:27,261 - test_reverse_diffusion_reasoning - INFO - Perfect constraint score: 1.000
2025-06-16 09:42:27,273 - test_reverse_diffusion_reasoning - INFO - Testing guidance function properties...
2025-06-16 09:42:27,276 - test_reverse_diffusion_reasoning - INFO - Guidance function tests passed
2025-06-16 09:42:27,286 - test_reverse_diffusion_reasoning - INFO - Testing guided reverse step...
2025-06-16 09:42:27,292 - test_reverse_diffusion_reasoning - INFO - Samples improved: 8/8
2025-06-16 09:42:27,293 - test_reverse_diffusion_reasoning - INFO - Average distance change: 9.857
2025-06-16 09:42:27,309 - test_reverse_diffusion_reasoning - INFO - Testing interpolative reasoning...
2025-06-16 09:42:27,821 - test_probabilistic_inference_engine - INFO - Testing Bayesian model selection...
2025-06-16 09:42:27,846 - test_probabilistic_inference_engine - INFO - Model probabilities: {'model_0': 0.0, 'model_1': 0.0, 'model_2': 1.0}
2025-06-16 09:42:27,851 - test_probabilistic_inference_engine - INFO - Testing information-theoretic measures...
2025-06-16 09:42:28,014 - test_reverse_diffusion_reasoning - INFO - Similarity trend to A: -0.208
2025-06-16 09:42:28,015 - test_reverse_diffusion_reasoning - INFO - Similarity trend to B: 0.201
2025-06-16 09:42:28,037 - test_reverse_diffusion_reasoning - INFO - Testing mathematical properties...
2025-06-16 09:42:28,044 - test_reverse_diffusion_reasoning - INFO - Guided computation difference: 9.4807
2025-06-16 09:42:28,050 - test_reverse_diffusion_reasoning - INFO - Testing multi-constraint reasoning...
2025-06-16 09:42:28,084 - test_reverse_diffusion_reasoning - INFO - Constraints improved: 2/2
2025-06-16 09:42:28,085 - test_reverse_diffusion_reasoning - INFO - Overall satisfaction - Initial: -78.083, Final: -62.555
2025-06-16 09:42:28,086 - test_reverse_diffusion_reasoning - INFO - Process completed successfully with trajectory variance: 4.100401
2025-06-16 09:42:28,097 - test_reverse_diffusion_reasoning - INFO - Testing performance benchmarks...
2025-06-16 09:42:28,223 - test_reverse_diffusion_reasoning - INFO - Single step time: 0.50ms
2025-06-16 09:42:28,224 - test_reverse_diffusion_reasoning - INFO - Full reasoning time: 7.39ms
2025-06-16 09:42:28,231 - test_reverse_diffusion_reasoning - INFO - Testing goal-directed reasoning...
2025-06-16 09:42:28,263 - test_reverse_diffusion_reasoning - INFO - Convergence ratio: 5.749
2025-06-16 09:42:28,264 - test_reverse_diffusion_reasoning - INFO - Final confidence: 0.000
2025-06-16 09:42:28,264 - test_reverse_diffusion_reasoning - INFO - Initial confidence: 0.000
2025-06-16 09:42:28,264 - test_reverse_diffusion_reasoning - INFO - Path variance: 26.643166
2025-06-16 09:42:28,265 - test_reverse_diffusion_reasoning - INFO - Confidence ratio: 0.000
2025-06-16 09:42:28,272 - test_reverse_diffusion_reasoning - INFO - Testing reasoning trajectory analysis...
2025-06-16 09:42:28,274 - test_reverse_diffusion_reasoning - INFO - Convergence rate: 0.850
2025-06-16 09:42:28,274 - test_reverse_diffusion_reasoning - INFO - Monotonicity: 1.000
2025-06-16 09:42:28,274 - test_reverse_diffusion_reasoning - INFO - Final distance: 1.466
2025-06-16 09:42:28,281 - test_reverse_diffusion_reasoning - INFO - Testing end-to-end reasoning...
2025-06-16 09:42:28,487 - test_reverse_diffusion_reasoning - INFO - End-to-end reasoning test passed
2025-06-16 09:42:28,493 - test_reverse_diffusion_reasoning - INFO - Testing training integration...
2025-06-16 09:42:28,702 - test_probabilistic_inference_engine - INFO - Entropy: 4.605
2025-06-16 09:42:28,703 - test_probabilistic_inference_engine - INFO - Mutual information: 2.303
2025-06-16 09:42:28,705 - test_probabilistic_inference_engine - INFO - Effective dimensionality: 52.708
2025-06-16 09:42:28,712 - test_probabilistic_inference_engine - INFO - Testing likelihood computation...
2025-06-16 09:42:28,717 - test_probabilistic_inference_engine - INFO - Log likelihood range: [-0.431, 0.168]
2025-06-16 09:42:28,725 - test_probabilistic_inference_engine - INFO - Testing marginal likelihood computation...
2025-06-16 09:42:28,784 - test_probabilistic_inference_engine - INFO - Log evidence (importance_sampling): -1.693
2025-06-16 09:42:28,843 - test_probabilistic_inference_engine - INFO - Log evidence (variational): -6.610
2025-06-16 09:42:28,852 - test_probabilistic_inference_engine - INFO - Log evidence (network): -0.112
2025-06-16 09:42:28,868 - test_probabilistic_inference_engine - INFO - Testing mathematical properties...
2025-06-16 09:42:28,879 - test_probabilistic_inference_engine - INFO - KL divergence: 40.409073
2025-06-16 09:42:28,883 - test_probabilistic_inference_engine - INFO - Mathematical properties test passed
2025-06-16 09:42:28,901 - test_probabilistic_inference_engine - INFO - Testing MCMC sampling...
2025-06-16 09:42:31,012 - test_reverse_diffusion_reasoning - INFO - Training integration test passed
2025-06-16 09:42:31,015 - test_reverse_diffusion_reasoning - INFO - ================================================================================
2025-06-16 09:42:31,015 - test_reverse_diffusion_reasoning - INFO - REVERSE DIFFUSION REASONING TEST SUMMARY
2025-06-16 09:42:31,015 - test_reverse_diffusion_reasoning - INFO - ================================================================================
2025-06-16 09:42:31,016 - test_reverse_diffusion_reasoning - INFO - Tests run: 12
2025-06-16 09:42:31,016 - test_reverse_diffusion_reasoning - INFO - Failures: 0
2025-06-16 09:42:31,016 - test_reverse_diffusion_reasoning - INFO - Errors: 0
2025-06-16 09:42:31,016 - test_reverse_diffusion_reasoning - INFO - Success rate: 100.00%
2025-06-16 09:42:31,017 - __main__ - INFO - Suite reverse_diffusion_reasoning completed in 6.60s - PASSED
2025-06-16 09:42:31,786 - test_probabilistic_inference_engine - INFO - MCMC Diagnostics:
2025-06-16 09:42:31,786 - test_probabilistic_inference_engine - INFO -   Acceptance rate: 0.244
2025-06-16 09:42:31,787 - test_probabilistic_inference_engine - INFO -   Mean error: 0.1088 (tolerance: 0.5485) ✓
2025-06-16 09:42:31,787 - test_probabilistic_inference_engine - INFO -   Cov error: 0.1312 (tolerance: 1.0970) ✓
2025-06-16 09:42:31,787 - test_probabilistic_inference_engine - INFO -   Var error: 0.0549 (tolerance: 0.7757) ✓
2025-06-16 09:42:31,787 - test_probabilistic_inference_engine - INFO -   Effective sample size: 106 (autocorr time: 23.0)
2025-06-16 09:42:31,787 - test_probabilistic_inference_engine - INFO -   Efficiency: 2.1% (effective/total)
2025-06-16 09:42:31,788 - test_probabilistic_inference_engine - INFO -   Test dimension: 8
2025-06-16 09:42:31,788 - test_probabilistic_inference_engine - INFO -   Samples: 5000, Warmup: 2000
2025-06-16 09:42:31,788 - test_probabilistic_inference_engine - INFO -   Mixing ratio: 0.231
2025-06-16 09:42:31,788 - test_probabilistic_inference_engine - INFO - MCMC sampling test passed with proper convergence validation
2025-06-16 09:42:31,793 - test_probabilistic_inference_engine - INFO - Testing normalizing flow...
2025-06-16 09:42:31,810 - test_probabilistic_inference_engine - INFO - Normalizing flow test passed
2025-06-16 09:42:31,824 - test_probabilistic_inference_engine - INFO - Testing performance benchmarks...
2025-06-16 09:42:31,857 - test_probabilistic_inference_engine - INFO - Likelihood computation time: 0.25ms
2025-06-16 09:42:31,859 - test_probabilistic_inference_engine - INFO - Posterior approximation time: 0.74ms
2025-06-16 09:42:31,870 - test_probabilistic_inference_engine - INFO - Testing predictive distribution...
2025-06-16 09:42:31,884 - test_probabilistic_inference_engine - INFO - Predictive mean range: [-3.027, 1.082]
2025-06-16 09:42:31,888 - test_probabilistic_inference_engine - INFO - Predictive std range: [6.834, 8.956]
2025-06-16 09:42:31,896 - test_probabilistic_inference_engine - INFO - Testing prior computation...
2025-06-16 09:42:31,900 - test_probabilistic_inference_engine - INFO - Prior mean norm: 0.000
2025-06-16 09:42:31,901 - test_probabilistic_inference_engine - INFO - Prior covariance trace: 64.000
2025-06-16 09:42:31,916 - test_probabilistic_inference_engine - INFO - Testing uncertainty quantification...
2025-06-16 09:42:31,921 - test_probabilistic_inference_engine - INFO - Epistemic uncertainty mean: 64.0407
2025-06-16 09:42:31,922 - test_probabilistic_inference_engine - INFO - Aleatoric uncertainty mean: 0.1000
2025-06-16 09:42:31,923 - test_probabilistic_inference_engine - INFO - Total uncertainty mean: 64.1407
2025-06-16 09:42:31,932 - test_probabilistic_inference_engine - INFO - Testing variational posterior approximation...
2025-06-16 09:42:31,937 - test_probabilistic_inference_engine - INFO - Posterior-prior mean difference: 1.710
2025-06-16 09:42:31,937 - test_probabilistic_inference_engine - INFO - Variational posterior approximation test passed
2025-06-16 09:42:31,943 - test_probabilistic_inference_engine - INFO - Testing end-to-end Bayesian inference...
2025-06-16 09:42:31,988 - test_probabilistic_inference_engine - INFO - Posterior error: 6.475
2025-06-16 09:42:31,989 - test_probabilistic_inference_engine - INFO - Prior error: 6.242
2025-06-16 09:42:31,989 - test_probabilistic_inference_engine - INFO - Evidence: -0.640
2025-06-16 09:42:31,990 - test_probabilistic_inference_engine - INFO - Posterior did not improve over prior (may be due to limited data)
2025-06-16 09:42:32,002 - test_probabilistic_inference_engine - INFO - Testing model comparison workflow...
2025-06-16 09:42:32,025 - test_probabilistic_inference_engine - INFO - True noise level: 0.2
2025-06-16 09:42:32,026 - test_probabilistic_inference_engine - INFO - Selected noise level: 0.5
2025-06-16 09:42:32,026 - test_probabilistic_inference_engine - INFO - Model probabilities: {'model_0': 0.0, 'model_1': 0.0, 'model_2': 1.0}
2025-06-16 09:42:32,027 - test_probabilistic_inference_engine - INFO - ================================================================================
2025-06-16 09:42:32,028 - test_probabilistic_inference_engine - INFO - PROBABILISTIC INFERENCE ENGINE TEST SUMMARY
2025-06-16 09:42:32,028 - test_probabilistic_inference_engine - INFO - ================================================================================
2025-06-16 09:42:32,029 - test_probabilistic_inference_engine - INFO - Tests run: 14
2025-06-16 09:42:32,030 - test_probabilistic_inference_engine - INFO - Failures: 0
2025-06-16 09:42:32,030 - test_probabilistic_inference_engine - INFO - Errors: 0
2025-06-16 09:42:32,031 - test_probabilistic_inference_engine - INFO - Success rate: 100.00%
2025-06-16 09:42:32,032 - __main__ - INFO - Suite probabilistic_inference completed in 7.61s - PASSED
2025-06-16 09:42:32,141 - __main__ - INFO - Executing priority 3 tests (1 suites)
2025-06-16 09:42:32,161 - __main__ - INFO - Executing test suite: comprehensive_integration
2025-06-16 09:42:34,281 - test_comprehensive_integration - INFO - Integration tests starting on cpu
2025-06-16 09:42:34,281 - test_comprehensive_integration - INFO - Initial resources: {'cpu_percent': 37.8, 'memory_percent': 48.4, 'memory_used_gb': 3.3871002197265625, 'gpu_memory_mb': 0}
2025-06-16 09:42:34,283 - test_comprehensive_integration - INFO - Testing end-to-end scientific reasoning...
2025-06-16 09:42:35,360 - test_comprehensive_integration - INFO - Scientific reasoning completed in 0.08s
2025-06-16 09:42:35,360 - test_comprehensive_integration - INFO - Hypothesis error: 3.4169
2025-06-16 09:42:35,361 - test_comprehensive_integration - INFO - Epistemic uncertainty: 0.9788
2025-06-16 09:42:36,498 - test_comprehensive_integration - INFO - Final resources: {'cpu_percent': 50.8, 'memory_percent': 48.4, 'memory_used_gb': 3.3880157470703125, 'gpu_memory_mb': 0}
2025-06-16 09:42:37,499 - test_comprehensive_integration - INFO - Integration tests starting on cpu
2025-06-16 09:42:37,499 - test_comprehensive_integration - INFO - Initial resources: {'cpu_percent': 45.6, 'memory_percent': 48.5, 'memory_used_gb': 3.3939971923828125, 'gpu_memory_mb': 0}
2025-06-16 09:42:37,499 - test_comprehensive_integration - INFO - Testing analogical reasoning integration...
2025-06-16 09:42:37,520 - test_comprehensive_integration - INFO - Expected analogical relation similarity: 0.908
2025-06-16 09:42:38,527 - test_comprehensive_integration - INFO - Analogical reasoning completed in 0.03s
2025-06-16 09:42:38,528 - test_comprehensive_integration - INFO - Relation similarity: 0.4200
2025-06-16 09:42:38,528 - test_comprehensive_integration - INFO - Target similarity: 0.8950
2025-06-16 09:42:38,529 - test_comprehensive_integration - INFO - Proportional consistency: 0.8897
2025-06-16 09:42:39,623 - test_comprehensive_integration - INFO - Final resources: {'cpu_percent': 37.3, 'memory_percent': 48.6, 'memory_used_gb': 3.4026222229003906, 'gpu_memory_mb': 0}
2025-06-16 09:42:40,624 - test_comprehensive_integration - INFO - Integration tests starting on cpu
2025-06-16 09:42:40,626 - test_comprehensive_integration - INFO - Initial resources: {'cpu_percent': 36.4, 'memory_percent': 48.4, 'memory_used_gb': 3.3884925842285156, 'gpu_memory_mb': 0}
2025-06-16 09:42:40,626 - test_comprehensive_integration - INFO - Testing multi-modal reasoning integration...
2025-06-16 09:42:41,645 - test_comprehensive_integration - INFO - Multi-modal reasoning completed in 0.02s
2025-06-16 09:42:41,646 - test_comprehensive_integration - INFO - Target similarity: 0.9691
2025-06-16 09:42:41,646 - test_comprehensive_integration - INFO - Cross-modal similarity: 0.4937
2025-06-16 09:42:42,733 - test_comprehensive_integration - INFO - Final resources: {'cpu_percent': 38.0, 'memory_percent': 48.4, 'memory_used_gb': 3.389240264892578, 'gpu_memory_mb': 0}
2025-06-16 09:42:43,734 - test_comprehensive_integration - INFO - Integration tests starting on cpu
2025-06-16 09:42:43,735 - test_comprehensive_integration - INFO - Initial resources: {'cpu_percent': 34.0, 'memory_percent': 48.4, 'memory_used_gb': 3.3921585083007812, 'gpu_memory_mb': 0}
2025-06-16 09:42:43,735 - test_comprehensive_integration - INFO - Testing mathematical consistency across components...
2025-06-16 09:42:44,741 - test_comprehensive_integration - INFO - Mathematical consistency test completed in 0.01s
2025-06-16 09:42:44,742 - test_comprehensive_integration - INFO - Overall consistency score: 0.7681
2025-06-16 09:42:44,742 - test_comprehensive_integration - INFO - Individual scores: {'encoding_decoding': 0.5334912237578451, 'diffusion_forward_reverse': 0.538747771315835, 'uncertainty_repeatability': 1.0, 'likelihood_repeatability': 1.0}
2025-06-16 09:42:45,859 - test_comprehensive_integration - INFO - Final resources: {'cpu_percent': 40.7, 'memory_percent': 48.8, 'memory_used_gb': 3.422595977783203, 'gpu_memory_mb': 0}
2025-06-16 09:42:46,860 - test_comprehensive_integration - INFO - Integration tests starting on cpu
2025-06-16 09:42:46,860 - test_comprehensive_integration - INFO - Initial resources: {'cpu_percent': 40.5, 'memory_percent': 48.5, 'memory_used_gb': 3.3958778381347656, 'gpu_memory_mb': 0}
2025-06-16 09:42:46,860 - test_comprehensive_integration - INFO - Testing stress conditions and scalability...
2025-06-16 09:43:00,321 - test_comprehensive_integration - INFO - Stress testing completed in 12.46s
2025-06-16 09:43:00,321 - test_comprehensive_integration - INFO - Performance metrics: {'avg_batch_processing_time': 0.007015633583068848, 'concurrent_processing_time': 0.013561010360717773, 'memory_increase_per_100_samples': -0.00513458251953125, 'reasoning_chain_time': 0.008488655090332031}
2025-06-16 09:43:01,430 - test_comprehensive_integration - INFO - Final resources: {'cpu_percent': 39.5, 'memory_percent': 48.7, 'memory_used_gb': 3.412586212158203, 'gpu_memory_mb': 0}
2025-06-16 09:43:02,432 - test_comprehensive_integration - INFO - Integration tests starting on cpu
2025-06-16 09:43:02,432 - test_comprehensive_integration - INFO - Initial resources: {'cpu_percent': 51.8, 'memory_percent': 49.0, 'memory_used_gb': 3.42559814453125, 'gpu_memory_mb': 0}
2025-06-16 09:43:02,433 - test_comprehensive_integration - INFO - Testing production-grade integration scenarios...
2025-06-16 09:43:03,507 - test_comprehensive_integration - INFO - Production integration completed in 0.07s
2025-06-16 09:43:03,507 - test_comprehensive_integration - INFO - Production metrics: {'avg_processing_time': 0.0022900938987731934, 'avg_reasoning_accuracy': 1.4079133366283618, 'real_time_compliance': 1.0, 'batch_processing_time': 0.025382041931152344, 'avg_quality_score': 0.8, 'error_recovery_rate': 1.0, 'production_readiness_score': 1.0823740009885086}
2025-06-16 09:43:04,609 - test_comprehensive_integration - INFO - Final resources: {'cpu_percent': 44.0, 'memory_percent': 48.8, 'memory_used_gb': 3.4191932678222656, 'gpu_memory_mb': 0}
2025-06-16 09:43:05,613 - test_comprehensive_integration - INFO - Integration tests starting on cpu
2025-06-16 09:43:05,614 - test_comprehensive_integration - INFO - Initial resources: {'cpu_percent': 40.7, 'memory_percent': 48.8, 'memory_used_gb': 3.416370391845703, 'gpu_memory_mb': 0}
2025-06-16 09:43:05,615 - test_comprehensive_integration - INFO - Generating comprehensive test report...
2025-06-16 09:43:05,619 - test_comprehensive_integration - INFO - ================================================================================
2025-06-16 09:43:05,621 - test_comprehensive_integration - INFO - DIFFUSION REASONING INTEGRATION TEST REPORT
2025-06-16 09:43:05,621 - test_comprehensive_integration - INFO - ================================================================================
2025-06-16 09:43:05,622 - test_comprehensive_integration - INFO - Total Tests: 6
2025-06-16 09:43:05,622 - test_comprehensive_integration - INFO - Passed: 6
2025-06-16 09:43:05,623 - test_comprehensive_integration - INFO - Failed: 0
2025-06-16 09:43:05,625 - test_comprehensive_integration - INFO - Success Rate: 100.00%
2025-06-16 09:43:05,625 - test_comprehensive_integration - INFO - Total Execution Time: 12.66s
2025-06-16 09:43:05,626 - test_comprehensive_integration - INFO - Average Execution Time: 2.11s
2025-06-16 09:43:05,626 - test_comprehensive_integration - INFO - Max Memory Usage: 3.41GB
2025-06-16 09:43:05,626 - test_comprehensive_integration - INFO - ================================================================================
2025-06-16 09:43:06,729 - test_comprehensive_integration - INFO - Final resources: {'cpu_percent': 51.8, 'memory_percent': 48.8, 'memory_used_gb': 3.421001434326172, 'gpu_memory_mb': 0}
2025-06-16 09:43:06,730 - test_comprehensive_integration - INFO - ================================================================================
2025-06-16 09:43:06,730 - test_comprehensive_integration - INFO - COMPREHENSIVE INTEGRATION TEST SUMMARY
2025-06-16 09:43:06,730 - test_comprehensive_integration - INFO - ================================================================================
2025-06-16 09:43:06,730 - test_comprehensive_integration - INFO - Tests run: 7
2025-06-16 09:43:06,731 - test_comprehensive_integration - INFO - Failures: 0
2025-06-16 09:43:06,731 - test_comprehensive_integration - INFO - Errors: 0
2025-06-16 09:43:06,731 - test_comprehensive_integration - INFO - Success rate: 100.00%
2025-06-16 09:43:06,732 - __main__ - INFO - Suite comprehensive_integration completed in 34.57s - PASSED
2025-06-16 09:43:06,822 - __main__ - INFO - Generating final test report...
2025-06-16 09:43:06,831 - __main__ - INFO - Detailed report saved to test_results/final_test_report.json
2025-06-16 09:43:06,834 - __main__ - INFO - ================================================================================
2025-06-16 09:43:06,834 - __main__ - INFO - ULTRA DIFFUSION REASONING MODULE - TEST EXECUTION COMPLETE
2025-06-16 09:43:06,834 - __main__ - INFO - ================================================================================
2025-06-16 09:43:06,834 - __main__ - INFO - Execution Summary:
2025-06-16 09:43:06,834 - __main__ - INFO -   Total Test Suites: 6
2025-06-16 09:43:06,834 - __main__ - INFO -   Passed Suites: 5
2025-06-16 09:43:06,835 - __main__ - INFO -   Failed Suites: 1
2025-06-16 09:43:06,836 - __main__ - INFO -   Success Rate: 83.3%
2025-06-16 09:43:06,836 - __main__ - INFO -   Total Execution Time: 1m 43s
2025-06-16 09:43:06,838 - __main__ - INFO - 
Suite Details:
2025-06-16 09:43:06,838 - __main__ - INFO -   conceptual_diffusion: PASSED (27.4s)
2025-06-16 09:43:06,838 - __main__ - INFO -   thought_latent_space: PASSED (60.2s)
2025-06-16 09:43:06,839 - __main__ - INFO -   bayesian_uncertainty: FAILED (0.0s)
2025-06-16 09:43:06,839 - __main__ - INFO -   reverse_diffusion_reasoning: PASSED (6.6s)
2025-06-16 09:43:06,842 - __main__ - INFO -   probabilistic_inference: PASSED (7.6s)
2025-06-16 09:43:06,843 - __main__ - INFO -   comprehensive_integration: PASSED (34.6s)
2025-06-16 09:43:06,845 - __main__ - WARNING - 
Failed Suites:
2025-06-16 09:43:06,846 - __main__ - WARNING -   bayesian_uncertainty: 2 errors
2025-06-16 09:43:06,847 - __main__ - WARNING -     - No module named 'test_bayesian_uncertainty_quantification'...
2025-06-16 09:43:06,849 - __main__ - WARNING -     - Traceback (most recent call last):
  File "/workspaces/Ultra/ultra/ultra/diffusion_reasoning/run_all...
2025-06-16 09:43:06,849 - __main__ - INFO - ================================================================================
2025-06-16 09:43:06,851 - __main__ - WARNING - Most tests passed, but some failures detected
2025-06-16 12:09:55,666 - __main__ - INFO - Setting up test environment...
2025-06-16 12:09:55,668 - __main__ - INFO - System info: {'cpu_count': 2, 'cpu_percent': 59.7, 'memory_total_gb': 7.751644134521484, 'memory_available_gb': 4.568763732910156, 'memory_percent': 41.1, 'gpu_available': False, 'gpu_count': 0, 'gpu_memory_gb': 0}
2025-06-16 12:09:55,669 - __main__ - INFO - Using device: cpu
2025-06-16 12:09:55,669 - __main__ - INFO - Test environment setup complete
2025-06-16 12:09:55,669 - __main__ - INFO - Initialized test orchestrator with 6 test suites
2025-06-16 12:09:55,670 - __main__ - INFO - ================================================================================
2025-06-16 12:09:55,670 - __main__ - INFO - STARTING ULTRA DIFFUSION REASONING MODULE TESTS
2025-06-16 12:09:55,670 - __main__ - INFO - ================================================================================
2025-06-16 12:09:55,670 - __main__ - INFO - Estimated total execution time: 43 minutes 20 seconds
2025-06-16 12:09:55,670 - __main__ - INFO - Running tests in parallel with 4 workers
2025-06-16 12:09:55,670 - __main__ - INFO - Executing priority 1 tests (3 suites)
2025-06-16 12:09:55,691 - __main__ - INFO - Executing test suite: thought_latent_space
2025-06-16 12:09:55,692 - __main__ - INFO - Executing test suite: conceptual_diffusion
2025-06-16 12:09:58,811 - test_thought_latent_space - INFO - Testing compositional operations...
2025-06-16 12:09:58,944 - test_thought_latent_space - INFO - Compositional operations tests passed
2025-06-16 12:09:58,980 - test_thought_latent_space - INFO - Testing encoding-decoding consistency...
2025-06-16 12:09:59,061 - test_thought_latent_space - INFO - Reconstruction error: 0.182883
2025-06-16 12:09:59,100 - test_thought_latent_space - INFO - Testing hierarchical structure preservation...
2025-06-16 12:09:59,202 - test_thought_latent_space - INFO - Training hierarchical structure classifier...
2025-06-16 12:09:59,532 - test_conceptual_diffusion - INFO - Testing conceptual space properties...
2025-06-16 12:09:59,556 - test_conceptual_diffusion - INFO - Within-level similarity: 0.169
2025-06-16 12:09:59,557 - test_conceptual_diffusion - INFO - Across-level similarity: 0.203
2025-06-16 12:09:59,558 - test_conceptual_diffusion - INFO - Semantic similarity preservation: 0.993
2025-06-16 12:09:59,587 - test_conceptual_diffusion - INFO - Testing forward diffusion process...
2025-06-16 12:09:59,596 - test_conceptual_diffusion - INFO - Forward diffusion process tests passed
2025-06-16 12:09:59,651 - test_conceptual_diffusion - INFO - Testing loss computation...
2025-06-16 12:09:59,722 - test_conceptual_diffusion - INFO - Standard loss: 1.189848
2025-06-16 12:09:59,722 - test_conceptual_diffusion - INFO - VLB loss: 82.340813
2025-06-16 12:09:59,750 - test_conceptual_diffusion - INFO - Testing mathematical consistency...
2025-06-16 12:09:59,781 - test_conceptual_diffusion - INFO - Reconstruction error: 0.802756
2025-06-16 12:09:59,809 - test_conceptual_diffusion - INFO - Testing noise schedule properties...
2025-06-16 12:09:59,823 - test_conceptual_diffusion - INFO - Beta range: [0.000100, 0.050000]
2025-06-16 12:09:59,825 - test_conceptual_diffusion - INFO - Alpha cumprod range: [0.999900, 0.078234]
2025-06-16 12:09:59,858 - test_conceptual_diffusion - INFO - Testing performance benchmarks...
2025-06-16 12:10:00,262 - test_conceptual_diffusion - INFO - Forward diffusion time: 0.12ms
2025-06-16 12:10:00,263 - test_conceptual_diffusion - INFO - Reverse diffusion time: 2.10ms
2025-06-16 12:10:00,263 - test_conceptual_diffusion - INFO - Loss computation time: 1.81ms
2025-06-16 12:10:00,292 - test_conceptual_diffusion - INFO - Testing reverse diffusion step...
2025-06-16 12:10:00,296 - test_conceptual_diffusion - INFO - Noise reduction ratio: 1.006
2025-06-16 12:10:00,321 - test_conceptual_diffusion - INFO - Testing sampling process...
2025-06-16 12:10:00,612 - test_conceptual_diffusion - INFO - Initial noise level: 8.374
2025-06-16 12:10:00,615 - test_conceptual_diffusion - INFO - Final noise level: 2.130
2025-06-16 12:10:00,615 - test_conceptual_diffusion - INFO - Noise reduction: 0.746
2025-06-16 12:10:00,655 - test_conceptual_diffusion - INFO - Testing score network properties...
2025-06-16 12:10:00,702 - test_conceptual_diffusion - INFO - Score network output range: [0.000, 0.000]
2025-06-16 12:10:00,758 - test_conceptual_diffusion - INFO - Testing memory efficiency...
2025-06-16 12:10:00,759 - test_conceptual_diffusion - INFO - CUDA not available, skipping memory test
2025-06-16 12:10:00,825 - test_conceptual_diffusion - INFO - Testing training integration...
2025-06-16 12:10:02,911 - test_conceptual_diffusion - INFO - Initial loss: 1.157808
2025-06-16 12:10:02,912 - test_conceptual_diffusion - INFO - Final loss: 1.022987
2025-06-16 12:10:02,912 - test_conceptual_diffusion - INFO - Relative change: 0.116
2025-06-16 12:10:02,913 - test_conceptual_diffusion - INFO - ================================================================================
2025-06-16 12:10:02,914 - test_conceptual_diffusion - INFO - CONCEPTUAL DIFFUSION TEST SUMMARY
2025-06-16 12:10:02,914 - test_conceptual_diffusion - INFO - ================================================================================
2025-06-16 12:10:02,914 - test_conceptual_diffusion - INFO - Tests run: 11
2025-06-16 12:10:02,914 - test_conceptual_diffusion - INFO - Failures: 0
2025-06-16 12:10:02,914 - test_conceptual_diffusion - INFO - Errors: 0
2025-06-16 12:10:02,914 - test_conceptual_diffusion - INFO - Success rate: 100.00%
2025-06-16 12:10:02,918 - __main__ - INFO - Suite conceptual_diffusion completed in 7.23s - PASSED
2025-06-16 12:10:03,056 - test_thought_latent_space - INFO - Epoch 0: Loss=2.3284, Accuracy=0.562
2025-06-16 12:10:03,336 - test_thought_latent_space - INFO - Epoch 1: Loss=1.5374, Accuracy=1.000
2025-06-16 12:10:03,633 - test_thought_latent_space - INFO - Epoch 2: Loss=1.1864, Accuracy=1.000
2025-06-16 12:10:03,926 - test_thought_latent_space - INFO - Epoch 3: Loss=1.0023, Accuracy=1.000
2025-06-16 12:10:04,205 - test_thought_latent_space - INFO - Epoch 4: Loss=0.9144, Accuracy=1.000
2025-06-16 12:10:04,484 - test_thought_latent_space - INFO - Epoch 5: Loss=0.8063, Accuracy=1.000
2025-06-16 12:10:04,805 - test_thought_latent_space - INFO - Epoch 6: Loss=0.7655, Accuracy=1.000
2025-06-16 12:10:05,108 - test_thought_latent_space - INFO - Epoch 7: Loss=0.6952, Accuracy=1.000
2025-06-16 12:10:05,395 - test_thought_latent_space - INFO - Epoch 8: Loss=0.6608, Accuracy=1.000
2025-06-16 12:10:05,674 - test_thought_latent_space - INFO - Epoch 9: Loss=0.6376, Accuracy=1.000
2025-06-16 12:10:08,820 - test_thought_latent_space - INFO - Epoch 20: Loss=0.5716, Accuracy=1.000
2025-06-16 12:10:11,966 - test_thought_latent_space - INFO - Achieved high accuracy 1.000 at epoch 31
2025-06-16 12:10:11,967 - test_thought_latent_space - INFO - Testing trained hierarchical structure...
2025-06-16 12:10:12,094 - test_thought_latent_space - INFO - Final test accuracy: 1.000
2025-06-16 12:10:12,094 - test_thought_latent_space - INFO - Training achieved best accuracy: 1.000
2025-06-16 12:10:12,095 - test_thought_latent_space - INFO - Hierarchical coherence: 1.053
2025-06-16 12:10:12,095 - test_thought_latent_space - INFO - Level prediction accuracy: 1.000
2025-06-16 12:10:12,119 - test_thought_latent_space - INFO - Testing interpolation properties...
2025-06-16 12:10:12,134 - test_thought_latent_space - INFO - Interpolation tests passed
2025-06-16 12:10:12,158 - test_thought_latent_space - INFO - Testing loss computation...
2025-06-16 12:10:12,199 - test_thought_latent_space - INFO - Total loss: 1.092096
2025-06-16 12:10:12,199 - test_thought_latent_space - INFO - Reconstruction loss: 0.164782
2025-06-16 12:10:12,200 - test_thought_latent_space - INFO - Hierarchical loss: 1.047797
2025-06-16 12:10:12,226 - test_thought_latent_space - INFO - Testing manifold structure analysis...
2025-06-16 12:10:12,515 - test_thought_latent_space - INFO - Intrinsic dimensionality: 36
2025-06-16 12:10:12,515 - test_thought_latent_space - INFO - Explained variance ratio: 0.951
2025-06-16 12:10:12,516 - test_thought_latent_space - INFO - Local smoothness: 9.005
2025-06-16 12:10:12,516 - test_thought_latent_space - INFO - Condition number: 999.000
2025-06-16 12:10:12,550 - test_thought_latent_space - INFO - Testing memory operations...
2025-06-16 12:10:12,576 - test_thought_latent_space - INFO - Memory operations tests passed
2025-06-16 12:10:12,596 - test_thought_latent_space - INFO - Testing performance benchmarks...
2025-06-16 12:10:22,074 - test_thought_latent_space - INFO - Single encoding time: 2.20ms
2025-06-16 12:10:22,074 - test_thought_latent_space - INFO - Batch processing time: 41.89ms
2025-06-16 12:10:22,098 - test_thought_latent_space - INFO - Testing relational vector computation...
2025-06-16 12:10:22,117 - test_thought_latent_space - INFO - Relational vector tests passed
2025-06-16 12:10:22,137 - test_thought_latent_space - INFO - Testing semantic similarity computation...
2025-06-16 12:10:22,252 - test_thought_latent_space - INFO - Same cluster similarity: 0.475
2025-06-16 12:10:22,252 - test_thought_latent_space - INFO - Different cluster similarity: 0.474
2025-06-16 12:10:22,272 - test_thought_latent_space - INFO - Testing integration with diffusion process...
2025-06-16 12:10:22,329 - test_thought_latent_space - INFO - Noise level 0.1: degradation 0.2024
2025-06-16 12:10:22,334 - test_thought_latent_space - INFO - Noise level 0.3: degradation 0.2002
2025-06-16 12:10:22,338 - test_thought_latent_space - INFO - Noise level 0.5: degradation 0.1952
2025-06-16 12:10:22,342 - test_thought_latent_space - INFO - Noise level 0.8: degradation 0.1964
2025-06-16 12:10:22,343 - test_thought_latent_space - INFO - Diffusion integration test passed
2025-06-16 12:10:22,362 - test_thought_latent_space - INFO - Testing training integration...
2025-06-16 12:10:22,994 - test_thought_latent_space - INFO - Initial loss: 1.014152
2025-06-16 12:10:22,994 - test_thought_latent_space - INFO - Final loss: 0.636176
2025-06-16 12:10:22,994 - test_thought_latent_space - INFO - Loss ratio: 0.627
2025-06-16 12:10:22,995 - test_thought_latent_space - INFO - ================================================================================
2025-06-16 12:10:22,995 - test_thought_latent_space - INFO - THOUGHT LATENT SPACE TEST SUMMARY
2025-06-16 12:10:22,995 - test_thought_latent_space - INFO - ================================================================================
2025-06-16 12:10:22,995 - test_thought_latent_space - INFO - Tests run: 12
2025-06-16 12:10:22,995 - test_thought_latent_space - INFO - Failures: 0
2025-06-16 12:10:22,995 - test_thought_latent_space - INFO - Errors: 0
2025-06-16 12:10:22,995 - test_thought_latent_space - INFO - Success rate: 100.00%
2025-06-16 12:10:22,997 - __main__ - INFO - Suite thought_latent_space completed in 27.30s - PASSED
2025-06-16 12:10:23,037 - __main__ - INFO - Executing test suite: bayesian_uncertainty
2025-06-16 12:10:23,038 - __main__ - ERROR - Suite bayesian_uncertainty failed with exception: No module named 'test_bayesian_uncertainty_quantification'
2025-06-16 12:10:23,038 - __main__ - ERROR - Traceback (most recent call last):
  File "/workspaces/Ultra/ultra/ultra/diffusion_reasoning/run_all_reasoning_diffusion_tests.py", line 339, in execute_test_suite
    module = __import__(module_name)
             ^^^^^^^^^^^^^^^^^^^^^^^
ModuleNotFoundError: No module named 'test_bayesian_uncertainty_quantification'

2025-06-16 12:10:23,084 - __main__ - INFO - Executing priority 2 tests (2 suites)
2025-06-16 12:10:23,101 - __main__ - INFO - Executing test suite: reverse_diffusion_reasoning
2025-06-16 12:10:23,103 - __main__ - INFO - Executing test suite: probabilistic_inference
2025-06-16 12:10:24,682 - test_reverse_diffusion_reasoning - INFO - Testing analogical reasoning...
2025-06-16 12:10:24,687 - test_reverse_diffusion_reasoning - INFO - Analogical relation similarity: 0.999
2025-06-16 12:10:24,694 - test_reverse_diffusion_reasoning - INFO - Testing constraint satisfaction scoring...
2025-06-16 12:10:24,695 - test_reverse_diffusion_reasoning - INFO - Constraint satisfaction score: 0.167
2025-06-16 12:10:24,695 - test_reverse_diffusion_reasoning - INFO - Perfect constraint score: 1.000
2025-06-16 12:10:24,705 - test_reverse_diffusion_reasoning - INFO - Testing guidance function properties...
2025-06-16 12:10:24,710 - test_reverse_diffusion_reasoning - INFO - Guidance function tests passed
2025-06-16 12:10:24,723 - test_reverse_diffusion_reasoning - INFO - Testing guided reverse step...
2025-06-16 12:10:24,732 - test_reverse_diffusion_reasoning - INFO - Samples improved: 8/8
2025-06-16 12:10:24,733 - test_reverse_diffusion_reasoning - INFO - Average distance change: 9.841
2025-06-16 12:10:24,745 - test_reverse_diffusion_reasoning - INFO - Testing interpolative reasoning...
2025-06-16 12:10:24,830 - test_reverse_diffusion_reasoning - INFO - Similarity trend to A: -0.237
2025-06-16 12:10:24,832 - test_reverse_diffusion_reasoning - INFO - Similarity trend to B: 0.254
2025-06-16 12:10:24,853 - test_reverse_diffusion_reasoning - INFO - Testing mathematical properties...
2025-06-16 12:10:24,856 - test_reverse_diffusion_reasoning - INFO - Guided computation difference: 6.7128
2025-06-16 12:10:24,864 - test_reverse_diffusion_reasoning - INFO - Testing multi-constraint reasoning...
2025-06-16 12:10:24,895 - test_reverse_diffusion_reasoning - INFO - Constraints improved: 2/2
2025-06-16 12:10:24,896 - test_reverse_diffusion_reasoning - INFO - Overall satisfaction - Initial: -67.550, Final: -62.318
2025-06-16 12:10:24,896 - test_reverse_diffusion_reasoning - INFO - Process completed successfully with trajectory variance: 3.732373
2025-06-16 12:10:24,908 - test_reverse_diffusion_reasoning - INFO - Testing performance benchmarks...
2025-06-16 12:10:25,062 - test_reverse_diffusion_reasoning - INFO - Single step time: 0.74ms
2025-06-16 12:10:25,064 - test_reverse_diffusion_reasoning - INFO - Full reasoning time: 7.42ms
2025-06-16 12:10:25,078 - test_reverse_diffusion_reasoning - INFO - Testing goal-directed reasoning...
2025-06-16 12:10:25,105 - test_reverse_diffusion_reasoning - INFO - Convergence ratio: 4.826
2025-06-16 12:10:25,109 - test_reverse_diffusion_reasoning - INFO - Final confidence: 0.000
2025-06-16 12:10:25,109 - test_reverse_diffusion_reasoning - INFO - Initial confidence: 0.000
2025-06-16 12:10:25,110 - test_reverse_diffusion_reasoning - INFO - Path variance: 27.699892
2025-06-16 12:10:25,110 - test_reverse_diffusion_reasoning - INFO - Confidence ratio: 0.000
2025-06-16 12:10:25,121 - test_reverse_diffusion_reasoning - INFO - Testing reasoning trajectory analysis...
2025-06-16 12:10:25,131 - test_reverse_diffusion_reasoning - INFO - Convergence rate: 0.838
2025-06-16 12:10:25,133 - test_reverse_diffusion_reasoning - INFO - Monotonicity: 1.000
2025-06-16 12:10:25,134 - test_reverse_diffusion_reasoning - INFO - Final distance: 1.515
2025-06-16 12:10:25,148 - test_reverse_diffusion_reasoning - INFO - Testing end-to-end reasoning...
2025-06-16 12:10:25,199 - test_reverse_diffusion_reasoning - INFO - End-to-end reasoning test passed
2025-06-16 12:10:25,212 - test_reverse_diffusion_reasoning - INFO - Testing training integration...
2025-06-16 12:10:25,225 - test_probabilistic_inference_engine - INFO - Testing Bayesian model selection...
2025-06-16 12:10:25,248 - test_probabilistic_inference_engine - INFO - Model probabilities: {'model_0': 0.0, 'model_1': 0.0, 'model_2': 1.0}
2025-06-16 12:10:25,256 - test_probabilistic_inference_engine - INFO - Testing information-theoretic measures...
2025-06-16 12:10:25,313 - test_probabilistic_inference_engine - INFO - Entropy: 4.605
2025-06-16 12:10:25,314 - test_probabilistic_inference_engine - INFO - Mutual information: 2.303
2025-06-16 12:10:25,315 - test_probabilistic_inference_engine - INFO - Effective dimensionality: 53.061
2025-06-16 12:10:25,329 - test_probabilistic_inference_engine - INFO - Testing likelihood computation...
2025-06-16 12:10:25,334 - test_probabilistic_inference_engine - INFO - Log likelihood range: [-0.418, 0.099]
2025-06-16 12:10:25,347 - test_probabilistic_inference_engine - INFO - Testing marginal likelihood computation...
2025-06-16 12:10:25,404 - test_probabilistic_inference_engine - INFO - Log evidence (importance_sampling): 4.074
2025-06-16 12:10:25,481 - test_probabilistic_inference_engine - INFO - Log evidence (variational): 0.116
2025-06-16 12:10:25,485 - test_probabilistic_inference_engine - INFO - Log evidence (network): 0.027
2025-06-16 12:10:25,501 - test_probabilistic_inference_engine - INFO - Testing mathematical properties...
2025-06-16 12:10:25,517 - test_probabilistic_inference_engine - INFO - KL divergence: 32.782810
2025-06-16 12:10:25,518 - test_probabilistic_inference_engine - INFO - Mathematical properties test passed
2025-06-16 12:10:25,529 - test_probabilistic_inference_engine - INFO - Testing MCMC sampling...
2025-06-16 12:10:26,805 - test_reverse_diffusion_reasoning - INFO - Training integration test passed
2025-06-16 12:10:26,806 - test_reverse_diffusion_reasoning - INFO - ================================================================================
2025-06-16 12:10:26,806 - test_reverse_diffusion_reasoning - INFO - REVERSE DIFFUSION REASONING TEST SUMMARY
2025-06-16 12:10:26,806 - test_reverse_diffusion_reasoning - INFO - ================================================================================
2025-06-16 12:10:26,806 - test_reverse_diffusion_reasoning - INFO - Tests run: 12
2025-06-16 12:10:26,807 - test_reverse_diffusion_reasoning - INFO - Failures: 0
2025-06-16 12:10:26,807 - test_reverse_diffusion_reasoning - INFO - Errors: 0
2025-06-16 12:10:26,807 - test_reverse_diffusion_reasoning - INFO - Success rate: 100.00%
2025-06-16 12:10:26,808 - __main__ - INFO - Suite reverse_diffusion_reasoning completed in 3.71s - PASSED
2025-06-16 12:10:27,305 - test_probabilistic_inference_engine - INFO - MCMC Diagnostics:
2025-06-16 12:10:27,305 - test_probabilistic_inference_engine - INFO -   Acceptance rate: 0.254
2025-06-16 12:10:27,305 - test_probabilistic_inference_engine - INFO -   Mean error: 0.1583 (tolerance: 0.5836) ✓
2025-06-16 12:10:27,306 - test_probabilistic_inference_engine - INFO -   Cov error: 0.1049 (tolerance: 1.1671) ✓
2025-06-16 12:10:27,306 - test_probabilistic_inference_engine - INFO -   Var error: 0.0570 (tolerance: 0.8253) ✓
2025-06-16 12:10:27,306 - test_probabilistic_inference_engine - INFO -   Effective sample size: 94 (autocorr time: 26.1)
2025-06-16 12:10:27,306 - test_probabilistic_inference_engine - INFO -   Efficiency: 1.9% (effective/total)
2025-06-16 12:10:27,306 - test_probabilistic_inference_engine - INFO -   Test dimension: 8
2025-06-16 12:10:27,306 - test_probabilistic_inference_engine - INFO -   Samples: 5000, Warmup: 2000
2025-06-16 12:10:27,306 - test_probabilistic_inference_engine - INFO -   Mixing ratio: 0.244
2025-06-16 12:10:27,306 - test_probabilistic_inference_engine - INFO - MCMC sampling test passed with proper convergence validation
2025-06-16 12:10:27,312 - test_probabilistic_inference_engine - INFO - Testing normalizing flow...
2025-06-16 12:10:27,326 - test_probabilistic_inference_engine - INFO - Normalizing flow test passed
2025-06-16 12:10:27,331 - test_probabilistic_inference_engine - INFO - Testing performance benchmarks...
2025-06-16 12:10:27,359 - test_probabilistic_inference_engine - INFO - Likelihood computation time: 0.21ms
2025-06-16 12:10:27,359 - test_probabilistic_inference_engine - INFO - Posterior approximation time: 0.69ms
2025-06-16 12:10:27,363 - test_probabilistic_inference_engine - INFO - Testing predictive distribution...
2025-06-16 12:10:27,371 - test_probabilistic_inference_engine - INFO - Predictive mean range: [-2.374, 1.061]
2025-06-16 12:10:27,371 - test_probabilistic_inference_engine - INFO - Predictive std range: [7.297, 9.869]
2025-06-16 12:10:27,376 - test_probabilistic_inference_engine - INFO - Testing prior computation...
2025-06-16 12:10:27,379 - test_probabilistic_inference_engine - INFO - Prior mean norm: 0.000
2025-06-16 12:10:27,380 - test_probabilistic_inference_engine - INFO - Prior covariance trace: 64.000
2025-06-16 12:10:27,385 - test_probabilistic_inference_engine - INFO - Testing uncertainty quantification...
2025-06-16 12:10:27,389 - test_probabilistic_inference_engine - INFO - Epistemic uncertainty mean: 65.1673
2025-06-16 12:10:27,389 - test_probabilistic_inference_engine - INFO - Aleatoric uncertainty mean: 0.1000
2025-06-16 12:10:27,389 - test_probabilistic_inference_engine - INFO - Total uncertainty mean: 65.2673
2025-06-16 12:10:27,394 - test_probabilistic_inference_engine - INFO - Testing variational posterior approximation...
2025-06-16 12:10:27,396 - test_probabilistic_inference_engine - INFO - Posterior-prior mean difference: 1.941
2025-06-16 12:10:27,396 - test_probabilistic_inference_engine - INFO - Variational posterior approximation test passed
2025-06-16 12:10:27,402 - test_probabilistic_inference_engine - INFO - Testing end-to-end Bayesian inference...
2025-06-16 12:10:27,432 - test_probabilistic_inference_engine - INFO - Posterior error: 5.654
2025-06-16 12:10:27,432 - test_probabilistic_inference_engine - INFO - Prior error: 5.473
2025-06-16 12:10:27,433 - test_probabilistic_inference_engine - INFO - Evidence: -4.409
2025-06-16 12:10:27,433 - test_probabilistic_inference_engine - INFO - Posterior did not improve over prior (may be due to limited data)
2025-06-16 12:10:27,439 - test_probabilistic_inference_engine - INFO - Testing model comparison workflow...
2025-06-16 12:10:27,455 - test_probabilistic_inference_engine - INFO - True noise level: 0.2
2025-06-16 12:10:27,455 - test_probabilistic_inference_engine - INFO - Selected noise level: 0.5
2025-06-16 12:10:27,455 - test_probabilistic_inference_engine - INFO - Model probabilities: {'model_0': 0.0, 'model_1': 0.0, 'model_2': 1.0}
2025-06-16 12:10:27,455 - test_probabilistic_inference_engine - INFO - ================================================================================
2025-06-16 12:10:27,456 - test_probabilistic_inference_engine - INFO - PROBABILISTIC INFERENCE ENGINE TEST SUMMARY
2025-06-16 12:10:27,456 - test_probabilistic_inference_engine - INFO - ================================================================================
2025-06-16 12:10:27,456 - test_probabilistic_inference_engine - INFO - Tests run: 14
2025-06-16 12:10:27,456 - test_probabilistic_inference_engine - INFO - Failures: 0
2025-06-16 12:10:27,456 - test_probabilistic_inference_engine - INFO - Errors: 0
2025-06-16 12:10:27,456 - test_probabilistic_inference_engine - INFO - Success rate: 100.00%
2025-06-16 12:10:27,457 - __main__ - INFO - Suite probabilistic_inference completed in 4.35s - PASSED
2025-06-16 12:10:27,544 - __main__ - INFO - Executing priority 3 tests (1 suites)
2025-06-16 12:10:27,565 - __main__ - INFO - Executing test suite: comprehensive_integration
2025-06-16 12:10:29,307 - test_comprehensive_integration - INFO - Integration tests starting on cpu
2025-06-16 12:10:29,307 - test_comprehensive_integration - INFO - Initial resources: {'cpu_percent': 1.5, 'memory_percent': 42.7, 'memory_used_gb': 2.9420013427734375, 'gpu_memory_mb': 0}
2025-06-16 12:10:29,307 - test_comprehensive_integration - INFO - Testing end-to-end scientific reasoning...
2025-06-16 12:10:30,375 - test_comprehensive_integration - INFO - Scientific reasoning completed in 0.07s
2025-06-16 12:10:30,375 - test_comprehensive_integration - INFO - Hypothesis error: 3.6228
2025-06-16 12:10:30,375 - test_comprehensive_integration - INFO - Epistemic uncertainty: 0.9829
2025-06-16 12:10:31,444 - test_comprehensive_integration - INFO - Final resources: {'cpu_percent': 2.0, 'memory_percent': 42.9, 'memory_used_gb': 2.9644088745117188, 'gpu_memory_mb': 0}
2025-06-16 12:10:32,445 - test_comprehensive_integration - INFO - Integration tests starting on cpu
2025-06-16 12:10:32,445 - test_comprehensive_integration - INFO - Initial resources: {'cpu_percent': 12.6, 'memory_percent': 43.0, 'memory_used_gb': 2.967975616455078, 'gpu_memory_mb': 0}
2025-06-16 12:10:32,445 - test_comprehensive_integration - INFO - Testing analogical reasoning integration...
2025-06-16 12:10:32,463 - test_comprehensive_integration - INFO - Expected analogical relation similarity: 0.908
2025-06-16 12:10:33,470 - test_comprehensive_integration - INFO - Analogical reasoning completed in 0.02s
2025-06-16 12:10:33,470 - test_comprehensive_integration - INFO - Relation similarity: 0.3832
2025-06-16 12:10:33,470 - test_comprehensive_integration - INFO - Target similarity: 0.8893
2025-06-16 12:10:33,470 - test_comprehensive_integration - INFO - Proportional consistency: 0.8617
2025-06-16 12:10:34,532 - test_comprehensive_integration - INFO - Final resources: {'cpu_percent': 6.5, 'memory_percent': 42.9, 'memory_used_gb': 2.9643516540527344, 'gpu_memory_mb': 0}
2025-06-16 12:10:35,532 - test_comprehensive_integration - INFO - Integration tests starting on cpu
2025-06-16 12:10:35,533 - test_comprehensive_integration - INFO - Initial resources: {'cpu_percent': 16.2, 'memory_percent': 43.3, 'memory_used_gb': 2.994579315185547, 'gpu_memory_mb': 0}
2025-06-16 12:10:35,533 - test_comprehensive_integration - INFO - Testing multi-modal reasoning integration...
2025-06-16 12:10:36,541 - test_comprehensive_integration - INFO - Multi-modal reasoning completed in 0.01s
2025-06-16 12:10:36,541 - test_comprehensive_integration - INFO - Target similarity: 0.9678
2025-06-16 12:10:36,541 - test_comprehensive_integration - INFO - Cross-modal similarity: 0.5040
2025-06-16 12:10:37,600 - test_comprehensive_integration - INFO - Final resources: {'cpu_percent': 4.0, 'memory_percent': 43.3, 'memory_used_gb': 2.9946327209472656, 'gpu_memory_mb': 0}
2025-06-16 12:10:38,600 - test_comprehensive_integration - INFO - Integration tests starting on cpu
2025-06-16 12:10:38,601 - test_comprehensive_integration - INFO - Initial resources: {'cpu_percent': 4.5, 'memory_percent': 43.3, 'memory_used_gb': 2.994403839111328, 'gpu_memory_mb': 0}
2025-06-16 12:10:38,601 - test_comprehensive_integration - INFO - Testing mathematical consistency across components...
2025-06-16 12:10:39,605 - test_comprehensive_integration - INFO - Mathematical consistency test completed in 0.00s
2025-06-16 12:10:39,606 - test_comprehensive_integration - INFO - Overall consistency score: 0.7681
2025-06-16 12:10:39,606 - test_comprehensive_integration - INFO - Individual scores: {'encoding_decoding': 0.5334912067935956, 'diffusion_forward_reverse': 0.5387427024054711, 'uncertainty_repeatability': 1.0, 'likelihood_repeatability': 1.0}
2025-06-16 12:10:40,664 - test_comprehensive_integration - INFO - Final resources: {'cpu_percent': 10.4, 'memory_percent': 43.3, 'memory_used_gb': 2.994312286376953, 'gpu_memory_mb': 0}
2025-06-16 12:10:41,665 - test_comprehensive_integration - INFO - Integration tests starting on cpu
2025-06-16 12:10:41,665 - test_comprehensive_integration - INFO - Initial resources: {'cpu_percent': 5.1, 'memory_percent': 43.2, 'memory_used_gb': 2.98651123046875, 'gpu_memory_mb': 0}
2025-06-16 12:10:41,665 - test_comprehensive_integration - INFO - Testing stress conditions and scalability...
2025-06-16 12:10:54,975 - test_comprehensive_integration - INFO - Stress testing completed in 12.31s
2025-06-16 12:10:54,975 - test_comprehensive_integration - INFO - Performance metrics: {'avg_batch_processing_time': 0.003983020782470703, 'concurrent_processing_time': 0.006065845489501953, 'memory_increase_per_100_samples': -0.000579833984375, 'reasoning_chain_time': 0.004976511001586914}
2025-06-16 12:10:56,047 - test_comprehensive_integration - INFO - Final resources: {'cpu_percent': 8.5, 'memory_percent': 43.3, 'memory_used_gb': 2.995319366455078, 'gpu_memory_mb': 0}
2025-06-16 12:10:57,048 - test_comprehensive_integration - INFO - Integration tests starting on cpu
2025-06-16 12:10:57,048 - test_comprehensive_integration - INFO - Initial resources: {'cpu_percent': 13.6, 'memory_percent': 43.4, 'memory_used_gb': 2.9994583129882812, 'gpu_memory_mb': 0}
2025-06-16 12:10:57,049 - test_comprehensive_integration - INFO - Testing production-grade integration scenarios...
2025-06-16 12:10:58,084 - test_comprehensive_integration - INFO - Production integration completed in 0.03s
2025-06-16 12:10:58,084 - test_comprehensive_integration - INFO - Production metrics: {'avg_processing_time': 0.0011408090591430663, 'avg_reasoning_accuracy': 1.4079138730701648, 'real_time_compliance': 1.0, 'batch_processing_time': 0.010267972946166992, 'avg_quality_score': 0.8, 'error_recovery_rate': 1.0, 'production_readiness_score': 1.0823741619210494}
2025-06-16 12:10:59,149 - test_comprehensive_integration - INFO - Final resources: {'cpu_percent': 1.0, 'memory_percent': 43.4, 'memory_used_gb': 2.9990158081054688, 'gpu_memory_mb': 0}
2025-06-16 12:11:00,149 - test_comprehensive_integration - INFO - Integration tests starting on cpu
2025-06-16 12:11:00,150 - test_comprehensive_integration - INFO - Initial resources: {'cpu_percent': 5.5, 'memory_percent': 43.4, 'memory_used_gb': 2.9988861083984375, 'gpu_memory_mb': 0}
2025-06-16 12:11:00,150 - test_comprehensive_integration - INFO - Generating comprehensive test report...
2025-06-16 12:11:00,151 - test_comprehensive_integration - INFO - ================================================================================
2025-06-16 12:11:00,151 - test_comprehensive_integration - INFO - DIFFUSION REASONING INTEGRATION TEST REPORT
2025-06-16 12:11:00,151 - test_comprehensive_integration - INFO - ================================================================================
2025-06-16 12:11:00,151 - test_comprehensive_integration - INFO - Total Tests: 6
2025-06-16 12:11:00,152 - test_comprehensive_integration - INFO - Passed: 6
2025-06-16 12:11:00,152 - test_comprehensive_integration - INFO - Failed: 0
2025-06-16 12:11:00,152 - test_comprehensive_integration - INFO - Success Rate: 100.00%
2025-06-16 12:11:00,152 - test_comprehensive_integration - INFO - Total Execution Time: 12.45s
2025-06-16 12:11:00,152 - test_comprehensive_integration - INFO - Average Execution Time: 2.07s
2025-06-16 12:11:00,152 - test_comprehensive_integration - INFO - Max Memory Usage: 3.00GB
2025-06-16 12:11:00,152 - test_comprehensive_integration - INFO - ================================================================================
2025-06-16 12:11:01,217 - test_comprehensive_integration - INFO - Final resources: {'cpu_percent': 19.0, 'memory_percent': 43.4, 'memory_used_gb': 2.9986038208007812, 'gpu_memory_mb': 0}
2025-06-16 12:11:01,217 - test_comprehensive_integration - INFO - ================================================================================
2025-06-16 12:11:01,218 - test_comprehensive_integration - INFO - COMPREHENSIVE INTEGRATION TEST SUMMARY
2025-06-16 12:11:01,218 - test_comprehensive_integration - INFO - ================================================================================
2025-06-16 12:11:01,218 - test_comprehensive_integration - INFO - Tests run: 7
2025-06-16 12:11:01,218 - test_comprehensive_integration - INFO - Failures: 0
2025-06-16 12:11:01,218 - test_comprehensive_integration - INFO - Errors: 0
2025-06-16 12:11:01,218 - test_comprehensive_integration - INFO - Success rate: 100.00%
2025-06-16 12:11:01,218 - __main__ - INFO - Suite comprehensive_integration completed in 33.65s - PASSED
2025-06-16 12:11:01,280 - __main__ - INFO - Generating final test report...
2025-06-16 12:11:01,282 - __main__ - INFO - Detailed report saved to test_results/final_test_report.json
2025-06-16 12:11:01,283 - __main__ - INFO - ================================================================================
2025-06-16 12:11:01,283 - __main__ - INFO - ULTRA DIFFUSION REASONING MODULE - TEST EXECUTION COMPLETE
2025-06-16 12:11:01,283 - __main__ - INFO - ================================================================================
2025-06-16 12:11:01,283 - __main__ - INFO - Execution Summary:
2025-06-16 12:11:01,283 - __main__ - INFO -   Total Test Suites: 6
2025-06-16 12:11:01,284 - __main__ - INFO -   Passed Suites: 5
2025-06-16 12:11:01,284 - __main__ - INFO -   Failed Suites: 1
2025-06-16 12:11:01,284 - __main__ - INFO -   Success Rate: 83.3%
2025-06-16 12:11:01,284 - __main__ - INFO -   Total Execution Time: 1m 6s
2025-06-16 12:11:01,284 - __main__ - INFO - 
Suite Details:
2025-06-16 12:11:01,285 - __main__ - INFO -   conceptual_diffusion: PASSED (7.2s)
2025-06-16 12:11:01,285 - __main__ - INFO -   thought_latent_space: PASSED (27.3s)
2025-06-16 12:11:01,285 - __main__ - INFO -   bayesian_uncertainty: FAILED (0.0s)
2025-06-16 12:11:01,285 - __main__ - INFO -   reverse_diffusion_reasoning: PASSED (3.7s)
2025-06-16 12:11:01,286 - __main__ - INFO -   probabilistic_inference: PASSED (4.4s)
2025-06-16 12:11:01,286 - __main__ - INFO -   comprehensive_integration: PASSED (33.7s)
2025-06-16 12:11:01,286 - __main__ - WARNING - 
Failed Suites:
2025-06-16 12:11:01,286 - __main__ - WARNING -   bayesian_uncertainty: 2 errors
2025-06-16 12:11:01,288 - __main__ - WARNING -     - No module named 'test_bayesian_uncertainty_quantification'...
2025-06-16 12:11:01,288 - __main__ - WARNING -     - Traceback (most recent call last):
  File "/workspaces/Ultra/ultra/ultra/diffusion_reasoning/run_all...
2025-06-16 12:11:01,289 - __main__ - INFO - ================================================================================
2025-06-16 12:11:01,289 - __main__ - WARNING - Most tests passed, but some failures detected
2025-06-16 13:34:50,826 - __main__ - INFO - Setting up test environment...
2025-06-16 13:34:50,830 - __main__ - INFO - System info: {'cpu_count': 2, 'cpu_percent': 62.8, 'memory_total_gb': 7.751636505126953, 'memory_available_gb': 4.502052307128906, 'memory_percent': 41.9, 'gpu_available': False, 'gpu_count': 0, 'gpu_memory_gb': 0}
2025-06-16 13:34:50,831 - __main__ - INFO - Using device: cpu
2025-06-16 13:34:50,831 - __main__ - INFO - Test environment setup complete
2025-06-16 13:34:50,831 - __main__ - INFO - Initialized test orchestrator with 6 test suites
2025-06-16 13:34:50,831 - __main__ - INFO - ================================================================================
2025-06-16 13:34:50,831 - __main__ - INFO - STARTING ULTRA DIFFUSION REASONING MODULE TESTS
2025-06-16 13:34:50,832 - __main__ - INFO - ================================================================================
2025-06-16 13:34:50,832 - __main__ - INFO - Estimated total execution time: 43 minutes 20 seconds
2025-06-16 13:34:50,832 - __main__ - INFO - Running tests in parallel with 4 workers
2025-06-16 13:34:50,832 - __main__ - INFO - Executing priority 1 tests (3 suites)
2025-06-16 13:34:50,851 - __main__ - INFO - Executing test suite: conceptual_diffusion
2025-06-16 13:34:50,852 - __main__ - INFO - Executing test suite: thought_latent_space
2025-06-16 13:34:54,267 - test_thought_latent_space - INFO - Testing compositional operations...
2025-06-16 13:34:54,461 - test_thought_latent_space - INFO - Compositional operations tests passed
2025-06-16 13:34:54,498 - test_thought_latent_space - INFO - Testing encoding-decoding consistency...
2025-06-16 13:34:54,589 - test_thought_latent_space - INFO - Reconstruction error: 0.186149
2025-06-16 13:34:54,623 - test_thought_latent_space - INFO - Testing hierarchical structure preservation...
2025-06-16 13:34:54,729 - test_thought_latent_space - INFO - Training hierarchical structure classifier...
2025-06-16 13:34:54,791 - test_conceptual_diffusion - INFO - Testing conceptual space properties...
2025-06-16 13:34:54,809 - test_conceptual_diffusion - INFO - Within-level similarity: 0.174
2025-06-16 13:34:54,810 - test_conceptual_diffusion - INFO - Across-level similarity: 0.218
2025-06-16 13:34:54,810 - test_conceptual_diffusion - INFO - Semantic similarity preservation: 0.993
2025-06-16 13:34:54,851 - test_conceptual_diffusion - INFO - Testing forward diffusion process...
2025-06-16 13:34:54,874 - test_conceptual_diffusion - INFO - Forward diffusion process tests passed
2025-06-16 13:34:54,900 - test_conceptual_diffusion - INFO - Testing loss computation...
2025-06-16 13:34:54,942 - test_conceptual_diffusion - INFO - Standard loss: 1.420443
2025-06-16 13:34:54,942 - test_conceptual_diffusion - INFO - VLB loss: 84.626549
2025-06-16 13:34:54,977 - test_conceptual_diffusion - INFO - Testing mathematical consistency...
2025-06-16 13:34:55,027 - test_conceptual_diffusion - INFO - Reconstruction error: 0.766616
2025-06-16 13:34:55,063 - test_conceptual_diffusion - INFO - Testing noise schedule properties...
2025-06-16 13:34:55,081 - test_conceptual_diffusion - INFO - Beta range: [0.000100, 0.050000]
2025-06-16 13:34:55,083 - test_conceptual_diffusion - INFO - Alpha cumprod range: [0.999900, 0.078234]
2025-06-16 13:34:55,118 - test_conceptual_diffusion - INFO - Testing performance benchmarks...
2025-06-16 13:34:55,531 - test_conceptual_diffusion - INFO - Forward diffusion time: 0.14ms
2025-06-16 13:34:55,532 - test_conceptual_diffusion - INFO - Reverse diffusion time: 2.13ms
2025-06-16 13:34:55,532 - test_conceptual_diffusion - INFO - Loss computation time: 1.86ms
2025-06-16 13:34:55,559 - test_conceptual_diffusion - INFO - Testing reverse diffusion step...
2025-06-16 13:34:55,563 - test_conceptual_diffusion - INFO - Noise reduction ratio: 0.999
2025-06-16 13:34:55,591 - test_conceptual_diffusion - INFO - Testing sampling process...
2025-06-16 13:34:55,895 - test_conceptual_diffusion - INFO - Initial noise level: 7.848
2025-06-16 13:34:55,896 - test_conceptual_diffusion - INFO - Final noise level: 2.192
2025-06-16 13:34:55,896 - test_conceptual_diffusion - INFO - Noise reduction: 0.721
2025-06-16 13:34:55,938 - test_conceptual_diffusion - INFO - Testing score network properties...
2025-06-16 13:34:55,978 - test_conceptual_diffusion - INFO - Score network output range: [0.000, 0.000]
2025-06-16 13:34:56,027 - test_conceptual_diffusion - INFO - Testing memory efficiency...
2025-06-16 13:34:56,028 - test_conceptual_diffusion - INFO - CUDA not available, skipping memory test
2025-06-16 13:34:56,088 - test_conceptual_diffusion - INFO - Testing training integration...
2025-06-16 13:34:57,995 - test_conceptual_diffusion - INFO - Initial loss: 1.236551
2025-06-16 13:34:57,996 - test_conceptual_diffusion - INFO - Final loss: 1.075525
2025-06-16 13:34:57,996 - test_conceptual_diffusion - INFO - Relative change: 0.130
2025-06-16 13:34:58,000 - test_conceptual_diffusion - INFO - ================================================================================
2025-06-16 13:34:58,000 - test_conceptual_diffusion - INFO - CONCEPTUAL DIFFUSION TEST SUMMARY
2025-06-16 13:34:58,001 - test_conceptual_diffusion - INFO - ================================================================================
2025-06-16 13:34:58,001 - test_conceptual_diffusion - INFO - Tests run: 11
2025-06-16 13:34:58,002 - test_conceptual_diffusion - INFO - Failures: 0
2025-06-16 13:34:58,002 - test_conceptual_diffusion - INFO - Errors: 0
2025-06-16 13:34:58,002 - test_conceptual_diffusion - INFO - Success rate: 100.00%
2025-06-16 13:34:58,006 - __main__ - INFO - Suite conceptual_diffusion completed in 7.15s - PASSED
2025-06-16 13:34:58,138 - test_thought_latent_space - INFO - Epoch 0: Loss=2.3832, Accuracy=0.766
2025-06-16 13:34:58,436 - test_thought_latent_space - INFO - Epoch 1: Loss=1.5703, Accuracy=1.000
2025-06-16 13:34:58,741 - test_thought_latent_space - INFO - Epoch 2: Loss=1.1764, Accuracy=1.000
2025-06-16 13:34:59,038 - test_thought_latent_space - INFO - Epoch 3: Loss=1.0212, Accuracy=1.000
2025-06-16 13:34:59,332 - test_thought_latent_space - INFO - Epoch 4: Loss=0.9319, Accuracy=1.000
2025-06-16 13:34:59,625 - test_thought_latent_space - INFO - Epoch 5: Loss=0.7938, Accuracy=1.000
2025-06-16 13:34:59,918 - test_thought_latent_space - INFO - Epoch 6: Loss=0.7732, Accuracy=1.000
2025-06-16 13:35:00,225 - test_thought_latent_space - INFO - Epoch 7: Loss=0.6941, Accuracy=1.000
2025-06-16 13:35:00,564 - test_thought_latent_space - INFO - Epoch 8: Loss=0.6687, Accuracy=1.000
2025-06-16 13:35:00,894 - test_thought_latent_space - INFO - Epoch 9: Loss=0.6503, Accuracy=1.000
2025-06-16 13:35:04,207 - test_thought_latent_space - INFO - Epoch 20: Loss=0.5806, Accuracy=1.000
2025-06-16 13:35:07,699 - test_thought_latent_space - INFO - Achieved high accuracy 1.000 at epoch 31
2025-06-16 13:35:07,699 - test_thought_latent_space - INFO - Testing trained hierarchical structure...
2025-06-16 13:35:07,833 - test_thought_latent_space - INFO - Final test accuracy: 1.000
2025-06-16 13:35:07,833 - test_thought_latent_space - INFO - Training achieved best accuracy: 1.000
2025-06-16 13:35:07,833 - test_thought_latent_space - INFO - Hierarchical coherence: 1.009
2025-06-16 13:35:07,833 - test_thought_latent_space - INFO - Level prediction accuracy: 1.000
2025-06-16 13:35:07,856 - test_thought_latent_space - INFO - Testing interpolation properties...
2025-06-16 13:35:07,873 - test_thought_latent_space - INFO - Interpolation tests passed
2025-06-16 13:35:07,896 - test_thought_latent_space - INFO - Testing loss computation...
2025-06-16 13:35:07,936 - test_thought_latent_space - INFO - Total loss: 1.118341
2025-06-16 13:35:07,937 - test_thought_latent_space - INFO - Reconstruction loss: 0.192518
2025-06-16 13:35:07,938 - test_thought_latent_space - INFO - Hierarchical loss: 1.071008
2025-06-16 13:35:07,995 - test_thought_latent_space - INFO - Testing manifold structure analysis...
2025-06-16 13:35:08,298 - test_thought_latent_space - INFO - Intrinsic dimensionality: 37
2025-06-16 13:35:08,299 - test_thought_latent_space - INFO - Explained variance ratio: 0.951
2025-06-16 13:35:08,299 - test_thought_latent_space - INFO - Local smoothness: 9.825
2025-06-16 13:35:08,299 - test_thought_latent_space - INFO - Condition number: 999.000
2025-06-16 13:35:08,334 - test_thought_latent_space - INFO - Testing memory operations...
2025-06-16 13:35:08,360 - test_thought_latent_space - INFO - Memory operations tests passed
2025-06-16 13:35:08,380 - test_thought_latent_space - INFO - Testing performance benchmarks...
2025-06-16 13:35:18,033 - test_thought_latent_space - INFO - Single encoding time: 2.28ms
2025-06-16 13:35:18,033 - test_thought_latent_space - INFO - Batch processing time: 41.72ms
2025-06-16 13:35:18,054 - test_thought_latent_space - INFO - Testing relational vector computation...
2025-06-16 13:35:18,073 - test_thought_latent_space - INFO - Relational vector tests passed
2025-06-16 13:35:18,093 - test_thought_latent_space - INFO - Testing semantic similarity computation...
2025-06-16 13:35:18,249 - test_thought_latent_space - INFO - Same cluster similarity: 0.541
2025-06-16 13:35:18,249 - test_thought_latent_space - INFO - Different cluster similarity: 0.548
2025-06-16 13:35:18,266 - test_thought_latent_space - INFO - Testing integration with diffusion process...
2025-06-16 13:35:18,327 - test_thought_latent_space - INFO - Noise level 0.1: degradation 0.1998
2025-06-16 13:35:18,331 - test_thought_latent_space - INFO - Noise level 0.3: degradation 0.2013
2025-06-16 13:35:18,336 - test_thought_latent_space - INFO - Noise level 0.5: degradation 0.2011
2025-06-16 13:35:18,342 - test_thought_latent_space - INFO - Noise level 0.8: degradation 0.2040
2025-06-16 13:35:18,343 - test_thought_latent_space - INFO - Diffusion integration test passed
2025-06-16 13:35:18,363 - test_thought_latent_space - INFO - Testing training integration...
2025-06-16 13:35:19,041 - test_thought_latent_space - INFO - Initial loss: 1.010175
2025-06-16 13:35:19,041 - test_thought_latent_space - INFO - Final loss: 0.665293
2025-06-16 13:35:19,041 - test_thought_latent_space - INFO - Loss ratio: 0.659
2025-06-16 13:35:19,042 - test_thought_latent_space - INFO - ================================================================================
2025-06-16 13:35:19,043 - test_thought_latent_space - INFO - THOUGHT LATENT SPACE TEST SUMMARY
2025-06-16 13:35:19,043 - test_thought_latent_space - INFO - ================================================================================
2025-06-16 13:35:19,043 - test_thought_latent_space - INFO - Tests run: 12
2025-06-16 13:35:19,043 - test_thought_latent_space - INFO - Failures: 0
2025-06-16 13:35:19,043 - test_thought_latent_space - INFO - Errors: 0
2025-06-16 13:35:19,044 - test_thought_latent_space - INFO - Success rate: 100.00%
2025-06-16 13:35:19,045 - __main__ - INFO - Suite thought_latent_space completed in 28.19s - PASSED
2025-06-16 13:35:19,093 - __main__ - INFO - Executing test suite: bayesian_uncertainty
2025-06-16 13:35:19,093 - __main__ - ERROR - Suite bayesian_uncertainty failed with exception: No module named 'test_bayesian_uncertainty_quantification'
2025-06-16 13:35:19,094 - __main__ - ERROR - Traceback (most recent call last):
  File "/workspaces/Ultra/ultra/ultra/diffusion_reasoning/run_all_reasoning_diffusion_tests.py", line 339, in execute_test_suite
    module = __import__(module_name)
             ^^^^^^^^^^^^^^^^^^^^^^^
ModuleNotFoundError: No module named 'test_bayesian_uncertainty_quantification'

2025-06-16 13:35:19,143 - __main__ - INFO - Executing priority 2 tests (2 suites)
2025-06-16 13:35:19,157 - __main__ - INFO - Executing test suite: reverse_diffusion_reasoning
2025-06-16 13:35:19,157 - __main__ - INFO - Executing test suite: probabilistic_inference
2025-06-16 13:35:20,683 - test_reverse_diffusion_reasoning - INFO - Testing analogical reasoning...
2025-06-16 13:35:20,689 - test_reverse_diffusion_reasoning - INFO - Analogical relation similarity: 0.998
2025-06-16 13:35:20,698 - test_reverse_diffusion_reasoning - INFO - Testing constraint satisfaction scoring...
2025-06-16 13:35:20,699 - test_reverse_diffusion_reasoning - INFO - Constraint satisfaction score: 0.000
2025-06-16 13:35:20,700 - test_reverse_diffusion_reasoning - INFO - Perfect constraint score: 1.000
2025-06-16 13:35:20,708 - test_reverse_diffusion_reasoning - INFO - Testing guidance function properties...
2025-06-16 13:35:20,716 - test_reverse_diffusion_reasoning - INFO - Guidance function tests passed
2025-06-16 13:35:20,729 - test_reverse_diffusion_reasoning - INFO - Testing guided reverse step...
2025-06-16 13:35:20,737 - test_reverse_diffusion_reasoning - INFO - Samples improved: 8/8
2025-06-16 13:35:20,739 - test_reverse_diffusion_reasoning - INFO - Average distance change: 8.423
2025-06-16 13:35:20,751 - test_reverse_diffusion_reasoning - INFO - Testing interpolative reasoning...
2025-06-16 13:35:20,828 - test_reverse_diffusion_reasoning - INFO - Similarity trend to A: -0.187
2025-06-16 13:35:20,828 - test_reverse_diffusion_reasoning - INFO - Similarity trend to B: 0.200
2025-06-16 13:35:20,840 - test_reverse_diffusion_reasoning - INFO - Testing mathematical properties...
2025-06-16 13:35:20,842 - test_reverse_diffusion_reasoning - INFO - Guided computation difference: 12.2145
2025-06-16 13:35:20,846 - test_reverse_diffusion_reasoning - INFO - Testing multi-constraint reasoning...
2025-06-16 13:35:20,875 - test_reverse_diffusion_reasoning - INFO - Constraints improved: 2/2
2025-06-16 13:35:20,875 - test_reverse_diffusion_reasoning - INFO - Overall satisfaction - Initial: -73.327, Final: -50.704
2025-06-16 13:35:20,875 - test_reverse_diffusion_reasoning - INFO - Process completed successfully with trajectory variance: 3.302609
2025-06-16 13:35:20,886 - test_reverse_diffusion_reasoning - INFO - Testing performance benchmarks...
2025-06-16 13:35:21,040 - test_reverse_diffusion_reasoning - INFO - Single step time: 0.84ms
2025-06-16 13:35:21,040 - test_reverse_diffusion_reasoning - INFO - Full reasoning time: 6.92ms
2025-06-16 13:35:21,046 - test_reverse_diffusion_reasoning - INFO - Testing goal-directed reasoning...
2025-06-16 13:35:21,059 - test_reverse_diffusion_reasoning - INFO - Convergence ratio: 5.410
2025-06-16 13:35:21,059 - test_reverse_diffusion_reasoning - INFO - Final confidence: 0.000
2025-06-16 13:35:21,060 - test_reverse_diffusion_reasoning - INFO - Initial confidence: 0.000
2025-06-16 13:35:21,060 - test_reverse_diffusion_reasoning - INFO - Path variance: 24.118523
2025-06-16 13:35:21,060 - test_reverse_diffusion_reasoning - INFO - Confidence ratio: 0.000
2025-06-16 13:35:21,066 - test_reverse_diffusion_reasoning - INFO - Testing reasoning trajectory analysis...
2025-06-16 13:35:21,068 - test_reverse_diffusion_reasoning - INFO - Convergence rate: 0.832
2025-06-16 13:35:21,069 - test_reverse_diffusion_reasoning - INFO - Monotonicity: 1.000
2025-06-16 13:35:21,069 - test_reverse_diffusion_reasoning - INFO - Final distance: 1.622
2025-06-16 13:35:21,075 - test_reverse_diffusion_reasoning - INFO - Testing end-to-end reasoning...
2025-06-16 13:35:21,095 - test_reverse_diffusion_reasoning - INFO - End-to-end reasoning test passed
2025-06-16 13:35:21,109 - test_reverse_diffusion_reasoning - INFO - Testing training integration...
2025-06-16 13:35:21,180 - test_probabilistic_inference_engine - INFO - Testing Bayesian model selection...
2025-06-16 13:35:21,204 - test_probabilistic_inference_engine - INFO - Model probabilities: {'model_0': 0.0, 'model_1': 0.0, 'model_2': 1.0}
2025-06-16 13:35:21,210 - test_probabilistic_inference_engine - INFO - Testing information-theoretic measures...
2025-06-16 13:35:21,280 - test_probabilistic_inference_engine - INFO - Entropy: 4.605
2025-06-16 13:35:21,281 - test_probabilistic_inference_engine - INFO - Mutual information: 2.303
2025-06-16 13:35:21,281 - test_probabilistic_inference_engine - INFO - Effective dimensionality: 52.493
2025-06-16 13:35:21,287 - test_probabilistic_inference_engine - INFO - Testing likelihood computation...
2025-06-16 13:35:21,294 - test_probabilistic_inference_engine - INFO - Log likelihood range: [-0.477, 0.074]
2025-06-16 13:35:21,302 - test_probabilistic_inference_engine - INFO - Testing marginal likelihood computation...
2025-06-16 13:35:21,371 - test_probabilistic_inference_engine - INFO - Log evidence (importance_sampling): 1.552
2025-06-16 13:35:21,425 - test_probabilistic_inference_engine - INFO - Log evidence (variational): -2.065
2025-06-16 13:35:21,429 - test_probabilistic_inference_engine - INFO - Log evidence (network): 0.041
2025-06-16 13:35:21,440 - test_probabilistic_inference_engine - INFO - Testing mathematical properties...
2025-06-16 13:35:21,448 - test_probabilistic_inference_engine - INFO - KL divergence: 37.985275
2025-06-16 13:35:21,450 - test_probabilistic_inference_engine - INFO - Mathematical properties test passed
2025-06-16 13:35:21,459 - test_probabilistic_inference_engine - INFO - Testing MCMC sampling...
2025-06-16 13:35:22,763 - test_reverse_diffusion_reasoning - INFO - Training integration test passed
2025-06-16 13:35:22,765 - test_reverse_diffusion_reasoning - INFO - ================================================================================
2025-06-16 13:35:22,765 - test_reverse_diffusion_reasoning - INFO - REVERSE DIFFUSION REASONING TEST SUMMARY
2025-06-16 13:35:22,765 - test_reverse_diffusion_reasoning - INFO - ================================================================================
2025-06-16 13:35:22,766 - test_reverse_diffusion_reasoning - INFO - Tests run: 12
2025-06-16 13:35:22,766 - test_reverse_diffusion_reasoning - INFO - Failures: 0
2025-06-16 13:35:22,766 - test_reverse_diffusion_reasoning - INFO - Errors: 0
2025-06-16 13:35:22,766 - test_reverse_diffusion_reasoning - INFO - Success rate: 100.00%
2025-06-16 13:35:22,767 - __main__ - INFO - Suite reverse_diffusion_reasoning completed in 3.61s - PASSED
2025-06-16 13:35:23,526 - test_probabilistic_inference_engine - INFO - MCMC Diagnostics:
2025-06-16 13:35:23,526 - test_probabilistic_inference_engine - INFO -   Acceptance rate: 0.250
2025-06-16 13:35:23,526 - test_probabilistic_inference_engine - INFO -   Mean error: 0.0987 (tolerance: 0.5534) ✓
2025-06-16 13:35:23,526 - test_probabilistic_inference_engine - INFO -   Cov error: 0.1241 (tolerance: 1.1068) ✓
2025-06-16 13:35:23,526 - test_probabilistic_inference_engine - INFO -   Var error: 0.0914 (tolerance: 0.7826) ✓
2025-06-16 13:35:23,527 - test_probabilistic_inference_engine - INFO -   Effective sample size: 104 (autocorr time: 23.4)
2025-06-16 13:35:23,527 - test_probabilistic_inference_engine - INFO -   Efficiency: 2.1% (effective/total)
2025-06-16 13:35:23,527 - test_probabilistic_inference_engine - INFO -   Test dimension: 8
2025-06-16 13:35:23,527 - test_probabilistic_inference_engine - INFO -   Samples: 5000, Warmup: 2000
2025-06-16 13:35:23,527 - test_probabilistic_inference_engine - INFO -   Mixing ratio: 0.239
2025-06-16 13:35:23,527 - test_probabilistic_inference_engine - INFO - MCMC sampling test passed with proper convergence validation
2025-06-16 13:35:23,532 - test_probabilistic_inference_engine - INFO - Testing normalizing flow...
2025-06-16 13:35:23,540 - test_probabilistic_inference_engine - INFO - Normalizing flow test passed
2025-06-16 13:35:23,546 - test_probabilistic_inference_engine - INFO - Testing performance benchmarks...
2025-06-16 13:35:23,576 - test_probabilistic_inference_engine - INFO - Likelihood computation time: 0.22ms
2025-06-16 13:35:23,576 - test_probabilistic_inference_engine - INFO - Posterior approximation time: 0.74ms
2025-06-16 13:35:23,581 - test_probabilistic_inference_engine - INFO - Testing predictive distribution...
2025-06-16 13:35:23,587 - test_probabilistic_inference_engine - INFO - Predictive mean range: [-2.330, 2.421]
2025-06-16 13:35:23,587 - test_probabilistic_inference_engine - INFO - Predictive std range: [8.061, 8.978]
2025-06-16 13:35:23,592 - test_probabilistic_inference_engine - INFO - Testing prior computation...
2025-06-16 13:35:23,595 - test_probabilistic_inference_engine - INFO - Prior mean norm: 0.000
2025-06-16 13:35:23,595 - test_probabilistic_inference_engine - INFO - Prior covariance trace: 64.000
2025-06-16 13:35:23,600 - test_probabilistic_inference_engine - INFO - Testing uncertainty quantification...
2025-06-16 13:35:23,604 - test_probabilistic_inference_engine - INFO - Epistemic uncertainty mean: 66.6604
2025-06-16 13:35:23,604 - test_probabilistic_inference_engine - INFO - Aleatoric uncertainty mean: 0.1000
2025-06-16 13:35:23,605 - test_probabilistic_inference_engine - INFO - Total uncertainty mean: 66.7604
2025-06-16 13:35:23,610 - test_probabilistic_inference_engine - INFO - Testing variational posterior approximation...
2025-06-16 13:35:23,613 - test_probabilistic_inference_engine - INFO - Posterior-prior mean difference: 1.907
2025-06-16 13:35:23,613 - test_probabilistic_inference_engine - INFO - Variational posterior approximation test passed
2025-06-16 13:35:23,618 - test_probabilistic_inference_engine - INFO - Testing end-to-end Bayesian inference...
2025-06-16 13:35:23,653 - test_probabilistic_inference_engine - INFO - Posterior error: 5.860
2025-06-16 13:35:23,654 - test_probabilistic_inference_engine - INFO - Prior error: 5.896
2025-06-16 13:35:23,654 - test_probabilistic_inference_engine - INFO - Evidence: 0.169
2025-06-16 13:35:23,654 - test_probabilistic_inference_engine - INFO - Posterior improved over prior ✓
2025-06-16 13:35:23,662 - test_probabilistic_inference_engine - INFO - Testing model comparison workflow...
2025-06-16 13:35:23,680 - test_probabilistic_inference_engine - INFO - True noise level: 0.2
2025-06-16 13:35:23,680 - test_probabilistic_inference_engine - INFO - Selected noise level: 0.5
2025-06-16 13:35:23,681 - test_probabilistic_inference_engine - INFO - Model probabilities: {'model_0': 0.0, 'model_1': 0.0, 'model_2': 1.0}
2025-06-16 13:35:23,682 - test_probabilistic_inference_engine - INFO - ================================================================================
2025-06-16 13:35:23,682 - test_probabilistic_inference_engine - INFO - PROBABILISTIC INFERENCE ENGINE TEST SUMMARY
2025-06-16 13:35:23,682 - test_probabilistic_inference_engine - INFO - ================================================================================
2025-06-16 13:35:23,682 - test_probabilistic_inference_engine - INFO - Tests run: 14
2025-06-16 13:35:23,682 - test_probabilistic_inference_engine - INFO - Failures: 0
2025-06-16 13:35:23,682 - test_probabilistic_inference_engine - INFO - Errors: 0
2025-06-16 13:35:23,682 - test_probabilistic_inference_engine - INFO - Success rate: 100.00%
2025-06-16 13:35:23,683 - __main__ - INFO - Suite probabilistic_inference completed in 4.53s - PASSED
2025-06-16 13:35:23,765 - __main__ - INFO - Executing priority 3 tests (1 suites)
2025-06-16 13:35:23,778 - __main__ - INFO - Executing test suite: comprehensive_integration
2025-06-16 13:35:25,553 - test_comprehensive_integration - INFO - Integration tests starting on cpu
2025-06-16 13:35:25,553 - test_comprehensive_integration - INFO - Initial resources: {'cpu_percent': 9.5, 'memory_percent': 43.9, 'memory_used_gb': 3.0377044677734375, 'gpu_memory_mb': 0}
2025-06-16 13:35:25,554 - test_comprehensive_integration - INFO - Testing end-to-end scientific reasoning...
2025-06-16 13:35:26,630 - test_comprehensive_integration - INFO - Scientific reasoning completed in 0.08s
2025-06-16 13:35:26,631 - test_comprehensive_integration - INFO - Hypothesis error: 3.4058
2025-06-16 13:35:26,631 - test_comprehensive_integration - INFO - Epistemic uncertainty: 0.9847
2025-06-16 13:35:27,702 - test_comprehensive_integration - INFO - Final resources: {'cpu_percent': 5.0, 'memory_percent': 44.0, 'memory_used_gb': 3.044952392578125, 'gpu_memory_mb': 0}
2025-06-16 13:35:28,703 - test_comprehensive_integration - INFO - Integration tests starting on cpu
2025-06-16 13:35:28,704 - test_comprehensive_integration - INFO - Initial resources: {'cpu_percent': 14.5, 'memory_percent': 44.1, 'memory_used_gb': 3.0495834350585938, 'gpu_memory_mb': 0}
2025-06-16 13:35:28,704 - test_comprehensive_integration - INFO - Testing analogical reasoning integration...
2025-06-16 13:35:28,719 - test_comprehensive_integration - INFO - Expected analogical relation similarity: 0.908
2025-06-16 13:35:29,724 - test_comprehensive_integration - INFO - Analogical reasoning completed in 0.02s
2025-06-16 13:35:29,724 - test_comprehensive_integration - INFO - Relation similarity: 0.4031
2025-06-16 13:35:29,724 - test_comprehensive_integration - INFO - Target similarity: 0.9016
2025-06-16 13:35:29,725 - test_comprehensive_integration - INFO - Proportional consistency: 0.8706
2025-06-16 13:35:30,795 - test_comprehensive_integration - INFO - Final resources: {'cpu_percent': 6.0, 'memory_percent': 44.0, 'memory_used_gb': 3.0458717346191406, 'gpu_memory_mb': 0}
2025-06-16 13:35:31,796 - test_comprehensive_integration - INFO - Integration tests starting on cpu
2025-06-16 13:35:31,796 - test_comprehensive_integration - INFO - Initial resources: {'cpu_percent': 4.0, 'memory_percent': 44.0, 'memory_used_gb': 3.0458602905273438, 'gpu_memory_mb': 0}
2025-06-16 13:35:31,796 - test_comprehensive_integration - INFO - Testing multi-modal reasoning integration...
2025-06-16 13:35:32,804 - test_comprehensive_integration - INFO - Multi-modal reasoning completed in 0.01s
2025-06-16 13:35:32,804 - test_comprehensive_integration - INFO - Target similarity: 0.9683
2025-06-16 13:35:32,804 - test_comprehensive_integration - INFO - Cross-modal similarity: 0.4930
2025-06-16 13:35:33,873 - test_comprehensive_integration - INFO - Final resources: {'cpu_percent': 18.7, 'memory_percent': 44.1, 'memory_used_gb': 3.0563011169433594, 'gpu_memory_mb': 0}
2025-06-16 13:35:34,874 - test_comprehensive_integration - INFO - Integration tests starting on cpu
2025-06-16 13:35:34,874 - test_comprehensive_integration - INFO - Initial resources: {'cpu_percent': 4.5, 'memory_percent': 44.1, 'memory_used_gb': 3.0567779541015625, 'gpu_memory_mb': 0}
2025-06-16 13:35:34,874 - test_comprehensive_integration - INFO - Testing mathematical consistency across components...
2025-06-16 13:35:35,881 - test_comprehensive_integration - INFO - Mathematical consistency test completed in 0.01s
2025-06-16 13:35:35,881 - test_comprehensive_integration - INFO - Overall consistency score: 0.7681
2025-06-16 13:35:35,881 - test_comprehensive_integration - INFO - Individual scores: {'encoding_decoding': 0.5334907826877109, 'diffusion_forward_reverse': 0.5387589647765934, 'uncertainty_repeatability': 1.0, 'likelihood_repeatability': 1.0}
2025-06-16 13:35:36,952 - test_comprehensive_integration - INFO - Final resources: {'cpu_percent': 2.0, 'memory_percent': 44.1, 'memory_used_gb': 3.0569992065429688, 'gpu_memory_mb': 0}
2025-06-16 13:35:37,953 - test_comprehensive_integration - INFO - Integration tests starting on cpu
2025-06-16 13:35:37,953 - test_comprehensive_integration - INFO - Initial resources: {'cpu_percent': 5.5, 'memory_percent': 44.2, 'memory_used_gb': 3.0572166442871094, 'gpu_memory_mb': 0}
2025-06-16 13:35:37,953 - test_comprehensive_integration - INFO - Testing stress conditions and scalability...
2025-06-16 13:35:51,339 - test_comprehensive_integration - INFO - Stress testing completed in 12.39s
2025-06-16 13:35:51,339 - test_comprehensive_integration - INFO - Performance metrics: {'avg_batch_processing_time': 0.004357767105102539, 'concurrent_processing_time': 0.006303548812866211, 'memory_increase_per_100_samples': 0.00664520263671875, 'reasoning_chain_time': 0.005067586898803711}
2025-06-16 13:35:52,414 - test_comprehensive_integration - INFO - Final resources: {'cpu_percent': 8.5, 'memory_percent': 44.2, 'memory_used_gb': 3.0646514892578125, 'gpu_memory_mb': 0}
2025-06-16 13:35:53,415 - test_comprehensive_integration - INFO - Integration tests starting on cpu
2025-06-16 13:35:53,415 - test_comprehensive_integration - INFO - Initial resources: {'cpu_percent': 7.5, 'memory_percent': 44.2, 'memory_used_gb': 3.064647674560547, 'gpu_memory_mb': 0}
2025-06-16 13:35:53,415 - test_comprehensive_integration - INFO - Testing production-grade integration scenarios...
2025-06-16 13:35:54,462 - test_comprehensive_integration - INFO - Production integration completed in 0.05s
2025-06-16 13:35:54,463 - test_comprehensive_integration - INFO - Production metrics: {'avg_processing_time': 0.0017197251319885254, 'avg_reasoning_accuracy': 1.4079146259709407, 'real_time_compliance': 1.0, 'batch_processing_time': 0.010425806045532227, 'avg_quality_score': 0.8, 'error_recovery_rate': 1.0, 'production_readiness_score': 1.0823743877912824}
2025-06-16 13:35:55,535 - test_comprehensive_integration - INFO - Final resources: {'cpu_percent': 10.0, 'memory_percent': 44.5, 'memory_used_gb': 3.085338592529297, 'gpu_memory_mb': 0}
2025-06-16 13:35:56,536 - test_comprehensive_integration - INFO - Integration tests starting on cpu
2025-06-16 13:35:56,536 - test_comprehensive_integration - INFO - Initial resources: {'cpu_percent': 7.5, 'memory_percent': 44.5, 'memory_used_gb': 3.083637237548828, 'gpu_memory_mb': 0}
2025-06-16 13:35:56,536 - test_comprehensive_integration - INFO - Generating comprehensive test report...
2025-06-16 13:35:56,538 - test_comprehensive_integration - INFO - ================================================================================
2025-06-16 13:35:56,538 - test_comprehensive_integration - INFO - DIFFUSION REASONING INTEGRATION TEST REPORT
2025-06-16 13:35:56,539 - test_comprehensive_integration - INFO - ================================================================================
2025-06-16 13:35:56,539 - test_comprehensive_integration - INFO - Total Tests: 6
2025-06-16 13:35:56,539 - test_comprehensive_integration - INFO - Passed: 6
2025-06-16 13:35:56,539 - test_comprehensive_integration - INFO - Failed: 0
2025-06-16 13:35:56,539 - test_comprehensive_integration - INFO - Success Rate: 100.00%
2025-06-16 13:35:56,539 - test_comprehensive_integration - INFO - Total Execution Time: 12.54s
2025-06-16 13:35:56,539 - test_comprehensive_integration - INFO - Average Execution Time: 2.09s
2025-06-16 13:35:56,539 - test_comprehensive_integration - INFO - Max Memory Usage: 3.07GB
2025-06-16 13:35:56,539 - test_comprehensive_integration - INFO - ================================================================================
2025-06-16 13:35:57,617 - test_comprehensive_integration - INFO - Final resources: {'cpu_percent': 7.0, 'memory_percent': 44.5, 'memory_used_gb': 3.0840721130371094, 'gpu_memory_mb': 0}
2025-06-16 13:35:57,617 - test_comprehensive_integration - INFO - ================================================================================
2025-06-16 13:35:57,618 - test_comprehensive_integration - INFO - COMPREHENSIVE INTEGRATION TEST SUMMARY
2025-06-16 13:35:57,618 - test_comprehensive_integration - INFO - ================================================================================
2025-06-16 13:35:57,618 - test_comprehensive_integration - INFO - Tests run: 7
2025-06-16 13:35:57,618 - test_comprehensive_integration - INFO - Failures: 0
2025-06-16 13:35:57,618 - test_comprehensive_integration - INFO - Errors: 0
2025-06-16 13:35:57,619 - test_comprehensive_integration - INFO - Success rate: 100.00%
2025-06-16 13:35:57,619 - __main__ - INFO - Suite comprehensive_integration completed in 33.84s - PASSED
2025-06-16 13:35:57,690 - __main__ - INFO - Generating final test report...
2025-06-16 13:35:57,692 - __main__ - INFO - Detailed report saved to test_results/final_test_report.json
2025-06-16 13:35:57,692 - __main__ - INFO - ================================================================================
2025-06-16 13:35:57,692 - __main__ - INFO - ULTRA DIFFUSION REASONING MODULE - TEST EXECUTION COMPLETE
2025-06-16 13:35:57,692 - __main__ - INFO - ================================================================================
2025-06-16 13:35:57,692 - __main__ - INFO - Execution Summary:
2025-06-16 13:35:57,692 - __main__ - INFO -   Total Test Suites: 6
2025-06-16 13:35:57,692 - __main__ - INFO -   Passed Suites: 5
2025-06-16 13:35:57,692 - __main__ - INFO -   Failed Suites: 1
2025-06-16 13:35:57,692 - __main__ - INFO -   Success Rate: 83.3%
2025-06-16 13:35:57,692 - __main__ - INFO -   Total Execution Time: 1m 7s
2025-06-16 13:35:57,692 - __main__ - INFO - 
Suite Details:
2025-06-16 13:35:57,693 - __main__ - INFO -   conceptual_diffusion: PASSED (7.2s)
2025-06-16 13:35:57,693 - __main__ - INFO -   thought_latent_space: PASSED (28.2s)
2025-06-16 13:35:57,693 - __main__ - INFO -   bayesian_uncertainty: FAILED (0.0s)
2025-06-16 13:35:57,693 - __main__ - INFO -   reverse_diffusion_reasoning: PASSED (3.6s)
2025-06-16 13:35:57,693 - __main__ - INFO -   probabilistic_inference: PASSED (4.5s)
2025-06-16 13:35:57,693 - __main__ - INFO -   comprehensive_integration: PASSED (33.8s)
2025-06-16 13:35:57,693 - __main__ - WARNING - 
Failed Suites:
2025-06-16 13:35:57,693 - __main__ - WARNING -   bayesian_uncertainty: 2 errors
2025-06-16 13:35:57,693 - __main__ - WARNING -     - No module named 'test_bayesian_uncertainty_quantification'...
2025-06-16 13:35:57,693 - __main__ - WARNING -     - Traceback (most recent call last):
  File "/workspaces/Ultra/ultra/ultra/diffusion_reasoning/run_all...
2025-06-16 13:35:57,693 - __main__ - INFO - ================================================================================
2025-06-16 13:35:57,693 - __main__ - WARNING - Most tests passed, but some failures detected
2025-06-16 14:15:55,660 - __main__ - INFO - Setting up test environment...
2025-06-16 14:15:55,661 - __main__ - INFO - System info: {'cpu_count': 2, 'cpu_percent': 68.6, 'memory_total_gb': 7.751636505126953, 'memory_available_gb': 4.205406188964844, 'memory_percent': 45.7, 'gpu_available': False, 'gpu_count': 0, 'gpu_memory_gb': 0}
2025-06-16 14:15:55,661 - __main__ - INFO - Using device: cpu
2025-06-16 14:15:55,661 - __main__ - INFO - Test environment setup complete
2025-06-16 14:15:55,662 - __main__ - INFO - Initialized test orchestrator with 6 test suites
2025-06-16 14:15:55,662 - __main__ - INFO - ================================================================================
2025-06-16 14:15:55,662 - __main__ - INFO - STARTING ULTRA DIFFUSION REASONING MODULE TESTS
2025-06-16 14:15:55,662 - __main__ - INFO - ================================================================================
2025-06-16 14:15:55,662 - __main__ - INFO - Estimated total execution time: 43 minutes 20 seconds
2025-06-16 14:15:55,662 - __main__ - INFO - Running tests in parallel with 4 workers
2025-06-16 14:15:55,662 - __main__ - INFO - Executing priority 1 tests (3 suites)
2025-06-16 14:15:55,679 - __main__ - INFO - Executing test suite: conceptual_diffusion
2025-06-16 14:15:55,680 - __main__ - INFO - Executing test suite: thought_latent_space
2025-06-16 14:15:57,490 - test_thought_latent_space - INFO - Testing compositional operations...
2025-06-16 14:15:57,537 - test_thought_latent_space - INFO - Compositional operations tests passed
2025-06-16 14:15:57,575 - test_thought_latent_space - INFO - Testing encoding-decoding consistency...
2025-06-16 14:15:57,638 - test_thought_latent_space - INFO - Reconstruction error: 0.205583
2025-06-16 14:15:57,669 - test_thought_latent_space - INFO - Testing hierarchical structure preservation...
2025-06-16 14:15:57,718 - test_conceptual_diffusion - INFO - Testing conceptual space properties...
2025-06-16 14:15:57,740 - test_thought_latent_space - INFO - Training hierarchical structure classifier...
2025-06-16 14:15:57,747 - test_conceptual_diffusion - INFO - Within-level similarity: 0.141
2025-06-16 14:15:57,747 - test_conceptual_diffusion - INFO - Across-level similarity: 0.156
2025-06-16 14:15:57,748 - test_conceptual_diffusion - INFO - Semantic similarity preservation: 0.992
2025-06-16 14:15:57,780 - test_conceptual_diffusion - INFO - Testing forward diffusion process...
2025-06-16 14:15:57,782 - test_conceptual_diffusion - INFO - Forward diffusion process tests passed
2025-06-16 14:15:57,809 - test_conceptual_diffusion - INFO - Testing loss computation...
2025-06-16 14:15:57,829 - test_conceptual_diffusion - INFO - Standard loss: 1.553960
2025-06-16 14:15:57,830 - test_conceptual_diffusion - INFO - VLB loss: 115.039497
2025-06-16 14:15:57,861 - test_conceptual_diffusion - INFO - Testing mathematical consistency...
2025-06-16 14:15:57,891 - test_conceptual_diffusion - INFO - Reconstruction error: 0.737502
2025-06-16 14:15:57,922 - test_conceptual_diffusion - INFO - Testing noise schedule properties...
2025-06-16 14:15:57,925 - test_conceptual_diffusion - INFO - Beta range: [0.000100, 0.050000]
2025-06-16 14:15:57,925 - test_conceptual_diffusion - INFO - Alpha cumprod range: [0.999900, 0.078234]
2025-06-16 14:15:57,956 - test_conceptual_diffusion - INFO - Testing performance benchmarks...
2025-06-16 14:15:58,340 - test_conceptual_diffusion - INFO - Forward diffusion time: 0.14ms
2025-06-16 14:15:58,340 - test_conceptual_diffusion - INFO - Reverse diffusion time: 2.03ms
2025-06-16 14:15:58,341 - test_conceptual_diffusion - INFO - Loss computation time: 1.66ms
2025-06-16 14:15:58,382 - test_conceptual_diffusion - INFO - Testing reverse diffusion step...
2025-06-16 14:15:58,394 - test_conceptual_diffusion - INFO - Noise reduction ratio: 0.996
2025-06-16 14:15:58,442 - test_conceptual_diffusion - INFO - Testing sampling process...
2025-06-16 14:15:58,747 - test_conceptual_diffusion - INFO - Initial noise level: 7.743
2025-06-16 14:15:58,748 - test_conceptual_diffusion - INFO - Final noise level: 2.166
2025-06-16 14:15:58,748 - test_conceptual_diffusion - INFO - Noise reduction: 0.720
2025-06-16 14:15:58,791 - test_conceptual_diffusion - INFO - Testing score network properties...
2025-06-16 14:15:58,807 - test_conceptual_diffusion - INFO - Score network output range: [0.000, 0.000]
2025-06-16 14:15:58,872 - test_conceptual_diffusion - INFO - Testing memory efficiency...
2025-06-16 14:15:58,873 - test_conceptual_diffusion - INFO - CUDA not available, skipping memory test
2025-06-16 14:15:58,938 - test_conceptual_diffusion - INFO - Testing training integration...
2025-06-16 14:16:00,135 - test_thought_latent_space - INFO - Epoch 0: Loss=2.5206, Accuracy=0.672
2025-06-16 14:16:00,620 - test_thought_latent_space - INFO - Epoch 1: Loss=1.5365, Accuracy=1.000
2025-06-16 14:16:00,884 - test_conceptual_diffusion - INFO - Initial loss: 1.068045
2025-06-16 14:16:00,885 - test_conceptual_diffusion - INFO - Final loss: 1.147841
2025-06-16 14:16:00,886 - test_conceptual_diffusion - INFO - Relative change: 0.075
2025-06-16 14:16:00,888 - test_conceptual_diffusion - INFO - ================================================================================
2025-06-16 14:16:00,888 - test_conceptual_diffusion - INFO - CONCEPTUAL DIFFUSION TEST SUMMARY
2025-06-16 14:16:00,888 - test_conceptual_diffusion - INFO - ================================================================================
2025-06-16 14:16:00,888 - test_conceptual_diffusion - INFO - Tests run: 11
2025-06-16 14:16:00,889 - test_conceptual_diffusion - INFO - Failures: 0
2025-06-16 14:16:00,889 - test_conceptual_diffusion - INFO - Errors: 0
2025-06-16 14:16:00,889 - test_conceptual_diffusion - INFO - Success rate: 100.00%
2025-06-16 14:16:00,892 - __main__ - INFO - Suite conceptual_diffusion completed in 5.21s - PASSED
2025-06-16 14:16:01,046 - test_thought_latent_space - INFO - Epoch 2: Loss=1.2949, Accuracy=1.000
2025-06-16 14:16:01,359 - test_thought_latent_space - INFO - Epoch 3: Loss=1.0107, Accuracy=1.000
2025-06-16 14:16:01,699 - test_thought_latent_space - INFO - Epoch 4: Loss=0.8849, Accuracy=1.000
2025-06-16 14:16:02,143 - test_thought_latent_space - INFO - Epoch 5: Loss=0.8552, Accuracy=1.000
2025-06-16 14:16:02,506 - test_thought_latent_space - INFO - Epoch 6: Loss=0.7346, Accuracy=1.000
2025-06-16 14:16:03,043 - test_thought_latent_space - INFO - Epoch 7: Loss=0.7121, Accuracy=1.000
2025-06-16 14:16:03,340 - test_thought_latent_space - INFO - Epoch 8: Loss=0.6640, Accuracy=1.000
2025-06-16 14:16:03,653 - test_thought_latent_space - INFO - Epoch 9: Loss=0.6377, Accuracy=1.000
2025-06-16 14:16:06,927 - test_thought_latent_space - INFO - Epoch 20: Loss=0.5852, Accuracy=1.000
2025-06-16 14:16:10,225 - test_thought_latent_space - INFO - Achieved high accuracy 1.000 at epoch 31
2025-06-16 14:16:10,225 - test_thought_latent_space - INFO - Testing trained hierarchical structure...
2025-06-16 14:16:10,364 - test_thought_latent_space - INFO - Final test accuracy: 1.000
2025-06-16 14:16:10,364 - test_thought_latent_space - INFO - Training achieved best accuracy: 1.000
2025-06-16 14:16:10,364 - test_thought_latent_space - INFO - Hierarchical coherence: 1.001
2025-06-16 14:16:10,364 - test_thought_latent_space - INFO - Level prediction accuracy: 1.000
2025-06-16 14:16:10,385 - test_thought_latent_space - INFO - Testing interpolation properties...
2025-06-16 14:16:10,399 - test_thought_latent_space - INFO - Interpolation tests passed
2025-06-16 14:16:10,419 - test_thought_latent_space - INFO - Testing loss computation...
2025-06-16 14:16:10,450 - test_thought_latent_space - INFO - Total loss: 1.112901
2025-06-16 14:16:10,450 - test_thought_latent_space - INFO - Reconstruction loss: 0.179374
2025-06-16 14:16:10,450 - test_thought_latent_space - INFO - Hierarchical loss: 1.103225
2025-06-16 14:16:10,471 - test_thought_latent_space - INFO - Testing manifold structure analysis...
2025-06-16 14:16:10,661 - test_thought_latent_space - INFO - Intrinsic dimensionality: 36
2025-06-16 14:16:10,663 - test_thought_latent_space - INFO - Explained variance ratio: 0.953
2025-06-16 14:16:10,663 - test_thought_latent_space - INFO - Local smoothness: 8.928
2025-06-16 14:16:10,663 - test_thought_latent_space - INFO - Condition number: 999.000
2025-06-16 14:16:10,713 - test_thought_latent_space - INFO - Testing memory operations...
2025-06-16 14:16:10,742 - test_thought_latent_space - INFO - Memory operations tests passed
2025-06-16 14:16:10,765 - test_thought_latent_space - INFO - Testing performance benchmarks...
2025-06-16 14:16:20,770 - test_thought_latent_space - INFO - Single encoding time: 2.30ms
2025-06-16 14:16:20,771 - test_thought_latent_space - INFO - Batch processing time: 44.81ms
2025-06-16 14:16:20,792 - test_thought_latent_space - INFO - Testing relational vector computation...
2025-06-16 14:16:20,814 - test_thought_latent_space - INFO - Relational vector tests passed
2025-06-16 14:16:20,832 - test_thought_latent_space - INFO - Testing semantic similarity computation...
2025-06-16 14:16:20,940 - test_thought_latent_space - INFO - Same cluster similarity: 0.468
2025-06-16 14:16:20,940 - test_thought_latent_space - INFO - Different cluster similarity: 0.488
2025-06-16 14:16:20,960 - test_thought_latent_space - INFO - Testing integration with diffusion process...
2025-06-16 14:16:21,014 - test_thought_latent_space - INFO - Noise level 0.1: degradation 0.2112
2025-06-16 14:16:21,020 - test_thought_latent_space - INFO - Noise level 0.3: degradation 0.1991
2025-06-16 14:16:21,025 - test_thought_latent_space - INFO - Noise level 0.5: degradation 0.1955
2025-06-16 14:16:21,031 - test_thought_latent_space - INFO - Noise level 0.8: degradation 0.1996
2025-06-16 14:16:21,031 - test_thought_latent_space - INFO - Diffusion integration test passed
2025-06-16 14:16:21,048 - test_thought_latent_space - INFO - Testing training integration...
2025-06-16 14:16:21,799 - test_thought_latent_space - INFO - Initial loss: 0.985887
2025-06-16 14:16:21,799 - test_thought_latent_space - INFO - Final loss: 0.659408
2025-06-16 14:16:21,799 - test_thought_latent_space - INFO - Loss ratio: 0.669
2025-06-16 14:16:21,801 - test_thought_latent_space - INFO - ================================================================================
2025-06-16 14:16:21,801 - test_thought_latent_space - INFO - THOUGHT LATENT SPACE TEST SUMMARY
2025-06-16 14:16:21,801 - test_thought_latent_space - INFO - ================================================================================
2025-06-16 14:16:21,801 - test_thought_latent_space - INFO - Tests run: 12
2025-06-16 14:16:21,801 - test_thought_latent_space - INFO - Failures: 0
2025-06-16 14:16:21,802 - test_thought_latent_space - INFO - Errors: 0
2025-06-16 14:16:21,802 - test_thought_latent_space - INFO - Success rate: 100.00%
2025-06-16 14:16:21,803 - __main__ - INFO - Suite thought_latent_space completed in 26.12s - PASSED
2025-06-16 14:16:21,842 - __main__ - INFO - Executing test suite: bayesian_uncertainty
2025-06-16 14:16:21,842 - __main__ - ERROR - Suite bayesian_uncertainty failed with exception: No module named 'test_bayesian_uncertainty'
2025-06-16 14:16:21,843 - __main__ - ERROR - Traceback (most recent call last):
  File "/workspaces/Ultra/ultra/ultra/diffusion_reasoning/run_all_reasoning_diffusion_tests.py", line 339, in execute_test_suite
    module = __import__(module_name)
             ^^^^^^^^^^^^^^^^^^^^^^^
ModuleNotFoundError: No module named 'test_bayesian_uncertainty'

2025-06-16 14:16:21,895 - __main__ - INFO - Executing priority 2 tests (2 suites)
2025-06-16 14:16:21,911 - __main__ - INFO - Executing test suite: reverse_diffusion_reasoning
2025-06-16 14:16:21,911 - __main__ - INFO - Executing test suite: probabilistic_inference
2025-06-16 14:16:23,421 - test_reverse_diffusion_reasoning - INFO - Testing analogical reasoning...
2025-06-16 14:16:23,437 - test_reverse_diffusion_reasoning - INFO - Analogical relation similarity: 0.999
2025-06-16 14:16:23,449 - test_reverse_diffusion_reasoning - INFO - Testing constraint satisfaction scoring...
2025-06-16 14:16:23,450 - test_reverse_diffusion_reasoning - INFO - Constraint satisfaction score: 0.000
2025-06-16 14:16:23,450 - test_reverse_diffusion_reasoning - INFO - Perfect constraint score: 1.000
2025-06-16 14:16:23,458 - test_reverse_diffusion_reasoning - INFO - Testing guidance function properties...
2025-06-16 14:16:23,463 - test_reverse_diffusion_reasoning - INFO - Guidance function tests passed
2025-06-16 14:16:23,474 - test_reverse_diffusion_reasoning - INFO - Testing guided reverse step...
2025-06-16 14:16:23,480 - test_reverse_diffusion_reasoning - INFO - Samples improved: 8/8
2025-06-16 14:16:23,482 - test_reverse_diffusion_reasoning - INFO - Average distance change: 9.720
2025-06-16 14:16:23,489 - test_reverse_diffusion_reasoning - INFO - Testing interpolative reasoning...
2025-06-16 14:16:23,548 - test_reverse_diffusion_reasoning - INFO - Similarity trend to A: -0.241
2025-06-16 14:16:23,549 - test_reverse_diffusion_reasoning - INFO - Similarity trend to B: 0.222
2025-06-16 14:16:23,566 - test_reverse_diffusion_reasoning - INFO - Testing mathematical properties...
2025-06-16 14:16:23,567 - test_reverse_diffusion_reasoning - INFO - Guided computation difference: 9.9010
2025-06-16 14:16:23,577 - test_reverse_diffusion_reasoning - INFO - Testing multi-constraint reasoning...
2025-06-16 14:16:23,609 - test_reverse_diffusion_reasoning - INFO - Constraints improved: 1/2
2025-06-16 14:16:23,611 - test_reverse_diffusion_reasoning - INFO - Overall satisfaction - Initial: -77.164, Final: -53.995
2025-06-16 14:16:23,611 - test_reverse_diffusion_reasoning - INFO - Process completed successfully with trajectory variance: 4.018056
2025-06-16 14:16:23,625 - test_reverse_diffusion_reasoning - INFO - Testing performance benchmarks...
2025-06-16 14:16:23,738 - test_probabilistic_inference_engine - INFO - Testing Bayesian model selection...
2025-06-16 14:16:23,761 - test_probabilistic_inference_engine - INFO - Model probabilities: {'model_0': 0.0, 'model_1': 0.0, 'model_2': 1.0}
2025-06-16 14:16:23,768 - test_probabilistic_inference_engine - INFO - Testing information-theoretic measures...
2025-06-16 14:16:23,769 - test_reverse_diffusion_reasoning - INFO - Single step time: 0.75ms
2025-06-16 14:16:23,770 - test_reverse_diffusion_reasoning - INFO - Full reasoning time: 6.77ms
2025-06-16 14:16:23,780 - test_reverse_diffusion_reasoning - INFO - Testing goal-directed reasoning...
2025-06-16 14:16:23,792 - test_probabilistic_inference_engine - INFO - Entropy: 4.605
2025-06-16 14:16:23,793 - test_probabilistic_inference_engine - INFO - Mutual information: 2.303
2025-06-16 14:16:23,793 - test_probabilistic_inference_engine - INFO - Effective dimensionality: 52.849
2025-06-16 14:16:23,799 - test_probabilistic_inference_engine - INFO - Testing likelihood computation...
2025-06-16 14:16:23,804 - test_probabilistic_inference_engine - INFO - Log likelihood range: [-0.266, 0.354]
2025-06-16 14:16:23,805 - test_reverse_diffusion_reasoning - INFO - Convergence ratio: 4.763
2025-06-16 14:16:23,805 - test_reverse_diffusion_reasoning - INFO - Final confidence: 0.000
2025-06-16 14:16:23,806 - test_reverse_diffusion_reasoning - INFO - Initial confidence: 0.000
2025-06-16 14:16:23,806 - test_reverse_diffusion_reasoning - INFO - Path variance: 26.443743
2025-06-16 14:16:23,807 - test_reverse_diffusion_reasoning - INFO - Confidence ratio: 0.000
2025-06-16 14:16:23,814 - test_probabilistic_inference_engine - INFO - Testing marginal likelihood computation...
2025-06-16 14:16:23,817 - test_reverse_diffusion_reasoning - INFO - Testing reasoning trajectory analysis...
2025-06-16 14:16:23,821 - test_reverse_diffusion_reasoning - INFO - Convergence rate: 0.832
2025-06-16 14:16:23,822 - test_reverse_diffusion_reasoning - INFO - Monotonicity: 1.000
2025-06-16 14:16:23,823 - test_reverse_diffusion_reasoning - INFO - Final distance: 1.556
2025-06-16 14:16:23,831 - test_reverse_diffusion_reasoning - INFO - Testing end-to-end reasoning...
2025-06-16 14:16:23,846 - test_reverse_diffusion_reasoning - INFO - End-to-end reasoning test passed
2025-06-16 14:16:23,853 - test_reverse_diffusion_reasoning - INFO - Testing training integration...
2025-06-16 14:16:23,866 - test_probabilistic_inference_engine - INFO - Log evidence (importance_sampling): 1.601
2025-06-16 14:16:23,911 - test_probabilistic_inference_engine - INFO - Log evidence (variational): -2.627
2025-06-16 14:16:23,915 - test_probabilistic_inference_engine - INFO - Log evidence (network): -0.104
2025-06-16 14:16:23,924 - test_probabilistic_inference_engine - INFO - Testing mathematical properties...
2025-06-16 14:16:23,927 - test_probabilistic_inference_engine - INFO - KL divergence: 53.361015
2025-06-16 14:16:23,928 - test_probabilistic_inference_engine - INFO - Mathematical properties test passed
2025-06-16 14:16:23,937 - test_probabilistic_inference_engine - INFO - Testing MCMC sampling...
2025-06-16 14:16:25,566 - test_reverse_diffusion_reasoning - INFO - Training integration test passed
2025-06-16 14:16:25,568 - test_reverse_diffusion_reasoning - INFO - ================================================================================
2025-06-16 14:16:25,569 - test_reverse_diffusion_reasoning - INFO - REVERSE DIFFUSION REASONING TEST SUMMARY
2025-06-16 14:16:25,569 - test_reverse_diffusion_reasoning - INFO - ================================================================================
2025-06-16 14:16:25,570 - test_reverse_diffusion_reasoning - INFO - Tests run: 12
2025-06-16 14:16:25,570 - test_reverse_diffusion_reasoning - INFO - Failures: 0
2025-06-16 14:16:25,570 - test_reverse_diffusion_reasoning - INFO - Errors: 0
2025-06-16 14:16:25,570 - test_reverse_diffusion_reasoning - INFO - Success rate: 100.00%
2025-06-16 14:16:25,571 - __main__ - INFO - Suite reverse_diffusion_reasoning completed in 3.66s - PASSED
2025-06-16 14:16:26,092 - test_probabilistic_inference_engine - INFO - MCMC Diagnostics:
2025-06-16 14:16:26,092 - test_probabilistic_inference_engine - INFO -   Acceptance rate: 0.259
2025-06-16 14:16:26,092 - test_probabilistic_inference_engine - INFO -   Mean error: 0.1383 (tolerance: 0.5737) ✓
2025-06-16 14:16:26,092 - test_probabilistic_inference_engine - INFO -   Cov error: 0.1029 (tolerance: 1.1474) ✓
2025-06-16 14:16:26,093 - test_probabilistic_inference_engine - INFO -   Var error: 0.0333 (tolerance: 0.8113) ✓
2025-06-16 14:16:26,093 - test_probabilistic_inference_engine - INFO -   Effective sample size: 97 (autocorr time: 25.2)
2025-06-16 14:16:26,093 - test_probabilistic_inference_engine - INFO -   Efficiency: 1.9% (effective/total)
2025-06-16 14:16:26,093 - test_probabilistic_inference_engine - INFO -   Test dimension: 8
2025-06-16 14:16:26,093 - test_probabilistic_inference_engine - INFO -   Samples: 5000, Warmup: 2000
2025-06-16 14:16:26,093 - test_probabilistic_inference_engine - INFO -   Mixing ratio: 0.251
2025-06-16 14:16:26,093 - test_probabilistic_inference_engine - INFO - MCMC sampling test passed with proper convergence validation
2025-06-16 14:16:26,101 - test_probabilistic_inference_engine - INFO - Testing normalizing flow...
2025-06-16 14:16:26,110 - test_probabilistic_inference_engine - INFO - Normalizing flow test passed
2025-06-16 14:16:26,117 - test_probabilistic_inference_engine - INFO - Testing performance benchmarks...
2025-06-16 14:16:26,144 - test_probabilistic_inference_engine - INFO - Likelihood computation time: 0.22ms
2025-06-16 14:16:26,145 - test_probabilistic_inference_engine - INFO - Posterior approximation time: 0.53ms
2025-06-16 14:16:26,149 - test_probabilistic_inference_engine - INFO - Testing predictive distribution...
2025-06-16 14:16:26,155 - test_probabilistic_inference_engine - INFO - Predictive mean range: [-1.076, 3.813]
2025-06-16 14:16:26,155 - test_probabilistic_inference_engine - INFO - Predictive std range: [6.358, 10.061]
2025-06-16 14:16:26,159 - test_probabilistic_inference_engine - INFO - Testing prior computation...
2025-06-16 14:16:26,161 - test_probabilistic_inference_engine - INFO - Prior mean norm: 0.000
2025-06-16 14:16:26,162 - test_probabilistic_inference_engine - INFO - Prior covariance trace: 64.000
2025-06-16 14:16:26,166 - test_probabilistic_inference_engine - INFO - Testing uncertainty quantification...
2025-06-16 14:16:26,170 - test_probabilistic_inference_engine - INFO - Epistemic uncertainty mean: 63.1776
2025-06-16 14:16:26,170 - test_probabilistic_inference_engine - INFO - Aleatoric uncertainty mean: 0.1000
2025-06-16 14:16:26,170 - test_probabilistic_inference_engine - INFO - Total uncertainty mean: 63.2776
2025-06-16 14:16:26,175 - test_probabilistic_inference_engine - INFO - Testing variational posterior approximation...
2025-06-16 14:16:26,177 - test_probabilistic_inference_engine - INFO - Posterior-prior mean difference: 1.846
2025-06-16 14:16:26,177 - test_probabilistic_inference_engine - INFO - Variational posterior approximation test passed
2025-06-16 14:16:26,181 - test_probabilistic_inference_engine - INFO - Testing end-to-end Bayesian inference...
2025-06-16 14:16:26,213 - test_probabilistic_inference_engine - INFO - Posterior error: 5.992
2025-06-16 14:16:26,213 - test_probabilistic_inference_engine - INFO - Prior error: 5.623
2025-06-16 14:16:26,214 - test_probabilistic_inference_engine - INFO - Evidence: 0.429
2025-06-16 14:16:26,214 - test_probabilistic_inference_engine - INFO - Posterior did not improve over prior (may be due to limited data)
2025-06-16 14:16:26,223 - test_probabilistic_inference_engine - INFO - Testing model comparison workflow...
2025-06-16 14:16:26,242 - test_probabilistic_inference_engine - INFO - True noise level: 0.2
2025-06-16 14:16:26,243 - test_probabilistic_inference_engine - INFO - Selected noise level: 0.5
2025-06-16 14:16:26,243 - test_probabilistic_inference_engine - INFO - Model probabilities: {'model_0': 0.0, 'model_1': 0.0, 'model_2': 1.0}
2025-06-16 14:16:26,243 - test_probabilistic_inference_engine - INFO - ================================================================================
2025-06-16 14:16:26,243 - test_probabilistic_inference_engine - INFO - PROBABILISTIC INFERENCE ENGINE TEST SUMMARY
2025-06-16 14:16:26,244 - test_probabilistic_inference_engine - INFO - ================================================================================
2025-06-16 14:16:26,244 - test_probabilistic_inference_engine - INFO - Tests run: 14
2025-06-16 14:16:26,244 - test_probabilistic_inference_engine - INFO - Failures: 0
2025-06-16 14:16:26,244 - test_probabilistic_inference_engine - INFO - Errors: 0
2025-06-16 14:16:26,244 - test_probabilistic_inference_engine - INFO - Success rate: 100.00%
2025-06-16 14:16:26,245 - __main__ - INFO - Suite probabilistic_inference completed in 4.33s - PASSED
2025-06-16 14:16:26,330 - __main__ - INFO - Executing priority 3 tests (1 suites)
2025-06-16 14:16:26,346 - __main__ - INFO - Executing test suite: comprehensive_integration
2025-06-16 14:16:28,181 - test_comprehensive_integration - INFO - Integration tests starting on cpu
2025-06-16 14:16:28,181 - test_comprehensive_integration - INFO - Initial resources: {'cpu_percent': 27.5, 'memory_percent': 48.2, 'memory_used_gb': 3.3669586181640625, 'gpu_memory_mb': 0}
2025-06-16 14:16:28,181 - test_comprehensive_integration - INFO - Testing end-to-end scientific reasoning...
2025-06-16 14:16:29,245 - test_comprehensive_integration - INFO - Scientific reasoning completed in 0.06s
2025-06-16 14:16:29,245 - test_comprehensive_integration - INFO - Hypothesis error: 3.9301
2025-06-16 14:16:29,245 - test_comprehensive_integration - INFO - Epistemic uncertainty: 0.9840
2025-06-16 14:16:30,317 - test_comprehensive_integration - INFO - Final resources: {'cpu_percent': 11.6, 'memory_percent': 48.2, 'memory_used_gb': 3.366374969482422, 'gpu_memory_mb': 0}
2025-06-16 14:16:31,318 - test_comprehensive_integration - INFO - Integration tests starting on cpu
2025-06-16 14:16:31,318 - test_comprehensive_integration - INFO - Initial resources: {'cpu_percent': 17.5, 'memory_percent': 48.4, 'memory_used_gb': 3.3877220153808594, 'gpu_memory_mb': 0}
2025-06-16 14:16:31,318 - test_comprehensive_integration - INFO - Testing analogical reasoning integration...
2025-06-16 14:16:31,322 - test_comprehensive_integration - INFO - Expected analogical relation similarity: 0.908
2025-06-16 14:16:32,329 - test_comprehensive_integration - INFO - Analogical reasoning completed in 0.01s
2025-06-16 14:16:32,330 - test_comprehensive_integration - INFO - Relation similarity: 0.4002
2025-06-16 14:16:32,330 - test_comprehensive_integration - INFO - Target similarity: 0.8787
2025-06-16 14:16:32,331 - test_comprehensive_integration - INFO - Proportional consistency: 0.8685
2025-06-16 14:16:33,411 - test_comprehensive_integration - INFO - Final resources: {'cpu_percent': 4.5, 'memory_percent': 47.9, 'memory_used_gb': 3.347827911376953, 'gpu_memory_mb': 0}
2025-06-16 14:16:34,412 - test_comprehensive_integration - INFO - Integration tests starting on cpu
2025-06-16 14:16:34,413 - test_comprehensive_integration - INFO - Initial resources: {'cpu_percent': 2.0, 'memory_percent': 47.8, 'memory_used_gb': 3.3383445739746094, 'gpu_memory_mb': 0}
2025-06-16 14:16:34,413 - test_comprehensive_integration - INFO - Testing multi-modal reasoning integration...
2025-06-16 14:16:35,423 - test_comprehensive_integration - INFO - Multi-modal reasoning completed in 0.01s
2025-06-16 14:16:35,424 - test_comprehensive_integration - INFO - Target similarity: 0.9687
2025-06-16 14:16:35,426 - test_comprehensive_integration - INFO - Cross-modal similarity: 0.4859
2025-06-16 14:16:36,508 - test_comprehensive_integration - INFO - Final resources: {'cpu_percent': 10.6, 'memory_percent': 47.5, 'memory_used_gb': 3.312824249267578, 'gpu_memory_mb': 0}
2025-06-16 14:16:37,509 - test_comprehensive_integration - INFO - Integration tests starting on cpu
2025-06-16 14:16:37,510 - test_comprehensive_integration - INFO - Initial resources: {'cpu_percent': 9.0, 'memory_percent': 47.4, 'memory_used_gb': 3.3112564086914062, 'gpu_memory_mb': 0}
2025-06-16 14:16:37,510 - test_comprehensive_integration - INFO - Testing mathematical consistency across components...
2025-06-16 14:16:38,514 - test_comprehensive_integration - INFO - Mathematical consistency test completed in 0.00s
2025-06-16 14:16:38,515 - test_comprehensive_integration - INFO - Overall consistency score: 0.7681
2025-06-16 14:16:38,515 - test_comprehensive_integration - INFO - Individual scores: {'encoding_decoding': 0.5334910710796392, 'diffusion_forward_reverse': 0.5387580651304098, 'uncertainty_repeatability': 1.0, 'likelihood_repeatability': 1.0}
2025-06-16 14:16:39,600 - test_comprehensive_integration - INFO - Final resources: {'cpu_percent': 2.0, 'memory_percent': 47.4, 'memory_used_gb': 3.306499481201172, 'gpu_memory_mb': 0}
2025-06-16 14:16:40,601 - test_comprehensive_integration - INFO - Integration tests starting on cpu
2025-06-16 14:16:40,601 - test_comprehensive_integration - INFO - Initial resources: {'cpu_percent': 11.6, 'memory_percent': 47.4, 'memory_used_gb': 3.3066253662109375, 'gpu_memory_mb': 0}
2025-06-16 14:16:40,601 - test_comprehensive_integration - INFO - Testing stress conditions and scalability...
2025-06-16 14:16:54,001 - test_comprehensive_integration - INFO - Stress testing completed in 12.40s
2025-06-16 14:16:54,001 - test_comprehensive_integration - INFO - Performance metrics: {'avg_batch_processing_time': 0.004823708534240722, 'concurrent_processing_time': 0.00646662712097168, 'memory_increase_per_100_samples': 0.00131988525390625, 'reasoning_chain_time': 0.005296468734741211}
2025-06-16 14:16:55,084 - test_comprehensive_integration - INFO - Final resources: {'cpu_percent': 7.0, 'memory_percent': 47.4, 'memory_used_gb': 3.3114166259765625, 'gpu_memory_mb': 0}
2025-06-16 14:16:56,085 - test_comprehensive_integration - INFO - Integration tests starting on cpu
2025-06-16 14:16:56,086 - test_comprehensive_integration - INFO - Initial resources: {'cpu_percent': 18.1, 'memory_percent': 47.2, 'memory_used_gb': 3.293529510498047, 'gpu_memory_mb': 0}
2025-06-16 14:16:56,086 - test_comprehensive_integration - INFO - Testing production-grade integration scenarios...
2025-06-16 14:16:57,157 - test_comprehensive_integration - INFO - Production integration completed in 0.07s
2025-06-16 14:16:57,157 - test_comprehensive_integration - INFO - Production metrics: {'avg_processing_time': 0.0021830081939697267, 'avg_reasoning_accuracy': 1.4079138824814244, 'real_time_compliance': 1.0, 'batch_processing_time': 0.024809837341308594, 'avg_quality_score': 0.8, 'error_recovery_rate': 1.0, 'production_readiness_score': 1.0823741647444274}
2025-06-16 14:16:58,229 - test_comprehensive_integration - INFO - Final resources: {'cpu_percent': 1.0, 'memory_percent': 47.3, 'memory_used_gb': 3.300098419189453, 'gpu_memory_mb': 0}
2025-06-16 14:16:59,230 - test_comprehensive_integration - INFO - Integration tests starting on cpu
2025-06-16 14:16:59,230 - test_comprehensive_integration - INFO - Initial resources: {'cpu_percent': 4.0, 'memory_percent': 47.3, 'memory_used_gb': 3.3008193969726562, 'gpu_memory_mb': 0}
2025-06-16 14:16:59,230 - test_comprehensive_integration - INFO - Generating comprehensive test report...
2025-06-16 14:16:59,232 - test_comprehensive_integration - INFO - ================================================================================
2025-06-16 14:16:59,232 - test_comprehensive_integration - INFO - DIFFUSION REASONING INTEGRATION TEST REPORT
2025-06-16 14:16:59,232 - test_comprehensive_integration - INFO - ================================================================================
2025-06-16 14:16:59,232 - test_comprehensive_integration - INFO - Total Tests: 6
2025-06-16 14:16:59,233 - test_comprehensive_integration - INFO - Passed: 6
2025-06-16 14:16:59,233 - test_comprehensive_integration - INFO - Failed: 0
2025-06-16 14:16:59,233 - test_comprehensive_integration - INFO - Success Rate: 100.00%
2025-06-16 14:16:59,233 - test_comprehensive_integration - INFO - Total Execution Time: 12.56s
2025-06-16 14:16:59,233 - test_comprehensive_integration - INFO - Average Execution Time: 2.09s
2025-06-16 14:16:59,233 - test_comprehensive_integration - INFO - Max Memory Usage: 3.37GB
2025-06-16 14:16:59,234 - test_comprehensive_integration - INFO - ================================================================================
2025-06-16 14:17:00,303 - test_comprehensive_integration - INFO - Final resources: {'cpu_percent': 12.0, 'memory_percent': 47.3, 'memory_used_gb': 3.3014907836914062, 'gpu_memory_mb': 0}
2025-06-16 14:17:00,303 - test_comprehensive_integration - INFO - ================================================================================
2025-06-16 14:17:00,303 - test_comprehensive_integration - INFO - COMPREHENSIVE INTEGRATION TEST SUMMARY
2025-06-16 14:17:00,304 - test_comprehensive_integration - INFO - ================================================================================
2025-06-16 14:17:00,304 - test_comprehensive_integration - INFO - Tests run: 7
2025-06-16 14:17:00,304 - test_comprehensive_integration - INFO - Failures: 0
2025-06-16 14:17:00,304 - test_comprehensive_integration - INFO - Errors: 0
2025-06-16 14:17:00,304 - test_comprehensive_integration - INFO - Success rate: 100.00%
2025-06-16 14:17:00,304 - __main__ - INFO - Suite comprehensive_integration completed in 33.96s - PASSED
2025-06-16 14:17:00,375 - __main__ - INFO - Generating final test report...
2025-06-16 14:17:00,378 - __main__ - INFO - Detailed report saved to test_results/final_test_report.json
2025-06-16 14:17:00,378 - __main__ - INFO - ================================================================================
2025-06-16 14:17:00,379 - __main__ - INFO - ULTRA DIFFUSION REASONING MODULE - TEST EXECUTION COMPLETE
2025-06-16 14:17:00,379 - __main__ - INFO - ================================================================================
2025-06-16 14:17:00,379 - __main__ - INFO - Execution Summary:
2025-06-16 14:17:00,379 - __main__ - INFO -   Total Test Suites: 6
2025-06-16 14:17:00,379 - __main__ - INFO -   Passed Suites: 5
2025-06-16 14:17:00,379 - __main__ - INFO -   Failed Suites: 1
2025-06-16 14:17:00,379 - __main__ - INFO -   Success Rate: 83.3%
2025-06-16 14:17:00,380 - __main__ - INFO -   Total Execution Time: 1m 5s
2025-06-16 14:17:00,380 - __main__ - INFO - 
Suite Details:
2025-06-16 14:17:00,380 - __main__ - INFO -   conceptual_diffusion: PASSED (5.2s)
2025-06-16 14:17:00,380 - __main__ - INFO -   thought_latent_space: PASSED (26.1s)
2025-06-16 14:17:00,381 - __main__ - INFO -   bayesian_uncertainty: FAILED (0.0s)
2025-06-16 14:17:00,381 - __main__ - INFO -   reverse_diffusion_reasoning: PASSED (3.7s)
2025-06-16 14:17:00,381 - __main__ - INFO -   probabilistic_inference: PASSED (4.3s)
2025-06-16 14:17:00,381 - __main__ - INFO -   comprehensive_integration: PASSED (34.0s)
2025-06-16 14:17:00,381 - __main__ - WARNING - 
Failed Suites:
2025-06-16 14:17:00,381 - __main__ - WARNING -   bayesian_uncertainty: 2 errors
2025-06-16 14:17:00,382 - __main__ - WARNING -     - No module named 'test_bayesian_uncertainty'...
2025-06-16 14:17:00,382 - __main__ - WARNING -     - Traceback (most recent call last):
  File "/workspaces/Ultra/ultra/ultra/diffusion_reasoning/run_all...
2025-06-16 14:17:00,382 - __main__ - INFO - ================================================================================
2025-06-16 14:17:00,382 - __main__ - WARNING - Most tests passed, but some failures detected
2025-06-16 14:18:07,530 - __main__ - INFO - Setting up test environment...
2025-06-16 14:18:07,532 - __main__ - INFO - System info: {'cpu_count': 2, 'cpu_percent': 66.0, 'memory_total_gb': 7.751636505126953, 'memory_available_gb': 4.180660247802734, 'memory_percent': 46.1, 'gpu_available': False, 'gpu_count': 0, 'gpu_memory_gb': 0}
2025-06-16 14:18:07,532 - __main__ - INFO - Using device: cpu
2025-06-16 14:18:07,533 - __main__ - INFO - Test environment setup complete
2025-06-16 14:18:07,533 - __main__ - INFO - Initialized test orchestrator with 6 test suites
2025-06-16 14:18:07,533 - __main__ - INFO - ================================================================================
2025-06-16 14:18:07,533 - __main__ - INFO - STARTING ULTRA DIFFUSION REASONING MODULE TESTS
2025-06-16 14:18:07,533 - __main__ - INFO - ================================================================================
2025-06-16 14:18:07,533 - __main__ - INFO - Estimated total execution time: 43 minutes 20 seconds
2025-06-16 14:18:07,533 - __main__ - INFO - Running tests in parallel with 4 workers
2025-06-16 14:18:07,533 - __main__ - INFO - Executing priority 1 tests (3 suites)
2025-06-16 14:18:07,552 - __main__ - INFO - Executing test suite: conceptual_diffusion
2025-06-16 14:18:07,553 - __main__ - INFO - Executing test suite: thought_latent_space
2025-06-16 14:18:09,164 - test_thought_latent_space - INFO - Testing compositional operations...
2025-06-16 14:18:09,203 - test_thought_latent_space - INFO - Compositional operations tests passed
2025-06-16 14:18:09,231 - test_thought_latent_space - INFO - Testing encoding-decoding consistency...
2025-06-16 14:18:09,299 - test_thought_latent_space - INFO - Reconstruction error: 0.192392
2025-06-16 14:18:09,327 - test_thought_latent_space - INFO - Testing hierarchical structure preservation...
2025-06-16 14:18:09,389 - test_thought_latent_space - INFO - Training hierarchical structure classifier...
2025-06-16 14:18:09,579 - test_conceptual_diffusion - INFO - Testing conceptual space properties...
2025-06-16 14:18:09,600 - test_conceptual_diffusion - INFO - Within-level similarity: 0.186
2025-06-16 14:18:09,600 - test_conceptual_diffusion - INFO - Across-level similarity: 0.224
2025-06-16 14:18:09,600 - test_conceptual_diffusion - INFO - Semantic similarity preservation: 0.991
2025-06-16 14:18:09,627 - test_conceptual_diffusion - INFO - Testing forward diffusion process...
2025-06-16 14:18:09,633 - test_conceptual_diffusion - INFO - Forward diffusion process tests passed
2025-06-16 14:18:09,659 - test_conceptual_diffusion - INFO - Testing loss computation...
2025-06-16 14:18:09,678 - test_conceptual_diffusion - INFO - Standard loss: 1.418071
2025-06-16 14:18:09,680 - test_conceptual_diffusion - INFO - VLB loss: 95.893036
2025-06-16 14:18:09,713 - test_conceptual_diffusion - INFO - Testing mathematical consistency...
2025-06-16 14:18:09,740 - test_conceptual_diffusion - INFO - Reconstruction error: 0.818803
2025-06-16 14:18:09,767 - test_conceptual_diffusion - INFO - Testing noise schedule properties...
2025-06-16 14:18:09,769 - test_conceptual_diffusion - INFO - Beta range: [0.000100, 0.050000]
2025-06-16 14:18:09,770 - test_conceptual_diffusion - INFO - Alpha cumprod range: [0.999900, 0.078234]
2025-06-16 14:18:09,808 - test_conceptual_diffusion - INFO - Testing performance benchmarks...
2025-06-16 14:18:10,233 - test_conceptual_diffusion - INFO - Forward diffusion time: 0.15ms
2025-06-16 14:18:10,233 - test_conceptual_diffusion - INFO - Reverse diffusion time: 2.09ms
2025-06-16 14:18:10,234 - test_conceptual_diffusion - INFO - Loss computation time: 2.01ms
2025-06-16 14:18:10,259 - test_conceptual_diffusion - INFO - Testing reverse diffusion step...
2025-06-16 14:18:10,262 - test_conceptual_diffusion - INFO - Noise reduction ratio: 1.006
2025-06-16 14:18:10,306 - test_conceptual_diffusion - INFO - Testing sampling process...
2025-06-16 14:18:10,637 - test_conceptual_diffusion - INFO - Initial noise level: 7.137
2025-06-16 14:18:10,639 - test_conceptual_diffusion - INFO - Final noise level: 2.042
2025-06-16 14:18:10,640 - test_conceptual_diffusion - INFO - Noise reduction: 0.714
2025-06-16 14:18:10,679 - test_conceptual_diffusion - INFO - Testing score network properties...
2025-06-16 14:18:10,690 - test_conceptual_diffusion - INFO - Score network output range: [0.000, 0.000]
2025-06-16 14:18:10,740 - test_conceptual_diffusion - INFO - Testing memory efficiency...
2025-06-16 14:18:10,741 - test_conceptual_diffusion - INFO - CUDA not available, skipping memory test
2025-06-16 14:18:10,800 - test_conceptual_diffusion - INFO - Testing training integration...
2025-06-16 14:18:11,729 - test_thought_latent_space - INFO - Epoch 0: Loss=2.4782, Accuracy=0.719
2025-06-16 14:18:12,276 - test_thought_latent_space - INFO - Epoch 1: Loss=1.4149, Accuracy=1.000
2025-06-16 14:18:12,815 - test_thought_latent_space - INFO - Epoch 2: Loss=1.1631, Accuracy=1.000
2025-06-16 14:18:12,975 - test_conceptual_diffusion - INFO - Initial loss: 1.109442
2025-06-16 14:18:12,976 - test_conceptual_diffusion - INFO - Final loss: 1.041772
2025-06-16 14:18:12,976 - test_conceptual_diffusion - INFO - Relative change: 0.061
2025-06-16 14:18:12,978 - test_conceptual_diffusion - INFO - ================================================================================
2025-06-16 14:18:12,978 - test_conceptual_diffusion - INFO - CONCEPTUAL DIFFUSION TEST SUMMARY
2025-06-16 14:18:12,979 - test_conceptual_diffusion - INFO - ================================================================================
2025-06-16 14:18:12,979 - test_conceptual_diffusion - INFO - Tests run: 11
2025-06-16 14:18:12,979 - test_conceptual_diffusion - INFO - Failures: 0
2025-06-16 14:18:12,979 - test_conceptual_diffusion - INFO - Errors: 0
2025-06-16 14:18:12,979 - test_conceptual_diffusion - INFO - Success rate: 100.00%
2025-06-16 14:18:12,982 - __main__ - INFO - Suite conceptual_diffusion completed in 5.43s - PASSED
2025-06-16 14:18:13,204 - test_thought_latent_space - INFO - Epoch 3: Loss=0.9312, Accuracy=1.000
2025-06-16 14:18:13,499 - test_thought_latent_space - INFO - Epoch 4: Loss=0.8466, Accuracy=1.000
2025-06-16 14:18:13,793 - test_thought_latent_space - INFO - Epoch 5: Loss=0.7527, Accuracy=1.000
2025-06-16 14:18:14,093 - test_thought_latent_space - INFO - Epoch 6: Loss=0.7162, Accuracy=1.000
2025-06-16 14:18:14,396 - test_thought_latent_space - INFO - Epoch 7: Loss=0.6711, Accuracy=1.000
2025-06-16 14:18:14,712 - test_thought_latent_space - INFO - Epoch 8: Loss=0.6426, Accuracy=1.000
2025-06-16 14:18:15,022 - test_thought_latent_space - INFO - Epoch 9: Loss=0.6258, Accuracy=1.000
2025-06-16 14:18:18,455 - test_thought_latent_space - INFO - Epoch 20: Loss=0.5724, Accuracy=1.000
2025-06-16 14:18:21,735 - test_thought_latent_space - INFO - Achieved high accuracy 1.000 at epoch 31
2025-06-16 14:18:21,735 - test_thought_latent_space - INFO - Testing trained hierarchical structure...
2025-06-16 14:18:21,866 - test_thought_latent_space - INFO - Final test accuracy: 1.000
2025-06-16 14:18:21,867 - test_thought_latent_space - INFO - Training achieved best accuracy: 1.000
2025-06-16 14:18:21,867 - test_thought_latent_space - INFO - Hierarchical coherence: 1.013
2025-06-16 14:18:21,867 - test_thought_latent_space - INFO - Level prediction accuracy: 1.000
2025-06-16 14:18:21,895 - test_thought_latent_space - INFO - Testing interpolation properties...
2025-06-16 14:18:21,911 - test_thought_latent_space - INFO - Interpolation tests passed
2025-06-16 14:18:21,933 - test_thought_latent_space - INFO - Testing loss computation...
2025-06-16 14:18:21,961 - test_thought_latent_space - INFO - Total loss: 1.124640
2025-06-16 14:18:21,961 - test_thought_latent_space - INFO - Reconstruction loss: 0.187285
2025-06-16 14:18:21,961 - test_thought_latent_space - INFO - Hierarchical loss: 1.097462
2025-06-16 14:18:21,981 - test_thought_latent_space - INFO - Testing manifold structure analysis...
2025-06-16 14:18:22,175 - test_thought_latent_space - INFO - Intrinsic dimensionality: 36
2025-06-16 14:18:22,175 - test_thought_latent_space - INFO - Explained variance ratio: 0.951
2025-06-16 14:18:22,175 - test_thought_latent_space - INFO - Local smoothness: 9.172
2025-06-16 14:18:22,175 - test_thought_latent_space - INFO - Condition number: 999.000
2025-06-16 14:18:22,231 - test_thought_latent_space - INFO - Testing memory operations...
2025-06-16 14:18:22,262 - test_thought_latent_space - INFO - Memory operations tests passed
2025-06-16 14:18:22,287 - test_thought_latent_space - INFO - Testing performance benchmarks...
2025-06-16 14:18:32,483 - test_thought_latent_space - INFO - Single encoding time: 2.31ms
2025-06-16 14:18:32,483 - test_thought_latent_space - INFO - Batch processing time: 46.45ms
2025-06-16 14:18:32,504 - test_thought_latent_space - INFO - Testing relational vector computation...
2025-06-16 14:18:32,525 - test_thought_latent_space - INFO - Relational vector tests passed
2025-06-16 14:18:32,547 - test_thought_latent_space - INFO - Testing semantic similarity computation...
2025-06-16 14:18:32,665 - test_thought_latent_space - INFO - Same cluster similarity: 0.488
2025-06-16 14:18:32,665 - test_thought_latent_space - INFO - Different cluster similarity: 0.491
2025-06-16 14:18:32,683 - test_thought_latent_space - INFO - Testing integration with diffusion process...
2025-06-16 14:18:32,744 - test_thought_latent_space - INFO - Noise level 0.1: degradation 0.1864
2025-06-16 14:18:32,749 - test_thought_latent_space - INFO - Noise level 0.3: degradation 0.1983
2025-06-16 14:18:32,753 - test_thought_latent_space - INFO - Noise level 0.5: degradation 0.1893
2025-06-16 14:18:32,757 - test_thought_latent_space - INFO - Noise level 0.8: degradation 0.1859
2025-06-16 14:18:32,757 - test_thought_latent_space - INFO - Diffusion integration test passed
2025-06-16 14:18:32,777 - test_thought_latent_space - INFO - Testing training integration...
2025-06-16 14:18:33,468 - test_thought_latent_space - INFO - Initial loss: 0.996349
2025-06-16 14:18:33,468 - test_thought_latent_space - INFO - Final loss: 0.675796
2025-06-16 14:18:33,468 - test_thought_latent_space - INFO - Loss ratio: 0.678
2025-06-16 14:18:33,469 - test_thought_latent_space - INFO - ================================================================================
2025-06-16 14:18:33,469 - test_thought_latent_space - INFO - THOUGHT LATENT SPACE TEST SUMMARY
2025-06-16 14:18:33,469 - test_thought_latent_space - INFO - ================================================================================
2025-06-16 14:18:33,469 - test_thought_latent_space - INFO - Tests run: 12
2025-06-16 14:18:33,469 - test_thought_latent_space - INFO - Failures: 0
2025-06-16 14:18:33,469 - test_thought_latent_space - INFO - Errors: 0
2025-06-16 14:18:33,470 - test_thought_latent_space - INFO - Success rate: 100.00%
2025-06-16 14:18:33,471 - __main__ - INFO - Suite thought_latent_space completed in 25.92s - PASSED
2025-06-16 14:18:33,515 - __main__ - INFO - Executing test suite: bayesian_uncertainty
2025-06-16 14:18:33,516 - __main__ - ERROR - Suite bayesian_uncertainty failed with exception: No module named 'test_bayesian_uncertainty'
2025-06-16 14:18:33,517 - __main__ - ERROR - Traceback (most recent call last):
  File "/workspaces/Ultra/ultra/ultra/diffusion_reasoning/run_all_reasoning_diffusion_tests.py", line 339, in execute_test_suite
    module = __import__(module_name)
             ^^^^^^^^^^^^^^^^^^^^^^^
ModuleNotFoundError: No module named 'test_bayesian_uncertainty'

2025-06-16 14:18:33,568 - __main__ - INFO - Executing priority 2 tests (2 suites)
2025-06-16 14:18:33,582 - __main__ - INFO - Executing test suite: reverse_diffusion_reasoning
2025-06-16 14:18:33,582 - __main__ - INFO - Executing test suite: probabilistic_inference
2025-06-16 14:18:35,222 - test_reverse_diffusion_reasoning - INFO - Testing analogical reasoning...
2025-06-16 14:18:35,227 - test_reverse_diffusion_reasoning - INFO - Analogical relation similarity: 0.999
2025-06-16 14:18:35,236 - test_reverse_diffusion_reasoning - INFO - Testing constraint satisfaction scoring...
2025-06-16 14:18:35,237 - test_reverse_diffusion_reasoning - INFO - Constraint satisfaction score: 0.000
2025-06-16 14:18:35,237 - test_reverse_diffusion_reasoning - INFO - Perfect constraint score: 1.000
2025-06-16 14:18:35,242 - test_reverse_diffusion_reasoning - INFO - Testing guidance function properties...
2025-06-16 14:18:35,245 - test_reverse_diffusion_reasoning - INFO - Guidance function tests passed
2025-06-16 14:18:35,250 - test_reverse_diffusion_reasoning - INFO - Testing guided reverse step...
2025-06-16 14:18:35,254 - test_reverse_diffusion_reasoning - INFO - Samples improved: 8/8
2025-06-16 14:18:35,254 - test_reverse_diffusion_reasoning - INFO - Average distance change: 8.471
2025-06-16 14:18:35,262 - test_reverse_diffusion_reasoning - INFO - Testing interpolative reasoning...
2025-06-16 14:18:35,346 - test_reverse_diffusion_reasoning - INFO - Similarity trend to A: -0.200
2025-06-16 14:18:35,348 - test_reverse_diffusion_reasoning - INFO - Similarity trend to B: 0.211
2025-06-16 14:18:35,370 - test_reverse_diffusion_reasoning - INFO - Testing mathematical properties...
2025-06-16 14:18:35,377 - test_reverse_diffusion_reasoning - INFO - Guided computation difference: 8.8671
2025-06-16 14:18:35,396 - test_reverse_diffusion_reasoning - INFO - Testing multi-constraint reasoning...
2025-06-16 14:18:35,441 - test_reverse_diffusion_reasoning - INFO - Constraints improved: 1/2
2025-06-16 14:18:35,442 - test_reverse_diffusion_reasoning - INFO - Overall satisfaction - Initial: -37.097, Final: -63.947
2025-06-16 14:18:35,442 - test_reverse_diffusion_reasoning - INFO - Process completed successfully with trajectory variance: 4.747659
2025-06-16 14:18:35,456 - test_reverse_diffusion_reasoning - INFO - Testing performance benchmarks...
2025-06-16 14:18:35,611 - test_probabilistic_inference_engine - INFO - Testing Bayesian model selection...
2025-06-16 14:18:35,613 - test_reverse_diffusion_reasoning - INFO - Single step time: 0.87ms
2025-06-16 14:18:35,615 - test_reverse_diffusion_reasoning - INFO - Full reasoning time: 6.80ms
2025-06-16 14:18:35,622 - test_reverse_diffusion_reasoning - INFO - Testing goal-directed reasoning...
2025-06-16 14:18:35,638 - test_probabilistic_inference_engine - INFO - Model probabilities: {'model_0': 0.0, 'model_1': 0.0, 'model_2': 1.0}
2025-06-16 14:18:35,645 - test_probabilistic_inference_engine - INFO - Testing information-theoretic measures...
2025-06-16 14:18:35,648 - test_reverse_diffusion_reasoning - INFO - Convergence ratio: 2.785
2025-06-16 14:18:35,649 - test_reverse_diffusion_reasoning - INFO - Final confidence: 0.000
2025-06-16 14:18:35,650 - test_reverse_diffusion_reasoning - INFO - Initial confidence: 0.000
2025-06-16 14:18:35,651 - test_reverse_diffusion_reasoning - INFO - Path variance: 11.810256
2025-06-16 14:18:35,651 - test_reverse_diffusion_reasoning - INFO - Confidence ratio: 0.218
2025-06-16 14:18:35,665 - test_reverse_diffusion_reasoning - INFO - Testing reasoning trajectory analysis...
2025-06-16 14:18:35,666 - test_probabilistic_inference_engine - INFO - Entropy: 4.605
2025-06-16 14:18:35,668 - test_probabilistic_inference_engine - INFO - Mutual information: 2.303
2025-06-16 14:18:35,668 - test_probabilistic_inference_engine - INFO - Effective dimensionality: 52.966
2025-06-16 14:18:35,670 - test_reverse_diffusion_reasoning - INFO - Convergence rate: 0.845
2025-06-16 14:18:35,671 - test_reverse_diffusion_reasoning - INFO - Monotonicity: 1.000
2025-06-16 14:18:35,671 - test_reverse_diffusion_reasoning - INFO - Final distance: 1.675
2025-06-16 14:18:35,679 - test_probabilistic_inference_engine - INFO - Testing likelihood computation...
2025-06-16 14:18:35,683 - test_reverse_diffusion_reasoning - INFO - Testing end-to-end reasoning...
2025-06-16 14:18:35,684 - test_probabilistic_inference_engine - INFO - Log likelihood range: [-0.385, 0.388]
2025-06-16 14:18:35,695 - test_probabilistic_inference_engine - INFO - Testing marginal likelihood computation...
2025-06-16 14:18:35,702 - test_reverse_diffusion_reasoning - INFO - End-to-end reasoning test passed
2025-06-16 14:18:35,714 - test_reverse_diffusion_reasoning - INFO - Testing training integration...
2025-06-16 14:18:35,758 - test_probabilistic_inference_engine - INFO - Log evidence (importance_sampling): -0.341
2025-06-16 14:18:35,821 - test_probabilistic_inference_engine - INFO - Log evidence (variational): -4.724
2025-06-16 14:18:35,828 - test_probabilistic_inference_engine - INFO - Log evidence (network): 0.058
2025-06-16 14:18:35,842 - test_probabilistic_inference_engine - INFO - Testing mathematical properties...
2025-06-16 14:18:35,848 - test_probabilistic_inference_engine - INFO - KL divergence: 54.247673
2025-06-16 14:18:35,850 - test_probabilistic_inference_engine - INFO - Mathematical properties test passed
2025-06-16 14:18:35,859 - test_probabilistic_inference_engine - INFO - Testing MCMC sampling...
2025-06-16 14:18:37,332 - test_reverse_diffusion_reasoning - INFO - Training integration test passed
2025-06-16 14:18:37,333 - test_reverse_diffusion_reasoning - INFO - ================================================================================
2025-06-16 14:18:37,334 - test_reverse_diffusion_reasoning - INFO - REVERSE DIFFUSION REASONING TEST SUMMARY
2025-06-16 14:18:37,335 - test_reverse_diffusion_reasoning - INFO - ================================================================================
2025-06-16 14:18:37,335 - test_reverse_diffusion_reasoning - INFO - Tests run: 12
2025-06-16 14:18:37,335 - test_reverse_diffusion_reasoning - INFO - Failures: 0
2025-06-16 14:18:37,335 - test_reverse_diffusion_reasoning - INFO - Errors: 0
2025-06-16 14:18:37,336 - test_reverse_diffusion_reasoning - INFO - Success rate: 100.00%
2025-06-16 14:18:37,337 - __main__ - INFO - Suite reverse_diffusion_reasoning completed in 3.75s - PASSED
2025-06-16 14:18:37,881 - test_probabilistic_inference_engine - INFO - MCMC Diagnostics:
2025-06-16 14:18:37,881 - test_probabilistic_inference_engine - INFO -   Acceptance rate: 0.253
2025-06-16 14:18:37,881 - test_probabilistic_inference_engine - INFO -   Mean error: 0.0952 (tolerance: 0.5532) ✓
2025-06-16 14:18:37,881 - test_probabilistic_inference_engine - INFO -   Cov error: 0.1013 (tolerance: 1.1064) ✓
2025-06-16 14:18:37,881 - test_probabilistic_inference_engine - INFO -   Var error: 0.0623 (tolerance: 0.7823) ✓
2025-06-16 14:18:37,881 - test_probabilistic_inference_engine - INFO -   Effective sample size: 105 (autocorr time: 23.4)
2025-06-16 14:18:37,881 - test_probabilistic_inference_engine - INFO -   Efficiency: 2.1% (effective/total)
2025-06-16 14:18:37,881 - test_probabilistic_inference_engine - INFO -   Test dimension: 8
2025-06-16 14:18:37,881 - test_probabilistic_inference_engine - INFO -   Samples: 5000, Warmup: 2000
2025-06-16 14:18:37,881 - test_probabilistic_inference_engine - INFO -   Mixing ratio: 0.245
2025-06-16 14:18:37,882 - test_probabilistic_inference_engine - INFO - MCMC sampling test passed with proper convergence validation
2025-06-16 14:18:37,887 - test_probabilistic_inference_engine - INFO - Testing normalizing flow...
2025-06-16 14:18:37,894 - test_probabilistic_inference_engine - INFO - Normalizing flow test passed
2025-06-16 14:18:37,900 - test_probabilistic_inference_engine - INFO - Testing performance benchmarks...
2025-06-16 14:18:37,929 - test_probabilistic_inference_engine - INFO - Likelihood computation time: 0.23ms
2025-06-16 14:18:37,929 - test_probabilistic_inference_engine - INFO - Posterior approximation time: 0.62ms
2025-06-16 14:18:37,934 - test_probabilistic_inference_engine - INFO - Testing predictive distribution...
2025-06-16 14:18:37,939 - test_probabilistic_inference_engine - INFO - Predictive mean range: [-6.519, 4.509]
2025-06-16 14:18:37,939 - test_probabilistic_inference_engine - INFO - Predictive std range: [7.098, 9.366]
2025-06-16 14:18:37,944 - test_probabilistic_inference_engine - INFO - Testing prior computation...
2025-06-16 14:18:37,946 - test_probabilistic_inference_engine - INFO - Prior mean norm: 0.000
2025-06-16 14:18:37,946 - test_probabilistic_inference_engine - INFO - Prior covariance trace: 64.000
2025-06-16 14:18:37,951 - test_probabilistic_inference_engine - INFO - Testing uncertainty quantification...
2025-06-16 14:18:37,954 - test_probabilistic_inference_engine - INFO - Epistemic uncertainty mean: 60.6422
2025-06-16 14:18:37,954 - test_probabilistic_inference_engine - INFO - Aleatoric uncertainty mean: 0.1000
2025-06-16 14:18:37,954 - test_probabilistic_inference_engine - INFO - Total uncertainty mean: 60.7422
2025-06-16 14:18:37,959 - test_probabilistic_inference_engine - INFO - Testing variational posterior approximation...
2025-06-16 14:18:37,961 - test_probabilistic_inference_engine - INFO - Posterior-prior mean difference: 2.112
2025-06-16 14:18:37,961 - test_probabilistic_inference_engine - INFO - Variational posterior approximation test passed
2025-06-16 14:18:37,965 - test_probabilistic_inference_engine - INFO - Testing end-to-end Bayesian inference...
2025-06-16 14:18:37,995 - test_probabilistic_inference_engine - INFO - Posterior error: 6.860
2025-06-16 14:18:37,995 - test_probabilistic_inference_engine - INFO - Prior error: 5.999
2025-06-16 14:18:37,995 - test_probabilistic_inference_engine - INFO - Evidence: -1.922
2025-06-16 14:18:37,995 - test_probabilistic_inference_engine - INFO - Posterior did not improve over prior (may be due to limited data)
2025-06-16 14:18:38,001 - test_probabilistic_inference_engine - INFO - Testing model comparison workflow...
2025-06-16 14:18:38,015 - test_probabilistic_inference_engine - INFO - True noise level: 0.2
2025-06-16 14:18:38,015 - test_probabilistic_inference_engine - INFO - Selected noise level: 0.5
2025-06-16 14:18:38,015 - test_probabilistic_inference_engine - INFO - Model probabilities: {'model_0': 0.0, 'model_1': 0.0, 'model_2': 1.0}
2025-06-16 14:18:38,016 - test_probabilistic_inference_engine - INFO - ================================================================================
2025-06-16 14:18:38,016 - test_probabilistic_inference_engine - INFO - PROBABILISTIC INFERENCE ENGINE TEST SUMMARY
2025-06-16 14:18:38,016 - test_probabilistic_inference_engine - INFO - ================================================================================
2025-06-16 14:18:38,016 - test_probabilistic_inference_engine - INFO - Tests run: 14
2025-06-16 14:18:38,016 - test_probabilistic_inference_engine - INFO - Failures: 0
2025-06-16 14:18:38,016 - test_probabilistic_inference_engine - INFO - Errors: 0
2025-06-16 14:18:38,016 - test_probabilistic_inference_engine - INFO - Success rate: 100.00%
2025-06-16 14:18:38,017 - __main__ - INFO - Suite probabilistic_inference completed in 4.43s - PASSED
2025-06-16 14:18:38,097 - __main__ - INFO - Executing priority 3 tests (1 suites)
2025-06-16 14:18:38,111 - __main__ - INFO - Executing test suite: comprehensive_integration
2025-06-16 14:18:39,863 - test_comprehensive_integration - INFO - Integration tests starting on cpu
2025-06-16 14:18:39,863 - test_comprehensive_integration - INFO - Initial resources: {'cpu_percent': 11.1, 'memory_percent': 48.0, 'memory_used_gb': 3.35638427734375, 'gpu_memory_mb': 0}
2025-06-16 14:18:39,863 - test_comprehensive_integration - INFO - Testing end-to-end scientific reasoning...
2025-06-16 14:18:40,957 - test_comprehensive_integration - INFO - Scientific reasoning completed in 0.09s
2025-06-16 14:18:40,957 - test_comprehensive_integration - INFO - Hypothesis error: 3.6015
2025-06-16 14:18:40,957 - test_comprehensive_integration - INFO - Epistemic uncertainty: 0.9811
2025-06-16 14:18:42,028 - test_comprehensive_integration - INFO - Final resources: {'cpu_percent': 7.0, 'memory_percent': 48.0, 'memory_used_gb': 3.356334686279297, 'gpu_memory_mb': 0}
2025-06-16 14:18:43,029 - test_comprehensive_integration - INFO - Integration tests starting on cpu
2025-06-16 14:18:43,029 - test_comprehensive_integration - INFO - Initial resources: {'cpu_percent': 2.5, 'memory_percent': 47.9, 'memory_used_gb': 3.3465118408203125, 'gpu_memory_mb': 0}
2025-06-16 14:18:43,029 - test_comprehensive_integration - INFO - Testing analogical reasoning integration...
2025-06-16 14:18:43,033 - test_comprehensive_integration - INFO - Expected analogical relation similarity: 0.908
2025-06-16 14:18:44,046 - test_comprehensive_integration - INFO - Analogical reasoning completed in 0.02s
2025-06-16 14:18:44,046 - test_comprehensive_integration - INFO - Relation similarity: 0.4115
2025-06-16 14:18:44,047 - test_comprehensive_integration - INFO - Target similarity: 0.8913
2025-06-16 14:18:44,047 - test_comprehensive_integration - INFO - Proportional consistency: 0.8628
2025-06-16 14:18:45,117 - test_comprehensive_integration - INFO - Final resources: {'cpu_percent': 9.6, 'memory_percent': 47.4, 'memory_used_gb': 3.305400848388672, 'gpu_memory_mb': 0}
2025-06-16 14:18:46,118 - test_comprehensive_integration - INFO - Integration tests starting on cpu
2025-06-16 14:18:46,118 - test_comprehensive_integration - INFO - Initial resources: {'cpu_percent': 9.0, 'memory_percent': 47.2, 'memory_used_gb': 3.2959671020507812, 'gpu_memory_mb': 0}
2025-06-16 14:18:46,118 - test_comprehensive_integration - INFO - Testing multi-modal reasoning integration...
2025-06-16 14:18:47,126 - test_comprehensive_integration - INFO - Multi-modal reasoning completed in 0.01s
2025-06-16 14:18:47,126 - test_comprehensive_integration - INFO - Target similarity: 0.9684
2025-06-16 14:18:47,126 - test_comprehensive_integration - INFO - Cross-modal similarity: 0.4930
2025-06-16 14:18:48,198 - test_comprehensive_integration - INFO - Final resources: {'cpu_percent': 10.1, 'memory_percent': 46.9, 'memory_used_gb': 3.2725830078125, 'gpu_memory_mb': 0}
2025-06-16 14:18:49,198 - test_comprehensive_integration - INFO - Integration tests starting on cpu
2025-06-16 14:18:49,199 - test_comprehensive_integration - INFO - Initial resources: {'cpu_percent': 3.0, 'memory_percent': 46.9, 'memory_used_gb': 3.2725067138671875, 'gpu_memory_mb': 0}
2025-06-16 14:18:49,199 - test_comprehensive_integration - INFO - Testing mathematical consistency across components...
2025-06-16 14:18:50,205 - test_comprehensive_integration - INFO - Mathematical consistency test completed in 0.00s
2025-06-16 14:18:50,205 - test_comprehensive_integration - INFO - Overall consistency score: 0.7681
2025-06-16 14:18:50,205 - test_comprehensive_integration - INFO - Individual scores: {'encoding_decoding': 0.5334904773318913, 'diffusion_forward_reverse': 0.5387545876801446, 'uncertainty_repeatability': 1.0, 'likelihood_repeatability': 1.0}
2025-06-16 14:18:51,287 - test_comprehensive_integration - INFO - Final resources: {'cpu_percent': 9.5, 'memory_percent': 46.8, 'memory_used_gb': 3.2631263732910156, 'gpu_memory_mb': 0}
2025-06-16 14:18:52,288 - test_comprehensive_integration - INFO - Integration tests starting on cpu
2025-06-16 14:18:52,288 - test_comprehensive_integration - INFO - Initial resources: {'cpu_percent': 8.0, 'memory_percent': 46.8, 'memory_used_gb': 3.2631149291992188, 'gpu_memory_mb': 0}
2025-06-16 14:18:52,289 - test_comprehensive_integration - INFO - Testing stress conditions and scalability...
2025-06-16 14:19:05,661 - test_comprehensive_integration - INFO - Stress testing completed in 12.37s
2025-06-16 14:19:05,662 - test_comprehensive_integration - INFO - Performance metrics: {'avg_batch_processing_time': 0.004813528060913086, 'concurrent_processing_time': 0.0064775943756103516, 'memory_increase_per_100_samples': 0.0007171630859375, 'reasoning_chain_time': 0.005485057830810547}
2025-06-16 14:19:06,733 - test_comprehensive_integration - INFO - Final resources: {'cpu_percent': 14.1, 'memory_percent': 46.7, 'memory_used_gb': 3.25531005859375, 'gpu_memory_mb': 0}
2025-06-16 14:19:07,734 - test_comprehensive_integration - INFO - Integration tests starting on cpu
2025-06-16 14:19:07,734 - test_comprehensive_integration - INFO - Initial resources: {'cpu_percent': 6.5, 'memory_percent': 46.7, 'memory_used_gb': 3.255767822265625, 'gpu_memory_mb': 0}
2025-06-16 14:19:07,734 - test_comprehensive_integration - INFO - Testing production-grade integration scenarios...
2025-06-16 14:19:08,775 - test_comprehensive_integration - INFO - Production integration completed in 0.04s
2025-06-16 14:19:08,775 - test_comprehensive_integration - INFO - Production metrics: {'avg_processing_time': 0.0012746930122375488, 'avg_reasoning_accuracy': 1.407914167956302, 'real_time_compliance': 1.0, 'batch_processing_time': 0.012164592742919922, 'avg_quality_score': 0.8, 'error_recovery_rate': 1.0, 'production_readiness_score': 1.0823742503868907}
2025-06-16 14:19:09,856 - test_comprehensive_integration - INFO - Final resources: {'cpu_percent': 4.0, 'memory_percent': 47.1, 'memory_used_gb': 3.2864341735839844, 'gpu_memory_mb': 0}
2025-06-16 14:19:10,857 - test_comprehensive_integration - INFO - Integration tests starting on cpu
2025-06-16 14:19:10,857 - test_comprehensive_integration - INFO - Initial resources: {'cpu_percent': 3.5, 'memory_percent': 47.0, 'memory_used_gb': 3.2762985229492188, 'gpu_memory_mb': 0}
2025-06-16 14:19:10,858 - test_comprehensive_integration - INFO - Generating comprehensive test report...
2025-06-16 14:19:10,859 - test_comprehensive_integration - INFO - ================================================================================
2025-06-16 14:19:10,859 - test_comprehensive_integration - INFO - DIFFUSION REASONING INTEGRATION TEST REPORT
2025-06-16 14:19:10,859 - test_comprehensive_integration - INFO - ================================================================================
2025-06-16 14:19:10,860 - test_comprehensive_integration - INFO - Total Tests: 6
2025-06-16 14:19:10,860 - test_comprehensive_integration - INFO - Passed: 6
2025-06-16 14:19:10,860 - test_comprehensive_integration - INFO - Failed: 0
2025-06-16 14:19:10,860 - test_comprehensive_integration - INFO - Success Rate: 100.00%
2025-06-16 14:19:10,860 - test_comprehensive_integration - INFO - Total Execution Time: 12.53s
2025-06-16 14:19:10,860 - test_comprehensive_integration - INFO - Average Execution Time: 2.09s
2025-06-16 14:19:10,860 - test_comprehensive_integration - INFO - Max Memory Usage: 3.36GB
2025-06-16 14:19:10,860 - test_comprehensive_integration - INFO - ================================================================================
2025-06-16 14:19:11,940 - test_comprehensive_integration - INFO - Final resources: {'cpu_percent': 15.2, 'memory_percent': 47.0, 'memory_used_gb': 3.2731552124023438, 'gpu_memory_mb': 0}
2025-06-16 14:19:11,942 - test_comprehensive_integration - INFO - ================================================================================
2025-06-16 14:19:11,942 - test_comprehensive_integration - INFO - COMPREHENSIVE INTEGRATION TEST SUMMARY
2025-06-16 14:19:11,942 - test_comprehensive_integration - INFO - ================================================================================
2025-06-16 14:19:11,942 - test_comprehensive_integration - INFO - Tests run: 7
2025-06-16 14:19:11,942 - test_comprehensive_integration - INFO - Failures: 0
2025-06-16 14:19:11,942 - test_comprehensive_integration - INFO - Errors: 0
2025-06-16 14:19:11,942 - test_comprehensive_integration - INFO - Success rate: 100.00%
2025-06-16 14:19:11,943 - __main__ - INFO - Suite comprehensive_integration completed in 33.83s - PASSED
2025-06-16 14:19:12,006 - __main__ - INFO - Generating final test report...
2025-06-16 14:19:12,008 - __main__ - INFO - Detailed report saved to test_results/final_test_report.json
2025-06-16 14:19:12,009 - __main__ - INFO - ================================================================================
2025-06-16 14:19:12,009 - __main__ - INFO - ULTRA DIFFUSION REASONING MODULE - TEST EXECUTION COMPLETE
2025-06-16 14:19:12,009 - __main__ - INFO - ================================================================================
2025-06-16 14:19:12,009 - __main__ - INFO - Execution Summary:
2025-06-16 14:19:12,009 - __main__ - INFO -   Total Test Suites: 6
2025-06-16 14:19:12,009 - __main__ - INFO -   Passed Suites: 5
2025-06-16 14:19:12,009 - __main__ - INFO -   Failed Suites: 1
2025-06-16 14:19:12,009 - __main__ - INFO -   Success Rate: 83.3%
2025-06-16 14:19:12,009 - __main__ - INFO -   Total Execution Time: 1m 4s
2025-06-16 14:19:12,009 - __main__ - INFO - 
Suite Details:
2025-06-16 14:19:12,009 - __main__ - INFO -   conceptual_diffusion: PASSED (5.4s)
2025-06-16 14:19:12,009 - __main__ - INFO -   thought_latent_space: PASSED (25.9s)
2025-06-16 14:19:12,009 - __main__ - INFO -   bayesian_uncertainty: FAILED (0.0s)
2025-06-16 14:19:12,009 - __main__ - INFO -   reverse_diffusion_reasoning: PASSED (3.8s)
2025-06-16 14:19:12,010 - __main__ - INFO -   probabilistic_inference: PASSED (4.4s)
2025-06-16 14:19:12,010 - __main__ - INFO -   comprehensive_integration: PASSED (33.8s)
2025-06-16 14:19:12,010 - __main__ - WARNING - 
Failed Suites:
2025-06-16 14:19:12,010 - __main__ - WARNING -   bayesian_uncertainty: 2 errors
2025-06-16 14:19:12,010 - __main__ - WARNING -     - No module named 'test_bayesian_uncertainty'...
2025-06-16 14:19:12,010 - __main__ - WARNING -     - Traceback (most recent call last):
  File "/workspaces/Ultra/ultra/ultra/diffusion_reasoning/run_all...
2025-06-16 14:19:12,010 - __main__ - INFO - ================================================================================
2025-06-16 14:19:12,010 - __main__ - WARNING - Most tests passed, but some failures detected
2025-06-17 08:28:44,699 - __main__ - INFO - Setting up test environment...
2025-06-17 08:28:44,702 - __main__ - INFO - System info: {'cpu_count': 2, 'cpu_percent': 50.1, 'memory_total_gb': 7.751640319824219, 'memory_available_gb': 3.8885574340820312, 'memory_percent': 49.8, 'gpu_available': False, 'gpu_count': 0, 'gpu_memory_gb': 0}
2025-06-17 08:28:44,706 - __main__ - INFO - Using device: cpu
2025-06-17 08:28:44,709 - __main__ - INFO - Test environment setup complete
2025-06-17 08:28:44,709 - __main__ - INFO - Initialized test orchestrator with 6 test suites
2025-06-17 08:28:44,710 - __main__ - INFO - ================================================================================
2025-06-17 08:28:44,710 - __main__ - INFO - STARTING ULTRA DIFFUSION REASONING MODULE TESTS
2025-06-17 08:28:44,710 - __main__ - INFO - ================================================================================
2025-06-17 08:28:44,710 - __main__ - INFO - Estimated total execution time: 43 minutes 20 seconds
2025-06-17 08:28:44,710 - __main__ - INFO - Running tests in parallel with 4 workers
2025-06-17 08:28:44,710 - __main__ - INFO - Executing priority 1 tests (3 suites)
2025-06-17 08:28:44,744 - __main__ - INFO - Executing test suite: conceptual_diffusion
2025-06-17 08:28:44,745 - __main__ - INFO - Executing test suite: thought_latent_space
2025-06-17 08:29:00,034 - __main__ - INFO - Successfully imported test_thought_latent_space via importlib
2025-06-17 08:29:00,839 - test_thought_latent_space - INFO - Testing compositional operations...
2025-06-17 08:29:02,347 - test_thought_latent_space - INFO - Compositional operations tests passed
2025-06-17 08:29:02,406 - test_thought_latent_space - INFO - Testing encoding-decoding consistency...
2025-06-17 08:29:02,520 - test_thought_latent_space - INFO - Reconstruction error: 0.194323
2025-06-17 08:29:02,574 - test_thought_latent_space - INFO - Testing hierarchical structure preservation...
2025-06-17 08:29:02,578 - __main__ - INFO - Successfully imported test_conceptual_diffusion via importlib
2025-06-17 08:29:02,676 - test_thought_latent_space - INFO - Training hierarchical structure classifier...
2025-06-17 08:29:03,360 - test_conceptual_diffusion - INFO - Testing conceptual space properties...
2025-06-17 08:29:03,378 - test_conceptual_diffusion - INFO - Within-level similarity: 0.166
2025-06-17 08:29:03,378 - test_conceptual_diffusion - INFO - Across-level similarity: 0.168
2025-06-17 08:29:03,379 - test_conceptual_diffusion - INFO - Semantic similarity preservation: 0.992
2025-06-17 08:29:03,410 - test_conceptual_diffusion - INFO - Testing forward diffusion process...
2025-06-17 08:29:03,456 - test_conceptual_diffusion - INFO - Forward diffusion process tests passed
2025-06-17 08:29:03,489 - test_conceptual_diffusion - INFO - Testing loss computation...
2025-06-17 08:29:03,816 - test_conceptual_diffusion - INFO - Standard loss: 1.285598
2025-06-17 08:29:03,818 - test_conceptual_diffusion - INFO - VLB loss: 99.944221
2025-06-17 08:29:03,860 - test_conceptual_diffusion - INFO - Testing mathematical consistency...
2025-06-17 08:29:03,903 - test_conceptual_diffusion - INFO - Reconstruction error: 0.694834
2025-06-17 08:29:03,935 - test_conceptual_diffusion - INFO - Testing noise schedule properties...
2025-06-17 08:29:03,951 - test_conceptual_diffusion - INFO - Beta range: [0.000100, 0.050000]
2025-06-17 08:29:03,953 - test_conceptual_diffusion - INFO - Alpha cumprod range: [0.999900, 0.078234]
2025-06-17 08:29:03,980 - test_conceptual_diffusion - INFO - Testing performance benchmarks...
2025-06-17 08:29:04,397 - test_conceptual_diffusion - INFO - Forward diffusion time: 0.08ms
2025-06-17 08:29:04,397 - test_conceptual_diffusion - INFO - Reverse diffusion time: 1.92ms
2025-06-17 08:29:04,398 - test_conceptual_diffusion - INFO - Loss computation time: 2.17ms
2025-06-17 08:29:04,440 - test_conceptual_diffusion - INFO - Testing reverse diffusion step...
2025-06-17 08:29:04,447 - test_conceptual_diffusion - INFO - Noise reduction ratio: 1.000
2025-06-17 08:29:04,491 - test_conceptual_diffusion - INFO - Testing sampling process...
2025-06-17 08:29:04,795 - test_conceptual_diffusion - INFO - Initial noise level: 7.852
2025-06-17 08:29:04,796 - test_conceptual_diffusion - INFO - Final noise level: 1.982
2025-06-17 08:29:04,796 - test_conceptual_diffusion - INFO - Noise reduction: 0.748
2025-06-17 08:29:04,838 - test_conceptual_diffusion - INFO - Testing score network properties...
2025-06-17 08:29:04,949 - test_conceptual_diffusion - INFO - Score network output range: [0.000, 0.000]
2025-06-17 08:29:05,010 - test_conceptual_diffusion - INFO - Testing memory efficiency...
2025-06-17 08:29:05,010 - test_conceptual_diffusion - INFO - CUDA not available, skipping memory test
2025-06-17 08:29:05,058 - test_conceptual_diffusion - INFO - Testing training integration...
2025-06-17 08:29:15,791 - test_conceptual_diffusion - INFO - Initial loss: 1.202766
2025-06-17 08:29:15,791 - test_conceptual_diffusion - INFO - Final loss: 0.988448
2025-06-17 08:29:15,791 - test_conceptual_diffusion - INFO - Relative change: 0.178
2025-06-17 08:29:15,792 - __main__ - INFO - Suite conceptual_diffusion executed successfully via direct import
2025-06-17 08:29:15,793 - __main__ - INFO - Suite conceptual_diffusion completed in 31.05s - PASSED
2025-06-17 08:29:15,943 - test_thought_latent_space - INFO - Epoch 0: Loss=2.1313, Accuracy=0.828
2025-06-17 08:29:16,360 - test_thought_latent_space - INFO - Epoch 1: Loss=1.4977, Accuracy=1.000
2025-06-17 08:29:16,875 - test_thought_latent_space - INFO - Epoch 2: Loss=1.1226, Accuracy=1.000
2025-06-17 08:29:17,357 - test_thought_latent_space - INFO - Epoch 3: Loss=0.9562, Accuracy=1.000
2025-06-17 08:29:17,701 - test_thought_latent_space - INFO - Epoch 4: Loss=0.8453, Accuracy=1.000
2025-06-17 08:29:18,098 - test_thought_latent_space - INFO - Epoch 5: Loss=0.7866, Accuracy=1.000
2025-06-17 08:29:18,482 - test_thought_latent_space - INFO - Epoch 6: Loss=0.7079, Accuracy=1.000
2025-06-17 08:29:18,861 - test_thought_latent_space - INFO - Epoch 7: Loss=0.6814, Accuracy=1.000
2025-06-17 08:29:19,371 - test_thought_latent_space - INFO - Epoch 8: Loss=0.6518, Accuracy=1.000
2025-06-17 08:29:19,913 - test_thought_latent_space - INFO - Epoch 9: Loss=0.6280, Accuracy=1.000
2025-06-17 08:29:24,564 - test_thought_latent_space - INFO - Epoch 20: Loss=0.5713, Accuracy=1.000
2025-06-17 08:29:29,030 - test_thought_latent_space - INFO - Achieved high accuracy 1.000 at epoch 31
2025-06-17 08:29:29,033 - test_thought_latent_space - INFO - Testing trained hierarchical structure...
2025-06-17 08:29:29,462 - test_thought_latent_space - INFO - Final test accuracy: 1.000
2025-06-17 08:29:29,463 - test_thought_latent_space - INFO - Training achieved best accuracy: 1.000
2025-06-17 08:29:29,463 - test_thought_latent_space - INFO - Hierarchical coherence: 0.951
2025-06-17 08:29:29,463 - test_thought_latent_space - INFO - Level prediction accuracy: 1.000
2025-06-17 08:29:29,513 - test_thought_latent_space - INFO - Testing interpolation properties...
2025-06-17 08:29:29,539 - test_thought_latent_space - INFO - Interpolation tests passed
2025-06-17 08:29:29,563 - test_thought_latent_space - INFO - Testing loss computation...
2025-06-17 08:29:29,621 - test_thought_latent_space - INFO - Total loss: 1.151511
2025-06-17 08:29:29,621 - test_thought_latent_space - INFO - Reconstruction loss: 0.197515
2025-06-17 08:29:29,621 - test_thought_latent_space - INFO - Hierarchical loss: 1.126987
2025-06-17 08:29:29,655 - test_thought_latent_space - INFO - Testing manifold structure analysis...
2025-06-17 08:29:30,626 - test_thought_latent_space - INFO - Intrinsic dimensionality: 36
2025-06-17 08:29:30,628 - test_thought_latent_space - INFO - Explained variance ratio: 0.952
2025-06-17 08:29:30,628 - test_thought_latent_space - INFO - Local smoothness: 9.105
2025-06-17 08:29:30,629 - test_thought_latent_space - INFO - Condition number: 999.000
2025-06-17 08:29:30,674 - test_thought_latent_space - INFO - Testing memory operations...
2025-06-17 08:29:30,762 - test_thought_latent_space - INFO - Memory operations tests passed
2025-06-17 08:29:30,793 - test_thought_latent_space - INFO - Testing performance benchmarks...
2025-06-17 08:29:45,073 - test_thought_latent_space - INFO - Single encoding time: 3.43ms
2025-06-17 08:29:45,073 - test_thought_latent_space - INFO - Batch processing time: 60.43ms
2025-06-17 08:29:45,100 - test_thought_latent_space - INFO - Testing relational vector computation...
2025-06-17 08:29:45,135 - test_thought_latent_space - INFO - Relational vector tests passed
2025-06-17 08:29:45,165 - test_thought_latent_space - INFO - Testing semantic similarity computation...
2025-06-17 08:29:45,313 - test_thought_latent_space - INFO - Same cluster similarity: 0.466
2025-06-17 08:29:45,313 - test_thought_latent_space - INFO - Different cluster similarity: 0.482
2025-06-17 08:29:45,341 - test_thought_latent_space - INFO - Testing integration with diffusion process...
2025-06-17 08:29:45,419 - test_thought_latent_space - INFO - Noise level 0.1: degradation 0.1787
2025-06-17 08:29:45,427 - test_thought_latent_space - INFO - Noise level 0.3: degradation 0.1872
2025-06-17 08:29:45,432 - test_thought_latent_space - INFO - Noise level 0.5: degradation 0.1914
2025-06-17 08:29:45,439 - test_thought_latent_space - INFO - Noise level 0.8: degradation 0.1838
2025-06-17 08:29:45,440 - test_thought_latent_space - INFO - Diffusion integration test passed
2025-06-17 08:29:45,465 - test_thought_latent_space - INFO - Testing training integration...
2025-06-17 08:29:46,544 - test_thought_latent_space - INFO - Initial loss: 1.019191
2025-06-17 08:29:46,545 - test_thought_latent_space - INFO - Final loss: 0.656549
2025-06-17 08:29:46,545 - test_thought_latent_space - INFO - Loss ratio: 0.644
2025-06-17 08:29:46,549 - __main__ - INFO - Suite thought_latent_space executed successfully via direct import
2025-06-17 08:29:46,553 - __main__ - INFO - Suite thought_latent_space completed in 61.80s - PASSED
2025-06-17 08:29:46,617 - __main__ - INFO - Executing test suite: bayesian_uncertainty
2025-06-17 08:29:46,619 - __main__ - ERROR - Direct import execution failed: Could not import test_bayesian_uncertainty from any path
2025-06-17 08:29:46,619 - __main__ - WARNING - Direct import failed for bayesian_uncertainty: Could not import test_bayesian_uncertainty from any path
2025-06-17 08:29:47,029 - __main__ - INFO - Suite bayesian_uncertainty completed in 0.41s - FAILED
2025-06-17 08:29:47,113 - __main__ - INFO - Executing priority 2 tests (2 suites)
2025-06-17 08:29:47,142 - __main__ - INFO - Executing test suite: reverse_diffusion_reasoning
2025-06-17 08:29:47,143 - __main__ - INFO - Executing test suite: probabilistic_inference
2025-06-17 08:29:48,866 - __main__ - INFO - Successfully imported test_probabilistic_inference_engine via importlib
2025-06-17 08:29:49,298 - __main__ - INFO - Successfully imported test_reverse_diffusion_reasoning via importlib
2025-06-17 08:29:49,309 - test_reverse_diffusion_reasoning - INFO - Testing analogical reasoning...
2025-06-17 08:29:49,325 - test_reverse_diffusion_reasoning - INFO - Analogical relation similarity: 0.999
2025-06-17 08:29:49,343 - test_reverse_diffusion_reasoning - INFO - Testing constraint satisfaction scoring...
2025-06-17 08:29:49,345 - test_reverse_diffusion_reasoning - INFO - Constraint satisfaction score: 0.000
2025-06-17 08:29:49,346 - test_reverse_diffusion_reasoning - INFO - Perfect constraint score: 1.000
2025-06-17 08:29:49,355 - test_reverse_diffusion_reasoning - INFO - Testing guidance function properties...
2025-06-17 08:29:49,363 - test_reverse_diffusion_reasoning - INFO - Guidance function tests passed
2025-06-17 08:29:49,372 - test_reverse_diffusion_reasoning - INFO - Testing guided reverse step...
2025-06-17 08:29:49,378 - test_reverse_diffusion_reasoning - INFO - Samples improved: 8/8
2025-06-17 08:29:49,379 - test_reverse_diffusion_reasoning - INFO - Average distance change: 8.533
2025-06-17 08:29:49,388 - test_reverse_diffusion_reasoning - INFO - Testing interpolative reasoning...
2025-06-17 08:29:50,231 - test_reverse_diffusion_reasoning - INFO - Similarity trend to A: -0.205
2025-06-17 08:29:50,232 - test_reverse_diffusion_reasoning - INFO - Similarity trend to B: 0.191
2025-06-17 08:29:50,243 - test_reverse_diffusion_reasoning - INFO - Testing mathematical properties...
2025-06-17 08:29:50,245 - test_reverse_diffusion_reasoning - INFO - Guided computation difference: 7.5133
2025-06-17 08:29:50,255 - test_reverse_diffusion_reasoning - INFO - Testing multi-constraint reasoning...
2025-06-17 08:29:50,291 - test_reverse_diffusion_reasoning - INFO - Constraints improved: 0/2
2025-06-17 08:29:50,291 - test_reverse_diffusion_reasoning - INFO - Overall satisfaction - Initial: -50.652, Final: -56.237
2025-06-17 08:29:50,291 - test_reverse_diffusion_reasoning - INFO - Process completed successfully with trajectory variance: 3.271734
2025-06-17 08:29:50,309 - test_reverse_diffusion_reasoning - INFO - Testing performance benchmarks...
2025-06-17 08:29:50,476 - test_reverse_diffusion_reasoning - INFO - Single step time: 0.67ms
2025-06-17 08:29:50,476 - test_reverse_diffusion_reasoning - INFO - Full reasoning time: 9.84ms
2025-06-17 08:29:50,487 - test_reverse_diffusion_reasoning - INFO - Testing goal-directed reasoning...
2025-06-17 08:29:50,507 - test_reverse_diffusion_reasoning - INFO - Convergence ratio: 5.098
2025-06-17 08:29:50,508 - test_reverse_diffusion_reasoning - INFO - Final confidence: 0.000
2025-06-17 08:29:50,508 - test_reverse_diffusion_reasoning - INFO - Initial confidence: 0.000
2025-06-17 08:29:50,508 - test_reverse_diffusion_reasoning - INFO - Path variance: 26.239626
2025-06-17 08:29:50,508 - test_reverse_diffusion_reasoning - INFO - Confidence ratio: 0.000
2025-06-17 08:29:50,514 - test_reverse_diffusion_reasoning - INFO - Testing reasoning trajectory analysis...
2025-06-17 08:29:50,520 - test_reverse_diffusion_reasoning - INFO - Convergence rate: 0.832
2025-06-17 08:29:50,520 - test_reverse_diffusion_reasoning - INFO - Monotonicity: 1.000
2025-06-17 08:29:50,521 - test_reverse_diffusion_reasoning - INFO - Final distance: 1.724
2025-06-17 08:29:50,528 - test_reverse_diffusion_reasoning - INFO - Testing end-to-end reasoning...
2025-06-17 08:29:50,758 - test_reverse_diffusion_reasoning - INFO - End-to-end reasoning test passed
2025-06-17 08:29:50,776 - test_reverse_diffusion_reasoning - INFO - Testing training integration...
2025-06-17 08:29:51,062 - test_probabilistic_inference_engine - INFO - Testing Bayesian model selection...
2025-06-17 08:29:51,137 - test_probabilistic_inference_engine - INFO - Model probabilities: {'model_0': 0.0, 'model_1': 0.0, 'model_2': 1.0}
2025-06-17 08:29:51,147 - test_probabilistic_inference_engine - INFO - Testing information-theoretic measures...
2025-06-17 08:29:53,028 - test_probabilistic_inference_engine - INFO - Entropy: 4.605
2025-06-17 08:29:53,029 - test_probabilistic_inference_engine - INFO - Mutual information: 2.303
2025-06-17 08:29:53,030 - test_probabilistic_inference_engine - INFO - Effective dimensionality: 52.973
2025-06-17 08:29:53,040 - test_probabilistic_inference_engine - INFO - Testing likelihood computation...
2025-06-17 08:29:53,045 - test_probabilistic_inference_engine - INFO - Log likelihood range: [-0.266, 0.177]
2025-06-17 08:29:53,052 - test_probabilistic_inference_engine - INFO - Testing marginal likelihood computation...
2025-06-17 08:29:53,101 - test_probabilistic_inference_engine - INFO - Log evidence (importance_sampling): 0.025
2025-06-17 08:29:53,178 - test_probabilistic_inference_engine - INFO - Log evidence (variational): -3.261
2025-06-17 08:29:53,199 - test_probabilistic_inference_engine - INFO - Log evidence (network): 0.035
2025-06-17 08:29:53,224 - test_probabilistic_inference_engine - INFO - Testing mathematical properties...
2025-06-17 08:29:53,246 - test_probabilistic_inference_engine - INFO - KL divergence: 41.538849
2025-06-17 08:29:53,248 - test_probabilistic_inference_engine - INFO - Mathematical properties test passed
2025-06-17 08:29:53,259 - test_probabilistic_inference_engine - INFO - Testing MCMC sampling...
2025-06-17 08:29:54,862 - test_reverse_diffusion_reasoning - INFO - Training integration test passed
2025-06-17 08:29:54,865 - __main__ - INFO - Suite reverse_diffusion_reasoning executed successfully via direct import
2025-06-17 08:29:54,874 - __main__ - INFO - Suite reverse_diffusion_reasoning completed in 7.73s - PASSED
2025-06-17 08:29:56,095 - test_probabilistic_inference_engine - INFO - MCMC Diagnostics:
2025-06-17 08:29:56,095 - test_probabilistic_inference_engine - INFO -   Acceptance rate: 0.254
2025-06-17 08:29:56,095 - test_probabilistic_inference_engine - INFO -   Mean error: 0.1217 (tolerance: 0.5537) ✓
2025-06-17 08:29:56,095 - test_probabilistic_inference_engine - INFO -   Cov error: 0.0888 (tolerance: 1.1073) ✓
2025-06-17 08:29:56,095 - test_probabilistic_inference_engine - INFO -   Var error: 0.0304 (tolerance: 0.7830) ✓
2025-06-17 08:29:56,095 - test_probabilistic_inference_engine - INFO -   Effective sample size: 104 (autocorr time: 23.4)
2025-06-17 08:29:56,096 - test_probabilistic_inference_engine - INFO -   Efficiency: 2.1% (effective/total)
2025-06-17 08:29:56,096 - test_probabilistic_inference_engine - INFO -   Test dimension: 8
2025-06-17 08:29:56,096 - test_probabilistic_inference_engine - INFO -   Samples: 5000, Warmup: 2000
2025-06-17 08:29:56,096 - test_probabilistic_inference_engine - INFO -   Mixing ratio: 0.241
2025-06-17 08:29:56,096 - test_probabilistic_inference_engine - INFO - MCMC sampling test passed with proper convergence validation
2025-06-17 08:29:56,104 - test_probabilistic_inference_engine - INFO - Testing normalizing flow...
2025-06-17 08:29:56,124 - test_probabilistic_inference_engine - INFO - Normalizing flow test passed
2025-06-17 08:29:56,131 - test_probabilistic_inference_engine - INFO - Testing performance benchmarks...
2025-06-17 08:29:56,172 - test_probabilistic_inference_engine - INFO - Likelihood computation time: 0.29ms
2025-06-17 08:29:56,172 - test_probabilistic_inference_engine - INFO - Posterior approximation time: 1.08ms
2025-06-17 08:29:56,179 - test_probabilistic_inference_engine - INFO - Testing predictive distribution...
2025-06-17 08:29:56,186 - test_probabilistic_inference_engine - INFO - Predictive mean range: [-0.786, 4.884]
2025-06-17 08:29:56,186 - test_probabilistic_inference_engine - INFO - Predictive std range: [6.929, 9.129]
2025-06-17 08:29:56,191 - test_probabilistic_inference_engine - INFO - Testing prior computation...
2025-06-17 08:29:56,193 - test_probabilistic_inference_engine - INFO - Prior mean norm: 0.000
2025-06-17 08:29:56,194 - test_probabilistic_inference_engine - INFO - Prior covariance trace: 64.000
2025-06-17 08:29:56,198 - test_probabilistic_inference_engine - INFO - Testing uncertainty quantification...
2025-06-17 08:29:56,204 - test_probabilistic_inference_engine - INFO - Epistemic uncertainty mean: 72.8462
2025-06-17 08:29:56,205 - test_probabilistic_inference_engine - INFO - Aleatoric uncertainty mean: 0.1000
2025-06-17 08:29:56,206 - test_probabilistic_inference_engine - INFO - Total uncertainty mean: 72.9462
2025-06-17 08:29:56,210 - test_probabilistic_inference_engine - INFO - Testing variational posterior approximation...
2025-06-17 08:29:56,211 - test_probabilistic_inference_engine - INFO - Posterior-prior mean difference: 1.555
2025-06-17 08:29:56,213 - test_probabilistic_inference_engine - INFO - Variational posterior approximation test passed
2025-06-17 08:29:56,218 - test_probabilistic_inference_engine - INFO - Testing end-to-end Bayesian inference...
2025-06-17 08:29:56,264 - test_probabilistic_inference_engine - INFO - Posterior error: 5.959
2025-06-17 08:29:56,264 - test_probabilistic_inference_engine - INFO - Prior error: 5.725
2025-06-17 08:29:56,264 - test_probabilistic_inference_engine - INFO - Evidence: -0.506
2025-06-17 08:29:56,264 - test_probabilistic_inference_engine - INFO - Posterior did not improve over prior (may be due to limited data)
2025-06-17 08:29:56,271 - test_probabilistic_inference_engine - INFO - Testing model comparison workflow...
2025-06-17 08:29:56,287 - test_probabilistic_inference_engine - INFO - True noise level: 0.2
2025-06-17 08:29:56,288 - test_probabilistic_inference_engine - INFO - Selected noise level: 0.5
2025-06-17 08:29:56,288 - test_probabilistic_inference_engine - INFO - Model probabilities: {'model_0': 0.0, 'model_1': 0.0, 'model_2': 1.0}
2025-06-17 08:29:56,289 - __main__ - INFO - Suite probabilistic_inference executed successfully via direct import
2025-06-17 08:29:56,289 - __main__ - INFO - Suite probabilistic_inference completed in 9.15s - PASSED
2025-06-17 08:29:56,386 - __main__ - INFO - Executing priority 3 tests (1 suites)
2025-06-17 08:29:56,400 - __main__ - INFO - Executing test suite: comprehensive_integration
2025-06-17 08:29:56,713 - __main__ - INFO - Successfully imported test_comprehensive_integration via importlib
2025-06-17 08:29:56,914 - __main__ - INFO - Suite comprehensive_integration executed successfully via direct import
2025-06-17 08:29:56,914 - __main__ - INFO - Suite comprehensive_integration completed in 0.51s - PASSED
2025-06-17 08:29:57,012 - __main__ - INFO - Generating final test report...
2025-06-17 08:29:57,015 - __main__ - INFO - Detailed report saved to test_results/final_test_report.json
2025-06-17 08:29:57,015 - __main__ - INFO - ================================================================================
2025-06-17 08:29:57,015 - __main__ - INFO - ULTRA DIFFUSION REASONING MODULE - TEST EXECUTION COMPLETE
2025-06-17 08:29:57,016 - __main__ - INFO - ================================================================================
2025-06-17 08:29:57,016 - __main__ - INFO - Execution Summary:
2025-06-17 08:29:57,016 - __main__ - INFO -   Total Test Suites: 6
2025-06-17 08:29:57,016 - __main__ - INFO -   Passed Suites: 5
2025-06-17 08:29:57,016 - __main__ - INFO -   Failed Suites: 1
2025-06-17 08:29:57,016 - __main__ - INFO -   Success Rate: 83.3%
2025-06-17 08:29:57,016 - __main__ - INFO -   Total Execution Time: 1m 12s
2025-06-17 08:29:57,016 - __main__ - INFO - 
Suite Details:
2025-06-17 08:29:57,016 - __main__ - INFO -   conceptual_diffusion: PASSED (31.0s)
2025-06-17 08:29:57,016 - __main__ - INFO -   thought_latent_space: PASSED (61.8s)
2025-06-17 08:29:57,018 - __main__ - INFO -   bayesian_uncertainty: FAILED (0.4s)
2025-06-17 08:29:57,018 - __main__ - INFO -   reverse_diffusion_reasoning: PASSED (7.7s)
2025-06-17 08:29:57,018 - __main__ - INFO -   probabilistic_inference: PASSED (9.1s)
2025-06-17 08:29:57,018 - __main__ - INFO -   comprehensive_integration: PASSED (0.5s)
2025-06-17 08:29:57,019 - __main__ - WARNING - 
Failed Suites:
2025-06-17 08:29:57,019 - __main__ - WARNING -   bayesian_uncertainty: 3 errors
2025-06-17 08:29:57,019 - __main__ - WARNING -     - Direct import failed: Could not import test_bayesian_uncertainty from any path...
2025-06-17 08:29:57,019 - __main__ - WARNING -     - Subprocess execution returned False...
2025-06-17 08:29:57,019 - __main__ - WARNING -     - Unittest discovery returned False...
2025-06-17 08:29:57,019 - __main__ - INFO - ================================================================================
2025-06-17 08:29:57,019 - __main__ - INFO - SUGGESTED FIXES:
2025-06-17 08:29:57,019 - __main__ - INFO -   For bayesian_uncertainty:
2025-06-17 08:29:57,019 - __main__ - INFO -     - Check if test file exists: bayesian_uncertainty.py
2025-06-17 08:29:57,019 - __main__ - INFO -     - Verify module is in Python path
2025-06-17 08:29:57,019 - __main__ - INFO -     - Try running: python -m bayesian_uncertainty
2025-06-17 08:29:57,020 - __main__ - WARNING - Most tests passed, but some failures detected
2025-06-17 09:21:12,202 - __main__ - INFO - Setting up test environment...
2025-06-17 09:21:12,204 - __main__ - INFO - System info: {'cpu_count': 2, 'cpu_percent': 62.2, 'memory_total_gb': 7.751644134521484, 'memory_available_gb': 2.8094940185546875, 'memory_percent': 63.8, 'gpu_available': False, 'gpu_count': 0, 'gpu_memory_gb': 0}
2025-06-17 09:21:12,204 - __main__ - INFO - Using device: cpu
2025-06-17 09:21:12,205 - __main__ - INFO - Test environment setup complete
2025-06-17 09:21:12,205 - __main__ - INFO - Initialized test orchestrator with 6 test suites
2025-06-17 09:21:12,205 - __main__ - INFO - ================================================================================
2025-06-17 09:21:12,205 - __main__ - INFO - STARTING ULTRA DIFFUSION REASONING MODULE TESTS
2025-06-17 09:21:12,205 - __main__ - INFO - ================================================================================
2025-06-17 09:21:12,205 - __main__ - INFO - Estimated total execution time: 43 minutes 20 seconds
2025-06-17 09:21:12,205 - __main__ - INFO - Running tests in parallel with 4 workers
2025-06-17 09:21:12,205 - __main__ - INFO - Executing priority 1 tests (3 suites)
2025-06-17 09:21:12,231 - __main__ - INFO - Executing test suite: conceptual_diffusion
2025-06-17 09:21:12,231 - __main__ - INFO - Executing test suite: thought_latent_space
2025-06-17 09:21:14,859 - __main__ - INFO - Successfully imported test_thought_latent_space via importlib
2025-06-17 09:21:14,912 - test_thought_latent_space - INFO - Testing compositional operations...
2025-06-17 09:21:15,166 - test_thought_latent_space - INFO - Compositional operations tests passed
2025-06-17 09:21:15,202 - test_thought_latent_space - INFO - Testing encoding-decoding consistency...
2025-06-17 09:21:15,277 - test_thought_latent_space - INFO - Reconstruction error: 0.202178
2025-06-17 09:21:15,313 - test_thought_latent_space - INFO - Testing hierarchical structure preservation...
2025-06-17 09:21:15,405 - test_thought_latent_space - INFO - Training hierarchical structure classifier...
2025-06-17 09:21:15,528 - __main__ - INFO - Successfully imported test_conceptual_diffusion via importlib
2025-06-17 09:21:15,579 - test_conceptual_diffusion - INFO - Testing conceptual space properties...
2025-06-17 09:21:15,598 - test_conceptual_diffusion - INFO - Within-level similarity: 0.183
2025-06-17 09:21:15,598 - test_conceptual_diffusion - INFO - Across-level similarity: 0.193
2025-06-17 09:21:15,598 - test_conceptual_diffusion - INFO - Semantic similarity preservation: 0.993
2025-06-17 09:21:15,627 - test_conceptual_diffusion - INFO - Testing forward diffusion process...
2025-06-17 09:21:15,632 - test_conceptual_diffusion - INFO - Forward diffusion process tests passed
2025-06-17 09:21:15,667 - test_conceptual_diffusion - INFO - Testing loss computation...
2025-06-17 09:21:15,718 - test_conceptual_diffusion - INFO - Standard loss: 1.235535
2025-06-17 09:21:15,720 - test_conceptual_diffusion - INFO - VLB loss: 102.854080
2025-06-17 09:21:15,764 - test_conceptual_diffusion - INFO - Testing mathematical consistency...
2025-06-17 09:21:15,795 - test_conceptual_diffusion - INFO - Reconstruction error: 0.816174
2025-06-17 09:21:15,821 - test_conceptual_diffusion - INFO - Testing noise schedule properties...
2025-06-17 09:21:15,822 - test_conceptual_diffusion - INFO - Beta range: [0.000100, 0.050000]
2025-06-17 09:21:15,823 - test_conceptual_diffusion - INFO - Alpha cumprod range: [0.999900, 0.078234]
2025-06-17 09:21:15,857 - test_conceptual_diffusion - INFO - Testing performance benchmarks...
2025-06-17 09:21:16,297 - test_conceptual_diffusion - INFO - Forward diffusion time: 0.10ms
2025-06-17 09:21:16,300 - test_conceptual_diffusion - INFO - Reverse diffusion time: 2.22ms
2025-06-17 09:21:16,300 - test_conceptual_diffusion - INFO - Loss computation time: 2.08ms
2025-06-17 09:21:16,341 - test_conceptual_diffusion - INFO - Testing reverse diffusion step...
2025-06-17 09:21:16,344 - test_conceptual_diffusion - INFO - Noise reduction ratio: 1.018
2025-06-17 09:21:16,372 - test_conceptual_diffusion - INFO - Testing sampling process...
2025-06-17 09:21:16,738 - test_conceptual_diffusion - INFO - Initial noise level: 7.525
2025-06-17 09:21:16,739 - test_conceptual_diffusion - INFO - Final noise level: 2.186
2025-06-17 09:21:16,740 - test_conceptual_diffusion - INFO - Noise reduction: 0.710
2025-06-17 09:21:16,798 - test_conceptual_diffusion - INFO - Testing score network properties...
2025-06-17 09:21:16,862 - test_conceptual_diffusion - INFO - Score network output range: [0.000, 0.000]
2025-06-17 09:21:16,933 - test_conceptual_diffusion - INFO - Testing memory efficiency...
2025-06-17 09:21:16,933 - test_conceptual_diffusion - INFO - CUDA not available, skipping memory test
2025-06-17 09:21:16,990 - test_conceptual_diffusion - INFO - Testing training integration...
2025-06-17 09:21:18,150 - test_thought_latent_space - INFO - Epoch 0: Loss=2.0968, Accuracy=0.625
2025-06-17 09:21:18,746 - test_thought_latent_space - INFO - Epoch 1: Loss=1.3939, Accuracy=1.000
2025-06-17 09:21:18,951 - test_conceptual_diffusion - INFO - Initial loss: 1.169667
2025-06-17 09:21:18,952 - test_conceptual_diffusion - INFO - Final loss: 1.091884
2025-06-17 09:21:18,952 - test_conceptual_diffusion - INFO - Relative change: 0.067
2025-06-17 09:21:18,954 - __main__ - INFO - Suite conceptual_diffusion executed successfully via direct import
2025-06-17 09:21:18,954 - __main__ - INFO - Suite conceptual_diffusion completed in 6.72s - PASSED
2025-06-17 09:21:19,173 - test_thought_latent_space - INFO - Epoch 2: Loss=1.2158, Accuracy=1.000
2025-06-17 09:21:19,475 - test_thought_latent_space - INFO - Epoch 3: Loss=0.9967, Accuracy=1.000
2025-06-17 09:21:19,796 - test_thought_latent_space - INFO - Epoch 4: Loss=0.8879, Accuracy=1.000
2025-06-17 09:21:20,099 - test_thought_latent_space - INFO - Epoch 5: Loss=0.7846, Accuracy=1.000
2025-06-17 09:21:20,400 - test_thought_latent_space - INFO - Epoch 6: Loss=0.7466, Accuracy=1.000
2025-06-17 09:21:20,728 - test_thought_latent_space - INFO - Epoch 7: Loss=0.7106, Accuracy=1.000
2025-06-17 09:21:21,034 - test_thought_latent_space - INFO - Epoch 8: Loss=0.6647, Accuracy=1.000
2025-06-17 09:21:21,332 - test_thought_latent_space - INFO - Epoch 9: Loss=0.6377, Accuracy=1.000
2025-06-17 09:21:24,744 - test_thought_latent_space - INFO - Epoch 20: Loss=0.5747, Accuracy=1.000
2025-06-17 09:21:28,185 - test_thought_latent_space - INFO - Achieved high accuracy 1.000 at epoch 31
2025-06-17 09:21:28,185 - test_thought_latent_space - INFO - Testing trained hierarchical structure...
2025-06-17 09:21:28,336 - test_thought_latent_space - INFO - Final test accuracy: 1.000
2025-06-17 09:21:28,337 - test_thought_latent_space - INFO - Training achieved best accuracy: 1.000
2025-06-17 09:21:28,337 - test_thought_latent_space - INFO - Hierarchical coherence: 1.023
2025-06-17 09:21:28,337 - test_thought_latent_space - INFO - Level prediction accuracy: 1.000
2025-06-17 09:21:28,372 - test_thought_latent_space - INFO - Testing interpolation properties...
2025-06-17 09:21:28,391 - test_thought_latent_space - INFO - Interpolation tests passed
2025-06-17 09:21:28,422 - test_thought_latent_space - INFO - Testing loss computation...
2025-06-17 09:21:28,463 - test_thought_latent_space - INFO - Total loss: 1.152036
2025-06-17 09:21:28,464 - test_thought_latent_space - INFO - Reconstruction loss: 0.164545
2025-06-17 09:21:28,464 - test_thought_latent_space - INFO - Hierarchical loss: 1.115558
2025-06-17 09:21:28,490 - test_thought_latent_space - INFO - Testing manifold structure analysis...
2025-06-17 09:21:28,852 - test_thought_latent_space - INFO - Intrinsic dimensionality: 37
2025-06-17 09:21:28,852 - test_thought_latent_space - INFO - Explained variance ratio: 0.953
2025-06-17 09:21:28,853 - test_thought_latent_space - INFO - Local smoothness: 9.453
2025-06-17 09:21:28,853 - test_thought_latent_space - INFO - Condition number: 999.000
2025-06-17 09:21:28,887 - test_thought_latent_space - INFO - Testing memory operations...
2025-06-17 09:21:28,913 - test_thought_latent_space - INFO - Memory operations tests passed
2025-06-17 09:21:28,938 - test_thought_latent_space - INFO - Testing performance benchmarks...
2025-06-17 09:21:38,859 - test_thought_latent_space - INFO - Single encoding time: 2.29ms
2025-06-17 09:21:38,859 - test_thought_latent_space - INFO - Batch processing time: 44.26ms
2025-06-17 09:21:38,880 - test_thought_latent_space - INFO - Testing relational vector computation...
2025-06-17 09:21:38,900 - test_thought_latent_space - INFO - Relational vector tests passed
2025-06-17 09:21:38,922 - test_thought_latent_space - INFO - Testing semantic similarity computation...
2025-06-17 09:21:39,048 - test_thought_latent_space - INFO - Same cluster similarity: 0.478
2025-06-17 09:21:39,048 - test_thought_latent_space - INFO - Different cluster similarity: 0.484
2025-06-17 09:21:39,068 - test_thought_latent_space - INFO - Testing integration with diffusion process...
2025-06-17 09:21:39,129 - test_thought_latent_space - INFO - Noise level 0.1: degradation 0.1957
2025-06-17 09:21:39,134 - test_thought_latent_space - INFO - Noise level 0.3: degradation 0.1882
2025-06-17 09:21:39,139 - test_thought_latent_space - INFO - Noise level 0.5: degradation 0.1995
2025-06-17 09:21:39,144 - test_thought_latent_space - INFO - Noise level 0.8: degradation 0.1948
2025-06-17 09:21:39,144 - test_thought_latent_space - INFO - Diffusion integration test passed
2025-06-17 09:21:39,166 - test_thought_latent_space - INFO - Testing training integration...
2025-06-17 09:21:39,858 - test_thought_latent_space - INFO - Initial loss: 0.991351
2025-06-17 09:21:39,858 - test_thought_latent_space - INFO - Final loss: 0.672033
2025-06-17 09:21:39,858 - test_thought_latent_space - INFO - Loss ratio: 0.678
2025-06-17 09:21:39,860 - __main__ - INFO - Suite thought_latent_space executed successfully via direct import
2025-06-17 09:21:39,861 - __main__ - INFO - Suite thought_latent_space completed in 27.63s - PASSED
2025-06-17 09:21:39,913 - __main__ - INFO - Executing test suite: bayesian_uncertainty
2025-06-17 09:21:40,294 - __main__ - INFO - Successfully imported test_bayesian_uncertainty via importlib
2025-06-17 09:21:40,304 - test_bayesian_uncertainty - INFO - Testing aleatoric uncertainty computation...
2025-06-17 09:21:40,309 - test_bayesian_uncertainty - INFO - Aleatoric uncertainty mean: 0.703709
2025-06-17 09:21:40,310 - test_bayesian_uncertainty - INFO - Aleatoric uncertainty std: 0.056205
2025-06-17 09:21:40,318 - test_bayesian_uncertainty - INFO - Testing Bayesian updating...
2025-06-17 09:21:40,323 - test_bayesian_uncertainty - INFO - Prior determinant: 1.000000
2025-06-17 09:21:40,323 - test_bayesian_uncertainty - INFO - Posterior determinant: 0.000000
2025-06-17 09:21:40,323 - test_bayesian_uncertainty - INFO - Uncertainty reduction: 1.000
2025-06-17 09:21:40,333 - test_bayesian_uncertainty - INFO - Testing calibration framework...
2025-06-17 09:21:41,378 - test_bayesian_uncertainty - INFO - ECE before calibration: 0.2410
2025-06-17 09:21:41,378 - test_bayesian_uncertainty - INFO - ECE after calibration: 0.1866
2025-06-17 09:21:41,378 - test_bayesian_uncertainty - INFO - Calibration framework test passed!
2025-06-17 09:21:41,386 - test_bayesian_uncertainty - INFO - Testing decision uncertainty computation...
2025-06-17 09:21:41,387 - test_bayesian_uncertainty - INFO - Decision uncertainty mean: 1.333538
2025-06-17 09:21:41,387 - test_bayesian_uncertainty - INFO - Maximum entropy: 1.609438
2025-06-17 09:21:41,394 - test_bayesian_uncertainty - INFO - Testing epistemic uncertainty computation...
2025-06-17 09:21:41,397 - test_bayesian_uncertainty - INFO - Epistemic uncertainty mean: 0.049242
2025-06-17 09:21:41,397 - test_bayesian_uncertainty - INFO - Epistemic uncertainty std: 0.026317
2025-06-17 09:21:41,404 - test_bayesian_uncertainty - INFO - Testing mathematical properties...
2025-06-17 09:21:41,406 - test_bayesian_uncertainty - INFO - Confidence mean: 0.577
2025-06-17 09:21:41,406 - test_bayesian_uncertainty - INFO - Temperature: 1.000
2025-06-17 09:21:41,414 - test_bayesian_uncertainty - INFO - Testing mutual information computation...
2025-06-17 09:21:41,424 - test_bayesian_uncertainty - INFO - Mutual information mean: 2.493496
2025-06-17 09:21:41,424 - test_bayesian_uncertainty - INFO - Mutual information std: 0.060712
2025-06-17 09:21:41,424 - test_bayesian_uncertainty - INFO - FIXED: Mutual information computation completed successfully!
2025-06-17 09:21:41,432 - test_bayesian_uncertainty - INFO - Testing performance benchmarks...
2025-06-17 09:21:41,543 - test_bayesian_uncertainty - INFO - CUDA not available, skipping GPU memory test
2025-06-17 09:21:41,544 - test_bayesian_uncertainty - INFO - Average inference time: 1.10ms
2025-06-17 09:21:41,549 - test_bayesian_uncertainty - INFO - Testing stress scenarios...
2025-06-17 09:21:41,556 - test_bayesian_uncertainty - INFO - Stress tests completed successfully
2025-06-17 09:21:41,564 - test_bayesian_uncertainty - INFO - Testing structured uncertainty estimate...
2025-06-17 09:21:41,566 - test_bayesian_uncertainty - INFO - Structured uncertainty estimate test passed!
2025-06-17 09:21:41,573 - test_bayesian_uncertainty - INFO - Testing total uncertainty decomposition...
2025-06-17 09:21:41,576 - test_bayesian_uncertainty - INFO - Total uncertainty mean: 0.743953
2025-06-17 09:21:41,577 - test_bayesian_uncertainty - INFO - Epistemic/Total ratio: 0.071
2025-06-17 09:21:41,577 - test_bayesian_uncertainty - INFO - Aleatoric/Total ratio: 0.929
2025-06-17 09:21:41,589 - test_bayesian_uncertainty - INFO - Testing uncertainty calibration...
2025-06-17 09:21:41,594 - test_bayesian_uncertainty - INFO - Uncertainty-error correlation: 0.089
2025-06-17 09:21:41,605 - test_bayesian_uncertainty - INFO - Testing uncertainty decomposition...
2025-06-17 09:21:41,606 - test_bayesian_uncertainty - INFO - Uncertainty decomposition test passed!
2025-06-17 09:21:41,618 - test_bayesian_uncertainty - INFO - Testing variational inference...
2025-06-17 09:21:41,621 - test_bayesian_uncertainty - INFO - KL divergence: 66.622719
2025-06-17 09:21:41,622 - test_bayesian_uncertainty - INFO - KL divergence (matching): 0.000000
2025-06-17 09:21:41,624 - test_bayesian_uncertainty - INFO - ================================================================================
2025-06-17 09:21:41,625 - test_bayesian_uncertainty - INFO - BAYESIAN UNCERTAINTY QUANTIFICATION TEST SUMMARY (ENHANCED)
2025-06-17 09:21:41,625 - test_bayesian_uncertainty - INFO - ================================================================================
2025-06-17 09:21:41,625 - test_bayesian_uncertainty - INFO - Tests run: 14
2025-06-17 09:21:41,626 - test_bayesian_uncertainty - INFO - Failures: 0
2025-06-17 09:21:41,626 - test_bayesian_uncertainty - INFO - Errors: 0
2025-06-17 09:21:41,627 - test_bayesian_uncertainty - INFO - Success rate: 100.00%
2025-06-17 09:21:41,627 - test_bayesian_uncertainty - INFO - 🎉 ALL TESTS PASSED! The Bayesian uncertainty quantification module is working correctly.
2025-06-17 09:21:41,627 - test_bayesian_uncertainty - INFO - ✅ Key fixes applied:
2025-06-17 09:21:41,627 - test_bayesian_uncertainty - INFO -    - Fixed tensor shape mismatch in mutual information computation
2025-06-17 09:21:41,628 - test_bayesian_uncertainty - INFO -    - Enhanced integration with ULTRA ecosystem
2025-06-17 09:21:41,628 - test_bayesian_uncertainty - INFO -    - Added uncertainty decomposition and calibration frameworks
2025-06-17 09:21:41,631 - test_bayesian_uncertainty - INFO -    - Improved numerical stability and robustness
2025-06-17 09:21:41,800 - __main__ - INFO - Suite bayesian_uncertainty completed in 1.89s - FAILED
2025-06-17 09:21:41,895 - __main__ - INFO - Executing priority 2 tests (2 suites)
2025-06-17 09:21:41,915 - __main__ - INFO - Executing test suite: reverse_diffusion_reasoning
2025-06-17 09:21:41,916 - __main__ - INFO - Executing test suite: probabilistic_inference
2025-06-17 09:21:42,688 - __main__ - INFO - Successfully imported test_probabilistic_inference_engine via importlib
2025-06-17 09:21:42,707 - test_probabilistic_inference_engine - INFO - Testing Bayesian model selection...
2025-06-17 09:21:42,734 - test_probabilistic_inference_engine - INFO - Model probabilities: {'model_0': 0.0, 'model_1': 0.0, 'model_2': 1.0}
2025-06-17 09:21:42,741 - test_probabilistic_inference_engine - INFO - Testing information-theoretic measures...
2025-06-17 09:21:42,802 - test_probabilistic_inference_engine - INFO - Entropy: 4.605
2025-06-17 09:21:42,803 - test_probabilistic_inference_engine - INFO - Mutual information: 2.303
2025-06-17 09:21:42,803 - test_probabilistic_inference_engine - INFO - Effective dimensionality: 52.592
2025-06-17 09:21:42,812 - test_probabilistic_inference_engine - INFO - Testing likelihood computation...
2025-06-17 09:21:42,818 - test_probabilistic_inference_engine - INFO - Log likelihood range: [-0.182, 0.590]
2025-06-17 09:21:42,829 - test_probabilistic_inference_engine - INFO - Testing marginal likelihood computation...
2025-06-17 09:21:42,892 - test_probabilistic_inference_engine - INFO - Log evidence (importance_sampling): 1.441
2025-06-17 09:21:42,957 - test_probabilistic_inference_engine - INFO - Log evidence (variational): -1.897
2025-06-17 09:21:42,962 - test_probabilistic_inference_engine - INFO - Log evidence (network): -0.041
2025-06-17 09:21:42,971 - __main__ - INFO - Successfully imported test_reverse_diffusion_reasoning via importlib
2025-06-17 09:21:42,975 - test_probabilistic_inference_engine - INFO - Testing mathematical properties...
2025-06-17 09:21:42,980 - test_probabilistic_inference_engine - INFO - KL divergence: 42.065525
2025-06-17 09:21:42,983 - test_probabilistic_inference_engine - INFO - Mathematical properties test passed
2025-06-17 09:21:42,992 - test_reverse_diffusion_reasoning - INFO - Testing analogical reasoning...
2025-06-17 09:21:42,994 - test_probabilistic_inference_engine - INFO - Testing MCMC sampling...
2025-06-17 09:21:42,998 - test_reverse_diffusion_reasoning - INFO - Analogical relation similarity: 0.999
2025-06-17 09:21:43,009 - test_reverse_diffusion_reasoning - INFO - Testing constraint satisfaction scoring...
2025-06-17 09:21:43,011 - test_reverse_diffusion_reasoning - INFO - Constraint satisfaction score: 0.000
2025-06-17 09:21:43,011 - test_reverse_diffusion_reasoning - INFO - Perfect constraint score: 1.000
2025-06-17 09:21:43,024 - test_reverse_diffusion_reasoning - INFO - Testing guidance function properties...
2025-06-17 09:21:43,029 - test_reverse_diffusion_reasoning - INFO - Guidance function tests passed
2025-06-17 09:21:43,043 - test_reverse_diffusion_reasoning - INFO - Testing guided reverse step...
2025-06-17 09:21:43,056 - test_reverse_diffusion_reasoning - INFO - Samples improved: 8/8
2025-06-17 09:21:43,060 - test_reverse_diffusion_reasoning - INFO - Average distance change: 8.985
2025-06-17 09:21:43,073 - test_reverse_diffusion_reasoning - INFO - Testing interpolative reasoning...
2025-06-17 09:21:43,175 - test_reverse_diffusion_reasoning - INFO - Similarity trend to A: -0.183
2025-06-17 09:21:43,177 - test_reverse_diffusion_reasoning - INFO - Similarity trend to B: 0.209
2025-06-17 09:21:43,189 - test_reverse_diffusion_reasoning - INFO - Testing mathematical properties...
2025-06-17 09:21:43,191 - test_reverse_diffusion_reasoning - INFO - Guided computation difference: 8.2336
2025-06-17 09:21:43,196 - test_reverse_diffusion_reasoning - INFO - Testing multi-constraint reasoning...
2025-06-17 09:21:43,503 - test_reverse_diffusion_reasoning - INFO - Constraints improved: 1/2
2025-06-17 09:21:43,504 - test_reverse_diffusion_reasoning - INFO - Overall satisfaction - Initial: -53.541, Final: -52.226
2025-06-17 09:21:43,504 - test_reverse_diffusion_reasoning - INFO - Process completed successfully with trajectory variance: 2.635046
2025-06-17 09:21:43,511 - test_reverse_diffusion_reasoning - INFO - Testing performance benchmarks...
2025-06-17 09:21:43,687 - test_reverse_diffusion_reasoning - INFO - Single step time: 0.78ms
2025-06-17 09:21:43,688 - test_reverse_diffusion_reasoning - INFO - Full reasoning time: 9.66ms
2025-06-17 09:21:43,697 - test_reverse_diffusion_reasoning - INFO - Testing goal-directed reasoning...
2025-06-17 09:21:43,724 - test_reverse_diffusion_reasoning - INFO - Convergence ratio: 5.137
2025-06-17 09:21:43,727 - test_reverse_diffusion_reasoning - INFO - Final confidence: 0.000
2025-06-17 09:21:43,728 - test_reverse_diffusion_reasoning - INFO - Initial confidence: 0.000
2025-06-17 09:21:43,728 - test_reverse_diffusion_reasoning - INFO - Path variance: 26.505276
2025-06-17 09:21:43,728 - test_reverse_diffusion_reasoning - INFO - Confidence ratio: 0.000
2025-06-17 09:21:43,742 - test_reverse_diffusion_reasoning - INFO - Testing reasoning trajectory analysis...
2025-06-17 09:21:43,749 - test_reverse_diffusion_reasoning - INFO - Convergence rate: 0.849
2025-06-17 09:21:43,749 - test_reverse_diffusion_reasoning - INFO - Monotonicity: 1.000
2025-06-17 09:21:43,749 - test_reverse_diffusion_reasoning - INFO - Final distance: 1.340
2025-06-17 09:21:43,769 - test_reverse_diffusion_reasoning - INFO - Testing end-to-end reasoning...
2025-06-17 09:21:43,793 - test_reverse_diffusion_reasoning - INFO - End-to-end reasoning test passed
2025-06-17 09:21:43,800 - test_reverse_diffusion_reasoning - INFO - Testing training integration...
2025-06-17 09:21:43,847 - test_reverse_diffusion_reasoning - INFO - Training integration test passed
2025-06-17 09:21:43,852 - __main__ - INFO - Suite reverse_diffusion_reasoning executed successfully via direct import
2025-06-17 09:21:43,854 - __main__ - INFO - Suite reverse_diffusion_reasoning completed in 1.94s - PASSED
2025-06-17 09:21:44,775 - test_probabilistic_inference_engine - INFO - MCMC Diagnostics:
2025-06-17 09:21:44,776 - test_probabilistic_inference_engine - INFO -   Acceptance rate: 0.247
2025-06-17 09:21:44,776 - test_probabilistic_inference_engine - INFO -   Mean error: 0.0965 (tolerance: 0.5320) ✓
2025-06-17 09:21:44,776 - test_probabilistic_inference_engine - INFO -   Cov error: 0.1142 (tolerance: 1.0639) ✓
2025-06-17 09:21:44,776 - test_probabilistic_inference_engine - INFO -   Var error: 0.0452 (tolerance: 0.7523) ✓
2025-06-17 09:21:44,776 - test_probabilistic_inference_engine - INFO -   Effective sample size: 113 (autocorr time: 21.6)
2025-06-17 09:21:44,776 - test_probabilistic_inference_engine - INFO -   Efficiency: 2.3% (effective/total)
2025-06-17 09:21:44,776 - test_probabilistic_inference_engine - INFO -   Test dimension: 8
2025-06-17 09:21:44,776 - test_probabilistic_inference_engine - INFO -   Samples: 5000, Warmup: 2000
2025-06-17 09:21:44,776 - test_probabilistic_inference_engine - INFO -   Mixing ratio: 0.236
2025-06-17 09:21:44,776 - test_probabilistic_inference_engine - INFO - MCMC sampling test passed with proper convergence validation
2025-06-17 09:21:44,781 - test_probabilistic_inference_engine - INFO - Testing normalizing flow...
2025-06-17 09:21:44,789 - test_probabilistic_inference_engine - INFO - Normalizing flow test passed
2025-06-17 09:21:44,795 - test_probabilistic_inference_engine - INFO - Testing performance benchmarks...
2025-06-17 09:21:44,829 - test_probabilistic_inference_engine - INFO - Likelihood computation time: 0.28ms
2025-06-17 09:21:44,829 - test_probabilistic_inference_engine - INFO - Posterior approximation time: 0.54ms
2025-06-17 09:21:44,834 - test_probabilistic_inference_engine - INFO - Testing predictive distribution...
2025-06-17 09:21:44,840 - test_probabilistic_inference_engine - INFO - Predictive mean range: [-1.523, 2.967]
2025-06-17 09:21:44,840 - test_probabilistic_inference_engine - INFO - Predictive std range: [6.661, 8.657]
2025-06-17 09:21:44,845 - test_probabilistic_inference_engine - INFO - Testing prior computation...
2025-06-17 09:21:44,847 - test_probabilistic_inference_engine - INFO - Prior mean norm: 0.000
2025-06-17 09:21:44,847 - test_probabilistic_inference_engine - INFO - Prior covariance trace: 64.000
2025-06-17 09:21:44,852 - test_probabilistic_inference_engine - INFO - Testing uncertainty quantification...
2025-06-17 09:21:44,856 - test_probabilistic_inference_engine - INFO - Epistemic uncertainty mean: 60.6189
2025-06-17 09:21:44,857 - test_probabilistic_inference_engine - INFO - Aleatoric uncertainty mean: 0.1000
2025-06-17 09:21:44,857 - test_probabilistic_inference_engine - INFO - Total uncertainty mean: 60.7189
2025-06-17 09:21:44,862 - test_probabilistic_inference_engine - INFO - Testing variational posterior approximation...
2025-06-17 09:21:44,864 - test_probabilistic_inference_engine - INFO - Posterior-prior mean difference: 1.700
2025-06-17 09:21:44,864 - test_probabilistic_inference_engine - INFO - Variational posterior approximation test passed
2025-06-17 09:21:44,868 - test_probabilistic_inference_engine - INFO - Testing end-to-end Bayesian inference...
2025-06-17 09:21:44,921 - test_probabilistic_inference_engine - INFO - Posterior error: 6.230
2025-06-17 09:21:44,921 - test_probabilistic_inference_engine - INFO - Prior error: 6.000
2025-06-17 09:21:44,921 - test_probabilistic_inference_engine - INFO - Evidence: -5.020
2025-06-17 09:21:44,922 - test_probabilistic_inference_engine - INFO - Posterior did not improve over prior (may be due to limited data)
2025-06-17 09:21:44,930 - test_probabilistic_inference_engine - INFO - Testing model comparison workflow...
2025-06-17 09:21:44,945 - test_probabilistic_inference_engine - INFO - True noise level: 0.2
2025-06-17 09:21:44,945 - test_probabilistic_inference_engine - INFO - Selected noise level: 0.5
2025-06-17 09:21:44,945 - test_probabilistic_inference_engine - INFO - Model probabilities: {'model_0': 0.0, 'model_1': 0.0, 'model_2': 1.0}
2025-06-17 09:21:44,946 - __main__ - INFO - Suite probabilistic_inference executed successfully via direct import
2025-06-17 09:21:44,946 - __main__ - INFO - Suite probabilistic_inference completed in 3.03s - PASSED
2025-06-17 09:21:45,089 - __main__ - INFO - Executing priority 3 tests (1 suites)
2025-06-17 09:21:45,109 - __main__ - INFO - Executing test suite: comprehensive_integration
2025-06-17 09:21:45,112 - __main__ - INFO - Successfully imported test_comprehensive_integration via importlib
2025-06-17 09:21:45,302 - __main__ - INFO - Suite comprehensive_integration executed successfully via direct import
2025-06-17 09:21:45,303 - __main__ - INFO - Suite comprehensive_integration completed in 0.19s - PASSED
2025-06-17 09:21:45,431 - __main__ - INFO - Generating final test report...
2025-06-17 09:21:45,434 - __main__ - INFO - Detailed report saved to test_results/final_test_report.json
2025-06-17 09:21:45,434 - __main__ - INFO - ================================================================================
2025-06-17 09:21:45,434 - __main__ - INFO - ULTRA DIFFUSION REASONING MODULE - TEST EXECUTION COMPLETE
2025-06-17 09:21:45,434 - __main__ - INFO - ================================================================================
2025-06-17 09:21:45,434 - __main__ - INFO - Execution Summary:
2025-06-17 09:21:45,434 - __main__ - INFO -   Total Test Suites: 6
2025-06-17 09:21:45,435 - __main__ - INFO -   Passed Suites: 5
2025-06-17 09:21:45,435 - __main__ - INFO -   Failed Suites: 1
2025-06-17 09:21:45,435 - __main__ - INFO -   Success Rate: 83.3%
2025-06-17 09:21:45,435 - __main__ - INFO -   Total Execution Time: 0m 33s
2025-06-17 09:21:45,435 - __main__ - INFO - 
Suite Details:
2025-06-17 09:21:45,435 - __main__ - INFO -   conceptual_diffusion: PASSED (6.7s)
2025-06-17 09:21:45,435 - __main__ - INFO -   thought_latent_space: PASSED (27.6s)
2025-06-17 09:21:45,436 - __main__ - INFO -   bayesian_uncertainty: FAILED (1.9s)
2025-06-17 09:21:45,436 - __main__ - INFO -   reverse_diffusion_reasoning: PASSED (1.9s)
2025-06-17 09:21:45,436 - __main__ - INFO -   probabilistic_inference: PASSED (3.0s)
2025-06-17 09:21:45,436 - __main__ - INFO -   comprehensive_integration: PASSED (0.2s)
2025-06-17 09:21:45,436 - __main__ - WARNING - 
Failed Suites:
2025-06-17 09:21:45,436 - __main__ - WARNING -   bayesian_uncertainty: 3 errors
2025-06-17 09:21:45,436 - __main__ - WARNING -     - Direct import execution returned False...
2025-06-17 09:21:45,437 - __main__ - WARNING -     - Subprocess execution returned False...
2025-06-17 09:21:45,437 - __main__ - WARNING -     - Unittest discovery returned False...
2025-06-17 09:21:45,437 - __main__ - INFO - ================================================================================
2025-06-17 09:21:45,437 - __main__ - INFO - SUGGESTED FIXES:
2025-06-17 09:21:45,437 - __main__ - INFO -   For bayesian_uncertainty:
2025-06-17 09:21:45,437 - __main__ - INFO -     - Check if test file exists: bayesian_uncertainty.py
2025-06-17 09:21:45,437 - __main__ - INFO -     - Verify module is in Python path
2025-06-17 09:21:45,438 - __main__ - INFO -     - Try running: python -m bayesian_uncertainty
2025-06-17 09:21:45,438 - __main__ - WARNING - Most tests passed, but some failures detected
2025-06-17 14:17:25,042 - __main__ - INFO - Setting up test environment...
2025-06-17 14:17:25,088 - __main__ - INFO - System info: {'cpu_count': 2, 'cpu_percent': 59.4, 'memory_total_gb': 7.751644134521484, 'memory_available_gb': 3.0859832763671875, 'memory_percent': 60.2, 'gpu_available': False, 'gpu_count': 0, 'gpu_memory_gb': 0}
2025-06-17 14:17:25,090 - __main__ - INFO - Using device: cpu
2025-06-17 14:17:25,091 - __main__ - INFO - Test environment setup complete
2025-06-17 14:17:25,092 - __main__ - INFO - Initialized test orchestrator with 6 test suites
2025-06-17 14:17:25,092 - __main__ - INFO - ================================================================================
2025-06-17 14:17:25,092 - __main__ - INFO - STARTING ULTRA DIFFUSION REASONING MODULE TESTS
2025-06-17 14:17:25,092 - __main__ - INFO - ================================================================================
2025-06-17 14:17:25,092 - __main__ - INFO - Estimated total execution time: 43 minutes 20 seconds
2025-06-17 14:17:25,092 - __main__ - INFO - Running tests in parallel with 4 workers
2025-06-17 14:17:25,092 - __main__ - INFO - Executing priority 1 tests (3 suites)
2025-06-17 14:17:25,132 - __main__ - INFO - Executing test suite: conceptual_diffusion
2025-06-17 14:17:25,135 - __main__ - INFO - Executing test suite: thought_latent_space
2025-06-17 14:17:36,500 - __main__ - INFO - Successfully imported test_thought_latent_space via importlib
2025-06-17 14:17:38,852 - test_thought_latent_space - INFO - Testing compositional operations...
2025-06-17 14:17:40,019 - test_thought_latent_space - INFO - Compositional operations tests passed
2025-06-17 14:17:40,035 - __main__ - INFO - Successfully imported test_conceptual_diffusion via importlib
2025-06-17 14:17:40,092 - test_thought_latent_space - INFO - Testing encoding-decoding consistency...
2025-06-17 14:17:40,158 - test_conceptual_diffusion - INFO - Testing conceptual space properties...
2025-06-17 14:17:40,199 - test_conceptual_diffusion - INFO - Within-level similarity: 0.194
2025-06-17 14:17:40,202 - test_conceptual_diffusion - INFO - Across-level similarity: 0.194
2025-06-17 14:17:40,202 - test_conceptual_diffusion - INFO - Semantic similarity preservation: 0.991
2025-06-17 14:17:40,216 - test_thought_latent_space - INFO - Reconstruction error: 0.194536
2025-06-17 14:17:40,249 - test_conceptual_diffusion - INFO - Testing forward diffusion process...
2025-06-17 14:17:40,252 - test_conceptual_diffusion - INFO - Forward diffusion process tests passed
2025-06-17 14:17:40,303 - test_thought_latent_space - INFO - Testing hierarchical structure preservation...
2025-06-17 14:17:40,305 - test_conceptual_diffusion - INFO - Testing loss computation...
2025-06-17 14:17:40,377 - test_conceptual_diffusion - INFO - Standard loss: 1.414206
2025-06-17 14:17:40,378 - test_conceptual_diffusion - INFO - VLB loss: 80.314240
2025-06-17 14:17:40,417 - test_conceptual_diffusion - INFO - Testing mathematical consistency...
2025-06-17 14:17:40,456 - test_conceptual_diffusion - INFO - Reconstruction error: 0.708325
2025-06-17 14:17:40,479 - test_thought_latent_space - INFO - Training hierarchical structure classifier...
2025-06-17 14:17:40,522 - test_conceptual_diffusion - INFO - Testing noise schedule properties...
2025-06-17 14:17:40,585 - test_conceptual_diffusion - INFO - Beta range: [0.000100, 0.050000]
2025-06-17 14:17:40,586 - test_conceptual_diffusion - INFO - Alpha cumprod range: [0.999900, 0.078234]
2025-06-17 14:17:40,631 - test_conceptual_diffusion - INFO - Testing performance benchmarks...
2025-06-17 14:17:41,303 - test_conceptual_diffusion - INFO - Forward diffusion time: 0.25ms
2025-06-17 14:17:41,344 - test_conceptual_diffusion - INFO - Reverse diffusion time: 3.42ms
2025-06-17 14:17:41,344 - test_conceptual_diffusion - INFO - Loss computation time: 3.03ms
2025-06-17 14:17:41,389 - test_conceptual_diffusion - INFO - Testing reverse diffusion step...
2025-06-17 14:17:41,400 - test_conceptual_diffusion - INFO - Noise reduction ratio: 1.002
2025-06-17 14:17:41,443 - test_conceptual_diffusion - INFO - Testing sampling process...
2025-06-17 14:17:41,913 - test_conceptual_diffusion - INFO - Initial noise level: 8.572
2025-06-17 14:17:41,913 - test_conceptual_diffusion - INFO - Final noise level: 1.793
2025-06-17 14:17:41,914 - test_conceptual_diffusion - INFO - Noise reduction: 0.791
2025-06-17 14:17:41,938 - test_conceptual_diffusion - INFO - Testing score network properties...
2025-06-17 14:17:42,791 - test_conceptual_diffusion - INFO - Score network output range: [0.000, 0.000]
2025-06-17 14:17:42,890 - test_conceptual_diffusion - INFO - Testing memory efficiency...
2025-06-17 14:17:42,890 - test_conceptual_diffusion - INFO - CUDA not available, skipping memory test
2025-06-17 14:17:42,950 - test_conceptual_diffusion - INFO - Testing training integration...
2025-06-17 14:17:50,583 - test_conceptual_diffusion - INFO - Initial loss: 1.060231
2025-06-17 14:17:50,585 - test_conceptual_diffusion - INFO - Final loss: 1.170345
2025-06-17 14:17:50,586 - test_conceptual_diffusion - INFO - Relative change: 0.104
2025-06-17 14:17:50,591 - __main__ - INFO - Suite conceptual_diffusion executed successfully via direct import
2025-06-17 14:17:50,595 - __main__ - INFO - Suite conceptual_diffusion completed in 25.46s - PASSED
2025-06-17 14:17:51,184 - test_thought_latent_space - INFO - Epoch 0: Loss=2.2395, Accuracy=0.688
2025-06-17 14:17:53,181 - test_thought_latent_space - INFO - Epoch 1: Loss=1.4740, Accuracy=1.000
2025-06-17 14:17:55,121 - test_thought_latent_space - INFO - Epoch 2: Loss=1.1379, Accuracy=1.000
2025-06-17 14:17:56,067 - test_thought_latent_space - INFO - Epoch 3: Loss=1.0055, Accuracy=1.000
2025-06-17 14:17:56,901 - test_thought_latent_space - INFO - Epoch 4: Loss=0.8489, Accuracy=1.000
2025-06-17 14:17:57,373 - test_thought_latent_space - INFO - Epoch 5: Loss=0.7786, Accuracy=1.000
2025-06-17 14:17:57,861 - test_thought_latent_space - INFO - Epoch 6: Loss=0.7132, Accuracy=1.000
2025-06-17 14:17:58,448 - test_thought_latent_space - INFO - Epoch 7: Loss=0.6812, Accuracy=1.000
2025-06-17 14:17:58,919 - test_thought_latent_space - INFO - Epoch 8: Loss=0.6541, Accuracy=1.000
2025-06-17 14:17:59,395 - test_thought_latent_space - INFO - Epoch 9: Loss=0.6428, Accuracy=1.000
2025-06-17 14:18:04,127 - test_thought_latent_space - INFO - Epoch 20: Loss=0.5729, Accuracy=1.000
2025-06-17 14:18:07,340 - test_thought_latent_space - INFO - Achieved high accuracy 1.000 at epoch 31
2025-06-17 14:18:07,340 - test_thought_latent_space - INFO - Testing trained hierarchical structure...
2025-06-17 14:18:07,689 - test_thought_latent_space - INFO - Final test accuracy: 1.000
2025-06-17 14:18:07,689 - test_thought_latent_space - INFO - Training achieved best accuracy: 1.000
2025-06-17 14:18:07,690 - test_thought_latent_space - INFO - Hierarchical coherence: 1.001
2025-06-17 14:18:07,690 - test_thought_latent_space - INFO - Level prediction accuracy: 1.000
2025-06-17 14:18:07,716 - test_thought_latent_space - INFO - Testing interpolation properties...
2025-06-17 14:18:07,731 - test_thought_latent_space - INFO - Interpolation tests passed
2025-06-17 14:18:07,755 - test_thought_latent_space - INFO - Testing loss computation...
2025-06-17 14:18:07,785 - test_thought_latent_space - INFO - Total loss: 1.121737
2025-06-17 14:18:07,785 - test_thought_latent_space - INFO - Reconstruction loss: 0.214613
2025-06-17 14:18:07,785 - test_thought_latent_space - INFO - Hierarchical loss: 1.109066
2025-06-17 14:18:07,808 - test_thought_latent_space - INFO - Testing manifold structure analysis...
2025-06-17 14:18:08,110 - test_thought_latent_space - INFO - Intrinsic dimensionality: 35
2025-06-17 14:18:08,110 - test_thought_latent_space - INFO - Explained variance ratio: 0.953
2025-06-17 14:18:08,110 - test_thought_latent_space - INFO - Local smoothness: 8.511
2025-06-17 14:18:08,110 - test_thought_latent_space - INFO - Condition number: 999.000
2025-06-17 14:18:08,139 - test_thought_latent_space - INFO - Testing memory operations...
2025-06-17 14:18:08,162 - test_thought_latent_space - INFO - Memory operations tests passed
2025-06-17 14:18:08,186 - test_thought_latent_space - INFO - Testing performance benchmarks...
2025-06-17 14:18:17,960 - test_thought_latent_space - INFO - Single encoding time: 2.30ms
2025-06-17 14:18:17,960 - test_thought_latent_space - INFO - Batch processing time: 42.49ms
2025-06-17 14:18:17,982 - test_thought_latent_space - INFO - Testing relational vector computation...
2025-06-17 14:18:18,002 - test_thought_latent_space - INFO - Relational vector tests passed
2025-06-17 14:18:18,020 - test_thought_latent_space - INFO - Testing semantic similarity computation...
2025-06-17 14:18:18,153 - test_thought_latent_space - INFO - Same cluster similarity: 0.476
2025-06-17 14:18:18,153 - test_thought_latent_space - INFO - Different cluster similarity: 0.476
2025-06-17 14:18:18,170 - test_thought_latent_space - INFO - Testing integration with diffusion process...
2025-06-17 14:18:18,228 - test_thought_latent_space - INFO - Noise level 0.1: degradation 0.1926
2025-06-17 14:18:18,232 - test_thought_latent_space - INFO - Noise level 0.3: degradation 0.1946
2025-06-17 14:18:18,237 - test_thought_latent_space - INFO - Noise level 0.5: degradation 0.1949
2025-06-17 14:18:18,241 - test_thought_latent_space - INFO - Noise level 0.8: degradation 0.1858
2025-06-17 14:18:18,241 - test_thought_latent_space - INFO - Diffusion integration test passed
2025-06-17 14:18:18,261 - test_thought_latent_space - INFO - Testing training integration...
2025-06-17 14:18:18,969 - test_thought_latent_space - INFO - Initial loss: 0.989053
2025-06-17 14:18:18,969 - test_thought_latent_space - INFO - Final loss: 0.624059
2025-06-17 14:18:18,970 - test_thought_latent_space - INFO - Loss ratio: 0.631
2025-06-17 14:18:18,972 - __main__ - INFO - Suite thought_latent_space executed successfully via direct import
2025-06-17 14:18:18,972 - __main__ - INFO - Suite thought_latent_space completed in 53.84s - PASSED
2025-06-17 14:18:19,019 - __main__ - INFO - Executing test suite: bayesian_uncertainty
2025-06-17 14:18:19,458 - __main__ - INFO - Successfully imported test_bayesian_uncertainty via importlib
2025-06-17 14:18:19,468 - test_bayesian_uncertainty - INFO - Testing aleatoric uncertainty computation...
2025-06-17 14:18:19,479 - test_bayesian_uncertainty - INFO - Aleatoric uncertainty mean: 0.692754
2025-06-17 14:18:19,480 - test_bayesian_uncertainty - INFO - Aleatoric uncertainty std: 0.054793
2025-06-17 14:18:19,488 - test_bayesian_uncertainty - INFO - Testing Bayesian updating...
2025-06-17 14:18:19,544 - test_bayesian_uncertainty - INFO - Prior determinant: 1.000000
2025-06-17 14:18:19,544 - test_bayesian_uncertainty - INFO - Posterior determinant: 0.000000
2025-06-17 14:18:19,544 - test_bayesian_uncertainty - INFO - Uncertainty reduction: 1.000
2025-06-17 14:18:19,553 - test_bayesian_uncertainty - INFO - Testing calibration framework...
2025-06-17 14:18:20,955 - test_bayesian_uncertainty - INFO - ECE before calibration: 0.2672
2025-06-17 14:18:20,955 - test_bayesian_uncertainty - INFO - ECE after calibration: 0.2209
2025-06-17 14:18:20,955 - test_bayesian_uncertainty - INFO - Calibration framework test passed!
2025-06-17 14:18:20,962 - test_bayesian_uncertainty - INFO - Testing decision uncertainty computation...
2025-06-17 14:18:20,962 - test_bayesian_uncertainty - INFO - Decision uncertainty mean: 1.333634
2025-06-17 14:18:20,963 - test_bayesian_uncertainty - INFO - Maximum entropy: 1.609438
2025-06-17 14:18:20,970 - test_bayesian_uncertainty - INFO - Testing epistemic uncertainty computation...
2025-06-17 14:18:20,973 - test_bayesian_uncertainty - INFO - Epistemic uncertainty mean: 0.055015
2025-06-17 14:18:20,973 - test_bayesian_uncertainty - INFO - Epistemic uncertainty std: 0.029926
2025-06-17 14:18:20,980 - test_bayesian_uncertainty - INFO - Testing mathematical properties...
2025-06-17 14:18:20,982 - test_bayesian_uncertainty - INFO - Confidence mean: 0.571
2025-06-17 14:18:20,982 - test_bayesian_uncertainty - INFO - Temperature: 1.000
2025-06-17 14:18:20,989 - test_bayesian_uncertainty - INFO - Testing mutual information computation...
2025-06-17 14:18:20,998 - test_bayesian_uncertainty - INFO - Mutual information mean: 2.491964
2025-06-17 14:18:20,999 - test_bayesian_uncertainty - INFO - Mutual information std: 0.088524
2025-06-17 14:18:20,999 - test_bayesian_uncertainty - INFO - FIXED: Mutual information computation completed successfully!
2025-06-17 14:18:21,006 - test_bayesian_uncertainty - INFO - Testing performance benchmarks...
2025-06-17 14:18:21,105 - test_bayesian_uncertainty - INFO - CUDA not available, skipping GPU memory test
2025-06-17 14:18:21,106 - test_bayesian_uncertainty - INFO - Average inference time: 0.99ms
2025-06-17 14:18:21,113 - test_bayesian_uncertainty - INFO - Testing stress scenarios...
2025-06-17 14:18:21,117 - test_bayesian_uncertainty - INFO - Stress tests completed successfully
2025-06-17 14:18:21,124 - test_bayesian_uncertainty - INFO - Testing structured uncertainty estimate...
2025-06-17 14:18:21,126 - test_bayesian_uncertainty - INFO - Structured uncertainty estimate test passed!
2025-06-17 14:18:21,133 - test_bayesian_uncertainty - INFO - Testing total uncertainty decomposition...
2025-06-17 14:18:21,136 - test_bayesian_uncertainty - INFO - Total uncertainty mean: 0.747629
2025-06-17 14:18:21,136 - test_bayesian_uncertainty - INFO - Epistemic/Total ratio: 0.066
2025-06-17 14:18:21,136 - test_bayesian_uncertainty - INFO - Aleatoric/Total ratio: 0.934
2025-06-17 14:18:21,143 - test_bayesian_uncertainty - INFO - Testing uncertainty calibration...
2025-06-17 14:18:21,155 - test_bayesian_uncertainty - INFO - Uncertainty-error correlation: 0.072
2025-06-17 14:18:21,162 - test_bayesian_uncertainty - INFO - Testing uncertainty decomposition...
2025-06-17 14:18:21,163 - test_bayesian_uncertainty - INFO - Uncertainty decomposition test passed!
2025-06-17 14:18:21,171 - test_bayesian_uncertainty - INFO - Testing variational inference...
2025-06-17 14:18:21,173 - test_bayesian_uncertainty - INFO - KL divergence: 58.785130
2025-06-17 14:18:21,174 - test_bayesian_uncertainty - INFO - KL divergence (matching): 0.000000
2025-06-17 14:18:21,174 - test_bayesian_uncertainty - INFO - ================================================================================
2025-06-17 14:18:21,174 - test_bayesian_uncertainty - INFO - BAYESIAN UNCERTAINTY QUANTIFICATION TEST SUMMARY (ENHANCED)
2025-06-17 14:18:21,175 - test_bayesian_uncertainty - INFO - ================================================================================
2025-06-17 14:18:21,175 - test_bayesian_uncertainty - INFO - Tests run: 14
2025-06-17 14:18:21,175 - test_bayesian_uncertainty - INFO - Failures: 0
2025-06-17 14:18:21,175 - test_bayesian_uncertainty - INFO - Errors: 0
2025-06-17 14:18:21,175 - test_bayesian_uncertainty - INFO - Success rate: 100.00%
2025-06-17 14:18:21,175 - test_bayesian_uncertainty - INFO - 🎉 ALL TESTS PASSED! The Bayesian uncertainty quantification module is working correctly.
2025-06-17 14:18:21,175 - test_bayesian_uncertainty - INFO - ✅ Key fixes applied:
2025-06-17 14:18:21,176 - test_bayesian_uncertainty - INFO -    - Fixed tensor shape mismatch in mutual information computation
2025-06-17 14:18:21,176 - test_bayesian_uncertainty - INFO -    - Enhanced integration with ULTRA ecosystem
2025-06-17 14:18:21,176 - test_bayesian_uncertainty - INFO -    - Added uncertainty decomposition and calibration frameworks
2025-06-17 14:18:21,176 - test_bayesian_uncertainty - INFO -    - Improved numerical stability and robustness
2025-06-17 14:18:21,450 - __main__ - INFO - Suite bayesian_uncertainty completed in 2.43s - FAILED
2025-06-17 14:18:21,553 - __main__ - INFO - Executing priority 2 tests (2 suites)
2025-06-17 14:18:21,574 - __main__ - INFO - Executing test suite: reverse_diffusion_reasoning
2025-06-17 14:18:21,574 - __main__ - INFO - Executing test suite: probabilistic_inference
2025-06-17 14:18:22,414 - __main__ - INFO - Successfully imported test_probabilistic_inference_engine via importlib
2025-06-17 14:18:22,431 - test_probabilistic_inference_engine - INFO - Testing Bayesian model selection...
2025-06-17 14:18:22,456 - test_probabilistic_inference_engine - INFO - Model probabilities: {'model_0': 0.0, 'model_1': 0.0, 'model_2': 1.0}
2025-06-17 14:18:22,466 - test_probabilistic_inference_engine - INFO - Testing information-theoretic measures...
2025-06-17 14:18:22,510 - test_probabilistic_inference_engine - INFO - Entropy: 4.605
2025-06-17 14:18:22,511 - test_probabilistic_inference_engine - INFO - Mutual information: 2.303
2025-06-17 14:18:22,511 - test_probabilistic_inference_engine - INFO - Effective dimensionality: 53.435
2025-06-17 14:18:22,517 - test_probabilistic_inference_engine - INFO - Testing likelihood computation...
2025-06-17 14:18:22,528 - test_probabilistic_inference_engine - INFO - Log likelihood range: [-0.253, 0.187]
2025-06-17 14:18:22,537 - test_probabilistic_inference_engine - INFO - Testing marginal likelihood computation...
2025-06-17 14:18:22,600 - test_probabilistic_inference_engine - INFO - Log evidence (importance_sampling): 1.336
2025-06-17 14:18:22,662 - test_probabilistic_inference_engine - INFO - Log evidence (variational): -2.055
2025-06-17 14:18:22,671 - test_probabilistic_inference_engine - INFO - Log evidence (network): -0.055
2025-06-17 14:18:22,691 - test_probabilistic_inference_engine - INFO - Testing mathematical properties...
2025-06-17 14:18:22,697 - test_probabilistic_inference_engine - INFO - KL divergence: 30.773285
2025-06-17 14:18:22,699 - test_probabilistic_inference_engine - INFO - Mathematical properties test passed
2025-06-17 14:18:22,705 - test_probabilistic_inference_engine - INFO - Testing MCMC sampling...
2025-06-17 14:18:22,890 - __main__ - INFO - Successfully imported test_reverse_diffusion_reasoning via importlib
2025-06-17 14:18:22,900 - test_reverse_diffusion_reasoning - INFO - Testing analogical reasoning...
2025-06-17 14:18:22,904 - test_reverse_diffusion_reasoning - INFO - Analogical relation similarity: 0.999
2025-06-17 14:18:22,909 - test_reverse_diffusion_reasoning - INFO - Testing constraint satisfaction scoring...
2025-06-17 14:18:22,909 - test_reverse_diffusion_reasoning - INFO - Constraint satisfaction score: 0.000
2025-06-17 14:18:22,910 - test_reverse_diffusion_reasoning - INFO - Perfect constraint score: 1.000
2025-06-17 14:18:22,915 - test_reverse_diffusion_reasoning - INFO - Testing guidance function properties...
2025-06-17 14:18:22,917 - test_reverse_diffusion_reasoning - INFO - Guidance function tests passed
2025-06-17 14:18:22,924 - test_reverse_diffusion_reasoning - INFO - Testing guided reverse step...
2025-06-17 14:18:22,927 - test_reverse_diffusion_reasoning - INFO - Samples improved: 8/8
2025-06-17 14:18:22,927 - test_reverse_diffusion_reasoning - INFO - Average distance change: 9.691
2025-06-17 14:18:22,931 - test_reverse_diffusion_reasoning - INFO - Testing interpolative reasoning...
2025-06-17 14:18:23,028 - test_reverse_diffusion_reasoning - INFO - Similarity trend to A: -0.170
2025-06-17 14:18:23,030 - test_reverse_diffusion_reasoning - INFO - Similarity trend to B: 0.171
2025-06-17 14:18:23,051 - test_reverse_diffusion_reasoning - INFO - Testing mathematical properties...
2025-06-17 14:18:23,053 - test_reverse_diffusion_reasoning - INFO - Guided computation difference: 8.4637
2025-06-17 14:18:23,064 - test_reverse_diffusion_reasoning - INFO - Testing multi-constraint reasoning...
2025-06-17 14:18:23,343 - test_reverse_diffusion_reasoning - INFO - Constraints improved: 1/2
2025-06-17 14:18:23,345 - test_reverse_diffusion_reasoning - INFO - Overall satisfaction - Initial: -67.294, Final: -63.909
2025-06-17 14:18:23,345 - test_reverse_diffusion_reasoning - INFO - Process completed successfully with trajectory variance: 4.329504
2025-06-17 14:18:23,358 - test_reverse_diffusion_reasoning - INFO - Testing performance benchmarks...
2025-06-17 14:18:23,532 - test_reverse_diffusion_reasoning - INFO - Single step time: 0.97ms
2025-06-17 14:18:23,532 - test_reverse_diffusion_reasoning - INFO - Full reasoning time: 7.59ms
2025-06-17 14:18:23,541 - test_reverse_diffusion_reasoning - INFO - Testing goal-directed reasoning...
2025-06-17 14:18:23,559 - test_reverse_diffusion_reasoning - INFO - Convergence ratio: 4.192
2025-06-17 14:18:23,560 - test_reverse_diffusion_reasoning - INFO - Final confidence: 0.000
2025-06-17 14:18:23,561 - test_reverse_diffusion_reasoning - INFO - Initial confidence: 0.000
2025-06-17 14:18:23,561 - test_reverse_diffusion_reasoning - INFO - Path variance: 23.438015
2025-06-17 14:18:23,562 - test_reverse_diffusion_reasoning - INFO - Confidence ratio: 0.000
2025-06-17 14:18:23,571 - test_reverse_diffusion_reasoning - INFO - Testing reasoning trajectory analysis...
2025-06-17 14:18:23,574 - test_reverse_diffusion_reasoning - INFO - Convergence rate: 0.840
2025-06-17 14:18:23,574 - test_reverse_diffusion_reasoning - INFO - Monotonicity: 1.000
2025-06-17 14:18:23,574 - test_reverse_diffusion_reasoning - INFO - Final distance: 1.664
2025-06-17 14:18:23,585 - test_reverse_diffusion_reasoning - INFO - Testing end-to-end reasoning...
2025-06-17 14:18:23,604 - test_reverse_diffusion_reasoning - INFO - End-to-end reasoning test passed
2025-06-17 14:18:23,614 - test_reverse_diffusion_reasoning - INFO - Testing training integration...
2025-06-17 14:18:23,651 - test_reverse_diffusion_reasoning - INFO - Training integration test passed
2025-06-17 14:18:23,654 - __main__ - INFO - Suite reverse_diffusion_reasoning executed successfully via direct import
2025-06-17 14:18:23,655 - __main__ - INFO - Suite reverse_diffusion_reasoning completed in 2.08s - PASSED
2025-06-17 14:18:24,493 - test_probabilistic_inference_engine - INFO - MCMC Diagnostics:
2025-06-17 14:18:24,493 - test_probabilistic_inference_engine - INFO -   Acceptance rate: 0.254
2025-06-17 14:18:24,493 - test_probabilistic_inference_engine - INFO -   Mean error: 0.0985 (tolerance: 0.5558) ✓
2025-06-17 14:18:24,493 - test_probabilistic_inference_engine - INFO -   Cov error: 0.1014 (tolerance: 1.1115) ✓
2025-06-17 14:18:24,493 - test_probabilistic_inference_engine - INFO -   Var error: 0.0651 (tolerance: 0.7860) ✓
2025-06-17 14:18:24,493 - test_probabilistic_inference_engine - INFO -   Effective sample size: 104 (autocorr time: 23.6)
2025-06-17 14:18:24,493 - test_probabilistic_inference_engine - INFO -   Efficiency: 2.1% (effective/total)
2025-06-17 14:18:24,494 - test_probabilistic_inference_engine - INFO -   Test dimension: 8
2025-06-17 14:18:24,494 - test_probabilistic_inference_engine - INFO -   Samples: 5000, Warmup: 2000
2025-06-17 14:18:24,494 - test_probabilistic_inference_engine - INFO -   Mixing ratio: 0.245
2025-06-17 14:18:24,494 - test_probabilistic_inference_engine - INFO - MCMC sampling test passed with proper convergence validation
2025-06-17 14:18:24,501 - test_probabilistic_inference_engine - INFO - Testing normalizing flow...
2025-06-17 14:18:24,509 - test_probabilistic_inference_engine - INFO - Normalizing flow test passed
2025-06-17 14:18:24,515 - test_probabilistic_inference_engine - INFO - Testing performance benchmarks...
2025-06-17 14:18:24,543 - test_probabilistic_inference_engine - INFO - Likelihood computation time: 0.22ms
2025-06-17 14:18:24,543 - test_probabilistic_inference_engine - INFO - Posterior approximation time: 0.52ms
2025-06-17 14:18:24,548 - test_probabilistic_inference_engine - INFO - Testing predictive distribution...
2025-06-17 14:18:24,554 - test_probabilistic_inference_engine - INFO - Predictive mean range: [0.093, 3.149]
2025-06-17 14:18:24,554 - test_probabilistic_inference_engine - INFO - Predictive std range: [7.411, 9.746]
2025-06-17 14:18:24,559 - test_probabilistic_inference_engine - INFO - Testing prior computation...
2025-06-17 14:18:24,561 - test_probabilistic_inference_engine - INFO - Prior mean norm: 0.000
2025-06-17 14:18:24,561 - test_probabilistic_inference_engine - INFO - Prior covariance trace: 64.000
2025-06-17 14:18:24,566 - test_probabilistic_inference_engine - INFO - Testing uncertainty quantification...
2025-06-17 14:18:24,570 - test_probabilistic_inference_engine - INFO - Epistemic uncertainty mean: 64.9135
2025-06-17 14:18:24,570 - test_probabilistic_inference_engine - INFO - Aleatoric uncertainty mean: 0.1000
2025-06-17 14:18:24,570 - test_probabilistic_inference_engine - INFO - Total uncertainty mean: 65.0135
2025-06-17 14:18:24,575 - test_probabilistic_inference_engine - INFO - Testing variational posterior approximation...
2025-06-17 14:18:24,577 - test_probabilistic_inference_engine - INFO - Posterior-prior mean difference: 1.431
2025-06-17 14:18:24,577 - test_probabilistic_inference_engine - INFO - Variational posterior approximation test passed
2025-06-17 14:18:24,584 - test_probabilistic_inference_engine - INFO - Testing end-to-end Bayesian inference...
2025-06-17 14:18:24,618 - test_probabilistic_inference_engine - INFO - Posterior error: 5.198
2025-06-17 14:18:24,618 - test_probabilistic_inference_engine - INFO - Prior error: 5.207
2025-06-17 14:18:24,619 - test_probabilistic_inference_engine - INFO - Evidence: 0.051
2025-06-17 14:18:24,619 - test_probabilistic_inference_engine - INFO - Posterior improved over prior ✓
2025-06-17 14:18:24,625 - test_probabilistic_inference_engine - INFO - Testing model comparison workflow...
2025-06-17 14:18:24,640 - test_probabilistic_inference_engine - INFO - True noise level: 0.2
2025-06-17 14:18:24,640 - test_probabilistic_inference_engine - INFO - Selected noise level: 0.5
2025-06-17 14:18:24,640 - test_probabilistic_inference_engine - INFO - Model probabilities: {'model_0': 0.0, 'model_1': 0.0, 'model_2': 1.0}
2025-06-17 14:18:24,641 - __main__ - INFO - Suite probabilistic_inference executed successfully via direct import
2025-06-17 14:18:24,641 - __main__ - INFO - Suite probabilistic_inference completed in 3.07s - PASSED
2025-06-17 14:18:24,772 - __main__ - INFO - Executing priority 3 tests (1 suites)
2025-06-17 14:18:24,789 - __main__ - INFO - Executing test suite: comprehensive_integration
2025-06-17 14:18:24,793 - __main__ - INFO - Successfully imported test_comprehensive_integration via importlib
2025-06-17 14:18:24,994 - __main__ - INFO - Suite comprehensive_integration executed successfully via direct import
2025-06-17 14:18:24,994 - __main__ - INFO - Suite comprehensive_integration completed in 0.20s - PASSED
2025-06-17 14:18:25,118 - __main__ - INFO - Generating final test report...
2025-06-17 14:18:25,121 - __main__ - INFO - Detailed report saved to test_results/final_test_report.json
2025-06-17 14:18:25,122 - __main__ - INFO - ================================================================================
2025-06-17 14:18:25,122 - __main__ - INFO - ULTRA DIFFUSION REASONING MODULE - TEST EXECUTION COMPLETE
2025-06-17 14:18:25,122 - __main__ - INFO - ================================================================================
2025-06-17 14:18:25,122 - __main__ - INFO - Execution Summary:
2025-06-17 14:18:25,122 - __main__ - INFO -   Total Test Suites: 6
2025-06-17 14:18:25,122 - __main__ - INFO -   Passed Suites: 5
2025-06-17 14:18:25,123 - __main__ - INFO -   Failed Suites: 1
2025-06-17 14:18:25,123 - __main__ - INFO -   Success Rate: 83.3%
2025-06-17 14:18:25,123 - __main__ - INFO -   Total Execution Time: 1m 0s
2025-06-17 14:18:25,123 - __main__ - INFO - 
Suite Details:
2025-06-17 14:18:25,123 - __main__ - INFO -   conceptual_diffusion: PASSED (25.5s)
2025-06-17 14:18:25,123 - __main__ - INFO -   thought_latent_space: PASSED (53.8s)
2025-06-17 14:18:25,123 - __main__ - INFO -   bayesian_uncertainty: FAILED (2.4s)
2025-06-17 14:18:25,124 - __main__ - INFO -   reverse_diffusion_reasoning: PASSED (2.1s)
2025-06-17 14:18:25,124 - __main__ - INFO -   probabilistic_inference: PASSED (3.1s)
2025-06-17 14:18:25,124 - __main__ - INFO -   comprehensive_integration: PASSED (0.2s)
2025-06-17 14:18:25,124 - __main__ - WARNING - 
Failed Suites:
2025-06-17 14:18:25,124 - __main__ - WARNING -   bayesian_uncertainty: 3 errors
2025-06-17 14:18:25,124 - __main__ - WARNING -     - Direct import execution returned False...
2025-06-17 14:18:25,124 - __main__ - WARNING -     - Subprocess execution returned False...
2025-06-17 14:18:25,124 - __main__ - WARNING -     - Unittest discovery returned False...
2025-06-17 14:18:25,125 - __main__ - INFO - ================================================================================
2025-06-17 14:18:25,125 - __main__ - INFO - SUGGESTED FIXES:
2025-06-17 14:18:25,125 - __main__ - INFO -   For bayesian_uncertainty:
2025-06-17 14:18:25,125 - __main__ - INFO -     - Check if test file exists: bayesian_uncertainty.py
2025-06-17 14:18:25,125 - __main__ - INFO -     - Verify module is in Python path
2025-06-17 14:18:25,125 - __main__ - INFO -     - Try running: python -m bayesian_uncertainty
2025-06-17 14:18:25,125 - __main__ - WARNING - Most tests passed, but some failures detected
