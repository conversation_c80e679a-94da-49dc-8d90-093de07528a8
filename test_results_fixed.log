2025-06-14 13:38:40,479 - INFO - Setting up fixed test suite...
2025-06-14 13:38:40,479 - INFO - ============================================================
2025-06-14 13:38:40,479 - INFO - FIXED TEST 5: ROBUST CONCEPT CLUSTERING VALIDATION
2025-06-14 13:38:40,479 - INFO - ============================================================
2025-06-14 13:38:40,480 - INFO - Average within-animals similarity: 0.862
2025-06-14 13:38:40,480 - INFO - Average within-vehicles similarity: 0.895
2025-06-14 13:38:40,480 - INFO - Average within-colors similarity: 0.966
2025-06-14 13:38:40,481 - INFO - Average within-emotions similarity: 0.972
2025-06-14 13:38:40,482 - INFO - Average animals<->vehicles similarity: 0.653
2025-06-14 13:38:40,482 - INFO - Average animals<->colors similarity: 0.597
2025-06-14 13:38:40,483 - INFO - Average animals<->emotions similarity: 0.649
2025-06-14 13:38:40,484 - INFO - Average vehicles<->colors similarity: 0.816
2025-06-14 13:38:40,487 - INFO - Average vehicles<->emotions similarity: 0.772
2025-06-14 13:38:40,488 - INFO - Average colors<->emotions similarity: 0.890
2025-06-14 13:38:40,508 - INFO - 
ROBUST VALIDATION RESULTS:
2025-06-14 13:38:40,508 - INFO - Within-category mean: 0.924
2025-06-14 13:38:40,508 - INFO - Between-category mean: 0.730
2025-06-14 13:38:40,508 - INFO - Separation ratio: 1.266
2025-06-14 13:38:40,508 - INFO - Effect size (Cohen's d): 2.227
2025-06-14 13:38:40,509 - INFO - Statistical significance (p-value): 0.007694
2025-06-14 13:38:40,509 - INFO - Gap size: 0.194
2025-06-14 13:38:40,509 - INFO - 
VALIDATION CRITERIA:
2025-06-14 13:38:40,509 - INFO -   effect_size_large: ✅
2025-06-14 13:38:40,509 - INFO -   statistically_significant: ✅
2025-06-14 13:38:40,509 - INFO -   separation_ratio_good: ✅
2025-06-14 13:38:40,509 - INFO -   absolute_gap_meaningful: ✅
2025-06-14 13:38:40,509 - INFO -   within_category_reasonable: ✅
2025-06-14 13:38:40,509 - INFO -   variance_reasonable: ✅
2025-06-14 13:38:40,509 - INFO - 
Criteria passed: 6/6
2025-06-14 13:38:40,509 - INFO - ✅ FIXED TEST 5 PASSED: Concept clustering is statistically robust
2025-06-14 13:38:40,512 - INFO - Setting up fixed test suite...
2025-06-14 13:38:40,512 - INFO - ============================================================
2025-06-14 13:38:40,512 - INFO - FIXED TEST 7: ROBUST PERFORMANCE BENCHMARKING
2025-06-14 13:38:40,512 - INFO - ============================================================
2025-06-14 13:38:40,513 - INFO - Testing performance with 5 steps...
2025-06-14 13:38:40,533 - ERROR - Test failed with exception: Tensors must have same number of dimensions: got 1 and 2
2025-06-14 13:38:40,613 - INFO - 
PERFORMANCE TEST RESULTS:
2025-06-14 13:38:40,613 - INFO - Test successful: False
2025-06-14 13:38:40,614 - WARNING - Error details: Test failed with exception: Tensors must have same number of dimensions: got 1 and 2
2025-06-14 13:38:40,614 - INFO - 
RESOURCE USAGE:
2025-06-14 13:38:40,614 - INFO - Initial memory: 313.1MB
2025-06-14 13:38:40,614 - INFO - Peak memory: 313.1MB
2025-06-14 13:38:40,614 - INFO - Final memory: 319.0MB
2025-06-14 13:38:40,614 - INFO - Memory increase: 5.9MB
2025-06-14 13:38:40,614 - INFO - 
PERFORMANCE BREAKDOWN:
2025-06-14 13:38:40,614 - INFO - 
VALIDATION RESULTS:
2025-06-14 13:38:40,614 - INFO - Performance valid: False
