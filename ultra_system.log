2025-06-05 08:08:42,718 - ultra.main - INFO - Logging configuration complete
2025-06-05 08:08:42,719 - ultra.main - INFO - ULTRA system package initialized (version 1.0.0)
2025-06-05 08:08:42,719 - ultra.main - INFO - Components available: False
2025-06-05 08:08:42,720 - ultra.main - WARNING - Environment issue: Insufficient memory: 2.4 GB available, 4+ GB recommended
2025-06-05 08:08:42,720 - ultra.main - WARNING - Environment issue: Insufficient disk space: 3.4 GB available, 10+ GB recommended
2025-06-05 08:08:42,721 - ultra.main - INFO - System info: posix with 2 CPU cores, 7.8 GB memory
2025-06-05 08:08:42,721 - ultra.main - INFO - ULTRA package initialization complete - ready for system creation
2025-06-05 08:08:42,721 - ultra.main - INFO - Use ultra.initialize_ultra() to create and start an ULTRA system
2025-06-05 08:08:42,728 - ultra.main - INFO - Logging configuration complete
2025-06-05 08:08:42,728 - ultra.main - INFO - ULTRA system package initialized (version 1.0.0)
2025-06-05 08:08:42,728 - ultra.main - INFO - Components available: False
2025-06-05 08:08:42,729 - ultra.main - WARNING - Environment issue: Insufficient memory: 2.4 GB available, 4+ GB recommended
2025-06-05 08:08:42,729 - ultra.main - WARNING - Environment issue: Insufficient disk space: 3.4 GB available, 10+ GB recommended
2025-06-05 08:08:42,730 - ultra.main - INFO - System info: posix with 2 CPU cores, 7.8 GB memory
2025-06-05 08:08:42,730 - ultra.main - INFO - ULTRA package initialization complete - ready for system creation
2025-06-05 08:08:42,731 - ultra.main - INFO - Use ultra.initialize_ultra() to create and start an ULTRA system
2025-06-05 08:08:42,731 - ultra.core_neural - INFO - Cleaning up Core Neural Architecture resources
2025-06-06 07:54:41,159 - ultra.main - INFO - Logging configuration complete
2025-06-06 07:54:41,160 - ultra.main - INFO - ULTRA system package initialized (version 1.0.0)
2025-06-06 07:54:41,160 - ultra.main - INFO - Components available: False
2025-06-06 07:54:41,161 - ultra.main - WARNING - Environment issue: Insufficient disk space: 3.1 GB available, 10+ GB recommended
2025-06-06 07:54:41,161 - ultra.main - INFO - System info: posix with 2 CPU cores, 7.8 GB memory
2025-06-06 07:54:41,161 - ultra.main - INFO - ULTRA package initialization complete - ready for system creation
2025-06-06 07:54:41,161 - ultra.main - INFO - Use ultra.initialize_ultra() to create and start an ULTRA system
2025-06-06 07:54:41,221 - utils - INFO - ULTRA Utils module initialized successfully
2025-06-06 07:54:41,222 - utils - INFO - Available utility classes: ['MathematicalOperations', 'NeuromorphicUtils', 'DiffusionUtils', 'AttentionUtils', 'GraphUtils', 'OptimizationUtils', 'MemoryManager', 'EvaluationMetrics', 'SystemMonitor', 'ParallelProcessor', 'ConfigManager']
2025-06-06 07:54:41,222 - utils - INFO - Mathematical operations validation passed
2025-06-06 07:54:41,223 - utils - INFO - ULTRA Utils module ready for production use
2025-06-06 07:54:41,255 - ultra.config - INFO - Configuration manager initialized with directory: ./config
2025-06-06 07:54:41,263 - ultra.config - INFO - ULTRA Configuration System initialized successfully
2025-06-06 07:54:41,264 - ultra.config - INFO - Available configuration classes: 38 total
2025-06-06 07:54:41,264 - ultra.config - INFO - Configuration system ready for production use
2025-06-06 07:54:41,292 - ultra.config - INFO - Configuration manager initialized with directory: ./config
2025-06-06 07:54:41,299 - ultra.config - INFO - ULTRA Configuration System initialized successfully
2025-06-06 07:54:41,300 - ultra.config - INFO - Available configuration classes: 38 total
2025-06-06 07:54:41,300 - ultra.config - INFO - Configuration system ready for production use
2025-06-06 07:54:41,330 - ultra.api - ERROR - Failed to connect to Redis: Error 111 connecting to localhost:6379. Connection refused.
2025-06-06 07:54:41,343 - ultra.api - INFO - Database initialized
2025-06-06 07:54:41,344 - ultra.api - INFO - Celery initialized
2025-06-06 07:54:41,345 - ultra.api - INFO - ULTRA API Manager initialized
2025-06-06 07:54:41,364 - asyncio - DEBUG - Using selector: EpollSelector
2025-06-06 07:54:41,439 - ultra.core_neural - INFO - Cleaning up Core Neural Architecture resources
2025-06-06 07:54:41,631 - jax._src.xla_bridge - DEBUG - Clearing JAX backend caches.
2025-06-06 08:07:56,869 - ultra.main - INFO - Logging configuration complete
2025-06-06 08:07:56,869 - ultra.main - INFO - ULTRA system package initialized (version 1.0.0)
2025-06-06 08:07:56,869 - ultra.main - INFO - Components available: False
2025-06-06 08:07:56,869 - ultra.main - WARNING - Environment issue: Insufficient disk space: 3.1 GB available, 10+ GB recommended
2025-06-06 08:07:56,870 - ultra.main - INFO - System info: posix with 2 CPU cores, 7.8 GB memory
2025-06-06 08:07:56,870 - ultra.main - INFO - ULTRA package initialization complete - ready for system creation
2025-06-06 08:07:56,870 - ultra.main - INFO - Use ultra.initialize_ultra() to create and start an ULTRA system
2025-06-06 08:07:56,887 - utils - INFO - ULTRA Utils module initialized successfully
2025-06-06 08:07:56,887 - utils - INFO - Available utility classes: ['MathematicalOperations', 'NeuromorphicUtils', 'DiffusionUtils', 'AttentionUtils', 'GraphUtils', 'OptimizationUtils', 'MemoryManager', 'EvaluationMetrics', 'SystemMonitor', 'ParallelProcessor', 'ConfigManager']
2025-06-06 08:07:56,888 - utils - INFO - Mathematical operations validation passed
2025-06-06 08:07:56,888 - utils - INFO - ULTRA Utils module ready for production use
2025-06-06 08:07:56,918 - ultra.config - INFO - Configuration manager initialized with directory: ./config
2025-06-06 08:07:56,924 - ultra.config - INFO - ULTRA Configuration System initialized successfully
2025-06-06 08:07:56,925 - ultra.config - INFO - Available configuration classes: 38 total
2025-06-06 08:07:56,925 - ultra.config - INFO - Configuration system ready for production use
2025-06-06 08:07:56,962 - ultra.config - INFO - Configuration manager initialized with directory: ./config
2025-06-06 08:07:56,969 - ultra.config - INFO - ULTRA Configuration System initialized successfully
2025-06-06 08:07:56,969 - ultra.config - INFO - Available configuration classes: 38 total
2025-06-06 08:07:56,969 - ultra.config - INFO - Configuration system ready for production use
2025-06-06 08:07:56,994 - ultra.api - ERROR - Failed to connect to Redis: Error 111 connecting to localhost:6379. Connection refused.
2025-06-06 08:07:57,006 - ultra.api - INFO - Database initialized
2025-06-06 08:07:57,007 - ultra.api - INFO - Celery initialized
2025-06-06 08:07:57,007 - ultra.api - INFO - ULTRA API Manager initialized
2025-06-06 08:07:57,026 - asyncio - DEBUG - Using selector: EpollSelector
2025-06-06 08:07:57,089 - ultra.api - ERROR - Failed to connect to Redis: Error 111 connecting to localhost:6379. Connection refused.
2025-06-06 08:07:57,091 - ultra.api - INFO - Database initialized
2025-06-06 08:07:57,092 - ultra.api - INFO - Celery initialized
2025-06-06 08:07:57,092 - ultra.api - INFO - ULTRA API Manager initialized
2025-06-06 08:07:57,106 - ultra.api - INFO - ULTRA API module initialized (version 1.0.0)
2025-06-06 08:07:57,106 - ultra.api - INFO - ULTRA system available: False
2025-06-06 08:07:57,106 - ultra.api - INFO - API ready for production deployment
2025-06-06 08:07:57,107 - ultra.api - INFO - Starting ULTRA API application...
2025-06-06 08:07:57,107 - ultra.api - WARNING - ULTRA system not available, using mock responses
2025-06-08 09:20:56,695 - ultra.main - INFO - Logging configuration complete
2025-06-08 09:20:56,695 - ultra.main - INFO - ULTRA system package initialized (version 1.0.0)
2025-06-08 09:20:56,696 - ultra.main - INFO - Components available: False
2025-06-08 09:20:56,696 - ultra.main - WARNING - Environment issue: Insufficient memory: 3.1 GB available, 4+ GB recommended
2025-06-08 09:20:56,697 - ultra.main - WARNING - Environment issue: Insufficient disk space: 3.6 GB available, 10+ GB recommended
2025-06-08 09:20:56,698 - ultra.main - INFO - System info: posix with 2 CPU cores, 7.8 GB memory
2025-06-08 09:20:56,699 - ultra.main - INFO - ULTRA package initialization complete - ready for system creation
2025-06-08 09:20:56,700 - ultra.main - INFO - Use ultra.initialize_ultra() to create and start an ULTRA system
2025-06-08 09:20:56,716 - ultra.main - INFO - Logging configuration complete
2025-06-08 09:20:56,718 - ultra.main - INFO - ULTRA system package initialized (version 1.0.0)
2025-06-08 09:20:56,718 - ultra.main - INFO - Components available: False
2025-06-08 09:20:56,719 - ultra.main - WARNING - Environment issue: Insufficient memory: 3.1 GB available, 4+ GB recommended
2025-06-08 09:20:56,719 - ultra.main - WARNING - Environment issue: Insufficient disk space: 3.6 GB available, 10+ GB recommended
2025-06-08 09:20:56,719 - ultra.main - INFO - System info: posix with 2 CPU cores, 7.8 GB memory
2025-06-08 09:20:56,719 - ultra.main - INFO - ULTRA package initialization complete - ready for system creation
2025-06-08 09:20:56,722 - ultra.main - INFO - Use ultra.initialize_ultra() to create and start an ULTRA system
2025-06-08 09:20:56,723 - ultra.core_neural - INFO - Cleaning up Core Neural Architecture resources
2025-06-08 10:59:42,770 - ultra.main - INFO - Logging configuration complete
2025-06-08 10:59:42,771 - ultra.main - INFO - ULTRA system package initialized (version 1.0.0)
2025-06-08 10:59:42,772 - ultra.main - INFO - Components available: False
2025-06-08 10:59:42,772 - ultra.main - WARNING - Environment issue: Insufficient memory: 2.5 GB available, 4+ GB recommended
2025-06-08 10:59:42,772 - ultra.main - WARNING - Environment issue: Insufficient disk space: 3.6 GB available, 10+ GB recommended
2025-06-08 10:59:42,773 - ultra.main - INFO - System info: posix with 2 CPU cores, 7.8 GB memory
2025-06-08 10:59:42,774 - ultra.main - INFO - ULTRA package initialization complete - ready for system creation
2025-06-08 10:59:42,774 - ultra.main - INFO - Use ultra.initialize_ultra() to create and start an ULTRA system
2025-06-08 10:59:42,788 - ultra.main - INFO - Logging configuration complete
2025-06-08 10:59:42,792 - ultra.main - INFO - ULTRA system package initialized (version 1.0.0)
2025-06-08 10:59:42,792 - ultra.main - INFO - Components available: False
2025-06-08 10:59:42,793 - ultra.main - WARNING - Environment issue: Insufficient memory: 2.5 GB available, 4+ GB recommended
2025-06-08 10:59:42,793 - ultra.main - WARNING - Environment issue: Insufficient disk space: 3.6 GB available, 10+ GB recommended
2025-06-08 10:59:42,793 - ultra.main - INFO - System info: posix with 2 CPU cores, 7.8 GB memory
2025-06-08 10:59:42,795 - ultra.main - INFO - ULTRA package initialization complete - ready for system creation
2025-06-08 10:59:42,797 - ultra.main - INFO - Use ultra.initialize_ultra() to create and start an ULTRA system
2025-06-08 10:59:42,804 - ultra.core_neural - INFO - Cleaning up Core Neural Architecture resources
2025-06-08 11:42:50,214 - ultra.main - INFO - Logging configuration complete
2025-06-08 11:42:50,215 - ultra.main - INFO - ULTRA system package initialized (version 1.0.0)
2025-06-08 11:42:50,215 - ultra.main - INFO - Components available: False
2025-06-08 11:42:50,215 - ultra.main - WARNING - Environment issue: Insufficient memory: 2.4 GB available, 4+ GB recommended
2025-06-08 11:42:50,215 - ultra.main - WARNING - Environment issue: Insufficient disk space: 3.6 GB available, 10+ GB recommended
2025-06-08 11:42:50,216 - ultra.main - INFO - System info: posix with 2 CPU cores, 7.8 GB memory
2025-06-08 11:42:50,216 - ultra.main - INFO - ULTRA package initialization complete - ready for system creation
2025-06-08 11:42:50,216 - ultra.main - INFO - Use ultra.initialize_ultra() to create and start an ULTRA system
2025-06-08 11:42:50,226 - ultra.main - INFO - Logging configuration complete
2025-06-08 11:42:50,227 - ultra.main - INFO - ULTRA system package initialized (version 1.0.0)
2025-06-08 11:42:50,227 - ultra.main - INFO - Components available: False
2025-06-08 11:42:50,227 - ultra.main - WARNING - Environment issue: Insufficient memory: 2.4 GB available, 4+ GB recommended
2025-06-08 11:42:50,227 - ultra.main - WARNING - Environment issue: Insufficient disk space: 3.6 GB available, 10+ GB recommended
2025-06-08 11:42:50,228 - ultra.main - INFO - System info: posix with 2 CPU cores, 7.8 GB memory
2025-06-08 11:42:50,230 - ultra.main - INFO - ULTRA package initialization complete - ready for system creation
2025-06-08 11:42:50,230 - ultra.main - INFO - Use ultra.initialize_ultra() to create and start an ULTRA system
2025-06-08 11:42:50,231 - ultra.core_neural - INFO - Cleaning up Core Neural Architecture resources
2025-06-08 11:46:13,935 - ultra.main - INFO - Logging configuration complete
2025-06-08 11:46:13,935 - ultra.main - INFO - ULTRA system package initialized (version 1.0.0)
2025-06-08 11:46:13,936 - ultra.main - INFO - Components available: False
2025-06-08 11:46:13,936 - ultra.main - WARNING - Environment issue: Insufficient memory: 2.4 GB available, 4+ GB recommended
2025-06-08 11:46:13,936 - ultra.main - WARNING - Environment issue: Insufficient disk space: 3.6 GB available, 10+ GB recommended
2025-06-08 11:46:13,937 - ultra.main - INFO - System info: posix with 2 CPU cores, 7.8 GB memory
2025-06-08 11:46:13,937 - ultra.main - INFO - ULTRA package initialization complete - ready for system creation
2025-06-08 11:46:13,937 - ultra.main - INFO - Use ultra.initialize_ultra() to create and start an ULTRA system
2025-06-08 11:46:13,945 - ultra.main - INFO - Logging configuration complete
2025-06-08 11:46:13,945 - ultra.main - INFO - ULTRA system package initialized (version 1.0.0)
2025-06-08 11:46:13,945 - ultra.main - INFO - Components available: False
2025-06-08 11:46:13,948 - ultra.main - WARNING - Environment issue: Insufficient memory: 2.4 GB available, 4+ GB recommended
2025-06-08 11:46:13,948 - ultra.main - WARNING - Environment issue: Insufficient disk space: 3.6 GB available, 10+ GB recommended
2025-06-08 11:46:13,949 - ultra.main - INFO - System info: posix with 2 CPU cores, 7.8 GB memory
2025-06-08 11:46:13,949 - ultra.main - INFO - ULTRA package initialization complete - ready for system creation
2025-06-08 11:46:13,949 - ultra.main - INFO - Use ultra.initialize_ultra() to create and start an ULTRA system
2025-06-08 11:46:13,949 - ultra.core_neural - INFO - Cleaning up Core Neural Architecture resources
2025-06-08 16:13:38,537 - ultra.main - INFO - Logging configuration complete
2025-06-08 16:13:38,537 - ultra.main - INFO - ULTRA system package initialized (version 1.0.0)
2025-06-08 16:13:38,537 - ultra.main - INFO - Components available: False
2025-06-08 16:13:38,538 - ultra.main - WARNING - Environment issue: Insufficient memory: 2.5 GB available, 4+ GB recommended
2025-06-08 16:13:38,538 - ultra.main - WARNING - Environment issue: Insufficient disk space: 3.6 GB available, 10+ GB recommended
2025-06-08 16:13:38,538 - ultra.main - INFO - System info: posix with 2 CPU cores, 7.8 GB memory
2025-06-08 16:13:38,539 - ultra.main - INFO - ULTRA package initialization complete - ready for system creation
2025-06-08 16:13:38,539 - ultra.main - INFO - Use ultra.initialize_ultra() to create and start an ULTRA system
2025-06-08 16:13:38,545 - ultra.main - INFO - Logging configuration complete
2025-06-08 16:13:38,546 - ultra.main - INFO - ULTRA system package initialized (version 1.0.0)
2025-06-08 16:13:38,546 - ultra.main - INFO - Components available: False
2025-06-08 16:13:38,546 - ultra.main - WARNING - Environment issue: Insufficient memory: 2.5 GB available, 4+ GB recommended
2025-06-08 16:13:38,547 - ultra.main - WARNING - Environment issue: Insufficient disk space: 3.6 GB available, 10+ GB recommended
2025-06-08 16:13:38,547 - ultra.main - INFO - System info: posix with 2 CPU cores, 7.8 GB memory
2025-06-08 16:13:38,547 - ultra.main - INFO - ULTRA package initialization complete - ready for system creation
2025-06-08 16:13:38,547 - ultra.main - INFO - Use ultra.initialize_ultra() to create and start an ULTRA system
2025-06-08 16:13:38,548 - ultra.core_neural - INFO - Cleaning up Core Neural Architecture resources
2025-06-08 18:19:20,535 - ultra.main - INFO - Logging configuration complete
2025-06-08 18:19:20,541 - ultra.main - INFO - ULTRA system package initialized (version 1.0.0)
2025-06-08 18:19:20,544 - ultra.main - INFO - Components available: False
2025-06-08 18:19:20,545 - ultra.main - WARNING - Environment issue: Insufficient memory: 0.9 GB available, 4+ GB recommended
2025-06-08 18:19:20,548 - ultra.main - WARNING - Environment issue: Insufficient disk space: 3.6 GB available, 10+ GB recommended
2025-06-08 18:19:20,549 - ultra.main - INFO - System info: posix with 2 CPU cores, 7.8 GB memory
2025-06-08 18:19:20,552 - ultra.main - INFO - ULTRA package initialization complete - ready for system creation
2025-06-08 18:19:20,555 - ultra.main - INFO - Use ultra.initialize_ultra() to create and start an ULTRA system
2025-06-08 18:19:20,602 - ultra.main - INFO - Logging configuration complete
2025-06-08 18:19:20,610 - ultra.main - INFO - ULTRA system package initialized (version 1.0.0)
2025-06-08 18:19:20,613 - ultra.main - INFO - Components available: False
2025-06-08 18:19:20,613 - ultra.main - WARNING - Environment issue: Insufficient memory: 0.9 GB available, 4+ GB recommended
2025-06-08 18:19:20,616 - ultra.main - WARNING - Environment issue: Insufficient disk space: 3.6 GB available, 10+ GB recommended
2025-06-08 18:19:20,619 - ultra.main - INFO - System info: posix with 2 CPU cores, 7.8 GB memory
2025-06-08 18:19:20,620 - ultra.main - INFO - ULTRA package initialization complete - ready for system creation
2025-06-08 18:19:20,626 - ultra.main - INFO - Use ultra.initialize_ultra() to create and start an ULTRA system
2025-06-08 18:19:20,627 - ultra.core_neural - INFO - Cleaning up Core Neural Architecture resources
2025-06-08 18:19:22,178 - jax._src.xla_bridge - DEBUG - Clearing JAX backend caches.
2025-06-08 21:13:03,405 - ultra.main - INFO - Logging configuration complete
2025-06-08 21:13:03,414 - ultra.main - INFO - ULTRA system package initialized (version 1.0.0)
2025-06-08 21:13:03,414 - ultra.main - INFO - Components available: False
2025-06-08 21:13:03,414 - ultra.main - WARNING - Environment issue: Insufficient memory: 2.2 GB available, 4+ GB recommended
2025-06-08 21:13:03,415 - ultra.main - WARNING - Environment issue: Insufficient disk space: 3.6 GB available, 10+ GB recommended
2025-06-08 21:13:03,416 - ultra.main - INFO - System info: posix with 2 CPU cores, 7.8 GB memory
2025-06-08 21:13:03,418 - ultra.main - INFO - ULTRA package initialization complete - ready for system creation
2025-06-08 21:13:03,422 - ultra.main - INFO - Use ultra.initialize_ultra() to create and start an ULTRA system
2025-06-08 21:13:03,463 - ultra.main - INFO - Logging configuration complete
2025-06-08 21:13:03,463 - ultra.main - INFO - ULTRA system package initialized (version 1.0.0)
2025-06-08 21:13:03,465 - ultra.main - INFO - Components available: False
2025-06-08 21:13:03,470 - ultra.main - WARNING - Environment issue: Insufficient memory: 2.2 GB available, 4+ GB recommended
2025-06-08 21:13:03,472 - ultra.main - WARNING - Environment issue: Insufficient disk space: 3.6 GB available, 10+ GB recommended
2025-06-08 21:13:03,476 - ultra.main - INFO - System info: posix with 2 CPU cores, 7.8 GB memory
2025-06-08 21:13:03,477 - ultra.main - INFO - ULTRA package initialization complete - ready for system creation
2025-06-08 21:13:03,477 - ultra.main - INFO - Use ultra.initialize_ultra() to create and start an ULTRA system
2025-06-08 21:13:03,477 - ultra.core_neural - INFO - Cleaning up Core Neural Architecture resources
2025-06-08 21:13:04,274 - jax._src.xla_bridge - DEBUG - Clearing JAX backend caches.
2025-06-09 11:42:48,659 - ultra.main - INFO - Logging configuration complete
2025-06-09 11:42:48,661 - ultra.main - INFO - ULTRA system package initialized (version 1.0.0)
2025-06-09 11:42:48,661 - ultra.main - INFO - Components available: False
2025-06-09 11:42:48,661 - ultra.main - WARNING - Environment issue: Insufficient memory: 1.0 GB available, 4+ GB recommended
2025-06-09 11:42:48,661 - ultra.main - WARNING - Environment issue: Insufficient disk space: 3.6 GB available, 10+ GB recommended
2025-06-09 11:42:48,661 - ultra.main - INFO - System info: posix with 2 CPU cores, 7.8 GB memory
2025-06-09 11:42:48,662 - ultra.main - INFO - ULTRA package initialization complete - ready for system creation
2025-06-09 11:42:48,663 - ultra.main - INFO - Use ultra.initialize_ultra() to create and start an ULTRA system
2025-06-09 11:42:48,681 - ultra.main - INFO - Logging configuration complete
2025-06-09 11:42:48,681 - ultra.main - INFO - ULTRA system package initialized (version 1.0.0)
2025-06-09 11:42:48,682 - ultra.main - INFO - Components available: False
2025-06-09 11:42:48,683 - ultra.main - WARNING - Environment issue: Insufficient memory: 1.0 GB available, 4+ GB recommended
2025-06-09 11:42:48,683 - ultra.main - WARNING - Environment issue: Insufficient disk space: 3.6 GB available, 10+ GB recommended
2025-06-09 11:42:48,683 - ultra.main - INFO - System info: posix with 2 CPU cores, 7.8 GB memory
2025-06-09 11:42:48,684 - ultra.main - INFO - ULTRA package initialization complete - ready for system creation
2025-06-09 11:42:48,684 - ultra.main - INFO - Use ultra.initialize_ultra() to create and start an ULTRA system
2025-06-09 11:42:48,684 - ultra.core_neural - INFO - Cleaning up Core Neural Architecture resources
2025-06-10 15:36:25,034 - ultra.main - INFO - Logging configuration complete
2025-06-10 15:36:25,038 - ultra.main - INFO - ULTRA system package initialized (version 1.0.0)
2025-06-10 15:36:25,038 - ultra.main - INFO - Components available: False
2025-06-10 15:36:25,039 - ultra.main - WARNING - Environment issue: Insufficient memory: 3.9 GB available, 4+ GB recommended
2025-06-10 15:36:25,040 - ultra.main - WARNING - Environment issue: Insufficient disk space: 3.6 GB available, 10+ GB recommended
2025-06-10 15:36:25,040 - ultra.main - INFO - System info: posix with 2 CPU cores, 7.8 GB memory
2025-06-10 15:36:25,040 - ultra.main - INFO - ULTRA package initialization complete - ready for system creation
2025-06-10 15:36:25,041 - ultra.main - INFO - Use ultra.initialize_ultra() to create and start an ULTRA system
2025-06-10 15:36:25,052 - ultra.main - INFO - Logging configuration complete
2025-06-10 15:36:25,053 - ultra.main - INFO - ULTRA system package initialized (version 1.0.0)
2025-06-10 15:36:25,053 - ultra.main - INFO - Components available: False
2025-06-10 15:36:25,053 - ultra.main - WARNING - Environment issue: Insufficient memory: 3.9 GB available, 4+ GB recommended
2025-06-10 15:36:25,053 - ultra.main - WARNING - Environment issue: Insufficient disk space: 3.6 GB available, 10+ GB recommended
2025-06-10 15:36:25,056 - ultra.main - INFO - System info: posix with 2 CPU cores, 7.8 GB memory
2025-06-10 15:36:25,056 - ultra.main - INFO - ULTRA package initialization complete - ready for system creation
2025-06-10 15:36:25,057 - ultra.main - INFO - Use ultra.initialize_ultra() to create and start an ULTRA system
2025-06-10 15:36:25,057 - ultra.core_neural - INFO - Cleaning up Core Neural Architecture resources
2025-06-10 15:36:25,264 - jax._src.xla_bridge - DEBUG - Clearing JAX backend caches.
2025-06-11 17:55:25,794 - ultra.main - INFO - Logging configuration complete
2025-06-11 17:55:25,796 - ultra.main - INFO - ULTRA system package initialized (version 1.0.0)
2025-06-11 17:55:25,796 - ultra.main - INFO - Components available: False
2025-06-11 17:55:25,797 - ultra.main - WARNING - Environment issue: Insufficient disk space: 3.1 GB available, 10+ GB recommended
2025-06-11 17:55:25,797 - ultra.main - INFO - System info: posix with 2 CPU cores, 7.8 GB memory
2025-06-11 17:55:25,797 - ultra.main - INFO - ULTRA package initialization complete - ready for system creation
2025-06-11 17:55:25,797 - ultra.main - INFO - Use ultra.initialize_ultra() to create and start an ULTRA system
2025-06-11 17:55:25,812 - ultra.main - INFO - Logging configuration complete
2025-06-11 17:55:25,813 - ultra.main - INFO - ULTRA system package initialized (version 1.0.0)
2025-06-11 17:55:25,813 - ultra.main - INFO - Components available: False
2025-06-11 17:55:25,813 - ultra.main - WARNING - Environment issue: Insufficient disk space: 3.1 GB available, 10+ GB recommended
2025-06-11 17:55:25,814 - ultra.main - INFO - System info: posix with 2 CPU cores, 7.8 GB memory
2025-06-11 17:55:25,814 - ultra.main - INFO - ULTRA package initialization complete - ready for system creation
2025-06-11 17:55:25,814 - ultra.main - INFO - Use ultra.initialize_ultra() to create and start an ULTRA system
2025-06-11 17:55:25,815 - ultra.core_neural - INFO - Cleaning up Core Neural Architecture resources
2025-06-11 17:55:26,018 - jax._src.xla_bridge - DEBUG - Clearing JAX backend caches.
