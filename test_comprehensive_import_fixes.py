#!/usr/bin/env python3
"""
Comprehensive Import Test for ULTRA System
==========================================

This script tests all the import fixes we made to ensure the system
can now be imported without errors.
"""

import sys
import traceback
from pathlib import Path

# Add the ultra directory to Python path
ultra_dir = Path(__file__).parent / "ultra"
sys.path.insert(0, str(ultra_dir))

def test_import(module_name, description):
    """Test importing a module and report results."""
    print(f"\n{'='*60}")
    print(f"Testing: {description}")
    print(f"Module: {module_name}")
    print(f"{'='*60}")
    
    try:
        # Dynamic import
        module = __import__(module_name, fromlist=[''])
        print(f"✅ SUCCESS: {module_name} imported successfully")
        
        # Try to get some attributes to verify the module loaded properly
        attrs = [attr for attr in dir(module) if not attr.startswith('_')]
        print(f"📋 Available attributes: {len(attrs)} items")
        if attrs:
            print(f"   Sample attributes: {attrs[:5]}")
        
        return True
        
    except Exception as e:
        print(f"❌ FAILED: {module_name}")
        print(f"   Error: {str(e)}")
        print(f"   Traceback:")
        traceback.print_exc()
        return False

def main():
    """Run comprehensive import tests."""
    print("🚀 ULTRA System Import Verification")
    print("=" * 80)
    
    # List of modules to test (ordered by dependency)
    test_modules = [
        # Configuration first
        ("ultra.config", "Configuration Management System"),
        
        # Core neural architecture
        ("ultra.core_neural", "Core Neural Architecture"),
        
        # Integration bridges that we fixed
        ("ultra.integration.diffusion_neuromorphic_bridge", "Diffusion-Neuromorphic Bridge"),
        ("ultra.integration.consciousness_lattice_bridge", "Consciousness-Lattice Bridge"), 
        ("ultra.integration.neuro_symbolic_bridge", "Neuro-Symbolic Bridge"),
        ("ultra.integration.meta_cognitive_bridge", "Meta-Cognitive Bridge"),
        
        # Meta-cognitive components that we fixed
        ("ultra.meta_cognitive.chain_of_thought", "Chain of Thought Reasoning"),
        ("ultra.meta_cognitive.tree_of_thought", "Tree of Thought Exploration"),
        ("ultra.meta_cognitive.reasoning_graphs", "Reasoning Graphs"),
        ("ultra.meta_cognitive.self_critique", "Self-Critique Loop"),
        ("ultra.meta_cognitive.bias_detection", "Bias Detection"),
        ("ultra.meta_cognitive.meta_learning", "Meta-Learning Controller"),
        ("ultra.meta_cognitive", "Meta-Cognitive System (Main)"),
        
        # Utils
        ("ultra.utils", "Utility Functions"),
        
        # Other important components
        ("ultra.hyper_transformer", "Hyper-Transformer Architecture"),
        ("ultra.diffusion_reasoning", "Diffusion-Based Reasoning"),
        ("ultra.consciousness_lattice", "Consciousness Lattice"),
        ("ultra.neuro_symbolic", "Neuro-Symbolic Integration"),
        ("ultra.knowledge_management", "Knowledge Management"),
    ]
    
    # Run tests
    results = []
    for module_name, description in test_modules:
        success = test_import(module_name, description)
        results.append((module_name, success))
    
    # Summary
    print(f"\n{'='*80}")
    print("📊 IMPORT TEST SUMMARY")
    print(f"{'='*80}")
    
    successful = sum(1 for _, success in results if success)
    total = len(results)
    
    print(f"✅ Successful imports: {successful}/{total}")
    print(f"❌ Failed imports: {total - successful}/{total}")
    
    if successful == total:
        print("\n🎉 ALL IMPORTS SUCCESSFUL! 🎉")
        print("The ULTRA system import fixes are working correctly.")
    else:
        print(f"\n⚠️  {total - successful} imports still have issues.")
        print("Failed modules:")
        for module_name, success in results:
            if not success:
                print(f"   - {module_name}")
    
    print(f"\n{'='*80}")

if __name__ == "__main__":
    main()
