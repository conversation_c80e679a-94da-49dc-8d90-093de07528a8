2025-05-27 08:38:32,541 - __main__ - INFO - ULTRA Core Neural Architecture v1.0.0 loaded successfully
2025-05-27 08:38:32,544 - __main__ - INFO - Available backends: numpy, torch
2025-05-27 08:38:32,692 - __main__ - INFO - Optimal backend detected: torch
2025-05-27 08:38:32,692 - __main__ - INFO - GPU acceleration: disabled
2025-05-27 08:38:32,692 - __main__ - INFO - Initializing CoreNeuralInterface
2025-05-27 08:38:32,693 - __main__ - INFO - Starting Core Neural Architecture initialization
2025-05-27 08:38:32,694 - __main__ - INFO - Initializing NeuromorphicCore components
2025-05-27 08:38:32,694 - __main__ - INFO - Component neuromorphic_core initialized successfully
2025-05-27 08:38:32,694 - __main__ - INFO - Initializing NeuroplasticityEngine
2025-05-27 08:38:32,694 - __main__ - INFO - Component neuroplasticity_engine initialized successfully
2025-05-27 08:38:32,694 - __main__ - INFO - Initializing SynapticPruningModule
2025-05-27 08:38:32,695 - __main__ - INFO - Component synaptic_pruning initialized successfully
2025-05-27 08:38:32,695 - __main__ - INFO - Initializing NeuromodulationSystem
2025-05-27 08:38:32,695 - __main__ - INFO - Component neuromodulation initialized successfully
2025-05-27 08:38:32,695 - __main__ - INFO - Initializing BiologicalTimingCircuits
2025-05-27 08:38:32,695 - __main__ - INFO - Component biological_timing initialized successfully
2025-05-27 08:38:32,695 - __main__ - INFO - Core Neural Architecture initialization completed with status: success
2025-05-27 08:38:32,695 - __main__ - INFO - NeuromorphicCore initialized with GPU=False, Parallel=True
2025-05-27 08:38:32,696 - __main__ - INFO - NeuroplasticityEngine initialized
2025-05-27 08:38:32,696 - __main__ - INFO - SynapticPruningModule initialized
2025-05-27 08:38:32,696 - __main__ - INFO - NeuromodulationSystem initialized
2025-05-27 08:38:32,696 - __main__ - INFO - BiologicalTimingCircuits initialized
2025-05-27 08:38:32,697 - __main__ - INFO - NeuroplasticityEngine connected to NeuromorphicCore
2025-05-27 08:38:32,697 - __main__ - INFO - SynapticPruningModule connected to NeuromorphicCore
2025-05-27 08:38:32,697 - __main__ - INFO - NeuromodulationSystem connected to NeuromorphicCore
2025-05-27 08:38:32,697 - __main__ - INFO - BiologicalTimingCircuits connected to NeuromorphicCore
2025-05-27 08:38:32,697 - __main__ - INFO - BiologicalTimingCircuits connected to NeuromodulationSystem
2025-05-27 08:38:32,697 - __main__ - INFO - Core neural components initialized and connected successfully
2025-05-27 08:38:32,697 - __main__ - INFO - Default CoreNeuralInterface instance created successfully
2025-05-27 08:38:32,699 - __main__ - INFO - Cleaning up Core Neural Architecture resources
2025-05-28 07:34:19,653 - __main__ - INFO - ULTRA Core Neural Architecture v1.0.0 loaded successfully
2025-05-28 07:34:19,656 - __main__ - INFO - Available backends: numpy, torch, numba
2025-05-28 07:34:19,811 - __main__ - INFO - Optimal backend detected: torch
2025-05-28 07:34:19,812 - __main__ - INFO - GPU acceleration: disabled
2025-05-28 07:34:19,813 - __main__ - INFO - Initializing CoreNeuralInterface
2025-05-28 07:34:19,815 - __main__ - INFO - Starting Core Neural Architecture initialization
2025-05-28 07:34:19,818 - __main__ - INFO - Initializing NeuromorphicCore components
2025-05-28 07:34:19,818 - __main__ - INFO - Component neuromorphic_core initialized successfully
2025-05-28 07:34:19,818 - __main__ - INFO - Initializing NeuroplasticityEngine
2025-05-28 07:34:19,818 - __main__ - INFO - Component neuroplasticity_engine initialized successfully
2025-05-28 07:34:19,821 - __main__ - INFO - Initializing SynapticPruningModule
2025-05-28 07:34:19,822 - __main__ - INFO - Component synaptic_pruning initialized successfully
2025-05-28 07:34:19,822 - __main__ - INFO - Initializing NeuromodulationSystem
2025-05-28 07:34:19,825 - __main__ - INFO - Component neuromodulation initialized successfully
2025-05-28 07:34:19,825 - __main__ - INFO - Initializing BiologicalTimingCircuits
2025-05-28 07:34:19,826 - __main__ - INFO - Component biological_timing initialized successfully
2025-05-28 07:34:19,826 - __main__ - INFO - Core Neural Architecture initialization completed with status: success
2025-05-28 07:34:19,826 - __main__ - INFO - NeuromorphicCore initialized with GPU=False, Parallel=True
2025-05-28 07:34:19,826 - __main__ - INFO - NeuroplasticityEngine initialized
2025-05-28 07:34:19,826 - __main__ - INFO - SynapticPruningModule initialized
2025-05-28 07:34:19,827 - __main__ - INFO - NeuromodulationSystem initialized
2025-05-28 07:34:19,830 - __main__ - INFO - BiologicalTimingCircuits initialized
2025-05-28 07:34:19,831 - __main__ - INFO - NeuroplasticityEngine connected to NeuromorphicCore
2025-05-28 07:34:19,831 - __main__ - INFO - SynapticPruningModule connected to NeuromorphicCore
2025-05-28 07:34:19,831 - __main__ - INFO - NeuromodulationSystem connected to NeuromorphicCore
2025-05-28 07:34:19,831 - __main__ - INFO - BiologicalTimingCircuits connected to NeuromorphicCore
2025-05-28 07:34:19,832 - __main__ - INFO - BiologicalTimingCircuits connected to NeuromodulationSystem
2025-05-28 07:34:19,832 - __main__ - INFO - Core neural components initialized and connected successfully
2025-05-28 07:34:19,832 - __main__ - INFO - Default CoreNeuralInterface instance created successfully
2025-05-28 07:34:19,836 - __main__ - INFO - Cleaning up Core Neural Architecture resources
2025-05-28 15:35:48,736 - __main__ - INFO - ULTRA Core Neural Architecture v1.0.0 loaded successfully
2025-05-28 15:35:48,755 - __main__ - INFO - Available backends: numpy, torch, numba
2025-05-28 15:35:48,946 - __main__ - INFO - Optimal backend detected: torch
2025-05-28 15:35:48,947 - __main__ - INFO - GPU acceleration: disabled
2025-05-28 15:35:48,947 - __main__ - INFO - Initializing CoreNeuralInterface
2025-05-28 15:35:48,951 - __main__ - INFO - Starting Core Neural Architecture initialization
2025-05-28 15:35:48,951 - __main__ - INFO - Initializing NeuromorphicCore components
2025-05-28 15:35:48,951 - __main__ - INFO - Component neuromorphic_core initialized successfully
2025-05-28 15:35:48,953 - __main__ - INFO - Initializing NeuroplasticityEngine
2025-05-28 15:35:48,953 - __main__ - INFO - Component neuroplasticity_engine initialized successfully
2025-05-28 15:35:48,953 - __main__ - INFO - Initializing SynapticPruningModule
2025-05-28 15:35:48,954 - __main__ - INFO - Component synaptic_pruning initialized successfully
2025-05-28 15:35:48,954 - __main__ - INFO - Initializing NeuromodulationSystem
2025-05-28 15:35:48,955 - __main__ - INFO - Component neuromodulation initialized successfully
2025-05-28 15:35:48,955 - __main__ - INFO - Initializing BiologicalTimingCircuits
2025-05-28 15:35:48,955 - __main__ - INFO - Component biological_timing initialized successfully
2025-05-28 15:35:48,957 - __main__ - INFO - Core Neural Architecture initialization completed with status: success
2025-05-28 15:35:48,957 - __main__ - INFO - NeuromorphicCore initialized with GPU=False, Parallel=True
2025-05-28 15:35:48,960 - __main__ - INFO - NeuroplasticityEngine initialized
2025-05-28 15:35:48,960 - __main__ - INFO - SynapticPruningModule initialized
2025-05-28 15:35:48,962 - __main__ - INFO - NeuromodulationSystem initialized
2025-05-28 15:35:48,962 - __main__ - INFO - BiologicalTimingCircuits initialized
2025-05-28 15:35:48,964 - __main__ - INFO - NeuroplasticityEngine connected to NeuromorphicCore
2025-05-28 15:35:48,964 - __main__ - INFO - SynapticPruningModule connected to NeuromorphicCore
2025-05-28 15:35:48,964 - __main__ - INFO - NeuromodulationSystem connected to NeuromorphicCore
2025-05-28 15:35:48,965 - __main__ - INFO - BiologicalTimingCircuits connected to NeuromorphicCore
2025-05-28 15:35:48,965 - __main__ - INFO - BiologicalTimingCircuits connected to NeuromodulationSystem
2025-05-28 15:35:48,965 - __main__ - INFO - Core neural components initialized and connected successfully
2025-05-28 15:35:48,965 - __main__ - INFO - Default CoreNeuralInterface instance created successfully
2025-05-28 15:35:48,965 - __main__ - INFO - Cleaning up Core Neural Architecture resources
2025-05-29 11:27:23,530 - __main__ - INFO - ULTRA Core Neural Architecture v1.0.0 loaded successfully
2025-05-29 11:27:23,530 - __main__ - INFO - Available backends: numpy, torch, numba
2025-05-29 11:27:23,551 - __main__ - INFO - Optimal backend detected: torch
2025-05-29 11:27:23,552 - __main__ - INFO - GPU acceleration: disabled
2025-05-29 11:27:23,552 - __main__ - INFO - Initializing CoreNeuralInterface
2025-05-29 11:27:23,553 - __main__ - INFO - Starting Core Neural Architecture initialization
2025-05-29 11:27:23,553 - __main__ - INFO - Initializing NeuromorphicCore components
2025-05-29 11:27:23,553 - __main__ - INFO - Component neuromorphic_core initialized successfully
2025-05-29 11:27:23,553 - __main__ - INFO - Initializing NeuroplasticityEngine
2025-05-29 11:27:23,553 - __main__ - INFO - Component neuroplasticity_engine initialized successfully
2025-05-29 11:27:23,553 - __main__ - INFO - Initializing SynapticPruningModule
2025-05-29 11:27:23,553 - __main__ - INFO - Component synaptic_pruning initialized successfully
2025-05-29 11:27:23,553 - __main__ - INFO - Initializing NeuromodulationSystem
2025-05-29 11:27:23,553 - __main__ - INFO - Component neuromodulation initialized successfully
2025-05-29 11:27:23,553 - __main__ - INFO - Initializing BiologicalTimingCircuits
2025-05-29 11:27:23,553 - __main__ - INFO - Component biological_timing initialized successfully
2025-05-29 11:27:23,553 - __main__ - INFO - Core Neural Architecture initialization completed with status: success
2025-05-29 11:27:23,554 - __main__ - INFO - NeuromorphicCore initialized with GPU=False, Parallel=True
2025-05-29 11:27:23,554 - __main__ - INFO - NeuroplasticityEngine initialized
2025-05-29 11:27:23,554 - __main__ - INFO - SynapticPruningModule initialized
2025-05-29 11:27:23,554 - __main__ - INFO - NeuromodulationSystem initialized
2025-05-29 11:27:23,554 - __main__ - INFO - BiologicalTimingCircuits initialized
2025-05-29 11:27:23,554 - __main__ - INFO - NeuroplasticityEngine connected to NeuromorphicCore
2025-05-29 11:27:23,554 - __main__ - INFO - SynapticPruningModule connected to NeuromorphicCore
2025-05-29 11:27:23,554 - __main__ - INFO - NeuromodulationSystem connected to NeuromorphicCore
2025-05-29 11:27:23,554 - __main__ - INFO - BiologicalTimingCircuits connected to NeuromorphicCore
2025-05-29 11:27:23,554 - __main__ - INFO - BiologicalTimingCircuits connected to NeuromodulationSystem
2025-05-29 11:27:23,555 - __main__ - INFO - Core neural components initialized and connected successfully
2025-05-29 11:27:23,555 - __main__ - INFO - Default CoreNeuralInterface instance created successfully
2025-05-29 11:27:23,555 - __main__ - INFO - Cleaning up Core Neural Architecture resources
2025-06-10 15:54:07,989 - __main__ - INFO - ULTRA Core Neural Architecture v1.0.0 loaded successfully
2025-06-10 15:54:07,991 - __main__ - INFO - Available backends: numpy, jax, torch, numba
2025-06-10 15:54:08,023 - jax._src.xla_bridge - INFO - Unable to initialize backend 'tpu': INTERNAL: Failed to open libtpu.so: libtpu.so: cannot open shared object file: No such file or directory
2025-06-10 15:54:08,041 - __main__ - INFO - Optimal backend detected: jax
2025-06-10 15:54:08,041 - __main__ - INFO - GPU acceleration: disabled
2025-06-10 15:54:08,042 - __main__ - INFO - Initializing CoreNeuralInterface
2025-06-10 15:54:08,043 - __main__ - INFO - Starting Core Neural Architecture initialization
2025-06-10 15:54:08,043 - __main__ - INFO - Initializing NeuromorphicCore components
2025-06-10 15:54:08,043 - __main__ - INFO - Component neuromorphic_core initialized successfully
2025-06-10 15:54:08,043 - __main__ - INFO - Initializing NeuroplasticityEngine
2025-06-10 15:54:08,043 - __main__ - INFO - Component neuroplasticity_engine initialized successfully
2025-06-10 15:54:08,043 - __main__ - INFO - Initializing SynapticPruningModule
2025-06-10 15:54:08,043 - __main__ - INFO - Component synaptic_pruning initialized successfully
2025-06-10 15:54:08,043 - __main__ - INFO - Initializing NeuromodulationSystem
2025-06-10 15:54:08,043 - __main__ - INFO - Component neuromodulation initialized successfully
2025-06-10 15:54:08,043 - __main__ - INFO - Initializing BiologicalTimingCircuits
2025-06-10 15:54:08,043 - __main__ - INFO - Component biological_timing initialized successfully
2025-06-10 15:54:08,043 - __main__ - INFO - Core Neural Architecture initialization completed with status: success
2025-06-10 15:54:08,044 - __main__ - INFO - NeuromorphicCore initialized with GPU=False, Parallel=True
2025-06-10 15:54:08,044 - __main__ - INFO - NeuroplasticityEngine initialized
2025-06-10 15:54:08,044 - __main__ - INFO - SynapticPruningModule initialized
2025-06-10 15:54:08,044 - __main__ - INFO - NeuromodulationSystem initialized
2025-06-10 15:54:08,044 - __main__ - INFO - BiologicalTimingCircuits initialized
2025-06-10 15:54:08,044 - __main__ - INFO - NeuroplasticityEngine connected to NeuromorphicCore
2025-06-10 15:54:08,044 - __main__ - INFO - SynapticPruningModule connected to NeuromorphicCore
2025-06-10 15:54:08,044 - __main__ - INFO - NeuromodulationSystem connected to NeuromorphicCore
2025-06-10 15:54:08,044 - __main__ - INFO - BiologicalTimingCircuits connected to NeuromorphicCore
2025-06-10 15:54:08,044 - __main__ - INFO - BiologicalTimingCircuits connected to NeuromodulationSystem
2025-06-10 15:54:08,044 - __main__ - INFO - Core neural components initialized and connected successfully
2025-06-10 15:54:08,044 - __main__ - INFO - Default CoreNeuralInterface instance created successfully
2025-06-10 15:54:08,045 - __main__ - INFO - Cleaning up Core Neural Architecture resources
2025-06-20 08:00:14,201 - __main__ - INFO - ULTRA Core Neural Architecture v1.0.0 loaded successfully
2025-06-20 08:00:14,203 - __main__ - INFO - Available backends: numpy, jax, torch, numba
2025-06-20 08:00:14,377 - jax._src.xla_bridge - INFO - Unable to initialize backend 'tpu': INTERNAL: Failed to open libtpu.so: libtpu.so: cannot open shared object file: No such file or directory
2025-06-20 08:00:14,407 - __main__ - INFO - Optimal backend detected: jax
2025-06-20 08:00:14,408 - __main__ - INFO - GPU acceleration: disabled
2025-06-20 08:00:14,408 - __main__ - INFO - Initializing CoreNeuralInterface
2025-06-20 08:00:14,409 - __main__ - INFO - Starting Core Neural Architecture initialization
2025-06-20 08:00:14,409 - __main__ - INFO - Initializing NeuromorphicCore components
2025-06-20 08:00:14,409 - __main__ - INFO - Component neuromorphic_core initialized successfully
2025-06-20 08:00:14,409 - __main__ - INFO - Initializing NeuroplasticityEngine
2025-06-20 08:00:14,410 - __main__ - INFO - Component neuroplasticity_engine initialized successfully
2025-06-20 08:00:14,410 - __main__ - INFO - Initializing SynapticPruningModule
2025-06-20 08:00:14,410 - __main__ - INFO - Component synaptic_pruning initialized successfully
2025-06-20 08:00:14,410 - __main__ - INFO - Initializing NeuromodulationSystem
2025-06-20 08:00:14,410 - __main__ - INFO - Component neuromodulation initialized successfully
2025-06-20 08:00:14,410 - __main__ - INFO - Initializing BiologicalTimingCircuits
2025-06-20 08:00:14,411 - __main__ - INFO - Component biological_timing initialized successfully
2025-06-20 08:00:14,411 - __main__ - INFO - Core Neural Architecture initialization completed with status: success
2025-06-20 08:00:14,411 - __main__ - INFO - NeuromorphicCore initialized with GPU=False, Parallel=True
2025-06-20 08:00:14,411 - __main__ - INFO - NeuroplasticityEngine initialized
2025-06-20 08:00:14,411 - __main__ - INFO - SynapticPruningModule initialized
2025-06-20 08:00:14,412 - __main__ - INFO - NeuromodulationSystem initialized
2025-06-20 08:00:14,412 - __main__ - INFO - BiologicalTimingCircuits initialized
2025-06-20 08:00:14,412 - __main__ - INFO - NeuroplasticityEngine connected to NeuromorphicCore
2025-06-20 08:00:14,412 - __main__ - INFO - SynapticPruningModule connected to NeuromorphicCore
2025-06-20 08:00:14,412 - __main__ - INFO - NeuromodulationSystem connected to NeuromorphicCore
2025-06-20 08:00:14,412 - __main__ - INFO - BiologicalTimingCircuits connected to NeuromorphicCore
2025-06-20 08:00:14,413 - __main__ - INFO - BiologicalTimingCircuits connected to NeuromodulationSystem
2025-06-20 08:00:14,413 - __main__ - INFO - Core neural components initialized and connected successfully
2025-06-20 08:00:14,413 - __main__ - INFO - Default CoreNeuralInterface instance created successfully
2025-06-20 08:00:14,414 - __main__ - INFO - Cleaning up Core Neural Architecture resources
2025-06-20 08:32:05,634 - __main__ - INFO - ULTRA Core Neural Architecture v1.0.0 loaded successfully
2025-06-20 08:32:05,638 - __main__ - INFO - Available backends: numpy, jax, torch, numba
2025-06-20 08:32:05,793 - jax._src.xla_bridge - INFO - Unable to initialize backend 'tpu': INTERNAL: Failed to open libtpu.so: libtpu.so: cannot open shared object file: No such file or directory
2025-06-20 08:32:05,813 - __main__ - INFO - Optimal backend detected: jax
2025-06-20 08:32:05,813 - __main__ - INFO - GPU acceleration: disabled
2025-06-20 08:32:05,813 - __main__ - INFO - Initializing CoreNeuralInterface
2025-06-20 08:32:05,814 - __main__ - INFO - Starting Core Neural Architecture initialization
2025-06-20 08:32:05,814 - __main__ - INFO - Initializing NeuromorphicCore components
2025-06-20 08:32:05,814 - __main__ - INFO - Component neuromorphic_core initialized successfully
2025-06-20 08:32:05,814 - __main__ - INFO - Initializing NeuroplasticityEngine
2025-06-20 08:32:05,814 - __main__ - INFO - Component neuroplasticity_engine initialized successfully
2025-06-20 08:32:05,815 - __main__ - INFO - Initializing SynapticPruningModule
2025-06-20 08:32:05,815 - __main__ - INFO - Component synaptic_pruning initialized successfully
2025-06-20 08:32:05,815 - __main__ - INFO - Initializing NeuromodulationSystem
2025-06-20 08:32:05,815 - __main__ - INFO - Component neuromodulation initialized successfully
2025-06-20 08:32:05,815 - __main__ - INFO - Initializing BiologicalTimingCircuits
2025-06-20 08:32:05,815 - __main__ - INFO - Component biological_timing initialized successfully
2025-06-20 08:32:05,815 - __main__ - INFO - Core Neural Architecture initialization completed with status: success
2025-06-20 08:32:05,815 - __main__ - INFO - NeuromorphicCore initialized with GPU=False, Parallel=True
2025-06-20 08:32:05,815 - __main__ - INFO - NeuroplasticityEngine initialized
2025-06-20 08:32:05,815 - __main__ - INFO - SynapticPruningModule initialized
2025-06-20 08:32:05,816 - __main__ - INFO - NeuromodulationSystem initialized
2025-06-20 08:32:05,816 - __main__ - INFO - BiologicalTimingCircuits initialized
2025-06-20 08:32:05,816 - __main__ - INFO - NeuroplasticityEngine connected to NeuromorphicCore
2025-06-20 08:32:05,816 - __main__ - INFO - SynapticPruningModule connected to NeuromorphicCore
2025-06-20 08:32:05,816 - __main__ - INFO - NeuromodulationSystem connected to NeuromorphicCore
2025-06-20 08:32:05,816 - __main__ - INFO - BiologicalTimingCircuits connected to NeuromorphicCore
2025-06-20 08:32:05,816 - __main__ - INFO - BiologicalTimingCircuits connected to NeuromodulationSystem
2025-06-20 08:32:05,816 - __main__ - INFO - Core neural components initialized and connected successfully
2025-06-20 08:32:05,817 - __main__ - INFO - Default CoreNeuralInterface instance created successfully
2025-06-20 08:32:05,817 - __main__ - INFO - Cleaning up Core Neural Architecture resources
2025-06-20 08:34:53,128 - __main__ - INFO - ULTRA Core Neural Architecture v1.0.0 loaded successfully
2025-06-20 08:34:53,128 - __main__ - INFO - Available backends: numpy, jax, torch, numba
2025-06-20 08:34:53,254 - jax._src.xla_bridge - INFO - Unable to initialize backend 'tpu': INTERNAL: Failed to open libtpu.so: libtpu.so: cannot open shared object file: No such file or directory
2025-06-20 08:34:53,272 - __main__ - INFO - Optimal backend detected: jax
2025-06-20 08:34:53,272 - __main__ - INFO - GPU acceleration: disabled
2025-06-20 08:34:53,272 - __main__ - INFO - Initializing CoreNeuralInterface
2025-06-20 08:34:53,273 - __main__ - INFO - Starting Core Neural Architecture initialization
2025-06-20 08:34:53,273 - __main__ - INFO - Initializing NeuromorphicCore components
2025-06-20 08:34:53,273 - __main__ - INFO - Component neuromorphic_core initialized successfully
2025-06-20 08:34:53,273 - __main__ - INFO - Initializing NeuroplasticityEngine
2025-06-20 08:34:53,273 - __main__ - INFO - Component neuroplasticity_engine initialized successfully
2025-06-20 08:34:53,274 - __main__ - INFO - Initializing SynapticPruningModule
2025-06-20 08:34:53,274 - __main__ - INFO - Component synaptic_pruning initialized successfully
2025-06-20 08:34:53,274 - __main__ - INFO - Initializing NeuromodulationSystem
2025-06-20 08:34:53,274 - __main__ - INFO - Component neuromodulation initialized successfully
2025-06-20 08:34:53,274 - __main__ - INFO - Initializing BiologicalTimingCircuits
2025-06-20 08:34:53,274 - __main__ - INFO - Component biological_timing initialized successfully
2025-06-20 08:34:53,274 - __main__ - INFO - Core Neural Architecture initialization completed with status: success
2025-06-20 08:34:53,274 - __main__ - INFO - NeuromorphicCore initialized with GPU=False, Parallel=True
2025-06-20 08:34:53,275 - __main__ - INFO - NeuroplasticityEngine initialized
2025-06-20 08:34:53,275 - __main__ - INFO - SynapticPruningModule initialized
2025-06-20 08:34:53,275 - __main__ - INFO - NeuromodulationSystem initialized
2025-06-20 08:34:53,275 - __main__ - INFO - BiologicalTimingCircuits initialized
2025-06-20 08:34:53,275 - __main__ - INFO - NeuroplasticityEngine connected to NeuromorphicCore
2025-06-20 08:34:53,275 - __main__ - INFO - SynapticPruningModule connected to NeuromorphicCore
2025-06-20 08:34:53,275 - __main__ - INFO - NeuromodulationSystem connected to NeuromorphicCore
2025-06-20 08:34:53,275 - __main__ - INFO - BiologicalTimingCircuits connected to NeuromorphicCore
2025-06-20 08:34:53,275 - __main__ - INFO - BiologicalTimingCircuits connected to NeuromodulationSystem
2025-06-20 08:34:53,275 - __main__ - INFO - Core neural components initialized and connected successfully
2025-06-20 08:34:53,276 - __main__ - INFO - Default CoreNeuralInterface instance created successfully
2025-06-20 08:34:53,276 - __main__ - INFO - Cleaning up Core Neural Architecture resources
2025-06-20 11:16:08,921 - __main__ - INFO - ULTRA Core Neural Architecture v1.0.0 loaded successfully
2025-06-20 11:16:08,924 - __main__ - INFO - Available backends: numpy, jax, torch, numba
2025-06-20 11:16:09,113 - jax._src.xla_bridge - INFO - Unable to initialize backend 'tpu': INTERNAL: Failed to open libtpu.so: libtpu.so: cannot open shared object file: No such file or directory
2025-06-20 11:16:09,132 - __main__ - INFO - Optimal backend detected: jax
2025-06-20 11:16:09,132 - __main__ - INFO - GPU acceleration: disabled
2025-06-20 11:16:09,133 - __main__ - INFO - Initializing CoreNeuralInterface
2025-06-20 11:16:09,133 - __main__ - INFO - Starting Core Neural Architecture initialization
2025-06-20 11:16:09,134 - __main__ - INFO - Initializing NeuromorphicCore components
2025-06-20 11:16:09,134 - __main__ - INFO - Component neuromorphic_core initialized successfully
2025-06-20 11:16:09,134 - __main__ - INFO - Initializing NeuroplasticityEngine
2025-06-20 11:16:09,135 - __main__ - INFO - Component neuroplasticity_engine initialized successfully
2025-06-20 11:16:09,135 - __main__ - INFO - Initializing SynapticPruningModule
2025-06-20 11:16:09,135 - __main__ - INFO - Component synaptic_pruning initialized successfully
2025-06-20 11:16:09,135 - __main__ - INFO - Initializing NeuromodulationSystem
2025-06-20 11:16:09,135 - __main__ - INFO - Component neuromodulation initialized successfully
2025-06-20 11:16:09,135 - __main__ - INFO - Initializing BiologicalTimingCircuits
2025-06-20 11:16:09,136 - __main__ - INFO - Component biological_timing initialized successfully
2025-06-20 11:16:09,136 - __main__ - INFO - Core Neural Architecture initialization completed with status: success
2025-06-20 11:16:09,136 - __main__ - ERROR - Failed to initialize components: 'dict' object has no attribute 'enable_threading'
2025-06-20 11:16:09,136 - __main__ - WARNING - Failed to create default interface instance: 'dict' object has no attribute 'enable_threading'
2025-06-20 17:54:45,726 - __main__ - INFO - ULTRA Core Neural Architecture v1.0.0 loaded successfully
2025-06-20 17:54:45,728 - __main__ - INFO - Available backends: numpy, jax, torch, numba
2025-06-20 17:54:45,996 - jax._src.xla_bridge - INFO - Unable to initialize backend 'tpu': INTERNAL: Failed to open libtpu.so: libtpu.so: cannot open shared object file: No such file or directory
2025-06-20 17:54:46,022 - __main__ - INFO - Optimal backend detected: jax
2025-06-20 17:54:46,025 - __main__ - INFO - GPU acceleration: disabled
2025-06-20 17:54:46,025 - __main__ - INFO - Initializing CoreNeuralInterface
2025-06-20 17:54:46,026 - __main__ - ERROR - Failed to initialize components: 'dict' object has no attribute 'enable_threading'
2025-06-20 17:54:46,027 - __main__ - ERROR - Failed to initialize core neural architecture: 'dict' object has no attribute 'enable_threading'
2025-06-20 17:54:46,030 - __main__ - WARNING - Failed to create default interface instance: Core neural architecture initialization failed: 'dict' object has no attribute 'enable_threading'
2025-06-20 17:54:46,030 - __main__ - INFO - ULTRA Core Neural Architecture - Fixed implementation loaded
2025-06-20 17:54:46,031 - __main__ - INFO - Cleaned up Core Neural Architecture resources
2025-06-21 14:43:50,220 - __main__ - INFO - ULTRA Core Neural Architecture v1.0.0 loaded successfully
2025-06-21 14:43:50,232 - __main__ - INFO - Available backends: numpy, jax, torch, numba
2025-06-21 14:43:50,839 - jax._src.xla_bridge - INFO - Unable to initialize backend 'tpu': INTERNAL: Failed to open libtpu.so: libtpu.so: cannot open shared object file: No such file or directory
2025-06-21 14:43:50,890 - __main__ - INFO - Optimal backend detected: jax
2025-06-21 14:43:50,890 - __main__ - INFO - GPU acceleration: disabled
2025-06-21 14:43:50,890 - __main__ - INFO - Initializing CoreNeuralInterface
2025-06-21 14:43:50,891 - __main__ - INFO - Initializing NeuromorphicCore...
2025-06-21 14:43:50,892 - __main__ - INFO - NeuromorphicCore initialized with GPU=False, Parallel=True
2025-06-21 14:43:50,893 - __main__ - INFO - Initializing NeuroplasticityEngine...
2025-06-21 14:43:50,893 - __main__ - INFO - NeuroplasticityEngine initialized
2025-06-21 14:43:50,894 - __main__ - INFO - Initializing SynapticPruningModule...
2025-06-21 14:43:50,894 - __main__ - INFO - SynapticPruningModule initialized
2025-06-21 14:43:50,894 - __main__ - INFO - Initializing NeuromodulationSystem...
2025-06-21 14:43:50,895 - __main__ - INFO - NeuromodulationSystem initialized
2025-06-21 14:43:50,895 - __main__ - INFO - Initializing BiologicalTimingCircuits...
2025-06-21 14:43:50,896 - __main__ - INFO - BiologicalTimingCircuits initialized with fixed initialization order
2025-06-21 14:43:50,897 - __main__ - INFO - Connecting components...
2025-06-21 14:43:50,897 - __main__ - INFO - NeuroplasticityEngine connected to NeuromorphicCore
2025-06-21 14:43:50,897 - __main__ - INFO - SynapticPruningModule connected to NeuromorphicCore
2025-06-21 14:43:50,898 - __main__ - INFO - NeuromodulationSystem connected to NeuromorphicCore
2025-06-21 14:43:50,898 - __main__ - INFO - BiologicalTimingCircuits connected to NeuromorphicCore
2025-06-21 14:43:50,899 - __main__ - INFO - BiologicalTimingCircuits connected to NeuromodulationSystem
2025-06-21 14:43:50,899 - __main__ - INFO - Core neural components initialized and connected successfully
2025-06-21 14:43:50,900 - __main__ - INFO - Core neural components initialized successfully
2025-06-21 14:43:50,900 - __main__ - INFO - Default CoreNeuralInterface instance created successfully
2025-06-21 14:43:50,900 - __main__ - INFO - ULTRA Core Neural Architecture - Fixed implementation loaded
2025-06-21 14:43:50,902 - __main__ - INFO - Cleaned up Core Neural Architecture resources
