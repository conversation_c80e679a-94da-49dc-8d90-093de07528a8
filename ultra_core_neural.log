2025-05-27 08:38:32,541 - __main__ - INFO - ULTRA Core Neural Architecture v1.0.0 loaded successfully
2025-05-27 08:38:32,544 - __main__ - INFO - Available backends: numpy, torch
2025-05-27 08:38:32,692 - __main__ - INFO - Optimal backend detected: torch
2025-05-27 08:38:32,692 - __main__ - INFO - GPU acceleration: disabled
2025-05-27 08:38:32,692 - __main__ - INFO - Initializing CoreNeuralInterface
2025-05-27 08:38:32,693 - __main__ - INFO - Starting Core Neural Architecture initialization
2025-05-27 08:38:32,694 - __main__ - INFO - Initializing NeuromorphicCore components
2025-05-27 08:38:32,694 - __main__ - INFO - Component neuromorphic_core initialized successfully
2025-05-27 08:38:32,694 - __main__ - INFO - Initializing NeuroplasticityEngine
2025-05-27 08:38:32,694 - __main__ - INFO - Component neuroplasticity_engine initialized successfully
2025-05-27 08:38:32,694 - __main__ - INFO - Initializing SynapticPruningModule
2025-05-27 08:38:32,695 - __main__ - INFO - Component synaptic_pruning initialized successfully
2025-05-27 08:38:32,695 - __main__ - INFO - Initializing NeuromodulationSystem
2025-05-27 08:38:32,695 - __main__ - INFO - Component neuromodulation initialized successfully
2025-05-27 08:38:32,695 - __main__ - INFO - Initializing BiologicalTimingCircuits
2025-05-27 08:38:32,695 - __main__ - INFO - Component biological_timing initialized successfully
2025-05-27 08:38:32,695 - __main__ - INFO - Core Neural Architecture initialization completed with status: success
2025-05-27 08:38:32,695 - __main__ - INFO - NeuromorphicCore initialized with GPU=False, Parallel=True
2025-05-27 08:38:32,696 - __main__ - INFO - NeuroplasticityEngine initialized
2025-05-27 08:38:32,696 - __main__ - INFO - SynapticPruningModule initialized
2025-05-27 08:38:32,696 - __main__ - INFO - NeuromodulationSystem initialized
2025-05-27 08:38:32,696 - __main__ - INFO - BiologicalTimingCircuits initialized
2025-05-27 08:38:32,697 - __main__ - INFO - NeuroplasticityEngine connected to NeuromorphicCore
2025-05-27 08:38:32,697 - __main__ - INFO - SynapticPruningModule connected to NeuromorphicCore
2025-05-27 08:38:32,697 - __main__ - INFO - NeuromodulationSystem connected to NeuromorphicCore
2025-05-27 08:38:32,697 - __main__ - INFO - BiologicalTimingCircuits connected to NeuromorphicCore
2025-05-27 08:38:32,697 - __main__ - INFO - BiologicalTimingCircuits connected to NeuromodulationSystem
2025-05-27 08:38:32,697 - __main__ - INFO - Core neural components initialized and connected successfully
2025-05-27 08:38:32,697 - __main__ - INFO - Default CoreNeuralInterface instance created successfully
2025-05-27 08:38:32,699 - __main__ - INFO - Cleaning up Core Neural Architecture resources
2025-05-28 07:34:19,653 - __main__ - INFO - ULTRA Core Neural Architecture v1.0.0 loaded successfully
2025-05-28 07:34:19,656 - __main__ - INFO - Available backends: numpy, torch, numba
2025-05-28 07:34:19,811 - __main__ - INFO - Optimal backend detected: torch
2025-05-28 07:34:19,812 - __main__ - INFO - GPU acceleration: disabled
2025-05-28 07:34:19,813 - __main__ - INFO - Initializing CoreNeuralInterface
2025-05-28 07:34:19,815 - __main__ - INFO - Starting Core Neural Architecture initialization
2025-05-28 07:34:19,818 - __main__ - INFO - Initializing NeuromorphicCore components
2025-05-28 07:34:19,818 - __main__ - INFO - Component neuromorphic_core initialized successfully
2025-05-28 07:34:19,818 - __main__ - INFO - Initializing NeuroplasticityEngine
2025-05-28 07:34:19,818 - __main__ - INFO - Component neuroplasticity_engine initialized successfully
2025-05-28 07:34:19,821 - __main__ - INFO - Initializing SynapticPruningModule
2025-05-28 07:34:19,822 - __main__ - INFO - Component synaptic_pruning initialized successfully
2025-05-28 07:34:19,822 - __main__ - INFO - Initializing NeuromodulationSystem
2025-05-28 07:34:19,825 - __main__ - INFO - Component neuromodulation initialized successfully
2025-05-28 07:34:19,825 - __main__ - INFO - Initializing BiologicalTimingCircuits
2025-05-28 07:34:19,826 - __main__ - INFO - Component biological_timing initialized successfully
2025-05-28 07:34:19,826 - __main__ - INFO - Core Neural Architecture initialization completed with status: success
2025-05-28 07:34:19,826 - __main__ - INFO - NeuromorphicCore initialized with GPU=False, Parallel=True
2025-05-28 07:34:19,826 - __main__ - INFO - NeuroplasticityEngine initialized
2025-05-28 07:34:19,826 - __main__ - INFO - SynapticPruningModule initialized
2025-05-28 07:34:19,827 - __main__ - INFO - NeuromodulationSystem initialized
2025-05-28 07:34:19,830 - __main__ - INFO - BiologicalTimingCircuits initialized
2025-05-28 07:34:19,831 - __main__ - INFO - NeuroplasticityEngine connected to NeuromorphicCore
2025-05-28 07:34:19,831 - __main__ - INFO - SynapticPruningModule connected to NeuromorphicCore
2025-05-28 07:34:19,831 - __main__ - INFO - NeuromodulationSystem connected to NeuromorphicCore
2025-05-28 07:34:19,831 - __main__ - INFO - BiologicalTimingCircuits connected to NeuromorphicCore
2025-05-28 07:34:19,832 - __main__ - INFO - BiologicalTimingCircuits connected to NeuromodulationSystem
2025-05-28 07:34:19,832 - __main__ - INFO - Core neural components initialized and connected successfully
2025-05-28 07:34:19,832 - __main__ - INFO - Default CoreNeuralInterface instance created successfully
2025-05-28 07:34:19,836 - __main__ - INFO - Cleaning up Core Neural Architecture resources
2025-05-28 15:35:48,736 - __main__ - INFO - ULTRA Core Neural Architecture v1.0.0 loaded successfully
2025-05-28 15:35:48,755 - __main__ - INFO - Available backends: numpy, torch, numba
2025-05-28 15:35:48,946 - __main__ - INFO - Optimal backend detected: torch
2025-05-28 15:35:48,947 - __main__ - INFO - GPU acceleration: disabled
2025-05-28 15:35:48,947 - __main__ - INFO - Initializing CoreNeuralInterface
2025-05-28 15:35:48,951 - __main__ - INFO - Starting Core Neural Architecture initialization
2025-05-28 15:35:48,951 - __main__ - INFO - Initializing NeuromorphicCore components
2025-05-28 15:35:48,951 - __main__ - INFO - Component neuromorphic_core initialized successfully
2025-05-28 15:35:48,953 - __main__ - INFO - Initializing NeuroplasticityEngine
2025-05-28 15:35:48,953 - __main__ - INFO - Component neuroplasticity_engine initialized successfully
2025-05-28 15:35:48,953 - __main__ - INFO - Initializing SynapticPruningModule
2025-05-28 15:35:48,954 - __main__ - INFO - Component synaptic_pruning initialized successfully
2025-05-28 15:35:48,954 - __main__ - INFO - Initializing NeuromodulationSystem
2025-05-28 15:35:48,955 - __main__ - INFO - Component neuromodulation initialized successfully
2025-05-28 15:35:48,955 - __main__ - INFO - Initializing BiologicalTimingCircuits
2025-05-28 15:35:48,955 - __main__ - INFO - Component biological_timing initialized successfully
2025-05-28 15:35:48,957 - __main__ - INFO - Core Neural Architecture initialization completed with status: success
2025-05-28 15:35:48,957 - __main__ - INFO - NeuromorphicCore initialized with GPU=False, Parallel=True
2025-05-28 15:35:48,960 - __main__ - INFO - NeuroplasticityEngine initialized
2025-05-28 15:35:48,960 - __main__ - INFO - SynapticPruningModule initialized
2025-05-28 15:35:48,962 - __main__ - INFO - NeuromodulationSystem initialized
2025-05-28 15:35:48,962 - __main__ - INFO - BiologicalTimingCircuits initialized
2025-05-28 15:35:48,964 - __main__ - INFO - NeuroplasticityEngine connected to NeuromorphicCore
2025-05-28 15:35:48,964 - __main__ - INFO - SynapticPruningModule connected to NeuromorphicCore
2025-05-28 15:35:48,964 - __main__ - INFO - NeuromodulationSystem connected to NeuromorphicCore
2025-05-28 15:35:48,965 - __main__ - INFO - BiologicalTimingCircuits connected to NeuromorphicCore
2025-05-28 15:35:48,965 - __main__ - INFO - BiologicalTimingCircuits connected to NeuromodulationSystem
2025-05-28 15:35:48,965 - __main__ - INFO - Core neural components initialized and connected successfully
2025-05-28 15:35:48,965 - __main__ - INFO - Default CoreNeuralInterface instance created successfully
2025-05-28 15:35:48,965 - __main__ - INFO - Cleaning up Core Neural Architecture resources
2025-05-29 11:27:23,530 - __main__ - INFO - ULTRA Core Neural Architecture v1.0.0 loaded successfully
2025-05-29 11:27:23,530 - __main__ - INFO - Available backends: numpy, torch, numba
2025-05-29 11:27:23,551 - __main__ - INFO - Optimal backend detected: torch
2025-05-29 11:27:23,552 - __main__ - INFO - GPU acceleration: disabled
2025-05-29 11:27:23,552 - __main__ - INFO - Initializing CoreNeuralInterface
2025-05-29 11:27:23,553 - __main__ - INFO - Starting Core Neural Architecture initialization
2025-05-29 11:27:23,553 - __main__ - INFO - Initializing NeuromorphicCore components
2025-05-29 11:27:23,553 - __main__ - INFO - Component neuromorphic_core initialized successfully
2025-05-29 11:27:23,553 - __main__ - INFO - Initializing NeuroplasticityEngine
2025-05-29 11:27:23,553 - __main__ - INFO - Component neuroplasticity_engine initialized successfully
2025-05-29 11:27:23,553 - __main__ - INFO - Initializing SynapticPruningModule
2025-05-29 11:27:23,553 - __main__ - INFO - Component synaptic_pruning initialized successfully
2025-05-29 11:27:23,553 - __main__ - INFO - Initializing NeuromodulationSystem
2025-05-29 11:27:23,553 - __main__ - INFO - Component neuromodulation initialized successfully
2025-05-29 11:27:23,553 - __main__ - INFO - Initializing BiologicalTimingCircuits
2025-05-29 11:27:23,553 - __main__ - INFO - Component biological_timing initialized successfully
2025-05-29 11:27:23,553 - __main__ - INFO - Core Neural Architecture initialization completed with status: success
2025-05-29 11:27:23,554 - __main__ - INFO - NeuromorphicCore initialized with GPU=False, Parallel=True
2025-05-29 11:27:23,554 - __main__ - INFO - NeuroplasticityEngine initialized
2025-05-29 11:27:23,554 - __main__ - INFO - SynapticPruningModule initialized
2025-05-29 11:27:23,554 - __main__ - INFO - NeuromodulationSystem initialized
2025-05-29 11:27:23,554 - __main__ - INFO - BiologicalTimingCircuits initialized
2025-05-29 11:27:23,554 - __main__ - INFO - NeuroplasticityEngine connected to NeuromorphicCore
2025-05-29 11:27:23,554 - __main__ - INFO - SynapticPruningModule connected to NeuromorphicCore
2025-05-29 11:27:23,554 - __main__ - INFO - NeuromodulationSystem connected to NeuromorphicCore
2025-05-29 11:27:23,554 - __main__ - INFO - BiologicalTimingCircuits connected to NeuromorphicCore
2025-05-29 11:27:23,554 - __main__ - INFO - BiologicalTimingCircuits connected to NeuromodulationSystem
2025-05-29 11:27:23,555 - __main__ - INFO - Core neural components initialized and connected successfully
2025-05-29 11:27:23,555 - __main__ - INFO - Default CoreNeuralInterface instance created successfully
2025-05-29 11:27:23,555 - __main__ - INFO - Cleaning up Core Neural Architecture resources
