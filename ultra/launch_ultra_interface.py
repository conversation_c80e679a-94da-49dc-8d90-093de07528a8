#!/usr/bin/env python3
"""
ULTRA AGI Interface Launcher
============================

Universal launcher for all ULTRA AGI interfaces:
- Desktop GUI (Tkinter)
- Web GUI (Flask)
- Terminal Chat
- Enhanced Chat
"""

import sys
import os
import subprocess
import webbrowser
import time

def print_banner():
    """Print ULTRA banner"""
    print("""
🤖 ╔══════════════════════════════════════════════════════════════╗
   ║                    ULTRA AGI LAUNCHER                        ║
   ║              Advanced Artificial General Intelligence        ║
   ╚══════════════════════════════════════════════════════════════╝
""")

def check_dependencies():
    """Check available dependencies"""
    deps = {
        'tkinter': False,
        'flask': False,
        'ultra': False,
        'enhancements': False
    }
    
    # Check tkinter
    try:
        import tkinter
        deps['tkinter'] = True
    except ImportError:
        pass
    
    # Check Flask
    try:
        import flask
        deps['flask'] = True
    except ImportError:
        pass
    
    # Check ULTRA
    try:
        import ultra
        deps['ultra'] = True
    except ImportError:
        pass
    
    # Check enhancements
    try:
        from ultra.enhancements import ULTRAEnhancementSystem
        deps['enhancements'] = True
    except ImportError:
        pass
    
    return deps

def show_interface_menu(deps):
    """Show interface selection menu"""
    print("📋 Available ULTRA Interfaces:")
    print("=" * 50)
    
    options = []
    
    # Desktop GUI
    if deps['tkinter'] and deps['ultra']:
        print("1. 🖥️  Desktop GUI (Tkinter) - Full-featured desktop interface")
        options.append(('desktop', launch_desktop_gui))
    else:
        print("1. ❌ Desktop GUI - Missing dependencies (tkinter/ultra)")
    
    # Web GUI
    if deps['flask'] and deps['ultra']:
        print("2. 🌐 Web GUI (Flask) - Modern web-based interface")
        options.append(('web', launch_web_gui))
    else:
        print("2. ❌ Web GUI - Missing dependencies (flask/ultra)")
    
    # Enhanced Terminal Chat
    if deps['ultra'] and deps['enhancements']:
        print("3. ✨ Enhanced Terminal Chat - Advanced chat with all features")
        options.append(('enhanced', launch_enhanced_chat))
    else:
        print("3. ⚠️  Enhanced Terminal Chat - Limited features (missing enhancements)")
        options.append(('enhanced_limited', launch_enhanced_chat_limited))
    
    # Basic Terminal Chat
    if deps['ultra']:
        print("4. 💬 Basic Terminal Chat - Simple text-based interface")
        options.append(('basic', launch_basic_chat))
    else:
        print("4. ❌ Basic Terminal Chat - Missing ULTRA system")
    
    # Test Interface
    print("5. 🧪 Test Enhanced System - Run enhancement tests")
    options.append(('test', launch_test_system))
    
    # Install Dependencies
    print("6. 📦 Install Dependencies - Install missing packages")
    options.append(('install', install_dependencies))
    
    print("7. ❌ Exit")
    options.append(('exit', lambda: sys.exit(0)))
    
    print("\n" + "=" * 50)
    
    return options

def get_user_choice(options):
    """Get user's interface choice"""
    while True:
        try:
            choice = input("👤 Select interface (1-7): ").strip()
            
            if choice.isdigit():
                choice_num = int(choice)
                if 1 <= choice_num <= len(options):
                    return options[choice_num - 1][1]
            
            print("❌ Invalid choice. Please enter a number between 1 and 7.")
            
        except KeyboardInterrupt:
            print("\n\n👋 Goodbye!")
            sys.exit(0)
        except Exception as e:
            print(f"❌ Error: {e}")

def launch_desktop_gui():
    """Launch desktop GUI"""
    print("\n🖥️ Launching Desktop GUI...")
    try:
        from interfaces.gui.ultra_gui import main
        main()
    except ImportError:
        try:
            subprocess.run([sys.executable, "interfaces/gui/ultra_gui.py"], check=True)
        except Exception as e:
            print(f"❌ Failed to launch desktop GUI: {e}")

def launch_web_gui():
    """Launch web GUI"""
    print("\n🌐 Launching Web GUI...")
    print("📝 The web interface will open in your browser...")
    
    try:
        # Start web server in background
        import threading
        from interfaces.web.ultra_web_gui import main as web_main
        
        server_thread = threading.Thread(target=web_main, daemon=True)
        server_thread.start()
        
        # Wait a moment for server to start
        time.sleep(3)
        
        # Open browser
        webbrowser.open('http://localhost:5000')
        
        print("✅ Web GUI started at http://localhost:5000")
        print("💡 Press Ctrl+C to stop the server")
        
        # Keep main thread alive
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n🛑 Stopping web server...")
            
    except ImportError:
        try:
            subprocess.run([sys.executable, "interfaces/web/ultra_web_gui.py"], check=True)
        except Exception as e:
            print(f"❌ Failed to launch web GUI: {e}")

def launch_enhanced_chat():
    """Launch enhanced terminal chat"""
    print("\n✨ Launching Enhanced Terminal Chat...")
    try:
        from interfaces.chat.ultra_enhanced_chat import main
        import asyncio
        asyncio.run(main())
    except ImportError:
        try:
            subprocess.run([sys.executable, "interfaces/chat/ultra_enhanced_chat.py"], check=True)
        except Exception as e:
            print(f"❌ Failed to launch enhanced chat: {e}")

def launch_enhanced_chat_limited():
    """Launch enhanced chat with limited features"""
    print("\n⚠️ Launching Enhanced Chat (Limited Features)...")
    print("💡 Some enhancement features may not be available")
    launch_enhanced_chat()

def launch_basic_chat():
    """Launch basic terminal chat"""
    print("\n💬 Launching Basic Terminal Chat...")
    try:
        from interfaces.chat.ultra_terminal_chat import main
        main()
    except ImportError:
        try:
            subprocess.run([sys.executable, "interfaces/chat/ultra_terminal_chat.py"], check=True)
        except Exception as e:
            print(f"❌ Failed to launch basic chat: {e}")

def launch_test_system():
    """Launch test system"""
    print("\n🧪 Running Enhancement System Tests...")
    try:
        subprocess.run([sys.executable, "test_enhanced_ultra.py"], check=True)
    except Exception as e:
        print(f"❌ Failed to run tests: {e}")

def install_dependencies():
    """Install missing dependencies"""
    print("\n📦 Installing Dependencies...")
    
    packages = [
        ("Flask & SocketIO (Web GUI)", "flask flask-socketio"),
        ("Pillow (Image processing)", "pillow"),
        ("Additional packages", "requests aiohttp wikipedia feedparser nltk")
    ]
    
    for name, package in packages:
        print(f"\n📦 Installing {name}...")
        try:
            subprocess.run([sys.executable, "-m", "pip", "install"] + package.split(), 
                         check=True, capture_output=True)
            print(f"✅ {name} installed successfully")
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install {name}: {e}")
    
    print("\n✅ Dependency installation completed!")
    print("💡 Please restart the launcher to use new features")

def show_system_info(deps):
    """Show system information"""
    print("\n📊 System Information:")
    print("-" * 30)
    print(f"Python version: {sys.version.split()[0]}")
    print(f"Operating system: {os.name}")
    print(f"Current directory: {os.getcwd()}")
    
    print("\n📋 Available Components:")
    for component, available in deps.items():
        status = "✅" if available else "❌"
        print(f"  {status} {component}")
    
    if deps['ultra']:
        try:
            import ultra
            print(f"\n🤖 ULTRA version: {getattr(ultra, '__version__', 'Unknown')}")
        except:
            pass

def main():
    """Main launcher function"""
    try:
        print_banner()
        
        # Check dependencies
        print("🔍 Checking system dependencies...")
        deps = check_dependencies()
        
        # Show system info
        show_system_info(deps)
        
        # Show interface menu
        options = show_interface_menu(deps)
        
        # Get user choice and launch
        launcher_func = get_user_choice(options)
        launcher_func()
        
    except KeyboardInterrupt:
        print("\n\n👋 Goodbye!")
    except Exception as e:
        print(f"\n❌ Launcher error: {e}")
        print("💡 Please check your ULTRA installation and try again")

if __name__ == "__main__":
    main()
