#!/usr/bin/env python3
"""
ULTRA Complete System Launcher
==============================

This launcher orchestrates the complete ULTRA system, including:
- Unified Backend (all bridges and components)
- Modern Web Frontend (professional interface)
- System monitoring and health checks
- Automatic dependency management
- Production-ready deployment

Features:
- One-command system startup
- Automatic port management
- Health monitoring
- Graceful shutdown
- Error recovery
- Development and production modes
"""

import os
import sys
import time
import signal
import subprocess
import threading
import webbrowser
import logging
import argparse
import json
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import psutil
import requests

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("ultra.launcher")

class ULTRASystemLauncher:
    """Complete ULTRA system launcher and orchestrator"""
    
    def __init__(self, development_mode: bool = False):
        self.development_mode = development_mode
        self.processes = {}
        self.running = False
        self.backend_port = 8000
        self.frontend_port = 8080
        self.health_check_interval = 10
        
        # Paths
        self.ultra_root = Path(__file__).parent
        self.backend_script = self.ultra_root / "ultra" / "unified_backend.py"
        self.frontend_script = self.ultra_root / "interfaces" / "web" / "ultra_unified_frontend.py"
        
        logger.info("ULTRA System Launcher initialized")
    
    def check_dependencies(self) -> bool:
        """Check if all required dependencies are available"""
        logger.info("Checking system dependencies...")
        
        required_packages = [
            "fastapi", "uvicorn", "flask", "flask-socketio", 
            "requests", "psutil", "numpy", "torch"
        ]
        
        missing_packages = []
        
        for package in required_packages:
            try:
                __import__(package.replace("-", "_"))
            except ImportError:
                missing_packages.append(package)
        
        if missing_packages:
            logger.error(f"Missing required packages: {missing_packages}")
            logger.info("Install with: pip install " + " ".join(missing_packages))
            return False
        
        logger.info("✅ All dependencies satisfied")
        return True
    
    def check_ports(self) -> bool:
        """Check if required ports are available"""
        logger.info(f"Checking ports {self.backend_port} and {self.frontend_port}...")
        
        def is_port_available(port: int) -> bool:
            try:
                import socket
                with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                    s.bind(('localhost', port))
                    return True
            except OSError:
                return False
        
        if not is_port_available(self.backend_port):
            logger.error(f"Port {self.backend_port} is already in use")
            return False
        
        if not is_port_available(self.frontend_port):
            logger.error(f"Port {self.frontend_port} is already in use")
            return False
        
        logger.info("✅ Ports are available")
        return True
    
    def start_backend(self) -> bool:
        """Start the ULTRA unified backend"""
        logger.info("Starting ULTRA Unified Backend...")
        
        try:
            # Start backend process
            backend_cmd = [
                sys.executable, "-c",
                f"""
import sys
sys.path.insert(0, '{self.ultra_root}')
from ultra.unified_backend import get_backend

if __name__ == "__main__":
    backend = get_backend()
    backend.run(host="0.0.0.0", port={self.backend_port})
"""
            ]
            
            backend_process = subprocess.Popen(
                backend_cmd,
                stdout=subprocess.PIPE if not self.development_mode else None,
                stderr=subprocess.PIPE if not self.development_mode else None,
                cwd=self.ultra_root
            )
            
            self.processes['backend'] = backend_process
            
            # Wait for backend to start
            logger.info("Waiting for backend to initialize...")
            for attempt in range(30):  # 30 seconds timeout
                try:
                    response = requests.get(f"http://localhost:{self.backend_port}/health", timeout=2)
                    if response.status_code == 200:
                        logger.info("✅ Backend started successfully")
                        return True
                except requests.exceptions.RequestException:
                    pass
                
                time.sleep(1)
            
            logger.error("❌ Backend failed to start within timeout")
            return False
            
        except Exception as e:
            logger.error(f"Failed to start backend: {e}")
            return False
    
    def start_frontend(self) -> bool:
        """Start the ULTRA web frontend"""
        logger.info("Starting ULTRA Web Frontend...")
        
        try:
            # Start frontend process
            frontend_cmd = [
                sys.executable, "-c",
                f"""
import sys
sys.path.insert(0, '{self.ultra_root}')
from interfaces.web.ultra_unified_frontend import ULTRAUnifiedFrontend

if __name__ == "__main__":
    frontend = ULTRAUnifiedFrontend(backend_url="http://localhost:{self.backend_port}")
    frontend.run(host="0.0.0.0", port={self.frontend_port})
"""
            ]
            
            frontend_process = subprocess.Popen(
                frontend_cmd,
                stdout=subprocess.PIPE if not self.development_mode else None,
                stderr=subprocess.PIPE if not self.development_mode else None,
                cwd=self.ultra_root
            )
            
            self.processes['frontend'] = frontend_process
            
            # Wait for frontend to start
            logger.info("Waiting for frontend to initialize...")
            for attempt in range(20):  # 20 seconds timeout
                try:
                    response = requests.get(f"http://localhost:{self.frontend_port}/api/system/status", timeout=2)
                    if response.status_code == 200:
                        logger.info("✅ Frontend started successfully")
                        return True
                except requests.exceptions.RequestException:
                    pass
                
                time.sleep(1)
            
            logger.error("❌ Frontend failed to start within timeout")
            return False
            
        except Exception as e:
            logger.error(f"Failed to start frontend: {e}")
            return False
    
    def monitor_system_health(self):
        """Monitor system health continuously"""
        logger.info("Starting system health monitoring...")
        
        while self.running:
            try:
                # Check backend health
                backend_healthy = False
                try:
                    response = requests.get(f"http://localhost:{self.backend_port}/health", timeout=5)
                    backend_healthy = response.status_code == 200
                except:
                    pass
                
                # Check frontend health
                frontend_healthy = False
                try:
                    response = requests.get(f"http://localhost:{self.frontend_port}/api/system/status", timeout=5)
                    frontend_healthy = response.status_code == 200
                except:
                    pass
                
                # Check process health
                backend_process_healthy = (
                    'backend' in self.processes and 
                    self.processes['backend'].poll() is None
                )
                
                frontend_process_healthy = (
                    'frontend' in self.processes and 
                    self.processes['frontend'].poll() is None
                )
                
                # Log status
                if self.development_mode:
                    status = {
                        "backend_api": "✅" if backend_healthy else "❌",
                        "frontend_api": "✅" if frontend_healthy else "❌",
                        "backend_process": "✅" if backend_process_healthy else "❌",
                        "frontend_process": "✅" if frontend_process_healthy else "❌"
                    }
                    logger.debug(f"System health: {status}")
                
                # Restart failed components if needed
                if not backend_process_healthy and self.running:
                    logger.warning("Backend process died, attempting restart...")
                    self.start_backend()
                
                if not frontend_process_healthy and self.running:
                    logger.warning("Frontend process died, attempting restart...")
                    self.start_frontend()
                
            except Exception as e:
                logger.error(f"Error in health monitoring: {e}")
            
            time.sleep(self.health_check_interval)
    
    def open_browser(self):
        """Open the ULTRA interface in the default browser"""
        url = f"http://localhost:{self.frontend_port}"
        logger.info(f"Opening ULTRA interface: {url}")
        
        # Wait a moment for the frontend to be ready
        time.sleep(3)
        
        try:
            webbrowser.open(url)
            logger.info("✅ Browser opened successfully")
        except Exception as e:
            logger.warning(f"Could not open browser automatically: {e}")
            logger.info(f"Please open {url} manually in your browser")
    
    def setup_signal_handlers(self):
        """Setup signal handlers for graceful shutdown"""
        def signal_handler(sig, frame):
            logger.info(f"Received signal {sig}, shutting down...")
            self.shutdown()
            sys.exit(0)
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    def start_system(self) -> bool:
        """Start the complete ULTRA system"""
        logger.info("🚀 Starting ULTRA Complete System...")
        
        # Pre-flight checks
        if not self.check_dependencies():
            return False
        
        if not self.check_ports():
            return False
        
        # Setup signal handlers
        self.setup_signal_handlers()
        
        # Start backend
        if not self.start_backend():
            logger.error("Failed to start backend")
            return False
        
        # Start frontend
        if not self.start_frontend():
            logger.error("Failed to start frontend")
            self.shutdown()
            return False
        
        # Mark as running
        self.running = True
        
        # Start health monitoring
        health_thread = threading.Thread(target=self.monitor_system_health, daemon=True)
        health_thread.start()
        
        # Open browser
        browser_thread = threading.Thread(target=self.open_browser, daemon=True)
        browser_thread.start()
        
        # Print success message
        self.print_startup_success()
        
        return True
    
    def print_startup_success(self):
        """Print startup success message"""
        print("\n" + "="*60)
        print("🎉 ULTRA COMPLETE SYSTEM STARTED SUCCESSFULLY!")
        print("="*60)
        print(f"🔧 Backend API:     http://localhost:{self.backend_port}")
        print(f"🌐 Web Interface:   http://localhost:{self.frontend_port}")
        print(f"📊 Dashboard:       http://localhost:{self.frontend_port}/dashboard")
        print(f"📖 API Docs:        http://localhost:{self.backend_port}/docs")
        print("="*60)
        print("✨ All ULTRA components are now active and ready!")
        print("   - 6 Integration Bridges")
        print("   - 8 Core Subsystems")
        print("   - Real-time Monitoring")
        print("   - Professional Web Interface")
        print("="*60)
        print("Press Ctrl+C to shutdown the system")
        print()
    
    def wait_for_shutdown(self):
        """Wait for shutdown signal"""
        try:
            while self.running:
                time.sleep(1)
        except KeyboardInterrupt:
            logger.info("Received keyboard interrupt")
            self.shutdown()
    
    def shutdown(self):
        """Gracefully shutdown all components"""
        logger.info("🔄 Shutting down ULTRA system...")
        
        self.running = False
        
        # Terminate processes
        for name, process in self.processes.items():
            if process and process.poll() is None:
                logger.info(f"Terminating {name}...")
                try:
                    process.terminate()
                    process.wait(timeout=10)
                    logger.info(f"✅ {name} terminated gracefully")
                except subprocess.TimeoutExpired:
                    logger.warning(f"Force killing {name}...")
                    process.kill()
                    process.wait()
                except Exception as e:
                    logger.error(f"Error terminating {name}: {e}")
        
        logger.info("🎯 ULTRA system shutdown complete")
    
    def run(self):
        """Run the complete ULTRA system"""
        if self.start_system():
            self.wait_for_shutdown()
        else:
            logger.error("Failed to start ULTRA system")
            sys.exit(1)

def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description="ULTRA Complete System Launcher")
    parser.add_argument(
        "--dev", 
        action="store_true", 
        help="Run in development mode with verbose output"
    )
    parser.add_argument(
        "--backend-port", 
        type=int, 
        default=8000, 
        help="Backend port (default: 8000)"
    )
    parser.add_argument(
        "--frontend-port", 
        type=int, 
        default=8080, 
        help="Frontend port (default: 8080)"
    )
    
    args = parser.parse_args()
    
    # Create launcher
    launcher = ULTRASystemLauncher(development_mode=args.dev)
    launcher.backend_port = args.backend_port
    launcher.frontend_port = args.frontend_port
    
    if args.dev:
        logging.getLogger().setLevel(logging.DEBUG)
        logger.info("Running in development mode")
    
    # Run the system
    launcher.run()

if __name__ == "__main__":
    main()
