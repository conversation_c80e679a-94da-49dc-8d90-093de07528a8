#!/usr/bin/env python3
"""
Test ULTRA Enhanced System
==========================

Simple test to verify all enhancement components work correctly.
"""

import asyncio
import sys
import os
import logging

# Add paths
sys.path.insert(0, os.path.abspath('.'))
sys.path.insert(0, os.path.abspath('ultra'))

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_enhancements():
    """Test all enhancement components"""
    
    print("🧪 ULTRA Enhancement System Test")
    print("=" * 50)
    
    # Test 1: Import enhancement system
    try:
        print("📦 Testing enhancement system import...")
        from ultra.enhancements import ULTRAEnhancementSystem, EnhancementConfig
        print("✅ Enhancement system imported successfully")
    except ImportError as e:
        print(f"❌ Enhancement system import failed: {e}")
        return False
    
    # Test 2: Create enhancement system
    try:
        print("🔧 Creating enhancement system...")
        config = EnhancementConfig(
            enable_wikipedia=True,
            enable_scientific_databases=True,
            enable_pretrained_models=False,  # Skip heavy models for test
            enable_voice_input=False,  # Skip voice for test
            enable_voice_output=False,
            enable_image_processing=False,  # Skip image processing for test
            enable_internet_training=True
        )
        
        enhancement_system = ULTRAEnhancementSystem(config)
        print("✅ Enhancement system created")
    except Exception as e:
        print(f"❌ Enhancement system creation failed: {e}")
        return False
    
    # Test 3: Initialize enhancement system
    try:
        print("🚀 Initializing enhancement system...")
        success = await enhancement_system.initialize()
        if success:
            print("✅ Enhancement system initialized successfully")
        else:
            print("⚠️ Enhancement system initialized with warnings")
    except Exception as e:
        print(f"❌ Enhancement system initialization failed: {e}")
        return False
    
    # Test 4: Test external knowledge
    try:
        print("📚 Testing external knowledge...")
        if enhancement_system.knowledge_manager:
            knowledge = await enhancement_system.knowledge_manager.get_relevant_knowledge("artificial intelligence")
            if knowledge:
                print("✅ External knowledge retrieval working")
                print(f"   Retrieved knowledge from: {list(knowledge.keys())}")
            else:
                print("⚠️ No knowledge retrieved (may be rate limited)")
        else:
            print("⚠️ Knowledge manager not available")
    except Exception as e:
        print(f"❌ External knowledge test failed: {e}")
    
    # Test 5: Test internet training
    try:
        print("🌐 Testing internet training...")
        if enhancement_system.internet_trainer:
            training_result = await enhancement_system.internet_trainer.train_on_topics(["quantum computing"])
            if training_result:
                print("✅ Internet training working")
                print(f"   Articles collected: {training_result.get('articles_collected', 0)}")
            else:
                print("⚠️ No training data collected")
        else:
            print("⚠️ Internet trainer not available")
    except Exception as e:
        print(f"❌ Internet training test failed: {e}")
    
    # Test 6: Test response enhancement
    try:
        print("💬 Testing response enhancement...")
        base_response = "Artificial intelligence is a fascinating field of computer science."
        enhanced_response = await enhancement_system.enhance_response(
            "What is AI?", 
            base_response
        )
        
        if enhanced_response and len(enhanced_response) > len(base_response):
            print("✅ Response enhancement working")
            print(f"   Enhanced response length: {len(enhanced_response)} chars")
        else:
            print("⚠️ Response enhancement returned original response")
    except Exception as e:
        print(f"❌ Response enhancement test failed: {e}")
    
    # Test 7: Get system status
    try:
        print("📊 Getting system status...")
        status = enhancement_system.get_enhancement_status()
        print("✅ System status retrieved:")
        for component, enabled in status.items():
            icon = "✅" if enabled else "❌"
            print(f"   {icon} {component.replace('_', ' ').title()}")
    except Exception as e:
        print(f"❌ System status test failed: {e}")
    
    print("\n" + "=" * 50)
    print("🎯 Enhancement System Test Summary:")
    
    # Count working components
    working_components = sum(1 for enabled in status.values() if enabled)
    total_components = len(status)
    
    print(f"   • Working components: {working_components}/{total_components}")
    print(f"   • Success rate: {working_components/total_components*100:.1f}%")
    
    if working_components >= total_components * 0.5:
        print("   • Status: 🟢 Enhancement system is functional")
        return True
    else:
        print("   • Status: 🔴 Enhancement system needs attention")
        return False

async def test_simple_chat():
    """Test a simple enhanced chat interaction"""
    
    print("\n🤖 Testing Simple Enhanced Chat")
    print("=" * 50)
    
    try:
        from ultra.enhancements import get_enhancement_system, EnhancementConfig
        
        # Create lightweight config
        config = EnhancementConfig(
            enable_wikipedia=True,
            enable_scientific_databases=False,
            enable_pretrained_models=False,
            enable_voice_input=False,
            enable_voice_output=False,
            enable_image_processing=False,
            enable_internet_training=False
        )
        
        enhancement_system = await get_enhancement_system(config)
        
        # Test simple interaction
        query = "What is quantum physics?"
        base_response = "Quantum physics is the study of matter and energy at the smallest scales."
        
        print(f"👤 User: {query}")
        print(f"🤖 Base Response: {base_response}")
        
        enhanced_response = await enhancement_system.enhance_response(query, base_response)
        
        print(f"✨ Enhanced Response: {enhanced_response}")
        
        print("✅ Simple enhanced chat test completed!")
        return True
        
    except Exception as e:
        print(f"❌ Simple chat test failed: {e}")
        return False

async def main():
    """Main test function"""
    
    print("🚀 ULTRA Enhanced System - Comprehensive Test Suite")
    print("=" * 70)
    
    # Run enhancement tests
    enhancement_success = await test_enhancements()
    
    # Run simple chat test
    chat_success = await test_simple_chat()
    
    print("\n" + "=" * 70)
    print("🏆 Final Test Results:")
    print(f"   • Enhancement System: {'✅ PASS' if enhancement_success else '❌ FAIL'}")
    print(f"   • Simple Chat: {'✅ PASS' if chat_success else '❌ FAIL'}")
    
    if enhancement_success and chat_success:
        print("\n🎉 ULTRA Enhanced System is ready for use!")
        print("💡 You can now run: python interfaces/chat/ultra_enhanced_chat.py")
    elif enhancement_success:
        print("\n🟡 ULTRA Enhanced System is partially working")
        print("💡 Core enhancements work, but chat interface needs fixes")
    else:
        print("\n🔴 ULTRA Enhanced System needs debugging")
        print("💡 Check dependencies and configuration")

if __name__ == "__main__":
    asyncio.run(main())
