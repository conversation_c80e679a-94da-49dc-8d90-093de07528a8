#!/usr/bin/env python3
"""
Test suite for the ULTRA ethical framework.

This module tests the ethical framework components that ensure ULTRA
operates according to ethical guidelines, maintains value alignment,
ensures fairness, provides transparency, and prevents harm.

The tests cover:
1. Ethical Validation - Adherence to ethical guidelines
2. Value Alignment - Alignment with core values
3. Fairness Evaluation - Bias detection and mitigation
4. Transparency Management - Explanation generation and clarity
5. Harm Prevention - Detection and prevention of potential harm
6. Integration with other components - How ethics interacts with the broader system
"""

import unittest
import numpy as np
import pandas as pd
import tensorflow as tf
import torch
import sklearn.metrics as metrics
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestClassifier, IsolationForest
from sklearn.linear_model import LogisticRegression
import matplotlib.pyplot as plt
import seaborn as sns
import logging
import sys
import os
from typing import Dict, List, Tuple, Any, Optional, Union, Callable
from dataclasses import dataclass
from pathlib import Path
import json

# Configure matplotlib to use non-interactive backend for testing
plt.switch_backend('Agg')

# Ensure the ultra package is in the path
sys.path.insert(0, str(Path(__file__).resolve().parents[1]))

# Import ethical framework modules
from ultra.ethical_framework.ethical_validator import (
    EthicalValidator,
    EthicalGuideline,
    EthicalValidationResult,
    EthicalPrinciple
)
from ultra.ethical_framework.value_alignment import (
    ValueAlignment,
    ValueSet,
    AlignmentResult,
    ValueMetric
)
from ultra.ethical_framework.fairness_evaluator import (
    FairnessEvaluator,
    FairnessMetric,
    FairnessResult,
    GroupFairnessMetrics,
    IndividualFairnessMetrics,
    BiasDetector,
    BiasMetrics
)
from ultra.ethical_framework.transparency_manager import (
    TransparencyManager,
    ExplanationMethod,
    ExplanationResult,
    ModelExplainer,
    InterpretabilityScore
)
from ultra.ethical_framework.harm_prevention import (
    HarmPrevention,
    HarmCategory,
    HarmAssessmentResult,
    PreventionStrategy,
    RiskProfile
)

# Import related modules for integration testing
from ultra.meta_cognitive.reasoning_graphs import ReasoningGraph
from ultra.neuro_symbolic.logical_reasoning import LogicalReasoningEngine
from ultra.diffusion_reasoning.bayesian_uncertainty import BayesianUncertaintyQuantification

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("ethical_framework_tests")


@dataclass
class EthicalTestScenario:
    """Test scenario for ethical framework evaluation."""
    name: str
    description: str
    context: Dict[str, Any]
    actions: List[Dict[str, Any]]
    expected_outcomes: Dict[str, Any]
    stakeholders: List[str]


@dataclass
class FairnessTestCase:
    """Test case for fairness evaluation."""
    name: str
    features: np.ndarray
    labels: np.ndarray
    sensitive_attributes: Dict[str, np.ndarray]
    model: Any
    expected_metrics: Dict[str, float]


class TestEthicalValidator(unittest.TestCase):
    """Tests for the EthicalValidator component."""
    
    def setUp(self):
        """Set up test fixtures for EthicalValidator tests."""
        # Define ethical principles
        self.principles = [
            EthicalPrinciple(
                name="non_maleficence",
                description="Do no harm",
                validation_function=lambda action, context: self._validate_non_maleficence(action, context),
                weight=0.9
            ),
            EthicalPrinciple(
                name="beneficence",
                description="Act in the best interest of others",
                validation_function=lambda action, context: self._validate_beneficence(action, context),
                weight=0.8
            ),
            EthicalPrinciple(
                name="autonomy",
                description="Respect individuals' right to make their own decisions",
                validation_function=lambda action, context: self._validate_autonomy(action, context),
                weight=0.8
            ),
            EthicalPrinciple(
                name="justice",
                description="Treat all individuals fairly and equally",
                validation_function=lambda action, context: self._validate_justice(action, context),
                weight=0.85
            )
        ]
        
        # Define ethical guidelines
        self.guidelines = [
            EthicalGuideline(
                name="informed_consent",
                description="Ensure informed consent is obtained before actions that affect individuals",
                relevant_principles=["autonomy"],
                validation_function=lambda action, context: self._validate_informed_consent(action, context)
            ),
            EthicalGuideline(
                name="fairness_across_groups",
                description="Ensure fair treatment across different demographic groups",
                relevant_principles=["justice"],
                validation_function=lambda action, context: self._validate_fairness_across_groups(action, context)
            ),
            EthicalGuideline(
                name="minimize_harm",
                description="Take steps to minimize potential harm from actions",
                relevant_principles=["non_maleficence"],
                validation_function=lambda action, context: self._validate_minimize_harm(action, context)
            ),
            EthicalGuideline(
                name="maximize_benefit",
                description="Seek to maximize benefits for individuals and society",
                relevant_principles=["beneficence"],
                validation_function=lambda action, context: self._validate_maximize_benefit(action, context)
            )
        ]
        
        # Initialize EthicalValidator
        self.validator = EthicalValidator(
            principles=self.principles,
            guidelines=self.guidelines,
            validation_threshold=0.8,
            log_validation=True
        )
        
        # Generate test scenarios
        self.ethical_scenarios = self._generate_ethical_scenarios()
        self.unethical_scenarios = self._generate_unethical_scenarios()
    
    def _validate_non_maleficence(self, action, context):
        """Validate adherence to non-maleficence principle."""
        # Check if action has explicit harm metrics
        if "harm_metrics" in action:
            total_harm = sum(action["harm_metrics"].values())
            return max(0.0, 1.0 - total_harm * 2)
        
        # Check if action type is inherently harmful
        harmful_actions = ["coerce", "deceive", "manipulate", "exploit"]
        if action.get("type", "").lower() in harmful_actions:
            return 0.2
        
        # Check potential negative consequences
        if "consequences" in action:
            negative_consequences = action["consequences"].get("negative", 0.0)
            return max(0.0, 1.0 - negative_consequences * 2)
        
        # Default if no explicit harm information
        return 0.8
    
    def _validate_beneficence(self, action, context):
        """Validate adherence to beneficence principle."""
        # Check if action has explicit benefit metrics
        if "benefit_metrics" in action:
            total_benefit = sum(action["benefit_metrics"].values())
            return min(1.0, total_benefit)
        
        # Check if action type is inherently beneficial
        beneficial_actions = ["assist", "protect", "inform", "empower"]
        if action.get("type", "").lower() in beneficial_actions:
            return 0.8
        
        # Check potential positive consequences
        if "consequences" in action:
            positive_consequences = action["consequences"].get("positive", 0.0)
            return min(1.0, positive_consequences)
        
        # Default if no explicit benefit information
        return 0.5
    
    def _validate_autonomy(self, action, context):
        """Validate adherence to autonomy principle."""
        # Check if action restricts autonomy
        if "autonomy_impact" in action:
            return action["autonomy_impact"]
        
        # Check if action involves consent
        if action.get("consent_obtained", False):
            return 0.9
        elif "consent_required" in action and action["consent_required"] and not action.get("consent_obtained", False):
            return 0.1
        
        # Check if action type is autonomy-enhancing or restricting
        autonomy_enhancing = ["inform", "educate", "empower", "provide_options"]
        autonomy_restricting = ["force", "coerce", "manipulate"]
        
        if action.get("type", "").lower() in autonomy_enhancing:
            return 0.9
        elif action.get("type", "").lower() in autonomy_restricting:
            return 0.1
        
        # Default if no explicit autonomy information
        return 0.7
    
    def _validate_justice(self, action, context):
        """Validate adherence to justice principle."""
        # Check if action has explicit fairness metrics
        if "fairness_metrics" in action:
            if "demographic_parity" in action["fairness_metrics"]:
                demographic_parity = action["fairness_metrics"]["demographic_parity"]
                return min(1.0, 1.0 - abs(demographic_parity - 1.0))
            
            if "equal_opportunity" in action["fairness_metrics"]:
                equal_opportunity = action["fairness_metrics"]["equal_opportunity"]
                return min(1.0, 1.0 - abs(equal_opportunity - 1.0))
            
            # Average available fairness metrics
            return sum(action["fairness_metrics"].values()) / len(action["fairness_metrics"])
        
        # Check if action involves differential treatment
        if "differential_treatment" in action:
            return 1.0 - action["differential_treatment"]
        
        # Check if action type is related to fairness
        fair_actions = ["distribute_equally", "ensure_access", "correct_bias"]
        unfair_actions = ["discriminate", "exclude", "favor"]
        
        if action.get("type", "").lower() in fair_actions:
            return 0.9
        elif action.get("type", "").lower() in unfair_actions:
            return 0.1
        
        # Default if no explicit justice information
        return 0.7
    
    def _validate_informed_consent(self, action, context):
        """Validate adherence to informed consent guideline."""
        # Check if consent is required for this action
        if not action.get("consent_required", False):
            return 1.0
        
        # Check if consent was obtained
        if action.get("consent_obtained", False):
            # Check quality of consent
            consent_quality = 1.0
            
            # Was information provided clear and comprehensive?
            if "consent_info_quality" in action:
                consent_quality *= action["consent_info_quality"]
            
            # Was there any coercion?
            if action.get("coercion_present", False):
                consent_quality *= 0.5
            
            # Was the individual capable of understanding?
            if "subject_capacity" in action:
                consent_quality *= action["subject_capacity"]
            
            return consent_quality
        else:
            return 0.0
    
    def _validate_fairness_across_groups(self, action, context):
        """Validate adherence to fairness across groups guideline."""
        # If fairness metrics are explicitly provided
        if "fairness_metrics" in action:
            metrics = action["fairness_metrics"]
            
            # Calculate average fairness from metrics
            if len(metrics) > 0:
                # For demographic parity and equal opportunity, values closer to 1.0 are better
                fairness_scores = []
                
                for metric_name, metric_value in metrics.items():
                    if metric_name in ["demographic_parity", "equal_opportunity", "predictive_parity"]:
                        fairness_scores.append(1.0 - abs(metric_value - 1.0))
                    else:
                        # For most disparity metrics, lower is better
                        fairness_scores.append(1.0 - metric_value)
                
                return sum(fairness_scores) / len(fairness_scores)
        
        # If disparate impact is provided
        if "disparate_impact" in action:
            # Disparate impact closer to 1.0 is better
            return 1.0 - abs(action["disparate_impact"] - 1.0)
        
        # If demographic distribution of outcomes is provided
        if "demographic_outcomes" in action and "demographics" in context:
            outcomes = action["demographic_outcomes"]
            demographics = context["demographics"]
            
            # Calculate statistical parity
            # Ideally, outcome rate should be similar across groups
            outcome_rates = {group: outcomes.get(group, 0) / demographics.get(group, 1) 
                            for group in demographics}
            
            if len(outcome_rates) > 1:
                min_rate = min(outcome_rates.values())
                max_rate = max(outcome_rates.values())
                
                # Perfect fairness would be ratio of 1.0
                if min_rate > 0:
                    return min_rate / max_rate
                else:
                    return 0.0
        
        # Default if no fairness information is available
        return 0.5
    
    def _validate_minimize_harm(self, action, context):
        """Validate adherence to harm minimization guideline."""
        # If harm mitigation strategies are explicitly provided
        if "harm_mitigation" in action:
            mitigations = action["harm_mitigation"]
            
            # Check if mitigations address all identified harms
            if "identified_harms" in action:
                identified_harms = action["identified_harms"]
                addressed_harms = set(mitigations.keys())
                unaddressed_harms = set(identified_harms) - addressed_harms
                
                if not identified_harms:
                    return 1.0  # No harms identified
                
                if not unaddressed_harms:
                    # All harms addressed, check effectiveness of mitigations
                    effectiveness_scores = [score for score in mitigations.values()]
                    return sum(effectiveness_scores) / len(effectiveness_scores)
                else:
                    # Some harms not addressed
                    addressed_ratio = len(addressed_harms) / len(identified_harms)
                    effectiveness_sum = sum([score for score in mitigations.values()])
                    
                    if addressed_harms:
                        effectiveness_avg = effectiveness_sum / len(addressed_harms)
                    else:
                        effectiveness_avg = 0.0
                    
                    return addressed_ratio * effectiveness_avg
            
            # If mitigations exist but no explicit identified harms
            if mitigations:
                return sum(mitigations.values()) / len(mitigations)
        
        # If harm assessment is provided
        if "harm_assessment" in action:
            assessment = action["harm_assessment"]
            
            # Calculate overall harm level (lower is better)
            if assessment:
                harm_level = sum(assessment.values()) / len(assessment)
                return 1.0 - harm_level
        
        # If action has known potential for harm
        if action.get("type", "").lower() in ["recommend", "decide", "intervene"]:
            if "risk_assessment" not in action:
                return 0.5  # Potentially harmful action with no risk assessment
        
        # Default if no harm information is available
        return 0.7
    
    def _validate_maximize_benefit(self, action, context):
        """Validate adherence to benefit maximization guideline."""
        # If benefit metrics are explicitly provided
        if "benefit_metrics" in action:
            benefits = action["benefit_metrics"]
            
            # Calculate average benefit
            if benefits:
                return sum(benefits.values()) / len(benefits)
        
        # If positive outcomes are provided
        if "outcomes" in action and "positive_outcomes" in action["outcomes"]:
            positive_outcomes = action["outcomes"]["positive_outcomes"]
            all_outcomes = sum(action["outcomes"].values())
            
            if all_outcomes > 0:
                return positive_outcomes / all_outcomes
        
        # If expected utility is provided
        if "expected_utility" in action:
            return min(1.0, action["expected_utility"])
        
        # Default if no benefit information is available
        return 0.5
    
    def _generate_ethical_scenarios(self):
        """Generate test scenarios that should pass ethical validation."""
        scenarios = []
        
        # Medical recommendation scenario
        scenarios.append(EthicalTestScenario(
            name="ethical_medical_recommendation",
            description="Recommending treatment based on evidence",
            context={
                "domain": "healthcare",
                "patient_demographics": {
                    "age": 45,
                    "gender": "female",
                    "ethnicity": "asian"
                },
                "current_condition": "hypertension",
                "medical_history": ["diabetes_type_2"],
                "demographics": {
                    "male": 0.48,
                    "female": 0.52
                }
            },
            actions=[
                {
                    "type": "recommend",
                    "content": "Prescribe ACE inhibitor",
                    "consent_required": True,
                    "consent_obtained": True,
                    "consent_info_quality": 0.9,
                    "subject_capacity": 1.0,
                    "fairness_metrics": {
                        "demographic_parity": 0.98,
                        "equal_opportunity": 0.97
                    },
                    "harm_assessment": {
                        "physical": 0.05,
                        "psychological": 0.02,
                        "social": 0.01,
                        "financial": 0.1
                    },
                    "harm_mitigation": {
                        "physical": 0.9,
                        "financial": 0.8
                    },
                    "identified_harms": ["physical", "financial"],
                    "benefit_metrics": {
                        "health_improvement": 0.8,
                        "quality_of_life": 0.7,
                        "longevity": 0.6
                    },
                    "expected_utility": 0.85
                }
            ],
            expected_outcomes={
                "ethical_score": 0.9,
                "should_pass": True
            },
            stakeholders=["patient", "healthcare_provider", "insurer"]
        ))
        
        # Financial advice scenario
        scenarios.append(EthicalTestScenario(
            name="ethical_financial_advice",
            description="Providing investment advice with full disclosure",
            context={
                "domain": "finance",
                "client_demographics": {
                    "age": 35,
                    "income_level": "middle",
                    "risk_tolerance": "moderate"
                },
                "financial_goal": "retirement",
                "time_horizon": "long_term",
                "demographics": {
                    "low_income": 0.3,
                    "middle_income": 0.5,
                    "high_income": 0.2
                }
            },
            actions=[
                {
                    "type": "inform",
                    "content": "Diversified portfolio recommendation",
                    "consent_required": True,
                    "consent_obtained": True,
                    "consent_info_quality": 0.95,
                    "subject_capacity": 0.9,
                    "fairness_metrics": {
                        "demographic_parity": 0.96,
                        "equal_opportunity": 0.95
                    },
                    "harm_assessment": {
                        "financial": 0.15,
                        "psychological": 0.05,
                        "social": 0.01
                    },
                    "harm_mitigation": {
                        "financial": 0.85
                    },
                    "identified_harms": ["financial"],
                    "benefit_metrics": {
                        "financial_security": 0.75,
                        "wealth_building": 0.7,
                        "goal_achievement": 0.8
                    },
                    "expected_utility": 0.8
                }
            ],
            expected_outcomes={
                "ethical_score": 0.85,
                "should_pass": True
            },
            stakeholders=["client", "financial_advisor", "financial_institutions"]
        ))
        
        # Educational content recommendation
        scenarios.append(EthicalTestScenario(
            name="ethical_educational_recommendation",
            description="Recommending personalized learning resources",
            context={
                "domain": "education",
                "student_demographics": {
                    "age": 16,
                    "grade_level": "high_school",
                    "learning_style": "visual"
                },
                "subject": "mathematics",
                "current_knowledge": "intermediate",
                "demographics": {
                    "visual_learners": 0.4,
                    "auditory_learners": 0.3,
                    "kinesthetic_learners": 0.3
                }
            },
            actions=[
                {
                    "type": "recommend",
                    "content": "Interactive geometry visualization tools",
                    "consent_required": False,
                    "fairness_metrics": {
                        "demographic_parity": 0.99,
                        "equal_opportunity": 0.98
                    },
                    "harm_assessment": {
                        "psychological": 0.02,
                        "social": 0.01
                    },
                    "harm_mitigation": {},
                    "identified_harms": [],
                    "benefit_metrics": {
                        "learning_outcomes": 0.85,
                        "engagement": 0.9,
                        "knowledge_retention": 0.8
                    },
                    "expected_utility": 0.85
                }
            ],
            expected_outcomes={
                "ethical_score": 0.9,
                "should_pass": True
            },
            stakeholders=["student", "teacher", "educational_institution"]
        ))
        
        return scenarios
    
    def _generate_unethical_scenarios(self):
        """Generate test scenarios that should fail ethical validation."""
        scenarios = []
        
        # Medical recommendation without consent
        scenarios.append(EthicalTestScenario(
            name="unethical_medical_recommendation",
            description="Recommending treatment without informed consent",
            context={
                "domain": "healthcare",
                "patient_demographics": {
                    "age": 75,
                    "gender": "male",
                    "ethnicity": "caucasian"
                },
                "current_condition": "early_dementia",
                "medical_history": ["hypertension"],
                "demographics": {
                    "male": 0.48,
                    "female": 0.52
                }
            },
            actions=[
                {
                    "type": "recommend",
                    "content": "Experimental treatment",
                    "consent_required": True,
                    "consent_obtained": False,
                    "subject_capacity": 0.6,
                    "fairness_metrics": {
                        "demographic_parity": 0.7,
                        "equal_opportunity": 0.65
                    },
                    "harm_assessment": {
                        "physical": 0.3,
                        "psychological": 0.2,
                        "social": 0.1,
                        "financial": 0.4
                    },
                    "harm_mitigation": {
                        "physical": 0.6
                    },
                    "identified_harms": ["physical", "psychological", "financial"],
                    "benefit_metrics": {
                        "health_improvement": 0.6,
                        "quality_of_life": 0.5
                    },
                    "expected_utility": 0.55
                }
            ],
            expected_outcomes={
                "ethical_score": 0.4,
                "should_pass": False,
                "violation_principles": ["autonomy"]
            },
            stakeholders=["patient", "healthcare_provider", "family_members"]
        ))
        
        # Discriminatory financial product
        scenarios.append(EthicalTestScenario(
            name="unethical_financial_product",
            description="Financial product with significant disparate impact",
            context={
                "domain": "finance",
                "demographic_distribution": {
                    "group_A": 0.6,
                    "group_B": 0.4
                },
                "demographics": {
                    "group_A": 0.6,
                    "group_B": 0.4
                }
            },
            actions=[
                {
                    "type": "recommend",
                    "content": "High-fee investment product",
                    "consent_required": True,
                    "consent_obtained": True,
                    "consent_info_quality": 0.5,
                    "fairness_metrics": {
                        "demographic_parity": 0.4,
                        "equal_opportunity": 0.35
                    },
                    "demographic_outcomes": {
                        "group_A": 0.8,
                        "group_B": 0.2
                    },
                    "disparate_impact": 0.3,
                    "harm_assessment": {
                        "financial": 0.6,
                        "psychological": 0.3,
                        "social": 0.2
                    },
                    "harm_mitigation": {},
                    "identified_harms": ["financial", "psychological"],
                    "benefit_metrics": {
                        "financial_institution_profit": 0.9,
                        "client_financial_gain": 0.3
                    },
                    "expected_utility": 0.4
                }
            ],
            expected_outcomes={
                "ethical_score": 0.3,
                "should_pass": False,
                "violation_principles": ["justice", "non_maleficence", "beneficence"]
            },
            stakeholders=["clients", "financial_institution", "regulators"]
        ))
        
        # Manipulative content recommendation
        scenarios.append(EthicalTestScenario(
            name="unethical_content_recommendation",
            description="Addictive and manipulative content recommendation",
            context={
                "domain": "social_media",
                "user_demographics": {
                    "age": 15,
                    "usage_pattern": "heavy",
                    "vulnerability": "high"
                },
                "demographics": {
                    "minors": 0.2,
                    "adults": 0.8
                }
            },
            actions=[
                {
                    "type": "manipulate",
                    "content": "Engagement-optimized addictive content",
                    "consent_required": True,
                    "consent_obtained": True,
                    "consent_info_quality": 0.3,
                    "coercion_present": True,
                    "subject_capacity": 0.7,
                    "fairness_metrics": {
                        "demographic_parity": 0.9
                    },
                    "harm_assessment": {
                        "psychological": 0.7,
                        "social": 0.6,
                        "physical": 0.3
                    },
                    "harm_mitigation": {
                        "psychological": 0.2
                    },
                    "identified_harms": ["psychological", "social", "physical"],
                    "benefit_metrics": {
                        "platform_engagement": 0.95,
                        "platform_revenue": 0.9,
                        "user_enjoyment": 0.7
                    },
                    "expected_utility": 0.8
                }
            ],
            expected_outcomes={
                "ethical_score": 0.2,
                "should_pass": False,
                "violation_principles": ["non_maleficence", "autonomy"]
            },
            stakeholders=["user", "platform", "society"]
        ))
        
        return scenarios
    
    def test_ethical_scenario_validation(self):
        """Test validation of ethical scenarios."""
        for scenario in self.ethical_scenarios:
            results = []
            
            for action in scenario.actions:
                result = self.validator.validate(action, scenario.context)
                results.append(result)
            
            # Check that all actions in the scenario pass validation
            for i, result in enumerate(results):
                action = scenario.actions[i]
                
                # Log detailed results for debugging
                logger.info(f"Scenario: {scenario.name}, Action {i+1}")
                logger.info(f"Ethics score: {result.overall_score}")
                logger.info(f"Principle scores: {result.principle_scores}")
                logger.info(f"Passes: {result.passes_validation}")
                
                self.assertTrue(result.passes_validation, 
                              f"Ethical scenario {scenario.name} failed validation")
                self.assertGreaterEqual(result.overall_score, 
                                      self.validator.validation_threshold,
                                      f"Ethical score below threshold in {scenario.name}")
                
                # Check that principle scores are reasonable
                for principle in self.validator.principles:
                    self.assertIn(principle.name, result.principle_scores,
                                 f"Principle {principle.name} missing from results")
                    
                # Verify guideline compliance based on action characteristics
                if action.get("consent_required", False) and action.get("consent_obtained", False):
                    self.assertTrue(result.guideline_compliance.get("informed_consent", False),
                                   "Informed consent guideline should be satisfied")
                
                if "fairness_metrics" in action and all(v > 0.9 for v in action["fairness_metrics"].values()):
                    self.assertTrue(result.guideline_compliance.get("fairness_across_groups", False),
                                   "Fairness guideline should be satisfied")
    
    def test_unethical_scenario_detection(self):
        """Test detection of unethical scenarios."""
        for scenario in self.unethical_scenarios:
            results = []
            
            for action in scenario.actions:
                result = self.validator.validate(action, scenario.context)
                results.append(result)
            
            # Check that all actions in the scenario fail validation
            for i, result in enumerate(results):
                action = scenario.actions[i]
                
                # Log detailed results for debugging
                logger.info(f"Scenario: {scenario.name}, Action {i+1}")
                logger.info(f"Ethics score: {result.overall_score}")
                logger.info(f"Principle scores: {result.principle_scores}")
                logger.info(f"Violations: {result.violations}")
                
                self.assertFalse(result.passes_validation, 
                               f"Unethical scenario {scenario.name} incorrectly passed validation")
                self.assertLessEqual(result.overall_score, 
                                   self.validator.validation_threshold,
                                   f"Unethical score above threshold in {scenario.name}")
                
                # Check for specific principle violations
                expected_violations = scenario.expected_outcomes.get("violation_principles", [])
                for principle in expected_violations:
                    self.assertIn(principle, [v.principle for v in result.violations],
                                 f"Expected violation of {principle} not detected")
    
    def test_validation_with_thresholds(self):
        """Test validation with different thresholds."""
        # Create validators with different thresholds
        strict_validator = EthicalValidator(
            principles=self.principles,
            guidelines=self.guidelines,
            validation_threshold=0.9,
            log_validation=True
        )
        
        permissive_validator = EthicalValidator(
            principles=self.principles,
            guidelines=self.guidelines,
            validation_threshold=0.6,
            log_validation=True
        )
        
        # Test with a borderline scenario
        borderline_action = {
            "type": "recommend",
            "content": "Borderline ethical recommendation",
            "consent_required": True,
            "consent_obtained": True,
            "consent_info_quality": 0.8,
            "fairness_metrics": {
                "demographic_parity": 0.85,
                "equal_opportunity": 0.8
            },
            "harm_assessment": {
                "physical": 0.1,
                "psychological": 0.15,
                "financial": 0.2
            },
            "benefit_metrics": {
                "utility": 0.75,
                "satisfaction": 0.8
            }
        }
        
        context = {"demographics": {"group_A": 0.5, "group_B": 0.5}}
        
        # Test with different validators
        strict_result = strict_validator.validate(borderline_action, context)
        permissive_result = permissive_validator.validate(borderline_action, context)
        
        # Strict validator should reject
        self.assertFalse(strict_result.passes_validation,
                       "Strict validator should reject borderline action")
        
        # Permissive validator should accept
        self.assertTrue(permissive_result.passes_validation,
                      "Permissive validator should accept borderline action")
        
        # Scores should be the same regardless of threshold
        self.assertAlmostEqual(strict_result.overall_score, 
                             permissive_result.overall_score,
                             places=4,
                             msg="Score calculation should be independent of threshold")


class TestValueAlignment(unittest.TestCase):
    """Tests for the ValueAlignment component."""
    
    def setUp(self):
        """Set up test fixtures for ValueAlignment tests."""
        # Define value sets
        self.universal_values = ValueSet(
            name="universal_values",
            description="Core universal human values",
            values={
                "fairness": ValueMetric(
                    name="fairness",
                    description="Equal treatment and opportunity",
                    evaluation_function=lambda action, context: self._evaluate_fairness(action, context),
                    weight=0.9
                ),
                "honesty": ValueMetric(
                    name="honesty",
                    description="Truthfulness and integrity",
                    evaluation_function=lambda action, context: self._evaluate_honesty(action, context),
                    weight=0.85
                ),
                "care": ValueMetric(
                    name="care",
                    description="Concern for welfare and prevention of harm",
                    evaluation_function=lambda action, context: self._evaluate_care(action, context),
                    weight=0.9
                ),
                "liberty": ValueMetric(
                    name="liberty",
                    description="Freedom of choice and action",
                    evaluation_function=lambda action, context: self._evaluate_liberty(action, context),
                    weight=0.8
                ),
                "respect": ValueMetric(
                    name="respect",
                    description="Recognition of dignity and autonomy",
                    evaluation_function=lambda action, context: self._evaluate_respect(action, context),
                    weight=0.85
                )
            }
        )
        
        self.utilitarian_values = ValueSet(
            name="utilitarian_values",
            description="Values focused on maximizing overall utility",
            values={
                "utility_maximization": ValueMetric(
                    name="utility_maximization",
                    description="Maximizing overall benefit",
                    evaluation_function=lambda action, context: self._evaluate_utility_maximization(action, context),
                    weight=1.0
                ),
                "efficiency": ValueMetric(
                    name="efficiency",
                    description="Optimal use of resources",
                    evaluation_function=lambda action, context: self._evaluate_efficiency(action, context),
                    weight=0.9
                ),
                "impartiality": ValueMetric(
                    name="impartiality",
                    description="Equal consideration of interests",
                    evaluation_function=lambda action, context: self._evaluate_impartiality(action, context),
                    weight=0.8
                )
            }
        )
        
        # Initialize ValueAlignment
        self.universal_alignment = ValueAlignment(
            value_set=self.universal_values,
            alignment_threshold=0.8,
            log_evaluation=True
        )
        
        self.utilitarian_alignment = ValueAlignment(
            value_set=self.utilitarian_values,
            alignment_threshold=0.8,
            log_evaluation=True
        )
        
        # Generate test actions
        self.aligned_actions = self._generate_aligned_actions()
        self.misaligned_actions = self._generate_misaligned_actions()
    
    def _evaluate_fairness(self, action, context):
        """Evaluate alignment with fairness value."""
        # Check explicit fairness metrics
        if "fairness_metrics" in action:
            metrics = action["fairness_metrics"]
            
            if "demographic_parity" in metrics:
                # Closer to 1.0 is better
                demographic_parity = metrics["demographic_parity"]
                return 1.0 - abs(demographic_parity - 1.0)
            
            if "disparate_impact" in metrics:
                # Closer to 1.0 is better for disparate impact ratio
                disparate_impact = metrics["disparate_impact"]
                return 1.0 - abs(disparate_impact - 1.0)
            
            # Average available metrics
            return sum(metrics.values()) / len(metrics)
        
        # Check for disparate treatment
        if "disparate_treatment" in action:
            return 1.0 - action["disparate_treatment"]
        
        # Check for favoritism
        if "favoritism" in action:
            return 1.0 - action["favoritism"]
        
        # Default if no explicit information
        return 0.7
    
    def _evaluate_honesty(self, action, context):
        """Evaluate alignment with honesty value."""
        # Check for deception
        if "deception_level" in action:
            return 1.0 - action["deception_level"]
        
        # Check for transparency
        if "transparency_level" in action:
            return action["transparency_level"]
        
        # Check for truthfulness
        if "truthfulness" in action:
            return action["truthfulness"]
        
        # Check for action types related to honesty
        honest_actions = ["inform", "disclose", "explain", "clarify"]
        deceptive_actions = ["mislead", "deceive", "hide", "manipulate"]
        
        if action.get("type", "").lower() in honest_actions:
            return 0.9
        elif action.get("type", "").lower() in deceptive_actions:
            return 0.1
        
        # Default if no explicit information
        return 0.7
    
    def _evaluate_care(self, action, context):
        """Evaluate alignment with care value."""
        # Check harm assessment
        if "harm_assessment" in action:
            harm_levels = action["harm_assessment"]
            if harm_levels:
                avg_harm = sum(harm_levels.values()) / len(harm_levels)
                return 1.0 - avg_harm
        
        # Check benefit assessment
        if "benefit_metrics" in action:
            benefit_levels = action["benefit_metrics"]
            if benefit_levels:
                avg_benefit = sum(benefit_levels.values()) / len(benefit_levels)
                return avg_benefit
        
        # Check for harm prevention
        if "harm_prevention" in action:
            return action["harm_prevention"]
        
        # Check action types related to care
        caring_actions = ["assist", "protect", "help", "support"]
        harmful_actions = ["harm", "hurt", "damage", "injure"]
        
        if action.get("type", "").lower() in caring_actions:
            return 0.9
        elif action.get("type", "").lower() in harmful_actions:
            return 0.1
        
        # Default if no explicit information
        return 0.7
    
    def _evaluate_liberty(self, action, context):
        """Evaluate alignment with liberty value."""
        # Check for choice restriction
        if "choice_restriction" in action:
            return 1.0 - action["choice_restriction"]
        
        # Check for autonomy support
        if "autonomy_support" in action:
            return action["autonomy_support"]
        
        # Check for consent
        if "consent_obtained" in action:
            return 0.9 if action["consent_obtained"] else 0.3
        
        # Check action types related to liberty
        liberty_enhancing = ["empower", "enable", "provide_options", "inform"]
        liberty_restricting = ["force", "restrict", "limit", "coerce"]
        
        if action.get("type", "").lower() in liberty_enhancing:
            return 0.9
        elif action.get("type", "").lower() in liberty_restricting:
            return 0.1
        
        # Default if no explicit information
        return 0.7
    
    def _evaluate_respect(self, action, context):
        """Evaluate alignment with respect value."""
        # Check for dignity affirmation
        if "dignity_affirmation" in action:
            return action["dignity_affirmation"]
        
        # Check for respectful communication
        if "communication_respectfulness" in action:
            return action["communication_respectfulness"]
        
        # Check for consideration of preferences
        if "preference_consideration" in action:
            return action["preference_consideration"]
        
        # Check action types related to respect
        respectful_actions = ["acknowledge", "accommodate", "recognize", "honor"]
        disrespectful_actions = ["dismiss", "ignore", "demean", "belittle"]
        
        if action.get("type", "").lower() in respectful_actions:
            return 0.9
        elif action.get("type", "").lower() in disrespectful_actions:
            return 0.1
        
        # Default if no explicit information
        return 0.7
    
    def _evaluate_utility_maximization(self, action, context):
        """Evaluate alignment with utility maximization value."""
        # Check for explicit utility metrics
        if "utility_metrics" in action:
            metrics = action["utility_metrics"]
            
            if metrics:
                return sum(metrics.values()) / len(metrics)
        
        # Check for benefit-to-harm ratio
        if "benefit_metrics" in action and "harm_assessment" in action:
            benefit_metrics = action["benefit_metrics"]
            harm_assessment = action["harm_assessment"]
            
            if benefit_metrics and harm_assessment:
                avg_benefit = sum(benefit_metrics.values()) / len(benefit_metrics)
                avg_harm = sum(harm_assessment.values()) / len(harm_assessment)
                
                if avg_harm == 0:
                    return avg_benefit
                else:
                    return avg_benefit / (avg_benefit + avg_harm)
        
        # Check for expected utility
        if "expected_utility" in action:
            return action["expected_utility"]
        
        # Default if no explicit information
        return 0.6
    
    def _evaluate_efficiency(self, action, context):
        """Evaluate alignment with efficiency value."""
        # Check for explicit efficiency metrics
        if "efficiency_metrics" in action:
            metrics = action["efficiency_metrics"]
            
            if metrics:
                return sum(metrics.values()) / len(metrics)
        
        # Check for resource utilization
        if "resource_utilization" in action:
            return action["resource_utilization"]
        
        # Check for output-to-input ratio
        if "output_to_input_ratio" in action:
            return min(1.0, action["output_to_input_ratio"])
        
        # Default if no explicit information
        return 0.6
    
    def _evaluate_impartiality(self, action, context):
        """Evaluate alignment with impartiality value."""
        # Check for explicit impartiality metrics
        if "impartiality_metrics" in action:
            metrics = action["impartiality_metrics"]
            
            if metrics:
                return sum(metrics.values()) / len(metrics)
        
        # Check for bias level
        if "bias_level" in action:
            return 1.0 - action["bias_level"]
        
        # Check for demographic parity
        if "fairness_metrics" in action and "demographic_parity" in action["fairness_metrics"]:
            demographic_parity = action["fairness_metrics"]["demographic_parity"]
            return 1.0 - abs(demographic_parity - 1.0)
        
        # Default if no explicit information
        return 0.7
    
    def _generate_aligned_actions(self):
        """Generate actions aligned with both value sets."""
        actions = []
        
        # Healthcare recommendation with universal values
        actions.append({
            "id": "healthcare_recommendation",
            "type": "recommend",
            "content": "Personalized preventive care plan",
            "fairness_metrics": {
                "demographic_parity": 0.95,
                "disparate_impact": 0.98
            },
            "transparency_level": 0.9,
            "truthfulness": 0.95,
            "harm_assessment": {
                "physical": 0.05,
                "psychological": 0.03,
                "social": 0.02
            },
            "benefit_metrics": {
                "health_improvement": 0.8,
                "quality_of_life": 0.75,
                "longevity": 0.7
            },
            "consent_obtained": True,
            "autonomy_support": 0.9,
            "dignity_affirmation": 0.85,
            "communication_respectfulness": 0.9,
            "preference_consideration": 0.85,
            "expected_utility": 0.85,
            "utility_metrics": {
                "overall_benefit": 0.8,
                "benefit_distribution": 0.85
            },
            "efficiency_metrics": {
                "cost_effectiveness": 0.8,
                "time_efficiency": 0.85
            },
            "impartiality_metrics": {
                "equal_consideration": 0.9,
                "unbiased_assessment": 0.85
            }
        })
        
        # Educational resource allocation with universal values
        actions.append({
            "id": "educational_resource",
            "type": "distribute",
            "content": "Equitable distribution of educational resources",
            "fairness_metrics": {
                "demographic_parity": 0.97,
                "disparate_impact": 0.96
            },
            "transparency_level": 0.95,
            "truthfulness": 0.9,
            "harm_assessment": {
                "psychological": 0.05,
                "social": 0.1
            },
            "benefit_metrics": {
                "learning_outcomes": 0.8,
                "opportunity_creation": 0.85
            },
            "choice_restriction": 0.1,
            "autonomy_support": 0.85,
            "dignity_affirmation": 0.9,
            "communication_respectfulness": 0.9,
            "expected_utility": 0.9,
            "utility_metrics": {
                "overall_benefit": 0.85,
                "benefit_distribution": 0.9
            },
            "efficiency_metrics": {
                "resource_utilization": 0.85,
                "outcome_efficiency": 0.8
            },
            "impartiality_metrics": {
                "equal_consideration": 0.95,
                "unbiased_assessment": 0.9
            }
        })
        
        # Environmental policy recommendation with utilitarian values
        actions.append({
            "id": "environmental_policy",
            "type": "recommend",
            "content": "Carbon tax policy recommendation",
            "fairness_metrics": {
                "demographic_parity": 0.85,
                "disparate_impact": 0.8
            },
            "transparency_level": 0.9,
            "harm_assessment": {
                "financial": 0.2,
                "social": 0.15
            },
            "benefit_metrics": {
                "environmental_protection": 0.9,
                "long_term_sustainability": 0.85
            },
            "expected_utility": 0.9,
            "utility_metrics": {
                "overall_benefit": 0.9,
                "long_term_utility": 0.95
            },
            "efficiency_metrics": {
                "cost_effectiveness": 0.85,
                "implementation_efficiency": 0.8,
                "output_to_input_ratio": 3.5
            },
            "impartiality_metrics": {
                "equal_consideration": 0.8,
                "intergenerational_equity": 0.9
            }
        })
        
        return actions
    
    def _generate_misaligned_actions(self):
        """Generate actions misaligned with one or both value sets."""
        actions = []
        
        # Deceptive marketing tactic (misaligned with universal values)
        actions.append({
            "id": "deceptive_marketing",
            "type": "advertise",
            "content": "Misleading product advertisement",
            "fairness_metrics": {
                "demographic_parity": 0.9
            },
            "deception_level": 0.7,
            "transparency_level": 0.3,
            "truthfulness": 0.4,
            "harm_assessment": {
                "financial": 0.3,
                "psychological": 0.2
            },
            "benefit_metrics": {
                "company_profit": 0.9,
                "short_term_sales": 0.8
            },
            "choice_restriction": 0.2,
            "expected_utility": 0.7,
            "utility_metrics": {
                "company_benefit": 0.9,
                "consumer_benefit": 0.3
            },
            "efficiency_metrics": {
                "marketing_efficiency": 0.9,
                "conversion_rate": 0.8
            },
            "misalignment_category": "universal_values",
            "expected_misaligned_values": ["honesty", "care", "respect"]
        })
        
        # Discriminatory algorithm (misaligned with both value sets)
        actions.append({
            "id": "discriminatory_algorithm",
            "type": "classify",
            "content": "Biased classification algorithm",
            "fairness_metrics": {
                "demographic_parity": 0.5,
                "disparate_impact": 0.4
            },
            "transparency_level": 0.4,
            "harm_assessment": {
                "social": 0.6,
                "psychological": 0.5,
                "financial": 0.7
            },
            "benefit_metrics": {
                "system_efficiency": 0.8
            },
            "bias_level": 0.7,
            "utility_metrics": {
                "overall_benefit": 0.5,
                "benefit_distribution": 0.3
            },
            "impartiality_metrics": {
                "equal_consideration": 0.3,
                "unbiased_assessment": 0.2
            },
            "misalignment_category": "both",
            "expected_misaligned_values": ["fairness", "care", "impartiality"]
        })
        
        # Inefficient resource allocation (misaligned with utilitarian values)
        actions.append({
            "id": "inefficient_allocation",
            "type": "distribute",
            "content": "Inefficient and wasteful resource allocation",
            "fairness_metrics": {
                "demographic_parity": 0.9,
                "disparate_impact": 0.85
            },
            "transparency_level": 0.8,
            "truthfulness": 0.9,
            "benefit_metrics": {
                "immediate_satisfaction": 0.7
            },
            "expected_utility": 0.4,
            "utility_metrics": {
                "overall_benefit": 0.4,
                "long_term_utility": 0.3
            },
            "efficiency_metrics": {
                "resource_utilization": 0.3,
                "outcome_efficiency": 0.2,
                "output_to_input_ratio": 0.4
            },
            "misalignment_category": "utilitarian_values",
            "expected_misaligned_values": ["utility_maximization", "efficiency"]
        })
        
        return actions
    
    def test_universal_value_alignment(self):
        """Test alignment with universal values."""
        # Test with aligned actions
        for action in self.aligned_actions:
            context = {"domain": "general"}
            result = self.universal_alignment.evaluate(action, context)
            
            # Log detailed results for debugging
            logger.info(f"Action ID: {action.get('id', 'unknown')}")
            logger.info(f"Universal alignment score: {result.overall_score}")
            logger.info(f"Value scores: {result.value_scores}")
            
            # All aligned actions should be aligned with universal values
            self.assertTrue(result.is_aligned, 
                          f"Action {action.get('id', 'unknown')} should be aligned with universal values")
            self.assertGreaterEqual(result.overall_score, 
                                  self.universal_alignment.alignment_threshold,
                                  f"Alignment score below threshold for {action.get('id', 'unknown')}")
        
        # Test with misaligned actions
        for action in self.misaligned_actions:
            context = {"domain": "general"}
            result = self.universal_alignment.evaluate(action, context)
            
            # Log detailed results for debugging
            logger.info(f"Action ID: {action.get('id', 'unknown')}")
            logger.info(f"Universal alignment score: {result.overall_score}")
            logger.info(f"Value scores: {result.value_scores}")
            
            # Check if the action is expected to be misaligned with universal values
            misalignment_category = action.get("misalignment_category", "")
            
            if misalignment_category in ["universal_values", "both"]:
                self.assertFalse(result.is_aligned, 
                               f"Action {action.get('id', 'unknown')} should be misaligned with universal values")
                self.assertLessEqual(result.overall_score, 
                                   self.universal_alignment.alignment_threshold,
                                   f"Alignment score above threshold for {action.get('id', 'unknown')}")
                
                # Check specific misaligned values
                expected_misaligned = action.get("expected_misaligned_values", [])
                for value in expected_misaligned:
                    if value in self.universal_values.values:
                        self.assertLessEqual(result.value_scores.get(value, 1.0), 0.7,
                                          f"Value {value} should be misaligned")
    
    def test_utilitarian_value_alignment(self):
        """Test alignment with utilitarian values."""
        # Test with aligned actions
        for action in self.aligned_actions:
            context = {"domain": "general"}
            result = self.utilitarian_alignment.evaluate(action, context)
            
            # Log detailed results for debugging
            logger.info(f"Action ID: {action.get('id', 'unknown')}")
            logger.info(f"Utilitarian alignment score: {result.overall_score}")
            logger.info(f"Value scores: {result.value_scores}")
            
            # All aligned actions should be aligned with utilitarian values
            self.assertTrue(result.is_aligned, 
                          f"Action {action.get('id', 'unknown')} should be aligned with utilitarian values")
            self.assertGreaterEqual(result.overall_score, 
                                  self.utilitarian_alignment.alignment_threshold,
                                  f"Alignment score below threshold for {action.get('id', 'unknown')}")
        
        # Test with misaligned actions
        for action in self.misaligned_actions:
            context = {"domain": "general"}
            result = self.utilitarian_alignment.evaluate(action, context)
            
            # Log detailed results for debugging
            logger.info(f"Action ID: {action.get('id', 'unknown')}")
            logger.info(f"Utilitarian alignment score: {result.overall_score}")
            logger.info(f"Value scores: {result.value_scores}")
            
            # Check if the action is expected to be misaligned with utilitarian values
            misalignment_category = action.get("misalignment_category", "")
            
            if misalignment_category in ["utilitarian_values", "both"]:
                self.assertFalse(result.is_aligned, 
                               f"Action {action.get('id', 'unknown')} should be misaligned with utilitarian values")
                self.assertLessEqual(result.overall_score, 
                                   self.utilitarian_alignment.alignment_threshold,
                                   f"Alignment score above threshold for {action.get('id', 'unknown')}")
                
                # Check specific misaligned values
                expected_misaligned = action.get("expected_misaligned_values", [])
                for value in expected_misaligned:
                    if value in self.utilitarian_values.values:
                        self.assertLessEqual(result.value_scores.get(value, 1.0), 0.7,
                                          f"Value {value} should be misaligned")
    
    def test_value_weighting(self):
        """Test impact of value weights on alignment score."""
        # Create a value set with different weights
        weighted_values = ValueSet(
            name="weighted_values",
            description="Values with varied weights",
            values={
                "highly_weighted": ValueMetric(
                    name="highly_weighted",
                    description="A highly weighted value",
                    evaluation_function=lambda a, c: 0.5,  # Always returns 0.5
                    weight=0.9
                ),
                "lowly_weighted": ValueMetric(
                    name="lowly_weighted",
                    description="A lowly weighted value",
                    evaluation_function=lambda a, c: 0.9,  # Always returns 0.9
                    weight=0.3
                )
            }
        )
        
        # Initialize ValueAlignment with weighted values
        weighted_alignment = ValueAlignment(
            value_set=weighted_values,
            alignment_threshold=0.7,
            log_evaluation=True
        )
        
        # Test action
        action = {"type": "test"}
        context = {}
        
        result = weighted_alignment.evaluate(action, context)
        
        # The highly weighted value (score 0.5) should have more impact than
        # the lowly weighted value (score 0.9)
        self.assertLess(result.overall_score, 0.7,
                      "Highly weighted low-scoring value should pull down overall score")
        
        # Calculate expected weighted average
        expected_score = (0.5 * 0.9 + 0.9 * 0.3) / (0.9 + 0.3)
        self.assertAlmostEqual(
            result.overall_score, 
            expected_score, 
            places=4,
            msg="Overall score should be weighted average of value scores"
        )


class TestFairnessEvaluator(unittest.TestCase):
    """Tests for the FairnessEvaluator component."""
    
    def setUp(self):
        """Set up test fixtures for FairnessEvaluator tests."""
        # Initialize FairnessEvaluator with various metrics
        self.fairness_evaluator = FairnessEvaluator(
            metrics=[
                FairnessMetric(
                    name="demographic_parity",
                    category="group_fairness",
                    computation_function=self._compute_demographic_parity,
                    threshold=0.8,
                    weight=0.8
                ),
                FairnessMetric(
                    name="equal_opportunity",
                    category="group_fairness",
                    computation_function=self._compute_equal_opportunity,
                    threshold=0.8,
                    weight=0.8
                ),
                FairnessMetric(
                    name="disparate_impact",
                    category="group_fairness",
                    computation_function=self._compute_disparate_impact,
                    threshold=0.8,
                    weight=0.7
                ),
                FairnessMetric(
                    name="individual_fairness",
                    category="individual_fairness",
                    computation_function=self._compute_individual_fairness,
                    threshold=0.85,
                    weight=0.7
                ),
                FairnessMetric(
                    name="counterfactual_fairness",
                    category="counterfactual_fairness",
                    computation_function=self._compute_counterfactual_fairness,
                    threshold=0.8,
                    weight=0.6
                )
            ],
            sensitive_attributes=["gender", "race", "age_group"],
            fairness_threshold=0.8,
            log_evaluations=True
        )
        
        # Create BiasDetector for testing bias detection
        self.bias_detector = BiasDetector(
            detection_methods=[
                "statistical_parity",
                "disparate_impact",
                "equal_odds"
            ],
            threshold=0.2,
            log_detections=True
        )
        
        # Generate test data
        self.fair_test_cases = self._generate_fair_test_cases()
        self.unfair_test_cases = self._generate_unfair_test_cases()
    
    def _compute_demographic_parity(self, predictions, labels, sensitive_attrs, **kwargs):
        """
        Compute demographic parity difference.
        
        Demographic parity: P(Ŷ=1|A=a) should be the same for all groups a.
        Difference of 0 is perfectly fair, larger values indicate bias.
        """
        # Get groups from first sensitive attribute
        if not sensitive_attrs:
            return 1.0  # No sensitive attributes provided
            
        attr_name = next(iter(sensitive_attrs.keys()))
        groups = sensitive_attrs[attr_name]
        
        unique_groups = np.unique(groups)
        if len(unique_groups) < 2:
            return 1.0  # Only one group
        
        # Calculate positive prediction rate for each group
        prediction_rates = {}
        for group in unique_groups:
            group_mask = (groups == group)
            if np.sum(group_mask) == 0:
                continue
                
            group_predictions = predictions[group_mask]
            prediction_rates[group] = np.mean(group_predictions > 0.5)
        
        if len(prediction_rates) < 2:
            return 1.0  # Not enough groups with data
        
        # Calculate maximum difference between any two groups
        rates = list(prediction_rates.values())
        max_diff = max(rates) - min(rates)
        
        # Convert to fairness score (0 to 1, higher is better)
        fairness_score = max(0.0, 1.0 - max_diff / 0.4)  # Scale the difference
        return min(1.0, fairness_score)
    
    def _compute_equal_opportunity(self, predictions, labels, sensitive_attrs, **kwargs):
        """
        Compute equal opportunity difference.
        
        Equal opportunity: P(Ŷ=1|Y=1,A=a) should be the same for all groups a.
        Difference of 0 is perfectly fair, larger values indicate bias.
        """
        # Get groups from first sensitive attribute
        if not sensitive_attrs:
            return 1.0  # No sensitive attributes provided
            
        attr_name = next(iter(sensitive_attrs.keys()))
        groups = sensitive_attrs[attr_name]
        
        unique_groups = np.unique(groups)
        if len(unique_groups) < 2:
            return 1.0  # Only one group
        
        # Calculate true positive rate for each group
        tpr_rates = {}
        for group in unique_groups:
            group_mask = (groups == group)
            positive_mask = (labels == 1)
            
            # Get positives in this group
            group_positives_mask = group_mask & positive_mask
            
            if np.sum(group_positives_mask) == 0:
                continue
                
            group_positive_predictions = predictions[group_positives_mask]
            tpr_rates[group] = np.mean(group_positive_predictions > 0.5)
        
        if len(tpr_rates) < 2:
            return 1.0  # Not enough groups with data
        
        # Calculate maximum difference between any two groups
        rates = list(tpr_rates.values())
        max_diff = max(rates) - min(rates)
        
        # Convert to fairness score (0 to 1, higher is better)
        fairness_score = max(0.0, 1.0 - max_diff / 0.4)  # Scale the difference
        return min(1.0, fairness_score)
    
    def _compute_disparate_impact(self, predictions, labels, sensitive_attrs, **kwargs):
        """
        Compute disparate impact ratio.
        
        Disparate impact: min(P(Ŷ=1|A=a) / P(Ŷ=1|A=b))
        Score of 1.0 is perfectly fair, lower values indicate bias.
        """
        # Get groups from first sensitive attribute
        if not sensitive_attrs:
            return 1.0  # No sensitive attributes provided
            
        attr_name = next(iter(sensitive_attrs.keys()))
        groups = sensitive_attrs[attr_name]
        
        unique_groups = np.unique(groups)
        if len(unique_groups) < 2:
            return 1.0  # Only one group
        
        # Calculate positive prediction rate for each group
        prediction_rates = {}
        for group in unique_groups:
            group_mask = (groups == group)
            if np.sum(group_mask) == 0:
                continue
                
            group_predictions = predictions[group_mask]
            prediction_rates[group] = np.mean(group_predictions > 0.5)
        
        if len(prediction_rates) < 2:
            return 1.0  # Not enough groups with data
        
        # Calculate minimum ratio between any two groups
        rates = list(prediction_rates.values())
        
        # Avoid division by zero
        min_rate = min(rates)
        if min_rate == 0:
            return 0.0
            
        max_rate = max(rates)
        disparate_impact_ratio = min_rate / max_rate
        
        return disparate_impact_ratio
    
    def _compute_individual_fairness(self, predictions, features, **kwargs):
        """
        Compute individual fairness score using similarity-based approach.
        
        Individual fairness: similar individuals should receive similar predictions.
        """
        if len(predictions) < 2:
            return 1.0  # Not enough data points
            
        # Compute pairwise distances between samples
        try:
            from sklearn.metrics.pairwise import euclidean_distances
            
            # Normalize features to [0, 1] range
            feature_mins = np.min(features, axis=0)
            feature_maxs = np.max(features, axis=0)
            feature_range = feature_maxs - feature_mins
            feature_range[feature_range == 0] = 1.0  # Avoid division by zero
            
            normalized_features = (features - feature_mins) / feature_range
            
            # Compute pairwise distances
            distances = euclidean_distances(normalized_features)
            
            # Compute pairwise prediction differences
            pred_diff = np.abs(predictions.reshape(-1, 1) - predictions.reshape(1, -1))
            
            # Calculate correlation between feature distance and prediction difference
            # Flatten the matrices (excluding diagonal elements)
            mask = ~np.eye(distances.shape[0], dtype=bool)
            flat_distances = distances[mask]
            flat_pred_diff = pred_diff[mask]
            
            # Normalize distances to [0, 1]
            max_dist = np.max(flat_distances)
            if max_dist > 0:
                flat_distances = flat_distances / max_dist
            
            # Calculate Lipschitz ratio: max(pred_diff / distance)
            # Lower ratio is better (more individually fair)
            with np.errstate(divide='ignore', invalid='ignore'):
                ratios = flat_pred_diff / flat_distances
            
            # Filter out infs and nans
            valid_ratios = ratios[np.isfinite(ratios)]
            
            if len(valid_ratios) == 0:
                return 0.5  # Default if no valid ratios
            
            lipschitz_constant = np.percentile(valid_ratios, 90)  # Use 90th percentile as robustness
            
            # Convert to fairness score (0 to 1, higher is better)
            # Lipschitz constant of 1.0 or less is ideal
            fairness_score = max(0.0, 1.0 - lipschitz_constant / 2.0)
            return min(1.0, fairness_score)
            
        except Exception as e:
            logger.error(f"Error computing individual fairness: {e}")
            return 0.5  # Default fallback
    
    def _compute_counterfactual_fairness(self, model, features, sensitive_attrs, **kwargs):
        """
        Compute counterfactual fairness score using causal model approach.
        
        Counterfactual fairness: P(Ŷ(A←a) | X=x, A=a) = P(Ŷ(A←a') | X=x, A=a)
        """
        # This is a simplified implementation of counterfactual fairness
        # In a real implementation, this would use a causal model
        
        if not model or not sensitive_attrs:
            return 0.5  # Cannot compute without model or sensitive attributes
        
        try:
            # Get first sensitive attribute
            attr_name = next(iter(sensitive_attrs.keys()))
            attr_values = sensitive_attrs[attr_name]
            
            # Get unique values of the sensitive attribute
            unique_values = np.unique(attr_values)
            if len(unique_values) < 2:
                return 1.0  # Only one group
            
            # Create counterfactual versions of the features
            counterfactual_predictions = []
            original_predictions = []
            
            # Find the index of the sensitive attribute in features
            # This assumes the sensitive attribute is included in features
            # In a real implementation, we would use a causal model
            attr_index = kwargs.get('attr_index', -1)
            
            if attr_index < 0 or attr_index >= features.shape[1]:
                return 0.5  # Invalid attribute index
            
            # For each sample
            for i in range(len(features)):
                # Original prediction
                orig_pred = model.predict_proba(features[i:i+1])[0][1]
                original_predictions.append(orig_pred)
                
                # For each unique value of the sensitive attribute
                cf_preds = []
                for value in unique_values:
                    # Skip if this is already the value for this sample
                    if attr_values[i] == value:
                        cf_preds.append(orig_pred)
                        continue
                    
                    # Create counterfactual
                    cf_features = features[i:i+1].copy()
                    cf_features[0, attr_index] = value
                    
                    # Get prediction for counterfactual
                    cf_pred = model.predict_proba(cf_features)[0][1]
                    cf_preds.append(cf_pred)
                
                # Calculate average difference across counterfactuals
                if cf_preds:
                    avg_diff = np.mean(np.abs(np.array(cf_preds) - orig_pred))
                    counterfactual_predictions.append(avg_diff)
            
            # Calculate average counterfactual difference
            if counterfactual_predictions:
                avg_cf_diff = np.mean(counterfactual_predictions)
                
                # Convert to fairness score (0 to 1, higher is better)
                fairness_score = max(0.0, 1.0 - avg_cf_diff / 0.4)  # Scale the difference
                return min(1.0, fairness_score)
            
            return 0.5  # Default if no counterfactual predictions
            
        except Exception as e:
            logger.error(f"Error computing counterfactual fairness: {e}")
            return 0.5  # Default fallback
    
    def _generate_fair_test_cases(self):
        """Generate fair test cases for fairness evaluation."""
        test_cases = []
        
        # Fair binary classification case
        n_samples = 1000
        
        # Generate features
        features = np.random.normal(0, 1, (n_samples, 5))
        
        # Generate sensitive attributes
        gender = np.random.choice(['male', 'female'], size=n_samples)
        gender_numeric = np.array([1 if g == 'male' else 0 for g in gender])
        
        race = np.random.choice(['white', 'black', 'asian', 'hispanic'], size=n_samples)
        race_numeric = np.array([0 if r == 'white' else 
                                1 if r == 'black' else
                                2 if r == 'asian' else 3 
                                for r in race])
        
        age_group = np.random.choice(['young', 'middle', 'senior'], size=n_samples)
        age_numeric = np.array([0 if a == 'young' else 
                               1 if a == 'middle' else 2 
                               for a in age_group])
        
        # Add sensitive attributes to features
        features_with_sensitive = np.column_stack([features, gender_numeric, race_numeric, age_numeric])
        
        # Generate fair labels (independent of sensitive attributes)
        latent_scores = 0.2 * features[:, 0] + 0.3 * features[:, 1] + 0.1 * features[:, 2]
        probs = 1 / (1 + np.exp(-latent_scores))
        labels = np.random.binomial(1, probs)
        
        # Train a logistic regression model (fair model)
        model = LogisticRegression()
        model.fit(features, labels)
        
        # Generate fair predictions
        predictions = model.predict_proba(features)[:, 1]
        
        test_cases.append(FairnessTestCase(
            name="fair_binary_classification",
            features=features,
            labels=labels,
            sensitive_attributes={
                "gender": gender,
                "race": race,
                "age_group": age_group
            },
            model=model,
            expected_metrics={
                "demographic_parity": 0.9,
                "equal_opportunity": 0.9,
                "disparate_impact": 0.9,
                "individual_fairness": 0.9
            }
        ))
        
        # Fair regression case
        n_samples = 1000
        
        # Generate features
        features = np.random.normal(0, 1, (n_samples, 5))
        
        # Generate sensitive attributes
        gender = np.random.choice(['male', 'female'], size=n_samples)
        gender_numeric = np.array([1 if g == 'male' else 0 for g in gender])
        
        # Generate fair continuous labels (independent of sensitive attributes)
        latent_values = 2.0 * features[:, 0] + 1.5 * features[:, 1] + 0.5 * features[:, 2] + np.random.normal(0, 0.5, n_samples)
        
        # Normalize to [0, 1]
        min_val = np.min(latent_values)
        max_val = np.max(latent_values)
        normalized_labels = (latent_values - min_val) / (max_val - min_val)
        
        # Discretize for evaluation purposes
        discrete_labels = (normalized_labels > 0.5).astype(int)
        
        # Train a fair regression model
        from sklearn.linear_model import LinearRegression
        model = LinearRegression()
        model.fit(features, normalized_labels)
        
        # Generate fair predictions
        predictions = model.predict(features)
        
        test_cases.append(FairnessTestCase(
            name="fair_regression",
            features=features,
            labels=discrete_labels,  # Use discretized labels for evaluation
            sensitive_attributes={
                "gender": gender
            },
            model=model,
            expected_metrics={
                "demographic_parity": 0.9,
                "equal_opportunity": 0.9,
                "disparate_impact": 0.9
            }
        ))
        
        return test_cases
    
    def _generate_unfair_test_cases(self):
        """Generate unfair test cases for fairness evaluation."""
        test_cases = []
        
        # Unfair binary classification case with demographic disparity
        n_samples = 1000
        
        # Generate features
        features = np.random.normal(0, 1, (n_samples, 5))
        
        # Generate sensitive attributes
        gender = np.random.choice(['male', 'female'], size=n_samples)
        gender_numeric = np.array([1 if g == 'male' else 0 for g in gender])
        
        race = np.random.choice(['white', 'black', 'asian', 'hispanic'], size=n_samples)
        race_numeric = np.array([0 if r == 'white' else 
                                1 if r == 'black' else
                                2 if r == 'asian' else 3 
                                for r in race])
        
        # Add sensitive attributes to features
        features_with_sensitive = np.column_stack([features, gender_numeric, race_numeric])
        
        # Generate unfair labels (dependent on sensitive attributes)
        # Gender bias: males are more likely to get positive outcomes
        gender_bias = 1.0 * (gender_numeric == 1)
        
        # Race bias: certain races are less likely to get positive outcomes
        race_bias = -0.8 * (race_numeric == 1)
        
        latent_scores = 0.2 * features[:, 0] + 0.3 * features[:, 1] + gender_bias + race_bias
        probs = 1 / (1 + np.exp(-latent_scores))
        labels = np.random.binomial(1, probs)
        
        # Train an unfair model that learns the bias
        model = LogisticRegression()
        model.fit(features_with_sensitive, labels)
        
        # Generate unfair predictions
        predictions = model.predict_proba(features_with_sensitive)[:, 1]
        
        test_cases.append(FairnessTestCase(
            name="unfair_demographic_parity",
            features=features_with_sensitive,
            labels=labels,
            sensitive_attributes={
                "gender": gender,
                "race": race
            },
            model=model,
            expected_metrics={
                "demographic_parity": 0.4,
                "disparate_impact": 0.5
            }
        ))
        
        # Unfair binary classification case with equal opportunity violation
        n_samples = 1000
        
        # Generate features
        features = np.random.normal(0, 1, (n_samples, 5))
        
        # Generate sensitive attributes
        gender = np.random.choice(['male', 'female'], size=n_samples)
        gender_numeric = np.array([1 if g == 'male' else 0 for g in gender])
        
        # Generate fair base labels
        latent_scores = 0.2 * features[:, 0] + 0.3 * features[:, 1] + 0.1 * features[:, 2]
        probs = 1 / (1 + np.exp(-latent_scores))
        fair_labels = np.random.binomial(1, probs)
        
        # Introduce bias in predictions: different error rates for different groups
        # Female positive examples are more likely to be misclassified (false negatives)
        female_mask = (gender_numeric == 0)
        positive_mask = (fair_labels == 1)
        female_positive_mask = female_mask & positive_mask
        
        # Create unfair labels by flipping some positive female examples to negative
        biased_labels = fair_labels.copy()
        flip_indices = np.where(female_positive_mask)[0]
        flip_count = int(len(flip_indices) * 0.4)  # Flip 40% of positive female examples
        
        if flip_count > 0:
            flip_indices = np.random.choice(flip_indices, flip_count, replace=False)
            biased_labels[flip_indices] = 0
        
        # Train a model on the biased labels
        features_with_sensitive = np.column_stack([features, gender_numeric])
        model = LogisticRegression()
        model.fit(features_with_sensitive, biased_labels)
        
        # Generate unfair predictions
        predictions = model.predict_proba(features_with_sensitive)[:, 1]
        
        test_cases.append(FairnessTestCase(
            name="unfair_equal_opportunity",
            features=features_with_sensitive,
            labels=biased_labels,
            sensitive_attributes={
                "gender": gender
            },
            model=model,
            expected_metrics={
                "demographic_parity": 0.7,
                "equal_opportunity": 0.5
            }
        ))
        
        return test_cases
    
    def test_fair_model_evaluation(self):
        """Test fairness evaluation on fair models."""
        for test_case in self.fair_test_cases:
            # Convert sensitive attributes to numeric for evaluation
            sensitive_attrs_numeric = {}
            
            for attr_name, attr_values in test_case.sensitive_attributes.items():
                if attr_values.dtype.kind not in ['i', 'f']:
                    # Convert categorical to numeric
                    unique_values = np.unique(attr_values)
                    value_map = {val: i for i, val in enumerate(unique_values)}
                    numeric_values = np.array([value_map[val] for val in attr_values])
                    sensitive_attrs_numeric[attr_name] = numeric_values
                else:
                    sensitive_attrs_numeric[attr_name] = attr_values
            
            # Evaluate fairness
            fairness_result = self.fairness_evaluator.evaluate(
                predictions=test_case.model.predict_proba(test_case.features)[:, 1] 
                    if hasattr(test_case.model, 'predict_proba') else test_case.model.predict(test_case.features),
                labels=test_case.labels,
                features=test_case.features,
                sensitive_attributes=test_case.sensitive_attributes,
                model=test_case.model
            )
            
            # Log detailed results for debugging
            logger.info(f"Test case: {test_case.name}")
            logger.info(f"Overall fairness score: {fairness_result.overall_score}")
            logger.info(f"Metric scores: {fairness_result.metric_scores}")
            logger.info(f"Groups: {fairness_result.group_metrics}")
            
            # Check that the model is classified as fair
            self.assertTrue(fairness_result.is_fair, 
                          f"Fair model {test_case.name} incorrectly classified as unfair")
            self.assertGreaterEqual(fairness_result.overall_score, 
                                  self.fairness_evaluator.fairness_threshold,
                                  f"Fair model {test_case.name} has fairness score below threshold")
            
            # Check that individual metrics match expectations
            for metric_name, expected_score in test_case.expected_metrics.items():
                if metric_name in fairness_result.metric_scores:
                    self.assertGreaterEqual(fairness_result.metric_scores[metric_name], 
                                          expected_score - 0.2,  # Allow some deviation
                                          f"Metric {metric_name} score below expectation in {test_case.name}")
    
    def test_unfair_model_evaluation(self):
        """Test fairness evaluation on unfair models."""
        for test_case in self.unfair_test_cases:
            # Convert sensitive attributes to numeric for evaluation
            sensitive_attrs_numeric = {}
            
            for attr_name, attr_values in test_case.sensitive_attributes.items():
                if attr_values.dtype.kind not in ['i', 'f']:
                    # Convert categorical to numeric
                    unique_values = np.unique(attr_values)
                    value_map = {val: i for i, val in enumerate(unique_values)}
                    numeric_values = np.array([value_map[val] for val in attr_values])
                    sensitive_attrs_numeric[attr_name] = numeric_values
                else:
                    sensitive_attrs_numeric[attr_name] = attr_values
            
            # Evaluate fairness
            fairness_result = self.fairness_evaluator.evaluate(
                predictions=test_case.model.predict_proba(test_case.features)[:, 1] 
                    if hasattr(test_case.model, 'predict_proba') else test_case.model.predict(test_case.features),
                labels=test_case.labels,
                features=test_case.features,
                sensitive_attributes=test_case.sensitive_attributes,
                model=test_case.model
            )
            
            # Log detailed results for debugging
            logger.info(f"Test case: {test_case.name}")
            logger.info(f"Overall fairness score: {fairness_result.overall_score}")
            logger.info(f"Metric scores: {fairness_result.metric_scores}")
            logger.info(f"Groups: {fairness_result.group_metrics}")
            logger.info(f"Bias detected: {fairness_result.bias_detected}")
            logger.info(f"Bias metrics: {fairness_result.bias_metrics}")
            
            # Check that the model is classified as unfair
            self.assertFalse(fairness_result.is_fair, 
                           f"Unfair model {test_case.name} incorrectly classified as fair")
            self.assertLessEqual(fairness_result.overall_score, 
                               self.fairness_evaluator.fairness_threshold,
                               f"Unfair model {test_case.name} has fairness score above threshold")
            
            # Check that biased metrics match expectations
            for metric_name, expected_score in test_case.expected_metrics.items():
                if metric_name in fairness_result.metric_scores:
                    self.assertLessEqual(fairness_result.metric_scores[metric_name], 
                                       expected_score + 0.2,  # Allow some deviation
                                       f"Metric {metric_name} score above expectation in {test_case.name}")
            
            # Check that bias is detected
            self.assertTrue(fairness_result.bias_detected,
                          f"Bias not detected in unfair model {test_case.name}")
    
    def test_bias_detection(self):
        """Test bias detection functionality."""
        for test_case in self.unfair_test_cases:
            # Convert sensitive attributes to numeric for evaluation
            sensitive_attrs_numeric = {}
            
            for attr_name, attr_values in test_case.sensitive_attributes.items():
                if attr_values.dtype.kind not in ['i', 'f']:
                    # Convert categorical to numeric
                    unique_values = np.unique(attr_values)
                    value_map = {val: i for i, val in enumerate(unique_values)}
                    numeric_values = np.array([value_map[val] for val in attr_values])
                    sensitive_attrs_numeric[attr_name] = numeric_values
                else:
                    sensitive_attrs_numeric[attr_name] = attr_values
            
            # Get predictions
            predictions = test_case.model.predict_proba(test_case.features)[:, 1] \
                if hasattr(test_case.model, 'predict_proba') else test_case.model.predict(test_case.features)
            
            # Detect bias
            bias_metrics = BiasMetrics()
            
            # Test each sensitive attribute
            for attr_name, attr_values in test_case.sensitive_attributes.items():
                if attr_values.dtype.kind not in ['i', 'f']:
                    # Convert categorical to numeric
                    unique_values = np.unique(attr_values)
                    value_map = {val: i for i, val in enumerate(unique_values)}
                    numeric_values = np.array([value_map[val] for val in attr_values])
                else:
                    numeric_values = attr_values
                
                # Detect bias for this attribute
                detection_result = self.bias_detector.detect_bias(
                    predictions=predictions,
                    labels=test_case.labels,
                    sensitive_attribute=numeric_values,
                    attribute_name=attr_name
                )
                
                # Update bias metrics
                bias_metrics.update(detection_result)
            
            # Log detailed results for debugging
            logger.info(f"Test case: {test_case.name}")
            logger.info(f"Bias detected: {bias_metrics.bias_detected}")
            logger.info(f"Bias detection results: {bias_metrics.detection_results}")
            
            # Check that bias is detected
            self.assertTrue(bias_metrics.bias_detected,
                          f"Bias not detected in unfair model {test_case.name}")
            
            # Check that specific attributes have detected bias
            if "gender" in test_case.sensitive_attributes:
                self.assertIn("gender", bias_metrics.detection_results,
                            f"Gender bias not detected in {test_case.name}")
            
            if "race" in test_case.sensitive_attributes:
                self.assertIn("race", bias_metrics.detection_results,
                            f"Race bias not detected in {test_case.name}")
    
    def test_bias_mitigation_recommendation(self):
        """Test bias mitigation recommendation functionality."""
        for test_case in self.unfair_test_cases:
            # Evaluate fairness and detect bias
            fairness_result = self.fairness_evaluator.evaluate(
                predictions=test_case.model.predict_proba(test_case.features)[:, 1] 
                    if hasattr(test_case.model, 'predict_proba') else test_case.model.predict(test_case.features),
                labels=test_case.labels,
                features=test_case.features,
                sensitive_attributes=test_case.sensitive_attributes,
                model=test_case.model
            )
            
            # Get mitigation recommendations
            recommendations = self.fairness_evaluator.recommend_mitigations(fairness_result)
            
            # Log detailed results for debugging
            logger.info(f"Test case: {test_case.name}")
            logger.info(f"Mitigation recommendations: {recommendations}")
            
            # Check that recommendations are provided
            self.assertTrue(len(recommendations) > 0,
                          f"No mitigation recommendations for {test_case.name}")
            
            # Check for specific mitigation techniques based on the type of bias
            if "demographic_parity" in test_case.expected_metrics and \
               test_case.expected_metrics["demographic_parity"] < 0.7:
                found_relevant_technique = False
                for rec in recommendations:
                    if any(technique in rec.technique for technique in 
                          ["reweighing", "disparate impact remover", "preprocessing"]):
                        found_relevant_technique = True
                        break
                
                self.assertTrue(found_relevant_technique,
                              f"No relevant mitigation technique for demographic parity in {test_case.name}")
            
            if "equal_opportunity" in test_case.expected_metrics and \
               test_case.expected_metrics["equal_opportunity"] < 0.7:
                found_relevant_technique = False
                for rec in recommendations:
                    if any(technique in rec.technique for technique in 
                          ["equalized odds", "reject option", "threshold optimizer"]):
                        found_relevant_technique = True
                        break
                
                self.assertTrue(found_relevant_technique,
                              f"No relevant mitigation technique for equal opportunity in {test_case.name}")


class TestTransparencyManager(unittest.TestCase):
    """Tests for the TransparencyManager component."""
    
    def setUp(self):
        """Set up test fixtures for TransparencyManager tests."""
        # Initialize ExplanationMethods
        self.explanation_methods = [
            ExplanationMethod(
                name="feature_importance",
                description="Explains model predictions using feature importance",
                implementation=self._generate_feature_importance,
                supported_models=["tree", "linear", "neural_network"],
                requirements=["model", "features"]
            ),
            ExplanationMethod(
                name="counterfactual",
                description="Generates counterfactual examples",
                implementation=self._generate_counterfactual,
                supported_models=["tree", "linear", "neural_network"],
                requirements=["model", "features", "prediction"]
            ),
            ExplanationMethod(
                name="rule_based",
                description="Explains model predictions using rule extraction",
                implementation=self._generate_rule_based,
                supported_models=["tree", "rule_based"],
                requirements=["model", "features"]
            ),
            ExplanationMethod(
                name="local_surrogate",
                description="Explains model predictions using local surrogate models",
                implementation=self._generate_local_surrogate,
                supported_models=["black_box"],
                requirements=["model", "features", "prediction"]
            )
        ]
        
        # Initialize model explainers
        self.model_explainers = {
            "linear": ModelExplainer(
                name="linear_explainer",
                model_types=["linear_model"],
                explanation_methods=["feature_importance", "counterfactual"],
                implementation=self._explain_linear_model
            ),
            "tree": ModelExplainer(
                name="tree_explainer",
                model_types=["decision_tree", "random_forest", "gradient_boosting"],
                explanation_methods=["feature_importance", "rule_based"],
                implementation=self._explain_tree_model
            ),
            "generic": ModelExplainer(
                name="generic_explainer",
                model_types=["any"],
                explanation_methods=["local_surrogate"],
                implementation=self._explain_generic_model
            )
        }
        
        # Initialize TransparencyManager
        self.transparency_manager = TransparencyManager(
            explanation_methods=self.explanation_methods,
            model_explainers=self.model_explainers,
            interpretability_threshold=0.7,
            log_explanations=True
        )
        
        # Generate test models, inputs, and predictions
        self.test_models = self._generate_test_models()
        self.test_inputs = self._generate_test_inputs()
        self.test_predictions = self._generate_test_predictions()
    
    def _generate_feature_importance(self, model, features, feature_names=None, **kwargs):
        """Generate feature importance explanation."""
        try:
            # For linear models
            if hasattr(model, 'coef_'):
                importance = np.abs(model.coef_[0] if model.coef_.ndim > 1 else model.coef_)
                
            # For tree-based models
            elif hasattr(model, 'feature_importances_'):
                importance = model.feature_importances_
                
            # For other models, use permutation importance
            else:
                from sklearn.inspection import permutation_importance
                
                # Get labels if provided
                labels = kwargs.get('labels')
                if labels is None:
                    # Use model to predict
                    if hasattr(model, 'predict_proba'):
                        labels = model.predict_proba(features)[:, 1] > 0.5
                    else:
                        labels = model.predict(features)
                
                # Calculate permutation importance
                result = permutation_importance(
                    model, features, labels, 
                    n_repeats=10, random_state=42
                )
                importance = result.importances_mean
            
            # Normalize importance scores
            if np.sum(importance) > 0:
                importance = importance / np.sum(importance)
            
            # Create feature names if not provided
            if feature_names is None:
                feature_names = [f"feature_{i}" for i in range(len(importance))]
            
            # Create feature importance dictionary
            feature_importance = {
                name: float(imp) for name, imp in zip(feature_names, importance)
            }
            
            # Sort by importance
            feature_importance = dict(sorted(
                feature_importance.items(), 
                key=lambda x: x[1], 
                reverse=True
            ))
            
            # Create explanation with quality score
            quality_score = min(1.0, max(0.0, 0.7 + 0.3 * (1.0 - np.std(list(feature_importance.values())))))
            
            return ExplanationResult(
                method="feature_importance",
                content={
                    "feature_importance": feature_importance,
                    "top_features": list(feature_importance.keys())[:5]
                },
                quality_score=quality_score,
                interpretability_score=InterpretabilityScore(
                    simplicity=0.8,
                    fidelity=0.9,
                    coherence=0.9
                )
            )
            
        except Exception as e:
            logger.error(f"Error generating feature importance: {e}")
            return ExplanationResult(
                method="feature_importance",
                content={"error": str(e)},
                quality_score=0.0,
                interpretability_score=InterpretabilityScore()
            )
    
    def _generate_counterfactual(self, model, features, prediction, feature_names=None, **kwargs):
        """Generate counterfactual explanation."""
        try:
            # For simplicity, we'll implement a basic counterfactual approach
            # In a real implementation, we would use a more sophisticated method
            
            # Create feature names if not provided
            if feature_names is None:
                feature_names = [f"feature_{i}" for i in range(features.shape[1])]
            
            # Get prediction threshold
            threshold = kwargs.get('threshold', 0.5)
            
            # Determine target class (opposite of current prediction)
            if hasattr(model, 'predict_proba'):
                current_prob = model.predict_proba(features.reshape(1, -1))[0][1]
                current_class = int(current_prob > threshold)
                target_class = 1 - current_class
            else:
                current_class = int(model.predict(features.reshape(1, -1))[0])
                target_class = 1 - current_class
            
            # Simple approach: perturb features one by one to see if prediction changes
            counterfactual = features.copy()
            changes = {}
            
            for i in range(len(features)):
                # Try increasing and decreasing the feature value
                directions = [1.0, -1.0]
                for direction in directions:
                    # Try different step sizes
                    for step_size in [0.1, 0.2, 0.5, 1.0, 2.0]:
                        perturbation = counterfactual.copy()
                        perturbation[i] += direction * step_size
                        
                        # Check if prediction changes
                        if hasattr(model, 'predict_proba'):
                            new_prob = model.predict_proba(perturbation.reshape(1, -1))[0][1]
                            new_class = int(new_prob > threshold)
                        else:
                            new_class = int(model.predict(perturbation.reshape(1, -1))[0])
                        
                        if new_class == target_class:
                            counterfactual = perturbation
                            changes[feature_names[i]] = (
                                float(features[i]), 
                                float(counterfactual[i]),
                                f"{'+' if direction > 0 else '-'}{step_size}"
                            )
                            break
                    
                    if feature_names[i] in changes:
                        break
            
            # Calculate sparsity (fewer changes is better)
            sparsity = 1.0 - (len(changes) / len(features))
            
            # Create explanation with quality score
            quality_score = min(1.0, max(0.0, 0.8 * sparsity + 0.2))
            
            return ExplanationResult(
                method="counterfactual",
                content={
                    "counterfactual_example": counterfactual.tolist(),
                    "original_example": features.tolist(),
                    "changes": changes,
                    "original_prediction": current_class,
                    "counterfactual_prediction": target_class
                },
                quality_score=quality_score,
                interpretability_score=InterpretabilityScore(
                    simplicity=0.7,
                    fidelity=0.9,
                    coherence=0.8
                )
            )
            
        except Exception as e:
            logger.error(f"Error generating counterfactual: {e}")
            return ExplanationResult(
                method="counterfactual",
                content={"error": str(e)},
                quality_score=0.0,
                interpretability_score=InterpretabilityScore()
            )
    
    def _generate_rule_based(self, model, features, feature_names=None, **kwargs):
        """Generate rule-based explanation."""
        try:
            # This is a simplified implementation
            # In a real system, we would extract rules from decision trees or rule-based models
            
            # Create feature names if not provided
            if feature_names is None:
                feature_names = [f"feature_{i}" for i in range(features.shape[1])]
            
            # For tree models, extract simple rules
            if hasattr(model, 'tree_'):
                # Extract decision paths (simplified)
                rules = []
                
                # For decision trees, extract rules directly
                if hasattr(model, 'tree_'):
                    n_nodes = model.tree_.node_count
                    children_left = model.tree_.children_left
                    children_right = model.tree_.children_right
                    feature = model.tree_.feature
                    threshold = model.tree_.threshold
                    
                    # Extract paths to leaf nodes
                    for leaf_id in range(n_nodes):
                        if children_left[leaf_id] == children_right[leaf_id]:  # a leaf node
                            path = []
                            node_id = leaf_id
                            
                            # Traverse from leaf to root
                            while node_id != 0:  # 0 is the root node
                                parent_id = -1
                                for i in range(n_nodes):
                                    if children_left[i] == node_id or children_right[i] == node_id:
                                        parent_id = i
                                        break
                                
                                if parent_id == -1:
                                    break
                                
                                if children_left[parent_id] == node_id:
                                    path.append((feature[parent_id], '<=', threshold[parent_id]))
                                else:
                                    path.append((feature[parent_id], '>', threshold[parent_id]))
                                
                                node_id = parent_id
                            
                            if path:
                                # Convert feature indices to names
                                named_path = [
                                    (feature_names[f], op, t) 
                                    for f, op, t in path 
                                    if f >= 0  # -2 indicates undefined feature
                                ]
                                
                                # Create a rule string
                                rule_str = " AND ".join([
                                    f"{name} {op} {float(t):.2f}" 
                                    for name, op, t in named_path
                                ])
                                
                                rules.append(rule_str)
                
                # Limit to top rules (by frequency or importance)
                rules = rules[:5]
                
            # For other models, use LIME to approximate rules
            else:
                # Import LIME
                try:
                    from lime.lime_tabular import LimeTabularExplainer
                    
                    # Get a sample to explain
                    sample = features[0] if features.ndim > 1 else features
                    
                    # Create a LIME explainer
                    explainer = LimeTabularExplainer(
                        training_data=np.random.normal(0, 1, (100, len(sample))),
                        feature_names=feature_names,
                        mode="classification" if hasattr(model, "predict_proba") else "regression"
                    )
                    
                    # Get explanation
                    exp = explainer.explain_instance(
                        sample, 
                        model.predict_proba if hasattr(model, "predict_proba") else model.predict,
                        num_features=5
                    )
                    
                    # Extract rules from explanation
                    rules = []
                    for idx, weight in exp.as_list():
                        feature_name, value_range = idx.split(" <= ")
                        if " > " in idx:
                            feature_name, value_range = idx.split(" > ")
                            op = ">"
                        else:
                            op = "<="
                        
                        rule = f"{feature_name} {op} {value_range}"
                        rules.append(rule)
                    
                except ImportError:
                    # If LIME is not available, create simple threshold rules
                    rules = []
                    for i, name in enumerate(feature_names):
                        value = features[0, i] if features.ndim > 1 else features[i]
                        rules.append(f"{name} > {value - 0.5:.2f} AND {name} < {value + 0.5:.2f}")
                    
                    rules = rules[:3]  # Limit to 3 rules
            
            # Create explanation with quality score
            quality_score = min(1.0, max(0.0, 0.7 + 0.3 * (min(5, len(rules)) / 5)))
            
            return ExplanationResult(
                method="rule_based",
                content={
                    "rules": rules,
                    "num_rules": len(rules)
                },
                quality_score=quality_score,
                interpretability_score=InterpretabilityScore(
                    simplicity=0.9,
                    fidelity=0.7,
                    coherence=0.8
                )
            )
            
        except Exception as e:
            logger.error(f"Error generating rule-based explanation: {e}")
            return ExplanationResult(
                method="rule_based",
                content={"error": str(e)},
                quality_score=0.0,
                interpretability_score=InterpretabilityScore()
            )
    
    def _generate_local_surrogate(self, model, features, prediction, feature_names=None, **kwargs):
        """Generate local surrogate explanation."""
        try:
            # Create feature names if not provided
            if feature_names is None:
                feature_names = [f"feature_{i}" for i in range(features.shape[1])]
            
            # Generate synthetic neighborhood around the instance
            num_samples = 1000
            synthetic_data = np.random.normal(
                features, 
                scale=0.1, 
                size=(num_samples, len(features))
            )
            
            # Get predictions from the original model
            if hasattr(model, 'predict_proba'):
                synthetic_labels = model.predict_proba(synthetic_data)[:, 1]
            else:
                synthetic_labels = model.predict(synthetic_data)
            
            # Train a simple surrogate model (decision tree or linear model)
            from sklearn.tree import DecisionTreeClassifier
            
            surrogate_model = DecisionTreeClassifier(max_depth=3)
            surrogate_model.fit(synthetic_data, synthetic_labels > 0.5)
            
            # Extract feature importances from surrogate
            importance = surrogate_model.feature_importances_
            
            # Normalize importance scores
            if np.sum(importance) > 0:
                importance = importance / np.sum(importance)
            
            # Create feature importance dictionary
            feature_importance = {
                name: float(imp) for name, imp in zip(feature_names, importance)
            }
            
            # Sort by importance
            feature_importance = dict(sorted(
                feature_importance.items(), 
                key=lambda x: x[1], 
                reverse=True
            ))
            
            # Extract rules from surrogate
            from sklearn.tree import export_text
            tree_rules = export_text(surrogate_model, feature_names=feature_names)
            
            # Calculate fidelity (agreement between surrogate and original model)
            surrogate_predictions = surrogate_model.predict(synthetic_data)
            original_binary_predictions = synthetic_labels > 0.5
            fidelity = np.mean(surrogate_predictions == original_binary_predictions)
            
            # Create explanation with quality score
            quality_score = min(1.0, max(0.0, 0.5 + 0.5 * fidelity))
            
            return ExplanationResult(
                method="local_surrogate",
                content={
                    "surrogate_type": "decision_tree",
                    "feature_importance": feature_importance,
                    "surrogate_rules": tree_rules,
                    "surrogate_fidelity": float(fidelity)
                },
                quality_score=quality_score,
                interpretability_score=InterpretabilityScore(
                    simplicity=0.8,
                    fidelity=float(fidelity),
                    coherence=0.7
                )
            )
            
        except Exception as e:
            logger.error(f"Error generating local surrogate explanation: {e}")
            return ExplanationResult(
                method="local_surrogate",
                content={"error": str(e)},
                quality_score=0.0,
                interpretability_score=InterpretabilityScore()
            )
    
    def _explain_linear_model(self, model, features, prediction=None, **kwargs):
        """Generate explanation for linear models."""
        # For linear models, feature importance is most suitable
        feature_importance = self._generate_feature_importance(
            model, features, **kwargs
        )
        
        # If prediction is provided, also generate counterfactual
        if prediction is not None:
            counterfactual = self._generate_counterfactual(
                model, features, prediction, **kwargs
            )
            return [feature_importance, counterfactual]
        
        return [feature_importance]
    
    def _explain_tree_model(self, model, features, prediction=None, **kwargs):
        """Generate explanation for tree-based models."""
        # For tree models, feature importance and rule-based are suitable
        feature_importance = self._generate_feature_importance(
            model, features, **kwargs
        )
        
        rule_based = self._generate_rule_based(
            model, features, **kwargs
        )
        
        return [feature_importance, rule_based]
    
    def _explain_generic_model(self, model, features, prediction=None, **kwargs):
        """Generate explanation for generic models."""
        # For generic models, local surrogate is suitable
        if prediction is not None:
            local_surrogate = self._generate_local_surrogate(
                model, features, prediction, **kwargs
            )
            return [local_surrogate]
        
        # Fallback to feature importance using permutation
        feature_importance = self._generate_feature_importance(
            model, features, **kwargs
        )
        
        return [feature_importance]
    
    def _generate_test_models(self):
        """Generate test models for explanation."""
        models = {}
        
        # Generate synthetic dataset
        np.random.seed(42)
        X = np.random.normal(0, 1, (1000, 5))
        y = (0.3 * X[:, 0] + 0.5 * X[:, 1] - 0.2 * X[:, 2] > 0).astype(int)
        
        # Linear model
        from sklearn.linear_model import LogisticRegression
        linear_model = LogisticRegression(random_state=42)
        linear_model.fit(X, y)
        models["linear"] = linear_model
        
        # Tree model
        from sklearn.tree import DecisionTreeClassifier
        tree_model = DecisionTreeClassifier(random_state=42)
        tree_model.fit(X, y)
        models["tree"] = tree_model
        
        # Random forest model
        from sklearn.ensemble import RandomForestClassifier
        rf_model = RandomForestClassifier(random_state=42)
        rf_model.fit(X, y)
        models["random_forest"] = rf_model
        
        # Model metadata
        models["dataset"] = (X, y)
        models["feature_names"] = ["income", "education", "age", "experience", "debt_ratio"]
        
        return models
    
    def _generate_test_inputs(self):
        """Generate test inputs for explanation."""
        test_inputs = {}
        
        # Get dataset
        X, y = self.test_models["dataset"]
        feature_names = self.test_models["feature_names"]
        
        # Generate sample inputs
        test_inputs["sample"] = {
            "features": X[0],
            "feature_names": feature_names,
            "labels": y[0]
        }
        
        # Generate batch inputs
        test_inputs["batch"] = {
            "features": X[:10],
            "feature_names": feature_names,
            "labels": y[:10]
        }
        
        return test_inputs
    
    def _generate_test_predictions(self):
        """Generate test predictions for explanation."""
        test_predictions = {}
        
        # Get sample features
        sample_features = self.test_inputs["sample"]["features"]
        
        # Generate predictions for each model
        for model_name, model in self.test_models.items():
            if model_name in ["dataset", "feature_names"]:
                continue
                
            # Make prediction
            if hasattr(model, 'predict_proba'):
                prediction = model.predict_proba(sample_features.reshape(1, -1))[0, 1]
            else:
                prediction = model.predict(sample_features.reshape(1, -1))[0]
                
            test_predictions[model_name] = prediction
        
        return test_predictions
    
    def test_explanation_generation(self):
        """Test explanation generation for different models and methods."""
        # Test for each model type
        for model_name, model in self.test_models.items():
            if model_name in ["dataset", "feature_names"]:
                continue
                
            # Get sample input
            sample_input = self.test_inputs["sample"]
            features = sample_input["features"]
            feature_names = sample_input["feature_names"]
            
            # Get prediction
            prediction = self.test_predictions[model_name]
            
            logger.info(f"\nTesting explanations for {model_name} model")
            
            # Test each explanation method
            for method in self.explanation_methods:
                # Check if method is supported for this model
                model_type = "linear" if model_name == "linear" else "tree" if model_name in ["tree", "random_forest"] else "black_box"
                if model_type not in method.supported_models and "any" not in method.supported_models:
                    continue
                    
                # Check if required inputs are available
                required_inputs = {
                    "model": model,
                    "features": features,
                    "feature_names": feature_names
                }
                
                if "prediction" in method.requirements:
                    required_inputs["prediction"] = prediction
                
                # Generate explanation
                explanation = self.transparency_manager.generate_explanation(
                    model_input={"features": features, "feature_names": feature_names},
                    model_prediction={"prediction": prediction},
                    model=model,
                    method=method.name
                )
                
                # Log explanation results
                logger.info(f"Method: {method.name}, Quality: {explanation.quality_score}")
                
                # Check that explanation is generated
                self.assertIsNotNone(explanation)
                self.assertEqual(explanation.method, method.name)
                
                # Check that explanation content is valid
                self.assertIsNotNone(explanation.content)
                self.assertNotIn("error", explanation.content)
                
                # Check quality score
                self.assertGreaterEqual(explanation.quality_score, 0.0)
                self.assertLessEqual(explanation.quality_score, 1.0)
    
    def test_model_explanations(self):
        """Test model-specific explanations."""
        # Test for each model type
        for model_name, model in self.test_models.items():
            if model_name in ["dataset", "feature_names"]:
                continue
                
            # Get sample input
            sample_input = self.test_inputs["sample"]
            features = sample_input["features"]
            feature_names = sample_input["feature_names"]
            
            # Get prediction
            prediction = self.test_predictions[model_name]
            
            logger.info(f"\nTesting model-specific explanations for {model_name} model")
            
            # Determine model explainer to use
            if model_name == "linear":
                explainer_name = "linear"
            elif model_name in ["tree", "random_forest"]:
                explainer_name = "tree"
            else:
                explainer_name = "generic"
            
            # Generate explanations
            explanations = self.transparency_manager.explain_model_prediction(
                model=model,
                features=features,
                feature_names=feature_names,
                prediction=prediction,
                explainer_name=explainer_name
            )
            
            # Log explanation results
            logger.info(f"Explanations generated: {len(explanations)}")
            for explanation in explanations:
                logger.info(f"Method: {explanation.method}, Quality: {explanation.quality_score}")
            
            # Check that explanations are generated
            self.assertGreater(len(explanations), 0)
            
            # Check that explanations have valid content
            for explanation in explanations:
                self.assertIsNotNone(explanation.content)
                self.assertNotIn("error", explanation.content)
                
                # Check quality score
                self.assertGreaterEqual(explanation.quality_score, 0.0)
                self.assertLessEqual(explanation.quality_score, 1.0)
    
    def test_interpretability_assessment(self):
        """Test interpretability assessment."""
        # Get a linear model and features
        model = self.test_models["linear"]
        features = self.test_inputs["sample"]["features"]
        feature_names = self.test_inputs["sample"]["feature_names"]
        prediction = self.test_predictions["linear"]
        
        # Generate explanation
        explanation = self.transparency_manager.generate_explanation(
            model_input={"features": features, "feature_names": feature_names},
            model_prediction={"prediction": prediction},
            model=model,
            method="feature_importance"
        )
        
        # Assess interpretability
        interpretability = self.transparency_manager.assess_interpretability(explanation)
        
        # Log interpretability results
        logger.info(f"Interpretability scores: {interpretability}")
        
        # Check that interpretability scores are valid
        self.assertIsNotNone(interpretability)
        self.assertGreaterEqual(interpretability.simplicity, 0.0)
        self.assertLessEqual(interpretability.simplicity, 1.0)
        self.assertGreaterEqual(interpretability.fidelity, 0.0)
        self.assertLessEqual(interpretability.fidelity, 1.0)
        self.assertGreaterEqual(interpretability.coherence, 0.0)
        self.assertLessEqual(interpretability.coherence, 1.0)
    
    def test_transparency_report_generation(self):
        """Test transparency report generation."""
        # Get a model and features
        model = self.test_models["random_forest"]
        features = self.test_inputs["sample"]["features"]
        feature_names = self.test_inputs["sample"]["feature_names"]
        prediction = self.test_predictions["random_forest"]
        
        # Generate explanations
        explanations = self.transparency_manager.explain_model_prediction(
            model=model,
            features=features,
            feature_names=feature_names,
            prediction=prediction,
            explainer_name="tree"
        )
        
        # Generate transparency report
        report = self.transparency_manager.generate_transparency_report(
            model=model,
            explanations=explanations,
            model_info={
                "name": "Random Forest Classifier",
                "type": "ensemble",
                "features": feature_names,
                "target": "Approval"
            }
        )
        
        # Log report results
        logger.info(f"Report sections: {list(report.sections.keys())}")
        
        # Check that report is valid
        self.assertIsNotNone(report)
        self.assertIn("model_overview", report.sections)
        self.assertIn("explanations", report.sections)
        self.assertIn("interpretability", report.sections)
        
        # Check transparency score
        self.assertGreaterEqual(report.transparency_score, 0.0)
        self.assertLessEqual(report.transparency_score, 1.0)


class TestHarmPrevention(unittest.TestCase):
    """Tests for the HarmPrevention component."""
    
    def setUp(self):
        """Set up test fixtures for HarmPrevention tests."""
        # Initialize harm categories
        self.harm_categories = [
            HarmCategory(
                name="physical",
                description="Potential for physical harm to individuals",
                severity_threshold=0.7,
                assessment_function=self._assess_physical_harm
            ),
            HarmCategory(
                name="psychological",
                description="Potential for psychological harm or distress",
                severity_threshold=0.6,
                assessment_function=self._assess_psychological_harm
            ),
            HarmCategory(
                name="social",
                description="Potential for social harm, discrimination, or injustice",
                severity_threshold=0.6,
                assessment_function=self._assess_social_harm
            ),
            HarmCategory(
                name="financial",
                description="Potential for financial harm or economic damage",
                severity_threshold=0.7,
                assessment_function=self._assess_financial_harm
            ),
            HarmCategory(
                name="environmental",
                description="Potential for environmental harm",
                severity_threshold=0.7,
                assessment_function=self._assess_environmental_harm
            ),
            HarmCategory(
                name="security",
                description="Potential for security breaches or privacy violations",
                severity_threshold=0.8,
                assessment_function=self._assess_security_harm
            )
        ]
        
        # Initialize prevention strategies
        self.prevention_strategies = [
            PreventionStrategy(
                name="content_filtering",
                description="Filter out harmful content",
                implementation=self._implement_content_filtering,
                applicable_categories=["physical", "psychological", "social"]
            ),
            PreventionStrategy(
                name="risk_warning",
                description="Provide warnings about potential risks",
                implementation=self._implement_risk_warning,
                applicable_categories=["physical", "psychological", "financial", "security"]
            ),
            PreventionStrategy(
                name="context_augmentation",
                description="Augment content with context to reduce harm",
                implementation=self._implement_context_augmentation,
                applicable_categories=["social", "psychological"]
            ),
            PreventionStrategy(
                name="alternative_recommendation",
                description="Recommend alternative, less harmful options",
                implementation=self._implement_alternative_recommendation,
                applicable_categories=["physical", "financial", "environmental"]
            ),
            PreventionStrategy(
                name="mitigation_steps",
                description="Provide steps to mitigate potential harm",
                implementation=self._implement_mitigation_steps,
                applicable_categories=["physical", "financial", "security", "environmental"]
            )
        ]
        
        # Initialize HarmPrevention
        self.harm_prevention = HarmPrevention(
            harm_categories=self.harm_categories,
            prevention_strategies=self.prevention_strategies,
            safety_threshold=0.8,
            log_assessments=True
        )
        
        # Generate test content
        self.safe_content = self._generate_safe_content()
        self.harmful_content = self._generate_harmful_content()
    
    def _assess_physical_harm(self, content, context=None):
        """Assess potential for physical harm."""
        # This is a simplified implementation
        # In a real system, this would use NLP techniques and trained models
        
        # Check for physical harm keywords in text content
        physical_harm_keywords = [
            "hurt", "injure", "kill", "attack", "damage", "harm", "assault",
            "weapon", "bomb", "violence", "dangerous", "toxic", "poison",
            "explosive", "deadly", "fatal", "suicide", "self-harm"
        ]
        
        if "text" in content and isinstance(content["text"], str):
            text = content["text"].lower()
            matches = sum(keyword in text for keyword in physical_harm_keywords)
            if matches > 0:
                # Scale based on number of matches and their density
                severity = min(1.0, matches / 5)
                
                # Increase severity if instructions or encouragement are present
                if any(phrase in text for phrase in ["how to", "steps to", "ways to", "you should"]):
                    severity += 0.2
                
                return min(1.0, severity)
        
        # Check for physical harm in recommendation type
        if content.get("type") == "recommendation":
            recommendation = content.get("recommendation", "")
            if isinstance(recommendation, str):
                recommendation = recommendation.lower()
                if any(keyword in recommendation for keyword in physical_harm_keywords):
                    return 0.8
        
        # Check for physical harm in action content
        if content.get("type") == "action":
            action = content.get("action", "")
            if isinstance(action, str):
                action = action.lower()
                if any(keyword in action for keyword in physical_harm_keywords):
                    return 0.9
        
        # Default assessment based on content attributes
        if "harm_metrics" in content and "physical" in content["harm_metrics"]:
            return content["harm_metrics"]["physical"]
        
        # Default low risk if no indicators are found
        return 0.05
    
    def _assess_psychological_harm(self, content, context=None):
        """Assess potential for psychological harm."""
        # This is a simplified implementation
        # In a real system, this would use NLP techniques and trained models
        
        # Check for psychological harm keywords in text content
        psychological_harm_keywords = [
            "depression", "anxiety", "trauma", "distress", "fear", "panic",
            "abuse", "bully", "harass", "threaten", "shame", "humiliate",
            "manipulate", "gaslight", "insult", "degrade", "disturbing"
        ]
        
        if "text" in content and isinstance(content["text"], str):
            text = content["text"].lower()
            matches = sum(keyword in text for keyword in psychological_harm_keywords)
            if matches > 0:
                # Scale based on number of matches and their density
                severity = min(1.0, matches / 5)
                return min(1.0, severity)
        
        # Check for targeting vulnerable groups
        vulnerable_groups = ["children", "minors", "elderly", "disabled", "mentally ill"]
        
        if "target_audience" in content:
            audience = content["target_audience"].lower() if isinstance(content["target_audience"], str) else ""
            if any(group in audience for group in vulnerable_groups):
                return 0.7
        
        # Check for content designed to trigger emotional responses
        if "emotion_targeting" in content and content["emotion_targeting"] is True:
            return 0.8
        
        # Default assessment based on content attributes
        if "harm_metrics" in content and "psychological" in content["harm_metrics"]:
            return content["harm_metrics"]["psychological"]
        
        # Default low risk if no indicators are found
        return 0.1
    
    def _assess_social_harm(self, content, context=None):
        """Assess potential for social harm."""
        # This is a simplified implementation
        # In a real system, this would use NLP techniques and trained models
        
        # Check for social harm keywords in text content
        social_harm_keywords = [
            "discriminate", "prejudice", "racism", "sexism", "stereotype",
            "hate", "bigotry", "derogatory", "offensive", "slur", "sexist",
            "racist", "homophobic", "xenophobic", "supremacist", "inequality"
        ]
        
        if "text" in content and isinstance(content["text"], str):
            text = content["text"].lower()
            matches = sum(keyword in text for keyword in social_harm_keywords)
            if matches > 0:
                # Scale based on number of matches and their density
                severity = min(1.0, matches / 3)
                return min(1.0, severity)
        
        # Check for demographic disparities in model outputs or recommendations
        if "fairness_metrics" in content:
            fairness_metrics = content["fairness_metrics"]
            if "demographic_parity" in fairness_metrics and fairness_metrics["demographic_parity"] < 0.7:
                return 0.8
            if "disparate_impact" in fairness_metrics and fairness_metrics["disparate_impact"] < 0.7:
                return 0.8
        
        # Check for reinforcement of stereotypes
        if "stereotype_reinforcement" in content:
            return content["stereotype_reinforcement"]
        
        # Default assessment based on content attributes
        if "harm_metrics" in content and "social" in content["harm_metrics"]:
            return content["harm_metrics"]["social"]
        
        # Default low risk if no indicators are found
        return 0.1
    
    def _assess_financial_harm(self, content, context=None):
        """Assess potential for financial harm."""
        # This is a simplified implementation
        # In a real system, this would use NLP techniques and trained models
        
        # Check for financial harm keywords in text content
        financial_harm_keywords = [
            "scam", "fraud", "steal", "hack", "theft", "pyramid scheme", "ponzi",
            "phishing", "unauthorized", "money", "bank account", "credit card",
            "password", "credentials", "financial", "investment", "risky"
        ]
        
        if "text" in content and isinstance(content["text"], str):
            text = content["text"].lower()
            matches = sum(keyword in text for keyword in financial_harm_keywords)
            if matches > 0:
                # Scale based on number of matches and their density
                severity = min(1.0, matches / 4)
                return min(1.0, severity)
        
        # Check for financial recommendations
        if content.get("type") == "recommendation" and content.get("domain") == "financial":
            # High-risk financial products without adequate warnings
            if content.get("risk_level", "low").lower() in ["high", "extreme"] and not content.get("risk_warning", False):
                return 0.8
            
            # Check for unrealistic financial promises
            if content.get("promised_returns", 0) > 20:  # Unrealistic returns
                return 0.9
        
        # Check for financial information requests
        if content.get("type") == "request" and "financial_data" in content.get("requested_info", []):
            return 0.8
        
        # Default assessment based on content attributes
        if "harm_metrics" in content and "financial" in content["harm_metrics"]:
            return content["harm_metrics"]["financial"]
        
        # Default low risk if no indicators are found
        return 0.1
    
    def _assess_environmental_harm(self, content, context=None):
        """Assess potential for environmental harm."""
        # This is a simplified implementation
        # In a real system, this would use NLP techniques and trained models
        
        # Check for environmental harm keywords in text content
        environmental_harm_keywords = [
            "pollution", "waste", "toxic", "contaminate", "deforest", "climate",
            "emissions", "carbon", "endangered", "habitat", "destroy", "damage",
            "ecosystem", "environment", "sustainable", "fossil fuels"
        ]
        
        if "text" in content and isinstance(content["text"], str):
            text = content["text"].lower()
            
            # Check for promotion of environmentally harmful activities
            if any(phrase in text for phrase in ["how to increase", "ways to maximize"]):
                if any(keyword in text for keyword in environmental_harm_keywords):
                    return 0.8
            
            matches = sum(keyword in text for keyword in environmental_harm_keywords)
            if matches > 0:
                # Scale based on number of matches and context
                severity = min(1.0, matches / 6)
                
                # Reduce severity if discussing solutions
                if any(phrase in text for phrase in ["reduce", "prevent", "mitigate", "solution"]):
                    severity -= 0.3
                    severity = max(0.0, severity)
                
                return severity
        
        # Check for environment-impacting recommendations without sustainability consideration
        if content.get("type") == "recommendation" and content.get("domain") in ["industrial", "energy", "transportation", "manufacturing"]:
            if not content.get("sustainability_considered", False):
                return 0.7
        
        # Default assessment based on content attributes
        if "harm_metrics" in content and "environmental" in content["harm_metrics"]:
            return content["harm_metrics"]["environmental"]
        
        # Default low risk if no indicators are found
        return 0.1
    
    def _assess_security_harm(self, content, context=None):
        """Assess potential for security or privacy harm."""
        # This is a simplified implementation
        # In a real system, this would use NLP techniques and trained models
        
        # Check for security harm keywords in text content
        security_harm_keywords = [
            "hack", "breach", "vulnerability", "exploit", "backdoor", "attack",
            "malware", "virus", "ransomware", "phishing", "credentials", "password",
            "private", "personal data", "tracking", "surveillance", "bypass security"
        ]
        
        if "text" in content and isinstance(content["text"], str):
            text = content["text"].lower()
            matches = sum(keyword in text for keyword in security_harm_keywords)
            if matches > 0:
                # Scale based on number of matches and their context
                severity = min(1.0, matches / 4)
                
                # Increase severity if instructions are present
                if any(phrase in text for phrase in ["how to", "steps to", "guide", "tutorial"]):
                    severity += 0.3
                    severity = min(1.0, severity)
                
                # Reduce severity if discussing protection
                if any(phrase in text for phrase in ["prevent", "protect", "secure", "fix"]):
                    severity -= 0.3
                    severity = max(0.0, severity)
                
                return severity
        
        # Check for code that might be potentially harmful
        if "code" in content and isinstance(content["code"], str):
            code = content["code"].lower()
            # Look for potentially malicious code patterns
            malicious_patterns = [
                "shell_exec", "system(", "exec(", "eval(", "os.system", 
                "subprocess.call", "backdoor", "exploit", "hack", "overflow"
            ]
            if any(pattern in code for pattern in malicious_patterns):
                return 0.9
        
        # Check for privacy-sensitive data handling
        if "data_processing" in content:
            data_types = content.get("data_types", [])
            sensitive_data = ["personal", "health", "financial", "location", "biometric"]
            
            if any(dtype in sensitive_data for dtype in data_types):
                # Check for proper safeguards
                if not content.get("encryption", False) or not content.get("consent", False):
                    return 0.8
        
        # Default assessment based on content attributes
        if "harm_metrics" in content and "security" in content["harm_metrics"]:
            return content["harm_metrics"]["security"]
        
        # Default low risk if no indicators are found
        return 0.1
    
    def _implement_content_filtering(self, content, harm_assessment, **kwargs):
        """Implement content filtering prevention strategy."""
        # Create a filtered version of the content
        filtered_content = content.copy() if isinstance(content, dict) else {"text": str(content)}
        
        # Filter based on harm category
        for category, severity in harm_assessment.severity.items():
            if severity > self.harm_categories_dict[category].severity_threshold:
                # For text content, apply filtering
                if "text" in filtered_content and isinstance(filtered_content["text"], str):
                    # Determine harmful segments (simplified)
                    text = filtered_content["text"]
                    
                    # Keywords to filter for each category
                    filter_keywords = {
                        "physical": ["hurt", "kill", "attack", "weapon", "violence", "suicide"],
                        "psychological": ["abuse", "trauma", "bully", "harass", "humiliate"],
                        "social": ["hate", "racism", "sexism", "slur", "offensive", "bigotry"]
                    }
                    
                    if category in filter_keywords:
                        # Replace harmful keywords with placeholders
                        for keyword in filter_keywords[category]:
                            if keyword in text.lower():
                                text = text.replace(keyword, "[REMOVED]")
                                
                        filtered_content["text"] = text
                
                # For recommendations, flag or remove harmful ones
                if filtered_content.get("type") == "recommendation":
                    if category in ["physical", "psychological", "social"]:
                        filtered_content["recommendation"] = "This recommendation has been withheld due to safety concerns."
                        filtered_content["filtered"] = True
        
        # Add filtering metadata
        filtered_content["safety_filtered"] = True
        filtered_content["filtering_reason"] = ", ".join(
            [f"{cat} ({sev:.2f})" for cat, sev in harm_assessment.severity.items() 
             if sev > self.harm_categories_dict[cat].severity_threshold]
        )
        
        return filtered_content
    
    def _implement_risk_warning(self, content, harm_assessment, **kwargs):
        """Implement risk warning prevention strategy."""
        # Create a version of the content with warnings
        warned_content = content.copy() if isinstance(content, dict) else {"text": str(content)}
        
        # Add appropriate warnings based on harm categories
        warnings = []
        
        for category, severity in harm_assessment.severity.items():
            if severity > self.harm_categories_dict[category].severity_threshold:
                # Define warning based on category
                category_warnings = {
                    "physical": "Warning: This content discusses physical actions that may be harmful.",
                    "psychological": "Warning: This content may be emotionally distressing to some individuals.",
                    "financial": "Warning: Consider financial risks before acting on this information.",
                    "security": "Warning: Be cautious about potential security or privacy implications."
                }
                
                if category in category_warnings:
                    warnings.append(category_warnings[category])
        
        # Add warnings to content
        if warnings:
            # For text content, prepend warnings
            if "text" in warned_content and isinstance(warned_content["text"], str):
                warned_content["text"] = "\n".join(warnings) + "\n\n" + warned_content["text"]
            
            # Add warnings metadata
            warned_content["warnings"] = warnings
            warned_content["warning_level"] = max(
                [sev for cat, sev in harm_assessment.severity.items() 
                 if sev > self.harm_categories_dict[cat].severity_threshold]
            )
        
        return warned_content
    
    def _implement_context_augmentation(self, content, harm_assessment, **kwargs):
        """Implement context augmentation prevention strategy."""
        # Create a version of the content with additional context
        augmented_content = content.copy() if isinstance(content, dict) else {"text": str(content)}
        
        # Add context based on harm categories
        context_additions = []
        
        for category, severity in harm_assessment.severity.items():
            if severity > self.harm_categories_dict[category].severity_threshold:
                # Define context additions based on category
                category_contexts = {
                    "social": "It's important to consider diverse perspectives and treat all individuals with respect and dignity.",
                    "psychological": "Individual experiences vary greatly, and what works for one person may not be appropriate for everyone."
                }
                
                if category in category_contexts:
                    context_additions.append(category_contexts[category])
        
        # Add context to content
        if context_additions:
            # For text content, append context
            if "text" in augmented_content and isinstance(augmented_content["text"], str):
                augmented_content["text"] = augmented_content["text"] + "\n\n" + "\n".join(context_additions)
            
            # Add context metadata
            augmented_content["augmented_context"] = context_additions
        
        return augmented_content
    
    def _implement_alternative_recommendation(self, content, harm_assessment, **kwargs):
        """Implement alternative recommendation prevention strategy."""
        # Create a version of the content with alternative recommendations
        alternative_content = content.copy() if isinstance(content, dict) else {"text": str(content)}
        
        # Add alternatives based on harm categories
        alternatives = []
        
        for category, severity in harm_assessment.severity.items():
            if severity > self.harm_categories_dict[category].severity_threshold:
                # Define alternatives based on category and content
                if category == "physical" and alternative_content.get("type") == "recommendation":
                    alternatives.append({
                        "original": alternative_content.get("recommendation", ""),
                        "alternative": "Consider consulting with a healthcare professional for safe advice."
                    })
                
                elif category == "financial" and alternative_content.get("type") == "recommendation":
                    alternatives.append({
                        "original": alternative_content.get("recommendation", ""),
                        "alternative": "Consider lower-risk financial options or consulting with a financial advisor."
                    })
                
                elif category == "environmental" and alternative_content.get("type") == "recommendation":
                    alternatives.append({
                        "original": alternative_content.get("recommendation", ""),
                        "alternative": "Consider more environmentally sustainable alternatives."
                    })
        
        # Add alternatives to content
        if alternatives:
            # For recommendation content, update recommendation
            if alternative_content.get("type") == "recommendation":
                # Replace with the first alternative
                if alternatives:
                    alternative_content["original_recommendation"] = alternative_content.get("recommendation", "")
                    alternative_content["recommendation"] = alternatives[0]["alternative"]
            
            # Add alternatives metadata
            alternative_content["alternatives"] = alternatives
        
        return alternative_content
    
    def _implement_mitigation_steps(self, content, harm_assessment, **kwargs):
        """Implement mitigation steps prevention strategy."""
        # Create a version of the content with mitigation steps
        mitigated_content = content.copy() if isinstance(content, dict) else {"text": str(content)}
        
        # Add mitigation steps based on harm categories
        mitigation_steps = []
        
        for category, severity in harm_assessment.severity.items():
            if severity > self.harm_categories_dict[category].severity_threshold:
                # Define mitigation steps based on category
                category_steps = {
                    "physical": [
                        "Consult with professionals before attempting any physical activities.",
                        "Start with lower intensity and gradually increase as appropriate.",
                        "Ensure proper safety equipment is used at all times."
                    ],
                    "financial": [
                        "Diversify investments to reduce risk exposure.",
                        "Only invest amounts you can afford to lose.",
                        "Consult with a financial advisor before making major decisions."
                    ],
                    "security": [
                        "Use strong, unique passwords for different accounts.",
                        "Enable two-factor authentication when available.",
                        "Keep software and systems updated with security patches.",
                        "Be cautious about sharing sensitive information."
                    ],
                    "environmental": [
                        "Consider more sustainable alternatives.",
                        "Reduce waste and energy consumption where possible.",
                        "Follow proper disposal guidelines for potentially harmful materials."
                    ]
                }
                
                if category in category_steps:
                    mitigation_steps.extend(category_steps[category])
        
        # Add mitigation steps to content
        if mitigation_steps:
            # For text content, append mitigation steps
            if "text" in mitigated_content and isinstance(mitigated_content["text"], str):
                mitigated_content["text"] = mitigated_content["text"] + "\n\nRisk Mitigation Steps:\n" + "\n".join(f"- {step}" for step in mitigation_steps)
            
            # Add mitigation metadata
            mitigated_content["mitigation_steps"] = mitigation_steps
        
        return mitigated_content
    
    def _generate_safe_content(self):
        """Generate safe content for testing."""
        safe_content = []
        
        # Safe text content
        safe_content.append({
            "type": "text",
            "text": "Regular exercise and a balanced diet can contribute to overall health and well-being.",
            "domain": "health",
            "target_audience": "general"
        })
        
        # Safe recommendation
        safe_content.append({
            "type": "recommendation",
            "recommendation": "Consider investing in a diversified portfolio of low-cost index funds.",
            "domain": "financial",
            "risk_level": "low",
            "risk_warning": True,
            "sustainability_considered": True,
            "harm_metrics": {
                "physical": 0.0,
                "psychological": 0.0,
                "social": 0.0,
                "financial": 0.2,
                "environmental": 0.1,
                "security": 0.0
            }
        })
        
        # Safe information request
        safe_content.append({
            "type": "request",
            "text": "Could you provide your preferred contact method for our newsletter?",
            "requested_info": ["communication_preference"],
            "purpose": "newsletter_subscription",
            "data_processing": {
                "data_types": ["contact"],
                "encryption": True,
                "consent": True
            },
            "harm_metrics": {
                "physical": 0.0,
                "psychological": 0.0,
                "social": 0.0,
                "financial": 0.0,
                "environmental": 0.0,
                "security": 0.1
            }
        })
        
        # Safe code
        safe_content.append({
            "type": "code",
            "code": '''
def calculate_bmi(height_m, weight_kg):
    """Calculate BMI given height in meters and weight in kilograms."""
    if height_m <= 0 or weight_kg <= 0:
        return "Invalid input"
    bmi = weight_kg / (height_m ** 2)
    return bmi
            ''',
            "language": "python",
            "purpose": "health_calculation",
            "harm_metrics": {
                "physical": 0.0,
                "psychological": 0.0,
                "social": 0.0,
                "financial": 0.0,
                "environmental": 0.0,
                "security": 0.0
            }
        })
        
        return safe_content
    
    def _generate_harmful_content(self):
        """Generate potentially harmful content for testing."""
        harmful_content = []
        
        # Physically harmful content
        harmful_content.append({
            "type": "text",
            "text": "How to build explosive devices at home using common household items. The steps include...",
            "domain": "diy",
            "harm_category": "physical",
            "harm_metrics": {
                "physical": 0.9,
                "psychological": 0.5,
                "social": 0.3,
                "financial": 0.1,
                "environmental": 0.2,
                "security": 0.7
            }
        })
        
        # Psychologically harmful content
        harmful_content.append({
            "type": "text",
            "text": "You are worthless and everyone hates you. You'll never amount to anything in life.",
            "domain": "message",
            "target_audience": "teenagers",
            "emotion_targeting": True,
            "harm_category": "psychological",
            "harm_metrics": {
                "physical": 0.2,
                "psychological": 0.9,
                "social": 0.6,
                "financial": 0.0,
                "environmental": 0.0,
                "security": 0.0
            }
        })
        
        # Socially harmful content
        harmful_content.append({
            "type": "recommendation",
            "recommendation": "Hire only men for technical positions as they are naturally better at these roles.",
            "domain": "hiring",
            "fairness_metrics": {
                "demographic_parity": 0.2,
                "disparate_impact": 0.3
            },
            "stereotype_reinforcement": 0.8,
            "harm_category": "social",
            "harm_metrics": {
                "physical": 0.0,
                "psychological": 0.7,
                "social": 0.9,
                "financial": 0.6,
                "environmental": 0.0,
                "security": 0.0
            }
        })
        
        # Financially harmful content
        harmful_content.append({
            "type": "recommendation",
            "recommendation": "Invest all your savings in this new cryptocurrency. Guaranteed 500% returns in one month!",
            "domain": "financial",
            "risk_level": "extreme",
            "risk_warning": False,
            "promised_returns": 500,
            "harm_category": "financial",
            "harm_metrics": {
                "physical": 0.0,
                "psychological": 0.5,
                "social": 0.2,
                "financial": 0.9,
                "environmental": 0.0,
                "security": 0.4
            }
        })
        
        # Security harmful content
        harmful_content.append({
            "type": "code",
            "code": '''
import os

def delete_all_files(directory):
    """Delete all files in the specified directory."""
    for root, dirs, files in os.walk(directory):
        for file in files:
            os.remove(os.path.join(root, file))

# Delete all files in the system directory
delete_all_files("/")
            ''',
            "language": "python",
            "purpose": "system_management",
            "harm_category": "security",
            "harm_metrics": {
                "physical": 0.0,
                "psychological": 0.3,
                "social": 0.0,
                "financial": 0.7,
                "environmental": 0.0,
                "security": 0.9
            }
        })
        
        return harmful_content
    
    @property
    def harm_categories_dict(self):
        """Get harm categories as a dictionary."""
        return {category.name: category for category in self.harm_categories}
    
    def test_safe_content_assessment(self):
        """Test harm assessment on safe content."""
        for content in self.safe_content:
            # Assess harm
            assessment = self.harm_prevention.assess_harm(content)
            
            # Log detailed results for debugging
            logger.info(f"\nContent type: {content.get('type', 'unknown')}")
            logger.info(f"Overall safety: {assessment.overall_safety}")
            logger.info(f"Category assessments: {assessment.severity}")
            logger.info(f"Is harmful: {assessment.is_harmful}")
            
            # Check that content is classified as safe
            self.assertFalse(assessment.is_harmful, 
                           f"Safe content incorrectly classified as harmful: {content.get('type', 'unknown')}")
            self.assertGreaterEqual(assessment.overall_safety, 
                                  self.harm_prevention.safety_threshold,
                                  f"Safe content has safety score below threshold: {content.get('type', 'unknown')}")
            
            # Check individual harm categories
            for category, severity in assessment.severity.items():
                # Safe content should have low severity scores
                self.assertLessEqual(severity, 0.5,
                                  f"Unexpectedly high {category} harm score for safe content: {content.get('type', 'unknown')}")
    
    def test_harmful_content_detection(self):
        """Test harm detection on harmful content."""
        for content in self.harmful_content:
            # Assess harm
            assessment = self.harm_prevention.assess_harm(content)
            
            # Log detailed results for debugging
            logger.info(f"\nContent type: {content.get('type', 'unknown')}")
            logger.info(f"Harm category: {content.get('harm_category', 'unknown')}")
            logger.info(f"Overall safety: {assessment.overall_safety}")
            logger.info(f"Category assessments: {assessment.severity}")
            logger.info(f"Is harmful: {assessment.is_harmful}")
            logger.info(f"Harm alerts: {assessment.harm_alerts}")
            
            # Check that content is classified as harmful
            self.assertTrue(assessment.is_harmful, 
                          f"Harmful content incorrectly classified as safe: {content.get('type', 'unknown')}")
            self.assertLessEqual(assessment.overall_safety, 
                               self.harm_prevention.safety_threshold,
                               f"Harmful content has safety score above threshold: {content.get('type', 'unknown')}")
            
            # Check specific harm category detection
            expected_harm_category = content.get("harm_category")
            if expected_harm_category:
                self.assertIn(expected_harm_category, assessment.severity,
                            f"Expected harm category {expected_harm_category} not detected")
                self.assertGreaterEqual(assessment.severity[expected_harm_category], 0.7,
                                     f"Harm severity for {expected_harm_category} lower than expected")
            
            # Check that harm alerts are generated
            self.assertGreaterEqual(len(assessment.harm_alerts), 1,
                                 f"No harm alerts generated for harmful content: {content.get('type', 'unknown')}")
    
    def test_prevention_strategy_application(self):
        """Test application of prevention strategies to harmful content."""
        for content in self.harmful_content:
            # Assess harm
            assessment = self.harm_prevention.assess_harm(content)
            
            # Apply prevention strategies
            prevention_result = self.harm_prevention.apply_prevention_strategies(content, assessment)
            
            # Log detailed results for debugging
            logger.info(f"\nContent type: {content.get('type', 'unknown')}")
            logger.info(f"Harm category: {content.get('harm_category', 'unknown')}")
            logger.info(f"Applied strategies: {[s.name for s in prevention_result.applied_strategies]}")
            logger.info(f"Modified content keys: {list(prevention_result.modified_content.keys())}")
            
            # Check that prevention strategies are applied
            self.assertGreaterEqual(len(prevention_result.applied_strategies), 1,
                                 f"No prevention strategies applied to harmful content: {content.get('type', 'unknown')}")
            
            # Check that content is modified
            self.assertNotEqual(content, prevention_result.modified_content,
                              f"Content not modified after applying prevention strategies: {content.get('type', 'unknown')}")
            
            # Check that residual risk is evaluated
            self.assertIsNotNone(prevention_result.residual_risk,
                               f"Residual risk not evaluated: {content.get('type', 'unknown')}")
            
            # Check that residual risk is lower than original risk
            self.assertLess(prevention_result.residual_risk, 1.0 - assessment.overall_safety,
                          f"Residual risk not reduced: {content.get('type', 'unknown')}")
    
    def test_risk_profiling(self):
        """Test risk profiling capabilities."""
        # Generate risk profiles for different content types
        profiles = {}
        
        for content in self.safe_content + self.harmful_content:
            content_type = content.get("type", "unknown")
            domain = content.get("domain", "general")
            
            # Get or create risk profile for this content type and domain
            profile_key = f"{content_type}_{domain}"
            if profile_key not in profiles:
                profiles[profile_key] = self.harm_prevention.create_risk_profile(content_type, domain)
            
            # Update risk profile with this content
            profiles[profile_key].update_with_assessment(
                self.harm_prevention.assess_harm(content)
            )
        
        # Log and check risk profiles
        for key, profile in profiles.items():
            logger.info(f"\nRisk profile for {key}")
            logger.info(f"Overall risk level: {profile.risk_level}")
            logger.info(f"High-risk categories: {profile.high_risk_categories}")
            logger.info(f"Risk trend: {profile.risk_trend}")
            
            # Check that risk profile is valid
            self.assertIsNotNone(profile.risk_level)
            
            # Check that risk trend is calculated
            self.assertIsNotNone(profile.risk_trend)
            
            # Check that high-risk categories are identified if present
            if profile.risk_level in ["medium", "high", "critical"]:
                self.assertGreaterEqual(len(profile.high_risk_categories), 1,
                                     f"No high-risk categories identified for {key} with risk level {profile.risk_level}")


class TestEthicalFrameworkIntegration(unittest.TestCase):
    """Tests for integration of ethical framework components with other ULTRA modules."""
    
    def setUp(self):
        """Set up test fixtures for integration tests."""
        # Initialize ethical framework components
        self.ethical_validator = EthicalValidator(
            principles=[
                EthicalPrinciple(
                    name="non_maleficence",
                    description="Do no harm",
                    validation_function=lambda action, context: 0.9 if action.get("safe", True) else 0.2,
                    weight=0.9
                ),
                EthicalPrinciple(
                    name="fairness",
                    description="Treat all individuals fairly",
                    validation_function=lambda action, context: 0.9 if action.get("fair", True) else 0.3,
                    weight=0.8
                )
            ],
            guidelines=[],
            validation_threshold=0.7
        )
        
        self.fairness_evaluator = FairnessEvaluator(
            metrics=[
                FairnessMetric(
                    name="demographic_parity",
                    category="group_fairness",
                    computation_function=lambda *args, **kwargs: 0.9 if kwargs.get("fair", True) else 0.3,
                    threshold=0.8,
                    weight=0.8
                )
            ],
            sensitive_attributes=["gender", "race"],
            fairness_threshold=0.7
        )
        
        self.harm_prevention = HarmPrevention(
            harm_categories=[
                HarmCategory(
                    name="physical",
                    description="Physical harm",
                    severity_threshold=0.7,
                    assessment_function=lambda content, context=None: 0.1 if content.get("safe", True) else 0.8
                )
            ],
            prevention_strategies=[],
            safety_threshold=0.8
        )
        
        # Initialize related ULTRA components
        self.reasoning_graph = ReasoningGraph()
        self.logical_reasoning = LogicalReasoningEngine(
            inference_methods=["deductive", "inductive", "abductive"],
            consistency_threshold=0.8
        )
        self.bayesian_uncertainty = BayesianUncertaintyQuantification()
        
        # Generate test data
        self.ethical_actions = [{"safe": True, "fair": True} for _ in range(5)]
        self.unethical_actions = [{"safe": False, "fair": False} for _ in range(5)]
    
    def test_integration_with_reasoning_graph(self):
        """Test integration with reasoning graph."""
        # Create a reasoning graph with ethical constraints
        nodes = {
            "A": {"content": "Premise A", "type": "premise"},
            "B": {"content": "Premise B", "type": "premise"},
            "C": {"content": "Inference C", "type": "inference"},
            "D": {"content": "Action recommendation D", "type": "action", "safe": True, "fair": True}
        }
        
        edges = {
            ("A", "C"): {"relation": "support", "weight": 0.8},
            ("B", "C"): {"relation": "support", "weight": 0.7},
            ("C", "D"): {"relation": "implies", "weight": 0.9}
        }
        
        self.reasoning_graph.update_graph(nodes, edges)
        
        # Create an ethical validation step in the reasoning process
        def validate_action_nodes(graph):
            """Validate action nodes in the graph using ethical validator."""
            for node_id, node in graph.nodes.items():
                if node.get("type") == "action":
                    validation_result = self.ethical_validator.validate(node, {})
                    graph.nodes[node_id]["ethical_validation"] = validation_result
            return graph
        
        # Apply ethical validation
        updated_graph = validate_action_nodes(self.reasoning_graph)
        
        # Check that ethical validation is applied to action nodes
        for node_id, node in updated_graph.nodes.items():
            if node.get("type") == "action":
                self.assertIn("ethical_validation", node)
                self.assertTrue(node["ethical_validation"].passes_validation)
    
    def test_integration_with_logical_reasoning(self):
        """Test integration with logical reasoning engine."""
        # Define a reasoning task with ethical implications
        premises = [
            "All models trained on biased data produce biased predictions",
            "Model X is trained on biased data"
        ]
        
        conclusion = "Model X produces biased predictions"
        
        # Integrate ethical evaluation into inference process
        original_infer = self.logical_reasoning.infer
        
        def ethical_infer(premises, conclusion):
            """Extend inference with ethical validation."""
            inference_result = original_infer(premises, conclusion)
            
            # If inference suggests a potentially problematic model, evaluate ethics
            if "biased predictions" in conclusion.lower():
                action = {
                    "type": "model_deployment",
                    "model": conclusion.split()[1],  # Extract model name
                    "safe": False,
                    "fair": False
                }
                
                ethical_result = self.ethical_validator.validate(action, {})
                inference_result["ethical_validation"] = ethical_result
            
            return inference_result
        
        # Monkey patch the infer method
        self.logical_reasoning.infer = ethical_infer
        
        # Perform inference
        result = self.logical_reasoning.infer(premises, conclusion)
        
        # Check that ethical validation is included in result
        self.assertIn("ethical_validation", result)
        self.assertFalse(result["ethical_validation"].passes_validation)
    
    def test_integration_with_bayesian_uncertainty(self):
        """Test integration with Bayesian uncertainty quantification."""
        # Define a decision problem with ethical implications
        actions = [
            {"name": "deploy_model_A", "safe": True, "fair": True},
            {"name": "deploy_model_B", "safe": False, "fair": False}
        ]
        
        # Integrate ethical evaluation into decision making
        def ethical_utility(action, outcome):
            """Calculate utility considering ethical factors."""
            # Base utility
            base_utility = 0.8 if action["name"] == "deploy_model_A" else 0.9
            
            # Ethical adjustment
            ethical_result = self.ethical_validator.validate(action, {})
            ethical_factor = ethical_result.overall_score
            
            # Adjust utility
            adjusted_utility = base_utility * ethical_factor
            
            return adjusted_utility
        
        # Mock the Bayesian uncertainty quantification to use ethical utility
        self.bayesian_uncertainty.expected_utility = lambda action: ethical_utility(action, None)
        
        # Select best action
        utilities = [self.bayesian_uncertainty.expected_utility(action) for action in actions]
        best_action_index = np.argmax(utilities)
        best_action = actions[best_action_index]
        
        # Check that the ethically preferable action is selected
        self.assertEqual(best_action["name"], "deploy_model_A")
        self.assertTrue(best_action["safe"])
        self.assertTrue(best_action["fair"])
    
    def test_end_to_end_ethical_decision_workflow(self):
        """Test an end-to-end ethical decision workflow."""
        # Define a decision context
        context = {
            "domain": "loan_approval",
            "applicants": [
                {"id": 1, "gender": "female", "race": "black", "income": 60000, "credit_score": 720},
                {"id": 2, "gender": "male", "race": "white", "income": 65000, "credit_score": 700},
                {"id": 3, "gender": "female", "race": "asian", "income": 70000, "credit_score": 750},
                {"id": 4, "gender": "male", "race": "hispanic", "income": 55000, "credit_score": 680}
            ]
        }
        
        # Define model and predictions
        model_fair = {
            "name": "fair_model",
            "predictions": [0.8, 0.75, 0.85, 0.7],  # Approval probabilities
            "fair": True
        }
        
        model_unfair = {
            "name": "unfair_model",
            "predictions": [0.6, 0.8, 0.7, 0.5],  # Biased approval probabilities
            "fair": False
        }
        
        # Step 1: Evaluate fairness of models
        def evaluate_model_fairness(model, context):
            """Evaluate fairness of a model's predictions."""
            applicants = context["applicants"]
            predictions = model["predictions"]
            
            # Extract sensitive attributes
            genders = [a["gender"] for a in applicants]
            races = [a["race"] for a in applicants]
            
            # Evaluate fairness
            fairness_result = self.fairness_evaluator.evaluate(
                predictions=np.array(predictions),
                labels=None,  # No ground truth labels available
                features=None,  # No features needed for this simplified test
                sensitive_attributes={
                    "gender": np.array(genders),
                    "race": np.array(races)
                },
                fair=model["fair"]  # Pass the fair flag to the mock metric function
            )
            
            return fairness_result
        
        fairness_result_fair = evaluate_model_fairness(model_fair, context)
        fairness_result_unfair = evaluate_model_fairness(model_unfair, context)
        
        # Step 2: Create action proposals
        action_fair = {
            "type": "model_deployment",
            "model": model_fair["name"],
            "domain": context["domain"],
            "fairness_metrics": {
                "demographic_parity": 0.9,
                "equal_opportunity": 0.85
            },
            "transparent": True,
            "safe": True,
            "fair": True
        }
        
        action_unfair = {
            "type": "model_deployment",
            "model": model_unfair["name"],
            "domain": context["domain"],
            "fairness_metrics": {
                "demographic_parity": 0.5,
                "equal_opportunity": 0.4
            },
            "transparent": False,
            "safe": False,
            "fair": False
        }
        
        # Step 3: Ethical validation
        ethical_result_fair = self.ethical_validator.validate(action_fair, context)
        ethical_result_unfair = self.ethical_validator.validate(action_unfair, context)
        
        # Step 4: Harm assessment
        harm_assessment_fair = self.harm_prevention.assess_harm(action_fair)
        harm_assessment_unfair = self.harm_prevention.assess_harm(action_unfair)
        
        # Step 5: Make final decision
        def make_ethical_decision(actions, fairness_results, ethical_results, harm_assessments):
            """Make a decision considering all ethical factors."""
            scores = []
            
            for i, action in enumerate(actions):
                fairness_score = fairness_results[i].overall_score
                ethical_score = ethical_results[i].overall_score
                safety_score = 1.0 - harm_assessments[i].severity.get("physical", 0.0)
                
                # Weighted combination
                combined_score = 0.4 * fairness_score + 0.4 * ethical_score + 0.2 * safety_score
                scores.append(combined_score)
            
            # Select action with highest score
            best_index = np.argmax(scores)
            return actions[best_index], scores[best_index]
        
        selected_action, score = make_ethical_decision(
            [action_fair, action_unfair],
            [fairness_result_fair, fairness_result_unfair],
            [ethical_result_fair, ethical_result_unfair],
            [harm_assessment_fair, harm_assessment_unfair]
        )
        
        # Log decision results
        logger.info(f"\nSelected action: {selected_action['model']}")
        logger.info(f"Decision score: {score}")
        
        # Check that the fair model is selected
        self.assertEqual(selected_action["model"], "fair_model")
        self.assertTrue(selected_action["fair"])
        self.assertTrue(selected_action["safe"])
        self.assertGreaterEqual(score, 0.8)


if __name__ == "__main__":
    unittest.main()