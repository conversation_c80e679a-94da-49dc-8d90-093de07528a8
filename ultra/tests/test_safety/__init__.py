#!/usr/bin/env python3
"""
Test suite for the ULTRA safety module.

This module tests the safety mechanisms that ensure ULTRA's
self-modification, reasoning, and actions maintain system integrity,
ethical compliance, and operational safety.

The tests cover:
1. Monitoring - Detection of unsafe states or behaviors
2. Constraints - Enforcement of operational boundaries
3. Ethical Framework - Adherence to ethical guidelines
4. Safety Integration - Interaction with self-modification and other systems
"""

import unittest
import numpy as np
import tensorflow as tf
import torch
import logging
import sys
import os
from pathlib import Path

# Ensure the ultra package is in the path
sys.path.insert(0, str(Path(__file__).resolve().parents[2]))

# Import safety modules
from ultra.safety.monitoring import (
    SystemMonitor, 
    BehaviorTracker, 
    AnomalyDetection, 
    StateValidator,
    RiskAssessment
)
from ultra.safety.constraints import (
    ConstraintManager, 
    OperationalBoundary, 
    LogicalConstraint,
    ResourceConstraint, 
    EthicalConstraint, 
    InteractionConstraint
)
from ultra.safety.ethical_framework import (
    EthicalValidator, 
    ValueAlignment, 
    FairnessEvaluator,
    TransparencyManager, 
    HarmPrevention
)

# Import related modules for integration testing
from ultra.self_evolution.self_modification import SelfModificationProtocols
from ultra.neuro_symbolic.logical_reasoning import LogicalReasoningEngine
from ultra.meta_cognitive.self_critique import SelfCritiqueLoop

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("safety_tests")


class MonitoringTests(unittest.TestCase):
    """Tests for the safety monitoring subsystem."""
    
    def setUp(self):
        """Set up common test fixtures."""
        # Initialize monitoring components
        self.monitor = SystemMonitor(
            check_interval=0.1,
            critical_subsystems=[
                "CoreNeuralArchitecture", 
                "DiffusionReasoning", 
                "MetaCognitive"
            ]
        )
        self.behavior_tracker = BehaviorTracker(
            history_length=100,
            tracked_metrics=[
                "reasoning_depth", 
                "uncertainty", 
                "decision_confidence",
                "resource_utilization"
            ]
        )
        self.anomaly_detector = AnomalyDetection(
            sensitivity=0.85,
            baseline_period=1000,
            detection_algorithms=[
                "isolation_forest", 
                "local_outlier_factor", 
                "autoencoder"
            ]
        )
        self.state_validator = StateValidator(
            validation_frequency=5,
            consistency_threshold=0.9
        )
        self.risk_assessor = RiskAssessment(
            risk_threshold=0.7,
            uncertainty_weight=0.6,
            impact_weight=0.8,
            probability_models={
                "system_integrity": "beta_distribution",
                "resource_exhaustion": "poisson_distribution",
                "reasoning_error": "gaussian_distribution"
            }
        )
        
        # Generate test data
        self.normal_states = [self._generate_normal_state() for _ in range(50)]
        self.anomalous_states = [self._generate_anomalous_state() for _ in range(10)]
        
    def _generate_normal_state(self):
        """Generate a normal system state for testing."""
        return {
            "core_neural": {
                "activation_distribution": np.random.normal(0.5, 0.1, 100),
                "synaptic_weights": np.random.normal(0, 0.1, 100),
                "neuromodulation_levels": {
                    "dopamine": np.random.uniform(0.4, 0.6),
                    "serotonin": np.random.uniform(0.4, 0.6),
                    "norepinephrine": np.random.uniform(0.4, 0.6),
                    "acetylcholine": np.random.uniform(0.4, 0.6)
                },
                "pruning_rate": np.random.uniform(0.05, 0.1)
            },
            "reasoning": {
                "uncertainty": np.random.uniform(0.1, 0.3),
                "decision_confidence": np.random.uniform(0.7, 0.9),
                "reasoning_depth": np.random.randint(3, 8),
                "reasoning_paths": np.random.randint(2, 5)
            },
            "resources": {
                "memory_usage": np.random.uniform(0.4, 0.7),
                "cpu_utilization": np.random.uniform(0.3, 0.6),
                "gpu_utilization": np.random.uniform(0.4, 0.8),
                "bandwidth": np.random.uniform(0.2, 0.5)
            },
            "outputs": {
                "uncertainty_estimation": np.random.uniform(0.1, 0.3),
                "logical_consistency": np.random.uniform(0.8, 0.95),
                "ethical_compliance": np.random.uniform(0.9, 0.98)
            }
        }
    
    def _generate_anomalous_state(self):
        """Generate an anomalous system state for testing."""
        state = self._generate_normal_state()
        # Introduce anomalies
        anomaly_type = np.random.choice([
            "activation_spike", 
            "reasoning_collapse", 
            "resource_spike",
            "ethical_deviation"
        ])
        
        if anomaly_type == "activation_spike":
            state["core_neural"]["activation_distribution"] = np.random.normal(2.5, 0.5, 100)
            state["core_neural"]["neuromodulation_levels"]["dopamine"] = np.random.uniform(0.8, 0.95)
        elif anomaly_type == "reasoning_collapse":
            state["reasoning"]["uncertainty"] = np.random.uniform(0.8, 0.95)
            state["reasoning"]["decision_confidence"] = np.random.uniform(0.1, 0.3)
            state["reasoning"]["reasoning_depth"] = np.random.randint(1, 2)
        elif anomaly_type == "resource_spike":
            state["resources"]["memory_usage"] = np.random.uniform(0.9, 0.98)
            state["resources"]["cpu_utilization"] = np.random.uniform(0.95, 0.99)
        elif anomaly_type == "ethical_deviation":
            state["outputs"]["ethical_compliance"] = np.random.uniform(0.1, 0.5)
            state["outputs"]["logical_consistency"] = np.random.uniform(0.2, 0.5)
        
        return state, anomaly_type
    
    def test_system_monitoring_detects_normal_states(self):
        """Test that the system monitor correctly validates normal states."""
        for state in self.normal_states:
            result = self.monitor.check_system_state(state)
            self.assertTrue(result.is_safe)
            self.assertGreaterEqual(result.safety_score, 0.7)
            self.assertEqual(len(result.warnings), 0)
    
    def test_system_monitoring_detects_anomalies(self):
        """Test that the system monitor correctly detects anomalous states."""
        for state, anomaly_type in self.anomalous_states:
            result = self.monitor.check_system_state(state)
            self.assertFalse(result.is_safe)
            self.assertLessEqual(result.safety_score, 0.5)
            self.assertGreaterEqual(len(result.warnings), 1)
            
            # Verify the warning matches the anomaly type
            anomaly_warnings = [w for w in result.warnings if anomaly_type.lower() in w.lower()]
            self.assertGreaterEqual(len(anomaly_warnings), 1)
    
    def test_behavior_tracking(self):
        """Test behavior tracking capabilities."""
        # Train on normal behaviors
        for state in self.normal_states:
            self.behavior_tracker.track_behavior(state)
        
        # Verify behavior model is trained
        self.assertTrue(self.behavior_tracker.is_trained)
        
        # Test behavior assessment on normal data
        for state in self.normal_states:
            assessment = self.behavior_tracker.assess_behavior(state)
            self.assertGreaterEqual(assessment.normalcy_score, 0.7)
            self.assertLessEqual(assessment.anomaly_score, 0.3)
        
        # Test behavior assessment on anomalous data
        for state, _ in self.anomalous_states:
            assessment = self.behavior_tracker.assess_behavior(state)
            self.assertLessEqual(assessment.normalcy_score, 0.5)
            self.assertGreaterEqual(assessment.anomaly_score, 0.5)
    
    def test_anomaly_detection(self):
        """Test anomaly detection capabilities."""
        # Train the detector on normal data
        self.anomaly_detector.fit(self.normal_states)
        
        # Test on normal data
        for state in self.normal_states:
            result = self.anomaly_detector.detect(state)
            self.assertFalse(result.is_anomaly)
            self.assertLessEqual(result.anomaly_score, 0.3)
        
        # Test on anomalous data
        for state, _ in self.anomalous_states:
            result = self.anomaly_detector.detect(state)
            self.assertTrue(result.is_anomaly)
            self.assertGreaterEqual(result.anomaly_score, 0.7)
    
    def test_state_validation(self):
        """Test state validation capabilities."""
        # Initialize with normal states
        for state in self.normal_states[:10]:
            self.state_validator.update_baseline(state)
        
        # Test validation on normal states
        for state in self.normal_states[10:]:
            result = self.state_validator.validate(state)
            self.assertTrue(result.is_valid)
            self.assertGreaterEqual(result.consistency_score, 0.8)
        
        # Test validation on anomalous states
        for state, _ in self.anomalous_states:
            result = self.state_validator.validate(state)
            self.assertFalse(result.is_valid)
            self.assertLessEqual(result.consistency_score, 0.6)
    
    def test_risk_assessment(self):
        """Test risk assessment capabilities."""
        # Test risk assessment on normal states
        for state in self.normal_states:
            assessment = self.risk_assessor.assess_risk(state)
            self.assertLessEqual(assessment.overall_risk, 0.3)
            self.assertEqual(assessment.risk_level, "low")
            
        # Test risk assessment on anomalous states
        for state, anomaly_type in self.anomalous_states:
            assessment = self.risk_assessor.assess_risk(state)
            self.assertGreaterEqual(assessment.overall_risk, 0.6)
            self.assertIn(assessment.risk_level, ["medium", "high", "critical"])
            
            # Check that the highest risk factor matches the anomaly type
            if anomaly_type == "activation_spike":
                self.assertEqual(assessment.highest_risk_factor, "system_integrity")
            elif anomaly_type == "reasoning_collapse":
                self.assertEqual(assessment.highest_risk_factor, "reasoning_error")
            elif anomaly_type == "resource_spike":
                self.assertEqual(assessment.highest_risk_factor, "resource_exhaustion")


class ConstraintTests(unittest.TestCase):
    """Tests for the constraint management subsystem."""
    
    def setUp(self):
        """Set up common test fixtures."""
        # Initialize constraint components
        self.constraint_manager = ConstraintManager()
        
        # Define operational boundaries
        self.resource_boundary = OperationalBoundary(
            name="resource_limits",
            bounds={
                "memory": (0.0, 0.9),
                "cpu": (0.0, 0.8),
                "gpu": (0.0, 0.95),
                "network": (0.0, 0.7)
            }
        )
        
        self.reasoning_boundary = OperationalBoundary(
            name="reasoning_bounds",
            bounds={
                "recursion_depth": (0, 10),
                "uncertainty": (0.0, 0.7),
                "processing_time": (0, 60.0)
            }
        )
        
        # Define logical constraints
        self.consistency_constraint = LogicalConstraint(
            name="consistency_constraint",
            condition=lambda state: self._check_logical_consistency(state),
            description="Ensures logical consistency across reasoning processes"
        )
        
        # Define resource constraints
        self.resource_constraint = ResourceConstraint(
            name="memory_constraint",
            resource_type="memory",
            max_usage=0.85,
            critical_threshold=0.95,
            recovery_action="garbage_collect"
        )
        
        # Define ethical constraints
        self.ethical_constraint = EthicalConstraint(
            name="fairness_constraint",
            validation_function=lambda action, context: self._validate_fairness(action, context),
            description="Ensures fairness across demographic groups",
            priority="high"
        )
        
        # Define interaction constraints
        self.interaction_constraint = InteractionConstraint(
            name="response_constraint",
            validation_function=lambda response: self._validate_response(response),
            description="Ensures responses are safe and appropriate",
            priority="critical"
        )
        
        # Register constraints with the manager
        self.constraint_manager.register_boundary(self.resource_boundary)
        self.constraint_manager.register_boundary(self.reasoning_boundary)
        self.constraint_manager.register_constraint(self.consistency_constraint)
        self.constraint_manager.register_constraint(self.resource_constraint)
        self.constraint_manager.register_constraint(self.ethical_constraint)
        self.constraint_manager.register_constraint(self.interaction_constraint)
        
        # Generate test data
        self.valid_states = [self._generate_valid_state() for _ in range(20)]
        self.invalid_states = [self._generate_invalid_state() for _ in range(10)]
        
        self.valid_actions = [self._generate_valid_action() for _ in range(20)]
        self.invalid_actions = [self._generate_invalid_action() for _ in range(10)]
    
    def _check_logical_consistency(self, state):
        """Validate logical consistency of a state."""
        # In a real implementation, this would check for contradictions
        # Simplified version for testing
        if not state or not isinstance(state, dict):
            return False
        
        if "reasoning" not in state:
            return False
            
        if "logical_consistency_score" in state["reasoning"]:
            return state["reasoning"]["logical_consistency_score"] >= 0.7
            
        return True
    
    def _validate_fairness(self, action, context):
        """Validate fairness of an action in a given context."""
        # In a real implementation, this would check for disparate impact
        # Simplified version for testing
        if not action or not context:
            return False
            
        if "fairness_metrics" not in action:
            return False
            
        # Check for equal treatment across groups
        demographic_variance = action["fairness_metrics"].get("demographic_variance", 1.0)
        return demographic_variance <= 0.2
    
    def _validate_response(self, response):
        """Validate that a response is safe and appropriate."""
        # In a real implementation, this would check for harmful content
        # Simplified version for testing
        if not response:
            return False
            
        if "safety_metrics" not in response:
            return False
            
        # Check safety score
        safety_score = response["safety_metrics"].get("overall_safety", 0.0)
        return safety_score >= 0.9
    
    def _generate_valid_state(self):
        """Generate a valid system state for testing."""
        return {
            "reasoning": {
                "logical_consistency_score": np.random.uniform(0.8, 0.99),
                "recursion_depth": np.random.randint(1, 8),
                "uncertainty": np.random.uniform(0.1, 0.5),
                "processing_time": np.random.uniform(0.1, 30.0)
            },
            "resources": {
                "memory": np.random.uniform(0.1, 0.8),
                "cpu": np.random.uniform(0.1, 0.7),
                "gpu": np.random.uniform(0.2, 0.8),
                "network": np.random.uniform(0.1, 0.6)
            }
        }
    
    def _generate_invalid_state(self):
        """Generate an invalid system state for testing."""
        state = self._generate_valid_state()
        violation_type = np.random.choice([
            "logical_consistency", 
            "resource_bound", 
            "reasoning_bound"
        ])
        
        if violation_type == "logical_consistency":
            state["reasoning"]["logical_consistency_score"] = np.random.uniform(0.1, 0.5)
        elif violation_type == "resource_bound":
            resource = np.random.choice(["memory", "cpu", "gpu", "network"])
            state["resources"][resource] = np.random.uniform(0.91, 0.99)
        elif violation_type == "reasoning_bound":
            bound = np.random.choice(["recursion_depth", "uncertainty", "processing_time"])
            if bound == "recursion_depth":
                state["reasoning"][bound] = np.random.randint(11, 20)
            elif bound == "uncertainty":
                state["reasoning"][bound] = np.random.uniform(0.8, 0.99)
            elif bound == "processing_time":
                state["reasoning"][bound] = np.random.uniform(70.0, 120.0)
        
        return state, violation_type
    
    def _generate_valid_action(self):
        """Generate a valid action for testing."""
        return {
            "type": np.random.choice(["prediction", "recommendation", "classification"]),
            "confidence": np.random.uniform(0.7, 0.95),
            "fairness_metrics": {
                "demographic_variance": np.random.uniform(0.05, 0.15),
                "group_benefit_ratio": np.random.uniform(0.9, 1.1)
            },
            "safety_metrics": {
                "overall_safety": np.random.uniform(0.9, 0.99),
                "harmful_content_probability": np.random.uniform(0.01, 0.05),
                "bias_score": np.random.uniform(0.01, 0.1)
            }
        }
    
    def _generate_invalid_action(self):
        """Generate an invalid action for testing."""
        action = self._generate_valid_action()
        violation_type = np.random.choice(["fairness", "safety"])
        
        if violation_type == "fairness":
            action["fairness_metrics"]["demographic_variance"] = np.random.uniform(0.3, 0.8)
            action["fairness_metrics"]["group_benefit_ratio"] = np.random.uniform(0.5, 0.7)
        elif violation_type == "safety":
            action["safety_metrics"]["overall_safety"] = np.random.uniform(0.3, 0.7)
            action["safety_metrics"]["harmful_content_probability"] = np.random.uniform(0.3, 0.6)
        
        return action, violation_type
    
    def test_operational_boundary_validation(self):
        """Test operational boundary validation."""
        # Test with valid states
        for state in self.valid_states:
            resource_result = self.resource_boundary.validate(state)
            reasoning_result = self.reasoning_boundary.validate(state)
            
            self.assertTrue(resource_result.is_valid)
            self.assertTrue(reasoning_result.is_valid)
        
        # Test with invalid states
        for state, violation_type in self.invalid_states:
            if violation_type == "resource_bound":
                result = self.resource_boundary.validate(state)
                self.assertFalse(result.is_valid)
            elif violation_type == "reasoning_bound":
                result = self.reasoning_boundary.validate(state)
                self.assertFalse(result.is_valid)
    
    def test_logical_constraint_validation(self):
        """Test logical constraint validation."""
        # Test with valid states
        for state in self.valid_states:
            result = self.consistency_constraint.validate(state)
            self.assertTrue(result.is_valid)
        
        # Test with invalid states
        for state, violation_type in self.invalid_states:
            if violation_type == "logical_consistency":
                result = self.consistency_constraint.validate(state)
                self.assertFalse(result.is_valid)
    
    def test_resource_constraint_validation(self):
        """Test resource constraint validation."""
        # Test with valid states
        for state in self.valid_states:
            result = self.resource_constraint.validate(state)
            self.assertTrue(result.is_valid)
        
        # Test with invalid states
        for state, violation_type in self.invalid_states:
            if violation_type == "resource_bound" and state["resources"]["memory"] > 0.85:
                result = self.resource_constraint.validate(state)
                self.assertFalse(result.is_valid)
    
    def test_ethical_constraint_validation(self):
        """Test ethical constraint validation."""
        # Test with valid actions
        for action in self.valid_actions:
            context = {"user_demographics": "diverse"}
            result = self.ethical_constraint.validate(action, context)
            self.assertTrue(result.is_valid)
        
        # Test with invalid actions
        for action, violation_type in self.invalid_actions:
            context = {"user_demographics": "diverse"}
            if violation_type == "fairness":
                result = self.ethical_constraint.validate(action, context)
                self.assertFalse(result.is_valid)
    
    def test_interaction_constraint_validation(self):
        """Test interaction constraint validation."""
        # Test with valid actions
        for action in self.valid_actions:
            result = self.interaction_constraint.validate(action)
            self.assertTrue(result.is_valid)
        
        # Test with invalid actions
        for action, violation_type in self.invalid_actions:
            if violation_type == "safety":
                result = self.interaction_constraint.validate(action)
                self.assertFalse(result.is_valid)
    
    def test_constraint_manager(self):
        """Test constraint manager validation."""
        # Test with valid states
        for state in self.valid_states:
            # Add an action to the state for testing action constraints
            state["action"] = self._generate_valid_action()
            state["context"] = {"user_demographics": "diverse"}
            
            result = self.constraint_manager.validate_all(state)
            self.assertTrue(result.all_valid)
            self.assertEqual(len(result.violations), 0)
        
        # Test with invalid states
        for state, violation_type in self.invalid_states:
            # Add an invalid action to the state
            invalid_action, action_violation = self._generate_invalid_action()
            state["action"] = invalid_action
            state["context"] = {"user_demographics": "diverse"}
            
            result = self.constraint_manager.validate_all(state)
            self.assertFalse(result.all_valid)
            self.assertGreaterEqual(len(result.violations), 1)
            
            # Check that the violation matches the type
            if violation_type == "logical_consistency":
                self.assertIn("consistency_constraint", [v.constraint_name for v in result.violations])
            elif violation_type == "resource_bound" and state["resources"]["memory"] > 0.85:
                self.assertIn("memory_constraint", [v.constraint_name for v in result.violations])
            
            if action_violation == "fairness":
                self.assertIn("fairness_constraint", [v.constraint_name for v in result.violations])
            elif action_violation == "safety":
                self.assertIn("response_constraint", [v.constraint_name for v in result.violations])


class EthicalFrameworkTests(unittest.TestCase):
    """Tests for the ethical framework subsystem."""
    
    def setUp(self):
        """Set up common test fixtures."""
        # Initialize ethical framework components
        self.ethical_validator = EthicalValidator(
            guideline_sources=["internal", "external"],
            validation_threshold=0.8
        )
        
        self.value_alignment = ValueAlignment(
            value_framework="universal_values",
            alignment_metrics=[
                "fairness", 
                "beneficence", 
                "autonomy", 
                "justice",
                "non_maleficence"
            ]
        )
        
        self.fairness_evaluator = FairnessEvaluator(
            fairness_metrics=[
                "demographic_parity", 
                "equal_opportunity", 
                "predictive_parity"
            ],
            sensitive_attributes=["race", "gender", "age", "disability"]
        )
        
        self.transparency_manager = TransparencyManager(
            explanation_methods=[
                "feature_importance", 
                "counterfactual", 
                "rule_based"
            ],
            transparency_level="high"
        )
        
        self.harm_prevention = HarmPrevention(
            harm_categories=[
                "physical", 
                "psychological", 
                "social", 
                "financial"
            ],
            prevention_threshold=0.9
        )
        
        # Generate test data
        self.ethical_actions = [self._generate_ethical_action() for _ in range(20)]
        self.unethical_actions = [self._generate_unethical_action() for _ in range(10)]
        
        self.fair_predictions = [self._generate_fair_prediction() for _ in range(20)]
        self.unfair_predictions = [self._generate_unfair_prediction() for _ in range(10)]
        
        self.safe_outputs = [self._generate_safe_output() for _ in range(20)]
        self.harmful_outputs = [self._generate_harmful_output() for _ in range(10)]
    
    def _generate_ethical_action(self):
        """Generate an ethical action for testing."""
        return {
            "type": np.random.choice(["medical_recommendation", "financial_advice", "content_filtering"]),
            "description": f"Action of type {np.random.choice(['assist', 'inform', 'protect'])}",
            "ethical_metrics": {
                "fairness": np.random.uniform(0.8, 0.95),
                "beneficence": np.random.uniform(0.8, 0.95),
                "autonomy": np.random.uniform(0.8, 0.95),
                "justice": np.random.uniform(0.8, 0.95),
                "non_maleficence": np.random.uniform(0.9, 0.99),
            },
            "consequences": {
                "positive": np.random.uniform(0.7, 0.9),
                "negative": np.random.uniform(0.05, 0.15),
                "uncertain": np.random.uniform(0.05, 0.15)
            }
        }
    
    def _generate_unethical_action(self):
        """Generate an unethical action for testing."""
        action = self._generate_ethical_action()
        violation_type = np.random.choice([
            "fairness", 
            "autonomy", 
            "non_maleficence",
            "consequences"
        ])
        
        if violation_type == "fairness":
            action["ethical_metrics"]["fairness"] = np.random.uniform(0.2, 0.5)
        elif violation_type == "autonomy":
            action["ethical_metrics"]["autonomy"] = np.random.uniform(0.2, 0.5)
        elif violation_type == "non_maleficence":
            action["ethical_metrics"]["non_maleficence"] = np.random.uniform(0.2, 0.5)
        elif violation_type == "consequences":
            action["consequences"]["positive"] = np.random.uniform(0.1, 0.3)
            action["consequences"]["negative"] = np.random.uniform(0.6, 0.8)
        
        return action, violation_type
    
    def _generate_fair_prediction(self):
        """Generate a fair prediction for testing."""
        # Create synthetic data with multiple demographic groups
        num_samples = 100
        demographics = {
            "group_A": {
                "samples": num_samples // 4,
                "positive_rate": np.random.uniform(0.3, 0.4)
            },
            "group_B": {
                "samples": num_samples // 4,
                "positive_rate": np.random.uniform(0.3, 0.4)
            },
            "group_C": {
                "samples": num_samples // 4,
                "positive_rate": np.random.uniform(0.3, 0.4)
            },
            "group_D": {
                "samples": num_samples // 4,
                "positive_rate": np.random.uniform(0.3, 0.4)
            }
        }
        
        # Generate synthetic predictions
        predictions = []
        labels = []
        groups = []
        
        for group, data in demographics.items():
            group_predictions = np.random.uniform(0, 1, data["samples"])
            group_labels = np.random.binomial(1, data["positive_rate"], data["samples"])
            
            predictions.extend(group_predictions)
            labels.extend(group_labels)
            groups.extend([group] * data["samples"])
        
        return {
            "predictions": np.array(predictions),
            "labels": np.array(labels),
            "demographic_groups": np.array(groups),
            "fairness_metrics": {
                "demographic_parity_difference": np.random.uniform(0.0, 0.05),
                "equal_opportunity_difference": np.random.uniform(0.0, 0.05),
                "predictive_parity_difference": np.random.uniform(0.0, 0.05)
            }
        }
    
    def _generate_unfair_prediction(self):
        """Generate an unfair prediction for testing."""
        prediction = self._generate_fair_prediction()
        
        # Introduce bias by modifying the fairness metrics
        violation_type = np.random.choice([
            "demographic_parity", 
            "equal_opportunity", 
            "predictive_parity"
        ])
        
        if violation_type == "demographic_parity":
            prediction["fairness_metrics"]["demographic_parity_difference"] = np.random.uniform(0.2, 0.4)
        elif violation_type == "equal_opportunity":
            prediction["fairness_metrics"]["equal_opportunity_difference"] = np.random.uniform(0.2, 0.4)
        elif violation_type == "predictive_parity":
            prediction["fairness_metrics"]["predictive_parity_difference"] = np.random.uniform(0.2, 0.4)
        
        return prediction, violation_type
    
    def _generate_safe_output(self):
        """Generate a safe output for testing."""
        output_type = np.random.choice(["text", "recommendation", "decision"])
        
        if output_type == "text":
            return {
                "type": "text",
                "content": "This is a safe and helpful message.",
                "safety_metrics": {
                    "harmful_content": np.random.uniform(0.01, 0.05),
                    "offensive_language": np.random.uniform(0.01, 0.05),
                    "misinformation": np.random.uniform(0.01, 0.05),
                    "manipulation": np.random.uniform(0.01, 0.05)
                },
                "harm_assessment": {
                    "physical": np.random.uniform(0.01, 0.05),
                    "psychological": np.random.uniform(0.01, 0.05),
                    "social": np.random.uniform(0.01, 0.05),
                    "financial": np.random.uniform(0.01, 0.05)
                }
            }
        elif output_type == "recommendation":
            return {
                "type": "recommendation",
                "options": ["option_A", "option_B", "option_C"],
                "scores": [0.8, 0.6, 0.4],
                "safety_metrics": {
                    "harmful_outcomes": np.random.uniform(0.01, 0.05),
                    "manipulative_intent": np.random.uniform(0.01, 0.05)
                },
                "harm_assessment": {
                    "physical": np.random.uniform(0.01, 0.05),
                    "psychological": np.random.uniform(0.01, 0.05),
                    "social": np.random.uniform(0.01, 0.05),
                    "financial": np.random.uniform(0.01, 0.05)
                }
            }
        elif output_type == "decision":
            return {
                "type": "decision",
                "decision": np.random.choice(["approve", "deny", "review"]),
                "confidence": np.random.uniform(0.7, 0.9),
                "safety_metrics": {
                    "fairness": np.random.uniform(0.8, 0.95),
                    "transparency": np.random.uniform(0.8, 0.95)
                },
                "harm_assessment": {
                    "physical": np.random.uniform(0.01, 0.05),
                    "psychological": np.random.uniform(0.01, 0.05),
                    "social": np.random.uniform(0.01, 0.05),
                    "financial": np.random.uniform(0.01, 0.05)
                }
            }
    
    def _generate_harmful_output(self):
        """Generate a harmful output for testing."""
        output = self._generate_safe_output()
        output_type = output["type"]
        harm_type = np.random.choice([
            "physical", 
            "psychological", 
            "social", 
            "financial"
        ])
        
        # Modify the harm assessment
        output["harm_assessment"][harm_type] = np.random.uniform(0.5, 0.8)
        
        # Modify safety metrics based on output type
        if output_type == "text":
            if harm_type == "psychological":
                output["safety_metrics"]["offensive_language"] = np.random.uniform(0.5, 0.8)
            elif harm_type == "social":
                output["safety_metrics"]["misinformation"] = np.random.uniform(0.5, 0.8)
        elif output_type == "recommendation":
            output["safety_metrics"]["harmful_outcomes"] = np.random.uniform(0.5, 0.8)
        elif output_type == "decision":
            output["safety_metrics"]["fairness"] = np.random.uniform(0.2, 0.5)
        
        return output, harm_type
    
    def test_ethical_validation(self):
        """Test ethical validation capabilities."""
        # Test with ethical actions
        for action in self.ethical_actions:
            result = self.ethical_validator.validate(action)
            self.assertTrue(result.is_ethical)
            self.assertGreaterEqual(result.ethics_score, 0.8)
            self.assertEqual(len(result.violations), 0)
        
        # Test with unethical actions
        for action, violation_type in self.unethical_actions:
            result = self.ethical_validator.validate(action)
            self.assertFalse(result.is_ethical)
            self.assertLessEqual(result.ethics_score, 0.7)
            self.assertGreaterEqual(len(result.violations), 1)
            
            # Check that the violation matches the type
            violation_names = [v.principle for v in result.violations]
            if violation_type == "fairness":
                self.assertIn("fairness", violation_names)
            elif violation_type == "autonomy":
                self.assertIn("autonomy", violation_names)
            elif violation_type == "non_maleficence":
                self.assertIn("non_maleficence", violation_names)
    
    def test_value_alignment(self):
        """Test value alignment capabilities."""
        # Test with ethical actions
        for action in self.ethical_actions:
            result = self.value_alignment.evaluate(action)
            self.assertTrue(result.is_aligned)
            self.assertGreaterEqual(result.alignment_score, 0.8)
            
            # Check individual value scores
            for value in self.value_alignment.alignment_metrics:
                self.assertGreaterEqual(result.value_scores[value], 0.7)
        
        # Test with unethical actions
        for action, violation_type in self.unethical_actions:
            result = self.value_alignment.evaluate(action)
            self.assertFalse(result.is_aligned)
            self.assertLessEqual(result.alignment_score, 0.7)
            
            # Check that the violated value has a low score
            if violation_type in self.value_alignment.alignment_metrics:
                self.assertLessEqual(result.value_scores[violation_type], 0.6)
    
    def test_fairness_evaluation(self):
        """Test fairness evaluation capabilities."""
        # Test with fair predictions
        for prediction in self.fair_predictions:
            result = self.fairness_evaluator.evaluate(
                predictions=prediction["predictions"],
                labels=prediction["labels"],
                demographic_groups=prediction["demographic_groups"]
            )
            self.assertTrue(result.is_fair)
            self.assertGreaterEqual(result.fairness_score, 0.8)
            
            # Check individual fairness metrics
            for metric in self.fairness_evaluator.fairness_metrics:
                self.assertGreaterEqual(result.metric_scores[metric], 0.7)
        
        # Test with unfair predictions
        for prediction, violation_type in self.unfair_predictions:
            result = self.fairness_evaluator.evaluate(
                predictions=prediction["predictions"],
                labels=prediction["labels"],
                demographic_groups=prediction["demographic_groups"]
            )
            self.assertFalse(result.is_fair)
            self.assertLessEqual(result.fairness_score, 0.7)
            
            # Check that the violated metric has a low score
            if violation_type in self.fairness_evaluator.fairness_metrics:
                self.assertLessEqual(result.metric_scores[violation_type], 0.6)
    
    def test_transparency_management(self):
        """Test transparency management capabilities."""
        # Test explanation generation
        model_input = {
            "features": np.random.rand(10),
            "feature_names": [f"feature_{i}" for i in range(10)]
        }
        
        model_prediction = {
            "output": np.random.choice([0, 1]),
            "probability": np.random.uniform(0.6, 0.9)
        }
        
        # Test different explanation methods
        for method in self.transparency_manager.explanation_methods:
            explanation = self.transparency_manager.generate_explanation(
                model_input=model_input,
                model_prediction=model_prediction,
                method=method
            )
            
            self.assertIsNotNone(explanation)
            self.assertEqual(explanation.method, method)
            self.assertGreaterEqual(explanation.quality_score, 0.7)
            
            if method == "feature_importance":
                self.assertIn("feature_importance", explanation.content)
                self.assertEqual(len(explanation.content["feature_importance"]), 10)
            elif method == "counterfactual":
                self.assertIn("counterfactual_example", explanation.content)
                self.assertEqual(len(explanation.content["counterfactual_example"]), 10)
            elif method == "rule_based":
                self.assertIn("rules", explanation.content)
                self.assertGreaterEqual(len(explanation.content["rules"]), 1)
    
    def test_harm_prevention(self):
        """Test harm prevention capabilities."""
        # Test with safe outputs
        for output in self.safe_outputs:
            result = self.harm_prevention.assess(output)
            self.assertTrue(result.is_safe)
            self.assertGreaterEqual(result.safety_score, 0.9)
            self.assertEqual(len(result.harm_alerts), 0)
        
        # Test with harmful outputs
        for output, harm_type in self.harmful_outputs:
            result = self.harm_prevention.assess(output)
            self.assertFalse(result.is_safe)
            self.assertLessEqual(result.safety_score, 0.7)
            self.assertGreaterEqual(len(result.harm_alerts), 1)
            
            # Check that the alert matches the harm type
            self.assertIn(harm_type, [a.harm_category for a in result.harm_alerts])


class SafetyIntegrationTests(unittest.TestCase):
    """Tests for safety integration with other system components."""
    
    def setUp(self):
        """Set up common test fixtures."""
        # Initialize safety components
        self.system_monitor = SystemMonitor(
            check_interval=0.1,
            critical_subsystems=["CoreNeuralArchitecture", "DiffusionReasoning"]
        )
        
        self.constraint_manager = ConstraintManager()
        self.constraint_manager.register_constraint(LogicalConstraint(
            name="reasoning_consistency",
            condition=lambda state: state.get("consistency_score", 0) >= 0.8,
            description="Ensures logical consistency in reasoning"
        ))
        
        self.ethical_validator = EthicalValidator(
            guideline_sources=["internal"],
            validation_threshold=0.8
        )
        
        # Initialize other system components
        self.self_modification = SelfModificationProtocols(
            approval_threshold=0.9,
            safety_checks=["functional", "ethical", "security"]
        )
        
        self.logical_reasoning = LogicalReasoningEngine(
            inference_methods=["deductive", "inductive", "abductive"],
            consistency_threshold=0.9
        )
        
        self.self_critique = SelfCritiqueLoop(
            critique_depth=3,
            refinement_threshold=0.7
        )
        
        # Generate test data
        self.valid_modifications = [self._generate_valid_modification() for _ in range(10)]
        self.invalid_modifications = [self._generate_invalid_modification() for _ in range(10)]
        
        self.valid_reasoning_paths = [self._generate_valid_reasoning() for _ in range(10)]
        self.invalid_reasoning_paths = [self._generate_invalid_reasoning() for _ in range(10)]
    
    def _generate_valid_modification(self):
        """Generate a valid self-modification for testing."""
        return {
            "type": np.random.choice(["parameter_update", "architecture_change", "algorithm_refinement"]),
            "target_module": np.random.choice(["CoreNeuralArchitecture", "DiffusionReasoning", "MetaCognitive"]),
            "description": "Valid modification to improve performance",
            "expected_impact": {
                "performance": np.random.uniform(0.1, 0.3),
                "efficiency": np.random.uniform(0.1, 0.3),
                "robustness": np.random.uniform(0.1, 0.3)
            },
            "safety_assessment": {
                "functional_integrity": np.random.uniform(0.85, 0.99),
                "ethical_alignment": np.random.uniform(0.9, 0.99),
                "security_implications": np.random.uniform(0.9, 0.99),
                "overall_safety": np.random.uniform(0.9, 0.99)
            },
            "modification_code": "def update_parameters(module): pass",
            "verification_tests": ["unit_tests", "integration_tests", "safety_tests"]
        }
    
    def _generate_invalid_modification(self):
        """Generate an invalid self-modification for testing."""
        modification = self._generate_valid_modification()
        violation_type = np.random.choice([
            "functional", 
            "ethical", 
            "security"
        ])
        
        if violation_type == "functional":
            modification["safety_assessment"]["functional_integrity"] = np.random.uniform(0.4, 0.7)
        elif violation_type == "ethical":
            modification["safety_assessment"]["ethical_alignment"] = np.random.uniform(0.4, 0.7)
        elif violation_type == "security":
            modification["safety_assessment"]["security_implications"] = np.random.uniform(0.4, 0.7)
        
        modification["safety_assessment"]["overall_safety"] = np.random.uniform(0.4, 0.7)
        
        return modification, violation_type
    
    def _generate_valid_reasoning(self):
        """Generate a valid reasoning path for testing."""
        return {
            "problem": "Sample logical problem",
            "reasoning_steps": [
                {"step": 1, "content": "Initial premise", "type": "premise"},
                {"step": 2, "content": "Intermediate deduction", "type": "deduction"},
                {"step": 3, "content": "Conclusion", "type": "conclusion"}
            ],
            "consistency_score": np.random.uniform(0.85, 0.99),
            "confidence": np.random.uniform(0.8, 0.95),
            "verified_by": ["consistency_check", "rule_verification"],
            "critique": {
                "logical_errors": 0,
                "missing_steps": 0,
                "alternative_paths": 1
            }
        }
    
    def _generate_invalid_reasoning(self):
        """Generate an invalid reasoning path for testing."""
        reasoning = self._generate_valid_reasoning()
        violation_type = np.random.choice([
            "consistency", 
            "logical_errors", 
            "missing_steps"
        ])
        
        if violation_type == "consistency":
            reasoning["consistency_score"] = np.random.uniform(0.4, 0.7)
        elif violation_type == "logical_errors":
            reasoning["critique"]["logical_errors"] = np.random.randint(1, 3)
        elif violation_type == "missing_steps":
            reasoning["critique"]["missing_steps"] = np.random.randint(1, 3)
        
        return reasoning, violation_type
    
    def test_safe_self_modification(self):
        """Test integration of safety checks with self-modification."""
        # Test with valid modifications
        for modification in self.valid_modifications:
            # Apply safety checks
            monitor_result = self.system_monitor.check_modification_safety(modification)
            constraint_result = self.constraint_manager.validate_modification(modification)
            ethical_result = self.ethical_validator.validate_modification(modification)
            
            # Check if all safety checks pass
            self.assertTrue(monitor_result.is_safe)
            self.assertTrue(constraint_result.is_valid)
            self.assertTrue(ethical_result.is_ethical)
            
            # Test integration with self-modification process
            approval_result = self.self_modification.approve_modification(
                modification=modification,
                safety_results={
                    "monitor": monitor_result,
                    "constraints": constraint_result,
                    "ethics": ethical_result
                }
            )
            
            self.assertTrue(approval_result.is_approved)
            self.assertGreaterEqual(approval_result.approval_score, 0.9)
        
        # Test with invalid modifications
        for modification, violation_type in self.invalid_modifications:
            # Apply safety checks
            monitor_result = self.system_monitor.check_modification_safety(modification)
            constraint_result = self.constraint_manager.validate_modification(modification)
            ethical_result = self.ethical_validator.validate_modification(modification)
            
            # Test integration with self-modification process
            approval_result = self.self_modification.approve_modification(
                modification=modification,
                safety_results={
                    "monitor": monitor_result,
                    "constraints": constraint_result,
                    "ethics": ethical_result
                }
            )
            
            self.assertFalse(approval_result.is_approved)
            self.assertLessEqual(approval_result.approval_score, 0.7)
            
            # Check that the rejection reason matches the violation type
            self.assertIn(violation_type, approval_result.rejection_reasons)
    
    def test_safe_reasoning_integration(self):
        """Test integration of safety checks with reasoning processes."""
        # Test with valid reasoning paths
        for reasoning in self.valid_reasoning_paths:
            # Apply constraint check
            constraint_result = self.constraint_manager.validate_reasoning(reasoning)
            self.assertTrue(constraint_result.is_valid)
            
            # Test integration with logical reasoning
            validation_result = self.logical_reasoning.validate_reasoning_path(
                reasoning_path=reasoning,
                safety_results={"constraints": constraint_result}
            )
            
            self.assertTrue(validation_result.is_valid)
            self.assertGreaterEqual(validation_result.validity_score, 0.9)
            
            # Test integration with self-critique
            critique_result = self.self_critique.critique_with_safety(
                reasoning_path=reasoning,
                safety_results={"constraints": constraint_result}
            )
            
            self.assertGreaterEqual(critique_result.quality_score, 0.8)
            self.assertEqual(len(critique_result.safety_issues), 0)
        
        # Test with invalid reasoning paths
        for reasoning, violation_type in self.invalid_reasoning_paths:
            # Apply constraint check
            constraint_result = self.constraint_manager.validate_reasoning(reasoning)
            self.assertFalse(constraint_result.is_valid)
            
            # Test integration with logical reasoning
            validation_result = self.logical_reasoning.validate_reasoning_path(
                reasoning_path=reasoning,
                safety_results={"constraints": constraint_result}
            )
            
            self.assertFalse(validation_result.is_valid)
            self.assertLessEqual(validation_result.validity_score, 0.7)
            
            # Test integration with self-critique
            critique_result = self.self_critique.critique_with_safety(
                reasoning_path=reasoning,
                safety_results={"constraints": constraint_result}
            )
            
            self.assertLessEqual(critique_result.quality_score, 0.7)
            self.assertGreaterEqual(len(critique_result.safety_issues), 1)
            
            # Check that the safety issue matches the violation type
            if violation_type == "consistency":
                self.assertIn("consistency", [i.issue_type for i in critique_result.safety_issues])
            elif violation_type == "logical_errors":
                self.assertIn("logical_error", [i.issue_type for i in critique_result.safety_issues])
            elif violation_type == "missing_steps":
                self.assertIn("incomplete_reasoning", [i.issue_type for i in critique_result.safety_issues])


if __name__ == "__main__":
    unittest.main()