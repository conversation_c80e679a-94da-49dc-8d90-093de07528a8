#!/usr/bin/env python3
"""
Comprehensive test suite for the Reverse Diffusion Reasoning component of ULTRA.

This module implements production-grade tests for reverse diffusion reasoning,
including goal-directed reasoning, constrained reasoning, multi-objective optimization,
and uncertainty-aware reasoning pathways.

Based on the mathematical formulations:
- Guided reverse diffusion: μ_θ(z_t,t,y) = μ_θ(z_t,t) + γ_t∇_{z_t}log p(y|z_t)
- Multi-constraint guidance: μ_θ(z_t,t,{y_i}) = μ_θ(z_t,t) + Σᵢγ_{t,i}∇_{z_t}log p(y_i|z_t)
- Uncertainty quantification through variational inference
"""

import unittest
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.distributions import MultivariateNormal, Normal, Categorical
from scipy.stats import entropy, wasserstein_distance
from scipy.spatial.distance import pdist, squareform
import pytest
import matplotlib.pyplot as plt
from pathlib import Path
import sys
import os
import logging
import json
import pickle
from typing import List, Dict, Tuple, Optional, Callable, Union
from dataclasses import dataclass
from collections import defaultdict
import warnings

# Suppress warnings for cleaner test output
warnings.filterwarnings("ignore", category=UserWarning)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Add ULTRA module to path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../')))

try:
    from ultra.diffusion_reasoning.conceptual_diffusion import ConceptualDiffusion
    from ultra.diffusion_reasoning.thought_latent_space import ThoughtLatentSpace
    from ultra.diffusion_reasoning.reverse_diffusion import ReverseDiffusionReasoning
    from ultra.diffusion_reasoning.bayesian_uncertainty import BayesianUncertaintyQuantification
    from ultra.diffusion_reasoning.probabilistic_inference import ProbabilisticInferenceEngine
except ImportError as e:
    logger.error(f"Failed to import ULTRA modules: {e}")
    # Create mock modules for testing if imports fail
    pass


@dataclass
class ReasoningConstraint:
    """Data class for reasoning constraints."""
    concept_id: str
    relation_type: str  # 'similarity', 'dissimilarity', 'causal', 'temporal'
    strength: float
    target_vector: torch.Tensor
    tolerance: float = 0.1


@dataclass
class ReasoningGoal:
    """Data class for reasoning goals."""
    goal_id: str
    target_concept: torch.Tensor
    priority: float
    success_threshold: float = 0.8
    max_steps: int = 50


class DiffusionGuidanceFunction:
    """
    Advanced guidance function for diffusion-based reasoning.
    Implements multiple guidance mechanisms including similarity, dissimilarity,
    causal, and temporal constraints.
    """
    
    def __init__(self, device: torch.device = None):
        self.device = device or torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.guidance_history = []
        
    def similarity_guidance(self, x: torch.Tensor, target: torch.Tensor, 
                          strength: float = 1.0) -> torch.Tensor:
        """
        Compute similarity-based guidance gradient.
        
        Args:
            x: Current concept state [batch_size, concept_dim]
            target: Target concept [concept_dim]
            strength: Guidance strength
            
        Returns:
            Guidance gradient [batch_size, concept_dim]
        """
        # Ensure target has correct dimensions
        if target.dim() == 1:
            target = target.unsqueeze(0).expand_as(x)
        
        # Compute cosine similarity gradient
        x_norm = F.normalize(x, dim=-1)
        target_norm = F.normalize(target, dim=-1)
        
        # Gradient of cosine similarity w.r.t. x
        cosine_sim = torch.sum(x_norm * target_norm, dim=-1, keepdim=True)
        
        # d/dx [x·y / (||x|| ||y||)] = y/||y|| / ||x|| - x * (x·y) / (||x||³ ||y||)
        x_magnitude = torch.norm(x, dim=-1, keepdim=True) + 1e-8
        gradient = (target_norm / x_magnitude - 
                   x * cosine_sim.unsqueeze(-1) / (x_magnitude ** 3))
        
        return strength * gradient
    
    def dissimilarity_guidance(self, x: torch.Tensor, avoid_target: torch.Tensor,
                             strength: float = 1.0) -> torch.Tensor:
        """
        Compute dissimilarity-based guidance gradient.
        
        Args:
            x: Current concept state [batch_size, concept_dim]
            avoid_target: Target to avoid [concept_dim]
            strength: Guidance strength
            
        Returns:
            Guidance gradient [batch_size, concept_dim]
        """
        # Negative similarity guidance
        return -self.similarity_guidance(x, avoid_target, strength)
    
    def causal_guidance(self, x: torch.Tensor, cause: torch.Tensor, 
                       effect: torch.Tensor, strength: float = 1.0) -> torch.Tensor:
        """
        Compute causal relationship guidance gradient.
        
        Args:
            x: Current concept state [batch_size, concept_dim]
            cause: Cause concept [concept_dim]
            effect: Effect concept [concept_dim]
            strength: Guidance strength
            
        Returns:
            Guidance gradient [batch_size, concept_dim]
        """
        # Causal guidance encourages the relation (effect - cause) to be preserved
        if cause.dim() == 1:
            cause = cause.unsqueeze(0).expand_as(x)
        if effect.dim() == 1:
            effect = effect.unsqueeze(0).expand_as(x)
            
        # The desired relation vector
        relation_vector = effect - cause
        
        # Current relation from x
        current_relation = x - cause
        
        # Guidance towards the desired relation
        relation_diff = relation_vector - current_relation
        
        return strength * relation_diff
    
    def temporal_guidance(self, x: torch.Tensor, temporal_sequence: List[torch.Tensor],
                         position: float, strength: float = 1.0) -> torch.Tensor:
        """
        Compute temporal sequence guidance gradient.
        
        Args:
            x: Current concept state [batch_size, concept_dim]
            temporal_sequence: List of concept states representing temporal sequence
            position: Position in sequence [0, 1]
            strength: Guidance strength
            
        Returns:
            Guidance gradient [batch_size, concept_dim]
        """
        if len(temporal_sequence) < 2:
            return torch.zeros_like(x)
        
        # Interpolate target position in temporal sequence
        sequence_length = len(temporal_sequence)
        continuous_pos = position * (sequence_length - 1)
        
        lower_idx = int(continuous_pos)
        upper_idx = min(lower_idx + 1, sequence_length - 1)
        alpha = continuous_pos - lower_idx
        
        # Linear interpolation between adjacent sequence elements
        lower_concept = temporal_sequence[lower_idx]
        upper_concept = temporal_sequence[upper_idx]
        
        if lower_concept.dim() == 1:
            lower_concept = lower_concept.unsqueeze(0).expand_as(x)
        if upper_concept.dim() == 1:
            upper_concept = upper_concept.unsqueeze(0).expand_as(x)
            
        target_concept = (1 - alpha) * lower_concept + alpha * upper_concept
        
        # Guidance towards interpolated target
        return strength * (target_concept - x)
    
    def multi_constraint_guidance(self, x: torch.Tensor, constraints: List[ReasoningConstraint]) -> torch.Tensor:
        """
        Combine multiple guidance constraints with adaptive weighting.
        
        Args:
            x: Current concept state [batch_size, concept_dim]
            constraints: List of reasoning constraints
            
        Returns:
            Combined guidance gradient [batch_size, concept_dim]
        """
        total_guidance = torch.zeros_like(x)
        
        for constraint in constraints:
            if constraint.relation_type == 'similarity':
                guidance = self.similarity_guidance(x, constraint.target_vector, constraint.strength)
            elif constraint.relation_type == 'dissimilarity':
                guidance = self.dissimilarity_guidance(x, constraint.target_vector, constraint.strength)
            elif constraint.relation_type == 'causal':
                # For causal constraints, target_vector should contain [cause, effect]
                if constraint.target_vector.shape[0] >= 2:
                    cause = constraint.target_vector[0]
                    effect = constraint.target_vector[1]
                    guidance = self.causal_guidance(x, cause, effect, constraint.strength)
                else:
                    guidance = torch.zeros_like(x)
            else:
                guidance = torch.zeros_like(x)
            
            total_guidance += guidance
        
        # Normalize combined guidance
        guidance_norm = torch.norm(total_guidance, dim=-1, keepdim=True) + 1e-8
        total_guidance = total_guidance / guidance_norm
        
        # Store guidance history for analysis
        self.guidance_history.append({
            'guidance_magnitude': torch.mean(guidance_norm).item(),
            'num_constraints': len(constraints)
        })
        
        return total_guidance


class ReasoningQualityMetrics:
    """
    Comprehensive metrics for evaluating reasoning quality in diffusion-based reasoning.
    """
    
    def __init__(self, device: torch.device = None):
        self.device = device or torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    def coherence_metric(self, reasoning_path: List[torch.Tensor]) -> float:
        """
        Compute coherence of a reasoning path as smoothness of transitions.
        
        Args:
            reasoning_path: List of concept states in the reasoning trajectory
            
        Returns:
            Coherence score [0, 1], higher is better
        """
        if len(reasoning_path) < 2:
            return 1.0
        
        # Compute step-wise similarities
        similarities = []
        for i in range(len(reasoning_path) - 1):
            current = F.normalize(reasoning_path[i], dim=-1)
            next_step = F.normalize(reasoning_path[i + 1], dim=-1)
            similarity = torch.sum(current * next_step, dim=-1).mean().item()
            similarities.append(similarity)
        
        # Coherence is the average similarity between consecutive steps
        coherence = np.mean(similarities)
        return max(0.0, min(1.0, coherence))
    
    def directness_metric(self, reasoning_path: List[torch.Tensor], 
                         start: torch.Tensor, goal: torch.Tensor) -> float:
        """
        Compute directness of reasoning path towards the goal.
        
        Args:
            reasoning_path: List of concept states in the reasoning trajectory
            start: Starting concept state
            goal: Goal concept state
            
        Returns:
            Directness score [0, 1], higher is better
        """
        if len(reasoning_path) == 0:
            return 0.0
        
        # Compute direct path distance
        direct_distance = torch.norm(goal - start).item()
        
        if direct_distance < 1e-6:
            return 1.0
        
        # Compute actual path length
        actual_distance = 0.0
        current = start
        for step in reasoning_path:
            actual_distance += torch.norm(step - current).item()
            current = step
        
        # Add final step to goal
        actual_distance += torch.norm(goal - current).item()
        
        # Directness is the ratio of direct to actual distance
        directness = direct_distance / (actual_distance + 1e-6)
        return min(1.0, directness)
    
    def novelty_metric(self, reasoning_path: List[torch.Tensor], 
                      reference_concepts: torch.Tensor) -> float:
        """
        Compute novelty of reasoning path relative to reference concepts.
        
        Args:
            reasoning_path: List of concept states in the reasoning trajectory
            reference_concepts: Tensor of reference concepts [num_refs, concept_dim]
            
        Returns:
            Novelty score [0, 1], higher is more novel
        """
        if len(reasoning_path) == 0 or reference_concepts.shape[0] == 0:
            return 0.0
        
        novelty_scores = []
        for step in reasoning_path:
            # Compute minimum distance to any reference concept
            step_normalized = F.normalize(step, dim=-1)
            ref_normalized = F.normalize(reference_concepts, dim=-1)
            
            similarities = torch.mm(step_normalized.unsqueeze(0), ref_normalized.T)
            max_similarity = torch.max(similarities).item()
            
            # Novelty is inverse of maximum similarity
            novelty = 1.0 - max_similarity
            novelty_scores.append(max(0.0, novelty))
        
        return np.mean(novelty_scores)
    
    def consistency_metric(self, reasoning_path: List[torch.Tensor],
                          constraints: List[ReasoningConstraint]) -> float:
        """
        Compute consistency of reasoning path with given constraints.
        
        Args:
            reasoning_path: List of concept states in the reasoning trajectory
            constraints: List of reasoning constraints to check
            
        Returns:
            Consistency score [0, 1], higher is more consistent
        """
        if len(reasoning_path) == 0 or len(constraints) == 0:
            return 1.0
        
        consistency_scores = []
        
        for step in reasoning_path:
            step_consistency = []
            
            for constraint in constraints:
                if constraint.relation_type == 'similarity':
                    step_norm = F.normalize(step, dim=-1)
                    target_norm = F.normalize(constraint.target_vector, dim=-1)
                    similarity = torch.sum(step_norm * target_norm, dim=-1).mean().item()
                    
                    # Consistency is how well the similarity matches the constraint strength
                    expected_similarity = constraint.strength
                    consistency = 1.0 - abs(similarity - expected_similarity)
                    
                elif constraint.relation_type == 'dissimilarity':
                    step_norm = F.normalize(step, dim=-1)
                    target_norm = F.normalize(constraint.target_vector, dim=-1)
                    similarity = torch.sum(step_norm * target_norm, dim=-1).mean().item()
                    
                    # For dissimilarity, we want low similarity
                    expected_dissimilarity = 1.0 - constraint.strength
                    consistency = 1.0 - abs(similarity - expected_dissimilarity)
                
                else:
                    consistency = 1.0  # Neutral for unknown constraint types
                
                step_consistency.append(max(0.0, min(1.0, consistency)))
            
            consistency_scores.append(np.mean(step_consistency))
        
        return np.mean(consistency_scores)
    
    def information_gain_metric(self, reasoning_path: List[torch.Tensor]) -> float:
        """
        Compute information gain along the reasoning path using differential entropy estimation.
        
        Args:
            reasoning_path: List of concept states in the reasoning trajectory
            
        Returns:
            Information gain score [0, 1], higher indicates more information gain
        """
        if len(reasoning_path) < 2:
            return 0.0
        
        # Convert reasoning path to numpy for entropy calculation
        path_array = torch.stack(reasoning_path).detach().cpu().numpy()
        
        # Compute pairwise distances for each step
        initial_distances = pdist(path_array[:len(path_array)//2])
        final_distances = pdist(path_array[len(path_array)//2:])
        
        # Information gain approximated by change in distance distribution entropy
        initial_entropy = entropy(np.histogram(initial_distances, bins=20)[0] + 1e-8)
        final_entropy = entropy(np.histogram(final_distances, bins=20)[0] + 1e-8)
        
        # Normalize information gain
        max_entropy = np.log(20)  # Maximum possible entropy for 20 bins
        normalized_gain = (final_entropy - initial_entropy) / (2 * max_entropy)
        
        return max(0.0, min(1.0, 0.5 + normalized_gain))


class AdvancedReverseDiffusionReasoning:
    """
    Advanced implementation of reverse diffusion reasoning with comprehensive
    guidance mechanisms, uncertainty quantification, and quality metrics.
    """
    
    def __init__(self, diffusion_model, thought_space, device: torch.device = None):
        self.diffusion_model = diffusion_model
        self.thought_space = thought_space
        self.device = device or torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # Initialize guidance and metrics
        self.guidance_function = DiffusionGuidanceFunction(device=self.device)
        self.quality_metrics = ReasoningQualityMetrics(device=self.device)
        
        # Reasoning parameters
        self.default_num_steps = 20
        self.default_guidance_strength = 0.5
        self.adaptive_guidance = True
        self.uncertainty_threshold = 0.1
        
        # History tracking
        self.reasoning_history = []
        
    def goal_directed_reasoning(self, start_state: torch.Tensor, goal_state: torch.Tensor,
                              num_steps: int = None, guidance_strength: float = None) -> List[torch.Tensor]:
        """
        Perform goal-directed reasoning from start to goal state using guided reverse diffusion.
        
        Mathematical formulation:
        μ_θ(z_t,t,y) = μ_θ(z_t,t) + γ_t∇_{z_t}log p(y|z_t)
        
        Args:
            start_state: Starting concept state [concept_dim]
            goal_state: Goal concept state [concept_dim]
            num_steps: Number of reasoning steps
            guidance_strength: Strength of goal guidance
            
        Returns:
            List of concept states forming the reasoning trajectory
        """
        num_steps = num_steps or self.default_num_steps
        guidance_strength = guidance_strength or self.default_guidance_strength
        
        # Ensure states are on correct device and normalized
        start_state = F.normalize(start_state.to(self.device), dim=-1)
        goal_state = F.normalize(goal_state.to(self.device), dim=-1)
        
        # Initialize trajectory with start state
        reasoning_trajectory = [start_state.clone()]
        current_state = start_state.clone().unsqueeze(0)  # Add batch dimension
        
        # Determine diffusion timesteps for reverse process
        total_timesteps = self.diffusion_model.num_timesteps
        timestep_schedule = torch.linspace(
            total_timesteps // 2, 1, num_steps, dtype=torch.long, device=self.device
        )
        
        for i, t in enumerate(timestep_schedule):
            # Standard reverse diffusion step
            denoised_state = self.diffusion_model.reverse_diffusion_step(current_state, t.item())
            
            # Apply goal guidance
            guidance_gradient = self.guidance_function.similarity_guidance(
                current_state, goal_state, strength=guidance_strength
            )
            
            # Adaptive guidance strength based on distance to goal
            current_distance = torch.norm(current_state - goal_state.unsqueeze(0), dim=-1)
            adaptive_strength = guidance_strength * (1.0 + current_distance.item())
            
            # Apply guided update
            guided_state = denoised_state + adaptive_strength * guidance_gradient
            
            # Normalize to maintain unit sphere constraint
            guided_state = F.normalize(guided_state, dim=-1)
            
            # Store reasoning step
            reasoning_trajectory.append(guided_state.squeeze(0).clone())
            current_state = guided_state
        
        # Store reasoning session in history
        self.reasoning_history.append({
            'type': 'goal_directed',
            'start_state': start_state.cpu(),
            'goal_state': goal_state.cpu(),
            'trajectory': [step.cpu() for step in reasoning_trajectory],
            'num_steps': num_steps,
            'guidance_strength': guidance_strength
        })
        
        return reasoning_trajectory
    
    def constrained_reasoning(self, start_state: torch.Tensor, goal_state: torch.Tensor,
                            constraints: List[ReasoningConstraint], num_steps: int = None) -> List[torch.Tensor]:
        """
        Perform constrained reasoning with multiple simultaneous constraints.
        
        Mathematical formulation:
        μ_θ(z_t,t,{y_i}) = μ_θ(z_t,t) + Σᵢγ_{t,i}∇_{z_t}log p(y_i|z_t)
        
        Args:
            start_state: Starting concept state [concept_dim]
            goal_state: Goal concept state [concept_dim]
            constraints: List of reasoning constraints
            num_steps: Number of reasoning steps
            
        Returns:
            List of concept states forming the constrained reasoning trajectory
        """
        num_steps = num_steps or self.default_num_steps
        
        # Ensure states are on correct device and normalized
        start_state = F.normalize(start_state.to(self.device), dim=-1)
        goal_state = F.normalize(goal_state.to(self.device), dim=-1)
        
        # Initialize trajectory
        reasoning_trajectory = [start_state.clone()]
        current_state = start_state.clone().unsqueeze(0)
        
        # Timestep schedule
        total_timesteps = self.diffusion_model.num_timesteps
        timestep_schedule = torch.linspace(
            total_timesteps // 2, 1, num_steps, dtype=torch.long, device=self.device
        )
        
        for i, t in enumerate(timestep_schedule):
            # Standard reverse diffusion step
            denoised_state = self.diffusion_model.reverse_diffusion_step(current_state, t.item())
            
            # Apply goal guidance
            goal_guidance = self.guidance_function.similarity_guidance(
                current_state, goal_state, strength=self.default_guidance_strength
            )
            
            # Apply constraint guidance
            constraint_guidance = self.guidance_function.multi_constraint_guidance(
                current_state, constraints
            )
            
            # Combine guidances with adaptive weighting
            progress_ratio = i / num_steps
            goal_weight = 0.5 + 0.5 * progress_ratio  # Increase goal importance over time
            constraint_weight = 1.0 - goal_weight
            
            combined_guidance = (goal_weight * goal_guidance + 
                               constraint_weight * constraint_guidance)
            
            # Apply guided update
            guided_state = denoised_state + combined_guidance
            guided_state = F.normalize(guided_state, dim=-1)
            
            # Store reasoning step
            reasoning_trajectory.append(guided_state.squeeze(0).clone())
            current_state = guided_state
        
        # Evaluate constraint satisfaction
        final_consistency = self.quality_metrics.consistency_metric(
            reasoning_trajectory, constraints
        )
        
        # Store reasoning session
        self.reasoning_history.append({
            'type': 'constrained',
            'start_state': start_state.cpu(),
            'goal_state': goal_state.cpu(),
            'constraints': constraints,
            'trajectory': [step.cpu() for step in reasoning_trajectory],
            'consistency_score': final_consistency
        })
        
        return reasoning_trajectory
    
    def multi_goal_reasoning(self, start_state: torch.Tensor, goal_states: List[torch.Tensor],
                           goal_weights: torch.Tensor, num_steps: int = None) -> List[torch.Tensor]:
        """
        Perform multi-objective reasoning towards multiple goals with specified weights.
        
        Args:
            start_state: Starting concept state [concept_dim]
            goal_states: List of goal concept states
            goal_weights: Weights for each goal [num_goals]
            num_steps: Number of reasoning steps
            
        Returns:
            List of concept states forming the multi-goal reasoning trajectory
        """
        num_steps = num_steps or self.default_num_steps
        
        # Ensure states are on correct device and normalized
        start_state = F.normalize(start_state.to(self.device), dim=-1)
        goal_states = [F.normalize(goal.to(self.device), dim=-1) for goal in goal_states]
        goal_weights = F.softmax(goal_weights.to(self.device), dim=0)
        
        # Initialize trajectory
        reasoning_trajectory = [start_state.clone()]
        current_state = start_state.clone().unsqueeze(0)
        
        # Timestep schedule
        total_timesteps = self.diffusion_model.num_timesteps
        timestep_schedule = torch.linspace(
            total_timesteps // 2, 1, num_steps, dtype=torch.long, device=self.device
        )
        
        for i, t in enumerate(timestep_schedule):
            # Standard reverse diffusion step
            denoised_state = self.diffusion_model.reverse_diffusion_step(current_state, t.item())
            
            # Compute weighted goal guidance
            total_guidance = torch.zeros_like(current_state)
            
            for goal, weight in zip(goal_states, goal_weights):
                goal_guidance = self.guidance_function.similarity_guidance(
                    current_state, goal, strength=weight.item()
                )
                total_guidance += goal_guidance
            
            # Apply combined guidance
            guided_state = denoised_state + total_guidance
            guided_state = F.normalize(guided_state, dim=-1)
            
            # Store reasoning step
            reasoning_trajectory.append(guided_state.squeeze(0).clone())
            current_state = guided_state
        
        # Evaluate goal achievement
        final_state = reasoning_trajectory[-1]
        goal_distances = [
            torch.norm(final_state - goal).item() for goal in goal_states
        ]
        
        # Store reasoning session
        self.reasoning_history.append({
            'type': 'multi_goal',
            'start_state': start_state.cpu(),
            'goal_states': [goal.cpu() for goal in goal_states],
            'goal_weights': goal_weights.cpu(),
            'trajectory': [step.cpu() for step in reasoning_trajectory],
            'final_goal_distances': goal_distances
        })
        
        return reasoning_trajectory
    
    def uncertainty_aware_reasoning(self, start_state: torch.Tensor, goal_state: torch.Tensor,
                                  uncertainty_model, num_steps: int = None) -> Tuple[List[torch.Tensor], List[float]]:
        """
        Perform uncertainty-aware reasoning with dynamic adaptation based on confidence.
        
        Args:
            start_state: Starting concept state [concept_dim]
            goal_state: Goal concept state [concept_dim]
            uncertainty_model: Model for uncertainty estimation
            num_steps: Number of reasoning steps
            
        Returns:
            Tuple of (reasoning trajectory, uncertainty trajectory)
        """
        num_steps = num_steps or self.default_num_steps
        
        # Ensure states are on correct device and normalized
        start_state = F.normalize(start_state.to(self.device), dim=-1)
        goal_state = F.normalize(goal_state.to(self.device), dim=-1)
        
        # Initialize trajectories
        reasoning_trajectory = [start_state.clone()]
        uncertainty_trajectory = []
        current_state = start_state.clone().unsqueeze(0)
        
        # Timestep schedule
        total_timesteps = self.diffusion_model.num_timesteps
        timestep_schedule = torch.linspace(
            total_timesteps // 2, 1, num_steps, dtype=torch.long, device=self.device
        )
        
        for i, t in enumerate(timestep_schedule):
            # Estimate uncertainty for current state
            with torch.no_grad():
                if hasattr(uncertainty_model, 'get_uncertainty'):
                    # Use belief-based uncertainty if available
                    current_belief = (current_state.squeeze(0), torch.eye(current_state.shape[-1]) * 0.1)
                    uncertainty = uncertainty_model.get_uncertainty(current_belief).item()
                else:
                    # Simple distance-based uncertainty
                    goal_distance = torch.norm(current_state - goal_state.unsqueeze(0), dim=-1)
                    uncertainty = torch.sigmoid(goal_distance - 1.0).item()
            
            uncertainty_trajectory.append(uncertainty)
            
            # Adaptive guidance based on uncertainty
            if uncertainty > self.uncertainty_threshold:
                # High uncertainty: increase exploration
                guidance_strength = self.default_guidance_strength * 0.5
                exploration_noise = 0.1 * torch.randn_like(current_state)
            else:
                # Low uncertainty: increase exploitation
                guidance_strength = self.default_guidance_strength * 1.5
                exploration_noise = 0.01 * torch.randn_like(current_state)
            
            # Standard reverse diffusion step
            denoised_state = self.diffusion_model.reverse_diffusion_step(current_state, t.item())
            
            # Apply uncertainty-aware guidance
            goal_guidance = self.guidance_function.similarity_guidance(
                current_state, goal_state, strength=guidance_strength
            )
            
            # Apply guided update with exploration noise
            guided_state = denoised_state + goal_guidance + exploration_noise
            guided_state = F.normalize(guided_state, dim=-1)
            
            # Store reasoning step
            reasoning_trajectory.append(guided_state.squeeze(0).clone())
            current_state = guided_state
        
        # Store reasoning session
        self.reasoning_history.append({
            'type': 'uncertainty_aware',
            'start_state': start_state.cpu(),
            'goal_state': goal_state.cpu(),
            'trajectory': [step.cpu() for step in reasoning_trajectory],
            'uncertainty_trajectory': uncertainty_trajectory,
            'avg_uncertainty': np.mean(uncertainty_trajectory)
        })
        
        return reasoning_trajectory, uncertainty_trajectory
    
    def collaborative_reasoning(self, agents: List['AdvancedReverseDiffusionReasoning'],
                              start_state: torch.Tensor, goal_state: torch.Tensor,
                              num_steps: int = None) -> List[torch.Tensor]:
        """
        Perform collaborative reasoning with multiple agents.
        
        Args:
            agents: List of reasoning agents (including self)
            start_state: Starting concept state [concept_dim]
            goal_state: Goal concept state [concept_dim]
            num_steps: Number of reasoning steps
            
        Returns:
            Collaborative reasoning trajectory
        """
        num_steps = num_steps or self.default_num_steps
        
        # Generate individual reasoning trajectories
        individual_trajectories = []
        for agent in agents:
            trajectory = agent.goal_directed_reasoning(
                start_state, goal_state, num_steps=num_steps
            )
            individual_trajectories.append(trajectory)
        
        # Combine trajectories through consensus
        collaborative_trajectory = []
        
        for step_idx in range(min(len(traj) for traj in individual_trajectories)):
            # Gather all agent states at this step
            agent_states = torch.stack([
                traj[step_idx] for traj in individual_trajectories
            ])
            
            # Compute consensus through weighted averaging
            # Weight by inverse distance to goal
            goal_distances = torch.norm(
                agent_states - goal_state.unsqueeze(0), dim=-1
            )
            weights = F.softmax(-goal_distances, dim=0)
            
            # Consensus state
            consensus_state = torch.sum(
                agent_states * weights.unsqueeze(-1), dim=0
            )
            consensus_state = F.normalize(consensus_state, dim=-1)
            
            collaborative_trajectory.append(consensus_state)
        
        # Store collaborative session
        self.reasoning_history.append({
            'type': 'collaborative',
            'num_agents': len(agents),
            'start_state': start_state.cpu(),
            'goal_state': goal_state.cpu(),
            'trajectory': [step.cpu() for step in collaborative_trajectory],
            'individual_trajectories': [
                [step.cpu() for step in traj] for traj in individual_trajectories
            ]
        })
        
        return collaborative_trajectory
    
    def evaluate_reasoning_quality(self, reasoning_path: List[torch.Tensor],
                                 metric: str = 'comprehensive') -> Union[float, Dict[str, float]]:
        """
        Evaluate the quality of a reasoning path using various metrics.
        
        Args:
            reasoning_path: List of concept states in the reasoning trajectory
            metric: Type of metric ('coherence', 'directness', 'novelty', 'comprehensive')
            
        Returns:
            Quality score(s)
        """
        if metric == 'coherence':
            return self.quality_metrics.coherence_metric(reasoning_path)
        elif metric == 'directness':
            if len(self.reasoning_history) > 0:
                last_session = self.reasoning_history[-1]
                start = last_session['start_state']
                goal = last_session['goal_state']
                return self.quality_metrics.directness_metric(reasoning_path, start, goal)
            else:
                return 0.0
        elif metric == 'comprehensive':
            # Compute all available metrics
            coherence = self.quality_metrics.coherence_metric(reasoning_path)
            info_gain = self.quality_metrics.information_gain_metric(reasoning_path)
            
            metrics = {
                'coherence': coherence,
                'information_gain': info_gain
            }
            
            # Add context-dependent metrics if available
            if len(self.reasoning_history) > 0:
                last_session = self.reasoning_history[-1]
                start = last_session['start_state']
                goal = last_session['goal_state']
                metrics['directness'] = self.quality_metrics.directness_metric(
                    reasoning_path, start, goal
                )
                
                if 'constraints' in last_session:
                    metrics['consistency'] = self.quality_metrics.consistency_metric(
                        reasoning_path, last_session['constraints']
                    )
            
            return metrics
        else:
            raise ValueError(f"Unknown metric: {metric}")


class TestAdvancedReverseDiffusionReasoning(unittest.TestCase):
    """
    Comprehensive test suite for Advanced Reverse Diffusion Reasoning.
    Tests all mathematical formulations and practical implementations.
    """
    
    @classmethod
    def setUpClass(cls):
        """Set up class-level test fixtures."""
        cls.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        cls.concept_dim = 128
        cls.batch_size = 4
        cls.num_timesteps = 100  # Reduced for faster testing
        
        logger.info(f"Running tests on device: {cls.device}")
        logger.info(f"Concept dimension: {cls.concept_dim}")
    
    def setUp(self):
        """Set up test fixtures for each test method."""
        # Initialize core components
        self.diffusion = ConceptualDiffusion(
            concept_dim=self.concept_dim,
            num_timesteps=self.num_timesteps,
            beta_schedule='linear',
            device=self.device
        )
        
        self.thought_space = ThoughtLatentSpace(
            dimension=self.concept_dim,
            num_levels=3,
            num_clusters=10,
            device=self.device
        )
        
        self.uncertainty_model = BayesianUncertaintyQuantification(
            thought_space_dim=self.concept_dim,
            device=self.device
        )
        
        # Initialize advanced reasoning system
        self.reasoning_system = AdvancedReverseDiffusionReasoning(
            diffusion_model=self.diffusion,
            thought_space=self.thought_space,
            device=self.device
        )
        
        # Create test states
        self.start_state = F.normalize(
            torch.randn(self.concept_dim, device=self.device), dim=0
        )
        self.goal_state = F.normalize(
            torch.randn(self.concept_dim, device=self.device), dim=0
        )
        
        # Ensure they are different
        while torch.dot(self.start_state, self.goal_state) > 0.9:
            self.goal_state = F.normalize(
                torch.randn(self.concept_dim, device=self.device), dim=0
            )
    
    def test_guidance_function_similarity(self):
        """Test similarity-based guidance function."""
        guidance_fn = DiffusionGuidanceFunction(device=self.device)
        
        # Test with batch of states
        x = torch.randn(self.batch_size, self.concept_dim, device=self.device)
        target = torch.randn(self.concept_dim, device=self.device)
        
        # Compute guidance
        guidance = guidance_fn.similarity_guidance(x, target, strength=1.0)
        
        # Check shape
        self.assertEqual(guidance.shape, x.shape)
        
        # Test that guidance points towards target
        # After applying guidance, similarity should increase
        updated_x = x + 0.1 * guidance
        updated_x = F.normalize(updated_x, dim=-1)
        
        original_sim = torch.mean(F.cosine_similarity(
            F.normalize(x, dim=-1), 
            F.normalize(target.unsqueeze(0).expand_as(x), dim=-1), 
            dim=-1
        ))
        
        updated_sim = torch.mean(F.cosine_similarity(
            updated_x,
            F.normalize(target.unsqueeze(0).expand_as(x), dim=-1),
            dim=-1
        ))
        
        self.assertGreater(updated_sim.item(), original_sim.item())
    
    def test_guidance_function_dissimilarity(self):
        """Test dissimilarity-based guidance function."""
        guidance_fn = DiffusionGuidanceFunction(device=self.device)
        
        x = torch.randn(self.batch_size, self.concept_dim, device=self.device)
        avoid_target = torch.randn(self.concept_dim, device=self.device)
        
        # Compute dissimilarity guidance
        guidance = guidance_fn.dissimilarity_guidance(x, avoid_target, strength=1.0)
        
        # Check shape
        self.assertEqual(guidance.shape, x.shape)
        
        # Test that guidance points away from target
        updated_x = x + 0.1 * guidance
        updated_x = F.normalize(updated_x, dim=-1)
        
        original_sim = torch.mean(F.cosine_similarity(
            F.normalize(x, dim=-1),
            F.normalize(avoid_target.unsqueeze(0).expand_as(x), dim=-1),
            dim=-1
        ))
        
        updated_sim = torch.mean(F.cosine_similarity(
            updated_x,
            F.normalize(avoid_target.unsqueeze(0).expand_as(x), dim=-1),
            dim=-1
        ))
        
        self.assertLess(updated_sim.item(), original_sim.item())
    
    def test_guidance_function_causal(self):
        """Test causal relationship guidance function."""
        guidance_fn = DiffusionGuidanceFunction(device=self.device)
        
        x = torch.randn(self.batch_size, self.concept_dim, device=self.device)
        cause = torch.randn(self.concept_dim, device=self.device)
        effect = torch.randn(self.concept_dim, device=self.device)
        
        # Compute causal guidance
        guidance = guidance_fn.causal_guidance(x, cause, effect, strength=1.0)
        
        # Check shape
        self.assertEqual(guidance.shape, x.shape)
        
        # Test that guidance promotes the causal relation
        # The guidance should align x with the causal relation vector
        expected_relation = effect - cause
        updated_x = x + 0.1 * guidance
        actual_relation = updated_x - cause.unsqueeze(0).expand_as(updated_x)
        
        # Compute alignment with expected relation
        alignment = torch.mean(F.cosine_similarity(
            F.normalize(actual_relation, dim=-1),
            F.normalize(expected_relation.unsqueeze(0).expand_as(actual_relation), dim=-1),
            dim=-1
        ))
        
        # Guidance should improve alignment
        self.assertGreater(alignment.item(), 0.0)
    
    def test_guidance_function_temporal(self):
        """Test temporal sequence guidance function."""
        guidance_fn = DiffusionGuidanceFunction(device=self.device)
        
        # Create a temporal sequence
        sequence_length = 5
        temporal_sequence = [
            F.normalize(torch.randn(self.concept_dim, device=self.device), dim=0)
            for _ in range(sequence_length)
        ]
        
        x = torch.randn(self.batch_size, self.concept_dim, device=self.device)
        position = 0.5  # Middle of sequence
        
        # Compute temporal guidance
        guidance = guidance_fn.temporal_guidance(
            x, temporal_sequence, position, strength=1.0
        )
        
        # Check shape
        self.assertEqual(guidance.shape, x.shape)
        
        # Test that guidance points towards interpolated position
        # At position 0.5, should be between elements 2 and 3
        expected_target = 0.5 * temporal_sequence[2] + 0.5 * temporal_sequence[3]
        
        updated_x = x + 0.1 * guidance
        similarity_improvement = torch.mean(F.cosine_similarity(
            F.normalize(updated_x, dim=-1),
            F.normalize(expected_target.unsqueeze(0).expand_as(updated_x), dim=-1),
            dim=-1
        )) - torch.mean(F.cosine_similarity(
            F.normalize(x, dim=-1),
            F.normalize(expected_target.unsqueeze(0).expand_as(x), dim=-1),
            dim=-1
        ))
        
        self.assertGreater(similarity_improvement.item(), 0.0)
    
    def test_multi_constraint_guidance(self):
        """Test multi-constraint guidance combination."""
        guidance_fn = DiffusionGuidanceFunction(device=self.device)
        
        # Create multiple constraints
        constraints = [
            ReasoningConstraint(
                concept_id="similar_1",
                relation_type="similarity",
                strength=0.8,
                target_vector=F.normalize(torch.randn(self.concept_dim, device=self.device), dim=0)
            ),
            ReasoningConstraint(
                concept_id="dissimilar_1",
                relation_type="dissimilarity",
                strength=0.6,
                target_vector=F.normalize(torch.randn(self.concept_dim, device=self.device), dim=0)
            )
        ]
        
        x = torch.randn(self.batch_size, self.concept_dim, device=self.device)
        
        # Compute multi-constraint guidance
        guidance = guidance_fn.multi_constraint_guidance(x, constraints)
        
        # Check shape
        self.assertEqual(guidance.shape, x.shape)
        
        # Check that guidance is normalized
        guidance_norms = torch.norm(guidance, dim=-1)
        torch.testing.assert_close(
            guidance_norms, 
            torch.ones_like(guidance_norms),
            rtol=1e-3, atol=1e-3
        )
        
        # Check guidance history
        self.assertEqual(len(guidance_fn.guidance_history), 1)
        self.assertEqual(guidance_fn.guidance_history[0]['num_constraints'], 2)
    
    def test_quality_metrics_coherence(self):
        """Test coherence metric calculation."""
        metrics = ReasoningQualityMetrics(device=self.device)
        
        # Create a coherent path (smooth transitions)
        coherent_path = []
        current = F.normalize(torch.randn(self.concept_dim, device=self.device), dim=0)
        coherent_path.append(current.clone())
        
        for _ in range(5):
            # Small random step
            next_step = current + 0.1 * torch.randn(self.concept_dim, device=self.device)
            next_step = F.normalize(next_step, dim=0)
            coherent_path.append(next_step)
            current = next_step
        
        # Create an incoherent path (random jumps)
        incoherent_path = [
            F.normalize(torch.randn(self.concept_dim, device=self.device), dim=0)
            for _ in range(6)
        ]
        
        # Test coherence scores
        coherent_score = metrics.coherence_metric(coherent_path)
        incoherent_score = metrics.coherence_metric(incoherent_path)
        
        # Coherent path should have higher score
        self.assertGreater(coherent_score, incoherent_score)
        self.assertTrue(0.0 <= coherent_score <= 1.0)
        self.assertTrue(0.0 <= incoherent_score <= 1.0)
    
    def test_quality_metrics_directness(self):
        """Test directness metric calculation."""
        metrics = ReasoningQualityMetrics(device=self.device)
        
        start = F.normalize(torch.randn(self.concept_dim, device=self.device), dim=0)
        goal = F.normalize(torch.randn(self.concept_dim, device=self.device), dim=0)
        
        # Create a direct path (straight line)
        direct_path = []
        for i in range(5):
            alpha = i / 4.0
            interpolated = (1 - alpha) * start + alpha * goal
            interpolated = F.normalize(interpolated, dim=0)
            direct_path.append(interpolated)
        
        # Create an indirect path (detour)
        indirect_path = []
        detour_point = F.normalize(torch.randn(self.concept_dim, device=self.device), dim=0)
        
        # Go to detour point first
        for i in range(3):
            alpha = i / 2.0
            interpolated = (1 - alpha) * start + alpha * detour_point
            interpolated = F.normalize(interpolated, dim=0)
            indirect_path.append(interpolated)
        
        # Then go to goal
        for i in range(1, 3):
            alpha = i / 2.0
            interpolated = (1 - alpha) * detour_point + alpha * goal
            interpolated = F.normalize(interpolated, dim=0)
            indirect_path.append(interpolated)
        
        # Test directness scores
        direct_score = metrics.directness_metric(direct_path, start, goal)
        indirect_score = metrics.directness_metric(indirect_path, start, goal)
        
        # Direct path should have higher score
        self.assertGreater(direct_score, indirect_score)
        self.assertTrue(0.0 <= direct_score <= 1.0)
        self.assertTrue(0.0 <= indirect_score <= 1.0)
    
    def test_quality_metrics_novelty(self):
        """Test novelty metric calculation."""
        metrics = ReasoningQualityMetrics(device=self.device)
        
        # Create reference concepts
        num_refs = 10
        reference_concepts = torch.stack([
            F.normalize(torch.randn(self.concept_dim, device=self.device), dim=0)
            for _ in range(num_refs)
        ])
        
        # Create a novel path (far from references)
        novel_path = []
        for _ in range(5):
            # Generate concept orthogonal to all references
            concept = torch.randn(self.concept_dim, device=self.device)
            for ref in reference_concepts:
                concept = concept - torch.dot(concept, ref) * ref
            concept = F.normalize(concept, dim=0)
            novel_path.append(concept)
        
        # Create a non-novel path (similar to references)
        non_novel_path = []
        for i in range(5):
            # Use reference concepts with small perturbations
            ref_idx = i % num_refs
            concept = reference_concepts[ref_idx] + 0.01 * torch.randn(self.concept_dim, device=self.device)
            concept = F.normalize(concept, dim=0)
            non_novel_path.append(concept)
        
        # Test novelty scores
        novel_score = metrics.novelty_metric(novel_path, reference_concepts)
        non_novel_score = metrics.novelty_metric(non_novel_path, reference_concepts)
        
        # Novel path should have higher score
        self.assertGreater(novel_score, non_novel_score)
        self.assertTrue(0.0 <= novel_score <= 1.0)
        self.assertTrue(0.0 <= non_novel_score <= 1.0)
    
    def test_quality_metrics_consistency(self):
        """Test consistency metric calculation."""
        metrics = ReasoningQualityMetrics(device=self.device)
        
        # Create constraints
        similar_target = F.normalize(torch.randn(self.concept_dim, device=self.device), dim=0)
        constraints = [
            ReasoningConstraint(
                concept_id="test_similar",
                relation_type="similarity",
                strength=0.8,
                target_vector=similar_target
            )
        ]
        
        # Create a consistent path (high similarity to target)
        consistent_path = []
        for _ in range(5):
            concept = 0.9 * similar_target + 0.1 * torch.randn(self.concept_dim, device=self.device)
            concept = F.normalize(concept, dim=0)
            consistent_path.append(concept)
        
        # Create an inconsistent path (low similarity to target)
        inconsistent_path = []
        for _ in range(5):
            concept = F.normalize(torch.randn(self.concept_dim, device=self.device), dim=0)
            # Ensure it's different from target
            while torch.dot(concept, similar_target) > 0.2:
                concept = F.normalize(torch.randn(self.concept_dim, device=self.device), dim=0)
            inconsistent_path.append(concept)
        
        # Test consistency scores
        consistent_score = metrics.consistency_metric(consistent_path, constraints)
        inconsistent_score = metrics.consistency_metric(inconsistent_path, constraints)
        
        # Consistent path should have higher score
        self.assertGreater(consistent_score, inconsistent_score)
        self.assertTrue(0.0 <= consistent_score <= 1.0)
        self.assertTrue(0.0 <= inconsistent_score <= 1.0)
    
    def test_quality_metrics_information_gain(self):
        """Test information gain metric calculation."""
        metrics = ReasoningQualityMetrics(device=self.device)
        
        # Create a path with increasing diversity (high information gain)
        diverse_path = []
        for i in range(10):
            # Generate increasingly diverse concepts
            concept = torch.randn(self.concept_dim, device=self.device)
            concept = concept * (1 + i * 0.2)  # Increasing variance
            concept = F.normalize(concept, dim=0)
            diverse_path.append(concept)
        
        # Create a path with constant similarity (low information gain)
        uniform_path = []
        base_concept = F.normalize(torch.randn(self.concept_dim, device=self.device), dim=0)
        for _ in range(10):
            concept = base_concept + 0.01 * torch.randn(self.concept_dim, device=self.device)
            concept = F.normalize(concept, dim=0)
            uniform_path.append(concept)
        
        # Test information gain scores
        diverse_score = metrics.information_gain_metric(diverse_path)
        uniform_score = metrics.information_gain_metric(uniform_path)
        
        # Diverse path should have higher information gain
        self.assertGreater(diverse_score, uniform_score)
        self.assertTrue(0.0 <= diverse_score <= 1.0)
        self.assertTrue(0.0 <= uniform_score <= 1.0)
    
    def test_goal_directed_reasoning(self):
        """Test basic goal-directed reasoning functionality."""
        num_steps = 10
        
        # Perform goal-directed reasoning
        trajectory = self.reasoning_system.goal_directed_reasoning(
            self.start_state, self.goal_state, num_steps=num_steps
        )
        
        # Check trajectory properties
        self.assertEqual(len(trajectory), num_steps + 1)  # Including start state
        
        # Check that all states are normalized
        for state in trajectory:
            self.assertEqual(state.shape, (self.concept_dim,))
            self.assertAlmostEqual(torch.norm(state).item(), 1.0, delta=1e-5)
        
        # Check that trajectory moves towards goal
        start_distance = torch.norm(trajectory[0] - self.goal_state).item()
        end_distance = torch.norm(trajectory[-1] - self.goal_state).item()
        self.assertLess(end_distance, start_distance)
        
        # Check reasoning history
        self.assertEqual(len(self.reasoning_system.reasoning_history), 1)
        history_entry = self.reasoning_system.reasoning_history[0]
        self.assertEqual(history_entry['type'], 'goal_directed')
        self.assertEqual(len(history_entry['trajectory']), num_steps + 1)
    
    def test_constrained_reasoning(self):
        """Test constrained reasoning with multiple constraints."""
        # Create constraints
        similar_target = F.normalize(torch.randn(self.concept_dim, device=self.device), dim=0)
        avoid_target = F.normalize(torch.randn(self.concept_dim, device=self.device), dim=0)
        
        constraints = [
            ReasoningConstraint(
                concept_id="similar",
                relation_type="similarity",
                strength=0.7,
                target_vector=similar_target
            ),
            ReasoningConstraint(
                concept_id="avoid",
                relation_type="dissimilarity",
                strength=0.8,
                target_vector=avoid_target
            )
        ]
        
        num_steps = 8
        
        # Perform constrained reasoning
        trajectory = self.reasoning_system.constrained_reasoning(
            self.start_state, self.goal_state, constraints, num_steps=num_steps
        )
        
        # Check trajectory properties
        self.assertEqual(len(trajectory), num_steps + 1)
        
        # Check constraint satisfaction in final state
        final_state = trajectory[-1]
        
        # Should be similar to similar_target
        similarity_score = torch.dot(
            F.normalize(final_state, dim=0),
            F.normalize(similar_target, dim=0)
        ).item()
        self.assertGreater(similarity_score, 0.3)
        
        # Should be dissimilar to avoid_target
        dissimilarity_score = 1.0 - torch.dot(
            F.normalize(final_state, dim=0),
            F.normalize(avoid_target, dim=0)
        ).item()
        self.assertGreater(dissimilarity_score, 0.3)
        
        # Check reasoning history
        self.assertEqual(len(self.reasoning_system.reasoning_history), 1)
        history_entry = self.reasoning_system.reasoning_history[0]
        self.assertEqual(history_entry['type'], 'constrained')
        self.assertIn('consistency_score', history_entry)
    
    def test_multi_goal_reasoning(self):
        """Test multi-objective reasoning towards multiple goals."""
        # Create multiple goals
        num_goals = 3
        goal_states = [
            F.normalize(torch.randn(self.concept_dim, device=self.device), dim=0)
            for _ in range(num_goals)
        ]
        
        # Assign different weights (first goal should be prioritized)
        goal_weights = torch.tensor([0.6, 0.3, 0.1], device=self.device)
        
        num_steps = 10
        
        # Perform multi-goal reasoning
        trajectory = self.reasoning_system.multi_goal_reasoning(
            self.start_state, goal_states, goal_weights, num_steps=num_steps
        )
        
        # Check trajectory properties
        self.assertEqual(len(trajectory), num_steps + 1)
        
        # Check that final state is closest to highest-weighted goal
        final_state = trajectory[-1]
        distances = [
            torch.norm(final_state - goal).item() for goal in goal_states
        ]
        closest_goal_idx = np.argmin(distances)
        self.assertEqual(closest_goal_idx, 0)  # Highest weighted goal
        
        # Check reasoning history
        self.assertEqual(len(self.reasoning_system.reasoning_history), 1)
        history_entry = self.reasoning_system.reasoning_history[0]
        self.assertEqual(history_entry['type'], 'multi_goal')
        self.assertIn('final_goal_distances', history_entry)
    
    def test_uncertainty_aware_reasoning(self):
        """Test uncertainty-aware reasoning with adaptive behavior."""
        num_steps = 8
        
        # Perform uncertainty-aware reasoning
        trajectory, uncertainty_trajectory = self.reasoning_system.uncertainty_aware_reasoning(
            self.start_state, self.goal_state, self.uncertainty_model, num_steps=num_steps
        )
        
        # Check trajectory properties
        self.assertEqual(len(trajectory), num_steps + 1)
        self.assertEqual(len(uncertainty_trajectory), num_steps)
        
        # Check uncertainty values are in reasonable range
        for uncertainty in uncertainty_trajectory:
            self.assertTrue(0.0 <= uncertainty <= 1.0)
        
        # Check reasoning history
        self.assertEqual(len(self.reasoning_system.reasoning_history), 1)
        history_entry = self.reasoning_system.reasoning_history[0]
        self.assertEqual(history_entry['type'], 'uncertainty_aware')
        self.assertIn('uncertainty_trajectory', history_entry)
        self.assertIn('avg_uncertainty', history_entry)
    
    def test_collaborative_reasoning(self):
        """Test collaborative reasoning with multiple agents."""
        # Create additional reasoning agents
        num_agents = 3
        agents = [self.reasoning_system]
        
        for _ in range(num_agents - 1):
            agent = AdvancedReverseDiffusionReasoning(
                diffusion_model=self.diffusion,
                thought_space=self.thought_space,
                device=self.device
            )
            agents.append(agent)
        
        num_steps = 6
        
        # Perform collaborative reasoning
        collaborative_trajectory = self.reasoning_system.collaborative_reasoning(
            agents, self.start_state, self.goal_state, num_steps=num_steps
        )
        
        # Check trajectory properties
        self.assertTrue(len(collaborative_trajectory) > 0)
        
        # Check that collaborative result is different from individual results
        individual_trajectory = self.reasoning_system.goal_directed_reasoning(
            self.start_state, self.goal_state, num_steps=num_steps
        )
        
        # Compare final states
        collaborative_final = collaborative_trajectory[-1]
        individual_final = individual_trajectory[-1]
        
        difference = torch.norm(collaborative_final - individual_final).item()
        self.assertGreater(difference, 0.01)  # Should be noticeably different
        
        # Check reasoning history
        # Note: collaborative_reasoning calls goal_directed_reasoning for each agent,
        # so we'll have multiple history entries
        self.assertGreater(len(self.reasoning_system.reasoning_history), 1)
        
        # Find the collaborative entry
        collaborative_entry = None
        for entry in self.reasoning_system.reasoning_history:
            if entry['type'] == 'collaborative':
                collaborative_entry = entry
                break
        
        self.assertIsNotNone(collaborative_entry)
        self.assertEqual(collaborative_entry['num_agents'], num_agents)
    
    def test_reasoning_quality_evaluation(self):
        """Test comprehensive reasoning quality evaluation."""
        # Generate a test trajectory
        trajectory = self.reasoning_system.goal_directed_reasoning(
            self.start_state, self.goal_state, num_steps=8
        )
        
        # Test individual metrics
        coherence = self.reasoning_system.evaluate_reasoning_quality(trajectory, metric='coherence')
        self.assertTrue(0.0 <= coherence <= 1.0)
        
        directness = self.reasoning_system.evaluate_reasoning_quality(trajectory, metric='directness')
        self.assertTrue(0.0 <= directness <= 1.0)
        
        # Test comprehensive evaluation
        comprehensive_metrics = self.reasoning_system.evaluate_reasoning_quality(
            trajectory, metric='comprehensive'
        )
        
        self.assertIsInstance(comprehensive_metrics, dict)
        self.assertIn('coherence', comprehensive_metrics)
        self.assertIn('information_gain', comprehensive_metrics)
        self.assertIn('directness', comprehensive_metrics)
        
        # All metrics should be in [0, 1]
        for metric_name, metric_value in comprehensive_metrics.items():
            self.assertTrue(0.0 <= metric_value <= 1.0, 
                          f"Metric {metric_name} = {metric_value} not in [0, 1]")
    
    def test_mathematical_formulation_consistency(self):
        """Test that the implementation follows the mathematical formulations."""
        # Test guided reverse diffusion formulation:
        # μ_θ(z_t,t,y) = μ_θ(z_t,t) + γ_t∇_{z_t}log p(y|z_t)
        
        # Create test state
        test_state = self.start_state.unsqueeze(0)  # Add batch dimension
        t = 50
        
        # Standard reverse diffusion step
        standard_step = self.diffusion.reverse_diffusion_step(test_state, t)
        
        # Guided step
        guidance_gradient = self.reasoning_system.guidance_function.similarity_guidance(
            test_state, self.goal_state, strength=0.5
        )
        guided_step = standard_step + guidance_gradient
        
        # The guided step should be different from the standard step
        difference = torch.norm(guided_step - standard_step).item()
        self.assertGreater(difference, 0.01)
        
        # Test multi-constraint formulation:
        # μ_θ(z_t,t,{y_i}) = μ_θ(z_t,t) + Σᵢγ_{t,i}∇_{z_t}log p(y_i|z_t)
        
        constraints = [
            ReasoningConstraint(
                concept_id="test1",
                relation_type="similarity",
                strength=0.5,
                target_vector=F.normalize(torch.randn(self.concept_dim, device=self.device), dim=0)
            ),
            ReasoningConstraint(
                concept_id="test2",
                relation_type="similarity",
                strength=0.3,
                target_vector=F.normalize(torch.randn(self.concept_dim, device=self.device), dim=0)
            )
        ]
        
        # Multi-constraint guidance
        multi_guidance = self.reasoning_system.guidance_function.multi_constraint_guidance(
            test_state, constraints
        )
        
        # Individual guidances
        guidance1 = self.reasoning_system.guidance_function.similarity_guidance(
            test_state, constraints[0].target_vector, strength=constraints[0].strength
        )
        guidance2 = self.reasoning_system.guidance_function.similarity_guidance(
            test_state, constraints[1].target_vector, strength=constraints[1].strength
        )
        
        # The multi-constraint guidance should incorporate both individual guidances
        # (though it's normalized, so we check direction alignment)
        combined_guidance = guidance1 + guidance2
        combined_guidance = F.normalize(combined_guidance, dim=-1)
        
        alignment = F.cosine_similarity(multi_guidance, combined_guidance, dim=-1).mean()
        self.assertGreater(alignment.item(), 0.5)  # Should be reasonably aligned
    
    def test_integration_with_thought_space(self):
        """Test integration with the thought latent space."""
        # Test that reasoning trajectories respect thought space properties
        
        # Encode concepts using thought space
        concept1 = self.thought_space.encode_concept("artificial intelligence")
        concept2 = self.thought_space.encode_concept("machine learning")
        
        # Perform reasoning between encoded concepts
        trajectory = self.reasoning_system.goal_directed_reasoning(
            concept1, concept2, num_steps=6
        )
        
        # Check that trajectory maintains semantic relationships
        # Concepts should remain in reasonable semantic space
        for state in trajectory:
            # Check normalization (thought space constraint)
            self.assertAlmostEqual(torch.norm(state).item(), 1.0, delta=1e-5)
            
            # Check that state is semantically reasonable
            # (similarity to either start or goal should be > 0)
            sim_to_start = torch.dot(F.normalize(state, dim=0), F.normalize(concept1, dim=0))
            sim_to_goal = torch.dot(F.normalize(state, dim=0), F.normalize(concept2, dim=0))
            
            self.assertTrue(sim_to_start.item() > -0.5 or sim_to_goal.item() > -0.5)
    
    def test_performance_and_scalability(self):
        """Test performance characteristics and scalability."""
        import time
        
        # Test with different trajectory lengths
        step_counts = [5, 10, 20]
        times = []
        
        for num_steps in step_counts:
            start_time = time.time()
            
            trajectory = self.reasoning_system.goal_directed_reasoning(
                self.start_state, self.goal_state, num_steps=num_steps
            )
            
            end_time = time.time()
            times.append(end_time - start_time)
            
            # Check that longer trajectories take more time but scale reasonably
            self.assertEqual(len(trajectory), num_steps + 1)
        
        # Time should increase with number of steps, but not too dramatically
        self.assertGreater(times[1], times[0])
        self.assertGreater(times[2], times[1])
        
        # Test memory usage is reasonable
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        memory_before = process.memory_info().rss / 1024 / 1024  # MB
        
        # Run multiple reasoning sessions
        for _ in range(5):
            trajectory = self.reasoning_system.goal_directed_reasoning(
                F.normalize(torch.randn(self.concept_dim, device=self.device), dim=0),
                F.normalize(torch.randn(self.concept_dim, device=self.device), dim=0),
                num_steps=10
            )
        
        memory_after = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = memory_after - memory_before
        
        # Memory increase should be reasonable (< 100 MB for test)
        self.assertLess(memory_increase, 100)
    
    def test_edge_cases_and_robustness(self):
        """Test edge cases and robustness of the implementation."""
        
        # Test with identical start and goal states
        identical_trajectory = self.reasoning_system.goal_directed_reasoning(
            self.start_state, self.start_state, num_steps=5
        )
        
        # Should still produce a trajectory
        self.assertEqual(len(identical_trajectory), 6)
        
        # Final state should be very close to start state
        final_distance = torch.norm(identical_trajectory[-1] - self.start_state).item()
        self.assertLess(final_distance, 0.1)
        
        # Test with zero-length trajectory
        zero_trajectory = self.reasoning_system.goal_directed_reasoning(
            self.start_state, self.goal_state, num_steps=0
        )
        self.assertEqual(len(zero_trajectory), 1)  # Just the start state
        
        # Test with very small concept dimension
        small_dim = 4
        small_start = F.normalize(torch.randn(small_dim, device=self.device), dim=0)
        small_goal = F.normalize(torch.randn(small_dim, device=self.device), dim=0)
        
        # Create temporary reasoning system for small dimension
        small_diffusion = ConceptualDiffusion(
            concept_dim=small_dim,
            num_timesteps=50,
            device=self.device
        )
        small_thought_space = ThoughtLatentSpace(
            dimension=small_dim,
            num_levels=2,
            num_clusters=3,
            device=self.device
        )
        small_reasoning = AdvancedReverseDiffusionReasoning(
            diffusion_model=small_diffusion,
            thought_space=small_thought_space,
            device=self.device
        )
        
        small_trajectory = small_reasoning.goal_directed_reasoning(
            small_start, small_goal, num_steps=3
        )
        
        self.assertEqual(len(small_trajectory), 4)
        for state in small_trajectory:
            self.assertEqual(state.shape, (small_dim,))
    
    def tearDown(self):
        """Clean up after each test method."""
        # Clear CUDA cache if using GPU
        if self.device.type == 'cuda':
            torch.cuda.empty_cache()
        
        # Clear reasoning history
        self.reasoning_system.reasoning_history.clear()
    
    @classmethod
    def tearDownClass(cls):
        """Clean up after all tests."""
        logger.info("All Advanced Reverse Diffusion Reasoning tests completed")


if __name__ == '__main__':
    # Configure test runner
    unittest.main(
        verbosity=2,
        testRunner=unittest.TextTestRunner(
            stream=sys.stdout,
            descriptions=True,
            verbosity=2
        )
    )