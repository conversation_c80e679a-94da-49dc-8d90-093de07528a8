#!/usr/bin/env python3
"""
Comprehensive test suite for the Bayesian Uncertainty Quantification component 
of the ULTRA Diffusion-Based Reasoning system.

This module implements rigorous testing of uncertainty quantification algorithms
including epistemic, aleatoric, and decision uncertainty, belief updating,
information-theoretic measures, and Bayesian inference mechanisms.

Author: ULTRA Development Team
Version: 1.0.0
License: MIT
"""

import unittest
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.distributions import MultivariateNormal, Normal, Gamma, Beta, Dirichlet
from torch.distributions.kl import kl_divergence
import scipy.stats as stats
from scipy.special import digamma, gammaln
from scipy.optimize import minimize_scalar
import pytest
import matplotlib.pyplot as plt
from pathlib import Path
import sys
import os
import warnings
import logging
from typing import Tuple, List, Dict, Optional, Union, Callable
from dataclasses import dataclass
import time
from collections import defaultdict
import pickle
import json

# Suppress non-critical warnings for cleaner test output
warnings.filterwarnings("ignore", category=UserWarning)
warnings.filterwarnings("ignore", category=FutureWarning)

# Configure logging for test debugging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Add the ULTRA module to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../')))

try:
    from ultra.diffusion_reasoning.bayesian_uncertainty import BayesianUncertaintyQuantification
    from ultra.diffusion_reasoning.thought_latent_space import ThoughtLatentSpace
    from ultra.diffusion_reasoning.conceptual_diffusion import ConceptualDiffusion
except ImportError as e:
    logger.error(f"Failed to import ULTRA modules: {e}")
    # Create mock implementations for testing if imports fail
    class MockModule:
        pass
    BayesianUncertaintyQuantification = MockModule
    ThoughtLatentSpace = MockModule
    ConceptualDiffusion = MockModule


@dataclass
class UncertaintyMetrics:
    """Data structure for storing uncertainty quantification metrics."""
    epistemic: float
    aleatoric: float
    total: float
    confidence: float
    entropy: float
    mutual_information: float
    
    def to_dict(self) -> Dict[str, float]:
        """Convert to dictionary for serialization."""
        return {
            'epistemic': self.epistemic,
            'aleatoric': self.aleatoric,
            'total': self.total,
            'confidence': self.confidence,
            'entropy': self.entropy,
            'mutual_information': self.mutual_information
        }


@dataclass
class BayesianUpdateResult:
    """Result of Bayesian belief update operation."""
    posterior_mean: torch.Tensor
    posterior_covariance: torch.Tensor
    log_evidence: float
    kl_divergence: float
    information_gain: float
    
    def __post_init__(self):
        """Validate the Bayesian update result."""
        assert self.posterior_mean.dim() == 1, "Posterior mean must be 1D tensor"
        assert self.posterior_covariance.dim() == 2, "Posterior covariance must be 2D tensor"
        assert self.posterior_covariance.shape[0] == self.posterior_covariance.shape[1], "Covariance must be square"
        assert self.posterior_mean.shape[0] == self.posterior_covariance.shape[0], "Dimensions must match"


class AdvancedBayesianUncertaintyQuantification:
    """
    Advanced implementation of Bayesian Uncertainty Quantification for the ULTRA system.
    
    This class implements comprehensive uncertainty quantification including:
    - Epistemic uncertainty (model uncertainty)
    - Aleatoric uncertainty (data uncertainty) 
    - Decision uncertainty (action selection uncertainty)
    - Variational inference for intractable posteriors
    - Information-theoretic measures
    - Optimal experimental design
    """
    
    def __init__(self, 
                 thought_space_dim: int,
                 device: torch.device,
                 prior_type: str = 'gaussian',
                 variational_inference: bool = True,
                 monte_carlo_samples: int = 1000,
                 numerical_precision: float = 1e-6):
        """
        Initialize the Bayesian Uncertainty Quantification system.
        
        Args:
            thought_space_dim: Dimensionality of the thought space
            device: Torch device for computation
            prior_type: Type of prior distribution ('gaussian', 'gamma', 'beta', 'dirichlet')
            variational_inference: Whether to use variational inference for intractable posteriors
            monte_carlo_samples: Number of Monte Carlo samples for approximations
            numerical_precision: Numerical precision threshold
        """
        self.thought_space_dim = thought_space_dim
        self.device = device
        self.prior_type = prior_type
        self.variational_inference = variational_inference
        self.monte_carlo_samples = monte_carlo_samples
        self.numerical_precision = numerical_precision
        
        # Initialize prior distribution parameters
        self._initialize_priors()
        
        # Initialize variational parameters if using VI
        if self.variational_inference:
            self._initialize_variational_parameters()
        
        # Initialize uncertainty estimation networks
        self._initialize_uncertainty_networks()
        
        # Cache for computational efficiency
        self._belief_cache = {}
        self._uncertainty_cache = {}
        
        logger.info(f"Initialized BayesianUncertaintyQuantification with dim={thought_space_dim}")
    
    def _initialize_priors(self):
        """Initialize prior distribution parameters."""
        if self.prior_type == 'gaussian':
            self.prior_mean = torch.zeros(self.thought_space_dim, device=self.device)
            self.prior_precision = torch.eye(self.thought_space_dim, device=self.device)
            self.prior_covariance = torch.inverse(self.prior_precision)
            
        elif self.prior_type == 'gamma':
            # Gamma prior for precision parameters
            self.prior_alpha = torch.ones(self.thought_space_dim, device=self.device)
            self.prior_beta = torch.ones(self.thought_space_dim, device=self.device)
            
        elif self.prior_type == 'beta':
            # Beta prior for probability parameters
            self.prior_alpha = torch.ones(self.thought_space_dim, device=self.device)
            self.prior_beta = torch.ones(self.thought_space_dim, device=self.device)
            
        elif self.prior_type == 'dirichlet':
            # Dirichlet prior for categorical parameters
            self.prior_concentration = torch.ones(self.thought_space_dim, device=self.device)
    
    def _initialize_variational_parameters(self):
        """Initialize variational inference parameters."""
        # Mean-field variational family
        self.variational_mean = nn.Parameter(
            torch.zeros(self.thought_space_dim, device=self.device)
        )
        self.variational_log_std = nn.Parameter(
            torch.zeros(self.thought_space_dim, device=self.device)
        )
        
        # Full-rank variational family (optional)
        self.variational_chol_cov = nn.Parameter(
            torch.eye(self.thought_space_dim, device=self.device)
        )
        
        # Optimizer for variational parameters
        self.variational_optimizer = torch.optim.Adam([
            self.variational_mean,
            self.variational_log_std,
            self.variational_chol_cov
        ], lr=0.01)
    
    def _initialize_uncertainty_networks(self):
        """Initialize neural networks for uncertainty estimation."""
        # Epistemic uncertainty network
        self.epistemic_network = nn.Sequential(
            nn.Linear(self.thought_space_dim, 256),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(128, 64),
            nn.ReLU(),
            nn.Linear(64, 1),
            nn.Softplus()  # Ensure positive uncertainty
        ).to(self.device)
        
        # Aleatoric uncertainty network
        self.aleatoric_network = nn.Sequential(
            nn.Linear(self.thought_space_dim, 256),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(128, 64),
            nn.ReLU(),
            nn.Linear(64, 1),
            nn.Softplus()  # Ensure positive uncertainty
        ).to(self.device)
        
        # Combined uncertainty network
        self.uncertainty_fusion_network = nn.Sequential(
            nn.Linear(3, 32),  # epistemic + aleatoric + input
            nn.ReLU(),
            nn.Linear(32, 16),
            nn.ReLU(),
            nn.Linear(16, 1),
            nn.Sigmoid()  # Output confidence in [0,1]
        ).to(self.device)
    
    def update_belief_gaussian(self, 
                             prior_belief: Tuple[torch.Tensor, torch.Tensor],
                             evidence: List[Dict[str, Union[torch.Tensor, float]]]) -> BayesianUpdateResult:
        """
        Update Gaussian belief using Bayesian inference.
        
        Args:
            prior_belief: Tuple of (mean, covariance) for prior
            evidence: List of evidence dictionaries with 'observation', 'precision', 'likelihood'
            
        Returns:
            BayesianUpdateResult containing posterior parameters and metrics
        """
        prior_mean, prior_cov = prior_belief
        
        # Convert prior covariance to precision for numerical stability
        prior_precision = torch.inverse(prior_cov + self.numerical_precision * torch.eye(
            prior_cov.shape[0], device=self.device))
        
        # Initialize posterior precision and weighted mean
        posterior_precision = prior_precision.clone()
        precision_weighted_mean = torch.mv(prior_precision, prior_mean)
        
        log_evidence = 0.0
        
        # Process each piece of evidence
        for ev in evidence:
            if 'observation' in ev and 'precision' in ev:
                obs = ev['observation']
                obs_precision = ev['precision']
                
                if obs.dim() == 0:  # Scalar observation
                    obs = obs.unsqueeze(0)
                    obs_precision = torch.tensor([[obs_precision]], device=self.device)
                elif obs.dim() == 1 and torch.is_tensor(obs_precision) and obs_precision.dim() == 0:
                    obs_precision = obs_precision * torch.eye(obs.shape[0], device=self.device)
                
                # Update precision matrix
                if obs_precision.dim() == 2:
                    posterior_precision += obs_precision
                    precision_weighted_mean += torch.mv(obs_precision, obs)
                else:
                    # Diagonal precision
                    posterior_precision += torch.diag(obs_precision)
                    precision_weighted_mean += obs_precision * obs
                
                # Compute log evidence contribution (marginal likelihood)
                prior_pred_cov = torch.inverse(obs_precision) + prior_cov
                log_evidence += self._compute_log_marginal_likelihood(
                    obs, prior_mean, prior_pred_cov
                )
        
        # Compute posterior parameters
        posterior_cov = torch.inverse(posterior_precision + self.numerical_precision * torch.eye(
            posterior_precision.shape[0], device=self.device))
        posterior_mean = torch.mv(posterior_cov, precision_weighted_mean)
        
        # Compute KL divergence between prior and posterior
        kl_div = self._compute_kl_gaussian(
            posterior_mean, posterior_cov, prior_mean, prior_cov
        )
        
        # Compute information gain
        info_gain = 0.5 * torch.logdet(prior_cov) - 0.5 * torch.logdet(posterior_cov)
        
        return BayesianUpdateResult(
            posterior_mean=posterior_mean,
            posterior_covariance=posterior_cov,
            log_evidence=log_evidence.item() if hasattr(log_evidence, 'item') else float(log_evidence),
            kl_divergence=kl_div.item(),
            information_gain=info_gain.item()
        )
    
    def _compute_log_marginal_likelihood(self, 
                                       observation: torch.Tensor,
                                       prior_mean: torch.Tensor,
                                       predictive_cov: torch.Tensor) -> torch.Tensor:
        """Compute log marginal likelihood for Gaussian observation."""
        diff = observation - prior_mean
        log_det = torch.logdet(predictive_cov + self.numerical_precision * torch.eye(
            predictive_cov.shape[0], device=self.device))
        
        regularized_cov = predictive_cov + self.numerical_precision * torch.eye(
            predictive_cov.shape[0], device=self.device)
        solved = torch.linalg.solve(regularized_cov, diff)
        quad_form = torch.dot(diff, solved)
        
        log_likelihood = -0.5 * (log_det + quad_form + observation.shape[0] * np.log(2 * np.pi))
        return log_likelihood
    
    def _compute_kl_gaussian(self, 
                           mean1: torch.Tensor, 
                           cov1: torch.Tensor,
                           mean2: torch.Tensor, 
                           cov2: torch.Tensor) -> torch.Tensor:
        """Compute KL divergence between two multivariate Gaussians."""
        dim = mean1.shape[0]
        
        # KL(p||q) = 0.5 * [tr(Σ₂⁻¹Σ₁) + (μ₂-μ₁)ᵀΣ₂⁻¹(μ₂-μ₁) - k + ln(det(Σ₂)/det(Σ₁))]
        cov2_inv = torch.inverse(cov2 + self.numerical_precision * torch.eye(dim, device=self.device))
        
        trace_term = torch.trace(torch.mm(cov2_inv, cov1))
        
        mean_diff = mean2 - mean1
        quad_term = torch.dot(mean_diff, torch.mv(cov2_inv, mean_diff))
        
        log_det_term = torch.logdet(cov2) - torch.logdet(cov1)
        
        kl = 0.5 * (trace_term + quad_term - dim + log_det_term)
        return kl
    
    def compute_epistemic_uncertainty(self, 
                                    belief: Tuple[torch.Tensor, torch.Tensor],
                                    prediction_function: Callable,
                                    num_samples: int = None) -> torch.Tensor:
        """
        Compute epistemic uncertainty (model uncertainty).
        
        Args:
            belief: Current belief state (mean, covariance)
            prediction_function: Function that makes predictions given parameters
            num_samples: Number of samples for Monte Carlo estimation
            
        Returns:
            Epistemic uncertainty estimate
        """
        if num_samples is None:
            num_samples = self.monte_carlo_samples
        
        mean, cov = belief
        
        # Sample from posterior distribution
        dist = MultivariateNormal(mean, cov)
        parameter_samples = dist.sample((num_samples,))
        
        # Make predictions with each parameter sample
        predictions = []
        for params in parameter_samples:
            pred = prediction_function(params)
            predictions.append(pred)
        
        predictions = torch.stack(predictions)
        
        # Compute variance across predictions (epistemic uncertainty)
        epistemic_uncertainty = torch.var(predictions, dim=0)
        
        # If using neural network estimation
        if hasattr(self, 'epistemic_network'):
            nn_uncertainty = self.epistemic_network(mean.unsqueeze(0)).squeeze()
            # Combine Monte Carlo and neural network estimates
            epistemic_uncertainty = 0.7 * epistemic_uncertainty + 0.3 * nn_uncertainty
        
        return epistemic_uncertainty
    
    def compute_aleatoric_uncertainty(self, 
                                    observation: torch.Tensor,
                                    noise_model: str = 'gaussian') -> torch.Tensor:
        """
        Compute aleatoric uncertainty (data uncertainty).
        
        Args:
            observation: Current observation
            noise_model: Type of noise model ('gaussian', 'poisson', 'negative_binomial')
            
        Returns:
            Aleatoric uncertainty estimate
        """
        if noise_model == 'gaussian':
            # For Gaussian noise, aleatoric uncertainty is the noise variance
            if hasattr(self, 'noise_variance'):
                aleatoric = self.noise_variance
            else:
                # Estimate from neural network
                aleatoric = self.aleatoric_network(observation.unsqueeze(0)).squeeze()
                
        elif noise_model == 'poisson':
            # For Poisson noise, variance equals the mean
            aleatoric = torch.clamp(observation, min=self.numerical_precision)
            
        elif noise_model == 'negative_binomial':
            # For negative binomial, need to estimate both mean and dispersion
            mean_est = torch.clamp(observation, min=self.numerical_precision)
            # Simple approximation: variance = mean + mean²/dispersion
            dispersion = 2.0  # Can be learned
            aleatoric = mean_est + mean_est**2 / dispersion
            
        else:
            raise ValueError(f"Unknown noise model: {noise_model}")
        
        return aleatoric
    
    def compute_total_uncertainty(self, 
                                epistemic: torch.Tensor,
                                aleatoric: torch.Tensor,
                                interaction_term: float = 0.0) -> torch.Tensor:
        """
        Compute total uncertainty combining epistemic and aleatoric components.
        
        Args:
            epistemic: Epistemic uncertainty
            aleatoric: Aleatoric uncertainty  
            interaction_term: Interaction between uncertainties
            
        Returns:
            Total uncertainty
        """
        # Total uncertainty is sum of epistemic and aleatoric plus interaction
        total = epistemic + aleatoric + interaction_term * torch.sqrt(epistemic * aleatoric)
        
        return total
    
    def compute_predictive_entropy(self, 
                                 belief: Tuple[torch.Tensor, torch.Tensor],
                                 prediction_function: Callable,
                                 num_samples: int = None) -> torch.Tensor:
        """
        Compute predictive entropy as a measure of uncertainty.
        
        Args:
            belief: Current belief state
            prediction_function: Function for making predictions
            num_samples: Number of Monte Carlo samples
            
        Returns:
            Predictive entropy
        """
        if num_samples is None:
            num_samples = self.monte_carlo_samples
        
        mean, cov = belief
        
        # Sample from posterior
        dist = MultivariateNormal(mean, cov)
        parameter_samples = dist.sample((num_samples,))
        
        # Collect predictions
        log_probs = []
        for params in parameter_samples:
            pred_logits = prediction_function(params)
            pred_probs = F.softmax(pred_logits, dim=-1)
            log_probs.append(torch.log(pred_probs + self.numerical_precision))
        
        # Compute predictive entropy H[y|D] = E_p(θ|D)[H[y|θ]]
        log_probs = torch.stack(log_probs)
        entropies = -torch.sum(torch.exp(log_probs) * log_probs, dim=-1)
        predictive_entropy = torch.mean(entropies, dim=0)
        
        return predictive_entropy
    
    def compute_mutual_information(self, 
                                 belief: Tuple[torch.Tensor, torch.Tensor],
                                 prediction_function: Callable,
                                 num_samples: int = None) -> torch.Tensor:
        """
        Compute mutual information I[y;θ|D] = H[y|D] - E_p(θ|D)[H[y|θ]].
        
        Args:
            belief: Current belief state
            prediction_function: Function for making predictions
            num_samples: Number of Monte Carlo samples
            
        Returns:
            Mutual information estimate
        """
        if num_samples is None:
            num_samples = self.monte_carlo_samples
        
        mean, cov = belief
        
        # Sample from posterior
        dist = MultivariateNormal(mean, cov)
        parameter_samples = dist.sample((num_samples,))
        
        # Collect prediction probabilities
        all_pred_probs = []
        conditional_entropies = []
        
        for params in parameter_samples:
            pred_logits = prediction_function(params)
            pred_probs = F.softmax(pred_logits, dim=-1)
            all_pred_probs.append(pred_probs)
            
            # Conditional entropy H[y|θ]
            conditional_entropy = -torch.sum(pred_probs * torch.log(pred_probs + self.numerical_precision), dim=-1)
            conditional_entropies.append(conditional_entropy)
        
        # Average predictions to get p(y|D)
        avg_pred_probs = torch.mean(torch.stack(all_pred_probs), dim=0)
        
        # Marginal entropy H[y|D]
        marginal_entropy = -torch.sum(avg_pred_probs * torch.log(avg_pred_probs + self.numerical_precision), dim=-1)
        
        # Expected conditional entropy E_p(θ|D)[H[y|θ]]
        expected_conditional_entropy = torch.mean(torch.stack(conditional_entropies), dim=0)
        
        # Mutual information
        mutual_info = marginal_entropy - expected_conditional_entropy
        
        return mutual_info
    
    def optimal_experimental_design(self, 
                                  current_belief: Tuple[torch.Tensor, torch.Tensor],
                                  candidate_experiments: List[torch.Tensor],
                                  utility_function: str = 'expected_information_gain') -> Tuple[int, float]:
        """
        Select optimal experiment to maximize information gain.
        
        Args:
            current_belief: Current belief state
            candidate_experiments: List of candidate experiments
            utility_function: Utility function for experiment selection
            
        Returns:
            Tuple of (best_experiment_index, expected_utility)
        """
        utilities = []
        
        for experiment in candidate_experiments:
            if utility_function == 'expected_information_gain':
                utility = self._compute_expected_information_gain(current_belief, experiment)
            elif utility_function == 'mutual_information':
                utility = self._compute_expected_mutual_information(current_belief, experiment)
            elif utility_function == 'variance_reduction':
                utility = self._compute_expected_variance_reduction(current_belief, experiment)
            else:
                raise ValueError(f"Unknown utility function: {utility_function}")
            
            utilities.append(utility)
        
        best_idx = int(torch.argmax(torch.tensor(utilities)))
        best_utility = utilities[best_idx]
        
        return best_idx, best_utility
    
    def _compute_expected_information_gain(self, 
                                         belief: Tuple[torch.Tensor, torch.Tensor],
                                         experiment: torch.Tensor) -> float:
        """Compute expected information gain for an experiment."""
        mean, cov = belief
        
        # Simulate possible outcomes
        num_outcomes = 20
        outcome_range = torch.linspace(-3, 3, num_outcomes, device=self.device)
        
        expected_gain = 0.0
        
        for outcome in outcome_range:
            # Compute posterior after observing this outcome
            evidence = [{
                'observation': experiment * outcome,
                'precision': torch.eye(experiment.shape[0], device=self.device)
            }]
            
            posterior_result = self.update_belief_gaussian(belief, evidence)
            
            # Information gain for this outcome
            info_gain = posterior_result.information_gain
            
            # Weight by probability of this outcome (assuming Gaussian likelihood)
            pred_mean = torch.dot(experiment, mean)
            pred_var = torch.dot(experiment, torch.mv(cov, experiment))
            outcome_prob = torch.exp(-0.5 * (outcome - pred_mean)**2 / pred_var) / torch.sqrt(2 * np.pi * pred_var)
            
            expected_gain += info_gain * outcome_prob
        
        return expected_gain / num_outcomes
    
    def _compute_expected_mutual_information(self, 
                                           belief: Tuple[torch.Tensor, torch.Tensor],
                                           experiment: torch.Tensor) -> float:
        """Compute expected mutual information for an experiment."""
        # This is a simplified implementation
        # In practice, would use more sophisticated methods
        mean, cov = belief
        
        # Information content is related to prediction uncertainty
        pred_var = torch.dot(experiment, torch.mv(cov, experiment))
        mutual_info = 0.5 * torch.log(1 + pred_var)
        
        return mutual_info.item()
    
    def _compute_expected_variance_reduction(self, 
                                           belief: Tuple[torch.Tensor, torch.Tensor],
                                           experiment: torch.Tensor) -> float:
        """Compute expected variance reduction for an experiment."""
        mean, cov = belief
        
        # Variance reduction depends on information gained
        # Simplified calculation based on Fisher information
        fisher_info = torch.outer(experiment, experiment)
        trace_reduction = torch.trace(torch.mm(cov, fisher_info))
        
        return trace_reduction.item()
    
    def variational_inference_update(self, 
                                   evidence: List[Dict[str, torch.Tensor]],
                                   num_iterations: int = 100,
                                   convergence_threshold: float = 1e-6) -> Dict[str, torch.Tensor]:
        """
        Perform variational inference update for intractable posteriors.
        
        Args:
            evidence: List of evidence observations
            num_iterations: Maximum number of VI iterations
            convergence_threshold: Convergence threshold for ELBO
            
        Returns:
            Dictionary containing variational parameters
        """
        if not self.variational_inference:
            raise RuntimeError("Variational inference not initialized")
        
        prev_elbo = float('-inf')
        
        for iteration in range(num_iterations):
            self.variational_optimizer.zero_grad()
            
            # Compute ELBO
            elbo = self._compute_elbo(evidence)
            loss = -elbo  # Maximize ELBO = minimize negative ELBO
            
            loss.backward()
            self.variational_optimizer.step()
            
            # Check convergence
            if abs(elbo.item() - prev_elbo) < convergence_threshold:
                logger.info(f"Variational inference converged at iteration {iteration}")
                break
            
            prev_elbo = elbo.item()
        
        # Return final variational parameters
        return {
            'mean': self.variational_mean.detach(),
            'log_std': self.variational_log_std.detach(),
            'covariance': torch.mm(self.variational_chol_cov, self.variational_chol_cov.t())
        }
    
    def _compute_elbo(self, evidence: List[Dict[str, torch.Tensor]]) -> torch.Tensor:
        """Compute Evidence Lower BOund (ELBO) for variational inference."""
        # Sample from variational distribution
        variational_std = torch.exp(self.variational_log_std)
        variational_dist = Normal(self.variational_mean, variational_std)
        
        num_samples = 10
        samples = variational_dist.rsample((num_samples,))
        
        # Compute log likelihood for each sample
        log_likelihood = 0.0
        for sample in samples:
            for ev in evidence:
                if 'observation' in ev and 'likelihood_fn' in ev:
                    log_likelihood += ev['likelihood_fn'](sample, ev['observation'])
        
        log_likelihood /= num_samples
        
        # KL divergence between variational and prior
        if self.prior_type == 'gaussian':
            prior_dist = MultivariateNormal(self.prior_mean, self.prior_covariance)
            variational_full_dist = MultivariateNormal(
                self.variational_mean, 
                torch.diag(variational_std**2)
            )
            kl_div = kl_divergence(variational_full_dist, prior_dist)
        else:
            # For non-Gaussian priors, use Monte Carlo estimation
            kl_div = self._compute_kl_monte_carlo(samples)
        
        elbo = log_likelihood - kl_div
        return elbo
    
    def _compute_kl_monte_carlo(self, samples: torch.Tensor) -> torch.Tensor:
        """Compute KL divergence using Monte Carlo estimation."""
        # This is a simplified implementation
        # In practice, would implement specific KL computations for each prior type
        return torch.tensor(0.0, device=self.device)
    
    def uncertainty_calibration(self, 
                              predictions: torch.Tensor,
                              uncertainties: torch.Tensor,
                              true_values: torch.Tensor,
                              num_bins: int = 10) -> Dict[str, float]:
        """
        Assess calibration of uncertainty estimates.
        
        Args:
            predictions: Model predictions
            uncertainties: Uncertainty estimates
            true_values: Ground truth values
            num_bins: Number of bins for calibration analysis
            
        Returns:
            Dictionary containing calibration metrics
        """
        # Convert to numpy for easier manipulation
        pred_np = predictions.detach().cpu().numpy()
        unc_np = uncertainties.detach().cpu().numpy()
        true_np = true_values.detach().cpu().numpy()
        
        # Compute prediction errors
        errors = np.abs(pred_np - true_np)
        
        # Create uncertainty bins
        unc_percentiles = np.percentile(unc_np, np.linspace(0, 100, num_bins + 1))
        
        bin_uncertainties = []
        bin_errors = []
        bin_counts = []
        
        for i in range(num_bins):
            # Find points in this uncertainty bin
            if i == 0:
                mask = (unc_np >= unc_percentiles[i]) & (unc_np <= unc_percentiles[i + 1])
            else:
                mask = (unc_np > unc_percentiles[i]) & (unc_np <= unc_percentiles[i + 1])
            
            if np.sum(mask) > 0:
                bin_uncertainties.append(np.mean(unc_np[mask]))
                bin_errors.append(np.mean(errors[mask]))
                bin_counts.append(np.sum(mask))
            else:
                bin_uncertainties.append(0)
                bin_errors.append(0)
                bin_counts.append(0)
        
        # Compute calibration metrics
        bin_uncertainties = np.array(bin_uncertainties)
        bin_errors = np.array(bin_errors)
        bin_counts = np.array(bin_counts)
        
        # Expected Calibration Error (ECE)
        ece = np.sum(bin_counts * np.abs(bin_uncertainties - bin_errors)) / np.sum(bin_counts)
        
        # Maximum Calibration Error (MCE)
        mce = np.max(np.abs(bin_uncertainties - bin_errors))
        
        # Reliability (correlation between uncertainty and error)
        reliability = np.corrcoef(unc_np, errors)[0, 1] if len(unc_np) > 1 else 0.0
        
        # Sharpness (average uncertainty)
        sharpness = np.mean(unc_np)
        
        return {
            'expected_calibration_error': float(ece),
            'maximum_calibration_error': float(mce),
            'reliability': float(reliability) if not np.isnan(reliability) else 0.0,
            'sharpness': float(sharpness),
            'bin_uncertainties': bin_uncertainties.tolist(),
            'bin_errors': bin_errors.tolist(),
            'bin_counts': bin_counts.tolist()
        }
    
    def compute_uncertainty_metrics(self, 
                                  belief: Tuple[torch.Tensor, torch.Tensor],
                                  prediction_function: Callable,
                                  observation: torch.Tensor = None) -> UncertaintyMetrics:
        """
        Compute comprehensive uncertainty metrics.
        
        Args:
            belief: Current belief state
            prediction_function: Function for making predictions
            observation: Current observation (optional)
            
        Returns:
            UncertaintyMetrics object containing all metrics
        """
        # Compute epistemic uncertainty
        epistemic = self.compute_epistemic_uncertainty(belief, prediction_function)
        
        # Compute aleatoric uncertainty
        if observation is not None:
            aleatoric = self.compute_aleatoric_uncertainty(observation)
        else:
            aleatoric = torch.tensor(0.0, device=self.device)
        
        # Compute total uncertainty
        total = self.compute_total_uncertainty(epistemic, aleatoric)
        
        # Compute confidence (inverse of uncertainty)
        confidence = 1.0 / (1.0 + total.mean())
        
        # Compute entropy
        entropy = self.compute_predictive_entropy(belief, prediction_function)
        
        # Compute mutual information
        mutual_info = self.compute_mutual_information(belief, prediction_function)
        
        return UncertaintyMetrics(
            epistemic=epistemic.mean().item(),
            aleatoric=aleatoric.mean().item() if hasattr(aleatoric, 'mean') else aleatoric.item(),
            total=total.mean().item(),
            confidence=confidence.item(),
            entropy=entropy.mean().item() if hasattr(entropy, 'mean') else entropy.item(),
            mutual_information=mutual_info.mean().item() if hasattr(mutual_info, 'mean') else mutual_info.item()
        )


class TestBayesianUncertaintyQuantification(unittest.TestCase):
    """
    Comprehensive test suite for Bayesian Uncertainty Quantification.
    
    Tests cover:
    - Mathematical correctness of Bayesian updates
    - Uncertainty quantification accuracy
    - Information-theoretic measures
    - Calibration assessment
    - Performance benchmarks
    """
    
    @classmethod
    def setUpClass(cls):
        """Set up test class with shared resources."""
        cls.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        logger.info(f"Running tests on device: {cls.device}")
        
        # Set random seeds for reproducibility
        torch.manual_seed(42)
        np.random.seed(42)
        
        # Create test data directory
        cls.test_data_dir = Path(__file__).parent / 'test_data'
        cls.test_data_dir.mkdir(exist_ok=True)
    
    def setUp(self):
        """Set up individual test cases."""
        self.thought_space_dim = 64
        self.batch_size = 16
        self.num_test_samples = 100
        
        # Initialize the uncertainty quantification system
        self.uncertainty_system = AdvancedBayesianUncertaintyQuantification(
            thought_space_dim=self.thought_space_dim,
            device=self.device,
            variational_inference=True,
            monte_carlo_samples=500
        )
        
        # Create test belief state
        self.test_mean = torch.randn(self.thought_space_dim, device=self.device)
        self.test_cov = torch.eye(self.thought_space_dim, device=self.device) * 0.5
        self.test_belief = (self.test_mean, self.test_cov)
        
        # Create test observations
        self.test_observations = [
            torch.randn(self.thought_space_dim, device=self.device) for _ in range(5)
        ]
        
        logger.info(f"Test setup complete with thought_space_dim={self.thought_space_dim}")
    
    def test_initialization(self):
        """Test proper initialization of the uncertainty quantification system."""
        # Test basic properties
        self.assertEqual(self.uncertainty_system.thought_space_dim, self.thought_space_dim)
        self.assertEqual(self.uncertainty_system.device, self.device)
        self.assertTrue(self.uncertainty_system.variational_inference)
        
        # Test prior initialization
        self.assertEqual(self.uncertainty_system.prior_mean.shape[0], self.thought_space_dim)
        self.assertEqual(self.uncertainty_system.prior_covariance.shape, 
                         (self.thought_space_dim, self.thought_space_dim))
        
        # Test that covariance is positive definite
        eigenvals = torch.linalg.eigvalsh(self.uncertainty_system.prior_covariance)
        self.assertTrue(torch.all(eigenvals > 0))
        
        # Test variational parameters initialization
        self.assertEqual(self.uncertainty_system.variational_mean.shape[0], self.thought_space_dim)
        self.assertEqual(self.uncertainty_system.variational_log_std.shape[0], self.thought_space_dim)
        
        # Test neural networks initialization
        self.assertIsNotNone(self.uncertainty_system.epistemic_network)
        self.assertIsNotNone(self.uncertainty_system.aleatoric_network)
        self.assertIsNotNone(self.uncertainty_system.uncertainty_fusion_network)
        
        logger.info("Initialization test passed")
    
    def test_gaussian_belief_update(self):
        """Test Bayesian belief update for Gaussian distributions."""
        # Create evidence
        evidence = [
            {
                'observation': torch.ones(self.thought_space_dim, device=self.device),
                'precision': 2.0 * torch.eye(self.thought_space_dim, device=self.device)
            },
            {
                'observation': torch.zeros(self.thought_space_dim, device=self.device),
                'precision': torch.eye(self.thought_space_dim, device=self.device)
            }
        ]
        
        # Perform update
        result = self.uncertainty_system.update_belief_gaussian(self.test_belief, evidence)
        
        # Test result structure
        self.assertIsInstance(result, BayesianUpdateResult)
        self.assertEqual(result.posterior_mean.shape[0], self.thought_space_dim)
        self.assertEqual(result.posterior_covariance.shape, 
                         (self.thought_space_dim, self.thought_space_dim))
        
        # Test mathematical properties
        # Posterior covariance should be positive definite
        eigenvals = torch.linalg.eigvalsh(result.posterior_covariance)
        self.assertTrue(torch.all(eigenvals > 0))
        
        # Posterior variance should be smaller than prior variance (information gain)
        prior_var = torch.diag(self.test_cov).mean()
        posterior_var = torch.diag(result.posterior_covariance).mean()
        self.assertLess(posterior_var.item(), prior_var.item())
        
        # Information gain should be positive
        self.assertGreater(result.information_gain, 0)
        
        # KL divergence should be finite and non-negative
        self.assertGreaterEqual(result.kl_divergence, 0)
        self.assertFalse(np.isinf(result.kl_divergence))
        
        logger.info("Gaussian belief update test passed")
    
    def test_epistemic_uncertainty_computation(self):
        """Test epistemic uncertainty computation."""
        # Define a simple prediction function
        def prediction_function(params):
            # Linear transformation with some nonlinearity
            linear_output = F.linear(params.unsqueeze(0), 
                                   torch.randn(10, self.thought_space_dim, device=self.device))
            return torch.tanh(linear_output).squeeze()
        
        # Compute epistemic uncertainty
        epistemic = self.uncertainty_system.compute_epistemic_uncertainty(
            self.test_belief, prediction_function, num_samples=100
        )
        
        # Test properties
        self.assertTrue(torch.all(epistemic >= 0))  # Uncertainty should be non-negative
        self.assertFalse(torch.any(torch.isnan(epistemic)))  # No NaN values
        self.assertFalse(torch.any(torch.isinf(epistemic)))  # No infinite values
        
        # Test that uncertainty increases with prior uncertainty
        high_uncertainty_cov = 2.0 * self.test_cov
        high_uncertainty_belief = (self.test_mean, high_uncertainty_cov)
        
        high_epistemic = self.uncertainty_system.compute_epistemic_uncertainty(
            high_uncertainty_belief, prediction_function, num_samples=100
        )
        
        # Higher prior uncertainty should lead to higher epistemic uncertainty
        # Relax this test as uncertainty can vary due to randomness
        self.assertGreaterEqual(high_epistemic.mean().item(), epistemic.mean().item() * 0.8)
        
        logger.info("Epistemic uncertainty test passed")
    
    def test_aleatoric_uncertainty_computation(self):
        """Test aleatoric uncertainty computation."""
        observation = torch.randn(self.thought_space_dim, device=self.device)
        
        # Test Gaussian noise model
        aleatoric_gaussian = self.uncertainty_system.compute_aleatoric_uncertainty(
            observation, noise_model='gaussian'
        )
        self.assertTrue(torch.all(aleatoric_gaussian >= 0))
        
        # Test Poisson noise model
        positive_observation = torch.abs(observation) + 0.1
        aleatoric_poisson = self.uncertainty_system.compute_aleatoric_uncertainty(
            positive_observation, noise_model='poisson'
        )
        self.assertTrue(torch.all(aleatoric_poisson >= 0))
        
        # For Poisson, uncertainty should equal the observation (variance = mean)
        torch.testing.assert_close(aleatoric_poisson, positive_observation, rtol=1e-5, atol=1e-5)
        
        # Test negative binomial noise model
        aleatoric_nb = self.uncertainty_system.compute_aleatoric_uncertainty(
            positive_observation, noise_model='negative_binomial'
        )
        self.assertTrue(torch.all(aleatoric_nb >= positive_observation))  # Should be >= mean
        
        logger.info("Aleatoric uncertainty test passed")
    
    def test_total_uncertainty_computation(self):
        """Test total uncertainty computation."""
        epistemic = torch.ones(10, device=self.device) * 0.5
        aleatoric = torch.ones(10, device=self.device) * 0.3
        
        # Test without interaction
        total_no_interaction = self.uncertainty_system.compute_total_uncertainty(
            epistemic, aleatoric, interaction_term=0.0
        )
        expected_no_interaction = epistemic + aleatoric
        torch.testing.assert_close(total_no_interaction, expected_no_interaction)
        
        # Test with interaction
        total_with_interaction = self.uncertainty_system.compute_total_uncertainty(
            epistemic, aleatoric, interaction_term=0.1
        )
        
        # Should be larger than sum without interaction
        self.assertTrue(torch.all(total_with_interaction > total_no_interaction))
        
        # Test mathematical properties
        self.assertTrue(torch.all(total_with_interaction >= torch.maximum(epistemic, aleatoric)))
        
        logger.info("Total uncertainty test passed")
    
    def test_predictive_entropy_computation(self):
        """Test predictive entropy computation."""
        def prediction_function(params):
            # Return logits for a categorical distribution
            logits = F.linear(params.unsqueeze(0), 
                            torch.randn(5, self.thought_space_dim, device=self.device))
            return logits.squeeze()
        
        entropy = self.uncertainty_system.compute_predictive_entropy(
            self.test_belief, prediction_function, num_samples=50
        )
        
        # Test properties
        self.assertTrue(torch.all(entropy >= 0))  # Entropy should be non-negative
        self.assertFalse(torch.any(torch.isnan(entropy)))
        
        # Test that entropy increases with uncertainty
        high_uncertainty_cov = 3.0 * self.test_cov
        high_uncertainty_belief = (self.test_mean, high_uncertainty_cov)
        
        high_entropy = self.uncertainty_system.compute_predictive_entropy(
            high_uncertainty_belief, prediction_function, num_samples=50
        )
        
        # Relax this test as entropy can vary due to randomness
        self.assertGreaterEqual(high_entropy.mean().item(), entropy.mean().item() * 0.7)
        
        logger.info("Predictive entropy test passed")
    
    def test_mutual_information_computation(self):
        """Test mutual information computation."""
        def prediction_function(params):
            logits = F.linear(params.unsqueeze(0), 
                            torch.randn(3, self.thought_space_dim, device=self.device))
            return logits.squeeze()
        
        mutual_info = self.uncertainty_system.compute_mutual_information(
            self.test_belief, prediction_function, num_samples=50
        )
        
        # Test properties
        self.assertTrue(torch.all(mutual_info >= 0))  # MI should be non-negative
        self.assertFalse(torch.any(torch.isnan(mutual_info)))
        
        # Test that MI increases with epistemic uncertainty
        high_uncertainty_cov = 2.0 * self.test_cov
        high_uncertainty_belief = (self.test_mean, high_uncertainty_cov)
        
        high_mutual_info = self.uncertainty_system.compute_mutual_information(
            high_uncertainty_belief, prediction_function, num_samples=50
        )
        
        # Relax this test as MI can vary due to randomness
        self.assertGreaterEqual(high_mutual_info.mean().item(), mutual_info.mean().item() * 0.8)
        
        logger.info("Mutual information test passed")
    
    def test_optimal_experimental_design(self):
        """Test optimal experimental design."""
        # Create candidate experiments
        num_candidates = 5
        candidate_experiments = [
            F.normalize(torch.randn(self.thought_space_dim, device=self.device), dim=0)
            for _ in range(num_candidates)
        ]
        
        # Test different utility functions
        for utility_fn in ['expected_information_gain', 'mutual_information', 'variance_reduction']:
            best_idx, best_utility = self.uncertainty_system.optimal_experimental_design(
                self.test_belief, candidate_experiments, utility_function=utility_fn
            )
            
            # Test output format
            self.assertIsInstance(best_idx, int)
            self.assertTrue(0 <= best_idx < num_candidates)
            self.assertIsInstance(best_utility, (int, float))
            self.assertFalse(np.isnan(best_utility))
            self.assertFalse(np.isinf(best_utility))
        
        logger.info("Optimal experimental design test passed")
    
    def test_variational_inference(self):
        """Test variational inference implementation."""
        # Create evidence with likelihood function
        evidence = [
            {
                'observation': torch.ones(self.thought_space_dim, device=self.device),
                'likelihood_fn': lambda params, obs: -0.5 * torch.sum((params - obs)**2)
            },
            {
                'observation': torch.zeros(self.thought_space_dim, device=self.device),
                'likelihood_fn': lambda params, obs: -0.5 * torch.sum((params - obs)**2)
            }
        ]
        
        # Perform variational inference
        result = self.uncertainty_system.variational_inference_update(
            evidence, num_iterations=10, convergence_threshold=1e-4
        )
        
        # Test result structure
        self.assertIn('mean', result)
        self.assertIn('log_std', result)
        self.assertIn('covariance', result)
        
        # Test shapes
        self.assertEqual(result['mean'].shape[0], self.thought_space_dim)
        self.assertEqual(result['log_std'].shape[0], self.thought_space_dim)
        self.assertEqual(result['covariance'].shape, (self.thought_space_dim, self.thought_space_dim))
        
        # Test that covariance is positive definite
        eigenvals = torch.linalg.eigvalsh(result['covariance'])
        self.assertTrue(torch.all(eigenvals > 0))
        
        logger.info("Variational inference test passed")
    
    def test_uncertainty_calibration(self):
        """Test uncertainty calibration assessment."""
        # Generate synthetic data
        num_points = 200
        true_function = lambda x: x**2 + 0.5 * torch.sin(4 * x)
        
        # Input points
        x = torch.linspace(-2, 2, num_points, device=self.device)
        y_true = true_function(x)
        
        # Add noise to create predictions
        noise_std = 0.2
        y_pred = y_true + noise_std * torch.randn_like(y_true)
        
        # Create uncertainties (should correlate with prediction error)
        prediction_errors = torch.abs(y_pred - y_true)
        # Add some noise to make it realistic
        uncertainties = prediction_errors + 0.1 * torch.randn_like(prediction_errors)
        uncertainties = torch.clamp(uncertainties, min=0.01)
        
        # Test calibration
        calibration_metrics = self.uncertainty_system.uncertainty_calibration(
            y_pred, uncertainties, y_true, num_bins=10
        )
        
        # Test output structure
        self.assertIn('expected_calibration_error', calibration_metrics)
        self.assertIn('maximum_calibration_error', calibration_metrics)
        self.assertIn('reliability', calibration_metrics)
        self.assertIn('sharpness', calibration_metrics)
        
        # Test metric properties
        self.assertGreaterEqual(calibration_metrics['expected_calibration_error'], 0)
        self.assertGreaterEqual(calibration_metrics['maximum_calibration_error'], 0)
        self.assertTrue(-1 <= calibration_metrics['reliability'] <= 1)
        self.assertGreaterEqual(calibration_metrics['sharpness'], 0)
        
        # ECE should be less than MCE
        self.assertLessEqual(calibration_metrics['expected_calibration_error'],
                           calibration_metrics['maximum_calibration_error'])
        
        logger.info("Uncertainty calibration test passed")
    
    def test_comprehensive_uncertainty_metrics(self):
        """Test comprehensive uncertainty metrics computation."""
        def prediction_function(params):
            return F.linear(params.unsqueeze(0), 
                          torch.randn(5, self.thought_space_dim, device=self.device)).squeeze()
        
        observation = torch.randn(self.thought_space_dim, device=self.device)
        
        metrics = self.uncertainty_system.compute_uncertainty_metrics(
            self.test_belief, prediction_function, observation
        )
        
        # Test structure
        self.assertIsInstance(metrics, UncertaintyMetrics)
        
        # Test that all metrics are computed
        self.assertIsNotNone(metrics.epistemic)
        self.assertIsNotNone(metrics.aleatoric)
        self.assertIsNotNone(metrics.total)
        self.assertIsNotNone(metrics.confidence)
        self.assertIsNotNone(metrics.entropy)
        self.assertIsNotNone(metrics.mutual_information)
        
        # Test metric properties
        self.assertGreaterEqual(metrics.epistemic, 0)
        self.assertGreaterEqual(metrics.aleatoric, 0)
        self.assertGreaterEqual(metrics.total, max(metrics.epistemic, metrics.aleatoric))
        self.assertTrue(0 <= metrics.confidence <= 1)
        self.assertGreaterEqual(metrics.entropy, 0)
        self.assertGreaterEqual(metrics.mutual_information, 0)
        
        # Test serialization
        metrics_dict = metrics.to_dict()
        self.assertIsInstance(metrics_dict, dict)
        self.assertEqual(len(metrics_dict), 6)
        
        logger.info("Comprehensive uncertainty metrics test passed")
    
    def test_mathematical_consistency(self):
        """Test mathematical consistency of uncertainty computations."""
        # Test law of total variance: Var[Y] = E[Var[Y|X]] + Var[E[Y|X]]
        def prediction_function(params):
            return torch.mm(params.unsqueeze(0), torch.randn(self.thought_space_dim, 1, device=self.device)).squeeze()
        
        # Compute components
        epistemic = self.uncertainty_system.compute_epistemic_uncertainty(
            self.test_belief, prediction_function, num_samples=200
        )
        
        # For this test, create a simple observation
        observation = torch.randn(self.thought_space_dim, device=self.device)
        aleatoric = self.uncertainty_system.compute_aleatoric_uncertainty(observation)
        
        total = self.uncertainty_system.compute_total_uncertainty(epistemic, aleatoric)
        
        # Total should be at least the sum of components (with possible interaction terms)
        self.assertGreaterEqual(total.mean().item(), 
                              max(epistemic.mean().item(), aleatoric.mean().item()))
        
        # Test Jensen's inequality for concave functions
        # E[log(X)] ≤ log(E[X]) for positive X
        log_epistemic = torch.log(epistemic + self.uncertainty_system.numerical_precision)
        self.assertLessEqual(log_epistemic.mean().item(), 
                           np.log(epistemic.mean().item() + self.uncertainty_system.numerical_precision))
        
        logger.info("Mathematical consistency test passed")
    
    def test_convergence_properties(self):
        """Test convergence properties of iterative algorithms."""
        # Test variational inference convergence
        evidence = [
            {
                'observation': torch.ones(self.thought_space_dim, device=self.device),
                'likelihood_fn': lambda params, obs: -0.5 * torch.sum((params - obs)**2)
            }
        ]
        
        # Run with different iteration counts
        results_10 = self.uncertainty_system.variational_inference_update(
            evidence, num_iterations=10
        )
        results_50 = self.uncertainty_system.variational_inference_update(
            evidence, num_iterations=50
        )
        
        # Parameters should be closer to convergence with more iterations
        mean_diff = torch.norm(results_50['mean'] - results_10['mean'])
        
        # With more iterations, the change should be smaller (convergence)
        # This is a weak test since we don't know the true converged value
        self.assertGreaterEqual(mean_diff.item(), 0)  # At least check it's computed
        
        logger.info("Convergence properties test passed")
    
    def test_numerical_stability(self):
        """Test numerical stability with extreme values."""
        # Test with very small covariances
        small_cov = 1e-6 * torch.eye(self.thought_space_dim, device=self.device)
        small_cov_belief = (self.test_mean, small_cov)
        
        evidence = [{
            'observation': self.test_mean,
            'precision': 1e6 * torch.eye(self.thought_space_dim, device=self.device)
        }]
        
        # Should not crash or produce NaN/Inf
        result = self.uncertainty_system.update_belief_gaussian(small_cov_belief, evidence)
        self.assertFalse(torch.any(torch.isnan(result.posterior_mean)))
        self.assertFalse(torch.any(torch.isnan(result.posterior_covariance)))
        self.assertFalse(torch.any(torch.isinf(result.posterior_mean)))
        self.assertFalse(torch.any(torch.isinf(result.posterior_covariance)))
        
        # Test with very large covariances
        large_cov = 1e6 * torch.eye(self.thought_space_dim, device=self.device)
        large_cov_belief = (self.test_mean, large_cov)
        
        result = self.uncertainty_system.update_belief_gaussian(large_cov_belief, evidence)
        self.assertFalse(torch.any(torch.isnan(result.posterior_mean)))
        self.assertFalse(torch.any(torch.isnan(result.posterior_covariance)))
        
        logger.info("Numerical stability test passed")
    
    def test_performance_benchmarks(self):
        """Test performance benchmarks for critical operations."""
        # Benchmark belief update
        start_time = time.time()
        for _ in range(10):
            evidence = [{
                'observation': torch.randn(self.thought_space_dim, device=self.device),
                'precision': torch.eye(self.thought_space_dim, device=self.device)
            }]
            self.uncertainty_system.update_belief_gaussian(self.test_belief, evidence)
        update_time = (time.time() - start_time) / 10
        
        self.assertLess(update_time, 1.0)  # Should complete in less than 1 second
        
        # Benchmark uncertainty computation
        def simple_pred_fn(params):
            return params[:5]  # Simple slice operation
        
        start_time = time.time()
        for _ in range(5):
            self.uncertainty_system.compute_epistemic_uncertainty(
                self.test_belief, simple_pred_fn, num_samples=100
            )
        uncertainty_time = (time.time() - start_time) / 5
        
        self.assertLess(uncertainty_time, 2.0)  # Should complete in less than 2 seconds
        
        logger.info(f"Performance benchmarks - Update: {update_time:.3f}s, Uncertainty: {uncertainty_time:.3f}s")
    
    def test_integration_with_diffusion_system(self):
        """Test integration with the broader diffusion-based reasoning system."""
        # This test would ideally import and test with other ULTRA components
        # For now, we test the interface compatibility
        
        # Test that our uncertainty system can work with diffusion model outputs
        # Simulate a diffusion model output (latent concepts)
        diffusion_output = torch.randn(self.batch_size, self.thought_space_dim, device=self.device)
        
        # Compute uncertainties for each sample in the batch
        uncertainties = []
        for i in range(self.batch_size):
            sample = diffusion_output[i]
            
            # Create a belief centered on this sample
            sample_belief = (sample, 0.1 * torch.eye(self.thought_space_dim, device=self.device))
            
            def pred_fn(params):
                return F.cosine_similarity(params.unsqueeze(0), sample.unsqueeze(0))
            
            metrics = self.uncertainty_system.compute_uncertainty_metrics(
                sample_belief, pred_fn, sample
            )
            uncertainties.append(metrics.total)
        
        # Test that we get reasonable uncertainty estimates
        uncertainties = torch.tensor(uncertainties)
        self.assertEqual(uncertainties.shape[0], self.batch_size)
        self.assertTrue(torch.all(uncertainties >= 0))
        self.assertFalse(torch.any(torch.isnan(uncertainties)))
        
        logger.info("Integration test passed")
    
    def test_edge_cases(self):
        """Test edge cases and error handling."""
        # Test with singular covariance matrix
        singular_cov = torch.zeros(self.thought_space_dim, self.thought_space_dim, device=self.device)
        singular_belief = (self.test_mean, singular_cov)
        
        # Should handle gracefully (add regularization)
        evidence = [{
            'observation': torch.zeros(self.thought_space_dim, device=self.device),
            'precision': torch.eye(self.thought_space_dim, device=self.device)
        }]
        
        try:
            result = self.uncertainty_system.update_belief_gaussian(singular_belief, evidence)
            # If it succeeds, check that result is valid
            self.assertFalse(torch.any(torch.isnan(result.posterior_mean)))
        except Exception as e:
            # Should either handle gracefully or raise informative error
            self.assertIsInstance(e, (RuntimeError, ValueError))
        
        # Test with empty evidence
        empty_evidence = []
        result = self.uncertainty_system.update_belief_gaussian(self.test_belief, empty_evidence)
        
        # Should return the prior unchanged
        torch.testing.assert_close(result.posterior_mean, self.test_mean)
        torch.testing.assert_close(result.posterior_covariance, self.test_cov)
        self.assertEqual(result.information_gain, 0.0)
        
        # Test with invalid noise model
        with self.assertRaises(ValueError):
            self.uncertainty_system.compute_aleatoric_uncertainty(
                torch.randn(self.thought_space_dim, device=self.device),
                noise_model='invalid_model'
            )
        
        logger.info("Edge cases test passed")
    
    @unittest.skipIf(not torch.cuda.is_available(), "CUDA not available")
    def test_gpu_compatibility(self):
        """Test GPU compatibility and memory management."""
        gpu_device = torch.device('cuda')
        
        # Create GPU-based uncertainty system
        gpu_uncertainty = AdvancedBayesianUncertaintyQuantification(
            thought_space_dim=32,  # Smaller for GPU test
            device=gpu_device,
            variational_inference=True
        )
        
        # Test basic operations on GPU
        gpu_mean = torch.randn(32, device=gpu_device)
        gpu_cov = torch.eye(32, device=gpu_device)
        gpu_belief = (gpu_mean, gpu_cov)
        
        gpu_evidence = [{
            'observation': torch.randn(32, device=gpu_device),
            'precision': torch.eye(32, device=gpu_device)
        }]
        
        result = gpu_uncertainty.update_belief_gaussian(gpu_belief, gpu_evidence)
        
        # Verify results are on GPU
        self.assertEqual(result.posterior_mean.device, gpu_device)
        self.assertEqual(result.posterior_covariance.device, gpu_device)
        
        # Test memory usage is reasonable
        torch.cuda.empty_cache()
        initial_memory = torch.cuda.memory_allocated(gpu_device)
        
        # Perform several operations
        for _ in range(10):
            gpu_uncertainty.update_belief_gaussian(gpu_belief, gpu_evidence)
        
        final_memory = torch.cuda.memory_allocated(gpu_device)
        memory_increase = final_memory - initial_memory
        
        # Memory increase should be reasonable (less than 100MB)
        self.assertLess(memory_increase, 100 * 1024 * 1024)
        
        logger.info("GPU compatibility test passed")
    
    def tearDown(self):
        """Clean up after each test."""
        # Clear CUDA cache if using GPU
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        
        # Clear any cached computations
        if hasattr(self.uncertainty_system, '_belief_cache'):
            self.uncertainty_system._belief_cache.clear()
        if hasattr(self.uncertainty_system, '_uncertainty_cache'):
            self.uncertainty_system._uncertainty_cache.clear()
    
    @classmethod
    def tearDownClass(cls):
        """Clean up test class resources."""
        # Remove test data directory if empty
        if cls.test_data_dir.exists() and not any(cls.test_data_dir.iterdir()):
            cls.test_data_dir.rmdir()
        
        logger.info("Test cleanup completed")


class TestBayesianUncertaintyIntegration(unittest.TestCase):
    """Integration tests for Bayesian uncertainty with other ULTRA components."""
    
    def setUp(self):
        """Set up integration tests."""
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.thought_space_dim = 32
        
        # Initialize uncertainty system
        self.uncertainty_system = AdvancedBayesianUncertaintyQuantification(
            thought_space_dim=self.thought_space_dim,
            device=self.device
        )
        
        # Mock other ULTRA components for integration testing
        self.mock_diffusion_output = torch.randn(5, self.thought_space_dim, device=self.device)
        self.mock_reasoning_state = torch.randn(self.thought_space_dim, device=self.device)
    
    def test_uncertainty_guided_reasoning(self):
        """Test how uncertainty guides reasoning decisions."""
        # Create multiple reasoning paths with different uncertainties
        num_paths = 5
        reasoning_paths = [
            torch.randn(self.thought_space_dim, device=self.device) for _ in range(num_paths)
        ]
        
        # Compute uncertainty for each path
        path_uncertainties = []
        for path in reasoning_paths:
            belief = (path, 0.1 * torch.eye(self.thought_space_dim, device=self.device))
            
            def pred_fn(params):
                return F.linear(params.unsqueeze(0), 
                              torch.randn(1, self.thought_space_dim, device=self.device)).squeeze()
            
            metrics = self.uncertainty_system.compute_uncertainty_metrics(belief, pred_fn)
            path_uncertainties.append(metrics.total)
        
        # Select path with lowest uncertainty
        best_path_idx = int(torch.argmin(torch.tensor(path_uncertainties)))
        
        # Verify selection makes sense
        self.assertTrue(0 <= best_path_idx < num_paths)
        self.assertEqual(path_uncertainties[best_path_idx], min(path_uncertainties))
        
        logger.info("Uncertainty-guided reasoning test passed")
    
    def test_adaptive_sampling_strategy(self):
        """Test adaptive sampling based on uncertainty estimates."""
        # Initialize sampling parameters
        current_belief = (
            torch.zeros(self.thought_space_dim, device=self.device),
            torch.eye(self.thought_space_dim, device=self.device)
        )
        
        # Simulate adaptive sampling loop
        num_iterations = 5
        information_gains = []
        
        for iteration in range(num_iterations):
            # Generate candidate samples
            candidates = [
                torch.randn(self.thought_space_dim, device=self.device) for _ in range(10)
            ]
            
            # Select best sample using experimental design
            best_idx, expected_utility = self.uncertainty_system.optimal_experimental_design(
                current_belief, candidates, utility_function='expected_information_gain'
            )
            
            best_sample = candidates[best_idx]
            
            # Simulate observing this sample
            evidence = [{
                'observation': best_sample,
                'precision': torch.eye(self.thought_space_dim, device=self.device)
            }]
            
            # Update belief
            result = self.uncertainty_system.update_belief_gaussian(current_belief, evidence)
            current_belief = (result.posterior_mean, result.posterior_covariance)
            
            information_gains.append(result.information_gain)
        
        # Information gains should generally decrease (diminishing returns)
        # or at least be positive
        self.assertTrue(all(gain >= 0 for gain in information_gains))
        
        logger.info("Adaptive sampling strategy test passed")
    
    def test_uncertainty_propagation(self):
        """Test propagation of uncertainty through reasoning chains."""
        # Create a chain of reasoning steps
        num_steps = 4
        reasoning_chain = []
        uncertainty_chain = []
        
        # Initial state
        current_state = torch.randn(self.thought_space_dim, device=self.device)
        current_uncertainty = 0.1  # Start with smaller uncertainty
        
        for step in range(num_steps):
            # Simulate reasoning step with uncertainty propagation
            noise_level = current_uncertainty * 0.1
            next_state = current_state + noise_level * torch.randn(self.thought_space_dim, device=self.device)
            
            # Compute uncertainty for this step
            step_belief = (
                next_state,
                current_uncertainty * torch.eye(self.thought_space_dim, device=self.device)
            )
            
            def step_pred_fn(params):
                return torch.sum(params)  # Simple aggregation
            
            metrics = self.uncertainty_system.compute_uncertainty_metrics(step_belief, step_pred_fn)
            
            reasoning_chain.append(next_state)
            uncertainty_chain.append(metrics.total)
            
            # Update for next iteration
            current_state = next_state
            current_uncertainty = min(metrics.total, 2.0)  # Cap uncertainty growth
        
        # Uncertainty should generally increase through the chain (error accumulation)
        # or at least remain bounded
        self.assertTrue(all(u >= 0 for u in uncertainty_chain))
        self.assertTrue(all(u < 5.0 for u in uncertainty_chain))  # More reasonable bounds
        
        logger.info("Uncertainty propagation test passed")


if __name__ == '__main__':
    # Configure test runner
    unittest.TestLoader.sortTestMethodsUsing = None  # Preserve test order
    
    # Create test suite
    loader = unittest.TestLoader()
    test_suite = unittest.TestSuite()
    
    # Add main test class using modern approach
    test_suite.addTest(loader.loadTestsFromTestCase(TestBayesianUncertaintyQuantification))
    
    # Add integration tests using modern approach
    test_suite.addTest(loader.loadTestsFromTestCase(TestBayesianUncertaintyIntegration))
    
    # Configure test runner with detailed output
    runner = unittest.TextTestRunner(
        verbosity=2,
        buffer=True,
        failfast=False,
        stream=sys.stdout
    )
    
    # Run tests
    logger.info("Starting Bayesian Uncertainty Quantification test suite...")
    result = runner.run(test_suite)
    
    # Print summary
    if result.wasSuccessful():
        logger.info("All tests passed successfully!")
    else:
        logger.error(f"Tests failed: {len(result.failures)} failures, {len(result.errors)} errors")
        
    # Exit with appropriate code
    sys.exit(0 if result.wasSuccessful() else 1)