#!/usr/bin/env python3
"""
Comprehensive test suite for the Probabilistic Inference Engine component of ULTRA.

This module provides exhaustive testing of probabilistic inference capabilities including:
- Bayesian inference with complex priors and evidence
- Monte Carlo sampling methods (MCMC, Importance Sampling, Sequential Monte Carlo)
- Variational inference for intractable posteriors
- Constraint satisfaction with probabilistic programming
- Multi-modal distribution handling and mixture models
- Causal inference and counterfactual reasoning
- Uncertainty propagation through inference chains
- Information-theoretic measures and entropy calculations

All tests are designed for production-grade validation with mathematical rigor.
"""

import unittest
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.distributions import (
    MultivariateNormal, Normal, Categorical, Dirichlet, 
    Beta, Gamma, Bern<PERSON>lli, Poisson, Exponential
)
from torch.distributions.kl import kl_divergence
from scipy.stats import entropy, multivariate_normal, chi2
from scipy.special import logsumexp, digamma, polygamma
import pytest
import matplotlib.pyplot as plt
from pathlib import Path
import sys
import os
import warnings
import logging
from typing import Dict, List, Tuple, Optional, Union, Callable, Any
from dataclasses import dataclass
from collections import defaultdict
import pickle
import json
import time

# Suppress warnings for cleaner test output
warnings.filterwarnings("ignore", category=UserWarning)
warnings.filterwarnings("ignore", category=FutureWarning)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Add the ULTRA module to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..')))

try:
    from ultra.diffusion_reasoning.probabilistic_inference import ProbabilisticInferenceEngine
    from ultra.diffusion_reasoning.thought_latent_space import ThoughtLatentSpace
    from ultra.diffusion_reasoning.bayesian_uncertainty import BayesianUncertaintyQuantification
    from ultra.diffusion_reasoning.conceptual_diffusion import ConceptualDiffusion
except ImportError as e:
    logger.error(f"Failed to import ULTRA components: {e}")
    raise


@dataclass
class InferenceResult:
    """Data class for storing inference results with metadata."""
    samples: torch.Tensor
    log_weights: torch.Tensor
    posterior_mean: torch.Tensor
    posterior_covariance: torch.Tensor
    evidence: float
    entropy: float
    kl_divergence: float
    effective_sample_size: float
    convergence_diagnostics: Dict[str, float]
    computation_time: float


class TestProbabilisticInferenceEngine(unittest.TestCase):
    """Comprehensive test suite for the Probabilistic Inference Engine."""
    
    @classmethod
    def setUpClass(cls):
        """Set up class-level test fixtures."""
        cls.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        cls.dtype = torch.float32
        cls.test_results_dir = Path(__file__).parent / "test_results"
        cls.test_results_dir.mkdir(exist_ok=True)
        
        # Set random seeds for reproducibility
        torch.manual_seed(42)
        np.random.seed(42)
        
        logger.info(f"Running tests on device: {cls.device}")
        logger.info(f"Test results directory: {cls.test_results_dir}")
    
    def setUp(self):
        """Set up test environment for each test method."""
        self.thought_space_dim = 128
        self.batch_size = 32
        self.num_samples = 1000
        self.tolerance = 1e-4
        
        # Initialize core components
        self.thought_space = ThoughtLatentSpace(
            dimension=self.thought_space_dim,
            num_levels=4,
            num_clusters=20,
            learning_rate=1e-3,
            regularization_strength=1e-4,
            device=self.device,
            dtype=self.dtype
        )
        
        self.uncertainty_quantifier = BayesianUncertaintyQuantification(
            thought_space_dim=self.thought_space_dim,
            prior_precision=1.0,
            likelihood_precision=10.0,
            device=self.device,
            dtype=self.dtype
        )
        
        self.inference_engine = ProbabilisticInferenceEngine(
            thought_space=self.thought_space,
            uncertainty_quantifier=self.uncertainty_quantifier,
            mcmc_warmup_steps=500,
            mcmc_sampling_steps=1000,
            vi_max_iterations=5000,
            vi_learning_rate=1e-2,
            importance_sample_size=2000,
            device=self.device,
            dtype=self.dtype
        )
        
        # Create test data
        self._create_test_data()
        
    def _create_test_data(self):
        """Create comprehensive test datasets for various inference scenarios."""
        # Multi-modal Gaussian mixture in thought space
        self.mixture_components = 3
        self.mixture_weights = F.softmax(torch.randn(self.mixture_components), dim=0)
        self.mixture_means = [
            torch.randn(self.thought_space_dim, device=self.device, dtype=self.dtype)
            for _ in range(self.mixture_components)
        ]
        self.mixture_covs = [
            self._create_positive_definite_matrix(self.thought_space_dim)
            for _ in range(self.mixture_components)
        ]
        
        # Hierarchical model parameters
        self.hierarchical_levels = 3
        self.hierarchical_means = [
            torch.randn(self.thought_space_dim, device=self.device, dtype=self.dtype)
            for _ in range(self.hierarchical_levels)
        ]
        self.hierarchical_precisions = [
            torch.exp(torch.randn(1, device=self.device, dtype=self.dtype))
            for _ in range(self.hierarchical_levels)
        ]
        
        # Causal graph structure (DAG)
        self.causal_nodes = ['X1', 'X2', 'X3', 'X4', 'Y']
        self.causal_edges = [('X1', 'X3'), ('X2', 'X3'), ('X3', 'X4'), ('X4', 'Y')]
        self.causal_parameters = {
            'X1': {'mean': 0.0, 'std': 1.0},
            'X2': {'mean': 0.0, 'std': 1.0},
            'X3': {'coeffs': [0.5, 0.3], 'noise_std': 0.2},
            'X4': {'coeffs': [0.7], 'noise_std': 0.1},
            'Y': {'coeffs': [0.8], 'noise_std': 0.15}
        }
        
        # Constraint satisfaction problem
        self.constraint_variables = ['A', 'B', 'C', 'D']
        self.constraint_domains = {
            var: torch.linspace(-2, 2, 21, device=self.device, dtype=self.dtype)
            for var in self.constraint_variables
        }
        self.constraints = [
            lambda a, b: torch.abs(a + b - 1.0) < 0.1,
            lambda b, c: torch.abs(b * c - 0.5) < 0.1,
            lambda c, d: torch.abs(c - d**2) < 0.1
        ]
        
    def _create_positive_definite_matrix(self, dim: int) -> torch.Tensor:
        """Create a random positive definite matrix."""
        A = torch.randn(dim, dim, device=self.device, dtype=self.dtype)
        return torch.mm(A, A.t()) + torch.eye(dim, device=self.device, dtype=self.dtype) * 0.1
        
    def _gaussian_mixture_log_prob(self, x: torch.Tensor) -> torch.Tensor:
        """Compute log probability of Gaussian mixture."""
        log_probs = torch.zeros(x.shape[0], self.mixture_components, device=self.device)
        
        for i in range(self.mixture_components):
            dist = MultivariateNormal(self.mixture_means[i], self.mixture_covs[i])
            log_probs[:, i] = dist.log_prob(x) + torch.log(self.mixture_weights[i])
            
        return torch.logsumexp(log_probs, dim=1)
    
    def _hierarchical_log_prob(self, x: torch.Tensor, level: int = 0) -> torch.Tensor:
        """Compute log probability for hierarchical model."""
        if level >= self.hierarchical_levels:
            return torch.zeros(x.shape[0], device=self.device)
            
        mean = self.hierarchical_means[level]
        precision = self.hierarchical_precisions[level]
        
        # Compute log probability at current level
        log_prob = -0.5 * precision * torch.sum((x - mean)**2, dim=1)
        
        # Add contribution from higher levels
        if level < self.hierarchical_levels - 1:
            log_prob += self._hierarchical_log_prob(x, level + 1)
            
        return log_prob
    
    def test_initialization_and_configuration(self):
        """Test proper initialization and configuration of the inference engine."""
        # Test basic initialization
        self.assertIsInstance(self.inference_engine, ProbabilisticInferenceEngine)
        self.assertEqual(self.inference_engine.device, self.device)
        self.assertEqual(self.inference_engine.dtype, self.dtype)
        
        # Test configuration parameters
        self.assertGreater(self.inference_engine.mcmc_warmup_steps, 0)
        self.assertGreater(self.inference_engine.mcmc_sampling_steps, 0)
        self.assertGreater(self.inference_engine.vi_max_iterations, 0)
        self.assertGreater(self.inference_engine.vi_learning_rate, 0)
        self.assertGreater(self.inference_engine.importance_sample_size, 0)
        
        # Test component integration
        self.assertIsInstance(self.inference_engine.thought_space, ThoughtLatentSpace)
        self.assertIsInstance(self.inference_engine.uncertainty_quantifier, BayesianUncertaintyQuantification)
        
        logger.info("✓ Initialization and configuration tests passed")
    
    def test_bayesian_inference_with_conjugate_priors(self):
        """Test Bayesian inference with conjugate prior-likelihood pairs."""
        # Normal-Normal conjugacy
        prior_mean = torch.zeros(self.thought_space_dim, device=self.device, dtype=self.dtype)
        prior_precision = torch.eye(self.thought_space_dim, device=self.device, dtype=self.dtype)
        
        # Generate synthetic observations
        true_mean = torch.ones(self.thought_space_dim, device=self.device, dtype=self.dtype)
        likelihood_precision = 5.0 * torch.eye(self.thought_space_dim, device=self.device, dtype=self.dtype)
        
        n_obs = 50
        observations = MultivariateNormal(true_mean, torch.inverse(likelihood_precision)).sample((n_obs,))
        
        # Perform Bayesian inference
        posterior_mean, posterior_precision = self.inference_engine.conjugate_bayesian_update(
            prior_mean, prior_precision, observations, likelihood_precision
        )
        
        # Analytical solution for verification
        expected_precision = prior_precision + n_obs * likelihood_precision
        expected_mean = torch.solve(
            torch.mv(prior_precision, prior_mean) + torch.mv(likelihood_precision, observations.sum(dim=0)),
            expected_precision
        )[0]
        
        # Verify results
        torch.testing.assert_close(posterior_precision, expected_precision, rtol=1e-4, atol=1e-4)
        torch.testing.assert_close(posterior_mean, expected_mean, rtol=1e-3, atol=1e-3)
        
        # Test Beta-Binomial conjugacy
        alpha_prior, beta_prior = 2.0, 3.0
        n_trials, n_successes = 100, 65
        
        alpha_posterior, beta_posterior = self.inference_engine.beta_binomial_update(
            alpha_prior, beta_prior, n_trials, n_successes
        )
        
        expected_alpha = alpha_prior + n_successes
        expected_beta = beta_prior + n_trials - n_successes
        
        self.assertAlmostEqual(alpha_posterior, expected_alpha, places=6)
        self.assertAlmostEqual(beta_posterior, expected_beta, places=6)
        
        logger.info("✓ Conjugate Bayesian inference tests passed")
    
    def test_markov_chain_monte_carlo_sampling(self):
        """Test MCMC sampling methods for complex posterior distributions."""
        # Test Metropolis-Hastings for multimodal distribution
        def log_prob_fn(x):
            return self._gaussian_mixture_log_prob(x)
        
        # Initialize chain
        initial_state = torch.randn(self.thought_space_dim, device=self.device, dtype=self.dtype)
        
        # Run MCMC
        samples, log_probs, acceptance_rate = self.inference_engine.metropolis_hastings_sample(
            log_prob_fn=log_prob_fn,
            initial_state=initial_state,
            num_samples=self.num_samples,
            step_size=0.1,
            adapt_step_size=True
        )
        
        # Verify sample properties
        self.assertEqual(samples.shape, (self.num_samples, self.thought_space_dim))
        self.assertEqual(log_probs.shape, (self.num_samples,))
        self.assertTrue(0.2 <= acceptance_rate <= 0.8)  # Reasonable acceptance rate
        
        # Test Hamiltonian Monte Carlo
        def grad_log_prob_fn(x):
            x.requires_grad_(True)
            log_prob = log_prob_fn(x)
            grad = torch.autograd.grad(log_prob.sum(), x, create_graph=True)[0]
            return grad
        
        hmc_samples, hmc_log_probs, hmc_acceptance_rate = self.inference_engine.hamiltonian_monte_carlo(
            log_prob_fn=log_prob_fn,
            grad_log_prob_fn=grad_log_prob_fn,
            initial_state=initial_state,
            num_samples=self.num_samples // 2,  # HMC is more expensive
            step_size=0.01,
            num_leapfrog_steps=10
        )
        
        self.assertEqual(hmc_samples.shape, (self.num_samples // 2, self.thought_space_dim))
        self.assertTrue(0.6 <= hmc_acceptance_rate <= 0.95)  # HMC typically has higher acceptance
        
        # Test No-U-Turn Sampler (NUTS)
        nuts_samples, nuts_diagnostics = self.inference_engine.nuts_sample(
            log_prob_fn=log_prob_fn,
            grad_log_prob_fn=grad_log_prob_fn,
            initial_state=initial_state,
            num_samples=self.num_samples // 4,  # NUTS is most expensive
            target_accept_prob=0.8
        )
        
        self.assertEqual(nuts_samples.shape, (self.num_samples // 4, self.thought_space_dim))
        self.assertIn('avg_tree_depth', nuts_diagnostics)
        self.assertIn('divergences', nuts_diagnostics)
        
        # Convergence diagnostics
        r_hat = self.inference_engine.compute_r_hat(samples.unsqueeze(0))  # Single chain
        self.assertTrue(torch.all(r_hat < 1.1))  # Good convergence
        
        ess = self.inference_engine.compute_effective_sample_size(samples)
        self.assertTrue(torch.all(ess > self.num_samples * 0.1))  # At least 10% efficiency
        
        logger.info("✓ MCMC sampling tests passed")
    
    def test_variational_inference_methods(self):
        """Test variational inference for approximate posterior computation."""
        # Mean-field variational inference
        def log_prob_fn(x):
            return self._gaussian_mixture_log_prob(x)
        
        # Initialize variational parameters
        var_mean = torch.randn(self.thought_space_dim, device=self.device, dtype=self.dtype, requires_grad=True)
        var_log_std = torch.randn(self.thought_space_dim, device=self.device, dtype=self.dtype, requires_grad=True)
        
        # Run mean-field VI
        vi_params, elbo_history = self.inference_engine.mean_field_variational_inference(
            log_prob_fn=log_prob_fn,
            initial_mean=var_mean.clone(),
            initial_log_std=var_log_std.clone(),
            num_iterations=1000,
            num_samples=100,
            learning_rate=1e-2
        )
        
        # Verify convergence
        self.assertTrue(len(elbo_history) > 0)
        self.assertTrue(elbo_history[-1] > elbo_history[0])  # ELBO should increase
        
        # Test full-rank variational inference
        L_init = torch.randn(self.thought_space_dim, self.thought_space_dim, device=self.device, dtype=self.dtype)
        L_init = torch.tril(L_init)  # Lower triangular
        
        full_rank_params, full_rank_elbo = self.inference_engine.full_rank_variational_inference(
            log_prob_fn=log_prob_fn,
            initial_mean=var_mean.clone(),
            initial_L=L_init,
            num_iterations=500,
            num_samples=100,
            learning_rate=5e-3
        )
        
        # Full-rank should generally achieve higher ELBO
        self.assertTrue(full_rank_elbo[-1] >= elbo_history[-1] - 0.1)
        
        # Test normalizing flow variational inference
        flow_params, flow_elbo = self.inference_engine.normalizing_flow_vi(
            log_prob_fn=log_prob_fn,
            flow_type='planar',
            num_flows=5,
            num_iterations=800,
            num_samples=200,
            learning_rate=1e-3
        )
        
        # Flow-based VI should achieve the highest ELBO
        self.assertTrue(flow_elbo[-1] >= full_rank_elbo[-1] - 0.1)
        
        logger.info("✓ Variational inference tests passed")
    
    def test_importance_sampling_methods(self):
        """Test importance sampling for evidence estimation and sampling."""
        def target_log_prob(x):
            return self._gaussian_mixture_log_prob(x)
        
        # Standard importance sampling
        proposal_mean = torch.zeros(self.thought_space_dim, device=self.device, dtype=self.dtype)
        proposal_cov = 2.0 * torch.eye(self.thought_space_dim, device=self.device, dtype=self.dtype)
        
        samples, log_weights, log_evidence = self.inference_engine.importance_sampling(
            target_log_prob_fn=target_log_prob,
            proposal_mean=proposal_mean,
            proposal_cov=proposal_cov,
            num_samples=self.num_samples
        )
        
        self.assertEqual(samples.shape, (self.num_samples, self.thought_space_dim))
        self.assertEqual(log_weights.shape, (self.num_samples,))
        self.assertFalse(torch.isnan(log_evidence))
        self.assertFalse(torch.isinf(log_evidence))
        
        # Adaptive importance sampling
        adaptive_samples, adaptive_log_weights, final_proposal = self.inference_engine.adaptive_importance_sampling(
            target_log_prob_fn=target_log_prob,
            initial_proposal_mean=proposal_mean,
            initial_proposal_cov=proposal_cov,
            num_iterations=10,
            samples_per_iteration=100
        )
        
        # Adaptive sampling should improve proposal
        initial_kl = self._compute_kl_divergence_gaussian(proposal_mean, proposal_cov, 
                                                        final_proposal['mean'], final_proposal['cov'])
        self.assertTrue(initial_kl > 0.01)  # Proposal should change significantly
        
        # Sequential Monte Carlo
        smc_samples, smc_log_weights, smc_evidence = self.inference_engine.sequential_monte_carlo(
            target_log_prob_fn=target_log_prob,
            num_particles=500,
            num_steps=20,
            resampling_threshold=0.5
        )
        
        self.assertEqual(smc_samples.shape, (500, self.thought_space_dim))
        self.assertTrue(torch.all(torch.isfinite(smc_log_weights)))
        
        logger.info("✓ Importance sampling tests passed")
    
    def test_constraint_satisfaction_inference(self):
        """Test probabilistic constraint satisfaction problems."""
        # Define constraint satisfaction problem
        variables = self.constraint_variables
        domains = self.constraint_domains
        constraints = self.constraints
        
        # Solve using probabilistic inference
        solution, satisfaction_prob = self.inference_engine.constraint_satisfaction(
            variables=variables,
            domains=domains,
            constraints=constraints,
            max_iterations=1000,
            temperature=1.0
        )
        
        # Verify solution
        self.assertEqual(len(solution), len(variables))
        for var in variables:
            self.assertIn(var, solution)
            self.assertTrue(torch.any(torch.isclose(solution[var], domains[var], atol=0.1)))
        
        # Test constraint satisfaction with soft constraints
        soft_constraints = [
            {'constraint': constraints[0], 'weight': 1.0},
            {'constraint': constraints[1], 'weight': 0.8},
            {'constraint': constraints[2], 'weight': 0.6}
        ]
        
        soft_solution, soft_scores = self.inference_engine.soft_constraint_satisfaction(
            variables=variables,
            domains=domains,
            soft_constraints=soft_constraints,
            num_samples=1000
        )
        
        self.assertTrue(all(score >= 0 for score in soft_scores.values()))
        
        # Test probabilistic constraint propagation
        propagated_domains = self.inference_engine.constraint_propagation(
            variables=variables,
            domains=domains,
            constraints=constraints,
            max_iterations=100
        )
        
        # Domains should be reduced after propagation
        for var in variables:
            self.assertTrue(len(propagated_domains[var]) <= len(domains[var]))
        
        logger.info("✓ Constraint satisfaction tests passed")
    
    def test_causal_inference_and_counterfactuals(self):
        """Test causal inference and counterfactual reasoning."""
        # Generate causal data
        n_samples = 1000
        causal_data = self.inference_engine.generate_causal_data(
            nodes=self.causal_nodes,
            edges=self.causal_edges,
            parameters=self.causal_parameters,
            n_samples=n_samples
        )
        
        self.assertEqual(len(causal_data), len(self.causal_nodes))
        for node in self.causal_nodes:
            self.assertEqual(causal_data[node].shape[0], n_samples)
        
        # Learn causal structure
        learned_edges, edge_weights = self.inference_engine.causal_structure_learning(
            data=causal_data,
            method='pc_algorithm',
            significance_level=0.05
        )
        
        # Verify learned structure (should be close to true structure)
        true_edge_set = set(self.causal_edges)
        learned_edge_set = set(learned_edges)
        
        # Calculate precision and recall
        true_positives = len(true_edge_set.intersection(learned_edge_set))
        precision = true_positives / len(learned_edge_set) if learned_edge_set else 0
        recall = true_positives / len(true_edge_set) if true_edge_set else 0
        
        self.assertTrue(precision >= 0.6)  # At least 60% precision
        self.assertTrue(recall >= 0.6)     # At least 60% recall
        
        # Test do-calculus for interventional queries
        intervention = {'X1': 1.5}
        interventional_dist = self.inference_engine.compute_interventional_distribution(
            causal_graph=learned_edges,
            data=causal_data,
            intervention=intervention,
            target_variable='Y'
        )
        
        self.assertIsInstance(interventional_dist, dict)
        self.assertIn('mean', interventional_dist)
        self.assertIn('std', interventional_dist)
        
        # Test counterfactual queries
        evidence = {'X2': 0.5, 'Y': 2.0}
        counterfactual = {'X1': -1.0}
        
        counterfactual_dist = self.inference_engine.counterfactual_inference(
            causal_graph=learned_edges,
            data=causal_data,
            evidence=evidence,
            counterfactual=counterfactual,
            target_variable='Y'
        )
        
        self.assertIsInstance(counterfactual_dist, dict)
        self.assertIn('samples', counterfactual_dist)
        self.assertIn('probability', counterfactual_dist)
        
        # Test causal effect estimation
        treatment_var = 'X1'
        outcome_var = 'Y'
        
        ate = self.inference_engine.estimate_average_treatment_effect(
            causal_graph=learned_edges,
            data=causal_data,
            treatment=treatment_var,
            outcome=outcome_var,
            method='backdoor'
        )
        
        self.assertIsInstance(ate, float)
        self.assertFalse(np.isnan(ate))
        
        logger.info("✓ Causal inference tests passed")
    
    def test_hierarchical_bayesian_models(self):
        """Test hierarchical Bayesian model inference."""
        # Create hierarchical data
        n_groups = 5
        n_obs_per_group = 50
        
        # Group-level parameters
        group_means = torch.randn(n_groups, device=self.device, dtype=self.dtype)
        group_stds = torch.exp(torch.randn(n_groups, device=self.device, dtype=self.dtype))
        
        # Generate data
        hierarchical_data = []
        for g in range(n_groups):
            group_data = Normal(group_means[g], group_stds[g]).sample((n_obs_per_group,))
            hierarchical_data.append(group_data)
        
        # Fit hierarchical model
        posterior_params = self.inference_engine.fit_hierarchical_model(
            data=hierarchical_data,
            model_type='normal_normal',
            num_samples=1000,
            warmup_steps=500
        )
        
        # Verify posterior structure
        self.assertIn('global_mean', posterior_params)
        self.assertIn('global_precision', posterior_params)
        self.assertIn('group_means', posterior_params)
        self.assertIn('group_precisions', posterior_params)
        
        # Test posterior predictive sampling
        posterior_predictive = self.inference_engine.posterior_predictive_sampling(
            posterior_params=posterior_params,
            model_type='normal_normal',
            num_predictions=100,
            new_group=True
        )
        
        self.assertEqual(posterior_predictive.shape[0], 100)
        
        # Test hierarchical model comparison
        model_evidence = self.inference_engine.compute_model_evidence(
            data=hierarchical_data,
            model_type='normal_normal',
            num_samples=500
        )
        
        self.assertIsInstance(model_evidence, float)
        self.assertFalse(np.isnan(model_evidence))
        
        logger.info("✓ Hierarchical Bayesian model tests passed")
    
    def test_mixture_model_inference(self):
        """Test mixture model inference and model selection."""
        # Generate mixture data
        true_k = 3
        true_weights = F.softmax(torch.randn(true_k), dim=0)
        true_means = torch.randn(true_k, self.thought_space_dim, device=self.device, dtype=self.dtype)
        true_covs = [self._create_positive_definite_matrix(self.thought_space_dim) for _ in range(true_k)]
        
        n_samples = 500
        mixture_data = self.inference_engine.generate_mixture_data(
            weights=true_weights,
            means=true_means,
            covariances=true_covs,
            n_samples=n_samples
        )
        
        # Fit mixture model with known K
        fitted_params = self.inference_engine.fit_gaussian_mixture(
            data=mixture_data,
            num_components=true_k,
            method='variational_bayes',
            max_iterations=200
        )
        
        # Verify fitted parameters
        self.assertEqual(fitted_params['weights'].shape[0], true_k)
        self.assertEqual(fitted_params['means'].shape, (true_k, self.thought_space_dim))
        self.assertEqual(len(fitted_params['covariances']), true_k)
        
        # Test model selection for unknown K
        k_candidates = range(1, 6)
        model_selection_results = self.inference_engine.mixture_model_selection(
            data=mixture_data,
            k_candidates=k_candidates,
            criterion='bic',
            num_runs=5
        )
        
        # Best K should be close to true K
        best_k = model_selection_results['best_k']
        self.assertTrue(abs(best_k - true_k) <= 1)
        
        # Test infinite mixture model (Dirichlet Process)
        dp_params = self.inference_engine.fit_dirichlet_process_mixture(
            data=mixture_data,
            concentration=1.0,
            num_samples=1000,
            warmup_steps=500
        )
        
        self.assertIn('cluster_assignments', dp_params)
        self.assertIn('cluster_parameters', dp_params)
        
        # Test mixture model diagnostics
        diagnostics = self.inference_engine.mixture_model_diagnostics(
            data=mixture_data,
            fitted_params=fitted_params,
            true_params={'weights': true_weights, 'means': true_means, 'covariances': true_covs}
        )
        
        self.assertIn('log_likelihood', diagnostics)
        self.assertIn('aic', diagnostics)
        self.assertIn('bic', diagnostics)
        self.assertIn('parameter_recovery_error', diagnostics)
        
        logger.info("✓ Mixture model inference tests passed")
    
    def test_information_theoretic_measures(self):
        """Test computation of information-theoretic quantities."""
        # Generate test distributions
        dist1 = MultivariateNormal(
            torch.zeros(self.thought_space_dim, device=self.device, dtype=self.dtype),
            torch.eye(self.thought_space_dim, device=self.device, dtype=self.dtype)
        )
        
        dist2 = MultivariateNormal(
            torch.ones(self.thought_space_dim, device=self.device, dtype=self.dtype),
            2.0 * torch.eye(self.thought_space_dim, device=self.device, dtype=self.dtype)
        )
        
        # Test entropy computation
        entropy1 = self.inference_engine.compute_entropy(dist1, method='analytical')
        entropy2 = self.inference_engine.compute_entropy(dist2, method='analytical')
        
        # Higher variance should lead to higher entropy
        self.assertTrue(entropy2 > entropy1)
        
        # Test Monte Carlo entropy estimation
        samples1 = dist1.sample((self.num_samples,))
        mc_entropy1 = self.inference_engine.compute_entropy(samples1, method='monte_carlo')
        
        # Should be close to analytical entropy
        self.assertTrue(abs(mc_entropy1 - entropy1) < 0.5)
        
        # Test KL divergence
        kl_div = self.inference_engine.compute_kl_divergence(dist1, dist2, method='analytical')
        mc_kl_div = self.inference_engine.compute_kl_divergence(dist1, dist2, method='monte_carlo', num_samples=self.num_samples)
        
        self.assertTrue(kl_div > 0)  # KL divergence is non-negative
        self.assertTrue(abs(kl_div - mc_kl_div) < 0.5)  # MC estimate should be close
        
        # Test mutual information
        joint_samples = torch.cat([samples1, dist2.sample((self.num_samples,))], dim=1)
        mutual_info = self.inference_engine.compute_mutual_information(
            joint_samples[:, :self.thought_space_dim],
            joint_samples[:, self.thought_space_dim:],
            method='kraskov'
        )
        
        self.assertTrue(mutual_info >= 0)
        
        # Test Jensen-Shannon divergence
        js_div = self.inference_engine.compute_jensen_shannon_divergence(dist1, dist2, num_samples=self.num_samples)
        
        self.assertTrue(0 <= js_div <= 1)  # JS divergence is bounded
        
        # Test Wasserstein distance
        samples2 = dist2.sample((self.num_samples,))
        wasserstein_dist = self.inference_engine.compute_wasserstein_distance(samples1, samples2, p=2)
        
        self.assertTrue(wasserstein_dist > 0)
        
        logger.info("✓ Information-theoretic measures tests passed")
    
    def test_uncertainty_propagation(self):
        """Test uncertainty propagation through inference chains."""
        # Define a chain of inference operations
        def operation1(x):
            return x + torch.randn_like(x) * 0.1
        
        def operation2(x):
            return torch.sin(x) + torch.randn_like(x) * 0.05
        
        def operation3(x):
            return x**2 + torch.randn_like(x) * 0.02
        
        operations = [operation1, operation2, operation3]
        
        # Initial uncertainty
        initial_mean = torch.zeros(self.thought_space_dim, device=self.device, dtype=self.dtype)
        initial_cov = torch.eye(self.thought_space_dim, device=self.device, dtype=self.dtype)
        
        # Propagate uncertainty
        final_mean, final_cov, intermediate_results = self.inference_engine.propagate_uncertainty(
            operations=operations,
            initial_mean=initial_mean,
            initial_covariance=initial_cov,
            method='monte_carlo',
            num_samples=self.num_samples
        )
        
        # Verify shapes
        self.assertEqual(final_mean.shape, (self.thought_space_dim,))
        self.assertEqual(final_cov.shape, (self.thought_space_dim, self.thought_space_dim))
        self.assertEqual(len(intermediate_results), len(operations))
        
        # Test linearization method
        linear_mean, linear_cov = self.inference_engine.propagate_uncertainty_linear(
            operations=operations,
            initial_mean=initial_mean,
            initial_covariance=initial_cov
        )
        
        # Linear and MC methods should give similar results for small uncertainties
        mean_diff = torch.norm(final_mean - linear_mean)
        self.assertTrue(mean_diff < 1.0)
        
        # Test unscented transform
        ut_mean, ut_cov = self.inference_engine.propagate_uncertainty_unscented(
            operations=operations,
            initial_mean=initial_mean,
            initial_covariance=initial_cov,
            alpha=1e-3,
            beta=2.0,
            kappa=0.0
        )
        
        # UT should be more accurate than linearization
        ut_mean_diff = torch.norm(final_mean - ut_mean)
        self.assertTrue(ut_mean_diff <= mean_diff)
        
        logger.info("✓ Uncertainty propagation tests passed")
    
    def test_active_learning_and_optimal_design(self):
        """Test active learning and optimal experimental design."""
        # Define a simple regression model
        true_weights = torch.randn(self.thought_space_dim, device=self.device, dtype=self.dtype)
        noise_std = 0.1
        
        def model_fn(x, weights):
            return torch.mv(x, weights)
        
        def log_likelihood_fn(y, x, weights):
            pred = model_fn(x, weights)
            return Normal(pred, noise_std).log_prob(y).sum()
        
        # Initial data
        n_initial = 20
        X_initial = torch.randn(n_initial, self.thought_space_dim, device=self.device, dtype=self.dtype)
        y_initial = model_fn(X_initial, true_weights) + noise_std * torch.randn(n_initial, device=self.device, dtype=self.dtype)
        
        # Active learning loop
        X_current = X_initial.clone()
        y_current = y_initial.clone()
        
        for iteration in range(5):
            # Fit current model
            posterior_weights = self.inference_engine.bayesian_linear_regression(
                X=X_current,
                y=y_current,
                prior_precision=1.0,
                noise_precision=1.0 / (noise_std**2)
            )
            
            # Generate candidate points
            n_candidates = 100
            X_candidates = torch.randn(n_candidates, self.thought_space_dim, device=self.device, dtype=self.dtype)
            
            # Compute acquisition function (expected information gain)
            acquisition_values = self.inference_engine.compute_acquisition_function(
                X_candidates=X_candidates,
                posterior_params=posterior_weights,
                model_fn=model_fn,
                acquisition_type='expected_information_gain'
            )
            
            # Select best candidate
            best_idx = torch.argmax(acquisition_values)
            x_new = X_candidates[best_idx:best_idx+1]
            y_new = model_fn(x_new, true_weights) + noise_std * torch.randn(1, device=self.device, dtype=self.dtype)
            
            # Update data
            X_current = torch.cat([X_current, x_new], dim=0)
            y_current = torch.cat([y_current, y_new], dim=0)
        
        # Final model should be better than initial
        final_posterior = self.inference_engine.bayesian_linear_regression(
            X=X_current,
            y=y_current,
            prior_precision=1.0,
            noise_precision=1.0 / (noise_std**2)
        )
        
        # Compute prediction error
        X_test = torch.randn(100, self.thought_space_dim, device=self.device, dtype=self.dtype)
        y_test_true = model_fn(X_test, true_weights)
        y_test_pred = model_fn(X_test, final_posterior['mean'])
        
        prediction_error = torch.mean((y_test_true - y_test_pred)**2)
        self.assertTrue(prediction_error < 0.5)  # Should have reasonable accuracy
        
        # Test optimal design for parameter estimation
        optimal_design = self.inference_engine.optimal_experimental_design(
            model_fn=model_fn,
            parameter_dim=self.thought_space_dim,
            design_space_bounds=(-2.0, 2.0),
            num_experiments=10,
            criterion='d_optimal'
        )
        
        self.assertEqual(optimal_design.shape, (10, self.thought_space_dim))
        
        logger.info("✓ Active learning and optimal design tests passed")
    
    def test_model_criticism_and_checking(self):
        """Test posterior predictive checks and model criticism."""
        # Generate data from a known model
        true_mean = torch.ones(self.thought_space_dim, device=self.device, dtype=self.dtype)
        true_cov = self._create_positive_definite_matrix(self.thought_space_dim)
        
        n_obs = 200
        observed_data = MultivariateNormal(true_mean, true_cov).sample((n_obs,))
        
        # Fit correct model
        correct_posterior = self.inference_engine.fit_multivariate_normal(
            data=observed_data,
            prior_mean=torch.zeros(self.thought_space_dim, device=self.device, dtype=self.dtype),
            prior_precision=torch.eye(self.thought_space_dim, device=self.device, dtype=self.dtype)
        )
        
        # Fit incorrect model (wrong dimensionality)
        incorrect_data = observed_data[:, :self.thought_space_dim//2]  # Use only half dimensions
        incorrect_posterior = self.inference_engine.fit_multivariate_normal(
            data=incorrect_data,
            prior_mean=torch.zeros(self.thought_space_dim//2, device=self.device, dtype=self.dtype),
            prior_precision=torch.eye(self.thought_space_dim//2, device=self.device, dtype=self.dtype)
        )
        
        # Posterior predictive checks
        ppc_correct = self.inference_engine.posterior_predictive_check(
            observed_data=observed_data,
            posterior_params=correct_posterior,
            model_type='multivariate_normal',
            test_statistics=['mean', 'covariance', 'multivariate_normality'],
            num_posterior_samples=500
        )
        
        # Correct model should pass most checks
        self.assertTrue(ppc_correct['p_values']['mean'] > 0.05)
        self.assertTrue(ppc_correct['p_values']['covariance'] > 0.05)
        
        # Test model comparison
        model_comparison = self.inference_engine.compare_models(
            data=observed_data,
            models=[
                {'type': 'multivariate_normal', 'params': correct_posterior},
                {'type': 'multivariate_normal', 'params': incorrect_posterior, 'data': incorrect_data}
            ],
            criteria=['aic', 'bic', 'waic', 'loo']
        )
        
        # Correct model should have better scores
        self.assertTrue(model_comparison['best_model_index'] == 0)
        
        # Test residual analysis
        residual_analysis = self.inference_engine.analyze_residuals(
            observed_data=observed_data,
            fitted_params=correct_posterior,
            model_type='multivariate_normal'
        )
        
        self.assertIn('standardized_residuals', residual_analysis)
        self.assertIn('qq_plot_data', residual_analysis)
        self.assertIn('residual_autocorrelation', residual_analysis)
        
        logger.info("✓ Model criticism and checking tests passed")
    
    def test_computational_efficiency_and_scalability(self):
        """Test computational efficiency and scalability of inference methods."""
        # Test scaling with dimension
        dimensions = [16, 32, 64, 128]
        times = []
        
        for dim in dimensions:
            # Create test problem
            mean = torch.zeros(dim, device=self.device, dtype=self.dtype)
            cov = torch.eye(dim, device=self.device, dtype=self.dtype)
            target_dist = MultivariateNormal(mean, cov)
            
            def log_prob_fn(x):
                return target_dist.log_prob(x)
            
            # Time MCMC sampling
            start_time = time.time()
            samples, _, _ = self.inference_engine.metropolis_hastings_sample(
                log_prob_fn=log_prob_fn,
                initial_state=torch.zeros(dim, device=self.device, dtype=self.dtype),
                num_samples=500,
                step_size=0.1
            )
            end_time = time.time()
            
            times.append(end_time - start_time)
            
            # Verify samples
            self.assertEqual(samples.shape, (500, dim))
        
        # Time should scale reasonably with dimension
        self.assertTrue(times[-1] / times[0] < 100)  # Not more than 100x slower for 8x dimension
        
        # Test batch processing
        batch_sizes = [1, 10, 50, 100]
        batch_times = []
        
        for batch_size in batch_sizes:
            X = torch.randn(batch_size, self.thought_space_dim, device=self.device, dtype=self.dtype)
            
            start_time = time.time()
            log_probs = self._gaussian_mixture_log_prob(X)
            end_time = time.time()
            
            batch_times.append(end_time - start_time)
            
            self.assertEqual(log_probs.shape, (batch_size,))
        
        # Batch processing should be efficient
        self.assertTrue(batch_times[-1] / batch_times[0] < 10)  # Less than 10x slower for 100x batch size
        
        # Test memory efficiency
        torch.cuda.empty_cache() if torch.cuda.is_available() else None
        initial_memory = torch.cuda.memory_allocated() if torch.cuda.is_available() else 0
        
        # Large computation
        large_samples = self.inference_engine.importance_sampling(
            target_log_prob_fn=lambda x: self._gaussian_mixture_log_prob(x),
            proposal_mean=torch.zeros(self.thought_space_dim, device=self.device, dtype=self.dtype),
            proposal_cov=torch.eye(self.thought_space_dim, device=self.device, dtype=self.dtype),
            num_samples=10000
        )[0]
        
        peak_memory = torch.cuda.memory_allocated() if torch.cuda.is_available() else 0
        
        # Clean up
        del large_samples
        torch.cuda.empty_cache() if torch.cuda.is_available() else None
        
        final_memory = torch.cuda.memory_allocated() if torch.cuda.is_available() else 0
        
        # Memory should be released
        if torch.cuda.is_available():
            self.assertTrue(final_memory <= initial_memory + 1024**2)  # Within 1MB of initial
        
        logger.info("✓ Computational efficiency and scalability tests passed")
    
    def test_integration_with_ultra_components(self):
        """Test integration with other ULTRA system components."""
        # Test integration with ThoughtLatentSpace
        concept_desc = "A test concept for integration testing"
        concept_embedding = self.thought_space.encode_concept(concept_desc)
        
        # Use concept embedding in probabilistic inference
        constraints = [
            {
                'concept': concept_embedding,
                'relation': 'similar',
                'strength': 0.8
            }
        ]
        
        inferred_concept, confidence = self.inference_engine.infer_concept(
            constraints=constraints,
            num_samples=100
        )
        
        # Should be similar to the original concept
        similarity = torch.dot(concept_embedding, inferred_concept)
        self.assertTrue(similarity > 0.7)
        
        # Test integration with uncertainty quantification
        belief = (
            torch.zeros(self.thought_space_dim, device=self.device, dtype=self.dtype),
            torch.eye(self.thought_space_dim, device=self.device, dtype=self.dtype)
        )
        
        evidence = (
            concept_embedding,
            0.1 * torch.eye(self.thought_space_dim, device=self.device, dtype=self.dtype)
        )
        
        updated_belief = self.uncertainty_quantifier.update_belief(belief, evidence)
        
        # Updated belief should be closer to the evidence
        distance_before = torch.norm(belief[0] - concept_embedding)
        distance_after = torch.norm(updated_belief[0] - concept_embedding)
        self.assertTrue(distance_after < distance_before)
        
        # Test probabilistic reasoning chains
        reasoning_chain = [
            lambda x: self.thought_space.vector_operation(x, concept_embedding, None, 'composition'),
            lambda x: self.inference_engine.infer_concept([{'concept': x, 'relation': 'similar', 'strength': 0.9}], num_samples=50)[0],
            lambda x: self.uncertainty_quantifier.sample_from_belief((x, 0.1 * torch.eye(self.thought_space_dim, device=self.device, dtype=self.dtype)), n_samples=1)[0]
        ]
        
        initial_concept = torch.randn(self.thought_space_dim, device=self.device, dtype=self.dtype)
        initial_concept = F.normalize(initial_concept, dim=0)
        
        final_concept = initial_concept.clone()
        for operation in reasoning_chain:
            final_concept = operation(final_concept)
        
        # Final concept should be different from initial
        self.assertTrue(torch.norm(final_concept - initial_concept) > 0.1)
        
        # Test probabilistic composition with thought space
        concepts = [
            self.thought_space.encode_concept(f"concept_{i}")
            for i in range(3)
        ]
        
        weights = F.softmax(torch.randn(3), dim=0)
        
        composed_concept, uncertainty = self.inference_engine.probabilistic_composition(
            concepts=concepts,
            weights=weights
        )
        
        # Composed concept should be in the thought space
        self.assertEqual(composed_concept.shape, (self.thought_space_dim,))
        self.assertAlmostEqual(torch.norm(composed_concept).item(), 1.0, places=3)
        
        logger.info("✓ Integration with ULTRA components tests passed")
    
    def test_edge_cases_and_robustness(self):
        """Test edge cases and robustness of the inference engine."""
        # Test with singular covariance matrices
        singular_cov = torch.zeros(self.thought_space_dim, self.thought_space_dim, device=self.device, dtype=self.dtype)
        singular_cov[0, 0] = 1.0
        
        try:
            samples = self.inference_engine.importance_sampling(
                target_log_prob_fn=lambda x: MultivariateNormal(torch.zeros(self.thought_space_dim, device=self.device), singular_cov + 1e-6 * torch.eye(self.thought_space_dim, device=self.device)).log_prob(x),
                proposal_mean=torch.zeros(self.thought_space_dim, device=self.device, dtype=self.dtype),
                proposal_cov=torch.eye(self.thought_space_dim, device=self.device, dtype=self.dtype),
                num_samples=100
            )[0]
            self.assertEqual(samples.shape, (100, self.thought_space_dim))
        except Exception as e:
            logger.warning(f"Singular covariance test failed: {e}")
        
        # Test with extreme values
        extreme_mean = 1e6 * torch.ones(self.thought_space_dim, device=self.device, dtype=self.dtype)
        extreme_cov = 1e-6 * torch.eye(self.thought_space_dim, device=self.device, dtype=self.dtype)
        
        try:
            extreme_dist = MultivariateNormal(extreme_mean, extreme_cov)
            log_prob = extreme_dist.log_prob(extreme_mean.unsqueeze(0))
            self.assertFalse(torch.isnan(log_prob))
            self.assertFalse(torch.isinf(log_prob))
        except Exception as e:
            logger.warning(f"Extreme values test failed: {e}")
        
        # Test with empty data
        try:
            empty_data = torch.empty(0, self.thought_space_dim, device=self.device, dtype=self.dtype)
            result = self.inference_engine.fit_multivariate_normal(
                data=empty_data,
                prior_mean=torch.zeros(self.thought_space_dim, device=self.device, dtype=self.dtype),
                prior_precision=torch.eye(self.thought_space_dim, device=self.device, dtype=self.dtype)
            )
            # Should fall back to prior
            torch.testing.assert_close(result['mean'], torch.zeros(self.thought_space_dim, device=self.device, dtype=self.dtype), rtol=1e-3, atol=1e-3)
        except Exception as e:
            logger.warning(f"Empty data test failed: {e}")
        
        # Test with NaN/Inf inputs
        nan_input = torch.full((10, self.thought_space_dim), float('nan'), device=self.device, dtype=self.dtype)
        inf_input = torch.full((10, self.thought_space_dim), float('inf'), device=self.device, dtype=self.dtype)
        
        with self.assertRaises((ValueError, RuntimeError)):
            self.inference_engine.fit_multivariate_normal(
                data=nan_input,
                prior_mean=torch.zeros(self.thought_space_dim, device=self.device, dtype=self.dtype),
                prior_precision=torch.eye(self.thought_space_dim, device=self.device, dtype=self.dtype)
            )
        
        with self.assertRaises((ValueError, RuntimeError)):
            self.inference_engine.fit_multivariate_normal(
                data=inf_input,
                prior_mean=torch.zeros(self.thought_space_dim, device=self.device, dtype=self.dtype),
                prior_precision=torch.eye(self.thought_space_dim, device=self.device, dtype=self.dtype)
            )
        
        # Test numerical stability
        small_numbers = 1e-10 * torch.randn(100, self.thought_space_dim, device=self.device, dtype=self.dtype)
        large_numbers = 1e10 * torch.randn(100, self.thought_space_dim, device=self.device, dtype=self.dtype)
        
        try:
            small_result = self.inference_engine.fit_multivariate_normal(
                data=small_numbers,
                prior_mean=torch.zeros(self.thought_space_dim, device=self.device, dtype=self.dtype),
                prior_precision=torch.eye(self.thought_space_dim, device=self.device, dtype=self.dtype)
            )
            self.assertFalse(torch.any(torch.isnan(small_result['mean'])))
            self.assertFalse(torch.any(torch.isnan(small_result['covariance'])))
        except Exception as e:
            logger.warning(f"Small numbers test failed: {e}")
        
        logger.info("✓ Edge cases and robustness tests passed")
    
    def _compute_kl_divergence_gaussian(self, mean1, cov1, mean2, cov2):
        """Compute KL divergence between two multivariate Gaussians."""
        dist1 = MultivariateNormal(mean1, cov1)
        dist2 = MultivariateNormal(mean2, cov2)
        return kl_divergence(dist1, dist2).item()
    
    def tearDown(self):
        """Clean up after each test."""
        # Clear GPU memory if using CUDA
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
    
    @classmethod
    def tearDownClass(cls):
        """Clean up class-level fixtures."""
        # Save test results summary
        results_summary = {
            'device': str(cls.device),
            'timestamp': time.time(),
            'test_status': 'completed'
        }
        
        with open(cls.test_results_dir / 'test_summary.json', 'w') as f:
            json.dump(results_summary, f, indent=2)
        
        logger.info("All tests completed successfully!")


if __name__ == '__main__':
    # Configure test runner
    unittest.main(
        verbosity=2,
        failfast=False,
        buffer=True,
        catchbreak=True
    )