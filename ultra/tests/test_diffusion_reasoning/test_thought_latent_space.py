#!/usr/bin/env python3
"""
Test suite for the Thought Latent Space component of the ULTRA architecture.

This module contains comprehensive tests to validate the functionality and mathematical
correctness of the Thought Latent Space, which provides a continuous mathematical space
for representing abstract ideas, reasoning paths, and semantic relationships.

The tests cover:
- Initialization and configuration
- Hierarchical encoding and projection
- Semantic similarity and distance metrics
- Vector operations and concept manipulation
- Relational structure and extraction
- Compositional properties
- Integration with other ULTRA components
"""

import unittest
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
import matplotlib.pyplot as plt
from sklearn.manifold import TSNE
from scipy.spatial.distance import cosine
import os
import sys
import tempfile
from pathlib import Path
import pytest
import json
import logging

# Add the ULTRA module to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

from ultra.diffusion_reasoning.thought_latent_space import ThoughtLatentSpace
from ultra.diffusion_reasoning.conceptual_diffusion import ConceptualDiffusion
from ultra.core_neural.neuromorphic_core import Neuromorphic<PERSON><PERSON>
from ultra.hyper_transformer.dynamic_attention import DynamicAttention
from ultra.meta_cognitive.reasoning_graphs import ReasoningGraph

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class TestThoughtLatentSpace(unittest.TestCase):
    """Test suite for the Thought Latent Space component."""
    
    def setUp(self):
        """Set up the test environment with a Thought Latent Space instance."""
        self.dimension = 128
        self.num_levels = 3
        self.num_clusters = 20
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.batch_size = 16
        
        # Create a Thought Latent Space instance
        self.tls = ThoughtLatentSpace(
            dimension=self.dimension,
            num_levels=self.num_levels,
            num_clusters=self.num_clusters,
            device=self.device
        )
        
        # Create sample data for testing
        self.sample_concepts = [
            "artificial intelligence",
            "machine learning",
            "neural networks",
            "deep learning",
            "computer vision",
            "natural language processing",
            "reinforcement learning",
            "generative models",
            "transformer architecture",
            "diffusion models"
        ]
        
        self.concept_pairs = [
            ("dog", "cat"),
            ("sun", "moon"),
            ("king", "queen"),
            ("man", "woman"),
            ("rich", "poor"),
            ("happy", "sad"),
            ("big", "small"),
            ("hot", "cold"),
            ("good", "bad"),
            ("love", "hate")
        ]
        
        self.analogies = [
            ("man", "woman", "king", "queen"),  # man:woman::king:queen
            ("water", "ice", "gas", "liquid"),  # water:ice::gas:liquid
            ("good", "better", "bad", "worse"), # good:better::bad:worse
            ("hot", "cold", "summer", "winter"), # hot:cold::summer:winter
            ("cat", "kitten", "dog", "puppy")   # cat:kitten::dog:puppy
        ]
        
        # Create random vector representations for testing
        self.random_vectors = torch.randn(10, self.dimension, device=self.device)
        self.random_vectors = F.normalize(self.random_vectors, dim=1)
        
        # Create a temporary directory for saving and loading models
        self.temp_dir = tempfile.TemporaryDirectory()
        
    def tearDown(self):
        """Clean up resources after tests."""
        self.temp_dir.cleanup()
        
    def test_initialization(self):
        """Test proper initialization of the Thought Latent Space."""
        # Check dimensions and configurations
        self.assertEqual(self.tls.dimension, self.dimension)
        self.assertEqual(self.tls.num_levels, self.num_levels)
        self.assertEqual(self.tls.num_clusters, self.num_clusters)
        self.assertEqual(self.tls.device, self.device)
        
        # Check cluster centers
        self.assertEqual(self.tls.cluster_centers.shape, (self.num_clusters, self.dimension))
        self.assertTrue(torch.all(torch.norm(self.tls.cluster_centers, dim=1) > 0))
        
        # Check projection networks for hierarchical levels
        self.assertEqual(len(self.tls.level_projections), self.num_levels)
        for i in range(self.num_levels):
            self.assertIsInstance(self.tls.level_projections[i], nn.Module)
            
        # Check the text encoder initialization
        self.assertIsNotNone(self.tls.text_encoder)
        
    def test_encode_concept_text(self):
        """Test encoding text concepts into the latent space."""
        # Test single concept encoding
        concept = "artificial intelligence"
        embedding = self.tls.encode_concept(concept)
        
        # Check shape and normalization
        self.assertEqual(embedding.shape, (self.dimension,))
        self.assertAlmostEqual(torch.norm(embedding).item(), 1.0, delta=1e-5)
        
        # Test batch encoding
        batch_embeddings = self.tls.encode_concept_batch(self.sample_concepts)
        
        # Check shape and normalization
        self.assertEqual(batch_embeddings.shape, (len(self.sample_concepts), self.dimension))
        norms = torch.norm(batch_embeddings, dim=1)
        self.assertTrue(torch.all(torch.abs(norms - 1.0) < 1e-5))
        
        # Check that similar concepts are closer in the latent space
        sim_matrix = torch.mm(batch_embeddings, batch_embeddings.T)
        
        # "Machine learning" should be closer to "neural networks" than to "reinforcement learning"
        ml_idx = self.sample_concepts.index("machine learning")
        nn_idx = self.sample_concepts.index("neural networks")
        rl_idx = self.sample_concepts.index("reinforcement learning")
        
        self.assertGreater(sim_matrix[ml_idx, nn_idx].item(), 
                           sim_matrix[ml_idx, rl_idx].item())
        
    def test_encode_concept_vector(self):
        """Test encoding vector representations into the latent space."""
        # Test single vector encoding
        vector = torch.randn(self.dimension, device=self.device)
        embedding = self.tls.encode_concept(vector)
        
        # Check shape and normalization
        self.assertEqual(embedding.shape, (self.dimension,))
        self.assertAlmostEqual(torch.norm(embedding).item(), 1.0, delta=1e-5)
        
        # Test that the embedding is different from but related to the input
        cosine_sim = F.cosine_similarity(embedding.unsqueeze(0), 
                                         F.normalize(vector, dim=0).unsqueeze(0))
        self.assertGreater(cosine_sim.item(), 0.5)  # Should be similar but not identical
        
        # Test batch encoding
        batch_vectors = torch.randn(5, self.dimension, device=self.device)
        batch_embeddings = self.tls.encode_concept_batch(batch_vectors)
        
        # Check shape and normalization
        self.assertEqual(batch_embeddings.shape, (5, self.dimension))
        norms = torch.norm(batch_embeddings, dim=1)
        self.assertTrue(torch.all(torch.abs(norms - 1.0) < 1e-5))
        
    def test_hierarchical_encoding(self):
        """Test hierarchical encoding at different abstraction levels."""
        # Test encoding at all hierarchical levels
        concept = "neural networks"
        hierarchical_embeddings = self.tls.hierarchical_encode(concept)
        
        # Check that we have embeddings for each level
        self.assertEqual(len(hierarchical_embeddings), self.num_levels)
        
        # Check that each level has the correct shape and is normalized
        for level_emb in hierarchical_embeddings:
            self.assertEqual(level_emb.shape, (self.dimension,))
            self.assertAlmostEqual(torch.norm(level_emb).item(), 1.0, delta=1e-5)
            
        # Check different abstraction properties
        # Lower levels should capture more specific details
        # Higher levels should capture more abstract concepts
        
        # Let's encode a related concept and check similarity at different levels
        related_concept = "deep learning"
        related_hierarchical = self.tls.hierarchical_encode(related_concept)
        
        similarities = [
            F.cosine_similarity(hierarchical_embeddings[i].unsqueeze(0),
                               related_hierarchical[i].unsqueeze(0)).item()
            for i in range(self.num_levels)
        ]
        
        # Similarity should increase with abstraction level
        # This might not always be true depending on initialization, but it's a good test
        # for the hierarchical structure after training
        logger.info(f"Hierarchical similarities between '{concept}' and '{related_concept}': {similarities}")
        
        # Test projection between levels
        # Project from level 0 to level 1
        level0_emb = hierarchical_embeddings[0]
        projected_level1 = self.tls.project_to_level(level0_emb, 0, 1)
        
        # Check shape and normalization
        self.assertEqual(projected_level1.shape, (self.dimension,))
        self.assertAlmostEqual(torch.norm(projected_level1).item(), 1.0, delta=1e-5)
        
        # Should be similar but not identical to actual level 1 embedding
        similarity = F.cosine_similarity(projected_level1.unsqueeze(0),
                                        hierarchical_embeddings[1].unsqueeze(0))
        logger.info(f"Similarity between projected and actual level 1: {similarity.item()}")
        self.assertGreater(similarity.item(), 0.5)
        
    def test_find_nearest_concepts(self):
        """Test finding nearest concepts in the latent space."""
        # Encode sample concepts
        embeddings = self.tls.encode_concept_batch(self.sample_concepts)
        
        # Create a query concept
        query = "deep neural networks"
        query_embedding = self.tls.encode_concept(query)
        
        # Find nearest concepts
        k = 3
        indices, distances = self.tls.find_nearest_concepts(query_embedding, embeddings, k=k)
        
        # Check output shapes
        self.assertEqual(len(indices), k)
        self.assertEqual(len(distances), k)
        
        # Check that distances are sorted (nearest first)
        for i in range(len(distances) - 1):
            self.assertLessEqual(distances[i], distances[i+1])
            
        # Check that the nearest concepts make semantic sense
        nearest_concepts = [self.sample_concepts[idx] for idx in indices]
        logger.info(f"Query: '{query}', Nearest concepts: {nearest_concepts}")
        
        # "deep neural networks" should be close to "neural networks" and "deep learning"
        self.assertTrue("neural networks" in nearest_concepts or "deep learning" in nearest_concepts)
        
        # Test with custom distance metric
        def custom_distance(a, b):
            # A weighted cosine distance
            sim = F.cosine_similarity(a.unsqueeze(0), b.unsqueeze(0)).item()
            return 1 - sim
            
        indices_custom, distances_custom = self.tls.find_nearest_concepts(
            query_embedding, embeddings, k=k, distance_fn=custom_distance
        )
        
        # Check that we get valid results with custom distance function
        self.assertEqual(len(indices_custom), k)
        self.assertEqual(len(distances_custom), k)
        
    def test_semantic_similarity(self):
        """Test semantic similarity calculations."""
        # Encode concept pairs
        similarities = []
        for concept1, concept2 in self.concept_pairs:
            emb1 = self.tls.encode_concept(concept1)
            emb2 = self.tls.encode_concept(concept2)
            sim = self.tls.semantic_similarity(emb1, emb2)
            similarities.append((concept1, concept2, sim))
            
        # Log similarities for inspection
        for c1, c2, sim in similarities:
            logger.info(f"Similarity between '{c1}' and '{c2}': {sim:.4f}")
            
        # Test specific cases
        # Encode antonyms
        hot_emb = self.tls.encode_concept("hot")
        cold_emb = self.tls.encode_concept("cold")
        antonym_sim = self.tls.semantic_similarity(hot_emb, cold_emb)
        
        # Encode synonyms
        happy_emb = self.tls.encode_concept("happy")
        joyful_emb = self.tls.encode_concept("joyful")
        synonym_sim = self.tls.semantic_similarity(happy_emb, joyful_emb)
        
        # Synonym similarity should be higher than antonym similarity
        logger.info(f"Antonym similarity: {antonym_sim:.4f}, Synonym similarity: {synonym_sim:.4f}")
        self.assertGreater(synonym_sim, antonym_sim)
        
        # Test batch similarity computation
        embs1 = self.tls.encode_concept_batch([pair[0] for pair in self.concept_pairs])
        embs2 = self.tls.encode_concept_batch([pair[1] for pair in self.concept_pairs])
        batch_sims = self.tls.batch_semantic_similarity(embs1, embs2)
        
        # Check shape
        self.assertEqual(batch_sims.shape, (len(self.concept_pairs),))
        
        # Check that batch results match individual results
        for i, (c1, c2, sim) in enumerate(similarities):
            self.assertAlmostEqual(sim, batch_sims[i].item(), delta=1e-5)
            
    def test_vector_operations(self):
        """Test vector operations in the latent space."""
        # Test analogy operation: a is to b as c is to ?
        for a_text, b_text, c_text, d_text in self.analogies:
            a = self.tls.encode_concept(a_text)
            b = self.tls.encode_concept(b_text)
            c = self.tls.encode_concept(c_text)
            
            # Predict d using vector operations
            d_pred = self.tls.vector_operation(a, b, c, operation_type='analogy')
            
            # Encode the expected result
            d_true = self.tls.encode_concept(d_text)
            
            # Check that d_pred is close to d_true
            similarity = F.cosine_similarity(d_pred.unsqueeze(0), d_true.unsqueeze(0))
            logger.info(f"Analogy {a_text}:{b_text}::{c_text}:{d_text}, similarity: {similarity.item():.4f}")
            self.assertGreater(similarity.item(), 0.5)
            
        # Test composition operation
        concept1 = "artificial"
        concept2 = "intelligence"
        combined = "artificial intelligence"
        
        emb1 = self.tls.encode_concept(concept1)
        emb2 = self.tls.encode_concept(concept2)
        combined_emb = self.tls.encode_concept(combined)
        
        # Compose using vector operation
        composed_emb = self.tls.vector_operation(emb1, emb2, None, operation_type='composition')
        
        # Check similarity to the combined concept
        similarity = F.cosine_similarity(composed_emb.unsqueeze(0), combined_emb.unsqueeze(0))
        logger.info(f"Composition similarity: {similarity.item():.4f}")
        self.assertGreater(similarity.item(), 0.5)
        
        # Test negation operation
        concept = "good"
        negated = "bad"
        
        emb = self.tls.encode_concept(concept)
        negated_emb = self.tls.encode_concept(negated)
        
        # Negate using vector operation
        neg_emb = self.tls.vector_operation(emb, None, None, operation_type='negation')
        
        # Negated concept should be more similar to the antonym
        sim_to_antonym = F.cosine_similarity(neg_emb.unsqueeze(0), negated_emb.unsqueeze(0))
        sim_to_original = F.cosine_similarity(neg_emb.unsqueeze(0), emb.unsqueeze(0))
        logger.info(f"Negation: similarity to antonym: {sim_to_antonym.item():.4f}, "
                    f"similarity to original: {sim_to_original.item():.4f}")
        self.assertGreater(sim_to_antonym.item(), sim_to_original.item())
        
    def test_extract_relation(self):
        """Test extraction of relations between concepts."""
        # Extract relations from concept pairs
        relations = []
        for concept1, concept2 in self.concept_pairs:
            emb1 = self.tls.encode_concept(concept1)
            emb2 = self.tls.encode_concept(concept2)
            relation = self.tls.extract_relation(emb1, emb2)
            relations.append(relation)
            
        # Check that relations are normalized
        for relation in relations:
            self.assertAlmostEqual(torch.norm(relation).item(), 1.0, delta=1e-5)
            
        # Test applying relations
        # Apply the "man" -> "woman" relation to "king"
        man_emb = self.tls.encode_concept("man")
        woman_emb = self.tls.encode_concept("woman")
        king_emb = self.tls.encode_concept("king")
        queen_emb = self.tls.encode_concept("queen")
        
        gender_relation = self.tls.extract_relation(man_emb, woman_emb)
        
        # Apply the relation to "king"
        predicted_queen = self.tls.apply_relation(king_emb, gender_relation)
        
        # Check similarity to "queen"
        similarity = F.cosine_similarity(predicted_queen.unsqueeze(0), queen_emb.unsqueeze(0))
        logger.info(f"Relation application similarity: {similarity.item():.4f}")
        self.assertGreater(similarity.item(), 0.6)
        
        # Test relation clustering
        # Extract multiple relations of the same type
        gender_pairs = [("man", "woman"), ("king", "queen"), ("actor", "actress"), 
                         ("waiter", "waitress"), ("host", "hostess")]
        size_pairs = [("large", "small"), ("big", "little"), ("huge", "tiny"), 
                       ("enormous", "microscopic"), ("gigantic", "minuscule")]
        
        gender_relations = []
        size_relations = []
        
        for c1, c2 in gender_pairs:
            emb1 = self.tls.encode_concept(c1)
            emb2 = self.tls.encode_concept(c2)
            relation = self.tls.extract_relation(emb1, emb2)
            gender_relations.append(relation)
            
        for c1, c2 in size_pairs:
            emb1 = self.tls.encode_concept(c1)
            emb2 = self.tls.encode_concept(c2)
            relation = self.tls.extract_relation(emb1, emb2)
            size_relations.append(relation)
            
        # Calculate average similarity within each group
        avg_gender_sim = 0
        for i in range(len(gender_relations)):
            for j in range(i + 1, len(gender_relations)):
                avg_gender_sim += F.cosine_similarity(
                    gender_relations[i].unsqueeze(0), 
                    gender_relations[j].unsqueeze(0)
                ).item()
        avg_gender_sim /= (len(gender_relations) * (len(gender_relations) - 1) / 2)
        
        avg_size_sim = 0
        for i in range(len(size_relations)):
            for j in range(i + 1, len(size_relations)):
                avg_size_sim += F.cosine_similarity(
                    size_relations[i].unsqueeze(0), 
                    size_relations[j].unsqueeze(0)
                ).item()
        avg_size_sim /= (len(size_relations) * (len(size_relations) - 1) / 2)
        
        # Calculate average cross-group similarity
        avg_cross_sim = 0
        for gender_rel in gender_relations:
            for size_rel in size_relations:
                avg_cross_sim += F.cosine_similarity(
                    gender_rel.unsqueeze(0), 
                    size_rel.unsqueeze(0)
                ).item()
        avg_cross_sim /= (len(gender_relations) * len(size_relations))
        
        logger.info(f"Average gender relation similarity: {avg_gender_sim:.4f}")
        logger.info(f"Average size relation similarity: {avg_size_sim:.4f}")
        logger.info(f"Average cross-group similarity: {avg_cross_sim:.4f}")
        
        # In-group similarity should be higher than cross-group similarity
        self.assertGreater(avg_gender_sim, avg_cross_sim)
        self.assertGreater(avg_size_sim, avg_cross_sim)
        
    def test_traverse(self):
        """Test traversal between concepts in the latent space."""
        # Define start and end concepts
        start_concept = "novice"
        end_concept = "expert"
        
        # Encode concepts
        start_emb = self.tls.encode_concept(start_concept)
        end_emb = self.tls.encode_concept(end_concept)
        
        # Generate trajectory between concepts
        num_steps = 5
        trajectory = self.tls.traverse(start_emb, end_emb, num_steps)
        
        # Check that we get the right number of steps
        self.assertEqual(len(trajectory), num_steps)
        
        # Check that each step is normalized
        for step in trajectory:
            self.assertAlmostEqual(torch.norm(step).item(), 1.0, delta=1e-5)
            
        # Check that the first step is close to start and last step is close to end
        start_sim = F.cosine_similarity(trajectory[0].unsqueeze(0), start_emb.unsqueeze(0))
        end_sim = F.cosine_similarity(trajectory[-1].unsqueeze(0), end_emb.unsqueeze(0))
        
        logger.info(f"Trajectory start similarity: {start_sim.item():.4f}")
        logger.info(f"Trajectory end similarity: {end_sim.item():.4f}")
        
        self.assertGreater(start_sim.item(), 0.9)
        self.assertGreater(end_sim.item(), 0.9)
        
        # Check that we're progressively getting closer to the end
        prev_sim = -1
        for step in trajectory:
            curr_sim = F.cosine_similarity(step.unsqueeze(0), end_emb.unsqueeze(0)).item()
            self.assertGreaterEqual(curr_sim, prev_sim)
            prev_sim = curr_sim
    
    def test_save_and_load(self):
        """Test saving and loading the Thought Latent Space model."""
        # Define a save path
        save_path = os.path.join(self.temp_dir.name, "thought_latent_space.pt")
        
        # Save the model
        self.tls.save(save_path)
        
        # Check that the file exists
        self.assertTrue(os.path.exists(save_path))
        
        # Create a new model with different parameters
        new_tls = ThoughtLatentSpace(
            dimension=64,  # Different dimension
            num_levels=2,  # Different levels
            num_clusters=10,  # Different clusters
            device=self.device
        )
        
        # The parameters should be different
        self.assertNotEqual(self.tls.dimension, new_tls.dimension)
        self.assertNotEqual(self.tls.num_levels, new_tls.num_levels)
        self.assertNotEqual(self.tls.num_clusters, new_tls.num_clusters)
        
        # Load the saved model
        new_tls.load(save_path)
        
        # Now the parameters should match
        self.assertEqual(self.tls.dimension, new_tls.dimension)
        self.assertEqual(self.tls.num_levels, new_tls.num_levels)
        self.assertEqual(self.tls.num_clusters, new_tls.num_clusters)
        
        # Check that the weights are the same by comparing outputs
        test_concept = "test concept"
        orig_embedding = self.tls.encode_concept(test_concept)
        loaded_embedding = new_tls.encode_concept(test_concept)
        
        # The embeddings should be identical
        torch.testing.assert_close(orig_embedding, loaded_embedding)
        
    def test_train_model(self):
        """Test training the Thought Latent Space model."""
        # Create a small dataset for training
        word_pairs = [
            ("man", "woman"), ("king", "queen"), ("cat", "kitten"),
            ("dog", "puppy"), ("good", "bad"), ("hot", "cold")
        ]
        
        analogies = [
            ("man", "woman", "king", "queen"),
            ("cat", "kitten", "dog", "puppy"),
            ("good", "better", "bad", "worse")
        ]
        
        # Create training data
        train_data = {
            "word_pairs": word_pairs,
            "analogies": analogies
        }
        
        # Train the model
        num_epochs = 5
        self.tls.train_model(train_data, num_epochs=num_epochs, batch_size=2, 
                            learning_rate=0.001, verbose=True)
        
        # After training, test some properties
        # Test that analogies work better
        a_text, b_text, c_text, d_text = analogies[0]
        a = self.tls.encode_concept(a_text)
        b = self.tls.encode_concept(b_text)
        c = self.tls.encode_concept(c_text)
        d_true = self.tls.encode_concept(d_text)
        
        d_pred = self.tls.vector_operation(a, b, c, operation_type='analogy')
        similarity = F.cosine_similarity(d_pred.unsqueeze(0), d_true.unsqueeze(0))
        
        logger.info(f"Post-training analogy similarity: {similarity.item():.4f}")
        self.assertGreater(similarity.item(), 0.6)
        
    def test_concept_clusters(self):
        """Test clustering concepts in the latent space."""
        # Create a set of related concepts
        animals = ["dog", "cat", "horse", "cow", "sheep", "pig", "tiger", "lion", "elephant", "giraffe"]
        fruits = ["apple", "banana", "orange", "grape", "strawberry", "blueberry", "pear", "peach", "plum", "cherry"]
        vehicles = ["car", "truck", "bus", "train", "plane", "boat", "bicycle", "motorcycle", "scooter", "helicopter"]
        
        all_concepts = animals + fruits + vehicles
        
        # Encode all concepts
        embeddings = self.tls.encode_concept_batch(all_concepts)
        
        # Perform clustering
        num_clusters = 3
        labels, centers = self.tls.cluster_concepts(embeddings, num_clusters)
        
        # Check that we get the right number of labels and centers
        self.assertEqual(len(labels), len(all_concepts))
        self.assertEqual(centers.shape, (num_clusters, self.dimension))
        
        # Check that clusters are somewhat aligned with categories
        animal_clusters = labels[:len(animals)]
        fruit_clusters = labels[len(animals):len(animals)+len(fruits)]
        vehicle_clusters = labels[len(animals)+len(fruits):]
        
        # Count majority cluster for each category
        animal_majority = max(set(animal_clusters.tolist()), key=animal_clusters.tolist().count)
        fruit_majority = max(set(fruit_clusters.tolist()), key=fruit_clusters.tolist().count)
        vehicle_majority = max(set(vehicle_clusters.tolist()), key=vehicle_clusters.tolist().count)
        
        logger.info(f"Animal majority cluster: {animal_majority}")
        logger.info(f"Fruit majority cluster: {fruit_majority}")
        logger.info(f"Vehicle majority cluster: {vehicle_majority}")
        
        # Check that the majority clusters are different for at least 2 categories
        different_majorities = len(set([animal_majority, fruit_majority, vehicle_majority])) >= 2
        self.assertTrue(different_majorities)
        
        # Check nearest concepts to cluster centers
        for i in range(num_clusters):
            indices, _ = self.tls.find_nearest_concepts(centers[i], embeddings, k=5)
            nearest_concepts = [all_concepts[idx] for idx in indices]
            logger.info(f"Cluster {i} nearest concepts: {nearest_concepts}")
            
    def test_visualization(self):
        """Test visualization of concepts in the latent space."""
        # Skip test if matplotlib is not available
        try:
            import matplotlib.pyplot as plt
            from sklearn.manifold import TSNE
        except ImportError:
            logger.warning("Skipping visualization test as matplotlib or sklearn is not available")
            return
        
        # Create a set of concepts for visualization
        concepts = [
            # Animals
            "dog", "cat", "horse", "cow", "lion", "tiger", "elephant", 
            # Fruits
            "apple", "banana", "orange", "grape", "strawberry", "pear", 
            # Vehicles
            "car", "truck", "bus", "train", "plane", "bicycle"
        ]
        
        # Encode concepts
        embeddings = self.tls.encode_concept_batch(concepts)
        
        # Visualize using t-SNE
        try:
            # Use a temporary file for the plot
            temp_file = os.path.join(self.temp_dir.name, "tsne_plot.png")
            self.tls.visualize_concepts(concepts, embeddings, file_path=temp_file)
            
            # Check that the file was created
            self.assertTrue(os.path.exists(temp_file))
            
            # File should have a reasonable size
            self.assertGreater(os.path.getsize(temp_file), 10000)
        except Exception as e:
            logger.warning(f"Visualization failed: {e}")
            
    def test_integration_with_diffusion(self):
        """Test integration with the Conceptual Diffusion component."""
        # Initialize a ConceptualDiffusion model
        try:
            diffusion = ConceptualDiffusion(
                concept_dim=self.dimension,
                num_timesteps=100,
                beta_schedule='linear',
                device=self.device
            )
        except ImportError:
            logger.warning("Skipping integration test as ConceptualDiffusion is not available")
            return
            
        # Create a starting concept
        start_concept = "simple idea"
        start_emb = self.tls.encode_concept(start_concept)
        
        # Use diffusion to generate related concepts
        batch_size = 5
        samples = diffusion.sample(
            batch_size=batch_size,
            guidance_fn=lambda x, t: 0.1 * (start_emb - x),
            guidance_strength=0.5
        )
        
        # Check that we get the right number of samples
        self.assertEqual(samples.shape, (batch_size, self.dimension))
        
        # Check that the samples are normalized
        norms = torch.norm(samples, dim=1)
        self.assertTrue(torch.all(torch.abs(norms - 1.0) < 1e-5))
        
        # Check that samples are related to but different from the start concept
        similarities = [
            F.cosine_similarity(sample.unsqueeze(0), start_emb.unsqueeze(0)).item()
            for sample in samples
        ]
        
        # Log similarities
        logger.info(f"Diffusion sample similarities to '{start_concept}': {similarities}")
        
        # Samples should be moderately similar to the start concept
        self.assertTrue(all(0.5 < sim < 0.95 for sim in similarities))
        
        # Check that the samples are different from each other
        for i in range(batch_size):
            for j in range(i + 1, batch_size):
                sim = F.cosine_similarity(
                    samples[i].unsqueeze(0), samples[j].unsqueeze(0)
                ).item()
                self.assertLess(sim, 0.95)  # Not too similar
                
    def test_integration_with_reasoning_graph(self):
        """Test integration with the Reasoning Graph component."""
        try:
            # Create a simple reasoning graph
            graph = ReasoningGraph()
        except ImportError:
            logger.warning("Skipping integration test as ReasoningGraph is not available")
            return
            
        # Create a set of facts and concepts
        facts = [
            "Socrates is a man",
            "All men are mortal",
            "Socrates is mortal"
        ]
        
        # Encode facts into the thought space
        fact_embeddings = [self.tls.encode_concept(fact) for fact in facts]
        
        # Add nodes to the reasoning graph
        for i, (fact, embedding) in enumerate(zip(facts, fact_embeddings)):
            graph.add_node(
                node_id=f"fact_{i}",
                text=fact,
                embedding=embedding.cpu().numpy(),
                node_type="fact"
            )
            
        # Add edges between facts
        graph.add_edge(
            from_id="fact_0",
            to_id="fact_2",
            edge_type="supports",
            weight=0.8
        )
        
        graph.add_edge(
            from_id="fact_1",
            to_id="fact_2",
            edge_type="supports",
            weight=0.9
        )
        
        # Find paths in the graph
        paths = graph.find_paths("fact_0", "fact_2")
        self.assertGreaterEqual(len(paths), 1)
        
        # Extract a subgraph
        subgraph = graph.extract_subgraph(["fact_0", "fact_2"])
        self.assertEqual(len(subgraph.nodes), 2)
        
        # Verify that embeddings in the graph match the thought space
        for node_id, node in graph.nodes.items():
            if "embedding" in node:
                # Convert back to tensor and to the right device
                emb = torch.tensor(node["embedding"], device=self.device)
                
                # Re-encode the text
                text = node["text"]
                re_encoded = self.tls.encode_concept(text)
                
                # Calculate similarity
                sim = F.cosine_similarity(emb.unsqueeze(0), re_encoded.unsqueeze(0))
                logger.info(f"Graph node embedding similarity: {sim.item():.4f}")
                self.assertGreater(sim.item(), 0.99)
                
    def test_adversarial_examples(self):
        """Test robustness to adversarial examples."""
        # Create a base concept
        concept = "artificial intelligence"
        base_emb = self.tls.encode_concept(concept)
        
        # Create an adversarial perturbation
        epsilon = 0.01
        perturbation = epsilon * torch.randn_like(base_emb)
        perturbed_emb = base_emb + perturbation
        perturbed_emb = F.normalize(perturbed_emb, dim=0)
        
        # Measure similarity between base and perturbed embeddings
        sim = F.cosine_similarity(base_emb.unsqueeze(0), perturbed_emb.unsqueeze(0))
        logger.info(f"Similarity after small perturbation: {sim.item():.4f}")
        
        # Small perturbations should not drastically change the embedding
        self.assertGreater(sim.item(), 0.95)
        
        # Test adversarial word
        adv_concept = "art1ficial inte11igence"  # Substituting some characters
        adv_emb = self.tls.encode_concept(adv_concept)
        
        # Measure similarity to the original concept
        sim = F.cosine_similarity(base_emb.unsqueeze(0), adv_emb.unsqueeze(0))
        logger.info(f"Similarity with adversarial text: {sim.item():.4f}")
        
        # Despite character substitutions, the meaning should be preserved
        self.assertGreater(sim.item(), 0.8)
        
    def test_batch_processing(self):
        """Test batch processing capabilities for efficiency."""
        # Create a large batch of concepts
        batch_size = 100
        batch_concepts = ["concept_" + str(i) for i in range(batch_size)]
        
        # Time batch processing
        import time
        start_time = time.time()
        batch_embeddings = self.tls.encode_concept_batch(batch_concepts)
        batch_time = time.time() - start_time
        
        # Time sequential processing
        start_time = time.time()
        seq_embeddings = []
        for concept in batch_concepts:
            emb = self.tls.encode_concept(concept)
            seq_embeddings.append(emb)
        seq_embeddings = torch.stack(seq_embeddings)
        seq_time = time.time() - start_time
        
        # Check that results are the same
        torch.testing.assert_close(batch_embeddings, seq_embeddings)
        
        # Batch processing should be faster
        logger.info(f"Batch processing time: {batch_time:.4f}s, Sequential time: {seq_time:.4f}s")
        self.assertLess(batch_time, seq_time)
        
        # Test batch vector operations
        a_batch = torch.randn(batch_size, self.dimension, device=self.device)
        b_batch = torch.randn(batch_size, self.dimension, device=self.device)
        a_batch = F.normalize(a_batch, dim=1)
        b_batch = F.normalize(b_batch, dim=1)
        
        # Time batch operation
        start_time = time.time()
        batch_result = self.tls.batch_vector_operation(a_batch, b_batch, None, operation_type='composition')
        batch_op_time = time.time() - start_time
        
        # Time sequential operation
        start_time = time.time()
        seq_result = []
        for a, b in zip(a_batch, b_batch):
            res = self.tls.vector_operation(a, b, None, operation_type='composition')
            seq_result.append(res)
        seq_result = torch.stack(seq_result)
        seq_op_time = time.time() - start_time
        
        # Check that results are the same
        torch.testing.assert_close(batch_result, seq_result)
        
        # Batch operation should be faster
        logger.info(f"Batch operation time: {batch_op_time:.4f}s, Sequential operation time: {seq_op_time:.4f}s")
        self.assertLess(batch_op_time, seq_op_time)
        
    def test_embedding_interpolation(self):
        """Test more complex embedding interpolation methods."""
        # Create some test concepts
        concepts = ["red", "green", "blue"]
        concept_embeds = self.tls.encode_concept_batch(concepts)
        
        # Test linear interpolation
        weights = torch.tensor([0.2, 0.3, 0.5], device=self.device)
        interpolated = self.tls.interpolate_embeddings(concept_embeds, weights, method='linear')
        
        # Check shape and normalization
        self.assertEqual(interpolated.shape, (self.dimension,))
        self.assertAlmostEqual(torch.norm(interpolated).item(), 1.0, delta=1e-5)
        
        # Test spherical interpolation
        slerp_interpolated = self.tls.interpolate_embeddings(concept_embeds, weights, method='slerp')
        
        # Check shape and normalization
        self.assertEqual(slerp_interpolated.shape, (self.dimension,))
        self.assertAlmostEqual(torch.norm(slerp_interpolated).item(), 1.0, delta=1e-5)
        
        # Slerp interpolation should be different from linear interpolation
        difference = torch.norm(interpolated - slerp_interpolated)
        logger.info(f"Difference between linear and slerp interpolation: {difference.item():.4f}")
        self.assertGreater(difference.item(), 0.01)
        
        # The interpolated embedding should be closer to "blue" since it has the highest weight
        similarities = [
            F.cosine_similarity(interpolated.unsqueeze(0), concept_embeds[i].unsqueeze(0)).item()
            for i in range(len(concepts))
        ]
        logger.info(f"Similarities to concepts {concepts}: {similarities}")
        self.assertEqual(np.argmax(similarities), 2)  # "blue" is index 2
        
    def test_cross_modal_mapping(self):
        """Test mapping between different modalities in the thought space."""
        # This test assumes integration with image or other modalities
        # For now, we'll simulate with random vectors
        
        # Create text concepts
        text_concepts = ["red square", "blue circle", "green triangle"]
        text_embeds = self.tls.encode_concept_batch(text_concepts)
        
        # Create "visual" concepts (simulated)
        visual_embeds = torch.randn(len(text_concepts), self.dimension, device=self.device)
        visual_embeds = F.normalize(visual_embeds, dim=1)
        
        # Train a cross-modal mapping
        self.tls.train_cross_modal_mapping(text_embeds, visual_embeds, epochs=50)
        
        # Test mapping from text to visual
        mapped_visual = self.tls.map_across_modalities(text_embeds, source_modality='text', target_modality='visual')
        
        # Check shape and normalization
        self.assertEqual(mapped_visual.shape, visual_embeds.shape)
        norms = torch.norm(mapped_visual, dim=1)
        self.assertTrue(torch.all(torch.abs(norms - 1.0) < 1e-5))
        
        # The mapped embeddings should be close to the target visual embeddings
        similarities = [
            F.cosine_similarity(mapped_visual[i].unsqueeze(0), visual_embeds[i].unsqueeze(0)).item()
            for i in range(len(text_concepts))
        ]
        logger.info(f"Cross-modal mapping similarities: {similarities}")
        avg_sim = sum(similarities) / len(similarities)
        self.assertGreater(avg_sim, 0.7)
        
        # Test mapping from visual to text
        mapped_text = self.tls.map_across_modalities(visual_embeds, source_modality='visual', target_modality='text')
        
        # Check shape and normalization
        self.assertEqual(mapped_text.shape, text_embeds.shape)
        norms = torch.norm(mapped_text, dim=1)
        self.assertTrue(torch.all(torch.abs(norms - 1.0) < 1e-5))
        
        # The mapped embeddings should be close to the target text embeddings
        similarities = [
            F.cosine_similarity(mapped_text[i].unsqueeze(0), text_embeds[i].unsqueeze(0)).item()
            for i in range(len(text_concepts))
        ]
        logger.info(f"Reverse cross-modal mapping similarities: {similarities}")
        avg_sim = sum(similarities) / len(similarities)
        self.assertGreater(avg_sim, 0.7)


if __name__ == '__main__':
    unittest.main()