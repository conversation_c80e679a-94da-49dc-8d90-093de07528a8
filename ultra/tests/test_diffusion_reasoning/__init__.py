#!/usr/bin/env python3
"""
Test suite for the Diffusion-Based Reasoning components of the ULTRA architecture.

This module contains comprehensive tests for:
- Conceptual Diffusion Process
- Thought Latent Space
- Reverse Diffusion Reasoning
- Bayesian Uncertainty Quantification
- Probabilistic Inference Engine

The tests validate both the mathematical correctness and the functional integration
of these components into the overall ULTRA architecture.
"""

import unittest
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.distributions import MultivariateNormal, Normal
from scipy.stats import entropy
import pytest
import matplotlib.pyplot as plt
from pathlib import Path
import sys
import os

# Add the ULTRA module to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..')))

from ultra.diffusion_reasoning.conceptual_diffusion import ConceptualDiffusion
from ultra.diffusion_reasoning.thought_latent_space import ThoughtLatentSpace
from ultra.diffusion_reasoning.reverse_diffusion import ReverseDiffusionReasoning
from ultra.diffusion_reasoning.bayesian_uncertainty import BayesianUncertaintyQuantification
from ultra.diffusion_reasoning.probabilistic_inference import ProbabilisticInferenceEngine


class TestConceptualDiffusion(unittest.TestCase):
    """Test suite for the Conceptual Diffusion Process."""
    
    def setUp(self):
        """Set up the test environment."""
        self.concept_dim = 128
        self.batch_size = 16
        self.num_timesteps = 1000
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.diffusion = ConceptualDiffusion(
            concept_dim=self.concept_dim,
            num_timesteps=self.num_timesteps,
            beta_schedule='linear',
            beta_start=1e-4,
            beta_end=0.02,
            device=self.device
        )
        
    def test_initialization(self):
        """Test that the diffusion model initializes correctly."""
        self.assertEqual(self.diffusion.concept_dim, self.concept_dim)
        self.assertEqual(self.diffusion.num_timesteps, self.num_timesteps)
        self.assertEqual(self.diffusion.device, self.device)
        
        # Check that beta schedule is correctly initialized
        self.assertEqual(len(self.diffusion.betas), self.num_timesteps)
        self.assertTrue(torch.all(self.diffusion.betas >= 0))
        self.assertTrue(torch.all(self.diffusion.betas <= 1))
        
        # Check alphas and derived values
        self.assertEqual(len(self.diffusion.alphas), self.num_timesteps)
        self.assertEqual(len(self.diffusion.alphas_cumprod), self.num_timesteps)
        self.assertEqual(len(self.diffusion.alphas_cumprod_prev), self.num_timesteps)
        self.assertEqual(len(self.diffusion.sqrt_alphas_cumprod), self.num_timesteps)
        self.assertEqual(len(self.diffusion.sqrt_one_minus_alphas_cumprod), self.num_timesteps)
        
    def test_forward_diffusion(self):
        """Test the forward diffusion process."""
        # Create an initial concept embedding
        x_0 = torch.randn(self.batch_size, self.concept_dim, device=self.device)
        
        # Test forward diffusion at different timesteps
        for t in [1, 10, 100, self.num_timesteps-1]:
            # Apply forward diffusion
            x_t, noise = self.diffusion.forward_diffusion(x_0, t)
            
            # Check shapes
            self.assertEqual(x_t.shape, x_0.shape)
            self.assertEqual(noise.shape, x_0.shape)
            
            # Check mathematical properties
            alpha_cumprod_t = self.diffusion.alphas_cumprod[t]
            expected_mean = torch.sqrt(alpha_cumprod_t) * x_0
            expected_std = torch.sqrt(1 - alpha_cumprod_t)
            
            # The mean should be close to expected_mean
            torch.testing.assert_close(
                torch.mean((x_t - expected_mean) ** 2), 
                torch.tensor(0.0, device=self.device),
                rtol=1e-1, atol=1e-1
            )
            
            # The standard deviation should be close to expected_std
            batch_std = torch.std(x_t - torch.sqrt(alpha_cumprod_t) * x_0)
            self.assertAlmostEqual(batch_std.item(), expected_std.item(), delta=0.1)
    
    def test_reverse_diffusion_step(self):
        """Test a single step of the reverse diffusion process."""
        # Create a noisy concept at timestep t
        t = 500
        x_t = torch.randn(self.batch_size, self.concept_dim, device=self.device)
        
        # Perform a reverse diffusion step
        x_t_minus_1 = self.diffusion.reverse_diffusion_step(x_t, t)
        
        # Check that the shape is preserved
        self.assertEqual(x_t_minus_1.shape, x_t.shape)
        
        # Check that the output is different from the input (denoising occurred)
        self.assertTrue(torch.any(torch.abs(x_t_minus_1 - x_t) > 1e-5))
        
    def test_sample(self):
        """Test the full sampling process."""
        # Sample from the model
        samples = self.diffusion.sample(self.batch_size)
        
        # Check that the output has the expected shape
        self.assertEqual(samples.shape, (self.batch_size, self.concept_dim))
        
        # Check that different samples are different
        if self.batch_size > 1:
            sample_dists = torch.cdist(samples, samples)
            # Set diagonal to infinity to exclude self-distances
            sample_dists.fill_diagonal_(float('inf'))
            # Check that the minimum distance between any two samples is non-zero
            self.assertTrue(torch.min(sample_dists) > 0)
            
    def test_guided_sampling(self):
        """Test guided sampling towards a specific concept."""
        # Create a target concept embedding
        target = torch.randn(1, self.concept_dim, device=self.device)
        target = F.normalize(target, dim=1)
        
        # Define a guidance function
        def guidance_fn(x, t, target=target):
            # Simple guidance towards the target
            return 0.1 * (target - x)
        
        # Sample with guidance
        guided_samples = self.diffusion.sample(
            self.batch_size,
            guidance_fn=guidance_fn,
            guidance_strength=0.5
        )
        
        # Sample without guidance
        unguided_samples = self.diffusion.sample(self.batch_size)
        
        # Check that guided samples are closer to the target than unguided samples
        guided_dist = torch.mean(torch.norm(guided_samples - target, dim=1))
        unguided_dist = torch.mean(torch.norm(unguided_samples - target, dim=1))
        
        self.assertLess(guided_dist, unguided_dist)
        
    def test_compute_loss(self):
        """Test the loss computation for training."""
        # Create an initial concept embedding
        x_0 = torch.randn(self.batch_size, self.concept_dim, device=self.device)
        
        # Compute loss
        loss = self.diffusion.compute_loss(x_0)
        
        # Check that the loss is a scalar
        self.assertEqual(loss.shape, torch.Size([]))
        
        # Check that the loss is positive
        self.assertTrue(loss > 0)
        
    def test_consistency(self):
        """Test that forward followed by reverse diffusion is consistent."""
        # This is a more comprehensive test that simulates the complete process
        # Create an initial concept embedding
        x_0 = torch.randn(self.batch_size, self.concept_dim, device=self.device)
        
        # Apply forward diffusion to get a noisy concept
        t = 100
        x_t, noise = self.diffusion.forward_diffusion(x_0, t)
        
        # Apply multiple reverse diffusion steps to denoise back to t=0
        x_recovered = x_t.clone()
        for t_step in range(t, 0, -1):
            x_recovered = self.diffusion.reverse_diffusion_step(x_recovered, t_step)
        
        # Check that the recovered concept is close to the original (within reasonable bounds)
        mse = torch.mean((x_recovered - x_0) ** 2)
        self.assertLess(mse.item(), 0.5)  # This threshold might need adjustment


class TestThoughtLatentSpace(unittest.TestCase):
    """Test suite for the Thought Latent Space."""
    
    def setUp(self):
        """Set up the test environment."""
        self.dimension = 256
        self.num_levels = 3
        self.num_concepts = 100
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.thought_space = ThoughtLatentSpace(
            dimension=self.dimension,
            num_levels=self.num_levels,
            num_clusters=20,
            device=self.device
        )
        
        # Create some sample concepts for testing
        self.sample_concepts = torch.randn(self.num_concepts, self.dimension, device=self.device)
        self.sample_concepts = F.normalize(self.sample_concepts, dim=1)
        
    def test_initialization(self):
        """Test that the thought latent space initializes correctly."""
        self.assertEqual(self.thought_space.dimension, self.dimension)
        self.assertEqual(self.thought_space.num_levels, self.num_levels)
        self.assertEqual(self.thought_space.device, self.device)
        
        # Check that cluster centers are initialized
        self.assertEqual(self.thought_space.cluster_centers.shape, 
                         (self.thought_space.num_clusters, self.dimension))
        
        # Check that the hierarchical structure is initialized
        self.assertEqual(len(self.thought_space.level_projections), self.num_levels)
        for i in range(self.num_levels):
            self.assertIsInstance(self.thought_space.level_projections[i], nn.Module)
            
    def test_encode_concept(self):
        """Test concept encoding functionality."""
        # Test string-based encoding
        concept_description = "A test concept description"
        concept_embedding = self.thought_space.encode_concept(concept_description)
        
        # Check shape
        self.assertEqual(concept_embedding.shape, (self.dimension,))
        
        # Check normalization
        self.assertAlmostEqual(torch.norm(concept_embedding).item(), 1.0, delta=1e-5)
        
        # Test vector-based encoding
        concept_vector = torch.randn(self.dimension, device=self.device)
        concept_embedding = self.thought_space.encode_concept(concept_vector)
        
        # Check shape
        self.assertEqual(concept_embedding.shape, (self.dimension,))
        
        # Check normalization
        self.assertAlmostEqual(torch.norm(concept_embedding).item(), 1.0, delta=1e-5)
        
    def test_hierarchical_encoding(self):
        """Test hierarchical encoding of concepts."""
        # Encode a concept at different abstraction levels
        concept_description = "A test concept for hierarchical encoding"
        
        # Encode at all levels
        hierarchical_embeddings = self.thought_space.hierarchical_encode(concept_description)
        
        # Check that we have embeddings for each level
        self.assertEqual(len(hierarchical_embeddings), self.num_levels)
        
        # Check shapes and normalization
        for level_embedding in hierarchical_embeddings:
            self.assertEqual(level_embedding.shape, (self.dimension,))
            self.assertAlmostEqual(torch.norm(level_embedding).item(), 1.0, delta=1e-5)
            
    def test_find_nearest_concepts(self):
        """Test finding nearest concepts in the thought space."""
        # Create a query concept
        query = torch.randn(self.dimension, device=self.device)
        query = F.normalize(query, dim=0)
        
        # Find nearest concepts
        k = 5
        indices, distances = self.thought_space.find_nearest_concepts(
            query, self.sample_concepts, k=k
        )
        
        # Check that we get k indices and distances
        self.assertEqual(len(indices), k)
        self.assertEqual(len(distances), k)
        
        # Check that distances are sorted in ascending order
        self.assertTrue(torch.all(distances[:-1] <= distances[1:]))
        
        # Check that the distances make sense (should be between 0 and 2 for normalized vectors)
        self.assertTrue(torch.all(distances >= 0))
        self.assertTrue(torch.all(distances <= 2))
        
    def test_vector_operations(self):
        """Test vector operations in the thought space."""
        # Create three concept vectors for analogy testing: A is to B as C is to ?
        a = F.normalize(torch.randn(self.dimension, device=self.device), dim=0)
        b = F.normalize(torch.randn(self.dimension, device=self.device), dim=0)
        c = F.normalize(torch.randn(self.dimension, device=self.device), dim=0)
        
        # Test analogy operation
        result = self.thought_space.vector_operation(a, b, c, operation_type='analogy')
        
        # Check shape and normalization
        self.assertEqual(result.shape, (self.dimension,))
        self.assertAlmostEqual(torch.norm(result).item(), 1.0, delta=1e-5)
        
        # Check that the result is different from the inputs
        self.assertTrue(torch.norm(result - a) > 0.1)
        self.assertTrue(torch.norm(result - b) > 0.1)
        self.assertTrue(torch.norm(result - c) > 0.1)
        
        # Test other operations
        # Composition
        result = self.thought_space.vector_operation(a, b, None, operation_type='composition')
        self.assertEqual(result.shape, (self.dimension,))
        self.assertAlmostEqual(torch.norm(result).item(), 1.0, delta=1e-5)
        
        # Negation
        result = self.thought_space.vector_operation(a, None, None, operation_type='negation')
        self.assertEqual(result.shape, (self.dimension,))
        self.assertAlmostEqual(torch.norm(result).item(), 1.0, delta=1e-5)
        
    def test_concept_traversal(self):
        """Test traversing between concepts in the thought space."""
        # Create start and end concepts
        start = F.normalize(torch.randn(self.dimension, device=self.device), dim=0)
        end = F.normalize(torch.randn(self.dimension, device=self.device), dim=0)
        
        # Generate intermediate steps
        num_steps = 10
        trajectory = self.thought_space.traverse(start, end, num_steps)
        
        # Check that we get the right number of steps
        self.assertEqual(len(trajectory), num_steps)
        
        # Check shapes and normalization
        for step in trajectory:
            self.assertEqual(step.shape, (self.dimension,))
            self.assertAlmostEqual(torch.norm(step).item(), 1.0, delta=1e-5)
            
        # Check that the first and last steps are close to start and end
        self.assertLess(torch.norm(trajectory[0] - start).item(), 0.1)
        self.assertLess(torch.norm(trajectory[-1] - end).item(), 0.1)
        
        # Check that steps are progressively closer to the end
        distances_to_end = [torch.norm(step - end).item() for step in trajectory]
        for i in range(len(distances_to_end) - 1):
            self.assertGreaterEqual(distances_to_end[i], distances_to_end[i+1])
            
    def test_semantic_similarity(self):
        """Test semantic similarity measurement in the thought space."""
        # Create pairs of concepts with varying similarity
        a = F.normalize(torch.randn(self.dimension, device=self.device), dim=0)
        
        # Similar concept (small random perturbation)
        similar = a + 0.1 * torch.randn(self.dimension, device=self.device)
        similar = F.normalize(similar, dim=0)
        
        # Different concept
        different = F.normalize(torch.randn(self.dimension, device=self.device), dim=0)
        
        # Somewhat similar concept (interpolation)
        somewhat = 0.7 * a + 0.3 * different
        somewhat = F.normalize(somewhat, dim=0)
        
        # Compute similarities
        sim_similar = self.thought_space.semantic_similarity(a, similar)
        sim_different = self.thought_space.semantic_similarity(a, different)
        sim_somewhat = self.thought_space.semantic_similarity(a, somewhat)
        
        # Check that similarities are in [0, 1]
        self.assertTrue(0 <= sim_similar <= 1)
        self.assertTrue(0 <= sim_different <= 1)
        self.assertTrue(0 <= sim_somewhat <= 1)
        
        # Check that similar concepts have higher similarity
        self.assertGreater(sim_similar, sim_somewhat)
        self.assertGreater(sim_somewhat, sim_different)
        
    def test_relation_extraction(self):
        """Test extraction of relations between concepts."""
        # Create pairs of related concepts
        pairs = []
        relations = []
        
        for _ in range(10):
            # Create a random relation vector
            relation = F.normalize(torch.randn(self.dimension, device=self.device), dim=0)
            relations.append(relation)
            
            # Create a pair of concepts related by this relation
            a = F.normalize(torch.randn(self.dimension, device=self.device), dim=0)
            b = F.normalize(a + 0.5 * relation, dim=0)
            pairs.append((a, b))
            
        # Extract relations from pairs
        extracted_relations = [
            self.thought_space.extract_relation(a, b) for a, b in pairs
        ]
        
        # Check shapes and normalization
        for relation in extracted_relations:
            self.assertEqual(relation.shape, (self.dimension,))
            self.assertAlmostEqual(torch.norm(relation).item(), 1.0, delta=1e-5)
            
        # Check that extracted relations are close to the original relations
        for i in range(len(relations)):
            similarity = torch.dot(relations[i], extracted_relations[i])
            # Due to normalization and the way we constructed b, the similarity won't be perfect
            self.assertGreater(similarity.item(), 0.7)


class TestReverseDiffusionReasoning(unittest.TestCase):
    """Test suite for the Reverse Diffusion Reasoning component."""
    
    def setUp(self):
        """Set up the test environment."""
        self.concept_dim = 128
        self.batch_size = 8
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # Initialize the diffusion model
        self.diffusion = ConceptualDiffusion(
            concept_dim=self.concept_dim,
            num_timesteps=1000,
            beta_schedule='linear',
            beta_start=1e-4,
            beta_end=0.02,
            device=self.device
        )
        
        # Initialize the thought space
        self.thought_space = ThoughtLatentSpace(
            dimension=self.concept_dim,
            num_levels=3,
            num_clusters=10,
            device=self.device
        )
        
        # Initialize the reverse diffusion reasoning component
        self.reverse_diffusion = ReverseDiffusionReasoning(
            diffusion_model=self.diffusion,
            thought_space=self.thought_space,
            device=self.device
        )
        
        # Create sample start and goal states
        self.start_state = F.normalize(torch.randn(self.concept_dim, device=self.device), dim=0)
        self.goal_state = F.normalize(torch.randn(self.concept_dim, device=self.device), dim=0)
        
    def test_initialization(self):
        """Test that the reverse diffusion reasoning initializes correctly."""
        self.assertEqual(self.reverse_diffusion.diffusion_model, self.diffusion)
        self.assertEqual(self.reverse_diffusion.thought_space, self.thought_space)
        self.assertEqual(self.reverse_diffusion.device, self.device)
        
    def test_goal_directed_reasoning(self):
        """Test goal-directed reasoning from start to goal state."""
        # Generate a reasoning path
        num_steps = 5
        reasoning_steps = self.reverse_diffusion.goal_directed_reasoning(
            self.start_state, self.goal_state, num_steps=num_steps
        )
        
        # Check that we get the right number of steps
        self.assertEqual(len(reasoning_steps), num_steps)
        
        # Check shapes
        for step in reasoning_steps:
            self.assertEqual(step.shape, (self.concept_dim,))
            
        # Check that the path starts near the start state and ends near the goal state
        start_distance = torch.norm(reasoning_steps[0] - self.start_state)
        goal_distance = torch.norm(reasoning_steps[-1] - self.goal_state)
        
        self.assertLess(start_distance.item(), 1.0)
        self.assertLess(goal_distance.item(), 1.0)
        
        # Check that each step moves closer to the goal
        goal_distances = [torch.norm(step - self.goal_state).item() for step in reasoning_steps]
        for i in range(len(goal_distances) - 1):
            self.assertGreaterEqual(goal_distances[i], goal_distances[i+1])
            
    def test_constrained_reasoning(self):
        """Test reasoning with constraints."""
        # Define constraints
        constraints = [
            # Constraint 1: Similar to concept A
            {
                'type': 'similarity',
                'concept': F.normalize(torch.randn(self.concept_dim, device=self.device), dim=0),
                'weight': 0.5
            },
            # Constraint 2: Different from concept B
            {
                'type': 'dissimilarity',
                'concept': F.normalize(torch.randn(self.concept_dim, device=self.device), dim=0),
                'weight': 0.3
            }
        ]
        
        # Generate reasoning paths with and without constraints
        num_steps = 5
        constrained_steps = self.reverse_diffusion.constrained_reasoning(
            self.start_state, self.goal_state, constraints, num_steps=num_steps
        )
        unconstrained_steps = self.reverse_diffusion.goal_directed_reasoning(
            self.start_state, self.goal_state, num_steps=num_steps
        )
        
        # Check that we get the right number of steps
        self.assertEqual(len(constrained_steps), num_steps)
        
        # Check that constrained and unconstrained paths are different
        differences = [
            torch.norm(c - u).item() 
            for c, u in zip(constrained_steps, unconstrained_steps)
        ]
        avg_difference = sum(differences) / len(differences)
        self.assertGreater(avg_difference, 0.1)
        
        # Check that constrained path respects the constraints
        similar_concept = constraints[0]['concept']
        different_concept = constraints[1]['concept']
        
        # For the similarity constraint, the path should be closer to similar_concept
        # compared to the unconstrained path
        constrained_similarities = [
            torch.dot(step, similar_concept).item() for step in constrained_steps
        ]
        unconstrained_similarities = [
            torch.dot(step, similar_concept).item() for step in unconstrained_steps
        ]
        avg_constrained_sim = sum(constrained_similarities) / len(constrained_similarities)
        avg_unconstrained_sim = sum(unconstrained_similarities) / len(unconstrained_similarities)
        self.assertGreater(avg_constrained_sim, avg_unconstrained_sim)
        
        # For the dissimilarity constraint, the path should be further from different_concept
        # compared to the unconstrained path
        constrained_diffs = [
            torch.dot(step, different_concept).item() for step in constrained_steps
        ]
        unconstrained_diffs = [
            torch.dot(step, different_concept).item() for step in unconstrained_steps
        ]
        avg_constrained_diff = sum(constrained_diffs) / len(constrained_diffs)
        avg_unconstrained_diff = sum(unconstrained_diffs) / len(unconstrained_diffs)
        self.assertLess(avg_constrained_diff, avg_unconstrained_diff)
        
    def test_multi_goal_reasoning(self):
        """Test reasoning with multiple goal states."""
        # Create multiple goal states
        num_goals = 3
        goal_states = [
            F.normalize(torch.randn(self.concept_dim, device=self.device), dim=0)
            for _ in range(num_goals)
        ]
        
        # Assign weights to goals
        goal_weights = F.softmax(torch.tensor([1.0, 0.5, 0.2], device=self.device), dim=0)
        
        # Generate reasoning path
        num_steps = 5
        reasoning_steps = self.reverse_diffusion.multi_goal_reasoning(
            self.start_state, goal_states, goal_weights, num_steps=num_steps
        )
        
        # Check that we get the right number of steps
        self.assertEqual(len(reasoning_steps), num_steps)
        
        # Check shapes
        for step in reasoning_steps:
            self.assertEqual(step.shape, (self.concept_dim,))
            
        # Check that the final step is closest to the highest-weighted goal
        final_step = reasoning_steps[-1]
        distances = [torch.norm(final_step - goal).item() for goal in goal_states]
        closest_goal_idx = np.argmin(distances)
        self.assertEqual(closest_goal_idx, 0)  # The highest-weighted goal
        
    def test_reasoning_quality(self):
        """Test the quality of the reasoning process."""
        # Generate reasoning steps
        num_steps = 5
        reasoning_steps = self.reverse_diffusion.goal_directed_reasoning(
            self.start_state, self.goal_state, num_steps=num_steps
        )
        
        # Evaluate the quality of reasoning
        coherence = self.reverse_diffusion.evaluate_reasoning_quality(
            reasoning_steps, metric='coherence'
        )
        directness = self.reverse_diffusion.evaluate_reasoning_quality(
            reasoning_steps, metric='directness'
        )
        
        # Check that quality metrics are in a reasonable range
        self.assertTrue(0 <= coherence <= 1)
        self.assertTrue(0 <= directness <= 1)
        
    def test_collaborative_reasoning(self):
        """Test collaborative reasoning with multiple reasoning agents."""
        # Create multiple reasoning agents
        num_agents = 2
        agents = [self.reverse_diffusion] + [
            ReverseDiffusionReasoning(
                diffusion_model=self.diffusion,
                thought_space=self.thought_space,
                device=self.device
            )
            for _ in range(num_agents - 1)
        ]
        
        # Generate collaborative reasoning path
        num_steps = 5
        collaborative_steps = self.reverse_diffusion.collaborative_reasoning(
            agents, self.start_state, self.goal_state, num_steps=num_steps
        )
        
        # Generate individual reasoning paths
        individual_paths = [
            agent.goal_directed_reasoning(
                self.start_state, self.goal_state, num_steps=num_steps
            )
            for agent in agents
        ]
        
        # Check that we get the right number of steps
        self.assertEqual(len(collaborative_steps), num_steps)
        
        # Check that collaborative reasoning is different from individual reasoning
        for individual_path in individual_paths:
            differences = [
                torch.norm(c - i).item() 
                for c, i in zip(collaborative_steps, individual_path)
            ]
            avg_difference = sum(differences) / len(differences)
            self.assertGreater(avg_difference, 0.05)


class TestBayesianUncertaintyQuantification(unittest.TestCase):
    """Test suite for the Bayesian Uncertainty Quantification component."""
    
    def setUp(self):
        """Set up the test environment."""
        self.thought_space_dim = 64
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.uncertainty = BayesianUncertaintyQuantification(
            thought_space_dim=self.thought_space_dim,
            device=self.device
        )
        
    def test_initialization(self):
        """Test that the uncertainty quantification initializes correctly."""
        self.assertEqual(self.uncertainty.thought_space_dim, self.thought_space_dim)
        self.assertEqual(self.uncertainty.device, self.device)
        
        # Check prior initialization
        self.assertEqual(self.uncertainty.prior_mean.shape, (self.thought_space_dim,))
        self.assertEqual(self.uncertainty.prior_cov.shape, (self.thought_space_dim, self.thought_space_dim))
        
    def test_update_belief(self):
        """Test updating belief with new evidence."""
        # Create a current belief
        current_mean = torch.zeros(self.thought_space_dim, device=self.device)
        current_cov = torch.eye(self.thought_space_dim, device=self.device)
        current_belief = (current_mean, current_cov)
        
        # Create new evidence
        evidence_mean = torch.ones(self.thought_space_dim, device=self.device)
        evidence_cov = 2 * torch.eye(self.thought_space_dim, device=self.device)
        new_evidence = (evidence_mean, evidence_cov)
        
        # Update belief
        new_belief = self.uncertainty.update_belief(current_belief, new_evidence)
        new_mean, new_cov = new_belief
        
        # Check shapes
        self.assertEqual(new_mean.shape, (self.thought_space_dim,))
        self.assertEqual(new_cov.shape, (self.thought_space_dim, self.thought_space_dim))
        
        # Check that the update is correct
        # In Bayesian update, the posterior mean should be between the prior mean and the evidence mean
        self.assertTrue(torch.all(new_mean >= torch.min(current_mean, evidence_mean)))
        self.assertTrue(torch.all(new_mean <= torch.max(current_mean, evidence_mean)))
        
        # The posterior covariance should be smaller than both prior and evidence covariance
        self.assertTrue(torch.all(torch.diag(new_cov) <= torch.diag(current_cov)))
        self.assertTrue(torch.all(torch.diag(new_cov) <= torch.diag(evidence_cov)))
        
    def test_get_uncertainty(self):
        """Test uncertainty calculation."""
        # Create a belief with known uncertainty
        mean = torch.zeros(self.thought_space_dim, device=self.device)
        # Create a diagonal covariance with varying values
        diag_values = torch.linspace(0.1, 1.0, self.thought_space_dim, device=self.device)
        cov = torch.diag(diag_values)
        belief = (mean, cov)
        
        # Calculate uncertainty
        uncertainty = self.uncertainty.get_uncertainty(belief)
        
        # Check that it's a scalar
        self.assertEqual(uncertainty.shape, torch.Size([]))
        
        # Check that it's positive
        self.assertTrue(uncertainty > 0)
        
        # The uncertainty should be the trace of the covariance matrix
        expected_uncertainty = torch.trace(cov)
        self.assertAlmostEqual(uncertainty.item(), expected_uncertainty.item(), delta=1e-5)
        
    def test_sample_from_belief(self):
        """Test sampling from a belief distribution."""
        # Create a belief
        mean = torch.ones(self.thought_space_dim, device=self.device)
        cov = 0.1 * torch.eye(self.thought_space_dim, device=self.device)
        belief = (mean, cov)
        
        # Sample from the belief
        n_samples = 100
        samples = self.uncertainty.sample_from_belief(belief, n_samples=n_samples)
        
        # Check shape
        self.assertEqual(samples.shape, (n_samples, self.thought_space_dim))
        
        # Check that samples are distributed around the mean
        sample_mean = torch.mean(samples, dim=0)
        sample_std = torch.std(samples, dim=0)
        
        # Mean should be close to the belief mean
        torch.testing.assert_close(
            sample_mean, mean, rtol=0.2, atol=0.2
        )
        
        # Standard deviation should be close to the square root of the diagonal of the covariance
        expected_std = torch.sqrt(torch.diag(cov))
        torch.testing.assert_close(
            sample_std, expected_std, rtol=0.3, atol=0.3
        )
        
    def test_compute_expected_utility(self):
        """Test computation of expected utility for decisions."""
        # Create a belief
        mean = torch.zeros(self.thought_space_dim, device=self.device)
        cov = torch.eye(self.thought_space_dim, device=self.device)
        belief = (mean, cov)
        
        # Create a set of actions
        n_actions = 5
        actions = [
            torch.randn(self.thought_space_dim, device=self.device)
            for _ in range(n_actions)
        ]
        
        # Define a utility function
        def utility_fn(state, action):
            return -torch.norm(state - action) ** 2
        
        # Compute expected utilities
        utilities = self.uncertainty.compute_expected_utility(belief, actions, utility_fn)
        
        # Check shape
        self.assertEqual(len(utilities), n_actions)
        
        # Check that utilities are finite
        for utility in utilities:
            self.assertFalse(torch.isinf(utility))
            self.assertFalse(torch.isnan(utility))
            
    def test_optimal_action(self):
        """Test selection of the optimal action."""
        # Create a belief centered at a specific point
        target = torch.ones(self.thought_space_dim, device=self.device)
        mean = target.clone()
        cov = 0.1 * torch.eye(self.thought_space_dim, device=self.device)
        belief = (mean, cov)
        
        # Create a set of actions, one of which is very close to the target
        n_actions = 5
        actions = [
            torch.randn(self.thought_space_dim, device=self.device)
            for _ in range(n_actions - 1)
        ]
        # Add an action very close to the target
        actions.append(target + 0.01 * torch.randn(self.thought_space_dim, device=self.device))
        
        # Define a utility function that rewards proximity to the target
        def utility_fn(state, action):
            return -torch.norm(state - action) ** 2
        
        # Select the optimal action
        optimal_idx, optimal_utility = self.uncertainty.optimal_action(
            belief, actions, utility_fn
        )
        
        # Check that the optimal action is the one closest to the target
        self.assertEqual(optimal_idx, n_actions - 1)
        
        # Check that the optimal utility is finite
        self.assertFalse(torch.isinf(optimal_utility))
        self.assertFalse(torch.isnan(optimal_utility))
        
    def test_information_gain(self):
        """Test computation of information gain."""
        # Create a belief
        mean = torch.zeros(self.thought_space_dim, device=self.device)
        cov = torch.eye(self.thought_space_dim, device=self.device)
        belief = (mean, cov)
        
        # Create a set of queries that would provide information
        n_queries = 3
        queries = [
            {
                'dimension': i,
                'precision': 10.0  # High precision means low uncertainty
            }
            for i in range(n_queries)
        ]
        
        # Compute information gain for each query
        info_gains = self.uncertainty.compute_information_gain(belief, queries)
        
        # Check shape
        self.assertEqual(len(info_gains), n_queries)
        
        # Check that information gains are positive
        for gain in info_gains:
            self.assertTrue(gain > 0)
            
        # All queries have the same precision, so they should have the same information gain
        for i in range(1, n_queries):
            self.assertAlmostEqual(info_gains[0].item(), info_gains[i].item(), delta=1e-5)
            
    def test_epistemic_vs_aleatoric(self):
        """Test distinguishing between epistemic and aleatoric uncertainty."""
        # Create a prediction with both types of uncertainty
        x = torch.linspace(-3, 3, 100, device=self.device).unsqueeze(1)
        
        # True function: f(x) = x^2
        true_fn = lambda x: x**2
        
        # Add noise to create training data
        y_true = true_fn(x)
        noise = 0.2 * torch.randn(x.shape[0], 1, device=self.device)
        y_noisy = y_true + noise
        
        # Train a simple model with uncertainty estimation
        model = self.uncertainty.train_example_model(x, y_noisy)
        
        # Make predictions with uncertainty
        test_x = torch.linspace(-5, 5, 200, device=self.device).unsqueeze(1)
        pred_mean, pred_epistemic, pred_aleatoric = self.uncertainty.predict_with_uncertainty(
            model, test_x
        )
        
        # Check shapes
        self.assertEqual(pred_mean.shape, (200, 1))
        self.assertEqual(pred_epistemic.shape, (200, 1))
        self.assertEqual(pred_aleatoric.shape, (200, 1))
        
        # Epistemic uncertainty should be higher in regions far from training data
        close_mask = (test_x >= -3) & (test_x <= 3)
        far_mask = ~close_mask
        
        close_epistemic = pred_epistemic[close_mask].mean()
        far_epistemic = pred_epistemic[far_mask].mean()
        
        self.assertLess(close_epistemic.item(), far_epistemic.item())
        
        # Aleatoric uncertainty should be relatively constant
        close_aleatoric = pred_aleatoric[close_mask].mean()
        far_aleatoric = pred_aleatoric[far_mask].mean()
        
        # The difference in aleatoric uncertainty should be smaller than the difference in epistemic
        aleatoric_diff = abs(close_aleatoric.item() - far_aleatoric.item())
        epistemic_diff = abs(close_epistemic.item() - far_epistemic.item())
        
        self.assertLess(aleatoric_diff, epistemic_diff)


class TestProbabilisticInferenceEngine(unittest.TestCase):
    """Test suite for the Probabilistic Inference Engine."""
    
    def setUp(self):
        """Set up the test environment."""
        self.thought_space_dim = 64
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # Initialize thought space
        self.thought_space = ThoughtLatentSpace(
            dimension=self.thought_space_dim,
            num_levels=2,
            num_clusters=10,
            device=self.device
        )
        
        # Initialize inference engine
        self.inference_engine = ProbabilisticInferenceEngine(
            thought_space=self.thought_space,
            device=self.device
        )
        
    def test_initialization(self):
        """Test that the inference engine initializes correctly."""
        self.assertEqual(self.inference_engine.thought_space, self.thought_space)
        self.assertEqual(self.inference_engine.device, self.device)
        
    def test_infer_concept(self):
        """Test inference of a concept given constraints."""
        # Define some constraints
        constraints = [
            {
                'concept': F.normalize(torch.randn(self.thought_space_dim, device=self.device), dim=0),
                'relation': 'similar',
                'strength': 0.8
            },
            {
                'concept': F.normalize(torch.randn(self.thought_space_dim, device=self.device), dim=0),
                'relation': 'different',
                'strength': 0.6
            }
        ]
        
        # Infer a concept
        num_samples = 50
        inferred_concept, confidence = self.inference_engine.infer_concept(
            constraints, num_samples=num_samples
        )
        
        # Check shape and normalization
        self.assertEqual(inferred_concept.shape, (self.thought_space_dim,))
        self.assertAlmostEqual(torch.norm(inferred_concept).item(), 1.0, delta=1e-5)
        
        # Check that confidence is in [0, 1]
        self.assertTrue(0 <= confidence <= 1)
        
        # Check that the inferred concept respects the constraints
        similar_to = constraints[0]['concept']
        different_from = constraints[1]['concept']
        
        similarity = torch.dot(inferred_concept, similar_to)
        difference = 1 - torch.dot(inferred_concept, different_from)
        
        # For the 'similar' constraint, similarity should be high
        self.assertGreater(similarity.item(), 0.5)
        
        # For the 'different' constraint, difference should be high
        self.assertGreater(difference.item(), 0.5)
        
    def test_probabilistic_composition(self):
        """Test probabilistic composition of concepts."""
        # Create base concepts
        concepts = [
            F.normalize(torch.randn(self.thought_space_dim, device=self.device), dim=0)
            for _ in range(3)
        ]
        
        # Define weights for composition
        weights = torch.tensor([0.5, 0.3, 0.2], device=self.device)
        
        # Perform composition
        composed, uncertainty = self.inference_engine.probabilistic_composition(
            concepts, weights
        )
        
        # Check shape and normalization
        self.assertEqual(composed.shape, (self.thought_space_dim,))
        self.assertAlmostEqual(torch.norm(composed).item(), 1.0, delta=1e-5)
        
        # Check that uncertainty is a scalar
        self.assertEqual(uncertainty.shape, torch.Size([]))
        
        # Check that uncertainty is non-negative
        self.assertGreaterEqual(uncertainty.item(), 0)
        
        # The composed concept should be a weighted combination of the base concepts
        # (after normalization), so it should be more similar to concepts with higher weights
        similarities = [torch.dot(composed, concept).item() for concept in concepts]
        # Check that the most similar concept is the one with the highest weight
        self.assertEqual(np.argmax(similarities), 0)
        
    def test_relation_based_inference(self):
        """Test inference based on analogical relations."""
        # Create concepts for analogy: A is to B as C is to ?
        a = F.normalize(torch.randn(self.thought_space_dim, device=self.device), dim=0)
        b = F.normalize(torch.randn(self.thought_space_dim, device=self.device), dim=0)
        c = F.normalize(torch.randn(self.thought_space_dim, device=self.device), dim=0)
        
        # Extract relation
        relation = self.thought_space.extract_relation(a, b)
        
        # Infer the fourth concept
        d, confidence = self.inference_engine.relation_based_inference(c, relation)
        
        # Check shape and normalization
        self.assertEqual(d.shape, (self.thought_space_dim,))
        self.assertAlmostEqual(torch.norm(d).item(), 1.0, delta=1e-5)
        
        # Check that confidence is in [0, 1]
        self.assertTrue(0 <= confidence <= 1)
        
        # Check that the relation between C and D is similar to the relation between A and B
        relation_cd = self.thought_space.extract_relation(c, d)
        similarity = torch.dot(relation, relation_cd)
        
        # The similarity should be high
        self.assertGreater(similarity.item(), 0.8)
        
    def test_bayesian_inference(self):
        """Test Bayesian inference given evidence."""
        # Define a prior distribution
        prior_mean = torch.zeros(self.thought_space_dim, device=self.device)
        prior_cov = torch.eye(self.thought_space_dim, device=self.device)
        prior = MultivariateNormal(prior_mean, prior_cov)
        
        # Define evidence
        evidence = {
            'dimension': 0,  # The first dimension
            'value': 1.0,    # Has value 1.0
            'precision': 2.0  # With precision 2.0
        }
        
        # Perform Bayesian inference
        posterior_mean, posterior_cov = self.inference_engine.bayesian_inference(
            prior, [evidence]
        )
        
        # Check shapes
        self.assertEqual(posterior_mean.shape, (self.thought_space_dim,))
        self.assertEqual(posterior_cov.shape, (self.thought_space_dim, self.thought_space_dim))
        
        # The posterior mean should have been updated in the evidence dimension
        self.assertGreater(posterior_mean[0].item(), 0)
        
        # The posterior variance in the evidence dimension should be smaller than the prior
        self.assertLess(posterior_cov[0, 0].item(), prior_cov[0, 0].item())
        
        # The posterior covariance should still be symmetric and positive definite
        # Check symmetry
        diff = posterior_cov - posterior_cov.T
        self.assertLess(torch.max(torch.abs(diff)).item(), 1e-5)
        
        # Check positive definiteness (all eigenvalues should be positive)
        eigvals = torch.linalg.eigvalsh(posterior_cov)
        self.assertTrue(torch.all(eigvals > 0))
        
    def test_joint_inference(self):
        """Test joint inference over multiple variables."""
        # Define variables and their domains
        variables = ['A', 'B', 'C']
        domains = {
            'A': torch.tensor([0, 1], device=self.device),
            'B': torch.tensor([0, 1, 2], device=self.device),
            'C': torch.tensor([0, 1], device=self.device)
        }
        
        # Define a joint distribution as a simple tensor
        joint_probs = torch.ones(2, 3, 2, device=self.device)
        joint_probs = joint_probs / joint_probs.sum()  # Normalize
        
        # Define conditional dependencies
        conditionals = {
            'B|A': torch.tensor([
                [0.2, 0.3, 0.5],  # P(B|A=0)
                [0.5, 0.3, 0.2]   # P(B|A=1)
            ], device=self.device),
            'C|B': torch.tensor([
                [0.7, 0.3],  # P(C|B=0)
                [0.5, 0.5],  # P(C|B=1)
                [0.3, 0.7]   # P(C|B=2)
            ], device=self.device)
        }
        
        # Define evidence
        evidence = {'A': 1}  # A = 1
        
        # Perform inference to compute P(B, C | A=1)
        posterior = self.inference_engine.joint_inference(
            variables, domains, joint_probs, conditionals, evidence
        )
        
        # Check shape
        self.assertEqual(posterior.shape, (3, 2))  # 3 values for B, 2 values for C
        
        # Check that the posterior sums to 1
        self.assertAlmostEqual(posterior.sum().item(), 1.0, delta=1e-5)
        
        # The posterior should be consistent with the conditionals
        # P(B|A=1) * P(C|B)
        expected = torch.zeros(3, 2, device=self.device)
        for b in range(3):
            for c in range(2):
                expected[b, c] = conditionals['B|A'][1, b] * conditionals['C|B'][b, c]
        expected = expected / expected.sum()  # Normalize
        
        torch.testing.assert_close(posterior, expected, rtol=1e-4, atol=1e-4)
        
    def test_monte_carlo_inference(self):
        """Test inference using Monte Carlo sampling."""
        # Define a target distribution
        # A mixture of two Gaussians in the thought space
        mixture_weights = torch.tensor([0.7, 0.3], device=self.device)
        means = [
            torch.ones(self.thought_space_dim, device=self.device),
            -torch.ones(self.thought_space_dim, device=self.device)
        ]
        covs = [
            0.1 * torch.eye(self.thought_space_dim, device=self.device),
            0.2 * torch.eye(self.thought_space_dim, device=self.device)
        ]
        
        def log_prob_fn(x):
            # Log probability of mixture of Gaussians
            log_probs = torch.zeros(x.shape[0], 2, device=self.device)
            for i in range(2):
                dist = MultivariateNormal(means[i], covs[i])
                log_probs[:, i] = dist.log_prob(x) + torch.log(mixture_weights[i])
            return torch.logsumexp(log_probs, dim=1)
        
        # Perform Monte Carlo inference
        num_samples = 1000
        samples, log_weights = self.inference_engine.monte_carlo_inference(
            log_prob_fn, num_samples=num_samples
        )
        
        # Check shape
        self.assertEqual(samples.shape, (num_samples, self.thought_space_dim))
        self.assertEqual(log_weights.shape, (num_samples,))
        
        # Check that log weights sum to 1 after normalization
        normalized_weights = torch.exp(log_weights - torch.logsumexp(log_weights, dim=0))
        self.assertAlmostEqual(normalized_weights.sum().item(), 1.0, delta=1e-5)
        
        # Compute mean of samples using importance weights
        weighted_mean = torch.sum(
            samples * normalized_weights.unsqueeze(1), dim=0
        )
        
        # The weighted mean should be closer to the first Gaussian (which has higher weight)
        dist_to_first = torch.norm(weighted_mean - means[0])
        dist_to_second = torch.norm(weighted_mean - means[1])
        self.assertLess(dist_to_first.item(), dist_to_second.item())


if __name__ == '__main__':
    unittest.main()