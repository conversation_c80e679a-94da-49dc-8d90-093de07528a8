"""
ULTRA Emergent Consciousness Lattice Test Suite
==================================================

Comprehensive test suite for the Emergent Consciousness Lattice subsystem,
implementing full mathematical models and production-grade testing for:
- Self-Awareness Module
- Intentionality System  
- Integrated Information Matrix
- Attentional Awareness
- Global Workspace

Based on mathematical formulations from ULTRA documentation and implementing
Integrated Information Theory, Global Workspace Theory, and advanced 
metacognitive architectures.
"""

import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.distributions import MultivariateNormal, Categorical
import pytest
import logging
import time
import uuid
from typing import Dict, List, Tuple, Optional, Any, Union
from dataclasses import dataclass, field
from collections import defaultdict, deque
import networkx as nx
from scipy.special import entropy
from scipy.stats import pearsonr
from scipy.linalg import pinv, det
from sklearn.metrics import mutual_info_score
from sklearn.preprocessing import StandardScaler
import matplotlib.pyplot as plt
import seaborn as sns
from abc import ABC, abstractmethod
import warnings
warnings.filterwarnings('ignore')

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# ============================================================================
# Mathematical Constants and Configurations
# ============================================================================

@dataclass
class ConsciousnessConfig:
    """Configuration parameters for consciousness lattice components."""
    
    # Self-Awareness Module Parameters
    capability_learning_rate: float = 0.1
    knowledge_learning_rate: float = 0.05
    confidence_calibration_window: int = 100
    performance_history_length: int = 1000
    capability_dimensions: int = 12
    knowledge_dimensions: int = 8
    
    # Intentionality System Parameters
    max_goals: int = 50
    goal_priority_decay: float = 0.95
    planning_horizon: int = 10
    goal_completion_threshold: float = 0.9
    intention_update_rate: float = 0.2
    
    # Integrated Information Parameters
    phi_computation_steps: int = 100
    phi_convergence_threshold: float = 1e-6
    information_integration_alpha: float = 0.3
    information_integration_beta: float = 0.7
    partition_search_depth: int = 5
    
    # Attentional Awareness Parameters
    attention_temperature: float = 2.0
    attention_decay_rate: float = 0.1
    salience_weights: List[float] = field(default_factory=lambda: [0.3, 0.4, 0.2, 0.1])
    habituation_time_constant: float = 50.0
    attention_enhancement_factor: float = 2.0
    
    # Global Workspace Parameters
    workspace_capacity: int = 7  # Miller's magical number
    competition_temperature: float = 1.5
    broadcast_decay: float = 0.05
    access_threshold: float = 0.7
    coalition_formation_steps: int = 20

# ============================================================================
# Advanced Mathematical Utilities
# ============================================================================

class InformationTheoryUtils:
    """Advanced information theory calculations for consciousness metrics."""
    
    @staticmethod
    def mutual_information_continuous(X: np.ndarray, Y: np.ndarray, bins: int = 50) -> float:
        """
        Calculate mutual information for continuous variables using histogram method.
        
        I(X;Y) = ∑∑ p(x,y) log(p(x,y)/(p(x)p(y)))
        """
        # Create joint histogram
        hist_xy, x_edges, y_edges = np.histogram2d(X.flatten(), Y.flatten(), bins=bins)
        hist_xy = hist_xy + 1e-10  # Avoid log(0)
        
        # Marginal distributions
        hist_x = np.sum(hist_xy, axis=1)
        hist_y = np.sum(hist_xy, axis=0)
        
        # Normalize to probabilities
        p_xy = hist_xy / np.sum(hist_xy)
        p_x = hist_x / np.sum(hist_x)
        p_y = hist_y / np.sum(hist_y)
        
        # Calculate mutual information
        mi = 0.0
        for i in range(len(p_x)):
            for j in range(len(p_y)):
                if p_xy[i, j] > 0:
                    mi += p_xy[i, j] * np.log2(p_xy[i, j] / (p_x[i] * p_y[j]))
        
        return mi
    
    @staticmethod
    def integrated_information_phi(system_state: np.ndarray, 
                                   connectivity_matrix: np.ndarray) -> float:
        """
        Calculate Φ (phi) according to Integrated Information Theory.
        
        Φ(S) = min_{P∈Partitions} [I(S₁;S₂|S_past)]
        """
        n_nodes = len(system_state)
        if n_nodes < 2:
            return 0.0
            
        # Generate all possible bipartitions
        partitions = InformationTheoryUtils._generate_bipartitions(n_nodes)
        
        min_phi = float('inf')
        
        for partition in partitions:
            subset1, subset2 = partition
            
            # Extract subsystem states
            state1 = system_state[subset1]
            state2 = system_state[subset2]
            
            # Calculate conditional mutual information
            # Simplified version - in practice would need temporal dynamics
            if len(state1) > 0 and len(state2) > 0:
                mi = InformationTheoryUtils.mutual_information_continuous(state1, state2)
                min_phi = min(min_phi, mi)
        
        return max(0.0, min_phi)
    
    @staticmethod
    def _generate_bipartitions(n: int) -> List[Tuple[List[int], List[int]]]:
        """Generate all possible bipartitions of n elements."""
        partitions = []
        
        # Generate all non-empty subsets (except full set and empty set)
        for i in range(1, 2**(n-1)):
            subset1 = []
            subset2 = []
            
            for j in range(n):
                if i & (1 << j):
                    subset1.append(j)
                else:
                    subset2.append(j)
            
            if len(subset1) > 0 and len(subset2) > 0:
                partitions.append((subset1, subset2))
        
        return partitions

class BayesianCalibration:
    """Bayesian confidence calibration for self-awareness."""
    
    def __init__(self, alpha: float = 1.0, beta: float = 1.0):
        self.alpha = alpha
        self.beta = beta
        self.observations = []
    
    def update(self, confidence: float, correctness: bool):
        """Update Bayesian posterior with new observation."""
        self.observations.append((confidence, correctness))
        
        # Update Beta distribution parameters
        if correctness:
            self.alpha += confidence
        else:
            self.beta += (1 - confidence)
    
    def calibrate_confidence(self, raw_confidence: float) -> float:
        """Return calibrated confidence using Bayesian approach."""
        if len(self.observations) < 5:
            return raw_confidence
        
        # Expected value of Beta distribution
        expected_accuracy = self.alpha / (self.alpha + self.beta)
        
        # Confidence interval width as uncertainty measure
        variance = (self.alpha * self.beta) / ((self.alpha + self.beta)**2 * (self.alpha + self.beta + 1))
        uncertainty = np.sqrt(variance)
        
        # Adjust confidence based on calibration
        calibrated = raw_confidence * expected_accuracy - uncertainty
        
        return np.clip(calibrated, 0.0, 1.0)

# ============================================================================
# Core Consciousness Components
# ============================================================================

class SelfAwarenessModule:
    """
    Advanced self-awareness module implementing dynamic capability modeling
    and metacognitive monitoring with Bayesian confidence calibration.
    """
    
    def __init__(self, config: ConsciousnessConfig):
        self.config = config
        
        # Capability model: C = {(t_i, p_i, u_i)}
        self.capability_model = {
            'language_understanding': {'performance': 0.8, 'uncertainty': 0.2, 'samples': 0},
            'logical_reasoning': {'performance': 0.7, 'uncertainty': 0.3, 'samples': 0},
            'visual_processing': {'performance': 0.6, 'uncertainty': 0.4, 'samples': 0},
            'creative_synthesis': {'performance': 0.5, 'uncertainty': 0.5, 'samples': 0},
            'mathematical_computation': {'performance': 0.9, 'uncertainty': 0.1, 'samples': 0},
            'temporal_reasoning': {'performance': 0.6, 'uncertainty': 0.4, 'samples': 0},
            'causal_inference': {'performance': 0.5, 'uncertainty': 0.5, 'samples': 0},
            'metacognitive_monitoring': {'performance': 0.4, 'uncertainty': 0.6, 'samples': 0},
            'knowledge_integration': {'performance': 0.7, 'uncertainty': 0.3, 'samples': 0},
            'uncertainty_quantification': {'performance': 0.6, 'uncertainty': 0.4, 'samples': 0},
            'attention_control': {'performance': 0.7, 'uncertainty': 0.3, 'samples': 0},
            'goal_management': {'performance': 0.6, 'uncertainty': 0.4, 'samples': 0}
        }
        
        # Knowledge state: K = {(d_j, c_j, e_j)}
        self.knowledge_state = {
            'natural_language': {'coverage': 0.8, 'expertise': 0.9},
            'mathematics': {'coverage': 0.7, 'expertise': 0.8},
            'computer_science': {'coverage': 0.9, 'expertise': 0.8},
            'physics': {'coverage': 0.6, 'expertise': 0.7},
            'biology': {'coverage': 0.5, 'expertise': 0.6},
            'psychology': {'coverage': 0.7, 'expertise': 0.7},
            'philosophy': {'coverage': 0.6, 'expertise': 0.6},
            'history': {'coverage': 0.4, 'expertise': 0.5}
        }
        
        # Performance history and confidence calibration
        self.performance_history = deque(maxlen=config.performance_history_length)
        self.confidence_calibrators = {
            capability: BayesianCalibration() 
            for capability in self.capability_model.keys()
        }
        
        # Metacognitive state
        self.metacognitive_state = {
            'current_uncertainty': 0.5,
            'confidence_level': 0.7,
            'attention_focus': None,
            'processing_difficulty': 0.5,
            'cognitive_load': 0.4
        }
        
        # Self-model neural network
        self.self_model_network = self._build_self_model_network()
        
    def _build_self_model_network(self) -> nn.Module:
        """Build neural network for self-model representation."""
        
        class SelfModelNetwork(nn.Module):
            def __init__(self, input_dim: int, hidden_dim: int = 128):
                super().__init__()
                self.capability_encoder = nn.Sequential(
                    nn.Linear(input_dim, hidden_dim),
                    nn.ReLU(),
                    nn.Dropout(0.2),
                    nn.Linear(hidden_dim, hidden_dim // 2),
                    nn.ReLU(),
                    nn.Linear(hidden_dim // 2, 32)
                )
                
                self.uncertainty_predictor = nn.Sequential(
                    nn.Linear(32, 16),
                    nn.ReLU(),
                    nn.Linear(16, 1),
                    nn.Sigmoid()
                )
                
                self.performance_predictor = nn.Sequential(
                    nn.Linear(32, 16),
                    nn.ReLU(),
                    nn.Linear(16, 1),
                    nn.Sigmoid()
                )
                
            def forward(self, capability_features):
                encoded = self.capability_encoder(capability_features)
                uncertainty = self.uncertainty_predictor(encoded)
                performance = self.performance_predictor(encoded)
                return performance, uncertainty
        
        return SelfModelNetwork(len(self.capability_model) + len(self.knowledge_state))
    
    def update_capability_model(self, task_result: Dict[str, Any]):
        """
        Update capability model based on task performance.
        
        p_i^new = (1 - α) * p_i^old + α * p_observed
        u_i^new = (1 - β) * u_i^old + β * |p_i^old - p_observed|
        """
        task_type = task_result['task_type']
        performance = float(task_result['performance'])
        confidence = task_result.get('confidence', 0.5)
        correctness = performance > 0.7  # Threshold for correctness
        
        if task_type in self.capability_model:
            alpha = self.config.capability_learning_rate
            
            # Update performance estimate
            old_performance = self.capability_model[task_type]['performance']
            new_performance = (1 - alpha) * old_performance + alpha * performance
            
            # Update uncertainty estimate
            old_uncertainty = self.capability_model[task_type]['uncertainty']
            prediction_error = abs(old_performance - performance)
            new_uncertainty = (1 - alpha) * old_uncertainty + alpha * prediction_error
            
            # Update model
            self.capability_model[task_type].update({
                'performance': new_performance,
                'uncertainty': new_uncertainty,
                'samples': self.capability_model[task_type]['samples'] + 1
            })
            
            # Update confidence calibration
            self.confidence_calibrators[task_type].update(confidence, correctness)
            
            # Add to performance history
            self.performance_history.append({
                'task_type': task_type,
                'performance': performance,
                'confidence': confidence,
                'timestamp': time.time(),
                'correctness': correctness
            })
            
            logger.info(f"Updated capability model for {task_type}: "
                       f"performance={new_performance:.3f}, uncertainty={new_uncertainty:.3f}")
    
    def update_knowledge_state(self, domain: str, coverage_delta: float, expertise_delta: float):
        """
        Update knowledge state representation.
        
        c_j^new = c_j^old + γ * Δc_j
        e_j^new = e_j^old + δ * Δe_j
        """
        if domain in self.knowledge_state:
            gamma = self.config.knowledge_learning_rate
            delta = self.config.knowledge_learning_rate
            
            old_coverage = self.knowledge_state[domain]['coverage']
            old_expertise = self.knowledge_state[domain]['expertise']
            
            new_coverage = np.clip(old_coverage + gamma * coverage_delta, 0.0, 1.0)
            new_expertise = np.clip(old_expertise + delta * expertise_delta, 0.0, 1.0)
            
            self.knowledge_state[domain].update({
                'coverage': new_coverage,
                'expertise': new_expertise
            })
    
    def get_calibrated_confidence(self, task_type: str, raw_confidence: float) -> float:
        """Get calibrated confidence for a specific task type."""
        if task_type in self.confidence_calibrators:
            return self.confidence_calibrators[task_type].calibrate_confidence(raw_confidence)
        return raw_confidence
    
    def identify_limitations(self) -> List[Dict[str, Any]]:
        """Identify system limitations based on capability model."""
        limitations = []
        
        for capability, stats in self.capability_model.items():
            # Low performance or high uncertainty indicates limitation
            performance_threshold = 0.6
            uncertainty_threshold = 0.5
            
            if (stats['performance'] < performance_threshold or 
                stats['uncertainty'] > uncertainty_threshold):
                limitations.append({
                    'capability': capability,
                    'performance': stats['performance'],
                    'uncertainty': stats['uncertainty'],
                    'severity': 1 - stats['performance'] + stats['uncertainty'],
                    'samples': stats['samples']
                })
        
        # Sort by severity
        limitations.sort(key=lambda x: x['severity'], reverse=True)
        return limitations
    
    def generate_self_description(self) -> str:
        """Generate natural language description of capabilities and limitations."""
        # Identify top capabilities
        capabilities = sorted(
            self.capability_model.items(), 
            key=lambda x: x[1]['performance'] - x[1]['uncertainty'], 
            reverse=True
        )
        
        top_capabilities = capabilities[:3]
        limitations = self.identify_limitations()[:3]
        
        description = "ULTRA Self-Assessment Report:\n\n"
        description += "Core Strengths:\n"
        for capability, stats in top_capabilities:
            confidence = stats['performance'] - stats['uncertainty']
            description += f"- {capability.replace('_', ' ').title()}: "
            description += f"{stats['performance']:.2f} performance, "
            description += f"{confidence:.2f} confidence\n"
        
        if limitations:
            description += "\nAreas for Improvement:\n"
            for limitation in limitations:
                description += f"- {limitation['capability'].replace('_', ' ').title()}: "
                description += f"{limitation['performance']:.2f} performance, "
                description += f"requires attention\n"
        
        # Knowledge assessment
        knowledge_scores = [
            (domain, stats['coverage'] * stats['expertise']) 
            for domain, stats in self.knowledge_state.items()
        ]
        knowledge_scores.sort(key=lambda x: x[1], reverse=True)
        
        description += f"\nKnowledge Domains (Top 3):\n"
        for domain, score in knowledge_scores[:3]:
            description += f"- {domain.replace('_', ' ').title()}: {score:.2f}\n"
        
        return description
    
    def compute_metacognitive_features(self) -> np.ndarray:
        """Compute feature vector for metacognitive analysis."""
        features = []
        
        # Capability features
        for capability, stats in self.capability_model.items():
            features.extend([stats['performance'], stats['uncertainty']])
        
        # Knowledge features
        for domain, stats in self.knowledge_state.items():
            features.extend([stats['coverage'], stats['expertise']])
        
        # Performance history features
        if len(self.performance_history) > 0:
            recent_performances = [h['performance'] for h in list(self.performance_history)[-10:]]
            features.extend([
                np.mean(recent_performances),
                np.std(recent_performances),
                len(self.performance_history)
            ])
        else:
            features.extend([0.5, 0.5, 0])
        
        return np.array(features, dtype=np.float32)

class IntentionalitySystem:
    """
    Advanced intentionality system implementing hierarchical goal management,
    planning, and intention formation with dynamic adaptation.
    """
    
    def __init__(self, config: ConsciousnessConfig):
        self.config = config
        
        # Goal hierarchy: G = {(g_i, p_i, v_i, d_i)}
        self.goals = {}
        self.goal_hierarchy = {}
        self.active_intentions = {}
        self.completed_goals = set()
        
        # Planning system
        self.plans = {}
        self.plan_execution_state = {}
        
        # Intention formation network
        self.intention_network = self._build_intention_network()
        
        # Progress monitoring
        self.progress_tracker = defaultdict(list)
        
        # Goal utility estimator
        self.utility_estimator = self._build_utility_estimator()
        
    def _build_intention_network(self) -> nn.Module:
        """Build neural network for intention formation."""
        
        class IntentionNetwork(nn.Module):
            def __init__(self, input_dim: int = 64, hidden_dim: int = 128):
                super().__init__()
                
                self.goal_encoder = nn.Sequential(
                    nn.Linear(input_dim, hidden_dim),
                    nn.ReLU(),
                    nn.BatchNorm1d(hidden_dim),
                    nn.Dropout(0.3),
                    nn.Linear(hidden_dim, hidden_dim // 2)
                )
                
                self.context_encoder = nn.Sequential(
                    nn.Linear(input_dim, hidden_dim),
                    nn.ReLU(),
                    nn.BatchNorm1d(hidden_dim),
                    nn.Dropout(0.3),
                    nn.Linear(hidden_dim, hidden_dim // 2)
                )
                
                self.intention_decoder = nn.Sequential(
                    nn.Linear(hidden_dim, hidden_dim),
                    nn.ReLU(),
                    nn.Linear(hidden_dim, 32),
                    nn.ReLU(),
                    nn.Linear(32, 1),
                    nn.Sigmoid()
                )
                
            def forward(self, goal_features, context_features):
                goal_encoded = self.goal_encoder(goal_features)
                context_encoded = self.context_encoder(context_features)
                combined = torch.cat([goal_encoded, context_encoded], dim=-1)
                intention_strength = self.intention_decoder(combined)
                return intention_strength
        
        return IntentionNetwork()
    
    def _build_utility_estimator(self) -> nn.Module:
        """Build neural network for goal utility estimation."""
        
        class UtilityEstimator(nn.Module):
            def __init__(self, input_dim: int = 32):
                super().__init__()
                
                self.network = nn.Sequential(
                    nn.Linear(input_dim, 64),
                    nn.ReLU(),
                    nn.Dropout(0.2),
                    nn.Linear(64, 32),
                    nn.ReLU(),
                    nn.Linear(32, 16),
                    nn.ReLU(),
                    nn.Linear(16, 1)
                )
                
            def forward(self, goal_features):
                return self.network(goal_features)
        
        return UtilityEstimator()
    
    def set_goal(self, description: str, priority: float = 0.5, 
                 deadline: Optional[float] = None, 
                 parent_goal_id: Optional[str] = None) -> str:
        """
        Set a new goal in the hierarchy.
        
        Args:
            description: Natural language goal description
            priority: Goal priority (0-1)
            deadline: Optional deadline timestamp
            parent_goal_id: Optional parent goal ID for hierarchical structure
        
        Returns:
            goal_id: Unique identifier for the goal
        """
        goal_id = str(uuid.uuid4())
        
        # Ensure we don't exceed maximum goals
        if len(self.goals) >= self.config.max_goals:
            self._prune_low_priority_goals()
        
        # Create goal entry
        goal_entry = {
            'id': goal_id,
            'description': description,
            'priority': priority,
            'deadline': deadline,
            'created_at': time.time(),
            'status': 'active',
            'progress': 0.0,
            'parent_id': parent_goal_id,
            'expected_difficulty': self._estimate_goal_difficulty(description),
            'utility_estimate': self._estimate_goal_utility(description)
        }
        
        self.goals[goal_id] = goal_entry
        
        # Update hierarchy
        self.goal_hierarchy[goal_id] = {
            'parent': parent_goal_id,
            'children': [],
            'depth': 0 if parent_goal_id is None else self.goal_hierarchy[parent_goal_id]['depth'] + 1
        }
        
        # Add to parent's children if applicable
        if parent_goal_id and parent_goal_id in self.goal_hierarchy:
            self.goal_hierarchy[parent_goal_id]['children'].append(goal_id)
        
        # Initialize plan
        self.plans[goal_id] = self._generate_initial_plan(goal_entry)
        
        logger.info(f"Set goal {goal_id}: {description} with priority {priority}")
        return goal_id
    
    def _estimate_goal_difficulty(self, description: str) -> float:
        """Estimate goal difficulty based on description features."""
        # Simple heuristic based on description complexity
        complexity_indicators = ['complex', 'difficult', 'challenging', 'advanced', 'intricate']
        simple_indicators = ['simple', 'easy', 'basic', 'straightforward', 'quick']
        
        description_lower = description.lower()
        
        complexity_score = sum(1 for indicator in complexity_indicators if indicator in description_lower)
        simplicity_score = sum(1 for indicator in simple_indicators if indicator in description_lower)
        
        # Base difficulty on length and complexity indicators
        length_factor = min(len(description.split()) / 20.0, 1.0)
        complexity_factor = (complexity_score - simplicity_score) / 10.0
        
        difficulty = 0.5 + length_factor * 0.3 + complexity_factor
        return np.clip(difficulty, 0.1, 0.9)
    
    def _estimate_goal_utility(self, description: str) -> float:
        """Estimate goal utility using simple heuristics."""
        # High-utility keywords
        high_utility = ['learn', 'improve', 'optimize', 'enhance', 'develop', 'create']
        low_utility = ['busy', 'trivial', 'unnecessary', 'redundant']
        
        description_lower = description.lower()
        
        high_score = sum(1 for word in high_utility if word in description_lower)
        low_score = sum(1 for word in low_utility if word in description_lower)
        
        utility = 0.5 + (high_score - low_score) * 0.1
        return np.clip(utility, 0.1, 0.9)
    
    def _generate_initial_plan(self, goal_entry: Dict[str, Any]) -> Dict[str, Any]:
        """Generate initial plan for achieving a goal."""
        # Decompose goal into subgoals based on description
        description = goal_entry['description']
        difficulty = goal_entry['expected_difficulty']
        
        # Simple plan generation based on goal characteristics
        if difficulty > 0.7:
            # Complex goal - create multiple steps
            steps = [
                {'action': 'analyze_requirements', 'estimated_duration': 0.1, 'dependencies': []},
                {'action': 'gather_resources', 'estimated_duration': 0.2, 'dependencies': [0]},
                {'action': 'execute_main_task', 'estimated_duration': 0.5, 'dependencies': [1]},
                {'action': 'review_and_refine', 'estimated_duration': 0.2, 'dependencies': [2]}
            ]
        else:
            # Simple goal - direct execution
            steps = [
                {'action': 'execute_task', 'estimated_duration': 0.8, 'dependencies': []},
                {'action': 'verify_completion', 'estimated_duration': 0.2, 'dependencies': [0]}
            ]
        
        return {
            'goal_id': goal_entry['id'],
            'steps': steps,
            'current_step': 0,
            'estimated_completion_time': sum(step['estimated_duration'] for step in steps),
            'created_at': time.time()
        }
    
    def update_goal_progress(self, goal_id: str, progress: float):
        """Update progress for a specific goal."""
        if goal_id in self.goals:
            old_progress = self.goals[goal_id]['progress']
            self.goals[goal_id]['progress'] = np.clip(progress, 0.0, 1.0)
            
            # Track progress history
            self.progress_tracker[goal_id].append({
                'progress': progress,
                'timestamp': time.time(),
                'delta': progress - old_progress
            })
            
            # Check for goal completion
            if progress >= self.config.goal_completion_threshold:
                self._mark_goal_complete(goal_id)
            
            # Update parent goal progress if applicable
            parent_id = self.goal_hierarchy[goal_id]['parent']
            if parent_id:
                self._update_parent_progress(parent_id)
    
    def _mark_goal_complete(self, goal_id: str):
        """Mark a goal as completed and update hierarchy."""
        if goal_id in self.goals:
            self.goals[goal_id]['status'] = 'completed'
            self.goals[goal_id]['completed_at'] = time.time()
            self.completed_goals.add(goal_id)
            
            logger.info(f"Goal {goal_id} completed: {self.goals[goal_id]['description']}")
            
            # Check if parent goal can be completed
            parent_id = self.goal_hierarchy[goal_id]['parent']
            if parent_id:
                self._check_parent_completion(parent_id)
    
    def _update_parent_progress(self, parent_id: str):
        """Update parent goal progress based on children completion."""
        if parent_id not in self.goals:
            return
        
        children = self.goal_hierarchy[parent_id]['children']
        if not children:
            return
        
        # Calculate weighted average of children progress
        total_progress = 0.0
        total_weight = 0.0
        
        for child_id in children:
            if child_id in self.goals:
                child_progress = self.goals[child_id]['progress']
                child_priority = self.goals[child_id]['priority']
                total_progress += child_progress * child_priority
                total_weight += child_priority
        
        if total_weight > 0:
            parent_progress = total_progress / total_weight
            self.update_goal_progress(parent_id, parent_progress)
    
    def _check_parent_completion(self, parent_id: str):
        """Check if parent goal can be marked as completed."""
        children = self.goal_hierarchy[parent_id]['children']
        
        # Check if all children are completed
        all_completed = all(
            self.goals[child_id]['status'] == 'completed' 
            for child_id in children 
            if child_id in self.goals
        )
        
        if all_completed and children:
            self._mark_goal_complete(parent_id)
    
    def get_current_intentions(self) -> List[Dict[str, Any]]:
        """Get current active intentions ranked by priority and urgency."""
        active_goals = [
            goal for goal in self.goals.values() 
            if goal['status'] == 'active'
        ]
        
        # Calculate intention strength for each goal
        intentions = []
        current_time = time.time()
        
        for goal in active_goals:
            # Calculate urgency based on deadline
            urgency = 0.5
            if goal['deadline']:
                time_remaining = goal['deadline'] - current_time
                if time_remaining > 0:
                    urgency = 1.0 - min(time_remaining / (24 * 3600), 1.0)  # Daily urgency increase
                else:
                    urgency = 1.0  # Overdue
            
            # Calculate utility considering progress
            utility = goal['utility_estimate'] * (1 - goal['progress'])
            
            # Combined intention strength
            intention_strength = (
                goal['priority'] * 0.4 + 
                urgency * 0.3 + 
                utility * 0.3
            )
            
            intentions.append({
                'goal_id': goal['id'],
                'description': goal['description'],
                'intention_strength': intention_strength,
                'priority': goal['priority'],
                'urgency': urgency,
                'utility': utility,
                'progress': goal['progress']
            })
        
        # Sort by intention strength
        intentions.sort(key=lambda x: x['intention_strength'], reverse=True)
        return intentions
    
    def adapt_plans(self, performance_feedback: Dict[str, Any]):
        """Adapt plans based on performance feedback."""
        goal_id = performance_feedback.get('goal_id')
        
        if goal_id and goal_id in self.plans:
            plan = self.plans[goal_id]
            feedback_type = performance_feedback.get('type', 'progress')
            
            if feedback_type == 'obstacle':
                # Add contingency steps
                obstacle_description = performance_feedback.get('description', '')
                self._add_contingency_step(plan, obstacle_description)
                
            elif feedback_type == 'efficiency':
                # Adjust time estimates
                efficiency_factor = performance_feedback.get('efficiency', 1.0)
                self._adjust_time_estimates(plan, efficiency_factor)
                
            elif feedback_type == 'resource_constraint':
                # Modify resource requirements
                constraint = performance_feedback.get('constraint', '')
                self._handle_resource_constraint(plan, constraint)
    
    def _add_contingency_step(self, plan: Dict[str, Any], obstacle: str):
        """Add contingency step to handle obstacles."""
        contingency_step = {
            'action': f'handle_obstacle_{len(plan["steps"])}',
            'description': f'Address obstacle: {obstacle}',
            'estimated_duration': 0.1,
            'dependencies': [plan['current_step']]
        }
        plan['steps'].insert(plan['current_step'] + 1, contingency_step)
    
    def _adjust_time_estimates(self, plan: Dict[str, Any], efficiency_factor: float):
        """Adjust time estimates based on efficiency feedback."""
        for step in plan['steps'][plan['current_step']:]:
            step['estimated_duration'] *= (1.0 / efficiency_factor)
    
    def _prune_low_priority_goals(self):
        """Remove lowest priority inactive goals to make room for new ones."""
        inactive_goals = [
            (goal_id, goal) for goal_id, goal in self.goals.items()
            if goal['status'] == 'active' and goal['progress'] < 0.1
        ]
        
        if inactive_goals:
            # Sort by priority (ascending) and remove lowest
            inactive_goals.sort(key=lambda x: x[1]['priority'])
            goal_to_remove = inactive_goals[0][0]
            
            # Remove from all data structures
            del self.goals[goal_to_remove]
            del self.goal_hierarchy[goal_to_remove]
            if goal_to_remove in self.plans:
                del self.plans[goal_to_remove]

class IntegratedInformationMatrix:
    """
    Implementation of Integrated Information Theory (IIT) for measuring
    consciousness through integrated information (Φ).
    """
    
    def __init__(self, config: ConsciousnessConfig):
        self.config = config
        
        # System state representation
        self.system_nodes = []
        self.connectivity_matrix = None
        self.node_states = None
        
        # Φ computation history
        self.phi_history = deque(maxlen=1000)
        
        # Information flow control parameters
        self.alpha_ij = None  # Flow enhancement factors
        self.beta_ij = None   # Flow inhibition factors
        
    def initialize_system(self, n_nodes: int, connectivity_probability: float = 0.3):
        """Initialize the integrated information system."""
        self.system_nodes = list(range(n_nodes))
        
        # Create random connectivity matrix
        self.connectivity_matrix = np.random.rand(n_nodes, n_nodes)
        self.connectivity_matrix = (self.connectivity_matrix < connectivity_probability).astype(float)
        
        # Ensure no self-connections
        np.fill_diagonal(self.connectivity_matrix, 0)
        
        # Initialize node states
        self.node_states = np.random.randn(n_nodes)
        
        # Initialize flow control parameters
        self.alpha_ij = np.random.uniform(0.1, 0.9, (n_nodes, n_nodes))
        self.beta_ij = np.random.uniform(0.1, 0.9, (n_nodes, n_nodes))
        
        logger.info(f"Initialized IIT system with {n_nodes} nodes")
    
    def compute_phi(self, system_state: Optional[np.ndarray] = None) -> float:
        """
        Compute integrated information Φ according to IIT.
        
        Φ(X) = min_{P∈Partitions} [I(X₁;X₂|X_past)]
        """
        if system_state is not None:
            self.node_states = system_state
        
        if self.node_states is None:
            return 0.0
        
        n_nodes = len(self.node_states)
        if n_nodes < 2:
            return 0.0
        
        # Generate all possible bipartitions
        partitions = self._generate_bipartitions(n_nodes)
        
        min_phi = float('inf')
        best_partition = None
        
        for partition in partitions:
            subset1, subset2 = partition
            
            if len(subset1) == 0 or len(subset2) == 0:
                continue
            
            # Calculate effective information for this partition
            ei = self._calculate_effective_information(subset1, subset2)
            
            if ei < min_phi:
                min_phi = ei
                best_partition = partition
        
        phi = max(0.0, min_phi)
        
        # Store in history
        self.phi_history.append({
            'phi': phi,
            'timestamp': time.time(),
            'best_partition': best_partition,
            'system_state': self.node_states.copy()
        })
        
        return phi
    
    def _generate_bipartitions(self, n: int) -> List[Tuple[List[int], List[int]]]:
        """Generate all possible bipartitions."""
        partitions = []
        
        # Use bit manipulation to generate all subsets
        for i in range(1, 2**(n-1)):
            subset1 = []
            subset2 = []
            
            for j in range(n):
                if i & (1 << j):
                    subset1.append(j)
                else:
                    subset2.append(j)
            
            partitions.append((subset1, subset2))
        
        return partitions
    
    def _calculate_effective_information(self, subset1: List[int], subset2: List[int]) -> float:
        """
        Calculate effective information between two subsets.
        
        EI = I(X₁;X₂) - I(X₁;X₂|X_past)
        """
        if len(subset1) == 0 or len(subset2) == 0:
            return 0.0
        
        # Extract subset states
        state1 = self.node_states[subset1]
        state2 = self.node_states[subset2]
        
        # Calculate mutual information
        mi = self._calculate_mutual_information(state1, state2)
        
        # Calculate conditional mutual information (simplified)
        # In full implementation, this would require temporal dynamics
        conditional_mi = mi * 0.8  # Simplified approximation
        
        effective_info = mi - conditional_mi
        return max(0.0, effective_info)
    
    def _calculate_mutual_information(self, X: np.ndarray, Y: np.ndarray) -> float:
        """Calculate mutual information between two arrays."""
        if len(X) == 0 or len(Y) == 0:
            return 0.0
        
        # Convert to discrete bins for MI calculation
        bins = 10
        
        # Handle single values
        if np.isscalar(X):
            X = np.array([X])
        if np.isscalar(Y):
            Y = np.array([Y])
        
        # Ensure same length
        min_len = min(len(X), len(Y))
        X = X[:min_len]
        Y = Y[:min_len]
        
        # Discretize
        X_discrete = np.digitize(X, np.linspace(X.min(), X.max(), bins))
        Y_discrete = np.digitize(Y, np.linspace(Y.min(), Y.max(), bins))
        
        # Calculate MI using histogram method
        xy_hist = np.histogram2d(X_discrete, Y_discrete, bins=bins)[0]
        xy_hist = xy_hist + 1e-10  # Avoid log(0)
        
        # Normalize
        xy_prob = xy_hist / np.sum(xy_hist)
        x_prob = np.sum(xy_prob, axis=1)
        y_prob = np.sum(xy_prob, axis=0)
        
        # Calculate MI
        mi = 0.0
        for i in range(len(x_prob)):
            for j in range(len(y_prob)):
                if xy_prob[i, j] > 0 and x_prob[i] > 0 and y_prob[j] > 0:
                    mi += xy_prob[i, j] * np.log2(xy_prob[i, j] / (x_prob[i] * y_prob[j]))
        
        return mi
    
    def update_information_flow(self, node_i: int, node_j: int, enhancement: float):
        """
        Update information flow control between nodes.
        
        F_ij = α_ij * I(X_i; X_j) - β_ij * I(X_i; X_j | X \ {X_i, X_j})
        """
        if (0 <= node_i < len(self.system_nodes) and 
            0 <= node_j < len(self.system_nodes)):
            
            # Update flow parameters
            self.alpha_ij[node_i, node_j] = np.clip(
                self.alpha_ij[node_i, node_j] + enhancement * self.config.information_integration_alpha,
                0.0, 1.0
            )
            
            # Reciprocal update for bidirectional flow
            self.alpha_ij[node_j, node_i] = self.alpha_ij[node_i, node_j]
    
    def get_phi_statistics(self) -> Dict[str, float]:
        """Get statistical summary of Φ history."""
        if not self.phi_history:
            return {'mean': 0.0, 'std': 0.0, 'max': 0.0, 'min': 0.0, 'current': 0.0}
        
        phi_values = [h['phi'] for h in self.phi_history]
        
        return {
            'mean': np.mean(phi_values),
            'std': np.std(phi_values),
            'max': np.max(phi_values),
            'min': np.min(phi_values),
            'current': phi_values[-1] if phi_values else 0.0,
            'trend': self._calculate_phi_trend()
        }
    
    def _calculate_phi_trend(self) -> float:
        """Calculate trend in Φ values over time."""
        if len(self.phi_history) < 10:
            return 0.0
        
        recent_phi = [h['phi'] for h in list(self.phi_history)[-10:]]
        times = list(range(len(recent_phi)))
        
        # Linear regression slope
        if len(recent_phi) > 1:
            correlation, _ = pearsonr(times, recent_phi)
            return correlation
        
        return 0.0

class AttentionalAwareness:
    """
    Implementation of attentional awareness with salience calculation,
    attention allocation, and temporal dynamics.
    """
    
    def __init__(self, config: ConsciousnessConfig):
        self.config = config
        
        # Attention state
        self.attention_allocations = {}
        self.salience_scores = {}
        self.processing_enhancements = {}
        
        # Temporal dynamics
        self.attention_history = defaultdict(list)
        self.habituation_tracker = defaultdict(float)
        
        # Salience computation components
        self.novelty_detector = self._build_novelty_detector()
        self.relevance_estimator = self._build_relevance_estimator()
        self.uncertainty_estimator = self._build_uncertainty_estimator()
        self.gain_predictor = self._build_gain_predictor()
        
        # Attention control network
        self.attention_controller = self._build_attention_controller()
        
    def _build_novelty_detector(self) -> nn.Module:
        """Build neural network for novelty detection."""
        
        class NoveltyDetector(nn.Module):
            def __init__(self, input_dim: int = 64):
                super().__init__()
                
                # Autoencoder for novelty detection
                self.encoder = nn.Sequential(
                    nn.Linear(input_dim, 32),
                    nn.ReLU(),
                    nn.Linear(32, 16),
                    nn.ReLU(),
                    nn.Linear(16, 8)
                )
                
                self.decoder = nn.Sequential(
                    nn.Linear(8, 16),
                    nn.ReLU(),
                    nn.Linear(16, 32),
                    nn.ReLU(),
                    nn.Linear(32, input_dim)
                )
                
            def forward(self, x):
                encoded = self.encoder(x)
                decoded = self.decoder(encoded)
                reconstruction_error = F.mse_loss(decoded, x, reduction='none').mean(dim=-1)
                novelty = torch.sigmoid(reconstruction_error)
                return novelty, encoded
        
        return NoveltyDetector()
    
    def _build_relevance_estimator(self) -> nn.Module:
        """Build network for goal relevance estimation."""
        
        class RelevanceEstimator(nn.Module):
            def __init__(self, input_dim: int = 64, goal_dim: int = 32):
                super().__init__()
                
                self.input_processor = nn.Sequential(
                    nn.Linear(input_dim, 32),
                    nn.ReLU(),
                    nn.Dropout(0.2)
                )
                
                self.goal_processor = nn.Sequential(
                    nn.Linear(goal_dim, 32),
                    nn.ReLU(),
                    nn.Dropout(0.2)
                )
                
                self.relevance_calculator = nn.Sequential(
                    nn.Linear(64, 32),
                    nn.ReLU(),
                    nn.Linear(32, 1),
                    nn.Sigmoid()
                )
                
            def forward(self, input_features, goal_features):
                input_processed = self.input_processor(input_features)
                goal_processed = self.goal_processor(goal_features)
                combined = torch.cat([input_processed, goal_processed], dim=-1)
                relevance = self.relevance_calculator(combined)
                return relevance
        
        return RelevanceEstimator()
    
    def _build_uncertainty_estimator(self) -> nn.Module:
        """Build network for uncertainty estimation."""
        
        class UncertaintyEstimator(nn.Module):
            def __init__(self, input_dim: int = 64):
                super().__init__()
                
                # Ensemble of predictors for uncertainty estimation
                self.predictors = nn.ModuleList([
                    nn.Sequential(
                        nn.Linear(input_dim, 32),
                        nn.ReLU(),
                        nn.Dropout(0.3),
                        nn.Linear(32, 16),
                        nn.ReLU(),
                        nn.Linear(16, 1),
                        nn.Sigmoid()
                    ) for _ in range(5)
                ])
                
            def forward(self, x):
                predictions = [predictor(x) for predictor in self.predictors]
                predictions = torch.stack(predictions, dim=-1)
                
                # Uncertainty as variance across ensemble
                mean_pred = predictions.mean(dim=-1)
                uncertainty = predictions.var(dim=-1)
                
                return uncertainty
        
        return UncertaintyEstimator()
    
    def _build_gain_predictor(self) -> nn.Module:
        """Build network for potential gain prediction."""
        
        class GainPredictor(nn.Module):
            def __init__(self, input_dim: int = 64):
                super().__init__()
                
                self.network = nn.Sequential(
                    nn.Linear(input_dim, 64),
                    nn.ReLU(),
                    nn.BatchNorm1d(64),
                    nn.Dropout(0.2),
                    nn.Linear(64, 32),
                    nn.ReLU(),
                    nn.Linear(32, 16),
                    nn.ReLU(),
                    nn.Linear(16, 1),
                    nn.Sigmoid()
                )
                
            def forward(self, x):
                return self.network(x)
        
        return GainPredictor()
    
    def _build_attention_controller(self) -> nn.Module:
        """Build attention control network."""
        
        class AttentionController(nn.Module):
            def __init__(self, input_dim: int = 128):
                super().__init__()
                
                self.salience_integrator = nn.Sequential(
                    nn.Linear(input_dim, 64),
                    nn.ReLU(),
                    nn.BatchNorm1d(64),
                    nn.Dropout(0.2),
                    nn.Linear(64, 32),
                    nn.ReLU(),
                    nn.Linear(32, 1)
                )
                
                self.attention_gate = nn.Sequential(
                    nn.Linear(input_dim + 1, 32),
                    nn.ReLU(),
                    nn.Linear(32, 1),
                    nn.Sigmoid()
                )
                
            def forward(self, salience_features):
                integrated_salience = self.salience_integrator(salience_features)
                
                # Combine with salience for gating
                gate_input = torch.cat([salience_features, integrated_salience], dim=-1)
                attention_weight = self.attention_gate(gate_input)
                
                return attention_weight, integrated_salience
        
        return AttentionController()
    
    def calculate_salience(self, stimulus_id: str, stimulus_features: np.ndarray,
                          current_goals: List[Dict[str, Any]]) -> float:
        """
        Calculate salience score for a stimulus.
        
        S(x_i) = w₁·N(x_i) + w₂·R(x_i) + w₃·U(x_i) + w₄·G(x_i)
        """
        # Convert features to tensor
        features_tensor = torch.FloatTensor(stimulus_features).unsqueeze(0)
        
        # Calculate novelty
        with torch.no_grad():
            novelty, _ = self.novelty_detector(features_tensor)
            novelty = novelty.item()
        
        # Calculate relevance to current goals
        relevance = 0.0
        if current_goals:
            goal_relevances = []
            for goal in current_goals[:3]:  # Top 3 goals
                # Create simple goal feature representation
                goal_features = torch.FloatTensor([
                    goal.get('priority', 0.5),
                    goal.get('urgency', 0.5),
                    goal.get('progress', 0.0),
                    goal.get('utility', 0.5)
                ] + [0.0] * 28).unsqueeze(0)  # Pad to 32 dimensions
                
                with torch.no_grad():
                    goal_relevance = self.relevance_estimator(features_tensor, goal_features)
                    goal_relevances.append(goal_relevance.item())
            
            relevance = np.mean(goal_relevances)
        
        # Calculate uncertainty
        with torch.no_grad():
            uncertainty = self.uncertainty_estimator(features_tensor)
            uncertainty = uncertainty.item()
        
        # Calculate potential gain
        with torch.no_grad():
            gain = self.gain_predictor(features_tensor)
            gain = gain.item()
        
        # Combine components
        weights = self.config.salience_weights
        salience = (
            weights[0] * novelty +
            weights[1] * relevance +
            weights[2] * uncertainty +
            weights[3] * gain
        )
        
        # Store salience score
        self.salience_scores[stimulus_id] = {
            'total': salience,
            'novelty': novelty,
            'relevance': relevance,
            'uncertainty': uncertainty,
            'gain': gain,
            'timestamp': time.time()
        }
        
        return salience
    
    def allocate_attention(self, stimuli_saliences: Dict[str, float]) -> Dict[str, float]:
        """
        Allocate attention based on salience scores.
        
        A(x_i) = exp(S(x_i)/τ) / Σⱼ exp(S(x_j)/τ)
        """
        if not stimuli_saliences:
            return {}
        
        # Apply temperature scaling
        tau = self.config.attention_temperature
        
        # Calculate softmax attention allocation
        salience_values = np.array(list(stimuli_saliences.values()))
        exp_saliences = np.exp(salience_values / tau)
        attention_weights = exp_saliences / np.sum(exp_saliences)
        
        # Create allocation dictionary
        allocations = {}
        for i, stimulus_id in enumerate(stimuli_saliences.keys()):
            allocations[stimulus_id] = attention_weights[i]
        
        # Update attention allocations
        self.attention_allocations.update(allocations)
        
        # Update attention history
        for stimulus_id, allocation in allocations.items():
            self.attention_history[stimulus_id].append({
                'allocation': allocation,
                'timestamp': time.time()
            })
        
        return allocations
    
    def enhance_processing(self, stimulus_id: str, baseline_processing: float) -> float:
        """
        Enhance processing based on attention allocation.
        
        P(x_i) = P₀(x_i) · (1 + γ · A(x_i))
        """
        if stimulus_id not in self.attention_allocations:
            return baseline_processing
        
        attention_weight = self.attention_allocations[stimulus_id]
        enhancement_factor = self.config.attention_enhancement_factor
        
        enhanced_processing = baseline_processing * (1 + enhancement_factor * attention_weight)
        
        # Store enhancement
        self.processing_enhancements[stimulus_id] = {
            'baseline': baseline_processing,
            'enhanced': enhanced_processing,
            'enhancement_factor': enhancement_factor * attention_weight,
            'timestamp': time.time()
        }
        
        return enhanced_processing
    
    def update_attention_dynamics(self, dt: float = 1.0):
        """
        Update attention dynamics with habituation.
        
        dA(x_i,t)/dt = α(S(x_i,t) - A(x_i,t)) - β∫₀ᵗ A(x_i,τ)exp(-(t-τ)/τₕ)dτ
        """
        current_time = time.time()
        decay_rate = self.config.attention_decay_rate
        tau_h = self.config.habituation_time_constant
        
        # Update habituation for all tracked stimuli
        for stimulus_id in list(self.attention_allocations.keys()):
            current_allocation = self.attention_allocations[stimulus_id]
            
            # Calculate habituation integral (simplified)
            history = self.attention_history[stimulus_id]
            habituation_integral = 0.0
            
            for h in history[-10:]:  # Consider last 10 time steps
                time_diff = current_time - h['timestamp']
                weight = np.exp(-time_diff / tau_h)
                habituation_integral += h['allocation'] * weight
            
            # Update habituation tracker
            self.habituation_tracker[stimulus_id] = habituation_integral
            
            # Apply habituation decay
            if stimulus_id in self.salience_scores:
                original_salience = self.salience_scores[stimulus_id]['total']
                
                # Attention dynamics update
                salience_term = original_salience * decay_rate
                habituation_term = decay_rate * habituation_integral
                
                new_allocation = current_allocation + dt * (salience_term - habituation_term)
                new_allocation = np.clip(new_allocation, 0.0, 1.0)
                
                self.attention_allocations[stimulus_id] = new_allocation
    
    def get_attention_summary(self) -> Dict[str, Any]:
        """Get summary of current attention state."""
        if not self.attention_allocations:
            return {'total_stimuli': 0, 'top_attended': None, 'attention_entropy': 0.0}
        
        # Calculate attention entropy
        allocations = list(self.attention_allocations.values())
        attention_entropy = entropy(allocations) if len(allocations) > 1 else 0.0
        
        # Find top attended stimulus
        top_stimulus = max(self.attention_allocations.items(), key=lambda x: x[1])
        
        # Calculate attention distribution stats
        allocation_values = np.array(allocations)
        
        return {
            'total_stimuli': len(self.attention_allocations),
            'top_attended': {'stimulus': top_stimulus[0], 'allocation': top_stimulus[1]},
            'attention_entropy': attention_entropy,
            'mean_allocation': np.mean(allocation_values),
            'std_allocation': np.std(allocation_values),
            'max_allocation': np.max(allocation_values),
            'total_habituation': sum(self.habituation_tracker.values())
        }

class GlobalWorkspace:
    """
    Implementation of Global Workspace Theory for conscious broadcast
    and integration across distributed processing modules.
    """
    
    def __init__(self, config: ConsciousnessConfig):
        self.config = config
        
        # Workspace state
        self.workspace_contents = {}
        self.workspace_history = deque(maxlen=1000)
        
        # Competition and coalition formation
        self.competing_coalitions = {}
        self.access_probabilities = {}
        
        # Broadcast mechanism
        self.broadcast_targets = set()
        self.broadcast_history = deque(maxlen=500)
        
        # Monitoring and access patterns
        self.access_patterns = defaultdict(list)
        
        # Neural networks for workspace operations
        self.competition_network = self._build_competition_network()
        self.coalition_former = self._build_coalition_former()
        self.broadcast_controller = self._build_broadcast_controller()
        
    def _build_competition_network(self) -> nn.Module:
        """Build network for competitive selection."""
        
        class CompetitionNetwork(nn.Module):
            def __init__(self, input_dim: int = 64):
                super().__init__()
                
                # Competitive selection network
                self.competitor_encoder = nn.Sequential(
                    nn.Linear(input_dim, 128),
                    nn.ReLU(),
                    nn.BatchNorm1d(128),
                    nn.Dropout(0.3),
                    nn.Linear(128, 64),
                    nn.ReLU(),
                    nn.Linear(64, 32)
                )
                
                # Competition strength calculator
                self.competition_strength = nn.Sequential(
                    nn.Linear(32, 16),
                    nn.ReLU(),
                    nn.Linear(16, 1)
                )
                
                # Coalition compatibility
                self.coalition_compatibility = nn.Sequential(
                    nn.Linear(64, 32),  # For pairs of competitors
                    nn.ReLU(),
                    nn.Linear(32, 1),
                    nn.Sigmoid()
                )
                
            def forward(self, competitor_features):
                encoded = self.competitor_encoder(competitor_features)
                strength = self.competition_strength(encoded)
                return encoded, strength
        
        return CompetitionNetwork()
    
    def _build_coalition_former(self) -> nn.Module:
        """Build network for coalition formation."""
        
        class CoalitionFormer(nn.Module):
            def __init__(self, input_dim: int = 64):
                super().__init__()
                
                self.synergy_calculator = nn.Sequential(
                    nn.Linear(input_dim * 2, 128),
                    nn.ReLU(),
                    nn.BatchNorm1d(128),
                    nn.Dropout(0.2),
                    nn.Linear(128, 64),
                    nn.ReLU(),
                    nn.Linear(64, 1),
                    nn.Sigmoid()
                )
                
            def forward(self, member1_features, member2_features):
                combined = torch.cat([member1_features, member2_features], dim=-1)
                synergy = self.synergy_calculator(combined)
                return synergy
        
        return CoalitionFormer()
    
    def _build_broadcast_controller(self) -> nn.Module:
        """Build network for broadcast control."""
        
        class BroadcastController(nn.Module):
            def __init__(self, input_dim: int = 64):
                super().__init__()
                
                self.broadcast_selector = nn.Sequential(
                    nn.Linear(input_dim, 128),
                    nn.ReLU(),
                    nn.BatchNorm1d(128),
                    nn.Dropout(0.2),
                    nn.Linear(128, 64),
                    nn.ReLU(),
                    nn.Linear(64, 32),
                    nn.ReLU(),
                    nn.Linear(32, 1),
                    nn.Sigmoid()
                )
                
                self.influence_predictor = nn.Sequential(
                    nn.Linear(input_dim, 64),
                    nn.ReLU(),
                    nn.Linear(64, 32),
                    nn.ReLU(),
                    nn.Linear(32, 1)
                )
                
            def forward(self, content_features):
                broadcast_prob = self.broadcast_selector(content_features)
                influence = self.influence_predictor(content_features)
                return broadcast_prob, influence
        
        return BroadcastController()
    
    def add_competitor(self, content_id: str, content_features: np.ndarray, 
                      competitive_strength: float) -> str:
        """Add a competitor for workspace access."""
        
        # Generate unique coalition ID
        coalition_id = f"coalition_{content_id}_{int(time.time() * 1000)}"
        
        # Store competitor
        self.competing_coalitions[coalition_id] = {
            'content_id': content_id,
            'features': content_features,
            'strength': competitive_strength,
            'created_at': time.time(),
            'members': [content_id],
            'synergy_score': 1.0
        }
        
        return coalition_id
    
    def form_coalitions(self) -> Dict[str, Dict[str, Any]]:
        """
        Form coalitions among competing content through synergy detection.
        """
        if len(self.competing_coalitions) < 2:
            return self.competing_coalitions
        
        # Calculate pairwise synergies
        coalition_ids = list(self.competing_coalitions.keys())
        synergy_matrix = np.zeros((len(coalition_ids), len(coalition_ids)))
        
        for i, id1 in enumerate(coalition_ids):
            for j, id2 in enumerate(coalition_ids):
                if i != j:
                    features1 = torch.FloatTensor(self.competing_coalitions[id1]['features']).unsqueeze(0)
                    features2 = torch.FloatTensor(self.competing_coalitions[id2]['features']).unsqueeze(0)
                    
                    with torch.no_grad():
                        synergy = self.coalition_former(features1, features2)
                        synergy_matrix[i, j] = synergy.item()
        
        # Form coalitions based on high synergy
        synergy_threshold = 0.7
        merged_coalitions = {}
        processed = set()
        
        for i, id1 in enumerate(coalition_ids):
            if id1 in processed:
                continue
                
            # Start new coalition
            coalition_members = [id1]
            coalition_features = [self.competing_coalitions[id1]['features']]
            coalition_strength = self.competing_coalitions[id1]['strength']
            
            # Find high-synergy partners
            for j, id2 in enumerate(coalition_ids):
                if i != j and id2 not in processed and synergy_matrix[i, j] > synergy_threshold:
                    coalition_members.append(id2)
                    coalition_features.append(self.competing_coalitions[id2]['features'])
                    coalition_strength += self.competing_coalitions[id2]['strength']
                    processed.add(id2)
            
            processed.add(id1)
            
            # Create merged coalition
            merged_id = f"merged_{len(merged_coalitions)}"
            merged_coalitions[merged_id] = {
                'content_id': f"coalition_{len(coalition_members)}_members",
                'features': np.mean(coalition_features, axis=0),
                'strength': coalition_strength,
                'created_at': time.time(),
                'members': coalition_members,
                'synergy_score': np.mean([synergy_matrix[i, j] for j in range(len(coalition_ids)) if i != j])
            }
        
        # Update competing coalitions
        self.competing_coalitions = merged_coalitions
        return merged_coalitions
    
    def compete_for_access(self) -> Optional[str]:
        """
        Run competition for workspace access.
        
        P(x_i ∈ GW) = exp(C(x_i)/τ) / Σⱼ exp(C(x_j)/τ)
        """
        if not self.competing_coalitions:
            return None
        
        # Calculate competition strengths
        coalition_strengths = []
        coalition_ids = []
        
        for coalition_id, coalition in self.competing_coalitions.items():
            # Enhanced strength includes synergy
            enhanced_strength = coalition['strength'] * coalition['synergy_score']
            coalition_strengths.append(enhanced_strength)
            coalition_ids.append(coalition_id)
        
        # Apply temperature scaling
        tau = self.config.competition_temperature
        strengths_array = np.array(coalition_strengths)
        
        # Calculate access probabilities
        exp_strengths = np.exp(strengths_array / tau)
        access_probs = exp_strengths / np.sum(exp_strengths)
        
        # Store access probabilities
        for i, coalition_id in enumerate(coalition_ids):
            self.access_probabilities[coalition_id] = access_probs[i]
        
        # Select winner probabilistically
        winner_idx = np.random.choice(len(coalition_ids), p=access_probs)
        winner_id = coalition_ids[winner_idx]
        
        # Check access threshold
        if access_probs[winner_idx] > self.config.access_threshold:
            return winner_id
        
        return None
    
    def broadcast_to_workspace(self, coalition_id: str):
        """
        Broadcast winning coalition content to global workspace.
        
        I_j(t+1) = I_j(t) + α_j · GW(t)
        """
        if coalition_id not in self.competing_coalitions:
            return
        
        winning_coalition = self.competing_coalitions[coalition_id]
        
        # Add to workspace contents
        workspace_entry = {
            'coalition_id': coalition_id,
            'content_id': winning_coalition['content_id'],
            'features': winning_coalition['features'],
            'strength': winning_coalition['strength'],
            'members': winning_coalition['members'],
            'broadcast_time': time.time(),
            'access_probability': self.access_probabilities.get(coalition_id, 0.0)
        }
        
        # Manage workspace capacity
        if len(self.workspace_contents) >= self.config.workspace_capacity:
            # Remove oldest entry
            oldest_key = min(self.workspace_contents.keys(), 
                           key=lambda k: self.workspace_contents[k]['broadcast_time'])
            del self.workspace_contents[oldest_key]
        
        # Add new content
        self.workspace_contents[coalition_id] = workspace_entry
        
        # Record broadcast
        self.broadcast_history.append({
            'coalition_id': coalition_id,
            'content_id': winning_coalition['content_id'],
            'broadcast_time': time.time(),
            'workspace_size': len(self.workspace_contents)
        })
        
        # Simulate broadcast to registered targets
        self._execute_broadcast(workspace_entry)
        
        logger.info(f"Broadcast coalition {coalition_id} to global workspace")
    
    def _execute_broadcast(self, workspace_entry: Dict[str, Any]):
        """Execute broadcast to all registered targets."""
        broadcast_content = {
            'source': 'global_workspace',
            'content_id': workspace_entry['content_id'],
            'features': workspace_entry['features'],
            'strength': workspace_entry['strength'],
            'timestamp': workspace_entry['broadcast_time']
        }
        
        # Simulate influence on broadcast targets
        for target in self.broadcast_targets:
            # Calculate influence based on target type
            influence_factor = np.random.uniform(0.5, 1.0)  # Simplified
            
            # Record influence (in real implementation, would update target systems)
            self.access_patterns[target].append({
                'content_id': workspace_entry['content_id'],
                'influence': influence_factor,
                'timestamp': time.time()
            })
    
    def update_workspace_dynamics(self, dt: float = 1.0):
        """
        Update workspace dynamics with decay.
        
        dGW(t)/dt = -λ·GW(t) + Σᵢ βᵢ·xᵢ·1{xᵢ ∈ GW}
        """
        current_time = time.time()
        decay_rate = self.config.broadcast_decay
        
        # Apply decay to workspace contents
        to_remove = []
        
        for coalition_id, entry in self.workspace_contents.items():
            time_since_broadcast = current_time - entry['broadcast_time']
            
            # Calculate decay
            decay_factor = np.exp(-decay_rate * time_since_broadcast)
            entry['strength'] *= decay_factor
            
            # Remove if strength too low
            if entry['strength'] < 0.1:
                to_remove.append(coalition_id)
        
        # Remove decayed entries
        for coalition_id in to_remove:
            del self.workspace_contents[coalition_id]
        
        # Store workspace state in history
        self.workspace_history.append({
            'timestamp': current_time,
            'contents': list(self.workspace_contents.keys()),
            'total_strength': sum(e['strength'] for e in self.workspace_contents.values()),
            'capacity_utilization': len(self.workspace_contents) / self.config.workspace_capacity
        })
    
    def register_broadcast_target(self, target_id: str):
        """Register a system to receive broadcasts."""
        self.broadcast_targets.add(target_id)
    
    def get_workspace_state(self) -> Dict[str, Any]:
        """Get current workspace state summary."""
        if not self.workspace_contents:
            return {
                'active_contents': 0,
                'total_strength': 0.0,
                'capacity_utilization': 0.0,
                'most_recent_broadcast': None
            }
        
        total_strength = sum(entry['strength'] for entry in self.workspace_contents.values())
        
        # Find most recent broadcast
        most_recent = max(self.workspace_contents.values(), 
                         key=lambda x: x['broadcast_time'])
        
        return {
            'active_contents': len(self.workspace_contents),
            'total_strength': total_strength,
            'capacity_utilization': len(self.workspace_contents) / self.config.workspace_capacity,
            'most_recent_broadcast': {
                'content_id': most_recent['content_id'],
                'time_ago': time.time() - most_recent['broadcast_time'],
                'strength': most_recent['strength']
            },
            'registered_targets': len(self.broadcast_targets)
        }
    
    def clear_competitors(self):
        """Clear current competitors (typically called after competition)."""
        self.competing_coalitions.clear()
        self.access_probabilities.clear()

# ============================================================================
# Integration Tests
# ============================================================================

class TestEmergentConsciousnessIntegration:
    """Integration tests for the complete consciousness lattice."""
    
    def __init__(self):
        self.config = ConsciousnessConfig()
        self.self_awareness = SelfAwarenessModule(self.config)
        self.intentionality = IntentionalitySystem(self.config)
        self.integrated_info = IntegratedInformationMatrix(self.config)
        self.attention = AttentionalAwareness(self.config)
        self.global_workspace = GlobalWorkspace(self.config)
    
    def test_full_consciousness_cycle(self):
        """Test complete consciousness processing cycle."""
        logger.info("Starting full consciousness cycle test")
        
        # 1. Initialize systems
        self.integrated_info.initialize_system(n_nodes=10)
        
        # 2. Set goals in intentionality system
        goal1_id = self.intentionality.set_goal("Learn new concepts", priority=0.8)
        goal2_id = self.intentionality.set_goal("Solve complex problem", priority=0.6)
        
        # 3. Create stimuli for attention
        stimulus_features = np.random.randn(64)
        current_goals = self.intentionality.get_current_intentions()
        
        # 4. Calculate attention and salience
        salience = self.attention.calculate_salience("stimulus_1", stimulus_features, current_goals)
        attention_allocation = self.attention.allocate_attention({"stimulus_1": salience})
        
        # 5. Add content to global workspace
        coalition_id = self.global_workspace.add_competitor("stimulus_1", stimulus_features, salience)
        self.global_workspace.form_coalitions()
        winner = self.global_workspace.compete_for_access()
        
        if winner:
            self.global_workspace.broadcast_to_workspace(winner)
        
        # 6. Update self-awareness based on performance
        task_result = {
            'task_type': 'logical_reasoning',
            'performance': 0.75,
            'confidence': 0.8
        }
        self.self_awareness.update_capability_model(task_result)
        
        # 7. Calculate integrated information
        system_state = np.random.randn(10)
        phi = self.integrated_info.compute_phi(system_state)
        
        # 8. Update temporal dynamics
        self.attention.update_attention_dynamics()
        self.global_workspace.update_workspace_dynamics()
        
        # Assertions
        assert salience > 0.0, "Salience should be positive"
        assert len(attention_allocation) > 0, "Attention should be allocated"
        assert phi >= 0.0, "Phi should be non-negative"
        
        # Check self-awareness updates
        limitations = self.self_awareness.identify_limitations()
        assert isinstance(limitations, list), "Limitations should be a list"
        
        # Check workspace state
        workspace_state = self.global_workspace.get_workspace_state()
        assert isinstance(workspace_state, dict), "Workspace state should be a dict"
        
        logger.info("Full consciousness cycle test completed successfully")
        
        return {
            'salience': salience,
            'phi': phi,
            'attention_allocation': attention_allocation,
            'workspace_state': workspace_state,
            'limitations': limitations
        }
    
    def test_consciousness_integration_metrics(self):
        """Test consciousness integration metrics."""
        logger.info("Testing consciousness integration metrics")
        
        # Initialize with test data
        self.integrated_info.initialize_system(n_nodes=8)
        
        # Create multiple test scenarios
        results = []
        
        for scenario in range(5):
            # Generate scenario-specific state
            system_state = np.random.randn(8) * (scenario + 1) * 0.2
            
            # Calculate phi
            phi = self.integrated_info.compute_phi(system_state)
            
            # Create test stimuli
            stimuli = {}
            for i in range(3):
                stimulus_id = f"stimulus_{scenario}_{i}"
                features = np.random.randn(64) * np.random.uniform(0.5, 2.0)
                
                goals = [{'priority': np.random.uniform(0.3, 0.9), 'urgency': np.random.uniform(0.2, 0.8)}]
                salience = self.attention.calculate_salience(stimulus_id, features, goals)
                stimuli[stimulus_id] = salience
            
            # Allocate attention
            attention_allocations = self.attention.allocate_attention(stimuli)
            
            # Measure integration
            attention_entropy = entropy(list(attention_allocations.values()))
            
            results.append({
                'scenario': scenario,
                'phi': phi,
                'attention_entropy': attention_entropy,
                'n_stimuli': len(stimuli),
                'max_attention': max(attention_allocations.values()) if attention_allocations else 0
            })
        
        # Analyze results
        phi_values = [r['phi'] for r in results]
        entropy_values = [r['attention_entropy'] for r in results]
        
        # Basic validation
        assert all(p >= 0 for p in phi_values), "All phi values should be non-negative"
        assert all(e >= 0 for e in entropy_values), "All entropy values should be non-negative"
        
        # Check for reasonable variation
        phi_std = np.std(phi_values)
        entropy_std = np.std(entropy_values)
        
        assert phi_std > 0, "Phi values should show variation across scenarios"
        
        logger.info(f"Integration metrics test completed. Phi range: {min(phi_values):.3f}-{max(phi_values):.3f}")
        
        return results
    
    def test_temporal_dynamics(self):
        """Test temporal dynamics of consciousness components."""
        logger.info("Testing temporal dynamics")
        
        # Initialize systems
        self.integrated_info.initialize_system(n_nodes=6)
        
        # Create time series of states
        n_timesteps = 50
        phi_history = []
        attention_history = []
        workspace_history = []
        
        for t in range(n_timesteps):
            # Evolving system state
            system_state = np.sin(t * 0.1) * np.random.randn(6) + np.cos(t * 0.05) * 0.5
            
            # Calculate phi
            phi = self.integrated_info.compute_phi(system_state)
            phi_history.append(phi)
            
            # Create time-varying stimuli
            stimulus_features = np.random.randn(64) * (1 + 0.1 * np.sin(t * 0.2))
            salience = self.attention.calculate_salience(f"stimulus_{t}", stimulus_features, [])
            
            # Update attention dynamics
            self.attention.allocate_attention({f"stimulus_{t}": salience})
            self.attention.update_attention_dynamics(dt=1.0)
            
            attention_summary = self.attention.get_attention_summary()
            attention_history.append(attention_summary)
            
            # Workspace dynamics
            if t % 5 == 0:  # Every 5 timesteps
                coalition_id = self.global_workspace.add_competitor(f"content_{t}", stimulus_features, salience)
                winner = self.global_workspace.compete_for_access()
                if winner:
                    self.global_workspace.broadcast_to_workspace(winner)
                
                self.global_workspace.clear_competitors()
            
            self.global_workspace.update_workspace_dynamics(dt=1.0)
            workspace_state = self.global_workspace.get_workspace_state()
            workspace_history.append(workspace_state)
        
        # Analyze temporal patterns
        phi_array = np.array(phi_history)
        
        # Check for temporal coherence
        phi_autocorr = np.corrcoef(phi_array[:-1], phi_array[1:])[0, 1]
        
        # Attention entropy over time
        attention_entropies = [h['attention_entropy'] for h in attention_history]
        
        # Workspace utilization over time
        workspace_utilizations = [h['capacity_utilization'] for h in workspace_history]
        
        # Validate temporal properties
        assert len(phi_history) == n_timesteps, "Should have phi for each timestep"
        assert not np.isnan(phi_autocorr), "Phi autocorrelation should be valid"
        
        logger.info(f"Temporal dynamics test completed. Phi autocorr: {phi_autocorr:.3f}")
        
        return {
            'phi_history': phi_history,
            'attention_history': attention_history,
            'workspace_history': workspace_history,
            'phi_autocorrelation': phi_autocorr
        }

# ============================================================================
# Test Execution and Validation
# ============================================================================

def run_comprehensive_consciousness_tests():
    """Run comprehensive test suite for consciousness lattice."""
    
    logger.info("=" * 80)
    logger.info("ULTRA Emergent Consciousness Lattice - Comprehensive Test Suite")
    logger.info("=" * 80)
    
    # Initialize test system
    test_system = TestEmergentConsciousnessIntegration()
    
    try:
        # Test 1: Full consciousness cycle
        logger.info("\n" + "="*60)
        logger.info("TEST 1: Full Consciousness Processing Cycle")
        logger.info("="*60)
        
        cycle_results = test_system.test_full_consciousness_cycle()
        
        print(f"✓ Salience calculation: {cycle_results['salience']:.4f}")
        print(f"✓ Integrated information (Φ): {cycle_results['phi']:.4f}")
        print(f"✓ Attention allocation: {len(cycle_results['attention_allocation'])} stimuli")
        print(f"✓ Workspace active contents: {cycle_results['workspace_state']['active_contents']}")
        print(f"✓ Identified limitations: {len(cycle_results['limitations'])}")
        
        # Test 2: Integration metrics
        logger.info("\n" + "="*60)
        logger.info("TEST 2: Consciousness Integration Metrics")
        logger.info("="*60)
        
        integration_results = test_system.test_consciousness_integration_metrics()
        
        phi_values = [r['phi'] for r in integration_results]
        entropy_values = [r['attention_entropy'] for r in integration_results]
        
        print(f"✓ Φ range across scenarios: {min(phi_values):.4f} - {max(phi_values):.4f}")
        print(f"✓ Attention entropy range: {min(entropy_values):.4f} - {max(entropy_values):.4f}")
        print(f"✓ Mean Φ: {np.mean(phi_values):.4f} ± {np.std(phi_values):.4f}")
        
        # Test 3: Temporal dynamics
        logger.info("\n" + "="*60)
        logger.info("TEST 3: Temporal Dynamics")
        logger.info("="*60)
        
        temporal_results = test_system.test_temporal_dynamics()
        
        print(f"✓ Φ temporal autocorrelation: {temporal_results['phi_autocorrelation']:.4f}")
        print(f"✓ Temporal sequence length: {len(temporal_results['phi_history'])}")
        print(f"✓ Attention dynamics tracked: {len(temporal_results['attention_history'])}")
        print(f"✓ Workspace dynamics tracked: {len(temporal_results['workspace_history'])}")
        
        # Additional component-specific tests
        logger.info("\n" + "="*60)
        logger.info("TEST 4: Component-Specific Validation")
        logger.info("="*60)
        
        # Self-awareness validation
        self_description = test_system.self_awareness.generate_self_description()
        print("✓ Self-awareness description generated:")
        print(f"  {self_description[:100]}...")
        
        # Intentionality validation
        intentions = test_system.intentionality.get_current_intentions()
        print(f"✓ Active intentions: {len(intentions)}")
        
        # IIT validation
        phi_stats = test_system.integrated_info.get_phi_statistics()
        print(f"✓ Φ statistics: mean={phi_stats['mean']:.4f}, trend={phi_stats['trend']:.4f}")
        
        # Attention validation
        attention_summary = test_system.attention.get_attention_summary()
        print(f"✓ Attention summary: {attention_summary['total_stimuli']} stimuli, "
              f"entropy={attention_summary['attention_entropy']:.4f}")
        
        # Global workspace validation
        workspace_state = test_system.global_workspace.get_workspace_state()
        print(f"✓ Global workspace: {workspace_state['active_contents']} active contents, "
              f"{workspace_state['capacity_utilization']:.2%} capacity used")
        
        logger.info("\n" + "="*80)
        logger.info("ALL TESTS COMPLETED SUCCESSFULLY!")
        logger.info("="*80)
        
        # Return comprehensive results
        return {
            'cycle_results': cycle_results,
            'integration_results': integration_results,
            'temporal_results': temporal_results,
            'component_states': {
                'self_awareness': test_system.self_awareness.capability_model,
                'intentionality': len(test_system.intentionality.goals),
                'phi_stats': phi_stats,
                'attention_summary': attention_summary,
                'workspace_state': workspace_state
            }
        }
        
    except Exception as e:
        logger.error(f"Test failed with error: {str(e)}")
        raise

# ============================================================================
# Main Execution (continued)
# ============================================================================

if __name__ == "__main__":
    # Execute comprehensive test suite
    results = run_comprehensive_consciousness_tests()
    
    # Generate detailed report
    print("\n" + "="*80)
    print("DETAILED PERFORMANCE ANALYSIS")
    print("="*80)
    
    # Performance metrics
    phi_values = [r['phi'] for r in results['integration_results']]
    print(f"\nIntegrated Information (Φ) Analysis:")
    print(f"  Mean: {np.mean(phi_values):.6f}")
    print(f"  Std:  {np.std(phi_values):.6f}")
    print(f"  Min:  {np.min(phi_values):.6f}")
    print(f"  Max:  {np.max(phi_values):.6f}")
    
    # Attention analysis
    attention_summary = results['component_states']['attention_summary']
    print(f"\nAttentional Awareness Analysis:")
    print(f"  Active stimuli: {attention_summary['total_stimuli']}")
    print(f"  Attention entropy: {attention_summary['attention_entropy']:.6f}")
    print(f"  Mean allocation: {attention_summary.get('mean_allocation', 0):.6f}")
    
    # Workspace analysis
    workspace_state = results['component_states']['workspace_state']
    print(f"\nGlobal Workspace Analysis:")
    print(f"  Capacity utilization: {workspace_state['capacity_utilization']:.2%}")
    print(f"  Active contents: {workspace_state['active_contents']}")
    print(f"  Registered targets: {workspace_state['registered_targets']}")

# ============================================================================
# Performance Benchmarking Utilities
# ============================================================================

class ConsciousnessBenchmark:
    """Performance benchmarking suite for consciousness components."""
    
    def __init__(self, config: ConsciousnessConfig):
        self.config = config
        self.benchmark_results = {}
        
    def benchmark_self_awareness(self, n_updates: int = 1000) -> Dict[str, float]:
        """Benchmark self-awareness module performance."""
        logger.info(f"Benchmarking self-awareness with {n_updates} updates")
        
        self_awareness = SelfAwarenessModule(self.config)
        
        # Timing capability model updates
        start_time = time.time()
        
        for i in range(n_updates):
            task_result = {
                'task_type': np.random.choice(list(self_awareness.capability_model.keys())),
                'performance': np.random.uniform(0.3, 0.95),
                'confidence': np.random.uniform(0.4, 0.9)
            }
            self_awareness.update_capability_model(task_result)
        
        update_time = time.time() - start_time
        
        # Timing self-description generation
        start_time = time.time()
        description = self_awareness.generate_self_description()
        description_time = time.time() - start_time
        
        # Timing feature computation
        start_time = time.time()
        features = self_awareness.compute_metacognitive_features()
        features_time = time.time() - start_time
        
        results = {
            'updates_per_second': n_updates / update_time,
            'description_generation_time': description_time,
            'feature_computation_time': features_time,
            'total_capabilities': len(self_awareness.capability_model),
            'memory_usage_mb': self._estimate_memory_usage(self_awareness)
        }
        
        self.benchmark_results['self_awareness'] = results
        logger.info(f"Self-awareness benchmark: {results['updates_per_second']:.1f} updates/sec")
        
        return results
    
    def benchmark_intentionality(self, n_goals: int = 500) -> Dict[str, float]:
        """Benchmark intentionality system performance."""
        logger.info(f"Benchmarking intentionality with {n_goals} goals")
        
        intentionality = IntentionalitySystem(self.config)
        
        # Timing goal creation
        start_time = time.time()
        goal_ids = []
        
        for i in range(n_goals):
            goal_description = f"Test goal {i}: {np.random.choice(['learn', 'solve', 'optimize', 'create'])} something"
            priority = np.random.uniform(0.1, 1.0)
            goal_id = intentionality.set_goal(goal_description, priority)
            goal_ids.append(goal_id)
        
        goal_creation_time = time.time() - start_time
        
        # Timing intention computation
        start_time = time.time()
        intentions = intentionality.get_current_intentions()
        intention_computation_time = time.time() - start_time
        
        # Timing progress updates
        start_time = time.time()
        for goal_id in goal_ids[:100]:  # Update first 100
            intentionality.update_goal_progress(goal_id, np.random.uniform(0.0, 1.0))
        progress_update_time = time.time() - start_time
        
        results = {
            'goals_per_second': n_goals / goal_creation_time,
            'intention_computation_time': intention_computation_time,
            'progress_updates_per_second': 100 / progress_update_time,
            'active_goals': len([g for g in intentionality.goals.values() if g['status'] == 'active']),
            'memory_usage_mb': self._estimate_memory_usage(intentionality)
        }
        
        self.benchmark_results['intentionality'] = results
        logger.info(f"Intentionality benchmark: {results['goals_per_second']:.1f} goals/sec")
        
        return results
    
    def benchmark_integrated_information(self, n_computations: int = 100) -> Dict[str, float]:
        """Benchmark integrated information computation."""
        logger.info(f"Benchmarking IIT with {n_computations} computations")
        
        iit = IntegratedInformationMatrix(self.config)
        
        # Test different system sizes
        system_sizes = [4, 6, 8, 10, 12]
        phi_computation_times = []
        
        for size in system_sizes:
            iit.initialize_system(n_nodes=size)
            
            start_time = time.time()
            for _ in range(n_computations // len(system_sizes)):
                system_state = np.random.randn(size)
                phi = iit.compute_phi(system_state)
            
            computation_time = time.time() - start_time
            phi_computation_times.append(computation_time)
        
        results = {
            'mean_phi_computation_time': np.mean(phi_computation_times),
            'computations_per_second': n_computations / sum(phi_computation_times),
            'max_system_size_tested': max(system_sizes),
            'scaling_efficiency': phi_computation_times[0] / phi_computation_times[-1],
            'memory_usage_mb': self._estimate_memory_usage(iit)
        }
        
        self.benchmark_results['integrated_information'] = results
        logger.info(f"IIT benchmark: {results['computations_per_second']:.1f} computations/sec")
        
        return results
    
    def benchmark_attention(self, n_stimuli: int = 1000) -> Dict[str, float]:
        """Benchmark attentional awareness performance."""
        logger.info(f"Benchmarking attention with {n_stimuli} stimuli")
        
        attention = AttentionalAwareness(self.config)
        
        # Generate test stimuli
        stimuli_features = [np.random.randn(64) for _ in range(n_stimuli)]
        test_goals = [{'priority': np.random.uniform(0.3, 0.9)} for _ in range(10)]
        
        # Timing salience calculation
        start_time = time.time()
        saliences = {}
        
        for i, features in enumerate(stimuli_features):
            stimulus_id = f"stimulus_{i}"
            salience = attention.calculate_salience(stimulus_id, features, test_goals)
            saliences[stimulus_id] = salience
        
        salience_computation_time = time.time() - start_time
        
        # Timing attention allocation
        start_time = time.time()
        allocations = attention.allocate_attention(saliences)
        allocation_time = time.time() - start_time
        
        # Timing dynamics update
        start_time = time.time()
        attention.update_attention_dynamics()
        dynamics_update_time = time.time() - start_time
        
        results = {
            'salience_computations_per_second': n_stimuli / salience_computation_time,
            'allocation_time': allocation_time,
            'dynamics_update_time': dynamics_update_time,
            'total_tracked_stimuli': len(attention.attention_allocations),
            'memory_usage_mb': self._estimate_memory_usage(attention)
        }
        
        self.benchmark_results['attention'] = results
        logger.info(f"Attention benchmark: {results['salience_computations_per_second']:.1f} salience/sec")
        
        return results
    
    def benchmark_global_workspace(self, n_competitions: int = 200) -> Dict[str, float]:
        """Benchmark global workspace performance."""
        logger.info(f"Benchmarking global workspace with {n_competitions} competitions")
        
        workspace = GlobalWorkspace(self.config)
        
        # Timing coalition formation and competition
        start_time = time.time()
        
        for i in range(n_competitions):
            # Add competitors
            n_competitors = np.random.randint(3, 8)
            for j in range(n_competitors):
                content_features = np.random.randn(64)
                strength = np.random.uniform(0.2, 0.9)
                workspace.add_competitor(f"content_{i}_{j}", content_features, strength)
            
            # Form coalitions and compete
            workspace.form_coalitions()
            winner = workspace.compete_for_access()
            
            if winner:
                workspace.broadcast_to_workspace(winner)
            
            workspace.clear_competitors()
        
        competition_time = time.time() - start_time
        
        # Timing workspace dynamics
        start_time = time.time()
        for _ in range(100):
            workspace.update_workspace_dynamics()
        dynamics_time = time.time() - start_time
        
        results = {
            'competitions_per_second': n_competitions / competition_time,
            'dynamics_updates_per_second': 100 / dynamics_time,
            'workspace_capacity': self.config.workspace_capacity,
            'registered_targets': len(workspace.broadcast_targets),
            'memory_usage_mb': self._estimate_memory_usage(workspace)
        }
        
        self.benchmark_results['global_workspace'] = results
        logger.info(f"Global workspace benchmark: {results['competitions_per_second']:.1f} competitions/sec")
        
        return results
    
    def _estimate_memory_usage(self, obj) -> float:
        """Estimate memory usage of an object in MB."""
        import sys
        
        def get_size(obj, seen=None):
            size = sys.getsizeof(obj)
            if seen is None:
                seen = set()
            
            obj_id = id(obj)
            if obj_id in seen:
                return 0
            
            seen.add(obj_id)
            
            if isinstance(obj, dict):
                size += sum([get_size(v, seen) for v in obj.values()])
                size += sum([get_size(k, seen) for k in obj.keys()])
            elif hasattr(obj, '__dict__'):
                size += get_size(obj.__dict__, seen)
            elif hasattr(obj, '__iter__') and not isinstance(obj, (str, bytes, bytearray)):
                size += sum([get_size(i, seen) for i in obj])
            
            return size
        
        return get_size(obj) / (1024 * 1024)  # Convert to MB
    
    def run_full_benchmark(self) -> Dict[str, Dict[str, float]]:
        """Run complete benchmark suite."""
        logger.info("Starting full consciousness benchmark suite")
        
        # Run all benchmarks
        self.benchmark_self_awareness(n_updates=500)
        self.benchmark_intentionality(n_goals=200)
        self.benchmark_integrated_information(n_computations=50)
        self.benchmark_attention(n_stimuli=500)
        self.benchmark_global_workspace(n_competitions=100)
        
        # Calculate overall statistics
        total_memory = sum(results.get('memory_usage_mb', 0) 
                          for results in self.benchmark_results.values())
        
        self.benchmark_results['overall'] = {
            'total_memory_usage_mb': total_memory,
            'components_tested': len(self.benchmark_results) - 1,
            'benchmark_completion_time': time.time()
        }
        
        logger.info(f"Benchmark complete. Total memory usage: {total_memory:.2f} MB")
        
        return self.benchmark_results

# ============================================================================
# Visualization Utilities
# ============================================================================

class ConsciousnessVisualizer:
    """Visualization utilities for consciousness metrics and dynamics."""
    
    def __init__(self):
        pass
    
    def plot_phi_dynamics(self, phi_history: List[float], 
                         save_path: Optional[str] = None):
        """Plot integrated information (Φ) over time."""
        plt.figure(figsize=(12, 6))
        
        plt.subplot(2, 1, 1)
        plt.plot(phi_history, 'b-', linewidth=2, alpha=0.8)
        plt.title('Integrated Information (Φ) Dynamics', fontsize=14, fontweight='bold')
        plt.ylabel('Φ Value', fontsize=12)
        plt.grid(True, alpha=0.3)
        
        # Add trend line
        x = np.arange(len(phi_history))
        z = np.polyfit(x, phi_history, 1)
        p = np.poly1d(z)
        plt.plot(x, p(x), 'r--', alpha=0.8, label=f'Trend: {z[0]:.6f}')
        plt.legend()
        
        # Distribution
        plt.subplot(2, 1, 2)
        plt.hist(phi_history, bins=30, alpha=0.7, density=True, color='skyblue')
        plt.title('Φ Distribution', fontsize=14, fontweight='bold')
        plt.xlabel('Φ Value', fontsize=12)
        plt.ylabel('Density', fontsize=12)
        plt.grid(True, alpha=0.3)
        
        # Add statistics
        mean_phi = np.mean(phi_history)
        std_phi = np.std(phi_history)
        plt.axvline(mean_phi, color='red', linestyle='--', 
                   label=f'Mean: {mean_phi:.4f}')
        plt.axvline(mean_phi + std_phi, color='orange', linestyle=':', 
                   label=f'+1σ: {mean_phi + std_phi:.4f}')
        plt.axvline(mean_phi - std_phi, color='orange', linestyle=':', 
                   label=f'-1σ: {mean_phi - std_phi:.4f}')
        plt.legend()
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        else:
            plt.show()
    
    def plot_attention_landscape(self, attention_history: List[Dict[str, Any]], 
                                save_path: Optional[str] = None):
        """Plot attention allocation landscape over time."""
        plt.figure(figsize=(15, 8))
        
        # Extract data
        timestamps = [h.get('timestamp', i) for i, h in enumerate(attention_history)]
        entropies = [h.get('attention_entropy', 0) for h in attention_history]
        total_stimuli = [h.get('total_stimuli', 0) for h in attention_history]
        max_allocations = [h.get('max_allocation', 0) for h in attention_history]
        
        # Attention entropy over time
        plt.subplot(2, 2, 1)
        plt.plot(timestamps, entropies, 'g-', linewidth=2)
        plt.title('Attention Entropy Dynamics', fontsize=12, fontweight='bold')
        plt.ylabel('Entropy', fontsize=10)
        plt.grid(True, alpha=0.3)
        
        # Number of stimuli over time
        plt.subplot(2, 2, 2)
        plt.plot(timestamps, total_stimuli, 'b-', linewidth=2)
        plt.title('Active Stimuli Count', fontsize=12, fontweight='bold')
        plt.ylabel('Count', fontsize=10)
        plt.grid(True, alpha=0.3)
        
        # Maximum attention allocation
        plt.subplot(2, 2, 3)
        plt.plot(timestamps, max_allocations, 'r-', linewidth=2)
        plt.title('Maximum Attention Allocation', fontsize=12, fontweight='bold')
        plt.ylabel('Allocation', fontsize=10)
        plt.xlabel('Time', fontsize=10)
        plt.grid(True, alpha=0.3)
        
        # Correlation analysis
        plt.subplot(2, 2, 4)
        if len(entropies) > 1 and len(total_stimuli) > 1:
            plt.scatter(total_stimuli, entropies, alpha=0.6, c=timestamps, cmap='viridis')
            plt.colorbar(label='Time')
            plt.xlabel('Total Stimuli', fontsize=10)
            plt.ylabel('Attention Entropy', fontsize=10)
            plt.title('Entropy vs Stimuli Count', fontsize=12, fontweight='bold')
            plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        else:
            plt.show()
    
    def plot_workspace_activity(self, workspace_history: List[Dict[str, Any]], 
                               save_path: Optional[str] = None):
        """Plot global workspace activity patterns."""
        plt.figure(figsize=(12, 8))
        
        # Extract data
        timestamps = [h.get('timestamp', i) for i, h in enumerate(workspace_history)]
        utilizations = [h.get('capacity_utilization', 0) for h in workspace_history]
        strengths = [h.get('total_strength', 0) for h in workspace_history]
        active_contents = [h.get('active_contents', 0) for h in workspace_history]
        
        # Capacity utilization
        plt.subplot(3, 1, 1)
        plt.fill_between(timestamps, utilizations, alpha=0.7, color='lightblue')
        plt.plot(timestamps, utilizations, 'b-', linewidth=2)
        plt.title('Global Workspace Capacity Utilization', fontsize=14, fontweight='bold')
        plt.ylabel('Utilization', fontsize=12)
        plt.ylim(0, 1)
        plt.grid(True, alpha=0.3)
        
        # Total strength
        plt.subplot(3, 1, 2)
        plt.plot(timestamps, strengths, 'r-', linewidth=2)
        plt.title('Total Workspace Strength', fontsize=14, fontweight='bold')
        plt.ylabel('Strength', fontsize=12)
        plt.grid(True, alpha=0.3)
        
        # Active contents count
        plt.subplot(3, 1, 3)
        plt.step(timestamps, active_contents, 'g-', linewidth=2, where='post')
        plt.fill_between(timestamps, active_contents, step='post', alpha=0.3, color='green')
        plt.title('Active Content Count', fontsize=14, fontweight='bold')
        plt.ylabel('Count', fontsize=12)
        plt.xlabel('Time', fontsize=12)
        plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        else:
            plt.show()
    
    def plot_consciousness_dashboard(self, test_results: Dict[str, Any], 
                                   save_path: Optional[str] = None):
        """Create comprehensive consciousness dashboard."""
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        
        # Φ distribution
        phi_values = [r['phi'] for r in test_results['integration_results']]
        axes[0, 0].hist(phi_values, bins=20, alpha=0.7, color='purple')
        axes[0, 0].set_title('Φ Distribution', fontweight='bold')
        axes[0, 0].set_xlabel('Integrated Information (Φ)')
        axes[0, 0].set_ylabel('Frequency')
        axes[0, 0].grid(True, alpha=0.3)
        
        # Attention entropy over scenarios
        entropy_values = [r['attention_entropy'] for r in test_results['integration_results']]
        axes[0, 1].plot(entropy_values, 'o-', color='orange', linewidth=2, markersize=6)
        axes[0, 1].set_title('Attention Entropy by Scenario', fontweight='bold')
        axes[0, 1].set_xlabel('Scenario')
        axes[0, 1].set_ylabel('Entropy')
        axes[0, 1].grid(True, alpha=0.3)
        
        # Φ temporal dynamics
        phi_history = test_results['temporal_results']['phi_history']
        axes[0, 2].plot(phi_history, color='blue', linewidth=2)
        axes[0, 2].set_title('Φ Temporal Dynamics', fontweight='bold')
        axes[0, 2].set_xlabel('Time Step')
        axes[0, 2].set_ylabel('Φ')
        axes[0, 2].grid(True, alpha=0.3)
        
        # Workspace utilization
        workspace_history = test_results['temporal_results']['workspace_history']
        utilizations = [h['capacity_utilization'] for h in workspace_history]
        axes[1, 0].fill_between(range(len(utilizations)), utilizations, alpha=0.6, color='green')
        axes[1, 0].set_title('Workspace Utilization', fontweight='bold')
        axes[1, 0].set_xlabel('Time Step')
        axes[1, 0].set_ylabel('Utilization')
        axes[1, 0].set_ylim(0, 1)
        axes[1, 0].grid(True, alpha=0.3)
        
        # Capability distribution
        capability_model = test_results['component_states']['self_awareness']
        capabilities = list(capability_model.keys())
        performances = [capability_model[cap]['performance'] for cap in capabilities]
        uncertainties = [capability_model[cap]['uncertainty'] for cap in capabilities]
        
        x_pos = np.arange(len(capabilities))
        width = 0.35
        
        bars1 = axes[1, 1].bar(x_pos - width/2, performances, width, 
                              label='Performance', alpha=0.8, color='skyblue')
        bars2 = axes[1, 1].bar(x_pos + width/2, uncertainties, width, 
                              label='Uncertainty', alpha=0.8, color='lightcoral')
        
        axes[1, 1].set_title('Capability Assessment', fontweight='bold')
        axes[1, 1].set_xlabel('Capabilities')
        axes[1, 1].set_ylabel('Score')
        axes[1, 1].set_xticks(x_pos)
        axes[1, 1].set_xticklabels([cap[:8] + '...' if len(cap) > 8 else cap 
                                   for cap in capabilities], rotation=45)
        axes[1, 1].legend()
        axes[1, 1].grid(True, alpha=0.3)
        
        # Integration summary
        component_states = test_results['component_states']
        
        summary_data = [
            len(component_states['self_awareness']),
            component_states['intentionality'],
            component_states['phi_stats']['mean'] * 100,  # Scale for visibility
            component_states['attention_summary']['total_stimuli'],
            component_states['workspace_state']['active_contents']
        ]
        
        summary_labels = ['Capabilities', 'Goals', 'Φ×100', 'Stimuli', 'Workspace']
        
        bars = axes[1, 2].bar(summary_labels, summary_data, 
                             color=['blue', 'green', 'purple', 'orange', 'red'], alpha=0.7)
        axes[1, 2].set_title('System Integration Summary', fontweight='bold')
        axes[1, 2].set_ylabel('Count/Value')
        axes[1, 2].grid(True, alpha=0.3)
        
        # Add value labels on bars
        for bar, value in zip(bars, summary_data):
            height = bar.get_height()
            axes[1, 2].text(bar.get_x() + bar.get_width()/2., height + 0.01,
                           f'{value:.1f}', ha='center', va='bottom')
        
        plt.suptitle('ULTRA Emergent Consciousness Dashboard', 
                    fontsize=16, fontweight='bold', y=0.98)
        plt.tight_layout(rect=[0, 0.03, 1, 0.95])
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        else:
            plt.show()

# ============================================================================
# Data Export/Import Utilities
# ============================================================================

class ConsciousnessDataManager:
    """Data management utilities for consciousness system state."""
    
    def __init__(self):
        self.supported_formats = ['json', 'pickle', 'hdf5', 'npz']
    
    def export_consciousness_state(self, 
                                  self_awareness: SelfAwarenessModule,
                                  intentionality: IntentionalitySystem,
                                  integrated_info: IntegratedInformationMatrix,
                                  attention: AttentionalAwareness,
                                  workspace: GlobalWorkspace,
                                  filepath: str,
                                  format: str = 'json'):
        """Export complete consciousness state to file."""
        
        if format not in self.supported_formats:
            raise ValueError(f"Unsupported format: {format}. Supported: {self.supported_formats}")
        
        # Compile state data
        state_data = {
            'timestamp': time.time(),
            'self_awareness': {
                'capability_model': self_awareness.capability_model,
                'knowledge_state': self_awareness.knowledge_state,
                'performance_history': list(self_awareness.performance_history),
                'metacognitive_state': self_awareness.metacognitive_state
            },
            'intentionality': {
                'goals': intentionality.goals,
                'goal_hierarchy': intentionality.goal_hierarchy,
                'plans': intentionality.plans,
                'completed_goals': list(intentionality.completed_goals)
            },
            'integrated_information': {
                'phi_history': list(integrated_info.phi_history),
                'system_nodes': integrated_info.system_nodes,
                'connectivity_matrix': integrated_info.connectivity_matrix.tolist() 
                                      if integrated_info.connectivity_matrix is not None else None,
                'node_states': integrated_info.node_states.tolist() 
                              if integrated_info.node_states is not None else None
            },
            'attention': {
                'attention_allocations': attention.attention_allocations,
                'salience_scores': attention.salience_scores,
                'processing_enhancements': attention.processing_enhancements,
                'habituation_tracker': dict(attention.habituation_tracker)
            },
            'workspace': {
                'workspace_contents': workspace.workspace_contents,
                'workspace_history': list(workspace.workspace_history),
                'broadcast_history': list(workspace.broadcast_history),
                'broadcast_targets': list(workspace.broadcast_targets)
            }
        }
        
        # Export based on format
        if format == 'json':
            import json
            with open(filepath, 'w') as f:
                json.dump(state_data, f, indent=2, default=str)
                
        elif format == 'pickle':
            import pickle
            with open(filepath, 'wb') as f:
                pickle.dump(state_data, f)
                
        elif format == 'hdf5':
            import h5py
            with h5py.File(filepath, 'w') as f:
                self._save_dict_to_hdf5(f, state_data)
                
        elif format == 'npz':
            # Convert to numpy arrays where possible
            np_data = self._convert_to_numpy_dict(state_data)
            np.savez_compressed(filepath, **np_data)
        
        logger.info(f"Consciousness state exported to {filepath} ({format})")
    
    def import_consciousness_state(self, filepath: str, format: str = 'json') -> Dict[str, Any]:
        """Import consciousness state from file."""
        
        if format not in self.supported_formats:
            raise ValueError(f"Unsupported format: {format}. Supported: {self.supported_formats}")
        
        if format == 'json':
            import json
            with open(filepath, 'r') as f:
                state_data = json.load(f)
                
        elif format == 'pickle':
            import pickle
            with open(filepath, 'rb') as f:
                state_data = pickle.load(f)
                
        elif format == 'hdf5':
            import h5py
            with h5py.File(filepath, 'r') as f:
                state_data = self._load_dict_from_hdf5(f)
                
        elif format == 'npz':
            np_data = np.load(filepath, allow_pickle=True)
            state_data = self._convert_from_numpy_dict(dict(np_data))
        
        logger.info(f"Consciousness state imported from {filepath} ({format})")
        return state_data
    
    def _save_dict_to_hdf5(self, h5file, data_dict, path=''):
        """Recursively save dictionary to HDF5."""
        for key, value in data_dict.items():
            current_path = f"{path}/{key}" if path else key
            
            if isinstance(value, dict):
                self._save_dict_to_hdf5(h5file, value, current_path)
            elif isinstance(value, (list, tuple)):
                try:
                    h5file.create_dataset(current_path, data=np.array(value))
                except:
                    # Fallback for complex data structures
                    h5file.create_dataset(current_path, data=str(value))
            elif isinstance(value, np.ndarray):
                h5file.create_dataset(current_path, data=value)
            else:
                try:
                    h5file.create_dataset(current_path, data=value)
                except:
                    h5file.create_dataset(current_path, data=str(value))
    
    def _load_dict_from_hdf5(self, h5file, path=''):
        """Recursively load dictionary from HDF5."""
        data_dict = {}
        
        def visitor(name, obj):
            if isinstance(obj, h5py.Dataset):
                keys = name.split('/')
                current_dict = data_dict
                for key in keys[:-1]:
                    if key not in current_dict:
                        current_dict[key] = {}
                    current_dict = current_dict[key]
                
                try:
                    current_dict[keys[-1]] = obj[()]
                except:
                    current_dict[keys[-1]] = str(obj[()])
        
        h5file.visititems(visitor)
        return data_dict
    
    def _convert_to_numpy_dict(self, data_dict, prefix=''):
        """Convert dictionary to numpy-compatible format."""
        np_dict = {}
        
        for key, value in data_dict.items():
            np_key = f"{prefix}_{key}" if prefix else key
            
            if isinstance(value, dict):
                np_dict.update(self._convert_to_numpy_dict(value, np_key))
            elif isinstance(value, (list, tuple)):
                try:
                    np_dict[np_key] = np.array(value)
                except:
                    np_dict[np_key] = str(value)
            elif isinstance(value, np.ndarray):
                np_dict[np_key] = value
            else:
                try:
                    np_dict[np_key] = np.array([value])
                except:
                    np_dict[np_key] = str(value)
        
        return np_dict
    
    def _convert_from_numpy_dict(self, np_dict):
        """Convert numpy dictionary back to nested structure."""
        # This is a simplified conversion - full implementation would need
        # more sophisticated key parsing
        return {key.replace('_', '/'): value for key, value in np_dict.items()}

# ============================================================================
# Pytest Integration and Fixtures
# ============================================================================

@pytest.fixture
def consciousness_config():
    """Pytest fixture for consciousness configuration."""
    return ConsciousnessConfig()

@pytest.fixture
def self_awareness_module(consciousness_config):
    """Pytest fixture for self-awareness module."""
    return SelfAwarenessModule(consciousness_config)

@pytest.fixture
def intentionality_system(consciousness_config):
    """Pytest fixture for intentionality system."""
    return IntentionalitySystem(consciousness_config)

@pytest.fixture
def integrated_info_matrix(consciousness_config):
    """Pytest fixture for integrated information matrix."""
    iit = IntegratedInformationMatrix(consciousness_config)
    iit.initialize_system(n_nodes=6)
    return iit

@pytest.fixture
def attentional_awareness(consciousness_config):
    """Pytest fixture for attentional awareness."""
    return AttentionalAwareness(consciousness_config)

@pytest.fixture
def global_workspace(consciousness_config):
    """Pytest fixture for global workspace."""
    return GlobalWorkspace(consciousness_config)

@pytest.fixture
def test_integration_system():
    """Pytest fixture for full integration test system."""
    return TestEmergentConsciousnessIntegration()

# ============================================================================
# Unit Tests
# ============================================================================

class TestSelfAwarenessModule:
    """Unit tests for self-awareness module."""
    
    def test_capability_model_update(self, self_awareness_module):
        """Test capability model updating."""
        initial_performance = self_awareness_module.capability_model['logical_reasoning']['performance']
        
        task_result = {
            'task_type': 'logical_reasoning',
            'performance': 0.9,
            'confidence': 0.8
        }
        
        self_awareness_module.update_capability_model(task_result)
        
        updated_performance = self_awareness_module.capability_model['logical_reasoning']['performance']
        assert updated_performance != initial_performance
        assert 0.0 <= updated_performance <= 1.0
    
    def test_knowledge_state_update(self, self_awareness_module):
        """Test knowledge state updating."""
        initial_coverage = self_awareness_module.knowledge_state['mathematics']['coverage']
        
        self_awareness_module.update_knowledge_state('mathematics', 0.1, 0.05)
        
        updated_coverage = self_awareness_module.knowledge_state['mathematics']['coverage']
        assert updated_coverage > initial_coverage
        assert 0.0 <= updated_coverage <= 1.0
    
    def test_limitation_identification(self, self_awareness_module):
        """Test limitation identification."""
        limitations = self_awareness_module.identify_limitations()
        
        assert isinstance(limitations, list)
        for limitation in limitations:
            assert 'capability' in limitation
            assert 'performance' in limitation
            assert 'uncertainty' in limitation
            assert 'severity' in limitation
    
    def test_self_description_generation(self, self_awareness_module):
        """Test self-description generation."""
        description = self_awareness_module.generate_self_description()
        
        assert isinstance(description, str)
        assert len(description) > 0
        assert 'ULTRA' in description
    
    def test_metacognitive_features(self, self_awareness_module):
        """Test metacognitive feature computation."""
        features = self_awareness_module.compute_metacognitive_features()
        
        assert isinstance(features, np.ndarray)
        assert len(features) > 0
        assert not np.any(np.isnan(features))

class TestIntentionalitySystem:
    """Unit tests for intentionality system."""
    
    def test_goal_creation(self, intentionality_system):
        """Test goal creation and management."""
        initial_goal_count = len(intentionality_system.goals)
        
        goal_id = intentionality_system.set_goal("Test goal", priority=0.7)
        
        assert len(intentionality_system.goals) == initial_goal_count + 1
        assert goal_id in intentionality_system.goals
        assert intentionality_system.goals[goal_id]['priority'] == 0.7
    
    def test_hierarchical_goals(self, intentionality_system):
        """Test hierarchical goal structure."""
        parent_id = intentionality_system.set_goal("Parent goal", priority=0.8)
        child_id = intentionality_system.set_goal("Child goal", priority=0.6, parent_goal_id=parent_id)
        
        assert intentionality_system.goal_hierarchy[child_id]['parent'] == parent_id
        assert child_id in intentionality_system.goal_hierarchy[parent_id]['children']
    
    def test_progress_tracking(self, intentionality_system):
        """Test goal progress tracking."""
        goal_id = intentionality_system.set_goal("Progress test goal")
        
        intentionality_system.update_goal_progress(goal_id, 0.5)
        assert intentionality_system.goals[goal_id]['progress'] == 0.5
        
        intentionality_system.update_goal_progress(goal_id, 0.95)
        assert intentionality_system.goals[goal_id]['status'] == 'completed'
    
    def test_intention_computation(self, intentionality_system):
        """Test intention computation."""
        # Create test goals
        intentionality_system.set_goal("High priority goal", priority=0.9)
        intentionality_system.set_goal("Low priority goal", priority=0.3)
        
        intentions = intentionality_system.get_current_intentions()
        
        assert isinstance(intentions, list)
        assert len(intentions) >= 2
        
        # Check that intentions are sorted by strength
        if len(intentions) > 1:
            assert intentions[0]['intention_strength'] >= intentions[1]['intention_strength']

class TestIntegratedInformationMatrix:
    """Unit tests for integrated information matrix."""
    
    def test_phi_computation(self, integrated_info_matrix):
        """Test Φ computation."""
        system_state = np.random.randn(6)
        phi = integrated_info_matrix.compute_phi(system_state)
        
        assert isinstance(phi, float)
        assert phi >= 0.0
        assert not np.isnan(phi)
    
    def test_phi_history_tracking(self, integrated_info_matrix):
        """Test Φ history tracking."""
        initial_history_length = len(integrated_info_matrix.phi_history)
        
        for _ in range(5):
            system_state = np.random.randn(6)
            integrated_info_matrix.compute_phi(system_state)
        
        assert len(integrated_info_matrix.phi_history) == initial_history_length + 5
    
    def test_information_flow_update(self, integrated_info_matrix):
        """Test information flow control."""
        initial_alpha = integrated_info_matrix.alpha_ij[0, 1]
        
        integrated_info_matrix.update_information_flow(0, 1, 0.2)
        
        updated_alpha = integrated_info_matrix.alpha_ij[0, 1]
        assert updated_alpha != initial_alpha
        assert 0.0 <= updated_alpha <= 1.0
    
    def test_phi_statistics(self, integrated_info_matrix):
        """Test Φ statistics computation."""
        # Generate some history
        for _ in range(10):
            system_state = np.random.randn(6)
            integrated_info_matrix.compute_phi(system_state)
        
        stats = integrated_info_matrix.get_phi_statistics()
        
        assert isinstance(stats, dict)
        assert 'mean' in stats
        assert 'std' in stats
        assert 'max' in stats
        assert 'min' in stats
        assert 'current' in stats

class TestAttentionalAwareness:
    """Unit tests for attentional awareness."""
    
    def test_salience_calculation(self, attentional_awareness):
        """Test salience calculation."""
        stimulus_features = np.random.randn(64)
        goals = [{'priority': 0.8, 'urgency': 0.6}]
        
        salience = attentional_awareness.calculate_salience("test_stimulus", stimulus_features, goals)
        
        assert isinstance(salience, float)
        assert 0.0 <= salience <= 4.0  # Maximum possible with 4 components
    
    def test_attention_allocation(self, attentional_awareness):
        """Test attention allocation."""
        stimuli_saliences = {
            'stimulus_1': 0.8,
            'stimulus_2': 0.6,
            'stimulus_3': 0.4
        }
        
        allocations = attentional_awareness.allocate_attention(stimuli_saliences)
        
        assert len(allocations) == 3
        assert abs(sum(allocations.values()) - 1.0) < 1e-6  # Should sum to 1
        assert allocations['stimulus_1'] > allocations['stimulus_3']  # Higher salience gets more attention
    
    def test_processing_enhancement(self, attentional_awareness):
        """Test processing enhancement."""
        # Set up attention allocation
        attentional_awareness.attention_allocations['test_stimulus'] = 0.7
        
        baseline_processing = 1.0
        enhanced_processing = attentional_awareness.enhance_processing('test_stimulus', baseline_processing)
        
        assert enhanced_processing > baseline_processing
    
    def test_attention_dynamics(self, attentional_awareness):
        """Test attention dynamics update."""
        # Set up initial state
        attentional_awareness.attention_allocations['stimulus_1'] = 0.8
        attentional_awareness.salience_scores['stimulus_1'] = {'total': 0.6}
        
        initial_allocation = attentional_awareness.attention_allocations['stimulus_1']
        
        attentional_awareness.update_attention_dynamics(dt=1.0)
        
        # Allocation should change due to dynamics
        updated_allocation = attentional_awareness.attention_allocations['stimulus_1']
        # Note: allocation might increase or decrease depending on salience vs habituation

class TestGlobalWorkspace:
    """Unit tests for global workspace."""
    
    def test_competitor_addition(self, global_workspace):
        """Test adding competitors."""
        content_features = np.random.randn(64)
        
        coalition_id = global_workspace.add_competitor("test_content", content_features, 0.7)
        
        assert coalition_id in global_workspace.competing_coalitions
        assert global_workspace.competing_coalitions[coalition_id]['strength'] == 0.7
    
    def test_coalition_formation(self, global_workspace):
        """Test coalition formation."""
        # Add multiple competitors
        for i in range(3):
            content_features = np.random.randn(64)
            global_workspace.add_competitor(f"content_{i}", content_features, 0.5 + i * 0.1)
        
        initial_coalition_count = len(global_workspace.competing_coalitions)
        coalitions = global_workspace.form_coalitions()
        
        assert isinstance(coalitions, dict)
        # Coalition formation might merge or keep separate based on synergy
    
    def test_competition_for_access(self, global_workspace):
        """Test competition mechanism."""
        # Add competitors
        for i in range(3):
            content_features = np.random.randn(64)
            global_workspace.add_competitor(f"content_{i}", content_features, 0.5 + i * 0.2)
        
        winner = global_workspace.compete_for_access()
        
        # Winner might be None if no coalition exceeds threshold
        if winner is not None:
            assert winner in global_workspace.competing_coalitions
            assert winner in global_workspace.access_probabilities
    
    def test_workspace_broadcast(self, global_workspace):
        """Test workspace broadcast mechanism."""
        # Add and select winner
        content_features = np.random.randn(64)
        coalition_id = global_workspace.add_competitor("test_content", content_features, 0.9)
        
        global_workspace.broadcast_to_workspace(coalition_id)
        
        assert coalition_id in global_workspace.workspace_contents
        assert len(global_workspace.broadcast_history) > 0
    
    def test_workspace_dynamics(self, global_workspace):
        """Test workspace dynamics update."""
        # Add content to workspace
        content_features = np.random.randn(64)
        coalition_id = global_workspace.add_competitor("test_content", content_features, 0.8)
        global_workspace.broadcast_to_workspace(coalition_id)
        
        initial_strength = global_workspace.workspace_contents[coalition_id]['strength']
        
        global_workspace.update_workspace_dynamics(dt=1.0)
        
        # Strength should decay over time
        updated_strength = global_workspace.workspace_contents[coalition_id]['strength']
        assert updated_strength <= initial_strength

# ============================================================================
# Module Exports and Configuration
# ============================================================================

__all__ = [
    # Configuration
    'ConsciousnessConfig',
    
    # Core Components
    'SelfAwarenessModule',
    'IntentionalitySystem', 
    'IntegratedInformationMatrix',
    'AttentionalAwareness',
    'GlobalWorkspace',
    
    # Utilities
    'InformationTheoryUtils',
    'BayesianCalibration',
    'ConsciousnessBenchmark',
    'ConsciousnessVisualizer',
    'ConsciousnessDataManager',
    
    # Integration and Testing
    'TestEmergentConsciousnessIntegration',
    'run_comprehensive_consciousness_tests',
    
    # Test Classes
    'TestSelfAwarenessModule',
    'TestIntentionalitySystem',
    'TestIntegratedInformationMatrix', 
    'TestAttentionalAwareness',
    'TestGlobalWorkspace',
    
    # Pytest Fixtures
    'consciousness_config',
    'self_awareness_module',
    'intentionality_system',
    'integrated_info_matrix',
    'attentional_awareness',
    'global_workspace',
    'test_integration_system'
]

# Module metadata
__version__ = '1.0.0'
__author__ = 'ULTRA Development Team'
__description__ = 'Emergent Consciousness Lattice Test Suite for ULTRA System'
__license__ = 'MIT'

# Configure module-level logging
logger.info(f"ULTRA Emergent Consciousness Lattice Test Suite v{__version__} loaded successfully")
logger.info(f"Available components: {len(__all__)} exports")
logger.info("Ready for consciousness testing and validation")

# Performance monitoring
if __name__ == "__main__":
    import sys
    
    # Check if this is a direct execution or import
    if len(sys.argv) > 1 and sys.argv[1] == "--benchmark":
        # Run benchmark suite
        logger.info("Starting benchmark mode")
        config = ConsciousnessConfig()
        benchmark = ConsciousnessBenchmark(config)
        benchmark_results = benchmark.run_full_benchmark()
        
        print("\n" + "="*80)
        print("BENCHMARK RESULTS SUMMARY")
        print("="*80)
        
        for component, results in benchmark_results.items():
            if component != 'overall':
                print(f"\n{component.upper()}:")
                for metric, value in results.items():
                    if isinstance(value, float):
                        print(f"  {metric}: {value:.4f}")
                    else:
                        print(f"  {metric}: {value}")
    
    elif len(sys.argv) > 1 and sys.argv[1] == "--visualize":
        # Run with visualization
        logger.info("Starting visualization mode")
        results = run_comprehensive_consciousness_tests()
        
        visualizer = ConsciousnessVisualizer()
        
        # Generate visualizations
        visualizer.plot_phi_dynamics(results['temporal_results']['phi_history'])
        visualizer.plot_attention_landscape(results['temporal_results']['attention_history'])
        visualizer.plot_workspace_activity(results['temporal_results']['workspace_history'])
        visualizer.plot_consciousness_dashboard(results)
    
    else:
        # Standard test execution
        run_comprehensive_consciousness_tests()