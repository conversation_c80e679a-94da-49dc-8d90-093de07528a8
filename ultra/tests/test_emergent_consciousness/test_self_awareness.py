#!/usr/bin/env python3
"""
ULTRA Self-Awareness Module Comprehensive Test Suite

This module implements comprehensive tests for the Self-Awareness component of the
Emergent Consciousness Lattice, incorporating all mathematical formulations and
algorithms from the ULTRA architecture specification.

Mathematical Foundations:
- Capability Model: C = {(t_i, p_i, u_i)}
- Knowledge State: K = {(d_j, c_j, e_j)}
- Performance Updates: p_i^new = (1-α)p_i^old + α * p_observed
- Uncertainty Updates: u_i^new = (1-β)u_i^old + β * |p_i^old - p_observed|
- Confidence Calibration: Isotonic regression for confidence alignment
"""

import pytest
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Tuple, Optional, Any, Union
from dataclasses import dataclass, field
from collections import defaultdict, deque
import time
import logging
import json
import pickle
from pathlib import Path
from sklearn.isotonic import IsotonicRegression
from sklearn.metrics import mean_squared_error, accuracy_score
from scipy import stats
from scipy.stats import entropy  # Fixed: moved from scipy.special to scipy.stats
from scipy.special import softmax  # This one is correct
from scipy.signal import correlate, find_peaks
from scipy.optimize import minimize
import warnings
warnings.filterwarnings('ignore')

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class CapabilityEntry:
    """Individual capability tracking entry with uncertainty quantification."""
    task_type: str
    performance: float
    uncertainty: float
    last_updated: float
    sample_count: int = 0
    performance_history: List[float] = field(default_factory=list)
    confidence_history: List[float] = field(default_factory=list)
    
    def __post_init__(self):
        """Validate capability entry parameters."""
        if not 0.0 <= self.performance <= 1.0:
            raise ValueError(f"Performance must be in [0,1], got {self.performance}")
        if not 0.0 <= self.uncertainty <= 1.0:
            raise ValueError(f"Uncertainty must be in [0,1], got {self.uncertainty}")

@dataclass
class KnowledgeEntry:
    """Knowledge domain tracking with coverage and expertise metrics."""
    domain: str
    coverage: float  # Fraction of domain covered [0,1]
    expertise: float  # Level of expertise [0,1]
    confidence: float  # Confidence in knowledge [0,1]
    last_accessed: float
    access_count: int = 0
    knowledge_sources: List[str] = field(default_factory=list)
    
    def __post_init__(self):
        """Validate knowledge entry parameters."""
        for val, name in [(self.coverage, 'coverage'), (self.expertise, 'expertise'), 
                         (self.confidence, 'confidence')]:
            if not 0.0 <= val <= 1.0:
                raise ValueError(f"{name} must be in [0,1], got {val}")

class ConfidenceCalibrator:
    """Advanced confidence calibration using isotonic regression and ensemble methods."""
    
    def __init__(self, memory_size: int = 1000):
        self.memory_size = memory_size
        self.calibrators = {}
        self.prediction_history = defaultdict(lambda: deque(maxlen=memory_size))
        self.accuracy_history = defaultdict(lambda: deque(maxlen=memory_size))
        self.ensemble_weights = {}
        
    def add_prediction(self, domain: str, predicted_confidence: float, 
                      actual_correctness: float) -> None:
        """Add a prediction-outcome pair for calibration learning."""
        if not 0.0 <= predicted_confidence <= 1.0:
            raise ValueError("Predicted confidence must be in [0,1]")
        if not 0.0 <= actual_correctness <= 1.0:
            raise ValueError("Actual correctness must be in [0,1]")
            
        self.prediction_history[domain].append(predicted_confidence)
        self.accuracy_history[domain].append(actual_correctness)
        
        # Update calibrator if we have enough data
        if len(self.prediction_history[domain]) >= 10:
            self._update_calibrator(domain)
    
    def _update_calibrator(self, domain: str) -> None:
        """Update isotonic regression calibrator for a specific domain."""
        predictions = np.array(self.prediction_history[domain])
        accuracies = np.array(self.accuracy_history[domain])
        
        # Fit isotonic regression
        calibrator = IsotonicRegression(out_of_bounds='clip')
        try:
            calibrator.fit(predictions, accuracies)
            self.calibrators[domain] = calibrator
            
            # Compute ensemble weight based on calibration quality
            calibrated_preds = calibrator.predict(predictions)
            mse = mean_squared_error(accuracies, calibrated_preds)
            self.ensemble_weights[domain] = 1.0 / (1.0 + mse)
            
        except Exception as e:
            logger.warning(f"Failed to update calibrator for {domain}: {e}")
    
    def calibrate_confidence(self, domain: str, raw_confidence: float) -> float:
        """Calibrate raw confidence using domain-specific calibrator."""
        if domain not in self.calibrators:
            return raw_confidence
        
        try:
            calibrated = self.calibrators[domain].predict([raw_confidence])[0]
            # Ensemble with raw confidence based on calibrator quality
            weight = self.ensemble_weights.get(domain, 0.5)
            return weight * calibrated + (1 - weight) * raw_confidence
        except Exception:
            return raw_confidence
    
    def get_calibration_metrics(self, domain: str) -> Dict[str, float]:
        """Get calibration quality metrics for a domain."""
        if domain not in self.calibrators or len(self.prediction_history[domain]) < 10:
            return {"reliability": 0.0, "resolution": 0.0, "brier_score": 1.0}
        
        predictions = np.array(self.prediction_history[domain])
        accuracies = np.array(self.accuracy_history[domain])
        calibrated = self.calibrators[domain].predict(predictions)
        
        # Reliability (calibration error)
        reliability = np.mean(np.abs(calibrated - accuracies))
        
        # Resolution (how much the predictions vary)
        resolution = np.var(predictions)
        
        # Brier score
        brier_score = np.mean((predictions - accuracies) ** 2)
        
        return {
            "reliability": 1.0 - reliability,
            "resolution": resolution,
            "brier_score": 1.0 - brier_score
        }

class MetaCognitionEngine:
    """Advanced metacognitive reasoning about thinking processes."""
    
    def __init__(self):
        self.thinking_patterns = {}
        self.strategy_effectiveness = defaultdict(list)
        self.cognitive_load_history = []
        
    def analyze_thinking_pattern(self, task_type: str, reasoning_trace: List[str]) -> Dict:
        """Analyze patterns in reasoning for metacognitive insights."""
        pattern_analysis = {
            "depth": len(reasoning_trace),
            "complexity": self._compute_complexity(reasoning_trace),
            "coherence": self._compute_coherence(reasoning_trace),
            "efficiency": self._compute_efficiency(reasoning_trace),
        }
        
        self.thinking_patterns[task_type] = pattern_analysis
        return pattern_analysis
    
    def _compute_complexity(self, trace: List[str]) -> float:
        """Compute reasoning complexity based on trace characteristics."""
        if not trace:
            return 0.0
        
        # Factors: length, vocabulary diversity, logical operators
        length_factor = min(len(trace) / 10.0, 1.0)
        vocab_diversity = len(set(' '.join(trace).split())) / max(len(' '.join(trace).split()), 1)
        logical_ops = sum(1 for step in trace if any(op in step.lower() 
                         for op in ['therefore', 'because', 'if', 'then', 'since']))
        logical_factor = min(logical_ops / len(trace), 1.0)
        
        return (length_factor + vocab_diversity + logical_factor) / 3.0
    
    def _compute_coherence(self, trace: List[str]) -> float:
        """Compute reasoning coherence using semantic similarity."""
        if len(trace) < 2:
            return 1.0
        
        # Simple coherence metric based on word overlap between consecutive steps
        coherence_scores = []
        for i in range(len(trace) - 1):
            words1 = set(trace[i].lower().split())
            words2 = set(trace[i + 1].lower().split())
            overlap = len(words1.intersection(words2))
            union = len(words1.union(words2))
            coherence_scores.append(overlap / max(union, 1))
        
        return np.mean(coherence_scores)
    
    def _compute_efficiency(self, trace: List[str]) -> float:
        """Compute reasoning efficiency (inverse of redundancy)."""
        if not trace:
            return 1.0
        
        # Measure redundancy by repeated concepts
        all_words = []
        for step in trace:
            all_words.extend(step.lower().split())
        
        unique_words = len(set(all_words))
        total_words = len(all_words)
        
        return unique_words / max(total_words, 1)

class SelfAwarenessModule:
    """
    Advanced Self-Awareness Module implementing comprehensive capability tracking,
    knowledge state management, and metacognitive reasoning.
    
    Mathematical Implementation:
    - Capability updates: p_i^new = (1-α)p_i^old + α * p_observed
    - Uncertainty updates: u_i^new = (1-β)u_i^old + β * |p_i^old - p_observed|
    - Knowledge updates: c_j^new = c_j^old + γ * Δc_j
    """
    
    def __init__(self, learning_rates: Dict[str, float] = None, 
                 confidence_threshold: float = 0.7,
                 uncertainty_threshold: float = 0.3):
        """
        Initialize the Self-Awareness Module.
        
        Args:
            learning_rates: Learning rates for different update types
            confidence_threshold: Threshold for high-confidence decisions
            uncertainty_threshold: Threshold for high-uncertainty flagging
        """
        # Learning rate parameters (α, β, γ, δ from mathematical formulation)
        default_rates = {
            'performance_alpha': 0.1,
            'uncertainty_beta': 0.15,
            'coverage_gamma': 0.05,
            'expertise_delta': 0.08
        }
        self.learning_rates = learning_rates or default_rates
        
        # Thresholds
        self.confidence_threshold = confidence_threshold
        self.uncertainty_threshold = uncertainty_threshold
        
        # Core data structures
        self.capability_model: Dict[str, CapabilityEntry] = {}
        self.knowledge_state: Dict[str, KnowledgeEntry] = {}
        self.performance_history: List[Dict] = []
        
        # Advanced components
        self.confidence_calibrator = ConfidenceCalibrator()
        self.metacognition_engine = MetaCognitionEngine()
        
        # Self-model tracking
        self.self_model_version = 1
        self.last_introspection = time.time()
        self.introspection_frequency = 3600  # 1 hour
        
        # Neural network for capability prediction
        self.capability_predictor = self._build_capability_predictor()
        
        logger.info("Self-Awareness Module initialized successfully")
    
    def _build_capability_predictor(self) -> nn.Module:
        """Build neural network for predicting performance on new tasks."""
        class CapabilityPredictor(nn.Module):
            def __init__(self, input_dim: int = 64, hidden_dim: int = 128):
                super().__init__()
                self.encoder = nn.Sequential(
                    nn.Linear(input_dim, hidden_dim),
                    nn.ReLU(),
                    nn.Dropout(0.2),
                    nn.Linear(hidden_dim, hidden_dim),
                    nn.ReLU(),
                    nn.Dropout(0.2)
                )
                self.performance_head = nn.Linear(hidden_dim, 1)
                self.uncertainty_head = nn.Linear(hidden_dim, 1)
                
            def forward(self, x):
                features = self.encoder(x)
                performance = torch.sigmoid(self.performance_head(features))
                uncertainty = torch.sigmoid(self.uncertainty_head(features))
                return performance, uncertainty
        
        return CapabilityPredictor()
    
    def update_capability_model(self, task_result: Dict[str, Any]) -> None:
        """
        Update capability model based on task performance.
        
        Args:
            task_result: Dictionary containing task_type, performance, confidence, etc.
        """
        task_type = task_result['task_type']
        performance = float(task_result['performance'])
        confidence = task_result.get('confidence', 0.5)
        timestamp = time.time()
        
        # Validate inputs
        if not 0.0 <= performance <= 1.0:
            raise ValueError(f"Performance must be in [0,1], got {performance}")
        
        if task_type in self.capability_model:
            # Update existing capability using exponential moving average
            entry = self.capability_model[task_type]
            alpha = self.learning_rates['performance_alpha']
            beta = self.learning_rates['uncertainty_beta']
            
            # Performance update: p_i^new = (1-α)p_i^old + α * p_observed
            old_performance = entry.performance
            entry.performance = (1 - alpha) * old_performance + alpha * performance
            
            # Uncertainty update: u_i^new = (1-β)u_i^old + β * |p_i^old - p_observed|
            prediction_error = abs(old_performance - performance)
            entry.uncertainty = (1 - beta) * entry.uncertainty + beta * prediction_error
            
            # Update tracking
            entry.last_updated = timestamp
            entry.sample_count += 1
            entry.performance_history.append(performance)
            entry.confidence_history.append(confidence)
            
            # Keep history bounded
            if len(entry.performance_history) > 1000:
                entry.performance_history = entry.performance_history[-1000:]
                entry.confidence_history = entry.confidence_history[-1000:]
        else:
            # Create new capability entry
            initial_uncertainty = 0.5  # High initial uncertainty
            self.capability_model[task_type] = CapabilityEntry(
                task_type=task_type,
                performance=performance,
                uncertainty=initial_uncertainty,
                last_updated=timestamp,
                sample_count=1,
                performance_history=[performance],
                confidence_history=[confidence]
            )
        
        # Update confidence calibrator
        actual_correctness = 1.0 if performance > 0.7 else 0.0
        self.confidence_calibrator.add_prediction(task_type, confidence, actual_correctness)
        
        # Record in performance history
        self.performance_history.append({
            'task_type': task_type,
            'performance': performance,
            'confidence': confidence,
            'timestamp': timestamp
        })
        
        # Trigger introspection if needed
        if timestamp - self.last_introspection > self.introspection_frequency:
            self._perform_introspection()
        
        logger.debug(f"Updated capability for {task_type}: {performance:.3f} ± {self.capability_model[task_type].uncertainty:.3f}")
    
    def update_knowledge_state(self, domain: str, coverage_delta: float = 0.0,
                             expertise_delta: float = 0.0, 
                             confidence_delta: float = 0.0) -> None:
        """
        Update knowledge state for a domain.
        
        Args:
            domain: Knowledge domain identifier
            coverage_delta: Change in coverage [0,1]
            expertise_delta: Change in expertise [0,1] 
            confidence_delta: Change in confidence [0,1]
        """
        timestamp = time.time()
        gamma = self.learning_rates['coverage_gamma']
        delta = self.learning_rates['expertise_delta']
        
        if domain in self.knowledge_state:
            entry = self.knowledge_state[domain]
            
            # Knowledge updates: c_j^new = c_j^old + γ * Δc_j
            entry.coverage = np.clip(entry.coverage + gamma * coverage_delta, 0.0, 1.0)
            entry.expertise = np.clip(entry.expertise + delta * expertise_delta, 0.0, 1.0)
            entry.confidence = np.clip(entry.confidence + confidence_delta * 0.1, 0.0, 1.0)
            
            entry.last_accessed = timestamp
            entry.access_count += 1
        else:
            # Initialize new knowledge domain
            self.knowledge_state[domain] = KnowledgeEntry(
                domain=domain,
                coverage=max(0.1 + gamma * coverage_delta, 0.0),
                expertise=max(0.1 + delta * expertise_delta, 0.0),
                confidence=max(0.5 + confidence_delta * 0.1, 0.0),
                last_accessed=timestamp,
                access_count=1
            )
        
        logger.debug(f"Updated knowledge state for {domain}")
    
    def get_calibrated_confidence(self, task_type: str, raw_confidence: float) -> float:
        """Get calibrated confidence for a task type."""
        return self.confidence_calibrator.calibrate_confidence(task_type, raw_confidence)
    
    def identify_limitations(self) -> List[Dict[str, Any]]:
        """
        Identify system limitations based on capability model.
        
        Returns:
            List of limitation descriptions with severity scores
        """
        limitations = []
        
        for task_type, entry in self.capability_model.items():
            # Performance-based limitations
            if entry.performance < 0.6:
                severity = 1.0 - entry.performance
                limitations.append({
                    'type': 'performance',
                    'task_type': task_type,
                    'severity': severity,
                    'description': f"Low performance in {task_type}: {entry.performance:.2f}",
                    'confidence': 1.0 - entry.uncertainty
                })
            
            # Uncertainty-based limitations
            if entry.uncertainty > self.uncertainty_threshold:
                limitations.append({
                    'type': 'uncertainty',
                    'task_type': task_type,
                    'severity': entry.uncertainty,
                    'description': f"High uncertainty in {task_type}: {entry.uncertainty:.2f}",
                    'confidence': 0.8
                })
        
        # Knowledge gaps
        for domain, entry in self.knowledge_state.items():
            if entry.coverage < 0.5:
                severity = 1.0 - entry.coverage
                limitations.append({
                    'type': 'knowledge_gap',
                    'domain': domain,
                    'severity': severity,
                    'description': f"Limited knowledge coverage in {domain}: {entry.coverage:.2f}",
                    'confidence': entry.confidence
                })
        
        # Sort by severity
        limitations.sort(key=lambda x: x['severity'], reverse=True)
        
        return limitations
    
    def get_capability_estimate(self, task_type: str) -> Tuple[float, float]:
        """
        Get performance estimate and uncertainty for a task type.
        
        Returns:
            Tuple of (performance_estimate, uncertainty)
        """
        if task_type in self.capability_model:
            entry = self.capability_model[task_type]
            return entry.performance, entry.uncertainty
        else:
            # Use neural predictor for unknown tasks
            return self._predict_capability(task_type)
    
    def _predict_capability(self, task_type: str) -> Tuple[float, float]:
        """Predict capability for unknown task using neural network."""
        # Simple encoding of task type (in practice, would use more sophisticated embedding)
        task_encoding = torch.randn(1, 64)  # Placeholder encoding
        
        with torch.no_grad():
            performance, uncertainty = self.capability_predictor(task_encoding)
            return float(performance.item()), float(uncertainty.item())
    
    def generate_self_description(self) -> str:
        """
        Generate comprehensive self-description including capabilities and limitations.
        
        Returns:
            Natural language self-description
        """
        if not self.capability_model and not self.knowledge_state:
            return "I am still learning about my capabilities and have limited self-awareness."
        
        # Analyze capabilities
        capabilities = sorted(self.capability_model.items(), 
                            key=lambda x: x[1].performance, reverse=True)
        
        # Get top capabilities
        top_capabilities = capabilities[:3]
        strengths_text = []
        for task_type, entry in top_capabilities:
            confidence_level = "high" if entry.uncertainty < 0.2 else "moderate"
            strengths_text.append(f"{task_type} (performance: {entry.performance:.2f}, "
                                f"confidence: {confidence_level})")
        
        # Identify limitations
        limitations = self.identify_limitations()[:3]  # Top 3 limitations
        
        # Knowledge summary
        knowledge_domains = list(self.knowledge_state.keys())
        avg_coverage = np.mean([entry.coverage for entry in self.knowledge_state.values()]) if self.knowledge_state else 0.0
        
        # Construct description
        description = "I am an AI system with evolving self-awareness. "
        
        if strengths_text:
            description += f"My strongest capabilities include: {', '.join(strengths_text)}. "
        
        if limitations:
            limit_descriptions = [lim['description'] for lim in limitations]
            description += f"I have identified limitations in: {'; '.join(limit_descriptions)}. "
        
        if knowledge_domains:
            description += f"I have knowledge across {len(knowledge_domains)} domains "
            description += f"with average coverage of {avg_coverage:.1%}. "
        
        # Metacognitive insights
        description += f"I have processed {len(self.performance_history)} tasks and "
        description += f"am continuously updating my self-model (version {self.self_model_version})."
        
        return description
    
    def _perform_introspection(self) -> Dict[str, Any]:
        """
        Perform deep introspective analysis of capabilities and knowledge.
        
        Returns:
            Introspection results dictionary
        """
        logger.info("Performing introspective analysis...")
        
        introspection_results = {
            'timestamp': time.time(),
            'version': self.self_model_version,
            'capability_analysis': {},
            'knowledge_analysis': {},
            'metacognitive_insights': {},
            'improvement_recommendations': []
        }
        
        # Capability analysis
        if self.capability_model:
            performances = [entry.performance for entry in self.capability_model.values()]
            uncertainties = [entry.uncertainty for entry in self.capability_model.values()]
            
            introspection_results['capability_analysis'] = {
                'num_capabilities': len(self.capability_model),
                'avg_performance': np.mean(performances),
                'std_performance': np.std(performances),
                'avg_uncertainty': np.mean(uncertainties),
                'performance_trend': self._compute_performance_trend(),
                'capability_diversity': self._compute_capability_diversity()
            }
        
        # Knowledge analysis
        if self.knowledge_state:
            coverages = [entry.coverage for entry in self.knowledge_state.values()]
            expertises = [entry.expertise for entry in self.knowledge_state.values()]
            
            introspection_results['knowledge_analysis'] = {
                'num_domains': len(self.knowledge_state),
                'avg_coverage': np.mean(coverages),
                'avg_expertise': np.mean(expertises),
                'knowledge_breadth': len(self.knowledge_state),
                'knowledge_depth': np.mean(expertises)
            }
        
        # Metacognitive insights
        recent_tasks = self.performance_history[-100:] if len(self.performance_history) > 100 else self.performance_history
        if recent_tasks:
            task_types = [task['task_type'] for task in recent_tasks]
            performances = [task['performance'] for task in recent_tasks]
            
            introspection_results['metacognitive_insights'] = {
                'task_diversity': len(set(task_types)),
                'recent_avg_performance': np.mean(performances),
                'learning_velocity': self._compute_learning_velocity(),
                'adaptation_rate': self._compute_adaptation_rate()
            }
        
        # Generate improvement recommendations
        recommendations = self._generate_improvement_recommendations(introspection_results)
        introspection_results['improvement_recommendations'] = recommendations
        
        # Update introspection tracking
        self.last_introspection = time.time()
        self.self_model_version += 1
        
        logger.info(f"Introspection complete. Self-model updated to version {self.self_model_version}")
        
        return introspection_results
    
    def _compute_performance_trend(self) -> float:
        """Compute performance trend over recent tasks."""
        if len(self.performance_history) < 10:
            return 0.0
        
        recent_performances = [task['performance'] for task in self.performance_history[-50:]]
        
        # Simple linear trend
        x = np.arange(len(recent_performances))
        slope, _, _, _, _ = stats.linregress(x, recent_performances)
        
        return float(slope)
    
    def _compute_capability_diversity(self) -> float:
        """Compute diversity of capabilities using entropy."""
        if not self.capability_model:
            return 0.0
        
        performances = [entry.performance for entry in self.capability_model.values()]
        # Bin performances to compute entropy
        hist, _ = np.histogram(performances, bins=10, density=True)
        hist = hist + 1e-10  # Avoid log(0)
        return float(entropy(hist))
    
    def _compute_learning_velocity(self) -> float:
        """Compute rate of learning improvement."""
        if len(self.performance_history) < 20:
            return 0.0
        
        # Compare first and second half performance
        mid = len(self.performance_history) // 2
        first_half = np.mean([task['performance'] for task in self.performance_history[:mid]])
        second_half = np.mean([task['performance'] for task in self.performance_history[mid:]])
        
        return float(second_half - first_half)
    
    def _compute_adaptation_rate(self) -> float:
        """Compute how quickly the system adapts to new task types."""
        if len(self.performance_history) < 10:
            return 0.0
        
        # Group by task type and compute improvement rate
        task_improvements = []
        task_groups = defaultdict(list)
        
        for task in self.performance_history:
            task_groups[task['task_type']].append(task['performance'])
        
        for task_type, performances in task_groups.items():
            if len(performances) >= 3:
                # Linear trend for this task type
                x = np.arange(len(performances))
                slope, _, _, _, _ = stats.linregress(x, performances)
                task_improvements.append(slope)
        
        return float(np.mean(task_improvements)) if task_improvements else 0.0
    
    def _generate_improvement_recommendations(self, introspection: Dict) -> List[str]:
        """Generate specific recommendations for improvement."""
        recommendations = []
        
        cap_analysis = introspection.get('capability_analysis', {})
        know_analysis = introspection.get('knowledge_analysis', {})
        meta_insights = introspection.get('metacognitive_insights', {})
        
        # Performance-based recommendations
        if cap_analysis.get('avg_performance', 0) < 0.7:
            recommendations.append("Focus on improving overall performance through additional training")
        
        if cap_analysis.get('avg_uncertainty', 1) > 0.4:
            recommendations.append("Reduce uncertainty through more diverse task exposure")
        
        # Knowledge-based recommendations
        if know_analysis.get('avg_coverage', 0) < 0.6:
            recommendations.append("Expand knowledge coverage in existing domains")
        
        if know_analysis.get('num_domains', 0) < 5:
            recommendations.append("Explore new knowledge domains to increase breadth")
        
        # Learning-based recommendations
        if meta_insights.get('learning_velocity', 0) < 0.01:
            recommendations.append("Investigate learning bottlenecks and optimize learning algorithms")
        
        if meta_insights.get('task_diversity', 0) < 3:
            recommendations.append("Increase task diversity to improve generalization")
        
        return recommendations
    
    def save_state(self, filepath: str) -> None:
        """Save complete self-awareness state to file."""
        state = {
            'capability_model': {k: {
                'task_type': v.task_type,
                'performance': v.performance,
                'uncertainty': v.uncertainty,
                'last_updated': v.last_updated,
                'sample_count': v.sample_count,
                'performance_history': v.performance_history,
                'confidence_history': v.confidence_history
            } for k, v in self.capability_model.items()},
            'knowledge_state': {k: {
                'domain': v.domain,
                'coverage': v.coverage,
                'expertise': v.expertise,
                'confidence': v.confidence,
                'last_accessed': v.last_accessed,
                'access_count': v.access_count,
                'knowledge_sources': v.knowledge_sources
            } for k, v in self.knowledge_state.items()},
            'performance_history': self.performance_history,
            'self_model_version': self.self_model_version,
            'learning_rates': self.learning_rates
        }
        
        with open(filepath, 'wb') as f:
            pickle.dump(state, f)
        
        logger.info(f"Self-awareness state saved to {filepath}")
    
    def load_state(self, filepath: str) -> None:
        """Load self-awareness state from file."""
        with open(filepath, 'rb') as f:
            state = pickle.load(f)
        
        # Restore capability model
        self.capability_model = {}
        for k, v in state['capability_model'].items():
            self.capability_model[k] = CapabilityEntry(**v)
        
        # Restore knowledge state
        self.knowledge_state = {}
        for k, v in state['knowledge_state'].items():
            self.knowledge_state[k] = KnowledgeEntry(**v)
        
        self.performance_history = state['performance_history']
        self.self_model_version = state['self_model_version']
        self.learning_rates = state['learning_rates']
        
        logger.info(f"Self-awareness state loaded from {filepath}")


# ======================== COMPREHENSIVE TEST SUITE ========================

class TestSelfAwarenessModule:
    """Comprehensive test suite for Self-Awareness Module."""
    
    @pytest.fixture
    def self_awareness_module(self):
        """Create a fresh Self-Awareness Module for testing."""
        return SelfAwarenessModule()
    
    @pytest.fixture
    def sample_task_results(self):
        """Generate sample task results for testing."""
        return [
            {'task_type': 'language_understanding', 'performance': 0.85, 'confidence': 0.8},
            {'task_type': 'mathematical_reasoning', 'performance': 0.72, 'confidence': 0.7},
            {'task_type': 'visual_recognition', 'performance': 0.91, 'confidence': 0.9},
            {'task_type': 'logical_inference', 'performance': 0.68, 'confidence': 0.6},
            {'task_type': 'creative_writing', 'performance': 0.77, 'confidence': 0.75}
        ]
    
    def test_initialization(self, self_awareness_module):
        """Test proper initialization of Self-Awareness Module."""
        sam = self_awareness_module
        
        # Check initial state
        assert len(sam.capability_model) == 0
        assert len(sam.knowledge_state) == 0
        assert len(sam.performance_history) == 0
        assert sam.self_model_version == 1
        
        # Check learning rates
        assert 'performance_alpha' in sam.learning_rates
        assert 'uncertainty_beta' in sam.learning_rates
        assert 'coverage_gamma' in sam.learning_rates
        assert 'expertise_delta' in sam.learning_rates
        
        # Check components initialization
        assert sam.confidence_calibrator is not None
        assert sam.metacognition_engine is not None
        assert sam.capability_predictor is not None
    
    def test_capability_model_updates(self, self_awareness_module, sample_task_results):
        """Test capability model updates with mathematical formulations."""
        sam = self_awareness_module
        
        # Test first task result
        first_task = sample_task_results[0]
        sam.update_capability_model(first_task)
        
        # Verify new capability entry
        task_type = first_task['task_type']
        assert task_type in sam.capability_model
        entry = sam.capability_model[task_type]
        
        assert entry.task_type == task_type
        assert abs(entry.performance - first_task['performance']) < 1e-6
        assert entry.uncertainty == 0.5  # Initial uncertainty
        assert entry.sample_count == 1
        assert len(entry.performance_history) == 1
        
        # Test update with second result for same task
        second_result = {'task_type': task_type, 'performance': 0.90, 'confidence': 0.85}
        sam.update_capability_model(second_result)
        
        # Verify mathematical update formulas
        alpha = sam.learning_rates['performance_alpha']
        beta = sam.learning_rates['uncertainty_beta']
        
        expected_performance = (1 - alpha) * first_task['performance'] + alpha * second_result['performance']
        prediction_error = abs(first_task['performance'] - second_result['performance'])
        expected_uncertainty = (1 - beta) * 0.5 + beta * prediction_error
        
        updated_entry = sam.capability_model[task_type]
        assert abs(updated_entry.performance - expected_performance) < 1e-6
        assert abs(updated_entry.uncertainty - expected_uncertainty) < 1e-6
        assert updated_entry.sample_count == 2
        assert len(updated_entry.performance_history) == 2
    
    def test_knowledge_state_updates(self, self_awareness_module):
        """Test knowledge state updates with mathematical formulations."""
        sam = self_awareness_module
        
        # Test initial knowledge domain creation
        domain = "machine_learning"
        coverage_delta = 0.3
        expertise_delta = 0.2
        confidence_delta = 0.1
        
        sam.update_knowledge_state(domain, coverage_delta, expertise_delta, confidence_delta)
        
        # Verify knowledge entry creation
        assert domain in sam.knowledge_state
        entry = sam.knowledge_state[domain]
        
        gamma = sam.learning_rates['coverage_gamma']
        delta = sam.learning_rates['expertise_delta']
        
        expected_coverage = max(0.1 + gamma * coverage_delta, 0.0)
        expected_expertise = max(0.1 + delta * expertise_delta, 0.0)
        expected_confidence = max(0.5 + confidence_delta * 0.1, 0.0)
        
        assert abs(entry.coverage - expected_coverage) < 1e-6
        assert abs(entry.expertise - expected_expertise) < 1e-6
        assert abs(entry.confidence - expected_confidence) < 1e-6
        assert entry.access_count == 1
        
        # Test knowledge update
        initial_coverage = entry.coverage
        initial_expertise = entry.expertise
        
        coverage_delta2 = 0.1
        expertise_delta2 = 0.15
        
        sam.update_knowledge_state(domain, coverage_delta2, expertise_delta2)
        
        # Verify mathematical updates
        updated_entry = sam.knowledge_state[domain]
        expected_new_coverage = np.clip(initial_coverage + gamma * coverage_delta2, 0.0, 1.0)
        expected_new_expertise = np.clip(initial_expertise + delta * expertise_delta2, 0.0, 1.0)
        
        assert abs(updated_entry.coverage - expected_new_coverage) < 1e-6
        assert abs(updated_entry.expertise - expected_new_expertise) < 1e-6
        assert updated_entry.access_count == 2
    
    def test_confidence_calibration(self, self_awareness_module):
        """Test confidence calibration system."""
        sam = self_awareness_module
        calibrator = sam.confidence_calibrator
        
        # Add prediction-outcome pairs
        domain = "test_domain"
        predictions = [0.9, 0.8, 0.7, 0.6, 0.5, 0.4, 0.3, 0.2, 0.1]
        outcomes = [1.0, 1.0, 0.8, 0.6, 0.5, 0.4, 0.2, 0.0, 0.0]
        
        for pred, outcome in zip(predictions, outcomes):
            calibrator.add_prediction(domain, pred, outcome)
        
        # Test calibration
        raw_confidence = 0.75
        calibrated = calibrator.calibrate_confidence(domain, raw_confidence)
        
        # Calibrated confidence should be different from raw
        assert calibrated != raw_confidence
        assert 0.0 <= calibrated <= 1.0
        
        # Test calibration metrics
        metrics = calibrator.get_calibration_metrics(domain)
        assert 'reliability' in metrics
        assert 'resolution' in metrics
        assert 'brier_score' in metrics
        
        for metric_value in metrics.values():
            assert 0.0 <= metric_value <= 1.0
    
    def test_limitation_identification(self, self_awareness_module, sample_task_results):
        """Test system limitation identification."""
        sam = self_awareness_module
        
        # Add tasks with varying performance levels
        poor_task = {'task_type': 'difficult_reasoning', 'performance': 0.45, 'confidence': 0.4}
        uncertain_task = {'task_type': 'ambiguous_classification', 'performance': 0.75, 'confidence': 0.3}
        
        sam.update_capability_model(poor_task)
        sam.update_capability_model(uncertain_task) 
        
        # Add knowledge domain with low coverage
        sam.update_knowledge_state('specialized_physics', coverage_delta=-0.2)
        
        # Get limitations
        limitations = sam.identify_limitations()
        
        # Verify limitations are identified
        assert len(limitations) > 0
        
        limitation_types = [lim['type'] for lim in limitations]
        assert 'performance' in limitation_types
        assert 'uncertainty' in limitation_types
        assert 'knowledge_gap' in limitation_types
        
        # Verify limitations are sorted by severity
        severities = [lim['severity'] for lim in limitations]
        assert severities == sorted(severities, reverse=True)
        
        # Verify limitation structure
        for lim in limitations:
            assert 'type' in lim
            assert 'severity' in lim
            assert 'description' in lim
            assert 'confidence' in lim
            assert 0.0 <= lim['severity'] <= 1.0
            assert 0.0 <= lim['confidence'] <= 1.0
    
    def test_capability_estimation(self, self_awareness_module, sample_task_results):
        """Test capability estimation for known and unknown tasks."""
        sam = self_awareness_module
        
        # Train on known task
        known_task = sample_task_results[0]
        sam.update_capability_model(known_task)
        
        # Test estimation for known task
        perf_est, uncertainty = sam.get_capability_estimate(known_task['task_type'])
        assert abs(perf_est - known_task['performance']) < 1e-6
        assert 0.0 <= uncertainty <= 1.0
        
        # Test estimation for unknown task
        unknown_task_type = 'completely_new_task'
        perf_est_unknown, uncertainty_unknown = sam.get_capability_estimate(unknown_task_type)
        
        assert 0.0 <= perf_est_unknown <= 1.0
        assert 0.0 <= uncertainty_unknown <= 1.0
    
    def test_self_description_generation(self, self_awareness_module, sample_task_results):
        """Test self-description generation."""
        sam = self_awareness_module
        
        # Test with empty model
        empty_description = sam.generate_self_description()
        assert isinstance(empty_description, str)
        assert len(empty_description) > 0
        assert "limited self-awareness" in empty_description.lower()
        
        # Add capabilities and knowledge
        for task_result in sample_task_results:
            sam.update_capability_model(task_result)
        
        sam.update_knowledge_state('machine_learning', 0.4, 0.3)
        sam.update_knowledge_state('natural_language', 0.6, 0.5)
        
        # Test with populated model
        description = sam.generate_self_description()
        assert isinstance(description, str)
        assert len(description) > len(empty_description)
        
        # Check for key components
        assert "capabilities" in description.lower()
        assert "limitations" in description.lower() or "limited" in description.lower()
        assert "knowledge" in description.lower()
        assert str(len(sample_task_results)) in description or "tasks" in description.lower()
    
    def test_introspection_analysis(self, self_awareness_module, sample_task_results):
        """Test deep introspective analysis."""
        sam = self_awareness_module
        
        # Populate with data
        for task_result in sample_task_results:
            sam.update_capability_model(task_result)
        
        sam.update_knowledge_state('domain1', 0.3, 0.4)
        sam.update_knowledge_state('domain2', 0.5, 0.3)
        
        # Trigger introspection
        introspection = sam._perform_introspection()
        
        # Verify introspection structure
        assert 'timestamp' in introspection
        assert 'version' in introspection
        assert 'capability_analysis' in introspection
        assert 'knowledge_analysis' in introspection
        assert 'metacognitive_insights' in introspection
        assert 'improvement_recommendations' in introspection
        
        # Verify capability analysis
        cap_analysis = introspection['capability_analysis']
        assert cap_analysis['num_capabilities'] == len(sample_task_results)
        assert 0.0 <= cap_analysis['avg_performance'] <= 1.0
        assert cap_analysis['std_performance'] >= 0.0
        
        # Verify knowledge analysis
        know_analysis = introspection['knowledge_analysis']
        assert know_analysis['num_domains'] == 2
        assert 0.0 <= know_analysis['avg_coverage'] <= 1.0
        assert 0.0 <= know_analysis['avg_expertise'] <= 1.0
        
        # Verify recommendations
        recommendations = introspection['improvement_recommendations']
        assert isinstance(recommendations, list)
        for rec in recommendations:
            assert isinstance(rec, str)
            assert len(rec) > 0
    
    def test_metacognitive_reasoning(self, self_awareness_module):
        """Test metacognitive reasoning capabilities."""
        sam = self_awareness_module
        meta_engine = sam.metacognition_engine
        
        # Test thinking pattern analysis
        reasoning_trace = [
            "Given the problem, I need to identify key variables",
            "Since temperature affects reaction rate, I should consider kinetics",
            "Therefore, higher temperature will increase the rate",
            "This leads to the conclusion that the reaction will proceed faster"
        ]
        
        pattern = meta_engine.analyze_thinking_pattern('chemistry_problem', reasoning_trace)
        
        assert 'depth' in pattern
        assert 'complexity' in pattern
        assert 'coherence' in pattern
        assert 'efficiency' in pattern
        
        assert pattern['depth'] == len(reasoning_trace)
        assert 0.0 <= pattern['complexity'] <= 1.0
        assert 0.0 <= pattern['coherence'] <= 1.0
        assert 0.0 <= pattern['efficiency'] <= 1.0
        
        # Verify storage
        assert 'chemistry_problem' in meta_engine.thinking_patterns
    
    def test_state_persistence(self, self_awareness_module, sample_task_results, tmp_path):
        """Test saving and loading of self-awareness state."""
        sam = self_awareness_module
        
        # Populate with data
        for task_result in sample_task_results:
            sam.update_capability_model(task_result)
        
        sam.update_knowledge_state('test_domain', 0.3, 0.4)
        
        # Save state
        save_path = tmp_path / "self_awareness_state.pkl"
        sam.save_state(str(save_path))
        
        assert save_path.exists()
        
        # Create new module and load state
        sam_new = SelfAwarenessModule()
        sam_new.load_state(str(save_path))
        
        # Verify state restoration
        assert len(sam_new.capability_model) == len(sam.capability_model)
        assert len(sam_new.knowledge_state) == len(sam.knowledge_state)
        assert len(sam_new.performance_history) == len(sam.performance_history)
        assert sam_new.self_model_version == sam.self_model_version
        
        # Verify capability model details
        for task_type in sam.capability_model:
            assert task_type in sam_new.capability_model
            orig_entry = sam.capability_model[task_type]
            new_entry = sam_new.capability_model[task_type]
            
            assert abs(orig_entry.performance - new_entry.performance) < 1e-6
            assert abs(orig_entry.uncertainty - new_entry.uncertainty) < 1e-6
            assert orig_entry.sample_count == new_entry.sample_count
    
    def test_performance_trends(self, self_awareness_module):
        """Test performance trend analysis."""
        sam = self_awareness_module
        
        # Simulate improving performance over time
        task_type = 'learning_task'
        base_performance = 0.5
        
        for i in range(20):
            performance = base_performance + i * 0.02  # Gradual improvement
            sam.update_capability_model({
                'task_type': task_type,
                'performance': min(performance, 1.0),
                'confidence': 0.7
            })
        
        # Test trend computation
        trend = sam._compute_performance_trend()
        
        # Should detect positive trend
        assert trend > 0, f"Expected positive trend, got {trend}"
        
        # Test learning velocity
        velocity = sam._compute_learning_velocity()
        assert velocity > 0, f"Expected positive learning velocity, got {velocity}"
    
    def test_error_handling(self, self_awareness_module):
        """Test error handling and input validation."""
        sam = self_awareness_module
        
        # Test invalid performance values
        with pytest.raises(ValueError):
            sam.update_capability_model({
                'task_type': 'test_task',
                'performance': 1.5,  # Invalid: > 1.0
                'confidence': 0.7
            })
        
        with pytest.raises(ValueError):
            sam.update_capability_model({
                'task_type': 'test_task', 
                'performance': -0.1,  # Invalid: < 0.0
                'confidence': 0.7
            })
        
        # Test CapabilityEntry validation
        with pytest.raises(ValueError):
            CapabilityEntry(
                task_type='test',
                performance=1.5,  # Invalid
                uncertainty=0.5,
                last_updated=time.time()
            )
        
        # Test KnowledgeEntry validation
        with pytest.raises(ValueError):
            KnowledgeEntry(
                domain='test',
                coverage=1.5,  # Invalid
                expertise=0.5,
                confidence=0.5,
                last_accessed=time.time()
            )
    
    def test_integration_scenarios(self, self_awareness_module):
        """Test complex integration scenarios."""
        sam = self_awareness_module
        
        # Scenario 1: Multi-task learning with knowledge transfer
        related_tasks = [
            {'task_type': 'sentiment_analysis', 'performance': 0.8, 'confidence': 0.75},
            {'task_type': 'text_classification', 'performance': 0.75, 'confidence': 0.7},
            {'task_type': 'named_entity_recognition', 'performance': 0.82, 'confidence': 0.8}
        ]
        
        for task in related_tasks:
            sam.update_capability_model(task)
        
        # Update related knowledge domain
        sam.update_knowledge_state('natural_language_processing', 0.6, 0.4)
        
        # Verify cross-task insights
        limitations = sam.identify_limitations()
        description = sam.generate_self_description()
        
        assert 'natural_language_processing' in sam.knowledge_state
        assert len(sam.capability_model) == 3
        
        # Scenario 2: Uncertainty-driven exploration
        uncertain_task = {'task_type': 'novel_domain', 'performance': 0.6, 'confidence': 0.3}
        sam.update_capability_model(uncertain_task)
        
        # Should identify high uncertainty as limitation
        limitations_after = sam.identify_limitations()
        uncertainty_limitations = [lim for lim in limitations_after if lim['type'] == 'uncertainty']
        assert len(uncertainty_limitations) > 0
        
        # Scenario 3: Capability prediction for transfer learning
        perf_est, uncertainty = sam.get_capability_estimate('similar_nlp_task')
        
        # Should have reasonable estimates based on related experience
        assert 0.0 <= perf_est <= 1.0
        assert 0.0 <= uncertainty <= 1.0


if __name__ == "__main__":
    # Run comprehensive test suite
    pytest.main([__file__, "-v", "--tb=short"])
    
    # Demonstration of Self-Awareness Module
    print("\n" + "="*60)
    print("ULTRA Self-Awareness Module Demonstration")
    print("="*60)
    
    # Create and configure module
    sam = SelfAwarenessModule()
    
    # Simulate task execution and learning
    tasks = [
        {'task_type': 'language_understanding', 'performance': 0.85, 'confidence': 0.8},
        {'task_type': 'mathematical_reasoning', 'performance': 0.72, 'confidence': 0.7},
        {'task_type': 'visual_recognition', 'performance': 0.91, 'confidence': 0.9},
        {'task_type': 'logical_inference', 'performance': 0.68, 'confidence': 0.6},
        {'task_type': 'creative_writing', 'performance': 0.77, 'confidence': 0.75}
    ]
    
    print("\nProcessing tasks and updating capabilities...")
    for task in tasks:
        sam.update_capability_model(task)
        print(f"  {task['task_type']}: {task['performance']:.2f}")
    
    # Update knowledge domains
    knowledge_updates = [
        ('machine_learning', 0.4, 0.6),
        ('natural_language_processing', 0.7, 0.5),
        ('computer_vision', 0.5, 0.7),
        ('mathematics', 0.3, 0.4)
    ]
    
    print("\nUpdating knowledge domains...")
    for domain, coverage_delta, expertise_delta in knowledge_updates:
        sam.update_knowledge_state(domain, coverage_delta, expertise_delta)
        print(f"  {domain}: coverage_Δ={coverage_delta:.1f}, expertise_Δ={expertise_delta:.1f}")
    
    # Generate self-description
    print("\nSelf-Description:")
    print("-" * 40)
    description = sam.generate_self_description()
    print(description)
    
    # Identify limitations
    print("\nIdentified Limitations:")
    print("-" * 40)
    limitations = sam.identify_limitations()
    for i, lim in enumerate(limitations[:3], 1):
        print(f"{i}. {lim['description']} (severity: {lim['severity']:.2f})")
    
    # Perform introspection
    print("\nPerforming Introspective Analysis...")
    print("-" * 40)
    introspection = sam._perform_introspection()
    
    cap_analysis = introspection['capability_analysis']
    print(f"Average Performance: {cap_analysis['avg_performance']:.3f}")
    print(f"Performance Trend: {cap_analysis['performance_trend']:.4f}")
    print(f"Capability Diversity: {cap_analysis['capability_diversity']:.3f}")
    
    know_analysis = introspection['knowledge_analysis']
    print(f"Knowledge Domains: {know_analysis['num_domains']}")
    print(f"Average Coverage: {know_analysis['avg_coverage']:.3f}")
    print(f"Average Expertise: {know_analysis['avg_expertise']:.3f}")
    
    print("\nImprovement Recommendations:")
    for i, rec in enumerate(introspection['improvement_recommendations'], 1):
        print(f"{i}. {rec}")
    
    print("\n" + "="*60)
    print("Self-Awareness Module demonstration complete!")
    print("="*60)