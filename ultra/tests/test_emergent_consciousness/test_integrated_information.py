#!/usr/bin/env python3
"""
Comprehensive test suite for ULTRA's Integrated Information Matrix component.
Tests the implementation of Integrated Information Theory (IIT) within the 
Emergent Consciousness Lattice subsystem.

This module tests:
- Integrated Information (Φ) calculation
- Information flow control mechanisms  
- System partitioning and analysis
- Consciousness quantification
- Integration optimization
"""

import pytest
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from scipy import linalg
from scipy.optimize import minimize
from itertools import combinations
from typing import Dict, List, Tuple, Optional, Set
import networkx as nx
from dataclasses import dataclass
from enum import Enum
import logging
from concurrent.futures import ThreadPoolExecutor
import time

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class PartitionType(Enum):
    """Types of system partitions for IIT analysis."""
    MINIMAL = "minimal"
    MAXIMAL = "maximal" 
    BALANCED = "balanced"
    RANDOM = "random"


@dataclass
class SystemState:
    """Represents the state of a system for IIT analysis."""
    current_state: torch.Tensor
    previous_state: torch.Tensor
    connectivity_matrix: torch.Tensor
    node_labels: List[str]
    timestamp: float


@dataclass
class PartitionResult:
    """Results from system partition analysis."""
    partition: Tuple[Set[int], Set[int]]
    phi_value: float
    mutual_information: float
    effective_information: float
    partition_type: PartitionType


class InformationMetrics:
    """Advanced information-theoretic metrics computation."""
    
    @staticmethod
    def entropy(prob_dist: torch.Tensor, epsilon: float = 1e-12) -> torch.Tensor:
        """
        Calculate Shannon entropy of probability distribution.
        H(X) = -∑ p(x) log p(x)
        """
        prob_dist = torch.clamp(prob_dist, epsilon, 1.0)
        return -torch.sum(prob_dist * torch.log2(prob_dist), dim=-1)
    
    @staticmethod
    def joint_entropy(joint_prob: torch.Tensor, epsilon: float = 1e-12) -> torch.Tensor:
        """
        Calculate joint entropy H(X,Y) = -∑∑ p(x,y) log p(x,y)
        """
        joint_prob = torch.clamp(joint_prob, epsilon, 1.0)
        return -torch.sum(joint_prob * torch.log2(joint_prob))
    
    @staticmethod
    def mutual_information(joint_prob: torch.Tensor, 
                          marginal_x: torch.Tensor, 
                          marginal_y: torch.Tensor) -> torch.Tensor:
        """
        Calculate mutual information I(X;Y) = H(X) + H(Y) - H(X,Y)
        """
        h_x = InformationMetrics.entropy(marginal_x)
        h_y = InformationMetrics.entropy(marginal_y)
        h_xy = InformationMetrics.joint_entropy(joint_prob)
        return h_x + h_y - h_xy
    
    @staticmethod
    def conditional_entropy(joint_prob: torch.Tensor, 
                           marginal_y: torch.Tensor) -> torch.Tensor:
        """
        Calculate conditional entropy H(X|Y) = H(X,Y) - H(Y)
        """
        h_xy = InformationMetrics.joint_entropy(joint_prob)
        h_y = InformationMetrics.entropy(marginal_y)
        return h_xy - h_y
    
    @staticmethod
    def conditional_mutual_information(x_states: torch.Tensor,
                                     y_states: torch.Tensor,
                                     z_states: torch.Tensor) -> torch.Tensor:
        """
        Calculate conditional mutual information I(X;Y|Z)
        I(X;Y|Z) = H(X|Z) + H(Y|Z) - H(X,Y|Z)
        """
        # Create joint distributions
        xyz_joint = InformationMetrics._create_joint_distribution(
            torch.stack([x_states, y_states, z_states], dim=-1)
        )
        xz_joint = InformationMetrics._create_joint_distribution(
            torch.stack([x_states, z_states], dim=-1)
        )
        yz_joint = InformationMetrics._create_joint_distribution(
            torch.stack([y_states, z_states], dim=-1)
        )
        z_marginal = InformationMetrics._create_marginal_distribution(z_states)
        
        # Calculate conditional entropies
        h_x_given_z = InformationMetrics.conditional_entropy(xz_joint, z_marginal)
        h_y_given_z = InformationMetrics.conditional_entropy(yz_joint, z_marginal)
        h_xy_given_z = InformationMetrics.conditional_entropy(xyz_joint, z_marginal)
        
        return h_x_given_z + h_y_given_z - h_xy_given_z
    
    @staticmethod
    def _create_joint_distribution(states: torch.Tensor) -> torch.Tensor:
        """Create joint probability distribution from state samples."""
        unique_states, counts = torch.unique(states, return_counts=True, dim=0)
        probabilities = counts.float() / states.shape[0]
        return probabilities
    
    @staticmethod
    def _create_marginal_distribution(states: torch.Tensor) -> torch.Tensor:
        """Create marginal probability distribution from state samples."""
        unique_states, counts = torch.unique(states, return_counts=True)
        probabilities = counts.float() / states.shape[0]
        return probabilities


class SystemPartitioner:
    """Handles system partitioning for IIT analysis."""
    
    def __init__(self, num_nodes: int):
        self.num_nodes = num_nodes
        self.all_partitions = self._generate_all_partitions()
    
    def _generate_all_partitions(self) -> List[Tuple[Set[int], Set[int]]]:
        """Generate all possible bipartitions of the system."""
        partitions = []
        nodes = set(range(self.num_nodes))
        
        # Generate all possible non-empty subsets
        for r in range(1, self.num_nodes):
            for subset in combinations(nodes, r):
                subset_a = set(subset)
                subset_b = nodes - subset_a
                if len(subset_b) > 0:  # Ensure both partitions are non-empty
                    partitions.append((subset_a, subset_b))
        
        return partitions
    
    def get_minimal_partitions(self) -> List[Tuple[Set[int], Set[int]]]:
        """Get partitions that minimize the number of cut connections."""
        # This is a simplified implementation - in practice would use graph cuts
        minimal_partitions = []
        min_cuts = float('inf')
        
        for partition in self.all_partitions:
            cut_size = self._calculate_cut_size(partition)
            if cut_size < min_cuts:
                min_cuts = cut_size
                minimal_partitions = [partition]
            elif cut_size == min_cuts:
                minimal_partitions.append(partition)
        
        return minimal_partitions
    
    def _calculate_cut_size(self, partition: Tuple[Set[int], Set[int]]) -> int:
        """Calculate the number of edges cut by a partition."""
        subset_a, subset_b = partition
        cut_size = 0
        
        for node_a in subset_a:
            for node_b in subset_b:
                # In a complete graph, every cross-partition edge is cut
                cut_size += 1
        
        return cut_size


class IntegratedInformationMatrix:
    """
    Implementation of Integrated Information Theory (IIT) for consciousness quantification.
    
    Based on the mathematical formulation:
    Φ(X) = min_{P∈P} I(X₁;X₂|X_P) / min{H(X₁|X_P), H(X₂|X_P)}
    
    Where:
    - X is the system
    - P is a partition into subsystems X₁ and X₂  
    - I is mutual information
    - H is entropy
    - X_P represents the past state
    """
    
    def __init__(self, 
                 num_nodes: int,
                 connectivity_matrix: Optional[torch.Tensor] = None,
                 integration_threshold: float = 0.01,
                 optimization_steps: int = 1000):
        self.num_nodes = num_nodes
        self.connectivity_matrix = connectivity_matrix or self._initialize_connectivity()
        self.integration_threshold = integration_threshold
        self.optimization_steps = optimization_steps
        self.partitioner = SystemPartitioner(num_nodes)
        self.metrics = InformationMetrics()
        
        # State history for temporal analysis
        self.state_history: List[torch.Tensor] = []
        self.phi_history: List[float] = []
        
        logger.info(f"Initialized IIT matrix for {num_nodes} nodes")
    
    def _initialize_connectivity(self) -> torch.Tensor:
        """Initialize connectivity matrix with random connections."""
        connectivity = torch.rand(self.num_nodes, self.num_nodes)
        # Make symmetric and zero diagonal
        connectivity = (connectivity + connectivity.T) / 2
        connectivity.fill_diagonal_(0)
        # Apply sparsity
        mask = torch.rand_like(connectivity) > 0.7
        connectivity[mask] = 0
        return connectivity
    
    def compute_integrated_information(self, 
                                     current_state: torch.Tensor,
                                     previous_state: torch.Tensor,
                                     return_details: bool = False) -> Dict:
        """
        Compute integrated information Φ for the given system state.
        
        Args:
            current_state: Current state of all nodes [num_nodes]
            previous_state: Previous state of all nodes [num_nodes] 
            return_details: Whether to return detailed analysis
            
        Returns:
            Dictionary containing Φ value and optional details
        """
        logger.info("Computing integrated information...")
        
        # Validate inputs
        assert current_state.shape[0] == self.num_nodes
        assert previous_state.shape[0] == self.num_nodes
        
        # Store states for analysis
        self.state_history.append(current_state.clone())
        
        # Find the partition that minimizes integrated information
        min_phi = float('inf')
        best_partition = None
        partition_results = []
        
        # Analyze all possible partitions
        for partition in self.partitioner.all_partitions:
            phi_value = self._calculate_phi_for_partition(
                current_state, previous_state, partition
            )
            
            partition_result = PartitionResult(
                partition=partition,
                phi_value=phi_value,
                mutual_information=self._calculate_partition_mi(
                    current_state, partition
                ),
                effective_information=self._calculate_effective_information(
                    current_state, previous_state, partition
                ),
                partition_type=PartitionType.MINIMAL if phi_value == min_phi else PartitionType.RANDOM
            )
            partition_results.append(partition_result)
            
            if phi_value < min_phi:
                min_phi = phi_value
                best_partition = partition
        
        # Store Φ value
        self.phi_history.append(min_phi)
        
        result = {
            'phi': min_phi,
            'best_partition': best_partition,
            'integration_level': self._classify_integration_level(min_phi)
        }
        
        if return_details:
            result.update({
                'partition_results': partition_results,
                'connectivity_strength': self._calculate_connectivity_strength(),
                'state_complexity': self._calculate_state_complexity(current_state),
                'temporal_consistency': self._calculate_temporal_consistency()
            })
        
        logger.info(f"Computed Φ = {min_phi:.4f}")
        return result
    
    def _calculate_phi_for_partition(self,
                                   current_state: torch.Tensor,
                                   previous_state: torch.Tensor,
                                   partition: Tuple[Set[int], Set[int]]) -> float:
        """
        Calculate Φ for a specific partition using the IIT formula:
        Φ(X) = min_{P∈P} I(X₁;X₂|X_P) / min{H(X₁|X_P), H(X₂|X_P)}
        """
        subset_a, subset_b = partition
        
        # Extract states for each partition
        states_a_current = current_state[list(subset_a)]
        states_b_current = current_state[list(subset_b)]
        states_a_previous = previous_state[list(subset_a)]
        states_b_previous = previous_state[list(subset_b)]
        
        # Calculate conditional mutual information I(X₁;X₂|X_P)
        cmi = self._calculate_conditional_mutual_information(
            states_a_current, states_b_current, previous_state
        )
        
        # Calculate conditional entropies H(X₁|X_P) and H(X₂|X_P)  
        h_a_given_past = self._calculate_conditional_entropy(
            states_a_current, previous_state
        )
        h_b_given_past = self._calculate_conditional_entropy(
            states_b_current, previous_state
        )
        
        # Φ = CMI / min(conditional entropies)
        min_conditional_entropy = min(h_a_given_past, h_b_given_past)
        
        if min_conditional_entropy < 1e-6:
            return 0.0
        
        phi = cmi / min_conditional_entropy
        return max(0.0, phi)  # Φ cannot be negative
    
    def _calculate_conditional_mutual_information(self,
                                                states_a: torch.Tensor,
                                                states_b: torch.Tensor, 
                                                conditioning_states: torch.Tensor) -> float:
        """Calculate I(A;B|C) = H(A|C) + H(B|C) - H(A,B|C)"""
        
        # Discretize continuous states for information calculation
        states_a_disc = self._discretize_states(states_a)
        states_b_disc = self._discretize_states(states_b)
        conditioning_disc = self._discretize_states(conditioning_states)
        
        # Calculate conditional entropies
        h_a_given_c = self._calculate_conditional_entropy_discrete(
            states_a_disc, conditioning_disc
        )
        h_b_given_c = self._calculate_conditional_entropy_discrete(
            states_b_disc, conditioning_disc
        )
        h_ab_given_c = self._calculate_conditional_entropy_discrete(
            torch.stack([states_a_disc, states_b_disc], dim=-1), conditioning_disc
        )
        
        return h_a_given_c + h_b_given_c - h_ab_given_c
    
    def _calculate_conditional_entropy(self,
                                     target_states: torch.Tensor,
                                     conditioning_states: torch.Tensor) -> float:
        """Calculate H(X|Y) = H(X,Y) - H(Y)"""
        
        target_disc = self._discretize_states(target_states)
        conditioning_disc = self._discretize_states(conditioning_states)
        
        return self._calculate_conditional_entropy_discrete(target_disc, conditioning_disc)
    
    def _calculate_conditional_entropy_discrete(self,
                                              target_states: torch.Tensor,
                                              conditioning_states: torch.Tensor) -> float:
        """Calculate conditional entropy for discrete states."""
        
        # Create joint distribution
        if target_states.dim() == 1:
            joint_states = torch.stack([target_states, conditioning_states], dim=-1)
        else:
            joint_states = torch.cat([target_states, conditioning_states.unsqueeze(-1)], dim=-1)
        
        # Get unique states and counts
        unique_joint, joint_counts = torch.unique(joint_states, return_counts=True, dim=0)
        _, conditioning_counts = torch.unique(conditioning_states, return_counts=True, dim=0)
        
        # Calculate probabilities
        joint_probs = joint_counts.float() / joint_states.shape[0]
        conditioning_probs = conditioning_counts.float() / conditioning_states.shape[0]
        
        # Calculate H(X,Y)
        h_joint = -torch.sum(joint_probs * torch.log2(joint_probs + 1e-12))
        
        # Calculate H(Y)
        h_conditioning = -torch.sum(conditioning_probs * torch.log2(conditioning_probs + 1e-12))
        
        return (h_joint - h_conditioning).item()
    
    def _discretize_states(self, states: torch.Tensor, bins: int = 10) -> torch.Tensor:
        """Discretize continuous states into bins for information calculation."""
        if states.dim() == 0:
            return torch.tensor([0])
        
        # Handle multi-dimensional states
        if states.dim() > 1:
            # Flatten and discretize each dimension separately
            discretized = []
            for dim in range(states.shape[-1]):
                dim_states = states[..., dim]
                min_val, max_val = dim_states.min(), dim_states.max()
                if max_val > min_val:
                    bins_edges = torch.linspace(min_val, max_val, bins + 1)
                    discretized.append(torch.bucketize(dim_states, bins_edges) - 1)
                else:
                    discretized.append(torch.zeros_like(dim_states, dtype=torch.long))
            return torch.stack(discretized, dim=-1)
        else:
            # 1D case
            min_val, max_val = states.min(), states.max()
            if max_val > min_val:
                bins_edges = torch.linspace(min_val, max_val, bins + 1)
                return torch.bucketize(states, bins_edges) - 1
            else:
                return torch.zeros_like(states, dtype=torch.long)
    
    def _calculate_partition_mi(self,
                               current_state: torch.Tensor,
                               partition: Tuple[Set[int], Set[int]]) -> float:
        """Calculate mutual information between partition elements."""
        subset_a, subset_b = partition
        
        states_a = current_state[list(subset_a)]
        states_b = current_state[list(subset_b)]
        
        # Discretize for MI calculation
        states_a_disc = self._discretize_states(states_a)
        states_b_disc = self._discretize_states(states_b)
        
        # Calculate joint and marginal distributions
        joint_states = torch.stack([states_a_disc, states_b_disc], dim=-1)
        
        unique_joint, joint_counts = torch.unique(joint_states, return_counts=True, dim=0)
        unique_a, counts_a = torch.unique(states_a_disc, return_counts=True, dim=0)
        unique_b, counts_b = torch.unique(states_b_disc, return_counts=True, dim=0)
        
        # Calculate probabilities
        n_samples = joint_states.shape[0]
        joint_probs = joint_counts.float() / n_samples
        marginal_a = counts_a.float() / n_samples
        marginal_b = counts_b.float() / n_samples
        
        # Calculate MI using the metrics class
        return self.metrics.mutual_information(
            joint_probs.unsqueeze(0), marginal_a, marginal_b
        ).item()
    
    def _calculate_effective_information(self,
                                       current_state: torch.Tensor,
                                       previous_state: torch.Tensor,
                                       partition: Tuple[Set[int], Set[int]]) -> float:
        """Calculate effective information across the partition."""
        subset_a, subset_b = partition
        
        # Calculate information flow from A to B and B to A
        flow_a_to_b = self._calculate_information_flow(
            previous_state[list(subset_a)], current_state[list(subset_b)]
        )
        flow_b_to_a = self._calculate_information_flow(
            previous_state[list(subset_b)], current_state[list(subset_a)]
        )
        
        return flow_a_to_b + flow_b_to_a
    
    def _calculate_information_flow(self,
                                   source_states: torch.Tensor,
                                   target_states: torch.Tensor) -> float:
        """Calculate information flow from source to target states."""
        source_disc = self._discretize_states(source_states)
        target_disc = self._discretize_states(target_states)
        
        # Calculate transfer entropy approximation
        joint_states = torch.stack([source_disc, target_disc], dim=-1)
        unique_joint, joint_counts = torch.unique(joint_states, return_counts=True, dim=0)
        unique_target, target_counts = torch.unique(target_disc, return_counts=True, dim=0)
        
        # Calculate probabilities
        n_samples = joint_states.shape[0]
        joint_probs = joint_counts.float() / n_samples
        target_probs = target_counts.float() / n_samples
        
        # Approximation of transfer entropy
        return self.metrics.mutual_information(
            joint_probs.unsqueeze(0), 
            torch.ones_like(joint_probs), 
            target_probs
        ).item()
    
    def _classify_integration_level(self, phi_value: float) -> str:
        """Classify the level of integration based on Φ value."""
        if phi_value < 0.01:
            return "minimal"
        elif phi_value < 0.1:
            return "low"
        elif phi_value < 0.5:
            return "moderate"
        elif phi_value < 1.0:
            return "high"
        else:
            return "maximum"
    
    def _calculate_connectivity_strength(self) -> float:
        """Calculate overall connectivity strength of the system."""
        return torch.sum(torch.abs(self.connectivity_matrix)).item()
    
    def _calculate_state_complexity(self, state: torch.Tensor) -> float:
        """Calculate complexity of the current state."""
        state_disc = self._discretize_states(state)
        unique_states, counts = torch.unique(state_disc, return_counts=True, dim=0)
        probs = counts.float() / state.shape[0]
        return self.metrics.entropy(probs).item()
    
    def _calculate_temporal_consistency(self) -> float:
        """Calculate temporal consistency of Φ values."""
        if len(self.phi_history) < 2:
            return 1.0
        
        phi_tensor = torch.tensor(self.phi_history)
        return 1.0 - torch.std(phi_tensor).item() / (torch.mean(phi_tensor).item() + 1e-6)
    
    def optimize_integration(self,
                           current_state: torch.Tensor,
                           previous_state: torch.Tensor,
                           learning_rate: float = 0.01) -> Dict:
        """
        Optimize the system to maximize integrated information.
        Implementation of: Φ* = max_θ Φ(X_θ)
        """
        logger.info("Optimizing integration...")
        
        # Make connectivity matrix trainable
        optimizable_connectivity = self.connectivity_matrix.clone().requires_grad_(True)
        optimizer = torch.optim.Adam([optimizable_connectivity], lr=learning_rate)
        
        best_phi = -float('inf')
        best_connectivity = None
        phi_trajectory = []
        
        for step in range(self.optimization_steps):
            optimizer.zero_grad()
            
            # Update connectivity matrix
            self.connectivity_matrix = torch.sigmoid(optimizable_connectivity)
            
            # Calculate Φ for current configuration
            result = self.compute_integrated_information(current_state, previous_state)
            phi_value = result['phi']
            
            # Maximize Φ (minimize negative Φ)
            loss = -phi_value
            
            # Add regularization to prevent extreme connectivity
            reg_loss = 0.01 * torch.sum(optimizable_connectivity ** 2)
            total_loss = loss + reg_loss
            
            total_loss.backward()
            optimizer.step()
            
            phi_trajectory.append(phi_value)
            
            if phi_value > best_phi:
                best_phi = phi_value
                best_connectivity = self.connectivity_matrix.clone()
            
            if step % 100 == 0:
                logger.info(f"Step {step}: Φ = {phi_value:.4f}")
        
        # Restore best connectivity
        self.connectivity_matrix = best_connectivity
        
        return {
            'optimized_phi': best_phi,
            'initial_phi': phi_trajectory[0],
            'phi_trajectory': phi_trajectory,
            'optimized_connectivity': best_connectivity,
            'improvement': best_phi - phi_trajectory[0]
        }
    
    def analyze_information_flow(self,
                               current_state: torch.Tensor,
                               previous_state: torch.Tensor) -> Dict:
        """
        Analyze information flow control: F_ij = α_ij * I(X_i; X_j) - β_ij * I(X_i; X_j | X \ {X_i, X_j})
        """
        logger.info("Analyzing information flow...")
        
        flow_matrix = torch.zeros(self.num_nodes, self.num_nodes)
        
        for i in range(self.num_nodes):
            for j in range(self.num_nodes):
                if i != j:
                    # Calculate direct mutual information I(X_i; X_j)
                    direct_mi = self._calculate_pairwise_mi(
                        current_state[i], current_state[j]
                    )
                    
                    # Calculate conditional mutual information I(X_i; X_j | X \ {X_i, X_j})
                    other_nodes = list(range(self.num_nodes))
                    other_nodes.remove(i)
                    other_nodes.remove(j)
                    
                    if other_nodes:
                        conditional_mi = self._calculate_conditional_mi_pairwise(
                            current_state[i], current_state[j], 
                            current_state[other_nodes]
                        )
                    else:
                        conditional_mi = 0.0
                    
                    # Flow control parameters (learnable in practice)
                    alpha_ij = self.connectivity_matrix[i, j].item()
                    beta_ij = 0.5  # Could be learned
                    
                    # Calculate controlled flow
                    flow_ij = alpha_ij * direct_mi - beta_ij * conditional_mi
                    flow_matrix[i, j] = flow_ij
        
        return {
            'flow_matrix': flow_matrix,
            'total_flow': torch.sum(torch.abs(flow_matrix)).item(),
            'flow_balance': self._calculate_flow_balance(flow_matrix),
            'dominant_flows': self._identify_dominant_flows(flow_matrix)
        }
    
    def _calculate_pairwise_mi(self, state_i: torch.Tensor, state_j: torch.Tensor) -> float:
        """Calculate mutual information between two nodes."""
        state_i_disc = self._discretize_states(state_i.unsqueeze(0))
        state_j_disc = self._discretize_states(state_j.unsqueeze(0))
        
        joint_states = torch.stack([state_i_disc.squeeze(), state_j_disc.squeeze()], dim=-1)
        
        unique_joint, joint_counts = torch.unique(joint_states, return_counts=True, dim=0)
        unique_i, counts_i = torch.unique(state_i_disc, return_counts=True, dim=0)
        unique_j, counts_j = torch.unique(state_j_disc, return_counts=True, dim=0)
        
        n_samples = joint_states.shape[0]
        joint_probs = joint_counts.float() / n_samples
        marginal_i = counts_i.float() / n_samples
        marginal_j = counts_j.float() / n_samples
        
        return self.metrics.mutual_information(
            joint_probs.unsqueeze(0), marginal_i, marginal_j
        ).item()
    
    def _calculate_conditional_mi_pairwise(self,
                                         state_i: torch.Tensor,
                                         state_j: torch.Tensor,
                                         conditioning_states: torch.Tensor) -> float:
        """Calculate conditional MI between two nodes given others."""
        return self._calculate_conditional_mutual_information(
            state_i.unsqueeze(0), state_j.unsqueeze(0), conditioning_states
        )
    
    def _calculate_flow_balance(self, flow_matrix: torch.Tensor) -> float:
        """Calculate the balance of information flows."""
        in_flows = torch.sum(flow_matrix, dim=0)
        out_flows = torch.sum(flow_matrix, dim=1)
        flow_differences = torch.abs(in_flows - out_flows)
        return torch.mean(flow_differences).item()
    
    def _identify_dominant_flows(self, flow_matrix: torch.Tensor) -> List[Tuple[int, int, float]]:
        """Identify the strongest information flows."""
        abs_flows = torch.abs(flow_matrix)
        flat_flows = abs_flows.flatten()
        top_indices = torch.topk(flat_flows, k=min(5, flat_flows.numel())).indices
        
        dominant_flows = []
        for idx in top_indices:
            i = idx // self.num_nodes
            j = idx % self.num_nodes
            if i != j:  # Exclude diagonal
                dominant_flows.append((i.item(), j.item(), flow_matrix[i, j].item()))
        
        return dominant_flows


class ConsciousnessQuantifier:
    """Quantifies consciousness levels based on integrated information."""
    
    def __init__(self, iit_matrix: IntegratedInformationMatrix):
        self.iit_matrix = iit_matrix
        self.consciousness_thresholds = {
            'unconscious': 0.0,
            'minimally_conscious': 0.01,
            'conscious': 0.1,
            'highly_conscious': 0.5,
            'maximally_conscious': 1.0
        }
    
    def assess_consciousness_level(self,
                                 current_state: torch.Tensor,
                                 previous_state: torch.Tensor) -> Dict:
        """Assess the consciousness level of the system."""
        
        # Compute integrated information
        iit_result = self.iit_matrix.compute_integrated_information(
            current_state, previous_state, return_details=True
        )
        
        phi_value = iit_result['phi']
        
        # Determine consciousness level
        consciousness_level = self._classify_consciousness(phi_value)
        
        # Calculate additional consciousness metrics
        coherence = self._calculate_coherence(current_state)
        complexity = self._calculate_complexity(current_state)
        integration_dynamics = self._analyze_integration_dynamics()
        
        return {
            'phi_value': phi_value,
            'consciousness_level': consciousness_level,
            'coherence': coherence,
            'complexity': complexity,
            'integration_dynamics': integration_dynamics,
            'consciousness_score': self._calculate_consciousness_score(
                phi_value, coherence, complexity
            ),
            'detailed_analysis': iit_result
        }
    
    def _classify_consciousness(self, phi_value: float) -> str:
        """Classify consciousness level based on Φ value."""
        for level, threshold in reversed(self.consciousness_thresholds.items()):
            if phi_value >= threshold:
                return level
        return 'unconscious'
    
    def _calculate_coherence(self, state: torch.Tensor) -> float:
        """Calculate state coherence using correlation analysis."""
        if state.shape[0] < 2:
            return 1.0
        
        # Calculate correlation matrix
        state_expanded = state.unsqueeze(0) if state.dim() == 1 else state
        correlation_matrix = torch.corrcoef(state_expanded)
        
        # Calculate mean absolute correlation
        mask = ~torch.eye(correlation_matrix.shape[0], dtype=bool)
        correlations = correlation_matrix[mask]
        
        return torch.mean(torch.abs(correlations)).item()
    
    def _calculate_complexity(self, state: torch.Tensor) -> float:
        """Calculate state complexity using entropy measures."""
        return self.iit_matrix._calculate_state_complexity(state)
    
    def _analyze_integration_dynamics(self) -> Dict:
        """Analyze the dynamics of integration over time."""
        if len(self.iit_matrix.phi_history) < 2:
            return {'stability': 1.0, 'trend': 'stable', 'variability': 0.0}
        
        phi_values = torch.tensor(self.iit_matrix.phi_history)
        
        # Calculate stability
        stability = 1.0 - torch.std(phi_values) / (torch.mean(phi_values) + 1e-6)
        
        # Calculate trend
        if len(phi_values) > 1:
            trend_slope = (phi_values[-1] - phi_values[0]) / len(phi_values)
            if trend_slope > 0.01:
                trend = 'increasing'
            elif trend_slope < -0.01:
                trend = 'decreasing'
            else:
                trend = 'stable'
        else:
            trend = 'stable'
        
        # Calculate variability
        variability = torch.std(phi_values).item()
        
        return {
            'stability': stability.item(),
            'trend': trend,
            'variability': variability
        }
    
    def _calculate_consciousness_score(self,
                                     phi_value: float,
                                     coherence: float,
                                     complexity: float) -> float:
        """Calculate composite consciousness score."""
        # Weighted combination of metrics
        weights = {'phi': 0.5, 'coherence': 0.3, 'complexity': 0.2}
        
        normalized_phi = min(phi_value, 1.0)  # Cap at 1.0
        normalized_coherence = min(coherence, 1.0)
        normalized_complexity = min(complexity / 2.0, 1.0)  # Assume max complexity ~2.0
        
        score = (weights['phi'] * normalized_phi +
                weights['coherence'] * normalized_coherence +
                weights['complexity'] * normalized_complexity)
        
        return score


# Test Cases

class TestIntegratedInformationMatrix:
    """Comprehensive test suite for Integrated Information Matrix."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.num_nodes = 6
        self.iit_matrix = IntegratedInformationMatrix(
            num_nodes=self.num_nodes,
            integration_threshold=0.01,
            optimization_steps=100
        )
        self.consciousness_quantifier = ConsciousnessQuantifier(self.iit_matrix)
        
    def test_initialization(self):
        """Test proper initialization of IIT matrix."""
        assert self.iit_matrix.num_nodes == self.num_nodes
        assert self.iit_matrix.connectivity_matrix.shape == (self.num_nodes, self.num_nodes)
        assert torch.allclose(self.iit_matrix.connectivity_matrix, 
                            self.iit_matrix.connectivity_matrix.T)  # Symmetric
        assert torch.all(torch.diag(self.iit_matrix.connectivity_matrix) == 0)  # Zero diagonal
        
    def test_information_metrics(self):
        """Test information-theoretic calculations."""
        metrics = InformationMetrics()
        
        # Test entropy calculation
        prob_dist = torch.tensor([0.5, 0.3, 0.2])
        entropy = metrics.entropy(prob_dist)
        expected_entropy = -torch.sum(prob_dist * torch.log2(prob_dist))
        assert torch.allclose(entropy, expected_entropy, rtol=1e-4)
        
        # Test mutual information
        joint_prob = torch.tensor([[0.25, 0.25], [0.25, 0.25]])
        marginal_x = torch.tensor([0.5, 0.5])
        marginal_y = torch.tensor([0.5, 0.5])
        mi = metrics.mutual_information(joint_prob, marginal_x, marginal_y)
        # For independent variables, MI should be close to 0
        assert abs(mi.item()) < 1e-1
        
    def test_system_partitioning(self):
        """Test system partitioning functionality."""
        partitioner = SystemPartitioner(4)
        
        # Check that all partitions are generated
        assert len(partitioner.all_partitions) > 0
        
        # Check that each partition has non-empty subsets
        for partition in partitioner.all_partitions:
            subset_a, subset_b = partition
            assert len(subset_a) > 0
            assert len(subset_b) > 0
            assert subset_a.isdisjoint(subset_b)
            assert subset_a.union(subset_b) == set(range(4))
    
    def test_phi_calculation(self):
        """Test integrated information calculation."""
        # Create test states
        current_state = torch.rand(self.num_nodes)
        previous_state = torch.rand(self.num_nodes)
        
        # Calculate Φ
        result = self.iit_matrix.compute_integrated_information(
            current_state, previous_state, return_details=True
        )
        
        # Validate results
        assert 'phi' in result
        assert 'best_partition' in result
        assert 'integration_level' in result
        assert result['phi'] >= 0.0  # Φ cannot be negative
        
        # Check detailed results
        assert 'partition_results' in result
        assert 'connectivity_strength' in result
        assert 'state_complexity' in result
        
    def test_phi_properties(self):
        """Test mathematical properties of Φ."""
        # Test with perfectly synchronized states (high integration)
        synchronized_current = torch.ones(self.num_nodes) * 0.8
        synchronized_previous = torch.ones(self.num_nodes) * 0.2
        
        sync_result = self.iit_matrix.compute_integrated_information(
            synchronized_current, synchronized_previous
        )
        
        # Test with random states (lower integration expected)
        random_current = torch.rand(self.num_nodes)
        random_previous = torch.rand(self.num_nodes)
        
        random_result = self.iit_matrix.compute_integrated_information(
            random_current, random_previous
        )
        
        # Synchronized states should generally have different Φ than random
        assert isinstance(sync_result['phi'], float)
        assert isinstance(random_result['phi'], float)
        
    def test_information_flow_analysis(self):
        """Test information flow control mechanisms."""
        current_state = torch.rand(self.num_nodes)
        previous_state = torch.rand(self.num_nodes)
        
        flow_result = self.iit_matrix.analyze_information_flow(
            current_state, previous_state
        )
        
        # Validate flow analysis results
        assert 'flow_matrix' in flow_result
        assert 'total_flow' in flow_result
        assert 'flow_balance' in flow_result
        assert 'dominant_flows' in flow_result
        
        # Check flow matrix properties
        flow_matrix = flow_result['flow_matrix']
        assert flow_matrix.shape == (self.num_nodes, self.num_nodes)
        
        # Check dominant flows
        dominant_flows = flow_result['dominant_flows']
        assert isinstance(dominant_flows, list)
        for flow in dominant_flows:
            assert len(flow) == 3  # (source, target, strength)
            
    def test_integration_optimization(self):
        """Test integration optimization functionality."""
        current_state = torch.rand(self.num_nodes)
        previous_state = torch.rand(self.num_nodes)
        
        # Get initial Φ
        initial_result = self.iit_matrix.compute_integrated_information(
            current_state, previous_state
        )
        initial_phi = initial_result['phi']
        
        # Optimize integration
        opt_result = self.iit_matrix.optimize_integration(
            current_state, previous_state, learning_rate=0.1
        )
        
        # Validate optimization results
        assert 'optimized_phi' in opt_result
        assert 'initial_phi' in opt_result
        assert 'phi_trajectory' in opt_result
        assert 'optimized_connectivity' in opt_result
        assert 'improvement' in opt_result
        
        # Check that optimization trajectory is reasonable
        trajectory = opt_result['phi_trajectory']
        assert len(trajectory) > 0
        assert all(isinstance(phi, (int, float)) for phi in trajectory)
        
    def test_consciousness_quantification(self):
        """Test consciousness level assessment."""
        current_state = torch.rand(self.num_nodes)
        previous_state = torch.rand(self.num_nodes)
        
        consciousness_result = self.consciousness_quantifier.assess_consciousness_level(
            current_state, previous_state
        )
        
        # Validate consciousness assessment
        assert 'phi_value' in consciousness_result
        assert 'consciousness_level' in consciousness_result
        assert 'coherence' in consciousness_result
        assert 'complexity' in consciousness_result
        assert 'consciousness_score' in consciousness_result
        
        # Check consciousness level classification
        consciousness_level = consciousness_result['consciousness_level']
        valid_levels = ['unconscious', 'minimally_conscious', 'conscious', 
                       'highly_conscious', 'maximally_conscious']
        assert consciousness_level in valid_levels
        
        # Check score bounds
        score = consciousness_result['consciousness_score']
        assert 0.0 <= score <= 1.0
        
    def test_state_discretization(self):
        """Test state discretization for information calculations."""
        # Test 1D states
        states_1d = torch.tensor([0.1, 0.5, 0.9, 0.2, 0.8])
        discretized_1d = self.iit_matrix._discretize_states(states_1d, bins=3)
        assert discretized_1d.max() < 3
        assert discretized_1d.min() >= 0
        
        # Test 2D states
        states_2d = torch.rand(5, 2)
        discretized_2d = self.iit_matrix._discretize_states(states_2d, bins=3)
        assert discretized_2d.shape == states_2d.shape
        assert discretized_2d.max() < 3
        assert discretized_2d.min() >= 0
        
    def test_temporal_consistency(self):
        """Test temporal consistency tracking."""
        # Generate sequence of states
        for i in range(10):
            current_state = torch.rand(self.num_nodes) + 0.1 * i  # Slight trend
            previous_state = torch.rand(self.num_nodes) + 0.1 * (i-1)
            
            self.iit_matrix.compute_integrated_information(current_state, previous_state)
        
        # Check temporal consistency calculation
        consistency = self.iit_matrix._calculate_temporal_consistency()
        assert 0.0 <= consistency <= 1.0
        
        # Check that history is recorded
        assert len(self.iit_matrix.phi_history) == 10
        assert len(self.iit_matrix.state_history) == 10
        
    def test_edge_cases(self):
        """Test edge cases and error handling."""
        # Test with uniform states (minimal integration)
        uniform_state = torch.ones(self.num_nodes) * 0.5
        result = self.iit_matrix.compute_integrated_information(
            uniform_state, uniform_state
        )
        assert result['phi'] >= 0.0
        
        # Test with single node (should handle gracefully)
        single_node_matrix = IntegratedInformationMatrix(num_nodes=2)
        single_result = single_node_matrix.compute_integrated_information(
            torch.rand(2), torch.rand(2)
        )
        assert 'phi' in single_result
        
    def test_mathematical_consistency(self):
        """Test mathematical consistency of IIT calculations."""
        current_state = torch.rand(self.num_nodes)
        previous_state = torch.rand(self.num_nodes)
        
        # Calculate Φ multiple times - should be deterministic
        result1 = self.iit_matrix.compute_integrated_information(
            current_state, previous_state
        )
        
        # Reset history to ensure clean calculation
        self.iit_matrix.phi_history = []
        self.iit_matrix.state_history = []
        
        result2 = self.iit_matrix.compute_integrated_information(
            current_state, previous_state
        )
        
        # Results should be identical for deterministic calculation
        assert abs(result1['phi'] - result2['phi']) < 1e-6
        
    def test_performance_benchmarks(self):
        """Test performance benchmarks for large systems."""
        # Test with larger system
        large_system = IntegratedInformationMatrix(num_nodes=8)
        current_state = torch.rand(8)
        previous_state = torch.rand(8)
        
        start_time = time.time()
        result = large_system.compute_integrated_information(
            current_state, previous_state
        )
        end_time = time.time()
        
        computation_time = end_time - start_time
        logger.info(f"IIT calculation for 8 nodes took {computation_time:.4f}s")
        
        # Should complete within reasonable time (adjust threshold as needed)
        assert computation_time < 10.0  # 10 seconds max
        assert 'phi' in result
        
    @pytest.mark.parametrize("num_nodes", [3, 4, 5, 6, 7])
    def test_scalability(self, num_nodes):
        """Test scalability across different system sizes."""
        system = IntegratedInformationMatrix(num_nodes=num_nodes)
        current_state = torch.rand(num_nodes)
        previous_state = torch.rand(num_nodes)
        
        result = system.compute_integrated_information(
            current_state, previous_state, return_details=True
        )
        
        # Should handle all sizes gracefully
        assert 'phi' in result
        assert result['phi'] >= 0.0
        assert len(result['partition_results']) > 0
        
    def test_integration_with_real_dynamics(self):
        """Test integration with realistic neural dynamics."""
        # Simulate realistic neural activity patterns
        time_steps = 20
        
        # Create oscillatory patterns (more realistic)
        t = torch.linspace(0, 4*np.pi, time_steps)
        base_pattern = torch.sin(t).unsqueeze(1)
        
        phi_values = []
        
        for step in range(1, time_steps):
            # Create correlated activity with some noise
            current_state = (base_pattern[step] + 0.1 * torch.randn(self.num_nodes)).squeeze()
            previous_state = (base_pattern[step-1] + 0.1 * torch.randn(self.num_nodes)).squeeze()
            
            result = self.iit_matrix.compute_integrated_information(
                current_state, previous_state
            )
            phi_values.append(result['phi'])
        
        # Check that we get reasonable Φ values
        phi_tensor = torch.tensor(phi_values)
        assert torch.all(phi_tensor >= 0.0)
        assert len(phi_values) == time_steps - 1
        
        # Analyze consciousness over time
        final_consciousness = self.consciousness_quantifier.assess_consciousness_level(
            current_state, previous_state
        )
        
        assert 'integration_dynamics' in final_consciousness
        dynamics = final_consciousness['integration_dynamics']
        assert 'stability' in dynamics
        assert 'trend' in dynamics
        assert 'variability' in dynamics


if __name__ == "__main__":
    # Run comprehensive tests
    pytest.main([__file__, "-v", "--tb=short"])
    
    # Additional demonstration
    print("\n" + "="*80)
    print("ULTRA Integrated Information Matrix - Demonstration")
    print("="*80)
    
    # Create demonstration system
    demo_system = IntegratedInformationMatrix(num_nodes=5, optimization_steps=50)
    consciousness_quantifier = ConsciousnessQuantifier(demo_system)
    
    # Generate test states
    current_state = torch.tensor([0.8, 0.6, 0.4, 0.7, 0.5])
    previous_state = torch.tensor([0.3, 0.4, 0.2, 0.5, 0.1])
    
    print(f"System Configuration:")
    print(f"  Nodes: {demo_system.num_nodes}")
    print(f"  Current State: {current_state.numpy()}")
    print(f"  Previous State: {previous_state.numpy()}")
    
    # Calculate integrated information
    print(f"\nIntegrated Information Analysis:")
    iit_result = demo_system.compute_integrated_information(
        current_state, previous_state, return_details=True
    )
    
    print(f"  Φ (Integrated Information): {iit_result['phi']:.4f}")
    print(f"  Integration Level: {iit_result['integration_level']}")
    print(f"  Best Partition: {iit_result['best_partition']}")
    print(f"  Connectivity Strength: {iit_result['connectivity_strength']:.4f}")
    print(f"  State Complexity: {iit_result['state_complexity']:.4f}")
    
    # Analyze information flow
    print(f"\nInformation Flow Analysis:")
    flow_result = demo_system.analyze_information_flow(current_state, previous_state)
    print(f"  Total Flow: {flow_result['total_flow']:.4f}")
    print(f"  Flow Balance: {flow_result['flow_balance']:.4f}")
    print(f"  Dominant Flows: {flow_result['dominant_flows'][:3]}")
    
    # Assess consciousness
    print(f"\nConsciousness Assessment:")
    consciousness_result = consciousness_quantifier.assess_consciousness_level(
        current_state, previous_state
    )
    
    print(f"  Consciousness Level: {consciousness_result['consciousness_level']}")
    print(f"  Consciousness Score: {consciousness_result['consciousness_score']:.4f}")
    print(f"  Coherence: {consciousness_result['coherence']:.4f}")
    print(f"  Complexity: {consciousness_result['complexity']:.4f}")
    
    # Optimization demonstration
    print(f"\nIntegration Optimization:")
    opt_result = demo_system.optimize_integration(
        current_state, previous_state, learning_rate=0.05
    )
    
    print(f"  Initial Φ: {opt_result['initial_phi']:.4f}")
    print(f"  Optimized Φ: {opt_result['optimized_phi']:.4f}")
    print(f"  Improvement: {opt_result['improvement']:.4f}")
    
    print(f"\n" + "="*80)
    print("Demonstration completed successfully!")
    print("="*80)