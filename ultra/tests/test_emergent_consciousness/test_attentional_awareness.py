"""
ULTRA Attentional Awareness Test Suite
======================================

Comprehensive test suite for the Attentional Awareness component of the
Emergent Consciousness Lattice, implementing rigorous mathematical validation,
performance benchmarking, and production-grade testing for:

- Salience calculation with multi-component analysis
- Attention allocation and resource distribution
- Processing enhancement mechanisms
- Temporal dynamics and habituation modeling
- Neural network components for attention control
- Integration with goal-directed behavior

Mathematical Foundation:
- S(x_i) = w₁·N(x_i) + w₂·R(x_i) + w₃·U(x_i) + w₄·G(x_i)
- A(x_i) = exp(S(x_i)/τ) / Σⱼ exp(S(x_j)/τ)
- P(x_i) = P₀(x_i) · (1 + γ · A(x_i))
- dA(x_i,t)/dt = α(S(x_i,t) - A(x_i,t)) - β∫₀ᵗ A(x_i,τ)exp(-(t-τ)/τₕ)dτ
"""

# Essential imports
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.distributions import Normal, Categorical, MultivariateNormal
import pytest
import logging
import time
import uuid
from typing import Dict, List, Tuple, Optional, Any, Union, Callable
from dataclasses import dataclass, field
from collections import defaultdict, deque
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from scipy.stats import entropy  # Fixed: moved from scipy.special to scipy.stats
from scipy.special import softmax  # This one is correct
from scipy.signal import correlate, find_peaks
from scipy.optimize import minimize
from sklearn.metrics import mutual_info_score
from sklearn.decomposition import PCA
from sklearn.cluster import KMeans
from sklearn.preprocessing import StandardScaler, MinMaxScaler
import networkx as nx
from abc import ABC, abstractmethod
import warnings
import json
import pickle
import pandas as pd
import json
import pickle
import time
import logging
import warnings
import argparse
import sys
import psutil
import pytest

from torch.distributions import Normal, Categorical, MultivariateNormal
from typing import Dict, List, Tuple, Optional, Any, Union, Callable
from dataclasses import dataclass, field
from collections import defaultdict, deque
from scipy import stats
from scipy.stats import entropy  # Fixed: moved from scipy.special to scipy.stats
from scipy.special import softmax  # This one is correct
from scipy.signal import correlate, find_peaks
from scipy.optimize import minimize
from sklearn.metrics import mutual_info_score
from sklearn.decomposition import PCA
from sklearn.cluster import KMeans
from sklearn.preprocessing import StandardScaler, MinMaxScaler
import networkx as nx
from abc import ABC, abstractmethod
import warnings
import json
import pickle
from pathlib import Path
import os
import sys
import threading
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
import multiprocessing as mp

# Try to import psutil, with fallback if not available
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False
    logging.warning("psutil not available. Memory monitoring will be limited.")

warnings.filterwarnings('ignore')

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# ============================================================================
# Mathematical Utility Functions
# ============================================================================

def sigmoid(x):
    """Sigmoid activation function."""
    # Clip x to prevent overflow
    x = np.clip(x, -500, 500)
    return 1.0 / (1.0 + np.exp(-x))

def tanh_safe(x):
    """Safe tanh function with clipping."""
    x = np.clip(x, -100, 100)
    return np.tanh(x)

def safe_log(x, eps=1e-10):
    """Safe logarithm function."""
    return np.log(np.maximum(x, eps))

def safe_exp(x):
    """Safe exponential function with clipping."""
    x = np.clip(x, -100, 100)
    return np.exp(x)



# ============================================================================
# Configuration and Constants
# ============================================================================

@dataclass
class AttentionTestConfig:
    """Configuration for attentional awareness testing."""
    
    # Test parameters
    n_test_stimuli: int = 1000
    n_temporal_steps: int = 500
    n_performance_trials: int = 100
    n_benchmark_iterations: int = 50
    
    # Stimulus generation
    stimulus_feature_dim: int = 64
    stimulus_noise_std: float = 0.1
    stimulus_variety_factor: float = 2.0
    
    # Goal simulation
    n_test_goals: int = 10
    goal_feature_dim: int = 32
    goal_priority_range: Tuple[float, float] = (0.1, 1.0)
    goal_urgency_range: Tuple[float, float] = (0.0, 1.0)
    
    # Attention parameters (matching ULTRA specs)
    attention_temperature: float = 2.0
    attention_decay_rate: float = 0.1
    salience_weights: List[float] = field(default_factory=lambda: [0.3, 0.4, 0.2, 0.1])
    habituation_time_constant: float = 50.0
    attention_enhancement_factor: float = 2.0
    
    # Neural network parameters
    network_learning_rate: float = 0.001
    network_batch_size: int = 32
    network_training_epochs: int = 10
    
    # Validation thresholds
    salience_min_threshold: float = 0.0
    salience_max_threshold: float = 4.0
    attention_sum_tolerance: float = 1e-6
    temporal_consistency_threshold: float = 0.1
    
    # Performance benchmarks
    min_salience_computations_per_second: float = 100.0
    min_attention_allocations_per_second: float = 50.0
    max_memory_usage_mb: float = 500.0
    
    # Visualization settings
    figure_size: Tuple[int, int] = (12, 8)
    plot_dpi: int = 300
    color_palette: str = 'viridis'

# ============================================================================
# Mathematical Validation Utilities
# ============================================================================

class AttentionMathValidator:
    """Mathematical validation utilities for attention mechanisms."""
    
    @staticmethod
    def validate_salience_computation(novelty: float, relevance: float, 
                                    uncertainty: float, gain: float,
                                    weights: List[float]) -> Dict[str, bool]:
        """
        Validate salience computation according to ULTRA specification.
        
        S(x_i) = w₁·N(x_i) + w₂·R(x_i) + w₃·U(x_i) + w₄·G(x_i)
        """
        validations = {}
        
        # Check component ranges
        validations['novelty_range'] = 0.0 <= novelty <= 1.0
        validations['relevance_range'] = 0.0 <= relevance <= 1.0
        validations['uncertainty_range'] = 0.0 <= uncertainty <= 1.0
        validations['gain_range'] = 0.0 <= gain <= 1.0
        
        # Check weight validity
        validations['weights_length'] = len(weights) == 4
        validations['weights_positive'] = all(w >= 0 for w in weights)
        validations['weights_normalized'] = abs(sum(weights) - 1.0) < 1e-6
        
        # Compute salience
        computed_salience = (weights[0] * novelty + weights[1] * relevance + 
                           weights[2] * uncertainty + weights[3] * gain)
        
        # Check salience bounds
        validations['salience_range'] = 0.0 <= computed_salience <= sum(weights)
        
        # Check mathematical consistency
        manual_salience = sum(w * v for w, v in zip(weights, [novelty, relevance, uncertainty, gain]))
        validations['computation_consistency'] = abs(computed_salience - manual_salience) < 1e-10
        
        return validations
    
    @staticmethod
    def validate_attention_allocation(saliences: Dict[str, float], 
                                    allocations: Dict[str, float],
                                    temperature: float) -> Dict[str, bool]:
        """
        Validate attention allocation using softmax.
        
        A(x_i) = exp(S(x_i)/τ) / Σⱼ exp(S(x_j)/τ)
        """
        validations = {}
        
        if not saliences or not allocations:
            return {'empty_inputs': False}
        
        # Check input consistency
        validations['same_keys'] = set(saliences.keys()) == set(allocations.keys())
        
        # Check allocation properties
        allocation_values = list(allocations.values())
        validations['allocations_positive'] = all(a >= 0 for a in allocation_values)
        validations['allocations_sum_to_one'] = abs(sum(allocation_values) - 1.0) < 1e-6
        
        # Validate softmax computation
        salience_values = np.array([saliences[key] for key in saliences.keys()])
        expected_allocations = softmax(salience_values / temperature)
        actual_allocations = np.array([allocations[key] for key in saliences.keys()])
        
        validations['softmax_consistency'] = np.allclose(expected_allocations, actual_allocations, rtol=1e-5)
        
        # Check attention ordering
        salience_order = sorted(saliences.items(), key=lambda x: x[1], reverse=True)
        allocation_order = sorted(allocations.items(), key=lambda x: x[1], reverse=True)
        
        # Top salience should get top attention (with some tolerance for similar values)
        if len(salience_order) > 1:
            top_salience_key = salience_order[0][0]
            top_attention_key = allocation_order[0][0]
            validations['attention_follows_salience'] = top_salience_key == top_attention_key
        
        return validations
    
    @staticmethod
    def validate_processing_enhancement(baseline: float, enhanced: float,
                                      attention_weight: float,
                                      enhancement_factor: float) -> Dict[str, bool]:
        """
        Validate processing enhancement.
        
        P(x_i) = P₀(x_i) · (1 + γ · A(x_i))
        """
        validations = {}
        
        # Check input validity
        validations['baseline_positive'] = baseline > 0
        validations['attention_weight_range'] = 0.0 <= attention_weight <= 1.0
        validations['enhancement_factor_positive'] = enhancement_factor > 0
        
        # Check enhancement computation
        expected_enhanced = baseline * (1 + enhancement_factor * attention_weight)
        validations['enhancement_computation'] = abs(enhanced - expected_enhanced) < 1e-10
        
        # Check enhancement properties
        validations['enhancement_increases_processing'] = enhanced >= baseline
        validations['max_enhancement_bound'] = enhanced <= baseline * (1 + enhancement_factor)
        
        return validations
    
    @staticmethod
    def validate_temporal_dynamics(attention_history: List[float],
                                 salience_history: List[float],
                                 decay_rate: float,
                                 habituation_constant: float,
                                 dt: float = 1.0) -> Dict[str, bool]:
        """
        Validate temporal dynamics of attention.
        
        dA(x_i,t)/dt = α(S(x_i,t) - A(x_i,t)) - β∫₀ᵗ A(x_i,τ)exp(-(t-τ)/τₜₕ)dτ
        """
        validations = {}
        
        if len(attention_history) < 2 or len(salience_history) < 2:
            return {'insufficient_data': False}
        
        # Check temporal consistency
        validations['history_lengths_match'] = len(attention_history) == len(salience_history)
        
        # Check attention bounds over time
        validations['attention_bounded'] = all(0.0 <= a <= 1.0 for a in attention_history)
        
        # Check for reasonable temporal variation
        attention_variance = np.var(attention_history)
        validations['attention_varies'] = attention_variance > 1e-6
        
        # Check decay behavior
        if len(attention_history) > 10:
            # Check if attention shows some form of temporal structure
            autocorr = np.corrcoef(attention_history[:-1], attention_history[1:])[0, 1]
            validations['temporal_correlation'] = not np.isnan(autocorr)
        
        # Validate differential equation approximation for last few steps
        if len(attention_history) >= 5:
            # Check last derivative approximation
            a_current = attention_history[-1]
            a_previous = attention_history[-2]
            s_current = salience_history[-1]
            
            # Simplified habituation integral (exponential moving average)
            habituation_integral = 0.0
            for i, a_past in enumerate(attention_history[-5:]):
                time_diff = (len(attention_history) - 1 - i) * dt
                weight = np.exp(-time_diff / habituation_constant)
                habituation_integral += a_past * weight
            
            # Expected derivative
            expected_derivative = decay_rate * (s_current - a_current) - decay_rate * habituation_integral
            actual_derivative = (a_current - a_previous) / dt
            
            # Allow for numerical approximation errors
            validations['derivative_approximation'] = abs(expected_derivative - actual_derivative) < 0.5
        
        return validations
    
    @staticmethod
    def compute_attention_metrics(attention_allocations: Dict[str, float]) -> Dict[str, float]:
        """Compute comprehensive attention metrics."""
        if not attention_allocations:
            return {}
        
        values = np.array(list(attention_allocations.values()))
        
        metrics = {
            'entropy': entropy(values) if len(values) > 1 else 0.0,
            'gini_coefficient': AttentionMathValidator._gini_coefficient(values),
            'concentration_ratio': np.max(values) if len(values) > 0 else 0.0,
            'effective_stimuli': 1.0 / np.sum(values**2) if np.sum(values**2) > 0 else 0.0,
            'attention_spread': np.std(values),
            'uniformity_index': 1.0 - (len(values) * np.var(values)) if len(values) > 1 else 1.0
        }
        
        return metrics
    
    @staticmethod
    def _gini_coefficient(values: np.ndarray) -> float:
        """Compute Gini coefficient for attention distribution."""
        if len(values) == 0:
            return 0.0
        
        # Sort values
        sorted_values = np.sort(values)
        n = len(sorted_values)
        
        # Compute Gini coefficient
        cumulative_sum = np.cumsum(sorted_values)
        gini = (2 * np.sum((np.arange(1, n + 1) * sorted_values))) / (n * cumulative_sum[-1]) - (n + 1) / n
        
        return gini

# ============================================================================
# Test Data Generation Utilities
# ============================================================================

class StimulusGenerator:
    """Advanced stimulus generation for attention testing."""
    
    def __init__(self, config: AttentionTestConfig):
        self.config = config
        self.feature_dim = config.stimulus_feature_dim
        self.noise_std = config.stimulus_noise_std
        
        # Initialize feature generators
        self._initialize_generators()
    
    def _initialize_generators(self):
        """Initialize various feature generation mechanisms."""
        # Gaussian mixture components for realistic feature distributions
        self.n_components = 5
        self.component_means = np.random.randn(self.n_components, self.feature_dim) * 2
        self.component_covariances = []
        
        for _ in range(self.n_components):
            # Generate positive definite covariance matrices
            A = np.random.randn(self.feature_dim, self.feature_dim // 2)
            cov = A @ A.T + np.eye(self.feature_dim) * 0.1
            self.component_covariances.append(cov)
        
        # Temporal correlation parameters
        self.temporal_correlation = 0.7
        self.last_stimulus = None
    
    def generate_stimulus_batch(self, n_stimuli: int, 
                              stimulus_type: str = 'mixed') -> List[np.ndarray]:
        """Generate a batch of test stimuli with various characteristics."""
        stimuli = []
        
        for i in range(n_stimuli):
            if stimulus_type == 'gaussian':
                stimulus = self._generate_gaussian_stimulus()
            elif stimulus_type == 'sparse':
                stimulus = self._generate_sparse_stimulus()
            elif stimulus_type == 'correlated':
                stimulus = self._generate_correlated_stimulus()
            elif stimulus_type == 'mixture':
                stimulus = self._generate_mixture_stimulus()
            elif stimulus_type == 'temporal':
                stimulus = self._generate_temporal_stimulus()
            else:  # mixed
                stimulus_types = ['gaussian', 'sparse', 'correlated', 'mixture', 'temporal']
                chosen_type = np.random.choice(stimulus_types)
                stimulus = getattr(self, f'_generate_{chosen_type}_stimulus')()
            
            stimuli.append(stimulus)
        
        return stimuli
    
    def _generate_gaussian_stimulus(self) -> np.ndarray:
        """Generate Gaussian-distributed stimulus."""
        return np.random.randn(self.feature_dim) * self.config.stimulus_variety_factor
    
    def _generate_sparse_stimulus(self) -> np.ndarray:
        """Generate sparse stimulus with few active features."""
        stimulus = np.zeros(self.feature_dim)
        n_active = np.random.randint(1, self.feature_dim // 4)
        active_indices = np.random.choice(self.feature_dim, n_active, replace=False)
        stimulus[active_indices] = np.random.randn(n_active) * 3
        return stimulus
    
    def _generate_correlated_stimulus(self) -> np.ndarray:
        """Generate stimulus with correlated features."""
        # Create base features
        base_features = np.random.randn(self.feature_dim // 4)
        
        # Create correlated features
        stimulus = np.zeros(self.feature_dim)
        for i in range(self.feature_dim):
            base_idx = i % len(base_features)
            correlation = 0.8
            stimulus[i] = (correlation * base_features[base_idx] + 
                          np.sqrt(1 - correlation**2) * np.random.randn())
        
        return stimulus * self.config.stimulus_variety_factor
    
    def _generate_mixture_stimulus(self) -> np.ndarray:
        """Generate stimulus from Gaussian mixture model."""
        # Select component
        component_idx = np.random.randint(self.n_components)
        
        # Generate from selected component
        mean = self.component_means[component_idx]
        cov = self.component_covariances[component_idx]
        
        stimulus = np.random.multivariate_normal(mean, cov)
        return stimulus
    
    def _generate_temporal_stimulus(self) -> np.ndarray:
        """Generate temporally correlated stimulus."""
        if self.last_stimulus is None:
            self.last_stimulus = np.random.randn(self.feature_dim)
        
        # Generate temporally correlated stimulus
        noise = np.random.randn(self.feature_dim) * self.noise_std
        stimulus = (self.temporal_correlation * self.last_stimulus + 
                   np.sqrt(1 - self.temporal_correlation**2) * noise)
        
        self.last_stimulus = stimulus
        return stimulus * self.config.stimulus_variety_factor
    
    def generate_goal_features(self, n_goals: int) -> List[Dict[str, Any]]:
        """Generate realistic goal features for attention testing."""
        goals = []
        
        goal_types = ['learning', 'problem_solving', 'optimization', 'exploration', 'maintenance']
        
        for i in range(n_goals):
            goal_type = np.random.choice(goal_types)
            
            # Generate goal features based on type
            if goal_type == 'learning':
                priority = np.random.uniform(0.6, 0.9)
                urgency = np.random.uniform(0.3, 0.7)
                complexity = np.random.uniform(0.4, 0.8)
            elif goal_type == 'problem_solving':
                priority = np.random.uniform(0.7, 1.0)
                urgency = np.random.uniform(0.5, 0.9)
                complexity = np.random.uniform(0.6, 1.0)
            elif goal_type == 'optimization':
                priority = np.random.uniform(0.4, 0.7)
                urgency = np.random.uniform(0.2, 0.6)
                complexity = np.random.uniform(0.3, 0.7)
            elif goal_type == 'exploration':
                priority = np.random.uniform(0.3, 0.6)
                urgency = np.random.uniform(0.1, 0.4)
                complexity = np.random.uniform(0.2, 0.6)
            else:  # maintenance
                priority = np.random.uniform(0.2, 0.5)
                urgency = np.random.uniform(0.0, 0.3)
                complexity = np.random.uniform(0.1, 0.4)
            
            # Create goal feature vector
            goal_features = np.random.randn(self.config.goal_feature_dim) * 0.5
            goal_features[:4] = [priority, urgency, complexity, np.random.uniform(0.0, 1.0)]
            
            goals.append({
                'type': goal_type,
                'priority': priority,
                'urgency': urgency,
                'complexity': complexity,
                'features': goal_features,
                'utility': priority * urgency,
                'progress': np.random.uniform(0.0, 0.8)
            })
        
        return goals

class NoveltySimulator:
    """Simulate novelty patterns for testing novelty detection."""
    
    def __init__(self, feature_dim: int):
        self.feature_dim = feature_dim
        self.seen_stimuli = []
        self.novelty_threshold = 0.5
        
    def compute_true_novelty(self, stimulus: np.ndarray) -> float:
        """Compute ground truth novelty based on stimulus history."""
        if len(self.seen_stimuli) == 0:
            self.seen_stimuli.append(stimulus.copy())
            return 1.0  # First stimulus is completely novel
        
        # Compute minimum distance to seen stimuli
        distances = [np.linalg.norm(stimulus - seen) for seen in self.seen_stimuli[-50:]]  # Consider last 50
        min_distance = min(distances)
        
        # Convert distance to novelty score
        novelty = np.tanh(min_distance / 2.0)  # Normalize to [0, 1]
        
        # Update history
        self.seen_stimuli.append(stimulus.copy())
        
        return novelty

# ============================================================================
# Mock Attention System for Testing
# ============================================================================

class MockAttentionalAwareness:
    """Mock implementation of AttentionalAwareness for controlled testing."""
    
    def __init__(self, config: AttentionTestConfig):
        self.config = config
        
        # Initialize simplified components
        self.attention_allocations = {}
        self.salience_scores = {}
        self.processing_enhancements = {}
        self.attention_history = defaultdict(list)
        self.habituation_tracker = defaultdict(float)
        
        # Mock neural networks with deterministic behavior
        self.novelty_weights = np.random.randn(config.stimulus_feature_dim, 1) * 0.1
        self.relevance_weights = np.random.randn(config.stimulus_feature_dim + config.goal_feature_dim, 1) * 0.1
        self.uncertainty_weights = np.random.randn(config.stimulus_feature_dim, 1) * 0.1
        self.gain_weights = np.random.randn(config.stimulus_feature_dim, 1) * 0.1
        
        # Track computation calls for validation
        self.computation_calls = defaultdict(int)
        
    def calculate_salience(self, stimulus_id: str, stimulus_features: np.ndarray,
                          current_goals: List[Dict[str, Any]]) -> float:
        """Mock salience calculation with deterministic behavior."""
        self.computation_calls['calculate_salience'] += 1
        
        # Mock novelty (based on feature magnitude)
        novelty = np.tanh(np.abs(stimulus_features @ self.novelty_weights).item())
        
        # Mock relevance (based on goal alignment)
        relevance = 0.5  # Default
        if current_goals:
            goal_features = current_goals[0].get('features', np.zeros(self.config.goal_feature_dim))
            combined_features = np.concatenate([stimulus_features, goal_features])
            relevance = np.sigmoid(combined_features @ self.relevance_weights).item()
        
        # Mock uncertainty (based on feature variance)
        uncertainty = np.tanh(np.std(stimulus_features))
        
        # Mock gain (based on feature energy)
        gain = np.tanh(np.linalg.norm(stimulus_features) / np.sqrt(len(stimulus_features)))
        
        # Combine components
        weights = self.config.salience_weights
        salience = (weights[0] * novelty + weights[1] * relevance + 
                   weights[2] * uncertainty + weights[3] * gain)
        
        # Store detailed scores
        self.salience_scores[stimulus_id] = {
            'total': salience,
            'novelty': novelty,
            'relevance': relevance,
            'uncertainty': uncertainty,
            'gain': gain,
            'timestamp': time.time()
        }
        
        return salience
    
    def allocate_attention(self, stimuli_saliences: Dict[str, float]) -> Dict[str, float]:
        """Mock attention allocation using softmax."""
        self.computation_calls['allocate_attention'] += 1
        
        if not stimuli_saliences:
            return {}
        
        # Apply softmax
        salience_values = np.array(list(stimuli_saliences.values()))
        attention_weights = softmax(salience_values / self.config.attention_temperature)
        
        # Create allocation dictionary
        allocations = {}
        for i, stimulus_id in enumerate(stimuli_saliences.keys()):
            allocations[stimulus_id] = attention_weights[i]
        
        # Update internal state
        self.attention_allocations.update(allocations)
        
        # Update history
        for stimulus_id, allocation in allocations.items():
            self.attention_history[stimulus_id].append({
                'allocation': allocation,
                'timestamp': time.time()
            })
        
        return allocations
    
    def enhance_processing(self, stimulus_id: str, baseline_processing: float) -> float:
        """Mock processing enhancement."""
        self.computation_calls['enhance_processing'] += 1
        
        if stimulus_id not in self.attention_allocations:
            return baseline_processing
        
        attention_weight = self.attention_allocations[stimulus_id]
        enhancement_factor = self.config.attention_enhancement_factor
        
        enhanced_processing = baseline_processing * (1 + enhancement_factor * attention_weight)
        
        # Store enhancement details
        self.processing_enhancements[stimulus_id] = {
            'baseline': baseline_processing,
            'enhanced': enhanced_processing,
            'enhancement_factor': enhancement_factor * attention_weight,
            'timestamp': time.time()
        }
        
        return enhanced_processing
    
    def update_attention_dynamics(self, dt: float = 1.0):
        """Mock attention dynamics update."""
        self.computation_calls['update_attention_dynamics'] += 1
        
        current_time = time.time()
        decay_rate = self.config.attention_decay_rate
        tau_h = self.config.habituation_time_constant
        
        # Update habituation and dynamics
        for stimulus_id in list(self.attention_allocations.keys()):
            current_allocation = self.attention_allocations[stimulus_id]
            
            # Simplified habituation calculation
            history = self.attention_history[stimulus_id]
            habituation_integral = 0.0
            
            for h in history[-10:]:  # Last 10 steps
                time_diff = current_time - h['timestamp']
                weight = np.exp(-time_diff / tau_h)
                habituation_integral += h['allocation'] * weight
            
            self.habituation_tracker[stimulus_id] = habituation_integral
            
            # Apply dynamics (simplified)
            if stimulus_id in self.salience_scores:
                original_salience = self.salience_scores[stimulus_id]['total']
                salience_term = original_salience * decay_rate
                habituation_term = decay_rate * habituation_integral
                
                new_allocation = current_allocation + dt * (salience_term - habituation_term)
                new_allocation = np.clip(new_allocation, 0.0, 1.0)
                
                self.attention_allocations[stimulus_id] = new_allocation
    
    def get_attention_summary(self) -> Dict[str, Any]:
        """Get summary of attention state."""
        if not self.attention_allocations:
            return {'total_stimuli': 0, 'top_attended': None, 'attention_entropy': 0.0}
        
        allocations = list(self.attention_allocations.values())
        attention_entropy = entropy(allocations) if len(allocations) > 1 else 0.0
        
        top_stimulus = max(self.attention_allocations.items(), key=lambda x: x[1])
        
        allocation_values = np.array(allocations)
        
        return {
            'total_stimuli': len(self.attention_allocations),
            'top_attended': {'stimulus': top_stimulus[0], 'allocation': top_stimulus[1]},
            'attention_entropy': attention_entropy,
            'mean_allocation': np.mean(allocation_values),
            'std_allocation': np.std(allocation_values),
            'max_allocation': np.max(allocation_values),
            'total_habituation': sum(self.habituation_tracker.values()),
            'computation_calls': dict(self.computation_calls)
        }

# ============================================================================
# Core Test Classes
# ============================================================================

class TestSalienceCalculation:
    """Comprehensive tests for salience calculation mechanisms."""
    
    def __init__(self, config: AttentionTestConfig):
        self.config = config
        self.validator = AttentionMathValidator()
        self.stimulus_generator = StimulusGenerator(config)
        self.novelty_simulator = NoveltySimulator(config.stimulus_feature_dim)
        
    def test_salience_component_ranges(self):
        """Test that all salience components stay within valid ranges."""
        logger.info("Testing salience component ranges")
        
        # Generate test stimuli
        stimuli = self.stimulus_generator.generate_stimulus_batch(100, 'mixed')
        goals = self.stimulus_generator.generate_goal_features(5)
        
        # Create mock attention system
        attention_system = MockAttentionalAwareness(self.config)
        
        results = {
            'novelty_range_violations': 0,
            'relevance_range_violations': 0,
            'uncertainty_range_violations': 0,
            'gain_range_violations': 0,
            'salience_range_violations': 0
        }
        
        for i, stimulus in enumerate(stimuli):
            stimulus_id = f"stimulus_{i}"
            salience = attention_system.calculate_salience(stimulus_id, stimulus, goals)
            
            salience_details = attention_system.salience_scores[stimulus_id]
            
            # Check component ranges
            if not (0.0 <= salience_details['novelty'] <= 1.0):
                results['novelty_range_violations'] += 1
            if not (0.0 <= salience_details['relevance'] <= 1.0):
                results['relevance_range_violations'] += 1
            if not (0.0 <= salience_details['uncertainty'] <= 1.0):
                results['uncertainty_range_violations'] += 1
            if not (0.0 <= salience_details['gain'] <= 1.0):
                results['gain_range_violations'] += 1
            if not (0.0 <= salience <= sum(self.config.salience_weights)):
                results['salience_range_violations'] += 1
        
        # Validate results
        assert results['novelty_range_violations'] == 0, f"Novelty range violations: {results['novelty_range_violations']}"
        assert results['relevance_range_violations'] == 0, f"Relevance range violations: {results['relevance_range_violations']}"
        assert results['uncertainty_range_violations'] == 0, f"Uncertainty range violations: {results['uncertainty_range_violations']}"
        assert results['gain_range_violations'] == 0, f"Gain range violations: {results['gain_range_violations']}"
        assert results['salience_range_violations'] == 0, f"Salience range violations: {results['salience_range_violations']}"
        
        logger.info("✓ All salience components stay within valid ranges")
        return results
    
    def test_salience_mathematical_consistency(self):
        """Test mathematical consistency of salience computation."""
        logger.info("Testing salience mathematical consistency")
        
        # Generate test data
        stimuli = self.stimulus_generator.generate_stimulus_batch(50, 'gaussian')
        goals = self.stimulus_generator.generate_goal_features(3)
        
        attention_system = MockAttentionalAwareness(self.config)
        
        validation_results = []
        
        for i, stimulus in enumerate(stimuli):
            stimulus_id = f"stimulus_{i}"
            salience = attention_system.calculate_salience(stimulus_id, stimulus, goals)
            
            salience_details = attention_system.salience_scores[stimulus_id]
            
            # Validate mathematical consistency
            validation = self.validator.validate_salience_computation(
                salience_details['novelty'],
                salience_details['relevance'],
                salience_details['uncertainty'],
                salience_details['gain'],
                self.config.salience_weights
            )
            
            validation_results.append(validation)
        
        # Check that all validations pass
        for validation in validation_results:
            for check, passed in validation.items():
                assert passed, f"Salience validation failed: {check}"
        
        logger.info("✓ Salience computation is mathematically consistent")
        return validation_results
    
    def test_salience_goal_sensitivity(self):
        """Test salience sensitivity to different goals."""
        logger.info("Testing salience goal sensitivity")
        
        # Create test stimulus
        stimulus = self.stimulus_generator.generate_stimulus_batch(1, 'gaussian')[0]
        
        # Create different goal configurations
        goal_configs = [
            [{'priority': 0.9, 'urgency': 0.8, 'features': np.random.randn(self.config.goal_feature_dim)}],
            [{'priority': 0.3, 'urgency': 0.2, 'features': np.random.randn(self.config.goal_feature_dim)}],
            [{'priority': 0.6, 'urgency': 0.9, 'features': np.random.randn(self.config.goal_feature_dim)}],
            []  # No goals
        ]
        
        attention_system = MockAttentionalAwareness(self.config)
        salience_values = []
        
        for i, goals in enumerate(goal_configs):
            stimulus_id = f"stimulus_{i}"
            salience = attention_system.calculate_salience(stimulus_id, stimulus, goals)
            salience_values.append(salience)
        
        # Check that salience varies with different goal configurations
        salience_variance = np.var(salience_values)
        assert salience_variance > 1e-6, "Salience should vary with different goals"
        
        # Check that higher priority/urgency goals generally increase relevance component
        high_priority_salience = salience_values[0]
        low_priority_salience = salience_values[1]
        
        # At least some difference should be observed
        assert abs(high_priority_salience - low_priority_salience) > 1e-3, "High and low priority goals should produce different salience"
        
        logger.info("✓ Salience shows appropriate sensitivity to goals")
        return {
            'salience_values': salience_values,
            'salience_variance': salience_variance
        }
    
    def test_salience_stimulus_diversity(self):
        """Test salience computation across diverse stimulus types."""
        logger.info("Testing salience across diverse stimuli")
        
        stimulus_types = ['gaussian', 'sparse', 'correlated', 'mixture', 'temporal']
        goals = self.stimulus_generator.generate_goal_features(3)
        
        attention_system = MockAttentionalAwareness(self.config)
        
        results = {}
        
        for stimulus_type in stimulus_types:
            stimuli = self.stimulus_generator.generate_stimulus_batch(20, stimulus_type)
            salience_values = []
            
            for i, stimulus in enumerate(stimuli):
                stimulus_id = f"{stimulus_type}_{i}"
                salience = attention_system.calculate_salience(stimulus_id, stimulus, goals)
                salience_values.append(salience)
            
            results[stimulus_type] = {
                'mean_salience': np.mean(salience_values),
                'std_salience': np.std(salience_values),
                'min_salience': np.min(salience_values),
                'max_salience': np.max(salience_values),
                'salience_values': salience_values
            }
        
        # Validate that different stimulus types produce different salience patterns
        mean_saliences = [results[st]['mean_salience'] for st in stimulus_types]
        salience_type_variance = np.var(mean_saliences)
        
        assert salience_type_variance > 1e-6, "Different stimulus types should produce different salience patterns"
        
        # Check that all stimulus types produce valid salience ranges
        for stimulus_type, stats in results.items():
            assert stats['min_salience'] >= 0.0, f"Minimum salience for {stimulus_type} is negative"
            assert stats['max_salience'] <= sum(self.config.salience_weights), f"Maximum salience for {stimulus_type} exceeds bounds"
        
        logger.info("✓ Salience computation works across diverse stimulus types")
        return results

class TestAttentionAllocation:
    """Comprehensive tests for attention allocation mechanisms."""
    
    def __init__(self, config: AttentionTestConfig):
        self.config = config
        self.validator = AttentionMathValidator()
        self.stimulus_generator = StimulusGenerator(config)
        
    def test_attention_allocation_properties(self):
        """Test fundamental properties of attention allocation."""
        logger.info("Testing attention allocation properties")
        
        attention_system = MockAttentionalAwareness(self.config)
        
        # Test with various numbers of stimuli
        test_cases = [
            {'n_stimuli': 1, 'case': 'single_stimulus'},
            {'n_stimuli': 2, 'case': 'two_stimuli'},
            {'n_stimuli': 5, 'case': 'five_stimuli'},
            {'n_stimuli': 10, 'case': 'ten_stimuli'},
            {'n_stimuli': 20, 'case': 'twenty_stimuli'}
        ]
        
        results = {}
        
        for test_case in test_cases:
            n_stimuli = test_case['n_stimuli']
            case_name = test_case['case']
            
            # Generate stimuli and compute saliences
            stimuli_saliences = {}
            for i in range(n_stimuli):
                # Create varied salience values
                salience = np.random.uniform(0.1, 1.0) * np.random.choice([0.5, 1.0, 1.5, 2.0])
                stimuli_saliences[f"stimulus_{i}"] = salience
            
            # Allocate attention
            allocations = attention_system.allocate_attention(stimuli_saliences)
            
            # Validate allocation properties
            validation = self.validator.validate_attention_allocation(
                stimuli_saliences, allocations, self.config.attention_temperature
            )
            
            # Compute attention metrics
            metrics = self.validator.compute_attention_metrics(allocations)
            
            results[case_name] = {
                'n_stimuli': n_stimuli,
                'validation': validation,
                'metrics': metrics,
                'allocations': allocations,
                'saliences': stimuli_saliences
            }
            
            # Assert validations pass
            for check, passed in validation.items():
                assert passed, f"Attention allocation validation failed for {case_name}: {check}"
        
        logger.info("✓ Attention allocation satisfies all mathematical properties")
        return results
    
    def test_attention_temperature_effects(self):
        """Test effects of temperature parameter on attention allocation."""
        logger.info("Testing attention temperature effects")
        
        # Create test saliences with clear differences
        saliences = {
            'high_salience': 2.0,
            'medium_salience': 1.0,
            'low_salience': 0.5
        }
        
        temperatures = [0.1, 0.5, 1.0, 2.0, 5.0, 10.0]
        
        results = {}
        
        for temp in temperatures:
            # Create temporary config with different temperature
            temp_config = AttentionTestConfig()
            temp_config.attention_temperature = temp
            
            attention_system = MockAttentionalAwareness(temp_config)
            allocations = attention_system.allocate_attention(saliences)
            
            # Compute metrics
            metrics = self.validator.compute_attention_metrics(allocations)
            
            results[temp] = {
                'temperature': temp,
                'allocations': allocations,
                'entropy': metrics['entropy'],
                'concentration_ratio': metrics['concentration_ratio'],
                'gini_coefficient': metrics['gini_coefficient']
            }
        
        # Validate temperature effects
        entropies = [results[temp]['entropy'] for temp in temperatures]
        concentration_ratios = [results[temp]['concentration_ratio'] for temp in temperatures]
        
        # Lower temperature should lead to lower entropy (more concentrated attention)
        # Higher temperature should lead to higher entropy (more uniform attention)
        assert entropies[0] < entropies[-1], "Low temperature should produce lower entropy than high temperature"
        assert concentration_ratios[0] > concentration_ratios[-1], "Low temperature should produce higher concentration"
        
        # Check monotonicity trends (allowing for small numerical variations)
        entropy_trend_violations = 0
        for i in range(len(entropies) - 1):
            if entropies[i] > entropies[i + 1] + 0.1:  # Allow small tolerance
                entropy_trend_violations += 1
        
        assert entropy_trend_violations <= 1, "Entropy should generally increase with temperature"
        
        logger.info("✓ Temperature parameter correctly affects attention allocation")
        return results
    
    def test_attention_scaling_properties(self):
        """Test how attention allocation scales with number of stimuli."""
        logger.info("Testing attention scaling properties")
        
        attention_system = MockAttentionalAwareness(self.config)
        
        stimuli_counts = [2, 5, 10, 20, 50, 100]
        results = {}
        
        for n_stimuli in stimuli_counts:
            # Generate stimuli with random saliences
            saliences = {}
            for i in range(n_stimuli):
                salience = np.random.exponential(1.0)  # Exponential distribution for realism
                saliences[f"stimulus_{i}"] = salience
            
            # Measure allocation time
            start_time = time.time()
            allocations = attention_system.allocate_attention(saliences)
            allocation_time = time.time() - start_time
            
            # Compute metrics
            metrics = self.validator.compute_attention_metrics(allocations)
            
            results[n_stimuli] = {
                'n_stimuli': n_stimuli,
                'allocation_time': allocation_time,
                'entropy': metrics['entropy'],
                'effective_stimuli': metrics['effective_stimuli'],
                'uniformity_index': metrics['uniformity_index'],
                'max_allocation': np.max(list(allocations.values())),
                'min_allocation': np.min(list(allocations.values()))
            }
        
        # Validate scaling properties
        allocation_times = [results[n]['allocation_time'] for n in stimuli_counts]
        entropies = [results[n]['entropy'] for n in stimuli_counts]
        effective_stimuli = [results[n]['effective_stimuli'] for n in stimuli_counts]
        
        # Allocation time should scale reasonably (not exponentially)
        max_time = max(allocation_times)
        assert max_time < 1.0, f"Attention allocation taking too long: {max_time:.3f}s for {max(stimuli_counts)} stimuli"
        
        # Entropy should generally increase with more stimuli (but plateau)
        assert entropies[-1] >= entropies[0], "Entropy should not decrease with more stimuli"
        
        # Effective stimuli should be reasonable
        max_effective = max(effective_stimuli)
        assert max_effective <= max(stimuli_counts), "Effective stimuli cannot exceed actual stimuli count"
        
        logger.info("✓ Attention allocation scales appropriately with stimulus count")
        return results
    
    def test_attention_consistency(self):
        """Test consistency of attention allocation across repeated calls."""
        logger.info("Testing attention allocation consistency")
        
        attention_system = MockAttentionalAwareness(self.config)
        
        # Fixed saliences for consistency testing
        saliences = {
            'stimulus_1': 1.5,
            'stimulus_2': 1.0,
            'stimulus_3': 0.8,
            'stimulus_4': 0.3
        }
        
        # Compute allocations multiple times
        n_trials = 50
        allocation_results = []
        
        for trial in range(n_trials):
            allocations = attention_system.allocate_attention(saliences)
            allocation_results.append(allocations)
        
        # Check consistency across trials
        for stimulus_id in saliences.keys():
            allocations_for_stimulus = [result[stimulus_id] for result in allocation_results]
            
            # All allocations should be identical (deterministic softmax)
            allocation_std = np.std(allocations_for_stimulus)
            assert allocation_std < 1e-10, f"Attention allocation inconsistent for {stimulus_id}: std={allocation_std}"
        
        # Verify ordering consistency
        expected_order = sorted(saliences.items(), key=lambda x: x[1], reverse=True)
        for result in allocation_results:
            actual_order = sorted(result.items(), key=lambda x: x[1], reverse=True)
            
            for i, (expected_stimulus, actual_stimulus) in enumerate(zip(expected_order, actual_order)):
                assert expected_stimulus[0] == actual_stimulus[0], f"Attention ordering inconsistent at position {i}"
        
        logger.info("✓ Attention allocation is consistent across repeated calls")
        return {
            'n_trials': n_trials,
            'allocation_std': {k: np.std([r[k] for r in allocation_results]) for k in saliences.keys()},
            'order_consistency': True
        }

class TestProcessingEnhancement:
    """Tests for attention-based processing enhancement."""
    
    def __init__(self, config: AttentionTestConfig):
        self.config = config
        self.validator = AttentionMathValidator()
        
    def test_enhancement_mathematical_properties(self):
        """Test mathematical properties of processing enhancement."""
        logger.info("Testing processing enhancement mathematical properties")
        
        attention_system = MockAttentionalAwareness(self.config)
        
        # Set up test scenarios
        test_scenarios = [
            {'baseline': 1.0, 'attention': 0.0, 'case': 'no_attention'},
            {'baseline': 1.0, 'attention': 0.5, 'case': 'medium_attention'},
            {'baseline': 1.0, 'attention': 1.0, 'case': 'full_attention'},
            {'baseline': 2.0, 'attention': 0.5, 'case': 'high_baseline'},
            {'baseline': 0.1, 'attention': 0.8, 'case': 'low_baseline'}
        ]
        
        results = {}
        
        for scenario in test_scenarios:
            baseline = scenario['baseline']
            attention_weight = scenario['attention']
            case_name = scenario['case']
            
            # Set up attention allocation
            stimulus_id = f"test_stimulus_{case_name}"
            attention_system.attention_allocations[stimulus_id] = attention_weight
            
            # Compute enhancement
            enhanced = attention_system.enhance_processing(stimulus_id, baseline)
            
            # Validate enhancement
            validation = self.validator.validate_processing_enhancement(
                baseline, enhanced, attention_weight, self.config.attention_enhancement_factor
            )
            
            results[case_name] = {
                'baseline': baseline,
                'attention_weight': attention_weight,
                'enhanced': enhanced,
                'enhancement_ratio': enhanced / baseline,
                'validation': validation
            }
            
            # Assert validation passes
            for check, passed in validation.items():
                assert passed, f"Processing enhancement validation failed for {case_name}: {check}"
        
        # Test monotonicity: higher attention should lead to higher enhancement
        medium_enhancement = results['medium_attention']['enhanced']
        full_enhancement = results['full_attention']['enhanced']
        no_enhancement = results['no_attention']['enhanced']
        
        assert no_enhancement <= medium_enhancement <= full_enhancement, "Enhancement should be monotonic with attention"
        
        logger.info("✓ Processing enhancement satisfies all mathematical properties")
        return results
    
    def test_enhancement_scaling(self):
        """Test enhancement scaling with different enhancement factors."""
        logger.info("Testing enhancement scaling")
        
        # Test different enhancement factors
        enhancement_factors = [0.5, 1.0, 2.0, 5.0, 10.0]
        baseline_processing = 1.0
        attention_weight = 0.7
        
        results = {}
        
        for factor in enhancement_factors:
            # Create config with specific enhancement factor
            config = AttentionTestConfig()
            config.attention_enhancement_factor = factor
            
            attention_system = MockAttentionalAwareness(config)
            
            # Set attention allocation
            stimulus_id = "test_stimulus"
            attention_system.attention_allocations[stimulus_id] = attention_weight
            
            # Compute enhancement
            enhanced = attention_system.enhance_processing(stimulus_id, baseline_processing)
            
            results[factor] = {
                'enhancement_factor': factor,
                'enhanced_processing': enhanced,
                'enhancement_ratio': enhanced / baseline_processing,
                'absolute_enhancement': enhanced - baseline_processing
            }
        
        # Validate scaling properties
        enhancement_ratios = [results[f]['enhancement_ratio'] for f in enhancement_factors]
        
        # Higher enhancement factor should lead to higher enhancement ratio
        assert enhancement_ratios == sorted(enhancement_ratios), "Enhancement should scale monotonically with enhancement factor"
        
        # Check mathematical relationship
        for factor in enhancement_factors:
            expected_ratio = 1.0 + factor * attention_weight
            actual_ratio = results[factor]['enhancement_ratio']
            assert abs(expected_ratio - actual_ratio) < 1e-10, f"Enhancement ratio mismatch for factor {factor}"
        
        logger.info("✓ Processing enhancement scales correctly with enhancement factor")
        return results
    
    def test_enhancement_attention_distribution(self):
        """Test enhancement across different attention distributions."""
        logger.info("Testing enhancement with different attention distributions")
        
        attention_system = MockAttentionalAwareness(self.config)
        
        # Test different attention distributions
        attention_distributions = [
            {'uniform': [0.25, 0.25, 0.25, 0.25]},
            {'concentrated': [0.7, 0.1, 0.1, 0.1]},
            {'bimodal': [0.4, 0.1, 0.1, 0.4]},
            {'linear': [0.1, 0.2, 0.3, 0.4]}
        ]
        
        baseline_processing = 1.0
        results = {}
        
        for dist_info in attention_distributions:
            dist_name = list(dist_info.keys())[0]
            attention_weights = dist_info[dist_name]
            
            enhancements = []
            enhancement_ratios = []
            
            for i, weight in enumerate(attention_weights):
                stimulus_id = f"stimulus_{i}"
                attention_system.attention_allocations[stimulus_id] = weight
                
                enhanced = attention_system.enhance_processing(stimulus_id, baseline_processing)
                enhancements.append(enhanced)
                enhancement_ratios.append(enhanced / baseline_processing)
            
            results[dist_name] = {
                'attention_weights': attention_weights,
                'enhancements': enhancements,
                'enhancement_ratios': enhancement_ratios,
                'total_enhancement': sum(enhancements),
                'enhancement_variance': np.var(enhancement_ratios),
                'max_enhancement': max(enhancement_ratios),
                'min_enhancement': min(enhancement_ratios)
            }
        
        # Validate distribution effects
        concentrated_variance = results['concentrated']['enhancement_variance']
        uniform_variance = results['uniform']['enhancement_variance']
        
        # Concentrated attention should lead to higher variance in enhancement
        assert concentrated_variance > uniform_variance, "Concentrated attention should produce higher enhancement variance"
        
        # Check that enhancement preserves attention ordering
        for dist_name, data in results.items():
            attention_order = np.argsort(data['attention_weights'])[::-1]
            enhancement_order = np.argsort(data['enhancement_ratios'])[::-1]
            
            # Orders should match (highest attention gets highest enhancement)
            assert np.array_equal(attention_order, enhancement_order), f"Enhancement order mismatch for {dist_name} distribution"
        
        logger.info("✓ Processing enhancement works correctly across attention distributions")
        return results

class TestTemporalDynamics:
    """Tests for temporal dynamics and habituation in attention."""
    
    def __init__(self, config: AttentionTestConfig):
        self.config = config
        self.validator = AttentionMathValidator()
        self.stimulus_generator = StimulusGenerator(config)
        
    def test_attention_temporal_evolution(self):
        """Test temporal evolution of attention allocation."""
        logger.info("Testing attention temporal evolution")
        
        attention_system = MockAttentionalAwareness(self.config)
        
        # Create persistent stimulus with varying salience over time
        stimulus_id = "temporal_stimulus"
        n_timesteps = 100
        
        attention_history = []
        salience_history = []
        
        for t in range(n_timesteps):
            # Generate time-varying salience
            base_salience = 0.8
            variation = 0.3 * np.sin(t * 0.1) + 0.1 * np.random.randn()
            current_salience = np.clip(base_salience + variation, 0.1, 2.0)
            
            # Set current salience
            salience_dict = {stimulus_id: current_salience}
            allocations = attention_system.allocate_attention(salience_dict)
            
            # Update dynamics
            attention_system.update_attention_dynamics(dt=1.0)
            
            # Record history
            current_attention = attention_system.attention_allocations.get(stimulus_id, 0.0)
            attention_history.append(current_attention)
            salience_history.append(current_salience)
            
            # Add some delay to simulate real time
            time.sleep(0.001)
        
        # Validate temporal dynamics
        validation = self.validator.validate_temporal_dynamics(
            attention_history, salience_history,
            self.config.attention_decay_rate,
            self.config.habituation_time_constant
        )
        
        # Assert validation passes
        for check, passed in validation.items():
            assert passed, f"Temporal dynamics validation failed: {check}"
        
        # Analyze temporal patterns
        attention_array = np.array(attention_history)
        salience_array = np.array(salience_history)
        
        # Check for temporal correlation
        if len(attention_history) > 1:
            attention_autocorr = np.corrcoef(attention_array[:-1], attention_array[1:])[0, 1]
            assert not np.isnan(attention_autocorr), "Attention autocorrelation should be valid"
            assert attention_autocorr > 0.0, "Attention should show positive temporal correlation"
        
        # Check attention-salience relationship
        attention_salience_corr = np.corrcoef(attention_array, salience_array)[0, 1]
        assert attention_salience_corr > 0.2, "Attention should be positively correlated with salience"
        
        logger.info("✓ Attention temporal evolution follows expected dynamics")
        return {
            'attention_history': attention_history,
            'salience_history': salience_history,
            'validation': validation,
            'attention_autocorr': attention_autocorr,
            'attention_salience_corr': attention_salience_corr
        }
    
    def test_habituation_effects(self):
        """Test habituation effects on attention allocation."""
        logger.info("Testing habituation effects")
        
        attention_system = MockAttentionalAwareness(self.config)
        
        # Present constant high-salience stimulus
        stimulus_id = "habituating_stimulus"
        constant_salience = 1.5
        n_timesteps = 200
        
        attention_history = []
        habituation_history = []
        
        for t in range(n_timesteps):
            # Maintain constant salience
            salience_dict = {stimulus_id: constant_salience}
            allocations = attention_system.allocate_attention(salience_dict)
            
            # Update dynamics (this should cause habituation)
            attention_system.update_attention_dynamics(dt=1.0)
            
            # Record data
            current_attention = attention_system.attention_allocations.get(stimulus_id, 0.0)
            current_habituation = attention_system.habituation_tracker.get(stimulus_id, 0.0)
            
            attention_history.append(current_attention)
            habituation_history.append(current_habituation)
            
            time.sleep(0.001)  # Small delay
        
        # Analyze habituation pattern
        attention_array = np.array(attention_history)
        habituation_array = np.array(habituation_history)
        
        # Check for habituation effect (attention should generally decrease over time)
        if len(attention_history) >= 20:
            early_attention = np.mean(attention_array[:10])
            late_attention = np.mean(attention_array[-10:])
            
            # Due to habituation, late attention should be lower than early attention
            # (allowing for some tolerance due to simplified implementation)
            habituation_effect = early_attention - late_attention
            assert habituation_effect > -0.1, "Habituation should not increase attention significantly"
        
        # Check habituation accumulation
        assert habituation_array[-1] >= habituation_array[0], "Habituation should accumulate over time"
        
        # Validate temporal consistency
        attention_variance = np.var(attention_array)
        assert attention_variance > 1e-8, "Attention should show temporal variation due to dynamics"
        
        logger.info("✓ Habituation effects are working correctly")
        return {
            'attention_history': attention_history,
            'habituation_history': habituation_history,
            'habituation_effect': habituation_effect if len(attention_history) >= 20 else 0.0,
            'final_habituation': habituation_array[-1]
        }
    
    def test_attention_persistence(self):
        """Test attention persistence after stimulus removal."""
        logger.info("Testing attention persistence")
        
        attention_system = MockAttentionalAwareness(self.config)
        
        # Phase 1: Present stimulus with high salience
        stimulus_id = "persistent_stimulus"
        high_salience = 2.0
        presentation_duration = 50
        
        for t in range(presentation_duration):
            salience_dict = {stimulus_id: high_salience}
            attention_system.allocate_attention(salience_dict)
            attention_system.update_attention_dynamics(dt=1.0)
            time.sleep(0.001)
        
        # Record attention at end of presentation
        attention_at_removal = attention_system.attention_allocations.get(stimulus_id, 0.0)
        
        # Phase 2: Remove stimulus (no salience input) and observe decay
        decay_duration = 100
        attention_decay_history = []
        
        for t in range(decay_duration):
            # No new salience input, just dynamics
            attention_system.update_attention_dynamics(dt=1.0)
            
            current_attention = attention_system.attention_allocations.get(stimulus_id, 0.0)
            attention_decay_history.append(current_attention)
            time.sleep(0.001)
        
        # Analyze decay pattern
        decay_array = np.array(attention_decay_history)
        
        # Attention should decay over time without salience input
        initial_decay_attention = decay_array[0]
        final_decay_attention = decay_array[-1]
        
        # Should show decay (allowing for implementation details)
        decay_observed = initial_decay_attention > final_decay_attention
        
        # Calculate decay time constant (approximate)
        if decay_observed:
            # Find when attention drops to 1/e of initial value
            target_value = initial_decay_attention / np.e
            decay_indices = np.where(decay_array <= target_value)[0]
            if len(decay_indices) > 0:
                decay_time_constant = decay_indices[0]
            else:
                decay_time_constant = len(decay_array)  # Didn't reach 1/e
        else:
            decay_time_constant = float('inf')
        
        # Attention should persist for some time but eventually decay
        assert attention_at_removal > 0.0, "Attention should be present when stimulus is removed"
        
        logger.info("✓ Attention persistence and decay behavior is appropriate")
        return {
            'attention_at_removal': attention_at_removal,
            'attention_decay_history': attention_decay_history,
            'decay_observed': decay_observed,
            'decay_time_constant': decay_time_constant,
            'final_attention': final_decay_attention
        }
    
    def test_multi_stimulus_temporal_competition(self):
        """Test temporal competition between multiple stimuli."""
        logger.info("Testing multi-stimulus temporal competition")
        
        attention_system = MockAttentionalAwareness(self.config)
        
        # Create multiple stimuli with different temporal patterns
        stimuli_config = {
            'constant_stimulus': {'base_salience': 1.0, 'pattern': 'constant'},
            'increasing_stimulus': {'base_salience': 0.5, 'pattern': 'increasing'},
            'decreasing_stimulus': {'base_salience': 1.5, 'pattern': 'decreasing'},
            'oscillating_stimulus': {'base_salience': 1.0, 'pattern': 'oscillating'}
        }
        
        n_timesteps = 150
        stimulus_histories = {stimulus_id: [] for stimulus_id in stimuli_config.keys()}
        attention_histories = {stimulus_id: [] for stimulus_id in stimuli_config.keys()}
        
        for t in range(n_timesteps):
            current_saliences = {}
            
            # Generate salience for each stimulus based on its pattern
            for stimulus_id, config in stimuli_config.items():
                base = config['base_salience']
                pattern = config['pattern']
                
                if pattern == 'constant':
                    salience = base
                elif pattern == 'increasing':
                    salience = base + (t / n_timesteps) * 1.0
                elif pattern == 'decreasing':
                    salience = base - (t / n_timesteps) * 1.0
                elif pattern == 'oscillating':
                    salience = base + 0.5 * np.sin(t * 0.2)
                
                salience = np.clip(salience, 0.1, 3.0)
                current_saliences[stimulus_id] = salience
                stimulus_histories[stimulus_id].append(salience)
            
            # Allocate attention across all stimuli
            allocations = attention_system.allocate_attention(current_saliences)
            attention_system.update_attention_dynamics(dt=1.0)
            
            # Record attention allocations
            for stimulus_id in stimuli_config.keys():
                current_attention = attention_system.attention_allocations.get(stimulus_id, 0.0)
                attention_histories[stimulus_id].append(current_attention)
            
            time.sleep(0.001)
        
        # Analyze competitive dynamics
        results = {}
        
        for stimulus_id in stimuli_config.keys():
            attention_array = np.array(attention_histories[stimulus_id])
            salience_array = np.array(stimulus_histories[stimulus_id])
            
            # Calculate correlation between salience and attention
            correlation = np.corrcoef(salience_array, attention_array)[0, 1]
            
            # Calculate temporal statistics
            attention_mean = np.mean(attention_array)
            attention_std = np.std(attention_array)
            attention_trend = np.polyfit(range(len(attention_array)), attention_array, 1)[0]
            
            results[stimulus_id] = {
                'salience_attention_correlation': correlation,
                'mean_attention': attention_mean,
                'attention_std': attention_std,
                'attention_trend': attention_trend,
                'pattern': stimuli_config[stimulus_id]['pattern']
            }
        
        # Validate competitive behavior
        # Increasing stimulus should show positive trend
        increasing_trend = results['increasing_stimulus']['attention_trend']
        decreasing_trend = results['decreasing_stimulus']['attention_trend']
        
        assert increasing_trend > decreasing_trend, "Increasing stimulus should show more positive trend than decreasing"
        
        # All stimuli should show positive correlation between salience and attention
        for stimulus_id, data in results.items():
            correlation = data['salience_attention_correlation']
            assert not np.isnan(correlation), f"Correlation should be valid for {stimulus_id}"
            assert correlation > 0.0, f"Salience-attention correlation should be positive for {stimulus_id}"
        
        logger.info("✓ Multi-stimulus temporal competition works correctly")
        return {
            'results': results,
            'attention_histories': attention_histories,
            'stimulus_histories': stimulus_histories
        }

# ============================================================================
# Performance and Benchmarking Tests
# ============================================================================

class TestAttentionPerformance:
    """Performance and benchmarking tests for attention mechanisms."""
    
    def __init__(self, config: AttentionTestConfig):
        self.config = config
        self.stimulus_generator = StimulusGenerator(config)
        
    def test_salience_computation_performance(self):
        """Benchmark salience computation performance."""
        logger.info("Benchmarking salience computation performance")
        
        attention_system = MockAttentionalAwareness(self.config)
        
        # Generate test data
        n_stimuli = 1000
        stimuli = self.stimulus_generator.generate_stimulus_batch(n_stimuli, 'mixed')
        goals = self.stimulus_generator.generate_goal_features(5)
        
        # Benchmark salience computation
        start_time = time.time()
        
        for i, stimulus in enumerate(stimuli):
            stimulus_id = f"benchmark_stimulus_{i}"
            attention_system.calculate_salience(stimulus_id, stimulus, goals)
        
        total_time = time.time() - start_time
        computations_per_second = n_stimuli / total_time
        
        # Performance assertions
        assert computations_per_second >= self.config.min_salience_computations_per_second, \
            f"Salience computation too slow: {computations_per_second:.1f} < {self.config.min_salience_computations_per_second}"
        
        logger.info(f"✓ Salience computation performance: {computations_per_second:.1f} computations/sec")
        
        return {
            'n_stimuli': n_stimuli,
            'total_time': total_time,
            'computations_per_second': computations_per_second
        }
    
    def test_attention_allocation_performance(self):
        """Benchmark attention allocation performance."""
        logger.info("Benchmarking attention allocation performance")
        
        attention_system = MockAttentionalAwareness(self.config)
        
        # Test with different numbers of stimuli
        stimuli_counts = [10, 50, 100, 500, 1000]
        results = {}
        
        for n_stimuli in stimuli_counts:
            # Generate salience data
            saliences = {}
            for i in range(n_stimuli):
                saliences[f"stimulus_{i}"] = np.random.exponential(1.0)
            
            # Benchmark allocation
            start_time = time.time()
            
            for _ in range(self.config.n_benchmark_iterations):
                attention_system.allocate_attention(saliences)
            
            total_time = time.time() - start_time
            allocations_per_second = self.config.n_benchmark_iterations / total_time
            
            results[n_stimuli] = {
                'n_stimuli': n_stimuli,
                'allocations_per_second': allocations_per_second,
                'time_per_allocation': total_time / self.config.n_benchmark_iterations
            }
        
        # Check performance requirement for reasonable stimulus count
        allocation_rate_100 = results[100]['allocations_per_second']
        assert allocation_rate_100 >= self.config.min_attention_allocations_per_second, \
            f"Attention allocation too slow: {allocation_rate_100:.1f} < {self.config.min_attention_allocations_per_second}"
        
        # Check scaling properties
        smallest_rate = results[stimuli_counts[0]]['allocations_per_second']
        largest_rate = results[stimuli_counts[-1]]['allocations_per_second']
        
        # Performance shouldn't degrade too much with more stimuli
        degradation_ratio = largest_rate / smallest_rate
        assert degradation_ratio > 0.1, f"Performance degrades too much with scale: {degradation_ratio:.3f}"
        
        logger.info("✓ Attention allocation performance meets requirements")
        return results
    
    def test_memory_usage(self):
        """Test memory usage of attention system."""
        logger.info("Testing memory usage")
        
        # Get initial memory usage
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        attention_system = MockAttentionalAwareness(self.config)
        
        # Create substantial amount of data
        n_stimuli = 5000
        stimuli = self.stimulus_generator.generate_stimulus_batch(n_stimuli, 'mixed')
        goals = self.stimulus_generator.generate_goal_features(10)
        
        # Process all stimuli
        for i, stimulus in enumerate(stimuli):
            stimulus_id = f"memory_test_stimulus_{i}"
            salience = attention_system.calculate_salience(stimulus_id, stimulus, goals)
            
            if i % 100 == 0:  # Periodic attention allocation
                current_saliences = {f"stimulus_{j}": np.random.exponential(1.0) for j in range(min(i+1, 50))}
                attention_system.allocate_attention(current_saliences)
                attention_system.update_attention_dynamics()
        
        # Check final memory usage
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory
        
        # Memory usage should be reasonable
        assert memory_increase < self.config.max_memory_usage_mb, \
            f"Memory usage too high: {memory_increase:.1f}MB > {self.config.max_memory_usage_mb}MB"
        
        logger.info(f"✓ Memory usage within bounds: {memory_increase:.1f}MB increase")
        
        return {
            'initial_memory_mb': initial_memory,
            'final_memory_mb': final_memory,
            'memory_increase_mb': memory_increase,
            'n_stimuli_processed': n_stimuli
        }
    
    def test_concurrent_performance(self):
        """Test performance under concurrent access."""
        logger.info("Testing concurrent performance")
        
        attention_system = MockAttentionalAwareness(self.config)
        
        # Shared data for concurrent access
        stimuli = self.stimulus_generator.generate_stimulus_batch(100, 'mixed')
        goals = self.stimulus_generator.generate_goal_features(3)
        
        results = []
        errors = []
        
        def worker_task(worker_id: int, n_operations: int):
            """Worker function for concurrent testing."""
            worker_results = []
            try:
                for i in range(n_operations):
                    stimulus_idx = (worker_id * n_operations + i) % len(stimuli)
                    stimulus = stimuli[stimulus_idx]
                    stimulus_id = f"worker_{worker_id}_stimulus_{i}"
                    
                    # Compute salience
                    start_time = time.time()
                    salience = attention_system.calculate_salience(stimulus_id, stimulus, goals)
                    computation_time = time.time() - start_time
                    
                    worker_results.append({
                        'worker_id': worker_id,
                        'operation': i,
                        'salience': salience,
                        'computation_time': computation_time
                    })
                
                return worker_results
            except Exception as e:
                errors.append(f"Worker {worker_id} error: {str(e)}")
                return []
        
        # Run concurrent workers
        n_workers = 4
        n_operations_per_worker = 50
        
        with ThreadPoolExecutor(max_workers=n_workers) as executor:
            futures = [
                executor.submit(worker_task, worker_id, n_operations_per_worker)
                for worker_id in range(n_workers)
            ]
            
            for future in futures:
                worker_results = future.result()
                results.extend(worker_results)
        
        # Analyze concurrent performance
        assert len(errors) == 0, f"Concurrent access errors: {errors}"
        assert len(results) == n_workers * n_operations_per_worker, "Missing results from concurrent execution"
        
        # Check performance consistency
        computation_times = [r['computation_time'] for r in results]
        mean_time = np.mean(computation_times)
        std_time = np.std(computation_times)
        
        # Performance shouldn't vary too much between workers
        coefficient_of_variation = std_time / mean_time if mean_time > 0 else 0
        assert coefficient_of_variation < 2.0, f"High performance variation under concurrency: {coefficient_of_variation:.3f}"
        
        logger.info("✓ Concurrent performance is acceptable")
        
        return {
            'n_workers': n_workers,
            'n_operations_per_worker': n_operations_per_worker,
            'total_operations': len(results),
            'mean_computation_time': mean_time,
            'std_computation_time': std_time,
            'coefficient_of_variation': coefficient_of_variation,
            'errors': errors
        }

# ============================================================================
# Integration Tests
# ============================================================================

class TestAttentionIntegration:
    """Integration tests for attention system with other components."""
    
    def __init__(self, config: AttentionTestConfig):
        self.config = config
        self.stimulus_generator = StimulusGenerator(config)
        
    def test_goal_attention_integration(self):
        """Test integration between goal system and attention allocation."""
        logger.info("Testing goal-attention integration")
        
        attention_system = MockAttentionalAwareness(self.config)
        
        # Create goal-relevant and goal-irrelevant stimuli
        goals = self.stimulus_generator.generate_goal_features(3)
        
        # Simulate goal-relevant stimulus (high relevance features)
        goal_relevant_stimulus = np.random.randn(self.config.stimulus_feature_dim) * 0.5
        goal_relevant_stimulus[:10] = goals[0]['features'][:10]  # Share features with top goal
        
        # Simulate goal-irrelevant stimulus
        goal_irrelevant_stimulus = np.random.randn(self.config.stimulus_feature_dim) * 0.5
        
        # Compute saliences
        relevant_salience = attention_system.calculate_salience("relevant_stimulus", goal_relevant_stimulus, goals)
        irrelevant_salience = attention_system.calculate_salience("irrelevant_stimulus", goal_irrelevant_stimulus, goals)
        
        # Test with no goals
        no_goal_relevant_salience = attention_system.calculate_salience("relevant_no_goal", goal_relevant_stimulus, [])
        no_goal_irrelevant_salience = attention_system.calculate_salience("irrelevant_no_goal", goal_irrelevant_stimulus, [])
        
        # Analyze integration effects
        results = {
            'relevant_salience_with_goals': relevant_salience,
            'irrelevant_salience_with_goals': irrelevant_salience,
            'relevant_salience_no_goals': no_goal_relevant_salience,
            'irrelevant_salience_no_goals': no_goal_irrelevant_salience
        }
        
        # Test attention allocation with mixed stimuli
        mixed_saliences = {
            'goal_relevant': relevant_salience,
            'goal_irrelevant': irrelevant_salience,
            'neutral_1': np.random.uniform(0.5, 1.0),
            'neutral_2': np.random.uniform(0.3, 0.8)
        }
        
        allocations = attention_system.allocate_attention(mixed_saliences)
        
        # Validate goal influence
        # Goal-relevant stimulus should generally get more attention when goals are present
        # (though this depends on the mock implementation details)
        
        # Check that goal presence affects salience computation
        goal_effect_relevant = relevant_salience - no_goal_relevant_salience
        goal_effect_irrelevant = irrelevant_salience - no_goal_irrelevant_salience
        
        # At least some goal effect should be observable
        total_goal_effect = abs(goal_effect_relevant) + abs(goal_effect_irrelevant)
        assert total_goal_effect > 1e-6, "Goals should influence salience computation"
        
        results['goal_effect_relevant'] = goal_effect_relevant
        results['goal_effect_irrelevant'] = goal_effect_irrelevant
        results['allocations'] = allocations
        
        logger.info("✓ Goal-attention integration working correctly")
        return results
    
    def test_attention_persistence_integration(self):
        """Test integration of attention persistence with stimulus dynamics."""
        logger.info("Testing attention persistence integration")
        
        attention_system = MockAttentionalAwareness(self.config)
        
        # Simulate scenario: sudden appearance and disappearance of stimuli
        n_timesteps = 100
        
        # Phase 1: No stimuli (timesteps 0-19)
        # Phase 2: Single high-salience stimulus (timesteps 20-39) 
        # Phase 3: Multiple competing stimuli (timesteps 40-69)
        # Phase 4: Return to single stimulus (timesteps 70-89)
        # Phase 5: No stimuli again (timesteps 90-99)
        
        attention_timeline = []
        stimulus_timeline = []
        
        for t in range(n_timesteps):
            current_stimuli = {}
            
            if 20 <= t < 40:
                # Single high-salience stimulus
                current_stimuli['primary_stimulus'] = 2.0
            elif 40 <= t < 70:
                # Multiple competing stimuli
                current_stimuli['primary_stimulus'] = 1.8
                current_stimuli['competitor_1'] = 1.5
                current_stimuli['competitor_2'] = 1.2
                current_stimuli['weak_stimulus'] = 0.5
            elif 70 <= t < 90:
                # Return to single stimulus (weaker)
                current_stimuli['primary_stimulus'] = 1.0
            
            # Allocate attention and update dynamics
            if current_stimuli:
                allocations = attention_system.allocate_attention(current_stimuli)
            else:
                allocations = {}
            
            attention_system.update_attention_dynamics(dt=1.0)
            
            # Record state
            stimulus_timeline.append(current_stimuli.copy())
            attention_state = {}
            for stimulus_id in ['primary_stimulus', 'competitor_1', 'competitor_2', 'weak_stimulus']:
                attention_state[stimulus_id] = attention_system.attention_allocations.get(stimulus_id, 0.0)
            attention_timeline.append(attention_state)
            
            time.sleep(0.001)
        
        # Analyze integration behavior
        primary_attention = [state['primary_stimulus'] for state in attention_timeline]
        
        # Check attention buildup during single stimulus phase
        buildup_phase = primary_attention[20:40]
        assert max(buildup_phase) > 0.5, "Attention should build up during stimulus presentation"
        
        # Check attention competition during multi-stimulus phase
        competition_phase_primary = primary_attention[40:70]
        competition_phase_competitor1 = [state['competitor_1'] for state in attention_timeline[40:70]]
        
        # Primary should still get substantial attention but not all
        mean_primary_during_competition = np.mean(competition_phase_primary)
        mean_competitor1_during_competition = np.mean(competition_phase_competitor1)
        
        assert mean_primary_during_competition > 0.0, "Primary stimulus should maintain some attention during competition"
        assert mean_competitor1_during_competition > 0.0, "Competitor should gain some attention"
        
        # Check attention persistence after stimulus removal
        post_removal_phase = primary_attention[90:]
        initial_post_removal = post_removal_phase[0] if post_removal_phase else 0.0
        
        # Some attention might persist initially after removal
        results = {
            'attention_timeline': attention_timeline,
            'stimulus_timeline': stimulus_timeline,
            'primary_attention': primary_attention,
            'max_buildup_attention': max(buildup_phase),
            'mean_attention_during_competition': mean_primary_during_competition,
            'attention_after_removal': initial_post_removal
        }
        
        logger.info("✓ Attention persistence integration working correctly")
        return results
    
    def test_multi_modal_attention_integration(self):
        """Test attention integration across multiple modalities."""
        logger.info("Testing multi-modal attention integration")
        
        attention_system = MockAttentionalAwareness(self.config)
        
        # Simulate different modalities with different feature patterns
        modalities = {
            'visual': {'n_features': self.config.stimulus_feature_dim, 'noise_level': 0.1},
            'auditory': {'n_features': self.config.stimulus_feature_dim, 'noise_level': 0.2},
            'textual': {'n_features': self.config.stimulus_feature_dim, 'noise_level': 0.15}
        }
        
        # Generate modality-specific stimuli
        multi_modal_stimuli = {}
        for modality, config in modalities.items():
            for i in range(3):  # 3 stimuli per modality
                stimulus_id = f"{modality}_stimulus_{i}"
                
                # Generate modality-specific features
                if modality == 'visual':
                    # Visual: more spatial structure
                    stimulus = np.random.randn(config['n_features']) * 0.5
                    stimulus[::4] *= 2  # Enhance every 4th feature (spatial structure)
                elif modality == 'auditory':
                    # Auditory: more temporal structure
                    stimulus = np.random.randn(config['n_features']) * 0.5
                    for j in range(1, len(stimulus)):
                        stimulus[j] = 0.7 * stimulus[j-1] + 0.3 * stimulus[j]  # Temporal correlation
                else:  # textual
                    # Textual: more sparse, discrete features
                    stimulus = np.zeros(config['n_features'])
                    active_features = np.random.choice(config['n_features'], config['n_features']//4, replace=False)
                    stimulus[active_features] = np.random.randn(len(active_features)) * 2
                
                # Add modality-specific noise
                stimulus += np.random.randn(config['n_features']) * config['noise_level']
                multi_modal_stimuli[stimulus_id] = stimulus
        
        # Create goals that might favor different modalities
        goals = [
            {'priority': 0.8, 'type': 'visual_processing', 'features': np.random.randn(self.config.goal_feature_dim)},
            {'priority': 0.6, 'type': 'language_understanding', 'features': np.random.randn(self.config.goal_feature_dim)},
            {'priority': 0.4, 'type': 'auditory_analysis', 'features': np.random.randn(self.config.goal_feature_dim)}
        ]
        
        # Compute saliences for all stimuli
        salience_results = {}
        for stimulus_id, stimulus in multi_modal_stimuli.items():
            salience = attention_system.calculate_salience(stimulus_id, stimulus, goals)
            salience_results[stimulus_id] = salience
        
        # Allocate attention across all modalities
        allocations = attention_system.allocate_attention(salience_results)
        
        # Analyze multi-modal integration
        modality_stats = {}
        for modality in modalities.keys():
            modality_stimuli = [sid for sid in salience_results.keys() if sid.startswith(modality)]
            modality_saliences = [salience_results[sid] for sid in modality_stimuli]
            modality_attentions = [allocations[sid] for sid in modality_stimuli]
            
            modality_stats[modality] = {
                'mean_salience': np.mean(modality_saliences),
                'std_salience': np.std(modality_saliences),
                'total_attention': sum(modality_attentions),
                'max_attention': max(modality_attentions),
                'stimuli_count': len(modality_stimuli)
            }
        
        # Validate multi-modal behavior
        total_attention_allocated = sum(allocations.values())
        assert abs(total_attention_allocated - 1.0) < 1e-6, "Total attention should sum to 1.0"
        
        # Each modality should get some attention
        for modality, stats in modality_stats.items():
            assert stats['total_attention'] > 0.0, f"Modality {modality} should receive some attention"
        
        # Test modality switching
        # Focus on one modality by changing goals
        focused_goals = [{'priority': 0.9, 'type': 'visual_processing', 'features': np.random.randn(self.config.goal_feature_dim)}]
        
        focused_saliences = {}
        for stimulus_id, stimulus in multi_modal_stimuli.items():
            focused_salience = attention_system.calculate_salience(f"focused_{stimulus_id}", stimulus, focused_goals)
            focused_saliences[f"focused_{stimulus_id}"] = focused_salience
        
        focused_allocations = attention_system.allocate_attention(focused_saliences)
        
        # Analyze focus effect
        visual_focused_attention = sum(
            focused_allocations[sid] for sid in focused_allocations.keys() 
            if 'visual' in sid
        )
        non_visual_focused_attention = sum(
            focused_allocations[sid] for sid in focused_allocations.keys() 
            if 'visual' not in sid
        )
        
        results = {
            'modality_stats': modality_stats,
            'multi_modal_allocations': allocations,
            'focused_allocations': focused_allocations,
            'visual_focus_ratio': visual_focused_attention / (visual_focused_attention + non_visual_focused_attention) if (visual_focused_attention + non_visual_focused_attention) > 0 else 0
        }
        
        logger.info("✓ Multi-modal attention integration working correctly")
        return results

# ============================================================================
# Visualization and Analysis Tools
# ============================================================================

class AttentionVisualizer:
    """Visualization tools for attention analysis."""
    
    def __init__(self, config: AttentionTestConfig):
        self.config = config
        plt.style.use('default')
        
    def plot_salience_components(self, salience_data: Dict[str, Dict[str, float]], 
                                save_path: Optional[str] = None):
        """Plot breakdown of salience components."""
        fig, axes = plt.subplots(2, 2, figsize=self.config.figure_size)
        
        # Extract component data
        stimuli = list(salience_data.keys())
        novelty = [salience_data[s]['novelty'] for s in stimuli]
        relevance = [salience_data[s]['relevance'] for s in stimuli]
        uncertainty = [salience_data[s]['uncertainty'] for s in stimuli]
        gain = [salience_data[s]['gain'] for s in stimuli]
        total = [salience_data[s]['total'] for s in stimuli]
        
        # Component distributions
        axes[0, 0].hist(novelty, bins=20, alpha=0.7, color='blue', label='Novelty')
        axes[0, 0].set_title('Novelty Distribution')
        axes[0, 0].set_xlabel('Novelty Score')
        axes[0, 0].set_ylabel('Frequency')
        
        axes[0, 1].hist(relevance, bins=20, alpha=0.7, color='green', label='Relevance')
        axes[0, 1].set_title('Relevance Distribution')
        axes[0, 1].set_xlabel('Relevance Score')
        axes[0, 1].set_ylabel('Frequency')
        
        axes[1, 0].hist(uncertainty, bins=20, alpha=0.7, color='orange', label='Uncertainty')
        axes[1, 0].set_title('Uncertainty Distribution')
        axes[1, 0].set_xlabel('Uncertainty Score')
        axes[1, 0].set_ylabel('Frequency')
        
        axes[1, 1].hist(gain, bins=20, alpha=0.7, color='red', label='Gain')
        axes[1, 1].set_title('Gain Distribution')
        axes[1, 1].set_xlabel('Gain Score')
        axes[1, 1].set_ylabel('Frequency')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=self.config.plot_dpi, bbox_inches='tight')
        else:
            plt.show()
        
        # Component correlation analysis
        fig, ax = plt.subplots(figsize=(8, 6))
        
        components = np.array([novelty, relevance, uncertainty, gain]).T
        component_names = ['Novelty', 'Relevance', 'Uncertainty', 'Gain']
        
        correlation_matrix = np.corrcoef(components.T)
        
        im = ax.imshow(correlation_matrix, cmap='coolwarm', vmin=-1, vmax=1)
        ax.set_xticks(range(len(component_names)))
        ax.set_yticks(range(len(component_names)))
        ax.set_xticklabels(component_names)
        ax.set_yticklabels(component_names)
        
        # Add correlation values
        for i in range(len(component_names)):
            for j in range(len(component_names)):
                text = ax.text(j, i, f'{correlation_matrix[i, j]:.2f}',
                             ha="center", va="center", color="black", fontweight='bold')
        
        ax.set_title('Salience Component Correlations')
        plt.colorbar(im, ax=ax, label='Correlation Coefficient')
        plt.tight_layout()
        
        if save_path:
            correlation_path = save_path.replace('.png', '_correlations.png')
            plt.savefig(correlation_path, dpi=self.config.plot_dpi, bbox_inches='tight')
        else:
            plt.show()
    
    def plot_attention_dynamics(self, attention_history: Dict[str, List[float]], 
                               salience_history: Optional[Dict[str, List[float]]] = None,
                               save_path: Optional[str] = None):
        """Plot temporal dynamics of attention allocation."""
        fig, axes = plt.subplots(3, 1, figsize=(self.config.figure_size[0], self.config.figure_size[1] + 2))
        
        # Plot individual attention trajectories
        for stimulus_id, history in attention_history.items():
            timesteps = range(len(history))
            axes[0].plot(timesteps, history, label=stimulus_id, linewidth=2, alpha=0.8)
        
        axes[0].set_title('Attention Allocation Over Time')
        axes[0].set_xlabel('Time Step')
        axes[0].set_ylabel('Attention Weight')
        axes[0].legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        axes[0].grid(True, alpha=0.3)
        
        # Plot attention entropy over time
        if len(attention_history) > 1:
            entropy_history = []
            for t in range(max(len(h) for h in attention_history.values())):
                attention_at_t = []
                for history in attention_history.values():
                    if t < len(history):
                        attention_at_t.append(history[t])
                
                if len(attention_at_t) > 1:
                    entropy_t = entropy(attention_at_t)
                    entropy_history.append(entropy_t)
                else:
                    entropy_history.append(0.0)
            
            axes[1].plot(range(len(entropy_history)), entropy_history, 
                        color='purple', linewidth=2, label='Attention Entropy')
            axes[1].set_title('Attention Entropy Dynamics')
            axes[1].set_xlabel('Time Step')
            axes[1].set_ylabel('Entropy (bits)')
            axes[1].grid(True, alpha=0.3)
        
        # Plot salience vs attention correlation if salience history provided
        if salience_history:
            correlations = []
            for stimulus_id in attention_history.keys():
                if stimulus_id in salience_history:
                    att_hist = attention_history[stimulus_id]
                    sal_hist = salience_history[stimulus_id]
                    
                    # Align lengths
                    min_len = min(len(att_hist), len(sal_hist))
                    if min_len > 1:
                        corr = np.corrcoef(att_hist[:min_len], sal_hist[:min_len])[0, 1]
                        if not np.isnan(corr):
                            correlations.append(corr)
            
            if correlations:
                axes[2].bar(range(len(correlations)), correlations, alpha=0.7, color='orange')
                axes[2].set_title('Salience-Attention Correlations')
                axes[2].set_xlabel('Stimulus Index')
                axes[2].set_ylabel('Correlation Coefficient')
                axes[2].axhline(y=0, color='black', linestyle='--', alpha=0.5)
                axes[2].grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=self.config.plot_dpi, bbox_inches='tight')
        else:
            plt.show()
    
    def plot_attention_heatmap(self, attention_matrix: np.ndarray, 
                              stimulus_labels: List[str], 
                              time_labels: Optional[List[str]] = None,
                              save_path: Optional[str] = None):
        """Plot attention allocation as a heatmap over time."""
        fig, ax = plt.subplots(figsize=self.config.figure_size)
        
        # Create heatmap
        im = ax.imshow(attention_matrix, cmap=self.config.color_palette, aspect='auto')
        
        # Set labels
        ax.set_yticks(range(len(stimulus_labels)))
        ax.set_yticklabels(stimulus_labels)
        ax.set_ylabel('Stimuli')
        
        if time_labels:
            ax.set_xticks(range(0, len(time_labels), max(1, len(time_labels)//10)))
            ax.set_xticklabels([time_labels[i] for i in range(0, len(time_labels), max(1, len(time_labels)//10))])
        
        ax.set_xlabel('Time Steps')
        ax.set_title('Attention Allocation Heatmap')
        
        # Add colorbar
        cbar = plt.colorbar(im, ax=ax)
        cbar.set_label('Attention Weight')
        
        # Add text annotations for significant values
        threshold = np.max(attention_matrix) * 0.7
        for i in range(attention_matrix.shape[0]):
            for j in range(0, attention_matrix.shape[1], max(1, attention_matrix.shape[1]//20)):
                if attention_matrix[i, j] > threshold:
                    text = ax.text(j, i, f'{attention_matrix[i, j]:.2f}',
                                 ha="center", va="center", color="white", fontweight='bold')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=self.config.plot_dpi, bbox_inches='tight')
        else:
            plt.show()
    
    def plot_performance_metrics(self, performance_data: Dict[str, Any],
                               save_path: Optional[str] = None):
        """Plot comprehensive performance metrics."""
        fig, axes = plt.subplots(2, 3, figsize=(15, 10))
        
        # Performance over stimulus count
        if 'scaling_results' in performance_data:
            scaling_data = performance_data['scaling_results']
            stimulus_counts = list(scaling_data.keys())
            computation_rates = [scaling_data[n]['computations_per_second'] for n in stimulus_counts]
            
            axes[0, 0].plot(stimulus_counts, computation_rates, 'o-', linewidth=2, markersize=6)
            axes[0, 0].set_title('Computation Rate vs Stimulus Count')
            axes[0, 0].set_xlabel('Number of Stimuli')
            axes[0, 0].set_ylabel('Computations/Second')
            axes[0, 0].grid(True, alpha=0.3)
        
        # Memory usage
        if 'memory_usage' in performance_data:
            memory_data = performance_data['memory_usage']
            axes[0, 1].bar(['Initial', 'Final'], [memory_data['initial_memory_mb'], memory_data['final_memory_mb']], 
                          color=['blue', 'red'], alpha=0.7)
            axes[0, 1].set_title('Memory Usage')
            axes[0, 1].set_ylabel('Memory (MB)')
            axes[0, 1].grid(True, alpha=0.3)
        
        # Concurrent performance
        if 'concurrent_performance' in performance_data:
            concurrent_data = performance_data['concurrent_performance']
            computation_times = [r['computation_time'] for r in concurrent_data['results']]
            worker_ids = [r['worker_id'] for r in concurrent_data['results']]
            
            # Box plot by worker
            worker_times = {}
            for wid, time in zip(worker_ids, computation_times):
                if wid not in worker_times:
                    worker_times[wid] = []
                worker_times[wid].append(time)
            
            axes[0, 2].boxplot([worker_times[wid] for wid in sorted(worker_times.keys())], 
                              labels=[f'W{wid}' for wid in sorted(worker_times.keys())])
            axes[0, 2].set_title('Concurrent Performance Distribution')
            axes[0, 2].set_xlabel('Worker ID')
            axes[0, 2].set_ylabel('Computation Time (s)')
            axes[0, 2].grid(True, alpha=0.3)
        
        # Temperature effects
        if 'temperature_effects' in performance_data:
            temp_data = performance_data['temperature_effects']
            temperatures = list(temp_data.keys())
            entropies = [temp_data[t]['entropy'] for t in temperatures]
            concentrations = [temp_data[t]['concentration_ratio'] for t in temperatures]
            
            axes[1, 0].plot(temperatures, entropies, 'o-', label='Entropy', linewidth=2)
            axes[1, 0].set_title('Temperature Effects')
            axes[1, 0].set_xlabel('Temperature')
            axes[1, 0].set_ylabel('Attention Entropy')
            axes[1, 0].grid(True, alpha=0.3)
            
            ax2 = axes[1, 0].twinx()
            ax2.plot(temperatures, concentrations, 's-', color='red', label='Concentration', linewidth=2)
            ax2.set_ylabel('Concentration Ratio')
            
            # Combine legends
            lines1, labels1 = axes[1, 0].get_legend_handles_labels()
            lines2, labels2 = ax2.get_legend_handles_labels()
            axes[1, 0].legend(lines1 + lines2, labels1 + labels2, loc='center right')
        
        # Attention metrics distribution
        if 'attention_metrics' in performance_data:
            metrics_data = performance_data['attention_metrics']
            metric_names = list(metrics_data.keys())
            metric_values = list(metrics_data.values())
            
            axes[1, 1].bar(metric_names, metric_values, color=plt.cm.viridis(np.linspace(0, 1, len(metric_names))))
            axes[1, 1].set_title('Attention Quality Metrics')
            axes[1, 1].set_ylabel('Metric Value')
            axes[1, 1].tick_params(axis='x', rotation=45)
            axes[1, 1].grid(True, alpha=0.3)
        
        # Validation results
        if 'validation_results' in performance_data:
            validation_data = performance_data['validation_results']
            validation_names = list(validation_data.keys())
            validation_scores = [1.0 if validation_data[name] else 0.0 for name in validation_names]
            
            colors = ['green' if score == 1.0 else 'red' for score in validation_scores]
            axes[1, 2].bar(validation_names, validation_scores, color=colors, alpha=0.7)
            axes[1, 2].set_title('Validation Results')
            axes[1, 2].set_ylabel('Pass (1) / Fail (0)')
            axes[1, 2].set_ylim(0, 1.2)
            axes[1, 2].tick_params(axis='x', rotation=45)
            axes[1, 2].grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=self.config.plot_dpi, bbox_inches='tight')
        else:
            plt.show()

# ============================================================================
# Data Management and Persistence
# ============================================================================

class AttentionDataManager:
    """Data management for attention test results and configurations."""
    
    def __init__(self, base_path: str = "./attention_test_data"):
        self.base_path = Path(base_path)
        self.base_path.mkdir(parents=True, exist_ok=True)
        
    def save_test_results(self, results: Dict[str, Any], 
                         test_name: str, 
                         timestamp: Optional[str] = None) -> str:
        """Save test results to file."""
        if timestamp is None:
            timestamp = time.strftime("%Y%m%d_%H%M%S")
        
        filename = f"{test_name}_{timestamp}.json"
        filepath = self.base_path / filename
        
        # Convert numpy arrays to lists for JSON serialization
        serializable_results = self._make_serializable(results)
        
        with open(filepath, 'w') as f:
            json.dump(serializable_results, f, indent=2, default=str)
        
        logger.info(f"Test results saved to: {filepath}")
        return str(filepath)
    
    def load_test_results(self, filepath: str) -> Dict[str, Any]:
        """Load test results from file."""
        with open(filepath, 'r') as f:
            results = json.load(f)
        
        logger.info(f"Test results loaded from: {filepath}")
        return results
    
    def save_attention_state(self, attention_system: MockAttentionalAwareness,
                           filename: str) -> str:
        """Save complete attention system state."""
        filepath = self.base_path / f"{filename}.pkl"
        
        state_data = {
            'attention_allocations': attention_system.attention_allocations,
            'salience_scores': attention_system.salience_scores,
            'processing_enhancements': attention_system.processing_enhancements,
            'attention_history': dict(attention_system.attention_history),
            'habituation_tracker': dict(attention_system.habituation_tracker),
            'computation_calls': dict(attention_system.computation_calls),
            'timestamp': time.time()
        }
        
        with open(filepath, 'wb') as f:
            pickle.dump(state_data, f)
        
        logger.info(f"Attention state saved to: {filepath}")
        return str(filepath)
    
    def load_attention_state(self, filepath: str) -> Dict[str, Any]:
        """Load attention system state."""
        with open(filepath, 'rb') as f:
            state_data = pickle.load(f)
        
        logger.info(f"Attention state loaded from: {filepath}")
        return state_data
    
    def _make_serializable(self, obj: Any) -> Any:
        """Convert object to JSON-serializable format."""
        if isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, dict):
            return {key: self._make_serializable(value) for key, value in obj.items()}
        elif isinstance(obj, (list, tuple)):
            return [self._make_serializable(item) for item in obj]
        elif isinstance(obj, (set, frozenset)):
            return list(obj)
        elif hasattr(obj, '__dict__'):
            return self._make_serializable(obj.__dict__)
        else:
            return obj
    
    def export_test_report(self, test_results: Dict[str, Any], 
                          report_name: str) -> str:
        """Export comprehensive test report."""
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        report_filename = f"{report_name}_report_{timestamp}.md"
        report_path = self.base_path / report_filename
        
        with open(report_path, 'w') as f:
            f.write(f"# ULTRA Attentional Awareness Test Report\n\n")
            f.write(f"**Generated:** {time.strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            # Test Summary
            f.write("## Test Summary\n\n")
            for test_name, results in test_results.items():
                f.write(f"### {test_name}\n\n")
                
                if isinstance(results, dict):
                    for key, value in results.items():
                        if isinstance(value, (int, float)):
                            f.write(f"- **{key}:** {value:.4f}\n")
                        elif isinstance(value, str):
                            f.write(f"- **{key}:** {value}\n")
                        elif isinstance(value, (list, tuple)) and len(value) < 10:
                            f.write(f"- **{key}:** {value}\n")
                
                f.write("\n")
            
            # Performance Metrics
            f.write("## Performance Metrics\n\n")
            if 'performance_tests' in test_results:
                perf_data = test_results['performance_tests']
                f.write("| Metric | Value | Unit |\n")
                f.write("|--------|-------|------|\n")
                
                for metric, value in perf_data.items():
                    if isinstance(value, (int, float)):
                        f.write(f"| {metric} | {value:.4f} | - |\n")
            
            f.write("\n")
            
            # Validation Results
            f.write("## Validation Results\n\n")
            validation_summary = self._extract_validation_summary(test_results)
            
            f.write("| Test Category | Passed | Failed | Success Rate |\n")
            f.write("|---------------|--------|--------|--------------|\n")
            
            for category, stats in validation_summary.items():
                success_rate = stats['passed'] / (stats['passed'] + stats['failed']) if (stats['passed'] + stats['failed']) > 0 else 0
                f.write(f"| {category} | {stats['passed']} | {stats['failed']} | {success_rate:.2%} |\n")
        
        logger.info(f"Test report exported to: {report_path}")
        return str(report_path)
    
    def _extract_validation_summary(self, test_results: Dict[str, Any]) -> Dict[str, Dict[str, int]]:
        """Extract validation summary from test results."""
        summary = defaultdict(lambda: {'passed': 0, 'failed': 0})
        
        def count_validations(obj, prefix=""):
            if isinstance(obj, dict):
                for key, value in obj.items():
                    if key == 'validation' and isinstance(value, dict):
                        for val_key, val_result in value.items():
                            category = prefix or 'general'
                            if val_result:
                                summary[category]['passed'] += 1
                            else:
                                summary[category]['failed'] += 1
                    elif isinstance(value, dict):
                        count_validations(value, key)
                    elif isinstance(value, list):
                        for item in value:
                            if isinstance(item, dict):
                                count_validations(item, key)
        
        count_validations(test_results)
        return dict(summary)

# ============================================================================
# Comprehensive Test Suite Runner
# ============================================================================

class AttentionalAwarenessTestSuite:
    """Comprehensive test suite for attentional awareness components."""
    
    def __init__(self, config: Optional[AttentionTestConfig] = None):
        self.config = config or AttentionTestConfig()
        self.data_manager = AttentionDataManager()
        self.visualizer = AttentionVisualizer(self.config)
        
        # Initialize test classes
        self.salience_tests = TestSalienceCalculation(self.config)
        self.allocation_tests = TestAttentionAllocation(self.config)
        self.enhancement_tests = TestProcessingEnhancement(self.config)
        self.temporal_tests = TestTemporalDynamics(self.config)
        self.performance_tests = TestAttentionPerformance(self.config)
        self.integration_tests = TestAttentionIntegration(self.config)
        
        # Test results storage
        self.results = {}
        self.start_time = None
        self.end_time = None
        
    def run_all_tests(self, save_results: bool = True, 
                     generate_visualizations: bool = True) -> Dict[str, Any]:
        """Run complete test suite."""
        logger.info("="*80)
        logger.info("ULTRA ATTENTIONAL AWARENESS COMPREHENSIVE TEST SUITE")
        logger.info("="*80)
        
        self.start_time = time.time()
        
        try:
            # 1. Salience Calculation Tests
            logger.info("\n" + "="*60)
            logger.info("1. SALIENCE CALCULATION TESTS")
            logger.info("="*60)
            
            self.results['salience_tests'] = {
                'component_ranges': self.salience_tests.test_salience_component_ranges(),
                'mathematical_consistency': self.salience_tests.test_salience_mathematical_consistency(),
                'goal_sensitivity': self.salience_tests.test_salience_goal_sensitivity(),
                'stimulus_diversity': self.salience_tests.test_salience_stimulus_diversity()
            }
            
            # 2. Attention Allocation Tests
            logger.info("\n" + "="*60)
            logger.info("2. ATTENTION ALLOCATION TESTS")
            logger.info("="*60)
            
            self.results['allocation_tests'] = {
                'allocation_properties': self.allocation_tests.test_attention_allocation_properties(),
                'temperature_effects': self.allocation_tests.test_attention_temperature_effects(),
                'scaling_properties': self.allocation_tests.test_attention_scaling_properties(),
                'consistency': self.allocation_tests.test_attention_consistency()
            }
            
            # 3. Processing Enhancement Tests
            logger.info("\n" + "="*60)
            logger.info("3. PROCESSING ENHANCEMENT TESTS")
            logger.info("="*60)
            
            self.results['enhancement_tests'] = {
                'mathematical_properties': self.enhancement_tests.test_enhancement_mathematical_properties(),
                'scaling': self.enhancement_tests.test_enhancement_scaling(),
                'attention_distribution': self.enhancement_tests.test_enhancement_attention_distribution()
            }
            
            # 4. Temporal Dynamics Tests
            logger.info("\n" + "="*60)
            logger.info("4. TEMPORAL DYNAMICS TESTS")
            logger.info("="*60)
            
            self.results['temporal_tests'] = {
                'temporal_evolution': self.temporal_tests.test_attention_temporal_evolution(),
                'habituation_effects': self.temporal_tests.test_habituation_effects(),
                'attention_persistence': self.temporal_tests.test_attention_persistence(),
                'multi_stimulus_competition': self.temporal_tests.test_multi_stimulus_temporal_competition()
            }
            
            # 5. Performance Tests
            logger.info("\n" + "="*60)
            logger.info("5. PERFORMANCE TESTS")
            logger.info("="*60)
            
            self.results['performance_tests'] = {
                'salience_performance': self.performance_tests.test_salience_computation_performance(),
                'allocation_performance': self.performance_tests.test_attention_allocation_performance(),
                'memory_usage': self.performance_tests.test_memory_usage(),
                'concurrent_performance': self.performance_tests.test_concurrent_performance()
            }
            
            # 6. Integration Tests
            logger.info("\n" + "="*60)
            logger.info("6. INTEGRATION TESTS")
            logger.info("="*60)
            
            self.results['integration_tests'] = {
                'goal_attention_integration': self.integration_tests.test_goal_attention_integration(),
                'attention_persistence_integration': self.integration_tests.test_attention_persistence_integration(),
                'multi_modal_integration': self.integration_tests.test_multi_modal_attention_integration()
            }
            
            self.end_time = time.time()
            
            # Generate summary
            self._generate_test_summary()
            
            # Save results if requested
            if save_results:
                self._save_test_results()
            
            # Generate visualizations if requested
            if generate_visualizations:
                self._generate_visualizations()
            
            logger.info("\n" + "="*80)
            logger.info("ALL TESTS COMPLETED SUCCESSFULLY!")
            logger.info("="*80)
            
            return self.results
            
        except Exception as e:
            logger.error(f"Test suite failed: {str(e)}")
            raise
    
    def run_specific_test_category(self, category: str) -> Dict[str, Any]:
        """Run specific test category."""
        logger.info(f"Running specific test category: {category}")
        
        if category == 'salience':
            return {
                'component_ranges': self.salience_tests.test_salience_component_ranges(),
                'mathematical_consistency': self.salience_tests.test_salience_mathematical_consistency(),
                'goal_sensitivity': self.salience_tests.test_salience_goal_sensitivity(),
                'stimulus_diversity': self.salience_tests.test_salience_stimulus_diversity()
            }
        elif category == 'allocation':
            return {
                'allocation_properties': self.allocation_tests.test_attention_allocation_properties(),
                'temperature_effects': self.allocation_tests.test_attention_temperature_effects(),
                'scaling_properties': self.allocation_tests.test_attention_scaling_properties(),
                'consistency': self.allocation_tests.test_attention_consistency()
            }
        elif category == 'enhancement':
            return {
                'mathematical_properties': self.enhancement_tests.test_enhancement_mathematical_properties(),
                'scaling': self.enhancement_tests.test_enhancement_scaling(),
                'attention_distribution': self.enhancement_tests.test_enhancement_attention_distribution()
            }
        elif category == 'temporal':
            return {
                'temporal_evolution': self.temporal_tests.test_attention_temporal_evolution(),
                'habituation_effects': self.temporal_tests.test_habituation_effects(),
                'attention_persistence': self.temporal_tests.test_attention_persistence(),
                'multi_stimulus_competition': self.temporal_tests.test_multi_stimulus_temporal_competition()
            }
        elif category == 'performance':
            return {
                'salience_performance': self.performance_tests.test_salience_computation_performance(),
                'allocation_performance': self.performance_tests.test_attention_allocation_performance(),
                'memory_usage': self.performance_tests.test_memory_usage(),
                'concurrent_performance': self.performance_tests.test_concurrent_performance()
            }
        elif category == 'integration':
            return {
                'goal_attention_integration': self.integration_tests.test_goal_attention_integration(),
                'attention_persistence_integration': self.integration_tests.test_attention_persistence_integration(),
                'multi_modal_integration': self.integration_tests.test_multi_modal_attention_integration()
            }
        else:
            raise ValueError(f"Unknown test category: {category}")
    
    def _generate_test_summary(self):
        """Generate comprehensive test summary."""
        total_time = self.end_time - self.start_time if self.end_time and self.start_time else 0
        
        summary = {
            'execution_time': total_time,
            'test_categories': len(self.results),
            'total_tests': self._count_total_tests(),
            'validation_summary': self._extract_validation_summary(),
            'performance_summary': self._extract_performance_summary(),
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
        }
        
        self.results['test_summary'] = summary
        
        # Log summary
        logger.info(f"\nTEST EXECUTION SUMMARY:")
        logger.info(f"  Total execution time: {total_time:.2f} seconds")
        logger.info(f"  Test categories: {summary['test_categories']}")
        logger.info(f"  Total tests: {summary['total_tests']}")
        logger.info(f"  Validation success rate: {summary['validation_summary']['overall_success_rate']:.2%}")
    
    def _count_total_tests(self) -> int:
        """Count total number of tests executed."""
        count = 0
        
        def count_tests(obj):
            nonlocal count
            if isinstance(obj, dict):
                for key, value in obj.items():
                    if key.endswith('_tests') and isinstance(value, dict):
                        count += len(value)
                    elif isinstance(value, dict):
                        count_tests(value)
        
        count_tests(self.results)
        return count
    
    def _extract_validation_summary(self) -> Dict[str, Any]:
        """Extract overall validation summary."""
        total_validations = 0
        total_passed = 0
        
        def count_validations(obj):
            nonlocal total_validations, total_passed
            if isinstance(obj, dict):
                for key, value in obj.items():
                    if key == 'validation' and isinstance(value, dict):
                        for val_result in value.values():
                            total_validations += 1
                            if val_result:
                                total_passed += 1
                    elif isinstance(value, (dict, list)):
                        count_validations(value)
            elif isinstance(obj, list):
                for item in obj:
                    count_validations(item)
        
        count_validations(self.results)
        
        success_rate = total_passed / total_validations if total_validations > 0 else 0.0
        
        return {
            'total_validations': total_validations,
            'total_passed': total_passed,
            'total_failed': total_validations - total_passed,
            'overall_success_rate': success_rate
        }
    
    def _extract_performance_summary(self) -> Dict[str, Any]:
        """Extract performance summary."""
        performance_summary = {}
        
        if 'performance_tests' in self.results:
            perf_data = self.results['performance_tests']
            
            # Salience performance
            if 'salience_performance' in perf_data:
                performance_summary['salience_computations_per_second'] = perf_data['salience_performance']['computations_per_second']
            
            # Allocation performance
            if 'allocation_performance' in perf_data:
                alloc_data = perf_data['allocation_performance']
                if 100 in alloc_data:  # 100 stimuli benchmark
                    performance_summary['allocations_per_second_100_stimuli'] = alloc_data[100]['allocations_per_second']
            
            # Memory usage
            if 'memory_usage' in perf_data:
                performance_summary['memory_increase_mb'] = perf_data['memory_usage']['memory_increase_mb']
            
            # Concurrent performance
            if 'concurrent_performance' in perf_data:
                performance_summary['concurrent_coefficient_of_variation'] = perf_data['concurrent_performance']['coefficient_of_variation']
        
        return performance_summary
    
    def _save_test_results(self):
        """Save test results to files."""
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        
        # Save main results
        results_path = self.data_manager.save_test_results(
            self.results, 
            "attentional_awareness_tests", 
            timestamp
        )
        
        # Generate and save report
        report_path = self.data_manager.export_test_report(
            self.results, 
            "attentional_awareness"
        )
        
        logger.info(f"Test results saved to: {results_path}")
        logger.info(f"Test report exported to: {report_path}")
    
    def _generate_visualizations(self):
        """Generate comprehensive visualizations."""
        logger.info("Generating visualizations...")
        
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        viz_dir = self.data_manager.base_path / f"visualizations_{timestamp}"
        viz_dir.mkdir(exist_ok=True)
        
        try:
            # 1. Salience component visualization
            if 'salience_tests' in self.results and 'stimulus_diversity' in self.results['salience_tests']:
                # Extract salience data for visualization
                attention_system = MockAttentionalAwareness(self.config)
                stimulus_generator = StimulusGenerator(self.config)
                
                # Generate sample data for visualization
                stimuli = stimulus_generator.generate_stimulus_batch(100, 'mixed')
                goals = stimulus_generator.generate_goal_features(3)
                
                salience_data = {}
                for i, stimulus in enumerate(stimuli[:50]):  # Limit for visualization
                    stimulus_id = f"viz_stimulus_{i}"
                    attention_system.calculate_salience(stimulus_id, stimulus, goals)
                    if stimulus_id in attention_system.salience_scores:
                        salience_data[stimulus_id] = attention_system.salience_scores[stimulus_id]
                
                if salience_data:
                    self.visualizer.plot_salience_components(
                        salience_data, 
                        save_path=str(viz_dir / "salience_components.png")
                    )
            
            # 2. Temporal dynamics visualization
            if 'temporal_tests' in self.results:
                temporal_data = self.results['temporal_tests']
                
                # Attention evolution visualization
                if 'temporal_evolution' in temporal_data:
                    attention_history = {'stimulus': temporal_data['temporal_evolution']['attention_history']}
                    salience_history = {'stimulus': temporal_data['temporal_evolution']['salience_history']}
                    
                    self.visualizer.plot_attention_dynamics(
                        attention_history,
                        salience_history,
                        save_path=str(viz_dir / "temporal_dynamics.png")
                    )
                
                # Multi-stimulus competition visualization
                if 'multi_stimulus_competition' in temporal_data:
                    multi_attention = temporal_data['multi_stimulus_competition']['attention_histories']
                    multi_salience = temporal_data['multi_stimulus_competition']['stimulus_histories']
                    
                    self.visualizer.plot_attention_dynamics(
                        multi_attention,
                        multi_salience,
                        save_path=str(viz_dir / "multi_stimulus_competition.png")
                    )
            
            # 3. Performance metrics visualization
            if 'performance_tests' in self.results:
                self.visualizer.plot_performance_metrics(
                    {'scaling_results': self.results['performance_tests'].get('allocation_performance', {}),
                     'memory_usage': self.results['performance_tests'].get('memory_usage', {}),
                     'concurrent_performance': self.results['performance_tests'].get('concurrent_performance', {}),
                     'temperature_effects': self.results['allocation_tests'].get('temperature_effects', {}),
                     'attention_metrics': self.results.get('test_summary', {}).get('performance_summary', {}),
                     'validation_results': self.results.get('test_summary', {}).get('validation_summary', {})},
                    save_path=str(viz_dir / "performance_metrics.png")
                )
            
            logger.info(f"Visualizations saved to: {viz_dir}")
            
        except Exception as e:
            logger.warning(f"Visualization generation failed: {str(e)}")

# ============================================================================
# Pytest Integration
# ============================================================================

# Test fixtures for pytest integration
@pytest.fixture
def attention_config():
    """Pytest fixture for attention test configuration."""
    return AttentionTestConfig()

@pytest.fixture
def stimulus_generator(attention_config):
    """Pytest fixture for stimulus generator."""
    return StimulusGenerator(attention_config)

@pytest.fixture
def mock_attention_system(attention_config):
    """Pytest fixture for mock attention system."""
    return MockAttentionalAwareness(attention_config)

@pytest.fixture
def attention_validator():
    """Pytest fixture for attention validator."""
    return AttentionMathValidator()

@pytest.fixture
def test_suite(attention_config):
    """Pytest fixture for complete test suite."""
    return AttentionalAwarenessTestSuite(attention_config)

# Individual pytest test functions
def test_salience_component_ranges(attention_config):
    """Pytest wrapper for salience component range tests."""
    test_instance = TestSalienceCalculation(attention_config)
    results = test_instance.test_salience_component_ranges()
    assert all(v == 0 for v in results.values()), "All component ranges should be valid"

def test_attention_allocation_properties(attention_config):
    """Pytest wrapper for attention allocation property tests."""
    test_instance = TestAttentionAllocation(attention_config)
    results = test_instance.test_attention_allocation_properties()
    
    for case_name, case_data in results.items():
        validation = case_data['validation']
        for check, passed in validation.items():
            assert passed, f"Attention allocation validation failed for {case_name}: {check}"

def test_processing_enhancement_mathematical_properties(attention_config):
    """Pytest wrapper for processing enhancement tests."""
    test_instance = TestProcessingEnhancement(attention_config)
    results = test_instance.test_enhancement_mathematical_properties()
    
    for case_name, case_data in results.items():
        validation = case_data['validation']
        for check, passed in validation.items():
            assert passed, f"Processing enhancement validation failed for {case_name}: {check}"

def test_temporal_dynamics_validation(attention_config):
    """Pytest wrapper for temporal dynamics tests."""
    test_instance = TestTemporalDynamics(attention_config)
    results = test_instance.test_attention_temporal_evolution()
    
    validation = results['validation']
    for check, passed in validation.items():
        assert passed, f"Temporal dynamics validation failed: {check}"

def test_performance_benchmarks(attention_config):
    """Pytest wrapper for performance tests."""
    test_instance = TestAttentionPerformance(attention_config)
    
    # Test salience performance
    salience_results = test_instance.test_salience_computation_performance()
    assert salience_results['computations_per_second'] >= attention_config.min_salience_computations_per_second
    
    # Test allocation performance
    allocation_results = test_instance.test_attention_allocation_performance()
    allocation_rate_100 = allocation_results[100]['allocations_per_second']
    assert allocation_rate_100 >= attention_config.min_attention_allocations_per_second
    
    # Test memory usage
    memory_results = test_instance.test_memory_usage()
    assert memory_results['memory_increase_mb'] < attention_config.max_memory_usage_mb

def test_integration_functionality(attention_config):
    """Pytest wrapper for integration tests."""
    test_instance = TestAttentionIntegration(attention_config)
    
    # Test goal-attention integration
    goal_results = test_instance.test_goal_attention_integration()
    assert goal_results['total_goal_effect'] > 1e-6, "Goals should influence salience computation"
    
    # Test persistence integration
    persistence_results = test_instance.test_attention_persistence_integration()
    assert persistence_results['max_buildup_attention'] > 0.5, "Attention should build up during stimulus presentation"

# ============================================================================
# Main Execution and Command Line Interface
# ============================================================================

def run_comprehensive_attention_tests(config: Optional[AttentionTestConfig] = None,
                                     save_results: bool = True,
                                     generate_visualizations: bool = True,
                                     specific_category: Optional[str] = None) -> Dict[str, Any]:
    """
    Run comprehensive attention tests with optional category filtering.
    
    Args:
        config: Test configuration
        save_results: Whether to save results to files
        generate_visualizations: Whether to generate visualizations
        specific_category: Optional specific test category to run
        
    Returns:
        Complete test results dictionary
    """
    test_suite = AttentionalAwarenessTestSuite(config)
    
    if specific_category:
        results = {f"{specific_category}_tests": test_suite.run_specific_test_category(specific_category)}
    else:
        results = test_suite.run_all_tests(save_results, generate_visualizations)
    
    return results

def main():
    """Main execution function with command line argument handling."""
    import argparse
    
    parser = argparse.ArgumentParser(description='ULTRA Attentional Awareness Test Suite')
    parser.add_argument('--category', type=str, choices=['salience', 'allocation', 'enhancement', 'temporal', 'performance', 'integration'],
                       help='Run specific test category only')
    parser.add_argument('--no-save', action='store_true', help='Do not save results to files')
    parser.add_argument('--no-viz', action='store_true', help='Do not generate visualizations')
    parser.add_argument('--config-file', type=str, help='Path to configuration file')
    parser.add_argument('--output-dir', type=str, default='./attention_test_data', help='Output directory for results')
    parser.add_argument('--verbose', action='store_true', help='Enable verbose logging')
    
    args = parser.parse_args()
    
    # Configure logging level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Load configuration
    if args.config_file:
        # Load configuration from file (implement as needed)
        config = AttentionTestConfig()
        logger.info(f"Configuration loaded from: {args.config_file}")
    else:
        config = AttentionTestConfig()
    
    # Update output directory
    if args.output_dir:
        config.output_dir = args.output_dir
    
    # Run tests
    try:
        results = run_comprehensive_attention_tests(
            config=config,
            save_results=not args.no_save,
            generate_visualizations=not args.no_viz,
            specific_category=args.category
        )
        
        # Print summary
        if 'test_summary' in results:
            summary = results['test_summary']
            print(f"\n" + "="*80)
            print(f"TEST EXECUTION COMPLETED")
            print(f"="*80)
            print(f"Execution time: {summary['execution_time']:.2f} seconds")
            print(f"Test categories: {summary['test_categories']}")
            print(f"Total tests: {summary['total_tests']}")
            print(f"Validation success rate: {summary['validation_summary']['overall_success_rate']:.2%}")
            print(f"="*80)
        
        return 0
        
    except Exception as e:
        logger.error(f"Test execution failed: {str(e)}")
        return 1

# ============================================================================
# Module Exports and Metadata
# ============================================================================

__all__ = [
    # Configuration
    'AttentionTestConfig',
    
    # Utilities
    'AttentionMathValidator',
    'StimulusGenerator',
    'NoveltySimulator',
    
    # Mock Systems
    'MockAttentionalAwareness',
    
    # Test Classes
    'TestSalienceCalculation',
    'TestAttentionAllocation',
    'TestProcessingEnhancement',
    'TestTemporalDynamics',
    'TestAttentionPerformance',
    'TestAttentionIntegration',
    
    # Visualization and Analysis
    'AttentionVisualizer',
    'AttentionDataManager',
    
    # Test Suite
    'AttentionalAwarenessTestSuite',
    
    # Main Functions
    'run_comprehensive_attention_tests',
    'main',
    
    # Pytest Fixtures
    'attention_config',
    'stimulus_generator',
    'mock_attention_system',
    'attention_validator',
    'test_suite',
    
    # Pytest Test Functions
    'test_salience_component_ranges',
    'test_attention_allocation_properties',
    'test_processing_enhancement_mathematical_properties',
    'test_temporal_dynamics_validation',
    'test_performance_benchmarks',
    'test_integration_functionality'
]

# Module metadata
__version__ = '1.0.0'
__author__ = 'ULTRA Development Team'
__description__ = 'Comprehensive test suite for ULTRA Attentional Awareness component'
__license__ = 'MIT'

# Performance monitoring setup
_module_load_time = time.time()
logger.info(f"ULTRA Attentional Awareness Test Suite v{__version__} loaded successfully")
logger.info(f"Module load time: {time.time() - _module_load_time:.3f} seconds")
logger.info(f"Available test components: {len(__all__)} exports")

# Conditional main execution
if __name__ == "__main__":
    import sys
    sys.exit(main())