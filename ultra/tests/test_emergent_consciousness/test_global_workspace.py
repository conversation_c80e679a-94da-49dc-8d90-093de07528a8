"""
ULTRA Global Workspace Test Suite
=================================

Comprehensive test suite for the Global Workspace component of the
Emergent Consciousness Lattice, implementing rigorous mathematical validation,
performance benchmarking, and production-grade testing for:

- Competition for workspace access
- Coalition formation and synergy detection
- Broadcast mechanisms and influence propagation
- Workspace capacity management
- Temporal dynamics and decay processes
- Neural network components for workspace control
- Integration with consciousness processes

Mathematical Foundation:
- P(x_i ∈ GW) = exp(C(x_i)/τ) / Σⱼ exp(C(x_j)/τ)
- I_j(t+1) = I_j(t) + α_j · GW(t)
- dGW(t)/dt = -λ·GW(t) + Σᵢ βᵢ·xᵢ·1{xᵢ ∈ GW}
- Synergy(i,j) = f(features_i, features_j)
- M(x_i,t+1) = M(x_i,t) + γ(P(x_i ∈ GW) - M(x_i,t))
"""

import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.distributions import Normal, Categorical, MultivariateNormal, Beta
import pytest
import logging
import time
import uuid
from typing import Dict, List, Tuple, Optional, Any, Union, Callable, Set
from dataclasses import dataclass, field
from collections import defaultdict, deque, Counter
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from scipy import stats
from scipy.stats import entropy  # Fixed: moved from scipy.special to scipy.stats
from scipy.special import softmax  # This one is correct
from scipy.signal import correlate, find_peaks
from scipy.optimize import minimize
from scipy.signal import correlate, find_peaks, savgol_filter
from scipy.optimize import minimize, differential_evolution
from scipy.spatial.distance import cosine, euclidean, hamming
from sklearn.metrics import mutual_info_score, silhouette_score
from sklearn.decomposition import PCA, FastICA
from sklearn.cluster import KMeans, DBSCAN, SpectralClustering
from sklearn.preprocessing import StandardScaler, MinMaxScaler, RobustScaler
from sklearn.manifold import TSNE, MDS
import networkx as nx
from abc import ABC, abstractmethod
import warnings
import json
import pickle
import yaml
from pathlib import Path
import psutil
import threading
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
import multiprocessing as mp
from functools import lru_cache, wraps
import itertools
from copy import deepcopy
import gc

warnings.filterwarnings('ignore')

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# ============================================================================
# Mathematical Utility Functions
# ============================================================================

def sigmoid(x):
    """Numerically stable sigmoid function."""
    x = np.clip(x, -500, 500)
    return 1.0 / (1.0 + np.exp(-x))

def tanh_safe(x):
    """Numerically stable tanh function."""
    x = np.clip(x, -500, 500)
    return np.tanh(x)

def softmax_stable(x, temperature=1.0, axis=-1):
    """Numerically stable softmax with temperature scaling."""
    x = np.asarray(x)
    x_scaled = x / temperature
    x_max = np.max(x_scaled, axis=axis, keepdims=True)
    x_stable = x_scaled - x_max
    exp_x = np.exp(x_stable)
    return exp_x / np.sum(exp_x, axis=axis, keepdims=True)

def cosine_similarity(a, b):
    """Compute cosine similarity between two vectors."""
    a, b = np.asarray(a), np.asarray(b)
    if np.linalg.norm(a) == 0 or np.linalg.norm(b) == 0:
        return 0.0
    return np.dot(a, b) / (np.linalg.norm(a) * np.linalg.norm(b))

def euclidean_distance(a, b):
    """Compute Euclidean distance between two vectors."""
    return np.linalg.norm(np.asarray(a) - np.asarray(b))

def manhattan_distance(a, b):
    """Compute Manhattan distance between two vectors."""
    return np.sum(np.abs(np.asarray(a) - np.asarray(b)))

def kl_divergence(p, q, epsilon=1e-10):
    """Compute KL divergence between two probability distributions."""
    p, q = np.asarray(p) + epsilon, np.asarray(q) + epsilon
    return np.sum(p * np.log(p / q))

def jensen_shannon_divergence(p, q):
    """Compute Jensen-Shannon divergence between two distributions."""
    p, q = np.asarray(p), np.asarray(q)
    m = 0.5 * (p + q)
    return 0.5 * kl_divergence(p, m) + 0.5 * kl_divergence(q, m)

# ============================================================================
# Configuration and Constants
# ============================================================================

@dataclass
class GlobalWorkspaceTestConfig:
    """Configuration for global workspace testing."""
    
    # Test parameters
    n_test_contents: int = 1000
    n_temporal_steps: int = 500
    n_performance_trials: int = 100
    n_benchmark_iterations: int = 50
    
    # Content generation
    content_feature_dim: int = 128
    content_strength_range: Tuple[float, float] = (0.1, 2.0)
    content_noise_std: float = 0.1
    content_variety_factor: float = 1.5
    
    # Coalition parameters
    max_coalition_size: int = 5
    synergy_threshold: float = 0.7
    coalition_formation_steps: int = 20
    synergy_decay_rate: float = 0.05
    
    # Workspace parameters (matching ULTRA specs)
    workspace_capacity: int = 7  # Miller's magical number
    competition_temperature: float = 1.5
    broadcast_decay: float = 0.05
    access_threshold: float = 0.7
    influence_propagation_rate: float = 0.3
    
    # Neural network parameters
    network_learning_rate: float = 0.001
    network_batch_size: int = 64
    network_hidden_dim: int = 256
    network_dropout: float = 0.2
    
    # Validation thresholds
    competition_consistency_threshold: float = 0.95
    broadcast_efficiency_threshold: float = 0.8
    coalition_stability_threshold: float = 0.9
    temporal_coherence_threshold: float = 0.7
    
    # Performance benchmarks
    min_competitions_per_second: float = 50.0
    min_broadcasts_per_second: float = 30.0
    max_memory_usage_mb: float = 1000.0
    max_coalition_formation_time: float = 0.1
    
    # Advanced features
    enable_adaptive_capacity: bool = True
    enable_meta_workspace: bool = True
    enable_hierarchical_broadcasting: bool = True
    enable_predictive_competition: bool = True
    
    # Visualization settings
    figure_size: Tuple[int, int] = (14, 10)
    plot_dpi: int = 300
    color_palette: str = 'tab10'

# ============================================================================
# Advanced Mathematical Validation Utilities
# ============================================================================

class GlobalWorkspaceMathValidator:
    """Advanced mathematical validation for global workspace mechanisms."""
    
    @staticmethod
    def validate_competition_probabilities(content_strengths: Dict[str, float],
                                         access_probabilities: Dict[str, float],
                                         temperature: float) -> Dict[str, bool]:
        """
        Validate competition probability computation.
        
        P(x_i ∈ GW) = exp(C(x_i)/τ) / Σⱼ exp(C(x_j)/τ)
        """
        validations = {}
        
        if not content_strengths or not access_probabilities:
            return {'empty_inputs': False}
        
        # Check input consistency
        validations['same_keys'] = set(content_strengths.keys()) == set(access_probabilities.keys())
        
        # Check probability properties
        prob_values = list(access_probabilities.values())
        validations['probabilities_positive'] = all(p >= 0 for p in prob_values)
        validations['probabilities_sum_to_one'] = abs(sum(prob_values) - 1.0) < 1e-6
        validations['probabilities_bounded'] = all(0 <= p <= 1 for p in prob_values)
        
        # Validate softmax computation
        strength_values = np.array([content_strengths[key] for key in content_strengths.keys()])
        expected_probs = softmax_stable(strength_values, temperature)
        actual_probs = np.array([access_probabilities[key] for key in content_strengths.keys()])
        
        validations['softmax_consistency'] = np.allclose(expected_probs, actual_probs, rtol=1e-5)
        
        # Check ordering consistency
        strength_order = sorted(content_strengths.items(), key=lambda x: x[1], reverse=True)
        prob_order = sorted(access_probabilities.items(), key=lambda x: x[1], reverse=True)
        
        validations['probability_follows_strength'] = (
            strength_order[0][0] == prob_order[0][0] if len(strength_order) > 0 else True
        )
        
        # Validate temperature effects
        if temperature > 0:
            # Higher temperature should lead to more uniform distribution
            entropy_val = entropy(prob_values)
            max_entropy = np.log(len(prob_values))
            validations['entropy_reasonable'] = 0 <= entropy_val <= max_entropy
        
        return validations
    
    @staticmethod
    def validate_broadcast_dynamics(initial_influences: Dict[str, float],
                                  updated_influences: Dict[str, float],
                                  workspace_content: Dict[str, float],
                                  propagation_rates: Dict[str, float],
                                  dt: float = 1.0) -> Dict[str, bool]:
        """
        Validate broadcast influence propagation.
        
        I_j(t+1) = I_j(t) + α_j · GW(t) · dt
        """
        validations = {}
        
        # Check input consistency
        validations['consistent_keys'] = (
            set(initial_influences.keys()) == set(updated_influences.keys()) ==
            set(propagation_rates.keys())
        )
        
        # Validate influence updates
        for target_id in initial_influences.keys():
            initial_inf = initial_influences[target_id]
            updated_inf = updated_influences[target_id]
            prop_rate = propagation_rates[target_id]
            
            # Calculate expected influence change
            workspace_strength = sum(workspace_content.values())
            expected_change = prop_rate * workspace_strength * dt
            expected_updated = initial_inf + expected_change
            
            # Validate update
            tolerance = 1e-6
            validations[f'influence_update_{target_id}'] = (
                abs(updated_inf - expected_updated) < tolerance
            )
        
        # Check influence bounds
        all_influences = list(updated_influences.values())
        validations['influences_non_negative'] = all(inf >= 0 for inf in all_influences)
        
        # Validate influence conservation (total influence should not decrease without decay)
        total_initial = sum(initial_influences.values())
        total_updated = sum(updated_influences.values())
        total_expected_increase = sum(
            propagation_rates[tid] * sum(workspace_content.values()) * dt
            for tid in initial_influences.keys()
        )
        
        expected_total = total_initial + total_expected_increase
        validations['influence_conservation'] = abs(total_updated - expected_total) < 1e-5
        
        return validations
    
    @staticmethod
    def validate_workspace_dynamics(workspace_states: List[Dict[str, float]],
                                  decay_rate: float,
                                  dt: float = 1.0) -> Dict[str, bool]:
        """
        Validate workspace temporal dynamics.
        
        dGW(t)/dt = -λ·GW(t) + Σᵢ βᵢ·xᵢ·1{xᵢ ∈ GW}
        """
        validations = {}
        
        if len(workspace_states) < 2:
            return {'insufficient_data': False}
        
        # Check temporal consistency
        validations['states_not_empty'] = all(len(state) >= 0 for state in workspace_states)
        
        # Validate decay behavior
        total_strengths = [sum(state.values()) for state in workspace_states]
        
        # Check for reasonable decay without new inputs
        if len(total_strengths) >= 3:
            # Look for periods without new content addition
            for i in range(1, len(total_strengths) - 1):
                prev_strength = total_strengths[i-1]
                curr_strength = total_strengths[i]
                
                # If no new content was added, strength should decay
                if len(workspace_states[i]) == len(workspace_states[i-1]):
                    expected_decay = prev_strength * np.exp(-decay_rate * dt)
                    validations[f'decay_step_{i}'] = curr_strength <= prev_strength
        
        # Check capacity bounds
        capacity_violations = sum(1 for state in workspace_states if len(state) > 10)  # Reasonable upper bound
        validations['capacity_respected'] = capacity_violations == 0
        
        # Validate strength dynamics
        for i in range(1, len(workspace_states)):
            prev_state = workspace_states[i-1]
            curr_state = workspace_states[i]
            
            # Check that content strengths decay appropriately
            for content_id in prev_state.keys():
                if content_id in curr_state:
                    prev_strength = prev_state[content_id]
                    curr_strength = curr_state[content_id]
                    
                    # Strength should not increase without external input
                    validations[f'strength_decay_{content_id}_{i}'] = curr_strength <= prev_strength * 1.01  # Small tolerance
        
        return validations
    
    @staticmethod
    def validate_coalition_formation(individual_contents: List[Dict[str, Any]],
                                   formed_coalitions: List[Dict[str, Any]],
                                   synergy_threshold: float) -> Dict[str, bool]:
        """Validate coalition formation based on synergy calculations."""
        validations = {}
        
        # Check that coalitions contain valid members
        all_content_ids = set()
        for content in individual_contents:
            all_content_ids.add(content.get('id', ''))
        
        coalition_member_ids = set()
        for coalition in formed_coalitions:
            members = coalition.get('members', [])
            coalition_member_ids.update(members)
        
        validations['valid_coalition_members'] = coalition_member_ids.issubset(all_content_ids)
        
        # Check synergy threshold compliance
        for coalition in formed_coalitions:
            synergy_score = coalition.get('synergy_score', 0.0)
            validations[f'synergy_threshold_{coalition.get("id", "")}'] = synergy_score >= synergy_threshold
        
        # Check coalition strength computation
        for coalition in formed_coalitions:
            members = coalition.get('members', [])
            expected_strength = 0.0
            
            for content in individual_contents:
                if content.get('id', '') in members:
                    expected_strength += content.get('strength', 0.0)
            
            # Coalition strength should be at least the sum of member strengths
            actual_strength = coalition.get('strength', 0.0)
            synergy_bonus = coalition.get('synergy_score', 1.0)
            
            expected_with_synergy = expected_strength * synergy_bonus
            validations[f'coalition_strength_{coalition.get("id", "")}'] = (
                abs(actual_strength - expected_with_synergy) < 1e-3
            )
        
        return validations
    
    @staticmethod
    def compute_workspace_metrics(workspace_history: List[Dict[str, Any]]) -> Dict[str, float]:
        """Compute comprehensive workspace performance metrics."""
        if not workspace_history:
            return {}
        
        # Extract time series data
        capacities = [len(state.get('contents', {})) for state in workspace_history]
        total_strengths = [sum(state.get('contents', {}).values()) for state in workspace_history]
        
        metrics = {
            'mean_capacity_utilization': np.mean(capacities) / 7.0,  # Normalize by Miller's number
            'capacity_variance': np.var(capacities),
            'max_capacity_used': max(capacities) if capacities else 0,
            'mean_total_strength': np.mean(total_strengths),
            'strength_variance': np.var(total_strengths),
            'temporal_stability': GlobalWorkspaceMathValidator._compute_temporal_stability(workspace_history),
            'content_turnover_rate': GlobalWorkspaceMathValidator._compute_turnover_rate(workspace_history),
            'broadcast_efficiency': GlobalWorkspaceMathValidator._compute_broadcast_efficiency(workspace_history)
        }
        
        return metrics
    
    @staticmethod
    def _compute_temporal_stability(workspace_history: List[Dict[str, Any]]) -> float:
        """Compute temporal stability of workspace contents."""
        if len(workspace_history) < 2:
            return 1.0
        
        stability_scores = []
        
        for i in range(1, len(workspace_history)):
            prev_contents = set(workspace_history[i-1].get('contents', {}).keys())
            curr_contents = set(workspace_history[i].get('contents', {}).keys())
            
            if len(prev_contents) == 0 and len(curr_contents) == 0:
                stability = 1.0
            elif len(prev_contents) == 0 or len(curr_contents) == 0:
                stability = 0.0
            else:
                intersection = len(prev_contents.intersection(curr_contents))
                union = len(prev_contents.union(curr_contents))
                stability = intersection / union if union > 0 else 0.0
            
            stability_scores.append(stability)
        
        return np.mean(stability_scores)
    
    @staticmethod
    def _compute_turnover_rate(workspace_history: List[Dict[str, Any]]) -> float:
        """Compute content turnover rate."""
        if len(workspace_history) < 2:
            return 0.0
        
        total_entries = 0
        total_exits = 0
        
        for i in range(1, len(workspace_history)):
            prev_contents = set(workspace_history[i-1].get('contents', {}).keys())
            curr_contents = set(workspace_history[i].get('contents', {}).keys())
            
            entries = len(curr_contents - prev_contents)
            exits = len(prev_contents - curr_contents)
            
            total_entries += entries
            total_exits += exits
        
        total_changes = total_entries + total_exits
        total_timesteps = len(workspace_history) - 1
        
        return total_changes / total_timesteps if total_timesteps > 0 else 0.0
    
    @staticmethod
    def _compute_broadcast_efficiency(workspace_history: List[Dict[str, Any]]) -> float:
        """Compute broadcast efficiency metric."""
        if not workspace_history:
            return 0.0
        
        successful_broadcasts = 0
        total_broadcasts = 0
        
        for state in workspace_history:
            broadcasts = state.get('broadcasts', [])
            total_broadcasts += len(broadcasts)
            
            for broadcast in broadcasts:
                # Consider broadcast successful if it had measurable influence
                influence = broadcast.get('influence', 0.0)
                if influence > 0.1:  # Threshold for meaningful influence
                    successful_broadcasts += 1
        
        return successful_broadcasts / total_broadcasts if total_broadcasts > 0 else 0.0

# ============================================================================
# Advanced Content and Coalition Generation
# ============================================================================

class ContentGenerator:
    """Advanced content generation for workspace testing."""
    
    def __init__(self, config: GlobalWorkspaceTestConfig):
        self.config = config
        self.feature_dim = config.content_feature_dim
        
        # Initialize generation parameters
        self._initialize_generators()
        
    def _initialize_generators(self):
        """Initialize content generation mechanisms."""
        # Content type templates
        self.content_types = {
            'visual': {
                'feature_pattern': 'spatial',
                'strength_bias': 0.1,
                'coherence_factor': 0.8
            },
            'auditory': {
                'feature_pattern': 'temporal',
                'strength_bias': 0.0,
                'coherence_factor': 0.6
            },
            'semantic': {
                'feature_pattern': 'sparse',
                'strength_bias': 0.2,
                'coherence_factor': 0.9
            },
            'motor': {
                'feature_pattern': 'sequential',
                'strength_bias': -0.1,
                'coherence_factor': 0.7
            },
            'emotional': {
                'feature_pattern': 'diffuse',
                'strength_bias': 0.3,
                'coherence_factor': 0.5
            }
        }
        
        # Synergy patterns for coalition formation
        self.synergy_patterns = {
            'complementary': lambda f1, f2: 1.0 - cosine_similarity(f1, f2),
            'reinforcing': lambda f1, f2: cosine_similarity(f1, f2),
            'competitive': lambda f1, f2: max(0.0, 0.5 - cosine_similarity(f1, f2)),
            'orthogonal': lambda f1, f2: abs(cosine_similarity(f1, f2))
        }
        
    def generate_content_batch(self, n_contents: int, 
                             content_types: Optional[List[str]] = None) -> List[Dict[str, Any]]:
        """Generate a batch of workspace contents with realistic properties."""
        if content_types is None:
            content_types = list(self.content_types.keys())
        
        contents = []
        
        for i in range(n_contents):
            content_type = np.random.choice(content_types)
            content = self._generate_single_content(i, content_type)
            contents.append(content)
        
        return contents
    
    def _generate_single_content(self, content_id: int, content_type: str) -> Dict[str, Any]:
        """Generate a single content item with specified characteristics."""
        type_config = self.content_types[content_type]
        
        # Generate features based on content type
        if type_config['feature_pattern'] == 'spatial':
            features = self._generate_spatial_features()
        elif type_config['feature_pattern'] == 'temporal':
            features = self._generate_temporal_features()
        elif type_config['feature_pattern'] == 'sparse':
            features = self._generate_sparse_features()
        elif type_config['feature_pattern'] == 'sequential':
            features = self._generate_sequential_features()
        else:  # diffuse
            features = self._generate_diffuse_features()
        
        # Calculate content strength
        base_strength = np.random.uniform(*self.config.content_strength_range)
        strength_bias = type_config['strength_bias']
        strength = max(0.1, base_strength + strength_bias)
        
        # Calculate coherence
        coherence = type_config['coherence_factor'] * np.random.uniform(0.8, 1.0)
        
        # Generate metadata
        content = {
            'id': f"{content_type}_{content_id}",
            'type': content_type,
            'features': features,
            'strength': strength,
            'coherence': coherence,
            'created_at': time.time(),
            'access_count': 0,
            'last_access': None,
            'activation_history': [],
            'synergy_potential': np.random.uniform(0.3, 0.9),
            'priority': np.random.uniform(0.1, 1.0),
            'complexity': self._calculate_complexity(features),
            'novelty': np.random.uniform(0.0, 1.0),
            'relevance': np.random.uniform(0.0, 1.0)
        }
        
        return content
    
    def _generate_spatial_features(self) -> np.ndarray:
        """Generate spatially structured features."""
        # Create features with spatial locality
        features = np.zeros(self.feature_dim)
        
        # Add multiple spatial "blobs"
        n_blobs = np.random.randint(2, 5)
        for _ in range(n_blobs):
            center = np.random.randint(0, self.feature_dim)
            size = np.random.randint(3, 10)
            strength = np.random.uniform(0.5, 2.0)
            
            for i in range(max(0, center - size), min(self.feature_dim, center + size)):
                distance = abs(i - center)
                features[i] += strength * np.exp(-distance / 2.0)
        
        # Add noise
        features += np.random.randn(self.feature_dim) * self.config.content_noise_std
        
        return features
    
    def _generate_temporal_features(self) -> np.ndarray:
        """Generate temporally structured features."""
        # Create features with temporal patterns
        features = np.zeros(self.feature_dim)
        
        # Add sinusoidal components
        n_components = np.random.randint(2, 4)
        for _ in range(n_components):
            frequency = np.random.uniform(0.1, 0.5)
            amplitude = np.random.uniform(0.5, 1.5)
            phase = np.random.uniform(0, 2 * np.pi)
            
            for i in range(self.feature_dim):
                features[i] += amplitude * np.sin(2 * np.pi * frequency * i + phase)
        
        # Add temporal correlations
        for i in range(1, self.feature_dim):
            correlation = 0.7
            features[i] = correlation * features[i-1] + np.sqrt(1 - correlation**2) * features[i]
        
        return features
    
    def _generate_sparse_features(self) -> np.ndarray:
        """Generate sparse feature representation."""
        features = np.zeros(self.feature_dim)
        
        # Activate only a small fraction of features
        n_active = np.random.randint(self.feature_dim // 10, self.feature_dim // 4)
        active_indices = np.random.choice(self.feature_dim, n_active, replace=False)
        
        for idx in active_indices:
            features[idx] = np.random.uniform(1.0, 3.0)
        
        return features
    
    def _generate_sequential_features(self) -> np.ndarray:
        """Generate sequentially structured features."""
        features = np.zeros(self.feature_dim)
        
        # Create sequential activation pattern
        n_sequences = np.random.randint(2, 4)
        sequence_length = self.feature_dim // n_sequences
        
        for seq in range(n_sequences):
            start_idx = seq * sequence_length
            end_idx = min((seq + 1) * sequence_length, self.feature_dim)
            
            # Create ramping activation
            for i, idx in enumerate(range(start_idx, end_idx)):
                position = i / (end_idx - start_idx)
                features[idx] = np.sin(np.pi * position) * np.random.uniform(0.8, 1.2)
        
        return features
    
    def _generate_diffuse_features(self) -> np.ndarray:
        """Generate diffusely distributed features."""
        # Use mixture of Gaussians for diffuse activation
        features = np.zeros(self.feature_dim)
        
        n_gaussians = np.random.randint(3, 6)
        for _ in range(n_gaussians):
            mean = np.random.uniform(-1, 1)
            std = np.random.uniform(0.3, 0.8)
            weight = np.random.uniform(0.2, 0.8)
            
            gaussian_component = weight * np.random.normal(mean, std, self.feature_dim)
            features += gaussian_component
        
        return features
    
    def _calculate_complexity(self, features: np.ndarray) -> float:
        """Calculate content complexity based on feature statistics."""
        # Use multiple complexity measures
        
        # 1. Feature variance
        variance_complexity = np.var(features)
        
        # 2. Spectral complexity (using FFT)
        fft = np.fft.fft(features)
        spectral_entropy = entropy(np.abs(fft) + 1e-10)
        
        # 3. Correlation complexity
        autocorr = np.correlate(features, features, mode='full')
        autocorr_entropy = entropy(np.abs(autocorr) + 1e-10)
        
        # 4. Sparsity measure
        sparsity = np.sum(np.abs(features) > 0.1) / len(features)
        
        # Combine measures
        complexity = (
            0.3 * tanh_safe(variance_complexity) +
            0.3 * tanh_safe(spectral_entropy / 10.0) +
            0.2 * tanh_safe(autocorr_entropy / 10.0) +
            0.2 * sparsity
        )
        
        return np.clip(complexity, 0.0, 1.0)
    
    def generate_coalition_candidates(self, contents: List[Dict[str, Any]], 
                                    max_coalition_size: int = 5) -> List[List[str]]:
        """Generate potential coalition combinations based on synergy."""
        if len(contents) < 2:
            return []
        
        content_ids = [content['id'] for content in contents]
        coalition_candidates = []
        
        # Generate combinations of different sizes
        for size in range(2, min(max_coalition_size + 1, len(contents) + 1)):
            combinations = list(itertools.combinations(content_ids, size))
            
            # Sample from combinations to avoid combinatorial explosion
            if len(combinations) > 50:
                combinations = np.random.choice(len(combinations), 50, replace=False)
                combinations = [list(itertools.combinations(content_ids, size))[i] for i in combinations]
            
            coalition_candidates.extend([list(combo) for combo in combinations])
        
        return coalition_candidates
    
    def calculate_synergy(self, contents: List[Dict[str, Any]], 
                         synergy_type: str = 'complementary') -> float:
        """Calculate synergy score for a group of contents."""
        if len(contents) < 2:
            return 1.0
        
        # Extract features
        features = [content['features'] for content in contents]
        
        # Calculate pairwise synergies
        synergy_func = self.synergy_patterns.get(synergy_type, self.synergy_patterns['complementary'])
        pairwise_synergies = []
        
        for i in range(len(features)):
            for j in range(i + 1, len(features)):
                synergy = synergy_func(features[i], features[j])
                pairwise_synergies.append(synergy)
        
        # Combine pairwise synergies
        if not pairwise_synergies:
            return 1.0
        
        # Use geometric mean for synergy combination
        synergy_product = np.prod(pairwise_synergies)
        combined_synergy = synergy_product ** (1.0 / len(pairwise_synergies))
        
        # Add group coherence bonus
        coherences = [content['coherence'] for content in contents]
        coherence_bonus = np.mean(coherences) * 0.1
        
        # Add size penalty (larger coalitions are harder to maintain)
        size_penalty = 0.95 ** (len(contents) - 2)
        
        final_synergy = (combined_synergy + coherence_bonus) * size_penalty
        
        return np.clip(final_synergy, 0.0, 2.0)

# ============================================================================
# Neural Network Components for Global Workspace
# ============================================================================

class CompetitionNetwork(nn.Module):
    """Neural network for competitive selection in global workspace."""
    
    def __init__(self, input_dim: int = 128, hidden_dim: int = 256, dropout: float = 0.2):
        super().__init__()
        
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        
        # Content encoder
        self.content_encoder = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.LayerNorm(hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim // 2, 64)
        )
        
        # Competition strength predictor
        self.strength_predictor = nn.Sequential(
            nn.Linear(64, 32),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(32, 16),
            nn.ReLU(),
            nn.Linear(16, 1)
        )
        
        # Context modulation
        self.context_modulator = nn.Sequential(
            nn.Linear(64 + 32, 64),  # content + context
            nn.ReLU(),
            nn.Linear(64, 32),
            nn.ReLU(),
            nn.Linear(32, 1),
            nn.Sigmoid()
        )
        
        # Coalition compatibility predictor
        self.coalition_predictor = nn.Sequential(
            nn.Linear(128, 64),  # Two content encodings
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(64, 32),
            nn.ReLU(),
            nn.Linear(32, 1),
            nn.Sigmoid()
        )
        
    def forward(self, content_features: torch.Tensor, 
                context_features: Optional[torch.Tensor] = None) -> Dict[str, torch.Tensor]:
        """Forward pass for competition network."""
        
        # Encode content
        encoded_content = self.content_encoder(content_features)
        
        # Predict base competition strength
        base_strength = self.strength_predictor(encoded_content)
        
        # Apply context modulation if available
        if context_features is not None:
            combined_features = torch.cat([encoded_content, context_features], dim=-1)
            context_modulation = self.context_modulator(combined_features)
            modulated_strength = base_strength * context_modulation
        else:
            modulated_strength = base_strength
        
        return {
            'encoded_content': encoded_content,
            'base_strength': base_strength,
            'modulated_strength': modulated_strength,
            'context_modulation': context_modulation if context_features is not None else None
        }
    
    def predict_coalition_synergy(self, content1_encoded: torch.Tensor, 
                                 content2_encoded: torch.Tensor) -> torch.Tensor:
        """Predict synergy between two pieces of content."""
        combined = torch.cat([content1_encoded, content2_encoded], dim=-1)
        synergy = self.coalition_predictor(combined)
        return synergy

class BroadcastController(nn.Module):
    """Neural network for controlling broadcast mechanisms."""
    
    def __init__(self, input_dim: int = 128, hidden_dim: int = 256, n_targets: int = 10):
        super().__init__()
        
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        self.n_targets = n_targets
        
        # Content analysis for broadcast
        self.content_analyzer = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Linear(hidden_dim // 2, 64)
        )
        
        # Broadcast strength predictor
        self.broadcast_strength = nn.Sequential(
            nn.Linear(64, 32),
            nn.ReLU(),
            nn.Linear(32, 1),
            nn.Sigmoid()
        )
        
        # Target-specific influence predictors
        self.target_predictors = nn.ModuleList([
            nn.Sequential(
                nn.Linear(64, 32),
                nn.ReLU(),
                nn.Linear(32, 16),
                nn.ReLU(),
                nn.Linear(16, 1)
            ) for _ in range(n_targets)
        ])
        
        # Attention mechanism for broadcast routing
        self.routing_attention = nn.MultiheadAttention(
            embed_dim=64, num_heads=8, dropout=0.1, batch_first=True
        )
        
        # Broadcast decay predictor
        self.decay_predictor = nn.Sequential(
            nn.Linear(64, 32),
            nn.ReLU(),
            nn.Linear(32, 1),
            nn.Sigmoid()
        )
        
    def forward(self, content_features: torch.Tensor,
                target_states: Optional[torch.Tensor] = None) -> Dict[str, torch.Tensor]:
        """Forward pass for broadcast controller."""
        
        # Analyze content for broadcasting
        analyzed_content = self.content_analyzer(content_features)
        
        # Predict broadcast strength
        broadcast_strength = self.broadcast_strength(analyzed_content)
        
        # Predict target-specific influences
        target_influences = []
        for target_predictor in self.target_predictors:
            influence = target_predictor(analyzed_content)
            target_influences.append(influence)
        
        target_influences = torch.stack(target_influences, dim=-1)
        
        # Apply attention-based routing if target states available
        if target_states is not None:
            # Use attention to modulate influences based on target states
            routed_content, attention_weights = self.routing_attention(
                analyzed_content.unsqueeze(1),
                target_states,
                target_states
            )
            
            # Modulate influences based on attention
            attention_modulation = attention_weights.squeeze(1)
            modulated_influences = target_influences * attention_modulation
        else:
            modulated_influences = target_influences
            attention_weights = None
        
        # Predict decay rate
        decay_rate = self.decay_predictor(analyzed_content)
        
        return {
            'analyzed_content': analyzed_content,
            'broadcast_strength': broadcast_strength,
            'target_influences': modulated_influences,
            'attention_weights': attention_weights,
            'decay_rate': decay_rate
        }

class WorkspaceStatePredictor(nn.Module):
    """Neural network for predicting workspace state evolution."""
    
    def __init__(self, input_dim: int = 128, hidden_dim: int = 256, max_capacity: int = 7):
        super().__init__()
        
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        self.max_capacity = max_capacity
        
        # Current state encoder
        self.state_encoder = nn.Sequential(
            nn.Linear(input_dim * max_capacity, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(hidden_dim, hidden_dim // 2)
        )
        
        # Temporal dynamics predictor (LSTM)
        self.temporal_lstm = nn.LSTM(
            input_size=hidden_dim // 2,
            hidden_size=hidden_dim // 2,
            num_layers=2,
            dropout=0.2,
            batch_first=True
        )
        
        # State evolution predictor
        self.evolution_predictor = nn.Sequential(
            nn.Linear(hidden_dim // 2, hidden_dim // 4),
            nn.ReLU(),
            nn.Linear(hidden_dim // 4, max_capacity),
            nn.Sigmoid()  # Probability of each slot being active
        )
        
        # Content strength predictor
        self.strength_predictor = nn.Sequential(
            nn.Linear(hidden_dim // 2, hidden_dim // 4),
            nn.ReLU(),
            nn.Linear(hidden_dim // 4, max_capacity),
            nn.Sigmoid()  # Predicted strength for each slot
        )
        
        # Capacity utilization predictor
        self.capacity_predictor = nn.Sequential(
            nn.Linear(hidden_dim // 2, 32),
            nn.ReLU(),
            nn.Linear(32, 1),
            nn.Sigmoid()
        )
        
    def forward(self, workspace_history: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        Predict next workspace state based on history.
        
        Args:
            workspace_history: Tensor of shape (batch, sequence_length, input_dim * max_capacity)
        """
        batch_size, seq_len, _ = workspace_history.shape
        
        # Encode each state in the history
        encoded_states = []
        for t in range(seq_len):
            state_t = workspace_history[:, t, :]
            encoded_t = self.state_encoder(state_t)
            encoded_states.append(encoded_t)
        
        encoded_history = torch.stack(encoded_states, dim=1)
        
        # Apply temporal LSTM
        lstm_output, (hidden, cell) = self.temporal_lstm(encoded_history)
        
        # Use final LSTM output for predictions
        final_state = lstm_output[:, -1, :]
        
        # Predict evolution
        slot_probabilities = self.evolution_predictor(final_state)
        slot_strengths = self.strength_predictor(final_state)
        capacity_utilization = self.capacity_predictor(final_state)
        
        return {
            'slot_probabilities': slot_probabilities,
            'slot_strengths': slot_strengths,
            'capacity_utilization': capacity_utilization,
            'encoded_states': encoded_history,
            'final_state': final_state
        }

# ============================================================================
# Mock Global Workspace Implementation
# ============================================================================

class MockGlobalWorkspace:
    """Production-grade mock implementation of Global Workspace for testing."""
    
    def __init__(self, config: GlobalWorkspaceTestConfig):
        self.config = config
        
        # Core workspace state
        self.workspace_contents = {}
        self.competing_coalitions = {}
        self.access_probabilities = {}
        self.broadcast_targets = set()
        
        # History tracking
        self.workspace_history = deque(maxlen=1000)
        self.broadcast_history = deque(maxlen=500)
        self.competition_history = deque(maxlen=500)
        
        # Neural networks
        self.competition_network = CompetitionNetwork(
            input_dim=config.content_feature_dim,
            hidden_dim=config.network_hidden_dim,
            dropout=config.network_dropout
        )
        
        self.broadcast_controller = BroadcastController(
            input_dim=config.content_feature_dim,
            hidden_dim=config.network_hidden_dim,
            n_targets=10
        )
        
        self.state_predictor = WorkspaceStatePredictor(
            input_dim=config.content_feature_dim,
            hidden_dim=config.network_hidden_dim,
            max_capacity=config.workspace_capacity
        )
        
        # Performance monitoring
        self.performance_metrics = defaultdict(list)
        self.computation_calls = defaultdict(int)
        
        # Advanced features
        self.coalition_cache = {}
        self.influence_matrix = np.zeros((10, 10))  # 10 broadcast targets
        self.adaptive_capacity = config.workspace_capacity
        
    def add_competitor(self, content_id: str, content_features: np.ndarray,
                      competitive_strength: float, metadata: Optional[Dict[str, Any]] = None) -> str:
        """Add a competitor for workspace access."""
        self.computation_calls['add_competitor'] += 1
        
        # Generate unique coalition ID
        coalition_id = f"coalition_{content_id}_{int(time.time() * 1000000) % 1000000}"
        
        # Create competitor entry
        competitor = {
            'content_id': content_id,
            'features': content_features.copy(),
            'strength': competitive_strength,
            'created_at': time.time(),
            'members': [content_id],
            'synergy_score': 1.0,
            'metadata': metadata or {},
            'access_attempts': 0,
            'last_competition': None,
            'competitive_advantage': self._calculate_competitive_advantage(content_features),
            'coalition_potential': self._estimate_coalition_potential(content_features)
        }
        
        # Store competitor
        self.competing_coalitions[coalition_id] = competitor
        
        logger.debug(f"Added competitor {coalition_id} with strength {competitive_strength:.3f}")
        return coalition_id
    
    def _calculate_competitive_advantage(self, features: np.ndarray) -> float:
        """Calculate competitive advantage based on features."""
        # Use neural network to predict advantage
        with torch.no_grad():
            features_tensor = torch.FloatTensor(features).unsqueeze(0)
            
            # Simple advantage calculation based on feature characteristics
            feature_magnitude = torch.norm(features_tensor)
            feature_sparsity = torch.sum(torch.abs(features_tensor) > 0.1) / len(features_tensor)
            feature_entropy = -torch.sum(features_tensor * torch.log(torch.abs(features_tensor) + 1e-10))
            
            # Combine measures
            advantage = (
                0.4 * torch.tanh(feature_magnitude) +
                0.3 * feature_sparsity +
                0.3 * torch.tanh(feature_entropy / 10.0)
            )
            
            return float(advantage.item())
    
    def _estimate_coalition_potential(self, features: np.ndarray) -> float:
        """Estimate potential for coalition formation."""
        # Base potential on feature diversity and coherence
        feature_std = np.std(features)
        feature_mean = np.mean(np.abs(features))
        
        # High diversity with moderate magnitude suggests good coalition potential
        diversity_score = tanh_safe(feature_std)
        magnitude_score = tanh_safe(feature_mean)
        
        potential = 0.6 * diversity_score + 0.4 * magnitude_score
        return np.clip(potential, 0.0, 1.0)
    
    def form_coalitions(self, synergy_threshold: Optional[float] = None) -> Dict[str, Dict[str, Any]]:
        """Form coalitions among competing content through synergy detection."""
        self.computation_calls['form_coalitions'] += 1
        
        if len(self.competing_coalitions) < 2:
            return self.competing_coalitions
        
        synergy_threshold = synergy_threshold or self.config.synergy_threshold
        
        # Extract competitors for coalition formation
        coalition_ids = list(self.competing_coalitions.keys())
        
        # Calculate pairwise synergies using neural network
        synergy_matrix = self._calculate_synergy_matrix(coalition_ids)
        
        # Form coalitions using graph-based clustering
        formed_coalitions = self._cluster_coalitions(coalition_ids, synergy_matrix, synergy_threshold)
        
        # Update coalition data
        self.competing_coalitions = formed_coalitions
        
        # Cache results for efficiency
        cache_key = hash(tuple(sorted(coalition_ids)))
        self.coalition_cache[cache_key] = formed_coalitions.copy()
        
        logger.debug(f"Formed {len(formed_coalitions)} coalitions from {len(coalition_ids)} competitors")
        return formed_coalitions
    
    def _calculate_synergy_matrix(self, coalition_ids: List[str]) -> np.ndarray:
        """Calculate synergy matrix between all coalition pairs."""
        n_coalitions = len(coalition_ids)
        synergy_matrix = np.zeros((n_coalitions, n_coalitions))
        
        # Use neural network to predict synergies
        with torch.no_grad():
            for i, id1 in enumerate(coalition_ids):
                for j, id2 in enumerate(coalition_ids):
                    if i != j:
                        features1 = self.competing_coalitions[id1]['features']
                        features2 = self.competing_coalitions[id2]['features']
                        
                        # Convert to tensors
                        f1_tensor = torch.FloatTensor(features1).unsqueeze(0)
                        f2_tensor = torch.FloatTensor(features2).unsqueeze(0)
                        
                        # Get encoded features from competition network
                        encoded1 = self.competition_network(f1_tensor)['encoded_content']
                        encoded2 = self.competition_network(f2_tensor)['encoded_content']
                        
                        # Predict synergy
                        synergy = self.competition_network.predict_coalition_synergy(encoded1, encoded2)
                        synergy_matrix[i, j] = float(synergy.item())
        
        return synergy_matrix
    
    def _cluster_coalitions(self, coalition_ids: List[str], 
                          synergy_matrix: np.ndarray, 
                          synergy_threshold: float) -> Dict[str, Dict[str, Any]]:
        """Cluster coalitions based on synergy matrix."""
        
        # Create graph from synergy matrix
        n_coalitions = len(coalition_ids)
        G = nx.Graph()
        
        # Add nodes
        for i, coalition_id in enumerate(coalition_ids):
            G.add_node(i, coalition_id=coalition_id)
        
        # Add edges for high-synergy pairs
        for i in range(n_coalitions):
            for j in range(i + 1, n_coalitions):
                if synergy_matrix[i, j] > synergy_threshold:
                    G.add_edge(i, j, weight=synergy_matrix[i, j])
        
        # Find connected components (coalitions)
        connected_components = list(nx.connected_components(G))
        
        # Build new coalition dictionary
        new_coalitions = {}
        
        for component in connected_components:
            # Get coalition IDs in this component
            component_coalition_ids = [coalition_ids[i] for i in component]
            
            if len(component_coalition_ids) == 1:
                # Single coalition - keep as is
                coalition_id = component_coalition_ids[0]
                new_coalitions[coalition_id] = self.competing_coalitions[coalition_id]
            else:
                # Multiple coalitions - merge them
                merged_coalition = self._merge_coalitions(component_coalition_ids, synergy_matrix, coalition_ids)
                new_coalition_id = f"merged_{len(new_coalitions)}"
                new_coalitions[new_coalition_id] = merged_coalition
        
        return new_coalitions
    
    def _merge_coalitions(self, coalition_ids: List[str], 
                         synergy_matrix: np.ndarray,
                         all_coalition_ids: List[str]) -> Dict[str, Any]:
        """Merge multiple coalitions into one."""
        
        # Collect all members
        all_members = []
        all_features = []
        total_strength = 0.0
        creation_times = []
        
        for coalition_id in coalition_ids:
            coalition = self.competing_coalitions[coalition_id]
            all_members.extend(coalition['members'])
            all_features.append(coalition['features'])
            total_strength += coalition['strength']
            creation_times.append(coalition['created_at'])
        
        # Calculate merged features (weighted average)
        strengths = [self.competing_coalitions[cid]['strength'] for cid in coalition_ids]
        total_weight = sum(strengths)
        
        if total_weight > 0:
            merged_features = np.zeros_like(all_features[0])
            for features, weight in zip(all_features, strengths):
                merged_features += (weight / total_weight) * features
        else:
            merged_features = np.mean(all_features, axis=0)
        
        # Calculate synergy score
        synergy_scores = []
        for i, id1 in enumerate(coalition_ids):
            for j, id2 in enumerate(coalition_ids):
                if i < j:  # Avoid double counting
                    idx1 = all_coalition_ids.index(id1)
                    idx2 = all_coalition_ids.index(id2)
                    synergy_scores.append(synergy_matrix[idx1, idx2])
        
        avg_synergy = np.mean(synergy_scores) if synergy_scores else 1.0
        
        # Create merged coalition
        merged_coalition = {
            'content_id': f"merged_coalition_{len(all_members)}_members",
            'features': merged_features,
            'strength': total_strength * avg_synergy,  # Synergy bonus
            'created_at': min(creation_times),
            'members': all_members,
            'synergy_score': avg_synergy,
            'metadata': {'merged_from': coalition_ids},
            'access_attempts': 0,
            'last_competition': None,
            'competitive_advantage': self._calculate_competitive_advantage(merged_features),
            'coalition_potential': avg_synergy
        }
        
        return merged_coalition
    
    def compete_for_access(self) -> Optional[str]:
        """Run competition for workspace access using softmax selection."""
        self.computation_calls['compete_for_access'] += 1
        
        if not self.competing_coalitions:
            return None
        
        start_time = time.time()
        
        # Calculate enhanced competition strengths
        enhanced_strengths = {}
        
        for coalition_id, coalition in self.competing_coalitions.items():
            # Base strength
            base_strength = coalition['strength']
            
            # Competitive advantage bonus
            advantage_bonus = coalition.get('competitive_advantage', 0.0) * 0.2
            
            # Recency bonus (newer coalitions get slight advantage)
            recency_bonus = max(0.0, 0.1 - (time.time() - coalition['created_at']) / 3600.0)
            
            # Coalition size penalty (larger coalitions are harder to coordinate)
            size_penalty = 0.95 ** (len(coalition['members']) - 1)
            
            # Final enhanced strength
            enhanced_strength = (base_strength + advantage_bonus + recency_bonus) * size_penalty
            enhanced_strengths[coalition_id] = enhanced_strength
            
            # Update access attempts
            coalition['access_attempts'] += 1
        
        # Calculate access probabilities using softmax
        strength_values = np.array(list(enhanced_strengths.values()))
        access_probs = softmax_stable(strength_values, self.config.competition_temperature)
        
        # Store access probabilities
        self.access_probabilities = {}
        for i, coalition_id in enumerate(enhanced_strengths.keys()):
            self.access_probabilities[coalition_id] = access_probs[i]
        
        # Select winner probabilistically
        coalition_ids = list(enhanced_strengths.keys())
        winner_idx = np.random.choice(len(coalition_ids), p=access_probs)
        winner_id = coalition_ids[winner_idx]
        
        # Check access threshold
        winner_probability = access_probs[winner_idx]
        
        # Record competition
        competition_record = {
            'timestamp': time.time(),
            'participants': list(enhanced_strengths.keys()),
            'winner': winner_id,
            'winner_probability': winner_probability,
            'competition_strengths': enhanced_strengths.copy(),
            'access_probabilities': self.access_probabilities.copy(),
            'competition_time': time.time() - start_time
        }
        
        self.competition_history.append(competition_record)
        
        # Update winner's last competition time
        if winner_id in self.competing_coalitions:
            self.competing_coalitions[winner_id]['last_competition'] = time.time()
        
        # Performance tracking
        self.performance_metrics['competition_time'].append(time.time() - start_time)
        self.performance_metrics['winner_probability'].append(winner_probability)
        
        if winner_probability > self.config.access_threshold:
            logger.debug(f"Coalition {winner_id} won access with probability {winner_probability:.3f}")
            return winner_id
        else:
            logger.debug(f"No coalition exceeded access threshold (best: {winner_probability:.3f})")
            return None
    
    def broadcast_to_workspace(self, coalition_id: str):
        """Broadcast winning coalition content to global workspace."""
        self.computation_calls['broadcast_to_workspace'] += 1
        
        if coalition_id not in self.competing_coalitions:
            logger.warning(f"Coalition {coalition_id} not found for broadcasting")
            return
        
        start_time = time.time()
        winning_coalition = self.competing_coalitions[coalition_id]
        
        # Check workspace capacity
        if len(self.workspace_contents) >= self._get_current_capacity():
            # Remove oldest/weakest content
            self._evict_workspace_content()
        
        # Predict broadcast effects using neural network
        broadcast_predictions = self._predict_broadcast_effects(winning_coalition)
        
        # Create workspace entry
        workspace_entry = {
            'coalition_id': coalition_id,
            'content_id': winning_coalition['content_id'],
            'features': winning_coalition['features'].copy(),
            'strength': winning_coalition['strength'],
            'members': winning_coalition['members'].copy(),
            'synergy_score': winning_coalition['synergy_score'],
            'broadcast_time': time.time(),
            'access_probability': self.access_probabilities.get(coalition_id, 0.0),
            'predicted_influence': broadcast_predictions['broadcast_strength'],
            'predicted_decay': broadcast_predictions['decay_rate'],
            'broadcast_targets': list(self.broadcast_targets),
            'influence_propagated': 0.0,
            'access_count': 0
        }
        
        # Add to workspace
        self.workspace_contents[coalition_id] = workspace_entry
        
        # Execute broadcast to targets
        broadcast_effects = self._execute_broadcast(workspace_entry, broadcast_predictions)
        
        # Record broadcast
        broadcast_record = {
            'coalition_id': coalition_id,
            'content_id': winning_coalition['content_id'],
            'broadcast_time': time.time(),
            'workspace_size': len(self.workspace_contents),
            'broadcast_strength': broadcast_predictions['broadcast_strength'],
            'target_influences': broadcast_effects['target_influences'],
            'total_influence': broadcast_effects['total_influence'],
            'broadcast_processing_time': time.time() - start_time
        }
        
        self.broadcast_history.append(broadcast_record)
        
        # Performance tracking
        self.performance_metrics['broadcast_time'].append(time.time() - start_time)
        self.performance_metrics['broadcast_strength'].append(float(broadcast_predictions['broadcast_strength']))
        
        logger.debug(f"Broadcast coalition {coalition_id} to workspace (influence: {broadcast_effects['total_influence']:.3f})")
    
    def _get_current_capacity(self) -> int:
        """Get current workspace capacity (adaptive if enabled)."""
        if not self.config.enable_adaptive_capacity:
            return self.config.workspace_capacity
        
        # Adaptive capacity based on system load and performance
        base_capacity = self.config.workspace_capacity
        
        # Increase capacity if system is performing well
        if len(self.performance_metrics['broadcast_strength']) > 10:
            recent_performance = np.mean(self.performance_metrics['broadcast_strength'][-10:])
            if recent_performance > 0.8:
                self.adaptive_capacity = min(base_capacity + 2, 10)
            elif recent_performance < 0.5:
                self.adaptive_capacity = max(base_capacity - 1, 5)
        
        return self.adaptive_capacity
    
    def _evict_workspace_content(self):
        """Evict content from workspace to make room."""
        if not self.workspace_contents:
            return
        
        # Calculate eviction scores (lower = more likely to evict)
        eviction_scores = {}
        current_time = time.time()
        
        for coalition_id, content in self.workspace_contents.items():
            # Factors favoring retention
            strength_factor = content['strength']
            recency_factor = 1.0 / (1.0 + current_time - content['broadcast_time'])
            access_factor = content.get('access_count', 0) * 0.1
            influence_factor = content.get('influence_propagated', 0.0) * 0.5
            
            # Combined retention score
            retention_score = (
                0.4 * strength_factor +
                0.3 * recency_factor +
                0.2 * access_factor +
                0.1 * influence_factor
            )
            
            eviction_scores[coalition_id] = retention_score
        
        # Evict content with lowest retention score
        eviction_candidate = min(eviction_scores.items(), key=lambda x: x[1])
        coalition_to_evict = eviction_candidate[0]
        
        logger.debug(f"Evicting coalition {coalition_to_evict} from workspace (retention score: {eviction_candidate[1]:.3f})")
        del self.workspace_contents[coalition_to_evict]
    
    def _predict_broadcast_effects(self, coalition: Dict[str, Any]) -> Dict[str, torch.Tensor]:
        """Predict broadcast effects using neural network."""
        with torch.no_grad():
            features_tensor = torch.FloatTensor(coalition['features']).unsqueeze(0)
            predictions = self.broadcast_controller(features_tensor)
            
            return {
                'broadcast_strength': predictions['broadcast_strength'].squeeze(),
                'target_influences': predictions['target_influences'].squeeze(),
                'decay_rate': predictions['decay_rate'].squeeze()
            }
    
    def _execute_broadcast(self, workspace_entry: Dict[str, Any], 
                          predictions: Dict[str, torch.Tensor]) -> Dict[str, Any]:
        """Execute broadcast to all registered targets."""
        broadcast_content = {
            'source': 'global_workspace',
            'content_id': workspace_entry['content_id'],
            'features': workspace_entry['features'],
            'strength': workspace_entry['strength'],
            'broadcast_time': workspace_entry['broadcast_time'],
            'synergy_score': workspace_entry['synergy_score']
        }
        
        # Calculate target-specific influences
        target_influences = predictions['target_influences'].numpy()
        total_influence = 0.0
        
        # Simulate influence propagation
        for i, target in enumerate(list(self.broadcast_targets)[:len(target_influences)]):
            influence = float(target_influences[i]) * workspace_entry['strength']
            
            # Update influence matrix
            if i < self.influence_matrix.shape[0]:
                self.influence_matrix[i, :] = (
                    self.influence_matrix[i, :] * 0.9 +  # Decay existing
                    influence * 0.1  # Add new influence
                )
            
            total_influence += influence
        
        # Update workspace entry with influence information
        workspace_entry['influence_propagated'] = total_influence
        
        return {
            'target_influences': target_influences,
            'total_influence': total_influence,
            'broadcast_content': broadcast_content
        }
    
    def update_workspace_dynamics(self, dt: float = 1.0):
        """Update workspace dynamics with decay and temporal evolution."""
        self.computation_calls['update_workspace_dynamics'] += 1
        
        current_time = time.time()
        decay_rate = self.config.broadcast_decay
        
        # Apply decay to workspace contents
        contents_to_remove = []
        
        for coalition_id, entry in self.workspace_contents.items():
            time_since_broadcast = current_time - entry['broadcast_time']
            
            # Calculate decay factor
            predicted_decay = entry.get('predicted_decay', 0.05)
            effective_decay_rate = decay_rate * (1.0 + predicted_decay)
            decay_factor = np.exp(-effective_decay_rate * time_since_broadcast)
            
            # Apply decay to strength
            entry['strength'] *= decay_factor
            
            # Update access tracking
            if entry['strength'] > 0.5:  # Only count if still significant
                entry['access_count'] += 1
            
            # Remove if strength too low
            if entry['strength'] < 0.1:
                contents_to_remove.append(coalition_id)
        
        # Remove decayed contents
        for coalition_id in contents_to_remove:
            logger.debug(f"Removing decayed content {coalition_id} from workspace")
            del self.workspace_contents[coalition_id]
        
        # Update influence matrix decay
        self.influence_matrix *= 0.95  # Gradual decay
        
        # Store workspace state in history
        workspace_state = {
            'timestamp': current_time,
            'contents': {k: v.copy() for k, v in self.workspace_contents.items()},
            'total_strength': sum(c['strength'] for c in self.workspace_contents.values()),
            'capacity_utilization': len(self.workspace_contents) / self._get_current_capacity(),
            'influence_matrix': self.influence_matrix.copy(),
            'broadcasts': []  # Will be filled by recent broadcasts
        }
        
        # Add recent broadcast information
        recent_broadcasts = [
            b for b in self.broadcast_history 
            if current_time - b['broadcast_time'] < 10.0  # Last 10 seconds
        ]
        workspace_state['broadcasts'] = recent_broadcasts
        
        self.workspace_history.append(workspace_state)
        
        # Predictive workspace evolution (if enabled)
        if self.config.enable_predictive_competition:
            self._update_predictive_state()
    
    def _update_predictive_state(self):
        """Update predictive state for future workspace evolution."""
        if len(self.workspace_history) < 3:
            return
        
        # Prepare history for neural network
        recent_states = list(self.workspace_history)[-10:]  # Last 10 states
        
        # Convert to tensor format (simplified)
        history_features = []
        for state in recent_states:
            # Create feature vector representing workspace state
            features = np.zeros(self.config.content_feature_dim * self.config.workspace_capacity)
            
            contents = state['contents']
            for i, (coalition_id, content) in enumerate(contents.items()):
                if i < self.config.workspace_capacity:
                    start_idx = i * self.config.content_feature_dim
                    end_idx = start_idx + self.config.content_feature_dim
                    features[start_idx:end_idx] = content['features'][:self.config.content_feature_dim]
            
            history_features.append(features)
        
        if len(history_features) >= 3:  # Need minimum history
            # Predict next state
            with torch.no_grad():
                history_tensor = torch.FloatTensor(history_features).unsqueeze(0)
                predictions = self.state_predictor(history_tensor)
                
                # Store predictions for potential use
                self.performance_metrics['predicted_capacity'].append(
                    float(predictions['capacity_utilization'].item())
                )
    
    def register_broadcast_target(self, target_id: str):
        """Register a system to receive broadcasts."""
        self.broadcast_targets.add(target_id)
        logger.debug(f"Registered broadcast target: {target_id}")
    
    def unregister_broadcast_target(self, target_id: str):
        """Unregister a broadcast target."""
        self.broadcast_targets.discard(target_id)
        logger.debug(f"Unregistered broadcast target: {target_id}")
    
    def get_workspace_state(self) -> Dict[str, Any]:
        """Get comprehensive current workspace state."""
        if not self.workspace_contents:
            return {
                'active_contents': 0,
                'total_strength': 0.0,
                'capacity_utilization': 0.0,
                'most_recent_broadcast': None,
                'registered_targets': len(self.broadcast_targets),
                'performance_summary': {}
            }
        
        total_strength = sum(entry['strength'] for entry in self.workspace_contents.values())
        capacity_utilization = len(self.workspace_contents) / self._get_current_capacity()
        
        # Find most recent broadcast
        most_recent = max(
            self.workspace_contents.values(), 
            key=lambda x: x['broadcast_time']
        )
        
        # Performance summary
        performance_summary = {}
        for metric, values in self.performance_metrics.items():
            if values:
                performance_summary[metric] = {
                    'mean': np.mean(values),
                    'std': np.std(values),
                    'count': len(values)
                }
        
        return {
            'active_contents': len(self.workspace_contents),
            'total_strength': total_strength,
            'capacity_utilization': capacity_utilization,
            'adaptive_capacity': self.adaptive_capacity,
            'most_recent_broadcast': {
                'content_id': most_recent['content_id'],
                'time_ago': time.time() - most_recent['broadcast_time'],
                'strength': most_recent['strength'],
                'influence_propagated': most_recent.get('influence_propagated', 0.0)
            },
            'registered_targets': len(self.broadcast_targets),
            'performance_summary': performance_summary,
            'computation_calls': dict(self.computation_calls),
            'influence_matrix_norm': np.linalg.norm(self.influence_matrix)
        }
    
    def clear_competitors(self):
        """Clear current competitors (typically called after competition)."""
        self.competing_coalitions.clear()
        self.access_probabilities.clear()
        logger.debug("Cleared all competitors")
    
    def reset_workspace(self):
        """Reset workspace to initial state."""
        self.workspace_contents.clear()
        self.competing_coalitions.clear()
        self.access_probabilities.clear()
        self.workspace_history.clear()
        self.broadcast_history.clear()
        self.competition_history.clear()
        self.performance_metrics.clear()
        self.computation_calls.clear()
        self.coalition_cache.clear()
        self.influence_matrix.fill(0.0)
        self.adaptive_capacity = self.config.workspace_capacity
        logger.info("Reset workspace to initial state")

# ============================================================================
# Core Test Classes
# ============================================================================

class TestWorkspaceCompetition:
    """Comprehensive tests for workspace competition mechanisms."""
    
    def __init__(self, config: GlobalWorkspaceTestConfig):
        self.config = config
        self.validator = GlobalWorkspaceMathValidator()
        self.content_generator = ContentGenerator(config)
        
    def test_competition_probability_calculation(self):
        """Test competition probability calculation and mathematical consistency."""
        logger.info("Testing competition probability calculation")
        
        workspace = MockGlobalWorkspace(self.config)
        
        # Generate test contents with varying strengths
        test_cases = [
            {'n_contents': 3, 'strength_range': (0.5, 1.5), 'case': 'balanced'},
            {'n_contents': 5, 'strength_range': (0.1, 2.0), 'case': 'diverse'},
            {'n_contents': 10, 'strength_range': (0.8, 1.2), 'case': 'similar'},
            {'n_contents': 1, 'strength_range': (1.0, 1.0), 'case': 'single'},
        ]
        
        results = {}
        
        for test_case in test_cases:
            case_name = test_case['case']
            n_contents = test_case['n_contents']
            strength_range = test_case['strength_range']
            
            # Generate contents
            contents = self.content_generator.generate_content_batch(n_contents)
            
            # Add competitors with specified strength range
            coalition_ids = []
            content_strengths = {}
            
            for i, content in enumerate(contents):
                strength = np.random.uniform(*strength_range)
                coalition_id = workspace.add_competitor(
                    content['id'], content['features'], strength, content
                )
                coalition_ids.append(coalition_id)
                content_strengths[coalition_id] = strength
            
            # Run competition
            winner = workspace.compete_for_access()
            
            # Validate competition probabilities
            validation = self.validator.validate_competition_probabilities(
                content_strengths,
                workspace.access_probabilities,
                self.config.competition_temperature
            )
            
            # Calculate competition metrics
            prob_values = list(workspace.access_probabilities.values())
            competition_entropy = entropy(prob_values) if len(prob_values) > 1 else 0.0
            max_prob = max(prob_values) if prob_values else 0.0
            
            results[case_name] = {
                'n_contents': n_contents,
                'validation': validation,
                'competition_entropy': competition_entropy,
                'max_probability': max_prob,
                'winner': winner,
                'winner_probability': workspace.access_probabilities.get(winner, 0.0) if winner else 0.0,
                'total_strength': sum(content_strengths.values())
            }
            
            # Assert validations pass
            for check, passed in validation.items():
                assert passed, f"Competition validation failed for {case_name}: {check}"
            
            # Clear for next test
            workspace.clear_competitors()
        
        logger.info("✓ Competition probability calculation is mathematically sound")
        return results
    
    def test_competition_ordering_consistency(self):
        """Test that competition results are consistent with strength ordering."""
        logger.info("Testing competition ordering consistency")
        
        workspace = MockGlobalWorkspace(self.config)
        
        # Create contents with clear strength ordering
        n_trials = 50
        ordering_consistency = 0
        
        for trial in range(n_trials):
            # Generate contents with predetermined ordering
            strengths = [2.0, 1.5, 1.0, 0.5, 0.2]  # Clear ordering
            contents = self.content_generator.generate_content_batch(len(strengths))
            
            coalition_ids = []
            for i, (content, strength) in enumerate(zip(contents, strengths)):
                coalition_id = workspace.add_competitor(
                    content['id'], content['features'], strength, content
                )
                coalition_ids.append(coalition_id)
            
            # Run multiple competitions
            winner_counts = defaultdict(int)
            n_competitions = 20
            
            for _ in range(n_competitions):
                winner = workspace.compete_for_access()
                if winner:
                    winner_counts[winner] += 1
            
            # Check if strongest competitor wins most often
            if winner_counts:
                most_frequent_winner = max(winner_counts.items(), key=lambda x: x[1])[0]
                strongest_coalition = coalition_ids[0]  # First has highest strength
                
                if most_frequent_winner == strongest_coalition:
                    ordering_consistency += 1
            
            workspace.clear_competitors()
        
        consistency_rate = ordering_consistency / n_trials
        
        # Should be consistent most of the time (allowing for stochasticity)
        assert consistency_rate > 0.6, f"Competition ordering consistency too low: {consistency_rate:.2%}"
        
        logger.info(f"✓ Competition ordering consistency: {consistency_rate:.2%}")
        return {'consistency_rate': consistency_rate, 'n_trials': n_trials}
    
    def test_competition_temperature_effects(self):
        """Test effects of competition temperature on probability distribution."""
        logger.info("Testing competition temperature effects")
        
        # Test different temperatures
        temperatures = [0.1, 0.5, 1.0, 2.0, 5.0]
        
        results = {}
        
        for temp in temperatures:
            # Create temporary config with specific temperature
            temp_config = GlobalWorkspaceTestConfig()
            temp_config.competition_temperature = temp
            
            workspace = MockGlobalWorkspace(temp_config)
            
            # Add diverse competitors
            contents = self.content_generator.generate_content_batch(5)
            strengths = [2.0, 1.5, 1.0, 0.8, 0.3]  # Diverse strengths
            
            coalition_ids = []
            for content, strength in zip(contents, strengths):
                coalition_id = workspace.add_competitor(
                    content['id'], content['features'], strength, content
                )
                coalition_ids.append(coalition_id)
            
            # Run competition
            winner = workspace.compete_for_access()
            
            # Analyze probability distribution
            prob_values = list(workspace.access_probabilities.values())
            prob_entropy = entropy(prob_values)
            concentration_ratio = max(prob_values) / sum(prob_values) if sum(prob_values) > 0 else 0
            
            results[temp] = {
                'temperature': temp,
                'entropy': prob_entropy,
                'concentration_ratio': concentration_ratio,
                'probabilities': prob_values,
                'winner': winner
            }
            
            workspace.clear_competitors()
        
        # Validate temperature effects
        entropies = [results[temp]['entropy'] for temp in temperatures]
        concentrations = [results[temp]['concentration_ratio'] for temp in temperatures]
        
        # Lower temperature should lead to lower entropy (more concentrated)
        assert entropies[0] < entropies[-1], "Low temperature should produce lower entropy"
        assert concentrations[0] > concentrations[-1], "Low temperature should produce higher concentration"
        
        # Check general trend (allowing some variation)
        entropy_increases = sum(
            1 for i in range(len(entropies) - 1) 
            if entropies[i + 1] >= entropies[i] - 0.1  # Small tolerance
        )
        
        trend_consistency = entropy_increases / (len(entropies) - 1)
        assert trend_consistency > 0.6, f"Temperature-entropy trend inconsistent: {trend_consistency:.2%}"
        
        logger.info("✓ Competition temperature effects are working correctly")
        return results
    
    def test_competition_performance_scaling(self):
        """Test competition performance with varying numbers of competitors."""
        logger.info("Testing competition performance scaling")
        
        workspace = MockGlobalWorkspace(self.config)
        
        competitor_counts = [2, 5, 10, 20, 50, 100]
        results = {}
        
        for n_competitors in competitor_counts:
            # Generate competitors
            contents = self.content_generator.generate_content_batch(n_competitors)
            
            # Add competitors
            start_time = time.time()
            
            coalition_ids = []
            for content in contents:
                strength = np.random.uniform(0.5, 2.0)
                coalition_id = workspace.add_competitor(
                    content['id'], content['features'], strength, content
                )
                coalition_ids.append(coalition_id)
            
            addition_time = time.time() - start_time
            
            # Run competition
            start_time = time.time()
            winner = workspace.compete_for_access()
            competition_time = time.time() - start_time
            
            # Calculate metrics
            results[n_competitors] = {
                'n_competitors': n_competitors,
                'addition_time': addition_time,
                'competition_time': competition_time,
                'competitors_per_second_addition': n_competitors / addition_time,
                'competitions_per_second': 1.0 / competition_time,
                'winner': winner is not None,
                'memory_usage': self._estimate_memory_usage(workspace)
            }
            
            workspace.clear_competitors()
        
        # Validate performance requirements
        competition_rate_50 = results[50]['competitions_per_second']
        assert competition_rate_50 >= self.config.min_competitions_per_second, \
            f"Competition rate too slow: {competition_rate_50:.1f} < {self.config.min_competitions_per_second}"
        
        # Check scaling behavior
        times = [results[n]['competition_time'] for n in competitor_counts]
        max_time = max(times)
        assert max_time < 1.0, f"Competition time too slow for large groups: {max_time:.3f}s"
        
        logger.info("✓ Competition performance scales appropriately")
        return results
    
    def _estimate_memory_usage(self, workspace: MockGlobalWorkspace) -> float:
        """Estimate memory usage of workspace in MB."""
        import sys
        
        def get_size(obj, seen=None):
            size = sys.getsizeof(obj)
            if seen is None:
                seen = set()
            
            obj_id = id(obj)
            if obj_id in seen:
                return 0
            
            seen.add(obj_id)
            
            if isinstance(obj, dict):
                size += sum([get_size(v, seen) for v in obj.values()])
                size += sum([get_size(k, seen) for k in obj.keys()])
            elif hasattr(obj, '__dict__'):
                size += get_size(obj.__dict__, seen)
            elif hasattr(obj, '__iter__') and not isinstance(obj, (str, bytes, bytearray)):
                try:
                    size += sum([get_size(i, seen) for i in obj])
                except:
                    pass
            
            return size
        
        return get_size(workspace) / (1024 * 1024)

class TestCoalitionFormation:
    """Comprehensive tests for coalition formation mechanisms."""
    
    def __init__(self, config: GlobalWorkspaceTestConfig):
        self.config = config
        self.validator = GlobalWorkspaceMathValidator()
        self.content_generator = ContentGenerator(config)
        
    def test_synergy_calculation(self):
        """Test synergy calculation between contents."""
        logger.info("Testing synergy calculation")
        
        workspace = MockGlobalWorkspace(self.config)
        
        # Test different types of content pairs
        synergy_test_cases = [
            {'type1': 'visual', 'type2': 'visual', 'expected_synergy': 'high'},
            {'type1': 'visual', 'type2': 'auditory', 'expected_synergy': 'medium'},
            {'type1': 'semantic', 'type2': 'semantic', 'expected_synergy': 'high'},
            {'type1': 'motor', 'type2': 'emotional', 'expected_synergy': 'low'},
        ]
        
        results = {}
        
        for case in synergy_test_cases:
            type1, type2 = case['type1'], case['type2']
            expected = case['expected_synergy']
            
            # Generate content pairs
            content1 = self.content_generator._generate_single_content(0, type1)
            content2 = self.content_generator._generate_single_content(1, type2)
            
            # Calculate synergy using content generator
            synergy = self.content_generator.calculate_synergy([content1, content2])
            
            results[f"{type1}_{type2}"] = {
                'synergy_score': synergy,
                'expected_level': expected,
                'content_types': (type1, type2)
            }
        
        # Validate synergy patterns
        same_type_synergies = [
            results['visual_visual']['synergy_score'],
            results['semantic_semantic']['synergy_score']
        ]
        
        cross_type_synergies = [
            results['visual_auditory']['synergy_score'],
            results['motor_emotional']['synergy_score']
        ]
        
        # Same-type should generally have higher synergy
        avg_same_type = np.mean(same_type_synergies)
        avg_cross_type = np.mean(cross_type_synergies)
        
        # Allow some flexibility due to randomness in generation
        assert avg_same_type >= avg_cross_type - 0.2, \
            f"Same-type synergy should be higher: {avg_same_type:.3f} vs {avg_cross_type:.3f}"
        
        logger.info("✓ Synergy calculation produces reasonable patterns")
        return results
    
    def test_coalition_formation_algorithm(self):
        """Test coalition formation algorithm."""
        logger.info("Testing coalition formation algorithm")
        
        workspace = MockGlobalWorkspace(self.config)
        
        # Create diverse contents for coalition formation
        n_contents = 12
        contents = self.content_generator.generate_content_batch(n_contents, ['visual', 'auditory', 'semantic'])
        
        # Add all as competitors
        coalition_ids = []
        individual_contents = []
        
        for content in contents:
            strength = np.random.uniform(0.5, 1.5)
            coalition_id = workspace.add_competitor(
                content['id'], content['features'], strength, content
            )
            coalition_ids.append(coalition_id)
            individual_contents.append({
                'id': content['id'],
                'strength': strength,
                'features': content['features']
            })
        
        ## Form coalitions
        initial_count = len(workspace.competing_coalitions)
        formed_coalitions = workspace.form_coalitions()
        final_count = len(formed_coalitions)
        
        # Validate coalition formation
        validation = self.validator.validate_coalition_formation(
            individual_contents, list(formed_coalitions.values()), self.config.synergy_threshold
        )
        
        # Analyze coalition properties
        coalition_sizes = [len(coalition['members']) for coalition in formed_coalitions.values()]
        avg_coalition_size = np.mean(coalition_sizes) if coalition_sizes else 1.0
        max_coalition_size = max(coalition_sizes) if coalition_sizes else 1
        
        # Check synergy scores
        synergy_scores = [coalition['synergy_score'] for coalition in formed_coalitions.values()]
        avg_synergy = np.mean(synergy_scores) if synergy_scores else 0.0
        
        results = {
            'initial_competitors': initial_count,
            'final_coalitions': final_count,
            'validation': validation,
            'avg_coalition_size': avg_coalition_size,
            'max_coalition_size': max_coalition_size,
            'avg_synergy_score': avg_synergy,
            'coalition_reduction_rate': (initial_count - final_count) / initial_count if initial_count > 0 else 0.0
        }
        
        # Assert validations pass
        for check, passed in validation.items():
            assert passed, f"Coalition formation validation failed: {check}"
        
        # Validate formation efficiency
        assert final_count <= initial_count, "Coalition formation should not increase competitor count"
        assert avg_synergy >= self.config.synergy_threshold, f"Average synergy too low: {avg_synergy:.3f}"
        assert max_coalition_size <= self.config.max_coalition_size, f"Coalition too large: {max_coalition_size}"
        
        workspace.clear_competitors()
        
        logger.info("✓ Coalition formation algorithm working correctly")
        return results
    
    def test_coalition_stability(self):
        """Test stability of formed coalitions over time."""
        logger.info("Testing coalition stability")
        
        workspace = MockGlobalWorkspace(self.config)
        
        # Create contents with stable synergy patterns
        stable_contents = []
        for i in range(8):
            # Create paired contents that should form stable coalitions
            if i % 2 == 0:
                content_type = 'visual'
                base_features = np.random.randn(self.config.content_feature_dim) * 0.5
            else:
                content_type = 'visual'  # Same type for high synergy
                # Similar features to previous content
                base_features = stable_contents[-1]['features'] * 0.8 + np.random.randn(self.config.content_feature_dim) * 0.2
            
            content = {
                'id': f"stable_content_{i}",
                'type': content_type,
                'features': base_features,
                'strength': np.random.uniform(0.8, 1.2)
            }
            stable_contents.append(content)
        
        # Test coalition formation stability over multiple iterations
        n_iterations = 10
        coalition_configurations = []
        
        for iteration in range(n_iterations):
            # Add competitors
            coalition_ids = []
            for content in stable_contents:
                # Add small noise to simulate temporal variation
                noisy_features = content['features'] + np.random.randn(self.config.content_feature_dim) * 0.05
                coalition_id = workspace.add_competitor(
                    content['id'], noisy_features, content['strength'], content
                )
                coalition_ids.append(coalition_id)
            
            # Form coalitions
            formed_coalitions = workspace.form_coalitions()
            
            # Record coalition configuration
            config_signature = []
            for coalition in formed_coalitions.values():
                member_set = frozenset(coalition['members'])
                config_signature.append(member_set)
            
            coalition_configurations.append(frozenset(config_signature))
            workspace.clear_competitors()
        
        # Analyze stability
        unique_configurations = len(set(coalition_configurations))
        most_common_config = max(set(coalition_configurations), key=coalition_configurations.count)
        stability_frequency = coalition_configurations.count(most_common_config) / n_iterations
        
        results = {
            'n_iterations': n_iterations,
            'unique_configurations': unique_configurations,
            'stability_frequency': stability_frequency,
            'configuration_variance': unique_configurations / n_iterations
        }
        
        # Coalition formation should be reasonably stable
        assert stability_frequency >= self.config.coalition_stability_threshold, \
            f"Coalition stability too low: {stability_frequency:.2%}"
        
        logger.info(f"✓ Coalition stability: {stability_frequency:.2%} consistency")
        return results
    
    def test_coalition_size_distribution(self):
        """Test distribution of coalition sizes."""
        logger.info("Testing coalition size distribution")
        
        workspace = MockGlobalWorkspace(self.config)
        
        # Generate large number of contents for statistical analysis
        n_contents = 50
        contents = self.content_generator.generate_content_batch(n_contents)
        
        # Add all as competitors
        for content in contents:
            strength = np.random.uniform(0.5, 1.5)
            workspace.add_competitor(content['id'], content['features'], strength, content)
        
        # Form coalitions
        formed_coalitions = workspace.form_coalitions()
        
        # Analyze size distribution
        coalition_sizes = [len(coalition['members']) for coalition in formed_coalitions.values()]
        size_distribution = Counter(coalition_sizes)
        
        # Calculate statistics
        mean_size = np.mean(coalition_sizes)
        std_size = np.std(coalition_sizes)
        max_size = max(coalition_sizes)
        min_size = min(coalition_sizes)
        
        # Analyze size preferences
        single_member_fraction = size_distribution.get(1, 0) / len(coalition_sizes)
        large_coalition_fraction = sum(
            count for size, count in size_distribution.items() 
            if size >= 4
        ) / len(coalition_sizes)
        
        results = {
            'size_distribution': dict(size_distribution),
            'mean_size': mean_size,
            'std_size': std_size,
            'max_size': max_size,
            'min_size': min_size,
            'single_member_fraction': single_member_fraction,
            'large_coalition_fraction': large_coalition_fraction,
            'total_coalitions': len(formed_coalitions),
            'original_contents': n_contents
        }
        
        # Validate reasonable distribution
        assert max_size <= self.config.max_coalition_size, f"Coalition too large: {max_size}"
        assert min_size >= 1, f"Invalid coalition size: {min_size}"
        assert mean_size >= 1.0, f"Invalid mean coalition size: {mean_size}"
        
        # Should have some variety in coalition sizes
        assert len(size_distribution) > 1, "Should have variety in coalition sizes"
        
        workspace.clear_competitors()
        
        logger.info("✓ Coalition size distribution is reasonable")
        return results
    
    def test_coalition_performance_optimization(self):
        """Test performance optimization in coalition formation."""
        logger.info("Testing coalition formation performance optimization")
        
        workspace = MockGlobalWorkspace(self.config)
        
        # Test with varying numbers of contents
        content_counts = [5, 10, 20, 50, 100]
        results = {}
        
        for n_contents in content_counts:
            contents = self.content_generator.generate_content_batch(n_contents)
            
            # Add competitors
            start_time = time.time()
            for content in contents:
                strength = np.random.uniform(0.5, 1.5)
                workspace.add_competitor(content['id'], content['features'], strength, content)
            addition_time = time.time() - start_time
            
            # Form coalitions
            start_time = time.time()
            formed_coalitions = workspace.form_coalitions()
            formation_time = time.time() - start_time
            
            results[n_contents] = {
                'n_contents': n_contents,
                'addition_time': addition_time,
                'formation_time': formation_time,
                'contents_per_second': n_contents / addition_time,
                'formation_rate': len(formed_coalitions) / formation_time,
                'n_coalitions': len(formed_coalitions),
                'memory_usage': self._estimate_memory_usage(workspace)
            }
            
            workspace.clear_competitors()
        
        # Validate performance requirements
        formation_time_100 = results[100]['formation_time']
        assert formation_time_100 <= self.config.max_coalition_formation_time, \
            f"Coalition formation too slow: {formation_time_100:.3f}s > {self.config.max_coalition_formation_time}s"
        
        # Check scaling behavior
        formation_times = [results[n]['formation_time'] for n in content_counts]
        
        # Should scale reasonably (not exponentially)
        time_ratio = formation_times[-1] / formation_times[0]
        content_ratio = content_counts[-1] / content_counts[0]
        scaling_efficiency = time_ratio / content_ratio
        
        assert scaling_efficiency < 5.0, f"Coalition formation scaling inefficient: {scaling_efficiency:.2f}"
        
        logger.info("✓ Coalition formation performance is acceptable")
        return results
    
    def _estimate_memory_usage(self, workspace: MockGlobalWorkspace) -> float:
        """Estimate memory usage in MB."""
        import sys
        
        total_size = 0
        total_size += sys.getsizeof(workspace.competing_coalitions)
        total_size += sys.getsizeof(workspace.workspace_contents)
        total_size += sys.getsizeof(workspace.coalition_cache)
        
        # Estimate content of dictionaries
        for coalition in workspace.competing_coalitions.values():
            total_size += sys.getsizeof(coalition)
            total_size += sys.getsizeof(coalition.get('features', []))
        
        return total_size / (1024 * 1024)

class TestBroadcastMechanisms:
    """Comprehensive tests for broadcast mechanisms."""
    
    def __init__(self, config: GlobalWorkspaceTestConfig):
        self.config = config
        self.validator = GlobalWorkspaceMathValidator()
        self.content_generator = ContentGenerator(config)
        
    def test_broadcast_influence_propagation(self):
        """Test broadcast influence propagation to targets."""
        logger.info("Testing broadcast influence propagation")
        
        workspace = MockGlobalWorkspace(self.config)
        
        # Register multiple broadcast targets
        target_ids = ['visual_cortex', 'auditory_cortex', 'motor_cortex', 'prefrontal_cortex', 'memory_system']
        for target_id in target_ids:
            workspace.register_broadcast_target(target_id)
        
        # Create test content
        content = self.content_generator.generate_content_batch(1)[0]
        coalition_id = workspace.add_competitor(content['id'], content['features'], 1.5, content)
        
        # Simulate winning competition
        workspace.access_probabilities[coalition_id] = 0.9
        
        # Record initial influence state
        initial_influences = {target: 0.0 for target in target_ids}
        initial_influence_matrix = workspace.influence_matrix.copy()
        
        # Broadcast to workspace
        workspace.broadcast_to_workspace(coalition_id)
        
        # Check broadcast effects
        broadcast_record = workspace.broadcast_history[-1] if workspace.broadcast_history else None
        workspace_entry = workspace.workspace_contents.get(coalition_id)
        
        # Validate broadcast record
        assert broadcast_record is not None, "Broadcast should be recorded"
        assert broadcast_record['coalition_id'] == coalition_id, "Broadcast record should match coalition"
        assert broadcast_record['total_influence'] > 0, "Broadcast should have measurable influence"
        
        # Validate workspace entry
        assert workspace_entry is not None, "Content should be in workspace"
        assert workspace_entry['influence_propagated'] > 0, "Influence should be propagated"
        
        # Check influence matrix changes
        influence_change = np.linalg.norm(workspace.influence_matrix - initial_influence_matrix)
        assert influence_change > 0, "Influence matrix should change after broadcast"
        
        # Validate broadcast dynamics
        propagation_rates = {target: 0.1 for target in target_ids}  # Simplified
        updated_influences = {target: initial_influences[target] + broadcast_record['total_influence'] * 0.1 
                            for target in target_ids}
        
        validation = self.validator.validate_broadcast_dynamics(
            initial_influences, updated_influences, 
            {coalition_id: workspace_entry['strength']}, propagation_rates
        )
        
        results = {
            'broadcast_record': broadcast_record,
            'workspace_entry': workspace_entry,
            'influence_change': influence_change,
            'validation': validation,
            'n_targets': len(target_ids),
            'total_influence': broadcast_record['total_influence']
        }
        
        # Assert validations pass
        for check, passed in validation.items():
            assert passed, f"Broadcast dynamics validation failed: {check}"
        
        logger.info("✓ Broadcast influence propagation working correctly")
        return results
    
    def test_broadcast_strength_prediction(self):
        """Test neural network broadcast strength prediction."""
        logger.info("Testing broadcast strength prediction")
        
        workspace = MockGlobalWorkspace(self.config)
        
        # Test with different content types and strengths
        test_cases = [
            {'type': 'visual', 'strength': 2.0, 'expected': 'high'},
            {'type': 'auditory', 'strength': 1.0, 'expected': 'medium'},
            {'type': 'motor', 'strength': 0.5, 'expected': 'low'},
            {'type': 'semantic', 'strength': 1.8, 'expected': 'high'},
        ]
        
        results = {}
        
        for case in test_cases:
            content_type = case['type']
            strength = case['strength']
            expected_level = case['expected']
            
            # Generate content
            content = self.content_generator._generate_single_content(0, content_type)
            coalition_id = workspace.add_competitor(content['id'], content['features'], strength, content)
            
            # Predict broadcast effects
            coalition_data = workspace.competing_coalitions[coalition_id]
            predictions = workspace._predict_broadcast_effects(coalition_data)
            
            predicted_strength = float(predictions['broadcast_strength'])
            predicted_decay = float(predictions['decay_rate'])
            
            results[f"{content_type}_{strength}"] = {
                'content_type': content_type,
                'input_strength': strength,
                'predicted_broadcast_strength': predicted_strength,
                'predicted_decay_rate': predicted_decay,
                'expected_level': expected_level
            }
            
            workspace.clear_competitors()
        
        # Validate prediction patterns
        high_strength_cases = [r for r in results.values() if r['expected_level'] == 'high']
        low_strength_cases = [r for r in results.values() if r['expected_level'] == 'low']
        
        if high_strength_cases and low_strength_cases:
            avg_high_prediction = np.mean([r['predicted_broadcast_strength'] for r in high_strength_cases])
            avg_low_prediction = np.mean([r['predicted_broadcast_strength'] for r in low_strength_cases])
            
            # High strength should generally predict higher broadcast strength
            assert avg_high_prediction >= avg_low_prediction - 0.1, \
                f"High strength prediction should be higher: {avg_high_prediction:.3f} vs {avg_low_prediction:.3f}"
        
        # Validate prediction ranges
        all_predictions = [r['predicted_broadcast_strength'] for r in results.values()]
        all_decays = [r['predicted_decay_rate'] for r in results.values()]
        
        assert all(0.0 <= p <= 1.0 for p in all_predictions), "Broadcast strength predictions should be in [0,1]"
        assert all(0.0 <= d <= 1.0 for d in all_decays), "Decay rate predictions should be in [0,1]"
        
        logger.info("✓ Broadcast strength prediction working correctly")
        return results
    
    def test_broadcast_target_routing(self):
        """Test attention-based broadcast target routing."""
        logger.info("Testing broadcast target routing")
        
        workspace = MockGlobalWorkspace(self.config)
        
        # Register targets with different characteristics
        targets = {
            'visual_system': {'receptivity': 0.9, 'capacity': 0.8},
            'memory_system': {'receptivity': 0.7, 'capacity': 0.9},
            'motor_system': {'receptivity': 0.6, 'capacity': 0.7},
            'emotional_system': {'receptivity': 0.8, 'capacity': 0.6}
        }
        
        for target_id in targets.keys():
            workspace.register_broadcast_target(target_id)
        
        # Test routing with different content types
        content_types = ['visual', 'semantic', 'motor', 'emotional']
        routing_results = {}
        
        for content_type in content_types:
            content = self.content_generator._generate_single_content(0, content_type)
            coalition_id = workspace.add_competitor(content['id'], content['features'], 1.2, content)
            
            # Broadcast and analyze routing
            workspace.broadcast_to_workspace(coalition_id)
            
            broadcast_record = workspace.broadcast_history[-1]
            target_influences = broadcast_record['target_influences']
            
            # Find most influenced target
            max_influence_idx = np.argmax(target_influences[:len(targets)])
            most_influenced_target = list(targets.keys())[max_influence_idx]
            
            routing_results[content_type] = {
                'most_influenced_target': most_influenced_target,
                'influence_distribution': target_influences.tolist(),
                'total_influence': broadcast_record['total_influence'],
                'influence_variance': np.var(target_influences[:len(targets)])
            }
            
            workspace.clear_competitors()
            workspace.workspace_contents.clear()  # Clear workspace for next test
        
        # Validate routing intelligence
        # Visual content should influence visual system most
        visual_routing = routing_results.get('visual', {})
        if visual_routing:
            # Allow some flexibility in routing
            visual_influenced = visual_routing['most_influenced_target']
            # Just check that routing is happening (some target gets more influence)
            assert visual_routing['influence_variance'] > 0, "Should have varied influence distribution"
        
        # Check that different content types produce different routing patterns
        routing_patterns = [r['influence_distribution'][:len(targets)] for r in routing_results.values()]
        if len(routing_patterns) > 1:
            pattern_differences = []
            for i in range(len(routing_patterns)):
                for j in range(i + 1, len(routing_patterns)):
                    diff = euclidean_distance(routing_patterns[i], routing_patterns[j])
                    pattern_differences.append(diff)
            
            avg_pattern_difference = np.mean(pattern_differences)
            assert avg_pattern_difference > 0.01, f"Routing patterns should differ: {avg_pattern_difference:.4f}"
        
        logger.info("✓ Broadcast target routing working correctly")
        return routing_results
    
    def test_broadcast_efficiency_metrics(self):
        """Test broadcast efficiency measurement."""
        logger.info("Testing broadcast efficiency metrics")
        
        workspace = MockGlobalWorkspace(self.config)
        
        # Register targets
        for i in range(5):
            workspace.register_broadcast_target(f"target_{i}")
        
        # Simulate multiple broadcasts with varying success
        n_broadcasts = 20
        broadcast_scenarios = []
        
        for i in range(n_broadcasts):
            # Generate content with varying broadcast potential
            content = self.content_generator.generate_content_batch(1)[0]
            
            # Vary strength to create different broadcast scenarios
            if i < 5:
                strength = 2.0  # High strength - should broadcast well
            elif i < 10:
                strength = 1.0  # Medium strength
            elif i < 15:
                strength = 0.5  # Low strength
            else:
                strength = 0.2  # Very low strength
            
            coalition_id = workspace.add_competitor(content['id'], content['features'], strength, content)
            workspace.broadcast_to_workspace(coalition_id)
            
            broadcast_record = workspace.broadcast_history[-1]
            
            broadcast_scenarios.append({
                'strength': strength,
                'total_influence': broadcast_record['total_influence'],
                'broadcast_strength': broadcast_record['broadcast_strength'],
                'successful': broadcast_record['total_influence'] > 0.1  # Threshold for success
            })
            
            workspace.clear_competitors()
        
        # Calculate efficiency metrics
        successful_broadcasts = sum(1 for s in broadcast_scenarios if s['successful'])
        efficiency_rate = successful_broadcasts / n_broadcasts
        
        # Analyze strength-influence correlation
        strengths = [s['strength'] for s in broadcast_scenarios]
        influences = [s['total_influence'] for s in broadcast_scenarios]
        
        if len(strengths) > 1 and np.std(strengths) > 0:
            correlation = np.corrcoef(strengths, influences)[0, 1]
        else:
            correlation = 0.0
        
        # Calculate workspace efficiency using validator
        workspace_history = list(workspace.workspace_history)
        efficiency_metrics = self.validator.compute_workspace_metrics(workspace_history)
        
        results = {
            'n_broadcasts': n_broadcasts,
            'successful_broadcasts': successful_broadcasts,
            'efficiency_rate': efficiency_rate,
            'strength_influence_correlation': correlation,
            'workspace_metrics': efficiency_metrics,
            'scenarios': broadcast_scenarios
        }
        
        # Validate efficiency
        assert efficiency_rate >= self.config.broadcast_efficiency_threshold, \
            f"Broadcast efficiency too low: {efficiency_rate:.2%}"
        
        # Strong contents should generally have more influence
        assert correlation > 0.3, f"Strength-influence correlation too weak: {correlation:.3f}"
        
        logger.info(f"✓ Broadcast efficiency: {efficiency_rate:.2%}")
        return results

class TestWorkspaceDynamics:
    """Comprehensive tests for workspace temporal dynamics."""
    
    def __init__(self, config: GlobalWorkspaceTestConfig):
        self.config = config
        self.validator = GlobalWorkspaceMathValidator()
        self.content_generator = ContentGenerator(config)
        
    def test_workspace_decay_dynamics(self):
        """Test workspace content decay over time."""
        logger.info("Testing workspace decay dynamics")
        
        workspace = MockGlobalWorkspace(self.config)
        
        # Add content to workspace
        content = self.content_generator.generate_content_batch(1)[0]
        coalition_id = workspace.add_competitor(content['id'], content['features'], 1.5, content)
        workspace.broadcast_to_workspace(coalition_id)
        
        # Record initial state
        initial_strength = workspace.workspace_contents[coalition_id]['strength']
        initial_time = time.time()
        
        # Simulate time passing with decay updates
        n_steps = 50
        decay_history = []
        
        for step in range(n_steps):
            # Update dynamics
            workspace.update_workspace_dynamics(dt=1.0)
            
            # Record state
            if coalition_id in workspace.workspace_contents:
                current_strength = workspace.workspace_contents[coalition_id]['strength']
                decay_history.append({
                    'step': step,
                    'strength': current_strength,
                    'decay_ratio': current_strength / initial_strength,
                    'time_elapsed': step * 1.0
                })
            else:
                # Content was removed due to decay
                decay_history.append({
                    'step': step,
                    'strength': 0.0,
                    'decay_ratio': 0.0,
                    'time_elapsed': step * 1.0
                })
                break
        
        # Analyze decay pattern
        decay_ratios = [h['decay_ratio'] for h in decay_history]
        
        # Validate decay behavior
        assert decay_ratios[0] == 1.0, "Initial decay ratio should be 1.0"
        assert decay_ratios[-1] < decay_ratios[0], "Strength should decay over time"
        
        # Check exponential decay pattern
        if len(decay_ratios) > 5:
            # Fit exponential decay
            times = [h['time_elapsed'] for h in decay_history]
            
            # Check that decay is roughly exponential
            log_ratios = [np.log(max(r, 1e-10)) for r in decay_ratios]
            
            # Linear fit to log ratios should have negative slope
            if len(log_ratios) > 2:
                slope = (log_ratios[-1] - log_ratios[0]) / (times[-1] - times[0]) if times[-1] > times[0] else 0
                assert slope < 0, f"Decay should be exponential (negative slope): {slope:.4f}"
        
        # Validate workspace history
        workspace_states = list(workspace.workspace_history)[-n_steps:]
        
        if len(workspace_states) > 1:
            validation = self.validator.validate_workspace_dynamics(
                workspace_states, self.config.broadcast_decay
            )
            
            # Assert validations pass
            for check, passed in validation.items():
                assert passed, f"Workspace dynamics validation failed: {check}"
        
        results = {
            'initial_strength': initial_strength,
            'decay_history': decay_history,
            'final_strength': decay_history[-1]['strength'],
            'decay_steps': len(decay_history),
            'exponential_decay_slope': slope if 'slope' in locals() else None
        }
        
        logger.info("✓ Workspace decay dynamics working correctly")
        return results
    
    def test_workspace_capacity_management(self):
        """Test workspace capacity management and eviction."""
        logger.info("Testing workspace capacity management")
        
        workspace = MockGlobalWorkspace(self.config)
        
        # Generate more contents than workspace capacity
        n_contents = self.config.workspace_capacity + 5
        contents = self.content_generator.generate_content_batch(n_contents)
        
        # Add contents one by one and observe capacity management
        addition_order = []
        eviction_events = []
        
        for i, content in enumerate(contents):
            coalition_id = workspace.add_competitor(content['id'], content['features'], 
                                                  np.random.uniform(0.8, 1.5), content)
            
            # Broadcast to workspace
            workspace.broadcast_to_workspace(coalition_id)
            addition_order.append(coalition_id)
            
            # Check if eviction occurred
            current_contents = list(workspace.workspace_contents.keys())
            
            if len(current_contents) > self.config.workspace_capacity:
                # Capacity exceeded - this shouldn't happen with proper management
                assert False, f"Workspace capacity exceeded: {len(current_contents)} > {self.config.workspace_capacity}"
            
            # Record eviction if previous content was removed
            if i > 0:
                for prev_id in addition_order[:-1]:
                    if prev_id not in current_contents:
                        eviction_events.append({
                            'evicted_id': prev_id,
                            'step': i,
                            'workspace_size': len(current_contents)
                        })
            
            workspace.clear_competitors()
        
        # Analyze capacity management
        final_workspace_size = len(workspace.workspace_contents)
        n_evictions = len(eviction_events)
        
        results = {
            'n_contents_added': n_contents,
            'final_workspace_size': final_workspace_size,
            'n_evictions': n_evictions,
            'workspace_capacity': self.config.workspace_capacity,
            'eviction_events': eviction_events,
            'capacity_respected': final_workspace_size <= self.config.workspace_capacity
        }
        
        # Validate capacity management
        assert final_workspace_size <= self.config.workspace_capacity, \
            f"Final workspace size exceeds capacity: {final_workspace_size} > {self.config.workspace_capacity}"
        
        # Should have evictions when capacity is exceeded
        expected_evictions = max(0, n_contents - self.config.workspace_capacity)
        assert n_evictions >= expected_evictions * 0.8, \
            f"Not enough evictions: {n_evictions} < {expected_evictions * 0.8}"
        
        logger.info("✓ Workspace capacity management working correctly")
        return results
    
    def test_adaptive_capacity_adjustment(self):
        """Test adaptive capacity adjustment based on performance."""
        logger.info("Testing adaptive capacity adjustment")
        
        if not self.config.enable_adaptive_capacity:
            logger.info("Adaptive capacity disabled, skipping test")
            return {'skipped': True}
        
        workspace = MockGlobalWorkspace(self.config)
        
        # Simulate high performance scenario
        high_performance_contents = []
        for i in range(10):
            content = self.content_generator._generate_single_content(i, 'semantic')
            content['strength'] = np.random.uniform(1.5, 2.0)  # High strength
            high_performance_contents.append(content)
        
        initial_capacity = workspace._get_current_capacity()
        
        # Add high-performance contents
        for content in high_performance_contents:
            coalition_id = workspace.add_competitor(content['id'], content['features'], 
                                                  content['strength'], content)
            workspace.broadcast_to_workspace(coalition_id)
            
            # Record high broadcast strength
            workspace.performance_metrics['broadcast_strength'].append(0.9)
            
            workspace.clear_competitors()
        
        # Check if capacity increased
        high_performance_capacity = workspace._get_current_capacity()
        
        # Reset and simulate low performance scenario
        workspace.performance_metrics['broadcast_strength'].clear()
        
        low_performance_contents = []
        for i in range(10):
            content = self.content_generator._generate_single_content(i, 'motor')
            content['strength'] = np.random.uniform(0.2, 0.4)  # Low strength
            low_performance_contents.append(content)
        
        # Add low-performance contents
        for content in low_performance_contents:
            coalition_id = workspace.add_competitor(content['id'], content['features'], 
                                                  content['strength'], content)
            workspace.broadcast_to_workspace(coalition_id)
            
            # Record low broadcast strength
            workspace.performance_metrics['broadcast_strength'].append(0.3)
            
            workspace.clear_competitors()
        
        low_performance_capacity = workspace._get_current_capacity()
        
        results = {
            'initial_capacity': initial_capacity,
            'high_performance_capacity': high_performance_capacity,
            'low_performance_capacity': low_performance_capacity,
            'capacity_increased_with_performance': high_performance_capacity >= initial_capacity,
            'capacity_decreased_with_poor_performance': low_performance_capacity <= high_performance_capacity
        }
        
        # Validate adaptive behavior
        assert high_performance_capacity >= initial_capacity, \
            "Capacity should increase or stay same with high performance"
        
        logger.info("✓ Adaptive capacity adjustment working correctly")
        return results
    
    def test_temporal_coherence(self):
        """Test temporal coherence of workspace contents."""
        logger.info("Testing temporal coherence")
        
        workspace = MockGlobalWorkspace(self.config)
        
        # Create contents with temporal relationships
        base_content = self.content_generator._generate_single_content(0, 'semantic')
        
        # Generate sequence of related contents
        content_sequence = [base_content]
        for i in range(10):
            # Create content similar to previous one (temporal coherence)
            prev_features = content_sequence[-1]['features']
            coherent_features = prev_features * 0.8 + np.random.randn(self.config.content_feature_dim) * 0.2
            
            coherent_content = {
                'id': f"coherent_content_{i}",
                'type': 'semantic',
                'features': coherent_features,
                'strength': np.random.uniform(0.8, 1.2)
            }
            content_sequence.append(coherent_content)
        
        # Add contents to workspace over time
        coherence_measurements = []
        
        for i, content in enumerate(content_sequence):
            coalition_id = workspace.add_competitor(content['id'], content['features'], 
                                                  content['strength'], content)
            workspace.broadcast_to_workspace(coalition_id)
            
            # Measure coherence with previous workspace contents
            if len(workspace.workspace_contents) > 1:
                current_features = []
                for entry in workspace.workspace_contents.values():
                    current_features.append(entry['features'])
                
                # Calculate pairwise coherence
                coherence_scores = []
                for j in range(len(current_features)):
                    for k in range(j + 1, len(current_features)):
                        coherence = cosine_similarity(current_features[j], current_features[k])
                        coherence_scores.append(coherence)
                
                avg_coherence = np.mean(coherence_scores) if coherence_scores else 0.0
                coherence_measurements.append(avg_coherence)
            
            workspace.update_workspace_dynamics()
            workspace.clear_competitors()
        
        # Analyze temporal coherence
        if coherence_measurements:
            mean_coherence = np.mean(coherence_measurements)
            coherence_trend = np.polyfit(range(len(coherence_measurements)), coherence_measurements, 1)[0]
        else:
            mean_coherence = 0.0
            coherence_trend = 0.0
        
        results = {
            'coherence_measurements': coherence_measurements,
            'mean_coherence': mean_coherence,
            'coherence_trend': coherence_trend,
            'n_measurements': len(coherence_measurements)
        }
        
        # Validate temporal coherence
        assert mean_coherence >= self.config.temporal_coherence_threshold, \
            f"Temporal coherence too low: {mean_coherence:.3f}"
        
        logger.info(f"✓ Temporal coherence: {mean_coherence:.3f}")
        return results

class TestPerformanceBenchmarks:
    """Performance benchmarking tests for global workspace."""
    
    def __init__(self, config: GlobalWorkspaceTestConfig):
        self.config = config
        self.content_generator = ContentGenerator(config)
        
    def test_competition_performance(self):
        """Benchmark competition performance."""
        logger.info("Benchmarking competition performance")
        
        workspace = MockGlobalWorkspace(self.config)
        
        # Test with different numbers of competitors
        competitor_counts = [5, 10, 20, 50, 100]
        results = {}
        
        for n_competitors in competitor_counts:
            contents = self.content_generator.generate_content_batch(n_competitors)
            
            # Measure addition time
            start_time = time.time()
            coalition_ids = []
            for content in contents:
                strength = np.random.uniform(0.5, 1.5)
                coalition_id = workspace.add_competitor(content['id'], content['features'], strength, content)
                coalition_ids.append(coalition_id)
            addition_time = time.time() - start_time
            
            # Measure competition time
            start_time = time.time()
            n_competitions = self.config.n_benchmark_iterations
            winners = []
            
            for _ in range(n_competitions):
                winner = workspace.compete_for_access()
                winners.append(winner)
            
            competition_time = time.time() - start_time
            
            results[n_competitors] = {
                'n_competitors': n_competitors,
                'addition_time': addition_time,
                'competition_time': competition_time,
                'competitions_per_second': n_competitions / competition_time,
                'winners_found': sum(1 for w in winners if w is not None),
                'memory_usage': self._estimate_memory_usage(workspace)
            }
            
            workspace.clear_competitors()
        
        # Validate performance requirements
        competition_rate_50 = results[50]['competitions_per_second']
        assert competition_rate_50 >= self.config.min_competitions_per_second, \
            f"Competition rate too slow: {competition_rate_50:.1f} < {self.config.min_competitions_per_second}"
        
        logger.info("✓ Competition performance meets requirements")
        return results
    
    def test_broadcast_performance(self):
        """Benchmark broadcast performance."""
        logger.info("Benchmarking broadcast performance")
        
        workspace = MockGlobalWorkspace(self.config)
        
        # Register targets
        for i in range(10):
            workspace.register_broadcast_target(f"target_{i}")
        
        # Test broadcast performance with varying content sizes
        n_broadcasts = self.config.n_benchmark_iterations
        
        start_time = time.time()
        
        for i in range(n_broadcasts):
            content = self.content_generator.generate_content_batch(1)[0]
            coalition_id = workspace.add_competitor(content['id'], content['features'], 
                                                  np.random.uniform(0.8, 1.5), content)
            
            workspace.broadcast_to_workspace(coalition_id)
            workspace.clear_competitors()
        
        total_time = time.time() - start_time
        broadcasts_per_second = n_broadcasts / total_time
        
        # Measure memory usage
        memory_usage = self._estimate_memory_usage(workspace)
        
        results = {
            'n_broadcasts': n_broadcasts,
            'total_time': total_time,
            'broadcasts_per_second': broadcasts_per_second,
            'memory_usage_mb': memory_usage,
            'workspace_history_length': len(workspace.workspace_history),
            'broadcast_history_length': len(workspace.broadcast_history)
        }
        
        # Validate performance requirements
        assert broadcasts_per_second >= self.config.min_broadcasts_per_second, \
            f"Broadcast rate too slow: {broadcasts_per_second:.1f} < {self.config.min_broadcasts_per_second}"
        
        assert memory_usage <= self.config.max_memory_usage_mb, \
            f"Memory usage too high: {memory_usage:.1f} MB > {self.config.max_memory_usage_mb} MB"
        
        logger.info(f"✓ Broadcast performance: {broadcasts_per_second:.1f} broadcasts/sec")
        return results
    
    def test_concurrent_access_performance(self):
        """Test performance under concurrent access patterns."""
        logger.info("Testing concurrent access performance")
        
        workspace = MockGlobalWorkspace(self.config)
        
        # Register targets
        for i in range(5):
            workspace.register_broadcast_target(f"target_{i}")
        
        def worker_function(worker_id: int, n_operations: int):
            """Worker function for concurrent testing."""
            worker_results = []
            
            for i in range(n_operations):
                try:
                    # Generate content
                    content = self.content_generator.generate_content_batch(1)[0]
                    
                    # Add competitor
                    start_time = time.time()
                    coalition_id = workspace.add_competitor(
                        f"worker_{worker_id}_content_{i}",
                        content['features'],
                        np.random.uniform(0.5, 1.5),
                        content
                    )
                    add_time = time.time() - start_time
                    
                    # Compete for access
                    start_time = time.time()
                    winner = workspace.compete_for_access()
                    compete_time = time.time() - start_time
                    
                    # Broadcast if won
                    broadcast_time = 0.0
                    if winner == coalition_id:
                        start_time = time.time()
                        workspace.broadcast_to_workspace(coalition_id)
                        broadcast_time = time.time() - start_time
                    
                    worker_results.append({
                        'worker_id': worker_id,
                        'operation': i,
                        'add_time': add_time,
                        'compete_time': compete_time,
                        'broadcast_time': broadcast_time,
                        'won_access': winner == coalition_id
                    })
                    
                except Exception as e:
                    logger.error(f"Worker {worker_id}, operation {i} failed: {str(e)}")
                
                finally:
                    workspace.clear_competitors()
            
            return worker_results
        
        # Run concurrent workers
        n_workers = 4
        n_operations_per_worker = 25
        
        with ThreadPoolExecutor(max_workers=n_workers) as executor:
            futures = [
                executor.submit(worker_function, worker_id, n_operations_per_worker)
                for worker_id in range(n_workers)
            ]
            
            all_results = []
            for future in futures:
                worker_results = future.result()
                all_results.extend(worker_results)
        
        # Analyze concurrent performance
        add_times = [r['add_time'] for r in all_results]
        compete_times = [r['compete_time'] for r in all_results]
        broadcast_times = [r['broadcast_time'] for r in all_results if r['broadcast_time'] > 0]
        
        results = {
            'n_workers': n_workers,
            'n_operations_per_worker': n_operations_per_worker,
            'total_operations': len(all_results),
            'mean_add_time': np.mean(add_times),
            'mean_compete_time': np.mean(compete_times),
            'mean_broadcast_time': np.mean(broadcast_times) if broadcast_times else 0.0,
            'operations_per_second': len(all_results) / sum(add_times + compete_times + broadcast_times),
            'success_rate': sum(1 for r in all_results if r['won_access']) / len(all_results),
            'concurrent_errors': 0  # No errors if we reach here
        }
        
        # Validate concurrent performance
        assert results['operations_per_second'] > 10.0, \
            f"Concurrent operations too slow: {results['operations_per_second']:.1f} ops/sec"
        
        assert results['success_rate'] > 0.1, \
            f"Success rate too low under concurrency: {results['success_rate']:.2%}"
        
        logger.info("✓ Concurrent access performance acceptable")
        return results
    
    def test_memory_efficiency(self):
        """Test memory efficiency under sustained load."""
        logger.info("Testing memory efficiency")
        
        workspace = MockGlobalWorkspace(self.config)
        
        # Monitor memory usage over sustained operation
        initial_memory = self._estimate_memory_usage(workspace)
        memory_measurements = [initial_memory]
        
        # Sustained operation cycle
        n_cycles = 100
        
        for cycle in range(n_cycles):
            # Add multiple competitors
            contents = self.content_generator.generate_content_batch(5)
            
            for content in contents:
                coalition_id = workspace.add_competitor(
                    content['id'], content['features'], 
                    np.random.uniform(0.5, 1.5), content
                )
            
            # Form coalitions
            workspace.form_coalitions()
            
            # Run competition
            winner = workspace.compete_for_access()
            
            # Broadcast if winner
            if winner:
                workspace.broadcast_to_workspace(winner)
            
            # Update dynamics
            workspace.update_workspace_dynamics()
            
            # Clear competitors
            workspace.clear_competitors()
            
            # Measure memory every 10 cycles
            if cycle % 10 == 0:
                current_memory = self._estimate_memory_usage(workspace)
                memory_measurements.append(current_memory)
                
                # Force garbage collection
                gc.collect()
        
        final_memory = self._estimate_memory_usage(workspace)
        memory_growth = final_memory - initial_memory
        max_memory = max(memory_measurements)
        
        results = {
            'initial_memory_mb': initial_memory,
            'final_memory_mb': final_memory,
            'max_memory_mb': max_memory,
            'memory_growth_mb': memory_growth,
            'memory_measurements': memory_measurements,
            'n_cycles': n_cycles,
            'memory_efficiency': memory_growth / n_cycles  # Growth per cycle
        }
        
        # Validate memory efficiency
        assert memory_growth < self.config.max_memory_usage_mb * 0.5, \
            f"Memory growth too high: {memory_growth:.1f} MB"
        
        assert max_memory < self.config.max_memory_usage_mb, \
            f"Peak memory usage too high: {max_memory:.1f} MB"
        
        logger.info(f"✓ Memory efficiency: {memory_growth:.1f} MB growth over {n_cycles} cycles")
        return results
    
    def _estimate_memory_usage(self, workspace: MockGlobalWorkspace) -> float:
        """Estimate memory usage in MB."""
        import sys
        
        def get_size(obj, seen=None):
            size = sys.getsizeof(obj)
            if seen is None:
                seen = set()
            
            obj_id = id(obj)
            if obj_id in seen:
                return 0
            
            seen.add(obj_id)
            
            if isinstance(obj, dict):
                size += sum([get_size(v, seen) for v in obj.values()])
                size += sum([get_size(k, seen) for k in obj.keys()])
            elif hasattr(obj, '__dict__'):
                size += get_size(obj.__dict__, seen)
            elif hasattr(obj, '__iter__') and not isinstance(obj, (str, bytes, bytearray)):
                try:
                    size += sum([get_size(i, seen) for i in obj])
                except:
                    pass
            
            return size
        
        return get_size(workspace) / (1024 * 1024)

class TestIntegration:
    """Integration tests for global workspace with other components."""
    
    def __init__(self, config: GlobalWorkspaceTestConfig):
        self.config = config
        self.content_generator = ContentGenerator(config)
        
    def test_consciousness_integration(self):
        """Test integration with consciousness components."""
        logger.info("Testing consciousness integration")
        
        workspace = MockGlobalWorkspace(self.config)
        
        # Simulate consciousness components
        consciousness_modules = {
            'attention_system': {'receptivity': 0.9, 'current_focus': 'visual'},
            'memory_system': {'capacity': 0.8, 'consolidation_rate': 0.7},
            'executive_control': {'priority_bias': 0.6, 'inhibition_strength': 0.4},
            'self_awareness': {'monitoring_level': 0.5, 'metacognitive_state': 'active'}
        }
        
        # Register consciousness modules as broadcast targets
        for module_name in consciousness_modules.keys():
            workspace.register_broadcast_target(module_name)
        
        # Test consciousness-aware content processing
        consciousness_scenarios = [
            {'content_type': 'visual', 'expected_target': 'attention_system'},
            {'content_type': 'semantic', 'expected_target': 'memory_system'},
            {'content_type': 'motor', 'expected_target': 'executive_control'},
            {'content_type': 'emotional', 'expected_target': 'self_awareness'}
        ]
        
        integration_results = {}
        
        for scenario in consciousness_scenarios:
            content_type = scenario['content_type']
            expected_target = scenario['expected_target']
            
            # Generate consciousness-relevant content
            content = self.content_generator._generate_single_content(0, content_type)
            
            # Enhance content with consciousness metadata
            content['consciousness_relevance'] = {
                'attention_weight': 0.8 if content_type == 'visual' else 0.3,
                'memory_weight': 0.9 if content_type == 'semantic' else 0.4,
                'executive_weight': 0.7 if content_type == 'motor' else 0.3,
                'metacognitive_weight': 0.6 if content_type == 'emotional' else 0.2
            }
            
            # Process through workspace
            coalition_id = workspace.add_competitor(content['id'], content['features'], 1.2, content)
            winner = workspace.compete_for_access()
            
            if winner:
                workspace.broadcast_to_workspace(winner)
                
                # Analyze broadcast targeting
                broadcast_record = workspace.broadcast_history[-1]
                target_influences = broadcast_record['target_influences']
                
                # Map to consciousness modules
                module_influences = {}
                for i, module_name in enumerate(consciousness_modules.keys()):
                    if i < len(target_influences):
                        module_influences[module_name] = target_influences[i]
                
                integration_results[content_type] = {
                    'module_influences': module_influences,
                    'total_influence': broadcast_record['total_influence'],
                    'integration_strength': max(module_influences.values()) if module_influences else 0.0
                }
            
            workspace.clear_competitors()
            workspace.workspace_contents.clear()
        
        # Validate consciousness integration
        for content_type, results in integration_results.items():
            assert results['integration_strength'] > 0.1, \
                f"Integration strength too low for {content_type}: {results['integration_strength']:.3f}"
        
        # Check that different content types show different integration patterns
        influence_patterns = [r['module_influences'] for r in integration_results.values()]
        if len(influence_patterns) > 1:
            pattern_variance = np.var([list(p.values()) for p in influence_patterns])
            assert pattern_variance > 0.01, f"Consciousness integration patterns should vary: {pattern_variance:.4f}"
        
        logger.info("✓ Consciousness integration working correctly")
        return integration_results
    
    def test_multi_workspace_interaction(self):
        """Test interaction between multiple workspace instances."""
        logger.info("Testing multi-workspace interaction")
        
        # Create multiple workspace instances
        workspace_a = MockGlobalWorkspace(self.config)
        workspace_b = MockGlobalWorkspace(self.config)
        
        # Configure cross-workspace communication
        workspace_a.register_broadcast_target('workspace_b')
        workspace_b.register_broadcast_target('workspace_a')
        
        # Generate different types of content for each workspace
        visual_contents = self.content_generator.generate_content_batch(3, ['visual'])
        semantic_contents = self.content_generator.generate_content_batch(3, ['semantic'])
        
        # Process contents in workspace A (visual specialization)
        workspace_a_results = []
        for content in visual_contents:
            coalition_id = workspace_a.add_competitor(content['id'], content['features'], 
                                                    np.random.uniform(0.8, 1.5), content)
            winner = workspace_a.compete_for_access()
            if winner:
                workspace_a.broadcast_to_workspace(winner)
                workspace_a_results.append(workspace_a.broadcast_history[-1])
            workspace_a.clear_competitors()
        
        # Process contents in workspace B (semantic specialization)
        workspace_b_results = []
        for content in semantic_contents:
            coalition_id = workspace_b.add_competitor(content['id'], content['features'], 
                                                    np.random.uniform(0.8, 1.5), content)
            winner = workspace_b.compete_for_access()
            if winner:
                workspace_b.broadcast_to_workspace(winner)
                workspace_b_results.append(workspace_b.broadcast_history[-1])
            workspace_b.clear_competitors()
        
        # Analyze cross-workspace influence
        workspace_a_influence = [r['total_influence'] for r in workspace_a_results]
        workspace_b_influence = [r['total_influence'] for r in workspace_b_results]
        
        # Simulate cross-workspace information transfer
        if workspace_a_results and workspace_b_results:
            # Transfer strongest broadcast from A to B
            strongest_a = max(workspace_a_results, key=lambda x: x['total_influence'])
            
            # Create derived content for workspace B
            derived_content = self.content_generator._generate_single_content(0, 'semantic')
            derived_content['derived_from'] = strongest_a['content_id']
            derived_content['cross_workspace_influence'] = strongest_a['total_influence']
            
            # Process derived content in workspace B
            coalition_id = workspace_b.add_competitor(derived_content['id'], derived_content['features'], 
                                                    1.0 + strongest_a['total_influence'] * 0.5, derived_content)
            winner = workspace_b.compete_for_access()
            
            cross_workspace_success = winner is not None
        else:
            cross_workspace_success = False
        
        results = {
            'workspace_a_broadcasts': len(workspace_a_results),
            'workspace_b_broadcasts': len(workspace_b_results),
            'workspace_a_avg_influence': np.mean(workspace_a_influence) if workspace_a_influence else 0.0,
            'workspace_b_avg_influence': np.mean(workspace_b_influence) if workspace_b_influence else 0.0,
            'cross_workspace_transfer': cross_workspace_success,
            'workspace_specialization': abs(np.mean(workspace_a_influence) - np.mean(workspace_b_influence)) if workspace_a_influence and workspace_b_influence else 0.0
        }
        
        # Validate multi-workspace interaction
        assert results['workspace_a_broadcasts'] > 0, "Workspace A should have successful broadcasts"
        assert results['workspace_b_broadcasts'] > 0, "Workspace B should have successful broadcasts"
        
        logger.info("✓ Multi-workspace interaction working correctly")
        return results
    
    def test_hierarchical_broadcasting(self):
        """Test hierarchical broadcasting capabilities."""
        logger.info("Testing hierarchical broadcasting")
        
        if not self.config.enable_hierarchical_broadcasting:
            logger.info("Hierarchical broadcasting disabled, skipping test")
            return {'skipped': True}
        
        workspace = MockGlobalWorkspace(self.config)
        
        # Create hierarchical target structure
        hierarchical_targets = {
            'cortex': {
                'visual_cortex': ['v1', 'v2', 'v4', 'mt'],
                'auditory_cortex': ['a1', 'a2', 'belt', 'parabelt'],
                'motor_cortex': ['m1', 'premotor', 'sma']
            },
            'subcortex': {
                'thalamus': ['lgn', 'mgn', 'vpl'],
                'basal_ganglia': ['striatum', 'gpe', 'gpi', 'stn']
            }
        }
        
        # Register all targets
        all_targets = []
        for region, areas in hierarchical_targets.items():
            workspace.register_broadcast_target(region)
            all_targets.append(region)
            for area, subregions in areas.items():
                workspace.register_broadcast_target(area)
                all_targets.append(area)
                for subregion in subregions:
                    workspace.register_broadcast_target(subregion)
                    all_targets.append(subregion)
        
        # Test hierarchical broadcast with different content types
        hierarchical_results = {}
        
        content_types = ['visual', 'auditory', 'motor']
        for content_type in content_types:
            content = self.content_generator._generate_single_content(0, content_type)
            coalition_id = workspace.add_competitor(content['id'], content['features'], 1.5, content)
            
            workspace.broadcast_to_workspace(coalition_id)
            
            # Analyze hierarchical influence
            broadcast_record = workspace.broadcast_history[-1]
            target_influences = broadcast_record['target_influences']
            
            # Map influences to hierarchical structure
            hierarchical_influence = {}
            for i, target in enumerate(all_targets[:len(target_influences)]):
                hierarchical_influence[target] = float(target_influences[i])
            
            hierarchical_results[content_type] = {
                'hierarchical_influence': hierarchical_influence,
                'total_influence': broadcast_record['total_influence']
            }
            
            workspace.clear_competitors()
            workspace.workspace_contents.clear()
        
        # Validate hierarchical broadcasting
        for content_type, results in hierarchical_results.items():
            assert results['total_influence'] > 0, \
                f"Hierarchical broadcast should have influence for {content_type}"
            
            # Check that appropriate hierarchical targets are influenced
            influence_dict = results['hierarchical_influence']
            
            if content_type == 'visual':
                # Visual content should influence visual hierarchy
                visual_influence = influence_dict.get('visual_cortex', 0.0)
                total_influence = sum(influence_dict.values())
                visual_proportion = visual_influence / total_influence if total_influence > 0 else 0.0
                
                # Allow some flexibility in hierarchical targeting
                assert visual_proportion >= 0.1, \
                    f"Visual content should influence visual hierarchy: {visual_proportion:.3f}"
        
        logger.info("✓ Hierarchical broadcasting working correctly")
        return hierarchical_results
    
    def test_predictive_workspace_behavior(self):
        """Test predictive workspace behavior capabilities."""
        logger.info("Testing predictive workspace behavior")
        
        if not self.config.enable_predictive_competition:
            logger.info("Predictive competition disabled, skipping test")
            return {'skipped': True}
        
        workspace = MockGlobalWorkspace(self.config)
        
        # Create predictable content sequence
        content_sequence = []
        for i in range(15):
            if i < 5:
                content_type = 'visual'
            elif i < 10:
                content_type = 'auditory'
            else:
                content_type = 'semantic'
            
            content = self.content_generator._generate_single_content(i, content_type)
            content_sequence.append(content)
        
        # Process sequence and build prediction history
        prediction_accuracy = []
        
        for i, content in enumerate(content_sequence):
            coalition_id = workspace.add_competitor(content['id'], content['features'], 
                                                  np.random.uniform(0.8, 1.5), content)
            
            # Get prediction if available
            predicted_capacity = None
            if 'predicted_capacity' in workspace.performance_metrics:
                recent_predictions = workspace.performance_metrics['predicted_capacity']
                if recent_predictions:
                    predicted_capacity = recent_predictions[-1]
            
            # Broadcast and record actual behavior
            winner = workspace.compete_for_access()
            if winner:
                workspace.broadcast_to_workspace(winner)
            
            workspace.update_workspace_dynamics()  # This updates predictions
            
            # Measure prediction accuracy if prediction was made
            if predicted_capacity is not None:
                actual_utilization = workspace.get_workspace_state()['capacity_utilization']
                prediction_error = abs(predicted_capacity - actual_utilization)
                prediction_accuracy.append(1.0 - min(prediction_error, 1.0))
            
            workspace.clear_competitors()
        
        # Analyze predictive performance
        if prediction_accuracy:
            avg_prediction_accuracy = np.mean(prediction_accuracy)
            prediction_improvement = (
                np.mean(prediction_accuracy[-5:]) - np.mean(prediction_accuracy[:5])
                if len(prediction_accuracy) >= 10 else 0.0
            )
        else:
            avg_prediction_accuracy = 0.0
            prediction_improvement = 0.0
        
        results = {
            'n_predictions': len(prediction_accuracy),
            'avg_prediction_accuracy': avg_prediction_accuracy,
            'prediction_improvement': prediction_improvement,
            'final_accuracy': prediction_accuracy[-1] if prediction_accuracy else 0.0
        }
        
        # Validate predictive behavior
        if prediction_accuracy:
            assert avg_prediction_accuracy > 0.5, \
                f"Prediction accuracy too low: {avg_prediction_accuracy:.3f}"
        
        logger.info(f"✓ Predictive workspace behavior: {avg_prediction_accuracy:.3f} accuracy")
        return results

# ============================================================================
# Visualization and Analysis Tools
# ============================================================================

class GlobalWorkspaceVisualizer:
    """Visualization tools for global workspace analysis."""
    
    def __init__(self, config: GlobalWorkspaceTestConfig):
        self.config = config
        plt.style.use('default')
        
    def plot_competition_dynamics(self, competition_history: List[Dict[str, Any]], 
                                 save_path: Optional[str] = None):
        """Plot competition dynamics over time."""
        fig, axes = plt.subplots(2, 2, figsize=self.config.figure_size)
        
        if not competition_history:
            plt.text(0.5, 0.5, 'No competition data available', ha='center', va='center')
            if save_path:
                plt.savefig(save_path, dpi=self.config.plot_dpi, bbox_inches='tight')
            else:
                plt.show()
            return
        
        # Extract data
        timestamps = [c['timestamp'] for c in competition_history]
        winner_probabilities = [c['winner_probability'] for c in competition_history]
        n_participants = [len(c['participants']) for c in competition_history]
        competition_times = [c['competition_time'] for c in competition_history]
        
        # Winner probabilities over time
        axes[0, 0].plot(timestamps, winner_probabilities, 'b-', linewidth=2, alpha=0.7)
        axes[0, 0].axhline(y=0.7, color='red', linestyle='--', alpha=0.7, label='Threshold')
        axes[0, 0].set_title('Winner Probabilities Over Time')
        axes[0, 0].set_xlabel('Time')
        axes[0, 0].set_ylabel('Winner Probability')
        axes[0, 0].grid(True, alpha=0.3)
        axes[0, 0].legend()
        
        # Number of participants
        axes[0, 1].plot(timestamps, n_participants, 'g-', linewidth=2, alpha=0.7)
        axes[0, 1].set_title('Competition Participants')
        axes[0, 1].set_xlabel('Time')
        axes[0, 1].set_ylabel('Number of Participants')
        axes[0, 1].grid(True, alpha=0.3)
        
        # Competition time distribution
        axes[1, 0].hist(competition_times, bins=20, alpha=0.7, color='orange', edgecolor='black')
        axes[1, 0].set_title('Competition Time Distribution')
        axes[1, 0].set_xlabel('Competition Time (seconds)')
        axes[1, 0].set_ylabel('Frequency')
        axes[1, 0].grid(True, alpha=0.3)
        
        # Performance metrics
        success_rate = sum(1 for p in winner_probabilities if p > 0.7) / len(winner_probabilities)
        avg_participants = np.mean(n_participants)
        avg_competition_time = np.mean(competition_times)
        
        metrics_text = f"""Competition Metrics:
        Success Rate: {success_rate:.2%}
        Avg Participants: {avg_participants:.1f}
        Avg Time: {avg_competition_time:.4f}s
        Total Competitions: {len(competition_history)}"""
        
        axes[1, 1].text(0.05, 0.95, metrics_text, transform=axes[1, 1].transAxes,
                        verticalalignment='top', fontfamily='monospace',
                        bbox=dict(boxstyle='round', facecolor='lightgray', alpha=0.8))
        axes[1, 1].set_xlim(0, 1)
        axes[1, 1].set_ylim(0, 1)
        axes[1, 1].axis('off')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=self.config.plot_dpi, bbox_inches='tight')
        else:
            plt.show()
    
    def plot_workspace_evolution(self, workspace_history: List[Dict[str, Any]], 
                                save_path: Optional[str] = None):
        """Plot workspace state evolution over time."""
        fig, axes = plt.subplots(3, 1, figsize=(self.config.figure_size[0], self.config.figure_size[1] + 2))
        
        if not workspace_history:
            plt.text(0.5, 0.5, 'No workspace history available', ha='center', va='center')
            if save_path:
                plt.savefig(save_path, dpi=self.config.plot_dpi, bbox_inches='tight')
            else:
                plt.show()
            return
        
        # Extract data
        timestamps = [w['timestamp'] for w in workspace_history]
        total_strengths = [w['total_strength'] for w in workspace_history]
        capacity_utilizations = [w['capacity_utilization'] for w in workspace_history]
        n_contents = [len(w['contents']) for w in workspace_history]
        
        # Total strength over time
        axes[0].plot(timestamps, total_strengths, 'b-', linewidth=2, alpha=0.8)
        axes[0].fill_between(timestamps, total_strengths, alpha=0.3, color='blue')
        axes[0].set_title('Workspace Total Strength Over Time')
        axes[0].set_ylabel('Total Strength')
        axes[0].grid(True, alpha=0.3)
        
        # Capacity utilization
        axes[1].plot(timestamps, capacity_utilizations, 'r-', linewidth=2, alpha=0.8)
        axes[1].axhline(y=1.0, color='red', linestyle='--', alpha=0.7, label='Full Capacity')
        axes[1].fill_between(timestamps, capacity_utilizations, alpha=0.3, color='red')
        axes[1].set_title('Workspace Capacity Utilization')
        axes[1].set_ylabel('Capacity Utilization')
        axes[1].legend()
        axes[1].grid(True, alpha=0.3)
        
        # Number of contents
        axes[2].step(timestamps, n_contents, 'g-', linewidth=2, where='post', alpha=0.8)
        axes[2].fill_between(timestamps, n_contents, step='post', alpha=0.3, color='green')
        axes[2].set_title('Number of Active Contents')
        axes[2].set_xlabel('Time')
        axes[2].set_ylabel('Content Count')
        axes[2].grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=self.config.plot_dpi, bbox_inches='tight')
        else:
            plt.show()
    
    def plot_coalition_analysis(self, coalition_data: Dict[str, Any], 
                               save_path: Optional[str] = None):
        """Plot coalition formation analysis."""
        fig, axes = plt.subplots(2, 2, figsize=self.config.figure_size)
        
        # Coalition size distribution
        if 'size_distribution' in coalition_data:
            sizes = list(coalition_data['size_distribution'].keys())
            counts = list(coalition_data['size_distribution'].values())
            
            axes[0, 0].bar(sizes, counts, alpha=0.7, color='skyblue', edgecolor='black')
            axes[0, 0].set_title('Coalition Size Distribution')
            axes[0, 0].set_xlabel('Coalition Size')
            axes[0, 0].set_ylabel('Count')
            axes[0, 0].grid(True, alpha=0.3)
        
        # Synergy score distribution
        if 'synergy_scores' in coalition_data:
            synergy_scores = coalition_data['synergy_scores']
            axes[0, 1].hist(synergy_scores, bins=15, alpha=0.7, color='lightgreen', edgecolor='black')
            axes[0, 1].axvline(np.mean(synergy_scores), color='red', linestyle='--', 
                              label=f'Mean: {np.mean(synergy_scores):.3f}')
            axes[0, 1].set_title('Synergy Score Distribution')
            axes[0, 1].set_xlabel('Synergy Score')
            axes[0, 1].set_ylabel('Frequency')
            axes[0, 1].legend()
            axes[0, 1].grid(True, alpha=0.3)
        
        # Coalition formation efficiency
        if 'formation_times' in coalition_data:
            formation_times = coalition_data['formation_times']
            content_counts = coalition_data.get('content_counts', [])
            
            if len(formation_times) == len(content_counts):
                axes[1, 0].scatter(content_counts, formation_times, alpha=0.7, color='orange')
                axes[1, 0].set_title('Coalition Formation Efficiency')
                axes[1, 0].set_xlabel('Number of Contents')
                axes[1, 0].set_ylabel('Formation Time (s)')
                axes[1, 0].grid(True, alpha=0.3)
                
                # Add trend line
                if len(content_counts) > 1:
                    z = np.polyfit(content_counts, formation_times, 1)
                    p = np.poly1d(z)
                    axes[1, 0].plot(content_counts, p(content_counts), "r--", alpha=0.8)
        
        # Summary statistics
        summary_text = "Coalition Analysis Summary:\n"
        if 'mean_size' in coalition_data:
            summary_text += f"Mean Size: {coalition_data['mean_size']:.2f}\n"
        if 'avg_synergy_score' in coalition_data:
            summary_text += f"Avg Synergy: {coalition_data['avg_synergy_score']:.3f}\n"
        if 'total_coalitions' in coalition_data:
            summary_text += f"Total Coalitions: {coalition_data['total_coalitions']}\n"
        if 'coalition_reduction_rate' in coalition_data:
            summary_text += f"Reduction Rate: {coalition_data['coalition_reduction_rate']:.2%}"
        
        axes[1, 1].text(0.05, 0.95, summary_text, transform=axes[1, 1].transAxes,
                        verticalalignment='top', fontfamily='monospace',
                        bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
        axes[1, 1].set_xlim(0, 1)
        axes[1, 1].set_ylim(0, 1)
        axes[1, 1].axis('off')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=self.config.plot_dpi, bbox_inches='tight')
        else:
            plt.show()
    
    def plot_broadcast_influence_network(self, influence_data: Dict[str, Any], 
                                       save_path: Optional[str] = None):
        """Plot broadcast influence network."""
        fig, ax = plt.subplots(figsize=self.config.figure_size)
        
        if 'influence_matrix' not in influence_data:
            plt.text(0.5, 0.5, 'No influence data available', ha='center', va='center')
            if save_path:
                plt.savefig(save_path, dpi=self.config.plot_dpi, bbox_inches='tight')
            else:
                plt.show()
            return
        
        influence_matrix = np.array(influence_data['influence_matrix'])
        
        # Create network graph
        G = nx.DiGraph()
        
        # Add nodes
        n_nodes = influence_matrix.shape[0]
        node_names = influence_data.get('node_names', [f'Node_{i}' for i in range(n_nodes)])
        
        for i, name in enumerate(node_names):
            G.add_node(i, name=name)
        
        # Add edges based on influence strength
        threshold = np.percentile(influence_matrix.flatten(), 75)  # Top 25% of influences
        
        for i in range(n_nodes):
            for j in range(n_nodes):
                if i != j and influence_matrix[i, j] > threshold:
                    G.add_edge(i, j, weight=influence_matrix[i, j])
        
        # Layout
        pos = nx.spring_layout(G, k=2, iterations=50)
        
        # Draw network
        node_sizes = [300 + 200 * np.sum(influence_matrix[i, :]) for i in range(n_nodes)]
        edge_widths = [3 * G[u][v]['weight'] / np.max(influence_matrix) for u, v in G.edges()]
        
        nx.draw(G, pos, ax=ax, 
                node_size=node_sizes,
                node_color='lightblue',
                edge_color='gray',
                width=edge_widths,
                with_labels=True,
                labels={i: node_names[i][:8] for i in range(len(node_names))},
                font_size=8,
                arrows=True,
                arrowsize=20)
        
        ax.set_title('Broadcast Influence Network')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=self.config.plot_dpi, bbox_inches='tight')
        else:
            plt.show()
    
    def plot_performance_dashboard(self, performance_data: Dict[str, Any], 
                                 save_path: Optional[str] = None):
        """Create comprehensive performance dashboard."""
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        
        # Competition performance
        if 'competition_performance' in performance_data:
            comp_data = performance_data['competition_performance']
            competitor_counts = list(comp_data.keys())
            competition_rates = [comp_data[n]['competitions_per_second'] for n in competitor_counts]
            
            axes[0, 0].plot(competitor_counts, competition_rates, 'o-', linewidth=2, markersize=6)
            axes[0, 0].set_title('Competition Performance')
            axes[0, 0].set_xlabel('Number of Competitors')
            axes[0, 0].set_ylabel('Competitions/Second')
            axes[0, 0].grid(True, alpha=0.3)
        
        # Broadcast performance
        if 'broadcast_performance' in performance_data:
            broadcast_data = performance_data['broadcast_performance']
            broadcast_rate = broadcast_data['broadcasts_per_second']
            memory_usage = broadcast_data['memory_usage_mb']
            
            # Broadcast rate gauge
            axes[0, 1].bar(['Broadcast Rate'], [broadcast_rate], color='green', alpha=0.7)
            axes[0, 1].axhline(y=30, color='red', linestyle='--', label='Min Requirement')
            axes[0, 1].set_title('Broadcast Performance')
            axes[0, 1].set_ylabel('Broadcasts/Second')
            axes[0, 1].legend()
            axes[0, 1].grid(True, alpha=0.3)
        
        # Memory usage
        if 'memory_efficiency' in performance_data:
            memory_data = performance_data['memory_efficiency']
            memory_measurements = memory_data['memory_measurements']
            
            axes[0, 2].plot(memory_measurements, 'r-', linewidth=2)
            axes[0, 2].set_title('Memory Usage Over Time')
            axes[0, 2].set_xlabel('Time Step')
            axes[0, 2].set_ylabel('Memory (MB)')
            axes[0, 2].grid(True, alpha=0.3)
        
        # Coalition formation efficiency
        if 'coalition_performance' in performance_data:
            coalition_data = performance_data['coalition_performance']
            content_counts = list(coalition_data.keys())
            formation_times = [coalition_data[n]['formation_time'] for n in content_counts]
            
            axes[1, 0].plot(content_counts, formation_times, 's-', linewidth=2, markersize=6)
            axes[1, 0].set_title('Coalition Formation Time')
            axes[1, 0].set_xlabel('Number of Contents')
            axes[1, 0].set_ylabel('Formation Time (s)')
            axes[1, 0].grid(True, alpha=0.3)
        
        # Workspace metrics
        if 'workspace_metrics' in performance_data:
            workspace_metrics = performance_data['workspace_metrics']
            metric_names = list(workspace_metrics.keys())
            metric_values = list(workspace_metrics.values())
            
            axes[1, 1].bar(range(len(metric_names)), metric_values, alpha=0.7, 
                          color=plt.cm.viridis(np.linspace(0, 1, len(metric_names))))
            axes[1, 1].set_title('Workspace Quality Metrics')
            axes[1, 1].set_xticks(range(len(metric_names)))
            axes[1, 1].set_xticklabels([name[:10] for name in metric_names], rotation=45)
            axes[1, 1].set_ylabel('Metric Value')
            axes[1, 1].grid(True, alpha=0.3)
        
        # Overall system health
        health_metrics = {}
        if 'competition_performance' in performance_data:
            health_metrics['Competition'] = 1.0 if competition_rates[-1] > 50 else 0.5
        if 'broadcast_performance' in performance_data:
            health_metrics['Broadcast'] = 1.0 if broadcast_rate > 30 else 0.5
        if 'memory_efficiency' in performance_data:
            final_memory = memory_measurements[-1] if memory_measurements else 0
            health_metrics['Memory'] = 1.0 if final_memory < 1000 else 0.5
        
        if health_metrics:
            health_names = list(health_metrics.keys())
            health_values = list(health_metrics.values())
            colors = ['green' if v == 1.0 else 'orange' if v == 0.5 else 'red' for v in health_values]
            
            axes[1, 2].bar(health_names, health_values, color=colors, alpha=0.7)
            axes[1, 2].set_title('System Health Status')
            axes[1, 2].set_ylabel('Health Score')
            axes[1, 2].set_ylim(0, 1.2)
            axes[1, 2].grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=self.config.plot_dpi, bbox_inches='tight')
        else:
            plt.show()

# ============================================================================
# Data Management and Persistence
# ============================================================================

class GlobalWorkspaceDataManager:
    """Data management for global workspace test results."""
    
    def __init__(self, base_path: str = "./global_workspace_test_data"):
        self.base_path = Path(base_path)
        self.base_path.mkdir(parents=True, exist_ok=True)
        
    def save_test_results(self, results: Dict[str, Any], 
                         test_name: str, 
                         timestamp: Optional[str] = None) -> str:
        """Save test results to file."""
        if timestamp is None:
            timestamp = time.strftime("%Y%m%d_%H%M%S")
        
        filename = f"{test_name}_{timestamp}.json"
        filepath = self.base_path / filename
        
        # Convert to serializable format
        serializable_results = self._make_serializable(results)
        
        with open(filepath, 'w') as f:
            json.dump(serializable_results, f, indent=2, default=str)
        
        logger.info(f"Test results saved to: {filepath}")
        return str(filepath)
    
    def save_workspace_state(self, workspace: MockGlobalWorkspace, 
                           filename: str) -> str:
        """Save complete workspace state."""
        filepath = self.base_path / f"{filename}.pkl"
        
        state_data = {
            'workspace_contents': workspace.workspace_contents,
            'competing_coalitions': workspace.competing_coalitions,
            'workspace_history': list(workspace.workspace_history),
            'broadcast_history': list(workspace.broadcast_history),
            'competition_history': list(workspace.competition_history),
            'performance_metrics': dict(workspace.performance_metrics),
            'influence_matrix': workspace.influence_matrix.tolist(),
            'broadcast_targets': list(workspace.broadcast_targets),
            'timestamp': time.time()
        }
        
        with open(filepath, 'wb') as f:
            pickle.dump(state_data, f)
        
        logger.info(f"Workspace state saved to: {filepath}")
        return str(filepath)
    
    def load_workspace_state(self, filepath: str) -> Dict[str, Any]:
        """Load workspace state from file."""
        with open(filepath, 'rb') as f:
            state_data = pickle.load(f)
        
        logger.info(f"Workspace state loaded from: {filepath}")
        return state_data
    
    def export_comprehensive_report(self, test_results: Dict[str, Any], 
                                  report_name: str) -> str:
        """Export comprehensive test report."""
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        report_filename = f"{report_name}_report_{timestamp}.md"
        report_path = self.base_path / report_filename
        
        with open(report_path, 'w') as f:
            f.write(f"# ULTRA Global Workspace Test Report\n\n")
            f.write(f"**Generated:** {time.strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            # Executive Summary
            f.write("## Executive Summary\n\n")
            
            # Competition Analysis
            if 'competition_tests' in test_results:
                f.write("### Competition System\n")
                comp_data = test_results['competition_tests']
                
                if 'competition_probability_calculation' in comp_data:
                    prob_data = comp_data['competition_probability_calculation']
                    f.write(f"- Probability calculation tests: **PASSED**\n")
                    f.write(f"- Average competition entropy: {np.mean([case['competition_entropy'] for case in prob_data.values()]):.3f}\n")
                
                if 'competition_ordering_consistency' in comp_data:
                    order_data = comp_data['competition_ordering_consistency']
                    f.write(f"- Ordering consistency: **{order_data['consistency_rate']:.1%}**\n")
                
                f.write("\n")
            
            # Coalition Analysis
            if 'coalition_tests' in test_results:
                f.write("### Coalition Formation\n")
                coalition_data = test_results['coalition_tests']
                
                if 'coalition_formation_algorithm' in coalition_data:
                    formation_data = coalition_data['coalition_formation_algorithm']
                    f.write(f"- Coalition reduction rate: **{formation_data['coalition_reduction_rate']:.1%}**\n")
                    f.write(f"- Average synergy score: **{formation_data['avg_synergy_score']:.3f}**\n")
                
                f.write("\n")
            
            # Performance Analysis
            if 'performance_tests' in test_results:
                f.write("### Performance Metrics\n")
                perf_data = test_results['performance_tests']
                
                f.write("| Metric | Value | Unit | Status |\n")
                f.write("|--------|-------|------|--------|\n")
                
                if 'competition_performance' in perf_data:
                    comp_perf = perf_data['competition_performance']
                    rate_50 = comp_perf.get(50, {}).get('competitions_per_second', 0)
                    status = "✅ PASS" if rate_50 >= 50 else "❌ FAIL"
                    f.write(f"| Competition Rate (50 competitors) | {rate_50:.1f} | ops/sec | {status} |\n")
                
                if 'broadcast_performance' in perf_data:
                    broadcast_perf = perf_data['broadcast_performance']
                    broadcast_rate = broadcast_perf.get('broadcasts_per_second', 0)
                    status = "✅ PASS" if broadcast_rate >= 30 else "❌ FAIL"
                    f.write(f"| Broadcast Rate | {broadcast_rate:.1f} | broadcasts/sec | {status} |\n")
                
                f.write("\n")
            
            # Integration Analysis
            if 'integration_tests' in test_results:
                f.write("### Integration Tests\n")
                integration_data = test_results['integration_tests']
                
                for test_name, test_result in integration_data.items():
                    f.write(f"- **{test_name.replace('_', ' ').title()}**: ")
                    if isinstance(test_result, dict) and 'skipped' in test_result:
                        f.write("SKIPPED\n")
                    else:
                        f.write("PASSED\n")
                
                f.write("\n")
            
            # Detailed Results
            f.write("## Detailed Test Results\n\n")
            
            for test_category, category_results in test_results.items():
                f.write(f"### {test_category.replace('_', ' ').title()}\n\n")
                
                if isinstance(category_results, dict):
                    for test_name, test_data in category_results.items():
                        f.write(f"#### {test_name.replace('_', ' ').title()}\n\n")
                        
                        if isinstance(test_data, dict):
                            for key, value in test_data.items():
                                if isinstance(value, (int, float)):
                                    f.write(f"- **{key}**: {value:.4f}\n")
                                elif isinstance(value, str):
                                    f.write(f"- **{key}**: {value}\n")
                                elif isinstance(value, bool):
                                    f.write(f"- **{key}**: {'✅ PASS' if value else '❌ FAIL'}\n")
                        
                        f.write("\n")
                
                f.write("\n")
        
        logger.info(f"Comprehensive report exported to: {report_path}")
        return str(report_path)
    
    def _make_serializable(self, obj: Any) -> Any:
        """Convert object to JSON-serializable format."""
        if isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, torch.Tensor):
            return obj.detach().cpu().numpy().tolist()
        elif isinstance(obj, dict):
            return {key: self._make_serializable(value) for key, value in obj.items()}
        elif isinstance(obj, (list, tuple)):
            return [self._make_serializable(item) for item in obj]
        elif isinstance(obj, (set, frozenset)):
            return list(obj)
        elif isinstance(obj, deque):
            return list(obj)
        elif hasattr(obj, '__dict__'):
            return self._make_serializable(obj.__dict__)
        else:
            return obj

# ============================================================================
# Comprehensive Test Suite Runner
# ============================================================================

class GlobalWorkspaceTestSuite:
    """Comprehensive test suite for global workspace components."""
    
    def __init__(self, config: Optional[GlobalWorkspaceTestConfig] = None):
        self.config = config or GlobalWorkspaceTestConfig()
        self.data_manager = GlobalWorkspaceDataManager()
        self.visualizer = GlobalWorkspaceVisualizer(self.config)
        
        # Initialize test classes
        self.competition_tests = TestWorkspaceCompetition(self.config)
        self.coalition_tests = TestCoalitionFormation(self.config)
        self.broadcast_tests = TestBroadcastMechanisms(self.config)
        self.dynamics_tests = TestWorkspaceDynamics(self.config)
        self.performance_tests = TestPerformanceBenchmarks(self.config)
        self.integration_tests = TestIntegration(self.config)
        
        # Results storage
        self.results = {}
        self.start_time = None
        self.end_time = None
        
    def run_all_tests(self, save_results: bool = True, 
                     generate_visualizations: bool = True,
                     run_performance_tests: bool = True) -> Dict[str, Any]:
        """Run complete test suite."""
        logger.info("="*80)
        logger.info("ULTRA GLOBAL WORKSPACE COMPREHENSIVE TEST SUITE")
        logger.info("="*80)
        
        self.start_time = time.time()
        
        try:
            # 1. Competition Tests
            logger.info("\n" + "="*60)
            logger.info("1. WORKSPACE COMPETITION TESTS")
            logger.info("="*60)
            
            self.results['competition_tests'] = {
                'competition_probability_calculation': self.competition_tests.test_competition_probability_calculation(),
                'competition_ordering_consistency': self.competition_tests.test_competition_ordering_consistency(),
                'competition_temperature_effects': self.competition_tests.test_competition_temperature_effects(),
                'competition_performance_scaling': self.competition_tests.test_competition_performance_scaling()
            }
            
            # 2. Coalition Formation Tests
            logger.info("\n" + "="*60)
            logger.info("2. COALITION FORMATION TESTS")
            logger.info("="*60)
            
            self.results['coalition_tests'] = {
                'synergy_calculation': self.coalition_tests.test_synergy_calculation(),
                'coalition_formation_algorithm': self.coalition_tests.test_coalition_formation_algorithm(),
                'coalition_stability': self.coalition_tests.test_coalition_stability(),
                'coalition_size_distribution': self.coalition_tests.test_coalition_size_distribution(),
                'coalition_performance_optimization': self.coalition_tests.test_coalition_performance_optimization()
            }
            
            # 3. Broadcast Mechanism Tests
            logger.info("\n" + "="*60)
            logger.info("3. BROADCAST MECHANISM TESTS")
            logger.info("="*60)
            
            self.results['broadcast_tests'] = {
                'broadcast_influence_propagation': self.broadcast_tests.test_broadcast_influence_propagation(),
                'broadcast_strength_prediction': self.broadcast_tests.test_broadcast_strength_prediction(),
                'broadcast_target_routing': self.broadcast_tests.test_broadcast_target_routing(),
                'broadcast_efficiency_metrics': self.broadcast_tests.test_broadcast_efficiency_metrics()
            }
            
            # 4. Workspace Dynamics Tests
            logger.info("\n" + "="*60)
            logger.info("4. WORKSPACE DYNAMICS TESTS")
            logger.info("="*60)
            
            self.results['dynamics_tests'] = {
                'workspace_decay_dynamics': self.dynamics_tests.test_workspace_decay_dynamics(),
                'workspace_capacity_management': self.dynamics_tests.test_workspace_capacity_management(),
                'adaptive_capacity_adjustment': self.dynamics_tests.test_adaptive_capacity_adjustment(),
                'temporal_coherence': self.dynamics_tests.test_temporal_coherence()
            }
            
            # 5. Performance Tests (optional)
            if run_performance_tests:
                logger.info("\n" + "="*60)
                logger.info("5. PERFORMANCE BENCHMARK TESTS")
                logger.info("="*60)
                
                self.results['performance_tests'] = {
                    'competition_performance': self.performance_tests.test_competition_performance(),
                    'broadcast_performance': self.performance_tests.test_broadcast_performance(),
                    'concurrent_access_performance': self.performance_tests.test_concurrent_access_performance(),
                    'memory_efficiency': self.performance_tests.test_memory_efficiency()
                }
            
            # 6. Integration Tests
            logger.info("\n" + "="*60)
            logger.info("6. INTEGRATION TESTS")
            logger.info("="*60)
            
            self.results['integration_tests'] = {
                'consciousness_integration': self.integration_tests.test_consciousness_integration(),
                'multi_workspace_interaction': self.integration_tests.test_multi_workspace_interaction(),
                'hierarchical_broadcasting': self.integration_tests.test_hierarchical_broadcasting(),
                'predictive_workspace_behavior': self.integration_tests.test_predictive_workspace_behavior()
            }
            
            self.end_time = time.time()
            
            # Generate test summary
            self._generate_test_summary()
            
            # Save results if requested
            if save_results:
                self._save_test_results()
            
            # Generate visualizations if requested
            if generate_visualizations:
                self._generate_visualizations()
            
            logger.info("\n" + "="*80)
            logger.info("ALL TESTS COMPLETED SUCCESSFULLY!")
            logger.info("="*80)
            
            return self.results
            
        except Exception as e:
            logger.error(f"Test suite failed: {str(e)}")
            raise
    
    def run_specific_test_category(self, category: str) -> Dict[str, Any]:
        """Run specific test category."""
        logger.info(f"Running specific test category: {category}")
        
        if category == 'competition':
            return {
                'competition_probability_calculation': self.competition_tests.test_competition_probability_calculation(),
                'competition_ordering_consistency': self.competition_tests.test_competition_ordering_consistency(),
                'competition_temperature_effects': self.competition_tests.test_competition_temperature_effects(),
                'competition_performance_scaling': self.competition_tests.test_competition_performance_scaling()
            }
        elif category == 'coalition':
            return {
                'synergy_calculation': self.coalition_tests.test_synergy_calculation(),
                'coalition_formation_algorithm': self.coalition_tests.test_coalition_formation_algorithm(),
                'coalition_stability': self.coalition_tests.test_coalition_stability(),
                'coalition_size_distribution': self.coalition_tests.test_coalition_size_distribution(),
                'coalition_performance_optimization': self.coalition_tests.test_coalition_performance_optimization()
            }
        elif category == 'broadcast':
            return {
                'broadcast_influence_propagation': self.broadcast_tests.test_broadcast_influence_propagation(),
                'broadcast_strength_prediction': self.broadcast_tests.test_broadcast_strength_prediction(),
                'broadcast_target_routing': self.broadcast_tests.test_broadcast_target_routing(),
                'broadcast_efficiency_metrics': self.broadcast_tests.test_broadcast_efficiency_metrics()
            }
        elif category == 'dynamics':
            return {
                'workspace_decay_dynamics': self.dynamics_tests.test_workspace_decay_dynamics(),
                'workspace_capacity_management': self.dynamics_tests.test_workspace_capacity_management(),
                'adaptive_capacity_adjustment': self.dynamics_tests.test_adaptive_capacity_adjustment(),
                'temporal_coherence': self.dynamics_tests.test_temporal_coherence()
            }
        elif category == 'performance':
            return {
                'competition_performance': self.performance_tests.test_competition_performance(),
                'broadcast_performance': self.performance_tests.test_broadcast_performance(),
                'concurrent_access_performance': self.performance_tests.test_concurrent_access_performance(),
                'memory_efficiency': self.performance_tests.test_memory_efficiency()
            }
        elif category == 'integration':
            return {
                'consciousness_integration': self.integration_tests.test_consciousness_integration(),
                'multi_workspace_interaction': self.integration_tests.test_multi_workspace_interaction(),
                'hierarchical_broadcasting': self.integration_tests.test_hierarchical_broadcasting(),
                'predictive_workspace_behavior': self.integration_tests.test_predictive_workspace_behavior()
            }
        else:
            raise ValueError(f"Unknown test category: {category}")
    
    def _generate_test_summary(self):
        """Generate comprehensive test summary."""
        total_time = self.end_time - self.start_time if self.end_time and self.start_time else 0
        
        summary = {
            'execution_time': total_time,
            'test_categories': len(self.results),
            'total_tests': self._count_total_tests(),
            'validation_summary': self._extract_validation_summary(),
            'performance_summary': self._extract_performance_summary(),
            'workspace_metrics': self._extract_workspace_metrics(),
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
        }
        
        self.results['test_summary'] = summary
        
        # Log summary
        logger.info(f"\nTEST EXECUTION SUMMARY:")
        logger.info(f"  Total execution time: {total_time:.2f} seconds")
        logger.info(f"  Test categories: {summary['test_categories']}")
        logger.info(f"  Total tests: {summary['total_tests']}")
        logger.info(f"  Validation success rate: {summary['validation_summary']['overall_success_rate']:.2%}")
        
        if summary['performance_summary']:
            logger.info(f"  Competition rate (50 competitors): {summary['performance_summary'].get('competition_rate_50', 0):.1f} ops/sec")
            logger.info(f"  Broadcast rate: {summary['performance_summary'].get('broadcast_rate', 0):.1f} broadcasts/sec")
    
    def _count_total_tests(self) -> int:
        """Count total number of tests executed."""
        count = 0
        
        def count_tests(obj):
            nonlocal count
            if isinstance(obj, dict):
                for key, value in obj.items():
                    if key.endswith('_tests') and isinstance(value, dict):
                        count += len(value)
                    elif isinstance(value, dict):
                        count_tests(value)
        
        count_tests(self.results)
        return count
    
    def _extract_validation_summary(self) -> Dict[str, Any]:
        """Extract overall validation summary."""
        total_validations = 0
        total_passed = 0
        
        def count_validations(obj):
            nonlocal total_validations, total_passed
            if isinstance(obj, dict):
                for key, value in obj.items():
                    if key == 'validation' and isinstance(value, dict):
                        for val_result in value.values():
                            total_validations += 1
                            if val_result:
                                total_passed += 1
                    elif isinstance(value, (dict, list)):
                        count_validations(value)
            elif isinstance(obj, list):
                for item in obj:
                    count_validations(item)
        
        count_validations(self.results)
        
        success_rate = total_passed / total_validations if total_validations > 0 else 0.0
        
        return {
            'total_validations': total_validations,
            'total_passed': total_passed,
            'total_failed': total_validations - total_passed,
            'overall_success_rate': success_rate
        }
    
    def _extract_performance_summary(self) -> Dict[str, Any]:
        """Extract performance summary."""
        performance_summary = {}
        
        if 'performance_tests' in self.results:
            perf_data = self.results['performance_tests']
            
            # Competition performance
            if 'competition_performance' in perf_data:
                comp_data = perf_data['competition_performance']
                if 50 in comp_data:
                    performance_summary['competition_rate_50'] = comp_data[50]['competitions_per_second']
            
            # Broadcast performance
            if 'broadcast_performance' in perf_data:
                broadcast_data = perf_data['broadcast_performance']
                performance_summary['broadcast_rate'] = broadcast_data['broadcasts_per_second']
                performance_summary['memory_usage_mb'] = broadcast_data['memory_usage_mb']
            
            # Concurrent performance
            if 'concurrent_access_performance' in perf_data:
                concurrent_data = perf_data['concurrent_access_performance']
                performance_summary['concurrent_operations_per_second'] = concurrent_data['operations_per_second']
                performance_summary['concurrent_success_rate'] = concurrent_data['success_rate']
            
            # Memory efficiency
            if 'memory_efficiency' in perf_data:
                memory_data = perf_data['memory_efficiency']
                performance_summary['memory_growth_mb'] = memory_data['memory_growth_mb']
                performance_summary['memory_efficiency'] = memory_data['memory_efficiency']
        
        return performance_summary
    
    def _extract_workspace_metrics(self) -> Dict[str, Any]:
        """Extract workspace-specific metrics."""
        workspace_metrics = {}
        
        # Competition metrics
        if 'competition_tests' in self.results:
            comp_data = self.results['competition_tests']
            
            if 'competition_probability_calculation' in comp_data:
                prob_data = comp_data['competition_probability_calculation']
                entropies = [case['competition_entropy'] for case in prob_data.values()]
                workspace_metrics['mean_competition_entropy'] = np.mean(entropies) if entropies else 0.0
            
            if 'competition_ordering_consistency' in comp_data:
                order_data = comp_data['competition_ordering_consistency']
                workspace_metrics['ordering_consistency_rate'] = order_data['consistency_rate']
        
        # Coalition metrics
        if 'coalition_tests' in self.results:
            coalition_data = self.results['coalition_tests']
            
            if 'coalition_formation_algorithm' in coalition_data:
                formation_data = coalition_data['coalition_formation_algorithm']
                workspace_metrics['coalition_reduction_rate'] = formation_data['coalition_reduction_rate']
                workspace_metrics['avg_coalition_synergy'] = formation_data['avg_synergy_score']
            
            if 'coalition_size_distribution' in coalition_data:
                size_data = coalition_data['coalition_size_distribution']
                workspace_metrics['mean_coalition_size'] = size_data['mean_size']
        
        # Broadcast metrics
        if 'broadcast_tests' in self.results:
            broadcast_data = self.results['broadcast_tests']
            
            if 'broadcast_efficiency_metrics' in broadcast_data:
                efficiency_data = broadcast_data['broadcast_efficiency_metrics']
                workspace_metrics['broadcast_efficiency_rate'] = efficiency_data['efficiency_rate']
                workspace_metrics['strength_influence_correlation'] = efficiency_data['strength_influence_correlation']
        
        # Dynamics metrics
        if 'dynamics_tests' in self.results:
            dynamics_data = self.results['dynamics_tests']
            
            if 'temporal_coherence' in dynamics_data:
                coherence_data = dynamics_data['temporal_coherence']
                workspace_metrics['temporal_coherence'] = coherence_data['mean_coherence']
        
        return workspace_metrics
    
    def _save_test_results(self):
        """Save test results to files."""
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        
        # Save main results
        results_path = self.data_manager.save_test_results(
            self.results, 
            "global_workspace_tests", 
            timestamp
        )
        
        # Generate and save comprehensive report
        report_path = self.data_manager.export_comprehensive_report(
            self.results, 
            "global_workspace"
        )
        
        logger.info(f"Test results saved to: {results_path}")
        logger.info(f"Comprehensive report exported to: {report_path}")
    
    def _generate_visualizations(self):
        """Generate comprehensive visualizations."""
        logger.info("Generating visualizations...")
        
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        viz_dir = self.data_manager.base_path / f"visualizations_{timestamp}"
        viz_dir.mkdir(exist_ok=True)
        
        try:
            # 1. Competition dynamics visualization
            if 'competition_tests' in self.results:
                # Create mock competition history for visualization
                mock_workspace = MockGlobalWorkspace(self.config)
                
                # Generate sample competition data
                competition_history = []
                for i in range(50):
                    # Add competitors
                    n_competitors = np.random.randint(3, 8)
                    for j in range(n_competitors):
                        content = ContentGenerator(self.config).generate_content_batch(1)[0]
                        mock_workspace.add_competitor(
                            content['id'], content['features'], 
                            np.random.uniform(0.5, 1.5), content
                        )
                    
                    # Run competition
                    winner = mock_workspace.compete_for_access()
                    
                    # Record competition
                    if len(mock_workspace.competition_history) > 0:
                        competition_history.append(mock_workspace.competition_history[-1])
                    
                    mock_workspace.clear_competitors()
                
                if competition_history:
                    self.visualizer.plot_competition_dynamics(
                        competition_history,
                        save_path=str(viz_dir / "competition_dynamics.png")
                    )
            
            # 2. Workspace evolution visualization
            if 'dynamics_tests' in self.results:
                # Create mock workspace history
                mock_workspace = MockGlobalWorkspace(self.config)
                
                for i in range(30):
                    # Add content
                    content = ContentGenerator(self.config).generate_content_batch(1)[0]
                    coalition_id = mock_workspace.add_competitor(
                        content['id'], content['features'], 
                        np.random.uniform(0.8, 1.5), content
                    )
                    
                    # Broadcast
                    mock_workspace.broadcast_to_workspace(coalition_id)
                    
                    # Update dynamics
                    mock_workspace.update_workspace_dynamics()
                    
                    mock_workspace.clear_competitors()
                
                if len(mock_workspace.workspace_history) > 0:
                    self.visualizer.plot_workspace_evolution(
                        list(mock_workspace.workspace_history),
                        save_path=str(viz_dir / "workspace_evolution.png")
                    )
            
            # 3. Coalition analysis visualization
            if 'coalition_tests' in self.results:
                coalition_data = self.results['coalition_tests']
                
                # Combine coalition data for visualization
                viz_data = {}
                if 'coalition_size_distribution' in coalition_data:
                    size_data = coalition_data['coalition_size_distribution']
                    viz_data.update(size_data)
                
                if 'coalition_formation_algorithm' in coalition_data:
                    formation_data = coalition_data['coalition_formation_algorithm']
                    viz_data['avg_synergy_score'] = formation_data['avg_synergy_score']
                
                if viz_data:
                    self.visualizer.plot_coalition_analysis(
                        viz_data,
                        save_path=str(viz_dir / "coalition_analysis.png")
                    )
            
            # 4. Broadcast influence network
            if 'broadcast_tests' in self.results:
                # Create mock influence data
                n_targets = 8
                influence_matrix = np.random.rand(n_targets, n_targets) * 0.5
                np.fill_diagonal(influence_matrix, 0)
                
                influence_data = {
                    'influence_matrix': influence_matrix,
                    'node_names': [f'Target_{i}' for i in range(n_targets)]
                }
                
                self.visualizer.plot_broadcast_influence_network(
                    influence_data,
                    save_path=str(viz_dir / "broadcast_influence_network.png")
                )
            
            # 5. Performance dashboard
            if 'performance_tests' in self.results:
                self.visualizer.plot_performance_dashboard(
                    self.results['performance_tests'],
                    save_path=str(viz_dir / "performance_dashboard.png")
                )
            
            logger.info(f"Visualizations saved to: {viz_dir}")
            
        except Exception as e:
            logger.warning(f"Visualization generation failed: {str(e)}")

# ============================================================================
# Pytest Integration
# ============================================================================

# Test fixtures for pytest integration
@pytest.fixture
def workspace_config():
    """Pytest fixture for workspace test configuration."""
    return GlobalWorkspaceTestConfig()

@pytest.fixture
def content_generator(workspace_config):
    """Pytest fixture for content generator."""
    return ContentGenerator(workspace_config)

@pytest.fixture
def mock_workspace(workspace_config):
    """Pytest fixture for mock workspace."""
    return MockGlobalWorkspace(workspace_config)

@pytest.fixture
def workspace_validator():
    """Pytest fixture for workspace validator."""
    return GlobalWorkspaceMathValidator()

@pytest.fixture
def test_suite(workspace_config):
    """Pytest fixture for complete test suite."""
    return GlobalWorkspaceTestSuite(workspace_config)

# Individual pytest test functions
def test_competition_probability_calculation(workspace_config):
    """Pytest wrapper for competition probability tests."""
    test_instance = TestWorkspaceCompetition(workspace_config)
    results = test_instance.test_competition_probability_calculation()
    
    for case_name, case_data in results.items():
        validation = case_data['validation']
        for check, passed in validation.items():
            assert passed, f"Competition validation failed for {case_name}: {check}"

def test_coalition_formation_algorithm(workspace_config):
    """Pytest wrapper for coalition formation tests."""
    test_instance = TestCoalitionFormation(workspace_config)
    results = test_instance.test_coalition_formation_algorithm()
    
    validation = results['validation']
    for check, passed in validation.items():
        assert passed, f"Coalition formation validation failed: {check}"

def test_broadcast_influence_propagation(workspace_config):
    """Pytest wrapper for broadcast tests."""
    test_instance = TestBroadcastMechanisms(workspace_config)
    results = test_instance.test_broadcast_influence_propagation()
    
    validation = results['validation']
    for check, passed in validation.items():
        assert passed, f"Broadcast validation failed: {check}"

def test_workspace_decay_dynamics(workspace_config):
    """Pytest wrapper for workspace dynamics tests."""
    test_instance = TestWorkspaceDynamics(workspace_config)
    results = test_instance.test_workspace_decay_dynamics()
    
    assert results['initial_strength'] > results['final_strength'], "Workspace content should decay"
    assert results['decay_steps'] > 0, "Should have decay progression"

def test_performance_benchmarks(workspace_config):
    """Pytest wrapper for performance tests."""
    test_instance = TestPerformanceBenchmarks(workspace_config)
    
    # Test competition performance
    comp_results = test_instance.test_competition_performance()
    competition_rate_50 = comp_results[50]['competitions_per_second']
    assert competition_rate_50 >= workspace_config.min_competitions_per_second
    
    # Test broadcast performance
    broadcast_results = test_instance.test_broadcast_performance()
    assert broadcast_results['broadcasts_per_second'] >= workspace_config.min_broadcasts_per_second
    assert broadcast_results['memory_usage_mb'] <= workspace_config.max_memory_usage_mb

def test_consciousness_integration(workspace_config):
    """Pytest wrapper for integration tests."""
    test_instance = TestIntegration(workspace_config)
    results = test_instance.test_consciousness_integration()
    
    for content_type, integration_data in results.items():
        assert integration_data['integration_strength'] > 0.1, \
            f"Integration strength too low for {content_type}"

# ============================================================================
# Main Execution and Command Line Interface
# ============================================================================

def run_comprehensive_workspace_tests(config: Optional[GlobalWorkspaceTestConfig] = None,
                                     save_results: bool = True,
                                     generate_visualizations: bool = True,
                                     run_performance_tests: bool = True,
                                     specific_category: Optional[str] = None) -> Dict[str, Any]:
    """
    Run comprehensive global workspace tests with optional category filtering.
    
    Args:
        config: Test configuration
        save_results: Whether to save results to files
        generate_visualizations: Whether to generate visualizations
        run_performance_tests: Whether to run performance benchmarks
        specific_category: Optional specific test category to run
        
    Returns:
        Complete test results dictionary
    """
    test_suite = GlobalWorkspaceTestSuite(config)
    
    if specific_category:
        results = {f"{specific_category}_tests": test_suite.run_specific_test_category(specific_category)}
    else:
        results = test_suite.run_all_tests(save_results, generate_visualizations, run_performance_tests)
    
    return results

def main():
    """Main execution function with command line argument handling."""
    import argparse
    
    parser = argparse.ArgumentParser(description='ULTRA Global Workspace Test Suite')
    parser.add_argument('--category', type=str, 
                       choices=['competition', 'coalition', 'broadcast', 'dynamics', 'performance', 'integration'],
                       help='Run specific test category only')
    parser.add_argument('--no-save', action='store_true', help='Do not save results to files')
    parser.add_argument('--no-viz', action='store_true', help='Do not generate visualizations')
    parser.add_argument('--no-perf', action='store_true', help='Skip performance benchmarks')
    parser.add_argument('--config-file', type=str, help='Path to configuration file')
    parser.add_argument('--output-dir', type=str, default='./global_workspace_test_data', 
                       help='Output directory for results')
    parser.add_argument('--verbose', action='store_true', help='Enable verbose logging')
    parser.add_argument('--quick', action='store_true', help='Run quick tests only (reduced iterations)')
    
    args = parser.parse_args()
    
    # Configure logging level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Load configuration
    if args.config_file:
        try:
            with open(args.config_file, 'r') as f:
                if args.config_file.endswith('.yaml') or args.config_file.endswith('.yml'):
                    import yaml
                    config_dict = yaml.safe_load(f)
                else:
                    config_dict = json.load(f)
            
            # Create config with loaded parameters
            config = GlobalWorkspaceTestConfig(**config_dict)
            logger.info(f"Configuration loaded from: {args.config_file}")
        except Exception as e:
            logger.error(f"Failed to load configuration: {str(e)}")
            config = GlobalWorkspaceTestConfig()
    else:
        config = GlobalWorkspaceTestConfig()
    
    # Adjust configuration for quick tests
    if args.quick:
        config.n_test_contents = 100
        config.n_temporal_steps = 50
        config.n_performance_trials = 10
        config.n_benchmark_iterations = 10
        logger.info("Running in quick test mode with reduced iterations")
    
    # Run tests
    try:
        results = run_comprehensive_workspace_tests(
            config=config,
            save_results=not args.no_save,
            generate_visualizations=not args.no_viz,
            run_performance_tests=not args.no_perf,
            specific_category=args.category
        )
        
        # Print summary
        if 'test_summary' in results:
            summary = results['test_summary']
            print(f"\n" + "="*80)
            print(f"GLOBAL WORKSPACE TEST EXECUTION COMPLETED")
            print(f"="*80)
            print(f"Execution time: {summary['execution_time']:.2f} seconds")
            print(f"Test categories: {summary['test_categories']}")
            print(f"Total tests: {summary['total_tests']}")
            print(f"Validation success rate: {summary['validation_summary']['overall_success_rate']:.2%}")
            
            if summary['performance_summary']:
                perf = summary['performance_summary']
                if 'competition_rate_50' in perf:
                    print(f"Competition rate (50 competitors): {perf['competition_rate_50']:.1f} ops/sec")
                if 'broadcast_rate' in perf:
                    print(f"Broadcast rate: {perf['broadcast_rate']:.1f} broadcasts/sec")
                if 'memory_usage_mb' in perf:
                    print(f"Memory usage: {perf['memory_usage_mb']:.1f} MB")
            
            if summary['workspace_metrics']:
                workspace = summary['workspace_metrics']
                if 'ordering_consistency_rate' in workspace:
                    print(f"Competition ordering consistency: {workspace['ordering_consistency_rate']:.2%}")
                if 'coalition_reduction_rate' in workspace:
                    print(f"Coalition reduction rate: {workspace['coalition_reduction_rate']:.2%}")
                if 'broadcast_efficiency_rate' in workspace:
                    print(f"Broadcast efficiency: {workspace['broadcast_efficiency_rate']:.2%}")
            
            print(f"="*80)
        
        return 0
        
    except Exception as e:
        logger.error(f"Test execution failed: {str(e)}")
        return 1

# ============================================================================
# Module Exports and Metadata
# ============================================================================

__all__ = [
    # Configuration
    'GlobalWorkspaceTestConfig',
    
    # Utilities
    'GlobalWorkspaceMathValidator',
    'ContentGenerator',
    'sigmoid',
    'tanh_safe',
    'softmax_stable',
    'cosine_similarity',
    'euclidean_distance',
    'manhattan_distance',
    'kl_divergence',
    'jensen_shannon_divergence',
    
    # Neural Networks
    'CompetitionNetwork',
    'BroadcastController',
    'WorkspaceStatePredictor',
    
    # Mock Systems
    'MockGlobalWorkspace',
    
    # Test Classes
    'TestWorkspaceCompetition',
    'TestCoalitionFormation',
    'TestBroadcastMechanisms',
    'TestWorkspaceDynamics',
    'TestPerformanceBenchmarks',
    'TestIntegration',
    
    # Visualization and Analysis
    'GlobalWorkspaceVisualizer',
    'GlobalWorkspaceDataManager',
    
    # Test Suite
    'GlobalWorkspaceTestSuite',
    
    # Main Functions
    'run_comprehensive_workspace_tests',
    'main',
    
    # Pytest Fixtures
    'workspace_config',
    'content_generator',
    'mock_workspace',
    'workspace_validator',
    'test_suite',
    
    # Pytest Test Functions
    'test_competition_probability_calculation',
    'test_coalition_formation_algorithm',
    'test_broadcast_influence_propagation',
    'test_workspace_decay_dynamics',
    'test_performance_benchmarks',
    'test_consciousness_integration'
]

# Module metadata
__version__ = '1.0.0'
__author__ = 'ULTRA Development Team'
__description__ = 'Comprehensive test suite for ULTRA Global Workspace component'
__license__ = 'MIT'

# Performance monitoring setup
_module_load_time = time.time()
logger.info(f"ULTRA Global Workspace Test Suite v{__version__} loaded successfully")
logger.info(f"Module load time: {time.time() - _module_load_time:.3f} seconds")
logger.info(f"Available test components: {len(__all__)} exports")

# Module-level constants
MILLER_MAGICAL_NUMBER = 7  # Miller's rule for workspace capacity
COMPETITION_TEMPERATURE_DEFAULT = 1.5
BROADCAST_DECAY_DEFAULT = 0.05
SYNERGY_THRESHOLD_DEFAULT = 0.7

# Conditional main execution
if __name__ == "__main__":
    import sys
    exit_code = main()
    
    # Final cleanup
    gc.collect()
    
    logger.info("Global Workspace test suite execution completed")
    sys.exit(exit_code)