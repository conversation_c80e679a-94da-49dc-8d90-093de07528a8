#!/usr/bin/env python3
"""
ULTRA: Ultimate Learning & Thought Reasoning Architecture
Test Suite for Intentionality System

This module contains comprehensive tests for the Intentionality System component
of the Emergent Consciousness Lattice, implementing goal-directed behavior,
hierarchical planning, and adaptive intention management.

Mathematical Foundations:
- Goal Hierarchy: G = {(g_i, p_i, v_i, d_i)}
- Plan Representation: P = {(g_j, a_j, s_j, e_j)}
- Progress Monitoring: progress(g_i) = (Σ_j w_j * c_j) / (Σ_j w_j)
- Plan Adaptation: P' = adapt(P, O, S)
"""

import pytest
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Tuple, Optional, Any, Set, Union
from dataclasses import dataclass, field
from enum import Enum
import uuid
import time
import logging
from collections import defaultdict, deque
import networkx as nx
from scipy.optimize import minimize
from sklearn.metrics.pairwise import cosine_similarity
import pickle
import json
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
import warnings
warnings.filterwarnings("ignore")

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class GoalStatus(Enum):
    """Enumeration of possible goal states"""
    PENDING = "pending"
    ACTIVE = "active"
    PAUSED = "paused"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class ActionType(Enum):
    """Enumeration of action types in plans"""
    COGNITIVE = "cognitive"
    MOTOR = "motor"
    COMMUNICATION = "communication"
    PERCEPTION = "perception"
    MEMORY = "memory"
    REASONING = "reasoning"


class PlanningStrategy(Enum):
    """Different planning approaches"""
    FORWARD_SEARCH = "forward_search"
    BACKWARD_SEARCH = "backward_search"
    HIERARCHICAL = "hierarchical"
    REACTIVE = "reactive"
    HYBRID = "hybrid"


@dataclass
class Goal:
    """
    Represents a goal in the intentionality system
    
    Mathematical representation: g_i = (description, parent, value, deadline, constraints)
    """
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    description: str = ""
    parent_id: Optional[str] = None
    value: float = 0.5  # Importance/priority [0, 1]
    deadline: Optional[float] = None  # Unix timestamp
    status: GoalStatus = GoalStatus.PENDING
    created_at: float = field(default_factory=time.time)
    completed_at: Optional[float] = None
    progress: float = 0.0  # Completion percentage [0, 1]
    constraints: Dict[str, Any] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)
    embedding: Optional[np.ndarray] = None  # Semantic embedding
    
    def __post_init__(self):
        if self.embedding is None:
            # Generate semantic embedding from description
            self.embedding = self._generate_embedding()
    
    def _generate_embedding(self) -> np.ndarray:
        """Generate semantic embedding for goal description"""
        # Simplified embedding using character-based features
        # In production, use transformer-based embeddings
        text = self.description.lower()
        vocab_size = 256
        embedding_dim = 128
        
        # Character frequency encoding
        char_freq = np.zeros(vocab_size)
        for char in text:
            if ord(char) < vocab_size:
                char_freq[ord(char)] += 1
        
        # Normalize and project to embedding space
        char_freq = char_freq / (np.sum(char_freq) + 1e-8)
        
        # Project to higher dimensional space using random projection
        np.random.seed(hash(self.description) % (2**32))
        projection_matrix = np.random.randn(vocab_size, embedding_dim) * 0.1
        embedding = np.dot(char_freq, projection_matrix)
        
        # Add positional and temporal encoding
        pos_encoding = np.sin(np.arange(embedding_dim) * 0.1)
        time_encoding = np.cos(time.time() * 0.001) * np.ones(embedding_dim) * 0.1
        
        embedding = embedding + pos_encoding + time_encoding
        return F.normalize(torch.tensor(embedding), dim=0).numpy()


@dataclass
class Action:
    """
    Represents an action within a plan
    
    Mathematical representation: a_j = (type, parameters, duration, preconditions, effects)
    """
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    type: ActionType = ActionType.COGNITIVE
    name: str = ""
    parameters: Dict[str, Any] = field(default_factory=dict)
    duration: float = 1.0  # Expected duration in seconds
    preconditions: List[str] = field(default_factory=list)  # Required conditions
    effects: List[str] = field(default_factory=list)  # Expected outcomes
    cost: float = 1.0  # Resource cost [0, inf]
    success_probability: float = 0.9  # Probability of success [0, 1]
    priority: float = 0.5  # Action priority [0, 1]
    
    def is_executable(self, current_state: Dict[str, Any]) -> bool:
        """Check if action can be executed given current state"""
        for precondition in self.preconditions:
            if not self._evaluate_condition(precondition, current_state):
                return False
        return True
    
    def _evaluate_condition(self, condition: str, state: Dict[str, Any]) -> bool:
        """Evaluate a logical condition against current state"""
        # Simplified condition evaluation
        # In production, implement full logical expression parser
        try:
            # Handle simple key existence checks
            if condition.startswith("has_"):
                key = condition[4:]
                return key in state and state[key] is not None
            
            # Handle equality checks
            if "==" in condition:
                key, expected = condition.split("==")
                key, expected = key.strip(), expected.strip()
                return str(state.get(key, "")).strip() == expected
            
            # Handle threshold checks
            if ">=" in condition:
                key, threshold = condition.split(">=")
                key, threshold = key.strip(), float(threshold.strip())
                return float(state.get(key, 0)) >= threshold
            
            if "<=" in condition:
                key, threshold = condition.split("<=")
                key, threshold = key.strip(), float(threshold.strip())
                return float(state.get(key, float('inf'))) <= threshold
            
            return True  # Default to true for unknown conditions
        except Exception:
            return False


@dataclass  
class Plan:
    """
    Represents a plan to achieve a goal
    
    Mathematical representation: P = {(g_j, a_j, s_j, e_j)}
    """
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    goal_id: str = ""
    actions: List[Action] = field(default_factory=list)
    start_time: Optional[float] = None
    end_time: Optional[float] = None
    strategy: PlanningStrategy = PlanningStrategy.FORWARD_SEARCH
    expected_cost: float = 0.0
    expected_duration: float = 0.0
    confidence: float = 0.5  # Confidence in plan success [0, 1]
    created_at: float = field(default_factory=time.time)
    
    def __post_init__(self):
        self._update_metrics()
    
    def _update_metrics(self):
        """Update plan metrics based on actions"""
        if not self.actions:
            return
            
        self.expected_cost = sum(action.cost for action in self.actions)
        self.expected_duration = sum(action.duration for action in self.actions)
        
        # Calculate confidence as product of action success probabilities
        success_probs = [action.success_probability for action in self.actions]
        self.confidence = np.prod(success_probs) if success_probs else 0.5
    
    def add_action(self, action: Action):
        """Add action to plan and update metrics"""
        self.actions.append(action)
        self._update_metrics()
    
    def get_critical_path(self) -> List[Action]:
        """Identify critical path in plan execution"""
        # For sequential plans, entire sequence is critical
        # For parallel plans, identify longest duration path
        return self.actions.copy()


class GoalGraph:
    """
    Manages hierarchical goal relationships using graph structure
    """
    
    def __init__(self):
        self.graph = nx.DiGraph()
        self.goals: Dict[str, Goal] = {}
        self._lock = threading.RLock()
    
    def add_goal(self, goal: Goal):
        """Add goal to the graph"""
        with self._lock:
            self.goals[goal.id] = goal
            self.graph.add_node(goal.id, **goal.__dict__)
            
            if goal.parent_id and goal.parent_id in self.goals:
                self.graph.add_edge(goal.parent_id, goal.id, relation="subgoal")
    
    def get_subgoals(self, goal_id: str) -> List[Goal]:
        """Get all direct subgoals of a goal"""
        with self._lock:
            if goal_id not in self.graph:
                return []
            return [self.goals[child_id] for child_id in self.graph.successors(goal_id)]
    
    def get_parent_goal(self, goal_id: str) -> Optional[Goal]:
        """Get parent goal"""
        with self._lock:
            if goal_id not in self.graph:
                return None
            parents = list(self.graph.predecessors(goal_id))
            return self.goals[parents[0]] if parents else None
    
    def get_root_goals(self) -> List[Goal]:
        """Get all root-level goals (no parents)"""
        with self._lock:
            return [self.goals[node_id] for node_id in self.graph.nodes() 
                   if self.graph.in_degree(node_id) == 0]
    
    def compute_goal_priority(self, goal_id: str) -> float:
        """
        Compute dynamic priority based on goal hierarchy and constraints
        
        Priority = base_value * urgency_factor * dependency_factor
        """
        with self._lock:
            if goal_id not in self.goals:
                return 0.0
            
            goal = self.goals[goal_id]
            base_priority = goal.value
            
            # Urgency factor based on deadline
            urgency_factor = 1.0
            if goal.deadline:
                time_remaining = goal.deadline - time.time()
                if time_remaining > 0:
                    # Exponential increase in urgency as deadline approaches
                    urgency_factor = np.exp(-time_remaining / 86400)  # 1 day scaling
                else:
                    urgency_factor = 10.0  # Past deadline
            
            # Dependency factor based on subgoals
            dependency_factor = 1.0
            subgoals = self.get_subgoals(goal_id)
            if subgoals:
                completed_subgoals = sum(1 for sg in subgoals if sg.status == GoalStatus.COMPLETED)
                dependency_factor = 1.0 + (completed_subgoals / len(subgoals))
            
            return base_priority * urgency_factor * dependency_factor
    
    def find_goal_conflicts(self) -> List[Tuple[str, str, str]]:
        """Identify conflicting goals based on constraints and resources"""
        conflicts = []
        goal_ids = list(self.goals.keys())
        
        for i, goal_id_1 in enumerate(goal_ids):
            for goal_id_2 in goal_ids[i+1:]:
                conflict_type = self._check_goal_conflict(goal_id_1, goal_id_2)
                if conflict_type:
                    conflicts.append((goal_id_1, goal_id_2, conflict_type))
        
        return conflicts
    
    def _check_goal_conflict(self, goal_id_1: str, goal_id_2: str) -> Optional[str]:
        """Check if two goals are in conflict"""
        goal1, goal2 = self.goals[goal_id_1], self.goals[goal_id_2]
        
        # Resource conflict
        if ("resources" in goal1.constraints and "resources" in goal2.constraints):
            resources1 = set(goal1.constraints["resources"])
            resources2 = set(goal2.constraints["resources"])
            if resources1 & resources2:  # Overlapping resources
                return "resource_conflict"
        
        # Temporal conflict
        if (goal1.deadline and goal2.deadline and 
            abs(goal1.deadline - goal2.deadline) < 3600):  # Within 1 hour
            return "temporal_conflict"
        
        # Semantic conflict using embeddings
        if goal1.embedding is not None and goal2.embedding is not None:
            similarity = cosine_similarity([goal1.embedding], [goal2.embedding])[0][0]
            if similarity < -0.5:  # Opposing goals
                return "semantic_conflict"
        
        return None


class PlanningEngine:
    """
    Advanced planning engine using multiple strategies
    """
    
    def __init__(self, max_planning_depth: int = 10, max_actions_per_plan: int = 50):
        self.max_planning_depth = max_planning_depth
        self.max_actions_per_plan = max_actions_per_plan
        self.action_templates: Dict[str, Action] = {}
        self.domain_knowledge: Dict[str, Any] = {}
        self._initialize_action_templates()
    
    def _initialize_action_templates(self):
        """Initialize common action templates"""
        templates = [
            Action(
                name="gather_information",
                type=ActionType.PERCEPTION,
                parameters={"source": "default", "topic": ""},
                duration=2.0,
                cost=0.5,
                success_probability=0.9
            ),
            Action(
                name="analyze_data",
                type=ActionType.REASONING,
                parameters={"data": "", "method": "statistical"},
                duration=5.0,
                cost=2.0,
                success_probability=0.8,
                preconditions=["has_data"]
            ),
            Action(
                name="make_decision",
                type=ActionType.COGNITIVE,
                parameters={"options": [], "criteria": []},
                duration=3.0,
                cost=1.5,
                success_probability=0.7,
                preconditions=["has_analysis"]
            ),
            Action(
                name="communicate_result",
                type=ActionType.COMMUNICATION,
                parameters={"recipient": "", "message": ""},
                duration=1.0,
                cost=0.5,
                success_probability=0.95,
                preconditions=["has_result"]
            )
        ]
        
        for template in templates:
            self.action_templates[template.name] = template
    
    def generate_plan(self, goal: Goal, current_state: Dict[str, Any], 
                     strategy: PlanningStrategy = PlanningStrategy.HYBRID) -> Plan:
        """
        Generate optimal plan for achieving goal using specified strategy
        """
        plan = Plan(goal_id=goal.id, strategy=strategy)
        
        if strategy == PlanningStrategy.FORWARD_SEARCH:
            self._forward_search_planning(goal, current_state, plan)
        elif strategy == PlanningStrategy.BACKWARD_SEARCH:
            self._backward_search_planning(goal, current_state, plan)
        elif strategy == PlanningStrategy.HIERARCHICAL:
            self._hierarchical_planning(goal, current_state, plan)
        elif strategy == PlanningStrategy.HYBRID:
            self._hybrid_planning(goal, current_state, plan)
        else:
            self._reactive_planning(goal, current_state, plan)
        
        self._optimize_plan(plan, current_state)
        return plan
    
    def _forward_search_planning(self, goal: Goal, current_state: Dict[str, Any], plan: Plan):
        """Forward search from current state to goal state"""
        state = current_state.copy()
        search_queue = deque([(state, [])])
        visited_states = set()
        
        while search_queue and len(plan.actions) < self.max_actions_per_plan:
            current_state, action_sequence = search_queue.popleft()
            
            # Create state signature for cycle detection
            state_sig = self._create_state_signature(current_state)
            if state_sig in visited_states:
                continue
            visited_states.add(state_sig)
            
            # Check if goal is achieved
            if self._is_goal_achieved(goal, current_state):
                for action in action_sequence:
                    plan.add_action(action)
                break
            
            # Generate applicable actions
            applicable_actions = self._get_applicable_actions(current_state)
            
            for action in applicable_actions[:5]:  # Limit branching factor
                new_state = self._apply_action_effects(action, current_state.copy())
                new_sequence = action_sequence + [action]
                
                if len(new_sequence) < self.max_planning_depth:
                    search_queue.append((new_state, new_sequence))
    
    def _backward_search_planning(self, goal: Goal, current_state: Dict[str, Any], plan: Plan):
        """Backward search from goal state to current state"""
        goal_conditions = self._extract_goal_conditions(goal)
        search_queue = deque([(goal_conditions, [])])
        
        while search_queue and len(plan.actions) < self.max_actions_per_plan:
            conditions, action_sequence = search_queue.popleft()
            
            # Check if current state satisfies conditions
            if self._conditions_satisfied(conditions, current_state):
                # Reverse action sequence for forward execution
                for action in reversed(action_sequence):
                    plan.add_action(action)
                break
            
            # Find actions that can achieve some conditions
            for condition in conditions:
                achieving_actions = self._find_actions_achieving_condition(condition)
                
                for action in achieving_actions[:3]:  # Limit branching
                    new_conditions = self._get_action_preconditions(action)
                    new_sequence = [action] + action_sequence
                    
                    if len(new_sequence) < self.max_planning_depth:
                        search_queue.append((new_conditions, new_sequence))
    
    def _hierarchical_planning(self, goal: Goal, current_state: Dict[str, Any], plan: Plan):
        """Hierarchical task network planning"""
        # Decompose goal into subgoals
        subgoals = self._decompose_goal(goal)
        
        for subgoal in subgoals:
            subplan = self._generate_primitive_plan(subgoal, current_state)
            for action in subplan.actions:
                plan.add_action(action)
            
            # Update state after subplan execution
            current_state = self._simulate_plan_execution(subplan, current_state)
    
    def _hybrid_planning(self, goal: Goal, current_state: Dict[str, Any], plan: Plan):
        """Combine multiple planning strategies"""
        # Try hierarchical first for complex goals
        if self._is_complex_goal(goal):
            self._hierarchical_planning(goal, current_state, plan)
        
        # If no plan generated, try forward search
        if not plan.actions:
            self._forward_search_planning(goal, current_state, plan)
        
        # If still no plan, try backward search
        if not plan.actions:
            self._backward_search_planning(goal, current_state, plan)
    
    def _reactive_planning(self, goal: Goal, current_state: Dict[str, Any], plan: Plan):
        """Simple reactive planning based on goal-action mappings"""
        action_templates = self._get_goal_relevant_actions(goal)
        
        for template in action_templates[:10]:  # Limit plan size
            if template.is_executable(current_state):
                action = self._instantiate_action_template(template, goal, current_state)
                plan.add_action(action)
                current_state = self._apply_action_effects(action, current_state)
                
                if self._is_goal_achieved(goal, current_state):
                    break
    
    def _optimize_plan(self, plan: Plan, current_state: Dict[str, Any]):
        """Optimize plan using local search and constraint satisfaction"""
        if not plan.actions:
            return
        
        # Remove redundant actions
        plan.actions = self._remove_redundant_actions(plan.actions)
        
        # Reorder actions for better efficiency
        plan.actions = self._optimize_action_order(plan.actions, current_state)
        
        # Parallelize independent actions
        plan.actions = self._parallelize_actions(plan.actions)
        
        # Update plan metrics
        plan._update_metrics()
    
    def _create_state_signature(self, state: Dict[str, Any]) -> str:
        """Create hashable signature for state"""
        return str(sorted(state.items()))
    
    def _is_goal_achieved(self, goal: Goal, state: Dict[str, Any]) -> bool:
        """Check if goal is achieved in given state"""
        # Extract goal conditions from description and constraints
        goal_conditions = self._extract_goal_conditions(goal)
        return self._conditions_satisfied(goal_conditions, state)
    
    def _extract_goal_conditions(self, goal: Goal) -> List[str]:
        """Extract logical conditions from goal description"""
        conditions = []
        
        # Parse goal description for conditions
        description = goal.description.lower()
        
        # Simple heuristics for common goal patterns
        if "complete" in description or "finish" in description:
            conditions.append("task_completed==true")
        if "learn" in description or "understand" in description:
            conditions.append("knowledge_acquired>=0.8")
        if "create" in description or "build" in description:
            conditions.append("artifact_created==true")
        if "communicate" in description or "inform" in description:
            conditions.append("message_sent==true")
        
        # Add explicit constraints
        if "success_criteria" in goal.constraints:
            conditions.extend(goal.constraints["success_criteria"])
        
        return conditions or ["goal_achieved==true"]  # Default condition
    
    def _conditions_satisfied(self, conditions: List[str], state: Dict[str, Any]) -> bool:
        """Check if all conditions are satisfied in state"""
        for condition in conditions:
            if not Action()._evaluate_condition(condition, state):
                return False
        return True
    
    def _get_applicable_actions(self, state: Dict[str, Any]) -> List[Action]:
        """Get all actions applicable in current state"""
        applicable = []
        for template in self.action_templates.values():
            if template.is_executable(state):
                applicable.append(template)
        return sorted(applicable, key=lambda a: a.priority, reverse=True)
    
    def _apply_action_effects(self, action: Action, state: Dict[str, Any]) -> Dict[str, Any]:
        """Apply action effects to state"""
        new_state = state.copy()
        
        for effect in action.effects:
            if "=" in effect:
                key, value = effect.split("=", 1)
                key = key.strip()
                
                # Try to convert value to appropriate type
                try:
                    if value.strip().lower() == "true":
                        new_state[key] = True
                    elif value.strip().lower() == "false":
                        new_state[key] = False
                    elif value.strip().replace(".", "").isdigit():
                        new_state[key] = float(value.strip())
                    else:
                        new_state[key] = value.strip()
                except:
                    new_state[key] = value.strip()
        
        return new_state
    
    def _find_actions_achieving_condition(self, condition: str) -> List[Action]:
        """Find actions that can achieve a specific condition"""
        achieving_actions = []
        
        for template in self.action_templates.values():
            if condition in template.effects:
                achieving_actions.append(template)
        
        return achieving_actions
    
    def _get_action_preconditions(self, action: Action) -> List[str]:
        """Get preconditions for an action"""
        return action.preconditions.copy()
    
    def _decompose_goal(self, goal: Goal) -> List[Goal]:
        """Decompose complex goal into subgoals"""
        subgoals = []
        
        # Simple decomposition based on goal description
        description = goal.description.lower()
        
        if "and" in description:
            # Split conjunctive goals
            parts = description.split("and")
            for i, part in enumerate(parts):
                subgoal = Goal(
                    description=part.strip(),
                    parent_id=goal.id,
                    value=goal.value / len(parts),
                    deadline=goal.deadline
                )
                subgoals.append(subgoal)
        else:
            # Create generic subgoals for complex tasks
            subgoals = [
                Goal(
                    description=f"Prepare for: {goal.description}",
                    parent_id=goal.id,
                    value=goal.value * 0.3
                ),
                Goal(
                    description=f"Execute: {goal.description}",
                    parent_id=goal.id,
                    value=goal.value * 0.6
                ),
                Goal(
                    description=f"Verify: {goal.description}",
                    parent_id=goal.id,
                    value=goal.value * 0.1
                )
            ]
        
        return subgoals
    
    def _generate_primitive_plan(self, goal: Goal, state: Dict[str, Any]) -> Plan:
        """Generate plan for primitive (non-decomposable) goal"""
        plan = Plan(goal_id=goal.id)
        
        # Use reactive planning for primitive goals
        relevant_actions = self._get_goal_relevant_actions(goal)
        
        for template in relevant_actions[:5]:
            if template.is_executable(state):
                action = self._instantiate_action_template(template, goal, state)
                plan.add_action(action)
                state = self._apply_action_effects(action, state)
        
        return plan
    
    def _simulate_plan_execution(self, plan: Plan, initial_state: Dict[str, Any]) -> Dict[str, Any]:
        """Simulate plan execution and return final state"""
        state = initial_state.copy()
        
        for action in plan.actions:
            if action.is_executable(state):
                state = self._apply_action_effects(action, state)
        
        return state
    
    def _is_complex_goal(self, goal: Goal) -> bool:
        """Check if goal is complex enough for hierarchical planning"""
        # Heuristics for goal complexity
        description = goal.description.lower()
        complexity_indicators = ["and", "or", "then", "after", "before", "while"]
        
        return (len(description.split()) > 10 or 
                any(indicator in description for indicator in complexity_indicators) or
                len(goal.constraints) > 2)
    
    def _get_goal_relevant_actions(self, goal: Goal) -> List[Action]:
        """Get actions relevant to achieving the goal"""
        relevant_actions = []
        goal_embedding = goal.embedding
        
        for template in self.action_templates.values():
            # Simple relevance scoring based on action name similarity
            action_text = f"{template.name} {' '.join(template.parameters.keys())}"
            action_embedding = Goal(description=action_text).embedding
            
            if goal_embedding is not None and action_embedding is not None:
                similarity = cosine_similarity([goal_embedding], [action_embedding])[0][0]
                if similarity > 0.1:  # Threshold for relevance
                    template.priority = similarity
                    relevant_actions.append(template)
        
        return sorted(relevant_actions, key=lambda a: a.priority, reverse=True)
    
    def _instantiate_action_template(self, template: Action, goal: Goal, 
                                   state: Dict[str, Any]) -> Action:
        """Create specific action instance from template"""
        action = Action(
            name=template.name,
            type=template.type,
            parameters=template.parameters.copy(),
            duration=template.duration,
            cost=template.cost,
            success_probability=template.success_probability,
            preconditions=template.preconditions.copy(),
            effects=template.effects.copy()
        )
        
        # Customize parameters based on goal and state
        if "topic" in action.parameters:
            action.parameters["topic"] = goal.description
        if "goal_id" not in action.parameters:
            action.parameters["goal_id"] = goal.id
        
        return action
    
    def _remove_redundant_actions(self, actions: List[Action]) -> List[Action]:
        """Remove redundant actions from plan"""
        unique_actions = []
        seen_effects = set()
        
        for action in actions:
            action_effects = tuple(sorted(action.effects))
            if action_effects not in seen_effects or not action.effects:
                unique_actions.append(action)
                seen_effects.add(action_effects)
        
        return unique_actions
    
    def _optimize_action_order(self, actions: List[Action], 
                              initial_state: Dict[str, Any]) -> List[Action]:
        """Optimize order of actions for better execution"""
        if len(actions) <= 1:
            return actions
        
        # Topological sort based on precondition-effect dependencies
        dependency_graph = nx.DiGraph()
        
        # Add actions as nodes
        for i, action in enumerate(actions):
            dependency_graph.add_node(i, action=action)
        
        # Add edges for dependencies
        for i, action1 in enumerate(actions):
            for j, action2 in enumerate(actions):
                if i != j:
                    # Check if action1's effects satisfy action2's preconditions
                    if any(effect.split("=")[0].strip() in " ".join(action2.preconditions) 
                          for effect in action1.effects if "=" in effect):
                        dependency_graph.add_edge(i, j)
        
        try:
            # Topological sort
            sorted_indices = list(nx.topological_sort(dependency_graph))
            return [actions[i] for i in sorted_indices]
        except nx.NetworkXError:
            # Fall back to original order if cycles exist
            return actions
    
    def _parallelize_actions(self, actions: List[Action]) -> List[Action]:
        """Identify actions that can be executed in parallel"""
        # For now, keep sequential execution
        # In production, implement parallel action scheduling
        return actions


class IntentionalitySystem:
    """
    Main intentionality system implementing goal-directed behavior and planning
    
    Mathematical Foundation:
    - Goal Hierarchy: G = {(g_i, p_i, v_i, d_i)}
    - Plan Representation: P = {(g_j, a_j, s_j, e_j)}  
    - Progress Monitoring: progress(g_i) = (Σ_j w_j * c_j) / (Σ_j w_j)
    - Plan Adaptation: P' = adapt(P, O, S)
    """
    
    def __init__(self, max_active_goals: int = 10, planning_horizon: int = 100):
        self.goal_graph = GoalGraph()
        self.planning_engine = PlanningEngine()
        self.active_plans: Dict[str, Plan] = {}
        self.execution_history: List[Dict[str, Any]] = []
        self.current_state: Dict[str, Any] = {}
        self.max_active_goals = max_active_goals
        self.planning_horizon = planning_horizon
        
        # Learning components
        self.goal_success_rates: Dict[str, float] = defaultdict(lambda: 0.5)
        self.action_effectiveness: Dict[str, float] = defaultdict(lambda: 0.5)
        self.planning_strategy_performance: Dict[PlanningStrategy, float] = {
            strategy: 0.5 for strategy in PlanningStrategy
        }
        
        # Metrics and monitoring
        self.performance_metrics: Dict[str, float] = {
            "goal_completion_rate": 0.0,
            "average_planning_time": 0.0,
            "plan_success_rate": 0.0,
            "resource_efficiency": 0.0
        }
        
        self._lock = threading.RLock()
        self._running = False
        self._monitor_thread = None
    
    def start(self):
        """Start the intentionality system"""
        with self._lock:
            if not self._running:
                self._running = True
                self._monitor_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
                self._monitor_thread.start()
                logger.info("Intentionality system started")
    
    def stop(self):
        """Stop the intentionality system"""
        with self._lock:
            self._running = False
            if self._monitor_thread:
                self._monitor_thread.join(timeout=1.0)
            logger.info("Intentionality system stopped")
    
    def add_goal(self, description: str, parent_id: Optional[str] = None,
                value: float = 0.5, deadline: Optional[float] = None,
                constraints: Optional[Dict[str, Any]] = None) -> str:
        """Add a new goal to the system"""
        goal = Goal(
            description=description,
            parent_id=parent_id,
            value=value,
            deadline=deadline,
            constraints=constraints or {}
        )
        
        with self._lock:
            self.goal_graph.add_goal(goal)
            
            # Automatically generate plan for new goals
            if len(self.active_plans) < self.max_active_goals:
                self._generate_plan_for_goal(goal)
        
        logger.info(f"Added goal: {goal.id} - {description}")
        return goal.id
    
    def remove_goal(self, goal_id: str) -> bool:
        """Remove goal and associated plans"""
        with self._lock:
            if goal_id in self.goal_graph.goals:
                # Remove associated plan
                if goal_id in self.active_plans:
                    del self.active_plans[goal_id]
                
                # Mark goal as cancelled
                goal = self.goal_graph.goals[goal_id]
                goal.status = GoalStatus.CANCELLED
                
                logger.info(f"Removed goal: {goal_id}")
                return True
            return False
    
    def update_goal_progress(self, goal_id: str, progress: float):
        """Update progress for a specific goal"""
        with self._lock:
            if goal_id in self.goal_graph.goals:
                goal = self.goal_graph.goals[goal_id]
                goal.progress = max(0.0, min(1.0, progress))
                
                if progress >= 1.0:
                    self._mark_goal_completed(goal_id)
    
    def get_current_intention(self) -> Optional[Goal]:
        """Get the highest priority active goal"""
        with self._lock:
            active_goals = [goal for goal in self.goal_graph.goals.values() 
                          if goal.status == GoalStatus.ACTIVE]
            
            if not active_goals:
                return None
            
            # Compute dynamic priorities
            priorities = [(goal, self.goal_graph.compute_goal_priority(goal.id)) 
                         for goal in active_goals]
            
            return max(priorities, key=lambda x: x[1])[0] if priorities else None
    
    def get_active_goals(self) -> List[Goal]:
        """Get all currently active goals"""
        with self._lock:
            return [goal for goal in self.goal_graph.goals.values() 
                   if goal.status == GoalStatus.ACTIVE]
    
    def get_goal_hierarchy(self) -> Dict[str, Any]:
        """Get hierarchical representation of all goals"""
        with self._lock:
            hierarchy = {}
            root_goals = self.goal_graph.get_root_goals()
            
            for root_goal in root_goals:
                hierarchy[root_goal.id] = self._build_goal_subtree(root_goal.id)
            
            return hierarchy
    
    def update_current_state(self, state_updates: Dict[str, Any]):
        """Update the current state of the system"""
        with self._lock:
            self.current_state.update(state_updates)
            
            # Trigger plan adaptation if needed
            self._adapt_plans_to_state_change()
    
    def execute_next_action(self) -> Optional[Action]:
        """Execute the next highest priority action"""
        with self._lock:
            current_intention = self.get_current_intention()
            if not current_intention:
                return None
            
            plan = self.active_plans.get(current_intention.id)
            if not plan or not plan.actions:
                return None
            
            # Find next executable action
            for i, action in enumerate(plan.actions):
                if action.is_executable(self.current_state):
                    # Remove executed action from plan
                    executed_action = plan.actions.pop(i)
                    
                    # Apply action effects to current state
                    self._apply_action_effects(executed_action)
                    
                    # Record execution
                    self._record_action_execution(executed_action, current_intention.id)
                    
                    return executed_action
            
            return None
    
    def get_plan_for_goal(self, goal_id: str) -> Optional[Plan]:
        """Get the current plan for a specific goal"""
        with self._lock:
            return self.active_plans.get(goal_id)
    
    def regenerate_plan(self, goal_id: str, strategy: Optional[PlanningStrategy] = None) -> bool:
        """Regenerate plan for a specific goal"""
        with self._lock:
            if goal_id not in self.goal_graph.goals:
                return False
            
            goal = self.goal_graph.goals[goal_id]
            
            # Select strategy based on performance if not specified
            if strategy is None:
                strategy = self._select_best_planning_strategy()
            
            # Generate new plan
            start_time = time.time()
            new_plan = self.planning_engine.generate_plan(goal, self.current_state, strategy)
            planning_time = time.time() - start_time
            
            # Update performance metrics
            self._update_planning_metrics(planning_time)
            
            # Replace old plan
            self.active_plans[goal_id] = new_plan
            
            logger.info(f"Regenerated plan for goal {goal_id} using {strategy.value}")
            return True
    
    def get_performance_metrics(self) -> Dict[str, float]:
        """Get current performance metrics"""
        with self._lock:
            return self.performance_metrics.copy()
    
    def save_state(self, filepath: str):
        """Save system state to file"""
        with self._lock:
            state_data = {
                "goals": {goal_id: goal.__dict__ for goal_id, goal in self.goal_graph.goals.items()},
                "plans": {plan_id: plan.__dict__ for plan_id, plan in self.active_plans.items()},
                "current_state": self.current_state,
                "performance_metrics": self.performance_metrics,
                "goal_success_rates": dict(self.goal_success_rates),
                "action_effectiveness": dict(self.action_effectiveness)
            }
            
            with open(filepath, 'wb') as f:
                pickle.dump(state_data, f)
            
            logger.info(f"System state saved to {filepath}")
    
    def load_state(self, filepath: str):
        """Load system state from file"""
        with self._lock:
            try:
                with open(filepath, 'rb') as f:
                    state_data = pickle.load(f)
                
                # Restore goals
                for goal_dict in state_data["goals"].values():
                    goal = Goal(**goal_dict)
                    self.goal_graph.add_goal(goal)
                
                # Restore plans
                for plan_dict in state_data["plans"].values():
                    plan = Plan(**plan_dict)
                    self.active_plans[plan.goal_id] = plan
                
                # Restore other state
                self.current_state = state_data["current_state"]
                self.performance_metrics = state_data["performance_metrics"]
                self.goal_success_rates = defaultdict(lambda: 0.5, state_data["goal_success_rates"])
                self.action_effectiveness = defaultdict(lambda: 0.5, state_data["action_effectiveness"])
                
                logger.info(f"System state loaded from {filepath}")
                
            except Exception as e:
                logger.error(f"Failed to load state: {e}")
    
    def _generate_plan_for_goal(self, goal: Goal):
        """Generate and store plan for a goal"""
        strategy = self._select_best_planning_strategy()
        
        start_time = time.time()
        plan = self.planning_engine.generate_plan(goal, self.current_state, strategy)
        planning_time = time.time() - start_time
        
        self.active_plans[goal.id] = plan
        goal.status = GoalStatus.ACTIVE
        
        # Update metrics
        self._update_planning_metrics(planning_time)
        
        logger.info(f"Generated plan for goal {goal.id} with {len(plan.actions)} actions")
    
    def _select_best_planning_strategy(self) -> PlanningStrategy:
        """Select planning strategy based on historical performance"""
        best_strategy = max(self.planning_strategy_performance.items(), 
                           key=lambda x: x[1])[0]
        
        # Add some exploration
        if np.random.random() < 0.1:  # 10% exploration
            return np.random.choice(list(PlanningStrategy))
        
        return best_strategy
    
    def _mark_goal_completed(self, goal_id: str):
        """Mark goal as completed and update metrics"""
        goal = self.goal_graph.goals[goal_id]
        goal.status = GoalStatus.COMPLETED
        goal.completed_at = time.time()
        
        # Update success rate
        goal_type = self._classify_goal_type(goal)
        old_rate = self.goal_success_rates[goal_type]
        self.goal_success_rates[goal_type] = old_rate * 0.9 + 0.1  # Exponential moving average
        
        # Remove from active plans
        if goal_id in self.active_plans:
            plan = self.active_plans[goal_id]
            strategy = plan.strategy
            
            # Update strategy performance
            old_perf = self.planning_strategy_performance[strategy]
            self.planning_strategy_performance[strategy] = old_perf * 0.9 + 0.1
            
            del self.active_plans[goal_id]
        
        # Check if parent goal can be completed
        parent_goal = self.goal_graph.get_parent_goal(goal_id)
        if parent_goal:
            self._check_parent_goal_completion(parent_goal.id)
        
        logger.info(f"Goal completed: {goal_id}")
    
    def _check_parent_goal_completion(self, parent_goal_id: str):
        """Check if parent goal should be marked as completed"""
        subgoals = self.goal_graph.get_subgoals(parent_goal_id)
        
        if subgoals:
            completed_subgoals = sum(1 for sg in subgoals 
                                   if sg.status == GoalStatus.COMPLETED)
            completion_ratio = completed_subgoals / len(subgoals)
            
            # Update parent goal progress
            parent_goal = self.goal_graph.goals[parent_goal_id]
            parent_goal.progress = completion_ratio
            
            # Mark as completed if all subgoals are done
            if completion_ratio >= 1.0:
                self._mark_goal_completed(parent_goal_id)
    
    def _apply_action_effects(self, action: Action):
        """Apply action effects to current state"""
        for effect in action.effects:
            if "=" in effect:
                key, value = effect.split("=", 1)
                key = key.strip()
                
                # Convert value to appropriate type
                try:
                    if value.strip().lower() == "true":
                        self.current_state[key] = True
                    elif value.strip().lower() == "false":
                        self.current_state[key] = False
                    elif value.strip().replace(".", "").isdigit():
                        self.current_state[key] = float(value.strip())
                    else:
                        self.current_state[key] = value.strip()
                except:
                    self.current_state[key] = value.strip()
    
    def _record_action_execution(self, action: Action, goal_id: str):
        """Record action execution for learning and analysis"""
        execution_record = {
            "timestamp": time.time(),
            "action": action.__dict__,
            "goal_id": goal_id,
            "state_before": self.current_state.copy()
        }
        
        self.execution_history.append(execution_record)
        
        # Keep history size manageable
        if len(self.execution_history) > 1000:
            self.execution_history = self.execution_history[-800:]  # Keep last 800
    
    def _adapt_plans_to_state_change(self):
        """Adapt existing plans based on state changes"""
        for goal_id, plan in list(self.active_plans.items()):
            # Check if plan is still valid
            if not self._is_plan_still_valid(plan):
                # Regenerate plan
                goal = self.goal_graph.goals[goal_id]
                strategy = plan.strategy  # Keep same strategy initially
                
                new_plan = self.planning_engine.generate_plan(goal, self.current_state, strategy)
                self.active_plans[goal_id] = new_plan
                
                logger.info(f"Adapted plan for goal {goal_id} due to state change")
    
    def _is_plan_still_valid(self, plan: Plan) -> bool:
        """Check if a plan is still valid given current state"""
        if not plan.actions:
            return False
        
        # Check if first few actions are still executable
        executable_actions = 0
        for action in plan.actions[:3]:  # Check first 3 actions
            if action.is_executable(self.current_state):
                executable_actions += 1
        
        return executable_actions > 0
    
    def _build_goal_subtree(self, goal_id: str) -> Dict[str, Any]:
        """Build hierarchical subtree for a goal"""
        goal = self.goal_graph.goals[goal_id]
        subtree = {
            "goal": goal.__dict__,
            "subgoals": {}
        }
        
        subgoals = self.goal_graph.get_subgoals(goal_id)
        for subgoal in subgoals:
            subtree["subgoals"][subgoal.id] = self._build_goal_subtree(subgoal.id)
        
        return subtree
    
    def _classify_goal_type(self, goal: Goal) -> str:
        """Classify goal type for performance tracking"""
        description = goal.description.lower()
        
        if any(word in description for word in ["learn", "understand", "study"]):
            return "learning"
        elif any(word in description for word in ["create", "build", "make"]):
            return "creation"
        elif any(word in description for word in ["communicate", "inform", "tell"]):
            return "communication"
        elif any(word in description for word in ["analyze", "evaluate", "assess"]):
            return "analysis"
        else:
            return "general"
    
    def _update_planning_metrics(self, planning_time: float):
        """Update planning performance metrics"""
        # Update average planning time with exponential moving average
        alpha = 0.1
        old_avg = self.performance_metrics["average_planning_time"]
        self.performance_metrics["average_planning_time"] = (
            old_avg * (1 - alpha) + planning_time * alpha
        )
    
    def _monitoring_loop(self):
        """Background monitoring loop for system health and metrics"""
        while self._running:
            try:
                with self._lock:
                    self._update_performance_metrics()
                    self._check_goal_deadlines()
                    self._optimize_resource_allocation()
                
                time.sleep(5.0)  # Monitor every 5 seconds
                
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                time.sleep(1.0)
    
    def _update_performance_metrics(self):
        """Update system performance metrics"""
        total_goals = len(self.goal_graph.goals)
        if total_goals == 0:
            return
        
        completed_goals = sum(1 for goal in self.goal_graph.goals.values() 
                            if goal.status == GoalStatus.COMPLETED)
        
        self.performance_metrics["goal_completion_rate"] = completed_goals / total_goals
        
        # Calculate plan success rate
        successful_plans = 0
        total_plans = len(self.active_plans) + completed_goals
        
        if total_plans > 0:
            successful_plans = completed_goals  # Simplified metric
            self.performance_metrics["plan_success_rate"] = successful_plans / total_plans
        
        # Resource efficiency (simplified)
        if self.execution_history:
            recent_actions = [record for record in self.execution_history[-50:]]
            if recent_actions:
                avg_cost = np.mean([record["action"]["cost"] for record in recent_actions])
                self.performance_metrics["resource_efficiency"] = 1.0 / (1.0 + avg_cost)
    
    def _check_goal_deadlines(self):
        """Check for goals approaching or past deadlines"""
        current_time = time.time()
        
        for goal in self.goal_graph.goals.values():
            if goal.deadline and goal.status == GoalStatus.ACTIVE:
                time_remaining = goal.deadline - current_time
                
                if time_remaining < 0:
                    # Past deadline
                    goal.status = GoalStatus.FAILED
                    logger.warning(f"Goal {goal.id} failed due to deadline")
                elif time_remaining < 3600:  # Less than 1 hour remaining
                    # Increase priority or regenerate plan
                    if goal.id in self.active_plans:
                        self.regenerate_plan(goal.id, PlanningStrategy.REACTIVE)
    
    def _optimize_resource_allocation(self):
        """Optimize allocation of resources across active goals"""
        active_goals = self.get_active_goals()
        
        if len(active_goals) <= self.max_active_goals:
            return
        
        # Sort goals by priority and keep only top ones
        priorities = [(goal, self.goal_graph.compute_goal_priority(goal.id)) 
                     for goal in active_goals]
        priorities.sort(key=lambda x: x[1], reverse=True)
        
        # Pause lower priority goals
        for goal, _ in priorities[self.max_active_goals:]:
            goal.status = GoalStatus.PAUSED
            if goal.id in self.active_plans:
                del self.active_plans[goal.id]
            
            logger.info(f"Paused goal {goal.id} due to resource constraints")


# Test Cases
class TestIntentionalitySystem:
    """Comprehensive test suite for the Intentionality System"""
    
    @pytest.fixture
    def intentionality_system(self):
        """Create a fresh intentionality system for testing"""
        system = IntentionalitySystem(max_active_goals=5, planning_horizon=50)
        system.start()
        yield system
        system.stop()
    
    @pytest.fixture
    def sample_goals(self):
        """Create sample goals for testing"""
        return [
            {
                "description": "Learn machine learning fundamentals",
                "value": 0.8,
                "deadline": time.time() + 86400,  # 1 day from now
                "constraints": {
                    "resources": ["time", "computational_power"],
                    "success_criteria": ["knowledge_acquired>=0.8"]
                }
            },
            {
                "description": "Build a neural network classifier",
                "value": 0.9,
                "deadline": time.time() + 172800,  # 2 days from now
                "constraints": {
                    "resources": ["time", "data", "computational_power"],
                    "success_criteria": ["artifact_created==true", "accuracy>=0.85"]
                }
            },
            {
                "description": "Write research paper and publish results",
                "value": 0.7,
                "deadline": time.time() + 604800,  # 1 week from now
                "constraints": {
                    "resources": ["time", "writing_tools"],
                    "success_criteria": ["paper_written==true", "paper_submitted==true"]
                }
            }
        ]
    
    def test_goal_creation_and_management(self, intentionality_system, sample_goals):
        """Test basic goal creation and management functionality"""
        system = intentionality_system
        
        # Test goal creation
        goal_ids = []
        for goal_data in sample_goals:
            goal_id = system.add_goal(**goal_data)
            assert goal_id is not None
            assert len(goal_id) > 0
            goal_ids.append(goal_id)
        
        # Test goal retrieval
        active_goals = system.get_active_goals()
        assert len(active_goals) == len(sample_goals)
        
        # Test goal hierarchy
        hierarchy = system.get_goal_hierarchy()
        assert len(hierarchy) == len(sample_goals)  # All root goals
        
        # Test goal removal
        removed = system.remove_goal(goal_ids[0])
        assert removed is True
        
        active_goals = system.get_active_goals()
        assert len(active_goals) == len(sample_goals) - 1
    
    def test_hierarchical_goal_structure(self, intentionality_system):
        """Test hierarchical goal relationships"""
        system = intentionality_system
        
        # Create parent goal
        parent_id = system.add_goal(
            description="Complete machine learning project",
            value=0.9,
            deadline=time.time() + 604800
        )
        
        # Create subgoals
        subgoal_ids = []
        subgoals = [
            "Collect and preprocess data",
            "Train and evaluate model", 
            "Deploy model to production"
        ]
        
        for subgoal_desc in subgoals:
            subgoal_id = system.add_goal(
                description=subgoal_desc,
                parent_id=parent_id,
                value=0.7,
                deadline=time.time() + 86400
            )
            subgoal_ids.append(subgoal_id)
        
        # Test hierarchy structure
        hierarchy = system.get_goal_hierarchy()
        assert parent_id in hierarchy
        
        parent_subtree = hierarchy[parent_id]
        assert len(parent_subtree["subgoals"]) == len(subgoals)
        
        # Test goal completion propagation
        for subgoal_id in subgoal_ids:
            system.update_goal_progress(subgoal_id, 1.0)
        
        # Allow some time for processing
        time.sleep(0.1)
        
        # Parent goal should be completed
        parent_goal = system.goal_graph.goals[parent_id]
        assert parent_goal.status == GoalStatus.COMPLETED
    
    def test_planning_and_execution(self, intentionality_system):
        """Test plan generation and execution"""
        system = intentionality_system
        
        # Create goal with specific success criteria
        goal_id = system.add_goal(
            description="Analyze dataset and generate report",
            value=0.8,
            constraints={
                "success_criteria": ["data_analyzed==true", "report_generated==true"]
            }
        )
        
        # Allow time for plan generation
        time.sleep(0.1)
        
        # Check that plan was generated
        plan = system.get_plan_for_goal(goal_id)
        assert plan is not None
        assert len(plan.actions) > 0
        
        # Test action execution
        initial_action_count = len(plan.actions)
        executed_action = system.execute_next_action()
        
        if executed_action:
            # Plan should have one less action
            updated_plan = system.get_plan_for_goal(goal_id)
            if updated_plan:
                assert len(updated_plan.actions) == initial_action_count - 1
    
    def test_plan_adaptation(self, intentionality_system):
        """Test plan adaptation to state changes"""
        system = intentionality_system
        
        # Create goal
        goal_id = system.add_goal(
            description="Complete data processing task",
            value=0.7
        )
        
        # Allow time for initial plan generation
        time.sleep(0.1)
        
        original_plan = system.get_plan_for_goal(goal_id)
        assert original_plan is not None
        
        # Change system state significantly
        system.update_current_state({
            "data_available": False,
            "processing_blocked": True,
            "alternative_data_source": True
        })
        
        # Allow time for plan adaptation
        time.sleep(0.1)
        
        # Plan should be adapted or regenerated
        adapted_plan = system.get_plan_for_goal(goal_id)
        assert adapted_plan is not None
        
        # Plan ID might change if completely regenerated
        # At minimum, the plan should be different from original
        assert (adapted_plan.id != original_plan.id or 
                len(adapted_plan.actions) != len(original_plan.actions))
    
    def test_goal_priority_computation(self, intentionality_system):
        """Test dynamic goal priority computation"""
        system = intentionality_system
        
        current_time = time.time()
        
        # Create goals with different urgency levels
        urgent_goal_id = system.add_goal(
            description="Urgent deadline task",
            value=0.6,
            deadline=current_time + 3600  # 1 hour
        )
        
        normal_goal_id = system.add_goal(
            description="Normal priority task",
            value=0.8,
            deadline=current_time + 86400  # 1 day
        )
        
        low_priority_goal_id = system.add_goal(
            description="Low priority task",
            value=0.4,
            deadline=current_time + 604800  # 1 week
        )
        
        # Compute priorities
        urgent_priority = system.goal_graph.compute_goal_priority(urgent_goal_id)
        normal_priority = system.goal_graph.compute_goal_priority(normal_goal_id)
        low_priority = system.goal_graph.compute_goal_priority(low_priority_goal_id)
        
        # Urgent goal should have highest priority despite lower base value
        assert urgent_priority > normal_priority
        assert normal_priority > low_priority
    
    def test_goal_conflict_detection(self, intentionality_system):
        """Test detection of conflicting goals"""
        system = intentionality_system
        
        # Create conflicting goals (resource conflict)
        goal1_id = system.add_goal(
            description="Train large neural network",
            value=0.8,
            constraints={
                "resources": ["gpu_cluster", "high_memory"]
            }
        )
        
        goal2_id = system.add_goal(
            description="Run distributed simulation",
            value=0.7,
            constraints={
                "resources": ["gpu_cluster", "network_bandwidth"]
            }
        )
        
        # Allow time for processing
        time.sleep(0.1)
        
        # Check for conflicts
        conflicts = system.goal_graph.find_goal_conflicts()
        
        # Should detect resource conflict
        conflict_found = any(
            (goal1_id in conflict[:2] and goal2_id in conflict[:2]) 
            for conflict in conflicts
        )
        assert conflict_found
    
    def test_performance_metrics(self, intentionality_system):
        """Test performance metrics calculation"""
        system = intentionality_system
        
        # Create and complete some goals
        goal_ids = []
        for i in range(3):
            goal_id = system.add_goal(
                description=f"Test goal {i}",
                value=0.5
            )
            goal_ids.append(goal_id)
        
        # Complete some goals
        for goal_id in goal_ids[:2]:
            system.update_goal_progress(goal_id, 1.0)
        
        # Allow time for metrics update
        time.sleep(0.5)
        
        metrics = system.get_performance_metrics()
        
        # Check that metrics are reasonable
        assert 0.0 <= metrics["goal_completion_rate"] <= 1.0
        assert metrics["average_planning_time"] >= 0.0
        assert 0.0 <= metrics["plan_success_rate"] <= 1.0
        assert 0.0 <= metrics["resource_efficiency"] <= 1.0
    
    def test_state_persistence(self, intentionality_system, tmp_path):
        """Test saving and loading system state"""
        system = intentionality_system
        
        # Create some goals and state
        goal_id = system.add_goal(
            description="Persistent test goal",
            value=0.8,
            constraints={"test": True}
        )
        
        system.update_current_state({
            "test_state": "test_value",
            "numerical_state": 42.5
        })
        
        # Save state
        save_path = tmp_path / "system_state.pkl"
        system.save_state(str(save_path))
        
        # Create new system and load state
        new_system = IntentionalitySystem()
        new_system.load_state(str(save_path))
        
        # Verify state was loaded correctly
        assert goal_id in new_system.goal_graph.goals
        loaded_goal = new_system.goal_graph.goals[goal_id]
        assert loaded_goal.description == "Persistent test goal"
        assert loaded_goal.value == 0.8
        
        assert new_system.current_state["test_state"] == "test_value"
        assert new_system.current_state["numerical_state"] == 42.5
    
    def test_concurrent_operations(self, intentionality_system):
        """Test thread safety of concurrent operations"""
        system = intentionality_system
        
        # Function to add goals concurrently
        def add_goals_worker(worker_id):
            goal_ids = []
            for i in range(5):
                goal_id = system.add_goal(
                    description=f"Concurrent goal {worker_id}-{i}",
                    value=0.5
                )
                goal_ids.append(goal_id)
                time.sleep(0.01)  # Small delay
            return goal_ids
        
        # Function to update states concurrently
        def update_state_worker(worker_id):
            for i in range(10):
                system.update_current_state({
                    f"worker_{worker_id}_state_{i}": f"value_{i}"
                })
                time.sleep(0.01)
        
        # Run concurrent operations
        with ThreadPoolExecutor(max_workers=4) as executor:
            # Submit goal creation tasks
            goal_futures = [executor.submit(add_goals_worker, i) for i in range(3)]
            
            # Submit state update tasks
            state_futures = [executor.submit(update_state_worker, i) for i in range(2)]
            
            # Wait for completion
            goal_results = [future.result() for future in goal_futures]
            [future.result() for future in state_futures]
        
        # Verify all goals were created successfully
        total_expected_goals = sum(len(result) for result in goal_results)
        actual_goals = len(system.goal_graph.goals)
        
        assert actual_goals == total_expected_goals
    
    def test_complex_planning_scenario(self, intentionality_system):
        """Test complex planning scenario with multiple strategies"""
        system = intentionality_system
        
        # Create complex goal requiring multiple steps
        goal_id = system.add_goal(
            description="Research, analyze, and publish comprehensive study on AI safety",
            value=0.95,
            deadline=time.time() + 1209600,  # 2 weeks
            constraints={
                "resources": ["time", "computational_power", "data_access", "writing_tools"],
                "success_criteria": [
                    "literature_reviewed==true",
                    "data_collected>=1000",
                    "analysis_completed==true", 
                    "paper_written==true",
                    "peer_review_passed==true",
                    "paper_published==true"
                ]
            }
        )
        
        # Allow time for complex planning
        time.sleep(0.2)
        
        # Check that a sophisticated plan was generated
        plan = system.get_plan_for_goal(goal_id)
        assert plan is not None
        
        # Complex goal should generate multiple actions
        assert len(plan.actions) >= 3
        
        # Test different planning strategies
        for strategy in [PlanningStrategy.HIERARCHICAL, PlanningStrategy.FORWARD_SEARCH, 
                        PlanningStrategy.BACKWARD_SEARCH]:
            success = system.regenerate_plan(goal_id, strategy)
            assert success is True
            
            updated_plan = system.get_plan_for_goal(goal_id)
            assert updated_plan is not None
            assert updated_plan.strategy == strategy
    
    def test_learning_and_adaptation(self, intentionality_system):
        """Test learning from execution history and adaptation"""
        system = intentionality_system
        
        # Create multiple similar goals to enable learning
        learning_goals = []
        for i in range(5):
            goal_id = system.add_goal(
                description=f"Learning task {i}: analyze data and create model",
                value=0.6
            )
            learning_goals.append(goal_id)
        
        # Execute actions and simulate success/failure
        for i, goal_id in enumerate(learning_goals):
            # Execute a few actions
            for _ in range(3):
                action = system.execute_next_action()
                if action:
                    # Simulate action success/failure
                    success = i < 3  # First 3 goals succeed
                    
                    if success:
                        system.update_current_state({"last_action_success": True})
                    else:
                        system.update_current_state({"last_action_success": False})
            
            # Complete or fail goal based on simulation
            if i < 3:
                system.update_goal_progress(goal_id, 1.0)
            else:
                goal = system.goal_graph.goals[goal_id]
                goal.status = GoalStatus.FAILED
        
        # Allow time for learning updates
        time.sleep(0.1)
        
        # Check that system learned from experience
        metrics = system.get_performance_metrics()
        
        # Should have recorded some completion rate
        assert metrics["goal_completion_rate"] > 0.0
        
        # Check that planning strategy performance was updated
        assert len(system.planning_strategy_performance) > 0
        
        # Execution history should contain records
        assert len(system.execution_history) > 0
    
    def test_resource_optimization(self, intentionality_system):
        """Test resource optimization and goal prioritization"""
        system = intentionality_system
        
        # Create more goals than the system can handle simultaneously
        high_priority_goals = []
        low_priority_goals = []
        
        # High priority goals
        for i in range(3):
            goal_id = system.add_goal(
                description=f"High priority critical task {i}",
                value=0.9,
                deadline=time.time() + 3600  # 1 hour - urgent
            )
            high_priority_goals.append(goal_id)
        
        # Low priority goals  
        for i in range(8):  # More than max_active_goals
            goal_id = system.add_goal(
                description=f"Low priority routine task {i}",
                value=0.3,
                deadline=time.time() + 604800  # 1 week - not urgent
            )
            low_priority_goals.append(goal_id)
        
        # Allow time for resource optimization
        time.sleep(0.5)
        
        # Check that system maintained resource limits
        active_goals = system.get_active_goals()
        assert len(active_goals) <= system.max_active_goals
        
        # High priority goals should be active
        active_goal_ids = {goal.id for goal in active_goals}
        high_priority_active = sum(1 for goal_id in high_priority_goals 
                                 if goal_id in active_goal_ids)
        
        # Most or all high priority goals should be active
        assert high_priority_active >= 2
    
    def test_error_handling_and_robustness(self, intentionality_system):
        """Test system robustness and error handling"""
        system = intentionality_system
        
        # Test invalid goal operations
        invalid_goal_id = "non_existent_goal_123"
        
        # Should handle invalid goal ID gracefully
        result = system.remove_goal(invalid_goal_id)
        assert result is False
        
        result = system.regenerate_plan(invalid_goal_id)
        assert result is False
        
        plan = system.get_plan_for_goal(invalid_goal_id)
        assert plan is None
        
        # Test with invalid state updates
        system.update_current_state({
            "valid_key": "valid_value",
            None: "invalid_key",  # Should be handled gracefully
            "numeric_key": 42,
            "list_key": [1, 2, 3]
        })
        
        # Test with malformed goal constraints
        goal_id = system.add_goal(
            description="Test goal with malformed constraints",
            value=0.5,
            constraints={
                "invalid_constraint": None,
                "complex_constraint": {"nested": {"data": [1, 2, 3]}}
            }
        )
        
        # Should still create goal and generate plan
        assert goal_id is not None
        time.sleep(0.1)
        
        plan = system.get_plan_for_goal(goal_id)
        # Plan might be empty but shouldn't crash
        assert plan is not None
    
    def test_mathematical_correctness(self, intentionality_system):
        """Test mathematical correctness of priority and progress calculations"""
        system = intentionality_system
        
        current_time = time.time()
        
        # Create goal with known parameters for testing calculations
        goal_id = system.add_goal(
            description="Mathematical test goal",
            value=0.6,  # Base priority
            deadline=current_time + 86400  # 24 hours from now
        )
        
        # Test priority calculation
        priority = system.goal_graph.compute_goal_priority(goal_id)
        
        # Priority should be >= base value due to urgency factor
        assert priority >= 0.6
        
        # Create subgoals to test progress aggregation
        subgoal_ids = []
        for i in range(4):
            subgoal_id = system.add_goal(
                description=f"Subgoal {i}",
                parent_id=goal_id,
                value=0.5
            )
            subgoal_ids.append(subgoal_id)
        
        # Complete subgoals partially
        system.update_goal_progress(subgoal_ids[0], 1.0)
        system.update_goal_progress(subgoal_ids[1], 1.0)
        system.update_goal_progress(subgoal_ids[2], 0.5)
        # subgoal_ids[3] remains at 0.0
        
        # Allow time for progress aggregation
        time.sleep(0.1)
        
        # Check parent goal progress calculation
        parent_goal = system.goal_graph.goals[goal_id]
        
        # Expected progress: (1.0 + 1.0 + 0.5 + 0.0) / 4 = 0.625
        expected_progress = 2.5 / 4
        
        # Allow for small floating point differences
        assert abs(parent_goal.progress - expected_progress) < 0.01
        
        # Test priority recalculation with dependency factor
        updated_priority = system.goal_graph.compute_goal_priority(goal_id)
        
        # Priority should be higher due to partial subgoal completion
        assert updated_priority > priority


if __name__ == "__main__":
    # Run basic functionality test
    def test_basic_functionality():
        """Basic functionality test for manual verification"""
        print("Starting ULTRA Intentionality System Test...")
        
        # Create system
        system = IntentionalitySystem(max_active_goals=3)
        system.start()
        
        try:
            # Add some test goals
            goal1_id = system.add_goal(
                description="Learn advanced machine learning techniques",
                value=0.8,
                deadline=time.time() + 86400,
                constraints={
                    "resources": ["time", "computational_power"],
                    "success_criteria": ["knowledge_acquired>=0.8"]
                }
            )
            
            goal2_id = system.add_goal(
                description="Implement neural network from scratch",
                parent_id=goal1_id,
                value=0.7,
                constraints={
                    "success_criteria": ["code_completed==true", "tests_passed==true"]
                }
            )
            
            # Test system operations
            print(f"Created goals: {goal1_id}, {goal2_id}")
            
            # Get current intention
            current_intention = system.get_current_intention()
            if current_intention:
                print(f"Current intention: {current_intention.description}")
            
            # Update system state
            system.update_current_state({
                "learning_materials_available": True,
                "development_environment_ready": True
            })
            
            # Execute some actions
            for i in range(3):
                action = system.execute_next_action()
                if action:
                    print(f"Executed action: {action.name}")
                else:
                    print("No executable actions available")
                    break
            
            # Update progress
            system.update_goal_progress(goal2_id, 0.7)
            
            # Get performance metrics
            metrics = system.get_performance_metrics()
            print(f"Performance metrics: {metrics}")
            
            # Test goal hierarchy
            hierarchy = system.get_goal_hierarchy()
            print(f"Goal hierarchy: {list(hierarchy.keys())}")
            
            print("Test completed successfully!")
            
        finally:
            system.stop()
    
    # Run test
    test_basic_functionality()