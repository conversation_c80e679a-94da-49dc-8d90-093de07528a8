#!/usr/bin/env python3
"""
Comprehensive Integration Tests for ULTRA Autonomous Learning System

This module provides thorough integration testing for all autonomous learning components,
testing their interactions, performance, and integration with the broader ULTRA ecosystem.

Key Test Areas:
1. Component Integration Testing
2. End-to-End Learning Workflows
3. Performance Benchmarking
4. System Integration with ULTRA Components
5. Stress Testing and Scalability
6. Cross-Component Data Flow Validation
"""

import logging
import time
import pytest
import numpy as np
import torch
import tempfile
import json
from typing import Dict, List, Any, Optional
from pathlib import Path

# ULTRA Autonomous Learning System
from ultra.autonomous_learning import (
    AutonomousLearningSystem,
    AutonomousLearningConfig,
    ContinualLearningManager,
    MetaLearningOptimizer,
    AutonomousSkillAcquisition,
    SelfModifyingArchitecture,
    ExperienceReplaySystem,
    CurriculumGenerator
)

# Mock ULTRA components for testing
from ultra.meta_cognitive.meta_learning import MetaLearningController
from ultra.emergent_consciousness.self_awareness import SelfAwarenessModule

logger = logging.getLogger(__name__)

# Test Fixtures and Utilities

@pytest.fixture
def learning_config():
    """Create test configuration for autonomous learning."""
    return AutonomousLearningConfig(
        # Continual Learning
        consolidation_frequency=50,
        forgetting_threshold=0.3,
        
        # Meta-Learning
        meta_learning_rate=0.01,
        adaptation_window=10,
        
        # Skill Acquisition
        max_concurrent_skills=5,
        skill_discovery_threshold=0.7,
        
        # Architecture Evolution
        architecture_evaluation_frequency=100,
        modification_threshold=0.15,
        
        # Experience Replay
        replay_frequency=25,
        memory_size=1000,
        
        # Curriculum Generation
        curriculum_adaptation_frequency=75,
        learning_objective_weights={
            'performance': 0.4,
            'efficiency': 0.3,
            'exploration': 0.3
        }
    )

@pytest.fixture
def mock_meta_cognitive():
    """Create mock meta-cognitive system."""
    class MockMetaCognitive:
        def __init__(self):
            self.strategy_performance = {}
            
        def get_current_strategy(self):
            return "chain_of_thought"
            
        def adapt_strategy(self, feedback):
            return {"strategy_adapted": True, "new_strategy": "tree_of_thought"}
    
    return MockMetaCognitive()

@pytest.fixture
def mock_neuroplasticity():
    """Create mock neuroplasticity engine."""
    class MockNeuroplasticity:
        def __init__(self):
            self.synaptic_weights = np.random.random((100, 100))
            self.plasticity_rate = 0.1
            
        def update_weights(self, experience):
            # Simulate weight updates
            change = np.random.normal(0, 0.01, self.synaptic_weights.shape)
            self.synaptic_weights += change * self.plasticity_rate
            return {"weights_updated": True, "avg_change": np.mean(np.abs(change))}
            
        def get_network_state(self):
            return {
                "total_connections": self.synaptic_weights.size,
                "active_connections": np.sum(self.synaptic_weights > 0.1),
                "average_weight": np.mean(self.synaptic_weights)
            }
    
    return MockNeuroplasticity()

@pytest.fixture
def mock_self_awareness():
    """Create mock self-awareness module."""
    class MockSelfAwareness:
        def __init__(self):
            self.capability_model = {}
            self.confidence_levels = {}
            
        def assess_current_capabilities(self):
            return {
                "reasoning": 0.8,
                "learning": 0.7,
                "adaptation": 0.75,
                "meta_cognition": 0.65
            }
            
        def update_self_model(self, feedback):
            self.capability_model.update(feedback)
            return {"self_model_updated": True}
            
        def get_learning_progress(self):
            return {
                "learning_velocity": 0.05,
                "recent_improvements": ["reasoning", "adaptation"],
                "areas_for_improvement": ["meta_cognition"]
            }
    
    return MockSelfAwareness()

@pytest.fixture
def autonomous_learning_system(learning_config, mock_meta_cognitive, mock_neuroplasticity, mock_self_awareness):
    """Create complete autonomous learning system for testing."""
    return AutonomousLearningSystem(
        config=learning_config,
        meta_cognitive_system=mock_meta_cognitive,
        neuroplasticity_engine=mock_neuroplasticity,
        self_awareness_module=mock_self_awareness
    )

def generate_test_experience(experience_type: str = "learning") -> Dict[str, Any]:
    """Generate realistic test experience for learning."""
    experiences = {
        "learning": {
            "type": "problem_solving",
            "domain": "mathematics",
            "problem": "solve quadratic equation",
            "solution_quality": np.random.uniform(0.6, 0.95),
            "complexity": np.random.uniform(0.3, 0.8),
            "novelty": np.random.uniform(0.1, 0.7),
            "timestamp": time.time(),
            "context": {
                "cognitive_load": np.random.uniform(0.2, 0.8),
                "attention_level": np.random.uniform(0.6, 1.0),
                "prior_knowledge": np.random.uniform(0.4, 0.9)
            }
        },
        "exploration": {
            "type": "skill_discovery",
            "domain": "creative_reasoning",
            "task": "generate novel connections",
            "success_rate": np.random.uniform(0.3, 0.8),
            "creativity_score": np.random.uniform(0.4, 0.9),
            "novelty": np.random.uniform(0.6, 1.0),
            "timestamp": time.time(),
            "context": {
                "exploration_depth": np.random.uniform(0.5, 1.0),
                "risk_tolerance": np.random.uniform(0.3, 0.8)
            }
        },
        "adaptation": {
            "type": "domain_transfer",
            "source_domain": "logical_reasoning",
            "target_domain": "pattern_recognition",
            "transfer_success": np.random.uniform(0.4, 0.85),
            "adaptation_speed": np.random.uniform(0.2, 0.9),
            "timestamp": time.time(),
            "context": {
                "domain_similarity": np.random.uniform(0.3, 0.7),
                "adaptation_difficulty": np.random.uniform(0.2, 0.8)
            }
        }
    }
    
    return experiences.get(experience_type, experiences["learning"])

def generate_performance_feedback() -> Dict[str, float]:
    """Generate realistic performance feedback."""
    return {
        "accuracy": np.random.uniform(0.65, 0.95),
        "efficiency": np.random.uniform(0.55, 0.90),
        "creativity": np.random.uniform(0.40, 0.85),
        "adaptation_speed": np.random.uniform(0.45, 0.80),
        "knowledge_retention": np.random.uniform(0.70, 0.95),
        "transfer_capability": np.random.uniform(0.35, 0.75)
    }

# Core Integration Tests

class TestAutonomousLearningIntegration:
    """Integration tests for the complete autonomous learning system."""
    
    def test_system_initialization(self, autonomous_learning_system):
        """Test proper initialization of all components."""
        system = autonomous_learning_system
        
        # Verify all components are initialized
        assert system.continual_learning_manager is not None
        assert system.meta_learning_optimizer is not None
        assert system.autonomous_skill_acquisition is not None
        assert system.self_modifying_architecture is not None
        assert system.experience_replay_system is not None
        assert system.curriculum_generator is not None
        
        # Verify initial state
        assert system.learning_iteration == 0
        assert system.total_learning_time == 0.0
        assert len(system.performance_history) == 0
        assert len(system.adaptation_history) == 0
        
        # Verify statistics initialization
        expected_stats = {
            'skills_acquired', 'experiences_replayed', 'curricula_generated',
            'architecture_improvements', 'successful_adaptations',
            'knowledge_consolidations', 'meta_optimizations'
        }
        assert set(system.stats.keys()) == expected_stats
        
        logger.info("✓ System initialization test passed")
    
    def test_single_learning_step(self, autonomous_learning_system):
        """Test execution of a single autonomous learning step."""
        system = autonomous_learning_system
        
        # Generate test experience and feedback
        experience = generate_test_experience("learning")
        performance_feedback = generate_performance_feedback()
        
        # Execute learning step
        results = system.autonomous_learning_step(experience, performance_feedback)
        
        # Verify results structure
        assert 'learning_iteration' in results
        assert 'timestamp' in results
        assert 'updates' in results
        assert 'new_capabilities' in results
        assert 'performance_improvements' in results
        assert 'learning_stats' in results
        
        # Verify component updates occurred
        updates = results['updates']
        expected_components = {
            'continual_learning', 'experience_replay', 'meta_learning',
            'skill_acquisition', 'curriculum'
        }
        
        # At least some components should have been updated
        updated_components = {k for k, v in updates.items() if v and v.get('success', False)}
        assert len(updated_components) > 0
        
        # Verify system state advanced
        assert system.learning_iteration == 1
        assert system.total_learning_time > 0
        assert len(system.performance_history) == 1
        
        logger.info(f"✓ Single learning step test passed - Updated components: {updated_components}")
    
    def test_multi_step_learning_progression(self, autonomous_learning_system):
        """Test learning progression over multiple steps."""
        system = autonomous_learning_system
        
        n_steps = 25
        learning_metrics = []
        
        for step in range(n_steps):
            # Vary experience types to test different learning modes
            exp_type = ["learning", "exploration", "adaptation"][step % 3]
            experience = generate_test_experience(exp_type)
            performance_feedback = generate_performance_feedback()
            
            # Execute learning step
            results = system.autonomous_learning_step(experience, performance_feedback)
            
            # Track metrics
            learning_metrics.append({
                'iteration': step + 1,
                'total_time': system.total_learning_time,
                'successful_updates': sum(1 for v in results['updates'].values() 
                                        if v and v.get('success', False)),
                'new_capabilities': len(results.get('new_capabilities', [])),
                'performance_improvements': len(results.get('performance_improvements', {}))
            })
        
        # Verify progression
        assert system.learning_iteration == n_steps
        assert system.total_learning_time > 0
        assert len(system.performance_history) == n_steps
        
        # Analyze learning progression
        total_capabilities = sum(m['new_capabilities'] for m in learning_metrics)
        total_improvements = sum(m['performance_improvements'] for m in learning_metrics)
        avg_updates_per_step = np.mean([m['successful_updates'] for m in learning_metrics])
        
        # System should show learning activity
        assert total_capabilities >= 0  # May acquire new capabilities
        assert avg_updates_per_step > 0  # Should have regular updates
        
        # Learning time should increase progressively
        time_progression = [m['total_time'] for m in learning_metrics]
        assert all(time_progression[i] <= time_progression[i+1] for i in range(len(time_progression)-1))
        
        logger.info(f"✓ Multi-step learning progression test passed - "
                   f"Capabilities: {total_capabilities}, Avg updates/step: {avg_updates_per_step:.2f}")
    
    def test_component_interaction_flow(self, autonomous_learning_system):
        """Test data flow and interactions between components."""
        system = autonomous_learning_system
        
        # Execute several learning steps to trigger different component interactions
        experiences = [
            generate_test_experience("learning"),
            generate_test_experience("exploration"),
            generate_test_experience("adaptation")
        ]
        
        interaction_results = []
        
        for i, experience in enumerate(experiences):
            performance_feedback = generate_performance_feedback()
            results = system.autonomous_learning_step(experience, performance_feedback)
            
            # Analyze component interactions
            updates = results['updates']
            interaction_data = {
                'step': i + 1,
                'experience_type': experience['type'],
                'components_updated': list(updates.keys()),
                'successful_updates': [k for k, v in updates.items() if v and v.get('success', False)],
                'cross_component_effects': {}
            }
            
            # Check for cross-component effects
            if 'skill_acquisition' in updates and updates['skill_acquisition']:
                skill_results = updates['skill_acquisition']
                if skill_results.get('new_skills_acquired', 0) > 0:
                    interaction_data['cross_component_effects']['skills_to_curriculum'] = True
            
            if 'meta_learning' in updates and updates['meta_learning']:
                meta_results = updates['meta_learning']
                if 'parameter_updates' in meta_results:
                    interaction_data['cross_component_effects']['meta_to_components'] = True
            
            interaction_results.append(interaction_data)
        
        # Verify meaningful interactions occurred
        total_successful_updates = sum(len(r['successful_updates']) for r in interaction_results)
        cross_effects = sum(len(r['cross_component_effects']) for r in interaction_results)
        
        assert total_successful_updates > 0
        # Cross-component effects are beneficial but not strictly required
        
        logger.info(f"✓ Component interaction test passed - "
                   f"Total updates: {total_successful_updates}, Cross-effects: {cross_effects}")
    
    def test_learning_insights_generation(self, autonomous_learning_system):
        """Test generation of comprehensive learning insights."""
        system = autonomous_learning_system
        
        # Execute some learning steps to generate data
        for _ in range(15):
            experience = generate_test_experience()
            performance_feedback = generate_performance_feedback()
            system.autonomous_learning_step(experience, performance_feedback)
        
        # Get learning insights
        insights = system.get_learning_insights()
        
        # Verify insights structure
        expected_sections = {
            'learning_statistics', 'learning_efficiency', 'capability_development',
            'architecture_evolution', 'curriculum_progress', 'meta_learning_insights',
            'memory_utilization', 'continual_learning_health', 'learning_velocity'
        }
        
        assert set(insights.keys()) == expected_sections
        
        # Verify insights content
        stats = insights['learning_statistics']
        assert isinstance(stats, dict)
        assert all(isinstance(v, (int, float)) for v in stats.values())
        
        efficiency = insights['learning_efficiency']
        assert isinstance(efficiency, (int, float))
        assert 0 <= efficiency <= 1
        
        velocity = insights['learning_velocity']
        assert isinstance(velocity, (int, float))
        
        logger.info(f"✓ Learning insights test passed - "
                   f"Efficiency: {efficiency:.3f}, Velocity: {velocity:.3f}")
    
    def test_state_persistence(self, autonomous_learning_system):
        """Test saving and loading of learning state."""
        system = autonomous_learning_system
        
        # Execute some learning to create state
        for _ in range(10):
            experience = generate_test_experience()
            performance_feedback = generate_performance_feedback()
            system.autonomous_learning_step(experience, performance_feedback)
        
        # Save state
        with tempfile.NamedTemporaryFile(delete=False, suffix='.pkl') as tmp_file:
            save_path = tmp_file.name
        
        try:
            # Save current state
            save_success = system.save_learning_state(save_path)
            assert save_success, "Failed to save learning state"
            
            # Record current state for comparison
            original_iteration = system.learning_iteration
            original_time = system.total_learning_time
            original_history_len = len(system.performance_history)
            original_stats = system.stats.copy()
            
            # Reset system
            system.reset_learning_state()
            assert system.learning_iteration == 0
            assert system.total_learning_time == 0.0
            assert len(system.performance_history) == 0
            
            # Load saved state
            load_success = system.load_learning_state(save_path)
            assert load_success, "Failed to load learning state"
            
            # Verify state restoration
            assert system.learning_iteration == original_iteration
            assert abs(system.total_learning_time - original_time) < 0.001
            assert len(system.performance_history) == original_history_len
            
            # Verify statistics restoration
            for key, value in original_stats.items():
                assert system.stats[key] == value
            
        finally:
            # Clean up
            Path(save_path).unlink(missing_ok=True)
        
        logger.info("✓ State persistence test passed")

class TestPerformanceBenchmarking:
    """Performance and scalability tests for autonomous learning."""
    
    def test_learning_step_performance(self, autonomous_learning_system):
        """Test performance of individual learning steps."""
        system = autonomous_learning_system
        
        # Warm-up
        experience = generate_test_experience()
        performance_feedback = generate_performance_feedback()
        system.autonomous_learning_step(experience, performance_feedback)
        
        # Benchmark learning steps
        n_iterations = 50
        execution_times = []
        
        for _ in range(n_iterations):
            experience = generate_test_experience()
            performance_feedback = generate_performance_feedback()
            
            start_time = time.time()
            results = system.autonomous_learning_step(experience, performance_feedback)
            end_time = time.time()
            
            execution_times.append(end_time - start_time)
        
        # Analyze performance
        avg_time = np.mean(execution_times)
        max_time = np.max(execution_times)
        std_time = np.std(execution_times)
        
        # Performance requirements (adjust as needed)
        assert avg_time < 0.5, f"Average learning step time too high: {avg_time:.3f}s"
        assert max_time < 2.0, f"Maximum learning step time too high: {max_time:.3f}s"
        
        logger.info(f"✓ Learning step performance test passed - "
                   f"Avg: {avg_time:.3f}s, Max: {max_time:.3f}s, Std: {std_time:.3f}s")
    
    def test_memory_usage_stability(self, autonomous_learning_system):
        """Test memory usage stability over extended learning."""
        system = autonomous_learning_system
        
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss
        
        # Extended learning session
        n_steps = 100
        memory_samples = []
        
        for i in range(n_steps):
            experience = generate_test_experience()
            performance_feedback = generate_performance_feedback()
            system.autonomous_learning_step(experience, performance_feedback)
            
            # Sample memory usage every 10 steps
            if i % 10 == 0:
                current_memory = process.memory_info().rss
                memory_samples.append(current_memory - initial_memory)
        
        # Analyze memory usage
        memory_growth = memory_samples[-1] - memory_samples[0] if len(memory_samples) > 1 else 0
        max_memory_increase = max(memory_samples)
        
        # Memory requirements (adjust as needed)
        assert memory_growth < 100 * 1024 * 1024, f"Memory growth too high: {memory_growth / 1024 / 1024:.1f}MB"
        assert max_memory_increase < 200 * 1024 * 1024, f"Max memory increase too high: {max_memory_increase / 1024 / 1024:.1f}MB"
        
        logger.info(f"✓ Memory usage test passed - "
                   f"Growth: {memory_growth / 1024 / 1024:.1f}MB, "
                   f"Max increase: {max_memory_increase / 1024 / 1024:.1f}MB")
    
    def test_scalability_with_complexity(self, autonomous_learning_system):
        """Test system scalability with increasing complexity."""
        system = autonomous_learning_system
        
        complexity_levels = [0.2, 0.4, 0.6, 0.8, 1.0]
        performance_metrics = []
        
        for complexity in complexity_levels:
            # Generate complex experiences
            n_steps = 20
            step_times = []
            
            for _ in range(n_steps):
                # Create experience with specified complexity
                experience = generate_test_experience()
                experience['complexity'] = complexity
                experience['context']['cognitive_load'] = complexity
                
                performance_feedback = generate_performance_feedback()
                
                start_time = time.time()
                results = system.autonomous_learning_step(experience, performance_feedback)
                end_time = time.time()
                
                step_times.append(end_time - start_time)
            
            avg_time = np.mean(step_times)
            performance_metrics.append({
                'complexity': complexity,
                'avg_time': avg_time,
                'throughput': 1.0 / avg_time
            })
        
        # Analyze scalability
        times = [m['avg_time'] for m in performance_metrics]
        complexity_impact = times[-1] / times[0] if times[0] > 0 else 1.0
        
        # Should scale reasonably with complexity
        assert complexity_impact < 5.0, f"Performance degradation too high: {complexity_impact:.2f}x"
        
        logger.info(f"✓ Scalability test passed - "
                   f"Complexity impact: {complexity_impact:.2f}x, "
                   f"Range: {times[0]:.3f}s - {times[-1]:.3f}s")

class TestULTRASystemIntegration:
    """Integration tests with broader ULTRA ecosystem."""
    
    def test_meta_cognitive_integration(self, autonomous_learning_system):
        """Test integration with meta-cognitive systems."""
        system = autonomous_learning_system
        
        # Simulate meta-cognitive feedback
        for i in range(10):
            experience = generate_test_experience()
            performance_feedback = generate_performance_feedback()
            
            # Add meta-cognitive context
            experience['meta_cognitive_state'] = {
                'reasoning_strategy': 'chain_of_thought',
                'strategy_confidence': np.random.uniform(0.6, 0.95),
                'cognitive_load': np.random.uniform(0.3, 0.8)
            }
            
            results = system.autonomous_learning_step(experience, performance_feedback)
            
            # Verify meta-learning component processed the context
            if 'meta_learning' in results['updates']:
                meta_results = results['updates']['meta_learning']
                assert meta_results is not None
        
        # Test insights generation with meta-cognitive context
        insights = system.get_learning_insights()
        assert 'meta_learning_insights' in insights
        
        logger.info("✓ Meta-cognitive integration test passed")
    
    def test_neuroplasticity_integration(self, autonomous_learning_system):
        """Test integration with neuroplasticity engine."""
        system = autonomous_learning_system
        
        # Execute learning with neuroplasticity feedback
        for i in range(15):
            experience = generate_test_experience()
            performance_feedback = generate_performance_feedback()
            
            # Add neuroplasticity context
            experience['neural_context'] = {
                'synaptic_strength': np.random.uniform(0.4, 0.9),
                'plasticity_rate': np.random.uniform(0.05, 0.3),
                'network_connectivity': np.random.uniform(0.6, 0.95)
            }
            
            results = system.autonomous_learning_step(experience, performance_feedback)
            
            # Verify continual learning processed neural context
            if 'continual_learning' in results['updates']:
                continual_results = results['updates']['continual_learning']
                assert continual_results is not None
        
        # Test that neuroplasticity influenced learning
        insights = system.get_learning_insights()
        assert 'continual_learning_health' in insights
        
        logger.info("✓ Neuroplasticity integration test passed")
    
    def test_self_awareness_integration(self, autonomous_learning_system):
        """Test integration with self-awareness module."""
        system = autonomous_learning_system
        
        # Execute learning with self-awareness feedback
        capabilities_tracked = []
        
        for i in range(12):
            experience = generate_test_experience()
            performance_feedback = generate_performance_feedback()
            
            # Add self-awareness context
            experience['self_awareness_state'] = {
                'current_confidence': np.random.uniform(0.5, 0.9),
                'capability_assessment': {
                    'reasoning': np.random.uniform(0.6, 0.9),
                    'learning': np.random.uniform(0.5, 0.85),
                    'adaptation': np.random.uniform(0.4, 0.8)
                }
            }
            
            results = system.autonomous_learning_step(experience, performance_feedback)
            capabilities_tracked.append(experience['self_awareness_state']['capability_assessment'])
        
        # Verify self-awareness influenced learning
        insights = system.get_learning_insights()
        assert 'capability_development' in insights
        
        # Check for capability progression (should show some variation)
        reasoning_progression = [c['reasoning'] for c in capabilities_tracked]
        assert len(set(reasoning_progression)) > 1, "Self-awareness should show capability variation"
        
        logger.info("✓ Self-awareness integration test passed")

class TestStressAndRobustness:
    """Stress testing and robustness validation."""
    
    def test_error_handling_robustness(self, autonomous_learning_system):
        """Test system robustness against various error conditions."""
        system = autonomous_learning_system
        
        # Test with malformed experiences
        malformed_experiences = [
            {},  # Empty experience
            {"type": "unknown"},  # Missing required fields
            {"type": "learning", "invalid_field": "test"},  # Invalid fields
            None  # None experience
        ]
        
        for experience in malformed_experiences:
            try:
                results = system.autonomous_learning_step(experience, None)
                # Should handle gracefully without crashing
                assert isinstance(results, dict)
            except Exception as e:
                # If exceptions occur, they should be caught and logged
                assert 'error' in str(e).lower() or isinstance(e, (TypeError, ValueError))
        
        # Test with malformed performance feedback
        malformed_feedback = [
            {"invalid": "feedback"},
            {"accuracy": "not_a_number"},
            {"accuracy": -1.0},  # Invalid range
            {"accuracy": 2.0}   # Invalid range
        ]
        
        valid_experience = generate_test_experience()
        
        for feedback in malformed_feedback:
            try:
                results = system.autonomous_learning_step(valid_experience, feedback)
                assert isinstance(results, dict)
            except Exception as e:
                assert 'error' in str(e).lower() or isinstance(e, (TypeError, ValueError))
        
        logger.info("✓ Error handling robustness test passed")
    
    def test_concurrent_learning_stability(self, autonomous_learning_system):
        """Test stability under rapid concurrent learning steps."""
        system = autonomous_learning_system
        
        # Rapid-fire learning steps
        n_rapid_steps = 100
        results_list = []
        
        for i in range(n_rapid_steps):
            experience = generate_test_experience()
            performance_feedback = generate_performance_feedback()
            
            start_time = time.time()
            results = system.autonomous_learning_step(experience, performance_feedback)
            end_time = time.time()
            
            results_list.append({
                'step': i,
                'duration': end_time - start_time,
                'success': 'error' not in results
            })
        
        # Analyze stability
        success_rate = sum(1 for r in results_list if r['success']) / len(results_list)
        avg_duration = np.mean([r['duration'] for r in results_list])
        max_duration = np.max([r['duration'] for r in results_list])
        
        # System should maintain stability
        assert success_rate > 0.95, f"Success rate too low: {success_rate:.3f}"
        assert avg_duration < 1.0, f"Average duration too high: {avg_duration:.3f}s"
        assert max_duration < 5.0, f"Maximum duration too high: {max_duration:.3f}s"
        
        # Verify system state consistency
        assert system.learning_iteration == n_rapid_steps
        assert len(system.performance_history) == n_rapid_steps
        
        logger.info(f"✓ Concurrent learning stability test passed - "
                   f"Success rate: {success_rate:.3f}, Avg duration: {avg_duration:.3f}s")
    
    def test_resource_cleanup(self, autonomous_learning_system):
        """Test proper resource cleanup and management."""
        system = autonomous_learning_system
        
        # Generate significant learning activity
        for _ in range(50):
            experience = generate_test_experience()
            performance_feedback = generate_performance_feedback()
            system.autonomous_learning_step(experience, performance_feedback)
        
        # Test reset functionality
        initial_iteration = system.learning_iteration
        initial_history_len = len(system.performance_history)
        
        assert initial_iteration > 0
        assert initial_history_len > 0
        
        # Reset and verify cleanup
        system.reset_learning_state()
        
        assert system.learning_iteration == 0
        assert system.total_learning_time == 0.0
        assert len(system.performance_history) == 0
        assert len(system.adaptation_history) == 0
        assert all(v == 0 for v in system.stats.values())
        
        # Verify system can continue learning after reset
        experience = generate_test_experience()
        performance_feedback = generate_performance_feedback()
        results = system.autonomous_learning_step(experience, performance_feedback)
        
        assert system.learning_iteration == 1
        assert 'error' not in results
        
        logger.info("✓ Resource cleanup test passed")

def test_complete_autonomous_learning_workflow():
    """End-to-end test of complete autonomous learning workflow."""
    logger.info("Starting complete autonomous learning workflow test...")
    
    # Create system with default configuration
    config = AutonomousLearningConfig()
    system = AutonomousLearningSystem(config=config)
    
    # Simulate realistic learning scenario
    learning_domains = ["mathematics", "reasoning", "creativity", "adaptation"]
    experience_types = ["learning", "exploration", "adaptation"]
    
    workflow_results = {
        'total_steps': 0,
        'successful_steps': 0,
        'capabilities_acquired': 0,
        'architecture_improvements': 0,
        'knowledge_consolidations': 0,
        'final_efficiency': 0.0
    }
    
    # Execute extended learning workflow
    for domain in learning_domains:
        for exp_type in experience_types:
            for _ in range(15):  # 15 steps per domain-type combination
                # Create domain-specific experience
                experience = generate_test_experience(exp_type)
                experience['domain'] = domain
                experience['cross_domain_transfer'] = np.random.choice([True, False])
                
                performance_feedback = generate_performance_feedback()
                # Add domain-specific performance adjustments
                if domain == "mathematics":
                    performance_feedback['accuracy'] *= 1.1  # Slightly better at math
                elif domain == "creativity":
                    performance_feedback['creativity'] *= 1.2  # Better at creative tasks
                
                # Execute learning step
                results = system.autonomous_learning_step(experience, performance_feedback)
                
                # Track workflow progress
                workflow_results['total_steps'] += 1
                
                if 'error' not in results:
                    workflow_results['successful_steps'] += 1
                
                workflow_results['capabilities_acquired'] += len(results.get('new_capabilities', []))
                
                # Track architecture improvements
                if 'architecture_changes' in results:
                    workflow_results['architecture_improvements'] += len(results['architecture_changes'])
    
    # Analyze final workflow results
    workflow_results['final_efficiency'] = system.get_learning_insights()['learning_efficiency']
    success_rate = workflow_results['successful_steps'] / workflow_results['total_steps']
    
    # Validate workflow outcomes
    assert success_rate > 0.9, f"Workflow success rate too low: {success_rate:.3f}"
    assert workflow_results['final_efficiency'] > 0.0, "Learning efficiency should be positive"
    assert system.learning_iteration == workflow_results['total_steps']
    
    # Generate final insights
    final_insights = system.get_learning_insights()
    
    logger.info(f"✓ Complete workflow test passed - "
               f"Steps: {workflow_results['total_steps']}, "
               f"Success rate: {success_rate:.3f}, "
               f"Capabilities: {workflow_results['capabilities_acquired']}, "
               f"Efficiency: {workflow_results['final_efficiency']:.3f}")
    
    return {
        'workflow_results': workflow_results,
        'final_insights': final_insights,
        'system_state': {
            'learning_iteration': system.learning_iteration,
            'total_time': system.total_learning_time,
            'performance_history_length': len(system.performance_history)
        }
    }

if __name__ == "__main__":
    # Run comprehensive test if executed directly
    logging.basicConfig(level=logging.INFO)
    
    # Run end-to-end workflow test
    workflow_results = test_complete_autonomous_learning_workflow()
    
    print("\n" + "="*60)
    print("ULTRA AUTONOMOUS LEARNING INTEGRATION TEST RESULTS")
    print("="*60)
    print(f"Total Learning Steps: {workflow_results['workflow_results']['total_steps']}")
    print(f"Success Rate: {workflow_results['workflow_results']['successful_steps'] / workflow_results['workflow_results']['total_steps']:.1%}")
    print(f"Capabilities Acquired: {workflow_results['workflow_results']['capabilities_acquired']}")
    print(f"Architecture Improvements: {workflow_results['workflow_results']['architecture_improvements']}")
    print(f"Final Learning Efficiency: {workflow_results['workflow_results']['final_efficiency']:.3f}")
    print(f"System Learning Iteration: {workflow_results['system_state']['learning_iteration']}")
    print(f"Total Learning Time: {workflow_results['system_state']['total_time']:.3f}s")
    print("="*60)
    print("✅ All autonomous learning integration tests completed successfully!")
