#!/usr/bin/env python3
"""
ULTRA: Ultimate Learning & Thought Reasoning Architecture
Core Neural Architecture - Test Configuration and Fixtures

This module provides shared test fixtures and configuration for all Core Neural Architecture
test modules. It includes setup for pytest fixtures, common test helpers, and configuration
parameters.
"""

import os
import sys
import numpy as np
import pytest
import logging
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple, Union, Callable, Optional, Type, Any

# Ensure the ULTRA module is in the path
ROOT_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if ROOT_DIR not in sys.path:
    sys.path.insert(0, ROOT_DIR)

# Import ULTRA core neural modules
from ultra.core_neural.neuromorphic_core import NeuromorphicCore
from ultra.core_neural.neuroplasticity_engine import NeuroplasticityEngine
from ultra.core_neural.synaptic_pruning import SynapticPruningModule
from ultra.core_neural.neuromodulation import NeuromodulationSystem
from ultra.core_neural.biological_timing import BiologicalTimingCircuits

# Import test utilities
from tests.test_core_neural import (
    network_state_recorder, input_pattern_generator, TestUtils,
    DEFAULT_NEURON_PARAMS, STDP_DEFAULT_PARAMS, 
    NEUROMODULATION_DEFAULT_PARAMS, BIOLOGICAL_TIMING_DEFAULT_PARAMS
)

# Configure logging for tests
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

@pytest.fixture(scope="session")
def plot_dir(tmp_path_factory):
    """Create a directory for storing test plots"""
    plot_dir = tmp_path_factory.mktemp("plots")
    return plot_dir

@pytest.fixture
def enable_plotting(plot_dir):
    """Context manager for generating and saving plots during tests"""
    
    class PlottingContext:
        def __init__(self, plot_dir):
            self.plot_dir = plot_dir
            self.plots_created = []
        
        def create_plot(self, name, title=None):
            plt.figure(figsize=(10, 6))
            if title:
                plt.title(title)
            self.plots_created.append(name)
            return plt
        
        def save_plots(self):
            for name in self.plots_created:
                plt.figure(len(self.plots_created) - self.plots_created.index(name))
                plt.tight_layout()
                file_path = os.path.join(self.plot_dir, f"{name}.png")
                plt.savefig(file_path)
                logger.info(f"Plot saved to {file_path}")
            plt.close('all')
            self.plots_created = []
    
    context = PlottingContext(plot_dir)
    yield context
    context.save_plots()

@pytest.fixture
def mock_network(monkeypatch):
    """Creates a mock network that can be used for dependency injection in isolated tests"""
    
    class MockNetwork:
        def __init__(self):
            self.membrane_potentials = np.zeros(10)
            self.connections = None
            self.spike_times = {}
            self.neuromodulator_levels = {
                "dopamine": 1.0,
                "serotonin": 1.0,
                "norepinephrine": 1.0,
                "acetylcholine": 1.0
            }
            self.oscillator_phases = {
                "theta": 0.0,
                "alpha": 0.0,
                "beta": 0.0,
                "gamma": 0.0
            }
        
        def update(self, input_current, dt):
            # Simple update to change state
            self.membrane_potentials += input_current * dt
        
        def get_spikes(self):
            # Return any spikes that occurred
            spikes = []
            for i, v in enumerate(self.membrane_potentials):
                if v > 1.0:
                    spikes.append((i, 0))  # (neuron_id, time)
            return spikes
        
        def get_neuromodulator_levels(self):
            return self.neuromodulator_levels
        
        def get_oscillator_phases(self):
            return self.oscillator_phases
    
    return MockNetwork()

@pytest.fixture
def random_connectivity_matrix():
    """Generate a random connectivity matrix for testing"""
    
    def _generate(n_neurons, connection_prob=0.3, weight_mean=0.5, weight_std=0.1, seed=None):
        if seed is not None:
            np.random.seed(seed)
        
        # Generate mask for connections
        mask = np.random.rand(n_neurons, n_neurons) < connection_prob
        np.fill_diagonal(mask, False)  # No self-connections
        
        # Generate weights
        weights = np.random.normal(weight_mean, weight_std, (n_neurons, n_neurons))
        weights = weights * mask  # Apply mask
        
        return weights
    
    return _generate

@pytest.fixture
def random_spike_trains():
    """Generate random spike trains for testing"""
    
    def _generate(n_neurons, duration, rate=10, dt=1.0, seed=None):
        if seed is not None:
            np.random.seed(seed)
        
        steps = int(duration / dt)
        spike_trains = np.zeros((n_neurons, steps))
        
        # Generate Poisson spike trains
        for neuron_id in range(n_neurons):
            spike_trains[neuron_id] = np.random.rand(steps) < (rate * dt / 1000)
        
        return spike_trains
    
    return _generate

@pytest.fixture
def correlated_spike_trains():
    """Generate correlated spike trains for testing"""
    
    def _generate(n_groups, neurons_per_group, duration, rate=10, correlation=0.8, dt=1.0, seed=None):
        if seed is not None:
            np.random.seed(seed)
        
        steps = int(duration / dt)
        n_neurons = n_groups * neurons_per_group
        spike_trains = np.zeros((n_neurons, steps))
        
        # Generate group reference spike trains
        group_trains = np.zeros((n_groups, steps))
        for group_id in range(n_groups):
            group_trains[group_id] = np.random.rand(steps) < (rate * dt / 1000)
        
        # Generate correlated spike trains for each neuron
        for group_id in range(n_groups):
            group_start = group_id * neurons_per_group
            group_end = group_start + neurons_per_group
            
            for neuron_id in range(group_start, group_end):
                # Correlated component (copy from group reference)
                corr_spikes = group_trains[group_id].copy()
                
                # Independent component
                indep_spikes = np.random.rand(steps) < (rate * dt / 1000)
                
                # Combine with correlation weight
                spike_trains[neuron_id] = (np.random.rand(steps) < correlation) * corr_spikes + \
                                         (np.random.rand(steps) < (1 - correlation)) * indep_spikes
        
        return spike_trains
    
    return _generate

@pytest.fixture
def activity_correlation_matrix():
    """Generate activity correlation matrix from spike trains"""
    
    def _calculate(spike_trains):
        n_neurons = spike_trains.shape[0]
        corr_matrix = np.zeros((n_neurons, n_neurons))
        
        for i in range(n_neurons):
            for j in range(i, n_neurons):
                # Calculate correlation coefficient
                corr = np.corrcoef(spike_trains[i], spike_trains[j])[0, 1]
                if np.isnan(corr):
                    corr = 0
                corr_matrix[i, j] = corr
                corr_matrix[j, i] = corr  # Symmetric
        
        return corr_matrix
    
    return _calculate

# Additional fixtures for testing with realistic network parameters

@pytest.fixture
def realistic_cortical_network():
    """
    Create a network with more realistic cortical parameters:
    - Distance-dependent connectivity
    - Excitatory and inhibitory neuron types following Dale's law
    - Balanced excitation/inhibition
    - Realistic time constants
    """
    # Create a 10x10x10 grid of neurons
    dimensions = (10, 10, 10)
    total_neurons = dimensions[0] * dimensions[1] * dimensions[2]
    
    # Initialize cortical network
    network = NeuromorphicCore(dimensions=dimensions, neuron_types=4)
    
    # Override default parameters with more realistic values
    
    # 1. Neuron parameters
    # Excitatory neurons (type 0): Regular-spiking cells
    for i in range(total_neurons):
        if network.types[i] == 0:  # Excitatory
            # Set longer time constant, lower firing threshold
            network.neuron_params[i] = {
                "tau": 20.0,          # Membrane time constant (ms)
                "v_rest": -70.0,      # Resting potential (mV)
                "v_threshold": -55.0, # Threshold potential (mV)
                "v_reset": -70.0,     # Reset potential (mV)
                "refractory_period": 2  # Refractory period (ms)
            }
    
    # Inhibitory neurons (type 1): Fast-spiking cells
    for i in range(total_neurons):
        if network.types[i] == 1:  # Inhibitory
            # Set shorter time constant, higher firing threshold
            network.neuron_params[i] = {
                "tau": 10.0,          # Membrane time constant (ms)
                "v_rest": -70.0,      # Resting potential (mV)
                "v_threshold": -50.0, # Threshold potential (mV)
                "v_reset": -70.0,     # Reset potential (mV)
                "refractory_period": 1  # Refractory period (ms)
            }
    
    # 2. Enforce Dale's law - excitatory/inhibitory neurons can only form excitatory/inhibitory synapses
    weights = network.connections.toarray()
    
    for i in range(total_neurons):
        if network.types[i] == 0:  # Excitatory
            # Ensure all outgoing weights are positive
            out_weights = weights[i, :]
            out_weights[out_weights < 0] = 0
            weights[i, :] = out_weights
        elif network.types[i] == 1:  # Inhibitory
            # Ensure all outgoing weights are negative
            out_weights = weights[i, :]
            out_weights[out_weights > 0] = -out_weights[out_weights > 0]
            weights[i, :] = out_weights
    
    # Update connections
    network.connections = weights
    
    # 3. Set up STDP with realistic parameters
    stdp_params = {
        "A_plus": 0.005,     # Smaller weight changes
        "A_minus": -0.0025,  # Asymmetric STDP
        "tau_plus": 20.0,
        "tau_minus": 20.0
    }
    
    plasticity_engine = NeuroplasticityEngine(
        eta=0.01, alpha=0.001,
        A_plus=stdp_params["A_plus"], 
        A_minus=stdp_params["A_minus"],
        tau_plus=stdp_params["tau_plus"], 
        tau_minus=stdp_params["tau_minus"]
    )
    
    # 4. Set up other components with realistic parameters
    pruning_module = SynapticPruningModule(
        theta_prune=0.05,   # Lower pruning threshold
        theta_usage=0.05,   # Lower usage threshold
        pruning_rate=0.001  # Slower pruning rate
    )
    
    neuromodulation_system = NeuromodulationSystem(
        p_baseline={
            "dopamine": 1.0,
            "serotonin": 1.0,
            "norepinephrine": 1.0,
            "acetylcholine": 1.0
        },
        delta_p={
            "learning": 0.3,    # More moderate modulation effects
            "threshold": 0.2,
            "gain": 0.3,
            "adaptation": 0.1
        },
        tau_m=200.0  # Slower neuromodulator dynamics
    )
    
    # Set up gamma-theta coupling as in hippocampus
    biological_timing = BiologicalTimingCircuits(
        {
            "delta": {"frequency": 1.5, "amplitude": 1.0, "phase": 0.0},
            "theta": {"frequency": 8.0, "amplitude": 1.0, "phase": 0.0},   # Hippocampal theta
            "alpha": {"frequency": 10.0, "amplitude": 0.8, "phase": 0.0},
            "beta": {"frequency": 20.0, "amplitude": 0.6, "phase": 0.0},
            "gamma": {"frequency": 40.0, "amplitude": 0.5, "phase": 0.0}   # Gamma
        },
        {
            "theta_gamma": 0.7,  # Strong theta-gamma coupling
            "alpha_beta": 0.3
        }
    )
    
    # Connect all components
    network.set_neuroplasticity_engine(plasticity_engine)
    network.set_synaptic_pruning_module(pruning_module)
    network.set_neuromodulation_system(neuromodulation_system)
    network.set_biological_timing_circuits(biological_timing)
    
    return network

# Finally, add pytest hooks for better test reporting

def pytest_configure(config):
    """Add custom markers for ULTRA tests"""
    config.addinivalue_line(
        "markers",
        "neuromorphic_core: mark tests related to the Neuromorphic Core"
    )
    config.addinivalue_line(
        "markers",
        "neuroplasticity: mark tests related to the Neuroplasticity Engine"
    )
    config.addinivalue_line(
        "markers",
        "synaptic_pruning: mark tests related to the Synaptic Pruning Module"
    )
    config.addinivalue_line(
        "markers",
        "neuromodulation: mark tests related to the Neuromodulation System"
    )
    config.addinivalue_line(
        "markers",
        "biological_timing: mark tests related to the Biological Timing Circuits"
    )
    config.addinivalue_line(
        "markers",
        "integration: mark integration tests for the Core Neural Architecture"
    )
    config.addinivalue_line(
        "markers",
        "slow: mark tests that take a long time to run"
    )

def pytest_report_teststatus(report):
    """Add emoji to test reports to make them more readable"""
    if report.when == 'call':
        if hasattr(report, 'wasxfail'):
            if report.skipped:
                return 'xfailed', 'x', '❗ XFAIL'
            elif report.passed:
                return 'xpassed', 'X', '⚠️ XPASS'
        if report.passed:
            return 'passed', '.', '✅ PASS'
        elif report.skipped:
            return 'skipped', 's', '🚧 SKIP'
        elif report.failed:
            return 'failed', 'F', '❌ FAIL'