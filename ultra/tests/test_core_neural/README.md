# ULTRA Core Neural Architecture - Test Framework

This directory contains comprehensive tests for the Core Neural Architecture components of the ULTRA system. These tests verify the correct implementation and integration of neuromorphic computing principles, neural plasticity mechanisms, synaptic pruning, neuromodulation, and biological timing circuits.

## Test Structure

The test framework is organized into the following main components:

- **`__init__.py`**: Core test utilities, constants, and shared fixtures
- **`conftest.py`**: Pytest configuration, additional fixtures, and test reporting customization
- **`test_neuromorphic_core.py`**: Tests for the foundational 3D neural network
- **`test_neuroplasticity_engine.py`**: Tests for STDP, homeostatic plasticity, and structural plasticity
- **`test_synaptic_pruning.py`**: Tests for synaptic pruning based on weights, activity, and redundancy
- **`test_neuromodulation.py`**: Tests for neuromodulator dynamics and parameter modulation
- **`test_biological_timing.py`**: Tests for neural oscillators and phase-amplitude coupling
- **`test_integration.py`**: Integration tests for the combined functionality of all components

## Running Tests

To run the full test suite:

```bash
# From the repository root
pytest ultra/tests/test_core_neural

# Run with more verbose output
pytest -v ultra/tests/test_core_neural

# Run a specific test file
pytest ultra/tests/test_core_neural/test_neuroplasticity_engine.py

# Run a specific test class
pytest ultra/tests/test_core_neural/test_neuromorphic_core.py::TestNeuromorphicCore

# Run a specific test method
pytest ultra/tests/test_core_neural/test_neuromorphic_core.py::TestNeuromorphicCore::test_initialization
```

## Test Categories

Tests are grouped into categories using pytest markers:

- `neuromorphic_core`: Tests for the Neuromorphic Core
- `neuroplasticity`: Tests for the Neuroplasticity Engine
- `synaptic_pruning`: Tests for the Synaptic Pruning Module
- `neuromodulation`: Tests for the Neuromodulation System
- `biological_timing`: Tests for the Biological Timing Circuits
- `integration`: Integration tests for all components
- `slow`: Tests that take a long time to run

You can run tests for a specific category:

```bash
# Run all neuroplasticity tests
pytest -m neuroplasticity ultra/tests/test_core_neural

# Run all integration tests
pytest -m integration ultra/tests/test_core_neural

# Skip slow tests
pytest -m "not slow" ultra/tests/test_core_neural
```

## Test Utilities

The test framework includes several utilities to facilitate thorough testing:

- **`NetworkStateRecorder`**: Records and analyzes network state over time
- **`InputPatternGenerator`**: Generates various input patterns for testing
- **`TestUtils`**: Collection of utility functions for testing
- **`validate_*`** functions: Validate mathematical correctness of implementations
- **`analytical_*`** functions: Provide analytical solutions for comparison

## Visualization

Tests can generate visualizations to help understand network behavior:

```python
def test_example(enable_plotting):
    # Test code...
    
    # Create and configure a plot
    plt = enable_plotting.create_plot("my_analysis", "Neuron Activity")
    plt.plot(time, activity)
    plt.xlabel("Time (ms)")
    plt.ylabel("Activity")
    
    # Plots are automatically saved at the end of the test
```

## Adding New Tests

When adding new tests, follow these guidelines:

1. Use the appropriate test file for the component you're testing
2. Add integration tests for cross-component functionality
3. Use fixtures from `conftest.py` for common setup
4. Add appropriate pytest markers
5. Include both unit tests and functional tests
6. Validate against analytical solutions when possible
7. Test edge cases and error conditions

## Test Coverage

Aim for comprehensive test coverage including:

- Basic initialization and parameter validation
- Correct implementation of mathematical models
- Dynamic behavior over time
- Learning and adaptation capabilities
- Interaction between components
- Stability under various conditions
- Edge cases and error handling
- Performance and scaling characteristics

## Performance Benchmarks

Some tests include performance benchmarks to ensure implementations remain efficient:

```bash
# Run performance benchmarks
pytest ultra/tests/test_core_neural/test_performance.py
```

## Debugging

For debugging complex tests, increase verbosity and use the `--pdb` option to drop into the debugger on failures:

```bash
pytest -vvs --pdb ultra/tests/test_core_neural/test_integration.py
```