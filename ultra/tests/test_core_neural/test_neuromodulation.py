#!/usr/bin/env python3
"""
ULTRA: Ultimate Learning & Thought Reasoning Architecture
Core Neural Architecture - Neuromodulation System Tests

This module contains tests for the Neuromodulation System component, which implements
artificial analogs to neurotransmitters to globally regulate network activity, learning,
and computational modes based on context and task demands.
"""

import os
import sys
import numpy as np
import pytest
import matplotlib.pyplot as plt
import logging
from numpy.testing import assert_array_almost_equal
from scipy.stats import pearsonr

# Ensure the test module is in the path
ROOT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if ROOT_DIR not in sys.path:
    sys.path.insert(0, ROOT_DIR)

# Import test configuration and utilities
from tests.test_core_neural import (
    neuromodulation_system, network_state_recorder, input_pattern_generator,
    NEUROMODULATION_DEFAULT_PARAMS, TestUtils, validate_neuromodulation, analytical_neuromodulator
)

# Import modules to test
from ultra.core_neural.neuromodulation import (
    NeuromodulationSystem, Neuromodulator, ModulationProfile
)

# Configure logging
logger = logging.getLogger(__name__)

class TestNeuromodulator:
    """Test suite for the individual Neuromodulator component"""
    
    def test_initialization(self):
        """Test that neuromodulators initialize with the correct parameters"""
        # Create neuromodulator with default parameters
        name = "dopamine"
        baseline = 1.0
        tau = 100.0
        
        neuromodulator = Neuromodulator(name=name, baseline=baseline, tau=tau)
        
        # Check parameters
        assert neuromodulator.name == name, f"Name should be {name}"
        assert neuromodulator.baseline == baseline, f"Baseline should be {baseline}"
        assert neuromodulator.tau == tau, f"Tau should be {tau}"
        
        # Check initial state
        assert neuromodulator.level == baseline, "Initial level should equal baseline"
    
    def test_level_dynamics(self):
        """Test that neuromodulator levels change according to the differential equation"""
        # Create neuromodulator with known parameters
        baseline = 1.0
        tau = 100.0
        
        neuromodulator = Neuromodulator(name="test", baseline=baseline, tau=tau)
        
        # Test case 1: Input > baseline, level should increase
        input_signal = 1.5
        dt = 1.0
        
        # Apply update
        neuromodulator.update(input_signal, dt)
        
        # Level should increase toward input
        assert neuromodulator.level > baseline, "Level should increase when input > baseline"
        
        # Calculate expected increase based on the differential equation:
        # dm/dt = (-m(t) + I(t)) / τ_m
        expected_increase = (input_signal - baseline) * dt / tau
        expected_level = baseline + expected_increase
        
        assert abs(neuromodulator.level - expected_level) < 1e-6, \
            f"Level should increase by {expected_increase}, actual increase: {neuromodulator.level - baseline}"
        
        # Test case 2: Input < baseline, level should decrease
        neuromodulator.level = baseline  # Reset level
        input_signal = 0.5
        
        # Apply update
        neuromodulator.update(input_signal, dt)
        
        # Level should decrease toward input
        assert neuromodulator.level < baseline, "Level should decrease when input < baseline"
        
        # Calculate expected decrease
        expected_decrease = (input_signal - baseline) * dt / tau
        expected_level = baseline + expected_decrease
        
        assert abs(neuromodulator.level - expected_level) < 1e-6, \
            f"Level should decrease by {expected_decrease}, actual decrease: {baseline - neuromodulator.level}"
        
        # Test case 3: Input = baseline, level should stay constant
        neuromodulator.level = baseline  # Reset level
        input_signal = baseline
        
        # Apply update
        neuromodulator.update(input_signal, dt)
        
        # Level should remain unchanged
        assert abs(neuromodulator.level - baseline) < 1e-6, \
            "Level should remain constant when input = baseline"
    
    def test_convergence(self):
        """Test that neuromodulator levels converge to input signal with sustained input"""
        # Create neuromodulator with known parameters
        baseline = 1.0
        tau = 100.0
        
        neuromodulator = Neuromodulator(name="test", baseline=baseline, tau=tau)
        
        # Apply sustained input
        input_signal = 2.0
        dt = 1.0
        n_steps = 1000  # Run long enough to converge
        
        # Track level history
        levels = [neuromodulator.level]
        
        for _ in range(n_steps):
            neuromodulator.update(input_signal, dt)
            levels.append(neuromodulator.level)
        
        # Level should converge to input
        assert abs(neuromodulator.level - input_signal) < 1e-3, \
            f"Level should converge to input {input_signal}, got {neuromodulator.level}"
        
        # Convergence should be exponential with time constant tau
        # Check by fitting an exponential model to the approach
        # Extract the distance from target over time
        distance = np.array([abs(level - input_signal) for level in levels])
        log_distance = np.log(distance + 1e-10)  # Add small offset to avoid log(0)
        
        # Time values
        time = np.arange(len(levels)) * dt
        
        # For exponential decay, log(distance) should decrease linearly with time
        # with slope -1/tau
        if len(time) > 2:  # Need at least 3 points to calculate correlation
            correlation, _ = pearsonr(time, log_distance)
            assert correlation < -0.9, "Convergence should follow exponential decay pattern"
            
            # Estimate time constant from slope
            slope = np.polyfit(time, log_distance, 1)[0]
            estimated_tau = -1 / slope
            
            # Estimated tau should be close to actual tau
            assert 0.5 * tau < estimated_tau < 1.5 * tau, \
                f"Estimated time constant {estimated_tau} should be close to actual tau {tau}"

class TestModulationProfile:
    """Test suite for the Modulation Profile component"""
    
    def test_initialization(self):
        """Test that modulation profiles initialize with the correct parameters"""
        # Create modulation profile with default parameters
        param_name = "learning_rate"
        modulators = ["dopamine", "serotonin"]
        weights = [0.8, 0.2]
        baseline = 1.0
        
        profile = ModulationProfile(param_name=param_name, modulators=modulators, 
                                   weights=weights, baseline=baseline)
        
        # Check parameters
        assert profile.param_name == param_name, f"Parameter name should be {param_name}"
        assert profile.modulators == modulators, f"Modulators should be {modulators}"
        assert np.allclose(profile.weights, weights), f"Weights should be {weights}"
        assert profile.baseline == baseline, f"Baseline should be {baseline}"
    
    def test_compute_modulation(self):
        """Test that modulation is computed correctly based on neuromodulator levels"""
        # Create modulation profile
        param_name = "learning_rate"
        modulators = ["dopamine", "serotonin", "norepinephrine"]
        weights = [0.6, 0.3, 0.1]
        baseline = 1.0
        
        profile = ModulationProfile(param_name=param_name, modulators=modulators, 
                                   weights=weights, baseline=baseline)
        
        # Create modulators with different levels
        levels = {
            "dopamine": 1.5,       # Above baseline
            "serotonin": 0.5,       # Below baseline
            "norepinephrine": 1.0   # At baseline
        }
        
        # Compute modulation
        modulation = profile.compute_modulation(levels)
        
        # Calculate expected modulation
        # p(t) = p_baseline + Σ w_i * (m_i(t) - baseline_i)
        expected = baseline + 0.6 * (1.5 - 1.0) + 0.3 * (0.5 - 1.0) + 0.1 * (1.0 - 1.0)
        expected = baseline + 0.6 * 0.5 - 0.3 * 0.5 + 0
        expected = baseline + 0.15
        
        assert abs(modulation - expected) < 1e-6, \
            f"Modulation should be {expected}, got {modulation}"
        
        # Test with missing modulator (should use baseline)
        levels_missing = {
            "dopamine": 1.5,
            "norepinephrine": 1.0
            # serotonin missing
        }
        
        modulation_missing = profile.compute_modulation(levels_missing)
        
        # Calculate expected modulation (using baseline for missing modulators)
        expected_missing = baseline + 0.6 * (1.5 - 1.0) + 0.3 * (1.0 - 1.0) + 0.1 * (1.0 - 1.0)
        expected_missing = baseline + 0.6 * 0.5
        expected_missing = baseline + 0.3
        
        assert abs(modulation_missing - expected_missing) < 1e-6, \
            f"Modulation with missing modulator should be {expected_missing}, got {modulation_missing}"
    
    def test_modulation_bounds(self):
        """Test that modulation respects bounds if specified"""
        # Create modulation profile with bounds
        param_name = "threshold"
        modulators = ["dopamine"]
        weights = [1.0]
        baseline = 1.0
        bounds = (0.5, 2.0)  # Modulation should stay within these bounds
        
        profile = ModulationProfile(param_name=param_name, modulators=modulators, 
                                   weights=weights, baseline=baseline, bounds=bounds)
        
        # Test case 1: Modulation within bounds
        levels1 = {"dopamine": 1.5}  # Should give modulation = 1.5
        modulation1 = profile.compute_modulation(levels1)
        
        assert modulation1 == 1.5, f"Modulation should be 1.5, got {modulation1}"
        
        # Test case 2: Modulation above upper bound
        levels2 = {"dopamine": 3.0}  # Would give modulation = 3.0, but bounded to 2.0
        modulation2 = profile.compute_modulation(levels2)
        
        assert modulation2 == 2.0, f"Modulation should be bounded to 2.0, got {modulation2}"
        
        # Test case 3: Modulation below lower bound
        levels3 = {"dopamine": 0.0}  # Would give modulation = 0.0, but bounded to 0.5
        modulation3 = profile.compute_modulation(levels3)
        
        assert modulation3 == 0.5, f"Modulation should be bounded to 0.5, got {modulation3}"

class TestNeuromodulationSystem:
    """Test suite for the integrated Neuromodulation System"""
    
    def test_initialization(self, neuromodulation_system):
        """Test that the system initializes with the correct parameters and components"""
        system = neuromodulation_system
        
        # Check that modulators are initialized
        assert "dopamine" in system.modulators, "System should have dopamine modulator"
        assert "serotonin" in system.modulators, "System should have serotonin modulator"
        assert "norepinephrine" in system.modulators, "System should have norepinephrine modulator"
        assert "acetylcholine" in system.modulators, "System should have acetylcholine modulator"
        
        # Check modulator parameters
        for name, modulator in system.modulators.items():
            assert modulator.name == name, f"Modulator name should be {name}"
            assert modulator.baseline == NEUROMODULATION_DEFAULT_PARAMS["baseline_levels"][name], \
                f"Modulator baseline should match default params"
            assert modulator.tau == NEUROMODULATION_DEFAULT_PARAMS["tau_m"], \
                f"Modulator tau should match default params"
            assert modulator.level == modulator.baseline, "Initial level should equal baseline"
        
        # Check that modulation profiles are initialized
        assert "learning" in system.profiles, "System should have learning modulation profile"
        assert "threshold" in system.profiles, "System should have threshold modulation profile"
        assert "gain" in system.profiles, "System should have gain modulation profile"
        assert "adaptation" in system.profiles, "System should have adaptation modulation profile"
    
    def test_update_modulators(self, neuromodulation_system):
        """Test that modulators are updated correctly based on input signals"""
        system = neuromodulation_system
        
        # Create input signals
        input_signals = {
            "dopamine": 1.5,        # Above baseline
            "serotonin": 0.5,        # Below baseline
            "norepinephrine": 1.0,   # At baseline
            "acetylcholine": 2.0     # Well above baseline
        }
        
        # Record initial levels
        initial_levels = {name: modulator.level for name, modulator in system.modulators.items()}
        
        # Apply update
        dt = 1.0
        system.update(input_signals, dt)
        
        # Check that levels changed as expected
        for name, modulator in system.modulators.items():
            initial = initial_levels[name]
            input_val = input_signals[name]
            new_level = modulator.level
            
            if input_val > initial:
                assert new_level > initial, \
                    f"{name} level should increase when input > initial"
            elif input_val < initial:
                assert new_level < initial, \
                    f"{name} level should decrease when input < initial"
            else:
                assert abs(new_level - initial) < 1e-6, \
                    f"{name} level should remain constant when input = initial"
    
    def test_compute_modulations(self, neuromodulation_system):
        """Test that parameter modulations are computed correctly"""
        system = neuromodulation_system
        
        # Set specific modulator levels
        system.modulators["dopamine"].level = 1.5        # Above baseline
        system.modulators["serotonin"].level = 0.5        # Below baseline
        system.modulators["norepinephrine"].level = 1.2   # Slightly above baseline
        system.modulators["acetylcholine"].level = 1.0    # At baseline
        
        # Compute modulations
        modulations = system.compute_modulations()
        
        # Check that all parameters are modulated
        assert "learning" in modulations, "Learning rate should be modulated"
        assert "threshold" in modulations, "Threshold should be modulated"
        assert "gain" in modulations, "Gain should be modulated"
        assert "adaptation" in modulations, "Adaptation should be modulated"
        
        # Check specific modulations
        # 1. Dopamine should increase learning rate
        assert modulations["learning"] > 1.0, "Learning rate should be increased by dopamine"
        
        # 2. Serotonin being low should affect threshold
        # (specific effect depends on the weight)
        
        # 3. Norepinephrine should increase gain
        assert modulations["gain"] > 1.0, "Gain should be increased by norepinephrine"
    
    def test_dopamine_reward_response(self, neuromodulation_system, input_pattern_generator):
        """Test system response to simulated reward signals (dopamine)"""
        system = neuromodulation_system
        
        # Reset modulator levels to baseline
        for modulator in system.modulators.values():
            modulator.level = modulator.baseline
        
        # Simulate dopamine response to rewards
        duration = 1000  # ms
        dt = 1.0  # ms
        reward_times = [200, 500, 800]  # ms
        reward_amplitude = 1.0
        
        dopamine_signal = TestUtils.simulate_dopamine_reward(
            duration, reward_times, reward_amplitude, baseline=1.0, tau_decay=100.0, dt=dt
        )
        
        # Create input signals (only dopamine varies)
        input_signals = []
        for t in range(int(duration / dt)):
            signals = {
                "dopamine": dopamine_signal[t],
                "serotonin": 1.0,
                "norepinephrine": 1.0,
                "acetylcholine": 1.0
            }
            input_signals.append(signals)
        
        # Track dopamine levels and learning modulation
        dopamine_levels = []
        learning_modulations = []
        
        # Run simulation
        for t in range(int(duration / dt)):
            # Update modulators
            system.update(input_signals[t], dt)
            
            # Record values
            dopamine_levels.append(system.modulators["dopamine"].level)
            modulations = system.compute_modulations()
            learning_modulations.append(modulations["learning"])
        
        # Check dopamine response characteristics
        
        # 1. Peaks should occur shortly after reward times
        for reward_time in reward_times:
            reward_idx = int(reward_time / dt)
            # Find nearest peak after reward
            peak_idx = reward_idx + np.argmax(dopamine_levels[reward_idx:reward_idx+100])
            
            # Peak should occur close to reward time (allowing for time to build up)
            time_to_peak = (peak_idx - reward_idx) * dt
            assert time_to_peak < 20, f"Dopamine should peak shortly after reward, delay: {time_to_peak}ms"
            
            # Peak should be significantly above baseline
            peak_value = dopamine_levels[peak_idx]
            assert peak_value > 1.2, f"Dopamine peak should be well above baseline, got {peak_value}"
        
        # 2. Learning modulation should follow dopamine pattern
        assert np.corrcoef(dopamine_levels, learning_modulations)[0, 1] > 0.9, \
            "Learning modulation should be strongly correlated with dopamine levels"
    
    def test_attention_modulation(self, neuromodulation_system):
        """Test how norepinephrine affects attention and gain"""
        system = neuromodulation_system
        
        # Reset modulator levels to baseline
        for modulator in system.modulators.values():
            modulator.level = modulator.baseline
        
        # Set up norepinephrine levels
        ne_levels = [0.5, 1.0, 1.5, 2.0]  # From low to high
        
        gain_values = []
        threshold_values = []
        
        for ne_level in ne_levels:
            # Set norepinephrine level
            system.modulators["norepinephrine"].level = ne_level
            
            # Compute modulations
            modulations = system.compute_modulations()
            
            # Record values
            gain_values.append(modulations["gain"])
            threshold_values.append(modulations["threshold"])
        
        # Norepinephrine should increase gain
        assert gain_values[0] < gain_values[1] < gain_values[2] < gain_values[3], \
            "Gain should increase monotonically with norepinephrine level"
        
        # Norepinephrine can also affect threshold (often decreases it to increase sensitivity)
        # The exact relationship depends on the specific model parameters
    
    def test_modulation_interaction(self, neuromodulation_system):
        """Test the interaction between different neuromodulators"""
        system = neuromodulation_system
        
        # Reset modulator levels to baseline
        for modulator in system.modulators.values():
            modulator.level = modulator.baseline
        
        # Test case 1: High dopamine, low serotonin
        system.modulators["dopamine"].level = 1.5
        system.modulators["serotonin"].level = 0.5
        system.modulators["norepinephrine"].level = 1.0
        system.modulators["acetylcholine"].level = 1.0
        
        modulations1 = system.compute_modulations()
        
        # Test case 2: Low dopamine, high serotonin
        system.modulators["dopamine"].level = 0.5
        system.modulators["serotonin"].level = 1.5
        system.modulators["norepinephrine"].level = 1.0
        system.modulators["acetylcholine"].level = 1.0
        
        modulations2 = system.compute_modulations()
        
        # Test case 3: All high
        system.modulators["dopamine"].level = 1.5
        system.modulators["serotonin"].level = 1.5
        system.modulators["norepinephrine"].level = 1.5
        system.modulators["acetylcholine"].level = 1.5
        
        modulations3 = system.compute_modulations()
        
        # Test case 4: All low
        system.modulators["dopamine"].level = 0.5
        system.modulators["serotonin"].level = 0.5
        system.modulators["norepinephrine"].level = 0.5
        system.modulators["acetylcholine"].level = 0.5
        
        modulations4 = system.compute_modulations()
        
        # Different combinations should produce distinct modulation patterns
        
        # Learning should be higher in case 1 than case 2 (dopamine effect)
        assert modulations1["learning"] > modulations2["learning"], \
            "Learning should be higher with high dopamine"
        
        # Case 3 (all high) should generally have higher modulation values than case 4 (all low)
        for param in modulations3:
            assert modulations3[param] > modulations4[param], \
                f"{param} modulation should be higher when all neuromodulators are high"
    
    def test_parameter_modulation(self, neuromodulation_system):
        """Test the application of modulation to neural parameters"""
        system = neuromodulation_system
        
        # Set specific modulator levels
        system.modulators["dopamine"].level = 1.5        # High dopamine
        system.modulators["serotonin"].level = 1.0        # Normal serotonin
        system.modulators["norepinephrine"].level = 1.5   # High norepinephrine
        system.modulators["acetylcholine"].level = 1.0    # Normal acetylcholine
        
        # Define baseline parameters
        learning_rate_base = 0.01
        threshold_base = 1.0
        gain_base = 1.0
        adaptation_base = 0.1
        
        # Apply modulation
        learning_rate_mod = system.modulate_parameter("learning", learning_rate_base)
        threshold_mod = system.modulate_parameter("threshold", threshold_base)
        gain_mod = system.modulate_parameter("gain", gain_base)
        adaptation_mod = system.modulate_parameter("adaptation", adaptation_base)
        
        # High dopamine should increase learning rate
        assert learning_rate_mod > learning_rate_base, \
            "High dopamine should increase learning rate"
        
        # High norepinephrine should increase gain
        assert gain_mod > gain_base, \
            "High norepinephrine should increase gain"
        
        # Test non-existent parameter (should return baseline)
        nonexistent_base = 2.0
        nonexistent_mod = system.modulate_parameter("nonexistent", nonexistent_base)
        
        assert nonexistent_mod == nonexistent_base, \
            "Non-existent parameter should return baseline value"
    
    def test_modeling_states(self, neuromodulation_system):
        """Test that the system can model different computational states/modes"""
        system = neuromodulation_system
        
        # Define different computational states
        states = {
            "focused": {
                "dopamine": 1.0,
                "serotonin": 1.0,
                "norepinephrine": 1.5,  # High norepinephrine for focus
                "acetylcholine": 1.5     # High acetylcholine for attention
            },
            "exploratory": {
                "dopamine": 1.5,         # High dopamine for exploration
                "serotonin": 1.0,
                "norepinephrine": 1.2,   # Moderate norepinephrine
                "acetylcholine": 0.8     # Lower acetylcholine
            },
            "learning": {
                "dopamine": 1.5,         # High dopamine for learning
                "serotonin": 1.2,        # Elevated serotonin
                "norepinephrine": 1.0,
                "acetylcholine": 1.3     # Elevated acetylcholine
            },
            "resting": {
                "dopamine": 0.8,         # Lower dopamine
                "serotonin": 1.2,        # Elevated serotonin
                "norepinephrine": 0.7,   # Lower norepinephrine
                "acetylcholine": 0.7     # Lower acetylcholine
            }
        }
        
        modulation_patterns = {}
        
        # Set modulator levels for each state and compute modulations
        for state_name, levels in states.items():
            # Set levels
            for modulator_name, level in levels.items():
                system.modulators[modulator_name].level = level
            
            # Compute modulations
            modulations = system.compute_modulations()
            modulation_patterns[state_name] = modulations
        
        # Check characteristics of each state
        
        # Focused state should have high gain
        assert modulation_patterns["focused"]["gain"] > modulation_patterns["resting"]["gain"], \
            "Focused state should have higher gain than resting state"
        
        # Exploratory state should have high learning rate
        assert modulation_patterns["exploratory"]["learning"] > modulation_patterns["resting"]["learning"], \
            "Exploratory state should have higher learning rate than resting state"
        
        # Learning state should have high learning rate
        assert modulation_patterns["learning"]["learning"] > modulation_patterns["resting"]["learning"], \
            "Learning state should have higher learning rate than resting state"
        
        # States should be distinguishable from each other
        # (calculate distance between modulation patterns)
        def pattern_distance(pattern1, pattern2):
            return sum((pattern1[param] - pattern2[param])**2 for param in pattern1)
        
        for state1 in states:
            for state2 in states:
                if state1 != state2:
                    distance = pattern_distance(modulation_patterns[state1], modulation_patterns[state2])
                    assert distance > 0.01, \
                        f"States {state1} and {state2} should have distinct modulation patterns"
    
    def test_contextual_regulation(self, neuromodulation_system):
        """Test the system's ability to regulate network parameters based on context signals"""
        system = neuromodulation_system
        
        # Define a simple neural network simulation
        n_neurons = 10
        membrane_potentials = np.zeros(n_neurons)
        thresholds = np.ones(n_neurons)
        learning_rates = np.ones(n_neurons) * 0.01
        
        # Function to update neurons based on input and modulated parameters
        def update_neurons(potentials, thresholds, inputs, gain, dt):
            # Simple leaky integrate-and-fire dynamics
            tau = 10.0  # ms
            
            # Apply gain to inputs
            effective_inputs = inputs * gain
            
            # Update potentials
            dv = (-potentials + effective_inputs) * dt / tau
            potentials += dv
            
            # Check for spikes
            spikes = potentials > thresholds
            potentials[spikes] = 0  # Reset
            
            return potentials, np.sum(spikes)
        
        # Simulate in different contexts with corresponding neuromodulation
        contexts = {
            "high_attention": {
                "inputs": np.random.rand(n_neurons) * 0.5 + 0.5,  # [0.5, 1.0]
                "neuromodulators": {
                    "norepinephrine": 1.5,  # High norepinephrine for attention
                    "acetylcholine": 1.5,   # High acetylcholine for attention
                    "dopamine": 1.0,
                    "serotonin": 1.0
                }
            },
            "low_attention": {
                "inputs": np.random.rand(n_neurons) * 0.5 + 0.5,  # Same input range
                "neuromodulators": {
                    "norepinephrine": 0.5,  # Low norepinephrine
                    "acetylcholine": 0.5,   # Low acetylcholine
                    "dopamine": 1.0,
                    "serotonin": 1.0
                }
            }
        }
        
        results = {}
        
        # Run simulation for each context
        for context_name, context in contexts.items():
            # Set neuromodulator levels
            for modulator_name, level in context["neuromodulators"].items():
                system.modulators[modulator_name].level = level
            
            # Compute modulations
            modulations = system.compute_modulations()
            
            # Modulate parameters
            thresholds_mod = thresholds * modulations["threshold"]
            gain_mod = modulations["gain"]
            
            # Initialize
            membrane_potentials_sim = np.zeros(n_neurons)
            total_spikes = 0
            
            # Run for 100 steps
            for _ in range(100):
                membrane_potentials_sim, spikes = update_neurons(
                    membrane_potentials_sim, thresholds_mod, context["inputs"], gain_mod, dt=1.0
                )
                total_spikes += spikes
            
            results[context_name] = total_spikes
        
        # High attention context should produce more spikes
        # (due to higher gain and potentially lower thresholds)
        assert results["high_attention"] > results["low_attention"], \
            "High attention context should produce more spikes than low attention context"