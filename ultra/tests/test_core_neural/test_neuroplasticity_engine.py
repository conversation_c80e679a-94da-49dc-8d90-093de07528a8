#!/usr/bin/env python3
"""
ULTRA: Ultimate Learning & Thought Reasoning Architecture
Core Neural Architecture - Neuroplasticity Engine Tests

This module contains tests for the Neuroplasticity Engine component, which implements
various forms of synaptic plasticity including STDP, homeostatic plasticity, and
structural plasticity for dynamic connection formation and removal.
"""

import os
import sys
import numpy as np
import pytest
import scipy.sparse
import matplotlib.pyplot as plt
import logging
from numpy.testing import assert_array_almost_equal
from scipy.stats import pearsonr

# Ensure the test module is in the path
ROOT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if ROOT_DIR not in sys.path:
    sys.path.insert(0, ROOT_DIR)

# Import test configuration and utilities
from tests.test_core_neural import (
    neuroplasticity_engine, network_state_recorder, input_pattern_generator,
    STDP_DEFAULT_PARAMS, TestUtils, validate_stdp_rule, analytical_stdp
)

# Import modules to test
from ultra.core_neural.neuroplasticity_engine import (
    NeuroplasticityEngine, STDPMechanism, HomeostasisMechanism, StructuralPlasticityMechanism
)

# Configure logging
logger = logging.getLogger(__name__)

class TestSTDPMechanism:
    """Test suite for the Spike-Timing-Dependent Plasticity (STDP) component"""
    
    def test_stdp_rule(self):
        """Test that the STDP rule follows the mathematical formula from the paper"""
        # Create STDP mechanism with known parameters
        A_plus = 0.1
        A_minus = -0.1
        tau_plus = 20.0
        tau_minus = 20.0
        
        stdp = STDPMechanism(A_plus=A_plus, A_minus=A_minus, 
                             tau_plus=tau_plus, tau_minus=tau_minus)
        
        # Test a range of time differences
        delta_ts = np.arange(-50, 51, 5)  # -50ms to +50ms in 5ms steps
        
        for delta_t in delta_ts:
            # Calculate expected weight change using analytical formula
            expected_delta_w = analytical_stdp(A_plus, A_minus, tau_plus, tau_minus, delta_t)
            
            # Get actual weight change from STDP mechanism
            t_pre = 100  # Arbitrary reference time
            t_post = t_pre + delta_t
            
            actual_delta_w = stdp.compute_weight_change(t_pre, t_post)
            
            # Check that they match within tolerance
            assert abs(actual_delta_w - expected_delta_w) < 1e-6, \
                f"STDP rule mismatch for delta_t={delta_t}: expected {expected_delta_w}, got {actual_delta_w}"
    
    def test_stdp_learning_window(self):
        """Test that the STDP learning window has the correct shape"""
        # Create STDP mechanism
        stdp = STDPMechanism(A_plus=0.1, A_minus=-0.1, tau_plus=20.0, tau_minus=20.0)
        
        # Generate the STDP learning window
        delta_ts = np.linspace(-100, 100, 201)  # -100ms to +100ms in 1ms steps
        weight_changes = np.zeros_like(delta_ts)
        
        for i, delta_t in enumerate(delta_ts):
            t_pre = 1000  # Arbitrary reference time
            t_post = t_pre + delta_t
            weight_changes[i] = stdp.compute_weight_change(t_pre, t_post)
        
        # Check key properties of the learning window
        
        # 1. At delta_t = 0, weight change should be 0
        zero_idx = np.argmin(np.abs(delta_ts))
        assert abs(weight_changes[zero_idx]) < 1e-6, "Weight change at delta_t=0 should be close to 0"
        
        # 2. For delta_t > 0 (post spike after pre), weights should increase
        assert np.all(weight_changes[delta_ts > 0] > 0), "Weight changes should be positive for delta_t > 0"
        
        # 3. For delta_t < 0 (pre spike after post), weights should decrease
        assert np.all(weight_changes[delta_ts < 0] < 0), "Weight changes should be negative for delta_t < 0"
        
        # 4. The function should be continuous (no jumps)
        assert np.all(np.abs(np.diff(weight_changes)) < 0.01), "STDP function should be continuous"
        
        # 5. The function should be exponential (check log-linearity)
        # For delta_t > 0
        positive_idx = np.where(delta_ts > 10)[0]  # Start a bit away from 0 for numerical stability
        log_weights_pos = np.log(weight_changes[positive_idx])
        correlation_pos, _ = pearsonr(delta_ts[positive_idx], log_weights_pos)
        assert correlation_pos < -0.95, "Positive part of STDP window should follow exponential decay"
        
        # For delta_t < 0
        negative_idx = np.where(delta_ts < -10)[0]
        log_weights_neg = np.log(-weight_changes[negative_idx])  # Take log of absolute values
        correlation_neg, _ = pearsonr(-delta_ts[negative_idx], log_weights_neg)
        assert correlation_neg < -0.95, "Negative part of STDP window should follow exponential decay"
    
    def test_paired_spike_protocol(self, input_pattern_generator):
        """Test STDP with a standard paired-spike experimental protocol"""
        # Create STDP mechanism with known parameters
        A_plus = 0.1
        A_minus = -0.1
        tau_plus = 20.0
        tau_minus = 20.0
        
        stdp = STDPMechanism(A_plus=A_plus, A_minus=A_minus, 
                             tau_plus=tau_plus, tau_minus=tau_minus)
        
        # Define test conditions: varying time differences between pre and post spikes
        delta_ts = [-20, -10, -5, 5, 10, 20]  # in ms
        initial_weight = 0.5
        n_pairs = 60  # Number of spike pairs
        
        # For each condition, run the paired-spike protocol and measure weight change
        final_weights = []
        
        for delta_t in delta_ts:
            # Create paired spike trains
            duration = 10000  # ms
            pre_spikes, post_spikes = input_pattern_generator.paired_inputs(
                duration, delta_t, n_pairs, dt=1.0
            )
            
            # Initialize weight
            w = initial_weight
            
            # Process each pair and update weight
            # Find all spike times
            pre_times = np.where(pre_spikes > 0)[0]
            post_times = np.where(post_spikes > 0)[0]
            
            # Apply STDP for each pair
            for i in range(min(len(pre_times), len(post_times))):
                t_pre = pre_times[i]
                t_post = post_times[i]
                
                # Update weight
                delta_w = stdp.compute_weight_change(t_pre, t_post)
                w += delta_w
                
                # Apply bounds to prevent negative or exploding weights
                w = max(0, min(1, w))
            
            final_weights.append(w)
        
        # Check that the final weights follow the expected pattern:
        # - For delta_t > 0, weights should increase
        # - For delta_t < 0, weights should decrease
        # - The effect should be stronger for smaller absolute delta_t
        
        # Weights for negative delta_t should be less than initial weight
        for i, delta_t in enumerate(delta_ts):
            if delta_t < 0:
                assert final_weights[i] < initial_weight, \
                    f"Weight should decrease for delta_t={delta_t}"
                    
        # Weights for positive delta_t should be greater than initial weight
        for i, delta_t in enumerate(delta_ts):
            if delta_t > 0:
                assert final_weights[i] > initial_weight, \
                    f"Weight should increase for delta_t={delta_t}"
        
        # The effect should be stronger for smaller absolute delta_t
        # Find indices for negative delta_t pairs
        neg_idx = [i for i, dt in enumerate(delta_ts) if dt < 0]
        if len(neg_idx) >= 2:
            neg_weights = [final_weights[i] for i in neg_idx]
            neg_delta_ts = [abs(delta_ts[i]) for i in neg_idx]
            # Check that weights increase (less decrease) as |delta_t| increases
            assert all(neg_weights[i] <= neg_weights[i+1] for i in range(len(neg_weights)-1)), \
                "Weight decrease should be less for larger |delta_t|"
        
        # Find indices for positive delta_t pairs
        pos_idx = [i for i, dt in enumerate(delta_ts) if dt > 0]
        if len(pos_idx) >= 2:
            pos_weights = [final_weights[i] for i in pos_idx]
            pos_delta_ts = [delta_ts[i] for i in pos_idx]
            # Check that weights decrease (less increase) as delta_t increases
            assert all(pos_weights[i] >= pos_weights[i+1] for i in range(len(pos_weights)-1)), \
                "Weight increase should be less for larger delta_t"

class TestHomeostasisMechanism:
    """Test suite for the Homeostatic Plasticity component"""
    
    def test_synaptic_scaling(self):
        """Test that synaptic scaling normalizes total synaptic strength"""
        # Create homeostasis mechanism
        target_strength = 1.0
        scaling_rate = 0.1
        homeostasis = HomeostasisMechanism(target_strength=target_strength, scaling_rate=scaling_rate)
        
        # Create test weights
        n_synapses = 100
        
        # Test case 1: Initial weights sum to less than target
        weights1 = np.random.rand(n_synapses) * 0.5  # Sum will be about 25, less than target * n_synapses
        initial_sum1 = np.sum(weights1)
        
        # Apply scaling
        scaled_weights1 = homeostasis.scale_synapses(weights1)
        
        # Check that the sum increased toward target * n_synapses
        # The sum after scaling should be between the initial sum and the target
        assert np.sum(scaled_weights1) > initial_sum1, "Weights should increase when sum < target"
        
        # Repeated application should converge to target * n_synapses
        for _ in range(100):
            scaled_weights1 = homeostasis.scale_synapses(scaled_weights1)
        
        assert np.isclose(np.sum(scaled_weights1), target_strength * n_synapses, rtol=0.01), \
            "Weights should converge to target sum after repeated scaling"
        
        # Test case 2: Initial weights sum to more than target
        weights2 = np.random.rand(n_synapses) * 2.0  # Sum will be about 100, more than target * n_synapses
        initial_sum2 = np.sum(weights2)
        
        # Apply scaling
        scaled_weights2 = homeostasis.scale_synapses(weights2)
        
        # Check that the sum decreased toward target * n_synapses
        assert np.sum(scaled_weights2) < initial_sum2, "Weights should decrease when sum > target"
        
        # Repeated application should converge to target * n_synapses
        for _ in range(100):
            scaled_weights2 = homeostasis.scale_synapses(scaled_weights2)
        
        assert np.isclose(np.sum(scaled_weights2), target_strength * n_synapses, rtol=0.01), \
            "Weights should converge to target sum after repeated scaling"
        
        # Test case 3: Check that scaling preserves the relative strengths of synapses
        weights3 = np.array([0.1, 0.2, 0.3, 0.4])  # Initial weights
        initial_ratios = weights3[1:] / weights3[:-1]
        
        # Apply scaling
        scaled_weights3 = homeostasis.scale_synapses(weights3)
        
        # Calculate ratios after scaling
        scaled_ratios = scaled_weights3[1:] / scaled_weights3[:-1]
        
        # Check that ratios are preserved
        assert np.allclose(initial_ratios, scaled_ratios, rtol=0.01), \
            "Synaptic scaling should preserve relative strengths of synapses"
    
    def test_target_regulation(self):
        """Test that homeostatic plasticity regulates toward target activity"""
        # Create homeostasis mechanism
        target_rate = 10.0  # Hz
        adaptation_rate = 0.1
        homeostasis = HomeostasisMechanism(target_rate=target_rate, adaptation_rate=adaptation_rate)
        
        # Test threshold adaptation based on activity
        initial_threshold = 1.0
        
        # Test case 1: Activity below target
        actual_rate1 = 5.0  # Hz, below target
        new_threshold1 = homeostasis.adapt_threshold(initial_threshold, actual_rate1)
        
        # Threshold should decrease to increase activity
        assert new_threshold1 < initial_threshold, \
            "Threshold should decrease when activity is below target"
        
        # Test case 2: Activity above target
        actual_rate2 = 15.0  # Hz, above target
        new_threshold2 = homeostasis.adapt_threshold(initial_threshold, actual_rate2)
        
        # Threshold should increase to decrease activity
        assert new_threshold2 > initial_threshold, \
            "Threshold should increase when activity is above target"
        
        # Test case 3: Activity at target
        actual_rate3 = target_rate
        new_threshold3 = homeostasis.adapt_threshold(initial_threshold, actual_rate3)
        
        # Threshold should remain unchanged
        assert np.isclose(new_threshold3, initial_threshold, rtol=1e-6), \
            "Threshold should not change when activity is at target"
        
        # Test case 4: Convergence to equilibrium
        # Initialize threshold higher than optimal
        threshold = 2.0
        
        # Function to simulate how firing rate depends on threshold
        # (in a real network, this would be an emergent property)
        def simulate_firing_rate(threshold):
            # Simple model: rate decreases with threshold
            return 20.0 - 5.0 * threshold
        
        # Run adaptation until convergence
        for _ in range(100):
            rate = simulate_firing_rate(threshold)
            threshold = homeostasis.adapt_threshold(threshold, rate)
        
        # Check that the final threshold leads to a rate close to target
        final_rate = simulate_firing_rate(threshold)
        assert np.isclose(final_rate, target_rate, rtol=0.01), \
            "Threshold adaptation should converge to produce target firing rate"

class TestStructuralPlasticityMechanism:
    """Test suite for the Structural Plasticity component"""
    
    def test_connection_creation(self):
        """Test that new connections are created based on activity correlations"""
        # Create structural plasticity mechanism
        creation_threshold = 0.7
        deletion_threshold = 0.1
        structural_plasticity = StructuralPlasticityMechanism(
            creation_threshold=creation_threshold, deletion_threshold=deletion_threshold
        )
        
        # Create a connectivity matrix and activity correlation matrix
        n_neurons = 50
        
        # Initialize sparse connectivity (30% connected)
        connectivity = np.random.rand(n_neurons, n_neurons) < 0.3
        # No self-connections
        np.fill_diagonal(connectivity, 0)
        
        # Create activity correlation matrix with some high correlations
        activity_correlation = np.random.rand(n_neurons, n_neurons) * 0.5  # Most correlations low
        
        # Add some high correlations for a specific subset
        high_corr_pairs = [(i, i+1) for i in range(0, n_neurons-1, 2)]  # Every other pair
        for i, j in high_corr_pairs:
            activity_correlation[i, j] = 0.9  # High correlation
            activity_correlation[j, i] = 0.9  # Make it symmetric
        
        # Set correlated but already connected pair
        already_connected = (10, 11)
        connectivity[already_connected] = 1
        activity_correlation[already_connected] = 0.9
        activity_correlation[already_connected[1], already_connected[0]] = 0.9
        
        # Initial connection count
        initial_connections = np.sum(connectivity)
        
        # Apply structural plasticity
        new_connectivity = structural_plasticity.update_connections(
            connectivity, activity_correlation
        )
        
        # Check that new connections were created
        assert np.sum(new_connectivity) > initial_connections, \
            "New connections should be created"
        
        # Check that connections were added for highly correlated pairs
        for i, j in high_corr_pairs:
            if not connectivity[i, j]:  # Only check pairs that weren't already connected
                assert new_connectivity[i, j], \
                    f"Connection should be created for highly correlated pair ({i}, {j})"
        
        # Check that connections weren't added for weakly correlated pairs
        # Find a weakly correlated pair that wasn't connected
        weak_corr_pairs = []
        for i in range(n_neurons):
            for j in range(n_neurons):
                if i != j and not connectivity[i, j] and activity_correlation[i, j] < 0.5:
                    weak_corr_pairs.append((i, j))
                    if len(weak_corr_pairs) >= 5:  # Just check a few pairs
                        break
            if len(weak_corr_pairs) >= 5:
                break
        
        for i, j in weak_corr_pairs:
            assert not new_connectivity[i, j], \
                f"Connection should not be created for weakly correlated pair ({i}, {j})"
    
    def test_connection_pruning(self):
        """Test that weak connections are removed based on weight and usage"""
        # Create structural plasticity mechanism
        creation_threshold = 0.7
        deletion_threshold = 0.1
        structural_plasticity = StructuralPlasticityMechanism(
            creation_threshold=creation_threshold, deletion_threshold=deletion_threshold
        )
        
        # Create a connectivity matrix and weight matrix
        n_neurons = 50
        
        # Initialize connectivity (all connected)
        connectivity = np.ones((n_neurons, n_neurons))
        # No self-connections
        np.fill_diagonal(connectivity, 0)
        
        # Create weight matrix
        weights = np.random.rand(n_neurons, n_neurons)  # Random weights [0, 1)
        np.fill_diagonal(weights, 0)
        
        # Set some weights below deletion threshold
        weak_pairs = [(i, i+1) for i in range(0, n_neurons-1, 2)]  # Every other pair
        for i, j in weak_pairs:
            weights[i, j] = deletion_threshold * 0.5  # Below threshold
        
        # Create usage matrix (how often each synapse is active)
        usage = np.random.rand(n_neurons, n_neurons)
        np.fill_diagonal(usage, 0)
        
        # Set some connections with low usage
        low_usage_pairs = [(i, i+2) for i in range(0, n_neurons-2, 2)]
        for i, j in low_usage_pairs:
            usage[i, j] = deletion_threshold * 0.5  # Below threshold
        
        # Initial connection count
        initial_connections = np.sum(connectivity)
        
        # Apply structural plasticity
        new_connectivity = structural_plasticity.prune_connections(
            connectivity, weights, usage
        )
        
        # Check that connections were removed
        assert np.sum(new_connectivity) < initial_connections, \
            "Weak connections should be removed"
        
        # Check that connections were removed for weak pairs
        for i, j in weak_pairs:
            assert not new_connectivity[i, j], \
                f"Connection should be removed for weak pair ({i}, {j})"
        
        # Check that connections were removed for low usage pairs
        for i, j in low_usage_pairs:
            if weights[i, j] < deletion_threshold:  # Only prune if weight is also low
                assert not new_connectivity[i, j], \
                    f"Connection should be removed for low usage pair ({i}, {j})"
        
        # Check that strong, high-usage connections are preserved
        # Find a strong, high-usage pair
        strong_high_usage_pairs = []
        for i in range(n_neurons):
            for j in range(n_neurons):
                if i != j and weights[i, j] > 0.5 and usage[i, j] > 0.5:
                    strong_high_usage_pairs.append((i, j))
                    if len(strong_high_usage_pairs) >= 5:  # Just check a few pairs
                        break
            if len(strong_high_usage_pairs) >= 5:
                break
        
        for i, j in strong_high_usage_pairs:
            assert new_connectivity[i, j], \
                f"Connection should be preserved for strong, high-usage pair ({i}, {j})"
    
    def test_redundancy_detection(self):
        """Test that redundant connections are identified and pruned"""
        # Create structural plasticity mechanism
        creation_threshold = 0.7
        deletion_threshold = 0.1
        structural_plasticity = StructuralPlasticityMechanism(
            creation_threshold=creation_threshold, deletion_threshold=deletion_threshold
        )
        
        # Create a small network for testing
        n_neurons = 5
        
        # Initialize connectivity (all connected)
        connectivity = np.ones((n_neurons, n_neurons))
        # No self-connections
        np.fill_diagonal(connectivity, 0)
        
        # Create weight matrix
        weights = np.random.rand(n_neurons, n_neurons) * 0.5 + 0.25  # Random weights [0.25, 0.75)
        np.fill_diagonal(weights, 0)
        
        # Create spike correlation matrix (symmetric)
        spike_corr = np.zeros((n_neurons, n_neurons))
        
        # Set up redundant connections:
        # Neurons 0, 1, and 2 all provide similar information to neuron 3
        # Their spike trains are highly correlated
        spike_corr[0, 1] = 0.9
        spike_corr[0, 2] = 0.9
        spike_corr[1, 2] = 0.9
        # Make it symmetric
        spike_corr[1, 0] = 0.9
        spike_corr[2, 0] = 0.9
        spike_corr[2, 1] = 0.9
        
        # Calculate redundancy for connections to neuron 3
        redundancy = structural_plasticity.calculate_redundancy(
            3, connectivity, weights, spike_corr
        )
        
        # The connections from 0, 1, and 2 to 3 should have high redundancy
        assert redundancy[0] > 0.5, "Connection (0,3) should have high redundancy"
        assert redundancy[1] > 0.5, "Connection (1,3) should have high redundancy"
        assert redundancy[2] > 0.5, "Connection (2,3) should have high redundancy"
        
        # The connection from 4 to 3 should have low redundancy
        # (not correlated with other inputs)
        assert redundancy[4] < 0.3, "Connection (4,3) should have low redundancy"
        
        # Now test redundancy-based pruning
        # Set one connection to be weak
        weights[1, 3] = 0.1  # Make the connection from 1 to 3 weak
        
        # Apply pruning
        new_connectivity = structural_plasticity.prune_redundant_connections(
            connectivity, weights, spike_corr, deletion_threshold=0.1
        )
        
        # Check that redundant, weak connection was pruned
        assert not new_connectivity[1, 3], "Redundant, weak connection (1,3) should be pruned"
        
        # Check that redundant but strong connections are preserved
        assert new_connectivity[0, 3], "Redundant but strong connection (0,3) should be preserved"
        assert new_connectivity[2, 3], "Redundant but strong connection (2,3) should be preserved"
        
        # Check that non-redundant connection is preserved
        assert new_connectivity[4, 3], "Non-redundant connection (4,3) should be preserved"

class TestNeuroplasticityEngine:
    """Test suite for the integrated Neuroplasticity Engine"""
    
    def test_stdp_integration(self, neuroplasticity_engine, input_pattern_generator):
        """Test integration of STDP into the Neuroplasticity Engine"""
        engine = neuroplasticity_engine
        
        # Test STDP weight updates
        # Create a simple network with two neurons
        n_neurons = 2
        weights = np.array([[0, 0.5], [0.5, 0]])  # Initial weights
        
        # Generate spike trains that will cause potentiation (pre before post)
        duration = 1000  # ms
        dt = 1.0  # ms
        delta_t = 10  # ms, pre before post
        pre_spikes, post_spikes = input_pattern_generator.paired_inputs(
            duration, delta_t, 20, dt=dt
        )
        
        # Track weight changes
        weight_history = [weights.copy()]
        
        # Run simulation
        for t in range(int(duration / dt)):
            # Get spikes at this time step
            pre_spike = pre_spikes[t] > 0
            post_spike = post_spikes[t] > 0
            
            # Only consider connection from neuron 0 to neuron 1
            if pre_spike:
                engine.record_spike(t, 0)
            if post_spike:
                engine.record_spike(t, 1)
            
            # After every 50ms, update weights
            if t % 50 == 0 and t > 0:
                new_weights = engine.apply_stdp(weights)
                weight_history.append(new_weights.copy())
                weights = new_weights
        
        # Check that the connection 0->1 strengthened due to potentiation
        assert weight_history[-1][0, 1] > weight_history[0][0, 1], \
            "Weight should increase due to STDP potentiation"
        
        # Reset engine
        engine.reset()
        
        # Generate spike trains that will cause depression (post before pre)
        delta_t = -10  # ms, post before pre
        pre_spikes, post_spikes = input_pattern_generator.paired_inputs(
            duration, delta_t, 20, dt=dt
        )
        
        # Reset weights
        weights = np.array([[0, 0.5], [0.5, 0]])
        weight_history = [weights.copy()]
        
        # Run simulation
        for t in range(int(duration / dt)):
            # Get spikes at this time step
            pre_spike = pre_spikes[t] > 0
            post_spike = post_spikes[t] > 0
            
            # Only consider connection from neuron 0 to neuron 1
            if pre_spike:
                engine.record_spike(t, 0)
            if post_spike:
                engine.record_spike(t, 1)
            
            # After every 50ms, update weights
            if t % 50 == 0 and t > 0:
                new_weights = engine.apply_stdp(weights)
                weight_history.append(new_weights.copy())
                weights = new_weights
        
        # Check that the connection 0->1 weakened due to depression
        assert weight_history[-1][0, 1] < weight_history[0][0, 1], \
            "Weight should decrease due to STDP depression"
    
    def test_homeostatic_scaling(self, neuroplasticity_engine):
        """Test integration of homeostatic scaling into the Neuroplasticity Engine"""
        engine = neuroplasticity_engine
        
        # Create a neuron with multiple incoming connections
        # Weights will be scaled to maintain a target total
        incoming_weights = np.array([0.1, 0.2, 0.3, 0.4, 0.5])
        initial_sum = np.sum(incoming_weights)
        
        # Apply homeostatic scaling with different target totals
        
        # Test case 1: Target sum greater than actual sum
        target_sum1 = 2.0
        scaled_weights1 = engine.apply_homeostatic_scaling(incoming_weights, target_sum1)
        
        # Sum should increase toward target
        assert np.sum(scaled_weights1) > initial_sum, \
            "Weights should increase when sum < target"
        
        # Relative weights should be preserved
        ratios = incoming_weights[1:] / incoming_weights[:-1]
        scaled_ratios = scaled_weights1[1:] / scaled_weights1[:-1]
        assert np.allclose(ratios, scaled_ratios, rtol=0.01), \
            "Homeostatic scaling should preserve relative weights"
        
        # Test case 2: Target sum less than actual sum
        target_sum2 = 1.0
        scaled_weights2 = engine.apply_homeostatic_scaling(incoming_weights, target_sum2)
        
        # Sum should decrease toward target
        assert np.sum(scaled_weights2) < initial_sum, \
            "Weights should decrease when sum > target"
        
        # Relative weights should be preserved
        scaled_ratios2 = scaled_weights2[1:] / scaled_weights2[:-1]
        assert np.allclose(ratios, scaled_ratios2, rtol=0.01), \
            "Homeostatic scaling should preserve relative weights"
    
    def test_structural_plasticity(self, neuroplasticity_engine):
        """Test integration of structural plasticity into the Neuroplasticity Engine"""
        engine = neuroplasticity_engine
        
        # Create a small network for testing
        n_neurons = 10
        
        # Initialize sparse connectivity (50% connected)
        connectivity = np.random.rand(n_neurons, n_neurons) < 0.5
        connectivity_matrix = scipy.sparse.csr_matrix(connectivity)
        
        # Initialize weights (random between 0.1 and 0.9)
        weights = np.random.rand(n_neurons, n_neurons) * 0.8 + 0.1
        weights[~connectivity] = 0  # Ensure weights are 0 where not connected
        
        # Create activity correlation matrix
        # High correlation between neurons 0-4, low elsewhere
        activity_correlation = np.zeros((n_neurons, n_neurons))
        for i in range(5):
            for j in range(5):
                if i != j:
                    activity_correlation[i, j] = 0.8  # High correlation
        
        # Create usage statistics (how often each synapse is active)
        usage = np.random.rand(n_neurons, n_neurons) * 0.7 + 0.3  # [0.3, 1.0]
        usage[~connectivity] = 0  # Ensure usage is 0 where not connected
        
        # Set some connections to be weak
        weak_indices = np.where((connectivity) & (np.random.rand(n_neurons, n_neurons) < 0.3))
        weights[weak_indices] = 0.05  # Below deletion threshold
        
        # Initial connection count
        initial_connection_count = np.sum(connectivity)
        
        # Apply structural plasticity
        new_connectivity = engine.apply_structural_plasticity(
            connectivity_matrix.toarray(), weights, activity_correlation, usage
        )
        
        # Connectivity should have changed
        assert not np.array_equal(connectivity, new_connectivity), \
            "Structural plasticity should change connectivity"
        
        # Check that weak connections were pruned
        for i, j in zip(*weak_indices):
            # Some weak connections might have high usage or low redundancy
            # so we only check those with low usage
            if usage[i, j] < 0.4:
                assert not new_connectivity[i, j], \
                    f"Weak, low-usage connection ({i}, {j}) should be pruned"
        
        # Check that new connections were formed between highly correlated neurons
        for i in range(5):
            for j in range(5):
                if i != j and not connectivity[i, j]:
                    # This is a pair with high correlation that wasn't already connected
                    assert new_connectivity[i, j], \
                        f"New connection should form between highly correlated neurons ({i}, {j})"
    
    def test_full_plasticity_pipeline(self, neuroplasticity_engine, input_pattern_generator):
        """Test the full plasticity pipeline with all mechanisms active"""
        engine = neuroplasticity_engine
        
        # Create a small network for testing
        n_neurons = 10
        
        # Initialize sparse connectivity (50% connected)
        connectivity = np.random.rand(n_neurons, n_neurons) < 0.5
        # No self-connections
        np.fill_diagonal(connectivity, 0)
        
        # Initialize weights (random between 0.1 and 0.9)
        weights = np.random.rand(n_neurons, n_neurons) * 0.8 + 0.1
        weights[~connectivity] = 0  # Ensure weights are 0 where not connected
        
        # Generate activity data (spike rasters)
        duration = 2000  # ms
        dt = 1.0  # ms
        steps = int(duration / dt)
        
        # Create correlated spike patterns for neurons 0-4
        # Group 1: neurons 0-2 fire together
        # Group 2: neurons 3-4 fire together
        spike_trains = np.zeros((n_neurons, steps))
        
        # Generate spikes for Group 1 (synchronized)
        for t in range(0, steps, 100):  # Every 100ms
            if np.random.rand() < 0.8:  # 80% probability of group firing
                for i in range(3):
                    # Add jitter
                    spike_time = t + np.random.randint(-5, 6)
                    if 0 <= spike_time < steps:
                        spike_trains[i, spike_time] = 1
        
        # Generate spikes for Group 2 (synchronized)
        for t in range(50, steps, 100):  # Offset from Group 1
            if np.random.rand() < 0.8:
                for i in range(3, 5):
                    # Add jitter
                    spike_time = t + np.random.randint(-5, 6)
                    if 0 <= spike_time < steps:
                        spike_trains[i, spike_time] = 1
        
        # Generate spikes for the rest (independent)
        for i in range(5, n_neurons):
            rate = 10  # Hz
            spike_train = TestUtils.generate_spike_train(rate, duration, dt)
            spike_trains[i] = spike_train
        
        # Reset engine
        engine.reset()
        
        # Simulate network activity and apply plasticity
        for t in range(steps):
            # Record spikes
            for neuron_id in range(n_neurons):
                if spike_trains[neuron_id, t] > 0:
                    engine.record_spike(t, neuron_id)
            
            # Apply STDP every 100ms
            if t % 100 == 0 and t > 0:
                weights = engine.apply_stdp(weights)
        
        # Calculate activity correlation based on spike trains
        activity_correlation = np.zeros((n_neurons, n_neurons))
        for i in range(n_neurons):
            for j in range(i+1, n_neurons):
                corr, _ = pearsonr(spike_trains[i], spike_trains[j])
                if np.isnan(corr):
                    corr = 0
                activity_correlation[i, j] = corr
                activity_correlation[j, i] = corr
        
        # Calculate usage statistics (based on spike coincidences)
        usage = np.zeros((n_neurons, n_neurons))
        for i in range(n_neurons):
            for j in range(n_neurons):
                if connectivity[i, j]:
                    # Simple usage metric: count pre-post spike pairs within 20ms
                    pre_spikes = np.where(spike_trains[i] > 0)[0]
                    post_spikes = np.where(spike_trains[j] > 0)[0]
                    
                    coincidence_count = 0
                    for t_pre in pre_spikes:
                        # Count post spikes within 20ms after pre
                        coincidence_count += np.sum((post_spikes > t_pre) & (post_spikes <= t_pre + 20))
                    
                    # Normalize by number of pre spikes
                    if len(pre_spikes) > 0:
                        usage[i, j] = coincidence_count / len(pre_spikes)
        
        # Apply homeostatic scaling
        target_total = 1.0
        for neuron_id in range(n_neurons):
            # Get incoming weights for this neuron
            incoming = weights[:, neuron_id].copy()
            
            # Only scale non-zero weights (existing connections)
            if np.sum(incoming > 0) > 0:
                incoming_scaled = engine.apply_homeostatic_scaling(incoming, target_total)
                weights[:, neuron_id] = incoming_scaled
        
        # Apply structural plasticity
        new_connectivity = engine.apply_structural_plasticity(
            connectivity, weights, activity_correlation, usage
        )
        
        # Check that plasticity had meaningful effects
        
        # 1. Connections between correlated neurons should be strengthened
        # Group 1 internal connections
        g1_weights = []
        for i in range(3):
            for j in range(3):
                if i != j and connectivity[i, j]:
                    g1_weights.append(weights[i, j])
        
        # Group 2 internal connections
        g2_weights = []
        for i in range(3, 5):
            for j in range(3, 5):
                if i != j and connectivity[i, j]:
                    g2_weights.append(weights[i, j])
        
        # Connections between uncorrelated neurons
        other_weights = []
        for i in range(5):
            for j in range(5, n_neurons):
                if connectivity[i, j]:
                    other_weights.append(weights[i, j])
        
        # Group internal connections should be stronger than connections to other neurons
        if len(g1_weights) > 0 and len(other_weights) > 0:
            assert np.mean(g1_weights) > np.mean(other_weights), \
                "Connections within correlated Group 1 should be stronger than other connections"
        
        if len(g2_weights) > 0 and len(other_weights) > 0:
            assert np.mean(g2_weights) > np.mean(other_weights), \
                "Connections within correlated Group 2 should be stronger than other connections"
        
        # 2. New connections should form between correlated neurons
        for i in range(3):
            for j in range(3):
                if i != j and not connectivity[i, j]:
                    assert new_connectivity[i, j], \
                        f"New connection should form between correlated neurons ({i}, {j}) in Group 1"
        
        for i in range(3, 5):
            for j in range(3, 5):
                if i != j and not connectivity[i, j]:
                    assert new_connectivity[i, j], \
                        f"New connection should form between correlated neurons ({i}, {j}) in Group 2"
        
        # 3. Low-usage connections should be pruned
        low_usage_indices = np.where((connectivity) & (usage < 0.1))
        for i, j in zip(*low_usage_indices):
            assert not new_connectivity[i, j], \
                f"Low-usage connection ({i}, {j}) should be pruned"