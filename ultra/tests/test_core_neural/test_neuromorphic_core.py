#!/usr/bin/env python3
"""
ULTRA: Ultimate Learning & Thought Reasoning Architecture
Core Neural Architecture - Neuromorphic Core Tests

This module contains tests for the Neuromorphic Core component, which implements
a biologically-inspired neural network with 3D spatial organization, heterogeneous
neuron types, and connectivity patterns based on neuroscience principles.
"""

import os
import sys
import numpy as np
import pytest
import scipy.sparse
from scipy.stats import pearsonr
from numpy.testing import assert_array_almost_equal, assert_array_equal
import matplotlib.pyplot as plt
import logging

# Ensure the test module is in the path
ROOT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if ROOT_DIR not in sys.path:
    sys.path.insert(0, ROOT_DIR)

# Import test configuration and utilities
from tests.test_core_neural import (
    neuromorphic_core, network_state_recorder, input_pattern_generator,
    DEFAULT_NEURON_PARAMS, TestUtils, validate_lif_dynamics, analytical_lif
)

# Import modules to test
from ultra.core_neural.neuromorphic_core import NeuromorphicCore

# Configure logging
logger = logging.getLogger(__name__)

class TestNeuromorphicCore:
    """Test suite for the Neuromorphic Core component"""
    
    def test_initialization(self, neuromorphic_core):
        """Test that the core initializes with the correct dimensions and properties"""
        # The core should be initialized with the dimensions specified in the fixture
        assert neuromorphic_core.dimensions == (10, 10, 10)
        assert neuromorphic_core.total_neurons == 1000  # 10x10x10
        assert neuromorphic_core.neuron_types == 4
        
        # Check that positions were initialized correctly
        assert neuromorphic_core.positions.shape == (1000, 3)
        assert np.all(neuromorphic_core.positions >= 0)
        assert np.all(neuromorphic_core.positions <= 1)
        
        # Check that types were initialized with the correct distribution
        type_counts = np.bincount(neuromorphic_core.types)
        # 80% excitatory, 10% inhibitory, 5% adaptive, 5% neuromodulatory
        assert abs(type_counts[0] / 1000 - 0.8) < 0.05  # Allow small statistical variation
        assert abs(type_counts[1] / 1000 - 0.1) < 0.05
        assert abs(type_counts[2] / 1000 - 0.05) < 0.05
        assert abs(type_counts[3] / 1000 - 0.05) < 0.05
        
        # Check that connectivity was initialized
        assert isinstance(neuromorphic_core.connections, scipy.sparse.csr_matrix)
        assert neuromorphic_core.connections.shape == (1000, 1000)
        
        # Check that state vectors were initialized
        assert len(neuromorphic_core.membrane_potentials) == 1000
        assert len(neuromorphic_core.refractory_periods) == 1000
        assert len(neuromorphic_core.adaptation_variables) == 1000
    
    def test_distance_based_connectivity(self):
        """Test that connectivity follows distance-dependent probability"""
        # Create a smaller core for easier testing
        core = NeuromorphicCore(dimensions=(5, 5, 5), neuron_types=4)
        
        # Extract connectivity and positions
        connections = core.connections.toarray()
        positions = core.positions
        
        # Calculate pairwise distances
        n_neurons = len(positions)
        distances = np.zeros((n_neurons, n_neurons))
        for i in range(n_neurons):
            for j in range(n_neurons):
                distances[i, j] = np.sqrt(np.sum((positions[i] - positions[j])**2))
        
        # Check that connection probability decreases with distance
        # Group connections by distance bins
        dist_bins = np.linspace(0, np.sqrt(3), 10)  # Max distance in a unit cube is sqrt(3)
        bin_probs = []
        
        for i in range(len(dist_bins) - 1):
            dist_mask = (distances > dist_bins[i]) & (distances <= dist_bins[i+1])
            # Exclude self-connections
            np.fill_diagonal(dist_mask, False)
            
            # Calculate connection probability in this distance bin
            n_possible = np.sum(dist_mask)
            if n_possible > 0:
                n_connected = np.sum(connections[dist_mask] > 0)
                bin_probs.append(n_connected / n_possible)
            else:
                bin_probs.append(0)
        
        # Connection probability should decrease with distance
        # Check correlation is negative
        bin_centers = (dist_bins[:-1] + dist_bins[1:]) / 2
        correlation, _ = pearsonr(bin_centers, bin_probs)
        assert correlation < 0, "Connection probability should decrease with distance"
    
    def test_type_specific_connectivity(self):
        """Test that connectivity follows type-specific connection patterns"""
        # Create a core with controlled type distribution
        core = NeuromorphicCore(dimensions=(5, 5, 5), neuron_types=4)
        
        # Manually set types to have a clear distribution
        types = np.zeros(125, dtype=np.int32)
        types[0:80] = 0    # 80 excitatory
        types[80:100] = 1  # 20 inhibitory
        types[100:112] = 2 # 12 adaptive
        types[112:125] = 3 # 13 neuromodulatory
        core.types = types
        
        # Recreate connectivity with these fixed types
        core._initialize_connectivity()
        
        # Extract connectivity
        connections = core.connections.toarray()
        
        # Check type-specific connectivity patterns
        type_pairs = []
        for from_type in range(4):
            for to_type in range(4):
                from_mask = core.types == from_type
                to_mask = core.types == to_type
                n_from = np.sum(from_mask)
                n_to = np.sum(to_mask)
                
                if n_from > 0 and n_to > 0:
                    # Calculate connection probability from type A to type B
                    submatrix = connections[np.ix_(from_mask, to_mask)]
                    conn_prob = np.sum(submatrix > 0) / (n_from * n_to)
                    type_pairs.append((from_type, to_type, conn_prob))
        
        # Define expected connectivity patterns based on the S matrix in the documentation
        # Excitatory neurons (type 0) should have stronger connections to other neurons
        # Inhibitory neurons (type 1) should have strong connections to excitatory neurons
        excitatory_out = [p for from_type, _, p in type_pairs if from_type == 0]
        inhibitory_out = [p for from_type, _, p in type_pairs if from_type == 1]
        
        # Excitatory to excitatory should be stronger than inhibitory to inhibitory
        e_to_e = [p for from_type, to_type, p in type_pairs if from_type == 0 and to_type == 0][0]
        i_to_i = [p for from_type, to_type, p in type_pairs if from_type == 1 and to_type == 1][0]
        
        assert e_to_e > i_to_i, "Excitatory to excitatory connectivity should be stronger"
        
        # Inhibitory to excitatory should be stronger than excitatory to inhibitory (feedback inhibition)
        i_to_e = [p for from_type, to_type, p in type_pairs if from_type == 1 and to_type == 0][0]
        e_to_i = [p for from_type, to_type, p in type_pairs if from_type == 0 and to_type == 1][0]
        
        assert i_to_e > e_to_i, "Inhibitory to excitatory connectivity should be stronger"
    
    def test_neuron_dynamics_lif(self):
        """Test Leaky Integrate-and-Fire neuron dynamics"""
        # Create a smaller core for testing
        core = NeuromorphicCore(dimensions=(2, 2, 2), neuron_types=1)
        
        # Set all neurons to be LIF neurons with known parameters
        params = DEFAULT_NEURON_PARAMS["lif"]
        
        # Reset all neurons to the same state
        core.membrane_potentials = np.ones(core.total_neurons) * params["v_rest"]
        core.refractory_periods = np.zeros(core.total_neurons)
        
        # Simulate with constant current to a single neuron
        neuron_id = 0
        simulation_time = 100  # ms
        dt = 0.1  # ms
        I = 1.2  # Input current
        
        # Create recorder to track membrane potential
        recorder = network_state_recorder()
        
        # Calculate expected membrane potential using analytical solution
        expected_v = analytical_lif(
            params["v_rest"], params["v_reset"], params["v_threshold"], 
            params["tau"], 1.0, I, dt, int(simulation_time / dt), 
            params["refractory_period"]
        )
        
        # Run simulation
        for t in np.arange(0, simulation_time, dt):
            # Create input current (one neuron receives current, others don't)
            input_current = np.zeros(core.total_neurons)
            input_current[neuron_id] = I
            
            # Update network
            core.update(input_current, dt)
            
            # Record state
            recorder.record_state(core, t)
        
        # Extract membrane potential for the selected neuron
        v_trace = np.array([v[neuron_id] for v in recorder.membrane_potentials])
        
        # Verify dynamics match the analytical solution within a tolerance
        # Due to numerical integration, there might be small differences
        # Focus on sections between spikes
        # Find spike times
        spike_indices = np.where(np.diff(v_trace) < -50 * dt)[0]  # Large negative jumps indicate reset after spike
        
        if len(spike_indices) > 0:
            # Check membrane potential before first spike
            first_spike = spike_indices[0]
            assert np.allclose(v_trace[:first_spike], expected_v[:first_spike], atol=1e-4)
            
            # Check interspike intervals
            # The numerical integration and analytical solution may drift over time,
            # but the interspike intervals should be similar
            if len(spike_indices) >= 2:
                isi_actual = np.diff(spike_indices) * dt
                
                # Calculate expected interspike intervals
                spike_indices_expected = np.where(np.diff(expected_v) < -50 * dt)[0]
                if len(spike_indices_expected) >= 2:
                    isi_expected = np.diff(spike_indices_expected) * dt
                    
                    # Compare the first few intervals
                    n_intervals = min(len(isi_actual), len(isi_expected))
                    assert np.allclose(isi_actual[:n_intervals], isi_expected[:n_intervals], rtol=0.1)
        
        # Also verify that a neuron can't spike during its refractory period
        # Reset the core
        core.membrane_potentials = np.ones(core.total_neurons) * params["v_rest"]
        core.refractory_periods = np.zeros(core.total_neurons)
        
        # Set neuron to be in refractory period
        core.refractory_periods[neuron_id] = params["refractory_period"]
        
        # Apply very strong input that would normally cause immediate spiking
        input_current = np.zeros(core.total_neurons)
        input_current[neuron_id] = 100.0  # Very strong input
        
        # Update for one time step
        core.update(input_current, dt)
        
        # Verify that neuron didn't spike (membrane potential should still be at reset)
        assert core.membrane_potentials[neuron_id] == params["v_reset"]
        
        # Verify refractory period decreased
        assert core.refractory_periods[neuron_id] == params["refractory_period"] - dt
    
    def test_network_activity_patterns(self, neuromorphic_core, input_pattern_generator):
        """Test network-level activity patterns in response to different inputs"""
        core = neuromorphic_core
        
        # Reset network state
        core.membrane_potentials = np.zeros(core.total_neurons)
        core.refractory_periods = np.zeros(core.total_neurons)
        core.adaptation_variables = np.zeros(core.total_neurons)
        
        # Test 1: Constant input to a subset of neurons
        # Select 50 excitatory neurons
        excitatory_indices = np.where(core.types == 0)[0][:50]
        
        # Simulation parameters
        simulation_time = 500  # ms
        dt = 1.0  # ms
        I = 1.5  # Input current
        
        # Create recorder
        recorder = network_state_recorder()
        
        # Run simulation
        for t in np.arange(0, simulation_time, dt):
            # Create input current 
            input_current = np.zeros(core.total_neurons)
            input_current[excitatory_indices] = I
            
            # Update network
            core.update(input_current, dt)
            
            # Record state
            recorder.record_state(core, t)
        
        # Analyze activity patterns
        metrics = recorder.analyze()
        
        # Calculate mean firing rates for excitatory and inhibitory neurons
        exc_firing_rates = [metrics['firing_rates'].get(i, 0) for i in range(core.total_neurons) if core.types[i] == 0]
        inh_firing_rates = [metrics['firing_rates'].get(i, 0) for i in range(core.total_neurons) if core.types[i] == 1]
        
        # Only some excitatory neurons received input, so mean firing rate should be moderate
        assert 0 < np.mean(exc_firing_rates) < 50, "Excitatory neurons should have moderate firing rate"
        
        # Inhibitory neurons should have been activated by excitatory neurons
        assert 0 < np.mean(inh_firing_rates), "Inhibitory neurons should be active due to excitatory input"
        
        # Test 2: Oscillatory input to a different subset of neurons
        # Reset network state
        core.membrane_potentials = np.zeros(core.total_neurons)
        core.refractory_periods = np.zeros(core.total_neurons)
        core.adaptation_variables = np.zeros(core.total_neurons)
        
        # Select 50 different excitatory neurons
        excitatory_indices = np.where(core.types == 0)[0][50:100]
        
        # Create oscillatory input
        steps = int(simulation_time / dt)
        input_pattern = input_pattern_generator.sinusoidal(
            simulation_time, frequency=0.01, amplitude=1.0, offset=0.5, dt=dt
        )
        
        # Create recorder
        recorder = network_state_recorder()
        
        # Run simulation
        for step, t in enumerate(np.arange(0, simulation_time, dt)):
            # Create input current 
            input_current = np.zeros(core.total_neurons)
            input_current[excitatory_indices] = input_pattern[step]
            
            # Update network
            core.update(input_current, dt)
            
            # Record state
            recorder.record_state(core, t)
        
        # Analyze activity patterns
        metrics = recorder.analyze()
        
        # Extract spike times for the stimulated neurons
        stim_spike_times = {}
        for neuron_id in excitatory_indices:
            if neuron_id in recorder.spike_times:
                stim_spike_times[neuron_id] = recorder.spike_times[neuron_id]
        
        # Calculate mean firing rate over time bins to detect oscillatory pattern
        bin_size = 50  # ms
        n_bins = int(simulation_time / bin_size)
        binned_spikes = np.zeros(n_bins)
        
        for neuron_id, times in stim_spike_times.items():
            for t in times:
                bin_idx = min(int(t / bin_size), n_bins - 1)
                binned_spikes[bin_idx] += 1
        
        # Normalize by number of neurons and bin size to get firing rate in Hz
        binned_rates = binned_spikes / (len(excitatory_indices) * (bin_size / 1000))
        
        # Check if firing rate oscillates with input
        # We expect higher firing rates during the peaks of the input
        # Input peaks occur at t = 25, 125, 225, 325, 425 ms, corresponds to bins 0, 2, 4, 6, 8
        peak_bins = [0, 2, 4, 6, 8]
        trough_bins = [1, 3, 5, 7, 9]
        
        # Calculate mean rate at peaks and troughs
        peak_rate = np.mean([binned_rates[i] for i in peak_bins if i < len(binned_rates)])
        trough_rate = np.mean([binned_rates[i] for i in trough_bins if i < len(binned_rates)])
        
        # Peaks should have higher rate than troughs
        assert peak_rate > trough_rate, "Firing rate should oscillate with input pattern"
        
        # Test 3: Verify that the network exhibits realistic excitation/inhibition balance
        # Reset network state
        core.membrane_potentials = np.zeros(core.total_neurons)
        core.refractory_periods = np.zeros(core.total_neurons)
        core.adaptation_variables = np.zeros(core.total_neurons)
        
        # Apply constant input to all excitatory neurons
        excitatory_indices = np.where(core.types == 0)[0]
        
        # Create recorder
        recorder = network_state_recorder()
        
        # Run simulation
        for t in np.arange(0, simulation_time, dt):
            # Create input current 
            input_current = np.zeros(core.total_neurons)
            input_current[excitatory_indices] = 1.0
            
            # Update network
            core.update(input_current, dt)
            
            # Record state
            recorder.record_state(core, t)
        
        # Analyze activity patterns
        metrics = recorder.analyze()
        
        # Calculate mean firing rates for excitatory and inhibitory neurons
        exc_firing_rates = [metrics['firing_rates'].get(i, 0) for i in range(core.total_neurons) if core.types[i] == 0]
        inh_firing_rates = [metrics['firing_rates'].get(i, 0) for i in range(core.total_neurons) if core.types[i] == 1]
        
        # Both excitatory and inhibitory neurons should be active
        assert np.mean(exc_firing_rates) > 0, "Excitatory neurons should be active"
        assert np.mean(inh_firing_rates) > 0, "Inhibitory neurons should be active"
        
        # The ratio of excitatory to inhibitory firing rates should be within a biological range
        # Typically, inhibitory neurons fire faster than excitatory neurons
        e_i_ratio = np.mean(exc_firing_rates) / (np.mean(inh_firing_rates) + 1e-10)  # Avoid division by zero
        
        # In biological networks, inhibitory neurons often fire 2-5 times faster than excitatory
        assert 0.1 < e_i_ratio < 1.0, "E/I ratio should be in a biologically plausible range"
    
    def test_adaptive_neurons(self, neuromorphic_core):
        """Test that adaptive neurons show spike frequency adaptation"""
        core = neuromorphic_core
        
        # Reset network state
        core.membrane_potentials = np.zeros(core.total_neurons)
        core.refractory_periods = np.zeros(core.total_neurons)
        core.adaptation_variables = np.zeros(core.total_neurons)
        
        # Select an adaptive neuron (type 2)
        adaptive_indices = np.where(core.types == 2)[0]
        if len(adaptive_indices) == 0:
            pytest.skip("No adaptive neurons in the network")
        
        adaptive_id = adaptive_indices[0]
        
        # Simulation parameters
        simulation_time = 1000  # ms
        dt = 1.0  # ms
        I = 1.5  # Input current
        
        # Create recorder
        recorder = network_state_recorder()
        
        # Run simulation
        for t in np.arange(0, simulation_time, dt):
            # Create input current 
            input_current = np.zeros(core.total_neurons)
            input_current[adaptive_id] = I
            
            # Update network
            core.update(input_current, dt)
            
            # Record state
            recorder.record_state(core, t)
        
        # Check if the adaptive neuron shows spike frequency adaptation
        if adaptive_id in recorder.spike_times:
            spike_times = recorder.spike_times[adaptive_id]
            
            if len(spike_times) >= 5:  # Need enough spikes to measure adaptation
                # Calculate interspike intervals
                isis = np.diff(spike_times)
                
                # Calculate slope of ISIs over time
                # A positive slope indicates increasing ISIs, i.e., adaptation
                from sklearn.linear_model import LinearRegression
                X = np.arange(len(isis)).reshape(-1, 1)
                model = LinearRegression().fit(X, isis)
                
                # If the slope is positive and significant, there is adaptation
                assert model.coef_[0] > 0, "Adaptive neurons should show increasing ISIs (spike frequency adaptation)"
                
                # Calculate correlation between ISI and time
                correlation, p_value = pearsonr(np.arange(len(isis)), isis)
                
                # A positive correlation indicates adaptation
                assert correlation > 0, "Adaptive neurons should show positive correlation between ISI and time"
        else:
            # If no spikes were recorded, the test is inconclusive
            pytest.skip("Adaptive neuron did not spike enough to test adaptation")
    
    def test_spatial_connectivity_influence(self):
        """Test that spatial organization influences network dynamics"""
        # Create two networks with different spatial configurations
        # Network 1: Random positions
        np.random.seed(42)  # For reproducibility
        core1 = NeuromorphicCore(dimensions=(5, 5, 5), neuron_types=4)
        
        # Network 2: Clustered positions (two clusters)
        core2 = NeuromorphicCore(dimensions=(5, 5, 5), neuron_types=4)
        n_neurons = core2.total_neurons
        
        # Override positions with clustered configuration
        positions = np.zeros((n_neurons, 3))
        cluster_size = n_neurons // 2
        
        # Cluster 1: Bottom left corner
        positions[:cluster_size, 0] = np.random.uniform(0, 0.3, cluster_size)
        positions[:cluster_size, 1] = np.random.uniform(0, 0.3, cluster_size)
        positions[:cluster_size, 2] = np.random.uniform(0, 0.3, cluster_size)
        
        # Cluster 2: Top right corner
        positions[cluster_size:, 0] = np.random.uniform(0.7, 1.0, n_neurons - cluster_size)
        positions[cluster_size:, 1] = np.random.uniform(0.7, 1.0, n_neurons - cluster_size)
        positions[cluster_size:, 2] = np.random.uniform(0.7, 1.0, n_neurons - cluster_size)
        
        core2.positions = positions
        
        # Reinitialize connectivity based on new positions
        core2._initialize_connectivity()
        
        # Simulation parameters
        simulation_time = 500  # ms
        dt = 1.0  # ms
        
        # Test with stimulus to a subset of neurons
        # Select neurons from the first half (first cluster in core2)
        stim_indices = np.arange(n_neurons // 4)
        I = 1.5  # Input current
        
        # Create recorders
        recorder1 = network_state_recorder()
        recorder2 = network_state_recorder()
        
        # Run simulations
        for t in np.arange(0, simulation_time, dt):
            # Create input current 
            input_current = np.zeros(n_neurons)
            input_current[stim_indices] = I
            
            # Update networks
            core1.update(input_current, dt)
            core2.update(input_current, dt)
            
            # Record states
            recorder1.record_state(core1, t)
            recorder2.record_state(core2, t)
        
        # Analyze activity patterns
        metrics1 = recorder1.analyze()
        metrics2 = recorder2.analyze()
        
        # Calculate firing rates for the different halves of the networks
        first_half = np.arange(n_neurons // 2)
        second_half = np.arange(n_neurons // 2, n_neurons)
        
        # Network 1 (random positions)
        fr1_first = np.mean([metrics1['firing_rates'].get(i, 0) for i in first_half])
        fr1_second = np.mean([metrics1['firing_rates'].get(i, 0) for i in second_half])
        
        # Network 2 (clustered positions)
        fr2_first = np.mean([metrics2['firing_rates'].get(i, 0) for i in first_half])
        fr2_second = np.mean([metrics2['firing_rates'].get(i, 0) for i in second_half])
        
        # In the clustered network, activity should be higher in the first cluster
        # and lower in the second cluster compared to the random network
        # (because connectivity is stronger within clusters)
        
        # Calculate cluster activity ratios
        ratio1 = fr1_first / (fr1_second + 1e-10)  # Avoid division by zero
        ratio2 = fr2_first / (fr2_second + 1e-10)
        
        # Clustered network should have higher activity contrast between clusters
        assert ratio2 > ratio1, "Clustered spatial organization should lead to stronger within-cluster activity"
    
    def test_save_load_state(self, neuromorphic_core, tmp_path):
        """Test that the network state can be saved and loaded correctly"""
        core = neuromorphic_core
        
        # Set network to a known state
        core.membrane_potentials = np.random.rand(core.total_neurons)
        core.refractory_periods = np.random.randint(0, 5, core.total_neurons)
        core.adaptation_variables = np.random.rand(core.total_neurons)
        
        # Modify weights
        weights = core.connections.toarray()
        weights = weights * 0.5 + 0.5  # Rescale to [0.5, 1.0]
        core.connections = scipy.sparse.csr_matrix(weights)
        
        # Save state to a file
        state_file = tmp_path / "core_state.npz"
        core.save_state(state_file)
        
        # Create a new core with different initial state
        new_core = NeuromorphicCore(dimensions=core.dimensions, neuron_types=core.neuron_types)
        
        # Load the saved state
        new_core.load_state(state_file)
        
        # Check that all state variables match
        assert np.allclose(core.membrane_potentials, new_core.membrane_potentials)
        assert np.array_equal(core.refractory_periods, new_core.refractory_periods)
        assert np.allclose(core.adaptation_variables, new_core.adaptation_variables)
        assert np.array_equal(core.types, new_core.types)
        assert np.allclose(core.positions, new_core.positions)
        
        # Check that connectivity matches
        assert (core.connections != new_core.connections).nnz == 0
        assert np.allclose(core.connections.toarray(), new_core.connections.toarray())