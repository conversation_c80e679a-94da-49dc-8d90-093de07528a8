#!/usr/bin/env python3
"""
ULTRA: Ultimate Learning & Thought Reasoning Architecture
Core Neural Architecture - Visualization Utilities

This module provides visualization tools for analyzing and understanding
the behavior of Core Neural Architecture components, including network structure,
neural activity, plasticity dynamics, and oscillatory patterns.
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.colors import LinearSegmentedColormap
import matplotlib.animation as animation
from mpl_toolkits.mplot3d import Axes3D
import networkx as nx
import scipy.signal
from scipy.sparse import csr_matrix
import logging
from typing import Dict, List, Tuple, Union, Optional, Any

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class NetworkVisualizer:
    """
    Visualization tools for neural networks and their dynamics.
    """
    
    @staticmethod
    def plot_3d_network(
        positions: np.ndarray,
        connections: np.ndarray,
        neuron_types: Optional[np.ndarray] = None,
        weights: Optional[np.ndarray] = None,
        highlight_neurons: Optional[List[int]] = None,
        title: Optional[str] = None,
        save_path: Optional[str] = None,
        figsize: Tuple[int, int] = (10, 8),
        show: bool = True
    ) -> plt.Figure:
        """
        Plot a 3D visualization of the neural network structure.
        
        Args:
            positions: Nx3 array of neuron positions
            connections: NxN array of connection weights or binary connectivity
            neuron_types: Optional array of neuron types for coloring
            weights: Optional array of connection weights for edge coloring
            highlight_neurons: Optional list of neuron indices to highlight
            title: Optional title for the plot
            save_path: Optional path to save the figure
            figsize: Figure size as (width, height) in inches
            show: Whether to display the figure
            
        Returns:
            The matplotlib Figure object
        """
        if connections is not None and isinstance(connections, csr_matrix):
            connections = connections.toarray()
            
        fig = plt.figure(figsize=figsize)
        ax = fig.add_subplot(111, projection='3d')
        
        # Set up colors for different neuron types
        if neuron_types is not None:
            type_colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd']
            unique_types = np.unique(neuron_types)
            type_to_color = {t: type_colors[i % len(type_colors)] for i, t in enumerate(unique_types)}
            node_colors = [type_to_color[t] for t in neuron_types]
        else:
            node_colors = '#1f77b4'  # Default blue
        
        # Plot neurons
        ax.scatter(
            positions[:, 0], positions[:, 1], positions[:, 2],
            c=node_colors, s=50, alpha=0.8, edgecolors='w'
        )
        
        # Highlight specific neurons if requested
        if highlight_neurons is not None:
            ax.scatter(
                positions[highlight_neurons, 0],
                positions[highlight_neurons, 1],
                positions[highlight_neurons, 2],
                c='red', s=100, alpha=1.0, edgecolors='k'
            )
        
        # Plot connections if provided
        if connections is not None:
            # Find connected pairs
            i_indices, j_indices = np.where(connections > 0)
            
            # Set up edge colors based on weights if provided
            if weights is not None:
                # Normalize weights to [0, 1] for coloring
                edge_weights = weights[i_indices, j_indices]
                norm_weights = (edge_weights - np.min(edge_weights)) / (np.max(edge_weights) - np.min(edge_weights) + 1e-10)
                
                # Create a colormap for edge weights
                cmap = plt.cm.viridis
                edge_colors = [cmap(w) for w in norm_weights]
            else:
                edge_colors = 'gray'
            
            # Plot a subset of edges if there are too many
            max_edges = 1000  # Maximum edges to plot for readability
            if len(i_indices) > max_edges:
                indices = np.random.choice(len(i_indices), max_edges, replace=False)
                i_indices = i_indices[indices]
                j_indices = j_indices[indices]
                if isinstance(edge_colors, list):
                    edge_colors = [edge_colors[i] for i in indices]
            
            # Plot edges
            for idx, (i, j) in enumerate(zip(i_indices, j_indices)):
                ax.plot(
                    [positions[i, 0], positions[j, 0]],
                    [positions[i, 1], positions[j, 1]],
                    [positions[i, 2], positions[j, 2]],
                    color=edge_colors[idx] if isinstance(edge_colors, list) else edge_colors,
                    linewidth=0.5, alpha=0.3
                )
        
        # Set plot labels and title
        ax.set_xlabel('X')
        ax.set_ylabel('Y')
        ax.set_zlabel('Z')
        if title:
            ax.set_title(title)
        
        # Set equal aspect ratio
        ax.set_box_aspect([1, 1, 1])
        
        # Add legend for neuron types if provided
        if neuron_types is not None:
            from matplotlib.lines import Line2D
            legend_elements = [
                Line2D([0], [0], marker='o', color='w', markerfacecolor=type_to_color[t],
                      markeredgecolor='w', markersize=10, label=f'Type {t}')
                for t in unique_types
            ]
            ax.legend(handles=legend_elements, loc='upper right')
        
        # Save figure if requested
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"3D network visualization saved to {save_path}")
        
        # Show figure if requested
        if show:
            plt.show()
        
        return fig
    
    @staticmethod
    def plot_connectivity_matrix(
        connections: np.ndarray,
        neuron_types: Optional[np.ndarray] = None,
        title: Optional[str] = None,
        save_path: Optional[str] = None,
        figsize: Tuple[int, int] = (10, 8),
        show: bool = True,
        colorbar_label: str = "Weight"
    ) -> plt.Figure:
        """
        Plot the connectivity matrix of the neural network.
        
        Args:
            connections: NxN array of connection weights
            neuron_types: Optional array of neuron types for sorting
            title: Optional title for the plot
            save_path: Optional path to save the figure
            figsize: Figure size as (width, height) in inches
            show: Whether to display the figure
            colorbar_label: Label for the colorbar
            
        Returns:
            The matplotlib Figure object
        """
        if isinstance(connections, csr_matrix):
            connections = connections.toarray()
            
        fig, ax = plt.subplots(figsize=figsize)
        
        # Sort neurons by type if provided
        if neuron_types is not None:
            # Get sorting indices
            sort_idx = np.argsort(neuron_types)
            
            # Reorder connections matrix
            connections_sorted = connections[sort_idx, :][:, sort_idx]
            
            # Get boundaries between types for visualization
            unique_types = np.unique(neuron_types)
            boundaries = []
            for t in unique_types:
                boundary = np.sum(neuron_types[sort_idx] < t)
                if boundary > 0 and boundary < len(neuron_types):
                    boundaries.append(boundary)
        else:
            connections_sorted = connections
            boundaries = []
        
        # Plot connectivity matrix
        im = ax.imshow(connections_sorted, cmap='viridis', aspect='equal', interpolation='none')
        
        # Add colorbar
        cbar = fig.colorbar(im, ax=ax)
        cbar.set_label(colorbar_label)
        
        # Add type boundaries if available
        for b in boundaries:
            ax.axhline(y=b - 0.5, color='r', linestyle='-', linewidth=1)
            ax.axvline(x=b - 0.5, color='r', linestyle='-', linewidth=1)
        
        # Set labels and title
        ax.set_xlabel('Postsynaptic Neuron')
        ax.set_ylabel('Presynaptic Neuron')
        if title:
            ax.set_title(title)
        
        # Save figure if requested
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"Connectivity matrix visualization saved to {save_path}")
        
        # Show figure if requested
        if show:
            plt.show()
        
        return fig
    
    @staticmethod
    def plot_weight_distribution(
        weights: np.ndarray,
        neuron_types: Optional[np.ndarray] = None,
        title: Optional[str] = None,
        save_path: Optional[str] = None,
        figsize: Tuple[int, int] = (10, 6),
        show: bool = True,
        bins: int = 50
    ) -> plt.Figure:
        """
        Plot the distribution of synaptic weights.
        
        Args:
            weights: NxN array of connection weights
            neuron_types: Optional array of neuron types for separate histograms
            title: Optional title for the plot
            save_path: Optional path to save the figure
            figsize: Figure size as (width, height) in inches
            show: Whether to display the figure
            bins: Number of histogram bins
            
        Returns:
            The matplotlib Figure object
        """
        if isinstance(weights, csr_matrix):
            weights = weights.toarray()
            
        fig, ax = plt.subplots(figsize=figsize)
        
        # Exclude zero weights and self-connections
        mask = np.ones_like(weights, dtype=bool)
        np.fill_diagonal(mask, False)
        non_zero_weights = weights[mask & (weights != 0)]
        
        if neuron_types is not None:
            # Separate weights by pre-synaptic neuron type
            unique_types = np.unique(neuron_types)
            type_weights = []
            type_labels = []
            
            for t in unique_types:
                # Get weights where pre-synaptic neuron is of this type
                type_mask = np.zeros_like(weights, dtype=bool)
                type_indices = np.where(neuron_types == t)[0]
                for i in type_indices:
                    type_mask[i, :] = True
                
                # Exclude self-connections
                np.fill_diagonal(type_mask, False)
                
                # Extract weights
                w = weights[type_mask & (weights != 0)]
                if len(w) > 0:
                    type_weights.append(w)
                    type_labels.append(f'Type {t}')
            
            # Plot histogram for each type
            ax.hist(type_weights, bins=bins, label=type_labels, alpha=0.7)
            ax.legend()
        else:
            # Plot single histogram
            ax.hist(non_zero_weights, bins=bins, alpha=0.7)
        
        # Set labels and title
        ax.set_xlabel('Weight')
        ax.set_ylabel('Count')
        if title:
            ax.set_title(title)
        else:
            ax.set_title('Distribution of Synaptic Weights')
        
        # Save figure if requested
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"Weight distribution visualization saved to {save_path}")
        
        # Show figure if requested
        if show:
            plt.show()
        
        return fig
        
    @staticmethod
    def plot_neuron_activity(
        membrane_potentials: List[np.ndarray],
        spike_times: Dict[int, List[float]],
        time_points: List[float],
        neuron_indices: Optional[List[int]] = None,
        title: Optional[str] = None,
        save_path: Optional[str] = None,
        figsize: Tuple[int, int] = (12, 8),
        show: bool = True
    ) -> plt.Figure:
        """
        Plot the membrane potentials and spike times of selected neurons.
        
        Args:
            membrane_potentials: List of arrays with membrane potentials over time
            spike_times: Dictionary mapping neuron indices to lists of spike times
            time_points: List of time points corresponding to membrane_potentials
            neuron_indices: Optional list of neuron indices to plot (default: first 5)
            title: Optional title for the plot
            save_path: Optional path to save the figure
            figsize: Figure size as (width, height) in inches
            show: Whether to display the figure
            
        Returns:
            The matplotlib Figure object
        """
        if neuron_indices is None:
            # Default to first 5 neurons
            n_neurons = len(membrane_potentials[0])
            neuron_indices = list(range(min(5, n_neurons)))
        
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=figsize, sharex=True, gridspec_kw={'height_ratios': [3, 1]})
        
        # Plot membrane potentials
        for i, neuron_id in enumerate(neuron_indices):
            # Extract membrane potential for this neuron
            v_trace = [v_t[neuron_id] for v_t in membrane_potentials]
            ax1.plot(time_points, v_trace, label=f'Neuron {neuron_id}')
        
        # Set labels and legend for membrane potentials
        ax1.set_ylabel('Membrane Potential')
        ax1.legend()
        ax1.grid(True, linestyle='--', alpha=0.7)
        
        # Plot spike raster
        for i, neuron_id in enumerate(neuron_indices):
            if neuron_id in spike_times:
                ax2.scatter(
                    spike_times[neuron_id],
                    [i] * len(spike_times[neuron_id]),
                    marker='|', s=100, linewidth=1.5
                )
        
        # Set labels for spike raster
        ax2.set_xlabel('Time (ms)')
        ax2.set_ylabel('Neuron')
        ax2.set_yticks(range(len(neuron_indices)))
        ax2.set_yticklabels([f'{i}' for i in neuron_indices])
        ax2.grid(True, linestyle='--', alpha=0.7)
        
        # Set overall title
        if title:
            fig.suptitle(title)
        else:
            fig.suptitle('Neuron Activity: Membrane Potentials and Spikes')
        
        plt.tight_layout()
        
        # Save figure if requested
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"Neuron activity visualization saved to {save_path}")
        
        # Show figure if requested
        if show:
            plt.show()
        
        return fig
    
    @staticmethod
    def plot_network_activity_heatmap(
        membrane_potentials: List[np.ndarray],
        time_points: List[float],
        neuron_types: Optional[np.ndarray] = None,
        title: Optional[str] = None,
        save_path: Optional[str] = None,
        figsize: Tuple[int, int] = (12, 8),
        show: bool = True,
        max_neurons: int = 100
    ) -> plt.Figure:
        """
        Plot a heatmap of network activity over time.
        
        Args:
            membrane_potentials: List of arrays with membrane potentials over time
            time_points: List of time points corresponding to membrane_potentials
            neuron_types: Optional array of neuron types for sorting
            title: Optional title for the plot
            save_path: Optional path to save the figure
            figsize: Figure size as (width, height) in inches
            show: Whether to display the figure
            max_neurons: Maximum number of neurons to plot
            
        Returns:
            The matplotlib Figure object
        """
        # Get dimensions
        n_timesteps = len(time_points)
        n_neurons = len(membrane_potentials[0])
        
        # Limit the number of neurons for readability
        if n_neurons > max_neurons:
            indices = np.linspace(0, n_neurons-1, max_neurons, dtype=int)
        else:
            indices = np.arange(n_neurons)
        
        # Sort by neuron type if provided
        if neuron_types is not None:
            sorted_indices = indices[np.argsort(neuron_types[indices])]
        else:
            sorted_indices = indices
        
        # Create activity matrix
        activity = np.zeros((len(sorted_indices), n_timesteps))
        for t in range(n_timesteps):
            activity[:, t] = membrane_potentials[t][sorted_indices]
        
        # Create figure
        fig, ax = plt.subplots(figsize=figsize)
        
        # Plot heatmap
        im = ax.imshow(
            activity, 
            aspect='auto', 
            interpolation='none',
            extent=[min(time_points), max(time_points), len(sorted_indices)-0.5, -0.5],
            cmap='viridis'
        )
        
        # Add colorbar
        cbar = fig.colorbar(im, ax=ax)
        cbar.set_label('Membrane Potential')
        
        # Add type boundaries if available
        if neuron_types is not None:
            types = neuron_types[sorted_indices]
            unique_types = np.unique(types)
            boundaries = []
            for t in unique_types:
                boundary = np.sum(types < t)
                if boundary > 0 and boundary < len(types):
                    boundaries.append(boundary)
            
            for b in boundaries:
                ax.axhline(y=b - 0.5, color='r', linestyle='-', linewidth=1)
        
        # Set labels and title
        ax.set_xlabel('Time (ms)')
        ax.set_ylabel('Neuron Index')
        if title:
            ax.set_title(title)
        else:
            ax.set_title('Network Activity Over Time')
        
        # Save figure if requested
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"Network activity heatmap saved to {save_path}")
        
        # Show figure if requested
        if show:
            plt.show()
        
        return fig
    
    @staticmethod
    def create_network_animation(
        membrane_potentials: List[np.ndarray],
        positions: np.ndarray,
        time_points: List[float],
        connections: Optional[np.ndarray] = None,
        neuron_types: Optional[np.ndarray] = None,
        title: Optional[str] = None,
        save_path: Optional[str] = None,
        figsize: Tuple[int, int] = (10, 8),
        fps: int = 30,
        show: bool = True,
        subsample: int = 1
    ) -> plt.Figure:
        """
        Create an animation of network activity in 3D space.
        
        Args:
            membrane_potentials: List of arrays with membrane potentials over time
            positions: Nx3 array of neuron positions
            time_points: List of time points corresponding to membrane_potentials
            connections: Optional NxN array of connection weights
            neuron_types: Optional array of neuron types for coloring
            title: Optional title for the animation
            save_path: Optional path to save the animation
            figsize: Figure size as (width, height) in inches
            fps: Frames per second for the animation
            show: Whether to display the animation
            subsample: Subsample factor for time points to reduce animation size
            
        Returns:
            The matplotlib Figure object
        """
        if connections is not None and isinstance(connections, csr_matrix):
            connections = connections.toarray()
            
        # Subsample time points for efficiency
        time_indices = list(range(0, len(time_points), subsample))
        time_points_sub = [time_points[i] for i in time_indices]
        membrane_potentials_sub = [membrane_potentials[i] for i in time_indices]
        
        # Create figure
        fig = plt.figure(figsize=figsize)
        ax = fig.add_subplot(111, projection='3d')
        
        # Set up colors for different neuron types
        if neuron_types is not None:
            type_colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd']
            unique_types = np.unique(neuron_types)
            type_to_color = {t: type_colors[i % len(type_colors)] for i, t in enumerate(unique_types)}
            base_colors = np.array([type_to_color[t] for t in neuron_types])
        else:
            base_colors = np.array(['#1f77b4'] * len(positions))  # Default blue
        
        # Add connections if provided
        if connections is not None:
            # Find connected pairs
            i_indices, j_indices = np.where(connections > 0)
            
            # Plot a subset of edges if there are too many
            max_edges = 500  # Maximum edges to plot for readability
            if len(i_indices) > max_edges:
                indices = np.random.choice(len(i_indices), max_edges, replace=False)
                i_indices = i_indices[indices]
                j_indices = j_indices[indices]
            
            # Plot edges
            for i, j in zip(i_indices, j_indices):
                ax.plot(
                    [positions[i, 0], positions[j, 0]],
                    [positions[i, 1], positions[j, 1]],
                    [positions[i, 2], positions[j, 2]],
                    color='gray', linewidth=0.5, alpha=0.1
                )
        
        # Create a scatter plot for neurons
        scatter = ax.scatter(
            positions[:, 0], positions[:, 1], positions[:, 2],
            c=base_colors, s=50, alpha=0.8, edgecolors='w'
        )
        
        # Set plot labels and title
        ax.set_xlabel('X')
        ax.set_ylabel('Y')
        ax.set_zlabel('Z')
        if title:
            ax.set_title(title)
        else:
            ax.set_title('Network Activity Animation')
        
        # Set equal aspect ratio
        ax.set_box_aspect([1, 1, 1])
        
        # Add time indicator
        time_text = ax.text2D(0.05, 0.95, '', transform=ax.transAxes)
        
        # Find global min/max for consistent color scaling
        vmin = min(np.min(v) for v in membrane_potentials_sub)
        vmax = max(np.max(v) for v in membrane_potentials_sub)
        
        # Update function for animation
        def update(frame):
            # Get membrane potentials for this frame
            v = membrane_potentials_sub[frame]
            
            # Normalize potentials to [0, 1] for coloring
            v_norm = (v - vmin) / (vmax - vmin + 1e-10)
            
            # Create custom colors based on activity
            colors = []
            for i in range(len(positions)):
                # Get base color for this neuron
                base_color = base_colors[i]
                
                # Make it brighter based on activity
                # Convert to RGB values
                if isinstance(base_color, str) and base_color.startswith('#'):
                    r = int(base_color[1:3], 16) / 255
                    g = int(base_color[3:5], 16) / 255
                    b = int(base_color[5:7], 16) / 255
                else:
                    r, g, b = base_color
                
                # Brighten based on activity
                a = v_norm[i]
                r = min(1, r + 0.5 * a)
                g = min(1, g + 0.5 * a)
                b = min(1, b + 0.5 * a)
                
                colors.append((r, g, b))
            
            # Update scatter plot colors
            scatter.set_color(colors)
            
            # Update size based on activity
            scatter.set_sizes(50 + 100 * v_norm)
            
            # Update time indicator
            time_text.set_text(f'Time: {time_points_sub[frame]:.1f} ms')
            
            return scatter, time_text
        
        # Create animation
        anim = animation.FuncAnimation(
            fig, update, frames=len(time_points_sub),
            interval=1000/fps, blit=False
        )
        
        # Save animation if requested
        if save_path:
            writer = animation.FFMpegWriter(fps=fps, bitrate=5000)
            anim.save(save_path, writer=writer)
            logger.info(f"Network animation saved to {save_path}")
        
        # Show animation if requested
        if show:
            plt.show()
        
        return fig

class PlasticityVisualizer:
    """
    Visualization tools for neural plasticity mechanisms.
    """
    
    @staticmethod
    def plot_stdp_window(
        A_plus: float = 0.1,
        A_minus: float = -0.1,
        tau_plus: float = 20.0,
        tau_minus: float = 20.0,
        title: Optional[str] = None,
        save_path: Optional[str] = None,
        figsize: Tuple[int, int] = (8, 6),
        show: bool = True
    ) -> plt.Figure:
        """
        Plot the STDP learning window.
        
        Args:
            A_plus: Amplitude of potentiation
            A_minus: Amplitude of depression
            tau_plus: Time constant for potentiation
            tau_minus: Time constant for depression
            title: Optional title for the plot
            save_path: Optional path to save the figure
            figsize: Figure size as (width, height) in inches
            show: Whether to display the figure
            
        Returns:
            The matplotlib Figure object
        """
        fig, ax = plt.subplots(figsize=figsize)
        
        # Generate time differences
        delta_t = np.linspace(-100, 100, 1000)
        
        # Calculate weight changes
        dw = np.zeros_like(delta_t)
        for i, dt in enumerate(delta_t):
            if dt > 0:
                dw[i] = A_plus * np.exp(-dt / tau_plus)
            else:
                dw[i] = A_minus * np.exp(dt / tau_minus)
        
        # Plot STDP window
        ax.plot(delta_t, dw, 'b-', linewidth=2)
        
        # Add zero line
        ax.axhline(y=0, color='k', linestyle='--', alpha=0.5)
        ax.axvline(x=0, color='k', linestyle='--', alpha=0.5)
        
        # Set labels and title
        ax.set_xlabel('Spike Time Difference (post - pre) [ms]')
        ax.set_ylabel('Weight Change')
        if title:
            ax.set_title(title)
        else:
            ax.set_title('STDP Learning Window')
        
        # Add annotations
        ax.text(30, A_plus*0.6, 'Potentiation\n(pre → post)', ha='center', va='center', bbox=dict(boxstyle='round', facecolor='white', alpha=0.7))
        ax.text(-30, A_minus*0.6, 'Depression\n(post → pre)', ha='center', va='center', bbox=dict(boxstyle='round', facecolor='white', alpha=0.7))
        
        # Add parameters as text
        param_text = f'Parameters:\nA+ = {A_plus}\nA- = {A_minus}\nτ+ = {tau_plus} ms\nτ- = {tau_minus} ms'
        ax.text(0.95, 0.05, param_text, transform=ax.transAxes, fontsize=10,
                verticalalignment='bottom', horizontalalignment='right',
                bbox=dict(boxstyle='round', facecolor='white', alpha=0.7))
        
        # Grid
        ax.grid(True, linestyle='--', alpha=0.7)
        
        # Save figure if requested
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"STDP window visualization saved to {save_path}")
        
        # Show figure if requested
        if show:
            plt.show()
        
        return fig
    
    @staticmethod
    def plot_weight_evolution(
        weight_history: List[np.ndarray],
        connection_indices: Optional[List[Tuple[int, int]]] = None,
        time_points: Optional[List[float]] = None,
        title: Optional[str] = None,
        save_path: Optional[str] = None,
        figsize: Tuple[int, int] = (10, 6),
        show: bool = True
    ) -> plt.Figure:
        """
        Plot the evolution of specific synaptic weights over time.
        
        Args:
            weight_history: List of weight matrices over time
            connection_indices: List of (pre, post) indices to plot (default: random selection)
            time_points: Optional list of time points
            title: Optional title for the plot
            save_path: Optional path to save the figure
            figsize: Figure size as (width, height) in inches
            show: Whether to display the figure
            
        Returns:
            The matplotlib Figure object
        """
        if isinstance(weight_history[0], csr_matrix):
            weight_history = [w.toarray() for w in weight_history]
            
        # Get dimensions
        n_timesteps = len(weight_history)
        n_neurons = weight_history[0].shape[0]
        
        # Create time points if not provided
        if time_points is None:
            time_points = list(range(n_timesteps))
        
        # Select connections to plot if not provided
        if connection_indices is None:
            # Find non-zero connections
            non_zero = np.where(weight_history[0] > 0)
            non_zero_indices = list(zip(non_zero[0], non_zero[1]))
            
            # Select random subset
            if len(non_zero_indices) > 10:
                connection_indices = np.random.choice(len(non_zero_indices), 10, replace=False)
                connection_indices = [non_zero_indices[i] for i in connection_indices]
            else:
                connection_indices = non_zero_indices
        
        # Create figure
        fig, ax = plt.subplots(figsize=figsize)
        
        # Plot weight evolution for each connection
        for pre, post in connection_indices:
            weights = [w[pre, post] for w in weight_history]
            ax.plot(time_points, weights, label=f'({pre}→{post})')
        
        # Set labels and title
        ax.set_xlabel('Time' if time_points is None else 'Time (ms)')
        ax.set_ylabel('Weight')
        if title:
            ax.set_title(title)
        else:
            ax.set_title('Synaptic Weight Evolution')
        
        # Add legend
        ax.legend(title='Connections')
        
        # Grid
        ax.grid(True, linestyle='--', alpha=0.7)
        
        # Save figure if requested
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"Weight evolution visualization saved to {save_path}")
        
        # Show figure if requested
        if show:
            plt.show()
        
        return fig
    
    @staticmethod
    def plot_weight_change_heatmap(
        initial_weights: np.ndarray,
        final_weights: np.ndarray,
        neuron_types: Optional[np.ndarray] = None,
        title: Optional[str] = None,
        save_path: Optional[str] = None,
        figsize: Tuple[int, int] = (12, 10),
        show: bool = True
    ) -> plt.Figure:
        """
        Plot a heatmap of synaptic weight changes.
        
        Args:
            initial_weights: Initial weight matrix
            final_weights: Final weight matrix
            neuron_types: Optional array of neuron types for sorting
            title: Optional title for the plot
            save_path: Optional path to save the figure
            figsize: Figure size as (width, height) in inches
            show: Whether to display the figure
            
        Returns:
            The matplotlib Figure object
        """
        if isinstance(initial_weights, csr_matrix):
            initial_weights = initial_weights.toarray()
        if isinstance(final_weights, csr_matrix):
            final_weights = final_weights.toarray()
            
        # Calculate weight changes
        weight_changes = final_weights - initial_weights
        
        # Sort neurons by type if provided
        if neuron_types is not None:
            # Get sorting indices
            sort_idx = np.argsort(neuron_types)
            
            # Reorder weight changes matrix
            weight_changes_sorted = weight_changes[sort_idx, :][:, sort_idx]
            
            # Get boundaries between types for visualization
            unique_types = np.unique(neuron_types)
            boundaries = []
            for t in unique_types:
                boundary = np.sum(neuron_types[sort_idx] < t)
                if boundary > 0 and boundary < len(neuron_types):
                    boundaries.append(boundary)
        else:
            weight_changes_sorted = weight_changes
            boundaries = []
        
        # Create figure
        fig, ax = plt.subplots(figsize=figsize)
        
        # Create custom colormap (blue-white-red)
        colors = [(0, 0, 1), (1, 1, 1), (1, 0, 0)]
        cmap = LinearSegmentedColormap.from_list('bwr', colors, N=256)
        
        # Plot heatmap
        max_abs_change = np.max(np.abs(weight_changes_sorted))
        im = ax.imshow(
            weight_changes_sorted,
            cmap=cmap,
            vmin=-max_abs_change,
            vmax=max_abs_change,
            aspect='equal',
            interpolation='none'
        )
        
        # Add colorbar
        cbar = fig.colorbar(im, ax=ax)
        cbar.set_label('Weight Change')
        
        # Add type boundaries if available
        for b in boundaries:
            ax.axhline(y=b - 0.5, color='k', linestyle='-', linewidth=1)
            ax.axvline(x=b - 0.5, color='k', linestyle='-', linewidth=1)
        
        # Set labels and title
        ax.set_xlabel('Postsynaptic Neuron')
        ax.set_ylabel('Presynaptic Neuron')
        if title:
            ax.set_title(title)
        else:
            ax.set_title('Synaptic Weight Changes')
        
        # Save figure if requested
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"Weight change heatmap saved to {save_path}")
        
        # Show figure if requested
        if show:
            plt.show()
        
        return fig
    
    @staticmethod
    def plot_pruning_analysis(
        initial_connectivity: np.ndarray,
        final_connectivity: np.ndarray,
        weights: Optional[np.ndarray] = None,
        usage: Optional[np.ndarray] = None,
        title: Optional[str] = None,
        save_path: Optional[str] = None,
        figsize: Tuple[int, int] = (15, 10),
        show: bool = True
    ) -> plt.Figure:
        """
        Analyze and visualize synaptic pruning.
        
        Args:
            initial_connectivity: Initial connectivity matrix (boolean)
            final_connectivity: Final connectivity matrix (boolean)
            weights: Optional weight matrix for analysis
            usage: Optional usage matrix for analysis
            title: Optional title for the plot
            save_path: Optional path to save the figure
            figsize: Figure size as (width, height) in inches
            show: Whether to display the figure
            
        Returns:
            The matplotlib Figure object
        """
        if isinstance(initial_connectivity, csr_matrix):
            initial_connectivity = initial_connectivity.toarray() > 0
        if isinstance(final_connectivity, csr_matrix):
            final_connectivity = final_connectivity.toarray() > 0
        if weights is not None and isinstance(weights, csr_matrix):
            weights = weights.toarray()
        if usage is not None and isinstance(usage, csr_matrix):
            usage = usage.toarray()
            
        # Create figure with multiple subplots
        fig = plt.figure(figsize=figsize)
        gs = plt.GridSpec(2, 3, figure=fig)
        
        # Plot 1: Connectivity change
        ax1 = fig.add_subplot(gs[0, 0])
        pruned = initial_connectivity & ~final_connectivity
        new = ~initial_connectivity & final_connectivity
        unchanged = initial_connectivity & final_connectivity
        
        # Count changes
        n_pruned = np.sum(pruned)
        n_new = np.sum(new)
        n_unchanged = np.sum(unchanged)
        
        # Create pie chart
        ax1.pie(
            [n_pruned, n_new, n_unchanged],
            labels=['Pruned', 'New', 'Unchanged'],
            autopct='%1.1f%%',
            colors=['#ff9999', '#99ff99', '#9999ff']
        )
        ax1.set_title('Connectivity Changes')
        
        # Plot 2: Weight distribution of pruned vs. maintained connections
        ax2 = fig.add_subplot(gs[0, 1])
        if weights is not None:
            # Extract weights of pruned and maintained connections
            pruned_weights = weights[pruned]
            maintained_weights = weights[unchanged]
            
            # Plot histograms
            ax2.hist(
                [pruned_weights, maintained_weights],
                bins=30,
                label=['Pruned', 'Maintained'],
                alpha=0.7
            )
            ax2.set_xlabel('Weight')
            ax2.set_ylabel('Count')
            ax2.set_title('Weight Distribution')
            ax2.legend()
        else:
            ax2.text(0.5, 0.5, 'No weight data provided', ha='center', va='center')
            ax2.set_title('Weight Distribution (No Data)')
        
        # Plot 3: Usage distribution of pruned vs. maintained connections
        ax3 = fig.add_subplot(gs[0, 2])
        if usage is not None:
            # Extract usage of pruned and maintained connections
            pruned_usage = usage[pruned]
            maintained_usage = usage[unchanged]
            
            # Plot histograms
            ax3.hist(
                [pruned_usage, maintained_usage],
                bins=30,
                label=['Pruned', 'Maintained'],
                alpha=0.7
            )
            ax3.set_xlabel('Usage')
            ax3.set_ylabel('Count')
            ax3.set_title('Usage Distribution')
            ax3.legend()
        else:
            ax3.text(0.5, 0.5, 'No usage data provided', ha='center', va='center')
            ax3.set_title('Usage Distribution (No Data)')
        
        # Plot 4: Connectivity matrices (before and after)
        ax4 = fig.add_subplot(gs[1, 0])
        ax4.imshow(initial_connectivity, cmap='binary', interpolation='none')
        ax4.set_title('Initial Connectivity')
        ax4.set_xlabel('Postsynaptic Neuron')
        ax4.set_ylabel('Presynaptic Neuron')
        
        ax5 = fig.add_subplot(gs[1, 1])
        ax5.imshow(final_connectivity, cmap='binary', interpolation='none')
        ax5.set_title('Final Connectivity')
        ax5.set_xlabel('Postsynaptic Neuron')
        ax5.set_ylabel('Presynaptic Neuron')
        
        # Plot 6: Connectivity changes
        ax6 = fig.add_subplot(gs[1, 2])
        # Create a matrix: 1 for pruned, 2 for new, 3 for unchanged
        change_matrix = np.zeros_like(initial_connectivity, dtype=int)
        change_matrix[pruned] = 1
        change_matrix[new] = 2
        change_matrix[unchanged] = 3
        
        # Create custom colormap
        cmap = plt.cm.colors.ListedColormap(['white', '#ff9999', '#99ff99', '#9999ff'])
        bounds = [-0.5, 0.5, 1.5, 2.5, 3.5]
        norm = plt.cm.colors.BoundaryNorm(bounds, cmap.N)
        
        im = ax6.imshow(change_matrix, cmap=cmap, norm=norm, interpolation='none')
        ax6.set_title('Connectivity Changes')
        ax6.set_xlabel('Postsynaptic Neuron')
        ax6.set_ylabel('Presynaptic Neuron')
        
        # Add colorbar
        cbar = fig.colorbar(im, ax=ax6, ticks=[0, 1, 2, 3])
        cbar.set_ticklabels(['None', 'Pruned', 'New', 'Unchanged'])
        
        # Set overall title
        if title:
            fig.suptitle(title, fontsize=16)
        else:
            fig.suptitle('Synaptic Pruning Analysis', fontsize=16)
        
        plt.tight_layout()
        
        # Save figure if requested
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"Pruning analysis visualization saved to {save_path}")
        
        # Show figure if requested
        if show:
            plt.show()
        
        return fig

class NeuromodulationVisualizer:
    """
    Visualization tools for neuromodulation dynamics.
    """
    
    @staticmethod
    def plot_neuromodulator_levels(
        time_points: List[float],
        modulator_levels: Dict[str, List[float]],
        title: Optional[str] = None,
        save_path: Optional[str] = None,
        figsize: Tuple[int, int] = (10, 6),
        show: bool = True
    ) -> plt.Figure:
        """
        Plot neuromodulator levels over time.
        
        Args:
            time_points: List of time points
            modulator_levels: Dictionary mapping modulator names to level time series
            title: Optional title for the plot
            save_path: Optional path to save the figure
            figsize: Figure size as (width, height) in inches
            show: Whether to display the figure
            
        Returns:
            The matplotlib Figure object
        """
        fig, ax = plt.subplots(figsize=figsize)
        
        # Define colors for different neuromodulators
        colors = {
            'dopamine': '#4CAF50',
            'serotonin': '#2196F3',
            'norepinephrine': '#F44336',
            'acetylcholine': '#9C27B0'
        }
        
        # Plot levels for each neuromodulator
        for modulator, levels in modulator_levels.items():
            color = colors.get(modulator.lower(), None)
            ax.plot(time_points, levels, label=modulator.capitalize(), color=color)
        
        # Set labels and title
        ax.set_xlabel('Time (ms)')
        ax.set_ylabel('Neuromodulator Level')
        if title:
            ax.set_title(title)
        else:
            ax.set_title('Neuromodulator Levels Over Time')
        
        # Add legend
        ax.legend()
        
        # Add grid
        ax.grid(True, linestyle='--', alpha=0.7)
        
        # Add baseline
        ax.axhline(y=1.0, color='k', linestyle='--', linewidth=1, alpha=0.5)
        
        # Save figure if requested
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"Neuromodulator levels visualization saved to {save_path}")
        
        # Show figure if requested
        if show:
            plt.show()
        
        return fig
    
    @staticmethod
    def plot_modulation_effects(
        time_points: List[float],
        modulator_levels: Dict[str, List[float]],
        parameter_modulations: Dict[str, List[float]],
        title: Optional[str] = None,
        save_path: Optional[str] = None,
        figsize: Tuple[int, int] = (12, 8),
        show: bool = True
    ) -> plt.Figure:
        """
        Plot neuromodulator levels and their effects on parameter modulation.
        
        Args:
            time_points: List of time points
            modulator_levels: Dictionary mapping modulator names to level time series
            parameter_modulations: Dictionary mapping parameter names to modulation time series
            title: Optional title for the plot
            save_path: Optional path to save the figure
            figsize: Figure size as (width, height) in inches
            show: Whether to display the figure
            
        Returns:
            The matplotlib Figure object
        """
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=figsize, sharex=True, gridspec_kw={'height_ratios': [1, 1]})
        
        # Define colors for different neuromodulators
        modulator_colors = {
            'dopamine': '#4CAF50',
            'serotonin': '#2196F3',
            'norepinephrine': '#F44336',
            'acetylcholine': '#9C27B0'
        }
        
        # Define colors for different parameters
        parameter_colors = {
            'learning': '#FF9800',
            'threshold': '#03A9F4',
            'gain': '#E91E63',
            'adaptation': '#607D8B'
        }
        
        # Plot neuromodulator levels
        for modulator, levels in modulator_levels.items():
            color = modulator_colors.get(modulator.lower(), None)
            ax1.plot(time_points, levels, label=modulator.capitalize(), color=color)
        
        # Plot parameter modulations
        for parameter, modulations in parameter_modulations.items():
            color = parameter_colors.get(parameter.lower(), None)
            ax2.plot(time_points, modulations, label=parameter.capitalize(), color=color)
        
        # Set labels and titles
        ax1.set_ylabel('Neuromodulator Level')
        ax1.set_title('Neuromodulator Levels')
        ax1.legend()
        ax1.grid(True, linestyle='--', alpha=0.7)
        ax1.axhline(y=1.0, color='k', linestyle='--', linewidth=1, alpha=0.5)
        
        ax2.set_xlabel('Time (ms)')
        ax2.set_ylabel('Parameter Modulation')
        ax2.set_title('Parameter Modulations')
        ax2.legend()
        ax2.grid(True, linestyle='--', alpha=0.7)
        ax2.axhline(y=1.0, color='k', linestyle='--', linewidth=1, alpha=0.5)
        
        # Set overall title
        if title:
            fig.suptitle(title, fontsize=14)
        else:
            fig.suptitle('Neuromodulation Effects on Parameters', fontsize=14)
        
        plt.tight_layout()
        
        # Save figure if requested
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"Modulation effects visualization saved to {save_path}")
        
        # Show figure if requested
        if show:
            plt.show()
        
        return fig
    
    @staticmethod
    def plot_dopamine_reward_response(
        time_points: List[float],
        dopamine_levels: List[float],
        reward_times: List[float],
        learning_modulation: Optional[List[float]] = None,
        title: Optional[str] = None,
        save_path: Optional[str] = None,
        figsize: Tuple[int, int] = (10, 6),
        show: bool = True
    ) -> plt.Figure:
        """
        Plot dopamine response to rewards and its effect on learning modulation.
        
        Args:
            time_points: List of time points
            dopamine_levels: List of dopamine levels over time
            reward_times: List of reward delivery times
            learning_modulation: Optional list of learning rate modulation over time
            title: Optional title for the plot
            save_path: Optional path to save the figure
            figsize: Figure size as (width, height) in inches
            show: Whether to display the figure
            
        Returns:
            The matplotlib Figure object
        """
        if learning_modulation is not None:
            fig, (ax1, ax2) = plt.subplots(2, 1, figsize=figsize, sharex=True, gridspec_kw={'height_ratios': [2, 1]})
        else:
            fig, ax1 = plt.subplots(figsize=figsize)
        
        # Plot dopamine levels
        ax1.plot(time_points, dopamine_levels, color='#4CAF50', linewidth=2)
        
        # Add reward times as vertical lines
        for t in reward_times:
            ax1.axvline(x=t, color='r', linestyle='--', alpha=0.7)
        
        # Set labels and title for dopamine plot
        ax1.set_ylabel('Dopamine Level')
        ax1.set_title('Dopamine Response to Rewards')
        ax1.grid(True, linestyle='--', alpha=0.7)
        ax1.axhline(y=1.0, color='k', linestyle='--', linewidth=1, alpha=0.5)
        
        # Add reward markers in legend
        from matplotlib.lines import Line2D
        reward_line = Line2D([0], [0], color='r', linestyle='--', label='Reward')
        ax1.legend(handles=[reward_line])
        
        # Add learning modulation if provided
        if learning_modulation is not None:
            ax2.plot(time_points, learning_modulation, color='#FF9800', linewidth=2)
            
            # Set labels for learning modulation plot
            ax2.set_xlabel('Time (ms)')
            ax2.set_ylabel('Learning Rate\nModulation')
            ax2.set_title('Effect on Learning Rate')
            ax2.grid(True, linestyle='--', alpha=0.7)
            ax2.axhline(y=1.0, color='k', linestyle='--', linewidth=1, alpha=0.5)
        else:
            ax1.set_xlabel('Time (ms)')
        
        # Set overall title
        if title:
            fig.suptitle(title, fontsize=14)
        else:
            fig.suptitle('Dopamine Reward Response', fontsize=14)
        
        plt.tight_layout()
        
        # Save figure if requested
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"Dopamine reward response visualization saved to {save_path}")
        
        # Show figure if requested
        if show:
            plt.show()
        
        return fig

class OscillationVisualizer:
    """
    Visualization tools for neural oscillations and timing circuits.
    """
    
    @staticmethod
    def plot_oscillations(
        time_points: List[float],
        oscillator_outputs: Dict[str, List[float]],
        title: Optional[str] = None,
        save_path: Optional[str] = None,
        figsize: Tuple[int, int] = (10, 6),
        show: bool = True
    ) -> plt.Figure:
        """
        Plot the outputs of neural oscillators over time.
        
        Args:
            time_points: List of time points
            oscillator_outputs: Dictionary mapping oscillator names to output time series
            title: Optional title for the plot
            save_path: Optional path to save the figure
            figsize: Figure size as (width, height) in inches
            show: Whether to display the figure
            
        Returns:
            The matplotlib Figure object
        """
        fig, ax = plt.subplots(figsize=figsize)
        
        # Define colors for different oscillators
        colors = {
            'delta': '#1E88E5',
            'theta': '#43A047',
            'alpha': '#FB8C00',
            'beta': '#E53935',
            'gamma': '#8E24AA'
        }
        
        # Plot outputs for each oscillator
        for oscillator, outputs in oscillator_outputs.items():
            color = colors.get(oscillator.lower(), None)
            ax.plot(time_points, outputs, label=oscillator.capitalize(), color=color)
        
        # Set labels and title
        ax.set_xlabel('Time (ms)')
        ax.set_ylabel('Oscillator Output')
        if title:
            ax.set_title(title)
        else:
            ax.set_title('Neural Oscillations')
        
        # Add legend
        ax.legend()
        
        # Add grid
        ax.grid(True, linestyle='--', alpha=0.7)
        
        # Save figure if requested
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"Oscillations visualization saved to {save_path}")
        
        # Show figure if requested
        if show:
            plt.show()
        
        return fig
    
    @staticmethod
    def plot_oscillator_phases(
        time_points: List[float],
        oscillator_phases: Dict[str, List[float]],
        title: Optional[str] = None,
        save_path: Optional[str] = None,
        figsize: Tuple[int, int] = (10, 6),
        show: bool = True
    ) -> plt.Figure:
        """
        Plot the phases of neural oscillators over time.
        
        Args:
            time_points: List of time points
            oscillator_phases: Dictionary mapping oscillator names to phase time series
            title: Optional title for the plot
            save_path: Optional path to save the figure
            figsize: Figure size as (width, height) in inches
            show: Whether to display the figure
            
        Returns:
            The matplotlib Figure object
        """
        fig, ax = plt.subplots(figsize=figsize)
        
        # Define colors for different oscillators
        colors = {
            'delta': '#1E88E5',
            'theta': '#43A047',
            'alpha': '#FB8C00',
            'beta': '#E53935',
            'gamma': '#8E24AA'
        }
        
        # Plot wrapped phases for each oscillator
        for oscillator, phases in oscillator_phases.items():
            color = colors.get(oscillator.lower(), None)
            # Wrap phases to [0, 2π]
            wrapped_phases = [phase % (2 * np.pi) for phase in phases]
            ax.plot(time_points, wrapped_phases, label=oscillator.capitalize(), color=color)
        
        # Set labels and title
        ax.set_xlabel('Time (ms)')
        ax.set_ylabel('Phase (radians)')
        if title:
            ax.set_title(title)
        else:
            ax.set_title('Oscillator Phases')
        
        # Set y-axis limits
        ax.set_ylim(0, 2 * np.pi)
        
        # Set y-axis ticks
        ax.set_yticks([0, np.pi/2, np.pi, 3*np.pi/2, 2*np.pi])
        ax.set_yticklabels(['0', 'π/2', 'π', '3π/2', '2π'])
        
        # Add legend
        ax.legend()
        
        # Add grid
        ax.grid(True, linestyle='--', alpha=0.7)
        
        # Save figure if requested
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"Oscillator phases visualization saved to {save_path}")
        
        # Show figure if requested
        if show:
            plt.show()
        
        return fig
    
    @staticmethod
    def plot_phase_amplitude_coupling(
        time_points: List[float],
        carrier_oscillator: Tuple[str, List[float]],
        modulated_oscillator: Tuple[str, List[float]],
        title: Optional[str] = None,
        save_path: Optional[str] = None,
        figsize: Tuple[int, int] = (12, 8),
        show: bool = True
    ) -> plt.Figure:
        """
        Plot phase-amplitude coupling between two oscillators.
        
        Args:
            time_points: List of time points
            carrier_oscillator: Tuple of (name, outputs) for the carrier (low frequency) oscillator
            modulated_oscillator: Tuple of (name, outputs) for the modulated (high frequency) oscillator
            title: Optional title for the plot
            save_path: Optional path to save the figure
            figsize: Figure size as (width, height) in inches
            show: Whether to display the figure
            
        Returns:
            The matplotlib Figure object
        """
        fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=figsize, sharex=True, gridspec_kw={'height_ratios': [1, 1, 1]})
        
        # Unpack oscillator data
        carrier_name, carrier_output = carrier_oscillator
        modulated_name, modulated_output = modulated_oscillator
        
        # Define colors
        carrier_color = '#43A047'  # Green
        modulated_color = '#E53935'  # Red
        
        # Plot carrier oscillator
        ax1.plot(time_points, carrier_output, color=carrier_color, linewidth=2)
        ax1.set_ylabel(f'{carrier_name.capitalize()}\nOutput')
        ax1.set_title(f'{carrier_name.capitalize()} Oscillation (Carrier)')
        ax1.grid(True, linestyle='--', alpha=0.7)
        
        # Plot modulated oscillator
        ax2.plot(time_points, modulated_output, color=modulated_color, linewidth=1)
        ax2.set_ylabel(f'{modulated_name.capitalize()}\nOutput')
        ax2.set_title(f'{modulated_name.capitalize()} Oscillation (Modulated)')
        ax2.grid(True, linestyle='--', alpha=0.7)
        
        # Plot combined view
        ax3.plot(time_points, carrier_output, color=carrier_color, linewidth=2, alpha=0.7, label=carrier_name.capitalize())
        
        # Calculate amplitude envelope of modulated oscillator
        from scipy.signal import hilbert
        analytic_signal = hilbert(modulated_output)
        amplitude_envelope = np.abs(analytic_signal)
        
        # Plot modulated oscillator with amplitude envelope
        ax3.plot(time_points, modulated_output, color=modulated_color, linewidth=1, alpha=0.5, label=f'{modulated_name.capitalize()} (Raw)')
        ax3.plot(time_points, amplitude_envelope, color='blue', linewidth=1.5, label=f'{modulated_name.capitalize()} Envelope')
        
        ax3.set_xlabel('Time (ms)')
        ax3.set_ylabel('Output')
        ax3.set_title('Phase-Amplitude Coupling')
        ax3.legend()
        ax3.grid(True, linestyle='--', alpha=0.7)
        
        # Set overall title
        if title:
            fig.suptitle(title, fontsize=14)
        else:
            fig.suptitle(f'Phase-Amplitude Coupling: {carrier_name.capitalize()}-{modulated_name.capitalize()}', fontsize=14)
        
        plt.tight_layout()
        
        # Save figure if requested
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"Phase-amplitude coupling visualization saved to {save_path}")
        
        # Show figure if requested
        if show:
            plt.show()
        
        # Create a second figure with phase-amplitude plot
        fig2 = plt.figure(figsize=(8, 8))
        
        # Calculate carrier phases
        from scipy.signal import hilbert
        carrier_analytic = hilbert(carrier_output)
        carrier_phase = np.angle(carrier_analytic)
        
        # Create phase bins
        n_bins = 20
        bin_size = 2 * np.pi / n_bins
        phase_bins = np.linspace(0, 2*np.pi, n_bins+1)
        
        # Calculate mean amplitude in each phase bin
        bin_amplitudes = np.zeros(n_bins)
        bin_counts = np.zeros(n_bins)
        
        for i in range(len(carrier_phase)):
            # Find the bin for this phase
            phase = carrier_phase[i] % (2 * np.pi)
            bin_idx = int(phase / bin_size)
            if bin_idx == n_bins:
                bin_idx = 0
            
            # Add amplitude to this bin
            bin_amplitudes[bin_idx] += amplitude_envelope[i]
            bin_counts[bin_idx] += 1
        
        # Calculate mean amplitude for each bin
        bin_amplitudes = bin_amplitudes / np.maximum(bin_counts, 1)  # Avoid division by zero
        
        # Plot phase-amplitude relationship in polar coordinates
        ax_polar = fig2.add_subplot(111, projection='polar')
        
        # Repeat the first bin at the end for a complete circle
        bin_centers = phase_bins[:-1] + bin_size/2
        bin_amplitudes_plot = np.append(bin_amplitudes, bin_amplitudes[0])
        bin_centers_plot = np.append(bin_centers, bin_centers[0] + 2*np.pi)
        
        # Plot in polar coordinates
        ax_polar.plot(bin_centers_plot, bin_amplitudes_plot, marker='o', linewidth=2)
        
        # Fill the area
        ax_polar.fill(bin_centers_plot, bin_amplitudes_plot, alpha=0.3)
        
        # Set title
        if title:
            ax_polar.set_title(f'{title}\nPhase-Amplitude Coupling')
        else:
            ax_polar.set_title(f'{modulated_name.capitalize()} Amplitude vs. {carrier_name.capitalize()} Phase')
        
        # Set polar axis labels
        ax_polar.set_xticks([0, np.pi/2, np.pi, 3*np.pi/2])
        ax_polar.set_xticklabels(['0', 'π/2', 'π', '3π/2'])
        
        # Save polar plot if requested
        if save_path:
            polar_save_path = save_path.replace('.', '_polar.')
            plt.savefig(polar_save_path, dpi=300, bbox_inches='tight')
            logger.info(f"Phase-amplitude polar plot saved to {polar_save_path}")
        
        # Show figure if requested
        if show:
            plt.show()
        
        return fig
    
    @staticmethod
    def plot_spectrogram(
        time_points: List[float],
        signal: Union[List[float], Dict[str, List[float]]],
        fs: float = 1000.0,
        title: Optional[str] = None,
        save_path: Optional[str] = None,
        figsize: Tuple[int, int] = (12, 8),
        show: bool = True
    ) -> plt.Figure:
        """
        Plot a spectrogram to visualize oscillatory components over time.
        
        Args:
            time_points: List of time points
            signal: Either a single time series or a dictionary of named time series
            fs: Sampling frequency in Hz (default: 1000 Hz)
            title: Optional title for the plot
            save_path: Optional path to save the figure
            figsize: Figure size as (width, height) in inches
            show: Whether to display the figure
            
        Returns:
            The matplotlib Figure object
        """
        if isinstance(signal, dict):
            # Multiple signals
            n_signals = len(signal)
            fig, axs = plt.subplots(n_signals, 1, figsize=figsize, sharex=True)
            
            for i, (name, values) in enumerate(signal.items()):
                ax = axs[i] if n_signals > 1 else axs
                
                # Convert to numpy array if needed
                values = np.array(values)
                
                # Compute spectrogram
                f, t, Sxx = scipy.signal.spectrogram(
                    values,
                    fs=fs,
                    window='hann',
                    nperseg=min(256, len(values)//5),
                    noverlap=min(128, len(values)//10),
                    detrend='constant'
                )
                
                # Plot spectrogram
                im = ax.pcolormesh(t, f, 10 * np.log10(Sxx + 1e-10), shading='gouraud', cmap='viridis')
                
                # Set frequency limit to better visualize low-frequency components
                ax.set_ylim(0, min(100, fs/2))
                
                # Add frequency band labels
                frequency_bands = {
                    'Delta': (1, 4),
                    'Theta': (4, 8),
                    'Alpha': (8, 13),
                    'Beta': (13, 30),
                    'Gamma': (30, 100)
                }
                
                for band_name, (f_min, f_max) in frequency_bands.items():
                    if f_max <= ax.get_ylim()[1]:
                        ax.axhline(y=f_min, color='r', linestyle='--', linewidth=1, alpha=0.3)
                        ax.axhline(y=f_max, color='r', linestyle='--', linewidth=1, alpha=0.3)
                        ax.text(t[-1] * 1.01, (f_min + f_max) / 2, band_name, va='center')
                
                # Set labels
                ax.set_ylabel('Frequency (Hz)')
                if i == n_signals - 1:
                    ax.set_xlabel('Time (s)')
                else:
                    ax.set_xlabel('')
                ax.set_title(f'{name.capitalize()} Spectrogram')
                
                # Add colorbar
                plt.colorbar(im, ax=ax, label='Power/Frequency (dB/Hz)')
        else:
            # Single signal
            fig, ax = plt.subplots(figsize=figsize)
            
            # Convert to numpy array if needed
            values = np.array(signal)
            
            # Compute spectrogram
            f, t, Sxx = scipy.signal.spectrogram(
                values,
                fs=fs,
                window='hann',
                nperseg=min(256, len(values)//5),
                noverlap=min(128, len(values)//10),
                detrend='constant'
            )
            
            # Plot spectrogram
            im = ax.pcolormesh(t, f, 10 * np.log10(Sxx + 1e-10), shading='gouraud', cmap='viridis')
            
            # Set frequency limit to better visualize low-frequency components
            ax.set_ylim(0, min(100, fs/2))
            
            # Add frequency band labels
            frequency_bands = {
                'Delta': (1, 4),
                'Theta': (4, 8),
                'Alpha': (8, 13),
                'Beta': (13, 30),
                'Gamma': (30, 100)
            }
            
            for band_name, (f_min, f_max) in frequency_bands.items():
                if f_max <= ax.get_ylim()[1]:
                    ax.axhline(y=f_min, color='r', linestyle='--', linewidth=1, alpha=0.3)
                    ax.axhline(y=f_max, color='r', linestyle='--', linewidth=1, alpha=0.3)
                    ax.text(t[-1] * 1.01, (f_min + f_max) / 2, band_name, va='center')
            
            # Set labels
            ax.set_xlabel('Time (s)')
            ax.set_ylabel('Frequency (Hz)')
            if title:
                ax.set_title(title)
            else:
                ax.set_title('Signal Spectrogram')
            
            # Add colorbar
            plt.colorbar(im, ax=ax, label='Power/Frequency (dB/Hz)')
        
        plt.tight_layout()
        
        # Save figure if requested
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"Spectrogram visualization saved to {save_path}")
        
        # Show figure if requested
        if show:
            plt.show()
        
        return fig

# Usage example if the module is run directly
if __name__ == "__main__":
    # Create sample data for demonstration
    # This is just for demonstration purposes and not actual test data
    
    # Time points
    time_points = np.linspace(0, 1000, 1000)
    
    # Oscillations
    oscillator_outputs = {
        'delta': 0.5 * np.sin(2 * np.pi * 2 * time_points / 1000),
        'theta': 0.5 * np.sin(2 * np.pi * 6 * time_points / 1000),
        'alpha': 0.5 * np.sin(2 * np.pi * 10 * time_points / 1000),
        'beta': 0.5 * np.sin(2 * np.pi * 20 * time_points / 1000),
        'gamma': 0.5 * np.sin(2 * np.pi * 40 * time_points / 1000)
    }
    
    # Plot oscillations
    OscillationVisualizer.plot_oscillations(
        time_points,
        oscillator_outputs,
        title="Sample Neural Oscillations"
    )