#!/usr/bin/env python3
"""
ULTRA: Ultimate Learning & Thought Reasoning Architecture
Core Neural Architecture - Integration Tests

This module contains integration tests that verify the correct interaction
and combined functionality of all Core Neural Architecture components
working together as a unified system.
"""

import os
import sys
import numpy as np
import pytest
import scipy.sparse
import matplotlib.pyplot as plt
import logging
from numpy.testing import assert_array_almost_equal
from scipy.stats import pearsonr

# Ensure the test module is in the path
ROOT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if ROOT_DIR not in sys.path:
    sys.path.insert(0, ROOT_DIR)

# Import test configuration and utilities
from tests.test_core_neural import (
    test_network, network_state_recorder, input_pattern_generator,
    TestUtils
)

# Import modules to test
from ultra.core_neural.neuromorphic_core import NeuromorphicCore
from ultra.core_neural.neuroplasticity_engine import NeuroplasticityEngine
from ultra.core_neural.synaptic_pruning import SynapticPruningModule
from ultra.core_neural.neuromodulation import NeuromodulationSystem
from ultra.core_neural.biological_timing import BiologicalTimingCircuits

# Configure logging
logger = logging.getLogger(__name__)

class TestCoreNeuralIntegration:
    """Integration tests for the combined Core Neural Architecture components"""
    
    def test_full_system_initialization(self, test_network):
        """Test that a complete network with all components initializes correctly"""
        network = test_network
        
        # Check that all components are attached
        assert hasattr(network, 'neuroplasticity_engine'), "Network should have neuroplasticity_engine"
        assert hasattr(network, 'synaptic_pruning_module'), "Network should have synaptic_pruning_module"
        assert hasattr(network, 'neuromodulation_system'), "Network should have neuromodulation_system"
        assert hasattr(network, 'biological_timing'), "Network should have biological_timing_circuits"
        
        # Check that components are properly connected to the network
        assert network.neuroplasticity_engine is not None, "Neuroplasticity engine should be attached"
        assert network.synaptic_pruning_module is not None, "Synaptic pruning module should be attached"
        assert network.neuromodulation_system is not None, "Neuromodulation system should be attached"
        assert network.biological_timing is not None, "Biological timing circuits should be attached"
    
    def test_neuroplasticity_synaptic_pruning_integration(self, test_network, input_pattern_generator):
        """Test integration between neuroplasticity and synaptic pruning"""
        network = test_network
        
        # Create a scenario where we have three groups of neurons:
        # A: neurons 0-4 (active together)
        # B: neurons 5-9 (active together)
        # C: neurons 10-14 (not active or weakly active)
        
        n_neurons = network.total_neurons
        neuron_groups = {
            'A': list(range(5)),
            'B': list(range(5, 10)),
            'C': list(range(10, 15))
        }
        
        # Configure simulation
        duration = 10000  # ms
        dt = 1.0  # ms
        steps = int(duration / dt)
        
        # Generate correlated activity patterns
        spike_trains = np.zeros((n_neurons, steps))
        
        # Group A activity - synchronous bursts
        for t in range(0, steps, 200):  # Every 200ms
            if np.random.rand() < 0.8:  # 80% probability
                for neuron_id in neuron_groups['A']:
                    # Add jitter
                    spike_time = min(steps-1, t + np.random.randint(-10, 11))
                    spike_trains[neuron_id, spike_time] = 1
        
        # Group B activity - synchronous bursts at different times
        for t in range(100, steps, 200):  # Offset from Group A
            if np.random.rand() < 0.8:
                for neuron_id in neuron_groups['B']:
                    # Add jitter
                    spike_time = min(steps-1, t + np.random.randint(-10, 11))
                    spike_trains[neuron_id, spike_time] = 1
        
        # Group C - very sparse random activity
        for neuron_id in neuron_groups['C']:
            for t in range(steps):
                if np.random.rand() < 0.001:  # Very low firing rate
                    spike_trains[neuron_id, t] = 1
        
        # Create a recorder to track the network state
        recorder = network_state_recorder()
        
        # Run the simulation
        for step in range(steps):
            # Apply spikes as input currents
            input_current = np.zeros(n_neurons)
            for neuron_id in range(n_neurons):
                if spike_trains[neuron_id, step] > 0:
                    input_current[neuron_id] = 2.0  # Strong current to ensure spiking
                    
                    # Record spike for STDP
                    network.neuroplasticity_engine.record_spike(step, neuron_id)
            
            # Update network
            network.update(input_current, dt)
            
            # Record state
            if step % 100 == 0:  # Record every 100ms to save memory
                recorder.record_state(network, step)
            
            # Apply STDP every 200ms
            if step % 200 == 0 and step > 0:
                # Get current weights
                weights = network.connections.toarray()
                
                # Apply STDP
                weights = network.neuroplasticity_engine.apply_stdp(weights)
                
                # Update network weights
                network.connections = scipy.sparse.csr_matrix(weights)
            
            # Apply synaptic pruning every 1000ms
            if step % 1000 == 0 and step > 0:
                # Get current connectivity
                connectivity = network.connections.toarray() > 0
                
                # Get weights
                weights = network.connections.toarray()
                
                # Calculate usage statistics (simplified model)
                usage = np.zeros_like(weights)
                for pre in range(n_neurons):
                    for post in range(n_neurons):
                        if connectivity[pre, post]:
                            # Usage is proportional to weight and joint activity
                            pre_spikes = spike_trains[pre, max(0, step-1000):step]
                            post_spikes = spike_trains[post, max(0, step-1000):step]
                            
                            # Count near-coincidences (within 20ms)
                            coincidences = 0
                            for t_pre in np.where(pre_spikes)[0]:
                                window_start = t_pre
                                window_end = min(len(post_spikes), t_pre + 20)
                                coincidences += np.sum(post_spikes[window_start:window_end])
                            
                            usage[pre, post] = coincidences / max(1, np.sum(pre_spikes))
                
                # Calculate activity correlations
                activity_corr = np.zeros((n_neurons, n_neurons))
                for i in range(n_neurons):
                    for j in range(i+1, n_neurons):
                        corr, _ = pearsonr(spike_trains[i, max(0, step-1000):step], 
                                            spike_trains[j, max(0, step-1000):step])
                        if np.isnan(corr):
                            corr = 0
                        activity_corr[i, j] = corr
                        activity_corr[j, i] = corr  # Make symmetric
                
                # Apply pruning
                new_connectivity = network.synaptic_pruning_module.prune(
                    weights, usage, activity_corr, connectivity
                )
                
                # Update network connectivity
                # Zero out pruned weights
                weights[~new_connectivity] = 0
                network.connections = scipy.sparse.csr_matrix(weights)
        
        # Analyze results
        
        # 1. STDP should strengthen connections between correlated neurons
        final_weights = network.connections.toarray()
        
        # Calculate mean weights between and within groups
        weight_within_A = 0
        count_within_A = 0
        for i in neuron_groups['A']:
            for j in neuron_groups['A']:
                if i != j:
                    weight_within_A += final_weights[i, j]
                    count_within_A += 1
        weight_within_A /= max(1, count_within_A)
        
        weight_within_B = 0
        count_within_B = 0
        for i in neuron_groups['B']:
            for j in neuron_groups['B']:
                if i != j:
                    weight_within_B += final_weights[i, j]
                    count_within_B += 1
        weight_within_B /= max(1, count_within_B)
        
        weight_A_to_B = 0
        count_A_to_B = 0
        for i in neuron_groups['A']:
            for j in neuron_groups['B']:
                weight_A_to_B += final_weights[i, j]
                count_A_to_B += 1
        weight_A_to_B /= max(1, count_A_to_B)
        
        weight_to_C = 0
        count_to_C = 0
        for i in range(n_neurons):
            if i not in neuron_groups['C']:
                for j in neuron_groups['C']:
                    weight_to_C += final_weights[i, j]
                    count_to_C += 1
        weight_to_C /= max(1, count_to_C)
        
        # Weights should be higher within correlated groups than between groups
        assert weight_within_A > weight_A_to_B, \
            f"Weights within group A ({weight_within_A}) should be higher than between A and B ({weight_A_to_B})"
            
        assert weight_within_B > weight_A_to_B, \
            f"Weights within group B ({weight_within_B}) should be higher than between A and B ({weight_A_to_B})"
            
        # Weights to the inactive group C should be lowest
        assert weight_to_C < weight_A_to_B, \
            f"Weights to inactive group C ({weight_to_C}) should be lower than between groups A and B ({weight_A_to_B})"
        
        # 2. Pruning should have removed weak connections, especially to inactive neurons
        initial_connectivity = np.ones((n_neurons, n_neurons), dtype=bool)
        np.fill_diagonal(initial_connectivity, False)  # No self-connections
        
        final_connectivity = final_weights > 0
        
        # Count connections from active to inactive neurons
        active_to_inactive_connections = 0
        for i in range(n_neurons):
            if i not in neuron_groups['C']:
                for j in neuron_groups['C']:
                    if final_connectivity[i, j]:
                        active_to_inactive_connections += 1
        
        # Most connections to inactive neurons should be pruned
        assert active_to_inactive_connections < 0.5 * len(neuron_groups['C']) * (n_neurons - len(neuron_groups['C'])), \
            f"Most connections to inactive neurons should be pruned, but {active_to_inactive_connections} remain"
    
    def test_neuromodulation_neuroplasticity_integration(self, test_network, input_pattern_generator):
        """Test integration between neuromodulation and neuroplasticity"""
        network = test_network
        
        # Configure the simulation
        n_neurons = network.total_neurons
        duration = 5000  # ms
        dt = 1.0  # ms
        steps = int(duration / dt)
        
        # Generate a simple pattern of activity
        spike_trains = np.zeros((n_neurons, steps))
        
        # Two groups of neurons that will be active at different times
        group1 = list(range(5))
        group2 = list(range(5, 10))
        
        # Group 1 is active in the first half of the simulation
        for t in range(0, steps // 2, 100):  # Every 100ms in first half
            if np.random.rand() < 0.8:  # 80% probability
                for neuron_id in group1:
                    # Add jitter
                    spike_time = min(steps-1, t + np.random.randint(-10, 11))
                    spike_trains[neuron_id, spike_time] = 1
        
        # Group 2 is active in the second half of the simulation
        for t in range(steps // 2, steps, 100):  # Every 100ms in second half
            if np.random.rand() < 0.8:
                for neuron_id in group2:
                    # Add jitter
                    spike_time = min(steps-1, t + np.random.randint(-10, 11))
                    spike_trains[neuron_id, spike_time] = 1
        
        # Create dopamine signal that increases during the second half
        # (to modulate learning rate)
        dopamine = np.ones(steps)
        dopamine[steps//2:] = 2.0  # Higher dopamine in second half
        
        # Create recorder to track network state
        recorder = network_state_recorder()
        
        # Run simulation
        for step in range(steps):
            # Apply spikes as input currents
            input_current = np.zeros(n_neurons)
            for neuron_id in range(n_neurons):
                if spike_trains[neuron_id, step] > 0:
                    input_current[neuron_id] = 2.0  # Strong current to ensure spiking
                    
                    # Record spike for STDP
                    network.neuroplasticity_engine.record_spike(step, neuron_id)
            
            # Apply neuromodulation
            neuromodulator_inputs = {
                "dopamine": dopamine[step],
                "serotonin": 1.0,  # Baseline
                "norepinephrine": 1.0,  # Baseline
                "acetylcholine": 1.0  # Baseline
            }
            network.neuromodulation_system.update(neuromodulator_inputs, dt)
            
            # Get modulated learning rate
            modulations = network.neuromodulation_system.compute_modulations()
            learning_rate_mod = modulations["learning"]
            
            # Update neuroplasticity engine's learning rate
            network.neuroplasticity_engine.set_learning_rate(
                network.neuroplasticity_engine.base_learning_rate * learning_rate_mod
            )
            
            # Update network
            network.update(input_current, dt)
            
            # Record state
            if step % 100 == 0:  # Record every 100ms
                recorder.record_state(network, step)
            
            # Apply STDP every 100ms
            if step % 100 == 0 and step > 0:
                # Get current weights
                weights = network.connections.toarray()
                
                # Apply STDP
                weights = network.neuroplasticity_engine.apply_stdp(weights)
                
                # Update network weights
                network.connections = scipy.sparse.csr_matrix(weights)
        
        # Analyze results
        final_weights = network.connections.toarray()
        
        # Calculate mean weight changes for connections within each group
        within_group1 = []
        within_group2 = []
        
        for i in group1:
            for j in group1:
                if i != j:
                    within_group1.append(final_weights[i, j])
        
        for i in group2:
            for j in group2:
                if i != j:
                    within_group2.append(final_weights[i, j])
        
        mean_group1 = np.mean(within_group1)
        mean_group2 = np.mean(within_group2)
        
        # Group 2 connections should be stronger due to higher dopamine during second half
        assert mean_group2 > mean_group1, \
            f"Group 2 connections ({mean_group2}) should be stronger than group 1 ({mean_group1}) due to higher dopamine"
        
        # The ratio should be related to the dopamine level ratio
        weight_ratio = mean_group2 / mean_group1
        dopamine_ratio = 2.0 / 1.0  # Second half dopamine / first half dopamine
        
        # The relationship won't be exactly linear, but there should be a correlation
        assert weight_ratio > 1.2, \
            f"Weight ratio ({weight_ratio}) should be significantly higher than 1 due to dopamine modulation"
    
    def test_biological_timing_neuromodulation_integration(self, test_network):
        """Test integration between biological timing circuits and neuromodulation"""
        network = test_network
        
        # Configure simulation
        n_neurons = network.total_neurons
        duration = 5000  # ms
        dt = 1.0  # ms
        steps = int(duration / dt)
        
        # Create recorder
        recorder = network_state_recorder()
        
        # Run simulation with oscillatory neuromodulation
        for step in range(steps):
            t = step * dt
            
            # Update biological timing circuits
            network.biological_timing.update(dt)
            
            # Get oscillator outputs
            osc_outputs = network.biological_timing.get_outputs()
            
            # Use theta oscillation to modulate dopamine
            # (theta-dopamine coupling)
            dopamine_level = 1.0 + 0.5 * osc_outputs["theta"]
            
            # Use alpha oscillation to modulate norepinephrine
            # (alpha-norepinephrine coupling)
            norepinephrine_level = 1.0 + 0.5 * osc_outputs["alpha"]
            
            # Apply neuromodulation
            neuromodulator_inputs = {
                "dopamine": dopamine_level,
                "serotonin": 1.0,  # Baseline
                "norepinephrine": norepinephrine_level,
                "acetylcholine": 1.0  # Baseline
            }
            network.neuromodulation_system.update(neuromodulator_inputs, dt)
            
            # Get modulated parameters
            modulations = network.neuromodulation_system.compute_modulations()
            learning_rate_mod = modulations["learning"]  # Mainly affected by dopamine
            gain_mod = modulations["gain"]  # Mainly affected by norepinephrine
            
            # Apply gain modulation to neuron excitability
            excitability = np.ones(n_neurons) * gain_mod
            
            # Create a simple input pattern
            input_current = np.random.rand(n_neurons) * 0.5
            
            # Update network
            network.update(input_current * excitability, dt)
            
            # Record state
            if step % 100 == 0:
                recorder.record_state(network, step)
        
        # Analyze results
        
        # Extract time series
        time_points = recorder.timestamps
        
        # Extract oscillator outputs
        theta_outputs = []
        alpha_outputs = []
        for t in range(len(time_points)):
            if hasattr(recorder.oscillator_phases, "theta"):
                theta_outputs.append(np.sin(recorder.oscillator_phases["theta"][t]))
            if hasattr(recorder.oscillator_phases, "alpha"):
                alpha_outputs.append(np.sin(recorder.oscillator_phases["alpha"][t]))
        
        # Extract neuromodulator levels
        dopamine_levels = recorder.neuromodulator_levels.get("dopamine", [])
        norepinephrine_levels = recorder.neuromodulator_levels.get("norepinephrine", [])
        
        # There should be a correlation between theta and dopamine
        if len(theta_outputs) > 0 and len(dopamine_levels) > 0:
            min_len = min(len(theta_outputs), len(dopamine_levels))
            theta_dopamine_corr, _ = pearsonr(theta_outputs[:min_len], dopamine_levels[:min_len])
            assert theta_dopamine_corr > 0.5, \
                f"Theta oscillation and dopamine levels should be correlated, got {theta_dopamine_corr}"
        
        # There should be a correlation between alpha and norepinephrine
        if len(alpha_outputs) > 0 and len(norepinephrine_levels) > 0:
            min_len = min(len(alpha_outputs), len(norepinephrine_levels))
            alpha_ne_corr, _ = pearsonr(alpha_outputs[:min_len], norepinephrine_levels[:min_len])
            assert alpha_ne_corr > 0.5, \
                f"Alpha oscillation and norepinephrine levels should be correlated, got {alpha_ne_corr}"
        
        # Activity patterns should show oscillatory modulation
        # Extract average membrane potential over time
        membrane_potentials = recorder.membrane_potentials
        if len(membrane_potentials) > 0:
            # Average across neurons
            avg_potentials = [np.mean(potentials) for potentials in membrane_potentials]
            
            # Calculate power spectrum
            from scipy.signal import welch
            f, pxx = welch(avg_potentials, fs=1000/100)  # Assuming 100ms sampling
            
            # Find peaks in power spectrum
            from scipy.signal import find_peaks
            peaks, _ = find_peaks(pxx)
            
            # Check if there are peaks corresponding to theta and alpha frequencies
            theta_freq = network.biological_timing.oscillators["theta"].frequency
            alpha_freq = network.biological_timing.oscillators["alpha"].frequency
            
            # Find the closest peak to theta frequency
            if len(peaks) > 0:
                closest_to_theta = min(peaks, key=lambda p: abs(f[p] - theta_freq))
                closest_to_alpha = min(peaks, key=lambda p: abs(f[p] - alpha_freq))
                
                # Check that there is power at frequencies close to theta and alpha
                assert abs(f[closest_to_theta] - theta_freq) < 2.0 or abs(f[closest_to_alpha] - alpha_freq) < 2.0, \
                    "Neural activity should show oscillatory patterns at theta or alpha frequencies"
    
    def test_full_system_learning_task(self, test_network):
        """Test the full system's ability to learn a simple classification task"""
        network = test_network
        
        # Define a simple binary classification task
        # Two input patterns that should be classified into two output categories
        
        # Configure the task
        n_neurons = network.total_neurons
        input_size = 5  # First 5 neurons are input
        output_size = 2  # Next 2 neurons are output
        
        input_neurons = list(range(input_size))
        output_neurons = list(range(input_size, input_size + output_size))
        
        # Define input patterns
        patterns = {
            0: np.array([1, 1, 0, 0, 0]),  # Pattern for class 0
            1: np.array([0, 0, 1, 1, 1])   # Pattern for class 1
        }
        
        # Define desired outputs
        desired_outputs = {
            0: 0,  # First pattern should activate first output neuron
            1: 1   # Second pattern should activate second output neuron
        }
        
        # Configure simulation
        duration_per_trial = 500  # ms
        dt = 1.0  # ms
        n_trials = 100
        
        # Prepare for training
        trial_order = np.random.randint(0, 2, n_trials)  # Random sequence of patterns
        
        # Create recorder
        recorder = network_state_recorder()
        
        # Run simulation
        for trial in range(n_trials):
            pattern_idx = trial_order[trial]
            pattern = patterns[pattern_idx]
            desired_output = desired_outputs[pattern_idx]
            
            # Reset spike history between trials
            network.neuroplasticity_engine.reset()
            
            # Theta phase reset at the start of each trial
            network.biological_timing.reset_oscillator_phase("theta", 0.0)
            
            for step in range(int(duration_per_trial / dt)):
                t = step * dt
                
                # Update biological timing
                network.biological_timing.update(dt)
                
                # Present the pattern as input currents
                input_current = np.zeros(n_neurons)
                for i, val in enumerate(pattern):
                    input_current[i] = val * 2.0  # Scale input
                
                # Add some noise to make the task more challenging
                input_current += np.random.randn(n_neurons) * 0.1
                
                # Record spikes for STDP
                for neuron_id in range(n_neurons):
                    if network.membrane_potentials[neuron_id] >= 1.0:  # Threshold
                        network.neuroplasticity_engine.record_spike(int(trial * duration_per_trial + t), neuron_id)
                
                # Compute reward signal (dopamine) based on network output
                # Check if the correct output neuron is most active
                output_potentials = network.membrane_potentials[output_neurons]
                predicted_output = np.argmax(output_potentials)
                
                # Reward (dopamine increase) if prediction is correct
                dopamine_level = 1.0  # Baseline
                if predicted_output == desired_output:
                    dopamine_level = 2.0  # Higher dopamine as reward
                
                # Apply neuromodulation
                neuromodulator_inputs = {
                    "dopamine": dopamine_level,
                    "serotonin": 1.0,
                    "norepinephrine": 1.0,
                    "acetylcholine": 1.0
                }
                network.neuromodulation_system.update(neuromodulator_inputs, dt)
                
                # Get modulated learning rate
                modulations = network.neuromodulation_system.compute_modulations()
                learning_rate_mod = modulations["learning"]
                
                # Update neuroplasticity engine's learning rate
                network.neuroplasticity_engine.set_learning_rate(
                    network.neuroplasticity_engine.base_learning_rate * learning_rate_mod
                )
                
                # Update network
                network.update(input_current, dt)
                
                # Apply STDP every 50ms
                if step % 50 == 0 and step > 0:
                    # Get current weights
                    weights = network.connections.toarray()
                    
                    # Apply STDP
                    weights = network.neuroplasticity_engine.apply_stdp(weights)
                    
                    # Update network weights
                    network.connections = scipy.sparse.csr_matrix(weights)
                
                # Record state at the end of each trial
                if step == int(duration_per_trial / dt) - 1:
                    recorder.record_state(network, trial * duration_per_trial + t)
                    
                    # Log performance
                    if trial % 10 == 0:
                        logger.info(f"Trial {trial}, Pattern {pattern_idx}, " +
                                   f"Prediction {predicted_output}, " +
                                   f"Correct {desired_output}, " +
                                   f"Reward {dopamine_level}")
        
        # Test the network after training
        n_test_trials = 20
        correct_predictions = 0
        
        for test_trial in range(n_test_trials):
            pattern_idx = test_trial % 2  # Alternate between patterns
            pattern = patterns[pattern_idx]
            desired_output = desired_outputs[pattern_idx]
            
            # Present the pattern as input
            input_current = np.zeros(n_neurons)
            for i, val in enumerate(pattern):
                input_current[i] = val * 2.0
            
            # Reset membrane potentials
            network.membrane_potentials = np.zeros(n_neurons)
            
            # Run for 100ms
            for step in range(100):
                network.update(input_current, dt)
            
            # Check output
            output_potentials = network.membrane_potentials[output_neurons]
            predicted_output = np.argmax(output_potentials)
            
            if predicted_output == desired_output:
                correct_predictions += 1
        
        # Calculate accuracy
        accuracy = correct_predictions / n_test_trials
        logger.info(f"Test accuracy: {accuracy * 100:.2f}%")
        
        # The network should learn this simple task with reasonable accuracy
        assert accuracy > 0.7, f"Network should achieve at least 70% accuracy, got {accuracy * 100:.2f}%"
    
    def test_save_load_full_system(self, test_network, tmp_path):
        """Test saving and loading the full system state"""
        network = test_network
        
        # Modify network state to ensure changes are captured
        
        # 1. Change membrane potentials
        network.membrane_potentials = np.random.rand(network.total_neurons)
        
        # 2. Modify weights
        weights = network.connections.toarray()
        weights = weights * 0.5 + 0.25  # Rescale to [0.25, 0.75]
        network.connections = scipy.sparse.csr_matrix(weights)
        
        # 3. Set neuromodulator levels
        network.neuromodulation_system.modulators["dopamine"].level = 1.5
        network.neuromodulation_system.modulators["serotonin"].level = 0.8
        
        # 4. Modify oscillator phases
        network.biological_timing.oscillators["theta"].phase = np.pi/3
        network.biological_timing.oscillators["gamma"].phase = np.pi/2
        
        # Save state to a file
        state_file = tmp_path / "full_network_state.npz"
        network.save_state(state_file)
        
        # Create a new network
        new_network = NeuromorphicCore(dimensions=network.dimensions, neuron_types=network.neuron_types)
        
        # Attach components (same as the test_network fixture)
        neuroplasticity_engine = NeuroplasticityEngine(eta=0.01, alpha=0.001)
        
        pruning_module = SynapticPruningModule(
            theta_prune=0.1, theta_usage=0.1, pruning_rate=0.01
        )
        
        neuromodulation_system = NeuromodulationSystem(
            p_baseline={
                "dopamine": 1.0,
                "serotonin": 1.0,
                "norepinephrine": 1.0,
                "acetylcholine": 1.0
            },
            delta_p={
                "learning": 0.5,
                "threshold": 0.3,
                "gain": 0.4,
                "adaptation": 0.2
            },
            tau_m=100.0
        )
        
        biological_timing = BiologicalTimingCircuits(
            {
                "delta": {"frequency": 2.0, "amplitude": 1.0, "phase": 0.0},
                "theta": {"frequency": 6.0, "amplitude": 1.0, "phase": 0.0},
                "alpha": {"frequency": 10.0, "amplitude": 1.0, "phase": 0.0},
                "beta": {"frequency": 20.0, "amplitude": 1.0, "phase": 0.0},
                "gamma": {"frequency": 40.0, "amplitude": 1.0, "phase": 0.0}
            },
            {
                "theta_gamma": 0.5,
                "alpha_beta": 0.3
            }
        )
        
        new_network.set_neuroplasticity_engine(neuroplasticity_engine)
        new_network.set_synaptic_pruning_module(pruning_module)
        new_network.set_neuromodulation_system(neuromodulation_system)
        new_network.set_biological_timing_circuits(biological_timing)
        
        # Load the saved state
        new_network.load_state(state_file)
        
        # Check that core state variables match
        assert np.allclose(network.membrane_potentials, new_network.membrane_potentials)
        assert np.array_equal(network.types, new_network.types)
        assert np.allclose(network.positions, new_network.positions)
        assert np.allclose(network.connections.toarray(), new_network.connections.toarray())
        
        # Check that neuromodulator levels match
        assert network.neuromodulation_system.modulators["dopamine"].level == \
               new_network.neuromodulation_system.modulators["dopamine"].level
               
        assert network.neuromodulation_system.modulators["serotonin"].level == \
               new_network.neuromodulation_system.modulators["serotonin"].level
        
        # Check that oscillator phases match
        assert network.biological_timing.oscillators["theta"].phase == \
               new_network.biological_timing.oscillators["theta"].phase
               
        assert network.biological_timing.oscillators["gamma"].phase == \
               new_network.biological_timing.oscillators["gamma"].phase