#!/usr/bin/env python3
"""
ULTRA: Ultimate Learning & Thought Reasoning Architecture
Core Neural Architecture - Visualization Examples

This module demonstrates how to use the visualization utilities to analyze and understand
the behavior of Core Neural Architecture components through practical examples.
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
import logging
import argparse
from typing import Dict, List, Tuple, Union, Optional, Any

# Ensure the test module is in the path
ROOT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if ROOT_DIR not in sys.path:
    sys.path.insert(0, ROOT_DIR)

# Import visualization utilities
from tests.test_core_neural.visualization_utils import (
    NetworkVisualizer, PlasticityVisualizer, NeuromodulationVisualizer, OscillationVisualizer
)

# Import ULTRA components for generating sample data
from ultra.core_neural.neuromorphic_core import NeuromorphicCore
from ultra.core_neural.neuroplasticity_engine import NeuroplasticityEngine
from ultra.core_neural.synaptic_pruning import SynapticPruningModule
from ultra.core_neural.neuromodulation import NeuromodulationSystem
from ultra.core_neural.biological_timing import BiologicalTimingCircuits

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def create_output_directory(base_dir: str = "visualization_examples") -> str:
    """Create and return the path to an output directory for saving visualizations"""
    # Create output directory
    output_dir = os.path.join(os.path.dirname(__file__), base_dir)
    os.makedirs(output_dir, exist_ok=True)
    
    # Create timestamp subfolder to keep runs separate
    import datetime
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    run_dir = os.path.join(output_dir, timestamp)
    os.makedirs(run_dir, exist_ok=True)
    
    logger.info(f"Visualizations will be saved to: {run_dir}")
    return run_dir

def example_network_structure_visualizations(output_dir: str):
    """
    Example: Visualizing the structure of a neural network
    """
    logger.info("Generating network structure visualizations...")
    
    # Create a small network for visualization
    dimensions = (5, 5, 5)  # 125 neurons
    network = NeuromorphicCore(dimensions=dimensions, neuron_types=4)
    
    # Get network properties
    positions = network.positions
    connections = network.connections.toarray()
    neuron_types = network.types
    
    # Create weight matrix (random for demonstration)
    weights = np.random.rand(*connections.shape) * connections
    
    # 1. Visualize the 3D network structure
    NetworkVisualizer.plot_3d_network(
        positions=positions,
        connections=connections,
        neuron_types=neuron_types,
        title="3D Neural Network Structure",
        save_path=os.path.join(output_dir, "3d_network_structure.png"),
        show=False
    )
    
    # 2. Visualize the connectivity matrix
    NetworkVisualizer.plot_connectivity_matrix(
        connections=weights,
        neuron_types=neuron_types,
        title="Neural Connectivity Matrix",
        save_path=os.path.join(output_dir, "connectivity_matrix.png"),
        show=False
    )
    
    # 3. Visualize the weight distribution
    NetworkVisualizer.plot_weight_distribution(
        weights=weights,
        neuron_types=neuron_types,
        title="Synaptic Weight Distribution",
        save_path=os.path.join(output_dir, "weight_distribution.png"),
        show=False
    )
    
    # 4. Visualize network with highlighted neurons
    # Highlight neurons of type 2 (adaptive neurons)
    highlight_neurons = [i for i, t in enumerate(neuron_types) if t == 2]
    
    NetworkVisualizer.plot_3d_network(
        positions=positions,
        connections=connections,
        neuron_types=neuron_types,
        highlight_neurons=highlight_neurons,
        title="Network with Highlighted Adaptive Neurons",
        save_path=os.path.join(output_dir, "network_highlighted_neurons.png"),
        show=False
    )
    
    logger.info("Network structure visualizations complete.")

def example_neuron_activity_visualizations(output_dir: str):
    """
    Example: Visualizing neural activity over time
    """
    logger.info("Generating neuron activity visualizations...")
    
    # Create a small network for simulation
    dimensions = (3, 3, 3)  # 27 neurons
    network = NeuromorphicCore(dimensions=dimensions, neuron_types=4)
    
    # Simulation parameters
    duration = 500  # ms
    dt = 1.0  # ms
    steps = int(duration / dt)
    
    # Generate input pattern (random for demonstration)
    input_current = np.zeros((network.total_neurons, steps))
    
    # Add some structured input
    for t in range(steps):
        # Oscillatory input to first 5 neurons
        for i in range(5):
            input_current[i, t] = 0.5 + 0.5 * np.sin(2 * np.pi * t / 100)
        
        # Pulse input to neurons 5-10
        if 200 <= t < 300:
            for i in range(5, 10):
                input_current[i, t] = 1.5
    
    # Record membrane potentials and spikes
    membrane_potentials = []
    spike_times = {}
    time_points = []
    
    # Reset network state
    network.membrane_potentials = np.zeros(network.total_neurons)
    
    # Run simulation
    for t in range(steps):
        # Apply input for this timestep
        current_input = input_current[:, t]
        
        # Update network
        network.update(current_input, dt)
        
        # Record state
        membrane_potentials.append(network.membrane_potentials.copy())
        time_points.append(t * dt)
        
        # Record spikes
        for i, v in enumerate(network.membrane_potentials):
            if v >= 1.0:  # Threshold for spike
                if i not in spike_times:
                    spike_times[i] = []
                spike_times[i].append(t * dt)
    
    # 1. Visualize membrane potentials and spikes for selected neurons
    selected_neurons = [0, 1, 5, 6, 10]  # Select a few neurons to visualize
    
    NetworkVisualizer.plot_neuron_activity(
        membrane_potentials=membrane_potentials,
        spike_times=spike_times,
        time_points=time_points,
        neuron_indices=selected_neurons,
        title="Neuron Activity: Membrane Potentials and Spikes",
        save_path=os.path.join(output_dir, "neuron_activity.png"),
        show=False
    )
    
    # 2. Visualize network activity heatmap
    NetworkVisualizer.plot_network_activity_heatmap(
        membrane_potentials=membrane_potentials,
        time_points=time_points,
        neuron_types=network.types,
        title="Network Activity Heatmap",
        save_path=os.path.join(output_dir, "network_activity_heatmap.png"),
        show=False
    )
    
    # 3. Create network activity animation
    # (commented out to avoid long processing time, but can be uncommented for demonstrations)
    """
    NetworkVisualizer.create_network_animation(
        membrane_potentials=membrane_potentials,
        positions=network.positions,
        time_points=time_points,
        connections=network.connections,
        neuron_types=network.types,
        title="Neural Network Activity Animation",
        save_path=os.path.join(output_dir, "network_activity_animation.mp4"),
        fps=30,
        show=False,
        subsample=5  # Subsample for faster animation
    )
    """
    
    logger.info("Neuron activity visualizations complete.")

def example_plasticity_visualizations(output_dir: str):
    """
    Example: Visualizing plasticity mechanisms
    """
    logger.info("Generating plasticity visualizations...")
    
    # 1. Visualize STDP learning window
    PlasticityVisualizer.plot_stdp_window(
        A_plus=0.1,
        A_minus=-0.1,
        tau_plus=20.0,
        tau_minus=20.0,
        title="STDP Learning Window",
        save_path=os.path.join(output_dir, "stdp_learning_window.png"),
        show=False
    )
    
    # Create a small network for plasticity simulation
    dimensions = (3, 3, 3)  # 27 neurons
    network = NeuromorphicCore(dimensions=dimensions, neuron_types=4)
    
    # Add neuroplasticity
    plasticity_engine = NeuroplasticityEngine(eta=0.01, alpha=0.001)
    network.set_neuroplasticity_engine(plasticity_engine)
    
    # Simulation parameters
    duration = 1000  # ms
    dt = 1.0  # ms
    steps = int(duration / dt)
    
    # Initial weights
    weights = network.connections.toarray().copy()
    
    # Generate paired spike patterns for STDP
    for i in range(10):  # Repeat 10 times
        # Pre-before-post for neurons 0->1 (potentiation)
        t_pre = 100 + i * 100
        t_post = t_pre + 10  # 10ms after pre (potentiation)
        plasticity_engine.record_spike(t_pre, 0)
        plasticity_engine.record_spike(t_post, 1)
        
        # Post-before-pre for neurons 2->3 (depression)
        t_pre = 100 + i * 100
        t_post = t_pre - 10  # 10ms before pre (depression)
        plasticity_engine.record_spike(t_pre, 2)
        plasticity_engine.record_spike(t_post, 3)
    
    # Apply STDP every 100ms
    weight_history = [weights.copy()]
    
    for t in range(100, steps, 100):
        # Apply STDP
        weights = plasticity_engine.apply_stdp(weights)
        weight_history.append(weights.copy())
    
    # 2. Visualize weight evolution
    connection_indices = [(0, 1), (1, 0), (2, 3), (3, 2)]
    time_points = list(range(0, steps, 100))
    
    PlasticityVisualizer.plot_weight_evolution(
        weight_history=weight_history,
        connection_indices=connection_indices,
        time_points=time_points,
        title="Synaptic Weight Evolution with STDP",
        save_path=os.path.join(output_dir, "weight_evolution.png"),
        show=False
    )
    
    # 3. Visualize weight change heatmap
    PlasticityVisualizer.plot_weight_change_heatmap(
        initial_weights=weight_history[0],
        final_weights=weight_history[-1],
        neuron_types=network.types,
        title="Synaptic Weight Changes",
        save_path=os.path.join(output_dir, "weight_change_heatmap.png"),
        show=False
    )
    
    # 4. Visualize pruning analysis
    # Create artificial connectivity changes for demonstration
    initial_connectivity = weight_history[0] > 0
    
    # Randomly prune some connections
    pruned = np.random.rand(*initial_connectivity.shape) < 0.1
    pruned = pruned & initial_connectivity
    
    # Randomly add some new connections
    new_connections = np.random.rand(*initial_connectivity.shape) < 0.05
    new_connections = new_connections & ~initial_connectivity
    
    # Final connectivity
    final_connectivity = (initial_connectivity & ~pruned) | new_connections
    
    # Create usage matrix (random for demonstration)
    usage = np.random.rand(*weights.shape)
    usage[~initial_connectivity] = 0
    
    PlasticityVisualizer.plot_pruning_analysis(
        initial_connectivity=initial_connectivity,
        final_connectivity=final_connectivity,
        weights=weights,
        usage=usage,
        title="Synaptic Pruning Analysis",
        save_path=os.path.join(output_dir, "pruning_analysis.png"),
        show=False
    )
    
    logger.info("Plasticity visualizations complete.")

def example_neuromodulation_visualizations(output_dir: str):
    """
    Example: Visualizing neuromodulation dynamics
    """
    logger.info("Generating neuromodulation visualizations...")
    
    # Create neuromodulation system
    system = NeuromodulationSystem(
        p_baseline={
            "dopamine": 1.0,
            "serotonin": 1.0,
            "norepinephrine": 1.0,
            "acetylcholine": 1.0
        },
        delta_p={
            "learning": 0.5,
            "threshold": 0.3,
            "gain": 0.4,
            "adaptation": 0.2
        },
        tau_m=100.0
    )
    
    # Simulation parameters
    duration = 1000  # ms
    dt = 1.0  # ms
    steps = int(duration / dt)
    time_points = np.arange(0, duration, dt)
    
    # Generate neuromodulator inputs
    dopamine_input = np.ones(steps)
    serotonin_input = np.ones(steps)
    norepinephrine_input = np.ones(steps)
    acetylcholine_input = np.ones(steps)
    
    # Add some dynamics for visualization
    # Dopamine: reward response
    reward_times = [200, 500, 800]
    for t in reward_times:
        idx = int(t / dt)
        dopamine_input[idx:idx+100] = 2.0
        dopamine_input[idx+100:] = np.maximum(
            dopamine_input[idx+100:],
            1.0 + 1.0 * np.exp(-np.arange(steps-idx-100) / 100)
        )
    
    # Serotonin: slow increase then return to baseline
    serotonin_input[300:600] = np.linspace(1.0, 1.5, 300)
    serotonin_input[600:900] = np.linspace(1.5, 1.0, 300)
    
    # Norepinephrine: oscillatory attention pattern
    norepinephrine_input = 1.0 + 0.5 * np.sin(2 * np.pi * time_points / 300)
    
    # Acetylcholine: step increase
    acetylcholine_input[500:] = 1.3
    
    # Run simulation
    modulator_levels = {
        "dopamine": [],
        "serotonin": [],
        "norepinephrine": [],
        "acetylcholine": []
    }
    
    parameter_modulations = {
        "learning": [],
        "threshold": [],
        "gain": [],
        "adaptation": []
    }
    
    # Reset neuromodulator levels
    for modulator in system.modulators.values():
        modulator.level = modulator.baseline
    
    # Run simulation
    for t in range(steps):
        # Apply input
        input_signals = {
            "dopamine": dopamine_input[t],
            "serotonin": serotonin_input[t],
            "norepinephrine": norepinephrine_input[t],
            "acetylcholine": acetylcholine_input[t]
        }
        
        # Update system
        system.update(input_signals, dt)
        
        # Get modulations
        modulations = system.compute_modulations()
        
        # Record state
        for modulator, mod_obj in system.modulators.items():
            modulator_levels[modulator].append(mod_obj.level)
            
        for param, value in modulations.items():
            parameter_modulations[param].append(value)
    
    # 1. Visualize neuromodulator levels
    NeuromodulationVisualizer.plot_neuromodulator_levels(
        time_points=time_points,
        modulator_levels=modulator_levels,
        title="Neuromodulator Levels Over Time",
        save_path=os.path.join(output_dir, "neuromodulator_levels.png"),
        show=False
    )
    
    # 2. Visualize modulation effects
    NeuromodulationVisualizer.plot_modulation_effects(
        time_points=time_points,
        modulator_levels=modulator_levels,
        parameter_modulations=parameter_modulations,
        title="Neuromodulation Effects on Parameters",
        save_path=os.path.join(output_dir, "modulation_effects.png"),
        show=False
    )
    
    # 3. Visualize dopamine reward response
    NeuromodulationVisualizer.plot_dopamine_reward_response(
        time_points=time_points,
        dopamine_levels=modulator_levels["dopamine"],
        reward_times=reward_times,
        learning_modulation=parameter_modulations["learning"],
        title="Dopamine Reward Response and Learning Modulation",
        save_path=os.path.join(output_dir, "dopamine_reward_response.png"),
        show=False
    )
    
    logger.info("Neuromodulation visualizations complete.")

def example_oscillation_visualizations(output_dir: str):
    """
    Example: Visualizing neural oscillations and timing circuits
    """
    logger.info("Generating oscillation visualizations...")
    
    # Create biological timing circuits
    circuits = BiologicalTimingCircuits(
        {
            "delta": {"frequency": 2.0, "amplitude": 1.0, "phase": 0.0},
            "theta": {"frequency": 6.0, "amplitude": 1.0, "phase": 0.0},
            "alpha": {"frequency": 10.0, "amplitude": 1.0, "phase": 0.0},
            "beta": {"frequency": 20.0, "amplitude": 1.0, "phase": 0.0},
            "gamma": {"frequency": 40.0, "amplitude": 1.0, "phase": 0.0}
        },
        {
            "theta_gamma": 0.5,
            "alpha_beta": 0.3
        }
    )
    
    # Simulation parameters
    duration = 1000  # ms
    dt = 1.0  # ms
    steps = int(duration / dt)
    time_points = np.arange(0, duration, dt)
    
    # Run simulation
    oscillator_outputs = {
        "delta": [],
        "theta": [],
        "alpha": [],
        "beta": [],
        "gamma": []
    }
    
    oscillator_phases = {
        "delta": [],
        "theta": [],
        "alpha": [],
        "beta": [],
        "gamma": []
    }
    
    # Reset oscillator phases
    for oscillator in circuits.oscillators.values():
        oscillator.reset_phase(0.0)
    
    # Run simulation
    for t in range(steps):
        # Update circuits
        circuits.update(dt)
        
        # Get outputs
        outputs = circuits.get_outputs()
        
        # Get phases
        phases = {name: oscillator.phase for name, oscillator in circuits.oscillators.items()}
        
        # Record state
        for oscillator, output in outputs.items():
            oscillator_outputs[oscillator].append(output)
            oscillator_phases[oscillator].append(phases[oscillator])
    
    # 1. Visualize oscillator outputs
    OscillationVisualizer.plot_oscillations(
        time_points=time_points,
        oscillator_outputs=oscillator_outputs,
        title="Neural Oscillations",
        save_path=os.path.join(output_dir, "oscillator_outputs.png"),
        show=False
    )
    
    # 2. Visualize oscillator phases
    OscillationVisualizer.plot_oscillator_phases(
        time_points=time_points,
        oscillator_phases=oscillator_phases,
        title="Oscillator Phases",
        save_path=os.path.join(output_dir, "oscillator_phases.png"),
        show=False
    )
    
    # 3. Visualize phase-amplitude coupling
    OscillationVisualizer.plot_phase_amplitude_coupling(
        time_points=time_points,
        carrier_oscillator=("theta", oscillator_outputs["theta"]),
        modulated_oscillator=("gamma", oscillator_outputs["gamma"]),
        title="Theta-Gamma Phase-Amplitude Coupling",
        save_path=os.path.join(output_dir, "phase_amplitude_coupling.png"),
        show=False
    )
    
    # 4. Visualize spectrogram
    # Create a composite signal that includes all oscillations
    composite_signal = np.zeros(steps)
    for name, outputs in oscillator_outputs.items():
        composite_signal += np.array(outputs)
    
    # Add some noise
    composite_signal += np.random.normal(0, 0.1, steps)
    
    OscillationVisualizer.plot_spectrogram(
        time_points=time_points,
        signal=composite_signal,
        fs=1000.0,  # 1 kHz sampling rate
        title="Neural Oscillations Spectrogram",
        save_path=os.path.join(output_dir, "oscillations_spectrogram.png"),
        show=False
    )
    
    # 5. Visualize individual oscillator spectrograms
    OscillationVisualizer.plot_spectrogram(
        time_points=time_points,
        signal=oscillator_outputs,
        fs=1000.0,  # 1 kHz sampling rate
        title="Individual Oscillator Spectrograms",
        save_path=os.path.join(output_dir, "individual_spectrograms.png"),
        show=False
    )
    
    logger.info("Oscillation visualizations complete.")

def main(args):
    """Main function to run all or selected examples"""
    # Create output directory
    output_dir = create_output_directory(args.output_dir)
    
    # Run examples
    all_examples = True
    
    if args.network or all_examples:
        example_network_structure_visualizations(output_dir)
    
    if args.activity or all_examples:
        example_neuron_activity_visualizations(output_dir)
    
    if args.plasticity or all_examples:
        example_plasticity_visualizations(output_dir)
    
    if args.neuromodulation or all_examples:
        example_neuromodulation_visualizations(output_dir)
    
    if args.oscillation or all_examples:
        example_oscillation_visualizations(output_dir)
    
    logger.info("All visualizations completed successfully!")
    logger.info(f"Output directory: {output_dir}")
    
    if args.show:
        logger.info("Opening file browser to output directory...")
        import webbrowser
        webbrowser.open(f"file://{os.path.abspath(output_dir)}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Generate visualization examples for ULTRA Core Neural Architecture")
    parser.add_argument("--output-dir", type=str, default="visualization_examples", help="Output directory for visualizations")
    parser.add_argument("--show", action="store_true", help="Open file browser to output directory when done")
    
    # Example selection options
    parser.add_argument("--network", action="store_true", help="Run network structure visualization examples")
    parser.add_argument("--activity", action="store_true", help="Run neuron activity visualization examples")
    parser.add_argument("--plasticity", action="store_true", help="Run plasticity visualization examples")
    parser.add_argument("--neuromodulation", action="store_true", help="Run neuromodulation visualization examples")
    parser.add_argument("--oscillation", action="store_true", help="Run oscillation visualization examples")
    
    args = parser.parse_args()
    
    # If no specific examples are selected, run all
    all_examples = not (args.network or args.activity or args.plasticity or args.neuromodulation or args.oscillation)
    
    # Run main function
    main(args)