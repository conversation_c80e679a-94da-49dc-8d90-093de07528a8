#!/usr/bin/env python3
"""
ULTRA: Ultimate Learning & Thought Reasoning Architecture
Core Neural Architecture - Performance Tests

This module contains performance benchmarks for the Core Neural Architecture components,
measuring their computational efficiency across various scales and configurations.
"""

import os
import sys
import numpy as np
import pytest
import scipy.sparse
import time
import logging
from typing import Dict, List, Tuple, Union, Callable, Optional

# Ensure the test module is in the path
ROOT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if ROOT_DIR not in sys.path:
    sys.path.insert(0, ROOT_DIR)

# Import test configuration and utilities
from tests.test_core_neural import (
    neuromorphic_core, neuroplasticity_engine, synaptic_pruning_module,
    neuromodulation_system, biological_timing_circuits,
    TestUtils
)

# Import modules to test
from ultra.core_neural.neuromorphic_core import NeuromorphicCore
from ultra.core_neural.neuroplasticity_engine import NeuroplasticityEngine
from ultra.core_neural.synaptic_pruning import SynapticPruningModule
from ultra.core_neural.neuromodulation import NeuromodulationSystem
from ultra.core_neural.biological_timing import BiologicalTimingCircuits

# Configure logging
logger = logging.getLogger(__name__)

# Mark all tests in this file as performance
pytestmark = pytest.mark.performance

@pytest.fixture(params=[
    (10, 10, 1),    # 100 neurons
    (10, 10, 10),   # 1,000 neurons
    (20, 20, 20),   # 8,000 neurons
])
def network_sizes(request):
    """Fixture providing different network sizes for scaling tests"""
    return request.param

class TestNeuromorphicCorePerformance:
    """Performance benchmarks for the Neuromorphic Core"""
    
    @pytest.mark.slow
    def test_initialization_performance(self, network_sizes):
        """Benchmark initialization time for different network sizes"""
        dimensions = network_sizes
        total_neurons = dimensions[0] * dimensions[1] * dimensions[2]
        
        # Measure initialization time
        start_time = time.time()
        core = NeuromorphicCore(dimensions=dimensions, neuron_types=4)
        end_time = time.time()
        
        initialization_time = end_time - start_time
        
        # Log performance metrics
        logger.info(f"Neuromorphic Core initialization: {total_neurons} neurons in {initialization_time:.3f} seconds")
        logger.info(f"Per neuron initialization time: {initialization_time/total_neurons*1000:.3f} ms")
        
        # Initialization time should scale approximately linearly with neuron count
        # This is a very basic performance check - more sophisticated checks can be added
        assert initialization_time < total_neurons * 0.001, \
            f"Initialization time {initialization_time:.3f}s exceeds linear scaling threshold for {total_neurons} neurons"
    
    @pytest.mark.slow
    def test_update_performance(self, network_sizes):
        """Benchmark update performance for different network sizes"""
        dimensions = network_sizes
        total_neurons = dimensions[0] * dimensions[1] * dimensions[2]
        
        # Create network
        core = NeuromorphicCore(dimensions=dimensions, neuron_types=4)
        
        # Prepare input
        input_current = np.random.rand(total_neurons)
        dt = 1.0  # ms
        
        # Warm-up run
        core.update(input_current, dt)
        
        # Benchmark update time
        n_updates = 100
        start_time = time.time()
        for _ in range(n_updates):
            core.update(input_current, dt)
        end_time = time.time()
        
        update_time = (end_time - start_time) / n_updates
        
        # Log performance metrics
        logger.info(f"Neuromorphic Core update: {total_neurons} neurons in {update_time*1000:.3f} ms per step")
        logger.info(f"Per neuron update time: {update_time*1000/total_neurons:.6f} ms")
        
        # Update time should scale approximately linearly with neuron count
        # This threshold might need adjustment based on the implementation
        assert update_time < total_neurons * 0.0001, \
            f"Update time {update_time*1000:.3f}ms exceeds linear scaling threshold for {total_neurons} neurons"
    
    @pytest.mark.slow
    def test_connectivity_sparsity_performance(self):
        """Benchmark update performance with different connectivity sparsity levels"""
        # Fixed network size
        dimensions = (20, 20, 5)  # 2,000 neurons
        total_neurons = dimensions[0] * dimensions[1] * dimensions[2]
        
        # Test different connection probabilities
        connection_probs = [0.01, 0.05, 0.1, 0.2]
        update_times = []
        
        for prob in connection_probs:
            # Create network with custom connection probability
            core = NeuromorphicCore(dimensions=dimensions, neuron_types=4, connection_prob=prob)
            
            # Prepare input
            input_current = np.random.rand(total_neurons)
            dt = 1.0  # ms
            
            # Warm-up run
            core.update(input_current, dt)
            
            # Benchmark update time
            n_updates = 50
            start_time = time.time()
            for _ in range(n_updates):
                core.update(input_current, dt)
            end_time = time.time()
            
            update_time = (end_time - start_time) / n_updates
            update_times.append(update_time)
            
            # Log performance metrics
            logger.info(f"Connectivity {prob*100:.1f}%: Update time {update_time*1000:.3f} ms")
            
            # Calculate actual connectivity
            connectivity = core.connections.count_nonzero() / (total_neurons * total_neurons)
            logger.info(f"Actual connectivity: {connectivity*100:.2f}%")
        
        # Update time should increase with connectivity
        assert all(update_times[i] <= update_times[i+1] * 1.2 for i in range(len(update_times)-1)), \
            "Update time should increase with connectivity"

class TestNeuroplasticityEnginePerformance:
    """Performance benchmarks for the Neuroplasticity Engine"""
    
    @pytest.mark.slow
    def test_stdp_performance(self):
        """Benchmark STDP performance with different network sizes"""
        # Test different network sizes
        network_sizes = [100, 1000, 5000]
        stdp_times = []
        
        for size in network_sizes:
            # Create neuroplasticity engine
            engine = NeuroplasticityEngine(eta=0.01, alpha=0.001)
            
            # Create random weights
            weights = np.random.rand(size, size) * 0.5
            np.fill_diagonal(weights, 0)  # No self-connections
            
            # Generate random spikes
            n_spikes = size * 10  # 10 spikes per neuron on average
            for _ in range(n_spikes):
                neuron_id = np.random.randint(0, size)
                spike_time = np.random.randint(0, 1000)
                engine.record_spike(spike_time, neuron_id)
            
            # Warm-up run
            engine.apply_stdp(weights)
            
            # Benchmark STDP time
            start_time = time.time()
            engine.apply_stdp(weights)
            end_time = time.time()
            
            stdp_time = end_time - start_time
            stdp_times.append(stdp_time)
            
            # Log performance metrics
            logger.info(f"STDP for {size}x{size} weights: {stdp_time:.3f} seconds")
            logger.info(f"Per weight STDP time: {stdp_time/(size*size)*1000000:.3f} µs")
        
        # STDP time should scale with network size, but sub-quadratically due to optimizations
        # This is a very basic check
        assert stdp_times[1] < stdp_times[0] * (network_sizes[1]/network_sizes[0])**2, \
            "STDP time scaling worse than quadratic"
        assert stdp_times[2] < stdp_times[1] * (network_sizes[2]/network_sizes[1])**2, \
            "STDP time scaling worse than quadratic"
    
    @pytest.mark.slow
    def test_structural_plasticity_performance(self):
        """Benchmark structural plasticity performance"""
        # Test different network sizes
        network_sizes = [100, 500, 1000]
        
        for size in network_sizes:
            # Create neuroplasticity engine
            engine = NeuroplasticityEngine(eta=0.01, alpha=0.001)
            
            # Create connectivity, weights, and correlation matrix
            connectivity = np.random.rand(size, size) < 0.3
            np.fill_diagonal(connectivity, False)
            
            weights = np.random.rand(size, size) * 0.5
            weights[~connectivity] = 0
            
            activity_correlation = np.random.rand(size, size) * 0.5
            np.fill_diagonal(activity_correlation, 1.0)
            
            # Make correlation matrix symmetric
            activity_correlation = (activity_correlation + activity_correlation.T) / 2
            
            # Create usage matrix
            usage = np.random.rand(size, size) * 0.5
            usage[~connectivity] = 0
            
            # Warm-up run
            engine.apply_structural_plasticity(connectivity, weights, activity_correlation, usage)
            
            # Benchmark structural plasticity time
            start_time = time.time()
            engine.apply_structural_plasticity(connectivity, weights, activity_correlation, usage)
            end_time = time.time()
            
            structural_time = end_time - start_time
            
            # Log performance metrics
            logger.info(f"Structural plasticity for {size}x{size} network: {structural_time:.3f} seconds")
            logger.info(f"Per neuron structural plasticity time: {structural_time/size*1000:.3f} ms")
        
        # This test doesn't assert specific performance requirements
        # It's primarily for logging and tracking performance over time

class TestSynapticPruningPerformance:
    """Performance benchmarks for the Synaptic Pruning Module"""
    
    @pytest.mark.slow
    def test_pruning_performance(self):
        """Benchmark pruning performance with different network sizes"""
        # Test different network sizes
        network_sizes = [100, 500, 1000]
        pruning_times = []
        
        for size in network_sizes:
            # Create pruning module
            module = SynapticPruningModule(theta_prune=0.1, theta_usage=0.1, pruning_rate=0.01)
            
            # Create weights, usage, and correlation matrices
            weights = np.random.rand(size, size) * 0.5
            np.fill_diagonal(weights, 0)
            
            usage = np.random.rand(size, size) * 0.5
            np.fill_diagonal(usage, 0)
            
            spike_corr = np.random.rand(size, size) * 0.5
            np.fill_diagonal(spike_corr, 1.0)
            # Make symmetric
            spike_corr = (spike_corr + spike_corr.T) / 2
            
            connectivity = np.ones((size, size), dtype=bool)
            np.fill_diagonal(connectivity, False)
            
            # Warm-up run
            module.prune(weights, usage, spike_corr, connectivity)
            
            # Benchmark pruning time
            start_time = time.time()
            module.prune(weights, usage, spike_corr, connectivity)
            end_time = time.time()
            
            pruning_time = end_time - start_time
            pruning_times.append(pruning_time)
            
            # Log performance metrics
            logger.info(f"Pruning for {size}x{size} network: {pruning_time:.3f} seconds")
            logger.info(f"Per connection pruning time: {pruning_time/(size*size)*1000000:.3f} µs")
        
        # Basic scaling check
        assert pruning_times[1] < pruning_times[0] * (network_sizes[1]/network_sizes[0])**2.2, \
            "Pruning time scaling worse than expected"
        assert pruning_times[2] < pruning_times[1] * (network_sizes[2]/network_sizes[1])**2.2, \
            "Pruning time scaling worse than expected"

class TestNeuromodulationPerformance:
    """Performance benchmarks for the Neuromodulation System"""
    
    def test_update_performance(self):
        """Benchmark neuromodulation update performance"""
        # Create neuromodulation system
        system = NeuromodulationSystem(
            p_baseline={
                "dopamine": 1.0,
                "serotonin": 1.0,
                "norepinephrine": 1.0,
                "acetylcholine": 1.0
            },
            delta_p={
                "learning": 0.5,
                "threshold": 0.3,
                "gain": 0.4,
                "adaptation": 0.2
            },
            tau_m=100.0
        )
        
        # Input signals
        input_signals = {
            "dopamine": 1.5,
            "serotonin": 0.8,
            "norepinephrine": 1.2,
            "acetylcholine": 1.0
        }
        
        dt = 1.0  # ms
        
        # Warm-up run
        system.update(input_signals, dt)
        
        # Benchmark update time
        n_updates = 10000
        start_time = time.time()
        for _ in range(n_updates):
            system.update(input_signals, dt)
        end_time = time.time()
        
        update_time = (end_time - start_time) / n_updates
        
        # Log performance metrics
        logger.info(f"Neuromodulation update time: {update_time*1000000:.3f} µs per step")
        
        # Neuromodulation update should be very fast
        assert update_time < 0.0001, \
            f"Neuromodulation update time {update_time*1000000:.3f}µs exceeds expected threshold"
    
    def test_modulation_performance(self):
        """Benchmark parameter modulation performance"""
        # Create neuromodulation system
        system = NeuromodulationSystem(
            p_baseline={
                "dopamine": 1.0,
                "serotonin": 1.0,
                "norepinephrine": 1.0,
                "acetylcholine": 1.0
            },
            delta_p={
                "learning": 0.5,
                "threshold": 0.3,
                "gain": 0.4,
                "adaptation": 0.2
            },
            tau_m=100.0
        )
        
        # Warm-up
        system.compute_modulations()
        
        # Benchmark modulation calculation time
        n_computations = 10000
        start_time = time.time()
        for _ in range(n_computations):
            modulations = system.compute_modulations()
        end_time = time.time()
        
        computation_time = (end_time - start_time) / n_computations
        
        # Log performance metrics
        logger.info(f"Modulation computation time: {computation_time*1000000:.3f} µs per call")
        
        # Parameter modulation should be very fast
        assert computation_time < 0.0001, \
            f"Modulation computation time {computation_time*1000000:.3f}µs exceeds expected threshold"

class TestBiologicalTimingPerformance:
    """Performance benchmarks for the Biological Timing Circuits"""
    
    def test_oscillator_update_performance(self):
        """Benchmark oscillator update performance"""
        # Create biological timing circuits with different numbers of oscillators
        oscillator_counts = [5, 10, 20, 50, 100]
        update_times = []
        
        for count in oscillator_counts:
            # Create oscillator parameters
            oscillator_params = {}
            for i in range(count):
                freq = 1.0 + i  # Different frequency for each oscillator
                oscillator_params[f"osc_{i}"] = {
                    "frequency": freq,
                    "amplitude": 1.0,
                    "phase": 0.0
                }
            
            # Create minimal coupling parameters
            coupling_params = {}
            
            # Create circuits
            circuits = BiologicalTimingCircuits(oscillator_params, coupling_params)
            
            # Warm-up run
            circuits.update(1.0)
            
            # Benchmark update time
            n_updates = 10000
            start_time = time.time()
            for _ in range(n_updates):
                circuits.update(1.0)
            end_time = time.time()
            
            update_time = (end_time - start_time) / n_updates
            update_times.append(update_time)
            
            # Log performance metrics
            logger.info(f"Biological timing update with {count} oscillators: {update_time*1000000:.3f} µs per step")
            logger.info(f"Per oscillator update time: {update_time*1000000/count:.3f} µs")
        
        # Update time should scale approximately linearly with oscillator count
        # This is a basic check
        for i in range(len(oscillator_counts)-1):
            expected_ratio = oscillator_counts[i+1] / oscillator_counts[i]
            actual_ratio = update_times[i+1] / update_times[i]
            assert actual_ratio < expected_ratio * 1.5, \
                f"Update time increase from {oscillator_counts[i]} to {oscillator_counts[i+1]} oscillators " \
                f"({actual_ratio:.2f}x) significantly exceeds expected ratio ({expected_ratio:.2f}x)"
    
    def test_coupling_performance(self):
        """Benchmark oscillator coupling performance"""
        # Create biological timing circuits with different coupling configurations
        n_oscillators = 20
        coupling_densities = [0.0, 0.1, 0.3, 0.5, 1.0]  # Fraction of all possible couplings
        update_times = []
        
        for density in coupling_densities:
            # Create oscillator parameters
            oscillator_params = {}
            for i in range(n_oscillators):
                freq = 1.0 + i/2  # Different frequency for each oscillator
                oscillator_params[f"osc_{i}"] = {
                    "frequency": freq,
                    "amplitude": 1.0,
                    "phase": 0.0
                }
            
            # Create coupling parameters
            coupling_params = {}
            max_couplings = n_oscillators * (n_oscillators - 1) // 2  # Maximum possible couplings
            n_couplings = int(max_couplings * density)
            
            # Create random couplings
            for _ in range(n_couplings):
                i = np.random.randint(0, n_oscillators)
                j = np.random.randint(0, n_oscillators)
                if i != j:
                    coupling_key = f"osc_{i}_osc_{j}"
                    coupling_params[coupling_key] = 0.5  # Moderate coupling strength
            
            # Create circuits
            circuits = BiologicalTimingCircuits(oscillator_params, coupling_params)
            
            # Warm-up run
            circuits.update(1.0)
            
            # Benchmark update time
            n_updates = 5000
            start_time = time.time()
            for _ in range(n_updates):
                circuits.update(1.0)
            end_time = time.time()
            
            update_time = (end_time - start_time) / n_updates
            update_times.append(update_time)
            
            # Log performance metrics
            logger.info(f"Biological timing update with {density*100:.1f}% coupling density: {update_time*1000000:.3f} µs per step")
            logger.info(f"Actual number of couplings: {len(coupling_params)}")
        
        # Update time should increase with coupling density, but not too dramatically
        for i in range(len(coupling_densities)-1):
            assert update_times[i+1] > update_times[i], \
                "Update time should increase with coupling density"
            
            # Ratio check
            ratio = update_times[i+1] / update_times[i]
            density_increase = (coupling_densities[i+1] + 0.01) / (coupling_densities[i] + 0.01)  # Add small offset to avoid division by zero
            
            # Allow some increase, but not excessively (should be sublinear)
            if density_increase > 1.1:  # Only check if significant density increase
                assert ratio < density_increase, \
                    f"Update time increase ({ratio:.2f}x) exceeds coupling density increase ({density_increase:.2f}x)"

class TestFullSystemPerformance:
    """Performance benchmarks for the full Core Neural Architecture system"""
    
    @pytest.mark.slow
    def test_full_system_update_performance(self, test_network):
        """Benchmark full system update performance"""
        network = test_network
        n_neurons = network.total_neurons
        
        # Create random input
        input_current = np.random.rand(n_neurons) * 0.5
        dt = 1.0  # ms
        
        # Add some spikes to test neuroplasticity
        for i in range(20):
            neuron_id = np.random.randint(0, n_neurons)
            spike_time = np.random.randint(0, 100)
            network.neuroplasticity_engine.record_spike(spike_time, neuron_id)
        
        # Set up neuromodulation inputs
        neuromodulator_inputs = {
            "dopamine": 1.2,
            "serotonin": 0.9,
            "norepinephrine": 1.1,
            "acetylcholine": 1.0
        }
        
        # Warm-up run
        network.update(input_current, dt)
        network.neuromodulation_system.update(neuromodulator_inputs, dt)
        network.biological_timing.update(dt)
        
        # Benchmark individual component update times
        n_updates = 1000
        
        # Core update
        start_time = time.time()
        for _ in range(n_updates):
            network.update(input_current, dt)
        end_time = time.time()
        core_update_time = (end_time - start_time) / n_updates
        
        # Neuromodulation update
        start_time = time.time()
        for _ in range(n_updates):
            network.neuromodulation_system.update(neuromodulator_inputs, dt)
        end_time = time.time()
        neuromod_update_time = (end_time - start_time) / n_updates
        
        # Biological timing update
        start_time = time.time()
        for _ in range(n_updates):
            network.biological_timing.update(dt)
        end_time = time.time()
        timing_update_time = (end_time - start_time) / n_updates
        
        # STDP update (every 10 steps)
        weights = network.connections.toarray()
        start_time = time.time()
        for _ in range(n_updates // 10):
            weights = network.neuroplasticity_engine.apply_stdp(weights)
        end_time = time.time()
        stdp_update_time = (end_time - start_time) / (n_updates // 10)
        
        # Log performance metrics
        logger.info(f"Core update time: {core_update_time*1000:.3f} ms per step")
        logger.info(f"Neuromodulation update time: {neuromod_update_time*1000:.3f} ms per step")
        logger.info(f"Biological timing update time: {timing_update_time*1000:.3f} ms per step")
        logger.info(f"STDP update time: {stdp_update_time*1000:.3f} ms per step (every 10 steps)")
        
        # Full integrated update (all components)
        start_time = time.time()
        for i in range(n_updates):
            # Update biological timing
            network.biological_timing.update(dt)
            
            # Update neuromodulation
            network.neuromodulation_system.update(neuromodulator_inputs, dt)
            
            # Update neuroplasticity (STDP every 10 steps)
            if i % 10 == 0:
                weights = network.connections.toarray()
                weights = network.neuroplasticity_engine.apply_stdp(weights)
                network.connections = scipy.sparse.csr_matrix(weights)
            
            # Update core
            network.update(input_current, dt)
        end_time = time.time()
        
        full_update_time = (end_time - start_time) / n_updates
        
        # Log performance metrics
        logger.info(f"Full system update time: {full_update_time*1000:.3f} ms per step")
        logger.info(f"Per neuron full update time: {full_update_time*1000/n_neurons:.3f} ms")
        
        # Check that full update time is reasonable
        expected_time = core_update_time + neuromod_update_time + timing_update_time + stdp_update_time/10
        assert full_update_time < expected_time * 1.5, \
            f"Full update time ({full_update_time*1000:.3f}ms) significantly exceeds sum of component times ({expected_time*1000:.3f}ms)"
        
        # Basic performance requirement
        assert full_update_time < 0.05, \
            f"Full system update time {full_update_time*1000:.3f}ms exceeds 50ms threshold"

if __name__ == "__main__":
    # Allow running as a standalone script for more detailed performance analysis
    logging.basicConfig(level=logging.INFO)
    pytest.main(["-xvs", __file__])