#!/bin/bash
# ULTRA: Ultimate Learning & Thought Reasoning Architecture
# Core Neural Architecture Test Runner
#
# This script runs tests for the Core Neural Architecture components
# and generates test coverage reports.

# Set the working directory to the repository root
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
ROOT_DIR="$(dirname "$(dirname "$(dirname "$SCRIPT_DIR")")")"
cd "$ROOT_DIR"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
MAGENTA='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Print help message
function print_help {
    echo -e "${CYAN}ULTRA Core Neural Architecture Test Runner${NC}"
    echo ""
    echo "Usage: $0 [options]"
    echo ""
    echo "Options:"
    echo "  -h, --help            Show this help message"
    echo "  --all                 Run all tests"
    echo "  --unit                Run unit tests only"
    echo "  --integration         Run integration tests only"
    echo "  --performance         Run performance tests only"
    echo "  --coverage            Generate test coverage report"
    echo "  --visualize           Run visualization examples"
    echo "  --skip-slow           Skip slow tests"
    echo "  --verbosity=LEVEL     Set test verbosity (0-3)"
    echo "  --component=NAME      Test specific component"
    echo "                        (core, plasticity, pruning, neuromodulation, timing)"
    echo ""
    echo "Examples:"
    echo "  $0 --all              Run all tests"
    echo "  $0 --unit --coverage  Run unit tests with coverage"
    echo "  $0 --component=core   Test only the Neuromorphic Core"
    echo "  $0 --visualize        Run visualization examples"
    echo ""
}

# Default options
RUN_ALL=false
RUN_UNIT=false
RUN_INTEGRATION=false
RUN_PERFORMANCE=false
GENERATE_COVERAGE=false
RUN_VISUALIZE=false
SKIP_SLOW=false
VERBOSITY=1
COMPONENT=''

# Parse command line arguments
for arg in "$@"; do
    case $arg in
        -h|--help)
            print_help
            exit 0
            ;;
        --all)
            RUN_ALL=true
            ;;
        --unit)
            RUN_UNIT=true
            ;;
        --integration)
            RUN_INTEGRATION=true
            ;;
        --performance)
            RUN_PERFORMANCE=true
            ;;
        --coverage)
            GENERATE_COVERAGE=true
            ;;
        --visualize)
            RUN_VISUALIZE=true
            ;;
        --skip-slow)
            SKIP_SLOW=true
            ;;
        --verbosity=*)
            VERBOSITY="${arg#*=}"
            ;;
        --component=*)
            COMPONENT="${arg#*=}"
            ;;
        *)
            echo -e "${RED}Unknown option: $arg${NC}"
            print_help
            exit 1
            ;;
    esac
done

# Check if at least one test type is selected
if ! $RUN_ALL && ! $RUN_UNIT && ! $RUN_INTEGRATION && ! $RUN_PERFORMANCE && ! $RUN_VISUALIZE; then
    echo -e "${YELLOW}No test type selected, defaulting to --all${NC}"
    RUN_ALL=true
fi

# Setup verbosity flag
VERBOSE=""
case $VERBOSITY in
    0)
        VERBOSE=""
        ;;
    1)
        VERBOSE="-v"
        ;;
    2)
        VERBOSE="-vv"
        ;;
    3)
        VERBOSE="-vvs"
        ;;
    *)
        echo -e "${YELLOW}Invalid verbosity level: $VERBOSITY, defaulting to 1${NC}"
        VERBOSE="-v"
        ;;
esac

# Setup component filter
COMPONENT_FILTER=""
case $COMPONENT in
    core)
        COMPONENT_FILTER="test_neuromorphic_core.py"
        ;;
    plasticity)
        COMPONENT_FILTER="test_neuroplasticity_engine.py"
        ;;
    pruning)
        COMPONENT_FILTER="test_synaptic_pruning.py"
        ;;
    neuromodulation)
        COMPONENT_FILTER="test_neuromodulation.py"
        ;;
    timing)
        COMPONENT_FILTER="test_biological_timing.py"
        ;;
    integration)
        COMPONENT_FILTER="test_integration.py"
        ;;
    performance)
        COMPONENT_FILTER="test_performance.py"
        ;;
    '')
        COMPONENT_FILTER=""
        ;;
    *)
        echo -e "${RED}Invalid component: $COMPONENT${NC}"
        print_help
        exit 1
        ;;
esac

# Setup slow test filter
SLOW_FILTER=""
if $SKIP_SLOW; then
    SLOW_FILTER="-m 'not slow'"
fi

# Setup coverage command
COVERAGE_CMD=""
if $GENERATE_COVERAGE; then
    COVERAGE_CMD="--cov=ultra.core_neural --cov-report=term --cov-report=html:coverage_report"
fi

# Setup test type filters
UNIT_FILTER="-m 'not integration and not performance'"
INTEGRATION_FILTER="-m integration"
PERFORMANCE_FILTER="-m performance"

# Create the command
CMD="python -m pytest $VERBOSE $COVERAGE_CMD"

if [ -n "$COMPONENT_FILTER" ]; then
    CMD="$CMD ultra/ultra/tests/test_core_neural/$COMPONENT_FILTER"
else
    CMD="$CMD ultra/ultra/tests/test_core_neural/"
fi

if $RUN_ALL; then
    if $SKIP_SLOW; then
        CMD="$CMD $SLOW_FILTER"
    fi
else
    FILTERS=""
    
    if $RUN_UNIT; then
        FILTERS="$FILTERS $UNIT_FILTER"
    fi
    
    if $RUN_INTEGRATION; then
        if [ -n "$FILTERS" ]; then
            FILTERS="$FILTERS or $INTEGRATION_FILTER"
        else
            FILTERS="$INTEGRATION_FILTER"
        fi
    fi
    
    if $RUN_PERFORMANCE; then
        if [ -n "$FILTERS" ]; then
            FILTERS="$FILTERS or $PERFORMANCE_FILTER"
        else
            FILTERS="$PERFORMANCE_FILTER"
        fi
    fi
    
    if $SKIP_SLOW; then
        FILTERS="($FILTERS) and not slow"
    fi
    
    if [ -n "$FILTERS" ]; then
        CMD="$CMD -m \"$FILTERS\""
    fi
fi

# Print the command
echo -e "${BLUE}Running command:${NC}"
echo -e "${CYAN}$CMD${NC}"
echo ""

# Run the tests
eval $CMD
TEST_EXIT_CODE=$?

# Run visualization examples if requested
if $RUN_VISUALIZE; then
    echo ""
    echo -e "${BLUE}Running visualization examples...${NC}"
    python -m ultra.ultra.tests.test_core_neural.test_visualization_examples --show
    VISUALIZE_EXIT_CODE=$?
else
    VISUALIZE_EXIT_CODE=0
fi

# Print summary
echo ""
echo -e "${MAGENTA}Test Summary:${NC}"
if [ $TEST_EXIT_CODE -eq 0 ]; then
    echo -e "${GREEN}✓ Tests completed successfully${NC}"
else
    echo -e "${RED}✗ Tests failed with exit code $TEST_EXIT_CODE${NC}"
fi

if $RUN_VISUALIZE; then
    if [ $VISUALIZE_EXIT_CODE -eq 0 ]; then
        echo -e "${GREEN}✓ Visualization examples completed successfully${NC}"
    else
        echo -e "${RED}✗ Visualization examples failed with exit code $VISUALIZE_EXIT_CODE${NC}"
    fi
fi

# Display coverage report path if generated
if $GENERATE_COVERAGE; then
    echo ""
    echo -e "${BLUE}Coverage report generated at:${NC}"
    echo -e "${CYAN}$ROOT_DIR/coverage_report/index.html${NC}"
fi

# Exit with appropriate code
if [ $TEST_EXIT_CODE -ne 0 ] || [ $VISUALIZE_EXIT_CODE -ne 0 ]; then
    exit 1
else
    exit 0
fi