#!/usr/bin/env python3
"""
ULTRA: Ultimate Learning & Thought Reasoning Architecture
Core Neural Architecture - Biological Timing Circuits Tests

This module contains tests for the Biological Timing Circuits component, which implements
oscillatory mechanisms similar to brain rhythms (theta, alpha, beta, gamma) to coordinate
processing across distributed neural modules.
"""

import os
import sys
import numpy as np
import pytest
import matplotlib.pyplot as plt
import logging
from numpy.testing import assert_array_almost_equal
from scipy.signal import coherence, hilbert
from scipy.stats import pearsonr

# Ensure the test module is in the path
ROOT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if ROOT_DIR not in sys.path:
    sys.path.insert(0, ROOT_DIR)

# Import test configuration and utilities
from tests.test_core_neural import (
    biological_timing_circuits, network_state_recorder,
    BIOLOGICAL_TIMING_DEFAULT_PARAMS, validate_oscillator_dynamics, analytical_oscillator
)

# Import modules to test
from ultra.core_neural.biological_timing import (
    BiologicalTimingCircuits, NeuralOscillator, PhaseAmplitudeCoupling
)

# Configure logging
logger = logging.getLogger(__name__)

class TestNeuralOscillator:
    """Test suite for the Neural Oscillator component"""
    
    def test_initialization(self):
        """Test that neural oscillators initialize with the correct parameters"""
        # Create oscillator with default parameters
        frequency = 10.0  # Hz
        amplitude = 1.0
        phase = 0.0
        
        oscillator = NeuralOscillator(frequency=frequency, amplitude=amplitude, phase=phase)
        
        # Check parameters
        assert oscillator.frequency == frequency, f"Frequency should be {frequency}"
        assert oscillator.amplitude == amplitude, f"Amplitude should be {amplitude}"
        assert oscillator.initial_phase == phase, f"Initial phase should be {phase}"
        
        # Check initial state
        assert oscillator.phase == phase, "Initial phase should match parameter"
        assert oscillator.time == 0.0, "Initial time should be 0.0"
    
    def test_oscillation_dynamics(self):
        """Test that oscillator generates the correct waveform"""
        # Create oscillator with known parameters
        frequency = 10.0  # Hz
        amplitude = 2.0
        phase = np.pi / 4  # 45 degrees
        
        oscillator = NeuralOscillator(frequency=frequency, amplitude=amplitude, phase=phase)
        
        # Simulate for one cycle
        dt = 0.1  # ms
        duration = 1000.0 / frequency  # One cycle in ms
        steps = int(duration / dt)
        
        # Track output
        time_points = []
        outputs = []
        phases = []
        
        for _ in range(steps):
            output = oscillator.update(dt)
            time_points.append(oscillator.time)
            outputs.append(output)
            phases.append(oscillator.phase)
        
        # Convert to numpy arrays
        time_points = np.array(time_points)
        outputs = np.array(outputs)
        phases = np.array(phases)
        
        # Check frequency: time for one cycle
        # Phase should increase by 2π over one cycle
        phase_diff = phases[-1] - phases[0]
        cycle_time = time_points[-1] - time_points[0]
        
        expected_phase_diff = 2 * np.pi
        expected_cycle_time = 1000.0 / frequency  # ms
        
        assert abs(phase_diff - expected_phase_diff) < 0.1, \
            f"Phase should increase by 2π over one cycle, got {phase_diff}"
        
        assert abs(cycle_time - expected_cycle_time) < 0.1, \
            f"Cycle time should be {expected_cycle_time}ms, got {cycle_time}ms"
        
        # Check amplitude
        output_amplitude = (np.max(outputs) - np.min(outputs)) / 2
        assert abs(output_amplitude - amplitude) < 0.1, \
            f"Output amplitude should be {amplitude}, got {output_amplitude}"
        
        # Check waveform shape
        # It should follow a sine wave: output = amplitude * sin(2π * frequency * time/1000 + phase)
        expected_outputs = amplitude * np.sin(2 * np.pi * frequency * time_points / 1000 + phase)
        
        assert np.allclose(outputs, expected_outputs, rtol=0.05), \
            "Output should follow a sine wave pattern"
    
    def test_frequency_accuracy(self):
        """Test that oscillators accurately maintain their specified frequency"""
        # Create oscillators with different frequencies
        frequencies = [1.0, 5.0, 10.0, 20.0, 40.0]  # Hz
        oscillators = [NeuralOscillator(frequency=f) for f in frequencies]
        
        # Simulate for a longer duration
        dt = 0.1  # ms
        duration = 1000.0  # 1 second
        steps = int(duration / dt)
        
        # Track outputs for each oscillator
        all_outputs = [[] for _ in frequencies]
        
        for _ in range(steps):
            for i, oscillator in enumerate(oscillators):
                output = oscillator.update(dt)
                all_outputs[i].append(output)
        
        # Analyze frequency content of each oscillator's output
        for i, outputs in enumerate(all_outputs):
            # Convert to numpy array
            outputs = np.array(outputs)
            
            # Find peaks to estimate frequency
            from scipy.signal import find_peaks
            peaks, _ = find_peaks(outputs)
            
            if len(peaks) >= 2:
                # Calculate peak intervals
                peak_intervals = np.diff(peaks) * dt  # in ms
                
                # Convert to frequency (Hz)
                estimated_frequency = 1000.0 / np.mean(peak_intervals)
                
                # Check against expected frequency
                expected_frequency = frequencies[i]
                assert abs(estimated_frequency - expected_frequency) / expected_frequency < 0.05, \
                    f"Estimated frequency {estimated_frequency}Hz should match expected {expected_frequency}Hz"
    
    def test_phase_reset(self):
        """Test that oscillators can be phase-reset"""
        # Create oscillator
        frequency = 10.0  # Hz
        oscillator = NeuralOscillator(frequency=frequency)
        
        # Run for a while
        dt = 0.1  # ms
        for _ in range(100):
            oscillator.update(dt)
        
        # Record current phase and output
        phase_before = oscillator.phase
        output_before = oscillator.get_output()
        
        # Reset phase to 0
        oscillator.reset_phase(0.0)
        
        # Check that phase was reset
        assert oscillator.phase == 0.0, f"Phase should be reset to 0.0, got {oscillator.phase}"
        
        # Check that output changed according to new phase
        output_after = oscillator.get_output()
        assert output_after == oscillator.amplitude * np.sin(oscillator.phase), \
            f"Output should follow new phase, expected {oscillator.amplitude * np.sin(oscillator.phase)}, got {output_after}"
        
        # Check that time was not reset
        assert oscillator.time > 0, "Time should not be reset by phase reset"
    
    def test_modulate_frequency(self):
        """Test that oscillator frequency can be modulated"""
        # Create oscillator
        frequency = 10.0  # Hz
        oscillator = NeuralOscillator(frequency=frequency)
        
        # Track initial cycle time
        dt = 0.1  # ms
        initial_cycle_time = 1000.0 / frequency  # ms
        
        # Record phase change over 100 steps
        oscillator.reset_phase(0.0)
        for _ in range(100):
            oscillator.update(dt)
        phase_change_1 = oscillator.phase
        
        # Modulate frequency (double it)
        new_frequency = 20.0  # Hz
        oscillator.set_frequency(new_frequency)
        
        # Check that frequency was updated
        assert oscillator.frequency == new_frequency, \
            f"Frequency should be updated to {new_frequency}, got {oscillator.frequency}"
        
        # Record phase change over another 100 steps
        oscillator.reset_phase(0.0)
        for _ in range(100):
            oscillator.update(dt)
        phase_change_2 = oscillator.phase
        
        # Phase should change twice as fast with doubled frequency
        assert abs(phase_change_2 / phase_change_1 - 2.0) < 0.1, \
            f"Phase should change twice as fast with doubled frequency, ratio: {phase_change_2 / phase_change_1}"

class TestPhaseAmplitudeCoupling:
    """Test suite for the Phase-Amplitude Coupling component"""
    
    def test_initialization(self):
        """Test that phase-amplitude coupling initializes with the correct parameters"""
        # Create carrier (low frequency) and modulated (high frequency) oscillators
        carrier_freq = 5.0  # Hz
        modulated_freq = 40.0  # Hz
        coupling_strength = 0.5
        
        carrier = NeuralOscillator(frequency=carrier_freq)
        modulated = NeuralOscillator(frequency=modulated_freq)
        
        pac = PhaseAmplitudeCoupling(carrier=carrier, modulated=modulated, 
                                     coupling_strength=coupling_strength)
        
        # Check parameters
        assert pac.carrier == carrier, "Carrier oscillator should be set correctly"
        assert pac.modulated == modulated, "Modulated oscillator should be set correctly"
        assert pac.coupling_strength == coupling_strength, f"Coupling strength should be {coupling_strength}"
    
    def test_amplitude_modulation(self):
        """Test that the amplitude of the modulated oscillator is coupled to the phase of the carrier"""
        # Create phase-amplitude coupling
        carrier_freq = 5.0  # Hz
        modulated_freq = 40.0  # Hz
        coupling_strength = 0.5
        
        carrier = NeuralOscillator(frequency=carrier_freq)
        modulated = NeuralOscillator(frequency=modulated_freq)
        
        pac = PhaseAmplitudeCoupling(carrier=carrier, modulated=modulated, 
                                     coupling_strength=coupling_strength)
        
        # Simulate for one carrier cycle
        dt = 0.1  # ms
        duration = 1000.0 / carrier_freq  # One carrier cycle in ms
        steps = int(duration / dt)
        
        # Track outputs
        carrier_outputs = []
        modulated_outputs = []
        modulated_amplitudes = []
        carrier_phases = []
        
        for _ in range(steps):
            # Update oscillators
            pac.update(dt)
            
            # Record outputs
            carrier_outputs.append(carrier.get_output())
            modulated_output = modulated.get_output()
            modulated_outputs.append(modulated_output)
            
            # Record carrier phase
            carrier_phases.append(carrier.phase)
            
            # Estimate modulated amplitude (this is approximate)
            # In a real analysis, we would use Hilbert transform on a longer signal
            modulated_amplitudes.append(abs(modulated_output))
        
        # Convert to numpy arrays
        carrier_outputs = np.array(carrier_outputs)
        modulated_outputs = np.array(modulated_outputs)
        modulated_amplitudes = np.array(modulated_amplitudes)
        carrier_phases = np.array(carrier_phases)
        
        # Get a better amplitude estimate using Hilbert transform
        analytic_signal = hilbert(modulated_outputs)
        amplitude_envelope = np.abs(analytic_signal)
        
        # Check that amplitude envelope varies with carrier phase
        # amplitude should be highest when carrier is at peak (phase ≈ π/2)
        # and lowest when carrier is at trough (phase ≈ 3π/2)
        
        # Find carrier peaks and troughs
        # Peaks: phase ≈ π/2
        peak_indices = np.where(np.abs(carrier_phases - np.pi/2) < 0.5)[0]
        if len(peak_indices) > 0:
            peak_amplitudes = amplitude_envelope[peak_indices]
            peak_amplitude = np.mean(peak_amplitudes)
        else:
            peak_amplitude = 0
        
        # Troughs: phase ≈ 3π/2
        trough_indices = np.where(np.abs(carrier_phases - 3*np.pi/2) < 0.5)[0]
        if len(trough_indices) > 0:
            trough_amplitudes = amplitude_envelope[trough_indices]
            trough_amplitude = np.mean(trough_amplitudes)
        else:
            trough_amplitude = 0
        
        # Amplitude should be higher at peaks than troughs
        if peak_amplitude > 0 and trough_amplitude > 0:
            assert peak_amplitude > trough_amplitude, \
                f"Amplitude should be higher at carrier peaks ({peak_amplitude}) than troughs ({trough_amplitude})"
            
            # Modulation depth should be related to coupling strength
            modulation_depth = (peak_amplitude - trough_amplitude) / (peak_amplitude + trough_amplitude)
            assert 0.4*coupling_strength < modulation_depth < 1.5*coupling_strength, \
                f"Modulation depth ({modulation_depth}) should be related to coupling strength ({coupling_strength})"
        
        # Check correlation between carrier output and modulated amplitude
        # There should be a positive correlation due to the coupling
        correlation, _ = pearsonr(carrier_outputs, amplitude_envelope)
        assert correlation > 0.5, \
            f"Carrier output and modulated amplitude should be positively correlated, got {correlation}"
    
    def test_modulation_index(self):
        """Test calculation of modulation index to quantify coupling strength"""
        # Create phase-amplitude coupling with different strengths
        carrier_freq = 5.0  # Hz
        modulated_freq = 40.0  # Hz
        coupling_strengths = [0.1, 0.5, 0.9]
        
        # Simulate for longer duration
        dt = 0.1  # ms
        duration = 2000.0  # 2 seconds
        steps = int(duration / dt)
        
        modulation_indices = []
        
        for coupling_strength in coupling_strengths:
            carrier = NeuralOscillator(frequency=carrier_freq)
            modulated = NeuralOscillator(frequency=modulated_freq)
            
            pac = PhaseAmplitudeCoupling(carrier=carrier, modulated=modulated, 
                                        coupling_strength=coupling_strength)
            
            # Track outputs
            carrier_phases = []
            modulated_amplitudes = []
            
            for _ in range(steps):
                # Update oscillators
                pac.update(dt)
                
                # Record carrier phase
                carrier_phases.append(carrier.phase)
                
                # Record modulated output
                modulated_output = modulated.get_output()
                modulated_amplitudes.append(modulated_output)
                
            # Convert to numpy arrays
            modulated_outputs = np.array(modulated_amplitudes)
            carrier_phases = np.array(carrier_phases)
            
            # Get amplitude envelope using Hilbert transform
            analytic_signal = hilbert(modulated_outputs)
            amplitude_envelope = np.abs(analytic_signal)
            
            # Calculate modulation index using mean vector length method
            # Bin the carrier phases
            n_bins = 18  # 20-degree bins
            bin_means = np.zeros(n_bins)
            bin_width = 2 * np.pi / n_bins
            
            for j in range(n_bins):
                bin_start = j * bin_width
                bin_end = (j + 1) * bin_width
                
                # Find indices of carrier phases in this bin
                bin_indices = np.where((carrier_phases >= bin_start) & (carrier_phases < bin_end))[0]
                
                if len(bin_indices) > 0:
                    bin_means[j] = np.mean(amplitude_envelope[bin_indices])
                    
            # Normalize bin means
            bin_means = bin_means / np.sum(bin_means)
            
            # Calculate mean vector length
            theta = np.linspace(0, 2*np.pi, n_bins, endpoint=False) + bin_width/2
            complex_values = bin_means * np.exp(1j * theta)
            mean_vector = np.sum(complex_values)
            modulation_index = np.abs(mean_vector)
            
            modulation_indices.append(modulation_index)
        
        # Modulation index should increase with coupling strength
        assert modulation_indices[0] < modulation_indices[1] < modulation_indices[2], \
            f"Modulation index should increase with coupling strength, got {modulation_indices}"

class TestBiologicalTimingCircuits:
    """Test suite for the integrated Biological Timing Circuits"""
    
    def test_initialization(self, biological_timing_circuits):
        """Test that the circuit initializes with the correct parameters and components"""
        circuits = biological_timing_circuits
        
        # Check that oscillators are initialized
        assert "delta" in circuits.oscillators, "System should have delta oscillator"
        assert "theta" in circuits.oscillators, "System should have theta oscillator"
        assert "alpha" in circuits.oscillators, "System should have alpha oscillator"
        assert "beta" in circuits.oscillators, "System should have beta oscillator"
        assert "gamma" in circuits.oscillators, "System should have gamma oscillator"
        
        # Check oscillator parameters
        for name, oscillator in circuits.oscillators.items():
            assert oscillator.frequency == BIOLOGICAL_TIMING_DEFAULT_PARAMS["oscillators"][name]["frequency"], \
                f"{name} oscillator frequency should match default params"
            assert oscillator.amplitude == BIOLOGICAL_TIMING_DEFAULT_PARAMS["oscillators"][name]["amplitude"], \
                f"{name} oscillator amplitude should match default params"
            assert oscillator.initial_phase == BIOLOGICAL_TIMING_DEFAULT_PARAMS["oscillators"][name]["phase"], \
                f"{name} oscillator phase should match default params"
        
        # Check that couplings are initialized
        assert hasattr(circuits, "couplings"), "System should have couplings"
        assert len(circuits.couplings) > 0, "System should have at least one coupling"
    
    def test_oscillator_update(self, biological_timing_circuits):
        """Test that all oscillators update correctly"""
        circuits = biological_timing_circuits
        
        # Track initial phases
        initial_phases = {name: oscillator.phase for name, oscillator in circuits.oscillators.items()}
        
        # Update with small dt
        dt = 0.1  # ms
        circuits.update(dt)
        
        # Check that all phases increased
        for name, oscillator in circuits.oscillators.items():
            assert oscillator.phase > initial_phases[name], \
                f"{name} oscillator phase should increase after update"
            
            # Phase should increase by 2π * frequency * dt / 1000
            expected_increase = 2 * np.pi * oscillator.frequency * dt / 1000
            actual_increase = oscillator.phase - initial_phases[name]
            
            assert abs(actual_increase - expected_increase) < 1e-6, \
                f"{name} oscillator phase should increase by {expected_increase}, got {actual_increase}"
    
    def test_phase_amplitude_coupling(self, biological_timing_circuits):
        """Test that PAC affects gamma and beta amplitudes"""
        circuits = biological_timing_circuits
        
        # Simulate for multiple theta cycles
        dt = 0.1  # ms
        duration = 1000.0  # 1 second
        steps = int(duration / dt)
        
        # Track outputs
        theta_outputs = []
        gamma_outputs = []
        alpha_outputs = []
        beta_outputs = []
        
        for _ in range(steps):
            # Update circuits
            circuits.update(dt)
            
            # Get outputs
            outputs = circuits.get_outputs()
            theta_outputs.append(outputs["theta"])
            gamma_outputs.append(outputs["gamma"])
            alpha_outputs.append(outputs["alpha"])
            beta_outputs.append(outputs["beta"])
        
        # Convert to numpy arrays
        theta_outputs = np.array(theta_outputs)
        gamma_outputs = np.array(gamma_outputs)
        alpha_outputs = np.array(alpha_outputs)
        beta_outputs = np.array(beta_outputs)
        
        # Get amplitude envelopes using Hilbert transform
        gamma_analytic = hilbert(gamma_outputs)
        gamma_amplitude = np.abs(gamma_analytic)
        
        beta_analytic = hilbert(beta_outputs)
        beta_amplitude = np.abs(beta_analytic)
        
        # Check correlation between theta and gamma amplitude
        # There should be a positive correlation due to the coupling
        theta_gamma_corr, _ = pearsonr(theta_outputs, gamma_amplitude)
        assert theta_gamma_corr > 0.5, \
            f"Theta output and gamma amplitude should be positively correlated, got {theta_gamma_corr}"
        
        # Check correlation between alpha and beta amplitude
        alpha_beta_corr, _ = pearsonr(alpha_outputs, beta_amplitude)
        assert alpha_beta_corr > 0.3, \
            f"Alpha output and beta amplitude should be positively correlated, got {alpha_beta_corr}"
    
    def test_oscillation_coherence(self, biological_timing_circuits):
        """Test that oscillations maintain appropriate phase relationships when coupled"""
        circuits = biological_timing_circuits
        
        # Reset oscillator phases to specific values
        circuits.oscillators["theta"].reset_phase(0.0)
        circuits.oscillators["gamma"].reset_phase(0.0)
        
        # Simulate for multiple theta cycles
        dt = 0.1  # ms
        duration = 2000.0  # 2 seconds
        steps = int(duration / dt)
        
        # Track phases
        theta_phases = []
        gamma_phases = []
        
        for _ in range(steps):
            # Update circuits
            circuits.update(dt)
            
            # Record phases
            theta_phases.append(circuits.oscillators["theta"].phase)
            gamma_phases.append(circuits.oscillators["gamma"].phase)
        
        # Convert to numpy arrays
        theta_phases = np.array(theta_phases)
        gamma_phases = np.array(gamma_phases)
        
        # Calculate phase coherence
        # When gamma amplitude is modulated by theta phase, gamma is more likely
        # to oscillate at a specific phase of theta
        
        # Unwrap phases to handle 2π transitions
        theta_unwrapped = np.unwrap(theta_phases)
        gamma_unwrapped = np.unwrap(gamma_phases)
        
        # Calculate theta-gamma phase locking value (PLV)
        # First, get instantaneous phase difference
        theta_gamma_ratio = circuits.oscillators["gamma"].frequency / circuits.oscillators["theta"].frequency
        # This ratio should be close to an integer for phase locking
        expected_ratio = round(theta_gamma_ratio)
        
        # Calculate the n:m phase difference
        phase_diff = gamma_unwrapped - expected_ratio * theta_unwrapped
        
        # Calculate PLV
        complex_phase = np.exp(1j * phase_diff)
        plv = np.abs(np.mean(complex_phase))
        
        # PLV should be > 0 (indicating some phase locking)
        assert plv > 0.1, f"There should be some theta-gamma phase locking, PLV: {plv}"
    
    def test_phase_reset(self, biological_timing_circuits):
        """Test that oscillations can be externally synchronized through phase reset"""
        circuits = biological_timing_circuits
        
        # Set oscillators to random phases
        for oscillator in circuits.oscillators.values():
            oscillator.reset_phase(np.random.rand() * 2 * np.pi)
        
        # Record initial phase coherence
        phases_before = {name: oscillator.phase for name, oscillator in circuits.oscillators.items()}
        
        # Apply global reset to all oscillators
        reset_phase = 0.0
        for name in circuits.oscillators:
            circuits.reset_oscillator_phase(name, reset_phase)
        
        # Check that all phases were reset
        for name, oscillator in circuits.oscillators.items():
            assert oscillator.phase == reset_phase, \
                f"{name} oscillator phase should be reset to {reset_phase}, got {oscillator.phase}"
    
    def test_frequency_modulation(self, biological_timing_circuits):
        """Test that oscillator frequencies can be modulated"""
        circuits = biological_timing_circuits
        
        # Record original frequencies
        original_frequencies = {name: oscillator.frequency for name, oscillator in circuits.oscillators.items()}
        
        # Modulate beta frequency (increase by 50%)
        new_beta_freq = original_frequencies["beta"] * 1.5
        circuits.set_oscillator_frequency("beta", new_beta_freq)
        
        # Check that frequency was updated
        assert circuits.oscillators["beta"].frequency == new_beta_freq, \
            f"Beta frequency should be updated to {new_beta_freq}, got {circuits.oscillators['beta'].frequency}"
        
        # Run simulation and check that beta oscillates faster
        dt = 0.1  # ms
        duration = 500.0  # ms
        steps = int(duration / dt)
        
        # Reset phases
        for oscillator in circuits.oscillators.values():
            oscillator.reset_phase(0.0)
        
        # Simulate
        for _ in range(steps):
            circuits.update(dt)
        
        # Check beta phase increase compared to alpha
        beta_phase = circuits.oscillators["beta"].phase
        alpha_phase = circuits.oscillators["alpha"].phase
        
        # Beta should have more phase accumulation
        beta_alpha_ratio = beta_phase / alpha_phase
        expected_ratio = new_beta_freq / original_frequencies["alpha"]
        
        assert abs(beta_alpha_ratio - expected_ratio) / expected_ratio < 0.05, \
            f"Beta-Alpha phase ratio should be {expected_ratio}, got {beta_alpha_ratio}"
    
    def test_oscillation_binding(self, biological_timing_circuits):
        """Test synchronization between oscillators which simulates neural binding"""
        circuits = biological_timing_circuits
        
        # Add binding between beta and gamma oscillators
        binding_strength = 0.2
        circuits.add_binding("beta", "gamma", binding_strength)
        
        # Reset oscillator phases
        circuits.oscillators["beta"].reset_phase(0.0)
        circuits.oscillators["gamma"].reset_phase(np.pi)  # Start out of phase
        
        # Simulate
        dt = 0.1  # ms
        duration = 2000.0  # 2 seconds
        steps = int(duration / dt)
        
        # Track phases
        beta_phases = []
        gamma_phases = []
        
        for _ in range(steps):
            # Update circuits
            circuits.update(dt)
            
            # Record phases
            beta_phases.append(circuits.oscillators["beta"].phase)
            gamma_phases.append(circuits.oscillators["gamma"].phase)
        
        # Calculate phase synchrony over time
        # Divide the simulation into early, middle, and late segments
        segment_size = len(beta_phases) // 3
        
        # Calculate phase synchrony for each segment
        def calculate_synchrony(phase1, phase2):
            # Unwrap phases
            phase1_unwrapped = np.unwrap(phase1)
            phase2_unwrapped = np.unwrap(phase2)
            
            # Calculate frequency ratio
            freq_ratio = circuits.oscillators["gamma"].frequency / circuits.oscillators["beta"].frequency
            expected_ratio = round(freq_ratio)
            
            # Calculate n:m phase difference
            phase_diff = phase2_unwrapped - expected_ratio * phase1_unwrapped
            
            # Calculate PLV
            complex_phase = np.exp(1j * phase_diff)
            plv = np.abs(np.mean(complex_phase))
            return plv
        
        early_sync = calculate_synchrony(
            beta_phases[:segment_size], gamma_phases[:segment_size]
        )
        
        late_sync = calculate_synchrony(
            beta_phases[-segment_size:], gamma_phases[-segment_size:]
        )
        
        # Synchrony should increase over time due to binding
        assert late_sync > early_sync, \
            f"Synchrony should increase over time, early: {early_sync}, late: {late_sync}"
    
    def test_network_entrainment(self, biological_timing_circuits):
        """Test that oscillations can entrain neural activity patterns"""
        circuits = biological_timing_circuits
        
        # Simulate a small neural network driven by oscillations
        n_neurons = 10
        membrane_potentials = np.zeros(n_neurons)
        thresholds = np.ones(n_neurons)
        
        # Each neuron is influenced by a specific oscillator
        neuron_drivers = {
            0: "delta",
            1: "delta",
            2: "theta",
            3: "theta",
            4: "alpha",
            5: "alpha",
            6: "beta",
            7: "beta",
            8: "gamma",
            9: "gamma"
        }
        
        # Reset oscillator phases
        for oscillator in circuits.oscillators.values():
            oscillator.reset_phase(0.0)
        
        # Simulate
        dt = 0.1  # ms
        duration = 1000.0  # 1 second
        steps = int(duration / dt)
        
        # Track spike times
        spike_times = {i: [] for i in range(n_neurons)}
        
        for step in range(steps):
            t = step * dt
            
            # Update circuits
            circuits.update(dt)
            
            # Update neurons based on oscillator outputs
            outputs = circuits.get_outputs()
            
            for i in range(n_neurons):
                # Get driving oscillator
                driver = neuron_drivers[i]
                
                # Update membrane potential
                # Simple model: potential is driven by oscillator output
                tau = 10.0  # ms
                membrane_potentials[i] += (-membrane_potentials[i] + outputs[driver]) * dt / tau
                
                # Check for spike
                if membrane_potentials[i] > thresholds[i]:
                    spike_times[i].append(t)
                    membrane_potentials[i] = 0  # Reset
        
        # Calculate spike phase distribution for each neuron
        phase_stats = {}
        
        for i in range(n_neurons):
            driver = neuron_drivers[i]
            driver_freq = circuits.oscillators[driver].frequency
            
            phases = []
            for spike_t in spike_times[i]:
                # Calculate oscillator phase at spike time
                phase = (2 * np.pi * driver_freq * spike_t / 1000) % (2 * np.pi)
                phases.append(phase)
                
            if len(phases) > 5:  # Need enough spikes for statistics
                # Calculate phase concentration
                # For uniform distribution, R ≈ 0; for concentrated distribution, R approaches 1
                phases = np.array(phases)
                mean_vector = np.mean(np.exp(1j * phases))
                R = np.abs(mean_vector)
                
                phase_stats[i] = R
        
        # Neurons should show phase locking to their driving oscillators
        for i, R in phase_stats.items():
            assert R > 0.5, f"Neuron {i} should show phase locking to {neuron_drivers[i]}, R: {R}"
    
    def test_cross_frequency_dynamics(self, biological_timing_circuits):
        """Test complex cross-frequency dynamics between multiple oscillators"""
        circuits = biological_timing_circuits
        
        # Create a more complex coupling topology
        # theta → gamma (amplitude)
        # alpha → beta (amplitude)
        # theta → alpha (phase)
        # beta → gamma (phase)
        
        # Set up the gamma phase to follow beta phase
        phase_coupling_strength = 0.2
        circuits.add_phase_coupling("beta", "gamma", phase_coupling_strength)
        
        # Simulate
        dt = 0.1  # ms
        duration = 5000.0  # 5 seconds
        steps = int(duration / dt)
        
        # Track outputs
        outputs = {name: [] for name in circuits.oscillators}
        
        for _ in range(steps):
            # Update circuits
            circuits.update(dt)
            
            # Record outputs
            current_outputs = circuits.get_outputs()
            for name, value in current_outputs.items():
                outputs[name].append(value)
        
        # Convert to numpy arrays
        for name in outputs:
            outputs[name] = np.array(outputs[name])
        
        # Calculate amplitude envelopes
        envelopes = {}
        for name, signal in outputs.items():
            analytic = hilbert(signal)
            envelopes[name] = np.abs(analytic)
        
        # Calculate phase time series
        phases = {}
        for name, signal in outputs.items():
            analytic = hilbert(signal)
            phases[name] = np.angle(analytic)
        
        # Check theta-gamma amplitude coupling
        theta_gamma_corr, _ = pearsonr(outputs["theta"], envelopes["gamma"])
        assert theta_gamma_corr > 0.3, \
            f"Theta output and gamma amplitude should be correlated, got {theta_gamma_corr}"
        
        # Check alpha-beta amplitude coupling
        alpha_beta_corr, _ = pearsonr(outputs["alpha"], envelopes["beta"])
        assert alpha_beta_corr > 0.3, \
            f"Alpha output and beta amplitude should be correlated, got {alpha_beta_corr}"
        
        # Check beta-gamma phase coupling
        # Calculate phase difference
        beta_gamma_phase_diff = phases["gamma"] - phases["beta"]
        
        # If there is phase coupling, the phase difference should have a preferred value
        # Calculate phase difference concentration
        complex_diff = np.exp(1j * beta_gamma_phase_diff)
        mean_vector = np.abs(np.mean(complex_diff))
        
        assert mean_vector > 0.2, \
            f"Beta and gamma phases should show some coupling, mean vector: {mean_vector}"
    
    def test_oscillation_nesting(self, biological_timing_circuits):
        """Test hierarchical nesting of oscillations at different frequencies"""
        circuits = biological_timing_circuits
        
        # Create nesting: delta → theta → alpha → beta → gamma
        # Each oscillator's phase should be modulated by the slower oscillator
        circuits.add_nesting(["delta", "theta", "alpha", "beta", "gamma"], 0.3)
        
        # Simulate
        dt = 0.1  # ms
        duration = 10000.0  # 10 seconds
        steps = int(duration / dt)
        
        # Track outputs
        outputs = {name: [] for name in circuits.oscillators}
        
        for _ in range(steps):
            # Update circuits
            circuits.update(dt)
            
            # Record outputs
            current_outputs = circuits.get_outputs()
            for name, value in current_outputs.items():
                outputs[name].append(value)
        
        # Convert to numpy arrays
        for name in outputs:
            outputs[name] = np.array(outputs[name])
        
        # Calculate phase time series
        phases = {}
        for name, signal in outputs.items():
            analytic = hilbert(signal)
            phases[name] = np.angle(analytic)
        
        # Check for phase-phase coupling between adjacent frequency bands
        couplings = [
            ("delta", "theta"),
            ("theta", "alpha"),
            ("alpha", "beta"),
            ("beta", "gamma")
        ]
        
        for slow, fast in couplings:
            # Calculate n:m phase locking value
            slow_freq = circuits.oscillators[slow].frequency
            fast_freq = circuits.oscillators[fast].frequency
            ratio = int(round(fast_freq / slow_freq))
            
            # n:m phase difference
            phase_diff = phases[fast] - ratio * phases[slow]
            
            # Calculate PLV
            complex_diff = np.exp(1j * phase_diff)
            plv = np.abs(np.mean(complex_diff))
            
            assert plv > 0.1, \
                f"There should be phase-phase coupling between {slow} and {fast}, PLV: {plv}"