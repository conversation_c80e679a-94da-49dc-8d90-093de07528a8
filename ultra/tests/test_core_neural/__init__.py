#!/usr/bin/env python3
"""
ULTRA: Ultimate Learning & Thought Reasoning Architecture
Core Neural Architecture Testing Module

This module contains tests for the Core Neural Architecture components:
- Neuromorphic Core
- Neuroplasticity Engine
- Synaptic Pruning Module
- Neuromodulation System
- Biological Timing Circuits

The tests verify the implementation and mathematical correctness of the neural 
processing mechanisms and their interactions.
"""

import os
import sys
import numpy as np
import torch
import pytest
import scipy
import scipy.sparse
import math
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple, Union, Callable, Optional, Type, Any
from dataclasses import dataclass, field
from functools import partial
from contextlib import contextmanager
import logging
import time
import json
import unittest
import unittest.mock as mock

# Ensure the ULTRA module is in the path
ROOT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if ROOT_DIR not in sys.path:
    sys.path.insert(0, ROOT_DIR)

# Import ULTRA core neural modules with error handling
try:
    from ultra.ultra.core_neural.neuromorphic_core import NeuromorphicCore
except ImportError:
    # Fallback für fehlende Module
    class NeuromorphicCore:
        def __init__(self, dimensions, neuron_types):
            self.dimensions = dimensions
            self.neuron_types = neuron_types
            self.membrane_potentials = np.zeros(np.prod(dimensions))
            
        def get_spikes(self):
            return []
            
        def set_neuroplasticity_engine(self, engine):
            self.neuroplasticity_engine = engine
            
        def set_synaptic_pruning_module(self, module):
            self.synaptic_pruning_module = module
            
        def set_neuromodulation_system(self, system):
            self.neuromodulation_system = system
            
        def set_biological_timing_circuits(self, circuits):
            self.biological_timing = circuits

try:
    from ultra.ultra.core_neural.neuroplasticity import (
        NeuroplasticityEngine, 
        STDPMechanism, 
        HomeostasisMechanism, 
        StructuralPlasticityMechanism
    )
except ImportError:
    # Fallback-Klassen
    class NeuroplasticityEngine:
        def __init__(self, eta, alpha, A_plus, A_minus, tau_plus, tau_minus):
            self.eta = eta
            self.alpha = alpha
            self.A_plus = A_plus
            self.A_minus = A_minus
            self.tau_plus = tau_plus
            self.tau_minus = tau_minus
    
    class STDPMechanism:
        def __init__(self, A_plus=0.1, A_minus=-0.1, tau_plus=20.0, tau_minus=20.0):
            self.A_plus = A_plus
            self.A_minus = A_minus
            self.tau_plus = tau_plus
            self.tau_minus = tau_minus
            
        def compute_weight_change(self, t_pre, t_post):
            delta_t = t_post - t_pre
            if delta_t > 0:
                return self.A_plus * np.exp(-delta_t / self.tau_plus)
            else:
                return self.A_minus * np.exp(delta_t / self.tau_minus)
    
    class HomeostasisMechanism:
        def __init__(self):
            pass
    
    class StructuralPlasticityMechanism:
        def __init__(self):
            pass

try:
    from ultra.ultra.core_neural.synaptic_pruning import (
        SynapticPruningModule, 
        WeightBasedPruning, 
        ActivityBasedPruning, 
        RedundancyBasedPruning
    )
except ImportError:
    class SynapticPruningModule:
        def __init__(self, theta_prune, theta_usage, pruning_rate):
            self.theta_prune = theta_prune
            self.theta_usage = theta_usage
            self.pruning_rate = pruning_rate
    
    class WeightBasedPruning:
        def __init__(self):
            pass
    
    class ActivityBasedPruning:
        def __init__(self):
            pass
    
    class RedundancyBasedPruning:
        def __init__(self):
            pass

try:
    from ultra.ultra.core_neural.neuromodulation import (
        NeuromodulationSystem, 
        Modulator, 
        ModulatorType,
        ModulatorParameters
    )
    # Aliase für Kompatibilität
    Neuromodulator = Modulator
    ModulationProfile = ModulatorParameters
except ImportError:
    class NeuromodulationSystem:
        def __init__(self, p_baseline=None, delta_p=None, tau_m=100.0):
            self.p_baseline = p_baseline or {}
            self.delta_p = delta_p or {}
            self.tau_m = tau_m
            self.modulators = {}
            
        def get_levels(self):
            return self.p_baseline
            
        def update_levels(self, inputs, dt):
            pass
    
    class Neuromodulator:
        def __init__(self):
            self.level = 1.0
    
    class ModulationProfile:
        def __init__(self):
            pass

try:
    from ultra.ultra.core_neural.biological_timing import (
        BiologicalTimingCircuits, 
        NeuralOscillator, 
        PhaseAmplitudeCoupling
    )
except ImportError:
    class BiologicalTimingCircuits:
        def __init__(self, oscillator_params, coupling_params):
            self.oscillator_params = oscillator_params
            self.coupling_params = coupling_params
            
        def get_phases(self):
            return {}
    
    class NeuralOscillator:
        def __init__(self, frequency=10.0, amplitude=1.0, initial_phase=0.0):
            self.frequency = frequency
            self.amplitude = amplitude
            self.initial_phase = initial_phase
            self.phase = initial_phase
            self.time = 0.0
            
        def update(self, dt):
            self.time += dt
            self.phase += 2 * np.pi * self.frequency * dt / 1000.0  # Convert dt from ms to s
    
    class PhaseAmplitudeCoupling:
        def __init__(self):
            pass

# Configure logging for tests
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Test Constants
# These values are derived from the mathematical models in the documentation
DEFAULT_NEURON_PARAMS = {
    "lif": {
        "tau": 10.0,          # Membrane time constant (ms)
        "v_rest": 0.0,        # Resting potential (mV)
        "v_threshold": 1.0,   # Threshold potential (mV)
        "v_reset": 0.0,       # Reset potential (mV)
        "refractory_period": 5  # Refractory period (ms)
    },
    "adex": {
        "tau_m": 20.0,        # Membrane time constant (ms)
        "E_L": -70.0,         # Leak reversal potential (mV)
        "v_threshold": -50.0, # Threshold potential (mV)
        "delta_t": 2.0,       # Slope factor (mV)
        "a": 2.0,             # Adaptation coupling parameter (nS)
        "tau_w": 30.0,        # Adaptation time constant (ms)
        "b": 60.0,            # Spike-triggered adaptation (pA)
        "v_reset": -65.0      # Reset potential (mV)
    },
    "izhikevich": {
        "a": 0.02,            # Recovery variable time scale
        "b": 0.2,             # Recovery variable sensitivity to membrane potential
        "c": -65.0,           # After-spike reset value of membrane potential (mV)
        "d": 8.0              # After-spike reset value of recovery variable
    }
}

STDP_DEFAULT_PARAMS = {
    "A_plus": 0.1,           # Amplitude of potentiation
    "A_minus": -0.1,         # Amplitude of depression
    "tau_plus": 20.0,        # Time constant for potentiation (ms)
    "tau_minus": 20.0        # Time constant for depression (ms)
}

NEUROMODULATION_DEFAULT_PARAMS = {
    "tau_m": 100.0,          # Time constant for neuromodulator dynamics (ms)
    "baseline_levels": {
        "dopamine": 1.0,     # Baseline dopamine level
        "serotonin": 1.0,    # Baseline serotonin level 
        "norepinephrine": 1.0, # Baseline norepinephrine level
        "acetylcholine": 1.0 # Baseline acetylcholine level
    },
    "modulation_strength": {
        "learning": 0.5,     # Effect strength on learning rate
        "threshold": 0.3,    # Effect strength on neuron thresholds
        "gain": 0.4,         # Effect strength on neuronal gain
        "adaptation": 0.2    # Effect strength on adaptation
    }
}

BIOLOGICAL_TIMING_DEFAULT_PARAMS = {
    "oscillators": {
        "delta": {"frequency": 2.0, "amplitude": 1.0, "phase": 0.0},
        "theta": {"frequency": 6.0, "amplitude": 1.0, "phase": 0.0},
        "alpha": {"frequency": 10.0, "amplitude": 1.0, "phase": 0.0},
        "beta":  {"frequency": 20.0, "amplitude": 1.0, "phase": 0.0},
        "gamma": {"frequency": 40.0, "amplitude": 1.0, "phase": 0.0}
    },
    "coupling": {
        "theta_gamma": 0.5,  # Coupling strength from theta to gamma
        "alpha_beta": 0.3    # Coupling strength from alpha to beta
    }
}

# Test Utilities
class NetworkStateRecorder:
    """
    Utility for recording and analyzing network state over time during tests.
    """
    def __init__(self):
        self.membrane_potentials = []
        self.spike_times = {}
        self.weights = []
        self.neuromodulator_levels = {}
        self.oscillator_phases = {}
        self.timestamps = []
        
    def record_state(self, network, t):
        """Record the current state of the network at time t"""
        self.timestamps.append(t)
        
        # Record membrane potentials
        if hasattr(network, 'membrane_potentials'):
            self.membrane_potentials.append(network.membrane_potentials.copy())
            
        # Record spikes
        if hasattr(network, 'get_spikes'):
            spikes = network.get_spikes()
            for neuron_id, spike_time in spikes:
                if neuron_id not in self.spike_times:
                    self.spike_times[neuron_id] = []
                self.spike_times[neuron_id].append(spike_time)
                
        # Record weights
        if hasattr(network, 'connections') and hasattr(network.connections, 'weights'):
            self.weights.append(network.connections.weights.copy())
            
        # Record neuromodulator levels
        if hasattr(network, 'neuromodulation_system'):
            levels = network.neuromodulation_system.get_levels()
            for modulator, level in levels.items():
                if modulator not in self.neuromodulator_levels:
                    self.neuromodulator_levels[modulator] = []
                self.neuromodulator_levels[modulator].append(level)
                
        # Record oscillator phases
        if hasattr(network, 'biological_timing'):
            phases = network.biological_timing.get_phases()
            for oscillator, phase in phases.items():
                if oscillator not in self.oscillator_phases:
                    self.oscillator_phases[oscillator] = []
                self.oscillator_phases[oscillator].append(phase)
    
    def analyze(self):
        """Analyze the recorded state to extract metrics"""
        metrics = {}
        
        # Calculate firing rates
        if self.spike_times:
            firing_rates = {}
            total_time = self.timestamps[-1] - self.timestamps[0]
            for neuron_id, times in self.spike_times.items():
                firing_rates[neuron_id] = len(times) / (total_time / 1000.0)  # in Hz
            metrics['firing_rates'] = firing_rates
            metrics['mean_firing_rate'] = np.mean(list(firing_rates.values()))
            
        # Calculate weight changes
        if len(self.weights) > 1:
            initial_weights = self.weights[0]
            final_weights = self.weights[-1]
            weight_changes = final_weights - initial_weights
            metrics['weight_changes'] = {
                'mean': np.mean(weight_changes),
                'std': np.std(weight_changes),
                'max': np.max(weight_changes),
                'min': np.min(weight_changes)
            }
            
        # Calculate neuromodulator statistics
        if self.neuromodulator_levels:
            neuromod_stats = {}
            for modulator, levels in self.neuromodulator_levels.items():
                neuromod_stats[modulator] = {
                    'mean': np.mean(levels),
                    'std': np.std(levels),
                    'max': np.max(levels),
                    'min': np.min(levels)
                }
            metrics['neuromodulator_stats'] = neuromod_stats
            
        # Calculate oscillator coherence
        if len(self.oscillator_phases) >= 2:
            oscillator_pairs = []
            for osc1 in self.oscillator_phases:
                for osc2 in self.oscillator_phases:
                    if osc1 < osc2:  # To avoid duplicates
                        oscillator_pairs.append((osc1, osc2))
            
            coherence = {}
            for osc1, osc2 in oscillator_pairs:
                phases1 = np.array(self.oscillator_phases[osc1])
                phases2 = np.array(self.oscillator_phases[osc2])
                phase_diff = phases1 - phases2
                # Calculate Phase Locking Value (PLV)
                complex_phase = np.exp(1j * phase_diff)
                plv = np.abs(np.mean(complex_phase))
                coherence[f"{osc1}-{osc2}"] = plv
            metrics['oscillator_coherence'] = coherence
            
        return metrics

@pytest.fixture
def neuromorphic_core():
    """Fixture for creating a pre-configured NeuromorphicCore instance for testing"""
    dimensions = (10, 10, 10)  # Small 3D grid for testing
    neuron_types = 4  # Four neuron types as specified in documentation
    
    # Create the core with default parameters
    core = NeuromorphicCore(dimensions=dimensions, neuron_types=neuron_types)
    
    return core

@pytest.fixture
def neuroplasticity_engine():
    """Fixture for creating a pre-configured Neuroplasticity Engine for testing"""
    # Create with the default STDP parameters
    engine = NeuroplasticityEngine(
        eta=0.01,  # Learning rate
        alpha=0.001,  # Homeostatic scaling factor
        A_plus=STDP_DEFAULT_PARAMS["A_plus"],
        A_minus=STDP_DEFAULT_PARAMS["A_minus"],
        tau_plus=STDP_DEFAULT_PARAMS["tau_plus"],
        tau_minus=STDP_DEFAULT_PARAMS["tau_minus"]
    )
    
    return engine

@pytest.fixture
def synaptic_pruning_module():
    """Fixture for creating a pre-configured Synaptic Pruning Module for testing"""
    module = SynapticPruningModule(
        theta_prune=0.1,  # Weight threshold for pruning
        theta_usage=0.1,   # Usage threshold for pruning
        pruning_rate=0.01  # Rate of pruning per step
    )
    
    return module

@pytest.fixture
def neuromodulation_system():
    """Fixture for creating a pre-configured Neuromodulation System for testing"""
    system = NeuromodulationSystem(
        p_baseline=NEUROMODULATION_DEFAULT_PARAMS["baseline_levels"],
        delta_p=NEUROMODULATION_DEFAULT_PARAMS["modulation_strength"],
        tau_m=NEUROMODULATION_DEFAULT_PARAMS["tau_m"]
    )
    
    return system

@pytest.fixture
def biological_timing_circuits():
    """Fixture for creating pre-configured Biological Timing Circuits for testing"""
    oscillator_params = BIOLOGICAL_TIMING_DEFAULT_PARAMS["oscillators"]
    coupling_params = BIOLOGICAL_TIMING_DEFAULT_PARAMS["coupling"]
    
    circuits = BiologicalTimingCircuits(oscillator_params, coupling_params)
    
    return circuits

@pytest.fixture
def network_state_recorder():
    """Fixture for creating a NetworkStateRecorder instance"""
    return NetworkStateRecorder()

@pytest.fixture
def input_pattern_generator():
    """
    Fixture for generating various input patterns for testing neural responses
    """
    class InputPatternGenerator:
        @staticmethod
        def constant(duration, value=1.0, dt=1.0):
            """Generate a constant input pattern"""
            steps = int(duration / dt)
            return np.ones(steps) * value
            
        @staticmethod
        def step(duration, step_time, low_value=0.0, high_value=1.0, dt=1.0):
            """Generate a step function input"""
            steps = int(duration / dt)
            step_index = int(step_time / dt)
            pattern = np.ones(steps) * low_value
            pattern[step_index:] = high_value
            return pattern
            
        @staticmethod
        def pulse(duration, pulse_start, pulse_duration, baseline=0.0, pulse_value=1.0, dt=1.0):
            """Generate a pulse input"""
            steps = int(duration / dt)
            start_index = int(pulse_start / dt)
            end_index = start_index + int(pulse_duration / dt)
            
            pattern = np.ones(steps) * baseline
            pattern[start_index:end_index] = pulse_value
            return pattern
            
        @staticmethod
        def sinusoidal(duration, frequency, amplitude=1.0, offset=0.0, dt=1.0):
            """Generate a sinusoidal input"""
            steps = int(duration / dt)
            t = np.arange(steps) * dt
            return offset + amplitude * np.sin(2 * np.pi * frequency * t)
            
        @staticmethod
        def poisson(duration, rate, dt=1.0):
            """Generate a Poisson spike train"""
            steps = int(duration / dt)
            return np.random.poisson(rate * dt, steps)
            
        @staticmethod
        def paired_inputs(duration, interval, n_pairs, dt=1.0):
            """Generate paired inputs for STDP testing"""
            steps = int(duration / dt)
            pre_spikes = np.zeros(steps)
            post_spikes = np.zeros(steps)
            
            pair_interval_steps = int(interval / dt)
            
            # Place spike pairs at regular intervals
            for i in range(n_pairs):
                # Each pair is separated by 100ms
                pair_time = i * 100
                pre_time = int(pair_time / dt)
                post_time = pre_time + pair_interval_steps
                
                if pre_time < steps:
                    pre_spikes[pre_time] = 1.0
                
                if post_time < steps:
                    post_spikes[post_time] = 1.0
                    
            return pre_spikes, post_spikes
            
    return InputPatternGenerator()

@pytest.fixture
def test_network():
    """
    Fixture for creating a complete test network with all core neural components integrated
    """
    # Create a small test network with all components
    core = NeuromorphicCore(dimensions=(5, 5, 5), neuron_types=4)
    
    plasticity_engine = NeuroplasticityEngine(
        eta=0.01, alpha=0.001,
        A_plus=STDP_DEFAULT_PARAMS["A_plus"], 
        A_minus=STDP_DEFAULT_PARAMS["A_minus"],
        tau_plus=STDP_DEFAULT_PARAMS["tau_plus"], 
        tau_minus=STDP_DEFAULT_PARAMS["tau_minus"]
    )
    
    pruning_module = SynapticPruningModule(
        theta_prune=0.1, theta_usage=0.1, pruning_rate=0.01
    )
    
    neuromodulation_system = NeuromodulationSystem(
        p_baseline=NEUROMODULATION_DEFAULT_PARAMS["baseline_levels"],
        delta_p=NEUROMODULATION_DEFAULT_PARAMS["modulation_strength"],
        tau_m=NEUROMODULATION_DEFAULT_PARAMS["tau_m"]
    )
    
    biological_timing = BiologicalTimingCircuits(
        BIOLOGICAL_TIMING_DEFAULT_PARAMS["oscillators"],
        BIOLOGICAL_TIMING_DEFAULT_PARAMS["coupling"]
    )
    
    # Connect all components to the core
    core.set_neuroplasticity_engine(plasticity_engine)
    core.set_synaptic_pruning_module(pruning_module)
    core.set_neuromodulation_system(neuromodulation_system)
    core.set_biological_timing_circuits(biological_timing)
    
    return core

# Validation functions for mathematical models
def validate_lif_dynamics(neuron, inputs, expected_v, dt=0.1, tolerance=1e-6):
    """
    Validate that the LIF neuron dynamics match the mathematical model:
    τ dV/dt = -(V - V_rest) + RI
    
    Parameters:
        neuron: LIF neuron instance
        inputs: Input current trace
        expected_v: Expected membrane potential trace
        dt: Time step
        tolerance: Tolerance for comparison
    
    Returns:
        True if the dynamics match the mathematical model, False otherwise
    """
    # Reset neuron state
    if hasattr(neuron, 'v'):
        neuron.v = getattr(neuron, 'v_rest', 0.0)
    if hasattr(neuron, 'refractory_time'):
        neuron.refractory_time = 0
    
    # Capture actual membrane potential trace
    v_trace = []
    
    for I in inputs:
        if hasattr(neuron, 'update'):
            neuron.update(I, dt)
            v_trace.append(getattr(neuron, 'v', 0.0))
        else:
            # Fallback: simple integration
            v_trace.append(0.0)
    
    # Compare with expected trace
    v_trace = np.array(v_trace)
    expected_v = np.array(expected_v)
    
    # Check if the traces match within tolerance
    return np.allclose(v_trace, expected_v, atol=tolerance)

def validate_stdp_rule(stdp, t_pre, t_post, expected_delta_w, tolerance=1e-6):
    """
    Validate that the STDP rule matches the mathematical model:
    Δw_ij = {
        A_+ exp(-Δt/τ_+) if Δt > 0
        -A_- exp(Δt/τ_-) if Δt < 0
    }
    
    Parameters:
        stdp: STDP mechanism instance
        t_pre: Presynaptic spike time
        t_post: Postsynaptic spike time
        expected_delta_w: Expected weight change
        tolerance: Tolerance for comparison
    
    Returns:
        True if the STDP rule matches the mathematical model, False otherwise
    """
    if hasattr(stdp, 'compute_weight_change'):
        actual_delta_w = stdp.compute_weight_change(t_pre, t_post)
    else:
        # Fallback: compute using standard STDP rule
        delta_t = t_post - t_pre
        A_plus = getattr(stdp, 'A_plus', 0.1)
        A_minus = getattr(stdp, 'A_minus', -0.1)
        tau_plus = getattr(stdp, 'tau_plus', 20.0)
        tau_minus = getattr(stdp, 'tau_minus', 20.0)
        
        if delta_t > 0:
            actual_delta_w = A_plus * np.exp(-delta_t / tau_plus)
        else:
            actual_delta_w = A_minus * np.exp(delta_t / tau_minus)
    
    return abs(actual_delta_w - expected_delta_w) < tolerance

def validate_neuromodulation(neuromod_system, inputs, expected_levels, dt=1.0, tolerance=1e-6):
    """
    Validate that the neuromodulation dynamics match the mathematical model:
    dm/dt = (-m(t) + I(t)) / τ_m
    
    Parameters:
        neuromod_system: NeuromodulationSystem instance
        inputs: Dictionary of input signals for each neuromodulator
        expected_levels: Dictionary of expected neuromodulator levels
        dt: Time step
        tolerance: Tolerance for comparison
    
    Returns:
        True if the dynamics match the mathematical model, False otherwise
    """
    # Reset neuromodulator levels
    if hasattr(neuromod_system, 'modulators'):
        for modulator in neuromod_system.modulators:
            if hasattr(neuromod_system.modulators[modulator], 'level'):
                baseline = neuromod_system.p_baseline.get(modulator, 1.0)
                neuromod_system.modulators[modulator].level = baseline
    
    # Apply inputs and record actual levels
    actual_levels = {}
    for modulator in inputs.keys():
        actual_levels[modulator] = []
    
    steps = len(next(iter(inputs.values())))
    for step in range(steps):
        current_inputs = {mod: inputs[mod][step] for mod in inputs}
        
        if hasattr(neuromod_system, 'update_levels'):
            neuromod_system.update_levels(current_inputs, dt)
        
        for modulator in inputs.keys():
            if hasattr(neuromod_system, 'modulators') and modulator in neuromod_system.modulators:
                level = getattr(neuromod_system.modulators[modulator], 'level', 1.0)
            else:
                level = 1.0  # Fallback
            actual_levels[modulator].append(level)
    
    # Compare with expected levels
    for modulator in expected_levels:
        if modulator in actual_levels:
            if not np.allclose(actual_levels[modulator], expected_levels[modulator], atol=tolerance):
                return False
    
    return True

def validate_oscillator_dynamics(oscillator, duration, dt, expected_phases, tolerance=1e-6):
    """
    Validate that the neural oscillator dynamics match the mathematical model:
    phase(t+dt) = phase(t) + 2π * frequency * dt
    
    Parameters:
        oscillator: NeuralOscillator instance
        duration: Duration of simulation
        dt: Time step
        expected_phases: Expected phase trace
        tolerance: Tolerance for comparison
    
    Returns:
        True if the dynamics match the mathematical model, False otherwise
    """
    # Reset oscillator
    if hasattr(oscillator, 'phase'):
        oscillator.phase = getattr(oscillator, 'initial_phase', 0.0)
    if hasattr(oscillator, 'time'):
        oscillator.time = 0.0
    
    # Capture actual phase trace
    phase_trace = []
    
    steps = int(duration / dt)
    for _ in range(steps):
        if hasattr(oscillator, 'update'):
            oscillator.update(dt)
        
        # Normalize phase to [0, 2π)
        phase = getattr(oscillator, 'phase', 0.0) % (2 * np.pi)
        phase_trace.append(phase)
    
    # Compare with expected trace
    phase_trace = np.array(phase_trace)
    expected_phases = np.array(expected_phases)
    
    # Check if the traces match within tolerance
    return np.allclose(phase_trace, expected_phases, atol=tolerance)

# Create analytical solutions for test validation
def analytical_lif(v_rest, v_reset, v_threshold, tau, R, I, dt, steps, refractory_period=0):
    """
    Analytical solution for LIF neuron dynamics for constant input
    """
    v_trace = np.zeros(steps)
    v = v_rest if v_reset is None else v_reset
    refractory_counter = 0
    
    for i in range(steps):
        if refractory_counter > 0:
            refractory_counter -= 1
            v_trace[i] = v_reset
            continue
            
        # Update membrane potential
        dv = (-(v - v_rest) + R * I) * dt / tau
        v += dv
        
        # Check for spike
        if v >= v_threshold:
            v = v_reset
            refractory_counter = refractory_period
            
        v_trace[i] = v
        
    return v_trace

def analytical_stdp(A_plus, A_minus, tau_plus, tau_minus, delta_t):
    """
    Analytical solution for STDP weight change based on spike timing difference
    """
    if delta_t > 0:
        return A_plus * np.exp(-delta_t / tau_plus)
    else:
        return A_minus * np.exp(delta_t / tau_minus)

def analytical_neuromodulator(initial_level, p_baseline, tau_m, input_signal, dt, steps):
    """
    Analytical solution for neuromodulator dynamics
    """
    level_trace = np.zeros(steps)
    level = initial_level
    
    for i in range(steps):
        # Update level based on differential equation
        dl = (-level + p_baseline + input_signal[i]) / tau_m * dt
        level += dl
        level_trace[i] = level
        
    return level_trace

def analytical_oscillator(frequency, initial_phase, dt, steps):
    """
    Analytical solution for neural oscillator phase
    """
    time = np.arange(steps) * dt
    phases = (initial_phase + 2 * np.pi * frequency * time) % (2 * np.pi)
    return phases

class TestUtils:
    """
    Utilities for testing Core Neural component functionality
    """
    @staticmethod
    def generate_spike_train(rate, duration, dt=1.0, seed=None):
        """
        Generate a Poisson spike train.
        
        Parameters:
            rate: Firing rate in Hz
            duration: Duration in ms
            dt: Time step in ms
            seed: Random seed for reproducibility
            
        Returns:
            Binary array with spikes (1) and no spikes (0)
        """
        if seed is not None:
            np.random.seed(seed)
            
        steps = int(duration / dt)
        spike_prob = rate * dt / 1000.0  # Convert to probability per time step
        return np.random.rand(steps) < spike_prob
    
    @staticmethod
    def compute_firing_rate(spike_train, dt=1.0):
        """
        Compute the firing rate from a spike train.
        
        Parameters:
            spike_train: Binary array with spikes (1) and no spikes (0)
            dt: Time step in ms
            
        Returns:
            Firing rate in Hz
        """
        n_spikes = np.sum(spike_train)
        duration_sec = len(spike_train) * dt / 1000.0  # Convert to seconds
        return n_spikes / duration_sec if duration_sec > 0 else 0
    
    @staticmethod
    def create_connectivity_matrix(n_neurons, connection_prob=0.1, weight_mean=0.5, weight_std=0.1, seed=None):
        """
        Create a random connectivity matrix.
        
        Parameters:
            n_neurons: Number of neurons
            connection_prob: Probability of connection between neurons
            weight_mean: Mean of the weight distribution
            weight_std: Standard deviation of the weight distribution
            seed: Random seed for reproducibility
            
        Returns:
            Connectivity matrix as a sparse CSR matrix
        """
        if seed is not None:
            np.random.seed(seed)
            
        # Generate connection mask
        mask = np.random.rand(n_neurons, n_neurons) < connection_prob
        
        # No self-connections
        np.fill_diagonal(mask, 0)
        
        # Generate weights
        weights = np.random.normal(weight_mean, weight_std, (n_neurons, n_neurons))
        weights = weights * mask
        
        # Convert to sparse matrix
        return scipy.sparse.csr_matrix(weights)
    
    @staticmethod
    def simulate_dopamine_reward(duration, reward_times, reward_amplitude=1.0, baseline=0.0, tau_decay=100.0, dt=1.0):
        """
        Simulate dopamine release in response to rewards.
        
        Parameters:
            duration: Duration in ms
            reward_times: List of reward times in ms
            reward_amplitude: Amplitude of dopamine response
            baseline: Baseline dopamine level
            tau_decay: Decay time constant in ms
            dt: Time step in ms
            
        Returns:
            Array of dopamine levels over time
        """
        steps = int(duration / dt)
        dopamine = np.ones(steps) * baseline
        
        for reward_time in reward_times:
            reward_step = int(reward_time / dt)
            if reward_step < steps:
                # Add instantaneous reward
                dopamine[reward_step] += reward_amplitude
                
                # Exponential decay
                decay_steps = steps - reward_step - 1
                if decay_steps > 0:
                    t = np.arange(1, decay_steps + 1) * dt
                    decay = reward_amplitude * np.exp(-t / tau_decay)
                    dopamine[reward_step+1:] += decay[:len(dopamine[reward_step+1:])]
        
        return dopamine

# Export everything we need for the tests
__all__ = [
    "NeuromorphicCore", "NeuroplasticityEngine", "SynapticPruningModule", 
    "NeuromodulationSystem", "BiologicalTimingCircuits", "STDPMechanism",
    "HomeostasisMechanism", "StructuralPlasticityMechanism", "NeuralOscillator",
    "WeightBasedPruning", "ActivityBasedPruning", "RedundancyBasedPruning",
    "Neuromodulator", "ModulationProfile", "PhaseAmplitudeCoupling",
    "NetworkStateRecorder", "TestUtils",
    "validate_lif_dynamics", "validate_stdp_rule", "validate_neuromodulation",
    "validate_oscillator_dynamics",
    "analytical_lif", "analytical_stdp", "analytical_neuromodulator", "analytical_oscillator",
    "DEFAULT_NEURON_PARAMS", "STDP_DEFAULT_PARAMS", "NEUROMODULATION_DEFAULT_PARAMS",
    "BIOLOGICAL_TIMING_DEFAULT_PARAMS"
]