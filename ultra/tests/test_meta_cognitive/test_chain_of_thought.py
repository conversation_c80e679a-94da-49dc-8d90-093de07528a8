#!/usr/bin/env python3
"""
ULTRA: Ultimate Learning & Thought Reasoning Architecture
Test Suite for Chain of Thought and Multi-Path Reasoning Module

This module implements comprehensive tests for the chain of thought reasoning
component of the Meta-Cognitive System, including:
- Multi-Path Chain of Thought exploration
- Tree of Thought hierarchical reasoning
- Reasoning Graph construction and analysis
- Self-Critique Loop integration
- Dynamic resource allocation algorithms

Mathematical foundations based on ULTRA system specifications.
"""

import unittest
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Tuple, Optional, Any, Union, Set
from dataclasses import dataclass, field
from enum import Enum
import logging
import json
import math
import time
import heapq
import networkx as nx
from collections import defaultdict, deque
from copy import deepcopy
import uuid
import re
from sklearn.metrics.pairwise import cosine_similarity
from transformers import AutoTokenizer, AutoModel
import scipy.stats as stats
from scipy.optimize import minimize
import warnings
warnings.filterwarnings('ignore')

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ReasoningStepType(Enum):
    """Types of reasoning steps in chain of thought."""
    PREMISE = "premise"
    INFERENCE = "inference"
    DEDUCTION = "deduction"
    INDUCTION = "induction"
    ABDUCTION = "abduction"
    HYPOTHESIS = "hypothesis"
    EVIDENCE = "evidence"
    CONCLUSION = "conclusion"
    ASSUMPTION = "assumption"
    CONTRADICTION = "contradiction"


class ReasoningPathStatus(Enum):
    """Status of reasoning paths."""
    ACTIVE = "active"
    COMPLETE = "complete"
    PRUNED = "pruned"
    ERROR = "error"
    SUSPENDED = "suspended"


class CritiqueType(Enum):
    """Types of reasoning critiques."""
    LOGICAL_VALIDITY = "logical_validity"
    FACTUAL_ACCURACY = "factual_accuracy"
    COMPLETENESS = "completeness"
    RELEVANCE = "relevance"
    COHERENCE = "coherence"
    CONSISTENCY = "consistency"
    BIAS_DETECTION = "bias_detection"
    ALTERNATIVE_CONSIDERATION = "alternative_consideration"


@dataclass
class ChainOfThoughtStep:
    """Individual step in a chain of thought reasoning process."""
    step_id: str
    step_type: ReasoningStepType
    content: str
    confidence: float
    dependencies: List[str] = field(default_factory=list)
    supporting_evidence: List[str] = field(default_factory=list)
    timestamp: float = field(default_factory=time.time)
    logical_strength: float = 0.0
    factual_accuracy: float = 0.0
    relevance_score: float = 0.0
    
    def __post_init__(self):
        """Validate and normalize step parameters."""
        self.confidence = max(0.0, min(1.0, self.confidence))
        self.logical_strength = max(0.0, min(1.0, self.logical_strength))
        self.factual_accuracy = max(0.0, min(1.0, self.factual_accuracy))
        self.relevance_score = max(0.0, min(1.0, self.relevance_score))
        
        if not self.step_id:
            self.step_id = str(uuid.uuid4())


@dataclass 
class ReasoningPath:
    """Complete reasoning path containing multiple steps."""
    path_id: str
    steps: List[ChainOfThoughtStep] = field(default_factory=list)
    status: ReasoningPathStatus = ReasoningPathStatus.ACTIVE
    overall_confidence: float = 0.0
    logical_consistency: float = 0.0
    completeness_score: float = 0.0
    resource_allocation: float = 0.0
    creation_time: float = field(default_factory=time.time)
    last_updated: float = field(default_factory=time.time)
    parent_path: Optional[str] = None
    branch_point: Optional[int] = None
    
    def __post_init__(self):
        """Initialize path with validation."""
        self.overall_confidence = max(0.0, min(1.0, self.overall_confidence))
        self.logical_consistency = max(0.0, min(1.0, self.logical_consistency))
        self.completeness_score = max(0.0, min(1.0, self.completeness_score))
        self.resource_allocation = max(0.0, min(1.0, self.resource_allocation))
        
        if not self.path_id:
            self.path_id = str(uuid.uuid4())
    
    def add_step(self, step: ChainOfThoughtStep) -> None:
        """Add a reasoning step to the path."""
        self.steps.append(step)
        self.last_updated = time.time()
        self._update_metrics()
    
    def _update_metrics(self) -> None:
        """Update path-level metrics based on constituent steps."""
        if not self.steps:
            return
            
        # Calculate overall confidence as weighted average
        confidences = [step.confidence for step in self.steps]
        weights = [step.relevance_score + 0.1 for step in self.steps]  # Add small epsilon
        self.overall_confidence = np.average(confidences, weights=weights)
        
        # Calculate logical consistency
        logical_scores = [step.logical_strength for step in self.steps]
        self.logical_consistency = np.mean(logical_scores)
        
        # Calculate completeness based on step types
        step_types = set(step.step_type for step in self.steps)
        required_types = {ReasoningStepType.PREMISE, ReasoningStepType.INFERENCE, ReasoningStepType.CONCLUSION}
        self.completeness_score = len(step_types & required_types) / len(required_types)


@dataclass
class ReasoningNode:
    """Node in a reasoning graph representing a concept or statement."""
    node_id: str
    content: str
    node_type: ReasoningStepType
    confidence: float
    supporting_evidence: List[str] = field(default_factory=list)
    creation_time: float = field(default_factory=time.time)
    activation_level: float = 0.0
    visit_count: int = 0
    
    def __post_init__(self):
        """Validate node parameters."""
        self.confidence = max(0.0, min(1.0, self.confidence))
        self.activation_level = max(0.0, min(1.0, self.activation_level))
        
        if not self.node_id:
            self.node_id = str(uuid.uuid4())


@dataclass
class ReasoningEdge:
    """Edge in a reasoning graph representing logical relationships."""
    edge_id: str
    source_id: str
    target_id: str
    relationship_type: str
    strength: float
    confidence: float
    creation_time: float = field(default_factory=time.time)
    
    def __post_init__(self):
        """Validate edge parameters."""
        self.strength = max(0.0, min(1.0, self.strength))
        self.confidence = max(0.0, min(1.0, self.confidence))
        
        if not self.edge_id:
            self.edge_id = str(uuid.uuid4())


@dataclass
class CritiqueResult:
    """Result of reasoning critique analysis."""
    critique_id: str
    critique_type: CritiqueType
    target_step_id: str
    severity: float  # 0.0 = no issue, 1.0 = critical issue
    description: str
    suggested_improvements: List[str] = field(default_factory=list)
    confidence: float = 0.0
    
    def __post_init__(self):
        """Validate critique parameters."""
        self.severity = max(0.0, min(1.0, self.severity))
        self.confidence = max(0.0, min(1.0, self.confidence))
        
        if not self.critique_id:
            self.critique_id = str(uuid.uuid4())


class PathEvaluator:
    """Evaluates reasoning paths using multiple criteria."""
    
    def __init__(self, validity_weight: float = 0.3, progress_weight: float = 0.4, diversity_weight: float = 0.3):
        """
        Initialize path evaluator with configurable weights.
        
        Mathematical model: V(p) = w1*V_validity(p) + w2*V_progress(p) + w3*V_diversity(p)
        """
        self.validity_weight = validity_weight
        self.progress_weight = progress_weight
        self.diversity_weight = diversity_weight
        
        # Normalize weights
        total_weight = validity_weight + progress_weight + diversity_weight
        self.validity_weight /= total_weight
        self.progress_weight /= total_weight
        self.diversity_weight /= total_weight
        
        # Reference paths for diversity calculation
        self.reference_paths = []
    
    def evaluate_path(self, path: ReasoningPath, problem_context: str = "") -> float:
        """
        Comprehensive path evaluation using ULTRA mathematical model.
        
        Returns:
            Float score between 0.0 and 1.0 representing path quality
        """
        validity_score = self._calculate_validity(path)
        progress_score = self._calculate_progress(path, problem_context)
        diversity_score = self._calculate_diversity(path)
        
        total_score = (
            self.validity_weight * validity_score +
            self.progress_weight * progress_score +
            self.diversity_weight * diversity_score
        )
        
        return min(1.0, max(0.0, total_score))
    
    def _calculate_validity(self, path: ReasoningPath) -> float:
        """
        Calculate logical validity of reasoning path.
        
        Mathematical model:
        validity = (Σ logical_strength_i * confidence_i) / Σ confidence_i
        """
        if not path.steps:
            return 0.0  # Empty paths have no validity
        
        weighted_validity = 0.0
        total_confidence = 0.0
        
        for step in path.steps:
            weighted_validity += step.logical_strength * step.confidence
            total_confidence += step.confidence
        
        if total_confidence == 0.0:
            return 0.0
        
        return weighted_validity / total_confidence
    
    def _calculate_progress(self, path: ReasoningPath, problem_context: str) -> float:
        """
        Calculate progress toward problem solution.
        
        Mathematical model based on step completeness and relevance.
        """
        if not path.steps:
            return 0.0  # Empty paths have no progress
        
        # Step type completeness
        step_types = set(step.step_type for step in path.steps)
        essential_types = {ReasoningStepType.PREMISE, ReasoningStepType.INFERENCE, ReasoningStepType.CONCLUSION}
        type_completeness = len(step_types & essential_types) / len(essential_types)
        
        # Average relevance score
        relevance_scores = [step.relevance_score for step in path.steps]
        avg_relevance = np.mean(relevance_scores)
        
        # Length penalty for overly long paths
        length_penalty = max(0.5, 1.0 - (len(path.steps) - 5) * 0.05)
        
        # Conclusion presence bonus
        conclusion_bonus = 0.2 if any(step.step_type == ReasoningStepType.CONCLUSION for step in path.steps) else 0.0
        
        progress_score = (type_completeness * 0.4 + avg_relevance * 0.4 + conclusion_bonus) * length_penalty
        
        return min(1.0, max(0.0, progress_score))
    
    def _calculate_diversity(self, path: ReasoningPath) -> float:
        """
        Calculate diversity compared to existing paths.
        
        Mathematical model based on content similarity to reference paths.
        """
        if not path.steps:
            return 0.0  # Empty paths have no diversity value
        
        if not self.reference_paths:
            return 1.0  # Maximum diversity if no reference
        
        path_content = " ".join(step.content for step in path.steps)
        
        max_similarity = 0.0
        for ref_path in self.reference_paths:
            ref_content = " ".join(step.content for step in ref_path.steps)
            similarity = self._calculate_text_similarity(path_content, ref_content)
            max_similarity = max(max_similarity, similarity)
        
        # Diversity is inverse of similarity
        diversity_score = 1.0 - max_similarity
        
        return max(0.0, min(1.0, diversity_score))
    
    def _calculate_text_similarity(self, text1: str, text2: str) -> float:
        """Calculate similarity between two texts using word overlap."""
        if not text1 or not text2:
            return 0.0
        
        words1 = set(text1.lower().split())
        words2 = set(text2.lower().split())
        
        if not words1 or not words2:
            return 0.0
        
        intersection = len(words1 & words2)
        union = len(words1 | words2)
        
        return intersection / union if union > 0 else 0.0
    
    def add_reference_path(self, path: ReasoningPath) -> None:
        """Add a path to reference set for diversity calculation."""
        self.reference_paths.append(deepcopy(path))
        
        # Limit reference paths to prevent memory issues
        if len(self.reference_paths) > 50:
            self.reference_paths.pop(0)


class MultiPathChainOfThought:
    """
    Multi-path chain of thought reasoning engine implementing ULTRA specifications.
    
    Mathematical foundation:
    - Resource allocation: B_i = B_total * exp(V(p_i)/τ) / Σ_j exp(V(p_j)/τ)
    - Path evaluation: V(p) = w1*V_validity(p) + w2*V_progress(p) + w3*V_diversity(p)
    """
    
    def __init__(self, max_paths: int = 5, max_steps: int = 10, temperature: float = 1.0, 
                 total_budget: float = 1.0, pruning_threshold: float = 0.2):
        """Initialize multi-path chain of thought system."""
        self.max_paths = max_paths
        self.max_steps = max_steps
        self.temperature = temperature
        self.total_budget = total_budget
        self.pruning_threshold = pruning_threshold
        
        # Core components
        self.active_paths: List[ReasoningPath] = []
        self.completed_paths: List[ReasoningPath] = []
        self.pruned_paths: List[ReasoningPath] = []
        
        self.path_evaluator = PathEvaluator()
        
        # Resource allocation tracking
        self.resource_allocation_history = []
        
        logger.info(f"MultiPathChainOfThought initialized with max_paths={max_paths}")
    
    def generate_reasoning_paths(self, problem_statement: str, context: str = "") -> List[ReasoningPath]:
        """
        Generate multiple reasoning paths for a given problem.
        
        Args:
            problem_statement: The problem to reason about
            context: Additional context information
            
        Returns:
            List of completed reasoning paths
        """
        # Initialize with problem statement
        initial_step = ChainOfThoughtStep(
            step_id=str(uuid.uuid4()),
            step_type=ReasoningStepType.PREMISE,
            content=problem_statement,
            confidence=1.0,
            logical_strength=1.0,
            factual_accuracy=0.9,
            relevance_score=1.0
        )
        
        # Create initial paths
        for i in range(min(3, self.max_paths)):  # Start with fewer paths
            path = ReasoningPath(path_id=str(uuid.uuid4()))
            path.add_step(deepcopy(initial_step))
            self.active_paths.append(path)
        
        # Main reasoning loop
        for step_num in range(self.max_steps):
            if not self.active_paths:
                break
            
            # Allocate resources to paths
            self._allocate_resources()
            
            # Expand each active path
            new_paths = []
            for path in self.active_paths:
                if path.resource_allocation > 0.1:  # Only expand well-resourced paths
                    expanded_paths = self._expand_path(path, problem_statement, context)
                    new_paths.extend(expanded_paths)
            
            # Add new paths and manage path count
            self.active_paths.extend(new_paths)
            self._manage_path_population()
            
            # Check for completion
            completed_this_round = []
            for path in self.active_paths:
                if self._is_path_complete(path):
                    path.status = ReasoningPathStatus.COMPLETE
                    completed_this_round.append(path)
            
            # Move completed paths
            for path in completed_this_round:
                self.active_paths.remove(path)
                self.completed_paths.append(path)
                self.path_evaluator.add_reference_path(path)
            
            logger.debug(f"Step {step_num}: {len(self.active_paths)} active, {len(self.completed_paths)} completed")
        
        # Move any remaining active paths to completed
        for path in self.active_paths:
            if len(path.steps) >= 3:  # Minimum viable path
                path.status = ReasoningPathStatus.COMPLETE
                self.completed_paths.append(path)
        
        self.active_paths.clear()
        
        # Sort by overall quality
        self.completed_paths.sort(key=lambda p: self.path_evaluator.evaluate_path(p, problem_statement), reverse=True)
        
        logger.info(f"Generated {len(self.completed_paths)} reasoning paths")
        return self.completed_paths
    
    def _allocate_resources(self) -> None:
        """
        Allocate computational resources using ULTRA softmax allocation.
        
        Mathematical model: B_i = B_total * exp(V(p_i)/τ) / Σ_j exp(V(p_j)/τ)
        """
        if not self.active_paths:
            return
        
        # Calculate path values
        path_values = []
        for path in self.active_paths:
            value = self.path_evaluator.evaluate_path(path)
            path_values.append(value)
        
        # Apply softmax allocation
        exp_values = np.exp(np.array(path_values) / self.temperature)
        probabilities = exp_values / np.sum(exp_values)
        
        # Allocate resources
        for i, path in enumerate(self.active_paths):
            path.resource_allocation = self.total_budget * probabilities[i]
        
        # Track allocation history
        self.resource_allocation_history.append({
            'step': len(self.resource_allocation_history),
            'allocations': probabilities.tolist(),
            'path_values': path_values
        })
    
    def _expand_path(self, path: ReasoningPath, problem_statement: str, context: str) -> List[ReasoningPath]:
        """
        Expand a reasoning path by generating next possible steps.
        
        Returns:
            List of new paths created from expansion
        """
        if not path.steps:
            return []
        
        last_step = path.steps[-1]
        new_paths = []
        
        # Generate possible next steps based on current step type
        next_step_candidates = self._generate_next_steps(last_step, problem_statement, context)
        
        # Create new paths for each candidate (branching)
        for i, candidate_step in enumerate(next_step_candidates[:3]):  # Limit branching
            if i == 0:
                # Use original path for first candidate
                path.add_step(candidate_step)
            else:
                # Create new branch for other candidates
                new_path = deepcopy(path)
                new_path.path_id = str(uuid.uuid4())
                new_path.parent_path = path.path_id
                new_path.branch_point = len(path.steps) - 1
                new_path.add_step(candidate_step)
                new_paths.append(new_path)
        
        return new_paths
    
    def _generate_next_steps(self, current_step: ChainOfThoughtStep, problem_statement: str, context: str) -> List[ChainOfThoughtStep]:
        """Generate possible next reasoning steps."""
        candidates = []
        
        # Step generation based on current step type
        if current_step.step_type == ReasoningStepType.PREMISE:
            # From premise, we can make inferences or state hypotheses
            candidates.extend([
                ChainOfThoughtStep(
                    step_id=str(uuid.uuid4()),
                    step_type=ReasoningStepType.INFERENCE,
                    content=f"Based on the premise, we can infer that {self._generate_inference_content(current_step.content)}",
                    confidence=0.7,
                    dependencies=[current_step.step_id],
                    logical_strength=0.8,
                    factual_accuracy=0.7,
                    relevance_score=0.8
                ),
                ChainOfThoughtStep(
                    step_id=str(uuid.uuid4()),
                    step_type=ReasoningStepType.HYPOTHESIS,
                    content=f"This suggests the hypothesis that {self._generate_hypothesis_content(current_step.content)}",
                    confidence=0.6,
                    dependencies=[current_step.step_id],
                    logical_strength=0.6,
                    factual_accuracy=0.6,
                    relevance_score=0.7
                )
            ])
        
        elif current_step.step_type == ReasoningStepType.INFERENCE:
            # From inference, we can deduce conclusions or gather evidence
            candidates.extend([
                ChainOfThoughtStep(
                    step_id=str(uuid.uuid4()),
                    step_type=ReasoningStepType.DEDUCTION,
                    content=f"Therefore, we can deduce that {self._generate_deduction_content(current_step.content)}",
                    confidence=0.8,
                    dependencies=[current_step.step_id],
                    logical_strength=0.9,
                    factual_accuracy=0.7,
                    relevance_score=0.8
                ),
                ChainOfThoughtStep(
                    step_id=str(uuid.uuid4()),
                    step_type=ReasoningStepType.EVIDENCE,
                    content=f"Supporting evidence includes {self._generate_evidence_content(current_step.content)}",
                    confidence=0.7,
                    dependencies=[current_step.step_id],
                    logical_strength=0.7,
                    factual_accuracy=0.8,
                    relevance_score=0.7
                )
            ])
        
        elif current_step.step_type in [ReasoningStepType.DEDUCTION, ReasoningStepType.EVIDENCE]:
            # These can lead to conclusions
            candidates.append(
                ChainOfThoughtStep(
                    step_id=str(uuid.uuid4()),
                    step_type=ReasoningStepType.CONCLUSION,
                    content=f"In conclusion, {self._generate_conclusion_content(current_step.content, problem_statement)}",
                    confidence=0.8,
                    dependencies=[current_step.step_id],
                    logical_strength=0.8,
                    factual_accuracy=0.7,
                    relevance_score=0.9
                )
            )
        
        # Add noise/randomness for diversity
        for candidate in candidates:
            candidate.confidence += np.random.normal(0, 0.1)
            candidate.confidence = max(0.1, min(0.9, candidate.confidence))
        
        return candidates
    
    def _generate_inference_content(self, premise_content: str) -> str:
        """Generate inference content based on premise."""
        inference_templates = [
            "the underlying factors involve multiple variables",
            "there are causal relationships at play",
            "the situation requires careful analysis",
            "multiple perspectives should be considered",
            "the evidence points to several possibilities"
        ]
        return np.random.choice(inference_templates)
    
    def _generate_hypothesis_content(self, content: str) -> str:
        """Generate hypothesis content."""
        hypothesis_templates = [
            "the primary factor is systematic rather than random",
            "the relationship follows a predictable pattern",
            "the outcome depends on specific conditions",
            "there exists an optimal solution approach",
            "the phenomenon has measurable characteristics"
        ]
        return np.random.choice(hypothesis_templates)
    
    def _generate_deduction_content(self, inference_content: str) -> str:
        """Generate deduction content from inference."""
        deduction_templates = [
            "the logical consequence is a specific outcome",
            "the necessary result follows from the premises",
            "the implications are clearly defined",
            "the conclusion follows necessarily",
            "the logical chain leads to a definite result"
        ]
        return np.random.choice(deduction_templates)
    
    def _generate_evidence_content(self, content: str) -> str:
        """Generate supporting evidence content."""
        evidence_templates = [
            "empirical data from multiple sources",
            "documented cases and examples",
            "statistical analysis and trends",
            "expert opinions and research findings",
            "historical precedents and patterns"
        ]
        return np.random.choice(evidence_templates)
    
    def _generate_conclusion_content(self, content: str, problem_statement: str) -> str:
        """Generate conclusion content."""
        conclusion_templates = [
            "the evidence supports a comprehensive solution",
            "the analysis leads to actionable recommendations",
            "the reasoning demonstrates a clear path forward",
            "the investigation reveals important insights",
            "the systematic approach yields definitive results"
        ]
        return np.random.choice(conclusion_templates)
    
    def _manage_path_population(self) -> None:
        """Manage the number of active paths through pruning."""
        if len(self.active_paths) <= self.max_paths:
            return
        
        try:
            # Evaluate all paths safely
            path_scores = []
            for path in self.active_paths:
                if isinstance(path, ReasoningPath):
                    try:
                        score = self.path_evaluator.evaluate_path(path)
                        path_scores.append((score, path))
                    except Exception as e:
                        logger.warning(f"Error evaluating path {path.path_id}: {e}")
                        path_scores.append((0.0, path))  # Give low score to problematic paths
            
            if not path_scores:
                return
            
            # Sort by score (descending)
            path_scores.sort(key=lambda x: x[0], reverse=True)
            
            # Keep top paths and prune the rest
            paths_to_keep = [path for _, path in path_scores[:self.max_paths]]
            paths_to_prune = [path for _, path in path_scores[self.max_paths:]]
            
            # Prune low-scoring paths
            for path in paths_to_prune:
                try:
                    score = next((score for score, p in path_scores if p is path), 0.0)
                    if score < self.pruning_threshold:
                        path.status = ReasoningPathStatus.PRUNED
                        self.pruned_paths.append(path)
                except Exception as e:
                    logger.warning(f"Error pruning path: {e}")
            
            # Update active paths safely
            self.active_paths = [path for path in self.active_paths 
                               if path.status == ReasoningPathStatus.ACTIVE and path in paths_to_keep]
            
        except Exception as e:
            logger.error(f"Error managing path population: {e}")
            # Fallback: just limit the number of paths
            if len(self.active_paths) > self.max_paths:
                self.active_paths = self.active_paths[:self.max_paths]
    
    def _is_path_complete(self, path: ReasoningPath) -> bool:
        """Check if a reasoning path is complete."""
        if len(path.steps) < 3:  # Minimum path length
            return False
        
        # Check for conclusion step
        has_conclusion = any(step.step_type == ReasoningStepType.CONCLUSION for step in path.steps)
        
        # Check for minimum confidence
        avg_confidence = np.mean([step.confidence for step in path.steps])
        
        return has_conclusion and avg_confidence > 0.5
    
    def get_best_path(self) -> Optional[ReasoningPath]:
        """Get the highest-scoring completed path."""
        if not self.completed_paths:
            return None
        
        try:
            best_path = None
            best_score = -1.0
            
            for path in self.completed_paths:
                if isinstance(path, ReasoningPath):
                    score = self.path_evaluator.evaluate_path(path)
                    if score > best_score:
                        best_score = score
                        best_path = path
            
            return best_path
        except Exception as e:
            logger.warning(f"Error finding best path: {e}")
            # Return first valid path as fallback
            for path in self.completed_paths:
                if isinstance(path, ReasoningPath):
                    return path
            return None
    
    def get_reasoning_statistics(self) -> Dict[str, Any]:
        """Get comprehensive statistics about the reasoning process."""
        return {
            'total_paths_generated': len(self.completed_paths) + len(self.pruned_paths),
            'completed_paths': len(self.completed_paths),
            'pruned_paths': len(self.pruned_paths),
            'average_path_length': np.mean([len(p.steps) for p in self.completed_paths]) if self.completed_paths else 0,
            'average_confidence': np.mean([p.overall_confidence for p in self.completed_paths]) if self.completed_paths else 0,
            'resource_allocation_history': self.resource_allocation_history,
            'path_type_distribution': self._calculate_path_type_distribution()
        }
    
    def _calculate_path_type_distribution(self) -> Dict[str, int]:
        """Calculate distribution of step types across all paths."""
        type_counts = defaultdict(int)
        
        for path in self.completed_paths:
            for step in path.steps:
                type_counts[step.step_type.value] += 1
        
        return dict(type_counts)


class TreeOfThoughtNode:
    """Node in the Tree of Thought exploration structure."""
    
    def __init__(self, content: str, step_type: ReasoningStepType, parent: Optional['TreeOfThoughtNode'] = None):
        """Initialize tree node."""
        self.node_id = str(uuid.uuid4())
        self.content = content
        self.step_type = step_type
        self.parent = parent
        self.children: List['TreeOfThoughtNode'] = []
        
        # Node metrics
        self.value_estimate = 0.0
        self.visit_count = 0
        self.confidence = 0.5
        self.exploration_bonus = 0.0
        
        # Tree navigation
        self.depth = 0 if parent is None else parent.depth + 1
        self.is_expanded = False
        self.is_terminal = False
        
        if parent:
            parent.children.append(self)
    
    def calculate_ucb_score(self, exploration_constant: float = 1.4) -> float:
        """
        Calculate Upper Confidence Bound score for node selection.
        
        Mathematical model: UCB = V(n) + c * sqrt(ln(N_parent) / N(n))
        """
        if self.visit_count == 0:
            return float('inf')  # Unvisited nodes have highest priority
        
        if self.parent is None or self.parent.visit_count == 0:
            return self.value_estimate
        
        exploitation_term = self.value_estimate
        exploration_term = exploration_constant * math.sqrt(
            math.log(self.parent.visit_count) / self.visit_count
        )
        
        return exploitation_term + exploration_term
    
    def backpropagate(self, value: float) -> None:
        """Backpropagate value update through the tree."""
        self.visit_count += 1
        
        # Update value estimate using incremental average
        self.value_estimate += (value - self.value_estimate) / self.visit_count
        
        if self.parent:
            self.parent.backpropagate(value)
    
    def get_path_to_root(self) -> List['TreeOfThoughtNode']:
        """Get path from this node to root."""
        path = []
        current = self
        while current:
            path.append(current)
            current = current.parent
        return list(reversed(path))
    
    def is_leaf(self) -> bool:
        """Check if node is a leaf (no children)."""
        return len(self.children) == 0


class TreeOfThoughtExplorer:
    """
    Tree of Thought exploration engine implementing ULTRA specifications.
    
    Mathematical foundation:
    - Node selection: UCB = V(n) + c * sqrt(ln(N_parent) / N(n))
    - Backtracking: p_backtrack = argmax_{p ∈ Ancestors(s)} {V(p) * (1 - Explored(p))}
    """
    
    def __init__(self, max_depth: int = 6, branching_factor: int = 3, exploration_constant: float = 1.4,
                 value_threshold: float = 0.3, diversity_threshold: float = 0.7):
        """Initialize Tree of Thought explorer."""
        self.max_depth = max_depth
        self.branching_factor = branching_factor
        self.exploration_constant = exploration_constant
        self.value_threshold = value_threshold
        self.diversity_threshold = diversity_threshold
        
        self.root: Optional[TreeOfThoughtNode] = None
        self.explored_nodes: List[TreeOfThoughtNode] = []
        self.pruned_nodes: List[TreeOfThoughtNode] = []
        
        # Exploration statistics
        self.total_expansions = 0
        self.total_backtracks = 0
        
        logger.info("TreeOfThoughtExplorer initialized")
    
    def explore(self, problem_statement: str, max_iterations: int = 100) -> List[TreeOfThoughtNode]:
        """
        Explore reasoning space using Tree of Thought algorithm.
        
        Args:
            problem_statement: Initial problem to explore
            max_iterations: Maximum exploration iterations
            
        Returns:
            List of high-value leaf nodes representing complete reasoning paths
        """
        # Initialize root node
        self.root = TreeOfThoughtNode(
            content=problem_statement,
            step_type=ReasoningStepType.PREMISE
        )
        self.root.confidence = 1.0
        self.root.value_estimate = 0.8
        
        current_node = self.root
        
        for iteration in range(max_iterations):
            # Selection phase: find best leaf to expand
            if not current_node.is_terminal and current_node.depth < self.max_depth:
                if not current_node.is_expanded:
                    # Expansion phase
                    self._expand_node(current_node)
                    current_node.is_expanded = True
                    self.total_expansions += 1
                
                # Select best child using UCB
                if current_node.children:
                    current_node = max(current_node.children, key=lambda n: n.calculate_ucb_score(self.exploration_constant))
                else:
                    current_node.is_terminal = True
            
            # Evaluation phase
            if current_node.is_leaf() or current_node.is_terminal:
                value = self._evaluate_node(current_node)
                current_node.backpropagate(value)
                
                # Backtracking decision
                if value < self.value_threshold or current_node.depth >= self.max_depth:
                    backtrack_node = self._find_backtrack_target(current_node)
                    if backtrack_node:
                        current_node = backtrack_node
                        self.total_backtracks += 1
                    else:
                        # Exploration complete
                        break
            
            # Pruning check
            if iteration % 10 == 0:
                self._prune_unpromising_branches()
        
        # Collect high-value paths
        valuable_leaves = self._collect_valuable_leaves()
        
        logger.info(f"Tree exploration completed: {self.total_expansions} expansions, {self.total_backtracks} backtracks")
        return valuable_leaves
    
    def _expand_node(self, node: TreeOfThoughtNode) -> None:
        """Expand a node by generating child nodes."""
        child_contents = self._generate_child_contents(node)
        
        for content, step_type in child_contents:
            child = TreeOfThoughtNode(
                content=content,
                step_type=step_type,
                parent=node
            )
            
            # Initialize child metrics
            child.confidence = max(0.1, node.confidence - 0.1 + np.random.normal(0, 0.05))
            child.value_estimate = self._evaluate_node(child)
    
    def _generate_child_contents(self, node: TreeOfThoughtNode) -> List[Tuple[str, ReasoningStepType]]:
        """Generate content for child nodes based on parent node."""
        children = []
        
        if node.step_type == ReasoningStepType.PREMISE:
            children = [
                ("Let's analyze the key components of this problem", ReasoningStepType.INFERENCE),
                ("We should consider multiple approaches to this issue", ReasoningStepType.HYPOTHESIS),
                ("The evidence suggests several possible explanations", ReasoningStepType.EVIDENCE)
            ]
        
        elif node.step_type == ReasoningStepType.INFERENCE:
            children = [
                ("This leads us to conclude that", ReasoningStepType.DEDUCTION),
                ("Supporting data shows that", ReasoningStepType.EVIDENCE),
                ("An alternative interpretation might be", ReasoningStepType.HYPOTHESIS)
            ]
        
        elif node.step_type == ReasoningStepType.HYPOTHESIS:
            children = [
                ("Testing this hypothesis reveals", ReasoningStepType.EVIDENCE),
                ("The logical implications are", ReasoningStepType.DEDUCTION),
                ("This assumption leads to", ReasoningStepType.INFERENCE)
            ]
        
        elif node.step_type in [ReasoningStepType.DEDUCTION, ReasoningStepType.EVIDENCE]:
            children = [
                ("Therefore, we can conclude", ReasoningStepType.CONCLUSION),
                ("This suggests that", ReasoningStepType.INFERENCE),
                ("However, we should also consider", ReasoningStepType.ASSUMPTION)
            ]
        
        # Limit branching factor
        return children[:self.branching_factor]
    
    def _evaluate_node(self, node: TreeOfThoughtNode) -> float:
        """
        Evaluate node value using multiple criteria.
        
        Mathematical model combining logical strength, depth penalty, and completion bonus.
        """
        # Base logical strength
        logical_strength = node.confidence
        
        # Depth penalty (prefer balanced trees)
        depth_penalty = max(0.0, 1.0 - node.depth * 0.1)
        
        # Completion bonus for conclusion nodes
        completion_bonus = 0.3 if node.step_type == ReasoningStepType.CONCLUSION else 0.0
        
        # Diversity bonus (prefer diverse reasoning)
        diversity_bonus = self._calculate_diversity_bonus(node)
        
        # Path coherence (check logical flow)
        coherence_score = self._calculate_path_coherence(node)
        
        total_value = (
            logical_strength * 0.3 +
            depth_penalty * 0.2 +
            completion_bonus * 0.2 +
            diversity_bonus * 0.15 +
            coherence_score * 0.15
        )
        
        return min(1.0, max(0.0, total_value))
    
    def _calculate_diversity_bonus(self, node: TreeOfThoughtNode) -> float:
        """Calculate diversity bonus for node content."""
        if not self.explored_nodes:
            return 0.5
        
        node_words = set(node.content.lower().split())
        
        max_similarity = 0.0
        for explored_node in self.explored_nodes[-10:]:  # Check recent nodes
            explored_words = set(explored_node.content.lower().split())
            if node_words and explored_words:
                similarity = len(node_words & explored_words) / len(node_words | explored_words)
                max_similarity = max(max_similarity, similarity)
        
        return 1.0 - max_similarity
    
    def _calculate_path_coherence(self, node: TreeOfThoughtNode) -> float:
        """Calculate coherence of path from root to node."""
        path = node.get_path_to_root()
        
        if len(path) < 2:
            return 1.0
        
        # Check step type transitions
        valid_transitions = {
            ReasoningStepType.PREMISE: [ReasoningStepType.INFERENCE, ReasoningStepType.HYPOTHESIS],
            ReasoningStepType.INFERENCE: [ReasoningStepType.DEDUCTION, ReasoningStepType.EVIDENCE, ReasoningStepType.HYPOTHESIS],
            ReasoningStepType.HYPOTHESIS: [ReasoningStepType.EVIDENCE, ReasoningStepType.DEDUCTION, ReasoningStepType.INFERENCE],
            ReasoningStepType.EVIDENCE: [ReasoningStepType.CONCLUSION, ReasoningStepType.INFERENCE],
            ReasoningStepType.DEDUCTION: [ReasoningStepType.CONCLUSION, ReasoningStepType.INFERENCE]
        }
        
        valid_transitions_count = 0
        total_transitions = len(path) - 1
        
        for i in range(total_transitions):
            current_type = path[i].step_type
            next_type = path[i + 1].step_type
            
            if current_type in valid_transitions and next_type in valid_transitions[current_type]:
                valid_transitions_count += 1
        
        return valid_transitions_count / total_transitions if total_transitions > 0 else 1.0
    
    def _find_backtrack_target(self, current_node: TreeOfThoughtNode) -> Optional[TreeOfThoughtNode]:
        """
        Find optimal backtrack target using ULTRA backtracking formula.
        
        Mathematical model: p_backtrack = argmax_{p ∈ Ancestors(s)} {V(p) * (1 - Explored(p))}
        """
        ancestors = []
        current = current_node.parent
        
        while current:
            ancestors.append(current)
            current = current.parent
        
        if not ancestors:
            return None
        
        best_target = None
        best_score = -1.0
        
        for ancestor in ancestors:
            # Calculate exploration completeness
            if ancestor.is_expanded and ancestor.children:
                explored_ratio = sum(1 for child in ancestor.children if child.visit_count > 0) / len(ancestor.children)
            else:
                explored_ratio = 0.0
            
            # Calculate backtrack score
            backtrack_score = ancestor.value_estimate * (1.0 - explored_ratio)
            
            if backtrack_score > best_score:
                best_score = backtrack_score
                best_target = ancestor
        
        return best_target
    
    def _prune_unpromising_branches(self) -> None:
        """Prune branches with consistently low values."""
        if not self.root:
            return
        
        nodes_to_prune = []
        
        def _collect_prunable_nodes(node: TreeOfThoughtNode):
            if (node.visit_count > 3 and 
                node.value_estimate < self.value_threshold and 
                node != self.root):
                nodes_to_prune.append(node)
            
            for child in node.children:
                _collect_prunable_nodes(child)
        
        _collect_prunable_nodes(self.root)
        
        # Prune collected nodes
        for node in nodes_to_prune:
            if node.parent:
                node.parent.children.remove(node)
                self.pruned_nodes.append(node)
    
    def _collect_valuable_leaves(self) -> List[TreeOfThoughtNode]:
        """Collect leaf nodes with high values."""
        valuable_leaves = []
        
        def _collect_leaves(node: TreeOfThoughtNode):
            if node.is_leaf() and node.value_estimate > self.value_threshold:
                valuable_leaves.append(node)
            
            for child in node.children:
                _collect_leaves(child)
        
        if self.root:
            _collect_leaves(self.root)
        
        # Sort by value
        valuable_leaves.sort(key=lambda n: n.value_estimate, reverse=True)
        
        return valuable_leaves
    
    def get_best_reasoning_path(self) -> Optional[List[str]]:
        """Get the best reasoning path as list of contents."""
        valuable_leaves = self._collect_valuable_leaves()
        
        if not valuable_leaves:
            return None
        
        best_leaf = valuable_leaves[0]
        path = best_leaf.get_path_to_root()
        
        return [node.content for node in path]
    
    def get_tree_statistics(self) -> Dict[str, Any]:
        """Get comprehensive tree exploration statistics."""
        def _count_nodes(node: TreeOfThoughtNode) -> int:
            count = 1
            for child in node.children:
                count += _count_nodes(child)
            return count
        
        total_nodes = _count_nodes(self.root) if self.root else 0
        
        return {
            'total_nodes': total_nodes,
            'total_expansions': self.total_expansions,
            'total_backtracks': self.total_backtracks,
            'pruned_nodes': len(self.pruned_nodes),
            'max_depth_reached': self._get_max_depth(),
            'average_branching_factor': self._get_average_branching_factor()
        }
    
    def _get_max_depth(self) -> int:
        """Get maximum depth reached in tree."""
        if not self.root:
            return 0
        
        max_depth = 0
        
        def _find_max_depth(node: TreeOfThoughtNode):
            nonlocal max_depth
            max_depth = max(max_depth, node.depth)
            for child in node.children:
                _find_max_depth(child)
        
        _find_max_depth(self.root)
        return max_depth
    
    def _get_average_branching_factor(self) -> float:
        """Get average branching factor of internal nodes."""
        if not self.root:
            return 0.0
        
        internal_nodes = []
        
        def _collect_internal_nodes(node: TreeOfThoughtNode):
            if node.children:
                internal_nodes.append(len(node.children))
            for child in node.children:
                _collect_internal_nodes(child)
        
        _collect_internal_nodes(self.root)
        
        return np.mean(internal_nodes) if internal_nodes else 0.0


class ReasoningGraph:
    """
    Graph-based reasoning system implementing ULTRA specifications.
    
    Mathematical foundation: G = (V, E, L_V, L_E)
    - Nodes represent concepts/statements
    - Edges represent logical relationships
    - Dynamic expansion based on inference rules
    """
    
    def __init__(self):
        """Initialize reasoning graph."""
        self.nodes: Dict[str, ReasoningNode] = {}
        self.edges: Dict[str, ReasoningEdge] = {}
        
        # Graph analysis
        self.nx_graph = nx.DiGraph()
        
        # Inference rules
        self.inference_rules = self._initialize_inference_rules()
        
        # Statistics
        self.expansion_history = []
        self.consistency_checks = 0
        
        logger.info("ReasoningGraph initialized")
    
    def add_node(self, node: ReasoningNode) -> None:
        """Add a reasoning node to the graph."""
        self.nodes[node.node_id] = node
        self.nx_graph.add_node(node.node_id, **{
            'content': node.content,
            'type': node.node_type.value,
            'confidence': node.confidence
        })
    
    def add_edge(self, edge: ReasoningEdge) -> None:
        """Add a reasoning edge to the graph."""
        if edge.source_id not in self.nodes or edge.target_id not in self.nodes:
            raise ValueError("Source and target nodes must exist before adding edge")
        
        self.edges[edge.edge_id] = edge
        self.nx_graph.add_edge(edge.source_id, edge.target_id, **{
            'relationship': edge.relationship_type,
            'strength': edge.strength,
            'confidence': edge.confidence
        })
    
    def build_reasoning_graph(self, problem_statement: str) -> None:
        """Build reasoning graph from problem statement."""
        # Create root problem node
        root_node = ReasoningNode(
            node_id="root",
            content=problem_statement,
            node_type=ReasoningStepType.PREMISE,
            confidence=1.0
        )
        self.add_node(root_node)
        
        # Generate initial observations
        observations = self._generate_observations(problem_statement)
        obs_nodes = []
        
        for i, obs_content in enumerate(observations):
            obs_node = ReasoningNode(
                node_id=f"obs_{i}",
                content=obs_content,
                node_type=ReasoningStepType.EVIDENCE,
                confidence=0.8
            )
            self.add_node(obs_node)
            obs_nodes.append(obs_node)
            
            # Connect to root
            edge = ReasoningEdge(
                edge_id=f"root_to_obs_{i}",
                source_id="root",
                target_id=obs_node.node_id,
                relationship_type="leads_to",
                strength=0.7,
                confidence=0.8
            )
            self.add_edge(edge)
        
        # Generate hypotheses from observations
        for obs_node in obs_nodes:
            hypotheses = self._generate_hypotheses(obs_node.content)
            
            for j, hyp_content in enumerate(hypotheses):
                hyp_node = ReasoningNode(
                    node_id=f"hyp_{obs_node.node_id}_{j}",
                    content=hyp_content,
                    node_type=ReasoningStepType.HYPOTHESIS,
                    confidence=0.6
                )
                self.add_node(hyp_node)
                
                # Connect observation to hypothesis
                edge = ReasoningEdge(
                    edge_id=f"{obs_node.node_id}_to_{hyp_node.node_id}",
                    source_id=obs_node.node_id,
                    target_id=hyp_node.node_id,
                    relationship_type="suggests",
                    strength=0.6,
                    confidence=0.7
                )
                self.add_edge(edge)
        
        # Apply inference rules for further expansion
        self._apply_inference_rules()
        
        # Link related nodes
        self._link_related_nodes()
        
        logger.info(f"Reasoning graph built with {len(self.nodes)} nodes and {len(self.edges)} edges")
    
    def _generate_observations(self, problem_statement: str) -> List[str]:
        """Generate initial observations from problem statement."""
        observation_templates = [
            "The problem involves multiple interconnected factors",
            "There are observable patterns in the given information",
            "Key variables can be identified and analyzed",
            "The situation shows measurable characteristics",
            "Evidence points to systematic relationships"
        ]
        
        # Select subset based on problem complexity
        num_observations = min(3, max(1, len(problem_statement.split()) // 20))
        return observation_templates[:num_observations]
    
    def _generate_hypotheses(self, observation: str) -> List[str]:
        """Generate hypotheses from observations."""
        hypothesis_templates = [
            "The underlying mechanism follows predictable rules",
            "There exists an optimal solution approach",
            "The phenomenon has identifiable root causes",
            "Multiple factors interact in systematic ways"
        ]
        
        return hypothesis_templates[:2]  # Limit to 2 hypotheses per observation
    
    def _initialize_inference_rules(self) -> List[Dict[str, Any]]:
        """Initialize logical inference rules."""
        return [
            {
                'name': 'modus_ponens',
                'pattern': ['premise', 'implication'],
                'conclusion_type': ReasoningStepType.DEDUCTION,
                'confidence_factor': 0.9
            },
            {
                'name': 'hypothetical_syllogism',
                'pattern': ['implication', 'implication'],
                'conclusion_type': ReasoningStepType.INFERENCE,
                'confidence_factor': 0.8
            },
            {
                'name': 'evidence_synthesis',
                'pattern': ['evidence', 'evidence'],
                'conclusion_type': ReasoningStepType.CONCLUSION,
                'confidence_factor': 0.7
            },
            {
                'name': 'hypothesis_testing',
                'pattern': ['hypothesis', 'evidence'],
                'conclusion_type': ReasoningStepType.DEDUCTION,
                'confidence_factor': 0.8
            }
        ]
    
    def _apply_inference_rules(self) -> None:
        """Apply inference rules to expand the graph."""
        expansion_count = 0
        max_expansions = 20
        
        while expansion_count < max_expansions:
            new_nodes_created = False
            
            for rule in self.inference_rules:
                pattern = rule['pattern']
                
                # Find node combinations matching the pattern
                matching_combinations = self._find_pattern_matches(pattern)
                
                for combination in matching_combinations:
                    # Create new conclusion node
                    conclusion_content = self._generate_conclusion_from_rule(rule, combination)
                    
                    conclusion_node = ReasoningNode(
                        node_id=f"conclusion_{expansion_count}_{len(self.nodes)}",
                        content=conclusion_content,
                        node_type=rule['conclusion_type'],
                        confidence=rule['confidence_factor'] * 0.8
                    )
                    
                    self.add_node(conclusion_node)
                    new_nodes_created = True
                    
                    # Connect premise nodes to conclusion
                    for premise_node_id in combination:
                        edge = ReasoningEdge(
                            edge_id=f"{premise_node_id}_to_{conclusion_node.node_id}",
                            source_id=premise_node_id,
                            target_id=conclusion_node.node_id,
                            relationship_type="implies",
                            strength=rule['confidence_factor'],
                            confidence=0.8
                        )
                        self.add_edge(edge)
                    
                    expansion_count += 1
                    if expansion_count >= max_expansions:
                        break
                
                if expansion_count >= max_expansions:
                    break
            
            if not new_nodes_created:
                break
        
        self.expansion_history.append({
            'total_expansions': expansion_count,
            'final_node_count': len(self.nodes),
            'final_edge_count': len(self.edges)
        })
    
    def _find_pattern_matches(self, pattern: List[str]) -> List[List[str]]:
        """Find node combinations matching inference pattern."""
        matches = []
        
        # Simple pattern matching - could be enhanced with more sophisticated logic
        if len(pattern) == 2:
            type1, type2 = pattern
            
            nodes_type1 = [n.node_id for n in self.nodes.values() 
                          if n.node_type.value == type1 or 
                          (type1 == 'premise' and n.node_type in [ReasoningStepType.PREMISE, ReasoningStepType.EVIDENCE]) or
                          (type1 == 'implication' and n.node_type in [ReasoningStepType.INFERENCE, ReasoningStepType.HYPOTHESIS])]
            
            nodes_type2 = [n.node_id for n in self.nodes.values() 
                          if n.node_type.value == type2 or
                          (type2 == 'premise' and n.node_type in [ReasoningStepType.PREMISE, ReasoningStepType.EVIDENCE]) or
                          (type2 == 'implication' and n.node_type in [ReasoningStepType.INFERENCE, ReasoningStepType.HYPOTHESIS])]
            
            # Create combinations
            for n1 in nodes_type1:
                for n2 in nodes_type2:
                    if n1 != n2:
                        matches.append([n1, n2])
        
        return matches[:5]  # Limit matches to prevent explosion
    
    def _generate_conclusion_from_rule(self, rule: Dict[str, Any], premises: List[str]) -> str:
        """Generate conclusion content from rule and premises."""
        rule_templates = {
            'modus_ponens': "Therefore, the logical consequence is established",
            'hypothetical_syllogism': "This leads to the further implication that",
            'evidence_synthesis': "The combined evidence indicates that",
            'hypothesis_testing': "The hypothesis is supported by the evidence"
        }
        
        return rule_templates.get(rule['name'], "A conclusion follows from the premises")
    
    def _link_related_nodes(self) -> None:
        """Create edges between semantically related nodes."""
        node_list = list(self.nodes.values())
        
        for i, node1 in enumerate(node_list):
            for node2 in node_list[i+1:]:
                if node1.node_id != node2.node_id:
                    relatedness = self._calculate_relatedness(node1.content, node2.content)
                    
                    if relatedness > 0.7:  # Threshold for creating relation
                        edge = ReasoningEdge(
                            edge_id=f"relation_{node1.node_id}_{node2.node_id}",
                            source_id=node1.node_id,
                            target_id=node2.node_id,
                            relationship_type="related",
                            strength=relatedness,
                            confidence=0.6
                        )
                        self.add_edge(edge)
    
    def _calculate_relatedness(self, content1: str, content2: str) -> float:
        """Calculate semantic relatedness between two content strings."""
        words1 = set(content1.lower().split())
        words2 = set(content2.lower().split())
        
        if not words1 or not words2:
            return 0.0
        
        intersection = len(words1 & words2)
        union = len(words1 | words2)
        
        return intersection / union if union > 0 else 0.0
    
    def check_consistency(self) -> bool:
        """
        Check graph consistency for contradictions.
        
        Mathematical model: Consistent(G) = ¬∃ v1, v2 ∈ V such that Contradicts(v1, v2)
        """
        self.consistency_checks += 1
        
        # Simple contradiction detection
        node_contents = [node.content.lower() for node in self.nodes.values()]
        
        # Look for direct contradictions (e.g., "X is true" vs "X is false")
        for i, content1 in enumerate(node_contents):
            for content2 in node_contents[i+1:]:
                if self._are_contradictory(content1, content2):
                    logger.warning(f"Contradiction detected: '{content1}' vs '{content2}'")
                    return False
        
        return True
    
    def _are_contradictory(self, content1: str, content2: str) -> bool:
        """Check if two content strings are contradictory."""
        # Simple contradiction patterns
        contradiction_pairs = [
            ('true', 'false'),
            ('possible', 'impossible'),
            ('likely', 'unlikely'),
            ('increase', 'decrease'),
            ('positive', 'negative')
        ]
        
        for word1, word2 in contradiction_pairs:
            if word1 in content1 and word2 in content2:
                return True
            if word2 in content1 and word1 in content2:
                return True
        
        return False
    
    def find_reasoning_paths(self, start_node_id: str, end_node_id: str) -> List[List[str]]:
        """
        Find reasoning paths between two nodes.
        
        Mathematical model: P(v_start, v_end) = {p : p is a path from v_start to v_end in G}
        """
        if start_node_id not in self.nodes or end_node_id not in self.nodes:
            return []
        
        try:
            # Find all simple paths (no cycles)
            paths = list(nx.all_simple_paths(self.nx_graph, start_node_id, end_node_id, cutoff=6))
            return paths
        except nx.NetworkXNoPath:
            return []
    
    def extract_justification(self, conclusion_node_id: str) -> List[str]:
        """
        Extract justification path for a conclusion.
        
        Mathematical model: J(v) = {(v1, e1, v2, e2, ..., vn) : v1 ∈ V_premises, vn = v}
        """
        if conclusion_node_id not in self.nodes:
            return []
        
        # Find paths from premise nodes to conclusion
        premise_nodes = [node_id for node_id, node in self.nodes.items() 
                        if node.node_type == ReasoningStepType.PREMISE]
        
        justification_paths = []
        for premise_id in premise_nodes:
            paths = self.find_reasoning_paths(premise_id, conclusion_node_id)
            justification_paths.extend(paths)
        
        # Return the shortest justification path
        if justification_paths:
            shortest_path = min(justification_paths, key=len)
            return [self.nodes[node_id].content for node_id in shortest_path]
        
        return []
    
    def get_strongest_conclusions(self, top_k: int = 3) -> List[ReasoningNode]:
        """Get the strongest conclusion nodes based on support."""
        conclusion_nodes = [node for node in self.nodes.values() 
                          if node.node_type == ReasoningStepType.CONCLUSION]
        
        # Calculate support strength for each conclusion
        for node in conclusion_nodes:
            incoming_edges = [edge for edge in self.edges.values() 
                            if edge.target_id == node.node_id]
            
            support_strength = sum(edge.strength * edge.confidence for edge in incoming_edges)
            node.activation_level = support_strength
        
        # Sort by support strength
        conclusion_nodes.sort(key=lambda n: n.activation_level, reverse=True)
        
        return conclusion_nodes[:top_k]
    
    def get_graph_statistics(self) -> Dict[str, Any]:
        """Get comprehensive graph statistics."""
        node_types = defaultdict(int)
        for node in self.nodes.values():
            node_types[node.node_type.value] += 1
        
        edge_types = defaultdict(int)
        for edge in self.edges.values():
            edge_types[edge.relationship_type] += 1
        
        # Graph metrics
        try:
            avg_clustering = nx.average_clustering(self.nx_graph.to_undirected())
            density = nx.density(self.nx_graph)
        except:
            avg_clustering = 0.0
            density = 0.0
        
        return {
            'total_nodes': len(self.nodes),
            'total_edges': len(self.edges),
            'node_type_distribution': dict(node_types),
            'edge_type_distribution': dict(edge_types),
            'average_clustering': avg_clustering,
            'graph_density': density,
            'consistency_checks_performed': self.consistency_checks,
            'expansion_history': self.expansion_history
        }


class SelfCritiqueAnalyzer:
    """
    Self-critique analyzer for reasoning evaluation and improvement.
    
    Implements iterative reasoning refinement with critique generation.
    """
    
    def __init__(self, max_iterations: int = 3):
        """Initialize self-critique analyzer."""
        self.max_iterations = max_iterations
        
        # Critique criteria weights
        self.critique_weights = {
            CritiqueType.LOGICAL_VALIDITY: 0.25,
            CritiqueType.FACTUAL_ACCURACY: 0.20,
            CritiqueType.COMPLETENESS: 0.15,
            CritiqueType.RELEVANCE: 0.15,
            CritiqueType.COHERENCE: 0.10,
            CritiqueType.CONSISTENCY: 0.10,
            CritiqueType.BIAS_DETECTION: 0.04,
            CritiqueType.ALTERNATIVE_CONSIDERATION: 0.01
        }
        
        # Critique history
        self.critique_history = []
        
        logger.info("SelfCritiqueAnalyzer initialized")
    
    def analyze_reasoning_path(self, path: ReasoningPath) -> List[CritiqueResult]:
        """Analyze a reasoning path and generate critiques."""
        critiques = []
        
        for step in path.steps:
            step_critiques = self._critique_step(step, path)
            critiques.extend(step_critiques)
        
        # Add path-level critiques
        path_critiques = self._critique_path_structure(path)
        critiques.extend(path_critiques)
        
        # Sort by severity
        critiques.sort(key=lambda c: c.severity, reverse=True)
        
        self.critique_history.append({
            'path_id': path.path_id,
            'total_critiques': len(critiques),
            'severe_critiques': len([c for c in critiques if c.severity > 0.7]),
            'critique_types': [c.critique_type.value for c in critiques]
        })
        
        return critiques
    
    def _critique_step(self, step: ChainOfThoughtStep, path: ReasoningPath) -> List[CritiqueResult]:
        """Generate critiques for an individual reasoning step."""
        critiques = []
        
        # Logical validity critique
        validity_issue = self._check_logical_validity(step, path)
        if validity_issue:
            critiques.append(validity_issue)
        
        # Factual accuracy critique
        accuracy_issue = self._check_factual_accuracy(step)
        if accuracy_issue:
            critiques.append(accuracy_issue)
        
        # Relevance critique
        relevance_issue = self._check_relevance(step, path)
        if relevance_issue:
            critiques.append(relevance_issue)
        
        # Completeness critique
        completeness_issue = self._check_completeness(step)
        if completeness_issue:
            critiques.append(completeness_issue)
        
        return critiques
    
    def _check_logical_validity(self, step: ChainOfThoughtStep, path: ReasoningPath) -> Optional[CritiqueResult]:
        """Check logical validity of a reasoning step."""
        issues = []
        
        # Check for unsupported conclusions
        if step.step_type == ReasoningStepType.CONCLUSION and not step.dependencies:
            issues.append("Conclusion lacks supporting premises")
        
        # Check for circular reasoning
        if self._has_circular_dependency(step, path):
            issues.append("Circular reasoning detected")
        
        # Check for logical fallacies
        fallacies = self._detect_logical_fallacies(step.content)
        if fallacies:
            issues.extend(fallacies)
        
        if issues:
            return CritiqueResult(
                critique_id=str(uuid.uuid4()),
                critique_type=CritiqueType.LOGICAL_VALIDITY,
                target_step_id=step.step_id,
                severity=0.8,
                description="; ".join(issues),
                suggested_improvements=[
                    "Provide explicit logical connections",
                    "Add supporting premises",
                    "Avoid circular dependencies"
                ],
                confidence=0.7
            )
        
        return None
    
    def _check_factual_accuracy(self, step: ChainOfThoughtStep) -> Optional[CritiqueResult]:
        """Check factual accuracy of step content."""
        accuracy_score = step.factual_accuracy
        
        if accuracy_score < 0.6:
            return CritiqueResult(
                critique_id=str(uuid.uuid4()),
                critique_type=CritiqueType.FACTUAL_ACCURACY,
                target_step_id=step.step_id,
                severity=1.0 - accuracy_score,
                description=f"Low factual accuracy score: {accuracy_score:.2f}",
                suggested_improvements=[
                    "Verify claims with reliable sources",
                    "Add supporting evidence",
                    "Qualify uncertain statements"
                ],
                confidence=0.8
            )
        
        return None
    
    def _check_relevance(self, step: ChainOfThoughtStep, path: ReasoningPath) -> Optional[CritiqueResult]:
        """Check relevance of step to overall reasoning."""
        if step.relevance_score < 0.5:
            return CritiqueResult(
                critique_id=str(uuid.uuid4()),
                critique_type=CritiqueType.RELEVANCE,
                target_step_id=step.step_id,
                severity=0.6,
                description=f"Step may not be relevant to main reasoning line",
                suggested_improvements=[
                    "Clarify connection to main argument",
                    "Remove tangential content",
                    "Strengthen relevance links"
                ],
                confidence=0.6
            )
        
        return None
    
    def _check_completeness(self, step: ChainOfThoughtStep) -> Optional[CritiqueResult]:
        """Check completeness of reasoning step."""
        issues = []
        
        # Check for incomplete explanations
        if len(step.content) < 20:  # Very short content
            issues.append("Step content may be too brief")
        
        # Check for missing evidence
        if step.step_type == ReasoningStepType.EVIDENCE and not step.supporting_evidence:
            issues.append("Evidence step lacks supporting data")
        
        if issues:
            return CritiqueResult(
                critique_id=str(uuid.uuid4()),
                critique_type=CritiqueType.COMPLETENESS,
                target_step_id=step.step_id,
                severity=0.5,
                description="; ".join(issues),
                suggested_improvements=[
                    "Expand explanations",
                    "Add supporting details",
                    "Provide complete arguments"
                ],
                confidence=0.6
            )
        
        return None
    
    def _critique_path_structure(self, path: ReasoningPath) -> List[CritiqueResult]:
        """Generate critiques for overall path structure."""
        critiques = []
        
        # Check for missing essential components
        step_types = set(step.step_type for step in path.steps)
        required_types = {ReasoningStepType.PREMISE, ReasoningStepType.CONCLUSION}
        missing_types = required_types - step_types
        
        if missing_types:
            critiques.append(CritiqueResult(
                critique_id=str(uuid.uuid4()),
                critique_type=CritiqueType.COMPLETENESS,
                target_step_id=path.path_id,
                severity=0.7,
                description=f"Missing essential reasoning components: {[t.value for t in missing_types]}",
                suggested_improvements=[
                    "Add missing reasoning components",
                    "Ensure complete argument structure"
                ],
                confidence=0.8
            ))
        
        # Check coherence
        if path.logical_consistency < 0.6:
            critiques.append(CritiqueResult(
                critique_id=str(uuid.uuid4()),
                critique_type=CritiqueType.COHERENCE,
                target_step_id=path.path_id,
                severity=0.6,
                description=f"Low logical consistency: {path.logical_consistency:.2f}",
                suggested_improvements=[
                    "Improve logical flow between steps",
                    "Strengthen argument coherence",
                    "Remove contradictory elements"
                ],
                confidence=0.7
            ))
        
        return critiques
    
    def _has_circular_dependency(self, step: ChainOfThoughtStep, path: ReasoningPath) -> bool:
        """Check if step has circular dependencies."""
        if not step.dependencies:
            return False
        
        # Simple circular dependency check
        step_ids = [s.step_id for s in path.steps]
        
        for dep_id in step.dependencies:
            if dep_id == step.step_id:  # Self-dependency
                return True
            
            # Check if dependency comes after current step
            try:
                current_idx = step_ids.index(step.step_id)
                dep_idx = step_ids.index(dep_id)
                if dep_idx > current_idx:
                    return True
            except ValueError:
                continue
        
        return False
    
    def _detect_logical_fallacies(self, content: str) -> List[str]:
        """Detect common logical fallacies in content."""
        fallacies = []
        content_lower = content.lower()
        
        # Ad hominem
        if any(phrase in content_lower for phrase in ['stupid', 'idiot', 'foolish person']):
            fallacies.append("Potential ad hominem attack")
        
        # False dichotomy
        if any(phrase in content_lower for phrase in ['only two options', 'either...or', 'black and white']):
            fallacies.append("Potential false dichotomy")
        
        # Appeal to authority
        if any(phrase in content_lower for phrase in ['expert says', 'authority claims', 'because X said']):
            fallacies.append("Potential improper appeal to authority")
        
        # Slippery slope
        if any(phrase in content_lower for phrase in ['leads to', 'will result in', 'inevitably causes']):
            fallacies.append("Potential slippery slope")
        
        return fallacies
    
    def refine_reasoning_path(self, path: ReasoningPath, critiques: List[CritiqueResult]) -> ReasoningPath:
        """Refine reasoning path based on critiques."""
        refined_path = deepcopy(path)
        
        # Group critiques by severity
        severe_critiques = [c for c in critiques if c.severity > 0.7]
        moderate_critiques = [c for c in critiques if 0.4 <= c.severity <= 0.7]
        
        # Address severe critiques first
        for critique in severe_critiques:
            self._apply_critique_improvement(refined_path, critique)
        
        # Address moderate critiques
        for critique in moderate_critiques[:3]:  # Limit to prevent over-modification
            self._apply_critique_improvement(refined_path, critique)
        
        # Update path metrics
        refined_path._update_metrics()
        
        return refined_path
    
    def _apply_critique_improvement(self, path: ReasoningPath, critique: CritiqueResult) -> None:
        """Apply specific improvement based on critique."""
        if critique.critique_type == CritiqueType.LOGICAL_VALIDITY:
            # Add logical connectors
            for step in path.steps:
                if step.step_id == critique.target_step_id:
                    step.content = f"Following logically, {step.content.lower()}"
                    step.logical_strength = min(1.0, step.logical_strength + 0.2)
        
        elif critique.critique_type == CritiqueType.COMPLETENESS:
            # Expand content
            for step in path.steps:
                if step.step_id == critique.target_step_id:
                    step.content += " (This requires further explanation and supporting evidence.)"
        
        elif critique.critique_type == CritiqueType.FACTUAL_ACCURACY:
            # Add qualification
            for step in path.steps:
                if step.step_id == critique.target_step_id:
                    step.content = f"Based on available evidence, {step.content.lower()}"
                    step.factual_accuracy = min(1.0, step.factual_accuracy + 0.1)
    
    def get_critique_statistics(self) -> Dict[str, Any]:
        """Get comprehensive critique statistics."""
        if not self.critique_history:
            return {"message": "No critique history available"}
        
        total_critiques = sum(h['total_critiques'] for h in self.critique_history)
        total_severe = sum(h['severe_critiques'] for h in self.critique_history)
        
        all_critique_types = []
        for history in self.critique_history:
            all_critique_types.extend(history['critique_types'])
        
        critique_type_counts = defaultdict(int)
        for critique_type in all_critique_types:
            critique_type_counts[critique_type] += 1
        
        return {
            'total_paths_analyzed': len(self.critique_history),
            'total_critiques_generated': total_critiques,
            'severe_critiques': total_severe,
            'average_critiques_per_path': total_critiques / len(self.critique_history),
            'critique_type_distribution': dict(critique_type_counts),
            'critique_weights': self.critique_weights
        }


class ULTRAChainOfThoughtSystem:
    """
    Integrated Chain of Thought system combining all reasoning approaches.
    
    This is the main system that orchestrates multi-path reasoning, tree exploration,
    graph-based reasoning, and self-critique in a unified framework.
    """
    
    def __init__(self, reasoning_mode: str = "multi_path", max_reasoning_time: float = 60.0):
        """
        Initialize the ULTRA Chain of Thought system.
        
        Args:
            reasoning_mode: Mode of reasoning ('multi_path', 'tree', 'graph', 'integrated')
            max_reasoning_time: Maximum time for reasoning process (seconds)
        """
        # STEP 1: Initialize basic configuration
        self.reasoning_mode = reasoning_mode
        self.max_reasoning_time = max_reasoning_time
        
        # STEP 2: Initialize data structures BEFORE creating subsystems
        self.reasoning_history = []
        self.performance_metrics = defaultdict(list)
        
        # STEP 3: Initialize subsystems in dependency order
        try:
            self.multi_path_system = MultiPathChainOfThought()
        except Exception as e:
            logger.error(f"Failed to initialize MultiPathChainOfThought: {e}")
            self.multi_path_system = None
            
        try:
            self.tree_explorer = TreeOfThoughtExplorer()
        except Exception as e:
            logger.error(f"Failed to initialize TreeOfThoughtExplorer: {e}")
            self.tree_explorer = None
            
        try:
            self.reasoning_graph = ReasoningGraph()
        except Exception as e:
            logger.error(f"Failed to initialize ReasoningGraph: {e}")
            self.reasoning_graph = None
            
        try:
            self.critique_analyzer = SelfCritiqueAnalyzer()
        except Exception as e:
            logger.error(f"Failed to initialize SelfCritiqueAnalyzer: {e}")
            self.critique_analyzer = None
        
        # STEP 4: Validate initialization
        self._validate_initialization()
        
        logger.info(f"ULTRAChainOfThoughtSystem initialized in {reasoning_mode} mode")
    
    def _validate_initialization(self) -> None:
        """Validate that all required components are properly initialized."""
        required_attrs = [
            'reasoning_history', 'performance_metrics', 'reasoning_mode', 
            'max_reasoning_time'
        ]
        
        for attr in required_attrs:
            if not hasattr(self, attr):
                setattr(self, attr, [] if 'history' in attr or 'metrics' in attr else None)
                logger.warning(f"Initialized missing attribute: {attr}")
        
        # Ensure performance_metrics is a defaultdict
        if not isinstance(self.performance_metrics, defaultdict):
            self.performance_metrics = defaultdict(list)
        
        # Ensure reasoning_history is a list
        if not isinstance(self.reasoning_history, list):
            self.reasoning_history = []
    
    def reason(self, problem_statement: str, context: str = "", constraints: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Main reasoning interface that coordinates all reasoning approaches.
        
        Args:
            problem_statement: The problem to reason about
            context: Additional context information
            constraints: Optional constraints on reasoning process
            
        Returns:
            Comprehensive reasoning result with multiple solution paths
        """
        start_time = time.time()
        
        # Initialize reasoning session
        session_id = str(uuid.uuid4())
        
        logger.info(f"Starting reasoning session {session_id} for: {problem_statement[:100]}...")
        
        results = {
            'session_id': session_id,
            'problem_statement': problem_statement,
            'context': context,
            'reasoning_mode': self.reasoning_mode,
            'start_time': start_time
        }
        
        try:
            if self.reasoning_mode == "multi_path":
                results.update(self._multi_path_reasoning(problem_statement, context))
                
            elif self.reasoning_mode == "tree":
                results.update(self._tree_reasoning(problem_statement, context))
                
            elif self.reasoning_mode == "graph":
                results.update(self._graph_reasoning(problem_statement, context))
                
            elif self.reasoning_mode == "integrated":
                results.update(self._integrated_reasoning(problem_statement, context))
                
            else:
                raise ValueError(f"Unknown reasoning mode: {self.reasoning_mode}")
            
            # Apply self-critique if enabled
            if 'reasoning_paths' in results:
                results.update(self._apply_self_critique(results['reasoning_paths']))
        
        except Exception as e:
            logger.error(f"Error during reasoning: {str(e)}")
            results['error'] = str(e)
            results['success'] = False
        
        # Finalize results
        results['end_time'] = time.time()
        results['total_time'] = results['end_time'] - start_time
        results['success'] = results.get('success', True)
        
        # Store in history
        self.reasoning_history.append(results)
        self._update_performance_metrics(results)
        
        logger.info(f"Reasoning session {session_id} completed in {results['total_time']:.2f}s")
        
        return results
    
    def _multi_path_reasoning(self, problem_statement: str, context: str) -> Dict[str, Any]:
        """Execute multi-path chain of thought reasoning."""
        paths = self.multi_path_system.generate_reasoning_paths(problem_statement, context)
        
        best_path = self.multi_path_system.get_best_path()
        statistics = self.multi_path_system.get_reasoning_statistics()
        
        return {
            'reasoning_approach': 'multi_path',
            'reasoning_paths': paths,
            'best_path': best_path,
            'total_paths': len(paths),
            'statistics': statistics,
            'success': len(paths) > 0
        }
    
    def _tree_reasoning(self, problem_statement: str, context: str) -> Dict[str, Any]:
        """Execute tree of thought reasoning."""
        valuable_leaves = self.tree_explorer.explore(problem_statement)
        
        best_path = self.tree_explorer.get_best_reasoning_path()
        statistics = self.tree_explorer.get_tree_statistics()
        
        return {
            'reasoning_approach': 'tree',
            'valuable_nodes': valuable_leaves,
            'best_reasoning_path': best_path,
            'total_valuable_nodes': len(valuable_leaves),
            'statistics': statistics,
            'success': len(valuable_leaves) > 0
        }
    
    def _graph_reasoning(self, problem_statement: str, context: str) -> Dict[str, Any]:
        """Execute graph-based reasoning."""
        self.reasoning_graph.build_reasoning_graph(problem_statement)
        
        strongest_conclusions = self.reasoning_graph.get_strongest_conclusions()
        consistency_check = self.reasoning_graph.check_consistency()
        statistics = self.reasoning_graph.get_graph_statistics()
        
        # Extract reasoning paths for strongest conclusions
        reasoning_paths = []
        for conclusion in strongest_conclusions:
            justification = self.reasoning_graph.extract_justification(conclusion.node_id)
            if justification:
                reasoning_paths.append({
                    'conclusion': conclusion.content,
                    'justification_path': justification,
                    'confidence': conclusion.confidence,
                    'support_strength': conclusion.activation_level
                })
        
        return {
            'reasoning_approach': 'graph',
            'reasoning_paths': reasoning_paths,
            'strongest_conclusions': [c.content for c in strongest_conclusions],
            'is_consistent': consistency_check,
            'graph_statistics': statistics,
            'success': len(strongest_conclusions) > 0
        }
    
    def _integrated_reasoning(self, problem_statement: str, context: str) -> Dict[str, Any]:
        """Execute integrated reasoning combining all approaches."""
        results = {
            'reasoning_approach': 'integrated',
            'component_results': {}
        }
        
        # Run multi-path reasoning
        try:
            multi_path_results = self._multi_path_reasoning(problem_statement, context)
            results['component_results']['multi_path'] = multi_path_results
        except Exception as e:
            logger.warning(f"Multi-path reasoning failed: {e}")
        
        # Run tree reasoning
        try:
            tree_results = self._tree_reasoning(problem_statement, context)
            results['component_results']['tree'] = tree_results
        except Exception as e:
            logger.warning(f"Tree reasoning failed: {e}")
        
        # Run graph reasoning
        try:
            graph_results = self._graph_reasoning(problem_statement, context)
            results['component_results']['graph'] = graph_results
        except Exception as e:
            logger.warning(f"Graph reasoning failed: {e}")
        
        # Synthesize results
        results.update(self._synthesize_integrated_results(results['component_results']))
        
        return results
    
    def _synthesize_integrated_results(self, component_results: Dict[str, Any]) -> Dict[str, Any]:
        """Synthesize results from multiple reasoning approaches."""
        synthesis = {
            'synthesis_approach': 'weighted_combination',
            'component_weights': {
                'multi_path': 0.4,
                'tree': 0.3,
                'graph': 0.3
            }
        }
        
        # Collect all reasoning paths
        all_paths = []
        
        if 'multi_path' in component_results and component_results['multi_path'].get('success'):
            mp_paths = component_results['multi_path'].get('reasoning_paths', [])
            for path in mp_paths:
                all_paths.append({
                    'source': 'multi_path',
                    'content': [step.content for step in path.steps],
                    'confidence': path.overall_confidence,
                    'weight': synthesis['component_weights']['multi_path']
                })
        
        if 'tree' in component_results and component_results['tree'].get('success'):
            tree_path = component_results['tree'].get('best_reasoning_path')
            if tree_path:
                all_paths.append({
                    'source': 'tree',
                    'content': tree_path,
                    'confidence': 0.8,  # Default confidence for tree paths
                    'weight': synthesis['component_weights']['tree']
                })
        
        if 'graph' in component_results and component_results['graph'].get('success'):
            graph_paths = component_results['graph'].get('reasoning_paths', [])
            for path in graph_paths:
                all_paths.append({
                    'source': 'graph',
                    'content': path['justification_path'],
                    'confidence': path['confidence'],
                    'weight': synthesis['component_weights']['graph']
                })
        
        # Rank paths by weighted confidence
        for path in all_paths:
            path['weighted_score'] = path['confidence'] * path['weight']
        
        all_paths.sort(key=lambda p: p['weighted_score'], reverse=True)
        
        synthesis.update({
            'integrated_paths': all_paths,
            'best_integrated_path': all_paths[0] if all_paths else None,
            'total_paths': len(all_paths),
            'success': len(all_paths) > 0
        })
        
        return synthesis
    
    def _apply_self_critique(self, reasoning_paths: List[Any]) -> Dict[str, Any]:
        """Apply self-critique to reasoning paths."""
        critique_results = {
            'self_critique_applied': True,
            'critiques_by_path': {},
            'refined_paths': [],
            'improvement_summary': {}
        }
        
        total_critiques = 0
        total_severe_critiques = 0
        
        # Apply critique to each path (if they are ReasoningPath objects)
        for i, path in enumerate(reasoning_paths):
            if isinstance(path, ReasoningPath):
                critiques = self.critique_analyzer.analyze_reasoning_path(path)
                critique_results['critiques_by_path'][path.path_id] = critiques
                
                total_critiques += len(critiques)
                total_severe_critiques += len([c for c in critiques if c.severity > 0.7])
                
                # Refine path if critiques found
                if critiques:
                    refined_path = self.critique_analyzer.refine_reasoning_path(path, critiques)
                    critique_results['refined_paths'].append(refined_path)
                else:
                    critique_results['refined_paths'].append(path)
        
        critique_results['improvement_summary'] = {
            'total_critiques': total_critiques,
            'severe_critiques': total_severe_critiques,
            'paths_analyzed': len(reasoning_paths),
            'refinement_rate': total_critiques / max(len(reasoning_paths), 1)
        }
        
        return critique_results
    
    def _update_performance_metrics(self, results: Dict[str, Any]) -> None:
        """Update system performance metrics with comprehensive error handling."""
        try:
            # Ensure performance_metrics exists and is properly initialized
            if not hasattr(self, 'performance_metrics'):
                self.performance_metrics = defaultdict(list)
            elif not isinstance(self.performance_metrics, defaultdict):
                self.performance_metrics = defaultdict(list)
            
            # Safely update metrics
            try:
                reasoning_time = results.get('total_time', 0)
                if isinstance(reasoning_time, (int, float)) and reasoning_time >= 0:
                    self.performance_metrics['reasoning_time'].append(reasoning_time)
            except Exception as e:
                logger.warning(f"Error updating reasoning_time: {e}")
            
            try:
                success = results.get('success', False)
                success_value = 1 if success else 0
                self.performance_metrics['success_rate'].append(success_value)
            except Exception as e:
                logger.warning(f"Error updating success_rate: {e}")
            
            try:
                reasoning_mode = results.get('reasoning_mode', 'unknown')
                if isinstance(reasoning_mode, str):
                    self.performance_metrics['reasoning_mode'].append(reasoning_mode)
            except Exception as e:
                logger.warning(f"Error updating reasoning_mode: {e}")
            
            try:
                total_paths = results.get('total_paths', 0)
                if isinstance(total_paths, int) and total_paths >= 0:
                    self.performance_metrics['paths_generated'].append(total_paths)
            except Exception as e:
                logger.warning(f"Error updating paths_generated: {e}")
            
            try:
                if 'improvement_summary' in results:
                    improvement = results['improvement_summary']
                    if isinstance(improvement, dict):
                        refinement_rate = improvement.get('refinement_rate', 0)
                        if isinstance(refinement_rate, (int, float)) and refinement_rate >= 0:
                            self.performance_metrics['critique_rate'].append(refinement_rate)
            except Exception as e:
                logger.warning(f"Error updating critique_rate: {e}")
                
        except Exception as e:
            logger.error(f"Critical error in _update_performance_metrics: {e}")
            # Ensure performance_metrics exists even if everything fails
            if not hasattr(self, 'performance_metrics'):
                self.performance_metrics = defaultdict(list)
    
    def get_system_performance(self) -> Dict[str, Any]:
        """Get comprehensive system performance metrics."""
        # ALWAYS initialize metrics first - no matter what!
        metrics = {}
        
        # Early return case - but metrics is already defined
        if not self.performance_metrics:
            metrics["message"] = "No performance data available"
            metrics['total_sessions'] = len(self.reasoning_history) if hasattr(self, 'reasoning_history') else 0
            metrics['average_session_time'] = 0.0
            return metrics
        
        # Process performance metrics
        for metric_name, values in self.performance_metrics.items():
            if values:
                if isinstance(values[0], (int, float)):
                    metrics[metric_name] = {
                        'mean': np.mean(values),
                        'std': np.std(values),
                        'min': np.min(values),
                        'max': np.max(values),
                        'count': len(values)
                    }
                else:
                    # For categorical data
                    from collections import Counter
                    counts = Counter(values)
                    metrics[metric_name] = dict(counts)
        
        # Add session statistics - metrics is guaranteed to exist here
        metrics['total_sessions'] = len(self.reasoning_history) if hasattr(self, 'reasoning_history') else 0
        metrics['average_session_time'] = (
            np.mean([h.get('total_time', 0) for h in self.reasoning_history]) 
            if hasattr(self, 'reasoning_history') and self.reasoning_history 
            else 0.0
        )
        
        return metrics
    
    def reset_system_state(self) -> None:
        """Reset system state for fresh reasoning sessions with proper hierarchy."""
        try:
            # STEP 1: Clear data structures first
            self.reasoning_history = []
            self.performance_metrics = defaultdict(list)
            
            # STEP 2: Reinitialize subsystems in dependency order
            try:
                self.multi_path_system = MultiPathChainOfThought()
            except Exception as e:
                logger.warning(f"Failed to reinitialize MultiPathChainOfThought: {e}")
                self.multi_path_system = None
                
            try:
                self.tree_explorer = TreeOfThoughtExplorer()
            except Exception as e:
                logger.warning(f"Failed to reinitialize TreeOfThoughtExplorer: {e}")
                self.tree_explorer = None
                
            try:
                self.reasoning_graph = ReasoningGraph()
            except Exception as e:
                logger.warning(f"Failed to reinitialize ReasoningGraph: {e}")
                self.reasoning_graph = None
                
            try:
                self.critique_analyzer = SelfCritiqueAnalyzer()
            except Exception as e:
                logger.warning(f"Failed to reinitialize SelfCritiqueAnalyzer: {e}")
                self.critique_analyzer = None
            
            # STEP 3: Validate reinitialization
            self._validate_initialization()
            
            logger.info("System state reset successfully")
            
        except Exception as e:
            logger.error(f"Error during system reset: {e}")
            # Fallback initialization
            self.reasoning_history = []
            self.performance_metrics = defaultdict(list)
    
    def export_reasoning_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Export detailed results from a specific reasoning session."""
        for session in self.reasoning_history:
            if session.get('session_id') == session_id:
                return deepcopy(session)
        
        return None
    
    def get_system_statistics(self) -> Dict[str, Any]:
        """Get comprehensive system statistics across all components."""
        stats = {
            'system_overview': {
                'reasoning_mode': self.reasoning_mode,
                'max_reasoning_time': self.max_reasoning_time,
                'total_sessions': len(self.reasoning_history),
                'uptime_sessions': len(self.reasoning_history)
            }
        }
        
        # Multi-path system statistics
        try:
            stats['multi_path_statistics'] = self.multi_path_system.get_reasoning_statistics()
        except:
            stats['multi_path_statistics'] = {"error": "Unable to retrieve multi-path statistics"}
        
        # Tree exploration statistics
        try:
            stats['tree_statistics'] = self.tree_explorer.get_tree_statistics()
        except:
            stats['tree_statistics'] = {"error": "Unable to retrieve tree statistics"}
        
        # Graph reasoning statistics
        try:
            stats['graph_statistics'] = self.reasoning_graph.get_graph_statistics()
        except:
            stats['graph_statistics'] = {"error": "Unable to retrieve graph statistics"}
        
        # Critique analyzer statistics
        try:
            stats['critique_statistics'] = self.critique_analyzer.get_critique_statistics()
        except:
            stats['critique_statistics'] = {"error": "Unable to retrieve critique statistics"}
        
        # Performance metrics
        try:
            stats['performance_metrics'] = self.get_system_performance()
        except:
            stats['performance_metrics'] = {"error": "Unable to retrieve performance metrics"}
        
        return stats


# ============================================================================
# Test Suite Implementation
# ============================================================================

class TestULTRAChainOfThought(unittest.TestCase):
    """Comprehensive test suite for ULTRA Chain of Thought system."""
    
    @classmethod
    def setUpClass(cls):
        """Set up test fixtures before running all test methods."""
        cls.cot_system = ULTRAChainOfThoughtSystem(reasoning_mode="integrated")
        
        # Test problem statements
        cls.test_problems = {
            'simple_math': "If Alice has 15 apples and gives away 7 apples, then buys 3 more apples, how many apples does she have?",
            'logical_puzzle': "All birds can fly. All penguins are birds. Can penguins fly?",
            'scientific_reasoning': "Why do objects fall toward Earth rather than floating in space?",
            'ethical_dilemma': "Is it ethical to break a promise to help someone in greater need?",
            'complex_analysis': "What are the primary factors contributing to climate change and what are the most effective mitigation strategies?",
            'causal_reasoning': "If increasing minimum wage leads to job losses, but also reduces poverty, what is the optimal policy approach?",
            'creative_problem': "How can we design a city that is both environmentally sustainable and economically viable?",
            'abstract_concept': "What is the relationship between consciousness and artificial intelligence?"
        }
        
        # Expected reasoning patterns
        cls.expected_patterns = {
            'simple_math': [ReasoningStepType.PREMISE, ReasoningStepType.INFERENCE, ReasoningStepType.CONCLUSION],
            'logical_puzzle': [ReasoningStepType.PREMISE, ReasoningStepType.INFERENCE, ReasoningStepType.DEDUCTION, ReasoningStepType.CONCLUSION],
            'scientific_reasoning': [ReasoningStepType.PREMISE, ReasoningStepType.HYPOTHESIS, ReasoningStepType.EVIDENCE, ReasoningStepType.CONCLUSION]
        }
    
    def setUp(self):
        """Set up test fixtures before each test method."""
        # Reset system state for clean tests
        self.cot_system.reset_system_state()
    
    def test_chain_of_thought_step_creation(self):
        """Test ChainOfThoughtStep creation and validation."""
        step = ChainOfThoughtStep(
            step_id="test_step_1",
            step_type=ReasoningStepType.PREMISE,
            content="This is a test premise for reasoning.",
            confidence=0.85,
            logical_strength=0.9,
            factual_accuracy=0.8,
            relevance_score=0.95
        )
        
        # Verify basic properties
        self.assertEqual(step.step_id, "test_step_1")
        self.assertEqual(step.step_type, ReasoningStepType.PREMISE)
        self.assertEqual(step.content, "This is a test premise for reasoning.")
        
        # Verify score normalization
        self.assertGreaterEqual(step.confidence, 0.0)
        self.assertLessEqual(step.confidence, 1.0)
        self.assertGreaterEqual(step.logical_strength, 0.0)
        self.assertLessEqual(step.logical_strength, 1.0)
        
        # Test validation with out-of-range values
        invalid_step = ChainOfThoughtStep(
            step_id="invalid_step",
            step_type=ReasoningStepType.INFERENCE,
            content="Test content",
            confidence=1.5,  # Out of range
            logical_strength=-0.2,  # Out of range
            factual_accuracy=0.5,
            relevance_score=0.7
        )
        
        # Values should be normalized
        self.assertEqual(invalid_step.confidence, 1.0)
        self.assertEqual(invalid_step.logical_strength, 0.0)
        
        logger.info("ChainOfThoughtStep creation and validation test passed")
    
    def test_reasoning_path_creation_and_updates(self):
        """Test ReasoningPath creation and metric updates."""
        path = ReasoningPath(path_id="test_path_1")
        
        # Initially empty
        self.assertEqual(len(path.steps), 0)
        self.assertEqual(path.status, ReasoningPathStatus.ACTIVE)
        
        # Add steps
        step1 = ChainOfThoughtStep(
            step_id="step1",
            step_type=ReasoningStepType.PREMISE,
            content="Initial premise",
            confidence=0.9,
            logical_strength=0.8,
            relevance_score=0.9
        )
        
        step2 = ChainOfThoughtStep(
            step_id="step2",
            step_type=ReasoningStepType.INFERENCE,
            content="Following inference",
            confidence=0.7,
            logical_strength=0.6,
            relevance_score=0.8
        )
        
        path.add_step(step1)
        path.add_step(step2)
        
        # Verify path updates
        self.assertEqual(len(path.steps), 2)
        self.assertGreater(path.overall_confidence, 0.0)
        self.assertGreater(path.logical_consistency, 0.0)
        
        # Verify metric calculations
        expected_confidence = np.average([0.9, 0.7], weights=[0.9 + 0.1, 0.8 + 0.1])
        self.assertAlmostEqual(path.overall_confidence, expected_confidence, places=2)
        
        logger.info("ReasoningPath creation and updates test passed")
    
    def test_path_evaluator_functionality(self):
        """Test PathEvaluator scoring and evaluation."""
        evaluator = PathEvaluator(validity_weight=0.4, progress_weight=0.4, diversity_weight=0.2)
        
        # Create test path
        path = ReasoningPath(path_id="eval_test_path")
        
        # Add comprehensive reasoning steps
        steps = [
            ChainOfThoughtStep(
                step_id="eval_step1",
                step_type=ReasoningStepType.PREMISE,
                content="Starting premise for evaluation",
                confidence=0.9,
                logical_strength=0.8,
                factual_accuracy=0.9,
                relevance_score=0.85
            ),
            ChainOfThoughtStep(
                step_id="eval_step2",
                step_type=ReasoningStepType.INFERENCE,
                content="Logical inference from premise",
                confidence=0.8,
                logical_strength=0.75,
                factual_accuracy=0.8,
                relevance_score=0.8
            ),
            ChainOfThoughtStep(
                step_id="eval_step3",
                step_type=ReasoningStepType.CONCLUSION,
                content="Final conclusion based on reasoning",
                confidence=0.85,
                logical_strength=0.8,
                factual_accuracy=0.75,
                relevance_score=0.9
            )
        ]
        
        for step in steps:
            path.add_step(step)
        
        # Evaluate path
        score = evaluator.evaluate_path(path, "test problem")
        
        # Verify score properties
        self.assertGreaterEqual(score, 0.0)
        self.assertLessEqual(score, 1.0)
        self.assertIsInstance(score, float)
        
        # Test with empty path
        empty_path = ReasoningPath(path_id="empty_path")
        empty_score = evaluator.evaluate_path(empty_path, "test problem")
        self.assertEqual(empty_score, 0.0)
        
        # Add path to reference for diversity testing
        evaluator.add_reference_path(path)
        self.assertEqual(len(evaluator.reference_paths), 1)
        
        logger.info(f"PathEvaluator functionality test passed - Score: {score:.3f}")
    
    def test_multi_path_chain_of_thought(self):
        """Test MultiPathChainOfThought reasoning generation."""
        multi_path = MultiPathChainOfThought(max_paths=3, max_steps=5, temperature=1.0)
        
        problem = self.test_problems['simple_math']
        paths = multi_path.generate_reasoning_paths(problem)
        
        # Verify paths were generated
        self.assertGreater(len(paths), 0, "Should generate at least one reasoning path")
        self.assertLessEqual(len(paths), 10, "Should not exceed reasonable path limit")
        
        # Verify path structure
        for path in paths:
            self.assertIsInstance(path, ReasoningPath)
            self.assertGreater(len(path.steps), 0, "Each path should have steps")
            self.assertEqual(path.status, ReasoningPathStatus.COMPLETE)
            
            # Verify first step is premise
            first_step = path.steps[0]
            self.assertEqual(first_step.step_type, ReasoningStepType.PREMISE)
            self.assertIn(problem, first_step.content)
        
        # Test best path selection
        best_path = multi_path.get_best_path()
        if best_path:
            self.assertIn(best_path, paths, "Best path should be from generated paths")
        
        # Test statistics
        stats = multi_path.get_reasoning_statistics()
        self.assertIn('total_paths_generated', stats)
        self.assertIn('completed_paths', stats)
        self.assertGreaterEqual(stats['completed_paths'], len(paths))
        
        logger.info(f"MultiPathChainOfThought test passed - Generated {len(paths)} paths")
    
    def test_tree_of_thought_node_operations(self):
        """Test TreeOfThoughtNode operations and scoring."""
        root = TreeOfThoughtNode(
            content="Root problem statement",
            step_type=ReasoningStepType.PREMISE
        )
        
        # Verify initial state
        self.assertEqual(root.depth, 0)
        self.assertIsNone(root.parent)
        self.assertEqual(len(root.children), 0)
        self.assertTrue(root.is_leaf())
        
        # Create child nodes
        child1 = TreeOfThoughtNode(
            content="First reasoning step",
            step_type=ReasoningStepType.INFERENCE,
            parent=root
        )
        
        child2 = TreeOfThoughtNode(
            content="Alternative reasoning step",
            step_type=ReasoningStepType.HYPOTHESIS,
            parent=root
        )
        
        # Verify parent-child relationships
        self.assertEqual(len(root.children), 2)
        self.assertFalse(root.is_leaf())
        self.assertEqual(child1.depth, 1)
        self.assertEqual(child2.depth, 1)
        self.assertEqual(child1.parent, root)
        self.assertEqual(child2.parent, root)
        
        # Test UCB scoring
        root.visit_count = 10
        root.value_estimate = 0.8
        child1.visit_count = 3
        child1.value_estimate = 0.6
        
        ucb_score = child1.calculate_ucb_score(exploration_constant=1.4)
        self.assertGreater(ucb_score, 0.0)
        self.assertIsInstance(ucb_score, float)
        
        # Test backpropagation
        initial_visits = child1.visit_count
        initial_value = child1.value_estimate
        
        child1.backpropagate(0.9)
        
        self.assertEqual(child1.visit_count, initial_visits + 1)
        # Value should be updated (incremental average)
        expected_value = (initial_value * initial_visits + 0.9) / (initial_visits + 1)
        self.assertAlmostEqual(child1.value_estimate, expected_value, places=3)
        
        # Test path to root
        path = child1.get_path_to_root()
        self.assertEqual(len(path), 2)
        self.assertEqual(path[0], root)
        self.assertEqual(path[1], child1)
        
        logger.info("TreeOfThoughtNode operations test passed")
    
    def test_tree_of_thought_exploration(self):
        """Test TreeOfThoughtExplorer functionality."""
        explorer = TreeOfThoughtExplorer(max_depth=4, branching_factor=2, exploration_constant=1.4)
        
        problem = self.test_problems['logical_puzzle']
        valuable_leaves = explorer.explore(problem, max_iterations=20)
        
        # Verify exploration results
        self.assertIsInstance(valuable_leaves, list)
        
        for leaf in valuable_leaves:
            self.assertIsInstance(leaf, TreeOfThoughtNode)
            self.assertTrue(leaf.is_leaf())
            self.assertGreaterEqual(leaf.value_estimate, explorer.value_threshold)
        
        # Verify tree structure
        self.assertIsNotNone(explorer.root)
        self.assertEqual(explorer.root.content, problem)
        self.assertEqual(explorer.root.step_type, ReasoningStepType.PREMISE)
        
        # Test best reasoning path extraction
        best_path = explorer.get_best_reasoning_path()
        if best_path:
            self.assertIsInstance(best_path, list)
            self.assertGreater(len(best_path), 0)
            self.assertEqual(best_path[0], problem)  # Should start with problem
        
        # Test statistics
        stats = explorer.get_tree_statistics()
        self.assertIn('total_nodes', stats)
        self.assertIn('total_expansions', stats)
        self.assertIn('total_backtracks', stats)
        self.assertGreaterEqual(stats['total_nodes'], 1)  # At least root node
        
        logger.info(f"TreeOfThoughtExplorer test passed - Found {len(valuable_leaves)} valuable leaves")
    
    def test_reasoning_node_and_edge_creation(self):
        """Test ReasoningNode and ReasoningEdge creation."""
        node = ReasoningNode(
            node_id="test_node_1",
            content="Test reasoning node content",
            node_type=ReasoningStepType.EVIDENCE,
            confidence=0.85
        )
        
        # Verify node properties
        self.assertEqual(node.node_id, "test_node_1")
        self.assertEqual(node.content, "Test reasoning node content")
        self.assertEqual(node.node_type, ReasoningStepType.EVIDENCE)
        self.assertEqual(node.confidence, 0.85)
        self.assertEqual(node.visit_count, 0)
        
        # Test confidence normalization
        invalid_node = ReasoningNode(
            node_id="invalid_node",
            content="Invalid node",
            node_type=ReasoningStepType.PREMISE,
            confidence=1.5  # Out of range
        )
        self.assertEqual(invalid_node.confidence, 1.0)
        
        # Create second node for edge testing
        node2 = ReasoningNode(
            node_id="test_node_2",
            content="Second test node",
            node_type=ReasoningStepType.INFERENCE,
            confidence=0.7
        )
        
        # Create edge
        edge = ReasoningEdge(
            edge_id="test_edge_1",
            source_id=node.node_id,
            target_id=node2.node_id,
            relationship_type="leads_to",
            strength=0.8,
            confidence=0.75
        )
        
        # Verify edge properties
        self.assertEqual(edge.source_id, "test_node_1")
        self.assertEqual(edge.target_id, "test_node_2")
        self.assertEqual(edge.relationship_type, "leads_to")
        self.assertEqual(edge.strength, 0.8)
        self.assertEqual(edge.confidence, 0.75)
        
        logger.info("ReasoningNode and ReasoningEdge creation test passed")
    
    def test_reasoning_graph_construction(self):
        """Test ReasoningGraph construction and analysis."""
        graph = ReasoningGraph()
        
        # Initially empty
        self.assertEqual(len(graph.nodes), 0)
        self.assertEqual(len(graph.edges), 0)
        
        # Build graph from problem
        problem = self.test_problems['scientific_reasoning']
        graph.build_reasoning_graph(problem)
        
        # Verify graph was built
        self.assertGreater(len(graph.nodes), 0, "Graph should have nodes")
        self.assertGreater(len(graph.edges), 0, "Graph should have edges")
        
        # Verify root node exists
        self.assertIn("root", graph.nodes)
        root_node = graph.nodes["root"]
        self.assertEqual(root_node.content, problem)
        self.assertEqual(root_node.node_type, ReasoningStepType.PREMISE)
        
        # Test consistency checking
        is_consistent = graph.check_consistency()
        self.assertIsInstance(is_consistent, bool)
        
        # Test strongest conclusions
        conclusions = graph.get_strongest_conclusions(top_k=3)
        self.assertIsInstance(conclusions, list)
        self.assertLessEqual(len(conclusions), 3)
        
        for conclusion in conclusions:
            self.assertIsInstance(conclusion, ReasoningNode)
            self.assertEqual(conclusion.node_type, ReasoningStepType.CONCLUSION)
        
        # Test path finding
        if conclusions:
            conclusion_id = conclusions[0].node_id
            paths = graph.find_reasoning_paths("root", conclusion_id)
            self.assertIsInstance(paths, list)
            
            # Test justification extraction
            justification = graph.extract_justification(conclusion_id)
            self.assertIsInstance(justification, list)
        
        # Test statistics
        stats = graph.get_graph_statistics()
        self.assertIn('total_nodes', stats)
        self.assertIn('total_edges', stats)
        self.assertIn('node_type_distribution', stats)
        self.assertEqual(stats['total_nodes'], len(graph.nodes))
        self.assertEqual(stats['total_edges'], len(graph.edges))
        
        logger.info(f"ReasoningGraph construction test passed - {len(graph.nodes)} nodes, {len(graph.edges)} edges")
    
    def test_critique_result_creation(self):
        """Test CritiqueResult creation and validation."""
        critique = CritiqueResult(
            critique_id="test_critique_1",
            critique_type=CritiqueType.LOGICAL_VALIDITY,
            target_step_id="target_step_1",
            severity=0.8,
            description="Test critique description",
            suggested_improvements=["Improve logical flow", "Add supporting evidence"],
            confidence=0.7
        )
        
        # Verify properties
        self.assertEqual(critique.critique_id, "test_critique_1")
        self.assertEqual(critique.critique_type, CritiqueType.LOGICAL_VALIDITY)
        self.assertEqual(critique.target_step_id, "target_step_1")
        self.assertEqual(critique.severity, 0.8)
        self.assertEqual(len(critique.suggested_improvements), 2)
        
        # Test severity normalization
        invalid_critique = CritiqueResult(
            critique_id="invalid_critique",
            critique_type=CritiqueType.FACTUAL_ACCURACY,
            target_step_id="target_step_2",
            severity=1.5,  # Out of range
            description="Invalid critique",
            confidence=-0.1  # Out of range
        )
        
        self.assertEqual(invalid_critique.severity, 1.0)
        self.assertEqual(invalid_critique.confidence, 0.0)
        
        logger.info("CritiqueResult creation and validation test passed")
    
    def test_self_critique_analyzer(self):
        """Test SelfCritiqueAnalyzer functionality."""
        analyzer = SelfCritiqueAnalyzer(max_iterations=2)
        
        # Create test path with potential issues
        path = ReasoningPath(path_id="critique_test_path")
        
        # Add steps with various quality levels
        steps = [
            ChainOfThoughtStep(
                step_id="step1",
                step_type=ReasoningStepType.PREMISE,
                content="This is obviously true and everyone knows it",  # Potential issues
                confidence=0.9,
                logical_strength=0.5,  # Low logical strength
                factual_accuracy=0.4,  # Low factual accuracy
                relevance_score=0.8
            ),
            ChainOfThoughtStep(
                step_id="step2",
                step_type=ReasoningStepType.CONCLUSION,
                content="Therefore, the answer is clear",  # Unsupported conclusion
                confidence=0.8,
                logical_strength=0.6,
                factual_accuracy=0.7,
                relevance_score=0.3  # Low relevance
            )
        ]
        
        for step in steps:
            path.add_step(step)
        
        # Analyze path
        critiques = analyzer.analyze_reasoning_path(path)
        
        # Verify critiques were generated
        self.assertIsInstance(critiques, list)
        self.assertGreater(len(critiques), 0, "Should generate critiques for problematic path")
        
        # Verify critique structure
        for critique in critiques:
            self.assertIsInstance(critique, CritiqueResult)
            self.assertIn(critique.critique_type, CritiqueType)
            self.assertGreaterEqual(critique.severity, 0.0)
            self.assertLessEqual(critique.severity, 1.0)
            self.assertIsInstance(critique.suggested_improvements, list)
        
        # Test path refinement
        refined_path = analyzer.refine_reasoning_path(path, critiques)
        self.assertIsInstance(refined_path, ReasoningPath)
        self.assertNotEqual(refined_path, path)  # Should be different object
        self.assertEqual(len(refined_path.steps), len(path.steps))  # Same number of steps
        
        # Test statistics
        stats = analyzer.get_critique_statistics()
        self.assertIn('total_paths_analyzed', stats)
        self.assertIn('total_critiques_generated', stats)
        self.assertGreater(stats['total_paths_analyzed'], 0)
        
        logger.info(f"SelfCritiqueAnalyzer test passed - Generated {len(critiques)} critiques")
    
    def test_integrated_system_reasoning(self):
        """Test the integrated ULTRA Chain of Thought system."""
        # Test different reasoning modes
        reasoning_modes = ["multi_path", "tree", "graph", "integrated"]
        
        for mode in reasoning_modes:
            with self.subTest(reasoning_mode=mode):
                system = ULTRAChainOfThoughtSystem(reasoning_mode=mode)
                
                problem = self.test_problems['complex_analysis']
                context = "Consider both environmental and economic factors"
                
                # Execute reasoning
                results = system.reason(problem, context)
                
                # Verify basic result structure
                self.assertIsInstance(results, dict)
                self.assertIn('session_id', results)
                self.assertIn('problem_statement', results)
                self.assertIn('reasoning_mode', results)
                self.assertIn('start_time', results)
                self.assertIn('end_time', results)
                self.assertIn('total_time', results)
                self.assertIn('success', results)
                
                self.assertEqual(results['problem_statement'], problem)
                self.assertEqual(results['reasoning_mode'], mode)
                self.assertGreater(results['total_time'], 0.0)
                
                # Mode-specific verifications
                if mode == "multi_path":
                    self.assertIn('reasoning_paths', results)
                    self.assertIn('best_path', results)
                    self.assertIn('total_paths', results)
                
                elif mode == "tree":
                    self.assertIn('valuable_nodes', results)
                    self.assertIn('best_reasoning_path', results)
                
                elif mode == "graph":
                    self.assertIn('reasoning_paths', results)
                    self.assertIn('strongest_conclusions', results)
                    self.assertIn('is_consistent', results)
                
                elif mode == "integrated":
                    self.assertIn('component_results', results)
                    self.assertIn('synthesis_approach', results)
                
                logger.info(f"Integrated system test passed for mode: {mode}")
    
    def test_system_performance_tracking(self):
        """Test system performance tracking and metrics."""
        system = self.cot_system
        
        # Run multiple reasoning sessions
        problems = [
            self.test_problems['simple_math'],
            self.test_problems['logical_puzzle'],
            self.test_problems['scientific_reasoning']
        ]
        
        for problem in problems:
            results = system.reason(problem)
            self.assertTrue(results.get('success', False), f"Reasoning should succeed for: {problem[:50]}...")
        
        # Test performance metrics
        performance = system.get_system_performance()
        self.assertIsInstance(performance, dict)
        
        if 'reasoning_time' in performance:
            time_stats = performance['reasoning_time']
            self.assertIn('mean', time_stats)
            self.assertIn('std', time_stats)
            self.assertIn('count', time_stats)
            self.assertEqual(time_stats['count'], len(problems))
        
        # Test system statistics
        stats = system.get_system_statistics()
        self.assertIsInstance(stats, dict)
        self.assertIn('system_overview', stats)
        
        system_overview = stats['system_overview']
        self.assertEqual(system_overview['total_sessions'], len(problems))
        self.assertEqual(system_overview['reasoning_mode'], 'integrated')
        
        logger.info(f"System performance tracking test passed - {len(problems)} sessions analyzed")
    
    def test_reasoning_pattern_validation(self):
        """Test that reasoning follows expected patterns for different problem types."""
        system = ULTRAChainOfThoughtSystem(reasoning_mode="multi_path")
        
        for problem_type, expected_pattern in self.expected_patterns.items():
            with self.subTest(problem_type=problem_type):
                problem = self.test_problems[problem_type]
                results = system.reason(problem)
                
                if results.get('success') and 'reasoning_paths' in results:
                    paths = results['reasoning_paths']
                    
                    if paths:
                        # Check if any path follows expected pattern
                        pattern_found = False
                        
                        for path in paths:
                            if isinstance(path, ReasoningPath):
                                step_types = [step.step_type for step in path.steps]
                                
                                # Check if expected pattern is a subsequence
                                if self._is_subsequence(expected_pattern, step_types):
                                    pattern_found = True
                                    break
                        
                        # Some flexibility - not all paths need to follow exact pattern
                        # but at least one should have logical progression
                        if not pattern_found:
                            logger.warning(f"Expected reasoning pattern not found for {problem_type}")
                
                logger.info(f"Reasoning pattern validation completed for {problem_type}")
    
    def _is_subsequence(self, pattern: List[ReasoningStepType], sequence: List[ReasoningStepType]) -> bool:
        """Check if pattern is a subsequence of sequence."""
        if not pattern:
            return True
        if not sequence:
            return False
        
        pattern_idx = 0
        
        for step_type in sequence:
            if pattern_idx < len(pattern) and step_type == pattern[pattern_idx]:
                pattern_idx += 1
                if pattern_idx == len(pattern):
                    return True
        
        return pattern_idx == len(pattern)
    
    def test_error_handling_and_edge_cases(self):
        """Test system behavior with edge cases and error conditions."""
        system = self.cot_system
        
        # Test empty problem statement
        empty_result = system.reason("")
        self.assertIsInstance(empty_result, dict)
        self.assertIn('success', empty_result)
        
        # Test very long problem statement
        long_problem = "This is a very long problem statement. " * 100
        long_result = system.reason(long_problem)
        self.assertIsInstance(long_result, dict)
        
        # Test special characters and formatting
        special_problem = "Problem with special chars: @#$%^&*(){}[]|\\:;\"'<>?,./"
        special_result = system.reason(special_problem)
        self.assertIsInstance(special_result, dict)
        
        # Test Unicode characters
        unicode_problem = "问题：如何解决这个问题？ What is the αβγ solution to ∑∞?"
        unicode_result = system.reason(unicode_problem)
        self.assertIsInstance(unicode_result, dict)
        
        # Test system state reset
        initial_history_length = len(system.reasoning_history)
        system.reset_system_state()
        self.assertEqual(len(system.reasoning_history), 0)
        
        logger.info("Error handling and edge cases test passed")
    
    def test_reasoning_step_type_coverage(self):
        """Test that all reasoning step types are properly handled."""
        # Test each reasoning step type
        for step_type in ReasoningStepType:
            with self.subTest(step_type=step_type):
                step = ChainOfThoughtStep(
                    step_id=f"test_{step_type.value}",
                    step_type=step_type,
                    content=f"Test content for {step_type.value}",
                    confidence=0.7,
                    logical_strength=0.6,
                    factual_accuracy=0.8,
                    relevance_score=0.7
                )
                
                self.assertEqual(step.step_type, step_type)
                self.assertIsInstance(step.step_type, ReasoningStepType)
        
        logger.info(f"All {len(ReasoningStepType)} reasoning step types are properly handled")
    
    def test_critique_type_coverage(self):
        """Test that all critique types are properly handled."""
        analyzer = SelfCritiqueAnalyzer()
        
        # Test each critique type
        for critique_type in CritiqueType:
            with self.subTest(critique_type=critique_type):
                critique = CritiqueResult(
                    critique_id=f"test_{critique_type.value}",
                    critique_type=critique_type,
                    target_step_id="test_step",
                    severity=0.5,
                    description=f"Test critique for {critique_type.value}",
                    confidence=0.7
                )
                
                self.assertEqual(critique.critique_type, critique_type)
                self.assertIsInstance(critique.critique_type, CritiqueType)
        
        # Verify critique weights exist for all types
        for critique_type in CritiqueType:
            self.assertIn(critique_type, analyzer.critique_weights)
        
        logger.info(f"All {len(CritiqueType)} critique types are properly handled")
    
    def test_reasoning_path_status_transitions(self):
        """Test reasoning path status transitions."""
        path = ReasoningPath(path_id="status_test_path")
        
        # Initial status
        self.assertEqual(path.status, ReasoningPathStatus.ACTIVE)
        
        # Test valid status transitions
        valid_statuses = [
            ReasoningPathStatus.COMPLETE,
            ReasoningPathStatus.PRUNED,
            ReasoningPathStatus.ERROR,
            ReasoningPathStatus.SUSPENDED
        ]
        
        for status in valid_statuses:
            with self.subTest(status=status):
                path.status = status
                self.assertEqual(path.status, status)
                self.assertIsInstance(path.status, ReasoningPathStatus)
        
        logger.info("Reasoning path status transitions test passed")
    
    def test_system_export_and_import(self):
        """Test system export and import functionality."""
        system = self.cot_system
        
        # Run a reasoning session
        problem = self.test_problems['simple_math']
        results = system.reason(problem)
        session_id = results['session_id']
        
        # Export session
        exported_session = system.export_reasoning_session(session_id)
        self.assertIsNotNone(exported_session)
        self.assertEqual(exported_session['session_id'], session_id)
        self.assertEqual(exported_session['problem_statement'], problem)
        
        # Test export of non-existent session
        non_existent_export = system.export_reasoning_session("non_existent_id")
        self.assertIsNone(non_existent_export)
        
        logger.info("System export and import test passed")
    
    def test_concurrent_reasoning_safety(self):
        """Test system behavior under concurrent access patterns."""
        import threading
        import time
        
        system = ULTRAChainOfThoughtSystem(reasoning_mode="multi_path")
        results = []
        errors = []
        
        def reasoning_worker(worker_id):
            try:
                problem = f"Worker {worker_id}: {self.test_problems['simple_math']}"
                result = system.reason(problem)
                results.append(result)
            except Exception as e:
                errors.append(f"Worker {worker_id}: {str(e)}")
        
        # Create and start multiple threads
        threads = []
        for i in range(3):  # Moderate concurrency for testing
            thread = threading.Thread(target=reasoning_worker, args=(i,))
            threads.append(thread)
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join(timeout=30)  # 30 second timeout
        
        # Verify results
        self.assertEqual(len(errors), 0, f"Concurrent reasoning errors: {errors}")
        self.assertEqual(len(results), 3, "Should have results from all workers")
        
        # Verify each result has unique session ID
        session_ids = [r.get('session_id') for r in results if 'session_id' in r]
        self.assertEqual(len(session_ids), len(set(session_ids)), "Session IDs should be unique")
        
        logger.info("Concurrent reasoning safety test passed")
    
    def test_memory_efficiency(self):
        """Test memory efficiency and cleanup."""
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss
        
        system = ULTRAChainOfThoughtSystem(reasoning_mode="integrated")
        
        # Run multiple reasoning sessions
        for i in range(5):
            problem = f"Memory test {i}: {self.test_problems['complex_analysis']}"
            results = system.reason(problem)
            self.assertTrue(results.get('success', False))
        
        # Check memory usage
        current_memory = process.memory_info().rss
        memory_increase = current_memory - initial_memory
        
        # Memory increase should be reasonable (less than 100MB for this test)
        self.assertLess(memory_increase, 100 * 1024 * 1024, 
                       f"Memory usage increased by {memory_increase / 1024 / 1024:.1f}MB")
        
        # Test system reset and memory cleanup
        system.reset_system_state()
        
        # Allow some time for garbage collection
        import gc
        gc.collect()
        time.sleep(0.1)
        
        logger.info(f"Memory efficiency test passed - Memory increase: {memory_increase / 1024 / 1024:.1f}MB")
    
    def test_comprehensive_integration(self):
        """Comprehensive integration test combining all system components."""
        system = ULTRAChainOfThoughtSystem(reasoning_mode="integrated")
        
        # Test complex, multi-faceted problem
        complex_problem = """
        A city is experiencing rapid population growth, leading to increased traffic congestion, 
        housing shortages, and environmental concerns. The city council needs to develop a 
        comprehensive urban planning strategy that addresses these interconnected challenges 
        while maintaining economic growth and quality of life for residents. 
        What should be the key components of this strategy?
        """
        
        context = """
        Consider factors such as:
        - Sustainable transportation systems
        - Affordable housing development
        - Environmental impact mitigation
        - Economic development opportunities
        - Community engagement and social equity
        - Infrastructure capacity and financing
        """
        
        # Execute comprehensive reasoning
        results = system.reason(complex_problem, context)
        
        # Verify comprehensive result structure
        self.assertTrue(results.get('success', False), "Complex reasoning should succeed")
        self.assertIn('component_results', results)
        self.assertIn('synthesis_approach', results)
        
        # Verify all reasoning approaches were attempted
        component_results = results['component_results']
        expected_components = ['multi_path', 'tree', 'graph']
        
        for component in expected_components:
            if component in component_results:
                self.assertIn('success', component_results[component])
        
        # Verify integration synthesis
        if 'integrated_paths' in results:
            integrated_paths = results['integrated_paths']
            self.assertIsInstance(integrated_paths, list)
            
            # Paths should have proper structure
            for path in integrated_paths:
                self.assertIn('source', path)
                self.assertIn('content', path)
                self.assertIn('confidence', path)
                self.assertIn('weighted_score', path)
        
        # Test self-critique integration
        if 'self_critique_applied' in results:
            self.assertTrue(results['self_critique_applied'])
            self.assertIn('improvement_summary', results)
        
        # Verify timing and performance
        self.assertGreater(results['total_time'], 0.0)
        self.assertLess(results['total_time'], 120.0)  # Should complete within 2 minutes
        
        # Test system statistics after complex reasoning
        stats = system.get_system_statistics()
        self.assertIsInstance(stats, dict)
        self.assertIn('system_overview', stats)
        
        logger.info(f"Comprehensive integration test passed - Total time: {results['total_time']:.2f}s")


if __name__ == '__main__':
    # Configure test runner with comprehensive settings
    unittest.main(
        verbosity=2,
        failfast=False,
        buffer=True,
        warnings='ignore',
        testRunner=unittest.TextTestRunner(
            verbosity=2,
            descriptions=True,
            failfast=False
        )
    ) 