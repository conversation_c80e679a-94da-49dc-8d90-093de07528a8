#!/usr/bin/env python3
"""
Test initialization module for ULTRA Meta-Cognitive System.

This module contains comprehensive tests for the Meta-Cognitive System components:
- Multi-Path Chain of Thought
- Tree of Thought Exploration
- Reasoning Graphs
- Self-Critique Loop
- Bias Detection and Correction
- Meta-Learning Controller

The tests validate core functionality, mathematical correctness, and integration
capabilities of the Meta-Cognitive System within the ULTRA framework.
"""

import os
import sys
import unittest
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Tuple, Any, Optional, Union, Callable, Set
import networkx as nx
import pytest
from collections import defaultdict, deque
import math
import random
import inspect
import json
import time

# Add the parent directory to the path so we can import from the ultra package
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../../')))

# Import the Meta-Cognitive System components
from ultra.meta_cognitive.chain_of_thought import MultiPathChainOfThought
from ultra.meta_cognitive.tree_of_thought import TreeOfThoughtExploration
from ultra.meta_cognitive.reasoning_graphs import ReasoningGraphs
from ultra.meta_cognitive.self_critique import Self<PERSON>rit<PERSON><PERSON>oop
from ultra.meta_cognitive.bias_detection import BiasDetection
from ultra.meta_cognitive.meta_learning import MetaLearningController

# Import other necessary components
from ultra.utils.config import Config
from ultra.utils.ultra_logging import get_logger
from ultra.utils.visualization import visualize_reasoning_path, visualize_reasoning_graph
from ultra.utils.metrics import measure_logical_consistency, measure_reasoning_quality

# Set up logger
logger = get_logger(__name__)

class MockLanguageModel:
    """
    Mock language model class for testing meta-cognitive components
    that require language model capabilities.
    """
    def __init__(self, vocab_size=50000, embedding_dim=768, hidden_dim=2048):
        self.vocab_size = vocab_size
        self.embedding_dim = embedding_dim
        self.hidden_dim = hidden_dim
        
        # Create simple transformer-based architecture
        self.embedding = nn.Embedding(vocab_size, embedding_dim)
        self.encoder = nn.TransformerEncoder(
            nn.TransformerEncoderLayer(
                d_model=embedding_dim, 
                nhead=8, 
                dim_feedforward=hidden_dim,
                dropout=0.1
            ), 
            num_layers=6
        )
        self.decoder = nn.Linear(embedding_dim, vocab_size)
        
        # Sample knowledge for testing
        self.knowledge_base = {
            "math": {
                "probability": 0.85,
                "algebra": 0.9,
                "calculus": 0.8,
                "geometry": 0.75
            },
            "science": {
                "physics": 0.8,
                "chemistry": 0.7,
                "biology": 0.75
            },
            "reasoning": {
                "deductive": 0.9,
                "inductive": 0.85,
                "abductive": 0.8
            }
        }
    
    def generate(self, prompt, max_new_tokens=100, num_return_sequences=1, 
                temperature=0.7, stop_sequences=None):
        """
        Mock text generation that returns pre-defined responses based on prompts.
        
        Args:
            prompt (str): Input text prompt
            max_new_tokens (int): Maximum new tokens to generate
            num_return_sequences (int): Number of different sequences to return
            temperature (float): Sampling temperature
            stop_sequences (List[str]): Sequences that stop generation
            
        Returns:
            Union[str, List[str]]: Generated text or list of generated texts
        """
        # Test string matching for different types of generation
        responses = []
        
        if "observation" in prompt.lower():
            observations = [
                "The equation has variables on both sides: 3x + 2 = 2x - 5",
                "The coefficient of x on the left is 3",
                "The coefficient of x on the right is 2",
                "There's a constant term of 2 on the left",
                "There's a constant term of -5 on the right"
            ]
            if num_return_sequences == 1:
                return observations[0]
            else:
                return observations[:min(num_return_sequences, len(observations))]
                
        elif "hypothesis" in prompt.lower():
            hypotheses = [
                "We need to isolate x by moving all variable terms to one side",
                "We should combine like terms to simplify the equation",
                "Subtracting 2x from both sides will isolate the variable terms on the left",
                "Adding 5 to both sides will move all constants to the right"
            ]
            if num_return_sequences == 1:
                return hypotheses[0]
            else:
                return hypotheses[:min(num_return_sequences, len(hypotheses))]
                
        elif "conclusion" in prompt.lower():
            conclusions = [
                "Therefore, x = -7",
                "The solution to the equation is x = -7",
                "By solving the equation, we get x = -7",
                "The value of x that satisfies the equation is -7"
            ]
            if num_return_sequences == 1:
                return conclusions[0]
            else:
                return conclusions[:min(num_return_sequences, len(conclusions))]
                
        elif "critique" in prompt.lower():
            critiques = [
                "The reasoning contains a calculation error when combining like terms",
                "The solution doesn't verify the original equation; substituting x = -7 gives 3(-7) + 2 = 2(-7) - 5, which is -19 ≠ -19",
                "The method is correct, but there's a sign error when moving terms between sides",
                "The reasoning is logically sound and mathematically correct"
            ]
            if num_return_sequences == 1:
                return critiques[random.randint(0, len(critiques)-1)]
            else:
                random.shuffle(critiques)
                return critiques[:min(num_return_sequences, len(critiques))]
        
        elif "coherence" in prompt.lower() or "validity" in prompt.lower():
            # Return a random score between 0 and 1 for coherence/validity evaluations
            if num_return_sequences == 1:
                return str(round(random.uniform(0.5, 0.95), 2))
            else:
                return [str(round(random.uniform(0.5, 0.95), 2)) for _ in range(num_return_sequences)]
        
        elif "bias" in prompt.lower():
            # For bias detection, return different bias scores
            bias_scores = {
                "confirmation bias": round(random.uniform(0.7, 0.9), 2),
                "availability bias": round(random.uniform(0.4, 0.6), 2),
                "anchoring bias": round(random.uniform(0.5, 0.8), 2),
                "overconfidence bias": round(random.uniform(0.6, 0.9), 2)
            }
            
            if "score" in prompt.lower():
                for bias_type in bias_scores:
                    if bias_type in prompt.lower():
                        return str(bias_scores[bias_type])
                # Default score if no specific bias mentioned
                return str(round(random.uniform(0.3, 0.8), 2))
            else:
                return json.dumps(bias_scores)
                
        else:
            # Default responses for continuation of reasoning chains
            default_continuations = [
                "First, I'll combine like terms by moving all variables to the left side: 3x - 2x = -5 - 2",
                "Next, I'll simplify the expression: x = -7",
                "To verify, I'll substitute x = -7 back into the original equation: 3(-7) + 2 = 2(-7) - 5",
                "Computing the left side: 3(-7) + 2 = -21 + 2 = -19",
                "Computing the right side: 2(-7) - 5 = -14 - 5 = -19",
                "Since both sides equal -19, the solution x = -7 is correct."
            ]
            
            if num_return_sequences == 1:
                # Select one based on position in the chain
                idx = len(prompt.split("\n")) % len(default_continuations)
                return default_continuations[idx]
            else:
                random.shuffle(default_continuations)
                return default_continuations[:min(num_return_sequences, len(default_continuations))]
    
    def compute_coherence(self, current_text, new_text):
        """
        Compute a coherence score between current text and new text.
        
        Args:
            current_text (str): The existing text
            new_text (str): The new text to evaluate
            
        Returns:
            float: Coherence score between 0 and 1
        """
        # Simple mock implementation - return higher scores for longer responses
        # with some randomness
        base_score = 0.7
        length_factor = min(len(new_text) / 100, 0.2)  # Up to 0.2 bonus for length
        random_factor = random.uniform(-0.1, 0.1)  # Random variation
        
        # Higher coherence for continuations that mention previous concepts
        overlap_bonus = 0.0
        for word in current_text.split():
            if len(word) > 4 and word in new_text:  # Only consider non-trivial words
                overlap_bonus += 0.02
        
        return min(base_score + length_factor + random_factor + overlap_bonus, 0.98)
    
    def compute_logical_validity(self, text):
        """
        Compute logical validity score for a piece of text.
        
        Args:
            text (str): Text to evaluate
            
        Returns:
            float: Validity score between 0 and 1
        """
        # Simple mock implementation
        base_score = 0.75
        
        # Patterns that indicate logical structure
        logic_patterns = [
            "therefore", "thus", "hence", "so", "because", "since",
            "if", "then", "implies", "follows", "conclude", "infer"
        ]
        
        logic_bonus = sum(0.03 for pattern in logic_patterns if pattern in text.lower())
        
        # Mathematical expressions increase validity score
        math_bonus = 0.0
        if any(c in text for c in "+-*/=≠≤≥<>√∑∫"):
            math_bonus = 0.1
            
        return min(base_score + logic_bonus + math_bonus, 0.95)
        
    def is_final_answer(self, text):
        """
        Check if text contains indicators of a final answer.
        
        Args:
            text (str): Text to check
            
        Returns:
            bool: True if it appears to be a final answer
        """
        indicators = ["therefore", "thus", "hence", "so", "the answer is", "we conclude", "in conclusion"]
        return any(indicator in text.lower() for indicator in indicators)


class MockEvaluator:
    """
    Mock evaluator class for testing reasoning evaluation.
    """
    def __init__(self):
        pass
        
    def evaluate(self, context, continuation):
        """
        Evaluate the quality of a reasoning continuation.
        
        Args:
            context (str): The existing reasoning context
            continuation (str): The proposed continuation
            
        Returns:
            float: Quality score between 0 and 1
        """
        # Base score
        score = 0.6
        
        # Increase score for longer, more detailed continuations
        length_bonus = min(len(continuation) / 200, 0.15)
        
        # Increase score for continuations that reference the context
        context_words = set(w.lower() for w in context.split() if len(w) > 4)
        continuation_words = set(w.lower() for w in continuation.split() if len(w) > 4)
        overlap = context_words.intersection(continuation_words)
        reference_bonus = min(len(overlap) * 0.02, 0.15)
        
        # Randomness
        random_factor = random.uniform(-0.05, 0.05)
        
        return min(score + length_bonus + reference_bonus + random_factor, 0.95)


class TestMultiPathChainOfThought(unittest.TestCase):
    """Test cases for the Multi-Path Chain of Thought component."""
    
    def setUp(self):
        """Set up test resources."""
        self.language_model = MockLanguageModel()
        self.chain_of_thought = MultiPathChainOfThought(
            language_model=self.language_model,
            max_paths=5,
            beam_width=3
        )
        
        # Sample problem statements
        self.problems = [
            "Solve the equation: 3x + 2 = 2x - 5",
            "A train travels at 60 km/h. How far will it travel in 2.5 hours?",
            "If a rectangle has a length of 8 meters and a width of 6 meters, what is its area?"
        ]
    
    def test_initialization(self):
        """Test proper initialization of the component."""
        self.assertEqual(self.chain_of_thought.max_paths, 5)
        self.assertEqual(self.chain_of_thought.beam_width, 3)
        self.assertIsInstance(self.chain_of_thought.language_model, MockLanguageModel)
    
    def test_generate_next_steps(self):
        """Test the generation of reasoning continuation steps."""
        current_text = "Solve the equation: 3x + 2 = 2x - 5\nFirst, I'll move all terms with x to one side."
        next_steps = self.chain_of_thought.generate_next_steps(current_text, num_steps=2)
        
        # Check the structure of returned data
        self.assertIsInstance(next_steps, list)
        self.assertEqual(len(next_steps), 2)
        for step, score in next_steps:
            self.assertIsInstance(step, str)
            self.assertIsInstance(score, float)
            self.assertTrue(0 <= score <= 1)
    
    def test_is_final_answer(self):
        """Test detection of final answers in reasoning steps."""
        final_step = "Therefore, x = -7"
        non_final_step = "Moving all x terms to the left side gives us: 3x - 2x = -5 - 2"
        
        self.assertTrue(self.chain_of_thought.is_final_answer(final_step))
        self.assertFalse(self.chain_of_thought.is_final_answer(non_final_step))
    
    def test_generate_reasoning_paths(self):
        """Test the generation of complete reasoning paths."""
        problem = self.problems[0]
        paths = self.chain_of_thought.generate_reasoning_paths(problem)
        
        # Check the structure and content of the returned paths
        self.assertIsInstance(paths, list)
        self.assertLessEqual(len(paths), self.chain_of_thought.max_paths)
        
        # Check that at least one path contains a final answer
        has_final_answer = False
        for path in paths:
            self.assertIn("text", path)
            self.assertIn("score", path)
            
            if "therefore" in path["text"].lower() or "thus" in path["text"].lower():
                has_final_answer = True
                
        self.assertTrue(has_final_answer, "No path contains a final answer")
    
    def test_path_diversity(self):
        """Test that generated paths are sufficiently diverse."""
        problem = self.problems[0]
        paths = self.chain_of_thought.generate_reasoning_paths(problem)
        
        # Need at least 2 paths to test diversity
        if len(paths) >= 2:
            # Create sets of sentences for each path
            path_sentences = [set(path["text"].split('\n')) for path in paths]
            
            # Check that paths are not identical
            for i in range(len(paths) - 1):
                for j in range(i + 1, len(paths)):
                    overlap = path_sentences[i].intersection(path_sentences[j])
                    unique_sentences = path_sentences[i].union(path_sentences[j]) - overlap
                    
                    # Ensure at least some diversity between paths
                    self.assertGreater(len(unique_sentences), 0, 
                                      f"Paths {i} and {j} are identical")
    
    def test_score_based_path_selection(self):
        """Test that paths are ordered by their scores."""
        problem = self.problems[0]
        paths = self.chain_of_thought.generate_reasoning_paths(problem)
        
        # Check that paths are ordered by score (descending)
        if len(paths) >= 2:
            for i in range(len(paths) - 1):
                self.assertGreaterEqual(paths[i]["score"], paths[i+1]["score"],
                                      "Paths are not sorted by score")


class TestTreeOfThoughtExploration(unittest.TestCase):
    """Test cases for the Tree of Thought Exploration component."""
    
    def setUp(self):
        """Set up test resources."""
        self.language_model = MockLanguageModel()
        self.evaluator = MockEvaluator()
        self.tree_of_thought = TreeOfThoughtExploration(
            language_model=self.language_model,
            evaluator=self.evaluator,
            max_depth=5,
            branching_factor=3
        )
        
        # Sample problem statements
        self.problems = [
            "Solve the equation: 3x + 2 = 2x - 5",
            "A train travels at 60 km/h. How far will it travel in 2.5 hours?",
            "If a rectangle has a length of 8 meters and a width of 6 meters, what is its area?"
        ]
    
    def test_initialization(self):
        """Test proper initialization of the component."""
        self.assertEqual(self.tree_of_thought.max_depth, 5)
        self.assertEqual(self.tree_of_thought.branching_factor, 3)
        self.assertIsInstance(self.tree_of_thought.language_model, MockLanguageModel)
        self.assertIsInstance(self.tree_of_thought.evaluator, MockEvaluator)
    
    def test_explore_node(self):
        """Test exploration of a single node in the reasoning tree."""
        # Create a root node
        root = {
            "text": self.problems[0],
            "children": [],
            "score": 1.0,
            "depth": 0
        }
        
        # Explore the node
        self.tree_of_thought._explore_node(root)
        
        # Check that children were generated
        self.assertGreater(len(root["children"]), 0)
        self.assertLessEqual(len(root["children"]), self.tree_of_thought.branching_factor)
        
        # Check the structure of child nodes
        for child in root["children"]:
            self.assertIn("text", child)
            self.assertIn("children", child)
            self.assertIn("score", child)
            self.assertIn("depth", child)
            self.assertEqual(child["depth"], 1)
    
    def test_find_best_path(self):
        """Test finding the best path in a reasoning tree."""
        # Create a simple tree
        root = {
            "text": self.problems[0],
            "children": [
                {
                    "text": self.problems[0] + "\nFirst approach: Move terms with x to left side.",
                    "children": [],
                    "score": 0.8,
                    "depth": 1
                },
                {
                    "text": self.problems[0] + "\nSecond approach: Move constants to right side.",
                    "children": [],
                    "score": 0.9,  # This should be the best child
                    "depth": 1
                },
                {
                    "text": self.problems[0] + "\nThird approach: Multiply both sides by 2.",
                    "children": [],
                    "score": 0.7,
                    "depth": 1
                }
            ],
            "score": 1.0,
            "depth": 0
        }
        
        # Find the best path
        best_path = self.tree_of_thought._find_best_path(root)
        
        # Check that we got the expected path
        self.assertEqual(len(best_path), 2)  # Root + best child
        self.assertEqual(best_path[0], root["text"])
        self.assertEqual(best_path[1], root["children"][1]["text"])  # Second approach has highest score
    
    def test_explore(self):
        """Test the complete exploration of the reasoning tree."""
        problem = self.problems[0]
        best_path = self.tree_of_thought.explore(problem)
        
        # Check the structure of the result
        self.assertIsInstance(best_path, list)
        self.assertGreaterEqual(len(best_path), 1)
        self.assertEqual(best_path[0], problem)
        
        # Check that some reasoning was actually done
        full_path_text = "\n".join(best_path)
        reasoning_indicators = ["first", "next", "then", "therefore", "thus", "hence"]
        has_reasoning = any(indicator in full_path_text.lower() for indicator in reasoning_indicators)
        self.assertTrue(has_reasoning, "No evidence of reasoning in the path")
    
    def test_depth_limitation(self):
        """Test that the exploration respects the maximum depth."""
        # Set a very low max_depth to ensure it's respected
        self.tree_of_thought.max_depth = 2
        problem = self.problems[0]
        best_path = self.tree_of_thought.explore(problem)
        
        # The path should not exceed max_depth + 1 (including root)
        self.assertLessEqual(len(best_path), self.tree_of_thought.max_depth + 1)
    
    def test_node_value_calculation(self):
        """Test the calculation of node values using UCB formula."""
        # Create test parameters
        v_hat = 0.8  # Estimated value
        n_parent = 10  # Parent visit count
        n_node = 4  # Node visit count
        exploration_param = 1.0
        
        # Calculate expected value using the UCB formula
        expected_value = v_hat + exploration_param * math.sqrt(math.log(n_parent) / n_node)
        
        # Get the actual value
        actual_value = self.tree_of_thought.node_value(v_hat, n_parent, n_node)
        
        # Check that the value is calculated correctly
        self.assertAlmostEqual(actual_value, expected_value, places=6)


class TestReasoningGraphs(unittest.TestCase):
    """Test cases for the Reasoning Graphs component."""
    
    def setUp(self):
        """Set up test resources."""
        self.language_model = MockLanguageModel()
        self.reasoning_graphs = ReasoningGraphs(self.language_model)
        
        # Sample problem statements
        self.problems = [
            "Solve the equation: 3x + 2 = 2x - 5",
            "A train travels at 60 km/h. How far will it travel in 2.5 hours?",
            "If a rectangle has a length of 8 meters and a width of 6 meters, what is its area?"
        ]
    
    def test_initialization(self):
        """Test proper initialization of the component."""
        self.assertIsInstance(self.reasoning_graphs.language_model, MockLanguageModel)
    
    def test_generate_observations(self):
        """Test generation of observations from a problem statement."""
        problem = self.problems[0]
        observations = self.reasoning_graphs._generate_observations(problem)
        
        # Check that some observations were generated
        self.assertIsInstance(observations, list)
        self.assertGreater(len(observations), 0)
        
        # Check the content of observations
        for obs in observations:
            self.assertIsInstance(obs, str)
            self.assertGreater(len(obs), 0)
    
    def test_generate_hypotheses(self):
        """Test generation of hypotheses from observations."""
        observation = "The equation has variables on both sides: 3x + 2 = 2x - 5"
        hypotheses = self.reasoning_graphs._generate_hypotheses(observation)
        
        # Check that some hypotheses were generated
        self.assertIsInstance(hypotheses, list)
        self.assertGreater(len(hypotheses), 0)
        
        # Check the content of hypotheses
        for hyp in hypotheses:
            self.assertIsInstance(hyp, str)
            self.assertGreater(len(hyp), 0)
    
    def test_generate_conclusions(self):
        """Test generation of conclusions from hypotheses."""
        hypothesis = "We need to isolate x by moving all variable terms to one side"
        conclusions = self.reasoning_graphs._generate_conclusions(hypothesis)
        
        # Check that some conclusions were generated
        self.assertIsInstance(conclusions, list)
        self.assertGreater(len(conclusions), 0)
        
        # Check the content of conclusions
        for concl in conclusions:
            self.assertIsInstance(concl, str)
            self.assertGreater(len(concl), 0)
    
    def test_check_relatedness(self):
        """Test checking relatedness between statements."""
        statement1 = "We need to isolate x by moving all variable terms to one side"
        statement2 = "Subtracting 2x from both sides will isolate the variable terms on the left"
        statement3 = "The area of a rectangle is calculated as length times width"
        
        # Check that related statements have high relatedness
        relatedness_high = self.reasoning_graphs._check_relatedness(statement1, statement2)
        self.assertGreaterEqual(relatedness_high, 0.5)
        
        # Check that unrelated statements have low relatedness
        relatedness_low = self.reasoning_graphs._check_relatedness(statement1, statement3)
        self.assertLessEqual(relatedness_low, 0.5)
    
    def test_build_reasoning_graph(self):
        """Test building a complete reasoning graph from a problem statement."""
        problem = self.problems[0]
        graph = self.reasoning_graphs.build_reasoning_graph(problem)
        
        # Check the graph structure
        self.assertIn("nodes", graph)
        self.assertIn("edges", graph)
        
        # Check that there's at least one of each node type
        node_types = set()
        for node_id, node in graph["nodes"].items():
            node_types.add(node["type"])
        
        self.assertIn("problem", node_types)
        self.assertIn("observation", node_types)
        self.assertIn("hypothesis", node_types)
        self.assertIn("conclusion", node_types)
        
        # Check that edges connect existing nodes
        for edge_id, edge in graph["edges"].items():
            self.assertIn(edge["from"], graph["nodes"])
            self.assertIn(edge["to"], graph["nodes"])
    
    def test_find_support_paths(self):
        """Test finding support paths for a conclusion."""
        # Create a simple graph
        graph = {
            "nodes": {
                "0": {"text": "Problem", "type": "problem"},
                "obs_0": {"text": "Observation 0", "type": "observation"},
                "hyp_0_0": {"text": "Hypothesis 0", "type": "hypothesis"},
                "concl_hyp_0_0_0": {"text": "Conclusion 0", "type": "conclusion"}
            },
            "edges": {
                "0_obs_0": {"from": "0", "to": "obs_0", "type": "leads_to"},
                "obs_0_hyp_0_0": {"from": "obs_0", "to": "hyp_0_0", "type": "suggests"},
                "hyp_0_0_concl_hyp_0_0_0": {"from": "hyp_0_0", "to": "concl_hyp_0_0_0", "type": "implies"}
            }
        }
        
        # Find support paths for the conclusion
        paths = self.reasoning_graphs._find_support_paths(graph, "concl_hyp_0_0_0")
        
        # Check that at least one path was found
        self.assertIsInstance(paths, list)
        self.assertGreater(len(paths), 0)
        
        # Check that the path goes from the problem to the conclusion
        path = paths[0]
        self.assertEqual(path[0], "0")  # Starts with problem
        self.assertEqual(path[-1], "concl_hyp_0_0_0")  # Ends with conclusion
    
    def test_compute_path_strength(self):
        """Test computing the strength of a reasoning path."""
        # Create a simple graph with edge weights
        graph = {
            "nodes": {
                "0": {"text": "Problem", "type": "problem"},
                "1": {"text": "Intermediate", "type": "observation"},
                "2": {"text": "Conclusion", "type": "conclusion"}
            },
            "edges": {
                "0_1": {"from": "0", "to": "1", "type": "leads_to", "weight": 0.8},
                "1_2": {"from": "1", "to": "2", "type": "implies", "weight": 0.7}
            }
        }
        
        # Define a path through the graph
        path = ["0", "1", "2"]
        
        # Calculate the expected strength: product of edge weights
        expected_strength = 0.8 * 0.7
        
        # Get the actual strength
        actual_strength = self.reasoning_graphs._compute_path_strength(graph, path)
        
        # Check that the strength is calculated correctly
        self.assertAlmostEqual(actual_strength, expected_strength, places=6)
    
    def test_find_strongest_conclusion(self):
        """Test finding the strongest conclusion in a reasoning graph."""
        problem = self.problems[0]
        graph = self.reasoning_graphs.build_reasoning_graph(problem)
        
        # Find the strongest conclusion
        strongest_conclusion = self.reasoning_graphs.find_strongest_conclusion(graph)
        
        # Check that a conclusion was found
        self.assertIsInstance(strongest_conclusion, str)
        self.assertGreater(len(strongest_conclusion), 0)
        
        # Check that the conclusion mentions a solution
        solution_indicators = ["therefore", "thus", "answer", "solution", "conclude", "="]
        has_solution = any(indicator in strongest_conclusion.lower() for indicator in solution_indicators)
        self.assertTrue(has_solution, "Conclusion does not contain a solution")


class TestSelfCritiqueLoop(unittest.TestCase):
    """Test cases for the Self-Critique Loop component."""
    
    def setUp(self):
        """Set up test resources."""
        self.language_model = MockLanguageModel()
        self.self_critique = SelfCritiqueLoop(
            language_model=self.language_model,
            iterations=3
        )
        
        # Sample problems and solutions
        self.problem = "Solve the equation: 3x + 2 = 2x - 5"
        self.initial_solution = """
        To solve the equation 3x + 2 = 2x - 5:
        1. Move all terms with x to the left: 3x - 2x = -5 - 2
        2. Simplify: x = -7
        3. To verify: 3(-7) + 2 = 2(-7) - 5
        4. Left side: 3(-7) + 2 = -21 + 2 = -19
        5. Right side: 2(-7) - 5 = -14 - 5 = -19
        6. Since both sides equal -19, x = -7 is the solution.
        """
        
        self.flawed_solution = """
        To solve the equation 3x + 2 = 2x - 5:
        1. Move all terms with x to the left: 3x - 2x = -5 - 2
        2. Simplify: x = -3  # Error here, should be -7
        3. To verify: 3(-3) + 2 = 2(-3) - 5
        4. Left side: 3(-3) + 2 = -9 + 2 = -7
        5. Right side: 2(-3) - 5 = -6 - 5 = -11
        6. The verification doesn't work, but I'll ignore that.
        7. Therefore, x = -3 is the solution.
        """
    
    def test_initialization(self):
        """Test proper initialization of the component."""
        self.assertEqual(self.self_critique.iterations, 3)
        self.assertIsInstance(self.self_critique.language_model, MockLanguageModel)
    
    def test_generate_critique(self):
        """Test generating a critique for a solution."""
        critique = self.self_critique._generate_critique(self.problem, self.flawed_solution)
        
        # Check that a critique was generated
        self.assertIsInstance(critique, str)
        self.assertGreater(len(critique), 0)
    
    def test_is_substantial_critique(self):
        """Test determining if a critique is substantial."""
        # Create a substantial critique
        substantial_critique = """
        The solution contains a calculation error. In step 2, x is incorrectly calculated as -3, but it should be -7.
        This can be seen in the verification steps where the left side equals -7 and the right side equals -11,
        which are not equal. The correct solution is x = -7.
        """
        
        # Create a non-substantial critique
        non_substantial_critique = """
        The solution is well-structured and easy to follow. The reasoning is sound and the steps are clear.
        """
        
        # Check that substantial critique is identified correctly
        self.assertTrue(self.self_critique._is_substantial_critique(substantial_critique))
        
        # Check that non-substantial critique is identified correctly
        self.assertFalse(self.self_critique._is_substantial_critique(non_substantial_critique))
    
    def test_generate_improved_solution(self):
        """Test generating an improved solution based on a critique."""
        critique = """
        The solution contains a calculation error. In step 2, x is incorrectly calculated as -3, but it should be -7.
        This can be seen in the verification steps where the left side equals -7 and the right side equals -11,
        which are not equal. The correct solution is x = -7.
        """
        
        improved_solution = self.self_critique._generate_improved_solution(
            self.problem, self.flawed_solution, critique
        )
        
        # Check that an improved solution was generated
        self.assertIsInstance(improved_solution, str)
        self.assertGreater(len(improved_solution), 0)
        
        # Check that the improvement addresses the critique (x = -7 instead of x = -3)
        self.assertIn("-7", improved_solution)
    
    def test_is_improvement(self):
        """Test determining if a solution is an improvement."""
        # The improved solution should correctly state x = -7
        improved_solution = """
        To solve the equation 3x + 2 = 2x - 5:
        1. Move all terms with x to the left: 3x - 2x = -5 - 2
        2. Simplify: x = -7
        3. To verify: 3(-7) + 2 = 2(-7) - 5
        4. Left side: 3(-7) + 2 = -21 + 2 = -19
        5. Right side: 2(-7) - 5 = -14 - 5 = -19
        6. Since both sides equal -19, x = -7 is the solution.
        """
        
        # Check that the improved solution is identified correctly
        self.assertTrue(self.self_critique._is_improvement(self.flawed_solution, improved_solution))
        
        # Check that the same solution is not considered an improvement
        self.assertFalse(self.self_critique._is_improvement(self.flawed_solution, self.flawed_solution))
    
    def test_refine_reasoning(self):
        """Test the complete reasoning refinement process."""
        # Get the refined solution
        refined_solution = self.self_critique.refine_reasoning(self.problem, self.flawed_solution)
        
        # Check that a refined solution was generated
        self.assertIsInstance(refined_solution, str)
        self.assertGreater(len(refined_solution), 0)
        
        # Check that the refinement process corrected the error (x = -7)
        self.assertIn("-7", refined_solution)
        
        # Verify that the refined solution contains proper verification
        verification_indicators = ["verify", "check", "left side", "right side"]
        has_verification = any(indicator in refined_solution.lower() for indicator in verification_indicators)
        self.assertTrue(has_verification, "Refined solution does not contain proper verification")


class TestBiasDetection(unittest.TestCase):
    """Test cases for the Bias Detection component."""
    
    def setUp(self):
        """Set up test resources."""
        self.language_model = MockLanguageModel()
        self.bias_detection = BiasDetection(self.language_model)
        
        # Sample reasoning texts with different biases
        self.confirmation_biased_text = """
        I've always believed that vaccine X causes autism. Let's examine the evidence:
        1. Many parents report that their children developed autism after receiving vaccine X.
        2. There's a study that found a correlation between vaccine X and autism rates.
        3. I've read several articles that suggest a link between the two.
        Therefore, vaccine X definitely causes autism.
        """
        
        self.availability_biased_text = """
        Shark attacks are becoming more and more common. Just last month, there was a deadly
        shark attack that was all over the news. It's now extremely dangerous to swim in the
        ocean, and the risk of being attacked by a shark is very high.
        """
        
        self.unbiased_text = """
        To determine if there is a link between vaccine X and autism, we need to examine:
        1. Controlled scientific studies with large sample sizes
        2. Meta-analyses of multiple studies
        3. Potential confounding variables
        
        The scientific consensus based on multiple large-scale studies is that there is no causal 
        relationship between vaccine X and autism. While individual anecdotes exist, they don't 
        constitute reliable statistical evidence.
        """
    
    def test_initialization(self):
        """Test proper initialization of the component."""
        self.assertIsInstance(self.bias_detection.language_model, MockLanguageModel)
        self.assertIsInstance(self.bias_detection.bias_types, list)
        self.assertGreater(len(self.bias_detection.bias_types), 0)
    
    def test_evaluate_bias(self):
        """Test evaluating a specific bias in reasoning."""
        # Test confirmation bias in biased text
        confirmation_bias_score = self.bias_detection._evaluate_bias(
            self.confirmation_biased_text, "Confirmation bias"
        )
        
        # Test confirmation bias in unbiased text
        confirmation_bias_score_unbiased = self.bias_detection._evaluate_bias(
            self.unbiased_text, "Confirmation bias"
        )
        
        # Check that biased text gets a higher score
        self.assertGreater(confirmation_bias_score, 0.5)
        self.assertLess(confirmation_bias_score_unbiased, 0.5)
    
    def test_detect_biases(self):
        """Test detecting all biases in reasoning."""
        # Detect biases in biased text
        detected_biases = self.bias_detection.detect_biases(self.confirmation_biased_text)
        
        # Check that some biases were detected
        self.assertIsInstance(detected_biases, list)
        
        # Check that the biases are returned with scores
        for bias_type, score in detected_biases:
            self.assertIsInstance(bias_type, str)
            self.assertIsInstance(score, float)
            self.assertTrue(0 <= score <= 1)
        
        # Check that confirmation bias is detected with a high score
        has_confirmation_bias = False
        for bias_type, score in detected_biases:
            if "confirmation" in bias_type.lower() and score > 0.7:
                has_confirmation_bias = True
                break
        
        self.assertTrue(has_confirmation_bias, "Confirmation bias not detected properly")
    
    def test_correct_biased_reasoning(self):
        """Test correcting biased reasoning."""
        # First detect biases
        detected_biases = self.bias_detection.detect_biases(self.confirmation_biased_text)
        
        # Only test correction if biases were detected
        if detected_biases:
            corrected_reasoning = self.bias_detection.correct_biased_reasoning(
                self.confirmation_biased_text, detected_biases
            )
            
            # Check that corrected reasoning was generated
            self.assertIsInstance(corrected_reasoning, str)
            self.assertGreater(len(corrected_reasoning), 0)
            
            # Check that the corrected reasoning addresses confirmation bias
            debiasing_indicators = [
                "multiple perspectives", "consider alternative", "scientific evidence",
                "statistical significance", "controlled studies"
            ]
            has_debiasing = any(indicator in corrected_reasoning.lower() for indicator in debiasing_indicators)
            self.assertTrue(has_debiasing, "Corrected reasoning does not address confirmation bias")
    
    def test_bias_detection_unbiased_text(self):
        """Test that unbiased text is correctly identified as such."""
        # Detect biases in unbiased text
        detected_biases = self.bias_detection.detect_biases(self.unbiased_text)
        
        # Check if any high-score biases were detected
        high_score_biases = [bias for bias, score in detected_biases if score > 0.7]
        
        # Unbiased text should not have high-score biases
        self.assertEqual(len(high_score_biases), 0, "Unbiased text incorrectly flagged with high bias scores")


class TestMetaLearningController(unittest.TestCase):
    """Test cases for the Meta-Learning Controller component."""
    
    def setUp(self):
        """Set up test resources."""
        # Create mock reasoning modules
        self.chain_of_thought = MultiPathChainOfThought(MockLanguageModel(), max_paths=3, beam_width=2)
        self.tree_of_thought = TreeOfThoughtExploration(
            MockLanguageModel(), MockEvaluator(), max_depth=3, branching_factor=2
        )
        self.reasoning_graphs = ReasoningGraphs(MockLanguageModel())
        
        # Create reasoning modules dictionary
        self.reasoning_modules = {
            "chain_of_thought": self.chain_of_thought,
            "tree_of_thought": self.tree_of_thought,
            "reasoning_graphs": self.reasoning_graphs
        }
        
        # Create the meta-learning controller
        self.meta_learning = MetaLearningController(self.reasoning_modules)
        
        # Add a mock language model
        self.meta_learning.language_model = MockLanguageModel()
        
        # Sample problems of different types
        self.problems = {
            "math": "Solve the equation: 3x + 2 = 2x - 5",
            "logic": "If all A are B, and some B are C, what can we conclude about A and C?",
            "commonsense": "It's raining outside and Sarah doesn't want to get wet. What should she do?",
            "factual": "What is the capital of France?",
            "creative": "Describe a world where humans have evolved wings."
        }
    
    def test_initialization(self):
        """Test proper initialization of the component."""
        self.assertIsInstance(self.meta_learning.reasoning_modules, dict)
        self.assertEqual(len(self.meta_learning.reasoning_modules), 3)
        self.assertIsInstance(self.meta_learning.problem_history, list)
    
    def test_extract_problem_features(self):
        """Test extracting features from a problem statement."""
        problem = self.problems["math"]
        features = self.meta_learning._extract_problem_features(problem)
        
        # Check that features were extracted
        self.assertIsInstance(features, dict)
        self.assertIn("length", features)
        self.assertIn("domain", features)
        self.assertIn("complexity", features)
        
        # Check feature types
        self.assertIsInstance(features["length"], int)
        self.assertIsInstance(features["domain"], str)
        self.assertIsInstance(features["complexity"], int)
    
    def test_classify_domain(self):
        """Test classifying the domain of a problem."""
        # Test math problem
        math_domain = self.meta_learning._classify_domain(self.problems["math"])
        self.assertEqual(math_domain, "math")
        
        # Test logic problem
        logic_domain = self.meta_learning._classify_domain(self.problems["logic"])
        self.assertEqual(logic_domain, "logic")
    
    def test_estimate_complexity(self):
        """Test estimating problem complexity."""
        # Test simple problem
        simple_problem = "What is 2 + 2?"
        simple_complexity = self.meta_learning._estimate_complexity(simple_problem)
        
        # Test complex problem
        complex_problem = """
        A car travels from point A to point B at an average speed of 60 km/h and then
        returns from B to A along the same route at an average speed of 40 km/h.
        What is the average speed for the entire journey?
        """
        complex_complexity = self.meta_learning._estimate_complexity(complex_problem)
        
        # Check that complex problem gets higher complexity
        self.assertGreater(complex_complexity, simple_complexity)
    
    def test_compute_similarity(self):
        """Test computing similarity between problem features."""
        features1 = {
            "domain": "math",
            "length": 20,
            "complexity": 5
        }
        
        features2 = {
            "domain": "math",
            "length": 25,
            "complexity": 6
        }
        
        features3 = {
            "domain": "logic",
            "length": 20,
            "complexity": 5
        }
        
        # Check that similar features have high similarity
        similarity_high = self.meta_learning._compute_similarity(features1, features2)
        self.assertGreater(similarity_high, 0.7)
        
        # Check that different domains have lower similarity
        similarity_low = self.meta_learning._compute_similarity(features1, features3)
        self.assertLess(similarity_low, similarity_high)
    
    def test_find_similar_problems(self):
        """Test finding similar problems in history."""
        # Add some problems to history
        self.meta_learning.problem_history = [
            {
                "problem": self.problems["math"],
                "features": {
                    "domain": "math",
                    "length": 20,
                    "complexity": 5
                },
                "strategy": "chain_of_thought"
            },
            {
                "problem": self.problems["logic"],
                "features": {
                    "domain": "logic",
                    "length": 25,
                    "complexity": 6
                },
                "strategy": "tree_of_thought"
            }
        ]
        
        # Find similar problems for a math problem
        similar_problems = self.meta_learning._find_similar_problems(
            {"domain": "math", "length": 22, "complexity": 5}
        )
        
        # Check that similar problems were found
        self.assertIsInstance(similar_problems, list)
        self.assertGreater(len(similar_problems), 0)
        
        # Check that the most similar problem is the math problem
        most_similar = similar_problems[0][0]
        self.assertEqual(most_similar["problem"], self.problems["math"])
    
    def test_get_best_strategy(self):
        """Test getting the best strategy for similar problems."""
        # Create similar problems with different strategies
        similar_problems = [
            ({"problem": "Problem 1", "strategy": "chain_of_thought"}, 0.9),
            ({"problem": "Problem 2", "strategy": "tree_of_thought"}, 0.8),
            ({"problem": "Problem 3", "strategy": "chain_of_thought"}, 0.7)
        ]
        
        # Get best strategy
        best_strategy = self.meta_learning._get_best_strategy(similar_problems)
        
        # Check that the best strategy is the one with highest combined similarity
        self.assertEqual(best_strategy, "chain_of_thought")
    
    def test_evaluate_result(self):
        """Test evaluating the quality of a reasoning result."""
        problem = self.problems["math"]
        result = """
        To solve the equation 3x + 2 = 2x - 5:
        1. Move all terms with x to the left: 3x - 2x = -5 - 2
        2. Simplify: x = -7
        3. To verify: 3(-7) + 2 = 2(-7) - 5
        4. Left side: 3(-7) + 2 = -21 + 2 = -19
        5. Right side: 2(-7) - 5 = -14 - 5 = -19
        6. Since both sides equal -19, x = -7 is the solution.
        """
        
        # Evaluate result
        score = self.meta_learning._evaluate_result(problem, result)
        
        # Check that the score is reasonable
        self.assertIsInstance(score, float)
        self.assertTrue(0 <= score <= 1)
        self.assertGreater(score, 0.5, "Good solution should have high score")
    
    def test_select_reasoning_strategy(self):
        """Test selecting the best reasoning strategy for a problem."""
        problem = self.problems["math"]
        
        # Test strategy selection
        strategy = self.meta_learning.select_reasoning_strategy(problem)
        
        # Check that a valid strategy was selected
        self.assertIsInstance(strategy, MultiPathChainOfThought, TreeOfThoughtExploration, ReasoningGraphs)
        
        # Check that the problem was added to history
        self.assertGreater(len(self.meta_learning.problem_history), 0)
        latest_problem = self.meta_learning.problem_history[-1]
        self.assertEqual(latest_problem["problem"], problem)
        self.assertIn("features", latest_problem)
        self.assertIn("strategy", latest_problem)


if __name__ == "__main__":
    unittest.main()