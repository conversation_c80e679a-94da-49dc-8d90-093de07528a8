#!/usr/bin/env python3
"""
Test initialization module for ULTRA Output Generation System.

This module contains comprehensive tests for the Output Generation components:
- Text Output Generation
- Visual Output Generation
- Action Output Generation
- Multimodal Synthesis

The tests validate core functionality, mathematical correctness, and integration
capabilities of the Output Generation System within the ULTRA framework.
"""

import os
import sys
import unittest
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Tuple, Any, Optional, Union, Callable, Set
import pytest
from collections import defaultdict, deque
import math
import random
import inspect
import json
import time
import matplotlib
matplotlib.use('Agg')  # Non-interactive backend for testing
import matplotlib.pyplot as plt
from PIL import Image
import io
import base64
import networkx as nx
import re
import nltk
from nltk.tokenize import sent_tokenize, word_tokenize
from nltk.translate.bleu_score import sentence_bleu, corpus_bleu, SmoothingFunction
from nltk.translate.meteor_score import meteor_score
from rouge import Rouge

# Ensure NLTK resources are available
try:
    nltk.data.find('tokenizers/punkt')
except LookupError:
    nltk.download('punkt')

# Add the parent directory to the path so we can import from the ultra package
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../../')))

# Import the Output Generation components
from ultra.output_generation.text_output import TextOutputGenerator
from ultra.output_generation.visual_output import VisualOutputGenerator
from ultra.output_generation.action_output import ActionOutputGenerator
from ultra.output_generation.multimodal_synthesis import MultimodalOutputSynthesizer

# Import necessary utilities
from ultra.utils.config import Config
from ultra.utils.ultra_logging import get_logger
from ultra.utils.metrics import evaluate_output_quality

# Import components needed for integration testing
from ultra.meta_cognitive.reasoning_graphs import ReasoningGraphs
from ultra.knowledge_management.semantic_knowledge import SemanticKnowledgeBase
from ultra.knowledge_management.episodic_knowledge import EpisodicKnowledgeBase
from ultra.diffusion_reasoning.thought_latent_space import ThoughtLatentSpace

# Set up logger
logger = get_logger(__name__)


class MockReasoningOutput:
    """
    Mock class for simulating reasoning outputs to test the output generation system.
    Provides standardized formats for different reasoning types.
    """
    def __init__(self, reasoning_type="chain_of_thought"):
        """
        Initialize with a specific reasoning type.
        
        Args:
            reasoning_type: Type of reasoning output to simulate (chain_of_thought, 
                           tree_of_thought, reasoning_graph, etc.)
        """
        self.reasoning_type = reasoning_type
        
        # Generate appropriate mock data based on reasoning type
        if reasoning_type == "chain_of_thought":
            self.data = self._generate_chain_of_thought()
        elif reasoning_type == "tree_of_thought":
            self.data = self._generate_tree_of_thought()
        elif reasoning_type == "reasoning_graph":
            self.data = self._generate_reasoning_graph()
        elif reasoning_type == "diffusion_reasoning":
            self.data = self._generate_diffusion_reasoning()
        else:
            self.data = self._generate_generic_reasoning()
    
    def _generate_chain_of_thought(self):
        """Generate a mock chain of thought reasoning path."""
        return {
            "problem": "Solve the equation: 3x + 2 = 2x - 5",
            "path": [
                "First, I'll move all terms with x to the left side of the equation.",
                "3x - 2x = -5 - 2",
                "x = -7",
                "To verify, I'll substitute x = -7 back into the original equation.",
                "3(-7) + 2 = 2(-7) - 5",
                "-21 + 2 = -14 - 5",
                "-19 = -19",
                "The equation is verified, so x = -7 is the solution."
            ],
            "confidence": 0.92,
            "solution": "x = -7"
        }
    
    def _generate_tree_of_thought(self):
        """Generate a mock tree of thought reasoning structure."""
        return {
            "problem": "What is the value of 5! (5 factorial)?",
            "root": "Calculate 5!",
            "nodes": {
                "n0": {"text": "Calculate 5!", "children": ["n1", "n2"], "depth": 0},
                "n1": {"text": "Use the formula: n! = n × (n-1)!", "children": ["n3"], "depth": 1},
                "n2": {"text": "Enumerate: 5! = 5 × 4 × 3 × 2 × 1", "children": ["n4"], "depth": 1},
                "n3": {"text": "5! = 5 × 4!\n4! = 4 × 3!\n3! = 3 × 2!\n2! = 2 × 1!\n1! = 1", "children": ["n5"], "depth": 2},
                "n4": {"text": "5! = 5 × 4 × 3 × 2 × 1 = 120", "children": [], "depth": 2},
                "n5": {"text": "5! = 5 × 4! = 5 × 24 = 120", "children": [], "depth": 3}
            },
            "best_path": ["n0", "n2", "n4"],
            "confidence": 0.95,
            "solution": "120"
        }
    
    def _generate_reasoning_graph(self):
        """Generate a mock reasoning graph structure."""
        return {
            "problem": "Is a square a rectangle?",
            "nodes": {
                "p0": {"text": "Problem: Is a square a rectangle?", "type": "problem"},
                "obs1": {"text": "A square has 4 sides of equal length.", "type": "observation"},
                "obs2": {"text": "A square has 4 angles of 90 degrees.", "type": "observation"},
                "obs3": {"text": "A rectangle has 4 sides with opposite sides of equal length.", "type": "observation"},
                "obs4": {"text": "A rectangle has 4 angles of 90 degrees.", "type": "observation"},
                "hyp1": {"text": "A square satisfies all properties of a rectangle.", "type": "hypothesis"},
                "concl1": {"text": "A square is a special case of a rectangle.", "type": "conclusion"},
                "concl2": {"text": "Yes, a square is a rectangle.", "type": "conclusion"}
            },
            "edges": {
                "e1": {"from": "p0", "to": "obs1", "type": "generates"},
                "e2": {"from": "p0", "to": "obs2", "type": "generates"},
                "e3": {"from": "p0", "to": "obs3", "type": "generates"},
                "e4": {"from": "p0", "to": "obs4", "type": "generates"},
                "e5": {"from": "obs1", "to": "hyp1", "type": "supports"},
                "e6": {"from": "obs2", "to": "hyp1", "type": "supports"},
                "e7": {"from": "obs3", "to": "hyp1", "type": "supports"},
                "e8": {"from": "obs4", "to": "hyp1", "type": "supports"},
                "e9": {"from": "hyp1", "to": "concl1", "type": "implies"},
                "e10": {"from": "concl1", "to": "concl2", "type": "implies"}
            },
            "strongest_conclusion": "concl2",
            "confidence": 0.98,
            "solution": "Yes, a square is a rectangle."
        }
    
    def _generate_diffusion_reasoning(self):
        """Generate a mock diffusion reasoning process."""
        return {
            "problem": "How might we reduce urban traffic congestion?",
            "initial_concept": "Traffic congestion reduction",
            "diffusion_trajectory": [
                {"step": 0, "concept": "Traffic congestion reduction", "noise_level": 0.0},
                {"step": 5, "concept": "Urban planning solutions", "noise_level": 0.2},
                {"step": 10, "concept": "Public transportation enhancement", "noise_level": 0.4},
                {"step": 15, "concept": "Remote work policies", "noise_level": 0.6},
                {"step": 20, "concept": "Incentivize carpooling", "noise_level": 0.8},
                {"step": 25, "concept": "Congestion pricing", "noise_level": 1.0}
            ],
            "reverse_diffusion": [
                {"step": 0, "concept": "Congestion pricing", "guidance": "Cost-effective"},
                {"step": 5, "concept": "Congestion pricing in city centers", "guidance": "Implementable"},
                {"step": 10, "concept": "Time-based congestion pricing", "guidance": "Flexible"},
                {"step": 15, "concept": "Dynamic congestion pricing with mobile app integration", "guidance": "Modern"},
                {"step": 20, "concept": "AI-optimized dynamic congestion pricing system", "guidance": "Efficient"}
            ],
            "uncertainty": {
                "epistemic": 0.15,
                "aleatoric": 0.22,
                "decision": 0.18
            },
            "solution": "Implement AI-optimized dynamic congestion pricing system with mobile app integration",
            "confidence": 0.87
        }
    
    def _generate_generic_reasoning(self):
        """Generate a generic reasoning structure for testing."""
        return {
            "problem": "What happens when you mix baking soda and vinegar?",
            "reasoning": [
                "Baking soda is sodium bicarbonate (NaHCO3), a base.",
                "Vinegar contains acetic acid (CH3COOH), an acid.",
                "When acids and bases mix, they undergo a neutralization reaction.",
                "The reaction produces sodium acetate, water, and carbon dioxide.",
                "The chemical equation is: NaHCO3 + CH3COOH → CH3COONa + H2O + CO2",
                "The carbon dioxide forms bubbles, creating a fizzing effect."
            ],
            "solution": "Mixing baking soda and vinegar creates a chemical reaction that produces sodium acetate, water, and carbon dioxide gas, which causes visible fizzing.",
            "confidence": 0.94
        }
    
    def get_data(self):
        """Return the generated reasoning data."""
        return self.data


class MockKnowledgeBase:
    """
    Mock knowledge base for testing output generation.
    Provides simulated facts, concepts, and relationships.
    """
    def __init__(self):
        """Initialize the mock knowledge base with sample data."""
        self.facts = {
            "science": {
                "chemistry": [
                    "Water is composed of hydrogen and oxygen (H2O).",
                    "The periodic table has 118 confirmed elements.",
                    "Acids donate protons in chemical reactions.",
                    "The pH scale ranges from 0 to 14."
                ],
                "physics": [
                    "Newton's three laws describe motion and forces.",
                    "E = mc² is Einstein's mass-energy equivalence formula.",
                    "Light behaves both as a wave and a particle.",
                    "The universe is expanding at an accelerating rate."
                ],
                "biology": [
                    "DNA contains genetic instructions for organisms.",
                    "Cells are the basic structural units of living organisms.",
                    "Evolution occurs through natural selection.",
                    "The human body has 11 major organ systems."
                ]
            },
            "math": {
                "algebra": [
                    "Quadratic equations can be solved using the quadratic formula.",
                    "Linear equations have variables with power 1.",
                    "Systems of equations can be solved through substitution or elimination.",
                    "Inequalities show the relationship between expressions."
                ],
                "geometry": [
                    "A circle has 360 degrees.",
                    "The Pythagorean theorem relates the sides of a right triangle.",
                    "Similar triangles have proportional sides and equal angles.",
                    "Pi (π) is the ratio of a circle's circumference to its diameter."
                ],
                "calculus": [
                    "Derivatives measure the rate of change of a function.",
                    "Integrals calculate the area under a curve.",
                    "The fundamental theorem of calculus connects derivatives and integrals.",
                    "Limits describe behavior as a variable approaches a value."
                ]
            }
        }
        
        self.concepts = {
            "energy": {
                "definition": "The capacity to do work or produce heat.",
                "types": ["Kinetic", "Potential", "Thermal", "Chemical", "Nuclear"],
                "related_concepts": ["Force", "Work", "Power", "Conservation of Energy"],
                "importance": 0.95
            },
            "democracy": {
                "definition": "A system of government by the whole population or eligible members.",
                "types": ["Direct", "Representative", "Constitutional", "Parliamentary"],
                "related_concepts": ["Voting", "Citizenship", "Rights", "Governance"],
                "importance": 0.92
            },
            "algorithm": {
                "definition": "A process or set of rules to be followed in calculations or problem-solving.",
                "types": ["Sorting", "Searching", "Dynamic Programming", "Greedy"],
                "related_concepts": ["Computation", "Efficiency", "Data Structure", "Optimization"],
                "importance": 0.90
            }
        }
        
        self.relations = [
            {"subject": "Carbon", "predicate": "is_element_of", "object": "Periodic Table", "confidence": 0.99},
            {"subject": "Photosynthesis", "predicate": "converts", "object": "Light Energy", "confidence": 0.98},
            {"subject": "Democracy", "predicate": "requires", "object": "Voting Rights", "confidence": 0.97},
            {"subject": "Algorithm", "predicate": "solves", "object": "Computational Problem", "confidence": 0.95},
            {"subject": "Derivative", "predicate": "measures", "object": "Rate of Change", "confidence": 0.96}
        ]
    
    def get_facts(self, domain, subdomain=None):
        """
        Retrieve facts from the knowledge base.
        
        Args:
            domain: The domain to retrieve facts from
            subdomain: Optional subdomain to narrow the search
            
        Returns:
            List of facts
        """
        if domain in self.facts:
            if subdomain and subdomain in self.facts[domain]:
                return self.facts[domain][subdomain]
            elif not subdomain:
                all_facts = []
                for sub in self.facts[domain]:
                    all_facts.extend(self.facts[domain][sub])
                return all_facts
        return []
    
    def get_concept(self, concept_name):
        """
        Retrieve a concept from the knowledge base.
        
        Args:
            concept_name: Name of the concept to retrieve
            
        Returns:
            Concept data or None if not found
        """
        return self.concepts.get(concept_name.lower(), None)
    
    def get_relations(self, entity=None, relation_type=None):
        """
        Retrieve relations from the knowledge base.
        
        Args:
            entity: Optional entity to filter relations
            relation_type: Optional relation type to filter by
            
        Returns:
            List of matching relations
        """
        if not entity and not relation_type:
            return self.relations
        
        filtered = []
        for relation in self.relations:
            if entity and (relation["subject"] == entity or relation["object"] == entity):
                filtered.append(relation)
            elif relation_type and relation["predicate"] == relation_type:
                filtered.append(relation)
        
        return filtered


class MockLanguageModel:
    """
    Mock language model for testing text generation capabilities.
    """
    def __init__(self, seed=42):
        """
        Initialize the mock language model.
        
        Args:
            seed: Random seed for reproducibility
        """
        random.seed(seed)
        
        # Register style templates
        self.styles = {
            "concise": {
                "length_factor": 0.6,
                "formality": "low",
                "complexity": "low",
                "paragraph_length": "short"
            },
            "detailed": {
                "length_factor": 1.4,
                "formality": "high",
                "complexity": "high",
                "paragraph_length": "long"
            },
            "technical": {
                "length_factor": 1.2,
                "formality": "high",
                "complexity": "high",
                "terminology": "specialized",
                "paragraph_length": "medium"
            },
            "conversational": {
                "length_factor": 0.9,
                "formality": "low",
                "complexity": "low",
                "paragraph_length": "short",
                "include_questions": True
            },
            "narrative": {
                "length_factor": 1.3,
                "formality": "medium",
                "complexity": "medium",
                "paragraph_length": "long",
                "storytelling": True
            }
        }
        
        # Template responses for different domains
        self.domain_templates = {
            "science": [
                "The scientific evidence suggests that {concept} is a critical aspect of {field}.",
                "Research in {field} has demonstrated that {concept} plays a key role in {related_concept}.",
                "Experimental results indicate that {concept} directly affects {related_concept} under specific conditions.",
                "The current scientific consensus on {concept} is that it {action} through {mechanism}."
            ],
            "math": [
                "The mathematical definition of {concept} involves {related_concept} under the constraints of {conditions}.",
                "We can solve this {problem_type} by applying the {method}, which gives us {solution}.",
                "The {theorem} states that {statement}, which can be proven by {proof_technique}.",
                "For any {mathematical_object}, the {property} is determined by {relation}."
            ],
            "explanation": [
                "To understand {concept}, we need to first examine {prerequisite_concept}.",
                "{concept} can be explained as {definition}, which means that {implication}.",
                "The process of {concept} works by {step1}, followed by {step2}, and finally {step3}.",
                "When we talk about {concept}, we're referring to {definition} in the context of {field}."
            ]
        }
    
    def generate(self, prompt, max_tokens=100, temperature=0.7, style=None):
        """
        Generate text based on a prompt.
        
        Args:
            prompt: The input prompt
            max_tokens: Maximum number of tokens to generate
            temperature: Sampling temperature (higher = more random)
            style: Optional style name to use
            
        Returns:
            Generated text
        """
        # Determine domain based on prompt content
        domain = "explanation"  # Default
        if any(term in prompt.lower() for term in ["chemistry", "physics", "biology", "molecule", "atom", "cell"]):
            domain = "science"
        elif any(term in prompt.lower() for term in ["equation", "theorem", "calculate", "solve", "formula"]):
            domain = "math"
        
        # Extract potential concept terms from the prompt
        words = re.findall(r'\b[A-Za-z]{4,}\b', prompt)
        concepts = [word for word in words if word.lower() not in ["what", "explain", "describe", "about", "please"]]
        
        # Select template based on domain
        templates = self.domain_templates.get(domain, self.domain_templates["explanation"])
        template = random.choice(templates)
        
        # Apply style modifications if specified
        length_factor = 1.0
        if style and style in self.styles:
            length_factor = self.styles[style]["length_factor"]
            
            # Add style-specific modifications
            if self.styles[style].get("include_questions", False):
                template += " What do you think about this explanation?"
                
            if self.styles[style].get("storytelling", False):
                template = "Let me tell you a story about " + template
        
        # Fill in template placeholders with concepts from the prompt
        for placeholder in re.findall(r'\{([^}]+)\}', template):
            if concepts:
                concept = random.choice(concepts)
                template = template.replace("{" + placeholder + "}", concept)
            else:
                template = template.replace("{" + placeholder + "}", "this concept")
        
        # Generate additional sentences based on max_tokens and length_factor
        target_length = int(max_tokens * length_factor)
        current_length = len(template.split())
        
        while current_length < target_length:
            # Add another sentence
            new_template = random.choice(templates)
            for placeholder in re.findall(r'\{([^}]+)\}', new_template):
                if concepts:
                    concept = random.choice(concepts)
                    new_template = new_template.replace("{" + placeholder + "}", concept)
                else:
                    new_template = new_template.replace("{" + placeholder + "}", "this concept")
            
            template += " " + new_template
            current_length = len(template.split())
        
        return template


class TestTextOutputGenerator(unittest.TestCase):
    """Test cases for the Text Output Generator component."""
    
    def setUp(self):
        """Set up test resources for each test."""
        # Create required mock objects
        self.language_model = MockLanguageModel()
        self.knowledge_base = MockKnowledgeBase()
        
        # Create the text output generator
        self.text_generator = TextOutputGenerator(
            language_model=self.language_model,
            knowledge_base=self.knowledge_base
        )
        
        # Sample reasoning outputs for testing
        self.chain_of_thought = MockReasoningOutput("chain_of_thought").get_data()
        self.reasoning_graph = MockReasoningOutput("reasoning_graph").get_data()
        
        # Sample generation parameters
        self.default_params = {
            "max_length": 500,
            "style": "concise",
            "include_reasoning": True,
            "include_confidence": True,
            "format": "text"
        }
    
    def test_initialization(self):
        """Test proper initialization of the component."""
        self.assertIsInstance(self.text_generator.language_model, MockLanguageModel)
        self.assertIsInstance(self.text_generator.knowledge_base, MockKnowledgeBase)
        self.assertTrue(hasattr(self.text_generator, "generate_text"))
        self.assertTrue(hasattr(self.text_generator, "format_reasoning"))
        self.assertTrue(hasattr(self.text_generator, "evaluate_quality"))
    
    def test_generate_from_chain_of_thought(self):
        """Test generating text from chain of thought reasoning."""
        output = self.text_generator.generate_text(
            reasoning=self.chain_of_thought,
            params=self.default_params
        )
        
        # Check that output was generated
        self.assertIsInstance(output, str)
        self.assertGreater(len(output), 0)
        
        # Check that the solution is included in the output
        self.assertIn(self.chain_of_thought["solution"], output)
        
        # Check that confidence is included if requested
        if self.default_params["include_confidence"]:
            confidence_pattern = r"\b(confidence|certainty|confidence level)\b.*\b(0\.\d+|\d+%)"
            self.assertTrue(re.search(confidence_pattern, output, re.IGNORECASE))
    
    def test_generate_from_reasoning_graph(self):
        """Test generating text from reasoning graph."""
        output = self.text_generator.generate_text(
            reasoning=self.reasoning_graph,
            params=self.default_params
        )
        
        # Check that output was generated
        self.assertIsInstance(output, str)
        self.assertGreater(len(output), 0)
        
        # Check that the solution is included in the output
        self.assertIn(self.reasoning_graph["solution"], output)
    
    def test_style_customization(self):
        """Test that output style can be customized."""
        # Test with concise style
        concise_params = self.default_params.copy()
        concise_params["style"] = "concise"
        concise_output = self.text_generator.generate_text(
            reasoning=self.chain_of_thought,
            params=concise_params
        )
        
        # Test with detailed style
        detailed_params = self.default_params.copy()
        detailed_params["style"] = "detailed"
        detailed_output = self.text_generator.generate_text(
            reasoning=self.chain_of_thought,
            params=detailed_params
        )
        
        # The detailed output should be longer than the concise output
        self.assertGreater(len(detailed_output), len(concise_output))
    
    def test_format_reasoning_path(self):
        """Test formatting a reasoning path into a readable format."""
        formatted = self.text_generator.format_reasoning(self.chain_of_thought)
        
        # Check that formatting was done
        self.assertIsInstance(formatted, str)
        self.assertGreater(len(formatted), 0)
        
        # Check that steps are separated in some way
        steps = formatted.split("\n")
        self.assertGreater(len(steps), 1)
    
    def test_markdown_formatting(self):
        """Test generating text with markdown formatting."""
        markdown_params = self.default_params.copy()
        markdown_params["format"] = "markdown"
        
        output = self.text_generator.generate_text(
            reasoning=self.chain_of_thought,
            params=markdown_params
        )
        
        # Check for markdown elements
        markdown_elements = ["#", "##", "**", "*", "`", "```"]
        has_markdown = any(element in output for element in markdown_elements)
        self.assertTrue(has_markdown, "Output doesn't contain markdown formatting")
    
    def test_html_formatting(self):
        """Test generating text with HTML formatting."""
        html_params = self.default_params.copy()
        html_params["format"] = "html"
        
        output = self.text_generator.generate_text(
            reasoning=self.chain_of_thought,
            params=html_params
        )
        
        # Check for HTML elements
        html_elements = ["<h1>", "<h2>", "<p>", "<div>", "<span>", "<strong>", "<em>"]
        has_html = any(element in output for element in html_elements)
        self.assertTrue(has_html, "Output doesn't contain HTML formatting")
    
    def test_json_formatting(self):
        """Test generating text with JSON formatting."""
        json_params = self.default_params.copy()
        json_params["format"] = "json"
        
        output = self.text_generator.generate_text(
            reasoning=self.chain_of_thought,
            params=json_params
        )
        
        # Check that the output is valid JSON
        try:
            json_data = json.loads(output)
            self.assertIsInstance(json_data, dict)
            self.assertIn("solution", json_data)
            self.assertIn("reasoning", json_data)
        except json.JSONDecodeError:
            self.fail("Output is not valid JSON")
    
    def test_response_with_knowledge_integration(self):
        """Test that relevant knowledge is integrated into the response."""
        # Create reasoning data about chemistry
        chemistry_reasoning = {
            "problem": "What happens in an acid-base neutralization reaction?",
            "reasoning": [
                "Acids donate protons (H+) in solution.",
                "Bases accept protons or donate hydroxide ions (OH-).",
                "When an acid and base react, they form water and a salt.",
                "The general equation is: acid + base → salt + water",
                "This type of reaction is called neutralization."
            ],
            "solution": "In an acid-base neutralization reaction, an acid and a base react to form a salt and water, with the acid donating protons and the base accepting them.",
            "confidence": 0.95
        }
        
        # Generate text with knowledge integration
        output = self.text_generator.generate_text(
            reasoning=chemistry_reasoning,
            params={"enhance_with_knowledge": True}
        )
        
        # Get relevant facts from knowledge base
        chemistry_facts = self.knowledge_base.get_facts("science", "chemistry")
        
        # Check if any relevant knowledge was integrated
        knowledge_integrated = any(
            fact.lower() in output.lower() for fact in chemistry_facts
        )
        
        self.assertTrue(knowledge_integrated, "No knowledge integration detected in output")
    
    def test_evaluate_quality(self):
        """Test evaluation of output quality."""
        sample_output = "The solution to the equation 3x + 2 = 2x - 5 is x = -7. This was found by moving all terms with x to the left side, resulting in x = -7."
        
        metrics = self.text_generator.evaluate_quality(
            reference=self.chain_of_thought["solution"],
            generated=sample_output
        )
        
        # Check that metrics were calculated
        self.assertIsInstance(metrics, dict)
        expected_metrics = ["relevance", "clarity", "completeness", "correctness"]
        for metric in expected_metrics:
            self.assertIn(metric, metrics)
            self.assertIsInstance(metrics[metric], float)
            self.assertTrue(0 <= metrics[metric] <= 1)
    
    def test_language_adaptation(self):
        """Test adaptation to different languages."""
        # Set language parameter
        language_params = self.default_params.copy()
        language_params["language"] = "Spanish"
        
        # Mock the language model's ability to generate in different languages
        original_generate = self.language_model.generate
        def mock_generate(prompt, max_tokens=100, temperature=0.7, style=None):
            if language_params["language"] == "Spanish":
                return "La solución de la ecuación 3x + 2 = 2x - 5 es x = -7."
            else:
                return original_generate(prompt, max_tokens, temperature, style)
        
        self.language_model.generate = mock_generate
        
        # Generate text in Spanish
        output = self.text_generator.generate_text(
            reasoning=self.chain_of_thought,
            params=language_params
        )
        
        # Check that output is in Spanish
        self.assertIn("solución", output)
        self.assertIn("ecuación", output)
        
        # Restore original method
        self.language_model.generate = original_generate
    
    def test_audience_adaptation(self):
        """Test adaptation to different audience levels."""
        # Test for technical audience
        technical_params = self.default_params.copy()
        technical_params["audience"] = "technical"
        
        technical_output = self.text_generator.generate_text(
            reasoning=self.chain_of_thought,
            params=technical_params
        )
        
        # Test for general audience
        general_params = self.default_params.copy()
        general_params["audience"] = "general"
        
        general_output = self.text_generator.generate_text(
            reasoning=self.chain_of_thought,
            params=general_params
        )
        
        # Technical output should be different from general output
        self.assertNotEqual(technical_output, general_output)
    
    def test_error_handling(self):
        """Test handling of invalid inputs."""
        # Test with None reasoning
        with self.assertRaises(ValueError):
            self.text_generator.generate_text(reasoning=None)
        
        # Test with invalid format
        invalid_params = self.default_params.copy()
        invalid_params["format"] = "invalid_format"
        
        with self.assertRaises(ValueError):
            self.text_generator.generate_text(
                reasoning=self.chain_of_thought,
                params=invalid_params
            )


class TestVisualOutputGenerator(unittest.TestCase):
    """Test cases for the Visual Output Generator component."""
    
    def setUp(self):
        """Set up test resources for each test."""
        # Create the visual output generator
        self.visual_generator = VisualOutputGenerator()
        
        # Sample reasoning outputs for testing
        self.chain_of_thought = MockReasoningOutput("chain_of_thought").get_data()
        self.reasoning_graph = MockReasoningOutput("reasoning_graph").get_data()
        self.tree_of_thought = MockReasoningOutput("tree_of_thought").get_data()
        self.diffusion_reasoning = MockReasoningOutput("diffusion_reasoning").get_data()
        
        # Sample numerical data for visualization
        self.numerical_data = {
            "x": list(range(10)),
            "y1": [x**2 for x in range(10)],
            "y2": [2*x + 3 for x in range(10)],
            "categories": ["A", "B", "C", "D", "E"],
            "values": [23, 45, 56, 78, 32]
        }
        
        # Default generation parameters
        self.default_params = {
            "width": 800,
            "height": 600,
            "theme": "default",
            "format": "png"
        }
    
    def test_initialization(self):
        """Test proper initialization of the component."""
        self.assertTrue(hasattr(self.visual_generator, "generate_visualization"))
        self.assertTrue(hasattr(self.visual_generator, "visualize_reasoning_path"))
        self.assertTrue(hasattr(self.visual_generator, "visualize_reasoning_graph"))
        self.assertTrue(hasattr(self.visual_generator, "visualize_concept_space"))
        self.assertTrue(hasattr(self.visual_generator, "generate_chart"))
    
    def test_visualize_reasoning_path(self):
        """Test visualization of a reasoning path."""
        # Generate visualization
        visualization = self.visual_generator.visualize_reasoning_path(
            reasoning=self.chain_of_thought,
            params=self.default_params
        )
        
        # Check that visualization was generated
        self.assertIsInstance(visualization, dict)
        self.assertIn("image_data", visualization)
        self.assertIn("format", visualization)
        
        # Check that the format matches the requested format
        self.assertEqual(visualization["format"], self.default_params["format"])
        
        # Check that image data is valid
        self.assertTrue(visualization["image_data"].startswith("iVBORw0KGgo") or 
                      visualization["image_data"].startswith("data:image"))
    
    def test_visualize_reasoning_graph(self):
        """Test visualization of a reasoning graph."""
        # Generate visualization
        visualization = self.visual_generator.visualize_reasoning_graph(
            reasoning=self.reasoning_graph,
            params=self.default_params
        )
        
        # Check that visualization was generated
        self.assertIsInstance(visualization, dict)
        self.assertIn("image_data", visualization)
        
        # Check that image data is valid
        self.assertTrue(visualization["image_data"].startswith("iVBORw0KGgo") or 
                      visualization["image_data"].startswith("data:image"))
    
    def test_visualize_tree_of_thought(self):
        """Test visualization of a tree of thought structure."""
        # Generate visualization
        visualization = self.visual_generator.visualize_tree_of_thought(
            reasoning=self.tree_of_thought,
            params=self.default_params
        )
        
        # Check that visualization was generated
        self.assertIsInstance(visualization, dict)
        self.assertIn("image_data", visualization)
        
        # Check that image data is valid
        self.assertTrue(visualization["image_data"].startswith("iVBORw0KGgo") or 
                      visualization["image_data"].startswith("data:image"))
    
    def test_visualize_concept_space(self):
        """Test visualization of a concept space from diffusion reasoning."""
        # Generate visualization
        visualization = self.visual_generator.visualize_concept_space(
            reasoning=self.diffusion_reasoning,
            params=self.default_params
        )
        
        # Check that visualization was generated
        self.assertIsInstance(visualization, dict)
        self.assertIn("image_data", visualization)
        
        # Check that image data is valid
        self.assertTrue(visualization["image_data"].startswith("iVBORw0KGgo") or 
                      visualization["image_data"].startswith("data:image"))
    
    def test_generate_line_chart(self):
        """Test generation of a line chart."""
        # Chart data
        chart_data = {
            "type": "line",
            "data": {
                "x": self.numerical_data["x"],
                "y": [self.numerical_data["y1"], self.numerical_data["y2"]],
                "labels": ["y = x²", "y = 2x + 3"]
            },
            "options": {
                "title": "Sample Line Chart",
                "xlabel": "X Axis",
                "ylabel": "Y Axis"
            }
        }
        
        # Generate chart
        visualization = self.visual_generator.generate_chart(
            chart_data=chart_data,
            params=self.default_params
        )
        
        # Check that chart was generated
        self.assertIsInstance(visualization, dict)
        self.assertIn("image_data", visualization)
        
        # Check that image data is valid
        self.assertTrue(visualization["image_data"].startswith("iVBORw0KGgo") or 
                      visualization["image_data"].startswith("data:image"))
    
    def test_generate_bar_chart(self):
        """Test generation of a bar chart."""
        # Chart data
        chart_data = {
            "type": "bar",
            "data": {
                "categories": self.numerical_data["categories"],
                "values": self.numerical_data["values"]
            },
            "options": {
                "title": "Sample Bar Chart",
                "xlabel": "Categories",
                "ylabel": "Values"
            }
        }
        
        # Generate chart
        visualization = self.visual_generator.generate_chart(
            chart_data=chart_data,
            params=self.default_params
        )
        
        # Check that chart was generated
        self.assertIsInstance(visualization, dict)
        self.assertIn("image_data", visualization)
    
    def test_generate_scatter_plot(self):
        """Test generation of a scatter plot."""
        # Chart data
        chart_data = {
            "type": "scatter",
            "data": {
                "x": self.numerical_data["x"],
                "y": self.numerical_data["y1"]
            },
            "options": {
                "title": "Sample Scatter Plot",
                "xlabel": "X Axis",
                "ylabel": "Y Axis"
            }
        }
        
        # Generate chart
        visualization = self.visual_generator.generate_chart(
            chart_data=chart_data,
            params=self.default_params
        )
        
        # Check that chart was generated
        self.assertIsInstance(visualization, dict)
        self.assertIn("image_data", visualization)
    
    def test_generate_pie_chart(self):
        """Test generation of a pie chart."""
        # Chart data
        chart_data = {
            "type": "pie",
            "data": {
                "labels": self.numerical_data["categories"],
                "values": self.numerical_data["values"]
            },
            "options": {
                "title": "Sample Pie Chart"
            }
        }
        
        # Generate chart
        visualization = self.visual_generator.generate_chart(
            chart_data=chart_data,
            params=self.default_params
        )
        
        # Check that chart was generated
        self.assertIsInstance(visualization, dict)
        self.assertIn("image_data", visualization)
    
    def test_generate_heatmap(self):
        """Test generation of a heatmap."""
        # Heatmap data
        heatmap_data = {
            "type": "heatmap",
            "data": {
                "matrix": [
                    [0.1, 0.2, 0.3, 0.4, 0.5],
                    [0.2, 0.4, 0.6, 0.8, 1.0],
                    [0.3, 0.6, 0.9, 0.7, 0.5],
                    [0.4, 0.8, 0.7, 0.5, 0.3],
                    [0.5, 1.0, 0.5, 0.3, 0.1]
                ],
                "x_labels": ["A", "B", "C", "D", "E"],
                "y_labels": ["V", "W", "X", "Y", "Z"]
            },
            "options": {
                "title": "Sample Heatmap",
                "colormap": "viridis"
            }
        }
        
        # Generate heatmap
        visualization = self.visual_generator.generate_chart(
            chart_data=heatmap_data,
            params=self.default_params
        )
        
        # Check that heatmap was generated
        self.assertIsInstance(visualization, dict)
        self.assertIn("image_data", visualization)
    
    def test_svg_format(self):
        """Test generating visualizations in SVG format."""
        # Set SVG format parameter
        svg_params = self.default_params.copy()
        svg_params["format"] = "svg"
        
        # Generate visualization
        visualization = self.visual_generator.visualize_reasoning_path(
            reasoning=self.chain_of_thought,
            params=svg_params
        )
        
        # Check that SVG was generated
        self.assertEqual(visualization["format"], "svg")
        self.assertTrue(visualization["image_data"].startswith("<svg") or 
                       visualization["image_data"].startswith("<?xml"))
    
    def test_theme_customization(self):
        """Test theme customization for visualizations."""
        # Test with different themes
        themes = ["light", "dark", "seaborn", "ggplot"]
        
        for theme in themes:
            theme_params = self.default_params.copy()
            theme_params["theme"] = theme
            
            # Generate visualization with theme
            visualization = self.visual_generator.visualize_reasoning_path(
                reasoning=self.chain_of_thought,
                params=theme_params
            )
            
            # Check that visualization was generated
            self.assertIsInstance(visualization, dict)
            self.assertIn("image_data", visualization)
    
    def test_custom_color_palette(self):
        """Test using a custom color palette."""
        # Set custom color palette
        custom_params = self.default_params.copy()
        custom_params["color_palette"] = ["#ff0000", "#00ff00", "#0000ff", "#ffff00", "#00ffff"]
        
        # Generate visualization
        visualization = self.visual_generator.visualize_reasoning_path(
            reasoning=self.chain_of_thought,
            params=custom_params
        )
        
        # Check that visualization was generated
        self.assertIsInstance(visualization, dict)
        self.assertIn("image_data", visualization)
    
    def test_interactive_visualization(self):
        """Test generating interactive visualizations."""
        # Set interactive parameter
        interactive_params = self.default_params.copy()
        interactive_params["interactive"] = True
        interactive_params["format"] = "html"
        
        # Generate visualization
        visualization = self.visual_generator.visualize_reasoning_graph(
            reasoning=self.reasoning_graph,
            params=interactive_params
        )
        
        # Check that interactive visualization was generated
        self.assertEqual(visualization["format"], "html")
        self.assertTrue("<script>" in visualization["image_data"])
    
    def test_visualization_with_annotations(self):
        """Test adding annotations to visualizations."""
        # Set annotations parameter
        annotated_params = self.default_params.copy()
        annotated_params["annotations"] = [
            {"text": "Important step", "x": 3, "y": 2},
            {"text": "Key insight", "x": 5, "y": 7}
        ]
        
        # Generate visualization
        visualization = self.visual_generator.visualize_reasoning_path(
            reasoning=self.chain_of_thought,
            params=annotated_params
        )
        
        # Check that visualization was generated
        self.assertIsInstance(visualization, dict)
        self.assertIn("image_data", visualization)
    
    def test_error_handling(self):
        """Test handling of invalid inputs."""
        # Test with None reasoning
        with self.assertRaises(ValueError):
            self.visual_generator.visualize_reasoning_path(reasoning=None)
        
        # Test with invalid chart type
        invalid_chart_data = {
            "type": "invalid_type",
            "data": {
                "x": self.numerical_data["x"],
                "y": self.numerical_data["y1"]
            }
        }
        
        with self.assertRaises(ValueError):
            self.visual_generator.generate_chart(
                chart_data=invalid_chart_data,
                params=self.default_params
            )


class TestActionOutputGenerator(unittest.TestCase):
    """Test cases for the Action Output Generator component."""
    
    def setUp(self):
        """Set up test resources for each test."""
        # Create the action output generator
        self.action_generator = ActionOutputGenerator()
        
        # Sample reasoning outputs for testing
        self.chain_of_thought = MockReasoningOutput("chain_of_thought").get_data()
        
        # Sample action scenarios
        self.math_scenario = {
            "problem": "Solve for x: 3x + 2 = 2x - 5",
            "domain": "math",
            "context": "algebra",
            "solution": "x = -7"
        }
        
        self.robotics_scenario = {
            "task": "Navigate to the kitchen and bring a cup of water",
            "domain": "robotics",
            "environment": {
                "start_location": "living_room",
                "target_location": "kitchen",
                "objects": ["cup", "faucet", "water"]
            },
            "constraints": ["avoid obstacles", "maintain stability"]
        }
        
        self.code_scenario = {
            "task": "Implement a function to calculate the factorial of a number",
            "domain": "programming",
            "language": "python",
            "requirements": ["handle negative numbers", "use recursion"]
        }
        
        # Default generation parameters
        self.default_params = {
            "format": "json",
            "level_of_detail": "medium",
            "include_explanations": True
        }
    
    def test_initialization(self):
        """Test proper initialization of the component."""
        self.assertTrue(hasattr(self.action_generator, "generate_actions"))
        self.assertTrue(hasattr(self.action_generator, "generate_math_actions"))
        self.assertTrue(hasattr(self.action_generator, "generate_robotics_actions"))
        self.assertTrue(hasattr(self.action_generator, "generate_code_actions"))
        self.assertTrue(hasattr(self.action_generator, "validate_actions"))
    
    def test_generate_math_actions(self):
        """Test generating actions for a math problem."""
        # Generate actions
        actions = self.action_generator.generate_math_actions(
            scenario=self.math_scenario,
            params=self.default_params
        )
        
        # Check that actions were generated
        self.assertIsInstance(actions, dict)
        self.assertIn("actions", actions)
        self.assertIsInstance(actions["actions"], list)
        self.assertGreater(len(actions["actions"]), 0)
        
        # Check action structure
        for action in actions["actions"]:
            self.assertIn("type", action)
            self.assertIn("operation", action)
            
            # Check that explanations are included if requested
            if self.default_params["include_explanations"]:
                self.assertIn("explanation", action)
    
    def test_generate_robotics_actions(self):
        """Test generating actions for a robotics scenario."""
        # Generate actions
        actions = self.action_generator.generate_robotics_actions(
            scenario=self.robotics_scenario,
            params=self.default_params
        )
        
        # Check that actions were generated
        self.assertIsInstance(actions, dict)
        self.assertIn("actions", actions)
        self.assertIsInstance(actions["actions"], list)
        self.assertGreater(len(actions["actions"]), 0)
        
        # Check action structure
        for action in actions["actions"]:
            self.assertIn("type", action)
            self.assertIn("parameters", action)
            
            # Check that actions respect the environment
            if action["type"] == "navigate":
                self.assertIn(action["parameters"]["from"], 
                             [self.robotics_scenario["environment"]["start_location"], "current_location"])
    
    def test_generate_code_actions(self):
        """Test generating actions for a code scenario."""
        # Generate actions
        actions = self.action_generator.generate_code_actions(
            scenario=self.code_scenario,
            params=self.default_params
        )
        
        # Check that actions were generated
        self.assertIsInstance(actions, dict)
        self.assertIn("actions", actions)
        self.assertIn("code", actions)
        self.assertIsInstance(actions["actions"], list)
        self.assertGreater(len(actions["actions"]), 0)
        
        # Check action structure
        for action in actions["actions"]:
            self.assertIn("type", action)
            self.assertIn("details", action)
        
        # Check that the code is valid Python
        try:
            compile(actions["code"], "<string>", "exec")
        except SyntaxError:
            self.fail("Generated code is not valid Python")
        
        # Check that the code implements the requested function
        self.assertIn("def factorial", actions["code"])
        
        # Check that the code handles negative numbers as required
        self.assertTrue(
            "if n < 0" in actions["code"] or 
            "raise ValueError" in actions["code"] or
            "return None" in actions["code"]
        )
    
    def test_generate_actions_from_reasoning(self):
        """Test generating actions from reasoning output."""
        # Generate actions
        actions = self.action_generator.generate_actions(
            reasoning=self.chain_of_thought,
            domain="math",
            params=self.default_params
        )
        
        # Check that actions were generated
        self.assertIsInstance(actions, dict)
        self.assertIn("actions", actions)
        self.assertIsInstance(actions["actions"], list)
        self.assertGreater(len(actions["actions"]), 0)
    
    def test_generate_sequential_actions(self):
        """Test generating sequential actions with dependencies."""
        # Set sequential parameter
        sequential_params = self.default_params.copy()
        sequential_params["sequential"] = True
        
        # Generate actions
        actions = self.action_generator.generate_robotics_actions(
            scenario=self.robotics_scenario,
            params=sequential_params
        )
        
        # Check that actions were generated
        self.assertIsInstance(actions, dict)
        self.assertIn("actions", actions)
        self.assertIsInstance(actions["actions"], list)
        self.assertGreater(len(actions["actions"]), 0)
        
        # Check for sequential dependencies
        has_dependencies = False
        for action in actions["actions"]:
            if "dependencies" in action:
                has_dependencies = True
                break
        
        self.assertTrue(has_dependencies, "No dependencies found in sequential actions")
    
    def test_generate_parallel_actions(self):
        """Test generating parallel actions."""
        # Set parallel parameter
        parallel_params = self.default_params.copy()
        parallel_params["parallel"] = True
        
        # Generate actions
        actions = self.action_generator.generate_robotics_actions(
            scenario=self.robotics_scenario,
            params=parallel_params
        )
        
        # Check that actions were generated
        self.assertIsInstance(actions, dict)
        self.assertIn("actions", actions)
        self.assertIn("execution_graph", actions)
    
    def test_action_validation(self):
        """Test validation of generated actions."""
        # Generate valid actions
        actions = self.action_generator.generate_math_actions(
            scenario=self.math_scenario,
            params=self.default_params
        )
        
        # Validate actions
        validation_result = self.action_generator.validate_actions(
            actions=actions["actions"],
            domain="math",
            context=self.math_scenario
        )
        
        # Check validation result
        self.assertIsInstance(validation_result, dict)
        self.assertIn("valid", validation_result)
        self.assertTrue(validation_result["valid"])
        
        # Test with invalid actions
        invalid_actions = [
            {"type": "invalid_type", "operation": "solve"},
            {"type": "algebra", "operation": "invalid_operation"}
        ]
        
        # Validate invalid actions
        validation_result = self.action_generator.validate_actions(
            actions=invalid_actions,
            domain="math",
            context=self.math_scenario
        )
        
        # Check validation result
        self.assertFalse(validation_result["valid"])
        self.assertIn("errors", validation_result)
        self.assertGreater(len(validation_result["errors"]), 0)
    
    def test_different_formats(self):
        """Test generating actions in different formats."""
        # Test with JSON format
        json_params = self.default_params.copy()
        json_params["format"] = "json"
        
        json_actions = self.action_generator.generate_math_actions(
            scenario=self.math_scenario,
            params=json_params
        )
        
        # Check JSON format
        self.assertIsInstance(json_actions, dict)
        
        # Test with XML format
        xml_params = self.default_params.copy()
        xml_params["format"] = "xml"
        
        xml_actions = self.action_generator.generate_math_actions(
            scenario=self.math_scenario,
            params=xml_params
        )
        
        # Check XML format
        self.assertIsInstance(xml_actions, str)
        self.assertTrue(xml_actions.startswith("<?xml") or xml_actions.startswith("<actions>"))
        
        # Test with YAML format
        yaml_params = self.default_params.copy()
        yaml_params["format"] = "yaml"
        
        yaml_actions = self.action_generator.generate_math_actions(
            scenario=self.math_scenario,
            params=yaml_params
        )
        
        # Check YAML format
        self.assertIsInstance(yaml_actions, str)
        self.assertIn("actions:", yaml_actions)
    
    def test_level_of_detail(self):
        """Test different levels of detail in action generation."""
        # Test with low detail
        low_detail_params = self.default_params.copy()
        low_detail_params["level_of_detail"] = "low"
        
        low_detail_actions = self.action_generator.generate_robotics_actions(
            scenario=self.robotics_scenario,
            params=low_detail_params
        )
        
        # Test with high detail
        high_detail_params = self.default_params.copy()
        high_detail_params["level_of_detail"] = "high"
        
        high_detail_actions = self.action_generator.generate_robotics_actions(
            scenario=self.robotics_scenario,
            params=high_detail_params
        )
        
        # High detail should have more actions or more detailed parameters
        high_detail_count = len(high_detail_actions["actions"])
        low_detail_count = len(low_detail_actions["actions"])
        
        self.assertGreaterEqual(high_detail_count, low_detail_count)
        
        # If same number of actions, check parameter count
        if high_detail_count == low_detail_count:
            high_detail_params_count = sum(len(action.get("parameters", {})) 
                                          for action in high_detail_actions["actions"])
            low_detail_params_count = sum(len(action.get("parameters", {})) 
                                         for action in low_detail_actions["actions"])
            
            self.assertGreater(high_detail_params_count, low_detail_params_count)
    
    def test_error_handling(self):
        """Test handling of invalid inputs."""
        # Test with None scenario
        with self.assertRaises(ValueError):
            self.action_generator.generate_math_actions(scenario=None)
        
        # Test with unsupported domain
        with self.assertRaises(ValueError):
            self.action_generator.generate_actions(
                reasoning=self.chain_of_thought,
                domain="unsupported_domain"
            )
        
        # Test with invalid format
        invalid_params = self.default_params.copy()
        invalid_params["format"] = "invalid_format"
        
        with self.assertRaises(ValueError):
            self.action_generator.generate_math_actions(
                scenario=self.math_scenario,
                params=invalid_params
            )


class TestMultimodalOutputSynthesizer(unittest.TestCase):
    """Test cases for the Multimodal Output Synthesizer component."""
    
    def setUp(self):
        """Set up test resources for each test."""
        # Create required mock objects
        self.language_model = MockLanguageModel()
        self.knowledge_base = MockKnowledgeBase()
        
        # Create the component generators
        self.text_generator = TextOutputGenerator(
            language_model=self.language_model,
            knowledge_base=self.knowledge_base
        )
        self.visual_generator = VisualOutputGenerator()
        self.action_generator = ActionOutputGenerator()
        
        # Create the multimodal synthesizer
        self.multimodal_synthesizer = MultimodalOutputSynthesizer(
            text_generator=self.text_generator,
            visual_generator=self.visual_generator,
            action_generator=self.action_generator
        )
        
        # Sample reasoning outputs for testing
        self.chain_of_thought = MockReasoningOutput("chain_of_thought").get_data()
        self.reasoning_graph = MockReasoningOutput("reasoning_graph").get_data()
        self.tree_of_thought = MockReasoningOutput("tree_of_thought").get_data()
        
        # Default synthesis parameters
        self.default_params = {
            "modalities": ["text", "visual"],
            "text_params": {
                "max_length": 500,
                "style": "concise",
                "format": "markdown"
            },
            "visual_params": {
                "width": 800,
                "height": 600,
                "theme": "default",
                "format": "png"
            },
            "integration_level": "high"
        }
    
    def test_initialization(self):
        """Test proper initialization of the component."""
        self.assertIsInstance(self.multimodal_synthesizer.text_generator, TextOutputGenerator)
        self.assertIsInstance(self.multimodal_synthesizer.visual_generator, VisualOutputGenerator)
        self.assertIsInstance(self.multimodal_synthesizer.action_generator, ActionOutputGenerator)
        self.assertTrue(hasattr(self.multimodal_synthesizer, "synthesize_output"))
    
    def test_synthesize_text_and_visual(self):
        """Test synthesizing text and visual outputs."""
        # Synthesize output
        output = self.multimodal_synthesizer.synthesize_output(
            reasoning=self.chain_of_thought,
            params=self.default_params
        )
        
        # Check that output was synthesized
        self.assertIsInstance(output, dict)
        self.assertIn("text", output)
        self.assertIn("visual", output)
        
        # Check text output
        self.assertIsInstance(output["text"], str)
        self.assertGreater(len(output["text"]), 0)
        
        # Check visual output
        self.assertIsInstance(output["visual"], dict)
        self.assertIn("image_data", output["visual"])
    
    def test_synthesize_with_action(self):
        """Test synthesizing with action output."""
        # Set parameters for text, visual, and action
        params = self.default_params.copy()
        params["modalities"] = ["text", "visual", "action"]
        params["action_params"] = {
            "format": "json",
            "level_of_detail": "medium"
        }
        
        # Synthesize output
        output = self.multimodal_synthesizer.synthesize_output(
            reasoning=self.chain_of_thought,
            params=params
        )
        
        # Check that output was synthesized
        self.assertIsInstance(output, dict)
        self.assertIn("text", output)
        self.assertIn("visual", output)
        self.assertIn("action", output)
        
        # Check action output
        self.assertIsInstance(output["action"], dict)
        self.assertIn("actions", output["action"])
    
    def test_synthesis_with_different_reasoning_types(self):
        """Test synthesis with different types of reasoning outputs."""
        # Test with chain of thought
        cot_output = self.multimodal_synthesizer.synthesize_output(
            reasoning=self.chain_of_thought,
            params=self.default_params
        )
        
        # Test with reasoning graph
        graph_output = self.multimodal_synthesizer.synthesize_output(
            reasoning=self.reasoning_graph,
            params=self.default_params
        )
        
        # Test with tree of thought
        tot_output = self.multimodal_synthesizer.synthesize_output(
            reasoning=self.tree_of_thought,
            params=self.default_params
        )
        
        # Check that all outputs were synthesized
        self.assertIsInstance(cot_output, dict)
        self.assertIsInstance(graph_output, dict)
        self.assertIsInstance(tot_output, dict)
        
        # Outputs should be different for different reasoning types
        self.assertNotEqual(cot_output["text"], graph_output["text"])
        self.assertNotEqual(cot_output["text"], tot_output["text"])
    
    def test_integration_levels(self):
        """Test different levels of integration between modalities."""
        # Test with low integration
        low_params = self.default_params.copy()
        low_params["integration_level"] = "low"
        
        low_output = self.multimodal_synthesizer.synthesize_output(
            reasoning=self.chain_of_thought,
            params=low_params
        )
        
        # Test with high integration
        high_params = self.default_params.copy()
        high_params["integration_level"] = "high"
        
        high_output = self.multimodal_synthesizer.synthesize_output(
            reasoning=self.chain_of_thought,
            params=high_params
        )
        
        # Check that outputs were synthesized
        self.assertIsInstance(low_output, dict)
        self.assertIsInstance(high_output, dict)
        
        # At high integration, text should reference the visual
        self.assertNotEqual(low_output["text"], high_output["text"])
        
        # High integration text should reference figures or visuals
        visual_references = ["figure", "visualization", "diagram", "chart", "graph", "image"]
        has_visual_reference = any(ref in high_output["text"].lower() for ref in visual_references)
        
        self.assertTrue(has_visual_reference, "High integration text doesn't reference visuals")
    
    def test_html_output_format(self):
        """Test generating HTML output that integrates all modalities."""
        # Set HTML output parameter
        html_params = self.default_params.copy()
        html_params["output_format"] = "html"
        html_params["modalities"] = ["text", "visual"]
        
        # Synthesize output
        output = self.multimodal_synthesizer.synthesize_output(
            reasoning=self.chain_of_thought,
            params=html_params
        )
        
        # Check that HTML output was generated
        self.assertIsInstance(output, dict)
        self.assertIn("html", output)
        
        # Check HTML content
        html_content = output["html"]
        self.assertIsInstance(html_content, str)
        self.assertTrue(html_content.startswith("<!DOCTYPE html>") or html_content.startswith("<html"))
        
        # HTML should include both text and image
        self.assertIn("<p>", html_content)
        self.assertIn("<img", html_content)
    
    def test_custom_layout(self):
        """Test using a custom layout for multimodal output."""
        # Set custom layout parameter
        layout_params = self.default_params.copy()
        layout_params["layout"] = {
            "type": "grid",
            "rows": 2,
            "columns": 2,
            "placements": {
                "text": {"row": 0, "col": 0, "rowspan": 1, "colspan": 2},
                "visual": {"row": 1, "col": 0, "rowspan": 1, "colspan": 1},
                "action": {"row": 1, "col": 1, "rowspan": 1, "colspan": 1}
            }
        }
        layout_params["modalities"] = ["text", "visual", "action"]
        layout_params["action_params"] = {"format": "json"}
        layout_params["output_format"] = "html"
        
        # Synthesize output
        output = self.multimodal_synthesizer.synthesize_output(
            reasoning=self.chain_of_thought,
            params=layout_params
        )
        
        # Check that output with custom layout was generated
        self.assertIsInstance(output, dict)
        self.assertIn("html", output)
        
        # HTML should include grid layout
        html_content = output["html"]
        self.assertIn("grid", html_content)
    
    def test_interactive_elements(self):
        """Test including interactive elements in multimodal output."""
        # Set interactive parameter
        interactive_params = self.default_params.copy()
        interactive_params["interactive"] = True
        interactive_params["output_format"] = "html"
        
        # Synthesize output
        output = self.multimodal_synthesizer.synthesize_output(
            reasoning=self.tree_of_thought,
            params=interactive_params
        )
        
        # Check that output with interactive elements was generated
        self.assertIsInstance(output, dict)
        self.assertIn("html", output)
        
        # HTML should include interactive elements
        html_content = output["html"]
        self.assertTrue(
            "<script>" in html_content or 
            "onclick=" in html_content or 
            "addEventListener" in html_content
        )
    
    def test_accessibility_features(self):
        """Test including accessibility features in multimodal output."""
        # Set accessibility parameter
        accessibility_params = self.default_params.copy()
        accessibility_params["accessibility"] = True
        accessibility_params["output_format"] = "html"
        
        # Synthesize output
        output = self.multimodal_synthesizer.synthesize_output(
            reasoning=self.chain_of_thought,
            params=accessibility_params
        )
        
        # Check that output with accessibility features was generated
        self.assertIsInstance(output, dict)
        self.assertIn("html", output)
        
        # HTML should include accessibility attributes
        html_content = output["html"]
        accessibility_attributes = ["alt=", "aria-", "role="]
        has_accessibility = any(attr in html_content for attr in accessibility_attributes)
        
        self.assertTrue(has_accessibility, "No accessibility features found in output")
    
    def test_responsive_design(self):
        """Test responsive design in multimodal output."""
        # Set responsive parameter
        responsive_params = self.default_params.copy()
        responsive_params["responsive"] = True
        responsive_params["output_format"] = "html"
        
        # Synthesize output
        output = self.multimodal_synthesizer.synthesize_output(
            reasoning=self.chain_of_thought,
            params=responsive_params
        )
        
        # Check that output with responsive design was generated
        self.assertIsInstance(output, dict)
        self.assertIn("html", output)
        
        # HTML should include responsive design elements
        html_content = output["html"]
        responsive_elements = ["@media", "viewport", "flex", "max-width", "%"]
        has_responsive = any(elem in html_content for elem in responsive_elements)
        
        self.assertTrue(has_responsive, "No responsive design elements found in output")
    
    def test_error_handling(self):
        """Test handling of invalid inputs."""
        # Test with None reasoning
        with self.assertRaises(ValueError):
            self.multimodal_synthesizer.synthesize_output(reasoning=None)
        
        # Test with empty modalities
        empty_params = self.default_params.copy()
        empty_params["modalities"] = []
        
        with self.assertRaises(ValueError):
            self.multimodal_synthesizer.synthesize_output(
                reasoning=self.chain_of_thought,
                params=empty_params
            )
        
        # Test with invalid modality
        invalid_params = self.default_params.copy()
        invalid_params["modalities"] = ["text", "invalid_modality"]
        
        with self.assertRaises(ValueError):
            self.multimodal_synthesizer.synthesize_output(
                reasoning=self.chain_of_thought,
                params=invalid_params
            )


if __name__ == "__main__":
    unittest.main()