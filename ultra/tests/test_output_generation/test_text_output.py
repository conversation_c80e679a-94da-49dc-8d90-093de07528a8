#!/usr/bin/env python3
"""
Test module for the Text Output Generator component of ULTRA.

This module contains comprehensive tests for the Text Output Generator,
validating its ability to convert various reasoning outputs into high-quality
natural language text, with appropriate formatting, style, and content integration.
"""

import os
import sys
import unittest
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Tuple, Any, Optional, Union, Callable, Set
import pytest
from collections import defaultdict, deque
import math
import random
import inspect
import json
import time
import re
import copy
import nltk
from nltk.tokenize import sent_tokenize, word_tokenize
from nltk.translate.bleu_score import sentence_bleu, corpus_bleu, SmoothingFunction
from nltk.translate.meteor_score import meteor_score
import rouge
from bs4 import BeautifulSoup
import markdown
import textwrap
import yaml

# Ensure NLTK resources are available
try:
    nltk.data.find('tokenizers/punkt')
except LookupError:
    nltk.download('punkt')

# Add the parent directory to the path so we can import from the ultra package
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../')))

# Import the Text Output Generator
from ultra.output_generation.text_output import TextOutputGenerator

# Import necessary utilities
from ultra.utils.config import Config
from ultra.utils.ultra_logging import get_logger
from ultra.utils.metrics import (
    text_coherence_score, 
    text_relevance_score, 
    text_factual_accuracy, 
    text_complexity_score
)

# Set up logger
logger = get_logger(__name__)


class MockLanguageModel:
    """
    Mock language model for testing the Text Output Generator.
    Simulates text generation with various styles, formats, and complexities.
    """
    
    def __init__(self, seed=42):
        """Initialize the mock language model with a random seed."""
        random.seed(seed)
        self.seed = seed
        
        # Define style parameters for different text generation styles
        self.styles = {
            "concise": {
                "avg_sent_length": 12,
                "max_sentences": 4,
                "formality_level": 0.5,
                "technical_terms_ratio": 0.1,
                "structure_markers": ["First", "Next", "Finally"],
                "explanation_depth": 1
            },
            "detailed": {
                "avg_sent_length": 18,
                "max_sentences": 10,
                "formality_level": 0.7,
                "technical_terms_ratio": 0.3,
                "structure_markers": ["First", "Second", "Third", "Fourth", "Furthermore", "Additionally", "In conclusion"],
                "explanation_depth": 3
            },
            "technical": {
                "avg_sent_length": 20,
                "max_sentences": 8,
                "formality_level": 0.9,
                "technical_terms_ratio": 0.6,
                "structure_markers": ["Initially", "Subsequently", "Consequently", "Therefore"],
                "explanation_depth": 2
            },
            "conversational": {
                "avg_sent_length": 10,
                "max_sentences": 6,
                "formality_level": 0.3,
                "technical_terms_ratio": 0.05,
                "structure_markers": ["So", "Then", "Also", "By the way"],
                "explanation_depth": 1,
                "questions": ["What do you think?", "Does that make sense?", "Isn't that interesting?"]
            },
            "educational": {
                "avg_sent_length": 15,
                "max_sentences": 12,
                "formality_level": 0.6,
                "technical_terms_ratio": 0.2,
                "structure_markers": ["First", "Next", "Additionally", "Moreover", "For example", "In summary"],
                "explanation_depth": 3,
                "examples": True
            }
        }
        
        # Domain-specific vocabulary for realistic generation
        self.domain_vocabulary = {
            "math": [
                "equation", "variable", "coefficient", "exponent", "polynomial", "function",
                "solve", "simplify", "factor", "expand", "derivative", "integral",
                "algebra", "calculus", "geometry", "trigonometry", "matrix", "vector",
                "constant", "term", "expression", "formula", "theorem", "proof"
            ],
            "science": [
                "hypothesis", "experiment", "theory", "observation", "evidence", "data",
                "molecule", "atom", "element", "compound", "reaction", "solution",
                "energy", "force", "mass", "velocity", "acceleration", "momentum",
                "ecosystem", "organism", "species", "evolution", "genome", "protein"
            ],
            "logic": [
                "premise", "conclusion", "argument", "inference", "deduction", "induction",
                "valid", "sound", "fallacy", "contradiction", "tautology", "syllogism",
                "necessary", "sufficient", "condition", "statement", "proposition", "quantifier"
            ]
        }
        
        # Language specific vocabulary for multilingual support
        self.language_vocabulary = {
            "English": {
                "math": {
                    "solve": "solve",
                    "equation": "equation",
                    "solution": "solution",
                    "therefore": "therefore"
                }
            },
            "Spanish": {
                "math": {
                    "solve": "resolver",
                    "equation": "ecuación",
                    "solution": "solución",
                    "therefore": "por lo tanto"
                }
            },
            "French": {
                "math": {
                    "solve": "résoudre",
                    "equation": "équation",
                    "solution": "solution",
                    "therefore": "par conséquent"
                }
            },
            "German": {
                "math": {
                    "solve": "lösen",
                    "equation": "Gleichung",
                    "solution": "Lösung",
                    "therefore": "daher"
                }
            }
        }
        
        # Templates for different content types
        self.templates = {
            "solution_explanation": [
                "To solve this problem, we need to {action}. The solution is {solution}.",
                "The approach to this problem involves {action}. We find that {solution}.",
                "We can solve this by {action}, which gives us {solution}.",
                "After {action}, we determine that {solution}."
            ],
            "concept_explanation": [
                "{concept} refers to {definition}. It is important because {importance}.",
                "{concept} is defined as {definition}. This concept is crucial for {importance}.",
                "The term {concept} means {definition}. It plays a key role in {importance}.",
                "{concept} can be understood as {definition}, which is significant for {importance}."
            ],
            "process_description": [
                "The process of {process} involves {step1}, followed by {step2}, and finally {step3}.",
                "{process} works through a series of steps: first {step1}, then {step2}, and lastly {step3}.",
                "To {process}, one must {step1}, then proceed to {step2}, and complete with {step3}.",
                "The mechanism of {process} functions by {step1}, continuing with {step2}, and concluding with {step3}."
            ]
        }
    
    def generate_text(self, prompt: str, params: Dict[str, Any] = None) -> str:
        """
        Generate text based on the prompt and parameters.
        
        Args:
            prompt: The input prompt for text generation
            params: Dictionary of generation parameters
                style: Text style (concise, detailed, technical, etc.)
                language: Target language for generation
                domain: Knowledge domain (math, science, logic, etc.)
                max_length: Maximum length in tokens
                format: Output format (text, markdown, html)
        
        Returns:
            Generated text
        """
        params = params or {}
        
        # Extract parameters with defaults
        style = params.get('style', 'detailed')
        language = params.get('language', 'English')
        domain = self._detect_domain(prompt)
        max_length = params.get('max_length', 200)
        output_format = params.get('format', 'text')
        
        # Get style settings
        style_settings = self.styles.get(style, self.styles['detailed'])
        
        # Extract key information from the prompt
        keywords = self._extract_keywords(prompt, domain)
        
        # Determine template type based on prompt analysis
        template_type = self._determine_template_type(prompt)
        
        # Generate base content
        content = self._generate_base_content(template_type, keywords, style_settings, domain, language)
        
        # Apply style modifications
        content = self._apply_style(content, style_settings)
        
        # Adjust length
        content = self._adjust_length(content, max_length)
        
        # Format output
        formatted_content = self._format_output(content, output_format)
        
        return formatted_content
    
    def _detect_domain(self, prompt: str) -> str:
        """Detect the knowledge domain from the prompt."""
        # Default to math domain if no specific domain is detected
        domain = "math"
        
        # Check for domain-specific keywords
        for potential_domain, keywords in self.domain_vocabulary.items():
            if any(keyword in prompt.lower() for keyword in keywords):
                domain = potential_domain
                break
        
        return domain
    
    def _extract_keywords(self, prompt: str, domain: str) -> List[str]:
        """Extract domain-specific keywords from the prompt."""
        words = re.findall(r'\b\w+\b', prompt.lower())
        domain_words = set(word.lower() for word in self.domain_vocabulary.get(domain, []))
        
        # Extract domain-specific words from the prompt
        keywords = [word for word in words if word in domain_words]
        
        # If no domain-specific keywords found, extract any significant words
        if not keywords:
            # Extract words with 4+ characters, excluding common words
            common_words = {"what", "when", "where", "which", "that", "this", "these", "those", "they", "there"}
            keywords = [word for word in words if len(word) >= 4 and word not in common_words]
        
        return keywords[:5]  # Limit to top 5 keywords
    
    def _determine_template_type(self, prompt: str) -> str:
        """Determine the appropriate template type based on the prompt."""
        if any(word in prompt.lower() for word in ["solve", "solution", "find", "calculate"]):
            return "solution_explanation"
        elif any(word in prompt.lower() for word in ["explain", "define", "what is", "concept"]):
            return "concept_explanation"
        else:
            return "process_description"
    
    def _generate_base_content(self, template_type: str, keywords: List[str], 
                              style_settings: Dict[str, Any], domain: str, language: str) -> str:
        """Generate base content using templates and keywords."""
        # Select a template
        templates = self.templates.get(template_type, self.templates["process_description"])
        template = random.choice(templates)
        
        # Fill in template placeholders with keywords or default values
        content = template
        
        # Replace placeholders with keywords or generated content
        placeholders = re.findall(r'\{([^}]+)\}', template)
        for placeholder in placeholders:
            if keywords:
                # Use a domain-specific term if available
                keyword = random.choice(keywords)
                keywords.remove(keyword)
                
                # Translate keyword if specified language is not English
                if language != "English" and placeholder in self.language_vocabulary.get(language, {}).get(domain, {}):
                    keyword = self.language_vocabulary[language][domain].get(placeholder, keyword)
                
                replacement = keyword
            else:
                # Generate generic content for the placeholder
                replacement = self._generate_placeholder_content(placeholder, domain)
            
            content = content.replace(f"{{{placeholder}}}", replacement)
        
        # Add explanation depth based on style
        depth = style_settings.get("explanation_depth", 1)
        if depth > 1:
            content += " " + self._generate_additional_explanation(depth, domain, language)
        
        # Add examples for educational style
        if style_settings.get("examples", False):
            content += " " + self._generate_example(domain, language)
        
        # Add questions for conversational style
        if "questions" in style_settings:
            content += " " + random.choice(style_settings["questions"])
        
        return content
    
    def _generate_placeholder_content(self, placeholder: str, domain: str) -> str:
        """Generate content for a template placeholder."""
        if placeholder == "action":
            math_actions = ["solving for the variable", "isolating terms", "applying the formula", 
                           "using algebraic manipulation", "finding the derivative"]
            science_actions = ["conducting an experiment", "analyzing the data", "applying the theory", 
                              "examining the evidence", "testing the hypothesis"]
            logic_actions = ["evaluating the premises", "drawing an inference", "analyzing the argument", 
                           "checking for validity", "applying deductive reasoning"]
            
            domain_actions = {
                "math": math_actions,
                "science": science_actions,
                "logic": logic_actions
            }
            
            return random.choice(domain_actions.get(domain, math_actions))
        
        elif placeholder == "solution":
            return f"x = {random.randint(-10, 10)}" if domain == "math" else "the outcome confirms our hypothesis"
        
        elif placeholder == "concept":
            concepts = {
                "math": ["integration", "differentiation", "matrix multiplication", "vector calculus"],
                "science": ["photosynthesis", "thermodynamics", "quantum mechanics", "natural selection"],
                "logic": ["syllogism", "deductive reasoning", "propositional logic", "predicate calculus"]
            }
            return random.choice(concepts.get(domain, ["the concept"]))
        
        elif placeholder == "definition":
            return "a fundamental principle in this field"
        
        elif placeholder == "importance":
            return "understanding key relationships in the domain"
        
        elif placeholder in ["process", "step1", "step2", "step3"]:
            processes = {
                "math": ["solving equations", "calculating derivatives", "performing integration", "matrix operations"],
                "science": ["experimental design", "data collection", "hypothesis testing", "theory formulation"],
                "logic": ["argument construction", "validity checking", "rule application", "proof development"]
            }
            
            steps = {
                "math": ["identifying variables", "applying formulae", "simplifying expressions", "checking solutions"],
                "science": ["defining variables", "controlling conditions", "measuring outcomes", "analyzing results"],
                "logic": ["premise identification", "rule application", "inference drawing", "conclusion validation"]
            }
            
            if placeholder == "process":
                return random.choice(processes.get(domain, ["the process"]))
            else:
                step_index = int(placeholder[4:]) - 1
                domain_steps = steps.get(domain, ["step one", "step two", "step three", "step four"])
                return domain_steps[min(step_index, len(domain_steps) - 1)]
        
        else:
            return placeholder  # Return the placeholder itself if no specific generator
    
    def _generate_additional_explanation(self, depth: int, domain: str, language: str) -> str:
        """Generate additional explanation based on the specified depth."""
        explanations = []
        
        for i in range(depth):
            if domain == "math":
                explanation = random.choice([
                    "This approach works because it follows the principles of algebraic manipulation.",
                    "The key insight here is to recognize the pattern and apply the appropriate technique.",
                    "We can verify this result by substituting back into the original equation.",
                    "This method generalizes to similar problems with the same structure."
                ])
            elif domain == "science":
                explanation = random.choice([
                    "This phenomenon occurs due to the fundamental laws governing the system.",
                    "The evidence supports this conclusion through multiple independent observations.",
                    "This theory provides a consistent explanation for the observed data.",
                    "The experimental results confirm our prediction with statistical significance."
                ])
            else:
                explanation = random.choice([
                    "This reasoning follows from the established principles in the field.",
                    "The conclusion is supported by a logical chain of inferences.",
                    "This approach is consistent with standard methodologies in the domain.",
                    "The analysis reveals important patterns that inform our understanding."
                ])
            
            # Translate if necessary
            if language != "English":
                # In a real implementation, this would do actual translation
                # For the mock, we'll just add a language indicator
                explanation += f" [{language} text]"
            
            explanations.append(explanation)
        
        return " ".join(explanations)
    
    def _generate_example(self, domain: str, language: str) -> str:
        """Generate an example relevant to the domain."""
        if domain == "math":
            example = "For example, if we have the equation 2x + 5 = 15, we subtract 5 from both sides to get 2x = 10, then divide by 2 to find that x = 5."
        elif domain == "science":
            example = "For instance, in an experiment measuring the effect of temperature on reaction rate, we observed that increasing the temperature by 10°C doubled the rate of reaction."
        else:
            example = "As an example, consider the argument: 'All humans are mortal. Socrates is human. Therefore, Socrates is mortal.' This demonstrates valid deductive reasoning."
        
        # Translate if necessary
        if language != "English":
            # In a real implementation, this would do actual translation
            # For the mock, we'll just add a language indicator
            example += f" [{language} text]"
        
        return example
    
    def _apply_style(self, content: str, style_settings: Dict[str, Any]) -> str:
        """Apply style modifications to the content."""
        # Split into sentences
        sentences = sent_tokenize(content)
        
        # Apply average sentence length adjustment
        avg_length = style_settings.get("avg_sent_length", 15)
        sentences = [self._adjust_sentence_length(sent, avg_length) for sent in sentences]
        
        # Apply formality level
        formality = style_settings.get("formality_level", 0.5)
        sentences = [self._adjust_formality(sent, formality) for sent in sentences]
        
        # Apply technical term ratio
        tech_ratio = style_settings.get("technical_terms_ratio", 0.2)
        sentences = [self._adjust_technical_terms(sent, tech_ratio) for sent in sentences]
        
        # Combine sentences
        content = " ".join(sentences)
        
        # Add structure markers if appropriate
        markers = style_settings.get("structure_markers", [])
        if markers and len(sentences) >= 3:
            sentences = sentences.copy()
            for i, marker in enumerate(markers[:min(len(markers), len(sentences))]):
                sentences[i] = f"{marker}, {sentences[i][0].lower() + sentences[i][1:]}"
            content = " ".join(sentences)
        
        return content
    
    def _adjust_sentence_length(self, sentence: str, target_length: int) -> str:
        """Adjust the sentence length to approach the target length."""
        words = sentence.split()
        current_length = len(words)
        
        if current_length < target_length * 0.7:
            # Sentence is too short, add qualifiers or details
            qualifiers = [
                "importantly, ", "specifically, ", "in particular, ", 
                "notably, ", "as we can observe, ", "as one might expect, "
            ]
            adjectives = [
                "significant ", "relevant ", "important ", "key ", 
                "fundamental ", "essential ", "critical "
            ]
            
            # Insert a random qualifier at the beginning
            sentence = random.choice(qualifiers) + sentence[0].lower() + sentence[1:]
            
            # Insert adjectives before some nouns
            words = sentence.split()
            for i in range(len(words) - 1):
                if random.random() < 0.3 and len(words) < target_length * 0.9:
                    words.insert(i, random.choice(adjectives))
            
            sentence = " ".join(words)
            
        elif current_length > target_length * 1.3:
            # Sentence is too long, simplify
            words = sentence.split()
            # Remove some adverbs and adjectives
            simplified_words = []
            skip_next = False
            for i, word in enumerate(words):
                if skip_next:
                    skip_next = False
                    continue
                
                # Skip some adjectives and adverbs
                if (word.endswith('ly') or (i < len(words) - 1 and words[i+1] in ['of', 'the', 'in', 'for'])) and random.random() < 0.5:
                    if i < len(words) - 1 and words[i+1] in ['of', 'the', 'in', 'for']:
                        skip_next = True
                    continue
                
                simplified_words.append(word)
            
            if simplified_words:
                sentence = " ".join(simplified_words)
        
        return sentence
    
    def _adjust_formality(self, sentence: str, formality_level: float) -> str:
        """Adjust the formality level of the sentence."""
        # High formality markers
        formal_patterns = [
            (r'\bi am\b', 'I am'),
            (r'\bdon\'t\b', 'do not'),
            (r'\bcan\'t\b', 'cannot'),
            (r'\bwon\'t\b', 'will not'),
            (r'\bgotta\b', 'must'),
            (r'\bwanna\b', 'want to'),
            (r'\bgonna\b', 'going to'),
            (r'\byeah\b', 'yes'),
            (r'\bnope\b', 'no'),
            (r'\bkinda\b', 'somewhat'),
            (r'\bsort of\b', 'somewhat'),
            (r'\ba lot\b', 'significantly'),
            (r'\blots of\b', 'numerous'),
            (r'\bbig\b', 'significant'),
            (r'\bsmall\b', 'minimal'),
            (r'\bgood\b', 'favorable'),
            (r'\bbad\b', 'unfavorable')
        ]
        
        # Low formality markers
        informal_patterns = [
            (r'\bI am\b', 'I\'m'),
            (r'\bdo not\b', 'don\'t'),
            (r'\bcannot\b', 'can\'t'),
            (r'\bwill not\b', 'won\'t'),
            (r'\bmust\b', 'have to'),
            (r'\bwant to\b', 'want to'),
            (r'\bgoing to\b', 'gonna'),
            (r'\byes\b', 'yeah'),
            (r'\bno\b', 'nope'),
            (r'\bsomewhat\b', 'kind of'),
            (r'\bsignificantly\b', 'a lot'),
            (r'\bnumerous\b', 'lots of'),
            (r'\bsignificant\b', 'big'),
            (r'\bminimal\b', 'small'),
            (r'\bfavorable\b', 'good'),
            (r'\bunfavorable\b', 'bad')
        ]
        
        # Apply patterns based on formality level
        if formality_level > 0.7:
            # High formality
            for pattern, replacement in formal_patterns:
                sentence = re.sub(pattern, replacement, sentence, flags=re.IGNORECASE)
        elif formality_level < 0.3:
            # Low formality
            for pattern, replacement in informal_patterns:
                sentence = re.sub(pattern, replacement, sentence, flags=re.IGNORECASE)
        
        return sentence
    
    def _adjust_technical_terms(self, sentence: str, tech_ratio: float) -> str:
        """Adjust the ratio of technical terms in the sentence."""
        # Pairs of (common term, technical term)
        term_pairs = [
            ('use', 'utilize'),
            ('show', 'demonstrate'),
            ('find', 'determine'),
            ('make', 'construct'),
            ('end', 'terminate'),
            ('start', 'initiate'),
            ('mix', 'amalgamate'),
            ('join', 'consolidate'),
            ('split', 'bifurcate'),
            ('measure', 'quantify'),
            ('size', 'magnitude'),
            ('shape', 'morphology'),
            ('heat', 'thermal energy'),
            ('cold', 'low temperature'),
            ('light', 'illumination'),
            ('dark', 'absence of light'),
            ('speed', 'velocity'),
            ('weight', 'mass'),
            ('hardness', 'rigidity'),
            ('softness', 'malleability')
        ]
        
        words = sentence.split()
        
        if tech_ratio > 0.5:
            # Increase technical terms
            for i, word in enumerate(words):
                for common, technical in term_pairs:
                    if word.lower() == common and random.random() < tech_ratio:
                        words[i] = technical
                        break
        elif tech_ratio < 0.3:
            # Decrease technical terms
            for i, word in enumerate(words):
                for common, technical in term_pairs:
                    if word.lower() == technical and random.random() > tech_ratio:
                        words[i] = common
                        break
        
        return " ".join(words)
    
    def _adjust_length(self, content: str, max_length: int) -> str:
        """Adjust the content to the specified maximum length."""
        words = content.split()
        
        if len(words) <= max_length:
            return content
        
        # Simple truncation with ellipsis
        truncated = " ".join(words[:max_length]) + "..."
        return truncated
    
    def _format_output(self, content: str, output_format: str) -> str:
        """Format the content according to the specified output format."""
        if output_format == "markdown":
            # Add markdown formatting
            paragraphs = content.split(". ")
            
            # Format first paragraph as heading
            if paragraphs:
                paragraphs[0] = "## " + paragraphs[0]
            
            # Add bullet points to some paragraphs
            for i in range(1, len(paragraphs)):
                if random.random() < 0.3:
                    paragraphs[i] = "* " + paragraphs[i]
            
            # Add bold and italic to some terms
            for i, paragraph in enumerate(paragraphs):
                words = paragraph.split()
                for j, word in enumerate(words):
                    if len(word) > 5 and random.random() < 0.1:
                        words[j] = f"**{word}**"
                    elif len(word) > 5 and random.random() < 0.05:
                        words[j] = f"*{word}*"
                paragraphs[i] = " ".join(words)
            
            return ". ".join(paragraphs)
        
        elif output_format == "html":
            # Convert to HTML
            paragraphs = content.split(". ")
            
            # Format first paragraph with heading
            if paragraphs:
                paragraphs[0] = f"<h2>{paragraphs[0]}</h2>"
            
            # Format other paragraphs
            for i in range(1, len(paragraphs)):
                paragraphs[i] = f"<p>{paragraphs[i]}.</p>"
            
            # Add some spans with styling
            html_content = "".join(paragraphs)
            html_content = re.sub(r'\b(\w{6,})\b', r'<span style="color: blue;">\1</span>', html_content)
            
            return f"<!DOCTYPE html><html><body>{html_content}</body></html>"
        
        elif output_format == "json":
            # Create a structured JSON output
            paragraphs = content.split(". ")
            json_data = {
                "content": content,
                "structure": {
                    "paragraphs": [p + "." for p in paragraphs if p],
                    "paragraph_count": len(paragraphs),
                    "word_count": len(content.split())
                },
                "metadata": {
                    "format": "json",
                    "generated": True
                }
            }
            
            return json.dumps(json_data, indent=2)
        
        else:
            # Default text format
            return content


class MockKnowledgeBase:
    """
    Mock knowledge base providing structured information for testing.
    """
    
    def __init__(self):
        """Initialize the knowledge base with domain-specific information."""
        # Domain-specific facts
        self.facts = {
            "math": {
                "algebra": [
                    "Algebra is a branch of mathematics dealing with symbols and the rules for manipulating them.",
                    "A linear equation can be written in the form ax + b = 0, where a and b are constants and a ≠ 0.",
                    "The quadratic formula x = (-b ± √(b² - 4ac)) / 2a solves the equation ax² + bx + c = 0.",
                    "A system of linear equations can be solved using elimination, substitution, or matrices."
                ],
                "calculus": [
                    "Calculus is the mathematical study of continuous change.",
                    "The derivative of a function represents its rate of change.",
                    "Integration is the process of finding the function that has a given derivative.",
                    "The fundamental theorem of calculus connects differentiation and integration as inverse processes."
                ],
                "geometry": [
                    "Geometry is the branch of mathematics that studies shapes, sizes, and properties of space.",
                    "The Pythagorean theorem states that in a right triangle, a² + b² = c², where c is the hypotenuse.",
                    "Similar triangles have proportional sides and congruent angles.",
                    "The area of a circle is πr², where r is the radius."
                ]
            },
            "science": {
                "physics": [
                    "Newton's First Law: An object will remain at rest or in uniform motion unless acted upon by an external force.",
                    "Newton's Second Law: Force equals mass times acceleration (F = ma).",
                    "Newton's Third Law: For every action, there is an equal and opposite reaction.",
                    "Einstein's mass-energy equivalence is expressed as E = mc²."
                ],
                "chemistry": [
                    "The periodic table organizes elements based on their atomic number and chemical properties.",
                    "A chemical reaction involves the rearrangement of atoms to form new substances.",
                    "An acid is a substance that donates hydrogen ions (H+) in solution.",
                    "A base is a substance that accepts hydrogen ions or donates hydroxide ions (OH-)."
                ],
                "biology": [
                    "DNA (deoxyribonucleic acid) is the genetic material that carries the instructions for development and functioning of living organisms.",
                    "Cells are the basic structural and functional units of all living organisms.",
                    "Photosynthesis is the process by which plants convert light energy into chemical energy.",
                    "Evolution is the process of biological change by which descendants differ from their ancestors."
                ]
            },
            "logic": {
                "formal_logic": [
                    "Formal logic is the study of inference with purely formal content.",
                    "A syllogism is a kind of logical argument that applies deductive reasoning to arrive at a conclusion.",
                    "Modus ponens is a rule of inference expressed as: if P implies Q and P is true, then Q is true.",
                    "A logical fallacy is an error in reasoning that renders an argument invalid."
                ],
                "critical_thinking": [
                    "Critical thinking is the objective analysis and evaluation of an issue to form a judgment.",
                    "Confirmation bias is the tendency to search for or interpret information that confirms one's preexisting beliefs.",
                    "Ad hominem is a fallacious argument that attacks the character or attributes of a person rather than the position they are maintaining.",
                    "The principle of charity requires interpreting a speaker's statements in the most rational way possible."
                ]
            }
        }
        
        # Structured definitions
        self.definitions = {
            "equation": {
                "definition": "A mathematical statement asserting the equality of two expressions.",
                "examples": ["2x + 3 = 7", "a² + b² = c²"],
                "related_terms": ["expression", "variable", "constant"]
            },
            "derivative": {
                "definition": "A measure of how a function changes as its input changes.",
                "examples": ["The derivative of f(x) = x² is f'(x) = 2x"],
                "related_terms": ["function", "rate of change", "calculus"]
            },
            "syllogism": {
                "definition": "A form of deductive reasoning consisting of a major premise, a minor premise, and a conclusion.",
                "examples": ["All men are mortal. Socrates is a man. Therefore, Socrates is mortal."],
                "related_terms": ["logic", "deduction", "premise", "conclusion"]
            },
            "photosynthesis": {
                "definition": "The process by which green plants and some other organisms use sunlight to synthesize foods with carbon dioxide and water.",
                "examples": ["6CO₂ + 6H₂O + light energy → C₆H₁₂O₆ + 6O₂"],
                "related_terms": ["chlorophyll", "glucose", "carbon dioxide", "oxygen"]
            }
        }
        
        # Educational examples
        self.examples = {
            "solving_linear_equations": [
                {
                    "problem": "Solve for x: 2x + 3 = 7",
                    "solution": [
                        "2x + 3 = 7",
                        "2x = 7 - 3",
                        "2x = 4",
                        "x = 4 / 2",
                        "x = 2"
                    ],
                    "explanation": "We isolate the variable by moving all other terms to the opposite side of the equation, then divide both sides by the coefficient of the variable."
                },
                {
                    "problem": "Solve for y: 3y - 5 = 10",
                    "solution": [
                        "3y - 5 = 10",
                        "3y = 10 + 5",
                        "3y = 15",
                        "y = 15 / 3",
                        "y = 5"
                    ],
                    "explanation": "We isolate the variable by adding 5 to both sides, then divide both sides by the coefficient 3."
                }
            ],
            "law_of_motion": [
                {
                    "principle": "Newton's Second Law: F = ma",
                    "problem": "Calculate the force needed to accelerate a 2 kg object at 3 m/s².",
                    "solution": [
                        "F = ma",
                        "F = 2 kg × 3 m/s²",
                        "F = 6 N"
                    ],
                    "explanation": "We apply Newton's Second Law by multiplying the mass by the acceleration to find the force."
                }
            ],
            "syllogistic_reasoning": [
                {
                    "premises": [
                        "All A are B.",
                        "All B are C."
                    ],
                    "conclusion": "Therefore, all A are C.",
                    "explanation": "This is a valid syllogism demonstrating transitivity of the 'is a' relation."
                }
            ]
        }
        
        # Citations and references
        self.references = {
            "mathematics": [
                {
                    "title": "Principles of Mathematical Analysis",
                    "author": "Walter Rudin",
                    "year": 1976,
                    "publisher": "McGraw-Hill"
                },
                {
                    "title": "Calculus",
                    "author": "James Stewart",
                    "year": 2015,
                    "publisher": "Cengage Learning"
                }
            ],
            "physics": [
                {
                    "title": "The Feynman Lectures on Physics",
                    "author": "Richard Feynman",
                    "year": 1964,
                    "publisher": "Addison-Wesley"
                }
            ],
            "logic": [
                {
                    "title": "Introduction to Logic",
                    "author": "Irving M. Copi",
                    "year": 2018,
                    "publisher": "Routledge"
                }
            ]
        }
    
    def get_facts(self, domain: str, subdomain: str = None) -> List[str]:
        """
        Retrieve facts from the knowledge base.
        
        Args:
            domain: The domain to retrieve facts from (math, science, logic)
            subdomain: Optional subdomain to narrow the search
            
        Returns:
            List of facts
        """
        if domain in self.facts:
            if subdomain and subdomain in self.facts[domain]:
                return self.facts[domain][subdomain]
            elif not subdomain:
                # Return all facts from all subdomains
                all_facts = []
                for sub in self.facts[domain]:
                    all_facts.extend(self.facts[domain][sub])
                return all_facts
        return []
    
    def get_definition(self, term: str) -> Dict[str, Any]:
        """
        Retrieve a definition from the knowledge base.
        
        Args:
            term: The term to retrieve the definition for
            
        Returns:
            Dictionary containing definition information
        """
        return self.definitions.get(term.lower(), {})
    
    def get_examples(self, topic: str) -> List[Dict[str, Any]]:
        """
        Retrieve examples for a specific topic.
        
        Args:
            topic: The topic to retrieve examples for
            
        Returns:
            List of examples
        """
        return self.examples.get(topic, [])
    
    def get_references(self, field: str) -> List[Dict[str, Any]]:
        """
        Retrieve references for a specific field.
        
        Args:
            field: The field to retrieve references for
            
        Returns:
            List of references
        """
        return self.references.get(field, [])
    
    def find_related_terms(self, term: str) -> List[str]:
        """
        Find terms related to the given term.
        
        Args:
            term: The term to find related terms for
            
        Returns:
            List of related terms
        """
        # Check if the term has a definition with related terms
        definition = self.get_definition(term)
        if definition and "related_terms" in definition:
            return definition["related_terms"]
        
        # Otherwise search for the term in all definitions and gather related terms
        related = []
        for def_term, def_data in self.definitions.items():
            if term.lower() in def_data.get("related_terms", []):
                related.append(def_term)
        
        return related


class MockReasoningOutput:
    """
    Mock class for simulating reasoning outputs to test the text generator.
    Provides standardized formats for different reasoning types.
    """
    def __init__(self, reasoning_type="chain_of_thought"):
        """
        Initialize with a specific reasoning type.
        
        Args:
            reasoning_type: Type of reasoning output to simulate (chain_of_thought, 
                           tree_of_thought, reasoning_graph, etc.)
        """
        self.reasoning_type = reasoning_type
        
        # Generate appropriate mock data based on reasoning type
        if reasoning_type == "chain_of_thought":
            self.data = self._generate_chain_of_thought()
        elif reasoning_type == "tree_of_thought":
            self.data = self._generate_tree_of_thought()
        elif reasoning_type == "reasoning_graph":
            self.data = self._generate_reasoning_graph()
        elif reasoning_type == "diffusion_reasoning":
            self.data = self._generate_diffusion_reasoning()
        else:
            self.data = self._generate_generic_reasoning()
    
    def _generate_chain_of_thought(self):
        """Generate a mock chain of thought reasoning path."""
        return {
            "problem": "Solve the equation: 3x + 2 = 2x - 5",
            "path": [
                "First, I'll move all terms with x to the left side of the equation.",
                "3x - 2x = -5 - 2",
                "x = -7",
                "To verify, I'll substitute x = -7 back into the original equation.",
                "3(-7) + 2 = 2(-7) - 5",
                "-21 + 2 = -14 - 5",
                "-19 = -19",
                "The equation is verified, so x = -7 is the solution."
            ],
            "confidence": 0.92,
            "solution": "x = -7"
        }
    
    def _generate_tree_of_thought(self):
        """Generate a mock tree of thought reasoning structure."""
        return {
            "problem": "What is the value of 5! (5 factorial)?",
            "root": "Calculate 5!",
            "nodes": {
                "n0": {"text": "Calculate 5!", "children": ["n1", "n2"], "depth": 0},
                "n1": {"text": "Use the formula: n! = n × (n-1)!", "children": ["n3"], "depth": 1},
                "n2": {"text": "Enumerate: 5! = 5 × 4 × 3 × 2 × 1", "children": ["n4"], "depth": 1},
                "n3": {"text": "5! = 5 × 4!\n4! = 4 × 3!\n3! = 3 × 2!\n2! = 2 × 1!\n1! = 1", "children": ["n5"], "depth": 2},
                "n4": {"text": "5! = 5 × 4 × 3 × 2 × 1 = 120", "children": [], "depth": 2},
                "n5": {"text": "5! = 5 × 4! = 5 × 24 = 120", "children": [], "depth": 3}
            },
            "best_path": ["n0", "n2", "n4"],
            "confidence": 0.95,
            "solution": "120"
        }
    
    def _generate_reasoning_graph(self):
        """Generate a mock reasoning graph structure."""
        return {
            "problem": "Is a square a rectangle?",
            "nodes": {
                "p0": {"text": "Problem: Is a square a rectangle?", "type": "problem"},
                "obs1": {"text": "A square has 4 sides of equal length.", "type": "observation"},
                "obs2": {"text": "A square has 4 angles of 90 degrees.", "type": "observation"},
                "obs3": {"text": "A rectangle has 4 sides with opposite sides of equal length.", "type": "observation"},
                "obs4": {"text": "A rectangle has 4 angles of 90 degrees.", "type": "observation"},
                "hyp1": {"text": "A square satisfies all properties of a rectangle.", "type": "hypothesis"},
                "concl1": {"text": "A square is a special case of a rectangle.", "type": "conclusion"},
                "concl2": {"text": "Yes, a square is a rectangle.", "type": "conclusion"}
            },
            "edges": {
                "e1": {"from": "p0", "to": "obs1", "type": "generates"},
                "e2": {"from": "p0", "to": "obs2", "type": "generates"},
                "e3": {"from": "p0", "to": "obs3", "type": "generates"},
                "e4": {"from": "p0", "to": "obs4", "type": "generates"},
                "e5": {"from": "obs1", "to": "hyp1", "type": "supports"},
                "e6": {"from": "obs2", "to": "hyp1", "type": "supports"},
                "e7": {"from": "obs3", "to": "hyp1", "type": "supports"},
                "e8": {"from": "obs4", "to": "hyp1", "type": "supports"},
                "e9": {"from": "hyp1", "to": "concl1", "type": "implies"},
                "e10": {"from": "concl1", "to": "concl2", "type": "implies"}
            },
            "strongest_conclusion": "concl2",
            "confidence": 0.98,
            "solution": "Yes, a square is a rectangle."
        }
    
    def _generate_diffusion_reasoning(self):
        """Generate a mock diffusion reasoning process."""
        return {
            "problem": "How might we reduce urban traffic congestion?",
            "initial_concept": "Traffic congestion reduction",
            "diffusion_trajectory": [
                {"step": 0, "concept": "Traffic congestion reduction", "noise_level": 0.0},
                {"step": 5, "concept": "Urban planning solutions", "noise_level": 0.2},
                {"step": 10, "concept": "Public transportation enhancement", "noise_level": 0.4},
                {"step": 15, "concept": "Remote work policies", "noise_level": 0.6},
                {"step": 20, "concept": "Incentivize carpooling", "noise_level": 0.8},
                {"step": 25, "concept": "Congestion pricing", "noise_level": 1.0}
            ],
            "reverse_diffusion": [
                {"step": 0, "concept": "Congestion pricing", "guidance": "Cost-effective"},
                {"step": 5, "concept": "Congestion pricing in city centers", "guidance": "Implementable"},
                {"step": 10, "concept": "Time-based congestion pricing", "guidance": "Flexible"},
                {"step": 15, "concept": "Dynamic congestion pricing with mobile app integration", "guidance": "Modern"},
                {"step": 20, "concept": "AI-optimized dynamic congestion pricing system", "guidance": "Efficient"}
            ],
            "uncertainty": {
                "epistemic": 0.15,
                "aleatoric": 0.22,
                "decision": 0.18
            },
            "solution": "Implement AI-optimized dynamic congestion pricing system with mobile app integration",
            "confidence": 0.87
        }
    
    def _generate_generic_reasoning(self):
        """Generate a generic reasoning structure for testing."""
        return {
            "problem": "What happens when you mix baking soda and vinegar?",
            "reasoning": [
                "Baking soda is sodium bicarbonate (NaHCO3), a base.",
                "Vinegar contains acetic acid (CH3COOH), an acid.",
                "When acids and bases mix, they undergo a neutralization reaction.",
                "The reaction produces sodium acetate, water, and carbon dioxide.",
                "The chemical equation is: NaHCO3 + CH3COOH → CH3COONa + H2O + CO2",
                "The carbon dioxide forms bubbles, creating a fizzing effect."
            ],
            "solution": "Mixing baking soda and vinegar creates a chemical reaction that produces sodium acetate, water, and carbon dioxide gas, which causes visible fizzing.",
            "confidence": 0.94
        }
    
    def get_data(self):
        """Return the generated reasoning data."""
        return self.data


class TestTextOutputGenerator(unittest.TestCase):
    """Test suite for the Text Output Generator component."""
    
    def setUp(self):
        """Set up test resources for each test."""
        # Create mock objects
        self.language_model = MockLanguageModel()
        self.knowledge_base = MockKnowledgeBase()
        
        # Create the text output generator
        self.text_generator = TextOutputGenerator(
            language_model=self.language_model,
            knowledge_base=self.knowledge_base
        )
        
        # Sample reasoning outputs for testing
        self.cot_reasoning = MockReasoningOutput("chain_of_thought").get_data()
        self.tot_reasoning = MockReasoningOutput("tree_of_thought").get_data()
        self.graph_reasoning = MockReasoningOutput("reasoning_graph").get_data()
        self.diffusion_reasoning = MockReasoningOutput("diffusion_reasoning").get_data()
        self.generic_reasoning = MockReasoningOutput("generic").get_data()
        
        # Default generation parameters
        self.default_params = {
            "max_length": 500,
            "style": "concise",
            "format": "text",
            "include_reasoning": True,
            "include_confidence": True,
            "enhance_with_knowledge": False,
            "language": "English"
        }
    
    def test_initialization(self):
        """Test proper initialization of the Text Output Generator."""
        # Check that the generator has the required attributes
        self.assertIsInstance(self.text_generator.language_model, MockLanguageModel)
        self.assertIsInstance(self.text_generator.knowledge_base, MockKnowledgeBase)
        
        # Check that the generator has the required methods
        self.assertTrue(hasattr(self.text_generator, "generate_text"))
        self.assertTrue(hasattr(self.text_generator, "format_reasoning"))
        self.assertTrue(hasattr(self.text_generator, "enhance_with_knowledge"))
        self.assertTrue(hasattr(self.text_generator, "evaluate_quality"))
        
        # Verify default configuration values
        self.assertIsNotNone(self.text_generator.default_style)
        self.assertIsNotNone(self.text_generator.default_format)
        self.assertIsNotNone(self.text_generator.default_max_length)
    
    def test_initialization_with_config(self):
        """Test initialization with a configuration object."""
        # Create a custom configuration
        config = {
            "default_style": "technical",
            "default_format": "markdown",
            "default_max_length": 1000,
            "include_reasoning_by_default": False,
            "include_confidence_by_default": False,
            "knowledge_enhancement_level": "high"
        }
        
        # Create a generator with the custom configuration
        custom_generator = TextOutputGenerator(
            language_model=self.language_model,
            knowledge_base=self.knowledge_base,
            config=config
        )
        
        # Verify that the configuration was applied
        self.assertEqual(custom_generator.default_style, "technical")
        self.assertEqual(custom_generator.default_format, "markdown")
        self.assertEqual(custom_generator.default_max_length, 1000)
        self.assertFalse(custom_generator.include_reasoning_by_default)
        self.assertFalse(custom_generator.include_confidence_by_default)
        self.assertEqual(custom_generator.knowledge_enhancement_level, "high")
    
    def test_generate_text_from_chain_of_thought(self):
        """Test generating text from a chain of thought reasoning output."""
        output = self.text_generator.generate_text(
            reasoning=self.cot_reasoning,
            params=self.default_params
        )
        
        # Verify that text was generated
        self.assertIsInstance(output, str)
        self.assertGreater(len(output), 0)
        
        # Verify that the solution is included in the output
        self.assertIn(self.cot_reasoning["solution"], output)
        
        # If include_reasoning is True, verify that reasoning steps are included
        if self.default_params["include_reasoning"]:
            # Check for at least one intermediate step
            intermediate_steps = [step for step in self.cot_reasoning["path"] 
                                if step != self.cot_reasoning["path"][0] and "solution" not in step.lower()]
            if intermediate_steps:
                self.assertTrue(any(step in output for step in intermediate_steps), 
                              "Reasoning steps not included in output")
        
        # If include_confidence is True, verify that confidence information is included
        if self.default_params["include_confidence"]:
            confidence_str = str(self.cot_reasoning["confidence"])[:4]  # First few digits
            confidence_included = (
                confidence_str in output or
                "confidence" in output.lower() or
                "certainty" in output.lower()
            )
            self.assertTrue(confidence_included, "Confidence information not included in output")
    
    def test_generate_text_from_tree_of_thought(self):
        """Test generating text from a tree of thought reasoning output."""
        output = self.text_generator.generate_text(
            reasoning=self.tot_reasoning,
            params=self.default_params
        )
        
        # Verify that text was generated
        self.assertIsInstance(output, str)
        self.assertGreater(len(output), 0)
        
        # Verify that the solution is included in the output
        self.assertIn(self.tot_reasoning["solution"], output)
        
        # Verify that the best path is described in the output
        best_path_nodes = [self.tot_reasoning["nodes"][node_id]["text"] 
                          for node_id in self.tot_reasoning["best_path"]]
        
        # At least one node from the best path should be mentioned
        self.assertTrue(any(node in output for node in best_path_nodes), 
                       "Best path not described in output")
    
    def test_generate_text_from_reasoning_graph(self):
        """Test generating text from a reasoning graph output."""
        output = self.text_generator.generate_text(
            reasoning=self.graph_reasoning,
            params=self.default_params
        )
        
        # Verify that text was generated
        self.assertIsInstance(output, str)
        self.assertGreater(len(output), 0)
        
        # Verify that the solution is included in the output
        self.assertIn(self.graph_reasoning["solution"], output)
        
        # Verify that important observations and conclusions are included
        strongest_conclusion_id = self.graph_reasoning["strongest_conclusion"]
        strongest_conclusion = self.graph_reasoning["nodes"][strongest_conclusion_id]["text"]
        self.assertIn(strongest_conclusion, output, "Strongest conclusion not included in output")
    
    def test_generate_text_from_diffusion_reasoning(self):
        """Test generating text from diffusion reasoning output."""
        output = self.text_generator.generate_text(
            reasoning=self.diffusion_reasoning,
            params=self.default_params
        )
        
        # Verify that text was generated
        self.assertIsInstance(output, str)
        self.assertGreater(len(output), 0)
        
        # Verify that the solution is included in the output
        solution_fragments = [s for s in self.diffusion_reasoning["solution"].split() if len(s) > 5]
        self.assertTrue(any(fragment in output for fragment in solution_fragments), 
                       "Solution not included in output")
        
        # Verify that diffusion concepts are mentioned
        key_concepts = [step["concept"] for step in self.diffusion_reasoning["reverse_diffusion"]]
        self.assertTrue(any(concept in output for concept in key_concepts), 
                       "No diffusion concepts mentioned in output")
    
    def test_format_variations(self):
        """Test generating text in different formats."""
        # Test text format
        text_params = self.default_params.copy()
        text_params["format"] = "text"
        
        text_output = self.text_generator.generate_text(
            reasoning=self.cot_reasoning,
            params=text_params
        )
        
        # Test markdown format
        markdown_params = self.default_params.copy()
        markdown_params["format"] = "markdown"
        
        markdown_output = self.text_generator.generate_text(
            reasoning=self.cot_reasoning,
            params=markdown_params
        )
        
        # Verify that markdown contains markdown syntax
        markdown_elements = ["#", "##", "*", "**", "-", "`"]
        self.assertTrue(any(element in markdown_output for element in markdown_elements),
                       "No markdown syntax found in markdown output")
        
        # Test HTML format
        html_params = self.default_params.copy()
        html_params["format"] = "html"
        
        html_output = self.text_generator.generate_text(
            reasoning=self.cot_reasoning,
            params=html_params
        )
        
        # Verify that HTML contains HTML syntax
        html_elements = ["<h", "<p", "<div", "<span", "<b", "<i", "<ul", "<li"]
        self.assertTrue(any(element in html_output for element in html_elements),
                       "No HTML syntax found in HTML output")
        
        # Test JSON format
        json_params = self.default_params.copy()
        json_params["format"] = "json"
        
        json_output = self.text_generator.generate_text(
            reasoning=self.cot_reasoning,
            params=json_params
        )
        
        # Verify that output is valid JSON
        try:
            json_data = json.loads(json_output)
            self.assertIsInstance(json_data, dict)
        except json.JSONDecodeError:
            self.fail("JSON output is not valid JSON")
    
    def test_style_variations(self):
        """Test generating text in different styles."""
        # Test concise style
        concise_params = self.default_params.copy()
        concise_params["style"] = "concise"
        
        concise_output = self.text_generator.generate_text(
            reasoning=self.cot_reasoning,
            params=concise_params
        )
        
        # Test detailed style
        detailed_params = self.default_params.copy()
        detailed_params["style"] = "detailed"
        
        detailed_output = self.text_generator.generate_text(
            reasoning=self.cot_reasoning,
            params=detailed_params
        )
        
        # Detailed output should be longer than concise output
        self.assertGreater(len(detailed_output), len(concise_output),
                         "Detailed output is not longer than concise output")
        
        # Test technical style
        technical_params = self.default_params.copy()
        technical_params["style"] = "technical"
        
        technical_output = self.text_generator.generate_text(
            reasoning=self.cot_reasoning,
            params=technical_params
        )
        
        # Technical style should use more technical terminology
        technical_terms = ["variable", "coefficient", "equation", "expression", "solution", "algebraic"]
        self.assertTrue(any(term in technical_output for term in technical_terms),
                       "Technical output does not use technical terminology")
        
        # Test conversational style
        conversational_params = self.default_params.copy()
        conversational_params["style"] = "conversational"
        
        conversational_output = self.text_generator.generate_text(
            reasoning=self.cot_reasoning,
            params=conversational_params
        )
        
        # Conversational style should include questions or conversational markers
        conversational_markers = ["we", "you", "let's", "?"]
        self.assertTrue(any(marker in conversational_output.lower() for marker in conversational_markers),
                       "Conversational output does not include conversational markers")
    
    def test_enhance_with_knowledge(self):
        """Test enhancing output with knowledge base information."""
        # Test without knowledge enhancement
        no_knowledge_params = self.default_params.copy()
        no_knowledge_params["enhance_with_knowledge"] = False
        
        no_knowledge_output = self.text_generator.generate_text(
            reasoning=self.cot_reasoning,
            params=no_knowledge_params
        )
        
        # Test with knowledge enhancement
        with_knowledge_params = self.default_params.copy()
        with_knowledge_params["enhance_with_knowledge"] = True
        
        with_knowledge_output = self.text_generator.generate_text(
            reasoning=self.cot_reasoning,
            params=with_knowledge_params
        )
        
        # Output with knowledge enhancement should be longer
        self.assertGreater(len(with_knowledge_output), len(no_knowledge_output),
                         "Knowledge-enhanced output is not longer than basic output")
        
        # Knowledge-enhanced output should include facts from the knowledge base
        # Get relevant facts for the problem domain (math/algebra)
        math_facts = self.knowledge_base.get_facts("math", "algebra")
        knowledge_included = any(fact in with_knowledge_output for fact in math_facts)
        
        # If no direct fact matches, check for key math terms
        if not knowledge_included:
            math_terms = ["equation", "variable", "solve", "linear", "algebraic"]
            knowledge_included = any(term in with_knowledge_output for term in math_terms)
        
        self.assertTrue(knowledge_included, "Knowledge enhancement did not include domain knowledge")
    
    def test_language_adaptation(self):
        """Test generating text in different languages."""
        # Only test if the generator supports language adaptation
        if not hasattr(self.text_generator, "supported_languages"):
            self.skipTest("Text generator does not support language adaptation")
        
        # Test English (default)
        english_params = self.default_params.copy()
        english_params["language"] = "English"
        
        english_output = self.text_generator.generate_text(
            reasoning=self.cot_reasoning,
            params=english_params
        )
        
        # Test another supported language
        alternate_language = "Spanish"
        if alternate_language in self.text_generator.supported_languages:
            alternate_params = self.default_params.copy()
            alternate_params["language"] = alternate_language
            
            alternate_output = self.text_generator.generate_text(
                reasoning=self.cot_reasoning,
                params=alternate_params
            )
            
            # Outputs should be different
            self.assertNotEqual(english_output, alternate_output,
                              f"Output in {alternate_language} is identical to English output")
    
    def test_audience_adaptation(self):
        """Test adapting output to different audience levels."""
        # Test output for technical audience
        technical_audience_params = self.default_params.copy()
        technical_audience_params["audience"] = "technical"
        
        technical_audience_output = self.text_generator.generate_text(
            reasoning=self.cot_reasoning,
            params=technical_audience_params
        )
        
        # Test output for general audience
        general_audience_params = self.default_params.copy()
        general_audience_params["audience"] = "general"
        
        general_audience_output = self.text_generator.generate_text(
            reasoning=self.cot_reasoning,
            params=general_audience_params
        )
        
        # Test output for educational audience
        educational_audience_params = self.default_params.copy()
        educational_audience_params["audience"] = "educational"
        
        educational_audience_output = self.text_generator.generate_text(
            reasoning=self.cot_reasoning,
            params=educational_audience_params
        )
        
        # Verify that outputs are different
        outputs = [technical_audience_output, general_audience_output, educational_audience_output]
        for i in range(len(outputs)):
            for j in range(i+1, len(outputs)):
                self.assertNotEqual(outputs[i], outputs[j],
                                  "Outputs for different audiences are identical")
    
    def test_format_reasoning_path(self):
        """Test formatting a chain of thought reasoning path."""
        formatted_path = self.text_generator.format_reasoning(self.cot_reasoning)
        
        # Verify that formatting was done
        self.assertIsInstance(formatted_path, str)
        self.assertGreater(len(formatted_path), 0)
        
        # Verify that all reasoning steps are included
        for step in self.cot_reasoning["path"]:
            self.assertIn(step, formatted_path)
    
    def test_format_tree_of_thought(self):
        """Test formatting a tree of thought structure."""
        formatted_tree = self.text_generator.format_reasoning(self.tot_reasoning)
        
        # Verify that formatting was done
        self.assertIsInstance(formatted_tree, str)
        self.assertGreater(len(formatted_tree), 0)
        
        # Verify that the best path is highlighted
        best_path_nodes = [self.tot_reasoning["nodes"][node_id]["text"] 
                          for node_id in self.tot_reasoning["best_path"]]
        
        for node_text in best_path_nodes:
            self.assertIn(node_text, formatted_tree)
    
    def test_format_reasoning_graph(self):
        """Test formatting a reasoning graph."""
        formatted_graph = self.text_generator.format_reasoning(self.graph_reasoning)
        
        # Verify that formatting was done
        self.assertIsInstance(formatted_graph, str)
        self.assertGreater(len(formatted_graph), 0)
        
        # Verify that key nodes are included
        for node_id, node in self.graph_reasoning["nodes"].items():
            if node["type"] in ["problem", "hypothesis", "conclusion"]:
                self.assertIn(node["text"], formatted_graph)
    
    def test_evaluate_quality(self):
        """Test evaluating the quality of generated text."""
        # Generate sample output
        output = self.text_generator.generate_text(
            reasoning=self.cot_reasoning,
            params=self.default_params
        )
        
        # Evaluate quality
        metrics = self.text_generator.evaluate_quality(
            generated=output,
            reference=self.cot_reasoning["solution"]
        )
        
        # Verify that metrics were calculated
        self.assertIsInstance(metrics, dict)
        
        # Verify that essential metrics are included
        essential_metrics = ["relevance", "fluency", "coherence", "correctness"]
        for metric in essential_metrics:
            self.assertIn(metric, metrics)
            self.assertIsInstance(metrics[metric], float)
            self.assertTrue(0 <= metrics[metric] <= 1)
    
    def test_include_references(self):
        """Test including references in the output."""
        # Test with references
        with_references_params = self.default_params.copy()
        with_references_params["include_references"] = True
        
        with_references_output = self.text_generator.generate_text(
            reasoning=self.cot_reasoning,
            params=with_references_params
        )
        
        # Verify that references are included
        reference_markers = ["reference", "references", "citation", "citations"]
        self.assertTrue(any(marker in with_references_output.lower() for marker in reference_markers),
                       "References not included in output")
    
    def test_include_examples(self):
        """Test including educational examples in the output."""
        # Test with examples
        with_examples_params = self.default_params.copy()
        with_examples_params["include_examples"] = True
        with_examples_params["style"] = "educational"
        
        with_examples_output = self.text_generator.generate_text(
            reasoning=self.cot_reasoning,
            params=with_examples_params
        )
        
        # Verify that examples are included
        example_markers = ["example", "instance", "case", "illustration"]
        self.assertTrue(any(marker in with_examples_output.lower() for marker in example_markers),
                       "Examples not included in output")
    
    def test_error_handling(self):
        """Test error handling for invalid inputs."""
        # Test with None reasoning
        with self.assertRaises(ValueError):
            self.text_generator.generate_text(reasoning=None)
        
        # Test with invalid style
        invalid_style_params = self.default_params.copy()
        invalid_style_params["style"] = "invalid_style"
        
        with self.assertRaises(ValueError):
            self.text_generator.generate_text(
                reasoning=self.cot_reasoning,
                params=invalid_style_params
            )
        
        # Test with invalid format
        invalid_format_params = self.default_params.copy()
        invalid_format_params["format"] = "invalid_format"
        
        with self.assertRaises(ValueError):
            self.text_generator.generate_text(
                reasoning=self.cot_reasoning,
                params=invalid_format_params
            )
    
    def test_templated_output(self):
        """Test generating output using templates."""
        # Test with template
        template_params = self.default_params.copy()
        template_params["template"] = "The answer to {{problem}} is {{solution}}."
        
        template_output = self.text_generator.generate_text(
            reasoning=self.cot_reasoning,
            params=template_params
        )
        
        # Verify that template was used
        expected_output = f"The answer to {self.cot_reasoning['problem']} is {self.cot_reasoning['solution']}."
        self.assertEqual(template_output, expected_output)
    
    def test_structured_output(self):
        """Test generating structured output with sections."""
        # Test with structured output
        structured_params = self.default_params.copy()
        structured_params["structured"] = True
        structured_params["sections"] = ["problem", "approach", "solution", "verification"]
        
        structured_output = self.text_generator.generate_text(
            reasoning=self.cot_reasoning,
            params=structured_params
        )
        
        # Verify that sections are included
        for section in structured_params["sections"]:
            section_marker = f"{section.capitalize()}:"
            self.assertIn(section_marker, structured_output)
    
    def test_multilingual_formatting(self):
        """Test formatting reasoning in different languages."""
        # Only test if the generator supports language adaptation
        if not hasattr(self.text_generator, "supported_languages"):
            self.skipTest("Text generator does not support language adaptation")
        
        # Test formatting in English
        english_formatted = self.text_generator.format_reasoning(
            self.cot_reasoning,
            language="English"
        )
        
        # Test formatting in another language
        alternate_language = "Spanish"
        if alternate_language in self.text_generator.supported_languages:
            alternate_formatted = self.text_generator.format_reasoning(
                self.cot_reasoning,
                language=alternate_language
            )
            
            # Formatted outputs should be different
            self.assertNotEqual(english_formatted, alternate_formatted,
                              f"Formatting in {alternate_language} is identical to English formatting")
    
    def test_educational_explanation(self):
        """Test generating educational explanations."""
        # Test with educational style
        educational_params = self.default_params.copy()
        educational_params["style"] = "educational"
        educational_params["include_examples"] = True
        educational_params["explanation_level"] = "detailed"
        
        educational_output = self.text_generator.generate_text(
            reasoning=self.cot_reasoning,
            params=educational_params
        )
        
        # Verify that educational elements are included
        educational_markers = ["example", "understand", "learn", "step", "concept"]
        self.assertTrue(any(marker in educational_output.lower() for marker in educational_markers),
                       "Educational elements not included in output")
    
    def test_technical_documentation(self):
        """Test generating technical documentation."""
        # Test with technical documentation style
        tech_doc_params = self.default_params.copy()
        tech_doc_params["style"] = "technical"
        tech_doc_params["format"] = "markdown"
        tech_doc_params["documentation_type"] = "technical"
        
        tech_doc_output = self.text_generator.generate_text(
            reasoning=self.cot_reasoning,
            params=tech_doc_params
        )
        
        # Verify that technical documentation elements are included
        tech_doc_markers = ["#", "##", "```", "`", "*", "**"]
        self.assertTrue(any(marker in tech_doc_output for marker in tech_doc_markers),
                       "Technical documentation formatting not included in output")
    
    def test_consistency_across_formats(self):
        """Test consistency of content across different formats."""
        # Generate output in different formats
        formats = ["text", "markdown", "html", "json"]
        outputs = {}
        
        for fmt in formats:
            params = self.default_params.copy()
            params["format"] = fmt
            
            outputs[fmt] = self.text_generator.generate_text(
                reasoning=self.cot_reasoning,
                params=params
            )
        
        # Extract plain text content from all formats
        plain_contents = {}
        
        # From text (already plain)
        plain_contents["text"] = outputs["text"]
        
        # From markdown (remove formatting)
        markdown_text = outputs["markdown"]
        for char in ["#", "*", "_", "`"]:
            markdown_text = markdown_text.replace(char, "")
        plain_contents["markdown"] = markdown_text
        
        # From HTML (use BeautifulSoup to extract text)
        try:
            soup = BeautifulSoup(outputs["html"], "html.parser")
            plain_contents["html"] = soup.get_text()
        except:
            plain_contents["html"] = outputs["html"]  # Fallback if BeautifulSoup fails
        
        # From JSON (extract content field or convert to string)
        try:
            json_data = json.loads(outputs["json"])
            if isinstance(json_data, dict) and "content" in json_data:
                plain_contents["json"] = json_data["content"]
            else:
                plain_contents["json"] = str(json_data)
        except:
            plain_contents["json"] = outputs["json"]  # Fallback if JSON parsing fails
        
        # Verify that key content is consistent across formats
        # Use the solution as a marker of consistent content
        solution = self.cot_reasoning["solution"]
        for fmt, content in plain_contents.items():
            self.assertIn(solution, content, f"Solution not found in {fmt} content")
    
    def test_custom_formatting_options(self):
        """Test custom formatting options for different outputs."""
        # Test with custom formatting options
        custom_format_params = {
            "max_length": 500,
            "style": "detailed",
            "format": "markdown",
            "custom_formatting": {
                "problem_prefix": "## Problem\n\n",
                "solution_prefix": "## Solution\n\n",
                "reasoning_prefix": "## Reasoning\n\n",
                "confidence_prefix": "## Confidence\n\n",
                "use_bullet_points_for_steps": True,
                "highlight_key_terms": True
            }
        }
        
        custom_output = self.text_generator.generate_text(
            reasoning=self.cot_reasoning,
            params=custom_format_params
        )
        
        # Verify that custom formatting was applied
        self.assertIn("## Problem", custom_output)
        self.assertIn("## Solution", custom_output)
        
        # If bullet points are used, check for their presence
        if custom_format_params["custom_formatting"]["use_bullet_points_for_steps"]:
            self.assertIn("* ", custom_output)
    
    def test_text_output_with_citations(self):
        """Test generating text with proper citations."""
        # Test with citations
        citation_params = self.default_params.copy()
        citation_params["include_citations"] = True
        
        citation_output = self.text_generator.generate_text(
            reasoning=self.cot_reasoning,
            params=citation_params
        )
        
        # Verify that citations are included
        citation_markers = ["[", "]", "(", ")", "cited", "according to"]
        self.assertTrue(any(marker in citation_output for marker in citation_markers),
                       "Citations not included in output")
    
    def test_personalization(self):
        """Test personalizing output based on user information."""
        # Test with user information
        user_info = {
            "name": "Alice",
            "expertise_level": "beginner",
            "preferred_style": "conversational",
            "interests": ["mathematics", "education"]
        }
        
        personalized_params = self.default_params.copy()
        personalized_params["user_info"] = user_info
        
        personalized_output = self.text_generator.generate_text(
            reasoning=self.cot_reasoning,
            params=personalized_params
        )
        
        # Verify that personalization was applied
        self.assertIn(user_info["name"], personalized_output)
    
    def test_progressive_disclosure(self):
        """Test generating output with progressive disclosure of information."""
        # Test with progressive disclosure
        progressive_params = self.default_params.copy()
        progressive_params["progressive_disclosure"] = True
        progressive_params["disclosure_levels"] = 3
        
        progressive_output = self.text_generator.generate_text(
            reasoning=self.cot_reasoning,
            params=progressive_params
        )
        
        # Verify that progressive disclosure markers are included
        disclosure_markers = ["Level 1", "Level 2", "Level 3", "Basic", "Intermediate", "Advanced"]
        self.assertTrue(any(marker in progressive_output for marker in disclosure_markers),
                       "Progressive disclosure markers not included in output")
    
    def test_localization(self):
        """Test localization of output for different regions and cultures."""
        # Test with localization
        localization_params = self.default_params.copy()
        localization_params["locale"] = "US"
        
        us_output = self.text_generator.generate_text(
            reasoning=self.cot_reasoning,
            params=localization_params
        )
        
        # Test with another locale
        localization_params["locale"] = "UK"
        
        uk_output = self.text_generator.generate_text(
            reasoning=self.cot_reasoning,
            params=localization_params
        )
        
        # Outputs should be different (but may not be significantly different)
        self.assertIsInstance(us_output, str)
        self.assertIsInstance(uk_output, str)
    
    def test_output_length_control(self):
        """Test controlling the length of generated output."""
        # Test with different length settings
        length_settings = [100, 200, 500]
        outputs = []
        
        for length in length_settings:
            length_params = self.default_params.copy()
            length_params["max_length"] = length
            
            output = self.text_generator.generate_text(
                reasoning=self.cot_reasoning,
                params=length_params
            )
            
            outputs.append(output)
        
        # Verify that output lengths generally follow the specified constraints
        # (exact matching not expected due to sentence boundaries and other factors)
        for i in range(len(length_settings) - 1):
            len_ratio = len(outputs[i+1]) / len(outputs[i])
            self.assertGreater(len_ratio, 1.0)
    
    def test_format_reasoning_custom_options(self):
        """Test formatting reasoning with custom options."""
        # Test with custom formatting options
        custom_format_options = {
            "numbering": True,
            "include_problem": True,
            "include_solution": True,
            "highlight_key_steps": True,
            "condense_similar_steps": True
        }
        
        formatted_output = self.text_generator.format_reasoning(
            self.cot_reasoning,
            custom_format_options=custom_format_options
        )
        
        # Verify that formatting was done
        self.assertIsInstance(formatted_output, str)
        self.assertGreater(len(formatted_output), 0)
        
        # If numbering is enabled, check for numbered steps
        if custom_format_options["numbering"]:
            # Look for patterns like "1.", "2.", etc.
            numbered_steps = re.findall(r'\d+\.', formatted_output)
            self.assertGreater(len(numbered_steps), 0)


class TestTextOutputGeneratorIntegration(unittest.TestCase):
    """
    Integration tests for the Text Output Generator component,
    focusing on interaction with other ULTRA components.
    """
    
    def setUp(self):
        """Set up test resources for each test."""
        # Create mock objects
        self.language_model = MockLanguageModel()
        self.knowledge_base = MockKnowledgeBase()
        
        # Create the text output generator
        self.text_generator = TextOutputGenerator(
            language_model=self.language_model,
            knowledge_base=self.knowledge_base
        )
        
        # Sample reasoning outputs
        self.cot_reasoning = MockReasoningOutput("chain_of_thought").get_data()
        
        # Default parameters
        self.default_params = {
            "max_length": 500,
            "style": "concise",
            "format": "text"
        }
    
    def test_integration_with_knowledge_base(self):
        """Test integration with the knowledge base component."""
        # Define a query that should trigger knowledge retrieval
        query = "What is the quadratic formula?"
        
        # Set up parameters to enhance with knowledge
        params = self.default_params.copy()
        params["enhance_with_knowledge"] = True
        
        # Generate output
        output = self.text_generator.generate_text_from_query(
            query=query,
            params=params
        )
        
        # Verify that output was generated
        self.assertIsInstance(output, str)
        self.assertGreater(len(output), 0)
        
        # Verify that knowledge was included
        # The quadratic formula should be mentioned
        self.assertIn("quadratic formula", output.lower())
        
        # Check for the formula itself
        formula_patterns = [
            r'x\s*=\s*\(\s*-b\s*±', 
            r'√\s*\(\s*b\s*²\s*-\s*4ac\s*\)',
            r'2a'
        ]
        
        formula_included = any(re.search(pattern, output, re.UNICODE) for pattern in formula_patterns)
        self.assertTrue(formula_included, "Quadratic formula not included in output")
    
    def test_integration_with_reasoning_component(self):
        """Test integration with a reasoning component."""
        # Create a mock reasoning function that would normally be provided by a reasoning component
        def mock_reason(query):
            if "solve" in query.lower() and "equation" in query.lower():
                return self.cot_reasoning
            else:
                return {
                    "problem": query,
                    "reasoning": ["This is a simple query that doesn't require complex reasoning."],
                    "solution": "This is the answer to your query.",
                    "confidence": 0.85
                }
        
        # Set up the text generator to use the mock reasoning function
        self.text_generator.reasoning_function = mock_reason
        
        # Test with a query that triggers reasoning
        query = "Solve the equation: 3x + 2 = 2x - 5"
        output = self.text_generator.generate_text_from_query(
            query=query,
            params=self.default_params
        )
        
        # Verify that output was generated
        self.assertIsInstance(output, str)
        self.assertGreater(len(output), 0)
        
        # Verify that the solution from reasoning is included
        self.assertIn(self.cot_reasoning["solution"], output)
    
    def test_integration_with_model_chaining(self):
        """Test integration with chained language models."""
        # Create mock chained language models
        class MockChainedModel:
            def __init__(self, specialization):
                self.specialization = specialization
            
            def generate(self, prompt, params=None):
                return f"Output from {self.specialization} model: {prompt[:30]}..."
        
        # Create specialized models
        models = {
            "math": MockChainedModel("math"),
            "science": MockChainedModel("science"),
            "general": MockChainedModel("general")
        }
        
        # Create a router function to select the appropriate model
        def model_router(query, context=None):
            if "equation" in query.lower() or "solve" in query.lower():
                return "math"
            elif "science" in query.lower() or "experiment" in query.lower():
                return "science"
            else:
                return "general"
        
        # Set up the text generator to use chained models
        self.text_generator.chained_models = models
        self.text_generator.model_router = model_router
        
        # Test with different queries
        math_query = "Solve the equation: 3x + 2 = 2x - 5"
        science_query = "Explain the scientific method"
        general_query = "Tell me about history"
        
        # Generate outputs
        math_output = self.text_generator.generate_text_from_query(
            query=math_query,
            params=self.default_params
        )
        
        science_output = self.text_generator.generate_text_from_query(
            query=science_query,
            params=self.default_params
        )
        
        general_output = self.text_generator.generate_text_from_query(
            query=general_query,
            params=self.default_params
        )
        
        # Verify that the appropriate models were used
        self.assertIn("math model", math_output)
        self.assertIn("science model", science_output)
        self.assertIn("general model", general_output)
    
    def test_integration_with_external_templates(self):
        """Test integration with external template systems."""
        # Create a mock template system
        class MockTemplateSystem:
            def __init__(self):
                self.templates = {
                    "math_solution": "Problem: {{problem}}\n\nSolution: {{solution}}\n\nConfidence: {{confidence}}",
                    "concept_explanation": "{{concept}} refers to {{definition}}. It is important in {{field}}."
                }
            
            def render(self, template_name, context):
                if template_name not in self.templates:
                    return f"Template '{template_name}' not found"
                
                template = self.templates[template_name]
                result = template
                
                for key, value in context.items():
                    placeholder = "{{" + key + "}}"
                    if placeholder in result:
                        result = result.replace(placeholder, str(value))
                
                return result
        
        # Create the template system
        template_system = MockTemplateSystem()
        
        # Set up the text generator to use the template system
        self.text_generator.template_system = template_system
        
        # Test with template-based generation
        template_params = self.default_params.copy()
        template_params["use_template"] = True
        template_params["template_name"] = "math_solution"
        
        template_output = self.text_generator.generate_text(
            reasoning=self.cot_reasoning,
            params=template_params
        )
        
        # Verify that the template was used
        self.assertIn("Problem: " + self.cot_reasoning["problem"], template_output)
        self.assertIn("Solution: " + self.cot_reasoning["solution"], template_output)
    
    def test_integration_with_feedback_system(self):
        """Test integration with a feedback system for output quality."""
        # Create a mock feedback system
        class MockFeedbackSystem:
            def __init__(self):
                self.feedback_history = []
            
            def record_feedback(self, output, context, feedback):
                self.feedback_history.append({
                    "output": output,
                    "context": context,
                    "feedback": feedback
                })
            
            def get_feedback_history(self):
                return self.feedback_history
            
            def evaluate_output(self, output, context):
                # Simple evaluation based on length and content
                score = min(1.0, len(output) / 1000) * 0.5
                
                if context.get("solution", "") in output:
                    score += 0.3
                
                if context.get("problem", "") in output:
                    score += 0.2
                
                return {
                    "score": score,
                    "suggestions": ["Make output more concise.", "Include key details."]
                }
        
        # Create the feedback system
        feedback_system = MockFeedbackSystem()
        
        # Set up the text generator to use the feedback system
        self.text_generator.feedback_system = feedback_system
        
        # Generate output
        output = self.text_generator.generate_text(
            reasoning=self.cot_reasoning,
            params=self.default_params
        )
        
        # Evaluate the output
        evaluation = feedback_system.evaluate_output(output, self.cot_reasoning)
        
        # Record feedback
        feedback_system.record_feedback(
            output=output,
            context=self.cot_reasoning,
            feedback=evaluation
        )
        
        # Verify that feedback was recorded
        feedback_history = feedback_system.get_feedback_history()
        self.assertEqual(len(feedback_history), 1)
        self.assertEqual(feedback_history[0]["output"], output)


if __name__ == "__main__":
    unittest.main()