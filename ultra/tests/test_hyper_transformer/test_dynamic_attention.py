#!/usr/bin/env python3
"""
Test suite for the dynamic attention mechanisms in the ULTRA system.

This module provides comprehensive tests for the Self-Evolving Dynamic Attention
component of the Hyper-Dimensional Transformer subsystem. It focuses on the
ability of attention mechanisms to adapt dynamically based on context, performance
feedback, and uncertainty estimates.

The test suite validates the implementation against the mathematical formulations
defined in the ULTRA architecture specification, including mask evolution,
parameter updates, and temperature adaptation.
"""

import unittest
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
import numpy as np
import math
from typing import Tuple, Dict, List, Optional, Union
from parameterized import parameterized

# Import ULTRA modules
from ultra.hyper_transformer.dynamic_attention import (
    SelfEvolvingDynamicAttention,
    DynamicMultiHeadAttention,
    AttentionMaskGenerator,
    ContextualAttentionController,
    PerformanceFeedbackProcessor,
    UncertaintyEstimator
)


class TestAttentionMaskGenerator(unittest.TestCase):
    """Test cases for the AttentionMaskGenerator component."""

    def setUp(self):
        """Set up test parameters and initialize models."""
        self.batch_size = 8
        self.seq_len = 16
        self.context_dim = 32
        self.n_heads = 4
        self.bias_scale = 0.1
        
        # Initialize the mask generator with different configurations
        self.linear_generator = AttentionMaskGenerator(
            context_dim=self.context_dim,
            seq_len=self.seq_len,
            n_heads=self.n_heads,
            bias_scale=self.bias_scale,
            generator_type='linear'
        )
        
        self.mlp_generator = AttentionMaskGenerator(
            context_dim=self.context_dim,
            seq_len=self.seq_len,
            n_heads=self.n_heads,
            bias_scale=self.bias_scale,
            generator_type='mlp',
            hidden_dim=64
        )
        
        # Test inputs
        self.context = torch.randn(self.batch_size, self.context_dim)

    def test_mask_shape(self):
        """Test that generated masks have the correct shape."""
        # Test linear generator
        linear_mask = self.linear_generator(self.context)
        expected_shape = (self.batch_size, self.n_heads, self.seq_len, self.seq_len)
        self.assertEqual(linear_mask.shape, expected_shape)
        
        # Test MLP generator
        mlp_mask = self.mlp_generator(self.context)
        self.assertEqual(mlp_mask.shape, expected_shape)

    def test_mask_values(self):
        """Test that mask values are within the expected range."""
        # Test linear generator
        linear_mask = self.linear_generator(self.context)
        self.assertTrue(torch.all(linear_mask >= -self.bias_scale))
        self.assertTrue(torch.all(linear_mask <= self.bias_scale))
        
        # Test MLP generator
        mlp_mask = self.mlp_generator(self.context)
        self.assertTrue(torch.all(mlp_mask >= -self.bias_scale))
        self.assertTrue(torch.all(mlp_mask <= self.bias_scale))

    def test_context_sensitivity(self):
        """Test that different contexts produce different masks."""
        # Generate masks for original context
        original_context = self.context.clone()
        original_linear_mask = self.linear_generator(original_context)
        original_mlp_mask = self.mlp_generator(original_context)
        
        # Generate masks for modified context
        modified_context = original_context + 0.5 * torch.randn_like(original_context)
        modified_linear_mask = self.linear_generator(modified_context)
        modified_mlp_mask = self.mlp_generator(modified_context)
        
        # Check that masks are different
        self.assertFalse(torch.allclose(original_linear_mask, modified_linear_mask, rtol=1e-3, atol=1e-3))
        self.assertFalse(torch.allclose(original_mlp_mask, modified_mlp_mask, rtol=1e-3, atol=1e-3))

    def test_deterministic_output(self):
        """Test that the same context always produces the same mask."""
        # Generate masks twice with the same context
        context = self.context.clone()
        mask1_linear = self.linear_generator(context)
        mask1_mlp = self.mlp_generator(context)
        
        mask2_linear = self.linear_generator(context)
        mask2_mlp = self.mlp_generator(context)
        
        # Check that masks are identical
        self.assertTrue(torch.allclose(mask1_linear, mask2_linear, rtol=1e-5, atol=1e-5))
        self.assertTrue(torch.allclose(mask1_mlp, mask2_mlp, rtol=1e-5, atol=1e-5))

    def test_batch_independence(self):
        """Test that each batch element produces an independent mask."""
        # Generate mask for batch
        mask = self.linear_generator(self.context)
        
        # Check that different batch elements have different masks
        for i in range(1, self.batch_size):
            self.assertFalse(torch.allclose(mask[0], mask[i], rtol=1e-3, atol=1e-3))

    def test_mask_gradients(self):
        """Test that gradients flow properly through the mask generator."""
        # Set requires_grad on context
        context = self.context.clone()
        context.requires_grad = True
        
        # Generate mask and compute dummy loss
        linear_mask = self.linear_generator(context)
        mlp_mask = self.mlp_generator(context)
        
        linear_loss = linear_mask.sum()
        mlp_loss = mlp_mask.sum()
        
        # Backpropagate
        linear_loss.backward(retain_graph=True)
        self.assertIsNotNone(context.grad)
        
        # Reset gradients
        context.grad = None
        
        # Backpropagate for MLP
        mlp_loss.backward()
        self.assertIsNotNone(context.grad)


class TestContextualAttentionController(unittest.TestCase):
    """Test cases for the ContextualAttentionController component."""

    def setUp(self):
        """Set up test parameters and initialize models."""
        self.batch_size = 8
        self.context_dim = 32
        self.n_heads = 4
        self.d_model = 64
        self.seq_len = 16
        
        # Initialize the controller
        self.controller = ContextualAttentionController(
            context_dim=self.context_dim,
            d_model=self.d_model,
            n_heads=self.n_heads,
            seq_len=self.seq_len
        )
        
        # Test inputs
        self.context = torch.randn(self.batch_size, self.context_dim)
        self.performance = torch.rand(self.batch_size, 1)
        self.head_weights = torch.randn(self.batch_size, self.n_heads)
        self.temperature = torch.ones(self.batch_size, 1)
        self.uncertainty = torch.rand(self.batch_size, 1)

    def test_head_weight_adjustment(self):
        """Test that head weights are properly adjusted based on context."""
        # Get initial head weights
        initial_weights = self.head_weights.clone()
        
        # Adjust weights
        adjusted_weights = self.controller.adjust_head_weights(
            self.head_weights, self.context
        )
        
        # Check shape
        self.assertEqual(adjusted_weights.shape, initial_weights.shape)
        
        # Check that weights have been adjusted
        self.assertFalse(torch.allclose(initial_weights, adjusted_weights, rtol=1e-3, atol=1e-3))

    def test_temperature_adjustment(self):
        """Test that temperature is properly adjusted based on uncertainty."""
        # Get initial temperature
        initial_temp = self.temperature.clone()
        
        # Adjust temperature
        adjusted_temp = self.controller.adjust_temperature(
            self.temperature, self.uncertainty
        )
        
        # Check shape
        self.assertEqual(adjusted_temp.shape, initial_temp.shape)
        
        # Check that temperature has been adjusted
        self.assertFalse(torch.allclose(initial_temp, adjusted_temp, rtol=1e-3, atol=1e-3))
        
        # Check that temperature is always positive
        self.assertTrue(torch.all(adjusted_temp > 0))
        
        # Test that higher uncertainty leads to higher temperature (more exploration)
        high_uncertainty = torch.ones_like(self.uncertainty)
        low_uncertainty = torch.zeros_like(self.uncertainty)
        
        high_temp = self.controller.adjust_temperature(self.temperature, high_uncertainty)
        low_temp = self.controller.adjust_temperature(self.temperature, low_uncertainty)
        
        self.assertTrue(torch.all(high_temp >= low_temp))

    def test_mask_evolution(self):
        """Test that attention masks evolve based on context and performance."""
        # Create an initial mask
        initial_mask = torch.zeros(
            self.batch_size, self.n_heads, self.seq_len, self.seq_len
        )
        
        # Evolve mask
        evolved_mask = self.controller.evolve_mask(
            initial_mask, self.context, self.performance
        )
        
        # Check shape
        self.assertEqual(evolved_mask.shape, initial_mask.shape)
        
        # Check that mask has evolved
        self.assertFalse(torch.allclose(initial_mask, evolved_mask, rtol=1e-3, atol=1e-3))
        
        # Test that high performance leads to less change
        high_performance = torch.ones_like(self.performance)
        low_performance = torch.zeros_like(self.performance)
        
        high_perf_mask = self.controller.evolve_mask(
            initial_mask.clone(), self.context, high_performance
        )
        low_perf_mask = self.controller.evolve_mask(
            initial_mask.clone(), self.context, low_performance
        )
        
        # The difference between initial and evolved should be greater for low performance
        high_diff = torch.abs(high_perf_mask - initial_mask).mean()
        low_diff = torch.abs(low_perf_mask - initial_mask).mean()
        
        self.assertGreater(low_diff, high_diff)

    def test_attention_control_integration(self):
        """Test the integrated attention control functionality."""
        # Create mock attention parameters
        attention_params = {
            'mask': torch.zeros(self.batch_size, self.n_heads, self.seq_len, self.seq_len),
            'head_weights': torch.ones(self.batch_size, self.n_heads) / self.n_heads,
            'temperature': torch.ones(self.batch_size, 1)
        }
        
        # Apply controller
        updated_params = self.controller(
            attention_params, 
            self.context, 
            self.performance, 
            self.uncertainty
        )
        
        # Check that all parameters have been updated
        for param_name, param_value in updated_params.items():
            self.assertFalse(
                torch.allclose(attention_params[param_name], param_value, rtol=1e-3, atol=1e-3),
                f"Parameter {param_name} was not updated"
            )


class TestPerformanceFeedbackProcessor(unittest.TestCase):
    """Test cases for the PerformanceFeedbackProcessor component."""

    def setUp(self):
        """Set up test parameters and initialize models."""
        self.batch_size = 8
        self.seq_len = 16
        self.n_heads = 4
        self.d_model = 64
        self.history_length = 5
        
        # Initialize the processor
        self.processor = PerformanceFeedbackProcessor(
            d_model=self.d_model,
            n_heads=self.n_heads,
            history_length=self.history_length
        )
        
        # Test inputs
        self.attention_weights = torch.softmax(
            torch.randn(self.batch_size, self.n_heads, self.seq_len, self.seq_len),
            dim=-1
        )
        self.performance = torch.rand(self.batch_size, 1)
        
        # Initialize history
        self.attention_history = [
            torch.softmax(
                torch.randn(self.batch_size, self.n_heads, self.seq_len, self.seq_len),
                dim=-1
            )
            for _ in range(self.history_length)
        ]
        self.performance_history = [
            torch.rand(self.batch_size, 1)
            for _ in range(self.history_length)
        ]

    def test_history_update(self):
        """Test that history is properly updated."""
        # Update history
        new_attention_history, new_performance_history = self.processor.update_history(
            self.attention_weights,
            self.performance,
            self.attention_history,
            self.performance_history
        )
        
        # Check lengths
        self.assertEqual(len(new_attention_history), self.history_length)
        self.assertEqual(len(new_performance_history), self.history_length)
        
        # Check that newest element is at the front
        self.assertTrue(torch.allclose(new_attention_history[0], self.attention_weights))
        self.assertTrue(torch.allclose(new_performance_history[0], self.performance))
        
        # Check that oldest element is discarded
        for i in range(1, self.history_length):
            self.assertTrue(torch.allclose(new_attention_history[i], self.attention_history[i-1]))
            self.assertTrue(torch.allclose(new_performance_history[i], self.performance_history[i-1]))

    def test_compute_correlation(self):
        """Test computation of correlation between attention and performance."""
        # Compute correlation
        correlation = self.processor.compute_correlation(
            self.attention_history, self.performance_history
        )
        
        # Check shape
        expected_shape = (self.batch_size, self.n_heads, self.seq_len, self.seq_len)
        self.assertEqual(correlation.shape, expected_shape)
        
        # Generate test data with known correlation
        pos_correlation_data = []
        pos_performance_data = []
        
        # Create data where high attention corresponds to high performance
        for i in range(self.history_length):
            perf = torch.rand(self.batch_size, 1)
            # Create attention that correlates with performance
            attn = torch.zeros(self.batch_size, self.n_heads, self.seq_len, self.seq_len)
            for b in range(self.batch_size):
                # Set attention based on performance (higher perf = higher attention on diagonal)
                diagonal_val = 0.5 + 0.5 * perf[b, 0]
                off_diagonal_val = (1 - diagonal_val) / (self.seq_len - 1)
                for h in range(self.n_heads):
                    for s in range(self.seq_len):
                        for t in range(self.seq_len):
                            if s == t:
                                attn[b, h, s, t] = diagonal_val
                            else:
                                attn[b, h, s, t] = off_diagonal_val
            
            pos_correlation_data.append(attn)
            pos_performance_data.append(perf)
        
        # Compute correlation for test data
        test_correlation = self.processor.compute_correlation(
            pos_correlation_data, pos_performance_data
        )
        
        # Check that diagonal elements have positive correlation
        for b in range(self.batch_size):
            for h in range(self.n_heads):
                for s in range(self.seq_len):
                    self.assertGreater(test_correlation[b, h, s, s].item(), 0)

    def test_compute_gradient(self):
        """Test computation of gradient from correlation."""
        # Compute correlation
        correlation = self.processor.compute_correlation(
            self.attention_history, self.performance_history
        )
        
        # Compute gradient
        gradient = self.processor.compute_gradient(correlation, self.attention_weights)
        
        # Check shape
        expected_shape = (self.batch_size, self.n_heads, self.seq_len, self.seq_len)
        self.assertEqual(gradient.shape, expected_shape)
        
        # Create test data with perfect correlation
        perfect_correlation = torch.ones(self.batch_size, self.n_heads, self.seq_len, self.seq_len)
        test_gradient = self.processor.compute_gradient(perfect_correlation, self.attention_weights)
        
        # With perfect positive correlation, gradient should be positive
        self.assertTrue(torch.all(test_gradient >= 0))
        
        # Create test data with perfect negative correlation
        neg_correlation = -torch.ones(self.batch_size, self.n_heads, self.seq_len, self.seq_len)
        neg_gradient = self.processor.compute_gradient(neg_correlation, self.attention_weights)
        
        # With perfect negative correlation, gradient should be negative
        self.assertTrue(torch.all(neg_gradient <= 0))

    def test_process_feedback(self):
        """Test the end-to-end feedback processing."""
        # Process feedback
        gradient, updated_histories = self.processor.process_feedback(
            self.attention_weights,
            self.performance,
            self.attention_history,
            self.performance_history
        )
        
        # Check gradient shape
        expected_shape = (self.batch_size, self.n_heads, self.seq_len, self.seq_len)
        self.assertEqual(gradient.shape, expected_shape)
        
        # Check history lengths
        self.assertEqual(len(updated_histories[0]), self.history_length)
        self.assertEqual(len(updated_histories[1]), self.history_length)


class TestUncertaintyEstimator(unittest.TestCase):
    """Test cases for the UncertaintyEstimator component."""

    def setUp(self):
        """Set up test parameters and initialize models."""
        self.batch_size = 8
        self.seq_len = 16
        self.n_heads = 4
        self.d_model = 64
        self.hidden_dim = 128
        
        # Initialize the estimator
        self.estimator = UncertaintyEstimator(
            d_model=self.d_model,
            hidden_dim=self.hidden_dim
        )
        
        # Test inputs
        self.hidden_states = torch.randn(self.batch_size, self.seq_len, self.d_model)
        self.logits = torch.randn(self.batch_size, self.seq_len, 1000)  # Vocab size of 1000
        self.attn_weights = torch.softmax(
            torch.randn(self.batch_size, self.n_heads, self.seq_len, self.seq_len),
            dim=-1
        )

    def test_predictive_uncertainty(self):
        """Test computation of predictive uncertainty from logits."""
        # Compute uncertainty
        uncertainty = self.estimator.compute_predictive_uncertainty(self.logits)
        
        # Check shape
        self.assertEqual(uncertainty.shape, (self.batch_size, 1))
        
        # All uncertainty values should be between 0 and 1
        self.assertTrue(torch.all(uncertainty >= 0))
        self.assertTrue(torch.all(uncertainty <= 1))
        
        # Test with deterministic logits
        deterministic_logits = torch.zeros_like(self.logits)
        deterministic_logits[:, :, 0] = 100  # Very high probability for token 0
        
        deterministic_uncertainty = self.estimator.compute_predictive_uncertainty(deterministic_logits)
        
        # Check that uncertainty is low for deterministic predictions
        self.assertTrue(torch.all(deterministic_uncertainty < 0.1))
        
        # Test with uniform logits
        uniform_logits = torch.zeros_like(self.logits)
        
        uniform_uncertainty = self.estimator.compute_predictive_uncertainty(uniform_logits)
        
        # Check that uncertainty is high for uniform predictions
        self.assertTrue(torch.all(uniform_uncertainty > 0.9))

    def test_representation_uncertainty(self):
        """Test computation of representation uncertainty from hidden states."""
        # Compute uncertainty
        uncertainty = self.estimator.compute_representation_uncertainty(self.hidden_states)
        
        # Check shape
        self.assertEqual(uncertainty.shape, (self.batch_size, 1))
        
        # All uncertainty values should be non-negative
        self.assertTrue(torch.all(uncertainty >= 0))
        
        # Test with uniform hidden states
        uniform_hidden = torch.ones_like(self.hidden_states)
        uniform_uncertainty = self.estimator.compute_representation_uncertainty(uniform_hidden)
        
        # Check that uncertainty is low for uniform representations
        self.assertTrue(torch.all(uniform_uncertainty < uncertainty.mean()))

    def test_attention_uncertainty(self):
        """Test computation of uncertainty from attention weights."""
        # Compute uncertainty
        uncertainty = self.estimator.compute_attention_uncertainty(self.attn_weights)
        
        # Check shape
        self.assertEqual(uncertainty.shape, (self.batch_size, 1))
        
        # All uncertainty values should be between 0 and 1
        self.assertTrue(torch.all(uncertainty >= 0))
        self.assertTrue(torch.all(uncertainty <= 1))
        
        # Test with deterministic attention (all weight on one token)
        deterministic_attn = torch.zeros_like(self.attn_weights)
        deterministic_attn[:, :, :, 0] = 1.0  # All attention on first token
        
        deterministic_uncertainty = self.estimator.compute_attention_uncertainty(deterministic_attn)
        
        # Check that uncertainty is low for deterministic attention
        self.assertTrue(torch.all(deterministic_uncertainty < 0.1))
        
        # Test with uniform attention
        uniform_attn = torch.ones_like(self.attn_weights) / self.seq_len
        
        uniform_uncertainty = self.estimator.compute_attention_uncertainty(uniform_attn)
        
        # Check that uncertainty is high for uniform attention
        self.assertTrue(torch.all(uniform_uncertainty > 0.9))

    def test_estimate_uncertainty(self):
        """Test the end-to-end uncertainty estimation."""
        # Estimate uncertainty
        uncertainty = self.estimator.estimate_uncertainty(
            hidden_states=self.hidden_states,
            logits=self.logits,
            attention_weights=self.attn_weights
        )
        
        # Check shape
        self.assertEqual(uncertainty.shape, (self.batch_size, 1))
        
        # All uncertainty values should be between 0 and 1
        self.assertTrue(torch.all(uncertainty >= 0))
        self.assertTrue(torch.all(uncertainty <= 1))


class TestDynamicMultiHeadAttention(unittest.TestCase):
    """Test cases for the DynamicMultiHeadAttention component."""

    def setUp(self):
        """Set up test parameters and initialize models."""
        self.batch_size = 8
        self.seq_len = 16
        self.d_model = 64
        self.n_heads = 4
        self.d_k = self.d_model // self.n_heads
        self.dropout = 0.1
        
        # Initialize the attention module
        self.attention = DynamicMultiHeadAttention(
            d_model=self.d_model,
            n_heads=self.n_heads,
            dropout=self.dropout
        )
        
        # Test inputs
        self.query = torch.randn(self.batch_size, self.seq_len, self.d_model)
        self.key = torch.randn(self.batch_size, self.seq_len, self.d_model)
        self.value = torch.randn(self.batch_size, self.seq_len, self.d_model)
        
        # Create a mask for masked attention
        self.mask = torch.ones(self.batch_size, 1, self.seq_len, self.seq_len)
        
        # Contextual parameters
        self.head_weights = torch.softmax(torch.randn(self.batch_size, self.n_heads), dim=1)
        self.temperature = torch.abs(torch.randn(self.batch_size, 1)) + 0.5  # Ensure positive

    def test_forward_shape(self):
        """Test that the attention output has the correct shape."""
        # Forward pass
        output, attn_weights = self.attention(
            self.query, self.key, self.value
        )
        
        # Check output shape
        self.assertEqual(output.shape, (self.batch_size, self.seq_len, self.d_model))
        
        # Check attention weights shape
        expected_weights_shape = (self.batch_size, self.n_heads, self.seq_len, self.seq_len)
        self.assertEqual(attn_weights.shape, expected_weights_shape)

    def test_attention_with_mask(self):
        """Test attention with an explicit mask."""
        # Create a binary mask (1 = keep, 0 = mask)
        explicit_mask = torch.ones(self.batch_size, 1, self.seq_len, self.seq_len)
        explicit_mask[:, :, :, self.seq_len//2:] = 0  # Mask out second half of sequence
        
        # Compute attention with mask
        output, attention_weights = self.attention(
            self.query, self.key, self.value, mask=explicit_mask
        )
        
        # Check output shape
        self.assertEqual(output.shape, (self.batch_size, self.seq_len, self.d_model))
        
        # Check that masked positions have zero attention weights
        masked_weights = attention_weights[:, :, :, self.seq_len//2:]
        self.assertTrue(torch.allclose(masked_weights, torch.zeros_like(masked_weights), rtol=1e-3, atol=1e-3))

    def test_dynamic_head_weighting(self):
        """Test dynamic weighting of attention heads."""
        # Compute attention with default uniform head weights
        output1, _ = self.attention(self.query, self.key, self.value)
        
        # Compute attention with custom head weights
        output2, _ = self.attention(
            self.query, self.key, self.value,
            head_weights=self.head_weights
        )
        
        # Check that outputs are different
        self.assertFalse(torch.allclose(output1, output2, rtol=1e-3, atol=1e-3))
        
        # Test extreme case: all weight on one head
        extreme_weights = torch.zeros_like(self.head_weights)
        extreme_weights[:, 0] = 1.0
        
        output3, _ = self.attention(
            self.query, self.key, self.value,
            head_weights=extreme_weights
        )
        
        # This should be very different from uniform weighting
        self.assertFalse(torch.allclose(output1, output3, rtol=1e-2, atol=1e-2))

    def test_temperature_scaling(self):
        """Test the effect of temperature on attention weights."""
        # Compute attention with default temperature
        _, attn_weights1 = self.attention(self.query, self.key, self.value)
        
        # Compute attention with high temperature (softer)
        high_temp = torch.ones(self.batch_size, 1) * 10.0
        _, attn_weights_high = self.attention(
            self.query, self.key, self.value,
            temperature=high_temp
        )
        
        # Compute attention with low temperature (sharper)
        low_temp = torch.ones(self.batch_size, 1) * 0.1
        _, attn_weights_low = self.attention(
            self.query, self.key, self.value,
            temperature=low_temp
        )
        
        # High temperature should lead to more uniform attention
        high_entropy = -(attn_weights_high * torch.log(attn_weights_high + 1e-10)).sum(dim=-1).mean()
        default_entropy = -(attn_weights1 * torch.log(attn_weights1 + 1e-10)).sum(dim=-1).mean()
        low_entropy = -(attn_weights_low * torch.log(attn_weights_low + 1e-10)).sum(dim=-1).mean()
        
        self.assertGreater(high_entropy, default_entropy)
        self.assertGreater(default_entropy, low_entropy)

    def test_bias_addition(self):
        """Test addition of attention bias."""
        # Create a bias tensor
        bias = torch.randn(self.batch_size, self.n_heads, self.seq_len, self.seq_len) * 0.1
        
        # Compute attention with bias
        output_with_bias, attn_weights_with_bias = self.attention(
            self.query, self.key, self.value,
            bias=bias
        )
        
        # Compute attention without bias
        output_without_bias, attn_weights_without_bias = self.attention(
            self.query, self.key, self.value
        )
        
        # Check that outputs are different
        self.assertFalse(torch.allclose(
            output_with_bias, output_without_bias, rtol=1e-3, atol=1e-3
        ))
        
        # Check that attention weights are different
        self.assertFalse(torch.allclose(
            attn_weights_with_bias, attn_weights_without_bias, rtol=1e-3, atol=1e-3
        ))

    def test_causal_attention(self):
        """Test causal attention masking."""
        # Create causal mask
        causal_mask = torch.triu(
            torch.ones(self.seq_len, self.seq_len), diagonal=1
        ).bool().unsqueeze(0).unsqueeze(0)
        
        # Compute causal attention
        output, attention_weights = self.attention(
            self.query, self.key, self.value,
            mask=causal_mask
        )
        
        # Check output shape
        self.assertEqual(output.shape, (self.batch_size, self.seq_len, self.d_model))
        
        # Check that future positions have zero attention weights
        for i in range(self.seq_len - 1):
            future_attn = attention_weights[:, :, i, i+1:]
            self.assertTrue(torch.allclose(future_attn, torch.zeros_like(future_attn), rtol=1e-3, atol=1e-3))

    def test_attention_probabilities(self):
        """Test that attention weights form a valid probability distribution."""
        # Compute attention
        _, attention_weights = self.attention(
            self.query, self.key, self.value
        )
        
        # Check that weights are non-negative
        self.assertTrue(torch.all(attention_weights >= 0))
        
        # Check that weights sum to 1 along the right dimension
        attn_sum = attention_weights.sum(dim=-1)
        self.assertTrue(torch.allclose(attn_sum, torch.ones_like(attn_sum), rtol=1e-5, atol=1e-5))


class TestSelfEvolvingDynamicAttention(unittest.TestCase):
    """Test cases for the SelfEvolvingDynamicAttention component."""

    def setUp(self):
        """Set up test parameters and initialize models."""
        self.batch_size = 8
        self.seq_len = 16
        self.d_model = 64
        self.n_heads = 4
        self.d_k = self.d_model // self.n_heads
        self.context_dim = 32
        self.dropout = 0.1
        
        # Initialize the attention module
        self.attention = SelfEvolvingDynamicAttention(
            d_model=self.d_model,
            n_heads=self.n_heads,
            context_dim=self.context_dim,
            dropout=self.dropout
        )
        
        # Test inputs
        self.query = torch.randn(self.batch_size, self.seq_len, self.d_model)
        self.key = torch.randn(self.batch_size, self.seq_len, self.d_model)
        self.value = torch.randn(self.batch_size, self.seq_len, self.d_model)
        self.context = torch.randn(self.batch_size, self.context_dim)
        self.performance = torch.rand(self.batch_size, 1)
        
        # Create test logits and hidden states for uncertainty estimation
        self.logits = torch.randn(self.batch_size, self.seq_len, 1000)  # Vocab size of 1000
        self.hidden_states = torch.randn(self.batch_size, self.seq_len, self.d_model)

    def test_initialization(self):
        """Test proper initialization of the attention module."""
        # Check that context projection exists
        self.assertIsInstance(self.attention.context_projection, nn.Linear)
        self.assertEqual(self.attention.context_projection.in_features, self.context_dim)
        
        # Check that mask generator exists
        self.assertIsInstance(self.attention.mask_generator, AttentionMaskGenerator)
        
        # Check that controller exists
        self.assertIsInstance(self.attention.controller, ContextualAttentionController)
        
        # Check that multi-head attention exists
        self.assertIsInstance(self.attention.attention, DynamicMultiHeadAttention)
        
        # Check that performance processor exists
        self.assertIsInstance(self.attention.feedback_processor, PerformanceFeedbackProcessor)
        
        # Check that uncertainty estimator exists
        self.assertIsInstance(self.attention.uncertainty_estimator, UncertaintyEstimator)

    def test_forward_with_context(self):
        """Test forward pass with context information."""
        # Forward pass without context
        output1, _ = self.attention(self.query, self.key, self.value)
        
        # Forward pass with context
        output2, _ = self.attention(
            self.query, self.key, self.value,
            context=self.context
        )
        
        # Check shapes
        self.assertEqual(output1.shape, (self.batch_size, self.seq_len, self.d_model))
        self.assertEqual(output2.shape, (self.batch_size, self.seq_len, self.d_model))
        
        # Check that outputs are different
        self.assertFalse(torch.allclose(output1, output2, rtol=1e-3, atol=1e-3))

    def test_evolve_attention(self):
        """Test attention evolution based on context and performance."""
        # Initial forward pass
        _, initial_attn = self.attention(
            self.query, self.key, self.value,
            context=self.context
        )
        
        # Store initial mask
        initial_mask = self.attention.current_mask.clone() if hasattr(self.attention, 'current_mask') else None
        
        # Evolve attention
        self.attention.evolve_attention(
            self.context, 
            self.performance,
            self.logits,
            self.hidden_states
        )
        
        # Forward pass after evolution
        _, evolved_attn = self.attention(
            self.query, self.key, self.value,
            context=self.context
        )
        
        # Check that attention weights have changed
        self.assertFalse(torch.allclose(initial_attn, evolved_attn, rtol=1e-3, atol=1e-3))
        
        # Check that mask has been updated
        if initial_mask is not None:
            evolved_mask = self.attention.current_mask
            self.assertFalse(torch.allclose(initial_mask, evolved_mask, rtol=1e-3, atol=1e-3))

    def test_adaptation_to_high_performance(self):
        """Test that attention adapts differently based on performance levels."""
        # Store initial state
        self.attention.eval()  # Set to eval mode to avoid randomness
        with torch.no_grad():
            _, initial_attn = self.attention(
                self.query, self.key, self.value,
                context=self.context
            )
            initial_mask = self.attention.current_mask.clone() if hasattr(self.attention, 'current_mask') else None
        
        # Create a copy for testing with high performance
        high_perf_attention = SelfEvolvingDynamicAttention(
            d_model=self.d_model,
            n_heads=self.n_heads,
            context_dim=self.context_dim,
            dropout=self.dropout
        )
        high_perf_attention.load_state_dict(self.attention.state_dict())
        
        # Create a copy for testing with low performance
        low_perf_attention = SelfEvolvingDynamicAttention(
            d_model=self.d_model,
            n_heads=self.n_heads,
            context_dim=self.context_dim,
            dropout=self.dropout
        )
        low_perf_attention.load_state_dict(self.attention.state_dict())
        
        # Evolve with high performance
        high_performance = torch.ones_like(self.performance)
        high_perf_attention.evolve_attention(
            self.context, 
            high_performance,
            self.logits,
            self.hidden_states
        )
        
        # Evolve with low performance
        low_performance = torch.zeros_like(self.performance)
        low_perf_attention.evolve_attention(
            self.context, 
            low_performance,
            self.logits,
            self.hidden_states
        )
        
        # Forward passes after evolution
        with torch.no_grad():
            _, high_perf_attn = high_perf_attention(
                self.query, self.key, self.value,
                context=self.context
            )
            
            _, low_perf_attn = low_perf_attention(
                self.query, self.key, self.value,
                context=self.context
            )
        
        # Check that high and low performance lead to different adaptations
        self.assertFalse(torch.allclose(high_perf_attn, low_perf_attn, rtol=1e-3, atol=1e-3))
        
        # Low performance should lead to more change from the initial state
        high_diff = torch.abs(high_perf_attn - initial_attn).mean()
        low_diff = torch.abs(low_perf_attn - initial_attn).mean()
        
        self.assertGreater(low_diff, high_diff)

    def test_training_mode(self):
        """Test behavior during training vs. evaluation."""
        # Store initial state
        initial_state_dict = {k: v.clone() for k, v in self.attention.state_dict().items() 
                             if isinstance(v, torch.Tensor)}
        
        # Set to training mode
        self.attention.train()
        
        # Forward pass and backward
        output = self.attention(
            self.query, self.key, self.value,
            context=self.context
        )[0]
        loss = output.sum()
        loss.backward()
        
        # Check that gradients are flowing
        for name, param in self.attention.named_parameters():
            if param.requires_grad:
                self.assertIsNotNone(param.grad, f"Parameter {name} has no gradient")
        
        # Apply an optimization step
        with torch.no_grad():
            for param in self.attention.parameters():
                if param.grad is not None:
                    param -= 0.01 * param.grad
        
        # Check that parameters have been updated
        for name, initial_param in initial_state_dict.items():
            current_param = self.attention.state_dict()[name]
            if current_param.requires_grad:
                self.assertFalse(torch.allclose(initial_param, current_param, rtol=1e-3, atol=1e-3),
                                f"Parameter {name} was not updated")
    
    def test_uncertainty_estimation(self):
        """Test uncertainty estimation capabilities."""
        # Compute attention with uncertainty estimation
        _, _ = self.attention(
            self.query, self.key, self.value,
            context=self.context,
            return_attention=True,
            hidden_states=self.hidden_states,
            logits=self.logits
        )
        
        # Get uncertainty estimate
        uncertainty = self.attention.last_uncertainty
        
        # Check shape
        self.assertEqual(uncertainty.shape, (self.batch_size, 1))
        
        # Check that uncertainty is valid
        self.assertTrue(torch.all(uncertainty >= 0))
        self.assertTrue(torch.all(uncertainty <= 1))
        
        # Test that changing the hidden states affects uncertainty
        modified_hidden_states = self.hidden_states + 0.5 * torch.randn_like(self.hidden_states)
        _, _ = self.attention(
            self.query, self.key, self.value,
            context=self.context,
            return_attention=True,
            hidden_states=modified_hidden_states,
            logits=self.logits
        )
        
        modified_uncertainty = self.attention.last_uncertainty
        
        # Check that uncertainty has changed
        self.assertFalse(torch.allclose(uncertainty, modified_uncertainty, rtol=1e-3, atol=1e-3))

    @parameterized.expand([
        ("default", None, None, None),
        ("custom_head_weights", torch.softmax(torch.randn(8, 4), dim=1), None, None),
        ("custom_temperature", None, torch.ones(8, 1) * 0.5, None),
        ("custom_bias", None, None, torch.randn(8, 4, 16, 16) * 0.1),
        ("all_custom", 
         torch.softmax(torch.randn(8, 4), dim=1), 
         torch.ones(8, 1) * 0.5, 
         torch.randn(8, 4, 16, 16) * 0.1)
    ])
    def test_parameter_customization(self, name, head_weights, temperature, bias):
        """Test customization of attention parameters."""
        # Compute attention with custom parameters
        output, attn_weights = self.attention(
            self.query, self.key, self.value,
            context=self.context,
            head_weights=head_weights,
            temperature=temperature,
            bias=bias
        )
        
        # Check shapes
        self.assertEqual(output.shape, (self.batch_size, self.seq_len, self.d_model))
        self.assertEqual(
            attn_weights.shape, 
            (self.batch_size, self.n_heads, self.seq_len, self.seq_len)
        )


if __name__ == "__main__":
    unittest.main()