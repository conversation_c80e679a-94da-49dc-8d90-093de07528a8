#!/usr/bin/env python3
"""
Test suite for the Recursive Transformer component of the ULTRA system.

This module provides comprehensive tests for the Recursive Transformer, which
extends standard transformer architectures with dynamic recursion capabilities.
The Recursive Transformer allows layers to call themselves recursively for
complex inputs, creating a form of "deep thinking" with a learned halting mechanism.

The test suite validates the implementation against the mathematical formulations
defined in the ULTRA architecture specification, including the recursion mechanism,
halting probability computation, and weighted output combination.
"""

import unittest
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
import numpy as np
import math
from typing import Dict, List, Optional, Tuple, Union
from parameterized import parameterized

# Import ULTRA modules
from ultra.hyper_transformer.recursive_transformer import (
    RecursiveTransformer,
    RecursiveTransformerLayer,
    RecursiveTransformerEncoder,
    RecursiveTransformerDecoder,
    PonderNetHalting,
    ACTHalting,
    RecursionController,
    WeightedOutputCombiner
)


class TestRecursionController(unittest.TestCase):
    """Test cases for the RecursionController component."""
    
    def setUp(self):
        """Set up test parameters and initialize components."""
        self.batch_size = 8
        self.seq_len = 16
        self.d_model = 64
        self.max_recursion = 5
        
        # Initialize recursion controller with different halting mechanisms
        self.ponder_controller = RecursionController(
            d_model=self.d_model,
            max_recursion=self.max_recursion,
            halting_mechanism="ponder",
            epsilon=0.01
        )
        
        self.act_controller = RecursionController(
            d_model=self.d_model,
            max_recursion=self.max_recursion,
            halting_mechanism="act",
            epsilon=0.01
        )
        
        # Create test inputs
        self.x = torch.randn(self.seq_len, self.batch_size, self.d_model)  # [S, B, E]

    def test_initialization(self):
        """Test that the controller is properly initialized."""
        # Check halting mechanisms
        self.assertIsInstance(self.ponder_controller.halting_mechanism, PonderNetHalting)
        self.assertIsInstance(self.act_controller.halting_mechanism, ACTHalting)
        
        # Check that max recursion is set correctly
        self.assertEqual(self.ponder_controller.max_recursion, self.max_recursion)
        self.assertEqual(self.act_controller.max_recursion, self.max_recursion)

    def test_halting_probabilities_shape(self):
        """Test that halting probabilities have the correct shape."""
        # Compute halting probabilities
        ponder_probs = self.ponder_controller.compute_halting_probabilities(self.x)
        act_probs = self.act_controller.compute_halting_probabilities(self.x)
        
        # Check shapes
        self.assertEqual(ponder_probs.shape, (self.batch_size,))
        self.assertEqual(act_probs.shape, (self.batch_size,))
        
        # Check that probabilities are valid
        self.assertTrue(torch.all(ponder_probs >= 0))
        self.assertTrue(torch.all(ponder_probs <= 1))
        self.assertTrue(torch.all(act_probs >= 0))
        self.assertTrue(torch.all(act_probs <= 1))

    def test_determine_recursion_depth(self):
        """Test that recursion depth is determined correctly."""
        # Initialize dummy halting probabilities
        step_probs = torch.zeros(self.max_recursion, self.batch_size)
        
        # First test: no high probabilities, should use max recursion
        depths = self.ponder_controller.determine_recursion_depth(step_probs)
        self.assertTrue(torch.all(depths == self.max_recursion))
        
        # Second test: early halting
        step_probs[1, :4] = 0.95  # High probability at step 2 for first half of batch
        step_probs[3, 4:] = 0.95  # High probability at step 4 for second half of batch
        
        depths = self.ponder_controller.determine_recursion_depth(step_probs)
        self.assertTrue(torch.all(depths[:4] == 2))  # +1 because steps start at 0
        self.assertTrue(torch.all(depths[4:] == 4))  # +1 because steps start at 0

    def test_cumulative_halting_probability(self):
        """Test computation of cumulative halting probability."""
        # Create dummy step probabilities
        step_probs = torch.zeros(self.max_recursion, self.batch_size)
        step_probs[0, :] = 0.3
        step_probs[1, :] = 0.5
        step_probs[2, :] = 0.1
        step_probs[3, :] = 0.05
        step_probs[4, :] = 0.05
        
        # Compute cumulative probabilities
        cum_probs = self.ponder_controller.compute_cumulative_probability(step_probs)
        
        # Check shape
        self.assertEqual(cum_probs.shape, (self.max_recursion, self.batch_size))
        
        # Check values (cumulative sum)
        expected_cum_probs = torch.cumsum(step_probs, dim=0)
        self.assertTrue(torch.allclose(cum_probs, expected_cum_probs, rtol=1e-5, atol=1e-5))
        
        # Check that ACT halting uses the same cumulative probability computation
        act_cum_probs = self.act_controller.compute_cumulative_probability(step_probs)
        self.assertTrue(torch.allclose(act_cum_probs, expected_cum_probs, rtol=1e-5, atol=1e-5))

    def test_compute_recursion_weights(self):
        """Test computation of weights for combining intermediate outputs."""
        # Create dummy step probabilities
        step_probs = torch.zeros(self.max_recursion, self.batch_size)
        step_probs[0, :] = 0.3
        step_probs[1, :] = 0.5
        step_probs[2, :] = 0.1
        step_probs[3, :] = 0.05
        step_probs[4, :] = 0.05
        
        # Compute weights
        weights = self.ponder_controller.compute_recursion_weights(step_probs)
        
        # Check shape
        self.assertEqual(weights.shape, (self.max_recursion, self.batch_size))
        
        # Check that weights sum to 1
        weight_sums = weights.sum(dim=0)
        self.assertTrue(torch.allclose(weight_sums, torch.ones_like(weight_sums), rtol=1e-5, atol=1e-5))
        
        # For PonderNet, weights should be equal to step probabilities
        self.assertTrue(torch.allclose(weights, step_probs, rtol=1e-5, atol=1e-5))
        
        # For ACT, computation is different
        act_weights = self.act_controller.compute_recursion_weights(step_probs)
        cum_probs = torch.cumsum(step_probs, dim=0)
        
        # Last weight is different for ACT
        for i in range(self.max_recursion - 1):
            self.assertTrue(torch.allclose(act_weights[i], step_probs[i], rtol=1e-5, atol=1e-5))
        
        # Last weight should be the remaining probability
        expected_last_weight = 1 - cum_probs[-2]
        self.assertTrue(torch.allclose(act_weights[-1], expected_last_weight, rtol=1e-5, atol=1e-5))


class TestPonderNetHalting(unittest.TestCase):
    """Test cases for the PonderNet halting mechanism."""
    
    def setUp(self):
        """Set up test parameters and initialize components."""
        self.batch_size = 8
        self.d_model = 64
        self.max_recursion = 5
        
        # Initialize halting mechanism
        self.halting = PonderNetHalting(
            d_model=self.d_model,
            max_recursion=self.max_recursion
        )
        
        # Create test inputs
        self.x = torch.randn(self.batch_size, self.d_model)  # [B, E]

    def test_halting_network(self):
        """Test the halting network output."""
        # Forward pass through halting network
        halt_logits = self.halting.halting_network(self.x)
        
        # Check shape
        self.assertEqual(halt_logits.shape, (self.batch_size, 1))
        
        # Check that outputs are finite
        self.assertTrue(torch.all(torch.isfinite(halt_logits)))

    def test_compute_halting_probability(self):
        """Test computation of halting probability."""
        # Compute halting probability
        halt_prob = self.halting.compute_probability(self.x)
        
        # Check shape
        self.assertEqual(halt_prob.shape, (self.batch_size,))
        
        # Check that probabilities are valid
        self.assertTrue(torch.all(halt_prob >= 0))
        self.assertTrue(torch.all(halt_prob <= 1))

    def test_lambda_regularization(self):
        """Test lambda regularization for PonderNet."""
        # Set lambda parameter
        self.halting.lambda_p = 0.01
        
        # Compute regularization
        reg_loss = self.halting.compute_regularization([0.3, 0.5, 0.1, 0.05, 0.05])
        
        # Check that loss is non-negative
        self.assertGreaterEqual(reg_loss.item(), 0)
        
        # Expected KL divergence between actual distribution and geometric distribution
        expected_reg = 0.0
        actual_probs = torch.tensor([0.3, 0.5, 0.1, 0.05, 0.05])
        geo_probs = torch.tensor([1/3, (2/3)*(1/3), (2/3)**2*(1/3), (2/3)**3*(1/3), (2/3)**4*(1/3)])
        expected_reg = F.kl_div(torch.log(actual_probs), geo_probs, reduction='sum')
        
        # Should be close to expected value
        self.assertAlmostEqual(reg_loss.item(), self.halting.lambda_p * expected_reg.item(), places=5)

    def test_multi_step_halting(self):
        """Test halting probabilities across multiple steps."""
        # Create sequence of hidden states
        hidden_states = [torch.randn(self.batch_size, self.d_model) for _ in range(self.max_recursion)]
        
        # Compute halting probabilities
        halt_probs = [self.halting.compute_probability(h) for h in hidden_states]
        
        # Check shapes
        for prob in halt_probs:
            self.assertEqual(prob.shape, (self.batch_size,))
        
        # Stack probabilities
        stacked_probs = torch.stack(halt_probs, dim=0)
        
        # Check that these are different for different steps
        for i in range(1, self.max_recursion):
            self.assertFalse(torch.allclose(stacked_probs[i], stacked_probs[0], rtol=1e-3, atol=1e-3))


class TestACTHalting(unittest.TestCase):
    """Test cases for the Adaptive Computation Time (ACT) halting mechanism."""
    
    def setUp(self):
        """Set up test parameters and initialize components."""
        self.batch_size = 8
        self.d_model = 64
        self.max_recursion = 5
        self.epsilon = 0.01
        
        # Initialize halting mechanism
        self.halting = ACTHalting(
            d_model=self.d_model,
            max_recursion=self.max_recursion,
            epsilon=self.epsilon
        )
        
        # Create test inputs
        self.x = torch.randn(self.batch_size, self.d_model)  # [B, E]

    def test_halting_network(self):
        """Test the halting network output."""
        # Forward pass through halting network
        halt_logits = self.halting.halting_network(self.x)
        
        # Check shape
        self.assertEqual(halt_logits.shape, (self.batch_size, 1))
        
        # Check that outputs are finite
        self.assertTrue(torch.all(torch.isfinite(halt_logits)))

    def test_compute_halting_probability(self):
        """Test computation of halting probability."""
        # Compute halting probability
        halt_prob = self.halting.compute_probability(self.x)
        
        # Check shape
        self.assertEqual(halt_prob.shape, (self.batch_size,))
        
        # Check that probabilities are valid
        self.assertTrue(torch.all(halt_prob >= 0))
        self.assertTrue(torch.all(halt_prob <= 1))

    def test_ponder_cost_regularization(self):
        """Test ponder cost regularization for ACT."""
        # Set regularization parameter
        self.halting.lambda_p = 0.01
        
        # Create dummy step probabilities and N values
        step_probs = torch.zeros(self.max_recursion, self.batch_size)
        step_probs[0, :] = 0.3
        step_probs[1, :] = 0.5
        step_probs[2, :] = 0.1
        step_probs[3, :] = 0.05
        step_probs[4, :] = 0.05
        
        N = torch.ones(self.batch_size) * 3  # Average recursion depth of 3
        
        # Compute regularization
        reg_loss = self.halting.compute_regularization(step_probs, N)
        
        # Check that loss is non-negative
        self.assertGreaterEqual(reg_loss.item(), 0)
        
        # For ACT, ponder cost should be proportional to N - 1
        expected_ponder_cost = torch.mean(N) - 1
        expected_reg = self.halting.lambda_p * expected_ponder_cost
        
        # Should be close to expected value
        self.assertAlmostEqual(reg_loss.item(), expected_reg.item(), places=5)

    def test_epsilon_threshold(self):
        """Test that epsilon threshold is respected in halting decision."""
        # Create dummy cumulative probabilities
        cum_probs = torch.zeros(self.max_recursion, self.batch_size)
        cum_probs[0, :] = 0.3
        cum_probs[1, :] = 0.8
        cum_probs[2, :] = 0.9
        cum_probs[3, :] = 0.95
        cum_probs[4, :] = 1.0
        
        # Compute N values
        N = self.halting.compute_N(cum_probs)
        
        # Check shape
        self.assertEqual(N.shape, (self.batch_size,))
        
        # Check that N values respect threshold of 1-epsilon
        threshold = 1.0 - self.epsilon
        
        # At position 1, cum_prob is 0.8, which is less than 0.99 (for epsilon=0.01)
        # At position 2, cum_prob is 0.9, which is less than 0.99
        # At position 3, cum_prob is 0.95, which is less than 0.99
        # At position 4, cum_prob is 1.0, which is >= 0.99
        
        # So N should be 5 (index 4 + 1)
        self.assertTrue(torch.all(N == 5))
        
        # If we change threshold to 0.85, N should be 3 (index 2 + 1)
        self.halting.epsilon = 0.15  # 1 - 0.15 = 0.85
        N = self.halting.compute_N(cum_probs)
        self.assertTrue(torch.all(N == 3))


class TestWeightedOutputCombiner(unittest.TestCase):
    """Test cases for the WeightedOutputCombiner component."""
    
    def setUp(self):
        """Set up test parameters and initialize components."""
        self.batch_size = 8
        self.seq_len = 16
        self.d_model = 64
        self.max_recursion = 5
        
        # Initialize output combiner
        self.combiner = WeightedOutputCombiner()
        
        # Create test inputs
        self.intermediate_outputs = [
            torch.randn(self.seq_len, self.batch_size, self.d_model)
            for _ in range(self.max_recursion)
        ]
        
        # Create weights
        self.weights = torch.softmax(
            torch.randn(self.max_recursion, self.batch_size),
            dim=0
        )

    def test_output_shape(self):
        """Test that combined output has the correct shape."""
        # Combine outputs
        combined = self.combiner(self.intermediate_outputs, self.weights)
        
        # Check shape
        self.assertEqual(combined.shape, (self.seq_len, self.batch_size, self.d_model))

    def test_weighted_sum(self):
        """Test that the output is a correct weighted sum of intermediate outputs."""
        # Create simplified outputs and weights for testing
        outputs = [
            torch.ones(2, 2, 3) * i  # Each output is filled with its index
            for i in range(3)
        ]
        
        weights = torch.zeros(3, 2)
        weights[0, 0] = 1.0  # First sample uses only first output
        weights[1, 1] = 0.5  # Second sample uses 50% second output
        weights[2, 1] = 0.5  # Second sample uses 50% third output
        
        # Combine outputs
        combined = self.combiner(outputs, weights)
        
        # Check shape
        self.assertEqual(combined.shape, (2, 2, 3))
        
        # Check first sample (should be all 0s)
        self.assertTrue(torch.allclose(combined[:, 0, :], torch.zeros(2, 3)))
        
        # Check second sample (should be 0.5*1 + 0.5*2 = 1.5)
        self.assertTrue(torch.allclose(combined[:, 1, :], torch.ones(2, 3) * 1.5))

    def test_weight_normalization(self):
        """Test that weights are properly normalized."""
        # Create unnormalized weights
        unnormalized_weights = torch.rand(self.max_recursion, self.batch_size)
        
        # Normalize weights
        normalized_weights = self.combiner.normalize_weights(unnormalized_weights)
        
        # Check shape
        self.assertEqual(normalized_weights.shape, unnormalized_weights.shape)
        
        # Check that weights sum to 1 along recursion dimension
        weight_sums = normalized_weights.sum(dim=0)
        self.assertTrue(torch.allclose(weight_sums, torch.ones_like(weight_sums), rtol=1e-5, atol=1e-5))

    def test_extreme_weights(self):
        """Test behavior with extreme weight distributions."""
        # Create one-hot weights (each sample uses a different output exclusively)
        one_hot_weights = torch.zeros(self.max_recursion, self.batch_size)
        for i in range(self.batch_size):
            one_hot_weights[i % self.max_recursion, i] = 1.0
        
        # Combine outputs with one-hot weights
        combined = self.combiner(self.intermediate_outputs, one_hot_weights)
        
        # Check that each sample equals the corresponding intermediate output
        for i in range(self.batch_size):
            output_idx = i % self.max_recursion
            self.assertTrue(torch.allclose(
                combined[:, i, :],
                self.intermediate_outputs[output_idx][:, i, :],
                rtol=1e-5, atol=1e-5
            ))


class TestRecursiveTransformerLayer(unittest.TestCase):
    """Test cases for the RecursiveTransformerLayer component."""
    
    def setUp(self):
        """Set up test parameters and initialize components."""
        self.batch_size = 8
        self.seq_len = 16
        self.d_model = 64
        self.nhead = 4
        self.dim_feedforward = 256
        self.dropout = 0.1
        self.max_recursion = 5
        
        # Initialize recursive transformer layer
        self.layer = RecursiveTransformerLayer(
            d_model=self.d_model,
            nhead=self.nhead,
            dim_feedforward=self.dim_feedforward,
            dropout=self.dropout,
            max_recursion=self.max_recursion
        )
        
        # Create test inputs
        self.src = torch.randn(self.seq_len, self.batch_size, self.d_model)  # [S, B, E]
        self.src_mask = torch.zeros(self.seq_len, self.seq_len).bool()  # [S, S]
        self.src_key_padding_mask = torch.zeros(self.batch_size, self.seq_len).bool()  # [B, S]

    def test_single_step_forward(self):
        """Test a single step of recursive processing."""
        # Process a single step
        output, halt_prob = self.layer.process_step(
            self.src,
            src_mask=self.src_mask,
            src_key_padding_mask=self.src_key_padding_mask
        )
        
        # Check output shape
        self.assertEqual(output.shape, self.src.shape)
        
        # Check halt probability shape
        self.assertEqual(halt_prob.shape, (self.batch_size,))
        
        # Check that halt probability is valid
        self.assertTrue(torch.all(halt_prob >= 0))
        self.assertTrue(torch.all(halt_prob <= 1))

    def test_full_recursion(self):
        """Test the full recursive processing."""
        # Full recursive forward pass
        output = self.layer(
            self.src,
            src_mask=self.src_mask,
            src_key_padding_mask=self.src_key_padding_mask
        )
        
        # Check output shape
        self.assertEqual(output.shape, self.src.shape)
        
        # Check that recursion depth was tracked
        self.assertIsNotNone(getattr(self.layer, 'recursion_depths', None))
        self.assertEqual(self.layer.recursion_depths.shape, (self.batch_size,))

    def test_different_samples_different_depths(self):
        """Test that different samples in a batch can use different recursion depths."""
        # Create a mock halting probability function that gives different probs for different samples
        original_compute_prob = self.layer.recursion_controller.halting_mechanism.compute_probability
        
        def mock_compute_probability(x):
            batch_size = x.size(1) if x.dim() == 3 else x.size(0)
            # Even samples halt quickly, odd samples need more steps
            probs = torch.zeros(batch_size)
            probs[::2] = 0.9  # High probability for even samples
            probs[1::2] = 0.1  # Low probability for odd samples
            return probs
        
        self.layer.recursion_controller.halting_mechanism.compute_probability = mock_compute_probability
        
        # Process input
        output = self.layer(
            self.src,
            src_mask=self.src_mask,
            src_key_padding_mask=self.src_key_padding_mask
        )
        
        # Check output shape
        self.assertEqual(output.shape, self.src.shape)
        
        # Check recursion depths
        self.assertTrue(torch.all(self.layer.recursion_depths[::2] < self.layer.recursion_depths[1::2]))
        
        # Restore original function
        self.layer.recursion_controller.halting_mechanism.compute_probability = original_compute_prob

    def test_trainability(self):
        """Test that the layer is trainable end-to-end."""
        # Enable gradient tracking
        self.src.requires_grad = True
        
        # Forward pass
        output = self.layer(
            self.src,
            src_mask=self.src_mask,
            src_key_padding_mask=self.src_key_padding_mask
        )
        
        # Compute loss
        loss = output.mean()
        
        # Backward pass
        loss.backward()
        
        # Check that gradients are computed
        self.assertIsNotNone(self.src.grad)
        
        # Check that parameters have gradients
        for name, param in self.layer.named_parameters():
            if param.requires_grad:
                self.assertIsNotNone(param.grad, f"Parameter {name} has no gradient")

    def test_halt_regularization(self):
        """Test that regularization for halting can be computed."""
        # Forward pass to initialize step probabilities
        _ = self.layer(
            self.src,
            src_mask=self.src_mask,
            src_key_padding_mask=self.src_key_padding_mask
        )
        
        # Compute regularization loss
        reg_loss = self.layer.compute_regularization_loss()
        
        # Check that loss is a scalar
        self.assertEqual(reg_loss.shape, torch.Size([]))
        
        # Check that loss is non-negative
        self.assertGreaterEqual(reg_loss.item(), 0)


class TestRecursiveTransformerEncoder(unittest.TestCase):
    """Test cases for the RecursiveTransformerEncoder component."""
    
    def setUp(self):
        """Set up test parameters and initialize components."""
        self.batch_size = 8
        self.seq_len = 16
        self.d_model = 64
        self.nhead = 4
        self.num_layers = 3
        self.dim_feedforward = 256
        self.dropout = 0.1
        self.max_recursion = 5
        
        # Initialize recursive transformer encoder
        self.encoder = RecursiveTransformerEncoder(
            d_model=self.d_model,
            nhead=self.nhead,
            num_layers=self.num_layers,
            dim_feedforward=self.dim_feedforward,
            dropout=self.dropout,
            max_recursion=self.max_recursion
        )
        
        # Create test inputs
        self.src = torch.randn(self.seq_len, self.batch_size, self.d_model)  # [S, B, E]
        self.src_mask = torch.zeros(self.seq_len, self.seq_len).bool()  # [S, S]
        self.src_key_padding_mask = torch.zeros(self.batch_size, self.seq_len).bool()  # [B, S]

    def test_encoder_output_shape(self):
        """Test that encoder output has the correct shape."""
        # Forward pass
        output = self.encoder(
            self.src,
            mask=self.src_mask,
            src_key_padding_mask=self.src_key_padding_mask
        )
        
        # Check output shape
        self.assertEqual(output.shape, self.src.shape)

    def test_layer_stacking(self):
        """Test that encoder correctly stacks multiple layers."""
        # Check number of layers
        self.assertEqual(len(self.encoder.layers), self.num_layers)
        
        # Check that each layer is a RecursiveTransformerLayer
        for layer in self.encoder.layers:
            self.assertIsInstance(layer, RecursiveTransformerLayer)

    def test_encoder_with_recursion(self):
        """Test that encoder properly handles recursive layers."""
        # Create a mock layer to track inputs and outputs
        outputs = []
        
        class MockLayer(nn.Module):
            def __init__(self):
                super().__init__()
                self.recursion_depths = torch.ones(self.batch_size)
            
            def forward(self, src, src_mask=None, src_key_padding_mask=None):
                outputs.append(src.clone())
                return src + 0.1  # Small modification to track progress
        
        # Replace encoder layers with mock layers
        original_layers = self.encoder.layers
        self.encoder.layers = nn.ModuleList([MockLayer() for _ in range(self.num_layers)])
        
        # Forward pass
        output = self.encoder(
            self.src,
            mask=self.src_mask,
            src_key_padding_mask=self.src_key_padding_mask
        )
        
        # Check that all layers were called
        self.assertEqual(len(outputs), self.num_layers)
        
        # Check that output was modified by each layer
        expected_output = self.src + 0.1 * self.num_layers
        self.assertTrue(torch.allclose(output, expected_output, rtol=1e-5, atol=1e-5))
        
        # Restore original layers
        self.encoder.layers = original_layers

    def test_encoder_regularization(self):
        """Test computation of regularization loss for all layers."""
        # Forward pass
        _ = self.encoder(
            self.src,
            mask=self.src_mask,
            src_key_padding_mask=self.src_key_padding_mask
        )
        
        # Compute regularization loss
        reg_loss = self.encoder.compute_regularization_loss()
        
        # Check that loss is a scalar
        self.assertEqual(reg_loss.shape, torch.Size([]))
        
        # Check that loss is non-negative
        self.assertGreaterEqual(reg_loss.item(), 0)


class TestRecursiveTransformerDecoder(unittest.TestCase):
    """Test cases for the RecursiveTransformerDecoder component."""
    
    def setUp(self):
        """Set up test parameters and initialize components."""
        self.batch_size = 8
        self.seq_len = 16
        self.d_model = 64
        self.nhead = 4
        self.num_layers = 3
        self.dim_feedforward = 256
        self.dropout = 0.1
        self.max_recursion = 5
        
        # Initialize recursive transformer decoder
        self.decoder = RecursiveTransformerDecoder(
            d_model=self.d_model,
            nhead=self.nhead,
            num_layers=self.num_layers,
            dim_feedforward=self.dim_feedforward,
            dropout=self.dropout,
            max_recursion=self.max_recursion
        )
        
        # Create test inputs
        self.tgt = torch.randn(self.seq_len, self.batch_size, self.d_model)  # [S, B, E]
        self.memory = torch.randn(self.seq_len, self.batch_size, self.d_model)  # [S, B, E]
        
        # Create masks
        self.tgt_mask = torch.triu(
            torch.ones(self.seq_len, self.seq_len), diagonal=1
        ).bool()  # [S, S] - Standard causal mask for decoding
        
        self.memory_mask = torch.zeros(self.seq_len, self.seq_len).bool()  # [S, S]
        self.tgt_key_padding_mask = torch.zeros(self.batch_size, self.seq_len).bool()  # [B, S]
        self.memory_key_padding_mask = torch.zeros(self.batch_size, self.seq_len).bool()  # [B, S]

    def test_decoder_output_shape(self):
        """Test that decoder output has the correct shape."""
        # Forward pass
        output = self.decoder(
            self.tgt,
            self.memory,
            tgt_mask=self.tgt_mask,
            memory_mask=self.memory_mask,
            tgt_key_padding_mask=self.tgt_key_padding_mask,
            memory_key_padding_mask=self.memory_key_padding_mask
        )
        
        # Check output shape
        self.assertEqual(output.shape, self.tgt.shape)

    def test_layer_stacking(self):
        """Test that decoder correctly stacks multiple layers."""
        # Check number of layers
        self.assertEqual(len(self.decoder.layers), self.num_layers)

    def test_decoder_regularization(self):
        """Test computation of regularization loss for all layers."""
        # Forward pass
        _ = self.decoder(
            self.tgt,
            self.memory,
            tgt_mask=self.tgt_mask,
            memory_mask=self.memory_mask,
            tgt_key_padding_mask=self.tgt_key_padding_mask,
            memory_key_padding_mask=self.memory_key_padding_mask
        )
        
        # Compute regularization loss
        reg_loss = self.decoder.compute_regularization_loss()
        
        # Check that loss is a scalar
        self.assertEqual(reg_loss.shape, torch.Size([]))
        
        # Check that loss is non-negative
        self.assertGreaterEqual(reg_loss.item(), 0)

    def test_autoregressive_property(self):
        """Test that decoder respects autoregressive property with causal masking."""
        # Create a simple target sequence
        tgt = torch.zeros(self.seq_len, self.batch_size, self.d_model)
        tgt[0, :, 0] = 1.0  # First token has a specific pattern
        
        # Forward pass
        output = self.decoder(
            tgt,
            self.memory,
            tgt_mask=self.tgt_mask,
            memory_mask=self.memory_mask,
            tgt_key_padding_mask=self.tgt_key_padding_mask,
            memory_key_padding_mask=self.memory_key_padding_mask
        )
        
        # In an autoregressive decoder, the first position's output should not depend on later positions
        # So modifying later positions should not affect the first position's output
        
        # Create a modified target with changes in later positions
        modified_tgt = tgt.clone()
        modified_tgt[1:, :, :] = 2.0  # Change all positions after the first
        
        # Forward pass with modified target
        modified_output = self.decoder(
            modified_tgt,
            self.memory,
            tgt_mask=self.tgt_mask,
            memory_mask=self.memory_mask,
            tgt_key_padding_mask=self.tgt_key_padding_mask,
            memory_key_padding_mask=self.memory_key_padding_mask
        )
        
        # The first position's output should be the same
        self.assertTrue(torch.allclose(
            output[0], modified_output[0], rtol=1e-5, atol=1e-5
        ))
        
        # Later positions should differ
        for i in range(1, self.seq_len):
            self.assertFalse(torch.allclose(
                output[i], modified_output[i], rtol=1e-3, atol=1e-3
            ))


class TestRecursiveTransformer(unittest.TestCase):
    """Test cases for the complete RecursiveTransformer component."""
    
    def setUp(self):
        """Set up test parameters and initialize components."""
        self.batch_size = 8
        self.seq_len = 16
        self.d_model = 64
        self.nhead = 4
        self.num_encoder_layers = 3
        self.num_decoder_layers = 3
        self.dim_feedforward = 256
        self.dropout = 0.1
        self.max_recursion = 5
        
        # Initialize the recursive transformer
        self.transformer = RecursiveTransformer(
            d_model=self.d_model,
            nhead=self.nhead,
            num_encoder_layers=self.num_encoder_layers,
            num_decoder_layers=self.num_decoder_layers,
            dim_feedforward=self.dim_feedforward,
            dropout=self.dropout,
            max_recursion=self.max_recursion
        )
        
        # Create test inputs
        self.src = torch.randn(self.seq_len, self.batch_size, self.d_model)  # [S, B, E]
        self.tgt = torch.randn(self.seq_len, self.batch_size, self.d_model)  # [S, B, E]
        
        # Create masks
        self.src_mask = torch.zeros(self.seq_len, self.seq_len).bool()  # [S, S]
        self.tgt_mask = torch.triu(
            torch.ones(self.seq_len, self.seq_len), diagonal=1
        ).bool()  # [S, S]
        
        self.src_key_padding_mask = torch.zeros(self.batch_size, self.seq_len).bool()  # [B, S]
        self.tgt_key_padding_mask = torch.zeros(self.batch_size, self.seq_len).bool()  # [B, S]

    def test_transformer_output_shape(self):
        """Test that transformer output has the correct shape."""
        # Forward pass
        output = self.transformer(
            self.src,
            self.tgt,
            src_mask=self.src_mask,
            tgt_mask=self.tgt_mask,
            src_key_padding_mask=self.src_key_padding_mask,
            tgt_key_padding_mask=self.tgt_key_padding_mask
        )
        
        # Check output shape
        self.assertEqual(output.shape, self.tgt.shape)

    def test_encoder_decoder_structure(self):
        """Test that transformer has both encoder and decoder."""
        # Check encoder
        self.assertIsInstance(self.transformer.encoder, RecursiveTransformerEncoder)
        self.assertEqual(len(self.transformer.encoder.layers), self.num_encoder_layers)
        
        # Check decoder
        self.assertIsInstance(self.transformer.decoder, RecursiveTransformerDecoder)
        self.assertEqual(len(self.transformer.decoder.layers), self.num_decoder_layers)

    def test_recursion_stats(self):
        """Test that recursion statistics can be retrieved."""
        # Forward pass
        _ = self.transformer(
            self.src,
            self.tgt,
            src_mask=self.src_mask,
            tgt_mask=self.tgt_mask,
            src_key_padding_mask=self.src_key_padding_mask,
            tgt_key_padding_mask=self.tgt_key_padding_mask
        )
        
        # Get recursion stats
        stats = self.transformer.get_recursion_stats()
        
        # Check that stats include mean recursion depth for all layers
        self.assertIn('encoder_layers_mean_depth', stats)
        self.assertIn('decoder_layers_mean_depth', stats)
        
        # Check shapes
        self.assertEqual(stats['encoder_layers_mean_depth'].shape, (self.num_encoder_layers,))
        self.assertEqual(stats['decoder_layers_mean_depth'].shape, (self.num_decoder_layers,))

    def test_regularization(self):
        """Test computation of regularization loss for whole model."""
        # Forward pass
        _ = self.transformer(
            self.src,
            self.tgt,
            src_mask=self.src_mask,
            tgt_mask=self.tgt_mask,
            src_key_padding_mask=self.src_key_padding_mask,
            tgt_key_padding_mask=self.tgt_key_padding_mask
        )
        
        # Compute regularization loss
        reg_loss = self.transformer.compute_regularization_loss()
        
        # Check that loss is a scalar
        self.assertEqual(reg_loss.shape, torch.Size([]))
        
        # Check that loss is non-negative
        self.assertGreaterEqual(reg_loss.item(), 0)

    @parameterized.expand([
        ("encoder_only", True, False),
        ("decoder_only", False, True),
        ("both", True, True),
    ])
    def test_recursive_config(self, name, encoder_recursive, decoder_recursive):
        """Test configuration of which components use recursion."""
        # Initialize the transformer with specific recursion configuration
        transformer = RecursiveTransformer(
            d_model=self.d_model,
            nhead=self.nhead,
            num_encoder_layers=self.num_encoder_layers,
            num_decoder_layers=self.num_decoder_layers,
            dim_feedforward=self.dim_feedforward,
            dropout=self.dropout,
            max_recursion=self.max_recursion,
            encoder_recursive=encoder_recursive,
            decoder_recursive=decoder_recursive
        )
        
        # Forward pass
        output = transformer(
            self.src,
            self.tgt,
            src_mask=self.src_mask,
            tgt_mask=self.tgt_mask,
            src_key_padding_mask=self.src_key_padding_mask,
            tgt_key_padding_mask=self.tgt_key_padding_mask
        )
        
        # Check output shape
        self.assertEqual(output.shape, self.tgt.shape)
        
        # Check encoder layer type
        for layer in transformer.encoder.layers:
            if encoder_recursive:
                self.assertIsInstance(layer, RecursiveTransformerLayer)
            else:
                self.assertNotIsInstance(layer, RecursiveTransformerLayer)
        
        # Check decoder layer type
        for layer in transformer.decoder.layers:
            if decoder_recursive:
                self.assertTrue(hasattr(layer, 'self_attn') and hasattr(layer, 'multihead_attn'))
            else:
                self.assertTrue(hasattr(layer, 'self_attn') and hasattr(layer, 'multihead_attn'))

    def test_training_integration(self):
        """Test that the transformer can be trained end-to-end."""
        # Create a simple target for classification
        tgt_classes = torch.randint(0, 10, (self.batch_size * self.seq_len,))
        
        # Define optimizer
        optimizer = torch.optim.Adam(self.transformer.parameters(), lr=0.001)
        
        # Simple training loop
        for _ in range(2):  # Just a few iterations for testing
            optimizer.zero_grad()
            
            # Forward pass
            output = self.transformer(
                self.src,
                self.tgt,
                src_mask=self.src_mask,
                tgt_mask=self.tgt_mask,
                src_key_padding_mask=self.src_key_padding_mask,
                tgt_key_padding_mask=self.tgt_key_padding_mask
            )
            
            # Compute loss (using output as logits for simplicity)
            output_flat = output.view(-1, self.d_model)
            logits = torch.linear(output_flat, torch.randn(10, self.d_model))
            loss = F.cross_entropy(logits, tgt_classes)
            
            # Add regularization
            reg_loss = self.transformer.compute_regularization_loss()
            total_loss = loss + reg_loss
            
            # Backward pass
            total_loss.backward()
            
            # Optimizer step
            optimizer.step()
            
            # Check that gradients are flowing
            for name, param in self.transformer.named_parameters():
                if param.requires_grad:
                    self.assertIsNotNone(param.grad, f"Parameter {name} has no gradient")


if __name__ == "__main__":
    unittest.main()