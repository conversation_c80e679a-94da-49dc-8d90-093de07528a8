#!/usr/bin/env python3
"""
Fix Integration Bridges
=======================

Comprehensive fix for all integration bridge import and logger issues.
"""

import os
import re
from pathlib import Path

def fix_bridge_file(file_path: str):
    """Fix a single bridge file"""
    
    print(f"🔧 Fixing {file_path}...")
    
    try:
        with open(file_path, 'r') as f:
            content = f.read()
        
        original_content = content
        
        # Fix 1: Replace incorrect config imports
        content = re.sub(
            r'from config import.*',
            'from ultra.utils.config import ConfigurationManager',
            content
        )
        
        content = re.sub(
            r'from ultra\.config import get_config',
            'from ultra.utils.config import ConfigurationManager',
            content
        )
        
        # Fix 2: Replace incorrect logger imports
        content = re.sub(
            r'from ultra\.utils\.ultra_logging import get_logger',
            'from ultra.utils.ultra_logging import get_ultra_logger',
            content
        )
        
        # Fix 3: Fix logger initialization patterns
        content = re.sub(
            r'logger = get_logger\(__name__\)',
            'logger = None  # Will be initialized in classes',
            content
        )
        
        # Fix 4: Replace get_config() calls
        content = re.sub(
            r'get_config\(\)',
            '{}',
            content
        )
        
        # Fix 5: Fix logger.warning calls on None logger
        content = re.sub(
            r'logger\.warning\(f"([^"]+)"\)',
            r'print(f"Warning: \1")',
            content
        )
        
        content = re.sub(
            r'logger\.warning\("([^"]+)"\)',
            r'print("Warning: \1")',
            content
        )
        
        # Fix 6: Fix other logger calls on None logger
        content = re.sub(
            r'logger\.info\(f"([^"]+)"\)',
            r'print(f"Info: \1")',
            content
        )
        
        content = re.sub(
            r'logger\.error\(f"([^"]+)"\)',
            r'print(f"Error: \1")',
            content
        )
        
        # Fix 7: Add proper logger initialization in classes
        if 'def __init__(self' in content and 'self.logger' not in content:
            # Find __init__ methods and add logger initialization
            init_pattern = r'(def __init__\(self[^)]*\):[^\n]*\n(?:[ ]*[^\n]*\n)*?)'
            
            def add_logger_init(match):
                init_method = match.group(1)
                if 'self.logger' not in init_method:
                    # Add logger initialization after the first few lines
                    lines = init_method.split('\n')
                    if len(lines) >= 3:
                        # Insert after the first assignment or config line
                        insert_pos = 2
                        for i, line in enumerate(lines[1:], 1):
                            if 'self.config' in line or 'self.' in line:
                                insert_pos = i + 1
                                break
                        
                        logger_code = [
                            '        ',
                            '        # Initialize logger',
                            '        try:',
                            '            from ultra.utils.ultra_logging import get_ultra_logger',
                            '            ultra_logger = get_ultra_logger(f"{__name__}.{self.__class__.__name__}")',
                            '            self.logger = ultra_logger.logger',
                            '        except ImportError:',
                            '            import logging',
                            '            self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")'
                        ]
                        
                        lines[insert_pos:insert_pos] = logger_code
                        return '\n'.join(lines)
                
                return init_method
            
            content = re.sub(init_pattern, add_logger_init, content)
        
        # Fix 8: Fix numpy float conversion issues
        content = re.sub(
            r'confidence=np\.mean\(([^)]+)\)',
            r'confidence=float(np.mean(\1))',
            content
        )
        
        # Fix 9: Fix import name issues in bridge imports
        content = re.sub(
            r'from ultra\.integration\.meta_cognitive_bridge import MetaCognitiveBridge',
            'from ultra.integration.meta_cognitive_bridge import MetaCognitiveController',
            content
        )
        
        content = re.sub(
            r'from ultra\.integration\.consciousness_lattice_bridge import ConsciousnessLatticeBridge',
            'from ultra.integration.consciousness_lattice_bridge import ConsciousnessLatticeIntegrator',
            content
        )
        
        content = re.sub(
            r'from ultra\.integration\.neuro_symbolic_bridge import NeuroSymbolicBridge',
            'from ultra.integration.neuro_symbolic_bridge import NeuroSymbolicIntegrationBridge',
            content
        )
        
        content = re.sub(
            r'from ultra\.integration\.self_evolution_bridge import SelfEvolutionBridge',
            'from ultra.integration.self_evolution_bridge import ULTRAMasterController',
            content
        )
        
        # Only write if content changed
        if content != original_content:
            with open(file_path, 'w') as f:
                f.write(content)
            print(f"   ✅ Fixed {file_path}")
            return True
        else:
            print(f"   ⚪ No changes needed for {file_path}")
            return False
            
    except Exception as e:
        print(f"   ❌ Error fixing {file_path}: {e}")
        return False

def fix_all_integration_bridges():
    """Fix all integration bridge files"""
    
    print("🔧 ULTRA Integration Bridge Repair Tool")
    print("=" * 60)
    
    # Find all bridge files
    integration_dir = Path("ultra/integration")
    bridge_files = [
        "neuromorphic_transformer_bridge.py",
        "diffusion_neuromorphic_bridge.py", 
        "consciousness_lattice_bridge.py",
        "neuro_symbolic_bridge.py",
        "self_evolution_bridge.py"
    ]
    
    fixed_count = 0
    total_count = 0
    
    for bridge_file in bridge_files:
        file_path = integration_dir / bridge_file
        if file_path.exists():
            total_count += 1
            if fix_bridge_file(str(file_path)):
                fixed_count += 1
        else:
            print(f"⚠️ File not found: {file_path}")
    
    print("\n" + "=" * 60)
    print(f"🎯 Bridge Repair Summary:")
    print(f"   • Files processed: {total_count}")
    print(f"   • Files fixed: {fixed_count}")
    print(f"   • Success rate: {fixed_count/total_count*100:.1f}%" if total_count > 0 else "   • No files to process")
    
    if fixed_count > 0:
        print("\n✅ Bridge repair completed!")
        print("💡 Run test_integration_bridges.py to verify fixes")
    else:
        print("\n⚪ No fixes were needed")
    
    return fixed_count, total_count

if __name__ == "__main__":
    fix_all_integration_bridges()
