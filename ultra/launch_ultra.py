#!/usr/bin/env python3
"""
ULTRA AGI - Simple Launcher
===========================

Clean, professional launcher for ULTRA AGI system.
"""

import sys
import os
import webbrowser
import time
import subprocess

def main():
    """Launch ULTRA AGI clean interface"""
    
    print("ULTRA AGI - Starting...")
    
    try:
        # Check if we're in the right directory
        if not os.path.exists('interfaces/clean_gui/ultra_clean_gui.py'):
            print("Error: Please run from the ultra directory")
            return
        
        # Start the clean GUI
        print("Initializing ULTRA core system...")
        
        # Launch the GUI server
        process = subprocess.Popen([
            sys.executable, 
            'interfaces/clean_gui/ultra_clean_gui.py'
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # Wait a moment for server to start
        time.sleep(3)
        
        # Open browser
        print("Opening ULTRA interface...")
        webbrowser.open('http://localhost:8080')
        
        print("ULTRA AGI is ready at http://localhost:8080")
        print("Press Ctrl+C to stop")
        
        # Wait for process
        try:
            process.wait()
        except KeyboardInterrupt:
            print("\nStopping ULTRA AGI...")
            process.terminate()
            
    except Exception as e:
        print(f"Error starting ULTRA: {e}")

if __name__ == "__main__":
    main()
