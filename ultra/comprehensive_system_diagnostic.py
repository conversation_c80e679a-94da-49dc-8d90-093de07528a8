#!/usr/bin/env python3
"""
ULTRA System Comprehensive Diagnostic Tool
==========================================

This script performs a comprehensive analysis of the ULTRA system to determine:
1. What percentage of the system is functional
2. Which components are working
3. Which components have issues
4. What can be done with the current implementation
5. Specific recommendations for fixes

Author: ULTRA Diagnostic System
Version: 1.0.0
"""

import sys
import os
import traceback
import importlib
import inspect
from pathlib import Path
from typing import Dict, List, Tuple, Any, Optional
import json
from datetime import datetime

# Add ULTRA to Python path
sys.path.insert(0, os.path.abspath('.'))

class ULTRASystemDiagnostic:
    """Comprehensive diagnostic tool for ULTRA system analysis"""
    
    def __init__(self):
        self.results = {
            'timestamp': datetime.now().isoformat(),
            'system_info': {},
            'component_status': {},
            'functionality_percentage': 0.0,
            'working_components': [],
            'broken_components': [],
            'import_issues': [],
            'recommendations': [],
            'usable_features': []
        }
        
        # Define all ULTRA components to test
        self.components_to_test = {
            # Core Components
            'ultra': 'Main ULTRA package',
            'ultra.core_neural': 'Core Neural Architecture',
            'ultra.hyper_transformer': 'Hyper Transformer',
            'ultra.diffusion_reasoning': 'Diffusion Reasoning',
            'ultra.meta_cognitive': 'Meta Cognitive System',
            'ultra.neuromorphic_processing': 'Neuromorphic Processing',
            'ultra.emergent_consciousness': 'Emergent Consciousness',
            'ultra.neuro_symbolic': 'Neuro Symbolic Integration',
            'ultra.self_evolution': 'Self Evolution System',

            # Integration Bridges
            'ultra.integration.neuromorphic_transformer_bridge': 'Neuromorphic-Transformer Bridge',
            'ultra.integration.diffusion_neuromorphic_bridge': 'Diffusion-Neuromorphic Bridge',
            'ultra.integration.meta_cognitive_bridge': 'Meta Cognitive Bridge',
            'ultra.integration.consciousness_lattice_bridge': 'Consciousness Lattice Bridge',
            'ultra.integration.neuro_symbolic_bridge': 'Neuro Symbolic Bridge',
            'ultra.integration.self_evolution_bridge': 'Self Evolution Bridge',

            # Utilities and Support
            'ultra.utils': 'ULTRA Utilities',
            'ultra.config': 'Configuration System',
            'ultra.utils.ultra_logging': 'Logging System',
            'ultra.utils.config': 'Config Utilities',
            'ultra.utils.visualization': 'Visualization Tools',
            'ultra.utils.monitoring': 'System Monitoring',

            # Input/Output Processing
            'ultra.input_processing': 'Input Processing',
            'ultra.output_generation': 'Output Generation',
            'ultra.knowledge_management': 'Knowledge Management',

            # Safety and Security
            'ultra.safety': 'Safety Systems',

            # API and Interfaces
            'ultra.api': 'API Interface',
            'ultra.cli': 'Command Line Interface',
            'ultra.main': 'Main Entry Point',

            # Chat Interfaces
            'interfaces.chat.chat_with_ultra': 'Chat Interface',
            'interfaces.chat.simple_ultra_chat': 'Simple Chat',
            'interfaces.chat.ultra_terminal_chat': 'Terminal Chat',

            # Training Systems
            'training.core.train_ultra_system': 'Training System',
            'training.core.direct_training': 'Direct Training',
            'training.core.ultra_internet_training': 'Internet Training',
        }
        
        # Define executable entry points
        self.executable_entry_points = {
            'ultra.main': 'Main ULTRA System',
            'ultra.cli': 'Command Line Interface',
            'interfaces.chat.chat_with_ultra': 'Interactive Chat',
            'interfaces.chat.simple_ultra_chat': 'Simple Chat',
            'interfaces.chat.ultra_terminal_chat': 'Terminal Chat',
            'interfaces.demo.ultra_agi_live_demo': 'Live Demo',
            'training.core.train_ultra_system': 'Training System',
            'training.core.ultra_internet_training': 'Internet Training',
        }

    def collect_system_info(self):
        """Collect basic system information"""
        try:
            import platform
            import psutil
            
            self.results['system_info'] = {
                'platform': platform.platform(),
                'python_version': platform.python_version(),
                'cpu_count': os.cpu_count(),
                'memory_gb': round(psutil.virtual_memory().total / (1024**3), 2),
                'available_memory_gb': round(psutil.virtual_memory().available / (1024**3), 2),
                'disk_usage_gb': round(psutil.disk_usage('.').free / (1024**3), 2),
            }
        except Exception as e:
            self.results['system_info'] = {'error': str(e)}

    def test_component_import(self, module_path: str, description: str) -> Tuple[bool, str, Any]:
        """Test if a component can be imported successfully"""
        try:
            module = importlib.import_module(module_path)
            
            # Try to get some basic info about the module
            module_info = {
                'file': getattr(module, '__file__', 'Unknown'),
                'doc': getattr(module, '__doc__', 'No documentation')[:200] if getattr(module, '__doc__', None) else 'No documentation',
                'attributes': len([attr for attr in dir(module) if not attr.startswith('_')]),
                'classes': len([attr for attr in dir(module) if inspect.isclass(getattr(module, attr, None))]),
                'functions': len([attr for attr in dir(module) if inspect.isfunction(getattr(module, attr, None))]),
            }
            
            return True, "Import successful", module_info
            
        except ImportError as e:
            return False, f"Import error: {str(e)}", None
        except Exception as e:
            return False, f"Other error: {str(e)}", None

    def test_all_components(self):
        """Test all ULTRA components"""
        print("🔍 Testing ULTRA System Components...")
        print("=" * 60)
        
        working_count = 0
        total_count = len(self.components_to_test)
        
        for module_path, description in self.components_to_test.items():
            print(f"Testing {description}...")
            success, message, info = self.test_component_import(module_path, description)
            
            component_result = {
                'module_path': module_path,
                'description': description,
                'success': success,
                'message': message,
                'info': info
            }
            
            self.results['component_status'][module_path] = component_result
            
            if success:
                print(f"  ✅ {description}: OK")
                self.results['working_components'].append(component_result)
                working_count += 1
            else:
                print(f"  ❌ {description}: {message}")
                self.results['broken_components'].append(component_result)
                self.results['import_issues'].append({
                    'component': description,
                    'module': module_path,
                    'error': message
                })
        
        # Calculate functionality percentage
        self.results['functionality_percentage'] = (working_count / total_count) * 100
        
        print("=" * 60)
        print(f"📊 Component Test Results: {working_count}/{total_count} ({self.results['functionality_percentage']:.1f}%) working")

    def test_executable_entry_points(self):
        """Test which entry points can actually be executed"""
        print("\n🚀 Testing Executable Entry Points...")
        print("=" * 60)
        
        executable_components = []
        
        for module_path, description in self.executable_entry_points.items():
            if module_path in [comp['module_path'] for comp in self.results['working_components']]:
                try:
                    module = importlib.import_module(module_path)
                    
                    # Check if it has a main function or is directly executable
                    has_main = hasattr(module, 'main')
                    has_run = hasattr(module, 'run')
                    has_start = hasattr(module, 'start')
                    
                    executable_info = {
                        'module_path': module_path,
                        'description': description,
                        'has_main': has_main,
                        'has_run': has_run,
                        'has_start': has_start,
                        'executable': has_main or has_run or has_start
                    }
                    
                    if executable_info['executable']:
                        print(f"  ✅ {description}: Executable")
                        executable_components.append(executable_info)
                        self.results['usable_features'].append(executable_info)
                    else:
                        print(f"  ⚠️ {description}: Importable but no clear entry point")
                        
                except Exception as e:
                    print(f"  ❌ {description}: Error testing executability - {str(e)}")
            else:
                print(f"  ❌ {description}: Cannot import")
        
        print(f"📊 Executable Entry Points: {len(executable_components)} available")

    def analyze_import_issues(self):
        """Analyze common import issues and provide recommendations"""
        print("\n🔧 Analyzing Import Issues...")
        print("=" * 60)
        
        issue_patterns = {}
        
        for issue in self.results['import_issues']:
            error_msg = issue['error'].lower()
            
            # Categorize common issues
            if 'no module named' in error_msg:
                if 'missing_modules' not in issue_patterns:
                    issue_patterns['missing_modules'] = []
                issue_patterns['missing_modules'].append(issue)
                
            elif 'cannot import name' in error_msg:
                if 'import_name_errors' not in issue_patterns:
                    issue_patterns['import_name_errors'] = []
                issue_patterns['import_name_errors'].append(issue)
                
            elif 'circular import' in error_msg:
                if 'circular_imports' not in issue_patterns:
                    issue_patterns['circular_imports'] = []
                issue_patterns['circular_imports'].append(issue)
        
        # Generate recommendations
        recommendations = []
        
        if 'missing_modules' in issue_patterns:
            missing_count = len(issue_patterns['missing_modules'])
            recommendations.append(f"Install missing dependencies: {missing_count} modules not found")
            
        if 'import_name_errors' in issue_patterns:
            name_error_count = len(issue_patterns['import_name_errors'])
            recommendations.append(f"Fix import name mismatches: {name_error_count} name errors found")
            
        if 'circular_imports' in issue_patterns:
            circular_count = len(issue_patterns['circular_imports'])
            recommendations.append(f"Resolve circular import dependencies: {circular_count} circular imports")
        
        self.results['recommendations'] = recommendations
        
        for rec in recommendations:
            print(f"  💡 {rec}")

    def generate_usage_guide(self):
        """Generate a guide for what can actually be used"""
        print("\n📖 What You Can Do With Current ULTRA System:")
        print("=" * 60)

        if not self.results['usable_features']:
            print("  ❌ No fully functional entry points available")
            print("  🔧 System needs repair before use")
            return

        for feature in self.results['usable_features']:
            print(f"\n🎯 {feature['description']}:")
            print(f"   Module: {feature['module_path']}")

            if feature['has_main']:
                print(f"   Usage: python -m {feature['module_path']}")
            elif feature['has_run']:
                print(f"   Usage: Import and call .run() method")
            elif feature['has_start']:
                print(f"   Usage: Import and call .start() method")

    def save_diagnostic_report(self):
        """Save detailed diagnostic report to file"""
        report_file = f"ultra_diagnostic_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

        try:
            with open(report_file, 'w') as f:
                json.dump(self.results, f, indent=2, default=str)
            print(f"\n💾 Detailed diagnostic report saved to: {report_file}")
        except Exception as e:
            print(f"\n❌ Failed to save report: {str(e)}")

    def run_full_diagnostic(self):
        """Run complete system diagnostic"""
        print("🔬 ULTRA System Comprehensive Diagnostic")
        print("=" * 60)
        print(f"Timestamp: {self.results['timestamp']}")

        # Collect system information
        self.collect_system_info()
        print(f"System: {self.results['system_info'].get('platform', 'Unknown')}")
        print(f"Python: {self.results['system_info'].get('python_version', 'Unknown')}")
        print(f"Memory: {self.results['system_info'].get('available_memory_gb', 'Unknown')} GB available")

        # Test all components
        self.test_all_components()

        # Test executable entry points
        self.test_executable_entry_points()

        # Analyze issues
        self.analyze_import_issues()

        # Generate usage guide
        self.generate_usage_guide()

        # Save report
        self.save_diagnostic_report()

        # Final summary
        print("\n" + "=" * 60)
        print("🎯 ULTRA System Status Summary:")
        print(f"   Overall Functionality: {self.results['functionality_percentage']:.1f}%")
        print(f"   Working Components: {len(self.results['working_components'])}")
        print(f"   Broken Components: {len(self.results['broken_components'])}")
        print(f"   Usable Features: {len(self.results['usable_features'])}")
        print(f"   Critical Issues: {len(self.results['import_issues'])}")

        if self.results['functionality_percentage'] >= 80:
            print("   Status: 🟢 System is mostly functional")
        elif self.results['functionality_percentage'] >= 50:
            print("   Status: 🟡 System has significant issues but partially usable")
        else:
            print("   Status: 🔴 System requires major repairs")

        return self.results

if __name__ == "__main__":
    diagnostic = ULTRASystemDiagnostic()
    results = diagnostic.run_full_diagnostic()
