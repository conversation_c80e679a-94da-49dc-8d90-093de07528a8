#!/usr/bin/env python3
"""
ULTRA Complete System Test Suite
===============================

Comprehensive testing suite for the complete ULTRA system including:
- Backend API testing
- Frontend functionality testing
- Integration bridge testing
- System orchestrator testing
- Performance benchmarking
- End-to-end workflow testing

This test suite validates that all components work together harmoniously.
"""

import asyncio
import time
import json
import logging
import sys
import os
from pathlib import Path
from typing import Dict, List, Any, Optional

# Add ULTRA to path
ultra_root = Path(__file__).parent
sys.path.insert(0, str(ultra_root))

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ULTRASystemTester:
    """Comprehensive ULTRA system tester"""
    
    def __init__(self):
        self.test_results = {}
        logger.info("ULTRA System Tester initialized")
    
    async def run_complete_test_suite(self) -> Dict[str, Any]:
        """Run the complete test suite"""
        logger.info("🧪 Starting ULTRA Complete System Test Suite")
        print("=" * 60)
        print("🧪 ULTRA COMPLETE SYSTEM TEST SUITE")
        print("=" * 60)
        
        test_results = {
            "start_time": time.time(),
            "tests": {},
            "summary": {}
        }
        
        try:
            # Test 1: Component Integration Tests
            print("\n1️⃣ Testing Component Integration...")
            test_results["tests"]["component_integration"] = await self.test_component_integration()
            
            # Test 2: Backend API Tests
            print("\n2️⃣ Testing Backend API...")
            test_results["tests"]["backend_api"] = await self.test_backend_api()
            
            # Test 3: System Orchestrator Tests
            print("\n3️⃣ Testing System Orchestrator...")
            test_results["tests"]["system_orchestrator"] = await self.test_system_orchestrator()
            
            # Test 4: Bridge Performance Tests
            print("\n4️⃣ Testing Bridge Performance...")
            test_results["tests"]["bridge_performance"] = await self.test_bridge_performance()
            
            # Test 5: End-to-End Workflow Tests
            print("\n5️⃣ Testing End-to-End Workflows...")
            test_results["tests"]["end_to_end"] = await self.test_end_to_end_workflows()
            
            # Generate summary
            test_results["end_time"] = time.time()
            test_results["total_duration"] = test_results["end_time"] - test_results["start_time"]
            test_results["summary"] = self.generate_test_summary(test_results["tests"])
            
            # Print results
            self.print_test_results(test_results)
            
            return test_results
            
        except Exception as e:
            logger.error(f"Test suite failed: {e}")
            test_results["error"] = str(e)
            return test_results
    
    async def test_component_integration(self) -> Dict[str, Any]:
        """Test integration of all components"""
        results = {"status": "running", "tests": {}}
        
        try:
            # Test bridge imports and initialization
            print("   Testing bridge imports...")
            
            # Import all bridges
            from ultra.integration.test_integration_bridges import (
                TestKnowledgeManagementBridge,
                TestAutonomousLearningBridge,
                TestInputProcessingBridge
            )
            from ultra.integration.test_hyper_transformer_bridge import TestHyperTransformerBridge
            from ultra.integration.test_output_generation_bridge import TestOutputGenerationBridge
            from ultra.integration.test_safety_monitoring_bridge import TestSafetyMonitoringBridge
            
            results["tests"]["bridge_imports"] = {"status": "passed", "message": "All bridges imported successfully"}
            
            # Test bridge initialization
            print("   Testing bridge initialization...")
            bridges = {
                "knowledge_management": TestKnowledgeManagementBridge(),
                "autonomous_learning": TestAutonomousLearningBridge(),
                "input_processing": TestInputProcessingBridge(),
                "hyper_transformer": TestHyperTransformerBridge(),
                "output_generation": TestOutputGenerationBridge(),
                "safety_monitoring": TestSafetyMonitoringBridge()
            }
            
            initialization_results = {}
            for name, bridge in bridges.items():
                try:
                    success = await bridge.initialize_bridge()
                    initialization_results[name] = "passed" if success else "failed"
                    if success:
                        await bridge.shutdown_bridge()
                except Exception as e:
                    initialization_results[name] = f"error: {e}"
            
            results["tests"]["bridge_initialization"] = {
                "status": "passed" if all(r == "passed" for r in initialization_results.values()) else "partial",
                "details": initialization_results
            }
            
            results["status"] = "passed"
            print("   ✅ Component integration tests passed")
            
        except Exception as e:
            results["status"] = "failed"
            results["error"] = str(e)
            print(f"   ❌ Component integration tests failed: {e}")
        
        return results
    
    async def test_backend_api(self) -> Dict[str, Any]:
        """Test backend API functionality"""
        results = {"status": "running", "tests": {}}
        
        try:
            # Test unified backend import
            print("   Testing unified backend import...")
            from ultra.unified_backend import ULTRAUnifiedBackend
            
            backend = ULTRAUnifiedBackend()
            results["tests"]["backend_import"] = {"status": "passed"}
            
            # Test backend initialization
            print("   Testing backend initialization...")
            await backend.initialize_system()
            
            results["tests"]["backend_initialization"] = {"status": "passed"}
            
            # Test API endpoints
            print("   Testing API endpoint structure...")
            
            # Check if FastAPI app is properly configured
            app_routes = [route.path for route in backend.app.routes]
            expected_routes = ["/", "/health", "/api/v1/process", "/api/v1/bridges", "/api/v1/metrics"]
            
            route_check = all(route in app_routes for route in expected_routes)
            results["tests"]["api_routes"] = {
                "status": "passed" if route_check else "failed",
                "expected_routes": expected_routes,
                "actual_routes": app_routes
            }
            
            # Test request processing
            print("   Testing request processing...")
            from ultra.unified_backend import ULTRARequest
            
            test_request = ULTRARequest(
                operation="chat",
                input_data="Hello ULTRA",
                input_type="text"
            )
            
            response = await backend.process_unified_request(test_request, None)
            
            results["tests"]["request_processing"] = {
                "status": "passed" if response.success else "failed",
                "response_success": response.success
            }
            
            # Shutdown backend
            await backend.shutdown_system()
            
            results["status"] = "passed"
            print("   ✅ Backend API tests passed")
            
        except Exception as e:
            results["status"] = "failed"
            results["error"] = str(e)
            print(f"   ❌ Backend API tests failed: {e}")
        
        return results
    
    async def test_system_orchestrator(self) -> Dict[str, Any]:
        """Test system orchestrator functionality"""
        results = {"status": "running", "tests": {}}
        
        try:
            print("   Testing orchestrator operations...")
            
            from ultra.system_orchestrator import ULTRASystemOrchestrator, OperationType
            
            orchestrator = ULTRASystemOrchestrator()
            await orchestrator.initialize_system()
            
            # Test different operation types
            operation_tests = {}
            
            test_operations = [
                (OperationType.PROCESS_INPUT, "Test input processing"),
                (OperationType.GENERATE_OUTPUT, "Test output generation"),
                (OperationType.REASON, "What is 2+2?"),
                (OperationType.LEARN, "Learn about machine learning"),
                (OperationType.ANALYZE, "Analyze this text for safety")
            ]
            
            for op_type, input_data in test_operations:
                try:
                    operation = await orchestrator.execute_operation(op_type, input_data)
                    operation_tests[op_type.value] = {
                        "status": "passed" if operation.success else "failed",
                        "success": operation.success,
                        "duration": operation.end_time - operation.start_time if operation.end_time else 0
                    }
                except Exception as e:
                    operation_tests[op_type.value] = {
                        "status": "error",
                        "error": str(e)
                    }
            
            results["tests"]["operation_execution"] = operation_tests
            
            # Test system status
            status = orchestrator.get_system_status()
            results["tests"]["system_status"] = {
                "status": "passed",
                "active_components": status["active_components"],
                "total_components": status["total_components"],
                "success_rate": status["success_rate"]
            }
            
            await orchestrator.shutdown_system()
            
            results["status"] = "passed"
            print("   ✅ System orchestrator tests passed")
            
        except Exception as e:
            results["status"] = "failed"
            results["error"] = str(e)
            print(f"   ❌ System orchestrator tests failed: {e}")
        
        return results
    
    async def test_bridge_performance(self) -> Dict[str, Any]:
        """Test bridge performance and optimization"""
        results = {"status": "running", "tests": {}}
        
        try:
            print("   Testing bridge performance...")
            
            # Import performance optimizer
            from ultra.integration.bridge_performance_optimizer import get_performance_coordinator
            
            coordinator = get_performance_coordinator()
            
            # Register test bridges
            bridge_names = [
                "knowledge_management",
                "autonomous_learning", 
                "input_processing",
                "hyper_transformer",
                "output_generation",
                "safety_monitoring"
            ]
            
            for bridge_name in bridge_names:
                optimizer = coordinator.register_bridge(bridge_name)
                results["tests"][f"{bridge_name}_optimizer"] = {"status": "passed"}
            
            # Start coordination
            coordinator.start_coordination()
            
            # Get performance report
            report = coordinator.get_global_report()
            
            results["tests"]["performance_coordination"] = {
                "status": "passed",
                "total_bridges": report["total_bridges"],
                "coordination_active": report["coordination_active"]
            }
            
            # Stop coordination
            coordinator.stop_coordination()
            
            results["status"] = "passed"
            print("   ✅ Bridge performance tests passed")
            
        except Exception as e:
            results["status"] = "failed"
            results["error"] = str(e)
            print(f"   ❌ Bridge performance tests failed: {e}")
        
        return results
    
    async def test_end_to_end_workflows(self) -> Dict[str, Any]:
        """Test complete end-to-end workflows"""
        results = {"status": "running", "tests": {}}
        
        try:
            print("   Testing end-to-end workflows...")
            
            # Test complete system workflow
            from ultra.system_orchestrator import ULTRASystemOrchestrator, OperationType
            
            orchestrator = ULTRASystemOrchestrator()
            await orchestrator.initialize_system()
            
            # Workflow 1: Chat conversation
            print("     Testing chat workflow...")
            chat_operation = await orchestrator.execute_operation(
                OperationType.PROCESS_INPUT,
                "Hello, can you help me understand quantum physics?",
                {"workflow": "chat"}
            )
            
            results["tests"]["chat_workflow"] = {
                "status": "passed" if chat_operation.success else "failed",
                "success": chat_operation.success
            }
            
            # Workflow 2: Learning workflow
            print("     Testing learning workflow...")
            learn_operation = await orchestrator.execute_operation(
                OperationType.LEARN,
                "Machine learning fundamentals",
                {"workflow": "learning"}
            )
            
            results["tests"]["learning_workflow"] = {
                "status": "passed" if learn_operation.success else "failed",
                "success": learn_operation.success
            }
            
            await orchestrator.shutdown_system()
            
            results["status"] = "passed"
            print("   ✅ End-to-end workflow tests passed")
            
        except Exception as e:
            results["status"] = "failed"
            results["error"] = str(e)
            print(f"   ❌ End-to-end workflow tests failed: {e}")
        
        return results
    
    def generate_test_summary(self, tests: Dict[str, Any]) -> Dict[str, Any]:
        """Generate test summary"""
        total_tests = len(tests)
        passed_tests = len([t for t in tests.values() if t.get("status") == "passed"])
        failed_tests = len([t for t in tests.values() if t.get("status") == "failed"])
        
        return {
            "total_tests": total_tests,
            "passed_tests": passed_tests,
            "failed_tests": failed_tests,
            "success_rate": passed_tests / total_tests if total_tests > 0 else 0,
            "overall_status": "passed" if passed_tests == total_tests else "failed"
        }
    
    def print_test_results(self, results: Dict[str, Any]):
        """Print comprehensive test results"""
        print("\n" + "=" * 60)
        print("🎯 ULTRA SYSTEM TEST RESULTS")
        print("=" * 60)
        
        summary = results["summary"]
        
        print(f"📊 Test Summary:")
        print(f"   Total Tests: {summary['total_tests']}")
        print(f"   Passed: {summary['passed_tests']} ✅")
        print(f"   Failed: {summary['failed_tests']} ❌")
        print(f"   Success Rate: {summary['success_rate']:.1%}")
        print(f"   Overall Status: {summary['overall_status'].upper()}")
        print(f"   Total Duration: {results['total_duration']:.2f} seconds")
        
        print(f"\n📋 Detailed Results:")
        for test_name, test_result in results["tests"].items():
            status_emoji = "✅" if test_result["status"] == "passed" else "❌"
            print(f"   {status_emoji} {test_name}: {test_result['status'].upper()}")
        
        if summary["overall_status"] == "passed":
            print(f"\n🎉 ALL TESTS PASSED! ULTRA SYSTEM IS FULLY OPERATIONAL!")
        else:
            print(f"\n⚠️ Some tests failed. Please review the results above.")
        
        print("=" * 60)

async def main():
    """Main test function"""
    tester = ULTRASystemTester()
    results = await tester.run_complete_test_suite()
    
    # Save results to file
    with open("ultra_system_test_results.json", "w") as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\n💾 Test results saved to: ultra_system_test_results.json")
    
    return results["summary"]["overall_status"] == "passed"

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
