#!/usr/bin/env python3
"""
ULTRA AGI GUI Launcher
======================

Simple launcher script for the ULTRA AGI graphical user interface.
"""

import sys
import os
import subprocess

def check_dependencies():
    """Check if required dependencies are installed"""
    
    print("🔍 Checking ULTRA GUI dependencies...")
    
    missing_deps = []
    
    # Check tkinter
    try:
        import tkinter
        print("✅ tkinter - Available")
    except ImportError:
        print("❌ tkinter - Missing")
        missing_deps.append("tkinter")
    
    # Check PIL/Pillow
    try:
        from PIL import Image
        print("✅ Pillow - Available")
    except ImportError:
        print("⚠️ Pillow - Missing (optional for image features)")
    
    # Check ULTRA components
    try:
        import ultra
        print("✅ ULTRA system - Available")
    except ImportError:
        print("❌ ULTRA system - Missing")
        missing_deps.append("ultra")
    
    return missing_deps

def install_missing_dependencies(missing_deps):
    """Install missing dependencies"""
    
    if not missing_deps:
        return True
    
    print(f"\n📦 Installing missing dependencies: {', '.join(missing_deps)}")
    
    for dep in missing_deps:
        if dep == "tkinter":
            print("⚠️ tkinter must be installed system-wide:")
            print("   • Ubuntu/Debian: sudo apt-get install python3-tk")
            print("   • macOS: tkinter should be included with Python")
            print("   • Windows: tkinter should be included with Python")
            return False
        
        elif dep == "ultra":
            print("❌ ULTRA system not found. Please ensure you're in the correct directory.")
            return False
    
    return True

def launch_gui():
    """Launch the ULTRA GUI"""
    
    print("\n🚀 Launching ULTRA AGI GUI...")
    
    try:
        # Import and run GUI
        from interfaces.gui.ultra_gui import main
        main()
        
    except ImportError as e:
        print(f"❌ Failed to import GUI: {e}")
        
        # Try alternative import path
        try:
            sys.path.insert(0, os.path.join(os.getcwd(), 'interfaces', 'gui'))
            from ultra_gui import main
            main()
        except ImportError as e2:
            print(f"❌ Alternative import also failed: {e2}")
            return False
    
    except Exception as e:
        print(f"❌ GUI launch failed: {e}")
        return False
    
    return True

def main():
    """Main launcher function"""
    
    print("🤖 ULTRA AGI - GUI Launcher")
    print("=" * 40)
    
    # Check dependencies
    missing_deps = check_dependencies()
    
    if missing_deps:
        if not install_missing_dependencies(missing_deps):
            print("\n❌ Cannot launch GUI due to missing dependencies")
            return
    
    print("\n✅ All dependencies satisfied")
    
    # Launch GUI
    if launch_gui():
        print("\n✅ ULTRA GUI launched successfully")
    else:
        print("\n❌ Failed to launch ULTRA GUI")
        print("\n💡 Troubleshooting tips:")
        print("   • Ensure you're in the ULTRA directory")
        print("   • Check that all dependencies are installed")
        print("   • Try running: python interfaces/gui/ultra_gui.py")

if __name__ == "__main__":
    main()
