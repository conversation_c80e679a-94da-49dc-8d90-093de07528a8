#!/usr/bin/env python3
"""
ULTRA Enhancement Dependencies Installer
========================================

Installs all required dependencies for ULTRA's advanced enhancement features.
"""

import subprocess
import sys
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def install_package(package_name: str, description: str = ""):
    """Install a Python package"""
    try:
        print(f"📦 Installing {package_name}... {description}")
        subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
        print(f"✅ {package_name} installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install {package_name}: {e}")
        return False

def install_all_enhancements():
    """Install all enhancement dependencies"""
    
    print("🚀 ULTRA Enhancement Dependencies Installer")
    print("=" * 60)
    
    # Core dependencies
    core_packages = [
        ("requests", "HTTP requests for external APIs"),
        ("aiohttp", "Async HTTP client for concurrent requests"),
        ("numpy", "Numerical computing"),
        ("torch", "PyTorch for deep learning models"),
        ("transformers", "Hugging Face transformers for language models"),
        ("tokenizers", "Fast tokenizers for transformers"),
    ]
    
    # External knowledge dependencies
    knowledge_packages = [
        ("wikipedia", "Wikipedia API access"),
        ("beautifulsoup4", "Web scraping and HTML parsing"),
        ("feedparser", "RSS feed parsing"),
        ("nltk", "Natural language processing"),
    ]
    
    # Voice interface dependencies
    voice_packages = [
        ("speechrecognition", "Speech-to-text capabilities"),
        ("pyttsx3", "Text-to-speech synthesis"),
        ("pyaudio", "Audio input/output"),
    ]
    
    # Visual processing dependencies
    visual_packages = [
        ("pillow", "Image processing"),
        ("opencv-python", "Computer vision"),
        ("matplotlib", "Plotting and visualization"),
    ]
    
    # Optional advanced packages
    advanced_packages = [
        ("accelerate", "Accelerated transformers training"),
        ("datasets", "Hugging Face datasets"),
        ("scikit-learn", "Machine learning utilities"),
        ("scipy", "Scientific computing"),
    ]
    
    all_packages = [
        ("Core Dependencies", core_packages),
        ("External Knowledge", knowledge_packages),
        ("Voice Interface", voice_packages),
        ("Visual Processing", visual_packages),
        ("Advanced Features", advanced_packages),
    ]
    
    installation_results = {}
    
    for category, packages in all_packages:
        print(f"\n🔧 Installing {category}...")
        print("-" * 40)
        
        category_results = []
        for package, description in packages:
            success = install_package(package, description)
            category_results.append((package, success))
        
        installation_results[category] = category_results
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Installation Summary")
    print("=" * 60)
    
    total_packages = 0
    successful_packages = 0
    
    for category, results in installation_results.items():
        successful = sum(1 for _, success in results if success)
        total = len(results)
        
        total_packages += total
        successful_packages += successful
        
        print(f"\n{category}: {successful}/{total} packages installed")
        
        for package, success in results:
            status = "✅" if success else "❌"
            print(f"  {status} {package}")
    
    print(f"\n🎯 Overall Success Rate: {successful_packages}/{total_packages} ({successful_packages/total_packages*100:.1f}%)")
    
    if successful_packages == total_packages:
        print("🎉 All enhancement dependencies installed successfully!")
        print("✨ ULTRA Enhanced System is ready to use!")
    elif successful_packages >= total_packages * 0.8:
        print("🟡 Most dependencies installed successfully!")
        print("💡 Some optional features may not be available.")
    else:
        print("🔴 Many dependencies failed to install.")
        print("💡 Please check your Python environment and try again.")
    
    # Post-installation setup
    print("\n🔧 Post-installation setup...")
    
    try:
        # Download NLTK data
        import nltk
        print("📚 Downloading NLTK data...")
        nltk.download('punkt', quiet=True)
        nltk.download('stopwords', quiet=True)
        nltk.download('wordnet', quiet=True)
        print("✅ NLTK data downloaded")
    except:
        print("⚠️ NLTK data download failed (optional)")
    
    print("\n🚀 ULTRA Enhancement System Ready!")
    print("Run: python interfaces/chat/ultra_enhanced_chat.py")

if __name__ == "__main__":
    install_all_enhancements()
