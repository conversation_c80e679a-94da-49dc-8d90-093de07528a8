#!/usr/bin/env python3
"""
ULTRA Unified Frontend Server
============================

Modern web frontend that provides access to all ULTRA functionality through
a clean, professional interface. Integrates with the unified backend to
provide real-time access to all bridges and components.

Features:
- Modern responsive design
- Real-time WebSocket communication
- Component monitoring dashboard
- Multi-modal input support
- Voice interface integration
- Visual processing capabilities
- System administration panel
"""

import os
import sys
import asyncio
import json
import logging
from pathlib import Path
from typing import Dict, Any, Optional

# Add ULTRA to path
current_dir = Path(__file__).parent
ultra_root = current_dir.parent.parent
sys.path.insert(0, str(ultra_root))

from flask import Flask, render_template_string, request, jsonify, send_from_directory
from flask_socketio import SocketIO, emit
import requests
import threading
import time

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ULTRAUnifiedFrontend:
    """Modern web frontend for ULTRA system"""
    
    def __init__(self, backend_url: str = "http://localhost:8000"):
        self.app = Flask(__name__)
        self.app.config['SECRET_KEY'] = 'ultra_unified_frontend'
        self.socketio = SocketIO(self.app, cors_allowed_origins="*")
        
        self.backend_url = backend_url
        self.system_status = {"status": "connecting", "backend_available": False}
        
        self.setup_routes()
        self.setup_socket_handlers()
        
        # Start backend health monitoring
        threading.Thread(target=self.monitor_backend_health, daemon=True).start()
        
        logger.info("ULTRA Unified Frontend initialized")
    
    def setup_routes(self):
        """Setup Flask routes"""
        
        @self.app.route('/')
        def index():
            return self.render_main_interface()
        
        @self.app.route('/dashboard')
        def dashboard():
            return self.render_dashboard()
        
        @self.app.route('/api/system/status')
        def system_status():
            return jsonify(self.system_status)
        
        @self.app.route('/api/send', methods=['POST'])
        def send_message():
            data = request.get_json()
            message = data.get('message', '').strip()
            operation = data.get('operation', 'chat')
            
            if message:
                # Forward to backend
                try:
                    response = requests.post(f"{self.backend_url}/api/v1/process", json={
                        "operation": operation,
                        "input_data": message,
                        "input_type": "text",
                        "options": data.get('options', {})
                    }, timeout=30)
                    
                    if response.status_code == 200:
                        return jsonify(response.json())
                    else:
                        return jsonify({"success": False, "error": "Backend error"})
                        
                except Exception as e:
                    return jsonify({"success": False, "error": str(e)})
            
            return jsonify({"success": False, "error": "No message provided"})
        
        @self.app.route('/static/<path:filename>')
        def static_files(filename):
            return send_from_directory('static', filename)
    
    def setup_socket_handlers(self):
        """Setup SocketIO handlers"""
        
        @self.socketio.on('connect')
        def handle_connect():
            emit('system_status', self.system_status)
            logger.info("Client connected")
        
        @self.socketio.on('send_message')
        def handle_message(data):
            message = data.get('message', '').strip()
            operation = data.get('operation', 'chat')
            
            if message:
                # Process through backend
                threading.Thread(
                    target=self.process_message_async,
                    args=(message, operation, data.get('options', {})),
                    daemon=True
                ).start()
        
        @self.socketio.on('get_system_metrics')
        def handle_get_metrics():
            threading.Thread(target=self.get_system_metrics_async, daemon=True).start()
        
        @self.socketio.on('execute_bridge_operation')
        def handle_bridge_operation(data):
            bridge_name = data.get('bridge_name')
            operation_data = data.get('operation_data', {})
            
            if bridge_name:
                threading.Thread(
                    target=self.execute_bridge_operation_async,
                    args=(bridge_name, operation_data),
                    daemon=True
                ).start()
    
    def process_message_async(self, message: str, operation: str, options: Dict[str, Any]):
        """Process message through backend asynchronously"""
        try:
            response = requests.post(f"{self.backend_url}/api/v1/process", json={
                "operation": operation,
                "input_data": message,
                "input_type": "text",
                "options": options
            }, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                self.socketio.emit('message_response', {
                    'success': True,
                    'operation': operation,
                    'result': result.get('result'),
                    'metadata': result.get('metadata', {}),
                    'processing_time': result.get('processing_time', 0)
                })
            else:
                self.socketio.emit('message_response', {
                    'success': False,
                    'error': f"Backend error: {response.status_code}"
                })
                
        except Exception as e:
            self.socketio.emit('message_response', {
                'success': False,
                'error': str(e)
            })
    
    def get_system_metrics_async(self):
        """Get system metrics asynchronously"""
        try:
            response = requests.get(f"{self.backend_url}/api/v1/metrics", timeout=10)
            
            if response.status_code == 200:
                metrics = response.json()
                self.socketio.emit('system_metrics', metrics)
            else:
                self.socketio.emit('system_metrics', {"error": "Failed to get metrics"})
                
        except Exception as e:
            self.socketio.emit('system_metrics', {"error": str(e)})
    
    def execute_bridge_operation_async(self, bridge_name: str, operation_data: Dict[str, Any]):
        """Execute bridge operation asynchronously"""
        try:
            response = requests.post(
                f"{self.backend_url}/api/v1/bridges/{bridge_name}/execute",
                json=operation_data,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                self.socketio.emit('bridge_operation_result', {
                    'bridge_name': bridge_name,
                    'success': True,
                    'result': result
                })
            else:
                self.socketio.emit('bridge_operation_result', {
                    'bridge_name': bridge_name,
                    'success': False,
                    'error': f"Backend error: {response.status_code}"
                })
                
        except Exception as e:
            self.socketio.emit('bridge_operation_result', {
                'bridge_name': bridge_name,
                'success': False,
                'error': str(e)
            })
    
    def monitor_backend_health(self):
        """Monitor backend health continuously"""
        while True:
            try:
                response = requests.get(f"{self.backend_url}/health", timeout=5)
                if response.status_code == 200:
                    health_data = response.json()
                    self.system_status = {
                        "status": health_data.get("status", "unknown"),
                        "backend_available": True,
                        "uptime": health_data.get("uptime", 0),
                        "active_bridges": health_data.get("active_bridges", 0),
                        "total_bridges": health_data.get("total_bridges", 0)
                    }
                else:
                    self.system_status = {"status": "backend_error", "backend_available": False}
                    
            except Exception as e:
                self.system_status = {"status": "backend_unavailable", "backend_available": False}
                logger.warning(f"Backend health check failed: {e}")
            
            # Emit status update to all connected clients
            self.socketio.emit('system_status', self.system_status)
            
            time.sleep(10)  # Check every 10 seconds
    
    def render_main_interface(self) -> str:
        """Render the main ULTRA interface"""
        return '''<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ULTRA - Ultimate Learning & Thought Reasoning Architecture</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .logo {
            font-size: 2rem;
            font-weight: bold;
            color: white;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .system-status {
            display: flex;
            align-items: center;
            gap: 1rem;
            color: white;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #4ade80;
            box-shadow: 0 0 10px rgba(74, 222, 128, 0.5);
        }
        
        .status-indicator.error {
            background: #ef4444;
            box-shadow: 0 0 10px rgba(239, 68, 68, 0.5);
        }
        
        .main-container {
            flex: 1;
            display: flex;
            max-width: 1200px;
            margin: 0 auto;
            width: 100%;
            gap: 2rem;
            padding: 2rem;
        }
        
        .chat-panel {
            flex: 2;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        .control-panel {
            flex: 1;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 2rem;
            height: fit-content;
        }
        
        .messages {
            flex: 1;
            padding: 2rem;
            overflow-y: auto;
            max-height: 500px;
        }
        
        .message {
            margin-bottom: 1rem;
            padding: 1rem;
            border-radius: 12px;
            max-width: 80%;
        }
        
        .message.user {
            background: #3b82f6;
            color: white;
            margin-left: auto;
        }
        
        .message.assistant {
            background: #f3f4f6;
            color: #1f2937;
        }
        
        .input-area {
            padding: 2rem;
            border-top: 1px solid #e5e7eb;
            background: white;
        }
        
        .input-container {
            display: flex;
            gap: 1rem;
            align-items: flex-end;
        }
        
        .input-field {
            flex: 1;
            padding: 1rem;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            resize: vertical;
            min-height: 50px;
            font-family: inherit;
            font-size: 1rem;
        }
        
        .input-field:focus {
            outline: none;
            border-color: #3b82f6;
        }
        
        .send-button {
            padding: 1rem 2rem;
            background: #3b82f6;
            color: white;
            border: none;
            border-radius: 12px;
            cursor: pointer;
            font-weight: 600;
            transition: background 0.2s;
        }
        
        .send-button:hover:not(:disabled) {
            background: #2563eb;
        }
        
        .send-button:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }
        
        .operation-selector {
            margin-bottom: 1rem;
        }
        
        .operation-selector select {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 1rem;
        }
        
        .control-section {
            margin-bottom: 2rem;
        }
        
        .control-section h3 {
            margin-bottom: 1rem;
            color: #1f2937;
            font-size: 1.2rem;
        }
        
        .bridge-controls {
            display: grid;
            gap: 0.5rem;
        }
        
        .bridge-button {
            padding: 0.75rem;
            background: #f3f4f6;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s;
            text-align: left;
        }
        
        .bridge-button:hover {
            background: #e5e7eb;
        }
        
        .bridge-button.active {
            background: #dbeafe;
            border-color: #3b82f6;
        }
        
        .metrics-display {
            background: #f9fafb;
            padding: 1rem;
            border-radius: 8px;
            font-family: monospace;
            font-size: 0.9rem;
        }
        
        .status-text {
            font-size: 0.9rem;
            opacity: 0.8;
        }
        
        @media (max-width: 768px) {
            .main-container {
                flex-direction: column;
                padding: 1rem;
            }
            
            .header {
                padding: 1rem;
            }
            
            .logo {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="logo">ULTRA</div>
        <div class="system-status">
            <div class="status-indicator" id="statusIndicator"></div>
            <div>
                <div id="statusText">Connecting...</div>
                <div class="status-text" id="statusDetails"></div>
            </div>
        </div>
    </div>
    
    <div class="main-container">
        <div class="chat-panel">
            <div class="messages" id="messages">
                <div class="message assistant">
                    Welcome to ULTRA - Ultimate Learning & Thought Reasoning Architecture. 
                    I'm ready to help you with reasoning, learning, and complex problem-solving.
                </div>
            </div>
            
            <div class="input-area">
                <div class="operation-selector">
                    <select id="operationSelect">
                        <option value="chat">Chat</option>
                        <option value="reason">Reason</option>
                        <option value="learn">Learn</option>
                        <option value="analyze">Analyze</option>
                        <option value="generate">Generate</option>
                        <option value="process">Process</option>
                    </select>
                </div>
                <div class="input-container">
                    <textarea 
                        class="input-field" 
                        id="messageInput" 
                        placeholder="Ask ULTRA anything..."
                        rows="2"
                    ></textarea>
                    <button class="send-button" id="sendButton">Send</button>
                </div>
            </div>
        </div>
        
        <div class="control-panel">
            <div class="control-section">
                <h3>System Bridges</h3>
                <div class="bridge-controls" id="bridgeControls">
                    <div class="bridge-button" data-bridge="knowledge_management">Knowledge Management</div>
                    <div class="bridge-button" data-bridge="autonomous_learning">Autonomous Learning</div>
                    <div class="bridge-button" data-bridge="input_processing">Input Processing</div>
                    <div class="bridge-button" data-bridge="hyper_transformer">Hyper-Transformer</div>
                    <div class="bridge-button" data-bridge="output_generation">Output Generation</div>
                    <div class="bridge-button" data-bridge="safety_monitoring">Safety Monitoring</div>
                </div>
            </div>
            
            <div class="control-section">
                <h3>System Metrics</h3>
                <div class="metrics-display" id="metricsDisplay">
                    Loading metrics...
                </div>
            </div>
            
            <div class="control-section">
                <button class="send-button" onclick="refreshMetrics()" style="width: 100%;">
                    Refresh Metrics
                </button>
            </div>
        </div>
    </div>
    
    <script>
        // Initialize Socket.IO connection
        const socket = io();
        
        // DOM elements
        const messages = document.getElementById('messages');
        const messageInput = document.getElementById('messageInput');
        const sendButton = document.getElementById('sendButton');
        const operationSelect = document.getElementById('operationSelect');
        const statusIndicator = document.getElementById('statusIndicator');
        const statusText = document.getElementById('statusText');
        const statusDetails = document.getElementById('statusDetails');
        const metricsDisplay = document.getElementById('metricsDisplay');
        const bridgeControls = document.getElementById('bridgeControls');
        
        // System state
        let systemStatus = { backend_available: false };
        
        // Socket event handlers
        socket.on('connect', function() {
            console.log('Connected to ULTRA frontend');
        });
        
        socket.on('system_status', function(status) {
            systemStatus = status;
            updateSystemStatus(status);
        });
        
        socket.on('message_response', function(response) {
            addMessage('assistant', formatResponse(response));
            sendButton.disabled = false;
            sendButton.textContent = 'Send';
        });
        
        socket.on('system_metrics', function(metrics) {
            updateMetricsDisplay(metrics);
        });
        
        // UI functions
        function updateSystemStatus(status) {
            if (status.backend_available) {
                statusIndicator.className = 'status-indicator';
                statusText.textContent = `System ${status.status}`;
                statusDetails.textContent = `${status.active_bridges || 0}/${status.total_bridges || 0} bridges active`;
            } else {
                statusIndicator.className = 'status-indicator error';
                statusText.textContent = 'Backend Unavailable';
                statusDetails.textContent = 'Connecting...';
            }
        }
        
        function addMessage(role, content) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${role}`;
            messageDiv.textContent = content;
            messages.appendChild(messageDiv);
            messages.scrollTop = messages.scrollHeight;
        }
        
        function formatResponse(response) {
            if (response.success) {
                if (typeof response.result === 'string') {
                    return response.result;
                } else if (response.result && typeof response.result === 'object') {
                    return JSON.stringify(response.result, null, 2);
                } else {
                    return 'Operation completed successfully';
                }
            } else {
                return `Error: ${response.error || 'Unknown error'}`;
            }
        }
        
        function updateMetricsDisplay(metrics) {
            if (metrics.error) {
                metricsDisplay.textContent = `Error: ${metrics.error}`;
            } else {
                const health = metrics.system_health || {};
                const display = `Status: ${health.status || 'unknown'}
Uptime: ${Math.floor((health.uptime || 0) / 60)}m
Bridges: ${health.active_bridges || 0}/${health.total_bridges || 0}
Connections: ${health.active_connections || 0}
Operations: ${health.system_metrics?.total_operations || 0}`;
                metricsDisplay.textContent = display;
            }
        }
        
        function sendMessage() {
            const message = messageInput.value.trim();
            const operation = operationSelect.value;
            
            if (message && systemStatus.backend_available) {
                addMessage('user', message);
                
                socket.emit('send_message', {
                    message: message,
                    operation: operation,
                    options: {}
                });
                
                messageInput.value = '';
                sendButton.disabled = true;
                sendButton.textContent = 'Processing...';
            }
        }
        
        function refreshMetrics() {
            socket.emit('get_system_metrics');
        }
        
        // Event listeners
        sendButton.addEventListener('click', sendMessage);
        
        messageInput.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });
        
        // Bridge controls
        bridgeControls.addEventListener('click', function(e) {
            if (e.target.classList.contains('bridge-button')) {
                const bridgeName = e.target.dataset.bridge;
                // Toggle bridge selection (visual feedback)
                e.target.classList.toggle('active');
                
                // Execute bridge operation (placeholder)
                socket.emit('execute_bridge_operation', {
                    bridge_name: bridgeName,
                    operation_data: { action: 'status_check' }
                });
            }
        });
        
        // Auto-refresh metrics every 30 seconds
        setInterval(refreshMetrics, 30000);
        
        // Initial metrics load
        setTimeout(refreshMetrics, 2000);
    </script>
</body>
</html>'''
    
    def render_dashboard(self) -> str:
        """Render the system dashboard"""
        return '''<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ULTRA Dashboard</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 2rem;
            background: #f8fafc;
        }
        
        .dashboard-header {
            margin-bottom: 2rem;
        }
        
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }
        
        .dashboard-card {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .card-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #1f2937;
        }
        
        .metric-value {
            font-size: 2rem;
            font-weight: bold;
            color: #3b82f6;
        }
        
        .metric-label {
            color: #6b7280;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="dashboard-header">
        <h1>ULTRA System Dashboard</h1>
        <p>Real-time monitoring of all ULTRA components and bridges</p>
    </div>
    
    <div class="dashboard-grid">
        <div class="dashboard-card">
            <div class="card-title">System Health</div>
            <div class="metric-value" id="healthScore">--</div>
            <div class="metric-label">Health Score</div>
        </div>
        
        <div class="dashboard-card">
            <div class="card-title">Active Bridges</div>
            <div class="metric-value" id="activeBridges">--</div>
            <div class="metric-label">of 6 total bridges</div>
        </div>
        
        <div class="dashboard-card">
            <div class="card-title">Total Operations</div>
            <div class="metric-value" id="totalOperations">--</div>
            <div class="metric-label">since startup</div>
        </div>
        
        <div class="dashboard-card">
            <div class="card-title">Response Time</div>
            <div class="metric-value" id="responseTime">--</div>
            <div class="metric-label">average (ms)</div>
        </div>
    </div>
    
    <script>
        const socket = io();
        
        socket.on('system_metrics', function(metrics) {
            const health = metrics.system_health || {};
            
            document.getElementById('healthScore').textContent = 
                health.status === 'healthy' ? '100%' : '0%';
            document.getElementById('activeBridges').textContent = 
                `${health.active_bridges || 0}`;
            document.getElementById('totalOperations').textContent = 
                health.system_metrics?.total_operations || '0';
            document.getElementById('responseTime').textContent = 
                Math.round((health.system_metrics?.average_response_time || 0) * 1000);
        });
        
        // Request metrics every 5 seconds
        setInterval(() => {
            socket.emit('get_system_metrics');
        }, 5000);
        
        // Initial load
        socket.emit('get_system_metrics');
    </script>
</body>
</html>'''
    
    def run(self, host: str = "0.0.0.0", port: int = 8080):
        """Run the frontend server"""
        logger.info(f"🌐 Starting ULTRA Unified Frontend on http://{host}:{port}")
        self.socketio.run(self.app, host=host, port=port, debug=False)

if __name__ == "__main__":
    frontend = ULTRAUnifiedFrontend()
    frontend.run()
