#!/usr/bin/env python3
"""
ULTRA AGI - Web-based GUI
=========================

Modern web-based interface for ULTRA AGI system using Flask.
Provides better compatibility and modern UI/UX.
"""

import sys
import os
import asyncio
import threading
import json
from datetime import datetime

# Add ULTRA to path
sys.path.insert(0, os.path.abspath('.'))
sys.path.insert(0, os.path.abspath('ultra'))

try:
    from flask import Flask, render_template, request, jsonify, send_from_directory
    from flask_socketio import SocketIO, emit
    HAS_FLASK = True
except ImportError:
    print("❌ Flask not available. Install with: pip install flask flask-socketio")
    HAS_FLASK = False

import logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ULTRAWebGUI:
    """
    Web-based GUI for ULTRA AGI system
    """
    
    def __init__(self):
        if not HAS_FLASK:
            raise RuntimeError("Flask is required for web GUI")
        
        self.app = Flask(__name__)
        self.app.config['SECRET_KEY'] = 'ultra_agi_secret_key'
        self.socketio = SocketIO(self.app, cors_allowed_origins="*")
        
        # ULTRA system components
        self.ultra_system = None
        self.enhancement_system = None
        self.conversation_history = []
        self.system_status = {
            'initialized': False,
            'components': {},
            'last_update': None
        }
        
        # Setup routes and socket handlers
        self.setup_routes()
        self.setup_socket_handlers()
        
        # Initialize ULTRA in background
        self.init_thread = threading.Thread(target=self.initialize_ultra_async, daemon=True)
        self.init_thread.start()
        
        logger.info("ULTRA Web GUI initialized")
    
    def setup_routes(self):
        """Setup Flask routes"""
        
        @self.app.route('/')
        def index():
            return self.render_main_page()
        
        @self.app.route('/api/status')
        def get_status():
            return jsonify(self.system_status)
        
        @self.app.route('/api/conversation')
        def get_conversation():
            return jsonify(self.conversation_history)
        
        @self.app.route('/api/send_message', methods=['POST'])
        def send_message():
            data = request.get_json()
            message = data.get('message', '').strip()
            
            if message:
                # Process message asynchronously
                thread = threading.Thread(
                    target=self.process_message_async,
                    args=(message,),
                    daemon=True
                )
                thread.start()
                
                return jsonify({'status': 'processing'})
            
            return jsonify({'status': 'error', 'message': 'Empty message'})
        
        @self.app.route('/static/<path:filename>')
        def static_files(filename):
            return send_from_directory('static', filename)
    
    def setup_socket_handlers(self):
        """Setup SocketIO event handlers"""
        
        @self.socketio.on('connect')
        def handle_connect():
            logger.info("Client connected")
            emit('status_update', self.system_status)
        
        @self.socketio.on('disconnect')
        def handle_disconnect():
            logger.info("Client disconnected")
        
        @self.socketio.on('send_message')
        def handle_message(data):
            message = data.get('message', '').strip()
            if message:
                thread = threading.Thread(
                    target=self.process_message_async,
                    args=(message,),
                    daemon=True
                )
                thread.start()
        
        @self.socketio.on('voice_input')
        def handle_voice_input(data):
            # Handle voice input
            self.socketio.emit('message_update', {
                'type': 'system',
                'content': '🎤 Voice input received (processing...)',
                'timestamp': datetime.now().isoformat()
            })
        
        @self.socketio.on('train_topic')
        def handle_train_topic(data):
            topic = data.get('topic', '').strip()
            if topic:
                thread = threading.Thread(
                    target=self.train_on_topic_async,
                    args=(topic,),
                    daemon=True
                )
                thread.start()
    
    def render_main_page(self):
        """Render the main HTML page"""
        return '''
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ULTRA AGI - Advanced Artificial General Intelligence</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            color: #ffffff;
            height: 100vh;
            overflow: hidden;
        }
        
        .container {
            display: flex;
            height: 100vh;
        }
        
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: rgba(45, 45, 45, 0.95);
            backdrop-filter: blur(10px);
            padding: 15px 20px;
            border-bottom: 1px solid #404040;
            z-index: 1000;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #00ff88;
        }
        
        .status-indicator {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #ff4444;
            animation: pulse 2s infinite;
        }
        
        .status-dot.online {
            background: #00ff88;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .main-content {
            flex: 1;
            display: flex;
            margin-top: 70px;
        }
        
        .chat-area {
            flex: 1;
            display: flex;
            flex-direction: column;
            padding: 20px;
        }
        
        .conversation {
            flex: 1;
            background: rgba(26, 26, 26, 0.8);
            border-radius: 10px;
            padding: 20px;
            overflow-y: auto;
            margin-bottom: 20px;
            border: 1px solid #404040;
        }
        
        .message {
            margin-bottom: 15px;
            padding: 12px 16px;
            border-radius: 8px;
            max-width: 80%;
            word-wrap: break-word;
        }
        
        .message.user {
            background: linear-gradient(135deg, #00ff88 0%, #00cc66 100%);
            color: #000;
            margin-left: auto;
        }
        
        .message.ultra {
            background: rgba(64, 64, 64, 0.8);
            border-left: 4px solid #00ff88;
        }
        
        .message.system {
            background: rgba(255, 170, 0, 0.2);
            border-left: 4px solid #ffaa00;
            font-style: italic;
        }
        
        .message.error {
            background: rgba(255, 68, 68, 0.2);
            border-left: 4px solid #ff4444;
        }
        
        .input-area {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        .message-input {
            flex: 1;
            padding: 15px;
            border: 1px solid #404040;
            border-radius: 25px;
            background: rgba(64, 64, 64, 0.8);
            color: #fff;
            font-size: 16px;
            outline: none;
        }
        
        .message-input:focus {
            border-color: #00ff88;
            box-shadow: 0 0 10px rgba(0, 255, 136, 0.3);
        }
        
        .btn {
            padding: 12px 20px;
            border: none;
            border-radius: 20px;
            background: linear-gradient(135deg, #00ff88 0%, #00cc66 100%);
            color: #000;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 255, 136, 0.4);
        }
        
        .btn.secondary {
            background: rgba(64, 64, 64, 0.8);
            color: #fff;
            border: 1px solid #404040;
        }
        
        .sidebar {
            width: 300px;
            background: rgba(45, 45, 45, 0.95);
            padding: 20px;
            border-left: 1px solid #404040;
            overflow-y: auto;
        }
        
        .panel {
            background: rgba(26, 26, 26, 0.8);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            border: 1px solid #404040;
        }
        
        .panel h3 {
            color: #00ff88;
            margin-bottom: 15px;
            font-size: 16px;
        }
        
        .component-status {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #404040;
        }
        
        .component-status:last-child {
            border-bottom: none;
        }
        
        .status-icon {
            font-size: 14px;
        }
        
        .quick-action {
            width: 100%;
            margin-bottom: 8px;
            padding: 10px;
            font-size: 14px;
        }
        
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #404040;
            border-radius: 50%;
            border-top-color: #00ff88;
            animation: spin 1s ease-in-out infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="logo">🤖 ULTRA AGI</div>
        <div class="status-indicator">
            <div class="status-dot" id="statusDot"></div>
            <span id="statusText">Initializing...</span>
        </div>
    </div>
    
    <div class="container">
        <div class="main-content">
            <div class="chat-area">
                <div class="conversation" id="conversation">
                    <div class="message system">
                        🤖 Welcome to ULTRA AGI - Advanced Artificial General Intelligence<br>
                        System is initializing... Please wait.
                    </div>
                </div>
                
                <div class="input-area">
                    <input type="text" class="message-input" id="messageInput" 
                           placeholder="Ask ULTRA anything..." disabled>
                    <button class="btn" id="sendBtn" onclick="sendMessage()" disabled>Send</button>
                    <button class="btn secondary" id="voiceBtn" onclick="startVoiceInput()">🎤</button>
                    <button class="btn secondary" onclick="clearConversation()">Clear</button>
                </div>
            </div>
            
            <div class="sidebar">
                <div class="panel">
                    <h3>System Status</h3>
                    <div id="componentStatus">
                        <div class="loading"></div> Loading...
                    </div>
                </div>
                
                <div class="panel">
                    <h3>Quick Actions</h3>
                    <button class="btn secondary quick-action" onclick="testReasoning()">
                        🧠 Test Reasoning
                    </button>
                    <button class="btn secondary quick-action" onclick="trainOnTopic()">
                        📚 Train on Topic
                    </button>
                    <button class="btn secondary quick-action" onclick="knowledgeSearch()">
                        🔍 Knowledge Search
                    </button>
                    <button class="btn secondary quick-action" onclick="showSystemInfo()">
                        📊 System Info
                    </button>
                </div>
                
                <div class="panel">
                    <h3>Enhancement Controls</h3>
                    <div id="enhancementControls">
                        <div class="loading"></div> Loading...
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // Initialize Socket.IO connection
        const socket = io();
        
        // DOM elements
        const conversation = document.getElementById('conversation');
        const messageInput = document.getElementById('messageInput');
        const sendBtn = document.getElementById('sendBtn');
        const statusDot = document.getElementById('statusDot');
        const statusText = document.getElementById('statusText');
        
        // Socket event handlers
        socket.on('connect', function() {
            console.log('Connected to ULTRA server');
        });
        
        socket.on('status_update', function(status) {
            updateSystemStatus(status);
        });
        
        socket.on('message_update', function(message) {
            addMessage(message.content, message.type);
        });
        
        socket.on('conversation_update', function(conversation_data) {
            updateConversation(conversation_data);
        });
        
        // Functions
        function updateSystemStatus(status) {
            if (status.initialized) {
                statusDot.classList.add('online');
                statusText.textContent = 'Online';
                messageInput.disabled = false;
                sendBtn.disabled = false;
                
                // Update component status
                updateComponentStatus(status.components);
            } else {
                statusText.textContent = 'Initializing...';
            }
        }
        
        function updateComponentStatus(components) {
            const statusDiv = document.getElementById('componentStatus');
            statusDiv.innerHTML = '';
            
            for (const [component, enabled] of Object.entries(components)) {
                const div = document.createElement('div');
                div.className = 'component-status';
                div.innerHTML = `
                    <span>${component.replace('_', ' ')}</span>
                    <span class="status-icon">${enabled ? '✅' : '❌'}</span>
                `;
                statusDiv.appendChild(div);
            }
        }
        
        function addMessage(content, type = 'system') {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            messageDiv.innerHTML = content.replace(/\\n/g, '<br>');
            conversation.appendChild(messageDiv);
            conversation.scrollTop = conversation.scrollHeight;
        }
        
        function sendMessage() {
            const message = messageInput.value.trim();
            if (message) {
                addMessage(`👤 You: ${message}`, 'user');
                socket.emit('send_message', { message: message });
                messageInput.value = '';
            }
        }
        
        function startVoiceInput() {
            addMessage('🎤 Voice input activated (feature coming soon)', 'system');
            socket.emit('voice_input', {});
        }
        
        function clearConversation() {
            conversation.innerHTML = '';
            addMessage('🔄 Conversation cleared', 'system');
        }
        
        function testReasoning() {
            const testQuery = "Explain the relationship between quantum mechanics and consciousness";
            messageInput.value = testQuery;
            sendMessage();
        }
        
        function trainOnTopic() {
            const topic = prompt("Enter topic to train on:");
            if (topic) {
                addMessage(`🌐 Starting training on: ${topic}`, 'system');
                socket.emit('train_topic', { topic: topic });
            }
        }
        
        function knowledgeSearch() {
            const searchTerm = prompt("Enter search term:");
            if (searchTerm) {
                messageInput.value = `Search for: ${searchTerm}`;
                sendMessage();
            }
        }
        
        function showSystemInfo() {
            messageInput.value = "Show system status";
            sendMessage();
        }
        
        // Enter key handler
        messageInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });
        
        // Initial status check
        fetch('/api/status')
            .then(response => response.json())
            .then(status => updateSystemStatus(status))
            .catch(error => console.error('Error fetching status:', error));
    </script>
</body>
</html>
        '''

    def initialize_ultra_async(self):
        """Initialize ULTRA system asynchronously"""
        try:
            logger.info("Initializing ULTRA system...")

            # Import ULTRA components
            from ultra.enhancements import get_enhancement_system, EnhancementConfig

            # Create configuration
            config = EnhancementConfig(
                enable_wikipedia=True,
                enable_scientific_databases=True,
                enable_pretrained_models=False,  # Disable for performance
                enable_voice_input=False,
                enable_voice_output=False,
                enable_image_processing=False,
                enable_internet_training=True
            )

            # Initialize enhancement system
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            self.enhancement_system = loop.run_until_complete(
                get_enhancement_system(config)
            )

            # Update system status
            self.system_status = {
                'initialized': True,
                'components': self.enhancement_system.get_enhancement_status(),
                'last_update': datetime.now().isoformat()
            }

            # Notify clients
            self.socketio.emit('status_update', self.system_status)
            self.socketio.emit('message_update', {
                'type': 'system',
                'content': '✅ ULTRA AGI system initialized successfully!<br>All enhancement components are ready.',
                'timestamp': datetime.now().isoformat()
            })

            logger.info("ULTRA system initialized successfully")

        except Exception as e:
            logger.error(f"ULTRA initialization failed: {e}")
            self.system_status['initialized'] = False
            self.socketio.emit('message_update', {
                'type': 'error',
                'content': f'❌ System initialization failed: {e}',
                'timestamp': datetime.now().isoformat()
            })

    def process_message_async(self, message):
        """Process user message asynchronously"""
        try:
            # Add user message to history
            self.conversation_history.append({
                'type': 'user',
                'content': f'👤 You: {message}',
                'timestamp': datetime.now().isoformat()
            })

            # Generate response
            if self.enhancement_system:
                # Generate base response
                base_response = self.generate_base_response(message)

                # Enhance with ULTRA system
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

                enhanced_response = loop.run_until_complete(
                    self.enhancement_system.enhance_response(message, base_response)
                )

                response_content = f'🤖 ULTRA: {enhanced_response}'
            else:
                response_content = f'🤖 ULTRA: {self.generate_base_response(message)}'

            # Add response to history
            self.conversation_history.append({
                'type': 'ultra',
                'content': response_content,
                'timestamp': datetime.now().isoformat()
            })

            # Send response to clients
            self.socketio.emit('message_update', {
                'type': 'ultra',
                'content': response_content,
                'timestamp': datetime.now().isoformat()
            })

        except Exception as e:
            logger.error(f"Message processing failed: {e}")
            error_message = f'❌ Error processing message: {e}'

            self.conversation_history.append({
                'type': 'error',
                'content': error_message,
                'timestamp': datetime.now().isoformat()
            })

            self.socketio.emit('message_update', {
                'type': 'error',
                'content': error_message,
                'timestamp': datetime.now().isoformat()
            })

    def train_on_topic_async(self, topic):
        """Train ULTRA on topic asynchronously"""
        try:
            if self.enhancement_system and self.enhancement_system.internet_trainer:
                self.socketio.emit('message_update', {
                    'type': 'system',
                    'content': f'🌐 Training ULTRA on topic: {topic}',
                    'timestamp': datetime.now().isoformat()
                })

                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

                result = loop.run_until_complete(
                    self.enhancement_system.train_from_internet([topic])
                )

                result_message = f'''✅ Training completed on '{topic}':
• Articles collected: {result.get('articles_collected', 0)}
• Sources used: {', '.join(result.get('sources_used', []))}
• Training time: {result.get('training_time', 0):.2f}s'''

                self.socketio.emit('message_update', {
                    'type': 'system',
                    'content': result_message,
                    'timestamp': datetime.now().isoformat()
                })
            else:
                self.socketio.emit('message_update', {
                    'type': 'error',
                    'content': '❌ Internet training not available',
                    'timestamp': datetime.now().isoformat()
                })

        except Exception as e:
            logger.error(f"Training failed: {e}")
            self.socketio.emit('message_update', {
                'type': 'error',
                'content': f'❌ Training failed: {e}',
                'timestamp': datetime.now().isoformat()
            })

    def generate_base_response(self, message):
        """Generate base response for message"""
        message_lower = message.lower()

        if any(word in message_lower for word in ['hello', 'hi', 'hey']):
            return "Hello! I'm ULTRA, your Advanced AGI assistant. How can I help you today?"

        elif any(word in message_lower for word in ['status', 'system']):
            if self.enhancement_system:
                status = self.enhancement_system.get_enhancement_status()
                working_components = sum(1 for enabled in status.values() if enabled)
                total_components = len(status)

                return f'''📊 ULTRA System Status:
• Working components: {working_components}/{total_components}
• Conversation history: {len(self.conversation_history)} messages
• System initialized: {'Yes' if self.system_status['initialized'] else 'No'}
• Last update: {self.system_status.get('last_update', 'Unknown')}'''
            else:
                return "❌ Enhancement system not initialized"

        elif any(word in message_lower for word in ['what', 'explain', 'tell me']):
            return f"I understand you're asking about: '{message}'. Let me provide comprehensive information using my integrated knowledge systems."

        elif any(word in message_lower for word in ['search for']):
            search_term = message.replace('search for:', '').replace('Search for:', '').strip()
            return f"🔍 Searching knowledge bases for: '{search_term}'"

        else:
            return f"I'm processing your query: '{message}'. My neural networks are analyzing this through multiple cognitive pathways."

    def run(self, host='localhost', port=5000, debug=False):
        """Run the web GUI server"""
        try:
            print(f"🌐 Starting ULTRA Web GUI on http://{host}:{port}")
            self.socketio.run(self.app, host=host, port=port, debug=debug)
        except Exception as e:
            logger.error(f"Web GUI server failed: {e}")

def main():
    """Main function to start ULTRA Web GUI"""
    try:
        print("🚀 Starting ULTRA AGI Web GUI...")

        if not HAS_FLASK:
            print("❌ Flask not available. Install with: pip install flask flask-socketio")
            return

        gui = ULTRAWebGUI()
        gui.run(host='0.0.0.0', port=8080, debug=False)

    except Exception as e:
        print(f"❌ Failed to start ULTRA Web GUI: {e}")
        logger.error(f"Web GUI startup failed: {e}")

if __name__ == "__main__":
    main()
