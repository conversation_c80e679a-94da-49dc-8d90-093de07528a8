#!/usr/bin/env python3
"""
ULTRA Claude-Style GUI
=====================

Modern, professional, and simple GUI like <PERSON> for talking to ULTRA.
Clean interface focused on conversation experience.
"""

from flask import Flask, render_template, request, jsonify, send_from_directory
from flask_socketio import Socket<PERSON>, emit
import asyncio
import threading
import time
import sys
import os
from pathlib import Path
import json

# Add ULTRA to path
ultra_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(ultra_root))

app = Flask(__name__)
app.config['SECRET_KEY'] = 'ultra-claude-style-gui'
socketio = SocketIO(app, cors_allowed_origins="*")

# Global ULTRA backend instance
ultra_backend = None

def initialize_ultra_backend():
    """Initialize ULTRA backend for processing"""
    global ultra_backend
    try:
        from ultra.unified_backend import ULTRAUnifiedBackend, ULTRARequest
        
        print("🔧 Initializing ULTRA backend...")
        ultra_backend = ULTRAUnifiedBackend()
        
        # Initialize in separate thread to avoid blocking
        def init_async():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(ultra_backend.initialize_system())
            print("✅ ULTRA backend initialized successfully")
        
        init_thread = threading.Thread(target=init_async, daemon=True)
        init_thread.start()
        
        return True
    except Exception as e:
        print(f"❌ Failed to initialize ULTRA backend: {e}")
        return False

@app.route('/')
def index():
    """Main chat interface"""
    return render_template('claude_style_chat.html')

@app.route('/static/<path:filename>')
def static_files(filename):
    """Serve static files"""
    return send_from_directory('static', filename)

@socketio.on('connect')
def handle_connect():
    """Handle client connection"""
    print("🔗 Client connected")
    emit('status', {'message': 'Connected to ULTRA', 'type': 'system'})

@socketio.on('disconnect')
def handle_disconnect():
    """Handle client disconnection"""
    print("🔌 Client disconnected")

@socketio.on('send_message')
def handle_message(data):
    """Handle incoming message from user"""
    user_message = data.get('message', '').strip()
    
    if not user_message:
        return
    
    print(f"💬 User: {user_message}")
    
    # Echo user message
    emit('user_message', {'message': user_message, 'timestamp': time.time()})
    
    # Show typing indicator
    emit('typing', {'typing': True})
    
    # Process through ULTRA
    def process_message():
        try:
            if ultra_backend:
                # Create async loop for processing
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
                # Create ULTRA request
                from ultra.unified_backend import ULTRARequest
                request = ULTRARequest(
                    operation="chat",
                    input_data=user_message,
                    options={"authentic_processing": True}
                )
                
                # Process through ULTRA 8-core architecture
                response = loop.run_until_complete(
                    ultra_backend.process_unified_request(request, None)
                )
                
                if response.success:
                    ultra_response = str(response.result)
                    processing_time = response.processing_time
                    
                    print(f"🧠 ULTRA ({processing_time:.3f}s): {ultra_response}")
                    
                    # Send ULTRA response
                    socketio.emit('ultra_response', {
                        'message': ultra_response,
                        'timestamp': time.time(),
                        'processing_time': processing_time,
                        'authentic': True
                    })
                else:
                    error_msg = f"Error: {response.result}"
                    socketio.emit('ultra_response', {
                        'message': error_msg,
                        'timestamp': time.time(),
                        'processing_time': 0,
                        'authentic': False
                    })
                
                loop.close()
            else:
                # Fallback response if backend not ready
                socketio.emit('ultra_response', {
                    'message': "ULTRA backend is still initializing. Please wait a moment and try again.",
                    'timestamp': time.time(),
                    'processing_time': 0,
                    'authentic': False
                })
                
        except Exception as e:
            print(f"❌ Error processing message: {e}")
            socketio.emit('ultra_response', {
                'message': f"I encountered an error while processing your message: {str(e)}",
                'timestamp': time.time(),
                'processing_time': 0,
                'authentic': False
            })
        finally:
            # Hide typing indicator
            socketio.emit('typing', {'typing': False})
    
    # Process in background thread
    threading.Thread(target=process_message, daemon=True).start()

def create_templates():
    """Create HTML template for Claude-style interface"""
    templates_dir = Path(__file__).parent / 'templates'
    templates_dir.mkdir(exist_ok=True)
    
    html_content = '''<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ULTRA - AI Assistant</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8f9fa;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .header {
            background: white;
            border-bottom: 1px solid #e5e7eb;
            padding: 1rem 2rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: 600;
            color: #1f2937;
        }

        .status {
            font-size: 0.875rem;
            color: #6b7280;
        }

        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            max-width: 800px;
            margin: 0 auto;
            width: 100%;
            padding: 0 1rem;
        }

        .messages {
            flex: 1;
            overflow-y: auto;
            padding: 2rem 0;
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }

        .message {
            display: flex;
            gap: 0.75rem;
            max-width: 100%;
        }

        .message.user {
            flex-direction: row-reverse;
        }

        .avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 0.875rem;
            flex-shrink: 0;
        }

        .avatar.user {
            background: #3b82f6;
            color: white;
        }

        .avatar.ultra {
            background: #10b981;
            color: white;
        }

        .message-content {
            background: white;
            border-radius: 0.75rem;
            padding: 1rem 1.25rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            max-width: 70%;
            word-wrap: break-word;
        }

        .message.user .message-content {
            background: #3b82f6;
            color: white;
        }

        .message-meta {
            font-size: 0.75rem;
            color: #6b7280;
            margin-top: 0.5rem;
        }

        .typing-indicator {
            display: none;
            align-items: center;
            gap: 0.75rem;
            padding: 1rem 0;
        }

        .typing-dots {
            display: flex;
            gap: 0.25rem;
        }

        .typing-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #6b7280;
            animation: typing 1.4s infinite ease-in-out;
        }

        .typing-dot:nth-child(1) { animation-delay: -0.32s; }
        .typing-dot:nth-child(2) { animation-delay: -0.16s; }

        @keyframes typing {
            0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
            40% { transform: scale(1); opacity: 1; }
        }

        .input-container {
            background: white;
            border-top: 1px solid #e5e7eb;
            padding: 1.5rem 2rem;
        }

        .input-wrapper {
            max-width: 800px;
            margin: 0 auto;
            display: flex;
            gap: 0.75rem;
            align-items: flex-end;
        }

        .message-input {
            flex: 1;
            border: 1px solid #d1d5db;
            border-radius: 0.75rem;
            padding: 0.75rem 1rem;
            font-size: 1rem;
            resize: none;
            min-height: 44px;
            max-height: 120px;
            font-family: inherit;
        }

        .message-input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .send-button {
            background: #3b82f6;
            color: white;
            border: none;
            border-radius: 0.75rem;
            padding: 0.75rem 1.5rem;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .send-button:hover {
            background: #2563eb;
        }

        .send-button:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }

        .welcome-message {
            text-align: center;
            color: #6b7280;
            padding: 2rem;
            font-size: 1.125rem;
        }

        .welcome-title {
            font-size: 2rem;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 0.5rem;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="logo">ULTRA</div>
        <div class="status" id="status">Connecting...</div>
    </div>

    <div class="chat-container">
        <div class="messages" id="messages">
            <div class="welcome-message">
                <div class="welcome-title">Welcome to ULTRA</div>
                <p>Your advanced AI assistant with authentic 8-core processing</p>
            </div>
        </div>

        <div class="typing-indicator" id="typing">
            <div class="avatar ultra">U</div>
            <div class="typing-dots">
                <div class="typing-dot"></div>
                <div class="typing-dot"></div>
                <div class="typing-dot"></div>
            </div>
        </div>
    </div>

    <div class="input-container">
        <div class="input-wrapper">
            <textarea 
                id="messageInput" 
                class="message-input" 
                placeholder="Message ULTRA..."
                rows="1"
            ></textarea>
            <button id="sendButton" class="send-button">Send</button>
        </div>
    </div>

    <script>
        const socket = io();
        const messages = document.getElementById('messages');
        const messageInput = document.getElementById('messageInput');
        const sendButton = document.getElementById('sendButton');
        const status = document.getElementById('status');
        const typing = document.getElementById('typing');

        // Socket events
        socket.on('connect', () => {
            status.textContent = 'Connected';
        });

        socket.on('status', (data) => {
            status.textContent = data.message;
        });

        socket.on('user_message', (data) => {
            addMessage(data.message, 'user', data.timestamp);
        });

        socket.on('ultra_response', (data) => {
            addMessage(data.message, 'ultra', data.timestamp, data.processing_time, data.authentic);
        });

        socket.on('typing', (data) => {
            typing.style.display = data.typing ? 'flex' : 'none';
            if (!data.typing) {
                scrollToBottom();
            }
        });

        // Add message to chat
        function addMessage(text, sender, timestamp, processingTime = null, authentic = null) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}`;

            const avatar = document.createElement('div');
            avatar.className = `avatar ${sender}`;
            avatar.textContent = sender === 'user' ? 'Y' : 'U';

            const content = document.createElement('div');
            content.className = 'message-content';
            content.textContent = text;

            if (processingTime !== null) {
                const meta = document.createElement('div');
                meta.className = 'message-meta';
                meta.textContent = `${processingTime.toFixed(3)}s • ${authentic ? 'Authentic 8-core processing' : 'Fallback response'}`;
                content.appendChild(meta);
            }

            messageDiv.appendChild(avatar);
            messageDiv.appendChild(content);

            // Remove welcome message if it exists
            const welcome = messages.querySelector('.welcome-message');
            if (welcome) {
                welcome.remove();
            }

            messages.appendChild(messageDiv);
            scrollToBottom();
        }

        // Send message
        function sendMessage() {
            const text = messageInput.value.trim();
            if (!text) return;

            socket.emit('send_message', { message: text });
            messageInput.value = '';
            adjustTextareaHeight();
        }

        // Auto-resize textarea
        function adjustTextareaHeight() {
            messageInput.style.height = 'auto';
            messageInput.style.height = Math.min(messageInput.scrollHeight, 120) + 'px';
        }

        // Scroll to bottom
        function scrollToBottom() {
            messages.scrollTop = messages.scrollHeight;
        }

        // Event listeners
        sendButton.addEventListener('click', sendMessage);

        messageInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });

        messageInput.addEventListener('input', adjustTextareaHeight);

        // Initial setup
        adjustTextareaHeight();
    </script>
</body>
</html>'''
    
    with open(templates_dir / 'claude_style_chat.html', 'w') as f:
        f.write(html_content)

def main():
    """Main function to run the Claude-style GUI"""
    print("🚀 Starting ULTRA Claude-Style GUI...")
    
    # Create templates
    create_templates()
    
    # Initialize ULTRA backend
    if initialize_ultra_backend():
        print("✅ ULTRA backend initialization started")
    else:
        print("⚠️ ULTRA backend initialization failed - using fallback mode")
    
    # Start the web server
    print("🌐 Starting web server...")
    print("📱 Access ULTRA at: http://localhost:3000")
    print("💬 Modern Claude-style interface ready!")
    
    try:
        socketio.run(app, host='0.0.0.0', port=3000, debug=False)
    except KeyboardInterrupt:
        print("\n⏹️ ULTRA Claude-Style GUI stopped")

if __name__ == "__main__":
    main()
