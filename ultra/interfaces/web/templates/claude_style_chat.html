<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ULTRA - AI Assistant</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8f9fa;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .header {
            background: white;
            border-bottom: 1px solid #e5e7eb;
            padding: 1rem 2rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: 600;
            color: #1f2937;
        }

        .status {
            font-size: 0.875rem;
            color: #6b7280;
        }

        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            max-width: 800px;
            margin: 0 auto;
            width: 100%;
            padding: 0 1rem;
        }

        .messages {
            flex: 1;
            overflow-y: auto;
            padding: 2rem 0;
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }

        .message {
            display: flex;
            gap: 0.75rem;
            max-width: 100%;
        }

        .message.user {
            flex-direction: row-reverse;
        }

        .avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 0.875rem;
            flex-shrink: 0;
        }

        .avatar.user {
            background: #3b82f6;
            color: white;
        }

        .avatar.ultra {
            background: #10b981;
            color: white;
        }

        .message-content {
            background: white;
            border-radius: 0.75rem;
            padding: 1rem 1.25rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            max-width: 70%;
            word-wrap: break-word;
        }

        .message.user .message-content {
            background: #3b82f6;
            color: white;
        }

        .message-meta {
            font-size: 0.75rem;
            color: #6b7280;
            margin-top: 0.5rem;
        }

        .typing-indicator {
            display: none;
            align-items: center;
            gap: 0.75rem;
            padding: 1rem 0;
        }

        .typing-dots {
            display: flex;
            gap: 0.25rem;
        }

        .typing-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #6b7280;
            animation: typing 1.4s infinite ease-in-out;
        }

        .typing-dot:nth-child(1) { animation-delay: -0.32s; }
        .typing-dot:nth-child(2) { animation-delay: -0.16s; }

        @keyframes typing {
            0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
            40% { transform: scale(1); opacity: 1; }
        }

        .input-container {
            background: white;
            border-top: 1px solid #e5e7eb;
            padding: 1.5rem 2rem;
        }

        .input-wrapper {
            max-width: 800px;
            margin: 0 auto;
            display: flex;
            gap: 0.75rem;
            align-items: flex-end;
        }

        .message-input {
            flex: 1;
            border: 1px solid #d1d5db;
            border-radius: 0.75rem;
            padding: 0.75rem 1rem;
            font-size: 1rem;
            resize: none;
            min-height: 44px;
            max-height: 120px;
            font-family: inherit;
        }

        .message-input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .send-button {
            background: #3b82f6;
            color: white;
            border: none;
            border-radius: 0.75rem;
            padding: 0.75rem 1.5rem;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .send-button:hover {
            background: #2563eb;
        }

        .send-button:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }

        .welcome-message {
            text-align: center;
            color: #6b7280;
            padding: 2rem;
            font-size: 1.125rem;
        }

        .welcome-title {
            font-size: 2rem;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 0.5rem;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="logo">ULTRA</div>
        <div class="status" id="status">Connecting...</div>
    </div>

    <div class="chat-container">
        <div class="messages" id="messages">
            <div class="welcome-message">
                <div class="welcome-title">Welcome to ULTRA</div>
                <p>Your advanced AI assistant with authentic 8-core processing</p>
            </div>
        </div>

        <div class="typing-indicator" id="typing">
            <div class="avatar ultra">U</div>
            <div class="typing-dots">
                <div class="typing-dot"></div>
                <div class="typing-dot"></div>
                <div class="typing-dot"></div>
            </div>
        </div>
    </div>

    <div class="input-container">
        <div class="input-wrapper">
            <textarea 
                id="messageInput" 
                class="message-input" 
                placeholder="Message ULTRA..."
                rows="1"
            ></textarea>
            <button id="sendButton" class="send-button">Send</button>
        </div>
    </div>

    <script>
        const socket = io();
        const messages = document.getElementById('messages');
        const messageInput = document.getElementById('messageInput');
        const sendButton = document.getElementById('sendButton');
        const status = document.getElementById('status');
        const typing = document.getElementById('typing');

        // Socket events
        socket.on('connect', () => {
            status.textContent = 'Connected';
        });

        socket.on('status', (data) => {
            status.textContent = data.message;
        });

        socket.on('user_message', (data) => {
            addMessage(data.message, 'user', data.timestamp);
        });

        socket.on('ultra_response', (data) => {
            addMessage(data.message, 'ultra', data.timestamp, data.processing_time, data.authentic, data.cores_used);
        });

        socket.on('typing', (data) => {
            typing.style.display = data.typing ? 'flex' : 'none';
            if (!data.typing) {
                scrollToBottom();
            }
        });

        // Add message to chat
        function addMessage(text, sender, timestamp, processingTime = null, authentic = null, coresUsed = null) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}`;

            const avatar = document.createElement('div');
            avatar.className = `avatar ${sender}`;
            avatar.textContent = sender === 'user' ? 'Y' : 'U';

            const content = document.createElement('div');
            content.className = 'message-content';
            content.textContent = text;

            if (processingTime !== null) {
                const meta = document.createElement('div');
                meta.className = 'message-meta';
                const coreText = coresUsed ? `${coresUsed} cores` : 'processing';
                meta.textContent = `${processingTime.toFixed(3)}s • ${authentic ? `Authentic ${coreText}` : 'Fallback response'}`;
                content.appendChild(meta);
            }

            messageDiv.appendChild(avatar);
            messageDiv.appendChild(content);

            // Remove welcome message if it exists
            const welcome = messages.querySelector('.welcome-message');
            if (welcome) {
                welcome.remove();
            }

            messages.appendChild(messageDiv);
            scrollToBottom();
        }

        // Send message
        function sendMessage() {
            const text = messageInput.value.trim();
            if (!text) return;

            socket.emit('send_message', { message: text });
            messageInput.value = '';
            adjustTextareaHeight();
        }

        // Auto-resize textarea
        function adjustTextareaHeight() {
            messageInput.style.height = 'auto';
            messageInput.style.height = Math.min(messageInput.scrollHeight, 120) + 'px';
        }

        // Scroll to bottom
        function scrollToBottom() {
            messages.scrollTop = messages.scrollHeight;
        }

        // Event listeners
        sendButton.addEventListener('click', sendMessage);

        messageInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });

        messageInput.addEventListener('input', adjustTextareaHeight);

        // Initial setup
        adjustTextareaHeight();
    </script>
</body>
</html>