#!/usr/bin/env python3
"""
ULTRA AGI - Graphical User Interface
====================================

Modern GUI interface for ULTRA AGI system with:
- Query window/bar for text input
- Voice activation and control
- Visual component integration
- Real-time system monitoring
- Enhanced conversation display
"""

import sys
import os
import asyncio
import threading
import time
from typing import Dict, List, Any, Optional
import logging

# Add ULTRA to path
sys.path.insert(0, os.path.abspath('.'))
sys.path.insert(0, os.path.abspath('ultra'))

# GUI imports
try:
    import tkinter as tk
    from tkinter import ttk, scrolledtext, messagebox, filedialog
    from tkinter import font as tkFont
    HAS_TKINTER = True
except ImportError:
    print("❌ Tkinter not available. Please install tkinter.")
    HAS_TKINTER = False

try:
    from PIL import Image, ImageTk
    HAS_PIL = True
except ImportError:
    print("⚠️ PIL not available. Some image features disabled.")
    HAS_PIL = False

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ULTRAGUIApp:
    """
    Main ULTRA GUI Application
    """

    def __init__(self):
        if not HAS_TKINTER:
            raise RuntimeError("Tkinter is required for GUI")

        # Initialize main window
        self.root = tk.Tk()
        self.root.title("ULTRA AGI - Advanced Artificial General Intelligence")
        self.root.geometry("1400x900")
        self.root.configure(bg='#1a1a1a')

        # ULTRA system components
        self.ultra_system = None
        self.enhancement_system = None
        self.is_voice_active = False
        self.is_listening = False
        self.conversation_history = []

        # GUI components
        self.setup_styles()
        self.create_widgets()
        self.setup_layout()

        # Initialize ULTRA system in background
        self.init_thread = threading.Thread(target=self.initialize_ultra_async, daemon=True)
        self.init_thread.start()

        logger.info("ULTRA GUI initialized")

    def setup_styles(self):
        """Setup custom styles for the GUI"""
        self.style = ttk.Style()
        self.style.theme_use('clam')

        # Configure colors
        self.colors = {
            'bg_dark': '#1a1a1a',
            'bg_medium': '#2d2d2d',
            'bg_light': '#404040',
            'accent': '#00ff88',
            'accent_dim': '#00cc66',
            'text_primary': '#ffffff',
            'text_secondary': '#cccccc',
            'error': '#ff4444',
            'warning': '#ffaa00',
            'success': '#00ff88'
        }

        # Configure styles
        self.style.configure('Dark.TFrame', background=self.colors['bg_dark'])
        self.style.configure('Medium.TFrame', background=self.colors['bg_medium'])
        self.style.configure('Light.TFrame', background=self.colors['bg_light'])

        self.style.configure('Accent.TButton',
                           background=self.colors['accent'],
                           foreground='black',
                           font=('Arial', 10, 'bold'))

        self.style.configure('Dark.TButton',
                           background=self.colors['bg_light'],
                           foreground=self.colors['text_primary'],
                           font=('Arial', 9))

        # Fonts
        self.fonts = {
            'title': tkFont.Font(family='Arial', size=16, weight='bold'),
            'subtitle': tkFont.Font(family='Arial', size=12, weight='bold'),
            'body': tkFont.Font(family='Arial', size=10),
            'mono': tkFont.Font(family='Courier', size=9)
        }

    def create_widgets(self):
        """Create all GUI widgets"""

        # Main container
        self.main_frame = ttk.Frame(self.root, style='Dark.TFrame')

        # Header
        self.create_header()

        # Main content area
        self.create_main_content()

        # Query bar
        self.create_query_bar()

        # Status bar
        self.create_status_bar()

        # Side panels
        self.create_side_panels()

    def create_header(self):
        """Create header with title and controls"""
        self.header_frame = ttk.Frame(self.main_frame, style='Medium.TFrame')

        # Title
        self.title_label = tk.Label(
            self.header_frame,
            text="🤖 ULTRA AGI",
            font=self.fonts['title'],
            bg=self.colors['bg_medium'],
            fg=self.colors['accent']
        )

        # System status indicator
        self.status_indicator = tk.Label(
            self.header_frame,
            text="⚪ Initializing...",
            font=self.fonts['body'],
            bg=self.colors['bg_medium'],
            fg=self.colors['text_secondary']
        )

        # Voice activation button
        self.voice_button = ttk.Button(
            self.header_frame,
            text="🎤 Voice",
            style='Accent.TButton',
            command=self.toggle_voice_activation
        )

        # Settings button
        self.settings_button = ttk.Button(
            self.header_frame,
            text="⚙️ Settings",
            style='Dark.TButton',
            command=self.open_settings
        )

    def create_main_content(self):
        """Create main conversation area"""
        self.content_frame = ttk.Frame(self.main_frame, style='Dark.TFrame')

        # Conversation display
        self.conversation_frame = ttk.Frame(self.content_frame, style='Medium.TFrame')

        self.conversation_display = scrolledtext.ScrolledText(
            self.conversation_frame,
            wrap=tk.WORD,
            width=80,
            height=25,
            bg=self.colors['bg_medium'],
            fg=self.colors['text_primary'],
            font=self.fonts['body'],
            insertbackground=self.colors['accent'],
            selectbackground=self.colors['accent_dim']
        )

        # Configure text tags for different message types
        self.conversation_display.tag_configure('user', foreground=self.colors['accent'])
        self.conversation_display.tag_configure('ultra', foreground=self.colors['text_primary'])
        self.conversation_display.tag_configure('system', foreground=self.colors['text_secondary'])
        self.conversation_display.tag_configure('error', foreground=self.colors['error'])
        self.conversation_display.tag_configure('enhancement', foreground=self.colors['warning'])

    def create_query_bar(self):
        """Create query input bar"""
        self.query_frame = ttk.Frame(self.main_frame, style='Light.TFrame')

        # Query input
        self.query_var = tk.StringVar()
        self.query_entry = tk.Entry(
            self.query_frame,
            textvariable=self.query_var,
            font=self.fonts['body'],
            bg=self.colors['bg_light'],
            fg=self.colors['text_primary'],
            insertbackground=self.colors['accent'],
            relief='flat',
            bd=5
        )

        # Send button
        self.send_button = ttk.Button(
            self.query_frame,
            text="Send",
            style='Accent.TButton',
            command=self.send_query
        )

        # Voice input button
        self.voice_input_button = ttk.Button(
            self.query_frame,
            text="🎤",
            style='Dark.TButton',
            command=self.start_voice_input
        )

        # Image upload button
        self.image_button = ttk.Button(
            self.query_frame,
            text="🖼️",
            style='Dark.TButton',
            command=self.upload_image
        )

        # Clear button
        self.clear_button = ttk.Button(
            self.query_frame,
            text="Clear",
            style='Dark.TButton',
            command=self.clear_conversation
        )

        # Bind Enter key to send
        self.query_entry.bind('<Return>', lambda e: self.send_query())

    def create_status_bar(self):
        """Create status bar"""
        self.status_frame = ttk.Frame(self.main_frame, style='Medium.TFrame')

        self.status_text = tk.Label(
            self.status_frame,
            text="Ready",
            font=self.fonts['body'],
            bg=self.colors['bg_medium'],
            fg=self.colors['text_secondary'],
            anchor='w'
        )

        # Component status indicators
        self.component_status = {}
        components = ['Core', 'Enhancement', 'Knowledge', 'Voice', 'Visual']

        for component in components:
            indicator = tk.Label(
                self.status_frame,
                text=f"{component}: ❌",
                font=('Arial', 8),
                bg=self.colors['bg_medium'],
                fg=self.colors['text_secondary']
            )
            self.component_status[component] = indicator

    def create_side_panels(self):
        """Create side panels for controls and information"""

        # Right panel
        self.right_panel = ttk.Frame(self.main_frame, style='Medium.TFrame')

        # Enhancement controls
        self.create_enhancement_controls()

        # System monitor
        self.create_system_monitor()

        # Quick actions
        self.create_quick_actions()

    def create_enhancement_controls(self):
        """Create enhancement system controls"""
        self.enhancement_frame = ttk.LabelFrame(
            self.right_panel,
            text="Enhancement Controls",
            style='Medium.TFrame'
        )

        # Enhancement toggles
        self.enhancement_vars = {}
        enhancements = [
            ('Wikipedia', 'enable_wikipedia'),
            ('Scientific DB', 'enable_scientific_databases'),
            ('Language Models', 'enable_pretrained_models'),
            ('Voice Input', 'enable_voice_input'),
            ('Voice Output', 'enable_voice_output'),
            ('Image Processing', 'enable_image_processing'),
            ('Internet Training', 'enable_internet_training')
        ]

        for name, var_name in enhancements:
            var = tk.BooleanVar(value=True)
            self.enhancement_vars[var_name] = var

            checkbox = tk.Checkbutton(
                self.enhancement_frame,
                text=name,
                variable=var,
                bg=self.colors['bg_medium'],
                fg=self.colors['text_primary'],
                selectcolor=self.colors['bg_light'],
                activebackground=self.colors['bg_light'],
                font=self.fonts['body'],
                command=self.update_enhancements
            )
            checkbox.pack(anchor='w', padx=5, pady=2)

    def create_system_monitor(self):
        """Create system monitoring panel"""
        self.monitor_frame = ttk.LabelFrame(
            self.right_panel,
            text="System Monitor",
            style='Medium.TFrame'
        )

        # Performance metrics
        self.metrics_text = tk.Text(
            self.monitor_frame,
            height=8,
            width=25,
            bg=self.colors['bg_light'],
            fg=self.colors['text_secondary'],
            font=self.fonts['mono'],
            relief='flat'
        )
        self.metrics_text.pack(fill='both', expand=True, padx=5, pady=5)

    def create_quick_actions(self):
        """Create quick action buttons"""
        self.actions_frame = ttk.LabelFrame(
            self.right_panel,
            text="Quick Actions",
            style='Medium.TFrame'
        )

        actions = [
            ("🧠 Test Reasoning", self.test_reasoning),
            ("📚 Train on Topic", self.train_on_topic),
            ("🔍 Knowledge Search", self.knowledge_search),
            ("📊 System Status", self.show_system_status),
            ("💾 Save Conversation", self.save_conversation),
            ("🔄 Reset System", self.reset_system)
        ]

        for text, command in actions:
            button = ttk.Button(
                self.actions_frame,
                text=text,
                style='Dark.TButton',
                command=command
            )
            button.pack(fill='x', padx=5, pady=2)

    def setup_layout(self):
        """Setup the layout of all components"""

        # Main frame
        self.main_frame.pack(fill='both', expand=True, padx=5, pady=5)

        # Header
        self.header_frame.pack(fill='x', pady=(0, 5))
        self.title_label.pack(side='left', padx=5)
        self.status_indicator.pack(side='left', padx=20)
        self.settings_button.pack(side='right', padx=5)
        self.voice_button.pack(side='right', padx=5)

        # Content area with side panel
        content_container = ttk.Frame(self.main_frame, style='Dark.TFrame')
        content_container.pack(fill='both', expand=True, pady=(0, 5))

        # Main content
        self.content_frame.pack(side='left', fill='both', expand=True, padx=(0, 5))
        self.conversation_frame.pack(fill='both', expand=True)
        self.conversation_display.pack(fill='both', expand=True, padx=5, pady=5)

        # Right panel
        self.right_panel.pack(side='right', fill='y', padx=(5, 0))
        self.enhancement_frame.pack(fill='x', pady=(0, 5))
        self.monitor_frame.pack(fill='both', expand=True, pady=(0, 5))
        self.actions_frame.pack(fill='x')

        # Query bar
        self.query_frame.pack(fill='x', pady=(0, 5))
        self.query_entry.pack(side='left', fill='x', expand=True, padx=5)
        self.send_button.pack(side='right', padx=2)
        self.voice_input_button.pack(side='right', padx=2)
        self.image_button.pack(side='right', padx=2)
        self.clear_button.pack(side='right', padx=2)

        # Status bar
        self.status_frame.pack(fill='x')
        self.status_text.pack(side='left', padx=5)

        for indicator in self.component_status.values():
            indicator.pack(side='right', padx=5)

    def initialize_ultra_async(self):
        """Initialize ULTRA system in background thread"""
        try:
            self.update_status("Initializing ULTRA system...")

            # Import and initialize ULTRA components
            from ultra.enhancements import get_enhancement_system, EnhancementConfig

            # Create enhancement config based on GUI settings
            config = EnhancementConfig()
            for var_name, var in self.enhancement_vars.items():
                setattr(config, var_name, var.get())

            # Initialize enhancement system
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            self.enhancement_system = loop.run_until_complete(
                get_enhancement_system(config)
            )

            # Update status indicators
            self.root.after(0, self.update_component_status)
            self.root.after(0, lambda: self.update_status("ULTRA system ready"))
            self.root.after(0, lambda: self.update_status_indicator("🟢 Online"))

            # Add welcome message
            self.root.after(0, lambda: self.add_message(
                "🤖 ULTRA AGI system initialized successfully!\n"
                "All enhancement components are ready.\n"
                "You can now ask questions, use voice input, or upload images.",
                "system"
            ))

        except Exception as e:
            logger.error(f"ULTRA initialization failed: {e}")
            self.root.after(0, lambda: self.update_status(f"Initialization failed: {e}"))
            self.root.after(0, lambda: self.update_status_indicator("🔴 Error"))

    def update_status(self, message):
        """Update status bar message"""
        self.status_text.config(text=message)

    def update_status_indicator(self, status):
        """Update main status indicator"""
        self.status_indicator.config(text=status)

    def update_component_status(self):
        """Update component status indicators"""
        if self.enhancement_system:
            status = self.enhancement_system.get_enhancement_status()

            component_map = {
                'Core': True,  # Always true if we got this far
                'Enhancement': status.get('system_initialized', False),
                'Knowledge': status.get('external_knowledge', False),
                'Voice': status.get('voice_interface', False),
                'Visual': status.get('visual_processing', False)
            }

            for component, is_active in component_map.items():
                icon = "✅" if is_active else "❌"
                self.component_status[component].config(text=f"{component}: {icon}")

    def send_query(self):
        """Send user query to ULTRA"""
        query = self.query_var.get().strip()
        if not query:
            return

        # Clear input
        self.query_var.set("")

        # Add user message
        self.add_message(f"👤 You: {query}", "user")

        # Process in background thread
        thread = threading.Thread(
            target=self.process_query_async,
            args=(query,),
            daemon=True
        )
        thread.start()

    def process_query_async(self, query):
        """Process query asynchronously"""
        try:
            self.root.after(0, lambda: self.update_status("Processing query..."))

            if self.enhancement_system:
                # Generate base response
                base_response = self.generate_base_response(query)

                # Enhance with ULTRA system
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

                enhanced_response = loop.run_until_complete(
                    self.enhancement_system.enhance_response(query, base_response)
                )

                # Add response to conversation
                self.root.after(0, lambda: self.add_message(
                    f"🤖 ULTRA: {enhanced_response}", "ultra"
                ))
            else:
                # Fallback response
                response = self.generate_base_response(query)
                self.root.after(0, lambda: self.add_message(
                    f"🤖 ULTRA: {response}", "ultra"
                ))

            self.root.after(0, lambda: self.update_status("Ready"))

        except Exception as e:
            logger.error(f"Query processing failed: {e}")
            self.root.after(0, lambda: self.add_message(
                f"❌ Error processing query: {e}", "error"
            ))
            self.root.after(0, lambda: self.update_status("Ready"))

    def generate_base_response(self, query):
        """Generate base response for query"""
        # Simple pattern-based responses for fallback
        query_lower = query.lower()

        if any(word in query_lower for word in ['hello', 'hi', 'hey']):
            return "Hello! I'm ULTRA, your Advanced AGI assistant. How can I help you today?"

        elif any(word in query_lower for word in ['what', 'explain', 'tell me']):
            return f"I understand you're asking about: '{query}'. Let me provide you with comprehensive information using my integrated knowledge systems."

        elif any(word in query_lower for word in ['how', 'why', 'when']):
            return f"That's an excellent question about '{query}'. I'll analyze this using my reasoning capabilities and external knowledge sources."

        else:
            return f"I'm processing your query: '{query}'. My neural networks are analyzing this through multiple cognitive pathways to provide you with the most comprehensive response."

    def add_message(self, message, message_type="system"):
        """Add message to conversation display"""
        self.conversation_display.insert(tk.END, message + "\n\n")

        # Apply styling based on message type
        start_line = self.conversation_display.index(tk.END + "-2l linestart")
        end_line = self.conversation_display.index(tk.END + "-1l lineend")
        self.conversation_display.tag_add(message_type, start_line, end_line)

        # Auto-scroll to bottom
        self.conversation_display.see(tk.END)

        # Store in history
        self.conversation_history.append({
            'message': message,
            'type': message_type,
            'timestamp': time.time()
        })

    def toggle_voice_activation(self):
        """Toggle voice activation"""
        self.is_voice_active = not self.is_voice_active

        if self.is_voice_active:
            self.voice_button.config(text="🎤 Voice ON")
            self.add_message("🎤 Voice activation enabled", "system")
        else:
            self.voice_button.config(text="🎤 Voice")
            self.add_message("🎤 Voice activation disabled", "system")

    def start_voice_input(self):
        """Start voice input"""
        if not self.enhancement_system or not self.enhancement_system.voice_manager:
            self.add_message("❌ Voice interface not available", "error")
            return

        self.add_message("🎤 Listening... (speak now)", "system")
        self.voice_input_button.config(text="🔴", state='disabled')

        # Process voice input in background
        thread = threading.Thread(target=self.process_voice_input, daemon=True)
        thread.start()

    def process_voice_input(self):
        """Process voice input asynchronously"""
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            # Get voice input (this would need actual implementation)
            # For now, simulate voice input
            import time
            time.sleep(2)  # Simulate listening time

            # Simulated voice input
            voice_text = "Hello ULTRA, how are you today?"

            self.root.after(0, lambda: self.voice_input_button.config(text="🎤", state='normal'))
            self.root.after(0, lambda: self.query_var.set(voice_text))
            self.root.after(0, lambda: self.add_message(f"🎤 Voice input: {voice_text}", "system"))

        except Exception as e:
            logger.error(f"Voice input failed: {e}")
            self.root.after(0, lambda: self.voice_input_button.config(text="🎤", state='normal'))
            self.root.after(0, lambda: self.add_message(f"❌ Voice input failed: {e}", "error"))

    def upload_image(self):
        """Upload and analyze image"""
        try:
            from tkinter import filedialog

            file_path = filedialog.askopenfilename(
                title="Select Image",
                filetypes=[
                    ("Image files", "*.png *.jpg *.jpeg *.gif *.bmp"),
                    ("All files", "*.*")
                ]
            )

            if file_path:
                self.add_message(f"🖼️ Analyzing image: {file_path}", "system")

                # Process image in background
                thread = threading.Thread(
                    target=self.process_image_async,
                    args=(file_path,),
                    daemon=True
                )
                thread.start()

        except Exception as e:
            self.add_message(f"❌ Image upload failed: {e}", "error")

    def process_image_async(self, image_path):
        """Process image asynchronously"""
        try:
            if self.enhancement_system and self.enhancement_system.visual_engine:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

                analysis = loop.run_until_complete(
                    self.enhancement_system.process_image(image_path)
                )

                # Format analysis results
                result_text = "🖼️ Image Analysis Results:\n"
                if "description" in analysis:
                    result_text += f"Description: {analysis['description']}\n"
                if "objects" in analysis:
                    objects = [obj['label'] for obj in analysis['objects']]
                    result_text += f"Objects detected: {', '.join(objects)}\n"

                self.root.after(0, lambda: self.add_message(result_text, "enhancement"))
            else:
                self.root.after(0, lambda: self.add_message(
                    "❌ Visual processing not available", "error"
                ))

        except Exception as e:
            logger.error(f"Image processing failed: {e}")
            self.root.after(0, lambda: self.add_message(
                f"❌ Image processing failed: {e}", "error"
            ))

    def clear_conversation(self):
        """Clear conversation display"""
        self.conversation_display.delete(1.0, tk.END)
        self.conversation_history.clear()
        self.add_message("🔄 Conversation cleared", "system")

    def update_enhancements(self):
        """Update enhancement system configuration"""
        if self.enhancement_system:
            # This would require reinitializing the enhancement system
            # For now, just show a message
            self.add_message("⚙️ Enhancement settings updated", "system")

    def open_settings(self):
        """Open settings dialog"""
        settings_window = tk.Toplevel(self.root)
        settings_window.title("ULTRA Settings")
        settings_window.geometry("400x300")
        settings_window.configure(bg=self.colors['bg_medium'])

        # Settings content
        settings_label = tk.Label(
            settings_window,
            text="ULTRA AGI Settings",
            font=self.fonts['subtitle'],
            bg=self.colors['bg_medium'],
            fg=self.colors['text_primary']
        )
        settings_label.pack(pady=20)

        # Add settings controls here
        info_text = tk.Text(
            settings_window,
            height=10,
            width=50,
            bg=self.colors['bg_light'],
            fg=self.colors['text_primary'],
            font=self.fonts['body']
        )
        info_text.pack(padx=20, pady=10)

        info_text.insert(tk.END,
            "ULTRA AGI Configuration\n\n"
            "• Enhancement components can be toggled in the main interface\n"
            "• Voice activation requires microphone permissions\n"
            "• Image processing supports PNG, JPG, GIF formats\n"
            "• Internet training gathers knowledge from multiple sources\n\n"
            "For advanced settings, modify the configuration files directly."
        )
        info_text.config(state='disabled')

    def test_reasoning(self):
        """Test ULTRA reasoning capabilities"""
        test_query = "Explain the relationship between quantum mechanics and consciousness"
        self.query_var.set(test_query)
        self.send_query()

    def train_on_topic(self):
        """Train ULTRA on a specific topic"""
        from tkinter import simpledialog

        topic = simpledialog.askstring(
            "Internet Training",
            "Enter topic to train on:",
            parent=self.root
        )

        if topic:
            self.add_message(f"🌐 Starting internet training on: {topic}", "system")

            # Process training in background
            thread = threading.Thread(
                target=self.process_training_async,
                args=(topic,),
                daemon=True
            )
            thread.start()

    def process_training_async(self, topic):
        """Process training asynchronously"""
        try:
            if self.enhancement_system and self.enhancement_system.internet_trainer:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

                result = loop.run_until_complete(
                    self.enhancement_system.train_from_internet([topic])
                )

                result_text = f"✅ Training completed on '{topic}':\n"
                result_text += f"• Articles collected: {result.get('articles_collected', 0)}\n"
                result_text += f"• Sources used: {', '.join(result.get('sources_used', []))}\n"
                result_text += f"• Training time: {result.get('training_time', 0):.2f}s"

                self.root.after(0, lambda: self.add_message(result_text, "enhancement"))
            else:
                self.root.after(0, lambda: self.add_message(
                    "❌ Internet training not available", "error"
                ))

        except Exception as e:
            logger.error(f"Training failed: {e}")
            self.root.after(0, lambda: self.add_message(
                f"❌ Training failed: {e}", "error"
            ))

    def knowledge_search(self):
        """Search external knowledge"""
        from tkinter import simpledialog

        search_term = simpledialog.askstring(
            "Knowledge Search",
            "Enter search term:",
            parent=self.root
        )

        if search_term:
            self.add_message(f"🔍 Searching knowledge for: {search_term}", "system")

            # Process search in background
            thread = threading.Thread(
                target=self.process_search_async,
                args=(search_term,),
                daemon=True
            )
            thread.start()

    def process_search_async(self, search_term):
        """Process knowledge search asynchronously"""
        try:
            if self.enhancement_system and self.enhancement_system.knowledge_manager:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

                knowledge = loop.run_until_complete(
                    self.enhancement_system.knowledge_manager.get_relevant_knowledge(search_term)
                )

                if knowledge:
                    result_text = f"📚 Knowledge search results for '{search_term}':\n"
                    for source, data in knowledge.items():
                        result_text += f"\n{source.title()}:\n"
                        if isinstance(data, dict):
                            if 'title' in data:
                                result_text += f"• {data['title']}\n"
                            if 'summary' in data:
                                result_text += f"• {data['summary'][:200]}...\n"
                        else:
                            result_text += f"• {str(data)[:200]}...\n"

                    self.root.after(0, lambda: self.add_message(result_text, "enhancement"))
                else:
                    self.root.after(0, lambda: self.add_message(
                        f"No knowledge found for '{search_term}'", "system"
                    ))
            else:
                self.root.after(0, lambda: self.add_message(
                    "❌ Knowledge search not available", "error"
                ))

        except Exception as e:
            logger.error(f"Knowledge search failed: {e}")
            self.root.after(0, lambda: self.add_message(
                f"❌ Knowledge search failed: {e}", "error"
            ))

    def show_system_status(self):
        """Show detailed system status"""
        status_text = "📊 ULTRA System Status:\n\n"

        if self.enhancement_system:
            status = self.enhancement_system.get_enhancement_status()

            status_text += "Enhancement Components:\n"
            for component, enabled in status.items():
                icon = "✅" if enabled else "❌"
                status_text += f"  {icon} {component.replace('_', ' ').title()}\n"

            status_text += f"\nConversation History: {len(self.conversation_history)} messages\n"
            status_text += f"Voice Activation: {'ON' if self.is_voice_active else 'OFF'}\n"
        else:
            status_text += "❌ Enhancement system not initialized\n"

        self.add_message(status_text, "system")

    def save_conversation(self):
        """Save conversation to file"""
        try:
            from tkinter import filedialog

            file_path = filedialog.asksaveasfilename(
                title="Save Conversation",
                defaultextension=".txt",
                filetypes=[
                    ("Text files", "*.txt"),
                    ("All files", "*.*")
                ]
            )

            if file_path:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write("ULTRA AGI Conversation Log\n")
                    f.write("=" * 50 + "\n\n")

                    for entry in self.conversation_history:
                        timestamp = time.strftime(
                            "%Y-%m-%d %H:%M:%S",
                            time.localtime(entry['timestamp'])
                        )
                        f.write(f"[{timestamp}] {entry['message']}\n\n")

                self.add_message(f"💾 Conversation saved to: {file_path}", "system")

        except Exception as e:
            self.add_message(f"❌ Save failed: {e}", "error")

    def reset_system(self):
        """Reset ULTRA system"""
        from tkinter import messagebox

        if messagebox.askyesno("Reset System", "Are you sure you want to reset ULTRA?"):
            self.clear_conversation()
            self.update_status("Resetting system...")

            # Reset in background
            thread = threading.Thread(target=self.reset_system_async, daemon=True)
            thread.start()

    def reset_system_async(self):
        """Reset system asynchronously"""
        try:
            # Reinitialize system
            self.enhancement_system = None
            time.sleep(1)  # Brief pause

            self.root.after(0, lambda: self.update_status_indicator("⚪ Resetting..."))

            # Restart initialization
            self.initialize_ultra_async()

        except Exception as e:
            logger.error(f"System reset failed: {e}")
            self.root.after(0, lambda: self.add_message(f"❌ Reset failed: {e}", "error"))

    def run(self):
        """Start the GUI application"""
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            logger.info("GUI application interrupted")
        except Exception as e:
            logger.error(f"GUI application error: {e}")
        finally:
            logger.info("GUI application closed")

def main():
    """Main function to start ULTRA GUI"""
    try:
        print("🚀 Starting ULTRA AGI GUI...")

        if not HAS_TKINTER:
            print("❌ Tkinter not available. Please install tkinter.")
            return

        app = ULTRAGUIApp()
        app.run()

    except Exception as e:
        print(f"❌ Failed to start ULTRA GUI: {e}")
        logger.error(f"GUI startup failed: {e}")

if __name__ == "__main__":
    main()