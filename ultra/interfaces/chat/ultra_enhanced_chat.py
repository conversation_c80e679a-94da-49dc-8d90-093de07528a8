#!/usr/bin/env python3
"""
ULTRA Enhanced Chat Interface
=============================

Advanced chat interface with all enhancements:
- External knowledge integration (Wikipedia, scientific databases)
- Pre-trained language models for enhanced fluency
- Voice interface capabilities
- Visual processing for images
- Internet training for broader knowledge
"""

import asyncio
import sys
import os
import signal
import logging
from typing import Dict, List, Any, Optional
import time

# Add ULTRA to path
sys.path.insert(0, os.path.abspath('.'))
sys.path.insert(0, os.path.abspath('ultra'))

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ULTRAEnhancedChat:
    """
    Enhanced ULTRA chat interface with all advanced capabilities
    """
    
    def __init__(self):
        self.enhancement_system = None
        self.base_ultra = None
        self.conversation_count = 0
        self.is_voice_mode = False
        self.is_visual_mode = False
        
        # Set up signal handler
        signal.signal(signal.SIGINT, self.signal_handler)
        
        logger.info("ULTRA Enhanced Chat initialized")
    
    async def initialize(self):
        """Initialize all enhancement systems"""
        try:
            print("🚀 ULTRA Enhanced AGI - Initializing Advanced Systems...")
            print("=" * 70)
            
            # Initialize base ULTRA chat capabilities
            await self._initialize_base_ultra()
            
            # Initialize enhancement system
            print("🔧 Loading enhancement systems...")
            try:
                from ultra.enhancements import get_enhancement_system, EnhancementConfig
            except ImportError:
                # Try alternative import paths
                try:
                    sys.path.insert(0, os.path.join(os.getcwd(), 'ultra'))
                    from enhancements import get_enhancement_system, EnhancementConfig
                except ImportError:
                    print("❌ Enhancement system not found. Using fallback mode.")
                    return False
            
            config = EnhancementConfig(
                enable_wikipedia=True,
                enable_scientific_databases=True,
                enable_pretrained_models=True,
                enable_voice_input=True,
                enable_voice_output=True,
                enable_image_processing=True,
                enable_internet_training=True
            )
            
            self.enhancement_system = await get_enhancement_system(config)
            
            # Display initialization status
            await self._display_system_status()
            
            print("✅ ULTRA Enhanced System fully initialized!")
            print("=" * 70)
            
            return True
            
        except Exception as e:
            logger.error(f"Enhanced chat initialization failed: {e}")
            print(f"❌ Initialization failed: {e}")
            return False
    
    async def start_chat(self):
        """Start the enhanced chat interface"""
        print("\n🤖 **ULTRA Enhanced AGI** - Ready for Advanced Interaction!")
        print("=" * 70)
        print("💬 **Available Modes:**")
        print("   • Text Chat - Type your questions")
        print("   • Voice Mode - Say 'voice mode' to enable voice interaction")
        print("   • Visual Mode - Say 'visual mode' to analyze images")
        print("   • Training Mode - Say 'train on [topic]' to learn from internet")
        print("=" * 70)
        print("📝 **Commands:**")
        print("   • 'help' - Show all commands")
        print("   • 'status' - System status")
        print("   • 'voice mode' - Enable voice interaction")
        print("   • 'visual mode' - Enable image processing")
        print("   • 'train on [topic]' - Internet training")
        print("   • 'quit' - Exit")
        print("=" * 70)
        
        while True:
            try:
                # Get user input
                if self.is_voice_mode:
                    user_input = await self._get_voice_input()
                else:
                    user_input = input("\n👤 You: ").strip()
                
                if not user_input:
                    continue
                
                # Handle commands
                if await self._handle_commands(user_input):
                    continue
                
                # Process through enhanced ULTRA
                print("\n🧠 ULTRA Enhanced Processing...")
                response = await self._process_enhanced_input(user_input)
                
                # Output response
                if self.is_voice_mode:
                    await self._output_voice_response(response)
                else:
                    print(f"\n🤖 **ULTRA Enhanced:** {response}")
                
                print("\n" + "-" * 70)
                
            except KeyboardInterrupt:
                self.signal_handler(None, None)
            except EOFError:
                print("\n\n🤖 ULTRA: Session ended. Thank you for the enhanced conversation!")
                break
            except Exception as e:
                logger.error(f"Chat error: {e}")
                print(f"\n❌ Error: {e}")
                print("🤖 ULTRA: I encountered an error, but I'm still here! Please try again.")
    
    async def _initialize_base_ultra(self):
        """Initialize base ULTRA chat capabilities"""
        try:
            # Try multiple import paths for base ULTRA chat
            try:
                from ultra.interfaces.chat.ultra_terminal_chat import ULTRATerminalChat
            except ImportError:
                try:
                    from interfaces.chat.ultra_terminal_chat import ULTRATerminalChat
                except ImportError:
                    from ultra_terminal_chat import ULTRATerminalChat

            self.base_ultra = ULTRATerminalChat()
            print("✅ Base ULTRA chat system loaded")

        except Exception as e:
            logger.warning(f"Base ULTRA chat not available: {e}")
            print("⚠️ Using fallback response system")
            self.base_ultra = None
    
    async def _process_enhanced_input(self, user_input: str) -> str:
        """Process user input through enhanced ULTRA system"""
        try:
            # Get base ULTRA response
            if self.base_ultra:
                base_response = self.base_ultra.generate_ultra_response(user_input)
            else:
                base_response = self._generate_fallback_response(user_input)
            
            # Enhance with all available systems
            if self.enhancement_system:
                enhanced_response = await self.enhancement_system.enhance_response(
                    user_input, 
                    base_response,
                    {"conversation_count": self.conversation_count}
                )
                self.conversation_count += 1
                return enhanced_response
            
            return base_response
            
        except Exception as e:
            logger.error(f"Enhanced processing failed: {e}")
            return f"I encountered an error processing your request: {e}"
    
    async def _handle_commands(self, user_input: str) -> bool:
        """Handle special commands"""
        command = user_input.lower().strip()
        
        if command in ['quit', 'exit', 'bye']:
            print("\n👋 Goodbye! Thank you for exploring ULTRA's enhanced capabilities!")
            sys.exit(0)
        
        elif command == 'help':
            await self._show_help()
            return True
        
        elif command == 'status':
            await self._show_status()
            return True
        
        elif command == 'voice mode':
            await self._toggle_voice_mode()
            return True
        
        elif command == 'visual mode':
            await self._toggle_visual_mode()
            return True
        
        elif command.startswith('train on '):
            topic = command[9:].strip()
            await self._train_on_topic(topic)
            return True
        
        elif command.startswith('analyze image '):
            image_path = command[14:].strip()
            await self._analyze_image(image_path)
            return True
        
        return False
    
    async def _get_voice_input(self) -> str:
        """Get voice input from user"""
        try:
            if self.enhancement_system and self.enhancement_system.voice_manager:
                print("🎤 Listening... (speak now)")
                audio_data = await self.enhancement_system.process_voice_input(None)
                print(f"🎤 You said: {audio_data}")
                return audio_data
            else:
                print("❌ Voice input not available")
                return input("👤 You (text): ").strip()
                
        except Exception as e:
            logger.error(f"Voice input failed: {e}")
            return input("👤 You (fallback to text): ").strip()
    
    async def _output_voice_response(self, response: str):
        """Output response as voice"""
        try:
            if self.enhancement_system and self.enhancement_system.voice_manager:
                print(f"\n🤖 **ULTRA Enhanced:** {response}")
                print("🔊 Playing voice response...")
                
                audio_data = await self.enhancement_system.generate_voice_output(response)
                if audio_data:
                    print("🔊 Voice response generated successfully")
                else:
                    print("❌ Voice generation failed")
            else:
                print(f"\n🤖 **ULTRA Enhanced:** {response}")
                
        except Exception as e:
            logger.error(f"Voice output failed: {e}")
            print(f"\n🤖 **ULTRA Enhanced:** {response}")
    
    async def _train_on_topic(self, topic: str):
        """Train ULTRA on a specific topic"""
        try:
            if not self.enhancement_system or not self.enhancement_system.internet_trainer:
                print("❌ Internet training not available")
                return
            
            print(f"🌐 Training ULTRA on topic: {topic}")
            print("📚 Gathering knowledge from internet sources...")
            
            training_result = await self.enhancement_system.train_from_internet([topic])
            
            print(f"✅ Training completed!")
            print(f"   • Articles collected: {training_result.get('articles_collected', 0)}")
            print(f"   • Sources used: {', '.join(training_result.get('sources_used', []))}")
            print(f"   • Training time: {training_result.get('training_time', 0):.2f} seconds")
            
        except Exception as e:
            logger.error(f"Training failed: {e}")
            print(f"❌ Training failed: {e}")
    
    async def _analyze_image(self, image_path: str):
        """Analyze an image"""
        try:
            if not self.enhancement_system or not self.enhancement_system.visual_engine:
                print("❌ Visual processing not available")
                return
            
            print(f"👁️ Analyzing image: {image_path}")
            
            analysis = await self.enhancement_system.process_image(image_path)
            
            print("✅ Image analysis completed!")
            if "description" in analysis:
                print(f"   • Description: {analysis['description']}")
            if "objects" in analysis:
                objects = [obj['label'] for obj in analysis['objects']]
                print(f"   • Objects detected: {', '.join(objects)}")
            
        except Exception as e:
            logger.error(f"Image analysis failed: {e}")
            print(f"❌ Image analysis failed: {e}")
    
    async def _toggle_voice_mode(self):
        """Toggle voice interaction mode"""
        if self.enhancement_system and self.enhancement_system.voice_manager:
            self.is_voice_mode = not self.is_voice_mode
            status = "enabled" if self.is_voice_mode else "disabled"
            print(f"🎤 Voice mode {status}")
        else:
            print("❌ Voice interface not available")
    
    async def _toggle_visual_mode(self):
        """Toggle visual processing mode"""
        if self.enhancement_system and self.enhancement_system.visual_engine:
            self.is_visual_mode = not self.is_visual_mode
            status = "enabled" if self.is_visual_mode else "disabled"
            print(f"👁️ Visual mode {status}")
        else:
            print("❌ Visual processing not available")
    
    async def _show_help(self):
        """Show help information"""
        print("\n🔍 **ULTRA Enhanced Chat - Help**")
        print("=" * 50)
        print("**Basic Commands:**")
        print("  • help - Show this help")
        print("  • status - Show system status")
        print("  • quit - Exit chat")
        print()
        print("**Enhanced Features:**")
        print("  • voice mode - Toggle voice interaction")
        print("  • visual mode - Toggle image processing")
        print("  • train on [topic] - Learn from internet about topic")
        print("  • analyze image [path] - Analyze an image file")
        print()
        print("**Example Questions:**")
        print("  • What is quantum computing?")
        print("  • Explain machine learning")
        print("  • Tell me about consciousness")
        print("  • What are the latest developments in AI?")
    
    async def _show_status(self):
        """Show system status"""
        print("\n📊 **ULTRA Enhanced System Status**")
        print("=" * 50)
        
        if self.enhancement_system:
            status = self.enhancement_system.get_enhancement_status()
            
            print("**Enhancement Components:**")
            for component, enabled in status.items():
                icon = "✅" if enabled else "❌"
                print(f"  {icon} {component.replace('_', ' ').title()}")
            
            print(f"\n**Conversation Count:** {self.conversation_count}")
            print(f"**Voice Mode:** {'Enabled' if self.is_voice_mode else 'Disabled'}")
            print(f"**Visual Mode:** {'Enabled' if self.is_visual_mode else 'Disabled'}")
        else:
            print("❌ Enhancement system not available")
    
    async def _display_system_status(self):
        """Display initialization status"""
        if self.enhancement_system:
            status = self.enhancement_system.get_enhancement_status()
            
            print("\n📊 **Enhancement System Status:**")
            for component, enabled in status.items():
                icon = "✅" if enabled else "❌"
                print(f"   {icon} {component.replace('_', ' ').title()}")
    
    def _generate_fallback_response(self, user_input: str) -> str:
        """Generate fallback response when base ULTRA not available"""
        return f"""🤖 **ULTRA Enhanced Response:**

I understand you're asking about: "{user_input}"

While my base reasoning systems are initializing, I can still help you with:
• External knowledge lookup from Wikipedia and scientific databases
• Enhanced language generation using pre-trained models
• Voice interaction capabilities
• Image analysis and visual processing
• Internet-based learning on new topics

What would you like to explore?"""
    
    def signal_handler(self, signum, frame):
        """Handle Ctrl+C gracefully"""
        print("\n\n🤖 ULTRA Enhanced: Thank you for exploring the future of AGI!")
        print("✨ Keep pushing the boundaries of artificial intelligence!")
        sys.exit(0)

async def main():
    """Main function"""
    chat = ULTRAEnhancedChat()
    
    # Initialize enhanced systems
    if await chat.initialize():
        # Start enhanced chat
        await chat.start_chat()
    else:
        print("❌ Failed to initialize ULTRA Enhanced Chat")

if __name__ == "__main__":
    asyncio.run(main())
