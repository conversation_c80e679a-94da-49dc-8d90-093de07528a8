#!/usr/bin/env python3
"""
Base Chat Interface for ULTRA AGI System
========================================

Abstract base class that defines the common interface for all ULTRA chat implementations.
This provides a consistent foundation for different chat modes, protocols, and adapters.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Union, AsyncGenerator
from enum import Enum
import asyncio
import logging
from datetime import datetime

class ChatMode(Enum):
    """Available chat modes for ULTRA system."""
    STANDARD = "standard"
    REASONING = "reasoning"
    RESEARCH = "research"
    DIAGNOSTIC = "diagnostic"
    TRAINING = "training"
    
class MessageType(Enum):
    """Types of messages in the chat system."""
    USER = "user"
    ASSISTANT = "assistant"
    SYSTEM = "system"
    ERROR = "error"
    
class ChatMessage:
    """Represents a single message in the chat conversation."""
    
    def __init__(self, content: str, message_type: MessageType, 
                 metadata: Optional[Dict[str, Any]] = None,
                 timestamp: Optional[datetime] = None):
        self.content = content
        self.message_type = message_type
        self.metadata = metadata or {}
        self.timestamp = timestamp or datetime.now()
        
    def to_dict(self) -> Dict[str, Any]:
        """Convert message to dictionary format."""
        return {
            "content": self.content,
            "type": self.message_type.value,
            "metadata": self.metadata,
            "timestamp": self.timestamp.isoformat()
        }

class BaseChatInterface(ABC):
    """
    Abstract base class for all ULTRA chat interfaces.
    
    This defines the common contract that all chat implementations must follow,
    ensuring consistency across different modes, protocols, and adapters.
    """
    
    def __init__(self, mode: ChatMode = ChatMode.STANDARD):
        self.mode = mode
        self.conversation_history: List[ChatMessage] = []
        self.is_active = False
        self.logger = logging.getLogger(f"ultra.chat.{self.__class__.__name__}")
        
    @abstractmethod
    async def initialize(self) -> bool:
        """
        Initialize the chat interface and underlying ULTRA systems.
        
        Returns:
            bool: True if initialization successful, False otherwise
        """
        pass
        
    @abstractmethod
    async def process_message(self, message: str, metadata: Optional[Dict[str, Any]] = None) -> ChatMessage:
        """
        Process a user message and generate ULTRA's response.
        
        Args:
            message: The user's input message
            metadata: Optional metadata about the message
            
        Returns:
            ChatMessage: ULTRA's response message
        """
        pass
        
    @abstractmethod
    async def shutdown(self):
        """Clean shutdown of the chat interface and resources."""
        pass
        
    def add_message(self, message: ChatMessage):
        """Add a message to the conversation history."""
        self.conversation_history.append(message)
        
    def get_conversation_history(self) -> List[ChatMessage]:
        """Get the complete conversation history."""
        return self.conversation_history.copy()
        
    def clear_history(self):
        """Clear the conversation history."""
        self.conversation_history.clear()
        
    def get_last_messages(self, count: int = 10) -> List[ChatMessage]:
        """Get the last N messages from conversation history."""
        return self.conversation_history[-count:] if count > 0 else []
        
    async def stream_response(self, message: str, metadata: Optional[Dict[str, Any]] = None) -> AsyncGenerator[str, None]:
        """
        Stream response tokens as they are generated.
        
        Default implementation falls back to regular processing.
        Subclasses can override for true streaming.
        """
        response = await self.process_message(message, metadata)
        yield response.content
        
    def set_mode(self, mode: ChatMode):
        """Change the chat mode."""
        self.mode = mode
        self.logger.info(f"Chat mode changed to: {mode.value}")
        
    def get_status(self) -> Dict[str, Any]:
        """Get current status of the chat interface."""
        return {
            "mode": self.mode.value,
            "is_active": self.is_active,
            "message_count": len(self.conversation_history),
            "last_activity": self.conversation_history[-1].timestamp.isoformat() if self.conversation_history else None
        }

class ChatError(Exception):
    """Base exception for chat-related errors."""
    pass

class ChatInitializationError(ChatError):
    """Raised when chat interface fails to initialize."""
    pass

class ChatProcessingError(ChatError):
    """Raised when message processing fails."""
    pass

class ChatConnectionError(ChatError):
    """Raised when connection to ULTRA system fails."""
    pass