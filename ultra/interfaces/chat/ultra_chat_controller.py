#!/usr/bin/env python3
"""
ULTRA Chat Controller
====================

Central controller that manages different chat modes, protocols, and adapters.
Provides unified access to all ULTRA chat capabilities.
"""

import sys
import os
import asyncio
import logging
from typing import Dict, Any, List, Optional, Type
from pathlib import Path

# Add ULTRA system to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from .base_chat import BaseChatInterface, ChatMode, ChatMessage, MessageType
from .base_chat import ChatInitializationError, ChatProcessingError, ChatConnectionError

try:
    from ultra.ultra.system_init import ULTRASystem
    from ultra.ultra.config import ULTRAConfigManager
    from ultra.ultra.utils import get_logger
except ImportError:
    # Fallback imports
    ULTRASystem = None
    ULTRAConfigManager = None
    def get_logger(name):
        return logging.getLogger(name)

class ULTRAChatController:
    """
    Main controller for ULTRA chat system.
    
    Manages different chat interfaces, modes, and provides unified access
    to all ULTRA AGI capabilities through a consistent chat interface.
    """
    
    def __init__(self):
        self.logger = get_logger("ultra.chat.controller")
        self.ultra_system = None
        self.config = None
        self.active_interface = None
        self.available_interfaces = {}
        self.is_initialized = False
        
    async def initialize(self) -> bool:
        """Initialize the chat controller and ULTRA system."""
        try:
            self.logger.info("Initializing ULTRA Chat Controller...")
            
            # Initialize configuration
            if ULTRAConfigManager:
                self.config = ULTRAConfigManager()
            
            # Initialize ULTRA system
            if ULTRASystem:
                self.ultra_system = ULTRASystem()
                await self.ultra_system.initialize()
            
            # Register available interfaces
            await self._register_interfaces()
            
            # Set default interface
            await self.set_interface("standard")
            
            self.is_initialized = True
            self.logger.info("ULTRA Chat Controller initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize chat controller: {e}")
            return False
    
    async def _register_interfaces(self):
        """Register all available chat interfaces."""
        # Import and register different interface types
        try:
            from .modes.reasoning_chat import ReasoningChat
            self.available_interfaces["reasoning"] = ReasoningChat
        except ImportError:
            self.logger.warning("Reasoning chat interface not available")
            
        try:
            from .modes.research_chat import ResearchChat
            self.available_interfaces["research"] = ResearchChat
        except ImportError:
            self.logger.warning("Research chat interface not available")
            
        try:
            from .modes.diagnostic_chat import DiagnosticChat
            self.available_interfaces["diagnostic"] = DiagnosticChat
        except ImportError:
            self.logger.warning("Diagnostic chat interface not available")
            
        try:
            from .modes.training_chat import TrainingChat
            self.available_interfaces["training"] = TrainingChat
        except ImportError:
            self.logger.warning("Training chat interface not available")
        
        # Standard interface (fallback)
        self.available_interfaces["standard"] = StandardChatInterface
    
    async def set_interface(self, interface_name: str) -> bool:
        """Set the active chat interface."""
        if interface_name not in self.available_interfaces:
            self.logger.error(f"Interface '{interface_name}' not available")
            return False
            
        try:
            # Shutdown current interface if exists
            if self.active_interface:
                await self.active_interface.shutdown()
            
            # Create and initialize new interface
            interface_class = self.available_interfaces[interface_name]
            self.active_interface = interface_class(self.ultra_system, self.config)
            
            success = await self.active_interface.initialize()
            if success:
                self.logger.info(f"Switched to {interface_name} interface")
                return True
            else:
                self.logger.error(f"Failed to initialize {interface_name} interface")
                return False
                
        except Exception as e:
            self.logger.error(f"Error switching to {interface_name} interface: {e}")
            return False
    
    async def chat(self, message: str, metadata: Optional[Dict[str, Any]] = None) -> str:
        """Send a message to ULTRA and get response."""
        if not self.is_initialized or not self.active_interface:
            raise ChatConnectionError("Chat controller not properly initialized")
        
        try:
            response = await self.active_interface.process_message(message, metadata)
            return response.content
        except Exception as e:
            self.logger.error(f"Error processing message: {e}")
            raise ChatProcessingError(f"Failed to process message: {e}")
    
    async def stream_chat(self, message: str, metadata: Optional[Dict[str, Any]] = None):
        """Stream response from ULTRA as it's generated."""
        if not self.is_initialized or not self.active_interface:
            raise ChatConnectionError("Chat controller not properly initialized")
        
        try:
            async for token in self.active_interface.stream_response(message, metadata):
                yield token
        except Exception as e:
            self.logger.error(f"Error streaming response: {e}")
            raise ChatProcessingError(f"Failed to stream response: {e}")
    
    def get_conversation_history(self) -> List[Dict[str, Any]]:
        """Get conversation history from active interface."""
        if self.active_interface:
            return [msg.to_dict() for msg in self.active_interface.get_conversation_history()]
        return []
    
    def clear_history(self):
        """Clear conversation history."""
        if self.active_interface:
            self.active_interface.clear_history()
    
    def get_available_interfaces(self) -> List[str]:
        """Get list of available chat interfaces."""
        return list(self.available_interfaces.keys())
    
    def get_status(self) -> Dict[str, Any]:
        """Get current status of the chat controller."""
        status = {
            "initialized": self.is_initialized,
            "active_interface": self.active_interface.__class__.__name__ if self.active_interface else None,
            "available_interfaces": self.get_available_interfaces(),
            "ultra_system_status": "connected" if self.ultra_system else "disconnected"
        }
        
        if self.active_interface:
            status["interface_status"] = self.active_interface.get_status()
        
        return status
    
    async def shutdown(self):
        """Shutdown the chat controller and all resources."""
        self.logger.info("Shutting down ULTRA Chat Controller...")
        
        if self.active_interface:
            await self.active_interface.shutdown()
        
        if self.ultra_system:
            await self.ultra_system.shutdown()
        
        self.is_initialized = False
        self.logger.info("Chat controller shutdown complete")

class StandardChatInterface(BaseChatInterface):
    """Standard chat interface implementation."""
    
    def __init__(self, ultra_system=None, config=None):
        super().__init__(ChatMode.STANDARD)
        self.ultra_system = ultra_system
        self.config = config
    
    async def initialize(self) -> bool:
        """Initialize the standard interface."""
        self.is_active = True
        return True
    
    async def process_message(self, message: str, metadata: Optional[Dict[str, Any]] = None) -> ChatMessage:
        """Process message with standard ULTRA capabilities."""
        try:
            # Add user message to history
            user_msg = ChatMessage(message, MessageType.USER, metadata)
            self.add_message(user_msg)
            
            # Generate response (fallback implementation)
            response_text = await self._generate_response(message, metadata)
            
            # Create and add response message
            response_msg = ChatMessage(response_text, MessageType.ASSISTANT)
            self.add_message(response_msg)
            
            return response_msg
            
        except Exception as e:
            error_msg = ChatMessage(f"Error processing message: {e}", MessageType.ERROR)
            self.add_message(error_msg)
            raise ChatProcessingError(f"Failed to process message: {e}")
    
    async def _generate_response(self, message: str, metadata: Optional[Dict[str, Any]] = None) -> str:
        """Generate response using available ULTRA capabilities."""
        # If ULTRA system is available, use it
        if self.ultra_system:
            try:
                # Use ULTRA's processing capabilities
                response = await self.ultra_system.process_input(message)
                return response
            except Exception as e:
                self.logger.warning(f"ULTRA system error, using fallback: {e}")
        
        # Fallback response
        return f"ULTRA AGI Response: I received your message '{message}'. The full ULTRA system is currently in development mode. I'm processing your input using available cognitive architectures."
    
    async def shutdown(self):
        """Shutdown the interface."""
        self.is_active = False

# Global controller instance
_controller = None

async def get_controller() -> ULTRAChatController:
    """Get or create the global chat controller."""
    global _controller
    if _controller is None:
        _controller = ULTRAChatController()
        await _controller.initialize()
    return _controller

async def chat_with_ultra(message: str, interface: str = "standard", metadata: Optional[Dict[str, Any]] = None) -> str:
    """Quick function to chat with ULTRA."""
    controller = await get_controller()
    await controller.set_interface(interface)
    return await controller.chat(message, metadata)