#!/usr/bin/env python3
"""
Text Protocol for ULTRA Chat
============================

Handles text-based communication protocol for ULTRA AGI system.
Provides text encoding, decoding, and formatting for chat interfaces.
"""

import re
import json
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

class TextFormatting(Enum):
    """Text formatting options."""
    PLAIN = "plain"
    MARKDOWN = "markdown"
    HTML = "html"
    RICH = "rich"

@dataclass
class TextMessage:
    """Structured text message."""
    content: str
    formatting: TextFormatting = TextFormatting.PLAIN
    metadata: Optional[Dict[str, Any]] = None
    
class TextProtocol:
    """
    Text protocol handler for ULTRA chat system.
    
    Manages text encoding, decoding, formatting, and validation
    for text-based communication with ULTRA AGI.
    """
    
    def __init__(self, default_formatting: TextFormatting = TextFormatting.MARKDOWN):
        self.default_formatting = default_formatting
        self.max_message_length = 32000  # Max characters per message
        self.encoding = 'utf-8'
        
    def encode_message(self, content: str, metadata: Optional[Dict[str, Any]] = None) -> TextMessage:
        """
        Encode a message for transmission to ULTRA.
        
        Args:
            content: The message content
            metadata: Optional metadata
            
        Returns:
            TextMessage: Encoded message
        """
        # Validate content
        if not content or not content.strip():
            raise ValueError("Message content cannot be empty")
        
        if len(content) > self.max_message_length:
            raise ValueError(f"Message too long: {len(content)} > {self.max_message_length}")
        
        # Clean and normalize content
        content = self._normalize_text(content)
        
        # Detect formatting
        formatting = self._detect_formatting(content)
        
        return TextMessage(
            content=content,
            formatting=formatting,
            metadata=metadata or {}
        )
    
    def decode_response(self, message: TextMessage) -> str:
        """
        Decode ULTRA's response for display.
        
        Args:
            message: The response message
            
        Returns:
            str: Formatted response text
        """
        content = message.content
        
        # Apply formatting based on type
        if message.formatting == TextFormatting.MARKDOWN:
            content = self._format_markdown(content)
        elif message.formatting == TextFormatting.HTML:
            content = self._format_html(content)
        elif message.formatting == TextFormatting.RICH:
            content = self._format_rich(content)
        
        return content
    
    def _normalize_text(self, text: str) -> str:
        """Normalize text input."""
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text.strip())
        
        # Handle common encoding issues
        text = text.encode(self.encoding, errors='replace').decode(self.encoding)
        
        return text
    
    def _detect_formatting(self, content: str) -> TextFormatting:
        """Detect text formatting type."""
        # Check for markdown indicators
        markdown_patterns = [
            r'\*\*.*?\*\*',  # Bold
            r'\*.*?\*',      # Italic
            r'`.*?`',        # Code
            r'^#{1,6}\s',    # Headers
            r'^\*\s',        # Lists
            r'^\d+\.\s'      # Numbered lists
        ]
        
        for pattern in markdown_patterns:
            if re.search(pattern, content, re.MULTILINE):
                return TextFormatting.MARKDOWN
        
        # Check for HTML
        if re.search(r'<[^>]+>', content):
            return TextFormatting.HTML
        
        return TextFormatting.PLAIN
    
    def _format_markdown(self, content: str) -> str:
        """Apply markdown formatting."""
        # Basic markdown to text conversion for display
        # Bold
        content = re.sub(r'\*\*(.*?)\*\*', r'\1', content)
        content = re.sub(r'__(.*?)__', r'\1', content)
        
        # Italic
        content = re.sub(r'\*(.*?)\*', r'\1', content)
        content = re.sub(r'_(.*?)_', r'\1', content)
        
        # Code blocks
        content = re.sub(r'```(.*?)```', r'\1', content, flags=re.DOTALL)
        content = re.sub(r'`(.*?)`', r'\1', content)
        
        # Headers
        content = re.sub(r'^#{1,6}\s*(.*?)$', r'\1', content, flags=re.MULTILINE)
        
        return content
    
    def _format_html(self, content: str) -> str:
        """Basic HTML tag removal."""
        content = re.sub(r'<[^>]+>', '', content)
        return content
    
    def _format_rich(self, content: str) -> str:
        """Format rich text content."""
        # For now, treat as plain text
        return content
    
    def split_long_message(self, content: str, max_length: Optional[int] = None) -> List[str]:
        """
        Split long messages into chunks.
        
        Args:
            content: The message content
            max_length: Maximum length per chunk
            
        Returns:
            List[str]: List of message chunks
        """
        if max_length is None:
            max_length = self.max_message_length
        
        if len(content) <= max_length:
            return [content]
        
        chunks = []
        current_chunk = ""
        
        # Split by sentences first
        sentences = re.split(r'(?<=[.!?])\s+', content)
        
        for sentence in sentences:
            if len(current_chunk) + len(sentence) + 1 <= max_length:
                current_chunk += " " + sentence if current_chunk else sentence
            else:
                if current_chunk:
                    chunks.append(current_chunk.strip())
                    current_chunk = sentence
                else:
                    # Sentence too long, split by words
                    words = sentence.split()
                    for word in words:
                        if len(current_chunk) + len(word) + 1 <= max_length:
                            current_chunk += " " + word if current_chunk else word
                        else:
                            if current_chunk:
                                chunks.append(current_chunk.strip())
                            current_chunk = word
        
        if current_chunk:
            chunks.append(current_chunk.strip())
        
        return chunks
    
    def extract_code_blocks(self, content: str) -> List[Tuple[str, str]]:
        """
        Extract code blocks from message content.
        
        Returns:
            List[Tuple[str, str]]: List of (language, code) tuples
        """
        code_blocks = []
        
        # Extract fenced code blocks
        pattern = r'```(\w+)?\n(.*?)```'
        matches = re.findall(pattern, content, re.DOTALL)
        
        for language, code in matches:
            code_blocks.append((language or 'text', code.strip()))
        
        # Extract inline code
        inline_pattern = r'`([^`]+)`'
        inline_matches = re.findall(inline_pattern, content)
        
        for code in inline_matches:
            code_blocks.append(('inline', code))
        
        return code_blocks
    
    def format_for_display(self, content: str, terminal_width: int = 80) -> str:
        """
        Format content for terminal display.
        
        Args:
            content: Content to format
            terminal_width: Terminal width for wrapping
            
        Returns:
            str: Formatted content
        """
        lines = []
        
        for line in content.split('\n'):
            if len(line) <= terminal_width:
                lines.append(line)
            else:
                # Wrap long lines
                words = line.split()
                current_line = ""
                
                for word in words:
                    if len(current_line) + len(word) + 1 <= terminal_width:
                        current_line += " " + word if current_line else word
                    else:
                        if current_line:
                            lines.append(current_line)
                        current_line = word
                
                if current_line:
                    lines.append(current_line)
        
        return '\n'.join(lines)
    
    def validate_message(self, message: TextMessage) -> bool:
        """
        Validate a text message.
        
        Args:
            message: Message to validate
            
        Returns:
            bool: True if valid
        """
        if not message.content:
            return False
        
        if len(message.content) > self.max_message_length:
            return False
        
        # Check for valid encoding
        try:
            message.content.encode(self.encoding)
        except UnicodeEncodeError:
            return False
        
        return True