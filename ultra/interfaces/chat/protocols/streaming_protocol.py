#!/usr/bin/env python3
"""
Streaming Protocol for ULTRA Chat
=================================

Handles real-time streaming communication with ULTRA AGI system.
Enables token-by-token response generation and real-time interaction.
"""

import asyncio
import time
from typing import AsyncGenerator, Dict, Any, Optional, Callable, List
from dataclasses import dataclass
from enum import Enum
import logging

class StreamingMode(Enum):
    """Streaming modes."""
    TOKEN = "token"          # Token by token
    SENTENCE = "sentence"    # Sentence by sentence
    PARAGRAPH = "paragraph"  # Paragraph by paragraph
    THOUGHT = "thought"      # Thought process chunks

@dataclass
class StreamChunk:
    """A chunk of streamed content."""
    content: str
    chunk_type: str = "token"
    metadata: Optional[Dict[str, Any]] = None
    timestamp: float = None
    is_final: bool = False
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = time.time()

class StreamingProtocol:
    """
    Streaming protocol for ULTRA chat system.
    
    Manages real-time streaming of responses from ULTRA AGI,
    including token streaming, sentence completion, and thought processes.
    """
    
    def __init__(self, mode: StreamingMode = StreamingMode.TOKEN):
        self.mode = mode
        self.buffer = ""
        self.sentence_buffer = ""
        self.is_streaming = False
        self.stream_callbacks: List[Callable] = []
        self.logger = logging.getLogger("ultra.chat.streaming")
        
        # Streaming configuration
        self.chunk_delay = 0.05  # Delay between chunks (seconds)
        self.sentence_endings = {'.', '!', '?', '\n\n'}
        self.paragraph_markers = {'\n\n', '\n---\n', '\n\n---\n\n'}
        
    async def stream_response(self, response_generator: AsyncGenerator[str, None]) -> AsyncGenerator[StreamChunk, None]:
        """
        Stream response chunks from ULTRA's response generator.
        
        Args:
            response_generator: Async generator producing response tokens
            
        Yields:
            StreamChunk: Chunks of streamed content
        """
        self.is_streaming = True
        self.buffer = ""
        self.sentence_buffer = ""
        
        try:
            async for token in response_generator:
                chunk = await self._process_token(token)
                if chunk:
                    yield chunk
                    await asyncio.sleep(self.chunk_delay)
                    
            # Yield final chunk if buffer has content
            if self.buffer:
                final_chunk = StreamChunk(
                    content=self.buffer,
                    chunk_type="final",
                    is_final=True
                )
                yield final_chunk
                
        except Exception as e:
            self.logger.error(f"Streaming error: {e}")
            error_chunk = StreamChunk(
                content=f"[Streaming Error: {e}]",
                chunk_type="error",
                is_final=True
            )
            yield error_chunk
            
        finally:
            self.is_streaming = False
    
    async def _process_token(self, token: str) -> Optional[StreamChunk]:
        """Process a single token based on streaming mode."""
        self.buffer += token
        self.sentence_buffer += token
        
        if self.mode == StreamingMode.TOKEN:
            return await self._process_token_mode(token)
        elif self.mode == StreamingMode.SENTENCE:
            return await self._process_sentence_mode(token)
        elif self.mode == StreamingMode.PARAGRAPH:
            return await self._process_paragraph_mode(token)
        elif self.mode == StreamingMode.THOUGHT:
            return await self._process_thought_mode(token)
        
        return None
    
    async def _process_token_mode(self, token: str) -> StreamChunk:
        """Process token in TOKEN mode - immediate streaming."""
        return StreamChunk(
            content=token,
            chunk_type="token"
        )
    
    async def _process_sentence_mode(self, token: str) -> Optional[StreamChunk]:
        """Process token in SENTENCE mode - wait for sentence completion."""
        # Check if sentence is complete
        if any(ending in token for ending in self.sentence_endings):
            # Look for complete sentence
            for ending in self.sentence_endings:
                if ending in self.sentence_buffer:
                    # Extract complete sentence
                    parts = self.sentence_buffer.split(ending, 1)
                    if len(parts) > 1:
                        sentence = parts[0] + ending
                        remaining = parts[1]
                        
                        self.sentence_buffer = remaining
                        
                        return StreamChunk(
                            content=sentence,
                            chunk_type="sentence"
                        )
        
        return None
    
    async def _process_paragraph_mode(self, token: str) -> Optional[StreamChunk]:
        """Process token in PARAGRAPH mode - wait for paragraph completion."""
        # Check for paragraph markers
        for marker in self.paragraph_markers:
            if marker in self.buffer:
                parts = self.buffer.split(marker, 1)
                if len(parts) > 1:
                    paragraph = parts[0]
                    self.buffer = parts[1]
                    
                    return StreamChunk(
                        content=paragraph,
                        chunk_type="paragraph"
                    )
        
        return None
    
    async def _process_thought_mode(self, token: str) -> Optional[StreamChunk]:
        """Process token in THOUGHT mode - stream thought processes."""
        # Look for thought delimiters
        thought_markers = ['[THINKING]', '[/THINKING]', '[REASONING]', '[/REASONING]']
        
        for marker in thought_markers:
            if marker in self.buffer:
                parts = self.buffer.split(marker, 1)
                if len(parts) > 1:
                    thought = parts[0]
                    self.buffer = parts[1]
                    
                    chunk_type = "thought" if "THINKING" in marker else "reasoning"
                    
                    return StreamChunk(
                        content=thought,
                        chunk_type=chunk_type,
                        metadata={"marker": marker}
                    )
        
        return None
    
    def add_stream_callback(self, callback: Callable[[StreamChunk], None]):
        """Add a callback to be called for each stream chunk."""
        self.stream_callbacks.append(callback)
    
    def remove_stream_callback(self, callback: Callable[[StreamChunk], None]):
        """Remove a stream callback."""
        if callback in self.stream_callbacks:
            self.stream_callbacks.remove(callback)
    
    async def _notify_callbacks(self, chunk: StreamChunk):
        """Notify all registered callbacks about a new chunk."""
        for callback in self.stream_callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(chunk)
                else:
                    callback(chunk)
            except Exception as e:
                self.logger.error(f"Callback error: {e}")
    
    def set_mode(self, mode: StreamingMode):
        """Change streaming mode."""
        self.mode = mode
        self.logger.info(f"Streaming mode changed to: {mode.value}")
    
    def configure_delays(self, chunk_delay: float = 0.05):
        """Configure streaming delays."""
        self.chunk_delay = chunk_delay
    
    def get_buffer_status(self) -> Dict[str, Any]:
        """Get current buffer status."""
        return {
            "mode": self.mode.value,
            "is_streaming": self.is_streaming,
            "buffer_length": len(self.buffer),
            "sentence_buffer_length": len(self.sentence_buffer),
            "callbacks_count": len(self.stream_callbacks)
        }
    
    def clear_buffers(self):
        """Clear all internal buffers."""
        self.buffer = ""
        self.sentence_buffer = ""

class StreamingFormatter:
    """Formats streaming content for different display types."""
    
    @staticmethod
    def format_for_terminal(chunk: StreamChunk, show_metadata: bool = False) -> str:
        """Format chunk for terminal display."""
        content = chunk.content
        
        if show_metadata and chunk.metadata:
            metadata_str = f" [{chunk.chunk_type}]"
            content += metadata_str
        
        return content
    
    @staticmethod
    def format_for_web(chunk: StreamChunk) -> Dict[str, Any]:
        """Format chunk for web interface."""
        return {
            "content": chunk.content,
            "type": chunk.chunk_type,
            "timestamp": chunk.timestamp,
            "is_final": chunk.is_final,
            "metadata": chunk.metadata or {}
        }
    
    @staticmethod
    def format_for_api(chunk: StreamChunk) -> str:
        """Format chunk for API response."""
        import json
        return json.dumps({
            "chunk": chunk.content,
            "type": chunk.chunk_type,
            "timestamp": chunk.timestamp,
            "final": chunk.is_final
        })

class StreamingBuffer:
    """Buffer for managing streaming content reconstruction."""
    
    def __init__(self):
        self.chunks: List[StreamChunk] = []
        self.complete_content = ""
        
    def add_chunk(self, chunk: StreamChunk):
        """Add a chunk to the buffer."""
        self.chunks.append(chunk)
        self.complete_content += chunk.content
    
    def get_complete_content(self) -> str:
        """Get the complete reconstructed content."""
        return self.complete_content
    
    def get_chunks_by_type(self, chunk_type: str) -> List[StreamChunk]:
        """Get all chunks of a specific type."""
        return [chunk for chunk in self.chunks if chunk.chunk_type == chunk_type]
    
    def clear(self):
        """Clear the buffer."""
        self.chunks.clear()
        self.complete_content = ""