#!/usr/bin/env python3
"""
Multimodal Protocol for ULTRA Chat
==================================

Handles multimodal communication with ULTRA AGI system.
Supports text, images, audio, and other data types.
"""

import base64
import mimetypes
import hashlib
from typing import Dict, Any, List, Optional, Union, BinaryIO
from dataclasses import dataclass
from enum import Enum
from pathlib import Path
import logging

class ModalityType(Enum):
    """Supported modality types."""
    TEXT = "text"
    IMAGE = "image"
    AUDIO = "audio"
    VIDEO = "video"
    DOCUMENT = "document"
    CODE = "code"
    DATA = "data"

@dataclass
class ModalityData:
    """Container for multimodal data."""
    content: Union[str, bytes]
    modality: ModalityType
    mime_type: str
    metadata: Optional[Dict[str, Any]] = None
    encoding: str = "utf-8"
    checksum: Optional[str] = None
    
    def __post_init__(self):
        if self.checksum is None and isinstance(self.content, (str, bytes)):
            content_bytes = self.content.encode(self.encoding) if isinstance(self.content, str) else self.content
            self.checksum = hashlib.md5(content_bytes).hexdigest()

@dataclass
class MultimodalMessage:
    """Multimodal message containing multiple data types."""
    modalities: List[ModalityData]
    primary_modality: ModalityType = ModalityType.TEXT
    metadata: Optional[Dict[str, Any]] = None
    
    def get_text_content(self) -> Optional[str]:
        """Extract text content from message."""
        for modality in self.modalities:
            if modality.modality == ModalityType.TEXT:
                return modality.content if isinstance(modality.content, str) else modality.content.decode(modality.encoding)
        return None
    
    def get_modalities_by_type(self, modality_type: ModalityType) -> List[ModalityData]:
        """Get all modalities of a specific type."""
        return [m for m in self.modalities if m.modality == modality_type]

class MultimodalProtocol:
    """
    Multimodal protocol for ULTRA chat system.
    
    Handles encoding, decoding, and processing of different data modalities
    for comprehensive interaction with ULTRA AGI.
    """
    
    def __init__(self):
        self.logger = logging.getLogger("ultra.chat.multimodal")
        self.max_file_size = 10 * 1024 * 1024  # 10MB
        self.supported_image_types = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'}
        self.supported_audio_types = {'.mp3', '.wav', '.ogg', '.m4a', '.flac'}
        self.supported_video_types = {'.mp4', '.avi', '.mov', '.mkv', '.webm'}
        self.supported_document_types = {'.pdf', '.txt', '.md', '.docx', '.rtf'}
        
    def create_text_modality(self, text: str, metadata: Optional[Dict[str, Any]] = None) -> ModalityData:
        """Create a text modality."""
        return ModalityData(
            content=text,
            modality=ModalityType.TEXT,
            mime_type="text/plain",
            metadata=metadata or {}
        )
    
    def create_image_modality(self, image_path: Union[str, Path], metadata: Optional[Dict[str, Any]] = None) -> ModalityData:
        """Create an image modality from file path."""
        path = Path(image_path)
        
        if not path.exists():
            raise FileNotFoundError(f"Image file not found: {image_path}")
        
        if path.suffix.lower() not in self.supported_image_types:
            raise ValueError(f"Unsupported image type: {path.suffix}")
        
        if path.stat().st_size > self.max_file_size:
            raise ValueError(f"Image file too large: {path.stat().st_size} bytes")
        
        # Read and encode image
        with open(path, 'rb') as f:
            image_data = f.read()
        
        # Detect MIME type
        mime_type, _ = mimetypes.guess_type(str(path))
        if not mime_type:
            mime_type = "application/octet-stream"
        
        return ModalityData(
            content=image_data,
            modality=ModalityType.IMAGE,
            mime_type=mime_type,
            metadata={
                "filename": path.name,
                "size": len(image_data),
                "width": None,  # Would need PIL to get dimensions
                "height": None,
                **(metadata or {})
            }
        )
    
    def create_audio_modality(self, audio_path: Union[str, Path], metadata: Optional[Dict[str, Any]] = None) -> ModalityData:
        """Create an audio modality from file path."""
        path = Path(audio_path)
        
        if not path.exists():
            raise FileNotFoundError(f"Audio file not found: {audio_path}")
        
        if path.suffix.lower() not in self.supported_audio_types:
            raise ValueError(f"Unsupported audio type: {path.suffix}")
        
        if path.stat().st_size > self.max_file_size:
            raise ValueError(f"Audio file too large: {path.stat().st_size} bytes")
        
        # Read audio data
        with open(path, 'rb') as f:
            audio_data = f.read()
        
        # Detect MIME type
        mime_type, _ = mimetypes.guess_type(str(path))
        if not mime_type:
            mime_type = "application/octet-stream"
        
        return ModalityData(
            content=audio_data,
            modality=ModalityType.AUDIO,
            mime_type=mime_type,
            metadata={
                "filename": path.name,
                "size": len(audio_data),
                "duration": None,  # Would need audio library to get duration
                **(metadata or {})
            }
        )
    
    def create_document_modality(self, doc_path: Union[str, Path], metadata: Optional[Dict[str, Any]] = None) -> ModalityData:
        """Create a document modality from file path."""
        path = Path(doc_path)
        
        if not path.exists():
            raise FileNotFoundError(f"Document not found: {doc_path}")
        
        if path.suffix.lower() not in self.supported_document_types:
            raise ValueError(f"Unsupported document type: {path.suffix}")
        
        if path.stat().st_size > self.max_file_size:
            raise ValueError(f"Document too large: {path.stat().st_size} bytes")
        
        # Handle different document types
        if path.suffix.lower() in {'.txt', '.md'}:
            # Text-based documents
            with open(path, 'r', encoding='utf-8') as f:
                content = f.read()
            mime_type = "text/plain"
        else:
            # Binary documents
            with open(path, 'rb') as f:
                content = f.read()
            mime_type, _ = mimetypes.guess_type(str(path))
            if not mime_type:
                mime_type = "application/octet-stream"
        
        return ModalityData(
            content=content,
            modality=ModalityType.DOCUMENT,
            mime_type=mime_type,
            metadata={
                "filename": path.name,
                "size": len(content) if isinstance(content, str) else len(content),
                **(metadata or {})
            }
        )
    
    def create_code_modality(self, code: str, language: str, metadata: Optional[Dict[str, Any]] = None) -> ModalityData:
        """Create a code modality."""
        return ModalityData(
            content=code,
            modality=ModalityType.CODE,
            mime_type=f"text/x-{language}",
            metadata={
                "language": language,
                "lines": len(code.split('\n')),
                **(metadata or {})
            }
        )
    
    def encode_multimodal_message(self, message: MultimodalMessage) -> Dict[str, Any]:
        """
        Encode multimodal message for transmission.
        
        Args:
            message: Multimodal message to encode
            
        Returns:
            Dict: Encoded message data
        """
        encoded_modalities = []
        
        for modality in message.modalities:
            encoded_modality = {
                "type": modality.modality.value,
                "mime_type": modality.mime_type,
                "metadata": modality.metadata,
                "checksum": modality.checksum
            }
            
            # Encode content based on type
            if isinstance(modality.content, str):
                encoded_modality["content"] = modality.content
                encoded_modality["encoding"] = "text"
            else:
                # Binary content - encode as base64
                encoded_modality["content"] = base64.b64encode(modality.content).decode('ascii')
                encoded_modality["encoding"] = "base64"
            
            encoded_modalities.append(encoded_modality)
        
        return {
            "modalities": encoded_modalities,
            "primary_modality": message.primary_modality.value,
            "metadata": message.metadata or {},
            "version": "1.0"
        }
    
    def decode_multimodal_message(self, encoded_data: Dict[str, Any]) -> MultimodalMessage:
        """
        Decode multimodal message from encoded data.
        
        Args:
            encoded_data: Encoded message data
            
        Returns:
            MultimodalMessage: Decoded message
        """
        modalities = []
        
        for encoded_modality in encoded_data["modalities"]:
            # Decode content
            if encoded_modality["encoding"] == "text":
                content = encoded_modality["content"]
            elif encoded_modality["encoding"] == "base64":
                content = base64.b64decode(encoded_modality["content"])
            else:
                raise ValueError(f"Unknown encoding: {encoded_modality['encoding']}")
            
            modality = ModalityData(
                content=content,
                modality=ModalityType(encoded_modality["type"]),
                mime_type=encoded_modality["mime_type"],
                metadata=encoded_modality.get("metadata", {}),
                checksum=encoded_modality.get("checksum")
            )
            
            modalities.append(modality)
        
        return MultimodalMessage(
            modalities=modalities,
            primary_modality=ModalityType(encoded_data["primary_modality"]),
            metadata=encoded_data.get("metadata", {})
        )
    
    def validate_modality(self, modality: ModalityData) -> bool:
        """
        Validate a modality data object.
        
        Args:
            modality: Modality to validate
            
        Returns:
            bool: True if valid
        """
        # Check content exists
        if not modality.content:
            return False
        
        # Check size limits
        content_size = len(modality.content) if isinstance(modality.content, str) else len(modality.content)
        if content_size > self.max_file_size:
            return False
        
        # Validate checksum if provided
        if modality.checksum:
            content_bytes = modality.content.encode(modality.encoding) if isinstance(modality.content, str) else modality.content
            calculated_checksum = hashlib.md5(content_bytes).hexdigest()
            if calculated_checksum != modality.checksum:
                return False
        
        return True
    
    def extract_text_from_modalities(self, modalities: List[ModalityData]) -> str:
        """Extract all text content from modalities."""
        text_parts = []
        
        for modality in modalities:
            if modality.modality == ModalityType.TEXT:
                content = modality.content if isinstance(modality.content, str) else modality.content.decode(modality.encoding)
                text_parts.append(content)
            elif modality.modality == ModalityType.CODE:
                code_content = modality.content if isinstance(modality.content, str) else modality.content.decode(modality.encoding)
                language = modality.metadata.get("language", "unknown")
                text_parts.append(f"[CODE:{language}]\n{code_content}\n[/CODE]")
            elif modality.modality == ModalityType.DOCUMENT:
                if modality.mime_type.startswith("text/"):
                    doc_content = modality.content if isinstance(modality.content, str) else modality.content.decode(modality.encoding)
                    text_parts.append(f"[DOCUMENT:{modality.metadata.get('filename', 'unknown')}]\n{doc_content}\n[/DOCUMENT]")
                else:
                    text_parts.append(f"[BINARY_DOCUMENT:{modality.metadata.get('filename', 'unknown')}]")
            elif modality.modality in {ModalityType.IMAGE, ModalityType.AUDIO, ModalityType.VIDEO}:
                text_parts.append(f"[{modality.modality.value.upper()}:{modality.metadata.get('filename', 'attachment')}]")
        
        return "\n\n".join(text_parts)
    
    def get_supported_types(self) -> Dict[str, List[str]]:
        """Get supported file types by category."""
        return {
            "image": list(self.supported_image_types),
            "audio": list(self.supported_audio_types),
            "video": list(self.supported_video_types),
            "document": list(self.supported_document_types)
        }