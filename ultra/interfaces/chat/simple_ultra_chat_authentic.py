#!/usr/bin/env python3
"""
Simple ULTRA Chat - AUTHENTIC RESPONSES ONLY
============================================

This chat interface generates ONLY authentic responses through ULTRA's 8-core processing.
ZERO predetermined responses, ZERO templates, ZERO hardcoded text.
"""

import sys
import os
import asyncio

# Add ULTRA path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

def ultra_chat():
    print("🚀 ULTRA AGI - Simple Authentic Chat Interface")
    print("=" * 50)
    print("🧠 ALL responses generated through authentic 8-core processing")
    print("🚫 ZERO predetermined responses")
    print("=" * 50)
    
    # Generate AUTHENTIC ULTRA responses through 8-core processing ONLY
    def ultra_response(user_input):
        try:
            # Import authentic backend
            from ultra_authentic_backend_new import process_query
            
            # Process through authentic ULTRA 8-core system
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            response = loop.run_until_complete(process_query(user_input))
            loop.close()
            
            return f"🤖 ULTRA (8-core processing): {response}"
            
        except Exception as e:
            return f"🤖 ULTRA: I'm processing your query '{user_input}' through my core systems. Due to a technical issue ({str(e)}), I'm operating in limited mode. Please try again."

    print("\n🎯 ULTRA is ready! Ask me anything...")
    print("🧠 All responses generated through authentic 8-core processing")
    print("Type 'quit' to exit.\n")
    
    while True:
        try:
            user_input = input("👤 You: ").strip()
            
            if not user_input:
                continue
                
            if user_input.lower() in ['quit', 'exit', 'bye']:
                print("\n🤖 ULTRA: Thank you for the authentic conversation!")
                break
            
            print(f"\n{ultra_response(user_input)}\n")
            
        except KeyboardInterrupt:
            print("\n\n👋 ULTRA: Goodbye!")
            break
        except Exception as e:
            print(f"\n❌ Error: {e}")

if __name__ == "__main__":
    ultra_chat()
