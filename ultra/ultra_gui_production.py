#!/usr/bin/env python3
"""
ULTRA AGI - Production GUI
==========================

Clean, professional interface for ULTRA AGI.
Real intelligence, real answers.
"""

import sys
import os
import asyncio
import threading
from datetime import datetime

# Add paths
sys.path.insert(0, os.path.abspath('.'))
sys.path.insert(0, os.path.abspath('ultra'))

try:
    from flask import Flask, request, jsonify
    from flask_socketio import SocketIO, emit
    HAS_FLASK = True
except ImportError:
    print("Error: Flask required. Install with: pip install flask flask-socketio")
    sys.exit(1)

class ULTRAProductionGUI:
    """Production ULTRA AGI Interface"""
    
    def __init__(self):
        self.app = Flask(__name__)
        self.app.config['SECRET_KEY'] = 'ultra_production'
        self.socketio = SocketIO(self.app, cors_allowed_origins="*")
        
        # ULTRA AGI core
        self.ultra_agi = None
        self.conversation_history = []
        
        self.setup_routes()
        self.setup_socket_handlers()
        
        # Initialize ULTRA AGI
        threading.Thread(target=self.initialize_ultra_agi, daemon=True).start()
    
    def setup_routes(self):
        """Setup routes"""
        @self.app.route('/')
        def index():
            return self.render_interface()
    
    def setup_socket_handlers(self):
        """Setup socket handlers"""
        @self.socketio.on('send_message')
        def handle_message(data):
            message = data.get('message', '').strip()
            if message:
                threading.Thread(
                    target=self.process_message,
                    args=(message,),
                    daemon=True
                ).start()
    
    def initialize_ultra_agi(self):
        """Initialize ULTRA AGI core"""
        try:
            print("Initializing ULTRA AGI core...")
            
            from ultra_agi_core import get_ultra_agi
            
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            self.ultra_agi = loop.run_until_complete(get_ultra_agi())
            
            print("ULTRA AGI core initialized successfully")
            
            # Notify frontend
            self.socketio.emit('system_ready', {'status': 'ready'})
            
        except Exception as e:
            print(f"ULTRA AGI initialization failed: {e}")
            self.socketio.emit('system_error', {'error': str(e)})
    
    def process_message(self, message):
        """Process message through ULTRA AGI"""
        try:
            # Add user message
            self.conversation_history.append({
                'role': 'user',
                'content': message,
                'timestamp': datetime.now().isoformat()
            })
            
            # Emit user message
            self.socketio.emit('message', {
                'role': 'user',
                'content': message
            })
            
            # Generate ULTRA AGI response
            if self.ultra_agi:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                response = loop.run_until_complete(
                    self.ultra_agi.process_query(message)
                )
            else:
                response = "ULTRA AGI is still initializing..."
            
            # Add response to history
            self.conversation_history.append({
                'role': 'assistant',
                'content': response,
                'timestamp': datetime.now().isoformat()
            })
            
            # Emit response
            self.socketio.emit('message', {
                'role': 'assistant',
                'content': response
            })
            
        except Exception as e:
            error_msg = f"Error: {str(e)}"
            self.socketio.emit('message', {
                'role': 'assistant',
                'content': error_msg
            })
    
    def render_interface(self):
        """Render clean interface"""
        return '''<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ULTRA AGI</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #ffffff;
            color: #2d3748;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .header {
            padding: 20px 24px;
            border-bottom: 1px solid #e2e8f0;
            background: #ffffff;
        }
        
        .title {
            font-size: 20px;
            font-weight: 600;
            color: #1a202c;
        }
        
        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            max-width: 800px;
            margin: 0 auto;
            width: 100%;
        }
        
        .messages {
            flex: 1;
            overflow-y: auto;
            padding: 32px 24px;
            display: flex;
            flex-direction: column;
            gap: 24px;
        }
        
        .message {
            max-width: 100%;
            line-height: 1.6;
            font-size: 16px;
        }
        
        .message.user {
            align-self: flex-end;
            background: #f7fafc;
            padding: 16px 20px;
            border-radius: 20px;
            border-bottom-right-radius: 6px;
            max-width: 85%;
        }
        
        .message.assistant {
            align-self: flex-start;
            padding: 16px 0;
            max-width: 100%;
            white-space: pre-wrap;
        }
        
        .input-area {
            padding: 24px;
            border-top: 1px solid #e2e8f0;
            background: #ffffff;
        }
        
        .input-container {
            display: flex;
            gap: 12px;
            align-items: flex-end;
            max-width: 800px;
            margin: 0 auto;
        }
        
        .input-field {
            flex: 1;
            min-height: 48px;
            max-height: 120px;
            padding: 14px 18px;
            border: 1px solid #e2e8f0;
            border-radius: 24px;
            font-size: 16px;
            font-family: inherit;
            resize: none;
            outline: none;
            background: #ffffff;
        }
        
        .input-field:focus {
            border-color: #4299e1;
            box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
        }
        
        .send-button {
            width: 48px;
            height: 48px;
            border: none;
            border-radius: 50%;
            background: #4299e1;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            transition: background-color 0.2s;
        }
        
        .send-button:hover:not(:disabled) {
            background: #3182ce;
        }
        
        .send-button:disabled {
            background: #cbd5e0;
            cursor: not-allowed;
        }
        
        .status {
            text-align: center;
            padding: 16px;
            font-size: 14px;
            color: #718096;
            font-style: italic;
        }
        
        @media (max-width: 768px) {
            .messages {
                padding: 20px 16px;
            }
            
            .input-area {
                padding: 16px;
            }
            
            .header {
                padding: 16px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="title">ULTRA AGI</div>
    </div>
    
    <div class="chat-container">
        <div class="messages" id="messages">
            <div class="status" id="status">Initializing ULTRA AGI...</div>
        </div>
        
        <div class="input-area">
            <div class="input-container">
                <textarea 
                    class="input-field" 
                    id="messageInput" 
                    placeholder="Ask ULTRA anything..." 
                    rows="1"
                    disabled
                ></textarea>
                <button class="send-button" id="sendButton" disabled>→</button>
            </div>
        </div>
    </div>
    
    <script>
        const socket = io();
        const messages = document.getElementById('messages');
        const messageInput = document.getElementById('messageInput');
        const sendButton = document.getElementById('sendButton');
        const status = document.getElementById('status');
        
        // Auto-resize textarea
        messageInput.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = Math.min(this.scrollHeight, 120) + 'px';
        });
        
        // Send message
        function sendMessage() {
            const message = messageInput.value.trim();
            if (message && !sendButton.disabled) {
                socket.emit('send_message', { message: message });
                messageInput.value = '';
                messageInput.style.height = 'auto';
                sendButton.disabled = true;
            }
        }
        
        // Event listeners
        sendButton.addEventListener('click', sendMessage);
        
        messageInput.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });
        
        // Socket events
        socket.on('system_ready', function() {
            if (status.parentNode) {
                status.remove();
            }
            messageInput.disabled = false;
            sendButton.disabled = false;
            messageInput.focus();
        });
        
        socket.on('message', function(data) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${data.role}`;
            messageDiv.textContent = data.content;
            
            // Remove status if it exists
            if (status.parentNode) {
                status.remove();
            }
            
            messages.appendChild(messageDiv);
            messages.scrollTop = messages.scrollHeight;
            
            if (data.role === 'assistant') {
                sendButton.disabled = false;
                messageInput.focus();
            }
        });
        
        socket.on('system_error', function(data) {
            status.textContent = `Error: ${data.error}`;
        });
    </script>
</body>
</html>'''
    
    def run(self, host='localhost', port=8080):
        """Run the production GUI"""
        print(f"Starting ULTRA AGI Production GUI on http://{host}:{port}")
        self.socketio.run(self.app, host=host, port=port, debug=False)

if __name__ == "__main__":
    gui = ULTRAProductionGUI()
    gui.run()
