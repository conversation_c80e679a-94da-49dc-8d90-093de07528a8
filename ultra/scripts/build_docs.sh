#!/usr/bin/env bash
#
# ULTRA Documentation Builder
# 
# This script generates comprehensive documentation for the ULTRA framework:
# - API documentation from source code
# - Mathematical formulation documentation from TeX files
# - Architecture diagrams from Mermaid sources
# - Markdown compilation and cross-referencing
# - PDF generation for publishable documentation
#
# Usage: ./build_docs.sh [OPTIONS]
#   Options:
#     -o, --output DIR      Output directory (default: docs/build)
#     -f, --format FORMAT   Output format: html, pdf, both (default: html)
#     -v, --version VER     Documentation version (default: from version.py)
#     -c, --clean           Clean build directory before generation
#     -d, --dev             Include development/internal documentation
#     -h, --help            Display this help message

set -e

# Default values
OUTPUT_DIR="docs/build"
FORMAT="html"
VERSION=""
CLEAN=0
DEV_DOCS=0
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" &> /dev/null && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/../.." &> /dev/null && pwd)"
SOURCE_DIR="${PROJECT_ROOT}/ultra"
DOCS_DIR="${PROJECT_ROOT}/docs"
CONFIG_DIR="${PROJECT_ROOT}/config"
TEMP_DIR="/tmp/ultra_docs_build"
LOG_FILE="${TEMP_DIR}/build_docs.log"

# Parse command line arguments
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -o|--output)
                OUTPUT_DIR="$2"
                shift 2
                ;;
            -f|--format)
                FORMAT="$2"
                shift 2
                ;;
            -v|--version)
                VERSION="$2"
                shift 2
                ;;
            -c|--clean)
                CLEAN=1
                shift
                ;;
            -d|--dev)
                DEV_DOCS=1
                shift
                ;;
            -h|--help)
                echo "Usage: $0 [OPTIONS]"
                echo "  Options:"
                echo "    -o, --output DIR      Output directory (default: docs/build)"
                echo "    -f, --format FORMAT   Output format: html, pdf, both (default: html)"
                echo "    -v, --version VER     Documentation version (default: from version.py)"
                echo "    -c, --clean           Clean build directory before generation"
                echo "    -d, --dev             Include development/internal documentation"
                echo "    -h, --help            Display this help message"
                exit 0
                ;;
            *)
                echo "Unknown option: $1"
                exit 1
                ;;
        esac
    done
}

# Setup function
setup() {
    echo "Setting up documentation build environment..."
    
    # Create temp directory
    mkdir -p "${TEMP_DIR}"
    
    # Create output directory
    mkdir -p "${OUTPUT_DIR}"
    
    # Clean output directory if requested
    if [[ $CLEAN -eq 1 ]]; then
        echo "Cleaning output directory: ${OUTPUT_DIR}"
        rm -rf "${OUTPUT_DIR:?}"/*
    fi
    
    # Extract version if not provided
    if [[ -z "$VERSION" ]]; then
        if [[ -f "${SOURCE_DIR}/version.py" ]]; then
            VERSION=$(grep -oP '(?<=__version__ = ")[^"]+' "${SOURCE_DIR}/version.py")
            echo "Using version from version.py: ${VERSION}"
        else
            VERSION="development"
            echo "No version specified or found, using: ${VERSION}"
        fi
    fi
    
    # Check for required tools
    check_requirements
}

# Check if all required tools are installed
check_requirements() {
    local missing_tools=()
    
    # Required tools
    tools=("python3" "pip" "sphinx-build" "pandoc" "doxygen" "npm" 
          "latex" "makeinfo" "graphviz" "inkscape")
    
    for tool in "${tools[@]}"; do
        if ! command -v "$tool" &> /dev/null; then
            case "$tool" in
                "sphinx-build")
                    if ! python3 -c "import sphinx" &> /dev/null; then
                        missing_tools+=("$tool")
                    fi
                    ;;
                "latex")
                    if ! command -v "pdflatex" &> /dev/null; then
                        missing_tools+=("$tool")
                    fi
                    ;;
                *)
                    missing_tools+=("$tool")
                    ;;
            esac
        fi
    done
    
    if [[ ${#missing_tools[@]} -gt 0 ]]; then
        echo "ERROR: The following required tools are missing:"
        for tool in "${missing_tools[@]}"; do
            echo "  - $tool"
        done
        echo ""
        echo "Please install the missing tools and try again."
        echo "You can install most Python tools with: pip install sphinx sphinx-rtd-theme sphinx-math-dollar sphinx-autoapi nbsphinx"
        echo "For system packages, use your package manager (apt, yum, brew, etc.)"
        exit 1
    fi
    
    # Install required Python packages
    echo "Installing required Python packages..."
    pip install -q sphinx sphinx-rtd-theme sphinx-math-dollar sphinx-autoapi nbsphinx myst-parser sphinx-autodoc-typehints sphinx-copybutton sphinx-gallery sphinx-togglebutton breathe sphinxcontrib-bibtex
    
    # Install mermaid-cli for diagram rendering
    if ! command -v "mmdc" &> /dev/null; then
        echo "Installing mermaid-cli for diagram rendering..."
        npm install -g @mermaid-js/mermaid-cli
    fi
}

# Generate API documentation using Sphinx
generate_api_docs() {
    echo "Generating API documentation..."
    
    # Create sphinx config if it doesn't exist
    if [[ ! -f "${DOCS_DIR}/conf.py" ]]; then
        echo "Creating Sphinx configuration..."
        sphinx-quickstart -q -p "ULTRA" -a "ULTRA Team" -v "${VERSION}" --ext-autodoc --ext-viewcode --ext-mathjax "${TEMP_DIR}/sphinx"
        cp "${TEMP_DIR}/sphinx/conf.py" "${DOCS_DIR}/conf.py"
    fi
    
    # Customize sphinx config
    sed -i.bak 's/html_theme = .*/html_theme = "sphinx_rtd_theme"/' "${DOCS_DIR}/conf.py"
    sed -i.bak '/extensions = \[/a \    "sphinx.ext.autodoc",\n    "sphinx.ext.napoleon",\n    "sphinx.ext.viewcode",\n    "sphinx.ext.mathjax",\n    "sphinx_autodoc_typehints",\n    "sphinx_copybutton",\n    "sphinx_math_dollar",\n    "sphinx_gallery.gen_gallery",\n    "sphinx_togglebutton",\n    "autoapi.extension",\n    "nbsphinx",\n    "myst_parser",\n    "breathe",\n    "sphinxcontrib.bibtex",\n' "${DOCS_DIR}/conf.py"
    
    # Add autoapi configuration
    cat >> "${DOCS_DIR}/conf.py" << EOF

# AutoAPI configuration
autoapi_type = 'python'
autoapi_dirs = ['${SOURCE_DIR}']
autoapi_options = ['members', 'undoc-members', 'show-inheritance', 'show-module-summary', 'imported-members']

# Math configuration
math_number_all = True
math_eqref_format = "Eq. {number}"
math_dollar_inline = True
math_dollar_displayed = True

# BibTeX configuration
bibtex_bibfiles = ['${DOCS_DIR}/references.bib']

# Mermaid configuration
mermaid_output_format = 'svg'
mermaid_params = ['--width', '1000', '--backgroundColor', 'white']

# Napoleon settings
napoleon_google_docstring = True
napoleon_numpy_docstring = True
napoleon_include_init_with_doc = False
napoleon_include_private_with_doc = False
napoleon_include_special_with_doc = True
napoleon_use_admonition_for_examples = False
napoleon_use_admonition_for_notes = False
napoleon_use_admonition_for_references = False
napoleon_use_ivar = False
napoleon_use_param = True
napoleon_use_rtype = True
napoleon_type_aliases = None
napoleon_attr_annotations = True
EOF
    
    # Build API documentation
    sphinx-apidoc -f -o "${DOCS_DIR}/api" "${SOURCE_DIR}"
    sphinx-build -b html "${DOCS_DIR}" "${OUTPUT_DIR}/html"
}

# Generate diagrams from Mermaid sources
generate_diagrams() {
    echo "Generating diagrams from Mermaid sources..."
    
    # Find all Mermaid files
    find "${PROJECT_ROOT}" -name "*.mermaid" -o -name "*.mmd" | while read -r mermaid_file; do
        filename=$(basename "${mermaid_file%.*}")
        output_dir="${OUTPUT_DIR}/images"
        mkdir -p "${output_dir}"
        
        echo "  Processing: ${mermaid_file}"
        mmdc -i "${mermaid_file}" -o "${output_dir}/${filename}.svg" -t forest
        
        # Convert to PNG for PDF compatibility
        mmdc -i "${mermaid_file}" -o "${output_dir}/${filename}.png" -t forest
    done
    
    # Check for architecture diagram and convert it specially
    if [[ -f "${PROJECT_ROOT}/ULTRA System Architecture and Workflow.mermaid" ]]; then
        echo "  Processing main architecture diagram..."
        mmdc -i "${PROJECT_ROOT}/ULTRA System Architecture and Workflow.mermaid" -o "${OUTPUT_DIR}/images/ultra_architecture.svg" -t forest -w 1200 -H 900
        mmdc -i "${PROJECT_ROOT}/ULTRA System Architecture and Workflow.mermaid" -o "${OUTPUT_DIR}/images/ultra_architecture.png" -t forest -w 1200 -H 900
    fi
}

# Process TeX files with mathematical formulations
process_math_docs() {
    echo "Processing mathematical documentation..."
    
    # Find TeX files and convert them
    find "${DOCS_DIR}" -name "*.tex" | while read -r tex_file; do
        filename=$(basename "${tex_file%.*}")
        output_dir="${OUTPUT_DIR}/math"
        mkdir -p "${output_dir}"
        
        echo "  Processing: ${tex_file}"
        
        # Generate HTML from TeX
        pandoc -f latex -t html --mathjax -o "${output_dir}/${filename}.html" "${tex_file}"
        
        # Generate standalone PDF if needed
        if [[ "$FORMAT" == "pdf" || "$FORMAT" == "both" ]]; then
            pdflatex -output-directory="${output_dir}" "${tex_file}" > /dev/null
        fi
    done
    
    # Extract equations from main paper and create a formulation reference
    if [[ -f "${PROJECT_ROOT}/ULTRA" ]]; then
        echo "  Extracting mathematical formulations from main paper..."
        
        mkdir -p "${OUTPUT_DIR}/math/formulations"
        
        # Extract and organize equations
        grep -n -A 1 -B 1 "\\\begin{align}" "${PROJECT_ROOT}/ULTRA" | \
          awk 'BEGIN{i=1} /\\begin\{align\}/,/\\end\{align\}/ {print > "/tmp/ultra_docs_build/eq_" i ".tex"; if(/\\end\{align\}/) i++}' 
        
        # Create formulation index
        echo "<h1>ULTRA Mathematical Formulations</h1>" > "${OUTPUT_DIR}/math/formulations/index.html"
        echo "<ul>" >> "${OUTPUT_DIR}/math/formulations/index.html"
        
        # Process each extracted equation
        find "${TEMP_DIR}" -name "eq_*.tex" | sort -V | while read -r eq_file; do
            eq_num=$(basename "${eq_file%.*}" | cut -d'_' -f2)
            
            # Wrap equation in proper LaTeX document
            cat > "${TEMP_DIR}/eq_doc_${eq_num}.tex" << EOF
\\documentclass{article}
\\usepackage{amsmath,amssymb,amsfonts}
\\usepackage{mathtools}
\\begin{document}
$(cat "${eq_file}")
\\end{document}
EOF
            
            # Convert to HTML for web view
            pandoc -f latex -t html --mathjax -o "${OUTPUT_DIR}/math/formulations/eq_${eq_num}.html" "${TEMP_DIR}/eq_doc_${eq_num}.tex"
            
            # Add to index
            title=$(grep -A 1 "section{" "${eq_file}" | head -n 1 | sed 's/.*section{\(.*\)}.*/\1/')
            if [[ -z "$title" ]]; then
                title="Equation ${eq_num}"
            fi
            echo "<li><a href=\"eq_${eq_num}.html\">${title}</a></li>" >> "${OUTPUT_DIR}/math/formulations/index.html"
        done
        
        echo "</ul>" >> "${OUTPUT_DIR}/math/formulations/index.html"
    fi
}

# Generate comprehensive architecture documentation
generate_arch_docs() {
    echo "Generating architecture documentation..."
    
    # Create main architecture page
    mkdir -p "${OUTPUT_DIR}/architecture"
    
    cat > "${OUTPUT_DIR}/architecture/index.html" << EOF
<!DOCTYPE html>
<html>
<head>
    <title>ULTRA Architecture Documentation</title>
    <link rel="stylesheet" href="../_static/css/theme.css">
    <style>
        .arch-component {
            margin-bottom: 30px;
            padding: 15px;
            border-radius: 5px;
            background-color: #f9f9f9;
        }
        .arch-diagram {
            text-align: center;
            margin: 20px 0;
        }
        h1, h2, h3 {
            color: #2980b9;
        }
    </style>
</head>
<body>
    <div class="document">
        <div class="documentwrapper">
            <div class="bodywrapper">
                <div class="body" role="main">
                    <h1>ULTRA System Architecture</h1>
                    
                    <div class="arch-diagram">
                        <img src="../images/ultra_architecture.svg" alt="ULTRA Architecture Diagram">
                    </div>
                    
                    <p>The ULTRA (Ultimate Learning & Thought Reasoning Architecture) system consists of eight integrated subsystems designed to work together synergistically while maintaining modularity for independent development and testing.</p>
                    
                    <div class="arch-component">
                        <h2>1. Core Neural Architecture</h2>
                        <p>The foundational neuromorphic system that implements biologically-inspired neural processing with:</p>
                        <ul>
                            <li>Adaptive plasticity mechanisms</li>
                            <li>Synaptic pruning for network optimization</li>
                            <li>Neuromodulatory systems for global regulation</li>
                            <li>Biological timing circuits for oscillatory coordination</li>
                        </ul>
                        <p><a href="../api/ultra.core_neural.html">API Documentation</a> | <a href="../math/formulations/index.html#core_neural">Mathematical Formulations</a></p>
                    </div>
                    
                    <div class="arch-component">
                        <h2>2. Hyper-Dimensional Transformer</h2>
                        <p>An advanced transformer architecture featuring:</p>
                        <ul>
                            <li>Self-evolving attention mechanisms</li>
                            <li>Recursive processing capabilities</li>
                            <li>Temporal-causal modeling</li>
                            <li>Multi-scale knowledge embedding</li>
                            <li>Cross-modal dimension mapping</li>
                        </ul>
                        <p><a href="../api/ultra.hyper_transformer.html">API Documentation</a> | <a href="../math/formulations/index.html#transformer">Mathematical Formulations</a></p>
                    </div>
                    
                    <div class="arch-component">
                        <h2>3. Diffusion-Based Reasoning</h2>
                        <p>A novel approach to reasoning that leverages diffusion processes to:</p>
                        <ul>
                            <li>Navigate conceptual spaces</li>
                            <li>Model thought in continuous latent spaces</li>
                            <li>Perform goal-directed reverse diffusion reasoning</li>
                            <li>Quantify uncertainty in Bayesian frameworks</li>
                            <li>Implement probabilistic inference mechanisms</li>
                        </ul>
                        <p><a href="../api/ultra.diffusion_reasoning.html">API Documentation</a> | <a href="../math/formulations/index.html#diffusion">Mathematical Formulations</a></p>
                    </div>
                    
                    <div class="arch-component">
                        <h2>4. Meta-Cognitive System</h2>
                        <p>The executive function system that implements:</p>
                        <ul>
                            <li>Multi-path chain-of-thought reasoning</li>
                            <li>Tree of thought exploration with backtracking</li>
                            <li>Reasoning graphs for complex logical inference</li>
                            <li>Self-critique and reasoning refinement loops</li>
                            <li>Bias detection and correction mechanisms</li>
                            <li>Meta-learning for strategy adaptation</li>
                        </ul>
                        <p><a href="../api/ultra.meta_cognitive.html">API Documentation</a> | <a href="../math/formulations/index.html#metacognitive">Mathematical Formulations</a></p>
                    </div>
                    
                    <div class="arch-component">
                        <h2>5. Neuromorphic Processing Layer</h2>
                        <p>A specialized computing layer that implements:</p>
                        <ul>
                            <li>Spiking neural networks</li>
                            <li>Asynchronous event-based computing</li>
                            <li>Memristor arrays for efficient processing</li>
                            <li>Reservoir computing networks</li>
                            <li>Brain region emulation</li>
                        </ul>
                        <p><a href="../api/ultra.neuromorphic_processing.html">API Documentation</a> | <a href="../math/formulations/index.html#neuromorphic">Mathematical Formulations</a></p>
                    </div>
                    
                    <div class="arch-component">
                        <h2>6. Emergent Consciousness Lattice</h2>
                        <p>An experimental framework designed to foster:</p>
                        <ul>
                            <li>Self-awareness through capability modeling</li>
                            <li>Intentionality and goal-directed behavior</li>
                            <li>Integrated information processing</li>
                            <li>Attentional awareness mechanisms</li>
                            <li>Global workspace for system-wide information sharing</li>
                        </ul>
                        <p><a href="../api/ultra.emergent_consciousness.html">API Documentation</a> | <a href="../math/formulations/index.html#consciousness">Mathematical Formulations</a></p>
                    </div>
                    
                    <div class="arch-component">
                        <h2>7. Neuro-Symbolic Integration</h2>
                        <p>A bidirectional bridge between neural and symbolic representations, enabling:</p>
                        <ul>
                            <li>Logical reasoning within neural frameworks</li>
                            <li>Symbolic representation learning</li>
                            <li>Semantic alignment between paradigms</li>
                            <li>Program synthesis for algorithmic solutions</li>
                            <li>Rule extraction and learning</li>
                        </ul>
                        <p><a href="../api/ultra.neuro_symbolic.html">API Documentation</a> | <a href="../math/formulations/index.html#neurosymbolic">Mathematical Formulations</a></p>
                    </div>
                    
                    <div class="arch-component">
                        <h2>8. Self-Evolution System</h2>
                        <p>Mechanisms for architecture search, self-modification, and computational reflection that allow the system to:</p>
                        <ul>
                            <li>Discover optimal neural architectures</li>
                            <li>Safely modify its own code and structure</li>
                            <li>Reason about its own computational processes</li>
                            <li>Guide evolution toward desired properties</li>
                            <li>Detect and foster emergent innovations</li>
                        </ul>
                        <p><a href="../api/ultra.self_evolution.html">API Documentation</a> | <a href="../math/formulations/index.html#evolution">Mathematical Formulations</a></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
EOF
}

# Create PDF documentation
generate_pdf_docs() {
    echo "Generating PDF documentation..."
    
    if [[ "$FORMAT" != "pdf" && "$FORMAT" != "both" ]]; then
        return
    fi
    
    # Create LaTeX document for the whole system
    mkdir -p "${OUTPUT_DIR}/pdf"
    
    # Main paper first
    if [[ -f "${PROJECT_ROOT}/ULTRA" ]]; then
        echo "  Processing main research paper..."
        
        # Convert to LaTeX if needed
        if ! file "${PROJECT_ROOT}/ULTRA" | grep -q "LaTeX"; then
            pandoc -f markdown -t latex -o "${TEMP_DIR}/ULTRA.tex" "${PROJECT_ROOT}/ULTRA"
        else
            cp "${PROJECT_ROOT}/ULTRA" "${TEMP_DIR}/ULTRA.tex"
        fi
        
        # Create LaTeX document
        cat > "${TEMP_DIR}/ultra_main.tex" << EOF
\\documentclass[11pt,a4paper]{article}
\\usepackage{amsmath,amssymb,amsfonts}
\\usepackage{graphicx}
\\usepackage{hyperref}
\\usepackage{xcolor}
\\usepackage{listings}
\\usepackage{booktabs}
\\usepackage{algorithmic}
\\usepackage{algorithm}
\\usepackage{mathtools}
\\usepackage{tikz}
\\usepackage{float}
\\usepackage{geometry}
\\geometry{margin=1in}

\\title{ULTRA: Ultimate Learning \\& Thought Reasoning Architecture}
\\author{ULTRA Research Team}
\\date{Version ${VERSION}}

\\begin{document}

\\maketitle
\\tableofcontents
\\newpage

\\input{${TEMP_DIR}/ULTRA.tex}

\\end{document}
EOF
        
        # Generate PDF
        cd "${TEMP_DIR}" || exit 1
        pdflatex -interaction=nonstopmode ultra_main.tex > /dev/null
        pdflatex -interaction=nonstopmode ultra_main.tex > /dev/null  # Second run for references
        cp ultra_main.pdf "${OUTPUT_DIR}/pdf/ULTRA_Technical_Paper_v${VERSION}.pdf"
    fi
    
    # Generate implementation documentation
    sphinx-build -b latex "${DOCS_DIR}" "${TEMP_DIR}/latex"
    
    # Build PDF
    cd "${TEMP_DIR}/latex" || exit 1
    make > /dev/null
    cp ULTRA.pdf "${OUTPUT_DIR}/pdf/ULTRA_Implementation_Guide_v${VERSION}.pdf"
    
    # Generate combined PDF with all documentation
    echo "  Generating comprehensive reference manual..."
    cat > "${TEMP_DIR}/ultra_complete.tex" << EOF
\\documentclass[11pt,a4paper]{report}
\\usepackage{amsmath,amssymb,amsfonts}
\\usepackage{graphicx}
\\usepackage{hyperref}
\\usepackage{xcolor}
\\usepackage{listings}
\\usepackage{booktabs}
\\usepackage{algorithmic}
\\usepackage{algorithm}
\\usepackage{mathtools}
\\usepackage{tikz}
\\usepackage{float}
\\usepackage{geometry}
\\usepackage{pdfpages}
\\geometry{margin=1in}

\\title{ULTRA: Complete Documentation}
\\author{ULTRA Research Team}
\\date{Version ${VERSION}}

\\begin{document}

\\maketitle
\\tableofcontents
\\newpage

\\chapter{Technical Overview}
\\includepdf[pages=-]{${OUTPUT_DIR}/pdf/ULTRA_Technical_Paper_v${VERSION}.pdf}

\\chapter{Implementation Guide}
\\includepdf[pages=-]{${OUTPUT_DIR}/pdf/ULTRA_Implementation_Guide_v${VERSION}.pdf}

\\chapter{API Reference}
% This will be generated from API docs

\\chapter{Mathematical Formulations}
% This will be generated from equations

\\end{document}
EOF
    
    cd "${TEMP_DIR}" || exit 1
    pdflatex -interaction=nonstopmode ultra_complete.tex > /dev/null
    pdflatex -interaction=nonstopmode ultra_complete.tex > /dev/null  # Second run for references
    cp ultra_complete.pdf "${OUTPUT_DIR}/pdf/ULTRA_Complete_Documentation_v${VERSION}.pdf"
}

# Create landing page for HTML output
create_landing_page() {
    echo "Creating documentation landing page..."
    
    if [[ "$FORMAT" != "html" && "$FORMAT" != "both" ]]; then
        return
    fi
    
    cat > "${OUTPUT_DIR}/html/index.html" << EOF
<!DOCTYPE html>
<html>
<head>
    <title>ULTRA Documentation</title>
    <link rel="stylesheet" href="_static/css/theme.css">
    <style>
        .grid-container {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            padding: 20px;
        }
        .card {
            border: 1px solid #e0e0e0;
            border-radius: 5px;
            padding: 20px;
            transition: all 0.3s ease;
        }
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        .banner {
            text-align: center;
            padding: 40px 20px;
            background-color: #2980b9;
            color: white;
        }
        .version {
            font-size: 0.8em;
            color: #666;
            text-align: center;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="banner">
        <h1>ULTRA Documentation</h1>
        <p>Ultimate Learning & Thought Reasoning Architecture</p>
        <div class="version">Version ${VERSION}</div>
    </div>
    
    <div class="grid-container">
        <div class="card">
            <h2>Architecture Overview</h2>
            <p>Comprehensive overview of the ULTRA system architecture and its components.</p>
            <a href="architecture/index.html">View Architecture Documentation</a>
        </div>
        
        <div class="card">
            <h2>API Reference</h2>
            <p>Detailed API documentation for all ULTRA modules and components.</p>
            <a href="api/modules.html">View API Documentation</a>
        </div>
        
        <div class="card">
            <h2>Mathematical Formulations</h2>
            <p>Mathematical foundations and formulations used in ULTRA.</p>
            <a href="math/formulations/index.html">View Formulations</a>
        </div>
        
        <div class="card">
            <h2>Implementation Guide</h2>
            <p>Guide for implementing and extending ULTRA components.</p>
            <a href="guide/index.html">View Implementation Guide</a>
        </div>
        
        <div class="card">
            <h2>Tutorials</h2>
            <p>Step-by-step tutorials for working with ULTRA.</p>
            <a href="tutorials/index.html">View Tutorials</a>
        </div>
        
        <div class="card">
            <h2>PDF Documentation</h2>
            <p>Downloadable PDF versions of the ULTRA documentation.</p>
            <ul>
                <li><a href="../pdf/ULTRA_Technical_Paper_v${VERSION}.pdf">Technical Paper</a></li>
                <li><a href="../pdf/ULTRA_Implementation_Guide_v${VERSION}.pdf">Implementation Guide</a></li>
                <li><a href="../pdf/ULTRA_Complete_Documentation_v${VERSION}.pdf">Complete Documentation</a></li>
            </ul>
        </div>
    </div>
</body>
</html>
EOF
}

# Main function
main() {
    parse_args "$@"
    setup
    
    echo "Starting ULTRA documentation build (Version: ${VERSION})"
    
    # Generate all documentation components
    generate_api_docs
    generate_diagrams
    process_math_docs
    generate_arch_docs
    
    # Generate PDF documentation if requested
    if [[ "$FORMAT" == "pdf" || "$FORMAT" == "both" ]]; then
        generate_pdf_docs
    fi
    
    # Create landing page for HTML output
    if [[ "$FORMAT" == "html" || "$FORMAT" == "both" ]]; then
        create_landing_page
    fi
    
    echo "Documentation build complete!"
    echo "Output is available at: ${OUTPUT_DIR}"
}

# Execute main function
main "$@"