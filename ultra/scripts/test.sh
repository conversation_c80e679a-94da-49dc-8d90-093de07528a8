#!/bin/bash

# ULTRA Test Script
# This script runs comprehensive tests for all ULTRA subsystems
# Usage: ./test.sh [options]

set -e  # Exit immediately if a command exits with non-zero status

# ANSI color codes for better readability
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Get script directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
ULTRA_ROOT="$(dirname "$(dirname "$SCRIPT_DIR")")"

# Import configuration
if [ -f "$ULTRA_ROOT/config/test_config.yaml" ]; then
    echo -e "${BLUE}Loading test configuration from $ULTRA_ROOT/config/test_config.yaml${NC}"
else
    echo -e "${YELLOW}No test configuration found. Using default settings.${NC}"
fi

# Function to display usage
function display_usage() {
    echo "Usage: ./test.sh [options]"
    echo "Options:"
    echo "  --all                Run all tests"
    echo "  --unit               Run unit tests only"
    echo "  --integration        Run integration tests only"
    echo "  --subsystem [name]   Test specific subsystem (core_neural, hyper_transformer, etc.)"
    echo "  --quick              Run quick tests (subset of all tests)"
    echo "  --verbose            Increase verbosity"
    echo "  --help               Display this help message"
}

# Parse command line arguments
ALL_TESTS=false
UNIT_TESTS=false
INTEGRATION_TESTS=false
SUBSYSTEM=""
QUICK_MODE=false
VERBOSE=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --all)
            ALL_TESTS=true
            shift
            ;;
        --unit)
            UNIT_TESTS=true
            shift
            ;;
        --integration)
            INTEGRATION_TESTS=true
            shift
            ;;
        --subsystem)
            if [[ $# -gt 1 ]]; then
                SUBSYSTEM=$2
                shift 2
            else
                echo -e "${RED}Error: --subsystem requires an argument${NC}"
                display_usage
                exit 1
            fi
            ;;
        --quick)
            QUICK_MODE=true
            shift
            ;;
        --verbose)
            VERBOSE=true
            shift
            ;;
        --help)
            display_usage
            exit 0
            ;;
        *)
            echo -e "${RED}Error: Unknown option $1${NC}"
            display_usage
            exit 1
            ;;
    esac
done

# If no test type is specified, run all tests
if [ "$ALL_TESTS" = false ] && [ "$UNIT_TESTS" = false ] && [ "$INTEGRATION_TESTS" = false ] && [ -z "$SUBSYSTEM" ]; then
    ALL_TESTS=true
fi

# Environment check
function check_environment() {
    echo -e "${BLUE}Checking environment...${NC}"
    
    # Check Python version
    PYTHON_VERSION=$(python -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
    if [[ $(echo "$PYTHON_VERSION >= 3.8" | bc -l) -eq 1 ]]; then
        echo -e "${GREEN}✓ Python $PYTHON_VERSION detected${NC}"
    else
        echo -e "${RED}✗ Python 3.8+ required, but Python $PYTHON_VERSION detected${NC}"
        exit 1
    fi
    
    # Check required Python packages
    REQUIRED_PACKAGES=("numpy" "torch" "scipy" "matplotlib" "pyyaml" "pytest")
    MISSING_PACKAGES=()
    
    for pkg in "${REQUIRED_PACKAGES[@]}"; do
        if ! python -c "import $pkg" &> /dev/null; then
            MISSING_PACKAGES+=("$pkg")
        fi
    done
    
    if [ ${#MISSING_PACKAGES[@]} -gt 0 ]; then
        echo -e "${RED}✗ Missing required Python packages: ${MISSING_PACKAGES[*]}${NC}"
        echo -e "${YELLOW}Run 'pip install ${MISSING_PACKAGES[*]}' to install them.${NC}"
        exit 1
    else
        echo -e "${GREEN}✓ All required Python packages are installed${NC}"
    fi
    
    # Check for CUDA if available
    if python -c "import torch; print(torch.cuda.is_available())" 2>/dev/null | grep -q "True"; then
        echo -e "${GREEN}✓ CUDA is available for GPU acceleration${NC}"
        # Display CUDA version and available GPUs
        if [ "$VERBOSE" = true ]; then
            python -c "import torch; print(f'CUDA Version: {torch.version.cuda}')"
            python -c "import torch; print(f'GPU(s): {[torch.cuda.get_device_name(i) for i in range(torch.cuda.device_count())]}')"
        fi
    else
        echo -e "${YELLOW}! CUDA is not available. Tests will run on CPU only.${NC}"
    fi
}

# Main testing function
function run_tests() {
    local test_type=$1
    local subsystem=$2
    
    # Build the pytest command
    PYTEST_CMD="python -m pytest"
    
    if [ "$VERBOSE" = true ]; then
        PYTEST_CMD="$PYTEST_CMD -v"
    fi
    
    if [ "$QUICK_MODE" = true ]; then
        PYTEST_CMD="$PYTEST_CMD -xvs"
    fi
    
    # Add coverage if not in quick mode
    if [ "$QUICK_MODE" = false ]; then
        PYTEST_CMD="$PYTEST_CMD --cov=ultra --cov-report=term"
    fi
    
    # Determine test directory based on test type and subsystem
    TEST_DIR="$ULTRA_ROOT/tests"
    
    if [ "$test_type" = "unit" ]; then
        if [ -z "$subsystem" ]; then
            echo -e "${BLUE}Running unit tests for all subsystems...${NC}"
            PYTEST_CMD="$PYTEST_CMD $TEST_DIR/test_*.py"
        else
            echo -e "${BLUE}Running unit tests for $subsystem subsystem...${NC}"
            PYTEST_CMD="$PYTEST_CMD $TEST_DIR/test_${subsystem}.py"
        fi
    elif [ "$test_type" = "integration" ]; then
        if [ -z "$subsystem" ]; then
            echo -e "${BLUE}Running integration tests for all subsystems...${NC}"
            PYTEST_CMD="$PYTEST_CMD $TEST_DIR/integration/"
        else
            echo -e "${BLUE}Running integration tests for $subsystem subsystem...${NC}"
            PYTEST_CMD="$PYTEST_CMD $TEST_DIR/integration/test_${subsystem}_integration.py"
        fi
    fi
    
    # Run the tests
    echo -e "${BLUE}Executing: $PYTEST_CMD${NC}"
    eval "$PYTEST_CMD"
    
    local exit_code=$?
    if [ $exit_code -eq 0 ]; then
        echo -e "${GREEN}Tests completed successfully!${NC}"
    else
        echo -e "${RED}Tests failed with exit code $exit_code${NC}"
        return $exit_code
    fi
}

# Function to run neuromorphic core tests
function test_core_neural() {
    echo -e "${BLUE}Running tests for Core Neural Architecture...${NC}"
    
    # Test each component of the Core Neural Architecture
    components=("neuromorphic_core" "neuroplasticity_engine" "synaptic_pruning" "neuromodulation" "biological_timing")
    
    for component in "${components[@]}"; do
        echo -e "${YELLOW}Testing $component...${NC}"
        PYTEST_CMD="python -m pytest $ULTRA_ROOT/tests/test_core_neural.py::Test${component^} -v"
        
        if [ "$VERBOSE" = true ]; then
            eval "$PYTEST_CMD"
        else
            eval "$PYTEST_CMD" > /dev/null
        fi
        
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}✓ $component tests passed${NC}"
        else
            echo -e "${RED}✗ $component tests failed${NC}"
            return 1
        fi
    done
    
    return 0
}

# Function to run hyper-dimensional transformer tests
function test_hyper_transformer() {
    echo -e "${BLUE}Running tests for Hyper-Dimensional Transformer...${NC}"
    
    # Test each component of the Hyper-Dimensional Transformer
    components=("dynamic_attention" "contextual_bias" "recursive_transformer" "temporal_causal" "multiscale_embedding")
    
    for component in "${components[@]}"; do
        echo -e "${YELLOW}Testing $component...${NC}"
        PYTEST_CMD="python -m pytest $ULTRA_ROOT/tests/test_transformers.py::Test${component^} -v"
        
        if [ "$VERBOSE" = true ]; then
            eval "$PYTEST_CMD"
        else
            eval "$PYTEST_CMD" > /dev/null
        fi
        
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}✓ $component tests passed${NC}"
        else
            echo -e "${RED}✗ $component tests failed${NC}"
            return 1
        fi
    done
    
    return 0
}

# Function to run diffusion-based reasoning tests
function test_diffusion() {
    echo -e "${BLUE}Running tests for Diffusion-Based Reasoning...${NC}"
    
    # Test each component of the Diffusion-Based Reasoning
    components=("conceptual_diffusion" "thought_latent_space" "reverse_diffusion" "bayesian_uncertainty" "probabilistic_inference")
    
    for component in "${components[@]}"; do
        echo -e "${YELLOW}Testing $component...${NC}"
        PYTEST_CMD="python -m pytest $ULTRA_ROOT/tests/test_diffusion.py::Test${component^} -v"
        
        if [ "$VERBOSE" = true ]; then
            eval "$PYTEST_CMD"
        else
            eval "$PYTEST_CMD" > /dev/null
        fi
        
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}✓ $component tests passed${NC}"
        else
            echo -e "${RED}✗ $component tests failed${NC}"
            return 1
        fi
    done
    
    return 0
}

# Function to run meta-cognitive system tests
function test_metacognitive() {
    echo -e "${BLUE}Running tests for Meta-Cognitive System...${NC}"
    
    # Test each component of the Meta-Cognitive System
    components=("chain_of_thought" "tree_of_thought" "reasoning_graphs" "self_critique" "bias_detection" "meta_learning")
    
    for component in "${components[@]}"; do
        echo -e "${YELLOW}Testing $component...${NC}"
        PYTEST_CMD="python -m pytest $ULTRA_ROOT/tests/test_metacognitive.py::Test${component^} -v"
        
        if [ "$VERBOSE" = true ]; then
            eval "$PYTEST_CMD"
        else
            eval "$PYTEST_CMD" > /dev/null
        fi
        
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}✓ $component tests passed${NC}"
        else
            echo -e "${RED}✗ $component tests failed${NC}"
            return 1
        fi
    done
    
    return 0
}

# Function to run neuromorphic processing tests
function test_neuromorphic() {
    echo -e "${BLUE}Running tests for Neuromorphic Processing Layer...${NC}"
    
    # Test each component of the Neuromorphic Processing Layer
    components=("spiking_networks" "event_based_computing" "memristor_array" "reservoir_computing" "brain_region_emulation")
    
    for component in "${components[@]}"; do
        echo -e "${YELLOW}Testing $component...${NC}"
        PYTEST_CMD="python -m pytest $ULTRA_ROOT/tests/test_neuromorphic.py::Test${component^} -v"
        
        if [ "$VERBOSE" = true ]; then
            eval "$PYTEST_CMD"
        else
            eval "$PYTEST_CMD" > /dev/null
        fi
        
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}✓ $component tests passed${NC}"
        else
            echo -e "${RED}✗ $component tests failed${NC}"
            return 1
        fi
    done
    
    return 0
}

# Function to run emergent consciousness tests
function test_consciousness() {
    echo -e "${BLUE}Running tests for Emergent Consciousness Lattice...${NC}"
    
    # Test each component of the Emergent Consciousness Lattice
    components=("self_awareness" "intentionality" "integrated_information" "attentional_awareness" "global_workspace")
    
    for component in "${components[@]}"; do
        echo -e "${YELLOW}Testing $component...${NC}"
        PYTEST_CMD="python -m pytest $ULTRA_ROOT/tests/test_consciousness.py::Test${component^} -v"
        
        if [ "$VERBOSE" = true ]; then
            eval "$PYTEST_CMD"
        else
            eval "$PYTEST_CMD" > /dev/null
        fi
        
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}✓ $component tests passed${NC}"
        else
            echo -e "${RED}✗ $component tests failed${NC}"
            return 1
        fi
    done
    
    return 0
}

# Function to run neuro-symbolic integration tests
function test_neurosymbolic() {
    echo -e "${BLUE}Running tests for Neuro-Symbolic Integration...${NC}"
    
    # Test each component of the Neuro-Symbolic Integration
    components=("logical_reasoning" "symbolic_representation" "neuro_symbolic_bridge" "program_synthesis" "rule_extraction")
    
    for component in "${components[@]}"; do
        echo -e "${YELLOW}Testing $component...${NC}"
        PYTEST_CMD="python -m pytest $ULTRA_ROOT/tests/test_neurosymbolic.py::Test${component^} -v"
        
        if [ "$VERBOSE" = true ]; then
            eval "$PYTEST_CMD"
        else
            eval "$PYTEST_CMD" > /dev/null
        fi
        
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}✓ $component tests passed${NC}"
        else
            echo -e "${RED}✗ $component tests failed${NC}"
            return 1
        fi
    done
    
    return 0
}

# Function to run self-evolution tests
function test_evolution() {
    echo -e "${BLUE}Running tests for Self-Evolution System...${NC}"
    
    # Test each component of the Self-Evolution System
    components=("architecture_search" "self_modification" "computational_reflection" "evolutionary_steering" "innovation_detection")
    
    for component in "${components[@]}"; do
        echo -e "${YELLOW}Testing $component...${NC}"
        PYTEST_CMD="python -m pytest $ULTRA_ROOT/tests/test_evolution.py::Test${component^} -v"
        
        if [ "$VERBOSE" = true ]; then
            eval "$PYTEST_CMD"
        else
            eval "$PYTEST_CMD" > /dev/null
        fi
        
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}✓ $component tests passed${NC}"
        else
            echo -e "${RED}✗ $component tests failed${NC}"
            return 1
        fi
    done
    
    return 0
}

# Function to test ULTRA system integration
function test_system_integration() {
    echo -e "${BLUE}Running system integration tests...${NC}"
    
    PYTEST_CMD="python -m pytest $ULTRA_ROOT/tests/integration/test_system_integration.py -v"
    
    if [ "$VERBOSE" = true ]; then
        eval "$PYTEST_CMD"
    else
        eval "$PYTEST_CMD" > /dev/null
    fi
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✓ System integration tests passed${NC}"
        return 0
    else
        echo -e "${RED}✗ System integration tests failed${NC}"
        return 1
    fi
}

# Function to test example applications
function test_examples() {
    echo -e "${BLUE}Running tests for example applications...${NC}"
    
    # Test each example application
    examples=("basic_inference" "reasoning_example" "self_improvement_demo")
    
    for example in "${examples[@]}"; do
        echo -e "${YELLOW}Testing $example...${NC}"
        
        # First, check if the example script exists
        if [ -f "$ULTRA_ROOT/examples/${example}.py" ]; then
            # Try running the example with a small test dataset
            if [ "$VERBOSE" = true ]; then
                python "$ULTRA_ROOT/examples/${example}.py" --test
            else
                python "$ULTRA_ROOT/examples/${example}.py" --test > /dev/null
            fi
            
            if [ $? -eq 0 ]; then
                echo -e "${GREEN}✓ $example executed successfully${NC}"
            else
                echo -e "${RED}✗ $example execution failed${NC}"
                if [ "$QUICK_MODE" = false ]; then
                    return 1
                fi
            fi
        else
            echo -e "${YELLOW}! $example script not found, skipping${NC}"
        fi
    done
    
    return 0
}

# Function to run performance benchmarks
function run_benchmarks() {
    if [ "$QUICK_MODE" = true ]; then
        echo -e "${YELLOW}Skipping benchmarks in quick mode${NC}"
        return 0
    fi
    
    echo -e "${BLUE}Running performance benchmarks...${NC}"
    
    # Check if the benchmark script exists
    if [ -f "$ULTRA_ROOT/scripts/benchmark.py" ]; then
        python "$ULTRA_ROOT/scripts/benchmark.py"
        
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}✓ Benchmarks completed successfully${NC}"
        else
            echo -e "${RED}✗ Benchmarks failed${NC}"
            return 1
        fi
    else
        echo -e "${YELLOW}! Benchmark script not found, skipping${NC}"
    fi
    
    return 0
}

# Function to generate test report
function generate_report() {
    echo -e "${BLUE}Generating test report...${NC}"
    
    REPORT_DIR="$ULTRA_ROOT/test_reports"
    mkdir -p "$REPORT_DIR"
    
    REPORT_FILE="$REPORT_DIR/test_report_$(date +%Y%m%d_%H%M%S).txt"
    
    {
        echo "ULTRA Test Report"
        echo "================="
        echo "Generated on: $(date)"
        echo ""
        echo "System Information:"
        echo "-------------------"
        echo "OS: $(uname -s)"
        echo "Python: $(python --version 2>&1)"
        echo "PyTorch: $(python -c "import torch; print(torch.__version__)" 2>/dev/null || echo "Not installed")"
        echo "CUDA: $(python -c "import torch; print(torch.version.cuda if torch.cuda.is_available() else 'Not available')" 2>/dev/null || echo "Not available")"
        echo ""
        echo "Test Results:"
        echo "-------------"
        
        if [ "$ALL_TESTS" = true ] || [ "$UNIT_TESTS" = true ]; then
            echo "Unit Tests: $TEST_RESULTS_UNIT"
        fi
        
        if [ "$ALL_TESTS" = true ] || [ "$INTEGRATION_TESTS" = true ]; then
            echo "Integration Tests: $TEST_RESULTS_INTEGRATION"
        fi
        
        if [ ! -z "$SUBSYSTEM" ]; then
            echo "$SUBSYSTEM Tests: $TEST_RESULTS_SUBSYSTEM"
        fi
        
        echo ""
        echo "Coverage Report:"
        echo "---------------"
        if [ "$QUICK_MODE" = false ]; then
            python -m pytest --cov=ultra --cov-report=term-missing "$ULTRA_ROOT/tests" | grep -A 100 "Name" >> "$REPORT_FILE"
        else
            echo "Coverage report not available in quick mode"
        fi
    } > "$REPORT_FILE"
    
    echo -e "${GREEN}Test report generated: $REPORT_FILE${NC}"
}

# Main execution
echo -e "${BLUE}======================${NC}"
echo -e "${BLUE}ULTRA Testing Script${NC}"
echo -e "${BLUE}======================${NC}"
echo ""

# Check the environment
check_environment

# Set up result variables
TEST_RESULTS_UNIT="SKIPPED"
TEST_RESULTS_INTEGRATION="SKIPPED"
TEST_RESULTS_SUBSYSTEM="SKIPPED"

# Execute tests based on flags
EXIT_CODE=0

# Run unit tests if specified
if [ "$ALL_TESTS" = true ] || [ "$UNIT_TESTS" = true ]; then
    run_tests "unit" ""
    if [ $? -eq 0 ]; then
        TEST_RESULTS_UNIT="PASSED"
    else
        TEST_RESULTS_UNIT="FAILED"
        EXIT_CODE=1
    fi
fi

# Run integration tests if specified
if [ "$ALL_TESTS" = true ] || [ "$INTEGRATION_TESTS" = true ]; then
    run_tests "integration" ""
    if [ $? -eq 0 ]; then
        TEST_RESULTS_INTEGRATION="PASSED"
    else
        TEST_RESULTS_INTEGRATION="FAILED"
        EXIT_CODE=1
    fi
fi

# Run tests for specific subsystem if specified
if [ ! -z "$SUBSYSTEM" ]; then
    case $SUBSYSTEM in
        "core_neural")
            test_core_neural
            ;;
        "hyper_transformer")
            test_hyper_transformer
            ;;
        "diffusion")
            test_diffusion
            ;;
        "metacognitive")
            test_metacognitive
            ;;
        "neuromorphic")
            test_neuromorphic
            ;;
        "consciousness")
            test_consciousness
            ;;
        "neurosymbolic")
            test_neurosymbolic
            ;;
        "evolution")
            test_evolution
            ;;
        *)
            echo -e "${RED}Error: Unknown subsystem $SUBSYSTEM${NC}"
            display_usage
            exit 1
            ;;
    esac
    
    if [ $? -eq 0 ]; then
        TEST_RESULTS_SUBSYSTEM="PASSED"
    else
        TEST_RESULTS_SUBSYSTEM="FAILED"
        EXIT_CODE=1
    fi
fi

# Run system integration test if running all tests
if [ "$ALL_TESTS" = true ]; then
    test_system_integration
    if [ $? -ne 0 ]; then
        EXIT_CODE=1
    fi
    
    # Test example applications
    test_examples
    if [ $? -ne 0 ]; then
        EXIT_CODE=1
    fi
    
    # Run benchmarks
    run_benchmarks
    if [ $? -ne 0 ]; then
        EXIT_CODE=1
    fi
fi

# Generate test report
if [ "$QUICK_MODE" = false ]; then
    generate_report
fi

# Final output
echo ""
echo -e "${BLUE}======================${NC}"
echo -e "${BLUE}Testing Summary${NC}"
echo -e "${BLUE}======================${NC}"

if [ "$ALL_TESTS" = true ] || [ "$UNIT_TESTS" = true ]; then
    if [ "$TEST_RESULTS_UNIT" = "PASSED" ]; then
        echo -e "${GREEN}Unit Tests: $TEST_RESULTS_UNIT${NC}"
    else
        echo -e "${RED}Unit Tests: $TEST_RESULTS_UNIT${NC}"
    fi
fi

if [ "$ALL_TESTS" = true ] || [ "$INTEGRATION_TESTS" = true ]; then
    if [ "$TEST_RESULTS_INTEGRATION" = "PASSED" ]; then
        echo -e "${GREEN}Integration Tests: $TEST_RESULTS_INTEGRATION${NC}"
    else
        echo -e "${RED}Integration Tests: $TEST_RESULTS_INTEGRATION${NC}"
    fi
fi

if [ ! -z "$SUBSYSTEM" ]; then
    if [ "$TEST_RESULTS_SUBSYSTEM" = "PASSED" ]; then
        echo -e "${GREEN}$SUBSYSTEM Tests: $TEST_RESULTS_SUBSYSTEM${NC}"
    else
        echo -e "${RED}$SUBSYSTEM Tests: $TEST_RESULTS_SUBSYSTEM${NC}"
    fi
fi

echo ""
if [ $EXIT_CODE -eq 0 ]; then
    echo -e "${GREEN}All tests completed successfully!${NC}"
else
    echo -e "${RED}Some tests failed. Please check the output above for details.${NC}"
fi

exit $EXIT_CODE