#!/usr/bin/env bash
#
# ULTRA System Shutdown Script
# This script safely stops all running ULTRA system components
# following the correct dependency order.
#
# Usage: ./stop.sh [options]
# Options:
#   -f, --force     Force kill processes if graceful shutdown fails
#   -v, --verbose   Show detailed shutdown information
#   -h, --help      Display this help message and exit

set -e

# Script configuration
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
ULTRA_ROOT="$(dirname "$SCRIPT_DIR")"
ULTRA_PID_DIR="${ULTRA_ROOT}/var/run"
ULTRA_LOG_DIR="${ULTRA_ROOT}/var/log"
SHUTDOWN_TIMEOUT=30  # Seconds to wait for graceful shutdown before force kill
VERBOSE=false
FORCE=false

# Color codes for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Import common functions
if [ -f "${SCRIPT_DIR}/common.sh" ]; then
    source "${SCRIPT_DIR}/common.sh"
fi

# Process command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -f|--force)
            FORCE=true
            shift
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -h|--help)
            echo "Usage: ./stop.sh [options]"
            echo "Options:"
            echo "  -f, --force     Force kill processes if graceful shutdown fails"
            echo "  -v, --verbose   Show detailed shutdown information" 
            echo "  -h, --help      Display this help message and exit"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            echo "Use -h or --help for usage information."
            exit 1
            ;;
    esac
done

# Create necessary directories if they don't exist
mkdir -p "${ULTRA_PID_DIR}" "${ULTRA_LOG_DIR}"

# Log function
log() {
    local level=$1
    local message=$2
    local timestamp=$(date +"%Y-%m-%d %H:%M:%S")
    
    echo -e "${timestamp} [${level}] ${message}" | tee -a "${ULTRA_LOG_DIR}/ultra_shutdown.log"
    
    if [ "$VERBOSE" = true ] || [ "$level" = "ERROR" ]; then
        case $level in
            "INFO")
                echo -e "${GREEN}${message}${NC}"
                ;;
            "WARN")
                echo -e "${YELLOW}${message}${NC}"
                ;;
            "ERROR")
                echo -e "${RED}${message}${NC}"
                ;;
            *)
                echo "$message"
                ;;
        esac
    fi
}

# Check if process is running
is_process_running() {
    local pid=$1
    if [ -z "$pid" ]; then
        return 1
    fi
    
    if kill -0 "$pid" 2>/dev/null; then
        return 0
    else
        return 1
    fi
}

# Stop a component with graceful shutdown and optional force kill
stop_component() {
    local component=$1
    local pid_file="${ULTRA_PID_DIR}/${component}.pid"
    
    if [ ! -f "$pid_file" ]; then
        log "INFO" "${component} is not running (no PID file)"
        return 0
    fi
    
    local pid=$(cat "$pid_file")
    if ! is_process_running "$pid"; then
        log "INFO" "${component} is not running (stale PID file)"
        rm -f "$pid_file"
        return 0
    fi
    
    log "INFO" "Stopping ${component} (PID: ${pid})..."
    
    # Send SIGTERM for graceful shutdown
    kill -15 "$pid" 2>/dev/null
    
    # Wait for process to exit
    local count=0
    while is_process_running "$pid" && [ $count -lt $SHUTDOWN_TIMEOUT ]; do
        sleep 1
        count=$((count + 1))
        if [ "$VERBOSE" = true ] && [ $((count % 5)) -eq 0 ]; then
            log "INFO" "Waiting for ${component} to exit... (${count}s elapsed)"
        fi
    done
    
    # Check if process is still running
    if is_process_running "$pid"; then
        if [ "$FORCE" = true ]; then
            log "WARN" "${component} did not exit gracefully, sending SIGKILL..."
            kill -9 "$pid" 2>/dev/null
            sleep 1
        else
            log "ERROR" "${component} is still running after ${SHUTDOWN_TIMEOUT}s. Use --force to force kill."
            return 1
        fi
    fi
    
    # Check final status
    if is_process_running "$pid"; then
        log "ERROR" "Failed to stop ${component} (PID: ${pid})"
        return 1
    else
        log "INFO" "${component} stopped successfully"
        rm -f "$pid_file"
        return 0
    fi
}

# Stop components in reverse dependency order
shutdown_ultra() {
    log "INFO" "Beginning ULTRA system shutdown..."
    
    # Stop UI and API server components first
    stop_component "ultra_api_server"
    stop_component "ultra_web_ui"
    
    # Stop high-level reasoning components
    stop_component "self_evolution_system"
    stop_component "meta_cognitive_system"
    
    # Stop middle-tier components
    stop_component "neuro_symbolic_integration"
    stop_component "emergent_consciousness_lattice"
    stop_component "diffusion_reasoning"
    stop_component "hyper_transformer"
    
    # Stop core neural and processing components
    stop_component "neuromorphic_processing_layer"
    stop_component "core_neural_architecture"
    
    # Stop monitoring, logging, and infrastructure services
    stop_component "ultra_monitoring"
    stop_component "ultra_resource_manager"
    
    # Stop database and storage services
    stop_component "ultra_knowledge_db"
    stop_component "ultra_vector_store"
    
    # Check for any remaining ULTRA processes
    check_remaining_processes
    
    log "INFO" "ULTRA system shutdown complete"
}

# Check for any remaining ULTRA processes and offer to force-kill them
check_remaining_processes() {
    local remaining_pids=$(pgrep -f "ultra_" 2>/dev/null || true)
    if [ -n "$remaining_pids" ]; then
        local count=$(echo "$remaining_pids" | wc -l)
        log "WARN" "Found ${count} remaining ULTRA processes"
        
        if [ "$VERBOSE" = true ]; then
            log "INFO" "Remaining processes: $(ps -o pid,cmd -p $remaining_pids | grep -v PID)"
        fi
        
        if [ "$FORCE" = true ]; then
            log "WARN" "Force-killing remaining ULTRA processes..."
            kill -9 $remaining_pids 2>/dev/null || true
            log "INFO" "Forced shutdown of remaining processes complete"
        else
            log "WARN" "Use --force to terminate all remaining ULTRA processes"
        fi
    fi
    
    # Clean up any leftover PID files
    find "${ULTRA_PID_DIR}" -name "*.pid" -print0 | while IFS= read -r -d '' pid_file; do
        local component=$(basename "$pid_file" .pid)
        local pid=$(cat "$pid_file")
        if ! is_process_running "$pid"; then
            log "INFO" "Removing stale PID file for ${component}"
            rm -f "$pid_file"
        fi
    done
}

# Cleanup resources
cleanup_resources() {
    # Clean temporary files if needed
    if [ -d "${ULTRA_ROOT}/tmp" ]; then
        log "INFO" "Cleaning up temporary files..."
        find "${ULTRA_ROOT}/tmp" -type f -name "ultra_*" -mmin +60 -delete 2>/dev/null || true
    fi
    
    # Release shared memory segments if needed
    ipcs -m | grep "0x$(id -u)" | awk '{print $2}' | xargs -r -n1 ipcrm -m 2>/dev/null || true
    
    # Close any open file descriptors (handles)
    for fd in /proc/$$/fd/*; do
        fd_num=$(basename "$fd")
        if [ "$fd_num" -gt 2 ]; then
            eval "exec $fd_num>&-" 2>/dev/null || true
        fi
    done
}

# Main execution
main() {
    # Log start of shutdown
    log "INFO" "ULTRA System Shutdown Script (Version 1.0)"
    
    # Check if we're running as root (some components might need this)
    if [ "$(id -u)" -eq 0 ]; then
        log "WARN" "Running as root user - this is not recommended for security reasons"
    fi
    
    # Check if ULTRA is actually running before attempting shutdown
    local any_running=false
    for component in ultra_api_server ultra_web_ui ultra_knowledge_db core_neural_architecture; do
        local pid_file="${ULTRA_PID_DIR}/${component}.pid"
        if [ -f "$pid_file" ]; then
            local pid=$(cat "$pid_file")
            if is_process_running "$pid"; then
                any_running=true
                break
            fi
        fi
    done
    
    if [ "$any_running" = false ]; then
        # Quick check for any ultra processes
        if pgrep -f "ultra_" >/dev/null 2>&1; then
            log "WARN" "Some ULTRA processes are running but without proper PID files"
            any_running=true
        else
            log "INFO" "No ULTRA components appear to be running"
            cleanup_resources
            return 0
        fi
    fi
    
    # Shut down all components if any are running
    if [ "$any_running" = true ]; then
        shutdown_ultra
    fi
    
    # Clean up resources
    cleanup_resources
    
    # Final status
    if [ -z "$(pgrep -f "ultra_" 2>/dev/null || true)" ]; then
        log "INFO" "All ULTRA processes have been successfully terminated"
        return 0
    else
        log "WARN" "Some ULTRA processes could not be terminated. Use --force option or manual intervention."
        return 1
    fi
}

# Run the main function
main "$@"
exit $?