#!/bin/bash
# ULTRA System Startup Script
# This script initializes and launches the ULTRA system components
# following the architecture specified in the documentation.

# =====================================================================
# Configuration and Environment Setup
# =====================================================================

# Store script start time for benchmarking
start_time=$(date +%s)

# Set script to exit on error
set -e

# Script directory and project root
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"

# Load configuration files
if [ -f "$PROJECT_ROOT/config/default_config.yaml" ]; then
    CONFIG_FILE="$PROJECT_ROOT/config/default_config.yaml"
else
    echo "Error: Default configuration file not found."
    exit 1
fi

# Source environment configuration if exists
if [ -f "$PROJECT_ROOT/.env" ]; then
    source "$PROJECT_ROOT/.env"
fi

# =====================================================================
# Logger Setup
# =====================================================================

LOG_DIR="$PROJECT_ROOT/logs"
mkdir -p "$LOG_DIR"
LOG_FILE="$LOG_DIR/ultra_$(date +%Y%m%d_%H%M%S).log"

# Logger function with timestamps and log levels
log() {
    local level="$1"
    local message="$2"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] [$level] $message" | tee -a "$LOG_FILE"
}

log_info() {
    log "INFO" "$1"
}

log_warn() {
    log "WARNING" "$1"
}

log_error() {
    log "ERROR" "$1"
}

log_success() {
    log "SUCCESS" "$1"
}

# =====================================================================
# Dependency Checking
# =====================================================================

check_dependencies() {
    log_info "Checking system dependencies..."
    
    # Check Python version
    if ! command -v python3 &> /dev/null; then
        log_error "Python 3 not found. Please install Python 3.8 or higher."
        exit 1
    fi
    
    PY_VERSION=$(python3 -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
    if [ "$(echo "$PY_VERSION < 3.8" | bc)" -eq 1 ]; then
        log_error "Python 3.8 or higher is required. Found version $PY_VERSION"
        exit 1
    fi
    
    # Check for required libraries
    if [ -f "$PROJECT_ROOT/requirements.txt" ]; then
        python3 -c "
import pkg_resources
import sys
required = [l.strip() for l in open('$PROJECT_ROOT/requirements.txt') if l.strip() and not l.startswith('#')]
installed = {pkg.key for pkg in pkg_resources.working_set}
missing = [r for r in required if r.lower().split('==')[0] not in installed]
if missing:
    print('Missing required packages: ' + ', '.join(missing))
    sys.exit(1)
" || {
            log_warn "Some required packages are missing. Installing..."
            pip install -r "$PROJECT_ROOT/requirements.txt" || {
                log_error "Failed to install required packages."
                exit 1
            }
        }
    fi
    
    # Check for GPU support if needed
    if [ "${ULTRA_USE_GPU:-false}" = "true" ]; then
        if ! python3 -c "import torch; print(torch.cuda.is_available())" | grep -q "True"; then
            log_warn "GPU support requested but CUDA is not available. Falling back to CPU."
            export ULTRA_USE_GPU=false
        else
            log_info "GPU support detected and enabled."
        fi
    fi
    
    # Check for specialized hardware if specified
    if [ "${ULTRA_USE_NEUROMORPHIC:-false}" = "true" ]; then
        # Add code to check for Loihi or other neuromorphic hardware
        if ! command -v lava-nc &> /dev/null; then
            log_warn "Neuromorphic computing libraries not found. Some features may be disabled."
            export ULTRA_USE_NEUROMORPHIC=false
        fi
    fi
    
    log_success "Dependency check completed."
}

# =====================================================================
# System Initialization
# =====================================================================

initialize_system() {
    log_info "Initializing ULTRA system components..."
    
    # Create necessary directories
    mkdir -p "$PROJECT_ROOT/data/knowledge_bases"
    mkdir -p "$PROJECT_ROOT/data/embeddings"
    mkdir -p "$PROJECT_ROOT/data/user_data"
    
    # Initialize database if needed
    if [ "${ULTRA_USE_DB:-false}" = "true" ]; then
        log_info "Setting up database connections..."
        python3 "$PROJECT_ROOT/ultra/utils/db_init.py" || {
            log_error "Database initialization failed."
            exit 1
        }
    fi
    
    # Load pretrained models if not already present
    if [ ! -d "$PROJECT_ROOT/data/pretrained" ] || [ -z "$(ls -A "$PROJECT_ROOT/data/pretrained")" ]; then
        log_info "Downloading pretrained models..."
        "$SCRIPT_DIR/download_pretrained.sh" || {
            log_error "Failed to download pretrained models."
            exit 1
        }
    fi
    
    # Initialize system state
    log_info "Initializing system state..."
    python3 -c "
import sys
sys.path.append('$PROJECT_ROOT')
from ultra.main import initialize_system
initialize_system('$CONFIG_FILE')
" || {
        log_error "System state initialization failed."
        exit 1
    }
    
    log_success "System initialization completed."
}

# =====================================================================
# Component Startup Functions
# =====================================================================

start_core_neural_architecture() {
    log_info "Starting Core Neural Architecture components..."
    
    # Start the neuromorphic core
    python3 -c "
import sys
sys.path.append('$PROJECT_ROOT')
from ultra.core_neural import neuromorphic_core
core = neuromorphic_core.NeuromorphicCore()
core.initialize()
" &
    CORE_PID=$!
    log_info "Neuromorphic Core started with PID: $CORE_PID"
    
    # Start the neuroplasticity engine
    python3 -c "
import sys
sys.path.append('$PROJECT_ROOT')
from ultra.core_neural import neuroplasticity_engine
engine = neuroplasticity_engine.NeuroplasticityEngine()
engine.start()
" &
    PLASTICITY_PID=$!
    log_info "Neuroplasticity Engine started with PID: $PLASTICITY_PID"
    
    # Start neuromodulation system
    python3 -c "
import sys
sys.path.append('$PROJECT_ROOT')
from ultra.core_neural import neuromodulation
system = neuromodulation.NeuromodulationSystem()
system.start()
" &
    NEUROMOD_PID=$!
    log_info "Neuromodulation System started with PID: $NEUROMOD_PID"
    
    # Write PIDs to file for later reference
    echo "$CORE_PID $PLASTICITY_PID $NEUROMOD_PID" > "$PROJECT_ROOT/.core_neural_pids"
}

start_hyper_transformer() {
    log_info "Starting Hyper-Dimensional Transformer components..."
    
    # Start dynamic attention system
    python3 -c "
import sys
sys.path.append('$PROJECT_ROOT')
from ultra.hyper_transformer import dynamic_attention
attention = dynamic_attention.SelfEvolvingDynamicAttention()
attention.start()
" &
    ATTENTION_PID=$!
    log_info "Dynamic Attention started with PID: $ATTENTION_PID"
    
    # Start recursive transformer
    python3 -c "
import sys
sys.path.append('$PROJECT_ROOT')
from ultra.hyper_transformer import recursive_transformer
transformer = recursive_transformer.RecursiveTransformer()
transformer.start()
" &
    RECURSIVE_PID=$!
    log_info "Recursive Transformer started with PID: $RECURSIVE_PID"
    
    # Start temporal-causal transformer
    python3 -c "
import sys
sys.path.append('$PROJECT_ROOT')
from ultra.hyper_transformer import temporal_causal
transformer = temporal_causal.TemporalCausalTransformer()
transformer.start()
" &
    TEMPORAL_PID=$!
    log_info "Temporal-Causal Transformer started with PID: $TEMPORAL_PID"
    
    # Write PIDs to file for later reference
    echo "$ATTENTION_PID $RECURSIVE_PID $TEMPORAL_PID" > "$PROJECT_ROOT/.transformer_pids"
}

start_diffusion_reasoning() {
    log_info "Starting Diffusion-Based Reasoning components..."
    
    # Start conceptual diffusion process
    python3 -c "
import sys
sys.path.append('$PROJECT_ROOT')
from ultra.diffusion_reasoning import conceptual_diffusion
diffusion = conceptual_diffusion.ConceptualDiffusion()
diffusion.start()
" &
    DIFFUSION_PID=$!
    log_info "Conceptual Diffusion started with PID: $DIFFUSION_PID"
    
    # Start thought latent space
    python3 -c "
import sys
sys.path.append('$PROJECT_ROOT')
from ultra.diffusion_reasoning import thought_latent_space
latent = thought_latent_space.ThoughtLatentSpace()
latent.initialize()
" &
    LATENT_PID=$!
    log_info "Thought Latent Space started with PID: $LATENT_PID"
    
    # Start Bayesian uncertainty quantification
    python3 -c "
import sys
sys.path.append('$PROJECT_ROOT')
from ultra.diffusion_reasoning import bayesian_uncertainty
uncertainty = bayesian_uncertainty.BayesianUncertaintyQuantification()
uncertainty.start()
" &
    UNCERTAINTY_PID=$!
    log_info "Bayesian Uncertainty Quantification started with PID: $UNCERTAINTY_PID"
    
    # Write PIDs to file for later reference
    echo "$DIFFUSION_PID $LATENT_PID $UNCERTAINTY_PID" > "$PROJECT_ROOT/.diffusion_pids"
}

start_meta_cognitive_system() {
    log_info "Starting Meta-Cognitive System components..."
    
    # Start chain of thought reasoning
    python3 -c "
import sys
sys.path.append('$PROJECT_ROOT')
from ultra.meta_cognitive import chain_of_thought
cot = chain_of_thought.MultiPathChainOfThought()
cot.start()
" &
    COT_PID=$!
    log_info "Multi-Path Chain of Thought started with PID: $COT_PID"
    
    # Start tree of thought
    python3 -c "
import sys
sys.path.append('$PROJECT_ROOT')
from ultra.meta_cognitive import tree_of_thought
tot = tree_of_thought.TreeOfThoughtExploration()
tot.start()
" &
    TOT_PID=$!
    log_info "Tree of Thought Exploration started with PID: $TOT_PID"
    
    # Start self-critique loop
    python3 -c "
import sys
sys.path.append('$PROJECT_ROOT')
from ultra.meta_cognitive import self_critique
critique = self_critique.SelfCritiqueLoop()
critique.start()
" &
    CRITIQUE_PID=$!
    log_info "Self-Critique Loop started with PID: $CRITIQUE_PID"
    
    # Write PIDs to file for later reference
    echo "$COT_PID $TOT_PID $CRITIQUE_PID" > "$PROJECT_ROOT/.metacog_pids"
}

start_neuromorphic_processing() {
    log_info "Starting Neuromorphic Processing Layer components..."
    
    # Start spiking neural networks
    python3 -c "
import sys
sys.path.append('$PROJECT_ROOT')
from ultra.neuromorphic_processing import spiking_networks
snn = spiking_networks.SpikingNeuralNetwork()
snn.start()
" &
    SNN_PID=$!
    log_info "Spiking Neural Networks started with PID: $SNN_PID"
    
    # Start event-based computing
    python3 -c "
import sys
sys.path.append('$PROJECT_ROOT')
from ultra.neuromorphic_processing import event_based_computing
ebc = event_based_computing.EventBasedProcessor()
ebc.start()
" &
    EBC_PID=$!
    log_info "Event-Based Computing started with PID: $EBC_PID"
    
    # Start reservoir computing
    python3 -c "
import sys
sys.path.append('$PROJECT_ROOT')
from ultra.neuromorphic_processing import reservoir_computing
rc = reservoir_computing.ReservoirComputing()
rc.start()
" &
    RC_PID=$!
    log_info "Reservoir Computing started with PID: $RC_PID"
    
    # Write PIDs to file for later reference
    echo "$SNN_PID $EBC_PID $RC_PID" > "$PROJECT_ROOT/.neuromorphic_pids"
}

start_emergent_consciousness() {
    log_info "Starting Emergent Consciousness Lattice components..."
    
    # Start self-awareness module
    python3 -c "
import sys
sys.path.append('$PROJECT_ROOT')
from ultra.emergent_consciousness import self_awareness
awareness = self_awareness.SelfAwarenessModule()
awareness.start()
" &
    AWARENESS_PID=$!
    log_info "Self-Awareness Module started with PID: $AWARENESS_PID"
    
    # Start intentionality system
    python3 -c "
import sys
sys.path.append('$PROJECT_ROOT')
from ultra.emergent_consciousness import intentionality
intention = intentionality.IntentionalitySystem()
intention.start()
" &
    INTENTION_PID=$!
    log_info "Intentionality System started with PID: $INTENTION_PID"
    
    # Start global workspace
    python3 -c "
import sys
sys.path.append('$PROJECT_ROOT')
from ultra.emergent_consciousness import global_workspace
workspace = global_workspace.GlobalWorkspace()
workspace.start()
" &
    WORKSPACE_PID=$!
    log_info "Global Workspace started with PID: $WORKSPACE_PID"
    
    # Write PIDs to file for later reference
    echo "$AWARENESS_PID $INTENTION_PID $WORKSPACE_PID" > "$PROJECT_ROOT/.consciousness_pids"
}

start_neuro_symbolic() {
    log_info "Starting Neuro-Symbolic Integration components..."
    
    # Start logical reasoning engine
    python3 -c "
import sys
sys.path.append('$PROJECT_ROOT')
from ultra.neuro_symbolic import logical_reasoning
engine = logical_reasoning.LogicalReasoningEngine()
engine.start()
" &
    LOGIC_PID=$!
    log_info "Logical Reasoning Engine started with PID: $LOGIC_PID"
    
    # Start neuro-symbolic bridge
    python3 -c "
import sys
sys.path.append('$PROJECT_ROOT')
from ultra.neuro_symbolic import neuro_symbolic_bridge
bridge = neuro_symbolic_bridge.NeuroSymbolicBridge()
bridge.start()
" &
    BRIDGE_PID=$!
    log_info "Neuro-Symbolic Bridge started with PID: $BRIDGE_PID"
    
    # Start program synthesis
    python3 -c "
import sys
sys.path.append('$PROJECT_ROOT')
from ultra.neuro_symbolic import program_synthesis
synthesis = program_synthesis.ProgramSynthesis()
synthesis.start()
" &
    SYNTHESIS_PID=$!
    log_info "Program Synthesis started with PID: $SYNTHESIS_PID"
    
    # Write PIDs to file for later reference
    echo "$LOGIC_PID $BRIDGE_PID $SYNTHESIS_PID" > "$PROJECT_ROOT/.neurosymbolic_pids"
}

start_self_evolution() {
    log_info "Starting Self-Evolution System components..."
    
    # Start neural architecture search
    python3 -c "
import sys
sys.path.append('$PROJECT_ROOT')
from ultra.self_evolution import architecture_search
nas = architecture_search.NeuralArchitectureSearch()
nas.start()
" &
    NAS_PID=$!
    log_info "Neural Architecture Search started with PID: $NAS_PID"
    
    # Start self-modification protocols
    python3 -c "
import sys
sys.path.append('$PROJECT_ROOT')
from ultra.self_evolution import self_modification
modification = self_modification.SelfModificationProtocols()
modification.start()
" &
    MODIFY_PID=$!
    log_info "Self-Modification Protocols started with PID: $MODIFY_PID"
    
    # Start computational reflection
    python3 -c "
import sys
sys.path.append('$PROJECT_ROOT')
from ultra.self_evolution import computational_reflection
reflection = computational_reflection.ComputationalReflection()
reflection.start()
" &
    REFLECTION_PID=$!
    log_info "Computational Reflection started with PID: $REFLECTION_PID"
    
    # Write PIDs to file for later reference
    echo "$NAS_PID $MODIFY_PID $REFLECTION_PID" > "$PROJECT_ROOT/.evolution_pids"
}

# =====================================================================
# API and Interface Components
# =====================================================================

start_api_server() {
    log_info "Starting API and interface components..."
    
    # Start the API server
    python3 -c "
import sys
sys.path.append('$PROJECT_ROOT')
from ultra.main import start_api_server
start_api_server()
" &
    API_PID=$!
    log_info "API Server started with PID: $API_PID"
    
    # Write PID to file for later reference
    echo "$API_PID" > "$PROJECT_ROOT/.api_pid"
    
    # Wait for API to be ready
    local max_attempts=30
    local attempt=0
    local ready=false
    
    log_info "Waiting for API server to become ready..."
    while [ $attempt -lt $max_attempts ] && [ "$ready" = false ]; do
        if curl -s "http://localhost:${ULTRA_API_PORT:-8000}/health" | grep -q "ok"; then
            ready=true
        else
            attempt=$((attempt + 1))
            sleep 1
        fi
    done
    
    if [ "$ready" = true ]; then
        log_success "API Server is ready and responsive."
    else
        log_warn "API Server did not become ready in the expected time. It may still be starting up."
    fi
}

# =====================================================================
# System Monitoring and Management
# =====================================================================

start_monitoring() {
    log_info "Starting system monitoring..."
    
    # Start the monitoring service
    python3 -c "
import sys
sys.path.append('$PROJECT_ROOT')
from ultra.utils import monitoring
monitor = monitoring.SystemMonitor()
monitor.start()
" &
    MONITOR_PID=$!
    log_info "System Monitoring started with PID: $MONITOR_PID"
    
    # Write PID to file for later reference
    echo "$MONITOR_PID" > "$PROJECT_ROOT/.monitor_pid"
}

create_pid_file() {
    # Create a PID file for the whole system
    echo "$$" > "$PROJECT_ROOT/.ultra_pid"
    
    # Register a trap to remove the PID file on exit
    trap "rm -f $PROJECT_ROOT/.ultra_pid" EXIT
}

# =====================================================================
# Main Execution Flow
# =====================================================================

main() {
    log_info "Starting ULTRA system..."
    
    # Create PID file
    create_pid_file
    
    # Check dependencies
    check_dependencies
    
    # Initialize system
    initialize_system
    
    # Start subsystems in the appropriate order
    start_core_neural_architecture
    start_neuromorphic_processing
    start_hyper_transformer
    start_diffusion_reasoning
    start_neuro_symbolic
    start_meta_cognitive_system
    start_emergent_consciousness
    start_self_evolution
    
    # Start API and interface components
    start_api_server
    
    # Start monitoring
    start_monitoring
    
    # Calculate and display startup time
    end_time=$(date +%s)
    startup_duration=$((end_time - start_time))
    log_success "ULTRA system startup completed in $startup_duration seconds."
    
    # Display system access information
    log_info "ULTRA API is available at: http://localhost:${ULTRA_API_PORT:-8000}"
    if [ "${ULTRA_WEB_UI:-true}" = "true" ]; then
        log_info "ULTRA Web UI is available at: http://localhost:${ULTRA_WEB_PORT:-8080}"
    fi
    
    log_info "System is now running. Use './stop.sh' to shut down the system."
    
    # Keep the script running to properly manage child processes
    wait
}

main "$@"