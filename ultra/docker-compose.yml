# ultra/ultra/docker-compose.yml
version: '3.8'

services:
  # Core Neural Architecture services
  neuromorphic-core:
    build:
      context: ./core_neural
      dockerfile: Dockerfile.neuromorphic
    image: ultra/neuromorphic-core:latest
    volumes:
      - core_neural_data:/app/data
      - ./config:/app/config
    environment:
      - NEURON_TYPES=4
      - DIMENSIONS=100,100,100
      - SYNAPSE_PRUNING_RATE=0.01
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    networks:
      - ultra_network
    healthcheck:
      test: ["CMD", "python", "-c", "import os; os.system('echo Core status: healthy')"]
      interval: 30s
      timeout: 10s
      retries: 3

  hyper-transformer:
    build:
      context: ./hyper_transformer
      dockerfile: Dockerfile.transformer
    image: ultra/hyper-transformer:latest
    volumes:
      - transformer_data:/app/data
      - ./config:/app/config
    environment:
      - MODEL_DIM=1024
      - NUM_HEADS=16
      - RECURSIVE_DEPTH=5
      - NUM_SCALES=3
    depends_on:
      neuromorphic-core:
        condition: service_healthy
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    networks:
      - ultra_network
    healthcheck:
      test: ["CMD", "python", "-c", "import os; os.system('echo Transformer status: healthy')"]
      interval: 30s
      timeout: 10s
      retries: 3

  diffusion-reasoning:
    build:
      context: ./diffusion_reasoning
      dockerfile: Dockerfile.diffusion
    image: ultra/diffusion-reasoning:latest
    volumes:
      - diffusion_data:/app/data
      - ./config:/app/config
    environment:
      - THOUGHT_DIMENSION=1024
      - DIFFUSION_STEPS=1000
      - LATENT_SPACE_CLUSTERS=100
    depends_on:
      hyper-transformer:
        condition: service_healthy
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    networks:
      - ultra_network
    healthcheck:
      test: ["CMD", "python", "-c", "import os; os.system('echo Diffusion status: healthy')"]
      interval: 30s
      timeout: 10s
      retries: 3

  meta-cognitive:
    build:
      context: ./meta_cognitive
      dockerfile: Dockerfile.metacog
    image: ultra/meta-cognitive:latest
    volumes:
      - metacog_data:/app/data
      - ./config:/app/config
    environment:
      - MAX_REASONING_PATHS=5
      - TREE_MAX_DEPTH=10
      - SELF_CRITIQUE_ITERATIONS=3
    depends_on:
      diffusion-reasoning:
        condition: service_healthy
    networks:
      - ultra_network
    healthcheck:
      test: ["CMD", "python", "-c", "import os; os.system('echo Meta-Cognitive status: healthy')"]
      interval: 30s
      timeout: 10s
      retries: 3

  neuromorphic-processing:
    build:
      context: ./neuromorphic_processing
      dockerfile: Dockerfile.processing
    image: ultra/neuromorphic-processing:latest
    volumes:
      - processing_data:/app/data
      - ./config:/app/config
    environment:
      - SNN_TYPE=LIF
      - EVENT_THRESHOLD=0.1
      - RESERVOIR_SIZE=1000
    depends_on:
      neuromorphic-core:
        condition: service_healthy
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    networks:
      - ultra_network
    healthcheck:
      test: ["CMD", "python", "-c", "import os; os.system('echo Processing status: healthy')"]
      interval: 30s
      timeout: 10s
      retries: 3

  emergent-consciousness:
    build:
      context: ./emergent_consciousness
      dockerfile: Dockerfile.consciousness
    image: ultra/emergent-consciousness:latest
    volumes:
      - consciousness_data:/app/data
      - ./config:/app/config
    environment:
      - SELF_MODEL_CAPACITY=10000
      - GLOBAL_WORKSPACE_SIZE=512
      - ATTENTION_MODES=5
    depends_on:
      meta-cognitive:
        condition: service_healthy
      neuromorphic-processing:
        condition: service_healthy
    networks:
      - ultra_network
    healthcheck:
      test: ["CMD", "python", "-c", "import os; os.system('echo Consciousness status: healthy')"]
      interval: 30s
      timeout: 10s
      retries: 3

  neuro-symbolic:
    build:
      context: ./neuro_symbolic
      dockerfile: Dockerfile.neurosym
    image: ultra/neuro-symbolic:latest
    volumes:
      - neurosym_data:/app/data
      - ./config:/app/config
    environment:
      - KB_SIZE=1000000
      - SYMBOLIC_DIM=512
      - BRIDGE_CAPACITY=1024
    depends_on:
      hyper-transformer:
        condition: service_healthy
      meta-cognitive:
        condition: service_healthy
    networks:
      - ultra_network
    healthcheck:
      test: ["CMD", "python", "-c", "import os; os.system('echo Neuro-Symbolic status: healthy')"]
      interval: 30s
      timeout: 10s
      retries: 3

  self-evolution:
    build:
      context: ./self_evolution
      dockerfile: Dockerfile.evolution
    image: ultra/self-evolution:latest
    volumes:
      - evolution_data:/app/data
      - ./config:/app/config
    environment:
      - NAS_POPULATION_SIZE=100
      - EVOLUTION_GENERATIONS=50
      - MODIFICATION_SAFETY_THRESHOLD=0.95
    depends_on:
      emergent-consciousness:
        condition: service_healthy
      neuro-symbolic:
        condition: service_healthy
    networks:
      - ultra_network
    healthcheck:
      test: ["CMD", "python", "-c", "import os; os.system('echo Self-Evolution status: healthy')"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Input/Output Processing services
  input-processing:
    build:
      context: ./input_processing
      dockerfile: Dockerfile.input
    image: ultra/input-processing:latest
    volumes:
      - input_data:/app/data
      - ./config:/app/config
    environment:
      - TEXT_ENCODERS=bert,gpt2,t5
      - IMAGE_ENCODERS=resnet,vit,clip
      - AUDIO_ENCODERS=wav2vec,whisper
    ports:
      - "8000:8000"
    networks:
      - ultra_network
    healthcheck:
      test: ["CMD", "python", "-c", "import os; os.system('echo Input Processing status: healthy')"]
      interval: 30s
      timeout: 10s
      retries: 3

  output-generation:
    build:
      context: ./output_generation
      dockerfile: Dockerfile.output
    image: ultra/output-generation:latest
    volumes:
      - output_data:/app/data
      - ./config:/app/config
    environment:
      - TEXT_GENERATORS=t5,gpt2
      - IMAGE_GENERATORS=stablediffusion,dalle
      - MULTIMODAL_SYNTHESIS=true
    depends_on:
      meta-cognitive:
        condition: service_healthy
      neuro-symbolic:
        condition: service_healthy
    ports:
      - "8001:8001"
    networks:
      - ultra_network
    healthcheck:
      test: ["CMD", "python", "-c", "import os; os.system('echo Output Generation status: healthy')"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Monitoring and Management services
  system-monitoring:
    build:
      context: ./utils
      dockerfile: Dockerfile.monitoring
    image: ultra/system-monitoring:latest
    volumes:
      - monitoring_data:/app/data
      - ./config:/app/config
    ports:
      - "8080:8080"
    depends_on:
      - neuromorphic-core
      - hyper-transformer
      - diffusion-reasoning
      - meta-cognitive
      - neuromorphic-processing
      - emergent-consciousness
      - neuro-symbolic
      - self-evolution
      - input-processing
      - output-generation
    networks:
      - ultra_network

  # API Gateway
  api-gateway:
    build:
      context: ./api
      dockerfile: Dockerfile.gateway
    image: ultra/api-gateway:latest
    volumes:
      - api_data:/app/data
      - ./config:/app/config
    ports:
      - "9000:9000"
    depends_on:
      input-processing:
        condition: service_healthy
      output-generation:
        condition: service_healthy
    networks:
      - ultra_network
    healthcheck:
      test: ["CMD", "curl -f http://localhost:9000/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Web UI for interaction
  web-interface:
    build:
      context: ./web
      dockerfile: Dockerfile.web
    image: ultra/web-interface:latest
    volumes:
      - web_data:/app/data
      - ./config:/app/config
    ports:
      - "80:80"
    depends_on:
      api-gateway:
        condition: service_healthy
    networks:
      - ultra_network

  # Knowledge Management
  knowledge-management:
    build:
      context: ./knowledge_management
      dockerfile: Dockerfile.knowledge
    image: ultra/knowledge-management:latest
    volumes:
      - knowledge_data:/app/data
      - ./config:/app/config
    environment:
      - EPISODIC_CAPACITY=1000000
      - SEMANTIC_DIM=1024
      - PROCEDURAL_INTEGRATION=true
    depends_on:
      neuromorphic-core:
        condition: service_healthy
      hyper-transformer:
        condition: service_healthy
    networks:
      - ultra_network
    healthcheck:
      test: ["CMD", "python", "-c", "import os; os.system('echo Knowledge Management status: healthy')"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Safety Monitor
  safety-monitor:
    build:
      context: ./safety
      dockerfile: Dockerfile.safety
    image: ultra/safety-monitor:latest
    volumes:
      - safety_data:/app/data
      - ./config:/app/config
    environment:
      - SAFETY_CHECKS=ethical,legal,harmful,bias
      - CONSTRAINT_ENFORCEMENT=strict
      - MONITORING_INTERVAL=5
    depends_on:
      - api-gateway
    networks:
      - ultra_network
    healthcheck:
      test: ["CMD", "python", "-c", "import os; os.system('echo Safety Monitor status: healthy')"]
      interval: 30s
      timeout: 10s
      retries: 3

# Persistent volumes
volumes:
  core_neural_data:
  transformer_data:
  diffusion_data:
  metacog_data:
  processing_data:
  consciousness_data:
  neurosym_data:
  evolution_data:
  input_data:
  output_data:
  monitoring_data:
  api_data:
  web_data:
  knowledge_data:
  safety_data:

# Networks
networks:
  ultra_network:
    driver: bridge