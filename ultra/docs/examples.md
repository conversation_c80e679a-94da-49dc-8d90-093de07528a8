# ULTRA Module Examples

This document provides examples and code snippets that demonstrate how to use the core modules of the ULTRA (Ultimate Learning & Thought Reasoning Architecture) system. Each example focuses on a specific component and illustrates its functionality, interface, and integration with other system parts.

## Core Neural Architecture

### Neuroplasticity Engine Example

This example demonstrates how to use the neuroplasticity engine to implement Spike-Timing-Dependent Plasticity (STDP) in a neuromorphic network:

```python
import numpy as np
from ultra.core_neural import neuromorphic_core, neuroplasticity_engine
from ultra.utils.config import load_config

# Initialize the neuroplasticity engine with STDP parameters
config = load_config("config/neuroplasticity_params.yaml")
plasticity = neuroplasticity_engine.NeuroplasticityEngine(
    eta=config.eta,          # Learning rate
    alpha=config.alpha,      # Scaling factor
    tau_plus=config.tau_plus,    # Time constant for pre-post spiking
    tau_minus=config.tau_minus   # Time constant for post-pre spiking
)

# Initialize a simple neuromorphic network
core = neuromorphic_core.NeuromorphicCore(
    dimensions=(10, 10, 10),  # 10x10x10 = 1000 neurons
    neuron_types=4            # Four neuron types
)

# Example: Apply STDP based on spike timing
pre_neuron_id = 42
post_neuron_id = 137
spike_time_pre = 10.0  # ms
spike_time_post = 10.5  # ms
delta_t = spike_time_post - spike_time_pre

# Compute weight update based on STDP rule
old_weight = core.connections[post_neuron_id, pre_neuron_id]
weight_change = plasticity.compute_stdp_update(delta_t)
new_weight = old_weight + weight_change

print(f"Old weight: {old_weight}")
print(f"Delta t: {delta_t} ms")
print(f"Weight change: {weight_change}")
print(f"New weight: {new_weight}")

# Apply the update to the core
core.update_connection(post_neuron_id, pre_neuron_id, new_weight)

# Demonstrate homeostatic plasticity
for i in range(1000):  # Sample 1000 neurons
    current_activity = core.recent_activity[i]
    target_activity = plasticity.target_activity
    
    # Homeostatic adjustment of firing threshold
    threshold_change = plasticity.update_homeostatic_threshold(
        current_activity, target_activity
    )
    core.thresholds[i] += threshold_change
```

### Synaptic Pruning Example

This example shows how to implement synaptic pruning based on connection importance:

```python
import numpy as np
from ultra.core_neural import synaptic_pruning
from ultra.utils.visualization import plot_connectivity

# Initialize the synaptic pruning module
pruning = synaptic_pruning.SynapticPruningModule(
    theta_prune=0.1,     # Threshold for weight pruning
    theta_usage=0.1,     # Threshold for usage-based pruning
    pruning_rate=0.01    # Maximum pruning rate
)

# Create a sample network with random weights and usage statistics
num_neurons = 100
connections = np.random.rand(num_neurons, num_neurons) * 0.5
usage = np.random.rand(num_neurons, num_neurons)

# Visualize connectivity before pruning
plot_connectivity(connections, "before_pruning.png")

# Calculate importance scores for connections
importance_scores = pruning.calculate_importance(
    connections, usage, alpha=0.7, beta=0.3
)

# Apply pruning
mask = pruning.get_pruning_mask(importance_scores)
pruned_connections = connections.copy()
pruned_connections[mask] = 0.0

# Visualize connectivity after pruning
plot_connectivity(pruned_connections, "after_pruning.png")

# Print statistics
total_connections = np.count_nonzero(connections)
remaining_connections = np.count_nonzero(pruned_connections)
pruned_percent = 100 * (total_connections - remaining_connections) / total_connections

print(f"Total connections before pruning: {total_connections}")
print(f"Connections after pruning: {remaining_connections}")
print(f"Pruned {pruned_percent:.2f}% of connections")
```

### Neuromodulation System Example

This example demonstrates how the neuromodulation system modifies neural dynamics:

```python
import numpy as np
import matplotlib.pyplot as plt
from ultra.core_neural import neuromodulation_system
from ultra.core_neural import neuromorphic_core
from ultra.utils.simulation import run_simulation

# Initialize neuromodulation system
neuromod = neuromodulation_system.NeuromodulationSystem(
    p_baseline=1.0,   # Baseline parameter values
    delta_p=0.5,      # Maximum parameter modulation
    tau_m=100.0       # Time constant for modulator dynamics
)

# Create a simple neural network
core = neuromorphic_core.NeuromorphicCore(
    dimensions=(5, 5, 5),
    neuron_types=4
)

# Initialize modulators
dopamine = 0.0      # Reward signal
serotonin = 0.5     # Mood regulation
norepinephrine = 0.0  # Arousal
acetylcholine = 0.8   # Attention

# Run a simulation with changing neuromodulatory input
simulation_time = 1000  # ms
dt = 0.1  # ms
time_steps = int(simulation_time / dt)

# Arrays to store results
times = np.arange(0, simulation_time, dt)
dopamine_levels = np.zeros(time_steps)
network_activity = np.zeros(time_steps)

# Add a dopamine spike at t=300ms
dopamine_input = np.zeros(time_steps)
dopamine_input[int(300/dt):int(350/dt)] = 1.0

# Run simulation
for t in range(time_steps):
    # Update neuromodulator levels
    dopamine = neuromod.update_modulator(
        dopamine, dopamine_input[t], dt
    )
    dopamine_levels[t] = dopamine
    
    # Apply neuromodulation to neural dynamics
    learning_rate_mod = neuromod.modulate_parameter(
        'learning_rate', dopamine, serotonin, norepinephrine, acetylcholine
    )
    
    gain_mod = neuromod.modulate_parameter(
        'gain', dopamine, serotonin, norepinephrine, acetylcholine
    )
    
    # Apply to network
    core.apply_neuromodulation(
        learning_rate_mod=learning_rate_mod,
        gain_mod=gain_mod
    )
    
    # Run one step of neural simulation
    network_activity[t] = core.update(dt)

# Plot results
plt.figure(figsize=(10, 6))

plt.subplot(2, 1, 1)
plt.plot(times, dopamine_levels)
plt.title('Dopamine Level')
plt.ylabel('Concentration')

plt.subplot(2, 1, 2)
plt.plot(times, network_activity)
plt.title('Network Activity')
plt.xlabel('Time (ms)')
plt.ylabel('Activity')

plt.tight_layout()
plt.savefig('neuromodulation_example.png')
plt.show()
```

## Hyper-Dimensional Transformer

### Self-Evolving Dynamic Attention Example

This example shows how the self-evolving dynamic attention mechanism adapts based on context:

```python
import torch
import torch.nn as nn
import matplotlib.pyplot as plt
from ultra.hyper_transformer import dynamic_attention
from ultra.utils.visualization import plot_attention_maps

# Create a self-evolving dynamic attention module
attention = dynamic_attention.SelfEvolvingDynamicAttention(
    d_model=512,
    num_heads=8,
    dropout=0.1,
    eta_M=0.01  # Learning rate for mask evolution
)

# Example input sequence (batch_size=1, seq_len=10, d_model=512)
batch_size = 1
seq_len = 10
d_model = 512
query = torch.randn(batch_size, seq_len, d_model)
key = torch.randn(batch_size, seq_len, d_model)
value = torch.randn(batch_size, seq_len, d_model)

# Initial context embedding
context_embedding = torch.randn(batch_size, d_model)

# Get initial attention output
attention_output, attention_weights = attention(
    query, key, value, context=context_embedding
)

# Plot initial attention pattern
plot_attention_maps(attention_weights, "initial_attention.png")

# Simulate learning through several iterations
performance_metrics = []
attention_maps = []

# Learning loop
for i in range(5):
    # Generate a mock performance metric (higher is better)
    performance = torch.tensor([0.5 + i * 0.1])
    performance_metrics.append(performance.item())
    
    # Update the attention mechanism based on performance
    attention.update_mask(context_embedding, performance)
    
    # Compute attention with updated mask
    attention_output, attention_weights = attention(
        query, key, value, context=context_embedding
    )
    
    # Store attention map
    attention_maps.append(attention_weights.detach().cpu().numpy())

# Plot evolution of attention maps
plot_attention_maps(attention_maps, "evolving_attention.png")

# Plot performance improvement
plt.figure(figsize=(8, 4))
plt.plot(performance_metrics)
plt.title('Performance Improvement with Self-Evolving Attention')
plt.xlabel('Iteration')
plt.ylabel('Performance')
plt.grid(True)
plt.savefig('attention_performance.png')
plt.show()
```

### Recursive Transformer Example

This example demonstrates the recursive transformer's dynamic depth processing:

```python
import torch
import torch.nn as nn
import matplotlib.pyplot as plt
from ultra.hyper_transformer import recursive_transformer
from ultra.utils.visualization import visualize_computation_paths

# Create a recursive transformer layer
recursive_layer = recursive_transformer.RecursiveTransformerLayer(
    d_model=512,
    nhead=8,
    dim_feedforward=2048,
    dropout=0.1,
    max_recursion=5
)

# Input examples with different complexities
simple_input = torch.randn(1, 10, 512)  # Simple input (batch=1, seq_len=10, d_model=512)
complex_input = torch.randn(1, 10, 512)  # We'll mark this as complex

# Process the simple input
simple_output, simple_depths, simple_halt_probs = recursive_layer(
    simple_input, return_depth_info=True
)

# Process the complex input with a complexity signal
complex_output, complex_depths, complex_halt_probs = recursive_layer(
    complex_input, 
    complexity_signal=torch.ones(1, 10, 1) * 0.8,  # High complexity signal
    return_depth_info=True
)

# Print recursion depth statistics
print(f"Simple input average recursion depth: {simple_depths.mean().item():.2f}")
print(f"Complex input average recursion depth: {complex_depths.mean().item():.2f}")

# Visualize computation paths
visualize_computation_paths(
    simple_depths.view(-1).cpu().numpy(),
    complex_depths.view(-1).cpu().numpy(),
    "recursion_depths.png"
)

# Show halt probabilities
plt.figure(figsize=(10, 6))

plt.subplot(2, 1, 1)
plt.title("Simple Input Halting Probabilities")
for i in range(len(simple_halt_probs)):
    plt.plot(simple_halt_probs[i].view(-1).cpu().numpy(), label=f"Step {i+1}")
plt.legend()

plt.subplot(2, 1, 2)
plt.title("Complex Input Halting Probabilities")
for i in range(len(complex_halt_probs)):
    plt.plot(complex_halt_probs[i].view(-1).cpu().numpy(), label=f"Step {i+1}")
plt.legend()

plt.tight_layout()
plt.savefig("halt_probabilities.png")
plt.show()
```

## Diffusion-Based Reasoning

### Conceptual Diffusion Process Example

This example shows how conceptual diffusion processes work for reasoning:

```python
import torch
import numpy as np
import matplotlib.pyplot as plt
from ultra.diffusion_reasoning import conceptual_diffusion
from ultra.diffusion_reasoning import thought_latent_space
from ultra.utils.visualization import visualize_diffusion_process

# Initialize the conceptual diffusion process
diffusion = conceptual_diffusion.ConceptualDiffusion(
    concept_dim=128,
    num_timesteps=1000,
    beta_schedule='linear'
)

# Initialize the thought latent space
thought_space = thought_latent_space.ThoughtLatentSpace(
    dimension=128,
    num_clusters=50
)

# Encode a few example concepts
concepts = ["artificial intelligence", "neural network", "consciousness"]
concept_embeddings = torch.zeros(len(concepts), 128)

# In a real system, we would use a language model to get embeddings
# Here we'll just use random embeddings for demonstration
for i, concept in enumerate(concepts):
    concept_embeddings[i] = torch.randn(128)
    concept_embeddings[i] = concept_embeddings[i] / concept_embeddings[i].norm()

# Forward diffusion process (adding noise)
timesteps = [0, 100, 200, 500, 999]  # Sample timesteps to visualize
noisy_concepts = []

for t in timesteps:
    noisy_concept, _ = diffusion.forward_diffusion(concept_embeddings[0].unsqueeze(0), t)
    noisy_concepts.append(noisy_concept.squeeze().detach().cpu().numpy())

# Now run the reverse diffusion to denoise
denoised_concepts = [noisy_concepts[-1]]  # Start with the noisiest sample
current = torch.tensor(noisy_concepts[-1]).unsqueeze(0).float()

# Run reverse diffusion
for t in reversed(range(0, 1000, 200)):
    t_tensor = torch.tensor([t]).long()
    current = diffusion.reverse_diffusion_step(current, t_tensor)
    denoised_concepts.append(current.squeeze().detach().cpu().numpy())

# Visualize the diffusion process
visualize_diffusion_process(
    noisy_concepts, 
    denoised_concepts,
    "concept_diffusion.png"
)

# Example of conditioning the reverse diffusion on a goal
goal_concept = "quantum computing"
# In a real system, we would encode this properly
goal_embedding = torch.randn(1, 128)
goal_embedding = goal_embedding / goal_embedding.norm()

# Reverse diffusion with goal guidance
guided_concepts = [noisy_concepts[-1]]
current = torch.tensor(noisy_concepts[-1]).unsqueeze(0).float()

# Run guided reverse diffusion
for t in reversed(range(0, 1000, 200)):
    t_tensor = torch.tensor([t]).long()
    # Get gradient toward goal
    gamma_t = 0.5 * (1000 - t) / 1000  # Increase guidance as t decreases
    
    # In a real system, we would compute a proper gradient
    # Here we just use a simplified approach for demonstration
    goal_direction = goal_embedding - current
    
    # Apply guided reverse diffusion
    current = diffusion.reverse_diffusion_step(
        current, t_tensor, guidance=gamma_t * goal_direction
    )
    guided_concepts.append(current.squeeze().detach().cpu().numpy())

# Visualize the guided diffusion process
visualize_diffusion_process(
    noisy_concepts,
    guided_concepts,
    "guided_concept_diffusion.png",
    title="Reverse Diffusion Guided Toward 'Quantum Computing'"
)
```

### Bayesian Uncertainty Quantification Example

This example demonstrates how to quantify uncertainty in reasoning:

```python
import numpy as np
import torch
import matplotlib.pyplot as plt
from ultra.diffusion_reasoning import bayesian_uncertainty
from ultra.utils.visualization import plot_uncertainty

# Initialize the Bayesian Uncertainty Quantification module
buq = bayesian_uncertainty.BayesianUncertaintyQuantification(
    thought_space_dim=128
)

# Create sample belief distributions (mean and covariance)
# We'll use diagonal covariance matrices for simplicity
means = [
    torch.zeros(128),  # Completely uncertain
    torch.randn(128),  # Random belief
    torch.ones(128)    # Strong belief
]

covs = [
    torch.eye(128) * 10.0,  # High uncertainty
    torch.eye(128) * 1.0,   # Medium uncertainty
    torch.eye(128) * 0.1    # Low uncertainty
]

beliefs = [(mean, cov) for mean, cov in zip(means, covs)]

# Compute uncertainty for each belief
uncertainties = [buq.get_uncertainty(belief) for belief in beliefs]
print("Uncertainties:")
for i, u in enumerate(uncertainties):
    print(f"  Belief {i+1}: {u.item():.2f}")

# Generate samples from each belief
num_samples = 100
all_samples = []
for belief in beliefs:
    samples = buq.sample_from_belief(belief, n_samples=num_samples)
    all_samples.append(samples)

# Compute distances between samples as a measure of spread
spreads = []
for samples in all_samples:
    # Compute pairwise distances between all samples (using first 2D for visualization)
    samples_2d = samples[:, :2].numpy()
    dists = []
    for i in range(len(samples_2d)):
        for j in range(i+1, len(samples_2d)):
            dists.append(np.linalg.norm(samples_2d[i] - samples_2d[j]))
    spreads.append(np.mean(dists))

# Create a visual representation of uncertainty
plot_uncertainty(all_samples, uncertainties, spreads, "uncertainty_visualization.png")

# Example: Update a belief with new evidence
prior_belief = beliefs[1]  # Medium uncertainty belief
evidence_mean = torch.randn(128)
evidence_cov = torch.eye(128) * 0.5  # Moderately certain evidence
evidence = (evidence_mean, evidence_cov)

# Update belief using Bayes' rule
posterior_belief = buq.update_belief(prior_belief, evidence)
posterior_uncertainty = buq.get_uncertainty(posterior_belief)

print(f"Prior uncertainty: {uncertainties[1].item():.2f}")
print(f"Evidence uncertainty: {buq.get_uncertainty(evidence).item():.2f}")
print(f"Posterior uncertainty: {posterior_uncertainty.item():.2f}")

# Plot posterior samples
posterior_samples = buq.sample_from_belief(posterior_belief, n_samples=num_samples)
plot_uncertainty([all_samples[1], posterior_samples], 
                [uncertainties[1], posterior_uncertainty],
                [spreads[1], np.mean([np.linalg.norm(posterior_samples[i, :2] - posterior_samples[j, :2]) 
                                    for i in range(len(posterior_samples)) 
                                    for j in range(i+1, len(posterior_samples))])],
                "belief_update.png",
                titles=["Prior", "Posterior"])
```

## Meta-Cognitive System

### Multi-Path Chain of Thought Example

This example demonstrates the multi-path chain-of-thought reasoning:

```python
import numpy as np
import matplotlib.pyplot as plt
from ultra.meta_cognitive import chain_of_thought
from ultra.utils.visualization import visualize_reasoning_paths

# Initialize the Multi-Path Chain of Thought system
mcot = chain_of_thought.MultiPathChainOfThought(
    max_paths=5,
    beam_width=3
)

# Example problem
problem = "If a train travels at 60 mph, how long will it take to travel 240 miles?"

# Define a simplified mock language model for this example
class MockLanguageModel:
    def generate(self, text, num_return_sequences=1, max_new_tokens=50, temperature=1.0, stop_sequences=None):
        """Simplified mock generation that returns predefined continuations"""
        continuations = []
        
        if "train travels" in text and num_return_sequences >= 3:
            # Generate 3 different reasoning paths
            continuations.append(text + "\nI'll solve this step by step. The train travels at 60 mph. To find the time, I need to divide distance by speed. Time = Distance/Speed. Time = 240 miles / 60 mph = 4 hours.")
            continuations.append(text + "\nTo solve this, I need to use the formula: Time = Distance/Speed. We have: Distance = 240 miles, Speed = 60 mph. So Time = 240/60 = 4 hours.")
            continuations.append(text + "\nI'll approach this by converting units first. 60 mph means 60 miles per hour. The distance is 240 miles. Using Time = Distance/Speed, we get Time = 240/60 = 4 hours.")
        else:
            # Default continuation
            continuations.append(text + "\nThis is a continuation.")
        
        return continuations[:num_return_sequences]
    
    def compute_coherence(self, context, new_text):
        """Simple mock coherence scorer"""
        if "Time = Distance/Speed" in new_text:
            return 0.9
        elif "Distance/Speed" in new_text:
            return 0.8
        else:
            return 0.5
        
    def compute_logical_validity(self, text):
        """Simple mock validity scorer"""
        if "240" in text and "60" in text and "4" in text:
            return 0.95
        elif "240" in text and "60" in text:
            return 0.7
        else:
            return 0.4

# Set the language model
mcot.language_model = MockLanguageModel()

# Generate reasoning paths
reasoning_paths = mcot.generate_reasoning_paths(problem)

# Print the reasoning paths and their scores
print(f"Problem: {problem}\n")
print("Reasoning Paths:")
for i, path in enumerate(reasoning_paths):
    print(f"\nPath {i+1} (score: {path['score']:.2f}):")
    print(path["text"])

# Visualize the reasoning paths
visualize_reasoning_paths(
    [path["text"] for path in reasoning_paths],
    [path["score"] for path in reasoning_paths],
    "reasoning_paths.png"
)
```

### Tree of Thought Exploration Example

This example shows how to implement tree of thought reasoning:

```python
import numpy as np
import networkx as nx
import matplotlib.pyplot as plt
from ultra.meta_cognitive import tree_of_thought
from ultra.utils.visualization import visualize_reasoning_tree

# Create a mock evaluator for the Tree of Thought
class MockEvaluator:
    def evaluate(self, context, new_text):
        """Simple mock evaluation function"""
        score = 0.5  # Base score
        
        # Boost score for texts with certain keywords
        if "divide" in new_text or "divide" in context:
            score += 0.2
        if "formula" in new_text:
            score += 0.1
        if "240" in new_text and "60" in new_text:
            score += 0.2
        if "4 hours" in new_text:
            score += 0.3
        if "convert" in new_text:
            score += 0.1
            
        # Cap score at 1.0
        return min(1.0, score)

# Initialize the Tree of Thought system
tot = tree_of_thought.TreeOfThoughtExploration(
    MockLanguageModel(),  # Use the same mock language model from previous example
    MockEvaluator(),
    max_depth=3,
    branching_factor=2
)

# Example problem
problem = "If a train travels at 60 mph, how long will it take to travel 240 miles?"

# Explore the reasoning tree
exploration_result = tot.explore(problem)

# Print the best path
print(f"Problem: {problem}\n")
print("Best reasoning path:")
for step in exploration_result:
    print(f"\n{step}")

# Create a visualization of the reasoning tree
# In a real implementation, we would extract the tree structure from tot
# Here we'll create a mock tree for visualization

# Create a simple reasoning tree
G = nx.DiGraph()
G.add_node("0", text=problem, score=0.5)
G.add_node("1_0", text="I'll solve using the time = distance/speed formula.", score=0.7)
G.add_node("1_1", text="Let me first convert the units to make sure everything is consistent.", score=0.6)
G.add_node("2_0", text="Given: distance = 240 miles, speed = 60 mph.", score=0.8)
G.add_node("2_1", text="The train travels at 60 miles per hour and needs to cover 240 miles.", score=0.75)
G.add_node("3_0", text="Time = 240 miles / 60 mph = 4 hours.", score=0.95)
G.add_node("3_1", text="If we divide 240 by 60, we get 4, so it takes 4 hours.", score=0.9)

G.add_edge("0", "1_0")
G.add_edge("0", "1_1")
G.add_edge("1_0", "2_0")
G.add_edge("1_1", "2_1")
G.add_edge("2_0", "3_0")
G.add_edge("2_1", "3_1")

# Mark the best path
best_path = ["0", "1_0", "2_0", "3_0"]

# Visualize the tree
visualize_reasoning_tree(G, best_path, "reasoning_tree.png")
```

### Self-Critique Loop Example

This example demonstrates the self-critique mechanism:

```python
import numpy as np
from ultra.meta_cognitive import self_critique
from ultra.utils.visualization import visualize_critique_refinement

# Initialize the self-critique module
critique_system = self_critique.SelfCritiqueLoop()

# Define a mock language model with improved capabilities
class EnhancedMockLanguageModel:
    def generate(self, prompt):
        """Generate responses based on the prompt content"""
        # Return critique for a solution
        if "Proposed solution" in prompt and "Critique:" in prompt:
            return "The solution correctly uses the formula Time = Distance/Speed. However, there are two issues: (1) The units are not explicitly checked for consistency, and (2) the answer does not specify the units of the final result."
        
        # Evaluate if a critique is substantial
        elif "Is this critique substantial" in prompt:
            return "YES"
        
        # Generate improved solution based on critique
        elif "improved solution" in prompt:
            return "To find how long it takes the train to travel 240 miles at 60 mph, I'll use the formula Time = Distance/Speed.\n\nFirst, I'll check the units: Speed is in miles per hour (mph) and Distance is in miles, so the result will be in hours.\n\nCalculation: Time = 240 miles / 60 mph = 4 hours\n\nTherefore, it will take the train 4 hours to travel 240 miles."
        
        # Evaluate if the improvement is significant
        elif "Is the improved solution significantly better" in prompt:
            return "YES"
        
        # Default response
        else:
            return "Generated response"

# Set the language model
critique_system.language_model = EnhancedMockLanguageModel()

# Problem and initial solution
problem = "If a train travels at 60 mph, how long will it take to travel 240 miles?"
initial_solution = "Time = Distance/Speed, so Time = 240/60 = 4."

# Apply the self-critique loop
refined_solution = critique_system.refine_reasoning(problem, initial_solution)

# Print the results
print(f"Problem: {problem}\n")
print(f"Initial solution: {initial_solution}\n")
print(f"Refined solution: {refined_solution}")

# For visualization, create example critique steps
critique_steps = [
    {
        "solution": initial_solution,
        "critique": "The solution correctly uses the formula Time = Distance/Speed. However, there are two issues: (1) The units are not explicitly checked for consistency, and (2) the answer does not specify the units of the final result."
    },
    {
        "solution": refined_solution,
        "critique": "The solution is now complete and correct. It checks units for consistency, shows the calculation clearly, and specifies the units in the final answer."
    }
]

# Visualize the critique and refinement process
visualize_critique_refinement(critique_steps, "critique_refinement.png")
```

## Neuromorphic Processing Layer

### Spiking Neural Network Example

This example shows how to use spiking neural networks:

```python
import numpy as np
import matplotlib.pyplot as plt
from ultra.neuromorphic_processing import spiking_networks
from ultra.utils.visualization import plot_spiking_activity

# Create a spiking neural network
# Parameters for LIF neurons
tau = 10.0       # Membrane time constant (ms)
v_rest = 0.0     # Resting potential (mV)
v_threshold = 1.0  # Threshold potential (mV)
v_reset = 0.0    # Reset potential (mV)
refractory_period = 5.0  # Refractory period (ms)

# Create a small network
n_input = 100
n_hidden = 200
n_output = 10

snn = spiking_networks.SpikingNeuralNetwork(
    n_input=n_input,
    n_hidden=n_hidden,
    n_output=n_output,
    neuron_params={
        'tau': tau,
        'v_rest': v_rest,
        'v_threshold': v_threshold,
        'v_reset': v_reset,
        'refractory_period': refractory_period
    }
)

# Simulation parameters
simulation_time = 100.0  # ms
dt = 0.1  # ms
steps = int(simulation_time / dt)

# Create input spike pattern (random Poisson spikes)
rate = 20  # Hz
input_spikes = np.random.rand(n_input, steps) < rate * dt / 1000

# Run the simulation
input_spike_times = []
hidden_spike_times = []
output_spike_times = []

# Store membrane potentials for visualization
v_records = {
    'input': np.zeros((3, steps)),  # Record 3 random input neurons
    'hidden': np.zeros((3, steps)),  # Record 3 random hidden neurons
    'output': np.zeros((3, steps))   # Record 3 random output neurons
}

# Select random neurons to record
record_indices = {
    'input': np.random.choice(n_input, 3, replace=False),
    'hidden': np.random.choice(n_hidden, 3, replace=False),
    'output': np.random.choice(n_output, 3, replace=False)
}

# Run simulation
for t in range(steps):
    # Get input spikes for this time step
    current_input = input_spikes[:, t]
    
    # Convert to input currents (simplified)
    input_currents = current_input * 2.0  # Amplify spikes
    
    # Run one step of the SNN
    hidden_v, output_v, hidden_spikes, output_spikes = snn.step(
        input_currents, dt
    )
    
    # Record spikes
    for i in np.where(current_input)[0]:
        input_spike_times.append((i, t * dt))
    
    for i in np.where(hidden_spikes)[0]:
        hidden_spike_times.append((i, t * dt))
    
    for i in np.where(output_spikes)[0]:
        output_spike_times.append((i, t * dt))
    
    # Record membrane potentials
    for i, idx in enumerate(record_indices['input']):
        v_records['input'][i, t] = input_currents[idx]  # No actual V for inputs in this simple model
    
    for i, idx in enumerate(record_indices['hidden']):
        v_records['hidden'][i, t] = hidden_v[idx]
    
    for i, idx in enumerate(record_indices['output']):
        v_records['output'][i, t] = output_v[idx]

# Plot spiking activity
plot_spiking_activity(
    input_spike_times,
    hidden_spike_times,
    output_spike_times,
    v_records,
    simulation_time,
    dt,
    "spiking_network_simulation.png"
)

# Calculate statistics
input_rate = len(input_spike_times) / (n_input * simulation_time / 1000)
hidden_rate = len(hidden_spike_times) / (n_hidden * simulation_time / 1000)
output_rate = len(output_spike_times) / (n_output * simulation_time / 1000)

print(f"Average firing rates:")
print(f"  Input layer: {input_rate:.2f} Hz")
print(f"  Hidden layer: {hidden_rate:.2f} Hz")
print(f"  Output layer: {output_rate:.2f} Hz")
```

### Memristor Array Example

This example demonstrates how to use memristor arrays for computation:

```python
import numpy as np
import matplotlib.pyplot as plt
from ultra.neuromorphic_processing import memristor_array
from ultra.utils.visualization import visualize_memristor_dynamics

# Create a memristor array
rows = 10
cols = 10
array = memristor_array.MemristorArray(rows, cols)

# Parameters for the memristor model
r_on = 100      # Low resistance state (ohms)
r_off = 10000   # High resistance state (ohms)
k_p = 1e-4      # Positive voltage coefficient
k_n = 1e-4      # Negative voltage coefficient

# Initialize with random resistances between r_on and r_off
initial_resistances = r_on + np.random.rand(rows, cols) * (r_off - r_on)
array.initialize_resistances(initial_resistances)

# Create a sample vector to multiply with the array
input_vector = np.random.rand(rows)

# Simulation parameters
simulation_time = 10.0  # seconds
dt = 0.01  # seconds
steps = int(simulation_time / dt)

# Track resistance changes over time for a sample memristor
sample_row, sample_col = 5, 5
resistance_history = np.zeros(steps)
current_history = np.zeros(steps)
voltage_history = np.zeros(steps)

# Run the simulation with a sinusoidal input voltage
for t in range(steps):
    # Create input voltages (sinusoidal for the sample row)
    row_voltages = np.zeros(rows)
    row_voltages[sample_row] = np.sin(2 * np.pi * t * dt) * 2.0  # 2V amplitude
    
    # Ground all columns for this simplified example
    col_voltages = np.zeros(cols)
    
    # Apply voltages to the array
    currents = array.apply_voltages(row_voltages, col_voltages, dt)
    
    # Record history for the sample memristor
    resistance_history[t] = array.get_resistance(sample_row, sample_col)
    current_history[t] = currents[sample_row, sample_col]
    voltage_history[t] = row_voltages[sample_row] - col_voltages[sample_col]

# Demonstrate vector-matrix multiplication with the memristor array
result = array.vector_matrix_multiply(input_vector)

print("Vector-Matrix Multiplication Result:")
print(result[:5])  # Print first 5 elements

# Visualize memristor dynamics
visualize_memristor_dynamics(
    np.arange(0, simulation_time, dt),
    voltage_history,
    current_history,
    resistance_history,
    "memristor_dynamics.png"
)

# Visualize final resistance matrix as a heatmap
plt.figure(figsize=(8, 6))
plt.imshow(array.read_resistances(), cmap='viridis')
plt.colorbar(label='Resistance (Ohms)')
plt.title('Memristor Array Resistance Values')
plt.xlabel('Column')
plt.ylabel('Row')
plt.savefig("memristor_array.png")
plt.show()
```

### Reservoir Computing Example

This example demonstrates reservoir computing for temporal processing:

```python
import numpy as np
import matplotlib.pyplot as plt
from ultra.neuromorphic_processing import reservoir_computing
from ultra.utils.visualization import visualize_reservoir_states

# Create a reservoir computing network
input_size = 1
reservoir_size = 100
output_size = 1
spectral_radius = 0.9
connectivity = 0.1
leak_rate = 0.3

reservoir = reservoir_computing.ReservoirComputing(
    input_size=input_size,
    reservoir_size=reservoir_size,
    output_size=output_size,
    spectral_radius=spectral_radius,
    connectivity=connectivity
)

# Generate training data (simple sine wave)
def generate_sine_wave(n_samples, freq=1.0, noise=0.0):
    t = np.linspace(0, 2*np.pi, n_samples)
    y = np.sin(freq * t)
    if noise > 0:
        y += np.random.normal(0, noise, n_samples)
    return y.reshape(-1, 1)

# Training parameters
n_train = 1000
n_test = 200
train_data = generate_sine_wave(n_train + 1)  # +1 for target

# Prepare input/target pairs
X_train = train_data[:-1]
y_train = train_data[1:]  # Predict next value

# Collect reservoir states for all training inputs
train_states = np.zeros((n_train, reservoir_size))
for i, u in enumerate(X_train):
    reservoir.reset_state()  # Reset for each sequence
    train_states[i] = reservoir.update(u, leak_rate)

# Train output weights using ridge regression
ridge_param = 1e-6
reservoir.train(X_train, y_train, ridge_param)

# Test on new data
test_data = generate_sine_wave(n_test + 1, freq=1.0, noise=0.05)
X_test = test_data[:-1]
y_test = test_data[1:]

# Make predictions
predictions = reservoir.predict(X_test)

# Calculate mean squared error
mse = np.mean((predictions - y_test) ** 2)
print(f"Test MSE: {mse:.6f}")

# Visualize reservoir states
reservoir.reset_state()
test_states = []
for u in X_test[:100]:  # Use first 100 inputs
    state = reservoir.update(u, leak_rate)
    test_states.append(state)

visualize_reservoir_states(
    np.array(test_states),
    X_test[:100],
    predictions[:100],
    y_test[:100],
    "reservoir_computing.png"
)

# Plot predictions vs targets
plt.figure(figsize=(10, 6))
plt.plot(y_test, label='Target')
plt.plot(predictions, label='Prediction')
plt.title('Reservoir Computing: Predictions vs Targets')
plt.xlabel('Time Step')
plt.ylabel('Value')
plt.legend()
plt.savefig("reservoir_predictions.png")
plt.show()
```

## Neuro-Symbolic Integration

### Neuro-Symbolic Bridge Example

This example demonstrates the integration of neural and symbolic representations:

```python
import numpy as np
import torch
import torch.nn as nn
import matplotlib.pyplot as plt
from ultra.neuro_symbolic import neuro_symbolic_bridge
from ultra.utils.visualization import visualize_symbol_embedding

# Create a neuro-symbolic bridge
bridge = neuro_symbolic_bridge.NeuroSymbolicBridge(
    neural_dim=64,
    symbolic_dim=32,
    hidden_dim=128
)

# Create some sample symbolic expressions
symbolic_expressions = [
    "father(john, mary)",
    "mother(lisa, john)",
    "parent(X, Y) :- father(X, Y)",
    "parent(X, Y) :- mother(X, Y)",
    "grandparent(X, Z) :- parent(X, Y), parent(Y, Z)"
]

# Mock neural representations (in a real system, these would come from a neural model)
neural_representations = torch.randn(len(symbolic_expressions), 64)

# Translate from neural to symbolic
symbolic_representations = []
for i, neural_rep in enumerate(neural_representations):
    symbolic_rep = bridge.neural_to_symbolic(neural_rep)
    symbolic_representations.append(symbolic_rep)
    print(f"Neural → Symbolic: {symbolic_expressions[i]} → {symbolic_rep}")

# Translate from symbolic to neural
neural_back = []
for i, symbolic_rep in enumerate(symbolic_representations):
    neural_rep = bridge.symbolic_to_neural(symbolic_rep)
    neural_back.append(neural_rep)
    
    # Calculate reconstruction error
    original = neural_representations[i]
    reconstructed = neural_rep
    error = torch.norm(original - reconstructed).item()
    print(f"Symbolic → Neural: {symbolic_expressions[i]}, Reconstruction Error: {error:.4f}")

# Demonstrate logical operations in the symbolic space
# In a real system, these would be actual logical operations
# Here we'll just simulate the results
symbolic_op_results = {
    "Resolution": "grandparent(john, Z) :- parent(mary, Z)",
    "Unification": "parent(john, mary)"
}

for op, result in symbolic_op_results.items():
    print(f"\nSymbolic {op} Result: {result}")
    # Map result back to neural space
    neural_result = bridge.symbolic_to_neural(result)
    print(f"Mapped to Neural Space: {neural_result[:5]}...")  # Show first 5 values

# Demonstrate neural operations mapped to symbolic space
# We'll use a simple vector addition as an example neural operation
neural_op_result = neural_representations[0] + neural_representations[1]
symbolic_op_result = bridge.neural_to_symbolic(neural_op_result)
print(f"\nNeural Operation Result mapped to Symbolic: {symbolic_op_result}")

# Visualize embeddings
visualize_symbol_embedding(
    neural_representations.numpy(),
    symbolic_expressions,
    "neuro_symbolic_embedding.png"
)
```

### Program Synthesis Example

This example shows how to use program synthesis for problem-solving:

```python
import numpy as np
from ultra.neuro_symbolic import program_synthesis
from ultra.utils.visualization import visualize_program_search

# Initialize the program synthesis module
synthesizer = program_synthesis.ProgramSynthesis()

# Define a simple task specification
task = {
    "description": "Calculate the sum of squares of numbers in a list",
    "input_output_examples": [
        {"input": [1, 2, 3], "output": 14},  # 1^2 + 2^2 + 3^2 = 1 + 4 + 9 = 14
        {"input": [4, 5], "output": 41},     # 4^2 + 5^2 = 16 + 25 = 41
        {"input": [], "output": 0}           # Empty list should return 0
    ]
}

# Synthesize a program
program, search_info = synthesizer.synthesize(
    task, 
    language="python",
    return_search_info=True
)

print("Synthesized Program:")
print(program)

# Verify the program on test cases
print("\nVerifying program on examples:")
for example in task["input_output_examples"]:
    input_val = example["input"]
    expected_output = example["output"]
    # In a real system, we would execute the program
    # Here we'll simulate the result
    if "sum([x**2 for x in" in program:
        actual_output = sum([x**2 for x in input_val])
    else:
        actual_output = "Could not verify"
    
    print(f"Input: {input_val}, Expected: {expected_output}, Actual: {actual_output}")

# Visualize the program search process
# The search_info would contain the actual search trajectory in a real system
# Here we'll create a mock search trajectory for visualization
mock_search_tree = [
    {"program": "def sum_of_squares(numbers):", "score": 0.2},
    {"program": "def sum_of_squares(numbers):\n    return sum(numbers)", "score": 0.4},
    {"program": "def sum_of_squares(numbers):\n    return sum([x*x for x in numbers])", "score": 0.7},
    {"program": "def sum_of_squares(numbers):\n    return sum([x**2 for x in numbers])", "score": 0.9},
    {"program": "def sum_of_squares(numbers):\n    result = 0\n    for x in numbers:\n        result += x**2\n    return result", "score": 0.85}
]

visualize_program_search(mock_search_tree, "program_synthesis.png")
```

## Self-Evolution System

### Neural Architecture Search Example

This example demonstrates neural architecture search:

```python
import numpy as np
import matplotlib.pyplot as plt
from ultra.self_evolution import neural_architecture_search
from ultra.utils.visualization import visualize_architecture_search

# Initialize Neural Architecture Search
nas = neural_architecture_search.NeuralArchitectureSearch(
    search_space="transformer",
    max_layers=6,
    max_attention_heads=16,
    max_hidden_dim=1024
)

# Define a simple evaluation function
def evaluate_architecture(architecture, data=None):
    """Mock evaluation function that returns a score for an architecture"""
    # In a real system, we would train and evaluate the architecture
    # Here we'll use a simple heuristic based on the architecture parameters
    
    # Extract architecture parameters
    num_layers = architecture.get("num_layers", 0)
    num_heads = architecture.get("num_heads", 0)
    hidden_dim = architecture.get("hidden_dim", 0)
    dropout = architecture.get("dropout", 0)
    
    # Calculate score components
    performance_score = 0.5 + 0.1 * num_layers + 0.02 * num_heads + 0.0001 * hidden_dim
    complexity_penalty = 0.01 * num_layers * num_heads + 0.00001 * hidden_dim
    regularization_bonus = 0.1 * dropout
    
    # Final score with some randomness
    score = performance_score - complexity_penalty + regularization_bonus
    score += np.random.normal(0, 0.05)  # Add noise
    
    # Cap score between 0 and 1
    return max(0, min(1, score))

# Set evaluation function
nas.set_evaluator(evaluate_architecture)

# Run architecture search
search_results = nas.search(
    num_iterations=20,
    population_size=10
)

# Get the best architecture
best_architecture = search_results["best_architecture"]
best_score = search_results["best_score"]

print("Best Architecture:")
for key, value in best_architecture.items():
    print(f"  {key}: {value}")
print(f"Score: {best_score:.4f}")

# Visualize the search process
visualize_architecture_search(
    search_results["search_history"],
    "architecture_search.png"
)

# Compare top architectures
top_architectures = search_results["top_architectures"][:5]  # Top 5
print("\nTop 5 Architectures:")
for i, (arch, score) in enumerate(top_architectures):
    print(f"\n{i+1}. Score: {score:.4f}")
    for key, value in arch.items():
        print(f"  {key}: {value}")
```

### Computational Reflection Example

This example demonstrates computational reflection capabilities:

```python
import numpy as np
import time
import matplotlib.pyplot as plt
from ultra.self_evolution import computational_reflection
from ultra.utils.visualization import visualize_performance_profile

# Initialize the computational reflection module
reflector = computational_reflection.ComputationalReflection()

# Define a function to be analyzed
def example_function(n, algorithm='recursive'):
    """Compute the nth Fibonacci number using different algorithms"""
    if algorithm == 'recursive':
        if n <= 1:
            return n
        return example_function(n-1, algorithm) + example_function(n-2, algorithm)
    
    elif algorithm == 'iterative':
        if n <= 1:
            return n
        a, b = 0, 1
        for _ in range(2, n+1):
            a, b = b, a+b
        return b
    
    elif algorithm == 'matrix':
        if n <= 1:
            return n
        
        def matrix_multiply(A, B):
            C = [[0, 0], [0, 0]]
            for i in range(2):
                for j in range(2):
                    for k in range(2):
                        C[i][j] += A[i][k] * B[k][j]
            return C
        
        def matrix_power(A, n):
            if n == 1:
                return A
            if n % 2 == 0:
                return matrix_power(matrix_multiply(A, A), n // 2)
            else:
                return matrix_multiply(A, matrix_power(matrix_multiply(A, A), (n-1) // 2))
        
        result = matrix_power([[1, 1], [1, 0]], n-1)
        return result[0][0]
    
    else:
        raise ValueError(f"Unknown algorithm: {algorithm}")

# Let's analyze this function
code_info = reflector.analyze_code(example_function)

print("Code Analysis:")
print(f"Function name: {code_info['name']}")
print(f"Parameters: {code_info['parameters']}")
print(f"Algorithms detected: {code_info['algorithms']}")
print(f"Recursive calls detected: {code_info['recursive_calls']}")

# Run performance analysis
performance_data = {}
algorithms = ['recursive', 'iterative', 'matrix']
n_values = list(range(5, 31, 5))

for algorithm in algorithms:
    performance_data[algorithm] = []
    
    for n in n_values:
        # Skip high values of n for recursive algorithm to avoid timeout
        if algorithm == 'recursive' and n > 20:
            performance_data[algorithm].append(None)
            continue
            
        # Measure execution time
        start_time = time.time()
        result = example_function(n, algorithm)
        elapsed_time = time.time() - start_time
        
        performance_data[algorithm].append({
            'n': n,
            'result': result,
            'time': elapsed_time
        })

# Generate performance report
report = reflector.generate_performance_report(performance_data)

print("\nPerformance Report:")
print(f"Fastest algorithm: {report['fastest_algorithm']}")
print(f"Memory usage estimate: {report['memory_usage_estimate']}")
print("\nAlgorithm comparison:")
for algorithm, stats in report['algorithm_stats'].items():
    print(f"  {algorithm}: {stats}")

# Recommendations for code improvement
recommendations = reflector.recommend_improvements(code_info, performance_data)

print("\nRecommendations for Improvement:")
for i, recommendation in enumerate(recommendations):
    print(f"{i+1}. {recommendation}")

# Visualize performance data
visualize_performance_profile(
    n_values,
    performance_data,
    "computational_reflection.png"
)
```

## Complete System Integration

This example shows how to integrate all ULTRA components:

```python
import numpy as np
import torch
from ultra.main import ULTRA
from ultra.utils.config import load_config

# Initialize the ULTRA system with a configuration
config = load_config("config/system_params.yaml")
ultra_system = ULTRA(config)

# Define a sample problem
problem = "What is the most efficient algorithm for finding the shortest path in a weighted graph and why?"

# Process the problem through the ULTRA pipeline
result = ultra_system.process(problem)

print("ULTRA System Analysis:")
print(f"Input: {problem}")
print(f"Output: {result['output']}")
print("\nModule Activations:")
for module, activation in result['module_activations'].items():
    print(f"  {module}: {activation:.4f}")

print("\nReasoning Path:")
for step in result['reasoning_path']:
    print(f"  {step}")

print("\nUncertainty Metrics:")
for metric, value in result['uncertainty'].items():
    print(f"  {metric}: {value:.4f}")

# Demonstrate system adaptation
print("\nDemonstrating system adaptation...")
ultra_system.adapt("The provided answer was unclear in explaining the time complexity trade-offs.")

# Process the same problem again
adapted_result = ultra_system.process(problem)

print("\nAdapted Output:")
print(adapted_result['output'])

# Show improvement metrics
improvement = {
    "clarity": adapted_result['metrics']['clarity'] - result['metrics']['clarity'],
    "completeness": adapted_result['metrics']['completeness'] - result['metrics']['completeness'],
    "technical_accuracy": adapted_result['metrics']['technical_accuracy'] - result['metrics']['technical_accuracy']
}

print("\nImprovement Metrics:")
for metric, value in improvement.items():
    print(f"  {metric}: {value:+.4f}")
```

This document provides a comprehensive set of examples demonstrating the use of each core module in the ULTRA system. The examples show how to initialize, configure, and use each component, along with visualizations and metrics to understand system behavior.

For more detailed information on specific modules, refer to the API documentation in `api/` and implementation details in `implementation/`.