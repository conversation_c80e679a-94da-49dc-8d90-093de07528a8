#!/usr/bin/env python3
"""
Test Authentic ULTRA Responses
==============================

This script tests the new authentic ULTRA response system to ensure
responses are generated through actual 8-core processing instead of
predetermined messages.
"""

import sys
import asyncio
import time
from pathlib import Path

# Add ULTRA to path
ultra_root = Path(__file__).parent
sys.path.insert(0, str(ultra_root))

async def test_authentic_responses():
    """Test authentic ULTRA responses through 8-core processing"""
    print("🧪 TESTING AUTHENTIC ULTRA RESPONSES")
    print("=" * 50)
    
    try:
        from ultra.unified_backend import ULTRAUnifiedBackend, ULTRARequest
        
        # Initialize backend
        print("🔧 Initializing ULTRA Unified Backend...")
        backend = ULTRAUnifiedBackend()
        await backend.initialize_system()
        print("✅ Backend initialized successfully")
        
        # Test queries that should generate authentic responses
        test_queries = [
            "Hello, what is artificial intelligence?",
            "How does machine learning work?", 
            "Why is consciousness important?",
            "Can you explain neural networks?",
            "What are the benefits of AGI?",
            "How do you process information?",
            "What makes you different from other AI?"
        ]
        
        print(f"\n🎯 Testing {len(test_queries)} queries for authentic responses...")
        print("-" * 60)
        
        for i, query in enumerate(test_queries, 1):
            print(f"\n📝 Query {i}: {query}")
            print("🔄 Processing through ULTRA 8-core architecture...")
            
            # Create request
            request = ULTRARequest(
                operation="chat",
                input_data=query,
                options={"authentic_processing": True}
            )
            
            # Process request
            start_time = time.time()
            response = await backend.process_unified_request(request, None)
            processing_time = time.time() - start_time
            
            # Display results
            print(f"⏱️ Processing time: {processing_time:.3f}s")
            print(f"✅ Success: {response.success}")
            
            if response.success:
                result = response.result
                print(f"🧠 ULTRA Response: {result}")
                
                # Check if response seems authentic (not predetermined)
                if isinstance(result, str):
                    if "through" in result.lower() and ("core" in result.lower() or "system" in result.lower()):
                        print("✅ Response appears authentic (mentions core processing)")
                    elif len(result) > 100:
                        print("✅ Response appears authentic (detailed and contextual)")
                    else:
                        print("⚠️ Response may be predetermined (too generic)")
                else:
                    print("ℹ️ Response is structured data")
                
                # Show processing metadata
                if hasattr(response, 'metadata') and response.metadata:
                    bridges_used = response.metadata.get('bridges_used', [])
                    print(f"🔗 Bridges used: {', '.join(bridges_used)}")
            else:
                print(f"❌ Error: {response.result}")
            
            print("-" * 60)
        
        print("\n🎉 Authentic response testing completed!")
        
        # Shutdown backend
        await backend.shutdown_system()
        print("🔄 Backend shutdown complete")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def test_core_processing_verification():
    """Verify that all 8 cores are being used in processing"""
    print("\n🔍 VERIFYING 8-CORE PROCESSING")
    print("=" * 40)
    
    try:
        from ultra.unified_backend import ULTRAUnifiedBackend
        
        backend = ULTRAUnifiedBackend()
        
        # Test the core processing method directly
        test_context = {
            'operation': 'chat',
            'input': 'Test query for core verification',
            'bridge_results': {'test': 'data'},
            'timestamp': time.time()
        }
        
        print("🧠 Testing individual core processing stages...")
        
        # Test each core stage
        stages = [
            ('Neural Core', backend._process_neural_core),
            ('Diffusion Reasoning', lambda ctx: backend._process_diffusion_reasoning(ctx, test_context)),
            ('Meta-Cognitive', lambda ctx: backend._process_meta_cognitive(ctx, test_context)),
            ('Hyper-Transformer', lambda ctx: backend._process_hyper_transformer(ctx, test_context)),
            ('Neuromorphic', lambda ctx: backend._process_neuromorphic(ctx, test_context)),
            ('Consciousness', lambda ctx: backend._process_consciousness(ctx, test_context)),
            ('Neuro-Symbolic', lambda ctx: backend._process_neuro_symbolic(ctx, test_context)),
            ('Self-Evolution', lambda ctx: backend._process_self_evolution(ctx, test_context))
        ]
        
        context = test_context.copy()
        
        for stage_name, stage_func in stages:
            try:
                print(f"  🔧 Testing {stage_name}...")
                result = await stage_func(context)
                
                if isinstance(result, dict):
                    context.update(result)
                    print(f"  ✅ {stage_name} processed successfully")
                elif isinstance(result, str):
                    print(f"  ✅ {stage_name} generated final response")
                    break
                else:
                    print(f"  ⚠️ {stage_name} returned unexpected type: {type(result)}")
                    
            except Exception as e:
                print(f"  ❌ {stage_name} failed: {str(e)}")
        
        print("✅ Core processing verification completed")
        return True
        
    except Exception as e:
        print(f"❌ Core verification failed: {str(e)}")
        return False

def main():
    """Main test function"""
    print("🚀 ULTRA AUTHENTIC RESPONSE TESTING")
    print("=" * 50)
    print("Testing new backend to ensure authentic responses")
    print("through actual 8-core ULTRA processing architecture")
    print("=" * 50)
    
    async def run_tests():
        # Test 1: Authentic responses
        test1_success = await test_authentic_responses()
        
        # Test 2: Core processing verification
        test2_success = await test_core_processing_verification()
        
        # Summary
        print("\n📊 TEST SUMMARY")
        print("=" * 30)
        print(f"✅ Authentic Responses: {'PASSED' if test1_success else 'FAILED'}")
        print(f"✅ Core Processing: {'PASSED' if test2_success else 'FAILED'}")
        
        overall_success = test1_success and test2_success
        print(f"\n🎯 Overall Result: {'ALL TESTS PASSED' if overall_success else 'SOME TESTS FAILED'}")
        
        if overall_success:
            print("🎉 ULTRA is now generating authentic responses through 8-core processing!")
        else:
            print("⚠️ Some issues detected - responses may still be predetermined")
        
        return overall_success
    
    # Run async tests
    success = asyncio.run(run_tests())
    return success

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ Testing interrupted by user")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Unexpected error: {str(e)}")
        sys.exit(1)
