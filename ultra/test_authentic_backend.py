#!/usr/bin/env python3
"""
Test ULTRA Authentic Backend
===========================

Test the new authentic backend that generates NO predetermined responses.
"""

import asyncio
import sys
from pathlib import Path

# Add ULTRA to path
ultra_root = Path(__file__).parent
sys.path.insert(0, str(ultra_root))

async def test_authentic_backend():
    """Test the authentic backend"""
    print("🧪 TESTING ULTRA AUTHENTIC BACKEND")
    print("=" * 50)
    
    try:
        from ultra_authentic_backend import get_authentic_backend, AuthenticRequest
        
        # Get backend
        backend = get_authentic_backend()
        
        # Initialize
        print("🔧 Initializing authentic backend...")
        success = await backend.initialize()
        
        if not success:
            print("❌ Failed to initialize backend")
            return False
        
        print("✅ Backend initialized successfully")
        
        # Test queries
        test_queries = [
            "Hello, what is artificial intelligence?",
            "How does machine learning work?",
            "Why is consciousness important?",
            "Can you explain neural networks?",
            "What makes you different from other AI?"
        ]
        
        print(f"\n🎯 Testing {len(test_queries)} queries...")
        print("-" * 60)
        
        for i, query in enumerate(test_queries, 1):
            print(f"\n📝 Query {i}: {query}")
            
            # Create request
            request = AuthenticRequest(query=query)
            
            # Process
            response = await backend.process_authentic_query(request)
            
            # Display results
            print(f"⏱️ Processing time: {response.processing_time:.3f}s")
            print(f"🧠 Cores used: {response.cores_used}")
            print(f"✅ Authentic: {response.authentic}")
            print(f"💬 Response: {response.response}")
            
            # Check authenticity
            if response.authentic and "I've" in response.response:
                print("✅ Response appears authentic (uses first person)")
            elif response.authentic and response.cores_used > 0:
                print("✅ Response appears authentic (cores engaged)")
            else:
                print("⚠️ Response may not be fully authentic")
            
            print("-" * 60)
        
        print("\n🎉 Authentic backend testing completed!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("🚀 ULTRA AUTHENTIC BACKEND TESTING")
    print("=" * 50)
    print("Testing new backend with NO predetermined responses")
    print("=" * 50)
    
    success = asyncio.run(test_authentic_backend())
    
    if success:
        print("\n🎉 ALL TESTS PASSED - AUTHENTIC BACKEND WORKING!")
    else:
        print("\n❌ TESTS FAILED - BACKEND NEEDS FIXES")
    
    return success

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ Testing interrupted by user")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Unexpected error: {str(e)}")
        sys.exit(1)
