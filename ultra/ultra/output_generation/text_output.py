#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ULTRA Text Generation Module

This module implements the TextGenerator class for the ULTRA (Ultimate Learning
& Thought Reasoning Architecture) system, enabling the transformation of
internal representations into natural language text outputs.

The module integrates with state-of-the-art transformer-based language models,
implements sophisticated decoding strategies, and provides control over
generation style, tone, and format. It works closely with ULTRA's Meta-Cognitive
System to incorporate reasoning traces and self-reflection capabilities.

Key features:
- Multiple decoding strategies (auto-regressive, non-auto-regressive, hybrid)
- Advanced sampling methods (nucleus sampling, contrastive search, etc.)
- Dynamic prompt engineering with multiple strategies
- Control over formality, style, and tone
- Integration with reasoning traces from Meta-Cognitive System
- Self-reflexive generation capabilities
- Long context handling with sliding window approaches
- Guidance and control mechanisms for steering generation
"""

import os
import re
import json
import time
import hashlib
import logging
import warnings
import numpy as np
from copy import deepcopy
from typing import Dict, List, Tuple, Union, Optional, Any, Callable, Set, Type
from dataclasses import dataclass
from collections import OrderedDict, defaultdict

import torch
import torch.nn as nn
import torch.nn.functional as F

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(name)s: %(message)s'
)
logger = logging.getLogger(__name__)

# Import necessary transformers components if available
try:
    import transformers
    from transformers import (
        AutoTokenizer, AutoModelForCausalLM, AutoModelForSeq2SeqLM,
        PreTrainedTokenizer, PreTrainedModel, PreTrainedTokenizerFast,
        LogitsProcessor, LogitsProcessorList, StoppingCriteriaList, StoppingCriteria,
        GenerationConfig, top_k_top_p_filtering
    )
    from transformers.generation.utils import GenerationMixin

    # Check if we have access to specific model variants
    _TRANSFORMERS_MAJOR_VERSION = int(transformers.__version__.split('.')[0])
    _HAS_TRANSFORMERS_ADVANCED = _TRANSFORMERS_MAJOR_VERSION >= 4
    _HAS_TRANSFORMERS = True
except ImportError:
    logger.warning("Transformers library not found. Text generation capabilities will be limited.")
    _HAS_TRANSFORMERS = False
    _HAS_TRANSFORMERS_ADVANCED = False

# Try to import additional helpful libraries
try:
    import tiktoken
    _HAS_TIKTOKEN = True
except ImportError:
    _HAS_TIKTOKEN = False
    logger.debug("Tiktoken not found. Falling back to transformers tokenizers only.")

try:
    from sentence_transformers import SentenceTransformer
    _HAS_SENTENCE_TRANSFORMERS = True
except ImportError:
    _HAS_SENTENCE_TRANSFORMERS = False
    logger.debug("Sentence-transformers not found. Some semantic operations will be limited.")

try:
    import nltk
    from nltk.tokenize import sent_tokenize, word_tokenize
    # Ensure we have the necessary NLTK data
    try:
        nltk.data.find('tokenizers/punkt')
    except LookupError:
        nltk.download('punkt', quiet=True)
    _HAS_NLTK = True
except ImportError:
    _HAS_NLTK = False
    logger.debug("NLTK not found. Some text processing capabilities will be limited.")

# Import from parent module if possible
try:
    from ultra.output_generation import OutputGenerator, OutputGeneratorConfig, ReasoningTraceFormatter
except (ImportError, ValueError):
    # For standalone or testing purposes, create placeholders
    class OutputGeneratorConfig:
        def __init__(self, **kwargs):
            for key, value in kwargs.items():
                setattr(self, key, value)
                
    class OutputGenerator:
        def __init__(self, config):
            self.config = config
            
    class ReasoningTraceFormatter:
        def __init__(self, **kwargs):
            for key, value in kwargs.items():
                setattr(self, key, value)
            
        def format_trace(self, reasoning_trace, output_modality="text"):
            return str(reasoning_trace)


class TextGeneratorConfig(OutputGeneratorConfig):
    """
    Configuration for the TextGenerator.
    
    This class extends the base OutputGeneratorConfig with text-specific parameters
    for controlling generation behavior, language model selection, decoding strategies,
    and various stylistic controls.
    """
    
    def __init__(self, **kwargs):
        # Text-specific defaults
        text_defaults = {
            # Model configuration
            "model_name": "gpt2",               # Generation model
            "model_type": "causal_lm",          # Model type: causal_lm or seq2seq
            "use_local_model": True,            # Whether to use locally downloaded model
            "custom_model_path": None,          # Path to custom model
            "use_quantization": False,          # Whether to use quantization
            "quantization_bits": 8,             # Quantization bits (4, 8)
            "use_flash_attention": False,       # Whether to use flash attention
            
            # Tokenization configuration
            "tokenizer_name": None,             # Tokenizer name (defaults to model_name if None)
            "tokenizer_padding_side": "right",  # Padding side for tokenizer
            "tokenizer_truncation_side": "right", # Truncation side for tokenizer
            "add_special_tokens": True,         # Whether to add special tokens
            "use_fast_tokenizer": True,         # Whether to use the fast tokenizer
            
            # Generation configuration
            "decoding_strategy": "auto_regressive", # Decoding strategy
            "max_new_tokens": 512,              # Maximum new tokens to generate
            "min_new_tokens": 10,               # Minimum new tokens to generate
            "stop_sequences": [],               # Sequences that stop generation
            "sampling_strategy": "top_p",       # Sampling strategy
            "do_sample": True,                  # Whether to use sampling (vs greedy)
            
            # Sampling parameters
            "temperature": 0.7,                 # Temperature for sampling
            "top_p": 0.9,                       # Top-p (nucleus) sampling parameter
            "top_k": 50,                        # Top-k sampling parameter
            "repetition_penalty": 1.05,         # Repetition penalty for sampling
            "presence_penalty": 0.0,            # Presence penalty (like OpenAI API)
            "frequency_penalty": 0.0,           # Frequency penalty (like OpenAI API)
            "length_penalty": 1.0,              # Length penalty for beam search
            "diversity_penalty": 0.0,           # Diversity penalty for beam search
            "encoder_no_repeat_ngram_size": 0,  # No repeat ngram size for encoder
            "no_repeat_ngram_size": 0,          # No repeat ngram size for decoder
            "exponential_decay_length_penalty": None, # Exponential decay length penalty
            
            # Beam search parameters
            "num_beams": 1,                     # Number of beams for beam search
            "early_stopping": True,             # Whether to use early stopping
            "num_beam_groups": 1,               # Number of beam groups for diverse beam search
            
            # Contrastive search parameters
            "use_contrastive_search": False,    # Whether to use contrastive search
            "contrastive_search_k": 4,          # k for contrastive search
            "contrastive_search_alpha": 0.6,    # Alpha for contrastive search
            
            # Prompt engineering
            "prompt_template": None,            # Template for generation prompt
            "use_system_prompt": True,          # Whether to use system prompt
            "system_prompt": "You are a helpful assistant that provides accurate information.",
            "use_prompt_engineering": True,     # Whether to use prompt engineering
            "prompt_strategies": ["cot", "examples"], # List of prompt strategies
            "cot_prompt": "Let's think step by step.",  # Chain of thought prompt
            "few_shot_examples": [],            # Few-shot examples for prompting
            
            # Style control
            "formality": "neutral",             # Formality level (casual, neutral, formal)
            "tone": "neutral",                  # Tone (enthusiastic, neutral, serious)
            "verbosity": "balanced",            # Verbosity (concise, balanced, detailed)
            "complexity": "medium",             # Complexity (simple, medium, complex)
            "perspective": "objective",         # Perspective (objective, subjective)
            "persona": None,                    # Named persona for generation
            "audience": "general",              # Target audience (technical, general, etc.)
            
            # Context handling
            "max_context_length": 2048,         # Maximum context length
            "sliding_window": True,             # Use sliding window for long contexts
            "sliding_window_size": 1024,        # Sliding window size
            "sliding_window_overlap": 128,      # Overlap between sliding windows
            "context_compression": False,       # Whether to use context compression
            "contextual_control": True,         # Whether to use contextual control
            
            # Reasoning
            "include_reasoning_trace": True,    # Include CoT reasoning traces
            "reasoning_trace_format": "text",   # Format for reasoning traces
            "reasoning_detail_level": "medium", # Detail level for reasoning traces
            "inject_reflexion": False,          # Inject reflexion into generation
            "verify_factuality": False,         # Whether to verify factuality
            "consistency_checking": True,       # Whether to check logical consistency
            
            # Formatting
            "preamble": "",                     # Text to prepend to all outputs
            "postamble": "",                    # Text to append to all outputs
            "document_structure": "auto",       # Structure format (auto, paragraph, bullet, etc.)
            "output_format": "text",            # Output format (text, markdown, json, etc.)
            
            # Post-processing
            "reranking": False,                 # Whether to use reranking
            "reranking_criteria": ["relevance", "coherence"],  # Reranking criteria
            "post_processing": True,            # Whether to apply post-processing
            "clean_output": True,               # Whether to clean the output
            "enforce_constraints": True,        # Whether to enforce output constraints
        }
        
        # Update kwargs with defaults for any missing values
        for key, value in text_defaults.items():
            if key not in kwargs:
                kwargs[key] = value
                
        # Initialize parent class
        super().__init__(**kwargs)
    
    def _validate(self):
        """Validate text-specific configuration parameters."""
        super()._validate()
        
        # Validate model type
        valid_model_types = ["causal_lm", "seq2seq"]
        if self.model_type not in valid_model_types:
            raise ValueError(f"model_type must be one of {valid_model_types}")
        
        # Validate decoding strategy
        valid_decoding = ["auto_regressive", "non_auto_regressive", "hybrid"]
        if self.decoding_strategy not in valid_decoding:
            raise ValueError(f"decoding_strategy must be one of {valid_decoding}")
        
        # Validate sampling strategy
        valid_sampling = ["top_p", "top_k", "temperature", "beam", "greedy", "contrastive"]
        if self.sampling_strategy not in valid_sampling:
            raise ValueError(f"sampling_strategy must be one of {valid_sampling}")
        
        # Validate token constraints
        if self.max_new_tokens <= 0:
            raise ValueError(f"max_new_tokens must be positive")
        
        if self.min_new_tokens < 0:
            raise ValueError(f"min_new_tokens must be non-negative")
        
        if self.min_new_tokens > self.max_new_tokens:
            raise ValueError(f"min_new_tokens must be <= max_new_tokens")
        
        # Validate formality
        valid_formality = ["casual", "neutral", "formal"]
        if self.formality not in valid_formality:
            raise ValueError(f"formality must be one of {valid_formality}")
        
        # Validate tone
        valid_tone = ["enthusiastic", "neutral", "serious"]
        if self.tone not in valid_tone:
            raise ValueError(f"tone must be one of {valid_tone}")
        
        # Validate verbosity
        valid_verbosity = ["concise", "balanced", "detailed"]
        if self.verbosity not in valid_verbosity:
            raise ValueError(f"verbosity must be one of {valid_verbosity}")
        
        # Validate complexity
        valid_complexity = ["simple", "medium", "complex"]
        if self.complexity not in valid_complexity:
            raise ValueError(f"complexity must be one of {valid_complexity}")
        
        # Validate perspective
        valid_perspective = ["objective", "subjective"]
        if self.perspective not in valid_perspective:
            raise ValueError(f"perspective must be one of {valid_perspective}")
        
        # Validate context length
        if self.max_context_length <= 0:
            raise ValueError(f"max_context_length must be positive")
        
        # Validate sliding window parameters
        if self.sliding_window:
            if self.sliding_window_size <= 0:
                raise ValueError(f"sliding_window_size must be positive")
            
            if self.sliding_window_overlap < 0:
                raise ValueError(f"sliding_window_overlap must be non-negative")
            
            if self.sliding_window_overlap >= self.sliding_window_size:
                raise ValueError(f"sliding_window_overlap must be < sliding_window_size")
        
        # Validate prompt strategies
        valid_strategies = ["cot", "examples", "step_by_step", "persona", "preamble", "reframing"]
        for strategy in self.prompt_strategies:
            if strategy not in valid_strategies:
                raise ValueError(f"prompt_strategy must be one of {valid_strategies}")
        
        # Validate reranking criteria
        if self.reranking:
            valid_criteria = ["relevance", "coherence", "novelty", "factuality", "diversity"]
            for criterion in self.reranking_criteria:
                if criterion not in valid_criteria:
                    raise ValueError(f"reranking_criterion must be one of {valid_criteria}")
                    
        # Validate beam search parameters
        if self.num_beams > 1:
            if self.num_beam_groups > self.num_beams:
                raise ValueError(f"num_beam_groups must be <= num_beams")
            
            if self.num_beam_groups > 1 and self.num_beams % self.num_beam_groups != 0:
                raise ValueError(f"num_beams must be divisible by num_beam_groups")
        
        # Validate output format
        valid_formats = ["text", "markdown", "json", "html", "xml"]
        if self.output_format not in valid_formats:
            raise ValueError(f"output_format must be one of {valid_formats}")
            
        # Validate document structure
        valid_structures = ["auto", "paragraph", "bullet", "numbered", "section", "qa"]
        if self.document_structure not in valid_structures:
            raise ValueError(f"document_structure must be one of {valid_structures}")
            
        # Validate reasoning trace format
        valid_trace_formats = ["text", "structured", "simplified"]
        if self.reasoning_trace_format not in valid_trace_formats:
            raise ValueError(f"reasoning_trace_format must be one of {valid_trace_formats}")
            
        # Validate reasoning detail level
        valid_detail_levels = ["low", "medium", "high"]
        if self.reasoning_detail_level not in valid_detail_levels:
            raise ValueError(f"reasoning_detail_level must be one of {valid_detail_levels}")
            
        # Ensure we don't have conflicting settings
        if self.sampling_strategy == "beam" and self.num_beams <= 1:
            logger.warning("Sampling strategy is set to 'beam' but num_beams is <= 1. Setting num_beams to 4.")
            self.num_beams = 4
            
        if self.use_contrastive_search and self.sampling_strategy != "contrastive":
            logger.warning("use_contrastive_search is True but sampling_strategy is not 'contrastive'. Setting sampling_strategy to 'contrastive'.")
            self.sampling_strategy = "contrastive"
            
        if not self.do_sample and self.sampling_strategy in ["top_p", "top_k", "temperature"]:
            logger.warning(f"do_sample is False but sampling_strategy is '{self.sampling_strategy}'. Setting do_sample to True.")
            self.do_sample = True


# With this:
if _HAS_TRANSFORMERS:
    class CustomStoppingCriteria(StoppingCriteria):
        """
        Custom stopping criteria for text generation.
        
        This class enables stopping text generation based on custom patterns,
        sequences, or conditions beyond what the transformer's generation function
        provides by default.
        """
    
        def __init__(
            self, 
            tokenizer: PreTrainedTokenizer,
            stop_sequences: List[str],
            prompt_length: int = 0,
            device: str = "cpu"
        ):
            """
            Initialize the stopping criteria.
            
            Args:
                tokenizer: The tokenizer used for generation
                stop_sequences: List of text sequences that trigger stopping
                prompt_length: Length of the prompt (tokens before generation started)
                device: The device to use for token comparisons
            """
            self.tokenizer = tokenizer
            self.stop_sequences = stop_sequences
            self.prompt_length = prompt_length
            self.device = device
            
            # Tokenize stop sequences for faster checking
            self.stop_sequence_token_ids = []
            for seq in stop_sequences:
                # Tokenize without adding special tokens
                tokens = tokenizer.encode(seq, add_special_tokens=False)
                self.stop_sequence_token_ids.append(tokens)
                
            # Sort by length (descending) for efficiency
            self.stop_sequence_token_ids.sort(key=len, reverse=True)
        
        def __call__(self, input_ids: torch.LongTensor, scores: torch.FloatTensor, **kwargs) -> bool:
            """
            Check if generation should stop based on the current input_ids.
            
            Args:
                input_ids: Current generated token IDs
                scores: Current token scores
                **kwargs: Additional arguments
                
            Returns:
                bool: True if generation should stop, False otherwise
            """
            if not self.stop_sequences:
                return False
                
            # Only look at the newly generated part (after prompt)
            if input_ids.shape[1] <= self.prompt_length:
                return False
                
            for batch_idx in range(input_ids.shape[0]):
                # Get generated text so far
                generated_ids = input_ids[batch_idx, self.prompt_length:]
                
                # Check if any stop sequence is at the end of the generated text
                for stop_ids in self.stop_sequence_token_ids:
                    stop_len = len(stop_ids)
                    
                    # Skip if generated text is shorter than stop sequence
                    if len(generated_ids) < stop_len:
                        continue
                        
                    # Check if the end of generated text matches stop sequence
                    if torch.all(generated_ids[-stop_len:] == torch.tensor(stop_ids, device=self.device)):
                        return True
                        
            return False

else:
    # Fallback implementation when transformers is not available
    class CustomStoppingCriteria:
        """Fallback implementation when transformers is not available."""
        
        def __init__(self, *args, **kwargs):
            logger.warning("CustomStoppingCriteria requires transformers library")
            
        def __call__(self, *args, **kwargs):
            return False

class TopPTopKLogitsWarper(LogitsProcessor):
    """
    Custom logits processor that combines top-p and top-k filtering with temperature.
    
    This class provides more nuanced control over the logits transformation
    than the standard HuggingFace implementations, allowing for more
    precise control over token sampling.
    """
    
    def __init__(
        self, 
        top_p: float = 0.9, 
        top_k: int = 50, 
        temperature: float = 0.7,
        min_tokens_to_keep: int = 1
    ):
        """
        Initialize the logits processor.
        
        Args:
            top_p: Top-p sampling parameter (nucleus sampling)
            top_k: Top-k sampling parameter
            temperature: Temperature for softening the distribution
            min_tokens_to_keep: Minimum number of tokens to keep
        """
        self.top_p = top_p
        self.top_k = top_k
        self.temperature = temperature
        self.min_tokens_to_keep = min_tokens_to_keep
    
    def __call__(self, input_ids: torch.LongTensor, scores: torch.FloatTensor) -> torch.FloatTensor:
        """
        Process the logits with combined top-p and top-k filtering.
        
        Args:
            input_ids: Current token IDs
            scores: Token scores/logits
            
        Returns:
            torch.FloatTensor: Processed logits
        """
        # Apply temperature first
        if self.temperature != 1.0:
            scores = scores / self.temperature
            
        # Apply combined top-p and top-k filtering
        filtered_scores = top_k_top_p_filtering(
            scores,
            top_k=self.top_k,
            top_p=self.top_p,
            min_tokens_to_keep=self.min_tokens_to_keep
        )
            
        return filtered_scores


class PresenceFrequencyLogitsProcessor(LogitsProcessor):
    """
    Logits processor that applies presence and frequency penalties similar to OpenAI's API.
    
    This allows for more fine-grained control over repetition in generated text,
    applying stronger penalties to frequently occurring tokens.
    """
    
    def __init__(
        self, 
        presence_penalty: float = 0.0, 
        frequency_penalty: float = 0.0
    ):
        """
        Initialize the logits processor.
        
        Args:
            presence_penalty: Penalty applied to tokens present in the text (0.0 = none)
            frequency_penalty: Penalty scaled by token frequency (0.0 = none)
        """
        self.presence_penalty = presence_penalty
        self.frequency_penalty = frequency_penalty
    
    def __call__(self, input_ids: torch.LongTensor, scores: torch.FloatTensor) -> torch.FloatTensor:
        """
        Process the logits with presence and frequency penalties.
        
        Args:
            input_ids: Current token IDs
            scores: Token scores/logits
            
        Returns:
            torch.FloatTensor: Processed logits
        """
        # Skip if no penalties are applied
        if self.presence_penalty == 0.0 and self.frequency_penalty == 0.0:
            return scores
            
        # Calculate token frequency for each sequence in the batch
        batch_size, seq_length = input_ids.shape
        
        for batch_idx in range(batch_size):
            # Count token frequencies
            token_frequencies = {}
            for token_id in input_ids[batch_idx]:
                token_id_item = token_id.item()
                if token_id_item not in token_frequencies:
                    token_frequencies[token_id_item] = 0
                token_frequencies[token_id_item] += 1
                
            # Apply penalties to scores
            for token_id, freq in token_frequencies.items():
                # Apply presence penalty (penalize all tokens that appear at least once)
                if self.presence_penalty != 0.0:
                    scores[batch_idx, token_id] -= self.presence_penalty
                    
                # Apply frequency penalty (scaled by frequency)
                if self.frequency_penalty != 0.0:
                    scores[batch_idx, token_id] -= self.frequency_penalty * freq
        
        return scores


class ContextualLogitsProcessor(LogitsProcessor):
    """
    Logits processor that applies contextual adjustments based on semantic coherence.
    
    This processor boosts or penalizes tokens based on their semantic fit with the
    context, improving coherence and relevance of generated text.
    """
    
    def __init__(
        self, 
        tokenizer: PreTrainedTokenizer,
        context_embedding: Optional[torch.Tensor] = None,
        context_keywords: Optional[List[str]] = None,
        semantic_model = None,
        boost_factor: float = 1.0,
        semantic_threshold: float = 0.6
    ):
        """
        Initialize the contextual logits processor.
        
        Args:
            tokenizer: The tokenizer for decoding token IDs
            context_embedding: Optional precomputed context embedding
            context_keywords: Optional list of key concepts/terms to boost
            semantic_model: Optional model for semantic similarity calculation
            boost_factor: Factor for boosting/penalizing tokens
            semantic_threshold: Threshold for semantic similarity
        """
        self.tokenizer = tokenizer
        self.context_embedding = context_embedding
        self.context_keywords = context_keywords or []
        self.semantic_model = semantic_model
        self.boost_factor = boost_factor
        self.semantic_threshold = semantic_threshold
        
        # Cache for token embeddings to avoid recomputing
        self.token_embedding_cache = {}
        
        # Tokenize context keywords for faster matching
        self.context_keyword_ids = []
        if self.context_keywords:
            for keyword in self.context_keywords:
                keyword_ids = tokenizer.encode(keyword, add_special_tokens=False)
                self.context_keyword_ids.append(keyword_ids)
    
    def __call__(self, input_ids: torch.LongTensor, scores: torch.FloatTensor) -> torch.FloatTensor:
        """
        Process the logits based on contextual relevance.
        
        Args:
            input_ids: Current token IDs
            scores: Token scores/logits
            
        Returns:
            torch.FloatTensor: Processed logits
        """
        # Skip if no context embedding or keywords are provided
        if self.context_embedding is None and not self.context_keywords:
            return scores
            
        # Get the most recent tokens for context window
        context_window = 50  # Number of recent tokens to consider
        recent_tokens = input_ids[:, -min(context_window, input_ids.shape[1]):]
        
        batch_size, vocab_size = scores.shape
        
        for batch_idx in range(batch_size):
            # Process with semantic model if available
            if self.semantic_model is not None and self.context_embedding is not None:
                # Get top candidate tokens
                top_k = 100  # Number of top tokens to evaluate semantically
                top_indices = torch.topk(scores[batch_idx], k=top_k).indices
                
                for token_idx in top_indices:
                    token_idx_item = token_idx.item()
                    
                    # Skip if token is already in cache
                    if token_idx_item in self.token_embedding_cache:
                        similarity = self.token_embedding_cache[token_idx_item]
                    else:
                        # Decode token to text
                        token_text = self.tokenizer.decode([token_idx_item])
                        
                        # Get embedding and calculate similarity
                        try:
                            token_embedding = self.semantic_model.encode(token_text, convert_to_tensor=True)
                            similarity = F.cosine_similarity(
                                token_embedding.unsqueeze(0),
                                self.context_embedding.unsqueeze(0)
                            ).item()
                            self.token_embedding_cache[token_idx_item] = similarity
                        except Exception as e:
                            logger.warning(f"Error computing semantic similarity: {e}")
                            similarity = 0.5  # Neutral value on error
                    
                    # Apply boost/penalty based on similarity
                    if similarity > self.semantic_threshold:
                        boost = (similarity - self.semantic_threshold) * self.boost_factor
                        scores[batch_idx, token_idx] += boost
                    elif similarity < self.semantic_threshold - 0.1:  # Apply penalty with a gap
                        penalty = (self.semantic_threshold - similarity) * self.boost_factor * 0.5  # Smaller penalty
                        scores[batch_idx, token_idx] -= penalty
            
            # Boost scores for tokens that would continue context keywords
            if self.context_keywords:
                for keyword_ids in self.context_keyword_ids:
                    keyword_len = len(keyword_ids)
                    
                    # Check if we're in the middle of generating a keyword
                    for start_pos in range(1, keyword_len):
                        # Not enough tokens to match
                        if start_pos > len(recent_tokens[batch_idx]):
                            continue
                            
                        # Check if recent tokens match the start of a keyword
                        prefix_match = True
                        for i in range(start_pos):
                            if recent_tokens[batch_idx, -start_pos + i].item() != keyword_ids[i]:
                                prefix_match = False
                                break
                                
                        if prefix_match:
                            # Boost the next token in the keyword
                            next_token_id = keyword_ids[start_pos]
                            scores[batch_idx, next_token_id] += self.boost_factor
        
        return scores


class DocumentStructureProcessor:
    """
    Processor for handling document structure in text generation.
    
    This class implements different document structure formats and aids the
    TextGenerator in formatting the output according to the desired structure.
    """
    
    def __init__(
        self,
        structure_type: str = "auto",
        tokenizer: Optional[PreTrainedTokenizer] = None
    ):
        """
        Initialize the document structure processor.
        
        Args:
            structure_type: Type of document structure
            tokenizer: Optional tokenizer for token-level structure
        """
        self.structure_type = structure_type.lower()
        self.tokenizer = tokenizer
        
        # Define structure templates
        self.structure_templates = {
            "paragraph": {
                "intro": "\n\n",
                "section": "\n\n",
                "point": "",
                "conclusion": "\n\n"
            },
            "bullet": {
                "intro": "\n\n",
                "section": "\n\n",
                "point": "• ",
                "conclusion": "\n\n"
            },
            "numbered": {
                "intro": "\n\n",
                "section": "\n\n",
                "point": "{index}. ",
                "conclusion": "\n\n"
            },
            "section": {
                "intro": "\n\n## Introduction\n\n",
                "section": "\n\n## {title}\n\n",
                "point": "",
                "conclusion": "\n\n## Conclusion\n\n"
            },
            "qa": {
                "intro": "\n\n",
                "section": "\n\n**Q: {title}**\n\n",
                "point": "",
                "conclusion": "\n\n"
            }
        }
        
        # Token IDs for structure elements if tokenizer is provided
        self.structure_token_ids = {}
        if self.tokenizer is not None:
            for structure_type, templates in self.structure_templates.items():
                self.structure_token_ids[structure_type] = {
                    k: self.tokenizer.encode(v, add_special_tokens=False)
                    for k, v in templates.items() if v
                }
    
    def determine_structure(self, query: str) -> str:
        """
        Determine the most appropriate document structure for a query.
        
        Args:
            query: The query or request text
            
        Returns:
            str: The determined structure type
        """
        if self.structure_type != "auto":
            return self.structure_type
            
        # Analyze query for structure indicators
        query_lower = query.lower()
        
        # Check for explicit structure requests
        if re.search(r'(list|bullet|point|points|items|steps)', query_lower):
            return "bullet"
            
        if re.search(r'(number|numbered|steps in order|ordered list)', query_lower):
            return "numbered"
            
        if re.search(r'(section|chapter|divide|organize into)', query_lower):
            return "section"
            
        if re.search(r'(question|answer|interview|q&a|q & a)', query_lower):
            return "qa"
            
        # Default to paragraph for longer generations, bullets for lists
        if re.search(r'(explain|describe|tell me about|write|essay)', query_lower):
            return "paragraph"
            
        # Check query complexity
        sentence_count = len(re.split(r'[.!?]+', query))
        if sentence_count > 3 or len(query.split()) > 30:
            return "section"  # More structured for complex queries
            
        # Default to paragraph
        return "paragraph"
    
    def get_structure_template(self, structure_type: str, section_index: int = 0, section_title: str = "") -> Dict[str, str]:
        """
        Get the structure template for a given structure type.
        
        Args:
            structure_type: Type of document structure
            section_index: Index of current section (for numbered structures)
            section_title: Title of current section (for titled structures)
            
        Returns:
            Dict[str, str]: Structure template elements
        """
        if structure_type not in self.structure_templates:
            structure_type = "paragraph"  # Default fallback
            
        # Get the template
        template = deepcopy(self.structure_templates[structure_type])
        
        # Fill in dynamic elements
        if "{index}" in template.get("point", ""):
            template["point"] = template["point"].format(index=section_index+1)
            
        if "{title}" in template.get("section", ""):
            # Default title if none provided
            if not section_title:
                section_title = f"Section {section_index+1}"
                
            template["section"] = template["section"].format(title=section_title)
            
        return template
    
    def format_text_with_structure(
        self, 
        text: str, 
        structure_type: str,
        tokenize_paragraphs: bool = True
    ) -> str:
        """
        Format text according to the specified document structure.
        
        Args:
            text: Text to format
            structure_type: Type of document structure
            tokenize_paragraphs: Whether to tokenize text into paragraphs
            
        Returns:
            str: Formatted text
        """
        # Determine structure if auto
        if structure_type == "auto":
            structure_type = self.determine_structure("")
            
        # Get structure template
        template = self.get_structure_template(structure_type)
        
        # Split text into paragraphs if needed
        if tokenize_paragraphs:
            if _HAS_NLTK:
                paragraphs = []
                # Split into sentences first
                sentences = sent_tokenize(text)
                
                # Group sentences into paragraphs
                current_paragraph = []
                for sentence in sentences:
                    current_paragraph.append(sentence)
                    
                    # Heuristic: end paragraph after 3-5 sentences or on certain markers
                    if (len(current_paragraph) >= 4 or 
                        (len(current_paragraph) >= 2 and 
                         any(marker in sentence for marker in [".", "!", "?", ":", ";"]))):
                        paragraphs.append(" ".join(current_paragraph))
                        current_paragraph = []
                        
                # Add any remaining sentences
                if current_paragraph:
                    paragraphs.append(" ".join(current_paragraph))
            else:
                # Fallback to simple paragraph splitting
                paragraphs = re.split(r'\n\s*\n', text)
                
            paragraphs = [p.strip() for p in paragraphs if p.strip()]
        else:
            # Use the text as a single paragraph
            paragraphs = [text.strip()]
            
        # Format according to structure
        formatted_text = ""
        
        # Add intro if multiple paragraphs
        if len(paragraphs) > 1:
            formatted_text += template["intro"]
            
        # Process each paragraph
        for i, paragraph in enumerate(paragraphs):
            if i > 0:
                formatted_text += template["section"]
                
            # For bullet and numbered lists, apply point format to each sentence
            if structure_type in ["bullet", "numbered"] and len(paragraphs) > 1:
                if _HAS_NLTK:
                    sentences = sent_tokenize(paragraph)
                else:
                    sentences = re.split(r'[.!?]+\s+', paragraph)
                    
                for j, sentence in enumerate(sentences):
                    sentence = sentence.strip()
                    if sentence:
                        formatted_text += template["point"] + sentence + "\n"
            else:
                formatted_text += paragraph
                
        # Add conclusion for multi-paragraph texts
        if len(paragraphs) > 1:
            formatted_text += template["conclusion"]
            
        return formatted_text
    
    def get_structure_token_ids(self, structure_type: str, element: str) -> List[int]:
        """
        Get token IDs for a structure element.
        
        Args:
            structure_type: Type of document structure
            element: Structure element name
            
        Returns:
            List[int]: Token IDs for the structure element
        """
        if not self.tokenizer:
            return []
            
        if structure_type not in self.structure_token_ids:
            structure_type = "paragraph"  # Default fallback
            
        return self.structure_token_ids[structure_type].get(element, [])


class PromptEngineering:
    """
    Comprehensive prompt engineering system for ULTRA's text generation.
    
    This class implements various prompt engineering strategies to enhance
    text generation quality, including chain-of-thought prompting, few-shot
    learning, role-based prompting, and more.
    """
    
    def __init__(
        self,
        strategies: List[str] = ["cot", "examples"],
        system_prompt: Optional[str] = None,
        tokenizer: Optional[PreTrainedTokenizer] = None,
        max_length: int = 2048
    ):
        """
        Initialize the prompt engineering system.
        
        Args:
            strategies: List of prompt strategies to use
            system_prompt: Optional system prompt
            tokenizer: Optional tokenizer for length estimation
            max_length: Maximum prompt length
        """
        self.strategies = [s.lower() for s in strategies]
        self.system_prompt = system_prompt
        self.tokenizer = tokenizer
        self.max_length = max_length
        
        # Standard prompts for different strategies
        self.strategy_prompts = {
            "cot": "Let's think step by step.",
            "step_by_step": "I'll solve this step by step:",
            "examples": "",  # Will be populated with few-shot examples
            "persona": "I am an expert in this field with deep knowledge and experience.",
            "reframing": "Let me reframe this problem to make it clearer:",
            "preamble": "This is an important question that requires careful analysis."
        }
        
        # Few-shot examples
        self.few_shot_examples = []
        
        # Specialized prompts for different tasks
        self.task_prompts = {
            "explanation": {
                "cot": "Let me explain this concept step by step.",
                "persona": "As an expert in this field, I can explain that"
            },
            "analysis": {
                "cot": "I'll analyze this by considering key factors one by one.",
                "persona": "With my analytical expertise, I can break this down as follows:"
            },
            "comparison": {
                "cot": "To compare these items, I'll evaluate them across several dimensions.",
                "persona": "Based on my experience with comparative analysis:"
            },
            "creative": {
                "cot": "I'll develop this creative idea by exploring different elements.",
                "persona": "As a creative thinker, I approach this by:"
            },
            "instruction": {
                "cot": "Let me provide these instructions in clear sequential steps.",
                "persona": "Drawing on my practical expertise, I recommend:"
            }
        }
    
    def set_few_shot_examples(self, examples: List[Dict[str, str]]):
        """
        Set few-shot examples for example-based prompting.
        
        Args:
            examples: List of examples (dict with 'input' and 'output' keys)
        """
        self.few_shot_examples = examples
    
    def detect_task_type(self, query: str) -> str:
        """
        Detect the type of task from the query.
        
        Args:
            query: Query text
            
        Returns:
            str: Detected task type
        """
        query_lower = query.lower()
        
        # Detect task type based on keywords
        if re.search(r'(explain|description|clarify|what is|how does)', query_lower):
            return "explanation"
            
        if re.search(r'(analyze|analysis|examine|assess|evaluate)', query_lower):
            return "analysis"
            
        if re.search(r'(compare|contrast|difference|similarity|versus|vs)', query_lower):
            return "comparison"
            
        if re.search(r'(create|write|generate|design|story|novel|poem)', query_lower):
            return "creative"
            
        if re.search(r'(how to|steps|guide|instructions|tutorial)', query_lower):
            return "instruction"
            
        # Default to explanation
        return "explanation"
    
    def generate_prompt(
        self,
        query: str,
        context: Optional[Dict[str, Any]] = None,
        meta_data: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Generate a complete prompt using the selected strategies.
        
        Args:
            query: User query or input
            context: Optional context information
            meta_data: Optional metadata for prompt generation
            
        Returns:
            str: Complete engineered prompt
        """
        # Initialize prompt components
        components = []
        
        # Add system prompt if available
        if self.system_prompt:
            components.append({"role": "system", "content": self.system_prompt})
            
        # Detect task type
        task_type = meta_data.get("task_type") if meta_data else None
        if not task_type:
            task_type = self.detect_task_type(query)
            
        # Track estimated length
        estimated_length = len(self.tokenize_text(self.system_prompt or ""))
        
        # Track used strategies
        used_strategies = []
        
        # Add strategy-specific components
        for strategy in self.strategies:
            if strategy == "examples" and self.few_shot_examples:
                # Calculate available length for examples
                example_text = self.format_few_shot_examples(self.few_shot_examples)
                example_length = len(self.tokenize_text(example_text))
                
                if estimated_length + example_length < self.max_length:
                    components.append({"role": "user", "content": example_text})
                    estimated_length += example_length
                    used_strategies.append(strategy)
                    
            elif strategy in self.strategy_prompts:
                # Get strategy prompt, preferring task-specific version if available
                if task_type in self.task_prompts and strategy in self.task_prompts[task_type]:
                    strategy_prompt = self.task_prompts[task_type][strategy]
                else:
                    strategy_prompt = self.strategy_prompts[strategy]
                    
                # Apply the strategy if we have a prompt for it
                if strategy_prompt:
                    strategy_length = len(self.tokenize_text(strategy_prompt))
                    
                    if estimated_length + strategy_length < self.max_length:
                        components.append({"role": "user", "content": strategy_prompt})
                        estimated_length += strategy_length
                        used_strategies.append(strategy)
                        
        # Include context information if available
        if context and "reference_text" in context:
            ref_text = context["reference_text"]
            ref_length = len(self.tokenize_text(ref_text))
            
            # Add reference if it fits
            if estimated_length + ref_length < self.max_length:
                components.append({
                    "role": "user", 
                    "content": f"Reference information: {ref_text}"
                })
                estimated_length += ref_length
                
        # Add the query itself (must fit)
        query_length = len(self.tokenize_text(query))
        if estimated_length + query_length >= self.max_length:
            # Truncate query if necessary (rare case)
            query = self.truncate_text(query, self.max_length - estimated_length)
            
        components.append({"role": "user", "content": query})
        
        # Format final prompt
        if len(components) == 1:
            # Just the query
            final_prompt = query
        else:
            # Format multi-component prompt
            final_prompt = self.format_prompt_components(components)
            
        return final_prompt
    
    def format_few_shot_examples(self, examples: List[Dict[str, str]]) -> str:
        """
        Format few-shot examples for inclusion in prompts.
        
        Args:
            examples: List of examples
            
        Returns:
            str: Formatted examples text
        """
        if not examples:
            return ""
            
        # Format the examples
        formatted = "Here are some examples:\n\n"
        
        for i, example in enumerate(examples):
            # Extract input and output
            ex_input = example.get("input", "")
            ex_output = example.get("output", "")
            
            if not ex_input or not ex_output:
                continue
                
            # Add formatted example
            formatted += f"Example {i+1}:\n"
            formatted += f"Input: {ex_input}\n"
            formatted += f"Output: {ex_output}\n\n"
            
        formatted += "Please use a similar approach for the following input:\n"
        
        return formatted
    
    def format_prompt_components(self, components: List[Dict[str, str]]) -> str:
        """
        Format prompt components into a complete prompt.
        
        Args:
            components: List of prompt components
            
        Returns:
            str: Formatted prompt
        """
        formatted = ""
        
        for component in components:
            role = component.get("role", "user")
            content = component.get("content", "")
            
            if role == "system":
                formatted += f"System: {content}\n\n"
            elif role == "user":
                formatted += f"{content}\n\n"
            elif role == "assistant":
                formatted += f"Assistant: {content}\n\n"
            else:
                formatted += f"{content}\n\n"
                
        return formatted.strip()
    
    def tokenize_text(self, text: str) -> List[str]:
        """
        Tokenize text for length estimation.
        
        Args:
            text: Text to tokenize
            
        Returns:
            List[str]: Tokenized text
        """
        if not text:
            return []
            
        if self.tokenizer:
            # Use provided tokenizer
            return self.tokenizer.tokenize(text)
        elif _HAS_NLTK:
            # Fall back to NLTK word tokenization
            return word_tokenize(text)
        else:
            # Simple whitespace tokenization
            return text.split()
    
    def truncate_text(self, text: str, max_tokens: int) -> str:
        """
        Truncate text to fit within max_tokens.
        
        Args:
            text: Text to truncate
            max_tokens: Maximum number of tokens
            
        Returns:
            str: Truncated text
        """
        if not text:
            return ""
            
        tokens = self.tokenize_text(text)
        
        if len(tokens) <= max_tokens:
            return text
            
        if self.tokenizer:
            # Use tokenizer to decode truncated tokens
            return self.tokenizer.decode(self.tokenizer.encode(text)[:max_tokens])
        else:
            # Simple truncation based on word tokens
            return " ".join(tokens[:max_tokens])


class GuidanceController:
    """
    Controller for guiding text generation with specific constraints and criteria.
    
    This class enables steering generation to follow specific criteria, maintain
    a particular style, ensure fact accuracy, and constrain outputs to be consistent
    with provided knowledge.
    """
    
    def __init__(
        self,
        tokenizer: Optional[PreTrainedTokenizer] = None,
        guidance_scale: float = 1.0,
        criteria: Optional[List[str]] = None,
        knowledge_base: Optional[Dict[str, Any]] = None
    ):
        """
        Initialize the guidance controller.
        
        Args:
            tokenizer: Optional tokenizer for token manipulation
            guidance_scale: Scale for guidance strength
            criteria: List of guidance criteria to apply
            knowledge_base: Optional structured knowledge for factual guidance
        """
        self.tokenizer = tokenizer
        self.guidance_scale = guidance_scale
        self.criteria = criteria or ["relevance", "coherence", "factuality"]
        self.knowledge_base = knowledge_base or {}
        
        # Initialize similarity model if available
        self.similarity_model = None
        if _HAS_SENTENCE_TRANSFORMERS:
            try:
                self.similarity_model = SentenceTransformer('all-MiniLM-L6-v2')
                logger.debug("Initialized similarity model for guidance")
            except Exception as e:
                logger.warning(f"Failed to initialize similarity model: {e}")
                
        # Cache for generated text evaluation
        self.evaluation_cache = {}
    
    def generate_guidance_signal(
        self,
        generated_text: str,
        prompt: str,
        criterion: str
    ) -> float:
        """
        Generate a guidance signal for a given criterion.
        
        Args:
            generated_text: Currently generated text
            prompt: Original prompt
            criterion: Guidance criterion
            
        Returns:
            float: Guidance signal strength (-1.0 to 1.0)
        """
        # Check cache first
        cache_key = f"{hash(generated_text)}_{criterion}"
        if cache_key in self.evaluation_cache:
            return self.evaluation_cache[cache_key]
            
        # Initialize signal
        signal = 0.0
        
        # Generate signal based on criterion
        if criterion == "relevance":
            signal = self._evaluate_relevance(generated_text, prompt)
        elif criterion == "coherence":
            signal = self._evaluate_coherence(generated_text)
        elif criterion == "factuality":
            signal = self._evaluate_factuality(generated_text)
        elif criterion == "conciseness":
            signal = self._evaluate_conciseness(generated_text)
        elif criterion == "specificity":
            signal = self._evaluate_specificity(generated_text)
        elif criterion == "creativity":
            signal = self._evaluate_creativity(generated_text)
        else:
            # Unknown criterion
            signal = 0.0
            
        # Cache the result
        self.evaluation_cache[cache_key] = signal
        
        return signal
    
    def apply_guidance(
        self,
        scores: torch.FloatTensor,
        input_ids: torch.LongTensor,
        prompt: str
    ) -> torch.FloatTensor:
        """
        Apply guidance to token scores during generation.
        
        Args:
            scores: Current token scores
            input_ids: Current sequence of token IDs
            prompt: Original prompt
            
        Returns:
            torch.FloatTensor: Adjusted token scores
        """
        if self.guidance_scale == 0.0 or not self.criteria:
            return scores
            
        # Get generated text so far
        if self.tokenizer is None:
            return scores
            
        generated_text = self.tokenizer.decode(input_ids[0])
        
        # Apply guidance for each criterion
        for criterion in self.criteria:
            guidance_signal = self.generate_guidance_signal(generated_text, prompt, criterion)
            
            # Skip if signal is neutral
            if abs(guidance_signal) < 0.1:
                continue
                
            # Apply guidance to scores based on criterion and signal
            scores = self._apply_criterion_guidance(scores, input_ids, criterion, guidance_signal)
            
        return scores
    
    def _apply_criterion_guidance(
        self,
        scores: torch.FloatTensor,
        input_ids: torch.LongTensor,
        criterion: str,
        signal: float
    ) -> torch.FloatTensor:
        """
        Apply guidance for a specific criterion.
        
        Args:
            scores: Current token scores
            input_ids: Current sequence of token IDs
            criterion: Guidance criterion
            signal: Guidance signal strength
            
        Returns:
            torch.FloatTensor: Adjusted token scores
        """
        # Scale the signal by the guidance scale
        scaled_signal = signal * self.guidance_scale
        
        # Different application strategies based on criterion
        if criterion == "relevance":
            scores = self._apply_relevance_guidance(scores, input_ids, scaled_signal)
        elif criterion == "coherence":
            scores = self._apply_coherence_guidance(scores, input_ids, scaled_signal)
        elif criterion == "factuality":
            scores = self._apply_factuality_guidance(scores, input_ids, scaled_signal)
        elif criterion == "conciseness":
            scores = self._apply_conciseness_guidance(scores, input_ids, scaled_signal)
        elif criterion == "specificity":
            scores = self._apply_specificity_guidance(scores, input_ids, scaled_signal)
        elif criterion == "creativity":
            scores = self._apply_creativity_guidance(scores, input_ids, scaled_signal)
            
        return scores
    
    def _evaluate_relevance(self, generated_text: str, prompt: str) -> float:
        """
        Evaluate relevance of generated text to prompt.
        
        Args:
            generated_text: Generated text
            prompt: Original prompt
            
        Returns:
            float: Relevance score (-1.0 to 1.0)
        """
        # Use semantic similarity if available
        if self.similarity_model is not None:
            try:
                # Embed texts
                gen_embedding = self.similarity_model.encode(generated_text, convert_to_tensor=True)
                prompt_embedding = self.similarity_model.encode(prompt, convert_to_tensor=True)
                
                # Calculate cosine similarity
                similarity = F.cosine_similarity(
                    gen_embedding.unsqueeze(0),
                    prompt_embedding.unsqueeze(0)
                ).item()
                
                # Scale to -1.0 to 1.0 (mapping 0 -> -1, 0.5 -> 0, 1 -> 1)
                return (similarity * 2) - 1.0
                
            except Exception as e:
                logger.warning(f"Error in semantic similarity calculation: {e}")
                
        # Fall back to keyword-based relevance
        prompt_words = set(re.findall(r'\b\w+\b', prompt.lower()))
        gen_words = set(re.findall(r'\b\w+\b', generated_text.lower()))
        
        # Filter out common stop words
        stop_words = {'the', 'a', 'an', 'and', 'of', 'to', 'is', 'in', 'that', 'it', 'for'}
        prompt_words = prompt_words - stop_words
        gen_words = gen_words - stop_words
        
        # Calculate overlap
        if not prompt_words:
            return 0.0
            
        overlap = len(prompt_words.intersection(gen_words)) / len(prompt_words)
        
        # Scale to -1.0 to 1.0
        return (overlap * 2) - 1.0
    
    def _evaluate_coherence(self, generated_text: str) -> float:
        """
        Evaluate coherence of generated text.
        
        Args:
            generated_text: Generated text
            
        Returns:
            float: Coherence score (-1.0 to 1.0)
        """
        # Split into sentences for analysis
        if _HAS_NLTK:
            sentences = sent_tokenize(generated_text)
        else:
            sentences = re.split(r'[.!?]+\s+', generated_text)
            
        # Not enough sentences for coherence analysis
        if len(sentences) < 2:
            return 0.0
            
        # Use semantic similarity between adjacent sentences if available
        if self.similarity_model is not None:
            try:
                # Calculate pairwise similarities
                similarities = []
                embeddings = self.similarity_model.encode(sentences, convert_to_tensor=True)
                
                for i in range(len(sentences) - 1):
                    similarity = F.cosine_similarity(
                        embeddings[i].unsqueeze(0),
                        embeddings[i+1].unsqueeze(0)
                    ).item()
                    similarities.append(similarity)
                    
                # Average similarity as coherence measure
                avg_similarity = sum(similarities) / len(similarities)
                
                # Scale to -1.0 to 1.0 (mapping 0 -> -1, 0.5 -> 0, 1 -> 1)
                return (avg_similarity * 2) - 1.0
                
            except Exception as e:
                logger.warning(f"Error in coherence calculation: {e}")
                
        # Fall back to simple lexical cohesion
        cohesion_score = 0.0
        
        for i in range(len(sentences) - 1):
            sent1_words = set(re.findall(r'\b\w+\b', sentences[i].lower()))
            sent2_words = set(re.findall(r'\b\w+\b', sentences[i+1].lower()))
            
            # Calculate word overlap
            if sent1_words and sent2_words:
                overlap = len(sent1_words.intersection(sent2_words)) / max(len(sent1_words), len(sent2_words))
                cohesion_score += overlap
                
        # Average cohesion
        avg_cohesion = cohesion_score / (len(sentences) - 1) if len(sentences) > 1 else 0.0
        
        # Scale to -1.0 to 1.0
        return (avg_cohesion * 2) - 1.0
    
    def _evaluate_factuality(self, generated_text: str) -> float:
        """
        Evaluate factuality of generated text.
        
        Args:
            generated_text: Generated text
            
        Returns:
            float: Factuality score (-1.0 to 1.0)
        """
        # Without external knowledge verification, use internal knowledge base
        if not self.knowledge_base:
            return 0.0  # Neutral if no knowledge base
            
        # Extract factual claims from text
        facts = self._extract_facts(generated_text)
        if not facts:
            return 0.0  # No facts to verify
            
        # Match against knowledge base
        matched_facts = 0
        contradicted_facts = 0
        
        for fact in facts:
            # Check for matches in knowledge base
            match_found = False
            contradiction_found = False
            
            for kb_fact, kb_value in self.knowledge_base.items():
                # Calculate similarity with fact
                if self.similarity_model is not None:
                    try:
                        fact_embedding = self.similarity_model.encode(fact, convert_to_tensor=True)
                        kb_embedding = self.similarity_model.encode(kb_fact, convert_to_tensor=True)
                        
                        similarity = F.cosine_similarity(
                            fact_embedding.unsqueeze(0),
                            kb_embedding.unsqueeze(0)
                        ).item()
                        
                        # Consider it a match if similarity is high
                        if similarity > 0.85:
                            match_found = True
                            break
                            
                        # Consider it a contradiction if moderate similarity but opposite value
                        if similarity > 0.7 and kb_value is not None and isinstance(kb_value, bool) and not kb_value:
                            contradiction_found = True
                            break
                            
                    except Exception:
                        pass
                        
                # Fallback to simple string matching
                if kb_fact.lower() in fact.lower() or fact.lower() in kb_fact.lower():
                    match_found = True
                    break
                    
            if match_found:
                matched_facts += 1
            elif contradiction_found:
                contradicted_facts += 1
                
        # Calculate factuality score
        total_facts = len(facts)
        if total_facts == 0:
            return 0.0
            
        factuality = (matched_facts - contradicted_facts) / total_facts
        
        # Scale to -1.0 to 1.0
        return factuality
    
    def _extract_facts(self, text: str) -> List[str]:
        """
        Extract factual claims from text.
        
        Args:
            text: Input text
            
        Returns:
            List[str]: Extracted factual claims
        """
        facts = []
        
        # Split into sentences
        if _HAS_NLTK:
            sentences = sent_tokenize(text)
        else:
            sentences = re.split(r'[.!?]+\s+', text)
            
        # Identify fact-like sentences
        fact_markers = [
            r'\bis\b', r'\bare\b', r'\bwas\b', r'\bwere\b', r'\bhave\b', r'\bhas\b',
            r'\bconsists\b', r'\bcomprises\b', r'\bincludes\b', r'\bcontains\b',
            r'\bequals\b', r'\bamounts\b', r'\boccurred\b'
        ]
        
        for sentence in sentences:
            # Skip speculative or uncertain statements
            if re.search(r'\bmight\b|\bcould\b|\bperhaps\b|\bmaybe\b|\bpossibly\b', sentence, re.IGNORECASE):
                continue
                
            # Check for fact markers
            if any(re.search(marker, sentence, re.IGNORECASE) for marker in fact_markers):
                facts.append(sentence)
                
        return facts
    
    def _evaluate_conciseness(self, generated_text: str) -> float:
        """
        Evaluate conciseness of generated text.
        
        Args:
            generated_text: Generated text
            
        Returns:
            float: Conciseness score (-1.0 to 1.0)
        """
        # Count words and sentences
        if _HAS_NLTK:
            words = word_tokenize(generated_text)
            sentences = sent_tokenize(generated_text)
        else:
            words = generated_text.split()
            sentences = re.split(r'[.!?]+\s+', generated_text)
            
        if not sentences:
            return 0.0
            
        # Calculate average words per sentence
        avg_words = len(words) / len(sentences)
        
        # Ideal range for conciseness (10-25 words per sentence)
        if avg_words < 10:
            # Too concise, may lack detail
            return (avg_words / 10) - 1.0
        elif avg_words <= 25:
            # Good conciseness
            return 1.0 - ((avg_words - 10) / 15)
        else:
            # Too verbose
            return -1.0 * min(1.0, (avg_words - 25) / 25)
    
    def _evaluate_specificity(self, generated_text: str) -> float:
        """
        Evaluate specificity of generated text.
        
        Args:
            generated_text: Generated text
            
        Returns:
            float: Specificity score (-1.0 to 1.0)
        """
        # Count specific terms (numbers, named entities, technical terms)
        specific_markers = [
            # Numbers
            r'\b\d+\b', r'\b\d+\.\d+\b', 
            # Dates
            r'\b\d{1,2}[/-]\d{1,2}[/-]\d{2,4}\b', 
            r'\b(January|February|March|April|May|June|July|August|September|October|November|December)\b',
            # Technical terms
            r'\b[A-Z][a-z]+[A-Z][a-z]+\b',  # CamelCase
            r'\b[A-Z]{2,}\b',  # Acronyms
            # Named entities (simple heuristic)
            r'\b[A-Z][a-z]+\s+[A-Z][a-z]+\b',  # Proper names
        ]
        
        specific_count = 0
        for marker in specific_markers:
            specific_count += len(re.findall(marker, generated_text))
            
        # Get total words
        if _HAS_NLTK:
            words = word_tokenize(generated_text)
        else:
            words = generated_text.split()
            
        if not words:
            return 0.0
            
        # Calculate specificity ratio
        specificity_ratio = specific_count / len(words)
        
        # Scale to -1.0 to 1.0
        if specificity_ratio < 0.05:
            # Too general
            return -1.0 + (specificity_ratio * 20)
        elif specificity_ratio <= 0.15:
            # Good specificity
            return (specificity_ratio - 0.05) * 10
        else:
            # Too specific (might be over-detailed)
            return 1.0 - min(1.0, (specificity_ratio - 0.15) * 5)
    
    def _evaluate_creativity(self, generated_text: str) -> float:
        """
        Evaluate creativity of generated text.
        
        Args:
            generated_text: Generated text
            
        Returns:
            float: Creativity score (-1.0 to 1.0)
        """
        # Use linguistic diversity as a proxy for creativity
        if _HAS_NLTK:
            words = word_tokenize(generated_text.lower())
        else:
            words = generated_text.lower().split()
            
        if not words:
            return 0.0
            
        # Calculate lexical diversity (unique words / total words)
        unique_words = len(set(words))
        total_words = len(words)
        
        type_token_ratio = unique_words / total_words
        
        # Calculate rare word usage
        common_words = {'the', 'be', 'to', 'of', 'and', 'a', 'in', 'that', 'have', 'i',
                      'it', 'for', 'not', 'on', 'with', 'he', 'as', 'you', 'do', 'at',
                      'this', 'but', 'his', 'by', 'from', 'they', 'we', 'say', 'her', 'she',
                      'or', 'an', 'will', 'my', 'one', 'all', 'would', 'there', 'their'}
        
        rare_words = len([w for w in set(words) if w not in common_words])
        rare_word_ratio = rare_words / total_words if total_words > 0 else 0
        
        # Combine metrics
        creativity_score = (type_token_ratio * 0.6) + (rare_word_ratio * 0.4)
        
        # Scale to -1.0 to 1.0
        if creativity_score < 0.3:
            # Low creativity
            return -1.0 + (creativity_score * 3.33)
        elif creativity_score <= 0.6:
            # Moderate creativity
            return (creativity_score - 0.3) * 3.33 - 1.0
        else:
            # High creativity
            return 1.0
    
    def _apply_relevance_guidance(
        self, 
        scores: torch.FloatTensor,
        input_ids: torch.LongTensor,
        signal: float
    ) -> torch.FloatTensor:
        """
        Apply relevance guidance to token scores.
        
        Args:
            scores: Current token scores
            input_ids: Current sequence of token IDs
            signal: Guidance signal strength
            
        Returns:
            torch.FloatTensor: Adjusted token scores
        """
        # Skip if tokenizer is not available
        if self.tokenizer is None:
            return scores
            
        # Boost tokens that are related to the prompt
        if signal > 0:
            # Extract topic-related tokens
            topic_tokens = self._get_topic_tokens(input_ids)
            
            if topic_tokens:
                # Boost scores for topic-related tokens
                for token_id in topic_tokens:
                    if token_id < scores.shape[1]:
                        scores[:, token_id] += signal * 2.0
        else:
            # Penalize tokens that might lead to tangents
            potential_tangent_tokens = self._get_potential_tangent_tokens(input_ids)
            
            if potential_tangent_tokens:
                # Penalize scores for tangent-related tokens
                for token_id in potential_tangent_tokens:
                    if token_id < scores.shape[1]:
                        scores[:, token_id] += signal * 1.0  # Using negative signal
                        
        return scores
    
    def _apply_coherence_guidance(
        self, 
        scores: torch.FloatTensor,
        input_ids: torch.LongTensor,
        signal: float
    ) -> torch.FloatTensor:
        """
        Apply coherence guidance to token scores.
        
        Args:
            scores: Current token scores
            input_ids: Current sequence of token IDs
            signal: Guidance signal strength
            
        Returns:
            torch.FloatTensor: Adjusted token scores
        """
        # Skip if tokenizer is not available
        if self.tokenizer is None:
            return scores
            
        # Identify recent words to maintain coherence
        recent_window = 50  # Last 50 tokens
        recent_tokens = input_ids[0, -min(recent_window, input_ids.shape[1]):].tolist()
        
        # Get token IDs for punctuation and function words
        function_tokens = self._get_function_tokens()
        
        if signal > 0:
            # Boost tokens that would maintain coherence
            coherent_tokens = self._get_coherent_tokens(recent_tokens)
            
            if coherent_tokens:
                # Boost scores for coherent tokens
                for token_id in coherent_tokens:
                    if token_id < scores.shape[1]:
                        scores[:, token_id] += signal * 1.5
                        
            # For strong positive signals, also boost function words
            if signal > 0.5 and function_tokens:
                for token_id in function_tokens:
                    if token_id < scores.shape[1]:
                        scores[:, token_id] += signal * 0.5
        else:
            # Penalize tokens that might break coherence
            incoherent_tokens = self._get_incoherent_tokens(recent_tokens)
            
            if incoherent_tokens:
                # Penalize scores for incoherent tokens
                for token_id in incoherent_tokens:
                    if token_id < scores.shape[1]:
                        scores[:, token_id] += signal * 1.5  # Using negative signal
                        
        return scores
    
    def _apply_factuality_guidance(
        self, 
        scores: torch.FloatTensor,
        input_ids: torch.LongTensor,
        signal: float
    ) -> torch.FloatTensor:
        """
        Apply factuality guidance to token scores.
        
        Args:
            scores: Current token scores
            input_ids: Current sequence of token IDs
            signal: Guidance signal strength
            
        Returns:
            torch.FloatTensor: Adjusted token scores
        """
        # Skip if tokenizer is not available or no knowledge base
        if self.tokenizer is None or not self.knowledge_base:
            return scores
            
        # Get token IDs for factual and speculative language
        factual_tokens = self._get_factual_tokens()
        speculative_tokens = self._get_speculative_tokens()
        
        if signal > 0:
            # Boost tokens associated with factual language
            if factual_tokens:
                for token_id in factual_tokens:
                    if token_id < scores.shape[1]:
                        scores[:, token_id] += signal * 1.5
                        
            # Penalize tokens associated with speculative language
            if speculative_tokens:
                for token_id in speculative_tokens:
                    if token_id < scores.shape[1]:
                        scores[:, token_id] -= signal * 1.0
        else:
            # For negative signals (facts might be wrong), do the opposite
            if speculative_tokens:
                for token_id in speculative_tokens:
                    if token_id < scores.shape[1]:
                        scores[:, token_id] -= signal * 1.0  # Signal is already negative
                        
            # Penalize tokens associated with strong factual claims
            if factual_tokens:
                for token_id in factual_tokens:
                    if token_id < scores.shape[1]:
                        scores[:, token_id] += signal * 0.5  # Using negative signal
                        
        return scores
    
    def _apply_conciseness_guidance(
        self, 
        scores: torch.FloatTensor,
        input_ids: torch.LongTensor,
        signal: float
    ) -> torch.FloatTensor:
        """
        Apply conciseness guidance to token scores.
        
        Args:
            scores: Current token scores
            input_ids: Current sequence of token IDs
            signal: Guidance signal strength
            
        Returns:
            torch.FloatTensor: Adjusted token scores
        """
        # Skip if tokenizer is not available
        if self.tokenizer is None:
            return scores
            
        # Get token IDs for concise and verbose language
        concise_tokens = self._get_concise_tokens()
        verbose_tokens = self._get_verbose_tokens()
        
        if signal > 0:
            # Guidance toward conciseness
            if concise_tokens:
                for token_id in concise_tokens:
                    if token_id < scores.shape[1]:
                        scores[:, token_id] += signal * 1.5
                        
            if verbose_tokens:
                for token_id in verbose_tokens:
                    if token_id < scores.shape[1]:
                        scores[:, token_id] -= signal * 1.0
        else:
            # Guidance toward verbosity (negative signal)
            if verbose_tokens:
                for token_id in verbose_tokens:
                    if token_id < scores.shape[1]:
                        scores[:, token_id] -= signal * 1.0  # Signal is already negative
                        
        return scores
    
    def _apply_specificity_guidance(
        self, 
        scores: torch.FloatTensor,
        input_ids: torch.LongTensor,
        signal: float
    ) -> torch.FloatTensor:
        """
        Apply specificity guidance to token scores.
        
        Args:
            scores: Current token scores
            input_ids: Current sequence of token IDs
            signal: Guidance signal strength
            
        Returns:
            torch.FloatTensor: Adjusted token scores
        """
        # Skip if tokenizer is not available
        if self.tokenizer is None:
            return scores
            
        # Get token IDs for specific and general language
        specific_tokens = self._get_specific_tokens()
        general_tokens = self._get_general_tokens()
        
        if signal > 0:
            # Guidance toward specificity
            if specific_tokens:
                for token_id in specific_tokens:
                    if token_id < scores.shape[1]:
                        scores[:, token_id] += signal * 1.5
                        
            if general_tokens:
                for token_id in general_tokens:
                    if token_id < scores.shape[1]:
                        scores[:, token_id] -= signal * 0.7
        else:
            # Guidance toward generality (negative signal)
            if general_tokens:
                for token_id in general_tokens:
                    if token_id < scores.shape[1]:
                        scores[:, token_id] -= signal * 0.7  # Signal is already negative
                        
        return scores
    
    def _apply_creativity_guidance(
        self, 
        scores: torch.FloatTensor,
        input_ids: torch.LongTensor,
        signal: float
    ) -> torch.FloatTensor:
        """
        Apply creativity guidance to token scores.
        
        Args:
            scores: Current token scores
            input_ids: Current sequence of token IDs
            signal: Guidance signal strength
            
        Returns:
            torch.FloatTensor: Adjusted token scores
        """
        # Skip if tokenizer is not available
        if self.tokenizer is None:
            return scores
            
        # Get token IDs for creative and common language
        creative_tokens = self._get_creative_tokens()
        common_tokens = self._get_common_tokens()
        
        if signal > 0:
            # Guidance toward creativity
            if creative_tokens:
                for token_id in creative_tokens:
                    if token_id < scores.shape[1]:
                        scores[:, token_id] += signal * 1.8
                        
            # Penalize very common words
            if common_tokens:
                for token_id in common_tokens:
                    if token_id < scores.shape[1]:
                        scores[:, token_id] -= signal * 0.5
                        
            # Boost general diversity by flattening distribution slightly
            if signal > 0.5:
                scores = scores / (1.0 + signal * 0.2)
        else:
            # Guidance toward conventionality (negative signal)
            if common_tokens:
                for token_id in common_tokens:
                    if token_id < scores.shape[1]:
                        scores[:, token_id] -= signal * 0.5  # Signal is already negative
                        
        return scores
    
    def _get_topic_tokens(self, input_ids: torch.LongTensor) -> List[int]:
        """Extract tokens related to the main topic from input_ids."""
        # This is a placeholder implementation
        # In a real system, this would analyze the token sequence to identify
        # main topic entities and concepts
        return []
    
    def _get_potential_tangent_tokens(self, input_ids: torch.LongTensor) -> List[int]:
        """Identify tokens that might lead to tangential content."""
        # This is a placeholder implementation
        # In a real system, this would use contextual analysis to identify
        # tokens that could lead away from the main topic
        return []
    
    def _get_function_tokens(self) -> List[int]:
        """Get token IDs for common function words and punctuation."""
        if not self.tokenizer:
            return []
            
        # Common function words
        function_words = [
            "the", "a", "an", "and", "but", "or", "because", "if", "when",
            "that", "which", "who", "where", "how", "to", "in", "for", "from",
            "with", "by", "about", "as", "into", "like", "through", "after",
            "over", "between", "out", "against", "during", "without", "before",
            "under", "around", "among"
        ]
        
        # Punctuation
        punctuation = [".", ",", ";", ":", "?", "!"]
        
        # Get token IDs
        tokens = []
        for word in function_words + punctuation:
            token_ids = self.tokenizer.encode(word, add_special_tokens=False)
            tokens.extend(token_ids)
            
        return list(set(tokens))  # Remove duplicates
    
    def _get_coherent_tokens(self, recent_tokens: List[int]) -> List[int]:
        """Get tokens that would maintain coherence with recent tokens."""
        # This is a simplified implementation
        # In a real system, this would use semantic analysis to identify
        # tokens that are semantically related to recent context
        return []
    
    def _get_incoherent_tokens(self, recent_tokens: List[int]) -> List[int]:
        """Get tokens that might break coherence with recent tokens."""
        # This is a simplified implementation
        # In a real system, this would identify tokens that would create
        # semantic or logical discontinuities
        return []
    
    def _get_factual_tokens(self) -> List[int]:
        """Get tokens associated with factual language."""
        if not self.tokenizer:
            return []
            
        # Words associated with factual statements
        factual_words = [
            "is", "are", "was", "were", "have", "has", "had",
            "consists", "comprises", "includes", "contains",
            "equals", "amounts", "occurred", "resulted",
            "demonstrated", "proved", "confirmed", "verified",
            "measured", "calculated", "observed", "found",
            "specifically", "precisely", "exactly", "definitely"
        ]
        
        # Get token IDs
        tokens = []
        for word in factual_words:
            token_ids = self.tokenizer.encode(word, add_special_tokens=False)
            tokens.extend(token_ids)
            
        return list(set(tokens))  # Remove duplicates
    
    def _get_speculative_tokens(self) -> List[int]:
        """Get tokens associated with speculative language."""
        if not self.tokenizer:
            return []
            
        # Words associated with speculation or uncertainty
        speculative_words = [
            "might", "may", "could", "perhaps", "possibly", "probably",
            "seems", "appears", "suggests", "indicates", "likely",
            "unlikely", "uncertain", "unclear", "estimated", "approximately",
            "around", "about", "roughly", "supposedly", "allegedly",
            "reportedly", "sometimes", "occasionally", "believed to be"
        ]
        
        # Get token IDs
        tokens = []
        for word in speculative_words:
            token_ids = self.tokenizer.encode(word, add_special_tokens=False)
            tokens.extend(token_ids)
            
        return list(set(tokens))  # Remove duplicates
    
    def _get_concise_tokens(self) -> List[int]:
        """Get tokens associated with concise language."""
        if not self.tokenizer:
            return []
            
        # Words associated with concise expression
        concise_words = [
            "briefly", "concisely", "specifically", "directly",
            "clearly", "precisely", "efficiently", "succinctly",
            "essentially", "primarily", "mainly", "chiefly",
            "key", "core", "central", "vital", "critical",
            "fundamental", "basic", "crucial", "necessary",
            "in short", "in brief", "to summarize", "in essence"
        ]
        
        # Get token IDs
        tokens = []
        for word in concise_words:
            token_ids = self.tokenizer.encode(word, add_special_tokens=False)
            tokens.extend(token_ids)
            
        return list(set(tokens))  # Remove duplicates
    
    def _get_verbose_tokens(self) -> List[int]:
        """Get tokens associated with verbose language."""
        if not self.tokenizer:
            return []
            
        # Words associated with verbose expression
        verbose_words = [
            "additionally", "furthermore", "moreover", "consequently",
            "nevertheless", "notwithstanding", "correspondingly",
            "subsequently", "aforementioned", "heretofore",
            "in addition to the fact that", "it should be noted that",
            "it is important to remember that", "as previously indicated",
            "in light of the fact that", "with reference to",
            "taking into account", "on the subject of", "regarding the matter of"
        ]
        
        # Get token IDs
        tokens = []
        for word in verbose_words:
            token_ids = self.tokenizer.encode(word, add_special_tokens=False)
            tokens.extend(token_ids)
            
        return list(set(tokens))  # Remove duplicates
    
    def _get_specific_tokens(self) -> List[int]:
        """Get tokens associated with specific language."""
        if not self.tokenizer:
            return []
            
        # Words associated with specificity
        specific_words = [
            "precisely", "specifically", "exactly", "particularly",
            "explicitly", "distinctly", "uniquely", "individually",
            "separately", "specially", "expressly", "definitely",
            "specifically", "in particular", "notably", "especially",
            "namely", "that is", "i.e.", "e.g.", "such as", "for example"
        ]
        
        # Get token IDs
        tokens = []
        for word in specific_words:
            token_ids = self.tokenizer.encode(word, add_special_tokens=False)
            tokens.extend(token_ids)
            
        return list(set(tokens))  # Remove duplicates
    
    def _get_general_tokens(self) -> List[int]:
        """Get tokens associated with general language."""
        if not self.tokenizer:
            return []
            
        # Words associated with generality
        general_words = [
            "generally", "usually", "typically", "commonly",
            "normally", "often", "frequently", "mostly",
            "largely", "broadly", "widely", "universally",
            "overall", "in general", "as a rule", "by and large",
            "for the most part", "on the whole", "in most cases",
            "all", "every", "always", "never", "everything", "nothing"
        ]
        
        # Get token IDs
        tokens = []
        for word in general_words:
            token_ids = self.tokenizer.encode(word, add_special_tokens=False)
            tokens.extend(token_ids)
            
        return list(set(tokens))  # Remove duplicates
    
    def _get_creative_tokens(self) -> List[int]:
        """Get tokens associated with creative language."""
        if not self.tokenizer:
            return []
            
        # This is a simplified implementation
        # In a real system, this would be a much larger set of tokens
        # associated with metaphoric, novel, or imaginative language
        return []
    
    def _get_common_tokens(self) -> List[int]:
        """Get tokens for very common words."""
        if not self.tokenizer:
            return []
            
        # Most common English words
        common_words = [
            "the", "be", "to", "of", "and", "a", "in", "that", "have", "I",
            "it", "for", "not", "on", "with", "he", "as", "you", "do", "at",
            "this", "but", "his", "by", "from", "they", "we", "say", "her", "she",
            "or", "an", "will", "my", "one", "all", "would", "there", "their", "what",
            "so", "up", "out", "if", "about", "who", "get", "which", "go", "me"
        ]
        
        # Get token IDs
        tokens = []
        for word in common_words:
            token_ids = self.tokenizer.encode(word, add_special_tokens=False)
            tokens.extend(token_ids)
            
        return list(set(tokens))  # Remove duplicates


class PostProcessor:
    """
    Post-processor for generated text output.
    
    This class implements various post-processing operations to enhance the
    quality of generated text, such as cleaning, fixing formatting, ensuring
    consistency, and applying style guidelines.
    """
    
    def __init__(
        self,
        clean_output: bool = True,
        enforce_constraints: bool = True,
        max_output_length: Optional[int] = None,
        style_parameters: Optional[Dict[str, str]] = None
    ):
        """
        Initialize the post-processor.
        
        Args:
            clean_output: Whether to clean up the output
            enforce_constraints: Whether to enforce output constraints
            max_output_length: Maximum length of processed output
            style_parameters: Style parameters for output formatting
        """
        self.clean_output = clean_output
        self.enforce_constraints = enforce_constraints
        self.max_output_length = max_output_length
        self.style_parameters = style_parameters or {}
    
    def process(
        self,
        text: str,
        preamble: str = "",
        postamble: str = "",
        constraints: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Process generated text to enhance quality.
        
        Args:
            text: Raw generated text
            preamble: Text to prepend to the output
            postamble: Text to append to the output
            constraints: Optional constraints for the output
            
        Returns:
            str: Processed text
        """
        # Start with the raw text
        processed_text = text
        
        # Apply cleaning if enabled
        if self.clean_output:
            processed_text = self._clean_text(processed_text)
            
        # Apply constraint enforcement if enabled
        if self.enforce_constraints and constraints:
            processed_text = self._enforce_constraints(processed_text, constraints)
            
        # Apply style formatting
        processed_text = self._apply_style(processed_text)
        
        # Add preamble and postamble
        if preamble:
            processed_text = preamble + "\n\n" + processed_text
            
        if postamble:
            processed_text = processed_text + "\n\n" + postamble
            
        # Truncate if needed
        if self.max_output_length and len(processed_text) > self.max_output_length:
            processed_text = processed_text[:self.max_output_length]
            
        return processed_text
    
    def _clean_text(self, text: str) -> str:
        """
        Clean up text by fixing common issues.
        
        Args:
            text: Text to clean
            
        Returns:
            str: Cleaned text
        """
        cleaned = text
        
        # Strip leading/trailing whitespace
        cleaned = cleaned.strip()
        
        # Fix repeated punctuation
        cleaned = re.sub(r'([.!?]){3,}', r'\1\1\1', cleaned)  # Replace multiple ?/!/. with at most 3
        cleaned = re.sub(r'([,;:]){2,}', r'\1', cleaned)  # Replace multiple ,/;/: with 1
        
        # Fix repeated whitespace
        cleaned = re.sub(r'\s+', ' ', cleaned)
        
        # Fix spacing around punctuation
        cleaned = re.sub(r'\s+([,.!?;:])', r'\1', cleaned)
        cleaned = re.sub(r'([,.!?;:])([^\s\d])', r'\1 \2', cleaned)
        
        # Fix spacing around quotes
        cleaned = re.sub(r'"\s+', r'"', cleaned)
        cleaned = re.sub(r'\s+"', r'"', cleaned)
        
        # Fix spacing around parentheses
        cleaned = re.sub(r'\s+\)', r')', cleaned)
        cleaned = re.sub(r'\(\s+', r'(', cleaned)
        
        # Fix spacing around hyphens
        cleaned = re.sub(r'\s+-\s+', r' - ', cleaned)
        
        # Fix unbalanced quotes and parentheses
        cleaned = self._fix_unbalanced_chars(cleaned, '"', '"')
        cleaned = self._fix_unbalanced_chars(cleaned, '(', ')')
        cleaned = self._fix_unbalanced_chars(cleaned, '[', ']')
        cleaned = self._fix_unbalanced_chars(cleaned, '{', '}')
        
        # Fix hanging sentences (ending without proper punctuation)
        if not cleaned.rstrip()[-1] in ['.', '!', '?', '"', '\'', ':', ';', ')', ']', '}']:
            cleaned += '.'
            
        # Ensure paragraphs are separated by double newlines
        cleaned = re.sub(r'\n{3,}', '\n\n', cleaned)
        
        # Fix capitalization of the first letter of sentences
        def capitalize_sentence_start(match):
            return match.group(1) + match.group(2).upper() + match.group(3)
            
        cleaned = re.sub(r'([.!?]\s+)([a-z])(\w*)', capitalize_sentence_start, cleaned)
        
        # Capitalize first character of the text
        if cleaned and cleaned[0].islower():
            cleaned = cleaned[0].upper() + cleaned[1:]
            
        return cleaned
    
    def _fix_unbalanced_chars(self, text: str, opening_char: str, closing_char: str) -> str:
        """
        Fix unbalanced character pairs like quotes and parentheses.
        
        Args:
            text: Text to fix
            opening_char: Opening character
            closing_char: Closing character
            
        Returns:
            str: Text with balanced characters
        """
        # Count opening and closing chars
        opening_count = text.count(opening_char)
        closing_count = text.count(closing_char)
        
        # Fix if unbalanced
        if opening_count > closing_count:
            # Add missing closing chars
            text += closing_char * (opening_count - closing_count)
        elif closing_count > opening_count:
            # Remove extra closing chars from the end if possible
            for i in range(closing_count - opening_count):
                last_idx = text.rfind(closing_char)
                if last_idx >= 0:
                    text = text[:last_idx] + text[last_idx + 1:]
                    
        return text
    
    def _enforce_constraints(self, text: str, constraints: Dict[str, Any]) -> str:
        """
        Enforce constraints on the output text.
        
        Args:
            text: Text to process
            constraints: Dictionary of constraints
            
        Returns:
            str: Text with constraints enforced
        """
        constrained_text = text
        
        # Apply word count constraints
        if "max_words" in constraints:
            max_words = constraints["max_words"]
            words = constrained_text.split()
            if len(words) > max_words:
                constrained_text = " ".join(words[:max_words])
                # Add ellipsis if truncated
                if not constrained_text.endswith((".", "!", "?")):
                    constrained_text += "..."
                    
        if "min_words" in constraints:
            min_words = constraints["min_words"]
            words = constrained_text.split()
            if len(words) < min_words:
                # If too short, make no changes - original text is returned
                return text
                
        # Apply sentence count constraints
        if "max_sentences" in constraints and _HAS_NLTK:
            max_sentences = constraints["max_sentences"]
            sentences = sent_tokenize(constrained_text)
            if len(sentences) > max_sentences:
                constrained_text = " ".join(sentences[:max_sentences])
                
        # Apply specific output format constraints
        if "format" in constraints:
            output_format = constraints["format"].lower()
            
            if output_format == "json":
                # Ensure output is valid JSON
                try:
                    data = json.loads(constrained_text)
                    constrained_text = json.dumps(data, indent=2)
                except json.JSONDecodeError:
                    # If not valid JSON, try to convert it to JSON
                    constrained_text = self._convert_to_json(constrained_text)
                    
            elif output_format == "bullet_points":
                # Ensure output is formatted as bullet points
                constrained_text = self._convert_to_bullet_points(constrained_text)
                
            elif output_format == "numbered_list":
                # Ensure output is formatted as a numbered list
                constrained_text = self._convert_to_numbered_list(constrained_text)
                
        # Apply keyword constraints
        if "include_keywords" in constraints:
            keywords = constraints["include_keywords"]
            constrained_text = self._ensure_keywords_included(constrained_text, keywords)
            
        if "exclude_keywords" in constraints:
            keywords = constraints["exclude_keywords"]
            constrained_text = self._ensure_keywords_excluded(constrained_text, keywords)
            
        return constrained_text
    
    def _apply_style(self, text: str) -> str:
        """
        Apply style formatting based on style parameters.
        
        Args:
            text: Text to format
            
        Returns:
            str: Formatted text
        """
        styled_text = text
        
        # Apply formality adjustments
        formality = self.style_parameters.get("formality", "neutral")
        styled_text = self._adjust_formality(styled_text, formality)
        
        # Apply tone adjustments
        tone = self.style_parameters.get("tone", "neutral")
        styled_text = self._adjust_tone(styled_text, tone)
        
        # Apply verbosity adjustments
        verbosity = self.style_parameters.get("verbosity", "balanced")
        styled_text = self._adjust_verbosity(styled_text, verbosity)
        
        # Apply complexity adjustments
        complexity = self.style_parameters.get("complexity", "medium")
        styled_text = self._adjust_complexity(styled_text, complexity)
        
        return styled_text
    
    def _convert_to_json(self, text: str) -> str:
        """
        Attempt to convert text to valid JSON format.
        
        Args:
            text: Text to convert
            
        Returns:
            str: JSON-formatted text
        """
        # Simple heuristic to extract key-value pairs
        result = {}
        
        # Look for patterns like "key: value" or "key - value"
        # This is a simplified approach and won't work for all cases
        pairs = re.findall(r'(?:^|\n)([^:\n]+)[:|-]\s*([^\n]+)', text)
        
        for key, value in pairs:
            # Clean up key and value
            key = key.strip().lower().replace(' ', '_')
            value = value.strip()
            
            # Try to convert value to appropriate type
            try:
                if value.lower() in ['true', 'yes']:
                    value = True
                elif value.lower() in ['false', 'no']:
                    value = False
                elif value.replace('.', '', 1).isdigit():
                    value = float(value) if '.' in value else int(value)
            except (ValueError, AttributeError):
                pass
                
            result[key] = value
            
        try:
            return json.dumps(result, indent=2)
        except Exception:
            # If conversion fails, return original text wrapped in a simple JSON object
            return json.dumps({"content": text}, indent=2)
    
    def _convert_to_bullet_points(self, text: str) -> str:
        """
        Convert text to bullet point format.
        
        Args:
            text: Text to convert
            
        Returns:
            str: Bullet point formatted text
        """
        # Split into paragraphs or sentences
        if _HAS_NLTK:
            segments = sent_tokenize(text)
        else:
            segments = re.split(r'\n+|(?<=[.!?])\s+', text)
            
        segments = [s.strip() for s in segments if s.strip()]
        
        # Convert to bullet points
        result = []
        for segment in segments:
            # Skip if it already starts with a bullet point
            if segment.startswith(('•', '-', '*', '·')):
                result.append(segment)
            else:
                result.append(f"• {segment}")
                
        return '\n'.join(result)
    
    def _convert_to_numbered_list(self, text: str) -> str:
        """
        Convert text to numbered list format.
        
        Args:
            text: Text to convert
            
        Returns:
            str: Numbered list formatted text
        """
        # Split into paragraphs or sentences
        if _HAS_NLTK:
            segments = sent_tokenize(text)
        else:
            segments = re.split(r'\n+|(?<=[.!?])\s+', text)
            
        segments = [s.strip() for s in segments if s.strip()]
        
        # Convert to numbered list
        result = []
        for i, segment in enumerate(segments, 1):
            # Skip if it already starts with a number
            if re.match(r'^\d+\.', segment):
                result.append(segment)
            else:
                result.append(f"{i}. {segment}")
                
        return '\n'.join(result)
    
    def _ensure_keywords_included(self, text: str, keywords: List[str]) -> str:
        """
        Ensure text includes specified keywords.
        
        Args:
            text: Text to process
            keywords: List of keywords to include
            
        Returns:
            str: Text with keywords included
        """
        lower_text = text.lower()
        
        # Check which keywords are missing
        missing_keywords = []
        for keyword in keywords:
            if keyword.lower() not in lower_text:
                missing_keywords.append(keyword)
                
        # If no keywords are missing, return original text
        if not missing_keywords:
            return text
            
        # Add missing keywords in a postscript
        return text + f"\n\nKey points: {', '.join(keywords)}"
    
    def _ensure_keywords_excluded(self, text: str, keywords: List[str]) -> str:
        """
        Ensure text excludes specified keywords.
        
        Args:
            text: Text to process
            keywords: List of keywords to exclude
            
        Returns:
            str: Text with keywords excluded
        """
        processed_text = text
        
        # Replace each keyword with a blank or alternative
        for keyword in keywords:
            # Use case-insensitive replacement
            pattern = re.compile(re.escape(keyword), re.IGNORECASE)
            processed_text = pattern.sub("[redacted]", processed_text)
            
        return processed_text
    
    def _adjust_formality(self, text: str, formality: str) -> str:
        """
        Adjust text formality level.
        
        Args:
            text: Text to adjust
            formality: Formality level (casual, neutral, formal)
            
        Returns:
            str: Adjusted text
        """
        # Simple implementation - in practice, this would be more sophisticated
        if formality == "formal":
            # Replace contractions with full forms
            contractions = {
                "don't": "do not", "doesn't": "does not", "didn't": "did not",
                "can't": "cannot", "won't": "will not", "shouldn't": "should not",
                "wouldn't": "would not", "couldn't": "could not", "isn't": "is not",
                "aren't": "are not", "wasn't": "was not", "weren't": "were not",
                "haven't": "have not", "hasn't": "has not", "hadn't": "had not",
                "I'm": "I am", "you're": "you are", "he's": "he is", "she's": "she is",
                "it's": "it is", "we're": "we are", "they're": "they are",
                "I've": "I have", "you've": "you have", "we've": "we have",
                "they've": "they have", "I'll": "I will", "you'll": "you will",
                "he'll": "he will", "she'll": "she will", "it'll": "it will",
                "we'll": "we will", "they'll": "they will", "I'd": "I would",
                "you'd": "you would", "he'd": "he would", "she'd": "she would",
                "it'd": "it would", "we'd": "we would", "they'd": "they would"
            }
            
            for contraction, expansion in contractions.items():
                text = re.sub(r'\b' + re.escape(contraction) + r'\b', expansion, text, flags=re.IGNORECASE)
                
        elif formality == "casual":
            # For casual, we'd typically make the text more conversational,
            # but this is complex to implement properly
            pass
            
        return text
    
    def _adjust_tone(self, text: str, tone: str) -> str:
        """
        Adjust text tone.
        
        Args:
            text: Text to adjust
            tone: Tone (enthusiastic, neutral, serious)
            
        Returns:
            str: Adjusted text
        """
        # Simple implementation - in practice, this would use more sophisticated NLP techniques
        return text
    
    def _adjust_verbosity(self, text: str, verbosity: str) -> str:
        """
        Adjust text verbosity.
        
        Args:
            text: Text to adjust
            verbosity: Verbosity level (concise, balanced, detailed)
            
        Returns:
            str: Adjusted text
        """
        # Simple implementation - in practice, this would use text summarization for concise,
        # or expansion techniques for detailed
        return text
    
    def _adjust_complexity(self, text: str, complexity: str) -> str:
        """
        Adjust text complexity.
        
        Args:
            text: Text to adjust
            complexity: Complexity level (simple, medium, complex)
            
        Returns:
            str: Adjusted text
        """
        # Simple implementation - in practice, this would use readability metrics
        # and vocabulary adjustments
        return text


class TextRanker:
    """
    Ranks generated text candidates based on quality metrics.
    
    This class implements various scoring mechanisms to rank multiple text
    candidates, enabling selection of the highest quality output.
    """
    
    def __init__(
        self,
        criteria: List[str] = ["relevance", "coherence"],
        weights: Optional[Dict[str, float]] = None,
        semantic_model = None
    ):
        """
        Initialize the text ranker.
        
        Args:
            criteria: List of ranking criteria
            weights: Optional weights for each criterion
            semantic_model: Optional model for semantic analysis
        """
        self.criteria = criteria
        
        # Default to equal weights if not provided
        if weights is None:
            self.weights = {criterion: 1.0 / len(criteria) for criterion in criteria}
        else:
            # Normalize weights to sum to 1.0
            total = sum(weights.values())
            self.weights = {k: v / total for k, v in weights.items()}
            
        # Initialize semantic model if available
        self.semantic_model = semantic_model
        if semantic_model is None and _HAS_SENTENCE_TRANSFORMERS:
            try:
                self.semantic_model = SentenceTransformer('all-MiniLM-L6-v2')
                logger.debug("Initialized semantic model for ranking")
            except Exception as e:
                logger.warning(f"Failed to initialize semantic model: {e}")
    
    def rank(
        self, 
        candidates: List[str],
        query: str,
        context: Optional[Dict[str, Any]] = None
    ) -> List[Tuple[str, float]]:
        """
        Rank a list of text candidates.
        
        Args:
            candidates: List of text candidates to rank
            query: Original query or prompt
            context: Optional additional context
            
        Returns:
            List[Tuple[str, float]]: Ranked candidates with scores
        """
        # Score each candidate on each criterion
        candidate_scores = []
        
        for candidate in candidates:
            # Calculate scores for each criterion
            scores = {}
            
            for criterion in self.criteria:
                # Get score for this criterion
                score = self._score_criterion(candidate, query, criterion, context)
                scores[criterion] = score
                
            # Calculate weighted average score
            weighted_score = sum(scores[criterion] * self.weights.get(criterion, 0.0) for criterion in scores)
            
            candidate_scores.append((candidate, weighted_score))
            
        # Sort by score (descending)
        ranked_candidates = sorted(candidate_scores, key=lambda x: x[1], reverse=True)
        
        return ranked_candidates
    
    def _score_criterion(
        self, 
        text: str, 
        query: str, 
        criterion: str,
        context: Optional[Dict[str, Any]] = None
    ) -> float:
        """
        Score a text candidate on a specific criterion.
        
        Args:
            text: Text candidate to score
            query: Original query or prompt
            criterion: Criterion to score on
            context: Optional additional context
            
        Returns:
            float: Score for the criterion (0.0 to 1.0)
        """
        if criterion == "relevance":
            return self._score_relevance(text, query, context)
        elif criterion == "coherence":
            return self._score_coherence(text, context)
        elif criterion == "novelty":
            return self._score_novelty(text, context)
        elif criterion == "factuality":
            return self._score_factuality(text, context)
        elif criterion == "diversity":
            return self._score_diversity(text, context)
        else:
            # Unknown criterion
            return 0.5  # Neutral score
    
    def _score_relevance(
        self, 
        text: str, 
        query: str,
        context: Optional[Dict[str, Any]] = None
    ) -> float:
        """
        Score text relevance to the query.
        
        Args:
            text: Text to score
            query: Original query
            context: Optional additional context
            
        Returns:
            float: Relevance score (0.0 to 1.0)
        """
        # Use semantic similarity if available
        if self.semantic_model is not None:
            try:
                # Embed texts
                text_embedding = self.semantic_model.encode(text, convert_to_tensor=True)
                query_embedding = self.semantic_model.encode(query, convert_to_tensor=True)
                
                # Calculate cosine similarity
                similarity = F.cosine_similarity(
                    text_embedding.unsqueeze(0),
                    query_embedding.unsqueeze(0)
                ).item()
                
                return max(0.0, min(1.0, similarity))
                
            except Exception as e:
                logger.warning(f"Error in semantic similarity calculation: {e}")
                
        # Fall back to keyword-based relevance
        query_words = set(re.findall(r'\b\w+\b', query.lower()))
        text_words = set(re.findall(r'\b\w+\b', text.lower()))
        
        # Filter out common stop words
        stop_words = {'the', 'a', 'an', 'and', 'of', 'to', 'is', 'in', 'that', 'it', 'for'}
        query_words = query_words - stop_words
        text_words = text_words - stop_words
        
        # Calculate overlap
        if not query_words:
            return 0.5  # Neutral score if no meaningful query words
            
        overlap = len(query_words.intersection(text_words)) / len(query_words)
        
        return overlap
    
    def _score_coherence(
        self, 
        text: str,
        context: Optional[Dict[str, Any]] = None
    ) -> float:
        """
        Score text coherence.
        
        Args:
            text: Text to score
            context: Optional additional context
            
        Returns:
            float: Coherence score (0.0 to 1.0)
        """
        # Split into sentences for analysis
        if _HAS_NLTK:
            sentences = sent_tokenize(text)
        else:
            sentences = re.split(r'[.!?]+\s+', text)
            
        # Not enough sentences for coherence analysis
        if len(sentences) < 2:
            return 0.5  # Neutral score
            
        # Use semantic similarity between adjacent sentences if available
        if self.semantic_model is not None:
            try:
                # Calculate pairwise similarities
                similarities = []
                embeddings = self.semantic_model.encode(sentences, convert_to_tensor=True)
                
                for i in range(len(sentences) - 1):
                    similarity = F.cosine_similarity(
                        embeddings[i].unsqueeze(0),
                        embeddings[i+1].unsqueeze(0)
                    ).item()
                    similarities.append(similarity)
                    
                # Average similarity as coherence measure
                return sum(similarities) / len(similarities)
                
            except Exception as e:
                logger.warning(f"Error in coherence calculation: {e}")
                
        # Fall back to simple lexical cohesion
        cohesion_score = 0.0
        
        for i in range(len(sentences) - 1):
            sent1_words = set(re.findall(r'\b\w+\b', sentences[i].lower()))
            sent2_words = set(re.findall(r'\b\w+\b', sentences[i+1].lower()))
            
            # Calculate word overlap
            if sent1_words and sent2_words:
                overlap = len(sent1_words.intersection(sent2_words)) / max(len(sent1_words), len(sent2_words))
                cohesion_score += overlap
                
        # Average cohesion
        return cohesion_score / (len(sentences) - 1) if len(sentences) > 1 else 0.5
    
    def _score_novelty(
        self, 
        text: str,
        context: Optional[Dict[str, Any]] = None
    ) -> float:
        """
        Score text novelty.
        
        Args:
            text: Text to score
            context: Optional additional context
            
        Returns:
            float: Novelty score (0.0 to 1.0)
        """
        # Check for reference texts in context
        reference_texts = []
        if context and "reference_texts" in context:
            reference_texts = context["reference_texts"]
            
        # If no reference texts, can't calculate novelty
        if not reference_texts:
            return 0.5  # Neutral score
            
        # Use semantic similarity to assess novelty
        if self.semantic_model is not None:
            try:
                # Embed text
                text_embedding = self.semantic_model.encode(text, convert_to_tensor=True)
                
                # Embed reference texts
                reference_embeddings = [
                    self.semantic_model.encode(ref, convert_to_tensor=True)
                    for ref in reference_texts
                ]
                
                # Calculate similarities to reference texts
                similarities = []
                for ref_embedding in reference_embeddings:
                    similarity = F.cosine_similarity(
                        text_embedding.unsqueeze(0),
                        ref_embedding.unsqueeze(0)
                    ).item()
                    similarities.append(similarity)
                    
                # Novelty is inverse of maximum similarity
                max_similarity = max(similarities) if similarities else 0.0
                return 1.0 - max_similarity
                
            except Exception as e:
                logger.warning(f"Error in novelty calculation: {e}")
                
        # Fall back to simple text overlap measure
        text_words = set(re.findall(r'\b\w+\b', text.lower()))
        
        # Calculate overlap with each reference text
        max_overlap = 0.0
        for ref_text in reference_texts:
            ref_words = set(re.findall(r'\b\w+\b', ref_text.lower()))
            
            if text_words and ref_words:
                overlap = len(text_words.intersection(ref_words)) / len(text_words)
                max_overlap = max(max_overlap, overlap)
                
        # Novelty is inverse of overlap
        return 1.0 - max_overlap
    
    def _score_factuality(
        self, 
        text: str,
        context: Optional[Dict[str, Any]] = None
    ) -> float:
        """
        Score text factuality.
        
        Args:
            text: Text to score
            context: Optional additional context
            
        Returns:
            float: Factuality score (0.0 to 1.0)
        """
        # Without external knowledge verification, use knowledge base from context
        knowledge_base = {}
        if context and "knowledge_base" in context:
            knowledge_base = context["knowledge_base"]
            
        # If no knowledge base, can't verify factuality
        if not knowledge_base:
            return 0.5  # Neutral score
            
        # Extract factual claims from text
        facts = self._extract_facts(text)
        if not facts:
            return 0.5  # No facts to verify
            
        # Match against knowledge base
        matched_facts = 0
        contradicted_facts = 0
        
        for fact in facts:
            # Check for matches in knowledge base
            match_found = False
            contradiction_found = False
            
            for kb_fact, kb_value in knowledge_base.items():
                # Calculate similarity with fact
                if self.semantic_model is not None:
                    try:
                        fact_embedding = self.semantic_model.encode(fact, convert_to_tensor=True)
                        kb_embedding = self.semantic_model.encode(kb_fact, convert_to_tensor=True)
                        
                        similarity = F.cosine_similarity(
                            fact_embedding.unsqueeze(0),
                            kb_embedding.unsqueeze(0)
                        ).item()
                        
                        # Consider it a match if similarity is high
                        if similarity > 0.85:
                            match_found = True
                            break
                            
                        # Consider it a contradiction if moderate similarity but opposite value
                        if similarity > 0.7 and kb_value is not None and isinstance(kb_value, bool) and not kb_value:
                            contradiction_found = True
                            break
                            
                    except Exception:
                        pass
                        
                # Fallback to simple string matching
                if kb_fact.lower() in fact.lower() or fact.lower() in kb_fact.lower():
                    match_found = True
                    break
                    
            if match_found:
                matched_facts += 1
            elif contradiction_found:
                contradicted_facts += 1
                
        # Calculate factuality score
        total_facts = len(facts)
        if total_facts == 0:
            return 0.5  # Neutral score
            
        factuality = (matched_facts - contradicted_facts) / total_facts
        
        # Scale to 0.0 to 1.0
        return max(0.0, min(1.0, (factuality + 1.0) / 2.0))
    
    def _score_diversity(
        self, 
        text: str,
        context: Optional[Dict[str, Any]] = None
    ) -> float:
        """
        Score text diversity.
        
        Args:
            text: Text to score
            context: Optional additional context
            
        Returns:
            float: Diversity score (0.0 to 1.0)
        """
        # Check for other candidates in context
        other_candidates = []
        if context and "other_candidates" in context:
            other_candidates = context["other_candidates"]
            
        # If no other candidates, can't calculate diversity
        if not other_candidates:
            return 0.5  # Neutral score
            
        # Use semantic similarity to assess diversity
        if self.semantic_model is not None:
            try:
                # Embed text
                text_embedding = self.semantic_model.encode(text, convert_to_tensor=True)
                
                # Embed other candidates
                other_embeddings = [
                    self.semantic_model.encode(other, convert_to_tensor=True)
                    for other in other_candidates
                ]
                
                # Calculate similarities to other candidates
                similarities = []
                for other_embedding in other_embeddings:
                    similarity = F.cosine_similarity(
                        text_embedding.unsqueeze(0),
                        other_embedding.unsqueeze(0)
                    ).item()
                    similarities.append(similarity)
                    
                # Diversity is inverse of average similarity
                avg_similarity = sum(similarities) / len(similarities) if similarities else 0.0
                return 1.0 - avg_similarity
                
            except Exception as e:
                logger.warning(f"Error in diversity calculation: {e}")
                
        # Fall back to simple text overlap measure
        text_words = set(re.findall(r'\b\w+\b', text.lower()))
        
        # Calculate average overlap with other candidates
        total_overlap = 0.0
        for other_text in other_candidates:
            other_words = set(re.findall(r'\b\w+\b', other_text.lower()))
            
            if text_words and other_words:
                overlap = len(text_words.intersection(other_words)) / len(text_words)
                total_overlap += overlap
                
        # Average overlap
        avg_overlap = total_overlap / len(other_candidates) if other_candidates else 0.0
        
        # Diversity is inverse of overlap
        return 1.0 - avg_overlap
    
    def _extract_facts(self, text: str) -> List[str]:
        """
        Extract factual claims from text.
        
        Args:
            text: Input text
            
        Returns:
            List[str]: Extracted factual claims
        """
        facts = []
        
        # Split into sentences
        if _HAS_NLTK:
            sentences = sent_tokenize(text)
        else:
            sentences = re.split(r'[.!?]+\s+', text)
            
        # Identify fact-like sentences
        fact_markers = [
            r'\bis\b', r'\bare\b', r'\bwas\b', r'\bwere\b', r'\bhave\b', r'\bhas\b',
            r'\bconsists\b', r'\bcomprises\b', r'\bincludes\b', r'\bcontains\b',
            r'\bequals\b', r'\bamounts\b', r'\boccurred\b'
        ]
        
        for sentence in sentences:
            # Skip speculative or uncertain statements
            if re.search(r'\bmight\b|\bcould\b|\bperhaps\b|\bmaybe\b|\bpossibly\b', sentence, re.IGNORECASE):
                continue
                
            # Check for fact markers
            if any(re.search(marker, sentence, re.IGNORECASE) for marker in fact_markers):
                facts.append(sentence)
                
        return facts


class TextGenerator(OutputGenerator):
    """
    Text generation module for ULTRA system.
    
    This class implements text generation using transformer models, with
    support for various generation strategies, prompt engineering, and
    post-processing for high-quality outputs.
    """
    
    def __init__(self, config: TextGeneratorConfig):
        """
        Initialize the text generator.
        
        Args:
            config: Configuration for the text generator
        """
        super().__init__(config)
        
        # Store specific config
        self.text_config = config
        
        # Initialize model and tokenizer
        self.model = None
        self.tokenizer = None
        self._initialize_model_and_tokenizer()
        
        # Initialize auxiliary components
        self._initialize_auxiliary_components()
        
        logger.info(f"Initialized TextGenerator with model {config.model_name}")
    
    def _initialize_model_and_tokenizer(self):
        """Initialize the model and tokenizer based on configuration."""
        # Check for required dependencies
        if not _HAS_TRANSFORMERS:
            raise ImportError("Transformers library is required for TextGenerator")
            
        try:
            # Determine tokenizer name
            tokenizer_name = self.text_config.tokenizer_name or self.text_config.model_name
            
            # Initialize tokenizer
            self.tokenizer = AutoTokenizer.from_pretrained(
                tokenizer_name,
                use_fast=self.text_config.use_fast_tokenizer,
                padding_side=self.text_config.tokenizer_padding_side,
                truncation_side=self.text_config.tokenizer_truncation_side
            )
            
            # Initialize model
            model_kwargs = {}
            
            # Apply quantization if configured
            if self.text_config.use_quantization:
                if _HAS_TRANSFORMERS_ADVANCED:
                    from transformers import BitsAndBytesConfig
                    
                    # Set up quantization
                    if self.text_config.quantization_bits == 4:
                        quantization_config = BitsAndBytesConfig(
                            load_in_4bit=True,
                            bnb_4bit_compute_dtype=torch.float16
                        )
                    elif self.text_config.quantization_bits == 8:
                        quantization_config = BitsAndBytesConfig(
                            load_in_8bit=True
                        )
                    else:
                        logger.warning(f"Unsupported quantization bits: {self.text_config.quantization_bits}. Using 8-bit.")
                        quantization_config = BitsAndBytesConfig(
                            load_in_8bit=True
                        )
                        
                    model_kwargs["quantization_config"] = quantization_config
                else:
                    logger.warning("Quantization requested but advanced transformers features not available")
                    
            # Apply flash attention if configured
            if self.text_config.use_flash_attention and _HAS_TRANSFORMERS_ADVANCED:
                model_kwargs["use_flash_attention_2"] = True
                
            # Load the model
            if self.text_config.model_type == "causal_lm":
                self.model = AutoModelForCausalLM.from_pretrained(
                    self.text_config.model_name,
                    torch_dtype=self.dtype,
                    device_map="auto" if torch.cuda.is_available() else None,
                    **model_kwargs
                )
            elif self.text_config.model_type == "seq2seq":
                self.model = AutoModelForSeq2SeqLM.from_pretrained(
                    self.text_config.model_name,
                    torch_dtype=self.dtype,
                    device_map="auto" if torch.cuda.is_available() else None,
                    **model_kwargs
                )
            else:
                raise ValueError(f"Unsupported model type: {self.text_config.model_type}")
                
            # Move model to device if not using device_map
            if "device_map" not in model_kwargs and torch.cuda.is_available():
                self.model = self.model.to(self.device)
                
            # Set decoding parameters
            if self.tokenizer.pad_token is None and self.tokenizer.eos_token is not None:
                self.tokenizer.pad_token = self.tokenizer.eos_token
                
        except Exception as e:
            logger.error(f"Failed to initialize model and tokenizer: {e}")
            raise
    
    def _initialize_auxiliary_components(self):
        """Initialize auxiliary components for text generation."""
        # Initialize prompt engineering
        self.prompt_engineering = PromptEngineering(
            strategies=self.text_config.prompt_strategies,
            system_prompt=self.text_config.system_prompt if self.text_config.use_system_prompt else None,
            tokenizer=self.tokenizer,
            max_length=self.text_config.max_context_length
        )
        
        # Initialize document structure processor
        self.document_processor = DocumentStructureProcessor(
            structure_type=self.text_config.document_structure,
            tokenizer=self.tokenizer
        )
        
        # Initialize guidance controller
        self.guidance_controller = None
        if self.text_config.contextual_control:
            self.guidance_controller = GuidanceController(
                tokenizer=self.tokenizer,
                guidance_scale=1.0,
                criteria=["relevance", "coherence", "factuality"]
            )
            
        # Initialize post-processor
        self.post_processor = PostProcessor(
            clean_output=self.text_config.clean_output,
            enforce_constraints=self.text_config.enforce_constraints,
            max_output_length=self.text_config.max_output_length,
            style_parameters={
                "formality": self.text_config.formality,
                "tone": self.text_config.tone,
                "verbosity": self.text_config.verbosity,
                "complexity": self.text_config.complexity
            }
        )
        
        # Initialize text ranker if reranking is enabled
        self.text_ranker = None
        if self.text_config.reranking:
            self.text_ranker = TextRanker(
                criteria=self.text_config.reranking_criteria
            )
            
        # Initialize reasoning trace formatter
        self.trace_formatter = ReasoningTraceFormatter(
            include_trace=self.text_config.include_reasoning_trace,
            trace_format=self.text_config.reasoning_trace_format,
            trace_detail=self.text_config.reasoning_detail_level,
            max_trace_length=min(self.text_config.max_output_length // 2, 1000),
            show_uncertainties=True
        )
    
    def generate(
        self, 
        input_data: Union[str, Dict[str, Any]], 
        context: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> Union[str, Dict[str, Any]]:
        """
        Generate text from input data.
        
        Args:
            input_data: Input query or structured input
            context: Optional context information
            **kwargs: Additional generation parameters
            
        Returns:
            Union[str, Dict[str, Any]]: Generated text or structured output
        """
        # Check cache if enabled
        if self.cache_enabled:
            cache_key = self._compute_hash((input_data, context, frozenset(kwargs.items())))
            if cache_key in self.cache:
                return self.cache[cache_key]
                
        # Extract query from input data
        query = input_data
        metadata = {}
        
        if isinstance(input_data, dict):
            query = input_data.get("query", "")
            metadata = {k: v for k, v in input_data.items() if k != "query"}
            
        # Create context if None
        context = context or {}
        
        # Add metadata to context
        if metadata:
            context["metadata"] = metadata
            
        # Process the query and generate text
        try:
            # Apply prompt engineering
            processed_query = self._process_query(query, context)
            
            # Generate text
            if self.text_config.num_return_sequences > 1 or self.text_config.reranking:
                # Generate multiple candidates
                candidates = self._generate_candidates(processed_query, context, **kwargs)
                
                # Apply reranking if enabled
                if self.text_config.reranking and len(candidates) > 1:
                    ranked_candidates = self._rank_candidates(candidates, query, context)
                    generated_text = ranked_candidates[0][0]
                else:
                    generated_text = candidates[0]
            else:
                # Generate single text
                generated_text = self._generate_text(processed_query, context, **kwargs)
                
            # Handle reasoning trace
            if self.text_config.include_reasoning_trace and context.get("reasoning_trace"):
                trace_text = self.trace_formatter.format_trace(
                    context["reasoning_trace"],
                    output_modality="text"
                )
                
                # Include trace in output if available
                if trace_text:
                    generated_text = f"{generated_text}\n\n{trace_text}"
                    
            # Post-process the generated text
            final_text = self._post_process_text(generated_text, context)
            
            # Cache result if enabled
            if self.cache_enabled and cache_key:
                self.cache[cache_key] = final_text
                self._manage_cache_size()
                
            return final_text
            
        except Exception as e:
            logger.error(f"Error generating text: {e}")
            # Fallback to a simple message
            return "I'm unable to generate text due to an internal error. Please try again or modify your request."
    
    def _process_query(
        self, 
        query: str, 
        context: Dict[str, Any]
    ) -> str:
        """
        Process the query with prompt engineering.
        
        Args:
            query: Original query
            context: Context information
            
        Returns:
            str: Processed query
        """
        # Extract metadata from context
        metadata = context.get("metadata", {})
        
        # Apply prompt engineering
        if self.text_config.use_prompt_engineering:
            processed_query = self.prompt_engineering.generate_prompt(query, context, metadata)
        else:
            # Prepend system prompt if configured
            if self.text_config.use_system_prompt and self.text_config.system_prompt:
                processed_query = f"{self.text_config.system_prompt}\n\n{query}"
            else:
                processed_query = query
                
        return processed_query
    
    def _generate_candidates(
        self,
        processed_query: str,
        context: Dict[str, Any],
        **kwargs
    ) -> List[str]:
        """
        Generate multiple text candidates.
        
        Args:
            processed_query: Processed query
            context: Context information
            **kwargs: Additional generation parameters
            
        Returns:
            List[str]: List of generated candidates
        """
        # Set up generation parameters
        params = self._prepare_generation_params(processed_query, context, **kwargs)
        
        # Set number of return sequences
        num_sequences = max(
            self.text_config.num_return_sequences,
            kwargs.get("num_return_sequences", 1)
        )
        params["num_return_sequences"] = num_sequences
        
        # Ensure we're using sampling for diversity
        if num_sequences > 1:
            params["do_sample"] = True
            
        # Prepare input
        input_ids = self._prepare_input(processed_query)
        
        # Generate sequences
        with torch.no_grad():
            outputs = self.model.generate(
                input_ids=input_ids,
                **params
            )
            
        # Decode sequences
        candidates = []
        
        for output in outputs:
            # For causal models, we need to skip the prompt
            if self.text_config.model_type == "causal_lm":
                output = output[input_ids.shape[1]:]
                
            # Decode the output
            decoded_text = self.tokenizer.decode(output, skip_special_tokens=True)
            candidates.append(decoded_text)
            
        return candidates
    
    def _generate_text(
        self,
        processed_query: str,
        context: Dict[str, Any],
        **kwargs
    ) -> str:
        """
        Generate a single text output.
        
        Args:
            processed_query: Processed query
            context: Context information
            **kwargs: Additional generation parameters
            
        Returns:
            str: Generated text
        """
        # Set up generation parameters
        params = self._prepare_generation_params(processed_query, context, **kwargs)
        
        # Prepare input
        input_ids = self._prepare_input(processed_query)
        
        # Generate text
        with torch.no_grad():
            outputs = self.model.generate(
                input_ids=input_ids,
                **params
            )
            
        # Decode the output
        if self.text_config.model_type == "causal_lm":
            # For causal models, we need to skip the prompt
            output = outputs[0][input_ids.shape[1]:]
        else:
            output = outputs[0]
            
        decoded_text = self.tokenizer.decode(output, skip_special_tokens=True)
        
        return decoded_text
    
    def _prepare_input(self, text: str) -> torch.Tensor:
        """
        Prepare input for the model.
        
        Args:
            text: Input text
            
        Returns:
            torch.Tensor: Input IDs
        """
        # Encode the input
        inputs = self.tokenizer(
            text,
            padding="longest",
            truncation=True,
            max_length=self.text_config.max_context_length,
            return_tensors="pt"
        )
        
        # Move to device
        inputs = {k: v.to(self.device) for k, v in inputs.items()}
        
        return inputs.input_ids
    
    def _prepare_generation_params(
        self,
        processed_query: str,
        context: Dict[str, Any],
        **kwargs
    ) -> Dict[str, Any]:
        """
        Prepare parameters for generation.
        
        Args:
            processed_query: Processed query
            context: Context information
            **kwargs: Additional generation parameters
            
        Returns:
            Dict[str, Any]: Generation parameters
        """
        # Start with default parameters
        params = {
            "max_new_tokens": self.text_config.max_new_tokens,
            "min_new_tokens": self.text_config.min_new_tokens,
            "do_sample": self.text_config.do_sample,
            "temperature": self.text_config.temperature,
            "top_p": self.text_config.top_p,
            "top_k": self.text_config.top_k,
            "repetition_penalty": self.text_config.repetition_penalty,
            "no_repeat_ngram_size": self.text_config.no_repeat_ngram_size,
            "early_stopping": self.text_config.early_stopping,
            "num_beams": self.text_config.num_beams,
            "num_beam_groups": self.text_config.num_beam_groups,
            "length_penalty": self.text_config.length_penalty
        }
        
        # Apply sampling strategy
        if self.text_config.sampling_strategy == "top_p":
            params["do_sample"] = True
            params["top_p"] = self.text_config.top_p
            params["top_k"] = 0  # Disable top_k when using top_p
        elif self.text_config.sampling_strategy == "top_k":
            params["do_sample"] = True
            params["top_k"] = self.text_config.top_k
            params["top_p"] = 1.0  # Disable top_p when using top_k
        elif self.text_config.sampling_strategy == "beam":
            params["do_sample"] = False
            params["num_beams"] = max(self.text_config.num_beams, 4)
        elif self.text_config.sampling_strategy == "greedy":
            params["do_sample"] = False
            params["num_beams"] = 1
        elif self.text_config.sampling_strategy == "contrastive":
            if _HAS_TRANSFORMERS_ADVANCED:
                params["do_sample"] = False
                params["penalty_alpha"] = self.text_config.contrastive_search_alpha
                params["top_k"] = self.text_config.contrastive_search_k
            else:
                # Fall back to top_p sampling if contrastive search not available
                logger.warning("Contrastive search requested but not available. Falling back to top_p sampling.")
                params["do_sample"] = True
                params["top_p"] = self.text_config.top_p
                
        # Apply stop sequences if provided
        stop_sequences = self.text_config.stop_sequences
        if stop_sequences:
            params["stopping_criteria"] = StoppingCriteriaList([
                CustomStoppingCriteria(
                    tokenizer=self.tokenizer,
                    stop_sequences=stop_sequences,
                    prompt_length=self._prepare_input(processed_query).shape[1],
                    device=self.device
                )
            ])
            
        # Set up logits processors
        logits_processors = []
        
        # Add custom TopPTopK processor if using do_sample
        if params["do_sample"]:
            logits_processors.append(
                TopPTopKLogitsWarper(
                    top_p=params["top_p"],
                    top_k=params["top_k"],
                    temperature=params["temperature"],
                    min_tokens_to_keep=1
                )
            )
            
            # Remove redundant parameters (handled by our custom processor)
            params.pop("top_p", None)
            params.pop("top_k", None)
            params.pop("temperature", None)
            
        # Add presence/frequency penalty processor
        if self.text_config.presence_penalty > 0 or self.text_config.frequency_penalty > 0:
            logits_processors.append(
                PresenceFrequencyLogitsProcessor(
                    presence_penalty=self.text_config.presence_penalty,
                    frequency_penalty=self.text_config.frequency_penalty
                )
            )
            
        # Add contextual processor if enabled
        if self.text_config.contextual_control and self.guidance_controller is not None:
            # Extract relevant context for guidance
            context_embedding = None
            context_keywords = []
            
            if "context_embedding" in context:
                context_embedding = context["context_embedding"]
                
            if "context_keywords" in context:
                context_keywords = context["context_keywords"]
                
            # Add contextual processor if we have context information
            if context_embedding is not None or context_keywords:
                logits_processors.append(
                    ContextualLogitsProcessor(
                        tokenizer=self.tokenizer,
                        context_embedding=context_embedding,
                        context_keywords=context_keywords,
                        semantic_model=self.guidance_controller.similarity_model,
                        boost_factor=1.0
                    )
                )
                
        # Set logits processors if any are defined
        if logits_processors:
            params["logits_processor"] = LogitsProcessorList(logits_processors)
            
        # Override with any provided kwargs
        params.update(kwargs)
        
        return params
    
    def _rank_candidates(
        self,
        candidates: List[str],
        query: str,
        context: Dict[str, Any]
    ) -> List[Tuple[str, float]]:
        """
        Rank generated candidates.
        
        Args:
            candidates: List of generated candidates
            query: Original query
            context: Context information
            
        Returns:
            List[Tuple[str, float]]: Ranked candidates with scores
        """
        # Set up context for ranking
        ranking_context = context.copy()
        
        # Add other candidates for diversity scoring
        ranking_context["other_candidates"] = candidates
        
        # Perform ranking
        ranked_candidates = self.text_ranker.rank(
            candidates=candidates,
            query=query,
            context=ranking_context
        )
        
        return ranked_candidates
    
    def _post_process_text(
        self,
        text: str,
        context: Dict[str, Any]
    ) -> str:
        """
        Post-process generated text.
        
        Args:
            text: Generated text
            context: Context information
            
        Returns:
            str: Post-processed text
        """
        # Extract constraints from context
        constraints = context.get("constraints", {})
        
        # Apply post-processing
        processed_text = self.post_processor.process(
            text=text,
            preamble=self.text_config.preamble,
            postamble=self.text_config.postamble,
            constraints=constraints
        )
        
        # Apply document structure formatting
        structure_type = self.text_config.document_structure
        if structure_type != "auto":
            processed_text = self.document_processor.format_text_with_structure(
                processed_text,
                structure_type
            )
            
        return processed_text
    
    @classmethod
    def _config_class(cls) -> Type[OutputGeneratorConfig]:
        """Get the configuration class for this generator."""
        return TextGeneratorConfig
    
    def _save_model(self, directory: str):
        """
        Save model and tokenizer.
        
        Args:
            directory: Directory to save to
        """
        if self.model and self.tokenizer:
            model_dir = os.path.join(directory, "model")
            os.makedirs(model_dir, exist_ok=True)
            
            self.model.save_pretrained(model_dir)
            self.tokenizer.save_pretrained(model_dir)
            
    def _load_model(self, directory: str):
        """
        Load model and tokenizer.
        
        Args:
            directory: Directory to load from
        """
        model_dir = os.path.join(directory, "model")
        if os.path.exists(model_dir):
            # Load model and tokenizer
            if self.text_config.model_type == "causal_lm":
                self.model = AutoModelForCausalLM.from_pretrained(
                    model_dir,
                    torch_dtype=self.dtype,
                    device_map="auto" if torch.cuda.is_available() else None
                )
            elif self.text_config.model_type == "seq2seq":
                self.model = AutoModelForSeq2SeqLM.from_pretrained(
                    model_dir,
                    torch_dtype=self.dtype,
                    device_map="auto" if torch.cuda.is_available() else None
                )
                
            self.tokenizer = AutoTokenizer.from_pretrained(model_dir)
        else:
            # Fall back to initializing from config
            self._initialize_model_and_tokenizer()


# Register the generator
if "__main__" in __name__:
    # When run as a script
    print("TextGenerator module initialized successfully.")