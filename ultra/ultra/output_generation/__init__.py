#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ULTRA Output Generation Module

This module handles the transformation of internal representations from the ULTRA
(Ultimate Learning & Thought Reasoning Architecture) system into various output 
modalities, including text, visual elements, actions, and multimodal combinations.

The module implements specialized decoders for different output types:
- TextGenerator: Produces natural language text from internal representations
- VisualGenerator: Creates visual outputs such as images, charts, and diagrams
- ActionGenerator: Produces action sequences and commands
- MultiModalSynthesizer: Integrates multiple output modalities into coherent responses

This implementation is designed to work seamlessly with the Global Workspace and
Emergent Consciousness Lattice subsystems of ULTRA, ensuring outputs reflect the
system's internal reasoning processes and self-awareness.
"""

import os
import sys
import json
import logging
import pickle
import hashlib
import time
import re
from typing import Dict, List, Tuple, Optional, Union, Any, Callable, Type
from collections import OrderedDict, defaultdict
from abc import ABC, abstractmethod
import warnings

import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(name)s: %(message)s'
)
logger = logging.getLogger(__name__)

# Try to import optional dependencies
try:
    import transformers
    from transformers import (
        AutoTokenizer, AutoModelForCausalLM, AutoModelForSeq2SeqLM,
        LogitsProcessor, LogitsProcessorList, GenerationConfig
    )
    _HAS_TRANSFORMERS = True
except ImportError:
    logger.warning("Transformers library not found. Text generation capabilities will be limited.")
    _HAS_TRANSFORMERS = False

try:
    import matplotlib
    import matplotlib.pyplot as plt
    from matplotlib.figure import Figure
    matplotlib.use('Agg')  # Use non-interactive backend
    _HAS_MATPLOTLIB = True
except ImportError:
    logger.warning("Matplotlib not found. Plotting capabilities will be limited.")
    _HAS_MATPLOTLIB = False

try:
    from PIL import Image, ImageDraw, ImageFont
    _HAS_PIL = True
except ImportError:
    logger.warning("PIL not found. Image generation capabilities will be limited.")
    _HAS_PIL = False

try:
    import networkx as nx
    _HAS_NETWORKX = True
except ImportError:
    logger.warning("NetworkX not found. Graph visualization capabilities will be limited.")
    _HAS_NETWORKX = False

try:
    import svgwrite
    _HAS_SVGWRITE = True
except ImportError:
    logger.warning("svgwrite not found. SVG generation capabilities will be limited.")
    _HAS_SVGWRITE = False

# Additional necessary imports for enhanced functionality
try:
    import boto3
    _HAS_AWS = True
except ImportError:
    logger.warning("boto3 not found. AWS deployment capabilities will be limited.")
    _HAS_AWS = False

try:
    import azure.mgmt.compute
    import azure.mgmt.storage
    import azure.mgmt.resource
    _HAS_AZURE = True
except ImportError:
    logger.warning("Azure SDK not found. Azure deployment capabilities will be limited.")
    _HAS_AZURE = False

try:
    import google.cloud.compute
    import google.cloud.storage
    _HAS_GCP = True
except ImportError:
    logger.warning("Google Cloud SDK not found. GCP deployment capabilities will be limited.")
    _HAS_GCP = False

try:
    import docker
    _HAS_DOCKER = True
except ImportError:
    logger.warning("Docker SDK not found. Container operations will be limited.")
    _HAS_DOCKER = False

try:
    from cryptography.fernet import Fernet
    _HAS_CRYPTOGRAPHY = True
except ImportError:
    logger.warning("cryptography library not found. Secure credential handling will be limited.")
    _HAS_CRYPTOGRAPHY = False

try:
    import git
    _HAS_GITPYTHON = True
except ImportError:
    logger.warning("GitPython not found. Version control capabilities will be limited.")
    _HAS_GITPYTHON = False

try:
    import kubernetes
    _HAS_KUBERNETES = True
except ImportError:
    logger.warning("Kubernetes Python client not found. Kubernetes operations will be limited.")
    _HAS_KUBERNETES = False

try:
    import terraform_validate
    _HAS_TERRAFORM = True
except ImportError:
    logger.warning("Terraform validation libraries not found. Infrastructure validation will be limited.")
    _HAS_TERRAFORM = False

try:
    import alembic
    _HAS_ALEMBIC = True
except ImportError:
    logger.warning("Alembic not found. Database migration capabilities will be limited.")
    _HAS_ALEMBIC = False

try:
    import bandit
    _HAS_BANDIT = True
except ImportError:
    logger.warning("Bandit not found. Security scanning capabilities will be limited.")
    _HAS_BANDIT = False

# Import submodules if available
try:
    from ultra.text_output import TextGenerator, TextGeneratorConfig
    from ultra.visual_output import VisualGenerator, VisualGeneratorConfig
    from ultra.action_output import ActionGenerator, ActionGeneratorConfig
    from ultra.multimodal_synthesis import MultiModalSynthesizer, MultiModalSynthesizerConfig
    
    # Import extended action capabilities
    from ultra.action_output import (
        DeploymentHandler, GitHandler, CredentialsManager, 
        CICDHandler, TestingHandler, KubernetesHandler,
        SecurityScanHandler, ComplianceHandler, 
        DatabaseMigrationHandler, SystemIntegrationHandler
    )
    
    _HAS_ALL_GENERATORS = True
    logger.info("All output generators successfully imported")
except ImportError as e:
    _HAS_ALL_GENERATORS = False
    logger.warning(f"Some generators couldn't be imported: {str(e)}")
    logger.warning("Initializing with partial functionality")

# Define public API
__all__ = [
    # Base classes
    'OutputGenerator',
    'OutputGeneratorConfig',
    
    # Generator classes
    'TextGenerator',
    'VisualGenerator',
    'ActionGenerator',
    'MultiModalSynthesizer',
    
    # Configuration classes
    'TextGeneratorConfig',
    'VisualGeneratorConfig',
    'ActionGeneratorConfig',
    'MultiModalSynthesizerConfig',
    
    # Factory functions
    'create_generator',
    'create_multimodal_synthesizer',
    
    # Utility functions
    'register_generator',
    'get_available_generators',
    'set_generator_device',
    
    # Content planning
    'ContentPlanner',
    'ReasoningTraceFormatter',
    
    # Enhanced action capabilities
    'DeploymentHandler',
    'GitHandler',
    'CredentialsManager',
    'CICDHandler',
    'TestingHandler',
    'KubernetesHandler',
    'SecurityScanHandler',
    'ComplianceHandler',
    'DatabaseMigrationHandler',
    'SystemIntegrationHandler',
    
    # Extended factory functions
    'create_deployment_handler',
    'create_git_handler',
    'create_credentials_manager',
    'create_cicd_handler',
    'create_testing_handler',
    'create_kubernetes_handler',
    'create_security_scan_handler',
    'create_compliance_handler',
    'create_database_migration_handler',
    'create_system_integration_handler'
]

# Module variables
_REGISTERED_GENERATORS = {}
_REGISTERED_HANDLERS = {}
_DEFAULT_GENERATOR_DEVICE = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

# Default configuration values
DEFAULT_CONFIG = {
    "output_mode": "greedy",          # Generation mode (greedy, sampling, beam)
    "use_cache": True,                # Whether to use generation cache
    "cache_size": 1000,               # Cache size for generated outputs
    "device": str(_DEFAULT_GENERATOR_DEVICE),  # Device for generation
    "precision": "float32",           # Numerical precision
    "max_output_length": 1024,        # Maximum length of generated outputs
    "min_output_length": 10,          # Minimum length of generated outputs
    "enable_tracing": True,           # Whether to include reasoning traces
    "temperature": 0.7,               # Temperature for sampling
    "top_p": 0.9,                     # Top-p sampling parameter
    "top_k": 50,                      # Top-k sampling parameter
    "repetition_penalty": 1.05,       # Repetition penalty
    "num_return_sequences": 1,        # Number of sequences to return
    "deterministic": False,           # Whether to ensure deterministic outputs
    "include_metadata": True,         # Whether to include metadata in outputs
    "trust_level": "high",            # Trust level for self-verification
    "dynamic_adaptation": True,       # Whether to adapt parameters dynamically
}

# Extended handler configuration values
DEFAULT_HANDLER_CONFIG = {
    "security_level": "high",             # Security level for handlers
    "encryption_enabled": True,           # Whether to use encryption
    "max_retries": 3,                     # Maximum number of retry attempts
    "timeout": 300.0,                     # Timeout in seconds
    "rbac_enabled": True,                 # Whether to use RBAC
    "environment": "production",          # Deployment environment
    "validation_required": True,          # Whether validation is required
    "audit_logging": True,                # Whether to log auditing information
    "fail_fast": True,                    # Whether to fail fast on errors
    "rollback_enabled": True,             # Whether to enable rollbacks
    "parallel_execution": False,          # Whether to execute in parallel
}


class OutputGeneratorConfig:
    """
    Base configuration class for output generators.
    
    This class defines common configuration parameters used across
    different output modality generators.
    """
    
    def __init__(self, **kwargs):
        """
        Initialize configuration with default values and overrides.
        
        Args:
            **kwargs: Overrides for default configuration values
        """
        # Start with default configuration
        for key, value in DEFAULT_CONFIG.items():
            setattr(self, key, value)
        
        # Override with any provided kwargs
        for key, value in kwargs.items():
            setattr(self, key, value)
        
        # Convert device string to torch.device
        if isinstance(self.device, str):
            self.device = torch.device(self.device)
            
        # Validate configuration
        self._validate()
        
        logger.debug(f"Initialized {self.__class__.__name__} with config: {self.to_dict()}")
    
    def _validate(self):
        """Validate configuration parameters."""
        # Validate output mode
        valid_modes = ["greedy", "sampling", "beam", "contrastive", "nucleus"]
        if self.output_mode not in valid_modes:
            raise ValueError(f"output_mode must be one of {valid_modes}")
        
        # Validate numerical parameters
        if not 0 < self.temperature <= 2.0:
            raise ValueError(f"temperature must be in (0, 2.0], got {self.temperature}")
        
        if not 0 < self.top_p <= 1.0:
            raise ValueError(f"top_p must be in (0, 1.0], got {self.top_p}")
        
        if not self.top_k > 0:
            raise ValueError(f"top_k must be positive, got {self.top_k}")
        
        if self.repetition_penalty < 1.0:
            raise ValueError(f"repetition_penalty must be >= 1.0, got {self.repetition_penalty}")
        
        if self.num_return_sequences <= 0:
            raise ValueError(f"num_return_sequences must be positive")
        
        # Validate length constraints
        if self.max_output_length <= 0:
            raise ValueError(f"max_output_length must be positive")
        
        if self.min_output_length < 0:
            raise ValueError(f"min_output_length must be non-negative")
        
        if self.min_output_length > self.max_output_length:
            raise ValueError(f"min_output_length must be <= max_output_length")
        
        # Validate trust level
        valid_trust = ["low", "medium", "high"]
        if self.trust_level not in valid_trust:
            raise ValueError(f"trust_level must be one of {valid_trust}")
            
        # Validate precision
        valid_precision = ["float32", "float16", "bfloat16"]
        if self.precision not in valid_precision:
            raise ValueError(f"precision must be one of {valid_precision}")
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert configuration to dictionary.
        
        Returns:
            Dict[str, Any]: Configuration as dictionary
        """
        # Convert device to string for serialization
        config_dict = {k: v for k, v in self.__dict__.items() if not k.startswith('_')}
        if 'device' in config_dict and isinstance(config_dict['device'], torch.device):
            config_dict['device'] = str(config_dict['device'])
        return config_dict
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'OutputGeneratorConfig':
        """
        Create configuration from dictionary.
        
        Args:
            config_dict: Dictionary containing configuration parameters
            
        Returns:
            OutputGeneratorConfig: Configuration object
        """
        return cls(**config_dict)
    
    def save(self, file_path: str) -> None:
        """
        Save configuration to JSON file.
        
        Args:
            file_path: Path to save the configuration file
        """
        with open(file_path, 'w') as f:
            json.dump(self.to_dict(), f, indent=2)
    
    @classmethod
    def load(cls, file_path: str) -> 'OutputGeneratorConfig':
        """
        Load configuration from JSON file.
        
        Args:
            file_path: Path to the configuration file
            
        Returns:
            OutputGeneratorConfig: Configuration object
        """
        with open(file_path, 'r') as f:
            config_dict = json.load(f)
        return cls.from_dict(config_dict)


# Extended handler configuration classes
class HandlerConfig:
    """Base configuration class for extended handlers."""
    
    def __init__(self, **kwargs):
        # Start with default configuration
        for key, value in DEFAULT_HANDLER_CONFIG.items():
            setattr(self, key, value)
        
        # Override with any provided kwargs
        for key, value in kwargs.items():
            setattr(self, key, value)
            
        # Validate configuration
        self._validate()
        
        logger.debug(f"Initialized {self.__class__.__name__} with config: {self.to_dict()}")
    
    def _validate(self):
        """Validate configuration parameters."""
        # Validate security level
        valid_security = ["low", "medium", "high", "extreme"]
        if not hasattr(self, 'security_level') or self.security_level not in valid_security:
            raise ValueError(f"security_level must be one of {valid_security}")
        
        # Validate timeout
        if not hasattr(self, 'timeout') or self.timeout <= 0:
            raise ValueError("timeout must be positive")
        
        # Validate max_retries
        if not hasattr(self, 'max_retries') or self.max_retries < 0:
            raise ValueError("max_retries must be non-negative")
        
        # Validate environment
        valid_env = ["development", "testing", "staging", "production"]
        if not hasattr(self, 'environment') or self.environment not in valid_env:
            raise ValueError(f"environment must be one of {valid_env}")
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert configuration to dictionary.
        
        Returns:
            Dict[str, Any]: Configuration as dictionary
        """
        return {k: v for k, v in self.__dict__.items() if not k.startswith('_')}
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'HandlerConfig':
        """
        Create configuration from dictionary.
        
        Args:
            config_dict: Dictionary containing configuration parameters
            
        Returns:
            HandlerConfig: Configuration object
        """
        return cls(**config_dict)
    
    def save(self, file_path: str) -> None:
        """
        Save configuration to JSON file.
        
        Args:
            file_path: Path to save the configuration file
        """
        with open(file_path, 'w') as f:
            json.dump(self.to_dict(), f, indent=2)
    
    @classmethod
    def load(cls, file_path: str) -> 'HandlerConfig':
        """
        Load configuration from JSON file.
        
        Args:
            file_path: Path to the configuration file
            
        Returns:
            HandlerConfig: Configuration object
        """
        with open(file_path, 'r') as f:
            config_dict = json.load(f)
        return cls.from_dict(config_dict)


class DeploymentHandlerConfig(HandlerConfig):
    """Configuration for deployment handlers."""
    
    def __init__(self, **kwargs):
        # Deployment-specific defaults
        deployment_defaults = {
            "supported_providers": ["aws", "azure", "gcp", "heroku", "netlify", "vercel"],
            "terraform_support": True,
            "ansible_support": False,
            "pulumi_support": False,
            "cloud_formation_support": True,
            "arm_templates_support": True,
            "deployment_timeout": 1800,  # 30 minutes
            "health_check_enabled": True,
            "post_deployment_validation": True,
            "auto_scaling_enabled": False,
            "cost_optimization_enabled": False
        }
        
        # Update kwargs with defaults
        for key, value in deployment_defaults.items():
            if key not in kwargs:
                kwargs[key] = value
                
        super().__init__(**kwargs)
    
    def _validate(self):
        """Validate deployment-specific configuration."""
        super()._validate()
        
        # Validate supported providers
        if not hasattr(self, 'supported_providers') or not self.supported_providers:
            raise ValueError("At least one supported provider must be specified")
        
        # Validate deployment timeout
        if not hasattr(self, 'deployment_timeout') or self.deployment_timeout <= 0:
            raise ValueError("deployment_timeout must be positive")


class GitHandlerConfig(HandlerConfig):
    """Configuration for git handlers."""
    
    def __init__(self, **kwargs):
        # Git-specific defaults
        git_defaults = {
            "allowed_operations": ["clone", "checkout", "commit", "push", "pull", "branch", "merge"],
            "ssh_key_path": None,
            "username": None,
            "email": None,
            "remote_name": "origin",
            "default_branch": "main",
            "credential_store": "memory",  # memory, file, credential-helper
            "allow_force_push": False,
            "commit_signing": False,
            "verify_ssl": True
        }
        
        # Update kwargs with defaults
        for key, value in git_defaults.items():
            if key not in kwargs:
                kwargs[key] = value
                
        super().__init__(**kwargs)
    
    def _validate(self):
        """Validate git-specific configuration."""
        super()._validate()
        
        # Validate allowed operations
        valid_operations = ["clone", "checkout", "commit", "push", "pull", "branch", "merge", 
                           "tag", "fetch", "rebase", "reset", "stash"]
        
        if not hasattr(self, 'allowed_operations') or not self.allowed_operations:
            raise ValueError("At least one allowed operation must be specified")
            
        for op in self.allowed_operations:
            if op not in valid_operations:
                raise ValueError(f"Unknown git operation: {op}")
        
        # Validate credential store
        valid_stores = ["memory", "file", "credential-helper"]
        if not hasattr(self, 'credential_store') or self.credential_store not in valid_stores:
            raise ValueError(f"credential_store must be one of {valid_stores}")


class CICDHandlerConfig(HandlerConfig):
    """Configuration for CI/CD handlers."""
    
    def __init__(self, **kwargs):
        # CI/CD-specific defaults
        cicd_defaults = {
            "supported_platforms": ["github-actions", "gitlab-ci", "jenkins", "circle-ci", "travis-ci"],
            "pipeline_timeout": 3600,  # 1 hour
            "concurrent_builds": 1,
            "artifacts_retention": 30,  # days
            "notification_channels": ["email"],
            "auto_cancel_redundant_builds": True,
            "build_cache_enabled": True,
            "deploy_on_green": False,
            "approval_required": True,
            "quality_gates": ["tests", "linting", "security-scan"]
        }
        
        # Update kwargs with defaults
        for key, value in cicd_defaults.items():
            if key not in kwargs:
                kwargs[key] = value
                
        super().__init__(**kwargs)
    
    def _validate(self):
        """Validate CI/CD-specific configuration."""
        super()._validate()
        
        # Validate supported platforms
        if not hasattr(self, 'supported_platforms') or not self.supported_platforms:
            raise ValueError("At least one supported platform must be specified")
        
        # Validate pipeline timeout
        if not hasattr(self, 'pipeline_timeout') or self.pipeline_timeout <= 0:
            raise ValueError("pipeline_timeout must be positive")
        
        # Validate concurrent builds
        if not hasattr(self, 'concurrent_builds') or self.concurrent_builds < 1:
            raise ValueError("concurrent_builds must be at least 1")
        
        # Validate notification channels
        valid_channels = ["email", "slack", "teams", "webhook", "sms"]
        if not hasattr(self, 'notification_channels') or not self.notification_channels:
            raise ValueError("At least one notification channel must be specified")
            
        for channel in self.notification_channels:
            if channel not in valid_channels:
                raise ValueError(f"Unknown notification channel: {channel}")


class TestingHandlerConfig(HandlerConfig):
    """Configuration for testing handlers."""
    
    def __init__(self, **kwargs):
        # Testing-specific defaults
        testing_defaults = {
            "test_types": ["unit", "integration", "e2e", "performance", "security"],
            "test_frameworks": {
                "python": ["pytest", "unittest"],
                "javascript": ["jest", "mocha"],
                "java": ["junit", "testng"],
                "dotnet": ["nunit", "xunit"],
                "go": ["go-test"]
            },
            "parallel_tests": True,
            "report_formats": ["junit", "html"],
            "coverage_threshold": 80,  # percentage
            "test_timeout": 300,  # 5 minutes
            "retry_flaky_tests": 2,
            "fail_on_warning": False,
            "capture_logs": True,
            "clean_environment": True
        }
        
        # Update kwargs with defaults
        for key, value in testing_defaults.items():
            if key not in kwargs:
                kwargs[key] = value
                
        super().__init__(**kwargs)
    
    def _validate(self):
        """Validate testing-specific configuration."""
        super()._validate()
        
        # Validate test types
        valid_types = ["unit", "integration", "e2e", "performance", "security", "accessibility", "compatibility", "smoke"]
        if not hasattr(self, 'test_types') or not self.test_types:
            raise ValueError("At least one test type must be specified")
            
        for test_type in self.test_types:
            if test_type not in valid_types:
                raise ValueError(f"Unknown test type: {test_type}")
        
        # Validate coverage threshold
        if not hasattr(self, 'coverage_threshold') or not (0 <= self.coverage_threshold <= 100):
            raise ValueError("coverage_threshold must be between 0 and 100")
        
        # Validate test timeout
        if not hasattr(self, 'test_timeout') or self.test_timeout <= 0:
            raise ValueError("test_timeout must be positive")


class KubernetesHandlerConfig(HandlerConfig):
    """Configuration for Kubernetes handlers."""
    
    def __init__(self, **kwargs):
        # Kubernetes-specific defaults
        k8s_defaults = {
            "namespaces": ["development", "staging", "production"],
            "rbac_enabled": True,
            "config_path": None,  # Use default kubeconfig location
            "context": None,  # Use current context
            "resource_quotas_enabled": True,
            "pod_security_policies_enabled": True,
            "network_policies_enabled": True,
            "service_mesh_enabled": False,
            "ingress_classes": ["nginx"],
            "storage_classes": ["standard"],
            "label_selector": {},
            "deploy_timeout": 600,  # 10 minutes
            "rollback_on_failure": True
        }
        
        # Update kwargs with defaults
        for key, value in k8s_defaults.items():
            if key not in kwargs:
                kwargs[key] = value
                
        super().__init__(**kwargs)
    
    def _validate(self):
        """Validate Kubernetes-specific configuration."""
        super()._validate()
        
        # Validate namespaces
        if not hasattr(self, 'namespaces') or not self.namespaces:
            raise ValueError("At least one namespace must be specified")
        
        # Validate deploy timeout
        if not hasattr(self, 'deploy_timeout') or self.deploy_timeout <= 0:
            raise ValueError("deploy_timeout must be positive")


class SecurityScanHandlerConfig(HandlerConfig):
    """Configuration for security scan handlers."""
    
    def __init__(self, **kwargs):
        # Security scan-specific defaults
        security_defaults = {
            "scan_types": ["sast", "dast", "dependency", "secret", "compliance"],
            "tools": {
                "sast": ["sonarqube", "checkmarx", "semgrep"],
                "dast": ["zap", "burpsuite"],
                "dependency": ["snyk", "dependabot"],
                "secret": ["gitleaks", "trufflehog"],
                "compliance": ["chef-inspec", "prisma-cloud"]
            },
            "severity_thresholds": {
                "critical": 0,
                "high": 0,
                "medium": 10,
                "low": 100
            },
            "scan_schedule": "on-demand",  # on-demand, daily, weekly
            "fail_on_findings": True,
            "report_formats": ["json", "html", "pdf"],
            "scan_timeout": 1800,  # 30 minutes
            "auto_remediation": False,
            "exclude_patterns": [],
            "baseline_enabled": False
        }
        
        # Update kwargs with defaults
        for key, value in security_defaults.items():
            if key not in kwargs:
                kwargs[key] = value
                
        super().__init__(**kwargs)
    
    def _validate(self):
        """Validate security scan-specific configuration."""
        super()._validate()
        
        # Validate scan types
        valid_scan_types = ["sast", "dast", "dependency", "secret", "compliance", "container", "iac"]
        if not hasattr(self, 'scan_types') or not self.scan_types:
            raise ValueError("At least one scan type must be specified")
            
        for scan_type in self.scan_types:
            if scan_type not in valid_scan_types:
                raise ValueError(f"Unknown scan type: {scan_type}")
        
        # Validate severity thresholds
        if not hasattr(self, 'severity_thresholds'):
            raise ValueError("severity_thresholds must be specified")
            
        valid_severities = ["critical", "high", "medium", "low"]
        for severity in valid_severities:
            if severity not in self.severity_thresholds:
                raise ValueError(f"Threshold for severity '{severity}' must be specified")
                
            if not isinstance(self.severity_thresholds[severity], int) or self.severity_thresholds[severity] < 0:
                raise ValueError(f"Threshold for severity '{severity}' must be a non-negative integer")
        
        # Validate scan schedule
        valid_schedules = ["on-demand", "daily", "weekly", "monthly"]
        if not hasattr(self, 'scan_schedule') or self.scan_schedule not in valid_schedules:
            raise ValueError(f"scan_schedule must be one of {valid_schedules}")


class ComplianceHandlerConfig(HandlerConfig):
    """Configuration for compliance handlers."""
    
    def __init__(self, **kwargs):
        # Compliance-specific defaults
        compliance_defaults = {
            "frameworks": ["pci-dss", "gdpr", "hipaa", "sox", "iso27001"],
            "scan_mode": "full",  # full, incremental, targeted
            "evidence_collection": True,
            "report_level": "detailed",  # summary, detailed, comprehensive
            "controls_mapping": {},
            "exclusions": [],
            "include_justifications": True,
            "auto_remediation_suggestions": True,
            "compliance_threshold": 100,  # percentage
            "scan_timeout": 3600,  # 1 hour
            "continuous_monitoring": False
        }
        
        # Update kwargs with defaults
        for key, value in compliance_defaults.items():
            if key not in kwargs:
                kwargs[key] = value
                
        super().__init__(**kwargs)
    
    def _validate(self):
        """Validate compliance-specific configuration."""
        super()._validate()
        
        # Validate frameworks
        if not hasattr(self, 'frameworks') or not self.frameworks:
            raise ValueError("At least one compliance framework must be specified")
        
        # Validate scan mode
        valid_modes = ["full", "incremental", "targeted"]
        if not hasattr(self, 'scan_mode') or self.scan_mode not in valid_modes:
            raise ValueError(f"scan_mode must be one of {valid_modes}")
        
        # Validate report level
        valid_levels = ["summary", "detailed", "comprehensive"]
        if not hasattr(self, 'report_level') or self.report_level not in valid_levels:
            raise ValueError(f"report_level must be one of {valid_levels}")
        
        # Validate compliance threshold
        if not hasattr(self, 'compliance_threshold') or not (0 <= self.compliance_threshold <= 100):
            raise ValueError("compliance_threshold must be between 0 and 100")


class DatabaseMigrationHandlerConfig(HandlerConfig):
    """Configuration for database migration handlers."""
    
    def __init__(self, **kwargs):
        # Database migration-specific defaults
        migration_defaults = {
            "migration_tools": {
                "sql": ["flyway", "liquibase"],
                "orm": ["alembic", "django-migrations", "sequelize"]
            },
            "supported_databases": ["postgresql", "mysql", "sqlite", "sqlserver", "oracle"],
            "backup_before_migration": True,
            "validate_schema": True,
            "auto_generate_migration": False,
            "migration_timeout": 1800,  # 30 minutes
            "sequential_execution": True,
            "transaction_per_migration": True,
            "verify_integrity": True,
            "dry_run": False,
            "migration_naming_convention": "timestamp_description"
        }
        
        # Update kwargs with defaults
        for key, value in migration_defaults.items():
            if key not in kwargs:
                kwargs[key] = value
                
        super().__init__(**kwargs)
    
    def _validate(self):
        """Validate database migration-specific configuration."""
        super()._validate()
        
        # Validate supported databases
        if not hasattr(self, 'supported_databases') or not self.supported_databases:
            raise ValueError("At least one supported database must be specified")
        
        # Validate migration timeout
        if not hasattr(self, 'migration_timeout') or self.migration_timeout <= 0:
            raise ValueError("migration_timeout must be positive")
        
        # Validate naming convention
        valid_conventions = ["timestamp_description", "sequential_description", "semantic_version"]
        if not hasattr(self, 'migration_naming_convention') or self.migration_naming_convention not in valid_conventions:
            raise ValueError(f"migration_naming_convention must be one of {valid_conventions}")


class SystemIntegrationHandlerConfig(HandlerConfig):
    """Configuration for system integration handlers."""
    
    def __init__(self, **kwargs):
        # System integration-specific defaults
        integration_defaults = {
            "supported_systems": [
                "erp", "crm", "payment-processors", "identity-providers", "blockchain-networks"
            ],
            "integration_patterns": ["api", "event-streaming", "file-transfer", "messaging"],
            "authentication_methods": ["oauth2", "api-key", "certificate"],
            "retry_strategy": "exponential-backoff",  # none, fixed, exponential-backoff
            "circuit_breaker_enabled": True,
            "timeout_per_operation": 60,  # seconds
            "rate_limiting_enabled": True,
            "max_requests_per_second": 10,
            "data_validation": True,
            "log_payloads": False,
            "idempotency_enabled": True
        }
        
        # Update kwargs with defaults
        for key, value in integration_defaults.items():
            if key not in kwargs:
                kwargs[key] = value
                
        super().__init__(**kwargs)
    
    def _validate(self):
        """Validate system integration-specific configuration."""
        super()._validate()
        
        # Validate supported systems
        if not hasattr(self, 'supported_systems') or not self.supported_systems:
            raise ValueError("At least one supported system must be specified")
        
        # Validate integration patterns
        valid_patterns = ["api", "event-streaming", "file-transfer", "messaging", "shared-database", "rpc"]
        if not hasattr(self, 'integration_patterns') or not self.integration_patterns:
            raise ValueError("At least one integration pattern must be specified")
            
        for pattern in self.integration_patterns:
            if pattern not in valid_patterns:
                raise ValueError(f"Unknown integration pattern: {pattern}")
        
        # Validate authentication methods
        valid_auth = ["oauth2", "api-key", "certificate", "basic", "jwt", "saml"]
        if not hasattr(self, 'authentication_methods') or not self.authentication_methods:
            raise ValueError("At least one authentication method must be specified")
            
        for auth in self.authentication_methods:
            if auth not in valid_auth:
                raise ValueError(f"Unknown authentication method: {auth}")
        
        # Validate retry strategy
        valid_strategies = ["none", "fixed", "exponential-backoff", "custom"]
        if not hasattr(self, 'retry_strategy') or self.retry_strategy not in valid_strategies:
            raise ValueError(f"retry_strategy must be one of {valid_strategies}")
        
        # Validate timeout
        if not hasattr(self, 'timeout_per_operation') or self.timeout_per_operation <= 0:
            raise ValueError("timeout_per_operation must be positive")
        
        # Validate rate limiting
        if hasattr(self, 'rate_limiting_enabled') and self.rate_limiting_enabled:
            if not hasattr(self, 'max_requests_per_second') or self.max_requests_per_second <= 0:
                raise ValueError("When rate_limiting_enabled is True, max_requests_per_second must be positive")


class TextGeneratorConfig(OutputGeneratorConfig):
    """Configuration for text generators."""
    
    def __init__(self, **kwargs):
        # Text-specific defaults
        text_defaults = {
            "model_name": "gpt2",               # Generation model
            "decoding_strategy": "auto_regressive",  # Decoding strategy
            "max_new_tokens": 512,              # Maximum new tokens to generate
            "min_new_tokens": 10,               # Minimum new tokens to generate
            "stop_sequences": [],               # Sequences that stop generation
            "sampling_strategy": "top_p",       # Sampling strategy
            "prompt_template": None,            # Template for generation prompt
            "use_system_prompt": True,          # Whether to use system prompt
            "system_prompt": "You are a helpful assistant that provides accurate information.",
            "use_prompt_engineering": True,     # Whether to use prompt engineering
            "prompt_strategies": ["cot", "examples"],  # List of prompt strategies
            "formality": "neutral",             # Formality level
            "max_context_length": 2048,         # Maximum context length
            "sliding_window": True,             # Use sliding window for long contexts
            "include_reasoning_trace": True,    # Include CoT reasoning traces
            "inject_reflexion": False,          # Inject reflexion into generation
            "preamble": "",                     # Text to prepend to all outputs
            "postamble": "",                    # Text to append to all outputs
            "reranking": False,                 # Whether to use reranking
            "reranking_criteria": ["relevance", "coherence"],  # Reranking criteria
        }
        
        # Update kwargs with defaults
        for key, value in text_defaults.items():
            if key not in kwargs:
                kwargs[key] = value
                
        super().__init__(**kwargs)
    
    def _validate(self):
        """Validate text-specific configuration."""
        super()._validate()
        
        # Validate decoding strategy
        valid_decoding = ["auto_regressive", "non_auto_regressive", "hybrid"]
        if self.decoding_strategy not in valid_decoding:
            raise ValueError(f"decoding_strategy must be one of {valid_decoding}")
        
        # Validate sampling strategy
        valid_sampling = ["top_p", "top_k", "temperature", "beam", "greedy", "contrastive"]
        if self.sampling_strategy not in valid_sampling:
            raise ValueError(f"sampling_strategy must be one of {valid_sampling}")
        
        # Validate token constraints
        if self.max_new_tokens <= 0:
            raise ValueError(f"max_new_tokens must be positive")
        
        if self.min_new_tokens < 0:
            raise ValueError(f"min_new_tokens must be non-negative")
        
        if self.min_new_tokens > self.max_new_tokens:
            raise ValueError(f"min_new_tokens must be <= max_new_tokens")
        
        # Validate formality
        valid_formality = ["casual", "neutral", "formal"]
        if self.formality not in valid_formality:
            raise ValueError(f"formality must be one of {valid_formality}")
        
        # Validate context length
        if self.max_context_length <= 0:
            raise ValueError(f"max_context_length must be positive")
        
        # Validate prompt strategies
        valid_strategies = ["cot", "examples", "step_by_step", "persona", "preamble", "reframing"]
        for strategy in self.prompt_strategies:
            if strategy not in valid_strategies:
                raise ValueError(f"prompt_strategy must be one of {valid_strategies}")
        
        # Validate reranking criteria
        if self.reranking:
            valid_criteria = ["relevance", "coherence", "novelty", "factuality", "diversity"]
            for criterion in self.reranking_criteria:
                if criterion not in valid_criteria:
                    raise ValueError(f"reranking_criterion must be one of {valid_criteria}")


class VisualGeneratorConfig(OutputGeneratorConfig):
    """Configuration for visual generators."""
    
    def __init__(self, **kwargs):
        # Visual-specific defaults
        visual_defaults = {
            "visualization_types": ["chart", "diagram", "image"],  # Types of visualizations
            "default_width": 800,               # Default width in pixels
            "default_height": 600,              # Default height in pixels
            "dpi": 100,                         # DPI for raster images
            "format": "png",                    # Default output format
            "color_scheme": "default",          # Color scheme
            "interactive": False,               # Whether to generate interactive visualizations
            "use_vector_formats": True,         # Whether to use vector formats when possible
            "font_family": "sans-serif",        # Default font family
            "font_size": 12,                    # Default font size
            "use_gpu_acceleration": True,       # Whether to use GPU acceleration
            "auto_layout": True,                # Whether to use automatic layout
            "include_annotations": True,        # Whether to include annotations
            "max_elements": 100,                # Maximum number of elements in visualization
            "style": "modern",                  # Visual style
            "high_contrast": False,             # Whether to use high contrast
            "optimization_level": "medium",     # Level of optimization
            "include_text_labels": True,        # Whether to include text labels
            "accessibility_features": True,     # Whether to include accessibility features
            "dynamic_resolution": True,         # Whether to adapt resolution dynamically
        }
        
        # Update kwargs with defaults
        for key, value in visual_defaults.items():
            if key not in kwargs:
                kwargs[key] = value
                
        super().__init__(**kwargs)
    
    def _validate(self):
        """Validate visual-specific configuration."""
        super()._validate()
        
        # Validate dimensions
        if self.default_width <= 0:
            raise ValueError(f"default_width must be positive")
        
        if self.default_height <= 0:
            raise ValueError(f"default_height must be positive")
        
        if self.dpi <= 0:
            raise ValueError(f"dpi must be positive")
        
        # Validate format
        valid_formats = ["png", "jpg", "jpeg", "svg", "pdf", "webp", "gif"]
        if self.format not in valid_formats:
            raise ValueError(f"format must be one of {valid_formats}")
        
        # Validate visualization types
        valid_types = ["chart", "diagram", "graph", "image", "table", "map", "plot"]
        for vis_type in self.visualization_types:
            if vis_type not in valid_types:
                raise ValueError(f"visualization_type must be one of {valid_types}")
        
        # Validate style
        valid_styles = ["modern", "classic", "minimal", "dark", "light", "colorful"]
        if self.style not in valid_styles:
            raise ValueError(f"style must be one of {valid_styles}")
        
        # Validate optimization level
        valid_levels = ["low", "medium", "high"]
        if self.optimization_level not in valid_levels:
            raise ValueError(f"optimization_level must be one of {valid_levels}")
        
        # Validate font
        if self.font_size <= 0:
            raise ValueError(f"font_size must be positive")
        
        # Validate element limit
        if self.max_elements <= 0:
            raise ValueError(f"max_elements must be positive")


class ActionGeneratorConfig(OutputGeneratorConfig):
    """Configuration for action generators."""
    
    def __init__(self, **kwargs):
        # Action-specific defaults
        action_defaults = {
            "action_types": ["command", "api_call", "query"],  # Types of actions
            "execution_engine": "sandbox",      # Execution environment
            "validation_level": "high",         # Level of validation
            "timeout": 30.0,                    # Timeout in seconds
            "max_steps": 10,                    # Maximum number of steps
            "parallelism": False,               # Whether to allow parallel execution
            "sandboxing": True,                 # Whether to use sandboxing
            "fail_fast": False,                 # Whether to fail fast on errors
            "retry_attempts": 3,                # Number of retry attempts
            "verify_outputs": True,             # Whether to verify outputs
            "allowed_apis": [],                 # List of allowed APIs
            "disallowed_apis": [],              # List of disallowed APIs
            "execution_mode": "dry_run",        # Execution mode
            "state_persistence": False,         # Whether to persist state
            "logging_level": "info",            # Logging level
            "max_memory": 1024,                 # Max memory in MB
            "max_runtime": 60.0,                # Max runtime in seconds
            "abort_on_timeout": True,           # Whether to abort on timeout
            "environment_variables": {},        # Environment variables
            "capture_output": True,             # Whether to capture output
        }
        
        # Update kwargs with defaults
        for key, value in action_defaults.items():
            if key not in kwargs:
                kwargs[key] = value
                
        super().__init__(**kwargs)
    
    def _validate(self):
        """Validate action-specific configuration."""
        super()._validate()
        
        # Validate action types
        valid_types = ["command", "api_call", "query", "function", "workflow", "script"]
        for action_type in self.action_types:
            if action_type not in valid_types:
                raise ValueError(f"action_type must be one of {valid_types}")
        
        # Validate execution engine
        valid_engines = ["sandbox", "virtual_machine", "container", "native", "emulator"]
        if self.execution_engine not in valid_engines:
            raise ValueError(f"execution_engine must be one of {valid_engines}")
        
        # Validate validation level
        valid_levels = ["none", "low", "medium", "high", "extreme"]
        if self.validation_level not in valid_levels:
            raise ValueError(f"validation_level must be one of {valid_levels}")
        
        # Validate execution mode
        valid_modes = ["dry_run", "live", "simulated", "monitored"]
        if self.execution_mode not in valid_modes:
            raise ValueError(f"execution_mode must be one of {valid_modes}")
        
        # Validate logging level
        valid_logging = ["debug", "info", "warning", "error", "critical"]
        if self.logging_level not in valid_logging:
            raise ValueError(f"logging_level must be one of {valid_logging}")
        
        # Validate numerical parameters
        if self.timeout <= 0:
            raise ValueError(f"timeout must be positive")
        
        if self.max_steps <= 0:
            raise ValueError(f"max_steps must be positive")
        
        if self.retry_attempts < 0:
            raise ValueError(f"retry_attempts must be non-negative")
        
        if self.max_memory <= 0:
            raise ValueError(f"max_memory must be positive")
        
        if self.max_runtime <= 0:
            raise ValueError(f"max_runtime must be positive")


class MultiModalSynthesizerConfig(OutputGeneratorConfig):
    """Configuration for multi-modal synthesizers."""
    
    def __init__(self, **kwargs):
        # Multi-modal specific defaults
        mm_defaults = {
            "modality_types": ["text", "visual", "action"],  # Modality types
            "modality_ratios": {"text": 0.6, "visual": 0.3, "action": 0.1},  # Ratios
            "synchronization": "sequential",    # Synchronization strategy
            "consistency_checking": True,       # Whether to check consistency
            "content_planning": True,           # Whether to use content planning
            "coherence_threshold": 0.7,         # Threshold for coherence
            "interleaving": True,               # Whether to interleave modalities
            "modality_configs": {},             # Configs for individual modalities
            "primary_modality": "text",         # Primary modality
            "fallback_modality": "text",        # Fallback modality
            "complementary_modalities": True,   # Whether to use complementary modalities
            "redundancy_level": "low",          # Level of redundancy
            "optimize_bandwidth": True,         # Whether to optimize bandwidth
            "adaptive_selection": True,         # Whether to adapt modality selection
            "layout_engine": "flow",            # Layout engine
            "response_template": None,          # Template for responses
            "max_modalities_per_response": 5,   # Maximum modalities per response
            "modality_selection_strategy": "content_based",  # Selection strategy
            "enable_runtime_adaptation": True,  # Whether to adapt during runtime
        }
        
        # Update kwargs with defaults
        for key, value in mm_defaults.items():
            if key not in kwargs:
                kwargs[key] = value
                
        super().__init__(**kwargs)
    
    def _validate(self):
        """Validate multi-modal specific configuration."""
        super()._validate()
        
        # Validate modality types
        valid_types = ["text", "visual", "action", "audio", "haptic", "spatial"]
        for modality_type in self.modality_types:
            if modality_type not in valid_types:
                raise ValueError(f"modality_type must be one of {valid_types}")
        
        # Validate primary and fallback modalities
        if self.primary_modality not in self.modality_types:
            raise ValueError(f"primary_modality must be one of {self.modality_types}")
        
        if self.fallback_modality not in self.modality_types:
            raise ValueError(f"fallback_modality must be one of {self.modality_types}")
        
        # Validate modality ratios
        if self.modality_ratios:
            if not all(m in self.modality_types for m in self.modality_ratios):
                raise ValueError(f"modality_ratios keys must be in modality_types")
            
            if not all(0 <= r <= 1 for r in self.modality_ratios.values()):
                raise ValueError(f"modality_ratios values must be in range [0, 1]")
            
            total = sum(self.modality_ratios.values())
            if not 0.99 <= total <= 1.01:  # Allow for floating-point error
                raise ValueError(f"modality_ratios must sum to approximately 1.0, got {total}")
        
        # Validate synchronization
        valid_sync = ["sequential", "parallel", "adaptive", "hierarchical"]
        if self.synchronization not in valid_sync:
            raise ValueError(f"synchronization must be one of {valid_sync}")
        
        # Validate coherence threshold
        if not 0 <= self.coherence_threshold <= 1:
            raise ValueError(f"coherence_threshold must be in range [0, 1]")
        
        # Validate redundancy level
        valid_redundancy = ["none", "low", "medium", "high"]
        if self.redundancy_level not in valid_redundancy:
            raise ValueError(f"redundancy_level must be one of {valid_redundancy}")
        
        # Validate layout engine
        valid_layouts = ["flow", "grid", "adaptive", "hierarchical", "semantic"]
        if self.layout_engine not in valid_layouts:
            raise ValueError(f"layout_engine must be one of {valid_layouts}")
        
        # Validate modality selection strategy
        valid_strategies = ["content_based", "user_preference", "context_aware", "fixed"]
        if self.modality_selection_strategy not in valid_strategies:
            raise ValueError(f"modality_selection_strategy must be one of {valid_strategies}")
        
        # Validate max modalities
        if self.max_modalities_per_response <= 0:
            raise ValueError(f"max_modalities_per_response must be positive")


class OutputGenerator(ABC):
    """
    Abstract base class for output generators.
    
    This class defines the interface and common functionality for
    all output modality generators.
    """
    
    def __init__(self, config: OutputGeneratorConfig):
        """
        Initialize the generator with configuration.
        
        Args:
            config: Configuration for the generator
        """
        self.config = config
        self.device = config.device
        
        # Set precision
        self.dtype = {
            'float32': torch.float32,
            'float16': torch.float16,
            'bfloat16': torch.bfloat16 if hasattr(torch, 'bfloat16') else torch.float16
        }[config.precision]
        
        # Create cache if enabled
        self.cache = OrderedDict()
        self.cache_enabled = config.use_cache and config.cache_size > 0
        self.cache_size = config.cache_size
        
        # Register generator type
        self._register_generator()
        
        logger.debug(f"Initialized {self.__class__.__name__} with config: {config.to_dict()}")
    
    def _register_generator(self):
        """Register this generator in the global registry."""
        generator_type = self.__class__.__name__
        _REGISTERED_GENERATORS[generator_type] = self.__class__
    
    def _compute_hash(self, input_data: Any) -> str:
        """
        Compute a hash for cache lookup.
        
        Args:
            input_data: Input data to be hashed
            
        Returns:
            str: Hash string
        """
        try:
            if isinstance(input_data, str):
                # Hash string directly
                return hashlib.sha256(input_data.encode('utf-8')).hexdigest()
            elif isinstance(input_data, dict):
                # Sort dict items and concatenate
                items = sorted((str(k), str(v)) for k, v in input_data.items())
                return hashlib.sha256(str(items).encode('utf-8')).hexdigest()
            else:
                # Try to pickle and hash
                data_bytes = pickle.dumps(input_data)
                return hashlib.sha256(data_bytes).hexdigest()
        except Exception as e:
            logger.warning(f"Failed to hash input for caching: {str(e)}")
            return None
    
    def _manage_cache_size(self):
        """
        Ensure the cache does not exceed the maximum allowed size by removing the oldest items.
        """
        if len(self.cache) > self.cache_size:
            # Remove oldest items
            for _ in range(len(self.cache) - self.cache_size):
                self.cache.popitem(last=False)
    
    @abstractmethod
    def generate(self, 
                 input_data: Any, 
                 context: Optional[Dict[str, Any]] = None, 
                 **kwargs) -> Any:
        """
        Generate output from input data.
        
        Args:
            input_data: Input data for generation
            context: Optional context information
            **kwargs: Additional arguments for generation
            
        Returns:
            Any: Generated output
        """
        pass
    
    def clear_cache(self):
        """Clear the generation cache."""
        self.cache.clear()
        
    def to(self, device):
        """
        Move generator to specified device.
        
        Args:
            device: Target device
            
        Returns:
            OutputGenerator: Self
        """
        self.device = device
        return self
    
    def save(self, directory: str):
        """
        Save generator and configuration.
        
        Args:
            directory: Directory to save in
        """
        os.makedirs(directory, exist_ok=True)
        
        # Save configuration
        config_path = os.path.join(directory, "config.json")
        self.config.save(config_path)
        
        # Save model if applicable
        if hasattr(self, '_save_model'):
            self._save_model(directory)
        
        logger.info(f"Generator saved to {directory}")
    
    @classmethod
    def load(cls, directory: str, device=None):
        """
        Load generator from saved files.
        
        Args:
            directory: Directory containing saved generator
            device: Device to load onto
            
        Returns:
            OutputGenerator: Loaded generator
        """
        # Load configuration
        config_path = os.path.join(directory, "config.json")
        config = cls._config_class().load(config_path)
        
        # Override device if specified
        if device is not None:
            config.device = device
        
        # Create instance
        instance = cls(config)
        
        # Load model if applicable
        if hasattr(instance, '_load_model'):
            instance._load_model(directory)
        
        logger.info(f"Generator loaded from {directory}")
        return instance
    
    @classmethod
    def _config_class(cls) -> Type[OutputGeneratorConfig]:
        """Get the configuration class for this generator."""
        # Default implementation - override in subclasses
        return OutputGeneratorConfig


class ContentPlanner:
    """
    Content planner for organizing multi-modal responses.
    
    This class is responsible for determining the optimal structure,
    modality mix, and sequencing of content in responses.
    """
    
    def __init__(
        self,
        modality_types: List[str],
        modality_ratios: Dict[str, float],
        primary_modality: str,
        max_modalities: int = 5,
        coherence_threshold: float = 0.7,
        selection_strategy: str = "content_based"
    ):
        """
        Initialize content planner.
        
        Args:
            modality_types: List of available modality types
            modality_ratios: Target ratio for each modality
            primary_modality: Primary modality to always include
            max_modalities: Maximum number of modalities in a response
            coherence_threshold: Threshold for modality coherence
            selection_strategy: Strategy for modality selection
        """
        self.modality_types = modality_types
        self.modality_ratios = modality_ratios
        self.primary_modality = primary_modality
        self.max_modalities = max_modalities
        self.coherence_threshold = coherence_threshold
        self.selection_strategy = selection_strategy
        
        # Validate inputs
        self._validate_inputs()
        
        logger.debug(f"Initialized ContentPlanner with {len(modality_types)} modality types")
    
    def _validate_inputs(self):
        """Validate input parameters."""
        if self.primary_modality not in self.modality_types:
            raise ValueError(f"primary_modality must be in modality_types")
        
        if self.modality_ratios:
            if not all(m in self.modality_types for m in self.modality_ratios):
                raise ValueError(f"All keys in modality_ratios must be in modality_types")
                
            # Ensure all modality types have a ratio
            for modality in self.modality_types:
                if modality not in self.modality_ratios:
                    self.modality_ratios[modality] = 0.0
            
            # Normalize ratios to sum to 1.0
            total = sum(self.modality_ratios.values())
            if total > 0:
                self.modality_ratios = {k: v / total for k, v in self.modality_ratios.items()}
    
    def plan_content(
        self,
        query: str,
        context: Dict[str, Any],
        available_modalities: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """
        Create a content plan for a response.
        
        Args:
            query: User query or input
            context: Context information
            available_modalities: Optional list of available modalities
            
        Returns:
            Dict[str, Any]: Content plan with modality assignments
        """
        # Use available modalities if provided, otherwise use all modality types
        modalities = available_modalities or self.modality_types
        
        # Ensure primary modality is included
        if self.primary_modality not in modalities:
            modalities = [self.primary_modality] + modalities
        
        # Analyze query to determine appropriate modalities
        modality_scores = self._analyze_query(query, context, modalities)
        
        # Select modalities based on scores and strategy
        selected_modalities = self._select_modalities(modality_scores)
        
        # Plan structure and sequence
        structure = self._plan_structure(query, selected_modalities, context)
        
        # Create content plan
        plan = {
            "modalities": selected_modalities,
            "structure": structure,
            "primary_modality": self.primary_modality,
            "modality_assignments": self._assign_content(query, selected_modalities, context),
            "metadata": {
                "query": query,
                "modality_scores": modality_scores,
                "planning_strategy": self.selection_strategy,
                "context_features": self._extract_context_features(context)
            }
        }
        
        return plan
    
    def _analyze_query(
        self,
        query: str,
        context: Dict[str, Any],
        modalities: List[str]
    ) -> Dict[str, float]:
        """
        Analyze query to determine appropriate modalities.
        
        Args:
            query: User query or input
            context: Context information
            modalities: List of modalities to consider
            
        Returns:
            Dict[str, float]: Score for each modality
        """
        # Initialize scores with default ratios
        scores = {m: self.modality_ratios.get(m, 0.0) for m in modalities}
        
        # Boost score for primary modality
        if self.primary_modality in scores:
            scores[self.primary_modality] += 0.2
        
        # Analyze query for modality-specific keywords and patterns
        lower_query = query.lower()
        
        # Visual modality indicators
        visual_keywords = ["show", "display", "visualize", "chart", "graph", "diagram", "image", "plot"]
        if any(keyword in lower_query for keyword in visual_keywords) and "visual" in scores:
            scores["visual"] += 0.4
        
        # Action modality indicators
        action_keywords = ["do", "execute", "run", "perform", "action", "command", "operation"]
        if any(keyword in lower_query for keyword in action_keywords) and "action" in scores:
            scores["action"] += 0.4
        
        # Text modality indicators
        text_keywords = ["explain", "describe", "tell", "write", "summarize"]
        if any(keyword in lower_query for keyword in text_keywords) and "text" in scores:
            scores["text"] += 0.3
        
        # Consider context for additional cues
        if context.get("previous_modalities"):
            # Boost scores for previously successful modalities
            for modality in context["previous_modalities"]:
                if modality in scores:
                    scores[modality] += 0.1
        
        # Check for explicit modality requests
        explicit_requests = {
            "visual": ["image", "graph", "chart", "diagram", "visualize"],
            "text": ["text", "write", "summary"],
            "action": ["code", "command", "run", "execute"]
        }
        
        for modality, requests in explicit_requests.items():
            if modality in scores and any(f"in {r}" in lower_query or f"as {r}" in lower_query for r in requests):
                scores[modality] += 0.5
        
        # Normalize scores to sum to 1.0
        total = sum(scores.values())
        if total > 0:
            scores = {k: v / total for k, v in scores.items()}
        
        return scores
    
    def _select_modalities(self, modality_scores: Dict[str, float]) -> List[str]:
        """
        Select modalities based on scores and strategy.
        
        Args:
            modality_scores: Score for each modality
            
        Returns:
            List[str]: Selected modalities
        """
        # Always include primary modality
        selected = [self.primary_modality]
        
        # Different selection strategies
        if self.selection_strategy == "content_based":
            # Select modalities with scores above threshold
            threshold = max(self.coherence_threshold, 1.0 / len(modality_scores))
            
            for modality, score in sorted(modality_scores.items(), key=lambda x: x[1], reverse=True):
                if modality != self.primary_modality and score >= threshold:
                    selected.append(modality)
                    
                # Limit to max_modalities
                if len(selected) >= self.max_modalities:
                    break
                    
        elif self.selection_strategy == "fixed":
            # Use fixed modality ratios - select highest scores
            for modality, _ in sorted(modality_scores.items(), key=lambda x: x[1], reverse=True):
                if modality != self.primary_modality:
                    selected.append(modality)
                    
                # Limit to max_modalities
                if len(selected) >= self.max_modalities:
                    break
                    
        elif self.selection_strategy == "user_preference":
            # Prioritize user preferences if available
            preferences = modality_scores.get("user_preferences", {})
            
            if preferences:
                for modality, _ in sorted(preferences.items(), key=lambda x: x[1], reverse=True):
                    if modality != self.primary_modality and modality in modality_scores:
                        selected.append(modality)
                        
                    # Limit to max_modalities
                    if len(selected) >= self.max_modalities:
                        break
            else:
                # Fall back to content-based if no preferences
                for modality, score in sorted(modality_scores.items(), key=lambda x: x[1], reverse=True):
                    if modality != self.primary_modality and score >= self.coherence_threshold:
                        selected.append(modality)
                        
                    # Limit to max_modalities
                    if len(selected) >= self.max_modalities:
                        break
        
        # Ensure uniqueness
        return list(dict.fromkeys(selected))
    
    def _plan_structure(
        self,
        query: str,
        modalities: List[str],
        context: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """
        Plan the structure and sequence of content.
        
        Args:
            query: User query or input
            modalities: Selected modalities
            context: Context information
            
        Returns:
            List[Dict[str, Any]]: Structure plan
        """
        # Create initial structure based on query complexity and modalities
        structure = []
        
        # Simple classification of query complexity
        query_complexity = self._assess_query_complexity(query)
        
        if query_complexity == "simple":
            # For simple queries, use a straightforward structure
            
            # Start with introduction in primary modality
            structure.append({
                "role": "introduction",
                "modality": self.primary_modality,
                "purpose": "provide direct answer",
                "content_type": "answer"
            })
            
            # Add supplementary content in other modalities
            for modality in modalities:
                if modality != self.primary_modality:
                    structure.append({
                        "role": "supplementary",
                        "modality": modality,
                        "purpose": "enhance understanding",
                        "content_type": self._get_content_type(modality, query)
                    })
                    
        elif query_complexity == "moderate":
            # For moderate complexity, use a more structured approach
            
            # Start with introduction in primary modality
            structure.append({
                "role": "introduction",
                "modality": self.primary_modality,
                "purpose": "frame the response",
                "content_type": "framing"
            })
            
            # Add main content with appropriate modality
            main_modality = self._choose_main_modality(query, modalities)
            structure.append({
                "role": "main_content",
                "modality": main_modality,
                "purpose": "provide core information",
                "content_type": self._get_content_type(main_modality, query)
            })
            
            # Add explanatory content in primary modality
            if self.primary_modality != main_modality:
                structure.append({
                    "role": "explanation",
                    "modality": self.primary_modality,
                    "purpose": "explain main content",
                    "content_type": "explanation"
                })
                
            # Add supporting content in other modalities
            for modality in modalities:
                if modality != main_modality and modality != self.primary_modality:
                    structure.append({
                        "role": "supporting",
                        "modality": modality,
                        "purpose": "provide additional perspective",
                        "content_type": self._get_content_type(modality, query)
                    })
                    
            # Add conclusion in primary modality
            structure.append({
                "role": "conclusion",
                "modality": self.primary_modality,
                "purpose": "summarize key points",
                "content_type": "summary"
            })
            
        else:  # complex
            # For complex queries, use a comprehensive structure
            
            # Start with introduction in primary modality
            structure.append({
                "role": "introduction",
                "modality": self.primary_modality,
                "purpose": "introduce topic and approach",
                "content_type": "framing"
            })
            
            # Add context establishment
            structure.append({
                "role": "context",
                "modality": self.primary_modality,
                "purpose": "establish necessary background",
                "content_type": "background"
            })
            
            # Determine content sections
            sections = self._identify_content_sections(query)
            
            # For each section, assign appropriate modality
            for i, section in enumerate(sections):
                section_modality = self._choose_section_modality(section, modalities, i)
                structure.append({
                    "role": f"section_{i+1}",
                    "modality": section_modality,
                    "purpose": section["purpose"],
                    "content_type": section["type"]
                })
                
                # Add explanation if needed
                if section_modality != self.primary_modality:
                    structure.append({
                        "role": f"explanation_{i+1}",
                        "modality": self.primary_modality,
                        "purpose": "explain section content",
                        "content_type": "explanation"
                    })
                    
            # Add integration section
            structure.append({
                "role": "integration",
                "modality": self.primary_modality,
                "purpose": "integrate perspectives",
                "content_type": "integration"
            })
            
            # Add conclusion in primary modality
            structure.append({
                "role": "conclusion",
                "modality": self.primary_modality,
                "purpose": "summarize and provide next steps",
                "content_type": "summary"
            })
        
        return structure
    
    def _assign_content(
        self,
        query: str,
        modalities: List[str],
        context: Dict[str, Any]
    ) -> Dict[str, List[str]]:
        """
        Assign content to each modality.
        
        Args:
            query: User query or input
            modalities: Selected modalities
            context: Context information
            
        Returns:
            Dict[str, List[str]]: Content assignments
        """
        # Extract relevant concepts from query
        concepts = self._extract_key_concepts(query)
        
        # Initialize assignments
        assignments = {modality: [] for modality in modalities}
        
        # Assign concepts to modalities based on suitability
        for concept in concepts:
            # Determine best modality for this concept
            modality_matches = self._score_concept_modality_match(concept, modalities)
            best_modality = max(modality_matches.items(), key=lambda x: x[1])[0]
            
            # Assign concept to best modality
            assignments[best_modality].append(concept)
        
        # Ensure primary modality has comprehensive coverage
        primary_concepts = set(assignments[self.primary_modality])
        all_concepts = set(concepts)
        
        # Add missing concepts to primary modality
        missing_concepts = all_concepts - primary_concepts
        assignments[self.primary_modality].extend(missing_concepts)
        
        return assignments
    
    def _assess_query_complexity(self, query: str) -> str:
        """
        Assess the complexity of a query.
        
        Args:
            query: User query or input
            
        Returns:
            str: Complexity level ("simple", "moderate", or "complex")
        """
        # Simple heuristics for complexity assessment
        query_length = len(query.split())
        num_sentences = len(re.split(r'[.!?]+', query))
        
        # Count question marks as an indicator of complexity
        num_questions = query.count('?')
        
        # Look for indicators of complexity
        complexity_indicators = ["explain", "compare", "contrast", "analyze", "evaluate", 
                                "synthesize", "why", "how", "implications", "framework"]
        num_indicators = sum(1 for indicator in complexity_indicators if indicator in query.lower())
        
        # Calculate complexity score
        complexity_score = (query_length / 10) + (num_sentences * 2) + (num_questions * 3) + (num_indicators * 5)
        
        # Classify complexity
        if complexity_score < 10:
            return "simple"
        elif complexity_score < 25:
            return "moderate"
        else:
            return "complex"
    
    def _get_content_type(self, modality: str, query: str) -> str:
        """
        Determine appropriate content type for a modality based on query.
        
        Args:
            modality: Modality name
            query: User query or input
            
        Returns:
            str: Content type
        """
        # Define common content types for each modality
        modality_content_types = {
            "text": ["explanation", "narrative", "summary", "analysis", "instruction"],
            "visual": ["chart", "diagram", "graph", "image", "plot", "visualization"],
            "action": ["command", "api_call", "function", "query", "workflow"],
            "audio": ["speech", "sound", "music", "narration", "effect"]
        }
        
        if modality not in modality_content_types:
            return "content"
        
        # Select based on query
        lower_query = query.lower()
        
        # Check for specific content type requests
        for content_type in modality_content_types[modality]:
            if content_type in lower_query:
                return content_type
        
        # Choose based on query characteristics
        if modality == "visual":
            if any(term in lower_query for term in ["compare", "trend", "over time", "statistics"]):
                return "chart"
            elif any(term in lower_query for term in ["process", "flow", "structure", "relationship"]):
                return "diagram"
            elif any(term in lower_query for term in ["network", "connection", "linked"]):
                return "graph"
            else:
                return "visualization"
                
        elif modality == "text":
            if "explain" in lower_query or "how" in lower_query:
                return "explanation"
            elif any(term in lower_query for term in ["summarize", "brief", "overview"]):
                return "summary"
            elif any(term in lower_query for term in ["analyze", "examine", "evaluate"]):
                return "analysis"
            else:
                return "narrative"
                
        elif modality == "action":
            if any(term in lower_query for term in ["run", "execute", "perform"]):
                return "command"
            elif any(term in lower_query for term in ["call", "api", "service"]):
                return "api_call"
            elif any(term in lower_query for term in ["query", "search", "find"]):
                return "query"
            else:
                return "function"
        
        # Default to first content type
        return modality_content_types[modality][0]
    
    def _choose_main_modality(self, query: str, modalities: List[str]) -> str:
        """
        Choose the main modality for presenting content.
        
        Args:
            query: User query or input
            modalities: Selected modalities
            
        Returns:
            str: Main modality
        """
        # Default to primary modality
        if len(modalities) == 1:
            return modalities[0]
        
        # Define modality affinity for different query types
        query_types = {
            "visual": ["show", "display", "visualize", "plot", "draw", "chart", "graph"],
            "action": ["run", "execute", "perform", "implement", "code"],
            "text": ["explain", "describe", "tell", "summarize", "elaborate"]
        }
        
        # Score each modality based on query
        scores = {modality: 0 for modality in modalities}
        
        # Add base scores from modality ratios
        for modality in scores:
            scores[modality] += self.modality_ratios.get(modality, 0)
        
        # Add scores based on query terms
        lower_query = query.lower()
        for modality, terms in query_types.items():
            if modality in scores:
                for term in terms:
                    if term in lower_query:
                        scores[modality] += 0.2
        
        # Choose highest scoring modality
        return max(scores.items(), key=lambda x: x[1])[0]
    
    def _identify_content_sections(self, query: str) -> List[Dict[str, Any]]:
        """
        Identify content sections for a complex query.
        
        Args:
            query: User query or input
            
        Returns:
            List[Dict[str, Any]]: Content sections
        """
        # Extract key aspects of the query
        aspects = self._extract_query_aspects(query)
        
        # Convert aspects to content sections
        sections = []
        
        for i, aspect in enumerate(aspects):
            # Determine purpose and type based on aspect
            if "compare" in aspect.lower() or "contrast" in aspect.lower():
                purpose = "compare aspects"
                content_type = "comparison"
            elif "explain" in aspect.lower() or "how" in aspect.lower():
                purpose = "explain process"
                content_type = "explanation"
            elif "list" in aspect.lower() or "enumerate" in aspect.lower():
                purpose = "list items"
                content_type = "enumeration"
            elif "analyze" in aspect.lower() or "examine" in aspect.lower():
                purpose = "analyze topic"
                content_type = "analysis"
            else:
                purpose = "address aspect"
                content_type = "content"
            
            sections.append({
                "aspect": aspect,
                "purpose": purpose,
                "type": content_type
            })
        
        return sections
    
    def _choose_section_modality(
        self,
        section: Dict[str, Any],
        modalities: List[str],
        section_index: int
    ) -> str:
        """
        Choose the modality for a content section.
        
        Args:
            section: Content section information
            modalities: Selected modalities
            section_index: Index of section
            
        Returns:
            str: Chosen modality
        """
        # Define modality affinity for different content types
        content_type_affinities = {
            "comparison": {"visual": 0.8, "text": 0.6, "action": 0.3},
            "explanation": {"text": 0.8, "visual": 0.6, "action": 0.4},
            "enumeration": {"text": 0.7, "visual": 0.6, "action": 0.4},
            "analysis": {"text": 0.7, "visual": 0.8, "action": 0.5},
            "content": {"text": 0.7, "visual": 0.6, "action": 0.5}
        }
        
        # Get affinities for this content type
        content_type = section["type"]
        affinities = content_type_affinities.get(content_type, {"text": 0.7, "visual": 0.6, "action": 0.5})
        
        # Score each modality
        scores = {modality: affinities.get(modality, 0.5) for modality in modalities}
        
        # Adjust scores for modality distribution
        if section_index > 0:
            # Try to vary modalities for adjacent sections
            scores[self.primary_modality] -= 0.1 * (section_index % 2)
        
        # Choose highest scoring modality
        return max(scores.items(), key=lambda x: x[1])[0]
    
    def _extract_key_concepts(self, query: str) -> List[str]:
        """
        Extract key concepts from a query.
        
        Args:
            query: User query or input
            
        Returns:
            List[str]: Key concepts
        """
        # Simple extraction based on noun phrases and keywords
        # In a production system, this would use NLP techniques
        
        # Split query into sentences
        sentences = re.split(r'[.!?]+', query)
        concepts = []
        
        # Extract concepts from each sentence
        for sentence in sentences:
            # Split into words and remove stop words
            words = sentence.strip().split()
            
            # Identify potential concepts (noun phrases, etc.)
            if len(words) >= 3:
                # Extract 2-3 word phrases
                for i in range(len(words) - 1):
                    phrase = " ".join(words[i:i+2])
                    if len(phrase) > 5:  # Ignore very short phrases
                        concepts.append(phrase)
                        
                if len(words) >= 4:
                    for i in range(len(words) - 2):
                        phrase = " ".join(words[i:i+3])
                        if len(phrase) > 8:  # Longer threshold for 3-word phrases
                            concepts.append(phrase)
            
            # Add individual important words
            for word in words:
                if len(word) > 6 and word.lower() not in ["explain", "describe", "about", "would", "could", "should"]:
                    concepts.append(word)
        
        # Remove duplicates while preserving order
        return list(dict.fromkeys(concepts))
    
    def _score_concept_modality_match(
        self,
        concept: str,
        modalities: List[str]
    ) -> Dict[str, float]:
        """
        Score how well a concept matches each modality.
        
        Args:
            concept: Concept to score
            modalities: List of modalities
            
        Returns:
            Dict[str, float]: Score for each modality
        """
        # Define modality affinities for different concept types
        visual_affinities = ["visual", "image", "picture", "chart", "graph", "diagram", "map", "plot",
                           "display", "view", "illustration", "visualization", "color", "shape", "design"]
        
        action_affinities = ["action", "perform", "execute", "run", "code", "command", "operation",
                           "implement", "function", "process", "workflow", "procedure", "script"]
        
        text_affinities = ["explain", "describe", "text", "information", "story", "narrative", "theory",
                          "concept", "definition", "discussion", "explanation", "analysis", "argument"]
        
        # Score each modality
        scores = {modality: 0.5 for modality in modalities}  # Start with neutral scores
        
        # Check concept for modality affinity terms
        concept_lower = concept.lower()
        
        for term in visual_affinities:
            if term in concept_lower and "visual" in scores:
                scores["visual"] += 0.1
                
        for term in action_affinities:
            if term in concept_lower and "action" in scores:
                scores["action"] += 0.1
                
        for term in text_affinities:
            if term in concept_lower and "text" in scores:
                scores["text"] += 0.1
        
        # Apply modality ratios as base weights
        for modality in scores:
            scores[modality] *= self.modality_ratios.get(modality, 1.0)
        
        # Normalize scores
        total = sum(scores.values())
        if total > 0:
            scores = {k: v / total for k, v in scores.items()}
        
        return scores
    
    def _extract_query_aspects(self, query: str) -> List[str]:
        """
        Extract different aspects from a complex query.
        
        Args:
            query: User query or input
            
        Returns:
            List[str]: Query aspects
        """
        # Split into sentences
        sentences = re.split(r'[.!?]+', query)
        sentences = [s.strip() for s in sentences if s.strip()]
        
        # If multiple sentences, treat each as an aspect
        if len(sentences) > 1:
            return sentences
        
        # Check for explicit enumeration
        if ":" in query:
            parts = query.split(":")
            if len(parts) > 1:
                items = parts[1].split(",")
                if len(items) > 1:
                    return [f"{parts[0]}: {item.strip()}" for item in items]
        
        # Look for conjunction indicators
        indicators = ["and", "or", "also", "additionally", "moreover", "furthermore"]
        for indicator in indicators:
            if f" {indicator} " in query:
                parts = query.split(f" {indicator} ")
                if len(parts) > 1:
                    return parts
        
        # Fall back to creating artificial aspects
        if len(query.split()) > 15:
            # For long queries, split into approximately equal parts
            words = query.split()
            num_aspects = max(2, len(words) // 10)
            aspect_size = len(words) // num_aspects
            
            aspects = []
            for i in range(num_aspects):
                start = i * aspect_size
                end = start + aspect_size if i < num_aspects - 1 else len(words)
                aspects.append(" ".join(words[start:end]))
                
            return aspects
        
        # For short queries, treat the whole query as one aspect
        return [query]
    
    def _extract_context_features(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Extract relevant features from context.
        
        Args:
            context: Context information
            
        Returns:
            Dict[str, Any]: Extracted features
        """
        features = {}
        
        # Extract user preferences if available
        if "user_preferences" in context:
            features["user_preferences"] = context["user_preferences"]
        
        # Extract previous interaction patterns
        if "previous_modalities" in context:
            features["modality_history"] = context["previous_modalities"]
        
        # Extract device information
        if "device_info" in context:
            features["device_capabilities"] = {
                "supports_visual": context["device_info"].get("supports_visual", True),
                "supports_audio": context["device_info"].get("supports_audio", False),
                "supports_action": context["device_info"].get("supports_action", True)
            }
        
        # Extract content complexity preferences
        if "complexity_preference" in context:
            features["complexity_preference"] = context["complexity_preference"]
        
        return features


class ReasoningTraceFormatter:
    """
    Formatter for reasoning traces from meta-cognitive processes.
    
    This class transforms internal reasoning traces into formatted
    outputs suitable for inclusion in responses.
    """
    
    def __init__(
        self,
        include_trace: bool = True,
        trace_format: str = "text",
        trace_detail: str = "medium",
        max_trace_length: int = 500,
        show_uncertainties: bool = True
    ):
        """
        Initialize reasoning trace formatter.
        
        Args:
            include_trace: Whether to include reasoning traces
            trace_format: Format for traces ("text", "structured", "simplified")
            trace_detail: Level of detail ("low", "medium", "high")
            max_trace_length: Maximum length of formatted traces
            show_uncertainties: Whether to include uncertainty information
        """
        self.include_trace = include_trace
        self.trace_format = trace_format
        self.trace_detail = trace_detail
        self.max_trace_length = max_trace_length
        self.show_uncertainties = show_uncertainties
        
        logger.debug(f"Initialized ReasoningTraceFormatter with trace_format={trace_format}")
    
    def format_trace(
        self,
        reasoning_trace: Dict[str, Any],
        output_modality: str = "text"
    ) -> str:
        """
        Format a reasoning trace for inclusion in responses.
        
        Args:
            reasoning_trace: Reasoning trace from meta-cognitive system
            output_modality: Target output modality
            
        Returns:
            str: Formatted reasoning trace
        """
        if not self.include_trace or not reasoning_trace:
            return ""
        
        # Format trace based on specified format
        if self.trace_format == "text":
            return self._format_text_trace(reasoning_trace)
        elif self.trace_format == "structured":
            return self._format_structured_trace(reasoning_trace)
        elif self.trace_format == "simplified":
            return self._format_simplified_trace(reasoning_trace)
        else:
            return self._format_text_trace(reasoning_trace)
    
    def _format_text_trace(self, reasoning_trace: Dict[str, Any]) -> str:
        """
        Format trace as natural language text.
        
        Args:
            reasoning_trace: Reasoning trace from meta-cognitive system
            
        Returns:
            str: Formatted text trace
        """
        # Extract relevant trace components
        steps = reasoning_trace.get("steps", [])
        conclusion = reasoning_trace.get("conclusion", "")
        confidence = reasoning_trace.get("confidence", 0.0)
        uncertainties = reasoning_trace.get("uncertainties", {})
        
        # Build formatted trace
        formatted_trace = "Reasoning process:\n\n"
        
        # Add steps
        if steps:
            for i, step in enumerate(steps):
                step_str = f"{i+1}. {step.get('description', '')}"
                
                # Add supporting information for higher detail levels
                if self.trace_detail in ["medium", "high"] and "supporting_info" in step:
                    step_str += f"\n   {step['supporting_info']}"
                    
                # Add uncertainty information
                if self.show_uncertainties and self.trace_detail == "high" and "uncertainty" in step:
                    step_str += f"\n   (Confidence: {step.get('confidence', 0.0):.2f})"
                
                formatted_trace += step_str + "\n\n"
        
        # Add conclusion
        if conclusion:
            formatted_trace += f"Conclusion: {conclusion}\n"
            
            # Add confidence information
            if self.show_uncertainties:
                formatted_trace += f"Overall confidence: {confidence:.2f}\n"
        
        # Add uncertainties for high detail level
        if self.show_uncertainties and self.trace_detail == "high" and uncertainties:
            formatted_trace += "\nUncertainties:\n"
            for factor, value in uncertainties.items():
                formatted_trace += f"- {factor}: {value:.2f}\n"
        
        # Truncate if needed
        if len(formatted_trace) > self.max_trace_length:
            formatted_trace = formatted_trace[:self.max_trace_length - 3] + "..."
        
        return formatted_trace
    
    def _format_structured_trace(self, reasoning_trace: Dict[str, Any]) -> str:
        """
        Format trace as structured text with markdown formatting.
        
        Args:
            reasoning_trace: Reasoning trace from meta-cognitive system
            
        Returns:
            str: Formatted structured trace
        """
        # Extract relevant trace components
        steps = reasoning_trace.get("steps", [])
        conclusion = reasoning_trace.get("conclusion", "")
        confidence = reasoning_trace.get("confidence", 0.0)
        uncertainties = reasoning_trace.get("uncertainties", {})
        alternatives = reasoning_trace.get("alternatives", [])
        approach = reasoning_trace.get("approach", "")
        
        # Build formatted trace
        formatted_trace = "## Reasoning Process\n\n"
        
        # Add reasoning approach
        approach = reasoning_trace.get("approach", "")
        if approach and self.trace_detail in ["medium", "high"]:
            formatted_trace += f"**Approach**: {approach}\n\n"
        
        # Add steps
        if steps:
            for i, step in enumerate(steps):
                step_str = f"### Step {i+1}: {step.get('description', '')}\n\n"
                
                # Add reasoning
                if "reasoning" in step:
                    step_str += f"{step['reasoning']}\n\n"
                
                # Add intermediate conclusions
                if "conclusion" in step:
                    step_str += f"**Intermediate conclusion**: {step['conclusion']}\n\n"
                    
                # Add uncertainty information
                if self.show_uncertainties and self.trace_detail in ["medium", "high"] and "uncertainty" in step:
                    step_str += f"*Confidence: {step.get('confidence', 0.0):.2f}*\n\n"
                
                formatted_trace += step_str
        
        # Add conclusion
        if conclusion:
            formatted_trace += f"### Conclusion\n\n{conclusion}\n\n"
            
            # Add confidence information
            if self.show_uncertainties:
                formatted_trace += f"*Overall confidence: {confidence:.2f}*\n\n"
        
        # Add alternative perspectives for high detail level
        if self.trace_detail == "high" and alternatives:
            formatted_trace += "### Alternative Perspectives\n\n"
            for i, alternative in enumerate(alternatives):
                formatted_trace += f"**Alternative {i+1}**: {alternative.get('description', '')}\n\n"
                if "reason" in alternative:
                    formatted_trace += f"{alternative['reason']}\n\n"
        
        # Add uncertainties for medium/high detail level
        if self.show_uncertainties and self.trace_detail in ["medium", "high"] and uncertainties:
            formatted_trace += "### Uncertainties\n\n"
            for factor, value in uncertainties.items():
                formatted_trace += f"- **{factor}**: {value:.2f}\n"
        
        # Truncate if needed
        if len(formatted_trace) > self.max_trace_length:
            formatted_trace = formatted_trace[:self.max_trace_length - 3] + "..."
        
        return formatted_trace


