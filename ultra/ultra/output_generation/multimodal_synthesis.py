#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ULTRA Multimodal Synthesis Module

This module implements the MultiModalSynthesizer for the ULTRA (Ultimate Learning & Thought 
Reasoning Architecture) system, responsible for integrating outputs from different modality 
generators (text, visual, action) into coherent multimodal responses.

The MultiModalSynthesizer orchestrates:
- Content planning and modality selection 
- Synchronization between different modality outputs
- Consistency checking across modalities
- Adaptive selection based on context and query characteristics
- Integration with the Global Workspace and reasoning trace capabilities

It implements algorithms for:
- Coherence optimization
- Cross-modal attention mechanisms
- Content sequencing
- Layout management
- Adaptive complexity scaling
"""

import os
import sys
import json
import logging
import time
import math
import re
import uuid
import pickle
import hashlib
from typing import Dict, List, Tuple, Optional, Union, Any, Callable, Type, Set
from collections import OrderedDict, defaultdict, deque
from itertools import combinations
from datetime import datetime
from pathlib import Path

import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(name)s: %(message)s'
)
logger = logging.getLogger(__name__)

# Try to import optional dependencies
try:
    from sklearn.metrics.pairwise import cosine_similarity
    from sklearn.feature_extraction.text import TfidfVectorizer
    _HAS_SKLEARN = True
except ImportError:
    logger.warning("scikit-learn not found. Using fallback similarity methods.")
    _HAS_SKLEARN = False

try:
    import networkx as nx
    _HAS_NETWORKX = True
except ImportError:
    logger.warning("NetworkX not found. Using fallback graph algorithms.")
    _HAS_NETWORKX = False

try:
    import spacy
    _HAS_SPACY = True
    try:
        nlp = spacy.load("en_core_web_sm")
    except OSError:
        logger.warning("Default spaCy model not found. Using small model.")
        nlp = spacy.load("en_core_web_sm")
except ImportError:
    logger.warning("spaCy not found. Using fallback NLP methods.")
    _HAS_SPACY = False
    nlp = None

# Import from parent package
try:
    from ultra.utils.logging import log_execution_time
    from ultra.utils.config import load_config
    from ultra.utils.monitoring import performance_monitor
    _HAS_ULTRA_UTILS = True
except ImportError:
    logger.warning("ULTRA utilities not available. Using local fallbacks.")
    _HAS_ULTRA_UTILS = False
    
    # Fallback decorators
    def log_execution_time(func):
        def wrapper(*args, **kwargs):
            start_time = time.time()
            result = func(*args, **kwargs)
            end_time = time.time()
            logger.debug(f"{func.__name__} executed in {end_time - start_time:.4f} seconds")
            return result
        return wrapper
    
    def performance_monitor(func):
        return func

# Import from sibling modules
try:
    from ultra.text_output import TextGenerator, TextGeneratorConfig
    from ultra.visual_output import VisualGenerator, VisualGeneratorConfig
    from ultra.action_output import ActionGenerator, ActionGeneratorConfig
except ImportError:
    logger.warning("Generator modules not found. MultiModalSynthesizer may not function properly.")


class MultiModalSynthesizerConfig:
    """Configuration for MultiModalSynthesizer."""
    
    def __init__(self, **kwargs):
        """
        Initialize configuration with default values and overrides.
        
        Args:
            **kwargs: Overrides for default configuration values
        """
        # Common output generator configuration
        self.device = kwargs.get('device', 'cuda' if torch.cuda.is_available() else 'cpu')
        self.precision = kwargs.get('precision', 'float32')
        self.use_cache = kwargs.get('use_cache', True)
        self.cache_size = kwargs.get('cache_size', 1000)
        self.max_output_length = kwargs.get('max_output_length', 4096)
        self.min_output_length = kwargs.get('min_output_length', 10)
        self.enable_tracing = kwargs.get('enable_tracing', True)
        self.temperature = kwargs.get('temperature', 0.7)
        self.deterministic = kwargs.get('deterministic', False)
        self.include_metadata = kwargs.get('include_metadata', True)
        self.trust_level = kwargs.get('trust_level', 'high')
        self.dynamic_adaptation = kwargs.get('dynamic_adaptation', True)
        
        # Multimodal specific configuration
        self.modality_types = kwargs.get('modality_types', ['text', 'visual', 'action'])
        self.modality_ratios = kwargs.get('modality_ratios', {'text': 0.6, 'visual': 0.3, 'action': 0.1})
        self.synchronization = kwargs.get('synchronization', 'sequential')
        self.consistency_checking = kwargs.get('consistency_checking', True)
        self.content_planning = kwargs.get('content_planning', True)
        self.coherence_threshold = kwargs.get('coherence_threshold', 0.7)
        self.interleaving = kwargs.get('interleaving', True)
        self.modality_configs = kwargs.get('modality_configs', {})
        self.primary_modality = kwargs.get('primary_modality', 'text')
        self.fallback_modality = kwargs.get('fallback_modality', 'text')
        self.complementary_modalities = kwargs.get('complementary_modalities', True)
        self.redundancy_level = kwargs.get('redundancy_level', 'low')
        self.optimize_bandwidth = kwargs.get('optimize_bandwidth', True)
        self.adaptive_selection = kwargs.get('adaptive_selection', True)
        self.layout_engine = kwargs.get('layout_engine', 'flow')
        self.response_template = kwargs.get('response_template', None)
        self.max_modalities_per_response = kwargs.get('max_modalities_per_response', 5)
        self.modality_selection_strategy = kwargs.get('modality_selection_strategy', 'content_based')
        self.enable_runtime_adaptation = kwargs.get('enable_runtime_adaptation', True)
        
        # Advanced multimodal configuration
        self.cross_modal_attention = kwargs.get('cross_modal_attention', True)
        self.cross_modal_attention_layers = kwargs.get('cross_modal_attention_layers', 2)
        self.cross_modal_attention_heads = kwargs.get('cross_modal_attention_heads', 8)
        self.cross_modal_dim = kwargs.get('cross_modal_dim', 512)
        self.semantic_alignment_weight = kwargs.get('semantic_alignment_weight', 0.5)
        self.spatial_alignment_weight = kwargs.get('spatial_alignment_weight', 0.3)
        self.temporal_alignment_weight = kwargs.get('temporal_alignment_weight', 0.2)
        self.modality_fusion_strategy = kwargs.get('modality_fusion_strategy', 'hierarchical')
        self.fusion_temperature = kwargs.get('fusion_temperature', 1.0)
        self.early_fusion_layers = kwargs.get('early_fusion_layers', 1)
        self.late_fusion_layers = kwargs.get('late_fusion_layers', 2)
        self.use_query_modality_hints = kwargs.get('use_query_modality_hints', True)
        self.use_context_modality_history = kwargs.get('use_context_modality_history', True)
        self.content_diversity_weight = kwargs.get('content_diversity_weight', 0.4)
        self.content_coherence_weight = kwargs.get('content_coherence_weight', 0.6)
        
        # Content planning configuration 
        self.content_planning_algorithm = kwargs.get('content_planning_algorithm', 'hierarchical')
        self.content_planner_temperature = kwargs.get('content_planner_temperature', 0.8)
        self.section_transition_smoothing = kwargs.get('section_transition_smoothing', True)
        self.use_semantic_segmentation = kwargs.get('use_semantic_segmentation', True)
        self.semantic_segmentation_threshold = kwargs.get('semantic_segmentation_threshold', 0.6)
        
        # Layout configuration
        self.layout_algorithm = kwargs.get('layout_algorithm', 'flow-based')
        self.layout_optimization_steps = kwargs.get('layout_optimization_steps', 10)
        self.layout_energy_weights = kwargs.get('layout_energy_weights', {
            'alignment': 0.4,
            'spacing': 0.3,
            'grouping': 0.3,
            'balance': 0.2,
            'readability': 0.5
        })
        self.visual_dominance_factor = kwargs.get('visual_dominance_factor', 1.5)
        self.text_dominance_factor = kwargs.get('text_dominance_factor', 1.0)
        self.action_dominance_factor = kwargs.get('action_dominance_factor', 0.8)
        
        # Adaptation configuration
        self.query_complexity_estimator = kwargs.get('query_complexity_estimator', 'combined')
        self.context_sensitivity = kwargs.get('context_sensitivity', 0.5)
        self.user_preference_weight = kwargs.get('user_preference_weight', 0.7)
        self.adaptation_learning_rate = kwargs.get('adaptation_learning_rate', 0.1)
        self.adaptation_momentum = kwargs.get('adaptation_momentum', 0.9)
        self.adaptation_window_size = kwargs.get('adaptation_window_size', 5)
        
        # Modality generator references (to be set during initialization)
        self.text_generator_config = kwargs.get('text_generator_config', None)
        self.visual_generator_config = kwargs.get('visual_generator_config', None)
        self.action_generator_config = kwargs.get('action_generator_config', None)
        
        # Validate configuration
        self._validate()
        
        logger.debug(f"Initialized MultiModalSynthesizerConfig with {len(kwargs)} custom parameters")
    
    def _validate(self):
        """Validate configuration parameters."""
        # Validate modality types
        valid_types = ["text", "visual", "action", "audio", "haptic", "spatial"]
        for modality_type in self.modality_types:
            if modality_type not in valid_types:
                raise ValueError(f"modality_type must be one of {valid_types}")
        
        # Validate primary and fallback modalities
        if self.primary_modality not in self.modality_types:
            raise ValueError(f"primary_modality must be one of {self.modality_types}")
        
        if self.fallback_modality not in self.modality_types:
            raise ValueError(f"fallback_modality must be one of {self.modality_types}")
        
        # Validate modality ratios
        if self.modality_ratios:
            if not all(m in self.modality_types for m in self.modality_ratios):
                raise ValueError(f"modality_ratios keys must be in modality_types")
            
            if not all(0 <= r <= 1 for r in self.modality_ratios.values()):
                raise ValueError(f"modality_ratios values must be in range [0, 1]")
            
            total = sum(self.modality_ratios.values())
            if not 0.99 <= total <= 1.01:  # Allow for floating-point error
                raise ValueError(f"modality_ratios must sum to approximately 1.0, got {total}")
        
        # Validate synchronization
        valid_sync = ["sequential", "parallel", "adaptive", "hierarchical"]
        if self.synchronization not in valid_sync:
            raise ValueError(f"synchronization must be one of {valid_sync}")
        
        # Validate coherence threshold
        if not 0 <= self.coherence_threshold <= 1:
            raise ValueError(f"coherence_threshold must be in range [0, 1]")
        
        # Validate redundancy level
        valid_redundancy = ["none", "low", "medium", "high"]
        if self.redundancy_level not in valid_redundancy:
            raise ValueError(f"redundancy_level must be one of {valid_redundancy}")
        
        # Validate layout engine
        valid_layouts = ["flow", "grid", "adaptive", "hierarchical", "semantic"]
        if self.layout_engine not in valid_layouts:
            raise ValueError(f"layout_engine must be one of {valid_layouts}")
        
        # Validate modality selection strategy
        valid_strategies = ["content_based", "user_preference", "context_aware", "fixed"]
        if self.modality_selection_strategy not in valid_strategies:
            raise ValueError(f"modality_selection_strategy must be one of {valid_strategies}")
        
        # Validate max modalities
        if self.max_modalities_per_response <= 0:
            raise ValueError(f"max_modalities_per_response must be positive")
            
        # Validate fusion strategy
        valid_fusion = ["early", "late", "hierarchical", "adaptive", "attention"]
        if self.modality_fusion_strategy not in valid_fusion:
            raise ValueError(f"modality_fusion_strategy must be one of {valid_fusion}")
            
        # Validate content planning algorithm
        valid_planning = ["hierarchical", "sequential", "graph-based", "template", "neural"]
        if self.content_planning_algorithm not in valid_planning:
            raise ValueError(f"content_planning_algorithm must be one of {valid_planning}")
            
        # Validate layout algorithm
        valid_layout_algs = ["flow-based", "constraint-based", "grid-based", "force-directed", "template-based"]
        if self.layout_algorithm not in valid_layout_algs:
            raise ValueError(f"layout_algorithm must be one of {valid_layout_algs}")
            
        # Validate weights sum to 1.0
        if self.layout_energy_weights:
            total_weight = sum(self.layout_energy_weights.values())
            if not 0.99 <= total_weight <= 1.01:  # Allow for floating-point error
                logger.warning(f"layout_energy_weights sum to {total_weight}, should be approximately 1.0")
        
        # Validate weights
        weight_params = [
            self.semantic_alignment_weight, 
            self.spatial_alignment_weight, 
            self.temporal_alignment_weight,
            self.content_diversity_weight,
            self.content_coherence_weight,
            self.context_sensitivity,
            self.user_preference_weight
        ]
        
        for weight in weight_params:
            if not 0 <= weight <= 1:
                raise ValueError(f"Weight parameters must be in range [0, 1], got {weight}")
        
        # Check semantic + spatial + temporal weights
        alignment_sum = (self.semantic_alignment_weight + 
                        self.spatial_alignment_weight + 
                        self.temporal_alignment_weight)
        if not 0.99 <= alignment_sum <= 1.01:
            logger.warning(f"Alignment weights sum to {alignment_sum}, should be approximately 1.0")
            
        # Check diversity + coherence weights
        content_sum = self.content_diversity_weight + self.content_coherence_weight
        if not 0.99 <= content_sum <= 1.01:
            logger.warning(f"Content weights sum to {content_sum}, should be approximately 1.0")
        
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert configuration to dictionary.
        
        Returns:
            Dict[str, Any]: Configuration as dictionary
        """
        # Convert device to string for serialization
        config_dict = {k: v for k, v in self.__dict__.items() if not k.startswith('_')}
        if 'device' in config_dict and isinstance(config_dict['device'], torch.device):
            config_dict['device'] = str(config_dict['device'])
        return config_dict
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'MultiModalSynthesizerConfig':
        """
        Create configuration from dictionary.
        
        Args:
            config_dict: Dictionary containing configuration parameters
            
        Returns:
            MultiModalSynthesizerConfig: Configuration object
        """
        return cls(**config_dict)
    
    def save(self, file_path: str) -> None:
        """
        Save configuration to JSON file.
        
        Args:
            file_path: Path to save the configuration file
        """
        with open(file_path, 'w') as f:
            json.dump(self.to_dict(), f, indent=2)
    
    @classmethod
    def load(cls, file_path: str) -> 'MultiModalSynthesizerConfig':
        """
        Load configuration from JSON file.
        
        Args:
            file_path: Path to the configuration file
            
        Returns:
            MultiModalSynthesizerConfig: Configuration object
        """
        with open(file_path, 'r') as f:
            config_dict = json.load(f)
        return cls.from_dict(config_dict)


class ModalityOutputUnit:
    """Container for output from a specific modality."""
    
    def __init__(
        self, 
        modality: str,
        content: Any,
        content_type: str,
        metadata: Optional[Dict[str, Any]] = None,
        position: Optional[Tuple[int, int]] = None,
        size: Optional[Tuple[int, int]] = None,
        confidence: float = 1.0,
        importance: float = 0.5,
        section_id: Optional[str] = None,
        dependencies: Optional[List[str]] = None,
        rendering_options: Optional[Dict[str, Any]] = None
    ):
        """
        Initialize modality output unit.
        
        Args:
            modality: Modality type (e.g., "text", "visual", "action")
            content: Content of the output
            content_type: Type of content (e.g., "explanation", "chart", "command")
            metadata: Additional metadata for the output
            position: (x, y) position in layout
            size: (width, height) size in layout
            confidence: Confidence score for the output
            importance: Importance score for the output
            section_id: ID of the section this output belongs to
            dependencies: IDs of other outputs this output depends on
            rendering_options: Options for rendering the output
        """
        self.id = str(uuid.uuid4())
        self.modality = modality
        self.content = content
        self.content_type = content_type
        self.metadata = metadata or {}
        self.position = position
        self.size = size
        self.confidence = confidence
        self.importance = importance
        self.section_id = section_id
        self.dependencies = dependencies or []
        self.rendering_options = rendering_options or {}
        self.created_at = datetime.now()
        
        # Extract feature vector if possible
        self.features = self._extract_features()
    
    def _extract_features(self) -> Optional[np.ndarray]:
        """
        Extract feature vector from content for similarity comparisons.
        
        Returns:
            np.ndarray: Feature vector or None if extraction fails
        """
        if self.modality == "text":
            # Simple bag of words for text
            try:
                if isinstance(self.content, str):
                    if _HAS_SKLEARN:
                        vectorizer = TfidfVectorizer()
                        features = vectorizer.fit_transform([self.content]).toarray()[0]
                        return features
                    else:
                        # Fallback: word count dictionary
                        words = re.findall(r'\w+', self.content.lower())
                        word_counts = defaultdict(int)
                        for word in words:
                            word_counts[word] += 1
                        # Convert to vector
                        return np.array(list(word_counts.values()))
            except Exception as e:
                logger.warning(f"Failed to extract text features: {str(e)}")
                return None
                
        elif self.modality == "visual" and "embedding" in self.metadata:
            # Use pre-computed visual embedding
            return self.metadata["embedding"]
            
        elif self.modality == "action" and isinstance(self.content, str):
            # Simple bag of words for action text
            try:
                if _HAS_SKLEARN:
                    vectorizer = TfidfVectorizer()
                    features = vectorizer.fit_transform([self.content]).toarray()[0]
                    return features
                else:
                    # Fallback: word count dictionary
                    words = re.findall(r'\w+', self.content.lower())
                    word_counts = defaultdict(int)
                    for word in words:
                        word_counts[word] += 1
                    # Convert to vector
                    return np.array(list(word_counts.values()))
            except Exception as e:
                logger.warning(f"Failed to extract action features: {str(e)}")
                return None
                
        return None
    
    def update_position(self, position: Tuple[int, int]) -> None:
        """
        Update position in layout.
        
        Args:
            position: New (x, y) position
        """
        self.position = position
    
    def update_size(self, size: Tuple[int, int]) -> None:
        """
        Update size in layout.
        
        Args:
            size: New (width, height) size
        """
        self.size = size
    
    def update_content(self, content: Any) -> None:
        """
        Update content and re-extract features.
        
        Args:
            content: New content
        """
        self.content = content
        self.features = self._extract_features()
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert to dictionary representation.
        
        Returns:
            Dict[str, Any]: Dictionary representation
        """
        return {
            "id": self.id,
            "modality": self.modality,
            "content_type": self.content_type,
            "content": self.content if isinstance(self.content, (str, int, float, bool, list, dict)) else str(self.content),
            "metadata": self.metadata,
            "position": self.position,
            "size": self.size,
            "confidence": self.confidence,
            "importance": self.importance,
            "section_id": self.section_id,
            "dependencies": self.dependencies,
            "rendering_options": self.rendering_options,
            "created_at": self.created_at.isoformat()
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ModalityOutputUnit':
        """
        Create from dictionary representation.
        
        Args:
            data: Dictionary representation
            
        Returns:
            ModalityOutputUnit: Output unit
        """
        # Create instance
        instance = cls(
            modality=data["modality"],
            content=data["content"],
            content_type=data["content_type"],
            metadata=data.get("metadata", {}),
            position=data.get("position"),
            size=data.get("size"),
            confidence=data.get("confidence", 1.0),
            importance=data.get("importance", 0.5),
            section_id=data.get("section_id"),
            dependencies=data.get("dependencies", []),
            rendering_options=data.get("rendering_options", {})
        )
        
        # Set ID
        instance.id = data["id"]
        
        # Set creation time
        if "created_at" in data:
            try:
                instance.created_at = datetime.fromisoformat(data["created_at"])
            except (ValueError, TypeError):
                instance.created_at = datetime.now()
        
        return instance


class SectionUnit:
    """Container for a section of multimodal content."""
    
    def __init__(
        self,
        section_id: str,
        title: str,
        purpose: str,
        position: int,
        modality_outputs: Optional[List[ModalityOutputUnit]] = None,
        metadata: Optional[Dict[str, Any]] = None
    ):
        """
        Initialize section unit.
        
        Args:
            section_id: Unique identifier for the section
            title: Section title
            purpose: Purpose of the section
            position: Position in sequence
            modality_outputs: List of modality outputs in this section
            metadata: Additional metadata
        """
        self.section_id = section_id
        self.title = title
        self.purpose = purpose
        self.position = position
        self.modality_outputs = modality_outputs or []
        self.metadata = metadata or {}
        self.created_at = datetime.now()
    
    def add_output(self, output: ModalityOutputUnit) -> None:
        """
        Add modality output to section.
        
        Args:
            output: Modality output to add
        """
        output.section_id = self.section_id
        self.modality_outputs.append(output)
    
    def remove_output(self, output_id: str) -> bool:
        """
        Remove modality output from section.
        
        Args:
            output_id: ID of output to remove
            
        Returns:
            bool: True if output was removed, False otherwise
        """
        before_length = len(self.modality_outputs)
        self.modality_outputs = [o for o in self.modality_outputs if o.id != output_id]
        return len(self.modality_outputs) < before_length
    
    def get_outputs_by_modality(self, modality: str) -> List[ModalityOutputUnit]:
        """
        Get all outputs of a specific modality.
        
        Args:
            modality: Modality type to filter by
            
        Returns:
            List[ModalityOutputUnit]: Filtered outputs
        """
        return [o for o in self.modality_outputs if o.modality == modality]
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert to dictionary representation.
        
        Returns:
            Dict[str, Any]: Dictionary representation
        """
        return {
            "section_id": self.section_id,
            "title": self.title,
            "purpose": self.purpose,
            "position": self.position,
            "modality_outputs": [o.to_dict() for o in self.modality_outputs],
            "metadata": self.metadata,
            "created_at": self.created_at.isoformat()
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SectionUnit':
        """
        Create from dictionary representation.
        
        Args:
            data: Dictionary representation
            
        Returns:
            SectionUnit: Section unit
        """
        # Create instance
        instance = cls(
            section_id=data["section_id"],
            title=data["title"],
            purpose=data["purpose"],
            position=data["position"],
            metadata=data.get("metadata", {})
        )
        
        # Add outputs
        if "modality_outputs" in data:
            instance.modality_outputs = [
                ModalityOutputUnit.from_dict(o) for o in data["modality_outputs"]
            ]
        
        # Set creation time
        if "created_at" in data:
            try:
                instance.created_at = datetime.fromisoformat(data["created_at"])
            except (ValueError, TypeError):
                instance.created_at = datetime.now()
        
        return instance


class ContentPlan:
    """Content plan for organizing multimodal responses."""
    
    def __init__(
        self,
        query: str,
        modalities: List[str],
        sections: Optional[List[Dict[str, Any]]] = None,
        primary_modality: str = "text",
        modality_assignments: Optional[Dict[str, List[str]]] = None,
        metadata: Optional[Dict[str, Any]] = None
    ):
        """
        Initialize content plan.
        
        Args:
            query: Original user query
            modalities: Selected modalities for response
            sections: Planned sections
            primary_modality: Primary modality
            modality_assignments: Assignment of content to modalities
            metadata: Additional metadata
        """
        self.id = str(uuid.uuid4())
        self.query = query
        self.modalities = modalities
        self.sections = sections or []
        self.primary_modality = primary_modality
        self.modality_assignments = modality_assignments or {}
        self.metadata = metadata or {}
        self.created_at = datetime.now()
        
        # Ensure all modalities have assignments
        for modality in modalities:
            if modality not in self.modality_assignments:
                self.modality_assignments[modality] = []
        
        logger.debug(f"Created ContentPlan with {len(self.sections)} sections and "
                     f"{len(self.modalities)} modalities")
    
    def add_section(self, section: Dict[str, Any]) -> None:
        """
        Add section to content plan.
        
        Args:
            section: Section information
        """
        self.sections.append(section)
    
    def add_modality_assignment(self, modality: str, concept: str) -> None:
        """
        Add content assignment to modality.
        
        Args:
            modality: Modality to assign to
            concept: Content concept to assign
        """
        if modality in self.modality_assignments:
            self.modality_assignments[modality].append(concept)
        else:
            self.modality_assignments[modality] = [concept]
    
    def get_section_by_position(self, position: int) -> Optional[Dict[str, Any]]:
        """
        Get section by its position.
        
        Args:
            position: Position in sequence
            
        Returns:
            Dict[str, Any]: Section information or None if not found
        """
        for section in self.sections:
            if section.get("position") == position:
                return section
        return None
    
    def get_section_by_role(self, role: str) -> Optional[Dict[str, Any]]:
        """
        Get section by its role.
        
        Args:
            role: Section role
            
        Returns:
            Dict[str, Any]: Section information or None if not found
        """
        for section in self.sections:
            if section.get("role") == role:
                return section
        return None
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert to dictionary representation.
        
        Returns:
            Dict[str, Any]: Dictionary representation
        """
        return {
            "id": self.id,
            "query": self.query,
            "modalities": self.modalities,
            "sections": self.sections,
            "primary_modality": self.primary_modality,
            "modality_assignments": self.modality_assignments,
            "metadata": self.metadata,
            "created_at": self.created_at.isoformat()
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ContentPlan':
        """
        Create from dictionary representation.
        
        Args:
            data: Dictionary representation
            
        Returns:
            ContentPlan: Content plan
        """
        # Create instance
        instance = cls(
            query=data["query"],
            modalities=data["modalities"],
            sections=data.get("sections", []),
            primary_modality=data.get("primary_modality", "text"),
            modality_assignments=data.get("modality_assignments", {}),
            metadata=data.get("metadata", {})
        )
        
        # Set ID
        instance.id = data["id"]
        
        # Set creation time
        if "created_at" in data:
            try:
                instance.created_at = datetime.fromisoformat(data["created_at"])
            except (ValueError, TypeError):
                instance.created_at = datetime.now()
        
        return instance


class SemanticContentSegmenter:
    """
    Segmenter for dividing query content into semantic sections.
    
    This class identifies logical breaks and thematic shifts in content
    to create meaningful sections for multimodal responses.
    """
    
    def __init__(
        self,
        segmentation_threshold: float = 0.6,
        min_segment_length: int = 20,
        max_segment_length: int = 200,
        use_spacy: bool = _HAS_SPACY
    ):
        """
        Initialize semantic content segmenter.
        
        Args:
            segmentation_threshold: Threshold for creating segment boundaries
            min_segment_length: Minimum token length for a segment
            max_segment_length: Maximum token length for a segment
            use_spacy: Whether to use spaCy for segmentation
        """
        self.segmentation_threshold = segmentation_threshold
        self.min_segment_length = min_segment_length
        self.max_segment_length = max_segment_length
        self.use_spacy = use_spacy and _HAS_SPACY
        
        logger.debug("Initialized SemanticContentSegmenter")
    
    @log_execution_time
    def segment_query(self, query: str) -> List[Dict[str, Any]]:
        """
        Segment a query into semantic sections.
        
        Args:
            query: User query text
            
        Returns:
            List[Dict[str, Any]]: List of segment information
        """
        # Use appropriate segmentation method
        if self.use_spacy:
            return self._segment_with_spacy(query)
        else:
            return self._segment_fallback(query)
    
    def _segment_with_spacy(self, query: str) -> List[Dict[str, Any]]:
        """
        Segment query using spaCy NLP.
        
        Args:
            query: User query text
            
        Returns:
            List[Dict[str, Any]]: List of segment information
        """
        # Process query with spaCy
        doc = nlp(query)
        
        # Get sentences
        sentences = list(doc.sents)
        
        # Initialize segments
        segments = []
        current_segment = []
        current_tokens = 0
        
        # Track semantic similarity between consecutive sentences
        prev_embedding = None
        
        # Process each sentence
        for i, sentence in enumerate(sentences):
            # Extract key information
            tokens = len(sentence)
            entities = [{"text": e.text, "label": e.label_} for e in sentence.ents]
            
            # Get sentence embedding
            sentence_embedding = sentence.vector
            
            # Calculate similarity with previous sentence
            similarity = 0.0
            if prev_embedding is not None:
                norm_prev = np.linalg.norm(prev_embedding)
                norm_curr = np.linalg.norm(sentence_embedding)
                if norm_prev > 0 and norm_curr > 0:  # Avoid division by zero
                    similarity = np.dot(prev_embedding, sentence_embedding) / (norm_prev * norm_curr)
            
            # Update previous embedding
            prev_embedding = sentence_embedding
            
            # Check if we should start a new segment
            start_new_segment = False
            
            # Check length constraint
            if current_tokens + tokens > self.max_segment_length:
                start_new_segment = True
            
            # Check semantic similarity
            elif i > 0 and similarity < self.segmentation_threshold:
                # Only create a new segment if current one meets minimum length
                if current_tokens >= self.min_segment_length:
                    start_new_segment = True
            
            # Check for topic shift indicators
            topic_shift_words = ["however", "nevertheless", "conversely", "but", "yet", 
                                "on the other hand", "in contrast", "instead", "meanwhile"]
            if any(token.text.lower() in topic_shift_words for token in sentence):
                if current_tokens >= self.min_segment_length:
                    start_new_segment = True
            
            # Start new segment if needed
            if start_new_segment:
                # Complete current segment
                if current_segment:
                    segment_text = " ".join(current_segment)
                    segments.append({
                        "text": segment_text,
                        "tokens": current_tokens,
                        "entities": entities,
                        "similarity_to_prev": similarity
                    })
                
                # Start new segment
                current_segment = [sentence.text]
                current_tokens = tokens
            else:
                # Add to current segment
                current_segment.append(sentence.text)
                current_tokens += tokens
        
        # Add final segment
        if current_segment:
            segment_text = " ".join(current_segment)
            segments.append({
                "text": segment_text,
                "tokens": current_tokens,
                "entities": [],
                "similarity_to_prev": 0.0
            })
        
        # Post-process segments with additional metadata
        for i, segment in enumerate(segments):
            # Add position
            segment["position"] = i
            
            # Add purpose
            segment["purpose"] = self._infer_segment_purpose(segment["text"])
            
            # Add title
            segment["title"] = self._generate_segment_title(segment["text"], segment["purpose"])
            
            # Add section ID
            segment["section_id"] = f"section-{i+1}"
        
        return segments
    
    def _segment_fallback(self, query: str) -> List[Dict[str, Any]]:
        """
        Fallback segmentation method when spaCy is not available.
        
        Args:
            query: User query text
            
        Returns:
            List[Dict[str, Any]]: List of segment information
        """
        # Split into sentences (simple rule-based approach)
        sentence_endings = r'(?<=[.!?])\s+'
        sentences = re.split(sentence_endings, query)
        sentences = [s.strip() for s in sentences if s.strip()]
        
        # Initialize segments
        segments = []
        current_segment = []
        current_tokens = 0
        
        # Process each sentence
        for i, sentence in enumerate(sentences):
            # Approximate tokens by words
            tokens = len(sentence.split())
            
            # Check if we should start a new segment
            start_new_segment = False
            
            # Check length constraint
            if current_tokens + tokens > self.max_segment_length:
                start_new_segment = True
            
            # Check for topic shift indicators
            topic_shift_words = ["however", "nevertheless", "conversely", "but", "yet", 
                                "on the other hand", "in contrast", "instead", "meanwhile"]
            lower_sentence = sentence.lower()
            if any(word in lower_sentence for word in topic_shift_words):
                if current_tokens >= self.min_segment_length:
                    start_new_segment = True
            
            # Check for paragraph breaks (multiple sequential newlines)
            if sentence.startswith("\n\n") or sentence.startswith("\r\n\r\n"):
                if current_tokens >= self.min_segment_length:
                    start_new_segment = True
                    # Remove leading newlines
                    sentence = sentence.lstrip("\r\n")
            
            # Check for numeric/bullet list items at start (potential new section)
            if re.match(r'^\d+[\.\)]|^[\*\-•]\s', sentence):
                if current_tokens >= self.min_segment_length:
                    start_new_segment = True
            
            # Start new segment if needed
            if start_new_segment:
                # Complete current segment
                if current_segment:
                    segment_text = " ".join(current_segment)
                    segments.append({
                        "text": segment_text,
                        "tokens": current_tokens,
                        "entities": [],
                        "similarity_to_prev": 0.0
                    })
                
                # Start new segment
                current_segment = [sentence]
                current_tokens = tokens
            else:
                # Add to current segment
                current_segment.append(sentence)
                current_tokens += tokens
        
        # Add final segment
        if current_segment:
            segment_text = " ".join(current_segment)
            segments.append({
                "text": segment_text,
                "tokens": current_tokens,
                "entities": [],
                "similarity_to_prev": 0.0
            })
        
        # Post-process segments with additional metadata
        for i, segment in enumerate(segments):
            # Add position
            segment["position"] = i
            
            # Add purpose
            segment["purpose"] = self._infer_segment_purpose(segment["text"])
            
            # Add title
            segment["title"] = self._generate_segment_title(segment["text"], segment["purpose"])
            
            # Add section ID
            segment["section_id"] = f"section-{i+1}"
        
        return segments
    
    def _infer_segment_purpose(self, text: str) -> str:
        """
        Infer the purpose of a segment.
        
        Args:
            text: Segment text
            
        Returns:
            str: Inferred purpose
        """
        text_lower = text.lower()
        
        # Look for specific patterns to infer purpose
        if any(q in text_lower for q in ["?", "how", "why", "what", "when", "where", "who"]):
            return "address question"
            
        if any(term in text_lower for term in ["compare", "contrast", "versus", "vs", "similarities", "differences"]):
            return "compare aspects"
            
        if any(term in text_lower for term in ["list", "enumerate", "examples", "instances", "steps"]):
            return "list items"
            
        if any(term in text_lower for term in ["explain", "describe", "elaborate", "detail", "clarify"]):
            return "explain concept"
            
        if any(term in text_lower for term in ["analyze", "examine", "study", "investigate", "explore"]):
            return "analyze topic"
            
        if any(term in text_lower for term in ["background", "context", "introduction", "overview"]):
            return "provide background"
            
        if any(term in text_lower for term in ["summary", "conclusion", "overview", "recap", "tldr"]):
            return "summarize content"
            
        # Default purpose
        return "address topic"
    
    def _generate_segment_title(self, text: str, purpose: str) -> str:
        """
        Generate a title for a segment.
        
        Args:
            text: Segment text
            purpose: Segment purpose
            
        Returns:
            str: Generated title
        """
        # Generate title based on purpose and content
        first_sentence = text.split('.')[0].strip()
        
        # Truncate if too long
        if len(first_sentence) > 50:
            first_sentence = first_sentence[:47] + "..."
        
        # Format based on purpose
        if purpose == "address question":
            return f"Answering: {first_sentence}"
            
        elif purpose == "compare aspects":
            return "Comparison Analysis"
            
        elif purpose == "list items":
            return "Key Items"
            
        elif purpose == "explain concept":
            return f"Explaining {first_sentence}"
            
        elif purpose == "analyze topic":
            return f"Analysis of {first_sentence}"
            
        elif purpose == "provide background":
            return "Background Information"
            
        elif purpose == "summarize content":
            return "Summary"
            
        else:
            # Extract key phrases for default title
            if self.use_spacy:
                doc = nlp(text[:200])  # Limit to first 200 chars for efficiency
                # Get noun phrases
                noun_phrases = [chunk.text for chunk in doc.noun_chunks]
                if noun_phrases:
                    return noun_phrases[0].title()
            
            # Fallback to first few words
            words = text.split()
            title_words = words[:5] if len(words) > 5 else words
            return " ".join(title_words).title()


class LayoutEngine:
    """
    Engine for determining optimal layout of multimodal content.
    
    This class calculates positions and sizes for different modality
    outputs in a multimodal response.
    """
    
    def __init__(
        self,
        algorithm: str = "flow-based",
        optimization_steps: int = 10,
        energy_weights: Optional[Dict[str, float]] = None,
        visual_dominance_factor: float = 1.5,
        text_dominance_factor: float = 1.0,
        action_dominance_factor: float = 0.8,
        max_width: int = 800,
        max_height: int = 1200,
        default_margins: Dict[str, int] = {"top": 10, "right": 10, "bottom": 10, "left": 10}
    ):
        """
        Initialize layout engine.
        
        Args:
            algorithm: Layout algorithm ("flow-based", "constraint-based", "grid-based", 
                      "force-directed", "template-based")
            optimization_steps: Number of optimization steps
            energy_weights: Weights for layout energy terms
            visual_dominance_factor: Dominance factor for visual content
            text_dominance_factor: Dominance factor for text content
            action_dominance_factor: Dominance factor for action content
            max_width: Maximum layout width
            max_height: Maximum layout height
            default_margins: Default margins for elements
        """
        self.algorithm = algorithm
        self.optimization_steps = optimization_steps
        self.energy_weights = energy_weights or {
            "alignment": 0.4,
            "spacing": 0.3,
            "grouping": 0.3,
            "balance": 0.2,
            "readability": 0.5
        }
        self.visual_dominance_factor = visual_dominance_factor
        self.text_dominance_factor = text_dominance_factor
        self.action_dominance_factor = action_dominance_factor
        self.max_width = max_width
        self.max_height = max_height
        self.default_margins = default_margins
        
        # Initialize default sizes for different content types
        self.default_sizes = {
            "text": {
                "explanation": (max_width, 200),
                "narrative": (max_width, 300),
                "summary": (max_width, 150),
                "analysis": (max_width, 250),
                "instruction": (max_width, 150),
                "default": (max_width, 200)
            },
            "visual": {
                "chart": (600, 400),
                "diagram": (600, 450),
                "graph": (500, 500),
                "image": (400, 300),
                "plot": (550, 400),
                "visualization": (600, 450),
                "default": (500, 400)
            },
            "action": {
                "command": (max_width, 100),
                "api_call": (max_width, 150),
                "function": (max_width, 200),
                "query": (max_width, 100),
                "workflow": (max_width, 250),
                "default": (max_width, 150)
            }
        }
        
        logger.debug(f"Initialized LayoutEngine with algorithm={algorithm}")
    
    @log_execution_time
    def compute_layout(
        self,
        modality_outputs: List[ModalityOutputUnit],
        sections: Optional[List[SectionUnit]] = None
    ) -> List[ModalityOutputUnit]:
        """
        Compute layout for modality outputs.
        
        Args:
            modality_outputs: List of modality outputs
            sections: Optional list of sections
            
        Returns:
            List[ModalityOutputUnit]: Modality outputs with updated positions and sizes
        """
        # Choose appropriate layout algorithm
        if self.algorithm == "flow-based":
            return self._flow_based_layout(modality_outputs, sections)
        elif self.algorithm == "constraint-based":
            return self._constraint_based_layout(modality_outputs, sections)
        elif self.algorithm == "grid-based":
            return self._grid_based_layout(modality_outputs, sections)
        elif self.algorithm == "force-directed":
            return self._force_directed_layout(modality_outputs, sections)
        elif self.algorithm == "template-based":
            return self._template_based_layout(modality_outputs, sections)
        else:
            # Default to flow-based
            return self._flow_based_layout(modality_outputs, sections)
    
    def _flow_based_layout(
        self,
        modality_outputs: List[ModalityOutputUnit],
        sections: Optional[List[SectionUnit]] = None
    ) -> List[ModalityOutputUnit]:
        """
        Compute flow-based layout (similar to document flow).
        
        Args:
            modality_outputs: List of modality outputs
            sections: Optional list of sections
            
        Returns:
            List[ModalityOutputUnit]: Modality outputs with updated positions and sizes
        """
        # Make a copy of outputs to avoid modifying originals
        outputs = [ModalityOutputUnit(
            modality=output.modality,
            content=output.content,
            content_type=output.content_type,
            metadata=output.metadata,
            position=output.position,
            size=output.size,
            confidence=output.confidence,
            importance=output.importance,
            section_id=output.section_id,
            dependencies=output.dependencies,
            rendering_options=output.rendering_options
        ) for output in modality_outputs]
        
        # Sort outputs by section position and importance
        if sections:
            # Create section position lookup
            section_positions = {section.section_id: section.position for section in sections}
            # Sort by section position first, then by importance within sections
            outputs.sort(key=lambda o: (
                section_positions.get(o.section_id, float('inf')), 
                -o.importance
            ))
        else:
            # Sort by importance if no sections
            outputs.sort(key=lambda o: -o.importance)
        
        # Initialize current position
        current_x = self.default_margins["left"]
        current_y = self.default_margins["top"]
        max_height_in_row = 0
        
        # Process each output
        for i, output in enumerate(outputs):
            # Determine size if not set
            if not output.size:
                output.size = self._get_default_size(output)
            
            width, height = output.size
            
            # Special handling for visual content - may need to be on its own row
            if (output.modality == "visual" and i > 0 and 
                outputs[i-1].modality != "visual" and
                current_x > self.default_margins["left"]):
                # Move to next row
                current_x = self.default_margins["left"]
                current_y += max_height_in_row + self.default_margins["bottom"]
                max_height_in_row = 0
            
            # Check if we need to wrap to next row
            if (current_x + width > self.max_width - self.default_margins["right"] and 
                current_x > self.default_margins["left"]):
                # Move to next row
                current_x = self.default_margins["left"]
                current_y += max_height_in_row + self.default_margins["bottom"]
                max_height_in_row = 0
            
            # Set position
            output.position = (current_x, current_y)
            
            # Update current position
            current_x += width + self.default_margins["right"]
            max_height_in_row = max(max_height_in_row, height)
            
            # Special handling for text followed by visual - keep visual close
            if (output.modality == "text" and i < len(outputs) - 1 and 
                outputs[i+1].modality == "visual" and 
                current_x + outputs[i+1].size[0] > self.max_width - self.default_margins["right"]):
                # Move to next row for the upcoming visual
                current_x = self.default_margins["left"]
                current_y += max_height_in_row + self.default_margins["bottom"]
                max_height_in_row = 0
        
        return outputs
    
    def _constraint_based_layout(
        self,
        modality_outputs: List[ModalityOutputUnit],
        sections: Optional[List[SectionUnit]] = None
    ) -> List[ModalityOutputUnit]:
        """
        Compute constraint-based layout using optimization.
        
        Args:
            modality_outputs: List of modality outputs
            sections: Optional list of sections
            
        Returns:
            List[ModalityOutputUnit]: Modality outputs with updated positions and sizes
        """
        # Make a copy of outputs to avoid modifying originals
        outputs = [ModalityOutputUnit(
            modality=output.modality,
            content=output.content,
            content_type=output.content_type,
            metadata=output.metadata,
            position=output.position,
            size=output.size,
            confidence=output.confidence,
            importance=output.importance,
            section_id=output.section_id,
            dependencies=output.dependencies,
            rendering_options=output.rendering_options
        ) for output in modality_outputs]
        
        # Determine sizes for all outputs
        for output in outputs:
            if not output.size:
                output.size = self._get_default_size(output)
        
        # Group by section
        section_outputs = defaultdict(list)
        for output in outputs:
            section_outputs[output.section_id].append(output)
        
        # Initialize current position
        current_y = self.default_margins["top"]
        
        # Process each section
        for section_id, section_items in section_outputs.items():
            # Sort section items by importance
            section_items.sort(key=lambda o: -o.importance)
            
            # Apply constraints within section
            self._apply_section_constraints(section_items, current_y)
            
            # Update current position for next section
            section_height = max(
                (o.position[1] + o.size[1] for o in section_items), 
                default=0
            )
            current_y = section_height + self.default_margins["bottom"]
        
        # Run optimization steps to improve layout
        for _ in range(self.optimization_steps):
            self._optimize_layout(outputs)
        
        return outputs
    
    def _apply_section_constraints(
        self,
        section_items: List[ModalityOutputUnit],
        start_y: int
    ) -> None:
        """
        Apply constraints to section items.
        
        Args:
            section_items: Items in the section
            start_y: Starting y position
        """
        if not section_items:
            return
        
        # Group by modality
        modality_groups = defaultdict(list)
        for item in section_items:
            modality_groups[item.modality].append(item)
        
        # Current position
        current_x = self.default_margins["left"]
        current_y = start_y
        
        # Process items by modality groups for better organization
        # Order: text first, then visual, then action
        for modality in ["text", "visual", "action"]:
            items = modality_groups.get(modality, [])
            if not items:
                continue
            
            # Special handling for visual items
            if modality == "visual":
                # Visual items should align horizontally if possible
                visual_x = current_x
                max_height = 0
                
                for item in items:
                    width, height = item.size
                    
                    # Check if we need to wrap
                    if visual_x + width > self.max_width - self.default_margins["right"]:
                        visual_x = self.default_margins["left"]
                        current_y += max_height + self.default_margins["bottom"]
                        max_height = 0
                    
                    # Set position
                    item.position = (visual_x, current_y)
                    
                    # Update
                    visual_x += width + self.default_margins["right"]
                    max_height = max(max_height, height)
                
                # Update current position
                current_y += max_height + self.default_margins["bottom"]
                current_x = self.default_margins["left"]
                
            # Text and action items
            else:
                # Place items vertically
                for item in items:
                    width, height = item.size
                    item.position = (current_x, current_y)
                    current_y += height + self.default_margins["bottom"]
    
    def _optimize_layout(self, outputs: List[ModalityOutputUnit]) -> None:
        """
        Optimize layout by reducing overlaps and improving alignment.
        
        Args:
            outputs: Modality outputs to optimize
        """
        # Resolve overlaps
        self._resolve_overlaps(outputs)
        
        # Improve alignment
        self._improve_alignment(outputs)
        
        # Balance whitespace
        self._balance_whitespace(outputs)
    
    def _resolve_overlaps(self, outputs: List[ModalityOutputUnit]) -> None:
        """
        Resolve overlaps between outputs.
        
        Args:
            outputs: Modality outputs to process
        """
        # Check all pairs for overlaps
        for i, output1 in enumerate(outputs):
            for j, output2 in enumerate(outputs):
                if i >= j:  # Skip self-comparisons and duplicates
                    continue
                
                # Get bounding boxes
                x1, y1 = output1.position
                w1, h1 = output1.size
                x2, y2 = output2.position
                w2, h2 = output2.size
                
                # Check for overlap
                overlap_x = max(0, min(x1 + w1, x2 + w2) - max(x1, x2))
                overlap_y = max(0, min(y1 + h1, y2 + h2) - max(y1, y2))
                
                if overlap_x > 0 and overlap_y > 0:
                    # There is overlap - resolve by moving the less important item
                    if output1.importance >= output2.importance:
                        # Move output2
                        if overlap_x < overlap_y:
                            # Move horizontally
                            if x1 <= x2:
                                output2.position = (x1 + w1 + self.default_margins["right"], y2)
                            else:
                                output2.position = (x2 - w1 - self.default_margins["right"], y2)
                        else:
                            # Move vertically
                            if y1 <= y2:
                                output2.position = (x2, y1 + h1 + self.default_margins["bottom"])
                            else:
                                output2.position = (x2, y2 - h1 - self.default_margins["bottom"])
                    else:
                        # Move output1
                        if overlap_x < overlap_y:
                            # Move horizontally
                            if x1 <= x2:
                                output1.position = (x2 - w1 - self.default_margins["right"], y1)
                            else:
                                output1.position = (x2 + w2 + self.default_margins["right"], y1)
                        else:
                            # Move vertically
                            if y1 <= y2:
                                output1.position = (x1, y2 - h1 - self.default_margins["bottom"])
                            else:
                                output1.position = (x1, y2 + h2 + self.default_margins["bottom"])
    
    def _improve_alignment(self, outputs: List[ModalityOutputUnit]) -> None:
        """
        Improve alignment of outputs.
        
        Args:
            outputs: Modality outputs to process
        """
        # Group by section
        section_outputs = defaultdict(list)
        for output in outputs:
            section_outputs[output.section_id].append(output)
        
        # Process each section
        for section_id, section_items in section_outputs.items():
            # Skip sections with fewer than 2 items
            if len(section_items) < 2:
                continue
            
            # Find common x-coordinates
            x_coords = [item.position[0] for item in section_items]
            common_x = self._find_common_values(x_coords, threshold=5)
            
            # Find common right edges
            right_edges = [item.position[0] + item.size[0] for item in section_items]
            common_right = self._find_common_values(right_edges, threshold=5)
            
            # Align items horizontally
            for item in section_items:
                x, y = item.position
                width, _ = item.size
                
                # Align left edge
                closest_x = self._find_closest_value(x, common_x)
                if closest_x is not None and abs(x - closest_x) < 20:
                    x = closest_x
                
                # Align right edge
                right_edge = x + width
                closest_right = self._find_closest_value(right_edge, common_right)
                if closest_right is not None and abs(right_edge - closest_right) < 20:
                    x = closest_right - width
                
                # Update position
                item.position = (x, y)
    
    def _balance_whitespace(self, outputs: List[ModalityOutputUnit]) -> None:
        """
        Balance whitespace in the layout.
        
        Args:
            outputs: Modality outputs to process
        """
        # Compute bounding box
        min_x = min((item.position[0] for item in outputs), default=0)
        min_y = min((item.position[1] for item in outputs), default=0)
        max_x = max((item.position[0] + item.size[0] for item in outputs), default=0)
        max_y = max((item.position[1] + item.size[1] for item in outputs), default=0)
        
        # Compute layout width and height
        layout_width = max_x - min_x
        layout_height = max_y - min_y
        
        # Check if layout is too narrow
        if layout_width < self.max_width * 0.7:
            # Scale positions horizontally
            scale_factor = self.max_width * 0.8 / layout_width
            for item in outputs:
                x, y = item.position
                # Scale x position, keeping left margin fixed
                new_x = min_x + (x - min_x) * scale_factor
                item.position = (new_x, y)
        
        # Check if layout is too short
        if layout_height < 300 and len(outputs) > 3:
            # Increase vertical spacing
            outputs.sort(key=lambda item: item.position[1])  # Sort by y-coordinate
            
            # Apply additional spacing
            cumulative_shift = 0
            for i in range(1, len(outputs)):
                previous_bottom = outputs[i-1].position[1] + outputs[i-1].size[1]
                current_top = outputs[i].position[1]
                
                if current_top - previous_bottom < self.default_margins["bottom"] * 2:
                    additional_space = self.default_margins["bottom"]
                    cumulative_shift += additional_space
                    
                    # Shift this item and all following items
                    for j in range(i, len(outputs)):
                        x, y = outputs[j].position
                        outputs[j].position = (x, y + cumulative_shift)
    
    def _find_common_values(self, values: List[int], threshold: int = 5) -> List[int]:
        """
        Find common values in a list within a threshold.
        
        Args:
            values: List of values
            threshold: Threshold for considering values as same
            
        Returns:
            List[int]: List of common values
        """
        if not values:
            return []
        
        # Sort values
        sorted_values = sorted(values)
        
        # Group similar values
        groups = []
        current_group = [sorted_values[0]]
        
        for value in sorted_values[1:]:
            if value - current_group[-1] <= threshold:
                current_group.append(value)
            else:
                groups.append(current_group)
                current_group = [value]
        
        # Add last group
        if current_group:
            groups.append(current_group)
        
        # Return average of each group
        return [sum(group) // len(group) for group in groups]
    
    def _find_closest_value(self, value: int, candidates: List[int]) -> Optional[int]:
        """
        Find closest value in a list of candidates.
        
        Args:
            value: Target value
            candidates: List of candidate values
            
        Returns:
            int: Closest value or None if no candidates
        """
        if not candidates:
            return None
        
        return min(candidates, key=lambda x: abs(x - value))
    
    def _grid_based_layout(
        self,
        modality_outputs: List[ModalityOutputUnit],
        sections: Optional[List[SectionUnit]] = None
    ) -> List[ModalityOutputUnit]:
        """
        Compute grid-based layout.
        
        Args:
            modality_outputs: List of modality outputs
            sections: Optional list of sections
            
        Returns:
            List[ModalityOutputUnit]: Modality outputs with updated positions and sizes
        """
        # Make a copy of outputs to avoid modifying originals
        outputs = [ModalityOutputUnit(
            modality=output.modality,
            content=output.content,
            content_type=output.content_type,
            metadata=output.metadata,
            position=output.position,
            size=output.size,
            confidence=output.confidence,
            importance=output.importance,
            section_id=output.section_id,
            dependencies=output.dependencies,
            rendering_options=output.rendering_options
        ) for output in modality_outputs]
        
        # Determine sizes for all outputs
        for output in outputs:
            if not output.size:
                output.size = self._get_default_size(output)
        
        # Group by section
        section_outputs = defaultdict(list)
        for output in outputs:
            section_outputs[output.section_id].append(output)
        
        # Define grid parameters
        num_columns = 12  # 12-column grid
        column_width = self.max_width // num_columns
        grid_margin = self.default_margins["left"]
        
        # Initialize current position
        current_y = self.default_margins["top"]
        
        # Process each section
        for section_id, section_items in section_outputs.items():
            # Sort section items by importance
            section_items.sort(key=lambda o: -o.importance)
            
            # Calculate grid layout for this section
            row_height = 0
            current_col = 0
            
            for item in section_items:
                width, height = item.size
                
                # Calculate columns needed for this item
                cols_needed = min(num_columns, max(1, width // column_width))
                
                # Check if we need to wrap to next row
                if current_col + cols_needed > num_columns:
                    current_col = 0
                    current_y += row_height + self.default_margins["bottom"]
                    row_height = 0
                
                # Calculate position
                x = grid_margin + current_col * column_width
                y = current_y
                
                # Update item
                item.position = (x, y)
                
                # Adjust size to fit grid
                grid_width = cols_needed * column_width
                if abs(width - grid_width) > column_width // 2:
                    # Need to adjust width to fit grid
                    aspect_ratio = width / height
                    new_width = grid_width - 2 * self.default_margins["right"]
                    new_height = new_width / aspect_ratio
                    item.size = (new_width, new_height)
                
                # Update tracking variables
                current_col += cols_needed
                row_height = max(row_height, item.size[1])
            
            # Update current position for next section
            current_y += row_height + 2 * self.default_margins["bottom"]
        
        return outputs
    
    def _force_directed_layout(
        self,
        modality_outputs: List[ModalityOutputUnit],
        sections: Optional[List[SectionUnit]] = None
    ) -> List[ModalityOutputUnit]:
        """
        Compute force-directed layout using physics simulation.
        
        Args:
            modality_outputs: List of modality outputs
            sections: Optional list of sections
            
        Returns:
            List[ModalityOutputUnit]: Modality outputs with updated positions and sizes
        """
        # This layout is best implemented with NetworkX, so use fallback if not available
        if not _HAS_NETWORKX:
            logger.warning("NetworkX not available. Using flow-based layout as fallback.")
            return self._flow_based_layout(modality_outputs, sections)
        
        # Make a copy of outputs to avoid modifying originals
        outputs = [ModalityOutputUnit(
            modality=output.modality,
            content=output.content,
            content_type=output.content_type,
            metadata=output.metadata,
            position=output.position,
            size=output.size,
            confidence=output.confidence,
            importance=output.importance,
            section_id=output.section_id,
            dependencies=output.dependencies,
            rendering_options=output.rendering_options
        ) for output in modality_outputs]
        
        # Determine sizes for all outputs
        for output in outputs:
            if not output.size:
                output.size = self._get_default_size(output)
        
        # Create graph
        G = nx.Graph()
        
        # Add nodes for each output
        for i, output in enumerate(outputs):
            G.add_node(i, 
                      modality=output.modality, 
                      importance=output.importance,
                      size=output.size,
                      section_id=output.section_id)
        
        # Add edges based on dependencies and section relationships
        for i, output in enumerate(outputs):
            # Connect to dependencies
            for j, other in enumerate(outputs):
                if other.id in output.dependencies:
                    G.add_edge(i, j, weight=2.0, type="dependency")
            
            # Connect items in same section
            for j, other in enumerate(outputs):
                if i != j and output.section_id == other.section_id:
                    G.add_edge(i, j, weight=1.0, type="section")
            
            # Connect sequential text items
            if output.modality == "text":
                for j, other in enumerate(outputs):
                    if i != j and other.modality == "text":
                        G.add_edge(i, j, weight=0.5, type="text_flow")
        
        # Compute layout with force-directed algorithm
        # Use different weights for fixed vs. flexible positions
        pos = nx.spring_layout(
            G, 
            k=0.15,  # Optimal distance between nodes
            iterations=100,
            scale=min(self.max_width, self.max_height) * 0.8
        )
        
        # Scale and shift positions to fit within bounds
        min_x = min(x for x, _ in pos.values())
        min_y = min(y for _, y in pos.values())
        max_x = max(x for x, _ in pos.values())
        max_y = max(y for _, y in pos.values())
        
        # Apply margins
        margin_x = self.default_margins["left"] + self.default_margins["right"]
        margin_y = self.default_margins["top"] + self.default_margins["bottom"]
        
        # Compute scaling factors
        scale_x = (self.max_width - margin_x) / (max_x - min_x) if max_x > min_x else 1.0
        scale_y = (self.max_height - margin_y) / (max_y - min_y) if max_y > min_y else 1.0
        scale = min(scale_x, scale_y)
        
        # Apply positions to outputs
        for i, output in enumerate(outputs):
            x, y = pos[i]
            
            # Scale and shift
            scaled_x = self.default_margins["left"] + (x - min_x) * scale
            scaled_y = self.default_margins["top"] + (y - min_y) * scale
            
            output.position = (int(scaled_x), int(scaled_y))
        
        # Resolve overlaps
        self._resolve_overlaps(outputs)
        
        # Ensure text is readable (adjust visual positions if needed)
        self._ensure_text_readability(outputs)
        
        return outputs
    
    def _ensure_text_readability(self, outputs: List[ModalityOutputUnit]) -> None:
        """
        Ensure text outputs are positioned for readability.
        
        Args:
            outputs: Modality outputs to process
        """
        # Sort by modality (text first)
        modality_priority = {"text": 0, "visual": 1, "action": 2}
        outputs.sort(key=lambda o: modality_priority.get(o.modality, 100))
        
        # Check text positions
        text_outputs = [o for o in outputs if o.modality == "text"]
        for text_output in text_outputs:
            text_x, text_y = text_output.position
            text_w, text_h = text_output.size
            
            # Check if any visuals overlap with this text
            for visual_output in [o for o in outputs if o.modality == "visual"]:
                visual_x, visual_y = visual_output.position
                visual_w, visual_h = visual_output.size
                
                # Check for overlap
                overlap_x = max(0, min(text_x + text_w, visual_x + visual_w) - max(text_x, visual_x))
                overlap_y = max(0, min(text_y + text_h, visual_y + visual_h) - max(text_y, visual_y))
                
                if overlap_x > 0 and overlap_y > 0:
                    # There is overlap - move the visual
                    if overlap_x < overlap_y:
                        # Move horizontally
                        if text_x <= visual_x:
                            visual_output.position = (text_x + text_w + self.default_margins["right"], visual_y)
                        else:
                            visual_output.position = (visual_x - text_w - self.default_margins["right"], visual_y)
                    else:
                        # Move vertically
                        if text_y <= visual_y:
                            visual_output.position = (visual_x, text_y + text_h + self.default_margins["bottom"])
                        else:
                            visual_output.position = (visual_x, visual_y - text_h - self.default_margins["bottom"])
    
    def _template_based_layout(
        self,
        modality_outputs: List[ModalityOutputUnit],
        sections: Optional[List[SectionUnit]] = None
    ) -> List[ModalityOutputUnit]:
        """
        Compute layout based on templates for common use cases.
        
        Args:
            modality_outputs: List of modality outputs
            sections: Optional list of sections
            
        Returns:
            List[ModalityOutputUnit]: Modality outputs with updated positions and sizes
        """
        # Make a copy of outputs to avoid modifying originals
        outputs = [ModalityOutputUnit(
            modality=output.modality,
            content=output.content,
            content_type=output.content_type,
            metadata=output.metadata,
            position=output.position,
            size=output.size,
            confidence=output.confidence,
            importance=output.importance,
            section_id=output.section_id,
            dependencies=output.dependencies,
            rendering_options=output.rendering_options
        ) for output in modality_outputs]
        
        # Determine sizes for all outputs
        for output in outputs:
            if not output.size:
                output.size = self._get_default_size(output)
        
        # Count modalities
        modality_counts = {
            "text": len([o for o in outputs if o.modality == "text"]),
            "visual": len([o for o in outputs if o.modality == "visual"]),
            "action": len([o for o in outputs if o.modality == "action"])
        }
        
        # Identify template based on content mix
        if modality_counts["visual"] == 0:
            # Text only or text + action
            return self._apply_text_template(outputs, sections)
        elif modality_counts["visual"] == 1 and modality_counts["text"] >= 1:
            # Single visual with text
            return self._apply_single_visual_template(outputs, sections)
        elif modality_counts["visual"] > 1:
            # Multiple visuals
            return self._apply_multi_visual_template(outputs, sections)
        else:
            # Fall back to flow layout for other cases
            return self._flow_based_layout(outputs, sections)
    
    def _apply_text_template(
        self,
        outputs: List[ModalityOutputUnit],
        sections: Optional[List[SectionUnit]] = None
    ) -> List[ModalityOutputUnit]:
        """
        Apply template for text-only content.
        
        Args:
            outputs: Modality outputs
            sections: Optional list of sections
            
        Returns:
            List[ModalityOutputUnit]: Positioned outputs
        """
        # Sort by section and then by importance
        if sections:
            section_positions = {section.section_id: section.position for section in sections}
            outputs.sort(key=lambda o: (
                section_positions.get(o.section_id, float('inf')), 
                -o.importance
            ))
        else:
            outputs.sort(key=lambda o: -o.importance)
        
        current_y = self.default_margins["top"]
        
        for output in outputs:
            width, height = output.size
            
            # Position text items in vertical flow
            output.position = (self.default_margins["left"], current_y)
            
            # Update current position
            current_y += height + self.default_margins["bottom"]
            
            # Add extra space between sections
            if output != outputs[-1] and output.section_id != outputs[outputs.index(output) + 1].section_id:
                current_y += self.default_margins["bottom"]
        
        return outputs
    
    def _apply_single_visual_template(
        self,
        outputs: List[ModalityOutputUnit],
        sections: Optional[List[SectionUnit]] = None
    ) -> List[ModalityOutputUnit]:
        """
        Apply template for content with a single visual.
        
        Args:
            outputs: Modality outputs
            sections: Optional list of sections
            
        Returns:
            List[ModalityOutputUnit]: Positioned outputs
        """
        # Find the visual output
        visual_output = next(o for o in outputs if o.modality == "visual")
        text_outputs = [o for o in outputs if o.modality == "text"]
        action_outputs = [o for o in outputs if o.modality == "action"]
        
        # Sort text outputs by importance
        text_outputs.sort(key=lambda o: -o.importance)
        
        # Position the visual
        visual_width, visual_height = visual_output.size
        visual_output.position = (
            self.max_width - visual_width - self.default_margins["right"],
            self.default_margins["top"]
        )
        
        # Position text around the visual
        text_width = self.max_width - visual_width - 3 * self.default_margins["right"]
        current_y = self.default_margins["top"]
        
        for i, text_output in enumerate(text_outputs):
            text_width_original, text_height = text_output.size
            
            # Adjust text width if needed
            if text_width_original > text_width:
                # Scale height proportionally
                text_output.size = (text_width, int(text_height * text_width / text_width_original))
            
            # Position text
            if i == 0 and len(text_outputs) > 1:
                # First text item (usually introduction) goes above visual
                text_output.position = (self.default_margins["left"], current_y)
                current_y += text_output.size[1] + self.default_margins["bottom"]
            else:
                # Other text items go to the left of visual
                text_output.position = (self.default_margins["left"], current_y)
                current_y += text_output.size[1] + self.default_margins["bottom"]
        
        # Check if we need to move the visual down
        visual_bottom = visual_output.position[1] + visual_height
        text_bottom = current_y
        
        if text_bottom > visual_bottom:
            # Adjust visual to be vertically centered with the text
            visual_output.position = (
                visual_output.position[0],
                max(self.default_margins["top"], 
                    (text_bottom - visual_height) // 2)
            )
        
        # Position action outputs at the bottom
        current_y = max(text_bottom, visual_bottom) + self.default_margins["bottom"]
        
        for action_output in action_outputs:
            action_width, action_height = action_output.size
            action_output.position = (self.default_margins["left"], current_y)
            current_y += action_height + self.default_margins["bottom"]
        
        return outputs
    
    def _apply_multi_visual_template(
        self,
        outputs: List[ModalityOutputUnit],
        sections: Optional[List[SectionUnit]] = None
    ) -> List[ModalityOutputUnit]:
        """
        Apply template for content with multiple visuals.
        
        Args:
            outputs: Modality outputs
            sections: Optional list of sections
            
        Returns:
            List[ModalityOutputUnit]: Positioned outputs
        """
        # Group by modality
        visual_outputs = [o for o in outputs if o.modality == "visual"]
        text_outputs = [o for o in outputs if o.modality == "text"]
        action_outputs = [o for o in outputs if o.modality == "action"]
        
        # Sort by importance
        visual_outputs.sort(key=lambda o: -o.importance)
        text_outputs.sort(key=lambda o: -o.importance)
        
        # Count visuals to determine layout
        num_visuals = len(visual_outputs)
        
        if num_visuals == 2:
            # Side-by-side visuals with text above and below
            return self._template_two_visuals(visual_outputs, text_outputs, action_outputs)
        elif num_visuals == 3:
            # Triangle arrangement
            return self._template_three_visuals(visual_outputs, text_outputs, action_outputs)
        elif num_visuals == 4:
            # Grid arrangement
            return self._template_four_visuals(visual_outputs, text_outputs, action_outputs)
        else:
            # Grid flow for many visuals
            return self._template_many_visuals(visual_outputs, text_outputs, action_outputs)
    
    def _template_two_visuals(
        self,
        visual_outputs: List[ModalityOutputUnit],
        text_outputs: List[ModalityOutputUnit],
        action_outputs: List[ModalityOutputUnit]
    ) -> List[ModalityOutputUnit]:
        """
        Apply template for two visuals.
        
        Args:
            visual_outputs: Visual outputs
            text_outputs: Text outputs
            action_outputs: Action outputs
            
        Returns:
            List[ModalityOutputUnit]: Positioned outputs
        """
        # Start with text at the top
        current_y = self.default_margins["top"]
        
        # Position introduction text
        if text_outputs:
            intro_text = text_outputs[0]
            intro_width, intro_height = intro_text.size
            intro_text.position = (self.default_margins["left"], current_y)
            current_y += intro_height + self.default_margins["bottom"]
        
        # Position visuals side by side
        visual_y = current_y
        
        # Calculate total width and spacing
        total_visual_width = sum(o.size[0] for o in visual_outputs)
        visual_spacing = (self.max_width - total_visual_width - 2 * self.default_margins["left"]) // 3
        visual_spacing = max(visual_spacing, self.default_margins["right"])
        
        current_x = self.default_margins["left"] + visual_spacing
        max_visual_height = 0
        
        for visual in visual_outputs:
            visual_width, visual_height = visual.size
            visual.position = (current_x, visual_y)
            current_x += visual_width + visual_spacing
            max_visual_height = max(max_visual_height, visual_height)
        
        current_y += max_visual_height + self.default_margins["bottom"]
        
        # Position remaining text below visuals
        for text in text_outputs[1:]:
            text_width, text_height = text.size
            text.position = (self.default_margins["left"], current_y)
            current_y += text_height + self.default_margins["bottom"]
        
        # Position action outputs at the bottom
        for action in action_outputs:
            action_width, action_height = action.size
            action.position = (self.default_margins["left"], current_y)
            current_y += action_height + self.default_margins["bottom"]
        
        # Combine all outputs
        return visual_outputs + text_outputs + action_outputs
    
    def _template_three_visuals(
        self,
        visual_outputs: List[ModalityOutputUnit],
        text_outputs: List[ModalityOutputUnit],
        action_outputs: List[ModalityOutputUnit]
    ) -> List[ModalityOutputUnit]:
        """
        Apply template for three visuals.
        
        Args:
            visual_outputs: Visual outputs
            text_outputs: Text outputs
            action_outputs: Action outputs
            
        Returns:
            List[ModalityOutputUnit]: Positioned outputs
        """
        # Start with text at the top
        current_y = self.default_margins["top"]
        
        # Position introduction text
        if text_outputs:
            intro_text = text_outputs[0]
            intro_width, intro_height = intro_text.size
            intro_text.position = (self.default_margins["left"], current_y)
            current_y += intro_height + self.default_margins["bottom"]
        
        # Position first visual at the top-center
        visual1 = visual_outputs[0]
        visual1_width, visual1_height = visual1.size
        visual1.position = (
            (self.max_width - visual1_width) // 2,
            current_y
        )
        current_y += visual1_height + self.default_margins["bottom"]
        
        # Position other visuals side by side below
        visual2 = visual_outputs[1]
        visual3 = visual_outputs[2]
        visual2_width, visual2_height = visual2.size
        visual3_width, visual3_height = visual3.size
        
        # Calculate positions for bottom row
        total_width = visual2_width + visual3_width
        spacing = (self.max_width - total_width - 2 * self.default_margins["left"]) // 3
        spacing = max(spacing, self.default_margins["right"])
        
        visual2.position = (
            self.default_margins["left"] + spacing,
            current_y
        )
        
        visual3.position = (
            self.default_margins["left"] + spacing + visual2_width + spacing,
            current_y
        )
        
        current_y += max(visual2_height, visual3_height) + self.default_margins["bottom"]
        
        # Position remaining text below visuals
        for text in text_outputs[1:]:
            text_width, text_height = text.size
            text.position = (self.default_margins["left"], current_y)
            current_y += text_height + self.default_margins["bottom"]
        
        # Position action outputs at the bottom
        for action in action_outputs:
            action_width, action_height = action.size
            action.position = (self.default_margins["left"], current_y)
            current_y += action_height + self.default_margins["bottom"]
        
        # Combine all outputs
        return visual_outputs + text_outputs + action_outputs
    
    def _template_four_visuals(
        self,
        visual_outputs: List[ModalityOutputUnit],
        text_outputs: List[ModalityOutputUnit],
        action_outputs: List[ModalityOutputUnit]
    ) -> List[ModalityOutputUnit]:
        """
        Apply template for four visuals.
        
        Args:
            visual_outputs: Visual outputs
            text_outputs: Text outputs
            action_outputs: Action outputs
            
        Returns:
            List[ModalityOutputUnit]: Positioned outputs
        """
        # Start with text at the top
        current_y = self.default_margins["top"]
        
        # Position introduction text
        if text_outputs:
            intro_text = text_outputs[0]
            intro_width, intro_height = intro_text.size
            intro_text.position = (self.default_margins["left"], current_y)
            current_y += intro_height + self.default_margins["bottom"]
        
        # Position visuals in a 2x2 grid
        visual_spacing_x = self.default_margins["right"] * 2
        visual_spacing_y = self.default_margins["bottom"] * 2
        
        # Calculate grid cell width
        grid_width = (self.max_width - 2 * self.default_margins["left"] - visual_spacing_x) // 2
        
        # Resize visuals to fit grid if needed
        for visual in visual_outputs:
            visual_width, visual_height = visual.size
            if visual_width > grid_width:
                # Scale height proportionally
                aspect_ratio = visual_height / visual_width
                new_width = grid_width
                new_height = int(new_width * aspect_ratio)
                visual.size = (new_width, new_height)
        
        # Position visuals
        row1_y = current_y
        row2_y = row1_y
        
        # First row
        for i in range(2):
            if i < len(visual_outputs):
                visual = visual_outputs[i]
                visual_width, visual_height = visual.size
                x = self.default_margins["left"] + i * (grid_width + visual_spacing_x)
                visual.position = (x, row1_y)
                row2_y = max(row2_y, row1_y + visual_height + visual_spacing_y)
        
        # Second row
        for i in range(2):
            if i + 2 < len(visual_outputs):
                visual = visual_outputs[i + 2]
                visual_width, visual_height = visual.size
                x = self.default_margins["left"] + i * (grid_width + visual_spacing_x)
                visual.position = (x, row2_y)
                current_y = max(current_y, row2_y + visual_height + self.default_margins["bottom"])
        
        # Position remaining text below visuals
        for text in text_outputs[1:]:
            text_width, text_height = text.size
            text.position = (self.default_margins["left"], current_y)
            current_y += text_height + self.default_margins["bottom"]
        
        # Position action outputs at the bottom
        for action in action_outputs:
            action_width, action_height = action.size
            action.position = (self.default_margins["left"], current_y)
            current_y += action_height + self.default_margins["bottom"]
        
        # Combine all outputs
        return visual_outputs + text_outputs + action_outputs
    
    def _template_many_visuals(
        self,
        visual_outputs: List[ModalityOutputUnit],
        text_outputs: List[ModalityOutputUnit],
        action_outputs: List[ModalityOutputUnit]
    ) -> List[ModalityOutputUnit]:
        """
        Apply template for many visuals.
        
        Args:
            visual_outputs: Visual outputs
            text_outputs: Text outputs
            action_outputs: Action outputs
            
        Returns:
            List[ModalityOutputUnit]: Positioned outputs
        """
        # Start with text at the top
        current_y = self.default_margins["top"]
        
        # Position introduction text
        if text_outputs:
            intro_text = text_outputs[0]
            intro_width, intro_height = intro_text.size
            intro_text.position = (self.default_margins["left"], current_y)
            current_y += intro_height + self.default_margins["bottom"]
        
        # Position visuals in a grid
        visuals_per_row = min(3, len(visual_outputs))
        visual_spacing_x = self.default_margins["right"] * 2
        visual_spacing_y = self.default_margins["bottom"] * 2
        
        # Calculate grid cell width
        grid_width = (self.max_width - 2 * self.default_margins["left"] - 
                      (visuals_per_row - 1) * visual_spacing_x) // visuals_per_row
        
        # Resize visuals to fit grid if needed
        for visual in visual_outputs:
            visual_width, visual_height = visual.size
            if visual_width > grid_width:
                # Scale height proportionally
                aspect_ratio = visual_height / visual_width
                new_width = grid_width
                new_height = int(new_width * aspect_ratio)
                visual.size = (new_width, new_height)
        
        # Position visuals
        row_y = current_y
        max_height_in_row = 0
        
        for i, visual in enumerate(visual_outputs):
            # Check if we need to start a new row
            if i > 0 and i % visuals_per_row == 0:
                row_y += max_height_in_row + visual_spacing_y
                max_height_in_row = 0
            
            # Calculate position in current row
            col = i % visuals_per_row
            x = self.default_margins["left"] + col * (grid_width + visual_spacing_x)
            
            visual_width, visual_height = visual.size
            visual.position = (x, row_y)
            
            max_height_in_row = max(max_height_in_row, visual_height)
            
            # Update current_y for next elements
            current_y = max(current_y, row_y + max_height_in_row + self.default_margins["bottom"])
        
        # Position remaining text below visuals
        for text in text_outputs[1:]:
            text_width, text_height = text.size
            text.position = (self.default_margins["left"], current_y)
            current_y += text_height + self.default_margins["bottom"]
        
        # Position action outputs at the bottom
        for action in action_outputs:
            action_width, action_height = action.size
            action.position = (self.default_margins["left"], current_y)
            current_y += action_height + self.default_margins["bottom"]
        
        # Combine all outputs
        return visual_outputs + text_outputs + action_outputs
    
    def _get_default_size(self, output: ModalityOutputUnit) -> Tuple[int, int]:
        """
        Get default size for a modality output.
        
        Args:
            output: Modality output
            
        Returns:
            Tuple[int, int]: Default (width, height)
        """
        modality = output.modality
        content_type = output.content_type
        
        # Get default size map for this modality
        modality_sizes = self.default_sizes.get(modality, {})
        
        # Get default size for this content type
        default_size = modality_sizes.get(content_type, modality_sizes.get("default", (400, 300)))
        
        # Adjust size based on importance and additional factors
        importance_factor = 0.8 + output.importance * 0.4  # Scale from 0.8 to 1.2
        
        # Apply dominance factors
        if modality == "visual":
            dominance = self.visual_dominance_factor
        elif modality == "text":
            dominance = self.text_dominance_factor
        elif modality == "action":
            dominance = self.action_dominance_factor
        else:
            dominance = 1.0
        
        # Calculate adjusted size
        width, height = default_size
        adjusted_width = min(self.max_width, int(width * importance_factor * dominance))
        adjusted_height = min(self.max_height // 2, int(height * importance_factor * dominance))
        
        return (adjusted_width, adjusted_height)


class CrossModalAttention:
    """
    Cross-modal attention mechanism for aligning information across modalities.
    
    This class implements attention mechanisms that enable modalities to inform
    and enhance each other for more coherent multimodal outputs.
    """
    
    def __init__(
        self,
        embedding_dim: int = 512,
        num_heads: int = 8,
        num_layers: int = 2,
        dropout: float = 0.1,
        modalities: List[str] = None
    ):
        """
        Initialize cross-modal attention mechanism.
        
        Args:
            embedding_dim: Dimension of embeddings
            num_heads: Number of attention heads
            num_layers: Number of attention layers
            dropout: Dropout rate
            modalities: List of modality types
        """
        self.embedding_dim = embedding_dim
        self.num_heads = num_heads
        self.num_layers = num_layers
        self.dropout = dropout
        self.modalities = modalities or ["text", "visual", "action"]
        
        # Create attention matrices for each modality pair
        self.attention_weights = {}
        for source_modality in self.modalities:
            for target_modality in self.modalities:
                if source_modality != target_modality:
                    key = f"{source_modality}_to_{target_modality}"
                    self.attention_weights[key] = np.random.normal(0, 0.1, 
                                                                 (embedding_dim, embedding_dim))
        
        logger.debug(f"Initialized CrossModalAttention with {len(self.attention_weights)} pairs")
    
    @log_execution_time
    def compute_attention(
        self,
        source_outputs: List[ModalityOutputUnit],
        target_outputs: List[ModalityOutputUnit]
    ) -> np.ndarray:
        """
        Compute attention weights between source and target outputs.
        
        Args:
            source_outputs: Source modality outputs
            target_outputs: Target modality outputs
            
        Returns:
            np.ndarray: Attention weights matrix
        """
        # Extract modality types
        source_modality = source_outputs[0].modality if source_outputs else None
        target_modality = target_outputs[0].modality if target_outputs else None
        
        if not source_modality or not target_modality or source_modality == target_modality:
            return np.zeros((len(source_outputs), len(target_outputs)))
        
        # Get attention weight matrix for this modality pair
        key = f"{source_modality}_to_{target_modality}"
        weight_matrix = self.attention_weights.get(key, np.identity(self.embedding_dim))
        
        # Compute attention scores
        attention_scores = np.zeros((len(source_outputs), len(target_outputs)))
        
        for i, source in enumerate(source_outputs):
            if source.features is None:
                continue
                
            for j, target in enumerate(target_outputs):
                if target.features is None:
                    continue
                
                # Pad or truncate features to embedding dimension
                source_features = self._normalize_features(source.features)
                target_features = self._normalize_features(target.features)
                
                # Apply attention weight matrix
                score = np.matmul(np.matmul(source_features, weight_matrix), target_features.T)
                
                # Store score
                attention_scores[i, j] = score
        
        # Normalize scores
        row_sums = attention_scores.sum(axis=1, keepdims=True)
        if np.any(row_sums > 0):
            attention_scores = np.divide(attention_scores, row_sums, 
                                      out=np.zeros_like(attention_scores), where=row_sums != 0)
        
        return attention_scores
    
    def align_content(
        self,
        modality_outputs: Dict[str, List[ModalityOutputUnit]]
    ) -> Dict[str, List[ModalityOutputUnit]]:
        """
        Align content across modalities using attention.
        
        Args:
            modality_outputs: Outputs grouped by modality
            
        Returns:
            Dict[str, List[ModalityOutputUnit]]: Aligned outputs
        """
        # Create a copy of outputs
        aligned_outputs = {modality: outputs.copy() for modality, outputs in modality_outputs.items()}
        
        # Compute alignments between each modality pair
        for source_modality in self.modalities:
            if source_modality not in modality_outputs:
                continue
                
            source_outputs = modality_outputs[source_modality]
            if not source_outputs:
                continue
                
            for target_modality in self.modalities:
                if (target_modality not in modality_outputs or 
                    source_modality == target_modality):
                    continue
                    
                target_outputs = modality_outputs[target_modality]
                if not target_outputs:
                    continue
                
                # Compute attention
                attention_scores = self.compute_attention(source_outputs, target_outputs)
                
                # Apply alignments
                self._apply_alignments(source_outputs, target_outputs, attention_scores,
                                     aligned_outputs[source_modality], aligned_outputs[target_modality])
        
        return aligned_outputs
    
    def _normalize_features(self, features: np.ndarray) -> np.ndarray:
        """
        Normalize features to embedding dimension.
        
        Args:
            features: Feature vector
            
        Returns:
            np.ndarray: Normalized features
        """
        # Ensure features is numpy array
        if not isinstance(features, np.ndarray):
            features = np.array(features)
        
        # Handle dimensionality
        if len(features) > self.embedding_dim:
            # Truncate
            return features[:self.embedding_dim]
        elif len(features) < self.embedding_dim:
            # Pad with zeros
            padded = np.zeros(self.embedding_dim)
            padded[:len(features)] = features
            return padded
        else:
            return features
    
    def _apply_alignments(
        self,
        source_outputs: List[ModalityOutputUnit],
        target_outputs: List[ModalityOutputUnit],
        attention_scores: np.ndarray,
        aligned_sources: List[ModalityOutputUnit],
        aligned_targets: List[ModalityOutputUnit]
    ) -> None:
        """
        Apply alignments based on attention scores.
        
        Args:
            source_outputs: Source modality outputs
            target_outputs: Target modality outputs
            attention_scores: Attention scores between sources and targets
            aligned_sources: Output list for aligned sources
            aligned_targets: Output list for aligned targets
        """
        # Find highest attention pairs
        for i, source in enumerate(source_outputs):
            best_target_idx = np.argmax(attention_scores[i])
            best_score = attention_scores[i, best_target_idx]
            
            # Only apply if score is significant
            if best_score > 0.3:
                target = target_outputs[best_target_idx]
                
                # Add dependency between items
                aligned_sources[i].dependencies.append(target.id)
                aligned_targets[best_target_idx].dependencies.append(source.id)
                
                # Update metadata to indicate alignment
                if "alignments" not in aligned_sources[i].metadata:
                    aligned_sources[i].metadata["alignments"] = []
                
                if "alignments" not in aligned_targets[best_target_idx].metadata:
                    aligned_targets[best_target_idx].metadata["alignments"] = []
                
                aligned_sources[i].metadata["alignments"].append({
                    "target_id": target.id,
                    "target_modality": target.modality,
                    "score": float(best_score)
                })
                
                aligned_targets[best_target_idx].metadata["alignments"].append({
                    "source_id": source.id,
                    "source_modality": source.modality,
                    "score": float(best_score)
                })


class MultiModalSynthesizer:
    """
    Core class for synthesizing multimodal outputs.
    
    This class orchestrates the generation of coherent multimodal responses
    by coordinating content planning, modality selection, generation, and layout.
    """
    
    def __init__(
        self,
        config: MultiModalSynthesizerConfig,
        text_generator: Optional['TextGenerator'] = None,
        visual_generator: Optional['VisualGenerator'] = None,
        action_generator: Optional['ActionGenerator'] = None
    ):
        """
        Initialize multimodal synthesizer.
        
        Args:
            config: Configuration for the synthesizer
            text_generator: Text generator
            visual_generator: Visual generator
            action_generator: Action generator
        """
        self.config = config
        self.device = torch.device(config.device)
        
        # Initialize modality generators
        self.generators = {}
        
        if text_generator is not None:
            self.generators["text"] = text_generator
        
        if visual_generator is not None:
            self.generators["visual"] = visual_generator
        
        if action_generator is not None:
            self.generators["action"] = action_generator
        
        # Initialize content planner
        self.content_planner = ContentPlanner(
            modality_types=config.modality_types,
            modality_ratios=config.modality_ratios,
            primary_modality=config.primary_modality,
            max_modalities=config.max_modalities_per_response,
            coherence_threshold=config.coherence_threshold,
            selection_strategy=config.modality_selection_strategy
        )
        
        # Initialize semantic segmenter
        self.semantic_segmenter = SemanticContentSegmenter(
            segmentation_threshold=config.semantic_segmentation_threshold,
            use_spacy=config.use_semantic_segmentation
        )
        
        # Initialize layout engine
        self.layout_engine = LayoutEngine(
            algorithm=config.layout_algorithm,
            optimization_steps=config.layout_optimization_steps,
            energy_weights=config.layout_energy_weights,
            visual_dominance_factor=config.visual_dominance_factor,
            text_dominance_factor=config.text_dominance_factor,
            action_dominance_factor=config.action_dominance_factor
        )
        
        # Initialize cross-modal attention
        self.cross_modal_attention = CrossModalAttention(
            embedding_dim=config.cross_modal_dim,
            num_heads=config.cross_modal_attention_heads,
            num_layers=config.cross_modal_attention_layers,
            modalities=config.modality_types
        )
        
        # Initialize reasoning trace formatter if needed
        if config.enable_tracing:
            self.reasoning_formatter = ReasoningTraceFormatter(
                include_trace=True,
                trace_format="structured",
                trace_detail="medium",
                max_trace_length=1000,
                show_uncertainties=True
            )
        else:
            self.reasoning_formatter = None
        
        # Create cache
        self.cache = OrderedDict()
        self.cache_enabled = config.use_cache and config.cache_size > 0
        self.cache_size = config.cache_size
        
        # Load modality-specific configurations
        self._load_modality_configs()
        
        # Initialize metrics
        self.metrics = defaultdict(float)
        self.metrics_count = defaultdict(int)
        
        logger.info(f"Initialized MultiModalSynthesizer with {len(self.generators)} modality generators")
    
    def _load_modality_configs(self) -> None:
        """Load modality-specific configurations."""
        # Text generator config
        if "text" in self.config.modality_configs:
            self.text_config = self.config.modality_configs["text"]
        elif self.config.text_generator_config is not None:
            self.text_config = self.config.text_generator_config
        else:
            self.text_config = None
        
        # Visual generator config
        if "visual" in self.config.modality_configs:
            self.visual_config = self.config.modality_configs["visual"]
        elif self.config.visual_generator_config is not None:
            self.visual_config = self.config.visual_generator_config
        else:
            self.visual_config = None
        
        # Action generator config
        if "action" in self.config.modality_configs:
            self.action_config = self.config.modality_configs["action"]
        elif self.config.action_generator_config is not None:
            self.action_config = self.config.action_generator_config
        else:
            self.action_config = None
    
    def _compute_hash(self, input_data: Any) -> str:
        """
        Compute a hash for cache lookup.
        
        Args:
            input_data: Input data to be hashed
            
        Returns:
            str: Hash string
        """
        try:
            if isinstance(input_data, str):
                # Hash string directly
                return hashlib.sha256(input_data.encode('utf-8')).hexdigest()
            elif isinstance(input_data, dict):
                # Sort dict items and concatenate
                items = sorted((str(k), str(v)) for k, v in input_data.items())
                return hashlib.sha256(str(items).encode('utf-8')).hexdigest()
            else:
                # Try to pickle and hash
                data_bytes = pickle.dumps(input_data)
                return hashlib.sha256(data_bytes).hexdigest()
        except Exception as e:
            logger.warning(f"Failed to hash input for caching: {str(e)}")
            return None
    
    def _manage_cache_size(self) -> None:
        """
        Ensure the cache does not exceed the maximum allowed size by removing the oldest items.
        """
        if len(self.cache) > self.cache_size:
            # Remove oldest items
            for _ in range(len(self.cache) - self.cache_size):
                self.cache.popitem(last=False)
    
    @log_execution_time
    @performance_monitor
    def generate(
        self,
        query: str,
        context: Optional[Dict[str, Any]] = None,
        reasoning_trace: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Generate a multimodal response.
        
        Args:
            query: User query
            context: Optional context information
            reasoning_trace: Optional reasoning trace
            **kwargs: Additional generation parameters
            
        Returns:
            Dict[str, Any]: Multimodal response
        """
        start_time = time.time()
        context = context or {}
        
        # Check cache if enabled
        if self.cache_enabled:
            cache_key = self._compute_hash((query, context))
            if cache_key in self.cache:
                logger.debug(f"Cache hit for query: {query[:50]}...")
                return self.cache[cache_key]
        
        # Add query to context
        context["query"] = query
        
        try:
            # Step 1: Plan content based on query and context
            content_plan = self._plan_content(query, context)
            
            # Step 2: Generate outputs for each modality
            modality_outputs = self._generate_modality_outputs(content_plan, context, reasoning_trace)
            
            # Step 3: Post-process and align outputs across modalities
            aligned_outputs = self._align_outputs(modality_outputs, content_plan)
            
            # Step 4: Compute layout for outputs
            sections, positioned_outputs = self._compute_layout(aligned_outputs, content_plan)
            
            # Step 5: Prepare final response
            response = self._prepare_response(positioned_outputs, sections, content_plan, reasoning_trace)
            
            # Update cache
            if self.cache_enabled and cache_key is not None:
                self.cache[cache_key] = response
                self._manage_cache_size()
            
            # Update metrics
            generation_time = time.time() - start_time
            self.metrics["generation_time"] += generation_time
            self.metrics_count["generation_time"] += 1
            
            return response
            
        except Exception as e:
            logger.error(f"Error generating multimodal response: {str(e)}", exc_info=True)
            # Fallback to primary modality
            return self._generate_fallback_response(query, context, reasoning_trace)
    
    @log_execution_time
    def _plan_content(
        self,
        query: str,
        context: Dict[str, Any]
    ) -> ContentPlan:
        """
        Plan content for multimodal response.
        
        Args:
            query: User query
            context: Context information
            
        Returns:
            ContentPlan: Content plan
        """
        # Determine available modalities
        available_modalities = list(self.generators.keys())
        if not available_modalities:
            logger.warning("No modality generators available. Using primary modality only.")
            available_modalities = [self.config.primary_modality]
        
        # Use content planner to create plan
        plan_data = self.content_planner.plan_content(
            query=query,
            context=context,
            available_modalities=available_modalities
        )
        
        # Create sections based on semantic segmentation if enabled
        if self.config.use_semantic_segmentation:
            segments = self.semantic_segmenter.segment_query(query)
            
            # Update or replace sections in plan
            if segments:
                # Keep introduction and conclusion if present
                intro_section = next((s for s in plan_data["structure"] if s.get("role") == "introduction"), None)
                conclusion_section = next((s for s in plan_data["structure"] if s.get("role") == "conclusion"), None)
                
                # Build new structure
                new_structure = []
                
                # Add introduction if present
                if intro_section:
                    new_structure.append(intro_section)
                
                # Add segments
                for i, segment in enumerate(segments):
                    new_structure.append({
                        "role": f"section_{i+1}",
                        "modality": self._choose_modality_for_segment(segment, plan_data["modalities"]),
                        "purpose": segment["purpose"],
                        "content_type": self._get_content_type_for_purpose(segment["purpose"]),
                        "section_id": segment["section_id"],
                        "title": segment["title"],
                        "text": segment["text"]
                    })
                
                # Add conclusion if present
                if conclusion_section:
                    new_structure.append(conclusion_section)
                
                # Update plan
                plan_data["structure"] = new_structure
        
        # Create ContentPlan object
        content_plan = ContentPlan(
            query=query,
            modalities=plan_data["modalities"],
            sections=plan_data["structure"],
            primary_modality=plan_data["primary_modality"],
            modality_assignments=plan_data["modality_assignments"],
            metadata=plan_data["metadata"]
        )
        
        logger.debug(f"Created content plan with {len(content_plan.sections)} sections "
                    f"and {len(content_plan.modalities)} modalities")
        
        return content_plan
    
    def _choose_modality_for_segment(
        self,
        segment: Dict[str, Any],
        available_modalities: List[str]
    ) -> str:
        """
        Choose the best modality for a segment.
        
        Args:
            segment: Segment information
            available_modalities: Available modalities
            
        Returns:
            str: Chosen modality
        """
        purpose = segment["purpose"]
        
        # Simple heuristic for modality selection based on purpose
        if purpose in ["compare aspects", "analyze topic"]:
            # Visual comparison or analysis is often effective
            return "visual" if "visual" in available_modalities else self.config.primary_modality
            
        elif purpose in ["list items", "explain concept"]:
            # Both text and visuals can work well
            if "visual" in available_modalities and "text" in available_modalities:
                # Randomize with weighted probability
                return "visual" if np.random.random() < 0.4 else "text"
            
        elif purpose in ["address question", "provide background"]:
            # Text is typically more appropriate
            return "text" if "text" in available_modalities else self.config.primary_modality
            
        elif purpose in ["summarize content"]:
            # Text summaries are typically more effective
            return "text" if "text" in available_modalities else self.config.primary_modality
        
        # Default to primary modality
        return self.config.primary_modality if self.config.primary_modality in available_modalities else available_modalities[0]
    
    def _get_content_type_for_purpose(self, purpose: str) -> str:
        """
        Get appropriate content type for a purpose.
        
        Args:
            purpose: Purpose of content
            
        Returns:
            str: Content type
        """
        purpose_to_type = {
            "address question": "explanation",
            "compare aspects": "comparison",
            "list items": "enumeration",
            "explain concept": "explanation",
            "analyze topic": "analysis",
            "provide background": "narrative",
            "summarize content": "summary",
            "address topic": "explanation"
        }
        
        return purpose_to_type.get(purpose, "explanation")
    
    @log_execution_time
    def _generate_modality_outputs(
        self,
        content_plan: ContentPlan,
        context: Dict[str, Any],
        reasoning_trace: Optional[Dict[str, Any]] = None
    ) -> Dict[str, List[ModalityOutputUnit]]:
        """
        Generate outputs for each modality.
        
        Args:
            content_plan: Content plan
            context: Context information
            reasoning_trace: Optional reasoning trace
            
        Returns:
            Dict[str, List[ModalityOutputUnit]]: Outputs grouped by modality
        """
        # Initialize outputs dictionary
        modality_outputs = {modality: [] for modality in content_plan.modalities}
        
        # Create sections dictionary
        section_data = {}
        for i, section in enumerate(content_plan.sections):
            section_id = section.get("section_id", f"section-{i+1}")
            section_data[section_id] = section
        
        # Generate outputs for each modality
        for modality in content_plan.modalities:
            generator = self.generators.get(modality)
            if not generator:
                logger.warning(f"No generator available for {modality}. Skipping.")
                continue
            
            # Prepare concepts for this modality
            concepts = content_plan.modality_assignments.get(modality, [])
            
            # Generate content for relevant sections
            for section in content_plan.sections:
                section_id = section.get("section_id", "")
                section_modality = section.get("modality")
                
                # Check if this section should use this modality
                if section_modality == modality:
                    try:
                        # Generate content for this section
                        output = self._generate_for_section(
                            generator=generator,
                            modality=modality,
                            section=section,
                            concepts=concepts,
                            context=context,
                            reasoning_trace=reasoning_trace
                        )
                        
                        if output:
                            modality_outputs[modality].append(output)
                            
                    except Exception as e:
                        logger.error(f"Error generating {modality} for section {section_id}: {str(e)}")
            
            # Generate additional content if needed
            if (len(modality_outputs[modality]) < len([s for s in content_plan.sections if s.get("modality") == modality])
                and modality != self.config.primary_modality):
                try:
                    # Generate supplementary content
                    output = self._generate_supplementary(
                        generator=generator,
                        modality=modality,
                        concepts=concepts,
                        context=context,
                        reasoning_trace=reasoning_trace
                    )
                    
                    if output:
                        modality_outputs[modality].append(output)
                        
                except Exception as e:
                    logger.error(f"Error generating supplementary {modality}: {str(e)}")
        
        # Ensure primary modality has content
        if self.config.primary_modality in modality_outputs and not modality_outputs[self.config.primary_modality]:
            generator = self.generators.get(self.config.primary_modality)
            if generator:
                try:
                    # Generate fallback content
                    output = self._generate_fallback_content(
                        generator=generator,
                        modality=self.config.primary_modality,
                        query=content_plan.query,
                        context=context,
                        reasoning_trace=reasoning_trace
                    )
                    
                    if output:
                        modality_outputs[self.config.primary_modality].append(output)
                        
                except Exception as e:
                    logger.error(f"Error generating fallback content: {str(e)}")
        
        # Log generation results
        for modality, outputs in modality_outputs.items():
            logger.debug(f"Generated {len(outputs)} {modality} outputs")
        
        return modality_outputs
    
    def _generate_for_section(
        self,
        generator: Any,
        modality: str,
        section: Dict[str, Any],
        concepts: List[str],
        context: Dict[str, Any],
        reasoning_trace: Optional[Dict[str, Any]] = None
    ) -> Optional[ModalityOutputUnit]:
        """
        Generate content for a specific section.
        
        Args:
            generator: Modality generator
            modality: Modality type
            section: Section information
            concepts: Concepts for this modality
            context: Context information
            reasoning_trace: Optional reasoning trace
            
        Returns:
            ModalityOutputUnit: Generated output or None if generation fails
        """
        section_id = section.get("section_id", "")
        role = section.get("role", "")
        purpose = section.get("purpose", "")
        content_type = section.get("content_type", "")
        
        # Filter concepts relevant to this section
        section_text = section.get("text", "")
        if section_text:
            # Filter concepts that appear in the section text
            section_concepts = [c for c in concepts 
                              if c.lower() in section_text.lower()]
        else:
            # Use all concepts if no section text
            section_concepts = concepts
        
        # Create generation prompt based on section
        if modality == "text":
            prompt = self._create_text_prompt(section, section_concepts, context)
        elif modality == "visual":
            prompt = self._create_visual_prompt(section, section_concepts, context)
        elif modality == "action":
            prompt = self._create_action_prompt(section, section_concepts, context)
        else:
            prompt = f"Generate {modality} content for: {section_text or purpose}"
        
        # Add context and section information
        generation_context = context.copy()
        generation_context.update({
            "section_id": section_id,
            "section_role": role,
            "section_purpose": purpose,
            "content_type": content_type,
            "concepts": section_concepts
        })
        
        # Generate content
        try:
            if modality == "text":
                content = generator.generate(prompt, generation_context)
            elif modality == "visual":
                content = generator.generate(
                    prompt, 
                    generation_context,
                    content_type=content_type
                )
            elif modality == "action":
                content = generator.generate(
                    prompt, 
                    generation_context,
                    purpose=purpose
                )
            else:
                content = generator.generate(prompt, generation_context)
                
            # Create output unit
            output = ModalityOutputUnit(
                modality=modality,
                content=content,
                content_type=content_type,
                metadata={
                    "section_id": section_id,
                    "section_role": role,
                    "section_purpose": purpose,
                    "concepts": section_concepts,
                    "generated_from": "section"
                },
                position=None,  # Will be set during layout
                size=None,      # Will be set during layout
                confidence=0.9,
                importance=self._calculate_importance(section),
                section_id=section_id
            )
            
            return output
            
        except Exception as e:
            logger.error(f"Error in {modality} generation for section {section_id}: {str(e)}")
            return None
    
    def _generate_supplementary(
        self,
        generator: Any,
        modality: str,
        concepts: List[str],
        context: Dict[str, Any],
        reasoning_trace: Optional[Dict[str, Any]] = None
    ) -> Optional[ModalityOutputUnit]:
        """
        Generate supplementary content for a modality.
        
        Args:
            generator: Modality generator
            modality: Modality type
            concepts: Concepts for this modality
            context: Context information
            reasoning_trace: Optional reasoning trace
            
        Returns:
            ModalityOutputUnit: Generated output or None if generation fails
        """
        query = context.get("query", "")
        
        # Create appropriate prompt based on modality
        if modality == "visual":
            content_type = "visualization"
            prompt = f"Create a visual representation that enhances understanding of: {query}"
            if concepts:
                prompt += f"\nFocus on these key concepts: {', '.join(concepts[:5])}"
        elif modality == "action":
            content_type = "function"
            prompt = f"Generate executable code or commands related to: {query}"
            if concepts:
                prompt += f"\nIncorporate these key elements: {', '.join(concepts[:5])}"
        else:
            content_type = "explanation"
            prompt = f"Provide supplementary information about: {query}"
            if concepts:
                prompt += f"\nCovering these key concepts: {', '.join(concepts[:5])}"
        
        # Add context information
        generation_context = context.copy()
        generation_context.update({
            "purpose": "supplementary",
            "content_type": content_type,
            "concepts": concepts
        })
        
        # Generate content
        try:
            if modality == "text":
                content = generator.generate(prompt, generation_context)
            elif modality == "visual":
                content = generator.generate(
                    prompt, 
                    generation_context,
                    content_type=content_type
                )
            elif modality == "action":
                content = generator.generate(
                    prompt, 
                    generation_context,
                    purpose="supplementary"
                )
            else:
                content = generator.generate(prompt, generation_context)
                
            # Create output unit
            output = ModalityOutputUnit(
                modality=modality,
                content=content,
                content_type=content_type,
                metadata={
                    "purpose": "supplementary",
                    "concepts": concepts,
                    "generated_from": "supplementary"
                },
                position=None,  # Will be set during layout
                size=None,      # Will be set during layout
                confidence=0.7,
                importance=0.5,
                section_id="supplementary"  # Generic section ID
            )
            
            return output
            
        except Exception as e:
            logger.error(f"Error in supplementary {modality} generation: {str(e)}")
            return None
    
    def _generate_fallback_content(
        self,
        generator: Any,
        modality: str,
        query: str,
        context: Dict[str, Any],
        reasoning_trace: Optional[Dict[str, Any]] = None
    ) -> Optional[ModalityOutputUnit]:
        """
        Generate fallback content when other generations fail.
        
        Args:
            generator: Modality generator
            modality: Modality type
            query: User query
            context: Context information
            reasoning_trace: Optional reasoning trace
            
        Returns:
            ModalityOutputUnit: Generated output or None if generation fails
        """
        # Create simple prompt
        prompt = f"Answer the following: {query}"
        
        # Generate content
        try:
            if modality == "text":
                content_type = "explanation"
                content = generator.generate(prompt, context)
            elif modality == "visual":
                content_type = "visualization"
                content = generator.generate(
                    prompt, 
                    context,
                    content_type=content_type
                )
            elif modality == "action":
                content_type = "function"
                content = generator.generate(
                    prompt, 
                    context,
                    purpose="answer"
                )
            else:
                content_type = "explanation"
                content = generator.generate(prompt, context)
                
            # Create output unit
            output = ModalityOutputUnit(
                modality=modality,
                content=content,
                content_type=content_type,
                metadata={
                    "purpose": "fallback",
                    "generated_from": "fallback"
                },
                position=None,  # Will be set during layout
                size=None,      # Will be set during layout
                confidence=0.6,
                importance=0.9,
                section_id="fallback"  # Generic section ID
            )
            
            return output
            
        except Exception as e:
            logger.error(f"Error in fallback {modality} generation: {str(e)}")
            return None
    
    def _create_text_prompt(
        self,
        section: Dict[str, Any],
        concepts: List[str],
        context: Dict[str, Any]
    ) -> str:
        """
        Create text generation prompt.
        
        Args:
            section: Section information
            concepts: Concepts to include
            context: Context information
            
        Returns:
            str: Generation prompt
        """
        purpose = section.get("purpose", "")
        content_type = section.get("content_type", "")
        query = context.get("query", "")
        
        # Start with section text if available
        section_text = section.get("text", "")
        if section_text:
            prompt = f"Write a {content_type} about: {section_text}"
        else:
            prompt = f"Write a {content_type} that {purpose} for the query: {query}"
        
        # Add concepts
        if concepts:
            prompt += f"\nInclude these key concepts: {', '.join(concepts[:5])}"
            
            # Add remaining concepts if there are many
            if len(concepts) > 5:
                prompt += f"\nAlso consider these additional concepts if relevant: {', '.join(concepts[5:10])}"
        
        # Add formatting guidance based on content type
        if content_type == "explanation":
            prompt += "\nProvide a clear and detailed explanation."
        elif content_type == "summary":
            prompt += "\nCreate a concise summary highlighting the key points."
        elif content_type == "analysis":
            prompt += "\nOffer an analytical perspective with supporting evidence."
        elif content_type == "enumeration":
            prompt += "\nPresent information as a structured list or enumeration."
        elif content_type == "comparison":
            prompt += "\nStructure as a comparison highlighting similarities and differences."
        
        return prompt
    
    def _create_visual_prompt(
        self,
        section: Dict[str, Any],
        concepts: List[str],
        context: Dict[str, Any]
    ) -> str:
        """
        Create visual generation prompt.
        
        Args:
            section: Section information
            concepts: Concepts to include
            context: Context information
            
        Returns:
            str: Generation prompt
        """
        purpose = section.get("purpose", "")
        content_type = section.get("content_type", "")
        query = context.get("query", "")
        
        # Start with section text if available
        section_text = section.get("text", "")
        if section_text:
            prompt = f"Create a {content_type} visualization for: {section_text}"
        else:
            prompt = f"Create a {content_type} visualization that {purpose} for the query: {query}"
        
        # Add concepts
        if concepts:
            prompt += f"\nFeature these key elements: {', '.join(concepts[:5])}"
        
        # Add specific guidance based on content type
        if content_type == "chart":
            prompt += "\nCreate a data chart that effectively presents the information."
        elif content_type == "diagram":
            prompt += "\nCreate a diagram showing relationships or processes."
        elif content_type == "graph":
            prompt += "\nCreate a graph showing connections between elements."
        elif content_type == "visualization":
            prompt += "\nCreate a visual representation that enhances understanding."
        
        # Add styling guidance
        prompt += f"\nUse a {self.config.style} visual style that clearly communicates the information."
        
        return prompt
    
    def _create_action_prompt(
        self,
        section: Dict[str, Any],
        concepts: List[str],
        context: Dict[str, Any]
    ) -> str:
        """
        Create action generation prompt.
        
        Args:
            section: Section information
            concepts: Concepts to include
            context: Context information
            
        Returns:
            str: Generation prompt
        """
        purpose = section.get("purpose", "")
        content_type = section.get("content_type", "")
        query = context.get("query", "")
        
        # Start with section text if available
        section_text = section.get("text", "")
        if section_text:
            prompt = f"Generate {content_type} for: {section_text}"
        else:
            prompt = f"Generate {content_type} that {purpose} for the query: {query}"
        
        # Add concepts
        if concepts:
            prompt += f"\nIncorporate these key elements: {', '.join(concepts[:5])}"
        
        # Add specific guidance based on content type
        if content_type == "command":
            prompt += "\nGenerate executable command-line commands."
        elif content_type == "api_call":
            prompt += "\nGenerate API call examples with parameters."
        elif content_type == "function":
            prompt += "\nCreate a functional code snippet that can be executed."
        elif content_type == "query":
            prompt += "\nGenerate query examples for data retrieval."
        elif content_type == "workflow":
            prompt += "\nCreate a step-by-step workflow that can be followed."
        
        return prompt
    
    def _calculate_importance(self, section: Dict[str, Any]) -> float:
        """
        Calculate importance score for a section.
        
        Args:
            section: Section information
            
        Returns:
            float: Importance score (0.0-1.0)
        """
        # Base importance by role
        role = section.get("role", "")
        role_scores = {
            "introduction": 0.9,
            "conclusion": 0.8,
            "main_content": 0.9,
            "explanation": 0.8,
            "supporting": 0.7,
            "context": 0.6,
            "integration": 0.7
        }
        
        # Default importance by position
        position = section.get("position", 0)
        if isinstance(position, int):
            position_score = max(0.9 - position * 0.1, 0.3)
        else:
            position_score = 0.5
        
        # Combine scores
        base_score = role_scores.get(role, position_score)
        
        # Adjust based on purpose
        purpose = section.get("purpose", "")
        purpose_adjustments = {
            "provide direct answer": 0.2,
            "frame the response": 0.1,
            "provide core information": 0.2,
            "explain main content": 0.1,
            "provide additional perspective": 0.0,
            "summarize key points": 0.1,
            "integrate perspectives": 0.1
        }
        
        adjustment = purpose_adjustments.get(purpose, 0.0)
        
        # Calculate final score
        importance = min(max(base_score + adjustment, 0.1), 1.0)
        
        return importance
    
    @log_execution_time
    def _align_outputs(
        self,
        modality_outputs: Dict[str, List[ModalityOutputUnit]],
        content_plan: ContentPlan
    ) -> Dict[str, List[ModalityOutputUnit]]:
        """
        Align outputs across modalities.
        
        Args:
            modality_outputs: Outputs grouped by modality
            content_plan: Content plan
            
        Returns:
            Dict[str, List[ModalityOutputUnit]]: Aligned outputs
        """
        # Skip alignment if not enough modalities
        if len(modality_outputs) < 2:
            return modality_outputs
        
        # Apply cross-modal attention for alignment
        if self.config.cross_modal_attention:
            aligned_outputs = self.cross_modal_attention.align_content(modality_outputs)
        else:
            aligned_outputs = modality_outputs
        
        # Ensure complementary content if enabled
        if self.config.complementary_modalities:
            aligned_outputs = self._ensure_complementary_content(aligned_outputs, content_plan)
        
        # Apply consistency checking if enabled
        if self.config.consistency_checking:
            aligned_outputs = self._check_consistency(aligned_outputs)
        
        return aligned_outputs
    
    def _ensure_complementary_content(
        self,
        modality_outputs: Dict[str, List[ModalityOutputUnit]],
        content_plan: ContentPlan
    ) -> Dict[str, List[ModalityOutputUnit]]:
        """
        Ensure content across modalities is complementary.
        
        Args:
            modality_outputs: Outputs grouped by modality
            content_plan: Content plan
            
        Returns:
            Dict[str, List[ModalityOutputUnit]]: Outputs with complementary content
        """
        # Create a copy of outputs
        complementary_outputs = {modality: outputs.copy() for modality, outputs in modality_outputs.items()}
        
        # Check sections covered by modalities
        section_coverage = defaultdict(list)
        for modality, outputs in modality_outputs.items():
            for output in outputs:
                section_coverage[output.section_id].append(modality)
        
        # For each section, ensure complementary coverage
        for section_id, modalities in section_coverage.items():
            # Skip if only one modality or already has multiple modalities
            if len(modalities) < 2:
                continue
                
            # Get outputs for this section
            section_outputs = {}
            for modality in modalities:
                section_outputs[modality] = [o for o in modality_outputs[modality] 
                                           if o.section_id == section_id]
            
            # Check for complementary content between modalities
            if "visual" in section_outputs and "text" in section_outputs:
                visual_outputs = section_outputs["visual"]
                text_outputs = section_outputs["text"]
                
                # Enhance metadata to indicate complementary relationship
                for visual in visual_outputs:
                    if "complements" not in visual.metadata:
                        visual.metadata["complements"] = []
                    
                    # Find best matching text
                    best_match = None
                    best_score = 0.0
                    
                    for text in text_outputs:
                        # Check if already complementary
                        if text.id in [c.get("id") for c in visual.metadata.get("complements", [])]:
                            continue
                        
                        # Compute similarity if features available
                        if visual.features is not None and text.features is not None:
                            score = self._compute_similarity(visual.features, text.features)
                        else:
                            # Basic content-based matching
                            text_content = str(text.content).lower()
                            visual_content = str(visual.metadata.get("description", "")).lower()
                            
                            # Count matching terms
                            matching_terms = sum(1 for term in visual_content.split() 
                                              if term in text_content)
                            score = matching_terms / max(1, len(visual_content.split()))
                        
                        if score > best_score:
                            best_score = score
                            best_match = text
                    
                    # Add complementary relationship
                    if best_match and best_score > 0.3:
                        visual.metadata["complements"].append({
                            "id": best_match.id,
                            "modality": "text",
                            "score": best_score
                        })
                        
                        if "complements" not in best_match.metadata:
                            best_match.metadata["complements"] = []
                            
                        best_match.metadata["complements"].append({
                            "id": visual.id,
                            "modality": "visual",
                            "score": best_score
                        })
        
        return complementary_outputs
    
    def _check_consistency(
        self,
        modality_outputs: Dict[str, List[ModalityOutputUnit]]
    ) -> Dict[str, List[ModalityOutputUnit]]:
        """
        Check consistency across modality outputs.
        
        Args:
            modality_outputs: Outputs grouped by modality
            
        Returns:
            Dict[str, List[ModalityOutputUnit]]: Outputs with consistency checks
        """
        # Create a copy of outputs
        consistent_outputs = {modality: outputs.copy() for modality, outputs in modality_outputs.items()}
        
        # Check consistency between text and visual outputs
        if "text" in consistent_outputs and "visual" in consistent_outputs:
            text_outputs = consistent_outputs["text"]
            visual_outputs = consistent_outputs["visual"]
            
            for visual in visual_outputs:
                # Get metadata description or title
                visual_description = visual.metadata.get("description", "")
                visual_title = visual.metadata.get("title", "")
                visual_content = visual_description or visual_title
                
                if not visual_content:
                    continue
                
                # Check consistency with text outputs
                for text in text_outputs:
                    text_content = str(text.content)
                    
                    # Simple consistency check - look for contradictions
                    visual_terms = set(visual_content.lower().split())
                    text_terms = set(text_content.lower().split())
                    
                    # Calculate Jaccard similarity
                    intersection = len(visual_terms.intersection(text_terms))
                    union = len(visual_terms.union(text_terms))
                    similarity = intersection / max(1, union)
                    
                    # Check if outputs complement each other
                    if text.id in [c.get("id") for c in visual.metadata.get("complements", [])]:
                        # Already complementary - update score
                        for complement in visual.metadata["complements"]:
                            if complement.get("id") == text.id:
                                complement["consistency"] = similarity
                    elif similarity > 0.2:
                        # Add new complementary relationship
                        if "complements" not in visual.metadata:
                            visual.metadata["complements"] = []
                            
                        visual.metadata["complements"].append({
                            "id": text.id,
                            "modality": "text",
                            "consistency": similarity
                        })
                        
                        if "complements" not in text.metadata:
                            text.metadata["complements"] = []
                            
                        text.metadata["complements"].append({
                            "id": visual.id,
                            "modality": "visual",
                            "consistency": similarity
                        })
        
        return consistent_outputs
    
    def _compute_similarity(self, features1: np.ndarray, features2: np.ndarray) -> float:
        """
        Compute similarity between feature vectors.
        
        Args:
            features1: First feature vector
            features2: Second feature vector
            
        Returns:
            float: Similarity score (0.0-1.0)
        """
        # Ensure features are numpy arrays
        if not isinstance(features1, np.ndarray):
            features1 = np.array(features1)
        
        if not isinstance(features2, np.ndarray):
            features2 = np.array(features2)
        
        # Ensure same dimensionality
        min_dim = min(len(features1), len(features2))
        features1 = features1[:min_dim]
        features2 = features2[:min_dim]
        
        # Compute cosine similarity
        norm1 = np.linalg.norm(features1)
        norm2 = np.linalg.norm(features2)
        
        if norm1 > 0 and norm2 > 0:
            similarity = np.dot(features1, features2) / (norm1 * norm2)
            return float(similarity)
        else:
            return 0.0
    
    @log_execution_time
    def _compute_layout(
        self,
        aligned_outputs: Dict[str, List[ModalityOutputUnit]],
        content_plan: ContentPlan
    ) -> Tuple[List[SectionUnit], List[ModalityOutputUnit]]:
        """
        Compute layout for multimodal content.
        
        Args:
            aligned_outputs: Outputs grouped by modality
            content_plan: Content plan
            
        Returns:
            Tuple[List[SectionUnit], List[ModalityOutputUnit]]: 
                Sections and positioned outputs
        """
        # Create sections
        sections = []
        for i, section_data in enumerate(content_plan.sections):
            section_id = section_data.get("section_id", f"section-{i+1}")
            title = section_data.get("title", f"Section {i+1}")
            purpose = section_data.get("purpose", "")
            
            section = SectionUnit(
                section_id=section_id,
                title=title,
                purpose=purpose,
                position=i,
                metadata=section_data
            )
            
            sections.append(section)
        
        # Add outputs to sections
        for modality, outputs in aligned_outputs.items():
            for output in outputs:
                section_id = output.section_id
                
                # Find matching section
                matching_section = next((s for s in sections if s.section_id == section_id), None)
                
                if matching_section:
                    matching_section.add_output(output)
                else:
                    # Create supplementary section if needed
                    if section_id == "supplementary":
                        supplementary_section = next(
                            (s for s in sections if s.section_id == "supplementary"), 
                            None
                        )
                        
                        if not supplementary_section:
                            supplementary_section = SectionUnit(
                                section_id="supplementary",
                                title="Additional Information",
                                purpose="provide additional information",
                                position=len(sections),
                                metadata={"role": "supporting"}
                            )
                            sections.append(supplementary_section)
                        
                        supplementary_section.add_output(output)
                    elif section_id == "fallback":
                        fallback_section = next(
                            (s for s in sections if s.section_id == "fallback"), 
                            None
                        )
                        
                        if not fallback_section:
                            fallback_section = SectionUnit(
                                section_id="fallback",
                                title="Response",
                                purpose="provide answer",
                                position=0,
                                metadata={"role": "main"}
                            )
                            # Insert at beginning
                            sections.insert(0, fallback_section)
                        
                        fallback_section.add_output(output)
        
        # Collect all outputs for layout
        all_outputs = []
        for section in sections:
            all_outputs.extend(section.modality_outputs)
        
        # Compute layout
        positioned_outputs = self.layout_engine.compute_layout(all_outputs, sections)
        
        # Update outputs in sections
        for section in sections:
            section.modality_outputs = []
        
        for output in positioned_outputs:
            section_id = output.section_id
            matching_section = next((s for s in sections if s.section_id == section_id), None)
            if matching_section:
                matching_section.add_output(output)
        
        # Sort sections by position
        sections.sort(key=lambda s: s.position)
        
        return sections, positioned_outputs
    
    @log_execution_time
    def _prepare_response(
        self,
        positioned_outputs: List[ModalityOutputUnit],
        sections: List[SectionUnit],
        content_plan: ContentPlan,
        reasoning_trace: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Prepare final multimodal response.
        
        Args:
            positioned_outputs: Positioned outputs
            sections: Content sections
            content_plan: Content plan
            reasoning_trace: Optional reasoning trace
            
        Returns:
            Dict[str, Any]: Multimodal response
        """
        # Prepare response structure
        response = {
            "query": content_plan.query,
            "sections": [section.to_dict() for section in sections],
            "outputs": [output.to_dict() for output in positioned_outputs],
            "modalities": content_plan.modalities,
            "layout": {
                "width": self.layout_engine.max_width,
                "height": max((output.position[1] + output.size[1] for output in positioned_outputs), 
                             default=self.layout_engine.max_height)
            }
        }
        
        # Add reasoning trace if available
        if reasoning_trace and self.reasoning_formatter:
            trace_text = self.reasoning_formatter.format_trace(
                reasoning_trace,
                output_modality=content_plan.primary_modality
            )
            
            if trace_text:
                response["reasoning_trace"] = trace_text
        
        # Add metadata
        if self.config.include_metadata:
            response["metadata"] = {
                "generated_at": datetime.now().isoformat(),
                "content_plan_id": content_plan.id,
                "primary_modality": content_plan.primary_modality,
                "modality_counts": {modality: len(outputs) for modality, outputs in 
                                  {m: [o for o in positioned_outputs if o.modality == m] 
                                  for m in content_plan.modalities}.items()},
                "generation_config": {
                    "layout_algorithm": self.layout_engine.algorithm,
                    "cross_modal_attention": self.config.cross_modal_attention,
                    "content_planning": self.config.content_planning,
                    "semantic_segmentation": self.config.use_semantic_segmentation
                }
            }
        
        return response
    
    def _generate_fallback_response(
        self,
        query: str,
        context: Dict[str, Any],
        reasoning_trace: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Generate fallback response when regular generation fails.
        
        Args:
            query: User query
            context: Context information
            reasoning_trace: Optional reasoning trace
            
        Returns:
            Dict[str, Any]: Fallback response
        """
        # Use primary modality for fallback if available
        primary_modality = self.config.primary_modality
        generator = self.generators.get(primary_modality)
        
        if not generator:
            # Find any available generator
            for modality, gen in self.generators.items():
                primary_modality = modality
                generator = gen
                break
        
        if not generator:
            # No generators available - return error
            return {
                "query": query,
                "error": "No modality generators available",
                "fallback_text": "Unable to generate response. No generators available."
            }
        
        try:
            # Generate simple response
            prompt = f"Answer the following question: {query}"
            
            content = generator.generate(prompt, context)
            
            # Create fallback section
            fallback_section = SectionUnit(
                section_id="fallback",
                title="Response",
                purpose="provide answer",
                position=0,
                metadata={"role": "main"}
            )
            
            # Create output unit
            output = ModalityOutputUnit(
                modality=primary_modality,
                content=content,
                content_type="explanation",
                metadata={
                    "purpose": "fallback",
                    "generated_from": "fallback"
                },
                position=(50, 50),
                size=self.layout_engine._get_default_size(ModalityOutputUnit(
                    modality=primary_modality,
                    content=content,
                    content_type="explanation"
                )),
                confidence=0.5,
                importance=1.0,
                section_id="fallback"
            )
            
            fallback_section.add_output(output)
            
            # Prepare response
            response = {
                "query": query,
                "fallback": True,
                "sections": [fallback_section.to_dict()],
                "outputs": [output.to_dict()],
                "modalities": [primary_modality],
                "layout": {
                    "width": self.layout_engine.max_width,
                    "height": output.position[1] + output.size[1]
                }
            }
            
            # Add reasoning trace if available
            if reasoning_trace and self.reasoning_formatter:
                trace_text = self.reasoning_formatter.format_trace(
                    reasoning_trace,
                    output_modality=primary_modality
                )
                
                if trace_text:
                    response["reasoning_trace"] = trace_text
            
            return response
            
        except Exception as e:
            logger.error(f"Error generating fallback response: {str(e)}", exc_info=True)
            
            # Return minimal error response
            return {
                "query": query,
                "error": str(e),
                "fallback_text": "An error occurred while generating the response."
            }
    
    def update_context(
        self,
        context: Dict[str, Any],
        response: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Update context with response information for subsequent calls.
        
        Args:
            context: Original context
            response: Generated response
            
        Returns:
            Dict[str, Any]: Updated context
        """
        # Create a copy of context
        updated_context = context.copy()
        
        # Add previous modalities
        previous_modalities = response.get("modalities", [])
        if "previous_modalities" in updated_context:
            updated_context["previous_modalities"].extend(previous_modalities)
        else:
            updated_context["previous_modalities"] = previous_modalities
        
        # Limit history size
        if "previous_modalities" in updated_context:
            updated_context["previous_modalities"] = updated_context["previous_modalities"][-5:]
        
        # Add section information
        sections = response.get("sections", [])
        if sections:
            updated_context["previous_sections"] = [
                {
                    "section_id": s.get("section_id"),
                    "title": s.get("title"),
                    "modality_outputs": len(s.get("modality_outputs", []))
                }
                for s in sections
            ]
        
        return updated_context
    
    def get_metrics(self) -> Dict[str, float]:
        """
        Get generation metrics.
        
        Returns:
            Dict[str, float]: Metrics
        """
        # Calculate averages
        metrics = {}
        for key, value in self.metrics.items():
            count = self.metrics_count.get(key, 1)
            metrics[key] = value / count
        
        return metrics
    
    def reset_metrics(self) -> None:
        """Reset generation metrics."""
        self.metrics = defaultdict(float)
        self.metrics_count = defaultdict(int)
    
    def save(self, directory: str) -> None:
        """
        Save synthesizer state and configuration.
        
        Args:
            directory: Directory to save to
        """
        os.makedirs(directory, exist_ok=True)
        
        # Save configuration
        config_path = os.path.join(directory, "config.json")
        self.config.save(config_path)
        
        # Save metrics
        metrics_path = os.path.join(directory, "metrics.json")
        with open(metrics_path, "w") as f:
            json.dump(self.get_metrics(), f, indent=2)
        
        logger.info(f"Saved MultiModalSynthesizer to {directory}")
    
    @classmethod
    def load(
        cls,
        directory: str,
        text_generator: Optional['TextGenerator'] = None,
        visual_generator: Optional['VisualGenerator'] = None,
        action_generator: Optional['ActionGenerator'] = None
    ) -> 'MultiModalSynthesizer':
        """
        Load synthesizer from saved state.
        
        Args:
            directory: Directory to load from
            text_generator: Text generator
            visual_generator: Visual generator
            action_generator: Action generator
            
        Returns:
            MultiModalSynthesizer: Loaded synthesizer
        """
        # Load configuration
        config_path = os.path.join(directory, "config.json")
        config = MultiModalSynthesizerConfig.load(config_path)
        
        # Create instance
        synthesizer = cls(
            config=config,
            text_generator=text_generator,
            visual_generator=visual_generator,
            action_generator=action_generator
        )
        
        logger.info(f"Loaded MultiModalSynthesizer from {directory}")
        return synthesizer


# Factory functions
def create_multimodal_synthesizer(
    config: Optional[Dict[str, Any]] = None,
    text_generator: Optional['TextGenerator'] = None,
    visual_generator: Optional['VisualGenerator'] = None,
    action_generator: Optional['ActionGenerator'] = None
) -> MultiModalSynthesizer:
    """
    Create a multimodal synthesizer.
    
    Args:
        config: Configuration dictionary
        text_generator: Text generator
        visual_generator: Visual generator
        action_generator: Action generator
        
    Returns:
        MultiModalSynthesizer: Initialized synthesizer
    """
    # Create config
    if config is None:
        config = {}
    
    # Create configuration
    mm_config = MultiModalSynthesizerConfig(**config)
    
    # Create synthesizer
    synthesizer = MultiModalSynthesizer(
        config=mm_config,
        text_generator=text_generator,
        visual_generator=visual_generator,
        action_generator=action_generator
    )
    
    return synthesizer


# Module exports
__all__ = [
    'MultiModalSynthesizerConfig',
    'MultiModalSynthesizer',
    'ContentPlan',
    'SectionUnit',
    'ModalityOutputUnit',
    'ContentPlanner',
    'SemanticContentSegmenter',
    'LayoutEngine',
    'CrossModalAttention',
    'create_multimodal_synthesizer'
]