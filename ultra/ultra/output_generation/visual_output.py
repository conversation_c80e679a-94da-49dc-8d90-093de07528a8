#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ULTRA Visual Generation Module

This module implements the VisualGenerator class for the ULTRA (Ultimate Learning
& Thought Reasoning Architecture) system, enabling the transformation of internal
representations into various types of visual outputs including charts, diagrams,
graphs, and structured information displays.

The module integrates with modern visualization libraries and implements several
novel algorithms for automatically selecting optimal visualization types,
organizing complex data relationships, and creating visually coherent outputs
that align with the system's reasoning processes.

Key features:
- Automatic visualization type selection based on data characteristics
- Dynamic layout algorithms for complex information displays
- Integration with reasoning traces for explainable visualizations
- Support for multiple output formats (PNG, SVG, PDF, etc.)
- Hierarchical information visualization techniques
- Style consistency across visualization types
- Visual highlighting of key insights and relationships
"""

import os
import io
import math
import re
import json
import base64
import hashlib
import logging
import warnings
from typing import Dict, List, Tuple, Union, Optional, Any, Callable, Set
from copy import deepcopy
from collections import Counter, defaultdict

import numpy as np
import torch

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(name)s: %(message)s'
)
logger = logging.getLogger(__name__)

# Try to import visualization libraries
try:
    import matplotlib
    import matplotlib.pyplot as plt
    from matplotlib.figure import Figure
    from matplotlib.axes import Axes
    from matplotlib.colors import LinearSegmentedColormap, to_rgba
    from mpl_toolkits.mplot3d import Axes3D
    matplotlib.use('Agg')  # Use non-interactive backend
    _HAS_MATPLOTLIB = True
except ImportError:
    logger.warning("Matplotlib not found. Most visualization capabilities will be limited.")
    _HAS_MATPLOTLIB = False

try:
    from PIL import Image, ImageDraw, ImageFont
    _HAS_PIL = True
except ImportError:
    logger.warning("PIL not found. Image generation capabilities will be limited.")
    _HAS_PIL = False

try:
    import networkx as nx
    _HAS_NETWORKX = True
except ImportError:
    logger.warning("NetworkX not found. Graph visualization capabilities will be limited.")
    _HAS_NETWORKX = False

try:
    import svgwrite
    from svgwrite import Drawing
    from svgwrite.container import Group
    from svgwrite.shapes import Rect, Circle, Line, Polygon
    from svgwrite.text import Text
    from svgwrite.path import Path
    _HAS_SVGWRITE = True
except ImportError:
    logger.warning("svgwrite not found. SVG generation capabilities will be limited.")
    _HAS_SVGWRITE = False

try:
    import pandas as pd
    _HAS_PANDAS = True
except ImportError:
    logger.warning("Pandas not found. Data processing capabilities will be limited.")
    _HAS_PANDAS = False

try:
    import seaborn as sns
    _HAS_SEABORN = True
except ImportError:
    logger.warning("Seaborn not found. Advanced statistical visualization will be limited.")
    _HAS_SEABORN = False

try:
    import plotly.graph_objects as go
    import plotly.express as px
    from plotly.subplots import make_subplots
    _HAS_PLOTLY = True
except ImportError:
    logger.warning("Plotly not found. Interactive visualization capabilities will be limited.")
    _HAS_PLOTLY = False

# Import from parent module if possible
try:
    from ..output_generation import OutputGenerator, OutputGeneratorConfig
except (ImportError, ValueError):
    # For standalone or testing purposes, create placeholders
    class OutputGeneratorConfig:
        def __init__(self, **kwargs):
            for key, value in kwargs.items():
                setattr(self, key, value)
                
    class OutputGenerator:
        def __init__(self, config):
            self.config = config


class VisualGeneratorConfig(OutputGeneratorConfig):
    """
    Configuration for the VisualGenerator.
    
    This class extends the base OutputGeneratorConfig with visual-specific parameters
    for controlling visualization behavior, style, and output formats.
    """
    
    def __init__(self, **kwargs):
        # Visual-specific defaults
        visual_defaults = {
            # General visualization settings
            "visualization_types": ["chart", "diagram", "graph", "image"],  # Types of visualizations
            "default_width": 800,               # Default width in pixels
            "default_height": 600,              # Default height in pixels
            "dpi": 100,                         # DPI for raster images
            "format": "png",                    # Default output format
            "vector_format": "svg",             # Default vector format
            "auto_format": True,                # Whether to automatically select format
            
            # Style settings
            "color_scheme": "default",          # Color scheme
            "color_map": "viridis",             # Default color map for continuous values
            "qualitative_palette": "tab10",     # Default palette for categorical data
            "sequential_palette": "Blues",      # Default palette for sequential data
            "diverging_palette": "RdBu_r",      # Default palette for diverging data
            "style": "modern",                  # Visual style
            "high_contrast": False,             # Whether to use high contrast
            "dark_mode": False,                 # Whether to use dark mode
            
            # Font settings
            "font_family": "sans-serif",        # Default font family
            "title_font_size": 16,              # Title font size
            "label_font_size": 12,              # Label font size
            "tick_font_size": 10,               # Tick font size
            "annotation_font_size": 11,         # Annotation font size
            "legend_font_size": 10,             # Legend font size
            
            # Layout settings
            "fig_title_loc": "center",          # Figure title location
            "title_pad": 15,                    # Padding around title
            "tight_layout": True,               # Whether to use tight layout
            "show_grid": True,                  # Whether to show grid lines
            "grid_alpha": 0.3,                  # Grid transparency
            "auto_layout": True,                # Whether to use automatic layout
            "aspect_ratio": "auto",             # Aspect ratio (auto, equal, or float)
            
            # Content settings
            "include_annotations": True,        # Whether to include annotations
            "annotations_max": 10,              # Maximum number of annotations
            "show_data_labels": True,           # Whether to show data labels
            "data_label_threshold": 0.05,       # Threshold for showing data labels
            "max_elements": 100,                # Maximum number of elements in visualization
            "include_text_labels": True,        # Whether to include text labels
            "label_wrap_length": 20,            # Character length for label wrapping
            
            # Legend settings
            "show_legend": True,                # Whether to show legend
            "legend_loc": "best",               # Legend location
            "outside_legend": False,            # Whether to place legend outside plot
            "legend_columns": 1,                # Number of legend columns
            
            # Chart-specific settings
            "bar_orientation": "vertical",      # Bar chart orientation
            "stacked_bars": False,              # Whether to stack bars
            "grouped_bars": False,              # Whether to group bars
            "normalize_bars": False,            # Whether to normalize bar values
            "pie_start_angle": 90,              # Starting angle for pie charts
            "donut_ratio": 0.4,                 # Inner radius ratio for donut charts
            "line_style": "-",                  # Line style
            "line_width": 2.0,                  # Line width
            "marker_size": 6,                   # Marker size
            "marker_edge_width": 0.8,           # Marker edge width
            "scatter_alpha": 0.7,               # Scatter plot alpha
            
            # Graph-specific settings
            "node_size": 500,                   # Node size in graphs
            "edge_width": 1.0,                  # Edge width in graphs
            "directed_graphs": False,           # Whether graphs are directed
            "graph_layout": "spring",           # Graph layout algorithm
            "graph_k": None,                    # k parameter for spring layout
            "hierarchical_layout": False,       # Whether to use hierarchical layout
            
            # Interactive settings
            "interactive": False,               # Whether to generate interactive visualizations
            "include_tooltips": True,           # Whether to include tooltips
            "include_zoom": True,               # Whether to include zoom controls
            "include_pan": True,                # Whether to include pan controls
            "include_download": True,           # Whether to include download button
            
            # Optimization settings
            "optimization_level": "medium",     # Level of optimization
            "simplify_plots": False,            # Whether to simplify plots
            "vector_simplification": 0.1,       # Vector simplification tolerance
            "max_ticks": 10,                    # Maximum number of ticks
            "memory_limit": 128,                # Memory limit in MB
            
            # Accessibility settings
            "accessibility_features": True,     # Whether to include accessibility features
            "accessible_colors": False,         # Whether to use colorblind-friendly colors
            "include_alt_text": True,           # Whether to include alt text
            "include_title_text": True,         # Whether to include title text
            "include_description": True,        # Whether to include description
            
            # Advanced settings
            "use_gpu_acceleration": True,       # Whether to use GPU acceleration
            "dynamic_resolution": True,         # Whether to adapt resolution dynamically
            "embed_metadata": True,             # Whether to embed metadata
            "custom_js": "",                    # Custom JavaScript for interactive vis
            "custom_css": "",                   # Custom CSS for styling
            "base64_encode": True,              # Whether to Base64 encode output
        }
        
        # Update kwargs with defaults for any missing values
        for key, value in visual_defaults.items():
            if key not in kwargs:
                kwargs[key] = value
                
        # Initialize parent class
        super().__init__(**kwargs)
    
    def _validate(self):
        """Validate visual-specific configuration parameters."""
        super()._validate()
        
        # Validate dimensions
        if self.default_width <= 0:
            raise ValueError(f"default_width must be positive")
        
        if self.default_height <= 0:
            raise ValueError(f"default_height must be positive")
        
        if self.dpi <= 0:
            raise ValueError(f"dpi must be positive")
        
        # Validate format
        valid_formats = ["png", "jpg", "jpeg", "svg", "pdf", "webp", "gif"]
        if self.format not in valid_formats:
            raise ValueError(f"format must be one of {valid_formats}")
        
        # Validate vector format
        valid_vector_formats = ["svg", "pdf", "eps"]
        if self.vector_format not in valid_vector_formats:
            raise ValueError(f"vector_format must be one of {valid_vector_formats}")
        
        # Validate visualization types
        valid_types = ["chart", "diagram", "graph", "image", "table", "map", "plot"]
        for vis_type in self.visualization_types:
            if vis_type not in valid_types:
                raise ValueError(f"visualization_type must be one of {valid_types}")
        
        # Validate style
        valid_styles = ["modern", "classic", "minimal", "dark", "light", "colorful"]
        if self.style not in valid_styles:
            raise ValueError(f"style must be one of {valid_styles}")
        
        # Validate optimization level
        valid_levels = ["low", "medium", "high"]
        if self.optimization_level not in valid_levels:
            raise ValueError(f"optimization_level must be one of {valid_levels}")
        
        # Validate font settings
        if self.title_font_size <= 0:
            raise ValueError(f"title_font_size must be positive")
        
        if self.label_font_size <= 0:
            raise ValueError(f"label_font_size must be positive")
        
        # Validate data limits
        if self.max_elements <= 0:
            raise ValueError(f"max_elements must be positive")
        
        # Validate graph layout
        valid_layouts = ["spring", "circular", "kamada_kawai", "planar", "random", 
                         "spectral", "shell", "bipartite", "dot", "neato", "fdp", "twopi"]
        if self.graph_layout not in valid_layouts:
            raise ValueError(f"graph_layout must be one of {valid_layouts}")


class ColorManager:
    """
    Class to manage color schemes and palette generation.
    
    This class provides color management capabilities including palette generation,
    color mapping, accessibility-focused color schemes, and style-based defaults.
    """
    
    def __init__(
        self, 
        color_scheme: str = "default",
        color_map: str = "viridis",
        qualitative_palette: str = "tab10",
        sequential_palette: str = "Blues",
        diverging_palette: str = "RdBu_r",
        accessible_colors: bool = False,
        dark_mode: bool = False,
        high_contrast: bool = False
    ):
        """
        Initialize the color manager.
        
        Args:
            color_scheme: Base color scheme
            color_map: Default colormap for continuous data
            qualitative_palette: Default palette for categorical data
            sequential_palette: Default palette for sequential data
            diverging_palette: Default palette for diverging data
            accessible_colors: Whether to use colorblind-friendly colors
            dark_mode: Whether to use dark mode
            high_contrast: Whether to use high contrast
        """
        self.color_scheme = color_scheme
        self.color_map_name = color_map
        self.qualitative_palette = qualitative_palette
        self.sequential_palette = sequential_palette
        self.diverging_palette = diverging_palette
        self.accessible_colors = accessible_colors
        self.dark_mode = dark_mode
        self.high_contrast = high_contrast
        
        # Define base color schemes
        self.base_schemes = {
            "default": {
                "primary": "#4e79a7",
                "secondary": "#f28e2c", 
                "tertiary": "#e15759",
                "quaternary": "#76b7b2",
                "quinary": "#59a14f",
                "senary": "#edc949",
                "septenary": "#af7aa1",
                "octonary": "#ff9da7",
                "background": "#ffffff",
                "text": "#333333",
                "grid": "#cccccc",
                "accent": "#1f77b4"
            },
            "modern": {
                "primary": "#3498db",
                "secondary": "#2ecc71", 
                "tertiary": "#e74c3c",
                "quaternary": "#9b59b6",
                "quinary": "#1abc9c",
                "senary": "#f1c40f",
                "septenary": "#34495e",
                "octonary": "#e67e22",
                "background": "#ffffff",
                "text": "#2c3e50",
                "grid": "#ecf0f1",
                "accent": "#3498db"
            },
            "minimal": {
                "primary": "#555555",
                "secondary": "#999999", 
                "tertiary": "#cccccc",
                "quaternary": "#777777",
                "quinary": "#aaaaaa",
                "senary": "#333333",
                "septenary": "#888888",
                "octonary": "#dddddd",
                "background": "#ffffff",
                "text": "#333333",
                "grid": "#eeeeee",
                "accent": "#000000"
            },
            "classic": {
                "primary": "#1f77b4",
                "secondary": "#ff7f0e", 
                "tertiary": "#2ca02c",
                "quaternary": "#d62728",
                "quinary": "#9467bd",
                "senary": "#8c564b",
                "septenary": "#e377c2",
                "octonary": "#7f7f7f",
                "background": "#ffffff",
                "text": "#000000",
                "grid": "#cccccc",
                "accent": "#1f77b4"
            },
            "dark": {
                "primary": "#61afef",
                "secondary": "#c678dd", 
                "tertiary": "#e06c75",
                "quaternary": "#98c379",
                "quinary": "#e5c07b",
                "senary": "#56b6c2",
                "septenary": "#abb2bf",
                "octonary": "#528bff",
                "background": "#282c34",
                "text": "#abb2bf",
                "grid": "#3e4451",
                "accent": "#61afef"
            },
            "colorful": {
                "primary": "#ff595e",
                "secondary": "#ffca3a", 
                "tertiary": "#8ac926",
                "quaternary": "#1982c4",
                "quinary": "#6a4c93",
                "senary": "#ff924c",
                "septenary": "#ff7477",
                "octonary": "#3be8b0",
                "background": "#ffffff",
                "text": "#1d3557",
                "grid": "#f1faee",
                "accent": "#e63946"
            }
        }
        
        # Define colorblind-friendly palettes
        self.accessible_palettes = {
            "qualitative": ["#377eb8", "#ff7f00", "#4daf4a", "#e41a1c", "#984ea3", 
                           "#a65628", "#f781bf", "#999999", "#66c2a5", "#fc8d62"],
            "sequential": ["#fef0d9", "#fdcc8a", "#fc8d59", "#e34a33", "#b30000"],
            "diverging": ["#2166ac", "#67a9cf", "#f7f7f7", "#ef8a62", "#b2182b"]
        }
        
        # Initialize current scheme
        self._init_current_scheme()
    
    def _init_current_scheme(self):
        """Initialize the current color scheme based on settings."""
        # Start with base scheme
        scheme_name = self.color_scheme
        if scheme_name not in self.base_schemes:
            scheme_name = "default"
            
        self.scheme = deepcopy(self.base_schemes[scheme_name])
        
        # Apply dark mode
        if self.dark_mode and scheme_name != "dark":
            self._apply_dark_mode()
            
        # Apply high contrast
        if self.high_contrast:
            self._apply_high_contrast()
            
        # Apply accessible colors
        if self.accessible_colors:
            self._apply_accessible_colors()
    
    def _apply_dark_mode(self):
        """Apply dark mode to the current scheme."""
        # Invert background and text colors
        self.scheme["background"] = "#282c34"
        self.scheme["text"] = "#abb2bf"
        self.scheme["grid"] = "#3e4451"
        
        # Brighten colors
        for key in ["primary", "secondary", "tertiary", "quaternary", 
                   "quinary", "senary", "septenary", "octonary", "accent"]:
            # Lighten color without full conversion
            color = self.scheme[key]
            r, g, b = self._hex_to_rgb(color)
            r = min(255, int(r * 1.2))
            g = min(255, int(g * 1.2))
            b = min(255, int(b * 1.2))
            self.scheme[key] = self._rgb_to_hex(r, g, b)
    
    def _apply_high_contrast(self):
        """Apply high contrast to the current scheme."""
        # Increase text contrast
        self.scheme["text"] = "#000000" if not self.dark_mode else "#ffffff"
        
        # Make grid more visible
        self.scheme["grid"] = "#aaaaaa" if not self.dark_mode else "#555555"
        
        # Increase contrast for main colors
        for key in ["primary", "secondary", "tertiary", "quaternary", 
                   "quinary", "senary", "septenary", "octonary"]:
            color = self.scheme[key]
            r, g, b = self._hex_to_rgb(color)
            # Increase contrast by pushing colors away from middle gray
            for i, c in enumerate([r, g, b]):
                if c > 128:
                    c = min(255, c + 30)
                else:
                    c = max(0, c - 30)
                if i == 0: r = c
                elif i == 1: g = c
                else: b = c
            self.scheme[key] = self._rgb_to_hex(r, g, b)
    
    def _apply_accessible_colors(self):
        """Apply colorblind-friendly colors to the current scheme."""
        # Replace primary palette with accessible palette
        for i, key in enumerate(["primary", "secondary", "tertiary", "quaternary", 
                                "quinary", "senary", "septenary", "octonary"]):
            if i < len(self.accessible_palettes["qualitative"]):
                self.scheme[key] = self.accessible_palettes["qualitative"][i]
    
    def _hex_to_rgb(self, hex_color: str) -> Tuple[int, int, int]:
        """Convert hex color to RGB."""
        hex_color = hex_color.lstrip('#')
        return tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))
    
    def _rgb_to_hex(self, r: int, g: int, b: int) -> str:
        """Convert RGB to hex color."""
        return f"#{r:02x}{g:02x}{b:02x}"
    
    def get_color(self, name: str) -> str:
        """
        Get a color by name from the current scheme.
        
        Args:
            name: Color name
            
        Returns:
            str: Hex color code
        """
        return self.scheme.get(name, self.scheme.get("primary"))
    
    def get_categorical_palette(self, n_categories: int) -> List[str]:
        """
        Get a palette for categorical data.
        
        Args:
            n_categories: Number of categories
            
        Returns:
            List[str]: List of hex color codes
        """
        # Use accessible palette if configured
        if self.accessible_colors:
            palette = self.accessible_palettes["qualitative"]
            # Cycle through palette if more categories than colors
            return [palette[i % len(palette)] for i in range(n_categories)]
        
        # Use main scheme colors first
        main_colors = [
            self.scheme["primary"],
            self.scheme["secondary"],
            self.scheme["tertiary"],
            self.scheme["quaternary"],
            self.scheme["quinary"],
            self.scheme["senary"],
            self.scheme["septenary"],
            self.scheme["octonary"]
        ]
        
        if n_categories <= len(main_colors):
            return main_colors[:n_categories]
            
        # For more categories, use seaborn or matplotlib palettes
        try:
            if _HAS_SEABORN:
                return list(sns.color_palette(self.qualitative_palette, n_categories).as_hex())
            elif _HAS_MATPLOTLIB:
                cmap = plt.get_cmap(self.qualitative_palette)
                return [matplotlib.colors.rgb2hex(cmap(i / (n_categories - 1))) for i in range(n_categories)]
        except Exception as e:
            logger.warning(f"Error creating categorical palette: {e}")
            
        # Fallback: cycle through main colors
        return [main_colors[i % len(main_colors)] for i in range(n_categories)]
    
    def get_sequential_palette(self, n_steps: int) -> List[str]:
        """
        Get a palette for sequential data.
        
        Args:
            n_steps: Number of steps
            
        Returns:
            List[str]: List of hex color codes
        """
        # Use accessible palette if configured
        if self.accessible_colors:
            palette = self.accessible_palettes["sequential"]
            # Interpolate if needed
            if n_steps <= len(palette):
                return palette[:n_steps]
            else:
                return self._interpolate_palette(palette, n_steps)
                
        # Try to create sequential palette
        try:
            if _HAS_SEABORN:
                return list(sns.color_palette(self.sequential_palette, n_steps).as_hex())
            elif _HAS_MATPLOTLIB:
                cmap = plt.get_cmap(self.sequential_palette)
                return [matplotlib.colors.rgb2hex(cmap(i / (n_steps - 1))) for i in range(n_steps)]
        except Exception as e:
            logger.warning(f"Error creating sequential palette: {e}")
            
        # Fallback: create a gradient from primary color to light/dark
        primary = self._hex_to_rgb(self.scheme["primary"])
        if self.dark_mode:
            target = (255, 255, 255)  # White
        else:
            target = (240, 240, 240)  # Light gray
            
        palette = []
        for i in range(n_steps):
            t = i / (n_steps - 1) if n_steps > 1 else 0
            r = int(primary[0] + (target[0] - primary[0]) * t)
            g = int(primary[1] + (target[1] - primary[1]) * t)
            b = int(primary[2] + (target[2] - primary[2]) * t)
            palette.append(self._rgb_to_hex(r, g, b))
            
        return palette
    
    def get_diverging_palette(self, n_steps: int) -> List[str]:
        """
        Get a palette for diverging data.
        
        Args:
            n_steps: Number of steps
            
        Returns:
            List[str]: List of hex color codes
        """
        # Use accessible palette if configured
        if self.accessible_colors:
            palette = self.accessible_palettes["diverging"]
            # Interpolate if needed
            if n_steps <= len(palette):
                return palette[:n_steps]
            else:
                return self._interpolate_palette(palette, n_steps)
                
        # Try to create diverging palette
        try:
            if _HAS_SEABORN:
                return list(sns.color_palette(self.diverging_palette, n_steps).as_hex())
            elif _HAS_MATPLOTLIB:
                cmap = plt.get_cmap(self.diverging_palette)
                return [matplotlib.colors.rgb2hex(cmap(i / (n_steps - 1))) for i in range(n_steps)]
        except Exception as e:
            logger.warning(f"Error creating diverging palette: {e}")
            
        # Fallback: create a diverging palette from primary and secondary colors
        primary = self._hex_to_rgb(self.scheme["primary"])
        secondary = self._hex_to_rgb(self.scheme["secondary"])
        mid = (240, 240, 240)  # Light gray
        if self.dark_mode:
            mid = (60, 60, 60)  # Dark gray
            
        palette = []
        half_steps = n_steps // 2
        remainder = n_steps % 2
        
        # First half: from secondary to middle
        for i in range(half_steps):
            t = i / (half_steps - 1) if half_steps > 1 else 0
            r = int(secondary[0] + (mid[0] - secondary[0]) * t)
            g = int(secondary[1] + (mid[1] - secondary[1]) * t)
            b = int(secondary[2] + (mid[2] - secondary[2]) * t)
            palette.append(self._rgb_to_hex(r, g, b))
            
        # Middle point if odd number of steps
        if remainder:
            palette.append(self._rgb_to_hex(*mid))
            
        # Second half: from middle to primary
        for i in range(half_steps):
            t = i / (half_steps - 1) if half_steps > 1 else 0
            r = int(mid[0] + (primary[0] - mid[0]) * t)
            g = int(mid[1] + (primary[1] - mid[1]) * t)
            b = int(mid[2] + (primary[2] - mid[2]) * t)
            palette.append(self._rgb_to_hex(r, g, b))
            
        return palette
    
    def get_colormap(self, name: Optional[str] = None) -> Any:
        """
        Get a colormap for continuous data.
        
        Args:
            name: Optional name of colormap
            
        Returns:
            Any: Colormap object
        """
        if name is None:
            name = self.color_map_name
            
        try:
            if _HAS_MATPLOTLIB:
                return plt.get_cmap(name)
        except Exception as e:
            logger.warning(f"Error getting colormap: {e}")
            
        # Fallback to creating a simple colormap
        if _HAS_MATPLOTLIB:
            sequential_colors = self.get_sequential_palette(5)
            return LinearSegmentedColormap.from_list("custom_cmap", sequential_colors)
            
        return None
    
    def _interpolate_palette(self, palette: List[str], n_steps: int) -> List[str]:
        """
        Interpolate a palette to get the desired number of steps.
        
        Args:
            palette: Original palette
            n_steps: Desired number of steps
            
        Returns:
            List[str]: Interpolated palette
        """
        if n_steps <= 1:
            return [palette[0]]
            
        # Convert to RGB
        rgb_palette = [self._hex_to_rgb(color) for color in palette]
        
        # Create a normalized position array for original palette
        old_positions = np.linspace(0, 1, len(rgb_palette))
        
        # Create new positions for desired number of steps
        new_positions = np.linspace(0, 1, n_steps)
        
        # Interpolate each RGB component
        r_interp = np.interp(new_positions, old_positions, [rgb[0] for rgb in rgb_palette])
        g_interp = np.interp(new_positions, old_positions, [rgb[1] for rgb in rgb_palette])
        b_interp = np.interp(new_positions, old_positions, [rgb[2] for rgb in rgb_palette])
        
        # Convert back to hex
        return [self._rgb_to_hex(int(r), int(g), int(b)) 
                for r, g, b in zip(r_interp, g_interp, b_interp)]


class DataAnalyzer:
    """
    Analyzes data to determine appropriate visualization techniques.
    
    This class examines data characteristics to recommend optimal visualization
    types, identify key features, and suggest visual encodings.
    """
    
    def __init__(self, max_categories: int = 10, correlation_threshold: float = 0.7):
        """
        Initialize the data analyzer.
        
        Args:
            max_categories: Maximum number of categories for categorical visualization
            correlation_threshold: Threshold for identifying strong correlations
        """
        self.max_categories = max_categories
        self.correlation_threshold = correlation_threshold
    
    def analyze_data(self, data: Any) -> Dict[str, Any]:
        """
        Analyze data and recommend visualization approaches.
        
        Args:
            data: Input data (various formats supported)
            
        Returns:
            Dict[str, Any]: Analysis results
        """
        # Convert data to a standardized format for analysis
        df, data_type = self._standardize_data(data)
        
        # Perform analysis
        if df is not None:
            return self._analyze_dataframe(df, data_type)
        elif isinstance(data, dict) and "nodes" in data and "edges" in data:
            return self._analyze_graph(data)
        elif isinstance(data, (list, tuple)) and all(isinstance(x, dict) for x in data):
            return self._analyze_nested_json(data)
        else:
            # Basic fallback analysis
            return self._analyze_basic(data)
    
    def _standardize_data(self, data: Any) -> Tuple[Optional[Any], str]:
        """
        Convert data to a standardized format for analysis.
        
        Args:
            data: Input data
            
        Returns:
            Tuple[Optional[Any], str]: Standardized data and data type
        """
        # Check if already a DataFrame
        if _HAS_PANDAS and isinstance(data, pd.DataFrame):
            return data, "dataframe"
            
        # Try to convert from common formats
        if isinstance(data, dict):
            # Check if it's a graph structure
            if "nodes" in data and "edges" in data:
                return None, "graph"
                
            # Try to convert dict to DataFrame
            if _HAS_PANDAS:
                try:
                    return pd.DataFrame.from_dict(data), "dict"
                except:
                    pass
        
        if isinstance(data, (list, tuple)):
            # Check if it's a list of dicts (records)
            if all(isinstance(x, dict) for x in data):
                if _HAS_PANDAS:
                    try:
                        return pd.DataFrame.from_records(data), "records"
                    except:
                        return None, "nested_json"
                else:
                    return None, "nested_json"
                    
            # Check if it's a list of lists (matrix)
            if all(isinstance(x, (list, tuple)) for x in data):
                if _HAS_PANDAS:
                    try:
                        return pd.DataFrame(data), "matrix"
                    except:
                        pass
        
        # Return original data with a generic type
        return None, "unknown"
    
    def _analyze_dataframe(self, df: Any, data_type: str) -> Dict[str, Any]:
        """
        Analyze a pandas DataFrame.
        
        Args:
            df: DataFrame to analyze
            data_type: Original data type
            
        Returns:
            Dict[str, Any]: Analysis results
        """
        results = {
            "data_type": data_type,
            "n_rows": len(df),
            "n_cols": len(df.columns),
            "columns": list(df.columns),
            "dtypes": {},
            "categorical_columns": [],
            "numeric_columns": [],
            "temporal_columns": [],
            "missing_values": {},
            "unique_values": {},
            "recommended_viz": [],
            "viz_options": []
        }
        
        # Analyze column types
        for col in df.columns:
            dtype = str(df[col].dtype)
            results["dtypes"][col] = dtype
            
            # Count missing values
            missing = df[col].isna().sum()
            results["missing_values"][col] = missing
            
            # Count unique values
            n_unique = df[col].nunique()
            results["unique_values"][col] = n_unique
            
            # Categorical vs. numeric classification
            if dtype.startswith(('int', 'float', 'complex')):
                results["numeric_columns"].append(col)
            elif dtype.startswith(('datetime', 'timedelta')):
                results["temporal_columns"].append(col)
            elif n_unique <= self.max_categories:
                results["categorical_columns"].append(col)
            else:
                # For non-numeric columns with many unique values, treat as text
                pass
        
        # Recommend visualizations based on data characteristics
        self._recommend_visualizations(results, df)
        
        return results
    
    def _recommend_visualizations(self, results: Dict[str, Any], df: Any) -> None:
        """
        Recommend visualizations based on DataFrame characteristics.
        
        Args:
            results: Current analysis results
            df: DataFrame being analyzed
        """
        n_rows = results["n_rows"]
        n_cols = results["n_cols"]
        n_cat = len(results["categorical_columns"])
        n_num = len(results["numeric_columns"])
        n_time = len(results["temporal_columns"])
        
        # Single numeric column
        if n_num == 1 and n_cat == 0:
            col = results["numeric_columns"][0]
            results["recommended_viz"].append(("histogram", [col]))
            results["viz_options"].append(("density_plot", [col]))
            results["viz_options"].append(("boxplot", [col]))
            
        # Single categorical column
        if n_cat == 1 and n_num == 0:
            col = results["categorical_columns"][0]
            results["recommended_viz"].append(("bar_chart", [col]))
            results["viz_options"].append(("pie_chart", [col]))
            
        # One categorical and one numeric column
        if n_cat == 1 and n_num == 1:
            cat_col = results["categorical_columns"][0]
            num_col = results["numeric_columns"][0]
            
            # Check number of categories
            n_unique = results["unique_values"][cat_col]
            
            if n_unique <= 10:
                results["recommended_viz"].append(("bar_chart", [cat_col, num_col]))
                results["viz_options"].append(("grouped_bar", [cat_col, num_col]))
            else:
                results["recommended_viz"].append(("heatmap", [cat_col, num_col]))
                
            results["viz_options"].append(("boxplot", [cat_col, num_col]))
            
        # Two numeric columns
        if n_num == 2 and n_cat == 0:
            cols = results["numeric_columns"]
            results["recommended_viz"].append(("scatter_plot", cols))
            results["viz_options"].append(("line_chart", cols))
            
        # One temporal and one numeric column
        if n_time == 1 and n_num == 1:
            time_col = results["temporal_columns"][0]
            num_col = results["numeric_columns"][0]
            results["recommended_viz"].append(("line_chart", [time_col, num_col]))
            results["viz_options"].append(("area_chart", [time_col, num_col]))
            
        # Two categorical columns
        if n_cat == 2 and n_num == 0:
            cols = results["categorical_columns"]
            results["recommended_viz"].append(("heatmap", cols))
            results["viz_options"].append(("grouped_bar", cols))
            
        # Two categorical columns and one numeric column
        if n_cat == 2 and n_num == 1:
            cat_cols = results["categorical_columns"]
            num_col = results["numeric_columns"][0]
            results["recommended_viz"].append(("heatmap", [*cat_cols, num_col]))
            results["viz_options"].append(("grouped_bar", [*cat_cols, num_col]))
            
        # Multiple numeric columns
        if n_num > 2:
            cols = results["numeric_columns"]
            results["recommended_viz"].append(("correlation_matrix", cols))
            results["viz_options"].append(("parallel_coordinates", cols))
            results["viz_options"].append(("scatter_matrix", cols))
            
        # Many columns but few rows
        if n_cols > 5 and n_rows < 20:
            results["recommended_viz"].append(("heatmap", results["columns"]))
            
        # Several categorical columns
        if n_cat >= 3:
            results["viz_options"].append(("parallel_sets", results["categorical_columns"]))
            
        # Fallbacks for complex data
        if not results["recommended_viz"] and not results["viz_options"]:
            if n_num > 0:
                results["recommended_viz"].append(("scatter_plot", results["numeric_columns"][:2]))
            elif n_cat > 0:
                results["recommended_viz"].append(("bar_chart", [results["categorical_columns"][0]]))
            else:
                results["recommended_viz"].append(("table", results["columns"]))
    
    def _analyze_graph(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Analyze graph data.
        
        Args:
            data: Graph data with nodes and edges
            
        Returns:
            Dict[str, Any]: Analysis results
        """
        nodes = data.get("nodes", [])
        edges = data.get("edges", [])
        
        results = {
            "data_type": "graph",
            "n_nodes": len(nodes),
            "n_edges": len(edges),
            "node_attributes": set(),
            "edge_attributes": set(),
            "directed": data.get("directed", False),
            "density": 0,
            "has_weights": False,
            "recommended_viz": [],
            "viz_options": []
        }
        
        # Extract node and edge attributes
        if nodes and isinstance(nodes[0], dict):
            results["node_attributes"] = set().union(*(node.keys() for node in nodes))
            
        if edges and isinstance(edges[0], dict):
            results["edge_attributes"] = set().union(*(edge.keys() for edge in edges))
            results["has_weights"] = "weight" in results["edge_attributes"]
            
        # Calculate graph density
        if results["n_nodes"] > 1:
            max_edges = results["n_nodes"] * (results["n_nodes"] - 1)
            if not results["directed"]:
                max_edges /= 2
            results["density"] = results["n_edges"] / max_edges
            
        # Recommend visualizations
        if results["n_nodes"] <= 100:
            results["recommended_viz"].append(("network_graph", ["nodes", "edges"]))
        else:
            results["recommended_viz"].append(("adjacency_matrix", ["nodes", "edges"]))
            
        results["viz_options"].append(("chord_diagram", ["nodes", "edges"]))
        results["viz_options"].append(("arc_diagram", ["nodes", "edges"]))
        
        return results
    
    def _analyze_nested_json(self, data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Analyze nested JSON data.
        
        Args:
            data: List of dictionaries
            
        Returns:
            Dict[str, Any]: Analysis results
        """
        results = {
            "data_type": "nested_json",
            "n_records": len(data),
            "fields": set(),
            "field_types": {},
            "recommended_viz": [],
            "viz_options": []
        }
        
        # Extract fields and their types
        all_fields = set()
        field_values = defaultdict(list)
        
        for record in data:
            all_fields.update(record.keys())
            for field, value in record.items():
                field_values[field].append(value)
                
        results["fields"] = all_fields
        
        # Analyze field types
        for field, values in field_values.items():
            # Skip empty values
            values = [v for v in values if v is not None]
            if not values:
                results["field_types"][field] = "unknown"
                continue
                
            # Determine type based on values
            if all(isinstance(v, (int, float)) for v in values):
                results["field_types"][field] = "numeric"
            elif all(isinstance(v, bool) for v in values):
                results["field_types"][field] = "boolean"
            elif all(isinstance(v, str) for v in values):
                # Check for date strings
                if all(self._is_date(v) for v in values):
                    results["field_types"][field] = "temporal"
                else:
                    # Check if categorical (few unique values)
                    unique_values = len(set(values))
                    if unique_values <= self.max_categories:
                        results["field_types"][field] = "categorical"
                    else:
                        results["field_types"][field] = "text"
            elif all(isinstance(v, (list, tuple)) for v in values):
                results["field_types"][field] = "array"
            elif all(isinstance(v, dict) for v in values):
                results["field_types"][field] = "object"
            else:
                results["field_types"][field] = "mixed"
                
        # Count field types
        type_counts = Counter(results["field_types"].values())
        n_numeric = type_counts.get("numeric", 0)
        n_categorical = type_counts.get("categorical", 0)
        n_temporal = type_counts.get("temporal", 0)
        
        # Recommend visualizations based on field types
        numeric_fields = [f for f, t in results["field_types"].items() if t == "numeric"]
        categorical_fields = [f for f, t in results["field_types"].items() if t == "categorical"]
        temporal_fields = [f for f, t in results["field_types"].items() if t == "temporal"]
        
        # Similar logic to DataFrame visualization recommendation
        if n_numeric == 1 and n_categorical == 0:
            results["recommended_viz"].append(("histogram", numeric_fields))
            
        if n_categorical == 1 and n_numeric == 0:
            results["recommended_viz"].append(("bar_chart", categorical_fields))
            
        if n_categorical == 1 and n_numeric == 1:
            results["recommended_viz"].append(("bar_chart", [categorical_fields[0], numeric_fields[0]]))
            
        if n_numeric == 2:
            results["recommended_viz"].append(("scatter_plot", numeric_fields[:2]))
            
        if n_temporal == 1 and n_numeric == 1:
            results["recommended_viz"].append(("line_chart", [temporal_fields[0], numeric_fields[0]]))
            
        # Fallback
        if not results["recommended_viz"]:
            results["recommended_viz"].append(("table", list(all_fields)))
            
        return results
    
    def _analyze_basic(self, data: Any) -> Dict[str, Any]:
        """
        Perform basic analysis on generic data.
        
        Args:
            data: Input data
            
        Returns:
            Dict[str, Any]: Analysis results
        """
        results = {
            "data_type": "unknown",
            "recommended_viz": [],
            "viz_options": []
        }
        
        # Try to determine data type
        if isinstance(data, (list, tuple)):
            results["data_type"] = "list"
            results["length"] = len(data)
            
            # Check if it's a list of numbers
            if all(isinstance(x, (int, float)) for x in data):
                results["element_type"] = "numeric"
                results["recommended_viz"].append(("bar_chart", ["values"]))
                results["viz_options"].append(("line_chart", ["indices", "values"]))
                
            # Check if it's a list of strings
            elif all(isinstance(x, str) for x in data):
                results["element_type"] = "string"
                results["recommended_viz"].append(("word_cloud", ["values"]))
                
            else:
                results["element_type"] = "mixed"
                results["recommended_viz"].append(("table", ["values"]))
                
        elif isinstance(data, dict):
            results["data_type"] = "dict"
            results["n_keys"] = len(data)
            
            # Check if keys and values suggest specific visualizations
            key_types = set(type(k).__name__ for k in data.keys())
            value_types = set(type(v).__name__ for v in data.values())
            
            if len(key_types) == 1 and len(value_types) == 1:
                key_type = list(key_types)[0]
                value_type = list(value_types)[0]
                
                if key_type in ("str", "int") and value_type in ("int", "float"):
                    results["recommended_viz"].append(("bar_chart", ["keys", "values"]))
                    results["viz_options"].append(("pie_chart", ["keys", "values"]))
                    
        else:
            # Primitive type or custom object
            results["data_type"] = type(data).__name__
            results["recommended_viz"].append(("text", ["value"]))
            
        return results
    
    def _is_date(self, value: str) -> bool:
        """Check if a string looks like a date."""
        if not isinstance(value, str):
            return False
            
        # Simple date pattern matching
        date_patterns = [
            r'^\d{4}-\d{2}-\d{2}$',  # ISO format: 2023-01-31
            r'^\d{1,2}/\d{1,2}/\d{2,4}$',  # Common US format: 1/31/2023
            r'^\d{1,2}-\d{1,2}-\d{2,4}$',  # Common format: 31-1-2023
            r'^\d{1,2}\s+(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]*\s+\d{2,4}$'  # 31 Jan 2023
        ]
        
        return any(re.match(pattern, value) for pattern in date_patterns)


class LayoutManager:
    """
    Manages the layout of visualization elements.
    
    This class handles automatic layout for different visualization types,
    ensuring proper spacing, alignment, and composition of visual elements.
    """
    
    def __init__(
        self, 
        default_width: int = 800, 
        default_height: int = 600,
        dpi: int = 100,
        tight_layout: bool = True,
        padding: float = 0.1,
        aspect_ratio: Union[str, float] = "auto"
    ):
        """
        Initialize the layout manager.
        
        Args:
            default_width: Default width in pixels
            default_height: Default height in pixels
            dpi: Dots per inch for raster images
            tight_layout: Whether to use tight layout
            padding: Padding around the layout
            aspect_ratio: Aspect ratio (auto, equal, or float)
        """
        self.default_width = default_width
        self.default_height = default_height
        self.dpi = dpi
        self.tight_layout = tight_layout
        self.padding = padding
        self.aspect_ratio = aspect_ratio
    
    def create_figure(
        self, 
        fig_type: str,
        width: Optional[int] = None, 
        height: Optional[int] = None,
        **kwargs
    ) -> Tuple[Any, Any]:
        """
        Create a figure with the appropriate dimensions and layout.
        
        Args:
            fig_type: Type of figure
            width: Optional width override
            height: Optional height override
            **kwargs: Additional figure parameters
            
        Returns:
            Tuple[Any, Any]: Figure and axes objects
        """
        if not _HAS_MATPLOTLIB:
            raise ImportError("Matplotlib is required for creating figures")
            
        # Use default dimensions if not specified
        width = width or self.default_width
        height = height or self.default_height
        
        # Convert to inches for matplotlib
        width_in = width / self.dpi
        height_in = height / self.dpi
        
        # Create figure based on type
        if fig_type == "regular":
            fig, ax = plt.subplots(figsize=(width_in, height_in), dpi=self.dpi)
            
        elif fig_type == "3d":
            fig = plt.figure(figsize=(width_in, height_in), dpi=self.dpi)
            ax = fig.add_subplot(111, projection="3d")
            
        elif fig_type == "grid":
            nrows = kwargs.get("nrows", 1)
            ncols = kwargs.get("ncols", 1)
            fig, axes = plt.subplots(
                nrows=nrows, 
                ncols=ncols, 
                figsize=(width_in, height_in), 
                dpi=self.dpi,
                sharex=kwargs.get("sharex", False),
                sharey=kwargs.get("sharey", False)
            )
            
            # For single-cell grid, axes will be a single object, not array
            if nrows == 1 and ncols == 1:
                ax = np.array([[axes]])
            else:
                ax = axes
                
        elif fig_type == "subplots":
            fig = plt.figure(figsize=(width_in, height_in), dpi=self.dpi)
            subplot_specs = kwargs.get("subplot_specs", {})
            ax = {}
            
            for name, spec in subplot_specs.items():
                ax[name] = fig.add_subplot(spec.get("position"), projection=spec.get("projection"))
                
        else:
            # Default to regular figure
            fig, ax = plt.subplots(figsize=(width_in, height_in), dpi=self.dpi)
            
        # Apply aspect ratio if not auto
        if self.aspect_ratio != "auto":
            if isinstance(ax, plt.Axes):
                if self.aspect_ratio == "equal":
                    ax.set_aspect("equal")
                else:
                    try:
                        aspect_value = float(self.aspect_ratio)
                        ax.set_aspect(aspect_value)
                    except (ValueError, TypeError):
                        pass
                        
        # Apply tight layout if enabled
        if self.tight_layout:
            plt.tight_layout(pad=self.padding)
            
        return fig, ax
    
    def create_grid_layout(
        self, 
        n_elements: int,
        width: Optional[int] = None, 
        height: Optional[int] = None
    ) -> Tuple[Any, Any]:
        """
        Create a grid layout for multiple visualization elements.
        
        Args:
            n_elements: Number of elements in the grid
            width: Optional width override
            height: Optional height override
            
        Returns:
            Tuple[Any, Any]: Figure and axes objects
        """
        # Calculate grid dimensions
        if n_elements == 1:
            nrows, ncols = 1, 1
        elif n_elements == 2:
            nrows, ncols = 1, 2
        elif n_elements <= 4:
            nrows, ncols = 2, 2
        else:
            # Calculate a grid that's roughly square
            ncols = math.ceil(math.sqrt(n_elements))
            nrows = math.ceil(n_elements / ncols)
            
        # Create grid figure
        return self.create_figure(
            "grid", 
            width=width, 
            height=height,
            nrows=nrows,
            ncols=ncols
        )
    
    def adjust_layout(self, fig: Any, **kwargs) -> None:
        """
        Adjust the layout of an existing figure.
        
        Args:
            fig: Figure object
            **kwargs: Layout parameters
        """
        if not fig:
            return
            
        # Apply tight layout with specified parameters
        if self.tight_layout:
            pad = kwargs.get("pad", self.padding)
            w_pad = kwargs.get("w_pad", None)
            h_pad = kwargs.get("h_pad", None)
            
            plt.figure(fig.number)
            plt.tight_layout(pad=pad, w_pad=w_pad, h_pad=h_pad)
            
        # Apply specific adjustments
        fig.subplots_adjust(
            left=kwargs.get("left", None),
            right=kwargs.get("right", None),
            bottom=kwargs.get("bottom", None),
            top=kwargs.get("top", None),
            wspace=kwargs.get("wspace", None),
            hspace=kwargs.get("hspace", None)
        )
    
    def create_svg_document(
        self, 
        width: Optional[int] = None, 
        height: Optional[int] = None
    ) -> Any:
        """
        Create an SVG document for vector graphics.
        
        Args:
            width: Optional width override
            height: Optional height override
            
        Returns:
            Any: SVG document object
        """
        if not _HAS_SVGWRITE:
            raise ImportError("svgwrite is required for creating SVG documents")
            
        # Use default dimensions if not specified
        width = width or self.default_width
        height = height or self.default_height
        
        # Create SVG document
        return Drawing(
            size=(f"{width}px", f"{height}px"),
            profile="tiny",
            debug=False
        )
    
    def calculate_optimal_dimensions(
        self,
        data: Any,
        viz_type: str,
        base_width: Optional[int] = None,
        base_height: Optional[int] = None
    ) -> Tuple[int, int]:
        """
        Calculate optimal dimensions for a visualization based on data characteristics.
        
        Args:
            data: Input data
            viz_type: Visualization type
            base_width: Base width to scale from
            base_height: Base height to scale from
            
        Returns:
            Tuple[int, int]: Width and height in pixels
        """
        # Use default dimensions if not specified
        base_width = base_width or self.default_width
        base_height = base_height or self.default_height
        
        # Initial dimensions
        width = base_width
        height = base_height
        
        # Adjust based on visualization type and data
        if viz_type == "bar_chart":
            # Scale height based on number of categories for horizontal bars
            if isinstance(data, dict) and "categorical_columns" in data:
                n_categories = len(data.get("categorical_columns", []))
                if n_categories > 10:
                    # Horizontal bar chart
                    height = min(base_height * 1.5, 50 * n_categories)
                    
        elif viz_type == "scatter_plot":
            # For scatter plots, aim for a more square aspect ratio
            aspect_ratio = 4/3
            height = width / aspect_ratio
            
        elif viz_type == "network_graph":
            # For network graphs, use larger dimensions for many nodes
            if isinstance(data, dict) and "n_nodes" in data:
                n_nodes = data.get("n_nodes", 0)
                if n_nodes > 50:
                    # Scale up for many nodes
                    scale_factor = min(2.0, 1.0 + (n_nodes - 50) / 100)
                    width = base_width * scale_factor
                    height = base_height * scale_factor
                    
        elif viz_type == "heatmap":
            # For heatmaps, adjust dimensions based on matrix size
            if isinstance(data, (list, tuple)):
                rows = len(data)
                cols = len(data[0]) if data and isinstance(data[0], (list, tuple)) else rows
                
                # Size each cell appropriately
                min_cell_size = 40  # minimum cell size in pixels
                width = max(base_width, min_cell_size * cols)
                height = max(base_height, min_cell_size * rows)
                
        elif viz_type == "table":
            # For tables, adjust height based on number of rows
            if isinstance(data, (list, tuple)):
                rows = len(data)
                # Estimate row height (header + data rows)
                row_height = 30  # pixels per row
                height = max(base_height, 100 + row_height * rows)
                
        # Ensure reasonable limits
        width = max(300, min(width, 2000))
        height = max(200, min(height, 2000))
        
        return int(width), int(height)


class VisualizationGenerator:
    """
    Generates visualizations based on data and configuration.
    
    This class implements various visualization types and handles the
    rendering process for charts, graphs, and diagrams.
    """
    
    def __init__(
        self,
        color_manager: ColorManager,
        layout_manager: LayoutManager,
        config: Dict[str, Any]
    ):
        """
        Initialize the visualization generator.
        
        Args:
            color_manager: Color management instance
            layout_manager: Layout management instance
            config: Configuration parameters
        """
        self.color_manager = color_manager
        self.layout_manager = layout_manager
        self.config = config
        
        # Set up matplotlib style
        if _HAS_MATPLOTLIB:
            self._setup_matplotlib_style()
    
    def _setup_matplotlib_style(self):
        """Configure matplotlib style based on settings."""
        # Get colors from color manager
        bg_color = self.color_manager.get_color("background")
        text_color = self.color_manager.get_color("text")
        grid_color = self.color_manager.get_color("grid")
        
        # Create style dict
        style_dict = {
            "figure.facecolor": bg_color,
            "axes.facecolor": bg_color,
            "axes.edgecolor": text_color,
            "axes.labelcolor": text_color,
            "xtick.color": text_color,
            "ytick.color": text_color,
            "text.color": text_color,
            "grid.color": grid_color,
            "grid.alpha": self.config.get("grid_alpha", 0.3),
            "font.family": self.config.get("font_family", "sans-serif"),
            "font.size": self.config.get("label_font_size", 12),
            "axes.titlesize": self.config.get("title_font_size", 16),
            "axes.labelsize": self.config.get("label_font_size", 12),
            "xtick.labelsize": self.config.get("tick_font_size", 10),
            "ytick.labelsize": self.config.get("tick_font_size", 10),
            "legend.fontsize": self.config.get("legend_font_size", 10),
            "figure.titlesize": self.config.get("title_font_size", 16),
            "axes.grid": self.config.get("show_grid", True),
            "grid.linestyle": "--",
            "axes.spines.top": False,
            "axes.spines.right": False,
        }
        
        # Apply style
        plt.rcParams.update(style_dict)
    
    def create_visualization(
        self,
        data: Any,
        viz_type: str,
        columns: Optional[List[str]] = None,
        title: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Create a visualization based on data and type.
        
        Args:
            data: Input data
            viz_type: Visualization type
            columns: Optional column selection
            title: Optional visualization title
            **kwargs: Additional visualization parameters
            
        Returns:
            Dict[str, Any]: Visualization result including image data
        """
        # Initialize result dict
        result = {
            "success": False,
            "type": viz_type,
            "format": self.config.get("format", "png"),
            "data": None,
            "error": None
        }
        
        try:
            # Select visualization function based on type
            viz_func = self._get_visualization_function(viz_type)
            
            if viz_func is None:
                result["error"] = f"Unsupported visualization type: {viz_type}"
                return result
                
            # Calculate optimal dimensions
            width, height = self.layout_manager.calculate_optimal_dimensions(
                data=data,
                viz_type=viz_type,
                base_width=self.config.get("default_width"),
                base_height=self.config.get("default_height")
            )
            
            # Add dimensions to kwargs
            kwargs["width"] = kwargs.get("width", width)
            kwargs["height"] = kwargs.get("height", height)
            
            # Generate visualization
            viz_output = viz_func(
                data=data,
                columns=columns,
                title=title,
                **kwargs
            )
            
            # Process output
            if "figure" in viz_output:
                result["data"] = self._render_figure(
                    viz_output["figure"],
                    format=result["format"]
                )
            elif "svg" in viz_output:
                result["data"] = viz_output["svg"].tostring()
                result["format"] = "svg"
            elif "html" in viz_output:
                result["data"] = viz_output["html"]
                result["format"] = "html"
            elif "image" in viz_output:
                result["data"] = viz_output["image"]
                
            # Add extra data if available
            if "insights" in viz_output:
                result["insights"] = viz_output["insights"]
                
            # Mark as successful
            result["success"] = True
            
        except Exception as e:
            result["error"] = str(e)
            logger.error(f"Error generating {viz_type} visualization: {e}")
            
        return result
    
    def _get_visualization_function(self, viz_type: str) -> Optional[Callable]:
        """
        Get the appropriate visualization function for a given type.
        
        Args:
            viz_type: Visualization type
            
        Returns:
            Optional[Callable]: Visualization function or None
        """
        # Map visualization types to methods
        viz_map = {
            # Chart types
            "bar_chart": self.create_bar_chart,
            "line_chart": self.create_line_chart,
            "scatter_plot": self.create_scatter_plot,
            "pie_chart": self.create_pie_chart,
            "donut_chart": self.create_donut_chart,
            "area_chart": self.create_area_chart,
            "histogram": self.create_histogram,
            "boxplot": self.create_boxplot,
            "heatmap": self.create_heatmap,
            "correlation_matrix": self.create_correlation_matrix,
            "radar_chart": self.create_radar_chart,
            "parallel_coordinates": self.create_parallel_coordinates,
            "density_plot": self.create_density_plot,
            "stacked_bar": self.create_stacked_bar,
            "grouped_bar": self.create_grouped_bar,
            "violin_plot": self.create_violin_plot,
            "bubble_chart": self.create_bubble_chart,
            
            # Graph types
            "network_graph": self.create_network_graph,
            "adjacency_matrix": self.create_adjacency_matrix,
            "chord_diagram": self.create_chord_diagram,
            "tree_diagram": self.create_tree_diagram,
            "sankey_diagram": self.create_sankey_diagram,
            "arc_diagram": self.create_arc_diagram,
            
            # Map types
            "choropleth_map": self.create_choropleth_map,
            "geographic_map": self.create_geographic_map,
            
            # Other types
            "table": self.create_table,
            "word_cloud": self.create_word_cloud,
            "calendar_heatmap": self.create_calendar_heatmap,
            "venn_diagram": self.create_venn_diagram,
            "parallel_sets": self.create_parallel_sets,
            "sunburst_chart": self.create_sunburst_chart,
            "treemap": self.create_treemap,
            "timeline": self.create_timeline,
            "gantt_chart": self.create_gantt_chart,
            "scatter_matrix": self.create_scatter_matrix
        }
        
        return viz_map.get(viz_type)
    
    def _render_figure(self, fig: Any, format: str = "png") -> Optional[bytes]:
        """
        Render a matplotlib figure to the specified format.
        
        Args:
            fig: Matplotlib figure
            format: Output format
            
        Returns:
            Optional[bytes]: Encoded image data
        """
        if not fig:
            return None
            
        # Create a BytesIO object to store the image
        img_data = io.BytesIO()
        
        # Save figure to BytesIO object
        try:
            fig.savefig(
                img_data, 
                format=format,
                dpi=self.config.get("dpi", 100),
                bbox_inches="tight",
                pad_inches=0.1,
                facecolor=fig.get_facecolor(),
                edgecolor="none",
                transparent=False
            )
            plt.close(fig)
            
            # Get image data
            img_data.seek(0)
            return img_data.getvalue()
            
        except Exception as e:
            logger.error(f"Error rendering figure: {e}")
            plt.close(fig)
            return None
    
    #
    # Chart creation methods
    #
    
    def create_bar_chart(
        self,
        data: Any,
        columns: Optional[List[str]] = None,
        title: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Create a bar chart visualization.
        
        Args:
            data: Input data
            columns: Column names for categories and values
            title: Chart title
            **kwargs: Additional parameters
            
        Returns:
            Dict[str, Any]: Visualization output
        """
        # Check if matplotlib is available
        if not _HAS_MATPLOTLIB:
            raise ImportError("Matplotlib is required for bar charts")
            
        # Process parameters
        width = kwargs.get("width", self.config.get("default_width"))
        height = kwargs.get("height", self.config.get("default_height"))
        orientation = kwargs.get("orientation", self.config.get("bar_orientation", "vertical"))
        show_values = kwargs.get("show_values", self.config.get("show_data_labels", True))
        show_grid = kwargs.get("show_grid", self.config.get("show_grid", True))
        
        # Create figure and axes
        fig, ax = self.layout_manager.create_figure("regular", width=width, height=height)
        
        # Extract data
        x_data, y_data, labels = self._extract_data_for_bar_chart(data, columns)
        
        # Get colors
        colors = kwargs.get("colors", self.color_manager.get_categorical_palette(len(x_data)))
        
        # Create bar chart
        if orientation == "vertical":
            bars = ax.bar(x_data, y_data, color=colors, width=kwargs.get("bar_width", 0.6))
            ax.set_xlabel(kwargs.get("xlabel", columns[0] if columns and len(columns) > 0 else "Categories"))
            ax.set_ylabel(kwargs.get("ylabel", columns[1] if columns and len(columns) > 1 else "Values"))
            
            # Rotate x labels if there are many categories
            if len(x_data) > 5:
                plt.xticks(rotation=45, ha="right")
                
            # Add value labels
            if show_values:
                for bar in bars:
                    height = bar.get_height()
                    ax.text(
                        bar.get_x() + bar.get_width() / 2.,
                        height,
                        f"{height:.1f}",
                        ha="center",
                        va="bottom",
                        fontsize=self.config.get("annotation_font_size", 11)
                    )
        else:
            # Horizontal orientation
            bars = ax.barh(x_data, y_data, color=colors, height=kwargs.get("bar_width", 0.6))
            ax.set_ylabel(kwargs.get("xlabel", columns[0] if columns and len(columns) > 0 else "Categories"))
            ax.set_xlabel(kwargs.get("ylabel", columns[1] if columns and len(columns) > 1 else "Values"))
            
            # Add value labels
            if show_values:
                for bar in bars:
                    width = bar.get_width()
                    ax.text(
                        width,
                        bar.get_y() + bar.get_height() / 2.,
                        f"{width:.1f}",
                        ha="left",
                        va="center",
                        fontsize=self.config.get("annotation_font_size", 11)
                    )
                    
        # Set title
        if title:
            ax.set_title(title, fontsize=self.config.get("title_font_size", 16))
            
        # Show grid if requested
        ax.grid(show_grid, axis="y" if orientation == "vertical" else "x",
                linestyle="--", alpha=self.config.get("grid_alpha", 0.3))
                
        # Tight layout
        fig.tight_layout()
        
        # Extract insights
        insights = self._extract_insights_from_bar_chart(x_data, y_data, labels)
        
        return {
            "figure": fig,
            "insights": insights
        }
    
    def _extract_data_for_bar_chart(
        self,
        data: Any,
        columns: Optional[List[str]] = None
    ) -> Tuple[List[Any], List[float], List[str]]:
        """
        Extract data for a bar chart from various input formats.
        
        Args:
            data: Input data
            columns: Column names
            
        Returns:
            Tuple[List[Any], List[float], List[str]]: x data, y data, and labels
        """
        x_data = []
        y_data = []
        labels = []
        
        if _HAS_PANDAS and isinstance(data, pd.DataFrame):
            # DataFrame data
            if columns and len(columns) >= 2:
                # Use specified columns
                cat_col, val_col = columns[0], columns[1]
                df = data[[cat_col, val_col]].dropna()
                
                # For too many categories, take top N
                if len(df) > 20:
                    df = df.nlargest(20, val_col)
                    
                x_data = df[cat_col].tolist()
                y_data = df[val_col].tolist()
                labels = df[cat_col].tolist()
            else:
                # Use first categorical and first numeric column
                cat_cols = data.select_dtypes(include=["object", "category"]).columns
                num_cols = data.select_dtypes(include=["number"]).columns
                
                if len(cat_cols) > 0 and len(num_cols) > 0:
                    cat_col, val_col = cat_cols[0], num_cols[0]
                    df = data[[cat_col, val_col]].dropna()
                    
                    # For too many categories, take top N
                    if len(df) > 20:
                        df = df.nlargest(20, val_col)
                        
                    x_data = df[cat_col].tolist()
                    y_data = df[val_col].tolist()
                    labels = df[cat_col].tolist()
                elif len(num_cols) > 0:
                    # Use column name as category
                    val_col = num_cols[0]
                    x_data = data.index.tolist()
                    y_data = data[val_col].tolist()
                    labels = data.index.tolist()
                    
        elif isinstance(data, dict):
            # Dictionary data
            if "keys" in data and "values" in data:
                # Pre-defined keys and values
                x_data = data["keys"]
                y_data = data["values"]
                labels = data["keys"]
            else:
                # Use dict items
                items = list(data.items())
                
                # Sort by value for better visualization
                items.sort(key=lambda x: x[1], reverse=True)
                
                # For too many items, take top N
                if len(items) > 20:
                    items = items[:20]
                    
                x_data = [item[0] for item in items]
                y_data = [item[1] for item in items]
                labels = x_data
                
        elif isinstance(data, (list, tuple)):
            # List data
            if all(isinstance(item, (int, float)) for item in data):
                # List of numbers
                x_data = list(range(len(data)))
                y_data = data
                labels = [f"Item {i+1}" for i in range(len(data))]
            elif all(isinstance(item, (list, tuple)) for item in data) and all(len(item) >= 2 for item in data):
                # List of [x, y] pairs
                x_data = [item[0] for item in data]
                y_data = [item[1] for item in data]
                labels = [str(item[0]) for item in data]
                
        # Handle empty data
        if not x_data or not y_data:
            # Generate dummy data
            x_data = ["No data"]
            y_data = [0]
            labels = ["No data"]
            
        return x_data, y_data, labels
    
    def _extract_insights_from_bar_chart(
        self,
        x_data: List[Any],
        y_data: List[float],
        labels: List[str]
    ) -> Dict[str, Any]:
        """
        Extract insights from bar chart data.
        
        Args:
            x_data: X-axis data
            y_data: Y-axis data
            labels: Data labels
            
        Returns:
            Dict[str, Any]: Extracted insights
        """
        insights = {}
        
        if not y_data:
            return insights
            
        # Find maximum and minimum values
        max_idx = np.argmax(y_data)
        min_idx = np.argmin(y_data)
        
        insights["maximum"] = {
            "value": y_data[max_idx],
            "category": labels[max_idx] if max_idx < len(labels) else "Unknown"
        }
        
        insights["minimum"] = {
            "value": y_data[min_idx],
            "category": labels[min_idx] if min_idx < len(labels) else "Unknown"
        }
        
        # Calculate basic statistics
        insights["statistics"] = {
            "mean": np.mean(y_data),
            "median": np.median(y_data),
            "sum": np.sum(y_data),
            "std_dev": np.std(y_data)
        }
        
        # Calculate distribution metrics
        total = sum(y_data)
        if total > 0:
            # Calculate percentages
            percentages = [value / total * 100 for value in y_data]
            
            # Identify items that make up majority
            cumulative_percent = 0
            majority_items = []
            
            sorted_pairs = sorted(zip(labels, y_data, percentages), key=lambda x: x[1], reverse=True)
            
            for label, value, percent in sorted_pairs:
                cumulative_percent += percent
                majority_items.append({"category": label, "value": value, "percent": percent})
                if cumulative_percent >= 80:
                    break
                    
            insights["distribution"] = {
                "top_items": majority_items,
                "top_percent": cumulative_percent
            }
            
        return insights
    
    def create_line_chart(
        self,
        data: Any,
        columns: Optional[List[str]] = None,
        title: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Create a line chart visualization.
        
        Args:
            data: Input data
            columns: Column names for x and y axes
            title: Chart title
            **kwargs: Additional parameters
            
        Returns:
            Dict[str, Any]: Visualization output
        """
        # Check if matplotlib is available
        if not _HAS_MATPLOTLIB:
            raise ImportError("Matplotlib is required for line charts")
            
        # Process parameters
        width = kwargs.get("width", self.config.get("default_width"))
        height = kwargs.get("height", self.config.get("default_height"))
        line_style = kwargs.get("line_style", self.config.get("line_style", "-"))
        line_width = kwargs.get("line_width", self.config.get("line_width", 2.0))
        marker = kwargs.get("marker", "o")
        marker_size = kwargs.get("marker_size", self.config.get("marker_size", 6))
        show_grid = kwargs.get("show_grid", self.config.get("show_grid", True))
        
        # Create figure and axes
        fig, ax = self.layout_manager.create_figure("regular", width=width, height=height)
        
        # Extract data
        x_data, y_data, labels, series = self._extract_data_for_line_chart(data, columns)
        
        # Get colors
        if series:
            # Multiple series
            colors = kwargs.get("colors", self.color_manager.get_categorical_palette(len(series)))
            
            # Plot each series
            for i, serie in enumerate(series):
                ax.plot(
                    x_data,
                    serie["values"],
                    label=serie["name"],
                    color=colors[i % len(colors)],
                    linestyle=line_style,
                    linewidth=line_width,
                    marker=marker,
                    markersize=marker_size
                )
                
            # Add legend
            if self.config.get("show_legend", True):
                ax.legend(
                    loc=self.config.get("legend_loc", "best"),
                    fontsize=self.config.get("legend_font_size", 10)
                )
        else:
            # Single series
            color = kwargs.get("color", self.color_manager.get_color("primary"))
            ax.plot(
                x_data,
                y_data,
                label=kwargs.get("label", columns[1] if columns and len(columns) > 1 else "Values"),
                color=color,
                linestyle=line_style,
                linewidth=line_width,
                marker=marker,
                markersize=marker_size
            )
            
            # Add legend if label is provided
            if kwargs.get("label") and self.config.get("show_legend", True):
                ax.legend(
                    loc=self.config.get("legend_loc", "best"),
                    fontsize=self.config.get("legend_font_size", 10)
                )
                
        # Set labels
        ax.set_xlabel(kwargs.get("xlabel", columns[0] if columns and len(columns) > 0 else "X"))
        ax.set_ylabel(kwargs.get("ylabel", columns[1] if columns and len(columns) > 1 else "Y"))
        
        # Set title
        if title:
            ax.set_title(title, fontsize=self.config.get("title_font_size", 16))
            
        # Rotate x-axis labels if datetime or many categories
        if isinstance(x_data[0], str) and len(x_data) > 5:
            plt.xticks(rotation=45, ha="right")
            
        # Show grid if requested
        ax.grid(show_grid, linestyle="--", alpha=self.config.get("grid_alpha", 0.3))
        
        # Tight layout
        fig.tight_layout()
        
        # Extract insights
        insights = self._extract_insights_from_line_chart(x_data, y_data, series)
        
        return {
            "figure": fig,
            "insights": insights
        }
    
    def _extract_data_for_line_chart(
        self,
        data: Any,
        columns: Optional[List[str]] = None
    ) -> Tuple[List[Any], List[float], List[str], List[Dict[str, Any]]]:
        """
        Extract data for a line chart from various input formats.
        
        Args:
            data: Input data
            columns: Column names
            
        Returns:
            Tuple[List[Any], List[float], List[str], List[Dict[str, Any]]]:
            x data, y data, labels, and series data
        """
        x_data = []
        y_data = []
        labels = []
        series = []
        
        if _HAS_PANDAS and isinstance(data, pd.DataFrame):
            # DataFrame data
            if columns and len(columns) >= 2:
                # Use specified columns
                x_col = columns[0]
                
                if len(columns) == 2:
                    # Single y column
                    y_col = columns[1]
                    df = data[[x_col, y_col]].dropna()
                    
                    x_data = df[x_col].tolist()
                    y_data = df[y_col].tolist()
                    labels = df[x_col].astype(str).tolist()
                else:
                    # Multiple y columns
                    y_cols = columns[1:]
                    df = data[[x_col] + y_cols].dropna()
                    
                    x_data = df[x_col].tolist()
                    labels = df[x_col].astype(str).tolist()
                    
                    # Create series for each y column
                    for y_col in y_cols:
                        series.append({
                            "name": y_col,
                            "values": df[y_col].tolist()
                        })
            else:
                # Try to find appropriate columns
                # Use first temporal column as x, or if none, use first categorical
                time_cols = [col for col in data.select_dtypes(include=["datetime64"]).columns]
                cat_cols = [col for col in data.select_dtypes(include=["object", "category"]).columns]
                num_cols = [col for col in data.select_dtypes(include=["number"]).columns]
                
                if time_cols and num_cols:
                    # Use first time column as x and numeric columns as y
                    x_col = time_cols[0]
                    
                    x_data = data[x_col].tolist()
                    labels = [str(x) for x in x_data]
                    
                    for y_col in num_cols[:5]:  # Limit to first 5 numeric columns
                        series.append({
                            "name": y_col,
                            "values": data[y_col].tolist()
                        })
                elif cat_cols and num_cols:
                    # Use first categorical column as x and first numeric column as y
                    x_col = cat_cols[0]
                    y_col = num_cols[0]
                    
                    df = data[[x_col, y_col]].dropna()
                    
                    x_data = df[x_col].tolist()
                    y_data = df[y_col].tolist()
                    labels = df[x_col].astype(str).tolist()
                elif num_cols:
                    # Use first numeric column as x and second as y
                    if len(num_cols) >= 2:
                        x_col, y_col = num_cols[0], num_cols[1]
                        
                        df = data[[x_col, y_col]].dropna()
                        
                        x_data = df[x_col].tolist()
                        y_data = df[y_col].tolist()
                        labels = [str(x) for x in x_data]
                    else:
                        # Use index as x and numeric column as y
                        y_col = num_cols[0]
                        
                        x_data = data.index.tolist()
                        y_data = data[y_col].tolist()
                        labels = [str(x) for x in x_data]
                        
        elif isinstance(data, dict):
            # Dictionary data
            if "x" in data and "y" in data:
                # Pre-defined x and y
                x_data = data["x"]
                y_data = data["y"]
                labels = [str(x) for x in x_data]
                
                # Check for series data
                if "series" in data and isinstance(data["series"], list):
                    series = data["series"]
            elif "series" in data and isinstance(data["series"], list):
                # Multiple series with common x-axis
                if "x" in data:
                    x_data = data["x"]
                else:
                    # Create sequential x data
                    series_data = data["series"]
                    max_len = max(len(s.get("values", [])) for s in series_data)
                    x_data = list(range(max_len))
                    
                labels = [str(x) for x in x_data]
                series = data["series"]
            else:
                # Use dict items as x-y pairs
                items = list(data.items())
                
                # Sort by key for line chart
                items.sort(key=lambda x: x[0])
                
                x_data = [item[0] for item in items]
                y_data = [item[1] for item in items]
                labels = [str(x) for x in x_data]
                
        elif isinstance(data, (list, tuple)):
            # List data
            if all(isinstance(item, (int, float)) for item in data):
                # List of numbers - use indices as x
                x_data = list(range(len(data)))
                y_data = data
                labels = [str(i) for i in x_data]
            elif all(isinstance(item, (list, tuple)) for item in data) and all(len(item) >= 2 for item in data):
                # List of [x, y] pairs
                x_data = [item[0] for item in data]
                y_data = [item[1] for item in data]
                labels = [str(x) for x in x_data]
                
                # Check for additional series (3+ columns)
                if all(len(item) > 2 for item in data):
                    series_count = len(data[0]) - 1
                    for i in range(1, series_count):
                        series.append({
                            "name": f"Series {i}",
                            "values": [item[i+1] for item in data]
                        })
                    
        # Handle empty data
        if not x_data:
            # Generate dummy data
            x_data = list(range(5))
            y_data = [0] * 5
            labels = [str(i) for i in x_data]
            
        # If we have series but no y_data, use first series as y_data
        if series and not y_data and series[0]["values"]:
            y_data = series[0]["values"]
            
        return x_data, y_data, labels, series
    
    def _extract_insights_from_line_chart(
        self,
        x_data: List[Any],
        y_data: List[float],
        series: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        Extract insights from line chart data.
        
        Args:
            x_data: X-axis data
            y_data: Y-axis data
            series: Series data for multi-series charts
            
        Returns:
            Dict[str, Any]: Extracted insights
        """
        insights = {}
        
        # Process single series data
        if y_data:
            # Find maximum and minimum values
            max_idx = np.argmax(y_data)
            min_idx = np.argmin(y_data)
            
            insights["maximum"] = {
                "value": y_data[max_idx],
                "x": x_data[max_idx] if max_idx < len(x_data) else "Unknown"
            }
            
            insights["minimum"] = {
                "value": y_data[min_idx],
                "x": x_data[min_idx] if min_idx < len(x_data) else "Unknown"
            }
            
            # Calculate basic statistics
            insights["statistics"] = {
                "mean": np.mean(y_data),
                "median": np.median(y_data),
                "std_dev": np.std(y_data)
            }
            
            # Calculate trend (simple linear regression)
            if len(x_data) > 1 and all(isinstance(x, (int, float)) for x in x_data):
                # Use numeric x values
                x_numeric = x_data
            else:
                # Use indices
                x_numeric = list(range(len(x_data)))
                
            if len(x_numeric) == len(y_data) and len(x_numeric) > 1:
                x_arr = np.array(x_numeric)
                y_arr = np.array(y_data)
                
                # Linear regression: y = mx + b
                if len(x_arr) > 1:
                    m, b = np.polyfit(x_arr, y_arr, 1)
                    r_squared = np.corrcoef(x_arr, y_arr)[0, 1] ** 2
                    
                    insights["trend"] = {
                        "slope": m,
                        "intercept": b,
                        "r_squared": r_squared,
                        "direction": "increasing" if m > 0 else "decreasing" if m < 0 else "flat"
                    }
                    
            # Detect patterns
            patterns = []
            
            # Check for local extrema
            for i in range(1, len(y_data) - 1):
                if y_data[i] > y_data[i-1] and y_data[i] > y_data[i+1]:
                    patterns.append({
                        "type": "peak",
                        "position": i,
                        "x": x_data[i],
                        "value": y_data[i]
                    })
                elif y_data[i] < y_data[i-1] and y_data[i] < y_data[i+1]:
                    patterns.append({
                        "type": "valley",
                        "position": i,
                        "x": x_data[i],
                        "value": y_data[i]
                    })
                    
            if patterns:
                insights["patterns"] = patterns
                
        # Process multi-series data
        if series:
            multi_series_insights = []
            
            for s in series:
                series_values = s.get("values", [])
                
                if not series_values:
                    continue
                    
                # Calculate basic statistics
                series_insight = {
                    "name": s.get("name", "Unknown"),
                    "statistics": {
                        "mean": np.mean(series_values),
                        "median": np.median(series_values),
                        "std_dev": np.std(series_values),
                        "min": np.min(series_values),
                        "max": np.max(series_values)
                    }
                }
                
                multi_series_insights.append(series_insight)
                
            if multi_series_insights:
                insights["series_insights"] = multi_series_insights
                
                # Add correlation analysis if multiple series
                if len(series) > 1:
                    correlations = []
                    
                    for i in range(len(series)):
                        for j in range(i+1, len(series)):
                            series_i = series[i]["values"]
                            series_j = series[j]["values"]
                            
                            # Calculate correlation if lengths match
                            if len(series_i) == len(series_j) and len(series_i) > 1:
                                corr = np.corrcoef(series_i, series_j)[0, 1]
                                
                                correlations.append({
                                    "series1": series[i]["name"],
                                    "series2": series[j]["name"],
                                    "correlation": corr,
                                    "strength": "strong" if abs(corr) > 0.7 else "moderate" if abs(corr) > 0.3 else "weak"
                                })
                                
                    if correlations:
                        insights["correlations"] = correlations
                
        return insights
    
    def create_scatter_plot(
        self,
        data: Any,
        columns: Optional[List[str]] = None,
        title: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Create a scatter plot visualization.
        
        Args:
            data: Input data
            columns: Column names for x and y axes
            title: Chart title
            **kwargs: Additional parameters
            
        Returns:
            Dict[str, Any]: Visualization output
        """
        # Check if matplotlib is available
        if not _HAS_MATPLOTLIB:
            raise ImportError("Matplotlib is required for scatter plots")
            
        # Process parameters
        width = kwargs.get("width", self.config.get("default_width"))
        height = kwargs.get("height", self.config.get("default_height"))
        marker = kwargs.get("marker", "o")
        marker_size = kwargs.get("marker_size", self.config.get("marker_size", 6))
        alpha = kwargs.get("alpha", self.config.get("scatter_alpha", 0.7))
        show_grid = kwargs.get("show_grid", self.config.get("show_grid", True))
        show_trend = kwargs.get("show_trend", False)
        
        # Create figure and axes
        fig, ax = self.layout_manager.create_figure("regular", width=width, height=height)
        
        # Extract data
        x_data, y_data, labels, groups = self._extract_data_for_scatter_plot(data, columns)
        
        # Handle color mapping
        if "color_values" in kwargs:
            # Use provided color values for a continuous colormap
            color_values = kwargs["color_values"]
            cmap = self.color_manager.get_colormap(kwargs.get("colormap", self.config.get("color_map")))
            scatter = ax.scatter(x_data, y_data, s=marker_size, c=color_values, cmap=cmap, marker=marker, alpha=alpha)
            
            # Add colorbar
            if kwargs.get("show_colorbar", True):
                cbar = plt.colorbar(scatter, ax=ax)
                if "colorbar_label" in kwargs:
                    cbar.set_label(kwargs["colorbar_label"])
        elif groups:
            # Use categorical colors for groups
            unique_groups = sorted(set(groups))
            colors = kwargs.get("colors", self.color_manager.get_categorical_palette(len(unique_groups)))
            
            # Plot each group
            for i, group in enumerate(unique_groups):
                group_indices = [j for j, g in enumerate(groups) if g == group]
                x_group = [x_data[j] for j in group_indices]
                y_group = [y_data[j] for j in group_indices]
                
                ax.scatter(
                    x_group,
                    y_group,
                    s=marker_size,
                    c=[colors[i % len(colors)]],
                    marker=marker,
                    alpha=alpha,
                    label=group
                )
                
            # Add legend
            if self.config.get("show_legend", True):
                ax.legend(
                    loc=self.config.get("legend_loc", "best"),
                    fontsize=self.config.get("legend_font_size", 10)
                )
        else:
            # Single group scatter plot
            color = kwargs.get("color", self.color_manager.get_color("primary"))
            ax.scatter(x_data, y_data, s=marker_size, c=color, marker=marker, alpha=alpha)
            
        # Add trend line if requested
        if show_trend and len(x_data) > 1:
            try:
                # Linear regression
                m, b = np.polyfit(x_data, y_data, 1)
                x_line = np.array([min(x_data), max(x_data)])
                y_line = m * x_line + b
                
                # Plot trend line
                ax.plot(
                    x_line,
                    y_line,
                    '--',
                    color=kwargs.get("trend_line_color", self.color_manager.get_color("secondary")),
                    linewidth=kwargs.get("trend_line_width", 1.5),
                    alpha=0.8,
                    label=f"Trend: y = {m:.2f}x + {b:.2f}"
                )
                
                # Add trend line legend
                if self.config.get("show_legend", True):
                    handles, labels = ax.get_legend_handles_labels()
                    if handles:
                        ax.legend(
                            loc=self.config.get("legend_loc", "best"),
                            fontsize=self.config.get("legend_font_size", 10)
                        )
            except:
                # Skip trend line if fitting fails
                pass
                
        # Set labels
        ax.set_xlabel(kwargs.get("xlabel", columns[0] if columns and len(columns) > 0 else "X"))
        ax.set_ylabel(kwargs.get("ylabel", columns[1] if columns and len(columns) > 1 else "Y"))
        
        # Set title
        if title:
            ax.set_title(title, fontsize=self.config.get("title_font_size", 16))
            
        # Show grid if requested
        ax.grid(show_grid, linestyle="--", alpha=self.config.get("grid_alpha", 0.3))
        
        # Tight layout
        fig.tight_layout()
        
        # Extract insights
        insights = self._extract_insights_from_scatter_plot(x_data, y_data, groups)
        
        return {
            "figure": fig,
            "insights": insights
        }
    
    def _extract_data_for_scatter_plot(
        self,
        data: Any,
        columns: Optional[List[str]] = None
    ) -> Tuple[List[float], List[float], List[str], List[Any]]:
        """
        Extract data for a scatter plot from various input formats.
        
        Args:
            data: Input data
            columns: Column names
            
        Returns:
            Tuple[List[float], List[float], List[str], List[Any]]:
            x data, y data, labels, and group data
        """
        x_data = []
        y_data = []
        labels = []
        groups = []
        
        if _HAS_PANDAS and isinstance(data, pd.DataFrame):
            # DataFrame data
            if columns and len(columns) >= 2:
                # Use specified columns
                x_col, y_col = columns[0], columns[1]
                
                # Group column if specified
                group_col = columns[2] if len(columns) > 2 else None
                
                df = data[[x_col, y_col]].dropna()
                
                x_data = df[x_col].tolist()
                y_data = df[y_col].tolist()
                labels = [f"({x}, {y})" for x, y in zip(x_data, y_data)]
                
                # Extract group data if available
                if group_col and group_col in data.columns:
                    groups = data[group_col].iloc[df.index].tolist()
            else:
                # Try to find appropriate columns
                num_cols = [col for col in data.select_dtypes(include=["number"]).columns]
                
                if len(num_cols) >= 2:
                    # Use first two numeric columns
                    x_col, y_col = num_cols[0], num_cols[1]
                    
                    df = data[[x_col, y_col]].dropna()
                    
                    x_data = df[x_col].tolist()
                    y_data = df[y_col].tolist()
                    labels = [f"({x}, {y})" for x, y in zip(x_data, y_data)]
                    
                    # Check for potential group column
                    if len(num_cols) > 2:
                        # Use next numeric column as potential group
                        group_col = num_cols[2]
                        groups = data[group_col].iloc[df.index].tolist()
                    else:
                        # Check for categorical columns
                        cat_cols = [col for col in data.select_dtypes(include=["object", "category"]).columns]
                        if cat_cols:
                            group_col = cat_cols[0]
                            groups = data[group_col].iloc[df.index].tolist()
                    
        elif isinstance(data, dict):
            # Dictionary data
            if "x" in data and "y" in data:
                # Pre-defined x and y
                x_data = data["x"]
                y_data = data["y"]
                labels = [f"({x}, {y})" for x, y in zip(x_data, y_data)]
                
                # Group data if available
                if "groups" in data:
                    groups = data["groups"]
            elif all(isinstance(v, (list, tuple)) for v in data.values()):
                # Dictionary of series
                series_names = list(data.keys())
                
                if len(series_names) >= 2:
                    # Use first two series
                    x_col, y_col = series_names[0], series_names[1]
                    
                    x_data = data[x_col]
                    y_data = data[y_col]
                    labels = [f"({x}, {y})" for x, y in zip(x_data, y_data)]
                    
                    # Use third series as groups if available
                    if len(series_names) > 2:
                        groups = data[series_names[2]]
                        
        elif isinstance(data, (list, tuple)):
            # List data
            if all(isinstance(item, (list, tuple)) for item in data) and all(len(item) >= 2 for item in data):
                # List of [x, y] or [x, y, group] tuples
                x_data = [item[0] for item in data]
                y_data = [item[1] for item in data]
                labels = [f"({x}, {y})" for x, y in zip(x_data, y_data)]
                
                # Check for group data (third element)
                if all(len(item) > 2 for item in data):
                    groups = [item[2] for item in data]
                    
        # Handle empty data
        if not x_data or not y_data:
            # Generate dummy data
            x_data = [0]
            y_data = [0]
            labels = ["(0, 0)"]
            
        return x_data, y_data, labels, groups
    
    def _extract_insights_from_scatter_plot(
        self,
        x_data: List[float],
        y_data: List[float],
        groups: List[Any]
    ) -> Dict[str, Any]:
        """
        Extract insights from scatter plot data.
        
        Args:
            x_data: X-axis data
            y_data: Y-axis data
            groups: Optional grouping data
            
        Returns:
            Dict[str, Any]: Extracted insights
        """
        insights = {}
        
        if not x_data or not y_data or len(x_data) != len(y_data):
            return insights
            
        # Calculate correlation
        if len(x_data) > 1:
            correlation = np.corrcoef(x_data, y_data)[0, 1]
            
            insights["correlation"] = {
                "value": correlation,
                "type": "positive" if correlation > 0 else "negative" if correlation < 0 else "none",
                "strength": "strong" if abs(correlation) > 0.7 else 
                           "moderate" if abs(correlation) > 0.3 else "weak"
            }
            
            # Linear regression
            try:
                m, b = np.polyfit(x_data, y_data, 1)
                r_squared = correlation ** 2
                
                insights["regression"] = {
                    "slope": m,
                    "intercept": b,
                    "r_squared": r_squared,
                    "equation": f"y = {m:.4f}x + {b:.4f}"
                }
            except:
                # Skip regression if fitting fails
                pass
                
        # Calculate statistics
        insights["statistics"] = {
            "x": {
                "mean": np.mean(x_data),
                "median": np.median(x_data),
                "std_dev": np.std(x_data),
                "min": np.min(x_data),
                "max": np.max(x_data)
            },
            "y": {
                "mean": np.mean(y_data),
                "median": np.median(y_data),
                "std_dev": np.std(y_data),
                "min": np.min(y_data),
                "max": np.max(y_data)
            }
        }
        
        # Group analysis if groups are provided
        if groups and len(groups) == len(x_data):
            group_insights = {}
            unique_groups = sorted(set(groups))
            
            for group in unique_groups:
                # Get data for this group
                indices = [i for i, g in enumerate(groups) if g == group]
                group_x = [x_data[i] for i in indices]
                group_y = [y_data[i] for i in indices]
                
                # Skip if not enough data
                if len(group_x) < 2:
                    continue
                    
                # Calculate group statistics
                group_corr = np.corrcoef(group_x, group_y)[0, 1] if len(group_x) > 1 else 0
                
                group_insights[str(group)] = {
                    "count": len(group_x),
                    "correlation": group_corr,
                    "x_mean": np.mean(group_x),
                    "y_mean": np.mean(group_y),
                    "x_std": np.std(group_x),
                    "y_std": np.std(group_y)
                }
                
            if group_insights:
                insights["groups"] = group_insights
                
        # Outlier detection
        outliers = []
        
        if len(x_data) > 4:
            # Simple outlier detection using z-scores
            x_mean, x_std = np.mean(x_data), np.std(x_data)
            y_mean, y_std = np.mean(y_data), np.std(y_data)
            
            for i in range(len(x_data)):
                x_z_score = (x_data[i] - x_mean) / x_std if x_std > 0 else 0
                y_z_score = (y_data[i] - y_mean) / y_std if y_std > 0 else 0
                
                # Consider points with z-score > 3 as outliers
                if abs(x_z_score) > 3 or abs(y_z_score) > 3:
                    outliers.append({
                        "index": i,
                        "x": x_data[i],
                        "y": y_data[i],
                        "x_z_score": x_z_score,
                        "y_z_score": y_z_score
                    })
                    
            if outliers:
                insights["outliers"] = outliers
                
        return insights
    
    def create_pie_chart(
        self,
        data: Any,
        columns: Optional[List[str]] = None,
        title: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Create a pie chart visualization.
        
        Args:
            data: Input data
            columns: Column names for categories and values
            title: Chart title
            **kwargs: Additional parameters
            
        Returns:
            Dict[str, Any]: Visualization output
        """
        # Check if matplotlib is available
        if not _HAS_MATPLOTLIB:
            raise ImportError("Matplotlib is required for pie charts")
            
        # Process parameters
        width = kwargs.get("width", self.config.get("default_width"))
        height = kwargs.get("height", self.config.get("default_height"))
        start_angle = kwargs.get("start_angle", self.config.get("pie_start_angle", 90))
        show_labels = kwargs.get("show_labels", self.config.get("show_data_labels", True))
        show_percentages = kwargs.get("show_percentages", True)
        explode = kwargs.get("explode", None)
        
        # Create figure and axes
        fig, ax = self.layout_manager.create_figure("regular", width=width, height=height)
        
        # Extract data
        labels, values = self._extract_data_for_pie_chart(data, columns)
        
        # Generate colors
        colors = kwargs.get("colors", self.color_manager.get_categorical_palette(len(values)))
        
        # Default explode to None, only explode the largest slice if not specified
        if explode is None and kwargs.get("explode_largest", False):
            explode = [0] * len(values)
            if values:
                explode[np.argmax(values)] = 0.1
                
        # Format labels with percentages if requested
        if show_labels and show_percentages:
            total = sum(values) if sum(values) != 0 else 1
            pct_labels = [f"{l} ({v/total:.1%})" for l, v in zip(labels, values)]
        else:
            pct_labels = labels
            
        # Create pie chart
        wedges, texts, autotexts = ax.pie(
            values,
            labels=None if show_percentages else pct_labels,  # Don't show labels if showing percentages
            autopct='%1.1f%%' if show_percentages else None,
            startangle=start_angle,
            colors=colors,
            explode=explode,
            shadow=kwargs.get("shadow", False),
            wedgeprops=dict(width=kwargs.get("width_ratio", 1), edgecolor='w') if kwargs.get("use_donut", False) else None
        )
        
        # Add legend if labels should not be shown directly
        if show_labels and show_percentages:
            ax.legend(
                wedges,
                pct_labels,
                loc=kwargs.get("legend_loc", self.config.get("legend_loc", "center left")),
                bbox_to_anchor=kwargs.get("legend_position", (1, 0, 0.5, 1))
            )
            
        # Style auto-texts
        if show_percentages:
            for autotext in autotexts:
                autotext.set_color(kwargs.get("percent_text_color", "white"))
                autotext.set_fontsize(kwargs.get("percent_font_size", self.config.get("annotation_font_size", 11)))
                
        # Set aspect ratio to be equal (circular pie)
        ax.set_aspect("equal")
        
        # Set title
        if title:
            ax.set_title(title, fontsize=self.config.get("title_font_size", 16))
            
        # Tight layout
        fig.tight_layout()
        
        # Extract insights
        insights = self._extract_insights_from_pie_chart(labels, values)
        
        return {
            "figure": fig,
            "insights": insights
        }
    
    def _extract_data_for_pie_chart(
        self,
        data: Any,
        columns: Optional[List[str]] = None
    ) -> Tuple[List[str], List[float]]:
        """
        Extract data for a pie chart from various input formats.
        
        Args:
            data: Input data
            columns: Column names
            
        Returns:
            Tuple[List[str], List[float]]: labels and values
        """
        labels = []
        values = []
        
        if _HAS_PANDAS and isinstance(data, pd.DataFrame):
            # DataFrame data
            if columns and len(columns) >= 2:
                # Use specified columns
                cat_col, val_col = columns[0], columns[1]
                df = data[[cat_col, val_col]].dropna()
                
                # For too many categories, take top N
                max_categories = min(10, self.config.get("max_elements", 10))
                if len(df) > max_categories:
                    df = df.nlargest(max_categories, val_col)
                    
                labels = df[cat_col].tolist()
                values = df[val_col].tolist()
            else:
                # Use first categorical and first numeric column
                cat_cols = data.select_dtypes(include=["object", "category"]).columns
                num_cols = data.select_dtypes(include=["number"]).columns
                
                if len(cat_cols) > 0 and len(num_cols) > 0:
                    cat_col, val_col = cat_cols[0], num_cols[0]
                    df = data[[cat_col, val_col]].dropna()
                    
                    # For too many categories, take top N
                    max_categories = min(10, self.config.get("max_elements", 10))
                    if len(df) > max_categories:
                        df = df.nlargest(max_categories, val_col)
                        
                    labels = df[cat_col].tolist()
                    values = df[val_col].tolist()
                elif len(num_cols) > 0:
                    # Use column names as categories
                    val_col = num_cols[0]
                    labels = data.index.tolist()
                    values = data[val_col].tolist()
                    
        elif isinstance(data, dict):
            # Dictionary data
            if "labels" in data and "values" in data:
                # Pre-defined labels and values
                labels = data["labels"]
                values = data["values"]
            else:
                # Use dict items
                items = list(data.items())
                
                # Sort by value for better visualization
                items.sort(key=lambda x: x[1], reverse=True)
                
                # For too many items, take top N
                max_categories = min(10, self.config.get("max_elements", 10))
                if len(items) > max_categories:
                    items = items[:max_categories]
                    
                labels = [str(item[0]) for item in items]
                values = [item[1] for item in items]
                
        elif isinstance(data, (list, tuple)):
            # List data
            if all(isinstance(item, (int, float)) for item in data):
                # List of numbers
                labels = [f"Item {i+1}" for i in range(len(data))]
                values = data
            elif all(isinstance(item, (list, tuple)) for item in data) and all(len(item) >= 2 for item in data):
                # List of [label, value] pairs
                labels = [str(item[0]) for item in data]
                values = [item[1] for item in data]
                
        # Filter out non-positive values (they don't work well in pie charts)
        if values:
            valid_indices = [i for i, v in enumerate(values) if v > 0]
            labels = [labels[i] for i in valid_indices]
            values = [values[i] for i in valid_indices]
            
        # Handle empty data
        if not labels or not values:
            # Generate dummy data
            labels = ["No data"]
            values = [1]
            
        return labels, values
    
    def _extract_insights_from_pie_chart(
        self,
        labels: List[str],
        values: List[float]
    ) -> Dict[str, Any]:
        """
        Extract insights from pie chart data.
        
        Args:
            labels: Category labels
            values: Category values
            
        Returns:
            Dict[str, Any]: Extracted insights
        """
        insights = {}
        
        if not values:
            return insights
            
        # Calculate total
        total = sum(values)
        
        # Calculate percentages
        percentages = [value / total * 100 if total > 0 else 0 for value in values]
        
        # Create sorted data
        sorted_data = sorted(zip(labels, values, percentages), key=lambda x: x[1], reverse=True)
        
        # Extract top categories
        insights["top_categories"] = [
            {
                "label": label,
                "value": value,
                "percentage": percentage
            }
            for label, value, percentage in sorted_data[:5]  # Top 5
        ]
        
        # Calculate dominance (how much the top category dominates)
        if sorted_data:
            top_percentage = sorted_data[0][2]
            insights["dominance"] = {
                "top_category": sorted_data[0][0],
                "percentage": top_percentage,
                "type": "high" if top_percentage > 50 else "medium" if top_percentage > 33 else "low"
            }
            
        # Calculate diversity (how evenly distributed the values are)
        if len(values) > 1:
            # Normalize values to calculate entropy
            norm_values = [v / total for v in values] if total > 0 else [1 / len(values)] * len(values)
            
            # Calculate entropy
            entropy = -sum(p * np.log(p) if p > 0 else 0 for p in norm_values)
            
            # Max entropy for this number of categories
            max_entropy = np.log(len(values))
            
            # Normalized entropy (0 to 1)
            norm_entropy = entropy / max_entropy if max_entropy > 0 else 0
            
            insights["diversity"] = {
                "entropy": entropy,
                "normalized_entropy": norm_entropy,
                "type": "high" if norm_entropy > 0.75 else "medium" if norm_entropy > 0.4 else "low"
            }
            
        # Calculate interesting thresholds
        thresholds = []
        
        # Check for majority (>50%)
        majority_categories = [
            {"label": label, "percentage": pct}
            for label, _, pct in sorted_data if pct > 50
        ]
        
        if majority_categories:
            thresholds.append({
                "type": "majority",
                "categories": majority_categories,
                "description": f"{majority_categories[0]['label']} represents a majority ({majority_categories[0]['percentage']:.1f}%)."
            })
            
        # Check for "other" categories (small categories that could be grouped)
        small_categories = [
            {"label": label, "percentage": pct}
            for label, _, pct in sorted_data if pct < 5  # Less than 5%
        ]
        
        if small_categories:
            total_small = sum(cat["percentage"] for cat in small_categories)
            thresholds.append({
                "type": "small_categories",
                "count": len(small_categories),
                "total_percentage": total_small,
                "description": f"{len(small_categories)} small categories represent {total_small:.1f}% of the total."
            })
            
        if thresholds:
            insights["thresholds"] = thresholds
            
        return insights
    
    def create_donut_chart(
        self,
        data: Any,
        columns: Optional[List[str]] = None,
        title: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Create a donut chart visualization.
        
        Args:
            data: Input data
            columns: Column names for categories and values
            title: Chart title
            **kwargs: Additional parameters
            
        Returns:
            Dict[str, Any]: Visualization output
        """
        # Donut chart is just a pie chart with a hole
        kwargs["use_donut"] = True
        kwargs["width_ratio"] = kwargs.get("donut_ratio", self.config.get("donut_ratio", 0.4))
        
        # Center text if provided
        center_text = kwargs.get("center_text", None)
        
        # Create pie chart
        result = self.create_pie_chart(data, columns, title, **kwargs)
        
        # Add center text if provided
        if center_text and "figure" in result:
            fig = result["figure"]
            ax = fig.gca()
            
            # Add text in the center
            ax.text(0, 0, center_text,
                    ha='center',
                    va='center',
                    fontsize=kwargs.get("center_text_size", self.config.get("title_font_size", 16)),
                    fontweight='bold')
                    
            # Re-render figure
            result["figure"] = fig
            
        return result
    
    def create_area_chart(
        self,
        data: Any,
        columns: Optional[List[str]] = None,
        title: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Create an area chart visualization.
        
        Args:
            data: Input data
            columns: Column names for x and y axes
            title: Chart title
            **kwargs: Additional parameters
            
        Returns:
            Dict[str, Any]: Visualization output
        """
        # Check if matplotlib is available
        if not _HAS_MATPLOTLIB:
            raise ImportError("Matplotlib is required for area charts")
            
        # Process parameters
        width = kwargs.get("width", self.config.get("default_width"))
        height = kwargs.get("height", self.config.get("default_height"))
        alpha = kwargs.get("alpha", 0.5)
        show_grid = kwargs.get("show_grid", self.config.get("show_grid", True))
        stacked = kwargs.get("stacked", False)
        
        # Create figure and axes
        fig, ax = self.layout_manager.create_figure("regular", width=width, height=height)
        
        # Extract data (similar to line chart)
        x_data, y_data, labels, series = self._extract_data_for_line_chart(data, columns)
        
        # Get colors
        if series:
            # Multiple series
            colors = kwargs.get("colors", self.color_manager.get_categorical_palette(len(series)))
            
            if stacked:
                # Stacked area chart
                y_stack = np.row_stack([serie["values"] for serie in series])
                labels_stack = [serie["name"] for serie in series]
                
                ax.stackplot(
                    x_data,
                    y_stack,
                    labels=labels_stack,
                    colors=colors,
                    alpha=alpha
                )
            else:
                # Multiple area charts
                for i, serie in enumerate(series):
                    ax.fill_between(
                        x_data,
                        serie["values"],
                        alpha=alpha,
                        color=colors[i % len(colors)],
                        label=serie["name"]
                    )
                    
            # Add legend
            if self.config.get("show_legend", True):
                ax.legend(
                    loc=self.config.get("legend_loc", "best"),
                    fontsize=self.config.get("legend_font_size", 10)
                )
        else:
            # Single series
            color = kwargs.get("color", self.color_manager.get_color("primary"))
            ax.fill_between(
                x_data,
                y_data,
                alpha=alpha,
                color=color,
                label=kwargs.get("label", columns[1] if columns and len(columns) > 1 else "Values")
            )
            
            # Add legend if label is provided
            if kwargs.get("label") and self.config.get("show_legend", True):
                ax.legend(
                    loc=self.config.get("legend_loc", "best"),
                    fontsize=self.config.get("legend_font_size", 10)
                )
                
        # Set labels
        ax.set_xlabel(kwargs.get("xlabel", columns[0] if columns and len(columns) > 0 else "X"))
        ax.set_ylabel(kwargs.get("ylabel", columns[1] if columns and len(columns) > 1 else "Y"))
        
        # Set title
        if title:
            ax.set_title(title, fontsize=self.config.get("title_font_size", 16))
            
        # Rotate x-axis labels if datetime or many categories
        if isinstance(x_data[0], str) and len(x_data) > 5:
            plt.xticks(rotation=45, ha="right")
            
        # Show grid if requested
        ax.grid(show_grid, linestyle="--", alpha=self.config.get("grid_alpha", 0.3))
        
        # Tight layout
        fig.tight_layout()
        
        # Extract insights (reuse line chart insights)
        insights = self._extract_insights_from_line_chart(x_data, y_data, series)
        
        return {
            "figure": fig,
            "insights": insights
        }
    
    def create_histogram(
        self,
        data: Any,
        columns: Optional[List[str]] = None,
        title: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Create a histogram visualization.
        
        Args:
            data: Input data
            columns: Column names for values
            title: Chart title
            **kwargs: Additional parameters
            
        Returns:
            Dict[str, Any]: Visualization output
        """
        # Check if matplotlib is available
        if not _HAS_MATPLOTLIB:
            raise ImportError("Matplotlib is required for histograms")
            
        # Process parameters
        width = kwargs.get("width", self.config.get("default_width"))
        height = kwargs.get("height", self.config.get("default_height"))
        bins = kwargs.get("bins", "auto")
        color = kwargs.get("color", self.color_manager.get_color("primary"))
        edge_color = kwargs.get("edge_color", self.color_manager.get_color("text"))
        alpha = kwargs.get("alpha", 0.7)
        show_grid = kwargs.get("show_grid", self.config.get("show_grid", True))
        show_kde = kwargs.get("show_kde", False)
        
        # Create figure and axes
        fig, ax = self.layout_manager.create_figure("regular", width=width, height=height)
        
        # Extract data
        values = self._extract_data_for_histogram(data, columns)
        
        # Create histogram
        n, bins_edges, patches = ax.hist(
            values,
            bins=bins,
            color=color,
            edgecolor=edge_color,
            alpha=alpha,
            density=show_kde  # Normalize if showing KDE
        )
        
        # Add kernel density estimate if requested
        if show_kde and len(values) > 1:
            try:
                # Compute kernel density estimate
                from scipy import stats
                kde_x = np.linspace(min(values), max(values), 100)
                kde = stats.gaussian_kde(values)
                kde_y = kde(kde_x)
                
                # Plot KDE
                ax.plot(
                    kde_x,
                    kde_y,
                    color=kwargs.get("kde_color", self.color_manager.get_color("secondary")),
                    linewidth=kwargs.get("kde_linewidth", 2),
                    label="Density"
                )
                
                # Add legend
                if self.config.get("show_legend", True):
                    ax.legend(
                        loc=self.config.get("legend_loc", "best"),
                        fontsize=self.config.get("legend_font_size", 10)
                    )
            except:
                # Skip KDE if it fails
                pass
                
        # Set labels
        ax.set_xlabel(kwargs.get("xlabel", columns[0] if columns and len(columns) > 0 else "Values"))
        ax.set_ylabel(kwargs.get("ylabel", "Frequency"))
        
        # Set title
        if title:
            ax.set_title(title, fontsize=self.config.get("title_font_size", 16))
            
        # Show grid if requested
        ax.grid(show_grid, linestyle="--", alpha=self.config.get("grid_alpha", 0.3))
        
        # Tight layout
        fig.tight_layout()
        
        # Extract insights
        insights = self._extract_insights_from_histogram(values, n, bins_edges)
        
        return {
            "figure": fig,
            "insights": insights
        }
    
    def _extract_data_for_histogram(
        self,
        data: Any,
        columns: Optional[List[str]] = None
    ) -> List[float]:
        """
        Extract data for a histogram from various input formats.
        
        Args:
            data: Input data
            columns: Column names
            
        Returns:
            List[float]: Values for histogram
        """
        values = []
        
        if _HAS_PANDAS and isinstance(data, pd.DataFrame):
            # DataFrame data
            if columns and len(columns) > 0:
                # Use specified column
                col = columns[0]
                if col in data.columns:
                    values = data[col].dropna().tolist()
            else:
                # Use first numeric column
                num_cols = data.select_dtypes(include=["number"]).columns
                if len(num_cols) > 0:
                    values = data[num_cols[0]].dropna().tolist()
                    
        elif isinstance(data, dict):
            # Dictionary data
            if "values" in data:
                # Pre-defined values
                values = data["values"]
            elif "data" in data:
                # Alternative key
                values = data["data"]
            elif all(isinstance(v, (int, float)) for v in data.values()):
                # Use dictionary values
                values = list(data.values())
                
        elif isinstance(data, (list, tuple)):
            # List data
            if all(isinstance(item, (int, float)) for item in data):
                # List of numbers
                values = data
            elif all(isinstance(item, (list, tuple)) for item in data) and len(data[0]) > 0:
                # List of lists/tuples - use first column
                values = [item[0] for item in data if isinstance(item[0], (int, float))]
                
        # Handle empty data
        if not values:
            # Generate dummy data
            values = np.random.normal(0, 1, 100)
            
        return values
    
    def _extract_insights_from_histogram(
        self,
        values: List[float],
        counts: np.ndarray,
        bin_edges: np.ndarray
    ) -> Dict[str, Any]:
        """
        Extract insights from histogram data.
        
        Args:
            values: Input values
            counts: Bin counts
            bin_edges: Bin edges
            
        Returns:
            Dict[str, Any]: Extracted insights
        """
        insights = {}
        
        if not values:
            return insights
            
        # Basic statistics
        insights["statistics"] = {
            "mean": np.mean(values),
            "median": np.median(values),
            "std_dev": np.std(values),
            "min": np.min(values),
            "max": np.max(values),
            "count": len(values),
            "range": np.max(values) - np.min(values)
        }
        
        # Calculate percentiles
        percentiles = [10, 25, 50, 75, 90]
        insights["percentiles"] = {
            f"p{p}": np.percentile(values, p) for p in percentiles
        }
        
        # Identify mode (most frequent value)
        # For continuous data, we use the midpoint of the bin with highest count
        bin_indices = np.argmax(counts)
        mode_value = (bin_edges[bin_indices] + bin_edges[bin_indices + 1]) / 2
        
        insights["mode"] = {
            "value": mode_value,
            "count": counts[bin_indices]
        }
        
        # Distribution characteristics
        skewness = float(np.mean(((values - np.mean(values)) / np.std(values))**3)) if np.std(values) > 0 else 0
        kurtosis = float(np.mean(((values - np.mean(values)) / np.std(values))**4)) - 3 if np.std(values) > 0 else 0
        
        insights["distribution"] = {
            "skewness": skewness,
            "kurtosis": kurtosis,
            "shape": "Normal" if abs(skewness) < 0.5 and abs(kurtosis) < 0.5 else 
                    "Right-skewed" if skewness > 0.5 else
                    "Left-skewed" if skewness < -0.5 else
                    "Platykurtic" if kurtosis < -0.5 else
                    "Leptokurtic" if kurtosis > 0.5 else
                    "Irregular"
        }
        
        # Check for outliers using IQR method
        q1, q3 = np.percentile(values, [25, 75])
        iqr = q3 - q1
        lower_bound = q1 - 1.5 * iqr
        upper_bound = q3 + 1.5 * iqr
        
        outliers = [v for v in values if v < lower_bound or v > upper_bound]
        
        if outliers:
            insights["outliers"] = {
                "count": len(outliers),
                "percentage": len(outliers) / len(values) * 100,
                "min": min(outliers),
                "max": max(outliers)
            }
            
        return insights
    
    def create_boxplot(
        self,
        data: Any,
        columns: Optional[List[str]] = None,
        title: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Create a boxplot visualization.
        
        Args:
            data: Input data
            columns: Column names
            title: Chart title
            **kwargs: Additional parameters
            
        Returns:
            Dict[str, Any]: Visualization output
        """
        # Check if matplotlib is available
        if not _HAS_MATPLOTLIB:
            raise ImportError("Matplotlib is required for boxplots")
            
        # Process parameters
        width = kwargs.get("width", self.config.get("default_width"))
        height = kwargs.get("height", self.config.get("default_height"))
        orientation = kwargs.get("orientation", "vertical")
        patch_color = kwargs.get("patch_color", self.color_manager.get_color("primary"))
        median_color = kwargs.get("median_color", self.color_manager.get_color("secondary"))
        show_grid = kwargs.get("show_grid", self.config.get("show_grid", True))
        show_outliers = kwargs.get("show_outliers", True)
        
        # Create figure and axes
        fig, ax = self.layout_manager.create_figure("regular", width=width, height=height)
        
        # Extract data
        categories, values_by_category = self._extract_data_for_boxplot(data, columns)
        
        # Get colors
        if len(categories) > 1:
            patch_colors = kwargs.get("colors", self.color_manager.get_categorical_palette(len(categories)))
        else:
            patch_colors = [patch_color]
            
        # Set boxplot properties
        boxplot_props = {
            'boxprops': {'facecolor': patch_color, 'edgecolor': self.color_manager.get_color("text")},
            'medianprops': {'color': median_color, 'linewidth': 2},
            'whiskerprops': {'color': self.color_manager.get_color("text")},
            'capprops': {'color': self.color_manager.get_color("text")},
            'flierprops': {'markerfacecolor': self.color_manager.get_color("tertiary"), 
                          'markeredgecolor': self.color_manager.get_color("text"), 
                          'markersize': 6},
            'showfliers': show_outliers
        }
        
        # If multiple categories, set different colors for each box
        if len(categories) > 1:
            boxplot = ax.boxplot(values_by_category, 
                               patch_artist=True, 
                               vert=(orientation == "vertical"),
                               showfliers=show_outliers,
                               widths=kwargs.get("box_width", 0.5))
            
            # Set color for each box
            for patch, color in zip(boxplot['boxes'], patch_colors):
                patch.set_facecolor(color)
                patch.set_alpha(0.7)
        else:
            # Single category boxplot
            boxplot = ax.boxplot(values_by_category, 
                               patch_artist=True, 
                               vert=(orientation == "vertical"),
                               showfliers=show_outliers,
                               widths=kwargs.get("box_width", 0.5),
                               **boxplot_props)
            
        # Set labels
        if orientation == "vertical":
            ax.set_xticklabels(categories)
            ax.set_xlabel(kwargs.get("xlabel", columns[0] if columns and len(columns) > 0 else "Categories"))
            ax.set_ylabel(kwargs.get("ylabel", "Values"))
        else:
            ax.set_yticklabels(categories)
            ax.set_ylabel(kwargs.get("xlabel", columns[0] if columns and len(columns) > 0 else "Categories"))
            ax.set_xlabel(kwargs.get("ylabel", "Values"))
            
        # Set title
        if title:
            ax.set_title(title, fontsize=self.config.get("title_font_size", 16))
            
        # Show grid if requested
        ax.grid(show_grid, linestyle="--", alpha=self.config.get("grid_alpha", 0.3))
        
        # Tight layout
        fig.tight_layout()
        
        # Extract insights
        insights = self._extract_insights_from_boxplot(categories, values_by_category)
        
        return {
            "figure": fig,
            "insights": insights
        }
    
    def _extract_data_for_boxplot(
        self,
        data: Any,
        columns: Optional[List[str]] = None
    ) -> Tuple[List[str], List[List[float]]]:
        """
        Extract data for a boxplot from various input formats.
        
        Args:
            data: Input data
            columns: Column names
            
        Returns:
            Tuple[List[str], List[List[float]]]: Categories and values by category
        """
        categories = []
        values_by_category = []
        
        if _HAS_PANDAS and isinstance(data, pd.DataFrame):
            # DataFrame data
            if columns and len(columns) >= 1:
                if len(columns) == 1:
                    # Single column: use df.index as categories
                    values_col = columns[0]
                    categories = ["Values"]
                    values_by_category = [data[values_col].dropna().tolist()]
                elif len(columns) == 2:
                    # Two columns: first is category, second is values
                    cat_col, values_col = columns[0], columns[1]
                    
                    # Group by category column
                    grouped = data.groupby(cat_col)[values_col].apply(lambda x: x.dropna().tolist()).to_dict()
                    
                    categories = list(grouped.keys())
                    values_by_category = [grouped[cat] for cat in categories]
                else:
                    # Multiple columns: each is a separate category
                    categories = columns
                    values_by_category = [data[col].dropna().tolist() for col in columns]
            else:
                # Try to detect data structure automatically
                cat_cols = data.select_dtypes(include=["object", "category"]).columns
                num_cols = data.select_dtypes(include=["number"]).columns
                
                if len(cat_cols) > 0 and len(num_cols) > 0:
                    # Use first categorical column as grouping variable and first numeric column as values
                    cat_col, val_col = cat_cols[0], num_cols[0]
                    grouped = data.groupby(cat_col)[val_col].apply(lambda x: x.dropna().tolist()).to_dict()
                    
                    categories = list(grouped.keys())
                    values_by_category = [grouped[cat] for cat in categories]
                elif len(num_cols) > 0:
                    # Use all numeric columns
                    categories = num_cols.tolist()
                    values_by_category = [data[col].dropna().tolist() for col in categories]
                
        elif isinstance(data, dict):
            # Dictionary data
            if "categories" in data and "values" in data:
                # Pre-defined categories and values
                if isinstance(data["values"], list) and all(isinstance(v, list) for v in data["values"]):
                    # Multiple category values
                    categories = data["categories"]
                    values_by_category = data["values"]
                else:
                    # Single category values
                    categories = data["categories"]
                    values_by_category = [[data["values"][i]] for i in range(len(categories))]
            elif all(isinstance(v, list) for v in data.values()):
                # Dictionary of lists
                categories = list(data.keys())
                values_by_category = list(data.values())
            elif all(isinstance(v, (int, float)) for v in data.values()):
                # Dictionary of values
                categories = ["Values"]
                values_by_category = [list(data.values())]
                
        elif isinstance(data, (list, tuple)):
            # List data
            if all(isinstance(item, (int, float)) for item in data):
                # List of values for a single boxplot
                categories = ["Values"]
                values_by_category = [data]
            elif all(isinstance(item, (list, tuple)) for item in data):
                if all(isinstance(subitem, (int, float)) for item in data for subitem in item):
                    # List of lists - each inner list is a separate category
                    categories = [f"Category {i+1}" for i in range(len(data))]
                    values_by_category = data
                    
        # Handle empty data
        if not categories or not values_by_category:
            # Generate dummy data
            categories = ["No data"]
            values_by_category = [np.random.normal(0, 1, 10)]
            
        return categories, values_by_category
    
    def _extract_insights_from_boxplot(
        self,
        categories: List[str],
        values_by_category: List[List[float]]
    ) -> Dict[str, Any]:
        """
        Extract insights from boxplot data.
        
        Args:
            categories: Category names
            values_by_category: Values for each category
            
        Returns:
            Dict[str, Any]: Extracted insights
        """
        insights = {}
        
        if not categories or not values_by_category:
            return insights
            
        # Calculate statistics for each category
        category_stats = []
        
        for i, (category, values) in enumerate(zip(categories, values_by_category)):
            if not values:
                continue
                
            # Calculate quartiles
            q1, median, q3 = np.percentile(values, [25, 50, 75])
            
            # Calculate IQR
            iqr = q3 - q1
            
            # Define outlier boundaries
            lower_bound = q1 - 1.5 * iqr
            upper_bound = q3 + 1.5 * iqr
            
            # Identify outliers
            outliers = [v for v in values if v < lower_bound or v > upper_bound]
            
            # Calculate statistics
            stats = {
                "category": category,
                "min": np.min(values),
                "max": np.max(values),
                "q1": q1,
                "median": median,
                "q3": q3,
                "iqr": iqr,
                "mean": np.mean(values),
                "std_dev": np.std(values),
                "count": len(values),
                "outliers": {
                    "count": len(outliers),
                    "percentage": len(outliers) / len(values) * 100 if values else 0,
                    "values": outliers if len(outliers) <= 10 else outliers[:10]  # Limit number of outliers reported
                }
            }
            
            category_stats.append(stats)
            
        insights["category_statistics"] = category_stats
        
        # Compare categories if more than one
        if len(category_stats) > 1:
            # Find category with highest/lowest median
            highest_median = max(category_stats, key=lambda x: x["median"])
            lowest_median = min(category_stats, key=lambda x: x["median"])
            
            # Find category with highest/lowest variability (IQR)
            highest_variability = max(category_stats, key=lambda x: x["iqr"])
            lowest_variability = min(category_stats, key=lambda x: x["iqr"])
            
            insights["comparisons"] = {
                "highest_median": {
                    "category": highest_median["category"],
                    "value": highest_median["median"]
                },
                "lowest_median": {
                    "category": lowest_median["category"],
                    "value": lowest_median["median"]
                },
                "highest_variability": {
                    "category": highest_variability["category"],
                    "value": highest_variability["iqr"]
                },
                "lowest_variability": {
                    "category": lowest_variability["category"],
                    "value": lowest_variability["iqr"]
                }
            }
            
            # Calculate the difference between highest and lowest median
            median_diff = highest_median["median"] - lowest_median["median"]
            median_ratio = highest_median["median"] / lowest_median["median"] if lowest_median["median"] != 0 else float('inf')
            
            insights["comparisons"]["median_difference"] = {
                "absolute": median_diff,
                "ratio": median_ratio
            }
            
            # Check for overlap between boxes
            overlap_insights = []
            
            for i in range(len(category_stats)):
                for j in range(i+1, len(category_stats)):
                    cat_i = category_stats[i]
                    cat_j = category_stats[j]
                    
                    # Check if boxes overlap
                    overlap = not (cat_i["q3"] < cat_j["q1"] or cat_j["q3"] < cat_i["q1"])
                    
                    if overlap:
                        overlap_insights.append({
                            "category1": cat_i["category"],
                            "category2": cat_j["category"],
                            "overlap": "yes",
                            "overlap_range": [max(cat_i["q1"], cat_j["q1"]), min(cat_i["q3"], cat_j["q3"])]
                        })
                    else:
                        overlap_insights.append({
                            "category1": cat_i["category"],
                            "category2": cat_j["category"],
                            "overlap": "no",
                            "gap": min(cat_i["q1"], cat_j["q1"]) - max(cat_i["q3"], cat_j["q3"])
                        })
                        
            if overlap_insights:
                insights["comparisons"]["overlaps"] = overlap_insights
                
        return insights
    
    def create_heatmap(
        self,
        data: Any,
        columns: Optional[List[str]] = None,
        title: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Create a heatmap visualization.
        
        Args:
            data: Input data
            columns: Column names
            title: Chart title
            **kwargs: Additional parameters
            
        Returns:
            Dict[str, Any]: Visualization output
        """
        # Check if matplotlib is available
        if not _HAS_MATPLOTLIB:
            raise ImportError("Matplotlib is required for heatmaps")
            
        # Process parameters
        width = kwargs.get("width", self.config.get("default_width"))
        height = kwargs.get("height", self.config.get("default_height"))
        cmap = kwargs.get("cmap", self.color_manager.get_colormap())
        show_values = kwargs.get("show_values", self.config.get("show_data_labels", True))
        value_format = kwargs.get("value_format", "{:.1f}")
        
        # Create figure and axes
        fig, ax = self.layout_manager.create_figure("regular", width=width, height=height)
        
        # Extract data
        matrix, row_labels, col_labels = self._extract_data_for_heatmap(data, columns)
        
        # Create heatmap
        im = ax.imshow(matrix, cmap=cmap)
        
        # Add colorbar
        if kwargs.get("show_colorbar", True):
            cbar = plt.colorbar(im, ax=ax)
            if "colorbar_label" in kwargs:
                cbar.set_label(kwargs["colorbar_label"])
                
        # Set ticks and labels
        ax.set_xticks(np.arange(len(col_labels)))
        ax.set_yticks(np.arange(len(row_labels)))
        ax.set_xticklabels(col_labels)
        ax.set_yticklabels(row_labels)
        
        # Rotate x-axis labels if needed
        if kwargs.get("rotate_xlabels", True):
            plt.setp(ax.get_xticklabels(), rotation=45, ha="right", rotation_mode="anchor")
            
        # Add value annotations
        if show_values:
            threshold = im.norm(np.max(matrix)) / 2
            
            for i in range(len(row_labels)):
                for j in range(len(col_labels)):
                    value = matrix[i, j]
                    text_color = "white" if im.norm(value) > threshold else "black"
                    
                    ax.text(j, i, value_format.format(value),
                            ha="center", va="center",
                            color=text_color,
                            fontsize=kwargs.get("value_font_size", self.config.get("annotation_font_size", 10)))
                            
        # Set axis labels
        ax.set_xlabel(kwargs.get("xlabel", "Columns"))
        ax.set_ylabel(kwargs.get("ylabel", "Rows"))
        
        # Set title
        if title:
            ax.set_title(title, fontsize=self.config.get("title_font_size", 16))
            
        # Tight layout
        fig.tight_layout()
        
        # Extract insights
        insights = self._extract_insights_from_heatmap(matrix, row_labels, col_labels)
        
        return {
            "figure": fig,
            "insights": insights
        }
    
    def _extract_data_for_heatmap(
        self,
        data: Any,
        columns: Optional[List[str]] = None
    ) -> Tuple[np.ndarray, List[str], List[str]]:
        """
        Extract data for a heatmap from various input formats.
        
        Args:
            data: Input data
            columns: Column names
            
        Returns:
            Tuple[np.ndarray, List[str], List[str]]: Matrix data, row labels, and column labels
        """
        matrix = None
        row_labels = []
        col_labels = []
        
        if _HAS_PANDAS and isinstance(data, pd.DataFrame):
            # DataFrame data
            if columns and len(columns) >= 2:
                # Use only specified columns
                df = data[columns].copy()
            else:
                # Use all columns
                df = data.copy()
                
            # Get pivot table if one categorical and one numeric column
            cat_cols = df.select_dtypes(include=["object", "category"]).columns
            num_cols = df.select_dtypes(include=["number"]).columns
            
            if len(cat_cols) == 2 and len(num_cols) >= 1:
                # Two categorical columns and at least one numeric: create pivot table
                pivot = pd.pivot_table(
                    df, 
                    values=num_cols[0], 
                    index=cat_cols[0], 
                    columns=cat_cols[1],
                    aggfunc=kwargs.get("aggfunc", "mean")
                )
                
                matrix = pivot.values
                row_labels = list(pivot.index)
                col_labels = list(pivot.columns)
            else:
                # Use entire DataFrame as matrix
                matrix = df.values
                row_labels = [str(i) for i in df.index]
                col_labels = [str(c) for c in df.columns]
                
        elif isinstance(data, dict):
            # Dictionary data
            if "matrix" in data:
                # Pre-defined matrix
                matrix = np.array(data["matrix"])
                row_labels = data.get("row_labels", [str(i) for i in range(matrix.shape[0])])
                col_labels = data.get("col_labels", [str(i) for i in range(matrix.shape[1])])
            elif "data" in data:
                # Alternative key
                matrix = np.array(data["data"])
                row_labels = data.get("row_labels", [str(i) for i in range(matrix.shape[0])])
                col_labels = data.get("col_labels", [str(i) for i in range(matrix.shape[1])])
                
        elif isinstance(data, (list, tuple)):
            # List data
            if all(isinstance(row, (list, tuple)) for row in data):
                # List of lists/arrays
                matrix = np.array(data)
                row_labels = [str(i) for i in range(matrix.shape[0])]
                col_labels = [str(i) for i in range(matrix.shape[1])]
                
        # Handle empty data
        if matrix is None:
            # Generate dummy data
            matrix = np.random.rand(5, 5)
            row_labels = [f"Row {i+1}" for i in range(5)]
            col_labels = [f"Col {i+1}" for i in range(5)]
            
        return matrix, row_labels, col_labels
    
    def _extract_insights_from_heatmap(
        self,
        matrix: np.ndarray,
        row_labels: List[str],
        col_labels: List[str]
    ) -> Dict[str, Any]:
        """
        Extract insights from heatmap data.
        
        Args:
            matrix: Matrix data
            row_labels: Row labels
            col_labels: Column labels
            
        Returns:
            Dict[str, Any]: Extracted insights
        """
        insights = {}
        
        if matrix.size == 0:
            return insights
            
        # Find global min and max
        min_value = np.min(matrix)
        max_value = np.max(matrix)
        
        min_index = np.unravel_index(np.argmin(matrix), matrix.shape)
        max_index = np.unravel_index(np.argmax(matrix), matrix.shape)
        
        insights["global_min"] = {
            "value": min_value,
            "row": row_labels[min_index[0]] if min_index[0] < len(row_labels) else f"Row {min_index[0]}",
            "column": col_labels[min_index[1]] if min_index[1] < len(col_labels) else f"Col {min_index[1]}"
        }
        
        insights["global_max"] = {
            "value": max_value,
            "row": row_labels[max_index[0]] if max_index[0] < len(row_labels) else f"Row {max_index[0]}",
            "column": col_labels[max_index[1]] if max_index[1] < len(col_labels) else f"Col {max_index[1]}"
        }
        
        # Calculate row and column statistics
        row_statistics = []
        for i, row_label in enumerate(row_labels):
            row_data = matrix[i, :]
            row_statistics.append({
                "row": row_label,
                "mean": np.mean(row_data),
                "min": np.min(row_data),
                "max": np.max(row_data),
                "std_dev": np.std(row_data)
            })
            
        col_statistics = []
        for j, col_label in enumerate(col_labels):
            col_data = matrix[:, j]
            col_statistics.append({
                "column": col_label,
                "mean": np.mean(col_data),
                "min": np.min(col_data),
                "max": np.max(col_data),
                "std_dev": np.std(col_data)
            })
            
        # Sort by mean to find highest/lowest
        row_statistics.sort(key=lambda x: x["mean"], reverse=True)
        col_statistics.sort(key=lambda x: x["mean"], reverse=True)
        
        insights["row_statistics"] = row_statistics
        insights["column_statistics"] = col_statistics
        
        insights["top_rows"] = row_statistics[:3] if len(row_statistics) >= 3 else row_statistics
        insights["top_columns"] = col_statistics[:3] if len(col_statistics) >= 3 else col_statistics
        
        # Check for patterns (e.g., diagonal dominance)
        if matrix.shape[0] == matrix.shape[1]:
            # Square matrix - check diagonal
            diagonal = np.diag(matrix)
            diagonal_mean = np.mean(diagonal)
            off_diagonal_mean = (np.sum(matrix) - np.sum(diagonal)) / (matrix.size - len(diagonal))
            
            diagonal_dominance = diagonal_mean / off_diagonal_mean if off_diagonal_mean != 0 else float('inf')
            
            insights["diagonal_analysis"] = {
                "diagonal_mean": diagonal_mean,
                "off_diagonal_mean": off_diagonal_mean,
                "diagonal_dominance": diagonal_dominance,
                "is_diagonal_dominant": diagonal_dominance > 2
            }
            
        # Calculate correlation between rows (if multiple rows)
        if matrix.shape[0] > 1:
            # Compute row correlations
            row_correlations = np.corrcoef(matrix)
            
            # Find strongest positive and negative correlations between rows
            strong_correlations = []
            
            for i in range(row_correlations.shape[0]):
                for j in range(i+1, row_correlations.shape[1]):
                    corr_value = row_correlations[i, j]
                    
                    if abs(corr_value) > 0.7:  # Strong correlation threshold
                        strong_correlations.append({
                            "row1": row_labels[i] if i < len(row_labels) else f"Row {i}",
                            "row2": row_labels[j] if j < len(row_labels) else f"Row {j}",
                            "correlation": corr_value,
                            "type": "positive" if corr_value > 0 else "negative"
                        })
                        
            if strong_correlations:
                insights["strong_row_correlations"] = strong_correlations
                
        return insights
    
    def create_correlation_matrix(
        self,
        data: Any,
        columns: Optional[List[str]] = None,
        title: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Create a correlation matrix visualization.
        
        Args:
            data: Input data
            columns: Column names
            title: Chart title
            **kwargs: Additional parameters
            
        Returns:
            Dict[str, Any]: Visualization output
        """
        # Check if matplotlib and numpy are available
        if not _HAS_MATPLOTLIB:
            raise ImportError("Matplotlib is required for correlation matrices")
            
        # Process parameters
        width = kwargs.get("width", self.config.get("default_width"))
        height = kwargs.get("height", self.config.get("default_height"))
        cmap = kwargs.get("cmap", self.color_manager.get_colormap("RdBu_r"))
        show_values = kwargs.get("show_values", self.config.get("show_data_labels", True))
        value_format = kwargs.get("value_format", "{:.2f}")
        
        # Create figure and axes
        fig, ax = self.layout_manager.create_figure("regular", width=width, height=height)
        
        # Extract data
        corr_matrix, corr_labels = self._extract_data_for_correlation_matrix(data, columns)
        
        # Create heatmap with fixed range (-1 to 1)
        im = ax.imshow(corr_matrix, cmap=cmap, vmin=-1, vmax=1)
        
        # Add colorbar
        if kwargs.get("show_colorbar", True):
            cbar = plt.colorbar(im, ax=ax)
            cbar.set_label(kwargs.get("colorbar_label", "Correlation"))
            
        # Set ticks and labels
        ax.set_xticks(np.arange(len(corr_labels)))
        ax.set_yticks(np.arange(len(corr_labels)))
        ax.set_xticklabels(corr_labels)
        ax.set_yticklabels(corr_labels)
        
        # Rotate x-axis labels if needed
        if kwargs.get("rotate_xlabels", True):
            plt.setp(ax.get_xticklabels(), rotation=45, ha="right", rotation_mode="anchor")
            
        # Add value annotations
        if show_values:
            for i in range(len(corr_labels)):
                for j in range(len(corr_labels)):
                    value = corr_matrix[i, j]
                    # Use white text for negative correlations, black for positive
                    text_color = "white" if value < 0 else "black"
                    
                    ax.text(j, i, value_format.format(value),
                            ha="center", va="center",
                            color=text_color,
                            fontsize=kwargs.get("value_font_size", self.config.get("annotation_font_size", 10)))
                            
        # Set title
        if title:
            ax.set_title(title, fontsize=self.config.get("title_font_size", 16))
            
        # Tight layout
        fig.tight_layout()
        
        # Extract insights
        insights = self._extract_insights_from_correlation_matrix(corr_matrix, corr_labels)
        
        return {
            "figure": fig,
            "insights": insights
        }
    
    def _extract_data_for_correlation_matrix(
        self,
        data: Any,
        columns: Optional[List[str]] = None
    ) -> Tuple[np.ndarray, List[str]]:
        """
        Extract data for a correlation matrix from various input formats.
        
        Args:
            data: Input data
            columns: Column names
            
        Returns:
            Tuple[np.ndarray, List[str]]: Correlation matrix and labels
        """
        corr_matrix = None
        corr_labels = []
        
        if _HAS_PANDAS and isinstance(data, pd.DataFrame):
            # DataFrame data
            if columns:
                # Use only specified columns
                df = data[columns].select_dtypes(include=["number"])
            else:
                # Use all numeric columns
                df = data.select_dtypes(include=["number"])
                
            if df.empty:
                # No numeric data
                raise ValueError("No numeric data found for correlation matrix")
                
            # Compute correlation matrix
            corr_matrix = df.corr().values
            corr_labels = df.columns.tolist()
            
        elif isinstance(data, dict):
            # Dictionary data
            if "matrix" in data:
                # Pre-defined correlation matrix
                corr_matrix = np.array(data["matrix"])
                corr_labels = data.get("labels", [f"Var {i+1}" for i in range(corr_matrix.shape[0])])
            elif "data" in data and all(isinstance(v, (list, tuple)) for v in data["data"]):
                # Data points to compute correlation from
                data_array = np.array(data["data"])
                corr_matrix = np.corrcoef(data_array.T)
                corr_labels = data.get("labels", [f"Var {i+1}" for i in range(corr_matrix.shape[0])])
                
        elif isinstance(data, (list, tuple)):
            # List data
            if all(isinstance(row, (list, tuple)) for row in data) and len(data) > 1:
                # Compute correlation between columns
                data_array = np.array(data)
                corr_matrix = np.corrcoef(data_array.T)
                corr_labels = [f"Var {i+1}" for i in range(corr_matrix.shape[0])]
                
        # Handle empty data
        if corr_matrix is None:
            # Generate dummy correlation matrix
            n = 5
            corr_matrix = np.eye(n)
            off_diag = np.random.uniform(-0.7, 0.7, size=(n, n))
            
            # Make it symmetric
            off_diag = (off_diag + off_diag.T) / 2
            
            # Ensure diagonal is 1
            np.fill_diagonal(off_diag, 0)
            
            corr_matrix += off_diag
            corr_labels = [f"Var {i+1}" for i in range(n)]
            
        return corr_matrix, corr_labels
    
    def _extract_insights_from_correlation_matrix(
        self,
        corr_matrix: np.ndarray,
        corr_labels: List[str]
    ) -> Dict[str, Any]:
        """
        Extract insights from correlation matrix data.
        
        Args:
            corr_matrix: Correlation matrix
            corr_labels: Variable labels
            
        Returns:
            Dict[str, Any]: Extracted insights
        """
        insights = {}
        
        if corr_matrix.size == 0:
            return insights
            
        # Find strongest positive correlations
        positive_correlations = []
        
        for i in range(corr_matrix.shape[0]):
            for j in range(i+1, corr_matrix.shape[1]):
                if i != j:
                    correlation = corr_matrix[i, j]
                    
                    if correlation > 0.5:  # Threshold for strong positive correlation
                        positive_correlations.append({
                            "var1": corr_labels[i] if i < len(corr_labels) else f"Var {i}",
                            "var2": corr_labels[j] if j < len(corr_labels) else f"Var {j}",
                            "correlation": correlation,
                            "strength": "very strong" if correlation > 0.8 else "strong"
                        })
                        
        # Sort by correlation strength
        positive_correlations.sort(key=lambda x: x["correlation"], reverse=True)
        
        # Find strongest negative correlations
        negative_correlations = []
        
        for i in range(corr_matrix.shape[0]):
            for j in range(i+1, corr_matrix.shape[1]):
                if i != j:
                    correlation = corr_matrix[i, j]
                    
                    if correlation < -0.5:  # Threshold for strong negative correlation
                        negative_correlations.append({
                            "var1": corr_labels[i] if i < len(corr_labels) else f"Var {i}",
                            "var2": corr_labels[j] if j < len(corr_labels) else f"Var {j}",
                            "correlation": correlation,
                            "strength": "very strong" if correlation < -0.8 else "strong"
                        })
                        
        # Sort by correlation strength
        negative_correlations.sort(key=lambda x: x["correlation"])
        
        insights["strong_positive_correlations"] = positive_correlations[:5] if len(positive_correlations) > 5 else positive_correlations
        insights["strong_negative_correlations"] = negative_correlations[:5] if len(negative_correlations) > 5 else negative_correlations
        
        # Calculate average absolute correlation
        abs_corr = np.abs(corr_matrix)
        np.fill_diagonal(abs_corr, 0)  # Exclude diagonal (self-correlations)
        avg_abs_corr = np.sum(abs_corr) / (abs_corr.size - corr_matrix.shape[0])  # Divide by number of non-diagonal elements
        
        insights["average_correlation"] = {
            "absolute_avg": avg_abs_corr,
            "interpretation": "strong" if avg_abs_corr > 0.6 else 
                             "moderate" if avg_abs_corr > 0.3 else "weak"
        }
        
        # Find variables with highest average correlation
        var_avg_corr = []
        
        for i in range(corr_matrix.shape[0]):
            # Calculate average absolute correlation with other variables
            abs_corr_i = np.abs(corr_matrix[i, :])
            abs_corr_i[i] = 0  # Exclude self-correlation
            avg_abs_corr_i = np.sum(abs_corr_i) / (len(abs_corr_i) - 1)
            
            var_avg_corr.append({
                "variable": corr_labels[i] if i < len(corr_labels) else f"Var {i}",
                "avg_correlation": avg_abs_corr_i
            })
            
        # Sort by average correlation
        var_avg_corr.sort(key=lambda x: x["avg_correlation"], reverse=True)
        
        insights["variable_importance"] = var_avg_corr
        
        return insights
    
    def create_radar_chart(
        self,
        data: Any,
        columns: Optional[List[str]] = None,
        title: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Create a radar chart visualization.
        
        Args:
            data: Input data
            columns: Column names
            title: Chart title
            **kwargs: Additional parameters
            
        Returns:
            Dict[str, Any]: Visualization output
        """
        # Check if matplotlib is available
        if not _HAS_MATPLOTLIB:
            raise ImportError("Matplotlib is required for radar charts")
            
        # Process parameters
        width = kwargs.get("width", self.config.get("default_width"))
        height = kwargs.get("height", self.config.get("default_height"))
        n_levels = kwargs.get("n_levels", 5)
        fill_alpha = kwargs.get("fill_alpha", 0.25)
        
        # Create figure and axes
        fig = plt.figure(figsize=(width/self.config.get("dpi", 100), height/self.config.get("dpi", 100)))
        ax = fig.add_subplot(111, polar=True)
        
        # Extract data
        categories, values_by_category, legends = self._extract_data_for_radar_chart(data, columns)
        
        # Calculate angles for each category (evenly distributed around the circle)
        angles = np.linspace(0, 2*np.pi, len(categories), endpoint=False).tolist()
        
        # Make the plot circular by appending the first angle to the end
        angles += [angles[0]]
        
        # Get colors
        colors = kwargs.get("colors", self.color_manager.get_categorical_palette(len(values_by_category)))
        
        # Draw grid lines and category labels
        ax.set_xticks(angles[:-1])
        ax.set_xticklabels(categories)
        
        # Draw radar chart for each category
        for i, values in enumerate(values_by_category):
            # Close the loop
            values = values + [values[0]]
            
            # Plot values
            ax.plot(angles, values, color=colors[i % len(colors)], linewidth=2, label=legends[i])
            ax.fill(angles, values, color=colors[i % len(colors)], alpha=fill_alpha)
            
        # Draw axis lines
        ax.set_yticklabels([])
        
        # Set limits
        all_values = [v for sublist in values_by_category for v in sublist]
        max_value = max(all_values) if all_values else 1
        
        # Set y-axis limits
        ax.set_ylim(0, max_value * 1.1)
        
        # Draw circles at regular intervals
        level_step = max_value / n_levels
        levels = np.arange(level_step, max_value + level_step, level_step)
        
        for level in levels:
            ax.plot(angles, [level] * len(angles), color=self.color_manager.get_color("grid"), 
                   linestyle="--", alpha=self.config.get("grid_alpha", 0.3))
            
        # Add legend
        if self.config.get("show_legend", True) and len(legends) > 1:
            ax.legend(
                loc=kwargs.get("legend_loc", self.config.get("legend_loc", "upper right")),
                fontsize=self.config.get("legend_font_size", 10)
            )
            
        # Set title
        if title:
            ax.set_title(title, fontsize=self.config.get("title_font_size", 16))
            
        # Tight layout
        fig.tight_layout()
        
        # Extract insights
        insights = self._extract_insights_from_radar_chart(categories, values_by_category, legends)
        
        return {
            "figure": fig,
            "insights": insights
        }
    
    def _extract_data_for_radar_chart(
        self,
        data: Any,
        columns: Optional[List[str]] = None
    ) -> Tuple[List[str], List[List[float]], List[str]]:
        """
        Extract data for a radar chart from various input formats.
        
        Args:
            data: Input data
            columns: Column names
            
        Returns:
            Tuple[List[str], List[List[float]], List[str]]: 
            Categories, values by category, and legends
        """
        categories = []
        values_by_category = []
        legends = []
        
        if _HAS_PANDAS and isinstance(data, pd.DataFrame):
            # DataFrame data
            if columns:
                df = data[columns].copy()
            else:
                df = data.copy()
                
            # Two cases:
            # 1. Each row is a different radar chart, columns are categories
            # 2. Each column is a different radar chart, rows are categories
            
            # Check if we have an index column specified
            if "index_col" in kwargs:
                index_col = kwargs["index_col"]
                df = df.set_index(index_col)
                
            if kwargs.get("transpose", False):
                # Transpose: each column is a radar chart
                df = df.T
                
            # Extract categories from columns
            categories = df.columns.tolist()
            
            # Extract values for each category (row)
            for idx, row in df.iterrows():
                values_by_category.append(row.tolist())
                legends.append(str(idx))
                
        elif isinstance(data, dict):
            # Dictionary data
            if "categories" in data and "values" in data:
                # Pre-defined categories and values
                categories = data["categories"]
                
                if isinstance(data["values"], list) and all(isinstance(v, list) for v in data["values"]):
                    # Multiple series
                    values_by_category = data["values"]
                    legends = data.get("legends", [f"Series {i+1}" for i in range(len(values_by_category))])
                else:
                    # Single series
                    values_by_category = [data["values"]]
                    legends = data.get("legends", ["Series 1"])
            elif all(isinstance(v, list) for v in data.values()):
                # Dictionary of lists
                categories = list(data.keys())
                values_by_category = [list(data.values())]
                legends = ["Series 1"]
                
        elif isinstance(data, (list, tuple)):
            # List data
            if all(isinstance(item, (list, tuple)) for item in data):
                if all(isinstance(subitem, (int, float)) for item in data for subitem in item):
                    # List of lists - each inner list is a series
                    if columns:
                        categories = columns
                    else:
                        categories = [f"Category {i+1}" for i in range(len(data[0]))]
                        
                    values_by_category = data
                    legends = [f"Series {i+1}" for i in range(len(data))]
                    
        # Handle empty data
        if not categories or not values_by_category:
            # Generate dummy data
            categories = [f"Category {i+1}" for i in range(5)]
            values_by_category = [np.random.uniform(0, 10, 5).tolist()]
            legends = ["Series 1"]
            
        return categories, values_by_category, legends
    
    def _extract_insights_from_radar_chart(
        self,
        categories: List[str],
        values_by_category: List[List[float]],
        legends: List[str]
    ) -> Dict[str, Any]:
        """
        Extract insights from radar chart data.
        
        Args:
            categories: Category names
            values_by_category: Values for each category
            legends: Legend labels
            
        Returns:
            Dict[str, Any]: Extracted insights
        """
        insights = {}
        
        if not categories or not values_by_category:
            return insights
            
        # Calculate statistics for each category
        category_insights = []
        
        for i, category in enumerate(categories):
            # Get values for this category across all series
            category_values = [series[i] for series in values_by_category if i < len(series)]
            
            if not category_values:
                continue
                
            # Calculate statistics
            category_insights.append({
                "category": category,
                "min": min(category_values),
                "max": max(category_values),
                "mean": np.mean(category_values),
                "range": max(category_values) - min(category_values)
            })
            
        insights["category_insights"] = category_insights
        
        # Calculate statistics for each series
        series_insights = []
        
        for i, (series, legend) in enumerate(zip(values_by_category, legends)):
            # Calculate statistics
            series_insights.append({
                "series": legend,
                "min": min(series),
                "max": max(series),
                "mean": np.mean(series),
                "std_dev": np.std(series),
                "total": sum(series)
            })
            
            # Find dominant categories (highest values)
            sorted_indices = np.argsort(series)[::-1]
            top_categories = []
            
            for idx in sorted_indices[:3]:  # Top 3 categories
                if idx < len(categories):
                    top_categories.append({
                        "category": categories[idx],
                        "value": series[idx]
                    })
                    
            series_insights[-1]["top_categories"] = top_categories
            
        insights["series_insights"] = series_insights
        
        # Compare series if multiple exist
        if len(values_by_category) > 1:
            comparisons = []
            
            for i in range(len(values_by_category)):
                for j in range(i+1, len(values_by_category)):
                    series_i = values_by_category[i]
                    series_j = values_by_category[j]
                    legend_i = legends[i]
                    legend_j = legends[j]
                    
                    # Calculate similarity (cosine similarity)
                    similarity = np.dot(series_i, series_j) / (np.linalg.norm(series_i) * np.linalg.norm(series_j))
                    
                    # Find categories with biggest differences
                    diffs = [abs(a - b) for a, b in zip(series_i, series_j)]
                    diff_indices = np.argsort(diffs)[::-1]
                    
                    top_diffs = []
                    for idx in diff_indices[:3]:  # Top 3 differences
                        if idx < len(categories):
                            top_diffs.append({
                                "category": categories[idx],
                                "difference": abs(series_i[idx] - series_j[idx]),
                                "values": [series_i[idx], series_j[idx]]
                            })
                            
                    comparisons.append({
                        "series1": legend_i,
                        "series2": legend_j,
                        "similarity": similarity,
                        "top_differences": top_diffs
                    })
                    
            insights["comparisons"] = comparisons
            
        return insights
    
    def create_parallel_coordinates(
        self,
        data: Any,
        columns: Optional[List[str]] = None,
        title: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Create a parallel coordinates visualization.
        
        Args:
            data: Input data
            columns: Column names
            title: Chart title
            **kwargs: Additional parameters
            
        Returns:
            Dict[str, Any]: Visualization output
        """
        # Check if matplotlib is available
        if not _HAS_MATPLOTLIB:
            raise ImportError("Matplotlib is required for parallel coordinates plots")
            
        # Process parameters
        width = kwargs.get("width", self.config.get("default_width"))
        height = kwargs.get("height", self.config.get("default_height"))
        color_by = kwargs.get("color_by")
        alpha = kwargs.get("alpha", 0.5)
        
        # Create figure and axes
        fig, ax = self.layout_manager.create_figure("regular", width=width, height=height)
        
        # Extract data
        df, dimensions, color_values = self._extract_data_for_parallel_coordinates(data, columns, color_by)
        
        if df is None or df.empty:
            raise ValueError("No data for parallel coordinates plot")
            
        # Create y-ticks for each dimension
        y_ticks = range(len(dimensions))
        
        # Create dimension lines
        for i, dim in enumerate(dimensions):
            ax.axvline(x=i, linestyle='-', color=self.color_manager.get_color("grid"), zorder=0)
            
        # Get colors for lines
        if color_values is not None:
            # Use colormap for color values
            cmap = self.color_manager.get_colormap(kwargs.get("colormap", self.config.get("color_map")))
            norm = plt.Normalize(np.min(color_values), np.max(color_values))
            colors = [cmap(norm(val)) for val in color_values]
        else:
            # Single color for all lines
            colors = [kwargs.get("color", self.color_manager.get_color("primary"))] * len(df)
            
        # Plot each data point as a line
        for i, (_, row) in enumerate(df.iterrows()):
            # Get values for this row
            values = [row[dim] for dim in dimensions]
            
            # Create line segments
            segments = [[(j, values[j]) for j in range(len(dimensions))]]
            
            # Plot line
            line_segments = matplotlib.collections.LineCollection(
                segments,
                colors=[colors[i]],
                alpha=alpha,
                linewidth=kwargs.get("line_width", 1.0)
            )
            ax.add_collection(line_segments)
            
        # Add colorbar if using color values
        if color_values is not None and kwargs.get("show_colorbar", True):
            sm = plt.cm.ScalarMappable(cmap=cmap, norm=norm)
            sm.set_array([])
            cbar = plt.colorbar(sm, ax=ax)
            cbar.set_label(kwargs.get("colorbar_label", color_by))
            
        # Set axis limits
        ax.set_xlim([-0.5, len(dimensions) - 0.5])
        
        # Set y-limits for each dimension
        min_values = [df[dim].min() for dim in dimensions]
        max_values = [df[dim].max() for dim in dimensions]
        
        for i, (min_val, max_val) in enumerate(zip(min_values, max_values)):
            # Add some padding
            padding = (max_val - min_val) * 0.05
            ax.plot([i, i], [min_val - padding, max_val + padding], alpha=0)
            
        # Set ticks and labels
        ax.set_xticks(range(len(dimensions)))
        ax.set_xticklabels(dimensions)
        
        # Rotate x labels if requested
        if kwargs.get("rotate_xlabels", False):
            plt.setp(ax.get_xticklabels(), rotation=45, ha="right")
            
        # Set title
        if title:
            ax.set_title(title, fontsize=self.config.get("title_font_size", 16))
            
        # Tight layout
        fig.tight_layout()
        
        # Extract insights
        insights = self._extract_insights_from_parallel_coordinates(df, dimensions)
        
        return {
            "figure": fig,
            "insights": insights
        }
    
    def _extract_data_for_parallel_coordinates(
        self,
        data: Any,
        columns: Optional[List[str]] = None,
        color_by: Optional[str] = None
    ) -> Tuple[Optional[Any], List[str], Optional[List[float]]]:
        """
        Extract data for parallel coordinates from various input formats.
        
        Args:
            data: Input data
            columns: Column names to include
            color_by: Column to use for coloring
            
        Returns:
            Tuple[Optional[Any], List[str], Optional[List[float]]]:
            DataFrame, dimensions, and color values
        """
        df = None
        dimensions = []
        color_values = None
        
        if _HAS_PANDAS and isinstance(data, pd.DataFrame):
            # DataFrame data
            if columns:
                # Use specified columns
                df = data[columns].copy()
                dimensions = columns
            else:
                # Use all numeric columns
                df = data.select_dtypes(include=["number"])
                dimensions = df.columns.tolist()
                
            # Extract color values if specified
            if color_by and color_by in data.columns:
                color_col = data[color_by]
                
                # If categorical, convert to numeric
                if color_col.dtype == 'object' or color_col.dtype.name == 'category':
                    # Create mapping of categories to integers
                    categories = color_col.astype('category').cat.categories
                    color_values = color_col.astype('category').cat.codes.tolist()
                else:
                    # Use numeric values directly
                    color_values = color_col.tolist()
                    
        elif isinstance(data, dict):
            # Dictionary data
            if "data" in data and isinstance(data["data"], list):
                # List of records
                if _HAS_PANDAS:
                    df = pd.DataFrame(data["data"])
                    
                    if columns:
                        dimensions = [col for col in columns if col in df.columns]
                    else:
                        dimensions = df.columns.tolist()
                        
                    # Extract color values if specified
                    if color_by and color_by in df.columns:
                        color_col = df[color_by]
                        
                        # If categorical, convert to numeric
                        if color_col.dtype == 'object' or color_col.dtype.name == 'category':
                            # Create mapping of categories to integers
                            categories = color_col.astype('category').cat.categories
                            color_values = color_col.astype('category').cat.codes.tolist()
                        else:
                            # Use numeric values directly
                            color_values = color_col.tolist()
            elif all(isinstance(v, list) for v in data.values()):
                # Dictionary of lists (columns)
                if _HAS_PANDAS:
                    df = pd.DataFrame(data)
                    
                    if columns:
                        dimensions = [col for col in columns if col in df.columns]
                    else:
                        dimensions = df.columns.tolist()
                        
                    # Extract color values if specified
                    if color_by and color_by in df.columns:
                        color_col = df[color_by]
                        
                        # If categorical, convert to numeric
                        if color_col.dtype == 'object' or color_col.dtype.name == 'category':
                            # Create mapping of categories to integers
                            categories = color_col.astype('category').cat.categories
                            color_values = color_col.astype('category').cat.codes.tolist()
                        else:
                            # Use numeric values directly
                            color_values = color_col.tolist()
                
        elif isinstance(data, (list, tuple)):
            # List data
            if all(isinstance(item, dict) for item in data):
                # List of dictionaries (records)
                if _HAS_PANDAS:
                    df = pd.DataFrame(data)
                    
                    if columns:
                        dimensions = [col for col in columns if col in df.columns]
                    else:
                        dimensions = df.columns.tolist()
                        
                    # Extract color values if specified
                    if color_by and color_by in df.columns:
                        color_col = df[color_by]
                        
                        # If categorical, convert to numeric
                        if color_col.dtype == 'object' or color_col.dtype.name == 'category':
                            # Create mapping of categories to integers
                            categories = color_col.astype('category').cat.categories
                            color_values = color_col.astype('category').cat.codes.tolist()
                        else:
                            # Use numeric values directly
                            color_values = color_col.tolist()
            elif all(isinstance(item, (list, tuple)) for item in data):
                # List of lists (rows)
                if _HAS_PANDAS:
                    if columns:
                        # Use specified column names
                        df = pd.DataFrame(data, columns=columns)
                        dimensions = columns
                    else:
                        # Generate column names
                        df = pd.DataFrame(data)
                        dimensions = df.columns.tolist()
                        
                    # Extract color values if specified
                    if color_by and isinstance(color_by, int) and color_by < df.shape[1]:
                        color_col = df.iloc[:, color_by]
                        
                        # If categorical, convert to numeric
                        if color_col.dtype == 'object' or color_col.dtype.name == 'category':
                            # Create mapping of categories to integers
                            categories = color_col.astype('category').cat.categories
                            color_values = color_col.astype('category').cat.codes.tolist()
                        else:
                            # Use numeric values directly
                            color_values = color_col.tolist()
                            
        # Handle empty data
        if df is None or df.empty:
            # Generate dummy data
            if _HAS_PANDAS:
                n_rows = 10
                n_cols = 5
                
                df = pd.DataFrame(np.random.normal(size=(n_rows, n_cols)))
                dimensions = df.columns.tolist()
                color_values = None
                
        return df, dimensions, color_values
    
    def _extract_insights_from_parallel_coordinates(
        self,
        df: Any,
        dimensions: List[str]
    ) -> Dict[str, Any]:
        """
        Extract insights from parallel coordinates data.
        
        Args:
            df: DataFrame
            dimensions: Dimension names
            
        Returns:
            Dict[str, Any]: Extracted insights
        """
        insights = {}
        
        if df is None or df.empty or not dimensions:
            return insights
            
        # Calculate statistics for each dimension
        dimension_stats = []
        
        for dim in dimensions:
            values = df[dim]
            
            dimension_stats.append({
                "dimension": dim,
                "min": float(values.min()),
                "max": float(values.max()),
                "mean": float(values.mean()),
                "median": float(values.median()),
                "std_dev": float(values.std())
            })
            
        insights["dimension_statistics"] = dimension_stats
        
        # Calculate correlations between dimensions
        if len(dimensions) > 1:
            correlations = []
            
            for i in range(len(dimensions)):
                for j in range(i+1, len(dimensions)):
                    dim_i = dimensions[i]
                    dim_j = dimensions[j]
                    
                    # Calculate correlation
                    corr = np.corrcoef(df[dim_i], df[dim_j])[0, 1]
                    
                    correlations.append({
                        "dimension1": dim_i,
                        "dimension2": dim_j,
                        "correlation": float(corr),
                        "strength": "strong" if abs(corr) > 0.7 else 
                                   "moderate" if abs(corr) > 0.3 else "weak",
                        "type": "positive" if corr > 0 else "negative"
                    })
                    
            # Sort by correlation strength
            correlations.sort(key=lambda x: abs(x["correlation"]), reverse=True)
            
            insights["correlations"] = correlations[:5]  # Top 5 correlations
            
        # Check for clusters or patterns
        if len(df) > 10 and _HAS_PANDAS:
            try:
                # Standardize data for clustering
                from sklearn.preprocessing import StandardScaler
                from sklearn.cluster import KMeans
                
                # Standardize data
                scaler = StandardScaler()
                scaled_data = scaler.fit_transform(df[dimensions])
                
                # Apply K-means clustering
                n_clusters = min(3, len(df) // 3)
                kmeans = KMeans(n_clusters=n_clusters, random_state=42)
                clusters = kmeans.fit_predict(scaled_data)
                
                # Calculate cluster statistics
                cluster_stats = []
                
                for i in range(n_clusters):
                    cluster_df = df[clusters == i]
                    
                    if len(cluster_df) > 0:
                        # Calculate mean values for each dimension
                        mean_values = {dim: float(cluster_df[dim].mean()) for dim in dimensions}
                        
                        cluster_stats.append({
                            "cluster": i,
                            "size": len(cluster_df),
                            "percentage": float(len(cluster_df) / len(df) * 100),
                            "mean_values": mean_values
                        })
                        
                insights["clusters"] = cluster_stats
                
            except:
                # Skip clustering if fails
                pass
                
        return insights
    
    def create_density_plot(
        self,
        data: Any,
        columns: Optional[List[str]] = None,
        title: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Create a density plot visualization.
        
        Args:
            data: Input data
            columns: Column names
            title: Chart title
            **kwargs: Additional parameters
            
        Returns:
            Dict[str, Any]: Visualization output
        """
        # Check if matplotlib is available
        if not _HAS_MATPLOTLIB:
            raise ImportError("Matplotlib is required for density plots")
            
        # Process parameters
        width = kwargs.get("width", self.config.get("default_width"))
        height = kwargs.get("height", self.config.get("default_height"))
        fill = kwargs.get("fill", True)
        alpha = kwargs.get("alpha", 0.5)
        bw_method = kwargs.get("bw_method", "scott")
        show_grid = kwargs.get("show_grid", self.config.get("show_grid", True))
        show_rug = kwargs.get("show_rug", False)
        
        # Create figure and axes
        fig, ax = self.layout_manager.create_figure("regular", width=width, height=height)
        
        # Extract data
        series_data, series_names = self._extract_data_for_density_plot(data, columns)
        
        # Get colors
        colors = kwargs.get("colors", self.color_manager.get_categorical_palette(len(series_data)))
        
        # Create density plot for each series
        for i, (values, name) in enumerate(zip(series_data, series_names)):
            try:
                # Compute kernel density estimate
                from scipy import stats
                kde = stats.gaussian_kde(values, bw_method=bw_method)
                
                # Create x values for plotting
                x = np.linspace(min(values), max(values), 1000)
                
                # Compute density values
                y = kde(x)
                
                # Plot density curve
                ax.plot(x, y, color=colors[i % len(colors)], label=name,
                       linewidth=kwargs.get("line_width", self.config.get("line_width", 2.0)))
                
                # Fill area under curve if requested
                if fill:
                    ax.fill_between(x, y, color=colors[i % len(colors)], alpha=alpha)
                    
                # Add rug plot if requested
                if show_rug:
                    # Draw small vertical lines at each data point
                    ax.plot(values, np.zeros_like(values), '|', color=colors[i % len(colors)],
                           markersize=5, alpha=alpha*0.5)
                    
            except Exception as e:
                logger.warning(f"Error creating density plot for {name}: {e}")
                
        # Set labels
        ax.set_xlabel(kwargs.get("xlabel", columns[0] if columns and len(columns) > 0 else "Values"))
        ax.set_ylabel(kwargs.get("ylabel", "Density"))
        
        # Set title
        if title:
            ax.set_title(title, fontsize=self.config.get("title_font_size", 16))
            
        # Add legend if multiple series
        if len(series_data) > 1 and self.config.get("show_legend", True):
            ax.legend(
                loc=self.config.get("legend_loc", "best"),
                fontsize=self.config.get("legend_font_size", 10)
            )
            
        # Show grid if requested
        ax.grid(show_grid, linestyle="--", alpha=self.config.get("grid_alpha", 0.3))
        
        # Tight layout
        fig.tight_layout()
        
        # Extract insights
        insights = self._extract_insights_from_density_plot(series_data, series_names)
        
        return {
            "figure": fig,
            "insights": insights
        }
    
    def _extract_data_for_density_plot(
        self,
        data: Any,
        columns: Optional[List[str]] = None
    ) -> Tuple[List[List[float]], List[str]]:
        """
        Extract data for a density plot from various input formats.
        
        Args:
            data: Input data
            columns: Column names
            
        Returns:
            Tuple[List[List[float]], List[str]]: Series data and names
        """
        series_data = []
        series_names = []
        
        if _HAS_PANDAS and isinstance(data, pd.DataFrame):
            # DataFrame data
            if columns:
                # Use specified columns
                for col in columns:
                    if col in data.columns:
                        values = data[col].dropna().tolist()
                        if values:
                            series_data.append(values)
                            series_names.append(col)
            else:
                # Use all numeric columns
                num_cols = data.select_dtypes(include=["number"]).columns
                for col in num_cols:
                    values = data[col].dropna().tolist()
                    if values:
                        series_data.append(values)
                        series_names.append(col)
                        
        elif isinstance(data, dict):
            # Dictionary data
            if "values" in data:
                # Single series
                if isinstance(data["values"], list):
                    series_data.append(data["values"])
                    series_names.append(data.get("name", "Series 1"))
            elif "series" in data:
                # Multiple series
                for i, series in enumerate(data["series"]):
                    if isinstance(series, dict) and "values" in series:
                        values = series["values"]
                        name = series.get("name", f"Series {i+1}")
                    else:
                        values = series
                        name = f"Series {i+1}"
                        
                    if values:
                        series_data.append(values)
                        series_names.append(name)
            elif all(isinstance(v, list) for v in data.values()):
                # Dictionary of lists
                for key, values in data.items():
                    if values:
                        series_data.append(values)
                        series_names.append(key)
                        
        elif isinstance(data, (list, tuple)):
            # List data
            if all(isinstance(item, (int, float)) for item in data):
                # Single series of values
                series_data.append(data)
                series_names.append(columns[0] if columns and len(columns) > 0 else "Series 1")
            elif all(isinstance(item, (list, tuple)) for item in data):
                # Multiple series
                for i, values in enumerate(data):
                    if values:
                        series_data.append(values)
                        series_names.append(columns[i] if columns and i < len(columns) else f"Series {i+1}")
                        
        # Handle empty data
        if not series_data:
            # Generate dummy data
            series_data = [np.random.normal(0, 1, 100)]
            series_names = ["Series 1"]
            
        return series_data, series_names
    
    def _extract_insights_from_density_plot(
        self,
        series_data: List[List[float]],
        series_names: List[str]
    ) -> Dict[str, Any]:
        """
        Extract insights from density plot data.
        
        Args:
            series_data: Series data
            series_names: Series names
            
        Returns:
            Dict[str, Any]: Extracted insights
        """
        insights = {}
        
        if not series_data:
            return insights
            
        # Calculate statistics for each series
        series_stats = []
        
        for values, name in zip(series_data, series_names):
            # Basic statistics
            stats = {
                "series": name,
                "mean": np.mean(values),
                "median": np.median(values),
                "std_dev": np.std(values),
                "min": np.min(values),
                "max": np.max(values),
                "iqr": np.percentile(values, 75) - np.percentile(values, 25)
            }
            
            # Calculate distribution properties
            from scipy import stats as sp_stats
            
            # Skewness
            skewness = sp_stats.skew(values)
            stats["skewness"] = skewness
            
            # Kurtosis
            kurtosis = sp_stats.kurtosis(values)
            stats["kurtosis"] = kurtosis
            
            # Normality test
            try:
                k2, p_val = sp_stats.normaltest(values)
                stats["normality_test"] = {
                    "statistic": k2,
                    "p_value": p_val,
                    "is_normal": p_val > 0.05
                }
            except:
                # Skip normality test if it fails
                pass
                
            # Find modes (peaks in density)
            try:
                # Calculate kernel density estimate
                kde = sp_stats.gaussian_kde(values)
                
                # Create x values for finding peaks
                x = np.linspace(min(values), max(values), 1000)
                
                # Compute density values
                y = kde(x)
                
                # Find peaks
                from scipy.signal import find_peaks
                peaks, _ = find_peaks(y)
                
                # Get x values at peaks
                modes = [x[p] for p in peaks]
                
                # Sort by density value
                modes = sorted([(x[p], y[p]) for p in peaks], key=lambda m: m[1], reverse=True)
                
                if modes:
                    stats["modes"] = [{"value": m[0], "density": m[1]} for m in modes[:3]]  # Top 3 modes
                    stats["modality"] = "Multimodal" if len(modes) > 1 else "Unimodal"
            except:
                # Skip mode finding if it fails
                pass
                
            series_stats.append(stats)
            
        insights["series_statistics"] = series_stats
        
        # Compare distributions if multiple series
        if len(series_data) > 1:
            comparisons = []
            
            for i in range(len(series_data)):
                for j in range(i+1, len(series_data)):
                    values_i = np.array(series_data[i])
                    values_j = np.array(series_data[j])
                    name_i = series_names[i]
                    name_j = series_names[j]
                    
                    # Calculate mean difference
                    mean_diff = np.mean(values_i) - np.mean(values_j)
                    
                    # Calculate statistical test for difference
                    try:
                        from scipy import stats as sp_stats
                        t_stat, p_val = sp_stats.ttest_ind(values_i, values_j, equal_var=False)
                        
                        # Calculate effect size (Cohen's d)
                        s1 = np.std(values_i)
                        s2 = np.std(values_j)
                        n1 = len(values_i)
                        n2 = len(values_j)
                        
                        # Pooled standard deviation
                        s_pooled = np.sqrt(((n1 - 1) * s1**2 + (n2 - 1) * s2**2) / (n1 + n2 - 2))
                        
                        # Cohen's d
                        d = mean_diff / s_pooled
                        
                        comparison = {
                            "series1": name_i,
                            "series2": name_j,
                            "mean_difference": mean_diff,
                            "statistical_test": {
                                "test": "t-test",
                                "statistic": t_stat,
                                "p_value": p_val,
                                "significant": p_val < 0.05
                            },
                            "effect_size": {
                                "cohens_d": d,
                                "magnitude": "large" if abs(d) > 0.8 else 
                                            "medium" if abs(d) > 0.5 else "small"
                            }
                        }
                        
                        comparisons.append(comparison)
                    except:
                        # Skip statistical test if it fails
                        comparison = {
                            "series1": name_i,
                            "series2": name_j,
                            "mean_difference": mean_diff
                        }
                        
                        comparisons.append(comparison)
                        
            insights["comparisons"] = comparisons
            
        return insights
    
    def create_stacked_bar(
        self,
        data: Any,
        columns: Optional[List[str]] = None,
        title: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Create a stacked bar chart visualization.
        
        Args:
            data: Input data
            columns: Column names
            title: Chart title
            **kwargs: Additional parameters
            
        Returns:
            Dict[str, Any]: Visualization output
        """
        # Check if matplotlib is available
        if not _HAS_MATPLOTLIB:
            raise ImportError("Matplotlib is required for stacked bar charts")
            
        # Process parameters
        width = kwargs.get("width", self.config.get("default_width"))
        height = kwargs.get("height", self.config.get("default_height"))
        orientation = kwargs.get("orientation", "vertical")
        normalize = kwargs.get("normalize", self.config.get("normalize_bars", False))
        show_values = kwargs.get("show_values", self.config.get("show_data_labels", True))
        value_format = kwargs.get("value_format", "{:.1f}" if not normalize else "{:.1%}")
        bar_width = kwargs.get("bar_width", 0.8)
        show_grid = kwargs.get("show_grid", self.config.get("show_grid", True))
        
        # Create figure and axes
        fig, ax = self.layout_manager.create_figure("regular", width=width, height=height)
        
        # Extract data
        categories, subcategories, values = self._extract_data_for_stacked_bar(data, columns)
        
        # Normalize values if requested
        if normalize:
            # Calculate sums for each category
            sums = np.sum(values, axis=1)
            
            # Avoid division by zero
            sums[sums == 0] = 1.0
            
            # Normalize values
            normalized_values = values / sums[:, np.newaxis]
            plot_values = normalized_values
        else:
            plot_values = values
            
        # Get colors
        colors = kwargs.get("colors", self.color_manager.get_categorical_palette(len(subcategories)))
        
        # Create bar positions
        positions = np.arange(len(categories))
        
        # Create stacked bars
        if orientation == "vertical":
            bottoms = np.zeros(len(categories))
            bars = []
            
            for i, subcategory in enumerate(subcategories):
                bar = ax.bar(positions, plot_values[:, i], bottom=bottoms, 
                           width=bar_width, color=colors[i % len(colors)], 
                           label=subcategory)
                           
                bottoms += plot_values[:, i]
                bars.append(bar)
                
            # Set labels
            ax.set_xlabel(kwargs.get("xlabel", "Categories"))
            ax.set_ylabel(kwargs.get("ylabel", "Values"))
            
            # Set tick labels
            ax.set_xticks(positions)
            ax.set_xticklabels(categories)
            
            # Rotate x-axis labels if needed
            if len(categories) > 5 or kwargs.get("rotate_xlabels", False):
                plt.setp(ax.get_xticklabels(), rotation=45, ha="right")
        else:
            # Horizontal stacked bars
            bottoms = np.zeros(len(categories))
            bars = []
            
            for i, subcategory in enumerate(subcategories):
                bar = ax.barh(positions, plot_values[:, i], left=bottoms, 
                            height=bar_width, color=colors[i % len(colors)], 
                            label=subcategory)
                            
                bottoms += plot_values[:, i]
                bars.append(bar)
                
            # Set labels
            ax.set_ylabel(kwargs.get("xlabel", "Categories"))
            ax.set_xlabel(kwargs.get("ylabel", "Values"))
            
            # Set tick labels
            ax.set_yticks(positions)
            ax.set_yticklabels(categories)
            
        # Add values if requested
        if show_values:
            # For each subcategory
            for i, bar_set in enumerate(bars):
                # For each bar in the set
                for j, bar in enumerate(bar_set):
                    # Only show significant values
                    val = plot_values[j, i]
                    threshold = kwargs.get("value_threshold", self.config.get("data_label_threshold", 0.05))
                    
                    if val >= threshold:
                        # Get bar dimensions
                        if orientation == "vertical":
                            height = bar.get_height()
                            width = bar.get_width()
                            x = bar.get_x() + width / 2
                            y = bar.get_y() + height / 2
                            ha, va = "center", "center"
                        else:
                            height = bar.get_height()
                            width = bar.get_width()
                            x = bar.get_x() + width / 2
                            y = bar.get_y() + height / 2
                            ha, va = "center", "center"
                            
                        # Add text
                        ax.text(x, y, value_format.format(val),
                               ha=ha, va=va,
                               fontsize=kwargs.get("value_font_size", self.config.get("annotation_font_size", 10)),
                               color="white", fontweight="bold")
                               
        # Add legend
        if self.config.get("show_legend", True):
            ax.legend(
                loc=kwargs.get("legend_loc", self.config.get("legend_loc", "best")),
                fontsize=self.config.get("legend_font_size", 10)
            )
            
        # Show grid if requested
        if show_grid:
            if orientation == "vertical":
                ax.grid(True, axis="y", linestyle="--", alpha=self.config.get("grid_alpha", 0.3))
            else:
                ax.grid(True, axis="x", linestyle="--", alpha=self.config.get("grid_alpha", 0.3))
                
        # Set title
        if title:
            ax.set_title(title, fontsize=self.config.get("title_font_size", 16))
            
        # Tight layout
        fig.tight_layout()
        
        # Extract insights
        insights = self._extract_insights_from_stacked_bar(categories, subcategories, values)
        
        return {
            "figure": fig,
            "insights": insights
        }
    
    def _extract_data_for_stacked_bar(
        self,
        data: Any,
        columns: Optional[List[str]] = None
    ) -> Tuple[List[str], List[str], np.ndarray]:
        """
        Extract data for a stacked bar chart from various input formats.
        
        Args:
            data: Input data
            columns: Column names
            
        Returns:
            Tuple[List[str], List[str], np.ndarray]: 
            Categories, subcategories, and values matrix
        """
        categories = []
        subcategories = []
        values_matrix = None
        
        if _HAS_PANDAS and isinstance(data, pd.DataFrame):
            # DataFrame data
            if columns and len(columns) >= 2:
                # First column is category, rest are subcategories
                cat_col = columns[0]
                subcat_cols = columns[1:]
                
                categories = data[cat_col].unique().tolist()
                subcategories = subcat_cols
                
                # Create values matrix
                values_matrix = np.zeros((len(categories), len(subcategories)))
                
                for i, cat in enumerate(categories):
                    cat_rows = data[data[cat_col] == cat]
                    for j, subcat in enumerate(subcategories):
                        # Use mean for aggregation
                        values_matrix[i, j] = cat_rows[subcat].mean()
            else:
                # Try to determine appropriate columns
                cat_cols = data.select_dtypes(include=["object", "category"]).columns
                num_cols = data.select_dtypes(include=["number"]).columns
                
                if len(cat_cols) >= 1 and len(num_cols) >= 1:
                    # Use first categorical column as category
                    cat_col = cat_cols[0]
                    
                    # Use all numeric columns as subcategories
                    subcategories = num_cols.tolist()
                    
                    # Get unique categories
                    categories = data[cat_col].unique().tolist()
                    
                    # Create values matrix
                    values_matrix = np.zeros((len(categories), len(subcategories)))
                    
                    for i, cat in enumerate(categories):
                        cat_rows = data[data[cat_col] == cat]
                        for j, subcat in enumerate(subcategories):
                            # Use mean for aggregation
                            values_matrix[i, j] = cat_rows[subcat].mean()
                elif len(num_cols) >= 2:
                    # Use index as categories and numeric columns as subcategories
                    categories = data.index.astype(str).tolist()
                    subcategories = num_cols.tolist()
                    
                    # Create values matrix
                    values_matrix = data[subcategories].values
                    
        elif isinstance(data, dict):
            # Dictionary data
            if "categories" in data and "values" in data:
                categories = data["categories"]
                
                if "subcategories" in data:
                    subcategories = data["subcategories"]
                    
                    # Check values format
                    if isinstance(data["values"], list) and all(isinstance(v, list) for v in data["values"]):
                        # Values is a list of lists (matrix)
                        values_matrix = np.array(data["values"])
                    elif isinstance(data["values"], dict):
                        # Values is a dictionary of subcategory values
                        subcat_data = [data["values"].get(sc, [0] * len(categories)) for sc in subcategories]
                        values_matrix = np.array(subcat_data).T
            elif all(isinstance(v, dict) for v in data.values()):
                # Dictionary of dictionaries
                categories = list(data.keys())
                
                # Get all subcategories
                all_subcats = set()
                for cat_data in data.values():
                    all_subcats.update(cat_data.keys())
                
                subcategories = sorted(list(all_subcats))
                
                # Create values matrix
                values_matrix = np.zeros((len(categories), len(subcategories)))
                
                for i, cat in enumerate(categories):
                    cat_data = data[cat]
                    for j, subcat in enumerate(subcategories):
                        values_matrix[i, j] = cat_data.get(subcat, 0)
                        
        elif isinstance(data, (list, tuple)):
            # List data
            if all(isinstance(item, dict) for item in data):
                # List of dictionaries
                if all("category" in item and "values" in item for item in data):
                    # Each item has category and values dictionary
                    categories = [item["category"] for item in data]
                    
                    # Get all subcategories
                    all_subcats = set()
                    for item in data:
                        all_subcats.update(item["values"].keys())
                        
                    subcategories = sorted(list(all_subcats))
                    
                    # Create values matrix
                    values_matrix = np.zeros((len(categories), len(subcategories)))
                    
                    for i, item in enumerate(data):
                        for j, subcat in enumerate(subcategories):
                            values_matrix[i, j] = item["values"].get(subcat, 0)
            elif all(isinstance(item, (list, tuple)) for item in data):
                # List of lists/tuples
                if columns and len(columns) > 1:
                    # Use first column as categories
                    categories = [str(item[0]) for item in data]
                    
                    # Use remaining columns as subcategories
                    subcategories = columns[1:]
                    
                    # Create values matrix
                    values_matrix = np.array([item[1:] for item in data])
                else:
                    # Try to determine appropriate structure
                    # Assume first column is category, rest are values
                    categories = [str(item[0]) for item in data]
                    
                    # Generate subcategory names
                    max_len = max(len(item) for item in data)
                    subcategories = [f"Subcategory {i+1}" for i in range(max_len - 1)]
                    
                    # Create values matrix
                    values_matrix = np.zeros((len(categories), len(subcategories)))
                    
                    for i, item in enumerate(data):
                        for j in range(min(len(item) - 1, len(subcategories))):
                            values_matrix[i, j] = item[j + 1]
                            
        # Handle empty data
        if values_matrix is None:
            # Generate dummy data
            n_cat = 5
            n_subcat = 3
            
            categories = [f"Category {i+1}" for i in range(n_cat)]
            subcategories = [f"Subcategory {i+1}" for i in range(n_subcat)]
            values_matrix = np.random.rand(n_cat, n_subcat) * 10
            
        return categories, subcategories, values_matrix
    
    def _extract_insights_from_stacked_bar(
        self,
        categories: List[str],
        subcategories: List[str],
        values: np.ndarray
    ) -> Dict[str, Any]:
        """
        Extract insights from stacked bar chart data.
        
        Args:
            categories: Categories
            subcategories: Subcategories
            values: Values matrix
            
        Returns:
            Dict[str, Any]: Extracted insights
        """
        insights = {}
        
        if not categories or not subcategories or values.size == 0:
            return insights
            
        # Calculate total values for each category
        total_values = np.sum(values, axis=1)
        
        # Find category with highest/lowest total
        max_idx = np.argmax(total_values)
        min_idx = np.argmin(total_values)
        
        insights["category_totals"] = {
            "highest": {
                "category": categories[max_idx] if max_idx < len(categories) else f"Category {max_idx}",
                "value": float(total_values[max_idx])
            },
            "lowest": {
                "category": categories[min_idx] if min_idx < len(categories) else f"Category {min_idx}",
                "value": float(total_values[min_idx])
            },
            "all_totals": [
                {
                    "category": cat,
                    "value": float(val)
                } for cat, val in zip(categories, total_values)
            ]
        }
        
        # Calculate total values for each subcategory
        subcat_totals = np.sum(values, axis=0)
        
        # Find subcategory with highest/lowest total
        max_idx = np.argmax(subcat_totals)
        min_idx = np.argmin(subcat_totals)
        
        insights["subcategory_totals"] = {
            "highest": {
                "subcategory": subcategories[max_idx] if max_idx < len(subcategories) else f"Subcategory {max_idx}",
                "value": float(subcat_totals[max_idx])
            },
            "lowest": {
                "subcategory": subcategories[min_idx] if min_idx < len(subcategories) else f"Subcategory {min_idx}",
                "value": float(subcat_totals[min_idx])
            },
            "all_totals": [
                {
                    "subcategory": subcat,
                    "value": float(val)
                } for subcat, val in zip(subcategories, subcat_totals)
            ]
        }
        
        # Find significant combinations (highest values)
        flat_indices = np.argsort(values.flatten())[::-1]
        
        # Convert flat indices to 2D indices
        top_combinations = []
        
        for idx in flat_indices[:5]:  # Top 5 combinations
            cat_idx = idx // values.shape[1]
            subcat_idx = idx % values.shape[1]
            
            top_combinations.append({
                "category": categories[cat_idx] if cat_idx < len(categories) else f"Category {cat_idx}",
                "subcategory": subcategories[subcat_idx] if subcat_idx < len(subcategories) else f"Subcategory {subcat_idx}",
                "value": float(values[cat_idx, subcat_idx])
            })
            
        insights["top_combinations"] = top_combinations
        
        # Calculate proportions within each category
        proportions = values / np.sum(values, axis=1, keepdims=True)
        
        # Find dominant subcategory for each category
        dominant_subcats = []
        
        for i, cat in enumerate(categories):
            max_idx = np.argmax(proportions[i])
            
            dominant_subcats.append({
                "category": cat,
                "dominant_subcategory": subcategories[max_idx] if max_idx < len(subcategories) else f"Subcategory {max_idx}",
                "proportion": float(proportions[i, max_idx])
            })
            
        insights["dominant_subcategories"] = dominant_subcats
        
        # Look for patterns or trends
        # For example, check if the same subcategory is dominant across multiple categories
        subcat_dominance_count = Counter(item["dominant_subcategory"] for item in dominant_subcats)
        
        if max(subcat_dominance_count.values()) > 1:
            # Same subcategory is dominant across multiple categories
            most_dominant = subcat_dominance_count.most_common(1)[0]
            
            insights["patterns"] = {
                "consistent_dominance": {
                    "subcategory": most_dominant[0],
                    "count": most_dominant[1],
                    "percentage": most_dominant[1] / len(categories) * 100
                }
            }
            
        return insights
    
    def create_grouped_bar(
        self,
        data: Any,
        columns: Optional[List[str]] = None,
        title: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Create a grouped bar chart visualization.
        
        Args:
            data: Input data
            columns: Column names
            title: Chart title
            **kwargs: Additional parameters
            
        Returns:
            Dict[str, Any]: Visualization output
        """
        # Check if matplotlib is available
        if not _HAS_MATPLOTLIB:
            raise ImportError("Matplotlib is required for grouped bar charts")
            
        # Process parameters
        width = kwargs.get("width", self.config.get("default_width"))
        height = kwargs.get("height", self.config.get("default_height"))
        orientation = kwargs.get("orientation", "vertical")
        show_values = kwargs.get("show_values", self.config.get("show_data_labels", True))
        value_format = kwargs.get("value_format", "{:.1f}")
        bar_width = kwargs.get("bar_width", 0.8)
        group_gap = kwargs.get("group_gap", 0.2)
        show_grid = kwargs.get("show_grid", self.config.get("show_grid", True))
        
        # Create figure and axes
        fig, ax = self.layout_manager.create_figure("regular", width=width, height=height)
        
        # Extract data
        categories, subcategories, values = self._extract_data_for_stacked_bar(data, columns)
        
        # Get colors
        colors = kwargs.get("colors", self.color_manager.get_categorical_palette(len(subcategories)))
        
        # Create bar positions
        n_groups = len(categories)
        n_bars = len(subcategories)
        
        # Calculate width of each bar
        bar_width_final = bar_width / n_bars
        
        # Calculate positions
        if orientation == "vertical":
            # Calculate positions for each category group
            positions = np.arange(n_groups)
            
            # Calculate offsets for each bar within a group
            offsets = np.linspace(-(bar_width/2) + (bar_width_final/2), 
                                (bar_width/2) - (bar_width_final/2), n_bars)
                                
            # Create grouped bars
            bars = []
            
            for i, subcategory in enumerate(subcategories):
                # Calculate positions for this set of bars
                bar_positions = positions + offsets[i]
                
                # Create bars
                bar = ax.bar(bar_positions, values[:, i], width=bar_width_final,
                           color=colors[i % len(colors)], label=subcategory)
                           
                bars.append(bar)
                
            # Set labels
            ax.set_xlabel(kwargs.get("xlabel", "Categories"))
            ax.set_ylabel(kwargs.get("ylabel", "Values"))
            
            # Set tick positions and labels
            ax.set_xticks(positions)
            ax.set_xticklabels(categories)
            
            # Rotate x-axis labels if needed
            if len(categories) > 5 or kwargs.get("rotate_xlabels", False):
                plt.setp(ax.get_xticklabels(), rotation=45, ha="right")
                
        else:
            # Horizontal orientation
            # Calculate positions for each category group
            positions = np.arange(n_groups)
            
            # Calculate offsets for each bar within a group
            offsets = np.linspace(-(bar_width/2) + (bar_width_final/2), 
                                (bar_width/2) - (bar_width_final/2), n_bars)
                                
            # Create grouped bars
            bars = []
            
            for i, subcategory in enumerate(subcategories):
                # Calculate positions for this set of bars
                bar_positions = positions + offsets[i]
                
                # Create bars
                bar = ax.barh(bar_positions, values[:, i], height=bar_width_final,
                           color=colors[i % len(colors)], label=subcategory)
                           
                bars.append(bar)
                
            # Set labels
            ax.set_ylabel(kwargs.get("xlabel", "Categories"))
            ax.set_xlabel(kwargs.get("ylabel", "Values"))
            
            # Set tick positions and labels
            ax.set_yticks(positions)
            ax.set_yticklabels(categories)
            
        # Add values if requested
        if show_values:
            # For each subcategory
            for i, bar_set in enumerate(bars):
                # For each bar in the set
                for j, bar in enumerate(bar_set):
                    # Get value
                    val = values[j, i]
                    
                    # Get position
                    if orientation == "vertical":
                        height = bar.get_height()
                        x = bar.get_x() + bar.get_width() / 2
                        y = height
                        ha, va = "center", "bottom"
                    else:
                        width = bar.get_width()
                        x = width
                        y = bar.get_y() + bar.get_height() / 2
                        ha, va = "left", "center"
                        
                    # Add text
                    ax.text(x, y, value_format.format(val),
                           ha=ha, va=va,
                           fontsize=kwargs.get("value_font_size", self.config.get("annotation_font_size", 10)))
                           
        # Add legend
        if self.config.get("show_legend", True):
            ax.legend(
                loc=kwargs.get("legend_loc", self.config.get("legend_loc", "best")),
                fontsize=self.config.get("legend_font_size", 10)
            )
            
        # Show grid if requested
        if show_grid:
            if orientation == "vertical":
                ax.grid(True, axis="y", linestyle="--", alpha=self.config.get("grid_alpha", 0.3))
            else:
                ax.grid(True, axis="x", linestyle="--", alpha=self.config.get("grid_alpha", 0.3))
                
        # Set title
        if title:
            ax.set_title(title, fontsize=self.config.get("title_font_size", 16))
            
        # Tight layout
        fig.tight_layout()
        
        # Extract insights
        insights = self._extract_insights_from_stacked_bar(categories, subcategories, values)
        
        return {
            "figure": fig,
            "insights": insights
        }
    
    def create_violin_plot(
        self,
        data: Any,
        columns: Optional[List[str]] = None,
        title: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Create a violin plot visualization.
        
        Args:
            data: Input data
            columns: Column names
            title: Chart title
            **kwargs: Additional parameters
            
        Returns:
            Dict[str, Any]: Visualization output
        """
        # Check if matplotlib is available
        if not _HAS_MATPLOTLIB:
            raise ImportError("Matplotlib is required for violin plots")
            
        # Process parameters
        width = kwargs.get("width", self.config.get("default_width"))
        height = kwargs.get("height", self.config.get("default_height"))
        orientation = kwargs.get("orientation", "vertical")
        show_points = kwargs.get("show_points", False)
        show_boxes = kwargs.get("show_boxes", True)
        points_alpha = kwargs.get("points_alpha", 0.5)
        points_size = kwargs.get("points_size", 5)
        show_grid = kwargs.get("show_grid", self.config.get("show_grid", True))
        
        # Create figure and axes
        fig, ax = self.layout_manager.create_figure("regular", width=width, height=height)
        
        # Extract data
        categories, values_by_category = self._extract_data_for_violin_plot(data, columns)
        
        # Get colors
        colors = kwargs.get("colors", self.color_manager.get_categorical_palette(len(categories)))
        
        # Create violin plots
        violin_parts = ax.violinplot(
            values_by_category, 
            vert=(orientation == "vertical"),
            showmeans=show_boxes,
            showmedians=show_boxes,
            showextrema=show_boxes
        )
        
        # Style violin parts
        for i, pc in enumerate(violin_parts["bodies"]):
            pc.set_facecolor(colors[i % len(colors)])
            pc.set_alpha(0.7)
            pc.set_edgecolor(self.color_manager.get_color("text"))
            
        if show_boxes:
            # Style mean line
            violin_parts["cmeans"].set_color(self.color_manager.get_color("secondary"))
            violin_parts["cmeans"].set_linewidth(2)
            
            # Style median line
            violin_parts["cmedians"].set_color(self.color_manager.get_color("tertiary"))
            violin_parts["cmedians"].set_linewidth(2)
            
            # Style min/max lines
            violin_parts["cbars"].set_color(self.color_manager.get_color("text"))
            violin_parts["cbars"].set_linewidth(1)
            
            # Style whiskers
            violin_parts["cmins"].set_color(self.color_manager.get_color("text"))
            violin_parts["cmins"].set_linewidth(1)
            violin_parts["cmaxes"].set_color(self.color_manager.get_color("text"))
            violin_parts["cmaxes"].set_linewidth(1)
            
        # Add individual points if requested
        if show_points:
            for i, (cat, values) in enumerate(zip(categories, values_by_category)):
                # Calculate position
                pos = i + 1
                
                # Add jitter to x positions
                jitter = np.random.normal(0, 0.05, size=len(values))
                
                if orientation == "vertical":
                    # Vertical orientation - scatter points horizontally
                    ax.scatter(
                        pos + jitter, 
                        values, 
                        s=points_size, 
                        color=colors[i % len(colors)],
                        alpha=points_alpha,
                        edgecolor="none"
                    )
                else:
                    # Horizontal orientation - scatter points vertically
                    ax.scatter(
                        values,
                        pos + jitter,
                        s=points_size,
                        color=colors[i % len(colors)],
                        alpha=points_alpha,
                        edgecolor="none"
                    )
                    
        # Set labels
        if orientation == "vertical":
            ax.set_xlabel(kwargs.get("xlabel", "Categories"))
            ax.set_ylabel(kwargs.get("ylabel", "Values"))
            
            # Set tick positions and labels
            ax.set_xticks(np.arange(1, len(categories) + 1))
            ax.set_xticklabels(categories)
            
            # Rotate x-axis labels if needed
            if len(categories) > 5 or kwargs.get("rotate_xlabels", False):
                plt.setp(ax.get_xticklabels(), rotation=45, ha="right")
        else:
            ax.set_ylabel(kwargs.get("xlabel", "Categories"))
            ax.set_xlabel(kwargs.get("ylabel", "Values"))
            
            # Set tick positions and labels
            ax.set_yticks(np.arange(1, len(categories) + 1))
            ax.set_yticklabels(categories)
            
        # Show grid if requested
        if show_grid:
            if orientation == "vertical":
                ax.grid(True, axis="y", linestyle="--", alpha=self.config.get("grid_alpha", 0.3))
            else:
                ax.grid(True, axis="x", linestyle="--", alpha=self.config.get("grid_alpha", 0.3))
                
        # Set title
        if title:
            ax.set_title(title, fontsize=self.config.get("title_font_size", 16))
            
        # Tight layout
        fig.tight_layout()
        
        # Extract insights
        insights = self._extract_insights_from_violin_plot(categories, values_by_category)
        
        return {
            "figure": fig,
            "insights": insights
        }
    
    def _extract_data_for_violin_plot(
        self,
        data: Any,
        columns: Optional[List[str]] = None
    ) -> Tuple[List[str], List[List[float]]]:
        """
        Extract data for a violin plot from various input formats.
        
        Args:
            data: Input data
            columns: Column names
            
        Returns:
            Tuple[List[str], List[List[float]]]: Categories and values by category
        """
        categories = []
        values_by_category = []
        
        if _HAS_PANDAS and isinstance(data, pd.DataFrame):
            # DataFrame data
            if columns and len(columns) >= 2:
                # First column is category, second is values
                cat_col, val_col = columns[0], columns[1]
                
                # Group by category
                grouped = data.groupby(cat_col)[val_col].apply(lambda x: x.dropna().tolist()).to_dict()
                
                # Extract categories and values
                categories = list(grouped.keys())
                values_by_category = [grouped[cat] for cat in categories]
            elif columns and len(columns) == 1:
                # Single column - use all values
                val_col = columns[0]
                
                categories = ["Values"]
                values_by_category = [data[val_col].dropna().tolist()]
            else:
                # Use all numeric columns as separate categories
                num_cols = data.select_dtypes(include=["number"]).columns
                
                if num_cols.empty:
                    raise ValueError("No numeric columns found for violin plot")
                    
                categories = num_cols.tolist()
                values_by_category = [data[col].dropna().tolist() for col in categories]
                
        elif isinstance(data, dict):
            # Dictionary data
            if "categories" in data and "values" in data:
                # Pre-defined categories and values
                categories = data["categories"]
                
                if isinstance(data["values"], list) and all(isinstance(v, list) for v in data["values"]):
                    # List of lists - each inner list is values for a category
                    values_by_category = data["values"]
                else:
                    # Single list - use for all categories
                    values_by_category = [data["values"] for _ in categories]
            elif all(isinstance(v, list) for v in data.values()):
                # Dictionary of lists
                categories = list(data.keys())
                values_by_category = list(data.values())
                
        elif isinstance(data, (list, tuple)):
            # List data
            if all(isinstance(item, dict) for item in data):
                # List of dictionaries
                if all("category" in item and "values" in item for item in data):
                    # Each dictionary has category and values
                    categories = [item["category"] for item in data]
                    values_by_category = [item["values"] for item in data]
            elif all(isinstance(item, (list, tuple)) for item in data):
                # List of lists - each inner list is values for a category
                if columns:
                    # Use provided category names
                    categories = columns
                else:
                    # Generate category names
                    categories = [f"Category {i+1}" for i in range(len(data))]
                    
                values_by_category = data
            elif all(isinstance(item, (int, float)) for item in data):
                # Single list of values
                categories = ["Values"]
                values_by_category = [data]
                
        # Handle empty data
        if not categories or not values_by_category:
            # Generate dummy data
            n_cat = 3
            n_points = 100
            
            categories = [f"Category {i+1}" for i in range(n_cat)]
            values_by_category = [np.random.normal(i+1, 1, n_points).tolist() for i in range(n_cat)]
            
        return categories, values_by_category
    
    def _extract_insights_from_violin_plot(
        self,
        categories: List[str],
        values_by_category: List[List[float]]
    ) -> Dict[str, Any]:
        """
        Extract insights from violin plot data.
        
        Args:
            categories: Categories
            values_by_category: Values for each category
            
        Returns:
            Dict[str, Any]: Extracted insights
        """
        insights = {}
        
        if not categories or not values_by_category:
            return insights
            
        # Calculate statistics for each category
        category_stats = []
        
        for cat, values in zip(categories, values_by_category):
            if not values:
                continue
                
            # Calculate basic statistics
            stats = {
                "category": cat,
                "count": len(values),
                "min": float(np.min(values)),
                "max": float(np.max(values)),
                "mean": float(np.mean(values)),
                "median": float(np.median(values)),
                "std_dev": float(np.std(values)),
            }
            
            # Calculate quartiles
            q1, q3 = np.percentile(values, [25, 75])
            iqr = q3 - q1
            
            stats["q1"] = float(q1)
            stats["q3"] = float(q3)
            stats["iqr"] = float(iqr)
            
            # Check for outliers
            lower_bound = q1 - 1.5 * iqr
            upper_bound = q3 + 1.5 * iqr
            
            outliers = [v for v in values if v < lower_bound or v > upper_bound]
            
            stats["outliers"] = {
                "count": len(outliers),
                "percentage": len(outliers) / len(values) * 100 if values else 0
            }
            
            # Calculate distribution shape
            skewness = float(np.mean(((np.array(values) - np.mean(values)) / np.std(values))**3)) if np.std(values) > 0 else 0
            kurtosis = float(np.mean(((np.array(values) - np.mean(values)) / np.std(values))**4) - 3) if np.std(values) > 0 else 0
            
            stats["skewness"] = skewness
            stats["kurtosis"] = kurtosis
            stats["distribution_shape"] = (
                "Normal" if abs(skewness) < 0.5 and abs(kurtosis) < 0.5 else
                "Right-skewed" if skewness > 0.5 else
                "Left-skewed" if skewness < -0.5 else
                "Heavy-tailed" if kurtosis > 0.5 else
                "Light-tailed" if kurtosis < -0.5 else
                "Irregular"
            )
            
            category_stats.append(stats)
            
        insights["category_statistics"] = category_stats
        
        # Compare distributions if multiple categories
        if len(category_stats) > 1:
            # Perform statistical tests
            try:
                from scipy import stats as sp_stats
                
                # Perform one-way ANOVA if three or more categories
                if len(category_stats) >= 3:
                    # Prepare data for ANOVA
                    anova_data = [values for values in values_by_category if values]
                    
                    if all(len(values) > 0 for values in anova_data):
                        f_val, p_val = sp_stats.f_oneway(*anova_data)
                        
                        insights["anova_test"] = {
                            "f_statistic": float(f_val),
                            "p_value": float(p_val),
                            "significant": p_val < 0.05,
                            "interpretation": "There are significant differences between at least two categories." if p_val < 0.05 else
                                            "No significant differences detected between categories."
                        }
                
                # Perform pairwise t-tests
                pairwise_tests = []
                
                for i in range(len(category_stats)):
                    for j in range(i+1, len(category_stats)):
                        cat_i = categories[i]
                        cat_j = categories[j]
                        values_i = values_by_category[i]
                        values_j = values_by_category[j]
                        
                        if values_i and values_j:
                            # T-test
                            t_val, p_val = sp_stats.ttest_ind(values_i, values_j, equal_var=False)
                            
                            # Effect size (Cohen's d)
                            mean_diff = np.mean(values_i) - np.mean(values_j)
                            pooled_std = np.sqrt((np.std(values_i)**2 + np.std(values_j)**2) / 2)
                            cohens_d = mean_diff / pooled_std if pooled_std > 0 else 0
                            
                            pairwise_tests.append({
                                "category1": cat_i,
                                "category2": cat_j,
                                "t_statistic": float(t_val),
                                "p_value": float(p_val),
                                "significant": p_val < 0.05,
                                "cohens_d": float(cohens_d),
                                "effect_size": "large" if abs(cohens_d) > 0.8 else
                                              "medium" if abs(cohens_d) > 0.5 else "small"
                            })
                
                if pairwise_tests:
                    insights["pairwise_tests"] = pairwise_tests
            except:
                # Skip statistical tests if they fail
                pass
                
        return insights
    
    def create_bubble_chart(
        self,
        data: Any,
        columns: Optional[List[str]] = None,
        title: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Create a bubble chart visualization.
        
        Args:
            data: Input data
            columns: Column names
            title: Chart title
            **kwargs: Additional parameters
            
        Returns:
            Dict[str, Any]: Visualization output
        """
        # Check if matplotlib is available
        if not _HAS_MATPLOTLIB:
            raise ImportError("Matplotlib is required for bubble charts")
            
        # Process parameters
        width = kwargs.get("width", self.config.get("default_width"))
        height = kwargs.get("height", self.config.get("default_height"))
        min_size = kwargs.get("min_size", 10)
        max_size = kwargs.get("max_size", 500)
        alpha = kwargs.get("alpha", 0.7)
        show_labels = kwargs.get("show_labels", self.config.get("show_data_labels", True))
        show_grid = kwargs.get("show_grid", self.config.get("show_grid", True))
        
        # Create figure and axes
        fig, ax = self.layout_manager.create_figure("regular", width=width, height=height)
        
        # Extract data
        x_data, y_data, sizes, colors, labels = self._extract_data_for_bubble_chart(data, columns)
        
        # Get color and cmap
        color_values = colors if colors is not None else self.color_manager.get_color("primary")
        cmap = kwargs.get("cmap", self.color_manager.get_colormap())
        
        # Create bubble chart
        scatter = ax.scatter(
            x_data, 
            y_data, 
            s=sizes,
            c=color_values,
            cmap=cmap if colors is not None else None,
            alpha=alpha,
            edgecolors=kwargs.get("edge_color", "white"),
            linewidths=kwargs.get("edge_width", 0.5)
        )
        
        # Add colorbar if using color values
        if colors is not None and kwargs.get("show_colorbar", True):
            cbar = plt.colorbar(scatter, ax=ax)
            cbar.set_label(kwargs.get("colorbar_label", "Values"))
            
        # Add labels if requested
        if show_labels and labels is not None:
            # Adjust label visibility based on size
            for i, (x, y, size, label) in enumerate(zip(x_data, y_data, sizes, labels)):
                # Only label significant bubbles (size > threshold)
                if size > kwargs.get("label_threshold", min_size * 2):
                    ax.annotate(
                        label,
                        (x, y),
                        xytext=(0, 5),  # Small offset above bubble
                        textcoords="offset points",
                        ha="center",
                        va="bottom",
                        fontsize=kwargs.get("label_font_size", self.config.get("annotation_font_size", 10))
                    )
                    
        # Set labels
        ax.set_xlabel(kwargs.get("xlabel", columns[0] if columns and len(columns) > 0 else "X"))
        ax.set_ylabel(kwargs.get("ylabel", columns[1] if columns and len(columns) > 1 else "Y"))
        
        # Show grid if requested
        ax.grid(show_grid, linestyle="--", alpha=self.config.get("grid_alpha", 0.3))
        
        # Set title
        if title:
            ax.set_title(title, fontsize=self.config.get("title_font_size", 16))
            
        # Add a legend for bubble sizes if requested
        if kwargs.get("show_size_legend", True):
            # Create fake bubbles for the legend
            size_min = np.min(sizes)
            size_max = np.max(sizes)
            
            if size_min != size_max:
                # Create three sizes for the legend
                size_med = (size_min + size_max) / 2
                size_legend_handles = []
                
                for s, name in [(size_min, "Small"), (size_med, "Medium"), (size_max, "Large")]:
                    handle = plt.Line2D(
                        [0], [0],
                        marker='o',
                        color='w',
                        markerfacecolor=self.color_manager.get_color("primary"),
                        markersize=np.sqrt(s) / 2,  # Convert area to radius for markersize
                        label=name
                    )
                    size_legend_handles.append(handle)
                    
                # Add size legend
                ax.legend(
                    handles=size_legend_handles,
                    loc=kwargs.get("legend_loc", self.config.get("legend_loc", "upper right")),
                    title=kwargs.get("size_legend_title", "Size"),
                    fontsize=self.config.get("legend_font_size", 10)
                )
                
        # Tight layout
        fig.tight_layout()
        
        # Extract insights
        insights = self._extract_insights_from_bubble_chart(x_data, y_data, sizes, colors, labels)
        
        return {
            "figure": fig,
            "insights": insights
        }
    
    def _extract_data_for_bubble_chart(
        self,
        data: Any,
        columns: Optional[List[str]] = None
    ) -> Tuple[List[float], List[float], List[float], Optional[List[float]], Optional[List[str]]]:
        """
        Extract data for a bubble chart from various input formats.
        
        Args:
            data: Input data
            columns: Column names
            
        Returns:
            Tuple[List[float], List[float], List[float], Optional[List[float]], Optional[List[str]]]:
            x data, y data, sizes, colors, and labels
        """
        x_data = []
        y_data = []
        sizes = []
        colors = None
        labels = None
        
        if _HAS_PANDAS and isinstance(data, pd.DataFrame):
            # DataFrame data
            if columns and len(columns) >= 3:
                # First two columns are x and y, third is size
                x_col, y_col, size_col = columns[0], columns[1], columns[2]
                
                # Get color column if provided
                color_col = columns[3] if len(columns) > 3 else None
                
                # Get label column if provided
                label_col = columns[4] if len(columns) > 4 else None
                
                # Extract data
                df = data[[x_col, y_col, size_col]].dropna()
                
                x_data = df[x_col].tolist()
                y_data = df[y_col].tolist()
                
                # Extract sizes
                sizes_raw = df[size_col].tolist()
                
                # Normalize sizes
                if sizes_raw:
                    sizes_min = min(sizes_raw)
                    sizes_max = max(sizes_raw)
                    
                    if sizes_min != sizes_max:
                        # Scale sizes between min_size and max_size
                        min_size = kwargs.get("min_size", 10)
                        max_size = kwargs.get("max_size", 500)
                        
                        sizes = [
                            min_size + (max_size - min_size) * (size - sizes_min) / (sizes_max - sizes_min)
                            for size in sizes_raw
                        ]
                    else:
                        # All sizes are the same
                        sizes = [kwargs.get("min_size", 10) * 3] * len(sizes_raw)
                
                # Extract colors if column provided
                if color_col and color_col in data.columns:
                    colors = data[color_col].iloc[df.index].tolist()
                    
                # Extract labels if column provided
                if label_col and label_col in data.columns:
                    labels = data[label_col].iloc[df.index].astype(str).tolist()
                else:
                    # Use row indices as labels
                    labels = [str(i) for i in df.index]
            else:
                # Try to determine appropriate columns
                # Use first two numeric columns as x and y
                num_cols = data.select_dtypes(include=["number"]).columns
                
                if len(num_cols) >= 3:
                    x_col, y_col, size_col = num_cols[0], num_cols[1], num_cols[2]
                    
                    # Extract data
                    df = data[[x_col, y_col, size_col]].dropna()
                    
                    x_data = df[x_col].tolist()
                    y_data = df[y_col].tolist()
                    
                    # Extract sizes
                    sizes_raw = df[size_col].tolist()
                    
                    # Normalize sizes
                    if sizes_raw:
                        sizes_min = min(sizes_raw)
                        sizes_max = max(sizes_raw)
                        
                        if sizes_min != sizes_max:
                            # Scale sizes between min_size and max_size
                            min_size = kwargs.get("min_size", 10)
                            max_size = kwargs.get("max_size", 500)
                            
                            sizes = [
                                min_size + (max_size - min_size) * (size - sizes_min) / (sizes_max - sizes_min)
                                for size in sizes_raw
                            ]
                        else:
                            # All sizes are the same
                            sizes = [kwargs.get("min_size", 10) * 3] * len(sizes_raw)
                            
                    # Use row indices as labels
                    labels = [str(i) for i in df.index]
                    
        elif isinstance(data, dict):
            # Dictionary data
            if all(k in data for k in ["x", "y", "size"]):
                # Pre-defined x, y, and size
                x_data = data["x"]
                y_data = data["y"]
                sizes_raw = data["size"]
                
                # Normalize sizes
                if sizes_raw:
                    sizes_min = min(sizes_raw)
                    sizes_max = max(sizes_raw)
                    
                    if sizes_min != sizes_max:
                        # Scale sizes between min_size and max_size
                        min_size = kwargs.get("min_size", 10)
                        max_size = kwargs.get("max_size", 500)
                        
                        sizes = [
                            min_size + (max_size - min_size) * (size - sizes_min) / (sizes_max - sizes_min)
                            for size in sizes_raw
                        ]
                    else:
                        # All sizes are the same
                        sizes = [kwargs.get("min_size", 10) * 3] * len(sizes_raw)
                        
                # Extract colors if provided
                if "color" in data:
                    colors = data["color"]
                    
                # Extract labels if provided
                if "labels" in data:
                    labels = data["labels"]
                    
        elif isinstance(data, (list, tuple)):
            # List data
            if all(isinstance(item, dict) for item in data):
                # List of dictionaries
                if all(all(k in item for k in ["x", "y", "size"]) for item in data):
                    # Each dictionary has x, y, and size
                    x_data = [item["x"] for item in data]
                    y_data = [item["y"] for item in data]
                    sizes_raw = [item["size"] for item in data]
                    
                    # Normalize sizes
                    if sizes_raw:
                        sizes_min = min(sizes_raw)
                        sizes_max = max(sizes_raw)
                        
                        if sizes_min != sizes_max:
                            # Scale sizes between min_size and max_size
                            min_size = kwargs.get("min_size", 10)
                            max_size = kwargs.get("max_size", 500)
                            
                            sizes = [
                                min_size + (max_size - min_size) * (size - sizes_min) / (sizes_max - sizes_min)
                                for size in sizes_raw
                            ]
                        else:
                            # All sizes are the same
                            sizes = [kwargs.get("min_size", 10) * 3] * len(sizes_raw)
                            
                    # Extract colors if provided
                    if all("color" in item for item in data):
                        colors = [item["color"] for item in data]
                        
                    # Extract labels if provided
                    if all("label" in item for item in data):
                        labels = [item["label"] for item in data]
            elif all(isinstance(item, (list, tuple)) for item in data):
                # List of lists/tuples
                if all(len(item) >= 3 for item in data):
                    # Each item has at least x, y, and size
                    x_data = [item[0] for item in data]
                    y_data = [item[1] for item in data]
                    sizes_raw = [item[2] for item in data]
                    
                    # Normalize sizes
                    if sizes_raw:
                        sizes_min = min(sizes_raw)
                        sizes_max = max(sizes_raw)
                        
                        if sizes_min != sizes_max:
                            # Scale sizes between min_size and max_size
                            min_size = kwargs.get("min_size", 10)
                            max_size = kwargs.get("max_size", 500)
                            
                            sizes = [
                                min_size + (max_size - min_size) * (size - sizes_min) / (sizes_max - sizes_min)
                                for size in sizes_raw
                            ]
                        else:
                            # All sizes are the same
                            sizes = [kwargs.get("min_size", 10) * 3] * len(sizes_raw)
                            
                    # Extract colors if provided (fourth element)
                    if all(len(item) > 3 for item in data):
                        colors = [item[3] for item in data]
                        
                    # Extract labels if provided (fifth element)
                    if all(len(item) > 4 for item in data):
                        labels = [str(item[4]) for item in data]
                        
        # Handle empty data
        if not x_data or not y_data or not sizes:
            # Generate dummy data
            n_points = 10
            
            x_data = np.random.uniform(0, 10, n_points).tolist()
            y_data = np.random.uniform(0, 10, n_points).tolist()
            sizes = np.random.uniform(kwargs.get("min_size", 10), kwargs.get("max_size", 500), n_points).tolist()
            colors = None
            labels = [f"Point {i+1}" for i in range(n_points)]
            
        return x_data, y_data, sizes, colors, labels
    
    def _extract_insights_from_bubble_chart(
        self,
        x_data: List[float],
        y_data: List[float],
        sizes: List[float],
        colors: Optional[List[float]],
        labels: Optional[List[str]]
    ) -> Dict[str, Any]:
        """
        Extract insights from bubble chart data.
        
        Args:
            x_data: X-axis data
            y_data: Y-axis data
            sizes: Bubble sizes
            colors: Color values
            labels: Data labels
            
        Returns:
            Dict[str, Any]: Extracted insights
        """
        insights = {}
        
        if not x_data or not y_data or not sizes:
            return insights
            
        # Calculate basic statistics
        insights["x_axis"] = {
            "min": float(np.min(x_data)),
            "max": float(np.max(x_data)),
            "mean": float(np.mean(x_data)),
            "std_dev": float(np.std(x_data))
        }
        
        insights["y_axis"] = {
            "min": float(np.min(y_data)),
            "max": float(np.max(y_data)),
            "mean": float(np.mean(y_data)),
            "std_dev": float(np.std(y_data))
        }
        
        insights["sizes"] = {
            "min": float(np.min(sizes)),
            "max": float(np.max(sizes)),
            "mean": float(np.mean(sizes)),
            "std_dev": float(np.std(sizes))
        }
        
        if colors is not None:
            insights["colors"] = {
                "min": float(np.min(colors)),
                "max": float(np.max(colors)),
                "mean": float(np.mean(colors)),
                "std_dev": float(np.std(colors))
            }
            
        # Find notable points
        points = []
        
        for i in range(len(x_data)):
            point = {
                "x": float(x_data[i]),
                "y": float(y_data[i]),
                "size": float(sizes[i])
            }
            
            if colors is not None:
                point["color"] = float(colors[i])
                
            if labels is not None:
                point["label"] = labels[i]
                
            points.append(point)
            
        # Sort points by size (descending)
        points.sort(key=lambda p: p["size"], reverse=True)
        
        # Extract top points by size
        insights["notable_points"] = points[:5]  # Top 5 by size
        
        # Calculate correlation between variables
        corr_matrix = {}
        
        # X and Y correlation
        if len(x_data) > 1:
            corr_xy = np.corrcoef(x_data, y_data)[0, 1]
            corr_matrix["x_y"] = {
                "correlation": float(corr_xy),
                "strength": "strong" if abs(corr_xy) > 0.7 else 
                          "moderate" if abs(corr_xy) > 0.3 else "weak",
                "type": "positive" if corr_xy > 0 else "negative"
            }
            
        # X and Size correlation
        if len(x_data) > 1:
            corr_xsize = np.corrcoef(x_data, sizes)[0, 1]
            corr_matrix["x_size"] = {
                "correlation": float(corr_xsize),
                "strength": "strong" if abs(corr_xsize) > 0.7 else 
                          "moderate" if abs(corr_xsize) > 0.3 else "weak",
                "type": "positive" if corr_xsize > 0 else "negative"
            }
            
        # Y and Size correlation
        if len(y_data) > 1:
            corr_ysize = np.corrcoef(y_data, sizes)[0, 1]
            corr_matrix["y_size"] = {
                "correlation": float(corr_ysize),
                "strength": "strong" if abs(corr_ysize) > 0.7 else 
                          "moderate" if abs(corr_ysize) > 0.3 else "weak",
                "type": "positive" if corr_ysize > 0 else "negative"
            }
            
        # Colors correlations if provided
        if colors is not None:
            # X and Color correlation
            if len(x_data) > 1:
                corr_xcolor = np.corrcoef(x_data, colors)[0, 1]
                corr_matrix["x_color"] = {
                    "correlation": float(corr_xcolor),
                    "strength": "strong" if abs(corr_xcolor) > 0.7 else 
                              "moderate" if abs(corr_xcolor) > 0.3 else "weak",
                    "type": "positive" if corr_xcolor > 0 else "negative"
                }
                
            # Y and Color correlation
            if len(y_data) > 1:
                corr_ycolor = np.corrcoef(y_data, colors)[0, 1]
                corr_matrix["y_color"] = {
                    "correlation": float(corr_ycolor),
                    "strength": "strong" if abs(corr_ycolor) > 0.7 else 
                              "moderate" if abs(corr_ycolor) > 0.3 else "weak",
                    "type": "positive" if corr_ycolor > 0 else "negative"
                }
                
            # Size and Color correlation
            if len(sizes) > 1:
                corr_sizecolor = np.corrcoef(sizes, colors)[0, 1]
                corr_matrix["size_color"] = {
                    "correlation": float(corr_sizecolor),
                    "strength": "strong" if abs(corr_sizecolor) > 0.7 else 
                              "moderate" if abs(corr_sizecolor) > 0.3 else "weak",
                    "type": "positive" if corr_sizecolor > 0 else "negative"
                }
                
        insights["correlations"] = corr_matrix
        
        # Find clusters or patterns
        try:
            # Use KMeans to identify clusters
            from sklearn.cluster import KMeans
            
            # Combine features
            features = np.column_stack((x_data, y_data))
            
            # Add sizes as feature if they vary significantly
            size_range = max(sizes) - min(sizes)
            if size_range > 0.1 * max(sizes):
                features = np.column_stack((features, sizes))
                
            # Add colors as feature if provided
            if colors is not None:
                features = np.column_stack((features, colors))
                
            # Standardize features
            from sklearn.preprocessing import StandardScaler
            scaler = StandardScaler()
            features_scaled = scaler.fit_transform(features)
            
            # Determine number of clusters (max 5)
            n_clusters = min(5, max(2, len(x_data) // 5))
            
            # Apply KMeans
            kmeans = KMeans(n_clusters=n_clusters, random_state=42)
            clusters = kmeans.fit_predict(features_scaled)
            
            # Calculate cluster statistics
            cluster_stats = []
            
            for i in range(n_clusters):
                cluster_indices = np.where(clusters == i)[0]
                
                cluster_stats.append({
                    "cluster": i,
                    "size": len(cluster_indices),
                    "percentage": len(cluster_indices) / len(x_data) * 100,
                    "x_mean": float(np.mean([x_data[j] for j in cluster_indices])),
                    "y_mean": float(np.mean([y_data[j] for j in cluster_indices])),
                    "size_mean": float(np.mean([sizes[j] for j in cluster_indices])),
                    "color_mean": float(np.mean([colors[j] for j in cluster_indices])) if colors is not None else None,
                    "points": [
                        {
                            "index": int(j),
                            "x": float(x_data[j]),
                            "y": float(y_data[j]),
                            "size": float(sizes[j]),
                            "color": float(colors[j]) if colors is not None else None,
                            "label": labels[j] if labels is not None else f"Point {j}"
                        }
                        for j in cluster_indices[:5]  # Top 5 points per cluster
                    ]
                })
                
            insights["clusters"] = cluster_stats
        except:
            # Skip clustering if it fails
            pass
            
        return insights
    
    def create_network_graph(
        self,
        data: Any,
        columns: Optional[List[str]] = None,
        title: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Create a network graph visualization.
        
        Args:
            data: Input data with nodes and edges
            columns: Column names (unused but kept for consistency)
            title: Chart title
            **kwargs: Additional parameters
            
        Returns:
            Dict[str, Any]: Visualization output
        """
        # Check if matplotlib and networkx are available
        if not _HAS_MATPLOTLIB:
            raise ImportError("Matplotlib is required for network graphs")
            
        if not _HAS_NETWORKX:
            raise ImportError("NetworkX is required for network graphs")
            
        # Process parameters
        width = kwargs.get("width", self.config.get("default_width"))
        height = kwargs.get("height", self.config.get("default_height"))
        node_size = kwargs.get("node_size", self.config.get("node_size", 500))
        edge_width = kwargs.get("edge_width", self.config.get("edge_width", 1.0))
        directed = kwargs.get("directed", self.config.get("directed_graphs", False))
        layout_type = kwargs.get("layout", self.config.get("graph_layout", "spring"))
        layout_k = kwargs.get("k", self.config.get("graph_k", None))
        show_labels = kwargs.get("show_labels", self.config.get("show_data_labels", True))
        
        # Create figure and axes
        fig, ax = self.layout_manager.create_figure("regular", width=width, height=height)
        
        # Extract data and create graph
        G, node_attrs, edge_attrs = self._extract_data_for_network_graph(data, directed)
        
        # Default node and edge attributes if not provided
        node_colors = node_attrs.get("colors", [self.color_manager.get_color("primary")] * len(G.nodes()))
        node_sizes = node_attrs.get("sizes", [node_size] * len(G.nodes()))
        node_labels = node_attrs.get("labels", {})
        
        edge_colors = edge_attrs.get("colors", [self.color_manager.get_color("text")] * len(G.edges()))
        edge_widths = edge_attrs.get("widths", [edge_width] * len(G.edges()))
        
        # Compute layout
        pos = self._compute_graph_layout(G, layout_type, layout_k, **kwargs)
        
        # Draw nodes
        nodes = nx.draw_networkx_nodes(
            G, 
            pos, 
            ax=ax,
            node_size=node_sizes,
            node_color=node_colors,
            alpha=kwargs.get("node_alpha", 0.8),
            edgecolors=kwargs.get("node_edge_color", "white"),
            linewidths=kwargs.get("node_edge_width", 1.0)
        )
        
        # Draw edges
        if directed:
            edges = nx.draw_networkx_edges(
                G, 
                pos, 
                ax=ax,
                width=edge_widths,
                edge_color=edge_colors,
                alpha=kwargs.get("edge_alpha", 0.6),
                arrows=True,
                arrowsize=kwargs.get("arrow_size", 10),
                arrowstyle=kwargs.get("arrow_style", "-|>")
            )
        else:
            edges = nx.draw_networkx_edges(
                G, 
                pos, 
                ax=ax,
                width=edge_widths,
                edge_color=edge_colors,
                alpha=kwargs.get("edge_alpha", 0.6)
            )
            
        # Draw labels if requested
        if show_labels:
            nx.draw_networkx_labels(
                G, 
                pos, 
                ax=ax,
                labels=node_labels,
                font_size=kwargs.get("label_font_size", self.config.get("annotation_font_size", 10)),
                font_color=kwargs.get("label_color", self.color_manager.get_color("text")),
                font_weight=kwargs.get("label_weight", "normal")
            )
            
        # Set title
        if title:
            ax.set_title(title, fontsize=self.config.get("title_font_size", 16))
            
        # Remove axis ticks and labels
        ax.set_xticks([])
        ax.set_yticks([])
        ax.axis('off')
        
        # Tight layout
        fig.tight_layout()
        
        # Extract insights
        insights = self._extract_insights_from_network_graph(G, node_attrs, edge_attrs)
        
        return {
            "figure": fig,
            "insights": insights
        }
    
    def _extract_data_for_network_graph(
        self,
        data: Any,
        directed: bool = False
    ) -> Tuple[Any, Dict[str, Any], Dict[str, Any]]:
        """
        Extract data for a network graph from various input formats.
        
        Args:
            data: Input data
            directed: Whether the graph is directed
            
        Returns:
            Tuple[Any, Dict[str, Any], Dict[str, Any]]:
            NetworkX graph, node attributes, and edge attributes
        """
        # Create a new graph
        if directed:
            G = nx.DiGraph()
        else:
            G = nx.Graph()
            
        # Node attributes
        node_attrs = {
            "colors": [],
            "sizes": [],
            "labels": {}
        }
        
        # Edge attributes
        edge_attrs = {
            "colors": [],
            "widths": []
        }
        
        if isinstance(data, dict):
            # Dictionary data
            nodes = data.get("nodes", [])
            edges = data.get("edges", [])
            
            # Add nodes
            if nodes:
                # Check node format
                if all(isinstance(node, dict) for node in nodes):
                    # Nodes are dictionaries with attributes
                    for node in nodes:
                        node_id = node.get("id", None)
                        if node_id is not None:
                            # Add node
                            G.add_node(node_id)
                            
                            # Set attributes
                            for key, value in node.items():
                                if key != "id":
                                    nx.set_node_attributes(G, {node_id: value}, key)
                                    
                            # Store visual attributes
                            node_attrs["colors"].append(node.get("color", self.color_manager.get_color("primary")))
                            node_attrs["sizes"].append(node.get("size", self.config.get("node_size", 500)))
                            node_attrs["labels"][node_id] = node.get("label", str(node_id))
                else:
                    # Nodes are simple values
                    for i, node in enumerate(nodes):
                        # Add node
                        G.add_node(node)
                        
                        # Store visual attributes
                        node_attrs["colors"].append(self.color_manager.get_color("primary"))
                        node_attrs["sizes"].append(self.config.get("node_size", 500))
                        node_attrs["labels"][node] = str(node)
                        
            # Add edges
            if edges:
                # Check edge format
                if all(isinstance(edge, dict) for edge in edges):
                    # Edges are dictionaries with attributes
                    for edge in edges:
                        source = edge.get("source", None)
                        target = edge.get("target", None)
                        
                        if source is not None and target is not None:
                            # Add edge
                            G.add_edge(source, target)
                            
                            # Set attributes
                            for key, value in edge.items():
                                if key not in ["source", "target"]:
                                    nx.set_edge_attributes(G, {(source, target): value}, key)
                                    
                            # Store visual attributes
                            edge_attrs["colors"].append(edge.get("color", self.color_manager.get_color("text")))
                            edge_attrs["widths"].append(edge.get("width", self.config.get("edge_width", 1.0)))
                else:
                    # Edges are simple pairs
                    for i, edge in enumerate(edges):
                        if isinstance(edge, (list, tuple)) and len(edge) >= 2:
                            source, target = edge[0], edge[1]
                            
                            # Add edge
                            G.add_edge(source, target)
                            
                            # Store visual attributes
                            edge_attrs["colors"].append(self.color_manager.get_color("text"))
                            edge_attrs["widths"].append(self.config.get("edge_width", 1.0))
        elif isinstance(data, nx.Graph):
            # Already a NetworkX graph
            G = data.copy()
            
            # Extract node attributes
            node_colors = []
            node_sizes = []
            node_labels = {}
            
            for node in G.nodes():
                node_attrs_dict = G.nodes[node]
                
                # Extract visual attributes
                node_colors.append(node_attrs_dict.get("color", self.color_manager.get_color("primary")))
                node_sizes.append(node_attrs_dict.get("size", self.config.get("node_size", 500)))
                node_labels[node] = node_attrs_dict.get("label", str(node))
                
            node_attrs["colors"] = node_colors
            node_attrs["sizes"] = node_sizes
            node_attrs["labels"] = node_labels
            
            # Extract edge attributes
            edge_colors = []
            edge_widths = []
            
            for edge in G.edges():
                edge_attrs_dict = G.edges[edge]
                
                # Extract visual attributes
                edge_colors.append(edge_attrs_dict.get("color", self.color_manager.get_color("text")))
                edge_widths.append(edge_attrs_dict.get("width", self.config.get("edge_width", 1.0)))
                
            edge_attrs["colors"] = edge_colors
            edge_attrs["widths"] = edge_widths
        else:
            # Try to create a graph from adjacency matrix/list
            try:
                if isinstance(data, (list, tuple)):
                    if all(isinstance(row, (list, tuple)) for row in data):
                        # Adjacency matrix
                        # Convert to numpy array
                        adj_matrix = np.array(data)
                        
                        # Create graph from adjacency matrix
                        if directed:
                            G = nx.from_numpy_matrix(adj_matrix, create_using=nx.DiGraph)
                        else:
                            G = nx.from_numpy_matrix(adj_matrix)
                    elif all(isinstance(item, (list, tuple)) for item in data) and all(len(item) >= 2 for item in data):
                        # Edge list
                        for edge in data:
                            source, target = edge[0], edge[1]
                            
                            # Add edge
                            G.add_edge(source, target)
                            
                            # Store visual attributes
                            edge_attrs["colors"].append(self.color_manager.get_color("text"))
                            edge_attrs["widths"].append(self.config.get("edge_width", 1.0))
            except:
                # Failed to create graph from data
                raise ValueError("Invalid data format for network graph")
                
        # Generate default node attributes if empty
        if not node_attrs["colors"]:
            node_attrs["colors"] = [self.color_manager.get_color("primary")] * len(G.nodes())
            
        if not node_attrs["sizes"]:
            node_attrs["sizes"] = [self.config.get("node_size", 500)] * len(G.nodes())
            
        if not node_attrs["labels"]:
            node_attrs["labels"] = {node: str(node) for node in G.nodes()}
            
        # Generate default edge attributes if empty
        if not edge_attrs["colors"]:
            edge_attrs["colors"] = [self.color_manager.get_color("text")] * len(G.edges())
            
        if not edge_attrs["widths"]:
            edge_attrs["widths"] = [self.config.get("edge_width", 1.0)] * len(G.edges())
            
        # Handle empty graph
        if len(G.nodes()) == 0:
            # Create dummy graph
            G.add_node(0)
            G.add_node(1)
            G.add_edge(0, 1)
            
            node_attrs["colors"] = [self.color_manager.get_color("primary")] * 2
            node_attrs["sizes"] = [self.config.get("node_size", 500)] * 2
            node_attrs["labels"] = {0: "Node 1", 1: "Node 2"}
            
            edge_attrs["colors"] = [self.color_manager.get_color("text")]
            edge_attrs["widths"] = [self.config.get("edge_width", 1.0)]
            
        return G, node_attrs, edge_attrs
    
    def _compute_graph_layout(
        self,
        G: Any,
        layout_type: str,
        k: Optional[float] = None,
        **kwargs
    ) -> Dict[Any, Tuple[float, float]]:
        """
        Compute graph layout.
        
        Args:
            G: NetworkX graph
            layout_type: Layout algorithm
            k: Parameter for spring layout
            **kwargs: Additional parameters
            
        Returns:
            Dict[Any, Tuple[float, float]]: Node positions
        """
        # Get layout function
        layout_funcs = {
            "spring": nx.spring_layout,
            "circular": nx.circular_layout,
            "kamada_kawai": nx.kamada_kawai_layout,
            "planar": nx.planar_layout,
            "random": nx.random_layout,
            "spectral": nx.spectral_layout,
            "shell": nx.shell_layout,
        }
        
        layout_func = layout_funcs.get(layout_type, nx.spring_layout)
        
        # Compute layout
        if layout_type == "spring":
            # Spring layout has additional parameters
            pos = layout_func(
                G, 
                k=k,
                iterations=kwargs.get("iterations", 50),
                seed=kwargs.get("seed", 42)
            )
        elif layout_type == "shell":
            # Shell layout requires node grouping
            if "node_groups" in kwargs:
                pos = layout_func(G, nlist=kwargs["node_groups"])
            else:
                pos = layout_func(G)
        else:
            # Other layouts
            pos = layout_func(G)
            
        return pos
    
    def _extract_insights_from_network_graph(
        self,
        G: Any,
        node_attrs: Dict[str, Any],
        edge_attrs: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Extract insights from network graph data.
        
        Args:
            G: NetworkX graph
            node_attrs: Node attributes
            edge_attrs: Edge attributes
            
        Returns:
            Dict[str, Any]: Extracted insights
        """
        insights = {}
        
        if len(G.nodes()) == 0:
            return insights
            
        # Basic graph properties
        insights["graph_properties"] = {
            "n_nodes": len(G.nodes()),
            "n_edges": len(G.edges()),
            "density": nx.density(G),
            "is_directed": G.is_directed()
        }
        
        # Connected components
        if not G.is_directed():
            n_components = nx.number_connected_components(G)
            insights["graph_properties"]["connected_components"] = n_components
            insights["graph_properties"]["is_connected"] = nx.is_connected(G)
            
            if not nx.is_connected(G) and n_components <= 10:
                # Get component sizes
                components = list(nx.connected_components(G))
                component_sizes = [len(c) for c in components]
                
                insights["components"] = {
                    "count": n_components,
                    "sizes": component_sizes,
                    "largest_size": max(component_sizes)
                }
        else:
            # Directed graph - check strong/weak connectivity
            insights["graph_properties"]["is_strongly_connected"] = nx.is_strongly_connected(G)
            insights["graph_properties"]["is_weakly_connected"] = nx.is_weakly_connected(G)
            
            n_strong = nx.number_strongly_connected_components(G)
            n_weak = nx.number_weakly_connected_components(G)
            
            insights["graph_properties"]["strongly_connected_components"] = n_strong
            insights["graph_properties"]["weakly_connected_components"] = n_weak
            
        # Node centrality measures
        try:
            # Degree centrality
            if G.is_directed():
                in_degree = nx.in_degree_centrality(G)
                out_degree = nx.out_degree_centrality(G)
                
                # Find nodes with highest centrality
                top_in_degree = sorted(in_degree.items(), key=lambda x: x[1], reverse=True)[:5]
                top_out_degree = sorted(out_degree.items(), key=lambda x: x[1], reverse=True)[:5]
                
                insights["centrality"] = {
                    "in_degree": {
                        "top_nodes": [
                            {"node": str(node), "centrality": float(centrality)}
                            for node, centrality in top_in_degree
                        ]
                    },
                    "out_degree": {
                        "top_nodes": [
                            {"node": str(node), "centrality": float(centrality)}
                            for node, centrality in top_out_degree
                        ]
                    }
                }
            else:
                degree = nx.degree_centrality(G)
                
                # Find nodes with highest centrality
                top_degree = sorted(degree.items(), key=lambda x: x[1], reverse=True)[:5]
                
                insights["centrality"] = {
                    "degree": {
                        "top_nodes": [
                            {"node": str(node), "centrality": float(centrality)}
                            for node, centrality in top_degree
                        ]
                    }
                }
                
            # Betweenness centrality (for medium sized graphs)
            if len(G.nodes()) <= 1000:
                betweenness = nx.betweenness_centrality(G)
                
                # Find nodes with highest centrality
                top_betweenness = sorted(betweenness.items(), key=lambda x: x[1], reverse=True)[:5]
                
                insights["centrality"]["betweenness"] = {
                    "top_nodes": [
                        {"node": str(node), "centrality": float(centrality)}
                        for node, centrality in top_betweenness
                    ]
                }
                
            # Closeness centrality (for medium sized graphs)
            if len(G.nodes()) <= 1000:
                try:
                    closeness = nx.closeness_centrality(G)
                    
                    # Find nodes with highest centrality
                    top_closeness = sorted(closeness.items(), key=lambda x: x[1], reverse=True)[:5]
                    
                    insights["centrality"]["closeness"] = {
                        "top_nodes": [
                            {"node": str(node), "centrality": float(centrality)}
                            for node, centrality in top_closeness
                        ]
                    }
                except:
                    # Skip closeness if it fails (e.g. disconnected graph)
                    pass
        except:
            # Skip centrality if it fails
            pass
            
        # Community detection (for medium sized graphs)
        if len(G.nodes()) <= 1000:
            try:
                # Convert to undirected for community detection if needed
                G_undirected = G.to_undirected() if G.is_directed() else G
                
                # Use Louvain method for community detection
                from networkx.algorithms import community
                
                communities = community.greedy_modularity_communities(G_undirected)
                
                community_sizes = [len(c) for c in communities]
                
                insights["communities"] = {
                    "count": len(communities),
                    "sizes": community_sizes,
                    "modularity": community.modularity(G_undirected, communities)
                }
            except:
                # Skip community detection if it fails
                pass
                
        return insights
    
    def create_adjacency_matrix(
        self,
        data: Any,
        columns: Optional[List[str]] = None,
        title: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Create an adjacency matrix visualization.
        
        Args:
            data: Input data with nodes and edges
            columns: Column names (unused but kept for consistency)
            title: Chart title
            **kwargs: Additional parameters
            
        Returns:
            Dict[str, Any]: Visualization output
        """
        # Check if matplotlib and networkx are available
        if not _HAS_MATPLOTLIB:
            raise ImportError("Matplotlib is required for adjacency matrix visualizations")
            
        if not _HAS_NETWORKX:
            raise ImportError("NetworkX is required for adjacency matrix visualizations")
            
        # Process parameters
        width = kwargs.get("width", self.config.get("default_width"))
        height = kwargs.get("height", self.config.get("default_height"))
        show_values = kwargs.get("show_values", self.config.get("show_data_labels", True))
        value_format = kwargs.get("value_format", "{:.1f}")
        cmap = kwargs.get("cmap", self.color_manager.get_colormap())
        
        # Create figure and axes
        fig, ax = self.layout_manager.create_figure("regular", width=width, height=height)
        
        # Extract data and create graph
        G, node_attrs, _ = self._extract_data_for_network_graph(data, kwargs.get("directed", False))
        
        # Get node labels
        node_labels = list(node_attrs["labels"].keys())
        
        # Create adjacency matrix
        adj_matrix = nx.to_numpy_array(G, nodelist=node_labels)
        
        # Create heatmap
        im = ax.imshow(adj_matrix, cmap=cmap)
        
        # Add colorbar
        if kwargs.get("show_colorbar", True):
            cbar = plt.colorbar(im, ax=ax)
            cbar.set_label(kwargs.get("colorbar_label", "Edge Weight"))
            
        # Set ticks and labels
        ax.set_xticks(np.arange(len(node_labels)))
        ax.set_yticks(np.arange(len(node_labels)))
        ax.set_xticklabels([node_attrs["labels"][node] for node in node_labels])
        ax.set_yticklabels([node_attrs["labels"][node] for node in node_labels])
        
        # Rotate x-axis labels if needed
        if len(node_labels) > 5 or kwargs.get("rotate_xlabels", True):
            plt.setp(ax.get_xticklabels(), rotation=45, ha="right", rotation_mode="anchor")
            
        # Add value annotations
        if show_values:
            threshold = im.norm(adj_matrix.max()) / 2
            
            for i in range(len(node_labels)):
                for j in range(len(node_labels)):
                    value = adj_matrix[i, j]
                    
                    # Only show non-zero values
                    if value > 0:
                        text_color = "white" if im.norm(value) > threshold else "black"
                        
                        ax.text(j, i, value_format.format(value),
                                ha="center", va="center",
                                color=text_color,
                                fontsize=kwargs.get("value_font_size", self.config.get("annotation_font_size", 10)))
                                
        # Set title
        if title:
            ax.set_title(title, fontsize=self.config.get("title_font_size", 16))
            
        # Tight layout
        fig.tight_layout()
        
        # Extract insights
        insights = self._extract_insights_from_network_graph(G, node_attrs, {})
        
        return {
            "figure": fig,
            "insights": insights
        }
    
    def create_chord_diagram(
        self,
        data: Any,
        columns: Optional[List[str]] = None,
        title: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Create a chord diagram visualization.
        
        Args:
            data: Input data with flow matrix
            columns: Column names (node labels)
            title: Chart title
            **kwargs: Additional parameters
            
        Returns:
            Dict[str, Any]: Visualization output
        """
        # Check if matplotlib is available
        if not _HAS_MATPLOTLIB:
            raise ImportError("Matplotlib is required for chord diagrams")
            
        # Process parameters
        width = kwargs.get("width", self.config.get("default_width"))
        height = kwargs.get("height", self.config.get("default_height"))
        show_labels = kwargs.get("show_labels", self.config.get("show_data_labels", True))
        show_grid = kwargs.get("show_grid", False)
        
        # Create figure and axes
        fig, ax = self.layout_manager.create_figure("regular", width=width, height=height)
        
        # Extract data
        matrix, labels = self._extract_data_for_chord_diagram(data, columns)
        
        # Get colors
        colors = kwargs.get("colors", self.color_manager.get_categorical_palette(len(labels)))
        
        # Create chord diagram
        self._plot_chord_diagram(ax, matrix, labels, colors, show_labels=show_labels)
            
        # Set title
        if title:
            ax.set_title(title, fontsize=self.config.get("title_font_size", 16))
            
        # Turn off axis if no grid
        if not show_grid:
            ax.axis('off')
            
        # Set aspect ratio to equal
        ax.set_aspect('equal')
        
        # Tight layout
        fig.tight_layout()
        
        # Extract insights
        insights = self._extract_insights_from_chord_diagram(matrix, labels)
        
        return {
            "figure": fig,
            "insights": insights
        }
    
    def _extract_data_for_chord_diagram(
        self,
        data: Any,
        columns: Optional[List[str]] = None
    ) -> Tuple[np.ndarray, List[str]]:
        """
        Extract data for a chord diagram from various input formats.
        
        Args:
            data: Input data
            columns: Column names
            
        Returns:
            Tuple[np.ndarray, List[str]]: Flow matrix and labels
        """
        matrix = None
        labels = []
        
        if isinstance(data, dict):
            # Dictionary data
            if "matrix" in data:
                # Pre-defined matrix
                matrix = np.array(data["matrix"])
                labels = data.get("labels", [f"Node {i+1}" for i in range(matrix.shape[0])])
        elif isinstance(data, (list, tuple)):
            # List data
            if all(isinstance(row, (list, tuple)) for row in data):
                # List of lists - adjacency matrix
                matrix = np.array(data)
                labels = columns if columns else [f"Node {i+1}" for i in range(matrix.shape[0])]
        elif _HAS_NETWORKX and isinstance(data, nx.Graph):
            # NetworkX graph
            # Get node list
            nodes = list(data.nodes())
            
            # Create adjacency matrix
            matrix = nx.to_numpy_array(data, nodelist=nodes)
            
            # Get labels
            labels = []
            for node in nodes:
                label = data.nodes[node].get("label", str(node))
                labels.append(label)
                
        # Handle empty data
        if matrix is None:
            # Generate dummy data
            n = 5
            matrix = np.random.rand(n, n)
            
            # Make it symmetric for undirected case
            matrix = (matrix + matrix.T) / 2
            
            # Set diagonal to zero (no self-loops)
            np.fill_diagonal(matrix, 0)
            
            labels = [f"Node {i+1}" for i in range(n)]
            
        return matrix, labels
    
    def _plot_chord_diagram(
        self,
        ax: Any,
        matrix: np.ndarray,
        labels: List[str],
        colors: List[str],
        show_labels: bool = True
    ) -> None:
        """
        Plot a chord diagram using matplotlib.
        
        Args:
            ax: Matplotlib axes
            matrix: Flow matrix
            labels: Node labels
            colors: Node colors
            show_labels: Whether to show labels
        """
        n = matrix.shape[0]
        
        # Create a circle with n segments
        theta = np.linspace(0, 2*np.pi, n, endpoint=False)
        
        # Plot the outer ring segments
        radii = np.ones(n)
        width = 0.1
        
        # Calculate node sizes based on row/column sums
        node_sizes = []
        for i in range(n):
            # Use sum of incoming and outgoing flows
            size = 0.5 * (np.sum(matrix[i, :]) + np.sum(matrix[:, i]))
            node_sizes.append(size)
            
        # Normalize node sizes
        if max(node_sizes) > 0:
            node_sizes = [size / max(node_sizes) * width * 4 for size in node_sizes]
        else:
            node_sizes = [width] * n
            
        # Plot the outer ring segments
        for i in range(n):
            start_angle = theta[i] - node_sizes[i] / 2
            end_angle = theta[i] + node_sizes[i] / 2
            
            wedge = matplotlib.patches.Wedge(
                center=(0, 0),
                r=1.0,
                theta1=np.degrees(start_angle),
                theta2=np.degrees(end_angle),
                width=width,
                facecolor=colors[i % len(colors)],
                edgecolor=self.color_manager.get_color("background"),
                linewidth=1,
                alpha=0.8
            )
            
            ax.add_patch(wedge)
            
        # Add labels if requested
        if show_labels:
            for i in range(n):
                angle = theta[i]
                label_radius = 1.1  # Just outside the ring
                
                x = label_radius * np.cos(angle)
                y = label_radius * np.sin(angle)
                
                # Adjust text alignment based on angle
                ha = "left" if x < 0 else "right"
                va = "top" if y < 0 else "bottom"
                
                # Adjust angle for readability
                if x < 0:
                    text_angle = np.degrees(angle) + 180
                else:
                    text_angle = np.degrees(angle)
                    
                # Add label
                ax.text(
                    x, y, labels[i],
                    ha=ha, va=va,
                    rotation=text_angle,
                    rotation_mode="anchor",
                    fontsize=self.config.get("annotation_font_size", 10)
                )
                
        # Plot the chords
        for i in range(n):
            for j in range(n):
                if matrix[i, j] > 0:
                    # Calculate the width of the chord based on the flow value
                    max_flow = np.max(matrix)
                    rel_size = matrix[i, j] / max_flow if max_flow > 0 else 0
                    
                    # Skip very small flows
                    if rel_size < 0.05:
                        continue
                        
                    # Start and end angles for the nodes
                    start_angle_i = theta[i] - node_sizes[i] / 2
                    end_angle_i = theta[i] + node_sizes[i] / 2
                    
                    start_angle_j = theta[j] - node_sizes[j] / 2
                    end_angle_j = theta[j] + node_sizes[j] / 2
                    
                    # Interpolate angles based on flow size
                    s_i = start_angle_i + (end_angle_i - start_angle_i) * np.random.uniform(0.1, 0.9)
                    s_j = start_angle_j + (end_angle_j - start_angle_j) * np.random.uniform(0.1, 0.9)
                    
                    # Calculate control points for Bezier curve
                    r1 = 1.0 - width  # Inside the ring
                    r2 = 0.5  # Control point radius
                    
                    # Start and end points
                    x1 = r1 * np.cos(s_i)
                    y1 = r1 * np.sin(s_i)
                    
                    x2 = r1 * np.cos(s_j)
                    y2 = r1 * np.sin(s_j)
                    
                    # Control points
                    cx1 = r2 * np.cos(s_i)
                    cy1 = r2 * np.sin(s_i)
                    
                    cx2 = r2 * np.cos(s_j)
                    cy2 = r2 * np.sin(s_j)
                    
                    # Create Bezier path
                    verts = [
                        (x1, y1),
                        (cx1, cy1),
                        (cx2, cy2),
                        (x2, y2)
                    ]
                    
                    codes = [
                        matplotlib.path.Path.MOVETO,
                        matplotlib.path.Path.CURVE4,
                        matplotlib.path.Path.CURVE4,
                        matplotlib.path.Path.CURVE4
                    ]
                    
                    path = matplotlib.path.Path(verts, codes)
                    
                    # Draw the path
                    patch = matplotlib.patches.PathPatch(
                        path,
                        facecolor="none",
                        edgecolor=colors[i % len(colors)],
                        linewidth=rel_size * 5,  # Scale line width with flow
                        alpha=0.6
                    )
                    
                    ax.add_patch(patch)
                    
        # Set axis limits
        ax.set_xlim(-1.2, 1.2)
        ax.set_ylim(-1.2, 1.2)
    
    def _extract_insights_from_chord_diagram(
        self,
        matrix: np.ndarray,
        labels: List[str]
    ) -> Dict[str, Any]:
        """
        Extract insights from chord diagram data.
        
        Args:
            matrix: Flow matrix
            labels: Node labels
            
        Returns:
            Dict[str, Any]: Extracted insights
        """
        insights = {}
        
        if matrix.size == 0:
            return insights
            
        # Calculate row and column sums
        row_sums = np.sum(matrix, axis=1)
        col_sums = np.sum(matrix, axis=0)
        
        # Outgoing flows (row sums)
        outgoing_flows = []
        for i, label in enumerate(labels):
            outgoing_flows.append({
                "node": label,
                "outgoing_total": float(row_sums[i])
            })
            
        # Sort by outgoing flow
        outgoing_flows.sort(key=lambda x: x["outgoing_total"], reverse=True)
        
        # Incoming flows (column sums)
        incoming_flows = []
        for i, label in enumerate(labels):
            incoming_flows.append({
                "node": label,
                "incoming_total": float(col_sums[i])
            })
            
        # Sort by incoming flow
        incoming_flows.sort(key=lambda x: x["incoming_total"], reverse=True)
        
        # Net flows (outgoing - incoming)
        net_flows = []
        for i, label in enumerate(labels):
            net_flows.append({
                "node": label,
                "net_flow": float(row_sums[i] - col_sums[i]),
                "is_net_source": row_sums[i] > col_sums[i],
                "is_net_sink": row_sums[i] < col_sums[i]
            })
            
        # Sort by absolute net flow
        net_flows.sort(key=lambda x: abs(x["net_flow"]), reverse=True)
        
        insights["flow_analysis"] = {
            "top_outgoing": outgoing_flows[:5],  # Top 5 sources
            "top_incoming": incoming_flows[:5],  # Top 5 sinks
            "top_net_flows": net_flows[:5]  # Top 5 by net flow
        }
        
        # Find strongest individual flows
        strongest_flows = []
        
        for i in range(len(labels)):
            for j in range(len(labels)):
                if matrix[i, j] > 0:
                    strongest_flows.append({
                        "source": labels[i],
                        "target": labels[j],
                        "flow": float(matrix[i, j])
                    })
                    
        # Sort by flow strength
        strongest_flows.sort(key=lambda x: x["flow"], reverse=True)
        
        insights["strongest_flows"] = strongest_flows[:10]  # Top 10 flows
        
        # Check for symmetry
        is_symmetric = np.allclose(matrix, matrix.T)
        
        insights["matrix_properties"] = {
            "is_symmetric": is_symmetric,
            "total_flow": float(np.sum(matrix)),
            "density": float(np.count_nonzero(matrix) / matrix.size)
        }
        
        return insights
    
    def create_tree_diagram(
        self,
        data: Any,
        columns: Optional[List[str]] = None,
        title: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Create a tree diagram visualization.
        
        Args:
            data: Input data with tree structure
            columns: Column names (unused but kept for consistency)
            title: Chart title
            **kwargs: Additional parameters
            
        Returns:
            Dict[str, Any]: Visualization output
        """
        # Check if matplotlib and networkx are available
        if not _HAS_MATPLOTLIB:
            raise ImportError("Matplotlib is required for tree diagrams")
            
        if not _HAS_NETWORKX:
            raise ImportError("NetworkX is required for tree diagrams")
            
        # Process parameters
        width = kwargs.get("width", self.config.get("default_width"))
        height = kwargs.get("height", self.config.get("default_height"))
        node_size = kwargs.get("node_size", self.config.get("node_size", 500))
        edge_width = kwargs.get("edge_width", self.config.get("edge_width", 1.0))
        orientation = kwargs.get("orientation", "horizontal")
        show_labels = kwargs.get("show_labels", self.config.get("show_data_labels", True))
        
        # Create figure and axes
        fig, ax = self.layout_manager.create_figure("regular", width=width, height=height)
        
        # Extract data and create tree
        G, root, node_attrs, edge_attrs = self._extract_data_for_tree_diagram(data)
        
        # Default node and edge attributes if not provided
        node_colors = node_attrs.get("colors", [self.color_manager.get_color("primary")] * len(G.nodes()))
        node_sizes = node_attrs.get("sizes", [node_size] * len(G.nodes()))
        node_labels = node_attrs.get("labels", {})
        
        edge_colors = edge_attrs.get("colors", [self.color_manager.get_color("text")] * len(G.edges()))
        edge_widths = edge_attrs.get("widths", [edge_width] * len(G.edges()))
        
        # Compute positions
        pos = self._compute_tree_layout(G, root, orientation)
        
        # Draw nodes
        nodes = nx.draw_networkx_nodes(
            G, 
            pos, 
            ax=ax,
            node_size=node_sizes,
            node_color=node_colors,
            alpha=kwargs.get("node_alpha", 0.8),
            edgecolors=kwargs.get("node_edge_color", "white"),
            linewidths=kwargs.get("node_edge_width", 1.0)
        )
        
        # Draw edges
        edges = nx.draw_networkx_edges(
            G, 
            pos, 
            ax=ax,
            width=edge_widths,
            edge_color=edge_colors,
            alpha=kwargs.get("edge_alpha", 0.6),
            arrows=kwargs.get("show_arrows", True),
            arrowsize=kwargs.get("arrow_size", 10),
            arrowstyle=kwargs.get("arrow_style", "-|>"),
            connectionstyle="arc3,rad=0.1"
        )
        
        # Draw labels if requested
        if show_labels:
            nx.draw_networkx_labels(
                G, 
                pos, 
                ax=ax,
                labels=node_labels,
                font_size=kwargs.get("label_font_size", self.config.get("annotation_font_size", 10)),
                font_color=kwargs.get("label_color", self.color_manager.get_color("text")),
                font_weight=kwargs.get("label_weight", "normal")
            )
            
        # Set title
        if title:
            ax.set_title(title, fontsize=self.config.get("title_font_size", 16))
            
        # Remove axis ticks and labels
        ax.set_xticks([])
        ax.set_yticks([])
        ax.axis('off')
        
        # Tight layout
        fig.tight_layout()
        
        # Extract insights
        insights = self._extract_insights_from_tree_diagram(G, root)
        
        return {
            "figure": fig,
            "insights": insights
        }
    
    def _extract_data_for_tree_diagram(
        self,
        data: Any
    ) -> Tuple[Any, Any, Dict[str, Any], Dict[str, Any]]:
        """
        Extract data for a tree diagram from various input formats.
        
        Args:
            data: Input data
            
        Returns:
            Tuple[Any, Any, Dict[str, Any], Dict[str, Any]]:
            NetworkX graph, root node, node attributes, and edge attributes
        """
        # Create a new directed graph
        G = nx.DiGraph()
        
        # Node attributes
        node_attrs = {
            "colors": [],
            "sizes": [],
            "labels": {}
        }
        
        # Edge attributes
        edge_attrs = {
            "colors": [],
            "widths": []
        }
        
        root = None
        
        # Parse data based on format
        if isinstance(data, dict):
            # Dictionary data
            if "root" in data:
                # Tree with root node
                root_data = data["root"]
                
                # Check root format
                if isinstance(root_data, dict):
                    # Root is a dictionary with attributes
                    root = root_data.get("id", "root")
                    
                    # Process the tree recursively
                    self._process_tree_node(G, root_data, node_attrs, edge_attrs, parent=None)
                else:
                    # Root is a simple value
                    root = root_data
                    G.add_node(root)
                    node_attrs["labels"][root] = str(root)
                    
                    # Process children if available
                    if "children" in data:
                        for child in data["children"]:
                            if isinstance(child, dict):
                                child_id = child.get("id", f"child_{len(G.nodes())}")
                                G.add_node(child_id)
                                G.add_edge(root, child_id)
                                
                                # Set attributes
                                node_attrs["labels"][child_id] = child.get("label", str(child_id))
                                node_attrs["colors"].append(child.get("color", self.color_manager.get_color("primary")))
                                node_attrs["sizes"].append(child.get("size", self.config.get("node_size", 500)))
                                
                                # Process child's children recursively
                                self._process_tree_node(G, child, node_attrs, edge_attrs, parent=root)
                            else:
                                # Child is a simple value
                                G.add_node(child)
                                G.add_edge(root, child)
                                node_attrs["labels"][child] = str(child)
            elif "nodes" in data and "edges" in data:
                # Graph data with nodes and edges
                # Find root (node with no incoming edges)
                edges = data["edges"]
                nodes = data["nodes"]
                
                # Add nodes
                for node in nodes:
                    if isinstance(node, dict):
                        node_id = node.get("id", None)
                        if node_id is not None:
                            G.add_node(node_id)
                            
                            # Set attributes
                            node_attrs["labels"][node_id] = node.get("label", str(node_id))
                            node_attrs["colors"].append(node.get("color", self.color_manager.get_color("primary")))
                            node_attrs["sizes"].append(node.get("size", self.config.get("node_size", 500)))
                    else:
                        G.add_node(node)
                        node_attrs["labels"][node] = str(node)
                        
                # Add edges
                for edge in edges:
                    if isinstance(edge, dict):
                        source = edge.get("source", None)
                        target = edge.get("target", None)
                        
                        if source is not None and target is not None:
                            G.add_edge(source, target)
                            
                            # Set attributes
                            edge_attrs["colors"].append(edge.get("color", self.color_manager.get_color("text")))
                            edge_attrs["widths"].append(edge.get("width", self.config.get("edge_width", 1.0)))
                    else:
                        if isinstance(edge, (list, tuple)) and len(edge) >= 2:
                            source, target = edge[0], edge[1]
                            G.add_edge(source, target)
                            
                # Find root (node with no incoming edges)
                for node in G.nodes():
                    if G.in_degree(node) == 0:
                        root = node
                        break
        elif _HAS_NETWORKX and isinstance(data, nx.DiGraph):
            # Already a NetworkX graph
            G = data.copy()
            
            # Find root (node with no incoming edges)
            for node in G.nodes():
                if G.in_degree(node) == 0:
                    root = node
                    break
                    
            # Extract node attributes
            node_colors = []
            node_sizes = []
            node_labels = {}
            
            for node in G.nodes():
                node_attrs_dict = G.nodes[node]
                
                # Extract visual attributes
                node_colors.append(node_attrs_dict.get("color", self.color_manager.get_color("primary")))
                node_sizes.append(node_attrs_dict.get("size", self.config.get("node_size", 500)))
                node_labels[node] = node_attrs_dict.get("label", str(node))
                
            node_attrs["colors"] = node_colors
            node_attrs["sizes"] = node_sizes
            node_attrs["labels"] = node_labels
            
            # Extract edge attributes
            edge_colors = []
            edge_widths = []
            
            for edge in G.edges():
                edge_attrs_dict = G.edges[edge]
                
                # Extract visual attributes
                edge_colors.append(edge_attrs_dict.get("color", self.color_manager.get_color("text")))
                edge_widths.append(edge_attrs_dict.get("width", self.config.get("edge_width", 1.0)))
                
            edge_attrs["colors"] = edge_colors
            edge_attrs["widths"] = edge_widths
            
        # Handle empty data or no root
        if len(G.nodes()) == 0 or root is None:
            # Create dummy tree
            G = nx.DiGraph()
            root = "Root"
            G.add_node(root)
            
            # Add some children
            for i in range(3):
                child = f"Child {i+1}"
                G.add_node(child)
                G.add_edge(root, child)
                
                # Add grandchildren to first two children
                if i < 2:
                    for j in range(2):
                        grandchild = f"Grandchild {i+1}-{j+1}"
                        G.add_node(grandchild)
                        G.add_edge(child, grandchild)
                        
            # Generate default attributes
            node_attrs["colors"] = [self.color_manager.get_color("primary")] * len(G.nodes())
            node_attrs["sizes"] = [self.config.get("node_size", 500)] * len(G.nodes())
            node_attrs["labels"] = {node: str(node) for node in G.nodes()}
            
            edge_attrs["colors"] = [self.color_manager.get_color("text")] * len(G.edges())
            edge_attrs["widths"] = [self.config.get("edge_width", 1.0)] * len(G.edges())
            
        return G, root, node_attrs, edge_attrs
    
    def _process_tree_node(
        self,
        G: Any,
        node_data: Dict[str, Any],
        node_attrs: Dict[str, Any],
        edge_attrs: Dict[str, Any],
        parent: Optional[Any] = None
    ) -> Any:
        """
        Recursively process a tree node and its children.
        
        Args:
            G: NetworkX graph
            node_data: Node data dictionary
            node_attrs: Node attributes
            edge_attrs: Edge attributes
            parent: Parent node
            
        Returns:
            Any: Node ID
        """
        # Get node ID
        node_id = node_data.get("id", f"node_{len(G.nodes())}")
        
        # Add node
        G.add_node(node_id)
        
        # Set attributes
        node_attrs["labels"][node_id] = node_data.get("label", str(node_id))
        node_attrs["colors"].append(node_data.get("color", self.color_manager.get_color("primary")))
        node_attrs["sizes"].append(node_data.get("size", self.config.get("node_size", 500)))
        
        # Add edge from parent if exists
        if parent is not None:
            G.add_edge(parent, node_id)
            
            # Set edge attributes
            edge_attrs["colors"].append(node_data.get("edge_color", self.color_manager.get_color("text")))
            edge_attrs["widths"].append(node_data.get("edge_width", self.config.get("edge_width", 1.0)))
            
        # Process children recursively
        if "children" in node_data:
            children = node_data["children"]
            
            for child in children:
                if isinstance(child, dict):
                    self._process_tree_node(G, child, node_attrs, edge_attrs, parent=node_id)
                else:
                    # Child is a simple value
                    child_id = child
                    G.add_node(child_id)
                    G.add_edge(node_id, child_id)
                    node_attrs["labels"][child_id] = str(child_id)
                    
        return node_id
    
    def _compute_tree_layout(
        self,
        G: Any,
        root: Any,
        orientation: str = "horizontal"
    ) -> Dict[Any, Tuple[float, float]]:
        """
        Compute tree layout.
        
        Args:
            G: NetworkX graph
            root: Root node
            orientation: Layout orientation
            
        Returns:
            Dict[Any, Tuple[float, float]]: Node positions
        """
        # Compute layout using graph_tool or custom code
        if orientation == "vertical":
            # Use default drawing
            pos = nx.drawing.nx_agraph.graphviz_layout(G, prog="dot", root=root)
        else:
            # Horizontal layout
            pos = nx.drawing.nx_agraph.graphviz_layout(G, prog="dot", root=root)
            
            # Transpose x and y for horizontal layout
            pos = {node: (y, x) for node, (x, y) in pos.items()}
            
        return pos
    
    def _extract_insights_from_tree_diagram(
        self,
        G: Any,
        root: Any
    ) -> Dict[str, Any]:
        """
        Extract insights from tree diagram data.
        
        Args:
            G: NetworkX graph
            root: Root node
            
        Returns:
            Dict[str, Any]: Extracted insights
        """
        insights = {}
        
        if len(G.nodes()) == 0:
            return insights
            
        # Basic tree properties
        insights["tree_properties"] = {
            "n_nodes": len(G.nodes()),
            "n_edges": len(G.edges()),
            "root": str(root)
        }
        
        # Check if it's a valid tree
        is_tree = nx.is_tree(G.to_undirected())
        insights["tree_properties"]["is_valid_tree"] = is_tree
        
        if not is_tree:
            # Not a valid tree - might have cycles or disconnected components
            return insights
            
        # Calculate tree depth
        # Find the longest path from root to any leaf
        max_depth = 0
        
        for node in G.nodes():
            if G.out_degree(node) == 0:  # Leaf node
                # Find path from root to this leaf
                path = nx.shortest_path(G, root, node)
                depth = len(path) - 1  # Subtract 1 to get depth (edges)
                max_depth = max(max_depth, depth)
                
        insights["tree_properties"]["depth"] = max_depth
        
        # Count nodes at each level
        level_counts = {}
        
        for node in G.nodes():
            # Check if node is reachable from root
            if nx.has_path(G, root, node):
                path = nx.shortest_path(G, root, node)
                level = len(path) - 1
                
                if level not in level_counts:
                    level_counts[level] = 0
                    
                level_counts[level] += 1
                
        insights["level_counts"] = level_counts
        
        # Count leaf nodes
        leaf_count = 0
        
        for node in G.nodes():
            if G.out_degree(node) == 0:  # Leaf node
                leaf_count += 1
                
        insights["tree_properties"]["leaf_count"] = leaf_count
        
        # Calculate branching factor
        # Average number of children per non-leaf node
        non_leaf_count = len(G.nodes()) - leaf_count
        
        if non_leaf_count > 0:
            branching_factor = (len(G.nodes()) - 1) / non_leaf_count
        else:
            branching_factor = 0
            
        insights["tree_properties"]["avg_branching_factor"] = branching_factor
        
        # Calculate node distribution by subtree
        if len(G.nodes()) > 10:
            # Only calculate for larger trees
            subtree_sizes = {}
            
            for child in G.successors(root):
                # Count nodes in this subtree
                subtree = nx.descendants(G, child)
                subtree.add(child)  # Include child itself
                
                subtree_sizes[str(child)] = len(subtree)
                
            # Sort by size
            sorted_subtrees = sorted(subtree_sizes.items(), key=lambda x: x[1], reverse=True)
            
            insights["subtree_sizes"] = [
                {"node": node, "size": size}
                for node, size in sorted_subtrees
            ]
            
        return insights
    
    def create_sankey_diagram(
        self,
        data: Any,
        columns: Optional[List[str]] = None,
        title: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Create a Sankey diagram visualization.
        
        Args:
            data: Input data with flow information
            columns: Column names (source, target, value)
            title: Chart title
            **kwargs: Additional parameters
            
        Returns:
            Dict[str, Any]: Visualization output
        """
        # Check if matplotlib is available
        if not _HAS_MATPLOTLIB:
            raise ImportError("Matplotlib is required for Sankey diagrams")
            
        # Process parameters
        width = kwargs.get("width", self.config.get("default_width"))
        height = kwargs.get("height", self.config.get("default_height"))
        show_labels = kwargs.get("show_labels", self.config.get("show_data_labels", True))
        
        # Create figure and axes
        fig, ax = self.layout_manager.create_figure("regular", width=width, height=height)
        
        # Extract data
        nodes, links = self._extract_data_for_sankey_diagram(data, columns)
        
        # Create Sankey diagram using matplotlib
        from matplotlib.sankey import Sankey
        
        # Create Sankey instance
        sankey = Sankey(ax=ax, unit=kwargs.get("unit", ""), scale=kwargs.get("scale", 1.0))
        
        # Add flows
        self._add_sankey_flows(sankey, nodes, links, show_labels=show_labels)
        
        # Finish diagram
        sankey.finish()
        
        # Set title
        if title:
            ax.set_title(title, fontsize=self.config.get("title_font_size", 16))
            
        # Remove axis ticks and labels
        ax.set_xticks([])
        ax.set_yticks([])
        ax.axis('off')
        
        # Tight layout
        fig.tight_layout()
        
        # Extract insights
        insights = self._extract_insights_from_sankey_diagram(nodes, links)
        
        return {
            "figure": fig,
            "insights": insights
        }
    
    def _extract_data_for_sankey_diagram(
        self,
        data: Any,
        columns: Optional[List[str]] = None
    ) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
        """
        Extract data for a Sankey diagram from various input formats.
        
        Args:
            data: Input data
            columns: Column names
            
        Returns:
            Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]: Nodes and links
        """
        nodes = []
        links = []
        
        if isinstance(data, dict):
            # Dictionary data
            if "nodes" in data and "links" in data:
                # Pre-defined nodes and links
                nodes = data["nodes"]
                links = data["links"]
                
        elif _HAS_PANDAS and isinstance(data, pd.DataFrame):
            # DataFrame data
            if columns and len(columns) >= 3:
                # Extract source, target, and value columns
                source_col, target_col, value_col = columns[0], columns[1], columns[2]
                
                # Extract flow data
                flows = data[[source_col, target_col, value_col]].dropna()
                
                # Build nodes list
                node_names = set(flows[source_col].tolist() + flows[target_col].tolist())
                nodes = [{"name": name} for name in node_names]
                
                # Create node index mapping
                node_indices = {name: i for i, name in enumerate(node_names)}
                
                # Build links list
                for _, row in flows.iterrows():
                    source = row[source_col]
                    target = row[target_col]
                    value = row[value_col]
                    
                    links.append({
                        "source": node_indices[source],
                        "target": node_indices[target],
                        "value": value
                    })
                    
        # Handle empty data
        if not nodes or not links:
            # Generate dummy data
            nodes = [
                {"name": "Source 1"},
                {"name": "Source 2"},
                {"name": "Target 1"},
                {"name": "Target 2"}
            ]
            
            links = [
                {"source": 0, "target": 2, "value": 5},
                {"source": 0, "target": 3, "value": 3},
                {"source": 1, "target": 2, "value": 2},
                {"source": 1, "target": 3, "value": 4}
            ]
            
        return nodes, links
    
    def _add_sankey_flows(
        self,
        sankey: Any,
        nodes: List[Dict[str, Any]],
        links: List[Dict[str, Any]],
        show_labels: bool = True
    ) -> None:
        """
        Add flows to a Sankey diagram.
        
        Args:
            sankey: Matplotlib Sankey instance
            nodes: Node data
            links: Link data
            show_labels: Whether to show labels
        """
        # Group links by source
        flows_by_source = {}
        
        for link in links:
            source = link["source"]
            target = link["target"]
            value = link["value"]
            
            if source not in flows_by_source:
                flows_by_source[source] = []
                
            flows_by_source[source].append((target, value))
            
        # Add flows for each source
        for source, flows in flows_by_source.items():
            # Get values and targets
            values = [flow[1] for flow in flows]
            targets = [flow[0] for flow in flows]
            
            # Get source and target labels
            labels = None
            
            if show_labels:
                labels = []
                
                # Add source label
                if source < len(nodes):
                    source_name = nodes[source].get("name", f"Node {source}")
                    labels.append(source_name)
                else:
                    labels.append(f"Node {source}")
                    
                # Add target labels
                for target in targets:
                    if target < len(nodes):
                        target_name = nodes[target].get("name", f"Node {target}")
                        labels.append(target_name)
                    else:
                        labels.append(f"Node {target}")
                        
            # Add flow
            sankey.add(
                flows=values,
                orientations=[0] + [1] * len(values),  # Source to left, targets to right
                labels=labels,
                trunklength=kwargs.get("trunk_length", 1.0),
                pathlengths=[0.4] * len(values),
                rotation=kwargs.get("rotation", 0)
            )
    
    def _extract_insights_from_sankey_diagram(
        self,
        nodes: List[Dict[str, Any]],
        links: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        Extract insights from Sankey diagram data.
        
        Args:
            nodes: Node data
            links: Link data
            
        Returns:
            Dict[str, Any]: Extracted insights
        """
        insights = {}
        
        if not nodes or not links:
            return insights
            
        # Calculate total flow
        total_flow = sum(link["value"] for link in links)
        
        insights["total_flow"] = total_flow
        
        # Calculate node flows
        node_flows = []
        
        for i, node in enumerate(nodes):
            # Calculate incoming and outgoing flows
            incoming = sum(link["value"] for link in links if link["target"] == i)
            outgoing = sum(link["value"] for link in links if link["source"] == i)
            
            node_flows.append({
                "node": node.get("name", f"Node {i}"),
                "incoming": incoming,
                "outgoing": outgoing,
                "net_flow": outgoing - incoming,
                "is_source": outgoing > 0 and incoming == 0,
                "is_sink": incoming > 0 and outgoing == 0,
                "is_pass_through": incoming > 0 and outgoing > 0
            })
            
        # Sort by total flow (incoming + outgoing)
        node_flows.sort(key=lambda x: x["incoming"] + x["outgoing"], reverse=True)
        
        insights["node_flows"] = node_flows
        
        # Calculate top flows
        flow_strengths = []
        
        for link in links:
            source_idx = link["source"]
            target_idx = link["target"]
            value = link["value"]
            
            source = nodes[source_idx].get("name", f"Node {source_idx}") if source_idx < len(nodes) else f"Node {source_idx}"
            target = nodes[target_idx].get("name", f"Node {target_idx}") if target_idx < len(nodes) else f"Node {target_idx}"
            
            flow_strengths.append({
                "source": source,
                "target": target,
                "value": value,
                "percentage": value / total_flow * 100 if total_flow > 0 else 0
            })
            
        # Sort by value
        flow_strengths.sort(key=lambda x: x["value"], reverse=True)
        
        insights["top_flows"] = flow_strengths[:10]  # Top 10 flows
        
        return insights
    
    def create_arc_diagram(
        self,
        data: Any,
        columns: Optional[List[str]] = None,
        title: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Create an arc diagram visualization.
        
        Args:
            data: Input data with nodes and edges
            columns: Column names (unused but kept for consistency)
            title: Chart title
            **kwargs: Additional parameters
            
        Returns:
            Dict[str, Any]: Visualization output
        """
        # Check if matplotlib and networkx are available
        if not _HAS_MATPLOTLIB:
            raise ImportError("Matplotlib is required for arc diagrams")
            
        if not _HAS_NETWORKX:
            raise ImportError("NetworkX is required for arc diagrams")
            
        # Process parameters
        width = kwargs.get("width", self.config.get("default_width"))
        height = kwargs.get("height", self.config.get("default_height"))
        node_size = kwargs.get("node_size", self.config.get("node_size", 500))
        edge_width = kwargs.get("edge_width", self.config.get("edge_width", 1.0))
        show_labels = kwargs.get("show_labels", self.config.get("show_data_labels", True))
        
        # Create figure and axes
        fig, ax = self.layout_manager.create_figure("regular", width=width, height=height)
        
        # Extract data and create graph
        G, node_attrs, edge_attrs = self._extract_data_for_network_graph(data, False)
        
        # Default node and edge attributes if not provided
        node_colors = node_attrs.get("colors", [self.color_manager.get_color("primary")] * len(G.nodes()))
        node_sizes = node_attrs.get("sizes", [node_size] * len(G.nodes()))
        node_labels = node_attrs.get("labels", {})
        
        edge_colors = edge_attrs.get("colors", [self.color_manager.get_color("text")] * len(G.edges()))
        edge_widths = edge_attrs.get("widths", [edge_width] * len(G.edges()))
        
        # Generate node order
        node_order = kwargs.get("node_order", list(G.nodes()))
        
        # Create arc diagram layout
        pos = self._create_arc_diagram_layout(G, node_order)
        
        # Draw arcs
        self._draw_arcs(ax, G, pos, edge_colors, edge_widths)
        
        # Draw nodes
        x_pos = [p[0] for p in pos.values()]
        y_pos = [p[1] for p in pos.values()]
        
        ax.scatter(
            x_pos, 
            y_pos, 
            s=node_sizes, 
            c=node_colors,
            zorder=10  # Ensure nodes are drawn on top of arcs
        )
        
        # Draw labels if requested
        if show_labels:
            for node, (x, y) in pos.items():
                label = node_labels.get(node, str(node))
                
                ax.text(
                    x, y - 0.1,
                    label,
                    ha="center",
                    va="top",
                    fontsize=kwargs.get("label_font_size", self.config.get("annotation_font_size", 10)),
                    color=kwargs.get("label_color", self.color_manager.get_color("text")),
                    rotation=kwargs.get("label_rotation", 45)
                )
                
        # Set axis limits
        margin = 0.1
        ax.set_xlim(min(x_pos) - margin, max(x_pos) + margin)
        ax.set_ylim(-1.5, 1.5)
        
        # Set title
        if title:
            ax.set_title(title, fontsize=self.config.get("title_font_size", 16))
            
        # Remove axis ticks and labels
        ax.set_xticks([])
        ax.set_yticks([])
        ax.axis('off')
        
        # Tight layout
        fig.tight_layout()
        
        # Extract insights
        insights = self._extract_insights_from_network_graph(G, node_attrs, edge_attrs)
        
        return {
            "figure": fig,
            "insights": insights
        }
    
    def _create_arc_diagram_layout(
        self,
        G: Any,
        node_order: Optional[List[Any]] = None
    ) -> Dict[Any, Tuple[float, float]]:
        """
        Create layout for arc diagram.
        
        Args:
            G: NetworkX graph
            node_order: Optional node ordering
            
        Returns:
            Dict[Any, Tuple[float, float]]: Node positions
        """
        # Use provided node order or default order
        if node_order is None:
            # Try to find a good node ordering
            try:
                # Use spectral ordering
                laplacian = nx.laplacian_matrix(G).toarray()
                evals, evecs = np.linalg.eigh(laplacian)
                
                # Use Fiedler vector (second smallest eigenvalue) for ordering
                fiedler = evecs[:,1]
                node_order = [list(G.nodes())[i] for i in np.argsort(fiedler)]
            except:
                # Fall back to original node order
                node_order = list(G.nodes())
                
        # Create evenly spaced positions along the x-axis
        x_positions = np.linspace(0, 1, len(node_order))
        positions = {}
        
        for i, node in enumerate(node_order):
            positions[node] = (x_positions[i], 0)
            
        return positions
    
    def _draw_arcs(
        self,
        ax: Any,
        G: Any,
        pos: Dict[Any, Tuple[float, float]],
        edge_colors: List[str],
        edge_widths: List[float]
    ) -> None:
        """
        Draw arc connections.
        
        Args:
            ax: Matplotlib axes
            G: NetworkX graph
            pos: Node positions
            edge_colors: Edge colors
            edge_widths: Edge widths
        """
        # Track color/width index
        color_idx = 0
        
        # Draw each edge as an arc
        for edge in G.edges():
            source, target = edge
            
            # Get positions
            x1, y1 = pos[source]
            x2, y2 = pos[target]
            
            # Skip self-loops
            if source == target:
                continue
                
            # Calculate arc height based on distance
            dist = abs(x2 - x1)
            height = 0.5 + dist * 0.5  # Scale height with distance
            
            # Create arc
            center = (x1 + x2) / 2
            width = abs(x2 - x1)
            
            # Draw arc as an ellipse
            ellipse = matplotlib.patches.Arc(
                xy=(center, 0),
                width=width,
                height=height,
                theta1=0,
                theta2=180,
                color=edge_colors[color_idx % len(edge_colors)],
                linewidth=edge_widths[color_idx % len(edge_widths)],
                alpha=0.7
            )
            
            ax.add_patch(ellipse)
            
            # Update color index
            color_idx += 1
    
    def create_choropleth_map(
        self,
        data: Any,
        columns: Optional[List[str]] = None,
        title: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Create a choropleth map visualization.
        
        Args:
            data: Input data with geographic regions and values
            columns: Column names (region, value)
            title: Chart title
            **kwargs: Additional parameters
            
        Returns:
            Dict[str, Any]: Visualization output
        """
        # For a complete implementation, this would require GeoJSON data
        # or another source of geographic boundaries. This is a simplified
        # version that works with basic region data.
        
        # Create a simplified message for the user
        fig, ax = plt.subplots(figsize=(6, 4), dpi=100)
        ax.text(0.5, 0.5, "Choropleth maps require geographic boundary data.\n"
                          "Please provide GeoJSON or shapefile data for your regions.",
                ha="center", va="center", fontsize=12)
        ax.axis('off')
        
        return {
            "figure": fig,
            "insights": {
                "message": "Choropleth maps require geographic boundary data. Please provide GeoJSON or shapefile data for your regions."
            }
        }
    
    def create_geographic_map(
        self,
        data: Any,
        columns: Optional[List[str]] = None,
        title: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Create a geographic map visualization.
        
        Args:
            data: Input data with geographic coordinates
            columns: Column names (latitude, longitude, value)
            title: Chart title
            **kwargs: Additional parameters
            
        Returns:
            Dict[str, Any]: Visualization output
        """
        # For a complete implementation, this would require geographic base maps
        # and additional libraries (e.g., cartopy, folium). This is a simplified
        # version that works with basic coordinate data.
        
        # Create a simplified message for the user
        fig, ax = plt.subplots(figsize=(6, 4), dpi=100)
        ax.text(0.5, 0.5, "Geographic maps require additional mapping libraries.\n"
                          "Please install cartopy or folium for full map support.",
                ha="center", va="center", fontsize=12)
        ax.axis('off')
        
        return {
            "figure": fig,
            "insights": {
                "message": "Geographic maps require additional mapping libraries. Please install cartopy or folium for full map support."
            }
        }
    
    def create_table(
        self,
        data: Any,
        columns: Optional[List[str]] = None,
        title: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Create a table visualization.
        
        Args:
            data: Input data
            columns: Column names
            title: Table title
            **kwargs: Additional parameters
            
        Returns:
            Dict[str, Any]: Visualization output
        """
        # Check if matplotlib is available
        if not _HAS_MATPLOTLIB:
            raise ImportError("Matplotlib is required for table visualizations")
            
        # Process parameters
        width = kwargs.get("width", self.config.get("default_width"))
        height = kwargs.get("height", self.config.get("default_height"))
        
        # Create figure without axes
        fig = plt.figure(figsize=(width/100, height/100), dpi=100)
        ax = fig.add_subplot(111, frame_on=False)
        ax.set_axis_off()
        
        # Extract data
        table_data, col_labels, row_labels = self._extract_data_for_table(data, columns)
        
        # Create table
        table = ax.table(
            cellText=table_data,
            colLabels=col_labels,
            rowLabels=row_labels,
            loc='center',
            cellLoc=kwargs.get("cell_alignment", "center"),
            colLoc=kwargs.get("header_alignment", "center")
        )
        
        # Style table
        table.auto_set_font_size(False)
        table.set_fontsize(kwargs.get("font_size", self.config.get("annotation_font_size", 10)))
        
        # Adjust column widths
        for i in range(len(col_labels)):
            table.auto_set_column_width(i)
            
        # Style header row
        for i, key in enumerate(col_labels):
            cell = table[0, i]
            cell.set_facecolor(self.color_manager.get_color("primary"))
            cell.set_text_props(color='white', fontweight='bold')
            
        # Style row labels if present
        if row_labels:
            for i in range(len(row_labels)):
                cell = table[i+1, -1]  # +1 for header row
                cell.set_facecolor(self.color_manager.get_color("secondary"))
                cell.set_text_props(color='white', fontweight='bold')
                
        # Set title
        if title:
            plt.title(title, fontsize=self.config.get("title_font_size", 16), pad=20)
            
        # Tight layout
        fig.tight_layout()
        
        # Extract insights
        insights = self._extract_insights_from_table(table_data, col_labels, row_labels)
        
        return {
            "figure": fig,
            "insights": insights
        }
    
    def _extract_data_for_table(
        self,
        data: Any,
        columns: Optional[List[str]] = None
    ) -> Tuple[List[List[str]], List[str], List[str]]:
        """
        Extract data for a table from various input formats.
        
        Args:
            data: Input data
            columns: Column names
            
        Returns:
            Tuple[List[List[str]], List[str], List[str]]:
            Table data, column labels, and row labels
        """
        table_data = []
        col_labels = []
        row_labels = []
        
        if _HAS_PANDAS and isinstance(data, pd.DataFrame):
            # DataFrame data
            if columns:
                # Use specified columns
                df = data[columns].copy()
            else:
                # Use all columns
                df = data.copy()
                
            # Get column labels
            col_labels = df.columns.tolist()
            
            # Get row labels
            row_labels = df.index.astype(str).tolist()
            
            # Get data
            table_data = df.values.tolist()
            
            # Convert all values to strings
            table_data = [[str(cell) for cell in row] for row in table_data]
                
        elif isinstance(data, dict):
            # Dictionary data
            if "data" in data:
                # Pre-defined data matrix
                table_data = data["data"]
                col_labels = data.get("columns", [f"Column {i+1}" for i in range(len(table_data[0]))])
                row_labels = data.get("rows", [f"Row {i+1}" for i in range(len(table_data))])
            elif all(isinstance(v, (list, tuple)) for v in data.values()):
                # Dictionary of lists
                col_labels = list(data.keys())
                
                # Transpose data to get rows
                max_len = max(len(v) for v in data.values())
                table_data = [[] for _ in range(max_len)]
                
                for col in col_labels:
                    values = data[col]
                    
                    # Pad values if needed
                    values = values + [None] * (max_len - len(values))
                    
                    # Add values to rows
                    for i, val in enumerate(values):
                        table_data[i].append(str(val) if val is not None else "")
                        
                # Generate row labels
                row_labels = [f"Row {i+1}" for i in range(max_len)]
            elif all(isinstance(k, str) and isinstance(v, (dict, list, tuple)) for k, v in data.items()):
                # Dictionary of dictionaries or lists
                row_labels = list(data.keys())
                
                # Get all column keys
                if all(isinstance(v, dict) for v in data.values()):
                    # Dictionary of dictionaries
                    all_cols = set()
                    for v in data.values():
                        all_cols.update(v.keys())
                        
                    col_labels = sorted(list(all_cols))
                    
                    # Create table data
                    table_data = []
                    
                    for row in row_labels:
                        row_data = []
                        row_dict = data[row]
                        
                        for col in col_labels:
                            val = row_dict.get(col, None)
                            row_data.append(str(val) if val is not None else "")
                            
                        table_data.append(row_data)
                else:
                    # Dictionary of lists
                    col_labels = [f"Column {i+1}" for i in range(max(len(v) for v in data.values()))]
                    
                    # Create table data
                    table_data = []
                    
                    for row in row_labels:
                        row_data = data[row]
                        
                        # Pad row if needed
                        row_data = row_data + [None] * (len(col_labels) - len(row_data))
                        
                        # Convert to strings
                        row_data = [str(val) if val is not None else "" for val in row_data]
                        
                        table_data.append(row_data)
                        
        elif isinstance(data, (list, tuple)):
            # List data
            if all(isinstance(item, (list, tuple)) for item in data):
                # List of lists
                table_data = [[str(cell) for cell in row] for row in data]
                
                # Generate column and row labels
                if table_data:
                    col_labels = columns if columns else [f"Column {i+1}" for i in range(len(table_data[0]))]
                    row_labels = [f"Row {i+1}" for i in range(len(table_data))]
            elif all(isinstance(item, dict) for item in data):
                # List of dictionaries
                # Get all column keys
                all_cols = set()
                for item in data:
                    all_cols.update(item.keys())
                    
                col_labels = columns if columns else sorted(list(all_cols))
                
                # Create table data
                table_data = []
                
                for i, item in enumerate(data):
                    row_data = []
                    
                    for col in col_labels:
                        val = item.get(col, None)
                        row_data.append(str(val) if val is not None else "")
                        
                    table_data.append(row_data)
                    
                # Generate row labels
                row_labels = [f"Row {i+1}" for i in range(len(table_data))]
                
        # Handle empty data
        if not table_data:
            # Generate dummy data
            table_data = [["No data"]]
            col_labels = ["Column 1"]
            row_labels = ["Row 1"]
            
        # Truncate if too large
        max_rows = kwargs.get("max_rows", 50)
        max_cols = kwargs.get("max_cols", 20)
        
        if len(table_data) > max_rows:
            table_data = table_data[:max_rows]
            row_labels = row_labels[:max_rows]
            
        if len(col_labels) > max_cols:
            table_data = [row[:max_cols] for row in table_data]
            col_labels = col_labels[:max_cols]
            
        return table_data, col_labels, row_labels
    
    def _extract_insights_from_table(
        self,
        table_data: List[List[str]],
        col_labels: List[str],
        row_labels: List[str]
    ) -> Dict[str, Any]:
        """
        Extract insights from table data.
        
        Args:
            table_data: Table data
            col_labels: Column labels
            row_labels: Row labels
            
        Returns:
            Dict[str, Any]: Extracted insights
        """
        insights = {}
        
        if not table_data:
            return insights
            
        # Basic table properties
        insights["table_properties"] = {
            "n_rows": len(table_data),
            "n_cols": len(col_labels),
            "columns": col_labels
        }
        
        # Try to convert to numeric for additional insights
        numeric_data = []
        
        for row in table_data:
            numeric_row = []
            
            for cell in row:
                try:
                    # Try to convert to float
                    val = float(cell)
                    numeric_row.append(val)
                except:
                    # Not a number
                    numeric_row.append(None)
                    
            numeric_data.append(numeric_row)
            
        # Calculate column statistics
        column_stats = []
        
        for j, col in enumerate(col_labels):
            # Extract column values
            col_values = [row[j] for row in numeric_data if j < len(row) and row[j] is not None]
            
            # Skip if no numeric values
            if not col_values:
                continue
                
            # Calculate basic statistics
            stats = {
                "column": col,
                "mean": np.mean(col_values),
                "median": np.median(col_values),
                "min": np.min(col_values),
                "max": np.max(col_values),
                "std_dev": np.std(col_values),
                "count": len(col_values),
                "numeric_percentage": len(col_values) / len(table_data) * 100 if table_data else 0
            }
            
            column_stats.append(stats)
            
        # Add column statistics if available
        if column_stats:
            insights["column_statistics"] = column_stats
            
        return insights
    
    def create_word_cloud(
        self,
        data: Any,
        columns: Optional[List[str]] = None,
        title: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Create a word cloud visualization.
        
        Args:
            data: Input data with text
            columns: Column names
            title: Chart title
            **kwargs: Additional parameters
            
        Returns:
            Dict[str, Any]: Visualization output
        """
        # Check if matplotlib is available
        if not _HAS_MATPLOTLIB:
            raise ImportError("Matplotlib is required for word clouds")
            
        try:
            # Try to import wordcloud
            from wordcloud import WordCloud
        except ImportError:
            # Create a message that wordcloud is required
            fig, ax = plt.subplots(figsize=(6, 4), dpi=100)
            ax.text(0.5, 0.5, "WordCloud library is required for word clouds.\n"
                            "Please install it with 'pip install wordcloud'.",
                    ha="center", va="center", fontsize=12)
            ax.axis('off')
            
            return {
                "figure": fig,
                "insights": {
                    "message": "WordCloud library is required for word clouds. Please install it with 'pip install wordcloud'."
                }
            }
            
        # Process parameters
        width = kwargs.get("width", self.config.get("default_width"))
        height = kwargs.get("height", self.config.get("default_height"))
        background_color = kwargs.get("background_color", self.color_manager.get_color("background"))
        
        # Extract text data
        text, word_frequencies = self._extract_data_for_word_cloud(data, columns)
        
        # Create figure and axes
        fig, ax = self.layout_manager.create_figure("regular", width=width, height=height)
        
        if word_frequencies:
            # Create word cloud from frequencies
            wordcloud = WordCloud(
                width=width,
                height=height,
                background_color=background_color,
                colormap=kwargs.get("colormap", self.color_manager.color_map_name),
                max_words=kwargs.get("max_words", 200),
                max_font_size=kwargs.get("max_font_size", 100),
                min_font_size=kwargs.get("min_font_size", 10),
                scale=kwargs.get("scale", 1),
                random_state=kwargs.get("random_state", 42)
            ).generate_from_frequencies(word_frequencies)
        else:
            # Create word cloud from text
            wordcloud = WordCloud(
                width=width,
                height=height,
                background_color=background_color,
                colormap=kwargs.get("colormap", self.color_manager.color_map_name),
                max_words=kwargs.get("max_words", 200),
                max_font_size=kwargs.get("max_font_size", 100),
                min_font_size=kwargs.get("min_font_size", 10),
                scale=kwargs.get("scale", 1),
                random_state=kwargs.get("random_state", 42)
            ).generate(text)
            
        # Display word cloud
        ax.imshow(wordcloud, interpolation='bilinear')
        ax.axis('off')
        
        # Set title
        if title:
            ax.set_title(title, fontsize=self.config.get("title_font_size", 16))
            
        # Tight layout
        fig.tight_layout()
        
        # Extract insights
        insights = self._extract_insights_from_word_cloud(text, word_frequencies)
        
        return {
            "figure": fig,
            "insights": insights
        }
    
    def _extract_data_for_word_cloud(
        self,
        data: Any,
        columns: Optional[List[str]] = None
    ) -> Tuple[str, Dict[str, float]]:
        """
        Extract data for a word cloud from various input formats.
        
        Args:
            data: Input data
            columns: Column names
            
        Returns:
            Tuple[str, Dict[str, float]]: Text and word frequencies
        """
        text = ""
        word_frequencies = {}
        
        if _HAS_PANDAS and isinstance(data, pd.DataFrame):
            # DataFrame data
            if columns and len(columns) > 0:
                # Use specified columns
                for col in columns:
                    if col in data.columns:
                        # Concatenate text from column
                        text += " ".join(data[col].astype(str).tolist())
            else:
                # Use all object/string columns
                text_cols = data.select_dtypes(include=["object"]).columns
                
                if text_cols.empty:
                    # No text columns, use all columns
                    text = " ".join(" ".join(data[col].astype(str).tolist()) for col in data.columns)
                else:
                    # Use text columns
                    text = " ".join(" ".join(data[col].astype(str).tolist()) for col in text_cols)
                    
        elif isinstance(data, dict):
            # Dictionary data
            if "text" in data:
                # Pre-defined text
                text = data["text"]
            elif "words" in data and "frequencies" in data:
                # Pre-defined word frequencies
                words = data["words"]
                freqs = data["frequencies"]
                
                # Create word frequency dictionary
                word_frequencies = {word: freq for word, freq in zip(words, freqs)}
            elif all(isinstance(v, (int, float)) for v in data.values()):
                # Dictionary is already word frequencies
                word_frequencies = data
                
        elif isinstance(data, str):
            # String data
            text = data
            
        elif isinstance(data, (list, tuple)):
            # List data
            if all(isinstance(item, str) for item in data):
                # List of strings
                text = " ".join(data)
            elif all(isinstance(item, (list, tuple)) for item in data) and all(len(item) >= 2 for item in data):
                # List of [word, frequency] pairs
                word_frequencies = {item[0]: item[1] for item in data}
                
        # Handle empty data
        if not text and not word_frequencies:
            # Generate dummy text
            text = "No text data available. Please provide text or word frequencies for the word cloud."
            
        return text, word_frequencies
    
    def _extract_insights_from_word_cloud(
        self,
        text: str,
        word_frequencies: Dict[str, float]
    ) -> Dict[str, Any]:
        """
        Extract insights from word cloud data.
        
        Args:
            text: Text data
            word_frequencies: Word frequencies
            
        Returns:
            Dict[str, Any]: Extracted insights
        """
        insights = {}
        
        # If we have word frequencies, use those
        if word_frequencies:
            # Sort by frequency
            sorted_words = sorted(word_frequencies.items(), key=lambda x: x[1], reverse=True)
            
            # Top words
            insights["top_words"] = [
                {"word": word, "frequency": freq}
                for word, freq in sorted_words[:20]  # Top 20
            ]
            
            # Number of unique words
            insights["n_unique_words"] = len(word_frequencies)
            
            # Total frequency
            insights["total_frequency"] = sum(word_frequencies.values())
        elif text:
            # Generate word frequencies from text
            from collections import Counter
            import re
            
            # Simple tokenization (split on non-alphanumeric characters)
            words = re.findall(r'\b[a-zA-Z]+\b', text.lower())
            
            # Count words
            word_counts = Counter(words)
            
            # Sort by frequency
            sorted_words = word_counts.most_common(20)  # Top 20
            
            # Top words
            insights["top_words"] = [
                {"word": word, "frequency": freq}
                for word, freq in sorted_words
            ]
            
            # Number of unique words
            insights["n_unique_words"] = len(word_counts)
            
            # Total words
            insights["total_words"] = len(words)
            
        return insights
    
    def create_calendar_heatmap(
        self,
        data: Any,
        columns: Optional[List[str]] = None,
        title: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Create a calendar heatmap visualization.
        
        Args:
            data: Input data with dates and values
            columns: Column names (date, value)
            title: Chart title
            **kwargs: Additional parameters
            
        Returns:
            Dict[str, Any]: Visualization output
        """
        # Check if matplotlib is available
        if not _HAS_MATPLOTLIB:
            raise ImportError("Matplotlib is required for calendar heatmaps")
            
        try:
            # Try to import calmap
            import calmap
        except ImportError:
            # Create a message that calmap is required
            fig, ax = plt.subplots(figsize=(6, 4), dpi=100)
            ax.text(0.5, 0.5, "Calmap library is required for calendar heatmaps.\n"
                            "Please install it with 'pip install calmap'.",
                    ha="center", va="center", fontsize=12)
            ax.axis('off')
            
            return {
                "figure": fig,
                "insights": {
                    "message": "Calmap library is required for calendar heatmaps. Please install it with 'pip install calmap'."
                }
            }
            
        # Process parameters
        width = kwargs.get("width", self.config.get("default_width"))
        height = kwargs.get("height", self.config.get("default_height"))
        cmap = kwargs.get("cmap", self.color_manager.get_colormap())
        
        # Extract data
        date_values = self._extract_data_for_calendar_heatmap(data, columns)
        
        # Create figure and axes
        fig, ax = self.layout_manager.create_figure("regular", width=width, height=height)
        
        # Check if we have valid data
        if date_values is None or date_values.empty:
            ax.text(0.5, 0.5, "No valid date data found.",
                    ha="center", va="center", fontsize=12)
            ax.axis('off')
            
            return {
                "figure": fig,
                "insights": {
                    "message": "No valid date data found."
                }
            }
            
        # Create calendar heatmap
        calmap.yearplot(
            date_values,
            year=kwargs.get("year", date_values.index.year.min()),
            ax=ax,
            cmap=cmap,
            fillcolor=kwargs.get("fillcolor", self.color_manager.get_color("background")),
            linewidth=kwargs.get("linewidth", 1),
            linecolor=kwargs.get("linecolor", self.color_manager.get_color("grid")),
            daylabels=kwargs.get("daylabels", "MTWTFSS"),
            dayticks=kwargs.get("dayticks", [0, 2, 4, 6]),
            monthlabels=kwargs.get("monthlabels", ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']),
            monthticks=kwargs.get("monthticks", list(range(1, 13))),
            vmin=kwargs.get("vmin", None),
            vmax=kwargs.get("vmax", None)
        )
        
        # Set title
        if title:
            ax.set_title(title, fontsize=self.config.get("title_font_size", 16))
            
        # Add colorbar
        if kwargs.get("show_colorbar", True):
            fig.colorbar(ax.get_children()[0], ax=ax)
            
        # Tight layout
        fig.tight_layout()
        
        # Extract insights
        insights = self._extract_insights_from_calendar_heatmap(date_values)
        
        return {
            "figure": fig,
            "insights": insights
        }
    
    def _extract_data_for_calendar_heatmap(
        self,
        data: Any,
        columns: Optional[List[str]] = None
    ) -> Any:
        """
        Extract data for a calendar heatmap from various input formats.
        
        Args:
            data: Input data
            columns: Column names
            
        Returns:
            Any: Pandas Series with dates as index
        """
        if not _HAS_PANDAS:
            return None
            
        date_values = None
        
        if isinstance(data, pd.DataFrame):
            # DataFrame data
            if columns and len(columns) >= 2:
                # Use specified columns
                date_col, value_col = columns[0], columns[1]
                
                # Check if date column is already datetime
                if pd.api.types.is_datetime64_any_dtype(data[date_col]):
                    # Already datetime
                    dates = data[date_col]
                else:
                    # Try to convert to datetime
                    try:
                        dates = pd.to_datetime(data[date_col])
                    except:
                        # Failed to convert
                        return None
                        
                # Create Series with dates as index
                date_values = pd.Series(data[value_col].values, index=dates)
            else:
                # Try to find appropriate columns
                # Look for datetime columns
                date_cols = data.select_dtypes(include=["datetime64"]).columns
                
                if date_cols.empty:
                    # Try to convert object columns to datetime
                    for col in data.select_dtypes(include=["object"]).columns:
                        try:
                            dates = pd.to_datetime(data[col])
                            date_cols = [col]
                            data[col] = dates
                            break
                        except:
                            # Failed to convert
                            pass
                            
                if not date_cols.empty:
                    # Find a numeric column
                    num_cols = data.select_dtypes(include=["number"]).columns
                    
                    if not num_cols.empty:
                        # Use first datetime column and first numeric column
                        date_col = date_cols[0]
                        value_col = num_cols[0]
                        
                        # Create Series with dates as index
                        date_values = pd.Series(data[value_col].values, index=data[date_col])
                        
        elif isinstance(data, dict):
            # Dictionary data
            if "dates" in data and "values" in data:
                # Pre-defined dates and values
                dates = data["dates"]
                values = data["values"]
                
                # Try to convert to datetime
                try:
                    dates = pd.to_datetime(dates)
                    
                    # Create Series with dates as index
                    date_values = pd.Series(values, index=dates)
                except:
                    # Failed to convert
                    return None
            elif all(isinstance(k, str) for k in data.keys()) and all(isinstance(v, (int, float)) for v in data.values()):
                # Dictionary with date strings as keys and values
                # Try to convert keys to datetime
                try:
                    dates = pd.to_datetime(list(data.keys()))
                    
                    # Create Series with dates as index
                    date_values = pd.Series(list(data.values()), index=dates)
                except:
                    # Failed to convert
                    return None
                    
        elif isinstance(data, pd.Series):
            # Pandas Series
            if pd.api.types.is_datetime64_any_dtype(data.index):
                # Series with datetime index
                date_values = data
                
        # Handle empty data
        if date_values is None or date_values.empty:
            # Generate dummy data
            import datetime as dt
            
            # Create a year of daily data
            start_date = dt.datetime(2023, 1, 1)
            dates = [start_date + dt.timedelta(days=i) for i in range(365)]
            
            # Generate random values
            values = np.random.rand(365)
            
            # Create Series with dates as index
            date_values = pd.Series(values, index=dates)
            
        return date_values
    
    def _extract_insights_from_calendar_heatmap(
        self,
        date_values: Any
    ) -> Dict[str, Any]:
        """
        Extract insights from calendar heatmap data.
        
        Args:
            date_values: Pandas Series with dates as index
            
        Returns:
            Dict[str, Any]: Extracted insights
        """
        insights = {}
        
        if date_values is None or date_values.empty:
            return insights
            
        # Basic statistics
        insights["statistics"] = {
            "mean": float(date_values.mean()),
            "median": float(date_values.median()),
            "min": float(date_values.min()),
            "max": float(date_values.max()),
            "std_dev": float(date_values.std()),
            "count": len(date_values)
        }
        
        # Date range
        insights["date_range"] = {
            "start": date_values.index.min().strftime("%Y-%m-%d"),
            "end": date_values.index.max().strftime("%Y-%m-%d"),
            "days": (date_values.index.max() - date_values.index.min()).days + 1
        }
        
        # Find highest and lowest values
        max_idx = date_values.idxmax()
        min_idx = date_values.idxmin()
        
        insights["extremes"] = {
            "highest": {
                "date": max_idx.strftime("%Y-%m-%d"),
                "value": float(date_values.loc[max_idx])
            },
            "lowest": {
                "date": min_idx.strftime("%Y-%m-%d"),
                "value": float(date_values.loc[min_idx])
            }
        }
        
        # Temporal patterns
        # Group by day of week
        day_of_week = date_values.groupby(date_values.index.dayofweek).mean()
        
        insights["day_of_week"] = {
            "means": [float(day_of_week.get(i, 0)) for i in range(7)],
            "highest_day": int(day_of_week.idxmax()),
            "lowest_day": int(day_of_week.idxmin())
        }
        
        # Group by month
        month = date_values.groupby(date_values.index.month).mean()
        
        insights["month"] = {
            "means": [float(month.get(i, 0)) for i in range(1, 13)],
            "highest_month": int(month.idxmax()),
            "lowest_month": int(month.idxmin())
        }
        
        return insights
    
    def create_venn_diagram(
        self,
        data: Any,
        columns: Optional[List[str]] = None,
        title: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Create a Venn diagram visualization.
        
        Args:
            data: Input data with set information
            columns: Column names (set names)
            title: Chart title
            **kwargs: Additional parameters
            
        Returns:
            Dict[str, Any]: Visualization output
        """
        # Check if matplotlib is available
        if not _HAS_MATPLOTLIB:
            raise ImportError("Matplotlib is required for Venn diagrams")
            
        try:
            # Try to import matplotlib_venn
            from matplotlib_venn import venn2, venn3
        except ImportError:
            # Create a message that matplotlib_venn is required
            fig, ax = plt.subplots(figsize=(6, 4), dpi=100)
            ax.text(0.5, 0.5, "Matplotlib-venn library is required for Venn diagrams.\n"
                            "Please install it with 'pip install matplotlib-venn'.",
                    ha="center", va="center", fontsize=12)
            ax.axis('off')
            
            return {
                "figure": fig,
                "insights": {
                    "message": "Matplotlib-venn library is required for Venn diagrams. Please install it with 'pip install matplotlib-venn'."
                }
            }
            
        # Process parameters
        width = kwargs.get("width", self.config.get("default_width"))
        height = kwargs.get("height", self.config.get("default_height"))
        
        # Create figure and axes
        fig, ax = self.layout_manager.create_figure("regular", width=width, height=height)
        
        # Extract data
        sets, set_labels = self._extract_data_for_venn_diagram(data, columns)
        
        # Check if we have valid data
        if not sets:
            ax.text(0.5, 0.5, "No valid set data found.",
                    ha="center", va="center", fontsize=12)
            ax.axis('off')
            
            return {
                "figure": fig,
                "insights": {
                    "message": "No valid set data found."
                }
            }
            
        # Create Venn diagram
        if len(sets) == 2:
            # Two-set Venn diagram
            venn_plot = venn2(
                sets,
                set_labels=set_labels,
                ax=ax,
                set_colors=[self.color_manager.get_color("primary"), self.color_manager.get_color("secondary")],
                alpha=kwargs.get("alpha", 0.7)
            )
            
            # Set text colors
            if venn_plot:
                for text in venn_plot.set_labels:
                    if text:
                        text.set_color(self.color_manager.get_color("text"))
                        
                for text in venn_plot.subset_labels:
                    if text:
                        text.set_color("white")
        elif len(sets) == 3:
            # Three-set Venn diagram
            venn_plot = venn3(
                sets,
                set_labels=set_labels,
                ax=ax,
                set_colors=[
                    self.color_manager.get_color("primary"),
                    self.color_manager.get_color("secondary"),
                    self.color_manager.get_color("tertiary")
                ],
                alpha=kwargs.get("alpha", 0.7)
            )
            
            # Set text colors
            if venn_plot:
                for text in venn_plot.set_labels:
                    if text:
                        text.set_color(self.color_manager.get_color("text"))
                        
                for text in venn_plot.subset_labels:
                    if text:
                        text.set_color("white")
        else:
            # Unsupported number of sets
            ax.text(0.5, 0.5, "Venn diagrams only support 2 or 3 sets.",
                    ha="center", va="center", fontsize=12)
            ax.axis('off')
            
            return {
                "figure": fig,
                "insights": {
                    "message": "Venn diagrams only support 2 or 3 sets."
                }
            }
            
        # Set title
        if title:
            ax.set_title(title, fontsize=self.config.get("title_font_size", 16))
            
        # Tight layout
        fig.tight_layout()
        
        # Extract insights
        insights = self._extract_insights_from_venn_diagram(sets, set_labels)
        
        return {
            "figure": fig,
            "insights": insights
        }
    
    def _extract_data_for_venn_diagram(
        self,
        data: Any,
        columns: Optional[List[str]] = None
    ) -> Tuple[List[Set[Any]], List[str]]:
        """
        Extract data for a Venn diagram from various input formats.
        
        Args:
            data: Input data
            columns: Column names
            
        Returns:
            Tuple[List[Set[Any]], List[str]]: Sets and set labels
        """
        sets = []
        set_labels = []
        
        if isinstance(data, dict):
            # Dictionary data
            if "sets" in data:
                # Pre-defined sets
                if isinstance(data["sets"], list) and all(isinstance(s, (list, set)) for s in data["sets"]):
                    # Convert to sets
                    sets = [set(s) for s in data["sets"]]
                    
                    # Get labels if available
                    if "labels" in data:
                        set_labels = data["labels"]
                    else:
                        # Generate labels
                        set_labels = [f"Set {i+1}" for i in range(len(sets))]
            else:
                # Check if dictionary keys are set labels
                set_dicts = []
                
                for key, value in data.items():
                    if isinstance(value, (list, set)):
                        # Convert to set
                        set_dicts.append((key, set(value)))
                        
                if set_dicts:
                    # Use dictionary items as sets
                    set_labels = [key for key, _ in set_dicts]
                    sets = [s for _, s in set_dicts]
                    
        elif isinstance(data, (list, tuple)):
            # List data
            if all(isinstance(item, (list, set)) for item in data):
                # List of sets
                sets = [set(s) for s in data]
                
                # Generate labels
                set_labels = columns if columns else [f"Set {i+1}" for i in range(len(sets))]
            elif all(isinstance(item, dict) for item in data):
                # List of dictionaries
                if all("name" in item and "elements" in item for item in data):
                    # Each dictionary has name and elements
                    set_labels = [item["name"] for item in data]
                    sets = [set(item["elements"]) for item in data]
                    
        # Handle empty data
        if not sets:
            # Generate dummy data for 2 sets
            sets = [set(range(1, 11)), set(range(5, 15))]
            set_labels = ["Set A", "Set B"]
            
        # Limit to 2 or 3 sets (Venn diagram limitation)
        if len(sets) > 3:
            sets = sets[:3]
            set_labels = set_labels[:3]
        elif len(sets) < 2:
            # Need at least 2 sets
            sets.append(set(range(5, 15)))
            set_labels.append("Set B")
            
        return sets, set_labels
    
    def _extract_insights_from_venn_diagram(
        self,
        sets: List[Set[Any]],
        set_labels: List[str]
    ) -> Dict[str, Any]:
        """
        Extract insights from Venn diagram data.
        
        Args:
            sets: Sets
            set_labels: Set labels
            
        Returns:
            Dict[str, Any]: Extracted insights
        """
        insights = {}
        
        if not sets:
            return insights
            
        # Set sizes
        set_sizes = []
        
        for i, s in enumerate(sets):
            set_sizes.append({
                "set": set_labels[i] if i < len(set_labels) else f"Set {i+1}",
                "size": len(s)
            })
            
        insights["set_sizes"] = set_sizes
        
        # Calculate intersections
        if len(sets) == 2:
            # Two-set intersection
            intersection = sets[0].intersection(sets[1])
            
            insights["intersections"] = {
                "sets": [
                    set_labels[0] if 0 < len(set_labels) else "Set 1",
                    set_labels[1] if 1 < len(set_labels) else "Set 2"
                ],
                "size": len(intersection),
                "percentage": len(intersection) / len(sets[0].union(sets[1])) * 100 if sets[0].union(sets[1]) else 0
            }
            
            # Calculate Jaccard similarity
            jaccard = len(intersection) / len(sets[0].union(sets[1])) if sets[0].union(sets[1]) else 0
            
            insights["similarity"] = {
                "jaccard": jaccard,
                "interpretation": "high" if jaccard > 0.6 else "medium" if jaccard > 0.3 else "low"
            }
        elif len(sets) == 3:
            # Three-set intersections
            intersections = []
            
            # Pairwise intersections
            for i in range(len(sets)):
                for j in range(i+1, len(sets)):
                    intersection = sets[i].intersection(sets[j])
                    
                    intersections.append({
                        "sets": [
                            set_labels[i] if i < len(set_labels) else f"Set {i+1}",
                            set_labels[j] if j < len(set_labels) else f"Set {j+1}"
                        ],
                        "size": len(intersection),
                        "percentage": len(intersection) / len(sets[i].union(sets[j])) * 100 if sets[i].union(sets[j]) else 0
                    })
                    
            # Three-way intersection
            three_way = sets[0].intersection(sets[1], sets[2])
            
            intersections.append({
                "sets": [
                    set_labels[0] if 0 < len(set_labels) else "Set 1",
                    set_labels[1] if 1 < len(set_labels) else "Set 2",
                    set_labels[2] if 2 < len(set_labels) else "Set 3"
                ],
                "size": len(three_way),
                "percentage": len(three_way) / len(sets[0].union(sets[1], sets[2])) * 100 if sets[0].union(sets[1], sets[2]) else 0
            })
            
            insights["intersections"] = intersections
            
        return insights
    
    def create_parallel_sets(
        self,
        data: Any,
        columns: Optional[List[str]] = None,
        title: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Create a parallel sets visualization.
        
        Args:
            data: Input data with categorical variables
            columns: Column names
            title: Chart title
            **kwargs: Additional parameters
            
        Returns:
            Dict[str, Any]: Visualization output
        """
        # Create a simplified message for the user
        # Parallel sets require specialized libraries that are not included
        # in the standard scientific Python stack
        
        fig, ax = plt.subplots(figsize=(6, 4), dpi=100)
        ax.text(0.5, 0.5, "Parallel sets visualization requires specialized libraries.\n"
                          "Consider using the 'parsets' JavaScript library or the 'pdsplot' Python package.",
                ha="center", va="center", fontsize=12)
        ax.axis('off')
        
        return {
            "figure": fig,
            "insights": {
                "message": "Parallel sets visualization requires specialized libraries. Consider using the 'parsets' JavaScript library or the 'pdsplot' Python package."
            }
        }
    
    def create_sunburst_chart(
        self,
        data: Any,
        columns: Optional[List[str]] = None,
        title: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Create a sunburst chart visualization.
        
        Args:
            data: Input data with hierarchical structure
            columns: Column names
            title: Chart title
            **kwargs: Additional parameters
            
        Returns:
            Dict[str, Any]: Visualization output
        """
        # Check if matplotlib is available
        if not _HAS_MATPLOTLIB:
            raise ImportError("Matplotlib is required for sunburst charts")
            
        try:
            # Try to import plotly (for sunburst chart)
            if not _HAS_PLOTLY:
                raise ImportError("Plotly is required for sunburst charts")
                
        except ImportError:
            # Create a message that plotly is required
            fig, ax = plt.subplots(figsize=(6, 4), dpi=100)
            ax.text(0.5, 0.5, "Plotly is required for sunburst charts.\n"
                            "Please install it with 'pip install plotly'.",
                    ha="center", va="center", fontsize=12)
            ax.axis('off')
            
            return {
                "figure": fig,
                "insights": {
                    "message": "Plotly is required for sunburst charts. Please install it with 'pip install plotly'."
                }
            }
            
        # Process parameters
        width = kwargs.get("width", self.config.get("default_width"))
        height = kwargs.get("height", self.config.get("default_height"))
        
        # Extract data
        labels, parents, values, ids = self._extract_data_for_sunburst_chart(data, columns)
        
        # Create plotly figure
        fig = go.Figure(go.Sunburst(
            ids=ids,
            labels=labels,
            parents=parents,
            values=values,
            branchvalues="total",
            marker=dict(
                colors=self.color_manager.get_categorical_palette(len(labels)),
                line=dict(width=0.5, color=self.color_manager.get_color("background"))
            ),
            textinfo="label+value+percent parent+percent entry",
            insidetextorientation="radial"
        ))
        
        # Update layout
        fig.update_layout(
            title=title,
            width=width,
            height=height,
            margin=dict(t=30, l=0, r=0, b=0)
        )
        
        # Convert to HTML
        html = fig.to_html(include_plotlyjs="cdn", full_html=False)
        
        # Extract insights
        insights = self._extract_insights_from_sunburst_chart(labels, parents, values, ids)
        
        return {
            "html": html,
            "insights": insights
        }
    
    def _extract_data_for_sunburst_chart(
        self,
        data: Any,
        columns: Optional[List[str]] = None
    ) -> Tuple[List[str], List[str], List[float], List[str]]:
        """
        Extract data for a sunburst chart from various input formats.
        
        Args:
            data: Input data
            columns: Column names
            
        Returns:
            Tuple[List[str], List[str], List[float], List[str]]:
            Labels, parents, values, and ids
        """
        labels = []
        parents = []
        values = []
        ids = []
        
        if _HAS_PANDAS and isinstance(data, pd.DataFrame):
            # DataFrame data
            if columns and len(columns) >= 2:
                # Use specified columns
                if len(columns) == 2:
                    # Two columns: child, parent
                    child_col, parent_col = columns
                    
                    # Extract hierarchical relationships
                    for _, row in data.iterrows():
                        child = row[child_col]
                        parent = row[parent_col]
                        
                        if child not in ids:
                            ids.append(child)
                            labels.append(child)
                            parents.append(parent)
                            values.append(1)  # Default value
                elif len(columns) >= 3:
                    # Three or more columns: child, parent, value
                    child_col, parent_col, value_col = columns[:3]
                    
                    # Extract hierarchical relationships with values
                    for _, row in data.iterrows():
                        child = row[child_col]
                        parent = row[parent_col]
                        value = row[value_col]
                        
                        if child not in ids:
                            ids.append(child)
                            labels.append(child)
                            parents.append(parent)
                            values.append(value)
            else:
                # Try to find hierarchical structure in columns
                # Assume each column represents a level in the hierarchy
                if data.shape[1] >= 2:
                    # At least two columns needed for hierarchy
                    hierarchy_cols = data.columns.tolist()
                    
                    # Process each row
                    for _, row in data.iterrows():
                        # Process hierarchy levels
                        for i, col in enumerate(hierarchy_cols):
                            value = row[col]
                            
                            if pd.isna(value) or value == "":
                                continue
                                
                            # Generate ID
                            if i == 0:
                                # Root level
                                id_val = str(value)
                                parent_val = ""
                            else:
                                # Child level
                                id_val = f"{row[hierarchy_cols[i-1]]}_{value}"
                                parent_val = str(row[hierarchy_cols[i-1]])
                                
                            # Add to lists if not already present
                            if id_val not in ids:
                                ids.append(id_val)
                                labels.append(str(value))
                                parents.append(parent_val)
                                
                                # Use last column as value if numeric
                                if i == len(hierarchy_cols) - 1 and pd.api.types.is_numeric_dtype(data[col]):
                                    values.append(float(value))
                                else:
                                    values.append(1)  # Default value
                    
        elif isinstance(data, dict):
            # Dictionary data
            if "labels" in data and "parents" in data:
                # Pre-defined labels and parents
                labels = data["labels"]
                parents = data["parents"]
                
                # Values if available
                values = data.get("values", [1] * len(labels))
                
                # IDs if available, otherwise use labels
                ids = data.get("ids", labels)
            elif "root" in data:
                # Hierarchical structure with root
                root_data = data["root"]
                
                # Process hierarchical data recursively
                self._process_sunburst_node(root_data, "", ids, labels, parents, values)
                
        # Handle empty data
        if not ids:
            # Generate dummy data
            labels = ["Root", "A", "B", "A1", "A2", "B1", "B2"]
            parents = ["", "Root", "Root", "A", "A", "B", "B"]
            values = [0, 2, 3, 1, 1, 1, 2]
            ids = labels
            
        return labels, parents, values, ids
    
    def _process_sunburst_node(
        self,
        node: Dict[str, Any],
        parent: str,
        ids: List[str],
        labels: List[str],
        parents: List[str],
        values: List[float]
    ) -> None:
        """
        Process a node in a hierarchical structure for sunburst chart.
        
        Args:
            node: Node data
            parent: Parent ID
            ids: List of IDs
            labels: List of labels
            parents: List of parents
            values: List of values
        """
        # Extract node data
        if isinstance(node, dict):
            # Get node ID and label
            node_id = node.get("id", str(len(ids)))
            node_label = node.get("label", node_id)
            node_value = node.get("value", 1)
            
            # Add to lists
            ids.append(node_id)
            labels.append(node_label)
            parents.append(parent)
            values.append(node_value)
            
            # Process children
            if "children" in node:
                for child in node["children"]:
                    self._process_sunburst_node(child, node_id, ids, labels, parents, values)
        else:
            # Simple node
            node_id = str(node)
            
            # Add to lists
            ids.append(node_id)
            labels.append(node_id)
            parents.append(parent)
            values.append(1)
    
    def _extract_insights_from_sunburst_chart(
        self,
        labels: List[str],
        parents: List[str],
        values: List[float],
        ids: List[str]
    ) -> Dict[str, Any]:
        """
        Extract insights from sunburst chart data.
        
        Args:
            labels: Labels
            parents: Parents
            values: Values
            ids: IDs
            
        Returns:
            Dict[str, Any]: Extracted insights
        """
        insights = {}
        
        if not ids:
            return insights
            
        # Create tree structure
        tree = {}
        
        # Add nodes to tree
        for i, id_val in enumerate(ids):
            parent = parents[i]
            value = values[i] if i < len(values) else 1
            
            # Create node
            node = {
                "id": id_val,
                "label": labels[i] if i < len(labels) else id_val,
                "value": value,
                "children": []
            }
            
            # Add to tree
            tree[id_val] = node
            
        # Connect parents and children
        for i, id_val in enumerate(ids):
            parent = parents[i]
            
            if parent and parent in tree:
                # Add as child
                tree[parent]["children"].append(tree[id_val])
                
        # Find root nodes
        root_nodes = [tree[id_val] for i, id_val in enumerate(ids) if not parents[i] or parents[i] not in tree]
        
        # Calculate tree statistics
        tree_stats = {
            "n_nodes": len(ids),
            "n_root_nodes": len(root_nodes),
            "total_value": sum(values)
        }
        
        insights["tree_statistics"] = tree_stats
        
        # Calculate level statistics
        level_stats = {}
        
        # Process each node
        for i, id_val in enumerate(ids):
            parent = parents[i]
            value = values[i] if i < len(values) else 1
            
            # Determine level
            if not parent or parent not in tree:
                level = 0  # Root level
            else:
                # Find parent's level
                parent_level = 0
                current_parent = parent
                
                while current_parent:
                    # Find parent's index
                    try:
                        parent_idx = ids.index(current_parent)
                        current_parent = parents[parent_idx]
                        parent_level += 1
                    except ValueError:
                        # Parent not found
                        break
                        
                level = parent_level + 1
                
            # Add to level statistics
            if level not in level_stats:
                level_stats[level] = {
                    "count": 0,
                    "total_value": 0
                }
                
            level_stats[level]["count"] += 1
            level_stats[level]["total_value"] += value
            
        insights["level_statistics"] = level_stats
        
        # Find nodes with highest values
        node_values = []
        
        for i, id_val in enumerate(ids):
            if i < len(values):
                node_values.append({
                    "id": id_val,
                    "label": labels[i] if i < len(labels) else id_val,
                    "value": values[i]
                })
                
        # Sort by value
        node_values.sort(key=lambda x: x["value"], reverse=True)
        
        insights["top_nodes"] = node_values[:10]  # Top 10 nodes
        
        return insights
    
    def create_treemap(
        self,
        data: Any,
        columns: Optional[List[str]] = None,
        title: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Create a treemap visualization.
        
        Args:
            data: Input data with hierarchical structure
            columns: Column names
            title: Chart title
            **kwargs: Additional parameters
            
        Returns:
            Dict[str, Any]: Visualization output
        """
        # Check if matplotlib is available
        if not _HAS_MATPLOTLIB:
            raise ImportError("Matplotlib is required for treemaps")
            
        try:
            # Try to import squarify
            import squarify
        except ImportError:
            # Create a message that squarify is required
            fig, ax = plt.subplots(figsize=(6, 4), dpi=100)
            ax.text(0.5, 0.5, "Squarify library is required for treemaps.\n"
                            "Please install it with 'pip install squarify'.",
                    ha="center", va="center", fontsize=12)
            ax.axis('off')
            
            return {
                "figure": fig,
                "insights": {
                    "message": "Squarify library is required for treemaps. Please install it with 'pip install squarify'."
                }
            }
            
        # Process parameters
        width = kwargs.get("width", self.config.get("default_width"))
        height = kwargs.get("height", self.config.get("default_height"))
        
        # Create figure and axes
        fig, ax = self.layout_manager.create_figure("regular", width=width, height=height)
        
        # Extract data
        values, labels, colors, parents = self._extract_data_for_treemap(data, columns)
        
        # Create treemap
        squarify.plot(
            sizes=values,
            label=labels,
            color=colors,
            alpha=kwargs.get("alpha", 0.8),
            text_kwargs={"fontsize": kwargs.get("label_font_size", self.config.get("annotation_font_size", 10))},
            ax=ax
        )
        
        # Set title
        if title:
            ax.set_title(title, fontsize=self.config.get("title_font_size", 16))
            
        # Remove axis ticks and labels
        ax.set_xticks([])
        ax.set_yticks([])
        ax.axis('off')
        
        # Tight layout
        fig.tight_layout()
        
        # Extract insights
        insights = self._extract_insights_from_treemap(values, labels, parents)
        
        return {
            "figure": fig,
            "insights": insights
        }
    
    def _extract_data_for_treemap(
        self,
        data: Any,
        columns: Optional[List[str]] = None
    ) -> Tuple[List[float], List[str], List[str], List[str]]:
        """
        Extract data for a treemap from various input formats.
        
        Args:
            data: Input data
            columns: Column names
            
        Returns:
            Tuple[List[float], List[str], List[str], List[str]]:
            Values, labels, colors, and parents
        """
        values = []
        labels = []
        colors = []
        parents = []
        
        if _HAS_PANDAS and isinstance(data, pd.DataFrame):
            # DataFrame data
            if columns and len(columns) >= 2:
                # Use specified columns
                if len(columns) == 2:
                    # Two columns: labels, values
                    label_col, value_col = columns
                    
                    # Extract data
                    df = data[[label_col, value_col]].dropna()
                    
                    labels = df[label_col].astype(str).tolist()
                    values = df[value_col].tolist()
                    
                    # Generate colors
                    colors = self.color_manager.get_categorical_palette(len(labels))
                elif len(columns) >= 3:
                    # Three or more columns: labels, values, parents
                    label_col, value_col, parent_col = columns[:3]
                    
                    # Extract data
                    df = data[[label_col, value_col, parent_col]].dropna()
                    
                    labels = df[label_col].astype(str).tolist()
                    values = df[value_col].tolist()
                    parents = df[parent_col].astype(str).tolist()
                    
                    # Generate colors
                    colors = self.color_manager.get_categorical_palette(len(labels))
            else:
                # Try to find appropriate columns
                # First look for numeric columns for values
                num_cols = data.select_dtypes(include=["number"]).columns
                
                if len(num_cols) > 0:
                    # Use first numeric column as values
                    value_col = num_cols[0]
                    
                    # Use index or another column as labels
                    if len(data.columns) > 1:
                        for col in data.columns:
                            if col != value_col:
                                label_col = col
                                break
                    else:
                        # Use index as labels
                        labels = data.index.astype(str).tolist()
                        values = data[value_col].tolist()
                        
                        # Generate colors
                        colors = self.color_manager.get_categorical_palette(len(labels))
                        
                    if "label_col" in locals():
                        # Extract data
                        df = data[[label_col, value_col]].dropna()
                        
                        labels = df[label_col].astype(str).tolist()
                        values = df[value_col].tolist()
                        
                        # Generate colors
                        colors = self.color_manager.get_categorical_palette(len(labels))
                
        elif isinstance(data, dict):
            # Dictionary data
            if "values" in data and "labels" in data:
                # Pre-defined values and labels
                values = data["values"]
                labels = data["labels"]
                
                # Parents if available
                parents = data.get("parents", [])
                
                # Colors if available
                if "colors" in data:
                    colors = data["colors"]
                else:
                    # Generate colors
                    colors = self.color_manager.get_categorical_palette(len(labels))
            elif all(isinstance(v, (int, float)) for v in data.values()):
                # Dictionary of label -> value pairs
                labels = list(data.keys())
                values = list(data.values())
                
                # Generate colors
                colors = self.color_manager.get_categorical_palette(len(labels))
                
        elif isinstance(data, (list, tuple)):
            # List data
            if all(isinstance(item, (int, float)) for item in data):
                # List of values
                values = data
                
                # Generate labels
                labels = [f"Item {i+1}" for i in range(len(values))]
                
                # Generate colors
                colors = self.color_manager.get_categorical_palette(len(labels))
            elif all(isinstance(item, (list, tuple)) for item in data):
                # List of tuples
                if all(len(item) >= 2 for item in data):
                    # Extract values and labels
                    labels = [str(item[0]) for item in data]
                    values = [item[1] for item in data]
                    
                    # Check for parents
                    if all(len(item) >= 3 for item in data):
                        parents = [str(item[2]) for item in data]
                        
                    # Generate colors
                    colors = self.color_manager.get_categorical_palette(len(labels))
                    
            elif all(isinstance(item, dict) for item in data):
                # List of dictionaries
                if all("label" in item and "value" in item for item in data):
                    # Extract values and labels
                    labels = [str(item["label"]) for item in data]
                    values = [item["value"] for item in data]
                    
                    # Check for parents
                    if all("parent" in item for item in data):
                        parents = [str(item["parent"]) for item in data]
                        
                    # Check for colors
                    if all("color" in item for item in data):
                        colors = [item["color"] for item in data]
                    else:
                        # Generate colors
                        colors = self.color_manager.get_categorical_palette(len(labels))
                        
        # Handle empty data
        if not values:
            # Generate dummy data
            values = [100, 80, 60, 40, 20]
            labels = [f"Category {i+1}" for i in range(len(values))]
            colors = self.color_manager.get_categorical_palette(len(labels))
            
        # Ensure all lists have the same length
        min_len = min(len(values), len(labels))
        
        values = values[:min_len]
        labels = labels[:min_len]
        
        if colors:
            colors = colors[:min_len]
        else:
            colors = self.color_manager.get_categorical_palette(min_len)
            
        if parents:
            parents = parents[:min_len]
            
        return values, labels, colors, parents
    
    def _extract_insights_from_treemap(
        self,
        values: List[float],
        labels: List[str],
        parents: List[str]
    ) -> Dict[str, Any]:
        """
        Extract insights from treemap data.
        
        Args:
            values: Values
            labels: Labels
            parents: Parents
            
        Returns:
            Dict[str, Any]: Extracted insights
        """
        insights = {}
        
        if not values:
            return insights
            
        # Calculate basic statistics
        total = sum(values)
        
        # Sort by value
        sorted_items = sorted(zip(labels, values), key=lambda x: x[1], reverse=True)
        
        # Top items
        top_items = []
        
        for label, value in sorted_items[:10]:  # Top 10
            top_items.append({
                "label": label,
                "value": float(value),
                "percentage": float(value) / total * 100 if total else 0
            })
            
        insights["top_items"] = top_items
        
        # Calculate distribution metrics
        item_count = len(values)
        
        if item_count > 0:
            # Calculate entropy
            probabilities = [v / total for v in values] if total else [1 / item_count] * item_count
            entropy = -sum(p * math.log(p) if p > 0 else 0 for p in probabilities)
            
            # Maximum entropy for this number of items
            max_entropy = math.log(item_count) if item_count > 0 else 0
            
            # Normalized entropy (0 to 1)
            norm_entropy = entropy / max_entropy if max_entropy > 0 else 0
            
            insights["distribution"] = {
                "entropy": entropy,
                "normalized_entropy": norm_entropy,
                "evenness": "high" if norm_entropy > 0.75 else "medium" if norm_entropy > 0.4 else "low"
            }
            
        # Parse hierarchical structure if parents are available
        if parents and len(parents) == len(labels):
            # Create tree structure
            tree = {}
            
            # Add nodes to tree
            for i, label in enumerate(labels):
                parent = parents[i] if i < len(parents) else ""
                value = values[i] if i < len(values) else 0
                
                # Create node
                node = {
                    "label": label,
                    "value": value,
                    "children": []
                }
                
                # Add to tree
                tree[label] = node
                
            # Connect parents and children
            for i, label in enumerate(labels):
                parent = parents[i] if i < len(parents) else ""
                
                if parent and parent in tree:
                    # Add as child
                    tree[parent]["children"].append(tree[label])
                    
            # Find root nodes
            root_nodes = [tree[label] for i, label in enumerate(labels) if not parents[i] or parents[i] not in tree]
            
            # Calculate tree statistics
            tree_stats = {
                "n_nodes": len(labels),
                "n_root_nodes": len(root_nodes),
                "max_depth": 0  # Calculated below
            }
            
            # Calculate maximum depth
            def get_depth(node, depth=0):
                if not node["children"]:
                    return depth
                    
                return max(get_depth(child, depth + 1) for child in node["children"])
                
            if root_nodes:
                tree_stats["max_depth"] = max(get_depth(root) for root in root_nodes)
                
            insights["hierarchy"] = tree_stats
            
        return insights
    
    def create_timeline(
        self,
        data: Any,
        columns: Optional[List[str]] = None,
        title: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Create a timeline visualization.
        
        Args:
            data: Input data with events and dates
            columns: Column names
            title: Chart title
            **kwargs: Additional parameters
            
        Returns:
            Dict[str, Any]: Visualization output
        """
        # Check if matplotlib is available
        if not _HAS_MATPLOTLIB:
            raise ImportError("Matplotlib is required for timelines")
            
        # Process parameters
        width = kwargs.get("width", self.config.get("default_width"))
        height = kwargs.get("height", self.config.get("default_height"))
        show_labels = kwargs.get("show_labels", self.config.get("show_data_labels", True))
        
        # Extract data
        dates, events, categories, descriptions = self._extract_data_for_timeline(data, columns)
        
        # Create figure and axes
        fig, ax = self.layout_manager.create_figure("regular", width=width, height=height)
        
        # Create timeline
        self._plot_timeline(ax, dates, events, categories, descriptions, show_labels)
        
        # Set title
        if title:
            ax.set_title(title, fontsize=self.config.get("title_font_size", 16))
            
        # Tight layout
        fig.tight_layout()
        
        # Extract insights
        insights = self._extract_insights_from_timeline(dates, events, categories)
        
        return {
            "figure": fig,
            "insights": insights
        }
    
    def _extract_data_for_timeline(
        self,
        data: Any,
        columns: Optional[List[str]] = None
    ) -> Tuple[List[Any], List[str], List[str], List[str]]:
        """
        Extract data for a timeline from various input formats.
        
        Args:
            data: Input data
            columns: Column names
            
        Returns:
            Tuple[List[Any], List[str], List[str], List[str]]:
            Dates, events, categories, and descriptions
        """
        dates = []
        events = []
        categories = []
        descriptions = []
        
        if _HAS_PANDAS and isinstance(data, pd.DataFrame):
            # DataFrame data
            if columns and len(columns) >= 2:
                # Use specified columns
                date_col = columns[0]
                event_col = columns[1]
                
                # Check for category and description columns
                cat_col = columns[2] if len(columns) > 2 else None
                desc_col = columns[3] if len(columns) > 3 else None
                
                # Extract data
                df = data[[c for c in [date_col, event_col, cat_col, desc_col] if c is not None]].dropna(subset=[date_col, event_col])
                
                # Convert dates
                if pd.api.types.is_datetime64_any_dtype(df[date_col]):
                    # Already datetime
                    dates = df[date_col].tolist()
                else:
                    # Try to convert to datetime
                    try:
                        dates = pd.to_datetime(df[date_col]).tolist()
                    except:
                        # Failed to convert, use as-is
                        dates = df[date_col].tolist()
                        
                # Extract events
                events = df[event_col].astype(str).tolist()
                
                # Extract categories if available
                if cat_col is not None:
                    categories = df[cat_col].astype(str).tolist()
                    
                # Extract descriptions if available
                if desc_col is not None:
                    descriptions = df[desc_col].astype(str).tolist()
            else:
                # Try to find appropriate columns
                # Look for datetime columns
                date_cols = data.select_dtypes(include=["datetime64"]).columns
                
                if date_cols.empty:
                    # Try to convert object columns to datetime
                    for col in data.select_dtypes(include=["object"]).columns:
                        try:
                            dates = pd.to_datetime(data[col])
                            date_cols = [col]
                            break
                        except:
                            # Failed to convert
                            pass
                            
                if not date_cols.empty:
                    # Use first datetime column as dates
                    date_col = date_cols[0]
                    
                    # Find a column for events
                    for col in data.columns:
                        if col != date_col:
                            event_col = col
                            break
                            
                    if "event_col" in locals():
                        # Extract data
                        df = data[[date_col, event_col]].dropna()
                        
                        # Convert dates
                        if pd.api.types.is_datetime64_any_dtype(df[date_col]):
                            # Already datetime
                            dates = df[date_col].tolist()
                        else:
                            # Try to convert to datetime
                            try:
                                dates = pd.to_datetime(df[date_col]).tolist()
                            except:
                                # Failed to convert, use as-is
                                dates = df[date_col].tolist()
                                
                        # Extract events
                        events = df[event_col].astype(str).tolist()
                        
        elif isinstance(data, dict):
            # Dictionary data
            if "dates" in data and "events" in data:
                # Pre-defined dates and events
                dates = data["dates"]
                events = data["events"]
                
                # Categories if available
                if "categories" in data:
                    categories = data["categories"]
                    
                # Descriptions if available
                if "descriptions" in data:
                    descriptions = data["descriptions"]
            elif all(isinstance(v, (list, tuple)) for v in data.values()):
                # Dictionary of lists
                # Keys are categories, values are lists of [date, event] pairs
                for category, items in data.items():
                    for item in items:
                        if len(item) >= 2:
                            dates.append(item[0])
                            events.append(item[1])
                            categories.append(category)
                            
                            if len(item) >= 3:
                                descriptions.append(item[2])
                            else:
                                descriptions.append("")
                    
        elif isinstance(data, (list, tuple)):
            # List data
            if all(isinstance(item, (list, tuple)) for item in data):
                # List of tuples
                if all(len(item) >= 2 for item in data):
                    # Extract dates and events
                    dates = [item[0] for item in data]
                    events = [item[1] for item in data]
                    
                    # Check for categories
                    if all(len(item) >= 3 for item in data):
                        categories = [item[2] for item in data]
                        
                    # Check for descriptions
                    if all(len(item) >= 4 for item in data):
                        descriptions = [item[3] for item in data]
                        
            elif all(isinstance(item, dict) for item in data):
                # List of dictionaries
                if all("date" in item and "event" in item for item in data):
                    # Extract dates and events
                    dates = [item["date"] for item in data]
                    events = [item["event"] for item in data]
                    
                    # Check for categories
                    if all("category" in item for item in data):
                        categories = [item["category"] for item in data]
                        
                    # Check for descriptions
                    if all("description" in item for item in data):
                        descriptions = [item["description"] for item in data]
                        
        # Handle empty data
        if not dates or not events:
            # Generate dummy data
            import datetime as dt
            
            today = dt.datetime.now()
            
            dates = [
                today - dt.timedelta(days=30),
                today - dt.timedelta(days=20),
                today - dt.timedelta(days=10),
                today,
                today + dt.timedelta(days=10)
            ]
            
            events = [
                "Project Start",
                "Requirements Gathering",
                "Development",
                "Testing",
                "Deployment"
            ]
            
            categories = [
                "Planning",
                "Planning",
                "Implementation",
                "Implementation",
                "Deployment"
            ]
            
            descriptions = [
                "Project kickoff meeting",
                "Gather and document requirements",
                "Development of core features",
                "Quality assurance and testing",
                "Deploy to production"
            ]
            
        # Ensure all lists have the same length
        min_len = min(len(dates), len(events))
        
        dates = dates[:min_len]
        events = events[:min_len]
        
        if categories:
            if len(categories) < min_len:
                # Pad with empty categories
                categories = categories + [""] * (min_len - len(categories))
            else:
                categories = categories[:min_len]
        else:
            # Create empty categories
            categories = [""] * min_len
            
        if descriptions:
            if len(descriptions) < min_len:
                # Pad with empty descriptions
                descriptions = descriptions + [""] * (min_len - len(descriptions))
            else:
                descriptions = descriptions[:min_len]
        else:
            # Create empty descriptions
            descriptions = [""] * min_len
            
        # Sort by date if possible
        if all(isinstance(d, (dt.date, dt.datetime)) for d in dates):
            # Sort by date
            sorted_items = sorted(zip(dates, events, categories, descriptions))
            
            dates = [item[0] for item in sorted_items]
            events = [item[1] for item in sorted_items]
            categories = [item[2] for item in sorted_items]
            descriptions = [item[3] for item in sorted_items]
            
        return dates, events, categories, descriptions
    
    def _plot_timeline(
        self,
        ax: Any,
        dates: List[Any],
        events: List[str],
        categories: List[str],
        descriptions: List[str],
        show_labels: bool = True
    ) -> None:
        """
        Plot a timeline.
        
        Args:
            ax: Matplotlib axes
            dates: Dates
            events: Events
            categories: Categories
            descriptions: Descriptions
            show_labels: Whether to show labels
        """
        # Convert dates to numbers for plotting
        import datetime as dt
        
        # Check if dates are already datetime objects
        if all(isinstance(d, (dt.date, dt.datetime)) for d in dates):
            # Convert to matplotlib dates
            x = matplotlib.dates.date2num(dates)
            
            # Set date formatter
            date_format = matplotlib.dates.DateFormatter("%Y-%m-%d")
            ax.xaxis.set_major_formatter(date_format)
            
            # Set date locator
            ax.xaxis.set_major_locator(matplotlib.dates.AutoDateLocator())
        else:
            # Try to convert to numbers
            try:
                x = [float(d) for d in dates]
            except:
                # Use indices
                x = list(range(len(dates)))
                
                # Set custom tick labels
                ax.set_xticks(x)
                ax.set_xticklabels([str(d) for d in dates])
                
        # Get unique categories
        unique_cats = list(set(categories))
        unique_cats = [cat for cat in unique_cats if cat]  # Remove empty categories
        
        if not unique_cats:
            # No categories, plot all events on one line
            y = [0] * len(x)
            
            # Get colors
            colors = [self.color_manager.get_color("primary")] * len(x)
            
            # Plot points
            ax.scatter(x, y, c=colors, s=100, zorder=3)
            
            # Plot lines connecting points
            ax.plot(x, y, c=self.color_manager.get_color("grid"), linestyle="-", linewidth=1, zorder=2)
            
            # Add event labels
            if show_labels:
                for i, (xi, yi, event) in enumerate(zip(x, y, events)):
                    # Alternate label positions
                    if i % 2 == 0:
                        va = "bottom"
                        offset = 0.1
                    else:
                        va = "top"
                        offset = -0.1
                        
                    ax.text(
                        xi, yi + offset,
                        event,
                        ha="center",
                        va=va,
                        fontsize=self.config.get("annotation_font_size", 10),
                        rotation=45 if len(event) > 10 else 0,
                        rotation_mode="anchor"
                    )
        else:
            # Multiple categories, plot each on its own line
            # Create a mapping of category to y-position
            cat_positions = {cat: i for i, cat in enumerate(unique_cats)}
            
            # Default category for items without a category
            default_cat_pos = len(unique_cats)
            
            # Map each event to its y-position
            y = []
            
            for cat in categories:
                if cat and cat in cat_positions:
                    y.append(cat_positions[cat])
                else:
                    y.append(default_cat_pos)
                    
            # Get colors based on categories
            cat_colors = self.color_manager.get_categorical_palette(len(unique_cats) + 1)
            colors = [cat_colors[pos] for pos in y]
            
            # Plot points
            ax.scatter(x, y, c=colors, s=100, zorder=3)
            
            # Plot lines connecting points within the same category
            for cat in range(len(unique_cats) + 1):
                # Get points for this category
                cat_indices = [i for i, yi in enumerate(y) if yi == cat]
                
                if len(cat_indices) > 1:
                    # Plot line
                    cat_x = [x[i] for i in cat_indices]
                    cat_y = [y[i] for i in cat_indices]
                    
                    ax.plot(cat_x, cat_y, c=cat_colors[cat], linestyle="-", linewidth=1, alpha=0.5, zorder=2)
                    
            # Add event labels
            if show_labels:
                for i, (xi, yi, event) in enumerate(zip(x, y, events)):
                    ax.text(
                        xi, yi + 0.1,
                        event,
                        ha="center",
                        va="bottom",
                        fontsize=self.config.get("annotation_font_size", 10),
                        rotation=45 if len(event) > 10 else 0,
                        rotation_mode="anchor"
                    )
                    
            # Add category labels
            for cat, pos in cat_positions.items():
                ax.text(
                    ax.get_xlim()[0],
                    pos,
                    cat,
                    ha="right",
                    va="center",
                    fontsize=self.config.get("annotation_font_size", 10),
                    fontweight="bold"
                )
                
        # Add a horizontal line at each y-position
        for pos in range(len(unique_cats) + 1):
            ax.axhline(y=pos, color=self.color_manager.get_color("grid"), linestyle="--", linewidth=0.5, alpha=0.5, zorder=1)
            
        # Set y-limits
        ax.set_ylim([-0.5, len(unique_cats) + 0.5])
        
        # Remove y-axis ticks
        ax.set_yticks([])
        
        # Add grid
        ax.grid(True, axis="x", linestyle="--", alpha=0.3)
    
    def _extract_insights_from_timeline(
        self,
        dates: List[Any],
        events: List[str],
        categories: List[str]
    ) -> Dict[str, Any]:
        """
        Extract insights from timeline data.
        
        Args:
            dates: Dates
            events: Events
            categories: Categories
            
        Returns:
            Dict[str, Any]: Extracted insights
        """
        insights = {}
        
        if not dates or not events:
            return insights
            
        # Check if dates are datetime objects
        import datetime as dt
        
        if all(isinstance(d, (dt.date, dt.datetime)) for d in dates):
            # Calculate date range
            min_date = min(dates)
            max_date = max(dates)
            
            insights["date_range"] = {
                "start": min_date.strftime("%Y-%m-%d"),
                "end": max_date.strftime("%Y-%m-%d"),
                "days": (max_date - min_date).days
            }
            
            # Calculate time intervals between events
            intervals = []
            
            for i in range(1, len(dates)):
                interval = (dates[i] - dates[i-1]).days
                
                intervals.append({
                    "start_event": events[i-1],
                    "end_event": events[i],
                    "days": interval
                })
                
            # Sort by interval length
            intervals.sort(key=lambda x: x["days"], reverse=True)
            
            insights["intervals"] = {
                "longest": intervals[0] if intervals else None,
                "shortest": intervals[-1] if intervals else None,
                "average": sum(interval["days"] for interval in intervals) / len(intervals) if intervals else 0
            }
            
        # Count events by category
        cat_counts = {}
        
        for cat in categories:
            if cat:
                if cat not in cat_counts:
                    cat_counts[cat] = 0
                    
                cat_counts[cat] += 1
                
        # Sort by count
        cat_counts = sorted(cat_counts.items(), key=lambda x: x[1], reverse=True)
        
        if cat_counts:
            insights["category_counts"] = [
                {"category": cat, "count": count}
                for cat, count in cat_counts
            ]
            
        return insights
    
    def create_gantt_chart(
        self,
        data: Any,
        columns: Optional[List[str]] = None,
        title: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Create a Gantt chart visualization.
        
        Args:
            data: Input data with tasks, start dates, and end dates
            columns: Column names
            title: Chart title
            **kwargs: Additional parameters
            
        Returns:
            Dict[str, Any]: Visualization output
        """
        # Check if matplotlib is available
        if not _HAS_MATPLOTLIB:
            raise ImportError("Matplotlib is required for Gantt charts")
            
        # Process parameters
        width = kwargs.get("width", self.config.get("default_width"))
        height = kwargs.get("height", self.config.get("default_height"))
        show_labels = kwargs.get("show_labels", self.config.get("show_data_labels", True))
        
        # Create figure and axes
        fig, ax = self.layout_manager.create_figure("regular", width=width, height=height)
        
        # Extract data
        tasks, start_dates, end_dates, categories, progress = self._extract_data_for_gantt_chart(data, columns)
        
        # Create Gantt chart
        self._plot_gantt_chart(ax, tasks, start_dates, end_dates, categories, progress, show_labels)
        
        # Set title
        if title:
            ax.set_title(title, fontsize=self.config.get("title_font_size", 16))
            
        # Tight layout
        fig.tight_layout()
        
        # Extract insights
        insights = self._extract_insights_from_gantt_chart(tasks, start_dates, end_dates, categories, progress)
        
        return {
            "figure": fig,
            "insights": insights
        }
    
    def _extract_data_for_gantt_chart(
        self,
        data: Any,
        columns: Optional[List[str]] = None
    ) -> Tuple[List[str], List[Any], List[Any], List[str], List[float]]:
        """
        Extract data for a Gantt chart from various input formats.
        
        Args:
            data: Input data
            columns: Column names
            
        Returns:
            Tuple[List[str], List[Any], List[Any], List[str], List[float]]:
            Tasks, start dates, end dates, categories, and progress
        """
        tasks = []
        start_dates = []
        end_dates = []
        categories = []
        progress = []
        
        if _HAS_PANDAS and isinstance(data, pd.DataFrame):
            # DataFrame data
            if columns and len(columns) >= 3:
                # Use specified columns
                task_col = columns[0]
                start_col = columns[1]
                end_col = columns[2]
                
                # Check for category and progress columns
                cat_col = columns[3] if len(columns) > 3 else None
                prog_col = columns[4] if len(columns) > 4 else None
                
                # Extract data
                df = data[[c for c in [task_col, start_col, end_col, cat_col, prog_col] if c is not None]].dropna(subset=[task_col, start_col, end_col])
                
                # Extract tasks
                tasks = df[task_col].astype(str).tolist()
                
                # Convert dates
                if pd.api.types.is_datetime64_any_dtype(df[start_col]):
                    # Already datetime
                    start_dates = df[start_col].tolist()
                else:
                    # Try to convert to datetime
                    try:
                        start_dates = pd.to_datetime(df[start_col]).tolist()
                    except:
                        # Failed to convert, use as-is
                        start_dates = df[start_col].tolist()
                        
                if pd.api.types.is_datetime64_any_dtype(df[end_col]):
                    # Already datetime
                    end_dates = df[end_col].tolist()
                else:
                    # Try to convert to datetime
                    try:
                        end_dates = pd.to_datetime(df[end_col]).tolist()
                    except:
                        # Failed to convert, use as-is
                        end_dates = df[end_col].tolist()
                        
                # Extract categories if available
                if cat_col is not None:
                    categories = df[cat_col].astype(str).tolist()
                    
                # Extract progress if available
                if prog_col is not None:
                    progress = df[prog_col].tolist()
            else:
                # Try to find appropriate columns
                # Look for task-like column names
                task_cols = [col for col in data.columns if col.lower() in ["task", "name", "description", "activity"]]
                
                if task_cols:
                    task_col = task_cols[0]
                    
                    # Look for date columns
                    date_cols = data.select_dtypes(include=["datetime64"]).columns
                    
                    if len(date_cols) >= 2:
                        # Assume first two date columns are start and end
                        start_col, end_col = date_cols[:2]
                        
                        # Extract data
                        df = data[[task_col, start_col, end_col]].dropna()
                        
                        # Extract tasks
                        tasks = df[task_col].astype(str).tolist()
                        
                        # Extract dates
                        start_dates = df[start_col].tolist()
                        end_dates = df[end_col].tolist()
                        
                        # Look for category column
                        cat_cols = [col for col in data.columns if col.lower() in ["category", "group", "type"]]
                        
                        if cat_cols:
                            cat_col = cat_cols[0]
                            categories = data[cat_col].astype(str).tolist()
                            
                        # Look for progress column
                        prog_cols = [col for col in data.columns if col.lower() in ["progress", "completion", "percent"]]
                        
                        if prog_cols:
                            prog_col = prog_cols[0]
                            progress = data[prog_col].tolist()
                
        elif isinstance(data, dict):
            # Dictionary data
            if "tasks" in data and "start_dates" in data and "end_dates" in data:
                # Pre-defined tasks and dates
                tasks = data["tasks"]
                start_dates = data["start_dates"]
                end_dates = data["end_dates"]
                
                # Categories if available
                if "categories" in data:
                    categories = data["categories"]
                    
                # Progress if available
                if "progress" in data:
                    progress = data["progress"]
                    
        elif isinstance(data, (list, tuple)):
            # List data
            if all(isinstance(item, (list, tuple)) for item in data):
                # List of tuples
                if all(len(item) >= 3 for item in data):
                    # Extract tasks and dates
                    tasks = [item[0] for item in data]
                    start_dates = [item[1] for item in data]
                    end_dates = [item[2] for item in data]
                    
                    # Check for categories
                    if all(len(item) >= 4 for item in data):
                        categories = [item[3] for item in data]
                        
                    # Check for progress
                    if all(len(item) >= 5 for item in data):
                        progress = [item[4] for item in data]
                        
            elif all(isinstance(item, dict) for item in data):
                # List of dictionaries
                if all("task" in item and "start_date" in item and "end_date" in item for item in data):
                    # Extract tasks and dates
                    tasks = [item["task"] for item in data]
                    start_dates = [item["start_date"] for item in data]
                    end_dates = [item["end_date"] for item in data]
                    
                    # Check for categories
                    if all("category" in item for item in data):
                        categories = [item["category"] for item in data]
                        
                    # Check for progress
                    if all("progress" in item for item in data):
                        progress = [item["progress"] for item in data]
                        
        # Handle empty data
        if not tasks or not start_dates or not end_dates:
            # Generate dummy data
            import datetime as dt
            
            start_date = dt.datetime.now()
            
            tasks = [
                "Task 1",
                "Task 2",
                "Task 3",
                "Task 4",
                "Task 5"
            ]
            
            start_dates = [
                start_date,
                start_date + dt.timedelta(days=7),
                start_date + dt.timedelta(days=14),
                start_date + dt.timedelta(days=21),
                start_date + dt.timedelta(days=28)
            ]
            
            end_dates = [
                start_date + dt.timedelta(days=10),
                start_date + dt.timedelta(days=21),
                start_date + dt.timedelta(days=35),
                start_date + dt.timedelta(days=42),
                start_date + dt.timedelta(days=56)
            ]
            
            categories = [
                "Planning",
                "Planning",
                "Development",
                "Testing",
                "Deployment"
            ]
            
            progress = [
                100,
                75,
                50,
                25,
                0
            ]
            
        # Ensure all lists have the same length
        min_len = min(len(tasks), len(start_dates), len(end_dates))
        
        tasks = tasks[:min_len]
        start_dates = start_dates[:min_len]
        end_dates = end_dates[:min_len]
        
        if categories:
            if len(categories) < min_len:
                # Pad with empty categories
                categories = categories + [""] * (min_len - len(categories))
            else:
                categories = categories[:min_len]
        else:
            # Create empty categories
            categories = [""] * min_len
            
        if progress:
            if len(progress) < min_len:
                # Pad with zeros
                progress = progress + [0] * (min_len - len(progress))
            else:
                progress = progress[:min_len]
        else:
            # Create empty progress
            progress = [0] * min_len
            
        # Sort by start date if possible
        import datetime as dt
        
        if all(isinstance(d, (dt.date, dt.datetime)) for d in start_dates):
            # Sort by start date
            sorted_items = sorted(zip(tasks, start_dates, end_dates, categories, progress), key=lambda x: x[1])
            
            tasks = [item[0] for item in sorted_items]
            start_dates = [item[1] for item in sorted_items]
            end_dates = [item[2] for item in sorted_items]
            categories = [item[3] for item in sorted_items]
            progress = [item[4] for item in sorted_items]
            
        return tasks, start_dates, end_dates, categories, progress
    
    def _plot_gantt_chart(
        self,
        ax: Any,
        tasks: List[str],
        start_dates: List[Any],
        end_dates: List[Any],
        categories: List[str],
        progress: List[float],
        show_labels: bool = True
    ) -> None:
        """
        Plot a Gantt chart.
        
        Args:
            ax: Matplotlib axes
            tasks: Tasks
            start_dates: Start dates
            end_dates: End dates
            categories: Categories
            progress: Progress values
            show_labels: Whether to show labels
        """
        # Convert dates to numbers for plotting
        import datetime as dt
        
        # Check if dates are already datetime objects
        if all(isinstance(d, (dt.date, dt.datetime)) for d in start_dates + end_dates):
            # Convert to matplotlib dates
            start_num = matplotlib.dates.date2num(start_dates)
            end_num = matplotlib.dates.date2num(end_dates)
            
            # Set date formatter
            date_format = matplotlib.dates.DateFormatter("%Y-%m-%d")
            ax.xaxis.set_major_formatter(date_format)
            
            # Set date locator
            ax.xaxis.set_major_locator(matplotlib.dates.AutoDateLocator())
        else:
            # Try to convert to numbers
            try:
                start_num = [float(d) for d in start_dates]
                end_num = [float(d) for d in end_dates]
            except:
                # Use indices
                start_num = list(range(len(start_dates)))
                end_num = [s + 1 for s in start_num]
                
                # Set custom tick labels
                ax.set_xticks(sorted(set(start_num + end_num)))
                ax.set_xticklabels([str(d) for d in sorted(set(start_dates + end_dates))])
                
        # Get unique categories
        unique_cats = list(set(categories))
        unique_cats = [cat for cat in unique_cats if cat]  # Remove empty categories
        
        # Get colors based on categories
        if unique_cats:
            cat_colors = self.color_manager.get_categorical_palette(len(unique_cats))
            cat_map = {cat: i for i, cat in enumerate(unique_cats)}
            
            # Map each task to its color
            colors = []
            
            for cat in categories:
                if cat and cat in cat_map:
                    colors.append(cat_colors[cat_map[cat]])
                else:
                    colors.append(self.color_manager.get_color("primary"))
        else:
            # Single color for all tasks
            colors = [self.color_manager.get_color("primary")] * len(tasks)
            
        # Plot Gantt chart bars
        y_pos = np.arange(len(tasks))
        
        for i, (start, end, color, prog) in enumerate(zip(start_num, end_num, colors, progress)):
            # Calculate duration
            duration = end - start
            
            # Create task bar
            ax.barh(
                y_pos[i],
                duration,
                left=start,
                height=0.5,
                color=color,
                alpha=0.8,
                edgecolor=self.color_manager.get_color("text"),
                linewidth=0.5
            )
            
            # Add progress overlay if progress > 0
            if prog > 0:
                # Calculate progress width
                progress_width = duration * (prog / 100)
                
                # Create progress bar
                ax.barh(
                    y_pos[i],
                    progress_width,
                    left=start,
                    height=0.5,
                    color=self.color_manager.get_color("secondary"),
                    alpha=0.6,
                    edgecolor=self.color_manager.get_color("text"),
                    linewidth=0.5
                )