#!/usr/bin/env python3
"""
ULTRA: Logical Reasoning Engine

This module implements the Logical Reasoning Engine component of the Neuro-Symbolic 
Integration subsystem. It provides capabilities for deductive, inductive, and abductive 
reasoning using various logic systems including classical, probabilistic, and fuzzy logic.

The engine maintains a knowledge base of facts and rules, performs inference over this
knowledge base, and generates explanations for derived conclusions.

Author: ULTRA Development Team
"""

import os
import sys
import logging
import numpy as np
import re
import time
import json
from typing import Dict, List, Tuple, Set, Any, Union, Optional, Callable, FrozenSet
from enum import Enum, auto
from collections import defaultdict, deque, Counter
import itertools
import networkx as nx
import z3
import sympy as sp
from sympy.logic.boolalg import And, Or, Not, Implies, Equivalent
import torch
import torch.nn as nn
import torch.nn.functional as F
from abc import ABC, abstractmethod
import math

# Configure logging
logger = logging.getLogger(__name__)
handler = logging.StreamHandler()
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
handler.setFormatter(formatter)
logger.addHandler(handler)
logger.setLevel(logging.INFO)

# Enums and type definitions
class ReasoningType(Enum):
    """Types of reasoning supported by the Logical Reasoning Engine."""
    DEDUCTIVE = auto()
    INDUCTIVE = auto()
    ABDUCTIVE = auto()
    ANALOGICAL = auto()

class LogicType(Enum):
    """Types of logic supported by the Logical Reasoning Engine."""
    CLASSICAL = auto()
    PROBABILISTIC = auto()
    FUZZY = auto()
    MODAL = auto()
    TEMPORAL = auto()

class SymbolicForm(Enum):
    """Types of symbolic forms that can be used for representation."""
    PREDICATE = auto()
    FOL = auto()
    HORN = auto()
    ASP = auto()
    DESCRIPTION_LOGIC = auto()


# Knowledge representation classes
class Expression(ABC):
    """
    Base class for logical expressions.
    
    This abstract class defines the common interface for all logical expressions
    and provides methods for common operations like conversion to different formats,
    evaluation, and explanation.
    """
    
    @abstractmethod
    def evaluate(self, interpretation: Dict[str, bool]) -> bool:
        """
        Evaluate the expression in a given interpretation.
        
        Args:
            interpretation: Dictionary mapping variable names to boolean values
            
        Returns:
            Boolean result of evaluation
        """
        pass
    
    @abstractmethod
    def variables(self) -> Set[str]:
        """
        Get the set of variables used in the expression.
        
        Returns:
            Set of variable names
        """
        pass
    
    @abstractmethod
    def to_z3(self) -> z3.ExprRef:
        """
        Convert the expression to Z3 format.
        
        Returns:
            Z3 expression
        """
        pass
    
    @abstractmethod
    def to_sympy(self) -> sp.Expr:
        """
        Convert the expression to SymPy format.
        
        Returns:
            SymPy expression
        """
        pass
    
    @abstractmethod
    def to_neural(self) -> torch.Tensor:
        """
        Convert the expression to a neural representation.
        
        Returns:
            Neural tensor representation
        """
        pass
    
    @abstractmethod
    def to_string(self) -> str:
        """
        Convert the expression to a human-readable string.
        
        Returns:
            String representation
        """
        pass
    
    @abstractmethod
    def substitute(self, substitution: Dict[str, 'Expression']) -> 'Expression':
        """
        Apply a substitution to the expression.
        
        Args:
            substitution: Dictionary mapping variable names to expressions
            
        Returns:
            New expression with substitution applied
        """
        pass
    
    def __str__(self) -> str:
        return self.to_string()
    
    def __repr__(self) -> str:
        return self.to_string()


class Atom(Expression):
    """
    Atomic proposition or variable.
    """
    
    def __init__(self, name: str):
        """
        Initialize an atomic proposition.
        
        Args:
            name: Name of the atom
        """
        self.name = name
    
    def evaluate(self, interpretation: Dict[str, bool]) -> bool:
        """
        Evaluate the atom in a given interpretation.
        
        Args:
            interpretation: Dictionary mapping variable names to boolean values
            
        Returns:
            Boolean value of the atom
        """
        return interpretation.get(self.name, False)
    
    def variables(self) -> Set[str]:
        """
        Get the set of variables used in the atom.
        
        Returns:
            Set containing the atom name
        """
        return {self.name}
    
    def to_z3(self) -> z3.ExprRef:
        """
        Convert the atom to Z3 format.
        
        Returns:
            Z3 boolean variable
        """
        return z3.Bool(self.name)
    
    def to_sympy(self) -> sp.Expr:
        """
        Convert the atom to SymPy format.
        
        Returns:
            SymPy symbol
        """
        return sp.Symbol(self.name)
    
    def to_neural(self) -> torch.Tensor:
        """
        Convert the atom to a neural representation.
        
        Returns:
            Neural tensor representation (one-hot encoding)
        """
        # This is a simplified representation
        # In a real system, this would be a learned embedding
        return torch.tensor([1.0])
    
    def to_string(self) -> str:
        """
        Convert the atom to a human-readable string.
        
        Returns:
            String representation
        """
        return self.name
    
    def substitute(self, substitution: Dict[str, Expression]) -> Expression:
        """
        Apply a substitution to the atom.
        
        Args:
            substitution: Dictionary mapping variable names to expressions
            
        Returns:
            Resulting expression
        """
        return substitution.get(self.name, self)


class Constant(Expression):
    """
    Logical constant (True or False).
    """
    
    def __init__(self, value: bool):
        """
        Initialize a logical constant.
        
        Args:
            value: Boolean value
        """
        self.value = value
    
    def evaluate(self, interpretation: Dict[str, bool]) -> bool:
        """
        Evaluate the constant.
        
        Args:
            interpretation: Dictionary mapping variable names to boolean values
            
        Returns:
            Boolean value of the constant
        """
        return self.value
    
    def variables(self) -> Set[str]:
        """
        Get the set of variables used in the constant.
        
        Returns:
            Empty set
        """
        return set()
    
    def to_z3(self) -> z3.ExprRef:
        """
        Convert the constant to Z3 format.
        
        Returns:
            Z3 boolean constant
        """
        return z3.BoolVal(self.value)
    
    def to_sympy(self) -> sp.Expr:
        """
        Convert the constant to SymPy format.
        
        Returns:
            SymPy boolean constant
        """
        return sp.true if self.value else sp.false
    
    def to_neural(self) -> torch.Tensor:
        """
        Convert the constant to a neural representation.
        
        Returns:
            Neural tensor representation
        """
        return torch.tensor([1.0 if self.value else 0.0])
    
    def to_string(self) -> str:
        """
        Convert the constant to a human-readable string.
        
        Returns:
            String representation
        """
        return "TRUE" if self.value else "FALSE"
    
    def substitute(self, substitution: Dict[str, Expression]) -> Expression:
        """
        Apply a substitution to the constant.
        
        Args:
            substitution: Dictionary mapping variable names to expressions
            
        Returns:
            The constant itself (constants are not affected by substitution)
        """
        return self


class Negation(Expression):
    """
    Logical negation (NOT).
    """
    
    def __init__(self, expr: Expression):
        """
        Initialize a negation.
        
        Args:
            expr: Expression to negate
        """
        self.expr = expr
    
    def evaluate(self, interpretation: Dict[str, bool]) -> bool:
        """
        Evaluate the negation in a given interpretation.
        
        Args:
            interpretation: Dictionary mapping variable names to boolean values
            
        Returns:
            Boolean result of evaluation
        """
        return not self.expr.evaluate(interpretation)
    
    def variables(self) -> Set[str]:
        """
        Get the set of variables used in the negation.
        
        Returns:
            Set of variable names
        """
        return self.expr.variables()
    
    def to_z3(self) -> z3.ExprRef:
        """
        Convert the negation to Z3 format.
        
        Returns:
            Z3 expression
        """
        return z3.Not(self.expr.to_z3())
    
    def to_sympy(self) -> sp.Expr:
        """
        Convert the negation to SymPy format.
        
        Returns:
            SymPy expression
        """
        return sp.Not(self.expr.to_sympy())
    
    def to_neural(self) -> torch.Tensor:
        """
        Convert the negation to a neural representation.
        
        Returns:
            Neural tensor representation
        """
        # In a neural representation, negation might be represented as 1 - x
        return 1.0 - self.expr.to_neural()
    
    def to_string(self) -> str:
        """
        Convert the negation to a human-readable string.
        
        Returns:
            String representation
        """
        if isinstance(self.expr, Atom) or isinstance(self.expr, Constant):
            return f"NOT {self.expr.to_string()}"
        else:
            return f"NOT ({self.expr.to_string()})"
    
    def substitute(self, substitution: Dict[str, Expression]) -> Expression:
        """
        Apply a substitution to the negation.
        
        Args:
            substitution: Dictionary mapping variable names to expressions
            
        Returns:
            New negation with substitution applied
        """
        return Negation(self.expr.substitute(substitution))


class Conjunction(Expression):
    """
    Logical conjunction (AND).
    """
    
    def __init__(self, left: Expression, right: Expression):
        """
        Initialize a conjunction.
        
        Args:
            left: Left operand
            right: Right operand
        """
        self.left = left
        self.right = right
    
    def evaluate(self, interpretation: Dict[str, bool]) -> bool:
        """
        Evaluate the conjunction in a given interpretation.
        
        Args:
            interpretation: Dictionary mapping variable names to boolean values
            
        Returns:
            Boolean result of evaluation
        """
        return self.left.evaluate(interpretation) and self.right.evaluate(interpretation)
    
    def variables(self) -> Set[str]:
        """
        Get the set of variables used in the conjunction.
        
        Returns:
            Set of variable names
        """
        return self.left.variables().union(self.right.variables())
    
    def to_z3(self) -> z3.ExprRef:
        """
        Convert the conjunction to Z3 format.
        
        Returns:
            Z3 expression
        """
        return z3.And(self.left.to_z3(), self.right.to_z3())
    
    def to_sympy(self) -> sp.Expr:
        """
        Convert the conjunction to SymPy format.
        
        Returns:
            SymPy expression
        """
        return sp.And(self.left.to_sympy(), self.right.to_sympy())
    
    def to_neural(self) -> torch.Tensor:
        """
        Convert the conjunction to a neural representation.
        
        Returns:
            Neural tensor representation
        """
        # In a neural representation, conjunction might be represented as min(x, y)
        # or as a product for values between 0 and 1
        left_tensor = self.left.to_neural()
        right_tensor = self.right.to_neural()
        return torch.min(left_tensor, right_tensor)
    
    def to_string(self) -> str:
        """
        Convert the conjunction to a human-readable string.
        
        Returns:
            String representation
        """
        left_str = self.left.to_string()
        if not (isinstance(self.left, Atom) or isinstance(self.left, Constant)):
            left_str = f"({left_str})"
            
        right_str = self.right.to_string()
        if not (isinstance(self.right, Atom) or isinstance(self.right, Constant)):
            right_str = f"({right_str})"
            
        return f"{left_str} AND {right_str}"
    
    def substitute(self, substitution: Dict[str, Expression]) -> Expression:
        """
        Apply a substitution to the conjunction.
        
        Args:
            substitution: Dictionary mapping variable names to expressions
            
        Returns:
            New conjunction with substitution applied
        """
        return Conjunction(self.left.substitute(substitution), self.right.substitute(substitution))


class Disjunction(Expression):
    """
    Logical disjunction (OR).
    """
    
    def __init__(self, left: Expression, right: Expression):
        """
        Initialize a disjunction.
        
        Args:
            left: Left operand
            right: Right operand
        """
        self.left = left
        self.right = right
    
    def evaluate(self, interpretation: Dict[str, bool]) -> bool:
        """
        Evaluate the disjunction in a given interpretation.
        
        Args:
            interpretation: Dictionary mapping variable names to boolean values
            
        Returns:
            Boolean result of evaluation
        """
        return self.left.evaluate(interpretation) or self.right.evaluate(interpretation)
    
    def variables(self) -> Set[str]:
        """
        Get the set of variables used in the disjunction.
        
        Returns:
            Set of variable names
        """
        return self.left.variables().union(self.right.variables())
    
    def to_z3(self) -> z3.ExprRef:
        """
        Convert the disjunction to Z3 format.
        
        Returns:
            Z3 expression
        """
        return z3.Or(self.left.to_z3(), self.right.to_z3())
    
    def to_sympy(self) -> sp.Expr:
        """
        Convert the disjunction to SymPy format.
        
        Returns:
            SymPy expression
        """
        return sp.Or(self.left.to_sympy(), self.right.to_sympy())
    
    def to_neural(self) -> torch.Tensor:
        """
        Convert the disjunction to a neural representation.
        
        Returns:
            Neural tensor representation
        """
        # In a neural representation, disjunction might be represented as max(x, y)
        # or as x + y - x*y for values between 0 and 1
        left_tensor = self.left.to_neural()
        right_tensor = self.right.to_neural()
        return torch.max(left_tensor, right_tensor)
    
    def to_string(self) -> str:
        """
        Convert the disjunction to a human-readable string.
        
        Returns:
            String representation
        """
        left_str = self.left.to_string()
        if not (isinstance(self.left, Atom) or isinstance(self.left, Constant)):
            left_str = f"({left_str})"
            
        right_str = self.right.to_string()
        if not (isinstance(self.right, Atom) or isinstance(self.right, Constant)):
            right_str = f"({right_str})"
            
        return f"{left_str} OR {right_str}"
    
    def substitute(self, substitution: Dict[str, Expression]) -> Expression:
        """
        Apply a substitution to the disjunction.
        
        Args:
            substitution: Dictionary mapping variable names to expressions
            
        Returns:
            New disjunction with substitution applied
        """
        return Disjunction(self.left.substitute(substitution), self.right.substitute(substitution))


class Implication(Expression):
    """
    Logical implication (IMPLIES).
    """
    
    def __init__(self, premise: Expression, conclusion: Expression):
        """
        Initialize an implication.
        
        Args:
            premise: Premise expression
            conclusion: Conclusion expression
        """
        self.premise = premise
        self.conclusion = conclusion
    
    def evaluate(self, interpretation: Dict[str, bool]) -> bool:
        """
        Evaluate the implication in a given interpretation.
        
        Args:
            interpretation: Dictionary mapping variable names to boolean values
            
        Returns:
            Boolean result of evaluation
        """
        premise_value = self.premise.evaluate(interpretation)
        conclusion_value = self.conclusion.evaluate(interpretation)
        return (not premise_value) or conclusion_value
    
    def variables(self) -> Set[str]:
        """
        Get the set of variables used in the implication.
        
        Returns:
            Set of variable names
        """
        return self.premise.variables().union(self.conclusion.variables())
    
    def to_z3(self) -> z3.ExprRef:
        """
        Convert the implication to Z3 format.
        
        Returns:
            Z3 expression
        """
        return z3.Implies(self.premise.to_z3(), self.conclusion.to_z3())
    
    def to_sympy(self) -> sp.Expr:
        """
        Convert the implication to SymPy format.
        
        Returns:
            SymPy expression
        """
        return sp.Implies(self.premise.to_sympy(), self.conclusion.to_sympy())
    
    def to_neural(self) -> torch.Tensor:
        """
        Convert the implication to a neural representation.
        
        Returns:
            Neural tensor representation
        """
        # In a neural representation, implication might be represented as max(1-x, y)
        premise_tensor = self.premise.to_neural()
        conclusion_tensor = self.conclusion.to_neural()
        return torch.max(1.0 - premise_tensor, conclusion_tensor)
    
    def to_string(self) -> str:
        """
        Convert the implication to a human-readable string.
        
        Returns:
            String representation
        """
        premise_str = self.premise.to_string()
        if not (isinstance(self.premise, Atom) or isinstance(self.premise, Constant)):
            premise_str = f"({premise_str})"
            
        conclusion_str = self.conclusion.to_string()
        if not (isinstance(self.conclusion, Atom) or isinstance(self.conclusion, Constant)):
            conclusion_str = f"({conclusion_str})"
            
        return f"{premise_str} IMPLIES {conclusion_str}"
    
    def substitute(self, substitution: Dict[str, Expression]) -> Expression:
        """
        Apply a substitution to the implication.
        
        Args:
            substitution: Dictionary mapping variable names to expressions
            
        Returns:
            New implication with substitution applied
        """
        return Implication(self.premise.substitute(substitution), self.conclusion.substitute(substitution))


class Equivalence(Expression):
    """
    Logical equivalence (EQUIVALENT).
    """
    
    def __init__(self, left: Expression, right: Expression):
        """
        Initialize an equivalence.
        
        Args:
            left: Left operand
            right: Right operand
        """
        self.left = left
        self.right = right
    
    def evaluate(self, interpretation: Dict[str, bool]) -> bool:
        """
        Evaluate the equivalence in a given interpretation.
        
        Args:
            interpretation: Dictionary mapping variable names to boolean values
            
        Returns:
            Boolean result of evaluation
        """
        left_value = self.left.evaluate(interpretation)
        right_value = self.right.evaluate(interpretation)
        return left_value == right_value
    
    def variables(self) -> Set[str]:
        """
        Get the set of variables used in the equivalence.
        
        Returns:
            Set of variable names
        """
        return self.left.variables().union(self.right.variables())
    
    def to_z3(self) -> z3.ExprRef:
        """
        Convert the equivalence to Z3 format.
        
        Returns:
            Z3 expression
        """
        return self.left.to_z3() == self.right.to_z3()
    
    def to_sympy(self) -> sp.Expr:
        """
        Convert the equivalence to SymPy format.
        
        Returns:
            SymPy expression
        """
        return sp.Equivalent(self.left.to_sympy(), self.right.to_sympy())
    
    def to_neural(self) -> torch.Tensor:
        """
        Convert the equivalence to a neural representation.
        
        Returns:
            Neural tensor representation
        """
        # In a neural representation, equivalence might be represented as 1 - |x - y|
        left_tensor = self.left.to_neural()
        right_tensor = self.right.to_neural()
        return 1.0 - torch.abs(left_tensor - right_tensor)
    
    def to_string(self) -> str:
        """
        Convert the equivalence to a human-readable string.
        
        Returns:
            String representation
        """
        left_str = self.left.to_string()
        if not (isinstance(self.left, Atom) or isinstance(self.left, Constant)):
            left_str = f"({left_str})"
            
        right_str = self.right.to_string()
        if not (isinstance(self.right, Atom) or isinstance(self.right, Constant)):
            right_str = f"({right_str})"
            
        return f"{left_str} EQUIVALENT {right_str}"
    
    def substitute(self, substitution: Dict[str, Expression]) -> Expression:
        """
        Apply a substitution to the equivalence.
        
        Args:
            substitution: Dictionary mapping variable names to expressions
            
        Returns:
            New equivalence with substitution applied
        """
        return Equivalence(self.left.substitute(substitution), self.right.substitute(substitution))


class ProbabilisticExpression:
    """
    Expression with an associated probability.
    """
    
    def __init__(self, expr: Expression, probability: float):
        """
        Initialize a probabilistic expression.
        
        Args:
            expr: Logical expression
            probability: Probability value in [0, 1]
        """
        self.expr = expr
        self.probability = max(0.0, min(1.0, probability))  # Clamp to [0, 1]
    
    def to_string(self) -> str:
        """
        Convert the probabilistic expression to a human-readable string.
        
        Returns:
            String representation
        """
        return f"P({self.expr.to_string()}) = {self.probability:.4f}"
    
    def __str__(self) -> str:
        return self.to_string()
    
    def __repr__(self) -> str:
        return self.to_string()


class ConditionalProbabilisticExpression:
    """
    Conditional probabilistic expression.
    """
    
    def __init__(self, expr: Expression, condition: Expression, probability: float):
        """
        Initialize a conditional probabilistic expression.
        
        Args:
            expr: Target expression
            condition: Condition expression
            probability: Conditional probability value in [0, 1]
        """
        self.expr = expr
        self.condition = condition
        self.probability = max(0.0, min(1.0, probability))  # Clamp to [0, 1]
    
    def to_string(self) -> str:
        """
        Convert the conditional probabilistic expression to a human-readable string.
        
        Returns:
            String representation
        """
        return f"P({self.expr.to_string()} | {self.condition.to_string()}) = {self.probability:.4f}"
    
    def __str__(self) -> str:
        return self.to_string()
    
    def __repr__(self) -> str:
        return self.to_string()


class FuzzyExpression:
    """
    Expression with a fuzzy truth value.
    """
    
    def __init__(self, expr: Expression, membership: float):
        """
        Initialize a fuzzy expression.
        
        Args:
            expr: Logical expression
            membership: Membership value in [0, 1]
        """
        self.expr = expr
        self.membership = max(0.0, min(1.0, membership))  # Clamp to [0, 1]
    
    def to_string(self) -> str:
        """
        Convert the fuzzy expression to a human-readable string.
        
        Returns:
            String representation
        """
        return f"μ({self.expr.to_string()}) = {self.membership:.4f}"
    
    def __str__(self) -> str:
        return self.to_string()
    
    def __repr__(self) -> str:
        return self.to_string()


class KnowledgeBase:
    """
    Knowledge base for the logical reasoning engine.
    
    Maintains a collection of facts and rules in various logic systems.
    """
    
    def __init__(self, name: str = "Default KB"):
        """
        Initialize a knowledge base.
        
        Args:
            name: Name of the knowledge base
        """
        self.name = name
        self.facts = set()                    # Set of facts (classical logic)
        self.rules = set()                    # Set of rules (classical logic)
        self.probabilistic_facts = set()      # Set of probabilistic facts
        self.conditional_probabilities = set() # Set of conditional probabilities
        self.fuzzy_facts = set()              # Set of fuzzy facts
        
        # Indices for efficient lookup
        self.fact_index = {}      # Maps predicate names to sets of facts
        self.rule_index = {}      # Maps predicate names to sets of rules
        
        # Z3 solver for classical reasoning
        self.solver = z3.Solver()
        
        # Flag to track if solver needs updating
        self.solver_needs_update = True
    
    def add_fact(self, fact: Expression) -> None:
        """
        Add a fact to the knowledge base.
        
        Args:
            fact: Fact to add
        """
        self.facts.add(fact)
        
        # Update fact index
        for var in fact.variables():
            if var not in self.fact_index:
                self.fact_index[var] = set()
            self.fact_index[var].add(fact)
        
        # Add to Z3 solver
        if not self.solver_needs_update:
            self.solver.add(fact.to_z3())
        else:
            self.solver_needs_update = True
    
    def add_rule(self, rule: Expression) -> None:
        """
        Add a rule to the knowledge base.
        
        Args:
            rule: Rule to add
        """
        self.rules.add(rule)
        
        # Update rule index
        for var in rule.variables():
            if var not in self.rule_index:
                self.rule_index[var] = set()
            self.rule_index[var].add(rule)
        
        # Add to Z3 solver
        if not self.solver_needs_update:
            self.solver.add(rule.to_z3())
        else:
            self.solver_needs_update = True
    
    def add_probabilistic_fact(self, expr: Expression, probability: float) -> None:
        """
        Add a probabilistic fact to the knowledge base.
        
        Args:
            expr: Logical expression
            probability: Probability value in [0, 1]
        """
        prob_expr = ProbabilisticExpression(expr, probability)
        self.probabilistic_facts.add(prob_expr)
    
    def add_conditional_probability(self, expr: Expression, condition: Expression, probability: float) -> None:
        """
        Add a conditional probability to the knowledge base.
        
        Args:
            expr: Target expression
            condition: Condition expression
            probability: Conditional probability value in [0, 1]
        """
        cond_prob_expr = ConditionalProbabilisticExpression(expr, condition, probability)
        self.conditional_probabilities.add(cond_prob_expr)
    
    def add_fuzzy_fact(self, expr: Expression, membership: float) -> None:
        """
        Add a fuzzy fact to the knowledge base.
        
        Args:
            expr: Logical expression
            membership: Membership value in [0, 1]
        """
        fuzzy_expr = FuzzyExpression(expr, membership)
        self.fuzzy_facts.add(fuzzy_expr)
    
    def update_solver(self) -> None:
        """
        Update the Z3 solver with the current knowledge base.
        """
        if self.solver_needs_update:
            self.solver = z3.Solver()
            for fact in self.facts:
                self.solver.add(fact.to_z3())
            for rule in self.rules:
                self.solver.add(rule.to_z3())
            self.solver_needs_update = False
    
    def get_facts_with_variable(self, var: str) -> Set[Expression]:
        """
        Get all facts that use a specific variable.
        
        Args:
            var: Variable name
            
        Returns:
            Set of facts
        """
        return self.fact_index.get(var, set())
    
    def get_rules_with_variable(self, var: str) -> Set[Expression]:
        """
        Get all rules that use a specific variable.
        
        Args:
            var: Variable name
            
        Returns:
            Set of rules
        """
        return self.rule_index.get(var, set())
    
    def dump_to_file(self, file_path: str) -> None:
        """
        Save the knowledge base to a file.
        
        Args:
            file_path: Path to save the knowledge base
        """
        facts_str = [fact.to_string() for fact in self.facts]
        rules_str = [rule.to_string() for rule in self.rules]
        prob_facts_str = [f"{fact.expr.to_string()},{fact.probability}" for fact in self.probabilistic_facts]
        cond_prob_str = [f"{cp.expr.to_string()},{cp.condition.to_string()},{cp.probability}" 
                        for cp in self.conditional_probabilities]
        fuzzy_facts_str = [f"{fact.expr.to_string()},{fact.membership}" for fact in self.fuzzy_facts]
        
        data = {
            "name": self.name,
            "facts": facts_str,
            "rules": rules_str,
            "probabilistic_facts": prob_facts_str,
            "conditional_probabilities": cond_prob_str,
            "fuzzy_facts": fuzzy_facts_str
        }
        
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        
        with open(file_path, 'w') as f:
            json.dump(data, f, indent=2)
    
    def load_from_file(self, file_path: str, parser: 'ExpressionParser') -> None:
        """
        Load the knowledge base from a file.
        
        Args:
            file_path: Path to load the knowledge base from
            parser: Expression parser to parse string representations
        """
        with open(file_path, 'r') as f:
            data = json.load(f)
        
        self.name = data.get("name", "Default KB")
        
        # Clear existing knowledge
        self.facts = set()
        self.rules = set()
        self.probabilistic_facts = set()
        self.conditional_probabilities = set()
        self.fuzzy_facts = set()
        self.fact_index = {}
        self.rule_index = {}
        
        # Load facts
        for fact_str in data.get("facts", []):
            fact = parser.parse(fact_str)
            self.add_fact(fact)
        
        # Load rules
        for rule_str in data.get("rules", []):
            rule = parser.parse(rule_str)
            self.add_rule(rule)
        
        # Load probabilistic facts
        for pf_str in data.get("probabilistic_facts", []):
            parts = pf_str.split(",")
            if len(parts) == 2:
                expr = parser.parse(parts[0])
                prob = float(parts[1])
                self.add_probabilistic_fact(expr, prob)
        
        # Load conditional probabilities
        for cp_str in data.get("conditional_probabilities", []):
            parts = cp_str.split(",")
            if len(parts) == 3:
                expr = parser.parse(parts[0])
                condition = parser.parse(parts[1])
                prob = float(parts[2])
                self.add_conditional_probability(expr, condition, prob)
        
        # Load fuzzy facts
        for ff_str in data.get("fuzzy_facts", []):
            parts = ff_str.split(",")
            if len(parts) == 2:
                expr = parser.parse(parts[0])
                membership = float(parts[1])
                self.add_fuzzy_fact(expr, membership)
        
        # Update solver
        self.solver_needs_update = True
        self.update_solver()
    
    def __str__(self) -> str:
        """
        Get a string representation of the knowledge base.
        
        Returns:
            String representation
        """
        result = f"Knowledge Base: {self.name}\n"
        result += f"Facts ({len(self.facts)}):\n"
        for fact in self.facts:
            result += f"  {fact.to_string()}\n"
        result += f"Rules ({len(self.rules)}):\n"
        for rule in self.rules:
            result += f"  {rule.to_string()}\n"
        result += f"Probabilistic Facts ({len(self.probabilistic_facts)}):\n"
        for pf in self.probabilistic_facts:
            result += f"  {pf.to_string()}\n"
        result += f"Conditional Probabilities ({len(self.conditional_probabilities)}):\n"
        for cp in self.conditional_probabilities:
            result += f"  {cp.to_string()}\n"
        result += f"Fuzzy Facts ({len(self.fuzzy_facts)}):\n"
        for ff in self.fuzzy_facts:
            result += f"  {ff.to_string()}\n"
        return result


class ExpressionParser:
    """
    Parser for logical expressions from string representations.
    """
    
    def __init__(self):
        """
        Initialize the expression parser.
        """
        # Define operator precedence (higher number means higher precedence)
        self.precedence = {
            "EQUIVALENT": 1,
            "IMPLIES": 2,
            "OR": 3,
            "AND": 4,
            "NOT": 5
        }
    
    def parse(self, expr_str: str) -> Expression:
        """
        Parse a string expression into an Expression object.
        
        Args:
            expr_str: String representation of the expression
            
        Returns:
            Parsed Expression object
        """
        # Remove leading/trailing whitespace
        expr_str = expr_str.strip()
        
        # Handle constants
        if expr_str == "TRUE":
            return Constant(True)
        elif expr_str == "FALSE":
            return Constant(False)
        
        # Handle parenthesized expressions
        if expr_str.startswith("(") and expr_str.endswith(")"):
            # Check if the parentheses are balanced and necessary
            if self._is_balanced_parens(expr_str) and self._is_outer_parens(expr_str):
                # Parse the expression inside the parentheses
                return self.parse(expr_str[1:-1])
        
        # Handle negation
        if expr_str.startswith("NOT "):
            # Parse the rest of the expression
            return Negation(self.parse(expr_str[4:]))
        
        # Find the primary operator (outside of parentheses) with lowest precedence
        op_pos = self._find_primary_operator(expr_str)
        if op_pos:
            op, pos = op_pos
            if op == "AND":
                left = self.parse(expr_str[:pos])
                right = self.parse(expr_str[pos+4:])  # Skip "AND "
                return Conjunction(left, right)
            elif op == "OR":
                left = self.parse(expr_str[:pos])
                right = self.parse(expr_str[pos+3:])  # Skip "OR "
                return Disjunction(left, right)
            elif op == "IMPLIES":
                left = self.parse(expr_str[:pos])
                right = self.parse(expr_str[pos+8:])  # Skip "IMPLIES "
                return Implication(left, right)
            elif op == "EQUIVALENT":
                left = self.parse(expr_str[:pos])
                right = self.parse(expr_str[pos+11:])  # Skip "EQUIVALENT "
                return Equivalence(left, right)
        
        # If no operator found, it's an atom (variable)
        return Atom(expr_str)
    
    def _is_balanced_parens(self, expr_str: str) -> bool:
        """
        Check if parentheses in the expression are balanced.
        
        Args:
            expr_str: Expression string
            
        Returns:
            True if parentheses are balanced, False otherwise
        """
        count = 0
        for char in expr_str:
            if char == "(":
                count += 1
            elif char == ")":
                count -= 1
            if count < 0:
                return False
        return count == 0
    
    def _is_outer_parens(self, expr_str: str) -> bool:
        """
        Check if the outer parentheses in the expression are necessary.
        
        Args:
            expr_str: Expression string
            
        Returns:
            True if outer parentheses are necessary, False otherwise
        """
        if not (expr_str.startswith("(") and expr_str.endswith(")")):
            return False
            
        # Check if removing the outer parentheses would change the meaning
        count = 0
        for i, char in enumerate(expr_str):
            if char == "(":
                count += 1
            elif char == ")":
                count -= 1
            if count == 0 and i < len(expr_str) - 1:
                # Found a closing parenthesis matching the opening one, but not at the end
                # This means the outer parentheses are necessary
                return False
                
        return True
    
    def _find_primary_operator(self, expr_str: str) -> Optional[Tuple[str, int]]:
        """
        Find the primary operator in the expression.
        
        Args:
            expr_str: Expression string
            
        Returns:
            Tuple of (operator, position) or None if no operator found
        """
        # Search for operators outside of parentheses, with lowest precedence first
        paren_level = 0
        
        # Sort operators by precedence (ascending)
        sorted_operators = sorted(self.precedence.items(), key=lambda x: x[1])
        
        for op, _ in sorted_operators:
            i = 0
            while i < len(expr_str):
                if expr_str[i] == "(":
                    paren_level += 1
                elif expr_str[i] == ")":
                    paren_level -= 1
                elif paren_level == 0:
                    # Check if we found the operator
                    if expr_str[i:].startswith(op + " ") and (i == 0 or expr_str[i-1] == " "):
                        return (op, i)
                i += 1
                
        return None


class ClassicalReasoner:
    """
    Classical logic reasoner using Z3 solver.
    """
    
    def __init__(self, kb: Optional[KnowledgeBase] = None):
        """
        Initialize the classical reasoner.
        
        Args:
            kb: Knowledge base to use (optional)
        """
        self.kb = kb or KnowledgeBase()
        self.solver = None
        self.explanation_steps = []
    
    def set_knowledge_base(self, kb: KnowledgeBase) -> None:
        """
        Set the knowledge base for the reasoner.
        
        Args:
            kb: Knowledge base to use
        """
        self.kb = kb
        self.solver = None
    
    def _ensure_solver(self) -> None:
        """
        Ensure the solver is initialized and up to date.
        """
        if self.solver is None:
            self.kb.update_solver()
            self.solver = self.kb.solver
        elif self.kb.solver_needs_update:
            self.kb.update_solver()
            self.solver = self.kb.solver
    
    def is_entailed(self, conclusion: Expression) -> bool:
        """
        Check if a conclusion is entailed by the knowledge base.
        
        Args:
            conclusion: Conclusion to check
            
        Returns:
            True if the conclusion is entailed, False otherwise
        """
        # Ensure solver is up to date
        self._ensure_solver()
        
        # Create a new solver for this query
        query_solver = z3.Solver()
        
        # Add all assertions from the knowledge base
        for assertion in self.solver.assertions():
            query_solver.add(assertion)
        
        # Check if KB ⊢ conclusion
        # This is done by checking if KB ∧ ¬conclusion is unsatisfiable
        query_solver.add(z3.Not(conclusion.to_z3()))
        
        # Reset explanation steps
        self.explanation_steps = []
        
        result = query_solver.check()
        is_entailed = result == z3.unsat
        
        if is_entailed:
            # Generate explanation
            self._generate_explanation(conclusion)
        
        return is_entailed
    
    def _generate_explanation(self, conclusion: Expression) -> None:
        """
        Generate an explanation for an entailed conclusion.
        
        This is a simplified implementation. For a real system,
        more sophisticated techniques would be needed.
        
        Args:
            conclusion: Entailed conclusion
        """
        # First, check which facts and rules are relevant to the conclusion
        relevant_facts = set()
        relevant_rules = set()
        
        # Start with variables in the conclusion
        variables = conclusion.variables()
        
        # Find relevant facts and rules based on variables
        for var in variables:
            relevant_facts.update(self.kb.get_facts_with_variable(var))
            relevant_rules.update(self.kb.get_rules_with_variable(var))
        
        # For implication conclusions, check if there are rules that match
        if isinstance(conclusion, Implication):
            for rule in self.kb.rules:
                if isinstance(rule, Implication) and rule.conclusion.to_string() == conclusion.conclusion.to_string():
                    relevant_rules.add(rule)
        
        # Add explanation steps
        for fact in relevant_facts:
            self.explanation_steps.append(("fact", fact, None))
        
        for rule in relevant_rules:
            self.explanation_steps.append(("rule", rule, None))
        
        # Add final conclusion
        self.explanation_steps.append(("conclusion", conclusion, None))
    
    def get_explanation(self) -> List[Tuple[str, Expression, Optional[str]]]:
        """
        Get the explanation for the most recent entailment query.
        
        Returns:
            List of explanation steps as (type, expression, justification) tuples
        """
        return self.explanation_steps
    
    def find_model(self, expr: Expression = None) -> Optional[Dict[str, bool]]:
        """
        Find a model (interpretation) that satisfies the knowledge base and optional expression.
        
        Args:
            expr: Optional expression to include in the query
            
        Returns:
            Dictionary mapping variable names to boolean values, or None if unsatisfiable
        """
        # Ensure solver is up to date
        self._ensure_solver()
        
        # Create a new solver for this query
        query_solver = z3.Solver()
        
        # Add all assertions from the knowledge base
        for assertion in self.solver.assertions():
            query_solver.add(assertion)
        
        # Add the optional expression
        if expr is not None:
            query_solver.add(expr.to_z3())
        
        # Check satisfiability
        result = query_solver.check()
        
        if result == z3.sat:
            # Get model
            z3_model = query_solver.model()
            
            # Convert Z3 model to dictionary
            model = {}
            for decl in z3_model.decls():
                name = decl.name()
                value = z3_model[decl]
                if z3.is_bool(value):
                    model[name] = z3.is_true(value)
                    
            return model
        else:
            return None
    
    def find_all_models(self, expr: Expression = None, max_models: int = 10) -> List[Dict[str, bool]]:
        """
        Find all models (interpretations) that satisfy the knowledge base and optional expression.
        
        Args:
            expr: Optional expression to include in the query
            max_models: Maximum number of models to return
            
        Returns:
            List of dictionaries mapping variable names to boolean values
        """
        # Collect all variables
        variables = set()
        for fact in self.kb.facts:
            variables.update(fact.variables())
        for rule in self.kb.rules:
            variables.update(rule.variables())
        if expr is not None:
            variables.update(expr.variables())
        
        # If there are no variables, return empty list
        if not variables:
            return []
        
        # Ensure solver is up to date
        self._ensure_solver()
        
        # Create a new solver for this query
        query_solver = z3.Solver()
        
        # Add all assertions from the knowledge base
        for assertion in self.solver.assertions():
            query_solver.add(assertion)
        
        # Add the optional expression
        if expr is not None:
            query_solver.add(expr.to_z3())
        
        # Find models
        models = []
        
        while len(models) < max_models:
            result = query_solver.check()
            
            if result != z3.sat:
                break
                
            # Get model
            z3_model = query_solver.model()
            
            # Convert Z3 model to dictionary
            model = {}
            for var in variables:
                z3_var = z3.Bool(var)
                if z3_model.has_interp(z3_var):
                    model[var] = z3.is_true(z3_model[z3_var])
                else:
                    # Variable not constrained, can be either True or False
                    model[var] = False  # Arbitrary choice
                    
            models.append(model)
            
            # Add constraint to exclude this model
            exclusion_constraint = z3.Or([z3.Bool(var) != z3_model[z3.Bool(var)] for var in variables if z3_model.has_interp(z3.Bool(var))])
            query_solver.add(exclusion_constraint)
            
        return models


class ProbabilisticReasoner:
    """
    Probabilistic reasoner for probabilistic logic.
    """
    
    def __init__(self, kb: Optional[KnowledgeBase] = None):
        """
        Initialize the probabilistic reasoner.
        
        Args:
            kb: Knowledge base to use (optional)
        """
        self.kb = kb or KnowledgeBase()
        
        # Internal probability table
        # Maps expressions to P(expr)
        self.probabilities = {}
        
        # Internal conditional probability table
        # Maps (expr, condition) to P(expr | condition)
        self.conditionals = {}
        
        # Joint distribution (for exact inference)
        # Maps variable assignments to probabilities
        self.joint_distribution = {}
        
        # Bayesian network representation
        self.bayes_net = nx.DiGraph()
        
        # Flag to track if tables need updating
        self.tables_need_update = True
    
    def set_knowledge_base(self, kb: KnowledgeBase) -> None:
        """
        Set the knowledge base for the reasoner.
        
        Args:
            kb: Knowledge base to use
        """
        self.kb = kb
        self.tables_need_update = True
    
    def update_tables(self) -> None:
        """
        Update the probability tables with the current knowledge base.
        """
        if self.tables_need_update:
            # Clear existing tables
            self.probabilities = {}
            self.conditionals = {}
            
            # Extract probabilities from knowledge base
            for prob_fact in self.kb.probabilistic_facts:
                self.probabilities[prob_fact.expr.to_string()] = prob_fact.probability
                
            for cond_prob in self.kb.conditional_probabilities:
                key = (cond_prob.expr.to_string(), cond_prob.condition.to_string())
                self.conditionals[key] = cond_prob.probability
                
            # Update Bayesian network
            self._update_bayes_net()
                
            self.tables_need_update = False
    
    def _update_bayes_net(self) -> None:
        """
        Update the Bayesian network representation.
        """
        # Clear existing network
        self.bayes_net = nx.DiGraph()
        
        # Add nodes for all variables
        variables = set()
        for expr_str in self.probabilities:
            expr = self._parse_expr_str(expr_str)
            variables.update(expr.variables())
        for expr_str, cond_str in self.conditionals:
            expr = self._parse_expr_str(expr_str)
            cond = self._parse_expr_str(cond_str)
            variables.update(expr.variables())
            variables.update(cond.variables())
            
        for var in variables:
            self.bayes_net.add_node(var)
            
        # Add edges for conditional dependencies
        for (expr_str, cond_str), prob in self.conditionals.items():
            expr = self._parse_expr_str(expr_str)
            cond = self._parse_expr_str(cond_str)
            
            # Simplified: assume expr and cond are atoms
            if isinstance(expr, Atom) and isinstance(cond, Atom):
                self.bayes_net.add_edge(cond.name, expr.name, probability=prob)
    
    def _parse_expr_str(self, expr_str: str) -> Expression:
        """
        Parse an expression string.
        
        Args:
            expr_str: Expression string
            
        Returns:
            Parsed expression
        """
        # For simplicity, just create an atom
        # In a real system, this would involve proper parsing
        return Atom(expr_str)
    
    def probability(self, expr: Expression) -> float:
        """
        Compute the probability of an expression.
        
        Args:
            expr: Expression to compute probability for
            
        Returns:
            Probability value
        """
        # Update tables if needed
        self.update_tables()
        
        # Check if probability is directly available
        expr_str = expr.to_string()
        if expr_str in self.probabilities:
            return self.probabilities[expr_str]
            
        # For negation, use P(¬A) = 1 - P(A)
        if isinstance(expr, Negation):
            return 1.0 - self.probability(expr.expr)
            
        # For conjunction, use P(A ∧ B) = P(A) * P(B | A) if available
        if isinstance(expr, Conjunction):
            left_str = expr.left.to_string()
            right_str = expr.right.to_string()
            
            # Check if P(B | A) is available
            if (right_str, left_str) in self.conditionals:
                return self.probability(expr.left) * self.conditionals[(right_str, left_str)]
                
            # Check if P(A | B) is available
            if (left_str, right_str) in self.conditionals:
                return self.probability(expr.right) * self.conditionals[(left_str, right_str)]
                
            # Otherwise, assume independence (simplified)
            return self.probability(expr.left) * self.probability(expr.right)
            
        # For disjunction, use P(A ∨ B) = P(A) + P(B) - P(A ∧ B)
        if isinstance(expr, Disjunction):
            return (self.probability(expr.left) + self.probability(expr.right) -
                   self.probability(Conjunction(expr.left, expr.right)))
            
        # For implication, use P(A → B) = P(¬A ∨ B) = 1 - P(A) + P(A ∧ B)
        if isinstance(expr, Implication):
            return 1.0 - self.probability(expr.premise) + self.probability(Conjunction(expr.premise, expr.conclusion))
            
        # For equivalence, use P(A ↔ B) = P((A → B) ∧ (B → A)) = P(A ∧ B) + P(¬A ∧ ¬B)
        if isinstance(expr, Equivalence):
            return (self.probability(Conjunction(expr.left, expr.right)) +
                  self.probability(Conjunction(Negation(expr.left), Negation(expr.right))))
            
        # For complex expressions, use exact inference if possible
        # (simplified implementation)
        if len(expr.variables()) <= 10:  # Limit for exact inference
            return self._exact_inference(expr)
            
        # For larger expressions, use approximate inference
        return self._approximate_inference(expr)
    
    def conditional_probability(self, expr: Expression, condition: Expression) -> float:
        """
        Compute the conditional probability P(expr | condition).
        
        Args:
            expr: Target expression
            condition: Condition expression
            
        Returns:
            Conditional probability value
        """
        # Update tables if needed
        self.update_tables()
        
        # Check if conditional probability is directly available
        expr_str = expr.to_string()
        cond_str = condition.to_string()
        
        if (expr_str, cond_str) in self.conditionals:
            return self.conditionals[(expr_str, cond_str)]
            
        # Otherwise, use Bayes' rule: P(A|B) = P(A ∧ B) / P(B)
        conjunction = Conjunction(expr, condition)
        cond_prob = self.probability(condition)
        
        if cond_prob > 0:
            return self.probability(conjunction) / cond_prob
        else:
            return 0.0  # If P(B) = 0, P(A|B) is undefined (or 0 by convention)
    
    def _exact_inference(self, expr: Expression) -> float:
        """
        Perform exact inference for an expression.
        
        This computes the probability by enumerating all possible worlds.
        
        Args:
            expr: Expression to compute probability for
            
        Returns:
            Probability value
        """
        # Get all variables
        variables = list(expr.variables())
        
        # If no variables, expression is a constant
        if not variables:
            if isinstance(expr, Constant):
                return 1.0 if expr.value else 0.0
            return 0.0  # Default
            
        # Generate all possible worlds
        total_prob = 0.0
        
        # Enumerate all possible assignments (2^n possible worlds)
        for bits in range(2 ** len(variables)):
            # Create interpretation
            interpretation = {}
            for i, var in enumerate(variables):
                interpretation[var] = bool((bits >> i) & 1)
                
            # Evaluate expression in this world
            if expr.evaluate(interpretation):
                # Compute probability of this world
                world_prob = self._world_probability(interpretation)
                total_prob += world_prob
                
        return total_prob
    
    def _world_probability(self, interpretation: Dict[str, bool]) -> float:
        """
        Compute the probability of a possible world.
        
        Args:
            interpretation: Dictionary mapping variable names to boolean values
            
        Returns:
            Probability of the world
        """
        # Simplified implementation - assumes independence
        # In a real system, this would use the Bayesian network
        
        # For each variable, use its marginal probability
        prob = 1.0
        for var, value in interpretation.items():
            # Get marginal probability
            p_var = self.probabilities.get(var, 0.5)  # Default to 0.5 if unknown
            
            # Adjust probability based on value
            prob *= p_var if value else (1 - p_var)
            
        return prob
    
    def _approximate_inference(self, expr: Expression) -> float:
        """
        Perform approximate inference for an expression.
        
        This uses a simplified Monte Carlo approach.
        
        Args:
            expr: Expression to compute probability for
            
        Returns:
            Approximate probability value
        """
        # Get all variables
        variables = list(expr.variables())
        
        # If no variables, expression is a constant
        if not variables:
            if isinstance(expr, Constant):
                return 1.0 if expr.value else 0.0
            return 0.0  # Default
            
        # Generate random samples
        num_samples = 1000
        positive_samples = 0
        
        for _ in range(num_samples):
            # Generate a random interpretation
            interpretation = {}
            for var in variables:
                # Use marginal probability if available
                p_var = self.probabilities.get(var, 0.5)  # Default to 0.5 if unknown
                interpretation[var] = np.random.random() < p_var
                
            # Evaluate expression
            if expr.evaluate(interpretation):
                positive_samples += 1
                
        return positive_samples / num_samples
    
    def marginal_distribution(self, var: str) -> Dict[bool, float]:
        """
        Compute the marginal distribution for a variable.
        
        Args:
            var: Variable name
            
        Returns:
            Dictionary mapping boolean values to probabilities
        """
        # Update tables if needed
        self.update_tables()
        
        # Check if probability is directly available
        if var in self.probabilities:
            p_true = self.probabilities[var]
            return {True: p_true, False: 1.0 - p_true}
            
        # Otherwise, use the Bayesian network (if var exists in it)
        if var in self.bayes_net:
            # Find parent nodes
            parents = list(self.bayes_net.predecessors(var))
            
            if not parents:
                # No parents, use default
                return {True: 0.5, False: 0.5}
                
            # Simplified: use the first available conditional probability
            for parent in parents:
                parent_atom = Atom(parent)
                var_atom = Atom(var)
                
                key = (var, parent)
                if key in self.conditionals:
                    # Found conditional probability P(var | parent)
                    p_parent_true = self.marginal_distribution(parent)[True]
                    p_var_given_parent_true = self.conditionals[key]
                    
                    # Compute P(var) using law of total probability
                    p_var_true = (p_var_given_parent_true * p_parent_true +
                                (1 - self.conditionals.get((var, f"NOT {parent}"), 0.5)) * (1 - p_parent_true))
                    
                    return {True: p_var_true, False: 1.0 - p_var_true}
            
        # Default
        return {True: 0.5, False: 0.5}
    
    def explain_probability(self, expr: Expression) -> List[str]:
        """
        Generate an explanation for a probability computation.
        
        Args:
            expr: Expression whose probability was computed
            
        Returns:
            List of explanation steps
        """
        # Simplified implementation
        explanation = []
        
        expr_str = expr.to_string()
        
        # Check if probability is directly available
        if expr_str in self.probabilities:
            explanation.append(f"P({expr_str}) = {self.probabilities[expr_str]:.4f} (given)")
            return explanation
            
        # For negation
        if isinstance(expr, Negation):
            sub_expr = expr.expr.to_string()
            if sub_expr in self.probabilities:
                explanation.append(f"P({sub_expr}) = {self.probabilities[sub_expr]:.4f} (given)")
                explanation.append(f"P(NOT {sub_expr}) = 1 - P({sub_expr}) = {1 - self.probabilities[sub_expr]:.4f}")
                
        # For conjunction
        if isinstance(expr, Conjunction):
            left_str = expr.left.to_string()
            right_str = expr.right.to_string()
            
            if left_str in self.probabilities and right_str in self.probabilities:
                explanation.append(f"P({left_str}) = {self.probabilities[left_str]:.4f} (given)")
                explanation.append(f"P({right_str}) = {self.probabilities[right_str]:.4f} (given)")
                
                # Check if conditional probability is available
                if (right_str, left_str) in self.conditionals:
                    cond_prob = self.conditionals[(right_str, left_str)]
                    explanation.append(f"P({right_str} | {left_str}) = {cond_prob:.4f} (given)")
                    explanation.append(f"P({left_str} AND {right_str}) = P({left_str}) * P({right_str} | {left_str}) = {self.probabilities[left_str] * cond_prob:.4f}")
                elif (left_str, right_str) in self.conditionals:
                    cond_prob = self.conditionals[(left_str, right_str)]
                    explanation.append(f"P({left_str} | {right_str}) = {cond_prob:.4f} (given)")
                    explanation.append(f"P({left_str} AND {right_str}) = P({right_str}) * P({left_str} | {right_str}) = {self.probabilities[right_str] * cond_prob:.4f}")
                else:
                    explanation.append(f"Assuming independence: P({left_str} AND {right_str}) = P({left_str}) * P({right_str}) = {self.probabilities[left_str] * self.probabilities[right_str]:.4f}")
                    
        return explanation


class FuzzyReasoner:
    """
    Fuzzy reasoner for fuzzy logic.
    """
    
    def __init__(self, kb: Optional[KnowledgeBase] = None):
        """
        Initialize the fuzzy reasoner.
        
        Args:
            kb: Knowledge base to use (optional)
        """
        self.kb = kb or KnowledgeBase()
        
        # Membership functions
        self.membership_funcs = {}
        
        # Fuzzy rules (antecedent, consequent, weight)
        self.fuzzy_rules = []
        
        # Membership values for variables
        self.memberships = {}
        
        # Flag to track if tables need updating
        self.tables_need_update = True
    
    def set_knowledge_base(self, kb: KnowledgeBase) -> None:
        """
        Set the knowledge base for the reasoner.
        
        Args:
            kb: Knowledge base to use
        """
        self.kb = kb
        self.tables_need_update = True
    
    def update_tables(self) -> None:
        """
        Update the fuzzy tables with the current knowledge base.
        """
        if self.tables_need_update:
            # Clear existing tables
            self.memberships = {}
            
            # Extract fuzzy facts from knowledge base
            for fuzzy_fact in self.kb.fuzzy_facts:
                expr_str = fuzzy_fact.expr.to_string()
                self.memberships[expr_str] = fuzzy_fact.membership
                
            self.tables_need_update = False
    
    def add_membership_function(self, var: str, func: Callable[[float], float]) -> None:
        """
        Add a membership function for a fuzzy variable.
        
        Args:
            var: Variable name
            func: Function that maps crisp values to membership degrees [0,1]
        """
        self.membership_funcs[var] = func
    
    def add_fuzzy_rule(self, antecedent: List[Tuple[str, float]], consequent: Tuple[str, float], weight: float = 1.0) -> None:
        """
        Add a fuzzy rule of the form IF x1 is A1 AND x2 is A2... THEN y is B.
        
        Args:
            antecedent: List of (variable, membership_value) pairs
            consequent: (variable, membership_value) pair
            weight: Rule weight in [0,1]
        """
        self.fuzzy_rules.append((antecedent, consequent, weight))
    
    def membership(self, expr: Expression) -> float:
        """
        Compute the membership degree of an expression.
        
        Args:
            expr: Expression to compute membership for
            
        Returns:
            Membership value in [0,1]
        """
        # Update tables if needed
        self.update_tables()
        
        # Check if membership is directly available
        expr_str = expr.to_string()
        if expr_str in self.memberships:
            return self.memberships[expr_str]
            
        # For atoms, default to 0.5 if not known
        if isinstance(expr, Atom):
            return self.memberships.get(expr.name, 0.5)
            
        # For constants, use boolean value
        if isinstance(expr, Constant):
            return 1.0 if expr.value else 0.0
            
        # For negation, use μ(¬A) = 1 - μ(A)
        if isinstance(expr, Negation):
            return 1.0 - self.membership(expr.expr)
            
        # For conjunction, use μ(A ∧ B) = min(μ(A), μ(B))
        if isinstance(expr, Conjunction):
            return min(self.membership(expr.left), self.membership(expr.right))
            
        # For disjunction, use μ(A ∨ B) = max(μ(A), μ(B))
        if isinstance(expr, Disjunction):
            return max(self.membership(expr.left), self.membership(expr.right))
            
        # For implication, use μ(A → B) = max(1 - μ(A), μ(B))
        if isinstance(expr, Implication):
            return max(1.0 - self.membership(expr.premise), self.membership(expr.conclusion))
            
        # For equivalence, use μ(A ↔ B) = 1 - |μ(A) - μ(B)|
        if isinstance(expr, Equivalence):
            return 1.0 - abs(self.membership(expr.left) - self.membership(expr.right))
            
        # Default
        return 0.5
    
    def evaluate_rules(self, inputs: Dict[str, float]) -> Dict[str, float]:
        """
        Evaluate fuzzy rules with given crisp inputs and return fuzzy outputs.
        
        Args:
            inputs: Dictionary mapping input variables to crisp values
            
        Returns:
            Dictionary mapping output variables to fuzzy membership values
        """
        # Fuzzify inputs
        fuzzified = {}
        for var, value in inputs.items():
            if var in self.membership_funcs:
                fuzzified[var] = self.membership_funcs[var](value)
            else:
                fuzzified[var] = value  # Assume already fuzzified
        
        # Evaluate rules
        results = defaultdict(float)
        
        for antecedent, consequent, weight in self.fuzzy_rules:
            # Calculate rule firing strength using min for AND
            strengths = []
            for var, target_membership in antecedent:
                if var in fuzzified:
                    # Calculate how close the fuzzified value is to the target
                    membership = 1.0 - abs(fuzzified[var] - target_membership)
                    strengths.append(max(0.0, membership))
                else:
                    strengths.append(0.0)
            
            if not strengths:
                continue
                
            # Rule firing strength is the minimum of all antecedent strengths
            firing_strength = min(strengths) * weight
            
            # Apply to consequent
            cons_var, cons_value = consequent
            # Take max if multiple rules affect the same output
            results[cons_var] = max(results[cons_var], firing_strength * cons_value)
        
        return dict(results)
    
    def defuzzify(self, var: str, fuzzy_value: float, method: str = "centroid") -> float:
        """
        Defuzzify a fuzzy value to a crisp value.
        
        Args:
            var: Variable name
            fuzzy_value: Fuzzy membership value
            method: Defuzzification method ("centroid", "mean_of_max", "first_of_max")
            
        Returns:
            Crisp value
        """
        # This is a simplified implementation
        # In a real system, defuzzification would be more sophisticated
        
        if method == "centroid":
            # This simplified implementation doesn't actually do centroid defuzzification
            # It just scales fuzzy_value to a domain
            return fuzzy_value * 10.0  # Arbitrary scaling
        elif method == "mean_of_max" or method == "first_of_max":
            # For these methods, we would find where the membership function is maximized
            # Since we don't have actual membership functions here, just return the value
            return fuzzy_value * 10.0  # Arbitrary scaling
        else:
            raise ValueError(f"Unknown defuzzification method: {method}")
    
    def explain_membership(self, expr: Expression) -> List[str]:
        """
        Generate an explanation for a membership computation.
        
        Args:
            expr: Expression whose membership was computed
            
        Returns:
            List of explanation steps
        """
        # Simplified implementation
        explanation = []
        
        expr_str = expr.to_string()
        
        # Check if membership is directly available
        if expr_str in self.memberships:
            explanation.append(f"μ({expr_str}) = {self.memberships[expr_str]:.4f} (given)")
            return explanation
            
        # For negation
        if isinstance(expr, Negation):
            sub_expr = expr.expr.to_string()
            if sub_expr in self.memberships:
                explanation.append(f"μ({sub_expr}) = {self.memberships[sub_expr]:.4f} (given)")
                explanation.append(f"μ(NOT {sub_expr}) = 1 - μ({sub_expr}) = {1 - self.memberships[sub_expr]:.4f}")
                
        # For conjunction
        if isinstance(expr, Conjunction):
            left_str = expr.left.to_string()
            right_str = expr.right.to_string()
            
            if left_str in self.memberships and right_str in self.memberships:
                explanation.append(f"μ({left_str}) = {self.memberships[left_str]:.4f} (given)")
                explanation.append(f"μ({right_str}) = {self.memberships[right_str]:.4f} (given)")
                explanation.append(f"μ({left_str} AND {right_str}) = min(μ({left_str}), μ({right_str})) = {min(self.memberships[left_str], self.memberships[right_str]):.4f}")
                
        # For disjunction
        if isinstance(expr, Disjunction):
            left_str = expr.left.to_string()
            right_str = expr.right.to_string()
            
            if left_str in self.memberships and right_str in self.memberships:
                explanation.append(f"μ({left_str}) = {self.memberships[left_str]:.4f} (given)")
                explanation.append(f"μ({right_str}) = {self.memberships[right_str]:.4f} (given)")
                explanation.append(f"μ({left_str} OR {right_str}) = max(μ({left_str}), μ({right_str})) = {max(self.memberships[left_str], self.memberships[right_str]):.4f}")
                
        return explanation


class ModalReasoner:
    """
    Modal reasoner for modal logic.
    
    Implements reasoning in basic modal logics like K, T, S4, S5.
    """
    
    def __init__(self, kb: Optional[KnowledgeBase] = None, logic_type: str = "K"):
        """
        Initialize the modal reasoner.
        
        Args:
            kb: Knowledge base to use (optional)
            logic_type: Type of modal logic to use ("K", "T", "S4", "S5")
        """
        self.kb = kb or KnowledgeBase()
        self.logic_type = logic_type
        
        # Kripke model: (worlds, accessibility relation, valuation)
        self.worlds = set()
        self.accessibility = set()  # set of (w1, w2) tuples
        self.valuation = {}  # maps (world, atom) to boolean value
        
        # Flag to track if Kripke model needs updating
        self.model_needs_update = True
    
    def set_knowledge_base(self, kb: KnowledgeBase) -> None:
        """
        Set the knowledge base for the reasoner.
        
        Args:
            kb: Knowledge base to use
        """
        self.kb = kb
        self.model_needs_update = True
    
    def set_logic_type(self, logic_type: str) -> None:
        """
        Set the type of modal logic to use.
        
        Args:
            logic_type: Type of modal logic ("K", "T", "S4", "S5")
        """
        self.logic_type = logic_type
        self.model_needs_update = True
    
    def update_model(self) -> None:
        """
        Update the Kripke model with the current knowledge base.
        """
        # Simplified implementation
        # In a real system, this would involve complex reasoning
        
        # Extract atoms from knowledge base
        atoms = set()
        for fact in self.kb.facts:
            atoms.update(fact.variables())
        for rule in self.kb.rules:
            atoms.update(rule.variables())
            
        # Create possible worlds
        self.worlds = {0}  # Start with one world
        self.accessibility = {(0, 0)}  # Reflexive accessibility for world 0
        
        # Initialize valuation
        self.valuation = {}
        
        # Assign values to atoms in the initial world
        for atom in atoms:
            self.valuation[(0, atom)] = False  # Default value
            
        # Apply logic-specific constraints on accessibility relation
        if self.logic_type == "T":
            # Make accessibility reflexive
            for w in self.worlds:
                self.accessibility.add((w, w))
        elif self.logic_type == "S4":
            # Make accessibility reflexive and transitive
            # Reflexive
            for w in self.worlds:
                self.accessibility.add((w, w))
            # Transitive
            transitive_closure = set(self.accessibility)
            changed = True
            while changed:
                changed = False
                for w1, w2 in list(transitive_closure):
                    for w3 in self.worlds:
                        if (w2, w3) in transitive_closure and (w1, w3) not in transitive_closure:
                            transitive_closure.add((w1, w3))
                            changed = True
            self.accessibility = transitive_closure
        elif self.logic_type == "S5":
            # Make accessibility an equivalence relation (reflexive, symmetric, transitive)
            # Reflexive
            for w in self.worlds:
                self.accessibility.add((w, w))
            # Symmetric
            symmetric_closure = set(self.accessibility)
            for w1, w2 in list(symmetric_closure):
                symmetric_closure.add((w2, w1))
            # Transitive
            transitive_closure = set(symmetric_closure)
            changed = True
            while changed:
                changed = False
                for w1, w2 in list(transitive_closure):
                    for w3 in self.worlds:
                        if (w2, w3) in transitive_closure and (w1, w3) not in transitive_closure:
                            transitive_closure.add((w1, w3))
                            changed = True
            self.accessibility = transitive_closure
            
        self.model_needs_update = False
    
    def is_satisfiable(self, expr: Expression, world: int = 0) -> bool:
        """
        Check if an expression is satisfiable in a given world.
        
        Args:
            expr: Expression to check
            world: World to check in
            
        Returns:
            True if the expression is satisfiable, False otherwise
        """
        # Update model if needed
        if self.model_needs_update:
            self.update_model()
            
        # Find a valuation that makes the expression true in the given world
        
        # Extract atoms from expression
        atoms = expr.variables()
        
        # Try all possible valuations
        for bits in range(2 ** len(atoms)):
            # Create valuation
            valuation = dict(self.valuation)  # Start with existing valuation
            for i, atom in enumerate(atoms):
                valuation[(world, atom)] = bool((bits >> i) & 1)
                
            # Check if expression is true in this valuation
            if self._evaluate_modal(expr, world, valuation):
                return True
                
        return False
    
    def is_valid(self, expr: Expression) -> bool:
        """
        Check if an expression is valid (true in all worlds).
        
        Args:
            expr: Expression to check
            
        Returns:
            True if the expression is valid, False otherwise
        """
        # Update model if needed
        if self.model_needs_update:
            self.update_model()
            
        # Check if expression is true in all worlds
        for world in self.worlds:
            if not self.is_satisfiable(Negation(expr), world):
                continue  # Expression is true in this world
            else:
                return False  # Found a world where expression is false
                
        return True
    
    def _evaluate_modal(self, expr: Expression, world: int, valuation: Dict[Tuple[int, str], bool]) -> bool:
        """
        Evaluate a modal expression in a given world and valuation.
        
        Args:
            expr: Expression to evaluate
            world: World to evaluate in
            valuation: Valuation function
            
        Returns:
            Truth value of the expression
        """
        # Atoms
        if isinstance(expr, Atom):
            return valuation.get((world, expr.name), False)
            
        # Constants
        if isinstance(expr, Constant):
            return expr.value
            
        # Negation
        if isinstance(expr, Negation):
            return not self._evaluate_modal(expr.expr, world, valuation)
            
        # Conjunction
        if isinstance(expr, Conjunction):
            return (self._evaluate_modal(expr.left, world, valuation) and
                  self._evaluate_modal(expr.right, world, valuation))
                  
        # Disjunction
        if isinstance(expr, Disjunction):
            return (self._evaluate_modal(expr.left, world, valuation) or
                  self._evaluate_modal(expr.right, world, valuation))
                  
        # Implication
        if isinstance(expr, Implication):
            return (not self._evaluate_modal(expr.premise, world, valuation) or
                  self._evaluate_modal(expr.conclusion, world, valuation))
                  
        # Equivalence
        if isinstance(expr, Equivalence):
            return (self._evaluate_modal(expr.left, world, valuation) ==
                  self._evaluate_modal(expr.right, world, valuation))
                  
        # Modal operators (not implemented in the Expression classes, this is a placeholder)
        # For "Necessarily P", check if P is true in all accessible worlds
        # For "Possibly P", check if P is true in at least one accessible world
        
        # Default
        return False


class TemporalReasoner:
    """
    Temporal reasoner for temporal logic.
    
    Implements reasoning in linear temporal logic (LTL).
    """
    
    def __init__(self, kb: Optional[KnowledgeBase] = None):
        """
        Initialize the temporal reasoner.
        
        Args:
            kb: Knowledge base to use (optional)
        """
        self.kb = kb or KnowledgeBase()
        
        # Temporal model: sequence of states
        self.states = []  # List of dictionaries mapping atoms to boolean values
        
        # Flag to track if temporal model needs updating
        self.model_needs_update = True
    
    def set_knowledge_base(self, kb: KnowledgeBase) -> None:
        """
        Set the knowledge base for the reasoner.
        
        Args:
            kb: Knowledge base to use
        """
        self.kb = kb
        self.model_needs_update = True
    
    def update_model(self) -> None:
        """
        Update the temporal model with the current knowledge base.
        """
        # Simplified implementation
        # In a real system, this would involve complex reasoning
        
        # Extract atoms from knowledge base
        atoms = set()
        for fact in self.kb.facts:
            atoms.update(fact.variables())
        for rule in self.kb.rules:
            atoms.update(rule.variables())
            
        # Create a simple linear model with three states
        self.states = [{atom: False for atom in atoms} for _ in range(3)]
        
        self.model_needs_update = False
    
    def is_satisfiable(self, expr: Expression, state: int = 0) -> bool:
        """
        Check if a temporal expression is satisfiable starting from a given state.
        
        Args:
            expr: Expression to check
            state: Starting state
            
        Returns:
            True if the expression is satisfiable, False otherwise
        """
        # Update model if needed
        if self.model_needs_update:
            self.update_model()
            
        # Extract atoms from expression
        atoms = expr.variables()
        
        # Try all possible valuations for the states
        for bits in range(2 ** (len(atoms) * len(self.states))):
            # Create valuation
            states_copy = []
            for i in range(len(self.states)):
                state_copy = dict(self.states[i])
                for j, atom in enumerate(atoms):
                    bit_pos = i * len(atoms) + j
                    state_copy[atom] = bool((bits >> bit_pos) & 1)
                states_copy.append(state_copy)
                
            # Check if expression is true in this valuation
            if self._evaluate_temporal(expr, state, states_copy):
                return True
                
        return False
    
    def is_valid(self, expr: Expression) -> bool:
        """
        Check if a temporal expression is valid (true in all paths).
        
        Args:
            expr: Expression to check
            
        Returns:
            True if the expression is valid, False otherwise
        """
        # Update model if needed
        if self.model_needs_update:
            self.update_model()
            
        # Expression is valid if its negation is not satisfiable
        return not self.is_satisfiable(Negation(expr))
    
    def _evaluate_temporal(self, expr: Expression, state: int, states: List[Dict[str, bool]]) -> bool:
        """
        Evaluate a temporal expression starting from a given state.
        
        Args:
            expr: Expression to evaluate
            state: Starting state
            states: List of states (valuations)
            
        Returns:
            Truth value of the expression
        """
        # Check if state is valid
        if state < 0 or state >= len(states):
            return False
            
        # Atoms
        if isinstance(expr, Atom):
            return states[state].get(expr.name, False)
            
        # Constants
        if isinstance(expr, Constant):
            return expr.value
            
        # Negation
        if isinstance(expr, Negation):
            return not self._evaluate_temporal(expr.expr, state, states)
            
        # Conjunction
        if isinstance(expr, Conjunction):
            return (self._evaluate_temporal(expr.left, state, states) and
                  self._evaluate_temporal(expr.right, state, states))
                  
        # Disjunction
        if isinstance(expr, Disjunction):
            return (self._evaluate_temporal(expr.left, state, states) or
                  self._evaluate_temporal(expr.right, state, states))
                  
        # Implication
        if isinstance(expr, Implication):
            return (not self._evaluate_temporal(expr.premise, state, states) or
                  self._evaluate_temporal(expr.conclusion, state, states))
                  
        # Equivalence
        if isinstance(expr, Equivalence):
            return (self._evaluate_temporal(expr.left, state, states) ==
                  self._evaluate_temporal(expr.right, state, states))
                  
        # Temporal operators (not implemented in the Expression classes, this is a placeholder)
        # For "Next P", check if P is true in the next state
        # For "Always P", check if P is true in all future states
        # For "Eventually P", check if P is true in some future state
        # For "Until", etc.
        
        # Default
        return False


class LogicalReasoningEngine:
    """
    Main engine for logical reasoning, integrating different reasoning systems.
    """
    
    def __init__(self, name: str = "LogicalReasoningEngine", config: Dict = None):
        """
        Initialize the Logical Reasoning Engine.
        
        Args:
            name: Name of the engine
            config: Configuration dictionary
        """
        self.name = name
        self.config = config or {}
        
        # Default configuration
        if self.config is None:
            self.config = {}
            
        # Reasoning types enabled
        self.reasoning_types = self.config.get('reasoning_types', 
                                             [ReasoningType.DEDUCTIVE, 
                                              ReasoningType.INDUCTIVE, 
                                              ReasoningType.ABDUCTIVE])
        
        # Logic types enabled
        self.logic_types = self.config.get('logic_types', 
                                         [LogicType.CLASSICAL, 
                                          LogicType.PROBABILISTIC, 
                                          LogicType.FUZZY])
        
        # Knowledge base
        self.kb = KnowledgeBase(name=f"{name} KB")
        
        # Specialized reasoners
        self.classical_reasoner = ClassicalReasoner(self.kb)
        self.probabilistic_reasoner = ProbabilisticReasoner(self.kb)
        self.fuzzy_reasoner = FuzzyReasoner(self.kb)
        self.modal_reasoner = ModalReasoner(self.kb)
        self.temporal_reasoner = TemporalReasoner(self.kb)
        
        # Expression parser
        self.parser = ExpressionParser()
        
        # Explanation history
        self.explanation_history = []
        
        # Initialize
        self._is_initialized = False
    
    def initialize(self) -> None:
        """Initialize the reasoning engine with necessary resources."""
        try:
            # Load any predefined knowledge (if specified in config)
            kb_path = self.config.get('kb_path', None)
            if kb_path and os.path.exists(kb_path):
                self.load_knowledge_base(kb_path)
                
            self._is_initialized = True
            logger.info(f"Initialized {self.name}")
        except Exception as e:
            logger.error(f"Failed to initialize {self.name}: {str(e)}")
            raise
    
    def reset(self) -> None:
        """Reset the reasoning engine to its initial state."""
        self.kb = KnowledgeBase(name=f"{self.name} KB")
        self.classical_reasoner.set_knowledge_base(self.kb)
        self.probabilistic_reasoner.set_knowledge_base(self.kb)
        self.fuzzy_reasoner.set_knowledge_base(self.kb)
        self.modal_reasoner.set_knowledge_base(self.kb)
        self.temporal_reasoner.set_knowledge_base(self.kb)
        self.explanation_history = []
        logger.info(f"Reset {self.name}")
    
    def add_fact(self, fact_str: str) -> None:
        """
        Add a fact to the knowledge base.
        
        Args:
            fact_str: String representation of the fact
        """
        fact = self.parser.parse(fact_str)
        self.kb.add_fact(fact)
    
    def add_rule(self, rule_str: str) -> None:
        """
        Add a rule to the knowledge base.
        
        Args:
            rule_str: String representation of the rule
        """
        rule = self.parser.parse(rule_str)
        self.kb.add_rule(rule)
    
    def add_probabilistic_fact(self, expr_str: str, probability: float) -> None:
        """
        Add a probabilistic fact to the knowledge base.
        
        Args:
            expr_str: String representation of the expression
            probability: Probability value in [0, 1]
        """
        expr = self.parser.parse(expr_str)
        self.kb.add_probabilistic_fact(expr, probability)
    
    def add_conditional_probability(self, expr_str: str, condition_str: str, probability: float) -> None:
        """
        Add a conditional probability to the knowledge base.
        
        Args:
            expr_str: String representation of the target expression
            condition_str: String representation of the condition expression
            probability: Conditional probability value in [0, 1]
        """
        expr = self.parser.parse(expr_str)
        condition = self.parser.parse(condition_str)
        self.kb.add_conditional_probability(expr, condition, probability)
    
    def add_fuzzy_fact(self, expr_str: str, membership: float) -> None:
        """
        Add a fuzzy fact to the knowledge base.
        
        Args:
            expr_str: String representation of the expression
            membership: Membership value in [0, 1]
        """
        expr = self.parser.parse(expr_str)
        self.kb.add_fuzzy_fact(expr, membership)
    
    def deductive_reasoning(self, premises: List[str], conclusion: Optional[str] = None) -> Tuple[bool, List[str]]:
        """
        Perform deductive reasoning of the form A ∧ (A → B) ⊢ B.
        
        Args:
            premises: List of premise strings
            conclusion: Optional conclusion string to check
            
        Returns:
            Tuple of (is_valid, derived_conclusions)
        """
        if ReasoningType.DEDUCTIVE not in self.reasoning_types:
            raise ValueError("Deductive reasoning not enabled in this engine")
            
        # Parse premises and conclusion
        parsed_premises = [self.parser.parse(premise) for premise in premises]
        parsed_conclusion = self.parser.parse(conclusion) if conclusion else None
        
        # Use classical reasoner
        if parsed_conclusion:
            is_valid = self.classical_reasoner.is_entailed(parsed_conclusion)
            
            # Record to explanation history
            if is_valid:
                explanation = self.classical_reasoner.get_explanation()
                self.explanation_history.append({
                    'type': 'deductive',
                    'premises': premises,
                    'conclusion': conclusion,
                    'result': is_valid,
                    'explanation': explanation
                })
                
            return is_valid, [conclusion] if is_valid else []
        else:
            # Derive conclusions
            all_conclusions = []
            
            # Check each fact and rule in KB as potential conclusion
            for fact in self.kb.facts:
                for premise in parsed_premises:
                    if self.classical_reasoner.is_entailed(fact):
                        fact_str = fact.to_string()
                        if fact_str not in all_conclusions:
                            all_conclusions.append(fact_str)
                            
                            # Record to explanation history
                            explanation = self.classical_reasoner.get_explanation()
                            self.explanation_history.append({
                                'type': 'deductive',
                                'premises': premises,
                                'conclusion': fact_str,
                                'result': True,
                                'explanation': explanation
                            })
            
            for rule in self.kb.rules:
                for premise in parsed_premises:
                    if self.classical_reasoner.is_entailed(rule):
                        rule_str = rule.to_string()
                        if rule_str not in all_conclusions:
                            all_conclusions.append(rule_str)
                            
                            # Record to explanation history
                            explanation = self.classical_reasoner.get_explanation()
                            self.explanation_history.append({
                                'type': 'deductive',
                                'premises': premises,
                                'conclusion': rule_str,
                                'result': True,
                                'explanation': explanation
                            })
            
            return len(all_conclusions) > 0, all_conclusions
    
    def inductive_reasoning(self, instances: List[Tuple[str, str]], generalization: Optional[str] = None) -> Tuple[float, Optional[str]]:
        """
        Perform inductive reasoning of the form {A₁→B, A₂→B, ..., Aₙ→B} ⇝ (∀x)(A(x)→B).
        
        Args:
            instances: List of (condition, outcome) string pairs
            generalization: Optional generalization string to check
            
        Returns:
            Tuple of (confidence, generalization)
        """
        if ReasoningType.INDUCTIVE not in self.reasoning_types:
            raise ValueError("Inductive reasoning not enabled in this engine")
            
        # Parse instances
        parsed_instances = [(self.parser.parse(cond), self.parser.parse(outcome)) 
                          for cond, outcome in instances]
        
        # Extract conditions and outcomes
        conditions = [cond for cond, _ in parsed_instances]
        outcomes = [outcome for _, outcome in parsed_instances]
        
        # Check if all outcomes are the same
        first_outcome_str = outcomes[0].to_string() if outcomes else None
        if not all(outcome.to_string() == first_outcome_str for outcome in outcomes):
            return 0.0, None  # No consistent outcome
            
        consistent_outcome = outcomes[0]
        
        # If generalization is provided, check if it matches our pattern
        if generalization:
            parsed_generalization = self.parser.parse(generalization)
            
            # Check if generalization is an implication
            if isinstance(parsed_generalization, Implication):
                # Check if the consequent matches the consistent outcome
                if parsed_generalization.conclusion.to_string() == consistent_outcome.to_string():
                    # Calculate confidence based on number of instances
                    confidence = min(1.0, len(instances) / 10.0)  # Simple heuristic
                    
                    # Record to explanation history
                    self.explanation_history.append({
                        'type': 'inductive',
                        'instances': instances,
                        'generalization': generalization,
                        'confidence': confidence
                    })
                    
                    return confidence, generalization
                else:
                    return 0.0, None
            else:
                return 0.0, None
        else:
            # Attempt to find common pattern in conditions
            if conditions:
                # For simplicity, just create an implication with the common outcome
                if len(conditions) == 1:
                    # Single instance - just use the condition as is
                    generalization_expr = Implication(conditions[0], consistent_outcome)
                else:
                    # Multiple instances - create a disjunction of conditions
                    disj = conditions[0]
                    for cond in conditions[1:]:
                        disj = Disjunction(disj, cond)
                    generalization_expr = Implication(disj, consistent_outcome)
                
                generalization = generalization_expr.to_string()
                
                # Calculate confidence based on number of instances
                confidence = min(1.0, len(instances) / 10.0)  # Simple heuristic
                
                # Record to explanation history
                self.explanation_history.append({
                    'type': 'inductive',
                    'instances': instances,
                    'generalization': generalization,
                    'confidence': confidence
                })
                
                return confidence, generalization
            else:
                return 0.0, None
    
    def abductive_reasoning(self, observation: str, hypotheses: Optional[List[str]] = None) -> List[Tuple[str, float]]:
        """
        Perform abductive reasoning of the form B ∧ (A → B) ⇝ A.
        
        Args:
            observation: The observed fact B
            hypotheses: Optional list of potential hypotheses to consider
            
        Returns:
            List of (hypothesis, plausibility) pairs
        """
        if ReasoningType.ABDUCTIVE not in self.reasoning_types:
            raise ValueError("Abductive reasoning not enabled in this engine")
            
        # Parse observation
        parsed_observation = self.parser.parse(observation)
        
        # Parse hypotheses if provided
        parsed_hypotheses = []
        if hypotheses:
            parsed_hypotheses = [self.parser.parse(h) for h in hypotheses]
        else:
            # If no hypotheses provided, consider all facts and negations as potential causes
            for fact in self.kb.facts:
                parsed_hypotheses.append(fact)
                parsed_hypotheses.append(Negation(fact))
        
        # Find explanations
        explanations = []
        
        for hypothesis in parsed_hypotheses:
            # Check if hypothesis → observation is consistent with knowledge base
            implication = Implication(hypothesis, parsed_observation)
            
            # Check consistency using the classical reasoner
            model = self.classical_reasoner.find_model(implication)
            if model is not None:
                # Hypothesis is consistent with KB
                
                # Calculate plausibility (simplified)
                # More plausible if hypothesis is simple and already in KB
                plausibility = 0.5  # Base plausibility
                
                if hypothesis in self.kb.facts:
                    plausibility += 0.3  # Boost if already a known fact
                    
                # Simplicity heuristic (fewer operators = simpler)
                depth = self._expression_depth(hypothesis)
                plausibility += max(0.0, 0.2 - 0.05 * depth)  # Penalize complexity
                
                hypothesis_str = hypothesis.to_string()
                explanations.append((hypothesis_str, min(1.0, plausibility)))
                
                # Record to explanation history
                self.explanation_history.append({
                    'type': 'abductive',
                    'observation': observation,
                    'hypothesis': hypothesis_str,
                    'plausibility': plausibility
                })
        
        # Sort by plausibility
        explanations.sort(key=lambda x: x[1], reverse=True)
        return explanations
    
    def _expression_depth(self, expr: Expression) -> int:
        """
        Calculate the depth of a logical expression.
        
        Args:
            expr: Logical expression
            
        Returns:
            Depth of the expression tree
        """
        if isinstance(expr, (Atom, Constant)):
            return 0
        elif isinstance(expr, Negation):
            return 1 + self._expression_depth(expr.expr)
        elif isinstance(expr, (Conjunction, Disjunction, Implication, Equivalence)):
            return 1 + max(self._expression_depth(expr.left), self._expression_depth(expr.right))
        return 0
    
    def analogical_reasoning(self, source_premises: List[str], source_conclusion: str, 
                          target_premises: List[str]) -> Tuple[str, float]:
        """
        Perform analogical reasoning by mapping from a source problem to a target problem.
        
        Args:
            source_premises: List of premise strings for the source problem
            source_conclusion: Conclusion string for the source problem
            target_premises: List of premise strings for the target problem
            
        Returns:
            Tuple of (target_conclusion, confidence)
        """
        if ReasoningType.ANALOGICAL not in self.reasoning_types:
            raise ValueError("Analogical reasoning not enabled in this engine")
            
        # Parse source and target
        parsed_source_premises = [self.parser.parse(p) for p in source_premises]
        parsed_source_conclusion = self.parser.parse(source_conclusion)
        parsed_target_premises = [self.parser.parse(p) for p in target_premises]
        
        # Simple implementation of structure mapping:
        # 1. Find mapping from source premises to target premises
        # 2. Apply same mapping to source conclusion to get target conclusion
        
        # First, check if we have the same number of premises
        if len(source_premises) != len(target_premises):
            return "Cannot perform analogical reasoning with different numbers of premises", 0.0
            
        # Build mapping from source to target
        mapping = {}
        for s_premise, t_premise in zip(parsed_source_premises, parsed_target_premises):
            self._build_mapping(s_premise, t_premise, mapping)
            
        # Apply mapping to source conclusion to get target conclusion
        target_conclusion_expr = self._apply_mapping(parsed_source_conclusion, mapping)
        target_conclusion = target_conclusion_expr.to_string()
        
        # Calculate confidence
        # Higher if mapping is consistent and complete
        confidence = min(1.0, len(mapping) / 10.0)  # Simple heuristic
        
        # Record to explanation history
        self.explanation_history.append({
            'type': 'analogical',
            'source_premises': source_premises,
            'source_conclusion': source_conclusion,
            'target_premises': target_premises,
            'target_conclusion': target_conclusion,
            'confidence': confidence,
            'mapping': {k: v.to_string() for k, v in mapping.items() if isinstance(v, Expression)}
        })
        
        return target_conclusion, confidence
    
    def _build_mapping(self, source_expr: Expression, target_expr: Expression, mapping: Dict[str, Any]) -> None:
        """
        Build a mapping from source to target expressions.
        
        Args:
            source_expr: Source expression
            target_expr: Target expression
            mapping: Dictionary to store the mapping
        """
        # Map atoms
        if isinstance(source_expr, Atom) and isinstance(target_expr, Atom):
            mapping[source_expr.name] = target_expr
        
        # Map negations
        elif isinstance(source_expr, Negation) and isinstance(target_expr, Negation):
            self._build_mapping(source_expr.expr, target_expr.expr, mapping)
            
        # Map binary operations
        elif (isinstance(source_expr, (Conjunction, Disjunction, Implication, Equivalence)) and
             isinstance(target_expr, (Conjunction, Disjunction, Implication, Equivalence)) and
             type(source_expr) == type(target_expr)):
            self._build_mapping(source_expr.left, target_expr.left, mapping)
            self._build_mapping(source_expr.right, target_expr.right, mapping)
    
    def _apply_mapping(self, expr: Expression, mapping: Dict[str, Expression]) -> Expression:
        """
        Apply a mapping to an expression.
        
        Args:
            expr: Expression to map
            mapping: Mapping from source to target
            
        Returns:
            Mapped expression
        """
        # Apply to atoms
        if isinstance(expr, Atom):
            return mapping.get(expr.name, expr)
            
        # Apply to constants
        elif isinstance(expr, Constant):
            return expr
            
        # Apply to negations
        elif isinstance(expr, Negation):
            mapped_expr = self._apply_mapping(expr.expr, mapping)
            return Negation(mapped_expr)
            
        # Apply to binary operations
        elif isinstance(expr, Conjunction):
            mapped_left = self._apply_mapping(expr.left, mapping)
            mapped_right = self._apply_mapping(expr.right, mapping)
            return Conjunction(mapped_left, mapped_right)
            
        elif isinstance(expr, Disjunction):
            mapped_left = self._apply_mapping(expr.left, mapping)
            mapped_right = self._apply_mapping(expr.right, mapping)
            return Disjunction(mapped_left, mapped_right)
            
        elif isinstance(expr, Implication):
            mapped_premise = self._apply_mapping(expr.premise, mapping)
            mapped_conclusion = self._apply_mapping(expr.conclusion, mapping)
            return Implication(mapped_premise, mapped_conclusion)
            
        elif isinstance(expr, Equivalence):
            mapped_left = self._apply_mapping(expr.left, mapping)
            mapped_right = self._apply_mapping(expr.right, mapping)
            return Equivalence(mapped_left, mapped_right)
            
        # Default
        return expr
    
    def probabilistic_reasoning(self, expr_str: str, evidence: Optional[Dict[str, bool]] = None) -> float:
        """
        Compute the probability of an expression, possibly given evidence.
        
        Args:
            expr_str: Expression string
            evidence: Dictionary mapping variable names to boolean values
            
        Returns:
            Probability value
        """
        if LogicType.PROBABILISTIC not in self.logic_types:
            raise ValueError("Probabilistic reasoning not enabled in this engine")
            
        # Parse expression
        expr = self.parser.parse(expr_str)
        
        # Compute probability
        if evidence:
            # Compute conditional probability
            evidence_expr = self._dict_to_conjunction(evidence)
            return self.probabilistic_reasoner.conditional_probability(expr, evidence_expr)
        else:
            # Compute marginal probability
            return self.probabilistic_reasoner.probability(expr)
    
    def _dict_to_conjunction(self, var_dict: Dict[str, bool]) -> Expression:
        """
        Convert a dictionary of variable assignments to a conjunction.
        
        Args:
            var_dict: Dictionary mapping variable names to boolean values
            
        Returns:
            Conjunction expression
        """
        exprs = []
        for var, value in var_dict.items():
            atom = Atom(var)
            exprs.append(atom if value else Negation(atom))
            
        if not exprs:
            return Constant(True)
        elif len(exprs) == 1:
            return exprs[0]
        else:
            # Build conjunction
            result = exprs[0]
            for expr in exprs[1:]:
                result = Conjunction(result, expr)
            return result
    
    def fuzzy_reasoning(self, expr_str: str) -> float:
        """
        Compute the fuzzy membership degree of an expression.
        
        Args:
            expr_str: Expression string
            
        Returns:
            Membership value
        """
        if LogicType.FUZZY not in self.logic_types:
            raise ValueError("Fuzzy reasoning not enabled in this engine")
            
        # Parse expression
        expr = self.parser.parse(expr_str)
        
        # Compute membership
        return self.fuzzy_reasoner.membership(expr)
    
    def evaluate_fuzzy_rules(self, inputs: Dict[str, float]) -> Dict[str, float]:
        """
        Evaluate fuzzy rules with given crisp inputs.
        
        Args:
            inputs: Dictionary mapping input variables to crisp values
            
        Returns:
            Dictionary mapping output variables to fuzzy membership values
        """
        if LogicType.FUZZY not in self.logic_types:
            raise ValueError("Fuzzy reasoning not enabled in this engine")
            
        return self.fuzzy_reasoner.evaluate_rules(inputs)
    
    def modal_reasoning(self, expr_str: str, logic_type: str = "K") -> bool:
        """
        Check if a modal expression is valid.
        
        Args:
            expr_str: Modal expression string
            logic_type: Type of modal logic ("K", "T", "S4", "S5")
            
        Returns:
            True if the expression is valid, False otherwise
        """
        if LogicType.MODAL not in self.logic_types:
            raise ValueError("Modal reasoning not enabled in this engine")
            
        # Parse expression
        expr = self.parser.parse(expr_str)
        
        # Set logic type
        self.modal_reasoner.set_logic_type(logic_type)
        
        # Check validity
        return self.modal_reasoner.is_valid(expr)
    
    def temporal_reasoning(self, expr_str: str) -> bool:
        """
        Check if a temporal expression is valid.
        
        Args:
            expr_str: Temporal expression string
            
        Returns:
            True if the expression is valid, False otherwise
        """
        if LogicType.TEMPORAL not in self.logic_types:
            raise ValueError("Temporal reasoning not enabled in this engine")
            
        # Parse expression
        expr = self.parser.parse(expr_str)
        
        # Check validity
        return self.temporal_reasoner.is_valid(expr)
    
    def get_explanation(self, index: Optional[int] = None) -> Dict:
        """
        Get the explanation for a reasoning process.
        
        Args:
            index: Index in the explanation history (default: most recent)
            
        Returns:
            Explanation dictionary
        """
        if not self.explanation_history:
            return {"error": "No explanations available"}
            
        if index is None:
            index = len(self.explanation_history) - 1
            
        if 0 <= index < len(self.explanation_history):
            return self.explanation_history[index]
        else:
            return {"error": "Invalid explanation index"}
    
    def get_all_explanations(self) -> List[Dict]:
        """
        Get all explanations in the history.
        
        Returns:
            List of explanation dictionaries
        """
        return self.explanation_history
    
    def load_knowledge_base(self, kb_path: str) -> None:
        """
        Load a knowledge base from a file.
        
        Args:
            kb_path: Path to the knowledge base file
        """
        self.kb.load_from_file(kb_path, self.parser)
        
        # Update reasoners
        self.classical_reasoner.set_knowledge_base(self.kb)
        self.probabilistic_reasoner.set_knowledge_base(self.kb)
        self.fuzzy_reasoner.set_knowledge_base(self.kb)
        self.modal_reasoner.set_knowledge_base(self.kb)
        self.temporal_reasoner.set_knowledge_base(self.kb)
        
        logger.info(f"Loaded knowledge base from {kb_path}")
    
    def save_knowledge_base(self, kb_path: str) -> None:
        """
        Save the knowledge base to a file.
        
        Args:
            kb_path: Path to save the knowledge base
        """
        self.kb.dump_to_file(kb_path)
        logger.info(f"Saved knowledge base to {kb_path}")


# Helper functions for module level access

def create_atom(name: str) -> Atom:
    """
    Create an atomic proposition.
    
    Args:
        name: Name of the atom
        
    Returns:
        Atom instance
    """
    return Atom(name)

def create_constant(value: bool) -> Constant:
    """
    Create a logical constant.
    
    Args:
        value: Boolean value
        
    Returns:
        Constant instance
    """
    return Constant(value)

def create_negation(expr: Expression) -> Negation:
    """
    Create a negation.
    
    Args:
        expr: Expression to negate
        
    Returns:
        Negation instance
    """
    return Negation(expr)

def create_conjunction(left: Expression, right: Expression) -> Conjunction:
    """
    Create a conjunction.
    
    Args:
        left: Left operand
        right: Right operand
        
    Returns:
        Conjunction instance
    """
    return Conjunction(left, right)

def create_disjunction(left: Expression, right: Expression) -> Disjunction:
    """
    Create a disjunction.
    
    Args:
        left: Left operand
        right: Right operand
        
    Returns:
        Disjunction instance
    """
    return Disjunction(left, right)

def create_implication(premise: Expression, conclusion: Expression) -> Implication:
    """
    Create an implication.
    
    Args:
        premise: Premise expression
        conclusion: Conclusion expression
        
    Returns:
        Implication instance
    """
    return Implication(premise, conclusion)

def create_equivalence(left: Expression, right: Expression) -> Equivalence:
    """
    Create an equivalence.
    
    Args:
        left: Left operand
        right: Right operand
        
    Returns:
        Equivalence instance
    """
    return Equivalence(left, right)

def parse_expression(expr_str: str) -> Expression:
    """
    Parse a string expression.
    
    Args:
        expr_str: String representation of the expression
        
    Returns:
        Parsed Expression instance
    """
    parser = ExpressionParser()
    return parser.parse(expr_str)


# Create default engine
default_engine = LogicalReasoningEngine()

# Module-level functions that use the default engine
def deductive_reasoning(premises: List[str], conclusion: Optional[str] = None) -> Tuple[bool, List[str]]:
    """
    Perform deductive reasoning using the default engine.
    
    Args:
        premises: List of premise strings
        conclusion: Optional conclusion string to check
        
    Returns:
        Tuple of (is_valid, derived_conclusions)
    """
    return default_engine.deductive_reasoning(premises, conclusion)

def inductive_reasoning(instances: List[Tuple[str, str]], generalization: Optional[str] = None) -> Tuple[float, Optional[str]]:
    """
    Perform inductive reasoning using the default engine.
    
    Args:
        instances: List of (condition, outcome) string pairs
        generalization: Optional generalization string to check
        
    Returns:
        Tuple of (confidence, generalization)
    """
    return default_engine.inductive_reasoning(instances, generalization)

def abductive_reasoning(observation: str, hypotheses: Optional[List[str]] = None) -> List[Tuple[str, float]]:
    """
    Perform abductive reasoning using the default engine.
    
    Args:
        observation: The observed fact B
        hypotheses: Optional list of potential hypotheses to consider
        
    Returns:
        List of (hypothesis, plausibility) pairs
    """
    return default_engine.abductive_reasoning(observation, hypotheses)

def analogical_reasoning(source_premises: List[str], source_conclusion: str, 
                      target_premises: List[str]) -> Tuple[str, float]:
    """
    Perform analogical reasoning using the default engine.
    
    Args:
        source_premises: List of premise strings for the source problem
        source_conclusion: Conclusion string for the source problem
        target_premises: List of premise strings for the target problem
        
    Returns:
        Tuple of (target_conclusion, confidence)
    """
    return default_engine.analogical_reasoning(source_premises, source_conclusion, target_premises)

def probabilistic_reasoning(expr_str: str, evidence: Optional[Dict[str, bool]] = None) -> float:
    """
    Compute the probability of an expression using the default engine.
    
    Args:
        expr_str: Expression string
        evidence: Dictionary mapping variable names to boolean values
        
    Returns:
        Probability value
    """
    return default_engine.probabilistic_reasoning(expr_str, evidence)

def fuzzy_reasoning(expr_str: str) -> float:
    """
    Compute the fuzzy membership degree of an expression using the default engine.
    
    Args:
        expr_str: Expression string
        
    Returns:
        Membership value
    """
    return default_engine.fuzzy_reasoning(expr_str)

def add_fact(fact_str: str) -> None:
    """
    Add a fact to the default engine's knowledge base.
    
    Args:
        fact_str: String representation of the fact
    """
    default_engine.add_fact(fact_str)

def add_rule(rule_str: str) -> None:
    """
    Add a rule to the default engine's knowledge base.
    
    Args:
        rule_str: String representation of the rule
    """
    default_engine.add_rule(rule_str)

def init_engine(config: Optional[Dict] = None) -> LogicalReasoningEngine:
    """
    Initialize the default logical reasoning engine.
    
    Args:
        config: Optional configuration dictionary
        
    Returns:
        Initialized engine
    """
    global default_engine
    
    # Create new engine with config
    if config:
        default_engine = LogicalReasoningEngine(config=config)
    
    # Initialize the engine
    default_engine.initialize()
    
    return default_engine


# Export main components and utility functions
__all__ = [
    # Classes
    'Expression', 
    'Atom', 
    'Constant', 
    'Negation', 
    'Conjunction', 
    'Disjunction', 
    'Implication', 
    'Equivalence',
    'ProbabilisticExpression', 
    'ConditionalProbabilisticExpression', 
    'FuzzyExpression',
    'KnowledgeBase', 
    'ExpressionParser',
    'ClassicalReasoner', 
    'ProbabilisticReasoner', 
    'FuzzyReasoner', 
    'ModalReasoner', 
    'TemporalReasoner',
    'LogicalReasoningEngine',
    'ReasoningType', 
    'LogicType',
    
    # Factory functions
    'create_atom', 
    'create_constant', 
    'create_negation', 
    'create_conjunction', 
    'create_disjunction', 
    'create_implication', 
    'create_equivalence',
    'parse_expression',
    
    # Module-level functions
    'deductive_reasoning', 
    'inductive_reasoning', 
    'abductive_reasoning', 
    'analogical_reasoning',
    'probabilistic_reasoning', 
    'fuzzy_reasoning',
    'add_fact', 
    'add_rule',
    'init_engine',
    
    # Engine instance
    'default_engine'
]