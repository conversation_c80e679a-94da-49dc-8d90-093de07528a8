#!/usr/bin/env python3
"""
ULTRA: Rule Extraction Module

This module implements the Rule Extraction component of the Neuro-Symbolic Integration
subsystem. It provides mechanisms for extracting symbolic rules from neural networks,
enabling the transformation of implicit knowledge embedded in neural networks into
explicit symbolic representations.

The module implements several rule extraction algorithms:
1. DeepRED: Decompositional approach extracting rules layer by layer
2. TREPAN: Pedagogical approach using decision trees and oracle queries
3. ECLAIRE: Concept lattice-based approach for rule extraction
4. M-of-N: Extracting M-of-N rules (if M out of N conditions are true, then conclusion)
5. Decision Tree: Direct mapping of neural model to decision tree rules

Author: ULTRA Development Team
"""

import os
import sys
import logging
import numpy as np
import math
import json
import re
from typing import Dict, List, Tuple, Set, Any, Union, Optional, Callable, Type, TypeVar
from enum import Enum, auto
from abc import ABC, abstractmethod
from collections import defaultdict, deque, Counter
import copy
import random
import itertools

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader, TensorDataset

from sklearn.tree import DecisionTreeClassifier, DecisionTreeRegressor, _tree, export_text
from sklearn.ensemble import RandomForestClassifier, RandomForestRegressor
from sklearn.metrics import accuracy_score, f1_score, precision_score, recall_score, mean_squared_error, r2_score
from sklearn.model_selection import train_test_split
from sklearn.cluster import KMeans
import xgboost as xgb
import shap

# For concept lattice-based extraction
from itertools import combinations
import networkx as nx

# Import local modules
try:
    from .logical_reasoning import (
        Expression, 
        Atom, 
        Constant, 
        Negation, 
        Conjunction, 
        Disjunction, 
        Implication, 
        Equivalence,
        KnowledgeBase,
        ExpressionParser,
        LogicalReasoningEngine
    )
except ImportError:
    # For standalone testing
    from logical_reasoning import (
        Expression, 
        Atom, 
        Constant, 
        Negation, 
        Conjunction, 
        Disjunction, 
        Implication, 
        Equivalence,
        KnowledgeBase,
        ExpressionParser,
        LogicalReasoningEngine
    )

# Configure logging
logger = logging.getLogger(__name__)
handler = logging.StreamHandler()
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
handler.setFormatter(formatter)
logger.addHandler(handler)
logger.setLevel(logging.INFO)

# Type variables for generic functions
T = TypeVar('T')
U = TypeVar('U')

# Enums and type definitions
class RuleExtractionMethod(Enum):
    """Methods for rule extraction from neural networks."""
    DEEPRED = auto()          # Decompositional approach
    TREPAN = auto()           # Pedagogical approach using decision trees with oracle queries
    DECISION_TREE = auto()    # Direct decision tree extraction
    M_OF_N = auto()           # M-of-N rule extraction
    ECLAIRE = auto()          # Concept lattice-based rule extraction

class RuleType(Enum):
    """Types of rules that can be extracted."""
    PROPOSITIONAL = auto()    # Propositional logic rules
    FIRST_ORDER = auto()      # First-order logic rules
    FUZZY = auto()            # Fuzzy logic rules
    PROBABILISTIC = auto()    # Probabilistic rules
    M_OF_N = auto()           # M-of-N rules

class RuleConfidence(Enum):
    """Confidence or certainty metrics for extracted rules."""
    FIDELITY = auto()         # How well the rules model the neural network
    ACCURACY = auto()         # How well the rules match ground truth
    COVERAGE = auto()         # Proportion of input space covered by the rules
    CONSISTENCY = auto()      # Internal consistency of the rule set


class Rule:
    """
    Base class for representing symbolic rules extracted from neural networks.
    """
    
    def __init__(self, premise: Expression, conclusion: Expression, 
                confidence: float = 1.0, support: int = 0):
        """
        Initialize a rule with premise, conclusion, confidence, and support.
        
        Args:
            premise: Premise expression (antecedent)
            conclusion: Conclusion expression (consequent)
            confidence: Confidence value for the rule [0, 1]
            support: Number of examples supporting the rule
        """
        self.premise = premise
        self.conclusion = conclusion
        self.confidence = confidence
        self.support = support
        
        # Compute complexity metrics
        self.complexity = self._compute_complexity()
        
        # Validation status
        self.is_validated = False
        self.validation_metrics = {}
    
    def _compute_complexity(self) -> int:
        """
        Compute the complexity of the rule.
        
        Returns:
            Complexity score (higher means more complex)
        """
        # Count the number of operators in premise and conclusion
        def count_ops(expr: Expression) -> int:
            if isinstance(expr, (Atom, Constant)):
                return 0
            elif isinstance(expr, Negation):
                return 1 + count_ops(expr.expr)
            elif isinstance(expr, (Conjunction, Disjunction, Implication, Equivalence)):
                return 1 + count_ops(expr.left) + count_ops(expr.right)
            else:
                return 1  # Unknown expression type
                
        return count_ops(self.premise) + count_ops(self.conclusion)
    
    def to_implication(self) -> Implication:
        """
        Convert the rule to an implication expression.
        
        Returns:
            Implication expression representing the rule
        """
        return Implication(self.premise, self.conclusion)
    
    def to_string(self) -> str:
        """
        Convert the rule to a string representation.
        
        Returns:
            String representation of the rule
        """
        return f"{self.premise.to_string()} -> {self.conclusion.to_string()} (conf={self.confidence:.4f}, supp={self.support})"
    
    def __str__(self) -> str:
        return self.to_string()
    
    def __repr__(self) -> str:
        return self.to_string()


class MofNRule(Rule):
    """
    Represents an M-of-N rule: if M out of N conditions are true, then the conclusion is true.
    """
    
    def __init__(self, conditions: List[Expression], conclusion: Expression, 
                m: int, confidence: float = 1.0, support: int = 0):
        """
        Initialize an M-of-N rule.
        
        Args:
            conditions: List of N condition expressions
            conclusion: Conclusion expression (consequent)
            m: Threshold value M (minimum number of conditions to satisfy)
            confidence: Confidence value for the rule [0, 1]
            support: Number of examples supporting the rule
        """
        self.conditions = conditions
        self.m = m
        self.n = len(conditions)
        
        # Create premise as a meta-expression (for compatibility with base Rule class)
        # In practice, this is a placeholder as M-of-N rules have special evaluation logic
        premise = self._create_m_of_n_premise()
        
        super().__init__(premise, conclusion, confidence, support)
    
    def _create_m_of_n_premise(self) -> Expression:
        """
        Create a logical expression representing the M-of-N premise.
        
        Returns:
            Expression representing the M-of-N condition
        """
        # For small N, we can create the exact logical expression
        if self.n <= 10:  # Arbitrary threshold to avoid combinatorial explosion
            # Generate all combinations of M or more conditions
            combinations_list = []
            for r in range(self.m, self.n + 1):
                for combo in itertools.combinations(self.conditions, r):
                    # Create conjunction for this combination
                    conjunction = combo[0]
                    for cond in combo[1:]:
                        conjunction = Conjunction(conjunction, cond)
                    combinations_list.append(conjunction)
            
            # Create disjunction of all combinations
            if not combinations_list:
                return Constant(False)
            
            disjunction = combinations_list[0]
            for expr in combinations_list[1:]:
                disjunction = Disjunction(disjunction, expr)
                
            return disjunction
        else:
            # For large N, just create a placeholder
            # In practice, we'll use the evaluate method instead
            return Atom(f"M({self.m})-of-N({self.n})")
    
    def evaluate(self, interpretation: Dict[str, bool]) -> bool:
        """
        Evaluate the M-of-N rule under a given interpretation.
        
        Args:
            interpretation: Dictionary mapping variable names to boolean values
            
        Returns:
            Boolean result of evaluation
        """
        # Count how many conditions are satisfied
        satisfied = sum(1 for cond in self.conditions if cond.evaluate(interpretation))
        
        # Rule is satisfied if at least M conditions are satisfied
        return satisfied >= self.m
    
    def to_string(self) -> str:
        """
        Convert the rule to a string representation.
        
        Returns:
            String representation of the rule
        """
        conditions_str = ", ".join(cond.to_string() for cond in self.conditions)
        return f"IF {self.m} of [{conditions_str}] THEN {self.conclusion.to_string()} (conf={self.confidence:.4f}, supp={self.support})"


class DecisionTreeRule(Rule):
    """
    Represents a rule extracted from a decision tree path.
    """
    
    def __init__(self, path_conditions: List[Tuple[str, str, float]], 
                conclusion: Expression, confidence: float = 1.0, support: int = 0,
                feature_names: Optional[List[str]] = None):
        """
        Initialize a decision tree rule.
        
        Args:
            path_conditions: List of (feature, operator, threshold) tuples
            conclusion: Conclusion expression (consequent)
            confidence: Confidence value for the rule [0, 1]
            support: Number of examples supporting the rule
            feature_names: Optional list of feature names for readability
        """
        self.path_conditions = path_conditions
        self.feature_names = feature_names
        
        # Create premise as conjunction of all path conditions
        premise = self._create_premise_from_path()
        
        super().__init__(premise, conclusion, confidence, support)
    
    def _create_premise_from_path(self) -> Expression:
        """
        Create a logical expression representing the decision path.
        
        Returns:
            Expression representing the conjunction of path conditions
        """
        conditions = []
        for feature_idx, operator, threshold in self.path_conditions:
            # Get feature name
            if self.feature_names and isinstance(feature_idx, int) and feature_idx < len(self.feature_names):
                feature = self.feature_names[feature_idx]
            elif self.feature_names and feature_idx in self.feature_names:
                feature = feature_idx
            else:
                feature = f"x[{feature_idx}]"
                
            # Create atom based on operator
            atom_str = f"{feature} {operator} {threshold:.4f}"
            conditions.append(Atom(atom_str))
            
        # Create conjunction of all conditions
        if not conditions:
            return Constant(True)
            
        conjunction = conditions[0]
        for cond in conditions[1:]:
            conjunction = Conjunction(conjunction, cond)
            
        return conjunction
    
    def to_string(self) -> str:
        """
        Convert the rule to a string representation.
        
        Returns:
            String representation of the rule
        """
        path_str = " AND ".join(f"{feature} {operator} {threshold:.4f}" 
                              for feature, operator, threshold in self.path_conditions)
        return f"IF {path_str} THEN {self.conclusion.to_string()} (conf={self.confidence:.4f}, supp={self.support})"


class RuleSet:
    """
    Represents a set of extracted rules from a neural network.
    """
    
    def __init__(self, rules: List[Rule] = None, name: str = "RuleSet", 
                extraction_method: RuleExtractionMethod = None):
        """
        Initialize a rule set.
        
        Args:
            rules: List of Rule objects
            name: Name for the rule set
            extraction_method: Method used for rule extraction
        """
        self.rules = rules or []
        self.name = name
        self.extraction_method = extraction_method
        
        # Rule set metrics
        self.metrics = {
            'fidelity': 0.0,
            'accuracy': 0.0,
            'coverage': 0.0,
            'consistency': 0.0,
            'avg_support': 0.0,
            'avg_confidence': 0.0,
            'avg_complexity': 0.0,
            'num_rules': 0
        }
        
        # Rule set graph representation (for exploration and visualization)
        self.rule_graph = None
        
        # Update metrics
        self._update_metrics()
    
    def _update_metrics(self) -> None:
        """
        Update rule set metrics based on current rules.
        """
        if not self.rules:
            return
            
        # Simple metrics
        self.metrics['num_rules'] = len(self.rules)
        self.metrics['avg_support'] = sum(rule.support for rule in self.rules) / len(self.rules)
        self.metrics['avg_confidence'] = sum(rule.confidence for rule in self.rules) / len(self.rules)
        self.metrics['avg_complexity'] = sum(rule.complexity for rule in self.rules) / len(self.rules)
        
        # Consistency (to be computed later during validation)
        # Fidelity, accuracy, and coverage also need validation data
    
    def add_rule(self, rule: Rule) -> None:
        """
        Add a rule to the rule set.
        
        Args:
            rule: Rule to add
        """
        self.rules.append(rule)
        
        # Update metrics
        self._update_metrics()
    
    def remove_rule(self, rule: Rule) -> None:
        """
        Remove a rule from the rule set.
        
        Args:
            rule: Rule to remove
        """
        if rule in self.rules:
            self.rules.remove(rule)
            
            # Update metrics
            self._update_metrics()
    
    def sort_rules(self, key: str = 'confidence', reverse: bool = True) -> None:
        """
        Sort rules based on a specified key.
        
        Args:
            key: Attribute to sort by ('confidence', 'support', 'complexity')
            reverse: Whether to sort in descending order
        """
        if key == 'confidence':
            self.rules.sort(key=lambda rule: rule.confidence, reverse=reverse)
        elif key == 'support':
            self.rules.sort(key=lambda rule: rule.support, reverse=reverse)
        elif key == 'complexity':
            # Sort by complexity (ascending by default - simpler rules first)
            self.rules.sort(key=lambda rule: rule.complexity, reverse=not reverse)
    
    def filter_rules(self, min_confidence: float = 0.0, min_support: int = 0, 
                  max_complexity: Optional[int] = None) -> 'RuleSet':
        """
        Filter rules based on confidence, support, and complexity.
        
        Args:
            min_confidence: Minimum confidence threshold
            min_support: Minimum support threshold
            max_complexity: Maximum complexity threshold
            
        Returns:
            New rule set with filtered rules
        """
        filtered_rules = []
        
        for rule in self.rules:
            if rule.confidence >= min_confidence and rule.support >= min_support:
                if max_complexity is None or rule.complexity <= max_complexity:
                    filtered_rules.append(rule)
                    
        filtered_set = RuleSet(
            rules=filtered_rules,
            name=f"{self.name}_filtered",
            extraction_method=self.extraction_method
        )
        
        return filtered_set
    
    def simplify(self, min_improvement: float = 0.1) -> 'RuleSet':
        """
        Simplify the rule set by removing redundant and subsumed rules.
        
        Args:
            min_improvement: Minimum improvement threshold for rule simplification
            
        Returns:
            Simplified rule set
        """
        # Start with a copy of the rules
        simplified_rules = self.rules.copy()
        
        # First pass: remove exact duplicates
        unique_rules = []
        unique_str = set()
        
        for rule in simplified_rules:
            rule_str = rule.to_string()
            if rule_str not in unique_str:
                unique_rules.append(rule)
                unique_str.add(rule_str)
                
        simplified_rules = unique_rules
        
        # Second pass: address subsumption
        # This would require evaluating logical implications between rules
        # For now, we'll use a simplified approach
        to_remove = set()
        
        for i, rule1 in enumerate(simplified_rules):
            for j, rule2 in enumerate(simplified_rules):
                if i == j or i in to_remove or j in to_remove:
                    continue
                    
                # If rule1 and rule2 have the same conclusion,
                # and rule1 has higher confidence/support with similar complexity,
                # we might remove rule2
                if (rule1.conclusion.to_string() == rule2.conclusion.to_string() and
                    rule1.confidence >= rule2.confidence + min_improvement and
                    rule1.support >= rule2.support and
                    rule1.complexity <= rule2.complexity * 1.1):  # Allow slight complexity increase
                    
                    to_remove.add(j)
                    
        # Create new rule set without removed rules
        final_rules = [rule for i, rule in enumerate(simplified_rules) if i not in to_remove]
        
        simplified_set = RuleSet(
            rules=final_rules,
            name=f"{self.name}_simplified",
            extraction_method=self.extraction_method
        )
        
        return simplified_set
    
    def build_rule_graph(self) -> nx.DiGraph:
        """
        Build a directed graph representing the rule set structure.
        
        Returns:
            NetworkX DiGraph representing the rule set
        """
        graph = nx.DiGraph()
        
        # Add rules as nodes
        for i, rule in enumerate(self.rules):
            node_id = f"rule_{i}"
            graph.add_node(node_id, rule=rule, label=rule.to_string())
            
            # Extract atoms from premise and conclusion
            premise_atoms = self._extract_atoms(rule.premise)
            conclusion_atoms = self._extract_atoms(rule.conclusion)
            
            # Add atoms as nodes if they don't exist
            for atom in premise_atoms.union(conclusion_atoms):
                if atom not in graph:
                    graph.add_node(atom, label=atom, atom=True)
                    
            # Add edges from atoms to rules (premise)
            for atom in premise_atoms:
                graph.add_edge(atom, node_id, role='premise')
                
            # Add edges from rules to atoms (conclusion)
            for atom in conclusion_atoms:
                graph.add_edge(node_id, atom, role='conclusion')
                
        self.rule_graph = graph
        return graph
    
    def _extract_atoms(self, expr: Expression) -> Set[str]:
        """
        Extract atomic propositions from an expression.
        
        Args:
            expr: Logical expression
            
        Returns:
            Set of atomic proposition strings
        """
        if isinstance(expr, Atom):
            return {expr.to_string()}
        elif isinstance(expr, Constant):
            return set()
        elif isinstance(expr, Negation):
            return self._extract_atoms(expr.expr)
        elif isinstance(expr, (Conjunction, Disjunction, Implication, Equivalence)):
            return self._extract_atoms(expr.left).union(self._extract_atoms(expr.right))
        else:
            return set()
    
    def to_knowledge_base(self) -> KnowledgeBase:
        """
        Convert the rule set to a knowledge base.
        
        Returns:
            KnowledgeBase containing the rules
        """
        kb = KnowledgeBase(name=self.name)
        
        for rule in self.rules:
            implication = rule.to_implication()
            kb.add_rule(implication)
            
        return kb
    
    def save(self, file_path: str) -> None:
        """
        Save the rule set to a file.
        
        Args:
            file_path: Path to save the rule set
        """
        # Create a JSON serializable representation
        rule_set_dict = {
            'name': self.name,
            'extraction_method': self.extraction_method.name if self.extraction_method else None,
            'metrics': self.metrics,
            'rules': []
        }
        
        # Add each rule
        for rule in self.rules:
            rule_dict = {
                'type': rule.__class__.__name__,
                'premise': rule.premise.to_string(),
                'conclusion': rule.conclusion.to_string(),
                'confidence': rule.confidence,
                'support': rule.support,
                'complexity': rule.complexity
            }
            
            # Add type-specific fields
            if isinstance(rule, MofNRule):
                rule_dict['conditions'] = [cond.to_string() for cond in rule.conditions]
                rule_dict['m'] = rule.m
                rule_dict['n'] = rule.n
            elif isinstance(rule, DecisionTreeRule):
                rule_dict['path_conditions'] = rule.path_conditions
                rule_dict['feature_names'] = rule.feature_names
                
            rule_set_dict['rules'].append(rule_dict)
            
        # Save to file
        with open(file_path, 'w') as f:
            json.dump(rule_set_dict, f, indent=2)
            
        logger.info(f"Saved rule set to {file_path}")
    
    @classmethod
    def load(cls, file_path: str, parser: ExpressionParser) -> 'RuleSet':
        """
        Load a rule set from a file.
        
        Args:
            file_path: Path to load the rule set from
            parser: Expression parser for parsing rule strings
            
        Returns:
            Loaded RuleSet object
        """
        with open(file_path, 'r') as f:
            rule_set_dict = json.load(f)
            
        # Extract rule set attributes
        name = rule_set_dict.get('name', "RuleSet")
        extraction_method_str = rule_set_dict.get('extraction_method', None)
        extraction_method = RuleExtractionMethod[extraction_method_str] if extraction_method_str else None
        
        # Create empty rule set
        rule_set = cls(
            rules=[],
            name=name,
            extraction_method=extraction_method
        )
        
        # Add metrics
        rule_set.metrics = rule_set_dict.get('metrics', rule_set.metrics)
        
        # Parse and add rules
        for rule_dict in rule_set_dict.get('rules', []):
            rule_type = rule_dict.get('type', 'Rule')
            premise_str = rule_dict.get('premise', '')
            conclusion_str = rule_dict.get('conclusion', '')
            confidence = rule_dict.get('confidence', 1.0)
            support = rule_dict.get('support', 0)
            
            # Parse premise and conclusion
            premise = parser.parse(premise_str)
            conclusion = parser.parse(conclusion_str)
            
            # Create rule based on type
            if rule_type == 'MofNRule':
                conditions_str = rule_dict.get('conditions', [])
                conditions = [parser.parse(cond) for cond in conditions_str]
                m = rule_dict.get('m', 1)
                
                rule = MofNRule(
                    conditions=conditions,
                    conclusion=conclusion,
                    m=m,
                    confidence=confidence,
                    support=support
                )
            elif rule_type == 'DecisionTreeRule':
                path_conditions = rule_dict.get('path_conditions', [])
                feature_names = rule_dict.get('feature_names', None)
                
                rule = DecisionTreeRule(
                    path_conditions=path_conditions,
                    conclusion=conclusion,
                    confidence=confidence,
                    support=support,
                    feature_names=feature_names
                )
            else:
                # Default to basic rule
                rule = Rule(
                    premise=premise,
                    conclusion=conclusion,
                    confidence=confidence,
                    support=support
                )
                
            rule_set.add_rule(rule)
            
        return rule_set
    
    def __str__(self) -> str:
        """String representation of the rule set."""
        header = f"=== RuleSet: {self.name} ({len(self.rules)} rules) ==="
        
        if self.extraction_method:
            header += f"\nExtraction method: {self.extraction_method.name}"
            
        header += f"\nMetrics: {', '.join(f'{k}={v:.4f}' for k, v in self.metrics.items())}"
        
        rule_strs = [f"Rule {i+1}: {rule.to_string()}" for i, rule in enumerate(self.rules)]
        
        return header + "\n" + "\n".join(rule_strs)


class RuleExtractor(ABC):
    """
    Abstract base class for rule extraction algorithms.
    """
    
    def __init__(self, name: str = "RuleExtractor", rule_type: RuleType = RuleType.PROPOSITIONAL):
        """
        Initialize the rule extractor.
        
        Args:
            name: Name of the extractor
            rule_type: Type of rules to extract
        """
        self.name = name
        self.rule_type = rule_type
        
        # Expression parser for creating logical expressions
        self.parser = ExpressionParser()
        
        # Feature names for interpretability
        self.feature_names = None
        
        # Rule extraction metrics
        self.extraction_metrics = {}
        
        # Flag to track if extraction has been performed
        self._has_extracted = False
    
    @abstractmethod
    def extract_rules(self, model: nn.Module, X: torch.Tensor, 
                     y: torch.Tensor, **kwargs) -> RuleSet:
        """
        Extract rules from a neural network.
        
        Args:
            model: Neural network model
            X: Input data
            y: Target labels or values
            **kwargs: Additional extraction parameters
            
        Returns:
            Extracted RuleSet
        """
        pass
    
    def set_feature_names(self, feature_names: List[str]) -> None:
        """
        Set feature names for interpretability.
        
        Args:
            feature_names: List of feature names
        """
        self.feature_names = feature_names
    
    def validate_rules(self, rule_set: RuleSet, model: nn.Module, 
                     X_val: torch.Tensor, y_val: torch.Tensor) -> Dict[str, float]:
        """
        Validate extracted rules against the neural model.
        
        Args:
            rule_set: Rule set to validate
            model: Neural network model
            X_val: Validation inputs
            y_val: Validation targets
            
        Returns:
            Dictionary of validation metrics
        """
        # Move model to evaluation mode
        model.eval()
        
        # Get predictions from neural model
        with torch.no_grad():
            y_model = model(X_val)
            
        # Convert to numpy for easier processing
        X_np = X_val.cpu().numpy()
        y_np = y_val.cpu().numpy()
        y_model_np = y_model.cpu().numpy()
        
        # For classification, get class predictions
        if y_model.dim() > 1 and y_model.size(1) > 1:
            y_model_pred = torch.argmax(y_model, dim=1).cpu().numpy()
        else:
            # For regression or binary classification
            if y_model.dim() > 1:
                y_model_np = y_model.squeeze(1).cpu().numpy()
                
            # Threshold at 0.5 for binary classification
            y_model_pred = (y_model_np > 0.5).astype(int)
            
        # Get predictions from rules for each sample
        y_rules_pred = np.zeros_like(y_model_pred)
        rule_coverage = np.zeros_like(y_model_pred, dtype=bool)
        
        for i in range(len(X_np)):
            # Create interpretation (map feature names/indices to values)
            interpretation = {}
            
            # If feature names are available, use them
            if self.feature_names:
                for j, name in enumerate(self.feature_names):
                    if j < X_np.shape[1]:
                        interpretation[name] = X_np[i, j] > 0.5  # Binarize for boolean logic
            else:
                # Use feature indices
                for j in range(X_np.shape[1]):
                    interpretation[f"x[{j}]"] = X_np[i, j] > 0.5
                    
            # Evaluate each rule
            matched_rule = False
            for rule in rule_set.rules:
                if isinstance(rule, MofNRule):
                    # Special evaluation for M-of-N rules
                    premise_satisfied = rule.evaluate(interpretation)
                else:
                    # Normal rule evaluation
                    premise_satisfied = rule.premise.evaluate(interpretation)
                    
                if premise_satisfied:
                    # Rule triggered - get conclusion
                    if isinstance(rule.conclusion, Atom) and rule.conclusion.name.isdigit():
                        # Conclusion is a class index
                        y_rules_pred[i] = int(rule.conclusion.name)
                    else:
                        # Try to evaluate conclusion
                        conclusion_val = rule.conclusion.evaluate(interpretation)
                        y_rules_pred[i] = 1 if conclusion_val else 0
                        
                    matched_rule = True
                    break
                    
            # Update coverage
            rule_coverage[i] = matched_rule
            
        # Compute metrics
        # 1. Fidelity: agreement between rules and model
        fidelity = np.mean(y_rules_pred[rule_coverage] == y_model_pred[rule_coverage])
        
        # 2. Accuracy: agreement between rules and ground truth
        accuracy = np.mean(y_rules_pred[rule_coverage] == y_np[rule_coverage])
        
        # 3. Coverage: proportion of instances covered by at least one rule
        coverage = np.mean(rule_coverage)
        
        # 4. Consistency: check for contradictions in the rule set
        # This is a placeholder implementation - real consistency checking would be more complex
        consistency = 1.0  # Assume consistent for now
        
        # Update rule set metrics
        rule_set.metrics['fidelity'] = fidelity
        rule_set.metrics['accuracy'] = accuracy
        rule_set.metrics['coverage'] = coverage
        rule_set.metrics['consistency'] = consistency
        
        # Create detailed validation metrics
        validation_metrics = {
            'fidelity': fidelity,
            'accuracy': accuracy,
            'coverage': coverage,
            'consistency': consistency
        }
        
        # Add more detailed metrics
        if coverage > 0:
            validation_metrics['precision'] = precision_score(
                y_np[rule_coverage], y_rules_pred[rule_coverage], average='macro', zero_division=0
            )
            validation_metrics['recall'] = recall_score(
                y_np[rule_coverage], y_rules_pred[rule_coverage], average='macro', zero_division=0
            )
            validation_metrics['f1'] = f1_score(
                y_np[rule_coverage], y_rules_pred[rule_coverage], average='macro', zero_division=0
            )
            
        # Store validation metrics
        self.extraction_metrics.update(validation_metrics)
        
        return validation_metrics


class DecisionTreeRuleExtractor(RuleExtractor):
    """
    Extracts rules from a neural network using decision trees.
    """
    
    def __init__(self, name: str = "DecisionTreeExtractor", 
                rule_type: RuleType = RuleType.PROPOSITIONAL,
                max_depth: int = 5, min_samples_leaf: int = 10):
        """
        Initialize the decision tree rule extractor.
        
        Args:
            name: Name of the extractor
            rule_type: Type of rules to extract
            max_depth: Maximum depth of the decision tree
            min_samples_leaf: Minimum samples per leaf
        """
        super().__init__(name, rule_type)
        
        self.max_depth = max_depth
        self.min_samples_leaf = min_samples_leaf
        
        # Fitted tree model
        self.tree_model = None
        
        # Output classes for classification
        self.classes = None
        
        # Is this a regression problem
        self.is_regression = False
    
    def extract_rules(self, model: nn.Module, X: torch.Tensor, 
                     y: torch.Tensor, **kwargs) -> RuleSet:
        """
        Extract rules from a neural network using decision trees.
        
        Args:
            model: Neural network model
            X: Input data
            y: Target labels or values
            **kwargs: Additional extraction parameters:
                - max_depth: Maximum depth of the decision tree
                - min_samples_leaf: Minimum samples per leaf
                - feature_names: List of feature names
                - class_names: List of class names
            
        Returns:
            Extracted RuleSet
        """
        # Update parameters if provided
        self.max_depth = kwargs.get('max_depth', self.max_depth)
        self.min_samples_leaf = kwargs.get('min_samples_leaf', self.min_samples_leaf)
        
        # Set feature names if provided
        if 'feature_names' in kwargs:
            self.feature_names = kwargs['feature_names']
        
        # Determine task type (classification or regression)
        if y.dim() > 1 and y.size(1) > 1:
            # Multi-class classification
            self.is_regression = False
            num_classes = y.size(1)
            
            # Get class indices
            if 'class_names' in kwargs:
                self.classes = kwargs['class_names']
            else:
                self.classes = list(range(num_classes))
                
            # Create decision tree classifier
            self.tree_model = DecisionTreeClassifier(
                max_depth=self.max_depth,
                min_samples_leaf=self.min_samples_leaf
            )
        elif torch.unique(y).size(0) <= 10:  # Arbitrary threshold for classification
            # Binary or small number of classes - classification
            self.is_regression = False
            
            # Get class indices
            self.classes = torch.unique(y).cpu().numpy()
            
            # Create decision tree classifier
            self.tree_model = DecisionTreeClassifier(
                max_depth=self.max_depth,
                min_samples_leaf=self.min_samples_leaf
            )
        else:
            # Regression
            self.is_regression = True
            
            # Create decision tree regressor
            self.tree_model = DecisionTreeRegressor(
                max_depth=self.max_depth,
                min_samples_leaf=self.min_samples_leaf
            )
        
        # Get neural network predictions
        model.eval()
        with torch.no_grad():
            y_pred = model(X)
            
        # Convert data to numpy
        X_np = X.cpu().numpy()
        
        if y_pred.dim() > 1 and not self.is_regression:
            # Classification with probabilities - get predicted class
            y_model = torch.argmax(y_pred, dim=1).cpu().numpy()
        else:
            # Regression or binary classification
            y_model = y_pred.cpu().numpy()
            
            # Ensure correct shape for sklearn
            if y_model.ndim > 1 and y_model.shape[1] == 1:
                y_model = y_model.squeeze(1)
        
        # Train decision tree on neural network predictions
        self.tree_model.fit(X_np, y_model)
        
        # Extract rules from the decision tree
        rules = self._extract_rules_from_tree()
        
        # Create rule set
        rule_set = RuleSet(
            rules=rules,
            name=f"{self.name}_Rules",
            extraction_method=RuleExtractionMethod.DECISION_TREE
        )
        
        # Mark extraction as done
        self._has_extracted = True
        
        return rule_set
    
    def _extract_rules_from_tree(self) -> List[Rule]:
        """
        Extract rules from the trained decision tree.
        
        Returns:
            List of extracted rules
        """
        if self.tree_model is None:
            raise ValueError("Tree model has not been trained")
            
        rules = []
        
        # Get tree structure
        tree = self.tree_model.tree_
        
        # Get feature names (use indices if not provided)
        feature_names = self.feature_names or [f"feature_{i}" for i in range(tree.n_features)]
        
        # Extract rules from each leaf node
        def extract_path(node_id, path_conditions):
            # If leaf node, create rule
            if tree.children_left[node_id] == tree.children_right[node_id]:
                # Get prediction at leaf
                if self.is_regression:
                    # Regression - use mean value
                    value = tree.value[node_id][0, 0]
                    conclusion = Atom(f"{value:.4f}")
                else:
                    # Classification - get majority class
                    class_idx = np.argmax(tree.value[node_id])
                    class_val = self.classes[class_idx] if class_idx < len(self.classes) else class_idx
                    conclusion = Atom(f"{class_val}")
                
                # Get rule support (number of samples)
                support = tree.n_node_samples[node_id]
                
                # Calculate confidence (purity of the leaf)
                if self.is_regression:
                    # For regression, confidence based on variance reduction
                    variance = np.var(tree.value[node_id])
                    confidence = max(0, 1 - variance / (1 + variance))  # Normalize to [0, 1]
                else:
                    # For classification, confidence based on class distribution
                    total_samples = np.sum(tree.value[node_id])
                    max_samples = np.max(tree.value[node_id])
                    confidence = max_samples / total_samples if total_samples > 0 else 0.0
                
                # Create rule
                rule = DecisionTreeRule(
                    path_conditions=path_conditions,
                    conclusion=conclusion,
                    confidence=confidence,
                    support=support,
                    feature_names=feature_names
                )
                
                rules.append(rule)
            else:
                # Internal node - continue recursion
                feature = tree.feature[node_id]
                threshold = tree.threshold[node_id]
                
                # Left child (feature <= threshold)
                left_path = path_conditions + [(feature, "<=", threshold)]
                extract_path(tree.children_left[node_id], left_path)
                
                # Right child (feature > threshold)
                right_path = path_conditions + [(feature, ">", threshold)]
                extract_path(tree.children_right[node_id], right_path)
                
        # Start recursion from the root
        extract_path(0, [])
        
        return rules


class DeepREDRuleExtractor(RuleExtractor):
    """
    Decompositional rule extraction using the DeepRED algorithm.
    
    DeepRED extracts rules by decomposing a neural network layer by layer,
    starting from the output layer and working backwards through the network.
    """
    
    def __init__(self, name: str = "DeepREDExtractor", 
                rule_type: RuleType = RuleType.PROPOSITIONAL,
                min_fidelity: float = 0.95, discretize_splits: int = 5):
        """
        Initialize the DeepRED rule extractor.
        
        Args:
            name: Name of the extractor
            rule_type: Type of rules to extract
            min_fidelity: Minimum fidelity threshold for layer-wise extraction
            discretize_splits: Number of discretization splits for continuous activations
        """
        super().__init__(name, rule_type)
        
        self.min_fidelity = min_fidelity
        self.discretize_splits = discretize_splits
        
        # Layer-wise rule sets
        self.layer_rules = {}
        
        # Layer names
        self.layer_names = []
        
        # Combined rules
        self.combined_rules = []
        
        # Layer-wise fidelity metrics
        self.layer_fidelity = {}
    
    def extract_rules(self, model: nn.Module, X: torch.Tensor, 
                     y: torch.Tensor, **kwargs) -> RuleSet:
        """
        Extract rules from a neural network using DeepRED algorithm.
        
        Args:
            model: Neural network model
            X: Input data
            y: Target labels or values
            **kwargs: Additional extraction parameters:
                - min_fidelity: Minimum fidelity threshold
                - discretize_splits: Number of discretization splits
                - feature_names: List of feature names
                - layer_names: List of layer names
            
        Returns:
            Extracted RuleSet
        """
        # Update parameters if provided
        self.min_fidelity = kwargs.get('min_fidelity', self.min_fidelity)
        self.discretize_splits = kwargs.get('discretize_splits', self.discretize_splits)
        
        # Set feature names if provided
        if 'feature_names' in kwargs:
            self.feature_names = kwargs['feature_names']
            
        # Set layer names if provided
        if 'layer_names' in kwargs:
            self.layer_names = kwargs['layer_names']
        else:
            # Generate layer names
            self.layer_names = [f"layer_{i}" for i in range(len(list(model.modules())))]
        
        # Extract layers from the model
        layers = self._extract_model_layers(model)
        
        # Get model outputs
        model.eval()
        with torch.no_grad():
            outputs = self._get_layer_outputs(model, X)
            y_pred = outputs[-1]
            
        # Extract rules layer by layer, starting from the output layer
        rules = []
        current_layer_idx = len(outputs) - 1
        
        # Process the output layer first
        output_rules = self._extract_layer_rules(
            inputs=outputs[-2],  # Second-to-last layer activations
            outputs=y_pred,
            targets=y,
            layer_idx=current_layer_idx
        )
        
        self.layer_rules[current_layer_idx] = output_rules
        rules.extend(output_rules)
        
        # Work backwards through the layers
        for layer_idx in range(len(outputs) - 2, 0, -1):
            # Extract rules for this layer
            layer_rules = self._extract_layer_rules(
                inputs=outputs[layer_idx - 1],  # Previous layer's activations
                outputs=outputs[layer_idx],     # Current layer's activations
                targets=None,                   # No direct targets for hidden layers
                layer_idx=layer_idx
            )
            
            self.layer_rules[layer_idx] = layer_rules
            
            # For each neuron in this layer, substitute its definition into the rules
            # that use it in the next layer
            rules = self._substitute_layer_rules(rules, layer_rules, layer_idx)
        
        # Store combined rules
        self.combined_rules = rules
        
        # Create final rule set
        rule_set = RuleSet(
            rules=rules,
            name=f"{self.name}_Rules",
            extraction_method=RuleExtractionMethod.DEEPRED
        )
        
        # Mark extraction as done
        self._has_extracted = True
        
        return rule_set
    
    def _extract_model_layers(self, model: nn.Module) -> List[nn.Module]:
        """
        Extract layers from the model.
        
        Args:
            model: Neural network model
            
        Returns:
            List of layers
        """
        layers = []
        
        # Recursively extract layers
        def add_module(module):
            # Skip container modules like Sequential
            if isinstance(module, nn.Sequential):
                for child in module.children():
                    add_module(child)
            # Only add layers with parameters or activation functions
            elif (isinstance(module, (nn.Linear, nn.Conv2d, nn.ReLU, nn.Sigmoid, nn.Tanh)) or
                 len(list(module.parameters())) > 0):
                layers.append(module)
                
        # Start recursion from the model
        for module in model.children():
            add_module(module)
            
        return layers
    
    def _get_layer_outputs(self, model: nn.Module, X: torch.Tensor) -> List[torch.Tensor]:
        """
        Get outputs from all layers of the model.
        
        Args:
            model: Neural network model
            X: Input data
            
        Returns:
            List of layer outputs
        """
        outputs = [X]  # Start with input
        activations = {}
        
        # Define hook function to capture activations
        def hook_fn(module, input, output):
            activations[module] = output
            
        # Register hooks for all modules
        hooks = []
        for module in model.modules():
            if isinstance(module, (nn.Linear, nn.Conv2d, nn.ReLU, nn.Sigmoid, nn.Tanh)):
                hooks.append(module.register_forward_hook(hook_fn))
                
        # Forward pass
        with torch.no_grad():
            model(X)
            
        # Collect activations
        for module in model.modules():
            if module in activations:
                # Flatten outputs for simplicity
                if activations[module].dim() > 2:
                    # For convolutional layers, flatten spatial dimensions
                    batch_size = activations[module].size(0)
                    act_flat = activations[module].view(batch_size, -1)
                    outputs.append(act_flat)
                else:
                    outputs.append(activations[module])
                    
        # Remove hooks
        for hook in hooks:
            hook.remove()
            
        return outputs
    
    def _discretize_activations(self, activations: torch.Tensor) -> torch.Tensor:
        """
        Discretize continuous activations into buckets.
        
        Args:
            activations: Continuous activation values
            
        Returns:
            Discretized activations
        """
        # Convert to numpy for easier processing
        act_np = activations.cpu().numpy()
        
        # Discretize each feature
        discretized = np.zeros_like(act_np)
        
        for j in range(act_np.shape[1]):
            feature = act_np[:, j]
            
            # Skip if all values are the same
            if np.max(feature) == np.min(feature):
                discretized[:, j] = 0
                continue
                
            # Create buckets
            buckets = np.linspace(
                np.min(feature),
                np.max(feature),
                self.discretize_splits + 1
            )
            
            # Assign each value to a bucket
            for i in range(len(feature)):
                # Find the bucket that this value falls into
                bucket_idx = np.digitize(feature[i], buckets) - 1
                # Ensure valid index
                bucket_idx = max(0, min(bucket_idx, self.discretize_splits - 1))
                discretized[i, j] = bucket_idx
                
        return torch.tensor(discretized, dtype=torch.int64)
    
    def _extract_layer_rules(self, inputs: torch.Tensor, outputs: torch.Tensor,
                          targets: Optional[torch.Tensor], layer_idx: int) -> List[Rule]:
        """
        Extract rules for a single layer.
        
        Args:
            inputs: Inputs to the layer
            outputs: Outputs from the layer
            targets: Targets (for output layer only)
            layer_idx: Layer index
            
        Returns:
            List of rules for this layer
        """
        # Extract rules for each neuron in the layer
        rules = []
        
        # Discretize inputs and outputs
        inputs_discrete = self._discretize_activations(inputs)
        
        # For the output layer
        if targets is not None:
            # Classification
            if targets.dim() > 1 and targets.size(1) > 1:
                # Multi-class - get predicted class
                outputs_discrete = torch.argmax(outputs, dim=1)
                num_classes = targets.size(1)
            else:
                # Binary or regression - threshold at 0.5
                outputs_np = outputs.cpu().numpy()
                outputs_discrete = torch.tensor((outputs_np > 0.5).astype(int))
                num_classes = 2
                
            # Extract rules for each class
            for class_idx in range(num_classes):
                # Create class-specific targets
                class_targets = (outputs_discrete == class_idx).int()
                
                # Train a decision tree
                tree = DecisionTreeClassifier(
                    max_depth=5,
                    min_samples_leaf=10
                )
                
                tree.fit(inputs_discrete.cpu().numpy(), class_targets.cpu().numpy())
                
                # Extract rules from the tree
                class_rules = self._extract_rules_from_tree(
                    tree,
                    inputs_discrete,
                    class_targets,
                    layer_idx=layer_idx,
                    class_idx=class_idx
                )
                
                rules.extend(class_rules)
        else:
            # Hidden layer - treat each neuron as a separate prediction task
            outputs_discrete = self._discretize_activations(outputs)
            
            # For each neuron
            for neuron_idx in range(outputs.size(1)):
                # Get outputs for this neuron
                neuron_outputs = outputs_discrete[:, neuron_idx]
                
                # Get unique values
                unique_values = torch.unique(neuron_outputs)
                
                # For each output value, create rules
                for value in unique_values:
                    # Create binary targets for this value
                    value_targets = (neuron_outputs == value).int()
                    
                    # Skip if all or no examples have this value
                    if value_targets.sum() == 0 or value_targets.sum() == len(value_targets):
                        continue
                        
                    # Train a decision tree
                    tree = DecisionTreeClassifier(
                        max_depth=3,  # Smaller trees for hidden neurons
                        min_samples_leaf=5
                    )
                    
                    tree.fit(inputs_discrete.cpu().numpy(), value_targets.cpu().numpy())
                    
                    # Extract rules from the tree
                    neuron_rules = self._extract_rules_from_tree(
                        tree,
                        inputs_discrete,
                        value_targets,
                        layer_idx=layer_idx,
                        neuron_idx=neuron_idx,
                        value=value.item()
                    )
                    
                    rules.extend(neuron_rules)
        
        # Compute fidelity for this layer
        if rules:
            fidelity = self._compute_layer_fidelity(rules, inputs_discrete, outputs_discrete)
            self.layer_fidelity[layer_idx] = fidelity
            
        return rules
    
    def _extract_rules_from_tree(self, tree: DecisionTreeClassifier, 
                              inputs: torch.Tensor, targets: torch.Tensor,
                              layer_idx: int, neuron_idx: Optional[int] = None,
                              class_idx: Optional[int] = None, 
                              value: Optional[int] = None) -> List[Rule]:
        """
        Extract rules from a decision tree for a specific neuron or class.
        
        Args:
            tree: Trained decision tree
            inputs: Input data
            targets: Target values
            layer_idx: Layer index
            neuron_idx: Neuron index (for hidden layers)
            class_idx: Class index (for output layer)
            value: Output value (for hidden layers)
            
        Returns:
            List of rules extracted from the tree
        """
        rules = []
        
        # Get tree structure
        tree_ = tree.tree_
        
        # Feature names
        feature_names = [f"h{layer_idx-1}_{i}" for i in range(inputs.size(1))]
        
        # Function to extract rules from a path
        def extract_path(node_id, path_conditions):
            # If leaf node, create rule
            if tree_.children_left[node_id] == tree_.children_right[node_id]:
                # Only create rule if it predicts the positive class
                if tree_.value[node_id][0, 1] > tree_.value[node_id][0, 0]:
                    # Create conclusion
                    if class_idx is not None:
                        # Output layer - class prediction
                        conclusion = Atom(f"class_{class_idx}")
                    else:
                        # Hidden layer - neuron activation
                        conclusion = Atom(f"h{layer_idx}_{neuron_idx}={value}")
                    
                    # Get rule support
                    support = int(tree_.value[node_id][0, 1])
                    
                    # Calculate confidence
                    total = tree_.value[node_id][0, 0] + tree_.value[node_id][0, 1]
                    confidence = tree_.value[node_id][0, 1] / total if total > 0 else 0.0
                    
                    # Skip low-confidence rules
                    if confidence < 0.5:
                        return
                        
                    # Create rule
                    rule = DecisionTreeRule(
                        path_conditions=path_conditions,
                        conclusion=conclusion,
                        confidence=confidence,
                        support=support,
                        feature_names=feature_names
                    )
                    
                    rules.append(rule)
            else:
                # Internal node - continue recursion
                feature = tree_.feature[node_id]
                threshold = tree_.threshold[node_id]
                
                # Left child (feature <= threshold)
                left_path = path_conditions + [(feature, "<=", threshold)]
                extract_path(tree_.children_left[node_id], left_path)
                
                # Right child (feature > threshold)
                right_path = path_conditions + [(feature, ">", threshold)]
                extract_path(tree_.children_right[node_id], right_path)
                
        # Start recursion from the root
        extract_path(0, [])
        
        return rules
    
    def _compute_layer_fidelity(self, rules: List[Rule], inputs: torch.Tensor, 
                             outputs: torch.Tensor) -> float:
        """
        Compute fidelity of the rules for a layer.
        
        Args:
            rules: List of rules
            inputs: Input data
            outputs: Target outputs
            
        Returns:
            Fidelity score
        """
        # Convert to numpy
        inputs_np = inputs.cpu().numpy()
        outputs_np = outputs.cpu().numpy()
        
        # For each sample, check if any rule applies and gives the correct prediction
        correct = 0
        covered = 0
        
        for i in range(len(inputs_np)):
            # Check each rule
            rule_triggered = False
            rule_correct = False
            
            for rule in rules:
                # Create interpretation for this sample
                interpretation = {}
                
                # If rule has feature_names, use them
                if hasattr(rule, 'feature_names') and rule.feature_names:
                    for j, name in enumerate(rule.feature_names):
                        if j < inputs_np.shape[1]:
                            interpretation[name] = inputs_np[i, j] > 0.5  # Binarize
                else:
                    # Use feature indices
                    for j in range(inputs_np.shape[1]):
                        interpretation[f"x[{j}]"] = inputs_np[i, j] > 0.5
                        
                # Check if rule premise is satisfied
                if rule.premise.evaluate(interpretation):
                    rule_triggered = True
                    
                    # Check if rule conclusion matches the output
                    conclusion_str = rule.conclusion.to_string()
                    
                    if conclusion_str.startswith("class_"):
                        # Output layer - check class prediction
                        class_idx = int(conclusion_str.split("_")[1])
                        if outputs_np.ndim > 1:
                            # Multi-class
                            rule_correct = class_idx == outputs_np[i]
                        else:
                            # Binary
                            rule_correct = (class_idx == 1 and outputs_np[i] > 0.5) or \
                                         (class_idx == 0 and outputs_np[i] <= 0.5)
                    elif "=" in conclusion_str:
                        # Hidden layer - check neuron activation
                        parts = conclusion_str.split("=")
                        neuron_idx = int(parts[0].split("_")[1])
                        value = int(parts[1])
                        
                        if outputs_np.ndim > 1:
                            rule_correct = outputs_np[i, neuron_idx] == value
                        else:
                            rule_correct = outputs_np[i] == value
                    
                    # Stop after first triggered rule
                    break
            
            if rule_triggered:
                covered += 1
                if rule_correct:
                    correct += 1
                    
        # Compute fidelity
        fidelity = correct / covered if covered > 0 else 0.0
        
        return fidelity
    
    def _substitute_layer_rules(self, current_rules: List[Rule], 
                              layer_rules: List[Rule], layer_idx: int) -> List[Rule]:
        """
        Substitute rules for neurons in layer_idx into rules that use them in later layers.
        
        Args:
            current_rules: Current set of rules for later layers
            layer_rules: Rules for the current layer
            layer_idx: Layer index
            
        Returns:
            Updated rules after substitution
        """
        # Group layer rules by neuron and value
        neuron_rules = defaultdict(list)
        
        for rule in layer_rules:
            conclusion_str = rule.conclusion.to_string()
            
            # Check if this is a neuron rule
            if "=" in conclusion_str:
                parts = conclusion_str.split("=")
                neuron_id = parts[0]
                value = int(parts[1])
                
                neuron_rules[(neuron_id, value)].append(rule)
                
        # For each current rule, substitute neuron references
        new_rules = []
        
        for rule in current_rules:
            # Check if this rule references neurons in the current layer
            premise_str = rule.premise.to_string()
            neurons_to_substitute = []
            
            # Look for h{layer_idx}_{neuron_idx} patterns
            for neuron_id, value in neuron_rules.keys():
                if neuron_id in premise_str:
                    neurons_to_substitute.append((neuron_id, value))
                    
            if not neurons_to_substitute:
                # No substitution needed
                new_rules.append(rule)
                continue
                
            # Don't substitute if too many neurons referenced (would explode rule complexity)
            if len(neurons_to_substitute) > 3:
                new_rules.append(rule)
                continue
                
            # Perform substitution for the first neuron
            for neuron_id, value in neurons_to_substitute:
                # Get rules for this neuron and value
                sub_rules = neuron_rules[(neuron_id, value)]
                
                if not sub_rules:
                    continue
                    
                # For each rule that defines the neuron
                for sub_rule in sub_rules:
                    # Create a new rule that substitutes the neuron reference
                    # New premise: replace neuron atom with the sub_rule's premise
                    new_premise_str = premise_str.replace(
                        f"{neuron_id}={value}", 
                        f"({sub_rule.premise.to_string()})"
                    )
                    
                    try:
                        # Parse the new premise string
                        new_premise = self.parser.parse(new_premise_str)
                        
                        # Create the new rule
                        new_rule = Rule(
                            premise=new_premise,
                            conclusion=rule.conclusion,
                            confidence=rule.confidence * sub_rule.confidence,
                            support=min(rule.support, sub_rule.support)
                        )
                        
                        new_rules.append(new_rule)
                    except Exception as e:
                        # If parsing fails, keep the original rule
                        logger.warning(f"Failed to substitute neuron rule: {e}")
                        new_rules.append(rule)
                        
                # Stop after substituting the first neuron (to avoid combinatorial explosion)
                break
                
        return new_rules


class TREPANRuleExtractor(RuleExtractor):
    """
    Pedagogical rule extraction using the TREPAN algorithm.
    
    TREPAN builds a decision tree to approximate the neural network's behavior,
    using queries to the network to guide the tree construction and to generate
    synthetic examples in regions of the input space where more data is needed.
    """
    
    def __init__(self, name: str = "TREPANExtractor", 
                rule_type: RuleType = RuleType.PROPOSITIONAL,
                max_tree_size: int = 100, min_examples: int = 1000):
        """
        Initialize the TREPAN rule extractor.
        
        Args:
            name: Name of the extractor
            rule_type: Type of rules to extract
            max_tree_size: Maximum number of internal nodes in the tree
            min_examples: Minimum number of examples per node
        """
        super().__init__(name, rule_type)
        
        self.max_tree_size = max_tree_size
        self.min_examples = min_examples
        
        # Decision tree
        self.tree = None
        
        # Number of internal nodes
        self.tree_size = 0
        
        # Oracle model (the neural network)
        self.oracle = None
        
        # Number of features
        self.num_features = 0
        
        # Feature distributions for generating queries
        self.feature_distributions = []
        
        # Feature importance scores
        self.feature_importance = {}
    
    def extract_rules(self, model: nn.Module, X: torch.Tensor, 
                     y: torch.Tensor, **kwargs) -> RuleSet:
        """
        Extract rules from a neural network using TREPAN algorithm.
        
        Args:
            model: Neural network model
            X: Input data
            y: Target labels or values
            **kwargs: Additional extraction parameters:
                - max_tree_size: Maximum number of nodes in the tree
                - min_examples: Minimum number of examples per node
                - feature_names: List of feature names
                - sample_use_oracle: Whether to use oracle for sampling
            
        Returns:
            Extracted RuleSet
        """
        # Update parameters if provided
        self.max_tree_size = kwargs.get('max_tree_size', self.max_tree_size)
        self.min_examples = kwargs.get('min_examples', self.min_examples)
        
        # Set feature names if provided
        if 'feature_names' in kwargs:
            self.feature_names = kwargs['feature_names']
            
        # Set oracle
        self.oracle = model
        self.oracle.eval()
        
        # Set number of features
        self.num_features = X.size(1)
        
        # Compute feature distributions for generating queries
        self._compute_feature_distributions(X)
        
        # Extract feature importance
        self._extract_feature_importance(model, X, y)
        
        # Get oracle predictions for training data
        with torch.no_grad():
            oracle_outputs = model(X)
            
        # For classification, get class predictions
        if oracle_outputs.dim() > 1 and oracle_outputs.size(1) > 1:
            # Multi-class classification
            oracle_labels = torch.argmax(oracle_outputs, dim=1)
            num_classes = oracle_outputs.size(1)
            task_type = 'classification'
        elif torch.unique(y).size(0) <= 10:  # Arbitrary threshold for classification
            # Binary classification
            oracle_labels = (oracle_outputs > 0.5).int().squeeze()
            num_classes = 2
            task_type = 'classification'
        else:
            # Regression
            oracle_labels = oracle_outputs.squeeze()
            num_classes = 1
            task_type = 'regression'
            
        # Initialize the tree
        self.tree = self._init_tree(task_type, num_classes)
        
        # Build the tree using TREPAN algorithm
        rules = self._build_trepan_tree(
            X, oracle_labels, task_type, num_classes,
            sample_use_oracle=kwargs.get('sample_use_oracle', True)
        )
        
        # Create rule set
        rule_set = RuleSet(
            rules=rules,
            name=f"{self.name}_Rules",
            extraction_method=RuleExtractionMethod.TREPAN
        )
        
        # Mark extraction as done
        self._has_extracted = True
        
        return rule_set
    
    def _compute_feature_distributions(self, X: torch.Tensor) -> None:
        """
        Compute feature distributions for generating queries.
        
        Args:
            X: Input data
        """
        # Convert to numpy
        X_np = X.cpu().numpy()
        
        # For each feature, compute distribution
        self.feature_distributions = []
        
        for i in range(X_np.shape[1]):
            feature = X_np[:, i]
            
            # Check if feature is approximately binary
            unique_vals = np.unique(feature)
            if len(unique_vals) <= 2 or (len(unique_vals) <= 5 and np.all(np.mod(unique_vals, 1) == 0)):
                # Discrete feature - use empirical distribution
                values, counts = np.unique(feature, return_counts=True)
                probs = counts / counts.sum()
                self.feature_distributions.append({
                    'type': 'discrete',
                    'values': values,
                    'probs': probs
                })
            else:
                # Continuous feature - use a normal distribution
                mean = np.mean(feature)
                std = np.std(feature)
                self.feature_distributions.append({
                    'type': 'continuous',
                    'mean': mean,
                    'std': std,
                    'min': np.min(feature),
                    'max': np.max(feature)
                })
    
    def _extract_feature_importance(self, model: nn.Module, X: torch.Tensor, 
                                 y: torch.Tensor) -> None:
        """
        Extract feature importance from the model.
        
        Args:
            model: Neural network model
            X: Input data
            y: Target labels or values
        """
        # Use SHAP values for feature importance
        try:
            # Convert to numpy
            X_np = X.cpu().numpy()
            
            # Create a background dataset (subset of training data)
            bg_indices = np.random.choice(len(X_np), min(100, len(X_np)), replace=False)
            background = X_np[bg_indices]
            
            # Create a wrapper for the PyTorch model
            def model_fn(x):
                x_tensor = torch.tensor(x, dtype=torch.float32)
                with torch.no_grad():
                    return model(x_tensor).cpu().numpy()
                    
            # Create a SHAP explainer
            explainer = shap.KernelExplainer(model_fn, background)
            
            # Compute SHAP values for a subset of instances
            sample_indices = np.random.choice(len(X_np), min(50, len(X_np)), replace=False)
            shap_values = explainer.shap_values(X_np[sample_indices])
            
            # Average absolute SHAP values for each feature
            if isinstance(shap_values, list):
                # Multi-class - take the mean across classes
                shap_values = np.mean([np.abs(sv) for sv in shap_values], axis=0)
            else:
                shap_values = np.abs(shap_values)
                
            feature_importance = np.mean(shap_values, axis=0)
            
            # Normalize
            feature_importance = feature_importance / np.sum(feature_importance)
            
            # Store feature importance
            for i in range(len(feature_importance)):
                feature_name = self.feature_names[i] if self.feature_names and i < len(self.feature_names) else f"feature_{i}"
                self.feature_importance[feature_name] = feature_importance[i]
                
        except Exception as e:
            logger.warning(f"Failed to compute SHAP values: {e}")
            
            # Fallback to uniform importance
            for i in range(self.num_features):
                feature_name = self.feature_names[i] if self.feature_names and i < len(self.feature_names) else f"feature_{i}"
                self.feature_importance[feature_name] = 1.0 / self.num_features
    
    def _init_tree(self, task_type: str, num_classes: int) -> Dict:
        """
        Initialize the decision tree structure.
        
        Args:
            task_type: 'classification' or 'regression'
            num_classes: Number of classes (for classification)
            
        Returns:
            Root node of the tree
        """
        # Create root node
        root = {
            'id': 0,
            'samples': self.min_examples,
            'constraints': [],
            'children': [],
            'feature': None,
            'threshold': None,
            'is_leaf': False,
            'class_distribution': None,  # For classification
            'value': None,              # For regression
            'priority': 0.0             # Priority for expansion
        }
        
        return root
    
    def _build_trepan_tree(self, X: torch.Tensor, y: torch.Tensor, 
                         task_type: str, num_classes: int,
                         sample_use_oracle: bool = True) -> List[Rule]:
        """
        Build a decision tree using the TREPAN algorithm.
        
        Args:
            X: Input data
            y: Oracle labels
            task_type: 'classification' or 'regression'
            num_classes: Number of classes (for classification)
            sample_use_oracle: Whether to use oracle for sampling
            
        Returns:
            List of extracted rules
        """
        # Convert to numpy
        X_np = X.cpu().numpy()
        y_np = y.cpu().numpy()
        
        # Create a priority queue for nodes
        queue = [(0.0, self.tree)]  # (priority, node)
        
        # Keep track of internal nodes
        self.tree_size = 0
        
        # Assign training examples to root
        root_examples = list(range(len(X_np)))
        
        # Initialize root's class distribution or value
        if task_type == 'classification':
            # Class distribution at root
            class_counts = np.bincount(y_np[root_examples], minlength=num_classes)
            self.tree['class_distribution'] = class_counts / np.sum(class_counts)
            
            # Set priority for expansion (impurity)
            self.tree['priority'] = self._compute_entropy(self.tree['class_distribution'])
            
            # Update priority queue
            heapq.heappush(queue, (-self.tree['priority'], self.tree))
        else:
            # Mean value at root
            self.tree['value'] = np.mean(y_np[root_examples])
            
            # Set priority for expansion (variance)
            self.tree['priority'] = np.var(y_np[root_examples])
            
            # Update priority queue
            heapq.heappush(queue, (-self.tree['priority'], self.tree))
            
        # Main TREPAN loop
        while queue and self.tree_size < self.max_tree_size:
            # Get node with highest priority
            _, node = heapq.heappop(queue)
            
            # Skip if already a leaf
            if node['is_leaf']:
                continue
                
            # Gather examples that reach this node
            node_examples = self._get_examples_at_node(X_np, node['constraints'])
            
            # If not enough examples, generate more examples
            if len(node_examples) < self.min_examples and sample_use_oracle:
                # Generate examples that satisfy the constraints
                gen_examples = self._generate_examples(node['constraints'], self.min_examples - len(node_examples))
                
                # Query oracle for labels
                with torch.no_grad():
                    gen_X = torch.tensor(gen_examples, dtype=torch.float32)
                    gen_y = self.oracle(gen_X)
                    
                    # Process outputs
                    if task_type == 'classification':
                        if gen_y.dim() > 1 and gen_y.size(1) > 1:
                            gen_y = torch.argmax(gen_y, dim=1)
                        else:
                            gen_y = (gen_y > 0.5).int().squeeze()
                    else:
                        gen_y = gen_y.squeeze()
                        
                    gen_y = gen_y.cpu().numpy()
                    
                # Add generated examples to node_examples
                node_examples = np.vstack([X_np[node_examples], gen_examples])
                node_y = np.concatenate([y_np[node_examples[:len(X_np)]], gen_y])
            else:
                # Use existing examples
                node_y = y_np[node_examples]
                
            # Check stopping criteria
            if self._should_stop_splitting(node, node_examples, node_y, task_type):
                # Make this a leaf node
                node['is_leaf'] = True
                
                if task_type == 'classification':
                    # Update class distribution
                    class_counts = np.bincount(node_y, minlength=num_classes)
                    node['class_distribution'] = class_counts / np.sum(class_counts)
                else:
                    # Update regression value
                    node['value'] = np.mean(node_y)
                    
                continue
                
            # Find the best split
            feature, threshold, impurity, splits = self._find_best_split(
                node_examples, node_y, task_type, num_classes
            )
            
            if feature is None:
                # No valid split found - make this a leaf
                node['is_leaf'] = True
                continue
                
            # Apply the split
            node['feature'] = feature
            node['threshold'] = threshold
            
            # Create child nodes
            left_child = {
                'id': 2 * node['id'] + 1,
                'samples': len(splits[0]),
                'constraints': node['constraints'] + [(feature, "<=", threshold)],
                'children': [],
                'feature': None,
                'threshold': None,
                'is_leaf': False,
                'class_distribution': None,
                'value': None,
                'priority': 0.0
            }
            
            right_child = {
                'id': 2 * node['id'] + 2,
                'samples': len(splits[1]),
                'constraints': node['constraints'] + [(feature, ">", threshold)],
                'children': [],
                'feature': None,
                'threshold': None,
                'is_leaf': False,
                'class_distribution': None,
                'value': None,
                'priority': 0.0
            }
            
            # Set child class distributions or values
            if task_type == 'classification':
                # Class distributions
                left_counts = np.bincount(node_y[splits[0]], minlength=num_classes)
                right_counts = np.bincount(node_y[splits[1]], minlength=num_classes)
                
                left_child['class_distribution'] = left_counts / np.sum(left_counts) if np.sum(left_counts) > 0 else np.ones(num_classes) / num_classes
                right_child['class_distribution'] = right_counts / np.sum(right_counts) if np.sum(right_counts) > 0 else np.ones(num_classes) / num_classes
                
                # Set priorities (impurity weighted by sample size)
                left_child['priority'] = self._compute_entropy(left_child['class_distribution']) * len(splits[0]) / len(node_examples)
                right_child['priority'] = self._compute_entropy(right_child['class_distribution']) * len(splits[1]) / len(node_examples)
            else:
                # Regression values
                left_child['value'] = np.mean(node_y[splits[0]]) if len(splits[0]) > 0 else node['value']
                right_child['value'] = np.mean(node_y[splits[1]]) if len(splits[1]) > 0 else node['value']
                
                # Set priorities (variance weighted by sample size)
                left_child['priority'] = np.var(node_y[splits[0]]) * len(splits[0]) / len(node_examples) if len(splits[0]) > 0 else 0.0
                right_child['priority'] = np.var(node_y[splits[1]]) * len(splits[1]) / len(node_examples) if len(splits[1]) > 0 else 0.0
                
            # Add children to node
            node['children'] = [left_child, right_child]
            
            # Add children to queue
            heapq.heappush(queue, (-left_child['priority'], left_child))
            heapq.heappush(queue, (-right_child['priority'], right_child))
            
            # Increment tree size
            self.tree_size += 1
            
        # Extract rules from the completed tree
        rules = self._extract_rules_from_tree(self.tree, task_type, num_classes)
        
        return rules
    
    def _get_examples_at_node(self, X: np.ndarray, constraints: List[Tuple]) -> np.ndarray:
        """
        Get indices of examples that satisfy the constraints at a node.
        
        Args:
            X: Input data
            constraints: List of (feature, operator, threshold) constraints
            
        Returns:
            Indices of examples that satisfy the constraints
        """
        # Start with all examples
        mask = np.ones(len(X), dtype=bool)
        
        # Apply each constraint
        for feature, operator, threshold in constraints:
            if operator == "<=":
                mask = mask & (X[:, feature] <= threshold)
            elif operator == ">":
                mask = mask & (X[:, feature] > threshold)
                
        return np.where(mask)[0]
    
    def _generate_examples(self, constraints: List[Tuple], n_samples: int) -> np.ndarray:
        """
        Generate examples satisfying the constraints.
        
        Args:
            constraints: List of (feature, operator, threshold) constraints
            n_samples: Number of examples to generate
            
        Returns:
            Generated examples
        """
        # Generate candidates until we have enough examples
        examples = []
        attempts = 0
        max_attempts = n_samples * 10  # Avoid infinite loop
        
        while len(examples) < n_samples and attempts < max_attempts:
            # Generate random example based on feature distributions
            example = np.zeros(self.num_features)
            
            for i in range(self.num_features):
                dist = self.feature_distributions[i]
                
                if dist['type'] == 'discrete':
                    # Sample from discrete distribution
                    example[i] = np.random.choice(dist['values'], p=dist['probs'])
                else:
                    # Sample from normal distribution, within min/max bounds
                    value = np.random.normal(dist['mean'], dist['std'])
                    example[i] = np.clip(value, dist['min'], dist['max'])
                    
            # Check if example satisfies all constraints
            satisfies = True
            
            for feature, operator, threshold in constraints:
                if operator == "<=" and example[feature] > threshold:
                    satisfies = False
                    break
                elif operator == ">" and example[feature] <= threshold:
                    satisfies = False
                    break
                    
            if satisfies:
                examples.append(example)
                
            attempts += 1
            
        # Return generated examples
        if examples:
            return np.array(examples)
        else:
            # If we couldn't generate enough examples, return empty array
            return np.zeros((0, self.num_features))
    
    def _should_stop_splitting(self, node: Dict, examples: np.ndarray, 
                            targets: np.ndarray, task_type: str) -> bool:
        """
        Check if we should stop splitting a node.
        
        Args:
            node: Current node
            examples: Examples at this node
            targets: Target values for the examples
            task_type: 'classification' or 'regression'
            
        Returns:
            True if we should stop splitting, False otherwise
        """
        # Stop if no examples
        if len(examples) == 0:
            return True
            
        # Stop if all examples have the same target
        if np.all(targets == targets[0]):
            return True
            
        # Stop if we've reached the maximum tree size
        if self.tree_size >= self.max_tree_size:
            return True
            
        # Stop if node's priority is too low
        if node['priority'] < 0.01:
            return True
            
        # Stop if too few examples
        if len(examples) < 5:
            return True
            
        return False
    
    def _find_best_split(self, examples: np.ndarray, targets: np.ndarray, 
                       task_type: str, num_classes: int) -> Tuple:
        """
        Find the best feature and threshold to split on.
        
        Args:
            examples: Examples at this node
            targets: Target values for the examples
            task_type: 'classification' or 'regression'
            num_classes: Number of classes (for classification)
            
        Returns:
            Tuple of (feature, threshold, impurity_improvement, splits)
        """
        # If no examples, return None
        if len(examples) == 0:
            return None, None, 0.0, ([], [])
            
        best_feature = None
        best_threshold = None
        best_impurity = float('inf')
        best_splits = None
        
        # Compute parent impurity
        if task_type == 'classification':
            # Compute class distribution
            class_counts = np.bincount(targets, minlength=num_classes)
            class_probs = class_counts / np.sum(class_counts)
            
            # Compute entropy
            parent_impurity = self._compute_entropy(class_probs)
        else:
            # Compute variance
            parent_impurity = np.var(targets)
            
        # Iterate over features, weighted by importance
        feature_indices = list(range(examples.shape[1]))
        
        # Sort features by importance (descending)
        if self.feature_importance:
            feature_indices.sort(key=lambda i: self.feature_importance.get(
                self.feature_names[i] if self.feature_names and i < len(self.feature_names) else f"feature_{i}",
                0.0
            ), reverse=True)
            
        # Limit to top N features for efficiency
        feature_indices = feature_indices[:min(10, len(feature_indices))]
        
        for feature in feature_indices:
            # Get unique values for this feature
            values = np.unique(examples[:, feature])
            
            # Skip if only one value
            if len(values) <= 1:
                continue
                
            # Compute potential thresholds (midpoints between values)
            thresholds = (values[:-1] + values[1:]) / 2
            
            for threshold in thresholds:
                # Split examples
                left_mask = examples[:, feature] <= threshold
                right_mask = ~left_mask
                
                # Ensure both splits have some examples
                if np.sum(left_mask) == 0 or np.sum(right_mask) == 0:
                    continue
                    
                # Compute impurity for each split
                if task_type == 'classification':
                    # Compute class distributions
                    left_counts = np.bincount(targets[left_mask], minlength=num_classes)
                    right_counts = np.bincount(targets[right_mask], minlength=num_classes)
                    
                    left_probs = left_counts / np.sum(left_counts)
                    right_probs = right_counts / np.sum(right_counts)
                    
                    # Compute entropy
                    left_impurity = self._compute_entropy(left_probs)
                    right_impurity = self._compute_entropy(right_probs)
                    
                    # Compute weighted impurity
                    weighted_impurity = (np.sum(left_mask) * left_impurity + 
                                       np.sum(right_mask) * right_impurity) / len(targets)
                else:
                    # Compute variance
                    left_impurity = np.var(targets[left_mask]) if np.sum(left_mask) > 1 else 0.0
                    right_impurity = np.var(targets[right_mask]) if np.sum(right_mask) > 1 else 0.0
                    
                    # Compute weighted impurity
                    weighted_impurity = (np.sum(left_mask) * left_impurity + 
                                       np.sum(right_mask) * right_impurity) / len(targets)
                    
                # Update best split if this is better
                if weighted_impurity < best_impurity:
                    best_feature = feature
                    best_threshold = threshold
                    best_impurity = weighted_impurity
                    best_splits = (np.where(left_mask)[0], np.where(right_mask)[0])
                    
        # Return the best split
        return best_feature, best_threshold, parent_impurity - best_impurity, best_splits
    
    def _compute_entropy(self, probs: np.ndarray) -> float:
        """
        Compute entropy of a probability distribution.
        
        Args:
            probs: Probability distribution
            
        Returns:
            Entropy value
        """
        # Filter out zeros to avoid log(0)
        probs = probs[probs > 0]
        
        # Compute entropy
        return -np.sum(probs * np.log2(probs))
    
    def _extract_rules_from_tree(self, node: Dict, task_type: str, 
                              num_classes: int) -> List[Rule]:
        """
        Extract rules from the decision tree.
        
        Args:
            node: Root node of the tree
            task_type: 'classification' or 'regression'
            num_classes: Number of classes (for classification)
            
        Returns:
            List of extracted rules
        """
        rules = []
        
        # Function to extract rules from a path
        def extract_rules_from_path(node, path_constraints):
            # If leaf node, create rule
            if node['is_leaf']:
                # Create conclusion
                if task_type == 'classification':
                    # Get majority class
                    if node['class_distribution'] is not None:
                        class_idx = np.argmax(node['class_distribution'])
                        conclusion = Atom(f"class_{class_idx}")
                        
                        # Get confidence (probability of majority class)
                        confidence = node['class_distribution'][class_idx]
                    else:
                        # Default to class 0
                        conclusion = Atom("class_0")
                        confidence = 1.0
                else:
                    # Regression - use mean value
                    if node['value'] is not None:
                        value = node['value']
                        conclusion = Atom(f"{value:.4f}")
                        
                        # Use a default confidence for regression
                        confidence = 0.8
                    else:
                        # Default to 0
                        conclusion = Atom("0.0")
                        confidence = 1.0
                        
                # Get rule support
                support = node['samples']
                
                # Create rule
                rule = DecisionTreeRule(
                    path_conditions=path_constraints,
                    conclusion=conclusion,
                    confidence=confidence,
                    support=support,
                    feature_names=self.feature_names
                )
                
                rules.append(rule)
            else:
                # Internal node - continue recursion
                if len(node['children']) == 2:
                    # Left child (feature <= threshold)
                    left_constraints = path_constraints + [(node['feature'], "<=", node['threshold'])]
                    extract_rules_from_path(node['children'][0], left_constraints)
                    
                    # Right child (feature > threshold)
                    right_constraints = path_constraints + [(node['feature'], ">", node['threshold'])]
                    extract_rules_from_path(node['children'][1], right_constraints)
                    
        # Start recursion from the root
        extract_rules_from_path(node, [])
        
        return rules


class MofNRuleExtractor(RuleExtractor):
    """
    Extracts M-of-N rules from a neural network.
    
    M-of-N rules have the form "if M out of N conditions are true, then the conclusion is true",
    which is a good approximation for the behavior of neural network nodes.
    """
    
    def __init__(self, name: str = "MofNExtractor", 
                rule_type: RuleType = RuleType.M_OF_N,
                min_fidelity: float = 0.95, max_conditions: int = 10):
        """
        Initialize the M-of-N rule extractor.
        
        Args:
            name: Name of the extractor
            rule_type: Type of rules to extract
            min_fidelity: Minimum fidelity threshold for rule extraction
            max_conditions: Maximum number of conditions in an M-of-N rule
        """
        super().__init__(name, rule_type)
        
        self.min_fidelity = min_fidelity
        self.max_conditions = max_conditions
        
        # Extracted rules for each hidden unit
        self.hidden_unit_rules = {}
        
        # Extracted rules for each output unit
        self.output_unit_rules = {}
        
        # Weight matrices
        self.weights = {}
        
        # Activation functions
        self.activation_fns = {}
        
        # Hidden activations
        self.hidden_activations = {}
        
        # Activation thresholds
        self.activation_thresholds = {}
    
    def extract_rules(self, model: nn.Module, X: torch.Tensor, 
                     y: torch.Tensor, **kwargs) -> RuleSet:
        """
        Extract M-of-N rules from a neural network.
        
        Args:
            model: Neural network model
            X: Input data
            y: Target labels or values
            **kwargs: Additional extraction parameters:
                - min_fidelity: Minimum fidelity threshold
                - max_conditions: Maximum number of conditions in a rule
                - feature_names: List of feature names
                - hidden_layer_indices: List of hidden layer indices to extract rules from
            
        Returns:
            Extracted RuleSet
        """
        # Update parameters if provided
        self.min_fidelity = kwargs.get('min_fidelity', self.min_fidelity)
        self.max_conditions = kwargs.get('max_conditions', self.max_conditions)
        
        # Set feature names if provided
        if 'feature_names' in kwargs:
            self.feature_names = kwargs['feature_names']
            
        # Extract weights and activations from the model
        self._extract_network_structure(model)
        
        # Compute hidden activations for input data
        self._compute_hidden_activations(model, X)
        
        # Determine activation thresholds for hidden units
        self._determine_activation_thresholds()
        
        # Extract rules for hidden units
        self._extract_hidden_unit_rules(X)
        
        # Extract rules for output units
        self._extract_output_unit_rules(model, X, y)
        
        # Combine rules into a rule set
        rules = self._combine_rules()
        
        # Create rule set
        rule_set = RuleSet(
            rules=rules,
            name=f"{self.name}_Rules",
            extraction_method=RuleExtractionMethod.M_OF_N
        )
        
        # Mark extraction as done
        self._has_extracted = True
        
        return rule_set
    
    def _extract_network_structure(self, model: nn.Module) -> None:
        """
        Extract weights and activation functions from the neural network.
        
        Args:
            model: Neural network model
        """
        # Reset states
        self.weights = {}
        self.activation_fns = {}
        
        # Function to process a module and its children
        def process_module(module, path=""):
            # Process Linear layers (weight matrices)
            if isinstance(module, nn.Linear):
                layer_name = f"{path}.{module.__class__.__name__}" if path else module.__class__.__name__
                self.weights[layer_name] = {
                    'weight': module.weight.detach().cpu().numpy(),
                    'bias': module.bias.detach().cpu().numpy() if module.bias is not None else None
                }
            
            # Process activation functions
            elif isinstance(module, (nn.ReLU, nn.Sigmoid, nn.Tanh)):
                layer_name = f"{path}.{module.__class__.__name__}" if path else module.__class__.__name__
                self.activation_fns[layer_name] = module.__class__.__name__
                
            # Process children recursively
            for name, child in module.named_children():
                child_path = f"{path}.{name}" if path else name
                process_module(child, child_path)
                
        # Start processing from the model
        process_module(model)
        
        # Sort layers by name for ordered processing
        self.weight_layers = sorted(self.weights.keys())
        self.activation_layers = sorted(self.activation_fns.keys())
    
    def _compute_hidden_activations(self, model: nn.Module, X: torch.Tensor) -> None:
        """
        Compute activations of hidden units for the input data.
        
        Args:
            model: Neural network model
            X: Input data
        """
        # Reset hidden activations
        self.hidden_activations = {}
        
        # Register hooks to capture activations
        activations = {}
        hooks = []
        
        def hook_fn(module, input, output):
            activations[module] = output
            
        for name, module in model.named_modules():
            if isinstance(module, (nn.Linear, nn.ReLU, nn.Sigmoid, nn.Tanh)):
                hooks.append(module.register_forward_hook(hook_fn))
                
        # Forward pass
        model.eval()
        with torch.no_grad():
            model(X)
            
        # Store activations
        for name, module in model.named_modules():
            if module in activations:
                layer_name = f"{name}"
                
                # Flatten activations for multi-dimensional outputs
                if activations[module].dim() > 2:
                    batch_size = activations[module].size(0)
                    act_flat = activations[module].view(batch_size, -1)
                    self.hidden_activations[layer_name] = act_flat.detach().cpu().numpy()
                else:
                    self.hidden_activations[layer_name] = activations[module].detach().cpu().numpy()
                    
        # Remove hooks
        for hook in hooks:
            hook.remove()
    
    def _determine_activation_thresholds(self) -> None:
        """
        Determine activation thresholds for hidden units.
        
        This is used to decide when a hidden unit is considered "active" for rule extraction.
        """
        # Reset thresholds
        self.activation_thresholds = {}
        
        # For each hidden layer
        for layer_name, activations in self.hidden_activations.items():
            # Skip output layer
            if layer_name == list(self.hidden_activations.keys())[-1]:
                continue
                
            # Compute thresholds for each unit
            unit_thresholds = []
            
            for unit_idx in range(activations.shape[1]):
                # Get activations for this unit
                unit_activations = activations[:, unit_idx]
                
                # Find activation threshold (midpoint between min and max)
                min_activation = np.min(unit_activations)
                max_activation = np.max(unit_activations)
                
                # If activations are all the same, default to 0.5
                if min_activation == max_activation:
                    threshold = 0.5
                else:
                    # For ReLU, use a small positive value
                    if layer_name in self.activation_fns and self.activation_fns[layer_name] == "ReLU":
                        threshold = max(0.1, (min_activation + max_activation) / 4)
                    # For sigmoid, use 0.5
                    elif layer_name in self.activation_fns and self.activation_fns[layer_name] == "Sigmoid":
                        threshold = 0.5
                    # For tanh, use 0.0
                    elif layer_name in self.activation_fns and self.activation_fns[layer_name] == "Tanh":
                        threshold = 0.0
                    else:
                        # Default: use midpoint
                        threshold = (min_activation + max_activation) / 2
                        
                unit_thresholds.append(threshold)
                
            self.activation_thresholds[layer_name] = np.array(unit_thresholds)
    
    def _extract_hidden_unit_rules(self, X: torch.Tensor) -> None:
        """
        Extract M-of-N rules for hidden units.
        
        Args:
            X: Input data
        """
        # Reset hidden unit rules
        self.hidden_unit_rules = {}
        
        # Get input data as numpy
        X_np = X.cpu().numpy()
        
        # Function to create input conditions
        def create_input_conditions(weights, bias, input_dim):
            # Create conditions for each input feature
            conditions = []
            
            for i in range(input_dim):
                # Get feature name
                if self.feature_names and i < len(self.feature_names):
                    feature_name = self.feature_names[i]
                else:
                    feature_name = f"x[{i}]"
                    
                # Determine condition based on weight
                if weights[i] > 0:
                    # Positive weight: feature > 0
                    conditions.append(Atom(f"{feature_name} > 0"))
                elif weights[i] < 0:
                    # Negative weight: feature <= 0
                    conditions.append(Atom(f"{feature_name} <= 0"))
                    
            return conditions
        
        # Process each hidden layer
        for layer_idx, layer_name in enumerate(self.weight_layers):
            # Skip if not a weight layer
            if layer_name not in self.weights:
                continue
                
            # Get layer weights and bias
            weights = self.weights[layer_name]['weight']
            bias = self.weights[layer_name]['bias']
            
            # Get activations for this layer
            if layer_name in self.hidden_activations:
                activations = self.hidden_activations[layer_name]
            else:
                # Skip if no activations
                continue
                
            # Get activation thresholds
            if layer_name in self.activation_thresholds:
                thresholds = self.activation_thresholds[layer_name]
            else:
                # Default thresholds
                thresholds = np.ones(weights.shape[0]) * 0.5
                
            # Extract rules for each unit in the layer
            layer_rules = []
            
            for unit_idx in range(weights.shape[0]):
                # Get weights for this unit
                unit_weights = weights[unit_idx]
                unit_bias = bias[unit_idx] if bias is not None else 0.0
                
                # Get activations for this unit
                unit_activations = activations[:, unit_idx]
                
                # Get threshold for this unit
                threshold = thresholds[unit_idx]
                
                # Create binary labels based on threshold
                is_active = unit_activations > threshold
                
                # Skip if all or none are active
                if np.all(is_active) or np.all(~is_active):
                    continue
                    
                # Create input conditions
                conditions = create_input_conditions(unit_weights, unit_bias, weights.shape[1])
                
                # Skip if no conditions
                if not conditions:
                    continue
                    
                # Limit number of conditions
                if len(conditions) > self.max_conditions:
                    # Keep only the top conditions based on weight magnitude
                    sorted_indices = np.argsort(-np.abs(unit_weights))
                    top_indices = sorted_indices[:self.max_conditions]
                    conditions = [conditions[i] for i in top_indices if i < len(conditions)]
                    
                # Determine M for M-of-N rule
                # Start with a reasonable guess based on bias and weights
                pos_weights = np.sum(unit_weights > 0)
                neg_weights = np.sum(unit_weights < 0)
                
                # Adjust M based on bias
                if unit_bias >= 0:
                    # Positive bias: start with a lower M
                    initial_m = max(1, int(pos_weights / 2))
                else:
                    # Negative bias: start with a higher M
                    initial_m = max(1, pos_weights - int(neg_weights / 2))
                    
                # Limit to number of conditions
                initial_m = min(initial_m, len(conditions))
                
                # Search for the best M value
                best_m = initial_m
                best_accuracy = 0.0
                
                for m in range(1, len(conditions) + 1):
                    # Create M-of-N rule
                    rule = MofNRule(
                        conditions=conditions,
                        conclusion=Atom(f"h{layer_idx+1}_{unit_idx} > {threshold:.4f}"),
                        m=m,
                        confidence=0.5,  # Placeholder
                        support=0        # Placeholder
                    )
                    
                    # Evaluate rule on input data
                    rule_predictions = np.zeros(len(X_np), dtype=bool)
                    
                    for i in range(len(X_np)):
                        # Create interpretation for this sample
                        interpretation = {}
                        
                        for j in range(X_np.shape[1]):
                            feature_name = self.feature_names[j] if self.feature_names and j < len(self.feature_names) else f"x[{j}]"
                            interpretation[feature_name] = X_np[i, j] > 0
                            
                        # Evaluate rule
                        rule_predictions[i] = rule.evaluate(interpretation)
                        
                    # Compute accuracy
                    accuracy = np.mean(rule_predictions == is_active)
                    
                    # Update best M if better
                    if accuracy > best_accuracy:
                        best_accuracy = accuracy
                        best_m = m
                        
                # Create final rule with best M
                final_rule = MofNRule(
                    conditions=conditions,
                    conclusion=Atom(f"h{layer_idx+1}_{unit_idx} > {threshold:.4f}"),
                    m=best_m,
                    confidence=best_accuracy,
                    support=np.sum(is_active)
                )
                
                # Add rule to layer rules
                layer_rules.append(final_rule)
                
            # Store layer rules
            self.hidden_unit_rules[layer_name] = layer_rules
    
    def _extract_output_unit_rules(self, model: nn.Module, X: torch.Tensor, 
                                y: torch.Tensor) -> None:
        """
        Extract M-of-N rules for output units.
        
        Args:
            model: Neural network model
            X: Input data
            y: Target labels or values
        """
        # Reset output unit rules
        self.output_unit_rules = {}
        
        # Get model predictions
        model.eval()
        with torch.no_grad():
            y_pred = model(X)
            
        # For classification, get class predictions
        if y_pred.dim() > 1 and y_pred.size(1) > 1:
            # Multi-class classification
            class_preds = torch.argmax(y_pred, dim=1).cpu().numpy()
            num_classes = y_pred.size(1)
            is_classification = True
        elif torch.unique(y).size(0) <= 10:  # Arbitrary threshold for classification
            # Binary classification
            class_preds = (y_pred > 0.5).int().cpu().numpy()
            num_classes = 2
            is_classification = True
        else:
            # Regression
            class_preds = y_pred.cpu().numpy()
            num_classes = 1
            is_classification = False
            
        # Get last weight layer (output layer)
        last_layer_name = self.weight_layers[-1] if self.weight_layers else None
        
        # Skip if no weight layers
        if not last_layer_name:
            return
            
        # Get output layer weights and bias
        weights = self.weights[last_layer_name]['weight']
        bias = self.weights[last_layer_name]['bias']
        
        # Get activations of the last hidden layer
        hidden_layer_name = self.weight_layers[-2] if len(self.weight_layers) > 1 else None
        
        # Skip if no hidden layers
        if not hidden_layer_name or hidden_layer_name not in self.hidden_activations:
            return
            
        hidden_activations = self.hidden_activations[hidden_layer_name]
        
        # Extract rules for each output unit
        output_rules = []
        
        for output_idx in range(weights.shape[0]):
            # Get weights for this output unit
            output_weights = weights[output_idx]
            output_bias = bias[output_idx] if bias is not None else 0.0
            
            # Create conditions based on hidden unit activations
            conditions = []
            
            for hidden_idx in range(weights.shape[1]):
                # Get threshold for hidden unit
                if hidden_layer_name in self.activation_thresholds:
                    hidden_threshold = self.activation_thresholds[hidden_layer_name][hidden_idx]
                else:
                    hidden_threshold = 0.5
                    
                # Determine condition based on weight
                if output_weights[hidden_idx] > 0:
                    # Positive weight: hidden unit should be active
                    conditions.append(Atom(f"h{len(self.weight_layers)-1}_{hidden_idx} > {hidden_threshold:.4f}"))
                elif output_weights[hidden_idx] < 0:
                    # Negative weight: hidden unit should be inactive
                    conditions.append(Atom(f"h{len(self.weight_layers)-1}_{hidden_idx} <= {hidden_threshold:.4f}"))
                    
            # Limit number of conditions
            if len(conditions) > self.max_conditions:
                # Keep only the top conditions based on weight magnitude
                sorted_indices = np.argsort(-np.abs(output_weights))
                top_indices = sorted_indices[:self.max_conditions]
                conditions = [conditions[i] for i in top_indices if i < len(conditions)]
                
            # Skip if no conditions
            if not conditions:
                continue
                
            # Determine M for M-of-N rule
            # For classification, create rule for each class
            if is_classification:
                # For binary classification or output_idx < num_classes
                if num_classes == 2 or output_idx < num_classes:
                    # Create binary labels for this class
                    if num_classes == 2:
                        is_class = class_preds == 1
                    else:
                        is_class = class_preds == output_idx
                        
                    # Skip if all or none are in this class
                    if np.all(is_class) or np.all(~is_class):
                        continue
                        
                    # Search for the best M value
                    best_m = 1
                    best_accuracy = 0.0
                    
                    for m in range(1, len(conditions) + 1):
                        # Create M-of-N rule
                        if num_classes == 2:
                            conclusion = Atom("class_1")
                        else:
                            conclusion = Atom(f"class_{output_idx}")
                            
                        rule = MofNRule(
                            conditions=conditions,
                            conclusion=conclusion,
                            m=m,
                            confidence=0.5,  # Placeholder
                            support=0        # Placeholder
                        )
                        
                        # Evaluate rule on hidden activations
                        rule_predictions = np.zeros(len(hidden_activations), dtype=bool)
                        
                        for i in range(len(hidden_activations)):
                            # Create interpretation for this sample
                            interpretation = {}
                            
                            for hidden_idx in range(hidden_activations.shape[1]):
                                hidden_key = f"h{len(self.weight_layers)-1}_{hidden_idx}"
                                if hidden_layer_name in self.activation_thresholds:
                                    hidden_threshold = self.activation_thresholds[hidden_layer_name][hidden_idx]
                                else:
                                    hidden_threshold = 0.5
                                    
                                interpretation[hidden_key] = hidden_activations[i, hidden_idx] > hidden_threshold
                                
                            # Evaluate rule
                            rule_predictions[i] = rule.evaluate(interpretation)
                            
                        # Compute accuracy
                        accuracy = np.mean(rule_predictions == is_class)
                        
                        # Update best M if better
                        if accuracy > best_accuracy:
                            best_accuracy = accuracy
                            best_m = m
                            
                    # Create final rule with best M
                    if num_classes == 2:
                        conclusion = Atom("class_1")
                    else:
                        conclusion = Atom(f"class_{output_idx}")
                        
                    final_rule = MofNRule(
                        conditions=conditions,
                        conclusion=conclusion,
                        m=best_m,
                        confidence=best_accuracy,
                        support=np.sum(is_class)
                    )
                    
                    # Add rule to output rules
                    output_rules.append(final_rule)
            else:
                # For regression, create a rule for output value ranges
                # Divide output range into buckets
                y_min = np.min(class_preds)
                y_max = np.max(class_preds)
                
                # Create 5 buckets
                buckets = np.linspace(y_min, y_max, 6)
                
                # For each bucket, create a rule
                for bucket_idx in range(5):
                    bucket_min = buckets[bucket_idx]
                    bucket_max = buckets[bucket_idx + 1]
                    
                    # Create binary labels for this bucket
                    in_bucket = (class_preds >= bucket_min) & (class_preds < bucket_max)
                    
                    # Skip if no samples in this bucket
                    if np.sum(in_bucket) == 0:
                        continue
                        
                    # Search for the best M value
                    best_m = 1
                    best_accuracy = 0.0
                    
                    for m in range(1, len(conditions) + 1):
                        # Create M-of-N rule
                        conclusion = Atom(f"{(bucket_min + bucket_max) / 2:.4f}")
                        
                        rule = MofNRule(
                            conditions=conditions,
                            conclusion=conclusion,
                            m=m,
                            confidence=0.5,  # Placeholder
                            support=0        # Placeholder
                        )
                        
                        # Evaluate rule on hidden activations
                        rule_predictions = np.zeros(len(hidden_activations), dtype=bool)
                        
                        for i in range(len(hidden_activations)):
                            # Create interpretation for this sample
                            interpretation = {}
                            
                            for hidden_idx in range(hidden_activations.shape[1]):
                                hidden_key = f"h{len(self.weight_layers)-1}_{hidden_idx}"
                                if hidden_layer_name in self.activation_thresholds:
                                    hidden_threshold = self.activation_thresholds[hidden_layer_name][hidden_idx]
                                else:
                                    hidden_threshold = 0.5
                                    
                                interpretation[hidden_key] = hidden_activations[i, hidden_idx] > hidden_threshold
                                
                            # Evaluate rule
                            rule_predictions[i] = rule.evaluate(interpretation)
                            
                        # Compute accuracy
                        accuracy = np.mean(rule_predictions == in_bucket)
                        
                        # Update best M if better
                        if accuracy > best_accuracy:
                            best_accuracy = accuracy
                            best_m = m
                            
                    # Create final rule with best M
                    conclusion = Atom(f"{(bucket_min + bucket_max) / 2:.4f}")
                    
                    final_rule = MofNRule(
                        conditions=conditions,
                        conclusion=conclusion,
                        m=best_m,
                        confidence=best_accuracy,
                        support=np.sum(in_bucket)
                    )
                    
                    # Add rule to output rules
                    output_rules.append(final_rule)
                    
        # Store output rules
        self.output_unit_rules[last_layer_name] = output_rules
    
    def _combine_rules(self) -> List[Rule]:
        """
        Combine hidden unit and output unit rules into a unified rule set.
        
        Returns:
            Combined list of rules
        """
        # Start with output unit rules
        rules = []
        
        for layer_name, layer_rules in self.output_unit_rules.items():
            rules.extend(layer_rules)
            
        # Add hidden unit rules
        for layer_name, layer_rules in self.hidden_unit_rules.items():
            rules.extend(layer_rules)
            
        # Filter out low-confidence rules
        filtered_rules = [rule for rule in rules if rule.confidence >= 0.5]
        
        # Sort by confidence
        filtered_rules.sort(key=lambda rule: rule.confidence, reverse=True)
        
        return filtered_rules


class ECLAIRERuleExtractor(RuleExtractor):
    """
    Extracts rules using the ECLAIRE algorithm (Extracting Concept Lattice-based 
    Interpretable Rules Efficiently).
    
    ECLAIRE constructs a concept lattice from neural activations and extracts
    interpretable rules that capture the network's behavior.
    """
    
    def __init__(self, name: str = "ECLAIREExtractor", 
                rule_type: RuleType = RuleType.PROPOSITIONAL,
                min_support: float = 0.05, min_confidence: float = 0.7):
        """
        Initialize the ECLAIRE rule extractor.
        
        Args:
            name: Name of the extractor
            rule_type: Type of rules to extract
            min_support: Minimum support threshold for concepts
            min_confidence: Minimum confidence threshold for rules
        """
        super().__init__(name, rule_type)
        
        self.min_support = min_support
        self.min_confidence = min_confidence
        
        # Concept lattice
        self.lattice = None
        
        # Binary features
        self.binary_features = None
        
        # Feature names after binarization
        self.binary_feature_names = []
        
        # Rules extracted from the lattice
        self.lattice_rules = []
        
        # Feature importance
        self.feature_importance = {}
    
    def extract_rules(self, model: nn.Module, X: torch.Tensor, 
                     y: torch.Tensor, **kwargs) -> RuleSet:
        """
        Extract rules using the ECLAIRE algorithm.
        
        Args:
            model: Neural network model
            X: Input data
            y: Target labels or values
            **kwargs: Additional extraction parameters:
                - min_support: Minimum support threshold
                - min_confidence: Minimum confidence threshold
                - feature_names: List of feature names
                - binarize_strategy: Strategy for binarizing features
            
        Returns:
            Extracted RuleSet
        """
        # Update parameters if provided
        self.min_support = kwargs.get('min_support', self.min_support)
        self.min_confidence = kwargs.get('min_confidence', self.min_confidence)
        
        # Set feature names if provided
        if 'feature_names' in kwargs:
            self.feature_names = kwargs['feature_names']
            
        # Get model predictions
        model.eval()
        with torch.no_grad():
            y_pred = model(X)
            
        # For classification, get class predictions
        if y_pred.dim() > 1 and y_pred.size(1) > 1:
            # Multi-class classification
            y_model = torch.argmax(y_pred, dim=1).cpu().numpy()
            num_classes = y_pred.size(1)
            is_classification = True
        elif torch.unique(y).size(0) <= 10:  # Arbitrary threshold for classification
            # Binary classification
            y_model = (y_pred > 0.5).int().cpu().numpy()
            num_classes = 2
            is_classification = True
        else:
            # Regression
            y_model = y_pred.cpu().numpy()
            num_classes = 1
            is_classification = False
            
        # Convert data to numpy
        X_np = X.cpu().numpy()
        
        # Binarize features
        binarize_strategy = kwargs.get('binarize_strategy', 'median')
        self._binarize_features(X_np, binarize_strategy)
        
        # Build concept lattice
        self._build_concept_lattice(self.binary_features, y_model, is_classification)
        
        # Extract rules from the lattice
        self._extract_rules_from_lattice(y_model, is_classification, num_classes)
        
        # Create rule set
        rule_set = RuleSet(
            rules=self.lattice_rules,
            name=f"{self.name}_Rules",
            extraction_method=RuleExtractionMethod.ECLAIRE
        )
        
        # Mark extraction as done
        self._has_extracted = True
        
        return rule_set
    
    def _binarize_features(self, X: np.ndarray, strategy: str = 'median') -> None:
        """
        Binarize continuous features for concept lattice construction.
        
        Args:
            X: Input data
            strategy: Binarization strategy ('median', 'mean', 'threshold', 'quantile')
        """
        # Reset binary features and names
        self.binary_features = None
        self.binary_feature_names = []
        
        # Create binary features
        binary_features = np.zeros((X.shape[0], X.shape[1] * 2), dtype=bool)
        
        for j in range(X.shape[1]):
            feature = X[:, j]
            
            # Get feature name
            if self.feature_names and j < len(self.feature_names):
                feature_name = self.feature_names[j]
            else:
                feature_name = f"x[{j}]"
                
            # Determine threshold based on strategy
            if strategy == 'median':
                threshold = np.median(feature)
            elif strategy == 'mean':
                threshold = np.mean(feature)
            elif strategy == 'threshold':
                threshold = 0.5
            elif strategy == 'quantile':
                # Create multiple thresholds at different quantiles
                thresholds = [np.quantile(feature, q) for q in [0.25, 0.5, 0.75]]
                
                # Create binary features for each threshold
                for i, thresh in enumerate(thresholds):
                    binary_features[:, j*2+i] = feature > thresh
                    self.binary_feature_names.append(f"{feature_name} > {thresh:.4f}")
                    
                # Skip normal processing
                continue
            else:
                # Default to median
                threshold = np.median(feature)
                
            # Create binary features
            binary_features[:, j*2] = feature > threshold
            binary_features[:, j*2+1] = feature <= threshold
            
            # Create feature names
            self.binary_feature_names.append(f"{feature_name} > {threshold:.4f}")
            self.binary_feature_names.append(f"{feature_name} <= {threshold:.4f}")
            
        # Store binary features
        self.binary_features = binary_features
    
    def _build_concept_lattice(self, X: np.ndarray, y: np.ndarray, 
                            is_classification: bool) -> None:
        """
        Build a concept lattice from binary features.
        
        Args:
            X: Binary feature matrix
            y: Target values
            is_classification: Whether this is a classification task
        """
        # Reset lattice
        self.lattice = nx.DiGraph()
        
        # Create the empty concept (extent: all objects, intent: no attributes)
        empty_intent = frozenset()
        all_extent = frozenset(range(X.shape[0]))
        bottom_concept = (all_extent, empty_intent)
        
        # Create the full concept (extent: no objects, intent: all attributes)
        full_intent = frozenset(range(X.shape[1]))
        empty_extent = frozenset()
        top_concept = (empty_extent, full_intent)
        
        # Add top and bottom concepts to the lattice
        self.lattice.add_node(bottom_concept, extent=bottom_concept[0], intent=bottom_concept[1], 
                           support=len(bottom_concept[0]) / X.shape[0])
        self.lattice.add_node(top_concept, extent=top_concept[0], intent=top_concept[1], 
                           support=0.0)
        
        # Add edge from bottom to top
        self.lattice.add_edge(bottom_concept, top_concept)
        
        # Function to compute closure of a set of attributes
        def closure(attributes):
            # Find all objects that have all these attributes
            objects = set(range(X.shape[0]))
            for attr in attributes:
                objects &= set(np.where(X[:, attr])[0])
                
            # Find all attributes shared by these objects
            attrs = set(range(X.shape[1]))
            for obj in objects:
                attrs &= set(np.where(X[obj, :])[0])
                
            return frozenset(objects), frozenset(attrs)
        
        # Add concepts for each attribute
        concepts_to_process = deque()
        processed_intents = set([empty_intent, full_intent])
        
        # Add single-attribute concepts
        for attr in range(X.shape[1]):
            extent, intent = closure(frozenset([attr]))
            
            # Skip if already processed
            if intent in processed_intents:
                continue
                
            # Add concept to lattice
            concept = (extent, intent)
            self.lattice.add_node(concept, extent=extent, intent=intent, 
                               support=len(extent) / X.shape[0])
            
            # Add to processing queue
            concepts_to_process.append(concept)
            processed_intents.add(intent)
            
        # Process queue
        while concepts_to_process:
            concept = concepts_to_process.popleft()
            
            # Find all attributes not in this concept's intent
            remaining_attrs = full_intent - concept[1]
            
            # For each attribute, create a new concept by adding it to the intent
            for attr in remaining_attrs:
                new_intent = concept[1] | frozenset([attr])
                
                # Compute closure
                new_extent, closed_intent = closure(new_intent)
                
                # Skip if already processed
                if closed_intent in processed_intents:
                    continue
                    
                # Add concept to lattice
                new_concept = (new_extent, closed_intent)
                self.lattice.add_node(new_concept, extent=new_extent, intent=closed_intent, 
                                   support=len(new_extent) / X.shape[0])
                
                # Add to processing queue
                concepts_to_process.append(new_concept)
                processed_intents.add(closed_intent)
                
        # Add edges between concepts
        for concept1 in self.lattice.nodes():
            for concept2 in self.lattice.nodes():
                if concept1 != concept2:
                    # Add edge if concept1's intent is a subset of concept2's intent
                    if concept1[1].issubset(concept2[1]):
                        # Check if there's no intermediate concept
                        is_direct = True
                        for concept3 in self.lattice.nodes():
                            if (concept3 != concept1 and concept3 != concept2 and
                                concept1[1].issubset(concept3[1]) and concept3[1].issubset(concept2[1])):
                                is_direct = False
                                break
                                
                        if is_direct:
                            self.lattice.add_edge(concept1, concept2)
                            
        # Add target information to each concept
        for concept in self.lattice.nodes():
            extent = concept[0]
            
            if is_classification:
                # Compute class distribution
                if extent:
                    class_counts = np.bincount(y[list(extent)], minlength=len(np.unique(y)))
                    class_dist = class_counts / np.sum(class_counts)
                    self.lattice.nodes[concept]['class_distribution'] = class_dist
                    
                    # Set majority class
                    majority_class = np.argmax(class_counts)
                    self.lattice.nodes[concept]['majority_class'] = majority_class
                    
                    # Set purity (confidence)
                    purity = class_counts[majority_class] / np.sum(class_counts) if np.sum(class_counts) > 0 else 0.0
                    self.lattice.nodes[concept]['purity'] = purity
                else:
                    # Empty extent
                    self.lattice.nodes[concept]['class_distribution'] = np.zeros(len(np.unique(y)))
                    self.lattice.nodes[concept]['majority_class'] = 0
                    self.lattice.nodes[concept]['purity'] = 0.0
            else:
                # Regression: compute mean and variance
                if extent:
                    mean_value = np.mean(y[list(extent)])
                    variance = np.var(y[list(extent)])
                    self.lattice.nodes[concept]['mean_value'] = mean_value
                    self.lattice.nodes[concept]['variance'] = variance
                    
                    # Set confidence (inverse of normalized variance)
                    confidence = 1.0 / (1.0 + variance) if variance > 0 else 1.0
                    self.lattice.nodes[concept]['purity'] = confidence
                else:
                    # Empty extent
                    self.lattice.nodes[concept]['mean_value'] = 0.0
                    self.lattice.nodes[concept]['variance'] = float('inf')
                    self.lattice.nodes[concept]['purity'] = 0.0
    
    def _extract_rules_from_lattice(self, y: np.ndarray, is_classification: bool, 
                                 num_classes: int) -> None:
        """
        Extract rules from the concept lattice.
        
        Args:
            y: Target values
            is_classification: Whether this is a classification task
            num_classes: Number of classes (for classification)
        """
        # Reset lattice rules
        self.lattice_rules = []
        
        # Extract rules from the lattice
        for concept in self.lattice.nodes():
            # Skip concepts with low support
            support = self.lattice.nodes[concept]['support']
            if support < self.min_support:
                continue
                
            # Skip concepts with low purity
            purity = self.lattice.nodes[concept]['purity']
            if purity < self.min_confidence:
                continue
                
            # Create premise from intent
            intent = concept[1]
            
            # Skip empty intent
            if not intent:
                continue
                
            # Create conditions from intent
            conditions = []
            for attr in intent:
                if attr < len(self.binary_feature_names):
                    conditions.append(Atom(self.binary_feature_names[attr]))
                    
            # Skip if no conditions
            if not conditions:
                continue
                
            # Create rule
            premise = conditions[0]
            for cond in conditions[1:]:
                premise = Conjunction(premise, cond)
                
            # Create conclusion
            if is_classification:
                majority_class = self.lattice.nodes[concept]['majority_class']
                if num_classes == 2:
                    conclusion = Atom("class_1") if majority_class == 1 else Atom("class_0")
                else:
                    conclusion = Atom(f"class_{majority_class}")
            else:
                mean_value = self.lattice.nodes[concept]['mean_value']
                conclusion = Atom(f"{mean_value:.4f}")
                
            # Create rule
            rule = Rule(
                premise=premise,
                conclusion=conclusion,
                confidence=purity,
                support=int(support * self.binary_features.shape[0])
            )
            
            # Add rule to lattice rules
            self.lattice_rules.append(rule)
            
        # Sort rules by confidence (descending)
        self.lattice_rules.sort(key=lambda rule: rule.confidence, reverse=True)


class RuleExtractionEngine:
    """
    Main engine for rule extraction from neural networks.
    """
    
    def __init__(self, name: str = "RuleExtractionEngine", config: Dict = None):
        """
        Initialize the rule extraction engine.
        
        Args:
            name: Name of the engine
            config: Configuration dictionary
        """
        self.name = name
        self.config = config or {}
        
        # Default configuration
        if self.config is None:
            self.config = {}
            
        # Rule extraction methods enabled
        self.extraction_methods = self.config.get('extraction_methods', 
                                               [RuleExtractionMethod.DECISION_TREE,
                                                RuleExtractionMethod.M_OF_N])
        
        # Rule types enabled
        self.rule_types = self.config.get('rule_types',
                                        [RuleType.PROPOSITIONAL,
                                         RuleType.M_OF_N])
        
        # Available extractors
        self.extractors = {}
        
        # Expression parser
        self.parser = ExpressionParser()
        
        # Extraction metrics
        self.extraction_metrics = {}
        
        # Extracted rule sets
        self.rule_sets = {}
        
        # Initialize
        self._is_initialized = False
    
    def initialize(self) -> None:
        """Initialize the engine with necessary resources."""
        try:
            # Initialize extractors for each enabled method
            self._init_extractors()
            
            self._is_initialized = True
            logger.info(f"Initialized {self.name}")
        except Exception as e:
            logger.error(f"Failed to initialize {self.name}: {str(e)}")
            raise
    
    def reset(self) -> None:
        """Reset the engine to its initial state."""
        # Clear extractors and rule sets
        self.extractors = {}
        self.rule_sets = {}
        self.extraction_metrics = {}
        
        # Re-initialize extractors
        self._init_extractors()
        
        logger.info(f"Reset {self.name}")
    
    def _init_extractors(self) -> None:
        """Initialize rule extractors for each enabled method."""
        # Clear existing extractors
        self.extractors = {}
        
        # Create extractors for each enabled method
        for method in self.extraction_methods:
            if method == RuleExtractionMethod.DECISION_TREE:
                extractor = DecisionTreeRuleExtractor(
                    name="DecisionTreeExtractor",
                    rule_type=RuleType.PROPOSITIONAL,
                    max_depth=self.config.get('decision_tree_max_depth', 5),
                    min_samples_leaf=self.config.get('decision_tree_min_samples_leaf', 10)
                )
                self.extractors[method] = extractor
                
            elif method == RuleExtractionMethod.DEEPRED:
                extractor = DeepREDRuleExtractor(
                    name="DeepREDExtractor",
                    rule_type=RuleType.PROPOSITIONAL,
                    min_fidelity=self.config.get('deepred_min_fidelity', 0.95),
                    discretize_splits=self.config.get('deepred_discretize_splits', 5)
                )
                self.extractors[method] = extractor
                
            elif method == RuleExtractionMethod.TREPAN:
                extractor = TREPANRuleExtractor(
                    name="TREPANExtractor",
                    rule_type=RuleType.PROPOSITIONAL,
                    max_tree_size=self.config.get('trepan_max_tree_size', 100),
                    min_examples=self.config.get('trepan_min_examples', 1000)
                )
                self.extractors[method] = extractor
                
            elif method == RuleExtractionMethod.M_OF_N:
                extractor = MofNRuleExtractor(
                    name="MofNExtractor",
                    rule_type=RuleType.M_OF_N,
                    min_fidelity=self.config.get('mofn_min_fidelity', 0.95),
                    max_conditions=self.config.get('mofn_max_conditions', 10)
                )
                self.extractors[method] = extractor
                
            elif method == RuleExtractionMethod.ECLAIRE:
                extractor = ECLAIRERuleExtractor(
                    name="ECLAIREExtractor",
                    rule_type=RuleType.PROPOSITIONAL,
                    min_support=self.config.get('eclaire_min_support', 0.05),
                    min_confidence=self.config.get('eclaire_min_confidence', 0.7)
                )
                self.extractors[method] = extractor
    
    def extract_rules(self, model: nn.Module, X: torch.Tensor, y: torch.Tensor,
                     method: RuleExtractionMethod = None, **kwargs) -> RuleSet:
        """
        Extract rules from a neural network.
        
        Args:
            model: Neural network model
            X: Input data
            y: Target labels or values
            method: Rule extraction method to use
            **kwargs: Additional extraction parameters
            
        Returns:
            Extracted RuleSet
        """
        if not self._is_initialized:
            raise RuntimeError(f"{self.name} is not initialized")
            
        # Get feature names
        if 'feature_names' in kwargs:
            for extractor in self.extractors.values():
                extractor.set_feature_names(kwargs['feature_names'])
                
        # If method is specified, use that extractor
        if method is not None:
            if method not in self.extractors:
                raise ValueError(f"Extraction method not available: {method}")
                
            # Extract rules using the specified method
            rule_set = self.extractors[method].extract_rules(model, X, y, **kwargs)
            
            # Store rule set
            self.rule_sets[method] = rule_set
            
            # Validate rules
            validation_metrics = self.extractors[method].validate_rules(rule_set, model, X, y)
            
            # Store metrics
            self.extraction_metrics[method] = validation_metrics
            
            return rule_set
        else:
            # Use all available extractors and return the best one
            best_rule_set = None
            best_fidelity = 0.0
            
            for method, extractor in self.extractors.items():
                # Extract rules
                rule_set = extractor.extract_rules(model, X, y, **kwargs)
                
                # Store rule set
                self.rule_sets[method] = rule_set
                
                # Validate rules
                validation_metrics = extractor.validate_rules(rule_set, model, X, y)
                
                # Store metrics
                self.extraction_metrics[method] = validation_metrics
                
                # Update best rule set
                fidelity = validation_metrics.get('fidelity', 0.0)
                if fidelity > best_fidelity:
                    best_fidelity = fidelity
                    best_rule_set = rule_set
                    
            return best_rule_set
    
    def get_rule_set(self, method: RuleExtractionMethod) -> Optional[RuleSet]:
        """
        Get a rule set extracted using a specific method.
        
        Args:
            method: Rule extraction method
            
        Returns:
            Extracted RuleSet or None if not available
        """
        return self.rule_sets.get(method, None)
    
    def get_validation_metrics(self, method: RuleExtractionMethod) -> Dict[str, float]:
        """
        Get validation metrics for a rule set.
        
        Args:
            method: Rule extraction method
            
        Returns:
            Dictionary of validation metrics
        """
        return self.extraction_metrics.get(method, {})
    
    def compare_extractors(self, metrics: List[str] = None) -> pd.DataFrame:
        """
        Compare the performance of different rule extractors.
        
        Args:
            metrics: List of metrics to compare
            
        Returns:
            DataFrame comparing the extractors
        """
        import pandas as pd
        
        # Default metrics
        if metrics is None:
            metrics = ['fidelity', 'accuracy', 'coverage', 'consistency']
            
        # Create comparison table
        comparison = {}
        
        for method, metric_dict in self.extraction_metrics.items():
            method_name = method.name
            comparison[method_name] = {}
            
            for metric in metrics:
                comparison[method_name][metric] = metric_dict.get(metric, float('nan'))
                
            # Add number of rules
            if method in self.rule_sets:
                comparison[method_name]['num_rules'] = len(self.rule_sets[method].rules)
                comparison[method_name]['avg_complexity'] = self.rule_sets[method].metrics.get('avg_complexity', float('nan'))
                
        # Convert to DataFrame
        return pd.DataFrame(comparison).T
    
    def simplify_rule_set(self, rule_set: RuleSet, min_improvement: float = 0.1) -> RuleSet:
        """
        Simplify a rule set.
        
        Args:
            rule_set: Rule set to simplify
            min_improvement: Minimum improvement threshold for rule simplification
            
        Returns:
            Simplified rule set
        """
        return rule_set.simplify(min_improvement)
    
    def filter_rule_set(self, rule_set: RuleSet, min_confidence: float = 0.0, 
                      min_support: int = 0, max_complexity: Optional[int] = None) -> RuleSet:
        """
        Filter a rule set.
        
        Args:
            rule_set: Rule set to filter
            min_confidence: Minimum confidence threshold
            min_support: Minimum support threshold
            max_complexity: Maximum complexity threshold
            
        Returns:
            Filtered rule set
        """
        return rule_set.filter_rules(min_confidence, min_support, max_complexity)
    
    def save_rule_set(self, rule_set: RuleSet, file_path: str) -> None:
        """
        Save a rule set to a file.
        
        Args:
            rule_set: Rule set to save
            file_path: Path to save the rule set
        """
        rule_set.save(file_path)
    
    def load_rule_set(self, file_path: str) -> RuleSet:
        """
        Load a rule set from a file.
        
        Args:
            file_path: Path to load the rule set from
            
        Returns:
            Loaded RuleSet
        """
        return RuleSet.load(file_path, self.parser)
    
    def convert_to_knowledge_base(self, rule_set: RuleSet) -> KnowledgeBase:
        """
        Convert a rule set to a knowledge base.
        
        Args:
            rule_set: Rule set to convert
            
        Returns:
            KnowledgeBase containing the rules
        """
        return rule_set.to_knowledge_base()


# Create default engine
default_engine = RuleExtractionEngine()

# Module-level convenience functions
def init_engine(config: Optional[Dict] = None) -> RuleExtractionEngine:
    """
    Initialize the default rule extraction engine.
    
    Args:
        config: Optional configuration dictionary
        
    Returns:
        Initialized engine
    """
    global default_engine
    
    # Create new engine with config
    if config:
        default_engine = RuleExtractionEngine(config=config)
    
    # Initialize the engine
    default_engine.initialize()
    
    return default_engine

def extract_rules(model: nn.Module, X: torch.Tensor, y: torch.Tensor,
               method: RuleExtractionMethod = None, **kwargs) -> RuleSet:
    """
    Extract rules from a neural network using the default engine.
    
    Args:
        model: Neural network model
        X: Input data
        y: Target labels or values
        method: Rule extraction method to use
        **kwargs: Additional extraction parameters
        
    Returns:
        Extracted RuleSet
    """
    return default_engine.extract_rules(model, X, y, method, **kwargs)

def get_rule_set(method: RuleExtractionMethod) -> Optional[RuleSet]:
    """
    Get a rule set extracted using a specific method.
    
    Args:
        method: Rule extraction method
        
    Returns:
        Extracted RuleSet or None if not available
    """
    return default_engine.get_rule_set(method)

def simplify_rule_set(rule_set: RuleSet, min_improvement: float = 0.1) -> RuleSet:
    """
    Simplify a rule set.
    
    Args:
        rule_set: Rule set to simplify
        min_improvement: Minimum improvement threshold for rule simplification
        
    Returns:
        Simplified rule set
    """
    return default_engine.simplify_rule_set(rule_set, min_improvement)

def filter_rule_set(rule_set: RuleSet, min_confidence: float = 0.0, 
                  min_support: int = 0, max_complexity: Optional[int] = None) -> RuleSet:
    """
    Filter a rule set.
    
    Args:
        rule_set: Rule set to filter
        min_confidence: Minimum confidence threshold
        min_support: Minimum support threshold
        max_complexity: Maximum complexity threshold
        
    Returns:
        Filtered rule set
    """
    return default_engine.filter_rule_set(rule_set, min_confidence, min_support, max_complexity)

def save_rule_set(rule_set: RuleSet, file_path: str) -> None:
    """
    Save a rule set to a file.
    
    Args:
        rule_set: Rule set to save
        file_path: Path to save the rule set
    """
    default_engine.save_rule_set(rule_set, file_path)

def load_rule_set(file_path: str) -> RuleSet:
    """
    Load a rule set from a file.
    
    Args:
        file_path: Path to load the rule set from
        
    Returns:
        Loaded RuleSet
    """
    return default_engine.load_rule_set(file_path)

def convert_to_knowledge_base(rule_set: RuleSet) -> KnowledgeBase:
    """
    Convert a rule set to a knowledge base.
    
    Args:
        rule_set: Rule set to convert
        
    Returns:
        KnowledgeBase containing the rules
    """
    return default_engine.convert_to_knowledge_base(rule_set)


# Export main components and utility functions
__all__ = [
    # Classes
    'Rule',
    'MofNRule',
    'DecisionTreeRule',
    'RuleSet',
    'RuleExtractor',
    'DecisionTreeRuleExtractor',
    'DeepREDRuleExtractor',
    'TREPANRuleExtractor',
    'MofNRuleExtractor',
    'ECLAIRERuleExtractor',
    'RuleExtractionEngine',
    
    # Enums
    'RuleExtractionMethod',
    'RuleType',
    'RuleConfidence',
    
    # Default engine
    'default_engine',
    
    # Module-level functions
    'init_engine',
    'extract_rules',
    'get_rule_set',
    'simplify_rule_set',
    'filter_rule_set',
    'save_rule_set',
    'load_rule_set',
    'convert_to_knowledge_base'
]

# Initialize default engine if not in import context
if __name__ != '__main__':
    init_engine()
else:
    # Example usage
    from argparse import ArgumentParser
    
    parser = ArgumentParser(description='ULTRA Rule Extraction')
    parser.add_argument('--config', type=str, help='Path to configuration file')
    parser.add_argument('--model', type=str, required=True, help='Path to neural network model')
    parser.add_argument('--data', type=str, required=True, help='Path to input data')
    parser.add_argument('--output', type=str, help='Path to output rule set file')
    parser.add_argument('--method', type=str, choices=[m.name for m in RuleExtractionMethod], 
                      help='Rule extraction method')
    
    args = parser.parse_args()
    
    # Load configuration from file if provided
    config = None
    if args.config:
        with open(args.config, 'r') as f:
            import json
            config = json.load(f)
    
    # Initialize engine
    engine = init_engine(config)
    
    # Load model
    model = torch.load(args.model)
    
    # Load data
    data = torch.load(args.data)
    X, y = data['X'], data['y']
    
    # Extract rules
    method = RuleExtractionMethod[args.method] if args.method else None
    rule_set = engine.extract_rules(model, X, y, method)
    
    # Print rule set summary
    print(rule_set)
    
    # Save rule set if output path is provided
    if args.output:
        engine.save_rule_set(rule_set, args.output)
        print(f"Rule set saved to {args.output}")