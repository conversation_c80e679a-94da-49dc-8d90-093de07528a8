#!/usr/bin/env python3
"""
ULTRA: Symbolic Representation Learning

This module implements the Symbolic Representation Learning component of the Neuro-Symbolic 
Integration subsystem. It provides capabilities for mapping between neural representations and 
symbolic representations, allowing the system to extract structured symbolic knowledge from 
neural processing and, conversely, to ground symbolic representations in neural embeddings.

The module includes methods for:
1. Learning symbolic representations from neural representations
2. Mapping between different symbolic representations
3. Grounding symbolic representations in neural space
4. Evaluating the quality of symbolic representations
5. Integrating with other components of the ULTRA system

Author: ULTRA Development Team
"""

import os
import sys
import logging
import numpy as np
import math
import json
import re
from typing import Dict, List, Tuple, Set, Any, Union, Optional, Callable, Type, TypeVar, Iterator, FrozenSet
from enum import Enum, auto
from abc import ABC, abstractmethod
from collections import defaultdict, deque, Counter, OrderedDict
from dataclasses import dataclass, field
import itertools
import random
import pickle

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.optim import Adam, SGD, RMSprop
from torch.utils.data import Dataset, DataLoader, TensorDataset
import torch.distributions as distributions
from torch.nn.utils.rnn import pad_sequence, pack_padded_sequence, pad_packed_sequence

# Import local modules (if needed)
try:
    from ultra.logical_reasoning import (
        Expression, 
        Atom, 
        Constant, 
        Negation, 
        Conjunction, 
        Disjunction, 
        Implication, 
        Equivalence,
        ExpressionParser,
        KnowledgeBase
    )
except ImportError:
    # For standalone testing
    from logical_reasoning import (
        Expression, 
        Atom, 
        Constant, 
        Negation, 
        Conjunction, 
        Disjunction, 
        Implication, 
        Equivalence,
        ExpressionParser,
        KnowledgeBase
    )

# Configure logging
logger = logging.getLogger(__name__)
handler = logging.StreamHandler()
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
handler.setFormatter(formatter)
logger.addHandler(handler)
logger.setLevel(logging.INFO)

# Type variables for generic functions
T = TypeVar('T')
U = TypeVar('U')

# Enums and type definitions
class SymbolicFormType(Enum):
    """Types of symbolic forms that can be used for representation."""
    PREDICATE_LOGIC = auto()
    FIRST_ORDER_LOGIC = auto()
    DESCRIPTION_LOGIC = auto()
    ONTOLOGY = auto()
    KNOWLEDGE_GRAPH = auto()
    RULE_BASED = auto()
    PROBABILISTIC = auto()
    FUZZY = auto()
    MODAL = auto()

class EmbeddingMethod(Enum):
    """Methods for embedding symbolic knowledge."""
    ATOMIC = auto()      # Each symbol gets a unique embedding
    COMPOSITIONAL = auto()  # Embeddings are composed from sub-components
    CONTEXTUAL = auto()   # Embeddings depend on context
    RELATIONAL = auto()   # Embeddings preserve relational structure
    HIERARCHICAL = auto() # Embeddings preserve hierarchical structure

class SymbolicEntity:
    """
    Base class for symbolic entities in different representation forms.
    """
    
    def __init__(self, name: str, form_type: SymbolicFormType):
        """
        Initialize a symbolic entity.
        
        Args:
            name: Name of the entity
            form_type: Type of symbolic form
        """
        self.name = name
        self.form_type = form_type
        
    def __str__(self) -> str:
        return f"{self.name} ({self.form_type.name})"
    
    def __repr__(self) -> str:
        return self.__str__()
    
    def __eq__(self, other) -> bool:
        if not isinstance(other, SymbolicEntity):
            return False
        return self.name == other.name and self.form_type == other.form_type
    
    def __hash__(self) -> int:
        return hash((self.name, self.form_type))


class PredicateLogicSymbol(SymbolicEntity):
    """
    Symbol in predicate logic.
    """
    
    def __init__(self, name: str, arity: int = 0, is_function: bool = False):
        """
        Initialize a predicate logic symbol.
        
        Args:
            name: Name of the symbol
            arity: Number of arguments for the predicate or function
            is_function: Whether this is a function symbol (True) or predicate symbol (False)
        """
        super().__init__(name, SymbolicFormType.PREDICATE_LOGIC)
        self.arity = arity
        self.is_function = is_function
        
    def __str__(self) -> str:
        sym_type = "Function" if self.is_function else "Predicate"
        return f"{self.name}/{self.arity} ({sym_type})"
    
    def __eq__(self, other) -> bool:
        if not isinstance(other, PredicateLogicSymbol):
            return False
        return (self.name == other.name and 
                self.arity == other.arity and 
                self.is_function == other.is_function)
    
    def __hash__(self) -> int:
        return hash((self.name, self.arity, self.is_function))


class DescriptionLogicConcept(SymbolicEntity):
    """
    Concept in description logic.
    """
    
    def __init__(self, name: str, parent_concepts: List[str] = None):
        """
        Initialize a description logic concept.
        
        Args:
            name: Name of the concept
            parent_concepts: Names of parent concepts
        """
        super().__init__(name, SymbolicFormType.DESCRIPTION_LOGIC)
        self.parent_concepts = parent_concepts or []
        
    def __str__(self) -> str:
        if self.parent_concepts:
            return f"{self.name} ⊑ {' ⊓ '.join(self.parent_concepts)}"
        else:
            return self.name
    
    def __eq__(self, other) -> bool:
        if not isinstance(other, DescriptionLogicConcept):
            return False
        return (self.name == other.name and 
                set(self.parent_concepts) == set(other.parent_concepts))
    
    def __hash__(self) -> int:
        return hash((self.name, tuple(sorted(self.parent_concepts))))


class DescriptionLogicRole(SymbolicEntity):
    """
    Role in description logic.
    """
    
    def __init__(self, name: str, domain: str, range_: str, 
                is_transitive: bool = False, is_functional: bool = False,
                is_inverse_functional: bool = False, is_symmetric: bool = False):
        """
        Initialize a description logic role.
        
        Args:
            name: Name of the role
            domain: Domain concept
            range_: Range concept
            is_transitive: Whether the role is transitive
            is_functional: Whether the role is functional
            is_inverse_functional: Whether the role is inverse functional
            is_symmetric: Whether the role is symmetric
        """
        super().__init__(name, SymbolicFormType.DESCRIPTION_LOGIC)
        self.domain = domain
        self.range = range_
        self.is_transitive = is_transitive
        self.is_functional = is_functional
        self.is_inverse_functional = is_inverse_functional
        self.is_symmetric = is_symmetric
        
    def __str__(self) -> str:
        properties = []
        if self.is_transitive:
            properties.append("Transitive")
        if self.is_functional:
            properties.append("Functional")
        if self.is_inverse_functional:
            properties.append("InverseFunctional")
        if self.is_symmetric:
            properties.append("Symmetric")
            
        props_str = f" ({', '.join(properties)})" if properties else ""
        return f"{self.name}: {self.domain} → {self.range}{props_str}"
    
    def __eq__(self, other) -> bool:
        if not isinstance(other, DescriptionLogicRole):
            return False
        return (self.name == other.name and 
                self.domain == other.domain and 
                self.range == other.range and
                self.is_transitive == other.is_transitive and
                self.is_functional == other.is_functional and
                self.is_inverse_functional == other.is_inverse_functional and
                self.is_symmetric == other.is_symmetric)
    
    def __hash__(self) -> int:
        return hash((self.name, self.domain, self.range, 
                   self.is_transitive, self.is_functional, 
                   self.is_inverse_functional, self.is_symmetric))


class KnowledgeGraphEntity(SymbolicEntity):
    """
    Entity in a knowledge graph.
    """
    
    def __init__(self, name: str, entity_type: str, attributes: Dict[str, Any] = None):
        """
        Initialize a knowledge graph entity.
        
        Args:
            name: Name of the entity
            entity_type: Type of the entity
            attributes: Dictionary of entity attributes
        """
        super().__init__(name, SymbolicFormType.KNOWLEDGE_GRAPH)
        self.entity_type = entity_type
        self.attributes = attributes or {}
        
    def __str__(self) -> str:
        attrs = ', '.join(f"{k}={v}" for k, v in self.attributes.items())
        return f"{self.name} ({self.entity_type}{': ' + attrs if attrs else ''})"
    
    def __eq__(self, other) -> bool:
        if not isinstance(other, KnowledgeGraphEntity):
            return False
        return (self.name == other.name and 
                self.entity_type == other.entity_type and 
                self.attributes == other.attributes)
    
    def __hash__(self) -> int:
        attrs_tuple = tuple(sorted((k, str(v)) for k, v in self.attributes.items()))
        return hash((self.name, self.entity_type, attrs_tuple))


class KnowledgeGraphRelation(SymbolicEntity):
    """
    Relation in a knowledge graph.
    """
    
    def __init__(self, name: str, source: KnowledgeGraphEntity, target: KnowledgeGraphEntity,
                weight: float = 1.0, attributes: Dict[str, Any] = None):
        """
        Initialize a knowledge graph relation.
        
        Args:
            name: Name of the relation
            source: Source entity
            target: Target entity
            weight: Weight or confidence of the relation
            attributes: Dictionary of relation attributes
        """
        super().__init__(name, SymbolicFormType.KNOWLEDGE_GRAPH)
        self.source = source
        self.target = target
        self.weight = weight
        self.attributes = attributes or {}
        
    def __str__(self) -> str:
        attrs = ', '.join(f"{k}={v}" for k, v in self.attributes.items())
        return f"{self.source.name} --[{self.name}{f', {self.weight:.2f}' if self.weight != 1.0 else ''}{': ' + attrs if attrs else ''}]--> {self.target.name}"
    
    def __eq__(self, other) -> bool:
        if not isinstance(other, KnowledgeGraphRelation):
            return False
        return (self.name == other.name and 
                self.source == other.source and 
                self.target == other.target and
                self.weight == other.weight and
                self.attributes == other.attributes)
    
    def __hash__(self) -> int:
        attrs_tuple = tuple(sorted((k, str(v)) for k, v in self.attributes.items()))
        return hash((self.name, self.source, self.target, self.weight, attrs_tuple))


class Rule(SymbolicEntity):
    """
    Rule in a rule-based system.
    """
    
    def __init__(self, name: str, antecedent: List[Expression], consequent: Expression, 
                confidence: float = 1.0):
        """
        Initialize a rule.
        
        Args:
            name: Name of the rule
            antecedent: List of expressions forming the antecedent (body)
            consequent: Expression forming the consequent (head)
            confidence: Confidence or certainty factor of the rule
        """
        super().__init__(name, SymbolicFormType.RULE_BASED)
        self.antecedent = antecedent
        self.consequent = consequent
        self.confidence = confidence
        
    def __str__(self) -> str:
        antecedent_str = " ∧ ".join(str(expr) for expr in self.antecedent)
        return f"{self.name}: {antecedent_str} → {self.consequent}{f' (CF={self.confidence:.2f})' if self.confidence != 1.0 else ''}"
    
    def __eq__(self, other) -> bool:
        if not isinstance(other, Rule):
            return False
        return (self.name == other.name and 
                self.antecedent == other.antecedent and 
                self.consequent == other.consequent and
                self.confidence == other.confidence)
    
    def __hash__(self) -> int:
        return hash((self.name, tuple(self.antecedent), self.consequent, self.confidence))


class SymbolicVocabulary:
    """
    Vocabulary of symbolic entities for representation learning.
    """
    
    def __init__(self, name: str = "Default Vocabulary"):
        """
        Initialize a symbolic vocabulary.
        
        Args:
            name: Name of the vocabulary
        """
        self.name = name
        
        # Map from entity name to entity
        self.entities = {}
        
        # Indices for different form types
        self.form_indices = {form_type: {} for form_type in SymbolicFormType}
        
        # Symbol indices for neural representations
        self.symbol_to_idx = {"<PAD>": 0, "<UNK>": 1}
        self.idx_to_symbol = {0: "<PAD>", 1: "<UNK>"}
        
        # Track additions and removals for version control
        self.version = 0
        self.changes = []
    
    def add_entity(self, entity: SymbolicEntity) -> None:
        """
        Add an entity to the vocabulary.
        
        Args:
            entity: Symbolic entity to add
        """
        # Add to main entity map
        self.entities[entity.name] = entity
        
        # Add to form-specific index
        form_type = entity.form_type
        if entity.name not in self.form_indices[form_type]:
            self.form_indices[form_type][entity.name] = entity
            
        # Add to symbol indices if not already present
        if entity.name not in self.symbol_to_idx:
            idx = len(self.symbol_to_idx)
            self.symbol_to_idx[entity.name] = idx
            self.idx_to_symbol[idx] = entity.name
            
        # Record change
        self.version += 1
        self.changes.append(("add", entity.name, entity))
        
        logger.debug(f"Added entity to vocabulary: {entity}")
    
    def remove_entity(self, entity_name: str) -> bool:
        """
        Remove an entity from the vocabulary.
        
        Args:
            entity_name: Name of the entity to remove
            
        Returns:
            True if successfully removed, False otherwise
        """
        if entity_name not in self.entities:
            return False
            
        entity = self.entities[entity_name]
        form_type = entity.form_type
        
        # Remove from main entity map
        del self.entities[entity_name]
        
        # Remove from form-specific index
        if entity_name in self.form_indices[form_type]:
            del self.form_indices[form_type][entity_name]
            
        # Record change
        self.version += 1
        self.changes.append(("remove", entity_name, entity))
        
        logger.debug(f"Removed entity from vocabulary: {entity}")
        return True
    
    def get_entity(self, entity_name: str) -> Optional[SymbolicEntity]:
        """
        Get an entity by name.
        
        Args:
            entity_name: Name of the entity
            
        Returns:
            Entity or None if not found
        """
        return self.entities.get(entity_name)
    
    def get_entities_by_form(self, form_type: SymbolicFormType) -> List[SymbolicEntity]:
        """
        Get all entities of a specific form type.
        
        Args:
            form_type: Type of symbolic form
            
        Returns:
            List of entities
        """
        return list(self.form_indices[form_type].values())
    
    def get_symbol_idx(self, symbol: str) -> int:
        """
        Get the index for a symbol.
        
        Args:
            symbol: Symbol name
            
        Returns:
            Index for the symbol or the unknown index
        """
        return self.symbol_to_idx.get(symbol, self.symbol_to_idx["<UNK>"])
    
    def get_symbol_from_idx(self, idx: int) -> str:
        """
        Get the symbol for an index.
        
        Args:
            idx: Symbol index
            
        Returns:
            Symbol name or the unknown symbol
        """
        return self.idx_to_symbol.get(idx, "<UNK>")
    
    def size(self) -> int:
        """
        Get the size of the vocabulary.
        
        Returns:
            Number of entities in the vocabulary
        """
        return len(self.entities)
    
    def size_by_form(self, form_type: SymbolicFormType) -> int:
        """
        Get the size of the vocabulary for a specific form type.
        
        Args:
            form_type: Type of symbolic form
            
        Returns:
            Number of entities of the given form type
        """
        return len(self.form_indices[form_type])
    
    def save(self, file_path: str) -> None:
        """
        Save the vocabulary to a file.
        
        Args:
            file_path: Path to save the vocabulary
        """
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        
        # Prepare data for serialization
        data = {
            "name": self.name,
            "version": self.version,
            "symbol_to_idx": self.symbol_to_idx,
            "entities": []
        }
        
        # Serialize entities
        for entity in self.entities.values():
            entity_data = {
                "name": entity.name,
                "form_type": entity.form_type.name
            }
            
            # Add type-specific attributes
            if isinstance(entity, PredicateLogicSymbol):
                entity_data["class"] = "PredicateLogicSymbol"
                entity_data["arity"] = entity.arity
                entity_data["is_function"] = entity.is_function
            elif isinstance(entity, DescriptionLogicConcept):
                entity_data["class"] = "DescriptionLogicConcept"
                entity_data["parent_concepts"] = entity.parent_concepts
            elif isinstance(entity, DescriptionLogicRole):
                entity_data["class"] = "DescriptionLogicRole"
                entity_data["domain"] = entity.domain
                entity_data["range"] = entity.range
                entity_data["is_transitive"] = entity.is_transitive
                entity_data["is_functional"] = entity.is_functional
                entity_data["is_inverse_functional"] = entity.is_inverse_functional
                entity_data["is_symmetric"] = entity.is_symmetric
            elif isinstance(entity, KnowledgeGraphEntity):
                entity_data["class"] = "KnowledgeGraphEntity"
                entity_data["entity_type"] = entity.entity_type
                entity_data["attributes"] = entity.attributes
            elif isinstance(entity, KnowledgeGraphRelation):
                entity_data["class"] = "KnowledgeGraphRelation"
                entity_data["source"] = entity.source.name
                entity_data["target"] = entity.target.name
                entity_data["weight"] = entity.weight
                entity_data["attributes"] = entity.attributes
            elif isinstance(entity, Rule):
                entity_data["class"] = "Rule"
                entity_data["antecedent"] = [str(expr) for expr in entity.antecedent]
                entity_data["consequent"] = str(entity.consequent)
                entity_data["confidence"] = entity.confidence
            
            data["entities"].append(entity_data)
        
        with open(file_path, 'w') as f:
            json.dump(data, f, indent=2)
            
        logger.info(f"Saved vocabulary with {len(self.entities)} entities to {file_path}")
    
    def load(self, file_path: str, expression_parser: ExpressionParser = None) -> None:
        """
        Load the vocabulary from a file.
        
        Args:
            file_path: Path to load the vocabulary from
            expression_parser: Parser for expressions (required for rules)
        """
        with open(file_path, 'r') as f:
            data = json.load(f)
        
        # Clear existing data
        self.entities = {}
        self.form_indices = {form_type: {} for form_type in SymbolicFormType}
        
        # Load basic attributes
        self.name = data.get("name", "Default Vocabulary")
        self.version = data.get("version", 0)
        self.symbol_to_idx = data.get("symbol_to_idx", {"<PAD>": 0, "<UNK>": 1})
        self.idx_to_symbol = {int(k): v for k, v in 
                             {v: k for k, v in self.symbol_to_idx.items()}.items()}
        
        # Create parser if not provided
        if expression_parser is None and any(e["class"] == "Rule" for e in data.get("entities", [])):
            expression_parser = ExpressionParser()
        
        # Track entities by name for resolving references
        entity_map = {}
        
        # First pass: create basic entities
        for entity_data in data.get("entities", []):
            entity_class = entity_data.get("class")
            name = entity_data.get("name")
            form_type_name = entity_data.get("form_type")
            form_type = SymbolicFormType[form_type_name]
            
            if entity_class == "PredicateLogicSymbol":
                entity = PredicateLogicSymbol(
                    name=name,
                    arity=entity_data.get("arity", 0),
                    is_function=entity_data.get("is_function", False)
                )
                entity_map[name] = entity
            elif entity_class == "DescriptionLogicConcept":
                entity = DescriptionLogicConcept(
                    name=name,
                    parent_concepts=entity_data.get("parent_concepts", [])
                )
                entity_map[name] = entity
            elif entity_class == "DescriptionLogicRole":
                entity = DescriptionLogicRole(
                    name=name,
                    domain=entity_data.get("domain", ""),
                    range_=entity_data.get("range", ""),
                    is_transitive=entity_data.get("is_transitive", False),
                    is_functional=entity_data.get("is_functional", False),
                    is_inverse_functional=entity_data.get("is_inverse_functional", False),
                    is_symmetric=entity_data.get("is_symmetric", False)
                )
                entity_map[name] = entity
            elif entity_class == "KnowledgeGraphEntity":
                entity = KnowledgeGraphEntity(
                    name=name,
                    entity_type=entity_data.get("entity_type", "Entity"),
                    attributes=entity_data.get("attributes", {})
                )
                entity_map[name] = entity
        
        # Second pass: create entities with references
        for entity_data in data.get("entities", []):
            entity_class = entity_data.get("class")
            name = entity_data.get("name")
            
            if entity_class == "KnowledgeGraphRelation":
                source_name = entity_data.get("source")
                target_name = entity_data.get("target")
                
                if source_name in entity_map and target_name in entity_map:
                    source = entity_map[source_name]
                    target = entity_map[target_name]
                    
                    entity = KnowledgeGraphRelation(
                        name=name,
                        source=source,
                        target=target,
                        weight=entity_data.get("weight", 1.0),
                        attributes=entity_data.get("attributes", {})
                    )
                    entity_map[name] = entity
            elif entity_class == "Rule" and expression_parser is not None:
                antecedent_strs = entity_data.get("antecedent", [])
                consequent_str = entity_data.get("consequent", "")
                
                try:
                    antecedent = [expression_parser.parse(expr_str) for expr_str in antecedent_strs]
                    consequent = expression_parser.parse(consequent_str)
                    
                    entity = Rule(
                        name=name,
                        antecedent=antecedent,
                        consequent=consequent,
                        confidence=entity_data.get("confidence", 1.0)
                    )
                    entity_map[name] = entity
                except Exception as e:
                    logger.warning(f"Failed to parse rule {name}: {str(e)}")
        
        # Add entities to vocabulary
        for entity in entity_map.values():
            self.add_entity(entity)
            
        logger.info(f"Loaded vocabulary with {len(self.entities)} entities from {file_path}")


@dataclass
class SymbolicVector:
    """
    Vector representation of a symbolic entity.
    """
    entity: SymbolicEntity
    vector: torch.Tensor
    confidence: float = 1.0
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def distance(self, other: 'SymbolicVector') -> float:
        """
        Compute distance to another symbolic vector.
        
        Args:
            other: Other symbolic vector
            
        Returns:
            Distance between vectors
        """
        return F.pairwise_distance(
            self.vector.unsqueeze(0), 
            other.vector.unsqueeze(0)
        ).item()
    
    def similarity(self, other: 'SymbolicVector') -> float:
        """
        Compute similarity to another symbolic vector.
        
        Args:
            other: Other symbolic vector
            
        Returns:
            Similarity between vectors (cosine similarity)
        """
        return F.cosine_similarity(
            self.vector.unsqueeze(0), 
            other.vector.unsqueeze(0)
        ).item()


class SymbolicEmbedding(nn.Module):
    """
    Base class for embedding symbolic entities in a continuous space.
    """
    
    def __init__(self, vocab: SymbolicVocabulary, embedding_dim: int, 
                form_types: List[SymbolicFormType] = None,
                embedding_method: EmbeddingMethod = EmbeddingMethod.ATOMIC,
                device: str = 'cuda' if torch.cuda.is_available() else 'cpu'):
        """
        Initialize symbolic embedding.
        
        Args:
            vocab: Symbolic vocabulary
            embedding_dim: Dimension of embeddings
            form_types: Types of symbolic forms to embed (None for all)
            embedding_method: Method for embedding symbolic knowledge
            device: Device to use for tensors
        """
        super().__init__()
        
        self.vocab = vocab
        self.embedding_dim = embedding_dim
        self.form_types = form_types or list(SymbolicFormType)
        self.embedding_method = embedding_method
        self.device = device
        
        # Create embedding for symbols
        self.symbol_embedding = nn.Embedding(
            num_embeddings=len(vocab.symbol_to_idx),
            embedding_dim=embedding_dim,
            padding_idx=vocab.symbol_to_idx["<PAD>"]
        ).to(device)
        
        # Initialize weights
        self._init_weights()
        
        # Entity-specific encoders
        if embedding_method == EmbeddingMethod.COMPOSITIONAL:
            self._init_compositional_encoders()
    
    def _init_weights(self) -> None:
        """Initialize weights with Xavier uniform initialization."""
        nn.init.xavier_uniform_(self.symbol_embedding.weight)
    
    def _init_compositional_encoders(self) -> None:
        """Initialize encoders for compositional embedding."""
        # Encoders for different entity types
        self.predicate_encoder = nn.Sequential(
            nn.Linear(self.embedding_dim * 2, self.embedding_dim),
            nn.ReLU(),
            nn.Linear(self.embedding_dim, self.embedding_dim)
        ).to(self.device)
        
        self.concept_encoder = nn.Sequential(
            nn.Linear(self.embedding_dim * 2, self.embedding_dim),
            nn.ReLU(),
            nn.Linear(self.embedding_dim, self.embedding_dim)
        ).to(self.device)
        
        self.role_encoder = nn.Sequential(
            nn.Linear(self.embedding_dim * 3, self.embedding_dim),
            nn.ReLU(),
            nn.Linear(self.embedding_dim, self.embedding_dim)
        ).to(self.device)
        
        self.relation_encoder = nn.Sequential(
            nn.Linear(self.embedding_dim * 3, self.embedding_dim),
            nn.ReLU(),
            nn.Linear(self.embedding_dim, self.embedding_dim)
        ).to(self.device)
        
        self.rule_encoder = nn.GRU(
            input_size=self.embedding_dim,
            hidden_size=self.embedding_dim,
            batch_first=True
        ).to(self.device)
    
    def forward(self, entities: List[SymbolicEntity]) -> torch.Tensor:
        """
        Compute embeddings for symbolic entities.
        
        Args:
            entities: List of symbolic entities
            
        Returns:
            Tensor of embeddings
        """
        if self.embedding_method == EmbeddingMethod.ATOMIC:
            return self._atomic_embedding(entities)
        elif self.embedding_method == EmbeddingMethod.COMPOSITIONAL:
            return self._compositional_embedding(entities)
        elif self.embedding_method == EmbeddingMethod.CONTEXTUAL:
            return self._contextual_embedding(entities)
        elif self.embedding_method == EmbeddingMethod.RELATIONAL:
            return self._relational_embedding(entities)
        elif self.embedding_method == EmbeddingMethod.HIERARCHICAL:
            return self._hierarchical_embedding(entities)
        else:
            raise ValueError(f"Unsupported embedding method: {self.embedding_method}")
    
    def _atomic_embedding(self, entities: List[SymbolicEntity]) -> torch.Tensor:
        """
        Compute atomic embeddings for entities.
        
        Args:
            entities: List of symbolic entities
            
        Returns:
            Tensor of embeddings
        """
        # Get indices for entities
        indices = []
        for entity in entities:
            idx = self.vocab.get_symbol_idx(entity.name)
            indices.append(idx)
            
        # Convert to tensor
        indices_tensor = torch.tensor(indices, dtype=torch.long, device=self.device)
        
        # Get embeddings
        embeddings = self.symbol_embedding(indices_tensor)
        
        return embeddings
    
    def _compositional_embedding(self, entities: List[SymbolicEntity]) -> torch.Tensor:
        """
        Compute compositional embeddings for entities.
        
        Args:
            entities: List of symbolic entities
            
        Returns:
            Tensor of embeddings
        """
        # Initialize embeddings
        embeddings = []
        
        for entity in entities:
            if isinstance(entity, PredicateLogicSymbol):
                # Combine name and arity
                name_idx = self.vocab.get_symbol_idx(entity.name)
                name_emb = self.symbol_embedding(torch.tensor([name_idx], device=self.device))
                
                # Arity as one-hot
                arity_emb = torch.zeros(self.embedding_dim, device=self.device)
                if entity.arity < self.embedding_dim:
                    arity_emb[entity.arity] = 1.0
                
                # Combine
                inputs = torch.cat([name_emb.squeeze(0), arity_emb])
                embeddings.append(self.predicate_encoder(inputs.unsqueeze(0)))
                
            elif isinstance(entity, DescriptionLogicConcept):
                # Embed name
                name_idx = self.vocab.get_symbol_idx(entity.name)
                name_emb = self.symbol_embedding(torch.tensor([name_idx], device=self.device))
                
                # Embed parent concepts (if any)
                if entity.parent_concepts:
                    parent_embs = []
                    for parent in entity.parent_concepts:
                        parent_idx = self.vocab.get_symbol_idx(parent)
                        parent_emb = self.symbol_embedding(torch.tensor([parent_idx], device=self.device))
                        parent_embs.append(parent_emb)
                    
                    # Average parent embeddings
                    parent_emb = torch.mean(torch.stack(parent_embs), dim=0)
                else:
                    # Default parent embedding
                    parent_emb = torch.zeros_like(name_emb)
                
                # Combine
                inputs = torch.cat([name_emb.squeeze(0), parent_emb.squeeze(0)])
                embeddings.append(self.concept_encoder(inputs.unsqueeze(0)))
                
            elif isinstance(entity, DescriptionLogicRole):
                # Embed name, domain, and range
                name_idx = self.vocab.get_symbol_idx(entity.name)
                domain_idx = self.vocab.get_symbol_idx(entity.domain)
                range_idx = self.vocab.get_symbol_idx(entity.range)
                
                name_emb = self.symbol_embedding(torch.tensor([name_idx], device=self.device))
                domain_emb = self.symbol_embedding(torch.tensor([domain_idx], device=self.device))
                range_emb = self.symbol_embedding(torch.tensor([range_idx], device=self.device))
                
                # Combine
                inputs = torch.cat([
                    name_emb.squeeze(0), 
                    domain_emb.squeeze(0), 
                    range_emb.squeeze(0)
                ])
                embeddings.append(self.role_encoder(inputs.unsqueeze(0)))
                
            elif isinstance(entity, KnowledgeGraphRelation):
                # Embed name, source, and target
                name_idx = self.vocab.get_symbol_idx(entity.name)
                source_idx = self.vocab.get_symbol_idx(entity.source.name)
                target_idx = self.vocab.get_symbol_idx(entity.target.name)
                
                name_emb = self.symbol_embedding(torch.tensor([name_idx], device=self.device))
                source_emb = self.symbol_embedding(torch.tensor([source_idx], device=self.device))
                target_emb = self.symbol_embedding(torch.tensor([target_idx], device=self.device))
                
                # Combine
                inputs = torch.cat([
                    name_emb.squeeze(0), 
                    source_emb.squeeze(0), 
                    target_emb.squeeze(0)
                ])
                embeddings.append(self.relation_encoder(inputs.unsqueeze(0)))
                
            elif isinstance(entity, Rule):
                # Embed antecedent and consequent
                antecedent_embs = []
                for expr in entity.antecedent:
                    expr_str = str(expr)
                    expr_idx = self.vocab.get_symbol_idx(expr_str)
                    expr_emb = self.symbol_embedding(torch.tensor([expr_idx], device=self.device))
                    antecedent_embs.append(expr_emb.squeeze(0))
                
                consequent_str = str(entity.consequent)
                consequent_idx = self.vocab.get_symbol_idx(consequent_str)
                consequent_emb = self.symbol_embedding(torch.tensor([consequent_idx], device=self.device))
                
                # Combine antecedent embeddings with consequent
                all_embs = antecedent_embs + [consequent_emb.squeeze(0)]
                inputs = torch.stack(all_embs).unsqueeze(0)  # [1, num_expr, emb_dim]
                
                # Pass through GRU
                _, hidden = self.rule_encoder(inputs)
                embeddings.append(hidden.squeeze(0))
                
            else:
                # Default to atomic embedding for other entity types
                idx = self.vocab.get_symbol_idx(entity.name)
                emb = self.symbol_embedding(torch.tensor([idx], device=self.device))
                embeddings.append(emb)
        
        # Stack embeddings
        if embeddings:
            return torch.cat(embeddings, dim=0)
        else:
            return torch.zeros((0, self.embedding_dim), device=self.device)
    
    def _contextual_embedding(self, entities: List[SymbolicEntity]) -> torch.Tensor:
        """
        Compute contextual embeddings for entities.
        
        Args:
            entities: List of symbolic entities
            
        Returns:
            Tensor of embeddings
        """
        # This is a simplified implementation
        # In a real system, this would involve attention mechanisms and context
        
        # Start with atomic embeddings
        base_embeddings = self._atomic_embedding(entities)
        
        # For now, just return base embeddings
        # In a real implementation, we would modify these based on context
        return base_embeddings
    
    def _relational_embedding(self, entities: List[SymbolicEntity]) -> torch.Tensor:
        """
        Compute relational embeddings for entities.
        
        Args:
            entities: List of symbolic entities
            
        Returns:
            Tensor of embeddings
        """
        # This is a simplified implementation
        # In a real system, this would involve graph neural networks or similar
        
        # Start with atomic embeddings
        base_embeddings = self._atomic_embedding(entities)
        
        # For now, just return base embeddings
        # In a real implementation, we would modify these based on relations
        return base_embeddings
    
    def _hierarchical_embedding(self, entities: List[SymbolicEntity]) -> torch.Tensor:
        """
        Compute hierarchical embeddings for entities.
        
        Args:
            entities: List of symbolic entities
            
        Returns:
            Tensor of embeddings
        """
        # This is a simplified implementation
        # In a real system, this would involve hierarchical encoding
        
        # Start with atomic embeddings
        base_embeddings = self._atomic_embedding(entities)
        
        # For now, just return base embeddings
        # In a real implementation, we would modify these based on hierarchy
        return base_embeddings
    
    def get_embedding(self, entity: SymbolicEntity) -> torch.Tensor:
        """
        Get embedding for a single entity.
        
        Args:
            entity: Symbolic entity
            
        Returns:
            Embedding tensor
        """
        return self.forward([entity]).squeeze(0)
    
    def get_similarity(self, entity1: SymbolicEntity, entity2: SymbolicEntity) -> float:
        """
        Compute similarity between two entities.
        
        Args:
            entity1: First entity
            entity2: Second entity
            
        Returns:
            Similarity between entities
        """
        emb1 = self.get_embedding(entity1)
        emb2 = self.get_embedding(entity2)
        
        return F.cosine_similarity(emb1.unsqueeze(0), emb2.unsqueeze(0)).item()
    
    def get_nearest_neighbors(self, entity: SymbolicEntity, k: int = 5,
                           form_type: Optional[SymbolicFormType] = None) -> List[Tuple[SymbolicEntity, float]]:
        """
        Find the nearest neighbors of an entity.
        
        Args:
            entity: Query entity
            k: Number of neighbors to return
            form_type: Optional form type to filter candidates
            
        Returns:
            List of (entity, similarity) pairs
        """
        query_emb = self.get_embedding(entity)
        
        # Get candidate entities
        if form_type is not None:
            candidates = self.vocab.get_entities_by_form(form_type)
        else:
            candidates = list(self.vocab.entities.values())
            
        # Remove query entity from candidates
        candidates = [c for c in candidates if c.name != entity.name]
        
        # If no candidates, return empty list
        if not candidates:
            return []
            
        # Get embeddings for candidates
        candidate_embs = self.forward(candidates)
        
        # Compute similarities
        similarities = F.cosine_similarity(
            query_emb.unsqueeze(0).expand(len(candidates), -1),
            candidate_embs
        )
        
        # Get top-k indices
        top_k_indices = torch.topk(similarities, min(k, len(similarities)), dim=0).indices
        
        # Create result pairs
        results = []
        for idx in top_k_indices:
            candidate = candidates[idx]
            similarity = similarities[idx].item()
            results.append((candidate, similarity))
            
        return results
    
    def save(self, file_path: str) -> None:
        """
        Save the embedding model.
        
        Args:
            file_path: Path to save the model
        """
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        
        # Prepare model state
        state = {
            'embedding_dim': self.embedding_dim,
            'form_types': [ft.name for ft in self.form_types],
            'embedding_method': self.embedding_method.name,
            'state_dict': self.state_dict()
        }
        
        # Save model state
        torch.save(state, file_path)
        
        logger.info(f"Saved symbolic embedding model to {file_path}")
    
    def load(self, file_path: str) -> None:
        """
        Load the embedding model.
        
        Args:
            file_path: Path to load the model from
        """
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"Model file not found: {file_path}")
            
        # Load model state
        state = torch.load(file_path, map_location=self.device)
        
        # Update attributes
        self.embedding_dim = state['embedding_dim']
        self.form_types = [SymbolicFormType[name] for name in state['form_types']]
        self.embedding_method = EmbeddingMethod[state['embedding_method']]
        
        # Load state dict
        self.load_state_dict(state['state_dict'])
        
        logger.info(f"Loaded symbolic embedding model from {file_path}")


class SymbolicContrastiveExtractor(nn.Module):
    """
    Neural network model for extracting symbolic representations from neural representations.
    Uses contrastive learning to align neural and symbolic spaces.
    """
    
    def __init__(self, neural_dim: int, embedding_model: SymbolicEmbedding,
                hidden_dim: int = 512, temperature: float = 0.07,
                device: str = 'cuda' if torch.cuda.is_available() else 'cpu'):
        """
        Initialize symbolic contrastive extractor.
        
        Args:
            neural_dim: Dimension of neural representations
            embedding_model: Symbolic embedding model
            hidden_dim: Dimension of hidden layers
            temperature: Temperature parameter for contrastive loss
            device: Device to use for tensors
        """
        super().__init__()
        
        self.neural_dim = neural_dim
        self.embedding_model = embedding_model
        self.embedding_dim = embedding_model.embedding_dim
        self.hidden_dim = hidden_dim
        self.temperature = temperature
        self.device = device
        
        # Neural encoder
        self.neural_encoder = nn.Sequential(
            nn.Linear(neural_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, self.embedding_dim)
        ).to(device)
        
        # Training metrics
        self.training_metrics = {
            'contrastive_loss': [],
            'accuracy': []
        }
    
    def forward(self, neural_repr: torch.Tensor) -> torch.Tensor:
        """
        Encode neural representation into symbolic embedding space.
        
        Args:
            neural_repr: Neural representation tensor
            
        Returns:
            Encoded representation in symbolic space
        """
        # Ensure input is on the device
        neural_repr = neural_repr.to(self.device)
        
        # Encode neural representation
        encoded = self.neural_encoder(neural_repr)
        
        # Normalize
        encoded = F.normalize(encoded, p=2, dim=1)
        
        return encoded
    
    def get_nearest_symbolic(self, neural_repr: torch.Tensor, k: int = 1,
                          form_type: Optional[SymbolicFormType] = None) -> List[Tuple[SymbolicEntity, float]]:
        """
        Find the nearest symbolic entities to a neural representation.
        
        Args:
            neural_repr: Neural representation tensor
            k: Number of neighbors to return
            form_type: Optional form type to filter candidates
            
        Returns:
            List of (entity, similarity) pairs
        """
        # Encode neural representation
        encoded = self.forward(neural_repr.unsqueeze(0))
        
        # Get candidate entities
        if form_type is not None:
            candidates = self.embedding_model.vocab.get_entities_by_form(form_type)
        else:
            candidates = list(self.embedding_model.vocab.entities.values())
            
        # If no candidates, return empty list
        if not candidates:
            return []
            
        # Get embeddings for candidates
        with torch.no_grad():
            candidate_embs = self.embedding_model.forward(candidates)
            
        # Normalize
        candidate_embs = F.normalize(candidate_embs, p=2, dim=1)
        
        # Compute similarities
        similarities = F.cosine_similarity(
            encoded.expand(len(candidates), -1),
            candidate_embs
        )
        
        # Get top-k indices
        top_k_indices = torch.topk(similarities, min(k, len(similarities)), dim=0).indices
        
        # Create result pairs
        results = []
        for idx in top_k_indices:
            candidate = candidates[idx]
            similarity = similarities[idx].item()
            results.append((candidate, similarity))
            
        return results
    
    def train_model(self, neural_data: torch.Tensor, symbolic_entities: List[SymbolicEntity], 
                 num_epochs: int = 100, batch_size: int = 32, learning_rate: float = 1e-4,
                 weight_decay: float = 1e-5, log_interval: int = 10) -> Dict[str, List[float]]:
        """
        Train the extractor model.
        
        Args:
            neural_data: Neural representation data
            symbolic_entities: Corresponding symbolic entities
            num_epochs: Number of training epochs
            batch_size: Batch size
            learning_rate: Learning rate
            weight_decay: Weight decay (L2 regularization)
            log_interval: Interval for logging training progress
            
        Returns:
            Dictionary of training metrics
        """
        # Set models to training mode
        self.train()
        self.embedding_model.eval()  # Keep symbolic embeddings fixed
        
        # Create dataset
        dataset = list(zip(neural_data, symbolic_entities))
        
        # Create optimizer
        optimizer = torch.optim.Adam(
            self.parameters(),
            lr=learning_rate,
            weight_decay=weight_decay
        )
        
        # Training metrics
        epoch_metrics = {
            'contrastive_loss': [],
            'accuracy': []
        }
        
        # Training loop
        for epoch in range(num_epochs):
            # Shuffle dataset
            random.shuffle(dataset)
            
            # Create batches
            batches = [dataset[i:i+batch_size] for i in range(0, len(dataset), batch_size)]
            
            # Initialize metrics
            epoch_loss = 0.0
            epoch_acc = 0.0
            
            for batch in batches:
                # Unzip batch
                neural_batch, symbolic_batch = zip(*batch)
                
                # Convert to tensors
                neural_tensor = torch.stack(neural_batch).to(self.device)
                
                # Get symbolic embeddings
                with torch.no_grad():
                    symbolic_embeddings = self.embedding_model.forward(list(symbolic_batch))
                    symbolic_embeddings = F.normalize(symbolic_embeddings, p=2, dim=1)
                
                # Forward pass
                encoded = self.forward(neural_tensor)
                
                # Compute similarity matrix
                sim_matrix = torch.matmul(encoded, symbolic_embeddings.t()) / self.temperature
                
                # Create targets (diagonal elements should be 1, others 0)
                targets = torch.eye(len(batch), device=self.device)
                
                # Compute contrastive loss
                loss = -torch.mean(torch.sum(
                    targets * F.log_softmax(sim_matrix, dim=1), dim=1))
                
                # Backward pass and optimization
                optimizer.zero_grad()
                loss.backward()
                optimizer.step()
                
                # Compute accuracy
                predictions = torch.argmax(sim_matrix, dim=1)
                ground_truth = torch.arange(len(batch), device=self.device)
                accuracy = (predictions == ground_truth).float().mean().item()
                
                # Update metrics
                epoch_loss += loss.item()
                epoch_acc += accuracy
            
            # Average metrics
            epoch_loss /= len(batches)
            epoch_acc /= len(batches)
            
            # Store metrics
            epoch_metrics['contrastive_loss'].append(epoch_loss)
            epoch_metrics['accuracy'].append(epoch_acc)
            
            self.training_metrics['contrastive_loss'].append(epoch_loss)
            self.training_metrics['accuracy'].append(epoch_acc)
            
            # Log progress
            if (epoch + 1) % log_interval == 0:
                logger.info(f"Epoch {epoch+1}/{num_epochs}, " +
                          f"Loss: {epoch_loss:.4f}, " +
                          f"Accuracy: {epoch_acc:.4f}")
        
        return epoch_metrics
    
    def save(self, file_path: str) -> None:
        """
        Save the extractor model.
        
        Args:
            file_path: Path to save the model
        """
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        
        # Prepare model state
        state = {
            'neural_dim': self.neural_dim,
            'embedding_dim': self.embedding_dim,
            'hidden_dim': self.hidden_dim,
            'temperature': self.temperature,
            'state_dict': self.state_dict(),
            'training_metrics': self.training_metrics
        }
        
        # Save model state
        torch.save(state, file_path)
        
        logger.info(f"Saved symbolic contrastive extractor model to {file_path}")
    
    def load(self, file_path: str) -> None:
        """
        Load the extractor model.
        
        Args:
            file_path: Path to load the model from
        """
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"Model file not found: {file_path}")
            
        # Load model state
        state = torch.load(file_path, map_location=self.device)
        
        # Update attributes
        self.neural_dim = state['neural_dim']
        self.embedding_dim = state['embedding_dim']
        self.hidden_dim = state['hidden_dim']
        self.temperature = state['temperature']
        self.training_metrics = state.get('training_metrics', {
            'contrastive_loss': [],
            'accuracy': []
        })
        
        # Recreate model if dimensions changed
        if (self.neural_encoder[0].in_features != self.neural_dim or
            self.neural_encoder[-1].out_features != self.embedding_dim):
            self.neural_encoder = nn.Sequential(
                nn.Linear(self.neural_dim, self.hidden_dim),
                nn.ReLU(),
                nn.Linear(self.hidden_dim, self.hidden_dim),
                nn.ReLU(),
                nn.Linear(self.hidden_dim, self.embedding_dim)
            ).to(self.device)
        
        # Load state dict
        self.load_state_dict(state['state_dict'])
        
        logger.info(f"Loaded symbolic contrastive extractor model from {file_path}")


class SymbolicEncoder(nn.Module):
    """
    Neural network model for encoding symbolic structures into neural representations.
    """
    
    def __init__(self, vocab: SymbolicVocabulary, neural_dim: int, embedding_dim: int = 256,
                hidden_dim: int = 512, form_types: List[SymbolicFormType] = None,
                device: str = 'cuda' if torch.cuda.is_available() else 'cpu'):
        """
        Initialize symbolic encoder.
        
        Args:
            vocab: Symbolic vocabulary
            neural_dim: Dimension of output neural representations
            embedding_dim: Dimension of symbolic embeddings
            hidden_dim: Dimension of hidden layers
            form_types: Types of symbolic forms to encode (None for all)
            device: Device to use for tensors
        """
        super().__init__()
        
        self.vocab = vocab
        self.neural_dim = neural_dim
        self.embedding_dim = embedding_dim
        self.hidden_dim = hidden_dim
        self.form_types = form_types or list(SymbolicFormType)
        self.device = device
        
        # Create embedding layer for symbols
        self.embedding = nn.Embedding(
            num_embeddings=len(vocab.symbol_to_idx),
            embedding_dim=embedding_dim,
            padding_idx=vocab.symbol_to_idx["<PAD>"]
        ).to(device)
        
        # Encoder for different entity types
        self._init_type_encoders()
        
        # Final encoder to neural space
        self.final_encoder = nn.Sequential(
            nn.Linear(embedding_dim, hidden_dim),
            nn.ReLU(),
            nn.BatchNorm1d(hidden_dim),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.BatchNorm1d(hidden_dim),
            nn.Linear(hidden_dim, neural_dim)
        ).to(device)
        
        # Initialize weights
        self._init_weights()
    
    def _init_weights(self) -> None:
        """Initialize weights with Xavier uniform initialization."""
        nn.init.xavier_uniform_(self.embedding.weight)
        
        # Initialize type encoder weights
        for module in [
            self.predicate_encoder, self.concept_encoder, self.role_encoder,
            self.relation_encoder, self.rule_encoder
        ]:
            for layer in module:
                if isinstance(layer, nn.Linear):
                    nn.init.xavier_uniform_(layer.weight)
                    if layer.bias is not None:
                        nn.init.zeros_(layer.bias)
                elif isinstance(layer, (nn.GRU, nn.LSTM)):
                    for name, param in layer.named_parameters():
                        if 'weight' in name:
                            nn.init.xavier_uniform_(param)
                        elif 'bias' in name:
                            nn.init.zeros_(param)
    
    def _init_type_encoders(self) -> None:
        """Initialize encoders for different entity types."""
        # Predicate logic symbols
        self.predicate_encoder = nn.Sequential(
            nn.Linear(self.embedding_dim + 1, self.hidden_dim),  # +1 for arity
            nn.ReLU(),
            nn.Linear(self.hidden_dim, self.embedding_dim)
        ).to(self.device)
        
        # Description logic concepts
        self.concept_encoder = nn.Sequential(
            nn.Linear(self.embedding_dim * 2, self.hidden_dim),  # Original + parents
            nn.ReLU(),
            nn.Linear(self.hidden_dim, self.embedding_dim)
        ).to(self.device)
        
        # Description logic roles
        self.role_encoder = nn.Sequential(
            nn.Linear(self.embedding_dim * 3 + 4, self.hidden_dim),  # Name + domain + range + props
            nn.ReLU(),
            nn.Linear(self.hidden_dim, self.embedding_dim)
        ).to(self.device)
        
        # Knowledge graph relations
        self.relation_encoder = nn.Sequential(
            nn.Linear(self.embedding_dim * 3 + 1, self.hidden_dim),  # Name + source + target + weight
            nn.ReLU(),
            nn.Linear(self.hidden_dim, self.embedding_dim)
        ).to(self.device)
        
        # Rules (using GRU for variable-length antecedents)
        self.rule_encoder = nn.Sequential(
            nn.GRU(self.embedding_dim, self.hidden_dim, batch_first=True),
            nn.ReLU(),
            nn.Linear(self.hidden_dim, self.embedding_dim)
        ).to(self.device)
    
    def forward(self, entities: List[SymbolicEntity]) -> torch.Tensor:
        """
        Encode symbolic entities into neural representations.
        
        Args:
            entities: List of symbolic entities
            
        Returns:
            Tensor of neural representations
        """
        # Encode each entity based on its type
        entity_embeddings = []
        
        for entity in entities:
            # Get embedding based on entity type
            if isinstance(entity, PredicateLogicSymbol):
                embedding = self._encode_predicate(entity)
            elif isinstance(entity, DescriptionLogicConcept):
                embedding = self._encode_concept(entity)
            elif isinstance(entity, DescriptionLogicRole):
                embedding = self._encode_role(entity)
            elif isinstance(entity, KnowledgeGraphRelation):
                embedding = self._encode_relation(entity)
            elif isinstance(entity, Rule):
                embedding = self._encode_rule(entity)
            else:
                # Default encoding for other entity types
                embedding = self._encode_default(entity)
                
            entity_embeddings.append(embedding)
            
        # Stack embeddings
        if entity_embeddings:
            stacked_embeddings = torch.stack(entity_embeddings)
            
            # Encode to neural space
            neural_reprs = self.final_encoder(stacked_embeddings)
            
            return neural_reprs
        else:
            return torch.zeros((0, self.neural_dim), device=self.device)
    
    def _encode_predicate(self, entity: PredicateLogicSymbol) -> torch.Tensor:
        """
        Encode a predicate logic symbol.
        
        Args:
            entity: Predicate logic symbol
            
        Returns:
            Encoded representation
        """
        # Get name embedding
        name_idx = self.vocab.get_symbol_idx(entity.name)
        name_emb = self.embedding(torch.tensor([name_idx], device=self.device)).squeeze(0)
        
        # Create feature vector with arity (normalized)
        arity = torch.tensor([entity.arity / 10.0], device=self.device)  # Normalize arity
        
        # Combine features
        features = torch.cat([name_emb, arity])
        
        # Encode
        embedding = self.predicate_encoder[0](features)
        embedding = F.relu(embedding)
        embedding = self.predicate_encoder[2](embedding)
        
        return embedding
    
    def _encode_concept(self, entity: DescriptionLogicConcept) -> torch.Tensor:
        """
        Encode a description logic concept.
        
        Args:
            entity: Description logic concept
            
        Returns:
            Encoded representation
        """
        # Get name embedding
        name_idx = self.vocab.get_symbol_idx(entity.name)
        name_emb = self.embedding(torch.tensor([name_idx], device=self.device)).squeeze(0)
        
        # Get parent concept embeddings
        if entity.parent_concepts:
            parent_embs = []
            for parent in entity.parent_concepts:
                parent_idx = self.vocab.get_symbol_idx(parent)
                parent_emb = self.embedding(torch.tensor([parent_idx], device=self.device)).squeeze(0)
                parent_embs.append(parent_emb)
                
            # Average parent embeddings
            parent_emb = torch.mean(torch.stack(parent_embs), dim=0)
        else:
            # Default parent embedding
            parent_emb = torch.zeros_like(name_emb)
            
        # Combine features
        features = torch.cat([name_emb, parent_emb])
        
        # Encode
        embedding = self.concept_encoder[0](features)
        embedding = F.relu(embedding)
        embedding = self.concept_encoder[2](embedding)
        
        return embedding
    
    def _encode_role(self, entity: DescriptionLogicRole) -> torch.Tensor:
        """
        Encode a description logic role.
        
        Args:
            entity: Description logic role
            
        Returns:
            Encoded representation
        """
        # Get name, domain, and range embeddings
        name_idx = self.vocab.get_symbol_idx(entity.name)
        domain_idx = self.vocab.get_symbol_idx(entity.domain)
        range_idx = self.vocab.get_symbol_idx(entity.range)
        
        name_emb = self.embedding(torch.tensor([name_idx], device=self.device)).squeeze(0)
        domain_emb = self.embedding(torch.tensor([domain_idx], device=self.device)).squeeze(0)
        range_emb = self.embedding(torch.tensor([range_idx], device=self.device)).squeeze(0)
        
        # Create feature vector for properties
        props = torch.tensor(
            [
                float(entity.is_transitive),
                float(entity.is_functional),
                float(entity.is_inverse_functional),
                float(entity.is_symmetric)
            ],
            device=self.device
        )
        
        # Combine features
        features = torch.cat([name_emb, domain_emb, range_emb, props])
        
        # Encode
        embedding = self.role_encoder[0](features)
        embedding = F.relu(embedding)
        embedding = self.role_encoder[2](embedding)
        
        return embedding
    
    def _encode_relation(self, entity: KnowledgeGraphRelation) -> torch.Tensor:
        """
        Encode a knowledge graph relation.
        
        Args:
            entity: Knowledge graph relation
            
        Returns:
            Encoded representation
        """
        # Get name, source, and target embeddings
        name_idx = self.vocab.get_symbol_idx(entity.name)
        source_idx = self.vocab.get_symbol_idx(entity.source.name)
        target_idx = self.vocab.get_symbol_idx(entity.target.name)
        
        name_emb = self.embedding(torch.tensor([name_idx], device=self.device)).squeeze(0)
        source_emb = self.embedding(torch.tensor([source_idx], device=self.device)).squeeze(0)
        target_emb = self.embedding(torch.tensor([target_idx], device=self.device)).squeeze(0)
        
        # Create feature vector with weight
        weight = torch.tensor([entity.weight], device=self.device)
        
        # Combine features
        features = torch.cat([name_emb, source_emb, target_emb, weight])
        
        # Encode
        embedding = self.relation_encoder[0](features)
        embedding = F.relu(embedding)
        embedding = self.relation_encoder[2](embedding)
        
        return embedding
    
    def _encode_rule(self, entity: Rule) -> torch.Tensor:
        """
        Encode a rule.
        
        Args:
            entity: Rule
            
        Returns:
            Encoded representation
        """
        # Encode antecedent expressions
        antecedent_embs = []
        for expr in entity.antecedent:
            expr_str = str(expr)
            expr_idx = self.vocab.get_symbol_idx(expr_str)
            expr_emb = self.embedding(torch.tensor([expr_idx], device=self.device)).squeeze(0)
            antecedent_embs.append(expr_emb)
            
        # Encode consequent
        consequent_str = str(entity.consequent)
        consequent_idx = self.vocab.get_symbol_idx(consequent_str)
        consequent_emb = self.embedding(torch.tensor([consequent_idx], device=self.device)).squeeze(0)
        
        # Combine antecedent and consequent embeddings
        all_embs = antecedent_embs + [consequent_emb]
        inputs = torch.stack(all_embs).unsqueeze(0)  # [1, num_expr, emb_dim]
        
        # Pass through GRU
        gru = self.rule_encoder[0]
        _, hidden = gru(inputs)
        hidden = hidden.squeeze(0)
        
        # Apply ReLU
        hidden = F.relu(hidden)
        
        # Pass through linear layer
        embedding = self.rule_encoder[2](hidden)
        
        return embedding
    
    def _encode_default(self, entity: SymbolicEntity) -> torch.Tensor:
        """
        Default encoding for any symbolic entity.
        
        Args:
            entity: Symbolic entity
            
        Returns:
            Encoded representation
        """
        # Get name embedding
        name_idx = self.vocab.get_symbol_idx(entity.name)
        name_emb = self.embedding(torch.tensor([name_idx], device=self.device)).squeeze(0)
        
        return name_emb
    
    def train_model(self, entities: List[SymbolicEntity], neural_targets: torch.Tensor,
                 num_epochs: int = 100, batch_size: int = 32, learning_rate: float = 1e-4,
                 weight_decay: float = 1e-5, log_interval: int = 10) -> Dict[str, List[float]]:
        """
        Train the encoder model.
        
        Args:
            entities: List of symbolic entities
            neural_targets: Target neural representations
            num_epochs: Number of training epochs
            batch_size: Batch size
            learning_rate: Learning rate
            weight_decay: Weight decay (L2 regularization)
            log_interval: Interval for logging training progress
            
        Returns:
            Dictionary of training metrics
        """
        # Set model to training mode
        self.train()
        
        # Create dataset
        dataset = list(zip(entities, neural_targets))
        
        # Create optimizer
        optimizer = torch.optim.Adam(
            self.parameters(),
            lr=learning_rate,
            weight_decay=weight_decay
        )
        
        # Training metrics
        metrics = {
            'loss': [],
            'mse': []
        }
        
        # Training loop
        for epoch in range(num_epochs):
            # Shuffle dataset
            random.shuffle(dataset)
            
            # Create batches
            batches = [dataset[i:i+batch_size] for i in range(0, len(dataset), batch_size)]
            
            # Initialize metrics
            epoch_loss = 0.0
            epoch_mse = 0.0
            
            for batch in batches:
                # Unzip batch
                entity_batch, target_batch = zip(*batch)
                
                # Convert targets to tensor
                target_tensor = torch.stack(target_batch).to(self.device)
                
                # Forward pass
                output = self.forward(entity_batch)
                
                # Compute MSE loss
                loss = F.mse_loss(output, target_tensor)
                
                # Backward pass and optimization
                optimizer.zero_grad()
                loss.backward()
                optimizer.step()
                
                # Update metrics
                epoch_loss += loss.item()
                epoch_mse += F.mse_loss(output, target_tensor, reduction='mean').item()
            
            # Average metrics
            epoch_loss /= len(batches)
            epoch_mse /= len(batches)
            
            # Store metrics
            metrics['loss'].append(epoch_loss)
            metrics['mse'].append(epoch_mse)
            
            # Log progress
            if (epoch + 1) % log_interval == 0:
                logger.info(f"Epoch {epoch+1}/{num_epochs}, " +
                          f"Loss: {epoch_loss:.4f}, " +
                          f"MSE: {epoch_mse:.4f}")
        
        return metrics
    
    def save(self, file_path: str) -> None:
        """
        Save the encoder model.
        
        Args:
            file_path: Path to save the model
        """
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        
        # Prepare model state
        state = {
            'neural_dim': self.neural_dim,
            'embedding_dim': self.embedding_dim,
            'hidden_dim': self.hidden_dim,
            'form_types': [ft.name for ft in self.form_types],
            'state_dict': self.state_dict()
        }
        
        # Save model state
        torch.save(state, file_path)
        
        logger.info(f"Saved symbolic encoder model to {file_path}")
    
    def load(self, file_path: str) -> None:
        """
        Load the encoder model.
        
        Args:
            file_path: Path to load the model from
        """
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"Model file not found: {file_path}")
            
        # Load model state
        state = torch.load(file_path, map_location=self.device)
        
        # Update attributes
        self.neural_dim = state['neural_dim']
        self.embedding_dim = state['embedding_dim']
        self.hidden_dim = state['hidden_dim']
        self.form_types = [SymbolicFormType[name] for name in state['form_types']]
        
        # Recreate model if dimensions changed
        if (self.final_encoder[-1].out_features != self.neural_dim):
            self._init_type_encoders()
            
            self.final_encoder = nn.Sequential(
                nn.Linear(self.embedding_dim, self.hidden_dim),
                nn.ReLU(),
                nn.BatchNorm1d(self.hidden_dim),
                nn.Linear(self.hidden_dim, self.hidden_dim),
                nn.ReLU(),
                nn.BatchNorm1d(self.hidden_dim),
                nn.Linear(self.hidden_dim, self.neural_dim)
            ).to(self.device)
        
        # Load state dict
        self.load_state_dict(state['state_dict'])
        
        logger.info(f"Loaded symbolic encoder model from {file_path}")


class SymbolicRepresentationLearning:
    """
    Main class for symbolic representation learning, integrating the various components.
    """
    
    def __init__(self, name: str = "SymbolicRepresentationLearning", config: Dict = None):
        """
        Initialize the Symbolic Representation Learning component.
        
        Args:
            name: Name of the component
            config: Configuration with parameters
        """
        self.name = name
        self.config = config or {}
        
        # Default configuration
        if self.config is None:
            self.config = {}
            
        # Neural dimensions and symbolic parameters
        self.neural_dim = self.config.get('neural_dim', 768)
        self.embedding_dim = self.config.get('embedding_dim', 256)
        self.hidden_dim = self.config.get('hidden_dim', 512)
        self.vocab_size = self.config.get('vocab_size', 10000)
        
        # Device
        self.device = self.config.get('device', 'cuda' if torch.cuda.is_available() else 'cpu')
        
        # Symbolic vocabulary
        self.vocabulary = SymbolicVocabulary(name=f"{name} Vocabulary")
        
        # Embedding method
        embedding_method_name = self.config.get('embedding_method', 'COMPOSITIONAL')
        self.embedding_method = EmbeddingMethod[embedding_method_name]
        
        # Symbolic embedding
        self.symbolic_embedding = None
        
        # Neural-to-symbolic extractor
        self.symbolic_extractor = None
        
        # Symbolic-to-neural encoder
        self.symbolic_encoder = None
        
        # Expression parser for logical expressions
        self.expression_parser = ExpressionParser()
        
        # Vector cache for fast lookup
        self.vector_cache = {}
        
        # Global state
        self._is_initialized = False
    
    def initialize(self) -> None:
        """Initialize the symbolic representation learning component."""
        try:
            # Initialize vocabulary
            vocab_path = self.config.get('vocab_path', None)
            if vocab_path and os.path.exists(vocab_path):
                self.vocabulary.load(vocab_path, self.expression_parser)
                
            # Initialize embedding model
            self.symbolic_embedding = SymbolicEmbedding(
                vocab=self.vocabulary,
                embedding_dim=self.embedding_dim,
                embedding_method=self.embedding_method,
                device=self.device
            )
            
            # Initialize neural-to-symbolic extractor
            self.symbolic_extractor = SymbolicContrastiveExtractor(
                neural_dim=self.neural_dim,
                embedding_model=self.symbolic_embedding,
                hidden_dim=self.hidden_dim,
                device=self.device
            )
            
            # Initialize symbolic-to-neural encoder
            self.symbolic_encoder = SymbolicEncoder(
                vocab=self.vocabulary,
                neural_dim=self.neural_dim,
                embedding_dim=self.embedding_dim,
                hidden_dim=self.hidden_dim,
                device=self.device
            )
            
            # Load pre-trained models if available
            models_dir = self.config.get('models_dir', None)
            if models_dir and os.path.exists(models_dir):
                self.load_models(models_dir)
                
            self._is_initialized = True
            logger.info(f"Initialized {self.name}")
        except Exception as e:
            logger.error(f"Failed to initialize {self.name}: {str(e)}")
            raise
    
    def reset(self) -> None:
        """Reset the symbolic representation learning component."""
        # Clear vocabulary
        self.vocabulary = SymbolicVocabulary(name=f"{self.name} Vocabulary")
        
        # Re-initialize models
        self.symbolic_embedding = SymbolicEmbedding(
            vocab=self.vocabulary,
            embedding_dim=self.embedding_dim,
            embedding_method=self.embedding_method,
            device=self.device
        )
        
        self.symbolic_extractor = SymbolicContrastiveExtractor(
            neural_dim=self.neural_dim,
            embedding_model=self.symbolic_embedding,
            hidden_dim=self.hidden_dim,
            device=self.device
        )
        
        self.symbolic_encoder = SymbolicEncoder(
            vocab=self.vocabulary,
            neural_dim=self.neural_dim,
            embedding_dim=self.embedding_dim,
            hidden_dim=self.hidden_dim,
            device=self.device
        )
        
        # Clear cache
        self.vector_cache = {}
        
        logger.info(f"Reset {self.name}")
    
    def add_entity(self, entity: SymbolicEntity) -> None:
        """
        Add a symbolic entity to the vocabulary.
        
        Args:
            entity: Symbolic entity to add
        """
        if not self._is_initialized:
            raise RuntimeError(f"{self.name} is not initialized")
            
        self.vocabulary.add_entity(entity)
        
        # Clear cache for this entity
        cache_key = (entity.name, entity.form_type.name)
        if cache_key in self.vector_cache:
            del self.vector_cache[cache_key]
    
    def remove_entity(self, entity_name: str) -> bool:
        """
        Remove a symbolic entity from the vocabulary.
        
        Args:
            entity_name: Name of the entity to remove
            
        Returns:
            True if successfully removed, False otherwise
        """
        if not self._is_initialized:
            raise RuntimeError(f"{self.name} is not initialized")
            
        entity = self.vocabulary.get_entity(entity_name)
        if entity is None:
            return False
            
        # Clear cache for this entity
        cache_key = (entity.name, entity.form_type.name)
        if cache_key in self.vector_cache:
            del self.vector_cache[cache_key]
            
        return self.vocabulary.remove_entity(entity_name)
    
    def get_entity(self, entity_name: str) -> Optional[SymbolicEntity]:
        """
        Get a symbolic entity by name.
        
        Args:
            entity_name: Name of the entity
            
        Returns:
            Symbolic entity or None if not found
        """
        if not self._is_initialized:
            raise RuntimeError(f"{self.name} is not initialized")
            
        return self.vocabulary.get_entity(entity_name)
    
    def get_entities_by_form(self, form_type: SymbolicFormType) -> List[SymbolicEntity]:
        """
        Get all symbolic entities of a specific form type.
        
        Args:
            form_type: Type of symbolic form
            
        Returns:
            List of symbolic entities
        """
        if not self._is_initialized:
            raise RuntimeError(f"{self.name} is not initialized")
            
        return self.vocabulary.get_entities_by_form(form_type)
    
    def neural_to_symbolic(self, neural_repr: torch.Tensor, 
                        form_type: Optional[SymbolicFormType] = None,
                        k: int = 1) -> List[Tuple[SymbolicEntity, float]]:
        """
        Convert a neural representation to symbolic entities.
        
        Args:
            neural_repr: Neural representation tensor
            form_type: Optional form type to filter results
            k: Number of top matches to return
            
        Returns:
            List of (entity, similarity) pairs
        """
        if not self._is_initialized:
            raise RuntimeError(f"{self.name} is not initialized")
            
        # Ensure input is a tensor on the correct device
        if not isinstance(neural_repr, torch.Tensor):
            neural_repr = torch.tensor(neural_repr, dtype=torch.float32)
            
        neural_repr = neural_repr.to(self.device)
        
        # Get nearest symbolic entities
        return self.symbolic_extractor.get_nearest_symbolic(neural_repr, k, form_type)
    
    def symbolic_to_neural(self, entity: Union[SymbolicEntity, str]) -> torch.Tensor:
        """
        Convert a symbolic entity to a neural representation.
        
        Args:
            entity: Symbolic entity or entity name
            
        Returns:
            Neural representation tensor
        """
        if not self._is_initialized:
            raise RuntimeError(f"{self.name} is not initialized")
            
        # Get entity if string
        if isinstance(entity, str):
            entity = self.vocabulary.get_entity(entity)
            if entity is None:
                raise ValueError(f"Entity not found: {entity}")
                
        # Check cache
        cache_key = (entity.name, entity.form_type.name)
        if cache_key in self.vector_cache:
            return self.vector_cache[cache_key]
            
        # Encode entity
        neural_repr = self.symbolic_encoder([entity]).squeeze(0)
        
        # Cache result
        self.vector_cache[cache_key] = neural_repr
        
        return neural_repr
    
    def get_embedding(self, entity: Union[SymbolicEntity, str]) -> torch.Tensor:
        """
        Get the embedding for a symbolic entity.
        
        Args:
            entity: Symbolic entity or entity name
            
        Returns:
            Embedding tensor
        """
        if not self._is_initialized:
            raise RuntimeError(f"{self.name} is not initialized")
            
        # Get entity if string
        if isinstance(entity, str):
            entity = self.vocabulary.get_entity(entity)
            if entity is None:
                raise ValueError(f"Entity not found: {entity}")
                
        return self.symbolic_embedding.get_embedding(entity)
    
    def get_similarity(self, entity1: Union[SymbolicEntity, str], 
                    entity2: Union[SymbolicEntity, str]) -> float:
        """
        Compute similarity between two symbolic entities.
        
        Args:
            entity1: First symbolic entity or entity name
            entity2: Second symbolic entity or entity name
            
        Returns:
            Similarity between entities
        """
        if not self._is_initialized:
            raise RuntimeError(f"{self.name} is not initialized")
            
        # Get entities if strings
        if isinstance(entity1, str):
            entity1 = self.vocabulary.get_entity(entity1)
            if entity1 is None:
                raise ValueError(f"Entity not found: {entity1}")
                
        if isinstance(entity2, str):
            entity2 = self.vocabulary.get_entity(entity2)
            if entity2 is None:
                raise ValueError(f"Entity not found: {entity2}")
                
        return self.symbolic_embedding.get_similarity(entity1, entity2)
    
    def get_nearest_neighbors(self, entity: Union[SymbolicEntity, str], k: int = 5,
                           form_type: Optional[SymbolicFormType] = None) -> List[Tuple[SymbolicEntity, float]]:
        """
        Find the nearest neighbors of a symbolic entity.
        
        Args:
            entity: Query entity or entity name
            k: Number of neighbors to return
            form_type: Optional form type to filter candidates
            
        Returns:
            List of (entity, similarity) pairs
        """
        if not self._is_initialized:
            raise RuntimeError(f"{self.name} is not initialized")
            
        # Get entity if string
        if isinstance(entity, str):
            entity = self.vocabulary.get_entity(entity)
            if entity is None:
                raise ValueError(f"Entity not found: {entity}")
                
        return self.symbolic_embedding.get_nearest_neighbors(entity, k, form_type)
    
    def train_extraction(self, neural_data: torch.Tensor, symbolic_entities: List[SymbolicEntity],
                      num_epochs: int = 100, batch_size: int = 32) -> Dict[str, List[float]]:
        """
        Train the neural-to-symbolic extractor.
        
        Args:
            neural_data: Neural representation data
            symbolic_entities: Corresponding symbolic entities
            num_epochs: Number of training epochs
            batch_size: Batch size
            
        Returns:
            Dictionary of training metrics
        """
        if not self._is_initialized:
            raise RuntimeError(f"{self.name} is not initialized")
            
        # Ensure input is a tensor on the correct device
        if not isinstance(neural_data, torch.Tensor):
            neural_data = torch.tensor(neural_data, dtype=torch.float32)
            
        neural_data = neural_data.to(self.device)
        
        # Train extractor
        return self.symbolic_extractor.train_model(
            neural_data=neural_data,
            symbolic_entities=symbolic_entities,
            num_epochs=num_epochs,
            batch_size=batch_size
        )
    
    def train_encoding(self, entities: List[SymbolicEntity], neural_targets: torch.Tensor,
                    num_epochs: int = 100, batch_size: int = 32) -> Dict[str, List[float]]:
        """
        Train the symbolic-to-neural encoder.
        
        Args:
            entities: List of symbolic entities
            neural_targets: Target neural representations
            num_epochs: Number of training epochs
            batch_size: Batch size
            
        Returns:
            Dictionary of training metrics
        """
        if not self._is_initialized:
            raise RuntimeError(f"{self.name} is not initialized")
            
        # Ensure input is a tensor on the correct device
        if not isinstance(neural_targets, torch.Tensor):
            neural_targets = torch.tensor(neural_targets, dtype=torch.float32)
            
        neural_targets = neural_targets.to(self.device)
        
        # Clear cache
        self.vector_cache = {}
        
        # Train encoder
        return self.symbolic_encoder.train_model(
            entities=entities,
            neural_targets=neural_targets,
            num_epochs=num_epochs,
            batch_size=batch_size
        )
    
    def create_predicate_symbol(self, name: str, arity: int = 0, 
                             is_function: bool = False) -> PredicateLogicSymbol:
        """
        Create and add a predicate logic symbol.
        
        Args:
            name: Name of the symbol
            arity: Number of arguments
            is_function: Whether it's a function symbol
            
        Returns:
            Created symbol
        """
        entity = PredicateLogicSymbol(name, arity, is_function)
        self.add_entity(entity)
        return entity
    
    def create_concept(self, name: str, parent_concepts: List[str] = None) -> DescriptionLogicConcept:
        """
        Create and add a description logic concept.
        
        Args:
            name: Name of the concept
            parent_concepts: Names of parent concepts
            
        Returns:
            Created concept
        """
        entity = DescriptionLogicConcept(name, parent_concepts)
        self.add_entity(entity)
        return entity
    
    def create_role(self, name: str, domain: str, range_: str, 
                 is_transitive: bool = False, is_functional: bool = False,
                 is_inverse_functional: bool = False, is_symmetric: bool = False) -> DescriptionLogicRole:
        """
        Create and add a description logic role.
        
        Args:
            name: Name of the role
            domain: Domain concept
            range_: Range concept
            is_transitive: Whether the role is transitive
            is_functional: Whether the role is functional
            is_inverse_functional: Whether the role is inverse functional
            is_symmetric: Whether the role is symmetric
            
        Returns:
            Created role
        """
        entity = DescriptionLogicRole(
            name, domain, range_, is_transitive, is_functional,
            is_inverse_functional, is_symmetric
        )
        self.add_entity(entity)
        return entity
    
    def create_kg_entity(self, name: str, entity_type: str, 
                      attributes: Dict[str, Any] = None) -> KnowledgeGraphEntity:
        """
        Create and add a knowledge graph entity.
        
        Args:
            name: Name of the entity
            entity_type: Type of the entity
            attributes: Dictionary of entity attributes
            
        Returns:
            Created entity
        """
        entity = KnowledgeGraphEntity(name, entity_type, attributes)
        self.add_entity(entity)
        return entity
    
    def create_kg_relation(self, name: str, source: Union[KnowledgeGraphEntity, str],
                        target: Union[KnowledgeGraphEntity, str], weight: float = 1.0,
                        attributes: Dict[str, Any] = None) -> KnowledgeGraphRelation:
        """
        Create and add a knowledge graph relation.
        
        Args:
            name: Name of the relation
            source: Source entity or entity name
            target: Target entity or entity name
            weight: Weight or confidence of the relation
            attributes: Dictionary of relation attributes
            
        Returns:
            Created relation
        """
        # Get entities if strings
        if isinstance(source, str):
            source_entity = self.vocabulary.get_entity(source)
            if source_entity is None or not isinstance(source_entity, KnowledgeGraphEntity):
                raise ValueError(f"Source entity not found or not a KG entity: {source}")
            source = source_entity
                
        if isinstance(target, str):
            target_entity = self.vocabulary.get_entity(target)
            if target_entity is None or not isinstance(target_entity, KnowledgeGraphEntity):
                raise ValueError(f"Target entity not found or not a KG entity: {target}")
            target = target_entity
                
        entity = KnowledgeGraphRelation(name, source, target, weight, attributes)
        self.add_entity(entity)
        return entity
    
    def create_rule(self, name: str, antecedent: List[str], consequent: str,
                 confidence: float = 1.0) -> Rule:
        """
        Create and add a rule.
        
        Args:
            name: Name of the rule
            antecedent: List of antecedent expression strings
            consequent: Consequent expression string
            confidence: Confidence or certainty factor of the rule
            
        Returns:
            Created rule
        """
        # Parse expressions
        antecedent_exprs = [self.expression_parser.parse(expr) for expr in antecedent]
        consequent_expr = self.expression_parser.parse(consequent)
        
        entity = Rule(name, antecedent_exprs, consequent_expr, confidence)
        self.add_entity(entity)
        return entity
    
    def save_vocabulary(self, file_path: str) -> None:
        """
        Save the vocabulary to a file.
        
        Args:
            file_path: Path to save the vocabulary
        """
        if not self._is_initialized:
            raise RuntimeError(f"{self.name} is not initialized")
            
        self.vocabulary.save(file_path)
    
    def load_vocabulary(self, file_path: str) -> None:
        """
        Load the vocabulary from a file.
        
        Args:
            file_path: Path to load the vocabulary from
        """
        self.vocabulary.load(file_path, self.expression_parser)
        
        # Clear cache
        self.vector_cache = {}
    
    def save_models(self, models_dir: str) -> None:
        """
        Save the trained models.
        
        Args:
            models_dir: Directory to save the models
        """
        if not self._is_initialized:
            raise RuntimeError(f"{self.name} is not initialized")
            
        os.makedirs(models_dir, exist_ok=True)
        
        # Save symbolic embedding
        self.symbolic_embedding.save(os.path.join(models_dir, 'symbolic_embedding.pt'))
        
        # Save neural-to-symbolic extractor
        self.symbolic_extractor.save(os.path.join(models_dir, 'symbolic_extractor.pt'))
        
        # Save symbolic-to-neural encoder
        self.symbolic_encoder.save(os.path.join(models_dir, 'symbolic_encoder.pt'))
        
        # Save vocabulary
        self.vocabulary.save(os.path.join(models_dir, 'vocabulary.json'))
        
        logger.info(f"Saved models to {models_dir}")
    
    def load_models(self, models_dir: str) -> None:
        """
        Load the trained models.
        
        Args:
            models_dir: Directory to load the models from
        """
        if not os.path.exists(models_dir):
            raise FileNotFoundError(f"Models directory not found: {models_dir}")
            
        # Load vocabulary first
        vocab_path = os.path.join(models_dir, 'vocabulary.json')
        if os.path.exists(vocab_path):
            self.vocabulary.load(vocab_path, self.expression_parser)
        
        # Load symbolic embedding
        embedding_path = os.path.join(models_dir, 'symbolic_embedding.pt')
        if os.path.exists(embedding_path):
            self.symbolic_embedding.load(embedding_path)
        
        # Load neural-to-symbolic extractor
        extractor_path = os.path.join(models_dir, 'symbolic_extractor.pt')
        if os.path.exists(extractor_path):
            self.symbolic_extractor.load(extractor_path)
        
        # Load symbolic-to-neural encoder
        encoder_path = os.path.join(models_dir, 'symbolic_encoder.pt')
        if os.path.exists(encoder_path):
            self.symbolic_encoder.load(encoder_path)
        
        # Clear cache
        self.vector_cache = {}
        
        logger.info(f"Loaded models from {models_dir}")
    
    def evaluate(self, test_neural_data: torch.Tensor, test_symbolic_entities: List[SymbolicEntity]) -> Dict[str, float]:
        """
        Evaluate the performance of the representation learning.
        
        Args:
            test_neural_data: Test neural representation data
            test_symbolic_entities: Corresponding test symbolic entities
            
        Returns:
            Dictionary of evaluation metrics
        """
        if not self._is_initialized:
            raise RuntimeError(f"{self.name} is not initialized")
            
        # Ensure input is a tensor on the correct device
        if not isinstance(test_neural_data, torch.Tensor):
            test_neural_data = torch.tensor(test_neural_data, dtype=torch.float32)
            
        test_neural_data = test_neural_data.to(self.device)
        
        # Metrics to compute
        metrics = {
            'extraction_accuracy': 0.0,
            'encoding_mse': 0.0,
            'encoding_similarity': 0.0,
            'roundtrip_similarity': 0.0
        }
        
        # Evaluate neural-to-symbolic extraction
        num_correct = 0
        
        for i, (neural_repr, entity) in enumerate(zip(test_neural_data, test_symbolic_entities)):
            # Get nearest symbolic entity
            nearest = self.neural_to_symbolic(neural_repr, k=1)
            
            if nearest and nearest[0][0] == entity:
                num_correct += 1
                
        metrics['extraction_accuracy'] = num_correct / len(test_neural_data) if test_neural_data.size(0) > 0 else 0.0
        
        # Evaluate symbolic-to-neural encoding
        encoded_neural = []
        encoding_similarities = []
        
        for i, entity in enumerate(test_symbolic_entities):
            # Encode entity to neural space
            neural_repr = self.symbolic_to_neural(entity)
            encoded_neural.append(neural_repr)
            
            # Compute similarity to ground truth
            similarity = F.cosine_similarity(
                neural_repr.unsqueeze(0),
                test_neural_data[i].unsqueeze(0)
            ).item()
            
            encoding_similarities.append(similarity)
            
        if encoded_neural:
            encoded_tensor = torch.stack(encoded_neural)
            
            # Compute MSE
            metrics['encoding_mse'] = F.mse_loss(encoded_tensor, test_neural_data).item()
            
            # Average similarity
            metrics['encoding_similarity'] = sum(encoding_similarities) / len(encoding_similarities)
        
        # Evaluate roundtrip conversion
        roundtrip_similarities = []
        
        for i, neural_repr in enumerate(test_neural_data):
            # Neural -> Symbolic -> Neural
            nearest = self.neural_to_symbolic(neural_repr, k=1)
            
            if nearest:
                entity = nearest[0][0]
                roundtrip_neural = self.symbolic_to_neural(entity)
                
                # Compute similarity to original
                similarity = F.cosine_similarity(
                    neural_repr.unsqueeze(0),
                    roundtrip_neural.unsqueeze(0)
                ).item()
                
                roundtrip_similarities.append(similarity)
                
        if roundtrip_similarities:
            metrics['roundtrip_similarity'] = sum(roundtrip_similarities) / len(roundtrip_similarities)
            
        return metrics
    
    def get_symbolic_vector(self, entity: Union[SymbolicEntity, str]) -> SymbolicVector:
        """
        Get a symbolic vector for an entity.
        
        Args:
            entity: Symbolic entity or entity name
            
        Returns:
            Symbolic vector
        """
        if not self._is_initialized:
            raise RuntimeError(f"{self.name} is not initialized")
            
        # Get entity if string
        if isinstance(entity, str):
            entity = self.vocabulary.get_entity(entity)
            if entity is None:
                raise ValueError(f"Entity not found: {entity}")
                
        # Get embedding
        vector = self.symbolic_embedding.get_embedding(entity)
        
        return SymbolicVector(
            entity=entity,
            vector=vector,
            confidence=1.0,
            metadata={"source": "embedding"}
        )
    
    def symbolic_reasoning(self, expressions: List[Expression], 
                        reasoning_type: str = "deductive") -> List[Expression]:
        """
        Perform symbolic reasoning on logical expressions.
        
        Args:
            expressions: List of logical expressions
            reasoning_type: Type of reasoning to perform
            
        Returns:
            List of derived expressions
        """
        if not self._is_initialized:
            raise RuntimeError(f"{self.name} is not initialized")
            
        # Create a knowledge base from expressions
        kb = KnowledgeBase()
        
        for expr in expressions:
            if isinstance(expr, Implication):
                kb.add_rule(expr)
            else:
                kb.add_fact(expr)
                
        # Create a logical reasoning engine
        engine = LogicalReasoningEngine()
        engine.kb = kb
        
        # Perform reasoning
        if reasoning_type == "deductive":
            # Convert expressions to strings
            expr_strs = [str(expr) for expr in expressions]
            
            # Perform deductive reasoning
            _, conclusions = engine.deductive_reasoning(expr_strs)
            
            # Parse conclusions back to expressions
            return [self.expression_parser.parse(c) for c in conclusions]
        else:
            raise ValueError(f"Unsupported reasoning type: {reasoning_type}")
    
    def analyze_symbolic_structure(self, entity: Union[SymbolicEntity, str]) -> Dict[str, Any]:
        """
        Analyze the structure of a symbolic entity.
        
        Args:
            entity: Symbolic entity or entity name
            
        Returns:
            Dictionary of structural features
        """
        if not self._is_initialized:
            raise RuntimeError(f"{self.name} is not initialized")
            
        # Get entity if string
        if isinstance(entity, str):
            entity = self.vocabulary.get_entity(entity)
            if entity is None:
                raise ValueError(f"Entity not found: {entity}")
                
        # Initialize analysis result
        analysis = {
            "name": entity.name,
            "form_type": entity.form_type.name,
            "related_entities": [],
            "depth": 0,
            "complexity": 0
        }
        
        # Analyze based on entity type
        if isinstance(entity, PredicateLogicSymbol):
            analysis["arity"] = entity.arity
            analysis["is_function"] = entity.is_function
            analysis["complexity"] = entity.arity + (1 if entity.is_function else 0)
            
        elif isinstance(entity, DescriptionLogicConcept):
            # Get parent concepts
            analysis["parent_concepts"] = entity.parent_concepts
            
            # Find depth in concept hierarchy
            depth = 0
            visited = set()
            queue = deque([(parent, 1) for parent in entity.parent_concepts])
            
            while queue:
                parent_name, level = queue.popleft()
                
                if parent_name in visited:
                    continue
                    
                visited.add(parent_name)
                
                depth = max(depth, level)
                
                # Get parent entity
                parent_entity = self.vocabulary.get_entity(parent_name)
                if parent_entity and isinstance(parent_entity, DescriptionLogicConcept):
                    for grandparent in parent_entity.parent_concepts:
                        queue.append((grandparent, level + 1))
                        
            analysis["depth"] = depth
            analysis["complexity"] = len(entity.parent_concepts) + depth
            
            # Find related concepts (siblings)
            siblings = []
            
            for parent in entity.parent_concepts:
                # Find other concepts with the same parent
                for other_entity in self.vocabulary.get_entities_by_form(SymbolicFormType.DESCRIPTION_LOGIC):
                    if (isinstance(other_entity, DescriptionLogicConcept) and 
                        other_entity.name != entity.name and
                        parent in other_entity.parent_concepts):
                        siblings.append(other_entity.name)
                        
            analysis["siblings"] = siblings
            analysis["related_entities"] = siblings
            
        elif isinstance(entity, DescriptionLogicRole):
            analysis["domain"] = entity.domain
            analysis["range"] = entity.range
            analysis["is_transitive"] = entity.is_transitive
            analysis["is_functional"] = entity.is_functional
            analysis["is_inverse_functional"] = entity.is_inverse_functional
            analysis["is_symmetric"] = entity.is_symmetric
            
            # Find complexity based on properties
            prop_count = sum([
                entity.is_transitive,
                entity.is_functional,
                entity.is_inverse_functional,
                entity.is_symmetric
            ])
            
            analysis["complexity"] = 2 + prop_count  # Domain + Range + Properties
            
            # Find related roles
            related = []
            
            for other_entity in self.vocabulary.get_entities_by_form(SymbolicFormType.DESCRIPTION_LOGIC):
                if isinstance(other_entity, DescriptionLogicRole) and other_entity.name != entity.name:
                    # Related if same domain or range
                    if (other_entity.domain == entity.domain or 
                        other_entity.range == entity.range):
                        related.append(other_entity.name)
                        
            analysis["related_entities"] = related
            
        elif isinstance(entity, KnowledgeGraphEntity):
            analysis["entity_type"] = entity.entity_type
            analysis["attributes"] = entity.attributes
            
            # Find complexity based on attributes
            analysis["complexity"] = 1 + len(entity.attributes)
            
            # Find related entities
            related = []
            
            for other_entity in self.vocabulary.get_entities_by_form(SymbolicFormType.KNOWLEDGE_GRAPH):
                if isinstance(other_entity, KnowledgeGraphRelation):
                    # Related if this entity is source or target
                    if (other_entity.source.name == entity.name or 
                        other_entity.target.name == entity.name):
                        # Add the entity on the other end of the relation
                        if other_entity.source.name == entity.name:
                            related.append(other_entity.target.name)
                        else:
                            related.append(other_entity.source.name)
                            
            analysis["related_entities"] = related
            
        elif isinstance(entity, KnowledgeGraphRelation):
            analysis["source"] = entity.source.name
            analysis["target"] = entity.target.name
            analysis["weight"] = entity.weight
            analysis["attributes"] = entity.attributes
            
            # Find complexity based on attributes
            analysis["complexity"] = 2 + len(entity.attributes)  # Source + Target + Attributes
            
            # Related entities
            analysis["related_entities"] = [entity.source.name, entity.target.name]
            
        elif isinstance(entity, Rule):
            analysis["antecedent"] = [str(expr) for expr in entity.antecedent]
            analysis["consequent"] = str(entity.consequent)
            analysis["confidence"] = entity.confidence
            
            # Find complexity based on number of expressions
            analysis["complexity"] = len(entity.antecedent) + 1  # Antecedent + Consequent
            
            # Find depth based on expression depth
            expr_depths = [self._expr_depth(expr) for expr in entity.antecedent]
            expr_depths.append(self._expr_depth(entity.consequent))
            
            analysis["depth"] = max(expr_depths) if expr_depths else 0
            
        return analysis
    
    def _expr_depth(self, expr: Expression) -> int:
        """
        Calculate the depth of a logical expression.
        
        Args:
            expr: Logical expression
            
        Returns:
            Depth of the expression tree
        """
        if isinstance(expr, (Atom, Constant)):
            return 0
        elif isinstance(expr, Negation):
            return 1 + self._expr_depth(expr.expr)
        elif isinstance(expr, (Conjunction, Disjunction, Implication, Equivalence)):
            left_depth = self._expr_depth(expr.left if hasattr(expr, 'left') else expr.premise)
            right_depth = self._expr_depth(expr.right if hasattr(expr, 'right') else expr.conclusion)
            return 1 + max(left_depth, right_depth)
        return 0
    
    def create_entity_from_neural(self, neural_repr: torch.Tensor, name: str,
                               form_type: SymbolicFormType, metadata: Dict[str, Any] = None) -> SymbolicEntity:
        """
        Create a symbolic entity from a neural representation.
        
        Args:
            neural_repr: Neural representation tensor
            name: Name for the new entity
            form_type: Type of symbolic form
            metadata: Optional metadata for entity creation
            
        Returns:
            Created symbolic entity
        """
        if not self._is_initialized:
            raise RuntimeError(f"{self.name} is not initialized")
            
        # Create entity based on form type
        entity = None
        
        if form_type == SymbolicFormType.PREDICATE_LOGIC:
            arity = metadata.get('arity', 0) if metadata else 0
            is_function = metadata.get('is_function', False) if metadata else False
            entity = self.create_predicate_symbol(name, arity, is_function)
            
        elif form_type == SymbolicFormType.DESCRIPTION_LOGIC:
            parent_concepts = metadata.get('parent_concepts', []) if metadata else []
            
            if metadata and metadata.get('is_role', False):
                # Create role
                domain = metadata.get('domain', '')
                range_ = metadata.get('range', '')
                is_transitive = metadata.get('is_transitive', False)
                is_functional = metadata.get('is_functional', False)
                is_inverse_functional = metadata.get('is_inverse_functional', False)
                is_symmetric = metadata.get('is_symmetric', False)
                
                entity = self.create_role(
                    name, domain, range_, is_transitive, is_functional,
                    is_inverse_functional, is_symmetric
                )
            else:
                # Create concept
                entity = self.create_concept(name, parent_concepts)
                
        elif form_type == SymbolicFormType.KNOWLEDGE_GRAPH:
            if metadata and metadata.get('is_relation', False):
                # Create relation
                source = metadata.get('source', '')
                target = metadata.get('target', '')
                weight = metadata.get('weight', 1.0)
                attributes = metadata.get('attributes', {})
                
                entity = self.create_kg_relation(name, source, target, weight, attributes)
            else:
                # Create entity
                entity_type = metadata.get('entity_type', 'Entity') if metadata else 'Entity'
                attributes = metadata.get('attributes', {}) if metadata else {}
                
                entity = self.create_kg_entity(name, entity_type, attributes)
                
        elif form_type == SymbolicFormType.RULE_BASED:
            if metadata:
                # Create rule
                antecedent = metadata.get('antecedent', [])
                consequent = metadata.get('consequent', '')
                confidence = metadata.get('confidence', 1.0)
                
                entity = self.create_rule(name, antecedent, consequent, confidence)
                
        if entity:
            # Train the encoder for this entity
            self.symbolic_encoder.train_model(
                entities=[entity],
                neural_targets=neural_repr.unsqueeze(0),
                num_epochs=20,
                batch_size=1
            )
            
        return entity
    
    def compare_neural_representations(self, neural_repr1: torch.Tensor, 
                                    neural_repr2: torch.Tensor) -> Dict[str, float]:
        """
        Compare two neural representations in terms of symbolic features.
        
        Args:
            neural_repr1: First neural representation tensor
            neural_repr2: Second neural representation tensor
            
        Returns:
            Dictionary of comparison metrics
        """
        if not self._is_initialized:
            raise RuntimeError(f"{self.name} is not initialized")
            
        # Ensure inputs are tensors on the correct device
        if not isinstance(neural_repr1, torch.Tensor):
            neural_repr1 = torch.tensor(neural_repr1, dtype=torch.float32)
            
        if not isinstance(neural_repr2, torch.Tensor):
            neural_repr2 = torch.tensor(neural_repr2, dtype=torch.float32)
            
        neural_repr1 = neural_repr1.to(self.device)
        neural_repr2 = neural_repr2.to(self.device)
        
        # Get symbolic interpretations
        sym1 = self.neural_to_symbolic(neural_repr1, k=1)
        sym2 = self.neural_to_symbolic(neural_repr2, k=1)
        
        # Initialize comparison result
        comparison = {
            "neural_similarity": F.cosine_similarity(
                neural_repr1.unsqueeze(0),
                neural_repr2.unsqueeze(0)
            ).item(),
            "symbolic_similarity": 0.0,
            "same_form_type": False,
            "shared_attributes": [],
            "differ_attributes": []
        }
        
        # If both have symbolic interpretations
        if sym1 and sym2:
            entity1 = sym1[0][0]
            entity2 = sym2[0][0]
            
            # Check if same form type
            comparison["same_form_type"] = (entity1.form_type == entity2.form_type)
            
            # Get symbolic similarity
            comparison["symbolic_similarity"] = self.get_similarity(entity1, entity2)
            
            # Compare structural features
            analysis1 = self.analyze_symbolic_structure(entity1)
            analysis2 = self.analyze_symbolic_structure(entity2)
            
            # Find shared and different attributes
            shared = []
            differ = []
            
            for key in set(analysis1.keys()) & set(analysis2.keys()):
                if key in ["name", "form_type", "related_entities"]:
                    continue
                    
                if analysis1[key] == analysis2[key]:
                    shared.append(key)
                else:
                    differ.append(key)
                    
            comparison["shared_attributes"] = shared
            comparison["differ_attributes"] = differ
            
            # Check for shared relations
            common_relations = set(analysis1.get("related_entities", [])) & \
                             set(analysis2.get("related_entities", []))
            
            comparison["common_relations"] = list(common_relations)
            
        return comparison
    
    def create_composite_entity(self, components: List[SymbolicEntity], 
                             name: str, form_type: SymbolicFormType,
                             composition_type: str = "average") -> SymbolicEntity:
        """
        Create a composite symbolic entity from component entities.
        
        Args:
            components: List of component entities
            name: Name for the new entity
            form_type: Type of symbolic form
            composition_type: Type of composition ('average', 'weighted', 'max')
            
        Returns:
            Created composite entity
        """
        if not self._is_initialized:
            raise RuntimeError(f"{self.name} is not initialized")
            
        # Get embeddings for components
        component_embeddings = []
        
        for component in components:
            emb = self.get_embedding(component)
            component_embeddings.append(emb)
            
        # Compose embeddings
        if not component_embeddings:
            raise ValueError("No component embeddings to compose")
            
        if composition_type == "average":
            # Simple average
            composite_emb = torch.mean(torch.stack(component_embeddings), dim=0)
        elif composition_type == "max":
            # Element-wise max
            composite_emb = torch.max(torch.stack(component_embeddings), dim=0)[0]
        elif composition_type == "weighted":
            # Weighted average (equal weights for now)
            weights = torch.ones(len(component_embeddings), device=self.device) / len(component_embeddings)
            weighted_embs = torch.stack([w * emb for w, emb in zip(weights, component_embeddings)])
            composite_emb = torch.sum(weighted_embs, dim=0)
        else:
            raise ValueError(f"Unsupported composition type: {composition_type}")
            
        # Create entity of the specified form type
        # This is a simplified approach - in a real system, the creation would
        # involve more sophisticated analysis of the components
        
        # Create a basic entity based on form type
        entity = None
        
        if form_type == SymbolicFormType.PREDICATE_LOGIC:
            entity = self.create_predicate_symbol(name)
        elif form_type == SymbolicFormType.DESCRIPTION_LOGIC:
            # Use component names as parent concepts
            parent_concepts = [c.name for c in components if isinstance(c, DescriptionLogicConcept)]
            entity = self.create_concept(name, parent_concepts)
        elif form_type == SymbolicFormType.KNOWLEDGE_GRAPH:
            entity = self.create_kg_entity(name, "CompositeEntity")
        else:
            # Default to simple entity
            entity = SymbolicEntity(name, form_type)
            self.add_entity(entity)
            
        # Associate the composite embedding with the entity
        # Train the encoder for this entity
        neural_target = self.symbolic_encoder.final_encoder(composite_emb.unsqueeze(0))
        
        self.symbolic_encoder.train_model(
            entities=[entity],
            neural_targets=neural_target,
            num_epochs=20,
            batch_size=1
        )
        
        return entity


# Module-level convenience functions
def init_symbolic_representation(config: Optional[Dict] = None) -> SymbolicRepresentationLearning:
    """
    Initialize the symbolic representation learning component.
    
    Args:
        config: Optional configuration dictionary
        
    Returns:
        Initialized component
    """
    # Create instance with config
    srl = SymbolicRepresentationLearning(config=config)
    
    # Initialize
    srl.initialize()
    
    return srl

def create_predicate_symbol(name: str, arity: int = 0, is_function: bool = False) -> PredicateLogicSymbol:
    """
    Create a predicate logic symbol.
    
    Args:
        name: Name of the symbol
        arity: Number of arguments
        is_function: Whether it's a function symbol
        
    Returns:
        Created symbol
    """
    return PredicateLogicSymbol(name, arity, is_function)

def create_concept(name: str, parent_concepts: List[str] = None) -> DescriptionLogicConcept:
    """
    Create a description logic concept.
    
    Args:
        name: Name of the concept
        parent_concepts: Names of parent concepts
        
    Returns:
        Created concept
    """
    return DescriptionLogicConcept(name, parent_concepts)

def create_role(name: str, domain: str, range_: str, 
             is_transitive: bool = False, is_functional: bool = False,
             is_inverse_functional: bool = False, is_symmetric: bool = False) -> DescriptionLogicRole:
    """
    Create a description logic role.
    
    Args:
        name: Name of the role
        domain: Domain concept
        range_: Range concept
        is_transitive: Whether the role is transitive
        is_functional: Whether the role is functional
        is_inverse_functional: Whether the role is inverse functional
        is_symmetric: Whether the role is symmetric
        
    Returns:
        Created role
    """
    return DescriptionLogicRole(
        name, domain, range_, is_transitive, is_functional,
        is_inverse_functional, is_symmetric
    )

def create_kg_entity(name: str, entity_type: str, 
                  attributes: Dict[str, Any] = None) -> KnowledgeGraphEntity:
    """
    Create a knowledge graph entity.
    
    Args:
        name: Name of the entity
        entity_type: Type of the entity
        attributes: Dictionary of entity attributes
        
    Returns:
        Created entity
    """
    return KnowledgeGraphEntity(name, entity_type, attributes)

def create_kg_relation(name: str, source: KnowledgeGraphEntity, target: KnowledgeGraphEntity,
                    weight: float = 1.0, attributes: Dict[str, Any] = None) -> KnowledgeGraphRelation:
    """
    Create a knowledge graph relation.
    
    Args:
        name: Name of the relation
        source: Source entity
        target: Target entity
        weight: Weight or confidence of the relation
        attributes: Dictionary of relation attributes
        
    Returns:
        Created relation
    """
    return KnowledgeGraphRelation(name, source, target, weight, attributes)

def create_rule(name: str, antecedent: List[Expression], consequent: Expression,
             confidence: float = 1.0) -> Rule:
    """
    Create a rule.
    
    Args:
        name: Name of the rule
        antecedent: List of expressions forming the antecedent (body)
        consequent: Expression forming the consequent (head)
        confidence: Confidence or certainty factor of the rule
        
    Returns:
        Created rule
    """
    return Rule(name, antecedent, consequent, confidence)

def parse_expression(expr_str: str) -> Expression:
    """
    Parse an expression string.
    
    Args:
        expr_str: Expression string
        
    Returns:
        Parsed expression
    """
    parser = ExpressionParser()
    return parser.parse(expr_str)


# Create default instance for module-level access
default_srl = None

def get_default_srl() -> SymbolicRepresentationLearning:
    """
    Get the default symbolic representation learning instance.
    
    Returns:
        Default SRL instance
    """
    global default_srl
    
    if default_srl is None:
        default_srl = init_symbolic_representation()
        
    return default_srl

def neural_to_symbolic(neural_repr: torch.Tensor, form_type: Optional[SymbolicFormType] = None, 
                    k: int = 1) -> List[Tuple[SymbolicEntity, float]]:
    """
    Convert a neural representation to symbolic entities.
    
    Args:
        neural_repr: Neural representation tensor
        form_type: Optional form type to filter results
        k: Number of top matches to return
        
    Returns:
        List of (entity, similarity) pairs
    """
    srl = get_default_srl()
    return srl.neural_to_symbolic(neural_repr, form_type, k)

def symbolic_to_neural(entity: Union[SymbolicEntity, str]) -> torch.Tensor:
    """
    Convert a symbolic entity to a neural representation.
    
    Args:
        entity: Symbolic entity or entity name
        
    Returns:
        Neural representation tensor
    """
    srl = get_default_srl()
    return srl.symbolic_to_neural(entity)

def get_similarity(entity1: Union[SymbolicEntity, str], entity2: Union[SymbolicEntity, str]) -> float:
    """
    Compute similarity between two symbolic entities.
    
    Args:
        entity1: First symbolic entity or entity name
        entity2: Second symbolic entity or entity name
        
    Returns:
        Similarity between entities
    """
    srl = get_default_srl()
    return srl.get_similarity(entity1, entity2)

def get_nearest_neighbors(entity: Union[SymbolicEntity, str], k: int = 5,
                       form_type: Optional[SymbolicFormType] = None) -> List[Tuple[SymbolicEntity, float]]:
    """
    Find the nearest neighbors of a symbolic entity.
    
    Args:
        entity: Query entity or entity name
        k: Number of neighbors to return
        form_type: Optional form type to filter candidates
        
    Returns:
        List of (entity, similarity) pairs
    """
    srl = get_default_srl()
    return srl.get_nearest_neighbors(entity, k, form_type)

def train_extraction(neural_data: torch.Tensor, symbolic_entities: List[SymbolicEntity],
                  num_epochs: int = 100, batch_size: int = 32) -> Dict[str, List[float]]:
    """
    Train the neural-to-symbolic extractor.
    
    Args:
        neural_data: Neural representation data
        symbolic_entities: Corresponding symbolic entities
        num_epochs: Number of training epochs
        batch_size: Batch size
        
    Returns:
        Dictionary of training metrics
    """
    srl = get_default_srl()
    return srl.train_extraction(neural_data, symbolic_entities, num_epochs, batch_size)

def train_encoding(entities: List[SymbolicEntity], neural_targets: torch.Tensor,
                num_epochs: int = 100, batch_size: int = 32) -> Dict[str, List[float]]:
    """
    Train the symbolic-to-neural encoder.
    
    Args:
        entities: List of symbolic entities
        neural_targets: Target neural representations
        num_epochs: Number of training epochs
        batch_size: Batch size
        
    Returns:
        Dictionary of training metrics
    """
    srl = get_default_srl()
    return srl.train_encoding(entities, neural_targets, num_epochs, batch_size)


# Export main components and utility functions
__all__ = [
    # Classes
    'SymbolicRepresentationLearning',
    'SymbolicFormType',
    'EmbeddingMethod',
    'SymbolicEntity',
    'PredicateLogicSymbol',
    'DescriptionLogicConcept',
    'DescriptionLogicRole',
    'KnowledgeGraphEntity',
    'KnowledgeGraphRelation',
    'Rule',
    'SymbolicVocabulary',
    'SymbolicVector',
    'SymbolicEmbedding',
    'SymbolicContrastiveExtractor',
    'SymbolicEncoder',
    
    # Factory functions
    'create_predicate_symbol',
    'create_concept',
    'create_role',
    'create_kg_entity',
    'create_kg_relation',
    'create_rule',
    'parse_expression',
    
    # Module-level functions
    'init_symbolic_representation',
    'get_default_srl',
    'neural_to_symbolic',
    'symbolic_to_neural',
    'get_similarity',
    'get_nearest_neighbors',
    'train_extraction',
    'train_encoding'
]

# Initialize default instance if not in import context
if __name__ != '__main__':
    get_default_srl()
else:
    # Example usage
    from argparse import ArgumentParser
    
    parser = ArgumentParser(description='ULTRA Symbolic Representation Learning')
    parser.add_argument('--config', type=str, help='Path to configuration file')
    parser.add_argument('--models_dir', type=str, help='Path to models directory')
    parser.add_argument('--mode', choices=['train', 'inference'], default='inference',
                      help='Operation mode')
    parser.add_argument('--vocab_path', type=str, help='Path to vocabulary file')
    
    args = parser.parse_args()
    
    # Load configuration from file if provided
    config = None
    if args.config:
        with open(args.config, 'r') as f:
            import json
            config = json.load(f)
    
    # Initialize SRL
    srl = init_symbolic_representation(config)
    
    # Load vocabulary if provided
    if args.vocab_path and os.path.exists(args.vocab_path):
        srl.load_vocabulary(args.vocab_path)
    
    # Load models if provided
    if args.models_dir and os.path.exists(args.models_dir):
        srl.load_models(args.models_dir)
    
    # Example usage in training mode
    if args.mode == 'train':
        # Create example entities
        concept1 = srl.create_concept("Animal")
        concept2 = srl.create_concept("Dog", ["Animal"])
        concept3 = srl.create_concept("Cat", ["Animal"])
        
        role1 = srl.create_role("hasPet", "Person", "Animal")
        
        # Create example neural data
        neural_data = torch.randn(3, srl.neural_dim, device=srl.device)
        
        # Train extraction
        print("Training neural-to-symbolic extraction...")
        metrics = srl.train_extraction(
            neural_data=neural_data,
            symbolic_entities=[concept1, concept2, concept3],
            num_epochs=10,
            batch_size=3
        )
        
        # Train encoding
        print("Training symbolic-to-neural encoding...")
        metrics = srl.train_encoding(
            entities=[concept1, concept2, concept3],
            neural_targets=neural_data,
            num_epochs=10,
            batch_size=3
        )
        
        # Save models
        if args.models_dir:
            srl.save_models(args.models_dir)
    
    # Example usage in inference mode
    else:
        # Example entities
        if srl.vocabulary.size() == 0:
            print("Creating example entities...")
            concept1 = srl.create_concept("Animal")
            concept2 = srl.create_concept("Dog", ["Animal"])
            concept3 = srl.create_concept("Cat", ["Animal"])
            
        # Example inference
        print("Vocabulary size:", srl.vocabulary.size())
        
        for entity_name in ["Animal", "Dog", "Cat"]:
            entity = srl.get_entity(entity_name)
            if entity:
                print(f"Entity: {entity}")
                
                # Get nearest neighbors
                neighbors = srl.get_nearest_neighbors(entity, k=2)
                print(f"Nearest neighbors: {neighbors}")
                
                # Get neural representation
                neural_repr = srl.symbolic_to_neural(entity)
                print(f"Neural representation shape: {neural_repr.shape}")
                
                # Convert back to symbolic
                symbolic_entities = srl.neural_to_symbolic(neural_repr, k=1)
                print(f"Converted back to: {symbolic_entities}")
                
                print("-" * 40)