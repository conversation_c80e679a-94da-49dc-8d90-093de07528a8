#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ULTRA Input Processing Module

This module handles the encoding and preprocessing of various input modalities
for the ULTRA (Ultimate Learning & Thought Reasoning Architecture) system.
It provides a unified interface for processing text, images, audio, and
structured data into the internal representation formats required by the
core neural architecture and downstream processing components.

The module implements specialized encoders for different input modalities:
- TextEncoder: Processes text inputs using tokenization and embedding
- ImageEncoder: Transforms image inputs into neural representations
- AudioEncoder: Converts audio signals into feature representations
- DataEncoder: Handles structured data formats (JSON, CSV, etc.)
- MultiModalEncoder: Integrates multiple modalities into a unified representation

Usage example:
    from ultra.input_processing import TextEncoder, ImageEncoder, MultiModalEncoder
    
    # Process text
    text_encoder = TextEncoder()
    text_embedding = text_encoder.encode("Some input text")
    
    # Process multi-modal input
    mm_encoder = MultiModalEncoder()
    mm_embedding = mm_encoder.encode({
        "text": "Image of a cat",
        "image": image_data
    })
"""

import logging
import os
import sys
import importlib
from typing import Dict, List, Tuple, Optional, Union, Any, Type, Callable

import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(name)s: %(message)s'
)
logger = logging.getLogger(__name__)

# Import submodules
try:
    from .text_encoding import TextEncoder, TokenizerConfig
    from .image_encoding import ImageEncoder, ImageEncoderConfig
    from .audio_encoding import AudioEncoder, AudioEncoderConfig
    from .data_encoding import DataEncoder, DataEncoderConfig
    
    _has_all_encoders = True
    logger.info("All encoders successfully imported")
except ImportError as e:
    _has_all_encoders = False
    logger.warning(f"Some encoders couldn't be imported: {e}")
    logger.warning("Initializing with partial functionality")

# Check for optional dependencies
_has_transformers = importlib.util.find_spec("transformers") is not None
_has_pillow = importlib.util.find_spec("PIL") is not None
_has_librosa = importlib.util.find_spec("librosa") is not None
_has_pandas = importlib.util.find_spec("pandas") is not None
_has_torchvision = importlib.util.find_spec("torchvision") is not None
_has_torchaudio = importlib.util.find_spec("torchaudio") is not None
_has_timm = importlib.util.find_spec("timm") is not None

# Define public API
__all__ = [
    # Encoder classes
    'BaseEncoder',
    'TextEncoder',
    'ImageEncoder',
    'AudioEncoder',
    'DataEncoder',
    'MultiModalEncoder',
    
    # Configuration classes
    'InputEncoderConfig',
    'TextEncoderConfig',
    'ImageEncoderConfig',
    'AudioEncoderConfig',
    'DataEncoderConfig',
    'MultiModalEncoderConfig',
    
    # Factory functions
    'create_encoder',
    'create_multi_modal_encoder',
    
    # Utility functions
    'register_encoder',
    'get_available_encoders',
    'set_encoding_device',
    'fuse_embeddings'
]

# Module variables
_REGISTERED_ENCODERS = {}
_DEFAULT_ENCODING_DEVICE = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

# Default configuration values
DEFAULT_CONFIG = {
    "embedding_dim": 1024,      # Default embedding dimension
    "use_pooling": True,        # Whether to pool sequence outputs
    "pooling_method": "mean",   # Default pooling method
    "normalize_embeddings": True,  # Whether to normalize embeddings
    "max_sequence_length": 512,  # Default maximum sequence length
    "device": str(_DEFAULT_ENCODING_DEVICE),  # Device for encoding
    "precision": "float32",     # Numerical precision
    "cache_size": 1000,         # Cache size for encodings
    "use_gradient_checkpointing": False,  # For memory efficiency
}

class InputEncoderConfig:
    """
    Base configuration class for input encoders.
    
    This class defines common configuration parameters used across 
    different input modality encoders.
    """
    
    def __init__(self, **kwargs):
        """
        Initialize configuration with default values and overrides.
        
        Args:
            **kwargs: Overrides for default configuration values
        """
        # Start with default configuration
        for key, value in DEFAULT_CONFIG.items():
            setattr(self, key, value)
        
        # Override with any provided kwargs
        for key, value in kwargs.items():
            setattr(self, key, value)
        
        # Convert device string to torch.device
        if isinstance(self.device, str):
            self.device = torch.device(self.device)
            
        # Validate configuration
        self._validate()
        
        logger.debug(f"Initialized {self.__class__.__name__} with config: {self.to_dict()}")
    
    def _validate(self):
        """Validate configuration parameters."""
        # Validate embedding dimension
        if self.embedding_dim <= 0:
            raise ValueError(f"embedding_dim must be positive, got {self.embedding_dim}")
        
        # Validate max sequence length
        if self.max_sequence_length <= 0:
            raise ValueError(f"max_sequence_length must be positive, got {self.max_sequence_length}")
        
        # Validate pooling method
        valid_pooling_methods = ["mean", "max", "cls", "none"]
        if self.pooling_method.lower() not in valid_pooling_methods:
            raise ValueError(f"pooling_method must be one of {valid_pooling_methods}")
        
        # Validate precision
        valid_precisions = ["float32", "float16", "bfloat16"]
        if self.precision not in valid_precisions:
            raise ValueError(f"precision must be one of {valid_precisions}")
            
        # Validate cache size
        if self.cache_size < 0:
            raise ValueError(f"cache_size must be non-negative, got {self.cache_size}")
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert configuration to dictionary.
        
        Returns:
            Dict[str, Any]: Configuration as dictionary
        """
        # Convert device to string for serialization
        config_dict = {k: v for k, v in self.__dict__.items() if not k.startswith('_')}
        if 'device' in config_dict and isinstance(config_dict['device'], torch.device):
            config_dict['device'] = str(config_dict['device'])
        return config_dict
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'InputEncoderConfig':
        """
        Create configuration from dictionary.
        
        Args:
            config_dict: Dictionary containing configuration parameters
            
        Returns:
            InputEncoderConfig: Configuration object
        """
        return cls(**config_dict)
    
    def save(self, file_path: str) -> None:
        """
        Save configuration to JSON file.
        
        Args:
            file_path: Path to save the configuration file
        """
        import json
        with open(file_path, 'w') as f:
            json.dump(self.to_dict(), f, indent=2)
    
    @classmethod
    def load(cls, file_path: str) -> 'InputEncoderConfig':
        """
        Load configuration from JSON file.
        
        Args:
            file_path: Path to the configuration file
            
        Returns:
            InputEncoderConfig: Configuration object
        """
        import json
        with open(file_path, 'r') as f:
            config_dict = json.load(f)
        return cls.from_dict(config_dict)


class TextEncoderConfig(InputEncoderConfig):
    """Configuration for text encoders."""
    
    def __init__(self, **kwargs):
        # Text-specific defaults
        text_defaults = {
            "model_name": "distilbert-base-uncased",  # Default model
            "tokenizer_name": None,  # Use model_name if None
            "add_special_tokens": True,  # Add CLS, SEP etc.
            "truncation": True,  # Truncate to max_length
            "padding": "max_length",  # Padding strategy
            "use_sliding_window": False,  # For long texts
            "stride": 128,  # Stride for sliding window
            "layer_pooling": None,  # Which layers to use (None = last)
            "embedding_mode": "hidden_states"  # Or "pooler_output"
        }
        
        # Update kwargs with defaults
        for key, value in text_defaults.items():
            if key not in kwargs:
                kwargs[key] = value
                
        super().__init__(**kwargs)
    
    def _validate(self):
        """Validate text-specific configuration."""
        super()._validate()
        
        # Validate padding strategy
        valid_padding = ["max_length", "longest", "do_not_pad", "None"]
        if str(self.padding) not in valid_padding:
            raise ValueError(f"padding must be one of {valid_padding}")
        
        # Validate embedding mode
        valid_embedding_modes = ["hidden_states", "pooler_output"]
        if self.embedding_mode not in valid_embedding_modes:
            raise ValueError(f"embedding_mode must be one of {valid_embedding_modes}")
        
        # Validate stride for sliding window
        if self.use_sliding_window and self.stride <= 0:
            raise ValueError(f"stride must be positive when using sliding window")


class ImageEncoderConfig(InputEncoderConfig):
    """Configuration for image encoders."""
    
    def __init__(self, **kwargs):
        # Image-specific defaults
        image_defaults = {
            "model_name": "vit_base_patch16_224",  # Default model
            "image_size": 224,  # Default image size
            "mean": [0.485, 0.456, 0.406],  # ImageNet mean
            "std": [0.229, 0.224, 0.225],  # ImageNet std
            "resize_mode": "center_crop",  # How to resize
            "patch_size": 16,  # For ViT models
            "layer_pooling": None,  # Which layers to use
            "use_intermediate_layers": False,  # Use intermediate features
            "normalize_input": True,  # Normalize input images
            "efficient_preprocessing": True  # Use efficient preprocessing
        }
        
        # Update kwargs with defaults
        for key, value in image_defaults.items():
            if key not in kwargs:
                kwargs[key] = value
                
        super().__init__(**kwargs)
    
    def _validate(self):
        """Validate image-specific configuration."""
        super()._validate()
        
        # Validate image size
        if self.image_size <= 0:
            raise ValueError(f"image_size must be positive, got {self.image_size}")
        
        # Validate resize mode
        valid_resize_modes = ["center_crop", "resize", "pad_resize"]
        if self.resize_mode not in valid_resize_modes:
            raise ValueError(f"resize_mode must be one of {valid_resize_modes}")
        
        # Validate normalization parameters
        if len(self.mean) != 3 or len(self.std) != 3:
            raise ValueError(f"mean and std must have length 3 for RGB images")


class AudioEncoderConfig(InputEncoderConfig):
    """Configuration for audio encoders."""
    
    def __init__(self, **kwargs):
        # Audio-specific defaults
        audio_defaults = {
            "model_name": "wav2vec2-base",  # Default model
            "sample_rate": 16000,  # Default sample rate (Hz)
            "feature_type": "waveform",  # Raw waveform or spectrograms
            "n_fft": 400,  # FFT size for spectrogram
            "win_length": 400,  # Window length for spectrogram
            "hop_length": 160,  # Hop length for spectrogram
            "n_mels": 80,  # Number of mel bands
            "max_audio_length": 30.0,  # Max audio length in seconds
            "use_augmentations": False,  # Apply augmentations
            "normalization": "per_feature"  # Normalization strategy
        }
        
        # Update kwargs with defaults
        for key, value in audio_defaults.items():
            if key not in kwargs:
                kwargs[key] = value
                
        super().__init__(**kwargs)
    
    def _validate(self):
        """Validate audio-specific configuration."""
        super()._validate()
        
        # Validate sample rate
        if self.sample_rate <= 0:
            raise ValueError(f"sample_rate must be positive, got {self.sample_rate}")
        
        # Validate feature type
        valid_feature_types = ["waveform", "spectrogram", "melspectrogram", "mfcc"]
        if self.feature_type not in valid_feature_types:
            raise ValueError(f"feature_type must be one of {valid_feature_types}")
        
        # Validate spectrogram parameters
        if self.feature_type in ["spectrogram", "melspectrogram", "mfcc"]:
            if self.n_fft <= 0:
                raise ValueError(f"n_fft must be positive")
            if self.win_length <= 0:
                raise ValueError(f"win_length must be positive")
            if self.hop_length <= 0:
                raise ValueError(f"hop_length must be positive")
            if self.feature_type in ["melspectrogram", "mfcc"] and self.n_mels <= 0:
                raise ValueError(f"n_mels must be positive")
        
        # Validate max audio length
        if self.max_audio_length <= 0:
            raise ValueError(f"max_audio_length must be positive")
        
        # Validate normalization
        valid_norm = ["per_feature", "per_sample", "global", "none"]
        if self.normalization not in valid_norm:
            raise ValueError(f"normalization must be one of {valid_norm}")


class DataEncoderConfig(InputEncoderConfig):
    """Configuration for structured data encoders."""
    
    def __init__(self, **kwargs):
        # Data-specific defaults
        data_defaults = {
            "categorical_embedding_dim": 32,  # Embedding size for categorical features
            "numerical_normalization": "standard",  # How to normalize numerical features
            "handle_missing": "impute_mean",  # Strategy for missing values
            "categorical_encoding": "embedding",  # How to encode categorical features
            "max_categories": 100,  # Max categories per feature
            "schema_inference": True,  # Infer schema from data
            "feature_selection": None,  # Feature selection strategy
            "feature_hashing": False,  # Use feature hashing for high cardinality
            "n_hash_features": 64,  # Number of hash features
            "date_encoding": "cyclical"  # How to encode date/time
        }
        
        # Update kwargs with defaults
        for key, value in data_defaults.items():
            if key not in kwargs:
                kwargs[key] = value
                
        super().__init__(**kwargs)
    
    def _validate(self):
        """Validate data-specific configuration."""
        super()._validate()
        
        # Validate categorical embedding dimension
        if self.categorical_embedding_dim <= 0:
            raise ValueError(f"categorical_embedding_dim must be positive")
        
        # Validate numerical normalization
        valid_norm = ["standard", "minmax", "robust", "none"]
        if self.numerical_normalization not in valid_norm:
            raise ValueError(f"numerical_normalization must be one of {valid_norm}")
        
        # Validate missing value strategy
        valid_missing = ["impute_mean", "impute_median", "impute_constant", "drop", "use_indicator"]
        if self.handle_missing not in valid_missing:
            raise ValueError(f"handle_missing must be one of {valid_missing}")
        
        # Validate categorical encoding
        valid_encoding = ["embedding", "one_hot", "binary", "target", "frequency", "count"]
        if self.categorical_encoding not in valid_encoding:
            raise ValueError(f"categorical_encoding must be one of {valid_encoding}")
        
        # Validate max categories
        if self.max_categories <= 0:
            raise ValueError(f"max_categories must be positive")
        
        # Validate n_hash_features
        if self.feature_hashing and self.n_hash_features <= 0:
            raise ValueError(f"n_hash_features must be positive when using feature_hashing")
        
        # Validate date encoding
        valid_date = ["cyclical", "ordinal", "delta", "components"]
        if self.date_encoding not in valid_date:
            raise ValueError(f"date_encoding must be one of {valid_date}")


class MultiModalEncoderConfig(InputEncoderConfig):
    """Configuration for multi-modal encoders."""
    
    def __init__(self, **kwargs):
        # Multi-modal specific defaults
        mm_defaults = {
            "modality_configs": {},  # Configs for each modality
            "fusion_method": "concatenate",  # How to fuse modalities
            "fusion_layers": [1024, 512],  # Dims of fusion layers
            "modality_weights": None,  # Weights for each modality
            "projection_dim": 512,  # Common projection dim
            "dropout": 0.1,  # Dropout in fusion layers
            "cross_attention": False,  # Use cross-attention
            "adaptive_fusion": False,  # Use adaptive fusion
            "output_dim": 1024,  # Final embedding dim
            "use_modality_tokens": True  # Add modality tokens
        }
        
        # Update kwargs with defaults
        for key, value in mm_defaults.items():
            if key not in kwargs:
                kwargs[key] = value
                
        super().__init__(**kwargs)
    
    def _validate(self):
        """Validate multi-modal specific configuration."""
        super()._validate()
        
        # Validate fusion method
        valid_fusion = ["concatenate", "sum", "multiply", "attention", "bilinear", "gated"]
        if self.fusion_method not in valid_fusion:
            raise ValueError(f"fusion_method must be one of {valid_fusion}")
        
        # Validate fusion layers
        if self.fusion_layers and (not isinstance(self.fusion_layers, list) or 
                                  any(d <= 0 for d in self.fusion_layers)):
            raise ValueError(f"fusion_layers must be a list of positive integers")
        
        # Validate modality weights
        if self.modality_weights is not None:
            if not all(w >= 0 for w in self.modality_weights.values()):
                raise ValueError("All modality weights must be non-negative")
                
        # Validate projection dim
        if self.projection_dim <= 0:
            raise ValueError(f"projection_dim must be positive")
            
        # Validate dropout
        if not 0 <= self.dropout < 1:
            raise ValueError(f"dropout must be in range [0, 1)")
            
        # Validate output dim
        if self.output_dim <= 0:
            raise ValueError(f"output_dim must be positive")


class BaseEncoder(nn.Module):
    """
    Base class for all input encoders.
    
    This abstract class defines the interface and common functionality
    for all input modality encoders.
    """
    
    def __init__(self, config: InputEncoderConfig):
        """
        Initialize the encoder with configuration.
        
        Args:
            config: Configuration for the encoder
        """
        super().__init__()
        self.config = config
        self.device = config.device
        self.embedding_dim = config.embedding_dim
        
        # Create cache if enabled
        self.cache = {}
        self.cache_enabled = config.cache_size > 0
        self.cache_size = config.cache_size
        
        # Register encoder type
        self._register_encoder()
        
        logger.debug(f"Initialized {self.__class__.__name__} with config: {config.to_dict()}")
    
    def _register_encoder(self):
        """Register this encoder in the global registry."""
        encoder_type = self.__class__.__name__
        _REGISTERED_ENCODERS[encoder_type] = self.__class__
    
    def _compute_hash(self, input_data: Any) -> str:
        """
        Compute a hash for cache lookup.
        
        Args:
            input_data: Input data to be hashed
            
        Returns:
            str: Hash string
        """
        import hashlib
        import pickle
        
        # Convert input data to bytes for hashing
        try:
            data_bytes = pickle.dumps(input_data)
            hash_key = hashlib.sha256(data_bytes).hexdigest()
        except Exception as e:
            logger.warning(f"Failed to hash input for caching: {str(e)}")
            hash_key = None
            
        return hash_key
    
    def _manage_cache_size(self):
        """Ensure cache doesn't exceed maximum size."""
        if len(self.cache) > self.cache_size:
            # Remove oldest items
            num_to_remove = len(self.cache) - self.cache_size
            keys_to_remove = list(self.cache.keys())[:num_to_remove]
            for key in keys_to_remove:
                del self.cache[key]
    
    def encode(self, input_data: Any) -> torch.Tensor:
        """
        Encode input data into embeddings.
        
        Args:
            input_data: Input data to encode
            
        Returns:
            torch.Tensor: Encoded embeddings
        """
        # Check cache if enabled
        if self.cache_enabled:
            cache_key = self._compute_hash(input_data)
            if cache_key and cache_key in self.cache:
                return self.cache[cache_key]
        
        # Process input and get embeddings
        embeddings = self._encode_input(input_data)
        
        # Apply normalization if configured
        if self.config.normalize_embeddings:
            embeddings = F.normalize(embeddings, p=2, dim=-1)
        
        # Cache result if enabled
        if self.cache_enabled and cache_key:
            self.cache[cache_key] = embeddings
            self._manage_cache_size()
        
        return embeddings
    
    def _encode_input(self, input_data: Any) -> torch.Tensor:
        """
        Internal method to encode input, to be implemented by subclasses.
        
        Args:
            input_data: Input data to encode
            
        Returns:
            torch.Tensor: Encoded embeddings
        """
        raise NotImplementedError("Subclasses must implement _encode_input")
    
    def clear_cache(self):
        """Clear the encoding cache."""
        self.cache = {}
        
    def to(self, device):
        """
        Move encoder to specified device.
        
        Args:
            device: Target device
            
        Returns:
            BaseEncoder: Self
        """
        self.device = device
        return super().to(device)
    
    def save(self, directory: str):
        """
        Save encoder and configuration.
        
        Args:
            directory: Directory to save in
        """
        os.makedirs(directory, exist_ok=True)
        
        # Save configuration
        config_path = os.path.join(directory, "config.json")
        self.config.save(config_path)
        
        # Save model
        model_path = os.path.join(directory, "model.pt")
        torch.save(self.state_dict(), model_path)
        
        logger.info(f"Encoder saved to {directory}")
    
    @classmethod
    def load(cls, directory: str, device=None):
        """
        Load encoder from saved files.
        
        Args:
            directory: Directory containing saved encoder
            device: Device to load onto
            
        Returns:
            BaseEncoder: Loaded encoder
        """
        # Load configuration
        config_path = os.path.join(directory, "config.json")
        config = InputEncoderConfig.load(config_path)
        
        # Override device if specified
        if device is not None:
            config.device = device
        
        # Create instance
        instance = cls(config)
        
        # Load state
        model_path = os.path.join(directory, "model.pt")
        instance.load_state_dict(torch.load(model_path, map_location=config.device))
        
        logger.info(f"Encoder loaded from {directory}")
        return instance


# Register encoder classes
def register_encoder(encoder_cls: Type[BaseEncoder]):
    """
    Register a custom encoder class.
    
    Args:
        encoder_cls: Encoder class to register
        
    Returns:
        Type[BaseEncoder]: The registered class
    """
    encoder_name = encoder_cls.__name__
    _REGISTERED_ENCODERS[encoder_name] = encoder_cls
    logger.info(f"Registered encoder: {encoder_name}")
    return encoder_cls


def get_available_encoders() -> List[str]:
    """
    Get list of available encoder types.
    
    Returns:
        List[str]: List of encoder names
    """
    return list(_REGISTERED_ENCODERS.keys())


def create_encoder(encoder_type: str, config: Optional[InputEncoderConfig] = None, **kwargs) -> BaseEncoder:
    """
    Create an encoder of specified type.
    
    Args:
        encoder_type: Type of encoder to create
        config: Optional configuration object
        **kwargs: Configuration parameters if config not provided
        
    Returns:
        BaseEncoder: Created encoder instance
    """
    if encoder_type not in _REGISTERED_ENCODERS:
        raise ValueError(f"Unknown encoder type: {encoder_type}. Available types: {get_available_encoders()}")
        
    encoder_cls = _REGISTERED_ENCODERS[encoder_type]
    
    # Create configuration if not provided
    if config is None:
        config_cls = globals().get(f"{encoder_type}Config", InputEncoderConfig)
        config = config_cls(**kwargs)
        
    # Create encoder instance
    return encoder_cls(config)


def create_multi_modal_encoder(
    modality_configs: Dict[str, InputEncoderConfig], 
    mm_config: Optional[MultiModalEncoderConfig] = None,
    **kwargs
) -> 'MultiModalEncoder':
    """
    Create a multi-modal encoder with specified modality encoders.
    
    Args:
        modality_configs: Dictionary mapping modality names to configurations
        mm_config: Optional multi-modal configuration object
        **kwargs: Configuration parameters if mm_config not provided
        
    Returns:
        MultiModalEncoder: Created multi-modal encoder
    """
    if not modality_configs:
        raise ValueError("At least one modality configuration must be provided")
        
    # Create multi-modal configuration if not provided
    if mm_config is None:
        mm_kwargs = kwargs.copy()
        mm_kwargs['modality_configs'] = modality_configs
        mm_config = MultiModalEncoderConfig(**mm_kwargs)
    
    # Create multi-modal encoder
    if 'MultiModalEncoder' not in _REGISTERED_ENCODERS:
        raise ImportError("MultiModalEncoder not available")
        
    encoder_cls = _REGISTERED_ENCODERS['MultiModalEncoder']
    return encoder_cls(mm_config)


def set_encoding_device(device: Union[str, torch.device]):
    """
    Set the default device for encoding.
    
    Args:
        device: Target device
    """
    global _DEFAULT_ENCODING_DEVICE
    if isinstance(device, str):
        device = torch.device(device)
    _DEFAULT_ENCODING_DEVICE = device
    logger.info(f"Default encoding device set to {device}")


def fuse_embeddings(
    embeddings: Dict[str, torch.Tensor],
    method: str = "concatenate",
    weights: Optional[Dict[str, float]] = None,
    output_dim: Optional[int] = None
) -> torch.Tensor:
    """
    Fuse embeddings from multiple modalities.
    
    Args:
        embeddings: Dictionary mapping modality names to embeddings
        method: Fusion method ("concatenate", "sum", "average", "attention")
        weights: Optional weights for each modality
        output_dim: Optional target output dimension
        
    Returns:
        torch.Tensor: Fused embedding
    """
    if not embeddings:
        raise ValueError("Empty embeddings dictionary")
        
    # Default weights
    if weights is None:
        weights = {mod: 1.0 for mod in embeddings}
    
    # Check all modalities have weights
    for mod in embeddings:
        if mod not in weights:
            weights[mod] = 1.0
    
    # Apply fusion method
    if method == "concatenate":
        # Concatenate embeddings
        ordered_mods = sorted(embeddings.keys())
        embs = [embeddings[mod] * weights[mod] for mod in ordered_mods]
        fused = torch.cat(embs, dim=-1)
        
        # Project to output dim if needed
        if output_dim is not None and fused.size(-1) != output_dim:
            projection = nn.Linear(fused.size(-1), output_dim).to(fused.device)
            fused = projection(fused)
            
    elif method in ["sum", "average"]:
        # First project all embeddings to common dim
        embs = []
        first_emb = next(iter(embeddings.values()))
        common_dim = output_dim or first_emb.size(-1)
        
        for mod, emb in embeddings.items():
            if emb.size(-1) != common_dim:
                projection = nn.Linear(emb.size(-1), common_dim).to(emb.device)
                emb = projection(emb)
            embs.append(emb * weights[mod])
        
        # Sum or average
        fused = torch.stack(embs).sum(dim=0)
        if method == "average":
            fused = fused / len(embs)
            
    elif method == "attention":
        # Attention-based fusion
        embs = []
        dims = []
        first_emb = next(iter(embeddings.values()))
        batch_size = first_emb.size(0)
        device = first_emb.device
        
        # Project each to common dim
        common_dim = output_dim or first_emb.size(-1)
        for mod, emb in embeddings.items():
            if emb.size(-1) != common_dim:
                projection = nn.Linear(emb.size(-1), common_dim).to(device)
                emb = projection(emb)
            embs.append(emb * weights[mod])
            dims.append(emb.size(-1))
            
        # Attention scores
        scores = torch.zeros(batch_size, len(embs), device=device)
        for i, emb in enumerate(embs):
            scores[:, i] = emb.norm(dim=-1)
        
        # Apply softmax
        attn_weights = F.softmax(scores, dim=-1)
        
        # Weighted sum
        fused = torch.zeros(batch_size, common_dim, device=device)
        for i, emb in enumerate(embs):
            fused += emb * attn_weights[:, i].unsqueeze(-1)
    
    else:
        raise ValueError(f"Unknown fusion method: {method}")
        
    return fused


# Version information
__version__ = '0.1.0'