#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Audio Encoding Module for ULTRA

This module provides audio encoding capabilities for the ULTRA (Ultimate Learning & 
Thought Reasoning Architecture) system. It transforms raw audio inputs into neural 
representations suitable for processing by the core neural architecture and
downstream components.

Key features:
- Multi-format audio processing (WAV, MP3, FLAC, etc.)
- Multiple feature extraction methods (waveform, spectrogram, mel-spectrogram, MFCC)
- Pre-trained model support (Wav2Vec2, HuBERT, Whisper, etc.)
- Multi-scale representations (frame, segment, full-audio)
- Time-frequency domain transformations
- Acoustic event detection capabilities
- Integration with the Multi-Scale Knowledge Embedding component

The implementation is designed to work seamlessly with the rest of the ULTRA
architecture, particularly the Hyper-Dimensional Transformer and Diffusion-Based
Reasoning subsystems.
"""

import os
import io
import logging
import hashlib
import pickle
import warnings
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Union, Any, Callable, BinaryIO
from collections import OrderedDict
import math
from dataclasses import dataclass

import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.nn.utils.rnn import pad_sequence
from tqdm import tqdm

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Import optional dependencies
try:
    import librosa
    import librosa.display
    _HAS_LIBROSA = True
except ImportError:
    logger.warning("Librosa not found. Audio processing capabilities will be limited.")
    _HAS_LIBROSA = False

try:
    import soundfile as sf
    _HAS_SOUNDFILE = True
except ImportError:
    logger.warning("SoundFile not found. Some audio formats may not be supported.")
    _HAS_SOUNDFILE = False

try:
    import torchaudio
    import torchaudio.transforms as T
    _HAS_TORCHAUDIO = True
except ImportError:
    logger.warning("TorchAudio not found. Some processing will be less efficient.")
    _HAS_TORCHAUDIO = False

try:
    from transformers import (
        AutoConfig, 
        AutoModel, 
        AutoProcessor,
        Wav2Vec2Model, 
        Wav2Vec2Processor,
        HubertModel,
        WhisperModel,
        WhisperProcessor
    )
    _HAS_TRANSFORMERS = True
except ImportError:
    logger.warning("Transformers library not found. Pre-trained model support will be limited.")
    _HAS_TRANSFORMERS = False

try:
    import noisereduce as nr
    _HAS_NOISEREDUCE = True
except ImportError:
    logger.warning("NoiseReduce not found. Audio cleanup capabilities will be limited.")
    _HAS_NOISEREDUCE = False

try:
    import resampy
    _HAS_RESAMPY = True
except ImportError:
    logger.warning("Resampy not found. Will use alternative resampling methods.")
    _HAS_RESAMPY = False


@dataclass
class AudioEncoderConfig:
    """
    Configuration for audio encoding.
    
    This class defines parameters for audio processing, feature extraction,
    model selection, and embedding generation.
    """
    
    # Basic audio parameters
    sample_rate: int = 16000
    channels: int = 1
    max_duration: float = 30.0  # Maximum duration in seconds
    
    # Feature extraction parameters
    feature_type: str = "waveform"  # One of: waveform, spectrogram, melspectrogram, mfcc
    n_fft: int = 400
    win_length: Optional[int] = None
    hop_length: int = 160
    n_mels: int = 80
    n_mfcc: int = 13
    
    # Model parameters
    model_name: str = "facebook/wav2vec2-base-960h"
    use_pretrained: bool = True
    embedding_strategy: str = "last_hidden"  # One of: last_hidden, all_hidden, weighted, cls
    pooling_strategy: str = "mean"  # One of: mean, max, attention, none
    layers_to_use: Optional[List[int]] = None
    
    # Processing parameters
    normalize_audio: bool = True
    remove_silence: bool = False
    denoise: bool = False
    time_masking: float = 0.0  # Percentage of time to mask (data augmentation)
    freq_masking: float = 0.0  # Percentage of frequency to mask (data augmentation)
    
    # Segmentation parameters
    segment_length: float = 5.0  # Segment length in seconds
    segment_overlap: float = 0.5  # Overlap between segments
    use_sliding_window: bool = False
    
    # Audio cleanup parameters
    noise_reduction_amount: float = 0.5
    silence_threshold: float = 0.05
    min_silence_duration: float = 0.5
    
    # Framework parameters
    use_dynamic_length: bool = True
    pad_truncate_mode: str = "constant"  # One of: constant, reflect, edge
    preferred_library: str = "auto"  # One of: auto, librosa, torchaudio, transformers
    
    # Device and memory management
    device: Union[str, torch.device] = "cuda" if torch.cuda.is_available() else "cpu"
    use_gradient_checkpointing: bool = False
    precision: str = "float32"  # One of: float32, float16, bfloat16
    
    # Output parameters
    embedding_dim: Optional[int] = None  # None = use model's native dimension
    normalize_embeddings: bool = True
    output_prediction: bool = False
    
    # Cache parameters
    cache_dir: Optional[str] = None
    cache_size: int = 1000
    
    def __post_init__(self):
        """Validate configuration after initialization."""
        # Convert device string to torch.device if needed
        if isinstance(self.device, str):
            self.device = torch.device(self.device)
            
        # Set default win_length if None
        if self.win_length is None:
            self.win_length = self.n_fft
            
        # Validate configuration
        self._validate()
        
        logger.info(f"Initialized AudioEncoderConfig with model_name={self.model_name}")
    
    def _validate(self):
        """Validate configuration parameters."""
        # Validate feature type
        valid_features = ["waveform", "spectrogram", "melspectrogram", "mfcc"]
        if self.feature_type not in valid_features:
            raise ValueError(f"feature_type must be one of {valid_features}, got {self.feature_type}")
            
        # Validate embedding strategy
        valid_embedding = ["last_hidden", "all_hidden", "weighted", "cls", "layer_avg"]
        if self.embedding_strategy not in valid_embedding:
            raise ValueError(f"embedding_strategy must be one of {valid_embedding}")
            
        # Validate pooling strategy
        valid_pooling = ["mean", "max", "attention", "none"]
        if self.pooling_strategy not in valid_pooling:
            raise ValueError(f"pooling_strategy must be one of {valid_pooling}")
            
        # Validate pad_truncate_mode
        valid_padding = ["constant", "reflect", "edge"]
        if self.pad_truncate_mode not in valid_padding:
            raise ValueError(f"pad_truncate_mode must be one of {valid_padding}")
            
        # Validate preferred_library
        valid_libs = ["auto", "librosa", "torchaudio", "transformers"]
        if self.preferred_library not in valid_libs:
            raise ValueError(f"preferred_library must be one of {valid_libs}")
            
        # Validate precision
        valid_precision = ["float32", "float16", "bfloat16"]
        if self.precision not in valid_precision:
            raise ValueError(f"precision must be one of {valid_precision}")
            
        # Validate numeric parameters
        if self.sample_rate <= 0:
            raise ValueError(f"sample_rate must be positive, got {self.sample_rate}")
            
        if self.channels <= 0:
            raise ValueError(f"channels must be positive, got {self.channels}")
            
        if self.max_duration <= 0:
            raise ValueError(f"max_duration must be positive, got {self.max_duration}")
            
        if self.n_fft <= 0:
            raise ValueError(f"n_fft must be positive, got {self.n_fft}")
            
        if self.hop_length <= 0:
            raise ValueError(f"hop_length must be positive, got {self.hop_length}")
            
        if self.n_mels <= 0:
            raise ValueError(f"n_mels must be positive, got {self.n_mels}")
            
        if self.n_mfcc <= 0:
            raise ValueError(f"n_mfcc must be positive, got {self.n_mfcc}")
            
        if self.segment_length <= 0:
            raise ValueError(f"segment_length must be positive, got {self.segment_length}")
            
        if self.segment_overlap < 0 or self.segment_overlap >= 1.0:
            raise ValueError(f"segment_overlap must be in [0, 1), got {self.segment_overlap}")
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert configuration to dictionary.
        
        Returns:
            Dict[str, Any]: Configuration as dictionary
        """
        # Convert device to string for serialization
        config_dict = {k: v for k, v in self.__dict__.items() if not k.startswith('_')}
        if 'device' in config_dict and isinstance(config_dict['device'], torch.device):
            config_dict['device'] = str(config_dict['device'])
        return config_dict
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'AudioEncoderConfig':
        """
        Create configuration from dictionary.
        
        Args:
            config_dict: Dictionary containing configuration parameters
            
        Returns:
            AudioEncoderConfig: Configuration object
        """
        return cls(**config_dict)
    
    def save(self, file_path: str) -> None:
        """
        Save configuration to JSON file.
        
        Args:
            file_path: Path to save the configuration file
        """
        import json
        with open(file_path, 'w') as f:
            json.dump(self.to_dict(), f, indent=2)
    
    @classmethod
    def load(cls, file_path: str) -> 'AudioEncoderConfig':
        """
        Load configuration from JSON file.
        
        Args:
            file_path: Path to the configuration file
            
        Returns:
            AudioEncoderConfig: Configuration object
        """
        import json
        with open(file_path, 'r') as f:
            config_dict = json.load(f)
        return cls.from_dict(config_dict)


class AudioProcessor:
    """
    Audio processing utilities for loading, transforming, and extracting features.
    
    This class provides methods for handling various audio formats, converting
    between different representations, and preparing audio for neural encoding.
    """
    
    def __init__(self, config: AudioEncoderConfig):
        """
        Initialize audio processor.
        
        Args:
            config: Audio encoder configuration
        """
        self.config = config
        self.device = config.device
        
        # Initialize transforms based on configuration
        self._init_transforms()
        
        logger.debug(f"Initialized AudioProcessor with sample_rate={config.sample_rate}")
    
    def _init_transforms(self):
        """Initialize audio transforms based on configuration."""
        if _HAS_TORCHAUDIO:
            # TorchAudio transforms
            self.spectrogram_transform = T.Spectrogram(
                n_fft=self.config.n_fft,
                win_length=self.config.win_length,
                hop_length=self.config.hop_length,
                power=2.0
            )
            
            self.melspectrogram_transform = T.MelSpectrogram(
                sample_rate=self.config.sample_rate,
                n_fft=self.config.n_fft,
                win_length=self.config.win_length,
                hop_length=self.config.hop_length,
                n_mels=self.config.n_mels
            )
            
            self.mfcc_transform = T.MFCC(
                sample_rate=self.config.sample_rate,
                n_mfcc=self.config.n_mfcc,
                melkwargs={
                    'n_fft': self.config.n_fft,
                    'win_length': self.config.win_length,
                    'hop_length': self.config.hop_length,
                    'n_mels': self.config.n_mels
                }
            )
            
            # Augmentation transforms
            if self.config.time_masking > 0:
                time_mask_param = int(self.config.time_masking * self.config.sample_rate)
                self.time_masking_transform = T.TimeMasking(time_mask_param=time_mask_param)
            else:
                self.time_masking_transform = None
                
            if self.config.freq_masking > 0:
                freq_mask_param = int(self.config.freq_masking * self.config.n_mels)
                self.freq_masking_transform = T.FrequencyMasking(freq_mask_param=freq_mask_param)
            else:
                self.freq_masking_transform = None
    
    def load_audio(self, file_path_or_data: Union[str, Path, bytes, BinaryIO, np.ndarray, torch.Tensor],
                  start_time: float = 0.0, end_time: Optional[float] = None) -> torch.Tensor:
        """
        Load audio from file path or raw data.
        
        Args:
            file_path_or_data: File path or raw audio data
            start_time: Start time in seconds
            end_time: End time in seconds (None for full duration)
            
        Returns:
            torch.Tensor: Audio waveform tensor [channels, time]
        """
        # Handle numpy arrays
        if isinstance(file_path_or_data, np.ndarray):
            # Convert to tensor
            waveform = torch.from_numpy(file_path_or_data).float()
            
            # Ensure correct shape
            if waveform.dim() == 1:
                waveform = waveform.unsqueeze(0)  # Add channel dimension
            elif waveform.dim() > 2:
                raise ValueError(f"Expected 1D or 2D array, got shape {waveform.shape}")
            
            if waveform.shape[0] > waveform.shape[1]:
                # Transpose if time dimension is first
                waveform = waveform.transpose(0, 1)
                
            # Use provided sample_rate
            sample_rate = self.config.sample_rate
            
        # Handle torch tensors
        elif isinstance(file_path_or_data, torch.Tensor):
            waveform = file_path_or_data.float()
            
            # Ensure correct shape
            if waveform.dim() == 1:
                waveform = waveform.unsqueeze(0)  # Add channel dimension
            elif waveform.dim() > 2:
                raise ValueError(f"Expected 1D or 2D tensor, got shape {waveform.shape}")
            
            if waveform.shape[0] > waveform.shape[1]:
                # Transpose if time dimension is first
                waveform = waveform.transpose(0, 1)
                
            # Use provided sample_rate
            sample_rate = self.config.sample_rate
            
        # Handle file paths and binary data
        else:
            # Determine preferred library
            if self.config.preferred_library == "auto":
                if _HAS_TORCHAUDIO:
                    lib = "torchaudio"
                elif _HAS_LIBROSA:
                    lib = "librosa"
                else:
                    raise ImportError("Neither TorchAudio nor Librosa is available for audio loading")
            else:
                lib = self.config.preferred_library
            
            # Load with specified library
            if lib == "torchaudio" and _HAS_TORCHAUDIO:
                if isinstance(file_path_or_data, (str, Path)):
                    # Load from file
                    waveform, sample_rate = torchaudio.load(file_path_or_data)
                else:
                    # Load from binary data
                    with io.BytesIO(file_path_or_data if isinstance(file_path_or_data, bytes) else file_path_or_data.read()) as buffer:
                        waveform, sample_rate = torchaudio.load(buffer)
            
            elif lib == "librosa" and _HAS_LIBROSA:
                if isinstance(file_path_or_data, (str, Path)):
                    # Load from file
                    audio_data, sample_rate = librosa.load(
                        file_path_or_data, 
                        sr=None,  # Keep original sample rate
                        mono=False  # Keep original channels
                    )
                else:
                    # Load from binary data
                    buffer = io.BytesIO(file_path_or_data if isinstance(file_path_or_data, bytes) else file_path_or_data.read())
                    audio_data, sample_rate = librosa.load(
                        buffer, 
                        sr=None, 
                        mono=False
                    )
                
                # Convert to tensor
                waveform = torch.from_numpy(audio_data).float()
                
                # Ensure correct shape
                if waveform.dim() == 1:
                    waveform = waveform.unsqueeze(0)  # Add channel dimension
            
            else:
                raise ValueError(f"Unsupported library choice: {lib}")
        
        # Apply time slicing
        if start_time > 0 or end_time is not None:
            if end_time is None:
                end_time = waveform.shape[1] / sample_rate
                
            start_sample = int(start_time * sample_rate)
            end_sample = int(end_time * sample_rate)
            
            if start_sample >= waveform.shape[1] or end_sample <= 0 or start_sample >= end_sample:
                raise ValueError(f"Invalid time range: start={start_time}, end={end_time}")
                
            waveform = waveform[:, start_sample:end_sample]
        
        # Resample if needed
        if sample_rate != self.config.sample_rate:
            waveform = self.resample(waveform, sample_rate, self.config.sample_rate)
        
        # Convert to mono if needed
        if waveform.shape[0] > 1 and self.config.channels == 1:
            waveform = torch.mean(waveform, dim=0, keepdim=True)
        
        # Normalize audio if configured
        if self.config.normalize_audio:
            waveform = self.normalize_audio(waveform)
        
        # Remove silence if configured
        if self.config.remove_silence:
            waveform = self.remove_silence(waveform)
        
        # Apply denoising if configured
        if self.config.denoise:
            waveform = self.denoise(waveform)
        
        # Apply time masking (augmentation) if configured
        if self.config.time_masking > 0 and self.time_masking_transform is not None:
            waveform = self.time_masking_transform(waveform)
        
        # Truncate or pad to max_duration if needed
        max_samples = int(self.config.max_duration * self.config.sample_rate)
        
        if waveform.shape[1] > max_samples and not self.config.use_dynamic_length:
            # Truncate
            waveform = waveform[:, :max_samples]
        elif waveform.shape[1] < max_samples and not self.config.use_dynamic_length:
            # Pad
            pad_size = max_samples - waveform.shape[1]
            
            if self.config.pad_truncate_mode == "constant":
                waveform = F.pad(waveform, (0, pad_size), mode='constant', value=0)
            elif self.config.pad_truncate_mode == "reflect":
                waveform = F.pad(waveform, (0, pad_size), mode='reflect')
            elif self.config.pad_truncate_mode == "edge":
                waveform = F.pad(waveform, (0, pad_size), mode='replicate')
        
        return waveform.to(self.device)
    
    def resample(self, waveform: torch.Tensor, orig_sr: int, target_sr: int) -> torch.Tensor:
        """
        Resample audio to target sample rate.
        
        Args:
            waveform: Audio waveform tensor [channels, time]
            orig_sr: Original sample rate
            target_sr: Target sample rate
            
        Returns:
            torch.Tensor: Resampled audio waveform
        """
        if orig_sr == target_sr:
            return waveform
            
        # Try to use torchaudio first for efficiency
        if _HAS_TORCHAUDIO:
            resampler = T.Resample(orig_sr, target_sr).to(waveform.device)
            return resampler(waveform)
        
        # Fall back to librosa if available
        elif _HAS_LIBROSA:
            # Convert to numpy for librosa
            waveform_np = waveform.cpu().numpy()
            channels = waveform.shape[0]
            
            # Resample each channel
            resampled_channels = []
            for c in range(channels):
                if _HAS_RESAMPY:
                    # Use resampy (higher quality)
                    resampled = resampy.resample(waveform_np[c], orig_sr, target_sr)
                else:
                    # Use librosa fallback
                    resampled = librosa.resample(waveform_np[c], orig_sr=orig_sr, target_sr=target_sr)
                
                resampled_channels.append(resampled)
            
            # Stack channels and convert back to tensor
            resampled_audio = np.stack(resampled_channels)
            return torch.from_numpy(resampled_audio).float().to(waveform.device)
        
        # Use simple resampling as last resort
        else:
            # Simple linear resampling (lower quality)
            logger.warning("Using simple linear resampling, which may degrade audio quality.")
            scale_factor = target_sr / orig_sr
            resampled_length = int(waveform.shape[1] * scale_factor)
            
            # Indices for linear interpolation
            indices = torch.linspace(0, waveform.shape[1] - 1, resampled_length).to(waveform.device)
            indices_floor = indices.long()
            indices_ceil = torch.minimum(indices_floor + 1, torch.tensor(waveform.shape[1] - 1))
            weight_ceil = indices - indices_floor.float()
            weight_floor = 1.0 - weight_ceil
            
            # Weighted sum of adjacent samples
            resampled = waveform[:, indices_floor] * weight_floor + waveform[:, indices_ceil] * weight_ceil
            
            return resampled
    
    def normalize_audio(self, waveform: torch.Tensor) -> torch.Tensor:
        """
        Normalize audio to have zero mean and unit variance.
        
        Args:
            waveform: Audio waveform tensor [channels, time]
            
        Returns:
            torch.Tensor: Normalized audio waveform
        """
        # Compute mean and std per channel
        mean = waveform.mean(dim=1, keepdim=True)
        std = waveform.std(dim=1, keepdim=True)
        
        # Avoid division by zero
        std = torch.clamp(std, min=1e-8)
        
        # Normalize
        return (waveform - mean) / std
    
    def remove_silence(self, waveform: torch.Tensor) -> torch.Tensor:
        """
        Remove silent segments from audio.
        
        Args:
            waveform: Audio waveform tensor [channels, time]
            
        Returns:
            torch.Tensor: Audio with silence removed
        """
        if waveform.shape[1] == 0:
            return waveform  # Empty audio
            
        # Calculate energy for silence detection
        energy = waveform.pow(2).mean(dim=0)
        threshold = self.config.silence_threshold * energy.max()
        
        # Find segments above threshold
        is_sound = energy > threshold
        
        # Find continuous sound segments
        min_samples = int(self.config.min_silence_duration * self.config.sample_rate)
        sound_segments = []
        
        # Convert to numpy for efficient segment detection
        is_sound_np = is_sound.cpu().numpy()
        
        # Find segments of continuous sound
        in_segment = False
        segment_start = 0
        
        for i in range(len(is_sound_np)):
            if is_sound_np[i] and not in_segment:
                # Start of a new segment
                in_segment = True
                segment_start = i
            elif not is_sound_np[i] and in_segment:
                # End of a segment
                in_segment = False
                segment_end = i
                
                # Only keep segments longer than min_samples
                if segment_end - segment_start > min_samples:
                    sound_segments.append((segment_start, segment_end))
        
        # Handle last segment if it's still open
        if in_segment:
            segment_end = len(is_sound_np)
            if segment_end - segment_start > min_samples:
                sound_segments.append((segment_start, segment_end))
        
        # No valid segments found
        if not sound_segments:
            logger.warning("No sound segments found above threshold. Returning original audio.")
            return waveform
        
        # Concatenate sound segments
        non_silent_parts = []
        for start, end in sound_segments:
            non_silent_parts.append(waveform[:, start:end])
        
        return torch.cat(non_silent_parts, dim=1)
    
    def denoise(self, waveform: torch.Tensor) -> torch.Tensor:
        """
        Apply noise reduction to audio.
        
        Args:
            waveform: Audio waveform tensor [channels, time]
            
        Returns:
            torch.Tensor: Denoised audio waveform
        """
        if _HAS_NOISEREDUCE:
            # Convert to numpy for noisereduce
            waveform_np = waveform.cpu().numpy()
            channels = waveform.shape[0]
            
            # Denoise each channel
            denoised_channels = []
            for c in range(channels):
                # Use noisereduce to reduce stationary noise
                denoised = nr.reduce_noise(
                    y=waveform_np[c],
                    sr=self.config.sample_rate,
                    prop_decrease=self.config.noise_reduction_amount
                )
                denoised_channels.append(denoised)
            
            # Stack channels and convert back to tensor
            denoised_audio = np.stack(denoised_channels)
            return torch.from_numpy(denoised_audio).float().to(waveform.device)
        else:
            # Simple noise gate as fallback
            logger.warning("NoiseReduce not available, using simple noise gate as fallback.")
            
            # Compute energy
            energy = waveform.pow(2).mean(dim=0)
            
            # Determine noise floor (assume the lowest 10% of energy is noise)
            sorted_energy = torch.sort(energy)[0]
            noise_threshold = sorted_energy[int(0.1 * len(sorted_energy))]
            
            # Apply soft noise gate
            gain = torch.sigmoid((energy - noise_threshold) * 10)
            
            # Apply time-smoothing to avoid artifacts
            kernel_size = 11
            padding = kernel_size // 2
            gain_smoothed = F.avg_pool1d(
                gain.unsqueeze(0).unsqueeze(0),
                kernel_size=kernel_size,
                stride=1,
                padding=padding
            ).squeeze(0).squeeze(0)
            
            # Apply gain to each channel
            denoised = waveform * gain_smoothed.unsqueeze(0)
            
            return denoised
    
    def extract_features(self, waveform: torch.Tensor) -> torch.Tensor:
        """
        Extract audio features according to configuration.
        
        Args:
            waveform: Audio waveform tensor [channels, time]
            
        Returns:
            torch.Tensor: Audio features
        """
        if self.config.feature_type == "waveform":
            # Return the waveform directly
            return waveform
        
        # Check for empty input
        if waveform.shape[1] == 0:
            # Return empty tensor with appropriate shape for each feature type
            if self.config.feature_type == "spectrogram":
                return torch.zeros((1, self.config.n_fft // 2 + 1, 1), device=waveform.device)
            elif self.config.feature_type == "melspectrogram":
                return torch.zeros((1, self.config.n_mels, 1), device=waveform.device)
            elif self.config.feature_type == "mfcc":
                return torch.zeros((1, self.config.n_mfcc, 1), device=waveform.device)
        
        # Make mono if multi-channel
        if waveform.size(0) > 1:
            waveform = torch.mean(waveform, dim=0, keepdim=True)
        
        # Extract features using the appropriate transform
        if self.config.feature_type == "spectrogram":
            if _HAS_TORCHAUDIO:
                features = self.spectrogram_transform(waveform)
            else:
                # Fallback to manual spectrogram calculation
                window = torch.hann_window(self.config.win_length).to(waveform.device)
                features = torch.stft(
                    waveform.squeeze(0),
                    n_fft=self.config.n_fft,
                    hop_length=self.config.hop_length,
                    win_length=self.config.win_length,
                    window=window,
                    return_complex=True
                )
                features = torch.abs(features).pow(2).unsqueeze(0)
        
        elif self.config.feature_type == "melspectrogram":
            if _HAS_TORCHAUDIO:
                features = self.melspectrogram_transform(waveform)
            elif _HAS_LIBROSA:
                # Fall back to librosa
                waveform_np = waveform.cpu().numpy().squeeze(0)
                mel_spec = librosa.feature.melspectrogram(
                    y=waveform_np,
                    sr=self.config.sample_rate,
                    n_fft=self.config.n_fft,
                    hop_length=self.config.hop_length,
                    win_length=self.config.win_length,
                    n_mels=self.config.n_mels
                )
                features = torch.from_numpy(mel_spec).unsqueeze(0).to(waveform.device)
            else:
                raise ImportError("Neither TorchAudio nor Librosa is available for mel spectrogram extraction")
                
            # Apply frequency masking (augmentation) if configured
            if self.config.freq_masking > 0 and self.freq_masking_transform is not None:
                features = self.freq_masking_transform(features)
        
        elif self.config.feature_type == "mfcc":
            if _HAS_TORCHAUDIO:
                features = self.mfcc_transform(waveform)
            elif _HAS_LIBROSA:
                # Fall back to librosa
                waveform_np = waveform.cpu().numpy().squeeze(0)
                mfcc = librosa.feature.mfcc(
                    y=waveform_np,
                    sr=self.config.sample_rate,
                    n_mfcc=self.config.n_mfcc,
                    n_fft=self.config.n_fft,
                    hop_length=self.config.hop_length,
                    win_length=self.config.win_length
                )
                features = torch.from_numpy(mfcc).unsqueeze(0).to(waveform.device)
            else:
                raise ImportError("Neither TorchAudio nor Librosa is available for MFCC extraction")
        
        else:
            raise ValueError(f"Unknown feature type: {self.config.feature_type}")
        
        # Apply log scaling for spectrogram and melspectrogram
        if self.config.feature_type in ["spectrogram", "melspectrogram"]:
            # Add small constant to avoid log(0)
            features = torch.log(features + 1e-8)
        
        return features
    
    def segment_audio(self, waveform: torch.Tensor) -> List[torch.Tensor]:
        """
        Split audio into overlapping segments.
        
        Args:
            waveform: Audio waveform tensor [channels, time]
            
        Returns:
            List[torch.Tensor]: List of audio segments
        """
        # Calculate segment sizes in samples
        segment_samples = int(self.config.segment_length * self.config.sample_rate)
        hop_samples = int(segment_samples * (1 - self.config.segment_overlap))
        
        # Check if segmentation is needed
        if waveform.shape[1] <= segment_samples:
            return [waveform]
        
        # Create segments
        segments = []
        for i in range(0, waveform.shape[1] - segment_samples + 1, hop_samples):
            segment = waveform[:, i:i+segment_samples]
            segments.append(segment)
        
        # Add final segment if needed and it's at least half a segment long
        if waveform.shape[1] % hop_samples != 0 and waveform.shape[1] - len(segments) * hop_samples > segment_samples // 2:
            segments.append(waveform[:, -segment_samples:])
        
        return segments

    def apply_log_mel_scaling(self, features: torch.Tensor) -> torch.Tensor:
        """
        Apply log-mel scaling to features.
        
        Args:
            features: Audio features
            
        Returns:
            torch.Tensor: Scaled features
        """
        if features.dim() == 3:  # [batch, freq, time]
            # Log-mel scaling
            features = torch.log(torch.clamp(features, min=1e-10))
            
            # Normalize
            mean = features.mean(dim=(1, 2), keepdim=True)
            std = features.std(dim=(1, 2), keepdim=True)
            return (features - mean) / std
        else:
            # Handle other feature dimensions
            return features
    
    def get_silence_mask(self, waveform: torch.Tensor, threshold: float = 0.05) -> torch.Tensor:
        """
        Get mask indicating non-silent parts of audio.
        
        Args:
            waveform: Audio waveform tensor [channels, time]
            threshold: Energy threshold for silence detection
            
        Returns:
            torch.Tensor: Boolean mask for non-silent parts [time]
        """
        # Calculate energy
        energy = waveform.pow(2).mean(dim=0)
        max_energy = energy.max()
        
        # Create mask
        mask = energy > (threshold * max_energy)
        
        return mask
    
    def visualize_features(self, features: torch.Tensor, feature_type: Optional[str] = None) -> np.ndarray:
        """
        Generate visualization of audio features for debugging.
        
        Args:
            features: Audio features tensor
            feature_type: Feature type (will use config.feature_type if None)
            
        Returns:
            np.ndarray: Visualization image
        """
        if not _HAS_LIBROSA:
            logger.warning("Librosa is required for visualization. Returning empty image.")
            return np.zeros((224, 224, 3), dtype=np.uint8)
            
        feature_type = feature_type or self.config.feature_type
        
        # Move to CPU and convert to numpy
        features_np = features.cpu().numpy()
        
        # Ensure correct shape
        if features_np.ndim == 3 and features_np.shape[0] == 1:
            # Remove batch dimension [1, freq, time] -> [freq, time]
            features_np = features_np.squeeze(0)
        
        # Create figure for plotting
        import matplotlib.pyplot as plt
        fig, ax = plt.subplots(figsize=(10, 4))
        
        # Plot different feature types
        if feature_type == "waveform":
            if features_np.ndim == 2 and features_np.shape[0] <= 2:
                # Plot waveform
                librosa.display.waveshow(
                    features_np.squeeze(),
                    sr=self.config.sample_rate,
                    ax=ax
                )
                ax.set_title("Waveform")
            else:
                # Unsupported format
                ax.text(0.5, 0.5, "Unsupported waveform format", 
                       horizontalalignment='center', verticalalignment='center')
                
        elif feature_type in ["spectrogram", "melspectrogram"]:
            # Plot spectrogram
            img = librosa.display.specshow(
                features_np,
                sr=self.config.sample_rate,
                hop_length=self.config.hop_length,
                x_axis='time',
                y_axis='mel' if feature_type == "melspectrogram" else 'linear',
                ax=ax
            )
            fig.colorbar(img, ax=ax, format="%+2.0f dB")
            ax.set_title(f"{feature_type.capitalize()}")
            
        elif feature_type == "mfcc":
            # Plot MFCCs
            img = librosa.display.specshow(
                features_np,
                sr=self.config.sample_rate,
                hop_length=self.config.hop_length,
                x_axis='time',
                ax=ax
            )
            fig.colorbar(img, ax=ax)
            ax.set_title("MFCC")
            
        else:
            ax.text(0.5, 0.5, f"Unsupported feature type: {feature_type}", 
                   horizontalalignment='center', verticalalignment='center')
        
        # Convert figure to image
        fig.tight_layout()
        fig.canvas.draw()
        img_data = np.frombuffer(fig.canvas.tostring_rgb(), dtype=np.uint8)
        img_data = img_data.reshape(fig.canvas.get_width_height()[::-1] + (3,))
        
        # Clean up
        plt.close(fig)
        
        return img_data


class AudioModelBackend:
    """
    Backend for audio model functionality.
    
    This class provides a common interface for different audio model
    implementations, handling model loading and embedding extraction.
    """
    
    def __init__(self, config: AudioEncoderConfig):
        """
        Initialize audio model backend.
        
        Args:
            config: Audio encoder configuration
        """
        self.config = config
        self.device = config.device
        self.model = None
        self.processor = None
        
        # Set precision
        self.dtype = {
            'float32': torch.float32,
            'float16': torch.float16,
            'bfloat16': torch.bfloat16 if hasattr(torch, 'bfloat16') else torch.float16
        }[config.precision]
        
        # Load model
        self._load_model()
        
        logger.debug(f"Initialized AudioModelBackend with model {config.model_name}")
    
    def _load_model(self):
        """Load the audio model according to configuration."""
        if not _HAS_TRANSFORMERS:
            raise ImportError("Transformers library is required for pre-trained model support")
        
        try:
            if self.config.use_pretrained:
                # Try to determine model type from name
                model_name = self.config.model_name.lower()
                
                # Load appropriate model type
                if "wav2vec" in model_name:
                    self.processor = Wav2Vec2Processor.from_pretrained(
                        self.config.model_name,
                        cache_dir=self.config.cache_dir
                    )
                    self.model = Wav2Vec2Model.from_pretrained(
                        self.config.model_name,
                        cache_dir=self.config.cache_dir
                    )
                
                elif "hubert" in model_name:
                    self.processor = AutoProcessor.from_pretrained(
                        self.config.model_name,
                        cache_dir=self.config.cache_dir
                    )
                    self.model = HubertModel.from_pretrained(
                        self.config.model_name,
                        cache_dir=self.config.cache_dir
                    )
                
                elif "whisper" in model_name:
                    self.processor = WhisperProcessor.from_pretrained(
                        self.config.model_name,
                        cache_dir=self.config.cache_dir
                    )
                    self.model = WhisperModel.from_pretrained(
                        self.config.model_name,
                        cache_dir=self.config.cache_dir
                    )
                
                else:
                    # Generic loading
                    logger.info(f"Loading generic audio model: {self.config.model_name}")
                    self.processor = AutoProcessor.from_pretrained(
                        self.config.model_name,
                        cache_dir=self.config.cache_dir
                    )
                    self.model = AutoModel.from_pretrained(
                        self.config.model_name,
                        cache_dir=self.config.cache_dir
                    )
                
                # Set to evaluation mode
                self.model.eval()
                
                # Use gradient checkpointing if configured
                if self.config.use_gradient_checkpointing and hasattr(self.model, "gradient_checkpointing_enable"):
                    self.model.gradient_checkpointing_enable()
                
                # Move to specified device
                self.model.to(self.device)
                
                # Set precision
                self.model.to(dtype=self.dtype)
                
                logger.info(f"Loaded pre-trained model: {self.config.model_name} on {self.device}")
            else:
                logger.info("Pre-trained model loading disabled in configuration")
        except Exception as e:
            logger.error(f"Error loading model: {str(e)}")
            raise
    
    def preprocess(self, waveform: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        Preprocess audio for the model.
        
        Args:
            waveform: Audio waveform tensor [channels, time]
            
        Returns:
            Dict[str, torch.Tensor]: Preprocessed inputs for the model
        """
        if self.processor is None:
            # Simple waveform normalization if no processor available
            if waveform.dim() == 2 and waveform.shape[0] == 1:
                # Remove channel dimension for single channel
                inputs = {"input_values": waveform.squeeze(0)}
            else:
                inputs = {"input_values": waveform}
                
            # Add attention mask covering the entire sequence
            inputs["attention_mask"] = torch.ones((1, waveform.shape[1]), dtype=torch.long, device=waveform.device)
            
            return inputs
        
        # Handle processors with different APIs
        try:
            # Try the common API pattern
            inputs = self.processor(
                waveform.squeeze(0).cpu().numpy() if waveform.dim() == 2 and waveform.shape[0] == 1 else waveform.cpu().numpy(),
                sampling_rate=self.config.sample_rate,
                return_tensors="pt"
            )
            
            # Move to correct device
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            return inputs
        
        except (ValueError, TypeError, AttributeError) as e:
            # Try alternative APIs
            logger.warning(f"Standard processing failed, trying alternative: {str(e)}")
            
            # Different processor APIs for different models
            if hasattr(self.processor, "feature_extractor"):
                # For models with separate feature extractors
                inputs = self.processor.feature_extractor(
                    waveform.squeeze(0).cpu().numpy() if waveform.dim() == 2 and waveform.shape[0] == 1 else waveform.cpu().numpy(), 
                    sampling_rate=self.config.sample_rate,
                    return_tensors="pt"
                )
            else:
                # Fallback to direct waveform input
                inputs = {
                    "input_values": waveform.squeeze(0) if waveform.dim() == 2 and waveform.shape[0] == 1 else waveform
                }
            
            # Add attention mask if not provided
            if "attention_mask" not in inputs:
                seq_len = inputs["input_values"].shape[-1]
                inputs["attention_mask"] = torch.ones((1, seq_len), dtype=torch.long, device=self.device)
            
            # Move to correct device
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            return inputs
    
    def encode(self, inputs: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """
        Encode preprocessed inputs to embeddings.
        
        Args:
            inputs: Preprocessed inputs from preprocess()
            
        Returns:
            Dict[str, torch.Tensor]: Model outputs containing embeddings
        """
        if self.model is None:
            raise ValueError("Model not initialized")
            
        # Get model outputs
        with torch.no_grad():
            try:
                # Try standard inference
                outputs = self.model(**inputs, output_hidden_states=True, return_dict=True)
            except (TypeError, ValueError) as e:
                # Try alternative APIs based on common patterns
                logger.warning(f"Standard model inference failed, trying alternative: {str(e)}")
                
                if "output_hidden_states" in inspect.signature(self.model.forward).parameters:
                    # Model supports hidden states
                    outputs = self.model(**inputs, output_hidden_states=True)
                else:
                    # Basic inference
                    outputs = self.model(**inputs)
                
                # Ensure outputs are in a consistent format
                if not isinstance(outputs, dict) and hasattr(outputs, "to_dict"):
                    outputs = outputs.to_dict()
                elif not isinstance(outputs, dict):
                    # Create dict with standard keys
                    outputs_dict = {}
                    if hasattr(outputs, "last_hidden_state"):
                        outputs_dict["last_hidden_state"] = outputs.last_hidden_state
                    if hasattr(outputs, "hidden_states"):
                        outputs_dict["hidden_states"] = outputs.hidden_states
                    outputs = outputs_dict
            
        return outputs
    
    def extract_embeddings(self, outputs: Dict[str, torch.Tensor],
                          attention_mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        Extract embeddings from model outputs according to configured strategy.
        
        Args:
            outputs: Model outputs containing hidden states
            attention_mask: Optional attention mask for tokens
            
        Returns:
            torch.Tensor: Extracted embeddings
        """
        # Get all hidden states if available
        hidden_states = outputs.get("hidden_states", None)
        
        # Extract embeddings based on strategy
        if self.config.embedding_strategy == "last_hidden":
            # Use last layer hidden states
            if "last_hidden_state" in outputs:
                embeddings = outputs["last_hidden_state"]
            elif hidden_states is not None:
                embeddings = hidden_states[-1]
            else:
                # Fallback to whatever is available
                for key in ["embedded", "embeddings", "features", "representations"]:
                    if key in outputs:
                        embeddings = outputs[key]
                        break
                else:
                    # Last resort: use the first tensor we find
                    for key, value in outputs.items():
                        if isinstance(value, torch.Tensor) and value.dim() >= 2:
                            embeddings = value
                            logger.warning(f"Using {key} as embeddings due to lack of hidden states")
                            break
                    else:
                        raise ValueError("Could not find suitable embeddings in model outputs")
            
        elif self.config.embedding_strategy == "cls":
            # Use [CLS] token embedding or first token
            if "last_hidden_state" in outputs:
                embeddings = outputs["last_hidden_state"][:, 0]  # Take first token ([CLS])
            elif hidden_states is not None:
                embeddings = hidden_states[-1][:, 0]  # Take first token from last layer
            else:
                raise ValueError("Could not find hidden states for CLS token extraction")
                
        elif self.config.embedding_strategy == "all_hidden":
            # Use all hidden states from specified layers
            if hidden_states is None:
                raise ValueError("No hidden states available for 'all_hidden' strategy")
                
            if self.config.layers_to_use:
                # Use specific layers
                layers = [hidden_states[i] for i in self.config.layers_to_use]
            else:
                # Use all layers
                layers = list(hidden_states)
                
            # Average across layers
            embeddings = torch.stack(layers).mean(dim=0)
            
        elif self.config.embedding_strategy == "weighted":
            # Use weighted sum of layers
            if hidden_states is None:
                raise ValueError("No hidden states available for 'weighted' strategy")
                
            if self.config.layers_to_use:
                # Use specific layers
                layers = [hidden_states[i] for i in self.config.layers_to_use]
                # Equal weights if not specified
                weights = torch.ones(len(self.config.layers_to_use), device=self.device)
            else:
                # Use last 4 layers by default
                layers = list(hidden_states[-4:])
                # Increasing weights for later layers
                weights = torch.tensor([0.25, 0.5, 0.75, 1.0], device=self.device)
                
            # Normalize weights
            weights = weights / weights.sum()
            
            # Weighted sum
            embeddings = torch.zeros_like(layers[0])
            for i, layer in enumerate(layers):
                embeddings += weights[i] * layer
                
        elif self.config.embedding_strategy == "layer_avg":
            # Average specific layer activations
            if hidden_states is None:
                raise ValueError("No hidden states available for 'layer_avg' strategy")
                
            if self.config.layers_to_use:
                # Use specific layers
                layers = [hidden_states[i] for i in self.config.layers_to_use]
            else:
                # Use last layer
                layers = [hidden_states[-1]]
                
            # Concatenate along last dimension
            embeddings = layers[0]
            
        else:
            raise ValueError(f"Unknown embedding strategy: {self.config.embedding_strategy}")
        
        # Apply pooling if needed
        if embeddings.dim() == 3 and self.config.pooling_strategy != "none":
            embeddings = self._apply_pooling(embeddings, attention_mask)
        
        return embeddings
    
    def _apply_pooling(self, embeddings: torch.Tensor, attention_mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        Apply pooling to sequence embeddings.
        
        Args:
            embeddings: Sequence embeddings [batch_size, seq_len, hidden_dim]
            attention_mask: Optional attention mask [batch_size, seq_len]
            
        Returns:
            torch.Tensor: Pooled embeddings [batch_size, hidden_dim]
        """
        if self.config.pooling_strategy == "none":
            # Return sequence embeddings
            return embeddings
            
        if attention_mask is None:
            # If no mask provided, assume all tokens are valid
            attention_mask = torch.ones((embeddings.shape[0], embeddings.shape[1]), 
                                       device=embeddings.device, dtype=torch.long)
                                       
        if self.config.pooling_strategy == "mean":
            # Mean pooling over non-masked tokens
            mask_expanded = attention_mask.unsqueeze(-1).expand_as(embeddings).float()
            sum_embeddings = torch.sum(embeddings * mask_expanded, dim=1)
            sum_mask = torch.sum(mask_expanded, dim=1)
            sum_mask = torch.clamp(sum_mask, min=1e-9)  # Avoid division by zero
            
            return sum_embeddings / sum_mask
            
        elif self.config.pooling_strategy == "max":
            # Max pooling over non-masked tokens
            mask_expanded = attention_mask.unsqueeze(-1).expand_as(embeddings).float()
            embeddings = embeddings.clone()
            embeddings[mask_expanded == 0] = -1e9  # Set padded tokens to large negative value
            
            return torch.max(embeddings, dim=1)[0]
            
        elif self.config.pooling_strategy == "attention":
            # Self-attention pooling
            batch_size, seq_len, hidden_dim = embeddings.shape
            
            # Attention projection layer
            if not hasattr(self, 'attention_weights'):
                self.attention_weights = nn.Linear(hidden_dim, 1).to(embeddings.device)
                nn.init.xavier_uniform_(self.attention_weights.weight)
            
            # Compute attention scores
            attention_scores = self.attention_weights(embeddings).squeeze(-1)
            
            # Mask out padding tokens
            attention_scores = attention_scores.masked_fill(attention_mask == 0, -1e9)
            
            # Apply softmax
            attention_probs = F.softmax(attention_scores, dim=1)
            
            # Apply attention to embeddings
            return torch.bmm(attention_probs.unsqueeze(1), embeddings).squeeze(1)
            
        else:
            raise ValueError(f"Unknown pooling strategy: {self.config.pooling_strategy}")


class AudioEncoder(nn.Module):
    """
    Audio encoder for ULTRA architecture.
    
    This class provides comprehensive audio encoding capabilities,
    transforming raw audio inputs into neural representations at multiple
    levels of abstraction.
    """
    
    def __init__(self, config: Optional[Union[AudioEncoderConfig, dict]] = None):
        """
        Initialize audio encoder.
        
        Args:
            config: Configuration for the encoder (AudioEncoderConfig or dict)
        """
        super().__init__()
        
        # Handle configuration
        if config is None:
            self.config = AudioEncoderConfig()
        elif isinstance(config, dict):
            self.config = AudioEncoderConfig(**config)
        else:
            self.config = config
            
        # Set device
        self.device = self.config.device
        
        # Initialize audio processor
        self.processor = AudioProcessor(self.config)
        
        # Initialize model backend if using pre-trained model
        if self.config.use_pretrained:
            try:
                self.backend = AudioModelBackend(self.config)
            except ImportError as e:
                logger.warning(f"Could not load pre-trained model: {str(e)}")
                logger.warning("Falling back to feature-based encoding")
                self.backend = None
        else:
            self.backend = None
        
        # Create projection layer if needed
        if self.config.embedding_dim is not None:
            # Get the native model dimension
            if self.backend is not None and hasattr(self.backend.model, "config"):
                if hasattr(self.backend.model.config, "hidden_size"):
                    native_dim = self.backend.model.config.hidden_size
                else:
                    native_dim = self.backend.model.config.hidden_dim
            elif self.config.feature_type == "waveform":
                native_dim = self.config.sample_rate  # Default for waveform
            elif self.config.feature_type == "spectrogram":
                native_dim = self.config.n_fft // 2 + 1  # Spectrogram bins
            elif self.config.feature_type == "melspectrogram":
                native_dim = self.config.n_mels  # Mel bands
            elif self.config.feature_type == "mfcc":
                native_dim = self.config.n_mfcc  # MFCC coefficients
            else:
                native_dim = 768  # Default fallback
                
            # Create projection if dimensions differ
            if native_dim != self.config.embedding_dim:
                self.projection = nn.Linear(native_dim, self.config.embedding_dim)
                nn.init.xavier_uniform_(self.projection.weight)
                self.projection.to(self.device)
            else:
                self.projection = None
        else:
            self.projection = None
        
        # Cache for encodings
        self.cache_enabled = self.config.cache_size > 0
        self.cache = OrderedDict()
        self.cache_size = self.config.cache_size
        
        logger.info(f"Initialized AudioEncoder with feature_type={self.config.feature_type}")
    
    def _compute_hash(self, audio_data: Any) -> Optional[str]:
        """
        Compute a hash for cache lookup.
        
        Args:
            audio_data: Audio data to be hashed
            
        Returns:
            Optional[str]: Hash string or None if not hashable
        """
        # Handle different input types
        if isinstance(audio_data, str):
            # File path
            return hashlib.sha256(audio_data.encode('utf-8')).hexdigest()
        elif isinstance(audio_data, bytes):
            # Raw bytes
            return hashlib.sha256(audio_data).hexdigest()
        elif isinstance(audio_data, np.ndarray):
            # NumPy array
            try:
                return hashlib.sha256(audio_data.tobytes()).hexdigest()
            except:
                return None
        elif isinstance(audio_data, torch.Tensor):
            # PyTorch tensor
            try:
                return hashlib.sha256(audio_data.cpu().numpy().tobytes()).hexdigest()
            except:
                return None
        else:
            # Try to pickle and hash more complex data
            try:
                data_bytes = pickle.dumps(audio_data)
                return hashlib.sha256(data_bytes).hexdigest()
            except:
                return None
    
    def _manage_cache_size(self):
        """Ensure cache doesn't exceed maximum size."""
        if len(self.cache) > self.cache_size:
            # Remove oldest items (OrderedDict preserves insertion order)
            for _ in range(len(self.cache) - self.cache_size):
                self.cache.popitem(last=False)
    
    def encode(self, audio: Union[str, bytes, np.ndarray, torch.Tensor, BinaryIO],
             start_time: float = 0.0, end_time: Optional[float] = None,
             return_features: bool = False) -> Union[torch.Tensor, Dict[str, torch.Tensor]]:
        """
        Encode audio into embeddings.
        
        Args:
            audio: Audio file path, bytes, NumPy array, or PyTorch tensor
            start_time: Start time in seconds
            end_time: End time in seconds (None for full duration)
            return_features: Whether to return intermediate features
            
        Returns:
            torch.Tensor or Dict[str, torch.Tensor]: Encoded embeddings or full outputs
        """
        # Check cache if enabled
        if self.cache_enabled and start_time == 0.0 and end_time is None:
            cache_key = self._compute_hash(audio)
            if cache_key and cache_key in self.cache:
                return self.cache[cache_key]
        else:
            cache_key = None
        
        # Load audio data
        waveform = self.processor.load_audio(audio, start_time, end_time)
        
        # Process with sliding window if configured and audio is long enough
        if self.config.use_sliding_window and waveform.shape[1] > self.config.sample_rate * self.config.segment_length:
            return self._encode_sliding_window(waveform, return_features)
        
        # Handle empty or silent audio
        if waveform.shape[1] == 0 or torch.all(waveform == 0):
            logger.warning("Empty or silent audio detected")
            # Return zero embedding of appropriate dimension
            if self.config.embedding_dim is not None:
                dim = self.config.embedding_dim
            elif self.backend is not None:
                if hasattr(self.backend.model.config, "hidden_size"):
                    dim = self.backend.model.config.hidden_size
                else:
                    dim = 768  # Default fallback
            else:
                dim = 768
                
            empty_embedding = torch.zeros((1, dim), device=self.device)
            
            if return_features:
                return {
                    "embeddings": empty_embedding,
                    "features": torch.zeros((1, 1, 1), device=self.device)
                }
            else:
                return empty_embedding
        
        # Extract features if using feature-based encoding or no backend
        if self.backend is None or self.config.feature_type != "waveform":
            features = self.processor.extract_features(waveform)
        else:
            features = waveform
        
        # Use pre-trained model if available
        if self.backend is not None:
            # Preprocess for the model
            inputs = self.backend.preprocess(waveform)
            
            # Get model outputs
            outputs = self.backend.encode(inputs)
            
            # Extract embeddings
            embeddings = self.backend.extract_embeddings(
                outputs, 
                attention_mask=inputs.get("attention_mask", None)
            )
        else:
            # Feature-based encoding (without pre-trained model)
            if self.config.feature_type == "waveform":
                # For waveform, use the entire signal
                embeddings = features.view(1, -1)
            else:
                # For spectrogram-like features, reduce time dimension
                if features.dim() == 3:  # [channels, freq, time]
                    if self.config.pooling_strategy == "mean":
                        # Mean across time
                        embeddings = features.mean(dim=2)
                    elif self.config.pooling_strategy == "max":
                        # Max across time
                        embeddings = features.max(dim=2)[0]
                    else:
                        # Reshape to [batch, time, freq]
                        embeddings = features.transpose(1, 2)
                else:
                    embeddings = features
        
        # Ensure we have a batch dimension
        if embeddings.dim() == 1:
            embeddings = embeddings.unsqueeze(0)
        
        # Apply projection if configured
        if self.projection is not None:
            # Handle different input shapes
            if embeddings.dim() == 2:  # [batch, features]
                embeddings = self.projection(embeddings)
            elif embeddings.dim() == 3:  # [batch, seq, features]
                batch_size, seq_len, feat_dim = embeddings.shape
                embeddings = self.projection(embeddings.view(-1, feat_dim)).view(batch_size, seq_len, -1)
        
        # Apply normalization if configured
        if self.config.normalize_embeddings:
            # Normalize along the feature dimension
            if embeddings.dim() == 2:  # [batch, features]
                embeddings = F.normalize(embeddings, p=2, dim=-1)
            elif embeddings.dim() == 3:  # [batch, seq, features]
                embeddings = F.normalize(embeddings, p=2, dim=-1)
        
        # Cache result if enabled
        if self.cache_enabled and cache_key:
            if return_features:
                self.cache[cache_key] = {
                    "embeddings": embeddings,
                    "features": features
                }
            else:
                self.cache[cache_key] = embeddings
                
            self._manage_cache_size()
        
        # Return embeddings or full output
        if return_features:
            return {
                "embeddings": embeddings,
                "features": features
            }
        else:
            return embeddings
    
    def _encode_sliding_window(self, waveform: torch.Tensor, return_features: bool = False) -> Union[torch.Tensor, Dict[str, torch.Tensor]]:
        """
        Encode audio using sliding window approach for long sequences.
        
        Args:
            waveform: Audio waveform tensor
            return_features: Whether to return intermediate features
            
        Returns:
            torch.Tensor or Dict[str, torch.Tensor]: Encoded embeddings
        """
        # Split into segments
        segments = self.processor.segment_audio(waveform)
        
        # Process each segment
        segment_embeddings = []
        segment_features = []
        
        for segment in segments:
            # Extract features
            features = self.processor.extract_features(segment)
            segment_features.append(features)
            
            # Use pre-trained model if available
            if self.backend is not None:
                # Preprocess for the model
                inputs = self.backend.preprocess(segment)
                
                # Get model outputs
                outputs = self.backend.encode(inputs)
                
                # Extract embeddings
                embeddings = self.backend.extract_embeddings(
                    outputs, 
                    attention_mask=inputs.get("attention_mask", None)
                )
            else:
                # Feature-based encoding
                if self.config.feature_type == "waveform":
                    embeddings = segment.view(1, -1)
                else:
                    # For spectrogram-like features, reduce time dimension
                    if features.dim() == 3:  # [channels, freq, time]
                        if self.config.pooling_strategy == "mean":
                            embeddings = features.mean(dim=2)
                        elif self.config.pooling_strategy == "max":
                            embeddings = features.max(dim=2)[0]
                        else:
                            embeddings = features.transpose(1, 2)
                    else:
                        embeddings = features
            
            # Ensure we have a batch dimension
            if embeddings.dim() == 1:
                embeddings = embeddings.unsqueeze(0)
                
            segment_embeddings.append(embeddings)
        
        # Combine segment embeddings
        if len(segment_embeddings) == 0:
            # Handle empty segments
            if self.config.embedding_dim is not None:
                dim = self.config.embedding_dim
            elif self.backend is not None:
                if hasattr(self.backend.model.config, "hidden_size"):
                    dim = self.backend.model.config.hidden_size
                else:
                    dim = 768
            else:
                dim = 768
                
            combined_embeddings = torch.zeros((1, dim), device=self.device)
            combined_features = torch.zeros((1, 1, 1), device=self.device)
        else:
            # If embeddings are sequence-level (2D)
            if all(emb.dim() == 2 for emb in segment_embeddings):
                # Stack all segment embeddings
                stacked_embeddings = torch.cat(segment_embeddings, dim=0)
                
                # Aggregate across segments
                combined_embeddings = stacked_embeddings.mean(dim=0, keepdim=True)
            else:
                # For token-level embeddings (3D), concatenate sequences
                # This is a simplification; real-world usage would need more sophisticated handling
                concat_embeddings = []
                for emb in segment_embeddings:
                    if emb.dim() == 3:
                        concat_embeddings.append(emb.squeeze(0))
                    else:
                        concat_embeddings.append(emb)
                
                combined_embeddings = torch.cat(concat_embeddings, dim=0).unsqueeze(0)
            
            # Combine features (similar approach)
            if all(feat.dim() == 3 for feat in segment_features):
                try:
                    combined_features = torch.cat(segment_features, dim=2)  # Concatenate along time dimension
                except:
                    # Fallback to using the first segment's features
                    combined_features = segment_features[0]
            else:
                combined_features = segment_features[0]
        
        # Apply projection if configured
        if self.projection is not None:
            # Handle different input shapes
            if combined_embeddings.dim() == 2:  # [batch, features]
                combined_embeddings = self.projection(combined_embeddings)
            elif combined_embeddings.dim() == 3:  # [batch, seq, features]
                batch_size, seq_len, feat_dim = combined_embeddings.shape
                combined_embeddings = self.projection(combined_embeddings.view(-1, feat_dim)).view(batch_size, seq_len, -1)
        
        # Apply normalization if configured
        if self.config.normalize_embeddings:
            # Normalize along the feature dimension
            if combined_embeddings.dim() == 2:
                combined_embeddings = F.normalize(combined_embeddings, p=2, dim=-1)
            elif combined_embeddings.dim() == 3:
                combined_embeddings = F.normalize(combined_embeddings, p=2, dim=-1)
        
        # Return embeddings or full output
        if return_features:
            return {
                "embeddings": combined_embeddings,
                "features": combined_features
            }
        else:
            return combined_embeddings
    
    def encode_multi_scale(self, audio: Union[str, bytes, np.ndarray, torch.Tensor, BinaryIO]) -> Dict[str, torch.Tensor]:
        """
        Encode audio at multiple scales (time resolutions).
        
        Args:
            audio: Audio file path, bytes, NumPy array, or PyTorch tensor
            
        Returns:
            Dict[str, torch.Tensor]: Embeddings at different scales
        """
        # Load audio data
        waveform = self.processor.load_audio(audio)
        
        # Special case for empty audio
        if waveform.shape[1] == 0 or torch.all(waveform == 0):
            logger.warning("Empty or silent audio detected")
            if self.config.embedding_dim is not None:
                dim = self.config.embedding_dim
            elif self.backend is not None:
                if hasattr(self.backend.model.config, "hidden_size"):
                    dim = self.backend.model.config.hidden_size
                else:
                    dim = 768
            else:
                dim = 768
                
            empty_embedding = torch.zeros((1, dim), device=self.device)
            
            return {
                "waveform": empty_embedding,
                "frame": empty_embedding,
                "segment": empty_embedding,
                "phoneme": empty_embedding,
                "full_audio": empty_embedding
            }
        
        # Define different time scales
        scales = {
            "waveform": 0.01,    # 10ms
            "frame": 0.05,       # 50ms
            "segment": 0.5,      # 500ms
            "phoneme": 0.1,      # 100ms 
            "full_audio": None   # Full audio
        }
        
        results = {}
        
        for scale_name, duration in scales.items():
            if duration is None:
                # Full audio
                scale_waveform = waveform
            else:
                # Extract a fixed-duration sample from the middle of the audio
                sample_size = int(duration * self.config.sample_rate)
                
                if waveform.shape[1] <= sample_size:
                    # Audio shorter than scale duration, use full waveform
                    scale_waveform = waveform
                else:
                    # Take sample from middle
                    middle = waveform.shape[1] // 2
                    start = max(0, middle - sample_size // 2)
                    end = min(waveform.shape[1], start + sample_size)
                    scale_waveform = waveform[:, start:end]
            
            # Override pooling strategy temporarily for different scales
            original_pooling = self.config.pooling_strategy
            
            # Adjust pooling based on scale
            if scale_name == "waveform":
                self.config.pooling_strategy = "max"  # Max pooling for local features
            elif scale_name == "frame":
                self.config.pooling_strategy = "mean"  # Mean pooling for frames
            elif scale_name == "segment":
                self.config.pooling_strategy = "attention"  # Attention for segments
            elif scale_name == "phoneme":
                self.config.pooling_strategy = "mean"  # Mean for phoneme-level
            else:
                self.config.pooling_strategy = original_pooling
            
            # Encode at this scale
            scale_embeddings = self._encode_scale(scale_waveform)
            
            # Store result
            results[scale_name] = scale_embeddings
            
            # Restore original pooling strategy
            self.config.pooling_strategy = original_pooling
        
        return results
    
    def _encode_scale(self, waveform: torch.Tensor) -> torch.Tensor:
        """
        Encode audio at a specific scale.
        
        Args:
            waveform: Audio waveform tensor
            
        Returns:
            torch.Tensor: Encoded embeddings
        """
        # Extract features
        features = self.processor.extract_features(waveform)
        
        # Use pre-trained model if available
        if self.backend is not None:
            # Preprocess for the model
            inputs = self.backend.preprocess(waveform)
            
            # Get model outputs
            outputs = self.backend.encode(inputs)
            
            # Extract embeddings
            embeddings = self.backend.extract_embeddings(
                outputs, 
                attention_mask=inputs.get("attention_mask", None)
            )
        else:
            # Feature-based encoding
            if self.config.feature_type == "waveform":
                embeddings = waveform.view(1, -1)
            else:
                # For spectrogram-like features, reduce time dimension
                if features.dim() == 3:  # [channels, freq, time]
                    if self.config.pooling_strategy == "mean":
                        embeddings = features.mean(dim=2)
                    elif self.config.pooling_strategy == "max":
                        embeddings = features.max(dim=2)[0]
                    else:
                        embeddings = features.transpose(1, 2)
                else:
                    embeddings = features
        
        # Ensure we have a batch dimension
        if embeddings.dim() == 1:
            embeddings = embeddings.unsqueeze(0)
            
        # Apply projection if configured
        if self.projection is not None:
            # Handle different input shapes
            if embeddings.dim() == 2:  # [batch, features]
                embeddings = self.projection(embeddings)
            elif embeddings.dim() == 3:  # [batch, seq, features]
                batch_size, seq_len, feat_dim = embeddings.shape
                embeddings = self.projection(embeddings.view(-1, feat_dim)).view(batch_size, seq_len, -1)
        
        # Apply normalization if configured
        if self.config.normalize_embeddings:
            # Normalize along the feature dimension
            if embeddings.dim() == 2:
                embeddings = F.normalize(embeddings, p=2, dim=-1)
            elif embeddings.dim() == 3:
                embeddings = F.normalize(embeddings, p=2, dim=-1)
                
        return embeddings
    
    def extract_acoustic_features(self, audio: Union[str, bytes, np.ndarray, torch.Tensor, BinaryIO]) -> Dict[str, torch.Tensor]:
        """
        Extract detailed acoustic features for advanced analysis.
        
        Args:
            audio: Audio file path, bytes, NumPy array, or PyTorch tensor
            
        Returns:
            Dict[str, torch.Tensor]: Dictionary of acoustic features
        """
        # Load audio data
        waveform = self.processor.load_audio(audio)
        
        # Handle empty audio
        if waveform.shape[1] == 0 or torch.all(waveform == 0):
            empty_feature = torch.zeros((1, 1), device=self.device)
            return {
                "waveform": empty_feature,
                "spectrogram": empty_feature,
                "melspectrogram": empty_feature,
                "mfcc": empty_feature,
                "energy": empty_feature,
                "zero_crossing_rate": empty_feature,
                "spectral_centroid": empty_feature,
                "spectral_bandwidth": empty_feature,
                "spectral_rolloff": empty_feature
            }
        
        # Convert to mono for feature extraction if needed
        if waveform.shape[0] > 1:
            mono_waveform = torch.mean(waveform, dim=0, keepdim=True)
        else:
            mono_waveform = waveform
            
        # Convert to numpy for librosa features if needed
        if _HAS_LIBROSA:
            waveform_np = mono_waveform.squeeze(0).cpu().numpy()
        
        # Initialize results dictionary
        features = {"waveform": mono_waveform}
        
        # Extract basic features
        # Spectrogram
        if _HAS_TORCHAUDIO:
            spectrogram_transform = T.Spectrogram(
                n_fft=self.config.n_fft,
                win_length=self.config.win_length,
                hop_length=self.config.hop_length
            )
            features["spectrogram"] = spectrogram_transform(mono_waveform)
        elif _HAS_LIBROSA:
            spec = librosa.stft(
                waveform_np,
                n_fft=self.config.n_fft,
                hop_length=self.config.hop_length,
                win_length=self.config.win_length
            )
            features["spectrogram"] = torch.from_numpy(np.abs(spec)**2).unsqueeze(0).to(self.device)
        
        # Mel spectrogram
        if _HAS_TORCHAUDIO:
            melspectrogram_transform = T.MelSpectrogram(
                sample_rate=self.config.sample_rate,
                n_fft=self.config.n_fft,
                win_length=self.config.win_length,
                hop_length=self.config.hop_length,
                n_mels=self.config.n_mels
            )
            features["melspectrogram"] = melspectrogram_transform(mono_waveform)
        elif _HAS_LIBROSA:
            mel_spec = librosa.feature.melspectrogram(
                y=waveform_np,
                sr=self.config.sample_rate,
                n_fft=self.config.n_fft,
                hop_length=self.config.hop_length,
                win_length=self.config.win_length,
                n_mels=self.config.n_mels
            )
            features["melspectrogram"] = torch.from_numpy(mel_spec).unsqueeze(0).to(self.device)
        
        # MFCC
        if _HAS_TORCHAUDIO:
            mfcc_transform = T.MFCC(
                sample_rate=self.config.sample_rate,
                n_mfcc=self.config.n_mfcc,
                melkwargs={
                    'n_fft': self.config.n_fft,
                    'win_length': self.config.win_length,
                    'hop_length': self.config.hop_length,
                    'n_mels': self.config.n_mels
                }
            )
            features["mfcc"] = mfcc_transform(mono_waveform)
        elif _HAS_LIBROSA:
            mfcc = librosa.feature.mfcc(
                y=waveform_np,
                sr=self.config.sample_rate,
                n_mfcc=self.config.n_mfcc,
                n_fft=self.config.n_fft,
                hop_length=self.config.hop_length,
                win_length=self.config.win_length
            )
            features["mfcc"] = torch.from_numpy(mfcc).unsqueeze(0).to(self.device)
        
        # Extract advanced librosa features
        if _HAS_LIBROSA:
            # Energy
            energy = librosa.feature.rms(
                y=waveform_np,
                frame_length=self.config.win_length,
                hop_length=self.config.hop_length
            )
            features["energy"] = torch.from_numpy(energy).unsqueeze(0).to(self.device)
            
            # Zero crossing rate
            zcr = librosa.feature.zero_crossing_rate(
                y=waveform_np,
                frame_length=self.config.win_length,
                hop_length=self.config.hop_length
            )
            features["zero_crossing_rate"] = torch.from_numpy(zcr).unsqueeze(0).to(self.device)
            
            # Spectral centroid
            centroid = librosa.feature.spectral_centroid(
                y=waveform_np,
                sr=self.config.sample_rate,
                n_fft=self.config.n_fft,
                hop_length=self.config.hop_length,
                win_length=self.config.win_length
            )
            features["spectral_centroid"] = torch.from_numpy(centroid).unsqueeze(0).to(self.device)
            
            # Spectral bandwidth
            bandwidth = librosa.feature.spectral_bandwidth(
                y=waveform_np,
                sr=self.config.sample_rate,
                n_fft=self.config.n_fft,
                hop_length=self.config.hop_length,
                win_length=self.config.win_length
            )
            features["spectral_bandwidth"] = torch.from_numpy(bandwidth).unsqueeze(0).to(self.device)
            
            # Spectral rolloff
            rolloff = librosa.feature.spectral_rolloff(
                y=waveform_np,
                sr=self.config.sample_rate,
                n_fft=self.config.n_fft,
                hop_length=self.config.hop_length,
                win_length=self.config.win_length
            )
            features["spectral_rolloff"] = torch.from_numpy(rolloff).unsqueeze(0).to(self.device)
        
        return features
    
    def forward(self, audio: Union[str, bytes, np.ndarray, torch.Tensor, BinaryIO],
              start_time: float = 0.0, end_time: Optional[float] = None) -> torch.Tensor:
        """
        Forward pass to encode audio.
        
        Args:
            audio: Audio file path, bytes, NumPy array, or PyTorch tensor
            start_time: Start time in seconds
            end_time: End time in seconds (None for full duration)
            
        Returns:
            torch.Tensor: Encoded embeddings
        """
        return self.encode(audio, start_time, end_time)
    
    def clear_cache(self):
        """Clear the encoding cache."""
        self.cache.clear()
        
    def to(self, device):
        """
        Move encoder to specified device.
        
        Args:
            device: Target device
            
        Returns:
            AudioEncoder: Self
        """
        self.device = device
        self.processor.device = device
        
        if self.backend is not None:
            self.backend.device = device
            self.backend.model.to(device)
            
        if self.projection is not None:
            self.projection.to(device)
            
        return self
    
    def save(self, directory: str, save_model: bool = True):
        """
        Save encoder and configuration.
        
        Args:
            directory: Directory to save in
            save_model: Whether to save the underlying model
        """
        os.makedirs(directory, exist_ok=True)
        
        # Save configuration
        config_path = os.path.join(directory, "config.json")
        self.config.save(config_path)
        
        # Save encoder state (projection layer weights)
        if self.projection is not None:
            state_path = os.path.join(directory, "encoder_state.pt")
            torch.save(self.state_dict(), state_path)
        
        # Save model if requested
        if save_model and self.backend is not None and self.backend.model is not None:
            model_dir = os.path.join(directory, "model")
            os.makedirs(model_dir, exist_ok=True)
            self.backend.model.save_pretrained(model_dir)
            if self.backend.processor is not None:
                self.backend.processor.save_pretrained(model_dir)
        
        logger.info(f"AudioEncoder saved to {directory}")
    
    @classmethod
    def load(cls, directory: str, device=None, load_model: bool = True):
        """
        Load encoder from saved files.
        
        Args:
            directory: Directory containing saved encoder
            device: Device to load onto
            load_model: Whether to load the underlying model
            
        Returns:
            AudioEncoder: Loaded encoder
        """
        # Load configuration
        config_path = os.path.join(directory, "config.json")
        config = AudioEncoderConfig.load(config_path)
        
        # Override device if specified
        if device is not None:
            config.device = device
        
        # Create instance
        instance = cls(config)
        
        # Load encoder state (projection layer weights)
        state_path = os.path.join(directory, "encoder_state.pt")
        if os.path.exists(state_path):
            instance.load_state_dict(torch.load(state_path, map_location=config.device))
        
        # Load model if requested
        if load_model:
            model_dir = os.path.join(directory, "model")
            if os.path.exists(model_dir):
                config.model_name = model_dir
                instance.backend = AudioModelBackend(config)
        
        logger.info(f"AudioEncoder loaded from {directory}")
        return instance


# Import inspection module for model introspection
import inspect

# Sentinel to mark end of module
if __name__ == "__main__":
    # Example usage
    config = AudioEncoderConfig(feature_type="melspectrogram")
    encoder = AudioEncoder(config)
    
    audio_path = "example.wav"  # Replace with an actual audio file
    try:
        embeddings = encoder.encode(audio_path)
        print(f"Encoded audio shape: {embeddings.shape}")
    except FileNotFoundError:
        print(f"Demo file {audio_path} not found. Replace with an actual audio file path.")