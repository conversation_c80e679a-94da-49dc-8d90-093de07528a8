#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Image Encoding Module for ULTRA

This module provides image encoding capabilities for the ULTRA (Ultimate Learning & 
Thought Reasoning Architecture) system. It transforms raw image inputs into neural 
representations suitable for processing by the core neural architecture and
downstream components.

Key features:
- Support for multiple vision backbone architectures (CNNs, ViT, CLIP, etc.)
- Multi-scale feature extraction across different network depths
- Patch-based and region-based encoding for fine-grained visual reasoning
- Hierarchical spatial representations aligning with the ULTRA architecture
- Efficient preprocessing pipelines with optimized transformations
- Integration with the Cross-Modal Dimension Mapper component
- Compatibility with the Thought Latent Space in Diffusion-Based Reasoning

The implementation provides embeddings that can be directly consumed by the
Hyper-Dimensional Transformer and other ULTRA components.
"""

import os
import io
import logging
import hashlib
import pickle
from typing import Dict, List, Tuple, Optional, Union, Any, Callable
from collections import OrderedDict

import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from PIL import Image, ImageFile
import math

# Allow loading truncated images
ImageFile.LOAD_TRUNCATED_IMAGES = True

# Set up logging
logger = logging.getLogger(__name__)

# Import dependencies if available
try:
    import torchvision
    from torchvision import transforms
    from torchvision.transforms import functional as TF
    _HAS_TORCHVISION = True
except ImportError:
    logger.warning("Torchvision library not found. Some functionality will be limited.")
    _HAS_TORCHVISION = False

try:
    import timm
    from timm.data.transforms_factory import create_transform
    _HAS_TIMM = True
except ImportError:
    logger.warning("Timm library not found. Model variety will be limited.")
    _HAS_TIMM = False

try:
    from transformers import (
        AutoFeatureExtractor, AutoModel, AutoProcessor,
        CLIPProcessor, CLIPVisionModel, CLIPVisionModelWithProjection
    )
    _HAS_TRANSFORMERS = True
except ImportError:
    logger.warning("Transformers library not found. CLIP and other recent models will be unavailable.")
    _HAS_TRANSFORMERS = False

try:
    import cv2
    _HAS_OPENCV = True
except ImportError:
    logger.warning("OpenCV not found. Some preprocessing functions will be unavailable.")
    _HAS_OPENCV = False


class ImageEncoderConfig:
    """
    Configuration for image encoding and feature extraction.
    
    This class defines parameters for model selection, image preprocessing,
    and feature extraction for image data.
    """
    
    def __init__(
        self,
        model_name: str = "vit_base_patch16_224",
        image_size: Union[int, Tuple[int, int]] = 224,
        num_channels: int = 3,
        mean: List[float] = [0.485, 0.456, 0.406],  # ImageNet mean
        std: List[float] = [0.229, 0.224, 0.225],  # ImageNet std
        resize_mode: str = "resize_and_center_crop",
        patch_size: int = 16,
        num_patches: Optional[int] = None,
        use_intermediate_layers: bool = False,
        layers_to_use: Optional[List[int]] = None,
        use_patch_embeddings: bool = False,
        pooling_strategy: str = "cls",
        use_region_features: bool = False,
        num_regions: int = 36,
        normalize_embeddings: bool = True,
        use_augmentation: bool = False,
        augmentation_level: str = "light",
        custom_transforms: Optional[List[Any]] = None,
        cache_size: int = 1000,
        precomputed_patches: bool = False,
        device: Optional[Union[str, torch.device]] = None,
        use_mixed_precision: bool = False,
        embedding_dim: Optional[int] = None,
        use_fused_inference: bool = True,
        feature_level: str = "high_level",  # high_level, mid_level, low_level, multi_level
        pretrained: bool = True,
        cache_dir: Optional[str] = None,
        use_auth_token: bool = False,
        use_jit: bool = False,
        enable_attention_maps: bool = False,
        precision: str = "float32"
    ):
        """
        Initialize image encoder configuration.
        
        Args:
            model_name: Name of the vision model
            image_size: Target image size (single int or tuple)
            num_channels: Number of image channels (3 for RGB)
            mean: Normalization mean values per channel
            std: Normalization std values per channel
            resize_mode: Method for resizing images
            patch_size: Size of image patches (for ViT models)
            num_patches: Optional override for number of patches
            use_intermediate_layers: Whether to extract features from intermediate layers
            layers_to_use: Specific layers to use for features
            use_patch_embeddings: Whether to return patch-level embeddings
            pooling_strategy: Strategy for pooling features
            use_region_features: Whether to extract region-based features
            num_regions: Number of regions to extract (for region features)
            normalize_embeddings: Whether to L2-normalize embeddings
            use_augmentation: Whether to apply data augmentation
            augmentation_level: Intensity of augmentations
            custom_transforms: Custom transforms to apply
            cache_size: Size of the embedding cache
            precomputed_patches: Whether patches are precomputed
            device: Device to use for inference
            use_mixed_precision: Whether to use mixed precision
            embedding_dim: Output embedding dimension (None = use model's native dim)
            use_fused_inference: Whether to use fused inference operations
            feature_level: Level of features to extract
            pretrained: Whether to use pretrained weights
            cache_dir: Directory to cache models
            use_auth_token: Whether to use auth token for private models
            use_jit: Whether to use JIT compilation for faster inference
            enable_attention_maps: Whether to compute attention maps
            precision: Numerical precision ('float32', 'float16', or 'bfloat16')
        """
        self.model_name = model_name
        self.image_size = image_size if isinstance(image_size, tuple) else (image_size, image_size)
        self.num_channels = num_channels
        self.mean = mean
        self.std = std
        self.resize_mode = resize_mode
        self.patch_size = patch_size
        self.num_patches = num_patches
        self.use_intermediate_layers = use_intermediate_layers
        self.layers_to_use = layers_to_use
        self.use_patch_embeddings = use_patch_embeddings
        self.pooling_strategy = pooling_strategy
        self.use_region_features = use_region_features
        self.num_regions = num_regions
        self.normalize_embeddings = normalize_embeddings
        self.use_augmentation = use_augmentation
        self.augmentation_level = augmentation_level
        self.custom_transforms = custom_transforms
        self.cache_size = cache_size
        self.precomputed_patches = precomputed_patches
        self.device = device if device is not None else (
            torch.device('cuda') if torch.cuda.is_available() else torch.device('cpu')
        )
        self.use_mixed_precision = use_mixed_precision
        self.embedding_dim = embedding_dim
        self.use_fused_inference = use_fused_inference
        self.feature_level = feature_level
        self.pretrained = pretrained
        self.cache_dir = cache_dir
        self.use_auth_token = use_auth_token
        self.use_jit = use_jit
        self.enable_attention_maps = enable_attention_maps
        self.precision = precision
        
        # Validate configuration
        self._validate()
        
        logger.info(f"Initialized ImageEncoderConfig with model_name={model_name}")
    
    def _validate(self):
        """Validate configuration parameters."""
        # Validate image size
        if isinstance(self.image_size, tuple):
            if len(self.image_size) != 2 or any(s <= 0 for s in self.image_size):
                raise ValueError(f"image_size must be a tuple of positive integers, got {self.image_size}")
        
        # Validate normalization parameters
        if len(self.mean) != self.num_channels or len(self.std) != self.num_channels:
            raise ValueError(f"mean and std must have length {self.num_channels}")
            
        # Validate pooling strategy
        valid_pooling = ['mean', 'max', 'cls', 'token', 'spatial_mean', 'attention', 'none']
        if self.pooling_strategy not in valid_pooling:
            raise ValueError(f"pooling_strategy must be one of {valid_pooling}")
            
        # Validate feature level
        valid_features = ['high_level', 'mid_level', 'low_level', 'multi_level']
        if self.feature_level not in valid_features:
            raise ValueError(f"feature_level must be one of {valid_features}")
            
        # Validate augmentation level
        valid_aug = ['none', 'light', 'medium', 'heavy', 'custom']
        if self.augmentation_level not in valid_aug:
            raise ValueError(f"augmentation_level must be one of {valid_aug}")
            
        # Validate if custom transforms are provided when specified
        if self.augmentation_level == 'custom' and not self.custom_transforms:
            raise ValueError("Must provide custom_transforms when augmentation_level is 'custom'")
            
        # Validate resize mode
        valid_resize = ['resize', 'resize_and_center_crop', 'resize_and_pad', 'resize_shortest_edge', 'letterbox']
        if self.resize_mode not in valid_resize:
            raise ValueError(f"resize_mode must be one of {valid_resize}")
            
        # Validate precision
        valid_precision = ['float32', 'float16', 'bfloat16']
        if self.precision not in valid_precision:
            raise ValueError(f"precision must be one of {valid_precision}")
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert configuration to dictionary.
        
        Returns:
            Dict[str, Any]: Configuration as dictionary
        """
        # Convert device to string for serialization
        config_dict = {k: v for k, v in self.__dict__.items() if not k.startswith('_')}
        if 'device' in config_dict and isinstance(config_dict['device'], torch.device):
            config_dict['device'] = str(config_dict['device'])
        return config_dict
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'ImageEncoderConfig':
        """
        Create configuration from dictionary.
        
        Args:
            config_dict: Dictionary containing configuration parameters
            
        Returns:
            ImageEncoderConfig: Configuration object
        """
        return cls(**config_dict)
    
    def save(self, file_path: str) -> None:
        """
        Save configuration to JSON file.
        
        Args:
            file_path: Path to save the configuration file
        """
        import json
        with open(file_path, 'w') as f:
            json.dump(self.to_dict(), f, indent=2)
    
    @classmethod
    def load(cls, file_path: str) -> 'ImageEncoderConfig':
        """
        Load configuration from JSON file.
        
        Args:
            file_path: Path to the configuration file
            
        Returns:
            ImageEncoderConfig: Configuration object
        """
        import json
        with open(file_path, 'r') as f:
            config_dict = json.load(f)
        return cls.from_dict(config_dict)


class ImageProcessor:
    """
    Image preprocessing utilities for ULTRA.
    
    This class provides methods for preparing images for model inference,
    including loading, resizing, normalization, and augmentation.
    """
    
    def __init__(self, config: ImageEncoderConfig):
        """
        Initialize image processor with configuration.
        
        Args:
            config: Configuration for image processing
        """
        self.config = config
        
        # Create transform pipeline
        self.transform = self._create_transform_pipeline()
        
        # Create augmentation pipeline if needed
        self.augmentation = None
        if config.use_augmentation:
            self.augmentation = self._create_augmentation_pipeline()
            
        logger.info(f"Initialized ImageProcessor with transform pipeline")
        
    def _create_transform_pipeline(self):
        """
        Create the image transformation pipeline.
        
        Returns:
            callable: Transformation pipeline
        """
        if not _HAS_TORCHVISION:
            raise ImportError("Torchvision is required for image transformations")
            
        # Check if we're dealing with a known model from transformers
        is_clip_model = "clip" in self.config.model_name.lower()
        is_vit_model = "vit" in self.config.model_name.lower()
        
        # Use specialized pipeline for CLIP if available
        if is_clip_model and _HAS_TRANSFORMERS:
            try:
                from transformers import CLIPProcessor, CLIPModel, CLIPTokenizer
                model_name = self.config.model_name
                if not model_name.startswith("openai/") and not model_name.startswith("laion/"):
                    model_name = f"openai/{model_name}"
                processor = CLIPProcessor.from_pretrained(
                    model_name,
                    cache_dir=self.config.cache_dir,
                    use_auth_token=self.config.use_auth_token
                )
                
                # Create a wrapper function that matches our expected interface
                def clip_transform(img):
                    # Convert to RGB if needed
                    if img.mode != "RGB":
                        img = img.convert("RGB")
                    # Process image and return pixel values
                    return processor(images=img, return_tensors="pt")["pixel_values"][0]
                
                return clip_transform
            except Exception as e:
                logger.warning(f"Failed to load CLIP processor: {e}. Falling back to standard transforms.")
        
        # For ViT models from transformers, use their preprocessing if available
        elif is_vit_model and _HAS_TRANSFORMERS:
            try:
                from transformers import AutoFeatureExtractor
                extractor = AutoFeatureExtractor.from_pretrained(
                    self.config.model_name,
                    cache_dir=self.config.cache_dir,
                    use_auth_token=self.config.use_auth_token
                )
                
                # Create a wrapper function that matches our expected interface
                def vit_transform(img):
                    # Convert to RGB if needed
                    if img.mode != "RGB":
                        img = img.convert("RGB")
                    # Process image and return pixel values
                    return extractor(images=img, return_tensors="pt")["pixel_values"][0]
                
                return vit_transform
            except Exception as e:
                logger.warning(f"Failed to load ViT feature extractor: {e}. Falling back to standard transforms.")
        
        # For timm models, use their transforms if available
        if _HAS_TIMM and any(name in self.config.model_name for name in ['resnet', 'efficientnet', 'vit', 'deit', 'beit']):
            try:
                transform = create_transform(
                    input_size=self.config.image_size[0],
                    is_training=False,
                    mean=self.config.mean,
                    std=self.config.std
                )
                return transform
            except Exception as e:
                logger.warning(f"Failed to create timm transform: {e}. Falling back to standard transforms.")
        
        # Default transform pipeline using torchvision
        transform_list = []
        
        # Resize based on configured mode
        if self.config.resize_mode == 'resize':
            transform_list.append(transforms.Resize(self.config.image_size))
        elif self.config.resize_mode == 'resize_and_center_crop':
            transform_list.append(transforms.Resize(int(max(self.config.image_size) * 1.14)))
            transform_list.append(transforms.CenterCrop(self.config.image_size))
        elif self.config.resize_mode == 'resize_and_pad':
            transform_list.append(
                transforms.Lambda(lambda img: self._resize_and_pad(img, self.config.image_size))
            )
        elif self.config.resize_mode == 'resize_shortest_edge':
            transform_list.append(
                transforms.Lambda(lambda img: self._resize_shortest_edge(img, min(self.config.image_size)))
            )
        elif self.config.resize_mode == 'letterbox':
            transform_list.append(
                transforms.Lambda(lambda img: self._letterbox(img, self.config.image_size))
            )
        else:
            # Default to simple resize
            transform_list.append(transforms.Resize(self.config.image_size))
        
        # Convert to tensor and normalize
        transform_list.append(transforms.ToTensor())
        transform_list.append(transforms.Normalize(mean=self.config.mean, std=self.config.std))
        
        return transforms.Compose(transform_list)
    
    def _create_augmentation_pipeline(self):
        """
        Create data augmentation pipeline based on configured level.
        
        Returns:
            callable: Augmentation pipeline
        """
        if not _HAS_TORCHVISION:
            raise ImportError("Torchvision is required for augmentations")
            
        # If custom transforms are provided, use those
        if self.config.augmentation_level == 'custom' and self.config.custom_transforms:
            return transforms.Compose(self.config.custom_transforms)
        
        # Create different augmentation levels
        if self.config.augmentation_level == 'light':
            return transforms.Compose([
                transforms.RandomHorizontalFlip(),
                transforms.ColorJitter(brightness=0.1, contrast=0.1, saturation=0.1),
                transforms.RandomAffine(degrees=10, translate=(0.05, 0.05), scale=(0.95, 1.05))
            ])
        elif self.config.augmentation_level == 'medium':
            return transforms.Compose([
                transforms.RandomHorizontalFlip(),
                transforms.ColorJitter(brightness=0.2, contrast=0.2, saturation=0.2, hue=0.1),
                transforms.RandomAffine(degrees=15, translate=(0.1, 0.1), scale=(0.9, 1.1)),
                transforms.RandomPerspective(distortion_scale=0.2, p=0.5),
                transforms.RandomGrayscale(p=0.1)
            ])
        elif self.config.augmentation_level == 'heavy':
            return transforms.Compose([
                transforms.RandomHorizontalFlip(),
                transforms.RandomVerticalFlip(p=0.1),
                transforms.ColorJitter(brightness=0.3, contrast=0.3, saturation=0.3, hue=0.15),
                transforms.RandomAffine(degrees=30, translate=(0.15, 0.15), scale=(0.8, 1.2), shear=10),
                transforms.RandomPerspective(distortion_scale=0.5, p=0.5),
                transforms.RandomGrayscale(p=0.2),
                transforms.RandomApply([transforms.GaussianBlur(kernel_size=5, sigma=(0.1, 2.0))], p=0.2),
                transforms.RandomAutocontrast(p=0.2)
            ])
        else:  # 'none' or any other value
            return None
    
    def _resize_and_pad(self, img, target_size):
        """
        Resize image and pad to target size while preserving aspect ratio.
        
        Args:
            img: PIL Image
            target_size: Target size as (height, width) tuple
            
        Returns:
            PIL.Image: Resized and padded image
        """
        target_height, target_width = target_size
        width, height = img.size
        
        # Calculate scaling factor to fit within target size
        scale = min(target_width / width, target_height / height)
        new_width = int(width * scale)
        new_height = int(height * scale)
        
        # Resize image
        resized_img = img.resize((new_width, new_height), Image.LANCZOS)
        
        # Create new image with padding
        padded_img = Image.new("RGB", (target_width, target_height), (0, 0, 0))
        
        # Paste resized image in center
        padded_img.paste(
            resized_img, 
            ((target_width - new_width) // 2, (target_height - new_height) // 2)
        )
        
        return padded_img
    
    def _resize_shortest_edge(self, img, target_size):
        """
        Resize image so that shortest edge matches target size.
        
        Args:
            img: PIL Image
            target_size: Target size for shortest edge
            
        Returns:
            PIL.Image: Resized image
        """
        width, height = img.size
        
        # Calculate scaling factor
        scale = target_size / min(width, height)
        new_width = int(width * scale)
        new_height = int(height * scale)
        
        # Resize image
        return img.resize((new_width, new_height), Image.LANCZOS)
    
    def _letterbox(self, img, target_size):
        """
        Resize image using letterboxing (preserve aspect ratio with gray borders).
        
        Args:
            img: PIL Image
            target_size: Target size as (height, width) tuple
            
        Returns:
            PIL.Image: Letterboxed image
        """
        target_height, target_width = target_size
        width, height = img.size
        
        # Calculate scaling factor to fit within target size
        scale = min(target_width / width, target_height / height)
        new_width = int(width * scale)
        new_height = int(height * scale)
        
        # Resize image
        resized_img = img.resize((new_width, new_height), Image.LANCZOS)
        
        # Create new image with padding (gray color)
        letterboxed_img = Image.new("RGB", (target_width, target_height), (114, 114, 114))
        
        # Paste resized image in center
        letterboxed_img.paste(
            resized_img, 
            ((target_width - new_width) // 2, (target_height - new_height) // 2)
        )
        
        return letterboxed_img
    
    def _extract_patches(self, img_tensor, patch_size=None):
        """
        Extract patches from image tensor.
        
        Args:
            img_tensor: Image tensor [C, H, W]
            patch_size: Size of patches (default: from config)
            
        Returns:
            torch.Tensor: Patches [N_patches, C, patch_size, patch_size]
        """
        if patch_size is None:
            patch_size = self.config.patch_size
            
        # Get dimensions
        c, h, w = img_tensor.shape
        
        # Calculate number of patches along each dimension
        n_h = h // patch_size
        n_w = w // patch_size
        
        # Extract patches using unfold
        patches = img_tensor.unfold(1, patch_size, patch_size).unfold(2, patch_size, patch_size)
        patches = patches.contiguous().view(c, n_h * n_w, patch_size, patch_size)
        patches = patches.permute(1, 0, 2, 3)  # [N_patches, C, patch_size, patch_size]
        
        return patches
    
    def _extract_regions(self, img_tensor, num_regions=None):
        """
        Extract region features using adaptive pooling.
        
        Args:
            img_tensor: Image tensor [C, H, W]
            num_regions: Number of regions (default: from config)
            
        Returns:
            torch.Tensor: Region features [num_regions, C]
        """
        if num_regions is None:
            num_regions = self.config.num_regions
            
        # Calculate grid dimensions (try to keep aspect ratio)
        c, h, w = img_tensor.shape
        aspect_ratio = w / h
        grid_w = int(math.sqrt(num_regions * aspect_ratio))
        grid_h = int(num_regions / grid_w)
        
        # Ensure we have exactly num_regions regions
        while grid_w * grid_h < num_regions:
            if grid_w / grid_h < aspect_ratio:
                grid_w += 1
            else:
                grid_h += 1
        
        # Use adaptive pooling to extract features
        pooled = F.adaptive_avg_pool2d(img_tensor.unsqueeze(0), (grid_h, grid_w))
        regions = pooled.view(c, grid_h * grid_w).transpose(0, 1)
        
        # Truncate to exact number of regions if needed
        if regions.size(0) > num_regions:
            regions = regions[:num_regions]
            
        return regions
    
    def _pil_to_tensor(self, img):
        """
        Convert PIL image to tensor with optional normalization.
        
        Args:
            img: PIL Image
            
        Returns:
            torch.Tensor: Image tensor [C, H, W]
        """
        # Convert to RGB if needed
        if img.mode != "RGB":
            img = img.convert("RGB")
            
        # Apply transformations
        return self.transform(img)
    
    def _apply_augmentation(self, img):
        """
        Apply augmentation to PIL image.
        
        Args:
            img: PIL Image
            
        Returns:
            PIL.Image: Augmented image
        """
        if self.augmentation is not None:
            return self.augmentation(img)
        return img
    
    def process_image(self, img, apply_augmentation=False):
        """
        Process a single image.
        
        Args:
            img: PIL Image or path to image
            apply_augmentation: Whether to apply augmentation
            
        Returns:
            torch.Tensor: Processed image tensor [C, H, W]
        """
        # Load image if path is provided
        if isinstance(img, str):
            img = self.load_image(img)
            
        # Make sure we have a PIL Image
        if not isinstance(img, Image.Image):
            raise TypeError(f"Expected PIL.Image or path string, got {type(img)}")
            
        # Apply augmentation if requested
        if apply_augmentation and self.config.use_augmentation:
            img = self._apply_augmentation(img)
            
        # Convert to tensor
        return self._pil_to_tensor(img)
    
    def process_batch(self, images, apply_augmentation=False):
        """
        Process a batch of images.
        
        Args:
            images: List of PIL Images or paths
            apply_augmentation: Whether to apply augmentation
            
        Returns:
            torch.Tensor: Batch of processed images [B, C, H, W]
        """
        # Process each image
        batch = []
        for img in images:
            processed = self.process_image(img, apply_augmentation)
            batch.append(processed)
            
        # Stack into batch
        return torch.stack(batch)
    
    @staticmethod
    def load_image(img_path):
        """
        Load image from path.
        
        Args:
            img_path: Path to image
            
        Returns:
            PIL.Image: Loaded image
        """
        try:
            return Image.open(img_path).convert("RGB")
        except Exception as e:
            logger.error(f"Failed to load image from {img_path}: {e}")
            raise


class VisionModelBackend:
    """
    Backend for vision model inference.
    
    This class provides a common interface for different vision model
    implementations, handling model loading and feature extraction.
    """
    
    def __init__(self, config: ImageEncoderConfig):
        """
        Initialize vision model backend.
        
        Args:
            config: Configuration for the model
        """
        self.config = config
        self.device = config.device
        self.model = None
        self.hooks = []
        self.intermediate_features = {}
        self.feature_dims = {}
        self.attention_maps = None
        
        # Set precision
        self.dtype = {
            'float32': torch.float32,
            'float16': torch.float16,
            'bfloat16': torch.bfloat16 if hasattr(torch, 'bfloat16') else torch.float16
        }[config.precision]
        
        # Load model
        self._load_model()
        
        # Register hooks for intermediate features if needed
        if config.use_intermediate_layers:
            self._register_hooks()
            
        logger.info(f"Initialized vision model backend with {config.model_name}")
    
    def _load_model(self):
        """Load the vision model according to configuration."""
        model_name = self.config.model_name
        
        # Check if this is a CLIP model
        is_clip_model = "clip" in model_name.lower()
        
        # Check if this is a vision transformer 
        is_vit_model = any(name in model_name.lower() for name in ['vit', 'deit', 'beit'])
        
        # Handle CLIP models from Hugging Face
        if is_clip_model and _HAS_TRANSFORMERS:
            try:
                # Normalize model name for CLIP
                if not model_name.startswith("openai/") and not model_name.startswith("laion/"):
                    model_name = f"openai/{model_name}"
                
                # Load vision model only
                self.model = CLIPVisionModelWithProjection.from_pretrained(
                    model_name,
                    cache_dir=self.config.cache_dir,
                    use_auth_token=self.config.use_auth_token
                )
                
                # Set to evaluation mode
                self.model.eval()
                
                # Get feature dimensions
                self.feature_dims = {
                    'default': self.model.config.projection_dim,
                    'hidden': self.model.config.hidden_size,
                    'patches': self.model.config.hidden_size
                }
                
                logger.info(f"Loaded CLIP vision model: {model_name}")
                
            except Exception as e:
                logger.error(f"Failed to load CLIP model: {e}")
                raise
        
        # Handle other models from Hugging Face transformers
        elif _HAS_TRANSFORMERS and (is_vit_model or model_name.startswith("facebook/") or model_name.startswith("google/")):
            try:
                # Load feature extractor and model
                from transformers import AutoFeatureExtractor, AutoModel
                self.feature_extractor = AutoFeatureExtractor.from_pretrained(
                    model_name,
                    cache_dir=self.config.cache_dir,
                    use_auth_token=self.config.use_auth_token
                )
                
                self.model = AutoModel.from_pretrained(
                    model_name,
                    cache_dir=self.config.cache_dir,
                    use_auth_token=self.config.use_auth_token
                )
                
                # Set to evaluation mode
                self.model.eval()
                
                # Get feature dimensions
                if hasattr(self.model.config, "hidden_size"):
                    self.feature_dims = {
                        'default': self.model.config.hidden_size,
                        'hidden': self.model.config.hidden_size,
                        'patches': self.model.config.hidden_size
                    }
                else:
                    # Fallback for models without clear hidden size
                    self.feature_dims = {
                        'default': 768,  # Common default
                        'hidden': 768,
                        'patches': 768
                    }
                
                logger.info(f"Loaded transformers vision model: {model_name}")
                
            except Exception as e:
                logger.error(f"Failed to load transformers model: {e}")
                raise
        
        # Handle models from timm
        elif _HAS_TIMM:
            try:
                # Load model from timm
                self.model = timm.create_model(
                    model_name,
                    pretrained=self.config.pretrained,
                    num_classes=0,  # Remove classifier head
                    features_only=self.config.use_intermediate_layers
                )
                
                # Set to evaluation mode
                self.model.eval()
                
                # Get feature dimensions
                if hasattr(self.model, "feature_info") and self.model.feature_info.channels():
                    # For models that return multiple feature maps
                    channels = self.model.feature_info.channels()
                    self.feature_dims = {
                        'default': channels[-1],  # Last layer features
                        'low_level': channels[0] if len(channels) > 0 else channels[-1],
                        'mid_level': channels[len(channels)//2] if len(channels) > 2 else channels[-1],
                        'high_level': channels[-1]
                    }
                elif hasattr(self.model, "num_features"):
                    # For models with num_features attribute
                    self.feature_dims = {
                        'default': self.model.num_features,
                        'hidden': self.model.num_features,
                        'patches': self.model.num_features
                    }
                else:
                    # Fallback for other models
                    self.feature_dims = {
                        'default': 2048,  # Common for ResNet, adjust as needed
                        'hidden': 2048,
                        'patches': 2048
                    }
                
                logger.info(f"Loaded timm vision model: {model_name}")
                
            except Exception as e:
                logger.error(f"Failed to load timm model: {e}")
                raise
        
        # Handle models from torchvision
        elif _HAS_TORCHVISION:
            try:
                # Try to load from torchvision
                model_fn = getattr(torchvision.models, model_name, None)
                
                if model_fn is None:
                    # Try to find a close match
                    for name in dir(torchvision.models):
                        if model_name.lower() in name.lower() and callable(getattr(torchvision.models, name)):
                            model_fn = getattr(torchvision.models, name)
                            logger.info(f"Using torchvision model {name} as a match for {model_name}")
                            break
                
                if model_fn is None:
                    raise ValueError(f"Model {model_name} not found in torchvision")
                
                # Load model
                self.model = model_fn(pretrained=self.config.pretrained)
                
                # Remove classifier for feature extraction
                if hasattr(self.model, "fc"):
                    feature_dim = self.model.fc.in_features
                    self.model.fc = nn.Identity()
                elif hasattr(self.model, "classifier"):
                    if isinstance(self.model.classifier, nn.Sequential):
                        feature_dim = self.model.classifier[0].in_features
                    else:
                        feature_dim = self.model.classifier.in_features
                    self.model.classifier = nn.Identity()
                else:
                    # Try to find the classifier by examining the model
                    feature_dim = self._find_feature_dim(self.model)
                
                # Set to evaluation mode
                self.model.eval()
                
                # Store feature dimensions
                self.feature_dims = {
                    'default': feature_dim,
                    'hidden': feature_dim,
                    'patches': feature_dim
                }
                
                logger.info(f"Loaded torchvision model: {model_name}")
                
            except Exception as e:
                logger.error(f"Failed to load torchvision model: {e}")
                raise
        
        else:
            raise ImportError(f"No suitable library found for loading model {model_name}. "
                             f"Please install timm, transformers, or torchvision.")
        
        # Move model to device and set precision
        self.model.to(self.device)
        self.model.to(dtype=self.dtype)
        
        # Apply JIT compilation if requested
        if self.config.use_jit and torch.jit.is_scripting():
            try:
                self.model = torch.jit.script(self.model)
                logger.info(f"Applied JIT compilation to model")
            except Exception as e:
                logger.warning(f"Failed to apply JIT compilation: {e}")
    
    def _find_feature_dim(self, model):
        """
        Attempt to find the feature dimension of the model by examining its structure.
        
        Args:
            model: Vision model
            
        Returns:
            int: Feature dimension
        """
        # Common feature dimensions for known architectures
        if any(name in self.config.model_name.lower() for name in ['resnet18', 'resnet34']):
            return 512
        elif any(name in self.config.model_name.lower() for name in ['resnet50', 'resnet101', 'resnet152']):
            return 2048
        elif 'vgg' in self.config.model_name.lower():
            return 4096
        elif 'densenet' in self.config.model_name.lower():
            return 1024
        elif 'efficientnet' in self.config.model_name.lower():
            return 1280
        elif 'mobilenet' in self.config.model_name.lower():
            return 1280
        else:
            # Default to a common value
            logger.warning(f"Could not determine feature dimension for {self.config.model_name}, using default")
            return 2048
    
    def _register_hooks(self):
        """Register hooks to extract intermediate features from the model."""
        self.hooks = []
        self.intermediate_features = {}
        
        # Function to get features
        def get_features(name):
            def hook(model, input, output):
                # Store feature map
                self.intermediate_features[name] = output
            return hook
        
        # Different hook registration based on model type
        if "clip" in self.config.model_name.lower() and hasattr(self.model, "vision_model"):
            # For CLIP models
            layers = self.model.vision_model.encoder.layers
            
            # Register hooks for specific layers or all layers
            if self.config.layers_to_use:
                for i in self.config.layers_to_use:
                    if 0 <= i < len(layers):
                        hook = layers[i].register_forward_hook(get_features(f"layer_{i}"))
                        self.hooks.append(hook)
            else:
                # Register for first, middle, and last layers by default
                indices = [0, len(layers)//2, len(layers)-1]
                for i in indices:
                    hook = layers[i].register_forward_hook(get_features(f"layer_{i}"))
                    self.hooks.append(hook)
            
            # Register hook for attention if enabled
            if self.config.enable_attention_maps and hasattr(self.model.vision_model, "encoder"):
                for i, layer in enumerate(self.model.vision_model.encoder.layers):
                    if hasattr(layer, "self_attn"):
                        def get_attention(name):
                            def attn_hook(module, input, output):
                                # Extract attention weights (may need adjustment based on model)
                                if isinstance(output, tuple) and len(output) > 1:
                                    self.attention_maps[name] = output[1]  # Often the second element is attention weights
                                else:
                                    logger.warning(f"Could not extract attention map from {name}")
                            return attn_hook
                        
                        hook = layer.self_attn.register_forward_hook(get_attention(f"attn_{i}"))
                        self.hooks.append(hook)
        
        elif hasattr(self.model, "encoder") and hasattr(self.model.encoder, "layer"):
            # For ViT and similar models
            layers = self.model.encoder.layer
            
            # Register hooks for specific layers or all layers
            if self.config.layers_to_use:
                for i in self.config.layers_to_use:
                    if 0 <= i < len(layers):
                        hook = layers[i].register_forward_hook(get_features(f"layer_{i}"))
                        self.hooks.append(hook)
            else:
                # Register for first, middle, and last layers by default
                indices = [0, len(layers)//2, len(layers)-1]
                for i in indices:
                    hook = layers[i].register_forward_hook(get_features(f"layer_{i}"))
                    self.hooks.append(hook)
                    
        elif hasattr(self.model, "blocks"):
            # For timm ViT models
            blocks = self.model.blocks
            
            # Register hooks for specific layers or all layers
            if self.config.layers_to_use:
                for i in self.config.layers_to_use:
                    if 0 <= i < len(blocks):
                        hook = blocks[i].register_forward_hook(get_features(f"block_{i}"))
                        self.hooks.append(hook)
            else:
                # Register for first, middle, and last layers by default
                indices = [0, len(blocks)//2, len(blocks)-1]
                for i in indices:
                    hook = blocks[i].register_forward_hook(get_features(f"block_{i}"))
                    self.hooks.append(hook)
                    
        elif hasattr(self.model, "layer1"):
            # For ResNet and similar models
            layers = [self.model.layer1, self.model.layer2, self.model.layer3, self.model.layer4]
            
            # Register hooks for specific layers or all layers
            if self.config.layers_to_use:
                for i in self.config.layers_to_use:
                    if 0 <= i < len(layers):
                        hook = layers[i].register_forward_hook(get_features(f"layer_{i+1}"))
                        self.hooks.append(hook)
            else:
                # Register for all main layers
                for i, layer in enumerate(layers):
                    hook = layer.register_forward_hook(get_features(f"layer_{i+1}"))
                    self.hooks.append(hook)
        
        else:
            # For other model types, try to find suitable layers
            for name, module in self.model.named_modules():
                if any(x in name for x in ['layer', 'block', 'encoder', 'backbone']):
                    if len(name.split('.')) <= 3:  # Avoid registering hooks too deep
                        hook = module.register_forward_hook(get_features(name))
                        self.hooks.append(hook)
    
    def _remove_hooks(self):
        """Remove all registered hooks."""
        for hook in self.hooks:
            hook.remove()
        self.hooks = []
    
    def __del__(self):
        """Cleanup on deletion."""
        self._remove_hooks()
    
    def get_patch_embeddings(self, features):
        """
        Extract patch embeddings from model features.
        
        Args:
            features: Model output features
            
        Returns:
            torch.Tensor: Patch embeddings
        """
        # For CLIP-like models
        if hasattr(features, "last_hidden_state"):
            # Return all token embeddings except the class token
            if features.last_hidden_state.size(1) > 1:  # Has class token
                return features.last_hidden_state[:, 1:, :]
            else:
                return features.last_hidden_state
                
        # For ViT-like models
        elif isinstance(features, torch.Tensor) and features.dim() == 3:
            # Return all token embeddings except the class token
            if features.size(1) > 1:  # Has class token
                return features[:, 1:, :]
            else:
                return features
                
        # For CNN feature maps
        elif isinstance(features, torch.Tensor) and features.dim() == 4:
            # Reshape from [B, C, H, W] to [B, H*W, C]
            b, c, h, w = features.shape
            return features.flatten(2).transpose(1, 2)
            
        else:
            logger.warning(f"Unsupported feature format for patch extraction: {type(features)}")
            return None
    
    def extract_features(self, images):
        """
        Extract features from images.
        
        Args:
            images: Image tensors of shape [B, C, H, W]
            
        Returns:
            Dict[str, torch.Tensor]: Dictionary of extracted features
        """
        if images.dim() != 4:
            raise ValueError(f"Expected 4D input tensor [B, C, H, W], got shape {images.shape}")
            
        # Initialize attention maps if enabled
        if self.config.enable_attention_maps:
            self.attention_maps = {}
            
        # Forward pass through model
        with torch.no_grad():
            # Apply mixed precision if configured
            with torch.cuda.amp.autocast(enabled=self.config.use_mixed_precision):
                if "clip" in self.config.model_name.lower() and hasattr(self.model, "vision_model"):
                    # For CLIP models
                    outputs = self.model(pixel_values=images)
                    features = outputs.image_embeds  # Get the projected embeddings
                    hidden_states = outputs.last_hidden_state if hasattr(outputs, "last_hidden_state") else None
                    
                elif hasattr(self.model, "forward_features"):
                    # For timm models with forward_features method
                    features = self.model.forward_features(images)
                else:
                    # For standard models
                    features = self.model(images)
        
        # Prepare output dictionary
        feature_dict = {'default': features}
        
        # Add intermediate features if captured
        if self.intermediate_features:
            feature_dict.update({
                'intermediate': self.intermediate_features
            })
            
            # Add specific feature levels based on configuration
            if self.config.feature_level == 'low_level':
                # Use features from early layers
                keys = sorted(self.intermediate_features.keys())
                if keys:
                    feature_dict['selected'] = self.intermediate_features[keys[0]]
            elif self.config.feature_level == 'mid_level':
                # Use features from middle layers
                keys = sorted(self.intermediate_features.keys())
                if keys:
                    mid_idx = len(keys) // 2
                    feature_dict['selected'] = self.intermediate_features[keys[mid_idx]]
            elif self.config.feature_level == 'high_level':
                # Use features from late layers
                keys = sorted(self.intermediate_features.keys())
                if keys:
                    feature_dict['selected'] = self.intermediate_features[keys[-1]]
            elif self.config.feature_level == 'multi_level':
                # Provide features from all levels
                feature_dict['low_level'] = self.intermediate_features.get(sorted(self.intermediate_features.keys())[0], features)
                keys = sorted(self.intermediate_features.keys())
                if len(keys) > 2:
                    feature_dict['mid_level'] = self.intermediate_features[keys[len(keys)//2]]
                feature_dict['high_level'] = self.intermediate_features.get(sorted(self.intermediate_features.keys())[-1], features)
        
        # Add patch embeddings if configured
        if self.config.use_patch_embeddings:
            patch_embeddings = self.get_patch_embeddings(features)
            if patch_embeddings is not None:
                feature_dict['patches'] = patch_embeddings
                
        # Add attention maps if available
        if self.config.enable_attention_maps and self.attention_maps:
            feature_dict['attention_maps'] = self.attention_maps
            
        return feature_dict
    
    def apply_pooling(self, features, mask=None):
        """
        Apply pooling to features according to the configured strategy.
        
        Args:
            features: Features to pool [B, ...]
            mask: Optional attention mask
            
        Returns:
            torch.Tensor: Pooled features
        """
        # Handle different feature formats
        if isinstance(features, dict):
            # Use default features if specific format provided
            if 'selected' in features:
                return self.apply_pooling(features['selected'], mask)
            elif 'default' in features:
                return self.apply_pooling(features['default'], mask)
            else:
                # Try to find the most appropriate features
                for key in ['high_level', 'last_hidden_state', 'image_embeds']:
                    if key in features:
                        return self.apply_pooling(features[key], mask)
                
                # Fall back to the first available feature
                return self.apply_pooling(next(iter(features.values())), mask)
        
        # For CLIP models that return projected embeddings directly
        if features.dim() == 2:
            return features
            
        # For features with sequence dimension [B, N, D]
        elif features.dim() == 3:
            if self.config.pooling_strategy == 'cls' or self.config.pooling_strategy == 'token':
                # Use first token (CLS/global token)
                return features[:, 0]
                
            elif self.config.pooling_strategy == 'mean':
                # Mean pooling over tokens
                if mask is not None:
                    # Apply mask for proper mean
                    mask_expanded = mask.unsqueeze(-1).float()
                    features = features * mask_expanded
                    return features.sum(1) / mask_expanded.sum(1).clamp(min=1e-9)
                else:
                    return features.mean(1)
                    
            elif self.config.pooling_strategy == 'max':
                # Max pooling over tokens
                if mask is not None:
                    # Apply mask
                    mask_expanded = mask.unsqueeze(-1).float()
                    features = features * mask_expanded + (1 - mask_expanded) * -1e9
                return features.max(1)[0]
                
            elif self.config.pooling_strategy == 'attention':
                # Attention pooling
                B, N, D = features.shape
                
                # Create attention weights or use default uniform attention
                if not hasattr(self, 'attention_pooling'):
                    self.attention_pooling = nn.Linear(D, 1).to(features.device)
                    # Initialize with small weights for stability
                    nn.init.xavier_uniform_(self.attention_pooling.weight, gain=0.01)
                    
                # Compute attention scores
                scores = self.attention_pooling(features).squeeze(-1)
                
                # Apply mask if provided
                if mask is not None:
                    scores = scores.masked_fill(~mask, -1e9)
                    
                # Apply softmax
                weights = F.softmax(scores, dim=1)
                
                # Weighted sum
                return torch.bmm(weights.unsqueeze(1), features).squeeze(1)
                
            elif self.config.pooling_strategy == 'none':
                # Return full sequence of token features
                return features
                
            else:
                # Default to mean pooling
                return features.mean(1)
                
        # For CNN feature maps [B, C, H, W]
        elif features.dim() == 4:
            if self.config.pooling_strategy == 'spatial_mean':
                # Global average pooling
                return F.adaptive_avg_pool2d(features, 1).squeeze(-1).squeeze(-1)
                
            elif self.config.pooling_strategy == 'max':
                # Global max pooling
                return F.adaptive_max_pool2d(features, 1).squeeze(-1).squeeze(-1)
                
            elif self.config.pooling_strategy == 'attention':
                # Spatial attention pooling
                B, C, H, W = features.shape
                
                # Reshape to sequence
                features_seq = features.flatten(2).transpose(1, 2)  # [B, H*W, C]
                
                # Create attention weights or use default uniform attention
                if not hasattr(self, 'spatial_attention'):
                    self.spatial_attention = nn.Linear(C, 1).to(features.device)
                    nn.init.xavier_uniform_(self.spatial_attention.weight, gain=0.01)
                    
                # Compute attention scores
                scores = self.spatial_attention(features_seq).squeeze(-1)  # [B, H*W]
                
                # Apply softmax
                weights = F.softmax(scores, dim=1)  # [B, H*W]
                
                # Weighted sum
                return torch.bmm(weights.unsqueeze(1), features_seq).squeeze(1)  # [B, C]
                
            elif self.config.pooling_strategy == 'none':
                # Return full feature map
                return features
                
            else:
                # Default to global average pooling
                return F.adaptive_avg_pool2d(features, 1).squeeze(-1).squeeze(-1)
        
        else:
            logger.warning(f"Unsupported feature shape for pooling: {features.shape}")
            return features


class ImageEncoder(nn.Module):
    """
    Image encoder for ULTRA architecture.
    
    This class provides comprehensive image encoding capabilities,
    transforming raw image inputs into neural representations suitable
    for the ULTRA architecture.
    """
    
    def __init__(self, config: Optional[Union[ImageEncoderConfig, dict]] = None):
        """
        Initialize image encoder.
        
        Args:
            config: Configuration for the encoder
        """
        super().__init__()
        
        # Handle configuration
        if config is None:
            self.config = ImageEncoderConfig()
        elif isinstance(config, dict):
            self.config = ImageEncoderConfig(**config)
        else:
            self.config = config
            
        # Set device
        self.device = self.config.device
        
        # Initialize image processor
        self.processor = ImageProcessor(self.config)
        
        # Initialize vision model backend
        self.backend = VisionModelBackend(self.config)
        
        # Create projection layer if needed
        if self.config.embedding_dim is not None and 'default' in self.backend.feature_dims:
            native_dim = self.backend.feature_dims['default']
            
            # Create projection if dimensions differ
            if native_dim != self.config.embedding_dim:
                self.projection = nn.Linear(native_dim, self.config.embedding_dim)
                # Initialize with appropriate scaling
                nn.init.xavier_uniform_(self.projection.weight)
                nn.init.zeros_(self.projection.bias)
                self.projection.to(self.device)
            else:
                self.projection = None
        else:
            self.projection = None
        
        # Cache for encodings
        self.cache_enabled = self.config.cache_size > 0
        self.cache = OrderedDict()
        self.cache_size = self.config.cache_size
        
        logger.info(f"Initialized ImageEncoder with model {self.config.model_name}")
    
    def _compute_hash(self, input_data):
        """
        Compute a hash for cache lookup.
        
        Args:
            input_data: Input data to be hashed
            
        Returns:
            str: Hash string
        """
        # For PIL images
        if isinstance(input_data, Image.Image):
            img_bytes = io.BytesIO()
            input_data.save(img_bytes, format='PNG')
            return hashlib.sha256(img_bytes.getvalue()).hexdigest()
            
        # For file paths
        elif isinstance(input_data, str) and os.path.isfile(input_data):
            try:
                with open(input_data, 'rb') as f:
                    return hashlib.sha256(f.read()).hexdigest()
            except Exception as e:
                logger.warning(f"Failed to hash file {input_data}: {e}")
                return None
                
        # For tensors
        elif isinstance(input_data, torch.Tensor):
            return hashlib.sha256(input_data.cpu().numpy().tobytes()).hexdigest()
            
        # For other types
        else:
            try:
                data_bytes = pickle.dumps(input_data)
                return hashlib.sha256(data_bytes).hexdigest()
            except Exception as e:
                logger.warning(f"Failed to hash input for caching: {e}")
                return None
    
    def _manage_cache_size(self):
        """Ensure cache doesn't exceed maximum size."""
        if len(self.cache) > self.cache_size:
            # Remove oldest items (OrderedDict preserves insertion order)
            for _ in range(len(self.cache) - self.cache_size):
                self.cache.popitem(last=False)
    
    def encode(self, images, return_all_features=False, apply_augmentation=False):
        """
        Encode images into embeddings.
        
        Args:
            images: PIL Image, file path, tensor, or batch of images
            return_all_features: Whether to return all feature levels
            apply_augmentation: Whether to apply augmentation
            
        Returns:
            torch.Tensor or Dict: Encoded embeddings or all features
        """
        # Check cache for PIL images or file paths
        if self.cache_enabled and (isinstance(images, Image.Image) or 
                                    (isinstance(images, str) and os.path.isfile(images))):
            cache_key = self._compute_hash(images)
            if cache_key in self.cache:
                return self.cache[cache_key]
        else:
            cache_key = None
        
        # Process the input
        if isinstance(images, Image.Image):
            # Single PIL image
            processed = self.processor.process_image(images, apply_augmentation)
            processed = processed.unsqueeze(0).to(self.device)
        elif isinstance(images, str) and os.path.isfile(images):
            # Single image path
            processed = self.processor.process_image(images, apply_augmentation)
            processed = processed.unsqueeze(0).to(self.device)
        elif isinstance(images, torch.Tensor):
            # Handle tensor input
            if images.dim() == 3:
                # Single image [C, H, W]
                processed = images.unsqueeze(0).to(self.device)
            elif images.dim() == 4:
                # Batch of images [B, C, H, W]
                processed = images.to(self.device)
            else:
                raise ValueError(f"Expected 3D or 4D tensor, got shape {images.shape}")
        elif isinstance(images, list):
            # List of images or paths
            processed = self.processor.process_batch(images, apply_augmentation)
            processed = processed.to(self.device)
        elif isinstance(images, np.ndarray):
            # NumPy array
            if images.ndim == 3:
                # Single image [H, W, C]
                images = torch.from_numpy(images).permute(2, 0, 1)
                processed = images.unsqueeze(0).to(self.device)
            elif images.ndim == 4:
                # Batch of images [B, H, W, C]
                images = torch.from_numpy(images).permute(0, 3, 1, 2)
                processed = images.to(self.device)
            else:
                raise ValueError(f"Expected 3D or 4D numpy array, got shape {images.shape}")
        else:
            raise TypeError(f"Unsupported input type: {type(images)}")
        
        # Extract features
        features = self.backend.extract_features(processed)
        
        # Apply pooling to get global features
        if self.config.pooling_strategy != 'none':
            pooled = self.backend.apply_pooling(features)
        else:
            # For 'none' pooling, return patch features
            if 'patches' in features:
                pooled = features['patches']
            elif features['default'].dim() == 3:
                pooled = features['default']
            else:
                # Fall back to default pooling
                pooled = self.backend.apply_pooling(features)
        
        # Apply projection if configured
        if self.projection is not None:
            pooled = self.projection(pooled)
        
        # Apply normalization if configured
        if self.config.normalize_embeddings:
            if pooled.dim() == 2:
                pooled = F.normalize(pooled, p=2, dim=1)
            elif pooled.dim() == 3:
                pooled = F.normalize(pooled, p=2, dim=2)
        
        # Return result based on configuration
        if return_all_features:
            # Include pooled embeddings in features dictionary
            features['embeddings'] = pooled
            result = features
        else:
            result = pooled
        
        # Cache result if enabled
        if self.cache_enabled and cache_key:
            self.cache[cache_key] = result
            self._manage_cache_size()
        
        return result
    
    def encode_patches(self, image, patch_size=None):
        """
        Encode image as patch embeddings.
        
        Args:
            image: Image to encode (PIL, path, or tensor)
            patch_size: Optional override for patch size
            
        Returns:
            torch.Tensor: Patch embeddings [num_patches, embedding_dim]
        """
        patch_size = patch_size or self.config.patch_size
        
        # Process image
        if isinstance(image, torch.Tensor):
            if image.dim() == 3:
                # [C, H, W]
                processed = image.unsqueeze(0).to(self.device)
            elif image.dim() == 4:
                # [B, C, H, W]
                processed = image.to(self.device)
            else:
                raise ValueError(f"Expected 3D or 4D tensor, got shape {image.shape}")
        else:
            processed = self.processor.process_image(image)
            processed = processed.unsqueeze(0).to(self.device)
        
        # For vision transformers, use their native patch embeddings
        if any(name in self.config.model_name.lower() for name in ['vit', 'deit', 'beit', 'clip']):
            # Extract features
            features = self.backend.extract_features(processed)
            
            # Get patch embeddings
            if 'patches' in features:
                patch_embeddings = features['patches']
            else:
                # Try to extract from default features
                patch_embeddings = self.backend.get_patch_embeddings(features['default'])
                
            if patch_embeddings is None:
                logger.warning("Failed to extract native patch embeddings, falling back to manual extraction")
            else:
                # Apply projection if needed
                if self.projection is not None:
                    # Reshape if needed
                    original_shape = patch_embeddings.shape
                    patch_embeddings = patch_embeddings.reshape(-1, original_shape[-1])
                    patch_embeddings = self.projection(patch_embeddings)
                    patch_embeddings = patch_embeddings.reshape(original_shape[0], -1, self.config.embedding_dim)
                
                # Apply normalization if configured
                if self.config.normalize_embeddings:
                    patch_embeddings = F.normalize(patch_embeddings, p=2, dim=-1)
                
                return patch_embeddings.squeeze(0)  # Remove batch dimension
        
        # For CNNs or as fallback, manually extract patches
        patches = self.processor._extract_patches(processed.squeeze(0), patch_size)
        
        # Encode each patch
        patch_embeddings = []
        for i in range(patches.size(0)):
            patch = patches[i].unsqueeze(0)  # Add batch dimension
            embedding = self.encode(patch)
            patch_embeddings.append(embedding)
        
        # Stack patch embeddings
        return torch.cat(patch_embeddings)
    
    def encode_regions(self, image, num_regions=None):
        """
        Encode image as region embeddings.
        
        Args:
            image: Image to encode (PIL, path, or tensor)
            num_regions: Optional override for number of regions
            
        Returns:
            torch.Tensor: Region embeddings [num_regions, embedding_dim]
        """
        num_regions = num_regions or self.config.num_regions
        
        # Process image
        if isinstance(image, torch.Tensor):
            if image.dim() == 3:
                # [C, H, W]
                processed = image.to(self.device)
            elif image.dim() == 4 and image.size(0) == 1:
                # [1, C, H, W]
                processed = image.squeeze(0).to(self.device)
            else:
                raise ValueError(f"Expected 3D tensor or 4D tensor with batch=1, got shape {image.shape}")
        else:
            processed = self.processor.process_image(image)
            processed = processed.to(self.device)
        
        # Extract region features
        regions = self.processor._extract_regions(processed, num_regions)
        
        # Encode each region
        region_embeddings = []
        for i in range(regions.size(0)):
            region = regions[i].unsqueeze(0)  # Add batch dimension
            embedding = self.encode(region.unsqueeze(0))  # Add channel and batch dimensions
            region_embeddings.append(embedding)
        
        # Stack region embeddings
        return torch.cat(region_embeddings)
    
    def encode_multi_scale(self, image):
        """
        Encode image at multiple scales.
        
        Args:
            image: Image to encode (PIL, path, or tensor)
            
        Returns:
            Dict[str, torch.Tensor]: Embeddings at different scales
        """
        # Process image
        if isinstance(image, torch.Tensor):
            if image.dim() == 3:
                # [C, H, W]
                processed = image.unsqueeze(0).to(self.device)
            elif image.dim() == 4:
                # [B, C, H, W]
                processed = image.to(self.device)
            else:
                raise ValueError(f"Expected 3D or 4D tensor, got shape {image.shape}")
        else:
            processed = self.processor.process_image(image)
            processed = processed.unsqueeze(0).to(self.device)
        
        # Extract features at multiple scales
        features = self.backend.extract_features(processed)
        
        # Initialize result dictionary
        result = {}
        
        # Global features
        if 'default' in features:
            global_features = self.backend.apply_pooling(features['default'])
            
            # Apply projection if needed
            if self.projection is not None:
                global_features = self.projection(global_features)
                
            # Apply normalization
            if self.config.normalize_embeddings:
                global_features = F.normalize(global_features, p=2, dim=1)
                
            result['global'] = global_features.squeeze(0)  # Remove batch dimension
        
        # Patch-level features
        if 'patches' in features:
            patch_features = features['patches']
            
            # Apply projection if needed
            if self.projection is not None:
                patch_features = self.projection(patch_features.reshape(-1, patch_features.size(-1)))
                patch_features = patch_features.reshape(1, -1, self.config.embedding_dim)
                
            # Apply normalization
            if self.config.normalize_embeddings:
                patch_features = F.normalize(patch_features, p=2, dim=2)
                
            result['patches'] = patch_features.squeeze(0)  # Remove batch dimension
        
        # Multi-level features from intermediate layers
        if 'intermediate' in features:
            # Extract features at different levels
            for level in ['low_level', 'mid_level', 'high_level']:
                if level in features:
                    level_features = self.backend.apply_pooling(features[level])
                    
                    # Apply projection if needed
                    if self.projection is not None:
                        level_features = self.projection(level_features)
                        
                    # Apply normalization
                    if self.config.normalize_embeddings:
                        level_features = F.normalize(level_features, p=2, dim=1)
                        
                    result[level] = level_features.squeeze(0)  # Remove batch dimension
        
        return result
    
    def forward(self, images, **kwargs):
        """
        Forward pass to encode images.
        
        Args:
            images: Image input (PIL, path, tensor, or batch)
            **kwargs: Additional arguments for encoding
            
        Returns:
            torch.Tensor: Encoded embeddings
        """
        return self.encode(images, **kwargs)
    
    def clear_cache(self):
        """Clear the encoding cache."""
        self.cache.clear()
        
    def to(self, device):
        """
        Move encoder to specified device.
        
        Args:
            device: Target device
            
        Returns:
            ImageEncoder: Self
        """
        self.device = device
        self.backend.device = device
        self.backend.model.to(device)
        
        if self.projection is not None:
            self.projection.to(device)
            
        # Update config
        self.config.device = device
        
        return self
    
    def save(self, directory: str, save_model: bool = True):
        """
        Save encoder and configuration.
        
        Args:
            directory: Directory to save in
            save_model: Whether to save the underlying model
        """
        os.makedirs(directory, exist_ok=True)
        
        # Save configuration
        config_path = os.path.join(directory, "config.json")
        self.config.save(config_path)
        
        # Save encoder state (projection layer weights)
        if self.projection is not None:
            state_path = os.path.join(directory, "encoder_state.pt")
            torch.save(self.state_dict(), state_path)
        
        # Save model if requested
        if save_model and self.backend.model is not None:
            try:
                if hasattr(self.backend.model, "save_pretrained"):
                    # For Hugging Face models
                    model_dir = os.path.join(directory, "model")
                    os.makedirs(model_dir, exist_ok=True)
                    self.backend.model.save_pretrained(model_dir)
                else:
                    # For PyTorch models
                    model_path = os.path.join(directory, "model.pt")
                    torch.save(self.backend.model.state_dict(), model_path)
            except Exception as e:
                logger.warning(f"Could not save model: {e}")
        
        logger.info(f"ImageEncoder saved to {directory}")
    
    @classmethod
    def load(cls, directory: str, device=None, load_model: bool = True):
        """
        Load encoder from saved files.
        
        Args:
            directory: Directory containing saved encoder
            device: Device to load onto
            load_model: Whether to load the underlying model
            
        Returns:
            ImageEncoder: Loaded encoder
        """
        # Load configuration
        config_path = os.path.join(directory, "config.json")
        config = ImageEncoderConfig.load(config_path)
        
        # Override device if specified
        if device is not None:
            config.device = device
        
        # Create instance
        instance = cls(config)
        
        # Load encoder state (projection layer weights)
        state_path = os.path.join(directory, "encoder_state.pt")
        if os.path.exists(state_path):
            instance.load_state_dict(torch.load(state_path, map_location=config.device))
        
        # Load model if requested
        if load_model:
            model_dir = os.path.join(directory, "model")
            if os.path.exists(model_dir) and hasattr(instance.backend.model, "from_pretrained"):
                # For Hugging Face models
                model_class = instance.backend.model.__class__
                instance.backend.model = model_class.from_pretrained(model_dir)
                instance.backend.model.to(config.device)
            else:
                model_path = os.path.join(directory, "model.pt")
                if os.path.exists(model_path):
                    instance.backend.model.load_state_dict(torch.load(model_path, map_location=config.device))
        
        logger.info(f"ImageEncoder loaded from {directory}")
        return instance


# Sentinel to mark end of module
if __name__ == "__main__":
    # Example usage
    config = ImageEncoderConfig(model_name="vit_base_patch16_224")
    encoder = ImageEncoder(config)
    
    # Create dummy image tensor [3, 224, 224]
    image = torch.randn(3, 224, 224)
    embeddings = encoder.encode(image)
    
    print(f"Encoded image shape: {embeddings.shape}")