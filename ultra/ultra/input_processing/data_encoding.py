#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Data Encoding Module for ULTRA

This module handles the encoding of structured data inputs (CSV, JSON, tabular data)
for the ULTRA (Ultimate Learning & Thought Reasoning Architecture) system. It transforms
heterogeneous data features into unified neural representations suitable for processing
by the core neural architecture and downstream components.

Key capabilities:
- Automated schema inference and validation
- Sophisticated handling of different feature types (numerical, categorical, temporal)
- Advanced preprocessing with configurable pipelines per feature type
- Feature importance estimation and selection mechanisms
- Support for diverse encoding strategies (normalization, embedding, hashing)
- Memory-efficient processing of large datasets through streaming
- Incremental training for evolving datasets
- Hierarchical representation learning for complex tabular data

The implementation integrates with other ULTRA components, particularly the
Diffusion-Based Reasoning and Neuro-Symbolic Integration subsystems.
"""

import os
import sys
import re
import logging
import json
import pickle
import hashlib
import math
import copy
import warnings
from typing import Dict, List, Tuple, Optional, Union, Any, Set, Callable, Iterator
from collections import OrderedDict, defaultdict, Counter
from datetime import datetime, date, time, timedelta
from enum import Enum, auto
import traceback

import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Import dependencies if available
try:
    import pandas as pd
    import numpy as np
    _HAS_PANDAS = True
except ImportError:
    logger.warning("Pandas library not found. Limited tabular data functionality.")
    _HAS_PANDAS = False

try:
    from sklearn.preprocessing import (
        StandardScaler, MinMaxScaler, RobustScaler, OneHotEncoder,
        LabelEncoder, KBinsDiscretizer, PowerTransformer, QuantileTransformer
    )
    from sklearn.feature_selection import mutual_info_regression, mutual_info_classif
    from sklearn.impute import SimpleImputer
    _HAS_SKLEARN = True
except ImportError:
    logger.warning("Scikit-learn library not found. Limited preprocessing functionality.")
    _HAS_SKLEARN = False

try:
    import category_encoders as ce
    _HAS_CATEGORY_ENCODERS = True
except ImportError:
    logger.warning("Category-encoders library not found. Using simplified categorical encoding.")
    _HAS_CATEGORY_ENCODERS = False

try:
    from tqdm import tqdm
    _HAS_TQDM = True
except ImportError:
    # Simple tqdm replacement if not available
    def tqdm(iterable, **kwargs):
        return iterable
    _HAS_TQDM = False


class FeatureType(Enum):
    """Enumeration of possible feature types in structured data."""
    NUMERICAL_CONTINUOUS = auto()
    NUMERICAL_DISCRETE = auto()
    CATEGORICAL_NOMINAL = auto()
    CATEGORICAL_ORDINAL = auto()
    DATETIME = auto()
    BOOLEAN = auto()
    TEXT = auto()
    UNKNOWN = auto()


class NumericalNormalization(Enum):
    """Enumeration of numerical normalization methods."""
    STANDARD = auto()  # Z-score normalization
    MINMAX = auto()    # Min-max scaling to [0, 1]
    ROBUST = auto()    # Robust scaling using quantiles
    QUANTILE = auto()  # Quantile transformation to normal distribution
    POWER = auto()     # Power transform to normal distribution
    LOG = auto()       # Log transform
    NONE = auto()      # No normalization


class CategoricalEncoding(Enum):
    """Enumeration of categorical encoding methods."""
    ONEHOT = auto()      # One-hot encoding (dummy variables)
    BINARY = auto()      # Binary encoding
    ORDINAL = auto()     # Ordinal encoding
    TARGET = auto()      # Target encoding
    FREQUENCY = auto()   # Frequency encoding
    EMBEDDING = auto()   # Embedding encoding
    HASHING = auto()     # Feature hashing
    WEIGHT_OF_EVIDENCE = auto()  # Weight of evidence encoding
    NONE = auto()        # No encoding (keep as-is)


class DateEncoding(Enum):
    """Enumeration of date/time encoding methods."""
    CYCLICAL = auto()   # Cyclical encoding (sin/cos)
    ORDINAL = auto()    # Ordinal encoding (total seconds/days)
    COMPONENTS = auto() # Component-wise encoding (year, month, day, etc.)
    DIFFERENCE = auto() # Difference from reference date
    NONE = auto()       # No encoding (keep as-is)


class MissingValueStrategy(Enum):
    """Enumeration of missing value handling strategies."""
    IMPUTE_MEAN = auto()      # Impute with mean
    IMPUTE_MEDIAN = auto()    # Impute with median
    IMPUTE_MODE = auto()      # Impute with mode
    IMPUTE_CONSTANT = auto()  # Impute with a constant value
    INDICATOR = auto()        # Add indicator variable
    DROP = auto()             # Drop rows with missing values
    NONE = auto()             # Keep missing values


class SchemaChangePolicy(Enum):
    """Enumeration of policies for handling schema changes during inference."""
    IGNORE_NEW = auto()      # Ignore new columns
    IMPUTE_NEW = auto()      # Impute new columns with default values
    ERROR = auto()           # Raise error on schema mismatch
    DYNAMIC_ADAPT = auto()   # Dynamically adapt to schema changes
    RETRAIN = auto()         # Trigger retraining


class DataEncoderConfig:
    """
    Configuration for data encoding.
    
    This class defines parameters for schema inference, feature type detection,
    encoding strategies, and other data processing options.
    """
    
    def __init__(
        self,
        embedding_dim: int = 1024,
        categorical_embedding_dim: int = 32,
        numerical_normalization: Union[str, NumericalNormalization] = "standard",
        categorical_encoding: Union[str, CategoricalEncoding] = "embedding",
        date_encoding: Union[str, DateEncoding] = "cyclical",
        handle_missing: Union[str, MissingValueStrategy] = "impute_mean",
        schema_inference: bool = True,
        feature_selection: Optional[str] = None,
        max_categories: int = 100,
        min_frequency: int = 5,
        feature_hashing: bool = False,
        n_hash_features: int = 64,
        use_feature_names: bool = True,
        detect_outliers: bool = True,
        normalize_embeddings: bool = True,
        schema_change_policy: Union[str, SchemaChangePolicy] = "error",
        max_unique_ratio: float = 0.5,
        cache_size: int = 1000,
        device: Optional[Union[str, torch.device]] = None,
        precision: str = "float32",
        text_features_handling: str = "simple",
        constant_value: Any = 0,
        target_column: Optional[str] = None,
        feature_definitions: Optional[Dict[str, Dict[str, Any]]] = None,
        ordinal_categories: Optional[Dict[str, List[Any]]] = None,
        feature_groups: Optional[Dict[str, List[str]]] = None,
        feature_weights: Optional[Dict[str, float]] = None,
        n_jobs: int = -1,
        random_state: int = 42,
        preprocessing_steps: Optional[Dict[str, List[str]]] = None,
        drop_features: Optional[List[str]] = None,
        imputation_values: Optional[Dict[str, Any]] = None,
        cyclical_features: Optional[List[str]] = None,
        log_transform_features: Optional[List[str]] = None
    ):
        """
        Initialize data encoder configuration.
        
        Args:
            embedding_dim: Final embedding dimension
            categorical_embedding_dim: Embedding dimension for categorical features
            numerical_normalization: Strategy for normalizing numerical features
            categorical_encoding: Strategy for encoding categorical features
            date_encoding: Strategy for encoding date/time features
            handle_missing: Strategy for handling missing values
            schema_inference: Whether to infer schema from data
            feature_selection: Feature selection strategy (None, 'mutual_info', 'variance', etc.)
            max_categories: Maximum number of categories per feature
            min_frequency: Minimum frequency for a category to be included
            feature_hashing: Whether to use feature hashing for high cardinality
            n_hash_features: Number of hash features for feature hashing
            use_feature_names: Whether to use feature names in embedding
            detect_outliers: Whether to detect and handle outliers
            normalize_embeddings: Whether to L2-normalize embeddings
            schema_change_policy: Policy for handling schema changes during inference
            max_unique_ratio: Maximum ratio of unique values to total values for categorical features
            cache_size: Size of the in-memory cache
            device: Device to use for encoding
            precision: Numerical precision ('float32', 'float16', or 'bfloat16')
            text_features_handling: How to handle text features ('simple', 'nlp', 'ignore')
            constant_value: Constant value for imputation
            target_column: Target column for target encoding
            feature_definitions: Custom feature definitions
            ordinal_categories: Ordered categories for ordinal features
            feature_groups: Groups of related features
            feature_weights: Weights for each feature in the final embedding
            n_jobs: Number of jobs for parallel processing
            random_state: Random state for reproducibility
            preprocessing_steps: Custom preprocessing steps for each feature type
            drop_features: Features to drop during processing
            imputation_values: Custom imputation values for specific features
            cyclical_features: Features to encode cyclically
            log_transform_features: Features to apply log transform to
        """
        self.embedding_dim = embedding_dim
        self.categorical_embedding_dim = categorical_embedding_dim
        
        # Convert string enums to enum types
        self.numerical_normalization = self._parse_enum(numerical_normalization, NumericalNormalization)
        self.categorical_encoding = self._parse_enum(categorical_encoding, CategoricalEncoding)
        self.date_encoding = self._parse_enum(date_encoding, DateEncoding)
        self.handle_missing = self._parse_enum(handle_missing, MissingValueStrategy)
        self.schema_change_policy = self._parse_enum(schema_change_policy, SchemaChangePolicy)
        
        self.schema_inference = schema_inference
        self.feature_selection = feature_selection
        self.max_categories = max_categories
        self.min_frequency = min_frequency
        self.feature_hashing = feature_hashing
        self.n_hash_features = n_hash_features
        self.use_feature_names = use_feature_names
        self.detect_outliers = detect_outliers
        self.normalize_embeddings = normalize_embeddings
        self.max_unique_ratio = max_unique_ratio
        self.cache_size = cache_size
        
        self.device = device if device is not None else (
            torch.device('cuda') if torch.cuda.is_available() else torch.device('cpu')
        )
        
        self.precision = precision
        self.text_features_handling = text_features_handling
        self.constant_value = constant_value
        self.target_column = target_column
        self.feature_definitions = feature_definitions or {}
        self.ordinal_categories = ordinal_categories or {}
        self.feature_groups = feature_groups or {}
        self.feature_weights = feature_weights or {}
        self.n_jobs = n_jobs
        self.random_state = random_state
        self.preprocessing_steps = preprocessing_steps or {}
        self.drop_features = drop_features or []
        self.imputation_values = imputation_values or {}
        self.cyclical_features = cyclical_features or []
        self.log_transform_features = log_transform_features or []
        
        # Validate configuration
        self._validate()
        
        logger.info(f"Initialized DataEncoderConfig with embedding_dim={embedding_dim}")
    
    def _parse_enum(self, value, enum_class):
        """Parse string or enum value to enum type."""
        if isinstance(value, str):
            try:
                return enum_class[value.upper()]
            except KeyError:
                valid_values = [e.name.lower() for e in enum_class]
                raise ValueError(f"Invalid {enum_class.__name__} value: {value}. "
                               f"Valid values are: {valid_values}")
        elif isinstance(value, enum_class):
            return value
        else:
            raise TypeError(f"Expected string or {enum_class.__name__}, got {type(value)}")
    
    def _validate(self):
        """Validate configuration parameters."""
        if self.embedding_dim <= 0:
            raise ValueError(f"embedding_dim must be positive, got {self.embedding_dim}")
            
        if self.categorical_embedding_dim <= 0:
            raise ValueError(f"categorical_embedding_dim must be positive, got {self.categorical_embedding_dim}")
            
        if self.max_categories <= 0:
            raise ValueError(f"max_categories must be positive, got {self.max_categories}")
            
        if self.min_frequency <= 0:
            raise ValueError(f"min_frequency must be positive, got {self.min_frequency}")
            
        if self.feature_hashing and self.n_hash_features <= 0:
            raise ValueError(f"n_hash_features must be positive when feature_hashing is enabled, "
                           f"got {self.n_hash_features}")
            
        if not (0 < self.max_unique_ratio <= 1.0):
            raise ValueError(f"max_unique_ratio must be in (0, 1], got {self.max_unique_ratio}")
            
        valid_precision = ['float32', 'float16', 'bfloat16']
        if self.precision not in valid_precision:
            raise ValueError(f"precision must be one of {valid_precision}, got {self.precision}")
            
        valid_text_handling = ['simple', 'nlp', 'ignore']
        if self.text_features_handling not in valid_text_handling:
            raise ValueError(f"text_features_handling must be one of {valid_text_handling}, "
                           f"got {self.text_features_handling}")
            
        if self.feature_selection not in [None, 'mutual_info', 'variance', 'model_based', 'correlation']:
            raise ValueError(f"feature_selection must be None or one of ['mutual_info', 'variance', "
                           f"'model_based', 'correlation'], got {self.feature_selection}")
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert configuration to dictionary.
        
        Returns:
            Dict[str, Any]: Configuration as dictionary
        """
        config_dict = {}
        for key, value in self.__dict__.items():
            if key.startswith('_'):
                continue
                
            # Convert enums to strings
            if isinstance(value, Enum):
                value = value.name.lower()
                
            # Convert device to string
            if key == 'device' and isinstance(value, torch.device):
                value = str(value)
                
            config_dict[key] = value
            
        return config_dict
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'DataEncoderConfig':
        """
        Create configuration from dictionary.
        
        Args:
            config_dict: Dictionary containing configuration parameters
            
        Returns:
            DataEncoderConfig: Configuration object
        """
        return cls(**config_dict)
    
    def save(self, file_path: str) -> None:
        """
        Save configuration to JSON file.
        
        Args:
            file_path: Path to save the configuration file
        """
        os.makedirs(os.path.dirname(os.path.abspath(file_path)), exist_ok=True)
        with open(file_path, 'w') as f:
            json.dump(self.to_dict(), f, indent=2)
    
    @classmethod
    def load(cls, file_path: str) -> 'DataEncoderConfig':
        """
        Load configuration from JSON file.
        
        Args:
            file_path: Path to the configuration file
            
        Returns:
            DataEncoderConfig: Configuration object
        """
        with open(file_path, 'r') as f:
            config_dict = json.load(f)
        return cls.from_dict(config_dict)


class FeatureSchema:
    """
    Schema for a single feature in structured data.
    
    This class represents the schema (data type, encoding strategy, etc.)
    for a single feature in a structured dataset.
    """
    
    def __init__(
        self,
        name: str,
        feature_type: FeatureType,
        dtype: Optional[Any] = None,
        encoding: Optional[Union[NumericalNormalization, CategoricalEncoding, DateEncoding]] = None,
        categories: Optional[List[Any]] = None,
        is_ordinal: bool = False,
        missing_strategy: MissingValueStrategy = MissingValueStrategy.IMPUTE_MEAN,
        statistics: Optional[Dict[str, Any]] = None,
        weight: float = 1.0,
        is_target: bool = False,
        preprocessing_steps: Optional[List[str]] = None,
        imputation_value: Optional[Any] = None,
        min_value: Optional[float] = None,
        max_value: Optional[float] = None,
        mean_value: Optional[float] = None,
        std_value: Optional[float] = None,
        description: Optional[str] = None,
        feature_hashing: bool = False,
        embedding_dim: Optional[int] = None,
        cyclical: bool = False,
        log_transform: bool = False,
        special_values: Optional[Dict[Any, str]] = None
    ):
        """
        Initialize feature schema.
        
        Args:
            name: Feature name
            feature_type: Type of feature
            dtype: Data type
            encoding: Encoding strategy
            categories: List of categories for categorical features
            is_ordinal: Whether categorical feature is ordinal
            missing_strategy: Strategy for handling missing values
            statistics: Statistical properties of the feature
            weight: Feature weight in the final embedding
            is_target: Whether this is a target feature
            preprocessing_steps: List of preprocessing steps
            imputation_value: Value to use for imputation
            min_value: Minimum value (for numerical features)
            max_value: Maximum value (for numerical features)
            mean_value: Mean value (for numerical features)
            std_value: Standard deviation (for numerical features)
            description: Feature description
            feature_hashing: Whether to use feature hashing
            embedding_dim: Embedding dimension for this feature
            cyclical: Whether to encode cyclically
            log_transform: Whether to apply log transform
            special_values: Dictionary mapping special values to their meanings
        """
        self.name = name
        self.feature_type = feature_type
        self.dtype = dtype
        self.encoding = encoding
        self.categories = categories
        self.is_ordinal = is_ordinal
        self.missing_strategy = missing_strategy
        self.statistics = statistics or {}
        self.weight = weight
        self.is_target = is_target
        self.preprocessing_steps = preprocessing_steps or []
        self.imputation_value = imputation_value
        self.min_value = min_value
        self.max_value = max_value
        self.mean_value = mean_value
        self.std_value = std_value
        self.description = description
        self.feature_hashing = feature_hashing
        self.embedding_dim = embedding_dim
        self.cyclical = cyclical
        self.log_transform = log_transform
        self.special_values = special_values or {}
        
        # Additional attributes for encoders
        self.encoder = None
        self.index = None  # Position in the original dataset
        
        logger.debug(f"Created schema for feature '{name}' of type {feature_type.name}")
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert schema to dictionary.
        
        Returns:
            Dict[str, Any]: Schema as dictionary
        """
        schema_dict = {}
        for key, value in self.__dict__.items():
            if key in ['encoder', 'index']:
                continue  # Skip non-serializable objects
                
            # Convert enums to strings
            if isinstance(value, Enum):
                value = value.name.lower()
                
            schema_dict[key] = value
            
        return schema_dict
    
    @classmethod
    def from_dict(cls, schema_dict: Dict[str, Any]) -> 'FeatureSchema':
        """
        Create schema from dictionary.
        
        Args:
            schema_dict: Dictionary containing schema parameters
            
        Returns:
            FeatureSchema: Schema object
        """
        # Handle enums
        if 'feature_type' in schema_dict and isinstance(schema_dict['feature_type'], str):
            schema_dict['feature_type'] = FeatureType[schema_dict['feature_type'].upper()]
            
        if 'encoding' in schema_dict and schema_dict['encoding'] is not None:
            if isinstance(schema_dict['encoding'], str):
                # Determine encoding type based on feature type
                feature_type = schema_dict.get('feature_type')
                if feature_type in [FeatureType.NUMERICAL_CONTINUOUS, FeatureType.NUMERICAL_DISCRETE]:
                    schema_dict['encoding'] = NumericalNormalization[schema_dict['encoding'].upper()]
                elif feature_type in [FeatureType.CATEGORICAL_NOMINAL, FeatureType.CATEGORICAL_ORDINAL]:
                    schema_dict['encoding'] = CategoricalEncoding[schema_dict['encoding'].upper()]
                elif feature_type == FeatureType.DATETIME:
                    schema_dict['encoding'] = DateEncoding[schema_dict['encoding'].upper()]
                    
        if 'missing_strategy' in schema_dict and isinstance(schema_dict['missing_strategy'], str):
            schema_dict['missing_strategy'] = MissingValueStrategy[schema_dict['missing_strategy'].upper()]
            
        return cls(**schema_dict)


class DataSchema:
    """
    Schema for structured data.
    
    This class represents the schema for a structured dataset,
    including feature definitions, relationships, and encoding strategies.
    """
    
    def __init__(
        self,
        features: Optional[List[FeatureSchema]] = None,
        index_column: Optional[str] = None,
        target_column: Optional[str] = None,
        timestamp_column: Optional[str] = None,
        categorical_columns: Optional[List[str]] = None,
        numerical_columns: Optional[List[str]] = None,
        datetime_columns: Optional[List[str]] = None,
        text_columns: Optional[List[str]] = None,
        boolean_columns: Optional[List[str]] = None,
        feature_groups: Optional[Dict[str, List[str]]] = None,
        feature_relationships: Optional[Dict[str, List[Tuple[str, str]]]] = None,
        dataset_statistics: Optional[Dict[str, Any]] = None,
        metadata: Optional[Dict[str, Any]] = None
    ):
        """
        Initialize data schema.
        
        Args:
            features: List of feature schemas
            index_column: Name of the index column
            target_column: Name of the target column
            timestamp_column: Name of the timestamp column
            categorical_columns: List of categorical column names
            numerical_columns: List of numerical column names
            datetime_columns: List of datetime column names
            text_columns: List of text column names
            boolean_columns: List of boolean column names
            feature_groups: Groups of related features
            feature_relationships: Relationships between features
            dataset_statistics: Statistical properties of the dataset
            metadata: Additional metadata
        """
        self.features = features or []
        self.index_column = index_column
        self.target_column = target_column
        self.timestamp_column = timestamp_column
        self.categorical_columns = categorical_columns or []
        self.numerical_columns = numerical_columns or []
        self.datetime_columns = datetime_columns or []
        self.text_columns = text_columns or []
        self.boolean_columns = boolean_columns or []
        self.feature_groups = feature_groups or {}
        self.feature_relationships = feature_relationships or {}
        self.dataset_statistics = dataset_statistics or {}
        self.metadata = metadata or {}
        
        # Create maps for efficient lookup
        self.feature_map = {f.name: f for f in self.features}
        
        logger.info(f"Created schema with {len(self.features)} features")
    
    def get_feature(self, name: str) -> Optional[FeatureSchema]:
        """
        Get feature schema by name.
        
        Args:
            name: Feature name
            
        Returns:
            Optional[FeatureSchema]: Feature schema or None if not found
        """
        return self.feature_map.get(name)
    
    def add_feature(self, feature: FeatureSchema) -> None:
        """
        Add a feature to the schema.
        
        Args:
            feature: Feature schema to add
        """
        self.features.append(feature)
        self.feature_map[feature.name] = feature
        
        # Update feature type lists
        if feature.feature_type in [FeatureType.CATEGORICAL_NOMINAL, FeatureType.CATEGORICAL_ORDINAL]:
            if feature.name not in self.categorical_columns:
                self.categorical_columns.append(feature.name)
        elif feature.feature_type in [FeatureType.NUMERICAL_CONTINUOUS, FeatureType.NUMERICAL_DISCRETE]:
            if feature.name not in self.numerical_columns:
                self.numerical_columns.append(feature.name)
        elif feature.feature_type == FeatureType.DATETIME:
            if feature.name not in self.datetime_columns:
                self.datetime_columns.append(feature.name)
        elif feature.feature_type == FeatureType.TEXT:
            if feature.name not in self.text_columns:
                self.text_columns.append(feature.name)
        elif feature.feature_type == FeatureType.BOOLEAN:
            if feature.name not in self.boolean_columns:
                self.boolean_columns.append(feature.name)
    
    def remove_feature(self, name: str) -> None:
        """
        Remove a feature from the schema.
        
        Args:
            name: Name of the feature to remove
        """
        feature = self.feature_map.get(name)
        if feature:
            self.features.remove(feature)
            del self.feature_map[name]
            
            # Update feature type lists
            if name in self.categorical_columns:
                self.categorical_columns.remove(name)
            elif name in self.numerical_columns:
                self.numerical_columns.remove(name)
            elif name in self.datetime_columns:
                self.datetime_columns.remove(name)
            elif name in self.text_columns:
                self.text_columns.remove(name)
            elif name in self.boolean_columns:
                self.boolean_columns.remove(name)
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert schema to dictionary.
        
        Returns:
            Dict[str, Any]: Schema as dictionary
        """
        schema_dict = {
            'index_column': self.index_column,
            'target_column': self.target_column,
            'timestamp_column': self.timestamp_column,
            'categorical_columns': self.categorical_columns,
            'numerical_columns': self.numerical_columns,
            'datetime_columns': self.datetime_columns,
            'text_columns': self.text_columns,
            'boolean_columns': self.boolean_columns,
            'feature_groups': self.feature_groups,
            'feature_relationships': self.feature_relationships,
            'dataset_statistics': self.dataset_statistics,
            'metadata': self.metadata,
            'features': [f.to_dict() for f in self.features]
        }
        return schema_dict
    
    @classmethod
    def from_dict(cls, schema_dict: Dict[str, Any]) -> 'DataSchema':
        """
        Create schema from dictionary.
        
        Args:
            schema_dict: Dictionary containing schema parameters
            
        Returns:
            DataSchema: Schema object
        """
        # Create a copy to avoid modifying the input
        schema_copy = schema_dict.copy()
        
        # Extract and convert features
        features_dicts = schema_copy.pop('features', [])
        features = [FeatureSchema.from_dict(f) for f in features_dicts]
        
        # Create schema
        schema = cls(features=features, **schema_copy)
        
        return schema
    
    def save(self, file_path: str) -> None:
        """
        Save schema to JSON file.
        
        Args:
            file_path: Path to save the schema file
        """
        os.makedirs(os.path.dirname(os.path.abspath(file_path)), exist_ok=True)
        with open(file_path, 'w') as f:
            json.dump(self.to_dict(), f, indent=2)
    
    @classmethod
    def load(cls, file_path: str) -> 'DataSchema':
        """
        Load schema from JSON file.
        
        Args:
            file_path: Path to the schema file
            
        Returns:
            DataSchema: Schema object
        """
        with open(file_path, 'r') as f:
            schema_dict = json.load(f)
        return cls.from_dict(schema_dict)


class SchemaInferrer:
    """
    Infers data schema from input data.
    
    This class analyzes input data to automatically determine
    its structure, feature types, and statistical properties.
    """
    
    def __init__(
        self,
        config: DataEncoderConfig,
        sample_size: int = 10000,
        categorical_threshold: float = 0.1,
        date_format_patterns: Optional[List[str]] = None,
        text_min_length: int = 50,
        boolean_values: Optional[List[Tuple[Any, Any]]] = None
    ):
        """
        Initialize schema inferrer.
        
        Args:
            config: Data encoder configuration
            sample_size: Number of rows to sample for inference
            categorical_threshold: Maximum ratio of unique values for categorical features
            date_format_patterns: Patterns for date format detection
            text_min_length: Minimum length for text feature detection
            boolean_values: List of value pairs representing boolean features
        """
        self.config = config
        self.sample_size = sample_size
        self.categorical_threshold = categorical_threshold
        self.date_format_patterns = date_format_patterns or [
            '%Y-%m-%d', '%d/%m/%Y', '%m/%d/%Y', '%Y/%m/%d',
            '%Y-%m-%d %H:%M:%S', '%Y-%m-%d %H:%M:%S.%f',
            '%d-%m-%Y', '%m-%d-%Y', '%Y%m%d', '%H:%M:%S'
        ]
        self.text_min_length = text_min_length
        
        self.boolean_values = boolean_values or [
            (True, False), 
            ('true', 'false'), 
            ('True', 'False'), 
            ('TRUE', 'FALSE'),
            ('yes', 'no'), 
            ('Yes', 'No'), 
            ('YES', 'NO'),
            ('y', 'n'), 
            ('Y', 'N'),
            ('1', '0'), 
            (1, 0)
        ]
        
        logger.info("Initialized SchemaInferrer")
    
    def _is_datetime(self, column, sample_values):
        """Check if a column contains datetime values."""
        # Skip if all values are missing
        if all(pd.isna(val) for val in sample_values):
            return False
            
        # Check if already datetime
        if pd.api.types.is_datetime64_any_dtype(column.dtype):
            return True
            
        # Check for basic timestamp pattern (numeric values)
        if all(isinstance(val, (int, float)) for val in sample_values if pd.notna(val)):
            # Check if values could be timestamps (between 1970 and 2050)
            min_timestamp = int(datetime(1970, 1, 1).timestamp())
            max_timestamp = int(datetime(2050, 12, 31).timestamp())
            valid_sample = [val for val in sample_values if pd.notna(val)]
            if valid_sample and all(min_timestamp <= val <= max_timestamp for val in valid_sample):
                return True
                
        # Check if strings match date patterns
        if all(isinstance(val, str) for val in sample_values if pd.notna(val)):
            valid_sample = [val for val in sample_values if pd.notna(val)]
            if not valid_sample:
                return False
                
            # Try converting samples with various date formats
            for pattern in self.date_format_patterns:
                try:
                    for val in valid_sample:
                        datetime.strptime(val, pattern)
                    return True
                except (ValueError, TypeError):
                    continue
                    
        return False
    
    def _is_boolean(self, sample_values):
        """Check if a column contains boolean values."""
        # Skip if all values are missing
        if all(pd.isna(val) for val in sample_values):
            return False
            
        # Check if already boolean
        if all(isinstance(val, bool) for val in sample_values if pd.notna(val)):
            return True
            
        # Check if values match boolean pairs
        valid_sample = [val for val in sample_values if pd.notna(val)]
        if not valid_sample:
            return False
            
        # Check if all values are in any of the boolean pairs
        for true_val, false_val in self.boolean_values:
            if all(val in [true_val, false_val] for val in valid_sample):
                return True
                
        return False
    
    def _is_categorical(self, column, sample_values, n_samples):
        """Check if a column contains categorical values."""
        # Skip if all values are missing
        if all(pd.isna(val) for val in sample_values):
            return False
            
        # Check unique ratio
        unique_values = set(val for val in sample_values if pd.notna(val))
        unique_ratio = len(unique_values) / (len(sample_values) - sum(pd.isna(val) for val in sample_values))
        
        # If unique ratio is below threshold, it's likely categorical
        if unique_ratio <= self.categorical_threshold:
            return True
            
        # Also check absolute number of unique values
        if len(unique_values) <= min(self.config.max_categories, int(n_samples * self.categorical_threshold)):
            return True
            
        return False
    
    def _is_numerical(self, column, sample_values):
        """Check if a column contains numerical values."""
        # Skip if all values are missing
        if all(pd.isna(val) for val in sample_values):
            return False
            
        # Check if already numeric
        if pd.api.types.is_numeric_dtype(column.dtype):
            return True
            
        # Check if all non-NA values can be converted to float
        try:
            valid_sample = [val for val in sample_values if pd.notna(val)]
            if not valid_sample:
                return False
                
            for val in valid_sample:
                float(val)
            return True
        except (ValueError, TypeError):
            return False
    
    def _is_text(self, column, sample_values):
        """Check if a column contains text data."""
        # Skip if all values are missing
        if all(pd.isna(val) for val in sample_values):
            return False
            
        # Check if strings with substantial text content
        if all(isinstance(val, str) for val in sample_values if pd.notna(val)):
            # Check if any value has substantial length
            valid_sample = [val for val in sample_values if pd.notna(val)]
            if any(len(val) >= self.text_min_length for val in valid_sample):
                return True
                
        return False
    
    def _is_discrete(self, sample_values):
        """Check if a numerical column contains discrete values."""
        numeric_sample = [float(val) for val in sample_values if pd.notna(val)]
        if not numeric_sample:
            return False
            
        # Check if all values are integers
        return all(val.is_integer() for val in [float(val) for val in numeric_sample])
    
    def _compute_statistics(self, column, feature_type):
        """Compute statistics for a column."""
        stats = {}
        
        if feature_type in [FeatureType.NUMERICAL_CONTINUOUS, FeatureType.NUMERICAL_DISCRETE]:
            # Numerical statistics
            stats['count'] = column.count()
            stats['missing'] = column.isna().sum()
            stats['min'] = column.min() if not pd.isna(column.min()) else None
            stats['max'] = column.max() if not pd.isna(column.max()) else None
            stats['mean'] = column.mean() if not pd.isna(column.mean()) else None
            stats['median'] = column.median() if not pd.isna(column.median()) else None
            stats['std'] = column.std() if not pd.isna(column.std()) else None
            stats['skew'] = column.skew() if not pd.isna(column.skew()) else None
            stats['kurtosis'] = column.kurtosis() if not pd.isna(column.kurtosis()) else None
            
            # Check for zeros
            if stats['count'] > 0:
                stats['zero_fraction'] = (column == 0).sum() / stats['count']
                
            # Check for outliers (z-score)
            if stats['std'] and stats['std'] > 0:
                z_scores = np.abs((column - stats['mean']) / stats['std'])
                stats['outliers_3sigma'] = (z_scores > 3).sum()
                stats['outliers_fraction'] = stats['outliers_3sigma'] / stats['count'] if stats['count'] > 0 else 0
                
        elif feature_type in [FeatureType.CATEGORICAL_NOMINAL, FeatureType.CATEGORICAL_ORDINAL]:
            # Categorical statistics
            stats['count'] = column.count()
            stats['missing'] = column.isna().sum()
            stats['unique'] = column.nunique()
            
            # Value counts
            value_counts = column.value_counts()
            
            if not value_counts.empty:
                stats['most_frequent'] = value_counts.index[0]
                stats['most_frequent_count'] = value_counts.iloc[0]
                
                if stats['count'] > 0:
                    stats['most_frequent_fraction'] = stats['most_frequent_count'] / stats['count']
                    
                # Entropy
                probs = value_counts / stats['count']
                stats['entropy'] = -np.sum(probs * np.log2(probs))
                
        elif feature_type == FeatureType.DATETIME:
            # Datetime statistics
            stats['count'] = column.count()
            stats['missing'] = column.isna().sum()
            stats['min'] = column.min() if not pd.isna(column.min()) else None
            stats['max'] = column.max() if not pd.isna(column.max()) else None
            
            if stats['min'] and stats['max']:
                stats['range_days'] = (stats['max'] - stats['min']).days
                
        elif feature_type == FeatureType.BOOLEAN:
            # Boolean statistics
            stats['count'] = column.count()
            stats['missing'] = column.isna().sum()
            stats['true_count'] = column.sum()
            
            if stats['count'] > 0:
                stats['true_fraction'] = stats['true_count'] / stats['count']
                
        elif feature_type == FeatureType.TEXT:
            # Text statistics
            stats['count'] = column.count()
            stats['missing'] = column.isna().sum()
            
            # Length statistics
            if stats['count'] > 0:
                text_lengths = column.dropna().apply(len)
                stats['mean_length'] = text_lengths.mean()
                stats['max_length'] = text_lengths.max()
                stats['min_length'] = text_lengths.min()
                
        return stats
    
    def _determine_feature_type(self, column, sample_values, n_samples):
        """Determine the type of a feature."""
        # Check for datetime
        if self._is_datetime(column, sample_values):
            return FeatureType.DATETIME
            
        # Check for boolean
        if self._is_boolean(sample_values):
            return FeatureType.BOOLEAN
            
        # Check for text
        if self._is_text(column, sample_values):
            return FeatureType.TEXT
            
        # Check for numerical
        if self._is_numerical(column, sample_values):
            # Check if discrete or continuous
            if self._is_discrete(sample_values):
                return FeatureType.NUMERICAL_DISCRETE
            else:
                return FeatureType.NUMERICAL_CONTINUOUS
                
        # Check for categorical
        if self._is_categorical(column, sample_values, n_samples):
            # TODO: Improve detection of ordinal vs nominal
            return FeatureType.CATEGORICAL_NOMINAL
            
        # Default to unknown
        return FeatureType.UNKNOWN
    
    def _determine_missing_strategy(self, column, feature_type, config):
        """Determine strategy for handling missing values."""
        # Check if the column has missing values
        missing_count = column.isna().sum()
        if missing_count == 0:
            return MissingValueStrategy.NONE
            
        # Use globally configured strategy as default
        strategy = config.handle_missing
        
        # Customize based on feature type
        if feature_type in [FeatureType.NUMERICAL_CONTINUOUS, FeatureType.NUMERICAL_DISCRETE]:
            # For numerical, use mean or median
            if strategy == MissingValueStrategy.IMPUTE_MEAN:
                return MissingValueStrategy.IMPUTE_MEAN
            elif strategy == MissingValueStrategy.IMPUTE_MEDIAN:
                return MissingValueStrategy.IMPUTE_MEDIAN
            else:
                return strategy
                
        elif feature_type in [FeatureType.CATEGORICAL_NOMINAL, FeatureType.CATEGORICAL_ORDINAL]:
            # For categorical, use mode or indicator
            if strategy == MissingValueStrategy.IMPUTE_MEAN or strategy == MissingValueStrategy.IMPUTE_MEDIAN:
                return MissingValueStrategy.IMPUTE_MODE
            else:
                return strategy
                
        elif feature_type == FeatureType.DATETIME:
            # For datetime, usually impute with a reference date or indicator
            if strategy == MissingValueStrategy.IMPUTE_MEAN or strategy == MissingValueStrategy.IMPUTE_MEDIAN:
                # Use indicator for datetime by default
                return MissingValueStrategy.INDICATOR
            else:
                return strategy
                
        else:
            # Default to configured strategy
            return strategy
    
    def _determine_encoding(self, column_name, feature_type, config):
        """Determine encoding strategy for a feature."""
        # Check for custom encoding in feature definitions
        if column_name in config.feature_definitions:
            feature_def = config.feature_definitions[column_name]
            if 'encoding' in feature_def:
                encoding_str = feature_def['encoding']
                
                # Convert string to enum based on feature type
                if feature_type in [FeatureType.NUMERICAL_CONTINUOUS, FeatureType.NUMERICAL_DISCRETE]:
                    return NumericalNormalization[encoding_str.upper()]
                elif feature_type in [FeatureType.CATEGORICAL_NOMINAL, FeatureType.CATEGORICAL_ORDINAL]:
                    return CategoricalEncoding[encoding_str.upper()]
                elif feature_type == FeatureType.DATETIME:
                    return DateEncoding[encoding_str.upper()]
                    
        # Default encoding based on feature type and configuration
        if feature_type in [FeatureType.NUMERICAL_CONTINUOUS, FeatureType.NUMERICAL_DISCRETE]:
            return config.numerical_normalization
        elif feature_type in [FeatureType.CATEGORICAL_NOMINAL, FeatureType.CATEGORICAL_ORDINAL]:
            return config.categorical_encoding
        elif feature_type == FeatureType.DATETIME:
            return config.date_encoding
        else:
            return None
    
    def _determine_imputation_value(self, column, feature_type, strategy):
        """Determine the value to use for imputation."""
        # Skip if no imputation needed
        if strategy == MissingValueStrategy.NONE or strategy == MissingValueStrategy.DROP:
            return None
            
        # Compute imputation value based on strategy
        if strategy == MissingValueStrategy.IMPUTE_MEAN:
            return column.mean() if feature_type in [FeatureType.NUMERICAL_CONTINUOUS, FeatureType.NUMERICAL_DISCRETE] else None
        elif strategy == MissingValueStrategy.IMPUTE_MEDIAN:
            return column.median() if feature_type in [FeatureType.NUMERICAL_CONTINUOUS, FeatureType.NUMERICAL_DISCRETE] else None
        elif strategy == MissingValueStrategy.IMPUTE_MODE:
            mode_value = column.mode()
            return mode_value.iloc[0] if not mode_value.empty else None
        elif strategy == MissingValueStrategy.IMPUTE_CONSTANT:
            return self.config.constant_value
        else:
            return None
    
    def _get_categories(self, column, feature_type, max_categories=None):
        """Get list of categories for a categorical feature."""
        if feature_type not in [FeatureType.CATEGORICAL_NOMINAL, FeatureType.CATEGORICAL_ORDINAL]:
            return None
            
        # Get value counts
        value_counts = column.value_counts(dropna=False)
        
        # Filter by maximum categories and minimum frequency
        if max_categories and len(value_counts) > max_categories:
            # Take most frequent categories up to max_categories
            value_counts = value_counts.iloc[:max_categories]
            
        if self.config.min_frequency > 1:
            # Filter out categories with low frequency
            value_counts = value_counts[value_counts >= self.config.min_frequency]
            
        # Convert to list of categories
        return value_counts.index.tolist()
    
    def infer_schema(self, data: Union[pd.DataFrame, Dict[str, List], List[Dict]]) -> DataSchema:
        """
        Infer schema from input data.
        
        Args:
            data: Input data (pandas DataFrame, dict of lists, or list of dicts)
            
        Returns:
            DataSchema: Inferred schema
        """
        # Convert data to pandas DataFrame if needed
        if not isinstance(data, pd.DataFrame):
            if isinstance(data, dict):
                # Convert dict of lists to DataFrame
                data = pd.DataFrame(data)
            elif isinstance(data, list) and data and isinstance(data[0], dict):
                # Convert list of dicts to DataFrame
                data = pd.DataFrame(data)
            else:
                raise ValueError("Data must be a pandas DataFrame, dict of lists, or list of dicts")
                
        # Take a sample if dataset is large
        n_samples = len(data)
        if n_samples > self.sample_size:
            data_sample = data.sample(n=self.sample_size, random_state=self.config.random_state)
        else:
            data_sample = data
            
        # Initialize schema
        schema = DataSchema()
        
        # Initialize column types
        categorical_columns = []
        numerical_columns = []
        datetime_columns = []
        text_columns = []
        boolean_columns = []
        
        # Process each column
        for i, column_name in enumerate(data.columns):
            # Skip columns specified to be dropped
            if column_name in self.config.drop_features:
                continue
                
            column = data[column_name]
            sample_values = column.iloc[:self.sample_size].tolist()
            
            # Determine feature type
            if column_name in self.config.feature_definitions:
                # Use predefined feature type if available
                feature_def = self.config.feature_definitions[column_name]
                feature_type_str = feature_def.get('type', 'unknown').upper()
                try:
                    feature_type = FeatureType[feature_type_str]
                except KeyError:
                    # Infer feature type if invalid or not specified
                    feature_type = self._determine_feature_type(column, sample_values, n_samples)
            else:
                # Infer feature type
                feature_type = self._determine_feature_type(column, sample_values, n_samples)
                
            # Update column type lists
            if feature_type in [FeatureType.CATEGORICAL_NOMINAL, FeatureType.CATEGORICAL_ORDINAL]:
                categorical_columns.append(column_name)
            elif feature_type in [FeatureType.NUMERICAL_CONTINUOUS, FeatureType.NUMERICAL_DISCRETE]:
                numerical_columns.append(column_name)
            elif feature_type == FeatureType.DATETIME:
                datetime_columns.append(column_name)
            elif feature_type == FeatureType.TEXT:
                text_columns.append(column_name)
            elif feature_type == FeatureType.BOOLEAN:
                boolean_columns.append(column_name)
                
            # Compute statistics
            statistics = self._compute_statistics(column, feature_type)
            
            # Determine ordinal status for categorical features
            is_ordinal = False
            if feature_type in [FeatureType.CATEGORICAL_NOMINAL, FeatureType.CATEGORICAL_ORDINAL]:
                if column_name in self.config.ordinal_categories:
                    is_ordinal = True
                    feature_type = FeatureType.CATEGORICAL_ORDINAL
                    
            # Determine missing value strategy
            missing_strategy = self._determine_missing_strategy(column, feature_type, self.config)
            
            # Determine encoding strategy
            encoding = self._determine_encoding(column_name, feature_type, self.config)
            
            # Determine categories for categorical features
            categories = None
            if feature_type in [FeatureType.CATEGORICAL_NOMINAL, FeatureType.CATEGORICAL_ORDINAL]:
                if column_name in self.config.ordinal_categories:
                    # Use predefined ordinal categories
                    categories = self.config.ordinal_categories[column_name]
                else:
                    # Infer categories
                    categories = self._get_categories(
                        column, feature_type, max_categories=self.config.max_categories
                    )
                    
            # Determine imputation value
            imputation_value = None
            if column_name in self.config.imputation_values:
                # Use predefined imputation value
                imputation_value = self.config.imputation_values[column_name]
            else:
                # Compute imputation value
                imputation_value = self._determine_imputation_value(column, feature_type, missing_strategy)
                
            # Check for cyclical encoding
            cyclical = column_name in self.config.cyclical_features
            
            # Check for log transform
            log_transform = column_name in self.config.log_transform_features
            
            # Determine embedding dimension
            embedding_dim = None
            if feature_type in [FeatureType.CATEGORICAL_NOMINAL, FeatureType.CATEGORICAL_ORDINAL]:
                embedding_dim = self.config.categorical_embedding_dim
                
            # Create feature schema
            feature_schema = FeatureSchema(
                name=column_name,
                feature_type=feature_type,
                dtype=str(column.dtype),
                encoding=encoding,
                categories=categories,
                is_ordinal=is_ordinal,
                missing_strategy=missing_strategy,
                statistics=statistics,
                weight=self.config.feature_weights.get(column_name, 1.0),
                is_target=column_name == self.config.target_column,
                preprocessing_steps=self.config.preprocessing_steps.get(column_name, []),
                imputation_value=imputation_value,
                min_value=statistics.get('min'),
                max_value=statistics.get('max'),
                mean_value=statistics.get('mean'),
                std_value=statistics.get('std'),
                feature_hashing=self.config.feature_hashing,
                embedding_dim=embedding_dim,
                cyclical=cyclical,
                log_transform=log_transform,
                index=i
            )
            
            # Add feature to schema
            schema.add_feature(feature_schema)
            
        # Set schema attributes
        schema.categorical_columns = categorical_columns
        schema.numerical_columns = numerical_columns
        schema.datetime_columns = datetime_columns
        schema.text_columns = text_columns
        schema.boolean_columns = boolean_columns
        schema.target_column = self.config.target_column
        schema.feature_groups = self.config.feature_groups
        
        # Compute dataset statistics
        schema.dataset_statistics = {
            'n_rows': n_samples,
            'n_columns': len(data.columns),
            'n_categorical': len(categorical_columns),
            'n_numerical': len(numerical_columns),
            'n_datetime': len(datetime_columns),
            'n_text': len(text_columns),
            'n_boolean': len(boolean_columns)
        }
        
        # Add metadata
        schema.metadata = {
            'created_at': datetime.now().isoformat(),
            'version': '1.0',
            'config': self.config.to_dict()
        }
        
        logger.info(f"Inferred schema with {len(schema.features)} features: "
                 f"{schema.dataset_statistics['n_numerical']} numerical, "
                 f"{schema.dataset_statistics['n_categorical']} categorical, "
                 f"{schema.dataset_statistics['n_datetime']} datetime, "
                 f"{schema.dataset_statistics['n_text']} text, "
                 f"{schema.dataset_statistics['n_boolean']} boolean.")
        
        return schema


class FeatureProcessor:
    """
    Base class for feature processing.
    
    This class defines the interface for feature processing components
    that handle encoding, normalization, and transformation of features.
    """
    
    def __init__(self, feature_schema: FeatureSchema, config: DataEncoderConfig):
        """
        Initialize feature processor.
        
        Args:
            feature_schema: Schema for the feature
            config: Data encoder configuration
        """
        self.feature_schema = feature_schema
        self.config = config
        self.name = feature_schema.name
        self.feature_type = feature_schema.feature_type
        self.encoder = None
        self.input_dim = None
        self.output_dim = None
        self.is_fitted = False
    
    def fit(self, data: pd.Series) -> 'FeatureProcessor':
        """
        Fit the processor to the data.
        
        Args:
            data: Feature values
            
        Returns:
            FeatureProcessor: Self
        """
        raise NotImplementedError("Subclasses must implement fit")
    
    def transform(self, data: pd.Series) -> torch.Tensor:
        """
        Transform the data.
        
        Args:
            data: Feature values
            
        Returns:
            torch.Tensor: Transformed values
        """
        raise NotImplementedError("Subclasses must implement transform")
    
    def fit_transform(self, data: pd.Series) -> torch.Tensor:
        """
        Fit the processor and transform the data.
        
        Args:
            data: Feature values
            
        Returns:
            torch.Tensor: Transformed values
        """
        self.fit(data)
        return self.transform(data)
    
    def inverse_transform(self, data: torch.Tensor) -> pd.Series:
        """
        Inverse transform the data.
        
        Args:
            data: Transformed values
            
        Returns:
            pd.Series: Original values
        """
        raise NotImplementedError("Subclasses must implement inverse_transform")
    
    def get_feature_names(self) -> List[str]:
        """
        Get feature names after transformation.
        
        Returns:
            List[str]: Feature names
        """
        raise NotImplementedError("Subclasses must implement get_feature_names")
    
    def get_output_dim(self) -> int:
        """
        Get output dimension.
        
        Returns:
            int: Output dimension
        """
        return self.output_dim
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert processor to dictionary.
        
        Returns:
            Dict[str, Any]: Processor as dictionary
        """
        raise NotImplementedError("Subclasses must implement to_dict")
    
    @classmethod
    def from_dict(cls, processor_dict: Dict[str, Any], feature_schema: FeatureSchema,
                 config: DataEncoderConfig) -> 'FeatureProcessor':
        """
        Create processor from dictionary.
        
        Args:
            processor_dict: Dictionary containing processor parameters
            feature_schema: Schema for the feature
            config: Data encoder configuration
            
        Returns:
            FeatureProcessor: Processor object
        """
        raise NotImplementedError("Subclasses must implement from_dict")


class NumericalProcessor(FeatureProcessor):
    """
    Processor for numerical features.
    
    This class handles normalization and transformation of numerical features.
    """
    
    def __init__(self, feature_schema: FeatureSchema, config: DataEncoderConfig):
        """
        Initialize numerical processor.
        
        Args:
            feature_schema: Schema for the feature
            config: Data encoder configuration
        """
        super().__init__(feature_schema, config)
        
        self.normalization = feature_schema.encoding or NumericalNormalization.STANDARD
        self.missing_strategy = feature_schema.missing_strategy or MissingValueStrategy.IMPUTE_MEAN
        self.imputation_value = feature_schema.imputation_value
        self.log_transform = feature_schema.log_transform
        self.cyclical = feature_schema.cyclical
        
        # Initialize encoders
        self.scaler = None
        self.imputer = None
        
        # Set output dimension (1 for standard numerical features, 2 for cyclical)
        self.output_dim = 2 if self.cyclical else 1
        
        # Initialize statistics
        self.min_value = feature_schema.min_value
        self.max_value = feature_schema.max_value
        self.mean_value = feature_schema.mean_value
        self.std_value = feature_schema.std_value
        
        logger.debug(f"Initialized NumericalProcessor for feature '{feature_schema.name}'")
    
    def _create_scaler(self):
        """Create scaler based on normalization strategy."""
        if self.normalization == NumericalNormalization.STANDARD:
            if _HAS_SKLEARN:
                return StandardScaler()
            else:
                # Simple implementation of standard scaling
                return lambda x: (x - self.mean_value) / (self.std_value if self.std_value else 1.0)
                
        elif self.normalization == NumericalNormalization.MINMAX:
            if _HAS_SKLEARN:
                return MinMaxScaler()
            else:
                # Simple implementation of min-max scaling
                return lambda x: (x - self.min_value) / (self.max_value - self.min_value) if (self.max_value - self.min_value) else 0.0
                
        elif self.normalization == NumericalNormalization.ROBUST:
            if _HAS_SKLEARN:
                return RobustScaler()
            else:
                # Simple implementation of robust scaling (using mean and std as approximation)
                return lambda x: (x - self.mean_value) / (self.std_value if self.std_value else 1.0)
                
        elif self.normalization == NumericalNormalization.QUANTILE:
            if _HAS_SKLEARN:
                return QuantileTransformer(output_distribution='normal')
            else:
                # Fall back to standard scaling
                return lambda x: (x - self.mean_value) / (self.std_value if self.std_value else 1.0)
                
        elif self.normalization == NumericalNormalization.POWER:
            if _HAS_SKLEARN:
                return PowerTransformer()
            else:
                # Fall back to log transform
                return lambda x: np.log1p(x - self.min_value + 1e-6) if self.log_transform else x
                
        elif self.normalization == NumericalNormalization.LOG:
            # Simple log transform
            return lambda x: np.log1p(x - self.min_value + 1e-6)
            
        else:  # NumericalNormalization.NONE
            # Identity transformation
            return lambda x: x
    
    def _create_imputer(self):
        """Create imputer based on missing value strategy."""
        if _HAS_SKLEARN:
            if self.missing_strategy == MissingValueStrategy.IMPUTE_MEAN:
                return SimpleImputer(strategy='mean')
            elif self.missing_strategy == MissingValueStrategy.IMPUTE_MEDIAN:
                return SimpleImputer(strategy='median')
            elif self.missing_strategy == MissingValueStrategy.IMPUTE_CONSTANT:
                return SimpleImputer(strategy='constant', fill_value=self.imputation_value or 0.0)
            else:
                # Default to mean imputation
                return SimpleImputer(strategy='mean')
        else:
            # Simple implementation of imputation
            if self.missing_strategy == MissingValueStrategy.IMPUTE_MEAN:
                return lambda x: x.fillna(self.mean_value if self.mean_value is not None else 0.0)
            elif self.missing_strategy == MissingValueStrategy.IMPUTE_MEDIAN:
                return lambda x: x.fillna(self.imputation_value if self.imputation_value is not None else 0.0)
            elif self.missing_strategy == MissingValueStrategy.IMPUTE_CONSTANT:
                return lambda x: x.fillna(self.imputation_value if self.imputation_value is not None else 0.0)
            else:
                return lambda x: x.fillna(0.0)
    
    def fit(self, data: pd.Series) -> 'NumericalProcessor':
        """
        Fit the processor to the data.
        
        Args:
            data: Feature values
            
        Returns:
            NumericalProcessor: Self
        """
        # Handle empty data
        if data.empty:
            self.is_fitted = True
            return self
            
        # Convert to numeric if needed
        if not pd.api.types.is_numeric_dtype(data.dtype):
            data = pd.to_numeric(data, errors='coerce')
            
        # Update statistics if not already set
        if self.min_value is None:
            self.min_value = data.min() if not pd.isna(data.min()) else 0.0
        if self.max_value is None:
            self.max_value = data.max() if not pd.isna(data.max()) else 1.0
        if self.mean_value is None:
            self.mean_value = data.mean() if not pd.isna(data.mean()) else 0.0
        if self.std_value is None:
            self.std_value = data.std() if not pd.isna(data.std()) else 1.0
            
        # Handle log transform
        if self.log_transform:
            # Shift data to be positive for log transform
            shift = 0.0
            if self.min_value <= 0:
                shift = -self.min_value + 1e-6
                data = data + shift
                self.min_value += shift
                self.max_value += shift
                self.mean_value += shift
                
            # Apply log transform
            data = np.log1p(data)
            
            # Update statistics after log transform
            self.min_value = np.log1p(self.min_value)
            self.max_value = np.log1p(self.max_value)
            # Approximation for mean and std after log transform
            self.mean_value = data.mean() if not pd.isna(data.mean()) else 0.0
            self.std_value = data.std() if not pd.isna(data.std()) else 1.0
            
        # Create and fit scaler
        self.scaler = self._create_scaler()
        
        if _HAS_SKLEARN and hasattr(self.scaler, 'fit'):
            # Reshape for sklearn
            self.scaler.fit(data.values.reshape(-1, 1))
            
        # Create and fit imputer
        self.imputer = self._create_imputer()
        
        if _HAS_SKLEARN and hasattr(self.imputer, 'fit'):
            # Reshape for sklearn
            self.imputer.fit(data.values.reshape(-1, 1))
            
        self.is_fitted = True
        return self
    
    def transform(self, data: pd.Series) -> torch.Tensor:
        """
        Transform the data.
        
        Args:
            data: Feature values
            
        Returns:
            torch.Tensor: Transformed values
        """
        if not self.is_fitted:
            raise ValueError("Processor must be fitted before transform")
            
        # Handle empty data
        if data.empty:
            return torch.zeros((len(data), self.output_dim), device=self.config.device)
            
        # Convert to numeric if needed
        if not pd.api.types.is_numeric_dtype(data.dtype):
            data = pd.to_numeric(data, errors='coerce')
            
        # Make a copy to avoid modifying the input
        data_copy = data.copy()
        
        # Handle log transform
        if self.log_transform:
            # Shift data to be positive for log transform
            shift = 0.0
            if self.min_value <= 0:
                shift = -self.min_value + 1e-6
                data_copy = data_copy + shift
                
            # Apply log transform
            data_copy = np.log1p(data_copy)
            
        # Apply imputation
        if _HAS_SKLEARN and hasattr(self.imputer, 'transform'):
            # Reshape for sklearn
            data_array = data_copy.values.reshape(-1, 1)
            data_array = self.imputer.transform(data_array)
            data_copy = pd.Series(data_array.ravel(), index=data.index)
        else:
            # Apply simple imputation
            data_copy = self.imputer(data_copy)
            
        # Apply scaling
        if _HAS_SKLEARN and hasattr(self.scaler, 'transform'):
            # Reshape for sklearn
            data_array = data_copy.values.reshape(-1, 1)
            data_array = self.scaler.transform(data_array)
            data_copy = pd.Series(data_array.ravel(), index=data.index)
        else:
            # Apply simple scaling
            data_copy = self.scaler(data_copy)
            
        # Convert to tensor
        if self.cyclical:
            # For cyclical features, use sin/cos encoding
            min_val = data_copy.min() if not pd.isna(data_copy.min()) else 0.0
            max_val = data_copy.max() if not pd.isna(data_copy.max()) else 1.0
            range_val = max_val - min_val or 1.0
            
            # Normalize to [0, 2*pi]
            normalized = (data_copy - min_val) / range_val * 2 * np.pi
            
            # Sin/cos encoding
            sin_vals = np.sin(normalized)
            cos_vals = np.cos(normalized)
            
            # Stack sin/cos values
            result = torch.tensor(
                np.column_stack([sin_vals, cos_vals]),
                dtype=torch.float32,
                device=self.config.device
            )
        else:
            # Regular numerical feature
            result = torch.tensor(
                data_copy.values.reshape(-1, 1),
                dtype=torch.float32,
                device=self.config.device
            )
            
        return result
    
    def inverse_transform(self, data: torch.Tensor) -> pd.Series:
        """
        Inverse transform the data.
        
        Args:
            data: Transformed values
            
        Returns:
            pd.Series: Original values
        """
        if not self.is_fitted:
            raise ValueError("Processor must be fitted before inverse_transform")
            
        # Handle cyclical features
        if self.cyclical:
            if data.shape[1] != 2:
                raise ValueError("Cyclical features require 2 columns for inverse transform")
                
            # Extract sin/cos values
            sin_vals = data[:, 0].cpu().numpy()
            cos_vals = data[:, 1].cpu().numpy()
            
            # Compute angle from sin/cos
            angles = np.arctan2(sin_vals, cos_vals)
            
            # Normalize to [0, 1]
            normalized = (angles + np.pi) / (2 * np.pi)
            
            # Scale back to original range
            min_val = self.min_value or 0.0
            max_val = self.max_value or 1.0
            range_val = max_val - min_val or 1.0
            
            values = normalized * range_val + min_val
        else:
            # Regular numerical feature
            values = data.cpu().numpy().ravel()
            
            # Inverse scaling
            if _HAS_SKLEARN and hasattr(self.scaler, 'inverse_transform'):
                values = values.reshape(-1, 1)
                values = self.scaler.inverse_transform(values).ravel()
                
        # Handle log transform
        if self.log_transform:
            values = np.expm1(values)
            
            # Reverse shift if needed
            if self.min_value <= 0:
                shift = -self.min_value + 1e-6
                values = values - shift
                
        return pd.Series(values)
    
    def get_feature_names(self) -> List[str]:
        """
        Get feature names after transformation.
        
        Returns:
            List[str]: Feature names
        """
        if self.cyclical:
            return [f"{self.name}_sin", f"{self.name}_cos"]
        else:
            return [self.name]
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert processor to dictionary.
        
        Returns:
            Dict[str, Any]: Processor as dictionary
        """
        processor_dict = {
            'name': self.name,
            'feature_type': self.feature_type.name,
            'normalization': self.normalization.name,
            'missing_strategy': self.missing_strategy.name,
            'imputation_value': self.imputation_value,
            'log_transform': self.log_transform,
            'cyclical': self.cyclical,
            'min_value': self.min_value,
            'max_value': self.max_value,
            'mean_value': self.mean_value,
            'std_value': self.std_value,
            'output_dim': self.output_dim,
            'is_fitted': self.is_fitted
        }
        
        return processor_dict
    
    @classmethod
    def from_dict(cls, processor_dict: Dict[str, Any], feature_schema: FeatureSchema,
                 config: DataEncoderConfig) -> 'NumericalProcessor':
        """
        Create processor from dictionary.
        
        Args:
            processor_dict: Dictionary containing processor parameters
            feature_schema: Schema for the feature
            config: Data encoder configuration
            
        Returns:
            NumericalProcessor: Processor object
        """
        # Create processor instance
        processor = cls(feature_schema, config)
        
        # Update attributes
        processor.normalization = NumericalNormalization[processor_dict['normalization']]
        processor.missing_strategy = MissingValueStrategy[processor_dict['missing_strategy']]
        processor.imputation_value = processor_dict['imputation_value']
        processor.log_transform = processor_dict['log_transform']
        processor.cyclical = processor_dict['cyclical']
        processor.min_value = processor_dict['min_value']
        processor.max_value = processor_dict['max_value']
        processor.mean_value = processor_dict['mean_value']
        processor.std_value = processor_dict['std_value']
        processor.output_dim = processor_dict['output_dim']
        processor.is_fitted = processor_dict['is_fitted']
        
        # Recreate encoders
        processor.scaler = processor._create_scaler()
        processor.imputer = processor._create_imputer()
        
        return processor


class CategoricalProcessor(FeatureProcessor):
    """
    Processor for categorical features.
    
    This class handles encoding of categorical features using various strategies
    including one-hot encoding, binary encoding, and embeddings.
    """
    
    def __init__(self, feature_schema: FeatureSchema, config: DataEncoderConfig):
        """
        Initialize categorical processor.
        
        Args:
            feature_schema: Schema for the feature
            config: Data encoder configuration
        """
        super().__init__(feature_schema, config)
        
        self.encoding = feature_schema.encoding or CategoricalEncoding.ONEHOT
        self.missing_strategy = feature_schema.missing_strategy or MissingValueStrategy.IMPUTE_MODE
        self.categories = feature_schema.categories or []
        self.is_ordinal = feature_schema.is_ordinal
        self.imputation_value = feature_schema.imputation_value
        self.feature_hashing = feature_schema.feature_hashing or config.feature_hashing
        self.n_hash_features = config.n_hash_features
        
        # Get embedding dimension
        self.embedding_dim = feature_schema.embedding_dim or config.categorical_embedding_dim
        
        # Initialize encoders
        self.encoder = None
        self.embedding = None
        self.categories_ = None  # Categories after fitting
        
        # Set output dimension based on encoding
        self.output_dim = 0  # Will be set during fit
        
        logger.debug(f"Initialized CategoricalProcessor for feature '{feature_schema.name}'")
    
    def _create_encoder(self):
        """Create encoder based on encoding strategy."""
        if self.encoding == CategoricalEncoding.ONEHOT:
            if _HAS_SKLEARN:
                return OneHotEncoder(sparse=False, handle_unknown='ignore')
            else:
                # Simple implementation of one-hot encoding
                return lambda x: pd.get_dummies(x, columns=[self.name], prefix=self.name)
                
        elif self.encoding == CategoricalEncoding.BINARY:
            if _HAS_CATEGORY_ENCODERS:
                return ce.BinaryEncoder(cols=[self.name])
            else:
                # Fall back to one-hot encoding
                return lambda x: pd.get_dummies(x, columns=[self.name], prefix=self.name)
                
        elif self.encoding == CategoricalEncoding.ORDINAL:
            if _HAS_SKLEARN:
                return LabelEncoder()
            else:
                # Simple implementation of ordinal encoding
                def ordinal_encoder(x):
                    # Map categories to indices
                    category_map = {cat: i for i, cat in enumerate(self.categories_)}
                    # Handle unknown values with -1
                    return x.map(category_map).fillna(-1).astype(int) + 1
                return ordinal_encoder
                
        elif self.encoding == CategoricalEncoding.TARGET:
            if _HAS_CATEGORY_ENCODERS and self.config.target_column:
                return ce.TargetEncoder(cols=[self.name])
            else:
                # Fall back to ordinal encoding
                return self._create_encoder_for_encoding(CategoricalEncoding.ORDINAL)
                
        elif self.encoding == CategoricalEncoding.FREQUENCY:
            if _HAS_CATEGORY_ENCODERS:
                return ce.CountEncoder(cols=[self.name])
            else:
                # Simple implementation of frequency encoding
                def frequency_encoder(x):
                    counts = x.value_counts(normalize=True)
                    return x.map(counts)
                return frequency_encoder
                
        elif self.encoding == CategoricalEncoding.EMBEDDING:
            # For embedding, we first need ordinal encoding
            if _HAS_SKLEARN:
                return LabelEncoder()
            else:
                # Simple implementation of ordinal encoding
                def ordinal_encoder(x):
                    # Map categories to indices
                    category_map = {cat: i for i, cat in enumerate(self.categories_)}
                    # Handle unknown values with -1
                    return x.map(category_map).fillna(-1).astype(int) + 1
                return ordinal_encoder
                
        elif self.encoding == CategoricalEncoding.HASHING:
            if _HAS_CATEGORY_ENCODERS:
                return ce.HashingEncoder(cols=[self.name], n_components=self.n_hash_features)
            else:
                # Simple implementation of feature hashing
                def hash_encoder(x):
                    # Hash each category to a number in [0, n_hash_features)
                    hashed = x.apply(lambda v: hash(str(v)) % self.n_hash_features)
                    # Convert to one-hot
                    result = np.zeros((len(x), self.n_hash_features))
                    for i, h in enumerate(hashed):
                        result[i, h] = 1
                    return pd.DataFrame(result, index=x.index)
                return hash_encoder
                
        elif self.encoding == CategoricalEncoding.WEIGHT_OF_EVIDENCE:
            if _HAS_CATEGORY_ENCODERS and self.config.target_column:
                return ce.WOEEncoder(cols=[self.name])
            else:
                # Fall back to ordinal encoding
                return self._create_encoder_for_encoding(CategoricalEncoding.ORDINAL)
                
        else:  # CategoricalEncoding.NONE
            # Identity transformation (convert to indices)
            return lambda x: x
    
    def _create_imputer(self):
        """Create imputer based on missing value strategy."""
        if _HAS_SKLEARN:
            if self.missing_strategy == MissingValueStrategy.IMPUTE_MODE:
                return SimpleImputer(strategy='most_frequent')
            elif self.missing_strategy == MissingValueStrategy.IMPUTE_CONSTANT:
                return SimpleImputer(strategy='constant', fill_value=self.imputation_value or 'missing')
            else:
                # Default to mode imputation
                return SimpleImputer(strategy='most_frequent')
        else:
            # Simple implementation of imputation
            if self.missing_strategy == MissingValueStrategy.IMPUTE_MODE:
                mode_value = self.imputation_value
                return lambda x: x.fillna(mode_value if mode_value is not None else 'missing')
            elif self.missing_strategy == MissingValueStrategy.IMPUTE_CONSTANT:
                return lambda x: x.fillna(self.imputation_value if self.imputation_value is not None else 'missing')
            else:
                return lambda x: x.fillna('missing')
    
    def fit(self, data: pd.Series) -> 'CategoricalProcessor':
        """
        Fit the processor to the data.
        
        Args:
            data: Feature values
            
        Returns:
            CategoricalProcessor: Self
        """
        # Handle empty data
        if data.empty:
            self.categories_ = self.categories or ['missing']
            self.output_dim = self._get_output_dim_for_encoding()
            self.is_fitted = True
            return self
            
        # Get categories from data if not provided
        if not self.categories_:
            if self.categories:
                self.categories_ = self.categories
            else:
                # Get categories from data
                value_counts = data.value_counts()
                
                # Filter by minimum frequency if specified
                if self.config.min_frequency > 1:
                    value_counts = value_counts[value_counts >= self.config.min_frequency]
                    
                # Limit to max categories if specified
                if self.config.max_categories and len(value_counts) > self.config.max_categories:
                    value_counts = value_counts.iloc[:self.config.max_categories]
                    
                self.categories_ = value_counts.index.tolist()
                
                # Add a category for unknown values if not already present
                if 'unknown' not in self.categories_:
                    self.categories_.append('unknown')
                    
        # Create and fit encoder
        self.encoder = self._create_encoder()
        
        if _HAS_SKLEARN and hasattr(self.encoder, 'fit'):
            # Convert to strings to handle mixed types
            data_str = data.fillna('missing').astype(str)
            
            if isinstance(self.encoder, OneHotEncoder):
                # Reshape for sklearn
                self.encoder.fit(data_str.values.reshape(-1, 1))
            elif isinstance(self.encoder, LabelEncoder):
                self.encoder.fit(data_str.values)
            elif _HAS_CATEGORY_ENCODERS and isinstance(self.encoder, ce.BaseEncoder):
                # Category-encoders expects a DataFrame
                self.encoder.fit(pd.DataFrame({self.name: data_str}), y=None)
                
        # Create embedding if using embedding encoding
        if self.encoding == CategoricalEncoding.EMBEDDING:
            n_categories = len(self.categories_) + 1  # +1 for unknown
            self.embedding = nn.Embedding(
                num_embeddings=n_categories,
                embedding_dim=self.embedding_dim,
                padding_idx=0  # Use 0 for padding
            )
            
            # Initialize embedding weights
            self.embedding.weight.data.normal_(0, 0.1)
            self.embedding.to(self.config.device)
            
        # Set output dimension based on encoding
        self.output_dim = self._get_output_dim_for_encoding()
        
        self.is_fitted = True
        return self
    
    def _get_output_dim_for_encoding(self):
        """Get output dimension based on encoding strategy."""
        if self.encoding == CategoricalEncoding.ONEHOT:
            return len(self.categories_)
        elif self.encoding == CategoricalEncoding.BINARY:
            return int(np.ceil(np.log2(max(2, len(self.categories_)))))
        elif self.encoding == CategoricalEncoding.HASHING:
            return self.n_hash_features
        elif self.encoding == CategoricalEncoding.EMBEDDING:
            return self.embedding_dim
        else:
            return 1  # Ordinal, target, frequency, WOE
    
    def transform(self, data: pd.Series) -> torch.Tensor:
        """
        Transform the data.
        
        Args:
            data: Feature values
            
        Returns:
            torch.Tensor: Transformed values
        """
        if not self.is_fitted:
            raise ValueError("Processor must be fitted before transform")
            
        # Handle empty data
        if data.empty:
            return torch.zeros((len(data), self.output_dim), device=self.config.device)
            
        # Convert to strings to handle mixed types
        data_str = data.fillna('missing').astype(str)
        
        # Apply encoding
        if _HAS_SKLEARN and hasattr(self.encoder, 'transform'):
            if isinstance(self.encoder, OneHotEncoder):
                # Reshape for sklearn
                encoded = self.encoder.transform(data_str.values.reshape(-1, 1))
            elif isinstance(self.encoder, LabelEncoder):
                # Handle unknown categories
                known_categories = set(self.encoder.classes_)
                unknown_mask = ~data_str.isin(known_categories)
                
                # Create a copy to avoid modifying the input
                data_copy = data_str.copy()
                if unknown_mask.any():
                    data_copy[unknown_mask] = self.encoder.classes_[0]  # Use first category for unknown
                    
                encoded = self.encoder.transform(data_copy.values)
                
                # Add 1 to indices to reserve 0 for padding/unknown
                encoded = encoded + 1
                
                # Set unknown values to 0
                encoded[unknown_mask.values] = 0
            elif _HAS_CATEGORY_ENCODERS and isinstance(self.encoder, ce.BaseEncoder):
                # Category-encoders expects a DataFrame
                encoded_df = self.encoder.transform(pd.DataFrame({self.name: data_str}))
                encoded = encoded_df.values
        else:
            # Apply simple encoding
            encoded = self.encoder(data_str)
            
        # Apply embedding if using embedding encoding
        if self.encoding == CategoricalEncoding.EMBEDDING:
            # Convert to tensor
            indices = torch.tensor(encoded, dtype=torch.long, device=self.config.device)
            
            # Apply embedding
            embedded = self.embedding(indices)
            
            return embedded
        else:
            # Convert to tensor
            if isinstance(encoded, np.ndarray) and encoded.ndim == 1:
                # Add dimension for single-column output
                encoded = encoded.reshape(-1, 1)
                
            # Handle Pandas output
            if isinstance(encoded, pd.DataFrame):
                encoded = encoded.values
                
            result = torch.tensor(encoded, dtype=torch.float32, device=self.config.device)
            
            return result
    
    def inverse_transform(self, data: torch.Tensor) -> pd.Series:
        """
        Inverse transform the data.
        
        Args:
            data: Transformed values
            
        Returns:
            pd.Series: Original values
        """
        if not self.is_fitted:
            raise ValueError("Processor must be fitted before inverse_transform")
            
        # Different inverse transform based on encoding
        if self.encoding == CategoricalEncoding.ONEHOT:
            if _HAS_SKLEARN and isinstance(self.encoder, OneHotEncoder):
                # Convert to numpy
                data_np = data.cpu().numpy()
                
                # Get indices of maximum values (most likely category)
                indices = np.argmax(data_np, axis=1)
                
                # Map indices to categories
                if hasattr(self.encoder, 'categories_'):
                    categories = self.encoder.categories_[0]
                    return pd.Series([categories[i] if i < len(categories) else 'unknown' for i in indices])
                else:
                    return pd.Series([self.categories_[i] if i < len(self.categories_) else 'unknown' for i in indices])
            else:
                # Simple implementation
                data_np = data.cpu().numpy()
                indices = np.argmax(data_np, axis=1)
                return pd.Series([self.categories_[i] if i < len(self.categories_) else 'unknown' for i in indices])
                
        elif self.encoding == CategoricalEncoding.EMBEDDING:
            # For embeddings, find closest embedding vector
            data_np = data.cpu().numpy()
            embeddings_np = self.embedding.weight.data.cpu().numpy()
            
            # Compute distances to all embedding vectors
            indices = []
            for vec in data_np:
                distances = np.sqrt(np.sum((embeddings_np - vec) ** 2, axis=1))
                idx = np.argmin(distances)
                indices.append(idx)
                
            # Map indices to categories
            return pd.Series([self.categories_[i - 1] if 0 < i < len(self.categories_) + 1 else 'unknown' for i in indices])
            
        elif self.encoding in [CategoricalEncoding.ORDINAL, CategoricalEncoding.TARGET, 
                             CategoricalEncoding.FREQUENCY, CategoricalEncoding.WEIGHT_OF_EVIDENCE]:
            # Convert to numpy and flatten
            indices = data.cpu().numpy().ravel().astype(int)
            
            # Adjust indices
            if self.encoding == CategoricalEncoding.ORDINAL:
                indices = indices - 1  # Assuming 0 is padding/unknown
                
            # Map indices to categories
            if _HAS_SKLEARN and isinstance(self.encoder, LabelEncoder):
                categories = self.encoder.classes_
                return pd.Series([categories[i] if 0 <= i < len(categories) else 'unknown' for i in indices])
            else:
                return pd.Series([self.categories_[i] if 0 <= i < len(self.categories_) else 'unknown' for i in indices])
                
        else:
            # For other encodings (binary, hashing), reverse mapping is challenging
            # Just return original indices
            indices = data.cpu().numpy().ravel().astype(int)
            return pd.Series(indices)
    
    def get_feature_names(self) -> List[str]:
        """
        Get feature names after transformation.
        
        Returns:
            List[str]: Feature names
        """
        if self.encoding == CategoricalEncoding.ONEHOT:
            if _HAS_SKLEARN and isinstance(self.encoder, OneHotEncoder) and hasattr(self.encoder, 'get_feature_names_out'):
                feature_names = self.encoder.get_feature_names_out([self.name])
                return feature_names.tolist()
            else:
                return [f"{self.name}_{cat}" for cat in self.categories_]
                
        elif self.encoding == CategoricalEncoding.BINARY:
            n_bits = int(np.ceil(np.log2(max(2, len(self.categories_)))))
            return [f"{self.name}_bit{i}" for i in range(n_bits)]
            
        elif self.encoding == CategoricalEncoding.HASHING:
            return [f"{self.name}_hash{i}" for i in range(self.n_hash_features)]
            
        elif self.encoding == CategoricalEncoding.EMBEDDING:
            return [f"{self.name}_emb{i}" for i in range(self.embedding_dim)]
            
        else:
            return [self.name]
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert processor to dictionary.
        
        Returns:
            Dict[str, Any]: Processor as dictionary
        """
        processor_dict = {
            'name': self.name,
            'feature_type': self.feature_type.name,
            'encoding': self.encoding.name,
            'missing_strategy': self.missing_strategy.name,
            'imputation_value': self.imputation_value,
            'is_ordinal': self.is_ordinal,
            'feature_hashing': self.feature_hashing,
            'n_hash_features': self.n_hash_features,
            'embedding_dim': self.embedding_dim,
            'categories_': self.categories_,
            'output_dim': self.output_dim,
            'is_fitted': self.is_fitted
        }
        
        # Save embedding weights if available
        if self.embedding is not None:
            processor_dict['embedding_weights'] = self.embedding.weight.data.cpu().numpy().tolist()
            
        return processor_dict
    
    @classmethod
    def from_dict(cls, processor_dict: Dict[str, Any], feature_schema: FeatureSchema,
                 config: DataEncoderConfig) -> 'CategoricalProcessor':
        """
        Create processor from dictionary.
        
        Args:
            processor_dict: Dictionary containing processor parameters
            feature_schema: Schema for the feature
            config: Data encoder configuration
            
        Returns:
            CategoricalProcessor: Processor object
        """
        # Create processor instance
        processor = cls(feature_schema, config)
        
        # Update attributes
        processor.encoding = CategoricalEncoding[processor_dict['encoding']]
        processor.missing_strategy = MissingValueStrategy[processor_dict['missing_strategy']]
        processor.imputation_value = processor_dict['imputation_value']
        processor.is_ordinal = processor_dict['is_ordinal']
        processor.feature_hashing = processor_dict['feature_hashing']
        processor.n_hash_features = processor_dict['n_hash_features']
        processor.embedding_dim = processor_dict['embedding_dim']
        processor.categories_ = processor_dict['categories_']
        processor.output_dim = processor_dict['output_dim']
        processor.is_fitted = processor_dict['is_fitted']
        
        # Recreate encoder
        processor.encoder = processor._create_encoder()
        
        # Recreate embedding if needed
        if processor.encoding == CategoricalEncoding.EMBEDDING and 'embedding_weights' in processor_dict:
            n_categories = len(processor.categories_) + 1  # +1 for unknown
            processor.embedding = nn.Embedding(
                num_embeddings=n_categories,
                embedding_dim=processor.embedding_dim,
                padding_idx=0
            )
            
            # Load embedding weights
            weights = torch.tensor(processor_dict['embedding_weights'], dtype=torch.float32)
            processor.embedding.weight.data = weights
            processor.embedding.to(config.device)
            
        return processor


class DatetimeProcessor(FeatureProcessor):
    """
    Processor for datetime features.
    
    This class handles encoding of datetime features using various strategies
    including cyclical encoding, ordinal encoding, and component-wise encoding.
    """
    
    def __init__(self, feature_schema: FeatureSchema, config: DataEncoderConfig):
        """
        Initialize datetime processor.
        
        Args:
            feature_schema: Schema for the feature
            config: Data encoder configuration
        """
        super().__init__(feature_schema, config)
        
        self.encoding = feature_schema.encoding or DateEncoding.CYCLICAL
        self.missing_strategy = feature_schema.missing_strategy or MissingValueStrategy.INDICATOR
        self.imputation_value = feature_schema.imputation_value
        
        # Reference date for encoding (if not set, will be min date from data)
        self.reference_date = None
        
        # Min and max dates
        self.min_date = None
        self.max_date = None
        
        # Date format for string parsing
        self.date_format = None
        
        # Set output dimension based on encoding
        self.output_dim = self._get_output_dim_for_encoding()
        
        logger.debug(f"Initialized DatetimeProcessor for feature '{feature_schema.name}'")
    
    def _get_output_dim_for_encoding(self):
        """Get output dimension based on encoding strategy."""
        if self.encoding == DateEncoding.CYCLICAL:
            # Year (sin/cos) + Month (sin/cos) + Day (sin/cos) + Hour (sin/cos) + Minute (sin/cos)
            return 10
        elif self.encoding == DateEncoding.COMPONENTS:
            # Year, Month, Day, DayOfWeek, DayOfYear, Hour, Minute, Second
            return 8
        elif self.encoding == DateEncoding.ORDINAL:
            # Single value representing days since reference date
            return 1
        elif self.encoding == DateEncoding.DIFFERENCE:
            # Single value representing days since reference date
            return 1
        else:  # DateEncoding.NONE
            return 1
    
    def _parse_datetime(self, date_str):
        """Parse datetime from string using detected format."""
        if pd.isna(date_str):
            return None
            
        if isinstance(date_str, (datetime, date, pd.Timestamp)):
            return pd.Timestamp(date_str)
            
        if isinstance(date_str, (int, float)):
            # Assume timestamp in seconds
            try:
                return pd.Timestamp(datetime.fromtimestamp(date_str))
            except (ValueError, OverflowError):
                # Try milliseconds if seconds doesn't work
                try:
                    return pd.Timestamp(datetime.fromtimestamp(date_str / 1000))
                except (ValueError, OverflowError):
                    return None
                    
        if not isinstance(date_str, str):
            return None
            
        # Try known formats
        known_formats = [
            '%Y-%m-%d', '%Y-%m-%d %H:%M:%S', '%Y-%m-%d %H:%M:%S.%f',
            '%d/%m/%Y', '%d/%m/%Y %H:%M:%S', '%d/%m/%Y %H:%M:%S.%f',
            '%m/%d/%Y', '%m/%d/%Y %H:%M:%S', '%m/%d/%Y %H:%M:%S.%f',
            '%Y/%m/%d', '%Y/%m/%d %H:%M:%S', '%Y/%m/%d %H:%M:%S.%f',
            '%d-%m-%Y', '%d-%m-%Y %H:%M:%S', '%d-%m-%Y %H:%M:%S.%f',
            '%m-%d-%Y', '%m-%d-%Y %H:%M:%S', '%m-%d-%Y %H:%M:%S.%f',
            '%Y%m%d', '%Y%m%d%H%M%S'
        ]
        
        # Try with known formats
        for fmt in known_formats:
            try:
                return pd.Timestamp(datetime.strptime(date_str, fmt))
            except ValueError:
                continue
                
        # If we have a specific format, try it
        if self.date_format:
            try:
                return pd.Timestamp(datetime.strptime(date_str, self.date_format))
            except ValueError:
                pass
                
        # Try pandas parsing
        try:
            return pd.Timestamp(pd.to_datetime(date_str))
        except (ValueError, TypeError):
            return None
    
    def _detect_format(self, date_sample):
        """Detect date format from a sample of dates."""
        if not date_sample:
            return None
            
        # Skip non-string values
        str_sample = [d for d in date_sample if isinstance(d, str)]
        if not str_sample:
            return None
            
        known_formats = [
            '%Y-%m-%d', '%Y-%m-%d %H:%M:%S', '%Y-%m-%d %H:%M:%S.%f',
            '%d/%m/%Y', '%d/%m/%Y %H:%M:%S', '%d/%m/%Y %H:%M:%S.%f',
            '%m/%d/%Y', '%m/%d/%Y %H:%M:%S', '%m/%d/%Y %H:%M:%S.%f',
            '%Y/%m/%d', '%Y/%m/%d %H:%M:%S', '%Y/%m/%d %H:%M:%S.%f',
            '%d-%m-%Y', '%d-%m-%Y %H:%M:%S', '%d-%m-%Y %H:%M:%S.%f',
            '%m-%d-%Y', '%m-%d-%Y %H:%M:%S', '%m-%d-%Y %H:%M:%S.%f',
            '%Y%m%d', '%Y%m%d%H%M%S'
        ]
        
        # Try each format and count successes
        format_counts = {}
        for fmt in known_formats:
            count = 0
            for date_str in str_sample:
                try:
                    datetime.strptime(date_str, fmt)
                    count += 1
                except ValueError:
                    pass
            format_counts[fmt] = count
            
        # Get format with most successes
        best_format = max(format_counts, key=format_counts.get)
        if format_counts[best_format] > 0:
            return best_format
            
        return None
    
    def fit(self, data: pd.Series) -> 'DatetimeProcessor':
        """
        Fit the processor to the data.
        
        Args:
            data: Feature values
            
        Returns:
            DatetimeProcessor: Self
        """
        # Handle empty data
        if data.empty:
            self.is_fitted = True
            return self
            
        # Convert to datetime if needed
        if pd.api.types.is_datetime64_any_dtype(data.dtype):
            # Already datetime type
            datetime_data = data
        else:
            # Try to convert to datetime
            if isinstance(data.iloc[0], (str, int, float)):
                # Detect format if data contains strings
                str_sample = [d for d in data.sample(min(100, len(data))) if isinstance(d, str)]
                if str_sample:
                    self.date_format = self._detect_format(str_sample)
                    
            # Parse datetimes
            datetime_data = pd.Series([self._parse_datetime(d) for d in data])
            
        # Handle missing values
        datetime_data = datetime_data.dropna()
        
        # Set reference date and min/max dates
        if not datetime_data.empty:
            self.min_date = datetime_data.min()
            self.max_date = datetime_data.max()
            self.reference_date = self.min_date
            
        # Set imputation value if not already set
        if self.imputation_value is None:
            if not datetime_data.empty:
                # Default imputation to min date
                self.imputation_value = self.min_date
            else:
                # Default to 1970-01-01
                self.imputation_value = pd.Timestamp('1970-01-01')
                
        self.is_fitted = True
        return self
    
    def transform(self, data: pd.Series) -> torch.Tensor:
        """
        Transform the data.
        
        Args:
            data: Feature values
            
        Returns:
            torch.Tensor: Transformed values
        """
        if not self.is_fitted:
            raise ValueError("Processor must be fitted before transform")
            
        # Handle empty data
        if data.empty:
            return torch.zeros((len(data), self.output_dim), device=self.config.device)
            
        # Convert to datetime if needed
        if pd.api.types.is_datetime64_any_dtype(data.dtype):
            # Already datetime type
            datetime_data = data.copy()
        else:
            # Parse datetimes
            datetime_data = pd.Series([self._parse_datetime(d) for d in data])
            
        # Handle missing values based on strategy
        if self.missing_strategy == MissingValueStrategy.IMPUTE_CONSTANT:
            datetime_data = datetime_data.fillna(self.imputation_value)
        elif self.missing_strategy == MissingValueStrategy.DROP:
            datetime_data = datetime_data.dropna()
        else:
            # For other strategies, keep NaT values for now
            pass
            
        # Convert to encoded representation based on strategy
        if self.encoding == DateEncoding.CYCLICAL:
            # Compute cyclical features
            if datetime_data.empty:
                return torch.zeros((len(data), self.output_dim), device=self.config.device)
                
            # Extract components
            year = datetime_data.dt.year.fillna(0).astype(int)
            month = datetime_data.dt.month.fillna(0).astype(int)
            day = datetime_data.dt.day.fillna(0).astype(int)
            hour = datetime_data.dt.hour.fillna(0).astype(int)
            minute = datetime_data.dt.minute.fillna(0).astype(int)
            
            # Normalize components
            year_norm = (year - self.min_date.year) / max(1, self.max_date.year - self.min_date.year)
            month_norm = (month - 1) / 11  # 1-12 to 0-1
            day_norm = (day - 1) / 30  # 1-31 to 0-1
            hour_norm = hour / 23  # 0-23 to 0-1
            minute_norm = minute / 59  # 0-59 to 0-1
            
            # Convert to sin/cos representation
            year_sin = np.sin(2 * np.pi * year_norm)
            year_cos = np.cos(2 * np.pi * year_norm)
            month_sin = np.sin(2 * np.pi * month_norm)
            month_cos = np.cos(2 * np.pi * month_norm)
            day_sin = np.sin(2 * np.pi * day_norm)
            day_cos = np.cos(2 * np.pi * day_norm)
            hour_sin = np.sin(2 * np.pi * hour_norm)
            hour_cos = np.cos(2 * np.pi * hour_norm)
            minute_sin = np.sin(2 * np.pi * minute_norm)
            minute_cos = np.cos(2 * np.pi * minute_norm)
            
            # Stack features
            result = np.column_stack([
                year_sin, year_cos,
                month_sin, month_cos,
                day_sin, day_cos,
                hour_sin, hour_cos,
                minute_sin, minute_cos
            ])
            
            # Handle NaN values (missing dates)
            if self.missing_strategy == MissingValueStrategy.INDICATOR:
                # Add indicator for missing dates (1 for missing, 0 for present)
                missing_indicator = np.isnat(datetime_data.values).astype(float)
                result = np.where(missing_indicator[:, np.newaxis], 0, result)
                
            return torch.tensor(result, dtype=torch.float32, device=self.config.device)
            
        elif self.encoding == DateEncoding.COMPONENTS:
            # Extract components
            if datetime_data.empty:
                return torch.zeros((len(data), self.output_dim), device=self.config.device)
                
            # Extract components
            year = datetime_data.dt.year.fillna(0).astype(int)
            month = datetime_data.dt.month.fillna(0).astype(int)
            day = datetime_data.dt.day.fillna(0).astype(int)
            dow = datetime_data.dt.dayofweek.fillna(0).astype(int)
            doy = datetime_data.dt.dayofyear.fillna(0).astype(int)
            hour = datetime_data.dt.hour.fillna(0).astype(int)
            minute = datetime_data.dt.minute.fillna(0).astype(int)
            second = datetime_data.dt.second.fillna(0).astype(int)
            
            # Normalize components
            year_norm = (year - self.min_date.year) / max(1, self.max_date.year - self.min_date.year)
            month_norm = (month - 1) / 11  # 1-12 to 0-1
            day_norm = (day - 1) / 30  # 1-31 to 0-1
            dow_norm = dow / 6  # 0-6 to 0-1
            doy_norm = (doy - 1) / 364  # 1-365 to 0-1
            hour_norm = hour / 23  # 0-23 to 0-1
            minute_norm = minute / 59  # 0-59 to 0-1
            second_norm = second / 59  # 0-59 to 0-1
            
            # Stack features
            result = np.column_stack([
                year_norm, month_norm, day_norm, dow_norm,
                doy_norm, hour_norm, minute_norm, second_norm
            ])
            
            # Handle NaN values (missing dates)
            if self.missing_strategy == MissingValueStrategy.INDICATOR:
                # Add indicator for missing dates (1 for missing, 0 for present)
                missing_indicator = np.isnat(datetime_data.values).astype(float)
                result = np.where(missing_indicator[:, np.newaxis], 0, result)
                
            return torch.tensor(result, dtype=torch.float32, device=self.config.device)
            
        elif self.encoding in [DateEncoding.ORDINAL, DateEncoding.DIFFERENCE]:
            # Compute days since reference date
            if not self.reference_date:
                reference_date = pd.Timestamp('1970-01-01')
            else:
                reference_date = self.reference_date
                
            if datetime_data.empty:
                return torch.zeros((len(data), 1), device=self.config.device)
                
            # Compute days difference
            try:
                days_diff = (datetime_data - reference_date).dt.total_seconds() / (24 * 3600)
            except Exception:
                # Handle case where datetime_data contains NaT values
                days_diff = pd.Series([
                    (d - reference_date).total_seconds() / (24 * 3600) if not pd.isna(d) else np.nan
                    for d in datetime_data
                ])
                
            # Normalize days difference
            time_range = (self.max_date - self.min_date).total_seconds() / (24 * 3600)
            if time_range > 0:
                days_diff_norm = days_diff / time_range
            else:
                days_diff_norm = days_diff
                
            # Handle missing values
            if self.missing_strategy == MissingValueStrategy.INDICATOR:
                # Set missing values to a special value
                days_diff_norm = days_diff_norm.fillna(-1)
            else:
                # Fill missing values with 0
                days_diff_norm = days_diff_norm.fillna(0)
                
            # Reshape to 2D and convert to tensor
            result = days_diff_norm.values.reshape(-1, 1)
            
            return torch.tensor(result, dtype=torch.float32, device=self.config.device)
            
        else:  # DateEncoding.NONE
            # Convert to seconds since epoch
            if datetime_data.empty:
                return torch.zeros((len(data), 1), device=self.config.device)
                
            # Compute seconds since epoch
            try:
                epoch = pd.Timestamp('1970-01-01')
                seconds = (datetime_data - epoch).dt.total_seconds()
            except Exception:
                # Handle case where datetime_data contains NaT values
                seconds = pd.Series([
                    (d - epoch).total_seconds() if not pd.isna(d) else np.nan
                    for d in datetime_data
                ])
                
            # Handle missing values
            if self.missing_strategy == MissingValueStrategy.INDICATOR:
                # Set missing values to a special value
                seconds = seconds.fillna(-1)
            else:
                # Fill missing values with 0
                seconds = seconds.fillna(0)
                
            # Reshape to 2D and convert to tensor
            result = seconds.values.reshape(-1, 1)
            
            return torch.tensor(result, dtype=torch.float32, device=self.config.device)
    
    def inverse_transform(self, data: torch.Tensor) -> pd.Series:
        """
        Inverse transform the data.
        
        Args:
            data: Transformed values
            
        Returns:
            pd.Series: Original datetime values
        """
        if not self.is_fitted:
            raise ValueError("Processor must be fitted before inverse_transform")
            
        # Different inverse transform based on encoding
        if self.encoding == DateEncoding.CYCLICAL:
            # Extracting year, month, day from cyclical encoding is approximate at best
            # Especially for year, where the cycle is data-dependent
            # But we can try a best-effort approach
            
            # Extract sin/cos components
            year_sin, year_cos = data[:, 0].cpu().numpy(), data[:, 1].cpu().numpy()
            month_sin, month_cos = data[:, 2].cpu().numpy(), data[:, 3].cpu().numpy()
            day_sin, day_cos = data[:, 4].cpu().numpy(), data[:, 5].cpu().numpy()
            hour_sin, hour_cos = data[:, 6].cpu().numpy(), data[:, 7].cpu().numpy()
            minute_sin, minute_cos = data[:, 8].cpu().numpy(), data[:, 9].cpu().numpy()
            
            # Compute angles
            year_angle = np.arctan2(year_sin, year_cos)
            month_angle = np.arctan2(month_sin, month_cos)
            day_angle = np.arctan2(day_sin, day_cos)
            hour_angle = np.arctan2(hour_sin, hour_cos)
            minute_angle = np.arctan2(minute_sin, minute_cos)
            
            # Normalize angles to [0, 1]
            year_norm = (year_angle + np.pi) / (2 * np.pi)
            month_norm = (month_angle + np.pi) / (2 * np.pi)
            day_norm = (day_angle + np.pi) / (2 * np.pi)
            hour_norm = (hour_angle + np.pi) / (2 * np.pi)
            minute_norm = (minute_angle + np.pi) / (2 * np.pi)
            
            # Convert to actual values
            year = self.min_date.year + year_norm * (self.max_date.year - self.min_date.year)
            month = month_norm * 11 + 1
            day = day_norm * 30 + 1
            hour = hour_norm * 23
            minute = minute_norm * 59
            
            # Round to integers
            year = np.round(year).astype(int)
            month = np.round(month).astype(int)
            day = np.round(day).astype(int)
            hour = np.round(hour).astype(int)
            minute = np.round(minute).astype(int)
            
            # Clip to valid ranges
            month = np.clip(month, 1, 12)
            day = np.clip(day, 1, 31)
            hour = np.clip(hour, 0, 23)
            minute = np.clip(minute, 0, 59)
            
            # Create datetime objects
            datetime_objs = []
            for y, m, d, h, mm in zip(year, month, day, hour, minute):
                try:
                    dt = datetime(y, m, d, h, mm)
                    datetime_objs.append(dt)
                except ValueError:
                    # Handle invalid dates (e.g., February 31)
                    datetime_objs.append(pd.NaT)
                    
            return pd.Series(datetime_objs)
            
        elif self.encoding == DateEncoding.COMPONENTS:
            # Extract components
            year_norm = data[:, 0].cpu().numpy()
            month_norm = data[:, 1].cpu().numpy()
            day_norm = data[:, 2].cpu().numpy()
            hour_norm = data[:, 5].cpu().numpy()
            minute_norm = data[:, 6].cpu().numpy()
            second_norm = data[:, 7].cpu().numpy()
            
            # Convert to actual values
            year = self.min_date.year + year_norm * (self.max_date.year - self.min_date.year)
            month = month_norm * 11 + 1
            day = day_norm * 30 + 1
            hour = hour_norm * 23
            minute = minute_norm * 59
            second = second_norm * 59
            
            # Round to integers
            year = np.round(year).astype(int)
            month = np.round(month).astype(int)
            day = np.round(day).astype(int)
            hour = np.round(hour).astype(int)
            minute = np.round(minute).astype(int)
            second = np.round(second).astype(int)
            
            # Clip to valid ranges
            month = np.clip(month, 1, 12)
            day = np.clip(day, 1, 31)
            hour = np.clip(hour, 0, 23)
            minute = np.clip(minute, 0, 59)
            second = np.clip(second, 0, 59)
            
            # Create datetime objects
            datetime_objs = []
            for y, m, d, h, mm, s in zip(year, month, day, hour, minute, second):
                try:
                    dt = datetime(y, m, d, h, mm, s)
                    datetime_objs.append(dt)
                except ValueError:
                    # Handle invalid dates (e.g., February 31)
                    datetime_objs.append(pd.NaT)
                    
            return pd.Series(datetime_objs)
            
        elif self.encoding in [DateEncoding.ORDINAL, DateEncoding.DIFFERENCE]:
            # Extract days difference
            days_diff_norm = data.cpu().numpy().ravel()
            
            # Handle special values for missing indicators
            if self.missing_strategy == MissingValueStrategy.INDICATOR:
                missing_mask = days_diff_norm == -1
                days_diff_norm[missing_mask] = 0  # Will be set to NaT later
                
            # Denormalize days difference
            time_range = (self.max_date - self.min_date).total_seconds() / (24 * 3600)
            days_diff = days_diff_norm * time_range
            
            # Compute actual dates
            reference_date = self.reference_date or pd.Timestamp('1970-01-01')
            datetime_objs = [reference_date + pd.Timedelta(days=d) for d in days_diff]
            
            # Set missing values to NaT
            if self.missing_strategy == MissingValueStrategy.INDICATOR:
                for i in range(len(datetime_objs)):
                    if missing_mask[i]:
                        datetime_objs[i] = pd.NaT
                        
            return pd.Series(datetime_objs)
            
        else:  # DateEncoding.NONE
            # Extract seconds since epoch
            seconds = data.cpu().numpy().ravel()
            
            # Handle special values for missing indicators
            if self.missing_strategy == MissingValueStrategy.INDICATOR:
                missing_mask = seconds == -1
                seconds[missing_mask] = 0  # Will be set to NaT later
                
            # Compute actual dates
            epoch = pd.Timestamp('1970-01-01')
            datetime_objs = [epoch + pd.Timedelta(seconds=s) for s in seconds]
            
            # Set missing values to NaT
            if self.missing_strategy == MissingValueStrategy.INDICATOR:
                for i in range(len(datetime_objs)):
                    if missing_mask[i]:
                        datetime_objs[i] = pd.NaT
                        
            return pd.Series(datetime_objs)
    
    def get_feature_names(self) -> List[str]:
        """
        Get feature names after transformation.
        
        Returns:
            List[str]: Feature names
        """
        if self.encoding == DateEncoding.CYCLICAL:
            return [
                f"{self.name}_year_sin", f"{self.name}_year_cos",
                f"{self.name}_month_sin", f"{self.name}_month_cos",
                f"{self.name}_day_sin", f"{self.name}_day_cos",
                f"{self.name}_hour_sin", f"{self.name}_hour_cos",
                f"{self.name}_minute_sin", f"{self.name}_minute_cos"
            ]
        elif self.encoding == DateEncoding.COMPONENTS:
            return [
                f"{self.name}_year", f"{self.name}_month", f"{self.name}_day",
                f"{self.name}_dayofweek", f"{self.name}_dayofyear",
                f"{self.name}_hour", f"{self.name}_minute", f"{self.name}_second"
            ]
        else:
            return [self.name]
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert processor to dictionary.
        
        Returns:
            Dict[str, Any]: Processor as dictionary
        """
        processor_dict = {
            'name': self.name,
            'feature_type': self.feature_type.name,
            'encoding': self.encoding.name,
            'missing_strategy': self.missing_strategy.name,
            'imputation_value': str(self.imputation_value) if self.imputation_value is not None else None,
            'reference_date': str(self.reference_date) if self.reference_date is not None else None,
            'min_date': str(self.min_date) if self.min_date is not None else None,
            'max_date': str(self.max_date) if self.max_date is not None else None,
            'date_format': self.date_format,
            'output_dim': self.output_dim,
            'is_fitted': self.is_fitted
        }
        
        return processor_dict
    
    @classmethod
    def from_dict(cls, processor_dict: Dict[str, Any], feature_schema: FeatureSchema,
                 config: DataEncoderConfig) -> 'DatetimeProcessor':
        """
        Create processor from dictionary.
        
        Args:
            processor_dict: Dictionary containing processor parameters
            feature_schema: Schema for the feature
            config: Data encoder configuration
            
        Returns:
            DatetimeProcessor: Processor object
        """
        # Create processor instance
        processor = cls(feature_schema, config)
        
        # Update attributes
        processor.encoding = DateEncoding[processor_dict['encoding']]
        processor.missing_strategy = MissingValueStrategy[processor_dict['missing_strategy']]
        
        # Parse dates
        if processor_dict['imputation_value']:
            processor.imputation_value = pd.Timestamp(processor_dict['imputation_value'])
        
        if processor_dict['reference_date']:
            processor.reference_date = pd.Timestamp(processor_dict['reference_date'])
        
        if processor_dict['min_date']:
            processor.min_date = pd.Timestamp(processor_dict['min_date'])
        
        if processor_dict['max_date']:
            processor.max_date = pd.Timestamp(processor_dict['max_date'])
        
        processor.date_format = processor_dict['date_format']
        processor.output_dim = processor_dict['output_dim']
        processor.is_fitted = processor_dict['is_fitted']
        
        return processor


class BooleanProcessor(FeatureProcessor):
    """
    Processor for boolean features.
    
    This class handles encoding of boolean features.
    """
    
    def __init__(self, feature_schema: FeatureSchema, config: DataEncoderConfig):
        """
        Initialize boolean processor.
        
        Args:
            feature_schema: Schema for the feature
            config: Data encoder configuration
        """
        super().__init__(feature_schema, config)
        
        self.missing_strategy = feature_schema.missing_strategy or MissingValueStrategy.IMPUTE_MODE
        self.imputation_value = feature_schema.imputation_value
        
        # Mapping of values to boolean
        self.true_values = set([
            True, 'true', 'True', 'TRUE', 'yes', 'Yes', 'YES', 'y', 'Y', '1', 1
        ])
        self.false_values = set([
            False, 'false', 'False', 'FALSE', 'no', 'No', 'NO', 'n', 'N', '0', 0
        ])
        
        # Most frequent value for imputation
        self.most_frequent = None
        
        # Set output dimension (always 1 for boolean)
        self.output_dim = 1
        
        logger.debug(f"Initialized BooleanProcessor for feature '{feature_schema.name}'")
    
    def fit(self, data: pd.Series) -> 'BooleanProcessor':
        """
        Fit the processor to the data.
        
        Args:
            data: Feature values
            
        Returns:
            BooleanProcessor: Self
        """
        # Handle empty data
        if data.empty:
            self.is_fitted = True
            return self
            
        # Convert non-boolean values to boolean
        bool_data = data.apply(self._to_boolean)
        
        # Compute most frequent value for imputation
        value_counts = bool_data.value_counts(dropna=False)
        if not value_counts.empty:
            # Get most frequent non-NA value
            na_count = value_counts.get(pd.NA, 0)
            if len(value_counts) > 1 or na_count < len(bool_data):
                self.most_frequent = value_counts.drop(pd.NA, errors='ignore').index[0]
            else:
                # All values are NA, default to False
                self.most_frequent = False
        else:
            # Empty data, default to False
            self.most_frequent = False
            
        # Set imputation value if not already set
        if self.imputation_value is None:
            self.imputation_value = self.most_frequent
            
        self.is_fitted = True
        return self
    
    def _to_boolean(self, value):
        """Convert a value to boolean."""
        if pd.isna(value):
            return pd.NA
        if value in self.true_values:
            return True
        if value in self.false_values:
            return False
        # Try to convert to string and check again
        try:
            str_value = str(value).strip().lower()
            if str_value in ['true', 'yes', 'y', '1']:
                return True
            if str_value in ['false', 'no', 'n', '0']:
                return False
        except (ValueError, TypeError):
            pass
        # Default to NA for unrecognized values
        return pd.NA
    
    def transform(self, data: pd.Series) -> torch.Tensor:
        """
        Transform the data.
        
        Args:
            data: Feature values
            
        Returns:
            torch.Tensor: Transformed values
        """
        if not self.is_fitted:
            raise ValueError("Processor must be fitted before transform")
            
        # Handle empty data
        if data.empty:
            return torch.zeros((len(data), self.output_dim), device=self.config.device)
            
        # Convert to boolean
        bool_data = data.apply(self._to_boolean)
        
        # Handle missing values based on strategy
        if self.missing_strategy == MissingValueStrategy.IMPUTE_MODE:
            bool_data = bool_data.fillna(self.most_frequent)
        elif self.missing_strategy == MissingValueStrategy.IMPUTE_CONSTANT:
            bool_data = bool_data.fillna(self.imputation_value)
        elif self.missing_strategy == MissingValueStrategy.DROP:
            bool_data = bool_data.dropna()
        else:
            # Keep NA values for now
            pass
            
        # Convert NA to a special value
        bool_data = bool_data.fillna(False)
        
        # Convert to binary integers (0 for False, 1 for True)
        int_data = bool_data.astype(int)
        
        # Reshape to 2D and convert to tensor
        result = int_data.values.reshape(-1, 1)
        
        return torch.tensor(result, dtype=torch.float32, device=self.config.device)
    
    def inverse_transform(self, data: torch.Tensor) -> pd.Series:
        """
        Inverse transform the data.
        
        Args:
            data: Transformed values
            
        Returns:
            pd.Series: Original boolean values
        """
        if not self.is_fitted:
            raise ValueError("Processor must be fitted before inverse_transform")
            
        # Convert to numpy and flatten
        values = data.cpu().numpy().ravel()
        
        # Convert to boolean (rounded to handle non-integer values)
        bool_values = (np.round(values) > 0)
        
        return pd.Series(bool_values)
    
    def get_feature_names(self) -> List[str]:
        """
        Get feature names after transformation.
        
        Returns:
            List[str]: Feature names
        """
        return [self.name]
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert processor to dictionary.
        
        Returns:
            Dict[str, Any]: Processor as dictionary
        """
        processor_dict = {
            'name': self.name,
            'feature_type': self.feature_type.name,
            'missing_strategy': self.missing_strategy.name,
            'imputation_value': self.imputation_value,
            'most_frequent': self.most_frequent,
            'output_dim': self.output_dim,
            'is_fitted': self.is_fitted
        }
        
        return processor_dict
    
    @classmethod
    def from_dict(cls, processor_dict: Dict[str, Any], feature_schema: FeatureSchema,
                 config: DataEncoderConfig) -> 'BooleanProcessor':
        """
        Create processor from dictionary.
        
        Args:
            processor_dict: Dictionary containing processor parameters
            feature_schema: Schema for the feature
            config: Data encoder configuration
            
        Returns:
            BooleanProcessor: Processor object
        """
        # Create processor instance
        processor = cls(feature_schema, config)
        
        # Update attributes
        processor.missing_strategy = MissingValueStrategy[processor_dict['missing_strategy']]
        processor.imputation_value = processor_dict['imputation_value']
        processor.most_frequent = processor_dict['most_frequent']
        processor.output_dim = processor_dict['output_dim']
        processor.is_fitted = processor_dict['is_fitted']
        
        return processor


class TextProcessor(FeatureProcessor):
    """
    Processor for text features.
    
    This class handles encoding of text features.
    """
    
    def __init__(self, feature_schema: FeatureSchema, config: DataEncoderConfig):
        """
        Initialize text processor.
        
        Args:
            feature_schema: Schema for the feature
            config: Data encoder configuration
        """
        super().__init__(feature_schema, config)
        
        self.missing_strategy = feature_schema.missing_strategy or MissingValueStrategy.IMPUTE_CONSTANT
        self.imputation_value = feature_schema.imputation_value or ""
        self.text_handling = config.text_features_handling or "simple"
        
        # Set output dimension based on text handling
        self.output_dim = self._get_output_dim_for_handling()
        
        # Initialize text encoder
        self.encoder = None
        
        logger.debug(f"Initialized TextProcessor for feature '{feature_schema.name}'")
    
    def _get_output_dim_for_handling(self):
        """Get output dimension based on text handling."""
        # For now, all text encoding methods result in a single vector
        return 1
    
    def _create_encoder(self):
        """Create encoder based on text handling."""
        if self.text_handling == "simple":
            # Simple bag-of-words encoding or token counts
            if _HAS_SKLEARN:
                from sklearn.feature_extraction.text import CountVectorizer
                return CountVectorizer(max_features=100, binary=True)
            else:
                # Simple implementation of bag-of-words
                def bow_encoder(texts):
                    # Tokenize and count
                    vocab = set()
                    for text in texts:
                        if isinstance(text, str):
                            words = text.lower().split()
                            vocab.update(words)
                    vocab = list(vocab)[:100]  # Limit to 100 features
                    
                    # Create feature vectors
                    result = np.zeros((len(texts), len(vocab)))
                    for i, text in enumerate(texts):
                        if isinstance(text, str):
                            words = text.lower().split()
                            for j, word in enumerate(vocab):
                                result[i, j] = 1 if word in words else 0
                    return result
                return bow_encoder
                
        elif self.text_handling == "nlp":
            # Try to use more advanced NLP methods if available
            if _HAS_TRANSFORMERS:
                from transformers import AutoTokenizer, AutoModel
                
                # Use a small pre-trained model
                model_name = "distilbert-base-uncased"
                
                # Create simple text encoder class
                class TransformerTextEncoder:
                    def __init__(self, model_name):
                        self.tokenizer = AutoTokenizer.from_pretrained(model_name)
                        self.model = AutoModel.from_pretrained(model_name)
                        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
                        self.model.to(self.device)
                        self.model.eval()
                        
                    def encode(self, texts):
                        # Tokenize texts
                        tokens = self.tokenizer(
                            texts,
                            padding=True,
                            truncation=True,
                            return_tensors="pt"
                        ).to(self.device)
                        
                        # Generate embeddings
                        with torch.no_grad():
                            outputs = self.model(**tokens)
                            
                        # Use CLS token embedding
                        embeddings = outputs.last_hidden_state[:, 0, :]
                        
                        return embeddings.cpu().numpy()
                
                return TransformerTextEncoder(model_name)
            elif _HAS_SKLEARN:
                from sklearn.feature_extraction.text import TfidfVectorizer
                return TfidfVectorizer(max_features=100)
            else:
                # Fall back to simple bag-of-words
                return self._create_encoder()
                
        elif self.text_handling == "ignore":
            # Just return a dummy encoder that creates a zero vector
            def dummy_encoder(texts):
                return np.zeros((len(texts), 1))
            return dummy_encoder
            
        else:
            # Default to simple bag-of-words
            return self._create_encoder()
    
    def fit(self, data: pd.Series) -> 'TextProcessor':
        """
        Fit the processor to the data.
        
        Args:
            data: Feature values
            
        Returns:
            TextProcessor: Self
        """
        # Handle empty data
        if data.empty:
            self.is_fitted = True
            return self
            
        # Convert non-string values to strings
        text_data = data.fillna(self.imputation_value).astype(str)
        
        # Create and fit encoder
        self.encoder = self._create_encoder()
        
        # Fit encoder if it has a fit method
        if hasattr(self.encoder, 'fit'):
            self.encoder.fit(text_data)
        elif hasattr(self.encoder, 'fit_transform'):
            # If encoder has fit_transform but not fit
            self.encoder.fit_transform(text_data)
            
        # Update output dimension based on encoder
        if hasattr(self.encoder, 'get_feature_names_out'):
            self.output_dim = len(self.encoder.get_feature_names_out())
        elif _HAS_TRANSFORMERS and hasattr(self.encoder, 'model'):
            # For transformer models, get output dimension from model
            self.output_dim = self.encoder.model.config.hidden_size
            
        self.is_fitted = True
        return self
    
    def transform(self, data: pd.Series) -> torch.Tensor:
        """
        Transform the data.
        
        Args:
            data: Feature values
            
        Returns:
            torch.Tensor: Transformed values
        """
        if not self.is_fitted:
            raise ValueError("Processor must be fitted before transform")
            
        # Handle empty data
        if data.empty:
            return torch.zeros((len(data), self.output_dim), device=self.config.device)
            
        # Handle missing values based on strategy
        if self.missing_strategy == MissingValueStrategy.IMPUTE_CONSTANT:
            text_data = data.fillna(self.imputation_value)
        elif self.missing_strategy == MissingValueStrategy.DROP:
            text_data = data.dropna()
        else:
            # Fill missing values with empty string
            text_data = data.fillna("")
            
        # Convert all values to strings
        text_data = text_data.astype(str)
        
        # Apply encoder
        if hasattr(self.encoder, 'transform'):
            encoded = self.encoder.transform(text_data)
            
            # If output is sparse, convert to dense
            if hasattr(encoded, 'toarray'):
                encoded = encoded.toarray()
        elif hasattr(self.encoder, 'encode'):
            encoded = self.encoder.encode(text_data)
        else:
            # Use encoder function directly
            encoded = self.encoder(text_data)
            
        # Convert to tensor
        result = torch.tensor(encoded, dtype=torch.float32, device=self.config.device)
        
        return result
    
    def inverse_transform(self, data: torch.Tensor) -> pd.Series:
        """
        Inverse transform the data.
        
        Args:
            data: Transformed values
            
        Returns:
            pd.Series: Original text values
        """
        if not self.is_fitted:
            raise ValueError("Processor must be fitted before inverse_transform")
            
        # For text encoders, inverse transform is generally not well-defined
        # One approach is to find the most similar texts in the training set
        # But for simplicity, we'll just return placeholders
        
        return pd.Series(["[TEXT]"] * len(data))
    
    def get_feature_names(self) -> List[str]:
        """
        Get feature names after transformation.
        
        Returns:
            List[str]: Feature names
        """
        if hasattr(self.encoder, 'get_feature_names_out'):
            return self.encoder.get_feature_names_out([self.name])
        elif self.output_dim > 1:
            return [f"{self.name}_{i}" for i in range(self.output_dim)]
        else:
            return [self.name]
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert processor to dictionary.
        
        Returns:
            Dict[str, Any]: Processor as dictionary
        """
        processor_dict = {
            'name': self.name,
            'feature_type': self.feature_type.name,
            'missing_strategy': self.missing_strategy.name,
            'imputation_value': self.imputation_value,
            'text_handling': self.text_handling,
            'output_dim': self.output_dim,
            'is_fitted': self.is_fitted
        }
        
        return processor_dict
    
    @classmethod
    def from_dict(cls, processor_dict: Dict[str, Any], feature_schema: FeatureSchema,
                 config: DataEncoderConfig) -> 'TextProcessor':
        """
        Create processor from dictionary.
        
        Args:
            processor_dict: Dictionary containing processor parameters
            feature_schema: Schema for the feature
            config: Data encoder configuration
            
        Returns:
            TextProcessor: Processor object
        """
        # Create processor instance
        processor = cls(feature_schema, config)
        
        # Update attributes
        processor.missing_strategy = MissingValueStrategy[processor_dict['missing_strategy']]
        processor.imputation_value = processor_dict['imputation_value']
        processor.text_handling = processor_dict['text_handling']
        processor.output_dim = processor_dict['output_dim']
        processor.is_fitted = processor_dict['is_fitted']
        
        # Recreate encoder
        processor.encoder = processor._create_encoder()
        
        return processor


class FeatureSelector:
    """
    Feature selection for structured data.
    
    This class provides methods for selecting relevant features
    based on various criteria (mutual information, correlation, etc.).
    """
    
    def __init__(
        self,
        config: DataEncoderConfig,
        method: str = 'mutual_info',
        threshold: float = 0.01,
        k: Optional[int] = None,
        target_column: Optional[str] = None
    ):
        """
        Initialize feature selector.
        
        Args:
            config: Data encoder configuration
            method: Feature selection method
            threshold: Threshold for feature importance
            k: Number of top features to select
            target_column: Target column for supervised methods
        """
        self.config = config
        self.method = method
        self.threshold = threshold
        self.k = k
        self.target_column = target_column or config.target_column
        
        # Feature importances
        self.feature_importances = {}
        
        logger.info(f"Initialized FeatureSelector with method={method}")
    
    def fit(self, data: pd.DataFrame, schema: DataSchema) -> 'FeatureSelector':
        """
        Fit the feature selector to the data.
        
        Args:
            data: Input data
            schema: Data schema
            
        Returns:
            FeatureSelector: Self
        """
        if self.method == 'mutual_info':
            self._fit_mutual_info(data, schema)
        elif self.method == 'correlation':
            self._fit_correlation(data, schema)
        elif self.method == 'variance':
            self._fit_variance(data, schema)
        elif self.method == 'model_based':
            self._fit_model_based(data, schema)
        else:
            logger.warning(f"Unknown feature selection method: {self.method}. No features will be selected.")
            
        return self
    
    def _fit_mutual_info(self, data: pd.DataFrame, schema: DataSchema) -> None:
        """
        Select features based on mutual information with target.
        
        Args:
            data: Input data
            schema: Data schema
        """
        if not _HAS_SKLEARN:
            logger.warning("Scikit-learn is required for mutual information feature selection")
            return
            
        if not self.target_column or self.target_column not in data.columns:
            logger.warning("Target column not found. Cannot compute mutual information.")
            return
            
        # Get target
        y = data[self.target_column]
        
        # Compute mutual information for each feature
        feature_importances = {}
        
        for feature in schema.features:
            # Skip target column
            if feature.name == self.target_column:
                continue
                
            # Get feature values
            X = data[feature.name]
            
            try:
                # Handle different feature types
                if feature.feature_type in [FeatureType.NUMERICAL_CONTINUOUS, FeatureType.NUMERICAL_DISCRETE]:
                    # Convert to numeric
                    X = pd.to_numeric(X, errors='coerce')
                    
                    # Fill missing values
                    X = X.fillna(X.mean() if not pd.isna(X.mean()) else 0)
                    
                    # Reshape for scikit-learn
                    X = X.values.reshape(-1, 1)
                    
                    # Compute mutual information
                    if y.dtype == bool or pd.api.types.is_categorical_dtype(y.dtype) or len(y.unique()) < 10:
                        # Classification target
                        mi = mutual_info_classif(X, y, discrete_features=feature.feature_type == FeatureType.NUMERICAL_DISCRETE)
                    else:
                        # Regression target
                        mi = mutual_info_regression(X, y, discrete_features=feature.feature_type == FeatureType.NUMERICAL_DISCRETE)
                        
                    feature_importances[feature.name] = mi[0]
                elif feature.feature_type in [FeatureType.CATEGORICAL_NOMINAL, FeatureType.CATEGORICAL_ORDINAL]:
                    # One-hot encode categorical features
                    X_encoded = pd.get_dummies(X, prefix=feature.name, prefix_sep='__')
                    
                    # Fill missing values
                    X_encoded = X_encoded.fillna(0)
                    
                    # Compute mutual information
                    if y.dtype == bool or pd.api.types.is_categorical_dtype(y.dtype) or len(y.unique()) < 10:
                        # Classification target
                        mi = mutual_info_classif(X_encoded, y, discrete_features=True)
                    else:
                        # Regression target
                        mi = mutual_info_regression(X_encoded, y, discrete_features=True)
                        
                    # Use maximum mutual information across one-hot features
                    feature_importances[feature.name] = mi.max()
                else:
                    # Skip other feature types
                    continue
            except Exception as e:
                logger.warning(f"Error computing mutual information for feature {feature.name}: {str(e)}")
                continue
                
        # Normalize feature importances
        if feature_importances:
            max_importance = max(feature_importances.values())
            if max_importance > 0:
                feature_importances = {k: v / max_importance for k, v in feature_importances.items()}
                
        self.feature_importances = feature_importances
    
    def _fit_correlation(self, data: pd.DataFrame, schema: DataSchema) -> None:
        """
        Select features based on correlation with target.
        
        Args:
            data: Input data
            schema: Data schema
        """
        if not self.target_column or self.target_column not in data.columns:
            logger.warning("Target column not found. Cannot compute correlations.")
            return
            
        # Get target
        y = data[self.target_column]
        
        # Check if target is numeric
        try:
            y = pd.to_numeric(y, errors='raise')
        except (ValueError, TypeError):
            logger.warning("Target column is not numeric. Cannot compute correlations.")
            return
            
        # Compute correlation for each feature
        feature_importances = {}
        
        for feature in schema.features:
            # Skip target column
            if feature.name == self.target_column:
                continue
                
            # Get feature values
            X = data[feature.name]
            
            try:
                # Handle different feature types
                if feature.feature_type in [FeatureType.NUMERICAL_CONTINUOUS, FeatureType.NUMERICAL_DISCRETE]:
                    # Convert to numeric
                    X = pd.to_numeric(X, errors='coerce')
                    
                    # Compute correlation
                    corr = X.corr(y)
                    
                    # Use absolute correlation
                    feature_importances[feature.name] = abs(corr) if not pd.isna(corr) else 0
                elif feature.feature_type in [FeatureType.CATEGORICAL_NOMINAL, FeatureType.CATEGORICAL_ORDINAL]:
                    # For categorical features, use correlation ratio (more appropriate than Pearson)
                    try:
                        # Compute correlation ratio (eta squared)
                        categories = X.dropna().unique()
                        means = []
                        counts = []
                        
                        for category in categories:
                            category_y = y[X == category]
                            if not category_y.empty:
                                means.append(category_y.mean())
                                counts.append(len(category_y))
                                
                        if means and sum(counts) > 0:
                            # Weighted mean
                            overall_mean = sum(m * c for m, c in zip(means, counts)) / sum(counts)
                            
                            # Between-group variability
                            bgv = sum(c * ((m - overall_mean) ** 2) for m, c in zip(means, counts))
                            
                            # Total variability
                            tv = ((y - y.mean()) ** 2).sum()
                            
                            # Correlation ratio
                            if tv > 0:
                                eta_squared = bgv / tv
                                feature_importances[feature.name] = eta_squared
                            else:
                                feature_importances[feature.name] = 0
                        else:
                            feature_importances[feature.name] = 0
                    except Exception as e:
                        logger.warning(f"Error computing correlation ratio for feature {feature.name}: {str(e)}")
                        feature_importances[feature.name] = 0
                else:
                    # Skip other feature types
                    continue
            except Exception as e:
                logger.warning(f"Error computing correlation for feature {feature.name}: {str(e)}")
                continue
                
        # Normalize feature importances
        if feature_importances:
            max_importance = max(feature_importances.values())
            if max_importance > 0:
                feature_importances = {k: v / max_importance for k, v in feature_importances.items()}
                
        self.feature_importances = feature_importances
    
    def _fit_variance(self, data: pd.DataFrame, schema: DataSchema) -> None:
        """
        Select features based on variance.
        
        Args:
            data: Input data
            schema: Data schema
        """
        # Compute variance for each feature
        feature_importances = {}
        
        for feature in schema.features:
            # Get feature values
            X = data[feature.name]
            
            try:
                # Handle different feature types
                if feature.feature_type in [FeatureType.NUMERICAL_CONTINUOUS, FeatureType.NUMERICAL_DISCRETE]:
                    # Convert to numeric
                    X = pd.to_numeric(X, errors='coerce')
                    
                    # Compute variance
                    var = X.var()
                    
                    feature_importances[feature.name] = var if not pd.isna(var) else 0
                elif feature.feature_type in [FeatureType.CATEGORICAL_NOMINAL, FeatureType.CATEGORICAL_ORDINAL]:
                    # For categorical features, use entropy
                    value_counts = X.value_counts(normalize=True, dropna=True)
                    
                    if not value_counts.empty:
                        # Compute entropy
                        entropy = -sum(p * np.log2(p) for p in value_counts)
                        
                        # Normalize by log2 of number of categories
                        max_entropy = np.log2(len(value_counts))
                        
                        if max_entropy > 0:
                            normalized_entropy = entropy / max_entropy
                        else:
                            normalized_entropy = 0
                            
                        feature_importances[feature.name] = normalized_entropy
                    else:
                        feature_importances[feature.name] = 0
                else:
                    # Skip other feature types
                    continue
            except Exception as e:
                logger.warning(f"Error computing variance for feature {feature.name}: {str(e)}")
                continue
                
        # Normalize feature importances
        if feature_importances:
            max_importance = max(feature_importances.values())
            if max_importance > 0:
                feature_importances = {k: v / max_importance for k, v in feature_importances.items()}
                
        self.feature_importances = feature_importances
    
    def _fit_model_based(self, data: pd.DataFrame, schema: DataSchema) -> None:
        """
        Select features based on model-based importance.
        
        Args:
            data: Input data
            schema: Data schema
        """
        if not _HAS_SKLEARN:
            logger.warning("Scikit-learn is required for model-based feature selection")
            return
            
        if not self.target_column or self.target_column not in data.columns:
            logger.warning("Target column not found. Cannot compute model-based importance.")
            return
            
        try:
            from sklearn.ensemble import RandomForestClassifier, RandomForestRegressor
            from sklearn.preprocessing import LabelEncoder
            
            # Get target
            y = data[self.target_column]
            
            # Check if classification or regression
            is_classification = y.dtype == bool or pd.api.types.is_categorical_dtype(y.dtype) or len(y.unique()) < 10
            
            # Encode target if necessary for classification
            if is_classification:
                le = LabelEncoder()
                y = le.fit_transform(y)
                
            # Prepare feature matrix
            feature_names = []
            feature_arrays = []
            
            for feature in schema.features:
                # Skip target column
                if feature.name == self.target_column:
                    continue
                    
                # Get feature values
                X = data[feature.name]
                
                try:
                    # Handle different feature types
                    if feature.feature_type in [FeatureType.NUMERICAL_CONTINUOUS, FeatureType.NUMERICAL_DISCRETE]:
                        # Convert to numeric
                        X = pd.to_numeric(X, errors='coerce')
                        
                        # Fill missing values
                        X = X.fillna(X.mean() if not pd.isna(X.mean()) else 0)
                        
                        # Add to feature matrix
                        feature_names.append(feature.name)
                        feature_arrays.append(X.values)
                    elif feature.feature_type in [FeatureType.CATEGORICAL_NOMINAL, FeatureType.CATEGORICAL_ORDINAL]:
                        # One-hot encode categorical features
                        X_encoded = pd.get_dummies(X, prefix=feature.name, prefix_sep='__')
                        
                        # Fill missing values
                        X_encoded = X_encoded.fillna(0)
                        
                        # Add to feature matrix
                        for col in X_encoded.columns:
                            feature_names.append(col)
                            feature_arrays.append(X_encoded[col].values)
                    else:
                        # Skip other feature types
                        continue
                except Exception as e:
                    logger.warning(f"Error preprocessing feature {feature.name}: {str(e)}")
                    continue
                    
            # Skip if no features
            if not feature_arrays:
                logger.warning("No features available for model-based importance")
                return
                
            # Convert to feature matrix
            X = np.column_stack(feature_arrays)
            
            # Train model
            if is_classification:
                model = RandomForestClassifier(n_estimators=100, random_state=self.config.random_state)
            else:
                model = RandomForestRegressor(n_estimators=100, random_state=self.config.random_state)
                
            model.fit(X, y)
            
            # Get feature importances
            importances = model.feature_importances_
            
            # Map back to original features
            feature_importances = defaultdict(float)
            
            for name, importance in zip(feature_names, importances):
                # Extract original feature name
                if '__' in name:
                    original_name = name.split('__')[0]
                else:
                    original_name = name
                    
                # Sum importance for one-hot encoded features
                feature_importances[original_name] += importance
                
            # Normalize feature importances
            if feature_importances:
                max_importance = max(feature_importances.values())
                if max_importance > 0:
                    feature_importances = {k: v / max_importance for k, v in feature_importances.items()}
                    
            self.feature_importances = dict(feature_importances)
        except Exception as e:
            logger.error(f"Error in model-based feature selection: {str(e)}")
            self.feature_importances = {}
    
    def select_features(self, schema: DataSchema) -> DataSchema:
        """
        Select features based on importance.
        
        Args:
            schema: Data schema
            
        Returns:
            DataSchema: Schema with selected features
        """
        # If no feature importances, return original schema
        if not self.feature_importances:
            return schema
            
        # Select features based on threshold or top k
        selected_features = set()
        
        if self.k is not None:
            # Select top k features
            sorted_features = sorted(self.feature_importances.items(), key=lambda x: x[1], reverse=True)
            selected_features = {name for name, _ in sorted_features[:self.k]}
        else:
            # Select features above threshold
            selected_features = {name for name, importance in self.feature_importances.items() if importance >= self.threshold}
            
        # Add target column if available
        if self.target_column:
            selected_features.add(self.target_column)
            
        # Create new schema with selected features
        new_schema = DataSchema()
        
        for feature in schema.features:
            if feature.name in selected_features:
                new_schema.add_feature(feature)
                
        # Copy other schema attributes
        new_schema.index_column = schema.index_column
        new_schema.target_column = schema.target_column
        new_schema.timestamp_column = schema.timestamp_column
        
        # Update feature type lists
        new_schema.categorical_columns = [col for col in schema.categorical_columns if col in selected_features]
        new_schema.numerical_columns = [col for col in schema.numerical_columns if col in selected_features]
        new_schema.datetime_columns = [col for col in schema.datetime_columns if col in selected_features]
        new_schema.text_columns = [col for col in schema.text_columns if col in selected_features]
        new_schema.boolean_columns = [col for col in schema.boolean_columns if col in selected_features]
        
        # Update feature groups
        new_schema.feature_groups = {
            group: [col for col in columns if col in selected_features]
            for group, columns in schema.feature_groups.items()
        }
        
        # Remove empty feature groups
        new_schema.feature_groups = {
            group: columns for group, columns in new_schema.feature_groups.items()
            if columns
        }
        
        # Update feature relationships
        new_schema.feature_relationships = {
            relation: [
                (feat1, feat2) for feat1, feat2 in relationships
                if feat1 in selected_features and feat2 in selected_features
            ]
            for relation, relationships in schema.feature_relationships.items()
        }
        
        # Remove empty relationship types
        new_schema.feature_relationships = {
            relation: relationships for relation, relationships in new_schema.feature_relationships.items()
            if relationships
        }
        
        # Update dataset statistics
        new_schema.dataset_statistics = schema.dataset_statistics.copy()
        new_schema.dataset_statistics.update({
            'n_columns': len(new_schema.features),
            'n_categorical': len(new_schema.categorical_columns),
            'n_numerical': len(new_schema.numerical_columns),
            'n_datetime': len(new_schema.datetime_columns),
            'n_text': len(new_schema.text_columns),
            'n_boolean': len(new_schema.boolean_columns)
        })
        
        # Add feature importance metadata
        new_schema.metadata = schema.metadata.copy()
        new_schema.metadata.update({
            'feature_selection_method': self.method,
            'feature_selection_threshold': self.threshold,
            'feature_selection_k': self.k,
            'feature_importances': self.feature_importances
        })
        
        logger.info(f"Selected {len(new_schema.features)} features out of {len(schema.features)}")
        
        return new_schema
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert feature selector to dictionary.
        
        Returns:
            Dict[str, Any]: Feature selector as dictionary
        """
        selector_dict = {
            'method': self.method,
            'threshold': self.threshold,
            'k': self.k,
            'target_column': self.target_column,
            'feature_importances': self.feature_importances
        }
        
        return selector_dict
    
    @classmethod
    def from_dict(cls, selector_dict: Dict[str, Any], config: DataEncoderConfig) -> 'FeatureSelector':
        """
        Create feature selector from dictionary.
        
        Args:
            selector_dict: Dictionary containing feature selector parameters
            config: Data encoder configuration
            
        Returns:
            FeatureSelector: Feature selector object
        """
        # Create feature selector instance
        selector = cls(
            config=config,
            method=selector_dict['method'],
            threshold=selector_dict['threshold'],
            k=selector_dict['k'],
            target_column=selector_dict['target_column']
        )
        
        # Update feature importances
        selector.feature_importances = selector_dict['feature_importances']
        
        return selector


class DataEncoder:
    """
    Encoder for structured data.
    
    This class provides methods for encoding structured data into
    neural representations suitable for further processing.
    """
    
    def __init__(self, config: Optional[Union[DataEncoderConfig, dict]] = None):
        """
        Initialize data encoder.
        
        Args:
            config: Configuration for the encoder
        """
        # Handle configuration
        if config is None:
            self.config = DataEncoderConfig()
        elif isinstance(config, dict):
            self.config = DataEncoderConfig(**config)
        else:
            self.config = config
            
        # Set device
        self.device = self.config.device
        
        # Initialize schema
        self.schema = None
        
        # Initialize schema inferrer
        self.schema_inferrer = SchemaInferrer(self.config)
        
        # Initialize feature processors
        self.feature_processors = {}
        
        # Initialize feature selector
        if self.config.feature_selection:
            self.feature_selector = FeatureSelector(
                config=self.config,
                method=self.config.feature_selection
            )
        else:
            self.feature_selector = None
            
        # Cache for encodings
        self.cache_enabled = self.config.cache_size > 0
        self.cache = OrderedDict()
        self.cache_size = self.config.cache_size
        
        # Projection layer for final embedding dimension
        self.projection = None
        
        # Register encoder
        self.is_fitted = False
        
        logger.info(f"Initialized DataEncoder with embedding_dim={self.config.embedding_dim}")
    
    def _compute_hash(self, input_data: Any) -> str:
        """
        Compute a hash for cache lookup.
        
        Args:
            input_data: Input data to be hashed
            
        Returns:
            str: Hash string
        """
        import hashlib
        
        try:
            # If data is a DataFrame or Series, use its hash
            if hasattr(input_data, 'hash'):
                return str(input_data.hash())
                
            # For primitive types, use direct hashing
            if isinstance(input_data, (str, int, float, bool)):
                return hashlib.sha256(str(input_data).encode('utf-8')).hexdigest()
                
            # Try to pickle and hash more complex data
            data_bytes = pickle.dumps(input_data)
            return hashlib.sha256(data_bytes).hexdigest()
        except Exception as e:
            logger.warning(f"Failed to hash input for caching: {str(e)}")
            return None
    
    def _manage_cache_size(self):
        """Ensure cache doesn't exceed maximum size."""
        if len(self.cache) > self.cache_size:
            # Remove oldest items (OrderedDict preserves insertion order)
            for _ in range(len(self.cache) - self.cache_size):
                self.cache.popitem(last=False)
    
    def fit(self, data: Union[pd.DataFrame, Dict[str, List], List[Dict]]) -> 'DataEncoder':
        """
        Fit the encoder to the data.
        
        Args:
            data: Input data
            
        Returns:
            DataEncoder: Self
        """
        # Convert data to pandas DataFrame if needed
        if not isinstance(data, pd.DataFrame):
            if isinstance(data, dict):
                # Convert dict of lists to DataFrame
                data = pd.DataFrame(data)
            elif isinstance(data, list) and data and isinstance(data[0], dict):
                # Convert list of dicts to DataFrame
                data = pd.DataFrame(data)
            else:
                raise ValueError("Data must be a pandas DataFrame, dict of lists, or list of dicts")
                
        # Infer schema if not already set
        if self.schema is None:
            self.schema = self.schema_inferrer.infer_schema(data)
            
        # Apply feature selection if enabled
        if self.feature_selector is not None:
            self.feature_selector.fit(data, self.schema)
            self.schema = self.feature_selector.select_features(self.schema)
            
        # Create and fit feature processors
        for feature in self.schema.features:
            processor = self._create_processor(feature)
            if processor:
                # Get feature data
                feature_data = data[feature.name]
                
                # Fit processor
                processor.fit(feature_data)
                
                # Add to feature processors
                self.feature_processors[feature.name] = processor
                
        # Create projection layer if needed
        input_dim = sum(processor.get_output_dim() for processor in self.feature_processors.values())
        
        if self.config.embedding_dim != input_dim:
            self.projection = nn.Linear(input_dim, self.config.embedding_dim).to(self.device)
            # Initialize projection weights
            nn.init.xavier_uniform_(self.projection.weight)
            nn.init.zeros_(self.projection.bias)
            
        self.is_fitted = True
        
        return self
    
    def _create_processor(self, feature: FeatureSchema) -> Optional[FeatureProcessor]:
        """
        Create processor for a feature.
        
        Args:
            feature: Feature schema
            
        Returns:
            Optional[FeatureProcessor]: Feature processor or None
        """
        # Create processor based on feature type
        if feature.feature_type in [FeatureType.NUMERICAL_CONTINUOUS, FeatureType.NUMERICAL_DISCRETE]:
            return NumericalProcessor(feature, self.config)
        elif feature.feature_type in [FeatureType.CATEGORICAL_NOMINAL, FeatureType.CATEGORICAL_ORDINAL]:
            return CategoricalProcessor(feature, self.config)
        elif feature.feature_type == FeatureType.DATETIME:
            return DatetimeProcessor(feature, self.config)
        elif feature.feature_type == FeatureType.BOOLEAN:
            return BooleanProcessor(feature, self.config)
        elif feature.feature_type == FeatureType.TEXT:
            return TextProcessor(feature, self.config)
        else:
            # Unknown feature type
            logger.warning(f"Unsupported feature type: {feature.feature_type}")
            return None
    
    def transform(self, data: Union[pd.DataFrame, Dict[str, List], List[Dict]]) -> torch.Tensor:
        """
        Transform data into embeddings.
        
        Args:
            data: Input data
            
        Returns:
            torch.Tensor: Transformed embeddings
        """
        if not self.is_fitted:
            raise ValueError("Encoder must be fitted before transform")
            
        # Check cache if enabled
        if self.cache_enabled:
            cache_key = self._compute_hash(data)
            if cache_key in self.cache:
                return self.cache[cache_key]
                
        # Convert data to pandas DataFrame if needed
        if not isinstance(data, pd.DataFrame):
            if isinstance(data, dict):
                # Convert dict of lists to DataFrame
                data = pd.DataFrame(data)
            elif isinstance(data, list) and data and isinstance(data[0], dict):
                # Convert list of dicts to DataFrame
                data = pd.DataFrame(data)
            else:
                raise ValueError("Data must be a pandas DataFrame, dict of lists, or list of dicts")
                
        # Handle schema changes during inference
        if self.config.schema_change_policy == SchemaChangePolicy.ERROR:
            # Check if any columns are missing
            missing_columns = [col for col in self.feature_processors if col not in data.columns]
            if missing_columns:
                raise ValueError(f"Columns missing in input data: {missing_columns}")
        elif self.config.schema_change_policy == SchemaChangePolicy.IGNORE_NEW:
            # Ignore new columns, but ensure all required columns are present
            missing_columns = [col for col in self.feature_processors if col not in data.columns]
            if missing_columns:
                raise ValueError(f"Columns missing in input data: {missing_columns}")
        elif self.config.schema_change_policy == SchemaChangePolicy.IMPUTE_NEW:
            # Add missing columns with default values
            for col in self.feature_processors:
                if col not in data.columns:
                    # Get processor
                    processor = self.feature_processors[col]
                    
                    # Add column with default value
                    if isinstance(processor, NumericalProcessor):
                        data[col] = 0.0
                    elif isinstance(processor, CategoricalProcessor):
                        data[col] = 'unknown'
                    elif isinstance(processor, DatetimeProcessor):
                        data[col] = pd.NaT
                    elif isinstance(processor, BooleanProcessor):
                        data[col] = False
                    elif isinstance(processor, TextProcessor):
                        data[col] = ''
                    else:
                        data[col] = None
                        
        # Transform each feature
        feature_embeddings = []
        
        for feature_name, processor in self.feature_processors.items():
            # Skip if feature is missing
            if feature_name not in data.columns:
                continue
                
            # Get feature data
            feature_data = data[feature_name]
            
            # Transform feature
            feature_embedding = processor.transform(feature_data)
            
            # Add to feature embeddings
            feature_embeddings.append(feature_embedding)
            
        # Concatenate feature embeddings
        if not feature_embeddings:
            # No features to encode, return zero vector
            return torch.zeros((len(data), self.config.embedding_dim), device=self.device)
            
        embeddings = torch.cat(feature_embeddings, dim=1)
        
        # Apply projection if needed
        if self.projection is not None:
            embeddings = self.projection(embeddings)
            
        # Apply normalization if configured
        if self.config.normalize_embeddings:
            embeddings = F.normalize(embeddings, p=2, dim=1)
            
        # Cache result if enabled
        if self.cache_enabled and cache_key:
            self.cache[cache_key] = embeddings
            self._manage_cache_size()
            
        return embeddings
    
    def fit_transform(self, data: Union[pd.DataFrame, Dict[str, List], List[Dict]]) -> torch.Tensor:
        """
        Fit encoder and transform data.
        
        Args:
            data: Input data
            
        Returns:
            torch.Tensor: Transformed embeddings
        """
        self.fit(data)
        return self.transform(data)
    
    def transform_feature(self, feature_name: str, data: pd.Series) -> torch.Tensor:
        """
        Transform a single feature.
        
        Args:
            feature_name: Name of the feature
            data: Feature values
            
        Returns:
            torch.Tensor: Transformed feature
        """
        if not self.is_fitted:
            raise ValueError("Encoder must be fitted before transform_feature")
            
        # Check if feature exists
        if feature_name not in self.feature_processors:
            raise ValueError(f"Feature '{feature_name}' not found in encoder")
            
        # Get processor
        processor = self.feature_processors[feature_name]
        
        # Transform feature
        return processor.transform(data)
    
    def inverse_transform(self, embeddings: torch.Tensor) -> pd.DataFrame:
        """
        Inverse transform embeddings to original data.
        
        Note: This is an approximation, as information is lost during encoding.
        
        Args:
            embeddings: Encoded embeddings
            
        Returns:
            pd.DataFrame: Approximation of original data
        """
        if not self.is_fitted:
            raise ValueError("Encoder must be fitted before inverse_transform")
            
        # If projection is used, inverse projection is approximate
        if self.projection is not None:
            # Try to invert projection using pseudo-inverse
            with torch.no_grad():
                pseudo_inv = torch.pinverse(self.projection.weight)
                # Apply inverse projection
                feature_embeddings = torch.matmul(embeddings, pseudo_inv)
        else:
            # No projection, use embeddings directly
            feature_embeddings = embeddings
            
        # Current position in the embedding
        pos = 0
        
        # Reconstruct each feature
        result = {}
        
        for feature_name, processor in self.feature_processors.items():
            # Get output dimension for this feature
            output_dim = processor.get_output_dim()
            
            # Extract feature embedding
            if pos + output_dim <= feature_embeddings.size(1):
                feature_embedding = feature_embeddings[:, pos:pos+output_dim]
                
                # Inverse transform feature
                try:
                    feature_data = processor.inverse_transform(feature_embedding)
                    result[feature_name] = feature_data
                except Exception as e:
                    logger.warning(f"Error inverse transforming feature {feature_name}: {str(e)}")
                    # Use placeholder
                    result[feature_name] = pd.Series([""] * len(embeddings))
                    
                # Update position
                pos += output_dim
            else:
                logger.warning(f"Insufficient dimensions in embedding for feature {feature_name}")
                # Use placeholder
                result[feature_name] = pd.Series([""] * len(embeddings))
                
        return pd.DataFrame(result)
    
    def encode_batch(self, data: Union[pd.DataFrame, List[Dict]], batch_size: int = 32) -> torch.Tensor:
        """
        Encode data in batches.
        
        Args:
            data: Input data
            batch_size: Batch size
            
        Returns:
            torch.Tensor: Encoded embeddings
        """
        if not self.is_fitted:
            raise ValueError("Encoder must be fitted before encode_batch")
            
        # Convert data to pandas DataFrame if needed
        if not isinstance(data, pd.DataFrame):
            if isinstance(data, list) and data and isinstance(data[0], dict):
                # Convert list of dicts to DataFrame
                data = pd.DataFrame(data)
            else:
                raise ValueError("Data must be a pandas DataFrame or list of dicts")
                
        # Initialize result
        result = []
        
        # Process in batches
        for i in range(0, len(data), batch_size):
            # Get batch
            batch = data.iloc[i:i+batch_size]
            
            # Transform batch
            batch_embeddings = self.transform(batch)
            
            # Add to result
            result.append(batch_embeddings)
            
        # Concatenate batches
        if result:
            return torch.cat(result, dim=0)
        else:
            return torch.zeros((0, self.config.embedding_dim), device=self.device)
    
    def encode_streaming(self, data_stream: Iterator, batch_size: int = 32) -> Iterator[torch.Tensor]:
        """
        Encode a stream of data.
        
        Args:
            data_stream: Iterator of data chunks
            batch_size: Batch size
            
        Returns:
            Iterator[torch.Tensor]: Iterator of encoded embeddings
        """
        if not self.is_fitted:
            raise ValueError("Encoder must be fitted before encode_streaming")
            
        # Process stream
        for data_chunk in data_stream:
            # Convert to DataFrame if needed
            if not isinstance(data_chunk, pd.DataFrame):
                if isinstance(data_chunk, list) and data_chunk and isinstance(data_chunk[0], dict):
                    # Convert list of dicts to DataFrame
                    data_chunk = pd.DataFrame(data_chunk)
                elif isinstance(data_chunk, dict):
                    # Convert dict to DataFrame
                    data_chunk = pd.DataFrame([data_chunk])
                else:
                    raise ValueError("Data chunks must be pandas DataFrames, dicts, or lists of dicts")
                    
            # Encode in batches if needed
            if len(data_chunk) > batch_size:
                yield self.encode_batch(data_chunk, batch_size)
            else:
                yield self.transform(data_chunk)
    
    def get_feature_names(self) -> List[str]:
        """
        Get feature names after transformation.
        
        Returns:
            List[str]: Feature names
        """
        if not self.is_fitted:
            raise ValueError("Encoder must be fitted before get_feature_names")
            
        # Get feature names from each processor
        feature_names = []
        
        for processor in self.feature_processors.values():
            feature_names.extend(processor.get_feature_names())
            
        return feature_names
    
    def get_feature_importance(self) -> Dict[str, float]:
        """
        Get feature importance.
        
        Returns:
            Dict[str, float]: Feature importance
        """
        if not self.is_fitted:
            raise ValueError("Encoder must be fitted before get_feature_importance")
            
        if self.feature_selector is not None:
            return self.feature_selector.feature_importances
        else:
            # Default to equal importance for all features
            return {name: 1.0 for name in self.feature_processors}
    
    def clear_cache(self):
        """Clear the encoding cache."""
        self.cache.clear()
        
    def to(self, device):
        """
        Move encoder to specified device.
        
        Args:
            device: Target device
            
        Returns:
            DataEncoder: Self
        """
        self.device = device
        
        # Move projection to device
        if self.projection is not None:
            self.projection.to(device)
            
        # Move feature processors to device
        for processor in self.feature_processors.values():
            if hasattr(processor, 'to'):
                processor.to(device)
                
        return self
    
    def save(self, directory: str) -> None:
        """
        Save encoder to directory.
        
        Args:
            directory: Directory to save encoder
        """
        os.makedirs(directory, exist_ok=True)
        
        # Save configuration
        config_path = os.path.join(directory, "config.json")
        self.config.save(config_path)
        
        # Save schema
        if self.schema:
            schema_path = os.path.join(directory, "schema.json")
            self.schema.save(schema_path)
            
        # Save feature selector
        if self.feature_selector:
            selector_path = os.path.join(directory, "feature_selector.json")
            with open(selector_path, 'w') as f:
                json.dump(self.feature_selector.to_dict(), f, indent=2)
                
        # Save feature processors
        processors_dir = os.path.join(directory, "processors")
        os.makedirs(processors_dir, exist_ok=True)
        
        for name, processor in self.feature_processors.items():
            processor_path = os.path.join(processors_dir, f"{name}.json")
            with open(processor_path, 'w') as f:
                json.dump(processor.to_dict(), f, indent=2)
                
        # Save projection
        if self.projection is not None:
            projection_path = os.path.join(directory, "projection.pt")
            torch.save(self.projection.state_dict(), projection_path)
            
        logger.info(f"Encoder saved to {directory}")
    
    @classmethod
    def load(cls, directory: str, device=None) -> 'DataEncoder':
        """
        Load encoder from directory.
        
        Args:
            directory: Directory containing saved encoder
            device: Device to load encoder onto
            
        Returns:
            DataEncoder: Loaded encoder
        """
        # Load configuration
        config_path = os.path.join(directory, "config.json")
        config = DataEncoderConfig.load(config_path)
        
        # Override device if specified
        if device is not None:
            config.device = device
            
        # Create encoder instance
        encoder = cls(config)
        
        # Load schema
        schema_path = os.path.join(directory, "schema.json")
        if os.path.exists(schema_path):
            encoder.schema = DataSchema.load(schema_path)
            
        # Load feature selector
        selector_path = os.path.join(directory, "feature_selector.json")
        if os.path.exists(selector_path):
            with open(selector_path, 'r') as f:
                selector_dict = json.load(f)
            encoder.feature_selector = FeatureSelector.from_dict(selector_dict, config)
            
        # Load feature processors
        processors_dir = os.path.join(directory, "processors")
        if os.path.exists(processors_dir):
            for filename in os.listdir(processors_dir):
                if filename.endswith(".json"):
                    processor_path = os.path.join(processors_dir, filename)
                    with open(processor_path, 'r') as f:
                        processor_dict = json.load(f)
                        
                    # Get feature name
                    feature_name = filename[:-5]  # Remove .json
                    
                    # Get feature schema
                    feature_schema = next(
                        (feature for feature in encoder.schema.features if feature.name == feature_name),
                        None
                    )
                    
                    if feature_schema:
                        # Create processor based on feature type
                        if feature_schema.feature_type in [FeatureType.NUMERICAL_CONTINUOUS, FeatureType.NUMERICAL_DISCRETE]:
                            processor = NumericalProcessor.from_dict(processor_dict, feature_schema, config)
                        elif feature_schema.feature_type in [FeatureType.CATEGORICAL_NOMINAL, FeatureType.CATEGORICAL_ORDINAL]:
                            processor = CategoricalProcessor.from_dict(processor_dict, feature_schema, config)
                        elif feature_schema.feature_type == FeatureType.DATETIME:
                            processor = DatetimeProcessor.from_dict(processor_dict, feature_schema, config)
                        elif feature_schema.feature_type == FeatureType.BOOLEAN:
                            processor = BooleanProcessor.from_dict(processor_dict, feature_schema, config)
                        elif feature_schema.feature_type == FeatureType.TEXT:
                            processor = TextProcessor.from_dict(processor_dict, feature_schema, config)
                        else:
                            # Unknown feature type
                            logger.warning(f"Unsupported feature type: {feature_schema.feature_type}")
                            continue
                            
                        # Add to feature processors
                        encoder.feature_processors[feature_name] = processor
                    else:
                        logger.warning(f"Feature schema not found for processor: {feature_name}")
                        
        # Load projection
        projection_path = os.path.join(directory, "projection.pt")
        if os.path.exists(projection_path):
            input_dim = sum(processor.get_output_dim() for processor in encoder.feature_processors.values())
            encoder.projection = nn.Linear(input_dim, config.embedding_dim).to(config.device)
            encoder.projection.load_state_dict(torch.load(projection_path, map_location=config.device))
            
        encoder.is_fitted = True
        
        logger.info(f"Encoder loaded from {directory}")
        return encoder


# Version information
__version__ = '0.1.0'