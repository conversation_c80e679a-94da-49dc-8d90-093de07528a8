#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Text Encoding Module for ULTRA

This module provides text encoding capabilities for the ULTRA (Ultimate Learning & 
Thought Reasoning Architecture) system. It transforms raw text inputs into neural 
representations suitable for processing by the core neural architecture and
downstream components.

Key features:
- Multiple tokenization strategies (WordPiece, BPE, SentencePiece)
- Support for various transformer-based language models
- Multi-scale embeddings (token, sentence, document levels)
- Hierarchical processing for long documents
- Efficient caching and batching mechanisms
- Integration with the Multi-Scale Knowledge Embedding component

The implementation is designed to work seamlessly with the rest of the ULTRA
architecture, particularly the Hyper-Dimensional Transformer and Diffusion-Based
Reasoning subsystems.
"""

import os
import re
import logging
import unicodedata
import hashlib
import pickle
from typing import Dict, List, Tuple, Optional, Union, Any, Callable
from collections import defaultdict, OrderedDict
import warnings

import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from tqdm import tqdm

# Set up logging
logger = logging.getLogger(__name__)

# Import dependencies if available
try:
    import transformers
    from transformers import (
        AutoTokenizer, AutoModel, AutoConfig,
        PreTrainedTokenizer, PreTrainedModel,
        BatchEncoding
    )
    _HAS_TRANSFORMERS = True
except ImportError:
    logger.warning("Transformers library not found. Some functionality will be limited.")
    _HAS_TRANSFORMERS = False

try:
    import sentencepiece as spm
    _HAS_SENTENCEPIECE = True
except ImportError:
    logger.warning("SentencePiece library not found. Some tokenization methods will be unavailable.")
    _HAS_SENTENCEPIECE = False

try:
    from nltk.tokenize import sent_tokenize, word_tokenize
    import nltk
    # Ensure NLTK resources are available
    try:
        nltk.data.find('tokenizers/punkt')
    except LookupError:
        nltk.download('punkt', quiet=True)
    _HAS_NLTK = True
except ImportError:
    logger.warning("NLTK library not found. Fallback tokenization will be used.")
    _HAS_NLTK = False

try:
    import spacy
    _HAS_SPACY = True
except ImportError:
    logger.warning("Spacy library not found. Advanced NLP preprocessing will be limited.")
    _HAS_SPACY = False


class TokenizerConfig:
    """
    Configuration for text tokenization and encoding.
    
    This class defines parameters for tokenization, model selection,
    and embedding extraction for text data.
    """
    
    def __init__(
        self,
        model_name: str = "distilbert-base-uncased",
        tokenizer_name: Optional[str] = None,
        max_length: int = 512,
        stride: int = 128,
        padding: str = "max_length",
        truncation: bool = True,
        add_special_tokens: bool = True,
        return_tensors: str = "pt",
        embedding_strategy: str = "last_hidden",
        pooling_strategy: str = "mean",
        use_sliding_window: bool = False,
        layers_to_use: Optional[List[int]] = None,
        use_fast_tokenizer: bool = True,
        cache_dir: Optional[str] = None,
        device: Optional[Union[str, torch.device]] = None,
        embedding_dim: Optional[int] = None,
        normalize_embeddings: bool = True,
        use_gradient_checkpointing: bool = False,
        cache_size: int = 1000,
        preprocessing_steps: Optional[List[str]] = None,
        token_type_handling: str = "mask",
        doc_stride: int = 512,
        use_auth_token: bool = False,
        precision: str = "float32"
    ):
        """
        Initialize tokenizer configuration.
        
        Args:
            model_name: Name or path of the pretrained model
            tokenizer_name: Name or path of the tokenizer (if different from model)
            max_length: Maximum sequence length
            stride: Stride for sliding window tokenization
            padding: Padding strategy ('max_length', 'longest', or False)
            truncation: Whether to truncate sequences longer than max_length
            add_special_tokens: Whether to add special tokens (CLS, SEP, etc.)
            return_tensors: Format of the returned tensors ('pt', 'tf', or 'np')
            embedding_strategy: Strategy for embedding extraction ('last_hidden', 
                                'pooler_output', 'all_hidden', 'weighted')
            pooling_strategy: Pooling method ('mean', 'max', 'cls', 'sep', or 'none')
            use_sliding_window: Whether to use sliding window for long texts
            layers_to_use: Specific layers to use for embeddings
            use_fast_tokenizer: Whether to use the fast tokenizer implementation
            cache_dir: Directory to cache models
            device: Device to use for encoding
            embedding_dim: Output embedding dimension (None = use model's native dim)
            normalize_embeddings: Whether to L2-normalize embeddings
            use_gradient_checkpointing: Use gradient checkpointing to save memory
            cache_size: Size of the in-memory cache
            preprocessing_steps: Text preprocessing steps to apply
            token_type_handling: How to handle token types ('mask', 'embed', or 'ignore')
            doc_stride: Stride between chunks when processing long documents
            use_auth_token: Whether to use auth token for private models
            precision: Numerical precision ('float32', 'float16', or 'bfloat16')
        """
        self.model_name = model_name
        self.tokenizer_name = tokenizer_name if tokenizer_name else model_name
        self.max_length = max_length
        self.stride = stride
        self.padding = padding
        self.truncation = truncation
        self.add_special_tokens = add_special_tokens
        self.return_tensors = return_tensors
        self.embedding_strategy = embedding_strategy
        self.pooling_strategy = pooling_strategy
        self.use_sliding_window = use_sliding_window
        self.layers_to_use = layers_to_use
        self.use_fast_tokenizer = use_fast_tokenizer
        self.cache_dir = cache_dir
        self.device = device if device is not None else (
            torch.device('cuda') if torch.cuda.is_available() else torch.device('cpu')
        )
        self.embedding_dim = embedding_dim
        self.normalize_embeddings = normalize_embeddings
        self.use_gradient_checkpointing = use_gradient_checkpointing
        self.cache_size = cache_size
        self.preprocessing_steps = preprocessing_steps or ['lowercase', 'strip', 'normalize_unicode']
        self.token_type_handling = token_type_handling
        self.doc_stride = doc_stride
        self.use_auth_token = use_auth_token
        self.precision = precision
        
        # Validate configuration
        self._validate()
        
        logger.info(f"Initialized TokenizerConfig with model_name={model_name}")
    
    def _validate(self):
        """Validate configuration parameters."""
        valid_pooling = ['mean', 'max', 'cls', 'sep', 'none', 'attention']
        if self.pooling_strategy not in valid_pooling:
            raise ValueError(f"pooling_strategy must be one of {valid_pooling}")
        
        valid_embedding = ['last_hidden', 'pooler_output', 'all_hidden', 'weighted', 'concatenate']
        if self.embedding_strategy not in valid_embedding:
            raise ValueError(f"embedding_strategy must be one of {valid_embedding}")
        
        valid_token_type = ['mask', 'embed', 'ignore']
        if self.token_type_handling not in valid_token_type:
            raise ValueError(f"token_type_handling must be one of {valid_token_type}")
            
        if not isinstance(self.max_length, int) or self.max_length <= 0:
            raise ValueError(f"max_length must be a positive integer, got {self.max_length}")
            
        if not isinstance(self.stride, int) or self.stride <= 0:
            raise ValueError(f"stride must be a positive integer, got {self.stride}")
            
        if not isinstance(self.doc_stride, int) or self.doc_stride <= 0:
            raise ValueError(f"doc_stride must be a positive integer, got {self.doc_stride}")
            
        valid_precision = ['float32', 'float16', 'bfloat16']
        if self.precision not in valid_precision:
            raise ValueError(f"precision must be one of {valid_precision}")
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert configuration to dictionary.
        
        Returns:
            Dict[str, Any]: Configuration as dictionary
        """
        # Convert device to string for serialization
        config_dict = {k: v for k, v in self.__dict__.items() if not k.startswith('_')}
        if 'device' in config_dict and isinstance(config_dict['device'], torch.device):
            config_dict['device'] = str(config_dict['device'])
        return config_dict
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'TokenizerConfig':
        """
        Create configuration from dictionary.
        
        Args:
            config_dict: Dictionary containing configuration parameters
            
        Returns:
            TokenizerConfig: Configuration object
        """
        return cls(**config_dict)
    
    def save(self, file_path: str) -> None:
        """
        Save configuration to JSON file.
        
        Args:
            file_path: Path to save the configuration file
        """
        import json
        with open(file_path, 'w') as f:
            json.dump(self.to_dict(), f, indent=2)
    
    @classmethod
    def load(cls, file_path: str) -> 'TokenizerConfig':
        """
        Load configuration from JSON file.
        
        Args:
            file_path: Path to the configuration file
            
        Returns:
            TokenizerConfig: Configuration object
        """
        import json
        with open(file_path, 'r') as f:
            config_dict = json.load(f)
        return cls.from_dict(config_dict)


class TextNormalizer:
    """
    Text normalization and preprocessing utilities.
    
    This class provides methods for cleaning and normalizing text inputs
    before tokenization and encoding.
    """
    
    @staticmethod
    def normalize_unicode(text: str, form: str = 'NFKC') -> str:
        """
        Normalize Unicode characters.
        
        Args:
            text: Input text
            form: Normalization form ('NFC', 'NFKC', 'NFD', 'NFKD')
            
        Returns:
            str: Normalized text
        """
        return unicodedata.normalize(form, text)
    
    @staticmethod
    def remove_extra_whitespace(text: str) -> str:
        """
        Remove extra whitespace characters.
        
        Args:
            text: Input text
            
        Returns:
            str: Cleaned text
        """
        # Replace multiple whitespace with single space
        text = re.sub(r'\s+', ' ', text)
        # Trim leading/trailing whitespace
        return text.strip()
    
    @staticmethod
    def remove_html_tags(text: str) -> str:
        """
        Remove HTML tags from text.
        
        Args:
            text: Input text
            
        Returns:
            str: Text with HTML tags removed
        """
        return re.sub(r'<[^>]+>', '', text)
    
    @staticmethod
    def remove_urls(text: str) -> str:
        """
        Remove URLs from text.
        
        Args:
            text: Input text
            
        Returns:
            str: Text with URLs removed
        """
        url_pattern = r'https?://\S+|www\.\S+'
        return re.sub(url_pattern, '', text)
    
    @staticmethod
    def remove_special_chars(text: str, keep_punctuation: bool = True) -> str:
        """
        Remove special characters from text.
        
        Args:
            text: Input text
            keep_punctuation: Whether to keep punctuation
            
        Returns:
            str: Cleaned text
        """
        if keep_punctuation:
            # Keep alphanumeric and punctuation
            pattern = r'[^\w\s.,!?;:\'"-]'
        else:
            # Keep only alphanumeric
            pattern = r'[^\w\s]'
        
        return re.sub(pattern, '', text)
    
    @staticmethod
    def lowercase(text: str) -> str:
        """
        Convert text to lowercase.
        
        Args:
            text: Input text
            
        Returns:
            str: Lowercase text
        """
        return text.lower()
    
    @staticmethod
    def preprocess_text(text: str, steps: List[str]) -> str:
        """
        Apply multiple preprocessing steps.
        
        Args:
            text: Input text
            steps: List of preprocessing steps to apply
            
        Returns:
            str: Preprocessed text
        """
        if not text:
            return ""
            
        # Map of preprocessing steps to functions
        preprocessing_functions = {
            'normalize_unicode': TextNormalizer.normalize_unicode,
            'remove_extra_whitespace': TextNormalizer.remove_extra_whitespace,
            'remove_html_tags': TextNormalizer.remove_html_tags,
            'remove_urls': TextNormalizer.remove_urls,
            'remove_special_chars': TextNormalizer.remove_special_chars,
            'lowercase': TextNormalizer.lowercase,
            'strip': str.strip
        }
        
        # Apply each step in order
        for step in steps:
            if step in preprocessing_functions:
                text = preprocessing_functions[step](text)
            else:
                logger.warning(f"Unknown preprocessing step: {step}. Skipping.")
                
        return text


class LanguageModelBackend:
    """
    Backend for language model functionality.
    
    This class provides a common interface for different language model
    implementations, handling model loading, tokenization, and embedding
    extraction.
    """
    
    def __init__(self, config: TokenizerConfig):
        """
        Initialize language model backend.
        
        Args:
            config: Tokenizer configuration
        """
        self.config = config
        self.device = config.device
        self.model = None
        self.tokenizer = None
        self.model_config = None
        
        # Set precision
        self.dtype = {
            'float32': torch.float32,
            'float16': torch.float16,
            'bfloat16': torch.bfloat16 if hasattr(torch, 'bfloat16') else torch.float16
        }[config.precision]
        
        # Load tokenizer and model
        self._load_tokenizer()
        self._load_model()
        
        logger.info(f"Initialized language model backend with {config.model_name}")
    
    def _load_tokenizer(self):
        """Load the tokenizer according to configuration."""
        if not _HAS_TRANSFORMERS:
            raise ImportError("Transformers library is required for this functionality")
            
        try:
            self.tokenizer = AutoTokenizer.from_pretrained(
                self.config.tokenizer_name,
                use_fast=self.config.use_fast_tokenizer,
                cache_dir=self.config.cache_dir,
                use_auth_token=self.config.use_auth_token
            )
            logger.info(f"Loaded tokenizer: {self.config.tokenizer_name}")
        except Exception as e:
            logger.error(f"Error loading tokenizer: {str(e)}")
            raise
    
    def _load_model(self):
        """Load the model according to configuration."""
        if not _HAS_TRANSFORMERS:
            raise ImportError("Transformers library is required for this functionality")
            
        try:
            # Load model configuration
            self.model_config = AutoConfig.from_pretrained(
                self.config.model_name,
                cache_dir=self.config.cache_dir,
                use_auth_token=self.config.use_auth_token
            )
            
            # Load model
            self.model = AutoModel.from_pretrained(
                self.config.model_name,
                config=self.model_config,
                cache_dir=self.config.cache_dir,
                use_auth_token=self.config.use_auth_token
            )
            
            # Set to evaluation mode
            self.model.eval()
            
            # Use gradient checkpointing if configured
            if self.config.use_gradient_checkpointing and hasattr(self.model, "gradient_checkpointing_enable"):
                self.model.gradient_checkpointing_enable()
            
            # Move to specified device
            self.model.to(self.device)
            
            # Set precision
            self.model.to(dtype=self.dtype)
            
            logger.info(f"Loaded model: {self.config.model_name} on {self.device}")
        except Exception as e:
            logger.error(f"Error loading model: {str(e)}")
            raise
    
    def tokenize(self, texts: Union[str, List[str]], second_texts: Optional[List[str]] = None,
                token_type_ids: Optional[List[int]] = None, **kwargs) -> BatchEncoding:
        """
        Tokenize input texts.
        
        Args:
            texts: Input text or list of texts
            second_texts: Optional second texts for paired inputs
            token_type_ids: Optional token type IDs
            **kwargs: Additional keyword arguments for tokenizer
            
        Returns:
            BatchEncoding: Tokenized inputs
        """
        if not self.tokenizer:
            raise ValueError("Tokenizer not initialized")
            
        # Handle string vs list input
        is_single_text = isinstance(texts, str)
        if is_single_text:
            texts = [texts]
            
        if second_texts and len(second_texts) != len(texts):
            raise ValueError("Length of second_texts must match length of texts")
            
        # Set default tokenization parameters
        tokenize_kwargs = {
            "padding": kwargs.get("padding", self.config.padding),
            "truncation": kwargs.get("truncation", self.config.truncation),
            "max_length": kwargs.get("max_length", self.config.max_length),
            "return_tensors": kwargs.get("return_tensors", self.config.return_tensors),
            "add_special_tokens": kwargs.get("add_special_tokens", self.config.add_special_tokens),
            "return_token_type_ids": True,
            "return_attention_mask": True,
        }
        
        # Apply tokenization
        if second_texts:
            encoded = self.tokenizer(
                text=texts,
                text_pair=second_texts,
                **tokenize_kwargs
            )
        else:
            encoded = self.tokenizer(
                text=texts,
                **tokenize_kwargs
            )
        
        # Handle custom token type IDs if provided
        if token_type_ids is not None and self.config.token_type_handling != 'ignore':
            if self.config.token_type_handling == 'embed':
                # Replace default token type IDs
                encoded['token_type_ids'] = torch.tensor(token_type_ids, device=self.device)
            elif self.config.token_type_handling == 'mask':
                # Use token type IDs to modify attention mask
                for i, type_ids in enumerate(token_type_ids):
                    # Mask out tokens with type ID 0
                    encoded['attention_mask'][i] = encoded['attention_mask'][i] * (torch.tensor(type_ids, device=self.device) > 0)
        
        # Return single encoding if input was a single string
        return encoded
    
    def encode(self, encoded_inputs: BatchEncoding) -> Dict[str, torch.Tensor]:
        """
        Encode tokenized inputs to embeddings.
        
        Args:
            encoded_inputs: Tokenized inputs from tokenizer
            
        Returns:
            Dict[str, torch.Tensor]: Model outputs containing embeddings
        """
        if not self.model:
            raise ValueError("Model not initialized")
            
        # Prepare inputs
        inputs = {
            key: val.to(self.device) for key, val in encoded_inputs.items()
            if key in ['input_ids', 'attention_mask', 'token_type_ids']
        }
        
        # Get model outputs
        with torch.no_grad():
            outputs = self.model(**inputs, output_hidden_states=True, return_dict=True)
            
        return outputs
    
    def extract_embeddings(self, outputs: Dict[str, torch.Tensor],
                           attention_mask: torch.Tensor) -> torch.Tensor:
        """
        Extract embeddings from model outputs according to configured strategy.
        
        Args:
            outputs: Model outputs containing hidden states
            attention_mask: Attention mask for tokens
            
        Returns:
            torch.Tensor: Extracted embeddings
        """
        # Get all hidden states
        hidden_states = outputs.hidden_states
        
        # Extract embeddings based on strategy
        if self.config.embedding_strategy == 'last_hidden':
            # Use last layer hidden states
            embeddings = outputs.last_hidden_state
            
        elif self.config.embedding_strategy == 'pooler_output':
            # Use pooler output if available
            if hasattr(outputs, 'pooler_output') and outputs.pooler_output is not None:
                return outputs.pooler_output
            else:
                logger.warning("Pooler output not available, falling back to last hidden state")
                embeddings = outputs.last_hidden_state
                
        elif self.config.embedding_strategy == 'all_hidden':
            # Use all hidden states
            if self.config.layers_to_use:
                # Use specific layers
                layers = [hidden_states[i] for i in self.config.layers_to_use]
            else:
                # Use all layers
                layers = hidden_states
                
            # Average across layers
            embeddings = torch.stack(layers).mean(dim=0)
            
        elif self.config.embedding_strategy == 'weighted':
            # Use weighted sum of layers
            if self.config.layers_to_use:
                # Use specific layers
                layers = [hidden_states[i] for i in self.config.layers_to_use]
                # Equal weights if not specified
                weights = torch.ones(len(self.config.layers_to_use), device=self.device)
            else:
                # Use last 4 layers by default
                layers = hidden_states[-4:]
                # Increasing weights for later layers
                weights = torch.tensor([0.25, 0.5, 0.75, 1.0], device=self.device)
                
            # Normalize weights
            weights = weights / weights.sum()
            
            # Weighted sum
            embeddings = torch.zeros_like(layers[0])
            for i, layer in enumerate(layers):
                embeddings += weights[i] * layer
                
        elif self.config.embedding_strategy == 'concatenate':
            # Concatenate specified layers
            if self.config.layers_to_use:
                # Use specific layers
                layers = [hidden_states[i] for i in self.config.layers_to_use]
            else:
                # Use last 4 layers by default
                layers = hidden_states[-4:]
                
            # Concatenate along last dimension
            embeddings = torch.cat(layers, dim=-1)
            
        else:
            raise ValueError(f"Unknown embedding strategy: {self.config.embedding_strategy}")
        
        # Apply pooling
        return self._apply_pooling(embeddings, attention_mask)
    
    def _apply_pooling(self, embeddings: torch.Tensor, attention_mask: torch.Tensor) -> torch.Tensor:
        """
        Apply pooling to token embeddings.
        
        Args:
            embeddings: Token embeddings [batch_size, seq_len, hidden_dim]
            attention_mask: Attention mask [batch_size, seq_len]
            
        Returns:
            torch.Tensor: Pooled embeddings [batch_size, hidden_dim]
        """
        if self.config.pooling_strategy == 'none':
            # Return token-level embeddings
            return embeddings
            
        elif self.config.pooling_strategy == 'cls':
            # Use [CLS] token embedding
            return embeddings[:, 0]
            
        elif self.config.pooling_strategy == 'sep':
            # Find position of [SEP] token
            # For each sequence, find the first position where attention_mask is 0
            batch_size = embeddings.size(0)
            hidden_dim = embeddings.size(-1)
            
            # Initialize with last token as fallback
            sep_embeddings = embeddings[:, -1]
            
            # Try to find actual [SEP] tokens if model uses them
            if hasattr(self.tokenizer, 'sep_token_id'):
                sep_token_id = self.tokenizer.sep_token_id
                if sep_token_id is not None:
                    # Get input IDs
                    if hasattr(self.tokenizer, 'input_ids'):
                        input_ids = self.tokenizer.input_ids
                        # For each sequence, find the position of [SEP]
                        for i in range(batch_size):
                            sep_positions = (input_ids[i] == sep_token_id).nonzero()
                            if sep_positions.size(0) > 0:
                                # Use the first [SEP] token
                                sep_embeddings[i] = embeddings[i, sep_positions[0]]
            
            return sep_embeddings
            
        elif self.config.pooling_strategy == 'mean':
            # Mean pooling over non-padded tokens
            mask_expanded = attention_mask.unsqueeze(-1).expand_as(embeddings).float()
            sum_embeddings = torch.sum(embeddings * mask_expanded, dim=1)
            sum_mask = torch.sum(mask_expanded, dim=1)
            sum_mask = torch.clamp(sum_mask, min=1e-9)  # Avoid division by zero
            
            return sum_embeddings / sum_mask
            
        elif self.config.pooling_strategy == 'max':
            # Max pooling over non-padded tokens
            mask_expanded = attention_mask.unsqueeze(-1).expand_as(embeddings).float()
            embeddings[mask_expanded == 0] = -1e9  # Set padding tokens to large negative value
            
            return torch.max(embeddings, dim=1)[0]
            
        elif self.config.pooling_strategy == 'attention':
            # Self-attention pooling
            batch_size, seq_len, hidden_dim = embeddings.shape
            
            # Attention projection layer
            if not hasattr(self, 'attention_weights'):
                self.attention_weights = nn.Linear(hidden_dim, 1).to(embeddings.device)
                nn.init.xavier_uniform_(self.attention_weights.weight)
            
            # Compute attention scores
            attention_scores = self.attention_weights(embeddings).squeeze(-1)
            
            # Mask out padding tokens
            attention_scores = attention_scores.masked_fill(attention_mask == 0, -1e9)
            
            # Apply softmax
            attention_probs = F.softmax(attention_scores, dim=1)
            
            # Apply attention to embeddings
            return torch.bmm(attention_probs.unsqueeze(1), embeddings).squeeze(1)
            
        else:
            raise ValueError(f"Unknown pooling strategy: {self.config.pooling_strategy}")
    
    def encode_sliding_window(self, texts: Union[str, List[str]], **kwargs) -> torch.Tensor:
        """
        Encode long texts using sliding window approach.
        
        Args:
            texts: Input text or list of texts
            **kwargs: Additional keyword arguments for tokenization
            
        Returns:
            torch.Tensor: Encoded embeddings
        """
        # Handle string vs list input
        is_single_text = isinstance(texts, str)
        if is_single_text:
            texts = [texts]
        
        all_embeddings = []
        
        for text in texts:
            # Prepare to store embeddings for all chunks
            text_embeddings = []
            
            # Tokenize with stride
            tokens = self.tokenizer(
                text=text,
                padding="max_length",
                truncation=True,
                max_length=self.config.max_length,
                stride=self.config.stride,
                return_overflowing_tokens=True,
                return_tensors="pt"
            )
            
            # Process each chunk
            for i in range(len(tokens.input_ids)):
                # Extract the chunk
                chunk = {k: v[i:i+1] for k, v in tokens.items() if k in ['input_ids', 'attention_mask', 'token_type_ids']}
                
                # Encode the chunk
                outputs = self.encode(chunk)
                
                # Extract embeddings
                chunk_embedding = self.extract_embeddings(outputs, chunk['attention_mask'])
                
                # Add to text embeddings
                text_embeddings.append(chunk_embedding)
            
            # Combine chunk embeddings
            if text_embeddings:
                if self.config.pooling_strategy == 'none':
                    # For token-level embeddings, we need more complex merging
                    # This is a simplified approach that concatenates all tokens
                    combined = torch.cat(text_embeddings, dim=1)
                    # Truncate to a reasonable length if needed
                    max_len = min(combined.size(1), 1024)
                    combined = combined[:, :max_len]
                else:
                    # For sequence-level embeddings, we can average
                    combined = torch.cat(text_embeddings).mean(dim=0, keepdim=True)
            else:
                # Fallback for empty text
                combined = torch.zeros((1, self.model.config.hidden_size), device=self.device)
            
            all_embeddings.append(combined)
        
        # Stack all text embeddings
        if self.config.pooling_strategy == 'none':
            # For token-level embeddings, we need padding
            max_len = max(emb.size(1) for emb in all_embeddings)
            padded_embeddings = []
            
            for emb in all_embeddings:
                if emb.size(1) < max_len:
                    padding = torch.zeros((1, max_len - emb.size(1), emb.size(2)), device=emb.device)
                    padded = torch.cat([emb, padding], dim=1)
                else:
                    padded = emb
                padded_embeddings.append(padded)
            
            result = torch.cat(padded_embeddings, dim=0)
        else:
            # For sequence-level embeddings, just stack
            result = torch.cat(all_embeddings, dim=0)
        
        # Return single embedding if input was a single string
        if is_single_text:
            return result.squeeze(0)
        
        return result
    
    def process_long_document(self, doc: str) -> torch.Tensor:
        """
        Process a long document by splitting into chunks and encoding.
        
        Args:
            doc: Document text
            
        Returns:
            torch.Tensor: Document embedding
        """
        # Special case for empty document
        if not doc or doc.isspace():
            return torch.zeros((1, self.model.config.hidden_size), device=self.device)
        
        # Handle short documents directly
        if len(doc) < self.config.max_length * 2:
            return self.encode_sliding_window(doc)
        
        # Split document into sentences
        if _HAS_NLTK:
            sentences = sent_tokenize(doc)
        else:
            # Simple fallback sentence splitting
            sentences = re.split(r'(?<=[.!?])\s+', doc)
        
        # Group sentences into chunks
        chunks = []
        current_chunk = []
        current_length = 0
        
        for sentence in sentences:
            # Estimate token length (conservative approximation)
            sentence_length = len(sentence.split())
            
            if current_length + sentence_length > self.config.doc_stride:
                # Start a new chunk if this would exceed the stride
                if current_chunk:
                    chunks.append(" ".join(current_chunk))
                current_chunk = [sentence]
                current_length = sentence_length
            else:
                # Add to current chunk
                current_chunk.append(sentence)
                current_length += sentence_length
        
        # Add final chunk
        if current_chunk:
            chunks.append(" ".join(current_chunk))
        
        # Encode each chunk
        chunk_embeddings = []
        for chunk in chunks:
            embedding = self.encode_sliding_window(chunk)
            chunk_embeddings.append(embedding)
        
        # Combine chunk embeddings
        if self.config.pooling_strategy == 'none':
            # For token-level embeddings, concatenate first N tokens from each chunk
            # This is a simplification - a more sophisticated approach would be needed for token-level processing
            tokens_per_chunk = min(128, self.config.max_length // len(chunks))
            
            # Take first tokens_per_chunk tokens from each chunk
            selected_tokens = []
            for emb in chunk_embeddings:
                if emb.size(1) > tokens_per_chunk:
                    selected = emb[:, :tokens_per_chunk, :]
                else:
                    selected = emb
                selected_tokens.append(selected)
            
            # Concatenate
            document_embedding = torch.cat(selected_tokens, dim=1)
            
            # Truncate if needed
            if document_embedding.size(1) > self.config.max_length:
                document_embedding = document_embedding[:, :self.config.max_length, :]
        else:
            # For sequence-level embeddings, use weighted average based on chunk length
            chunk_weights = torch.tensor([len(chunk.split()) for chunk in chunks], 
                                       device=self.device, dtype=torch.float)
            chunk_weights = chunk_weights / chunk_weights.sum()
            
            # Apply weights
            weighted_embeddings = []
            for i, emb in enumerate(chunk_embeddings):
                weighted_embeddings.append(emb * chunk_weights[i])
            
            document_embedding = torch.stack(weighted_embeddings).sum(dim=0)
        
        return document_embedding
    
    def process_hierarchical_document(self, doc_parts: List[str], structure: Optional[List[int]] = None) -> Dict[str, torch.Tensor]:
        """
        Process a hierarchical document (e.g., with sections, paragraphs).
        
        Args:
            doc_parts: List of document parts (sections, paragraphs)
            structure: Optional list indicating the structure (e.g., section indices)
            
        Returns:
            Dict[str, torch.Tensor]: Hierarchical embeddings at different levels
        """
        # Handle empty document
        if not doc_parts:
            empty_tensor = torch.zeros((1, self.model.config.hidden_size), device=self.device)
            return {
                'document': empty_tensor,
                'sections': empty_tensor.unsqueeze(0),
                'parts': empty_tensor.unsqueeze(0)
            }
        
        # Encode individual parts
        part_embeddings = []
        for part in doc_parts:
            # Skip empty parts
            if not part or part.isspace():
                continue
                
            embedding = self.encode_sliding_window(part)
            part_embeddings.append(embedding)
        
        # Handle case with no valid parts
        if not part_embeddings:
            empty_tensor = torch.zeros((1, self.model.config.hidden_size), device=self.device)
            return {
                'document': empty_tensor,
                'sections': empty_tensor.unsqueeze(0),
                'parts': empty_tensor.unsqueeze(0)
            }
        
        # Stack part embeddings
        parts_tensor = torch.cat(part_embeddings)
        
        # Create section embeddings if structure is provided
        if structure and len(structure) == len(part_embeddings):
            # Group by section
            sections = defaultdict(list)
            for i, section_idx in enumerate(structure):
                sections[section_idx].append(part_embeddings[i])
            
            # Combine parts within each section
            section_embeddings = []
            for section_idx in sorted(sections.keys()):
                section_parts = sections[section_idx]
                if section_parts:
                    # Average parts within section
                    section_emb = torch.cat(section_parts).mean(dim=0, keepdim=True)
                    section_embeddings.append(section_emb)
            
            # Stack section embeddings
            sections_tensor = torch.cat(section_embeddings)
        else:
            # Without structure, treat each part as its own section
            sections_tensor = parts_tensor
        
        # Create document embedding (average of sections)
        document_tensor = sections_tensor.mean(dim=0, keepdim=True)
        
        return {
            'document': document_tensor,
            'sections': sections_tensor,
            'parts': parts_tensor
        }


class TextEncoder(nn.Module):
    """
    Text encoder for ULTRA architecture.
    
    This class provides comprehensive text encoding capabilities,
    transforming raw text inputs into neural representations at multiple
    levels of abstraction.
    """
    
    def __init__(self, config: Optional[Union[TokenizerConfig, dict]] = None):
        """
        Initialize text encoder.
        
        Args:
            config: Configuration for the encoder
        """
        super().__init__()
        
        # Handle configuration
        if config is None:
            self.config = TokenizerConfig()
        elif isinstance(config, dict):
            self.config = TokenizerConfig(**config)
        else:
            self.config = config
            
        # Set device
        self.device = self.config.device
        
        # Initialize backend
        self.backend = LanguageModelBackend(self.config)
        
        # Initialize text normalizer
        self.normalizer = TextNormalizer()
        
        # Create projection layer if needed
        if self.config.embedding_dim is not None:
            # Get the native model dimension
            if hasattr(self.backend.model.config, 'hidden_size'):
                native_dim = self.backend.model.config.hidden_size
            else:
                native_dim = self.backend.model.config.dim
                
            # Create projection if dimensions differ
            if native_dim != self.config.embedding_dim:
                self.projection = nn.Linear(native_dim, self.config.embedding_dim)
                nn.init.xavier_uniform_(self.projection.weight)
                self.projection.to(self.device)
            else:
                self.projection = None
        else:
            self.projection = None
        
        # Cache for encodings
        self.cache_enabled = self.config.cache_size > 0
        self.cache = OrderedDict()
        self.cache_size = self.config.cache_size
        
        logger.info(f"Initialized TextEncoder with model {self.config.model_name}")
    
    def _preprocess_text(self, text: Union[str, List[str]]) -> Union[str, List[str]]:
        """
        Preprocess text using configured steps.
        
        Args:
            text: Input text or list of texts
            
        Returns:
            str or List[str]: Preprocessed text
        """
        if isinstance(text, str):
            return self.normalizer.preprocess_text(text, self.config.preprocessing_steps)
        else:
            return [self.normalizer.preprocess_text(t, self.config.preprocessing_steps) for t in text]
    
    def _compute_hash(self, input_data: Any) -> str:
        """
        Compute a hash for cache lookup.
        
        Args:
            input_data: Input data to be hashed
            
        Returns:
            str: Hash string
        """
        import hashlib
        
        if isinstance(input_data, str):
            # Hash the string directly
            return hashlib.sha256(input_data.encode('utf-8')).hexdigest()
        elif isinstance(input_data, list) and all(isinstance(item, str) for item in input_data):
            # Hash list of strings
            joined = "\n".join(input_data)
            return hashlib.sha256(joined.encode('utf-8')).hexdigest()
        else:
            # Try to pickle and hash more complex data
            try:
                data_bytes = pickle.dumps(input_data)
                return hashlib.sha256(data_bytes).hexdigest()
            except Exception as e:
                logger.warning(f"Failed to hash input for caching: {str(e)}")
                return None
    
    def _manage_cache_size(self):
        """Ensure cache doesn't exceed maximum size."""
        if len(self.cache) > self.cache_size:
            # Remove oldest items (OrderedDict preserves insertion order)
            for _ in range(len(self.cache) - self.cache_size):
                self.cache.popitem(last=False)
    
    def encode(self, text: Union[str, List[str]], second_text: Optional[Union[str, List[str]]] = None,
              return_batch_encoding: bool = False, **kwargs) -> Union[torch.Tensor, Dict]:
        """
        Encode text into embeddings.
        
        Args:
            text: Input text or list of texts
            second_text: Optional second text for paired inputs
            return_batch_encoding: Whether to return the full BatchEncoding
            **kwargs: Additional arguments for tokenization
            
        Returns:
            torch.Tensor or Dict: Encoded embeddings or full outputs
        """
        # Check cache if enabled
        if self.cache_enabled and not kwargs:
            # Compute cache key
            if second_text:
                # For text pairs
                if isinstance(text, str) and isinstance(second_text, str):
                    cache_key = self._compute_hash((text, second_text))
                else:
                    # Create key for each text pair
                    cache_key = None
            else:
                # For single texts
                cache_key = self._compute_hash(text)
                
            # Return from cache if found
            if cache_key in self.cache:
                return self.cache[cache_key]
        else:
            cache_key = None
        
        # Determine if we're dealing with a long document
        is_long_doc = False
        
        # Switch to document processing for long single texts
        if isinstance(text, str) and len(text) > self.config.max_length * 4:
            is_long_doc = True
        
        # Preprocess text
        text = self._preprocess_text(text)
        if second_text:
            second_text = self._preprocess_text(second_text)
        
        # Use specialized processing for long documents
        if is_long_doc:
            embeddings = self.backend.process_long_document(text)
        elif isinstance(text, list) and any(len(t) > self.config.max_length * 4 for t in text):
            # Process each long document individually
            embeddings = []
            for t in text:
                if len(t) > self.config.max_length * 4:
                    emb = self.backend.process_long_document(t)
                else:
                    # For shorter texts, use sliding window if configured
                    if self.config.use_sliding_window:
                        emb = self.backend.encode_sliding_window(t, **kwargs)
                    else:
                        encoded = self.backend.tokenize(t, **kwargs)
                        outputs = self.backend.encode(encoded)
                        emb = self.backend.extract_embeddings(outputs, encoded['attention_mask'])
                embeddings.append(emb)
            
            # Stack embeddings
            embeddings = torch.cat([emb.unsqueeze(0) if emb.dim() == 1 else emb for emb in embeddings])
        else:
            # For regular texts, use sliding window if configured
            if self.config.use_sliding_window:
                embeddings = self.backend.encode_sliding_window(text, **kwargs)
            else:
                # Tokenize inputs
                encoded = self.backend.tokenize(text, second_text, **kwargs)
                
                # Encode and extract embeddings
                outputs = self.backend.encode(encoded)
                embeddings = self.backend.extract_embeddings(outputs, encoded['attention_mask'])
                
                # Return full output if requested
                if return_batch_encoding:
                    return {
                        'embeddings': embeddings,
                        'encoded': encoded,
                        'model_outputs': outputs
                    }
        
        # Apply projection if configured
        if self.projection is not None:
            embeddings = self.projection(embeddings)
        
        # Apply normalization if configured
        if self.config.normalize_embeddings:
            embeddings = F.normalize(embeddings, p=2, dim=-1)
        
        # Cache result if enabled
        if self.cache_enabled and cache_key:
            self.cache[cache_key] = embeddings
            self._manage_cache_size()
        
        return embeddings
    
    def encode_hierarchical(self, document_parts: List[str], structure: Optional[List[int]] = None) -> Dict[str, torch.Tensor]:
        """
        Encode a document with hierarchical structure.
        
        Args:
            document_parts: List of document parts (sections, paragraphs)
            structure: Optional list indicating the structure (e.g., section indices)
            
        Returns:
            Dict[str, torch.Tensor]: Hierarchical embeddings at different levels
        """
        return self.backend.process_hierarchical_document(document_parts, structure)
    
    def encode_multi_scale(self, text: str) -> Dict[str, torch.Tensor]:
        """
        Encode text at multiple scales of abstraction.
        
        Args:
            text: Input text
            
        Returns:
            Dict[str, torch.Tensor]: Embeddings at different scales
        """
        # Process at character/subword level (lowest scale)
        if not _HAS_TRANSFORMERS:
            raise ImportError("Transformers library is required for multi-scale encoding")
            
        # Special case for empty text
        if not text or text.isspace():
            empty_embedding = torch.zeros((1, self.backend.model.config.hidden_size), device=self.device)
            return {
                'character': empty_embedding,
                'word': empty_embedding,
                'sentence': empty_embedding,
                'paragraph': empty_embedding,
                'document': empty_embedding
            }
        
        # Preprocess text
        text = self._preprocess_text(text)
        
        # Split text into different levels
        if _HAS_NLTK:
            # Use NLTK for better segmentation
            sentences = sent_tokenize(text)
            words = word_tokenize(text)
        else:
            # Simple fallback
            sentences = re.split(r'(?<=[.!?])\s+', text)
            words = text.split()
        
        # Split into paragraphs
        paragraphs = text.split('\n\n')
        if len(paragraphs) == 1:
            # Try another common paragraph separator
            paragraphs = text.split('\n')
        
        # Filter out empty parts
        sentences = [s for s in sentences if s and not s.isspace()]
        paragraphs = [p for p in paragraphs if p and not p.isspace()]
        
        # Character/subword level (use token embeddings)
        char_embeddings = self.encode(text, return_batch_encoding=True)
        
        # Extract token-level embeddings
        if self.config.pooling_strategy != 'none':
            # Use standard encoding with token-level output
            temp_pooling = self.config.pooling_strategy
            self.config.pooling_strategy = 'none'
            token_embeddings = self.encode(text)
            self.config.pooling_strategy = temp_pooling
        else:
            token_embeddings = char_embeddings['embeddings'] if isinstance(char_embeddings, dict) else char_embeddings
        
        # Word level 
        if len(words) > 0:
            word_embeddings = self.encode(words)
        else:
            word_embeddings = torch.zeros((1, token_embeddings.size(-1)), device=self.device)
        
        # Sentence level
        if len(sentences) > 0:
            sentence_embeddings = self.encode(sentences)
        else:
            sentence_embeddings = torch.zeros((1, token_embeddings.size(-1)), device=self.device)
        
        # Paragraph level
        if len(paragraphs) > 0:
            paragraph_embeddings = self.encode(paragraphs)
        else:
            paragraph_embeddings = torch.zeros((1, token_embeddings.size(-1)), device=self.device)
        
        # Document level
        document_embedding = self.encode(text)
        
        return {
            'character': token_embeddings,
            'word': word_embeddings,
            'sentence': sentence_embeddings,
            'paragraph': paragraph_embeddings,
            'document': document_embedding
        }
    
    def forward(self, text: Union[str, List[str]], **kwargs) -> torch.Tensor:
        """
        Forward pass to encode text.
        
        Args:
            text: Input text or list of texts
            **kwargs: Additional arguments for encoding
            
        Returns:
            torch.Tensor: Encoded embeddings
        """
        return self.encode(text, **kwargs)
    
    def clear_cache(self):
        """Clear the encoding cache."""
        self.cache.clear()
        
    def to(self, device):
        """
        Move encoder to specified device.
        
        Args:
            device: Target device
            
        Returns:
            TextEncoder: Self
        """
        self.device = device
        self.backend.device = device
        self.backend.model.to(device)
        
        if self.projection is not None:
            self.projection.to(device)
            
        return self
    
    def save(self, directory: str, save_model: bool = True):
        """
        Save encoder and configuration.
        
        Args:
            directory: Directory to save in
            save_model: Whether to save the underlying model
        """
        os.makedirs(directory, exist_ok=True)
        
        # Save configuration
        config_path = os.path.join(directory, "config.json")
        self.config.save(config_path)
        
        # Save encoder state (projection layer weights)
        if self.projection is not None:
            state_path = os.path.join(directory, "encoder_state.pt")
            torch.save(self.state_dict(), state_path)
        
        # Save model if requested
        if save_model and self.backend.model is not None:
            model_dir = os.path.join(directory, "model")
            os.makedirs(model_dir, exist_ok=True)
            self.backend.model.save_pretrained(model_dir)
            self.backend.tokenizer.save_pretrained(model_dir)
        
        logger.info(f"TextEncoder saved to {directory}")
    
    @classmethod
    def load(cls, directory: str, device=None, load_model: bool = True):
        """
        Load encoder from saved files.
        
        Args:
            directory: Directory containing saved encoder
            device: Device to load onto
            load_model: Whether to load the underlying model
            
        Returns:
            TextEncoder: Loaded encoder
        """
        # Load configuration
        config_path = os.path.join(directory, "config.json")
        config = TokenizerConfig.load(config_path)
        
        # Override device if specified
        if device is not None:
            config.device = device
        
        # Create instance
        instance = cls(config)
        
        # Load encoder state (projection layer weights)
        state_path = os.path.join(directory, "encoder_state.pt")
        if os.path.exists(state_path):
            instance.load_state_dict(torch.load(state_path, map_location=config.device))
        
        # Load model if requested
        if load_model:
            model_dir = os.path.join(directory, "model")
            if os.path.exists(model_dir):
                config.model_name = model_dir
                config.tokenizer_name = model_dir
                instance.backend = LanguageModelBackend(config)
        
        logger.info(f"TextEncoder loaded from {directory}")
        return instance


# Sentinel to mark end of module
if __name__ == "__main__":
    # Example usage
    config = TokenizerConfig(model_name="distilbert-base-uncased")
    encoder = TextEncoder(config)
    
    text = "Hello, this is a test sentence for ULTRA text encoding."
    embeddings = encoder.encode(text)
    
    print(f"Encoded text shape: {embeddings.shape}")