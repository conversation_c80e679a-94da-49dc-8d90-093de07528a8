#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Multi-Modal Encoding Module for ULTRA

This module provides comprehensive multi-modal encoding capabilities for the ULTRA 
(Ultimate Learning & Thought Reasoning Architecture) system. It combines inputs from 
different modalities (text, images, audio, structured data) into unified representations 
suitable for processing by the core neural architecture and downstream components.

Key features:
- Multiple fusion strategies (concatenation, attention, bilinear, gated)
- Cross-modal attention for enhanced integration
- Dynamic weighting of modalities based on content
- Hierarchical multi-modal processing
- Adaptive fusion mechanisms
- Integration with the Cross-Modal Dimension Mapper of the Hyper-Dimensional Transformer

The implementation is optimized for seamless integration with other ULTRA components,
particularly the Hyper-Dimensional Transformer, Diffusion-Based Reasoning,
and Meta-Cognitive subsystems.
"""

import os
import logging
import pickle
import hashlib
import json
from typing import Dict, List, Tuple, Optional, Union, Any, Type, Callable
from collections import OrderedDict

import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F

# Configure logging
logger = logging.getLogger(__name__)

# Try to import optional dependencies
try:
    import transformers
    _HAS_TRANSFORMERS = True
except ImportError:
    logger.warning("Transformers library not found. Some functionality will be limited.")
    _HAS_TRANSFORMERS = False

try:
    import torchvision
    _HAS_TORCHVISION = True
except ImportError:
    logger.warning("TorchVision library not found. Image processing capabilities will be limited.")
    _HAS_TORCHVISION = False

try:
    import torchaudio
    _HAS_TORCHAUDIO = True
except ImportError:
    logger.warning("TorchAudio library not found. Audio processing capabilities will be limited.")
    _HAS_TORCHAUDIO = False

# Try to import ULTRA modality-specific encoders
try:
    from .text_encoding import TextEncoder, TokenizerConfig
    _HAS_TEXT_ENCODER = True
except ImportError:
    logger.warning("TextEncoder not found. Text modality will not be available.")
    _HAS_TEXT_ENCODER = False

try:
    from .image_encoding import ImageEncoder, ImageEncoderConfig
    _HAS_IMAGE_ENCODER = True
except ImportError:
    logger.warning("ImageEncoder not found. Image modality will not be available.")
    _HAS_IMAGE_ENCODER = False

try:
    from .audio_encoding import AudioEncoder, AudioEncoderConfig
    _HAS_AUDIO_ENCODER = True
except ImportError:
    logger.warning("AudioEncoder not found. Audio modality will not be available.")
    _HAS_AUDIO_ENCODER = False

try:
    from .data_encoding import DataEncoder, DataEncoderConfig
    _HAS_DATA_ENCODER = True
except ImportError:
    logger.warning("DataEncoder not found. Structured data modality will not be available.")
    _HAS_DATA_ENCODER = False


class MultiModalEncoderConfig:
    """
    Configuration for multi-modal encoding.
    
    This class defines parameters for combining and processing inputs
    from multiple modalities.
    """
    
    def __init__(
        self,
        modality_configs: Dict[str, Dict[str, Any]] = None,
        fusion_method: str = "concatenate",
        fusion_layers: List[int] = None,
        modality_weights: Dict[str, float] = None,
        projection_dim: int = 512,
        output_dim: int = 1024,
        dropout: float = 0.1,
        cross_attention: bool = False,
        cross_attention_heads: int = 8,
        adaptive_fusion: bool = False,
        use_modality_tokens: bool = True,
        normalize_embeddings: bool = True,
        cache_size: int = 1000,
        late_fusion: bool = False,
        device: Optional[Union[str, torch.device]] = None,
        precision: str = "float32",
        enable_context_gating: bool = False,
        modality_dropout: float = 0.0,
        use_expert_weighting: bool = False,
        num_experts: int = 4,
        adapter_dim: int = 128
    ):
        """
        Initialize multi-modal encoder configuration.
        
        Args:
            modality_configs: Configuration for each modality encoder
            fusion_method: Method for fusing modalities ('concatenate', 'attention',
                          'bilinear', 'gated', 'moe', 'film', 'sum')
            fusion_layers: Dimensions of fusion network layers
            modality_weights: Fixed weights for each modality
            projection_dim: Dimension for projecting each modality
            output_dim: Dimension of final multi-modal embedding
            dropout: Dropout rate in fusion layers
            cross_attention: Whether to use cross-attention between modalities
            cross_attention_heads: Number of heads in cross-attention
            adaptive_fusion: Whether to use adaptive fusion based on content
            use_modality_tokens: Add modality type tokens to embeddings
            normalize_embeddings: Whether to L2-normalize final embeddings
            cache_size: Size of the in-memory cache
            late_fusion: Whether to use late fusion (defer fusion until necessary)
            device: Device to use for encoding
            precision: Numerical precision ('float32', 'float16', or 'bfloat16')
            enable_context_gating: Whether to use context gating mechanism
            modality_dropout: Probability of dropping a modality during training
            use_expert_weighting: Whether to use mixture-of-experts for weighting
            num_experts: Number of expert networks for MoE fusion
            adapter_dim: Dimension of adapter networks for efficient fine-tuning
        """
        self.modality_configs = modality_configs or {}
        self.fusion_method = fusion_method
        self.fusion_layers = fusion_layers or [1024, 512]
        self.modality_weights = modality_weights
        self.projection_dim = projection_dim
        self.output_dim = output_dim
        self.dropout = dropout
        self.cross_attention = cross_attention
        self.cross_attention_heads = cross_attention_heads
        self.adaptive_fusion = adaptive_fusion
        self.use_modality_tokens = use_modality_tokens
        self.normalize_embeddings = normalize_embeddings
        self.cache_size = cache_size
        self.late_fusion = late_fusion
        self.device = device if device is not None else (
            torch.device('cuda') if torch.cuda.is_available() else torch.device('cpu')
        )
        self.precision = precision
        self.enable_context_gating = enable_context_gating
        self.modality_dropout = modality_dropout
        self.use_expert_weighting = use_expert_weighting
        self.num_experts = num_experts
        self.adapter_dim = adapter_dim
        
        # Validate configuration
        self._validate()
        
        logger.info(f"Initialized MultiModalEncoderConfig with fusion_method={fusion_method}")
    
    def _validate(self):
        """Validate configuration parameters."""
        valid_fusion = ['concatenate', 'attention', 'bilinear', 'gated', 'moe', 'film', 'sum', 'bottleneck']
        if self.fusion_method not in valid_fusion:
            raise ValueError(f"fusion_method must be one of {valid_fusion}")
        
        if not isinstance(self.fusion_layers, list) or not all(isinstance(d, int) and d > 0 for d in self.fusion_layers):
            raise ValueError("fusion_layers must be a list of positive integers")
        
        if self.modality_weights and not all(w >= 0 for w in self.modality_weights.values()):
            raise ValueError("All modality weights must be non-negative")
        
        if not isinstance(self.projection_dim, int) or self.projection_dim <= 0:
            raise ValueError(f"projection_dim must be a positive integer")
        
        if not isinstance(self.output_dim, int) or self.output_dim <= 0:
            raise ValueError(f"output_dim must be a positive integer")
        
        if not 0 <= self.dropout < 1:
            raise ValueError(f"dropout must be in range [0, 1)")
        
        if not 0 <= self.modality_dropout < 1:
            raise ValueError(f"modality_dropout must be in range [0, 1)")
        
        if self.cross_attention and not isinstance(self.cross_attention_heads, int):
            raise ValueError(f"cross_attention_heads must be an integer")
        
        if self.use_expert_weighting and (not isinstance(self.num_experts, int) or self.num_experts <= 0):
            raise ValueError(f"num_experts must be a positive integer")
            
        valid_precision = ['float32', 'float16', 'bfloat16']
        if self.precision not in valid_precision:
            raise ValueError(f"precision must be one of {valid_precision}")
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert configuration to dictionary.
        
        Returns:
            Dict[str, Any]: Configuration as dictionary
        """
        # Convert device to string for serialization
        config_dict = {k: v for k, v in self.__dict__.items() if not k.startswith('_')}
        if 'device' in config_dict and isinstance(config_dict['device'], torch.device):
            config_dict['device'] = str(config_dict['device'])
        return config_dict
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'MultiModalEncoderConfig':
        """
        Create configuration from dictionary.
        
        Args:
            config_dict: Dictionary containing configuration parameters
            
        Returns:
            MultiModalEncoderConfig: Configuration object
        """
        return cls(**config_dict)
    
    def save(self, file_path: str) -> None:
        """
        Save configuration to JSON file.
        
        Args:
            file_path: Path to save the configuration file
        """
        with open(file_path, 'w') as f:
            json.dump(self.to_dict(), f, indent=2)
    
    @classmethod
    def load(cls, file_path: str) -> 'MultiModalEncoderConfig':
        """
        Load configuration from JSON file.
        
        Args:
            file_path: Path to the configuration file
            
        Returns:
            MultiModalEncoderConfig: Configuration object
        """
        with open(file_path, 'r') as f:
            config_dict = json.load(f)
        return cls.from_dict(config_dict)


class ModalityAdapter(nn.Module):
    """
    Adapter network for modality-specific processing.
    
    This module adapts embeddings from a specific modality to a common
    representation space, optionally adding modality-specific tokens.
    """
    
    def __init__(
        self,
        modality: str,
        input_dim: int,
        output_dim: int,
        adapter_dim: Optional[int] = None,
        dropout: float = 0.1,
        use_modality_token: bool = True,
        device: Optional[torch.device] = None
    ):
        """
        Initialize modality adapter.
        
        Args:
            modality: Name of the modality
            input_dim: Input embedding dimension
            output_dim: Output projection dimension
            adapter_dim: Dimension of adapter bottleneck (if used)
            dropout: Dropout rate
            use_modality_token: Whether to add modality token
            device: Device to use
        """
        super().__init__()
        self.modality = modality
        self.input_dim = input_dim
        self.output_dim = output_dim
        self.use_modality_token = use_modality_token
        
        # Set device
        self.device = device if device is not None else (
            torch.device('cuda') if torch.cuda.is_available() else torch.device('cpu')
        )
        
        # Create projection layers
        if adapter_dim is not None and adapter_dim > 0:
            # Use an adapter network with bottleneck architecture
            self.projection = nn.Sequential(
                nn.Linear(input_dim, adapter_dim),
                nn.LayerNorm(adapter_dim),
                nn.GELU(),
                nn.Dropout(dropout),
                nn.Linear(adapter_dim, output_dim)
            )
        else:
            # Simple linear projection
            self.projection = nn.Linear(input_dim, output_dim)
        
        # Initialize weights
        self._init_weights()
        
        # Create learnable modality token if enabled
        if use_modality_token:
            self.modality_token = nn.Parameter(torch.randn(1, output_dim))
            nn.init.normal_(self.modality_token, mean=0.0, std=0.02)
        
        # Move to device
        self.to(device)
        
        logger.debug(f"Initialized ModalityAdapter for {modality}: {input_dim} -> {output_dim}")
    
    def _init_weights(self):
        """Initialize weights for projection layers."""
        if isinstance(self.projection, nn.Sequential):
            # Initialize bottleneck layers
            for module in self.projection:
                if isinstance(module, nn.Linear):
                    nn.init.xavier_uniform_(module.weight)
                    if module.bias is not None:
                        nn.init.zeros_(module.bias)
        else:
            # Initialize single linear layer
            nn.init.xavier_uniform_(self.projection.weight)
            if self.projection.bias is not None:
                nn.init.zeros_(self.projection.bias)
    
    def forward(self, embedding: torch.Tensor) -> torch.Tensor:
        """
        Process modality embedding through adapter.
        
        Args:
            embedding: Input embedding tensor [batch_size, seq_len, input_dim]
                      or [batch_size, input_dim]
            
        Returns:
            torch.Tensor: Adapted embedding [batch_size, seq_len, output_dim]
                         or [batch_size, output_dim]
        """
        # Check if input is a single vector or a sequence
        is_sequence = embedding.dim() > 2
        
        if not is_sequence:
            # Add sequence dimension for consistent processing
            embedding = embedding.unsqueeze(1)  # [batch_size, 1, input_dim]
        
        # Project to output dimension
        projected = self.projection(embedding)  # [batch_size, seq_len, output_dim]
        
        # Add modality token if enabled
        if self.use_modality_token:
            # Expand modality token to batch size
            token = self.modality_token.expand(embedding.size(0), -1)  # [batch_size, output_dim]
            
            if is_sequence:
                # For sequence input, add token as first element
                token = token.unsqueeze(1)  # [batch_size, 1, output_dim]
                projected = torch.cat([token, projected], dim=1)  # [batch_size, seq_len+1, output_dim]
            else:
                # For vector input, add token via addition (conditioned embedding)
                projected = projected + token.unsqueeze(1)  # [batch_size, 1, output_dim]
        
        # Remove sequence dimension if input was a single vector
        if not is_sequence and self.use_modality_token is False:
            projected = projected.squeeze(1)  # [batch_size, output_dim]
        
        return projected


class CrossModalAttention(nn.Module):
    """
    Cross-modal attention mechanism for integrating different modalities.
    
    This module implements attention between different modalities,
    allowing each modality to attend to relevant parts of other modalities.
    """
    
    def __init__(
        self,
        embed_dim: int,
        num_heads: int = 8,
        dropout: float = 0.1,
        device: Optional[torch.device] = None
    ):
        """
        Initialize cross-modal attention.
        
        Args:
            embed_dim: Embedding dimension
            num_heads: Number of attention heads
            dropout: Dropout rate
            device: Device to use
        """
        super().__init__()
        self.embed_dim = embed_dim
        self.num_heads = num_heads
        
        # Check that embed_dim is divisible by num_heads
        if embed_dim % num_heads != 0:
            raise ValueError(f"embed_dim ({embed_dim}) must be divisible by num_heads ({num_heads})")
        
        # Set device
        self.device = device if device is not None else (
            torch.device('cuda') if torch.cuda.is_available() else torch.device('cpu')
        )
        
        # Dimension per head
        self.head_dim = embed_dim // num_heads
        self.scale = self.head_dim ** -0.5
        
        # Linear projections for queries, keys, values
        self.q_proj = nn.Linear(embed_dim, embed_dim)
        self.k_proj = nn.Linear(embed_dim, embed_dim)
        self.v_proj = nn.Linear(embed_dim, embed_dim)
        
        # Output projection
        self.out_proj = nn.Linear(embed_dim, embed_dim)
        
        # Dropout for attention weights
        self.dropout = nn.Dropout(dropout)
        
        # Initialize weights
        self._init_weights()
        
        # Move to device
        self.to(device)
        
        logger.debug(f"Initialized CrossModalAttention with {num_heads} heads, embed_dim={embed_dim}")
    
    def _init_weights(self):
        """Initialize weights for projection layers."""
        for proj in [self.q_proj, self.k_proj, self.v_proj, self.out_proj]:
            nn.init.xavier_uniform_(proj.weight)
            nn.init.zeros_(proj.bias)
    
    def forward(
        self,
        query: torch.Tensor,
        key: torch.Tensor,
        value: torch.Tensor,
        attn_mask: Optional[torch.Tensor] = None,
        key_padding_mask: Optional[torch.Tensor] = None
    ) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Compute cross-modal attention.
        
        Args:
            query: Query tensor [batch_size, tgt_len, embed_dim]
            key: Key tensor [batch_size, src_len, embed_dim]
            value: Value tensor [batch_size, src_len, embed_dim]
            attn_mask: Optional attention mask [tgt_len, src_len]
            key_padding_mask: Optional key padding mask [batch_size, src_len]
            
        Returns:
            Tuple[torch.Tensor, torch.Tensor]: 
                - Output tensor [batch_size, tgt_len, embed_dim]
                - Attention weights [batch_size, num_heads, tgt_len, src_len]
        """
        batch_size, tgt_len, embed_dim = query.size()
        src_len = key.size(1)
        
        # Check dimensions
        assert embed_dim == self.embed_dim, f"Expected embed_dim {self.embed_dim}, got {embed_dim}"
        
        # Apply linear projections and reshape for multi-head attention
        q = self.q_proj(query).view(batch_size, tgt_len, self.num_heads, self.head_dim).transpose(1, 2)
        k = self.k_proj(key).view(batch_size, src_len, self.num_heads, self.head_dim).transpose(1, 2)
        v = self.v_proj(value).view(batch_size, src_len, self.num_heads, self.head_dim).transpose(1, 2)
        
        # Compute attention scores
        attn_weights = torch.matmul(q, k.transpose(-2, -1)) * self.scale
        
        # Apply attention mask if provided
        if attn_mask is not None:
            # Expand mask to match attention weights shape
            if attn_mask.dim() == 2:
                attn_mask = attn_mask.unsqueeze(0).unsqueeze(0)
            elif attn_mask.dim() == 3:
                attn_mask = attn_mask.unsqueeze(1)
            
            attn_weights = attn_weights + attn_mask
        
        # Apply key padding mask if provided
        if key_padding_mask is not None:
            attn_weights = attn_weights.masked_fill(
                key_padding_mask.unsqueeze(1).unsqueeze(2),
                float('-inf')
            )
        
        # Apply softmax and dropout
        attn_weights = F.softmax(attn_weights, dim=-1)
        attn_weights = self.dropout(attn_weights)
        
        # Apply attention to values
        output = torch.matmul(attn_weights, v)
        
        # Reshape and apply output projection
        output = output.transpose(1, 2).contiguous().view(batch_size, tgt_len, embed_dim)
        output = self.out_proj(output)
        
        return output, attn_weights


class BilinearFusion(nn.Module):
    """
    Bilinear fusion module for combining modalities.
    
    This module implements bilinear pooling to capture pairwise interactions
    between modality features, followed by a low-rank approximation for
    computational efficiency.
    """
    
    def __init__(
        self,
        modality_dims: Dict[str, int],
        output_dim: int,
        rank: int = 8,
        device: Optional[torch.device] = None
    ):
        """
        Initialize bilinear fusion module.
        
        Args:
            modality_dims: Dictionary mapping modality names to dimensions
            output_dim: Output dimension
            rank: Rank of bilinear approximation
            device: Device to use
        """
        super().__init__()
        self.modality_dims = modality_dims
        self.output_dim = output_dim
        self.rank = rank
        
        # Set device
        self.device = device if device is not None else (
            torch.device('cuda') if torch.cuda.is_available() else torch.device('cpu')
        )
        
        # Create projection matrices for each modality
        self.projection_U = nn.ModuleDict()
        self.projection_V = nn.ModuleDict()
        
        for modality, dim in modality_dims.items():
            self.projection_U[modality] = nn.Linear(dim, rank * output_dim)
            self.projection_V[modality] = nn.Linear(dim, rank)
        
        # Initialize weights
        self._init_weights()
        
        # Move to device
        self.to(device)
        
        logger.debug(f"Initialized BilinearFusion with rank={rank}, output_dim={output_dim}")
    
    def _init_weights(self):
        """Initialize weights for projection matrices."""
        for modality in self.modality_dims:
            nn.init.xavier_uniform_(self.projection_U[modality].weight)
            nn.init.zeros_(self.projection_U[modality].bias)
            nn.init.xavier_uniform_(self.projection_V[modality].weight)
            nn.init.zeros_(self.projection_V[modality].bias)
    
    def forward(self, embeddings: Dict[str, torch.Tensor]) -> torch.Tensor:
        """
        Compute bilinear fusion of modality embeddings.
        
        Args:
            embeddings: Dictionary mapping modality names to embedding tensors
            
        Returns:
            torch.Tensor: Fused embedding tensor [batch_size, output_dim]
        """
        batch_size = next(iter(embeddings.values())).size(0)
        
        # Initialize fusion output
        fusion = torch.zeros(batch_size, self.output_dim, device=self.device)
        
        # Process each pair of modalities
        modalities = list(embeddings.keys())
        
        for i, mod_i in enumerate(modalities):
            for j, mod_j in enumerate(modalities):
                if i < j:  # Process each pair only once
                    # Get embeddings
                    emb_i = embeddings[mod_i]
                    emb_j = embeddings[mod_j]
                    
                    # Ensure embeddings are 2D (batch_size x dim)
                    if emb_i.dim() > 2:
                        emb_i = emb_i.mean(dim=1)  # Average over sequence dimension
                    if emb_j.dim() > 2:
                        emb_j = emb_j.mean(dim=1)  # Average over sequence dimension
                    
                    # Project to factors
                    U_i = self.projection_U[mod_i](emb_i).view(batch_size, self.output_dim, self.rank)
                    V_i = self.projection_V[mod_i](emb_i).unsqueeze(1)  # [batch_size, 1, rank]
                    
                    U_j = self.projection_U[mod_j](emb_j).view(batch_size, self.output_dim, self.rank)
                    V_j = self.projection_V[mod_j](emb_j).unsqueeze(1)  # [batch_size, 1, rank]
                    
                    # Compute bilinear terms
                    bilinear_ij = torch.bmm(U_i, V_j.transpose(1, 2)).squeeze(2)  # [batch_size, output_dim]
                    bilinear_ji = torch.bmm(U_j, V_i.transpose(1, 2)).squeeze(2)  # [batch_size, output_dim]
                    
                    # Add to fusion output
                    fusion = fusion + bilinear_ij + bilinear_ji
        
        return fusion


class FiLMFusion(nn.Module):
    """
    Feature-wise Linear Modulation (FiLM) for fusion of modalities.
    
    This module implements FiLM conditioning, where features from one
    modality are used to modulate (scale and shift) features from another.
    """
    
    def __init__(
        self,
        base_modality: str,
        conditioning_modalities: List[str],
        modality_dims: Dict[str, int],
        output_dim: int,
        device: Optional[torch.device] = None
    ):
        """
        Initialize FiLM fusion module.
        
        Args:
            base_modality: Name of the base modality to be modulated
            conditioning_modalities: List of modalities used for conditioning
            modality_dims: Dictionary mapping modality names to dimensions
            output_dim: Output dimension
            device: Device to use
        """
        super().__init__()
        self.base_modality = base_modality
        self.conditioning_modalities = conditioning_modalities
        self.modality_dims = modality_dims
        self.output_dim = output_dim
        
        # Set device
        self.device = device if device is not None else (
            torch.device('cuda') if torch.cuda.is_available() else torch.device('cpu')
        )
        
        # Projection for base modality
        self.base_projection = nn.Linear(modality_dims[base_modality], output_dim)
        
        # Create FiLM generators for each conditioning modality
        self.film_generators = nn.ModuleDict()
        
        for modality in conditioning_modalities:
            # FiLM generator produces scaling and shifting parameters
            self.film_generators[modality] = nn.Sequential(
                nn.Linear(modality_dims[modality], 512),
                nn.ReLU(),
                nn.Linear(512, output_dim * 2)  # gamma and beta
            )
        
        # Initialize weights
        self._init_weights()
        
        # Move to device
        self.to(device)
        
        logger.debug(f"Initialized FiLMFusion with base_modality={base_modality}, output_dim={output_dim}")
    
    def _init_weights(self):
        """Initialize weights for projection and FiLM generator."""
        # Initialize base projection
        nn.init.xavier_uniform_(self.base_projection.weight)
        nn.init.zeros_(self.base_projection.bias)
        
        # Initialize FiLM generators
        for modality in self.conditioning_modalities:
            for layer in self.film_generators[modality]:
                if isinstance(layer, nn.Linear):
                    nn.init.xavier_uniform_(layer.weight)
                    nn.init.zeros_(layer.bias)
                    
                    # Initialize the last layer specially for gamma ~ 1, beta ~ 0
                    if layer == self.film_generators[modality][-1]:
                        nn.init.zeros_(layer.weight)
                        # Initialize bias for gamma to 1 and beta to 0
                        nn.init.ones_(layer.bias[:self.output_dim])
                        nn.init.zeros_(layer.bias[self.output_dim:])
    
    def forward(self, embeddings: Dict[str, torch.Tensor]) -> torch.Tensor:
        """
        Compute FiLM fusion of modality embeddings.
        
        Args:
            embeddings: Dictionary mapping modality names to embedding tensors
            
        Returns:
            torch.Tensor: Fused embedding tensor [batch_size, output_dim]
        """
        # Check if base modality is available
        if self.base_modality not in embeddings:
            raise ValueError(f"Base modality {self.base_modality} not found in embeddings")
        
        # Get base modality embedding
        base_emb = embeddings[self.base_modality]
        
        # Ensure base embedding is 2D (batch_size x dim)
        if base_emb.dim() > 2:
            base_emb = base_emb.mean(dim=1)  # Average over sequence dimension
        
        # Project base modality
        projected_base = self.base_projection(base_emb)  # [batch_size, output_dim]
        
        # Apply FiLM conditioning from each modality
        result = projected_base
        
        for modality in self.conditioning_modalities:
            if modality in embeddings:
                # Get conditioning embedding
                cond_emb = embeddings[modality]
                
                # Ensure conditioning embedding is 2D (batch_size x dim)
                if cond_emb.dim() > 2:
                    cond_emb = cond_emb.mean(dim=1)  # Average over sequence dimension
                
                # Generate FiLM parameters
                film_params = self.film_generators[modality](cond_emb)  # [batch_size, output_dim*2]
                gamma = film_params[:, :self.output_dim]  # [batch_size, output_dim]
                beta = film_params[:, self.output_dim:]  # [batch_size, output_dim]
                
                # Apply FiLM conditioning
                result = gamma * result + beta
        
        return result


class ContextGating(nn.Module):
    """
    Context Gating mechanism for adaptive feature recalibration.
    
    This module implements a gating mechanism that learns to modulate
    feature activations based on context, enhancing or suppressing
    different features depending on their relevance.
    """
    
    def __init__(
        self,
        embedding_dim: int,
        device: Optional[torch.device] = None
    ):
        """
        Initialize context gating module.
        
        Args:
            embedding_dim: Input/output embedding dimension
            device: Device to use
        """
        super().__init__()
        self.embedding_dim = embedding_dim
        
        # Set device
        self.device = device if device is not None else (
            torch.device('cuda') if torch.cuda.is_available() else torch.device('cpu')
        )
        
        # Context gating transformation
        self.gating = nn.Sequential(
            nn.Linear(embedding_dim, embedding_dim),
            nn.Sigmoid()
        )
        
        # Initialize weights
        nn.init.xavier_uniform_(self.gating[0].weight)
        nn.init.zeros_(self.gating[0].bias)
        
        # Move to device
        self.to(device)
        
        logger.debug(f"Initialized ContextGating with embedding_dim={embedding_dim}")
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Apply context gating to input embeddings.
        
        Args:
            x: Input embedding tensor [batch_size, embedding_dim] or [batch_size, seq_len, embedding_dim]
            
        Returns:
            torch.Tensor: Gated embedding tensor with same shape as input
        """
        # Handle sequence inputs
        original_shape = x.shape
        is_sequence = len(original_shape) > 2
        
        if is_sequence:
            # Reshape to 2D: [batch_size*seq_len, embedding_dim]
            x = x.view(-1, self.embedding_dim)
            
        # Compute gating weights
        gates = self.gating(x)
        
        # Apply gating
        gated = x * gates
        
        # Restore original shape if needed
        if is_sequence:
            gated = gated.view(original_shape)
            
        return gated


class MixtureOfExperts(nn.Module):
    """
    Mixture of Experts fusion module.
    
    This module implements a Mixture of Experts approach for fusing
    modalities, where multiple "expert" networks process the inputs
    and their outputs are combined using learned weightings.
    """
    
    def __init__(
        self,
        input_dim: int,
        output_dim: int,
        num_experts: int = 4,
        hidden_dim: Optional[int] = None,
        dropout: float = 0.1,
        device: Optional[torch.device] = None
    ):
        """
        Initialize Mixture of Experts module.
        
        Args:
            input_dim: Input embedding dimension
            output_dim: Output embedding dimension
            num_experts: Number of expert networks
            hidden_dim: Hidden dimension in expert networks
            dropout: Dropout rate
            device: Device to use
        """
        super().__init__()
        self.input_dim = input_dim
        self.output_dim = output_dim
        self.num_experts = num_experts
        self.hidden_dim = hidden_dim or input_dim
        
        # Set device
        self.device = device if device is not None else (
            torch.device('cuda') if torch.cuda.is_available() else torch.device('cpu')
        )
        
        # Create expert networks
        self.experts = nn.ModuleList([
            nn.Sequential(
                nn.Linear(input_dim, self.hidden_dim),
                nn.LayerNorm(self.hidden_dim),
                nn.ReLU(),
                nn.Dropout(dropout),
                nn.Linear(self.hidden_dim, output_dim)
            )
            for _ in range(num_experts)
        ])
        
        # Create gating network to determine expert weights
        self.gating = nn.Sequential(
            nn.Linear(input_dim, 256),
            nn.ReLU(),
            nn.Linear(256, num_experts),
            nn.Softmax(dim=-1)
        )
        
        # Initialize weights
        self._init_weights()
        
        # Move to device
        self.to(device)
        
        logger.debug(f"Initialized MixtureOfExperts with {num_experts} experts")
    
    def _init_weights(self):
        """Initialize weights for expert and gating networks."""
        # Initialize expert networks
        for expert in self.experts:
            for layer in expert:
                if isinstance(layer, nn.Linear):
                    nn.init.xavier_uniform_(layer.weight)
                    nn.init.zeros_(layer.bias)
        
        # Initialize gating network
        for layer in self.gating:
            if isinstance(layer, nn.Linear):
                nn.init.xavier_uniform_(layer.weight)
                nn.init.zeros_(layer.bias)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Apply Mixture of Experts fusion.
        
        Args:
            x: Input embedding tensor [batch_size, input_dim]
            
        Returns:
            torch.Tensor: Output embedding tensor [batch_size, output_dim]
        """
        # Get expert weights from gating network
        expert_weights = self.gating(x)  # [batch_size, num_experts]
        
        # Compute outputs from each expert
        expert_outputs = torch.stack([expert(x) for expert in self.experts])  # [num_experts, batch_size, output_dim]
        
        # Combine expert outputs using weights
        # Transpose to [batch_size, num_experts, output_dim]
        expert_outputs = expert_outputs.transpose(0, 1)
        
        # Expand weights for broadcasting
        expert_weights = expert_weights.unsqueeze(-1)  # [batch_size, num_experts, 1]
        
        # Weighted sum of expert outputs
        output = torch.sum(expert_outputs * expert_weights, dim=1)  # [batch_size, output_dim]
        
        return output


class MultiModalFusionNetwork(nn.Module):
    """
    Multi-modal fusion network.
    
    This module combines embeddings from multiple modalities into
    a unified representation using the specified fusion method.
    """
    
    def __init__(
        self,
        modality_dims: Dict[str, int],
        projection_dim: int,
        output_dim: int,
        fusion_method: str = "concatenate",
        fusion_layers: Optional[List[int]] = None,
        dropout: float = 0.1,
        use_expert_weighting: bool = False,
        num_experts: int = 4,
        enable_context_gating: bool = False,
        device: Optional[torch.device] = None
    ):
        """
        Initialize multi-modal fusion network.
        
        Args:
            modality_dims: Dictionary mapping modality names to dimensions
            projection_dim: Dimension for projecting each modality
            output_dim: Dimension of final fusion output
            fusion_method: Method for fusing modalities
            fusion_layers: Dimensions of intermediate layers in fusion network
            dropout: Dropout rate
            use_expert_weighting: Whether to use mixture-of-experts for weighting
            num_experts: Number of expert networks for MoE fusion
            enable_context_gating: Whether to use context gating mechanism
            device: Device to use
        """
        super().__init__()
        self.modality_dims = modality_dims
        self.projection_dim = projection_dim
        self.output_dim = output_dim
        self.fusion_method = fusion_method
        self.fusion_layers = fusion_layers or [1024, 512]
        self.use_expert_weighting = use_expert_weighting
        
        # Set device
        self.device = device if device is not None else (
            torch.device('cuda') if torch.cuda.is_available() else torch.device('cpu')
        )
        
        # Create modality adapters
        self.adapters = nn.ModuleDict()
        
        for modality, dim in modality_dims.items():
            self.adapters[modality] = ModalityAdapter(
                modality=modality,
                input_dim=dim,
                output_dim=projection_dim,
                dropout=dropout,
                use_modality_token=True,
                device=self.device
            )
        
        # Create fusion component based on method
        if fusion_method == "concatenate":
            # Concatenation fusion
            concat_dim = projection_dim * len(modality_dims)
            
            # Create fusion network
            fusion_layers = [concat_dim] + self.fusion_layers + [output_dim]
            fusion_modules = []
            
            for i in range(len(fusion_layers) - 1):
                fusion_modules.extend([
                    nn.Linear(fusion_layers[i], fusion_layers[i+1]),
                    nn.LayerNorm(fusion_layers[i+1]),
                    nn.ReLU(),
                    nn.Dropout(dropout)
                ])
            
            # Remove activation and dropout after final layer
            if len(fusion_modules) > 3:
                fusion_modules = fusion_modules[:-2]
                
            self.fusion_network = nn.Sequential(*fusion_modules)
            
        elif fusion_method == "attention":
            # Attention-based fusion
            self.attention_fusion = nn.MultiheadAttention(
                embed_dim=projection_dim,
                num_heads=8,
                dropout=dropout,
                batch_first=True
            )
            
            # Projection to output dimension
            self.fusion_proj = nn.Linear(projection_dim, output_dim)
            
        elif fusion_method == "bilinear":
            # Bilinear fusion
            self.bilinear_fusion = BilinearFusion(
                modality_dims={mod: projection_dim for mod in modality_dims},
                output_dim=output_dim,
                rank=8,
                device=self.device
            )
            
        elif fusion_method == "gated":
            # Gated fusion
            self.gated_fusion = nn.ModuleDict({
                mod: nn.Sequential(
                    nn.Linear(projection_dim, projection_dim),
                    nn.Sigmoid()
                ) for mod in modality_dims
            })
            
            # Projection to output dimension
            self.fusion_proj = nn.Linear(projection_dim, output_dim)
            
        elif fusion_method == "film":
            # FiLM fusion
            # Use the first modality as base, others as conditioning
            modalities = list(modality_dims.keys())
            base_modality = modalities[0]
            conditioning_modalities = modalities[1:]
            
            self.film_fusion = FiLMFusion(
                base_modality=base_modality,
                conditioning_modalities=conditioning_modalities,
                modality_dims={mod: projection_dim for mod in modality_dims},
                output_dim=output_dim,
                device=self.device
            )
            
        elif fusion_method == "sum" or fusion_method == "average":
            # Sum or average fusion (simple projection to output)
            self.fusion_proj = nn.Linear(projection_dim, output_dim)
            
        elif fusion_method == "moe":
            # Mixture of Experts fusion
            # Concatenate modality features first
            self.concat_dim = projection_dim * len(modality_dims)
            
            self.moe_fusion = MixtureOfExperts(
                input_dim=self.concat_dim,
                output_dim=output_dim,
                num_experts=num_experts,
                dropout=dropout,
                device=self.device
            )
            
        elif fusion_method == "bottleneck":
            # Bottleneck fusion (similar to concatenate but with a bottleneck)
            concat_dim = projection_dim * len(modality_dims)
            bottleneck_dim = min(512, concat_dim // 2)
            
            self.fusion_network = nn.Sequential(
                nn.Linear(concat_dim, bottleneck_dim),
                nn.LayerNorm(bottleneck_dim),
                nn.ReLU(),
                nn.Dropout(dropout),
                nn.Linear(bottleneck_dim, output_dim)
            )
            
        else:
            raise ValueError(f"Unknown fusion method: {fusion_method}")
        
        # Apply Mixture of Experts weighting if enabled
        if use_expert_weighting and fusion_method != "moe":
            concat_dim = projection_dim * len(modality_dims)
            
            self.modality_moe = MixtureOfExperts(
                input_dim=concat_dim,
                output_dim=projection_dim * len(modality_dims),
                num_experts=num_experts,
                dropout=dropout,
                device=self.device
            )
        
        # Add context gating if enabled
        self.context_gating = None
        if enable_context_gating:
            self.context_gating = ContextGating(
                embedding_dim=output_dim,
                device=self.device
            )
        
        # Initialize weights
        self._init_weights()
        
        # Move to device
        self.to(device)
        
        logger.info(f"Initialized MultiModalFusionNetwork with fusion_method={fusion_method}")
    
    def _init_weights(self):
        """Initialize weights for fusion network."""
        # Find and initialize linear layers
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight)
                if module.bias is not None:
                    nn.init.zeros_(module.bias)
    
    def forward(self, modality_embeddings: Dict[str, torch.Tensor]) -> torch.Tensor:
        """
        Compute fusion of multiple modality embeddings.
        
        Args:
            modality_embeddings: Dictionary mapping modality names to embedding tensors
            
        Returns:
            torch.Tensor: Fused multi-modal embedding [batch_size, output_dim]
        """
        # Check that we have at least one modality
        if not modality_embeddings:
            raise ValueError("Empty modality_embeddings dictionary")
        
        # Apply modality adapters
        adapted_embeddings = {}
        
        for modality, embedding in modality_embeddings.items():
            if modality in self.adapters:
                adapted = self.adapters[modality](embedding)
                
                # If output is sequence, take mean
                if adapted.dim() > 2:
                    # Mean pooling over sequence dimension
                    adapted = adapted.mean(dim=1)
                
                adapted_embeddings[modality] = adapted
        
        # Apply fusion based on method
        if self.fusion_method == "concatenate" or self.fusion_method == "bottleneck":
            # Concatenate modality embeddings
            modalities = sorted(adapted_embeddings.keys())
            concat_embed = torch.cat([adapted_embeddings[mod] for mod in modalities], dim=1)
            
            # Apply MoE weighting if enabled
            if self.use_expert_weighting and hasattr(self, 'modality_moe'):
                concat_embed = self.modality_moe(concat_embed)
            
            # Apply fusion network
            fused = self.fusion_network(concat_embed)
            
        elif self.fusion_method == "attention":
            # Attention-based fusion
            # Stack modality embeddings
            modalities = sorted(adapted_embeddings.keys())
            stacked_embeds = torch.stack([adapted_embeddings[mod] for mod in modalities], dim=1)
            
            # Self-attention over modalities
            fused_attn, _ = self.attention_fusion(stacked_embeds, stacked_embeds, stacked_embeds)
            
            # Mean pooling over modality dimension
            fused = fused_attn.mean(dim=1)
            
            # Project to output dimension
            fused = self.fusion_proj(fused)
            
        elif self.fusion_method == "bilinear":
            # Bilinear fusion
            fused = self.bilinear_fusion(adapted_embeddings)
            
        elif self.fusion_method == "gated":
            # Gated fusion
            modalities = sorted(adapted_embeddings.keys())
            
            # Apply gating to each modality
            gated_embeds = []
            for mod in modalities:
                gate = self.gated_fusion[mod](adapted_embeddings[mod])
                gated = adapted_embeddings[mod] * gate
                gated_embeds.append(gated)
            
            # Sum gated embeddings
            fused = sum(gated_embeds)
            
            # Project to output dimension
            fused = self.fusion_proj(fused)
            
        elif self.fusion_method == "film":
            # FiLM fusion
            fused = self.film_fusion(adapted_embeddings)
            
        elif self.fusion_method == "sum":
            # Sum fusion
            modalities = sorted(adapted_embeddings.keys())
            fused = sum(adapted_embeddings[mod] for mod in modalities)
            
            # Project to output dimension
            fused = self.fusion_proj(fused)
            
        elif self.fusion_method == "average":
            # Average fusion
            modalities = sorted(adapted_embeddings.keys())
            fused = sum(adapted_embeddings[mod] for mod in modalities) / len(modalities)
            
            # Project to output dimension
            fused = self.fusion_proj(fused)
            
        elif self.fusion_method == "moe":
            # Mixture of Experts fusion
            # Concatenate modality embeddings
            modalities = sorted(adapted_embeddings.keys())
            concat_embed = torch.cat([adapted_embeddings[mod] for mod in modalities], dim=1)
            
            # Apply MoE fusion
            fused = self.moe_fusion(concat_embed)
            
        else:
            raise ValueError(f"Unknown fusion method: {self.fusion_method}")
        
        # Apply context gating if enabled
        if self.context_gating is not None:
            fused = self.context_gating(fused)
        
        return fused


class MultiModalEncoder(nn.Module):
    """
    Multi-modal encoder for ULTRA architecture.
    
    This class provides comprehensive multi-modal encoding capabilities,
    combining inputs from different modalities (text, images, audio, structured data)
    into unified representations at multiple levels of abstraction.
    """
    
    def __init__(self, config: Optional[Union[MultiModalEncoderConfig, dict]] = None):
        """
        Initialize multi-modal encoder.
        
        Args:
            config: Configuration for the encoder
        """
        super().__init__()
        
        # Handle configuration
        if config is None:
            self.config = MultiModalEncoderConfig()
        elif isinstance(config, dict):
            self.config = MultiModalEncoderConfig(**config)
        else:
            self.config = config
            
        # Set device
        self.device = self.config.device
        
        # Set precision
        self.dtype = {
            'float32': torch.float32,
            'float16': torch.float16,
            'bfloat16': torch.bfloat16 if hasattr(torch, 'bfloat16') else torch.float16
        }[self.config.precision]
        
        # Initialize modality encoders
        self.modality_encoders = self._init_modality_encoders()
        
        # Get modality dimensions
        self.modality_dims = {
            modality: encoder.config.embedding_dim 
            for modality, encoder in self.modality_encoders.items()
        }
        
        # Initialize fusion network
        self.fusion_network = MultiModalFusionNetwork(
            modality_dims=self.modality_dims,
            projection_dim=self.config.projection_dim,
            output_dim=self.config.output_dim,
            fusion_method=self.config.fusion_method,
            fusion_layers=self.config.fusion_layers,
            dropout=self.config.dropout,
            use_expert_weighting=self.config.use_expert_weighting,
            num_experts=self.config.num_experts,
            enable_context_gating=self.config.enable_context_gating,
            device=self.device
        )
        
        # Initialize cross-modal attention if enabled
        self.cross_attention = None
        if self.config.cross_attention:
            self.cross_attention = nn.ModuleDict()
            
            # Create cross-attention between each pair of modalities
            modalities = list(self.modality_dims.keys())
            for i, mod_i in enumerate(modalities):
                for j, mod_j in enumerate(modalities):
                    if i != j:  # No self-attention
                        key = f"{mod_i}_to_{mod_j}"
                        self.cross_attention[key] = CrossModalAttention(
                            embed_dim=self.config.projection_dim,
                            num_heads=self.config.cross_attention_heads,
                            dropout=self.config.dropout,
                            device=self.device
                        )
        
        # Cache for encodings
        self.cache_enabled = self.config.cache_size > 0
        self.cache = OrderedDict()
        self.cache_size = self.config.cache_size
        
        # Move to device and set precision
        self.to(self.device)
        if self.dtype != torch.float32:
            self._set_precision(self.dtype)
        
        logger.info(f"Initialized MultiModalEncoder with {len(self.modality_encoders)} modalities, "
                   f"fusion_method={self.config.fusion_method}")
    
    def _init_modality_encoders(self) -> Dict[str, nn.Module]:
        """
        Initialize encoders for each modality.
        
        Returns:
            Dict[str, nn.Module]: Dictionary mapping modality names to encoder modules
        """
        encoders = {}
        
        # Check each modality type and initialize if configuration exists
        for modality, modality_config in self.config.modality_configs.items():
            # Ensure modality_config is a dictionary
            if not isinstance(modality_config, dict):
                logger.warning(f"Invalid configuration for modality {modality}. Skipping.")
                continue
                
            # Update device in configuration
            modality_config['device'] = self.device
                
            # Initialize based on modality type
            if modality == 'text' or modality.startswith('text_'):
                if not _HAS_TEXT_ENCODER:
                    logger.warning(f"TextEncoder not available for modality {modality}. Skipping.")
                    continue
                
                # Create text encoder config
                text_config = TokenizerConfig(**modality_config)
                encoders[modality] = TextEncoder(text_config)
                
            elif modality == 'image' or modality.startswith('image_'):
                if not _HAS_IMAGE_ENCODER:
                    logger.warning(f"ImageEncoder not available for modality {modality}. Skipping.")
                    continue
                
                # Create image encoder config
                image_config = ImageEncoderConfig(**modality_config)
                encoders[modality] = ImageEncoder(image_config)
                
            elif modality == 'audio' or modality.startswith('audio_'):
                if not _HAS_AUDIO_ENCODER:
                    logger.warning(f"AudioEncoder not available for modality {modality}. Skipping.")
                    continue
                
                # Create audio encoder config
                audio_config = AudioEncoderConfig(**modality_config)
                encoders[modality] = AudioEncoder(audio_config)
                
            elif modality == 'data' or modality.startswith('data_'):
                if not _HAS_DATA_ENCODER:
                    logger.warning(f"DataEncoder not available for modality {modality}. Skipping.")
                    continue
                
                # Create data encoder config
                data_config = DataEncoderConfig(**modality_config)
                encoders[modality] = DataEncoder(data_config)
                
            else:
                logger.warning(f"Unknown modality type: {modality}. Skipping.")
        
        if not encoders:
            logger.warning("No modality encoders could be initialized. Using defaults.")
            
            # Create default text encoder if available
            if _HAS_TEXT_ENCODER:
                text_config = TokenizerConfig(device=self.device)
                encoders['text'] = TextEncoder(text_config)
                
            # Create default image encoder if available
            if _HAS_IMAGE_ENCODER:
                image_config = ImageEncoderConfig(device=self.device)
                encoders['image'] = ImageEncoder(image_config)
        
        return encoders
    
    def _set_precision(self, dtype: torch.dtype):
        """
        Set numerical precision for all submodules.
        
        Args:
            dtype: Target data type
        """
        # Skip unsupported dtypes for certain platforms
        if dtype == torch.bfloat16 and not hasattr(torch, 'bfloat16'):
            logger.warning("bfloat16 not supported on this platform. Using float16 instead.")
            dtype = torch.float16
        
        # Convert module parameters
        for module in self.modules():
            if isinstance(module, nn.Module):
                for param in module.parameters():
                    param.data = param.data.to(dtype)
    
    def _compute_hash(self, inputs: Dict[str, Any]) -> str:
        """
        Compute a hash for cache lookup.
        
        Args:
            inputs: Input data to be hashed
            
        Returns:
            str: Hash string
        """
        try:
            # Concatenate modality hashes
            modality_hashes = []
            
            for modality, data in sorted(inputs.items()):
                if isinstance(data, str):
                    # Hash string directly
                    modality_hash = hashlib.sha256(data.encode('utf-8')).hexdigest()
                elif isinstance(data, (np.ndarray, torch.Tensor)):
                    # Hash array data
                    if isinstance(data, torch.Tensor):
                        data = data.detach().cpu().numpy()
                    modality_hash = hashlib.sha256(data.tobytes()).hexdigest()
                else:
                    # Try to pickle and hash other data types
                    try:
                        data_bytes = pickle.dumps(data)
                        modality_hash = hashlib.sha256(data_bytes).hexdigest()
                    except:
                        # If pickling fails, use string representation
                        modality_hash = hashlib.sha256(str(data).encode('utf-8')).hexdigest()
                
                modality_hashes.append(f"{modality}:{modality_hash}")
            
            # Combine modality hashes
            combined_hash = hashlib.sha256(
                "|".join(modality_hashes).encode('utf-8')
            ).hexdigest()
            
            return combined_hash
            
        except Exception as e:
            logger.warning(f"Failed to hash input for caching: {str(e)}")
            return None
    
    def _manage_cache_size(self):
        """Ensure cache doesn't exceed maximum size."""
        if len(self.cache) > self.cache_size:
            # Remove oldest items (OrderedDict preserves insertion order)
            for _ in range(len(self.cache) - self.cache_size):
                self.cache.popitem(last=False)
    
    def encode_modality(self, modality: str, data: Any, **kwargs) -> torch.Tensor:
        """
        Encode data for a specific modality.
        
        Args:
            modality: Modality name
            data: Input data for the modality
            **kwargs: Additional arguments for encoding
            
        Returns:
            torch.Tensor: Encoded modality embedding
        """
        if modality not in self.modality_encoders:
            raise ValueError(f"Unknown modality: {modality}")
        
        # Get encoder for this modality
        encoder = self.modality_encoders[modality]
        
        # Encode data
        embedding = encoder.encode(data, **kwargs)
        
        # Ensure embedding is on the correct device
        if embedding.device != self.device:
            embedding = embedding.to(self.device)
        
        return embedding
    
    def apply_cross_attention(
        self, 
        modality_embeddings: Dict[str, torch.Tensor]
    ) -> Dict[str, torch.Tensor]:
        """
        Apply cross-attention between modalities.
        
        Args:
            modality_embeddings: Dictionary mapping modality names to embeddings
            
        Returns:
            Dict[str, torch.Tensor]: Updated embeddings after cross-attention
        """
        # Skip if cross-attention is not enabled or no cross-attention modules
        if not self.config.cross_attention or not hasattr(self, 'cross_attention'):
            return modality_embeddings
        
        # Skip if we have fewer than 2 modalities
        if len(modality_embeddings) < 2:
            return modality_embeddings
        
        # Apply cross-attention between modalities
        updated_embeddings = {}
        modalities = list(modality_embeddings.keys())
        
        for i, mod_i in enumerate(modalities):
            # Initialize with original embedding
            embedding_i = modality_embeddings[mod_i]
            
            # Ensure embedding is sequence-like
            if embedding_i.dim() == 2:
                embedding_i = embedding_i.unsqueeze(1)  # [batch_size, 1, dim]
            
            # Apply cross-attention with each other modality
            attended_embeddings = [embedding_i]  # Start with self
            
            for j, mod_j in enumerate(modalities):
                if i != j:  # Skip self
                    # Get cross-attention module
                    key = f"{mod_i}_to_{mod_j}"
                    if key in self.cross_attention:
                        embedding_j = modality_embeddings[mod_j]
                        
                        # Ensure embedding is sequence-like
                        if embedding_j.dim() == 2:
                            embedding_j = embedding_j.unsqueeze(1)  # [batch_size, 1, dim]
                        
                        # Apply cross-attention
                        attended, _ = self.cross_attention[key](
                            query=embedding_i,
                            key=embedding_j,
                            value=embedding_j
                        )
                        
                        attended_embeddings.append(attended)
            
            # Combine attended embeddings
            if len(attended_embeddings) > 1:
                combined = torch.cat(attended_embeddings, dim=1)
                
                # Apply mean pooling
                combined = combined.mean(dim=1)  # [batch_size, dim]
            else:
                combined = attended_embeddings[0].squeeze(1)  # [batch_size, dim]
            
            updated_embeddings[mod_i] = combined
        
        return updated_embeddings
    
    def encode(
        self, 
        inputs: Dict[str, Any], 
        return_modality_embeddings: bool = False,
        **kwargs
    ) -> Union[torch.Tensor, Dict[str, torch.Tensor]]:
        """
        Encode multi-modal inputs into a unified embedding.
        
        Args:
            inputs: Dictionary mapping modality names to input data
            return_modality_embeddings: Whether to return individual modality embeddings
            **kwargs: Additional arguments for encoding
            
        Returns:
            torch.Tensor or Dict: Fused multi-modal embedding or dictionary of embeddings
        """
        # Check cache if enabled
        if self.cache_enabled and not kwargs:
            cache_key = self._compute_hash(inputs)
            if cache_key and cache_key in self.cache:
                if return_modality_embeddings:
                    # Return both fused and modality embeddings
                    return self.cache[cache_key]
                else:
                    # Return only fused embedding
                    return self.cache[cache_key]['fused']
        else:
            cache_key = None
        
        # Encode each modality
        modality_embeddings = {}
        
        for modality, data in inputs.items():
            if modality in self.modality_encoders and data is not None:
                # Skip certain modalities based on modality_dropout during training
                if self.training and self.config.modality_dropout > 0:
                    if torch.rand(1).item() < self.config.modality_dropout:
                        logger.debug(f"Dropping modality {modality} due to modality_dropout")
                        continue
                
                # Encode this modality
                embedding = self.encode_modality(modality, data, **kwargs)
                modality_embeddings[modality] = embedding
        
        # Check if we have any embeddings
        if not modality_embeddings:
            logger.warning("No valid modality embeddings could be generated")
            # Return zero tensor with correct shape
            zero_embedding = torch.zeros(
                (1, self.config.output_dim), 
                device=self.device,
                dtype=self.dtype
            )
            
            if return_modality_embeddings:
                return {
                    'fused': zero_embedding,
                    'modality_embeddings': {}
                }
            else:
                return zero_embedding
        
        # Apply cross-attention if enabled
        if self.config.cross_attention:
            modality_embeddings = self.apply_cross_attention(modality_embeddings)
        
        # Apply fusion network
        fused_embedding = self.fusion_network(modality_embeddings)
        
        # Apply normalization if configured
        if self.config.normalize_embeddings:
            fused_embedding = F.normalize(fused_embedding, p=2, dim=-1)
        
        # Cache result if enabled
        if self.cache_enabled and cache_key:
            self.cache[cache_key] = {
                'fused': fused_embedding,
                'modality_embeddings': modality_embeddings
            }
            self._manage_cache_size()
        
        # Return result
        if return_modality_embeddings:
            return {
                'fused': fused_embedding,
                'modality_embeddings': modality_embeddings
            }
        else:
            return fused_embedding
    
    def encode_hierarchical(
        self, 
        document_parts: Dict[str, List[Any]], 
        structure: Optional[List[int]] = None
    ) -> Dict[str, torch.Tensor]:
        """
        Encode a hierarchical multi-modal document.
        
        Args:
            document_parts: Dictionary mapping modalities to lists of document parts
            structure: Optional list indicating the structure (e.g., section indices)
            
        Returns:
            Dict[str, torch.Tensor]: Hierarchical embeddings at different levels
        """
        # Encode each part of each modality
        modality_part_embeddings = {}
        
        for modality, parts in document_parts.items():
            if modality in self.modality_encoders and parts:
                # Encode each part
                part_embeddings = []
                for part in parts:
                    if part is not None:
                        embedding = self.encode_modality(modality, part)
                        part_embeddings.append(embedding)
                
                if part_embeddings:
                    # Stack part embeddings
                    modality_part_embeddings[modality] = torch.stack(part_embeddings)
        
        # Check if we have any embeddings
        if not modality_part_embeddings:
            logger.warning("No valid modality part embeddings could be generated")
            zero_embedding = torch.zeros(
                (1, self.config.output_dim), 
                device=self.device,
                dtype=self.dtype
            )
            return {
                'document': zero_embedding,
                'sections': zero_embedding.unsqueeze(0),
                'parts': zero_embedding.unsqueeze(0)
            }
        
        # Compute part-level fusion
        num_parts = max(emb.size(0) for emb in modality_part_embeddings.values())
        part_embeddings = []
        
        for i in range(num_parts):
            # Get embeddings for this part from each modality
            part_modality_embeddings = {}
            for modality, embeddings in modality_part_embeddings.items():
                if i < embeddings.size(0):
                    part_modality_embeddings[modality] = embeddings[i]
            
            # Apply fusion if we have embeddings
            if part_modality_embeddings:
                fused_part = self.fusion_network(part_modality_embeddings)
                part_embeddings.append(fused_part)
        
        # Stack part embeddings
        parts_tensor = torch.stack(part_embeddings)
        
        # Create section embeddings if structure is provided
        if structure and len(structure) == len(part_embeddings):
            # Group by section
            sections = defaultdict(list)
            for i, section_idx in enumerate(structure):
                sections[section_idx].append(part_embeddings[i])
            
            # Combine parts within each section
            section_embeddings = []
            for section_idx in sorted(sections.keys()):
                section_parts = sections[section_idx]
                if section_parts:
                    # Average parts within section
                    section_emb = torch.stack(section_parts).mean(dim=0)
                    section_embeddings.append(section_emb)
            
            # Stack section embeddings
            sections_tensor = torch.stack(section_embeddings)
        else:
            # Without structure, treat each part as its own section
            sections_tensor = parts_tensor
        
        # Create document embedding (average of sections)
        document_tensor = sections_tensor.mean(dim=0, keepdim=True)
        
        # Apply normalization if configured
        if self.config.normalize_embeddings:
            document_tensor = F.normalize(document_tensor, p=2, dim=-1)
            sections_tensor = F.normalize(sections_tensor, p=2, dim=-1)
            parts_tensor = F.normalize(parts_tensor, p=2, dim=-1)
        
        return {
            'document': document_tensor.squeeze(0),
            'sections': sections_tensor,
            'parts': parts_tensor
        }
    
    def encode_multi_scale(
        self, 
        inputs: Dict[str, Any]
    ) -> Dict[str, Dict[str, torch.Tensor]]:
        """
        Encode multi-modal inputs at multiple scales of abstraction.
        
        Args:
            inputs: Dictionary mapping modality names to input data
            
        Returns:
            Dict[str, Dict[str, torch.Tensor]]: Multi-scale embeddings for each modality and fusion
        """
        # Get multi-scale embeddings for each modality
        modality_multi_scale = {}
        
        for modality, data in inputs.items():
            if modality in self.modality_encoders and data is not None:
                encoder = self.modality_encoders[modality]
                
                # Check if encoder supports multi-scale encoding
                if hasattr(encoder, 'encode_multi_scale'):
                    multi_scale = encoder.encode_multi_scale(data)
                    modality_multi_scale[modality] = multi_scale
        
        # Fuse embeddings at each scale
        fused_multi_scale = {}
        
        # Get all scales from all modalities
        all_scales = set()
        for modality, scales in modality_multi_scale.items():
            all_scales.update(scales.keys())
        
        # Fuse each scale
        for scale in all_scales:
            scale_embeddings = {}
            
            for modality, scales in modality_multi_scale.items():
                if scale in scales:
                    scale_embeddings[modality] = scales[scale]
            
            # Apply fusion if we have embeddings
            if scale_embeddings:
                fused_scale = self.fusion_network(scale_embeddings)
                
                # Apply normalization if configured
                if self.config.normalize_embeddings:
                    fused_scale = F.normalize(fused_scale, p=2, dim=-1)
                
                fused_multi_scale[scale] = fused_scale
        
        return {
            'fused': fused_multi_scale,
            'modalities': modality_multi_scale
        }
    
    def forward(
        self, 
        inputs: Dict[str, Any], 
        **kwargs
    ) -> torch.Tensor:
        """
        Forward pass to encode multi-modal inputs.
        
        Args:
            inputs: Dictionary mapping modality names to input data
            **kwargs: Additional arguments for encoding
            
        Returns:
            torch.Tensor: Fused multi-modal embedding
        """
        return self.encode(inputs, **kwargs)
    
    def clear_cache(self):
        """Clear the encoding cache."""
        self.cache.clear()
        
    def to(self, device):
        """
        Move encoder to specified device.
        
        Args:
            device: Target device
            
        Returns:
            MultiModalEncoder: Self
        """
        self.device = device
        
        # Move modality encoders
        for encoder in self.modality_encoders.values():
            encoder.to(device)
        
        # Move fusion network
        self.fusion_network.to(device)
        
        # Move cross-attention modules
        if hasattr(self, 'cross_attention') and self.cross_attention is not None:
            for attn in self.cross_attention.values():
                attn.to(device)
            
        return super().to(device)
    
    def save(self, directory: str, save_modality_encoders: bool = True):
        """
        Save encoder and configuration.
        
        Args:
            directory: Directory to save in
            save_modality_encoders: Whether to save individual modality encoders
        """
        os.makedirs(directory, exist_ok=True)
        
        # Save configuration
        config_path = os.path.join(directory, "config.json")
        self.config.save(config_path)
        
        # Save fusion network and cross-attention weights
        state_path = os.path.join(directory, "encoder_state.pt")
        state_dict = {
            'fusion_network': self.fusion_network.state_dict()
        }
        
        if hasattr(self, 'cross_attention') and self.cross_attention is not None:
            state_dict['cross_attention'] = self.cross_attention.state_dict()
            
        torch.save(state_dict, state_path)
        
        # Save modality encoders if requested
        if save_modality_encoders:
            encoders_dir = os.path.join(directory, "modality_encoders")
            os.makedirs(encoders_dir, exist_ok=True)
            
            for modality, encoder in self.modality_encoders.items():
                encoder_dir = os.path.join(encoders_dir, modality)
                encoder.save(encoder_dir)
        
        logger.info(f"MultiModalEncoder saved to {directory}")
    
    @classmethod
    def load(cls, directory: str, device=None, load_modality_encoders: bool = True):
        """
        Load encoder from saved files.
        
        Args:
            directory: Directory containing saved encoder
            device: Device to load onto
            load_modality_encoders: Whether to load individual modality encoders
            
        Returns:
            MultiModalEncoder: Loaded encoder
        """
        # Load configuration
        config_path = os.path.join(directory, "config.json")
        config = MultiModalEncoderConfig.load(config_path)
        
        # Override device if specified
        if device is not None:
            config.device = device
        
        # Create instance
        instance = cls(config)
        
        # Load state
        state_path = os.path.join(directory, "encoder_state.pt")
        if os.path.exists(state_path):
            state_dict = torch.load(state_path, map_location=config.device)
            
            # Load fusion network state
            if 'fusion_network' in state_dict:
                instance.fusion_network.load_state_dict(state_dict['fusion_network'])
            
            # Load cross-attention state
            if 'cross_attention' in state_dict and hasattr(instance, 'cross_attention'):
                instance.cross_attention.load_state_dict(state_dict['cross_attention'])
        
        # Load modality encoders if requested
        if load_modality_encoders:
            encoders_dir = os.path.join(directory, "modality_encoders")
            if os.path.exists(encoders_dir):
                # Get modality encoder types
                encoder_types = {
                    'text': TextEncoder if _HAS_TEXT_ENCODER else None,
                    'image': ImageEncoder if _HAS_IMAGE_ENCODER else None,
                    'audio': AudioEncoder if _HAS_AUDIO_ENCODER else None,
                    'data': DataEncoder if _HAS_DATA_ENCODER else None
                }
                
                # Load each modality encoder
                for modality_dir in os.listdir(encoders_dir):
                    encoder_path = os.path.join(encoders_dir, modality_dir)
                    
                    # Determine encoder type from modality name
                    base_modality = modality_dir.split('_')[0]
                    encoder_cls = encoder_types.get(base_modality)
                    
                    if encoder_cls is not None:
                        try:
                            encoder = encoder_cls.load(encoder_path, device=config.device)
                            instance.modality_encoders[modality_dir] = encoder
                        except Exception as e:
                            logger.warning(f"Failed to load encoder for {modality_dir}: {str(e)}")
        
        logger.info(f"MultiModalEncoder loaded from {directory}")
        return instance


# Version information
__version__ = '0.1.0'

# Register encoder class in the global registry
if 'register_encoder' in globals():
    register_encoder(MultiModalEncoder)