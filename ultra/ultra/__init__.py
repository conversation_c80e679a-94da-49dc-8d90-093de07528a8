#!/usr/bin/env python3
"""
ULTRA: Ultimate Learning & Thought Reasoning Architecture
========================================================

Main package initialization for the ULTRA (Ultimate Learning & Thought Reasoning Architecture) system.
This module provides the primary interface to all ULTRA components, manages system initialization,
coordinates inter-component communication, and provides the main API for the entire framework.

The ULTRA system represents a revolutionary approach to artificial intelligence that integrates:
- Neuromorphic computing with biologically-inspired neural networks
- Self-evolving transformer architectures with dynamic attention
- Diffusion-based reasoning in continuous thought spaces
- Meta-cognitive systems with self-critique capabilities
- Emergent consciousness through integrated information processing
- Neuro-symbolic integration for hybrid reasoning
- Self-evolution mechanisms for continuous improvement

This initialization module:
1. <PERSON><PERSON> the complete system lifecycle (initialization, execution, shutdown)
2. Coordinates communication between all 8 core subsystems
3. Provides unified APIs for external access
4. Implements system-wide monitoring and health management
5. Handles distributed processing and resource allocation
6. Manages configuration loading and validation
7. Implements safety protocols and error recovery

Mathematical Foundation:
The system is built upon rigorous mathematical formulations including:
- Neuromorphic dynamics: τ(dV_i(t)/dt) = -V_i(t) + Σ_j w_ij·S_j(t) + I_i^ext(t)
- STDP plasticity: Δw_ij = A_+exp(-Δt/τ_+) if Δt > 0, -A_-exp(Δt/τ_-) if Δt < 0
- Diffusion processes: q(z_t|z_0) = N(z_t; √ᾱ_t z_0, (1-ᾱ_t)I)
- Attention mechanisms: Attention(Q,K,V) = softmax(QK^T/√d_k · M)V
- Integrated information: Φ(X) = min_P∈P I(X_1;X_2|X_P)/min{H(X_1|X_P),H(X_2|X_P)}

Author: ULTRA Development Team
License: MIT
Version: 1.0.0
"""

import sys
import os
import warnings
import logging
import time
import threading
import multiprocessing as mp
import asyncio
import signal
import atexit
import psutil
import traceback
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Union, Any, Callable, Type
from dataclasses import dataclass, field
from enum import Enum, auto
from collections import defaultdict, OrderedDict
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor, as_completed
import queue
import json
import yaml
import numpy as np
import torch
import torch.distributed as dist
from datetime import datetime, timezone

# Set up path for ULTRA modules
ULTRA_ROOT = Path(__file__).parent.parent
sys.path.insert(0, str(ULTRA_ROOT))

# Core system imports
from ultra.utils import (
    MathematicalOperations, NeuromorphicUtils, DiffusionUtils, AttentionUtils,
    GraphUtils, OptimizationUtils, MemoryManager, EvaluationMetrics,
    SystemMonitor, ParallelProcessor, ConfigManager, logger as utils_logger
)

from ultra.utils.config import (
    ULTRAConfig, Environment, OptimizationLevel, HardwareType,
    ConfigurationManager, ConfigurationTemplates, ConfigurationError,
    config_manager, templates
)

# Component imports (these would be implemented in their respective modules)
try:
    from ultra.core_neural import CoreNeuralInterface as CoreNeuralArchitecture
    from ultra.hyper_transformer import HyperDimensionalTransformer  
    from ultra.diffusion_reasoning import DiffusionBasedReasoning
    from ultra.meta_cognitive import MetaCognitiveSystem
    from ultra.neuromorphic_processing import NeuromorphicProcessingLayer
    from ultra.emergent_consciousness import EmergentConsciousnessLattice
    from ultra.neuro_symbolic import NeuroSymbolicIntegration
    from ultra.self_evolution import SelfEvolutionSystem
    from ultra.input_processing import MultiModalInputProcessor
    from ultra.output_generation import MultiModalOutputSynthesis
    from ultra.knowledge_management import KnowledgeManagementSystem
    from ultra.safety import SafetyFramework
    
    COMPONENTS_AVAILABLE = True
except ImportError as e:
    warnings.warn(f"Some ULTRA components not available: {e}")
    COMPONENTS_AVAILABLE = False

# Initialize logging for the main ULTRA system
ultra_logger = logging.getLogger('ultra.main')
ultra_logger.setLevel(logging.INFO)

# Version information
__version__ = "1.0.0"
__author__ = "ULTRA Development Team"
__license__ = "MIT"
__description__ = "Ultimate Learning & Thought Reasoning Architecture"

# =============================================================================
# System State and Health Management
# =============================================================================

class SystemState(Enum):
    """Enumeration of possible system states."""
    UNINITIALIZED = auto()
    INITIALIZING = auto()
    INITIALIZED = auto()
    STARTING = auto()
    RUNNING = auto()
    PAUSING = auto()
    PAUSED = auto()
    RESUMING = auto()
    STOPPING = auto()
    STOPPED = auto()
    ERROR = auto()
    MAINTENANCE = auto()
    UPDATING = auto()
    SHUTTING_DOWN = auto()
    SHUTDOWN = auto()

class ComponentStatus(Enum):
    """Status of individual system components."""
    OFFLINE = auto()
    INITIALIZING = auto()
    ONLINE = auto()
    DEGRADED = auto()
    FAILED = auto()
    MAINTENANCE = auto()

@dataclass
class SystemHealth:
    """Comprehensive system health information."""
    overall_status: SystemState
    component_statuses: Dict[str, ComponentStatus]
    performance_metrics: Dict[str, float]
    resource_usage: Dict[str, float]
    error_counts: Dict[str, int]
    uptime: float
    last_health_check: datetime
    alerts: List[Dict[str, Any]]
    
    def is_healthy(self) -> bool:
        """Determine if the system is in a healthy state."""
        if self.overall_status not in [SystemState.RUNNING, SystemState.PAUSED]:
            return False
        
        # Check if any critical components have failed
        critical_components = [
            'core_neural', 'transformer', 'diffusion_reasoning', 'meta_cognitive'
        ]
        
        for component in critical_components:
            if component in self.component_statuses:
                if self.component_statuses[component] == ComponentStatus.FAILED:
                    return False
        
        # Check resource usage thresholds
        if self.resource_usage.get('memory', 0) > 0.95:  # 95% memory usage
            return False
        if self.resource_usage.get('cpu', 0) > 0.90:     # 90% CPU usage
            return False
        
        return True
    
    def get_health_score(self) -> float:
        """Calculate a health score between 0 and 1."""
        score = 1.0
        
        # Penalize based on component failures
        total_components = len(self.component_statuses)
        if total_components > 0:
            failed_components = sum(1 for status in self.component_statuses.values() 
                                  if status == ComponentStatus.FAILED)
            score *= (1 - failed_components / total_components)
        
        # Penalize based on resource usage
        memory_usage = self.resource_usage.get('memory', 0)
        cpu_usage = self.resource_usage.get('cpu', 0)
        
        if memory_usage > 0.8:
            score *= (1 - (memory_usage - 0.8) / 0.2)
        if cpu_usage > 0.8:
            score *= (1 - (cpu_usage - 0.8) / 0.2)
        
        # Penalize based on errors
        total_errors = sum(self.error_counts.values())
        if total_errors > 0:
            score *= max(0.1, 1 - total_errors / 1000)  # Cap penalty at 90%
        
        return max(0.0, min(1.0, score))

# =============================================================================
# Inter-Component Communication System
# =============================================================================

class MessageType(Enum):
    """Types of inter-component messages."""
    DATA_FLOW = auto()
    CONTROL_SIGNAL = auto()
    STATUS_UPDATE = auto()
    ERROR_REPORT = auto()
    RESOURCE_REQUEST = auto()
    CONFIGURATION_UPDATE = auto()
    SYNCHRONIZATION = auto()

@dataclass
class ComponentMessage:
    """Message structure for inter-component communication."""
    source: str
    destination: str
    message_type: MessageType
    payload: Any
    timestamp: datetime
    message_id: str
    priority: int = 5  # 1=highest, 10=lowest
    requires_response: bool = False
    correlation_id: Optional[str] = None

class MessageBus:
    """
    Central message bus for inter-component communication.
    Implements publish-subscribe pattern with message routing and priority handling.
    """
    
    def __init__(self, max_queue_size: int = 10000):
        self.max_queue_size = max_queue_size
        self.subscribers: Dict[str, List[Callable]] = defaultdict(list)
        self.message_queue = queue.PriorityQueue(maxsize=max_queue_size)
        self.message_history: List[ComponentMessage] = []
        self.running = False
        self.worker_thread: Optional[threading.Thread] = None
        self._lock = threading.RLock()
        self.metrics = {
            'messages_sent': 0,
            'messages_processed': 0,
            'queue_size': 0,
            'average_processing_time': 0.0
        }
    
    def start(self) -> None:
        """Start the message bus processing."""
        with self._lock:
            if not self.running:
                self.running = True
                self.worker_thread = threading.Thread(target=self._process_messages, daemon=True)
                self.worker_thread.start()
                ultra_logger.info("Message bus started")
    
    def stop(self) -> None:
        """Stop the message bus processing."""
        with self._lock:
            if self.running:
                self.running = False
                if self.worker_thread:
                    self.worker_thread.join(timeout=5.0)
                ultra_logger.info("Message bus stopped")
    
    def subscribe(self, topic: str, callback: Callable[[ComponentMessage], None]) -> None:
        """Subscribe to messages for a specific topic."""
        with self._lock:
            self.subscribers[topic].append(callback)
            ultra_logger.debug(f"New subscriber for topic '{topic}': {callback.__name__}")
    
    def unsubscribe(self, topic: str, callback: Callable[[ComponentMessage], None]) -> None:
        """Unsubscribe from messages for a specific topic."""
        with self._lock:
            if topic in self.subscribers and callback in self.subscribers[topic]:
                self.subscribers[topic].remove(callback)
                ultra_logger.debug(f"Unsubscribed from topic '{topic}': {callback.__name__}")
    
    def publish(self, message: ComponentMessage) -> bool:
        """Publish a message to the bus."""
        try:
            # Add to priority queue (lower priority number = higher priority)
            priority_tuple = (message.priority, time.time(), message)
            self.message_queue.put(priority_tuple, block=False)
            
            with self._lock:
                self.metrics['messages_sent'] += 1
                self.metrics['queue_size'] = self.message_queue.qsize()
            
            ultra_logger.debug(f"Message published: {message.source} -> {message.destination}")
            return True
            
        except queue.Full:
            ultra_logger.error("Message queue full, dropping message")
            return False
    
    def _process_messages(self) -> None:
        """Process messages from the queue."""
        while self.running:
            try:
                # Get message with timeout
                priority_tuple = self.message_queue.get(timeout=1.0)
                _, _, message = priority_tuple
                
                start_time = time.time()
                self._deliver_message(message)
                processing_time = time.time() - start_time
                
                with self._lock:
                    self.metrics['messages_processed'] += 1
                    self.metrics['queue_size'] = self.message_queue.qsize()
                    
                    # Update average processing time
                    current_avg = self.metrics['average_processing_time']
                    message_count = self.metrics['messages_processed']
                    self.metrics['average_processing_time'] = (
                        (current_avg * (message_count - 1) + processing_time) / message_count
                    )
                
                # Add to history (keep last 1000 messages)
                self.message_history.append(message)
                if len(self.message_history) > 1000:
                    self.message_history.pop(0)
                
            except queue.Empty:
                continue
            except Exception as e:
                ultra_logger.error(f"Error processing message: {e}")
    
    def _deliver_message(self, message: ComponentMessage) -> None:
        """Deliver message to subscribers."""
        # Deliver to specific destination
        if message.destination in self.subscribers:
            for callback in self.subscribers[message.destination]:
                try:
                    callback(message)
                except Exception as e:
                    ultra_logger.error(f"Error in message callback: {e}")
        
        # Deliver to wildcard subscribers
        if '*' in self.subscribers:
            for callback in self.subscribers['*']:
                try:
                    callback(message)
                except Exception as e:
                    ultra_logger.error(f"Error in wildcard message callback: {e}")
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get message bus metrics."""
        with self._lock:
            return self.metrics.copy()

# =============================================================================
# Resource Management and Allocation
# =============================================================================

@dataclass
class ResourceAllocation:
    """Resource allocation for system components."""
    component_name: str
    cpu_cores: int
    memory_gb: float
    gpu_memory_gb: float
    priority: int
    max_cpu_percent: float
    max_memory_percent: float

class ResourceManager:
    """
    Manages system resources across all ULTRA components.
    Implements dynamic resource allocation based on workload and priorities.
    """
    
    def __init__(self):
        self.total_cpu_cores = psutil.cpu_count()
        self.total_memory_gb = psutil.virtual_memory().total / (1024**3)
        self.total_gpu_memory_gb = self._get_total_gpu_memory()
        
        self.allocations: Dict[str, ResourceAllocation] = {}
        self.resource_usage: Dict[str, Dict[str, float]] = defaultdict(dict)
        self.monitoring_enabled = True
        self._lock = threading.RLock()
        
        ultra_logger.info(f"Resource manager initialized: {self.total_cpu_cores} CPU cores, "
                         f"{self.total_memory_gb:.2f} GB memory, "
                         f"{self.total_gpu_memory_gb:.2f} GB GPU memory")
    
    def _get_total_gpu_memory(self) -> float:
        """Get total GPU memory across all devices."""
        try:
            if torch.cuda.is_available():
                total_memory = 0
                for i in range(torch.cuda.device_count()):
                    total_memory += torch.cuda.get_device_properties(i).total_memory
                return total_memory / (1024**3)  # Convert to GB
            return 0.0
        except Exception:
            return 0.0
    
    def allocate_resources(self, component_name: str, 
                          cpu_cores: Optional[int] = None,
                          memory_gb: Optional[float] = None,
                          gpu_memory_gb: Optional[float] = None,
                          priority: int = 5) -> ResourceAllocation:
        """
        Allocate resources to a component.
        
        Args:
            component_name: Name of the component
            cpu_cores: Number of CPU cores (auto-allocated if None)
            memory_gb: Memory in GB (auto-allocated if None)
            gpu_memory_gb: GPU memory in GB (auto-allocated if None)
            priority: Priority level (1=highest, 10=lowest)
            
        Returns:
            Resource allocation details
        """
        with self._lock:
            # Auto-allocate resources if not specified
            if cpu_cores is None:
                cpu_cores = max(1, self.total_cpu_cores // 8)  # Default to 1/8 of cores
            if memory_gb is None:
                memory_gb = self.total_memory_gb * 0.1  # Default to 10% of memory
            if gpu_memory_gb is None:
                gpu_memory_gb = self.total_gpu_memory_gb * 0.1  # Default to 10% of GPU memory
            
            # Calculate percentage limits
            max_cpu_percent = min(100.0, (cpu_cores / self.total_cpu_cores) * 100)
            max_memory_percent = min(100.0, (memory_gb / self.total_memory_gb) * 100)
            
            allocation = ResourceAllocation(
                component_name=component_name,
                cpu_cores=cpu_cores,
                memory_gb=memory_gb,
                gpu_memory_gb=gpu_memory_gb,
                priority=priority,
                max_cpu_percent=max_cpu_percent,
                max_memory_percent=max_memory_percent
            )
            
            self.allocations[component_name] = allocation
            
            ultra_logger.info(f"Resources allocated to {component_name}: "
                             f"{cpu_cores} cores, {memory_gb:.2f} GB memory, "
                             f"{gpu_memory_gb:.2f} GB GPU memory")
            
            return allocation
    
    def deallocate_resources(self, component_name: str) -> None:
        """Deallocate resources from a component."""
        with self._lock:
            if component_name in self.allocations:
                del self.allocations[component_name]
                ultra_logger.info(f"Resources deallocated from {component_name}")
    
    def update_resource_usage(self, component_name: str, 
                            cpu_percent: float, 
                            memory_percent: float,
                            gpu_memory_percent: float = 0.0) -> None:
        """Update resource usage for a component."""
        with self._lock:
            self.resource_usage[component_name] = {
                'cpu_percent': cpu_percent,
                'memory_percent': memory_percent,
                'gpu_memory_percent': gpu_memory_percent,
                'timestamp': time.time()
            }
    
    def get_system_resource_usage(self) -> Dict[str, float]:
        """Get overall system resource usage."""
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        
        usage = {
            'cpu_percent': cpu_percent,
            'memory_percent': memory.percent,
            'memory_available_gb': memory.available / (1024**3),
            'disk_usage_percent': psutil.disk_usage('/').percent
        }
        
        # Add GPU usage if available
        try:
            if torch.cuda.is_available():
                gpu_memory_used = 0
                for i in range(torch.cuda.device_count()):
                    gpu_memory_used += torch.cuda.memory_allocated(i)
                
                if self.total_gpu_memory_gb > 0:
                    usage['gpu_memory_percent'] = (gpu_memory_used / (1024**3)) / self.total_gpu_memory_gb * 100
        except Exception:
            pass
        
        return usage
    
    def check_resource_violations(self) -> List[str]:
        """Check for resource allocation violations."""
        violations = []
        
        with self._lock:
            for component_name, allocation in self.allocations.items():
                if component_name in self.resource_usage:
                    usage = self.resource_usage[component_name]
                    
                    # Check CPU violations
                    if usage['cpu_percent'] > allocation.max_cpu_percent * 1.2:  # 20% tolerance
                        violations.append(f"{component_name}: CPU usage {usage['cpu_percent']:.1f}% "
                                        f"exceeds allocation {allocation.max_cpu_percent:.1f}%")
                    
                    # Check memory violations
                    if usage['memory_percent'] > allocation.max_memory_percent * 1.2:  # 20% tolerance
                        violations.append(f"{component_name}: Memory usage {usage['memory_percent']:.1f}% "
                                        f"exceeds allocation {allocation.max_memory_percent:.1f}%")
        
        return violations
    
    def optimize_allocations(self) -> None:
        """Optimize resource allocations based on usage patterns."""
        with self._lock:
            # Collect usage statistics
            usage_stats = {}
            for component_name, allocation in self.allocations.items():
                if component_name in self.resource_usage:
                    usage = self.resource_usage[component_name]
                    usage_stats[component_name] = {
                        'cpu_utilization': usage['cpu_percent'] / allocation.max_cpu_percent,
                        'memory_utilization': usage['memory_percent'] / allocation.max_memory_percent,
                        'priority': allocation.priority
                    }
            
            # Implement basic reallocation strategy
            # Components with high utilization and high priority get more resources
            # Components with low utilization get fewer resources
            
            for component_name, stats in usage_stats.items():
                allocation = self.allocations[component_name]
                
                # Adjust CPU allocation
                if stats['cpu_utilization'] > 0.8 and stats['priority'] <= 3:
                    # Increase allocation for high-utilization, high-priority components
                    new_cores = min(allocation.cpu_cores + 1, self.total_cpu_cores // 2)
                    allocation.cpu_cores = new_cores
                    allocation.max_cpu_percent = (new_cores / self.total_cpu_cores) * 100
                
                elif stats['cpu_utilization'] < 0.3 and stats['priority'] >= 7:
                    # Decrease allocation for low-utilization, low-priority components
                    new_cores = max(allocation.cpu_cores - 1, 1)
                    allocation.cpu_cores = new_cores
                    allocation.max_cpu_percent = (new_cores / self.total_cpu_cores) * 100
            
            ultra_logger.info("Resource allocations optimized")

# =============================================================================
# Main ULTRA System Class
# =============================================================================

class ULTRASystem:
    """
    Main ULTRA (Ultimate Learning & Thought Reasoning Architecture) system class.
    
    This class orchestrates all components of the ULTRA system, managing their
    lifecycle, communication, and coordination. It provides the primary interface
    for interacting with the complete ULTRA framework.
    
    The system integrates 8 core subsystems:
    1. Core Neural Architecture - Neuromorphic computing foundation
    2. Hyper-Dimensional Transformer - Advanced attention mechanisms  
    3. Diffusion-Based Reasoning - Continuous thought space navigation
    4. Meta-Cognitive System - Executive control and self-critique
    5. Neuromorphic Processing Layer - Spiking neural networks
    6. Emergent Consciousness Lattice - Integrated information processing
    7. Neuro-Symbolic Integration - Hybrid reasoning capabilities
    8. Self-Evolution System - Continuous self-improvement
    
    Additional supporting systems:
    - Input/Output Processing - Multi-modal data handling
    - Knowledge Management - Memory and learning systems
    - Safety Framework - Ethical and safety constraints
    """
    
    def __init__(self, config: Optional[ULTRAConfig] = None, 
                 config_path: Optional[str] = None,
                 environment: Optional[Environment] = None):
        """
        Initialize the ULTRA system.
        
        Args:
            config: ULTRA configuration object
            config_path: Path to configuration file
            environment: Environment type (development, production, etc.)
        """
        # System state management
        self.state = SystemState.UNINITIALIZED
        self.start_time = time.time()
        self.shutdown_requested = False
        self._lock = threading.RLock()
        
        # Load configuration
        if config is not None:
            self.config = config
        else:
            self.config = config_manager.load_config(config_path, environment)
        
        # Validate configuration
        try:
            self.config.validate()
            ultra_logger.info("Configuration validation successful")
        except Exception as e:
            ultra_logger.error(f"Configuration validation failed: {e}")
            raise ConfigurationError(f"Invalid configuration: {e}")
        
        # Initialize core systems
        self.message_bus = MessageBus(max_queue_size=self.config.batch_size * 100)
        self.resource_manager = ResourceManager()
        self.system_monitor = SystemMonitor()
        self.memory_manager = MemoryManager(
            capacity=getattr(self.config, 'memory_capacity', 10000),
            decay_rate=getattr(self.config, 'memory_decay_rate', 0.01)
        )
        
        # Component storage
        self.components: Dict[str, Any] = {}
        self.component_threads: Dict[str, threading.Thread] = {}
        self.component_status: Dict[str, ComponentStatus] = {}
        
        # Performance tracking
        self.performance_metrics: Dict[str, float] = {}
        self.error_counts: Dict[str, int] = defaultdict(int)
        self.health_history: List[SystemHealth] = []
        
        # Distributed processing
        self.is_distributed = self.config.distributed_processing
        self.rank = 0
        self.world_size = 1
        
        # Register signal handlers
        self._register_signal_handlers()
        
        # Register cleanup on exit
        atexit.register(self.shutdown)
        
        ultra_logger.info(f"ULTRA System initialized with configuration: {self.config.environment.value}")
        self.state = SystemState.INITIALIZED
    
    def initialize_components(self) -> None:
        """Initialize all ULTRA system components."""
        self.state = SystemState.INITIALIZING
        ultra_logger.info("Initializing ULTRA components...")
        
        try:
            # Start core systems
            self.message_bus.start()
            
            # Initialize distributed processing if enabled
            if self.is_distributed:
                self._initialize_distributed_processing()
            
            # Component initialization order (dependencies matter)
            component_init_order = [
                ('input_processing', self._initialize_input_processing),
                ('core_neural', self._initialize_core_neural),
                ('neuromorphic_processing', self._initialize_neuromorphic_processing),
                ('transformer', self._initialize_transformer),
                ('diffusion_reasoning', self._initialize_diffusion_reasoning),
                ('meta_cognitive', self._initialize_meta_cognitive),
                ('consciousness', self._initialize_consciousness),
                ('neuro_symbolic', self._initialize_neuro_symbolic),
                ('knowledge_management', self._initialize_knowledge_management),
                ('self_evolution', self._initialize_self_evolution),
                ('output_generation', self._initialize_output_generation),
                ('safety', self._initialize_safety)
            ]
            
            # Initialize components in order
            for component_name, init_func in component_init_order:
                try:
                    ultra_logger.info(f"Initializing {component_name}...")
                    component = init_func()
                    
                    if component is not None:
                        self.components[component_name] = component
                        self.component_status[component_name] = ComponentStatus.ONLINE
                        
                        # Allocate resources
                        self.resource_manager.allocate_resources(
                            component_name, 
                            priority=self._get_component_priority(component_name)
                        )
                        
                        ultra_logger.info(f"{component_name} initialized successfully")
                    else:
                        self.component_status[component_name] = ComponentStatus.FAILED
                        ultra_logger.warning(f"{component_name} initialization returned None")
                        
                except Exception as e:
                    self.component_status[component_name] = ComponentStatus.FAILED
                    self.error_counts[component_name] += 1
                    ultra_logger.error(f"Failed to initialize {component_name}: {e}")
                    
                    # Decide whether to continue or abort based on component criticality
                    if self._is_critical_component(component_name):
                        ultra_logger.error("Critical component failed, aborting initialization")
                        raise
                    else:
                        ultra_logger.warning(f"Non-critical component {component_name} failed, continuing")
            
            # Set up inter-component communication
            self._setup_component_communication()
            
            # Validate system integrity
            self._validate_system_integrity()
            
            ultra_logger.info("All components initialized successfully")
            self.state = SystemState.INITIALIZED
            
        except Exception as e:
            self.state = SystemState.ERROR
            ultra_logger.error(f"Component initialization failed: {e}")
            raise
    
    def start(self) -> None:
        """Start the ULTRA system and begin processing."""
        if self.state != SystemState.INITIALIZED:
            raise RuntimeError(f"Cannot start system in state {self.state}")
        
        self.state = SystemState.STARTING
        ultra_logger.info("Starting ULTRA system...")
        
        try:
            # Start system monitoring
            self._start_monitoring()
            
            # Start component processing threads
            self._start_component_threads()
            
            # Perform initial system health check
            health = self._perform_health_check()
            if not health.is_healthy():
                ultra_logger.warning(f"System health check failed: score={health.get_health_score():.2f}")
            
            # Begin main processing loop
            self._start_main_loop()
            
            self.state = SystemState.RUNNING
            ultra_logger.info("ULTRA system started successfully")
            
        except Exception as e:
            self.state = SystemState.ERROR
            ultra_logger.error(f"Failed to start ULTRA system: {e}")
            raise
    
    def process_input(self, input_data: Any, 
                     input_type: str = "multimodal",
                     processing_options: Optional[Dict[str, Any]] = None) -> Any:
        """
        Process input through the complete ULTRA pipeline.
        
        Args:
            input_data: Input data to process
            input_type: Type of input ("text", "image", "audio", "multimodal")
            processing_options: Additional processing options
            
        Returns:
            Processed output from the ULTRA system
        """
        if self.state != SystemState.RUNNING:
            raise RuntimeError(f"Cannot process input in state {self.state}")
        
        processing_id = f"proc_{int(time.time() * 1000000)}"
        start_time = time.time()
        
        ultra_logger.debug(f"Processing input {processing_id} of type {input_type}")
        
        try:
            # Stage 1: Input Processing
            if 'input_processing' in self.components:
                processed_input = self.components['input_processing'].process(
                    input_data, input_type, processing_options
                )
            else:
                processed_input = input_data
            
            # Stage 2: Core Neural Processing
            if 'core_neural' in self.components:
                neural_output = self.components['core_neural'].process(processed_input)
            else:
                neural_output = processed_input
            
            # Stage 3: Transformer Processing
            if 'transformer' in self.components:
                transformer_output = self.components['transformer'].process(neural_output)
            else:
                transformer_output = neural_output
            
            # Stage 4: Diffusion-Based Reasoning
            if 'diffusion_reasoning' in self.components:
                reasoning_output = self.components['diffusion_reasoning'].process(transformer_output)
            else:
                reasoning_output = transformer_output
            
            # Stage 5: Meta-Cognitive Processing
            if 'meta_cognitive' in self.components:
                metacognitive_output = self.components['meta_cognitive'].process(reasoning_output)
            else:
                metacognitive_output = reasoning_output
            
            # Stage 6: Consciousness Processing
            if 'consciousness' in self.components:
                consciousness_output = self.components['consciousness'].process(metacognitive_output)
            else:
                consciousness_output = metacognitive_output
            
            # Stage 7: Neuro-Symbolic Integration
            if 'neuro_symbolic' in self.components:
                symbolic_output = self.components['neuro_symbolic'].process(consciousness_output)
            else:
                symbolic_output = consciousness_output
            
            # Stage 8: Output Generation
            if 'output_generation' in self.components:
                final_output = self.components['output_generation'].synthesize(symbolic_output)
            else:
                final_output = symbolic_output
            
            # Update performance metrics
            processing_time = time.time() - start_time
            self.performance_metrics[f'processing_time_{input_type}'] = processing_time
            self.system_monitor.log_metric('processing_time', processing_time)
            
            ultra_logger.debug(f"Input {processing_id} processed successfully in {processing_time:.3f}s")
            
            return final_output
            
        except Exception as e:
            self.error_counts['processing'] += 1
            ultra_logger.error(f"Error processing input {processing_id}: {e}")
            raise
    
    def update_configuration(self, config_updates: Dict[str, Any]) -> None:
        """
        Update system configuration dynamically.
        
        Args:
            config_updates: Dictionary of configuration updates
        """
        ultra_logger.info("Updating system configuration...")
        
        try:
            # Update configuration
            old_config = self.config.copy()
            self.config = config_manager.update_config(config_updates)
            
            # Notify components of configuration changes
            self._propagate_config_updates(old_config, self.config)
            
            ultra_logger.info("Configuration updated successfully")
            
        except Exception as e:
            ultra_logger.error(f"Failed to update configuration: {e}")
            raise
    
    def pause(self) -> None:
        """Pause system processing."""
        if self.state != SystemState.RUNNING:
            raise RuntimeError(f"Cannot pause system in state {self.state}")
        
        self.state = SystemState.PAUSING
        ultra_logger.info("Pausing ULTRA system...")
        
        # Pause all components
        for component_name, component in self.components.items():
            if hasattr(component, 'pause'):
                try:
                    component.pause()
                    ultra_logger.debug(f"Paused {component_name}")
                except Exception as e:
                    ultra_logger.error(f"Failed to pause {component_name}: {e}")
        
        self.state = SystemState.PAUSED
        ultra_logger.info("ULTRA system paused")
    
    def resume(self) -> None:
        """Resume system processing."""
        if self.state != SystemState.PAUSED:
            raise RuntimeError(f"Cannot resume system in state {self.state}")
        
        self.state = SystemState.RESUMING
        ultra_logger.info("Resuming ULTRA system...")
        
        # Resume all components
        for component_name, component in self.components.items():
            if hasattr(component, 'resume'):
                try:
                    component.resume()
                    ultra_logger.debug(f"Resumed {component_name}")
                except Exception as e:
                    ultra_logger.error(f"Failed to resume {component_name}: {e}")
        
        self.state = SystemState.RUNNING
        ultra_logger.info("ULTRA system resumed")
    
    def shutdown(self) -> None:
        """Shutdown the ULTRA system gracefully."""
        if self.shutdown_requested:
            return
        
        self.shutdown_requested = True
        self.state = SystemState.SHUTTING_DOWN
        ultra_logger.info("Shutting down ULTRA system...")
        
        try:
            # Stop main processing loop
            self._stop_main_loop()
            
            # Stop component threads
            self._stop_component_threads()
            
            # Shutdown components in reverse order
            component_shutdown_order = [
                'safety', 'output_generation', 'self_evolution', 'knowledge_management',
                'neuro_symbolic', 'consciousness', 'meta_cognitive', 'diffusion_reasoning',
                'transformer', 'neuromorphic_processing', 'core_neural', 'input_processing'
            ]
            
            for component_name in component_shutdown_order:
                if component_name in self.components:
                    try:
                        component = self.components[component_name]
                        if hasattr(component, 'shutdown'):
                            component.shutdown()
                        
                        # Deallocate resources
                        self.resource_manager.deallocate_resources(component_name)
                        
                        ultra_logger.debug(f"Shutdown {component_name}")
                        
                    except Exception as e:
                        ultra_logger.error(f"Error shutting down {component_name}: {e}")
            
            # Stop core systems
            self.message_bus.stop()
            
            # Final cleanup
            if self.is_distributed:
                self._cleanup_distributed_processing()
            
            self.state = SystemState.SHUTDOWN
            ultra_logger.info("ULTRA system shutdown complete")
            
        except Exception as e:
            ultra_logger.error(f"Error during shutdown: {e}")
            self.state = SystemState.ERROR
    
    def get_system_health(self) -> SystemHealth:
        """Get comprehensive system health information."""
        return self._perform_health_check()
    
    def get_system_metrics(self) -> Dict[str, Any]:
        """Get comprehensive system metrics."""
        return {
            'system_state': self.state.name,
            'uptime': time.time() - self.start_time,
            'component_count': len(self.components),
            'active_components': len([s for s in self.component_status.values() 
                                    if s == ComponentStatus.ONLINE]),
            'failed_components': len([s for s in self.component_status.values() 
                                    if s == ComponentStatus.FAILED]),
            'performance_metrics': self.performance_metrics.copy(),
            'error_counts': dict(self.error_counts),
            'resource_usage': self.resource_manager.get_system_resource_usage(),
            'message_bus_metrics': self.message_bus.get_metrics(),
            'memory_statistics': self.memory_manager.get_memory_statistics()
        }
    
    # Component initialization methods
    def _initialize_input_processing(self) -> Optional[Any]:
        """Initialize the input processing component."""
        if not COMPONENTS_AVAILABLE:
            return self._create_mock_component("input_processing")
        
        try:
            config = getattr(self.config, 'input_processing_config', {})
            component = MultiModalInputProcessor(config)
            
            # Subscribe to relevant messages
            self.message_bus.subscribe('input_processing', component.handle_message)
            
            return component
        except Exception as e:
            ultra_logger.error(f"Failed to initialize input processing: {e}")
            return None
    
    def _initialize_core_neural(self) -> Optional[Any]:
        """Initialize the core neural architecture."""
        if not COMPONENTS_AVAILABLE:
            return self._create_mock_component("core_neural")
        
        try:
            component = CoreNeuralArchitecture(self.config.core_neural_config)
            
            # Subscribe to relevant messages
            self.message_bus.subscribe('core_neural', component.handle_message)
            
            return component
        except Exception as e:
            ultra_logger.error(f"Failed to initialize core neural architecture: {e}")
            return None
    
    def _initialize_transformer(self) -> Optional[Any]:
        """Initialize the hyper-dimensional transformer."""
        if not COMPONENTS_AVAILABLE:
            return self._create_mock_component("transformer")
        
        try:
            component = HyperDimensionalTransformer(self.config.transformer_config)
            
            # Subscribe to relevant messages
            self.message_bus.subscribe('transformer', component.handle_message)
            
            return component
        except Exception as e:
            ultra_logger.error(f"Failed to initialize transformer: {e}")
            return None
    
    def _initialize_diffusion_reasoning(self) -> Optional[Any]:
        """Initialize the diffusion-based reasoning component."""
        if not COMPONENTS_AVAILABLE:
            return self._create_mock_component("diffusion_reasoning")
        
        try:
            component = DiffusionBasedReasoning(self.config.diffusion_config)
            
            # Subscribe to relevant messages
            self.message_bus.subscribe('diffusion_reasoning', component.handle_message)
            
            return component
        except Exception as e:
            ultra_logger.error(f"Failed to initialize diffusion reasoning: {e}")
            return None
    
    def _initialize_meta_cognitive(self) -> Optional[Any]:
        """Initialize the meta-cognitive system."""
        if not COMPONENTS_AVAILABLE:
            return self._create_mock_component("meta_cognitive")
        
        try:
            component = MetaCognitiveSystem(self.config.meta_cognitive_config)
            
            # Subscribe to relevant messages
            self.message_bus.subscribe('meta_cognitive', component.handle_message)
            
            return component
        except Exception as e:
            ultra_logger.error(f"Failed to initialize meta-cognitive system: {e}")
            return None
    
    def _initialize_neuromorphic_processing(self) -> Optional[Any]:
        """Initialize the neuromorphic processing layer."""
        if not COMPONENTS_AVAILABLE:
            return self._create_mock_component("neuromorphic_processing")
        
        try:
            config = getattr(self.config, 'neuromorphic_config', {})
            component = NeuromorphicProcessingLayer(config)
            
            # Subscribe to relevant messages
            self.message_bus.subscribe('neuromorphic_processing', component.handle_message)
            
            return component
        except Exception as e:
            ultra_logger.error(f"Failed to initialize neuromorphic processing: {e}")
            return None
    
    def _initialize_consciousness(self) -> Optional[Any]:
        """Initialize the emergent consciousness lattice."""
        if not COMPONENTS_AVAILABLE:
            return self._create_mock_component("consciousness")
        
        try:
            config = getattr(self.config, 'consciousness_config', {})
            component = EmergentConsciousnessLattice(config)
            
            # Subscribe to relevant messages
            self.message_bus.subscribe('consciousness', component.handle_message)
            
            return component
        except Exception as e:
            ultra_logger.error(f"Failed to initialize consciousness lattice: {e}")
            return None
    
    def _initialize_neuro_symbolic(self) -> Optional[Any]:
        """Initialize the neuro-symbolic integration."""
        if not COMPONENTS_AVAILABLE:
            return self._create_mock_component("neuro_symbolic")
        
        try:
            config = getattr(self.config, 'neuro_symbolic_config', {})
            component = NeuroSymbolicIntegration(config)
            
            # Subscribe to relevant messages
            self.message_bus.subscribe('neuro_symbolic', component.handle_message)
            
            return component
        except Exception as e:
            ultra_logger.error(f"Failed to initialize neuro-symbolic integration: {e}")
            return None
    
    def _initialize_knowledge_management(self) -> Optional[Any]:
        """Initialize the knowledge management system."""
        if not COMPONENTS_AVAILABLE:
            return self._create_mock_component("knowledge_management")
        
        try:
            config = getattr(self.config, 'knowledge_config', {})
            component = KnowledgeManagementSystem(config)
            
            # Subscribe to relevant messages
            self.message_bus.subscribe('knowledge_management', component.handle_message)
            
            return component
        except Exception as e:
            ultra_logger.error(f"Failed to initialize knowledge management: {e}")
            return None
    
    def _initialize_self_evolution(self) -> Optional[Any]:
        """Initialize the self-evolution system."""
        if not COMPONENTS_AVAILABLE:
            return self._create_mock_component("self_evolution")
        
        try:
            config = getattr(self.config, 'self_evolution_config', {})
            component = SelfEvolutionSystem(config)
            
            # Subscribe to relevant messages
            self.message_bus.subscribe('self_evolution', component.handle_message)
            
            return component
        except Exception as e:
            ultra_logger.error(f"Failed to initialize self-evolution: {e}")
            return None
    
    def _initialize_output_generation(self) -> Optional[Any]:
        """Initialize the output generation component."""
        if not COMPONENTS_AVAILABLE:
            return self._create_mock_component("output_generation")
        
        try:
            config = getattr(self.config, 'output_config', {})
            component = MultiModalOutputSynthesis(config)
            
            # Subscribe to relevant messages
            self.message_bus.subscribe('output_generation', component.handle_message)
            
            return component
        except Exception as e:
            ultra_logger.error(f"Failed to initialize output generation: {e}")
            return None
    
    def _initialize_safety(self) -> Optional[Any]:
        """Initialize the safety framework."""
        if not COMPONENTS_AVAILABLE:
            return self._create_mock_component("safety")
        
        try:
            component = SafetyFramework(self.config.security_config)
            
            # Subscribe to relevant messages
            self.message_bus.subscribe('safety', component.handle_message)
            
            return component
        except Exception as e:
            ultra_logger.error(f"Failed to initialize safety framework: {e}")
            return None
    
    def _create_mock_component(self, name: str) -> Any:
        """Create a mock component for testing when real components aren't available."""
        class MockComponent:
            def __init__(self, name):
                self.name = name
                
            def process(self, data):
                return data
                
            def handle_message(self, message):
                pass
                
            def pause(self):
                pass
                
            def resume(self):
                pass
                
            def shutdown(self):
                pass
        
        return MockComponent(name)
    
    def _get_component_priority(self, component_name: str) -> int:
        """Get priority level for a component."""
        priority_map = {
            'core_neural': 1,
            'transformer': 2,
            'diffusion_reasoning': 2,
            'meta_cognitive': 3,
            'consciousness': 3,
            'neuro_symbolic': 4,
            'neuromorphic_processing': 4,
            'input_processing': 5,
            'output_generation': 5,
            'knowledge_management': 6,
            'self_evolution': 7,
            'safety': 1  # High priority for safety
        }
        return priority_map.get(component_name, 5)
    
    def _is_critical_component(self, component_name: str) -> bool:
        """Check if a component is critical for system operation."""
        critical_components = ['core_neural', 'transformer', 'safety']
        return component_name in critical_components
    
    def _setup_component_communication(self) -> None:
        """Set up communication channels between components."""
        ultra_logger.info("Setting up inter-component communication...")
        
        # Define communication pathways
        communication_map = {
            'input_processing': ['core_neural'],
            'core_neural': ['transformer', 'neuromorphic_processing'],
            'transformer': ['diffusion_reasoning'],
            'diffusion_reasoning': ['meta_cognitive'],
            'meta_cognitive': ['consciousness'],
            'consciousness': ['neuro_symbolic'],
            'neuro_symbolic': ['output_generation'],
            'neuromorphic_processing': ['consciousness'],
            'knowledge_management': ['*'],  # Can communicate with all
            'self_evolution': ['*'],        # Can communicate with all
            'safety': ['*']                 # Can communicate with all
        }
        
        # Set up message routing
        for source, destinations in communication_map.items():
            if source in self.components:
                for dest in destinations:
                    if dest == '*':
                        # Subscribe to all messages
                        self.message_bus.subscribe('*', self.components[source].handle_message)
                    elif dest in self.components:
                        # Set up specific communication channel
                        self.message_bus.subscribe(f"{source}_to_{dest}", 
                                                 self.components[dest].handle_message)
    
    def _validate_system_integrity(self) -> None:
        """Validate system integrity after initialization."""
        ultra_logger.info("Validating system integrity...")
        
        # Check critical components
        critical_components = ['core_neural', 'transformer', 'safety']
        for component in critical_components:
            if component not in self.components:
                raise RuntimeError(f"Critical component '{component}' not initialized")
            if self.component_status[component] != ComponentStatus.ONLINE:
                raise RuntimeError(f"Critical component '{component}' not online")
        
        # Check inter-component compatibility
        self._check_component_compatibility()
        
        # Validate resource allocations
        violations = self.resource_manager.check_resource_violations()
        if violations:
            ultra_logger.warning(f"Resource violations detected: {violations}")
        
        ultra_logger.info("System integrity validation complete")
    
    def _check_component_compatibility(self) -> None:
        """Check compatibility between components."""
        # Check dimension compatibility between transformer and diffusion
        if 'transformer' in self.components and 'diffusion_reasoning' in self.components:
            transformer_config = self.config.transformer_config.attention_config
            diffusion_config = self.config.diffusion_config.thought_space_config
            
            if transformer_config.d_model != diffusion_config.thought_dim:
                ultra_logger.warning(
                    f"Dimension mismatch: transformer d_model ({transformer_config.d_model}) "
                    f"!= diffusion thought_dim ({diffusion_config.thought_dim})"
                )
    
    def _initialize_distributed_processing(self) -> None:
        """Initialize distributed processing if enabled."""
        if not self.is_distributed:
            return
        
        try:
            # Initialize distributed backend
            if not dist.is_initialized():
                # Use environment variables for distributed setup
                backend = os.environ.get('ULTRA_DIST_BACKEND', 'nccl')
                init_method = os.environ.get('ULTRA_DIST_INIT_METHOD', 'env://')
                
                dist.init_process_group(backend=backend, init_method=init_method)
                
                self.rank = dist.get_rank()
                self.world_size = dist.get_world_size()
                
                ultra_logger.info(f"Distributed processing initialized: rank {self.rank}, "
                                 f"world size {self.world_size}")
        
        except Exception as e:
            ultra_logger.error(f"Failed to initialize distributed processing: {e}")
            self.is_distributed = False
    
    def _cleanup_distributed_processing(self) -> None:
        """Clean up distributed processing."""
        if self.is_distributed and dist.is_initialized():
            try:
                dist.destroy_process_group()
                ultra_logger.info("Distributed processing cleaned up")
            except Exception as e:
                ultra_logger.error(f"Error cleaning up distributed processing: {e}")
    
    def _start_monitoring(self) -> None:
        """Start system monitoring."""
        def monitoring_loop():
            while not self.shutdown_requested:
                try:
                    # Perform health check
                    health = self._perform_health_check()
                    self.health_history.append(health)
                    
                    # Keep only last 100 health checks
                    if len(self.health_history) > 100:
                        self.health_history.pop(0)
                    
                    # Update resource usage
                    for component_name in self.components:
                        # Mock resource usage - in real implementation, 
                        # components would report their actual usage
                        cpu_usage = np.random.normal(30, 10)  # Mock CPU usage
                        memory_usage = np.random.normal(40, 15)  # Mock memory usage
                        self.resource_manager.update_resource_usage(
                            component_name, cpu_usage, memory_usage
                        )
                    
                    # Check for resource violations
                    violations = self.resource_manager.check_resource_violations()
                    if violations:
                        for violation in violations:
                            ultra_logger.warning(f"Resource violation: {violation}")
                    
                    # Sleep before next check
                    time.sleep(self.config.logging_config.metrics_collection_interval)
                    
                except Exception as e:
                    ultra_logger.error(f"Error in monitoring loop: {e}")
                    time.sleep(5)  # Sleep longer on error
        
        monitoring_thread = threading.Thread(target=monitoring_loop, daemon=True)
        monitoring_thread.start()
        ultra_logger.info("System monitoring started")
    
    def _perform_health_check(self) -> SystemHealth:
        """Perform a comprehensive system health check."""
        current_time = datetime.now(timezone.utc)
        
        # Get system resource usage
        resource_usage = self.resource_manager.get_system_resource_usage()
        
        # Count errors by component
        error_counts = dict(self.error_counts)
        
        # Check for alerts
        alerts = []
        if resource_usage.get('memory_percent', 0) > 90:
            alerts.append({
                'type': 'high_memory_usage',
                'message': f"Memory usage at {resource_usage['memory_percent']:.1f}%",
                'severity': 'warning',
                'timestamp': current_time
            })
        
        if resource_usage.get('cpu_percent', 0) > 80:
            alerts.append({
                'type': 'high_cpu_usage',
                'message': f"CPU usage at {resource_usage['cpu_percent']:.1f}%",
                'severity': 'warning',
                'timestamp': current_time
            })
        
        # Create health object
        health = SystemHealth(
            overall_status=self.state,
            component_statuses=self.component_status.copy(),
            performance_metrics=self.performance_metrics.copy(),
            resource_usage=resource_usage,
            error_counts=error_counts,
            uptime=time.time() - self.start_time,
            last_health_check=current_time,
            alerts=alerts
        )
        
        return health
    
    def _start_component_threads(self) -> None:
        """Start processing threads for components that need them."""
        for component_name, component in self.components.items():
            if hasattr(component, 'run_background_processing'):
                thread = threading.Thread(
                    target=component.run_background_processing,
                    name=f"{component_name}_thread",
                    daemon=True
                )
                thread.start()
                self.component_threads[component_name] = thread
                ultra_logger.debug(f"Started thread for {component_name}")
    
    def _stop_component_threads(self) -> None:
        """Stop all component processing threads."""
        for component_name, thread in self.component_threads.items():
            try:
                if thread.is_alive():
                    # Signal component to stop
                    component = self.components.get(component_name)
                    if hasattr(component, 'stop_background_processing'):
                        component.stop_background_processing()
                    
                    # Wait for thread to finish
                    thread.join(timeout=5.0)
                    if thread.is_alive():
                        ultra_logger.warning(f"Thread {component_name} did not stop gracefully")
                    else:
                        ultra_logger.debug(f"Stopped thread for {component_name}")
                        
            except Exception as e:
                ultra_logger.error(f"Error stopping thread {component_name}: {e}")
    
    def _start_main_loop(self) -> None:
        """Start the main processing loop."""
        self.main_loop_running = True
        
        def main_loop():
            while self.main_loop_running and not self.shutdown_requested:
                try:
                    # Main processing logic would go here
                    # For now, just sleep and monitor
                    time.sleep(0.1)
                    
                except Exception as e:
                    ultra_logger.error(f"Error in main loop: {e}")
                    time.sleep(1)
        
        self.main_loop_thread = threading.Thread(target=main_loop, daemon=True)
        self.main_loop_thread.start()
    
    def _stop_main_loop(self) -> None:
        """Stop the main processing loop."""
        self.main_loop_running = False
        if hasattr(self, 'main_loop_thread') and self.main_loop_thread.is_alive():
            self.main_loop_thread.join(timeout=5.0)
    
    def _propagate_config_updates(self, old_config: ULTRAConfig, new_config: ULTRAConfig) -> None:
        """Propagate configuration updates to all components."""
        for component_name, component in self.components.items():
            if hasattr(component, 'update_configuration'):
                try:
                    # Get component-specific config
                    config_attr = f"{component_name}_config"
                    if hasattr(new_config, config_attr):
                        component_config = getattr(new_config, config_attr)
                        component.update_configuration(component_config)
                        ultra_logger.debug(f"Updated configuration for {component_name}")
                        
                except Exception as e:
                    ultra_logger.error(f"Failed to update config for {component_name}: {e}")
    
    def _register_signal_handlers(self) -> None:
        """Register signal handlers for graceful shutdown."""
        def signal_handler(signum, frame):
            ultra_logger.info(f"Received signal {signum}, initiating shutdown...")
            self.shutdown()
        
        # Register handlers for common shutdown signals
        signal.signal(signal.SIGINT, signal_handler)   # Ctrl+C
        signal.signal(signal.SIGTERM, signal_handler)  # Termination request
        
        # On Unix systems, also handle SIGUSR1 for graceful restart
        if hasattr(signal, 'SIGUSR1'):
            def restart_handler(signum, frame):
                ultra_logger.info("Received restart signal, reloading configuration...")
                try:
                    # Reload configuration from file
                    new_config = config_manager.load_config()
                    self.update_configuration(new_config.to_dict())
                except Exception as e:
                    ultra_logger.error(f"Failed to reload configuration: {e}")
            
            signal.signal(signal.SIGUSR1, restart_handler)

# =============================================================================
# Factory Functions and Utilities
# =============================================================================

def create_ultra_system(config: Optional[ULTRAConfig] = None,
                       config_path: Optional[str] = None,
                       environment: Optional[Environment] = None,
                       template_name: Optional[str] = None) -> ULTRASystem:
    """
    Factory function to create and configure an ULTRA system.
    
    Args:
        config: Pre-configured ULTRAConfig object
        config_path: Path to configuration file
        environment: Environment type
        template_name: Name of configuration template to use
        
    Returns:
        Configured ULTRASystem instance
    """
    if config is None:
        if template_name:
            # Use a configuration template
            if template_name == "research":
                config = templates.research_config()
            elif template_name == "production":
                config = templates.production_config()
            elif template_name == "edge":
                config = templates.edge_device_config()
            elif template_name == "distributed":
                config = templates.distributed_config()
            else:
                raise ValueError(f"Unknown template: {template_name}")
        else:
            # Load from file or create default
            config = config_manager.load_config(config_path, environment)
    
    # Create and return system
    system = ULTRASystem(config)
    ultra_logger.info(f"Created ULTRA system with template: {template_name or 'custom'}")
    
    return system

def initialize_ultra(config: Optional[ULTRAConfig] = None,
                    config_path: Optional[str] = None,
                    environment: Optional[Environment] = None,
                    auto_start: bool = True) -> ULTRASystem:
    """
    Initialize and optionally start an ULTRA system.
    
    Args:
        config: Pre-configured ULTRAConfig object
        config_path: Path to configuration file
        environment: Environment type
        auto_start: Whether to automatically start the system
        
    Returns:
        Initialized (and optionally started) ULTRASystem
    """
    # Create system
    system = create_ultra_system(config, config_path, environment)
    
    # Initialize components
    system.initialize_components()
    
    # Start if requested
    if auto_start:
        system.start()
    
    return system

def get_system_info() -> Dict[str, Any]:
    """
    Get comprehensive information about the ULTRA system and environment.
    
    Returns:
        Dictionary containing system information
    """
    info = {
        'ultra_version': __version__,
        'python_version': sys.version,
        'platform': {
            'system': os.name,
            'platform': sys.platform,
        },
        'hardware': {
            'cpu_count': psutil.cpu_count(),
            'memory_gb': psutil.virtual_memory().total / (1024**3),
        },
        'dependencies': {
            'torch_version': torch.__version__ if torch else None,
            'numpy_version': np.__version__ if np else None,
        },
        'components_available': COMPONENTS_AVAILABLE,
    }
    
    # Add GPU information if available
    try:
        if torch.cuda.is_available():
            info['hardware']['gpu_count'] = torch.cuda.device_count()
            info['hardware']['cuda_version'] = torch.version.cuda
            info['hardware']['gpu_memory_gb'] = sum(
                torch.cuda.get_device_properties(i).total_memory / (1024**3)
                for i in range(torch.cuda.device_count())
            )
    except Exception:
        pass
    
    return info

# =============================================================================
# Configuration and Environment Setup
# =============================================================================

def setup_logging(config: Optional[ULTRAConfig] = None) -> None:
    """Set up logging configuration for the ULTRA system."""
    if config is None:
        config = config_manager.create_default_config()
    
    logging_config = config.logging_config
    
    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, logging_config.log_level))
    
    # Clear existing handlers
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # Configure formatting
    if logging_config.structured_logging:
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
    else:
        formatter = logging.Formatter(
            '%(asctime)s [%(levelname)s] %(name)s: %(message)s'
        )
    
    # Console handler
    if logging_config.log_to_console:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(formatter)
        root_logger.addHandler(console_handler)
    
    # File handler
    if logging_config.log_to_file:
        from logging.handlers import RotatingFileHandler
        
        file_handler = RotatingFileHandler(
            logging_config.log_file_path,
            maxBytes=logging_config.max_log_file_size_mb * 1024 * 1024,
            backupCount=logging_config.max_log_files
        )
        file_handler.setFormatter(formatter)
        root_logger.addHandler(file_handler)
    
    ultra_logger.info("Logging configuration complete")

def validate_environment() -> List[str]:
    """
    Validate the environment for ULTRA system requirements.
    
    Returns:
        List of validation issues (empty if all valid)
    """
    issues = []
    
    # Check Python version
    if sys.version_info < (3, 8):
        issues.append("Python 3.8 or higher required")
    
    # Check required packages
    required_packages = ['torch', 'numpy', 'scipy', 'yaml', 'psutil']
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            issues.append(f"Required package '{package}' not found")
    
    # Check memory requirements
    available_memory_gb = psutil.virtual_memory().available / (1024**3)
    if available_memory_gb < 4.0:
        issues.append(f"Insufficient memory: {available_memory_gb:.1f} GB available, 4+ GB recommended")
    
    # Check disk space
    available_disk_gb = psutil.disk_usage('/').free / (1024**3)
    if available_disk_gb < 10.0:
        issues.append(f"Insufficient disk space: {available_disk_gb:.1f} GB available, 10+ GB recommended")
    
    return issues

# =============================================================================
# Module Exports and Package Information
# =============================================================================

# Core classes
__all__ = [
    # Main system class
    'ULTRASystem',
    
    # Factory functions
    'create_ultra_system',
    'initialize_ultra',
    
    # Utility functions
    'get_system_info',
    'setup_logging',
    'validate_environment',
    
    # System state and health
    'SystemState',
    'ComponentStatus', 
    'SystemHealth',
    
    # Communication
    'MessageType',
    'ComponentMessage',
    'MessageBus',
    
    # Resource management
    'ResourceAllocation',
    'ResourceManager',
    
    # Configuration management (re-exported from utils.config)
    'ULTRAConfig',
    'Environment',
    'OptimizationLevel',
    'HardwareType',
    'ConfigurationManager',
    'ConfigurationTemplates',
    'config_manager',
    'templates',
    
    # Version information
    '__version__',
    '__author__',
    '__license__',
    '__description__',
]

# Initialize logging with default configuration
setup_logging()

# Log system initialization
ultra_logger.info(f"ULTRA system package initialized (version {__version__})")
ultra_logger.info(f"Components available: {COMPONENTS_AVAILABLE}")

# Validate environment and log any issues
env_issues = validate_environment()
if env_issues:
    for issue in env_issues:
        ultra_logger.warning(f"Environment issue: {issue}")
else:
    ultra_logger.info("Environment validation passed")

# Log system information
system_info = get_system_info()
ultra_logger.info(f"System info: {system_info['platform']['system']} with "
                 f"{system_info['hardware']['cpu_count']} CPU cores, "
                 f"{system_info['hardware']['memory_gb']:.1f} GB memory")

# Create global system instance for convenience (optional)
global_system: Optional[ULTRASystem] = None

def get_global_system() -> Optional[ULTRASystem]:
    """Get the global ULTRA system instance."""
    return global_system

def set_global_system(system: ULTRASystem) -> None:
    """Set the global ULTRA system instance."""
    global global_system
    global_system = system
    ultra_logger.info("Global ULTRA system instance set")

# Final initialization message
ultra_logger.info("ULTRA package initialization complete - ready for system creation")
ultra_logger.info("Use ultra.initialize_ultra() to create and start an ULTRA system")