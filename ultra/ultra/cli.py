#!/usr/bin/env python3
"""
ULTRA Command Line Interface
----------------------------

This module provides a comprehensive command-line interface for interacting with the
ULTRA (Ultimate Learning & Thought Reasoning Architecture) system. It allows users
to access ULTRA's capabilities through a terminal, including:

- Processing text and other inputs
- Performing complex reasoning tasks
- Managing system configuration
- Monitoring system status and performance
- Triggering system evolution
- Running interactive sessions
- Batch processing of multiple inputs
- Integration with standard Unix tools via stdin/stdout

The CLI is designed for both interactive use and automation in scripts or
data processing pipelines.
"""

import os
import sys
import time
import json
import yaml
import uuid
import logging
import argparse
import traceback
import threading
import readline
import atexit
import signal
import datetime
import tempfile
import shutil
from pathlib import Path
from typing import Dict, List, Optional, Union, Any, Tuple, Callable
from enum import Enum

# Rich for enhanced terminal output
import rich
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.progress import Progress, TaskID
from rich.prompt import Prompt, Confirm
from rich.syntax import Syntax
from rich.markdown import Markdown
from rich.live import Live
from rich.text import Text
from rich.traceback import install as install_rich_traceback

# Setup enhanced traceback printing
install_rich_traceback()

# Create console for rich output
console = Console()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("ultra.cli")

# Import ULTRA components
try:
    from ultra.ultra import ULTRASystem, get_system
    from ultra.config import ConfigManager, get_config, setup_logging
except ImportError as e:
    console.print(f"[bold red]Error importing ULTRA components: {e}[/bold red]")
    console.print("[yellow]Attempting to add ULTRA package to path...[/yellow]")
    
    # Try to add ULTRA to path
    script_dir = Path(__file__).parent.parent.parent.parent
    if script_dir not in sys.path:
        sys.path.insert(0, str(script_dir))
        try:
            from ultra.ultra import ULTRASystem, get_system
            from ultra.config import ConfigManager, get_config, setup_logging
            console.print("[green]Successfully imported ULTRA components after path adjustment[/green]")
        except ImportError as e:
            console.print(f"[bold red]Failed to import ULTRA components: {e}[/bold red]")
            console.print("[bold yellow]Please make sure ULTRA is correctly installed or the path is set correctly[/bold yellow]")
            sys.exit(1)

# CLI Version (should match ULTRA version)
CLI_VERSION = "1.0.0"
HISTORY_FILE = os.path.expanduser("~/.ultra_history")

# -----------------------------------------------------------------------------
# Helper Classes
# -----------------------------------------------------------------------------

class OutputFormat(str, Enum):
    """Output format options for CLI commands."""
    TEXT = "text"
    JSON = "json"
    YAML = "yaml"
    MARKDOWN = "markdown"
    
class ProcessingMode(str, Enum):
    """Processing modes for ULTRA system."""
    REASONING = "reasoning"
    DIFFUSION = "diffusion"
    TRANSFORMER = "transformer"
    NEURAL = "neural"
    SYMBOLIC = "symbolic"
    HYBRID = "hybrid"
    AUTO = "auto"

class InteractiveSession:
    """
    Manages an interactive session with the ULTRA system.
    
    This class provides a REPL (Read-Eval-Print Loop) interface for
    continuously interacting with the ULTRA system.
    """
    
    def __init__(self, system: ULTRASystem, mode: ProcessingMode = ProcessingMode.AUTO):
        """
        Initialize interactive session.
        
        Args:
            system: ULTRA system instance
            mode: Default processing mode
        """
        self.system = system
        self.mode = mode
        self.history = []
        self.running = False
        self.context = []
        
        # Set up readline history
        if os.path.exists(HISTORY_FILE):
            try:
                readline.read_history_file(HISTORY_FILE)
            except Exception as e:
                logger.warning(f"Failed to read history file: {e}")
        
        readline.set_history_length(1000)
        atexit.register(self._save_history)
    
    def _save_history(self):
        """Save command history to file."""
        try:
            readline.write_history_file(HISTORY_FILE)
        except Exception as e:
            logger.warning(f"Failed to write history file: {e}")
    
    def start(self):
        """Start the interactive session."""
        self.running = True
        
        # Print welcome message
        console.print("\n[bold blue]ULTRA Interactive Session[/bold blue]")
        console.print("[dim]Enter commands or queries. Type 'help' for assistance or 'exit' to quit.[/dim]\n")
        
        # Main REPL loop
        while self.running:
            try:
                # Get input with proper prompt
                prompt_str = f"[{self.mode.value}]> "
                user_input = Prompt.ask(prompt_str)
                
                # Skip empty inputs
                if not user_input.strip():
                    continue
                
                # Process special commands
                if user_input.lower() == "exit" or user_input.lower() == "quit":
                    self.running = False
                    console.print("[yellow]Exiting interactive session...[/yellow]")
                    break
                
                elif user_input.lower() == "help":
                    self._show_help()
                    continue
                
                elif user_input.lower().startswith("mode "):
                    # Change processing mode
                    new_mode = user_input[5:].strip().lower()
                    try:
                        self.mode = ProcessingMode(new_mode)
                        console.print(f"[green]Processing mode changed to: [bold]{self.mode.value}[/bold][/green]")
                    except ValueError:
                        console.print(f"[red]Invalid mode: {new_mode}[/red]")
                        console.print(f"[yellow]Available modes: {', '.join([m.value for m in ProcessingMode])}")
                    continue
                
                elif user_input.lower() == "modes":
                    # Show available modes
                    self._show_modes()
                    continue
                
                elif user_input.lower() == "clear":
                    # Clear context
                    self.context = []
                    console.print("[green]Context cleared[/green]")
                    continue
                
                elif user_input.lower() == "context":
                    # Show current context
                    self._show_context()
                    continue
                
                elif user_input.lower().startswith("save "):
                    # Save context or last response
                    self._handle_save_command(user_input)
                    continue
                
                elif user_input.lower().startswith("load "):
                    # Load file as input
                    filename = user_input[5:].strip()
                    try:
                        with open(filename, 'r') as f:
                            file_content = f.read()
                        console.print(f"[green]Loaded file: [bold]{filename}[/bold][/green]")
                        user_input = file_content
                    except Exception as e:
                        console.print(f"[red]Error loading file: {e}[/red]")
                        continue
                
                # Process input according to mode
                response = self._process_input(user_input)
                
                # Add to context history
                self.context.append({"input": user_input, "response": response})
                
                # Add to readline history
                self.history.append(user_input)
                
            except KeyboardInterrupt:
                console.print("\n[yellow]Operation interrupted[/yellow]")
            except EOFError:
                console.print("\n[yellow]Exiting interactive session...[/yellow]")
                self.running = False
                break
            except Exception as e:
                console.print(f"[bold red]Error: {str(e)}[/bold red]")
                logger.error(f"Error in interactive session: {e}", exc_info=True)
    
    def _process_input(self, user_input: str) -> Any:
        """
        Process user input and return result.
        
        Args:
            user_input: User input string
            
        Returns:
            Processing result
        """
        with Progress(transient=True) as progress:
            task = progress.add_task(f"[cyan]Processing with {self.mode.value} mode...", total=100)
            
            try:
                # Process based on mode
                if self.mode == ProcessingMode.REASONING:
                    # Create parameters with context
                    params = {}
                    if self.context:
                        # Add last few exchanges as context
                        context_window = min(len(self.context), 5)
                        params["context"] = [
                            {
                                "input": ctx["input"],
                                "response": str(ctx["response"])
                            }
                            for ctx in self.context[-context_window:]
                        ]
                    
                    # Process with reasoning mode
                    progress.update(task, advance=30)
                    result = self.system.reason(user_input, **params)
                    
                else:
                    # Process with standard processing
                    progress.update(task, advance=30)
                    result = self.system.process(user_input)
                
                progress.update(task, advance=70)
                
                # Format and display result
                if isinstance(result, str):
                    console.print(Panel(result, title="Response", border_style="green"))
                else:
                    # Try to pretty-print the result
                    try:
                        formatted_result = json.dumps(result, indent=2)
                        console.print(Panel(Syntax(formatted_result, "json", theme="monokai"), 
                                           title="Response (JSON)", border_style="green"))
                    except Exception:
                        # Fallback to simple string representation
                        console.print(Panel(str(result), title="Response", border_style="green"))
                
                return result
                
            except Exception as e:
                logger.error(f"Error processing input: {e}", exc_info=True)
                console.print(f"[bold red]Error: {str(e)}[/bold red]")
                return f"Error: {str(e)}"
    
    def _show_help(self):
        """Show help information."""
        help_text = """
# ULTRA Interactive Session Help

## Basic Commands
- `exit` or `quit`: Exit the interactive session
- `help`: Show this help information
- `clear`: Clear the conversation context
- `context`: Show the current conversation context
- `mode <name>`: Change the processing mode (e.g., `mode reasoning`)
- `modes`: List available processing modes
- `save <filename>`: Save the last response to a file
- `load <filename>`: Load a file as input

## Processing Modes
- `reasoning`: Use meta-cognitive reasoning abilities
- `diffusion`: Use diffusion-based reasoning
- `transformer`: Use transformer-based processing
- `neural`: Use core neural processing
- `symbolic`: Use neuro-symbolic processing
- `hybrid`: Use a combination of approaches
- `auto`: Automatically select the best approach

## Examples
- Process text: Simply type your text and press Enter
- Change mode: `mode reasoning`
- Save output: `save output.txt`
- Load file: `load input.txt`
"""
        console.print(Markdown(help_text))
    
    def _show_modes(self):
        """Show available processing modes."""
        table = Table(title="Available Processing Modes")
        table.add_column("Mode", style="cyan")
        table.add_column("Description", style="green")
        
        mode_descriptions = {
            "reasoning": "Meta-cognitive reasoning capabilities",
            "diffusion": "Diffusion-based reasoning in continuous thought spaces",
            "transformer": "Transformer-based processing for language and sequences",
            "neural": "Core neural architecture processing",
            "symbolic": "Neuro-symbolic integration for logical reasoning",
            "hybrid": "Combination of neural and symbolic approaches",
            "auto": "Automatically select the best approach based on input"
        }
        
        for mode in ProcessingMode:
            table.add_row(mode.value, mode_descriptions.get(mode.value, ""))
        
        console.print(table)
    
    def _show_context(self):
        """Show current conversation context."""
        if not self.context:
            console.print("[yellow]No context available yet[/yellow]")
            return
        
        console.print("[bold blue]Current Context:[/bold blue]")
        for i, ctx in enumerate(self.context):
            console.print(f"[bold cyan]Input {i+1}:[/bold cyan]")
            console.print(f"{ctx['input']}")
            console.print(f"[bold green]Response {i+1}:[/bold green]")
            console.print(f"{str(ctx['response'])}")
            console.print("")
    
    def _handle_save_command(self, command: str):
        """
        Handle save command.
        
        Args:
            command: Save command string (e.g., "save filename.txt")
        """
        parts = command.split(" ", 1)
        if len(parts) < 2 or not parts[1].strip():
            console.print("[red]Error: Missing filename[/red]")
            console.print("[yellow]Usage: save <filename>[/yellow]")
            return
        
        filename = parts[1].strip()
        
        # Check if we have context to save
        if not self.context:
            console.print("[yellow]No context available to save[/yellow]")
            return
        
        try:
            # Get last response
            last_response = self.context[-1]["response"]
            
            # Save to file
            with open(filename, 'w') as f:
                if isinstance(last_response, str):
                    f.write(last_response)
                else:
                    # Try to serialize as JSON
                    try:
                        json.dump(last_response, f, indent=2)
                    except Exception:
                        # Fallback to string representation
                        f.write(str(last_response))
            
            console.print(f"[green]Saved last response to: [bold]{filename}[/bold][/green]")
            
        except Exception as e:
            console.print(f"[red]Error saving file: {e}[/red]")

# -----------------------------------------------------------------------------
# CLI Command Functions
# -----------------------------------------------------------------------------

def setup_parser() -> argparse.ArgumentParser:
    """
    Set up command-line argument parser.
    
    Returns:
        Configured argument parser
    """
    parser = argparse.ArgumentParser(
        description="ULTRA Command Line Interface",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  ultra process "What is the meaning of life?"
  ultra reason --context context.txt "Solve this puzzle"
  ultra interactive --mode reasoning
  ultra config show
  ultra status
  cat input.txt | ultra process --format json > output.json
"""
    )
    
    # Global arguments
    parser.add_argument("--version", action="version", version=f"ULTRA CLI v{CLI_VERSION}")
    parser.add_argument("--verbose", "-v", action="count", default=0, help="Increase verbosity level")
    parser.add_argument("--config", "-c", type=str, help="Path to configuration file")
    parser.add_argument("--quiet", "-q", action="store_true", help="Suppress non-error output")
    
    # Subcommands
    subparsers = parser.add_subparsers(dest="command", help="Command to execute")
    
    # Process command
    process_parser = subparsers.add_parser("process", help="Process input through ULTRA system")
    process_parser.add_argument("input", nargs="?", type=str, help="Input text (reads from stdin if not provided)")
    process_parser.add_argument("--file", "-f", type=str, help="Read input from file")
    process_parser.add_argument("--format", choices=[f.value for f in OutputFormat], 
                              default=OutputFormat.TEXT.value, help="Output format")
    process_parser.add_argument("--mode", choices=[m.value for m in ProcessingMode], 
                              default=ProcessingMode.AUTO.value, help="Processing mode")
    process_parser.add_argument("--output", "-o", type=str, help="Write output to file")
    process_parser.add_argument("--batch", "-b", action="store_true", help="Process each line as separate input")
    process_parser.add_argument("--timeout", "-t", type=int, default=60, help="Timeout in seconds")
    
    # Reason command
    reason_parser = subparsers.add_parser("reason", help="Perform reasoning on input")
    reason_parser.add_argument("query", nargs="?", type=str, help="Query text (reads from stdin if not provided)")
    reason_parser.add_argument("--file", "-f", type=str, help="Read query from file")
    reason_parser.add_argument("--context", type=str, help="Context file for reasoning")
    reason_parser.add_argument("--format", choices=[f.value for f in OutputFormat], 
                             default=OutputFormat.TEXT.value, help="Output format")
    reason_parser.add_argument("--output", "-o", type=str, help="Write output to file")
    reason_parser.add_argument("--max-depth", type=int, help="Maximum reasoning depth")
    reason_parser.add_argument("--timeout", "-t", type=int, default=60, help="Timeout in seconds")
    
    # Interactive command
    interactive_parser = subparsers.add_parser("interactive", help="Start interactive session")
    interactive_parser.add_argument("--mode", choices=[m.value for m in ProcessingMode], 
                                  default=ProcessingMode.AUTO.value, help="Default processing mode")
    
    # Status command
    status_parser = subparsers.add_parser("status", help="Show system status")
    status_parser.add_argument("--format", choices=[f.value for f in OutputFormat], 
                             default=OutputFormat.TEXT.value, help="Output format")
    status_parser.add_argument("--watch", "-w", action="store_true", help="Watch status in real-time")
    status_parser.add_argument("--interval", "-i", type=int, default=2, help="Update interval in seconds for watch mode")
    
    # Config command
    config_parser = subparsers.add_parser("config", help="Manage configuration")
    config_subparsers = config_parser.add_subparsers(dest="subcommand", help="Configuration subcommand")
    
    # Config show
    config_show_parser = config_subparsers.add_parser("show", help="Show current configuration")
    config_show_parser.add_argument("--format", choices=[f.value for f in OutputFormat], 
                                  default=OutputFormat.YAML.value, help="Output format")
    config_show_parser.add_argument("--section", "-s", type=str, help="Show only specific configuration section")
    
    # Config set
    config_set_parser = config_subparsers.add_parser("set", help="Set configuration value")
    config_set_parser.add_argument("path", type=str, help="Configuration path (e.g., 'core_neural.neuron_model')")
    config_set_parser.add_argument("value", type=str, help="Value to set")
    
    # Config reset
    config_reset_parser = config_subparsers.add_parser("reset", help="Reset configuration to defaults")
    config_reset_parser.add_argument("--confirm", action="store_true", help="Skip confirmation prompt")
    
    # Evolve command
    evolve_parser = subparsers.add_parser("evolve", help="Trigger system evolution")
    evolve_parser.add_argument("--components", type=str, nargs="+", help="Target components for evolution")
    evolve_parser.add_argument("--metrics", type=str, help="JSON file with fitness metrics")
    evolve_parser.add_argument("--format", choices=[f.value for f in OutputFormat], 
                             default=OutputFormat.TEXT.value, help="Output format")
    
    # Model command
    model_parser = subparsers.add_parser("model", help="Manage models")
    model_subparsers = model_parser.add_subparsers(dest="subcommand", help="Model subcommand")
    
    # Model list
    model_list_parser = model_subparsers.add_parser("list", help="List available models")
    model_list_parser.add_argument("--format", choices=[f.value for f in OutputFormat], 
                                 default=OutputFormat.TEXT.value, help="Output format")
    
    # Export command (for exporting models or results)
    export_parser = subparsers.add_parser("export", help="Export models or results")
    export_parser.add_argument("type", choices=["model", "results"], help="Type of export")
    export_parser.add_argument("target", type=str, help="Target identifier (model name or results ID)")
    export_parser.add_argument("--format", type=str, default="pytorch", 
                             help="Export format (e.g., pytorch, onnx, json)")
    export_parser.add_argument("--output", "-o", type=str, required=True, help="Output file or directory")
    
    return parser

def read_input(args) -> str:
    """
    Read input from appropriate source based on arguments.
    
    Args:
        args: Command-line arguments
        
    Returns:
        Input string
    """
    # Check for direct input argument
    if hasattr(args, "input") and args.input:
        return args.input
    
    # Check for query argument (reasoning command)
    if hasattr(args, "query") and args.query:
        return args.query
    
    # Check for file input
    if hasattr(args, "file") and args.file:
        try:
            with open(args.file, 'r') as f:
                return f.read()
        except Exception as e:
            console.print(f"[bold red]Error reading file: {e}[/bold red]")
            sys.exit(1)
    
    # Check if stdin has data
    if not sys.stdin.isatty():
        return sys.stdin.read()
    
    # If we get here, there's no input
    console.print("[bold red]Error: No input provided[/bold red]")
    console.print("[yellow]Provide input as an argument, from a file with --file, or via stdin[/yellow]")
    sys.exit(1)

def load_context(context_file: str) -> List[Dict[str, Any]]:
    """
    Load context from a file.
    
    Args:
        context_file: Path to context file
        
    Returns:
        List of context items
    """
    try:
        with open(context_file, 'r') as f:
            content = f.read()
            
            # Try to parse as JSON
            try:
                return json.loads(content)
            except json.JSONDecodeError:
                # If not JSON, treat as text
                return [{"text": content}]
    except Exception as e:
        console.print(f"[bold red]Error loading context: {e}[/bold red]")
        return []

def format_output(result: Any, format_type: str) -> str:
    """
    Format result according to specified format.
    
    Args:
        result: Result to format
        format_type: Output format
        
    Returns:
        Formatted string
    """
    if format_type == OutputFormat.TEXT.value:
        if isinstance(result, str):
            return result
        else:
            return str(result)
    
    elif format_type == OutputFormat.JSON.value:
        try:
            if isinstance(result, str):
                # Try to parse as JSON first in case it's already JSON
                try:
                    parsed = json.loads(result)
                    return json.dumps(parsed, indent=2)
                except json.JSONDecodeError:
                    # Not JSON, so wrap as JSON string
                    return json.dumps(result, indent=2)
            else:
                return json.dumps(result, indent=2)
        except Exception as e:
            logger.error(f"Error formatting as JSON: {e}", exc_info=True)
            return json.dumps({"error": f"Could not format as JSON: {str(e)}", "raw_result": str(result)})
    
    elif format_type == OutputFormat.YAML.value:
        try:
            if isinstance(result, str):
                # Check if already valid YAML
                try:
                    parsed = yaml.safe_load(result)
                    return yaml.dump(parsed, default_flow_style=False)
                except Exception:
                    # Not YAML, so convert to YAML
                    return yaml.dump({"result": result}, default_flow_style=False)
            else:
                return yaml.dump(result, default_flow_style=False)
        except Exception as e:
            logger.error(f"Error formatting as YAML: {e}", exc_info=True)
            return yaml.dump({"error": f"Could not format as YAML: {str(e)}", "raw_result": str(result)})
    
    elif format_type == OutputFormat.MARKDOWN.value:
        if isinstance(result, str):
            return f"```\n{result}\n```"
        elif isinstance(result, dict) or isinstance(result, list):
            # Convert to JSON and format as code block
            try:
                json_str = json.dumps(result, indent=2)
                return f"```json\n{json_str}\n```"
            except Exception:
                return f"```\n{str(result)}\n```"
        else:
            return f"```\n{str(result)}\n```"
    
    # Default fallback
    return str(result)

def write_output(output: str, output_file: Optional[str] = None):
    """
    Write output to appropriate destination.
    
    Args:
        output: Output string
        output_file: Optional output file path
    """
    if output_file:
        try:
            with open(output_file, 'w') as f:
                f.write(output)
            if not args.quiet:
                console.print(f"[green]Output written to: [bold]{output_file}[/bold][/green]")
        except Exception as e:
            console.print(f"[bold red]Error writing to file: {e}[/bold red]")
    else:
        # Print to stdout without rich formatting
        print(output)

def get_status_info(system: ULTRASystem) -> Dict[str, Any]:
    """
    Get comprehensive system status information.
    
    Args:
        system: ULTRA system instance
        
    Returns:
        Status information dictionary
    """
    # Check initialization
    initialized = getattr(system, "initialized", False)
    ready = getattr(system, "ready", False)
    
    # Get subsystem statuses
    subsystems = {}
    if hasattr(system, "subsystems"):
        for name, subsystem in system.subsystems.items():
            if hasattr(subsystem, "is_ready") and callable(subsystem.is_ready):
                subsystems[name] = "ready" if subsystem.is_ready() else "initializing"
            else:
                subsystems[name] = "unknown"
    
    # Get memory usage
    import psutil
    process = psutil.Process(os.getpid())
    memory_info = process.memory_info()
    memory_usage = {
        "rss": memory_info.rss / (1024 * 1024),  # RSS in MB
        "vms": memory_info.vms / (1024 * 1024),  # VMS in MB
        "percent": process.memory_percent()
    }
    
    # Get device info
    device = getattr(system, "device", "unknown")
    
    # Get version
    version = getattr(system, "config", {}).get("version", "1.0.0")
    
    # Calculate uptime
    start_time = getattr(system, "start_time", time.time())
    uptime = time.time() - start_time
    
    # Get additional metrics if available
    metrics = {}
    if hasattr(system, "_collect_metrics") and callable(system._collect_metrics):
        system._collect_metrics()
        metrics = getattr(system, "metrics", {})
    
    # Assemble status info
    status_info = {
        "system": {
            "version": version,
            "cli_version": CLI_VERSION,
            "initialized": initialized,
            "ready": ready,
            "uptime": uptime,
            "device": str(device)
        },
        "subsystems": subsystems,
        "memory": memory_usage,
        "metrics": metrics,
        "timestamp": datetime.datetime.now().isoformat()
    }
    
    return status_info

def display_status(status_info: Dict[str, Any], format_type: str):
    """
    Display system status in appropriate format.
    
    Args:
        status_info: Status information dictionary
        format_type: Output format
    """
    if format_type == OutputFormat.TEXT.value:
        # Display as rich table and panels
        console.print("\n[bold blue]ULTRA System Status[/bold blue]")
        
        # System info panel
        system_info = status_info["system"]
        system_text = Text()
        system_text.append(f"Version: {system_info['version']}\n")
        system_text.append(f"Status: ")
        if system_info["ready"]:
            system_text.append("Ready\n", style="green bold")
        else:
            system_text.append("Initializing\n", style="yellow bold")
        
        system_text.append(f"Device: {system_info['device']}\n")
        system_text.append(f"Uptime: {format_timespan(system_info['uptime'])}\n")
        
        console.print(Panel(system_text, title="System Information", border_style="blue"))
        
        # Subsystems table
        subsystems = status_info["subsystems"]
        if subsystems:
            table = Table(title="Subsystems")
            table.add_column("Subsystem", style="cyan")
            table.add_column("Status", style="green")
            
            for name, status in subsystems.items():
                status_style = "green" if status == "ready" else "yellow"
                table.add_row(name, Text(status, style=status_style))
            
            console.print(table)
        
        # Memory usage
        memory = status_info["memory"]
        memory_text = Text()
        memory_text.append(f"RSS: {memory['rss']:.1f} MB\n")
        memory_text.append(f"VMS: {memory['vms']:.1f} MB\n")
        memory_text.append(f"Memory Usage: {memory['percent']:.1f}%\n")
        
        console.print(Panel(memory_text, title="Memory Usage", border_style="blue"))
        
        # Additional metrics
        metrics = status_info.get("metrics", {})
        if metrics:
            metrics_text = Text()
            for key, value in metrics.items():
                metrics_text.append(f"{key}: {value}\n")
            
            console.print(Panel(metrics_text, title="Metrics", border_style="blue"))
        
        console.print(f"\n[dim]Timestamp: {status_info['timestamp']}[/dim]")
    
    else:
        # For other formats, use standard formatting
        formatted = format_output(status_info, format_type)
        print(formatted)

def format_timespan(seconds: float) -> str:
    """
    Format a timespan in seconds to a human-readable string.
    
    Args:
        seconds: Time in seconds
        
    Returns:
        Formatted time string
    """
    if seconds < 60:
        return f"{seconds:.1f} seconds"
    elif seconds < 3600:
        minutes = seconds / 60
        return f"{minutes:.1f} minutes"
    elif seconds < 86400:
        hours = seconds / 3600
        return f"{hours:.1f} hours"
    else:
        days = seconds / 86400
        return f"{days:.1f} days"

def watch_status(system: ULTRASystem, interval: int, format_type: str):
    """
    Watch system status in real-time.
    
    Args:
        system: ULTRA system instance
        interval: Update interval in seconds
        format_type: Output format
    """
    if format_type != OutputFormat.TEXT.value:
        console.print("[yellow]Warning: Watch mode is designed for text format. Using text format.[/yellow]")
    
    try:
        # Set up signal handler for clean exit
        def signal_handler(sig, frame):
            raise KeyboardInterrupt
        
        signal.signal(signal.SIGINT, signal_handler)
        
        with Live() as live:
            while True:
                # Get status info
                status_info = get_status_info(system)
                
                # Create display content
                content = Panel(
                    get_status_display(status_info),
                    title="ULTRA System Status (Press Ctrl+C to exit)",
                    border_style="blue"
                )
                
                # Update display
                live.update(content)
                
                # Wait for interval
                time.sleep(interval)
    
    except KeyboardInterrupt:
        console.print("\n[yellow]Watch mode terminated.[/yellow]")
    except Exception as e:
        console.print(f"\n[bold red]Error in watch mode: {e}[/bold red]")

def get_status_display(status_info: Dict[str, Any]) -> Text:
    """
    Create rich text display of status information.
    
    Args:
        status_info: Status information dictionary
        
    Returns:
        Rich Text object for display
    """
    text = Text()
    
    # System section
    text.append("System: ", style="bold cyan")
    if status_info["system"]["ready"]:
        text.append("Ready", style="green bold")
    else:
        text.append("Initializing", style="yellow bold")
    text.append(f" - v{status_info['system']['version']}\n")
    
    text.append(f"Device: {status_info['system']['device']}\n")
    text.append(f"Uptime: {format_timespan(status_info['system']['uptime'])}\n\n")
    
    # Subsystems section
    text.append("Subsystems:\n", style="bold cyan")
    for name, status in status_info["subsystems"].items():
        text.append(f"  {name}: ")
        if status == "ready":
            text.append(f"{status}\n", style="green")
        else:
            text.append(f"{status}\n", style="yellow")
    
    text.append("\n")
    
    # Memory section
    memory = status_info["memory"]
    text.append("Memory Usage:\n", style="bold cyan")
    
    # Show memory usage with color based on percentage
    percent = memory["percent"]
    percent_text = f"  {percent:.1f}%"
    if percent < 50:
        text.append(percent_text + "\n", style="green")
    elif percent < 80:
        text.append(percent_text + "\n", style="yellow")
    else:
        text.append(percent_text + "\n", style="red")
    
    text.append(f"  RSS: {memory['rss']:.1f} MB\n")
    text.append(f"  VMS: {memory['vms']:.1f} MB\n\n")
    
    # Additional metrics
    metrics = status_info.get("metrics", {})
    if metrics:
        text.append("Metrics:\n", style="bold cyan")
        for key, value in metrics.items():
            text.append(f"  {key}: {value}\n")
    
    # Timestamp
    text.append(f"\nUpdated: {status_info['timestamp']}", style="dim")
    
    return text

def process_command(args, system: ULTRASystem):
    """
    Handle the 'process' command.
    
    Args:
        args: Command-line arguments
        system: ULTRA system instance
    """
    # Make sure system is initialized
    if not system.initialized:
        with Progress() as progress:
            task = progress.add_task("[cyan]Initializing ULTRA system...", total=100)
            progress.update(task, advance=30)
            system.initialize()
            progress.update(task, completed=100)
    
    # Check for batch mode
    if args.batch:
        # Read all lines
        if args.file:
            try:
                with open(args.file, 'r') as f:
                    lines = f.readlines()
            except Exception as e:
                console.print(f"[bold red]Error reading file: {e}[/bold red]")
                sys.exit(1)
        elif not sys.stdin.isatty():
            lines = sys.stdin.readlines()
        else:
            console.print("[bold red]Error: Batch mode requires input from file or stdin[/bold red]")
            sys.exit(1)
        
        # Process each line
        results = []
        with Progress() as progress:
            task = progress.add_task("[cyan]Processing batch...", total=len(lines))
            
            for i, line in enumerate(lines):
                line = line.strip()
                if not line:
                    continue
                
                progress.update(task, description=f"[cyan]Processing item {i+1}/{len(lines)}...")
                
                try:
                    # Process based on mode
                    mode = ProcessingMode(args.mode)
                    if mode == ProcessingMode.REASONING:
                        result = system.reason(line)
                    else:
                        result = system.process(line)
                    
                    results.append({
                        "input": line,
                        "result": result,
                        "success": True
                    })
                except Exception as e:
                    results.append({
                        "input": line,
                        "error": str(e),
                        "success": False
                    })
                
                progress.update(task, advance=1)
        
        # Output batch results
        output = format_output(results, args.format)
        write_output(output, args.output)
    
    else:
        # Process single input
        input_text = read_input(args)
        
        with Progress() as progress:
            task = progress.add_task(f"[cyan]Processing with {args.mode} mode...", total=100)
            progress.update(task, advance=30)
            
            try:
                # Process based on mode
                mode = ProcessingMode(args.mode)
                if mode == ProcessingMode.REASONING:
                    result = system.reason(input_text)
                else:
                    result = system.process(input_text)
                
                progress.update(task, completed=100)
                
                # Format and output result
                output = format_output(result, args.format)
                write_output(output, args.output)
                
            except Exception as e:
                progress.update(task, description=f"[red]Error: {str(e)}")
                logger.error(f"Error in process command: {e}", exc_info=True)
                
                # Format error as requested format
                error_data = {"error": str(e), "traceback": traceback.format_exc()}
                output = format_output(error_data, args.format)
                
                if args.output:
                    write_output(output, args.output)
                else:
                    console.print(f"[bold red]Error: {str(e)}[/bold red]")
                    sys.exit(1)

def reason_command(args, system: ULTRASystem):
    """
    Handle the 'reason' command.
    
    Args:
        args: Command-line arguments
        system: ULTRA system instance
    """
    # Make sure system is initialized
    if not system.initialized:
        with Progress() as progress:
            task = progress.add_task("[cyan]Initializing ULTRA system...", total=100)
            progress.update(task, advance=30)
            system.initialize()
            progress.update(task, completed=100)
    
    # Read query
    query = read_input(args)
    
    # Load context if provided
    context = None
    if args.context:
        context = load_context(args.context)
    
    # Prepare parameters
    params = {}
    if context:
        params["context"] = context
    if args.max_depth:
        params["max_depth"] = args.max_depth
    
    with Progress() as progress:
        task = progress.add_task("[cyan]Reasoning...", total=100)
        progress.update(task, advance=30)
        
        try:
            # Call reasoning function with timeout
            result = system.reason(query, **params)
            
            progress.update(task, completed=100)
            
            # Format and output result
            output = format_output(result, args.format)
            write_output(output, args.output)
            
        except Exception as e:
            progress.update(task, description=f"[red]Error: {str(e)}")
            logger.error(f"Error in reason command: {e}", exc_info=True)
            
            # Format error as requested format
            error_data = {"error": str(e), "traceback": traceback.format_exc()}
            output = format_output(error_data, args.format)
            
            if args.output:
                write_output(output, args.output)
            else:
                console.print(f"[bold red]Error: {str(e)}[/bold red]")
                sys.exit(1)

def interactive_command(args, system: ULTRASystem):
    """
    Handle the 'interactive' command.
    
    Args:
        args: Command-line arguments
        system: ULTRA system instance
    """
    # Make sure system is initialized
    if not system.initialized:
        with Progress() as progress:
            task = progress.add_task("[cyan]Initializing ULTRA system...", total=100)
            progress.update(task, advance=30)
            system.initialize()
            progress.update(task, completed=100)
    
    # Create interactive session
    session = InteractiveSession(system, ProcessingMode(args.mode))
    
    # Start session
    try:
        session.start()
    except Exception as e:
        logger.error(f"Error in interactive session: {e}", exc_info=True)
        console.print(f"[bold red]Error: {str(e)}[/bold red]")
        sys.exit(1)

def status_command(args, system: ULTRASystem):
    """
    Handle the 'status' command.
    
    Args:
        args: Command-line arguments
        system: ULTRA system instance
    """
    if args.watch:
        # Watch mode
        watch_status(system, args.interval, args.format)
    else:
        # Get status info
        status_info = get_status_info(system)
        
        # Display status
        if args.format == OutputFormat.TEXT.value:
            display_status(status_info, args.format)
        else:
            # Format and output
            output = format_output(status_info, args.format)
            print(output)

def config_command(args, system: ULTRASystem):
    """
    Handle the 'config' command.
    
    Args:
        args: Command-line arguments
        system: ULTRA system instance
    """
    if args.subcommand == "show":
        # Get configuration
        config = get_config()
        
        # Filter section if requested
        if args.section:
            section_path = args.section.split('.')
            current = config if hasattr(config, "__dict__") else config
            
            try:
                for part in section_path:
                    if hasattr(current, part):
                        current = getattr(current, part)
                    elif isinstance(current, dict) and part in current:
                        current = current[part]
                    else:
                        console.print(f"[bold red]Error: Section '{args.section}' not found in configuration[/bold red]")
                        sys.exit(1)
                
                # Convert to dict if it's an object
                if hasattr(current, "__dict__"):
                    config_data = current.__dict__
                else:
                    config_data = current
            except Exception as e:
                console.print(f"[bold red]Error accessing configuration section: {e}[/bold red]")
                sys.exit(1)
        else:
            # Use full config
            if hasattr(config, "__dict__"):
                config_data = config.__dict__
            else:
                config_data = config
        
        # Format and output
        output = format_output(config_data, args.format)
        print(output)
        
    elif args.subcommand == "set":
        # Get config manager
        config_manager = ConfigManager()
        
        # Set value
        try:
            # Parse value
            value = args.value
            
            # Try to interpret as proper type
            try:
                # Try as number
                if value.isdigit():
                    value = int(value)
                elif value.replace('.', '', 1).isdigit():
                    value = float(value)
                # Try as boolean
                elif value.lower() in ('true', 'yes', 'on'):
                    value = True
                elif value.lower() in ('false', 'no', 'off'):
                    value = False
                # Try as null
                elif value.lower() in ('null', 'none'):
                    value = None
                # Try as JSON
                elif value.startswith('{') or value.startswith('['):
                    try:
                        value = json.loads(value)
                    except json.JSONDecodeError:
                        # Keep as string
                        pass
            except Exception:
                # Keep as string if parsing fails
                pass
            
            # Set value
            config_manager.set_runtime_override(args.path, value)
            console.print(f"[green]Configuration updated: {args.path} = {value}[/green]")
        except Exception as e:
            console.print(f"[bold red]Error setting configuration: {e}[/bold red]")
            sys.exit(1)
        
    elif args.subcommand == "reset":
        # Confirm reset
        if not args.confirm:
            confirmed = Confirm.ask("Are you sure you want to reset configuration to defaults?")
            if not confirmed:
                console.print("[yellow]Reset cancelled[/yellow]")
                return
        
        # Get config manager
        config_manager = ConfigManager()
        
        # Reset configuration
        try:
            config_manager.clear_runtime_overrides()
            config_manager.update_config()
            console.print("[green]Configuration reset to defaults[/green]")
        except Exception as e:
            console.print(f"[bold red]Error resetting configuration: {e}[/bold red]")
            sys.exit(1)
    
    else:
        console.print("[bold red]Error: Missing config subcommand[/bold red]")
        console.print("[yellow]Available subcommands: show, set, reset[/yellow]")
        sys.exit(1)

def evolve_command(args, system: ULTRASystem):
    """
    Handle the 'evolve' command.
    
    Args:
        args: Command-line arguments
        system: ULTRA system instance
    """
    # Make sure system is initialized
    if not system.initialized:
        with Progress() as progress:
            task = progress.add_task("[cyan]Initializing ULTRA system...", total=100)
            progress.update(task, advance=30)
            system.initialize()
            progress.update(task, completed=100)
    
    # Load fitness metrics if provided
    fitness_metrics = {}
    if args.metrics:
        try:
            with open(args.metrics, 'r') as f:
                fitness_metrics = json.load(f)
        except Exception as e:
            console.print(f"[bold red]Error loading fitness metrics: {e}[/bold red]")
            sys.exit(1)
    
    # Define fitness function if metrics or components specified
    fitness_function = None
    if fitness_metrics or args.components:
        def custom_fitness(subsystem=None):
            # If subsystem specified and components listed, check if included
            if subsystem and args.components:
                subsystem_name = subsystem.__class__.__name__.lower()
                if not any(comp.lower() in subsystem_name for comp in args.components):
                    return None
            
            # Return metrics if provided
            return fitness_metrics if fitness_metrics else None
        
        fitness_function = custom_fitness
    
    # Trigger evolution
    with Progress() as progress:
        task = progress.add_task("[cyan]Evolving system...", total=100)
        progress.update(task, advance=10)
        
        try:
            # Collect initial metrics
            if hasattr(system, "_collect_metrics") and callable(system._collect_metrics):
                system._collect_metrics()
                initial_metrics = getattr(system, "metrics", {})
            else:
                initial_metrics = {}
            
            progress.update(task, advance=10)
            
            # Trigger evolution
            result = system.evolve(fitness_metric=fitness_function)
            
            progress.update(task, advance=70)
            
            # Collect final metrics
            if hasattr(system, "_collect_metrics") and callable(system._collect_metrics):
                system._collect_metrics()
                final_metrics = getattr(system, "metrics", {})
            else:
                final_metrics = {}
            
            progress.update(task, completed=100)
            
            # Prepare result data
            evolution_result = {
                "success": result,
                "timestamp": datetime.datetime.now().isoformat(),
                "components": args.components or "all",
                "metrics": {
                    "before": initial_metrics,
                    "after": final_metrics
                }
            }
            
            # Format and output result
            if args.format == OutputFormat.TEXT.value:
                if result:
                    console.print("[bold green]Evolution completed successfully[/bold green]")
                else:
                    console.print("[bold yellow]Evolution completed but no changes were made[/bold yellow]")
                
                if initial_metrics or final_metrics:
                    console.print("\n[bold blue]Metrics:[/bold blue]")
                    
                    table = Table(title="Evolution Metrics")
                    table.add_column("Metric", style="cyan")
                    table.add_column("Before", style="yellow")
                    table.add_column("After", style="green")
                    table.add_column("Change", style="blue")
                    
                    # Combine all metrics
                    all_keys = set(initial_metrics.keys()) | set(final_metrics.keys())
                    for key in sorted(all_keys):
                        before = initial_metrics.get(key, "N/A")
                        after = final_metrics.get(key, "N/A")
                        
                        # Calculate change if both are numbers
                        if isinstance(before, (int, float)) and isinstance(after, (int, float)):
                            change = after - before
                            change_str = f"{change:+.6g}"
                            if change > 0:
                                change_style = "green"
                            elif change < 0:
                                change_style = "red"
                            else:
                                change_style = "blue"
                        else:
                            change_str = "N/A"
                            change_style = "blue"
                        
                        table.add_row(
                            key, 
                            str(before), 
                            str(after), 
                            Text(change_str, style=change_style)
                        )
                    
                    console.print(table)
            else:
                # Format as requested format
                output = format_output(evolution_result, args.format)
                print(output)
            
        except Exception as e:
            progress.update(task, description=f"[red]Error: {str(e)}")
            logger.error(f"Error in evolve command: {e}", exc_info=True)
            console.print(f"[bold red]Error: {str(e)}[/bold red]")
            sys.exit(1)

def model_command(args, system: ULTRASystem):
    """
    Handle the 'model' command.
    
    Args:
        args: Command-line arguments
        system: ULTRA system instance
    """
    if args.subcommand == "list":
        # List available models
        models = []
        
        # Check each subsystem for model information
        if hasattr(system, "subsystems"):
            # Check for transformer models
            if "hyper_transformer" in system.subsystems:
                models.append({
                    "id": "ultra-transformer",
                    "name": "ULTRA Hyper-Dimensional Transformer",
                    "type": "transformer",
                    "parameters": "~1B",
                    "subsystem": "hyper_transformer"
                })
            
            # Check for diffusion models
            if "diffusion_reasoning" in system.subsystems:
                models.append({
                    "id": "ultra-diffusion",
                    "name": "ULTRA Diffusion-Based Reasoning Model",
                    "type": "diffusion",
                    "parameters": "~500M",
                    "subsystem": "diffusion_reasoning"
                })
            
            # Check for neuromorphic models
            if "neuromorphic_processing" in system.subsystems:
                models.append({
                    "id": "ultra-neuromorphic",
                    "name": "ULTRA Neuromorphic Processing Model",
                    "type": "spiking-neural-network",
                    "parameters": "~100M",
                    "subsystem": "neuromorphic_processing"
                })
            
            # Check for neuro-symbolic models
            if "neuro_symbolic" in system.subsystems:
                models.append({
                    "id": "ultra-symbolic",
                    "name": "ULTRA Neuro-Symbolic Integration Model",
                    "type": "neuro-symbolic",
                    "parameters": "~50M",
                    "subsystem": "neuro_symbolic"
                })
        
        # Format and output
        if args.format == OutputFormat.TEXT.value:
            if not models:
                console.print("[yellow]No models available[/yellow]")
                return
            
            table = Table(title="Available Models")
            table.add_column("ID", style="cyan")
            table.add_column("Name", style="green")
            table.add_column("Type", style="blue")
            table.add_column("Parameters", style="magenta")
            table.add_column("Subsystem", style="yellow")
            
            for model in models:
                table.add_row(
                    model["id"],
                    model["name"],
                    model["type"],
                    model["parameters"],
                    model["subsystem"]
                )
            
            console.print(table)
        else:
            # Format as requested format
            output = format_output(models, args.format)
            print(output)
    
    else:
        console.print("[bold red]Error: Missing model subcommand[/bold red]")
        console.print("[yellow]Available subcommands: list[/yellow]")
        sys.exit(1)

def export_command(args, system: ULTRASystem):
    """
    Handle the 'export' command.
    
    Args:
        args: Command-line arguments
        system: ULTRA system instance
    """
    # Make sure system is initialized
    if not system.initialized:
        with Progress() as progress:
            task = progress.add_task("[cyan]Initializing ULTRA system...", total=100)
            progress.update(task, advance=30)
            system.initialize()
            progress.update(task, completed=100)
    
    if args.type == "model":
        # Export model
        with Progress() as progress:
            task = progress.add_task(f"[cyan]Exporting model {args.target}...", total=100)
            progress.update(task, advance=10)
            
            try:
                # Check if model exists
                model_found = False
                for name, subsystem in system.subsystems.items():
                    if args.target.lower() in name.lower() or (hasattr(subsystem, "model_id") and args.target == subsystem.model_id):
                        model_found = True
                        break
                
                if not model_found:
                    progress.update(task, description=f"[red]Error: Model {args.target} not found")
                    console.print(f"[bold red]Error: Model '{args.target}' not found[/bold red]")
                    console.print("[yellow]Use 'ultra model list' to see available models[/yellow]")
                    sys.exit(1)
                
                # Export model
                # In a real implementation, this would call appropriate export method
                # For now, just create a dummy file
                progress.update(task, advance=50)
                
                output_path = Path(args.output)
                
                # Create directory if needed
                if not output_path.parent.exists():
                    output_path.parent.mkdir(parents=True)
                
                # Create dummy export file
                with open(output_path, 'w') as f:
                    f.write(f"# ULTRA Model Export\n")
                    f.write(f"# Model: {args.target}\n")
                    f.write(f"# Format: {args.format}\n")
                    f.write(f"# Date: {datetime.datetime.now().isoformat()}\n\n")
                    f.write("This is a placeholder for a real model export.")
                
                progress.update(task, completed=100)
                
                console.print(f"[green]Model exported to: [bold]{args.output}[/bold][/green]")
                
            except Exception as e:
                progress.update(task, description=f"[red]Error: {str(e)}")
                logger.error(f"Error exporting model: {e}", exc_info=True)
                console.print(f"[bold red]Error: {str(e)}[/bold red]")
                sys.exit(1)
    
    elif args.type == "results":
        # Export results
        with Progress() as progress:
            task = progress.add_task(f"[cyan]Exporting results {args.target}...", total=100)
            progress.update(task, advance=10)
            
            try:
                # Create dummy results file
                progress.update(task, advance=50)
                
                output_path = Path(args.output)
                
                # Create directory if needed
                if not output_path.parent.exists():
                    output_path.parent.mkdir(parents=True)
                
                # Create dummy results file
                with open(output_path, 'w') as f:
                    f.write(f"# ULTRA Results Export\n")
                    f.write(f"# ID: {args.target}\n")
                    f.write(f"# Format: {args.format}\n")
                    f.write(f"# Date: {datetime.datetime.now().isoformat()}\n\n")
                    f.write("This is a placeholder for real results export.")
                
                progress.update(task, completed=100)
                
                console.print(f"[green]Results exported to: [bold]{args.output}[/bold][/green]")
                
            except Exception as e:
                progress.update(task, description=f"[red]Error: {str(e)}")
                logger.error(f"Error exporting results: {e}", exc_info=True)
                console.print(f"[bold red]Error: {str(e)}[/bold red]")
                sys.exit(1)
    
    else:
        console.print(f"[bold red]Error: Unsupported export type: {args.type}[/bold red]")
        console.print("[yellow]Supported types: model, results[/yellow]")
        sys.exit(1)

# -----------------------------------------------------------------------------
# Main Function
# -----------------------------------------------------------------------------

def main():
    """Main entry point for the CLI."""
    parser = setup_parser()
    args = parser.parse_args()
    
    # Configure logging based on verbosity
    if args.verbose > 0:
        logger.setLevel(logging.DEBUG)
        if args.verbose > 1:
            logging.getLogger("ultra").setLevel(logging.DEBUG)
    
    # Suppress console output if quiet mode
    if args.quiet:
        console.quiet = True
    
    # Load configuration if specified
    if args.config:
        try:
            config_manager = ConfigManager(args.config)
        except Exception as e:
            console.print(f"[bold red]Error loading configuration: {e}[/bold red]")
            sys.exit(1)
    
    # Get ULTRA system instance
    system = get_system()
    
    # Dispatch command
    try:
        if args.command == "process":
            process_command(args, system)
        elif args.command == "reason":
            reason_command(args, system)
        elif args.command == "interactive":
            interactive_command(args, system)
        elif args.command == "status":
            status_command(args, system)
        elif args.command == "config":
            config_command(args, system)
        elif args.command == "evolve":
            evolve_command(args, system)
        elif args.command == "model":
            model_command(args, system)
        elif args.command == "export":
            export_command(args, system)
        else:
            parser.print_help()
    except KeyboardInterrupt:
        console.print("\n[yellow]Operation interrupted[/yellow]")
        sys.exit(130)
    except Exception as e:
        logger.error(f"Unhandled exception: {e}", exc_info=True)
        console.print(f"[bold red]Error: {str(e)}[/bold red]")
        sys.exit(1)

if __name__ == "__main__":
    main()