#!/usr/bin/env python3
"""
ULTRA: Ultimate Learning & Thought Reasoning Architecture
Computational Reflection Module

This module implements the Computational Reflection component of the Self-Evolution System,
which enables ULTRA to reason about its own computational processes, analyze its code
and architecture, and understand the implications of potential modifications.

Key components:
1. CodeRepresentation - Represents and analyzes system code structure
2. RuntimeAnalysis - Analyzes the runtime behavior of the system
3. PerformanceModel - Models the performance implications of code changes
4. SelfExplanation - Generates explanations of the system's own computational processes
5. ComputationalReflection - Main class that integrates all the above
"""

import os
import sys
import time
import logging
import ast
import inspect
import importlib
import importlib.util
import traceback
import re
import json
import hashlib
import copy
import concurrent.futures
import numpy as np
from typing import Dict, List, Tuple, Any, Callable, Optional, Union, Set
from collections import defaultdict, deque, Counter
from pathlib import Path
from dataclasses import dataclass, field
from enum import Enum, auto
from abc import ABC, abstractmethod

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Add parent directory to sys.path to allow for relative imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Try to import required libraries
try:
    from ultra.utils.config import Configuration
    from ultra.utils.visualization import ArchitectureVisualizer
    from ultra.meta_cognitive.meta_learning import MetaLearningController
except ImportError:
    logger.warning("Some ULTRA components could not be imported. Computational Reflection may have limited functionality.")

# ------------------------------------------------------------------------------
# Code Representation and Analysis
# ------------------------------------------------------------------------------

class CodeEntry:
    """Represents a unit of code (function, class, method, etc.)."""
    
    def __init__(self, name: str, source_code: str, ast_node: ast.AST = None):
        self.name = name
        self.source_code = source_code
        self.ast_node = ast_node
        self.complexity = 0
        self.docstring = ""
        self.line_count = 0
        self.start_line = 0
        self.end_line = 0
        self.calls = []
        self.called_by = []
        self.dependencies = []
        self.test_coverage = 0.0
        self.performance_metrics = {}
        
        # Parse if AST node is provided
        if ast_node:
            self._parse_ast_node()
        
    def _parse_ast_node(self):
        """Parse the AST node to extract code information."""
        if isinstance(self.ast_node, (ast.FunctionDef, ast.AsyncFunctionDef, ast.ClassDef)):
            # Extract docstring
            self.docstring = ast.get_docstring(self.ast_node) or ""
            
            # Get line information
            self.start_line = self.ast_node.lineno
            self.end_line = getattr(self.ast_node, 'end_lineno', 0)
            if self.end_line:
                self.line_count = self.end_line - self.start_line + 1
            
            # Calculate complexity (McCabe Cyclomatic Complexity)
            self.complexity = self._calculate_complexity()
            
            # Extract calls (function calls within this code)
            self.calls = self._extract_calls()
            
            # Extract dependencies
            self.dependencies = self._extract_dependencies()
    
    def _calculate_complexity(self) -> int:
        """Calculate McCabe's Cyclomatic Complexity."""
        if not self.ast_node:
            return 1
            
        complexity = 1  # Start with 1
        
        # Count branches that increase complexity
        for node in ast.walk(self.ast_node):
            if isinstance(node, (ast.If, ast.For, ast.While, ast.IfExp)):
                complexity += 1
            elif isinstance(node, ast.BoolOp) and isinstance(node.op, (ast.And, ast.Or)):
                complexity += len(node.values) - 1
            elif isinstance(node, ast.Try):
                complexity += len(node.handlers)  # Count except blocks
                if node.orelse:  # Count else block
                    complexity += 1
                if node.finalbody:  # Count finally block
                    complexity += 1
        
        return complexity
    
    def _extract_calls(self) -> List[str]:
        """Extract function/method calls from the code."""
        calls = []
        
        if not self.ast_node:
            return calls
            
        for node in ast.walk(self.ast_node):
            if isinstance(node, ast.Call):
                if isinstance(node.func, ast.Name):
                    calls.append(node.func.id)
                elif isinstance(node.func, ast.Attribute):
                    # Handle method calls (obj.method)
                    if isinstance(node.func.value, ast.Name):
                        calls.append(f"{node.func.value.id}.{node.func.attr}")
                    else:
                        # For complex cases, just add the method name
                        calls.append(node.func.attr)
        
        return list(set(calls))  # Remove duplicates
    
    def _extract_dependencies(self) -> List[str]:
        """Extract code dependencies (imports, etc.)."""
        dependencies = []
        
        if not self.ast_node:
            return dependencies
            
        # Extract imports within this code unit (if any)
        for node in ast.walk(self.ast_node):
            if isinstance(node, ast.Import):
                for name in node.names:
                    dependencies.append(name.name)
            elif isinstance(node, ast.ImportFrom):
                if node.module:
                    for name in node.names:
                        dependencies.append(f"{node.module}.{name.name}")
        
        return list(set(dependencies))  # Remove duplicates
    
    def get_summary(self) -> Dict[str, Any]:
        """Get a summary of this code entry."""
        return {
            'name': self.name,
            'line_count': self.line_count,
            'complexity': self.complexity,
            'docstring': self.docstring[:100] + "..." if len(self.docstring) > 100 else self.docstring,
            'calls_count': len(self.calls),
            'called_by_count': len(self.called_by),
            'dependencies_count': len(self.dependencies),
            'test_coverage': self.test_coverage
        }
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert the code entry to a dictionary."""
        return {
            'name': self.name,
            'source_code': self.source_code,
            'complexity': self.complexity,
            'docstring': self.docstring,
            'line_count': self.line_count,
            'start_line': self.start_line,
            'end_line': self.end_line,
            'calls': self.calls,
            'called_by': self.called_by,
            'dependencies': self.dependencies,
            'test_coverage': self.test_coverage,
            'performance_metrics': self.performance_metrics
        }


class MethodEntry(CodeEntry):
    """Represents a method within a class."""
    
    def __init__(self, name: str, source_code: str, ast_node: ast.AST = None, 
                 class_name: str = None, is_static: bool = False, 
                 is_class_method: bool = False, is_property: bool = False):
        super().__init__(name, source_code, ast_node)
        self.class_name = class_name
        self.full_name = f"{class_name}.{name}" if class_name else name
        self.is_static = is_static
        self.is_class_method = is_class_method
        self.is_property = is_property
        self.arguments = []
        self.return_type = None
        
        # Parse arguments and return type
        if ast_node and isinstance(ast_node, (ast.FunctionDef, ast.AsyncFunctionDef)):
            self._parse_method_signature()
    
    def _parse_method_signature(self):
        """Parse method signature to extract arguments and return type."""
        args = self.ast_node.args
        
        # Handle decorators to detect static, class methods, and properties
        for decorator in self.ast_node.decorator_list:
            if isinstance(decorator, ast.Name):
                if decorator.id == 'staticmethod':
                    self.is_static = True
                elif decorator.id == 'classmethod':
                    self.is_class_method = True
                elif decorator.id == 'property':
                    self.is_property = True
        
        # Process arguments
        self.arguments = []
        
        # Handle 'self' or 'cls' for instance and class methods
        if args.args and not self.is_static:
            first_arg = args.args[0]
            is_self = first_arg.arg == 'self' and not self.is_class_method
            is_cls = first_arg.arg == 'cls' and self.is_class_method
            
            # Skip 'self' or 'cls' in the arguments list
            start_idx = 1 if is_self or is_cls else 0
            
            # Process the rest of the arguments
            for i in range(start_idx, len(args.args)):
                arg = args.args[i]
                arg_type = 'unknown'
                
                # Extract type annotation if available
                if arg.annotation:
                    arg_type = self._get_annotation_str(arg.annotation)
                
                self.arguments.append({
                    'name': arg.arg,
                    'type': arg_type,
                    'has_default': i >= len(args.args) - len(args.defaults) if args.defaults else False
                })
        
        # Handle *args
        if args.vararg:
            self.arguments.append({
                'name': f"*{args.vararg.arg}",
                'type': self._get_annotation_str(args.vararg.annotation) if args.vararg.annotation else 'unknown',
                'has_default': False
            })
        
        # Handle **kwargs
        if args.kwarg:
            self.arguments.append({
                'name': f"**{args.kwarg.arg}",
                'type': self._get_annotation_str(args.kwarg.annotation) if args.kwarg.annotation else 'unknown',
                'has_default': False
            })
        
        # Extract return type
        if self.ast_node.returns:
            self.return_type = self._get_annotation_str(self.ast_node.returns)
    
    def _get_annotation_str(self, annotation) -> str:
        """Convert an AST annotation to a string."""
        if annotation is None:
            return 'unknown'
            
        if isinstance(annotation, ast.Name):
            return annotation.id
        elif isinstance(annotation, ast.Attribute):
            if isinstance(annotation.value, ast.Name):
                return f"{annotation.value.id}.{annotation.attr}"
            else:
                return annotation.attr
        elif isinstance(annotation, ast.Subscript):
            if isinstance(annotation.value, ast.Name):
                if annotation.value.id in ('List', 'Dict', 'Set', 'Tuple', 'Optional', 'Union'):
                    # Handle generic types like List[str]
                    if isinstance(annotation.slice, ast.Index):
                        # Python 3.8 and earlier
                        if hasattr(annotation.slice, 'value'):
                            if isinstance(annotation.slice.value, ast.Name):
                                return f"{annotation.value.id}[{annotation.slice.value.id}]"
                            elif isinstance(annotation.slice.value, ast.Tuple):
                                types = []
                                for elt in annotation.slice.value.elts:
                                    if isinstance(elt, ast.Name):
                                        types.append(elt.id)
                                    else:
                                        types.append('unknown')
                                return f"{annotation.value.id}[{', '.join(types)}]"
                    else:
                        # Python 3.9+
                        if isinstance(annotation.slice, ast.Name):
                            return f"{annotation.value.id}[{annotation.slice.id}]"
                        elif isinstance(annotation.slice, ast.Tuple):
                            types = []
                            for elt in annotation.slice.elts:
                                if isinstance(elt, ast.Name):
                                    types.append(elt.id)
                                else:
                                    types.append('unknown')
                            return f"{annotation.value.id}[{', '.join(types)}]"
        
        # Default fallback
        return ast.unparse(annotation) if hasattr(ast, 'unparse') else 'complex_type'
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert the method entry to a dictionary."""
        base_dict = super().to_dict()
        base_dict.update({
            'class_name': self.class_name,
            'full_name': self.full_name,
            'is_static': self.is_static,
            'is_class_method': self.is_class_method,
            'is_property': self.is_property,
            'arguments': self.arguments,
            'return_type': self.return_type
        })
        return base_dict


class ClassEntry(CodeEntry):
    """Represents a class definition."""
    
    def __init__(self, name: str, source_code: str, ast_node: ast.AST = None):
        super().__init__(name, source_code, ast_node)
        self.methods = {}  # name -> MethodEntry
        self.attributes = []
        self.base_classes = []
        self.instance_attributes = []
        self.class_attributes = []
        
        # Parse if AST node is provided
        if ast_node and isinstance(ast_node, ast.ClassDef):
            self._parse_class_definition()
    
    def _parse_class_definition(self):
        """Parse the class definition to extract class information."""
        # Extract base classes
        for base in self.ast_node.bases:
            if isinstance(base, ast.Name):
                self.base_classes.append(base.id)
            elif isinstance(base, ast.Attribute):
                if isinstance(base.value, ast.Name):
                    self.base_classes.append(f"{base.value.id}.{base.attr}")
        
        # Extract methods and attributes
        for item in self.ast_node.body:
            if isinstance(item, (ast.FunctionDef, ast.AsyncFunctionDef)):
                # Extract method
                method_source = ast.get_source_segment(self.source_code, item)
                is_static = any(isinstance(d, ast.Name) and d.id == 'staticmethod' for d in item.decorator_list)
                is_class_method = any(isinstance(d, ast.Name) and d.id == 'classmethod' for d in item.decorator_list)
                is_property = any(isinstance(d, ast.Name) and d.id == 'property' for d in item.decorator_list)
                
                method = MethodEntry(
                    name=item.name,
                    source_code=method_source,
                    ast_node=item,
                    class_name=self.name,
                    is_static=is_static,
                    is_class_method=is_class_method,
                    is_property=is_property
                )
                self.methods[item.name] = method
                
                # Check for instance attribute assignment in __init__
                if item.name == '__init__' and not is_static and not is_class_method:
                    self._extract_init_attributes(item)
            
            elif isinstance(item, ast.Assign):
                # Extract class attribute
                for target in item.targets:
                    if isinstance(target, ast.Name):
                        self.class_attributes.append({
                            'name': target.id,
                            'value': self._extract_value(item.value)
                        })
        
        # Combine attributes
        self.attributes = self.class_attributes + self.instance_attributes
    
    def _extract_init_attributes(self, init_method: ast.FunctionDef):
        """Extract instance attributes from __init__ method."""
        for node in ast.walk(init_method):
            if isinstance(node, ast.Assign):
                for target in node.targets:
                    if isinstance(target, ast.Attribute) and isinstance(target.value, ast.Name) and target.value.id == 'self':
                        self.instance_attributes.append({
                            'name': target.attr,
                            'value': self._extract_value(node.value)
                        })
    
    def _extract_value(self, value_node) -> str:
        """Extract a string representation of a value from an AST node."""
        if isinstance(value_node, ast.Constant):
            return repr(value_node.value)
        elif isinstance(value_node, ast.Name):
            return value_node.id
        elif isinstance(value_node, ast.List):
            return "[...]"
        elif isinstance(value_node, ast.Dict):
            return "{...}"
        elif isinstance(value_node, ast.Call):
            if isinstance(value_node.func, ast.Name):
                return f"{value_node.func.id}(...)"
            elif isinstance(value_node.func, ast.Attribute):
                return f"{value_node.func.attr}(...)"
            return "call(...)"
        else:
            return "complex_value"
    
    def get_method(self, name: str) -> Optional[MethodEntry]:
        """Get a method by name."""
        return self.methods.get(name)
    
    def get_all_methods(self) -> List[MethodEntry]:
        """Get all methods."""
        return list(self.methods.values())
    
    def get_total_complexity(self) -> int:
        """Get the total complexity of the class (sum of all methods)."""
        return sum(method.complexity for method in self.methods.values())
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert the class entry to a dictionary."""
        base_dict = super().to_dict()
        base_dict.update({
            'methods': {name: method.to_dict() for name, method in self.methods.items()},
            'attributes': self.attributes,
            'base_classes': self.base_classes,
            'instance_attributes': self.instance_attributes,
            'class_attributes': self.class_attributes,
            'total_complexity': self.get_total_complexity()
        })
        return base_dict


class FunctionEntry(CodeEntry):
    """Represents a function definition."""
    
    def __init__(self, name: str, source_code: str, ast_node: ast.AST = None):
        super().__init__(name, source_code, ast_node)
        self.arguments = []
        self.return_type = None
        
        # Parse arguments and return type
        if ast_node and isinstance(ast_node, (ast.FunctionDef, ast.AsyncFunctionDef)):
            self._parse_function_signature()
    
    def _parse_function_signature(self):
        """Parse function signature to extract arguments and return type."""
        args = self.ast_node.args
        
        # Process arguments
        self.arguments = []
        
        for i, arg in enumerate(args.args):
            arg_type = 'unknown'
            
            # Extract type annotation if available
            if arg.annotation:
                arg_type = self._get_annotation_str(arg.annotation)
            
            self.arguments.append({
                'name': arg.arg,
                'type': arg_type,
                'has_default': i >= len(args.args) - len(args.defaults) if args.defaults else False
            })
        
        # Handle *args
        if args.vararg:
            self.arguments.append({
                'name': f"*{args.vararg.arg}",
                'type': self._get_annotation_str(args.vararg.annotation) if args.vararg.annotation else 'unknown',
                'has_default': False
            })
        
        # Handle **kwargs
        if args.kwarg:
            self.arguments.append({
                'name': f"**{args.kwarg.arg}",
                'type': self._get_annotation_str(args.kwarg.annotation) if args.kwarg.annotation else 'unknown',
                'has_default': False
            })
        
        # Extract return type
        if self.ast_node.returns:
            self.return_type = self._get_annotation_str(self.ast_node.returns)
    
    def _get_annotation_str(self, annotation) -> str:
        """Convert an AST annotation to a string (same as in MethodEntry)."""
        if annotation is None:
            return 'unknown'
            
        if isinstance(annotation, ast.Name):
            return annotation.id
        elif isinstance(annotation, ast.Attribute):
            if isinstance(annotation.value, ast.Name):
                return f"{annotation.value.id}.{annotation.attr}"
            else:
                return annotation.attr
        elif isinstance(annotation, ast.Subscript):
            if isinstance(annotation.value, ast.Name):
                if annotation.value.id in ('List', 'Dict', 'Set', 'Tuple', 'Optional', 'Union'):
                    # Handle generic types like List[str]
                    if isinstance(annotation.slice, ast.Index):
                        # Python 3.8 and earlier
                        if hasattr(annotation.slice, 'value'):
                            if isinstance(annotation.slice.value, ast.Name):
                                return f"{annotation.value.id}[{annotation.slice.value.id}]"
                            elif isinstance(annotation.slice.value, ast.Tuple):
                                types = []
                                for elt in annotation.slice.value.elts:
                                    if isinstance(elt, ast.Name):
                                        types.append(elt.id)
                                    else:
                                        types.append('unknown')
                                return f"{annotation.value.id}[{', '.join(types)}]"
                    else:
                        # Python 3.9+
                        if isinstance(annotation.slice, ast.Name):
                            return f"{annotation.value.id}[{annotation.slice.id}]"
                        elif isinstance(annotation.slice, ast.Tuple):
                            types = []
                            for elt in annotation.slice.elts:
                                if isinstance(elt, ast.Name):
                                    types.append(elt.id)
                                else:
                                    types.append('unknown')
                            return f"{annotation.value.id}[{', '.join(types)}]"
        
        # Default fallback
        return ast.unparse(annotation) if hasattr(ast, 'unparse') else 'complex_type'
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert the function entry to a dictionary."""
        base_dict = super().to_dict()
        base_dict.update({
            'arguments': self.arguments,
            'return_type': self.return_type
        })
        return base_dict


class ModuleEntry(CodeEntry):
    """Represents a Python module."""
    
    def __init__(self, name: str, source_code: str = None, file_path: str = None):
        self.file_path = file_path
        self.source_code = source_code
        
        # Load source code if not provided but file path is
        if source_code is None and file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    self.source_code = f.read()
            except Exception as e:
                logger.error(f"Error reading file {file_path}: {e}")
                self.source_code = ""
        
        # Parse module
        self.ast_tree = None
        self.functions = {}  # name -> FunctionEntry
        self.classes = {}  # name -> ClassEntry
        self.imports = []
        self.constants = []
        self.docstring = ""
        
        # Initialize with empty AST node (will be populated in parse)
        super().__init__(name, self.source_code or "")
        
        # Parse the module
        self.parse()
    
    def parse(self):
        """Parse the module source code."""
        if not self.source_code:
            logger.warning(f"No source code available for module {self.name}")
            return
            
        try:
            self.ast_tree = ast.parse(self.source_code)
            
            # Extract module docstring
            self.docstring = ast.get_docstring(self.ast_tree) or ""
            
            # Extract imports, functions, classes, and constants
            for node in self.ast_tree.body:
                if isinstance(node, ast.Import):
                    for name in node.names:
                        alias = name.asname or name.name
                        self.imports.append({
                            'module': name.name,
                            'alias': alias,
                            'from_import': False
                        })
                
                elif isinstance(node, ast.ImportFrom):
                    module = node.module or ''
                    for name in node.names:
                        alias = name.asname or name.name
                        self.imports.append({
                            'module': module,
                            'name': name.name,
                            'alias': alias,
                            'from_import': True
                        })
                
                elif isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef)):
                    function_source = ast.get_source_segment(self.source_code, node)
                    function = FunctionEntry(node.name, function_source, node)
                    self.functions[node.name] = function
                
                elif isinstance(node, ast.ClassDef):
                    class_source = ast.get_source_segment(self.source_code, node)
                    class_entry = ClassEntry(node.name, class_source, node)
                    self.classes[node.name] = class_entry
                
                elif isinstance(node, ast.Assign):
                    # Check if this is a constant (all uppercase names are conventionally constants)
                    for target in node.targets:
                        if isinstance(target, ast.Name) and target.id.isupper():
                            value = ast.literal_eval(node.value) if isinstance(node.value, ast.Constant) else None
                            self.constants.append({
                                'name': target.id,
                                'value': value
                            })
            
            # Analyze cross-references (called_by relationships)
            self._analyze_cross_references()
            
        except Exception as e:
            logger.error(f"Error parsing module {self.name}: {e}")
            traceback.print_exc()
    
    def _analyze_cross_references(self):
        """Analyze cross-references between functions and methods."""
        # Collect all callables (functions and methods)
        all_callables = {}
        
        # Add functions
        for name, func in self.functions.items():
            all_callables[name] = func
        
        # Add methods with their full names (class.method)
        for class_name, class_entry in self.classes.items():
            for method_name, method in class_entry.methods.items():
                full_name = f"{class_name}.{method_name}"
                all_callables[full_name] = method
        
        # Process calls to establish called_by relationships
        for callable_name, callable_entry in all_callables.items():
            for call in callable_entry.calls:
                # Check if the call references another callable in this module
                if call in all_callables:
                    target = all_callables[call]
                    if callable_name not in target.called_by:
                        target.called_by.append(callable_name)
    
    def get_function(self, name: str) -> Optional[FunctionEntry]:
        """Get a function by name."""
        return self.functions.get(name)
    
    def get_class(self, name: str) -> Optional[ClassEntry]:
        """Get a class by name."""
        return self.classes.get(name)
    
    def get_method(self, class_name: str, method_name: str) -> Optional[MethodEntry]:
        """Get a method by class and method name."""
        class_entry = self.get_class(class_name)
        if class_entry:
            return class_entry.get_method(method_name)
        return None
    
    def get_summary(self) -> Dict[str, Any]:
        """Get a summary of this module."""
        total_complexity = (
            sum(func.complexity for func in self.functions.values()) +
            sum(cls.get_total_complexity() for cls in self.classes.values())
        )
        
        return {
            'name': self.name,
            'file_path': self.file_path,
            'functions_count': len(self.functions),
            'classes_count': len(self.classes),
            'imports_count': len(self.imports),
            'constants_count': len(self.constants),
            'total_complexity': total_complexity,
            'docstring': self.docstring[:100] + "..." if len(self.docstring) > 100 else self.docstring
        }
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert the module entry to a dictionary."""
        return {
            'name': self.name,
            'file_path': self.file_path,
            'docstring': self.docstring,
            'imports': self.imports,
            'constants': self.constants,
            'functions': {name: func.to_dict() for name, func in self.functions.items()},
            'classes': {name: cls.to_dict() for name, cls in self.classes.items()}
        }


class CodeRepository:
    """Manages a collection of code modules and their relationships."""
    
    def __init__(self):
        self.modules = {}  # name -> ModuleEntry
        self.call_graph = {}  # function/method full name -> list of called functions/methods
        self.dependency_graph = {}  # module name -> list of imported modules
    
    def add_module(self, name: str, source_code: str = None, file_path: str = None) -> ModuleEntry:
        """Add a module to the repository."""
        module = ModuleEntry(name, source_code, file_path)
        self.modules[name] = module
        self._update_graphs(module)
        return module
    
    def load_module_from_path(self, file_path: str) -> Optional[ModuleEntry]:
        """Load a module from a file path."""
        if not os.path.exists(file_path):
            logger.error(f"File not found: {file_path}")
            return None
            
        module_name = os.path.basename(file_path)
        if module_name.endswith('.py'):
            module_name = module_name[:-3]
            
        return self.add_module(module_name, file_path=file_path)
    
    def load_package(self, package_path: str):
        """Load all Python modules in a package."""
        if not os.path.isdir(package_path):
            logger.error(f"Directory not found: {package_path}")
            return
            
        for root, _, files in os.walk(package_path):
            for file in files:
                if file.endswith('.py'):
                    file_path = os.path.join(root, file)
                    self.load_module_from_path(file_path)
    
    def get_module(self, name: str) -> Optional[ModuleEntry]:
        """Get a module by name."""
        return self.modules.get(name)
    
    def get_function(self, module_name: str, function_name: str) -> Optional[FunctionEntry]:
        """Get a function by module and function name."""
        module = self.get_module(module_name)
        if module:
            return module.get_function(function_name)
        return None
    
    def get_class(self, module_name: str, class_name: str) -> Optional[ClassEntry]:
        """Get a class by module and class name."""
        module = self.get_module(module_name)
        if module:
            return module.get_class(class_name)
        return None
    
    def get_method(self, module_name: str, class_name: str, method_name: str) -> Optional[MethodEntry]:
        """Get a method by module, class, and method name."""
        module = self.get_module(module_name)
        if module:
            return module.get_method(class_name, method_name)
        return None
    
    def _update_graphs(self, module: ModuleEntry):
        """Update call and dependency graphs with information from a new module."""
        module_name = module.name
        
        # Update dependency graph
        dependencies = []
        for imp in module.imports:
            if imp.get('from_import'):
                dependencies.append(imp['module'])
            else:
                dependencies.append(imp['module'])
        self.dependency_graph[module_name] = list(set(dependencies))
        
        # Update call graph
        for func_name, func in module.functions.items():
            full_func_name = f"{module_name}.{func_name}"
            self.call_graph[full_func_name] = func.calls
        
        for class_name, class_entry in module.classes.items():
            for method_name, method in class_entry.methods.items():
                full_method_name = f"{module_name}.{class_name}.{method_name}"
                self.call_graph[full_method_name] = method.calls
    
    def find_callers(self, function_fullname: str) -> List[str]:
        """Find all functions/methods that call the given function."""
        callers = []
        for caller, callees in self.call_graph.items():
            if function_fullname in callees:
                callers.append(caller)
        return callers
    
    def find_callees(self, function_fullname: str) -> List[str]:
        """Find all functions/methods called by the given function."""
        return self.call_graph.get(function_fullname, [])
    
    def find_module_dependencies(self, module_name: str) -> List[str]:
        """Find all modules that the given module depends on."""
        return self.dependency_graph.get(module_name, [])
    
    def find_dependent_modules(self, module_name: str) -> List[str]:
        """Find all modules that depend on the given module."""
        dependents = []
        for mod, deps in self.dependency_graph.items():
            if module_name in deps:
                dependents.append(mod)
        return dependents
    
    def get_summary(self) -> Dict[str, Any]:
        """Get a summary of the code repository."""
        module_count = len(self.modules)
        function_count = sum(len(mod.functions) for mod in self.modules.values())
        class_count = sum(len(mod.classes) for mod in self.modules.values())
        method_count = sum(
            sum(len(cls.methods) for cls in mod.classes.values())
            for mod in self.modules.values()
        )
        total_complexity = sum(
            sum(func.complexity for func in mod.functions.values()) +
            sum(cls.get_total_complexity() for cls in mod.classes.values())
            for mod in self.modules.values()
        )
        
        return {
            'module_count': module_count,
            'function_count': function_count,
            'class_count': class_count,
            'method_count': method_count,
            'total_complexity': total_complexity,
            'call_graph_size': len(self.call_graph),
            'dependency_graph_size': len(self.dependency_graph)
        }


class CodeRepresentation:
    """Represents and analyzes system code for computational reflection."""
    
    def __init__(self, module_name: str = None, code_paths: List[str] = None):
        self.repository = CodeRepository()
        self.source_code = ""
        self.ast = None
        self.functions = {}
        self.classes = {}
        self.dependencies = []
        
        # Initialize with module name if provided
        if module_name:
            self._load_module(module_name)
            
        # Load additional code paths if provided
        if code_paths:
            for path in code_paths:
                if os.path.isdir(path):
                    self.repository.load_package(path)
                elif os.path.isfile(path) and path.endswith('.py'):
                    self.repository.load_module_from_path(path)
    
    def _load_module(self, module_name: str):
        """Load a module by name."""
        try:
            # Try to import the module
            module = importlib.import_module(module_name)
            
            # Get module file path
            if hasattr(module, '__file__'):
                file_path = module.__file__
                
                # Load the module into the repository
                mod_entry = self.repository.load_module_from_path(file_path)
                
                # Set fields from the loaded module
                if mod_entry:
                    self.source_code = mod_entry.source_code
                    self.ast = mod_entry.ast_tree
                    self.functions = mod_entry.functions
                    self.classes = mod_entry.classes
                    self.dependencies = mod_entry.imports
            else:
                logger.warning(f"Module {module_name} doesn't have a file location")
                
        except ImportError as e:
            logger.error(f"Could not import module {module_name}: {e}")
            
        except Exception as e:
            logger.error(f"Error loading module {module_name}: {e}")
            traceback.print_exc()
    
    def add_module(self, module_name: str) -> Optional[ModuleEntry]:
        """Add a module to the code representation."""
        try:
            # Try to import the module
            module = importlib.import_module(module_name)
            
            # Get module file path
            if hasattr(module, '__file__'):
                file_path = module.__file__
                
                # Load the module into the repository
                return self.repository.load_module_from_path(file_path)
            else:
                logger.warning(f"Module {module_name} doesn't have a file location")
                return None
                
        except ImportError as e:
            logger.error(f"Could not import module {module_name}: {e}")
            return None
            
        except Exception as e:
            logger.error(f"Error adding module {module_name}: {e}")
            traceback.print_exc()
            return None
    
    def add_file(self, file_path: str) -> Optional[ModuleEntry]:
        """Add a Python file to the code representation."""
        if not os.path.exists(file_path) or not file_path.endswith('.py'):
            logger.error(f"Invalid Python file: {file_path}")
            return None
            
        return self.repository.load_module_from_path(file_path)
    
    def add_directory(self, directory_path: str) -> int:
        """Add all Python files in a directory to the code representation."""
        if not os.path.isdir(directory_path):
            logger.error(f"Directory not found: {directory_path}")
            return 0
            
        # Store initial module count
        initial_count = len(self.repository.modules)
        
        # Load the package
        self.repository.load_package(directory_path)
        
        # Return number of modules added
        return len(self.repository.modules) - initial_count
    
    def get_function_info(self, function_name: str, module_name: str = None) -> Optional[Dict[str, Any]]:
        """Get information about a specific function."""
        # If module name is provided, look only in that module
        if module_name:
            func = self.repository.get_function(module_name, function_name)
            if func:
                return func.to_dict()
            
            # Check if it's a method
            if '.' in function_name:
                class_name, method_name = function_name.split('.', 1)
                method = self.repository.get_method(module_name, class_name, method_name)
                if method:
                    return method.to_dict()
            
            return None
            
        # If no module name, search in all modules
        for module_name, module in self.repository.modules.items():
            func = module.get_function(function_name)
            if func:
                return func.to_dict()
            
            # Check if it's a method
            if '.' in function_name:
                class_name, method_name = function_name.split('.', 1)
                method = module.get_method(class_name, method_name)
                if method:
                    return method.to_dict()
        
        return None
    
    def get_class_info(self, class_name: str, module_name: str = None) -> Optional[Dict[str, Any]]:
        """Get information about a specific class."""
        # If module name is provided, look only in that module
        if module_name:
            cls = self.repository.get_class(module_name, class_name)
            if cls:
                return cls.to_dict()
            return None
            
        # If no module name, search in all modules
        for module_name, module in self.repository.modules.items():
            cls = module.get_class(class_name)
            if cls:
                return cls.to_dict()
        
        return None
    
    def get_module_summary(self, module_name: str = None) -> Dict[str, Any]:
        """Get a summary of the module."""
        if module_name:
            module = self.repository.get_module(module_name)
            if module:
                return module.get_summary()
            return {}
            
        # If no module name provided, return repository summary
        return self.repository.get_summary()
    
    def generate_dependency_graph(self, module_name: str = None) -> Dict[str, List[str]]:
        """Generate a dependency graph for the module or the entire repository."""
        if module_name:
            # Return dependencies for a specific module
            deps = self.repository.find_module_dependencies(module_name)
            return {module_name: deps}
            
        # Return the full dependency graph
        return self.repository.dependency_graph.copy()
    
    def generate_call_graph(self, function_name: str = None, module_name: str = None) -> Dict[str, List[str]]:
        """Generate a call graph for a function/method or the entire repository."""
        if function_name:
            # Return call graph for a specific function
            if module_name:
                full_name = f"{module_name}.{function_name}"
            else:
                full_name = function_name
                
            callees = self.repository.find_callees(full_name)
            callers = self.repository.find_callers(full_name)
            
            return {
                'function': full_name,
                'calls': callees,
                'called_by': callers
            }
            
        # Return the full call graph
        return self.repository.call_graph.copy()
    
    def find_complex_functions(self, complexity_threshold: int = 10) -> List[Dict[str, Any]]:
        """Find functions with complexity above the given threshold."""
        complex_functions = []
        
        for module_name, module in self.repository.modules.items():
            # Check standalone functions
            for func_name, func in module.functions.items():
                if func.complexity > complexity_threshold:
                    complex_functions.append({
                        'module': module_name,
                        'name': func_name,
                        'complexity': func.complexity,
                        'type': 'function'
                    })
            
            # Check methods
            for class_name, cls in module.classes.items():
                for method_name, method in cls.methods.items():
                    if method.complexity > complexity_threshold:
                        complex_functions.append({
                            'module': module_name,
                            'class': class_name,
                            'name': method_name,
                            'complexity': method.complexity,
                            'type': 'method'
                        })
        
        # Sort by complexity (highest first)
        complex_functions.sort(key=lambda x: x['complexity'], reverse=True)
        
        return complex_functions
    
    def find_similar_functions(self, function_name: str, module_name: str = None) -> List[Dict[str, Any]]:
        """Find functions similar to the given function based on call patterns and complexity."""
        target_func_info = self.get_function_info(function_name, module_name)
        if not target_func_info:
            return []
            
        target_calls = set(target_func_info.get('calls', []))
        target_complexity = target_func_info.get('complexity', 0)
        
        similar_functions = []
        
        for module_name, module in self.repository.modules.items():
            # Check standalone functions
            for func_name, func in module.functions.items():
                full_name = f"{module_name}.{func_name}"
                
                # Skip the target function itself
                if target_func_info.get('name') == func_name:
                    continue
                    
                func_calls = set(func.calls)
                
                # Calculate similarity scores
                call_similarity = len(target_calls.intersection(func_calls)) / max(1, len(target_calls.union(func_calls)))
                complexity_similarity = 1.0 - min(1.0, abs(target_complexity - func.complexity) / max(1, target_complexity))
                
                # Combined similarity score
                similarity = (0.6 * call_similarity + 0.4 * complexity_similarity)
                
                if similarity > 0.3:  # Threshold for similarity
                    similar_functions.append({
                        'module': module_name,
                        'name': func_name,
                        'similarity': similarity,
                        'call_similarity': call_similarity,
                        'complexity_similarity': complexity_similarity,
                        'type': 'function'
                    })
            
            # Check methods
            for class_name, cls in module.classes.items():
                for method_name, method in cls.methods.items():
                    full_name = f"{module_name}.{class_name}.{method_name}"
                    
                    # Skip the target method itself
                    if target_func_info.get('full_name') == full_name:
                        continue
                        
                    method_calls = set(method.calls)
                    
                    # Calculate similarity scores
                    call_similarity = len(target_calls.intersection(method_calls)) / max(1, len(target_calls.union(method_calls)))
                    complexity_similarity = 1.0 - min(1.0, abs(target_complexity - method.complexity) / max(1, target_complexity))
                    
                    # Combined similarity score
                    similarity = (0.6 * call_similarity + 0.4 * complexity_similarity)
                    
                    if similarity > 0.3:  # Threshold for similarity
                        similar_functions.append({
                            'module': module_name,
                            'class': class_name,
                            'name': method_name,
                            'similarity': similarity,
                            'call_similarity': call_similarity,
                            'complexity_similarity': complexity_similarity,
                            'type': 'method'
                        })
        
        # Sort by similarity (highest first)
        similar_functions.sort(key=lambda x: x['similarity'], reverse=True)
        
        return similar_functions


# ------------------------------------------------------------------------------
# Runtime Analysis
# ------------------------------------------------------------------------------

class ExecutionContext:
    """Context information for a function execution."""
    
    def __init__(self, function_name: str, args: Dict[str, Any], kwargs: Dict[str, Any]):
        self.function_name = function_name
        self.args = args
        self.kwargs = kwargs
        self.start_time = time.time()
        self.end_time = None
        self.duration = None
        self.outcome = None  # 'success', 'error', etc.
        self.result = None
        self.error = None
        self.memory_start = self._get_current_memory()
        self.memory_end = None
        self.memory_delta = None
        self.children = []  # Child execution contexts
        self.parent = None  # Parent execution context
    
    def complete(self, result=None, error=None):
        """Complete this execution context with results."""
        self.end_time = time.time()
        self.duration = self.end_time - self.start_time
        self.memory_end = self._get_current_memory()
        self.memory_delta = self.memory_end - self.memory_start
        
        if error:
            self.outcome = 'error'
            self.error = error
        else:
            self.outcome = 'success'
            self.result = result
    
    def add_child(self, child_context: 'ExecutionContext'):
        """Add a child execution context."""
        self.children.append(child_context)
        child_context.parent = self
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            'function_name': self.function_name,
            'args': self._safe_repr(self.args),
            'kwargs': self._safe_repr(self.kwargs),
            'start_time': self.start_time,
            'end_time': self.end_time,
            'duration': self.duration,
            'outcome': self.outcome,
            'result': self._safe_repr(self.result),
            'error': str(self.error) if self.error else None,
            'memory_start': self.memory_start,
            'memory_end': self.memory_end,
            'memory_delta': self.memory_delta,
            'children': [child.to_dict() for child in self.children]
        }
    
    def _get_current_memory(self) -> int:
        """Get current memory usage in bytes."""
        try:
            import psutil
            process = psutil.Process()
            return process.memory_info().rss
        except (ImportError, Exception):
            return 0
    
    def _safe_repr(self, obj) -> Any:
        """Safely convert an object to a representation."""
        try:
            # Handle common built-in types
            if obj is None or isinstance(obj, (bool, int, float, str)):
                return obj
            elif isinstance(obj, (list, tuple)):
                return [self._safe_repr(item) for item in obj[:100]]  # Limit to first 100 items
            elif isinstance(obj, dict):
                return {str(k): self._safe_repr(v) for k, v in list(obj.items())[:100]}  # Limit to first 100 items
            elif isinstance(obj, set):
                return {self._safe_repr(item) for item in list(obj)[:100]}  # Limit to first 100 items
            else:
                # For other types, just return the type name and a simple representation
                return f"{type(obj).__name__}: {str(obj)[:100]}..."
        except Exception:
            return f"<unprintable {type(obj).__name__} object>"


class ExecutionTraceManager:
    """Manages execution traces for runtime analysis."""
    
    def __init__(self, max_trace_depth: int = 10, max_traces: int = 1000):
        self.max_trace_depth = max_trace_depth
        self.max_traces = max_traces
        self.traces = []  # list of root execution contexts
        self.current_trace_stack = []  # stack of current execution contexts
        self.function_stats = defaultdict(lambda: {
            'count': 0,
            'total_duration': 0,
            'min_duration': float('inf'),
            'max_duration': 0,
            'avg_duration': 0,
            'error_count': 0,
            'total_memory_delta': 0,
            'min_memory_delta': float('inf'),
            'max_memory_delta': 0,
            'avg_memory_delta': 0
        })
    
    def enter_function(self, function_name: str, args: Dict[str, Any], kwargs: Dict[str, Any]) -> ExecutionContext:
        """Enter a function execution, creating a new context."""
        context = ExecutionContext(function_name, args, kwargs)
        
        if self.current_trace_stack:
            # Add as child to current context
            parent = self.current_trace_stack[-1]
            parent.add_child(context)
        else:
            # This is a root context
            self.traces.append(context)
            
            # Limit total traces
            if len(self.traces) > self.max_traces:
                self.traces.pop(0)  # Remove oldest trace
        
        # Add to current stack if not exceeding max depth
        if len(self.current_trace_stack) < self.max_trace_depth:
            self.current_trace_stack.append(context)
        
        return context
    
    def exit_function(self, context: ExecutionContext, result=None, error=None):
        """Exit a function execution, updating the context."""
        context.complete(result, error)
        
        # Update function statistics
        stats = self.function_stats[context.function_name]
        stats['count'] += 1
        stats['total_duration'] += context.duration
        stats['min_duration'] = min(stats['min_duration'], context.duration)
        stats['max_duration'] = max(stats['max_duration'], context.duration)
        stats['avg_duration'] = stats['total_duration'] / stats['count']
        
        if context.memory_delta is not None:
            stats['total_memory_delta'] += context.memory_delta
            stats['min_memory_delta'] = min(stats['min_memory_delta'], context.memory_delta)
            stats['max_memory_delta'] = max(stats['max_memory_delta'], context.memory_delta)
            stats['avg_memory_delta'] = stats['total_memory_delta'] / stats['count']
        
        if error:
            stats['error_count'] += 1
        
        # Remove from current stack
        if self.current_trace_stack and self.current_trace_stack[-1] == context:
            self.current_trace_stack.pop()
    
    def get_current_trace_root(self) -> Optional[ExecutionContext]:
        """Get the root context of the current trace."""
        if not self.current_trace_stack:
            return None
            
        # Find the root by walking up the parent chain
        current = self.current_trace_stack[0]
        while current.parent:
            current = current.parent
            
        return current
    
    def get_trace(self, index: int) -> Optional[ExecutionContext]:
        """Get a trace by index."""
        if 0 <= index < len(self.traces):
            return self.traces[index]
        return None
    
    def get_traces_for_function(self, function_name: str) -> List[ExecutionContext]:
        """Get all traces for a specific function."""
        function_traces = []
        
        # Helper function to search recursively
        def search_traces(context):
            if context.function_name == function_name:
                function_traces.append(context)
            for child in context.children:
                search_traces(child)
        
        # Search in all traces
        for trace in self.traces:
            search_traces(trace)
            
        return function_traces
    
    def get_function_stats(self, function_name: str = None) -> Dict[str, Any]:
        """Get statistics for a function or all functions."""
        if function_name:
            return dict(self.function_stats.get(function_name, {}))
        
        return {name: dict(stats) for name, stats in self.function_stats.items()}
    
    def clear_traces(self):
        """Clear all traces."""
        self.traces = []
        self.current_trace_stack = []
    
    def clear_stats(self):
        """Clear all function statistics."""
        self.function_stats.clear()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            'traces': [trace.to_dict() for trace in self.traces],
            'function_stats': {name: dict(stats) for name, stats in self.function_stats.items()}
        }


class PerformanceRecord:
    """Record of performance metrics for a function."""
    
    def __init__(self, function_name: str):
        self.function_name = function_name
        self.executions = []  # list of (timestamp, duration, memory_delta, args_summary)
        self.error_executions = []  # list of (timestamp, error_message, args_summary)
        self.arg_influence = {}  # arg_name -> list of (arg_value, duration)
    
    def add_execution(self, timestamp: float, duration: float, memory_delta: int, 
                     args: Dict[str, Any], error: Optional[str] = None):
        """Add an execution record."""
        args_summary = self._summarize_args(args)
        
        if error:
            self.error_executions.append((timestamp, error, args_summary))
        else:
            self.executions.append((timestamp, duration, memory_delta, args_summary))
            
            # Update arg influence data
            for arg_name, arg_value in args.items():
                if arg_name not in self.arg_influence:
                    self.arg_influence[arg_name] = []
                
                # Store simple values directly, complex values as type info
                if isinstance(arg_value, (int, float, bool, str)) or arg_value is None:
                    value_to_store = arg_value
                elif isinstance(arg_value, (list, tuple, set)):
                    value_to_store = len(arg_value)
                elif isinstance(arg_value, dict):
                    value_to_store = len(arg_value)
                else:
                    value_to_store = type(arg_value).__name__
                
                self.arg_influence[arg_name].append((value_to_store, duration))
    
    def get_average_duration(self) -> float:
        """Get the average execution duration."""
        if not self.executions:
            return 0.0
        return sum(exec[1] for exec in self.executions) / len(self.executions)
    
    def get_average_memory_delta(self) -> float:
        """Get the average memory delta."""
        if not self.executions:
            return 0.0
        return sum(exec[2] for exec in self.executions) / len(self.executions)
    
    def get_error_rate(self) -> float:
        """Get the error rate."""
        total = len(self.executions) + len(self.error_executions)
        if total == 0:
            return 0.0
        return len(self.error_executions) / total
    
    def analyze_arg_influence(self) -> Dict[str, float]:
        """Analyze the influence of arguments on execution duration."""
        influence_scores = {}
        
        for arg_name, values in self.arg_influence.items():
            # Skip if not enough data points
            if len(values) < 5:
                influence_scores[arg_name] = 0.0
                continue
            
            # Extract numeric values for correlation analysis
            numeric_values = []
            durations = []
            
            for value, duration in values:
                if isinstance(value, (int, float)):
                    numeric_values.append(float(value))
                    durations.append(duration)
            
            # Skip if not enough numeric values
            if len(numeric_values) < 5:
                influence_scores[arg_name] = 0.0
                continue
            
            # Calculate correlation coefficient
            try:
                correlation = np.corrcoef(numeric_values, durations)[0, 1]
                influence_scores[arg_name] = abs(correlation)
            except Exception:
                influence_scores[arg_name] = 0.0
        
        return influence_scores
    
    def get_summary(self) -> Dict[str, Any]:
        """Get a summary of this performance record."""
        return {
            'function_name': self.function_name,
            'execution_count': len(self.executions),
            'error_count': len(self.error_executions),
            'error_rate': self.get_error_rate(),
            'avg_duration': self.get_average_duration(),
            'avg_memory_delta': self.get_average_memory_delta(),
            'arg_influence': self.analyze_arg_influence()
        }
    
    def _summarize_args(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """Create a summary of arguments for storage."""
        summary = {}
        
        for arg_name, arg_value in args.items():
            if isinstance(arg_value, (int, float, bool, str)) or arg_value is None:
                summary[arg_name] = arg_value
            elif isinstance(arg_value, (list, tuple, set)):
                summary[arg_name] = f"{type(arg_value).__name__}[{len(arg_value)}]"
            elif isinstance(arg_value, dict):
                summary[arg_name] = f"dict[{len(arg_value)}]"
            else:
                summary[arg_name] = type(arg_value).__name__
        
        return summary


class RuntimeAnalysis:
    """Analyzes the runtime behavior of the system."""
    
    def __init__(self, trace_manager: Optional[ExecutionTraceManager] = None):
        self.trace_manager = trace_manager or ExecutionTraceManager()
        self.performance_records = {}  # function_name -> PerformanceRecord
        self.error_logs = []  # list of (timestamp, function_name, error_message, traceback)
        self.active_functions = set()  # currently active functions
        self.resource_usage_history = []  # list of (timestamp, cpu_percent, memory_usage, disk_io)
        self._resource_monitoring_active = False
        self._resource_monitor_interval = 1.0  # seconds
        self._resource_monitor_thread = None
    
    def start_trace(self, function_name: str, args: Dict[str, Any] = None, kwargs: Dict[str, Any] = None) -> int:
        """
        Start tracing a function execution.
        
        Args:
            function_name: The name of the function.
            args: The positional arguments (as a dict with keys like '0', '1', ...).
            kwargs: The keyword arguments.
            
        Returns:
            int: A trace ID for referencing this trace.
        """
        if args is None:
            args = {}
        if kwargs is None:
            kwargs = {}
        
        # Add to active functions
        self.active_functions.add(function_name)
        
        # Create or update performance record
        if function_name not in self.performance_records:
            self.performance_records[function_name] = PerformanceRecord(function_name)
        
        # Create trace in trace manager
        context = self.trace_manager.enter_function(function_name, args, kwargs)
        
        # Return the index of this trace
        return self.trace_manager.traces.index(self.trace_manager.get_current_trace_root())
    
    def end_trace(self, trace_id: int, result: Any = None, error: Optional[str] = None):
        """
        End tracing a function execution.
        
        Args:
            trace_id: The trace ID returned by start_trace.
            result: The result of the function call.
            error: Any error that occurred during execution.
        """
        # Get the trace context
        trace = self.trace_manager.get_trace(trace_id)
        if trace is None:
            logger.error(f"Invalid trace ID: {trace_id}")
            return
        
        # Find the active context (should be the last one in the current stack)
        if self.trace_manager.current_trace_stack:
            context = self.trace_manager.current_trace_stack[-1]
            function_name = context.function_name
            
            # End the trace
            self.trace_manager.exit_function(context, result, error)
            
            # Remove from active functions
            if function_name in self.active_functions:
                self.active_functions.remove(function_name)
            
            # Update performance record
            record = self.performance_records.get(function_name)
            if record:
                record.add_execution(
                    timestamp=context.start_time,
                    duration=context.duration,
                    memory_delta=context.memory_delta or 0,
                    args={**context.args, **context.kwargs},
                    error=error
                )
            
            # Log error if any
            if error:
                self.error_logs.append((
                    time.time(),
                    function_name,
                    error,
                    traceback.format_exc() if error else None
                ))
    
    def add_sub_trace(self, parent_trace_id: int, child_trace_id: int):
        """
        Add a sub-trace to a parent trace.
        
        Args:
            parent_trace_id: The parent trace ID.
            child_trace_id: The child trace ID.
        """
        self.trace_manager.add_sub_trace(parent_trace_id, child_trace_id)
    
    def get_trace(self, trace_id: int) -> Optional[Dict[str, Any]]:
        """Get a trace by ID."""
        trace = self.trace_manager.get_trace(trace_id)
        if trace:
            return trace.to_dict()
        return None
    
    def get_performance_summary(self) -> Dict[str, Dict[str, Any]]:
        """Get a summary of performance data for all functions."""
        return {name: record.get_summary() for name, record in self.performance_records.items()}
    
    def get_function_performance(self, function_name: str) -> Optional[Dict[str, Any]]:
        """Get performance data for a specific function."""
        record = self.performance_records.get(function_name)
        if record:
            return record.get_summary()
        return None
    
    def get_error_summary(self) -> Dict[str, int]:
        """Get a summary of errors by function."""
        error_counts = Counter(error[1] for error in self.error_logs)
        return dict(error_counts)
    
    def get_recent_errors(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent errors."""
        recent_errors = []
        
        for timestamp, function_name, error_message, tb in reversed(self.error_logs[-limit:]):
            recent_errors.append({
                'timestamp': timestamp,
                'function_name': function_name,
                'error_message': error_message,
                'traceback': tb
            })
        
        return recent_errors
    
    def get_slowest_functions(self, n: int = 5) -> List[Dict[str, Any]]:
        """Get the n slowest functions based on average duration."""
        function_stats = self.trace_manager.get_function_stats()
        
        # Filter for functions with at least 5 executions
        filtered_stats = {name: stats for name, stats in function_stats.items() 
                         if stats.get('count', 0) >= 5}
        
        # Sort by average duration
        sorted_functions = sorted(
            filtered_stats.items(),
            key=lambda x: x[1].get('avg_duration', 0),
            reverse=True
        )
        
        # Convert to list of summaries
        summaries = []
        for name, stats in sorted_functions[:n]:
            record = self.performance_records.get(name)
            if record:
                summaries.append(record.get_summary())
            else:
                summaries.append({
                    'function_name': name,
                    'avg_duration': stats.get('avg_duration', 0),
                    'execution_count': stats.get('count', 0),
                    'error_count': stats.get('error_count', 0)
                })
        
        return summaries
    
    def get_most_memory_intensive_functions(self, n: int = 5) -> List[Dict[str, Any]]:
        """Get the n most memory-intensive functions based on average memory delta."""
        function_stats = self.trace_manager.get_function_stats()
        
        # Filter for functions with at least 5 executions
        filtered_stats = {name: stats for name, stats in function_stats.items() 
                         if stats.get('count', 0) >= 5}
        
        # Sort by average memory delta
        sorted_functions = sorted(
            filtered_stats.items(),
            key=lambda x: x[1].get('avg_memory_delta', 0),
            reverse=True
        )
        
        # Convert to list of summaries
        summaries = []
        for name, stats in sorted_functions[:n]:
            record = self.performance_records.get(name)
            if record:
                summaries.append(record.get_summary())
            else:
                summaries.append({
                    'function_name': name,
                    'avg_memory_delta': stats.get('avg_memory_delta', 0),
                    'execution_count': stats.get('count', 0)
                })
        
        return summaries
    
    def get_error_prone_functions(self, n: int = 5) -> List[Dict[str, Any]]:
        """Get the n most error-prone functions based on error rate."""
        function_summaries = []
        
        for name, record in self.performance_records.items():
            summary = record.get_summary()
            if summary['execution_count'] + summary['error_count'] >= 5:  # At least 5 total executions
                function_summaries.append(summary)
        
        # Sort by error rate
        function_summaries.sort(key=lambda x: x['error_rate'], reverse=True)
        
        return function_summaries[:n]
    
    def get_argument_influence_analysis(self, function_name: str) -> Dict[str, float]:
        """Analyze how arguments influence function performance."""
        record = self.performance_records.get(function_name)
        if record:
            return record.analyze_arg_influence()
        return {}
    
    def clear_traces(self):
        """Clear all traces."""
        self.trace_manager.clear_traces()
    
    def clear_performance_records(self):
        """Clear all performance records."""
        self.performance_records.clear()
    
    def clear_error_logs(self):
        """Clear all error logs."""
        self.error_logs.clear()
    
    def start_resource_monitoring(self, interval: float = 1.0):
        """Start monitoring system resources."""
        if self._resource_monitoring_active:
            return
            
        self._resource_monitor_interval = interval
        self._resource_monitoring_active = True
        
        # Start monitoring in a separate thread
        self._resource_monitor_thread = concurrent.futures.ThreadPoolExecutor(max_workers=1)
        self._resource_monitor_thread.submit(self._monitor_resources)
    
    def stop_resource_monitoring(self):
        """Stop monitoring system resources."""
        self._resource_monitoring_active = False
        if self._resource_monitor_thread:
            self._resource_monitor_thread.shutdown(wait=False)
            self._resource_monitor_thread = None
    
    def _monitor_resources(self):
        """Monitor system resources periodically."""
        try:
            import psutil
            
            while self._resource_monitoring_active:
                try:
                    # Collect resource usage
                    cpu_percent = psutil.cpu_percent(interval=None)
                    memory_info = psutil.virtual_memory()
                    disk_io = psutil.disk_io_counters()
                    
                    # Record usage
                    self.resource_usage_history.append((
                        time.time(),
                        cpu_percent,
                        memory_info.percent,
                        {
                            'read_bytes': disk_io.read_bytes if disk_io else 0,
                            'write_bytes': disk_io.write_bytes if disk_io else 0
                        }
                    ))
                    
                    # Limit history length
                    if len(self.resource_usage_history) > 1000:
                        self.resource_usage_history = self.resource_usage_history[-1000:]
                    
                except Exception as e:
                    logger.error(f"Error monitoring resources: {e}")
                
                # Sleep for the specified interval
                time.sleep(self._resource_monitor_interval)
                
        except ImportError:
            logger.warning("psutil is required for resource monitoring. Please install it with pip install psutil.")
    
    def get_resource_usage_summary(self) -> Dict[str, Any]:
        """Get a summary of resource usage."""
        if not self.resource_usage_history:
            return {
                'cpu_percent': None,
                'memory_percent': None,
                'disk_read_bytes': None,
                'disk_write_bytes': None
            }
        
        # Calculate averages over the last minute
        one_minute_ago = time.time() - 60
        recent_history = [entry for entry in self.resource_usage_history if entry[0] >= one_minute_ago]
        
        if not recent_history:
            recent_history = self.resource_usage_history[-10:]  # Use last 10 entries if no recent data
        
        avg_cpu = sum(entry[1] for entry in recent_history) / len(recent_history)
        avg_memory = sum(entry[2] for entry in recent_history) / len(recent_history)
        
        # Calculate disk IO rates (bytes per second)
        if len(recent_history) >= 2:
            first = recent_history[0]
            last = recent_history[-1]
            time_diff = last[0] - first[0]
            
            if time_diff > 0:
                read_diff = last[3]['read_bytes'] - first[3]['read_bytes']
                write_diff = last[3]['write_bytes'] - first[3]['write_bytes']
                
                read_rate = read_diff / time_diff
                write_rate = write_diff / time_diff
            else:
                read_rate = 0
                write_rate = 0
        else:
            read_rate = 0
            write_rate = 0
        
        return {
            'cpu_percent': avg_cpu,
            'memory_percent': avg_memory,
            'disk_read_bytes_per_sec': read_rate,
            'disk_write_bytes_per_sec': write_rate
        }


# ------------------------------------------------------------------------------
# Performance Modeling
# ------------------------------------------------------------------------------

class PerformanceFeature:
    """Represents a feature used in performance modeling."""
    
    def __init__(self, name: str, extractor: Callable[[Dict[str, Any]], float]):
        self.name = name
        self.extractor = extractor
    
    def extract(self, args: Dict[str, Any]) -> float:
        """Extract the feature value from function arguments."""
        try:
            return float(self.extractor(args))
        except Exception:
            return 0.0


class FeatureExtractor:
    """Extracts features from function arguments for performance modeling."""
    
    def __init__(self):
        self.features = []
    
    def add_numeric_feature(self, name: str, arg_path: str):
        """
        Add a feature that extracts a numeric value from arguments.
        
        Args:
            name: Feature name
            arg_path: Path to the argument value (e.g., 'x', 'options.timeout')
        """
        def extractor(args):
            value = self._get_value_by_path(args, arg_path)
            if value is None:
                return 0.0
            elif isinstance(value, (int, float)):
                return float(value)
            elif isinstance(value, bool):
                return 1.0 if value else 0.0
            elif isinstance(value, str):
                return float(len(value))
            elif isinstance(value, (list, tuple, set, dict)):
                return float(len(value))
            else:
                return 0.0
        
        self.features.append(PerformanceFeature(f"{name}", extractor))
    
    def add_collection_size_feature(self, name: str, arg_path: str):
        """
        Add a feature that extracts the size of a collection argument.
        
        Args:
            name: Feature name
            arg_path: Path to the collection argument
        """
        def extractor(args):
            value = self._get_value_by_path(args, arg_path)
            if isinstance(value, (list, tuple, set, dict)):
                return float(len(value))
            return 0.0
        
        self.features.append(PerformanceFeature(f"{name}_size", extractor))
    
    def add_type_feature(self, name: str, arg_path: str, possible_types: List[str]):
        """
        Add features for the type of an argument (one-hot encoded).
        
        Args:
            name: Feature name prefix
            arg_path: Path to the argument
            possible_types: List of possible type names to check
        """
        for type_name in possible_types:
            def extractor(args, type_name=type_name):
                value = self._get_value_by_path(args, arg_path)
                if value is not None and type(value).__name__ == type_name:
                    return 1.0
                return 0.0
            
            self.features.append(PerformanceFeature(f"{name}_is_{type_name}", extractor))
    
    def add_string_length_feature(self, name: str, arg_path: str):
        """
        Add a feature that extracts the length of a string argument.
        
        Args:
            name: Feature name
            arg_path: Path to the string argument
        """
        def extractor(args):
            value = self._get_value_by_path(args, arg_path)
            if isinstance(value, str):
                return float(len(value))
            return 0.0
        
        self.features.append(PerformanceFeature(f"{name}_length", extractor))
    
    def add_constant_feature(self, value: float = 1.0):
        """
        Add a constant feature (always returns the same value).
        
        Args:
            value: The constant value
        """
        def extractor(_):
            return value
        
        self.features.append(PerformanceFeature("constant", extractor))
    
    def extract_features(self, args: Dict[str, Any]) -> List[float]:
        """Extract all features from arguments."""
        return [feature.extract(args) for feature in self.features]
    
    def get_feature_names(self) -> List[str]:
        """Get the names of all features."""
        return [feature.name for feature in self.features]
    
    def _get_value_by_path(self, args: Dict[str, Any], path: str) -> Any:
        """Get a value from a nested dictionary by path."""
        if not path:
            return None
            
        parts = path.split('.')
        value = args
        
        for part in parts:
            if isinstance(value, dict) and part in value:
                value = value[part]
            else:
                return None
                
        return value


class LinearPerformanceModel:
    """Linear regression model for predicting function performance."""
    
    def __init__(self, feature_extractor: FeatureExtractor):
        self.feature_extractor = feature_extractor
        self.coefficients = None
        self.intercept = 0.0
        self.r_squared = 0.0
        self.mean_squared_error = 0.0
        self.feature_importances = {}
    
    def fit(self, args_list: List[Dict[str, Any]], durations: List[float]):
        """
        Fit the model to training data.
        
        Args:
            args_list: List of function argument dictionaries
            durations: List of corresponding execution durations
        """
        if len(args_list) != len(durations):
            raise ValueError("Number of argument dictionaries must match number of durations")
            
        if len(args_list) < 2:
            # Not enough data to fit a model
            self.coefficients = [0.0] * len(self.feature_extractor.features)
            self.intercept = durations[0] if durations else 0.0
            self.r_squared = 0.0
            self.mean_squared_error = 0.0
            return
        
        # Extract features
        X = np.array([self.feature_extractor.extract_features(args) for args in args_list])
        y = np.array(durations)
        
        # Add column of ones for intercept
        X_with_intercept = np.column_stack((np.ones(X.shape[0]), X))
        
        # Solve for coefficients using normal equations: (X^T X)^(-1) X^T y
        try:
            # Use pseudo-inverse for numerical stability
            coeffs = np.linalg.pinv(X_with_intercept.T @ X_with_intercept) @ X_with_intercept.T @ y
            
            self.intercept = coeffs[0]
            self.coefficients = coeffs[1:]
            
            # Calculate R-squared
            y_pred = self.intercept + X @ self.coefficients
            ss_total = np.sum((y - np.mean(y)) ** 2)
            ss_residual = np.sum((y - y_pred) ** 2)
            
            if ss_total > 0:
                self.r_squared = 1 - (ss_residual / ss_total)
            else:
                self.r_squared = 0.0
                
            # Calculate mean squared error
            self.mean_squared_error = np.mean((y - y_pred) ** 2)
            
            # Calculate feature importances
            feature_names = self.feature_extractor.get_feature_names()
            if len(feature_names) == len(self.coefficients):
                # Normalize coefficients to get importances
                if np.sum(np.abs(self.coefficients)) > 0:
                    importances = np.abs(self.coefficients) / np.sum(np.abs(self.coefficients))
                    self.feature_importances = {name: float(imp) for name, imp in zip(feature_names, importances)}
                else:
                    self.feature_importances = {name: 0.0 for name in feature_names}
            
        except np.linalg.LinAlgError:
            # Fallback to simple mean if matrix is singular
            self.intercept = np.mean(y)
            self.coefficients = [0.0] * len(self.feature_extractor.features)
            self.r_squared = 0.0
            self.mean_squared_error = np.mean((y - self.intercept) ** 2)
    
    def predict(self, args: Dict[str, Any]) -> float:
        """
        Predict execution duration for given arguments.
        
        Args:
            args: Function arguments
            
        Returns:
            float: Predicted execution duration
        """
        if self.coefficients is None:
            return 0.0
            
        features = self.feature_extractor.extract_features(args)
        return self.intercept + sum(c * f for c, f in zip(self.coefficients, features))
    
    def get_important_features(self, top_n: int = 5) -> List[Tuple[str, float]]:
        """
        Get the most important features for this model.
        
        Args:
            top_n: Number of top features to return
            
        Returns:
            List[Tuple[str, float]]: List of (feature_name, importance) pairs
        """
        sorted_features = sorted(
            self.feature_importances.items(),
            key=lambda x: x[1],
            reverse=True
        )
        
        return sorted_features[:top_n]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert the model to a dictionary for serialization."""
        return {
            'intercept': self.intercept,
            'coefficients': self.coefficients.tolist() if isinstance(self.coefficients, np.ndarray) else self.coefficients,
            'r_squared': self.r_squared,
            'mean_squared_error': self.mean_squared_error,
            'feature_importances': self.feature_importances,
            'feature_names': self.feature_extractor.get_feature_names()
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any], feature_extractor: FeatureExtractor) -> 'LinearPerformanceModel':
        """Create a model from a dictionary."""
        model = cls(feature_extractor)
        model.intercept = data.get('intercept', 0.0)
        model.coefficients = np.array(data.get('coefficients', []))
        model.r_squared = data.get('r_squared', 0.0)
        model.mean_squared_error = data.get('mean_squared_error', 0.0)
        model.feature_importances = data.get('feature_importances', {})
        return model


class PerformanceModel:
    """Models the performance implications of code changes."""
    
    def __init__(self, runtime_analysis: RuntimeAnalysis):
        self.runtime_analysis = runtime_analysis
        self.models = {}  # function_name -> LinearPerformanceModel
        
        # Import scipy's norm function for statistical calculations if available
        try:
            from scipy.stats import norm
            self.norm = norm
        except ImportError:
            # Create a simplified norm approximation if scipy is not available
            class SimplifiedNorm:
                def __init__(self):
                    pass
                    
                def cdf(self, x):
                    """Approximate normal CDF using a sigmoid function."""
                    return 1.0 / (1.0 + np.exp(-1.702 * x))
                    
                def pdf(self, x):
                    """Approximate normal PDF using a gaussian function."""
                    return np.exp(-0.5 * x * x) / np.sqrt(2.0 * np.pi)
            
            self.norm = SimplifiedNorm()
            logger.warning("scipy is not installed. Using simplified normal distribution approximations.")
    
    def build_models(self):
        """Build performance models for functions based on runtime data."""
        # Get all function performance records
        perf_records = self.runtime_analysis.performance_records
        
        for function_name, record in perf_records.items():
            # Skip functions with too few executions
            if len(record.executions) < 5:
                continue
            
            # Create feature extractor based on argument patterns
            feature_extractor = self._create_feature_extractor(record)
            
            # Extract training data
            args_list = []
            durations = []
            
            for timestamp, duration, memory_delta, args_summary in record.executions:
                args_list.append(args_summary)
                durations.append(duration)
            
            # Create and fit model
            model = LinearPerformanceModel(feature_extractor)
            model.fit(args_list, durations)
            
            # Store model
            self.models[function_name] = model
            
            logger.info(f"Built performance model for function {function_name} with R² = {model.r_squared:.4f}")
    
    def _create_feature_extractor(self, record: PerformanceRecord) -> FeatureExtractor:
        """Create a feature extractor based on observed argument patterns."""
        feature_extractor = FeatureExtractor()
        
        # Always add a constant feature
        feature_extractor.add_constant_feature()
        
        # Analyze arguments across all executions
        arg_types = {}  # arg_name -> set of observed types
        arg_values = {}  # arg_name -> list of observed values
        
        for _, _, _, args_summary in record.executions:
            for arg_name, arg_value in args_summary.items():
                # Track types
                if arg_name not in arg_types:
                    arg_types[arg_name] = set()
                    arg_values[arg_name] = []
                
                arg_types[arg_name].add(type(arg_value).__name__ if arg_value is not None else 'NoneType')
                arg_values[arg_name].append(arg_value)
        
        # Create appropriate features for each argument
        for arg_name, types in arg_types.items():
            values = arg_values[arg_name]
            
            # Numeric feature for numeric arguments
            if any(t in ('int', 'float') for t in types):
                feature_extractor.add_numeric_feature(arg_name, arg_name)
            
            # Size feature for collection arguments
            if any(t.endswith(']') for t in types) or 'dict' in types:
                feature_extractor.add_collection_size_feature(arg_name, arg_name)
            
            # Length feature for string arguments
            if 'str' in types:
                feature_extractor.add_string_length_feature(arg_name, arg_name)
            
            # Type features for arguments with multiple possible types
            if len(types) > 1:
                feature_extractor.add_type_feature(arg_name, arg_name, list(types))
        
        return feature_extractor
    
    def predict_performance(self, function_name: str, args: Dict[str, Any]) -> Dict[str, float]:
        """
        Predict the performance of a function call.
        
        Args:
            function_name: The name of the function.
            args: The arguments to pass to the function.
            
        Returns:
            Dict[str, float]: Predicted execution time and confidence.
        """
        if function_name not in self.models:
            return {
                'predicted_time': None,
                'confidence': 0.0
            }
        
        model = self.models[function_name]
        prediction = model.predict(args)
        
        # Compute confidence based on model quality
        confidence = min(1.0, max(0.0, model.r_squared))
        
        return {
            'predicted_time': prediction,
            'confidence': confidence
        }
    
    def predict_code_change_impact(self, 
                                  function_name: str, 
                                  before_code: str, 
                                  after_code: str) -> Dict[str, float]:
        """
        Predict the performance impact of a code change.
        
        Args:
            function_name: The name of the function.
            before_code: The function code before the change.
            after_code: The function code after the change.
            
        Returns:
            Dict[str, float]: Predicted impact on execution time.
        """
        # Get baseline performance from runtime analysis
        baseline = 0.0
        record = self.runtime_analysis.performance_records.get(function_name)
        if record:
            baseline = record.get_average_duration()
        
        # Analyze code complexity change
        before_complexity = self._analyze_code_complexity(before_code)
        after_complexity = self._analyze_code_complexity(after_code)
        
        # Calculate relative change in complexity
        if before_complexity > 0:
            complexity_ratio = after_complexity / before_complexity
        else:
            complexity_ratio = 1.0 if after_complexity == 0 else 1.5
        
        # Analyze code structural changes
        structural_impact = self._analyze_structural_changes(before_code, after_code)
        
        # Combine factors to predict performance impact
        # This is a simplified model based on the assumption that performance 
        # roughly scales with complexity, but is also affected by specific structural changes
        
        impact_multiplier = complexity_ratio * (1.0 + structural_impact)
        
        # Predict new performance
        predicted_time = baseline * impact_multiplier
        time_change = predicted_time - baseline
        
        # Calculate confidence based on the quality of the analysis
        confidence = 0.0
        model = self.models.get(function_name)
        if model:
            confidence = min(0.7, model.r_squared)  # Cap at 0.7 since this is an estimate
        else:
            confidence = 0.4  # Base confidence for complexity analysis
        
        return {
            'baseline_time': baseline,
            'predicted_time': predicted_time,
            'time_change': time_change,
            'time_change_ratio': impact_multiplier - 1.0,
            'complexity_before': before_complexity,
            'complexity_after': after_complexity,
            'complexity_ratio': complexity_ratio,
            'structural_impact': structural_impact,
            'confidence': confidence
        }
    
    def _analyze_code_complexity(self, code: str) -> float:
        """Analyze the complexity of a code snippet."""
        try:
            # Parse the code
            tree = ast.parse(code)
            
            # Count various complexity factors
            complexity = 1.0  # Base complexity
            
            # Count statements and expressions
            statement_count = 0
            expression_count = 0
            
            # Count control structures
            if_count = 0
            loop_count = 0
            try_count = 0
            function_call_count = 0
            
            # Visit all nodes
            for node in ast.walk(tree):
                # Statements
                if isinstance(node, (ast.Assign, ast.AugAssign, ast.Return, ast.Expr, 
                                    ast.Pass, ast.Break, ast.Continue)):
                    statement_count += 1
                
                # Expressions
                if isinstance(node, (ast.BinOp, ast.UnaryOp, ast.BoolOp, ast.Compare, 
                                    ast.IfExp, ast.comprehension)):
                    expression_count += 1
                
                # Control structures
                if isinstance(node, ast.If):
                    if_count += 1
                elif isinstance(node, (ast.For, ast.While)):
                    loop_count += 1
                elif isinstance(node, ast.Try):
                    try_count += 1
                elif isinstance(node, ast.Call):
                    function_call_count += 1
            
            # Calculate weighted complexity
            complexity = (
                1.0 +
                0.1 * statement_count +
                0.2 * expression_count +
                0.5 * if_count +
                1.0 * loop_count +
                0.8 * try_count +
                0.3 * function_call_count
            )
            
            return complexity
            
        except SyntaxError:
            # If code can't be parsed, return a default value
            return 10.0  # Assume moderately complex
        
        except Exception as e:
            logger.error(f"Error analyzing code complexity: {e}")
            return 5.0  # Default fallback
    
    def _analyze_structural_changes(self, before_code: str, after_code: str) -> float:
        """Analyze structural changes between two code versions."""
        try:
            # Parse the code
            before_tree = ast.parse(before_code)
            after_tree = ast.parse(after_code)
            
            # Count nodes by type before and after
            before_counts = self._count_node_types(before_tree)
            after_counts = self._count_node_types(after_tree)
            
            # Calculate change in node distribution
            all_types = set(before_counts.keys()) | set(after_counts.keys())
            type_changes = {}
            
            for node_type in all_types:
                before_count = before_counts.get(node_type, 0)
                after_count = after_counts.get(node_type, 0)
                
                if before_count > 0:
                    type_changes[node_type] = (after_count - before_count) / before_count
                else:
                    type_changes[node_type] = 1.0  # New type added
            
            # Assign impact weights to different change types
            impact_weights = {
                'Call': 0.5,         # Function calls are costly
                'For': 1.0,          # Loops are very costly
                'While': 1.0,        # Loops are very costly
                'If': 0.3,           # Conditionals are moderately costly
                'BinOp': 0.2,        # Binary operations are slightly costly
                'Subscript': 0.4,    # Array/dictionary access is moderately costly
                'Try': 0.3,          # Exception handling is moderately costly
                'comprehension': 0.6 # Comprehensions can be costly
            }
            
            # Calculate weighted impact
            total_impact = 0.0
            
            for node_type, change in type_changes.items():
                weight = impact_weights.get(node_type, 0.1)  # Default weight
                impact = weight * change
                total_impact += impact
            
            # Normalize impact to a reasonable range
            return min(1.0, max(-0.5, total_impact / 3.0))
            
        except SyntaxError:
            # If code can't be parsed, return a moderate impact
            return 0.3
        
        except Exception as e:
            logger.error(f"Error analyzing structural changes: {e}")
            return 0.2  # Default fallback
    
    def _count_node_types(self, tree: ast.AST) -> Dict[str, int]:
        """Count the occurrences of each AST node type."""
        counts = defaultdict(int)
        
        for node in ast.walk(tree):
            node_type = type(node).__name__
            counts[node_type] += 1
        
        return counts
    
    def get_function_model_info(self, function_name: str) -> Optional[Dict[str, Any]]:
        """Get information about a function's performance model."""
        model = self.models.get(function_name)
        if model:
            return {
                'function_name': function_name,
                'r_squared': model.r_squared,
                'mean_squared_error': model.mean_squared_error,
                'important_features': model.get_important_features(),
                'coefficient_count': len(model.coefficients) if model.coefficients is not None else 0
            }
        return None
    
    def analyze_bottlenecks(self) -> List[Dict[str, Any]]:
        """Analyze performance bottlenecks in the system."""
        bottlenecks = []
        
        # Get function statistics
        function_stats = self.runtime_analysis.trace_manager.get_function_stats()
        
        # Calculate total execution time for all functions
        total_time = sum(stats.get('total_duration', 0) for stats in function_stats.values())
        
        if total_time <= 0:
            return bottlenecks
        
        # Identify bottleneck functions (those consuming a significant portion of time)
        for function_name, stats in function_stats.items():
            if stats.get('count', 0) < 5:
                continue  # Skip functions with few calls
                
            duration = stats.get('total_duration', 0)
            time_fraction = duration / total_time
            
            if time_fraction > 0.05:  # More than 5% of total time
                # Get performance model info if available
                model_info = self.get_function_model_info(function_name)
                
                bottleneck = {
                    'function_name': function_name,
                    'total_duration': duration,
                    'time_fraction': time_fraction,
                    'call_count': stats.get('count', 0),
                    'avg_duration': stats.get('avg_duration', 0),
                    'has_model': model_info is not None
                }
                
                if model_info:
                    bottleneck['model_info'] = model_info
                
                bottlenecks.append(bottleneck)
        
        # Sort bottlenecks by time fraction (highest first)
        bottlenecks.sort(key=lambda x: x['time_fraction'], reverse=True)
        
        return bottlenecks
    
    def recommend_optimizations(self, function_name: str) -> List[Dict[str, Any]]:
        """Recommend optimizations for a function based on performance model."""
        recommendations = []
        
        # Get function model
        model = self.models.get(function_name)
        if not model:
            return recommendations
        
        # Get function performance record
        record = self.runtime_analysis.performance_records.get(function_name)
        if not record:
            return recommendations
        
        # Analyze important features
        important_features = model.get_important_features()
        
        for feature_name, importance in important_features:
            if importance < 0.1:
                continue  # Skip less important features
                
            if feature_name == 'constant':
                # Constant overhead
                if model.intercept > 0.01:  # Significant constant overhead
                    recommendations.append({
                        'type': 'reduce_overhead',
                        'description': 'Reduce constant overhead in function',
                        'importance': importance,
                        'details': f'Constant overhead is {model.intercept:.4f} seconds',
                        'estimated_impact': 0.2
                    })
            
            elif '_size' in feature_name:
                # Collection size optimization
                arg_name = feature_name.replace('_size', '')
                recommendations.append({
                    'type': 'optimize_collection',
                    'description': f'Optimize handling of collection argument "{arg_name}"',
                    'importance': importance,
                    'details': f'Collection size significantly impacts performance',
                    'estimated_impact': 0.3 * importance
                })
            
            elif '_length' in feature_name:
                # String length optimization
                arg_name = feature_name.replace('_length', '')
                recommendations.append({
                    'type': 'optimize_string',
                    'description': f'Optimize handling of string argument "{arg_name}"',
                    'importance': importance,
                    'details': f'String length significantly impacts performance',
                    'estimated_impact': 0.25 * importance
                })
            
            elif any(t in feature_name for t in ['_is_int', '_is_float', '_is_bool']):
                # Numeric argument optimization
                parts = feature_name.split('_is_')
                arg_name = parts[0]
                recommendations.append({
                    'type': 'optimize_numeric',
                    'description': f'Optimize handling of numeric argument "{arg_name}"',
                    'importance': importance,
                    'details': f'Numeric processing significantly impacts performance',
                    'estimated_impact': 0.2 * importance
                })
        
        # Analyze call patterns
        if record.executions:
            # Check for potential caching opportunities
            if self._has_caching_potential(record):
                recommendations.append({
                    'type': 'add_caching',
                    'description': 'Add result caching to improve performance',
                    'importance': 0.8,
                    'details': 'Function shows repetitive calls with same arguments',
                    'estimated_impact': 0.4
                })
        
        # Sort recommendations by estimated impact (highest first)
        recommendations.sort(key=lambda x: x['estimated_impact'], reverse=True)
        
        return recommendations
    
    def _has_caching_potential(self, record: PerformanceRecord) -> bool:
        """Check if a function has potential for caching based on its call pattern."""
        # Extract argument patterns
        arg_patterns = []
        
        for _, _, _, args_summary in record.executions[-100:]:  # Look at recent executions
            # Create a hashable representation of arguments
            try:
                arg_key = json.dumps(args_summary, sort_keys=True)
                arg_patterns.append(arg_key)
            except (TypeError, ValueError):
                # If arguments can't be serialized, assume they're unique
                arg_patterns.append(str(hash(str(args_summary))))
        
        # Count occurrences of each pattern
        pattern_counts = Counter(arg_patterns)
        
        # Calculate repetition ratio
        total_calls = len(arg_patterns)
        unique_patterns = len(pattern_counts)
        
        if total_calls == 0:
            return False
            
        repetition_ratio = 1.0 - (unique_patterns / total_calls)
        
        # Check if there are patterns with multiple occurrences
        has_repetitive_patterns = any(count > 1 for count in pattern_counts.values())
        
        # Return True if there's significant repetition
        return repetition_ratio > 0.2 and has_repetitive_patterns


# ------------------------------------------------------------------------------
# Self-Explanation
# ------------------------------------------------------------------------------

class SelfExplanation:
    """Generates explanations of the system's own computational processes."""
    
    def __init__(self, code_representation: CodeRepresentation, runtime_analysis: RuntimeAnalysis):
        self.code_representation = code_representation
        self.runtime_analysis = runtime_analysis
    
    def explain_function(self, function_name: str) -> Dict[str, Any]:
        """
        Generate an explanation of a function.
        
        Args:
            function_name: The name of the function to explain.
            
        Returns:
            Dict[str, Any]: Explanation of the function.
        """
        explanation = {
            'function_name': function_name,
            'description': None,
            'arguments': [],
            'return_type': None,
            'algorithm': [],
            'complexity': None,
            'performance': {},
            'dependencies': []
        }
        
        # Get function info from code representation
        function_info = self.code_representation.get_function_info(function_name)
        if not function_info:
            return {
                'function_name': function_name,
                'error': 'Function not found'
            }
        
        # Fill in basic information
        explanation['description'] = function_info.get('docstring', 'No description available.')
        explanation['arguments'] = function_info.get('arguments', [])
        explanation['return_type'] = function_info.get('return_type')
        explanation['complexity'] = function_info.get('complexity')
        explanation['dependencies'] = function_info.get('calls', [])
        
        # Generate algorithm explanation in natural language
        explanation['algorithm'] = self._generate_algorithm_explanation(function_info)
        
        # Add performance information
        function_stats = self.runtime_analysis.trace_manager.get_function_stats().get(function_name, {})
        if function_stats:
            explanation['performance'] = {
                'avg_duration': function_stats.get('avg_duration'),
                'min_duration': function_stats.get('min_duration'),
                'max_duration': function_stats.get('max_duration'),
                'call_count': function_stats.get('count', 0),
                'error_count': function_stats.get('error_count', 0)
            }
        
        # Add memory usage information
        if function_stats:
            explanation['memory_usage'] = {
                'avg_delta': function_stats.get('avg_memory_delta'),
                'min_delta': function_stats.get('min_memory_delta'),
                'max_delta': function_stats.get('max_memory_delta')
            }
        
        # Add argument influence analysis
        arg_influence = self.runtime_analysis.get_argument_influence_analysis(function_name)
        if arg_influence:
            explanation['argument_influence'] = arg_influence
        
        return explanation
    
    def explain_class(self, class_name: str) -> Dict[str, Any]:
        """
        Generate an explanation of a class.
        
        Args:
            class_name: The name of the class to explain.
            
        Returns:
            Dict[str, Any]: Explanation of the class.
        """
        explanation = {
            'class_name': class_name,
            'description': None,
            'base_classes': [],
            'attributes': [],
            'methods': [],
            'total_complexity': 0
        }
        
        # Get class info from code representation
        class_info = self.code_representation.get_class_info(class_name)
        if not class_info:
            return {
                'class_name': class_name,
                'error': 'Class not found'
            }
        
        # Fill in basic information
        explanation['description'] = class_info.get('docstring', 'No description available.')
        explanation['base_classes'] = class_info.get('base_classes', [])
        explanation['attributes'] = class_info.get('attributes', [])
        explanation['total_complexity'] = class_info.get('total_complexity', 0)
        
        # Process methods
        methods = []
        methods_info = class_info.get('methods', {})
        
        for method_name, method_info in methods_info.items():
            method_summary = {
                'name': method_name,
                'description': method_info.get('docstring', 'No description available.'),
                'arguments': method_info.get('arguments', []),
                'return_type': method_info.get('return_type'),
                'complexity': method_info.get('complexity', 0),
                'is_static': method_info.get('is_static', False),
                'is_class_method': method_info.get('is_class_method', False),
                'is_property': method_info.get('is_property', False)
            }
            
            # Add performance information if available
            full_method_name = f"{class_name}.{method_name}"
            function_stats = self.runtime_analysis.trace_manager.get_function_stats().get(full_method_name, {})
            if function_stats:
                method_summary['performance'] = {
                    'avg_duration': function_stats.get('avg_duration'),
                    'call_count': function_stats.get('count', 0),
                    'error_count': function_stats.get('error_count', 0)
                }
            
            methods.append(method_summary)
        
        explanation['methods'] = methods
        
        return explanation
    
    def explain_process(self, trace_id: int) -> Dict[str, Any]:
        """
        Generate an explanation of a process execution.
        
        Args:
            trace_id: The ID of the execution trace to explain.
            
        Returns:
            Dict[str, Any]: Explanation of the process.
        """
        explanation = {
            'trace_id': trace_id,
            'function_name': None,
            'arguments': None,
            'result': None,
            'error': None,
            'duration': None,
            'memory_usage': None,
            'steps': []
        }
        
        # Get trace data
        trace = self.runtime_analysis.get_trace(trace_id)
        if not trace:
            return {
                'trace_id': trace_id,
                'error': 'Trace not found'
            }
        
        # Fill in basic information
        explanation['function_name'] = trace.get('function_name')
        explanation['arguments'] = trace.get('args')
        explanation['result'] = trace.get('result')
        explanation['error'] = trace.get('error')
        explanation['duration'] = trace.get('duration')
        explanation['memory_usage'] = trace.get('memory_delta')
        
        # Reconstruct steps from children
        children = trace.get('children', [])
        for child in children:
            step = {
                'function_name': child.get('function_name'),
                'arguments': child.get('args'),
                'result': child.get('result'),
                'duration': child.get('duration'),
                'error': child.get('error')
            }
            explanation['steps'].append(step)
        
        return explanation
    
    def generate_system_explanation(self) -> Dict[str, Any]:
        """
        Generate an explanation of the overall system.
        
        Returns:
            Dict[str, Any]: Explanation of the system.
        """
        # Get code summary
        code_summary = self.code_representation.get_module_summary()
        
        # Get performance data
        slowest_functions = self.runtime_analysis.get_slowest_functions(5)
        memory_intensive_functions = self.runtime_analysis.get_most_memory_intensive_functions(5)
        error_prone_functions = self.runtime_analysis.get_error_prone_functions(5)
        
        # Get resource usage
        resource_usage = self.runtime_analysis.get_resource_usage_summary()
        
        # Create explanation
        explanation = {
            'code_structure': {
                'module_count': code_summary.get('module_count', 0),
                'function_count': code_summary.get('function_count', 0),
                'class_count': code_summary.get('class_count', 0),
                'method_count': code_summary.get('method_count', 0),
                'total_complexity': code_summary.get('total_complexity', 0)
            },
            'performance': {
                'slowest_functions': slowest_functions,
                'memory_intensive_functions': memory_intensive_functions,
                'error_prone_functions': error_prone_functions
            },
            'resource_usage': resource_usage
        }
        
        # Add dependency analysis
        dependency_graph = self.code_representation.generate_dependency_graph()
        if dependency_graph:
            # Calculate module coupling metrics
            module_dependencies = {}
            module_dependents = {}
            
            for module, dependencies in dependency_graph.items():
                module_dependencies[module] = len(dependencies)
                
                for dep in dependencies:
                    if dep not in module_dependents:
                        module_dependents[dep] = 0
                    module_dependents[dep] += 1
            
            # Find highly coupled modules
            highly_coupled = []
            for module, dep_count in module_dependencies.items():
                dependent_count = module_dependents.get(module, 0)
                
                if dep_count + dependent_count > 5:  # Arbitrary threshold
                    highly_coupled.append({
                        'module': module,
                        'dependencies': dep_count,
                        'dependents': dependent_count,
                        'coupling_score': dep_count + dependent_count
                    })
            
            # Sort by coupling score
            highly_coupled.sort(key=lambda x: x['coupling_score'], reverse=True)
            
            explanation['dependencies'] = {
                'highly_coupled_modules': highly_coupled[:5]  # Top 5
            }
        
        return explanation
    
    def _generate_algorithm_explanation(self, function_info: Dict[str, Any]) -> List[str]:
        """Generate a natural language explanation of an algorithm."""
        explanation = []
        
        # Add a description from the docstring
        docstring = function_info.get('docstring', '')
        if docstring:
            # Remove parameter and return sections from docstring
            lines = docstring.split('\n')
            description_lines = []
            
            in_params_or_returns = False
            for line in lines:
                stripped = line.strip()
                
                if stripped.startswith(('Args:', 'Arguments:', 'Parameters:', 'Returns:', 'Raises:', 'Yields:')):
                    in_params_or_returns = True
                    continue
                
                if in_params_or_returns:
                    if stripped == '' or stripped.startswith(('---', '===', '***')):
                        in_params_or_returns = False
                    else:
                        continue
                
                description_lines.append(line)
            
            description = '\n'.join(description_lines).strip()
            
            if description:
                explanation.append(description)
        
        # If no description could be extracted or the docstring was empty
        if not explanation:
            explanation.append("This function performs operations as defined in its implementation.")
        
        # Describe function signature
        arguments = function_info.get('arguments', [])
        if arguments:
            args_description = []
            for arg in arguments:
                arg_type = arg.get('type', 'unknown')
                arg_name = arg.get('name', '')
                has_default = arg.get('has_default', False)
                
                desc = f"{arg_name} ({arg_type})"
                if has_default:
                    desc += " (optional)"
                    
                args_description.append(desc)
            
            explanation.append(f"Takes arguments: {', '.join(args_description)}.")
        
        # Describe return type
        return_type = function_info.get('return_type')
        if return_type and return_type != 'unknown':
            explanation.append(f"Returns a result of type {return_type}.")
        
        # Describe complexity
        complexity = function_info.get('complexity', 0)
        if complexity > 0:
            if complexity <= 5:
                explanation.append("Has low algorithmic complexity (simple, likely linear time).")
            elif complexity <= 10:
                explanation.append("Has moderate algorithmic complexity (potentially quadratic time in some cases).")
            else:
                explanation.append("Has high algorithmic complexity (potentially exponential or complex control flow).")
        
        # Describe dependencies
        calls = function_info.get('calls', [])
        if calls:
            if len(calls) <= 3:
                explanation.append(f"Calls other functions: {', '.join(calls)}.")
            else:
                explanation.append(f"Calls {len(calls)} other functions, including: {', '.join(calls[:3])}...")
        
        # Add performance information if available
        perf_stats = self.runtime_analysis.get_function_performance(function_info.get('name', ''))
        if perf_stats:
            avg_duration = perf_stats.get('avg_duration')
            if avg_duration is not None:
                if avg_duration < 0.001:
                    explanation.append(f"Typically executes very quickly (under 1ms).")
                elif avg_duration < 0.01:
                    explanation.append(f"Typically executes in about {avg_duration*1000:.1f}ms.")
                elif avg_duration < 0.1:
                    explanation.append(f"Typically executes in about {avg_duration*1000:.0f}ms.")
                else:
                    explanation.append(f"Typically executes in about {avg_duration:.2f} seconds.")
        
        # Try to infer the algorithm's purpose from its name and structure
        function_name = function_info.get('name', '').lower()
        common_prefixes = {
            'get_': 'Retrieves or computes',
            'fetch_': 'Fetches data from',
            'load_': 'Loads data from',
            'save_': 'Saves data to',
            'update_': 'Updates',
            'compute_': 'Computes',
            'calculate_': 'Calculates',
            'process_': 'Processes',
            'validate_': 'Validates',
            'check_': 'Checks',
            'is_': 'Checks if',
            'has_': 'Checks if',
            'create_': 'Creates',
            'generate_': 'Generates',
            'convert_': 'Converts',
            'parse_': 'Parses',
            'format_': 'Formats',
            'transform_': 'Transforms',
            'search_': 'Searches for',
            'find_': 'Finds',
            'filter_': 'Filters',
            'sort_': 'Sorts',
            'merge_': 'Merges',
            'combine_': 'Combines',
            'split_': 'Splits',
            'extract_': 'Extracts',
            'analyze_': 'Analyzes'
        }
        
        for prefix, description in common_prefixes.items():
            if function_name.startswith(prefix):
                name_without_prefix = function_name[len(prefix):]
                explanation.append(f"{description} {name_without_prefix.replace('_', ' ')}.")
                break
        
        return explanation


# ------------------------------------------------------------------------------
# Computational Reflection
# ------------------------------------------------------------------------------

class ComputationalReflection:
    """
    Enables ULTRA to reason about its own computational processes, analyze its code
    and architecture, and understand the implications of potential modifications.
    """
    
    def __init__(self):
        self.code_representations = {}  # module_name -> CodeRepresentation
        self.runtime_analysis = RuntimeAnalysis()
        self.performance_models = {}  # module_name -> PerformanceModel
        self.self_explanation = None
        self.active_trace_stack = []  # stack of (trace_id, function_name)
    
    def analyze_module(self, module_name: str) -> CodeRepresentation:
        """
        Analyze a module's code.
        
        Args:
            module_name: The name of the module to analyze.
            
        Returns:
            CodeRepresentation: The code representation for the module.
        """
        if module_name in self.code_representations:
            return self.code_representations[module_name]
            
        code_repr = CodeRepresentation(module_name)
        self.code_representations[module_name] = code_repr
        
        # If this is the first module analyzed, create self_explanation
        if self.self_explanation is None:
            self.self_explanation = SelfExplanation(code_repr, self.runtime_analysis)
        
        return code_repr
    
    def analyze_file(self, file_path: str) -> CodeRepresentation:
        """
        Analyze a Python file.
        
        Args:
            file_path: Path to the Python file.
            
        Returns:
            CodeRepresentation: The code representation for the file.
        """
        # Create a temporary code representation for this file
        code_repr = CodeRepresentation()
        module_entry = code_repr.add_file(file_path)
        
        if module_entry:
            module_name = module_entry.name
            self.code_representations[module_name] = code_repr
            
            # If this is the first module analyzed, create self_explanation
            if self.self_explanation is None:
                self.self_explanation = SelfExplanation(code_repr, self.runtime_analysis)
            
        return code_repr
    
    def analyze_directory(self, directory_path: str) -> CodeRepresentation:
        """
        Analyze all Python files in a directory.
        
        Args:
            directory_path: Path to the directory.
            
        Returns:
            CodeRepresentation: The code representation for the directory.
        """
        # Create a code representation for this directory
        code_repr = CodeRepresentation()
        code_repr.add_directory(directory_path)
        
        # Store by directory name
        dir_name = os.path.basename(directory_path)
        self.code_representations[dir_name] = code_repr
        
        # If this is the first module analyzed, create self_explanation
        if self.self_explanation is None:
            self.self_explanation = SelfExplanation(code_repr, self.runtime_analysis)
        
        return code_repr
    
    def trace_function(self, function_name: str, args: Dict[str, Any] = None) -> int:
        """
        Trace a function execution.
        
        Args:
            function_name: The name of the function being executed.
            args: The arguments passed to the function.
            
        Returns:
            int: The trace ID.
        """
        if args is None:
            args = {}
            
        # If we have an active trace, use it as parent
        parent_trace_id = None
        if self.active_trace_stack:
            parent_trace_id = self.active_trace_stack[-1][0]
            
        # Start the trace
        trace_id = self.runtime_analysis.start_trace(function_name, args=args)
        
        # Add to active trace stack
        self.active_trace_stack.append((trace_id, function_name))
        
        # If we have a parent, add this as a sub-trace
        if parent_trace_id is not None:
            self.runtime_analysis.add_sub_trace(parent_trace_id, trace_id)
            
        return trace_id
    
    def end_trace(self, trace_id: int, result: Any = None, error: Optional[str] = None):
        """
        End a function execution trace.
        
        Args:
            trace_id: The trace ID returned by trace_function.
            result: The result of the function call.
            error: Any error that occurred during execution.
        """
        # End the trace
        self.runtime_analysis.end_trace(trace_id, result, error)
        
        # Remove from active trace stack
        if self.active_trace_stack and self.active_trace_stack[-1][0] == trace_id:
            self.active_trace_stack.pop()
    
    def build_performance_model(self, module_name: str) -> PerformanceModel:
        """
        Build a performance model for a module.
        
        Args:
            module_name: The name of the module.
            
        Returns:
            PerformanceModel: The performance model for the module.
        """
        if module_name in self.performance_models:
            # If model already exists, update it
            self.performance_models[module_name].build_models()
            return self.performance_models[module_name]
            
        model = PerformanceModel(self.runtime_analysis)
        model.build_models()
        self.performance_models[module_name] = model
        
        return model
    
    def predict_modification_impact(self, 
                                   module_name: str, 
                                   function_name: str, 
                                   before_code: str, 
                                   after_code: str) -> Dict[str, Any]:
        """
        Predict the impact of modifying a function.
        
        Args:
            module_name: The name of the module containing the function.
            function_name: The name of the function to modify.
            before_code: The function code before modification.
            after_code: The function code after modification.
            
        Returns:
            Dict[str, Any]: The predicted impact of the modification.
        """
        # Ensure we have a performance model for the module
        if module_name not in self.performance_models:
            self.build_performance_model(module_name)
            
        model = self.performance_models[module_name]
        
        # Predict impact
        impact = model.predict_code_change_impact(function_name, before_code, after_code)
        
        return impact
    
    def get_function_explanation(self, function_name: str, module_name: str = None) -> Dict[str, Any]:
        """
        Get an explanation of a function.
        
        Args:
            function_name: The name of the function.
            module_name: Optional module name to search in.
            
        Returns:
            Dict[str, Any]: Explanation of the function.
        """
        if self.self_explanation is None:
            # Try to find a valid code representation
            if module_name and module_name in self.code_representations:
                code_repr = self.code_representations[module_name]
            elif self.code_representations:
                code_repr = next(iter(self.code_representations.values()))
            else:
                return {
                    'function_name': function_name,
                    'error': 'No code representation available'
                }
                
            self.self_explanation = SelfExplanation(code_repr, self.runtime_analysis)
            
        return self.self_explanation.explain_function(function_name)
    
    def get_class_explanation(self, class_name: str, module_name: str = None) -> Dict[str, Any]:
        """
        Get an explanation of a class.
        
        Args:
            class_name: The name of the class.
            module_name: Optional module name to search in.
            
        Returns:
            Dict[str, Any]: Explanation of the class.
        """
        if self.self_explanation is None:
            # Try to find a valid code representation
            if module_name and module_name in self.code_representations:
                code_repr = self.code_representations[module_name]
            elif self.code_representations:
                code_repr = next(iter(self.code_representations.values()))
            else:
                return {
                    'class_name': class_name,
                    'error': 'No code representation available'
                }
                
            self.self_explanation = SelfExplanation(code_repr, self.runtime_analysis)
            
        return self.self_explanation.explain_class(class_name)
    
    def get_process_explanation(self, trace_id: int) -> Dict[str, Any]:
        """
        Get an explanation of a process execution.
        
        Args:
            trace_id: The ID of the execution trace.
            
        Returns:
            Dict[str, Any]: Explanation of the process.
        """
        if self.self_explanation is None:
            # Try to find a valid code representation
            if self.code_representations:
                code_repr = next(iter(self.code_representations.values()))
                self.self_explanation = SelfExplanation(code_repr, self.runtime_analysis)
            else:
                return {
                    'trace_id': trace_id,
                    'error': 'No code representation available'
                }
            
        return self.self_explanation.explain_process(trace_id)
    
    def get_system_explanation(self) -> Dict[str, Any]:
        """
        Get an explanation of the overall system.
        
        Returns:
            Dict[str, Any]: Explanation of the system.
        """
        if self.self_explanation is None:
            # Try to find a valid code representation
            if self.code_representations:
                code_repr = next(iter(self.code_representations.values()))
                self.self_explanation = SelfExplanation(code_repr, self.runtime_analysis)
            else:
                return {
                    'error': 'No code representation available'
                }
            
        return self.self_explanation.generate_system_explanation()
    
    def find_performance_bottlenecks(self) -> List[Dict[str, Any]]:
        """
        Find performance bottlenecks in the system.
        
        Returns:
            List[Dict[str, Any]]: List of identified bottlenecks.
        """
        bottlenecks = []
        
        # Build performance models for all modules
        for module_name in self.code_representations:
            if module_name not in self.performance_models:
                self.build_performance_model(module_name)
        
        # Find bottlenecks across all modules
        for module_name, model in self.performance_models.items():
            module_bottlenecks = model.analyze_bottlenecks()
            
            for bottleneck in module_bottlenecks:
                bottleneck['module'] = module_name
                bottlenecks.append(bottleneck)
        
        # Sort bottlenecks by time fraction (highest first)
        bottlenecks.sort(key=lambda x: x.get('time_fraction', 0), reverse=True)
        
        return bottlenecks
    
    def recommend_optimizations(self, function_name: str, module_name: str = None) -> List[Dict[str, Any]]:
        """
        Recommend optimizations for a function.
        
        Args:
            function_name: The name of the function.
            module_name: Optional module name to search in.
            
        Returns:
            List[Dict[str, Any]]: List of optimization recommendations.
        """
        recommendations = []
        
        # If module name is provided, look only in that module
        if module_name:
            if module_name in self.performance_models:
                model = self.performance_models[module_name]
                recommendations = model.recommend_optimizations(function_name)
            else:
                # Build a model for this module
                model = self.build_performance_model(module_name)
                recommendations = model.recommend_optimizations(function_name)
        else:
            # Try all modules
            for module_name, model in self.performance_models.items():
                module_recommendations = model.recommend_optimizations(function_name)
                
                if module_recommendations:
                    # Add module name to each recommendation
                    for rec in module_recommendations:
                        rec['module'] = module_name
                        
                    recommendations.extend(module_recommendations)
                    break  # Stop once we find recommendations
        
        return recommendations
    
    def analyze_code_change(self, 
                           module_name: str, 
                           old_code: str, 
                           new_code: str) -> Dict[str, Any]:
        """
        Analyze changes between two versions of code.
        
        Args:
            module_name: The name of the module.
            old_code: The old version of the code.
            new_code: The new version of the code.
            
        Returns:
            Dict[str, Any]: Analysis of the changes.
        """
        analysis = {
            'module_name': module_name,
            'complexity_change': 0,
            'added_functions': [],
            'modified_functions': [],
            'removed_functions': [],
            'added_classes': [],
            'modified_classes': [],
            'removed_classes': [],
            'impact_assessment': {}
        }
        
        try:
            # Parse both versions
            old_tree = ast.parse(old_code)
            new_tree = ast.parse(new_code)
            
            # Extract functions and classes from old code
            old_functions = {}
            old_classes = {}
            for node in old_tree.body:
                if isinstance(node, ast.FunctionDef):
                    old_functions[node.name] = ast.get_source_segment(old_code, node)
                elif isinstance(node, ast.ClassDef):
                    old_classes[node.name] = ast.get_source_segment(old_code, node)
            
            # Extract functions and classes from new code
            new_functions = {}
            new_classes = {}
            for node in new_tree.body:
                if isinstance(node, ast.FunctionDef):
                    new_functions[node.name] = ast.get_source_segment(new_code, node)
                elif isinstance(node, ast.ClassDef):
                    new_classes[node.name] = ast.get_source_segment(new_code, node)
            
            # Find added, modified, and removed functions
            for func_name, func_code in new_functions.items():
                if func_name not in old_functions:
                    analysis['added_functions'].append(func_name)
                elif old_functions[func_name] != func_code:
                    analysis['modified_functions'].append(func_name)
                    
                    # Predict impact of modification
                    if module_name in self.performance_models:
                        impact = self.predict_modification_impact(
                            module_name, 
                            func_name, 
                            old_functions[func_name], 
                            func_code
                        )
                        
                        analysis['impact_assessment'][func_name] = impact
            
            for func_name in old_functions:
                if func_name not in new_functions:
                    analysis['removed_functions'].append(func_name)
            
            # Find added, modified, and removed classes
            for class_name, class_code in new_classes.items():
                if class_name not in old_classes:
                    analysis['added_classes'].append(class_name)
                elif old_classes[class_name] != class_code:
                    analysis['modified_classes'].append(class_name)
            
            for class_name in old_classes:
                if class_name not in new_classes:
                    analysis['removed_classes'].append(class_name)
            
            # Calculate overall complexity change
            old_complexity = self._calculate_code_complexity(old_code)
            new_complexity = self._calculate_code_complexity(new_code)
            complexity_change = new_complexity - old_complexity
            analysis['complexity_change'] = complexity_change
            
            # Determine overall impact
            if complexity_change > 0:
                analysis['overall_assessment'] = {
                    'complexity_impact': 'increased',
                    'magnitude': abs(complexity_change) / max(1, old_complexity),
                    'description': f"Code complexity increased by {(abs(complexity_change) / max(1, old_complexity) * 100):.1f}%"
                }
            elif complexity_change < 0:
                analysis['overall_assessment'] = {
                    'complexity_impact': 'decreased',
                    'magnitude': abs(complexity_change) / max(1, old_complexity),
                    'description': f"Code complexity decreased by {(abs(complexity_change) / max(1, old_complexity) * 100):.1f}%"
                }
            else:
                analysis['overall_assessment'] = {
                    'complexity_impact': 'unchanged',
                    'magnitude': 0,
                    'description': "Code complexity is unchanged"
                }
            
            # Check for potential issues
            issues = []
            
            # Check for large functions
            for func_name, func_code in new_functions.items():
                if func_code.count('\n') > 100:  # Arbitrary threshold
                    issues.append({
                        'type': 'large_function',
                        'element': func_name,
                        'description': f"Function {func_name} is very large ({func_code.count('\n')} lines)"
                    })
            
            # Check for highly complex functions
            for func_name, func_code in new_functions.items():
                complexity = self._calculate_code_complexity(func_code)
                if complexity > 20:  # Arbitrary threshold
                    issues.append({
                        'type': 'high_complexity',
                        'element': func_name,
                        'description': f"Function {func_name} has high complexity ({complexity:.1f})"
                    })
            
            analysis['potential_issues'] = issues
            
        except SyntaxError as e:
            analysis['error'] = f"Syntax error in code: {str(e)}"
        except Exception as e:
            analysis['error'] = f"Error analyzing code change: {str(e)}"
            
        return analysis
    
    def _calculate_code_complexity(self, code: str) -> float:
        """Calculate the complexity of a code snippet."""
        try:
            tree = ast.parse(code)
            
            # Count various complexity factors
            statement_count = 0
            branch_count = 0
            loop_count = 0
            
            for node in ast.walk(tree):
                # Count statements
                if isinstance(node, (ast.Assign, ast.AugAssign, ast.Return, ast.Expr)):
                    statement_count += 1
                
                # Count branches
                if isinstance(node, (ast.If, ast.IfExp)):
                    branch_count += 1
                elif isinstance(node, ast.BoolOp):
                    # Count boolean operations (and, or)
                    if isinstance(node.op, (ast.And, ast.Or)):
                        branch_count += len(node.values) - 1
                
                # Count loops
                if isinstance(node, (ast.For, ast.While, ast.comprehension)):
                    loop_count += 1
            
            # Apply weights to different factors
            complexity = (
                0.1 * statement_count +
                1.0 * branch_count +
                2.0 * loop_count
            )
            
            return complexity
            
        except SyntaxError:
            return 0.0
        except Exception:
            return 0.0
    
    def clear_trace_data(self):
        """Clear all trace data."""
        self.runtime_analysis.clear_traces()
        self.runtime_analysis.clear_performance_records()
        self.runtime_analysis.clear_error_logs()
        self.active_trace_stack = []
    
    def start_resource_monitoring(self, interval: float = 1.0):
        """
        Start monitoring system resources.
        
        Args:
            interval: Monitoring interval in seconds.
        """
        self.runtime_analysis.start_resource_monitoring(interval)
    
    def stop_resource_monitoring(self):
        """Stop monitoring system resources."""
        self.runtime_analysis.stop_resource_monitoring()


# For backward compatibility with any code that might expect these to be in the main module
__all__ = [
    'CodeEntry', 'MethodEntry', 'ClassEntry', 'FunctionEntry', 'ModuleEntry',
    'CodeRepository', 'CodeRepresentation', 'ExecutionContext', 'ExecutionTraceManager',
    'PerformanceRecord', 'RuntimeAnalysis', 'PerformanceFeature', 'FeatureExtractor',
    'LinearPerformanceModel', 'PerformanceModel', 'SelfExplanation', 'ComputationalReflection'
]