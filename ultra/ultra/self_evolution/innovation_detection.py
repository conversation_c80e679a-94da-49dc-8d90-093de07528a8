#!/usr/bin/env python3
"""
ULTRA: Ultimate Learning & Thought Reasoning Architecture
Innovation Detection Module

This module implements mechanisms for detecting, evaluating, and cataloging emergent
innovations that occur during the system's self-evolution. It identifies novel, creative,
or unexpected patterns, behaviors, and solutions that may be valuable for improving
the system's capabilities.

The innovation detection system works by:
1. Monitoring system behavior and outputs for statistical anomalies and novelty
2. Evaluating detected innovations for utility and potential risks
3. Cataloging valuable innovations for potential integration
4. Facilitating the spread of beneficial innovations throughout the system
"""

import os
import sys
import time
import uuid
import json
import copy
import logging
import random
import hashlib
import threading
import numpy as np
from typing import Dict, List, Tuple, Any, Optional, Set, Callable, Union
from dataclasses import dataclass, field
from enum import Enum, auto
from datetime import datetime
from abc import ABC, abstractmethod
from collections import defaultdict, deque

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Add parent directory to sys.path to allow for relative imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import other ULTRA components as needed
try:
    from ultra.utils.config import Configuration
    from ultra.utils.monitoring import PerformanceMonitor
    from ultra.knowledge_management.episodic_knowledge import EpisodicKnowledgeBase
    from ultra.meta_cognitive.meta_learning import MetaLearningController
except ImportError:
    logger.warning("Some ULTRA components could not be imported. Innovation Detection may have limited functionality.")

# ------------------------------------------------------------------------------
# Innovation Types and Classification
# ------------------------------------------------------------------------------

class InnovationType(Enum):
    """Types of innovations that can be detected."""
    ALGORITHMIC = auto()       # New algorithm or algorithmic improvement
    ARCHITECTURAL = auto()     # New architectural pattern or structure
    BEHAVIORAL = auto()        # New behavior or interaction pattern
    REPRESENTATIONAL = auto()  # New way of representing information
    CONCEPTUAL = auto()        # New concept or abstraction
    EFFICIENCY = auto()        # Improvement in resource usage or performance
    INTEGRATION = auto()       # New way of integrating components
    PROCEDURAL = auto()        # New procedure or workflow
    EMERGENT = auto()          # Unexpected emergent property


class InnovationOrigin(Enum):
    """Origin of an innovation."""
    PLANNED = auto()           # Result of deliberate design or optimization
    ACCIDENTAL = auto()        # Unintended consequence of other changes
    EVOLUTIONARY = auto()      # Result of evolutionary processes
    RECOMBINATION = auto()     # Combination of existing elements
    EXTERNAL = auto()          # Imported from external source
    UNKNOWN = auto()           # Origin cannot be determined


class InnovationDomain(Enum):
    """Domain or area where an innovation occurred."""
    REASONING = auto()         # Logical reasoning and problem solving
    PERCEPTION = auto()        # Processing and understanding of inputs
    LEARNING = auto()          # Learning mechanisms and knowledge acquisition
    MEMORY = auto()            # Storage and retrieval of information
    ATTENTION = auto()         # Focus and allocation of resources
    PLANNING = auto()          # Planning and decision making
    LANGUAGE = auto()          # Natural language processing and generation
    VISUALIZATION = auto()     # Visual processing and representation
    INTEGRATION = auto()       # Integration of multiple capabilities
    SYSTEM = auto()            # System-wide innovation


@dataclass
class Innovation:
    """Represents a detected innovation."""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    name: str = ""
    description: str = ""
    type: InnovationType = InnovationType.EMERGENT
    origin: InnovationOrigin = InnovationOrigin.UNKNOWN
    domain: InnovationDomain = InnovationDomain.SYSTEM
    detection_time: float = field(default_factory=time.time)
    source_component: str = ""
    novelty_score: float = 0.0
    utility_score: float = 0.0
    risk_score: float = 0.0
    overall_value: float = 0.0
    reproducible: bool = False
    verified: bool = False
    integrated: bool = False
    data: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'type': self.type.name,
            'origin': self.origin.name,
            'domain': self.domain.name,
            'detection_time': self.detection_time,
            'source_component': self.source_component,
            'novelty_score': self.novelty_score,
            'utility_score': self.utility_score,
            'risk_score': self.risk_score,
            'overall_value': self.overall_value,
            'reproducible': self.reproducible,
            'verified': self.verified,
            'integrated': self.integrated,
            'data': self.data
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Innovation':
        """Create from dictionary after deserialization."""
        # Convert string enum names back to enum values
        innovation_type = InnovationType[data['type']] if 'type' in data else InnovationType.EMERGENT
        innovation_origin = InnovationOrigin[data['origin']] if 'origin' in data else InnovationOrigin.UNKNOWN
        innovation_domain = InnovationDomain[data['domain']] if 'domain' in data else InnovationDomain.SYSTEM
        
        return cls(
            id=data.get('id', str(uuid.uuid4())),
            name=data.get('name', ''),
            description=data.get('description', ''),
            type=innovation_type,
            origin=innovation_origin,
            domain=innovation_domain,
            detection_time=data.get('detection_time', time.time()),
            source_component=data.get('source_component', ''),
            novelty_score=data.get('novelty_score', 0.0),
            utility_score=data.get('utility_score', 0.0),
            risk_score=data.get('risk_score', 0.0),
            overall_value=data.get('overall_value', 0.0),
            reproducible=data.get('reproducible', False),
            verified=data.get('verified', False),
            integrated=data.get('integrated', False),
            data=data.get('data', {})
        )

    def calculate_overall_value(self, 
                              novelty_weight: float = 0.3, 
                              utility_weight: float = 0.5, 
                              risk_weight: float = 0.2) -> float:
        """
        Calculate the overall value of the innovation.
        
        Args:
            novelty_weight: Weight for novelty score (0.0 to 1.0)
            utility_weight: Weight for utility score (0.0 to 1.0)
            risk_weight: Weight for risk score (0.0 to 1.0)
            
        Returns:
            float: Overall value score (0.0 to 1.0)
        """
        # Normalize weights
        total_weight = novelty_weight + utility_weight + risk_weight
        novelty_weight /= total_weight
        utility_weight /= total_weight
        risk_weight /= total_weight
        
        # Calculate weighted score (risk is subtracted)
        value = (novelty_weight * self.novelty_score +
                utility_weight * self.utility_score -
                risk_weight * self.risk_score)
        
        # Ensure value is in [0.0, 1.0] range
        value = max(0.0, min(1.0, value))
        
        self.overall_value = value
        return value

    def get_summary(self) -> str:
        """Get a human-readable summary of the innovation."""
        timestamp = datetime.fromtimestamp(self.detection_time).strftime('%Y-%m-%d %H:%M:%S')
        
        summary = f"Innovation: {self.name}\n"
        summary += f"ID: {self.id}\n"
        summary += f"Detected: {timestamp}\n"
        summary += f"Type: {self.type.name}\n"
        summary += f"Domain: {self.domain.name}\n"
        summary += f"Source: {self.source_component}\n"
        summary += f"Value: {self.overall_value:.2f}\n"
        summary += f"Description: {self.description}\n"
        
        return summary


class InnovationRegistry:
    """Registry for storing and retrieving detected innovations."""
    
    def __init__(self, storage_path: Optional[str] = None):
        self.innovations: Dict[str, Innovation] = {}
        self.innovation_history: List[Dict[str, Any]] = []
        self.storage_path = storage_path
        
        # Create storage directory if specified
        if storage_path:
            os.makedirs(storage_path, exist_ok=True)
            
        # Try to load existing innovations if storage path exists
        if storage_path and os.path.exists(storage_path):
            self.load_innovations()
    
    def register_innovation(self, innovation: Innovation) -> str:
        """
        Register a new innovation.
        
        Args:
            innovation: The innovation to register.
            
        Returns:
            str: The ID of the registered innovation.
        """
        if innovation.id in self.innovations:
            logger.warning(f"Innovation with ID {innovation.id} already exists. Updating...")
            
        self.innovations[innovation.id] = innovation
        
        # Add to history
        self.innovation_history.append({
            'action': 'register',
            'innovation_id': innovation.id,
            'timestamp': time.time(),
            'details': {
                'name': innovation.name,
                'type': innovation.type.name,
                'novelty_score': innovation.novelty_score,
                'utility_score': innovation.utility_score,
                'overall_value': innovation.overall_value
            }
        })
        
        # Save to storage if configured
        if self.storage_path:
            self.save_innovation(innovation)
            
        logger.info(f"Registered innovation: {innovation.name} (ID: {innovation.id})")
        return innovation.id
    
    def update_innovation(self, innovation: Innovation) -> bool:
        """
        Update an existing innovation.
        
        Args:
            innovation: The innovation to update.
            
        Returns:
            bool: True if the update was successful, False otherwise.
        """
        if innovation.id not in self.innovations:
            logger.warning(f"Innovation with ID {innovation.id} not found. Cannot update.")
            return False
            
        self.innovations[innovation.id] = innovation
        
        # Add to history
        self.innovation_history.append({
            'action': 'update',
            'innovation_id': innovation.id,
            'timestamp': time.time(),
            'details': {
                'name': innovation.name,
                'novelty_score': innovation.novelty_score,
                'utility_score': innovation.utility_score,
                'overall_value': innovation.overall_value,
                'verified': innovation.verified,
                'integrated': innovation.integrated
            }
        })
        
        # Save to storage if configured
        if self.storage_path:
            self.save_innovation(innovation)
            
        logger.info(f"Updated innovation: {innovation.name} (ID: {innovation.id})")
        return True
    
    def get_innovation(self, innovation_id: str) -> Optional[Innovation]:
        """Get an innovation by ID."""
        return self.innovations.get(innovation_id)
    
    def get_innovations(self, 
                       type_filter: Optional[InnovationType] = None,
                       domain_filter: Optional[InnovationDomain] = None,
                       min_value: float = 0.0,
                       verified_only: bool = False,
                       integrated_only: bool = False) -> List[Innovation]:
        """
        Get innovations with optional filtering.
        
        Args:
            type_filter: Filter by innovation type.
            domain_filter: Filter by innovation domain.
            min_value: Minimum overall value score.
            verified_only: If True, only return verified innovations.
            integrated_only: If True, only return integrated innovations.
            
        Returns:
            List[Innovation]: Filtered list of innovations.
        """
        filtered_innovations = []
        
        for innovation in self.innovations.values():
            # Apply filters
            if type_filter and innovation.type != type_filter:
                continue
                
            if domain_filter and innovation.domain != domain_filter:
                continue
                
            if innovation.overall_value < min_value:
                continue
                
            if verified_only and not innovation.verified:
                continue
                
            if integrated_only and not innovation.integrated:
                continue
                
            filtered_innovations.append(innovation)
            
        return filtered_innovations
    
    def get_top_innovations(self, count: int = 10) -> List[Innovation]:
        """
        Get the top innovations by overall value.
        
        Args:
            count: Maximum number of innovations to return.
            
        Returns:
            List[Innovation]: Top innovations.
        """
        sorted_innovations = sorted(
            self.innovations.values(),
            key=lambda x: x.overall_value,
            reverse=True
        )
        
        return sorted_innovations[:count]
    
    def mark_as_verified(self, innovation_id: str, verified: bool = True) -> bool:
        """
        Mark an innovation as verified or unverified.
        
        Args:
            innovation_id: ID of the innovation.
            verified: Verification status.
            
        Returns:
            bool: True if successful, False if innovation not found.
        """
        if innovation_id not in self.innovations:
            return False
            
        self.innovations[innovation_id].verified = verified
        
        # Add to history
        self.innovation_history.append({
            'action': 'verify' if verified else 'unverify',
            'innovation_id': innovation_id,
            'timestamp': time.time()
        })
        
        # Save to storage if configured
        if self.storage_path:
            self.save_innovation(self.innovations[innovation_id])
            
        return True
    
    def mark_as_integrated(self, innovation_id: str, integrated: bool = True) -> bool:
        """
        Mark an innovation as integrated or not integrated.
        
        Args:
            innovation_id: ID of the innovation.
            integrated: Integration status.
            
        Returns:
            bool: True if successful, False if innovation not found.
        """
        if innovation_id not in self.innovations:
            return False
            
        self.innovations[innovation_id].integrated = integrated
        
        # Add to history
        self.innovation_history.append({
            'action': 'integrate' if integrated else 'deintegrate',
            'innovation_id': innovation_id,
            'timestamp': time.time()
        })
        
        # Save to storage if configured
        if self.storage_path:
            self.save_innovation(self.innovations[innovation_id])
            
        return True
    
    def get_history(self) -> List[Dict[str, Any]]:
        """Get the innovation history."""
        return self.innovation_history.copy()
    
    def save_innovation(self, innovation: Innovation):
        """Save an innovation to persistent storage."""
        if not self.storage_path:
            return
            
        # Create filename
        filename = os.path.join(self.storage_path, f"{innovation.id}.json")
        
        # Convert to dictionary
        data = innovation.to_dict()
        
        # Save to file
        with open(filename, 'w') as f:
            json.dump(data, f, indent=2)
    
    def load_innovations(self):
        """Load innovations from persistent storage."""
        if not self.storage_path or not os.path.exists(self.storage_path):
            return
            
        # Find all JSON files in the storage directory
        for filename in os.listdir(self.storage_path):
            if not filename.endswith('.json'):
                continue
                
            filepath = os.path.join(self.storage_path, filename)
            
            try:
                # Load the file
                with open(filepath, 'r') as f:
                    data = json.load(f)
                    
                # Create Innovation object
                innovation = Innovation.from_dict(data)
                
                # Add to registry
                self.innovations[innovation.id] = innovation
                
            except Exception as e:
                logger.error(f"Failed to load innovation from {filepath}: {str(e)}")
    
    def save_all(self):
        """Save all innovations to persistent storage."""
        if not self.storage_path:
            return
            
        for innovation in self.innovations.values():
            self.save_innovation(innovation)
            
        # Save history
        history_path = os.path.join(self.storage_path, "history.json")
        with open(history_path, 'w') as f:
            json.dump(self.innovation_history, f, indent=2)
            
        logger.info(f"Saved {len(self.innovations)} innovations to {self.storage_path}")
    
    def load_history(self):
        """Load innovation history from persistent storage."""
        if not self.storage_path:
            return
            
        history_path = os.path.join(self.storage_path, "history.json")
        
        if not os.path.exists(history_path):
            return
            
        try:
            with open(history_path, 'r') as f:
                self.innovation_history = json.load(f)
        except Exception as e:
            logger.error(f"Failed to load innovation history: {str(e)}")
    
    def clear(self):
        """Clear all innovations and history."""
        self.innovations.clear()
        self.innovation_history.clear()
        
        logger.info("Cleared innovation registry")


# ------------------------------------------------------------------------------
# Novelty Detection
# ------------------------------------------------------------------------------

class NoveltyMetric(ABC):
    """Base class for novelty metrics."""
    
    @abstractmethod
    def compute(self, observation: Any, reference: Any) -> float:
        """
        Compute the novelty of an observation relative to a reference.
        
        Args:
            observation: The observation to evaluate.
            reference: The reference to compare against.
            
        Returns:
            float: Novelty score in range [0.0, 1.0].
        """
        pass
    
    @abstractmethod
    def get_name(self) -> str:
        """Get the name of the novelty metric."""
        pass
    
    @abstractmethod
    def get_description(self) -> str:
        """Get a description of the novelty metric."""
        pass


class StatisticalDistanceNovelty(NoveltyMetric):
    """Novelty metric based on statistical distance from a reference distribution."""
    
    def __init__(self, 
                window_size: int = 100, 
                sensitivity: float = 2.0):
        """
        Initialize the metric.
        
        Args:
            window_size: Size of the sliding window for reference distribution.
            sensitivity: Sensitivity parameter (higher = more sensitivity to deviations).
        """
        self.window_size = window_size
        self.sensitivity = sensitivity
        self.reference_window = deque(maxlen=window_size)
        
    def compute(self, observation: Any, reference: Optional[Any] = None) -> float:
        """
        Compute the novelty of an observation based on its statistical distance.
        
        Args:
            observation: The observation to evaluate (numeric or vector).
            reference: Optional explicit reference (if None, uses internal window).
            
        Returns:
            float: Novelty score in range [0.0, 1.0].
        """
        # If no explicit reference, use the window
        if reference is None:
            if not self.reference_window:
                # If window is empty, assume this is the first observation
                self.reference_window.append(observation)
                return 0.5  # Medium novelty for the first observation
                
            reference = self.reference_window
        
        # Handle different data types
        if isinstance(observation, (int, float)) and isinstance(reference, (list, deque)):
            return self._compute_scalar_novelty(observation, reference)
        elif isinstance(observation, np.ndarray) and (isinstance(reference, (list, deque)) or 
                                                  isinstance(reference, np.ndarray)):
            return self._compute_vector_novelty(observation, reference)
        elif isinstance(observation, dict) and isinstance(reference, (list, deque)):
            return self._compute_dict_novelty(observation, reference)
        else:
            # Default to mean of individual novelty scores for complex objects
            if isinstance(observation, dict) and isinstance(reference, dict):
                # Handle dict-dict comparison
                common_keys = set(observation.keys()) & set(reference.keys())
                if not common_keys:
                    return 1.0  # Completely different dicts
                    
                novelty_scores = []
                for key in common_keys:
                    if isinstance(observation[key], (int, float)) and isinstance(reference[key], (int, float)):
                        # For numeric values, compute relative difference
                        diff = abs(observation[key] - reference[key])
                        scale = max(1.0, abs(reference[key]))
                        novelty_scores.append(min(1.0, diff / scale))
                    else:
                        # For non-numeric, use binary comparison
                        novelty_scores.append(0.0 if observation[key] == reference[key] else 1.0)
                        
                return sum(novelty_scores) / max(1, len(novelty_scores))
            else:
                # Fallback for other types: binary novelty (1.0 if different)
                return 0.0 if observation == reference else 1.0
    
    def _compute_scalar_novelty(self, value: float, reference: List[float]) -> float:
        """Compute novelty for scalar values."""
        # Convert reference to numpy array for statistical operations
        ref_array = np.array(reference)
        
        # Compute mean and standard deviation
        ref_mean = np.mean(ref_array)
        ref_std = np.std(ref_array) + 1e-10  # Avoid division by zero
        
        # Compute z-score
        z_score = abs(value - ref_mean) / ref_std
        
        # Convert to novelty score in [0, 1] range
        novelty = min(1.0, z_score / self.sensitivity)
        
        # Update reference window
        self.reference_window.append(value)
        
        return novelty
    
    def _compute_vector_novelty(self, vector: np.ndarray, reference: Union[List[np.ndarray], np.ndarray]) -> float:
        """Compute novelty for vector values."""
        if isinstance(reference, np.ndarray) and reference.ndim == 2:
            # Reference is a single 2D array (e.g., a covariance matrix)
            # Compute Mahalanobis distance
            diff = vector - np.mean(reference, axis=0)
            cov = np.cov(reference, rowvar=False) + np.eye(reference.shape[1]) * 1e-6  # Add regularization
            inv_cov = np.linalg.inv(cov)
            
            mahalanobis = np.sqrt(diff.dot(inv_cov).dot(diff))
            novelty = min(1.0, mahalanobis / self.sensitivity)
            
        elif isinstance(reference, list) or isinstance(reference, deque):
            # Reference is a list of vectors, compute average distance
            if not reference:
                return 0.5
                
            distances = []
            for ref_vector in reference:
                if isinstance(ref_vector, np.ndarray) and ref_vector.shape == vector.shape:
                    # Euclidean distance
                    dist = np.linalg.norm(vector - ref_vector)
                    distances.append(dist)
                    
            if not distances:
                return 0.5
                
            avg_distance = np.mean(distances)
            max_distance = np.max(distances)
            
            # Normalize by maximum observed distance
            novelty = min(1.0, avg_distance / (max_distance + 1e-10))
            
        else:
            # Fallback
            novelty = 0.5
            
        # Update reference window
        self.reference_window.append(vector)
        
        return novelty
    
    def _compute_dict_novelty(self, dict_value: Dict[str, Any], reference: List[Dict[str, Any]]) -> float:
        """Compute novelty for dictionary values."""
        if not reference:
            return 0.5
            
        # Compute similarities to each reference dict
        similarities = []
        
        for ref_dict in reference:
            # Find common keys
            common_keys = set(dict_value.keys()) & set(ref_dict.keys())
            total_keys = set(dict_value.keys()) | set(ref_dict.keys())
            
            if not total_keys:
                similarities.append(1.0)  # Both dictionaries are empty
                continue
                
            # Compute Jaccard similarity of keys
            key_similarity = len(common_keys) / len(total_keys)
            
            # Compute value similarity for common keys
            value_similarities = []
            for key in common_keys:
                if isinstance(dict_value[key], (int, float)) and isinstance(ref_dict[key], (int, float)):
                    # For numbers, use relative difference
                    diff = abs(dict_value[key] - ref_dict[key])
                    scale = max(1.0, abs(ref_dict[key]))
                    value_similarities.append(1.0 - min(1.0, diff / scale))
                else:
                    # For non-numeric, use binary comparison
                    value_similarities.append(1.0 if dict_value[key] == ref_dict[key] else 0.0)
                    
            # Average value similarity
            avg_value_similarity = sum(value_similarities) / max(1, len(value_similarities))
            
            # Overall similarity is a combination of key and value similarity
            similarity = 0.5 * key_similarity + 0.5 * avg_value_similarity
            similarities.append(similarity)
            
        # Overall novelty is 1 - maximum similarity
        novelty = 1.0 - max(similarities)
        
        # Update reference window
        self.reference_window.append(dict_value)
        
        return novelty
    
    def reset(self):
        """Reset the internal state."""
        self.reference_window.clear()
    
    def get_name(self) -> str:
        """Get the name of the novelty metric."""
        return "Statistical Distance Novelty"
    
    def get_description(self) -> str:
        """Get a description of the novelty metric."""
        return (f"Measures novelty based on statistical distance from a reference distribution "
                f"with window size {self.window_size} and sensitivity {self.sensitivity}.")


class RarityNovelty(NoveltyMetric):
    """Novelty metric based on frequency of occurrence in a reference set."""
    
    def __init__(self, 
                capacity: int = 1000, 
                decay_rate: float = 0.99):
        """
        Initialize the metric.
        
        Args:
            capacity: Maximum number of unique items to track.
            decay_rate: Rate at which frequencies decay over time.
        """
        self.capacity = capacity
        self.decay_rate = decay_rate
        self.frequency_map = {}
        self.total_observations = 0
        self.update_counter = 0
        
    def compute(self, observation: Any, reference: Optional[Any] = None) -> float:
        """
        Compute the novelty of an observation based on its rarity.
        
        Args:
            observation: The observation to evaluate.
            reference: Optional reference (ignored in this metric).
            
        Returns:
            float: Novelty score in range [0.0, 1.0].
        """
        # Convert observation to hashable representation
        hash_key = self._get_hash_key(observation)
        
        # Periodically decay all frequencies
        self.update_counter += 1
        if self.update_counter >= 100:
            self._decay_frequencies()
            self.update_counter = 0
            
        # Get current frequency
        current_freq = self.frequency_map.get(hash_key, 0)
        
        # Compute novelty based on frequency
        if self.total_observations == 0:
            novelty = 1.0  # First observation is novel
        else:
            # Novelty decreases with frequency
            novelty = np.exp(-5.0 * current_freq / max(1, self.total_observations))
            
        # Update frequency
        self.frequency_map[hash_key] = current_freq + 1
        self.total_observations += 1
        
        # Limit capacity
        if len(self.frequency_map) > self.capacity:
            self._prune_frequency_map()
            
        return novelty
    
    def _get_hash_key(self, observation: Any) -> str:
        """Convert an observation to a hashable key."""
        if isinstance(observation, (int, float, str, bool, type(None))):
            return str(observation)
        elif isinstance(observation, (list, tuple)):
            # For lists and tuples, hash each element
            return str([self._get_hash_key(x) for x in observation])
        elif isinstance(observation, dict):
            # For dictionaries, sort keys and hash each key-value pair
            return str(sorted((k, self._get_hash_key(v)) for k, v in observation.items()))
        elif isinstance(observation, np.ndarray):
            # For numpy arrays, hash the array contents
            return hashlib.md5(observation.tobytes()).hexdigest()
        else:
            # For other types, use object's string representation
            return str(observation)
    
    def _decay_frequencies(self):
        """Apply decay to all frequencies."""
        for key in self.frequency_map:
            self.frequency_map[key] *= self.decay_rate
            
    def _prune_frequency_map(self):
        """Remove least frequent items to maintain capacity."""
        items = sorted(self.frequency_map.items(), key=lambda x: x[1])
        
        # Keep only the most frequent items
        to_keep = items[-(self.capacity // 2):]
        
        # Reset frequency map
        self.frequency_map = {k: v for k, v in to_keep}
        
    def reset(self):
        """Reset the internal state."""
        self.frequency_map.clear()
        self.total_observations = 0
        self.update_counter = 0
    
    def get_name(self) -> str:
        """Get the name of the novelty metric."""
        return "Rarity Novelty"
    
    def get_description(self) -> str:
        """Get a description of the novelty metric."""
        return (f"Measures novelty based on frequency of occurrence with capacity "
                f"{self.capacity} and decay rate {self.decay_rate}.")


class SurpriseNovelty(NoveltyMetric):
    """Novelty metric based on prediction error or surprise."""
    
    def __init__(self, 
                prediction_model: Optional[Any] = None, 
                window_size: int = 10,
                sensitivity: float = 1.0):
        """
        Initialize the metric.
        
        Args:
            prediction_model: Optional model for prediction.
            window_size: Size of the window for simple prediction.
            sensitivity: Sensitivity parameter.
        """
        self.prediction_model = prediction_model
        self.window_size = window_size
        self.sensitivity = sensitivity
        self.history = deque(maxlen=window_size)
        
    def compute(self, observation: Any, reference: Optional[Any] = None) -> float:
        """
        Compute the novelty of an observation based on prediction error.
        
        Args:
            observation: The observation to evaluate.
            reference: Optional reference (ignored in this metric).
            
        Returns:
            float: Novelty score in range [0.0, 1.0].
        """
        # If we don't have enough history, assume medium novelty
        if len(self.history) < 2:
            self.history.append(observation)
            return 0.5
            
        # Make prediction
        if self.prediction_model is not None:
            try:
                # Use custom prediction model if provided
                prediction = self.prediction_model.predict(list(self.history))
            except Exception as e:
                logger.warning(f"Error using custom prediction model: {str(e)}")
                prediction = self._simple_prediction()
        else:
            # Use simple prediction
            prediction = self._simple_prediction()
            
        # Compute prediction error
        if isinstance(observation, (int, float)) and isinstance(prediction, (int, float)):
            # For scalars, use relative error
            error = abs(observation - prediction)
            scale = max(1.0, abs(prediction))
            surprise = min(1.0, (error / scale) * self.sensitivity)
        elif isinstance(observation, np.ndarray) and isinstance(prediction, np.ndarray):
            # For vectors, use normalized distance
            error = np.linalg.norm(observation - prediction)
            scale = max(1.0, np.linalg.norm(prediction))
            surprise = min(1.0, (error / scale) * self.sensitivity)
        else:
            # For other types, use binary comparison
            surprise = 0.0 if observation == prediction else 1.0
            
        # Update history
        self.history.append(observation)
        
        return surprise
    
    def _simple_prediction(self) -> Any:
        """Make a simple prediction based on history."""
        if not self.history:
            return None
            
        if isinstance(self.history[-1], (int, float)):
            # For numeric values, use linear extrapolation
            if len(self.history) >= 2:
                last_value = self.history[-1]
                previous_value = self.history[-2]
                return last_value + (last_value - previous_value)
            else:
                return self.history[-1]
        elif isinstance(self.history[-1], np.ndarray):
            # For vectors, use linear extrapolation
            if len(self.history) >= 2:
                last_value = self.history[-1]
                previous_value = self.history[-2]
                return last_value + (last_value - previous_value)
            else:
                return self.history[-1].copy()
        else:
            # For other types, predict the last observed value
            return self.history[-1]
    
    def reset(self):
        """Reset the internal state."""
        self.history.clear()
    
    def get_name(self) -> str:
        """Get the name of the novelty metric."""
        return "Surprise Novelty"
    
    def get_description(self) -> str:
        """Get a description of the novelty metric."""
        return (f"Measures novelty based on prediction error or surprise with "
                f"window size {self.window_size} and sensitivity {self.sensitivity}.")


class AnomalyNovelty(NoveltyMetric):
    """Novelty metric based on anomaly detection methods."""
    
    def __init__(self, 
                window_size: int = 1000, 
                n_neighbors: int = 5,
                contamination: float = 0.05):
        """
        Initialize the metric.
        
        Args:
            window_size: Size of the reference window.
            n_neighbors: Number of neighbors for LOF calculation.
            contamination: Expected proportion of anomalies.
        """
        self.window_size = window_size
        self.n_neighbors = n_neighbors
        self.contamination = contamination
        self.reference_window = deque(maxlen=window_size)
        
        # Minimum number of points needed before anomaly detection
        self.min_points = max(20, n_neighbors * 2)
        
        # Try to import scikit-learn for LOF
        try:
            from sklearn.neighbors import LocalOutlierFactor
            self.LocalOutlierFactor = LocalOutlierFactor
            self.sklearn_available = True
        except ImportError:
            logger.warning("scikit-learn not available. Using simplified anomaly detection.")
            self.sklearn_available = False
        
    def compute(self, observation: Any, reference: Optional[Any] = None) -> float:
        """
        Compute the novelty of an observation based on anomaly detection.
        
        Args:
            observation: The observation to evaluate.
            reference: Optional reference (ignored in this metric).
            
        Returns:
            float: Novelty score in range [0.0, 1.0].
        """
        # Handle different data types
        if isinstance(observation, (int, float)):
            # Convert to array for processing
            obs_array = np.array([[observation]])
        elif isinstance(observation, np.ndarray):
            # Ensure 2D array for LOF
            if observation.ndim == 1:
                obs_array = observation.reshape(1, -1)
            else:
                obs_array = observation.reshape(1, -1)
        elif isinstance(observation, dict) and all(isinstance(v, (int, float)) for v in observation.values()):
            # Extract numeric values from dict
            obs_array = np.array([[v for v in observation.values()]])
        else:
            # For other types, we can't do proper anomaly detection
            self.reference_window.append(observation)
            return 0.5
            
        # If we don't have enough reference data, use statistical distance
        if len(self.reference_window) < self.min_points:
            self.reference_window.append(observation)
            
            if len(self.reference_window) <= 2:
                return 0.5
                
            # Use statistical distance for initial points
            stat_novelty = StatisticalDistanceNovelty(window_size=self.window_size)
            for past_obs in list(self.reference_window)[:-1]:  # All but the last one
                stat_novelty.compute(past_obs)
                
            return stat_novelty.compute(observation)
            
        # Convert reference window to array
        if isinstance(self.reference_window[0], (int, float)):
            ref_array = np.array([[x] for x in self.reference_window])
        elif isinstance(self.reference_window[0], np.ndarray):
            ref_array = np.array([x.flatten() for x in self.reference_window])
        elif isinstance(self.reference_window[0], dict) and all(isinstance(v, (int, float)) for v in self.reference_window[0].values()):
            ref_array = np.array([[v for v in d.values()] for d in self.reference_window])
        else:
            # Unexpected data type in reference window
            self.reference_window.append(observation)
            return 0.5
            
        # Compute anomaly score
        if self.sklearn_available:
            # Use LOF for anomaly detection
            try:
                lof = self.LocalOutlierFactor(
                    n_neighbors=min(self.n_neighbors, len(ref_array) - 1),
                    contamination=self.contamination,
                    novelty=True
                )
                lof.fit(ref_array)
                
                # Predict returns -1 for outliers, 1 for inliers, convert to [0, 1]
                # Lower score means more anomalous
                score = lof.decision_function(obs_array)[0]
                
                # Convert to novelty score (higher = more novel)
                novelty = 1.0 / (1.0 + np.exp(score))  # Sigmoid transform
                
            except Exception as e:
                logger.warning(f"Error in LOF anomaly detection: {str(e)}")
                novelty = self._compute_simple_anomaly(obs_array, ref_array)
        else:
            # Use simple anomaly detection
            novelty = self._compute_simple_anomaly(obs_array, ref_array)
            
        # Update reference window
        self.reference_window.append(observation)
        
        return novelty
    
    def _compute_simple_anomaly(self, observation: np.ndarray, reference: np.ndarray) -> float:
        """Compute anomaly score using simple distance-based method."""
        # Compute distances to reference points
        distances = []
        for ref_point in reference:
            dist = np.linalg.norm(observation - ref_point)
            distances.append(dist)
            
        # Sort distances
        distances.sort()
        
        # Use average of k nearest distances
        k = min(self.n_neighbors, len(distances))
        avg_distance = np.mean(distances[:k])
        
        # Normalize by standard deviation of distances
        std_distance = np.std(distances) + 1e-10
        
        # Compute novelty score
        novelty = min(1.0, avg_distance / (3.0 * std_distance))
        
        return novelty
    
    def reset(self):
        """Reset the internal state."""
        self.reference_window.clear()
    
    def get_name(self) -> str:
        """Get the name of the novelty metric."""
        return "Anomaly Novelty"
    
    def get_description(self) -> str:
        """Get a description of the novelty metric."""
        return (f"Measures novelty based on anomaly detection with "
                f"window size {self.window_size} and {self.n_neighbors} neighbors.")


class NoveltyDetector:
    """Component for detecting novel behavior in the system."""
    
    def __init__(self, 
                metrics: Optional[List[NoveltyMetric]] = None,
                threshold: float = 0.7,
                window_size: int = 100):
        """
        Initialize the novelty detector.
        
        Args:
            metrics: List of novelty metrics to use.
            threshold: Threshold for novelty detection.
            window_size: Size of the observation window.
        """
        # Create default metrics if none provided
        if metrics is None:
            metrics = [
                StatisticalDistanceNovelty(window_size=window_size),
                RarityNovelty(capacity=window_size * 10),
                SurpriseNovelty(window_size=window_size // 10),
                AnomalyNovelty(window_size=window_size)
            ]
            
        self.metrics = metrics
        self.threshold = threshold
        self.window_size = window_size
        self.observation_history = deque(maxlen=window_size)
        self.novelty_scores = deque(maxlen=window_size)
        self.detection_history = []
        
    def detect_novelty(self, 
                      observation: Any, 
                      context: Optional[Dict[str, Any]] = None) -> Tuple[bool, float, Dict[str, float]]:
        """
        Detect if an observation is novel.
        
        Args:
            observation: The observation to evaluate.
            context: Optional context information.
            
        Returns:
            Tuple[bool, float, Dict[str, float]]: 
                - Whether the observation is novel
                - Combined novelty score
                - Dictionary of scores for each metric
        """
        # Compute novelty for each metric
        metric_scores = {}
        for metric in self.metrics:
            try:
                score = metric.compute(observation)
                metric_scores[metric.get_name()] = score
            except Exception as e:
                logger.warning(f"Error computing novelty with {metric.get_name()}: {str(e)}")
                metric_scores[metric.get_name()] = 0.0
                
        # Calculate combined score (average across metrics)
        combined_score = sum(metric_scores.values()) / max(1, len(metric_scores))
        
        # Update history
        self.observation_history.append(observation)
        self.novelty_scores.append(combined_score)
        
        # Detect novelty
        is_novel = combined_score >= self.threshold
        
        # Record detection
        if is_novel:
            detection = {
                'timestamp': time.time(),
                'score': combined_score,
                'metric_scores': metric_scores.copy(),
                'observation': observation,
                'context': context
            }
            self.detection_history.append(detection)
            
        return is_novel, combined_score, metric_scores
    
    def get_detection_history(self) -> List[Dict[str, Any]]:
        """Get the history of novelty detections."""
        return self.detection_history.copy()
    
    def get_novelty_statistics(self) -> Dict[str, Any]:
        """Get statistics about novelty scores."""
        if not self.novelty_scores:
            return {
                'mean': 0.0,
                'std': 0.0,
                'min': 0.0,
                'max': 0.0,
                'median': 0.0,
                'detections': 0
            }
            
        scores = list(self.novelty_scores)
        
        return {
            'mean': np.mean(scores),
            'std': np.std(scores),
            'min': min(scores),
            'max': max(scores),
            'median': np.median(scores),
            'detections': len(self.detection_history)
        }
    
    def reset(self):
        """Reset the detector's internal state."""
        self.observation_history.clear()
        self.novelty_scores.clear()
        self.detection_history = []
        
        for metric in self.metrics:
            metric.reset()
            
        logger.info("Reset novelty detector")
    
    def adjust_threshold(self, target_detection_rate: float = 0.05):
        """
        Adjust the novelty threshold to achieve the target detection rate.
        
        Args:
            target_detection_rate: Target rate of novelty detections.
        """
        if not self.novelty_scores:
            return
            
        scores = sorted(self.novelty_scores)
        index = int((1.0 - target_detection_rate) * len(scores))
        new_threshold = scores[index]
        
        logger.info(f"Adjusted novelty threshold from {self.threshold:.3f} to {new_threshold:.3f}")
        self.threshold = new_threshold


# ------------------------------------------------------------------------------
# Innovation Evaluation and Verification
# ------------------------------------------------------------------------------

class UtilityMetric(ABC):
    """Base class for utility metrics."""
    
    @abstractmethod
    def evaluate(self, innovation: Innovation, context: Dict[str, Any]) -> float:
        """
        Evaluate the utility of an innovation.
        
        Args:
            innovation: The innovation to evaluate.
            context: Context information for evaluation.
            
        Returns:
            float: Utility score in range [0.0, 1.0].
        """
        pass
    
    @abstractmethod
    def get_name(self) -> str:
        """Get the name of the utility metric."""
        pass
    
    @abstractmethod
    def get_description(self) -> str:
        """Get a description of the utility metric."""
        pass


class PerformanceUtility(UtilityMetric):
    """Utility metric based on performance improvements."""
    
    def __init__(self, 
                baseline_performance: Optional[Dict[str, float]] = None,
                weights: Optional[Dict[str, float]] = None):
        """
        Initialize the metric.
        
        Args:
            baseline_performance: Baseline performance metrics.
            weights: Weights for different performance aspects.
        """
        self.baseline_performance = baseline_performance or {}
        self.weights = weights or {
            'throughput': 0.3,
            'latency': 0.3,
            'error_rate': 0.2,
            'memory_usage': 0.1,
            'cpu_usage': 0.1
        }
        
        # Normalize weights
        total_weight = sum(self.weights.values())
        self.weights = {k: v / total_weight for k, v in self.weights.items()}
        
    def evaluate(self, innovation: Innovation, context: Dict[str, Any]) -> float:
        """
        Evaluate the utility of an innovation based on performance improvements.
        
        Args:
            innovation: The innovation to evaluate.
            context: Context with performance metrics.
            
        Returns:
            float: Utility score in range [0.0, 1.0].
        """
        if 'performance' not in context:
            return 0.5  # Neutral score if no performance data
            
        current_performance = context['performance']
        
        # If no baseline, use the current performance as baseline
        if not self.baseline_performance:
            self.baseline_performance = current_performance.copy()
            return 0.5  # Neutral score for the baseline
            
        # Calculate improvements for each metric
        improvements = {}
        
        for metric, weight in self.weights.items():
            if metric not in current_performance or metric not in self.baseline_performance:
                continue
                
            current = current_performance[metric]
            baseline = self.baseline_performance[metric]
            
            if metric in ['throughput']:
                # Higher is better
                if baseline > 0:
                    improvements[metric] = (current / baseline) - 1.0
                else:
                    improvements[metric] = 0.0
            elif metric in ['latency', 'error_rate', 'memory_usage', 'cpu_usage']:
                # Lower is better
                if baseline > 0:
                    improvements[metric] = 1.0 - (current / baseline)
                else:
                    improvements[metric] = 0.0
                    
        # Calculate weighted improvement
        weighted_improvement = 0.0
        total_used_weight = 0.0
        
        for metric, improvement in improvements.items():
            weighted_improvement += improvement * self.weights[metric]
            total_used_weight += self.weights[metric]
            
        if total_used_weight == 0:
            return 0.5  # Neutral score if no improvements calculated
            
        # Normalize and scale to [0, 1]
        normalized_improvement = weighted_improvement / total_used_weight
        
        # Convert to utility score (sigmoid function centered at 0)
        utility = 1.0 / (1.0 + np.exp(-10.0 * normalized_improvement))
        
        return utility
    
    def get_name(self) -> str:
        """Get the name of the utility metric."""
        return "Performance Utility"
    
    def get_description(self) -> str:
        """Get a description of the utility metric."""
        return "Measures utility based on performance improvements relative to a baseline."


class FunctionalUtility(UtilityMetric):
    """Utility metric based on new or improved functionality."""
    
    def __init__(self, 
                baseline_capabilities: Optional[Set[str]] = None,
                capability_weights: Optional[Dict[str, float]] = None):
        """
        Initialize the metric.
        
        Args:
            baseline_capabilities: Set of baseline capabilities.
            capability_weights: Weights for different capabilities.
        """
        self.baseline_capabilities = baseline_capabilities or set()
        self.capability_weights = capability_weights or {}
        
    def evaluate(self, innovation: Innovation, context: Dict[str, Any]) -> float:
        """
        Evaluate the utility of an innovation based on functional improvements.
        
        Args:
            innovation: The innovation to evaluate.
            context: Context with capability information.
            
        Returns:
            float: Utility score in range [0.0, 1.0].
        """
        if 'capabilities' not in context:
            return 0.5  # Neutral score if no capability data
            
        current_capabilities = set(context['capabilities'])
        
        # If no baseline, use the current capabilities as baseline
        if not self.baseline_capabilities:
            self.baseline_capabilities = current_capabilities.copy()
            return 0.5  # Neutral score for the baseline
            
        # Calculate new capabilities
        new_capabilities = current_capabilities - self.baseline_capabilities
        
        # If no new capabilities, return low utility
        if not new_capabilities:
            return 0.3  # Below neutral if no new capabilities
            
        # Calculate utility based on new capabilities
        if self.capability_weights:
            # If we have weights, use them
            total_weight = 0.0
            weighted_sum = 0.0
            
            for capability in new_capabilities:
                weight = self.capability_weights.get(capability, 1.0)
                weighted_sum += weight
                total_weight += weight
                
            if total_weight > 0:
                utility = min(1.0, weighted_sum / total_weight)
            else:
                utility = 0.5  # Neutral if no weights apply
        else:
            # Otherwise, base on number of new capabilities
            utility = min(1.0, len(new_capabilities) / 5.0)  # Cap at 5 new capabilities
            
        return utility
    
    def get_name(self) -> str:
        """Get the name of the utility metric."""
        return "Functional Utility"
    
    def get_description(self) -> str:
        """Get a description of the utility metric."""
        return "Measures utility based on new or improved functionality relative to baseline capabilities."


class ApplicationUtility(UtilityMetric):
    """Utility metric based on applicability to diverse tasks and domains."""
    
    def __init__(self, 
                test_tasks: Optional[List[Dict[str, Any]]] = None,
                domain_weights: Optional[Dict[str, float]] = None):
        """
        Initialize the metric.
        
        Args:
            test_tasks: List of test tasks for evaluation.
            domain_weights: Weights for different domains.
        """
        self.test_tasks = test_tasks or []
        self.domain_weights = domain_weights or {}
        self.baseline_results = {}
        
    def evaluate(self, innovation: Innovation, context: Dict[str, Any]) -> float:
        """
        Evaluate the utility of an innovation based on applicability.
        
        Args:
            innovation: The innovation to evaluate.
            context: Context with task results.
            
        Returns:
            float: Utility score in range [0.0, 1.0].
        """
        if 'task_results' not in context:
            return 0.5  # Neutral score if no task results
            
        current_results = context['task_results']
        
        # If no baseline, use the current results as baseline
        if not self.baseline_results:
            self.baseline_results = current_results.copy()
            return 0.5  # Neutral score for the baseline
            
        # Calculate improvements for each task
        improvements = []
        domain_improvements = defaultdict(list)
        
        for task_id, result in current_results.items():
            if task_id not in self.baseline_results:
                continue
                
            baseline = self.baseline_results[task_id]
            
            # Extract performance metrics
            if 'performance' in result and 'performance' in baseline:
                current_perf = result['performance']
                baseline_perf = baseline['performance']
                
                # Calculate improvement based on metric type
                if 'score' in current_perf and 'score' in baseline_perf:
                    # Higher is better
                    improvement = (current_perf['score'] / max(1e-10, baseline_perf['score'])) - 1.0
                elif 'error' in current_perf and 'error' in baseline_perf:
                    # Lower is better
                    improvement = 1.0 - (current_perf['error'] / max(1e-10, baseline_perf['error']))
                elif 'time' in current_perf and 'time' in baseline_perf:
                    # Lower is better
                    improvement = 1.0 - (current_perf['time'] / max(1e-10, baseline_perf['time']))
                else:
                    improvement = 0.0
                
                improvements.append(improvement)
                
                # Track improvement by domain
                if 'domain' in result:
                    domain = result['domain']
                    domain_improvements[domain].append(improvement)
                    
        if not improvements:
            return 0.5  # Neutral score if no improvements calculated
            
        # Calculate overall improvement
        avg_improvement = np.mean(improvements)
        
        # Apply domain weighting if available
        weighted_improvement = 0.0
        total_weight = 0.0
        
        if self.domain_weights and domain_improvements:
            for domain, domain_imps in domain_improvements.items():
                weight = self.domain_weights.get(domain, 1.0)
                weighted_improvement += np.mean(domain_imps) * weight
                total_weight += weight
                
            if total_weight > 0:
                avg_improvement = weighted_improvement / total_weight
                
        # Convert to utility score (sigmoid function centered at 0)
        utility = 1.0 / (1.0 + np.exp(-5.0 * avg_improvement))
        
        return utility
    
    def get_name(self) -> str:
        """Get the name of the utility metric."""
        return "Application Utility"
    
    def get_description(self) -> str:
        """Get a description of the utility metric."""
        return "Measures utility based on applicability to diverse tasks and domains."


class RiskMetric(ABC):
    """Base class for risk metrics."""
    
    @abstractmethod
    def evaluate(self, innovation: Innovation, context: Dict[str, Any]) -> float:
        """
        Evaluate the risk of an innovation.
        
        Args:
            innovation: The innovation to evaluate.
            context: Context information for evaluation.
            
        Returns:
            float: Risk score in range [0.0, 1.0].
        """
        pass
    
    @abstractmethod
    def get_name(self) -> str:
        """Get the name of the risk metric."""
        pass
    
    @abstractmethod
    def get_description(self) -> str:
        """Get a description of the risk metric."""
        pass


class StabilityRisk(RiskMetric):
    """Risk metric based on system stability concerns."""
    
    def __init__(self, 
                error_rate_threshold: float = 0.05,
                crash_penalty: float = 0.8):
        """
        Initialize the metric.
        
        Args:
            error_rate_threshold: Threshold for acceptable error rate.
            crash_penalty: Penalty for system crashes.
        """
        self.error_rate_threshold = error_rate_threshold
        self.crash_penalty = crash_penalty
        self.baseline_stability = None
        
    def evaluate(self, innovation: Innovation, context: Dict[str, Any]) -> float:
        """
        Evaluate the risk of an innovation based on stability impacts.
        
        Args:
            innovation: The innovation to evaluate.
            context: Context with stability information.
            
        Returns:
            float: Risk score in range [0.0, 1.0].
        """
        if 'stability' not in context:
            return 0.5  # Moderate risk if no stability data
            
        stability = context['stability']
        
        # Extract stability metrics
        error_rate = stability.get('error_rate', 0.0)
        crash_count = stability.get('crash_count', 0)
        exception_count = stability.get('exception_count', 0)
        
        # If no baseline, use the current stability as baseline
        if self.baseline_stability is None:
            self.baseline_stability = {
                'error_rate': error_rate,
                'crash_count': crash_count,
                'exception_count': exception_count
            }
            return 0.3  # Moderate-low risk for baseline
            
        # Calculate risk based on change in stability metrics
        baseline_error_rate = self.baseline_stability['error_rate']
        baseline_crash_count = self.baseline_stability['crash_count']
        baseline_exception_count = self.baseline_stability['exception_count']
        
        # Error rate risk (0 to 1)
        if error_rate > self.error_rate_threshold:
            error_rate_risk = min(1.0, error_rate / max(1e-10, self.error_rate_threshold))
        else:
            error_rate_risk = 0.0
            
        # Crash risk (0 to 1)
        if crash_count > baseline_crash_count:
            crash_risk = min(1.0, self.crash_penalty * (crash_count - baseline_crash_count))
        else:
            crash_risk = 0.0
            
        # Exception risk (0 to 1)
        if exception_count > baseline_exception_count:
            exception_risk = min(1.0, 0.5 * (exception_count - baseline_exception_count) / 
                               max(1, baseline_exception_count))
        else:
            exception_risk = 0.0
            
        # Combine risk factors
        combined_risk = max(error_rate_risk, crash_risk, exception_risk)
        
        return combined_risk
    
    def get_name(self) -> str:
        """Get the name of the risk metric."""
        return "Stability Risk"
    
    def get_description(self) -> str:
        """Get a description of the risk metric."""
        return "Measures risk based on potential impacts to system stability."


class ComplexityRisk(RiskMetric):
    """Risk metric based on increased system complexity."""
    
    def __init__(self, 
                complexity_threshold: float = 0.3,
                coupling_threshold: float = 0.5):
        """
        Initialize the metric.
        
        Args:
            complexity_threshold: Threshold for acceptable complexity increase.
            coupling_threshold: Threshold for acceptable coupling increase.
        """
        self.complexity_threshold = complexity_threshold
        self.coupling_threshold = coupling_threshold
        self.baseline_complexity = None
        
    def evaluate(self, innovation: Innovation, context: Dict[str, Any]) -> float:
        """
        Evaluate the risk of an innovation based on complexity impacts.
        
        Args:
            innovation: The innovation to evaluate.
            context: Context with complexity information.
            
        Returns:
            float: Risk score in range [0.0, 1.0].
        """
        if 'complexity' not in context:
            return 0.4  # Moderate risk if no complexity data
            
        complexity = context['complexity']
        
        # Extract complexity metrics
        code_complexity = complexity.get('code_complexity', 0.0)
        architectural_complexity = complexity.get('architectural_complexity', 0.0)
        coupling = complexity.get('coupling', 0.0)
        
        # If no baseline, use the current complexity as baseline
        if self.baseline_complexity is None:
            self.baseline_complexity = {
                'code_complexity': code_complexity,
                'architectural_complexity': architectural_complexity,
                'coupling': coupling
            }
            return 0.2  # Low-moderate risk for baseline
            
        # Calculate risk based on change in complexity metrics
        baseline_code_complexity = self.baseline_complexity['code_complexity']
        baseline_arch_complexity = self.baseline_complexity['architectural_complexity']
        baseline_coupling = self.baseline_complexity['coupling']
        
        # Code complexity risk (0 to 1)
        if code_complexity > baseline_code_complexity:
            relative_increase = (code_complexity - baseline_code_complexity) / max(1e-10, baseline_code_complexity)
            code_complexity_risk = min(1.0, relative_increase / self.complexity_threshold)
        else:
            code_complexity_risk = 0.0
            
        # Architectural complexity risk (0 to 1)
        if architectural_complexity > baseline_arch_complexity:
            relative_increase = (architectural_complexity - baseline_arch_complexity) / max(1e-10, baseline_arch_complexity)
            arch_complexity_risk = min(1.0, relative_increase / self.complexity_threshold)
        else:
            arch_complexity_risk = 0.0
            
        # Coupling risk (0 to 1)
        if coupling > baseline_coupling:
            relative_increase = (coupling - baseline_coupling) / max(1e-10, baseline_coupling)
            coupling_risk = min(1.0, relative_increase / self.coupling_threshold)
        else:
            coupling_risk = 0.0
            
        # Combine risk factors with weights
        combined_risk = 0.3 * code_complexity_risk + 0.4 * arch_complexity_risk + 0.3 * coupling_risk
        
        return combined_risk
    
    def get_name(self) -> str:
        """Get the name of the risk metric."""
        return "Complexity Risk"
    
    def get_description(self) -> str:
        """Get a description of the risk metric."""
        return "Measures risk based on potential increases in system complexity."


class ResourceRisk(RiskMetric):
    """Risk metric based on resource usage and efficiency."""
    
    def __init__(self, 
                memory_threshold: float = 0.8,
                cpu_threshold: float = 0.9,
                storage_threshold: float = 0.7):
        """
        Initialize the metric.
        
        Args:
            memory_threshold: Threshold for memory usage (fraction of capacity).
            cpu_threshold: Threshold for CPU usage (fraction of capacity).
            storage_threshold: Threshold for storage usage (fraction of capacity).
        """
        self.memory_threshold = memory_threshold
        self.cpu_threshold = cpu_threshold
        self.storage_threshold = storage_threshold
        self.baseline_resources = None
        
    def evaluate(self, innovation: Innovation, context: Dict[str, Any]) -> float:
        """
        Evaluate the risk of an innovation based on resource impacts.
        
        Args:
            innovation: The innovation to evaluate.
            context: Context with resource information.
            
        Returns:
            float: Risk score in range [0.0, 1.0].
        """
        if 'resources' not in context:
            return 0.5  # Moderate risk if no resource data
            
        resources = context['resources']
        
        # Extract resource metrics
        memory_usage = resources.get('memory_usage', 0.0)
        cpu_usage = resources.get('cpu_usage', 0.0)
        storage_usage = resources.get('storage_usage', 0.0)
        
        # If no baseline, use the current resources as baseline
        if self.baseline_resources is None:
            self.baseline_resources = {
                'memory_usage': memory_usage,
                'cpu_usage': cpu_usage,
                'storage_usage': storage_usage
            }
            return 0.3  # Moderate-low risk for baseline
            
        # Calculate risk based on resource usage
        
        # Memory risk (0 to 1)
        if memory_usage > self.memory_threshold:
            memory_risk = min(1.0, (memory_usage - self.memory_threshold) / 
                            (1.0 - self.memory_threshold))
        else:
            memory_risk = max(0.0, (memory_usage - self.baseline_resources['memory_usage']) / 
                             max(1e-10, self.memory_threshold))
            
        # CPU risk (0 to 1)
        if cpu_usage > self.cpu_threshold:
            cpu_risk = min(1.0, (cpu_usage - self.cpu_threshold) / 
                         (1.0 - self.cpu_threshold))
        else:
            cpu_risk = max(0.0, (cpu_usage - self.baseline_resources['cpu_usage']) / 
                          max(1e-10, self.cpu_threshold))
            
        # Storage risk (0 to 1)
        if storage_usage > self.storage_threshold:
            storage_risk = min(1.0, (storage_usage - self.storage_threshold) / 
                             (1.0 - self.storage_threshold))
        else:
            storage_risk = max(0.0, (storage_usage - self.baseline_resources['storage_usage']) / 
                              max(1e-10, self.storage_threshold))
            
        # Combine risk factors with weights
        combined_risk = 0.4 * memory_risk + 0.4 * cpu_risk + 0.2 * storage_risk
        
        return combined_risk
    
    def get_name(self) -> str:
        """Get the name of the risk metric."""
        return "Resource Risk"
    
    def get_description(self) -> str:
        """Get a description of the risk metric."""
        return "Measures risk based on potential impacts to system resources."


class InnovationEvaluator:
    """Evaluates detected innovations for utility and risk."""
    
    def __init__(self, 
                utility_metrics: Optional[List[UtilityMetric]] = None,
                risk_metrics: Optional[List[RiskMetric]] = None,
                novelty_weight: float = 0.3,
                utility_weight: float = 0.5,
                risk_weight: float = 0.2):
        """
        Initialize the evaluator.
        
        Args:
            utility_metrics: List of utility metrics to use.
            risk_metrics: List of risk metrics to use.
            novelty_weight: Weight for novelty in overall value calculation.
            utility_weight: Weight for utility in overall value calculation.
            risk_weight: Weight for risk in overall value calculation.
        """
        # Create default metrics if none provided
        if utility_metrics is None:
            utility_metrics = [
                PerformanceUtility(),
                FunctionalUtility(),
                ApplicationUtility()
            ]
            
        if risk_metrics is None:
            risk_metrics = [
                StabilityRisk(),
                ComplexityRisk(),
                ResourceRisk()
            ]
            
        self.utility_metrics = utility_metrics
        self.risk_metrics = risk_metrics
        self.novelty_weight = novelty_weight
        self.utility_weight = utility_weight
        self.risk_weight = risk_weight
        
        # Normalize weights
        total_weight = novelty_weight + utility_weight + risk_weight
        self.novelty_weight /= total_weight
        self.utility_weight /= total_weight
        self.risk_weight /= total_weight
        
    def evaluate_innovation(self, 
                           innovation: Innovation, 
                           context: Dict[str, Any]) -> Innovation:
        """
        Evaluate an innovation for utility and risk.
        
        Args:
            innovation: The innovation to evaluate.
            context: Context information for evaluation.
            
        Returns:
            Innovation: The updated innovation with evaluation scores.
        """
        # Compute utility scores
        utility_scores = {}
        for metric in self.utility_metrics:
            try:
                score = metric.evaluate(innovation, context)
                utility_scores[metric.get_name()] = score
            except Exception as e:
                logger.warning(f"Error evaluating utility with {metric.get_name()}: {str(e)}")
                utility_scores[metric.get_name()] = 0.5  # Neutral score on error
                
        # Calculate combined utility score (average across metrics)
        utility_score = sum(utility_scores.values()) / max(1, len(utility_scores))
        
        # Compute risk scores
        risk_scores = {}
        for metric in self.risk_metrics:
            try:
                score = metric.evaluate(innovation, context)
                risk_scores[metric.get_name()] = score
            except Exception as e:
                logger.warning(f"Error evaluating risk with {metric.get_name()}: {str(e)}")
                risk_scores[metric.get_name()] = 0.5  # Moderate risk on error
                
        # Calculate combined risk score (average across metrics)
        risk_score = sum(risk_scores.values()) / max(1, len(risk_scores))
        
        # Update innovation with scores
        innovation.utility_score = utility_score
        innovation.risk_score = risk_score
        
        # Store detailed scores in data
        if 'evaluation' not in innovation.data:
            innovation.data['evaluation'] = {}
            
        innovation.data['evaluation']['utility_scores'] = utility_scores
        innovation.data['evaluation']['risk_scores'] = risk_scores
        
        # Calculate overall value
        innovation.calculate_overall_value(
            novelty_weight=self.novelty_weight,
            utility_weight=self.utility_weight,
            risk_weight=self.risk_weight
        )
        
        return innovation
    
    def verify_innovation(self, 
                         innovation: Innovation, 
                         validation_context: Dict[str, Any]) -> Tuple[bool, Dict[str, Any]]:
        """
        Verify if an innovation is reproducible and beneficial.
        
        Args:
            innovation: The innovation to verify.
            validation_context: Context for validation.
            
        Returns:
            Tuple[bool, Dict[str, Any]]: Verification result and details.
        """
        result = {
            'verified': False,
            'reproducible': False,
            'beneficial': False,
            'details': {},
            'timestamp': time.time()
        }
        
        # Check reproducibility
        if 'test_results' in validation_context:
            test_results = validation_context['test_results']
            
            if 'reproducibility' in test_results:
                result['reproducible'] = test_results['reproducibility'].get('result', False)
                result['details']['reproducibility'] = test_results['reproducibility']
                
        # Check if beneficial
        if innovation.overall_value > 0.6:  # Threshold for considering beneficial
            result['beneficial'] = True
            
        # Verification requires both reproducibility and benefit
        result['verified'] = result['reproducible'] and result['beneficial']
        
        # Update innovation
        innovation.reproducible = result['reproducible']
        innovation.verified = result['verified']
        
        # Store verification results
        if 'verification' not in innovation.data:
            innovation.data['verification'] = []
            
        innovation.data['verification'].append(result)
        
        return result['verified'], result


# ------------------------------------------------------------------------------
# Innovation Generation and Propagation
# ------------------------------------------------------------------------------

class InnovationBehavior(ABC):
    """Base class for modeling how innovations occur and propagate."""
    
    @abstractmethod
    def generate_innovation(self, 
                           context: Dict[str, Any], 
                           novelty_score: float,
                           source_component: str) -> Optional[Innovation]:
        """
        Generate a potential innovation based on context.
        
        Args:
            context: Context information.
            novelty_score: Novelty score that triggered the innovation.
            source_component: Component where the novelty was detected.
            
        Returns:
            Optional[Innovation]: Generated innovation if applicable.
        """
        pass
    
    @abstractmethod
    def propagate_innovation(self, 
                            innovation: Innovation, 
                            target_components: List[str],
                            system_state: Dict[str, Any]) -> Dict[str, Any]:
        """
        Propagate an innovation to target components.
        
        Args:
            innovation: The innovation to propagate.
            target_components: List of target component names.
            system_state: Current system state.
            
        Returns:
            Dict[str, Any]: Propagation results.
        """
        pass


class EvolutionaryInnovation(InnovationBehavior):
    """Models innovations as evolutionary adaptations."""
    
    def __init__(self, 
                mutation_rate: float = 0.1,
                crossover_rate: float = 0.3,
                selection_pressure: float = 2.0):
        """
        Initialize the evolutionary innovation model.
        
        Args:
            mutation_rate: Rate of random mutations.
            crossover_rate: Rate of crossover between existing behaviors.
            selection_pressure: Pressure for selecting beneficial innovations.
        """
        self.mutation_rate = mutation_rate
        self.crossover_rate = crossover_rate
        self.selection_pressure = selection_pressure
        self.behavior_pool = []
        
    def generate_innovation(self, 
                           context: Dict[str, Any], 
                           novelty_score: float,
                           source_component: str) -> Optional[Innovation]:
        """
        Generate a potential innovation through evolutionary mechanisms.
        
        Args:
            context: Context information.
            novelty_score: Novelty score that triggered the innovation.
            source_component: Component where the novelty was detected.
            
        Returns:
            Optional[Innovation]: Generated innovation if applicable.
        """
        # Generate potential innovation probabilistically based on novelty
        if random.random() > novelty_score:
            return None  # No innovation generated
            
        # Decide innovation mechanism (mutation, crossover, or recombination)
        innovation_type = None
        innovation_origin = None
        
        rand_val = random.random()
        
        if rand_val < self.mutation_rate:
            # Mutation: random variation of existing behavior
            innovation_type = self._select_random_innovation_type()
            innovation_origin = InnovationOrigin.EVOLUTIONARY
            name = f"Evolutionary Mutation in {source_component}"
            description = f"Random variation of existing behavior in {source_component}"
            
        elif rand_val < self.mutation_rate + self.crossover_rate:
            # Crossover: combine aspects of different behaviors
            innovation_type = self._select_random_innovation_type()
            innovation_origin = InnovationOrigin.RECOMBINATION
            name = f"Behavioral Recombination in {source_component}"
            description = f"Combination of multiple behavior patterns in {source_component}"
            
        else:
            # Planned adaptation: directed change based on feedback
            innovation_type = self._select_random_innovation_type()
            innovation_origin = InnovationOrigin.PLANNED
            name = f"Adaptive Response in {source_component}"
            description = f"Directed adaptation to environmental feedback in {source_component}"
            
        # Create innovation with appropriate domain
        domain = self._select_domain_for_component(source_component)
        
        innovation = Innovation(
            name=name,
            description=description,
            type=innovation_type,
            origin=innovation_origin,
            domain=domain,
            source_component=source_component,
            novelty_score=novelty_score,
            data={
                'context': {k: v for k, v in context.items() if isinstance(v, (int, float, str, bool, type(None)))},
                'generation_mechanism': innovation_origin.name.lower(),
                'component_state': context.get('component_state', {})
            }
        )
        
        # Add to behavior pool for potential future recombination
        self.behavior_pool.append({
            'innovation': innovation,
            'fitness': 0.5,  # Initial fitness is neutral
            'age': 0
        })
        
        # Limit pool size
        if len(self.behavior_pool) > 100:
            # Remove oldest or least fit behaviors
            self.behavior_pool.sort(key=lambda x: x['fitness'] / (x['age'] + 1), reverse=True)
            self.behavior_pool = self.behavior_pool[:80]
            
        return innovation
    
    def propagate_innovation(self, 
                            innovation: Innovation, 
                            target_components: List[str],
                            system_state: Dict[str, Any]) -> Dict[str, Any]:
        """
        Propagate an innovation to target components through evolutionary mechanisms.
        
        Args:
            innovation: The innovation to propagate.
            target_components: List of target component names.
            system_state: Current system state.
            
        Returns:
            Dict[str, Any]: Propagation results.
        """
        results = {
            'successful_targets': [],
            'failed_targets': [],
            'modified_behaviors': {}
        }
        
        # Calculate propagation probability based on innovation value
        base_probability = 0.3 + 0.6 * innovation.overall_value
        
        for target in target_components:
            # Adjust probability based on compatibility
            compatibility = self._calculate_compatibility(innovation, target, system_state)
            propagation_probability = base_probability * compatibility
            
            # Attempt to propagate
            if random.random() < propagation_probability:
                results['successful_targets'].append(target)
                
                # Record behavior modification
                results['modified_behaviors'][target] = {
                    'innovation_id': innovation.id,
                    'modification_type': innovation.type.name,
                    'intensity': random.uniform(0.5, 1.0) * innovation.overall_value,
                    'timestamp': time.time()
                }
                
                # Update behavior pool
                for behavior in self.behavior_pool:
                    if behavior['innovation'].source_component == target:
                        behavior['fitness'] += 0.1 * innovation.overall_value
            else:
                results['failed_targets'].append(target)
                
        # Age all behaviors in pool
        for behavior in self.behavior_pool:
            behavior['age'] += 1
            
        return results
    
    def _select_random_innovation_type(self) -> InnovationType:
        """Select a random innovation type with weighted probabilities."""
        types = [
            (InnovationType.ALGORITHMIC, 3),
            (InnovationType.ARCHITECTURAL, 1),
            (InnovationType.BEHAVIORAL, 4),
            (InnovationType.REPRESENTATIONAL, 2),
            (InnovationType.CONCEPTUAL, 1),
            (InnovationType.EFFICIENCY, 3),
            (InnovationType.INTEGRATION, 2),
            (InnovationType.PROCEDURAL, 3),
            (InnovationType.EMERGENT, 1)
        ]
        
        total_weight = sum(weight for _, weight in types)
        r = random.uniform(0, total_weight)
        
        cumulative_weight = 0
        for innovation_type, weight in types:
            cumulative_weight += weight
            if r <= cumulative_weight:
                return innovation_type
                
        return InnovationType.BEHAVIORAL  # Default
    
    def _select_domain_for_component(self, component_name: str) -> InnovationDomain:
        """Select an appropriate domain based on component name."""
        component_lower = component_name.lower()
        
        if any(term in component_lower for term in ['reason', 'logic', 'thought', 'problem']):
            return InnovationDomain.REASONING
        elif any(term in component_lower for term in ['perceive', 'input', 'sensor', 'detect']):
            return InnovationDomain.PERCEPTION
        elif any(term in component_lower for term in ['learn', 'train', 'adapt']):
            return InnovationDomain.LEARNING
        elif any(term in component_lower for term in ['memory', 'store', 'retrieve', 'recall']):
            return InnovationDomain.MEMORY
        elif any(term in component_lower for term in ['attention', 'focus', 'allocate']):
            return InnovationDomain.ATTENTION
        elif any(term in component_lower for term in ['plan', 'schedule', 'decide']):
            return InnovationDomain.PLANNING
        elif any(term in component_lower for term in ['language', 'text', 'speech', 'word']):
            return InnovationDomain.LANGUAGE
        elif any(term in component_lower for term in ['visual', 'image', 'picture', 'render']):
            return InnovationDomain.VISUALIZATION
        elif any(term in component_lower for term in ['integrate', 'combine', 'bridge']):
            return InnovationDomain.INTEGRATION
        else:
            return InnovationDomain.SYSTEM
    
    def _calculate_compatibility(self, 
                               innovation: Innovation, 
                               target_component: str,
                               system_state: Dict[str, Any]) -> float:
        """Calculate the compatibility of an innovation with a target component."""
        # Base compatibility on domain matching
        target_domain = self._select_domain_for_component(target_component)
        
        if innovation.domain == target_domain:
            compatibility = 0.8  # High compatibility for same domain
        elif innovation.domain == InnovationDomain.SYSTEM or target_domain == InnovationDomain.SYSTEM:
            compatibility = 0.6  # Moderate compatibility for system-level innovations
        elif innovation.domain == InnovationDomain.INTEGRATION:
            compatibility = 0.7  # Good compatibility for integration innovations
        else:
            compatibility = 0.4  # Lower compatibility for different domains
            
        # Adjust based on innovation type
        if innovation.type == InnovationType.EMERGENT:
            compatibility *= 0.8  # Emergent innovations are harder to propagate
            
        # Adjust based on component state if available
        if 'components' in system_state and target_component in system_state['components']:
            component_state = system_state['components'][target_component]
            
            # More adaptable components are more compatible
            adaptability = component_state.get('adaptability', 0.5)
            compatibility *= 0.8 + 0.4 * adaptability
            
        return min(1.0, max(0.1, compatibility))


class CreativeInnovation(InnovationBehavior):
    """Models innovations as creative recombinations of existing ideas."""
    
    def __init__(self, 
                creativity_rate: float = 0.2,
                idea_pool_size: int = 50,
                combination_depth: int = 3):
        """
        Initialize the creative innovation model.
        
        Args:
            creativity_rate: Base rate of creative innovation generation.
            idea_pool_size: Maximum number of ideas to keep in the pool.
            combination_depth: Maximum number of ideas to combine.
        """
        self.creativity_rate = creativity_rate
        self.idea_pool_size = idea_pool_size
        self.combination_depth = combination_depth
        self.idea_pool = []
        
    def generate_innovation(self, 
                           context: Dict[str, Any], 
                           novelty_score: float,
                           source_component: str) -> Optional[Innovation]:
        """
        Generate a potential innovation through creative recombination.
        
        Args:
            context: Context information.
            novelty_score: Novelty score that triggered the innovation.
            source_component: Component where the novelty was detected.
            
        Returns:
            Optional[Innovation]: Generated innovation if applicable.
        """
        # Creative innovation happens less frequently but is more impactful
        if random.random() > self.creativity_rate * novelty_score:
            return None  # No innovation generated
            
        # Extract concepts from context
        concepts = self._extract_concepts(context)
        
        # Add to idea pool
        self._add_to_idea_pool(concepts, source_component)
        
        # Check if we have enough ideas to combine
        if len(self.idea_pool) < 2:
            return None  # Not enough ideas yet
            
        # Select random ideas to combine
        num_ideas = random.randint(2, min(self.combination_depth, len(self.idea_pool)))
        selected_ideas = random.sample(self.idea_pool, num_ideas)
        
        # Generate combined name and description
        components = [idea['name'] for idea in selected_ideas]
        domains = [idea['domain'] for idea in selected_ideas]
        
        name = f"Creative Combination: {' + '.join(components[:3])}"
        if len(components) > 3:
            name += f" + {len(components) - 3} more"
            
        description = f"Novel combination of concepts from {', '.join(set(domains))} domains"
        
        # Select appropriate innovation type
        innovation_type = self._determine_innovation_type(selected_ideas)
        
        # Create innovation
        innovation = Innovation(
            name=name,
            description=description,
            type=innovation_type,
            origin=InnovationOrigin.RECOMBINATION,
            domain=self._most_common_domain(domains),
            source_component=source_component,
            novelty_score=novelty_score * 1.2,  # Creative innovations are more novel
            data={
                'combined_concepts': [idea['name'] for idea in selected_ideas],
                'source_domains': list(set(domains)),
                'creation_method': 'creative_recombination'
            }
        )
        
        return innovation
    
    def propagate_innovation(self, 
                            innovation: Innovation, 
                            target_components: List[str],
                            system_state: Dict[str, Any]) -> Dict[str, Any]:
        """
        Propagate a creative innovation to target components.
        
        Args:
            innovation: The innovation to propagate.
            target_components: List of target component names.
            system_state: Current system state.
            
        Returns:
            Dict[str, Any]: Propagation results.
        """
        results = {
            'successful_targets': [],
            'failed_targets': [],
            'inspired_components': []
        }
        
        # Creative innovations have higher initial propagation probability
        base_probability = 0.4 + 0.5 * innovation.overall_value
        
        # Calculate domain relevance for each target
        for target in target_components:
            target_domain = self._determine_component_domain(target)
            
            # Calculate propagation probability based on domain relevance
            if innovation.domain == target_domain:
                propagation_probability = base_probability * 1.2  # Bonus for domain match
            elif innovation.domain == InnovationDomain.SYSTEM:
                propagation_probability = base_probability  # System innovations apply broadly
            else:
                propagation_probability = base_probability * 0.8  # Penalty for domain mismatch
                
            # Attempt to propagate
            if random.random() < propagation_probability:
                results['successful_targets'].append(target)
                
                # Creative innovations may inspire further innovations
                if random.random() < 0.3 * innovation.novelty_score:
                    results['inspired_components'].append(target)
            else:
                results['failed_targets'].append(target)
                
        return results
    
    def _extract_concepts(self, context: Dict[str, Any]) -> List[str]:
        """Extract key concepts from the context."""
        concepts = []
        
        # Extract from various context elements
        if 'observation' in context and isinstance(context['observation'], dict):
            # Extract keys as concepts
            concepts.extend(list(context['observation'].keys()))
            
        if 'features' in context and isinstance(context['features'], list):
            # Use features directly
            concepts.extend(context['features'])
            
        if 'keywords' in context and isinstance(context['keywords'], list):
            # Use keywords directly
            concepts.extend(context['keywords'])
            
        if 'message' in context and isinstance(context['message'], str):
            # Extract words from message (simplified)
            words = context['message'].split()
            concepts.extend([w for w in words if len(w) > 4])
            
        # Remove duplicates and limit length
        return list(set(concepts))[:10]
    
    def _add_to_idea_pool(self, concepts: List[str], source_component: str):
        """Add concepts to the idea pool."""
        domain = self._determine_component_domain(source_component)
        
        for concept in concepts:
            idea = {
                'name': concept,
                'domain': domain.name,
                'source_component': source_component,
                'timestamp': time.time()
            }
            
            # Add to pool (avoid duplicates)
            if not any(existing['name'] == concept for existing in self.idea_pool):
                self.idea_pool.append(idea)
                
        # Limit pool size
        if len(self.idea_pool) > self.idea_pool_size:
            # Keep most recent ideas
            self.idea_pool.sort(key=lambda x: x['timestamp'], reverse=True)
            self.idea_pool = self.idea_pool[:self.idea_pool_size]
    
    def _determine_innovation_type(self, ideas: List[Dict[str, Any]]) -> InnovationType:
        """Determine the most appropriate innovation type based on the combined ideas."""
        # Count domains
        domains = [idea['domain'] for idea in ideas]
        unique_domains = set(domains)
        
        if len(unique_domains) >= 3:
            # Cross-domain combinations tend to be conceptual or integration
            return random.choice([InnovationType.CONCEPTUAL, InnovationType.INTEGRATION])
        elif 'REASONING' in domains or 'PLANNING' in domains:
            # Reasoning/planning domains tend to produce algorithmic innovations
            return random.choice([InnovationType.ALGORITHMIC, InnovationType.PROCEDURAL])
        elif 'LEARNING' in domains:
            # Learning domains tend to produce behavioral innovations
            return random.choice([InnovationType.BEHAVIORAL, InnovationType.REPRESENTATIONAL])
        elif 'LANGUAGE' in domains or 'VISUALIZATION' in domains:
            # Language/visualization domains tend to produce representational innovations
            return random.choice([InnovationType.REPRESENTATIONAL, InnovationType.CONCEPTUAL])
        elif 'EFFICIENCY' in domains:
            # Efficiency-related domains tend to produce efficiency or architectural innovations
            return random.choice([InnovationType.EFFICIENCY, InnovationType.ARCHITECTURAL])
        else:
            # Default to behavioral or emergent
            return random.choice([InnovationType.BEHAVIORAL, InnovationType.EMERGENT])
    
    def _most_common_domain(self, domains: List[str]) -> InnovationDomain:
        """Determine the most common domain in a list."""
        if not domains:
            return InnovationDomain.SYSTEM
            
        # Count occurrences
        domain_counts = {}
        for domain in domains:
            if domain not in domain_counts:
                domain_counts[domain] = 0
            domain_counts[domain] += 1
            
        # Find most common
        most_common = max(domain_counts.items(), key=lambda x: x[1])[0]
        
        # Convert string to enum
        try:
            return InnovationDomain[most_common]
        except KeyError:
            return InnovationDomain.SYSTEM
    
    def _determine_component_domain(self, component_name: str) -> InnovationDomain:
        """Determine the most likely domain for a component based on its name."""
        component_lower = component_name.lower()
        
        domain_keywords = {
            InnovationDomain.REASONING: ['reason', 'logic', 'inference', 'deduction', 'planning'],
            InnovationDomain.PERCEPTION: ['perceive', 'sense', 'detect', 'input', 'observe'],
            InnovationDomain.LEARNING: ['learn', 'train', 'adapt', 'generalize', 'classify'],
            InnovationDomain.MEMORY: ['memory', 'store', 'recall', 'retrieve', 'buffer', 'cache'],
            InnovationDomain.ATTENTION: ['attention', 'focus', 'filter', 'priority', 'salience'],
            InnovationDomain.PLANNING: ['plan', 'goal', 'strategy', 'schedule', 'objective'],
            InnovationDomain.LANGUAGE: ['language', 'linguistic', 'text', 'speech', 'word', 'sentence'],
            InnovationDomain.VISUALIZATION: ['visual', 'image', 'display', 'render', 'graph', 'plot'],
            InnovationDomain.INTEGRATION: ['integrate', 'connect', 'bridge', 'combine', 'unify'],
            InnovationDomain.SYSTEM: ['system', 'framework', 'architecture', 'platform', 'infrastructure']
        }
        
        # Score each domain based on keyword matching
        domain_scores = {}
        for domain, keywords in domain_keywords.items():
            score = sum(1 for keyword in keywords if keyword in component_lower)
            domain_scores[domain] = score
            
        # Choose domain with highest score, or SYSTEM if no keywords match
        best_domain = max(domain_scores.items(), key=lambda x: x[1])[0]
        if domain_scores[best_domain] == 0:
            return InnovationDomain.SYSTEM
            
        return best_domain