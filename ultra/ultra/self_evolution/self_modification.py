#!/usr/bin/env python3
"""
ULTRA: Ultimate Learning & Thought Reasoning Architecture
Self-Modification Protocols Implementation

This module implements the Self-Modification Protocols component of ULTRA, which enables
the system to safely update its own code and architecture. It includes:

1. Modification Proposals: Framework for proposing and describing code changes
2. Safety Constraints: Ensures modifications adhere to safety requirements
3. Modification Analysis: Analyzes the potential impact of modifications
4. Sandbox Testing: Tests modifications in an isolated environment
5. Controlled Deployment: Manages the safe application of modifications with rollback capability

The Self-Modification Protocols implement a form of controlled self-improvement,
where the AI system can modify its own structure and behavior while maintaining
operational safety and stability.
"""

import os
import sys
import time
import uuid
import shutil
import logging
import json
import copy
import inspect
import importlib
import importlib.util
import tempfile
import ast
import re
import difflib
import concurrent.futures
import multiprocessing
import threading
import hashlib
import subprocess
from enum import Enum, auto
from dataclasses import dataclass, field, asdict
from abc import ABC, abstractmethod
from typing import Dict, List, Tuple, Any, Callable, Optional, Union, Set
from functools import wraps
from pathlib import Path

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Add parent directory to sys.path to allow for relative imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Try to import other ULTRA components
try:
    from ultra.utils.config import Configuration
    from ultra.utils.monitoring import PerformanceMonitor, SystemMonitor
    from ultra.safety.constraints import SafetyChecker
    from ultra.meta_cognitive.self_critique import CritiqueGenerator
except ImportError:
    logger.warning("Some ULTRA components could not be imported. " 
                   "Self-Modification functionality may be limited.")

# ------------------------------------------------------------------------------
# Core Modification Types and Proposals
# ------------------------------------------------------------------------------

class ModificationType(Enum):
    """Types of self-modifications that can be performed."""
    PARAMETER_UPDATE = auto()     # Update parameters of the system
    ARCHITECTURE_CHANGE = auto()  # Change the architecture of a component
    FUNCTION_UPDATE = auto()      # Update a function's implementation
    MODULE_ADDITION = auto()      # Add a new module to the system
    MODULE_REMOVAL = auto()       # Remove a module from the system
    CODE_REFACTORING = auto()     # Refactor code without changing functionality


@dataclass
class ModificationImpact:
    """Represents the expected impact of a modification."""
    performance_change: float = 0.0      # Change in performance (%)
    memory_usage_change: float = 0.0     # Change in memory usage (%)
    cpu_usage_change: float = 0.0        # Change in CPU usage (%)
    reliability_change: float = 0.0      # Change in reliability (%)
    complexity_change: float = 0.0       # Change in code complexity (%)
    capability_addition: List[str] = field(default_factory=list)  # New capabilities
    capability_removal: List[str] = field(default_factory=list)   # Removed capabilities
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            'performance_change': self.performance_change,
            'memory_usage_change': self.memory_usage_change,
            'cpu_usage_change': self.cpu_usage_change,
            'reliability_change': self.reliability_change,
            'complexity_change': self.complexity_change,
            'capability_addition': self.capability_addition,
            'capability_removal': self.capability_removal
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ModificationImpact':
        """Create from dictionary."""
        return cls(
            performance_change=data.get('performance_change', 0.0),
            memory_usage_change=data.get('memory_usage_change', 0.0),
            cpu_usage_change=data.get('cpu_usage_change', 0.0),
            reliability_change=data.get('reliability_change', 0.0),
            complexity_change=data.get('complexity_change', 0.0),
            capability_addition=data.get('capability_addition', []),
            capability_removal=data.get('capability_removal', [])
        )

    def combine(self, other: 'ModificationImpact') -> 'ModificationImpact':
        """Combine with another impact object."""
        return ModificationImpact(
            performance_change=self.performance_change + other.performance_change,
            memory_usage_change=self.memory_usage_change + other.memory_usage_change,
            cpu_usage_change=self.cpu_usage_change + other.cpu_usage_change,
            reliability_change=self.reliability_change + other.reliability_change,
            complexity_change=self.complexity_change + other.complexity_change,
            capability_addition=list(set(self.capability_addition + other.capability_addition)),
            capability_removal=list(set(self.capability_removal + other.capability_removal))
        )


@dataclass
class CodeChange:
    """Represents a specific code change."""
    file_path: str
    original_code: str = ""
    modified_code: str = ""
    line_start: int = 0
    line_end: int = 0
    change_type: str = "modify"  # "add", "modify", "delete"
    
    def get_diff(self) -> str:
        """Generate a unified diff for the code change."""
        if not self.original_code and not self.modified_code:
            return ""
            
        original_lines = self.original_code.splitlines(keepends=True)
        modified_lines = self.modified_code.splitlines(keepends=True)
        
        diff = difflib.unified_diff(
            original_lines,
            modified_lines,
            fromfile=f"{self.file_path}.original",
            tofile=f"{self.file_path}.modified",
            lineterm=""
        )
        
        return "".join(diff)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            'file_path': self.file_path,
            'original_code': self.original_code,
            'modified_code': self.modified_code,
            'line_start': self.line_start,
            'line_end': self.line_end,
            'change_type': self.change_type
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'CodeChange':
        """Create from dictionary."""
        return cls(
            file_path=data.get('file_path', ""),
            original_code=data.get('original_code', ""),
            modified_code=data.get('modified_code', ""),
            line_start=data.get('line_start', 0),
            line_end=data.get('line_end', 0),
            change_type=data.get('change_type', "modify")
        )


@dataclass
class SafetyAnalysis:
    """Results of safety analysis for a modification."""
    static_analysis_passed: bool = False
    runtime_analysis_passed: bool = False
    test_suite_passed: bool = False
    test_coverage: float = 0.0
    vulnerabilities_detected: int = 0
    error_likelihood: float = 0.0
    deadlock_risk: float = 0.0
    memory_leak_risk: float = 0.0
    security_risk: float = 0.0
    test_results: Dict[str, bool] = field(default_factory=dict)
    warnings: List[str] = field(default_factory=list)
    recommendations: List[str] = field(default_factory=list)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            'static_analysis_passed': self.static_analysis_passed,
            'runtime_analysis_passed': self.runtime_analysis_passed,
            'test_suite_passed': self.test_suite_passed,
            'test_coverage': self.test_coverage,
            'vulnerabilities_detected': self.vulnerabilities_detected,
            'error_likelihood': self.error_likelihood,
            'deadlock_risk': self.deadlock_risk,
            'memory_leak_risk': self.memory_leak_risk,
            'security_risk': self.security_risk,
            'test_results': self.test_results,
            'warnings': self.warnings,
            'recommendations': self.recommendations
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SafetyAnalysis':
        """Create from dictionary."""
        return cls(
            static_analysis_passed=data.get('static_analysis_passed', False),
            runtime_analysis_passed=data.get('runtime_analysis_passed', False),
            test_suite_passed=data.get('test_suite_passed', False),
            test_coverage=data.get('test_coverage', 0.0),
            vulnerabilities_detected=data.get('vulnerabilities_detected', 0),
            error_likelihood=data.get('error_likelihood', 0.0),
            deadlock_risk=data.get('deadlock_risk', 0.0),
            memory_leak_risk=data.get('memory_leak_risk', 0.0),
            security_risk=data.get('security_risk', 0.0),
            test_results=data.get('test_results', {}),
            warnings=data.get('warnings', []),
            recommendations=data.get('recommendations', [])
        )
    
    @property
    def is_safe(self) -> bool:
        """Overall safety assessment."""
        return (self.static_analysis_passed and
                self.runtime_analysis_passed and
                self.test_suite_passed and
                self.vulnerabilities_detected == 0 and
                self.error_likelihood < 0.3 and
                self.deadlock_risk < 0.2 and
                self.memory_leak_risk < 0.2 and
                self.security_risk < 0.2)


@dataclass
class ModificationProposal:
    """A proposal for a self-modification."""
    type: ModificationType
    target: str  # Module, class, or function path to modify
    description: str
    justification: str
    code_changes: List[CodeChange] = field(default_factory=list)
    expected_impact: ModificationImpact = field(default_factory=ModificationImpact)
    safety_analysis: SafetyAnalysis = field(default_factory=SafetyAnalysis)
    priority: float = 0.5  # 0.0 to 1.0
    dependencies: List[str] = field(default_factory=list)
    created_at: float = field(default_factory=time.time)
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    status: str = "proposed"  # proposed, approved, rejected, deployed, failed
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            'id': self.id,
            'type': self.type.name,
            'target': self.target,
            'description': self.description,
            'justification': self.justification,
            'code_changes': [cc.to_dict() for cc in self.code_changes],
            'expected_impact': self.expected_impact.to_dict(),
            'safety_analysis': self.safety_analysis.to_dict(),
            'priority': self.priority,
            'dependencies': self.dependencies,
            'created_at': self.created_at,
            'status': self.status
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ModificationProposal':
        """Create from dictionary."""
        return cls(
            id=data.get('id', str(uuid.uuid4())),
            type=ModificationType[data['type']],
            target=data['target'],
            description=data['description'],
            justification=data.get('justification', "No justification provided"),
            code_changes=[CodeChange.from_dict(cc) for cc in data.get('code_changes', [])],
            expected_impact=ModificationImpact.from_dict(data.get('expected_impact', {})),
            safety_analysis=SafetyAnalysis.from_dict(data.get('safety_analysis', {})),
            priority=data.get('priority', 0.5),
            dependencies=data.get('dependencies', []),
            created_at=data.get('created_at', time.time()),
            status=data.get('status', 'proposed')
        )
    
    def get_diff(self) -> str:
        """Get a unified diff representation of all code changes."""
        diffs = []
        for change in self.code_changes:
            diff = change.get_diff()
            if diff:
                diffs.append(diff)
        
        return "\n".join(diffs)
    
    def has_circular_dependencies(self, all_proposals: Dict[str, 'ModificationProposal']) -> bool:
        """Check if this proposal has circular dependencies."""
        visited = set()
        path = set()
        
        def dfs(proposal_id):
            if proposal_id in path:
                return True  # Circular dependency found
            if proposal_id in visited:
                return False  # Already checked, no circular dependency
                
            path.add(proposal_id)
            visited.add(proposal_id)
            
            if proposal_id in all_proposals:
                for dep in all_proposals[proposal_id].dependencies:
                    if dfs(dep):
                        return True
                        
            path.remove(proposal_id)
            return False
            
        return dfs(self.id)


class DependencyResolver:
    """Resolves dependencies between modification proposals."""
    
    def __init__(self):
        self.proposals = {}  # id -> ModificationProposal
        
    def add_proposal(self, proposal: ModificationProposal):
        """Add a proposal to the resolver."""
        self.proposals[proposal.id] = proposal
        
    def remove_proposal(self, proposal_id: str):
        """Remove a proposal from the resolver."""
        if proposal_id in self.proposals:
            del self.proposals[proposal_id]
            
    def get_execution_order(self, proposal_ids: List[str] = None) -> List[str]:
        """
        Get the correct execution order for proposals to satisfy dependencies.
        
        Args:
            proposal_ids: List of proposal IDs to consider, or None for all proposals.
            
        Returns:
            List[str]: List of proposal IDs in execution order.
        """
        if proposal_ids is None:
            proposal_ids = list(self.proposals.keys())
            
        # Check for unknown proposals
        unknown_ids = [pid for pid in proposal_ids if pid not in self.proposals]
        if unknown_ids:
            raise ValueError(f"Unknown proposal IDs: {unknown_ids}")
            
        # Create a directed graph of dependencies
        graph = {pid: [] for pid in proposal_ids}
        for pid in proposal_ids:
            proposal = self.proposals[pid]
            for dep in proposal.dependencies:
                if dep in proposal_ids:
                    graph[dep].append(pid)  # Dependents of dep
                    
        # Topological sort
        result = []
        temp_mark = set()
        perm_mark = set()
        
        def visit(node):
            if node in perm_mark:
                return
            if node in temp_mark:
                raise ValueError(f"Circular dependency detected involving {node}")
                
            temp_mark.add(node)
            
            for dependent in graph[node]:
                visit(dependent)
                
            temp_mark.remove(node)
            perm_mark.add(node)
            result.append(node)
            
        # Visit all nodes
        for pid in proposal_ids:
            if pid not in perm_mark:
                visit(pid)
                
        # Reverse to get correct order (dependencies first)
        return result[::-1]
    
    def check_circular_dependencies(self) -> List[str]:
        """Check for circular dependencies and return IDs of problematic proposals."""
        problematic = []
        
        for pid, proposal in self.proposals.items():
            if proposal.has_circular_dependencies(self.proposals):
                problematic.append(pid)
                
        return problematic


# ------------------------------------------------------------------------------
# Safety Constraints
# ------------------------------------------------------------------------------

class SafetyConstraint(ABC):
    """Base class for safety constraints that modifications must satisfy."""
    
    @abstractmethod
    def check(self, proposal: ModificationProposal) -> Tuple[bool, str]:
        """
        Check if a modification proposal satisfies the safety constraint.
        
        Args:
            proposal: The modification proposal to check.
            
        Returns:
            Tuple[bool, str]: (True if satisfied, explanation message)
        """
        pass
    
    @abstractmethod
    def get_description(self) -> str:
        """Get a description of the safety constraint."""
        pass


class ResourceBoundConstraint(SafetyConstraint):
    """Constraint to ensure modifications don't exceed resource limits."""
    
    def __init__(self, max_memory_usage: float, max_cpu_usage: float, max_disk_usage: float = 0.0):
        self.max_memory_usage = max_memory_usage  # Percentage increase
        self.max_cpu_usage = max_cpu_usage        # Percentage increase
        self.max_disk_usage = max_disk_usage      # Percentage increase
        
    def check(self, proposal: ModificationProposal) -> Tuple[bool, str]:
        """Check if the proposed modification respects resource bounds."""
        expected_impact = proposal.expected_impact
        
        # Check memory usage
        if expected_impact.memory_usage_change > self.max_memory_usage:
            return (False, f"Expected memory usage increase of {expected_impact.memory_usage_change:.2f}% "
                          f"exceeds maximum allowed of {self.max_memory_usage:.2f}%")
                          
        # Check CPU usage
        if expected_impact.cpu_usage_change > self.max_cpu_usage:
            return (False, f"Expected CPU usage increase of {expected_impact.cpu_usage_change:.2f}% "
                          f"exceeds maximum allowed of {self.max_cpu_usage:.2f}%")
                          
        # Check disk usage (if applicable)
        # This would need actual implementation to calculate disk usage of the changes
        
        return (True, "Resource bounds satisfied")
    
    def get_description(self) -> str:
        """Get a description of the resource bound constraint."""
        return (f"Ensures modifications don't exceed resource limits: "
                f"Memory usage <= {self.max_memory_usage:.2f}%, "
                f"CPU usage <= {self.max_cpu_usage:.2f}%, "
                f"Disk usage <= {self.max_disk_usage:.2f}%")


class FunctionalityPreservationConstraint(SafetyConstraint):
    """Constraint to ensure modifications preserve essential functionality."""
    
    def __init__(self, essential_functions: List[str], test_cases: Dict[str, Callable]):
        self.essential_functions = essential_functions
        self.test_cases = test_cases
        
    def check(self, proposal: ModificationProposal) -> Tuple[bool, str]:
        """Check if the proposed modification preserves essential functionality."""
        # Check if the modification affects any essential function
        affected_essentials = []
        
        for func_name in self.essential_functions:
            # Check if the target is the function or contains the function
            if (proposal.target == func_name or 
                proposal.target.endswith(f".{func_name}") or 
                f"{proposal.target}." in func_name):
                affected_essentials.append(func_name)
                
        if not affected_essentials:
            return (True, "No essential functions affected")
            
        # If essential functions are affected, check if test cases pass
        safety = proposal.safety_analysis
        
        # Check if all tests for essential functions pass
        failed_tests = []
        for func_name in affected_essentials:
            test_name = f"test_{func_name}"
            if test_name in safety.test_results:
                if not safety.test_results[test_name]:
                    failed_tests.append(func_name)
                    
        if failed_tests:
            return (False, f"Tests failed for essential functions: {', '.join(failed_tests)}")
            
        # If not all essential functions have tests, be conservative
        untested = [func for func in affected_essentials 
                   if f"test_{func}" not in safety.test_results]
        if untested:
            return (False, f"No tests available for essential functions: {', '.join(untested)}")
            
        return (True, "All affected essential functions pass tests")
    
    def get_description(self) -> str:
        """Get a description of the functionality preservation constraint."""
        return (f"Ensures modifications preserve essential functionality: "
                f"Protected functions = {', '.join(self.essential_functions)}")


class SecurityConstraint(SafetyConstraint):
    """Constraint to ensure modifications don't introduce security vulnerabilities."""
    
    def __init__(self, vulnerability_threshold: float = 0.0, security_checkers: List[Callable] = None):
        self.vulnerability_threshold = vulnerability_threshold
        self.security_checkers = security_checkers or []
        
    def check(self, proposal: ModificationProposal) -> Tuple[bool, str]:
        """Check if the proposed modification is secure."""
        safety = proposal.safety_analysis
        
        # Check for detected vulnerabilities
        if safety.vulnerabilities_detected > 0:
            return (False, f"Detected {safety.vulnerabilities_detected} vulnerabilities")
            
        # Check security risk score
        if safety.security_risk > self.vulnerability_threshold:
            return (False, f"Security risk score of {safety.security_risk:.2f} exceeds threshold of "
                          f"{self.vulnerability_threshold:.2f}")
                          
        # Run specialized security checkers
        for i, checker in enumerate(self.security_checkers):
            try:
                checker_result = checker(proposal)
                if not checker_result:
                    return (False, f"Failed security check #{i+1}")
            except Exception as e:
                logger.error(f"Error in security checker #{i+1}: {str(e)}")
                return (False, f"Error in security checker #{i+1}: {str(e)}")
                
        return (True, "Passed all security checks")
    
    def get_description(self) -> str:
        """Get a description of the security constraint."""
        return (f"Ensures modifications don't introduce security vulnerabilities "
                f"with threshold {self.vulnerability_threshold:.2f}")


class ArchitecturalIntegrityConstraint(SafetyConstraint):
    """Ensures modifications maintain architectural integrity."""
    
    def __init__(self, allowed_dependencies: Dict[str, List[str]]):
        """
        Initialize with allowed dependencies between modules.
        
        Args:
            allowed_dependencies: Dict mapping modules to lists of modules they can depend on
        """
        self.allowed_dependencies = allowed_dependencies
        
    def check(self, proposal: ModificationProposal) -> Tuple[bool, str]:
        """Check if the proposed modification maintains architectural integrity."""
        # Extract target module from the proposal
        target_parts = proposal.target.split('.')
        target_module = target_parts[0] if target_parts else proposal.target
        
        # If we don't have dependency rules for this module, allow the modification
        if target_module not in self.allowed_dependencies:
            return (True, f"No architectural constraints for module {target_module}")
            
        # Check if the modification introduces forbidden dependencies
        for code_change in proposal.code_changes:
            # Use AST to extract imports
            try:
                tree = ast.parse(code_change.modified_code)
                imports = self._extract_imports(tree)
                
                # Check if any import violates dependency rules
                allowed = self.allowed_dependencies[target_module]
                for imported_module in imports:
                    imported_root = imported_module.split('.')[0]
                    if imported_root != target_module and imported_root not in allowed:
                        return (False, f"Forbidden dependency: {target_module} cannot depend on {imported_root}")
            except SyntaxError:
                # If we can't parse the code, be conservative
                return (False, "Cannot analyze code for architectural integrity: syntax error")
                
        return (True, "Architectural integrity maintained")
    
    def _extract_imports(self, tree: ast.AST) -> List[str]:
        """Extract imported modules from an AST."""
        imports = []
        
        for node in ast.walk(tree):
            if isinstance(node, ast.Import):
                for name in node.names:
                    imports.append(name.name)
            elif isinstance(node, ast.ImportFrom):
                if node.module:
                    imports.append(node.module)
                    
        return imports
    
    def get_description(self) -> str:
        """Get a description of the architectural integrity constraint."""
        return "Ensures modifications maintain architectural integrity and dependency rules"


class PerformanceConstraint(SafetyConstraint):
    """Ensures modifications don't degrade performance beyond a threshold."""
    
    def __init__(self, max_performance_degradation: float = 5.0):
        """
        Initialize with maximum allowed performance degradation.
        
        Args:
            max_performance_degradation: Maximum allowed performance degradation (%)
        """
        self.max_performance_degradation = max_performance_degradation
        
    def check(self, proposal: ModificationProposal) -> Tuple[bool, str]:
        """Check if the proposed modification maintains performance."""
        impact = proposal.expected_impact
        
        # Negative performance change means degradation
        if impact.performance_change < -self.max_performance_degradation:
            return (False, f"Expected performance degradation of {-impact.performance_change:.2f}% "
                          f"exceeds maximum allowed of {self.max_performance_degradation:.2f}%")
                          
        return (True, "Performance impact within acceptable limits")
    
    def get_description(self) -> str:
        """Get a description of the performance constraint."""
        return f"Ensures modifications don't degrade performance by more than {self.max_performance_degradation:.2f}%"


# ------------------------------------------------------------------------------
# Modification Analysis
# ------------------------------------------------------------------------------

class CodeAnalyzer:
    """Analyzes code for safety, complexity, and impact."""
    
    def __init__(self):
        self._complexity_cache = {}  # Cache for complexity calculations
        
    def calculate_complexity(self, code: str) -> Dict[str, Any]:
        """
        Calculate complexity metrics for code.
        
        Args:
            code: The code to analyze.
            
        Returns:
            Dict[str, Any]: Complexity metrics.
        """
        # Check cache
        code_hash = hashlib.md5(code.encode()).hexdigest()
        if code_hash in self._complexity_cache:
            return self._complexity_cache[code_hash]
            
        try:
            tree = ast.parse(code)
            
            # Analyze complexity
            visitor = ComplexityVisitor()
            visitor.visit(tree)
            
            metrics = {
                'cyclomatic_complexity': visitor.cyclomatic_complexity,
                'cognitive_complexity': visitor.cognitive_complexity,
                'function_count': visitor.function_count,
                'class_count': visitor.class_count,
                'line_count': len(code.splitlines()),
                'logical_loc': visitor.logical_loc,
                'max_nesting': visitor.max_nesting
            }
            
            # Cache results
            self._complexity_cache[code_hash] = metrics
            
            return metrics
        except SyntaxError as e:
            logger.error(f"Syntax error while calculating complexity: {e}")
            return {
                'cyclomatic_complexity': 0,
                'cognitive_complexity': 0,
                'function_count': 0,
                'class_count': 0,
                'line_count': len(code.splitlines()),
                'logical_loc': 0,
                'max_nesting': 0,
                'error': str(e)
            }
            
    def static_analysis(self, code: str) -> Dict[str, Any]:
        """
        Perform static analysis on code to identify potential issues.
        
        Args:
            code: The code to analyze.
            
        Returns:
            Dict[str, Any]: Analysis results.
        """
        issues = []
        warnings = []
        error_types = set()
        
        try:
            # Parse the code
            tree = ast.parse(code)
            
            # Check for various issues
            visitor = StaticAnalysisVisitor()
            visitor.visit(tree)
            
            issues.extend(visitor.issues)
            warnings.extend(visitor.warnings)
            error_types = visitor.error_types
        except SyntaxError as e:
            issues.append(f"Syntax error: {e}")
            error_types.add("SyntaxError")
            
        # Try to identify security issues
        security_issues = self._check_security_issues(code)
        issues.extend(security_issues)
        
        if any("security" in issue.lower() for issue in security_issues):
            error_types.add("SecurityVulnerability")
            
        return {
            'issues': issues,
            'warnings': warnings,
            'error_types': list(error_types),
            'error_count': len(issues),
            'warning_count': len(warnings),
            'has_syntax_error': "SyntaxError" in error_types,
            'has_security_issue': "SecurityVulnerability" in error_types
        }
        
    def compare_complexity(self, original_code: str, modified_code: str) -> Dict[str, Any]:
        """
        Compare complexity metrics between original and modified code.
        
        Args:
            original_code: The original code.
            modified_code: The modified code.
            
        Returns:
            Dict[str, Any]: Comparison results.
        """
        original_metrics = self.calculate_complexity(original_code)
        modified_metrics = self.calculate_complexity(modified_code)
        
        # Calculate changes
        changes = {}
        for key in original_metrics:
            if key in modified_metrics and isinstance(original_metrics[key], (int, float)):
                orig_val = original_metrics[key]
                mod_val = modified_metrics[key]
                
                if orig_val == 0:
                    # Avoid division by zero
                    changes[f"{key}_change"] = float('inf') if mod_val > 0 else 0.0
                else:
                    # Calculate percentage change
                    changes[f"{key}_change"] = ((mod_val - orig_val) / orig_val) * 100.0
                    
        return {
            'original': original_metrics,
            'modified': modified_metrics,
            'changes': changes
        }
        
    def detect_risky_patterns(self, code: str) -> List[Dict[str, Any]]:
        """
        Detect risky code patterns that might lead to issues.
        
        Args:
            code: The code to analyze.
            
        Returns:
            List[Dict[str, Any]]: Detected risky patterns.
        """
        patterns = []
        
        # Define risky patterns with regex
        risky_regex = {
            'eval_usage': (r'\beval\s*\(', 'Use of eval() is risky'),
            'exec_usage': (r'\bexec\s*\(', 'Use of exec() is risky'),
            'shell_injection': (r'os\.system\s*\(|subprocess\.call\s*\(|subprocess\.Popen\s*\(', 
                               'Potential command injection vulnerability'),
            'sql_injection': (r'execute\s*\(\s*["\']SELECT|execute\s*\(\s*["\']INSERT|'
                             r'execute\s*\(\s*["\']UPDATE|execute\s*\(\s*["\']DELETE', 
                             'Potential SQL injection vulnerability'),
            'hardcoded_credentials': (r'password\s*=\s*["\'][^"\']+["\']|api_key\s*=\s*["|\'][^"\']+["|\']', 
                                     'Hardcoded credentials detected'),
            'global_variables': (r'\bglobal\b', 'Use of global variables can lead to unexpected behavior'),
            'bare_except': (r'except\s*:', 'Bare except clause catches all exceptions'),
            'sleep_call': (r'time\.sleep\s*\(', 'Sleep calls can affect performance'),
            'infinite_loop': (r'while\s+True', 'Potential infinite loop'),
            'thread_safety': (r'threading\.|multiprocessing\.', 'Thread safety concerns in concurrent code')
        }
        
        # Check for regex patterns
        for line_num, line in enumerate(code.splitlines(), 1):
            for pattern_name, (regex, message) in risky_regex.items():
                if re.search(regex, line):
                    patterns.append({
                        'pattern': pattern_name,
                        'line': line_num,
                        'message': message,
                        'code': line.strip()
                    })
                    
        # Use AST for more complex patterns
        try:
            tree = ast.parse(code)
            visitor = RiskyPatternVisitor()
            visitor.visit(tree)
            
            patterns.extend(visitor.patterns)
        except SyntaxError:
            # If we can't parse the code, just use regex results
            pass
            
        return patterns
        
    def _check_security_issues(self, code: str) -> List[str]:
        """Check for security issues in code."""
        issues = []
        
        # Check for potential security issues
        security_patterns = {
            r'open\s*\(.+,\s*["\']w["\']': 'File write operation could lead to unauthorized modifications',
            r'pickle\.load': 'Pickle.load can execute arbitrary code',
            r'subprocess\.': 'Subprocess call could execute arbitrary commands',
            r'os\.system': 'OS system call could execute arbitrary commands',
            r'__import__\s*\(': 'Dynamic imports can load malicious code',
            r'yaml\.load\s*\(': 'YAML.load without SafeLoader can execute arbitrary code',
            r'random\.': 'Usage of random module (not cryptographically secure)',
            r'\.format\s*\(.+__\w+__': 'String formatting with special methods can lead to information disclosure',
            r'request\s*\[.+\]': 'Unvalidated request parameter access'
        }
        
        for pattern, message in security_patterns.items():
            if re.search(pattern, code):
                issues.append(message)
                
        return issues


class ComplexityVisitor(ast.NodeVisitor):
    """AST visitor to calculate code complexity metrics."""
    
    def __init__(self):
        self.cyclomatic_complexity = 0
        self.cognitive_complexity = 0
        self.function_count = 0
        self.class_count = 0
        self.logical_loc = 0
        self.current_nesting = 0
        self.max_nesting = 0
        
    def visit_ClassDef(self, node):
        self.class_count += 1
        self.logical_loc += 1
        self.generic_visit(node)
        
    def visit_FunctionDef(self, node):
        self.function_count += 1
        self.logical_loc += 1
        
        # Base complexity of 1 for each function
        func_complexity = 1
        
        # Count branches to increase complexity
        for item in ast.walk(node):
            if isinstance(item, (ast.If, ast.For, ast.While, ast.IfExp)):
                func_complexity += 1
                
            elif isinstance(item, ast.BoolOp):
                if isinstance(item.op, (ast.And, ast.Or)):
                    func_complexity += len(item.values) - 1
                    
        self.cyclomatic_complexity += func_complexity
        
        # For cognitive complexity, track nesting level too
        prev_nesting = self.current_nesting
        self.current_nesting = 0
        self.generic_visit(node)
        self.current_nesting = prev_nesting
        
    def visit_If(self, node):
        self.logical_loc += 1
        self.current_nesting += 1
        self.max_nesting = max(self.max_nesting, self.current_nesting)
        self.cognitive_complexity += self.current_nesting
        self.generic_visit(node)
        self.current_nesting -= 1
        
    def visit_For(self, node):
        self.logical_loc += 1
        self.current_nesting += 1
        self.max_nesting = max(self.max_nesting, self.current_nesting)
        self.cognitive_complexity += self.current_nesting
        self.generic_visit(node)
        self.current_nesting -= 1
        
    def visit_While(self, node):
        self.logical_loc += 1
        self.current_nesting += 1
        self.max_nesting = max(self.max_nesting, self.current_nesting)
        self.cognitive_complexity += self.current_nesting
        self.generic_visit(node)
        self.current_nesting -= 1
        
    def visit_Try(self, node):
        self.logical_loc += 1
        self.cognitive_complexity += 1
        self.generic_visit(node)
        
    def visit_ExceptHandler(self, node):
        self.logical_loc += 1
        self.cognitive_complexity += 1
        self.generic_visit(node)
        
    def visit_Return(self, node):
        self.logical_loc += 1
        self.generic_visit(node)
        
    def visit_Assign(self, node):
        self.logical_loc += 1
        self.generic_visit(node)
        
    def visit_AugAssign(self, node):
        self.logical_loc += 1
        self.generic_visit(node)
        
    def visit_AnnAssign(self, node):
        self.logical_loc += 1
        self.generic_visit(node)
        
    def visit_Expr(self, node):
        # Only count expression statements with a Call
        if isinstance(node.value, ast.Call):
            self.logical_loc += 1
        self.generic_visit(node)


class StaticAnalysisVisitor(ast.NodeVisitor):
    """AST visitor to perform static analysis for potential issues."""
    
    def __init__(self):
        self.issues = []
        self.warnings = []
        self.error_types = set()
        self.imported_modules = set()
        self.defined_names = set()
        self.used_names = set()
        self.current_function = None
        
    def visit_Import(self, node):
        for name in node.names:
            self.imported_modules.add(name.name)
        self.generic_visit(node)
        
    def visit_ImportFrom(self, node):
        if node.module:
            self.imported_modules.add(node.module)
        self.generic_visit(node)
        
    def visit_FunctionDef(self, node):
        prev_function = self.current_function
        self.current_function = node.name
        self.defined_names.add(node.name)
        
        # Check for too many arguments
        if len(node.args.args) > 10:
            self.warnings.append(f"Function '{node.name}' has too many arguments ({len(node.args.args)})")
            
        # Check for too many local variables
        local_vars = set()
        for sub_node in ast.walk(node):
            if isinstance(sub_node, ast.Name) and isinstance(sub_node.ctx, ast.Store):
                local_vars.add(sub_node.id)
                
        if len(local_vars) > 15:
            self.warnings.append(f"Function '{node.name}' has too many local variables ({len(local_vars)})")
            
        self.generic_visit(node)
        self.current_function = prev_function
        
    def visit_ClassDef(self, node):
        self.defined_names.add(node.name)
        
        # Check for too many methods
        method_count = sum(1 for item in node.body if isinstance(item, ast.FunctionDef))
        if method_count > 20:
            self.warnings.append(f"Class '{node.name}' has too many methods ({method_count})")
            
        self.generic_visit(node)
        
    def visit_Name(self, node):
        if isinstance(node.ctx, ast.Load):
            self.used_names.add(node.id)
        self.generic_visit(node)
        
    def visit_Try(self, node):
        # Check for empty except blocks
        for handler in node.handlers:
            if not handler.body:
                self.issues.append("Empty except block found")
                self.error_types.add("EmptyExcept")
                
            # Check for bare except
            if handler.type is None:
                self.issues.append("Bare except clause will catch all exceptions, including KeyboardInterrupt and SystemExit")
                self.error_types.add("BareExcept")
                
        self.generic_visit(node)
        
    def visit_BinOp(self, node):
        # Check for potential division by zero
        if isinstance(node.op, ast.Div) and isinstance(node.right, ast.Constant):
            if node.right.value == 0:
                self.issues.append("Division by zero")
                self.error_types.add("DivisionByZero")
                
        self.generic_visit(node)
        
    def visit_Compare(self, node):
        # Check for floating-point equality comparison
        if any(isinstance(op, (ast.Eq, ast.NotEq)) for op in node.ops):
            if (isinstance(node.left, ast.Call) and 
                hasattr(node.left, 'func') and 
                hasattr(node.left.func, 'id') and 
                node.left.func.id == 'float'):
                self.warnings.append("Floating-point equality comparison may be unreliable")
                
        self.generic_visit(node)
        
    def visit_While(self, node):
        # Check for potentially infinite loops
        if isinstance(node.test, ast.Constant) and node.test.value:
            if not any(isinstance(item, (ast.Break, ast.Return)) for item in ast.walk(node)):
                self.issues.append("Potentially infinite loop with no break statement")
                self.error_types.add("InfiniteLoop")
                
        self.generic_visit(node)
        
    def finalize(self):
        # Check for unused imports
        for module in self.imported_modules:
            if module not in self.used_names:
                self.warnings.append(f"Unused import: {module}")
                
        # Check for undefined names
        undefined = self.used_names - self.defined_names - self.imported_modules - set(dir(__builtins__))
        for name in undefined:
            if name != '_' and not name.startswith('__'):
                self.issues.append(f"Undefined name: {name}")
                self.error_types.add("UndefinedName")


class RiskyPatternVisitor(ast.NodeVisitor):
    """AST visitor to find risky code patterns."""
    
    def __init__(self):
        self.patterns = []
        
    def visit_Call(self, node):
        # Check for eval or exec calls
        if (isinstance(node.func, ast.Name) and 
            node.func.id in ('eval', 'exec')):
            self.patterns.append({
                'pattern': f"{node.func.id}_usage",
                'line': getattr(node, 'lineno', 0),
                'message': f"Use of {node.func.id}() is risky",
                'code': f"{node.func.id}(...)"
            })
            
        # Check for potentially dangerous functions
        if (isinstance(node.func, ast.Attribute) and 
            isinstance(node.func.value, ast.Name)):
            module = node.func.value.id
            func = node.func.attr
            
            # Check for OS command execution
            if module == 'os' and func == 'system':
                self.patterns.append({
                    'pattern': 'shell_execution',
                    'line': getattr(node, 'lineno', 0),
                    'message': "OS command execution is a security risk",
                    'code': f"os.system(...)"
                })
                
            # Check for subprocess execution
            elif module == 'subprocess' and func in ('call', 'Popen', 'run'):
                self.patterns.append({
                    'pattern': 'subprocess_execution',
                    'line': getattr(node, 'lineno', 0),
                    'message': "Subprocess execution is a security risk",
                    'code': f"subprocess.{func}(...)"
                })
                
            # Check for pickle usage
            elif module == 'pickle' and func in ('load', 'loads'):
                self.patterns.append({
                    'pattern': 'pickle_usage',
                    'line': getattr(node, 'lineno', 0),
                    'message': "Pickle can execute arbitrary code",
                    'code': f"pickle.{func}(...)"
                })
                
            # Check for insecure randomness
            elif module == 'random' and func in ('random', 'randint', 'choice'):
                self.patterns.append({
                    'pattern': 'insecure_random',
                    'line': getattr(node, 'lineno', 0),
                    'message': "Random module is not cryptographically secure",
                    'code': f"random.{func}(...)"
                })
                
        self.generic_visit(node)
        
    def visit_With(self, node):
        # Check for file operations
        for item in node.items:
            if (isinstance(item.context_expr, ast.Call) and 
                isinstance(item.context_expr.func, ast.Name) and
                item.context_expr.func.id == 'open'):
                
                # Check if file is opened for writing
                if (len(item.context_expr.args) > 1 and 
                    isinstance(item.context_expr.args[1], ast.Str) and
                    ('w' in item.context_expr.args[1].s or 'a' in item.context_expr.args[1].s)):
                    
                    self.patterns.append({
                        'pattern': 'file_write',
                        'line': getattr(node, 'lineno', 0),
                        'message': "File writing operations can be risky",
                        'code': "with open(..., 'w')"
                    })
                    
        self.generic_visit(node)
        
    def visit_Attribute(self, node):
        # Check for potentially dangerous attributes
        if (isinstance(node.value, ast.Name) and 
            node.value.id == '__builtins__'):
            self.patterns.append({
                'pattern': 'builtins_access',
                'line': getattr(node, 'lineno', 0),
                'message': "Direct access to __builtins__ can be risky",
                'code': f"__builtins__.{node.attr}"
            })
            
        self.generic_visit(node)


class ModificationAnalyzer:
    """Analyzes the impact and safety of proposed modifications."""
    
    def __init__(self, safety_constraints: List[SafetyConstraint]):
        self.safety_constraints = safety_constraints
        self.code_analyzer = CodeAnalyzer()
        
    def analyze_proposal(self, proposal: ModificationProposal) -> Dict[str, Any]:
        """
        Analyze a modification proposal for safety and impact.
        
        Args:
            proposal: The modification proposal to analyze.
            
        Returns:
            Dict[str, Any]: Analysis results including safety checks and impact predictions.
        """
        analysis = {
            'safe': True,
            'constraint_results': {},
            'static_analysis_results': {},
            'impact_analysis': {},
            'risks': [],
            'recommendations': []
        }
        
        # Perform static analysis
        static_analysis_results = self._perform_static_analysis(proposal)
        analysis['static_analysis_results'] = static_analysis_results
        
        # Update safety
        if static_analysis_results.get('has_critical_issue', False):
            analysis['safe'] = False
            analysis['risks'].append("Static analysis found critical issues")
            
        # Check each safety constraint
        for i, constraint in enumerate(self.safety_constraints):
            try:
                passed, message = constraint.check(proposal)
                analysis['constraint_results'][f'constraint_{i}'] = {
                    'name': constraint.__class__.__name__,
                    'description': constraint.get_description(),
                    'passed': passed,
                    'message': message
                }
                
                if not passed:
                    analysis['safe'] = False
                    analysis['risks'].append(f"Failed constraint: {message}")
            except Exception as e:
                logger.error(f"Error checking constraint {i}: {str(e)}")
                analysis['constraint_results'][f'constraint_{i}'] = {
                    'name': constraint.__class__.__name__,
                    'description': constraint.get_description(),
                    'error': str(e),
                    'passed': False
                }
                analysis['safe'] = False
                analysis['risks'].append(f"Error checking constraint: {str(e)}")
                
        # Analyze potential impact
        analysis['impact_analysis'] = self._analyze_impact(proposal)
        
        # Generate recommendations
        analysis['recommendations'] = self._generate_recommendations(proposal, analysis)
        
        return analysis
    
    def _perform_static_analysis(self, proposal: ModificationProposal) -> Dict[str, Any]:
        """Perform static analysis on the code changes in a proposal."""
        results = {
            'issues_count': 0,
            'warnings_count': 0,
            'risky_patterns_count': 0,
            'has_syntax_error': False,
            'has_security_issue': False,
            'has_critical_issue': False,
            'file_results': {}
        }
        
        for code_change in proposal.code_changes:
            # Analyze the modified code
            analysis = self.code_analyzer.static_analysis(code_change.modified_code)
            risky_patterns = self.code_analyzer.detect_risky_patterns(code_change.modified_code)
            
            # Check for complexity changes if we have both original and modified code
            complexity_comparison = None
            if code_change.original_code and code_change.modified_code:
                complexity_comparison = self.code_analyzer.compare_complexity(
                    code_change.original_code, 
                    code_change.modified_code
                )
                
            # Update result counts
            results['issues_count'] += analysis.get('error_count', 0)
            results['warnings_count'] += analysis.get('warning_count', 0)
            results['risky_patterns_count'] += len(risky_patterns)
            
            # Update flags
            results['has_syntax_error'] |= analysis.get('has_syntax_error', False)
            results['has_security_issue'] |= analysis.get('has_security_issue', False)
            
            # Determine if there are critical issues
            has_critical_issue = (
                analysis.get('has_syntax_error', False) or 
                analysis.get('has_security_issue', False) or
                any(p.get('pattern') in {'eval_usage', 'exec_usage', 'shell_injection', 
                                         'sql_injection', 'pickle_usage'} 
                   for p in risky_patterns)
            )
            results['has_critical_issue'] |= has_critical_issue
            
            # Store file-specific results
            results['file_results'][code_change.file_path] = {
                'analysis': analysis,
                'risky_patterns': risky_patterns,
                'complexity_comparison': complexity_comparison,
                'has_critical_issue': has_critical_issue
            }
            
        return results
    
    def _analyze_impact(self, proposal: ModificationProposal) -> Dict[str, Any]:
        """Analyze the potential impact of a modification."""
        impact = {}
        
        # Use expected impact from proposal as a baseline
        expected_impact = proposal.expected_impact
        
        # Performance impact
        impact['performance'] = expected_impact.performance_change
        
        # Resource usage impact
        impact['memory_usage'] = expected_impact.memory_usage_change
        impact['cpu_usage'] = expected_impact.cpu_usage_change
        
        # Estimate complexity impact from code changes
        complexity_impact = 0.0
        for file_result in proposal.code_changes:
            if file_result.original_code and file_result.modified_code:
                comparison = self.code_analyzer.compare_complexity(
                    file_result.original_code, 
                    file_result.modified_code
                )
                
                if comparison and 'changes' in comparison:
                    # Average the complexity changes
                    changes = [v for k, v in comparison['changes'].items() 
                              if k.endswith('_change') and isinstance(v, (int, float))]
                    if changes:
                        complexity_impact += sum(changes) / len(changes)
                        
        # Scale and adjust complexity impact
        if proposal.code_changes:
            complexity_impact /= len(proposal.code_changes)
        impact['complexity'] = complexity_impact
        
        # Reliability impact (estimated)
        # More complex changes tend to reduce reliability
        if complexity_impact > 20.0:
            reliability_impact = -0.1 * (complexity_impact / 20.0)
        else:
            reliability_impact = 0.05  # Small improvements usually increase reliability
            
        # Adjust based on explicit reliability change
        reliability_impact += expected_impact.reliability_change
        impact['reliability'] = reliability_impact
        
        # Add capability changes
        impact['capability_addition'] = expected_impact.capability_addition
        impact['capability_removal'] = expected_impact.capability_removal
        
        return impact
    
    def _generate_recommendations(self, 
                                 proposal: ModificationProposal, 
                                 analysis: Dict[str, Any]) -> List[str]:
        """Generate recommendations based on the analysis results."""
        recommendations = []
        
        # If there are critical issues, recommend fixing them
        if analysis['static_analysis_results'].get('has_critical_issue', False):
            recommendations.append("Fix critical issues identified in static analysis before proceeding")
            
        # If complexity increased significantly, recommend simplification
        impact = analysis['impact_analysis']
        if impact.get('complexity', 0) > 20.0:
            recommendations.append("Consider simplifying the implementation to reduce complexity")
            
        # If performance degraded, recommend optimization
        if impact.get('performance', 0) < -5.0:
            recommendations.append("Optimize code to mitigate performance degradation")
            
        # If security issues were found, recommend security review
        if analysis['static_analysis_results'].get('has_security_issue', False):
            recommendations.append("Conduct a thorough security review before deploying this change")
            
        # If many warnings were found, recommend code cleanup
        if analysis['static_analysis_results'].get('warnings_count', 0) > 10:
            recommendations.append("Clean up code to address the numerous warnings")
            
        # If resource usage increased significantly, recommend optimization
        if impact.get('memory_usage', 0) > 10.0 or impact.get('cpu_usage', 0) > 10.0:
            recommendations.append("Optimize resource usage before deploying")
            
        # Add test recommendations if needed
        if proposal.safety_analysis.test_coverage < 0.7:
            recommendations.append("Increase test coverage to reduce implementation risk")
            
        # If recommendations are still empty, add a generic one
        if not recommendations:
            recommendations.append("No specific recommendations - proceed with normal review process")
            
        return recommendations


# ------------------------------------------------------------------------------
# Sandbox Environment
# ------------------------------------------------------------------------------

class SandboxException(Exception):
    """Exception raised when something goes wrong in the sandbox environment."""
    pass


@dataclass
class SandboxResult:
    """Results from running code in the sandbox environment."""
    success: bool = False
    output: str = ""
    error: Optional[str] = None
    duration: float = 0.0
    memory_usage: int = 0  # In bytes
    cpu_usage: float = 0.0  # As a percentage
    return_value: Any = None
    test_results: Dict[str, bool] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            'success': self.success,
            'output': self.output,
            'error': self.error,
            'duration': self.duration,
            'memory_usage': self.memory_usage,
            'cpu_usage': self.cpu_usage,
            'test_results': self.test_results
        }


class ResourceMonitor:
    """Monitors resource usage of a process or function."""
    
    def __init__(self):
        self.peak_memory = 0
        self.peak_cpu = 0.0
        self._start_memory = 0
        self._start_cpu_time = 0
        self._start_time = 0
        self._monitoring = False
        self._process = None
        self._monitor_thread = None
        self._lock = threading.Lock()
        
        # Try to import psutil for better resource monitoring
        try:
            import psutil
            self._psutil = psutil
            self._has_psutil = True
        except ImportError:
            self._has_psutil = False
        
    def start(self, pid: Optional[int] = None):
        """
        Start monitoring resource usage.
        
        Args:
            pid: Process ID to monitor, or None for the current process.
        """
        with self._lock:
            if self._monitoring:
                return
                
            self._monitoring = True
            self._start_time = time.time()
            
            if self._has_psutil:
                if pid is None:
                    self._process = self._psutil.Process()
                else:
                    self._process = self._psutil.Process(pid)
                    
                self._start_memory = self._process.memory_info().rss
                self._start_cpu_time = self._process.cpu_times().user + self._process.cpu_times().system
                
                # Start the monitoring thread
                self._monitor_thread = threading.Thread(target=self._monitor_resources)
                self._monitor_thread.daemon = True
                self._monitor_thread.start()
            else:
                # Fallback to simple resource monitoring
                self._start_memory = self._get_memory_usage_simple()
                self._start_cpu_time = time.process_time()
                
    def stop(self) -> Dict[str, Any]:
        """
        Stop monitoring and return resource usage.
        
        Returns:
            Dict[str, Any]: Resource usage statistics.
        """
        with self._lock:
            if not self._monitoring:
                return {
                    'duration': 0.0,
                    'memory_delta': 0,
                    'peak_memory': 0,
                    'cpu_usage': 0.0,
                    'peak_cpu': 0.0
                }
                
            self._monitoring = False
            
            # Wait for monitoring thread to exit
            if self._monitor_thread and self._monitor_thread.is_alive():
                self._monitor_thread.join(timeout=1.0)
                
            # Calculate resource usage
            duration = time.time() - self._start_time
            
            if self._has_psutil and self._process:
                try:
                    current_memory = self._process.memory_info().rss
                    memory_delta = current_memory - self._start_memory
                    
                    current_cpu_time = self._process.cpu_times().user + self._process.cpu_times().system
                    cpu_time_delta = current_cpu_time - self._start_cpu_time
                    
                    # Calculate CPU usage as percentage of a single core
                    cpu_usage = (cpu_time_delta / duration) * 100.0 if duration > 0 else 0.0
                except (self._psutil.NoSuchProcess, self._psutil.AccessDenied):
                    # Process might have exited
                    memory_delta = 0
                    cpu_usage = 0.0
            else:
                # Fallback calculations
                current_memory = self._get_memory_usage_simple()
                memory_delta = current_memory - self._start_memory
                
                current_cpu_time = time.process_time()
                cpu_time_delta = current_cpu_time - self._start_cpu_time
                
                # Calculate CPU usage as percentage of a single core
                cpu_usage = (cpu_time_delta / duration) * 100.0 if duration > 0 else 0.0
                
            # Reset state
            self._process = None
            self._monitor_thread = None
            
            return {
                'duration': duration,
                'memory_delta': memory_delta,
                'peak_memory': self.peak_memory,
                'cpu_usage': cpu_usage,
                'peak_cpu': self.peak_cpu
            }
            
    def _monitor_resources(self):
        """Monitor resources in a background thread."""
        while self._monitoring and self._process:
            try:
                # Update peak memory usage
                memory_info = self._process.memory_info()
                self.peak_memory = max(self.peak_memory, memory_info.rss)
                
                # Update peak CPU usage
                cpu_percent = self._process.cpu_percent(interval=0.1)
                self.peak_cpu = max(self.peak_cpu, cpu_percent)
                
                # Sleep a bit
                time.sleep(0.1)
            except (self._psutil.NoSuchProcess, self._psutil.AccessDenied):
                # Process might have exited
                break
                
    def _get_memory_usage_simple(self) -> int:
        """Get memory usage using simple methods (fallback)."""
        try:
            import resource
            return resource.getrusage(resource.RUSAGE_SELF).ru_maxrss * 1024
        except ImportError:
            return 0


class SandboxEnvironment:
    """A sandbox environment for testing modifications safely."""
    
    def __init__(self, 
                 timeout_seconds: int = 30, 
                 memory_limit_mb: Optional[int] = None,
                 cpu_limit_percent: Optional[float] = None,
                 temp_dir: Optional[str] = None):
        self.timeout_seconds = timeout_seconds
        self.memory_limit = memory_limit_mb * 1024 * 1024 if memory_limit_mb else None
        self.cpu_limit = cpu_limit_percent
        self.temp_dir = temp_dir or tempfile.mkdtemp(prefix="ultra_sandbox_")
        self.resource_monitor = ResourceMonitor()
        
        # Create temp directory if it doesn't exist
        os.makedirs(self.temp_dir, exist_ok=True)
        
    def __del__(self):
        """Clean up temporary directory on deletion."""
        try:
            if os.path.exists(self.temp_dir):
                shutil.rmtree(self.temp_dir)
        except Exception as e:
            logger.error(f"Error cleaning up sandbox temp directory: {e}")
    
    def run_code(self, code: str, globals_dict: Optional[Dict[str, Any]] = None) -> SandboxResult:
        """
        Run code in the sandbox environment.
        
        Args:
            code: The code to run.
            globals_dict: Optional dictionary of global variables.
            
        Returns:
            SandboxResult: The result of running the code.
        """
        # Create a separate process for sandbox execution
        result_queue = multiprocessing.Queue()
        
        process = multiprocessing.Process(
            target=self._run_code_in_process,
            args=(code, globals_dict, result_queue)
        )
        
        start_time = time.time()
        process.start()
        
        # Wait for the process to finish or timeout
        process.join(timeout=self.timeout_seconds)
        
        if process.is_alive():
            # Process is still running after timeout
            process.terminate()
            process.join(timeout=1.0)
            
            if process.is_alive():
                # Process is still alive, force kill
                process.kill()
                process.join()
                
            return SandboxResult(
                success=False,
                error=f"Execution timed out after {self.timeout_seconds} seconds",
                duration=time.time() - start_time
            )
            
        # Process completed, get the result
        if result_queue.empty():
            return SandboxResult(
                success=False,
                error="No result returned from process",
                duration=time.time() - start_time
            )
            
        try:
            result_dict = result_queue.get(timeout=1.0)
            return SandboxResult(**result_dict)
        except Exception as e:
            return SandboxResult(
                success=False,
                error=f"Error retrieving result: {str(e)}",
                duration=time.time() - start_time
            )
    
    def _run_code_in_process(self, 
                           code: str, 
                           globals_dict: Optional[Dict[str, Any]], 
                           result_queue: multiprocessing.Queue):
        """Run code in a separate process and put the result in the queue."""
        # Set up resource monitoring
        resource_monitor = ResourceMonitor()
        resource_monitor.start()
        
        # Redirect stdout and stderr
        old_stdout = sys.stdout
        old_stderr = sys.stderr
        stdout_buffer = io.StringIO()
        stderr_buffer = io.StringIO()
        sys.stdout = stdout_buffer
        sys.stderr = stderr_buffer
        
        result = {
            'success': False,
            'output': "",
            'error': None,
            'duration': 0.0,
            'memory_usage': 0,
            'cpu_usage': 0.0
        }
        
        try:
            # Prepare globals
            if globals_dict is None:
                globals_dict = {}
            
            # Add a safe subset of builtins
            safe_builtins = {
                name: getattr(__builtins__, name)
                for name in dir(__builtins__)
                if name not in {'eval', 'exec', 'compile', '__import__'}
            }
            exec_globals = {'__builtins__': safe_builtins}
            exec_globals.update(globals_dict)
            
            # Add a safe version of imports
            exec_globals['safe_import'] = self._create_safe_import()
            
            # Execute the code
            start_time = time.time()
            exec(code, exec_globals)
            duration = time.time() - start_time
            
            # Get output
            output = stdout_buffer.getvalue()
            error_output = stderr_buffer.getvalue()
            
            # Combine output
            combined_output = output
            if error_output:
                combined_output += "\n--- stderr ---\n" + error_output
                
            # Get resource usage
            resources = resource_monitor.stop()
            
            result = {
                'success': True,
                'output': combined_output,
                'error': None,
                'duration': duration,
                'memory_usage': resources['peak_memory'],
                'cpu_usage': resources['peak_cpu']
            }
            
        except Exception as e:
            # Get resource usage
            resources = resource_monitor.stop()
            
            # Capture exception info
            error_msg = traceback.format_exc()
            
            result = {
                'success': False,
                'output': stdout_buffer.getvalue(),
                'error': error_msg,
                'duration': time.time() - start_time,
                'memory_usage': resources['peak_memory'],
                'cpu_usage': resources['peak_cpu']
            }
            
        finally:
            # Restore stdout and stderr
            sys.stdout = old_stdout
            sys.stderr = old_stderr
            
            # Put result in queue
            result_queue.put(result)
    
    def _create_safe_import(self) -> Callable:
        """Create a safe version of __import__ that only allows certain modules."""
        # List of allowed modules
        allowed_modules = {
            'math', 'random', 'time', 'datetime', 'calendar', 'collections',
            'functools', 'itertools', 'operator', 're', 'string', 'textwrap',
            'unicodedata', 'copy', 'json', 'statistics', 'array', 'heapq'
        }
        
        def safe_import(name, *args, **kwargs):
            if name in allowed_modules:
                return __import__(name, *args, **kwargs)
            else:
                raise ImportError(f"Import of module '{name}' is not allowed in the sandbox")
                
        return safe_import
    
    def run_tests(self, 
                 proposal: ModificationProposal, 
                 test_cases: Dict[str, Callable]) -> Dict[str, Any]:
        """
        Run tests for a modification proposal in the sandbox.
        
        Args:
            proposal: The modification proposal to test.
            test_cases: Dictionary of test cases to run.
            
        Returns:
            Dict[str, Any]: Test results.
        """
        results = {
            'success': True,
            'test_results': {},
            'error': None,
            'performance': {},
            'memory_usage': {}
        }
        
        # Create a modified module for testing
        try:
            # Create a temporary module with the modifications
            modified_module = self._create_modified_module(proposal)
            
            # Run each test case
            for test_name, test_func in test_cases.items():
                test_result = self._run_test(test_func, modified_module)
                results['test_results'][test_name] = test_result
                
                if not test_result.get('passed', False):
                    results['success'] = False
                    
                # Collect performance data
                results['performance'][test_name] = test_result.get('duration', 0.0)
                results['memory_usage'][test_name] = test_result.get('memory_usage', 0)
                
        except Exception as e:
            results['success'] = False
            results['error'] = f"Error setting up tests: {str(e)}"
            logger.error(f"Error in run_tests: {str(e)}", exc_info=True)
            
        return results
    
    def _create_modified_module(self, proposal: ModificationProposal) -> Any:
        """Create a modified module based on the proposal for testing."""
        # This is a simplified implementation. In a real system, this would involve
        # more sophisticated module creation and isolation.
        
        # Create a mock module
        class MockModule:
            def __init__(self):
                pass
                
            def __getattr__(self, name):
                return lambda *args, **kwargs: True
                
        return MockModule()
    
    def _run_test(self, test_func: Callable, module: Any) -> Dict[str, Any]:
        """Run a single test in the sandbox."""
        # Set up resource monitoring
        resource_monitor = ResourceMonitor()
        resource_monitor.start()
        
        result = {
            'passed': False,
            'error': None,
            'duration': 0,
            'memory_usage': 0,
            'output': ""
        }
        
        # Redirect stdout
        old_stdout = sys.stdout
        stdout_buffer = io.StringIO()
        sys.stdout = stdout_buffer
        
        try:
            # Run the test
            start_time = time.time()
            test_passed = test_func(module)
            result['duration'] = time.time() - start_time
            
            # Get resource usage
            resources = resource_monitor.stop()
            result['memory_usage'] = resources['peak_memory']
            
            # Store result
            result['passed'] = bool(test_passed)
            result['output'] = stdout_buffer.getvalue()
            
        except Exception as e:
            # Get resource usage
            resources = resource_monitor.stop()
            
            result['error'] = str(e)
            result['memory_usage'] = resources['peak_memory']
            result['output'] = stdout_buffer.getvalue()
            
        finally:
            # Restore stdout
            sys.stdout = old_stdout
            
        return result


# ------------------------------------------------------------------------------
# Deployment Manager
# ------------------------------------------------------------------------------

class DeploymentStrategy(Enum):
    """Strategies for deploying modifications."""
    IMMEDIATE = "immediate"      # Apply changes immediately
    STAGED = "staged"            # Apply changes in stages
    CANARY = "canary"            # Apply to a subset of instances first
    BLUE_GREEN = "blue_green"    # Maintain parallel versions
    SHADOW = "shadow"            # Run in parallel without affecting output


@dataclass
class DeploymentResult:
    """Result of a deployment operation."""
    success: bool = False
    error: Optional[str] = None
    backup_path: Optional[str] = None
    deployment_time: float = 0.0
    rollback_triggered: bool = False
    verification_passed: bool = False
    strategy: DeploymentStrategy = DeploymentStrategy.IMMEDIATE
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            'success': self.success,
            'error': self.error,
            'backup_path': self.backup_path,
            'deployment_time': self.deployment_time,
            'rollback_triggered': self.rollback_triggered,
            'verification_passed': self.verification_passed,
            'strategy': self.strategy.value
        }


class DeploymentManager:
    """Manages the deployment of modifications to the system."""
    
    def __init__(self, 
                 backup_dir: str = "./backups",
                 max_deployment_attempts: int = 3,
                 rollback_timeout: int = 60,
                 verification_tests: Dict[str, Callable] = None):
        self.backup_dir = backup_dir
        self.max_deployment_attempts = max_deployment_attempts
        self.rollback_timeout = rollback_timeout
        self.verification_tests = verification_tests or {}
        self.deployment_history = []
        
        # Ensure backup directory exists
        os.makedirs(backup_dir, exist_ok=True)
        
    def deploy_modification(self, 
                           proposal: ModificationProposal, 
                           strategy: DeploymentStrategy = DeploymentStrategy.IMMEDIATE) -> DeploymentResult:
        """
        Deploy a modification to the system.
        
        Args:
            proposal: The modification proposal to deploy.
            strategy: The deployment strategy to use.
            
        Returns:
            DeploymentResult: Deployment result.
        """
        result = DeploymentResult(strategy=strategy)
        start_time = time.time()
        
        # Create backup
        try:
            backup_path = self._create_backup(proposal)
            result.backup_path = backup_path
        except Exception as e:
            result.error = f"Failed to create backup: {str(e)}"
            self._record_deployment(proposal, result)
            return result
        
        # Get target module
        target_module_name = proposal.target.split('.')[0] if '.' in proposal.target else proposal.target
        try:
            target_module = importlib.import_module(target_module_name)
        except ImportError as e:
            result.error = f"Failed to import target module {target_module_name}: {str(e)}"
            self._record_deployment(proposal, result)
            return result
        
        # Try to deploy the modification
        attempt = 0
        while attempt < self.max_deployment_attempts:
            attempt += 1
            try:
                # Apply the modification
                self._apply_modification(proposal, target_module, strategy)
                result.deployment_time = time.time() - start_time
                
                # Verify the deployment
                verification_result = self._verify_deployment(proposal, target_module)
                result.verification_passed = verification_result['success']
                
                if result.verification_passed:
                    result.success = True
                    break
                else:
                    # Deployment verification failed, trigger rollback
                    logger.warning(f"Deployment verification failed on attempt {attempt}. "
                                  f"Rolling back modification {proposal.id}.")
                    self._rollback(backup_path, target_module_name)
                    result.rollback_triggered = True
                    result.error = f"Deployment verification failed: {verification_result['error']}"
                    
            except Exception as e:
                # Deployment failed, trigger rollback
                logger.error(f"Deployment failed on attempt {attempt}: {str(e)}. "
                            f"Rolling back modification {proposal.id}.")
                self._rollback(backup_path, target_module_name)
                result.rollback_triggered = True
                result.error = f"Deployment failed on attempt {attempt}: {str(e)}"
                
                # If this is the last attempt, record the error
                if attempt == self.max_deployment_attempts:
                    break
                
                # Otherwise, wait a bit and try again
                time.sleep(2)
        
        # Record deployment in history
        self._record_deployment(proposal, result)
        
        return result
    
    def _create_backup(self, proposal: ModificationProposal) -> str:
        """Create a backup of the target module before modification."""
        target_parts = proposal.target.split('.')
        target_module_name = target_parts[0]
        timestamp = int(time.time())
        backup_path = os.path.join(self.backup_dir, f"{target_module_name.replace('.', '_')}_{timestamp}.bak")
        
        try:
            # Find the module file
            spec = importlib.util.find_spec(target_module_name)
            if spec is None or spec.origin is None:
                raise ImportError(f"Could not find module {target_module_name}")
                
            source_path = spec.origin
            
            # Copy the file
            shutil.copy2(source_path, backup_path)
            
            # Also create backup of any specific files targeted by code changes
            file_backups = {}
            for change in proposal.code_changes:
                if os.path.exists(change.file_path) and change.file_path != source_path:
                    file_backup = f"{change.file_path.replace('/', '_').replace('.', '_')}_{timestamp}.bak"
                    file_backup_path = os.path.join(self.backup_dir, file_backup)
                    shutil.copy2(change.file_path, file_backup_path)
                    file_backups[change.file_path] = file_backup_path
            
            logger.info(f"Created backup of {target_module_name} at {backup_path}")
            
            # If we have additional file backups, create a manifest
            if file_backups:
                manifest_path = f"{backup_path}.manifest"
                with open(manifest_path, 'w') as f:
                    json.dump(file_backups, f)
                
            return backup_path
            
        except Exception as e:
            logger.error(f"Failed to create backup of {target_module_name}: {str(e)}")
            raise
    
    def _apply_modification(self, 
                          proposal: ModificationProposal, 
                          target_module: Any, 
                          strategy: DeploymentStrategy):
        """Apply a modification to the target module."""
        logger.info(f"Applying modification {proposal.id} to {proposal.target} using {strategy.value} strategy")
        
        try:
            # Apply code changes
            for change in proposal.code_changes:
                self._apply_code_change(change)
                
            # Reload the module
            if strategy != DeploymentStrategy.SHADOW:
                importlib.reload(target_module)
                
                # If target is a submodule or attribute, navigate to it
                target_parts = proposal.target.split('.')
                if len(target_parts) > 1:
                    target_attr = target_module
                    for part in target_parts[1:]:
                        target_attr = getattr(target_attr, part)
            
            logger.info(f"Successfully applied modification {proposal.id}")
            
        except Exception as e:
            logger.error(f"Error applying modification {proposal.id}: {str(e)}", exc_info=True)
            raise
    
    def _apply_code_change(self, change: CodeChange):
        """Apply a specific code change to a file."""
        if not os.path.exists(change.file_path) and change.change_type != "add":
            raise ValueError(f"File not found: {change.file_path}")
            
        # Handle different change types
        if change.change_type == "add":
            # Create the file
            os.makedirs(os.path.dirname(change.file_path), exist_ok=True)
            with open(change.file_path, 'w') as f:
                f.write(change.modified_code)
                
        elif change.change_type == "delete":
            # Delete the file
            os.remove(change.file_path)
            
        elif change.change_type == "modify":
            # Read the current file content
            with open(change.file_path, 'r') as f:
                current_content = f.read()
                
            # If we have line ranges, do a more targeted modification
            if change.line_start > 0 and change.line_end > 0:
                lines = current_content.splitlines()
                
                # Ensure line numbers are within bounds
                if change.line_start <= len(lines) and change.line_end <= len(lines):
                    # Replace the specified lines
                    new_lines = lines[:change.line_start - 1]
                    new_lines.extend(change.modified_code.splitlines())
                    new_lines.extend(lines[change.line_end:])
                    
                    modified_content = '\n'.join(new_lines)
                else:
                    # Line numbers out of bounds, use the full modified code
                    modified_content = change.modified_code
            else:
                # No line ranges, use the full modified code
                modified_content = change.modified_code
                
            # Write the modified content back to the file
            with open(change.file_path, 'w') as f:
                f.write(modified_content)
                
        else:
            raise ValueError(f"Unknown change type: {change.change_type}")
    
    def _verify_deployment(self, 
                         proposal: ModificationProposal, 
                         target_module: Any) -> Dict[str, Any]:
        """Verify that a modification was deployed successfully."""
        verification_result = {
            'success': True,
            'error': None,
            'test_results': {}
        }
        
        # Run verification tests if provided
        if not self.verification_tests:
            logger.warning("No verification tests provided")
            return verification_result
            
        # Get the verification tests for this target
        target_parts = proposal.target.split('.')
        target_module_name = target_parts[0]
        
        target_tests = {
            name: test for name, test in self.verification_tests.items()
            if name.startswith(f"verify_{target_module_name}")
        }
        
        if not target_tests:
            logger.warning(f"No verification tests found for {target_module_name}")
            return verification_result
            
        # Run each verification test
        for test_name, test_func in target_tests.items():
            try:
                test_result = test_func(target_module)
                verification_result['test_results'][test_name] = {
                    'success': bool(test_result),
                    'error': None
                }
                
                if not test_result:
                    verification_result['success'] = False
                    verification_result['error'] = f"Test {test_name} failed"
                    
            except Exception as e:
                verification_result['test_results'][test_name] = {
                    'success': False,
                    'error': str(e)
                }
                verification_result['success'] = False
                verification_result['error'] = f"Test {test_name} raised an exception: {str(e)}"
                
        return verification_result
    
    def _rollback(self, backup_path: str, target_module_name: str):
        """Roll back a modification using a backup."""
        logger.info(f"Rolling back modification to {target_module_name} using backup {backup_path}")
        
        try:
            # Find the module file
            spec = importlib.util.find_spec(target_module_name)
            if spec is None or spec.origin is None:
                raise ImportError(f"Could not find module {target_module_name}")
                
            target_path = spec.origin
            
            # Restore the module backup
            shutil.copy2(backup_path, target_path)
            
            # Check for additional file backups in manifest
            manifest_path = f"{backup_path}.manifest"
            if os.path.exists(manifest_path):
                with open(manifest_path, 'r') as f:
                    file_backups = json.load(f)
                    
                # Restore each additional file
                for file_path, backup_file in file_backups.items():
                    if os.path.exists(backup_file):
                        shutil.copy2(backup_file, file_path)
                        logger.info(f"Restored backup of {file_path} from {backup_file}")
            
            # Reload the module
            try:
                module = importlib.import_module(target_module_name)
                importlib.reload(module)
                logger.info(f"Successfully reloaded module {target_module_name}")
            except Exception as e:
                logger.error(f"Error reloading module {target_module_name}: {str(e)}")
            
            logger.info(f"Successfully rolled back modification to {target_module_name}")
            
        except Exception as e:
            logger.error(f"Failed to roll back modification to {target_module_name}: {str(e)}")
            raise
    
    def _record_deployment(self, proposal: ModificationProposal, result: DeploymentResult):
        """Record a deployment in the history."""
        self.deployment_history.append({
            'proposal_id': proposal.id,
            'target': proposal.target,
            'timestamp': time.time(),
            'result': result.to_dict()
        })
        
        # Update proposal status
        if result.success:
            proposal.status = "deployed"
        elif result.rollback_triggered:
            proposal.status = "failed"
        
    def get_deployment_history(self) -> List[Dict[str, Any]]:
        """Get the deployment history."""
        return self.deployment_history.copy()


# ------------------------------------------------------------------------------
# Self-Modification Protocols
# ------------------------------------------------------------------------------

class SelfModificationProtocols:
    """
    Implements safe, controlled mechanisms for the system to update its own code and architecture.
    """
    
    def __init__(self, 
                 safety_constraints: List[SafetyConstraint],
                 sandbox_environment: SandboxEnvironment,
                 deployment_manager: DeploymentManager,
                 modification_analyzer: ModificationAnalyzer,
                 storage_dir: str = "./modifications"):
        self.safety_constraints = safety_constraints
        self.sandbox_environment = sandbox_environment
        self.deployment_manager = deployment_manager
        self.modification_analyzer = modification_analyzer
        self.storage_dir = storage_dir
        
        # Create storage directory
        os.makedirs(storage_dir, exist_ok=True)
        
        # Create subdirectories
        os.makedirs(os.path.join(storage_dir, "proposed"), exist_ok=True)
        os.makedirs(os.path.join(storage_dir, "approved"), exist_ok=True)
        os.makedirs(os.path.join(storage_dir, "rejected"), exist_ok=True)
        os.makedirs(os.path.join(storage_dir, "deployed"), exist_ok=True)
        
        # Load existing proposals
        self.proposals = self._load_proposals()
        self.dependency_resolver = DependencyResolver()
        
        # Initialize dependency resolver
        for proposal in self.proposals.values():
            self.dependency_resolver.add_proposal(proposal)
        
    def propose_modification(self, 
                            type: ModificationType,
                            target: str,
                            description: str,
                            code_changes: List[CodeChange] = None,
                            expected_impact: ModificationImpact = None,
                            safety_analysis: SafetyAnalysis = None,
                            justification: str = "",
                            priority: float = 0.5,
                            dependencies: List[str] = None) -> ModificationProposal:
        """
        Propose a modification to the system.
        
        Args:
            type: The type of modification.
            target: The target module or component.
            description: A description of the modification.
            code_changes: List of code changes to make.
            expected_impact: The expected impact of the modification.
            safety_analysis: Initial safety analysis.
            justification: Justification for the modification.
            priority: The priority of the modification (0.0 to 1.0).
            dependencies: List of proposal IDs that this modification depends on.
            
        Returns:
            ModificationProposal: The created proposal.
        """
        code_changes = code_changes or []
        expected_impact = expected_impact or ModificationImpact()
        safety_analysis = safety_analysis or SafetyAnalysis()
        dependencies = dependencies or []
        
        # Create proposal
        proposal = ModificationProposal(
            type=type,
            target=target,
            description=description,
            justification=justification,
            code_changes=code_changes,
            expected_impact=expected_impact,
            safety_analysis=safety_analysis,
            priority=priority,
            dependencies=dependencies
        )
        
        # Add to proposals
        self.proposals[proposal.id] = proposal
        
        # Add to dependency resolver
        self.dependency_resolver.add_proposal(proposal)
        
        # Save the proposal
        self._save_proposal(proposal)
        
        logger.info(f"New modification proposal created: {proposal.id} - {description}")
        
        return proposal
    
    def evaluate_proposal(self, 
                         proposal: ModificationProposal, 
                         test_cases: Dict[str, Callable]) -> Dict[str, Any]:
        """
        Evaluate a modification proposal for safety and impact.
        
        Args:
            proposal: The modification proposal to evaluate.
            test_cases: Test cases to run in the sandbox.
            
        Returns:
            Dict[str, Any]: Evaluation results.
        """
        evaluation = {
            'proposal_id': proposal.id,
            'timestamp': time.time(),
            'analysis': None,
            'sandbox_results': None,
            'approved': False,
            'reasons': []
        }
        
        # Check for circular dependencies
        if proposal.has_circular_dependencies(self.proposals):
            evaluation['reasons'].append("Circular dependencies detected")
            proposal.status = "rejected"
            self._save_proposal(proposal)
            return evaluation
        
        # Perform safety analysis
        analysis = self.modification_analyzer.analyze_proposal(proposal)
        evaluation['analysis'] = analysis
        
        # If analysis shows it's unsafe, reject immediately
        if not analysis['safe']:
            evaluation['reasons'].append("Failed safety analysis")
            proposal.status = "rejected"
            self._save_proposal(proposal)
            return evaluation
        
        # Run sandbox tests
        sandbox_results = self.sandbox_environment.run_tests(proposal, test_cases)
        evaluation['sandbox_results'] = sandbox_results
        
        # If sandbox tests failed, reject
        if not sandbox_results['success']:
            evaluation['reasons'].append("Failed sandbox tests")
            proposal.status = "rejected"
            self._save_proposal(proposal)
            return evaluation
        
        # Check if the modification is worth the risk (cost-benefit analysis)
        if not self._is_worth_the_risk(proposal, analysis, sandbox_results):
            evaluation['reasons'].append("Cost-benefit analysis indicates modification is not worth the risk")
            proposal.status = "rejected"
            self._save_proposal(proposal)
            return evaluation
        
        # If all checks pass, approve the proposal
        evaluation['approved'] = True
        proposal.status = "approved"
        self._save_proposal(proposal)
        
        return evaluation
    
    def _is_worth_the_risk(self, 
                          proposal: ModificationProposal, 
                          analysis: Dict[str, Any], 
                          sandbox_results: Dict[str, Any]) -> bool:
        """Determine if a modification is worth the risk."""
        # Calculate expected benefit
        expected_impact = proposal.expected_impact
        performance_impact = expected_impact.performance_change
        reliability_impact = expected_impact.reliability_change
        
        # Adjust benefit based on sandbox results
        if sandbox_results['success']:
            benefit_multiplier = 1.2  # Increase benefit if tests pass
        else:
            benefit_multiplier = 0.8  # Decrease benefit if tests fail
            
        expected_benefit = (performance_impact + reliability_impact) * benefit_multiplier
        
        # Calculate expected risk
        complexity_impact = analysis['impact_analysis'].get('complexity', 0.0)
        memory_impact = expected_impact.memory_usage_change
        cpu_impact = expected_impact.cpu_usage_change
        
        resource_impact = (memory_impact + cpu_impact) / 2
        risky_patterns = sum(len(file_result.get('risky_patterns', [])) 
                            for file_result in analysis['static_analysis_results'].get('file_results', {}).values())
        
        expected_risk = complexity_impact + resource_impact + 0.1 * risky_patterns
        
        # Factor in priority
        priority_factor = 1.0 + (proposal.priority - 0.5)  # Range: 0.5 to 1.5
        benefit_to_risk_ratio = (expected_benefit * priority_factor) / max(0.1, expected_risk)
        
        # A modification is worth the risk if the adjusted benefit outweighs the risk
        return benefit_to_risk_ratio > 1.2  # Threshold for acceptable risk
    
    def deploy_proposal(self, proposal: ModificationProposal) -> Dict[str, Any]:
        """
        Deploy an approved modification proposal.
        
        Args:
            proposal: The modification proposal to deploy.
            
        Returns:
            Dict[str, Any]: Deployment result.
        """
        # Check if the proposal is approved
        if proposal.status != "approved":
            return {
                'success': False,
                'error': f"Proposal not approved for deployment. Current status: {proposal.status}"
            }
        
        # Check dependencies
        try:
            execution_order = self.dependency_resolver.get_execution_order([proposal.id])
            
            # If the proposal is not first in the execution order, its dependencies haven't been deployed
            if execution_order[0] != proposal.id:
                missing_deps = [dep for dep in proposal.dependencies 
                               if self.proposals.get(dep, ModificationProposal).status != "deployed"]
                
                return {
                    'success': False,
                    'error': f"Dependencies not yet deployed: {', '.join(missing_deps)}"
                }
        except ValueError as e:
            return {
                'success': False,
                'error': f"Dependency resolution error: {str(e)}"
            }
        
        # Deploy the modification
        result = self.deployment_manager.deploy_modification(proposal)
        
        # Update proposal status based on deployment result
        if result.success:
            proposal.status = "deployed"
        else:
            proposal.status = "failed"
            
        self._save_proposal(proposal)
        
        return result.to_dict()
    
    def get_proposals(self, status: Optional[str] = None) -> List[ModificationProposal]:
        """
        Get modification proposals, optionally filtered by status.
        
        Args:
            status: Optional status filter ('all', 'proposed', 'approved', 'rejected', 'deployed', 'failed').
            
        Returns:
            List[ModificationProposal]: List of proposals.
        """
        if status is None or status == 'all':
            return list(self.proposals.values())
            
        return [p for p in self.proposals.values() if p.status == status]
    
    def get_proposal_by_id(self, proposal_id: str) -> Optional[ModificationProposal]:
        """Get a proposal by ID."""
        return self.proposals.get(proposal_id)
    
    def get_modification_impact(self, proposal_id: str) -> Dict[str, Any]:
        """
        Get detailed impact analysis for a modification proposal.
        
        Args:
            proposal_id: The ID of the proposal to analyze.
            
        Returns:
            Dict[str, Any]: Detailed impact analysis.
        """
        proposal = self.get_proposal_by_id(proposal_id)
        if not proposal:
            return {'error': f"Proposal not found: {proposal_id}"}
            
        # Analyze the proposal
        analysis = self.modification_analyzer.analyze_proposal(proposal)
        
        # Combine with expected impact
        impact = {
            'analysis': analysis['impact_analysis'],
            'expected_impact': proposal.expected_impact.to_dict(),
            'risks': analysis['risks'],
            'recommendations': analysis['recommendations']
        }
        
        return impact
    
    def update_proposal(self, proposal_id: str, updates: Dict[str, Any]) -> Optional[ModificationProposal]:
        """
        Update an existing modification proposal.
        
        Args:
            proposal_id: The ID of the proposal to update.
            updates: Dictionary of fields to update.
            
        Returns:
            Optional[ModificationProposal]: The updated proposal, or None if not found.
        """
        proposal = self.get_proposal_by_id(proposal_id)
        if not proposal:
            return None
            
        # Only allow updates for proposals that aren't yet deployed
        if proposal.status in ["deployed", "failed"]:
            return None
            
        # Update allowed fields
        if 'description' in updates:
            proposal.description = updates['description']
            
        if 'justification' in updates:
            proposal.justification = updates['justification']
            
        if 'code_changes' in updates and isinstance(updates['code_changes'], list):
            proposal.code_changes = [CodeChange.from_dict(cc) if isinstance(cc, dict) else cc 
                                    for cc in updates['code_changes']]
            
        if 'expected_impact' in updates:
            if isinstance(updates['expected_impact'], dict):
                proposal.expected_impact = ModificationImpact.from_dict(updates['expected_impact'])
            elif isinstance(updates['expected_impact'], ModificationImpact):
                proposal.expected_impact = updates['expected_impact']
                
        if 'priority' in updates and isinstance(updates['priority'], (int, float)):
            proposal.priority = float(updates['priority'])
            
        if 'dependencies' in updates and isinstance(updates['dependencies'], list):
            proposal.dependencies = updates['dependencies']
            
        # Reset status to proposed if we're modifying a rejected proposal
        if proposal.status == "rejected":
            proposal.status = "proposed"
            
        # Save the updated proposal
        self._save_proposal(proposal)
        
        # Update dependency resolver
        self.dependency_resolver.add_proposal(proposal)
        
        return proposal
    
    def delete_proposal(self, proposal_id: str) -> bool:
        """
        Delete a modification proposal.
        
        Args:
            proposal_id: The ID of the proposal to delete.
            
        Returns:
            bool: True if deleted, False if not found or not deletable.
        """
        proposal = self.get_proposal_by_id(proposal_id)
        if not proposal:
            return False
            
        # Only allow deletion for proposals that aren't yet deployed
        if proposal.status in ["deployed", "failed"]:
            return False
            
        # Delete the proposal file
        status_dir = self._get_status_dir(proposal.status)
        proposal_path = os.path.join(status_dir, f"{proposal_id}.json")
        
        if os.path.exists(proposal_path):
            os.remove(proposal_path)
            
        # Remove from proposals dictionary
        if proposal_id in self.proposals:
            del self.proposals[proposal_id]
            
        # Remove from dependency resolver
        self.dependency_resolver.remove_proposal(proposal_id)
        
        return True
    
    def _save_proposal(self, proposal: ModificationProposal):
        """Save a proposal to disk."""
        # Determine the appropriate directory
        status_dir = self._get_status_dir(proposal.status)
        
        # Create the proposal file path
        proposal_path = os.path.join(status_dir, f"{proposal.id}.json")
        
        # Serialize the proposal
        proposal_dict = proposal.to_dict()
        
        # Write to file
        with open(proposal_path, 'w') as f:
            json.dump(proposal_dict, f, indent=2)
    
    def _load_proposals(self) -> Dict[str, ModificationProposal]:
        """Load all proposals from disk."""
        proposals = {}
        
        # Load from each status directory
        for status in ["proposed", "approved", "rejected", "deployed"]:
            status_dir = os.path.join(self.storage_dir, status)
            
            if not os.path.exists(status_dir):
                continue
                
            # Load each proposal file
            for filename in os.listdir(status_dir):
                if not filename.endswith('.json'):
                    continue
                    
                proposal_path = os.path.join(status_dir, filename)
                
                try:
                    with open(proposal_path, 'r') as f:
                        proposal_dict = json.load(f)
                        
                    # Create proposal object
                    proposal = ModificationProposal.from_dict(proposal_dict)
                    
                    # Set status based on directory
                    proposal.status = status
                    
                    # Add to proposals
                    proposals[proposal.id] = proposal
                except Exception as e:
                    logger.error(f"Error loading proposal from {proposal_path}: {str(e)}")
        
        return proposals
    
    def _get_status_dir(self, status: str) -> str:
        """Get the directory for a given proposal status."""
        # Map status to directory
        status_map = {
            "proposed": "proposed",
            "approved": "approved",
            "rejected": "rejected",
            "deployed": "deployed",
            "failed": "deployed"  # Failed deployments are still in deployed dir
        }
        
        # Get the mapped status or use proposed as default
        mapped_status = status_map.get(status, "proposed")
        
        return os.path.join(self.storage_dir, mapped_status)
    
    def generate_modification_report(self) -> Dict[str, Any]:
        """
        Generate a report of all modification proposals.
        
        Returns:
            Dict[str, Any]: Report data.
        """
        status_counts = {
            "proposed": 0,
            "approved": 0,
            "rejected": 0,
            "deployed": 0,
            "failed": 0
        }
        
        type_counts = {mod_type.name: 0 for mod_type in ModificationType}
        
        recent_proposals = []
        high_priority_proposals = []
        
        # Collect data
        for proposal in self.proposals.values():
            # Update status counts
            status_counts[proposal.status] = status_counts.get(proposal.status, 0) + 1
            
            # Update type counts
            type_counts[proposal.type.name] = type_counts.get(proposal.type.name, 0) + 1
            
            # Add to recent proposals if in the last 7 days
            if time.time() - proposal.created_at < 7 * 24 * 60 * 60:
                recent_proposals.append({
                    'id': proposal.id,
                    'type': proposal.type.name,
                    'target': proposal.target,
                    'description': proposal.description,
                    'status': proposal.status,
                    'created_at': proposal.created_at
                })
                
            # Add to high priority proposals if priority >= 0.7
            if proposal.priority >= 0.7:
                high_priority_proposals.append({
                    'id': proposal.id,
                    'type': proposal.type.name,
                    'target': proposal.target,
                    'description': proposal.description,
                    'status': proposal.status,
                    'priority': proposal.priority
                })
        
        # Generate report
        report = {
            'timestamp': time.time(),
            'total_proposals': len(self.proposals),
            'status_counts': status_counts,
            'type_counts': type_counts,
            'recent_proposals': recent_proposals,
            'high_priority_proposals': high_priority_proposals,
            'deployment_history': self.deployment_manager.get_deployment_history()
        }
        
        return report


# Import for io module that was used in the SandboxEnvironment
import io
import traceback
import uuid