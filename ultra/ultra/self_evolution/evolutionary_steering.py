#!/usr/bin/env python3
"""
ULTRA: Ultimate Learning & Thought Reasoning Architecture
Evolutionary Steering Component Implementation

This module implements the Evolutionary Steering component of the Self-Evolution System,
which guides the evolution process toward desirable properties through fitness functions,
constraints, and adaptation heuristics. It ensures that self-modification and architecture
changes lead to improvements in system capabilities while respecting important constraints.

The evolutionary steering subsystem implements a form of guided, constrained optimization
where the system's evolution is steered toward greater capability, robustness, and alignment
with specified objectives.

Key components:
- Fitness Functions: Define criteria for evaluating the system's fitness
- Evolutionary Constraints: Enforce limits on the evolution process
- Adaptation Heuristics: Generate potential adaptations based on system state
- Evolutionary Steering: Coordinates the evaluation, constraint checking, and adaptation
"""

import os
import sys
import time
import math
import logging
import json
import copy
import uuid
import numpy as np
from typing import Dict, List, Tuple, Any, Callable, Optional, Union, Set
from dataclasses import dataclass, field
from enum import Enum, auto
from abc import ABC, abstractmethod
from collections import defaultdict, deque
from datetime import datetime

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Add parent directory to sys.path to allow for relative imports
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if parent_dir not in sys.path:
    sys.path.append(parent_dir)

# Import other ULTRA components as needed
try:
    from ultra.utils.config import Configuration
    from ultra.utils.monitoring import PerformanceMonitor
    from ultra.meta_cognitive.meta_learning import MetaLearningController
    from ultra.knowledge_management.episodic_knowledge import EpisodicKnowledgeBase
except ImportError:
    logger.warning("Some ULTRA components could not be imported. Evolutionary Steering may have limited functionality.")

# ------------------------------------------------------------------------------
# Fitness Functions
# ------------------------------------------------------------------------------

class FitnessFunction(ABC):
    """Base class for fitness functions used in evolutionary steering."""
    
    @abstractmethod
    def evaluate(self, system_state: Dict[str, Any]) -> float:
        """
        Evaluate the fitness of the current system state.
        
        Args:
            system_state: The current state of the system.
            
        Returns:
            float: A fitness score (higher is better).
        """
        pass
    
    @abstractmethod
    def get_description(self) -> str:
        """Get a description of the fitness function."""
        pass
    
    def get_importance_for_task(self, task_type: str) -> float:
        """
        Determine the importance of this fitness function for a specific task type.
        
        Args:
            task_type: The type of task (e.g., 'classification', 'generation').
            
        Returns:
            float: Importance weight (0.0 to 1.0).
        """
        # Default implementation returns medium importance
        return 0.5


class PerformanceFitness(FitnessFunction):
    """Fitness function based on system performance."""
    
    def __init__(self, 
                target_improvement: float = 0.1, 
                throughput_weight: float = 0.4,
                latency_weight: float = 0.4,
                error_rate_weight: float = 0.2):
        """
        Initialize the performance fitness function.
        
        Args:
            target_improvement: Target improvement ratio.
            throughput_weight: Weight for throughput in the overall score.
            latency_weight: Weight for latency in the overall score.
            error_rate_weight: Weight for error rate in the overall score.
        """
        self.target_improvement = target_improvement
        self.throughput_weight = throughput_weight
        self.latency_weight = latency_weight
        self.error_rate_weight = error_rate_weight
        self.baseline_performance = None
        self.history = []
        
    def evaluate(self, system_state: Dict[str, Any]) -> float:
        """
        Evaluate system performance fitness.
        
        Args:
            system_state: The current state of the system, including performance metrics.
            
        Returns:
            float: A fitness score (0.0 to 1.0).
        """
        current_performance = system_state.get('performance', {})
        
        if not current_performance:
            logger.warning("No performance data available in system state")
            return 0.0
            
        # Extract performance metrics
        throughput = current_performance.get('throughput', 0.0)
        latency = current_performance.get('latency', float('inf'))
        error_rate = current_performance.get('error_rate', 1.0)
        
        # Also check for task-specific metrics if available
        task_specific = current_performance.get('task_specific', {})
        accuracy = task_specific.get('accuracy', 0.0)
        f1_score = task_specific.get('f1_score', 0.0)
        
        # Initialize baseline if not set
        if self.baseline_performance is None:
            self.baseline_performance = {
                'throughput': throughput,
                'latency': latency,
                'error_rate': error_rate,
                'accuracy': accuracy,
                'f1_score': f1_score,
                'timestamp': time.time()
            }
            logger.info(f"Initialized performance baseline: throughput={throughput:.2f}, latency={latency:.2f}ms, error_rate={error_rate:.4f}")
            # Return a medium score for baseline
            return 0.5
            
        # Calculate improvements
        throughput_improvement = throughput / max(1e-10, self.baseline_performance['throughput']) - 1.0
        latency_improvement = 1.0 - latency / max(1e-10, self.baseline_performance['latency'])
        error_rate_improvement = 1.0 - error_rate / max(1e-10, self.baseline_performance['error_rate'])
        
        # Calculate task-specific improvements if applicable
        task_improvement = 0.0
        if accuracy > 0 and self.baseline_performance['accuracy'] > 0:
            accuracy_improvement = accuracy / self.baseline_performance['accuracy'] - 1.0
            f1_improvement = f1_score / max(1e-10, self.baseline_performance['f1_score']) - 1.0
            task_improvement = (accuracy_improvement + f1_improvement) / 2.0
        
        # Combine improvements using weights
        overall_improvement = (
            self.throughput_weight * throughput_improvement +
            self.latency_weight * latency_improvement +
            self.error_rate_weight * error_rate_improvement
        )
        
        # Add task improvement if available
        if task_improvement != 0.0:
            overall_improvement = 0.7 * overall_improvement + 0.3 * task_improvement
        
        # Normalize to [0, 1] relative to target improvement
        normalized_score = min(1.0, max(0.0, overall_improvement / self.target_improvement))
        
        # Update history
        self.history.append({
            'timestamp': time.time(),
            'throughput': throughput,
            'latency': latency,
            'error_rate': error_rate,
            'improvement': overall_improvement,
            'normalized_score': normalized_score
        })
        
        # Log significant changes
        if abs(overall_improvement) > 0.05:  # 5% change threshold
            logger.info(f"Significant performance change detected: {overall_improvement:.2%}")
            
        return normalized_score
    
    def update_baseline(self, system_state: Optional[Dict[str, Any]] = None):
        """
        Update the performance baseline.
        
        Args:
            system_state: Optional system state to use for the new baseline.
                          If None, uses the most recent evaluation.
        """
        if system_state is not None:
            current_performance = system_state.get('performance', {})
            if current_performance:
                self.baseline_performance = {
                    'throughput': current_performance.get('throughput', 0.0),
                    'latency': current_performance.get('latency', float('inf')),
                    'error_rate': current_performance.get('error_rate', 1.0),
                    'accuracy': current_performance.get('task_specific', {}).get('accuracy', 0.0),
                    'f1_score': current_performance.get('task_specific', {}).get('f1_score', 0.0),
                    'timestamp': time.time()
                }
        elif self.history:
            # Use the most recent evaluation
            latest = self.history[-1]
            self.baseline_performance = {
                'throughput': latest['throughput'],
                'latency': latest['latency'],
                'error_rate': latest['error_rate'],
                'accuracy': self.baseline_performance.get('accuracy', 0.0),  # Maintain previous values if not in history
                'f1_score': self.baseline_performance.get('f1_score', 0.0),
                'timestamp': latest['timestamp']
            }
            
        logger.info(f"Updated performance baseline: throughput={self.baseline_performance['throughput']:.2f}, "
                   f"latency={self.baseline_performance['latency']:.2f}ms, "
                   f"error_rate={self.baseline_performance['error_rate']:.4f}")
    
    def get_description(self) -> str:
        """Get a description of the performance fitness function."""
        return (f"Evaluates system performance improvement with target of {self.target_improvement:.1%}. "
                f"Weights: throughput={self.throughput_weight:.2f}, latency={self.latency_weight:.2f}, "
                f"error_rate={self.error_rate_weight:.2f}")
    
    def get_importance_for_task(self, task_type: str) -> float:
        """
        Get the importance of performance for different task types.
        
        Args:
            task_type: The type of task.
            
        Returns:
            float: Importance weight (0.0 to 1.0).
        """
        # High importance for real-time and interactive tasks
        if task_type in ['real-time', 'interactive', 'streaming', 'online']:
            return 0.9
        # Medium importance for batch processing
        elif task_type in ['batch', 'offline', 'analysis']:
            return 0.6
        # Default medium-high importance
        return 0.7
    
    def get_performance_trend(self, window: int = 10) -> Dict[str, Any]:
        """
        Analyze the trend of performance over time.
        
        Args:
            window: Number of recent entries to analyze.
            
        Returns:
            Dict[str, Any]: Trend analysis.
        """
        if len(self.history) < 2:
            return {
                'trend': 'unknown',
                'slope': 0.0,
                'volatility': 0.0
            }
            
        # Get recent history
        recent = self.history[-min(window, len(self.history)):]
        
        # Extract timestamps and scores
        times = np.array([entry['timestamp'] for entry in recent])
        scores = np.array([entry['normalized_score'] for entry in recent])
        
        # Normalize times to [0, 1] range
        t_min, t_max = times.min(), times.max()
        if t_max > t_min:
            times_norm = (times - t_min) / (t_max - t_min)
        else:
            times_norm = np.zeros_like(times)
            
        # Calculate slope using linear regression
        if len(times_norm) > 1:
            slope, _ = np.polyfit(times_norm, scores, 1)
        else:
            slope = 0.0
            
        # Calculate volatility as standard deviation
        volatility = scores.std() if len(scores) > 1 else 0.0
        
        # Determine trend
        if slope > 0.1:
            trend = 'rapidly_improving'
        elif slope > 0.03:
            trend = 'improving'
        elif slope < -0.1:
            trend = 'rapidly_declining'
        elif slope < -0.03:
            trend = 'declining'
        else:
            trend = 'stable'
            
        return {
            'trend': trend,
            'slope': float(slope),
            'volatility': float(volatility),
            'latest_score': float(scores[-1]) if scores.size > 0 else 0.0,
            'mean_score': float(scores.mean()) if scores.size > 0 else 0.0
        }


class ResourceEfficiencyFitness(FitnessFunction):
    """Fitness function based on resource efficiency."""
    
    def __init__(self, 
                memory_weight: float = 0.5, 
                cpu_weight: float = 0.3, 
                storage_weight: float = 0.2):
        """
        Initialize the resource efficiency fitness function.
        
        Args:
            memory_weight: Weight for memory efficiency in the overall score.
            cpu_weight: Weight for CPU efficiency in the overall score.
            storage_weight: Weight for storage efficiency in the overall score.
        """
        self.memory_weight = memory_weight
        self.cpu_weight = cpu_weight
        self.storage_weight = storage_weight
        self.baseline_resources = None
        self.history = []
        
    def evaluate(self, system_state: Dict[str, Any]) -> float:
        """
        Evaluate resource efficiency fitness.
        
        Args:
            system_state: The current state of the system, including resource usage metrics.
            
        Returns:
            float: A fitness score (0.0 to 1.0).
        """
        current_resources = system_state.get('resources', {})
        
        if not current_resources:
            logger.warning("No resource data available in system state")
            return 0.0
            
        # Extract resource metrics
        memory_usage = current_resources.get('memory_usage', 0.0)
        cpu_usage = current_resources.get('cpu_usage', 0.0)
        storage_usage = current_resources.get('storage_usage', 0.0)
        
        # Additional metrics if available
        network_usage = current_resources.get('network_usage', 0.0)
        gpu_usage = current_resources.get('gpu_usage', 0.0)
        
        # Normalize usages to [0, 1] if they're not already
        memory_usage = min(100.0, max(0.0, memory_usage)) / 100.0
        cpu_usage = min(100.0, max(0.0, cpu_usage)) / 100.0
        storage_usage = min(100.0, max(0.0, storage_usage)) / 100.0
        network_usage = min(100.0, max(0.0, network_usage)) / 100.0
        gpu_usage = min(100.0, max(0.0, gpu_usage)) / 100.0
        
        # Initialize baseline if not set
        if self.baseline_resources is None:
            self.baseline_resources = {
                'memory_usage': memory_usage,
                'cpu_usage': cpu_usage,
                'storage_usage': storage_usage,
                'network_usage': network_usage,
                'gpu_usage': gpu_usage,
                'timestamp': time.time()
            }
            logger.info(f"Initialized resource baseline: memory={memory_usage:.2%}, CPU={cpu_usage:.2%}, storage={storage_usage:.2%}")
            # Return a medium score for baseline
            return 0.5
            
        # Calculate efficiency ratios (lower usage is better)
        # Avoid division by zero by ensuring denominators are positive
        memory_efficiency = self.baseline_resources['memory_usage'] / max(0.01, memory_usage)
        cpu_efficiency = self.baseline_resources['cpu_usage'] / max(0.01, cpu_usage)
        storage_efficiency = self.baseline_resources['storage_usage'] / max(0.01, storage_usage)
        
        # Add additional metrics if they were non-zero in the baseline
        additional_efficiency = 0.0
        additional_weight = 0.0
        
        if self.baseline_resources['network_usage'] > 0:
            network_efficiency = self.baseline_resources['network_usage'] / max(0.01, network_usage)
            additional_efficiency += 0.05 * network_efficiency
            additional_weight += 0.05
            
        if self.baseline_resources['gpu_usage'] > 0:
            gpu_efficiency = self.baseline_resources['gpu_usage'] / max(0.01, gpu_usage)
            additional_efficiency += 0.1 * gpu_efficiency
            additional_weight += 0.1
            
        # Adjust weights if additional metrics are used
        adjusted_memory_weight = self.memory_weight * (1.0 - additional_weight)
        adjusted_cpu_weight = self.cpu_weight * (1.0 - additional_weight)
        adjusted_storage_weight = self.storage_weight * (1.0 - additional_weight)
        
        # Combine efficiencies
        overall_efficiency = (
            adjusted_memory_weight * memory_efficiency +
            adjusted_cpu_weight * cpu_efficiency +
            adjusted_storage_weight * storage_efficiency +
            additional_efficiency
        )
        
        # Normalize to [0, 1]
        # Efficiency of 1.0 means usage is the same as baseline
        # Efficiency of 2.0 means usage is half of baseline
        # Efficiency of 0.5 means usage is twice the baseline
        normalized_score = min(1.0, max(0.0, overall_efficiency / 2.0))
        
        # Update history
        self.history.append({
            'timestamp': time.time(),
            'memory_usage': memory_usage,
            'cpu_usage': cpu_usage,
            'storage_usage': storage_usage,
            'memory_efficiency': memory_efficiency,
            'cpu_efficiency': cpu_efficiency,
            'storage_efficiency': storage_efficiency,
            'overall_efficiency': overall_efficiency,
            'normalized_score': normalized_score
        })
        
        # Log significant changes
        efficiency_ratio = overall_efficiency / 1.0  # Ratio to baseline efficiency
        if abs(efficiency_ratio - 1.0) > 0.2:  # 20% change threshold
            logger.info(f"Significant resource efficiency change detected: {efficiency_ratio:.2f}x baseline")
            
        return normalized_score
    
    def update_baseline(self, system_state: Optional[Dict[str, Any]] = None):
        """
        Update the resource efficiency baseline.
        
        Args:
            system_state: Optional system state to use for the new baseline.
                          If None, uses the most recent evaluation.
        """
        if system_state is not None:
            current_resources = system_state.get('resources', {})
            if current_resources:
                # Normalize usages to [0, 1]
                memory_usage = min(100.0, max(0.0, current_resources.get('memory_usage', 0.0))) / 100.0
                cpu_usage = min(100.0, max(0.0, current_resources.get('cpu_usage', 0.0))) / 100.0
                storage_usage = min(100.0, max(0.0, current_resources.get('storage_usage', 0.0))) / 100.0
                network_usage = min(100.0, max(0.0, current_resources.get('network_usage', 0.0))) / 100.0
                gpu_usage = min(100.0, max(0.0, current_resources.get('gpu_usage', 0.0))) / 100.0
                
                self.baseline_resources = {
                    'memory_usage': memory_usage,
                    'cpu_usage': cpu_usage,
                    'storage_usage': storage_usage,
                    'network_usage': network_usage,
                    'gpu_usage': gpu_usage,
                    'timestamp': time.time()
                }
        elif self.history:
            # Use the most recent evaluation
            latest = self.history[-1]
            self.baseline_resources = {
                'memory_usage': latest['memory_usage'],
                'cpu_usage': latest['cpu_usage'],
                'storage_usage': latest['storage_usage'],
                'network_usage': self.baseline_resources.get('network_usage', 0.0),  # Maintain previous values
                'gpu_usage': self.baseline_resources.get('gpu_usage', 0.0),
                'timestamp': latest['timestamp']
            }
            
        logger.info(f"Updated resource baseline: memory={self.baseline_resources['memory_usage']:.2%}, "
                   f"CPU={self.baseline_resources['cpu_usage']:.2%}, "
                   f"storage={self.baseline_resources['storage_usage']:.2%}")
    
    def get_description(self) -> str:
        """Get a description of the resource efficiency fitness function."""
        return (f"Evaluates resource efficiency with weights: "
                f"Memory={self.memory_weight:.1f}, CPU={self.cpu_weight:.1f}, "
                f"Storage={self.storage_weight:.1f}")
    
    def get_importance_for_task(self, task_type: str) -> float:
        """
        Get the importance of resource efficiency for different task types.
        
        Args:
            task_type: The type of task.
            
        Returns:
            float: Importance weight (0.0 to 1.0).
        """
        # Higher importance for mobile or edge computing
        if task_type in ['mobile', 'edge', 'embedded', 'iot']:
            return 0.9
        # High importance for continuous processing
        elif task_type in ['continuous', 'streaming', 'real-time']:
            return 0.8
        # Medium importance for batch processing
        elif task_type in ['batch', 'offline']:
            return 0.5
        # Default medium importance
        return 0.6
    
    def get_efficiency_trend(self, window: int = 10) -> Dict[str, Any]:
        """
        Analyze the trend of resource efficiency over time.
        
        Args:
            window: Number of recent entries to analyze.
            
        Returns:
            Dict[str, Any]: Trend analysis.
        """
        if len(self.history) < 2:
            return {
                'trend': 'unknown',
                'slope': 0.0,
                'volatility': 0.0
            }
            
        # Get recent history
        recent = self.history[-min(window, len(self.history)):]
        
        # Extract timestamps and scores
        times = np.array([entry['timestamp'] for entry in recent])
        scores = np.array([entry['normalized_score'] for entry in recent])
        
        # Normalize times to [0, 1] range
        t_min, t_max = times.min(), times.max()
        if t_max > t_min:
            times_norm = (times - t_min) / (t_max - t_min)
        else:
            times_norm = np.zeros_like(times)
            
        # Calculate slope using linear regression
        if len(times_norm) > 1:
            slope, _ = np.polyfit(times_norm, scores, 1)
        else:
            slope = 0.0
            
        # Calculate volatility as standard deviation
        volatility = scores.std() if len(scores) > 1 else 0.0
        
        # Determine trend
        if slope > 0.1:
            trend = 'rapidly_improving'
        elif slope > 0.03:
            trend = 'improving'
        elif slope < -0.1:
            trend = 'rapidly_declining'
        elif slope < -0.03:
            trend = 'declining'
        else:
            trend = 'stable'
            
        # Get resource-specific trends
        memory_trend = self._analyze_specific_trend([entry['memory_efficiency'] for entry in recent])
        cpu_trend = self._analyze_specific_trend([entry['cpu_efficiency'] for entry in recent])
        storage_trend = self._analyze_specific_trend([entry['storage_efficiency'] for entry in recent])
            
        return {
            'trend': trend,
            'slope': float(slope),
            'volatility': float(volatility),
            'latest_score': float(scores[-1]) if scores.size > 0 else 0.0,
            'mean_score': float(scores.mean()) if scores.size > 0 else 0.0,
            'memory_trend': memory_trend,
            'cpu_trend': cpu_trend,
            'storage_trend': storage_trend
        }
    
    def _analyze_specific_trend(self, values: List[float]) -> str:
        """
        Analyze trend for a specific resource metric.
        
        Args:
            values: List of efficiency values.
            
        Returns:
            str: Trend description.
        """
        if len(values) < 2:
            return 'unknown'
            
        # Simple trend analysis based on recent direction
        recent_values = values[-min(5, len(values)):]
        if len(recent_values) < 2:
            return 'unknown'
            
        changes = [recent_values[i] - recent_values[i-1] for i in range(1, len(recent_values))]
        avg_change = sum(changes) / len(changes)
        
        if avg_change > 0.05:
            return 'improving'
        elif avg_change < -0.05:
            return 'declining'
        else:
            return 'stable'


class ReliabilityFitness(FitnessFunction):
    """Fitness function based on system reliability."""
    
    def __init__(self, 
                uptime_weight: float = 0.4, 
                error_weight: float = 0.4,
                recovery_weight: float = 0.2):
        """
        Initialize the reliability fitness function.
        
        Args:
            uptime_weight: Weight for uptime in the overall score.
            error_weight: Weight for error rates in the overall score.
            recovery_weight: Weight for recovery metrics in the overall score.
        """
        self.uptime_weight = uptime_weight
        self.error_weight = error_weight
        self.recovery_weight = recovery_weight
        self.baseline_reliability = None
        self.history = []
        
    def evaluate(self, system_state: Dict[str, Any]) -> float:
        """
        Evaluate system reliability fitness.
        
        Args:
            system_state: The current state of the system, including reliability metrics.
            
        Returns:
            float: A fitness score (0.0 to 1.0).
        """
        current_reliability = system_state.get('reliability', {})
        
        if not current_reliability:
            logger.warning("No reliability data available in system state")
            return 0.0
            
        # Extract reliability metrics
        uptime = current_reliability.get('uptime', 0.0)
        error_rate = current_reliability.get('error_rate', 1.0)
        mttr = current_reliability.get('mean_time_to_recovery', float('inf'))  # Mean Time To Recovery
        
        # Optional metrics
        availability = current_reliability.get('availability', 0.0)
        fault_tolerance = current_reliability.get('fault_tolerance', 0.0)
        
        # Initialize baseline if not set
        if self.baseline_reliability is None:
            self.baseline_reliability = {
                'uptime': uptime,
                'error_rate': error_rate,
                'mean_time_to_recovery': mttr,
                'availability': availability,
                'fault_tolerance': fault_tolerance,
                'timestamp': time.time()
            }
            logger.info(f"Initialized reliability baseline: uptime={uptime:.2%}, error_rate={error_rate:.4f}, MTTR={mttr:.2f}s")
            # Return a medium score for baseline
            return 0.5
            
        # Calculate improvements
        uptime_improvement = uptime / max(0.001, self.baseline_reliability['uptime'])
        error_improvement = self.baseline_reliability['error_rate'] / max(0.001, error_rate)
        
        # Recovery improvement (lower MTTR is better)
        if mttr > 0 and self.baseline_reliability['mean_time_to_recovery'] > 0:
            recovery_improvement = self.baseline_reliability['mean_time_to_recovery'] / mttr
        else:
            recovery_improvement = 1.0  # No change
            
        # Combine improvements
        overall_improvement = (
            self.uptime_weight * uptime_improvement +
            self.error_weight * error_improvement +
            self.recovery_weight * recovery_improvement
        )
        
        # Include optional metrics if available
        additional_weight = 0.0
        additional_improvement = 0.0
        
        if availability > 0 and self.baseline_reliability['availability'] > 0:
            avail_improvement = availability / self.baseline_reliability['availability']
            additional_improvement += 0.1 * avail_improvement
            additional_weight += 0.1
            
        if fault_tolerance > 0 and self.baseline_reliability['fault_tolerance'] > 0:
            ft_improvement = fault_tolerance / self.baseline_reliability['fault_tolerance']
            additional_improvement += 0.1 * ft_improvement
            additional_weight += 0.1
            
        # Adjust primary weights
        if additional_weight > 0:
            scaling_factor = 1.0 - additional_weight
            adjusted_improvement = (
                scaling_factor * overall_improvement +
                additional_improvement
            )
            overall_improvement = adjusted_improvement
        
        # Normalize to [0, 1]
        # Score of 1.0 means 2x improvement
        # Score of 0.5 means no change
        # Score of 0.0 means 0.5x deterioration
        normalized_score = min(1.0, max(0.0, overall_improvement / 2.0))
        
        # Update history
        self.history.append({
            'timestamp': time.time(),
            'uptime': uptime,
            'error_rate': error_rate,
            'mean_time_to_recovery': mttr,
            'uptime_improvement': uptime_improvement,
            'error_improvement': error_improvement,
            'recovery_improvement': recovery_improvement,
            'overall_improvement': overall_improvement,
            'normalized_score': normalized_score
        })
        
        # Log significant changes
        if abs(overall_improvement - 1.0) > 0.1:  # 10% change threshold
            logger.info(f"Significant reliability change detected: {overall_improvement:.2f}x baseline")
            
        return normalized_score
    
    def update_baseline(self, system_state: Optional[Dict[str, Any]] = None):
        """
        Update the reliability baseline.
        
        Args:
            system_state: Optional system state to use for the new baseline.
                          If None, uses the most recent evaluation.
        """
        if system_state is not None:
            current_reliability = system_state.get('reliability', {})
            if current_reliability:
                self.baseline_reliability = {
                    'uptime': current_reliability.get('uptime', 0.0),
                    'error_rate': current_reliability.get('error_rate', 1.0),
                    'mean_time_to_recovery': current_reliability.get('mean_time_to_recovery', float('inf')),
                    'availability': current_reliability.get('availability', 0.0),
                    'fault_tolerance': current_reliability.get('fault_tolerance', 0.0),
                    'timestamp': time.time()
                }
        elif self.history:
            # Use the most recent evaluation
            latest = self.history[-1]
            self.baseline_reliability = {
                'uptime': latest['uptime'],
                'error_rate': latest['error_rate'],
                'mean_time_to_recovery': latest['mean_time_to_recovery'],
                'availability': self.baseline_reliability.get('availability', 0.0),  # Maintain previous values
                'fault_tolerance': self.baseline_reliability.get('fault_tolerance', 0.0),
                'timestamp': latest['timestamp']
            }
            
        logger.info(f"Updated reliability baseline: uptime={self.baseline_reliability['uptime']:.2%}, "
                   f"error_rate={self.baseline_reliability['error_rate']:.4f}, "
                   f"MTTR={self.baseline_reliability['mean_time_to_recovery']:.2f}s")
    
    def get_description(self) -> str:
        """Get a description of the reliability fitness function."""
        return (f"Evaluates system reliability with weights: "
                f"Uptime={self.uptime_weight:.1f}, Error={self.error_weight:.1f}, "
                f"Recovery={self.recovery_weight:.1f}")
    
    def get_importance_for_task(self, task_type: str) -> float:
        """
        Get the importance of reliability for different task types.
        
        Args:
            task_type: The type of task.
            
        Returns:
            float: Importance weight (0.0 to 1.0).
        """
        # Highest importance for mission-critical systems
        if task_type in ['critical', 'mission-critical', 'safety-critical', 'financial']:
            return 1.0
        # High importance for continuous service systems
        elif task_type in ['service', 'production', 'customer-facing']:
            return 0.9
        # Medium importance for research/experimental systems
        elif task_type in ['research', 'experimental', 'development']:
            return 0.5
        # Default high importance
        return 0.8
    
    def get_reliability_trend(self, window: int = 10) -> Dict[str, Any]:
        """
        Analyze the trend of reliability over time.
        
        Args:
            window: Number of recent entries to analyze.
            
        Returns:
            Dict[str, Any]: Trend analysis.
        """
        if len(self.history) < 2:
            return {
                'trend': 'unknown',
                'slope': 0.0,
                'volatility': 0.0
            }
            
        # Get recent history
        recent = self.history[-min(window, len(self.history)):]
        
        # Extract timestamps and scores
        times = np.array([entry['timestamp'] for entry in recent])
        scores = np.array([entry['normalized_score'] for entry in recent])
        
        # Normalize times to [0, 1] range
        t_min, t_max = times.min(), times.max()
        if t_max > t_min:
            times_norm = (times - t_min) / (t_max - t_min)
        else:
            times_norm = np.zeros_like(times)
            
        # Calculate slope using linear regression
        if len(times_norm) > 1:
            slope, _ = np.polyfit(times_norm, scores, 1)
        else:
            slope = 0.0
            
        # Calculate volatility as standard deviation
        volatility = scores.std() if len(scores) > 1 else 0.0
        
        # Determine trend
        if slope > 0.1:
            trend = 'rapidly_improving'
        elif slope > 0.03:
            trend = 'improving'
        elif slope < -0.1:
            trend = 'rapidly_declining'
        elif slope < -0.03:
            trend = 'declining'
        else:
            trend = 'stable'
            
        # Get component-specific trends
        uptime_trend = self._analyze_specific_trend([entry['uptime_improvement'] for entry in recent])
        error_trend = self._analyze_specific_trend([entry['error_improvement'] for entry in recent])
        recovery_trend = self._analyze_specific_trend([entry['recovery_improvement'] for entry in recent])
            
        return {
            'trend': trend,
            'slope': float(slope),
            'volatility': float(volatility),
            'latest_score': float(scores[-1]) if scores.size > 0 else 0.0,
            'mean_score': float(scores.mean()) if scores.size > 0 else 0.0,
            'uptime_trend': uptime_trend,
            'error_trend': error_trend,
            'recovery_trend': recovery_trend
        }
    
    def _analyze_specific_trend(self, values: List[float]) -> str:
        """
        Analyze trend for a specific reliability metric.
        
        Args:
            values: List of improvement values.
            
        Returns:
            str: Trend description.
        """
        if len(values) < 2:
            return 'unknown'
            
        # Simple trend analysis based on recent direction
        recent_values = values[-min(5, len(values)):]
        if len(recent_values) < 2:
            return 'unknown'
            
        changes = [recent_values[i] - recent_values[i-1] for i in range(1, len(recent_values))]
        avg_change = sum(changes) / len(changes)
        
        if avg_change > 0.05:
            return 'improving'
        elif avg_change < -0.05:
            return 'declining'
        else:
            return 'stable'


class AdaptabilityFitness(FitnessFunction):
    """Fitness function based on system adaptability to new tasks or environments."""
    
    def __init__(self, 
                learning_rate_weight: float = 0.5, 
                generalization_weight: float = 0.3,
                transfer_learning_weight: float = 0.2):
        """
        Initialize the adaptability fitness function.
        
        Args:
            learning_rate_weight: Weight for learning rate in the overall score.
            generalization_weight: Weight for generalization capability in the overall score.
            transfer_learning_weight: Weight for transfer learning in the overall score.
        """
        self.learning_rate_weight = learning_rate_weight
        self.generalization_weight = generalization_weight
        self.transfer_learning_weight = transfer_learning_weight
        self.baseline_adaptability = None
        self.history = []
        
    def evaluate(self, system_state: Dict[str, Any]) -> float:
        """
        Evaluate system adaptability fitness.
        
        Args:
            system_state: The current state of the system, including adaptability metrics.
            
        Returns:
            float: A fitness score (0.0 to 1.0).
        """
        current_adaptability = system_state.get('adaptability', {})
        
        if not current_adaptability:
            logger.warning("No adaptability data available in system state")
            return 0.0
            
        # Extract adaptability metrics
        learning_rate = current_adaptability.get('learning_rate', 0.0)
        generalization = current_adaptability.get('generalization', 0.0)
        transfer_learning = current_adaptability.get('transfer_learning', 0.0)
        
        # Optional advanced metrics
        few_shot_learning = current_adaptability.get('few_shot_learning', 0.0)
        online_learning = current_adaptability.get('online_learning', 0.0)
        
        # Initialize baseline if not set
        if self.baseline_adaptability is None:
            self.baseline_adaptability = {
                'learning_rate': learning_rate,
                'generalization': generalization,
                'transfer_learning': transfer_learning,
                'few_shot_learning': few_shot_learning,
                'online_learning': online_learning,
                'timestamp': time.time()
            }
            logger.info(f"Initialized adaptability baseline: learning_rate={learning_rate:.4f}, "
                       f"generalization={generalization:.4f}, transfer_learning={transfer_learning:.4f}")
            # Return a medium score for baseline
            return 0.5
            
        # Calculate improvements
        learning_improvement = learning_rate / max(0.001, self.baseline_adaptability['learning_rate'])
        generalization_improvement = generalization / max(0.001, self.baseline_adaptability['generalization'])
        
        # Transfer learning improvement
        if transfer_learning > 0 and self.baseline_adaptability['transfer_learning'] > 0:
            transfer_improvement = transfer_learning / self.baseline_adaptability['transfer_learning']
        else:
            transfer_improvement = 1.0  # No change
            
        # Combine improvements
        overall_improvement = (
            self.learning_rate_weight * learning_improvement +
            self.generalization_weight * generalization_improvement +
            self.transfer_learning_weight * transfer_improvement
        )
        
        # Include optional metrics if available
        additional_weight = 0.0
        additional_improvement = 0.0
        
        if few_shot_learning > 0 and self.baseline_adaptability['few_shot_learning'] > 0:
            few_shot_improvement = few_shot_learning / self.baseline_adaptability['few_shot_learning']
            additional_improvement += 0.1 * few_shot_improvement
            additional_weight += 0.1
            
        if online_learning > 0 and self.baseline_adaptability['online_learning'] > 0:
            online_improvement = online_learning / self.baseline_adaptability['online_learning']
            additional_improvement += 0.1 * online_improvement
            additional_weight += 0.1
            
        # Adjust primary weights
        if additional_weight > 0:
            scaling_factor = 1.0 - additional_weight
            adjusted_improvement = (
                scaling_factor * overall_improvement +
                additional_improvement
            )
            overall_improvement = adjusted_improvement
        
        # Normalize to [0, 1]
        normalized_score = min(1.0, max(0.0, overall_improvement / 2.0))
        
        # Update history
        self.history.append({
            'timestamp': time.time(),
            'learning_rate': learning_rate,
            'generalization': generalization,
            'transfer_learning': transfer_learning,
            'learning_improvement': learning_improvement,
            'generalization_improvement': generalization_improvement,
            'transfer_improvement': transfer_improvement,
            'overall_improvement': overall_improvement,
            'normalized_score': normalized_score
        })
        
        # Log significant changes
        if abs(overall_improvement - 1.0) > 0.1:  # 10% change threshold
            logger.info(f"Significant adaptability change detected: {overall_improvement:.2f}x baseline")
            
        return normalized_score
    
    def update_baseline(self, system_state: Optional[Dict[str, Any]] = None):
        """
        Update the adaptability baseline.
        
        Args:
            system_state: Optional system state to use for the new baseline.
                          If None, uses the most recent evaluation.
        """
        if system_state is not None:
            current_adaptability = system_state.get('adaptability', {})
            if current_adaptability:
                self.baseline_adaptability = {
                    'learning_rate': current_adaptability.get('learning_rate', 0.0),
                    'generalization': current_adaptability.get('generalization', 0.0),
                    'transfer_learning': current_adaptability.get('transfer_learning', 0.0),
                    'few_shot_learning': current_adaptability.get('few_shot_learning', 0.0),
                    'online_learning': current_adaptability.get('online_learning', 0.0),
                    'timestamp': time.time()
                }
        elif self.history:
            # Use the most recent evaluation
            latest = self.history[-1]
            self.baseline_adaptability = {
                'learning_rate': latest['learning_rate'],
                'generalization': latest['generalization'],
                'transfer_learning': latest['transfer_learning'],
                'few_shot_learning': self.baseline_adaptability.get('few_shot_learning', 0.0),  # Maintain previous values
                'online_learning': self.baseline_adaptability.get('online_learning', 0.0),
                'timestamp': latest['timestamp']
            }
            
        logger.info(f"Updated adaptability baseline: learning_rate={self.baseline_adaptability['learning_rate']:.4f}, "
                   f"generalization={self.baseline_adaptability['generalization']:.4f}, "
                   f"transfer_learning={self.baseline_adaptability['transfer_learning']:.4f}")
    
    def get_description(self) -> str:
        """Get a description of the adaptability fitness function."""
        return (f"Evaluates system adaptability with weights: "
                f"Learning rate={self.learning_rate_weight:.1f}, "
                f"Generalization={self.generalization_weight:.1f}, "
                f"Transfer learning={self.transfer_learning_weight:.1f}")
    
    def get_importance_for_task(self, task_type: str) -> float:
        """
        Get the importance of adaptability for different task types.
        
        Args:
            task_type: The type of task.
            
        Returns:
            float: Importance weight (0.0 to 1.0).
        """
        # Higher importance for dynamic environments
        if task_type in ['dynamic', 'evolving', 'novel', 'exploration']:
            return 0.9
        # High importance for continuous learning
        elif task_type in ['lifelong', 'incremental', 'online', 'streaming']:
            return 0.8
        # Medium importance for stable environments
        elif task_type in ['static', 'stable', 'production']:
            return 0.4
        # Default medium-high importance
        return 0.6
    
    def get_adaptability_trend(self, window: int = 10) -> Dict[str, Any]:
        """
        Analyze the trend of adaptability over time.
        
        Args:
            window: Number of recent entries to analyze.
            
        Returns:
            Dict[str, Any]: Trend analysis.
        """
        if len(self.history) < 2:
            return {
                'trend': 'unknown',
                'slope': 0.0,
                'volatility': 0.0
            }
            
        # Get recent history
        recent = self.history[-min(window, len(self.history)):]
        
        # Extract timestamps and scores
        times = np.array([entry['timestamp'] for entry in recent])
        scores = np.array([entry['normalized_score'] for entry in recent])
        
        # Normalize times to [0, 1] range
        t_min, t_max = times.min(), times.max()
        if t_max > t_min:
            times_norm = (times - t_min) / (t_max - t_min)
        else:
            times_norm = np.zeros_like(times)
            
        # Calculate slope using linear regression
        if len(times_norm) > 1:
            slope, _ = np.polyfit(times_norm, scores, 1)
        else:
            slope = 0.0
            
        # Calculate volatility as standard deviation
        volatility = scores.std() if len(scores) > 1 else 0.0
        
        # Determine trend
        if slope > 0.1:
            trend = 'rapidly_improving'
        elif slope > 0.03:
            trend = 'improving'
        elif slope < -0.1:
            trend = 'rapidly_declining'
        elif slope < -0.03:
            trend = 'declining'
        else:
            trend = 'stable'
            
        # Get component-specific trends
        learning_trend = self._analyze_specific_trend([entry['learning_improvement'] for entry in recent])
        generalization_trend = self._analyze_specific_trend([entry['generalization_improvement'] for entry in recent])
        transfer_trend = self._analyze_specific_trend([entry['transfer_improvement'] for entry in recent])
            
        return {
            'trend': trend,
            'slope': float(slope),
            'volatility': float(volatility),
            'latest_score': float(scores[-1]) if scores.size > 0 else 0.0,
            'mean_score': float(scores.mean()) if scores.size > 0 else 0.0,
            'learning_trend': learning_trend,
            'generalization_trend': generalization_trend,
            'transfer_trend': transfer_trend
        }
    
    def _analyze_specific_trend(self, values: List[float]) -> str:
        """
        Analyze trend for a specific adaptability metric.
        
        Args:
            values: List of improvement values.
            
        Returns:
            str: Trend description.
        """
        if len(values) < 2:
            return 'unknown'
            
        # Simple trend analysis based on recent direction
        recent_values = values[-min(5, len(values)):]
        if len(recent_values) < 2:
            return 'unknown'
            
        changes = [recent_values[i] - recent_values[i-1] for i in range(1, len(recent_values))]
        avg_change = sum(changes) / len(changes)
        
        if avg_change > 0.05:
            return 'improving'
        elif avg_change < -0.05:
            return 'declining'
        else:
            return 'stable'


class SecurityFitness(FitnessFunction):
    """Fitness function based on system security."""
    
    def __init__(self, 
                vulnerability_weight: float = 0.4, 
                authentication_weight: float = 0.3,
                encryption_weight: float = 0.3):
        """
        Initialize the security fitness function.
        
        Args:
            vulnerability_weight: Weight for vulnerability metrics in the overall score.
            authentication_weight: Weight for authentication metrics in the overall score.
            encryption_weight: Weight for encryption metrics in the overall score.
        """
        self.vulnerability_weight = vulnerability_weight
        self.authentication_weight = authentication_weight
        self.encryption_weight = encryption_weight
        self.baseline_security = None
        self.history = []
        
    def evaluate(self, system_state: Dict[str, Any]) -> float:
        """
        Evaluate system security fitness.
        
        Args:
            system_state: The current state of the system, including security metrics.
            
        Returns:
            float: A fitness score (0.0 to 1.0).
        """
        current_security = system_state.get('security', {})
        
        if not current_security:
            logger.warning("No security data available in system state")
            return 0.0
            
        # Extract security metrics (higher is better)
        vulnerability_score = current_security.get('vulnerability_score', 0.0)
        authentication_score = current_security.get('authentication_score', 0.0)
        encryption_score = current_security.get('encryption_score', 0.0)
        
        # Advanced metrics
        attack_surface = current_security.get('attack_surface', 1.0)  # Lower is better
        threat_detection = current_security.get('threat_detection', 0.0)  # Higher is better
        
        # Normalize metrics if needed
        vulnerability_score = min(1.0, max(0.0, vulnerability_score))
        authentication_score = min(1.0, max(0.0, authentication_score))
        encryption_score = min(1.0, max(0.0, encryption_score))
        
        # For attack surface, lower is better, so invert
        attack_surface_score = 1.0 / max(0.1, attack_surface)
        threat_detection = min(1.0, max(0.0, threat_detection))
        
        # Initialize baseline if not set
        if self.baseline_security is None:
            self.baseline_security = {
                'vulnerability_score': vulnerability_score,
                'authentication_score': authentication_score,
                'encryption_score': encryption_score,
                'attack_surface_score': attack_surface_score,
                'threat_detection': threat_detection,
                'timestamp': time.time()
            }
            logger.info(f"Initialized security baseline: vulnerability={vulnerability_score:.4f}, "
                       f"authentication={authentication_score:.4f}, encryption={encryption_score:.4f}")
            # Return a medium score for baseline
            return 0.5
            
        # Calculate improvements
        vulnerability_improvement = vulnerability_score / max(0.001, self.baseline_security['vulnerability_score'])
        authentication_improvement = authentication_score / max(0.001, self.baseline_security['authentication_score'])
        encryption_improvement = encryption_score / max(0.001, self.baseline_security['encryption_score'])
        
        # Include advanced metrics if available
        attack_surface_improvement = attack_surface_score / max(0.001, self.baseline_security['attack_surface_score'])
        threat_detection_improvement = threat_detection / max(0.001, self.baseline_security['threat_detection'])
        
        # Combine improvements using weights
        base_improvement = (
            self.vulnerability_weight * vulnerability_improvement +
            self.authentication_weight * authentication_improvement +
            self.encryption_weight * encryption_improvement
        )
        
        # Add advanced metrics with smaller weights
        advanced_weight = 0.2
        if attack_surface_score > 0 and threat_detection > 0:
            advanced_improvement = (
                0.5 * attack_surface_improvement +
                0.5 * threat_detection_improvement
            )
            overall_improvement = (1 - advanced_weight) * base_improvement + advanced_weight * advanced_improvement
        else:
            overall_improvement = base_improvement
        
        # Normalize to [0, 1]
        normalized_score = min(1.0, max(0.0, overall_improvement / (1.0 + self.target_improvement())))
        
        # Update history
        self.history.append({
            'timestamp': time.time(),
            'vulnerability_score': vulnerability_score,
            'authentication_score': authentication_score,
            'encryption_score': encryption_score,
            'attack_surface_score': attack_surface_score,
            'threat_detection': threat_detection,
            'vulnerability_improvement': vulnerability_improvement,
            'authentication_improvement': authentication_improvement,
            'encryption_improvement': encryption_improvement,
            'attack_surface_improvement': attack_surface_improvement,
            'threat_detection_improvement': threat_detection_improvement,
            'overall_improvement': overall_improvement,
            'normalized_score': normalized_score
        })
        
        # Log significant changes
        if abs(overall_improvement - 1.0) > 0.1:  # 10% change threshold
            logger.info(f"Significant security change detected: {overall_improvement:.2f}x baseline")
            
        return normalized_score
    
    def target_improvement(self) -> float:
        """
        Calculate the target improvement based on current baseline.
        Lower initial scores have higher improvement targets.
        
        Returns:
            float: Target improvement factor.
        """
        if self.baseline_security is None:
            return 0.2  # Default target improvement
            
        # Calculate average baseline score
        avg_score = (
            self.baseline_security['vulnerability_score'] +
            self.baseline_security['authentication_score'] +
            self.baseline_security['encryption_score']
        ) / 3.0
        
        # Higher target for lower scores
        if avg_score < 0.3:
            return 0.3  # 30% improvement target
        elif avg_score < 0.6:
            return 0.2  # 20% improvement target
        else:
            return 0.1  # 10% improvement target
    
    def update_baseline(self, system_state: Optional[Dict[str, Any]] = None):
        """
        Update the security baseline.
        
        Args:
            system_state: Optional system state to use for the new baseline.
                          If None, uses the most recent evaluation.
        """
        if system_state is not None:
            current_security = system_state.get('security', {})
            if current_security:
                # Extract and normalize
                vulnerability_score = min(1.0, max(0.0, current_security.get('vulnerability_score', 0.0)))
                authentication_score = min(1.0, max(0.0, current_security.get('authentication_score', 0.0)))
                encryption_score = min(1.0, max(0.0, current_security.get('encryption_score', 0.0)))
                attack_surface = current_security.get('attack_surface', 1.0)
                threat_detection = min(1.0, max(0.0, current_security.get('threat_detection', 0.0)))
                
                # For attack surface, lower is better, so invert
                attack_surface_score = 1.0 / max(0.1, attack_surface)
                
                self.baseline_security = {
                    'vulnerability_score': vulnerability_score,
                    'authentication_score': authentication_score,
                    'encryption_score': encryption_score,
                    'attack_surface_score': attack_surface_score,
                    'threat_detection': threat_detection,
                    'timestamp': time.time()
                }
        elif self.history:
            # Use the most recent evaluation
            latest = self.history[-1]
            self.baseline_security = {
                'vulnerability_score': latest['vulnerability_score'],
                'authentication_score': latest['authentication_score'],
                'encryption_score': latest['encryption_score'],
                'attack_surface_score': latest['attack_surface_score'],
                'threat_detection': latest['threat_detection'],
                'timestamp': latest['timestamp']
            }
            
        logger.info(f"Updated security baseline: vulnerability={self.baseline_security['vulnerability_score']:.4f}, "
                   f"authentication={self.baseline_security['authentication_score']:.4f}, "
                   f"encryption={self.baseline_security['encryption_score']:.4f}")
    
    def get_description(self) -> str:
        """Get a description of the security fitness function."""
        return (f"Evaluates system security with weights: "
                f"Vulnerability={self.vulnerability_weight:.1f}, "
                f"Authentication={self.authentication_weight:.1f}, "
                f"Encryption={self.encryption_weight:.1f}")
    
    def get_importance_for_task(self, task_type: str) -> float:
        """
        Get the importance of security for different task types.
        
        Args:
            task_type: The type of task.
            
        Returns:
            float: Importance weight (0.0 to 1.0).
        """
        # Highest importance for sensitive data
        if task_type in ['financial', 'healthcare', 'personal', 'confidential']:
            return 1.0
        # High importance for user-facing systems
        elif task_type in ['user-data', 'authentication', 'payment', 'identity']:
            return 0.9
        # Medium importance for internal tools
        elif task_type in ['internal', 'analytics', 'reporting']:
            return 0.7
        # Default high importance
        return 0.8


# ------------------------------------------------------------------------------
# Evolutionary Constraints
# ------------------------------------------------------------------------------

class EvolutionaryConstraint(ABC):
    """Base class for constraints on the evolutionary process."""
    
    @abstractmethod
    def check(self, system_state: Dict[str, Any]) -> bool:
        """
        Check if the system state satisfies the constraint.
        
        Args:
            system_state: The current state of the system.
            
        Returns:
            bool: True if the constraint is satisfied, False otherwise.
        """
        pass
    
    @abstractmethod
    def get_description(self) -> str:
        """Get a description of the constraint."""
        pass
    
    def get_violation_details(self, system_state: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get details about why the constraint is violated.
        
        Args:
            system_state: The current state of the system.
            
        Returns:
            Dict[str, Any]: Violation details or empty dict if not violated.
        """
        if self.check(system_state):
            return {}
        
        # Default implementation returns generic details
        return {
            'message': f"Constraint '{self.get_description()}' is violated.",
            'constraint_type': self.__class__.__name__
        }


class ResourceLimitConstraint(EvolutionaryConstraint):
    """Constraint on system resource usage."""
    
    def __init__(self, 
                max_memory_usage: float, 
                max_cpu_usage: float, 
                max_storage_usage: float,
                headroom_percentage: float = 5.0):
        """
        Initialize the resource limit constraint.
        
        Args:
            max_memory_usage: Maximum allowed memory usage (%).
            max_cpu_usage: Maximum allowed CPU usage (%).
            max_storage_usage: Maximum allowed storage usage (%).
            headroom_percentage: Percentage headroom below max limits to warn.
        """
        self.max_memory_usage = max_memory_usage
        self.max_cpu_usage = max_cpu_usage
        self.max_storage_usage = max_storage_usage
        self.headroom_percentage = headroom_percentage
        self.warning_threshold_memory = max_memory_usage * (1.0 - headroom_percentage / 100.0)
        self.warning_threshold_cpu = max_cpu_usage * (1.0 - headroom_percentage / 100.0)
        self.warning_threshold_storage = max_storage_usage * (1.0 - headroom_percentage / 100.0)
        
    def check(self, system_state: Dict[str, Any]) -> bool:
        """
        Check if resource usage is within limits.
        
        Args:
            system_state: The current state of the system.
            
        Returns:
            bool: True if resource usage is within limits, False otherwise.
        """
        resources = system_state.get('resources', {})
        
        if not resources:
            logger.warning("No resource data available for constraint checking")
            return True  # Assume constraint is satisfied if no data
            
        memory_usage = resources.get('memory_usage', 0.0)
        cpu_usage = resources.get('cpu_usage', 0.0)
        storage_usage = resources.get('storage_usage', 0.0)
        
        # Check if any resource exceeds maximum
        if memory_usage > self.max_memory_usage:
            logger.warning(f"Memory usage ({memory_usage:.2f}%) exceeds limit ({self.max_memory_usage:.2f}%)")
            return False
            
        if cpu_usage > self.max_cpu_usage:
            logger.warning(f"CPU usage ({cpu_usage:.2f}%) exceeds limit ({self.max_cpu_usage:.2f}%)")
            return False
            
        if storage_usage > self.max_storage_usage:
            logger.warning(f"Storage usage ({storage_usage:.2f}%) exceeds limit ({self.max_storage_usage:.2f}%)")
            return False
            
        # Check if any resource is approaching maximum (for warning)
        if memory_usage > self.warning_threshold_memory:
            logger.info(f"Memory usage ({memory_usage:.2f}%) is approaching limit ({self.max_memory_usage:.2f}%)")
            
        if cpu_usage > self.warning_threshold_cpu:
            logger.info(f"CPU usage ({cpu_usage:.2f}%) is approaching limit ({self.max_cpu_usage:.2f}%)")
            
        if storage_usage > self.warning_threshold_storage:
            logger.info(f"Storage usage ({storage_usage:.2f}%) is approaching limit ({self.max_storage_usage:.2f}%)")
            
        return True
    
    def get_description(self) -> str:
        """Get a description of the resource limit constraint."""
        return (f"Limits resource usage to: Memory <= {self.max_memory_usage:.2f}%, "
                f"CPU <= {self.max_cpu_usage:.2f}%, Storage <= {self.max_storage_usage:.2f}%")
    
    def get_violation_details(self, system_state: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get details about the resource limit violation.
        
        Args:
            system_state: The current state of the system.
            
        Returns:
            Dict[str, Any]: Violation details or empty dict if not violated.
        """
        if self.check(system_state):
            return {}
            
        resources = system_state.get('resources', {})
        memory_usage = resources.get('memory_usage', 0.0)
        cpu_usage = resources.get('cpu_usage', 0.0)
        storage_usage = resources.get('storage_usage', 0.0)
        
        details = {
            'constraint_type': 'ResourceLimit',
            'violated_resources': []
        }
        
        if memory_usage > self.max_memory_usage:
            details['violated_resources'].append({
                'resource': 'memory',
                'current_usage': memory_usage,
                'limit': self.max_memory_usage,
                'excess': memory_usage - self.max_memory_usage
            })
            
        if cpu_usage > self.max_cpu_usage:
            details['violated_resources'].append({
                'resource': 'cpu',
                'current_usage': cpu_usage,
                'limit': self.max_cpu_usage,
                'excess': cpu_usage - self.max_cpu_usage
            })
            
        if storage_usage > self.max_storage_usage:
            details['violated_resources'].append({
                'resource': 'storage',
                'current_usage': storage_usage,
                'limit': self.max_storage_usage,
                'excess': storage_usage - self.max_storage_usage
            })
            
        # Add message summarizing violations
        resources_str = ", ".join([f"{v['resource']}: {v['current_usage']:.2f}% > {v['limit']:.2f}%" 
                                 for v in details['violated_resources']])
        details['message'] = f"Resource limits exceeded: {resources_str}"
        
        return details
    
    def get_headroom(self, system_state: Dict[str, Any]) -> Dict[str, float]:
        """
        Get the headroom available for each resource.
        
        Args:
            system_state: The current state of the system.
            
        Returns:
            Dict[str, float]: Headroom percentage for each resource.
        """
        resources = system_state.get('resources', {})
        
        if not resources:
            return {
                'memory': 100.0,
                'cpu': 100.0,
                'storage': 100.0
            }
            
        memory_usage = resources.get('memory_usage', 0.0)
        cpu_usage = resources.get('cpu_usage', 0.0)
        storage_usage = resources.get('storage_usage', 0.0)
        
        return {
            'memory': max(0.0, 100.0 * (self.max_memory_usage - memory_usage) / self.max_memory_usage),
            'cpu': max(0.0, 100.0 * (self.max_cpu_usage - cpu_usage) / self.max_cpu_usage),
            'storage': max(0.0, 100.0 * (self.max_storage_usage - storage_usage) / self.max_storage_usage)
        }


class SafetyConstraint(EvolutionaryConstraint):
    """Constraint on system safety metrics."""
    
    def __init__(self, 
                min_safety_score: float = 0.9,
                critical_components: Optional[List[str]] = None):
        """
        Initialize the safety constraint.
        
        Args:
            min_safety_score: Minimum acceptable safety score (0.0 to 1.0).
            critical_components: List of critical components that must have high safety.
        """
        self.min_safety_score = min_safety_score
        self.critical_components = critical_components or []
        self.min_component_score = min_safety_score  # Same threshold for components
        
    def check(self, system_state: Dict[str, Any]) -> bool:
        """
        Check if safety score is above minimum.
        
        Args:
            system_state: The current state of the system.
            
        Returns:
            bool: True if safety score is acceptable, False otherwise.
        """
        safety = system_state.get('safety', {})
        
        if not safety:
            logger.warning("No safety data available for constraint checking")
            return True  # Assume constraint is satisfied if no data
            
        # Check overall safety score
        overall_score = safety.get('overall_score', 0.0)
        if overall_score < self.min_safety_score:
            logger.warning(f"Safety score ({overall_score:.4f}) below minimum ({self.min_safety_score:.4f})")
            return False
            
        # Check critical component scores if available
        components = safety.get('components', {})
        for component in self.critical_components:
            if component in components:
                component_score = components[component]
                if component_score < self.min_component_score:
                    logger.warning(f"Critical component '{component}' safety score ({component_score:.4f}) "
                                 f"below minimum ({self.min_component_score:.4f})")
                    return False
            else:
                logger.warning(f"No safety data for critical component '{component}'")
                
        return True
    
    def get_description(self) -> str:
        """Get a description of the safety constraint."""
        desc = f"Requires minimum safety score of {self.min_safety_score:.2f}"
        if self.critical_components:
            desc += f" for overall system and critical components: {', '.join(self.critical_components)}"
        return desc
    
    def get_violation_details(self, system_state: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get details about the safety constraint violation.
        
        Args:
            system_state: The current state of the system.
            
        Returns:
            Dict[str, Any]: Violation details or empty dict if not violated.
        """
        if self.check(system_state):
            return {}
            
        safety = system_state.get('safety', {})
        overall_score = safety.get('overall_score', 0.0)
        components = safety.get('components', {})
        
        details = {
            'constraint_type': 'Safety',
            'violations': []
        }
        
        if overall_score < self.min_safety_score:
            details['violations'].append({
                'type': 'overall',
                'current_score': overall_score,
                'required_score': self.min_safety_score,
                'gap': self.min_safety_score - overall_score
            })
            
        for component in self.critical_components:
            if component in components:
                component_score = components[component]
                if component_score < self.min_component_score:
                    details['violations'].append({
                        'type': 'component',
                        'component': component,
                        'current_score': component_score,
                        'required_score': self.min_component_score,
                        'gap': self.min_component_score - component_score
                    })
                    
        # Add message summarizing violations
        if details['violations']:
            if len(details['violations']) == 1 and details['violations'][0]['type'] == 'overall':
                details['message'] = (f"Overall safety score ({overall_score:.4f}) "
                                    f"below required minimum ({self.min_safety_score:.4f})")
            else:
                components_str = ", ".join([f"{v['component']}: {v['current_score']:.4f}" 
                                          for v in details['violations'] if v['type'] == 'component'])
                details['message'] = f"Safety requirements not met. Affected components: {components_str}"
                
        return details


class PerformanceConstraint(EvolutionaryConstraint):
    """Constraint on minimum acceptable performance."""
    
    def __init__(self, 
                min_throughput: float, 
                max_latency: float, 
                max_error_rate: float,
                task_specific_metrics: Optional[Dict[str, Dict[str, float]]] = None):
        """
        Initialize the performance constraint.
        
        Args:
            min_throughput: Minimum acceptable throughput.
            max_latency: Maximum acceptable latency.
            max_error_rate: Maximum acceptable error rate.
            task_specific_metrics: Dict mapping task types to required metrics.
        """
        self.min_throughput = min_throughput
        self.max_latency = max_latency
        self.max_error_rate = max_error_rate
        self.task_specific_metrics = task_specific_metrics or {}
        
    def check(self, system_state: Dict[str, Any]) -> bool:
        """
        Check if performance meets minimum requirements.
        
        Args:
            system_state: The current state of the system.
            
        Returns:
            bool: True if performance is acceptable, False otherwise.
        """
        performance = system_state.get('performance', {})
        
        if not performance:
            logger.warning("No performance data available for constraint checking")
            return True  # Assume constraint is satisfied if no data
            
        # Check basic performance metrics
        throughput = performance.get('throughput', 0.0)
        latency = performance.get('latency', float('inf'))
        error_rate = performance.get('error_rate', 1.0)
        
        if throughput < self.min_throughput:
            logger.warning(f"Throughput ({throughput:.2f}) below minimum ({self.min_throughput:.2f})")
            return False
            
        if latency > self.max_latency:
            logger.warning(f"Latency ({latency:.2f}ms) above maximum ({self.max_latency:.2f}ms)")
            return False
            
        if error_rate > self.max_error_rate:
            logger.warning(f"Error rate ({error_rate:.4f}) above maximum ({self.max_error_rate:.4f})")
            return False
            
        # Check task-specific metrics if available
        task_specific = performance.get('task_specific', {})
        current_task = system_state.get('current_task', {})
        task_type = current_task.get('type', '')
        
        if task_type and task_type in self.task_specific_metrics and task_specific:
            required_metrics = self.task_specific_metrics[task_type]
            
            for metric_name, min_value in required_metrics.items():
                if metric_name in task_specific:
                    current_value = task_specific[metric_name]
                    if current_value < min_value:
                        logger.warning(f"Task-specific metric '{metric_name}' ({current_value:.4f}) "
                                     f"below minimum ({min_value:.4f}) for task type '{task_type}'")
                        return False
                else:
                    logger.warning(f"Required task-specific metric '{metric_name}' not available")
                    
        return True
    
    def get_description(self) -> str:
        """Get a description of the performance constraint."""
        return (f"Requires minimum performance: Throughput >= {self.min_throughput:.2f}, "
                f"Latency <= {self.max_latency:.2f}ms, Error rate <= {self.max_error_rate:.2%}")
    
    def get_violation_details(self, system_state: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get details about the performance constraint violation.
        
        Args:
            system_state: The current state of the system.
            
        Returns:
            Dict[str, Any]: Violation details or empty dict if not violated.
        """
        if self.check(system_state):
            return {}
            
        performance = system_state.get('performance', {})
        throughput = performance.get('throughput', 0.0)
        latency = performance.get('latency', float('inf'))
        error_rate = performance.get('error_rate', 1.0)
        
        details = {
            'constraint_type': 'Performance',
            'violations': []
        }
        
        if throughput < self.min_throughput:
            details['violations'].append({
                'metric': 'throughput',
                'current_value': throughput,
                'required_value': self.min_throughput,
                'gap_ratio': throughput / max(0.001, self.min_throughput)
            })
            
        if latency > self.max_latency:
            details['violations'].append({
                'metric': 'latency',
                'current_value': latency,
                'required_value': self.max_latency,
                'gap_ratio': self.max_latency / max(0.001, latency)
            })
            
        if error_rate > self.max_error_rate:
            details['violations'].append({
                'metric': 'error_rate',
                'current_value': error_rate,
                'required_value': self.max_error_rate,
                'gap_ratio': self.max_error_rate / max(0.001, error_rate)
            })
            
        # Check task-specific metrics
        task_specific = performance.get('task_specific', {})
        current_task = system_state.get('current_task', {})
        task_type = current_task.get('type', '')
        
        if task_type and task_type in self.task_specific_metrics and task_specific:
            required_metrics = self.task_specific_metrics[task_type]
            
            for metric_name, min_value in required_metrics.items():
                if metric_name in task_specific:
                    current_value = task_specific[metric_name]
                    if current_value < min_value:
                        details['violations'].append({
                            'metric': metric_name,
                            'task_type': task_type,
                            'current_value': current_value,
                            'required_value': min_value,
                            'gap_ratio': current_value / max(0.001, min_value)
                        })
                        
        # Add message summarizing violations
        if details['violations']:
            metrics_str = ", ".join([f"{v['metric']}: {v['current_value']:.2f}" 
                                   for v in details['violations']])
            details['message'] = f"Performance requirements not met. Violated metrics: {metrics_str}"
            
        return details


class SecurityConstraint(EvolutionaryConstraint):
    """Constraint on minimum acceptable security levels."""
    
    def __init__(self, 
                min_overall_security: float = 0.8,
                critical_vulnerabilities_allowed: int = 0,
                min_component_scores: Optional[Dict[str, float]] = None):
        """
        Initialize the security constraint.
        
        Args:
            min_overall_security: Minimum acceptable overall security score (0.0 to 1.0).
            critical_vulnerabilities_allowed: Maximum number of critical vulnerabilities allowed.
            min_component_scores: Dict mapping security component names to minimum scores.
        """
        self.min_overall_security = min_overall_security
        self.critical_vulnerabilities_allowed = critical_vulnerabilities_allowed
        self.min_component_scores = min_component_scores or {
            'authentication': 0.7,
            'encryption': 0.8,
            'data_protection': 0.8,
            'access_control': 0.7,
            'vulnerability': 0.7
        }
        
    def check(self, system_state: Dict[str, Any]) -> bool:
        """
        Check if security meets minimum requirements.
        
        Args:
            system_state: The current state of the system.
            
        Returns:
            bool: True if security is acceptable, False otherwise.
        """
        security = system_state.get('security', {})
        
        if not security:
            logger.warning("No security data available for constraint checking")
            return True  # Assume constraint is satisfied if no data
            
        # Check overall security score
        overall_score = security.get('overall_score', 0.0)
        if overall_score < self.min_overall_security:
            logger.warning(f"Overall security score ({overall_score:.4f}) "
                          f"below minimum ({self.min_overall_security:.4f})")
            return False
            
        # Check critical vulnerabilities
        critical_vulnerabilities = security.get('critical_vulnerabilities', 0)
        if critical_vulnerabilities > self.critical_vulnerabilities_allowed:
            logger.warning(f"Number of critical vulnerabilities ({critical_vulnerabilities}) "
                          f"exceeds allowed maximum ({self.critical_vulnerabilities_allowed})")
            return False
            
        # Check component-specific scores
        component_scores = security.get('component_scores', {})
        for component, min_score in self.min_component_scores.items():
            if component in component_scores:
                score = component_scores[component]
                if score < min_score:
                    logger.warning(f"Security component '{component}' score ({score:.4f}) "
                                  f"below minimum ({min_score:.4f})")
                    return False
                    
        return True
    
    def get_description(self) -> str:
        """Get a description of the security constraint."""
        component_reqs = ", ".join([f"{c}: {s:.2f}" for c, s in self.min_component_scores.items()])
        return (f"Requires minimum security: Overall score >= {self.min_overall_security:.2f}, "
                f"Critical vulnerabilities <= {self.critical_vulnerabilities_allowed}, "
                f"Component minimums: {component_reqs}")
    
    def get_violation_details(self, system_state: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get details about the security constraint violation.
        
        Args:
            system_state: The current state of the system.
            
        Returns:
            Dict[str, Any]: Violation details or empty dict if not violated.
        """
        if self.check(system_state):
            return {}
            
        security = system_state.get('security', {})
        overall_score = security.get('overall_score', 0.0)
        critical_vulnerabilities = security.get('critical_vulnerabilities', 0)
        component_scores = security.get('component_scores', {})
        
        details = {
            'constraint_type': 'Security',
            'violations': []
        }
        
        if overall_score < self.min_overall_security:
            details['violations'].append({
                'type': 'overall_score',
                'current_value': overall_score,
                'required_value': self.min_overall_security,
                'gap': self.min_overall_security - overall_score
            })
            
        if critical_vulnerabilities > self.critical_vulnerabilities_allowed:
            details['violations'].append({
                'type': 'critical_vulnerabilities',
                'current_value': critical_vulnerabilities,
                'required_value': self.critical_vulnerabilities_allowed,
                'excess': critical_vulnerabilities - self.critical_vulnerabilities_allowed
            })
            
        for component, min_score in self.min_component_scores.items():
            if component in component_scores:
                score = component_scores[component]
                if score < min_score:
                    details['violations'].append({
                        'type': 'component_score',
                        'component': component,
                        'current_value': score,
                        'required_value': min_score,
                        'gap': min_score - score
                    })
                    
        # Add message summarizing violations
        if details['violations']:
            if len(details['violations']) == 1:
                v = details['violations'][0]
                if v['type'] == 'overall_score':
                    details['message'] = (f"Overall security score ({v['current_value']:.4f}) "
                                         f"below minimum ({v['required_value']:.4f})")
                elif v['type'] == 'critical_vulnerabilities':
                    details['message'] = (f"Too many critical vulnerabilities: {v['current_value']} "
                                         f"(maximum allowed: {v['required_value']})")
                else:
                    details['message'] = (f"Security component '{v['component']}' score ({v['current_value']:.4f}) "
                                         f"below minimum ({v['required_value']:.4f})")
            else:
                violation_count = len(details['violations'])
                details['message'] = f"Multiple security requirements not met ({violation_count} violations)"
                
        return details


# ------------------------------------------------------------------------------
# Adaptation Heuristics
# ------------------------------------------------------------------------------

class AdaptationHeuristic(ABC):
    """Base class for adaptation heuristics that guide the evolution."""
    
    @abstractmethod
    def generate_adaptations(self, 
                            system_state: Dict[str, Any], 
                            environment: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Generate potential adaptations based on system state and environment.
        
        Args:
            system_state: The current state of the system.
            environment: The current environment state.
            
        Returns:
            List[Dict[str, Any]]: List of potential adaptations.
        """
        pass
    
    @abstractmethod
    def get_description(self) -> str:
        """Get a description of the adaptation heuristic."""
        pass


class PerformanceOptimizationHeuristic(AdaptationHeuristic):
    """Heuristic for optimizing system performance."""
    
    def __init__(self, performance_analyzer: Any = None):
        """
        Initialize the performance optimization heuristic.
        
        Args:
            performance_analyzer: Optional analyzer for performance profiling.
        """
        self.performance_analyzer = performance_analyzer
        self.last_analysis_time = 0
        self.analysis_interval = 3600  # 1 hour in seconds
        self.last_adaptation_time = {}  # Track when each adaptation was last suggested
        self.adaptation_cooldown = 86400  # 1 day in seconds
        
    def generate_adaptations(self, 
                            system_state: Dict[str, Any], 
                            environment: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Generate performance optimization adaptations.
        
        Args:
            system_state: The current state of the system.
            environment: The current environment state.
            
        Returns:
            List[Dict[str, Any]]: List of potential adaptations.
        """
        adaptations = []
        current_time = time.time()
        
        # Analyze bottlenecks
        bottlenecks = self._identify_bottlenecks(system_state)
        
        # Process each bottleneck for adaptations
        for bottleneck in bottlenecks:
            adaptation_key = f"{bottleneck['type']}_{bottleneck['component']}"
            
            # Check cooldown
            if (adaptation_key in self.last_adaptation_time and 
                current_time - self.last_adaptation_time[adaptation_key] < self.adaptation_cooldown):
                continue  # Skip if adaptation is on cooldown
                
            if bottleneck['type'] == 'memory':
                adaptations.append(self._create_memory_optimization(bottleneck))
                self.last_adaptation_time[adaptation_key] = current_time
                
            elif bottleneck['type'] == 'cpu':
                adaptations.append(self._create_algorithm_optimization(bottleneck))
                self.last_adaptation_time[adaptation_key] = current_time
                
            elif bottleneck['type'] == 'io':
                adaptations.append(self._create_io_optimization(bottleneck))
                self.last_adaptation_time[adaptation_key] = current_time
                
            elif bottleneck['type'] == 'latency':
                adaptations.append(self._create_latency_optimization(bottleneck))
                self.last_adaptation_time[adaptation_key] = current_time
                
            elif bottleneck['type'] == 'throughput':
                adaptations.append(self._create_throughput_optimization(bottleneck))
                self.last_adaptation_time[adaptation_key] = current_time
                
            elif bottleneck['type'] == 'error_rate':
                adaptations.append(self._create_error_handling_optimization(bottleneck))
                self.last_adaptation_time[adaptation_key] = current_time
                
        # Add general optimizations if no specific bottlenecks or few adaptations
        if not bottlenecks or len(adaptations) < 2:
            general_adaptation_key = "general_optimization"
            
            # Check cooldown for general adaptations
            if (general_adaptation_key not in self.last_adaptation_time or 
                current_time - self.last_adaptation_time[general_adaptation_key] >= self.adaptation_cooldown):
                
                adaptations.append(self._create_general_optimization(system_state))
                self.last_adaptation_time[general_adaptation_key] = current_time
                
        return adaptations
    
    def _identify_bottlenecks(self, system_state: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Identify performance bottlenecks in the system.
        
        Args:
            system_state: The current state of the system.
            
        Returns:
            List[Dict[str, Any]]: List of bottlenecks with component and severity.
        """
        bottlenecks = []
        current_time = time.time()
        
        # Use performance analyzer if available and not on cooldown
        if (self.performance_analyzer is not None and 
            current_time - self.last_analysis_time >= self.analysis_interval):
            
            try:
                analyzer_results = self.performance_analyzer.analyze(system_state)
                self.last_analysis_time = current_time
                
                # Process analyzer results
                if 'bottlenecks' in analyzer_results:
                    return analyzer_results['bottlenecks']
                    
            except Exception as e:
                logger.error(f"Error in performance analyzer: {str(e)}")
                
        # Fallback to simple heuristic analysis if analyzer not available
        
        # Check memory usage
        resources = system_state.get('resources', {})
        memory_usage = resources.get('memory_usage', 0.0)
        if memory_usage > 80.0:
            bottlenecks.append({
                'type': 'memory',
                'component': 'memory_manager',
                'severity': (memory_usage - 80.0) / 20.0,  # 0.0 to 1.0
                'value': memory_usage,
                'threshold': 80.0,
                'margin': 20.0
            })
            
        # Check CPU usage
        cpu_usage = resources.get('cpu_usage', 0.0)
        if cpu_usage > 75.0:
            bottlenecks.append({
                'type': 'cpu',
                'component': 'processing_engine',
                'severity': (cpu_usage - 75.0) / 25.0,  # 0.0 to 1.0
                'value': cpu_usage,
                'threshold': 75.0,
                'margin': 25.0
            })
            
        # Check I/O operations
        performance = system_state.get('performance', {})
        io_latency = performance.get('io_latency', 0.0)
        if io_latency > 100.0:  # ms
            bottlenecks.append({
                'type': 'io',
                'component': 'io_subsystem',
                'severity': (io_latency - 100.0) / 100.0,  # 0.0 to 1.0
                'value': io_latency,
                'threshold': 100.0,
                'margin': 100.0
            })
            
        # Check overall latency
        latency = performance.get('latency', 0.0)
        if latency > 200.0:  # ms
            bottlenecks.append({
                'type': 'latency',
                'component': 'request_processor',
                'severity': (latency - 200.0) / 300.0,  # 0.0 to 1.0
                'value': latency,
                'threshold': 200.0,
                'margin': 300.0
            })
            
        # Check throughput (lower than expected)
        throughput = performance.get('throughput', 0.0)
        expected_throughput = performance.get('expected_throughput', 100.0)
        if throughput < 0.7 * expected_throughput:
            bottlenecks.append({
                'type': 'throughput',
                'component': 'processing_pipeline',
                'severity': 1.0 - (throughput / (0.7 * expected_throughput)),  # 0.0 to 1.0
                'value': throughput,
                'threshold': 0.7 * expected_throughput,
                'margin': 0.7 * expected_throughput
            })
            
        # Check error rate
        error_rate = performance.get('error_rate', 0.0)
        if error_rate > 0.05:  # 5%
            bottlenecks.append({
                'type': 'error_rate',
                'component': 'error_handler',
                'severity': min(1.0, (error_rate - 0.05) / 0.15),  # 0.0 to 1.0
                'value': error_rate,
                'threshold': 0.05,
                'margin': 0.15
            })
            
        # Sort bottlenecks by severity (highest first)
        bottlenecks.sort(key=lambda x: x['severity'], reverse=True)
        
        # Log discovered bottlenecks
        if bottlenecks:
            logger.info(f"Identified {len(bottlenecks)} performance bottlenecks")
            for i, bottleneck in enumerate(bottlenecks):
                logger.info(f"  Bottleneck {i+1}: {bottleneck['type']} in {bottleneck['component']} "
                          f"(severity: {bottleneck['severity']:.2f})")
                
        return bottlenecks
    
    def _create_memory_optimization(self, bottleneck: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a memory optimization adaptation.
        
        Args:
            bottleneck: The memory bottleneck information.
            
        Returns:
            Dict[str, Any]: Memory optimization adaptation.
        """
        severity = bottleneck['severity']
        component = bottleneck['component']
        
        # Scale impact based on severity
        memory_reduction = 0.1 + 0.3 * severity  # 10-40% reduction
        performance_improvement = 0.03 + 0.07 * severity  # 3-10% improvement
        
        description = f"Optimize memory usage in {component}"
        suggestions = [
            "Implement more efficient data structures",
            "Add memory pooling for frequently allocated objects",
            "Reduce cache sizes or implement LRU eviction policies",
            "Optimize object lifecycles and ensure proper garbage collection"
        ]
        
        return {
            'type': 'memory_optimization',
            'target': component,
            'description': description,
            'suggestions': suggestions,
            'severity': severity,
            'expected_impact': {
                'memory': -memory_reduction,  # Reduction in memory usage
                'performance': performance_improvement  # Performance improvement
            }
        }
    
    def _create_algorithm_optimization(self, bottleneck: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create an algorithm optimization adaptation.
        
        Args:
            bottleneck: The CPU bottleneck information.
            
        Returns:
            Dict[str, Any]: Algorithm optimization adaptation.
        """
        severity = bottleneck['severity']
        component = bottleneck['component']
        
        # Scale impact based on severity
        cpu_reduction = 0.1 + 0.25 * severity  # 10-35% reduction
        performance_improvement = 0.05 + 0.15 * severity  # 5-20% improvement
        
        description = f"Optimize algorithm efficiency in {component}"
        suggestions = [
            "Refactor algorithms to reduce computational complexity",
            "Introduce caching for expensive computations",
            "Parallelize CPU-intensive operations",
            "Profile and optimize hotspots in the code"
        ]
        
        return {
            'type': 'algorithm_optimization',
            'target': component,
            'description': description,
            'suggestions': suggestions,
            'severity': severity,
            'expected_impact': {
                'cpu': -cpu_reduction,  # Reduction in CPU usage
                'performance': performance_improvement  # Performance improvement
            }
        }
    
    def _create_io_optimization(self, bottleneck: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create an I/O optimization adaptation.
        
        Args:
            bottleneck: The I/O bottleneck information.
            
        Returns:
            Dict[str, Any]: I/O optimization adaptation.
        """
        severity = bottleneck['severity']
        component = bottleneck['component']
        
        # Scale impact based on severity
        latency_reduction = 0.15 + 0.35 * severity  # 15-50% reduction
        performance_improvement = 0.05 + 0.20 * severity  # 5-25% improvement
        
        description = f"Optimize I/O operations in {component}"
        suggestions = [
            "Implement a read/write buffer system",
            "Use asynchronous I/O operations",
            "Batch I/O operations to reduce overall overhead",
            "Optimize data serialization and deserialization",
            "Compress data for I/O operations"
        ]
        
        return {
            'type': 'io_optimization',
            'target': component,
            'description': description,
            'suggestions': suggestions,
            'severity': severity,
            'expected_impact': {
                'latency': -latency_reduction,  # Reduction in I/O latency
                'performance': performance_improvement  # Performance improvement
            }
        }
    
    def _create_latency_optimization(self, bottleneck: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a latency optimization adaptation.
        
        Args:
            bottleneck: The latency bottleneck information.
            
        Returns:
            Dict[str, Any]: Latency optimization adaptation.
        """
        severity = bottleneck['severity']
        component = bottleneck['component']
        
        # Scale impact based on severity
        latency_reduction = 0.2 + 0.3 * severity  # 20-50% reduction
        
        description = f"Reduce processing latency in {component}"
        suggestions = [
            "Optimize critical path processing",
            "Implement request prioritization",
            "Add caching for frequently accessed data",
            "Streamline request handling pipeline"
        ]
        
        return {
            'type': 'latency_optimization',
            'target': component,
            'description': description,
            'suggestions': suggestions,
            'severity': severity,
            'expected_impact': {
                'latency': -latency_reduction,  # Reduction in latency
                'performance': 0.1 + 0.1 * severity  # 10-20% performance improvement
            }
        }
    
    def _create_throughput_optimization(self, bottleneck: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a throughput optimization adaptation.
        
        Args:
            bottleneck: The throughput bottleneck information.
            
        Returns:
            Dict[str, Any]: Throughput optimization adaptation.
        """
        severity = bottleneck['severity']
        component = bottleneck['component']
        
        # Scale impact based on severity
        throughput_improvement = 0.2 + 0.4 * severity  # 20-60% improvement
        
        description = f"Increase processing throughput in {component}"
        suggestions = [
            "Implement parallel processing for independent tasks",
            "Add a task queue system with multiple workers",
            "Optimize resource allocation for concurrent processing",
            "Reduce synchronization points in the processing pipeline"
        ]
        
        return {
            'type': 'throughput_optimization',
            'target': component,
            'description': description,
            'suggestions': suggestions,
            'severity': severity,
            'expected_impact': {
                'throughput': throughput_improvement,  # Improvement in throughput
                'cpu': 0.1 * severity,  # Might increase CPU usage
                'performance': 0.15 + 0.15 * severity  # 15-30% performance improvement
            }
        }
    
    def _create_error_handling_optimization(self, bottleneck: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create an error handling optimization adaptation.
        
        Args:
            bottleneck: The error rate bottleneck information.
            
        Returns:
            Dict[str, Any]: Error handling optimization adaptation.
        """
        severity = bottleneck['severity']
        component = bottleneck['component']
        
        # Scale impact based on severity
        error_reduction = 0.3 + 0.4 * severity  # 30-70% reduction
        
        description = f"Improve error handling in {component}"
        suggestions = [
            "Add robust input validation",
            "Implement error recovery mechanisms",
            "Enhance error detection and logging",
            "Add circuit breakers for failing components",
            "Implement retry mechanisms with exponential backoff"
        ]
        
        return {
            'type': 'error_handling',
            'target': component,
            'description': description,
            'suggestions': suggestions,
            'severity': severity,
            'expected_impact': {
                'error_rate': -error_reduction,  # Reduction in error rate
                'performance': -0.02 * severity,  # Small performance impact
                'reliability': 0.1 + 0.2 * severity  # 10-30% reliability improvement
            }
        }
    
    def _create_general_optimization(self, system_state: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a general system optimization adaptation.
        
        Args:
            system_state: The current system state.
            
        Returns:
            Dict[str, Any]: General optimization adaptation.
        """
        # Determine which type of general optimization to suggest
        performance = system_state.get('performance', {})
        resources = system_state.get('resources', {})
        
        throughput = performance.get('throughput', 0.0)
        latency = performance.get('latency', 100.0)
        cpu_usage = resources.get('cpu_usage', 50.0)
        
        # If throughput could be improved and CPU usage is not too high
        if cpu_usage < 70.0:
            description = "Increase parallelization of processing pipeline"
            suggestions = [
                "Decompose processing into parallel stages",
                "Implement work-stealing task scheduler",
                "Add thread pool for concurrent task execution",
                "Reduce synchronization between processing stages"
            ]
            
            return {
                'type': 'parallelization',
                'target': 'system',
                'description': description,
                'suggestions': suggestions,
                'severity': 0.5,  # Medium severity
                'expected_impact': {
                    'throughput': 0.2,  # 20% increase in throughput
                    'latency': 0.05,  # 5% increase in latency (trade-off)
                    'performance': 0.1,  # 10% overall performance improvement
                    'cpu': 0.1  # 10% increase in CPU usage
                }
            }
        # If latency is the main concern
        elif latency > 100.0:
            description = "Optimize request processing pipeline"
            suggestions = [
                "Streamline request processing steps",
                "Add caching layer for repeated operations",
                "Implement fast-path for common requests",
                "Reduce data copying between processing stages"
            ]
            
            return {
                'type': 'pipeline_optimization',
                'target': 'system',
                'description': description,
                'suggestions': suggestions,
                'severity': 0.5,  # Medium severity
                'expected_impact': {
                    'latency': -0.2,  # 20% reduction in latency
                    'throughput': 0.05,  # 5% increase in throughput
                    'performance': 0.1  # 10% overall performance improvement
                }
            }
        # Default optimization
        else:
            description = "Implement general system optimizations"
            suggestions = [
                "Profile system to identify optimization opportunities",
                "Optimize frequently used code paths",
                "Improve memory management and object lifecycle",
                "Enhance load balancing between system components"
            ]
            
            return {
                'type': 'general_optimization',
                'target': 'system',
                'description': description,
                'suggestions': suggestions,
                'severity': 0.3,  # Lower severity
                'expected_impact': {
                    'performance': 0.08,  # 8% performance improvement
                    'throughput': 0.05,  # 5% throughput improvement
                    'latency': -0.05  # 5% latency reduction
                }
            }
    
    def get_description(self) -> str:
        """Get a description of the performance optimization heuristic."""
        return "Generates adaptations to optimize system performance by identifying and addressing bottlenecks"


class EnvironmentalAdaptationHeuristic(AdaptationHeuristic):
    """Heuristic for adapting to changes in the environment."""
    
    def __init__(self, sensitivity: float = 0.5, detection_window: int = 5):
        """
        Initialize the environmental adaptation heuristic.
        
        Args:
            sensitivity: Sensitivity to environmental changes (0.0 to 1.0).
            detection_window: Number of samples to use for change detection.
        """
        self.sensitivity = sensitivity
        self.detection_window = detection_window
        self.environment_history = []
        self.last_adaptation_time = {}  # Track when each adaptation was last suggested
        self.adaptation_cooldown = 86400  # 1 day in seconds
        
    def generate_adaptations(self, 
                            system_state: Dict[str, Any], 
                            environment: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Generate adaptations in response to environmental changes.
        
        Args:
            system_state: The current state of the system.
            environment: The current environment state.
            
        Returns:
            List[Dict[str, Any]]: List of potential adaptations.
        """
        adaptations = []
        current_time = time.time()
        
        # Add environment to history
        self.environment_history.append({
            'environment': copy.deepcopy(environment),
            'timestamp': current_time
        })
        
        # Keep history limited to detection window
        if len(self.environment_history) > self.detection_window:
            self.environment_history = self.environment_history[-self.detection_window:]
        
        # If this is the first few samples, just collect data
        if len(self.environment_history) < 2:
            return adaptations
            
        # Detect changes in the environment
        changes = self._detect_environment_changes(environment)
        
        # Generate adaptations for each change
        for change in changes:
            adaptation_key = f"{change['type']}_{change['component']}"
            
            # Check cooldown
            if (adaptation_key in self.last_adaptation_time and 
                current_time - self.last_adaptation_time[adaptation_key] < self.adaptation_cooldown):
                continue  # Skip if adaptation is on cooldown
                
            if change['type'] == 'load_increase':
                adaptations.append(self._create_scale_up_adaptation(change))
                self.last_adaptation_time[adaptation_key] = current_time
                
            elif change['type'] == 'load_decrease':
                adaptations.append(self._create_scale_down_adaptation(change))
                self.last_adaptation_time[adaptation_key] = current_time
                
            elif change['type'] == 'error_rate_increase':
                adaptations.append(self._create_error_handling_adaptation(change))
                self.last_adaptation_time[adaptation_key] = current_time
                
            elif change['type'] == 'new_task_type':
                adaptations.append(self._create_new_module_adaptation(change))
                self.last_adaptation_time[adaptation_key] = current_time
                
            elif change['type'] == 'data_volume_increase':
                adaptations.append(self._create_data_scaling_adaptation(change))
                self.last_adaptation_time[adaptation_key] = current_time
                
            elif change['type'] == 'user_pattern_change':
                adaptations.append(self._create_usage_pattern_adaptation(change))
                self.last_adaptation_time[adaptation_key] = current_time
                
        return adaptations
    
    def _detect_environment_changes(self, environment: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Detect changes in the environment.
        
        Args:
            environment: The current environment state.
            
        Returns:
            List[Dict[str, Any]]: List of detected changes.
        """
        changes = []
        
        # If we don't have enough history, return empty
        if len(self.environment_history) < 2:
            return changes
            
        # Use the earliest environment in our window as baseline
        baseline_env = self.environment_history[0]['environment']
        
        # Check for load changes
        current_load = environment.get('load', 0.0)
        baseline_load = baseline_env.get('load', 0.0)
        
        # Avoid division by zero
        load_change = ((current_load - baseline_load) / 
                       max(1.0, baseline_load))
                       
        if load_change > self.sensitivity:
            changes.append({
                'type': 'load_increase',
                'component': 'request_handler',
                'magnitude': load_change,
                'baseline': baseline_load,
                'current': current_load,
                'details': f"Load increased from {baseline_load:.2f} to {current_load:.2f} ({load_change:.1%})"
            })
        elif load_change < -self.sensitivity:
            changes.append({
                'type': 'load_decrease',
                'component': 'request_handler',
                'magnitude': -load_change,
                'baseline': baseline_load,
                'current': current_load,
                'details': f"Load decreased from {baseline_load:.2f} to {current_load:.2f} ({-load_change:.1%})"
            })
            
        # Check for error rate changes
        current_error_rate = environment.get('error_rate', 0.0)
        baseline_error_rate = baseline_env.get('error_rate', 0.0)
        
        # Avoid very small baseline error rates for ratio calculation
        error_change = ((current_error_rate - baseline_error_rate) / 
                       max(0.001, baseline_error_rate))
                       
        if error_change > self.sensitivity:
            changes.append({
                'type': 'error_rate_increase',
                'component': 'error_handler',
                'magnitude': error_change,
                'baseline': baseline_error_rate,
                'current': current_error_rate,
                'details': f"Error rate increased from {baseline_error_rate:.2%} to {current_error_rate:.2%}"
            })
            
        # Check for new task types
        current_task_types = set(environment.get('task_types', []))
        baseline_task_types = set(baseline_env.get('task_types', []))
        
        new_task_types = current_task_types - baseline_task_types
        for task_type in new_task_types:
            changes.append({
                'type': 'new_task_type',
                'component': 'task_processor',
                'magnitude': 1.0,  # Fixed magnitude for new task type
                'details': f"New task type detected: {task_type}",
                'task_type': task_type
            })
            
        # Check for data volume changes
        current_data_volume = environment.get('data_volume', 0)
        baseline_data_volume = baseline_env.get('data_volume', 0)
        
        # Avoid division by zero
        if baseline_data_volume > 0:
            data_volume_change = (current_data_volume - baseline_data_volume) / baseline_data_volume
            
            if data_volume_change > self.sensitivity:
                changes.append({
                    'type': 'data_volume_increase',
                    'component': 'data_processor',
                    'magnitude': data_volume_change,
                    'baseline': baseline_data_volume,
                    'current': current_data_volume,
                    'details': f"Data volume increased from {baseline_data_volume} to {current_data_volume}"
                })
                
        # Check for user pattern changes
        current_user_patterns = environment.get('user_patterns', {})
        baseline_user_patterns = baseline_env.get('user_patterns', {})
        
        if current_user_patterns and baseline_user_patterns:
            pattern_change_score = self._calculate_pattern_change(
                current_user_patterns, baseline_user_patterns)
                
            if pattern_change_score > self.sensitivity:
                changes.append({
                    'type': 'user_pattern_change',
                    'component': 'user_interface',
                    'magnitude': pattern_change_score,
                    'details': f"User usage patterns changed significantly (score: {pattern_change_score:.2f})"
                })
                
        # Log detected changes
        if changes:
            logger.info(f"Detected {len(changes)} environmental changes")
            for i, change in enumerate(changes):
                logger.info(f"  Change {i+1}: {change['type']} in {change['component']} "
                          f"(magnitude: {change['magnitude']:.2f})")
                
        return changes
    
    def _calculate_pattern_change(self, 
                                 current_patterns: Dict[str, Any],
                                 baseline_patterns: Dict[str, Any]) -> float:
        """
        Calculate the change score between user usage patterns.
        
        Args:
            current_patterns: Current usage patterns.
            baseline_patterns: Baseline usage patterns.
            
        Returns:
            float: Change score (0.0 to 1.0).
        """
        # This is a simplified implementation
        # A real implementation would use more sophisticated pattern comparison
        
        changes = []
        
        # Compare each pattern dimension
        for key in set(current_patterns.keys()) | set(baseline_patterns.keys()):
            current_value = current_patterns.get(key, 0)
            baseline_value = baseline_patterns.get(key, 0)
            
            # Skip if both are zero
            if current_value == 0 and baseline_value == 0:
                continue
                
            # Calculate relative change
            if isinstance(current_value, (int, float)) and isinstance(baseline_value, (int, float)):
                if baseline_value != 0:
                    change = abs(current_value - baseline_value) / max(0.001, abs(baseline_value))
                else:
                    change = 1.0 if current_value != 0 else 0.0
                    
                changes.append(change)
            # Handle non-numeric values
            elif current_value != baseline_value:
                changes.append(1.0)  # Complete change
            else:
                changes.append(0.0)  # No change
                
        # Return average change score
        return sum(changes) / max(1, len(changes))
    
    def _create_scale_up_adaptation(self, change: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a scale-up adaptation for increased load.
        
        Args:
            change: The detected load increase.
            
        Returns:
            Dict[str, Any]: Scale-up adaptation.
        """
        magnitude = change['magnitude']
        component = change['component']
        
        # Scale impact based on magnitude
        capacity_increase = 0.2 + 0.3 * min(1.0, magnitude)  # 20-50% increase
        resource_increase = 0.15 + 0.25 * min(1.0, magnitude)  # 15-40% increase
        
        description = f"Scale up to handle increased load in {component}"
        suggestions = [
            "Add more processing instances/threads",
            "Increase resource allocation for processing",
            "Implement load balancing across components",
            "Enable auto-scaling based on load metrics"
        ]
        
        return {
            'type': 'scale_up',
            'target': component,
            'description': description,
            'suggestions': suggestions,
            'magnitude': magnitude,
            'expected_impact': {
                'capacity': capacity_increase,  # Increase in capacity
                'resource_usage': resource_increase,  # Increase in resource usage
                'performance': 0.1  # Performance improvement
            }
        }
    
    def _create_scale_down_adaptation(self, change: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a scale-down adaptation for decreased load.
        
        Args:
            change: The detected load decrease.
            
        Returns:
            Dict[str, Any]: Scale-down adaptation.
        """
        magnitude = change['magnitude']
        component = change['component']
        
        # Scale impact based on magnitude
        capacity_decrease = 0.15 + 0.25 * min(1.0, magnitude)  # 15-40% decrease
        resource_decrease = 0.2 + 0.3 * min(1.0, magnitude)  # 20-50% decrease
        
        description = f"Scale down to conserve resources due to decreased load in {component}"
        suggestions = [
            "Reduce number of processing instances/threads",
            "Decrease resource allocation for processing",
            "Consolidate workloads on fewer resources",
            "Implement more aggressive resource recycling"
        ]
        
        return {
            'type': 'scale_down',
            'target': component,
            'description': description,
            'suggestions': suggestions,
            'magnitude': magnitude,
            'expected_impact': {
                'capacity': -capacity_decrease,  # Decrease in capacity
                'resource_usage': -resource_decrease,  # Decrease in resource usage
                'efficiency': 0.2  # Efficiency improvement
            }
        }
    
    def _create_error_handling_adaptation(self, change: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create an error handling adaptation for increased error rates.
        
        Args:
            change: The detected error rate increase.
            
        Returns:
            Dict[str, Any]: Error handling adaptation.
        """
        magnitude = change['magnitude']
        component = change['component']
        
        # Scale impact based on magnitude
        error_reduction = 0.3 + 0.3 * min(1.0, magnitude)  # 30-60% reduction
        performance_impact = 0.05 * min(1.0, magnitude)  # 0-5% performance decrease
        
        description = f"Enhance error handling in {component}"
        suggestions = [
            "Implement more robust error detection",
            "Add circuit breakers for failing components",
            "Create fallback mechanisms for error conditions",
            "Improve input validation and error prevention"
        ]
        
        return {
            'type': 'error_handling',
            'target': component,
            'description': description,
            'suggestions': suggestions,
            'magnitude': magnitude,
            'expected_impact': {
                'error_rate': -error_reduction,  # Reduction in error rate
                'performance': -performance_impact,  # Small performance impact
                'reliability': 0.25  # Reliability improvement
            }
        }
    
    def _create_new_module_adaptation(self, change: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create an adaptation for handling a new task type.
        
        Args:
            change: The detected new task type.
            
        Returns:
            Dict[str, Any]: New module adaptation.
        """
        task_type = change.get('task_type', 'unknown')
        component = change['component']
        
        description = f"Add new module to handle task type: {task_type}"
        suggestions = [
            f"Create specialized processor for {task_type} tasks",
            "Implement handlers for the new task requirements",
            "Add appropriate validation for new task inputs",
            "Update task routing logic to direct to new module"
        ]
        
        return {
            'type': 'new_module',
            'target': component,
            'description': description,
            'suggestions': suggestions,
            'magnitude': 1.0,
            'task_type': task_type,
            'expected_impact': {
                'capabilities': 0.2,  # Increase in capabilities
                'complexity': 0.15,  # Increase in complexity
                'task_type_support': 1.0  # Complete support for new task type
            }
        }
    
    def _create_data_scaling_adaptation(self, change: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create an adaptation for increased data volume.
        
        Args:
            change: The detected data volume increase.
            
        Returns:
            Dict[str, Any]: Data scaling adaptation.
        """
        magnitude = change['magnitude']
        component = change['component']
        
        # Scale impact based on magnitude
        efficiency_improvement = 0.2 + 0.2 * min(1.0, magnitude)  # 20-40% improvement
        
        description = f"Scale data processing capabilities in {component}"
        suggestions = [
            "Implement data sharding/partitioning",
            "Add batched processing for large datasets",
            "Optimize data structures for larger volumes",
            "Implement incremental processing techniques"
        ]
        
        return {
            'type': 'data_scaling',
            'target': component,
            'description': description,
            'suggestions': suggestions,
            'magnitude': magnitude,
            'expected_impact': {
                'data_processing_capacity': 0.3 + 0.3 * min(1.0, magnitude),  # 30-60% increase
                'memory_usage': 0.1 * min(1.0, magnitude),  # Up to 10% increase
                'efficiency': efficiency_improvement  # Processing efficiency
            }
        }
    
    def _create_usage_pattern_adaptation(self, change: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create an adaptation for changed user usage patterns.
        
        Args:
            change: The detected pattern change.
            
        Returns:
            Dict[str, Any]: Usage pattern adaptation.
        """
        magnitude = change['magnitude']
        component = change['component']
        
        description = f"Adapt to changed user usage patterns in {component}"
        suggestions = [
            "Update prioritization of user flows",
            "Adjust caching strategy for new usage patterns",
            "Reoptimize resource allocation for common patterns",
            "Update UI/UX for common usage flows"
        ]
        
        return {
            'type': 'pattern_adaptation',
            'target': component,
            'description': description,
            'suggestions': suggestions,
            'magnitude': magnitude,
            'expected_impact': {
                'user_satisfaction': 0.15,  # User satisfaction improvement
                'performance': 0.1,  # Performance improvement for common cases
                'resource_efficiency': 0.05  # Small efficiency gain
            }
        }
    
    def get_description(self) -> str:
        """Get a description of the environmental adaptation heuristic."""
        return f"Generates adaptations in response to environmental changes with sensitivity {self.sensitivity:.2f}"


class SecurityAdaptationHeuristic(AdaptationHeuristic):
    """Heuristic for adapting system security based on threats and vulnerabilities."""
    
    def __init__(self, proactive_level: float = 0.5):
        """
        Initialize the security adaptation heuristic.
        
        Args:
            proactive_level: Level of proactive security improvements (0.0 to 1.0).
        """
        self.proactive_level = proactive_level
        self.threat_history = []
        self.last_adaptation_time = {}  # Track when each adaptation was last suggested
        self.adaptation_cooldown = 604800  # 1 week in seconds
        self.min_proactive_interval = 2592000  # 30 days for proactive suggestions
        self.last_proactive_time = time.time() - self.min_proactive_interval  # Allow initial proactive
        
    def generate_adaptations(self, 
                            system_state: Dict[str, Any], 
                            environment: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Generate security-related adaptations.
        
        Args:
            system_state: The current state of the system.
            environment: The current environment state.
            
        Returns:
            List[Dict[str, Any]]: List of potential adaptations.
        """
        adaptations = []
        current_time = time.time()
        
        # Get security state
        security = system_state.get('security', {})
        if not security:
            logger.warning("No security data available for generating adaptations")
            return adaptations
            
        # Record threat information
        threats = security.get('threats', [])
        if threats:
            self.threat_history.append({
                'threats': threats,
                'timestamp': current_time
            })
            
        # Generate reactive adaptations for active threats
        for threat in threats:
            adaptation_key = f"threat_{threat['type']}_{threat['target']}"
            
            # Check cooldown
            if (adaptation_key in self.last_adaptation_time and 
                current_time - self.last_adaptation_time[adaptation_key] < self.adaptation_cooldown):
                continue  # Skip if adaptation is on cooldown
                
            adaptations.append(self._create_threat_mitigation(threat))
            self.last_adaptation_time[adaptation_key] = current_time
            
        # Get vulnerability information
        vulnerabilities = security.get('vulnerabilities', [])
        
        # Generate adaptations for vulnerabilities
        for vulnerability in vulnerabilities:
            adaptation_key = f"vulnerability_{vulnerability['type']}_{vulnerability['component']}"
            
            # Check cooldown
            if (adaptation_key in self.last_adaptation_time and 
                current_time - self.last_adaptation_time[adaptation_key] < self.adaptation_cooldown):
                continue  # Skip if adaptation is on cooldown
                
            adaptations.append(self._create_vulnerability_fix(vulnerability))
            self.last_adaptation_time[adaptation_key] = current_time
            
        # Generate proactive security improvements if enough time has passed
        if (current_time - self.last_proactive_time >= self.min_proactive_interval and 
            self.proactive_level > 0.0):
            
            # Generate proactive adaptations
            proactive_adaptations = self._generate_proactive_adaptations(system_state)
            
            # Add to adaptation list
            adaptations.extend(proactive_adaptations)
            
            # Update last proactive time
            if proactive_adaptations:
                self.last_proactive_time = current_time
                
        return adaptations
    
    def _create_threat_mitigation(self, threat: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create an adaptation to mitigate an active threat.
        
        Args:
            threat: Information about the threat.
            
        Returns:
            Dict[str, Any]: Threat mitigation adaptation.
        """
        threat_type = threat.get('type', 'unknown')
        severity = threat.get('severity', 0.5)
        target = threat.get('target', 'system')
        
        # Create appropriate mitigation based on threat type
        if threat_type == 'intrusion':
            description = f"Mitigate intrusion attempt targeting {target}"
            suggestions = [
                "Implement additional access controls",
                "Add IP blocking for suspicious sources",
                "Enhance authentication requirements",
                "Deploy additional intrusion detection mechanisms"
            ]
            
            return {
                'type': 'intrusion_mitigation',
                'target': target,
                'description': description,
                'suggestions': suggestions,
                'severity': severity,
                'expected_impact': {
                    'security': 0.2,  # Security improvement
                    'performance': -0.05,  # Small performance impact
                    'user_experience': -0.03  # Small user experience impact
                }
            }
            
        elif threat_type == 'data_leak':
            description = f"Prevent data leakage from {target}"
            suggestions = [
                "Implement data loss prevention controls",
                "Add encryption for sensitive data",
                "Update data access policies",
                "Implement data access auditing"
            ]
            
            return {
                'type': 'data_protection',
                'target': target,
                'description': description,
                'suggestions': suggestions,
                'severity': severity,
                'expected_impact': {
                    'security': 0.25,  # Security improvement
                    'performance': -0.02,  # Very small performance impact
                    'privacy': 0.3  # Privacy improvement
                }
            }
            
        elif threat_type == 'dos':
            description = f"Mitigate denial of service attack targeting {target}"
            suggestions = [
                "Implement rate limiting",
                "Add request filtering",
                "Deploy traffic analysis and blocking",
                "Set up redundant processing paths"
            ]
            
            return {
                'type': 'dos_protection',
                'target': target,
                'description': description,
                'suggestions': suggestions,
                'severity': severity,
                'expected_impact': {
                    'security': 0.2,  # Security improvement
                    'performance': -0.08,  # Performance impact
                    'reliability': 0.15  # Reliability improvement
                }
            }
            
        # Default for other threat types
        else:
            description = f"Mitigate {threat_type} threat targeting {target}"
            suggestions = [
                "Implement additional security controls",
                "Add monitoring for suspicious activities",
                "Update security policies",
                "Perform security audit"
            ]
            
            return {
                'type': 'security_hardening',
                'target': target,
                'description': description,
                'suggestions': suggestions,
                'severity': severity,
                'expected_impact': {
                    'security': 0.15,  # Security improvement
                    'performance': -0.03  # Small performance impact
                }
            }
    
    def _create_vulnerability_fix(self, vulnerability: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create an adaptation to fix a vulnerability.
        
        Args:
            vulnerability: Information about the vulnerability.
            
        Returns:
            Dict[str, Any]: Vulnerability fix adaptation.
        """
        vuln_type = vulnerability.get('type', 'unknown')
        severity = vulnerability.get('severity', 0.5)
        component = vulnerability.get('component', 'system')
        
        # Create appropriate fix based on vulnerability type
        if vuln_type == 'code_injection':
            description = f"Fix code injection vulnerability in {component}"
            suggestions = [
                "Implement input validation and sanitization",
                "Use parameterized queries/statements",
                "Apply context-sensitive escaping",
                "Add security headers and controls"
            ]
            
            return {
                'type': 'injection_prevention',
                'target': component,
                'description': description,
                'suggestions': suggestions,
                'severity': severity,
                'expected_impact': {
                    'security': 0.3,  # Significant security improvement
                    'performance': -0.01  # Minimal performance impact
                }
            }
            
        elif vuln_type == 'authentication':
            description = f"Strengthen authentication in {component}"
            suggestions = [
                "Implement multi-factor authentication",
                "Enforce strong password policies",
                "Add rate limiting for authentication attempts",
                "Use secure session management"
            ]
            
            return {
                'type': 'authentication_hardening',
                'target': component,
                'description': description,
                'suggestions': suggestions,
                'severity': severity,
                'expected_impact': {
                    'security': 0.25,  # Security improvement
                    'user_experience': -0.05  # Small user experience impact
                }
            }
            
        elif vuln_type == 'outdated_dependency':
            description = f"Update outdated dependencies in {component}"
            suggestions = [
                "Upgrade to latest secure version",
                "Implement dependency scanning",
                "Set up automated dependency updates",
                "Add vulnerability monitoring"
            ]
            
            return {
                'type': 'dependency_update',
                'target': component,
                'description': description,
                'suggestions': suggestions,
                'severity': severity,
                'expected_impact': {
                    'security': 0.2,  # Security improvement
                    'stability': -0.05,  # Small stability impact (risk)
                    'maintenance': 0.1  # Improved maintainability
                }
            }
            
        # Default for other vulnerability types
        else:
            description = f"Fix {vuln_type} vulnerability in {component}"
            suggestions = [
                "Implement security best practices",
                "Add security testing",
                "Update security controls",
                "Perform code review"
            ]
            
            return {
                'type': 'vulnerability_remediation',
                'target': component,
                'description': description,
                'suggestions': suggestions,
                'severity': severity,
                'expected_impact': {
                    'security': 0.15,  # Security improvement
                    'performance': -0.02  # Small performance impact
                }
            }
    
    def _generate_proactive_adaptations(self, system_state: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Generate proactive security adaptations.
        
        Args:
            system_state: The current state of the system.
            
        Returns:
            List[Dict[str, Any]]: List of proactive adaptations.
        """
        adaptations = []
        current_time = time.time()
        
        # Get security state
        security = system_state.get('security', {})
        if not security:
            return adaptations
            
        # Get component scores
        component_scores = security.get('component_scores', {})
        
        # Find weakest security areas
        weak_components = []
        for component, score in component_scores.items():
            if score < 0.7:  # Consider components with score < 70% as weak
                weak_components.append((component, score))
                
        # Sort by score (weakest first)
        weak_components.sort(key=lambda x: x[1])
        
        # Generate adaptations for weakest components
        for component, score in weak_components[:2]:  # Focus on top 2 weakest
            adaptation_key = f"proactive_{component}"
            
            # Check cooldown
            if (adaptation_key in self.last_adaptation_time and 
                current_time - self.last_adaptation_time[adaptation_key] < self.adaptation_cooldown):
                continue  # Skip if adaptation is on cooldown
                
            description = f"Proactively enhance security of {component}"
            suggestions = [
                f"Conduct security review of {component}",
                "Implement additional security controls",
                "Add security testing and monitoring",
                "Apply security hardening practices"
            ]
            
            adaptation = {
                'type': 'proactive_security',
                'target': component,
                'description': description,
                'suggestions': suggestions,
                'severity': 0.3,  # Medium severity
                'expected_impact': {
                    'security': 0.15,  # Security improvement
                    'performance': -0.01,  # Minimal performance impact
                    'maintainability': 0.05  # Improved maintainability
                }
            }
            
            adaptations.append(adaptation)
            self.last_adaptation_time[adaptation_key] = current_time
            
        # Add general security improvement if we haven't reached adaptation limit
        if len(adaptations) < 2 and self.proactive_level > 0.5:
            adaptation_key = "proactive_general"
            
            # Check cooldown
            if (adaptation_key not in self.last_adaptation_time or 
                current_time - self.last_adaptation_time[adaptation_key] >= self.adaptation_cooldown):
                
                description = "Implement system-wide security improvements"
                suggestions = [
                    "Conduct comprehensive security audit",
                    "Update security policies and controls",
                    "Implement security monitoring and alerting",
                    "Enhance encryption and access controls"
                ]
                
                adaptation = {
                    'type': 'security_enhancement',
                    'target': 'system',
                    'description': description,
                    'suggestions': suggestions,
                    'severity': 0.2,  # Lower severity
                    'expected_impact': {
                        'security': 0.1,  # Security improvement
                        'compliance': 0.15,  # Compliance improvement
                        'performance': -0.01  # Minimal performance impact
                    }
                }
                
                adaptations.append(adaptation)
                self.last_adaptation_time[adaptation_key] = current_time
                
        return adaptations
    
    def get_description(self) -> str:
        """Get a description of the security adaptation heuristic."""
        return f"Generates security adaptations based on threats and vulnerabilities with proactive level {self.proactive_level:.2f}"


class ArchitecturalAdaptationHeuristic(AdaptationHeuristic):
    """Heuristic for adapting system architecture to improve overall capabilities."""
    
    def __init__(self, innovation_level: float = 0.5):
        """
        Initialize the architectural adaptation heuristic.
        
        Args:
            innovation_level: Level of innovative suggestions (0.0 to 1.0).
        """
        self.innovation_level = innovation_level
        self.last_adaptation_time = {}
        self.adaptation_cooldown = 2592000  # 30 days in seconds
        
    def generate_adaptations(self, 
                            system_state: Dict[str, Any], 
                            environment: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Generate architectural adaptation suggestions.
        
        Args:
            system_state: The current state of the system.
            environment: The current environment state.
            
        Returns:
            List[Dict[str, Any]]: List of potential adaptations.
        """
        adaptations = []
        current_time = time.time()
        
        # Analyze system architecture
        architectural_gaps = self._identify_architectural_gaps(system_state, environment)
        
        # Generate adaptations for architectural gaps
        for gap in architectural_gaps:
            adaptation_key = f"architecture_{gap['type']}"
            
            # Check cooldown
            if (adaptation_key in self.last_adaptation_time and 
                current_time - self.last_adaptation_time[adaptation_key] < self.adaptation_cooldown):
                continue  # Skip if adaptation is on cooldown
                
            adaptation = self._create_architectural_adaptation(gap)
            adaptations.append(adaptation)
            self.last_adaptation_time[adaptation_key] = current_time
            
        # Add innovative architectural improvements based on innovation level
        if len(adaptations) < 3 and np.random.rand() < self.innovation_level:
            adaptation_key = "architecture_innovation"
            
            # Check cooldown for innovation
            if (adaptation_key not in self.last_adaptation_time or 
                current_time - self.last_adaptation_time[adaptation_key] >= self.adaptation_cooldown * 2):
                
                innovative_adaptation = self._create_innovative_adaptation(system_state, environment)
                adaptations.append(innovative_adaptation)
                self.last_adaptation_time[adaptation_key] = current_time
                
        return adaptations
    
    def _identify_architectural_gaps(self, 
                                    system_state: Dict[str, Any], 
                                    environment: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Identify gaps or improvement opportunities in the system architecture.
        
        Args:
            system_state: The current state of the system.
            environment: The current environment state.
            
        Returns:
            List[Dict[str, Any]]: List of architectural gaps.
        """
        gaps = []
        
        # Check for performance bottlenecks
        performance = system_state.get('performance', {})
        if performance:
            throughput = performance.get('throughput', 0.0)
            expected_throughput = performance.get('expected_throughput', throughput * 1.5)
            
            if throughput < 0.6 * expected_throughput:
                gaps.append({
                    'type': 'scalability',
                    'severity': min(1.0, 1.0 - (throughput / expected_throughput)),
                    'details': f"System throughput ({throughput:.2f}) below expectations ({expected_throughput:.2f})"
                })
                
        # Check for reliability issues
        reliability = system_state.get('reliability', {})
        if reliability:
            uptime = reliability.get('uptime', 100.0)
            error_rate = reliability.get('error_rate', 0.0)
            
            if uptime < 99.5 or error_rate > 0.05:
                gaps.append({
                    'type': 'reliability',
                    'severity': min(1.0, (100.0 - uptime) / 10.0 + error_rate),
                    'details': f"Reliability concerns: Uptime={uptime:.2f}%, Error rate={error_rate:.2%}"
                })
                
        # Check for adaptability issues
        adaptability = system_state.get('adaptability', {})
        if adaptability:
            learning_rate = adaptability.get('learning_rate', 1.0)
            generalization = adaptability.get('generalization', 1.0)
            
            if learning_rate < 0.7 or generalization < 0.6:
                gaps.append({
                    'type': 'adaptability',
                    'severity': min(1.0, (1.0 - learning_rate) + (1.0 - generalization)),
                    'details': f"Adaptability concerns: Learning rate={learning_rate:.2f}, Generalization={generalization:.2f}"
                })
                
        # Check for integration gaps
        integration = system_state.get('integration', {})
        if integration:
            component_coupling = integration.get('component_coupling', 0.5)
            interoperability = integration.get('interoperability', 1.0)
            
            if component_coupling > 0.7 or interoperability < 0.7:
                gaps.append({
                    'type': 'integration',
                    'severity': min(1.0, component_coupling + (1.0 - interoperability)),
                    'details': f"Integration concerns: Coupling={component_coupling:.2f}, Interoperability={interoperability:.2f}"
                })
                
        # Check for maintainability issues
        maintainability = system_state.get('maintainability', {})
        if maintainability:
            code_quality = maintainability.get('code_quality', 1.0)
            technical_debt = maintainability.get('technical_debt', 0.0)
            
            if code_quality < 0.7 or technical_debt > 0.3:
                gaps.append({
                    'type': 'maintainability',
                    'severity': min(1.0, (1.0 - code_quality) + technical_debt),
                    'details': f"Maintainability concerns: Code quality={code_quality:.2f}, Technical debt={technical_debt:.2f}"
                })
                
        # Check for modularity issues
        modularity = system_state.get('modularity', {})
        if modularity:
            cohesion = modularity.get('cohesion', 1.0)
            coupling = modularity.get('coupling', 0.0)
            
            if cohesion < 0.7 or coupling > 0.4:
                gaps.append({
                    'type': 'modularity',
                    'severity': min(1.0, (1.0 - cohesion) + coupling),
                    'details': f"Modularity concerns: Cohesion={cohesion:.2f}, Coupling={coupling:.2f}"
                })
                
        # Sort gaps by severity
        gaps.sort(key=lambda x: x['severity'], reverse=True)
        
        # Log gaps
        if gaps:
            logger.info(f"Identified {len(gaps)} architectural gaps")
            for i, gap in enumerate(gaps):
                logger.info(f"  Gap {i+1}: {gap['type']} (severity: {gap['severity']:.2f}) - {gap['details']}")
                
        return gaps
    
    def _create_architectural_adaptation(self, gap: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create an architectural adaptation based on an identified gap.
        
        Args:
            gap: Information about the architectural gap.
            
        Returns:
            Dict[str, Any]: Architectural adaptation.
        """
        gap_type = gap['type']
        severity = gap['severity']
        
        if gap_type == 'scalability':
            description = "Enhance system scalability architecture"
            suggestions = [
                "Implement microservices architecture",
                "Add load balancing and auto-scaling capabilities",
                "Introduce sharding or partitioning for data",
                "Optimize resource allocation and utilization"
            ]
            
            return {
                'type': 'architecture_scalability',
                'target': 'system',
                'description': description,
                'suggestions': suggestions,
                'severity': severity,
                'expected_impact': {
                    'scalability': 0.3,  # Scalability improvement
                    'throughput': 0.25,  # Throughput improvement
                    'complexity': 0.1,  # Complexity increase
                    'maintainability': -0.05  # Small maintainability impact
                }
            }
            
        elif gap_type == 'reliability':
            description = "Improve system reliability architecture"
            suggestions = [
                "Implement circuit breakers and bulkheads",
                "Add redundancy and failover mechanisms",
                "Enhance error recovery systems",
                "Implement health checking and self-healing"
            ]
            
            return {
                'type': 'architecture_reliability',
                'target': 'system',
                'description': description,
                'suggestions': suggestions,
                'severity': severity,
                'expected_impact': {
                    'reliability': 0.3,  # Reliability improvement
                    'error_rate': -0.15,  # Error rate reduction
                    'complexity': 0.08,  # Complexity increase
                    'performance': -0.02  # Small performance impact
                }
            }
            
        elif gap_type == 'adaptability':
            description = "Enhance system adaptability architecture"
            suggestions = [
                "Implement a modular, plugin-based architecture",
                "Add feature toggles and dynamic configuration",
                "Create abstraction layers for key components",
                "Implement machine learning for system adaptation"
            ]
            
            return {
                'type': 'architecture_adaptability',
                'target': 'system',
                'description': description,
                'suggestions': suggestions,
                'severity': severity,
                'expected_impact': {
                    'adaptability': 0.25,  # Adaptability improvement
                    'flexibility': 0.2,  # Flexibility improvement
                    'complexity': 0.1,  # Complexity increase
                    'performance': -0.02  # Small performance impact
                }
            }
            
        elif gap_type == 'integration':
            description = "Improve system integration architecture"
            suggestions = [
                "Implement service-oriented architecture (SOA)",
                "Add API gateway for component interactions",
                "Create standardized interfaces between components",
                "Use event-driven architecture for loosely coupled integration"
            ]
            
            return {
                'type': 'architecture_integration',
                'target': 'system',
                'description': description,
                'suggestions': suggestions,
                'severity': severity,
                'expected_impact': {
                    'integration': 0.3,  # Integration improvement
                    'interoperability': 0.25,  # Interoperability improvement
                    'coupling': -0.2,  # Coupling reduction
                    'complexity': 0.05  # Small complexity increase
                }
            }
            
        elif gap_type == 'maintainability':
            description = "Enhance system maintainability architecture"
            suggestions = [
                "Refactor toward clean architecture principles",
                "Implement domain-driven design (DDD)",
                "Add comprehensive testing infrastructure",
                "Create better separation of concerns in architecture"
            ]
            
            return {
                'type': 'architecture_maintainability',
                'target': 'system',
                'description': description,
                'suggestions': suggestions,
                'severity': severity,
                'expected_impact': {
                    'maintainability': 0.25,  # Maintainability improvement
                    'code_quality': 0.2,  # Code quality improvement
                    'technical_debt': -0.15,  # Technical debt reduction
                    'development_speed': 0.1  # Development speed improvement
                }
            }
            
        elif gap_type == 'modularity':
            description = "Improve system modularity architecture"
            suggestions = [
                "Refactor toward more cohesive modules",
                "Implement dependency injection",
                "Create clear boundaries between components",
                "Use interface-based design for component interactions"
            ]
            
            return {
                'type': 'architecture_modularity',
                'target': 'system',
                'description': description,
                'suggestions': suggestions,
                'severity': severity,
                'expected_impact': {
                    'modularity': 0.3,  # Modularity improvement
                    'maintainability': 0.2,  # Maintainability improvement
                    'flexibility': 0.15,  # Flexibility improvement
                    'development_speed': 0.1  # Development speed improvement
                }
            }
            
        # Default for other gap types
        else:
            description = f"Improve system architecture ({gap_type})"
            suggestions = [
                "Analyze system architecture for improvements",
                "Refactor problematic components",
                "Apply architectural patterns and best practices",
                "Implement architecture governance"
            ]
            
            return {
                'type': 'architecture_improvement',
                'target': 'system',
                'description': description,
                'suggestions': suggestions,
                'severity': severity,
                'expected_impact': {
                    'architecture_quality': 0.15,  # Architecture quality improvement
                    'maintainability': 0.1,  # Maintainability improvement
                    'complexity': 0.05  # Small complexity increase
                }
            }
    
    def _create_innovative_adaptation(self, 
                                     system_state: Dict[str, Any], 
                                     environment: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create an innovative architectural adaptation.
        
        Args:
            system_state: The current state of the system.
            environment: The current environment state.
            
        Returns:
            Dict[str, Any]: Innovative adaptation.
        """
        # Select an innovative architectural approach based on system needs
        # and innovation level
        
        # Higher innovation level = more novel/experimental approaches
        innovation_options = [
            {
                'type': 'serverless',
                'description': "Implement serverless architecture",
                'suggestions': [
                    "Refactor components into function-as-a-service model",
                    "Implement event-driven processing",
                    "Use managed services for infrastructure",
                    "Design for stateless processing"
                ],
                'novelty': 0.3,  # Moderate novelty
                'expected_impact': {
                    'scalability': 0.25,
                    'operational_cost': -0.2,
                    'development_complexity': 0.1
                }
            },
            {
                'type': 'reactive',
                'description': "Implement reactive architecture pattern",
                'suggestions': [
                    "Use reactive programming model",
                    "Implement non-blocking I/O throughout",
                    "Design for resilience and elasticity",
                    "Use message-driven communication"
                ],
                'novelty': 0.5,  # Medium novelty
                'expected_impact': {
                    'responsiveness': 0.3,
                    'resilience': 0.25,
                    'throughput': 0.2,
                    'learning_curve': 0.15
                }
            },
            {
                'type': 'event_sourcing',
                'description': "Implement event sourcing and CQRS",
                'suggestions': [
                    "Store state changes as event streams",
                    "Separate command and query responsibilities",
                    "Use event-driven architecture",
                    "Implement eventual consistency model"
                ],
                'novelty': 0.6,  # Medium-high novelty
                'expected_impact': {
                    'scalability': 0.2,
                    'audit_capability': 0.3,
                    'flexibility': 0.25,
                    'complexity': 0.2
                }
            },
            {
                'type': 'self_healing',
                'description': "Implement self-healing architecture",
                'suggestions': [
                    "Add autonomous health monitoring",
                    "Implement automatic recovery mechanisms",
                    "Use chaos engineering principles",
                    "Design for graceful degradation"
                ],
                'novelty': 0.7,  # High novelty
                'expected_impact': {
                    'reliability': 0.3,
                    'uptime': 0.2,
                    'operational_cost': -0.15,
                    'complexity': 0.25
                }
            },
            {
                'type': 'mesh',
                'description': "Implement service mesh architecture",
                'suggestions': [
                    "Add service discovery and routing",
                    "Implement distributed tracing",
                    "Use circuit breaking and fault tolerance",
                    "Centralize security policies"
                ],
                'novelty': 0.65,  # Medium-high novelty
                'expected_impact': {
                    'observability': 0.35,
                    'security': 0.2,
                    'reliability': 0.2,
                    'operational_overhead': 0.15
                }
            },
            {
                'type': 'neural_architecture',
                'description': "Implement neural architecture search for system optimization",
                'suggestions': [
                    "Use machine learning to optimize component structure",
                    "Implement adaptive neural networks for processing",
                    "Auto-tune system parameters based on usage patterns",
                    "Develop self-optimizing processing pipelines"
                ],
                'novelty': 0.9,  # Very high novelty
                'expected_impact': {
                    'adaptability': 0.4,
                    'performance': 0.25,
                    'complexity': 0.35,
                    'resource_usage': 0.2
                }
            }
        ]
        
        # Filter options based on innovation level
        viable_options = [
            option for option in innovation_options
            if option['novelty'] <= self.innovation_level + 0.2  # Allow slightly higher than set level
        ]
        
        if not viable_options:
            viable_options = innovation_options[:2]  # Use the least novel options
            
        # Select an option considering system needs
        selected = np.random.choice(viable_options)
        
        # Customize the adaptation
        adaptation = {
            'type': f"architecture_{selected['type']}",
            'target': 'system',
            'description': selected['description'],
            'suggestions': selected['suggestions'],
            'severity': 0.4,  # Medium severity
            'novelty': selected['novelty'],
            'expected_impact': selected['expected_impact']
        }
        
        return adaptation
    
    def get_description(self) -> str:
        """Get a description of the architectural adaptation heuristic."""
        return f"Generates architectural adaptations to improve system capabilities with innovation level {self.innovation_level:.2f}"


# ------------------------------------------------------------------------------
# Evolutionary Steering
# ------------------------------------------------------------------------------

class EvolutionarySteering:
    """
    Guides the self-evolution process toward desirable properties through
    fitness functions, constraints, and adaptation heuristics.
    """
    
    def __init__(self, 
                 fitness_functions: List[FitnessFunction],
                 constraints: List[EvolutionaryConstraint],
                 adaptation_heuristics: List[AdaptationHeuristic],
                 fitness_weights: Optional[List[float]] = None):
        """
        Initialize the evolutionary steering system.
        
        Args:
            fitness_functions: List of fitness functions to evaluate system state.
            constraints: List of constraints on the evolution process.
            adaptation_heuristics: List of heuristics to generate adaptations.
            fitness_weights: Optional weights for the fitness functions.
        """
        self.fitness_functions = fitness_functions
        self.constraints = constraints
        self.adaptation_heuristics = adaptation_heuristics
        
        # Set equal weights if not provided
        if fitness_weights is None:
            self.fitness_weights = [1.0 / len(fitness_functions)] * len(fitness_functions)
        else:
            # Normalize weights to sum to 1.0
            total_weight = sum(fitness_weights)
            self.fitness_weights = [w / total_weight for w in fitness_weights]
            
        self.progress_history = []
        self.adaptation_history = []
        
        logger.info(f"Initialized Evolutionary Steering with {len(fitness_functions)} fitness functions, "
                   f"{len(constraints)} constraints, and {len(adaptation_heuristics)} adaptation heuristics.")
        
    def evaluate_system_state(self, system_state: Dict[str, Any]) -> Dict[str, Any]:
        """
        Evaluate the current system state.
        
        Args:
            system_state: The current state of the system.
            
        Returns:
            Dict[str, Any]: Evaluation results.
        """
        evaluation = {
            'timestamp': time.time(),
            'overall_fitness': 0.0,
            'fitness_scores': {},
            'constraints_satisfied': True,
            'constraint_results': {},
            'valid': True,
            'violations': []
        }
        
        # Evaluate fitness functions
        fitness_scores = []
        for i, fitness_func in enumerate(self.fitness_functions):
            try:
                score = fitness_func.evaluate(system_state)
                function_name = fitness_func.__class__.__name__
                evaluation['fitness_scores'][function_name] = score
                fitness_scores.append(score)
                
                logger.debug(f"Fitness function '{function_name}' score: {score:.4f}")
                
            except Exception as e:
                logger.error(f"Error evaluating fitness function {i}: {str(e)}")
                # Use a neutral score in case of error
                fitness_scores.append(0.5)
                
        # Calculate overall fitness with weighted sum
        if fitness_scores:
            overall_fitness = sum(score * weight for score, weight in zip(fitness_scores, self.fitness_weights))
            evaluation['overall_fitness'] = overall_fitness
            
            logger.info(f"Overall system fitness: {overall_fitness:.4f}")
        else:
            logger.warning("No fitness functions could be evaluated")
            
        # Check constraints
        for constraint in self.constraints:
            constraint_name = constraint.__class__.__name__
            
            try:
                result = constraint.check(system_state)
                evaluation['constraint_results'][constraint_name] = result
                
                if not result:
                    evaluation['constraints_satisfied'] = False
                    violation_details = constraint.get_violation_details(system_state)
                    if violation_details:
                        evaluation['violations'].append(violation_details)
                        
                    logger.warning(f"Constraint '{constraint_name}' violated")
                else:
                    logger.debug(f"Constraint '{constraint_name}' satisfied")
                    
            except Exception as e:
                logger.error(f"Error checking constraint '{constraint_name}': {str(e)}")
                evaluation['constraint_results'][constraint_name] = False
                evaluation['constraints_satisfied'] = False
                
        # Overall validity
        evaluation['valid'] = evaluation['constraints_satisfied']
        
        # Record in history
        self.progress_history.append({
            'timestamp': evaluation['timestamp'],
            'overall_fitness': evaluation['overall_fitness'],
            'fitness_scores': evaluation['fitness_scores'].copy(),
            'constraints_satisfied': evaluation['constraints_satisfied'],
            'violation_count': len(evaluation['violations'])
        })
        
        return evaluation
    
    def generate_adaptations(self, 
                            system_state: Dict[str, Any], 
                            environment: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Generate potential adaptations based on the current state and environment.
        
        Args:
            system_state: The current state of the system.
            environment: The current environment state.
            
        Returns:
            List[Dict[str, Any]]: List of potential adaptations.
        """
        adaptations = []
        
        # Apply each adaptation heuristic
        for heuristic in self.adaptation_heuristics:
            try:
                heuristic_adaptations = heuristic.generate_adaptations(system_state, environment)
                
                # Add heuristic name to adaptations
                for adaptation in heuristic_adaptations:
                    adaptation['heuristic'] = heuristic.__class__.__name__
                    
                adaptations.extend(heuristic_adaptations)
                
                logger.debug(f"Heuristic '{heuristic.__class__.__name__}' generated {len(heuristic_adaptations)} adaptations")
                
            except Exception as e:
                logger.error(f"Error in adaptation heuristic '{heuristic.__class__.__name__}': {str(e)}")
                
        # Add unique ID and timestamp to each adaptation
        for adaptation in adaptations:
            adaptation['id'] = str(uuid.uuid4())
            adaptation['timestamp'] = time.time()
            
        logger.info(f"Generated {len(adaptations)} potential adaptations")
            
        return adaptations
    
    def rank_adaptations(self, 
                        adaptations: List[Dict[str, Any]], 
                        system_state: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Rank potential adaptations based on their expected impact.
        
        Args:
            adaptations: List of potential adaptations.
            system_state: The current state of the system.
            
        Returns:
            List[Dict[str, Any]]: Ranked adaptations.
        """
        # Calculate potential fitness for each adaptation
        ranked_adaptations = []
        
        for adaptation in adaptations:
            try:
                # Simulate applying the adaptation
                expected_impact = adaptation.get('expected_impact', {})
                simulated_state = self._simulate_adaptation_impact(system_state, expected_impact)
                
                # Evaluate the simulated state
                evaluation = self.evaluate_system_state(simulated_state)
                
                # Store evaluation results with the adaptation
                ranked_adaptation = copy.deepcopy(adaptation)
                ranked_adaptation['expected_fitness'] = evaluation['overall_fitness']
                ranked_adaptation['constraint_satisfaction'] = evaluation['constraints_satisfied']
                ranked_adaptation['valid'] = evaluation['valid']
                ranked_adaptation['fitness_delta'] = evaluation['overall_fitness'] - self._get_current_fitness(system_state)
                
                ranked_adaptations.append(ranked_adaptation)
                
            except Exception as e:
                logger.error(f"Error ranking adaptation '{adaptation.get('id', 'unknown')}': {str(e)}")
                
        # Sort by expected fitness, only including valid adaptations
        valid_adaptations = [a for a in ranked_adaptations if a['valid']]
        valid_adaptations.sort(key=lambda x: x['expected_fitness'], reverse=True)
        
        # Log ranking results
        logger.info(f"Ranked {len(valid_adaptations)} valid adaptations out of {len(adaptations)} total")
        if valid_adaptations:
            logger.info(f"Top adaptation: {valid_adaptations[0]['description']} with expected fitness {valid_adaptations[0]['expected_fitness']:.4f}")
            
        return valid_adaptations
    
    def select_adaptations(self, 
                         ranked_adaptations: List[Dict[str, Any]], 
                         max_adaptations: int = 5,
                         min_improvement: float = 0.01) -> List[Dict[str, Any]]:
        """
        Select the best adaptations to apply.
        
        Args:
            ranked_adaptations: List of ranked adaptations.
            max_adaptations: Maximum number of adaptations to select.
            min_improvement: Minimum fitness improvement required.
            
        Returns:
            List[Dict[str, Any]]: Selected adaptations.
        """
        # Filter adaptations by minimum improvement
        improved_adaptations = [
            a for a in ranked_adaptations 
            if a.get('fitness_delta', 0.0) >= min_improvement
        ]
        
        # If we have more adaptations than the maximum allowed, select the best ones
        if len(improved_adaptations) > max_adaptations:
            selected_adaptations = improved_adaptations[:max_adaptations]
        else:
            selected_adaptations = improved_adaptations
            
        # Record in adaptation history
        self.adaptation_history.append({
            'timestamp': time.time(),
            'candidates': len(ranked_adaptations),
            'improved': len(improved_adaptations),
            'selected': len(selected_adaptations),
            'selected_ids': [a['id'] for a in selected_adaptations]
        })
        
        logger.info(f"Selected {len(selected_adaptations)} adaptations out of {len(improved_adaptations)} with improvement >= {min_improvement}")
        
        return selected_adaptations
    
    def apply_adaptations(self, 
                         adaptations: List[Dict[str, Any]], 
                         system_state: Dict[str, Any]) -> Dict[str, Any]:
        """
        Apply selected adaptations to the system state (simulation).
        
        Args:
            adaptations: List of adaptations to apply.
            system_state: The current system state.
            
        Returns:
            Dict[str, Any]: Updated system state after applying adaptations.
        """
        # Start with a copy of the current state
        updated_state = copy.deepcopy(system_state)
        
        # Apply each adaptation
        for adaptation in adaptations:
            expected_impact = adaptation.get('expected_impact', {})
            updated_state = self._apply_adaptation_impact(updated_state, expected_impact)
            
            logger.info(f"Applied adaptation: {adaptation['description']}")
            
        # Evaluate the new state
        evaluation = self.evaluate_system_state(updated_state)
        
        # Add evaluation to the updated state
        updated_state['evaluation'] = evaluation
        
        return updated_state
    
    def get_progress_history(self) -> List[Dict[str, Any]]:
        """Get the history of system progress."""
        return self.progress_history.copy()
    
    def get_adaptation_history(self) -> List[Dict[str, Any]]:
        """Get the history of adaptation selections."""
        return self.adaptation_history.copy()
    
    def get_fitness_trend(self, window: int = 10) -> Dict[str, Any]:
        """
        Get the trend of fitness over time.
        
        Args:
            window: Number of history entries to consider.
            
        Returns:
            Dict[str, Any]: Trend analysis.
        """
        if not self.progress_history:
            return {
                'trend': 'unknown',
                'slope': 0.0,
                'stability': 0.0
            }
            
        # Get recent history
        recent_history = self.progress_history[-min(window, len(self.progress_history)):]
        
        # Calculate trend
        fitness_values = [entry['overall_fitness'] for entry in recent_history]
        
        if len(fitness_values) < 2:
            return {
                'trend': 'unknown',
                'slope': 0.0,
                'stability': 0.0
            }
            
        # Calculate slope
        x = list(range(len(fitness_values)))
        mean_x = sum(x) / len(x)
        mean_y = sum(fitness_values) / len(fitness_values)
        
        numerator = sum((x[i] - mean_x) * (fitness_values[i] - mean_y) for i in range(len(x)))
        denominator = sum((x[i] - mean_x) ** 2 for i in range(len(x)))
        
        slope = numerator / max(1e-10, denominator)
        
        # Determine trend
        if slope > 0.01:
            trend = 'improving'
        elif slope < -0.01:
            trend = 'declining'
        else:
            trend = 'stable'
            
        # Calculate stability (inverse of variance)
        variance = sum((v - mean_y) ** 2 for v in fitness_values) / len(fitness_values)
        stability = 1.0 / max(1e-10, variance)
        
        # Calculate fitness range
        fitness_range = max(fitness_values) - min(fitness_values) if fitness_values else 0.0
        
        # Get individual fitness function trends
        function_trends = {}
        fitness_functions = {f.__class__.__name__: f for f in self.fitness_functions}
        
        for function_name in fitness_functions:
            function_values = [entry.get('fitness_scores', {}).get(function_name, 0.0) 
                             for entry in recent_history]
            
            if len(function_values) >= 2:
                function_slope = self._calculate_slope(range(len(function_values)), function_values)
                
                if function_slope > 0.01:
                    function_trend = 'improving'
                elif function_slope < -0.01:
                    function_trend = 'declining'
                else:
                    function_trend = 'stable'
                    
                function_trends[function_name] = {
                    'trend': function_trend,
                    'slope': function_slope
                }
            
        return {
            'trend': trend,
            'slope': slope,
            'stability': stability,
            'range': fitness_range,
            'latest': fitness_values[-1] if fitness_values else 0.0,
            'function_trends': function_trends
        }
    
    def get_constraint_status(self, system_state: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get detailed status of all constraints.
        
        Args:
            system_state: The current system state.
            
        Returns:
            Dict[str, Any]: Constraint status.
        """
        constraint_status = {
            'overall_satisfied': True,
            'constraints': {}
        }
        
        for constraint in self.constraints:
            constraint_name = constraint.__class__.__name__
            
            try:
                satisfied = constraint.check(system_state)
                details = {}
                
                if not satisfied:
                    details = constraint.get_violation_details(system_state)
                    constraint_status['overall_satisfied'] = False
                    
                constraint_status['constraints'][constraint_name] = {
                    'satisfied': satisfied,
                    'description': constraint.get_description(),
                    'details': details
                }
                
            except Exception as e:
                logger.error(f"Error checking constraint '{constraint_name}': {str(e)}")
                constraint_status['constraints'][constraint_name] = {
                    'satisfied': False,
                    'description': constraint.get_description(),
                    'details': {'error': str(e)}
                }
                constraint_status['overall_satisfied'] = False
                
        return constraint_status
    
    def get_adaptation_statistics(self) -> Dict[str, Any]:
        """
        Get statistics about adaptation generation and selection.
        
        Returns:
            Dict[str, Any]: Adaptation statistics.
        """
        if not self.adaptation_history:
            return {
                'total_selections': 0,
                'success_rate': 0.0,
                'heuristic_performance': {}
            }
            
        # Calculate statistics
        total_candidates = sum(entry['candidates'] for entry in self.adaptation_history)
        total_improved = sum(entry['improved'] for entry in self.adaptation_history)
        total_selected = sum(entry['selected'] for entry in self.adaptation_history)
        
        # Calculate improvement rate and selection rate
        improvement_rate = total_improved / max(1, total_candidates)
        selection_rate = total_selected / max(1, total_improved)
        
        # Collect per-heuristic statistics (requires additional tracking)
        heuristic_stats = {}
        
        return {
            'total_candidates': total_candidates,
            'total_improved': total_improved,
            'total_selected': total_selected,
            'improvement_rate': improvement_rate,
            'selection_rate': selection_rate,
            'heuristic_performance': heuristic_stats
        }
    
    def adjust_fitness_weights(self, task_type: str = '', auto_adjust: bool = True) -> Dict[str, float]:
        """
        Adjust fitness weights based on task type or auto-adjust based on history.
        
        Args:
            task_type: Type of task to adjust weights for.
            auto_adjust: Whether to automatically adjust weights based on history.
            
        Returns:
            Dict[str, float]: Updated weights by fitness function.
        """
        if not self.fitness_functions:
            return {}
            
        # Case 1: Adjust weights based on task type
        if task_type:
            # Get importance of each fitness function for this task
            importances = []
            for fitness_func in self.fitness_functions:
                importance = fitness_func.get_importance_for_task(task_type)
                importances.append(importance)
                
            # Normalize importances
            total_importance = sum(importances)
            if total_importance > 0:
                self.fitness_weights = [imp / total_importance for imp in importances]
                
        # Case 2: Auto-adjust based on history
        elif auto_adjust and len(self.progress_history) >= 10:
            # Calculate correlation between each fitness function and overall fitness
            correlations = self._calculate_fitness_correlations()
            
            # Adjust weights to focus more on weakest areas
            function_means = {}
            for func_name, func in zip([f.__class__.__name__ for f in self.fitness_functions], self.fitness_functions):
                values = [entry.get('fitness_scores', {}).get(func_name, 0.0) 
                        for entry in self.progress_history[-10:]]
                function_means[func_name] = sum(values) / max(1, len(values))
                
            # Invert means to give more weight to functions with lower scores
            inverse_means = {name: 1.0 - score for name, score in function_means.items()}
            total_inverse = sum(inverse_means.values())
            
            # Combine inverse means and correlations (50/50 weight)
            new_weights = []
            for i, func_name in enumerate([f.__class__.__name__ for f in self.fitness_functions]):
                inverse_weight = inverse_means.get(func_name, 0.5) / max(1e-10, total_inverse)
                correlation_weight = abs(correlations.get(func_name, 0.0))
                
                # Combined weight (50% inverse mean, 50% correlation)
                combined_weight = 0.5 * inverse_weight + 0.5 * correlation_weight
                new_weights.append(combined_weight)
                
            # Normalize weights
            total_weight = sum(new_weights)
            if total_weight > 0:
                self.fitness_weights = [w / total_weight for w in new_weights]
                
        # Return current weights
        return {
            f.__class__.__name__: w 
            for f, w in zip(self.fitness_functions, self.fitness_weights)
        }
    
    def _calculate_fitness_correlations(self) -> Dict[str, float]:
        """
        Calculate correlations between individual fitness functions and overall fitness.
        
        Returns:
            Dict[str, float]: Correlation coefficients by fitness function.
        """
        correlations = {}
        
        if len(self.progress_history) < 5:
            return correlations
            
        # Extract overall fitness
        overall_fitness = [entry['overall_fitness'] for entry in self.progress_history]
        
        # Calculate correlation for each fitness function
        for function_name in set().union(*(entry.get('fitness_scores', {}) for entry in self.progress_history)):
            function_values = [entry.get('fitness_scores', {}).get(function_name, None) 
                             for entry in self.progress_history]
            
            # Filter out None values
            paired_values = [
                (f, o) for f, o in zip(function_values, overall_fitness)
                if f is not None
            ]
            
            if len(paired_values) >= 5:
                function_values = [p[0] for p in paired_values]
                paired_overall = [p[1] for p in paired_values]
                
                # Calculate correlation coefficient
                correlation = self._calculate_correlation(function_values, paired_overall)
                correlations[function_name] = correlation
                
        return correlations
    
    def _calculate_correlation(self, x: List[float], y: List[float]) -> float:
        """
        Calculate Pearson correlation coefficient between two series.
        
        Args:
            x: First series.
            y: Second series.
            
        Returns:
            float: Correlation coefficient.
        """
        if len(x) != len(y) or len(x) < 2:
            return 0.0
            
        n = len(x)
        mean_x = sum(x) / n
        mean_y = sum(y) / n
        
        var_x = sum((xi - mean_x) ** 2 for xi in x) / n
        var_y = sum((yi - mean_y) ** 2 for yi in y) / n
        
        if var_x == 0 or var_y == 0:
            return 0.0
            
        cov_xy = sum((xi - mean_x) * (yi - mean_y) for xi, yi in zip(x, y)) / n
        
        return cov_xy / (math.sqrt(var_x) * math.sqrt(var_y))
    
    def _calculate_slope(self, x: List[float], y: List[float]) -> float:
        """
        Calculate the slope of a linear regression.
        
        Args:
            x: X values.
            y: Y values.
            
        Returns:
            float: Slope.
        """
        if len(x) != len(y) or len(x) < 2:
            return 0.0
            
        mean_x = sum(x) / len(x)
        mean_y = sum(y) / len(y)
        
        numerator = sum((x[i] - mean_x) * (y[i] - mean_y) for i in range(len(x)))
        denominator = sum((x[i] - mean_x) ** 2 for i in range(len(x)))
        
        return numerator / max(1e-10, denominator)
    
    def _get_current_fitness(self, system_state: Dict[str, Any]) -> float:
        """
        Get the current fitness of the system state.
        
        Args:
            system_state: The current system state.
            
        Returns:
            float: Current fitness.
        """
        # Check if system state already has an evaluation
        if 'evaluation' in system_state and 'overall_fitness' in system_state['evaluation']:
            return system_state['evaluation']['overall_fitness']
            
        # If we have recent history, use the latest fitness
        if self.progress_history:
            return self.progress_history[-1]['overall_fitness']
            
        # Otherwise, evaluate the system state
        try:
            evaluation = self.evaluate_system_state(system_state)
            return evaluation['overall_fitness']
        except Exception as e:
            logger.error(f"Error evaluating current fitness: {str(e)}")
            return 0.5  # Default to neutral fitness
    
    def _simulate_adaptation_impact(self, 
                                  system_state: Dict[str, Any], 
                                  expected_impact: Dict[str, float]) -> Dict[str, Any]:
        """
        Simulate the impact of an adaptation on the system state.
        
        Args:
            system_state: The current system state.
            expected_impact: Expected impact of the adaptation.
            
        Returns:
            Dict[str, Any]: Simulated system state after adaptation.
        """
        # Create a copy of the system state
        simulated_state = copy.deepcopy(system_state)
        
        # Apply the expected impact
        simulated_state = self._apply_adaptation_impact(simulated_state, expected_impact)
            
        return simulated_state
    
    def _apply_adaptation_impact(self, 
                                system_state: Dict[str, Any], 
                                impact: Dict[str, float]) -> Dict[str, Any]:
        """
        Apply adaptation impact to system state.
        
        Args:
            system_state: The system state to modify.
            impact: Impact values to apply.
            
        Returns:
            Dict[str, Any]: Modified system state.
        """
        for key, value in impact.items():
            if key in system_state and isinstance(system_state[key], (int, float)):
                # Direct impact on a simple value
                if value >= 0:
                    # Positive impact: increase by percentage
                    system_state[key] *= (1.0 + value)
                else:
                    # Negative impact: decrease by percentage
                    system_state[key] *= (1.0 + value)  # value is negative
                    
            elif key in system_state and isinstance(system_state[key], dict):
                # Impact on a dictionary value - apply to all numeric values
                for subkey, subvalue in system_state[key].items():
                    if isinstance(subvalue, (int, float)):
                        if value >= 0:
                            system_state[key][subkey] *= (1.0 + 0.1 * value)  # Scale down impact
                        else:
                            system_state[key][subkey] *= (1.0 + 0.1 * value)  # value is negative
                            
            elif key == 'performance' and 'performance' in system_state:
                # Special handling for performance metrics
                performance = system_state['performance']
                
                if 'throughput' in performance:
                    if value >= 0:
                        # For throughput, higher is better
                        performance['throughput'] *= (1.0 + value)
                    else:
                        # Decrease throughput
                        performance['throughput'] *= (1.0 + value)  # value is negative
                        
                if 'latency' in performance:
                    if value >= 0:
                        # For latency, lower is better, so positive impact means decrease
                        performance['latency'] *= (1.0 - value)
                    else:
                        # Increase latency
                        performance['latency'] *= (1.0 - value)  # value is negative
                        
                if 'error_rate' in performance:
                    if value >= 0:
                        # For error rate, lower is better, so positive impact means decrease
                        performance['error_rate'] *= (1.0 - value)
                    else:
                        # Increase error rate
                        performance['error_rate'] *= (1.0 - value)  # value is negative
                        
            elif key == 'resource_usage' and 'resources' in system_state:
                # Special handling for resource usage
                resources = system_state['resources']
                
                for resource in ['memory_usage', 'cpu_usage', 'storage_usage']:
                    if resource in resources:
                        if value >= 0:
                            # Increase resource usage
                            resources[resource] *= (1.0 + value)
                        else:
                            # Decrease resource usage
                            resources[resource] *= (1.0 + value)  # value is negative
                            
            elif key == 'reliability' and 'reliability' in system_state:
                # Special handling for reliability metrics
                reliability = system_state['reliability']
                
                if 'uptime' in reliability:
                    if value >= 0:
                        # For uptime, higher is better
                        reliability['uptime'] = min(100.0, reliability['uptime'] * (1.0 + 0.5 * value))
                    else:
                        # Decrease uptime
                        reliability['uptime'] *= (1.0 + 0.5 * value)  # value is negative
                        
                if 'error_rate' in reliability:
                    if value >= 0:
                        # For error rate, lower is better
                        reliability['error_rate'] *= (1.0 - value)
                    else:
                        # Increase error rate
                        reliability['error_rate'] *= (1.0 - value)  # value is negative
                        
            elif key == 'security' and 'security' in system_state:
                # Special handling for security metrics
                security = system_state['security']
                
                if 'overall_score' in security:
                    if value >= 0:
                        # Increase security score (bounded by 1.0)
                        security['overall_score'] = min(1.0, security['overall_score'] * (1.0 + value))
                    else:
                        # Decrease security score
                        security['overall_score'] *= (1.0 + value)  # value is negative
                        
                if 'component_scores' in security:
                    for component, score in security['component_scores'].items():
                        if value >= 0:
                            # Increase component scores (bounded by 1.0)
                            security['component_scores'][component] = min(1.0, score * (1.0 + 0.8 * value))
                        else:
                            # Decrease component scores
                            security['component_scores'][component] *= (1.0 + 0.8 * value)  # value is negative
                            
            elif key == 'adaptability' and 'adaptability' in system_state:
                # Special handling for adaptability metrics
                adaptability = system_state['adaptability']
                
                for metric in ['learning_rate', 'generalization', 'transfer_learning']:
                    if metric in adaptability:
                        if value >= 0:
                            # Increase adaptability metrics (bounded by 1.0)
                            adaptability[metric] = min(1.0, adaptability[metric] * (1.0 + value))
                        else:
                            # Decrease adaptability metrics
                            adaptability[metric] *= (1.0 + value)  # value is negative
            
        return system_state


# Export the main components
__all__ = [
    'FitnessFunction',
    'PerformanceFitness',
    'ResourceEfficiencyFitness',
    'ReliabilityFitness',
    'AdaptabilityFitness',
    'SecurityFitness',
    'EvolutionaryConstraint',
    'ResourceLimitConstraint',
    'SafetyConstraint',
    'PerformanceConstraint',
    'SecurityConstraint',
    'AdaptationHeuristic',
    'PerformanceOptimizationHeuristic',
    'EnvironmentalAdaptationHeuristic',
    'SecurityAdaptationHeuristic',
    'ArchitecturalAdaptationHeuristic',
    'EvolutionarySteering'
]