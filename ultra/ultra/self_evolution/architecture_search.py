#!/usr/bin/env python3
"""
ULTRA: Ultimate Learning & Thought Reasoning Architecture
Neural Architecture Search Implementation

This module implements the Neural Architecture Search (NAS) component of the
Self-Evolution System, enabling ULTRA to automatically discover optimal neural architectures
for specific tasks or domains. It provides:

1. Comprehensive architecture representation capabilities
2. Customizable search spaces
3. Multiple search strategies (evolutionary search, Bayesian optimization, etc.)
4. Efficient performance evaluation
5. Integration with other ULTRA components

The NAS system follows a cyclical process:
1. Generate candidate architectures
2. Evaluate their performance on target tasks
3. Use evaluation results to guide further search
4. Return the best architecture found

This implementation incorporates state-of-the-art techniques from the NAS literature
while adding novel algorithms for efficiency and effectiveness.
"""

import os
import sys
import time
import logging
import json
import copy
import uuid
import math
import random
import numpy as np
import torch
import concurrent.futures
import multiprocessing
import itertools
from pathlib import Path
from typing import Dict, List, Tuple, Any, Callable, Optional, Union, Set, TypeVar, Generic
from dataclasses import dataclass, field, asdict
from enum import Enum, auto
from abc import ABC, abstractmethod
from collections import defaultdict, deque, Counter, OrderedDict
from datetime import datetime
import networkx as nx

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Add parent directory to sys.path to allow for relative imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import ULTRA components (with error handling for optional dependencies)
try:
    from ultra.utils.config import Configuration
    from ultra.utils.monitoring import PerformanceMonitor
    from ultra.utils.visualization import ArchitectureVisualizer
    from ultra.core_neural.neuromorphic_core import NeuromorphicCore
    from ultra.knowledge_management.episodic_knowledge import EpisodicKnowledgeBase
    from ultra.meta_cognitive.meta_learning import MetaLearningController
except ImportError as e:
    logger.warning(f"Some ULTRA components could not be imported: {e}. Neural Architecture Search may have limited functionality.")


# ------------------------------------------------------------------------------
# Architecture Representation
# ------------------------------------------------------------------------------

class OperationType(Enum):
    """Enumeration of different neural operation types."""
    LINEAR = auto()
    CONV1D = auto()
    CONV2D = auto()
    CONV3D = auto()
    DEPTHWISE_CONV = auto()
    SEPARABLE_CONV = auto()
    DILATED_CONV = auto()
    TRANSPOSED_CONV = auto()
    LSTM = auto()
    GRU = auto()
    ATTENTION = auto()
    MULTI_HEAD_ATTENTION = auto()
    SELF_ATTENTION = auto()
    TRANSFORMER = auto()
    POOLING_MAX = auto()
    POOLING_AVG = auto()
    POOLING_ADAPTIVE = auto()
    BATCH_NORM = auto()
    LAYER_NORM = auto()
    DROPOUT = auto()
    ACTIVATION_RELU = auto()
    ACTIVATION_SIGMOID = auto()
    ACTIVATION_TANH = auto()
    ACTIVATION_GELU = auto()
    ACTIVATION_SWISH = auto()
    ACTIVATION_MISH = auto()
    ACTIVATION_SOFTMAX = auto()
    SKIP_CONNECTION = auto()
    RESIDUAL = auto()
    IDENTITY = auto()
    EMBEDDING = auto()
    CONCATENATE = auto()
    ADD = auto()
    MULTIPLY = auto()
    GLOBAL_POOLING = auto()
    FLATTEN = auto()


class ConnectionType(Enum):
    """Enumeration of different connection types between nodes."""
    FORWARD = auto()
    SKIP = auto()
    RESIDUAL = auto()
    DENSE = auto()
    RECURRENT = auto()
    ATTENTION = auto()
    WEIGHTED = auto()


@dataclass
class OperationConfig:
    """Configuration for a neural operation."""
    op_type: OperationType
    params: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            'op_type': self.op_type.name,
            'params': self.params
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'OperationConfig':
        """Create from dictionary."""
        return cls(
            op_type=OperationType[data['op_type']],
            params=data.get('params', {})
        )


@dataclass
class NodeConfig:
    """Configuration for a node in a neural architecture."""
    node_id: str
    operation: OperationConfig
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            'node_id': self.node_id,
            'operation': self.operation.to_dict(),
            'metadata': self.metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'NodeConfig':
        """Create from dictionary."""
        return cls(
            node_id=data['node_id'],
            operation=OperationConfig.from_dict(data['operation']),
            metadata=data.get('metadata', {})
        )


@dataclass
class EdgeConfig:
    """Configuration for an edge between nodes in a neural architecture."""
    source_id: str
    target_id: str
    connection_type: ConnectionType = ConnectionType.FORWARD
    weight: float = 1.0
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            'source_id': self.source_id,
            'target_id': self.target_id,
            'connection_type': self.connection_type.name,
            'weight': self.weight,
            'metadata': self.metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'EdgeConfig':
        """Create from dictionary."""
        return cls(
            source_id=data['source_id'],
            target_id=data['target_id'],
            connection_type=ConnectionType[data['connection_type']],
            weight=data.get('weight', 1.0),
            metadata=data.get('metadata', {})
        )


@dataclass
class NeuralArchitecture:
    """Comprehensive representation of a neural architecture."""
    nodes: Dict[str, NodeConfig] = field(default_factory=dict)
    edges: List[EdgeConfig] = field(default_factory=list)
    input_nodes: List[str] = field(default_factory=list)
    output_nodes: List[str] = field(default_factory=list)
    global_params: Dict[str, Any] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            'nodes': {node_id: node.to_dict() for node_id, node in self.nodes.items()},
            'edges': [edge.to_dict() for edge in self.edges],
            'input_nodes': self.input_nodes,
            'output_nodes': self.output_nodes,
            'global_params': self.global_params,
            'metadata': self.metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'NeuralArchitecture':
        """Create from dictionary."""
        return cls(
            nodes={node_id: NodeConfig.from_dict(node_data) 
                  for node_id, node_data in data.get('nodes', {}).items()},
            edges=[EdgeConfig.from_dict(edge_data) 
                  for edge_data in data.get('edges', [])],
            input_nodes=data.get('input_nodes', []),
            output_nodes=data.get('output_nodes', []),
            global_params=data.get('global_params', {}),
            metadata=data.get('metadata', {})
        )
    
    def get_hash(self) -> str:
        """Compute a hash for this architecture."""
        arch_str = json.dumps(self.to_dict(), sort_keys=True)
        return str(hash(arch_str))
    
    def to_adjacency_matrix(self) -> Tuple[np.ndarray, List[str]]:
        """
        Convert the architecture to an adjacency matrix representation.
        
        Returns:
            Tuple[np.ndarray, List[str]]: The adjacency matrix and the list of node IDs.
        """
        node_ids = list(self.nodes.keys())
        node_id_to_idx = {node_id: i for i, node_id in enumerate(node_ids)}
        
        n = len(node_ids)
        adj_matrix = np.zeros((n, n), dtype=float)
        
        for edge in self.edges:
            if edge.source_id in node_id_to_idx and edge.target_id in node_id_to_idx:
                source_idx = node_id_to_idx[edge.source_id]
                target_idx = node_id_to_idx[edge.target_id]
                adj_matrix[source_idx, target_idx] = edge.weight
        
        return adj_matrix, node_ids
    
    def to_networkx(self) -> nx.DiGraph:
        """
        Convert the architecture to a NetworkX directed graph.
        
        Returns:
            nx.DiGraph: A directed graph representation of the architecture.
        """
        G = nx.DiGraph()
        
        # Add nodes with attributes
        for node_id, node_config in self.nodes.items():
            G.add_node(
                node_id, 
                operation=node_config.operation.op_type.name,
                params=node_config.operation.params,
                metadata=node_config.metadata
            )
        
        # Add edges with attributes
        for edge in self.edges:
            G.add_edge(
                edge.source_id,
                edge.target_id,
                connection_type=edge.connection_type.name,
                weight=edge.weight,
                metadata=edge.metadata
            )
        
        return G
    
    def validate(self) -> Tuple[bool, List[str]]:
        """
        Validate the architecture for consistency and correctness.
        
        Returns:
            Tuple[bool, List[str]]: Whether the architecture is valid and a list of validation messages.
        """
        messages = []
        valid = True
        
        # Check that all input nodes exist
        for node_id in self.input_nodes:
            if node_id not in self.nodes:
                valid = False
                messages.append(f"Input node {node_id} does not exist")
        
        # Check that all output nodes exist
        for node_id in self.output_nodes:
            if node_id not in self.nodes:
                valid = False
                messages.append(f"Output node {node_id} does not exist")
        
        # Check that all edge endpoints exist
        for edge in self.edges:
            if edge.source_id not in self.nodes:
                valid = False
                messages.append(f"Edge source node {edge.source_id} does not exist")
            if edge.target_id not in self.nodes:
                valid = False
                messages.append(f"Edge target node {edge.target_id} does not exist")
        
        # Check for cycles
        try:
            G = self.to_networkx()
            cycles = list(nx.simple_cycles(G))
            if cycles:
                valid = False
                messages.append(f"Architecture contains cycles: {cycles}")
        except Exception as e:
            valid = False
            messages.append(f"Error checking for cycles: {str(e)}")
        
        # Check that all nodes (except input nodes) have at least one incoming edge
        node_has_incoming = {node_id: False for node_id in self.nodes.keys()}
        for edge in self.edges:
            node_has_incoming[edge.target_id] = True
        
        for node_id, has_incoming in node_has_incoming.items():
            if not has_incoming and node_id not in self.input_nodes:
                valid = False
                messages.append(f"Node {node_id} has no incoming edges and is not an input node")
        
        # Check that all nodes (except output nodes) have at least one outgoing edge
        node_has_outgoing = {node_id: False for node_id in self.nodes.keys()}
        for edge in self.edges:
            node_has_outgoing[edge.source_id] = True
        
        for node_id, has_outgoing in node_has_outgoing.items():
            if not has_outgoing and node_id not in self.output_nodes:
                valid = False
                messages.append(f"Node {node_id} has no outgoing edges and is not an output node")
        
        return valid, messages
    
    def optimize(self) -> 'NeuralArchitecture':
        """
        Optimize the architecture by removing redundancy and improving efficiency.
        
        Returns:
            NeuralArchitecture: The optimized architecture.
        """
        # Create a copy of the architecture
        optimized = copy.deepcopy(self)
        
        # Remove redundant skip connections
        redundant_edges = []
        for i, edge1 in enumerate(optimized.edges):
            if edge1.connection_type == ConnectionType.SKIP:
                for j, edge2 in enumerate(optimized.edges):
                    if i != j and edge2.source_id == edge1.source_id and edge2.target_id == edge1.target_id:
                        # Found a redundant skip connection
                        redundant_edges.append(i)
                        break
        
        # Remove redundant edges in reverse order to preserve indices
        for i in sorted(redundant_edges, reverse=True):
            del optimized.edges[i]
        
        # Merge sequential linear operations
        mergeable_ops = {
            (OperationType.LINEAR, OperationType.LINEAR): OperationType.LINEAR,
            (OperationType.CONV1D, OperationType.CONV1D): OperationType.CONV1D,
            (OperationType.CONV2D, OperationType.CONV2D): OperationType.CONV2D,
            (OperationType.CONV3D, OperationType.CONV3D): OperationType.CONV3D
        }
        
        nodes_to_merge = []
        for edge in optimized.edges:
            source_node = optimized.nodes.get(edge.source_id)
            target_node = optimized.nodes.get(edge.target_id)
            
            if source_node and target_node:
                source_op = source_node.operation.op_type
                target_op = target_node.operation.op_type
                
                op_pair = (source_op, target_op)
                if op_pair in mergeable_ops:
                    # Check if source node has exactly one outgoing edge to target
                    source_out_count = sum(1 for e in optimized.edges if e.source_id == edge.source_id)
                    # Check if target node has exactly one incoming edge from source
                    target_in_count = sum(1 for e in optimized.edges if e.target_id == edge.target_id)
                    
                    if source_out_count == 1 and target_in_count == 1:
                        # Candidate for merging
                        nodes_to_merge.append((edge.source_id, edge.target_id, mergeable_ops[op_pair]))
        
        # Perform the merging
        for source_id, target_id, merged_op_type in nodes_to_merge:
            source_node = optimized.nodes[source_id]
            target_node = optimized.nodes[target_id]
            
            # Create merged node
            merged_node = NodeConfig(
                node_id=source_id,
                operation=OperationConfig(
                    op_type=merged_op_type,
                    params={**source_node.operation.params, **target_node.operation.params}
                ),
                metadata={**source_node.metadata, **target_node.metadata}
            )
            
            # Update node
            optimized.nodes[source_id] = merged_node
            
            # Remove target node
            del optimized.nodes[target_id]
            
            # Update edges
            edges_to_remove = []
            edges_to_add = []
            
            for i, e in enumerate(optimized.edges):
                if e.source_id == source_id and e.target_id == target_id:
                    # Edge between merged nodes - remove
                    edges_to_remove.append(i)
                elif e.source_id == target_id:
                    # Edge from target node - redirect to source
                    edges_to_add.append(EdgeConfig(
                        source_id=source_id,
                        target_id=e.target_id,
                        connection_type=e.connection_type,
                        weight=e.weight,
                        metadata=e.metadata
                    ))
                    edges_to_remove.append(i)
            
            # Remove edges in reverse order
            for i in sorted(edges_to_remove, reverse=True):
                del optimized.edges[i]
            
            # Add new edges
            optimized.edges.extend(edges_to_add)
            
            # Update output nodes if needed
            if target_id in optimized.output_nodes:
                optimized.output_nodes.remove(target_id)
                if source_id not in optimized.output_nodes:
                    optimized.output_nodes.append(source_id)
        
        return optimized

    def get_canonical_ordering(self) -> List[str]:
        """
        Get a canonical ordering of nodes for deterministic processing.
        
        Returns:
            List[str]: An ordered list of node IDs.
        """
        # Create a graph representation
        G = nx.DiGraph()
        
        # Add nodes
        for node_id in self.nodes:
            G.add_node(node_id)
        
        # Add edges
        for edge in self.edges:
            G.add_edge(edge.source_id, edge.target_id)
        
        # Use topological sort to get an ordering
        try:
            return list(nx.topological_sort(G))
        except nx.NetworkXUnfeasible:
            # If there's a cycle, use a different method
            return sorted(self.nodes.keys())

    def get_layer_ordering(self) -> List[List[str]]:
        """
        Group nodes into layers for parallel processing.
        
        Returns:
            List[List[str]]: A list of layers, where each layer is a list of node IDs.
        """
        # Create a graph representation
        G = nx.DiGraph()
        
        # Add nodes
        for node_id in self.nodes:
            G.add_node(node_id)
        
        # Add edges
        for edge in self.edges:
            G.add_edge(edge.source_id, edge.target_id)
        
        # Initialize with input nodes as layer 0
        layers = [self.input_nodes]
        processed_nodes = set(self.input_nodes)
        
        # Continue until all nodes are processed
        while processed_nodes != set(self.nodes.keys()):
            current_layer = []
            
            for node_id in self.nodes:
                if node_id in processed_nodes:
                    continue
                
                # Check if all predecessors have been processed
                predecessors = set(G.predecessors(node_id))
                if predecessors and predecessors.issubset(processed_nodes):
                    current_layer.append(node_id)
            
            if not current_layer:
                # If there are no nodes ready for this layer, we might have a cycle
                # Add the remaining nodes that have the most predecessors already processed
                node_scores = {}
                for node_id in self.nodes:
                    if node_id not in processed_nodes:
                        predecessors = set(G.predecessors(node_id))
                        score = len(predecessors & processed_nodes) / max(1, len(predecessors))
                        node_scores[node_id] = score
                
                if node_scores:
                    next_node = max(node_scores.items(), key=lambda x: x[1])[0]
                    current_layer.append(next_node)
            
            # Add the current layer and update processed nodes
            if current_layer:
                layers.append(current_layer)
                processed_nodes.update(current_layer)
            else:
                # If no nodes were added, but we haven't processed all nodes,
                # we might be stuck due to cycles. Add remaining nodes.
                remaining = [n for n in self.nodes if n not in processed_nodes]
                layers.append(remaining)
                processed_nodes.update(remaining)
        
        return layers

    def to_pytorch_modules(self) -> Dict[str, torch.nn.Module]:
        """
        Convert the architecture to PyTorch modules.
        
        Returns:
            Dict[str, torch.nn.Module]: A dictionary mapping node IDs to PyTorch modules.
        """
        import torch.nn as nn
        
        modules = {}
        
        for node_id, node_config in self.nodes.items():
            op_type = node_config.operation.op_type
            params = node_config.operation.params
            
            if op_type == OperationType.LINEAR:
                in_features = params.get('in_features', 64)
                out_features = params.get('out_features', 64)
                bias = params.get('bias', True)
                modules[node_id] = nn.Linear(in_features, out_features, bias=bias)
                
            elif op_type == OperationType.CONV1D:
                in_channels = params.get('in_channels', 1)
                out_channels = params.get('out_channels', 16)
                kernel_size = params.get('kernel_size', 3)
                stride = params.get('stride', 1)
                padding = params.get('padding', 0)
                modules[node_id] = nn.Conv1d(in_channels, out_channels, kernel_size, stride, padding)
                
            elif op_type == OperationType.CONV2D:
                in_channels = params.get('in_channels', 1)
                out_channels = params.get('out_channels', 16)
                kernel_size = params.get('kernel_size', 3)
                stride = params.get('stride', 1)
                padding = params.get('padding', 0)
                modules[node_id] = nn.Conv2d(in_channels, out_channels, kernel_size, stride, padding)
                
            elif op_type == OperationType.LSTM:
                input_size = params.get('input_size', 64)
                hidden_size = params.get('hidden_size', 64)
                num_layers = params.get('num_layers', 1)
                bidirectional = params.get('bidirectional', False)
                modules[node_id] = nn.LSTM(input_size, hidden_size, num_layers, bidirectional=bidirectional)
                
            elif op_type == OperationType.GRU:
                input_size = params.get('input_size', 64)
                hidden_size = params.get('hidden_size', 64)
                num_layers = params.get('num_layers', 1)
                bidirectional = params.get('bidirectional', False)
                modules[node_id] = nn.GRU(input_size, hidden_size, num_layers, bidirectional=bidirectional)
                
            elif op_type == OperationType.BATCH_NORM:
                num_features = params.get('num_features', 64)
                modules[node_id] = nn.BatchNorm1d(num_features)
                
            elif op_type == OperationType.LAYER_NORM:
                normalized_shape = params.get('normalized_shape', 64)
                modules[node_id] = nn.LayerNorm(normalized_shape)
                
            elif op_type == OperationType.DROPOUT:
                p = params.get('p', 0.5)
                modules[node_id] = nn.Dropout(p)
                
            elif op_type == OperationType.ACTIVATION_RELU:
                modules[node_id] = nn.ReLU()
                
            elif op_type == OperationType.ACTIVATION_SIGMOID:
                modules[node_id] = nn.Sigmoid()
                
            elif op_type == OperationType.ACTIVATION_TANH:
                modules[node_id] = nn.Tanh()
                
            elif op_type == OperationType.ACTIVATION_GELU:
                modules[node_id] = nn.GELU()
                
            elif op_type == OperationType.ACTIVATION_SOFTMAX:
                dim = params.get('dim', -1)
                modules[node_id] = nn.Softmax(dim=dim)
                
            elif op_type == OperationType.POOLING_MAX:
                kernel_size = params.get('kernel_size', 2)
                stride = params.get('stride', None)
                modules[node_id] = nn.MaxPool1d(kernel_size, stride=stride)
                
            elif op_type == OperationType.POOLING_AVG:
                kernel_size = params.get('kernel_size', 2)
                stride = params.get('stride', None)
                modules[node_id] = nn.AvgPool1d(kernel_size, stride=stride)
                
            elif op_type == OperationType.FLATTEN:
                modules[node_id] = nn.Flatten()
                
            elif op_type == OperationType.IDENTITY:
                modules[node_id] = nn.Identity()
                
            else:
                modules[node_id] = nn.Identity()
                logger.warning(f"Unsupported operation type {op_type} for node {node_id}. Using Identity instead.")
        
        return modules


class PyTorchModel(torch.nn.Module):
    """PyTorch model implementation of a neural architecture."""
    
    def __init__(self, architecture: NeuralArchitecture):
        super().__init__()
        
        self.architecture = architecture
        self.modules_dict = nn.ModuleDict()
        pytorch_modules = architecture.to_pytorch_modules()
        
        for node_id, module in pytorch_modules.items():
            self.modules_dict[node_id] = module
        
        # Get node ordering for forward pass
        self.node_ordering = architecture.get_canonical_ordering()
        self.layer_ordering = architecture.get_layer_ordering()
        
        # Create lookup for edge connections
        self.connections = defaultdict(list)
        for edge in architecture.edges:
            self.connections[edge.target_id].append((edge.source_id, edge.connection_type, edge.weight))
    
    def forward(self, x):
        """Forward pass through the model."""
        # Initialize activations for input nodes
        activations = {}
        
        if isinstance(x, dict):
            # Input is already a dict mapping input nodes to tensors
            activations = x
        elif isinstance(x, torch.Tensor):
            # Single tensor input, distribute to all input nodes
            if len(self.architecture.input_nodes) == 1:
                activations[self.architecture.input_nodes[0]] = x
            else:
                # If multiple input nodes but single tensor, split it
                # This is just a heuristic and may need adjustment for specific architectures
                split_size = x.size(1) // len(self.architecture.input_nodes)
                for i, node_id in enumerate(self.architecture.input_nodes):
                    start_idx = i * split_size
                    end_idx = start_idx + split_size if i < len(self.architecture.input_nodes) - 1 else x.size(1)
                    activations[node_id] = x[:, start_idx:end_idx]
        else:
            raise ValueError(f"Unsupported input type: {type(x)}")
        
        # Process all nodes in topological order
        for node_id in self.node_ordering:
            if node_id in self.architecture.input_nodes and node_id in activations:
                # Input node already has activation
                continue
            
            # Get incoming connections
            incoming_connections = self.connections[node_id]
            
            if not incoming_connections:
                # No incoming connections, skip this node
                continue
            
            # Aggregate inputs from incoming connections
            inputs = []
            
            for source_id, conn_type, weight in incoming_connections:
                if source_id not in activations:
                    # Source node doesn't have activation yet, which shouldn't happen with proper ordering
                    raise ValueError(f"Source node {source_id} for {node_id} doesn't have activation")
                
                source_activation = activations[source_id]
                
                if conn_type == ConnectionType.FORWARD:
                    inputs.append(source_activation * weight)
                elif conn_type == ConnectionType.SKIP:
                    inputs.append(source_activation * weight)
                elif conn_type == ConnectionType.RESIDUAL:
                    # For residual connections, the dimensions must match
                    # We'll check this when processing the node
                    inputs.append((source_activation, True, weight))  # Mark as residual
                else:
                    inputs.append(source_activation * weight)
            
            # Check if this is a simple aggregation node (ADD, MULTIPLY, CONCATENATE)
            op_type = self.architecture.nodes[node_id].operation.op_type
            
            if op_type == OperationType.ADD:
                # Sum all inputs
                node_input = sum(inp for inp in inputs if not isinstance(inp, tuple))
                # Add residual connections
                for inp, is_res, w in (inp for inp in inputs if isinstance(inp, tuple)):
                    node_input = node_input + inp * w
                
                # No module for ADD, just pass through
                activations[node_id] = node_input
                
            elif op_type == OperationType.MULTIPLY:
                # Multiply all inputs element-wise
                inp0 = next((inp for inp in inputs if not isinstance(inp, tuple)), None)
                if inp0 is not None:
                    node_input = inp0
                    for inp in inputs:
                        if not isinstance(inp, tuple):
                            node_input = node_input * inp
                    
                    # No module for MULTIPLY, just pass through
                    activations[node_id] = node_input
                
            elif op_type == OperationType.CONCATENATE:
                # Concatenate all inputs along a specified dimension
                concat_dim = self.architecture.nodes[node_id].operation.params.get('dim', 1)
                node_input = torch.cat([inp for inp in inputs if not isinstance(inp, tuple)], dim=concat_dim)
                
                # No module for CONCATENATE, just pass through
                activations[node_id] = node_input
                
            else:
                # Regular processing node, apply the module
                # First, aggregate inputs
                node_input = sum(inp for inp in inputs if not isinstance(inp, tuple))
                
                # Apply the module
                if node_id in self.modules_dict:
                    module = self.modules_dict[node_id]
                    node_output = module(node_input)
                else:
                    node_output = node_input
                
                # Add residual connections
                for inp, is_res, w in (inp for inp in inputs if isinstance(inp, tuple)):
                    if node_output.shape == inp.shape:
                        node_output = node_output + inp * w
                    else:
                        logger.warning(f"Shape mismatch for residual connection to {node_id}: "
                                       f"{node_output.shape} vs {inp.shape}")
                
                activations[node_id] = node_output
        
        # Return the outputs
        if len(self.architecture.output_nodes) == 1:
            # Single output node, return the tensor
            return activations[self.architecture.output_nodes[0]]
        else:
            # Multiple output nodes, return a dict
            return {node_id: activations[node_id] for node_id in self.architecture.output_nodes if node_id in activations}


# ------------------------------------------------------------------------------
# Search Space Definitions
# ------------------------------------------------------------------------------

class ArchitectureTemplate:
    """Template for generating neural architectures."""
    
    def __init__(self, 
                 name: str,
                 operation_types: List[OperationType],
                 connection_types: List[ConnectionType],
                 min_layers: int,
                 max_layers: int,
                 min_nodes_per_layer: int,
                 max_nodes_per_layer: int,
                 min_connections: int,
                 max_connections: int,
                 input_shape: Any,
                 output_shape: Any,
                 parameter_ranges: Dict[str, Dict[str, Any]] = None):
        """
        Initialize the architecture template.
        
        Args:
            name: Template name for identification
            operation_types: List of allowed operation types
            connection_types: List of allowed connection types
            min_layers: Minimum number of layers
            max_layers: Maximum number of layers
            min_nodes_per_layer: Minimum nodes per layer
            max_nodes_per_layer: Maximum nodes per layer
            min_connections: Minimum connections per node
            max_connections: Maximum connections per node
            input_shape: Shape of the input data
            output_shape: Shape of the output data
            parameter_ranges: Dictionary mapping parameter names to ranges
        """
        self.name = name
        self.operation_types = operation_types
        self.connection_types = connection_types
        self.min_layers = min_layers
        self.max_layers = max_layers
        self.min_nodes_per_layer = min_nodes_per_layer
        self.max_nodes_per_layer = max_nodes_per_layer
        self.min_connections = min_connections
        self.max_connections = max_connections
        self.input_shape = input_shape
        self.output_shape = output_shape
        self.parameter_ranges = parameter_ranges or {}
    
    def sample_random_architecture(self, seed: Optional[int] = None) -> NeuralArchitecture:
        """
        Sample a random architecture from the template.
        
        Args:
            seed: Optional random seed for reproducibility
            
        Returns:
            NeuralArchitecture: A randomly sampled architecture
        """
        if seed is not None:
            np.random.seed(seed)
        
        # Determine number of layers
        num_layers = np.random.randint(self.min_layers, self.max_layers + 1)
        
        # Initialize architecture
        arch = NeuralArchitecture()
        
        # Create input nodes
        if isinstance(self.input_shape, tuple) and len(self.input_shape) > 0:
            input_node_id = f"input_0"
            arch.nodes[input_node_id] = NodeConfig(
                node_id=input_node_id,
                operation=OperationConfig(
                    op_type=OperationType.IDENTITY,
                    params={}
                ),
                metadata={"layer": 0}
            )
            arch.input_nodes.append(input_node_id)
        else:
            # Multiple input shapes, create separate nodes
            for i, shape in enumerate(self.input_shape):
                input_node_id = f"input_{i}"
                arch.nodes[input_node_id] = NodeConfig(
                    node_id=input_node_id,
                    operation=OperationConfig(
                        op_type=OperationType.IDENTITY,
                        params={}
                    ),
                    metadata={"layer": 0}
                )
                arch.input_nodes.append(input_node_id)
        
        # Create hidden layers
        hidden_layers = []
        
        for layer_idx in range(1, num_layers - 1):
            nodes_in_layer = np.random.randint(self.min_nodes_per_layer, self.max_nodes_per_layer + 1)
            layer_nodes = []
            
            for node_idx in range(nodes_in_layer):
                node_id = f"hidden_{layer_idx}_{node_idx}"
                
                # Sample random operation type
                op_type = np.random.choice(self.operation_types)
                
                # Sample parameters for the operation
                op_params = self._sample_operation_params(op_type)
                
                # Create node
                arch.nodes[node_id] = NodeConfig(
                    node_id=node_id,
                    operation=OperationConfig(
                        op_type=op_type,
                        params=op_params
                    ),
                    metadata={"layer": layer_idx}
                )
                
                layer_nodes.append(node_id)
            
            hidden_layers.append(layer_nodes)
        
        # Create output nodes
        if isinstance(self.output_shape, tuple) and len(self.output_shape) > 0:
            output_node_id = f"output_0"
            arch.nodes[output_node_id] = NodeConfig(
                node_id=output_node_id,
                operation=OperationConfig(
                    op_type=OperationType.IDENTITY,
                    params={}
                ),
                metadata={"layer": num_layers - 1}
            )
            arch.output_nodes.append(output_node_id)
        else:
            # Multiple output shapes, create separate nodes
            for i, shape in enumerate(self.output_shape):
                output_node_id = f"output_{i}"
                arch.nodes[output_node_id] = NodeConfig(
                    node_id=output_node_id,
                    operation=OperationConfig(
                        op_type=OperationType.IDENTITY,
                        params={}
                    ),
                    metadata={"layer": num_layers - 1}
                )
                arch.output_nodes.append(output_node_id)
        
        # Add final hidden layer connecting to outputs if needed
        if len(hidden_layers) == 0 or hidden_layers[-1] == []:
            # No hidden layers or empty last layer, create connections directly from inputs to outputs
            for input_id in arch.input_nodes:
                for output_id in arch.output_nodes:
                    arch.edges.append(EdgeConfig(
                        source_id=input_id,
                        target_id=output_id,
                        connection_type=ConnectionType.FORWARD,
                        weight=1.0
                    ))
        else:
            # Connect last hidden layer to outputs
            for node_id in hidden_layers[-1]:
                for output_id in arch.output_nodes:
                    arch.edges.append(EdgeConfig(
                        source_id=node_id,
                        target_id=output_id,
                        connection_type=ConnectionType.FORWARD,
                        weight=1.0
                    ))
        
        # Connect layers
        for layer_idx in range(len(hidden_layers)):
            current_layer = hidden_layers[layer_idx]
            
            # Connect from previous layer or inputs
            if layer_idx == 0:
                # Connect from inputs
                for node_id in current_layer:
                    connections_count = np.random.randint(self.min_connections, 
                                                         min(self.max_connections, len(arch.input_nodes)) + 1)
                    
                    source_nodes = np.random.choice(arch.input_nodes, 
                                                   size=min(connections_count, len(arch.input_nodes)), 
                                                   replace=False)
                    
                    for source_id in source_nodes:
                        connection_type = np.random.choice(self.connection_types)
                        weight = np.random.uniform(0.1, 1.0)
                        
                        arch.edges.append(EdgeConfig(
                            source_id=source_id,
                            target_id=node_id,
                            connection_type=connection_type,
                            weight=weight
                        ))
            else:
                # Connect from previous hidden layer
                prev_layer = hidden_layers[layer_idx - 1]
                
                for node_id in current_layer:
                    connections_count = np.random.randint(self.min_connections, 
                                                         min(self.max_connections, len(prev_layer)) + 1)
                    
                    if len(prev_layer) > 0:
                        source_nodes = np.random.choice(prev_layer, 
                                                       size=min(connections_count, len(prev_layer)), 
                                                       replace=False)
                        
                        for source_id in source_nodes:
                            connection_type = np.random.choice(self.connection_types)
                            weight = np.random.uniform(0.1, 1.0)
                            
                            arch.edges.append(EdgeConfig(
                                source_id=source_id,
                                target_id=node_id,
                                connection_type=connection_type,
                                weight=weight
                            ))
        
        # Add skip connections across layers (with some probability)
        skip_prob = 0.3
        for layer_i_idx in range(len(hidden_layers)):
            layer_i = hidden_layers[layer_i_idx]
            
            for layer_j_idx in range(layer_i_idx + 2, len(hidden_layers)):
                layer_j = hidden_layers[layer_j_idx]
                
                if np.random.rand() < skip_prob and layer_i and layer_j:
                    # Add a skip connection from layer_i to layer_j
                    source_id = np.random.choice(layer_i)
                    target_id = np.random.choice(layer_j)
                    
                    arch.edges.append(EdgeConfig(
                        source_id=source_id,
                        target_id=target_id,
                        connection_type=ConnectionType.SKIP,
                        weight=np.random.uniform(0.1, 0.5)
                    ))
        
        # Add global parameters
        arch.global_params = self._sample_global_params()
        
        # Add metadata
        arch.metadata = {
            "template_name": self.name,
            "generated_timestamp": time.time(),
            "num_layers": num_layers,
            "num_nodes": len(arch.nodes),
            "num_edges": len(arch.edges)
        }
        
        # Validate and fix if necessary
        valid, messages = arch.validate()
        if not valid:
            logger.warning(f"Sampled architecture is not valid: {messages}")
            # Fix issues
            if "cycles" in ' '.join(messages):
                # Remove edges causing cycles
                arch = self._fix_cycles(arch)
        
        return arch
    
    def _sample_operation_params(self, op_type: OperationType) -> Dict[str, Any]:
        """Sample parameters for a given operation type."""
        params = {}
        
        if op_type == OperationType.LINEAR:
            in_features = self._sample_parameter('linear_in_features', (32, 1024), 64)
            out_features = self._sample_parameter('linear_out_features', (32, 1024), 64)
            bias = self._sample_parameter('linear_bias', (True, False), True)
            
            params = {
                'in_features': in_features,
                'out_features': out_features,
                'bias': bias
            }
            
        elif op_type in [OperationType.CONV1D, OperationType.CONV2D, OperationType.CONV3D]:
            in_channels = self._sample_parameter('conv_in_channels', (1, 256), 3)
            out_channels = self._sample_parameter('conv_out_channels', (1, 256), 16)
            kernel_size = self._sample_parameter('conv_kernel_size', (1, 7), 3)
            stride = self._sample_parameter('conv_stride', (1, 3), 1)
            padding = self._sample_parameter('conv_padding', (0, 3), 1)
            
            params = {
                'in_channels': in_channels,
                'out_channels': out_channels,
                'kernel_size': kernel_size,
                'stride': stride,
                'padding': padding
            }
            
        elif op_type in [OperationType.LSTM, OperationType.GRU]:
            input_size = self._sample_parameter('rnn_input_size', (32, 1024), 64)
            hidden_size = self._sample_parameter('rnn_hidden_size', (32, 1024), 64)
            num_layers = self._sample_parameter('rnn_num_layers', (1, 4), 1)
            bidirectional = self._sample_parameter('rnn_bidirectional', (True, False), False)
            
            params = {
                'input_size': input_size,
                'hidden_size': hidden_size,
                'num_layers': num_layers,
                'bidirectional': bidirectional
            }
            
        elif op_type == OperationType.ATTENTION or op_type == OperationType.MULTI_HEAD_ATTENTION:
            embed_dim = self._sample_parameter('attention_embed_dim', (32, 512), 64)
            num_heads = self._sample_parameter('attention_num_heads', (1, 16), 8)
            dropout = self._sample_parameter('attention_dropout', (0.0, 0.5), 0.1)
            
            params = {
                'embed_dim': embed_dim,
                'num_heads': num_heads,
                'dropout': dropout
            }
            
        elif op_type == OperationType.TRANSFORMER:
            d_model = self._sample_parameter('transformer_d_model', (32, 1024), 512)
            nhead = self._sample_parameter('transformer_nhead', (1, 16), 8)
            dim_feedforward = self._sample_parameter('transformer_dim_feedforward', (128, 2048), 2048)
            dropout = self._sample_parameter('transformer_dropout', (0.0, 0.5), 0.1)
            
            params = {
                'd_model': d_model,
                'nhead': nhead,
                'dim_feedforward': dim_feedforward,
                'dropout': dropout
            }
            
        elif op_type in [OperationType.BATCH_NORM, OperationType.LAYER_NORM]:
            num_features = self._sample_parameter('norm_num_features', (32, 1024), 64)
            
            params = {
                'num_features': num_features
            }
            
        elif op_type == OperationType.DROPOUT:
            p = self._sample_parameter('dropout_p', (0.0, 0.7), 0.5)
            
            params = {
                'p': p
            }
            
        elif op_type in [OperationType.POOLING_MAX, OperationType.POOLING_AVG, OperationType.POOLING_ADAPTIVE]:
            kernel_size = self._sample_parameter('pooling_kernel_size', (1, 5), 2)
            stride = self._sample_parameter('pooling_stride', (1, 3), 2)
            
            params = {
                'kernel_size': kernel_size,
                'stride': stride
            }
            
        return params
    
    def _sample_global_params(self) -> Dict[str, Any]:
        """Sample global architecture parameters."""
        learning_rate = self._sample_parameter('learning_rate', (1e-5, 1e-2), 1e-3)
        weight_decay = self._sample_parameter('weight_decay', (0.0, 1e-3), 1e-4)
        batch_size = self._sample_parameter('batch_size', (16, 512), 64)
        
        return {
            'learning_rate': learning_rate,
            'weight_decay': weight_decay,
            'batch_size': batch_size
        }
    
    def _sample_parameter(self, name: str, default_range: Any, default_value: Any) -> Any:
        """Sample a parameter value from its range."""
        if name in self.parameter_ranges:
            param_range = self.parameter_ranges[name]
            
            if isinstance(param_range, tuple) and len(param_range) == 2:
                min_val, max_val = param_range
                
                if isinstance(default_value, bool):
                    return bool(np.random.choice([True, False]))
                elif isinstance(default_value, int):
                    return np.random.randint(min_val, max_val + 1)
                elif isinstance(default_value, float):
                    return np.random.uniform(min_val, max_val)
                else:
                    return np.random.choice(param_range)
            else:
                return np.random.choice(param_range)
        else:
            min_val, max_val = default_range
            
            if isinstance(default_value, bool):
                return bool(np.random.choice([True, False]))
            elif isinstance(default_value, int):
                return np.random.randint(min_val, max_val + 1)
            elif isinstance(default_value, float):
                return np.random.uniform(min_val, max_val)
            else:
                return default_value
    
    def _fix_cycles(self, arch: NeuralArchitecture) -> NeuralArchitecture:
        """Fix cycles in the architecture by removing edges."""
        G = nx.DiGraph()
        
        # Add nodes
        for node_id in arch.nodes:
            G.add_node(node_id)
        
        # Add edges one by one, checking for cycles
        fixed_edges = []
        
        for edge in arch.edges:
            G.add_edge(edge.source_id, edge.target_id)
            
            # Check for cycles
            try:
                cycles = list(nx.simple_cycles(G))
                if cycles:
                    # Remove the edge that created the cycle
                    G.remove_edge(edge.source_id, edge.target_id)
                else:
                    fixed_edges.append(edge)
            except Exception:
                # Keep the edge if there's an error
                fixed_edges.append(edge)
        
        # Create a new architecture with fixed edges
        fixed_arch = copy.deepcopy(arch)
        fixed_arch.edges = fixed_edges
        
        return fixed_arch


class CNNTemplate(ArchitectureTemplate):
    """Template for generating CNN architectures."""
    
    def __init__(self, 
                 input_shape: tuple = (3, 32, 32),  # (channels, height, width)
                 output_shape: tuple = (10,),       # (num_classes,)
                 min_conv_layers: int = 2,
                 max_conv_layers: int = 6,
                 min_fc_layers: int = 1,
                 max_fc_layers: int = 3,
                 parameter_ranges: Dict[str, Dict[str, Any]] = None):
        """
        Initialize the CNN template.
        
        Args:
            input_shape: Shape of the input images (channels, height, width)
            output_shape: Shape of the output (num_classes,)
            min_conv_layers: Minimum number of convolutional layers
            max_conv_layers: Maximum number of convolutional layers
            min_fc_layers: Minimum number of fully connected layers
            max_fc_layers: Maximum number of fully connected layers
            parameter_ranges: Dictionary mapping parameter names to ranges
        """
        # Define allowed operations for CNNs
        operation_types = [
            OperationType.CONV2D,
            OperationType.POOLING_MAX,
            OperationType.POOLING_AVG,
            OperationType.BATCH_NORM,
            OperationType.ACTIVATION_RELU,
            OperationType.ACTIVATION_SIGMOID,
            OperationType.ACTIVATION_TANH,
            OperationType.DROPOUT,
            OperationType.LINEAR,
            OperationType.FLATTEN
        ]
        
        # Define allowed connections
        connection_types = [
            ConnectionType.FORWARD,
            ConnectionType.SKIP,
            ConnectionType.RESIDUAL
        ]
        
        # Total layers is convolutional + fully connected
        min_layers = min_conv_layers + min_fc_layers
        max_layers = max_conv_layers + max_fc_layers
        
        # Call the parent constructor
        super().__init__(
            name="CNN",
            operation_types=operation_types,
            connection_types=connection_types,
            min_layers=min_layers,
            max_layers=max_layers,
            min_nodes_per_layer=1,
            max_nodes_per_layer=3,
            min_connections=1,
            max_connections=3,
            input_shape=input_shape,
            output_shape=output_shape,
            parameter_ranges=parameter_ranges
        )
        
        # Additional CNN-specific parameters
        self.min_conv_layers = min_conv_layers
        self.max_conv_layers = max_conv_layers
        self.min_fc_layers = min_fc_layers
        self.max_fc_layers = max_fc_layers
    
    def sample_random_architecture(self, seed: Optional[int] = None) -> NeuralArchitecture:
        """
        Sample a random CNN architecture.
        
        Args:
            seed: Optional random seed for reproducibility
            
        Returns:
            NeuralArchitecture: A randomly sampled CNN architecture
        """
        if seed is not None:
            np.random.seed(seed)
        
        # Determine number of convolutional and fully connected layers
        num_conv_layers = np.random.randint(self.min_conv_layers, self.max_conv_layers + 1)
        num_fc_layers = np.random.randint(self.min_fc_layers, self.max_fc_layers + 1)
        
        # Initialize architecture
        arch = NeuralArchitecture()
        
        # Create input node
        input_node_id = "input_0"
        arch.nodes[input_node_id] = NodeConfig(
            node_id=input_node_id,
            operation=OperationConfig(
                op_type=OperationType.IDENTITY,
                params={}
            ),
            metadata={"layer": 0, "type": "input"}
        )
        arch.input_nodes.append(input_node_id)
        
        # Create convolutional layers
        current_layer_id = 1
        last_node_id = input_node_id
        
        # Track current feature dimensions
        in_channels, height, width = self.input_shape
        current_channels = in_channels
        
        for layer_idx in range(num_conv_layers):
            # Determine number of operations in this layer
            ops_in_layer = np.random.randint(1, 4)  # 1 to 3 operations per layer
            
            last_op_id = last_node_id
            
            for op_idx in range(ops_in_layer):
                node_id = f"conv_{layer_idx}_{op_idx}"
                
                # Determine operation type
                if op_idx == 0:
                    # First operation in the layer is always convolution
                    op_type = OperationType.CONV2D
                    
                    # Determine channels for this conv layer (usually increasing)
                    out_channels = self._sample_parameter('conv_out_channels', (current_channels, current_channels * 2), current_channels * 2)
                    kernel_size = self._sample_parameter('conv_kernel_size', (1, 7), 3)
                    stride = self._sample_parameter('conv_stride', (1, 2), 1)
                    padding = self._sample_parameter('conv_padding', (0, 3), 1)
                    
                    # Update current dimensions
                    current_channels = out_channels
                    height = (height + 2 * padding - kernel_size) // stride + 1
                    width = (width + 2 * padding - kernel_size) // stride + 1
                    
                    params = {
                        'in_channels': in_channels,
                        'out_channels': out_channels,
                        'kernel_size': kernel_size,
                        'stride': stride,
                        'padding': padding
                    }
                    
                    # Update for next operation
                    in_channels = out_channels
                    
                elif np.random.rand() < 0.7:
                    # Most operations after convolution are activation or normalization
                    op_choices = [
                        OperationType.ACTIVATION_RELU,
                        OperationType.BATCH_NORM,
                        OperationType.DROPOUT
                    ]
                    op_type = np.random.choice(op_choices)
                    
                    if op_type == OperationType.BATCH_NORM:
                        params = {'num_features': current_channels}
                    elif op_type == OperationType.DROPOUT:
                        params = {'p': np.random.uniform(0.1, 0.5)}
                    else:
                        params = {}
                        
                else:
                    # Sometimes add pooling
                    op_type = np.random.choice([OperationType.POOLING_MAX, OperationType.POOLING_AVG])
                    kernel_size = np.random.randint(2, 4)
                    stride = np.random.randint(1, 3)
                    
                    # Update dimensions after pooling
                    height = (height - kernel_size) // stride + 1
                    width = (width - kernel_size) // stride + 1
                    
                    params = {
                        'kernel_size': kernel_size,
                        'stride': stride
                    }
                
                # Create node
                arch.nodes[node_id] = NodeConfig(
                    node_id=node_id,
                    operation=OperationConfig(
                        op_type=op_type,
                        params=params
                    ),
                    metadata={"layer": current_layer_id, "type": "conv"}
                )
                
                # Connect to previous operation
                arch.edges.append(EdgeConfig(
                    source_id=last_op_id,
                    target_id=node_id,
                    connection_type=ConnectionType.FORWARD,
                    weight=1.0
                ))
                
                last_op_id = node_id
            
            # Add skip connections with some probability
            if layer_idx > 0 and np.random.rand() < 0.3:
                # Find a previous conv layer to connect to
                prev_layer = np.random.randint(0, layer_idx)
                prev_node_candidates = [nid for nid, node in arch.nodes.items() 
                                        if node.metadata.get("layer") == prev_layer + 1 
                                        and node.metadata.get("type") == "conv"]
                
                if prev_node_candidates:
                    prev_node = np.random.choice(prev_node_candidates)
                    
                    # Add skip connection
                    arch.edges.append(EdgeConfig(
                        source_id=prev_node,
                        target_id=last_op_id,
                        connection_type=ConnectionType.SKIP,
                        weight=np.random.uniform(0.1, 0.5)
                    ))
            
            last_node_id = last_op_id
            current_layer_id += 1
        
        # Add flatten node to transition from conv to fully connected
        flatten_id = "flatten"
        arch.nodes[flatten_id] = NodeConfig(
            node_id=flatten_id,
            operation=OperationConfig(
                op_type=OperationType.FLATTEN,
                params={}
            ),
            metadata={"layer": current_layer_id, "type": "flatten"}
        )
        
        # Connect flatten to last conv layer
        arch.edges.append(EdgeConfig(
            source_id=last_node_id,
            target_id=flatten_id,
            connection_type=ConnectionType.FORWARD,
            weight=1.0
        ))
        
        last_node_id = flatten_id
        current_layer_id += 1
        
        # Calculate input size for first FC layer
        fc_input_size = current_channels * height * width
        
        # Create fully connected layers
        for layer_idx in range(num_fc_layers):
            node_id = f"fc_{layer_idx}"
            
            # Determine fc layer size (usually decreasing)
            if layer_idx == num_fc_layers - 1:
                # Last FC layer has output_shape[0] units
                out_features = self.output_shape[0]
            else:
                # Hidden FC layers have decreasing sizes
                out_features = self._sample_parameter('fc_out_features', 
                                                     (self.output_shape[0], fc_input_size),
                                                     max(self.output_shape[0], fc_input_size // 2))
            
            # Create FC node
            arch.nodes[node_id] = NodeConfig(
                node_id=node_id,
                operation=OperationConfig(
                    op_type=OperationType.LINEAR,
                    params={
                        'in_features': fc_input_size,
                        'out_features': out_features,
                        'bias': True
                    }
                ),
                metadata={"layer": current_layer_id, "type": "fc"}
            )
            
            # Connect to previous layer
            arch.edges.append(EdgeConfig(
                source_id=last_node_id,
                target_id=node_id,
                connection_type=ConnectionType.FORWARD,
                weight=1.0
            ))
            
            # Add activation for all but the last FC layer
            if layer_idx < num_fc_layers - 1:
                act_id = f"fc_act_{layer_idx}"
                act_type = np.random.choice([
                    OperationType.ACTIVATION_RELU,
                    OperationType.ACTIVATION_SIGMOID,
                    OperationType.ACTIVATION_TANH
                ])
                
                arch.nodes[act_id] = NodeConfig(
                    node_id=act_id,
                    operation=OperationConfig(
                        op_type=act_type,
                        params={}
                    ),
                    metadata={"layer": current_layer_id, "type": "activation"}
                )
                
                # Connect activation to FC layer
                arch.edges.append(EdgeConfig(
                    source_id=node_id,
                    target_id=act_id,
                    connection_type=ConnectionType.FORWARD,
                    weight=1.0
                ))
                
                # Add dropout with some probability
                if np.random.rand() < 0.3:
                    dropout_id = f"fc_dropout_{layer_idx}"
                    
                    arch.nodes[dropout_id] = NodeConfig(
                        node_id=dropout_id,
                        operation=OperationConfig(
                            op_type=OperationType.DROPOUT,
                            params={'p': np.random.uniform(0.1, 0.5)}
                        ),
                        metadata={"layer": current_layer_id, "type": "dropout"}
                    )
                    
                    # Connect dropout to activation
                    arch.edges.append(EdgeConfig(
                        source_id=act_id,
                        target_id=dropout_id,
                        connection_type=ConnectionType.FORWARD,
                        weight=1.0
                    ))
                    
                    last_node_id = dropout_id
                else:
                    last_node_id = act_id
            else:
                last_node_id = node_id
            
            # Update for next FC layer
            fc_input_size = out_features
            current_layer_id += 1
        
        # Create output node
        output_node_id = "output_0"
        
        if num_fc_layers == 0:
            # If no FC layers, need a final layer to map to output size
            arch.nodes[output_node_id] = NodeConfig(
                node_id=output_node_id,
                operation=OperationConfig(
                    op_type=OperationType.LINEAR,
                    params={
                        'in_features': current_channels * height * width,
                        'out_features': self.output_shape[0],
                        'bias': True
                    }
                ),
                metadata={"layer": current_layer_id, "type": "output"}
            )
        else:
            # Use the last FC layer's output
            arch.nodes[output_node_id] = NodeConfig(
                node_id=output_node_id,
                operation=OperationConfig(
                    op_type=OperationType.IDENTITY,
                    params={}
                ),
                metadata={"layer": current_layer_id, "type": "output"}
            )
        
        arch.output_nodes.append(output_node_id)
        
        # Connect the last node to the output
        arch.edges.append(EdgeConfig(
            source_id=last_node_id,
            target_id=output_node_id,
            connection_type=ConnectionType.FORWARD,
            weight=1.0
        ))
        
        # Add global parameters
        arch.global_params = self._sample_global_params()
        
        # Add metadata
        arch.metadata = {
            "template_name": self.name,
            "generated_timestamp": time.time(),
            "num_conv_layers": num_conv_layers,
            "num_fc_layers": num_fc_layers,
            "num_nodes": len(arch.nodes),
            "num_edges": len(arch.edges)
        }
        
        return arch


class TransformerTemplate(ArchitectureTemplate):
    """Template for generating Transformer architectures."""
    
    def __init__(self, 
                 input_shape: tuple = (512,),  # (sequence_length, )
                 output_shape: tuple = (512,),  # (sequence_length, )
                 min_encoder_layers: int = 2,
                 max_encoder_layers: int = 8,
                 min_decoder_layers: int = 2,
                 max_decoder_layers: int = 8,
                 parameter_ranges: Dict[str, Dict[str, Any]] = None):
        """
        Initialize the Transformer template.
        
        Args:
            input_shape: Shape of the input sequence
            output_shape: Shape of the output sequence
            min_encoder_layers: Minimum number of encoder layers
            max_encoder_layers: Maximum number of encoder layers
            min_decoder_layers: Minimum number of decoder layers
            max_decoder_layers: Maximum number of decoder layers
            parameter_ranges: Dictionary mapping parameter names to ranges
        """
        # Define allowed operations for Transformers
        operation_types = [
            OperationType.TRANSFORMER,
            OperationType.SELF_ATTENTION,
            OperationType.MULTI_HEAD_ATTENTION,
            OperationType.LINEAR,
            OperationType.LAYER_NORM,
            OperationType.DROPOUT,
            OperationType.ACTIVATION_RELU,
            OperationType.ACTIVATION_GELU,
            OperationType.EMBEDDING
        ]
        
        # Define allowed connections
        connection_types = [
            ConnectionType.FORWARD,
            ConnectionType.SKIP,
            ConnectionType.ATTENTION
        ]
        
        # Total layers (conservatively)
        min_layers = min_encoder_layers + min_decoder_layers + 2  # +2 for embedding and output
        max_layers = max_encoder_layers + max_decoder_layers + 2
        
        # Call the parent constructor
        super().__init__(
            name="Transformer",
            operation_types=operation_types,
            connection_types=connection_types,
            min_layers=min_layers,
            max_layers=max_layers,
            min_nodes_per_layer=1,
            max_nodes_per_layer=3,
            min_connections=1,
            max_connections=3,
            input_shape=input_shape,
            output_shape=output_shape,
            parameter_ranges=parameter_ranges
        )
        
        # Additional Transformer-specific parameters
        self.min_encoder_layers = min_encoder_layers
        self.max_encoder_layers = max_encoder_layers
        self.min_decoder_layers = min_decoder_layers
        self.max_decoder_layers = max_decoder_layers
    
    def sample_random_architecture(self, seed: Optional[int] = None) -> NeuralArchitecture:
        """
        Sample a random Transformer architecture.
        
        Args:
            seed: Optional random seed for reproducibility
            
        Returns:
            NeuralArchitecture: A randomly sampled Transformer architecture
        """
        if seed is not None:
            np.random.seed(seed)
        
        # Determine number of encoder and decoder layers
        num_encoder_layers = np.random.randint(self.min_encoder_layers, self.max_encoder_layers + 1)
        num_decoder_layers = np.random.randint(self.min_decoder_layers, self.max_decoder_layers + 1)
        
        # Initialize architecture
        arch = NeuralArchitecture()
        
        # Sample key hyperparameters
        d_model = self._sample_parameter('d_model', (64, 1024), 512)
        d_ff = self._sample_parameter('d_ff', (d_model, d_model * 4), d_model * 4)
        num_heads = self._sample_parameter('num_heads', (1, 16), 8)
        dropout_rate = self._sample_parameter('dropout_rate', (0.0, 0.5), 0.1)
        
        # Create input node
        input_node_id = "input_0"
        arch.nodes[input_node_id] = NodeConfig(
            node_id=input_node_id,
            operation=OperationConfig(
                op_type=OperationType.IDENTITY,
                params={}
            ),
            metadata={"layer": 0, "type": "input"}
        )
        arch.input_nodes.append(input_node_id)
        
        # Create embedding layer
        embedding_id = "embedding"
        arch.nodes[embedding_id] = NodeConfig(
            node_id=embedding_id,
            operation=OperationConfig(
                op_type=OperationType.EMBEDDING,
                params={
                    'vocab_size': self._sample_parameter('vocab_size', (1000, 50000), 30000),
                    'embedding_dim': d_model
                }
            ),
            metadata={"layer": 1, "type": "embedding"}
        )
        
        # Connect input to embedding
        arch.edges.append(EdgeConfig(
            source_id=input_node_id,
            target_id=embedding_id,
            connection_type=ConnectionType.FORWARD,
            weight=1.0
        ))
        
        # Keep track of the current node for connections
        last_encoder_node = embedding_id
        
        # Create encoder layers
        for i in range(num_encoder_layers):
            layer_idx = i + 2  # Offset for input and embedding
            
            # Self-attention node
            attn_id = f"encoder_attn_{i}"
            arch.nodes[attn_id] = NodeConfig(
                node_id=attn_id,
                operation=OperationConfig(
                    op_type=OperationType.MULTI_HEAD_ATTENTION,
                    params={
                        'embed_dim': d_model,
                        'num_heads': num_heads,
                        'dropout': dropout_rate
                    }
                ),
                metadata={"layer": layer_idx, "type": "encoder_attn"}
            )
            
            # Connect previous layer to attention
            arch.edges.append(EdgeConfig(
                source_id=last_encoder_node,
                target_id=attn_id,
                connection_type=ConnectionType.FORWARD,
                weight=1.0
            ))
            
            # Layer norm 1
            norm1_id = f"encoder_norm1_{i}"
            arch.nodes[norm1_id] = NodeConfig(
                node_id=norm1_id,
                operation=OperationConfig(
                    op_type=OperationType.LAYER_NORM,
                    params={
                        'normalized_shape': d_model
                    }
                ),
                metadata={"layer": layer_idx, "type": "encoder_norm"}
            )
            
            # Connect attention to norm1
            arch.edges.append(EdgeConfig(
                source_id=attn_id,
                target_id=norm1_id,
                connection_type=ConnectionType.FORWARD,
                weight=1.0
            ))
            
            # Residual connection from previous layer to after norm1
            arch.edges.append(EdgeConfig(
                source_id=last_encoder_node,
                target_id=norm1_id,
                connection_type=ConnectionType.RESIDUAL,
                weight=1.0
            ))
            
            # Feedforward node
            ff_id = f"encoder_ff_{i}"
            arch.nodes[ff_id] = NodeConfig(
                node_id=ff_id,
                operation=OperationConfig(
                    op_type=OperationType.LINEAR,
                    params={
                        'in_features': d_model,
                        'out_features': d_ff,
                        'bias': True
                    }
                ),
                metadata={"layer": layer_idx, "type": "encoder_ff"}
            )
            
            # Connect norm1 to ff
            arch.edges.append(EdgeConfig(
                source_id=norm1_id,
                target_id=ff_id,
                connection_type=ConnectionType.FORWARD,
                weight=1.0
            ))
            
            # Activation
            act_id = f"encoder_act_{i}"
            arch.nodes[act_id] = NodeConfig(
                node_id=act_id,
                operation=OperationConfig(
                    op_type=OperationType.ACTIVATION_GELU,
                    params={}
                ),
                metadata={"layer": layer_idx, "type": "encoder_act"}
            )
            
            # Connect ff to activation
            arch.edges.append(EdgeConfig(
                source_id=ff_id,
                target_id=act_id,
                connection_type=ConnectionType.FORWARD,
                weight=1.0
            ))
            
            # FF output layer
            ff_out_id = f"encoder_ff_out_{i}"
            arch.nodes[ff_out_id] = NodeConfig(
                node_id=ff_out_id,
                operation=OperationConfig(
                    op_type=OperationType.LINEAR,
                    params={
                        'in_features': d_ff,
                        'out_features': d_model,
                        'bias': True
                    }
                ),
                metadata={"layer": layer_idx, "type": "encoder_ff_out"}
            )
            
            # Connect activation to ff output
            arch.edges.append(EdgeConfig(
                source_id=act_id,
                target_id=ff_out_id,
                connection_type=ConnectionType.FORWARD,
                weight=1.0
            ))
            
            # Layer norm 2
            norm2_id = f"encoder_norm2_{i}"
            arch.nodes[norm2_id] = NodeConfig(
                node_id=norm2_id,
                operation=OperationConfig(
                    op_type=OperationType.LAYER_NORM,
                    params={
                        'normalized_shape': d_model
                    }
                ),
                metadata={"layer": layer_idx, "type": "encoder_norm"}
            )
            
            # Connect ff output to norm2
            arch.edges.append(EdgeConfig(
                source_id=ff_out_id,
                target_id=norm2_id,
                connection_type=ConnectionType.FORWARD,
                weight=1.0
            ))
            
            # Residual connection from norm1 to after norm2
            arch.edges.append(EdgeConfig(
                source_id=norm1_id,
                target_id=norm2_id,
                connection_type=ConnectionType.RESIDUAL,
                weight=1.0
            ))
            
            # Dropout (optional)
            if np.random.rand() < 0.7:
                dropout_id = f"encoder_dropout_{i}"
                arch.nodes[dropout_id] = NodeConfig(
                    node_id=dropout_id,
                    operation=OperationConfig(
                        op_type=OperationType.DROPOUT,
                        params={
                            'p': dropout_rate
                        }
                    ),
                    metadata={"layer": layer_idx, "type": "encoder_dropout"}
                )
                
                # Connect norm2 to dropout
                arch.edges.append(EdgeConfig(
                    source_id=norm2_id,
                    target_id=dropout_id,
                    connection_type=ConnectionType.FORWARD,
                    weight=1.0
                ))
                
                last_encoder_node = dropout_id
            else:
                last_encoder_node = norm2_id
        
        # Create decoder layers
        decoder_input_id = "decoder_input"
        arch.nodes[decoder_input_id] = NodeConfig(
            node_id=decoder_input_id,
            operation=OperationConfig(
                op_type=OperationType.IDENTITY,
                params={}
            ),
            metadata={"layer": 0, "type": "decoder_input"}
        )
        
        # Add as second input
        arch.input_nodes.append(decoder_input_id)
        
        # Decoder embedding
        decoder_embed_id = "decoder_embedding"
        arch.nodes[decoder_embed_id] = NodeConfig(
            node_id=decoder_embed_id,
            operation=OperationConfig(
                op_type=OperationType.EMBEDDING,
                params={
                    'vocab_size': self._sample_parameter('vocab_size', (1000, 50000), 30000),
                    'embedding_dim': d_model
                }
            ),
            metadata={"layer": 1, "type": "decoder_embedding"}
        )
        
        # Connect decoder input to embedding
        arch.edges.append(EdgeConfig(
            source_id=decoder_input_id,
            target_id=decoder_embed_id,
            connection_type=ConnectionType.FORWARD,
            weight=1.0
        ))
        
        last_decoder_node = decoder_embed_id
        
        for i in range(num_decoder_layers):
            layer_idx = i + 2 + num_encoder_layers  # Offset for input, embedding, and encoder layers
            
            # Self-attention node
            self_attn_id = f"decoder_self_attn_{i}"
            arch.nodes[self_attn_id] = NodeConfig(
                node_id=self_attn_id,
                operation=OperationConfig(
                    op_type=OperationType.MULTI_HEAD_ATTENTION,
                    params={
                        'embed_dim': d_model,
                        'num_heads': num_heads,
                        'dropout': dropout_rate
                    }
                ),
                metadata={"layer": layer_idx, "type": "decoder_self_attn"}
            )
            
            # Connect previous layer to self attention
            arch.edges.append(EdgeConfig(
                source_id=last_decoder_node,
                target_id=self_attn_id,
                connection_type=ConnectionType.FORWARD,
                weight=1.0
            ))
            
            # Layer norm 1
            norm1_id = f"decoder_norm1_{i}"
            arch.nodes[norm1_id] = NodeConfig(
                node_id=norm1_id,
                operation=OperationConfig(
                    op_type=OperationType.LAYER_NORM,
                    params={
                        'normalized_shape': d_model
                    }
                ),
                metadata={"layer": layer_idx, "type": "decoder_norm"}
            )
            
            # Connect self-attention to norm1
            arch.edges.append(EdgeConfig(
                source_id=self_attn_id,
                target_id=norm1_id,
                connection_type=ConnectionType.FORWARD,
                weight=1.0
            ))
            
            # Residual connection from previous layer to after norm1
            arch.edges.append(EdgeConfig(
                source_id=last_decoder_node,
                target_id=norm1_id,
                connection_type=ConnectionType.RESIDUAL,
                weight=1.0
            ))
            
            # Cross-attention node
            cross_attn_id = f"decoder_cross_attn_{i}"
            arch.nodes[cross_attn_id] = NodeConfig(
                node_id=cross_attn_id,
                operation=OperationConfig(
                    op_type=OperationType.MULTI_HEAD_ATTENTION,
                    params={
                        'embed_dim': d_model,
                        'num_heads': num_heads,
                        'dropout': dropout_rate
                    }
                ),
                metadata={"layer": layer_idx, "type": "decoder_cross_attn"}
            )
            
            # Connect norm1 to cross attention
            arch.edges.append(EdgeConfig(
                source_id=norm1_id,
                target_id=cross_attn_id,
                connection_type=ConnectionType.FORWARD,
                weight=1.0
            ))
            
            # Connect encoder output to cross attention
            arch.edges.append(EdgeConfig(
                source_id=last_encoder_node,
                target_id=cross_attn_id,
                connection_type=ConnectionType.ATTENTION,
                weight=1.0
            ))
            
            # Layer norm 2
            norm2_id = f"decoder_norm2_{i}"
            arch.nodes[norm2_id] = NodeConfig(
                node_id=norm2_id,
                operation=OperationConfig(
                    op_type=OperationType.LAYER_NORM,
                    params={
                        'normalized_shape': d_model
                    }
                ),
                metadata={"layer": layer_idx, "type": "decoder_norm"}
            )
            
            # Connect cross-attention to norm2
            arch.edges.append(EdgeConfig(
                source_id=cross_attn_id,
                target_id=norm2_id,
                connection_type=ConnectionType.FORWARD,
                weight=1.0
            ))
            
            # Residual connection from norm1 to after norm2
            arch.edges.append(EdgeConfig(
                source_id=norm1_id,
                target_id=norm2_id,
                connection_type=ConnectionType.RESIDUAL,
                weight=1.0
            ))
            
            # Feedforward node
            ff_id = f"decoder_ff_{i}"
            arch.nodes[ff_id] = NodeConfig(
                node_id=ff_id,
                operation=OperationConfig(
                    op_type=OperationType.LINEAR,
                    params={
                        'in_features': d_model,
                        'out_features': d_ff,
                        'bias': True
                    }
                ),
                metadata={"layer": layer_idx, "type": "decoder_ff"}
            )
            
            # Connect norm2 to ff
            arch.edges.append(EdgeConfig(
                source_id=norm2_id,
                target_id=ff_id,
                connection_type=ConnectionType.FORWARD,
                weight=1.0
            ))
            
            # Activation
            act_id = f"decoder_act_{i}"
            arch.nodes[act_id] = NodeConfig(
                node_id=act_id,
                operation=OperationConfig(
                    op_type=OperationType.ACTIVATION_GELU,
                    params={}
                ),
                metadata={"layer": layer_idx, "type": "decoder_act"}
            )
            
            # Connect ff to activation
            arch.edges.append(EdgeConfig(
                source_id=ff_id,
                target_id=act_id,
                connection_type=ConnectionType.FORWARD,
                weight=1.0
            ))
            
            # FF output layer
            ff_out_id = f"decoder_ff_out_{i}"
            arch.nodes[ff_out_id] = NodeConfig(
                node_id=ff_out_id,
                operation=OperationConfig(
                    op_type=OperationType.LINEAR,
                    params={
                        'in_features': d_ff,
                        'out_features': d_model,
                        'bias': True
                    }
                ),
                metadata={"layer": layer_idx, "type": "decoder_ff_out"}
            )
            
            # Connect activation to ff output
            arch.edges.append(EdgeConfig(
                source_id=act_id,
                target_id=ff_out_id,
                connection_type=ConnectionType.FORWARD,
                weight=1.0
            ))
            
            # Layer norm 3
            norm3_id = f"decoder_norm3_{i}"
            arch.nodes[norm3_id] = NodeConfig(
                node_id=norm3_id,
                operation=OperationConfig(
                    op_type=OperationType.LAYER_NORM,
                    params={
                        'normalized_shape': d_model
                    }
                ),
                metadata={"layer": layer_idx, "type": "decoder_norm"}
            )
            
            # Connect ff output to norm3
            arch.edges.append(EdgeConfig(
                source_id=ff_out_id,
                target_id=norm3_id,
                connection_type=ConnectionType.FORWARD,
                weight=1.0
            ))
            
            # Residual connection from norm2 to after norm3
            arch.edges.append(EdgeConfig(
                source_id=norm2_id,
                target_id=norm3_id,
                connection_type=ConnectionType.RESIDUAL,
                weight=1.0
            ))
            
            # Dropout (optional)
            if np.random.rand() < 0.7:
                dropout_id = f"decoder_dropout_{i}"
                arch.nodes[dropout_id] = NodeConfig(
                    node_id=dropout_id,
                    operation=OperationConfig(
                        op_type=OperationType.DROPOUT,
                        params={
                            'p': dropout_rate
                        }
                    ),
                    metadata={"layer": layer_idx, "type": "decoder_dropout"}
                )
                
                # Connect norm3 to dropout
                arch.edges.append(EdgeConfig(
                    source_id=norm3_id,
                    target_id=dropout_id,
                    connection_type=ConnectionType.FORWARD,
                    weight=1.0
                ))
                
                last_decoder_node = dropout_id
            else:
                last_decoder_node = norm3_id
        
        # Output projection
        output_projection_id = "output_projection"
        arch.nodes[output_projection_id] = NodeConfig(
            node_id=output_projection_id,
            operation=OperationConfig(
                op_type=OperationType.LINEAR,
                params={
                    'in_features': d_model,
                    'out_features': self._sample_parameter('vocab_size', (1000, 50000), 30000),
                    'bias': True
                }
            ),
            metadata={"layer": layer_idx + 1, "type": "output_projection"}
        )
        
        # Connect last decoder layer to output projection
        arch.edges.append(EdgeConfig(
            source_id=last_decoder_node,
            target_id=output_projection_id,
            connection_type=ConnectionType.FORWARD,
            weight=1.0
        ))
        
        # Create output node
        output_node_id = "output_0"
        arch.nodes[output_node_id] = NodeConfig(
            node_id=output_node_id,
            operation=OperationConfig(
                op_type=OperationType.IDENTITY,
                params={}
            ),
            metadata={"layer": layer_idx + 2, "type": "output"}
        )
        arch.output_nodes.append(output_node_id)
        
        # Connect output projection to output node
        arch.edges.append(EdgeConfig(
            source_id=output_projection_id,
            target_id=output_node_id,
            connection_type=ConnectionType.FORWARD,
            weight=1.0
        ))
        
        # Add global parameters
        arch.global_params = {
            'learning_rate': self._sample_parameter('learning_rate', (1e-5, 1e-3), 5e-4),
            'weight_decay': self._sample_parameter('weight_decay', (1e-6, 1e-3), 1e-4),
            'batch_size': self._sample_parameter('batch_size', (8, 128), 32),
            'warmup_steps': self._sample_parameter('warmup_steps', (1000, 10000), 4000),
            'label_smoothing': self._sample_parameter('label_smoothing', (0.0, 0.3), 0.1)
        }
        
        # Add metadata
        arch.metadata = {
            "template_name": self.name,
            "generated_timestamp": time.time(),
            "num_encoder_layers": num_encoder_layers,
            "num_decoder_layers": num_decoder_layers,
            "d_model": d_model,
            "d_ff": d_ff,
            "num_heads": num_heads,
            "num_nodes": len(arch.nodes),
            "num_edges": len(arch.edges)
        }
        
        return arch


class HyperparameterSpace:
    """Defines the space of hyperparameters for neural architectures."""
    
    def __init__(self):
        self.param_spaces = {}
    
    def add_continuous_param(self, name: str, min_val: float, max_val: float, default: float = None, log_scale: bool = False):
        """
        Add a continuous hyperparameter.
        
        Args:
            name: Parameter name
            min_val: Minimum value
            max_val: Maximum value
            default: Default value
            log_scale: Whether to sample in log scale
        """
        self.param_spaces[name] = {
            'type': 'continuous',
            'min': min_val,
            'max': max_val,
            'default': default if default is not None else (min_val + max_val) / 2,
            'log_scale': log_scale
        }
    
    def add_integer_param(self, name: str, min_val: int, max_val: int, default: int = None):
        """
        Add an integer hyperparameter.
        
        Args:
            name: Parameter name
            min_val: Minimum value
            max_val: Maximum value
            default: Default value
        """
        self.param_spaces[name] = {
            'type': 'integer',
            'min': min_val,
            'max': max_val,
            'default': default if default is not None else (min_val + max_val) // 2
        }
    
    def add_categorical_param(self, name: str, values: List[Any], default: Any = None):
        """
        Add a categorical hyperparameter.
        
        Args:
            name: Parameter name
            values: List of possible values
            default: Default value
        """
        self.param_spaces[name] = {
            'type': 'categorical',
            'values': values,
            'default': default if default is not None else values[0]
        }
    
    def add_boolean_param(self, name: str, default: bool = True):
        """
        Add a boolean hyperparameter.
        
        Args:
            name: Parameter name
            default: Default value
        """
        self.param_spaces[name] = {
            'type': 'boolean',
            'values': [True, False],
            'default': default
        }
    
    def sample_random_config(self, seed: Optional[int] = None) -> Dict[str, Any]:
        """
        Sample a random hyperparameter configuration.
        
        Args:
            seed: Optional random seed for reproducibility
            
        Returns:
            Dict[str, Any]: A dictionary of sampled hyperparameters
        """
        if seed is not None:
            np.random.seed(seed)
        
        config = {}
        
        for name, space in self.param_spaces.items():
            if space['type'] == 'continuous':
                if space['log_scale']:
                    # Sample in log scale
                    log_min = np.log(space['min'])
                    log_max = np.log(space['max'])
                    value = np.exp(np.random.uniform(log_min, log_max))
                else:
                    # Sample in linear scale
                    value = np.random.uniform(space['min'], space['max'])
            elif space['type'] == 'integer':
                value = np.random.randint(space['min'], space['max'] + 1)
            elif space['type'] in ['categorical', 'boolean']:
                value = np.random.choice(space['values'])
            else:
                value = space['default']
            
            config[name] = value
        
        return config
    
    def get_default_config(self) -> Dict[str, Any]:
        """
        Get the default hyperparameter configuration.
        
        Returns:
            Dict[str, Any]: A dictionary of default hyperparameters
        """
        return {name: space['default'] for name, space in self.param_spaces.items()}
    
    def get_param_space(self, name: str) -> Dict[str, Any]:
        """
        Get the parameter space for a specific hyperparameter.
        
        Args:
            name: Parameter name
            
        Returns:
            Dict[str, Any]: The parameter space
        """
        return self.param_spaces.get(name, None)
    
    def get_all_param_spaces(self) -> Dict[str, Dict[str, Any]]:
        """
        Get all parameter spaces.
        
        Returns:
            Dict[str, Dict[str, Any]]: All parameter spaces
        """
        return self.param_spaces.copy()


class SearchSpace:
    """Defines the search space for neural architecture search."""
    
    def __init__(self, templates: List[ArchitectureTemplate] = None, hyperparameters: HyperparameterSpace = None):
        """
        Initialize the search space.
        
        Args:
            templates: List of architecture templates
            hyperparameters: Hyperparameter space
        """
        self.templates = templates or []
        self.hyperparameters = hyperparameters or HyperparameterSpace()
    
    def add_template(self, template: ArchitectureTemplate):
        """
        Add an architecture template to the search space.
        
        Args:
            template: The architecture template to add
        """
        self.templates.append(template)
    
    def sample_random_architecture(self, seed: Optional[int] = None) -> NeuralArchitecture:
        """
        Sample a random architecture from the search space.
        
        Args:
            seed: Optional random seed for reproducibility
            
        Returns:
            NeuralArchitecture: A randomly sampled architecture
        """
        if seed is not None:
            np.random.seed(seed)
        
        # Choose a random template
        template = np.random.choice(self.templates)
        
        # Sample hyperparameters
        hyperparams = self.hyperparameters.sample_random_config(seed)
        
        # Generate architecture from template
        architecture = template.sample_random_architecture(seed)
        
        # Apply hyperparameters
        for param_name, param_value in hyperparams.items():
            if param_name not in architecture.global_params:
                architecture.global_params[param_name] = param_value
        
        return architecture
    
    @classmethod
    def create_default_search_space(cls, input_shape: Any, output_shape: Any) -> 'SearchSpace':
        """
        Create a default search space with common templates and hyperparameters.
        
        Args:
            input_shape: Shape of the input data
            output_shape: Shape of the output data
            
        Returns:
            SearchSpace: A default search space
        """
        # Create templates
        templates = []
        
        # Check if input shape suggests image data
        if isinstance(input_shape, tuple) and len(input_shape) == 3:
            # Image data, add CNN template
            templates.append(CNNTemplate(
                input_shape=input_shape,
                output_shape=output_shape,
                min_conv_layers=2,
                max_conv_layers=6,
                min_fc_layers=1,
                max_fc_layers=3
            ))
        
        # Check if input shape suggests sequence data
        if isinstance(input_shape, tuple) and len(input_shape) <= 2:
            # Sequence data, add Transformer template
            templates.append(TransformerTemplate(
                input_shape=input_shape,
                output_shape=output_shape,
                min_encoder_layers=2,
                max_encoder_layers=6,
                min_decoder_layers=2,
                max_decoder_layers=6
            ))
        
        # Create hyperparameter space
        hyperparams = HyperparameterSpace()
        
        # Add common hyperparameters
        hyperparams.add_continuous_param('learning_rate', 1e-5, 1e-2, 1e-3, log_scale=True)
        hyperparams.add_continuous_param('weight_decay', 1e-6, 1e-3, 1e-4, log_scale=True)
        hyperparams.add_integer_param('batch_size', 8, 256, 64)
        hyperparams.add_continuous_param('dropout', 0.0, 0.5, 0.1)
        hyperparams.add_boolean_param('use_batch_norm', True)
        
        return cls(templates=templates, hyperparameters=hyperparams)


# ------------------------------------------------------------------------------
# Architecture Evaluation
# ------------------------------------------------------------------------------

class ModelEvaluator:
    """Evaluates neural architectures on specific tasks."""
    
    def __init__(self, task_data: Any = None, device: str = 'cpu'):
        """
        Initialize the model evaluator.
        
        Args:
            task_data: Data for the task to evaluate on
            device: Device to use for evaluation ('cpu' or 'cuda')
        """
        self.task_data = task_data
        self.device = device
        self.evaluation_cache = {}
    
    def evaluate(self, architecture: NeuralArchitecture, metrics: List[str] = None) -> Dict[str, float]:
        """
        Evaluate an architecture on the task.
        
        Args:
            architecture: The architecture to evaluate
            metrics: List of metrics to evaluate
            
        Returns:
            Dict[str, float]: A dictionary of evaluation metrics
        """
        # Check if this architecture has been evaluated before
        arch_hash = architecture.get_hash()
        if arch_hash in self.evaluation_cache:
            return self.evaluation_cache[arch_hash]
        
        # Default metrics
        if metrics is None:
            metrics = ['accuracy', 'loss', 'params', 'flops', 'latency']
        
        # Initialize results
        results = {}
        
        # Convert to PyTorch model for evaluation
        try:
            model = self._build_model(architecture)
            model.to(self.device)
            
            # Calculate model size (number of parameters)
            results['params'] = self._count_parameters(model)
            
            # Estimate FLOPs
            results['flops'] = self._estimate_flops(model, architecture)
            
            # Measure inference latency
            results['latency'] = self._measure_latency(model, architecture)
            
            # If task data is available, evaluate accuracy and loss
            if self.task_data is not None:
                accuracy, loss = self._evaluate_performance(model, architecture)
                results['accuracy'] = accuracy
                results['loss'] = loss
            else:
                # If no task data, use estimated values
                results['accuracy'] = self._estimate_accuracy(architecture)
                results['loss'] = self._estimate_loss(architecture)
        
        except Exception as e:
            logger.warning(f"Error evaluating architecture: {str(e)}")
            # Assign poor scores to architectures that fail
            results = {
                'accuracy': 0.0,
                'loss': float('inf'),
                'params': float('inf'),
                'flops': float('inf'),
                'latency': float('inf')
            }
        
        # Compute an aggregate score
        results['score'] = self._compute_aggregate_score(results)
        
        # Cache the results
        self.evaluation_cache[arch_hash] = results
        
        return results
    
    def _build_model(self, architecture: NeuralArchitecture) -> torch.nn.Module:
        """Build a PyTorch model from the architecture."""
        return PyTorchModel(architecture)
    
    def _count_parameters(self, model: torch.nn.Module) -> int:
        """Count the number of trainable parameters in the model."""
        return sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    def _estimate_flops(self, model: torch.nn.Module, architecture: NeuralArchitecture) -> int:
        """Estimate the FLOPs required for a forward pass."""
        try:
            from thop import profile
            
            # Create dummy input based on architecture's input shape
            dummy_inputs = []
            
            for node_id in architecture.input_nodes:
                # Get the expected input shape from the node's metadata
                node = architecture.nodes[node_id]
                input_shape = node.metadata.get('input_shape', (1, 32, 32, 3))  # Default shape
                
                # Create dummy tensor
                dummy_input = torch.randn(1, *input_shape).to(self.device)
                dummy_inputs.append(dummy_input)
            
            # If multiple inputs, use a dictionary
            if len(dummy_inputs) > 1:
                dummy_input = {node_id: dummy_inputs[i] for i, node_id in enumerate(architecture.input_nodes)}
            else:
                dummy_input = dummy_inputs[0]
            
            # Profile the model
            flops, _ = profile(model, inputs=(dummy_input,), verbose=False)
            
            return flops
        except ImportError:
            # If thop is not available, use a simple estimate based on parameter count
            param_count = self._count_parameters(model)
            return param_count * 2  # Simple heuristic: 2 FLOPs per parameter
    
    def _measure_latency(self, model: torch.nn.Module, architecture: NeuralArchitecture) -> float:
        """Measure the inference latency of the model."""
        # Create dummy input based on architecture's input shape
        dummy_inputs = []
        
        for node_id in architecture.input_nodes:
            # Get the expected input shape from the node's metadata
            node = architecture.nodes[node_id]
            input_shape = node.metadata.get('input_shape', (1, 32, 32, 3))  # Default shape
            
            # Create dummy tensor
            dummy_input = torch.randn(1, *input_shape).to(self.device)
            dummy_inputs.append(dummy_input)
        
        # If multiple inputs, use a dictionary
        if len(dummy_inputs) > 1:
            dummy_input = {node_id: dummy_inputs[i] for i, node_id in enumerate(architecture.input_nodes)}
        else:
            dummy_input = dummy_inputs[0]
        
        # Warmup
        for _ in range(5):
            with torch.no_grad():
                model(dummy_input)
        
        # Measure latency
        num_iterations = 50
        start_time = time.time()
        
        for _ in range(num_iterations):
            with torch.no_grad():
                model(dummy_input)
        
        end_time = time.time()
        
        # Average latency in milliseconds
        latency = (end_time - start_time) * 1000 / num_iterations
        
        return latency
    
    def _evaluate_performance(self, model: torch.nn.Module, architecture: NeuralArchitecture) -> Tuple[float, float]:
        """Evaluate the model's performance on the task data."""
        # This is a simplified implementation. In practice, you would:
        # 1. Load task-specific data into a DataLoader
        # 2. Define an appropriate loss function
        # 3. Iterate through the data and compute loss and metrics
        # 4. Return the average results
        
        # For now, return simulated performance
        return self._estimate_accuracy(architecture), self._estimate_loss(architecture)
    
    def _estimate_accuracy(self, architecture: NeuralArchitecture) -> float:
        """Estimate the accuracy of the architecture on the task."""
        # This is a simplified estimation. In practice, you would use actual evaluation results.
        
        # Start with a baseline accuracy
        accuracy = 0.7
        
        # Adjust based on architecture complexity
        num_nodes = len(architecture.nodes)
        num_edges = len(architecture.edges)
        
        # More complex architectures tend to perform better, up to a point
        complexity_factor = min(0.2, 0.05 * math.log(1 + num_nodes + num_edges))
        accuracy += complexity_factor
        
        # Add some randomness to simulate real-world variation
        accuracy += np.random.uniform(-0.05, 0.05)
        
        # Ensure accuracy is in [0, 1]
        accuracy = max(0.0, min(1.0, accuracy))
        
        return accuracy
    
    def _estimate_loss(self, architecture: NeuralArchitecture) -> float:
        """Estimate the loss of the architecture on the task."""
        # Loss is roughly inversely proportional to accuracy
        accuracy = self._estimate_accuracy(architecture)
        loss = 1.0 - accuracy + 0.2
        
        # Add some randomness
        loss += np.random.uniform(-0.1, 0.1)
        
        # Ensure loss is positive
        loss = max(0.1, loss)
        
        return loss
    
    def _compute_aggregate_score(self, results: Dict[str, float]) -> float:
        """Compute an aggregate score from individual metrics."""
        # Higher is better for the aggregate score
        
        # Normalize and invert metrics where needed
        accuracy_norm = results.get('accuracy', 0.5)  # Higher is better
        
        # Invert loss (lower is better -> higher is better)
        loss = results.get('loss', 1.0)
        loss_norm = 1.0 / (1.0 + loss)  # Normalize to [0, 1]
        
        # Normalize and invert parameter count (lower is better)
        params = results.get('params', 1e6)
        params_norm = 1.0 / (1.0 + params / 1e6)  # Normalize to [0, 1]
        
        # Normalize and invert FLOPs (lower is better)
        flops = results.get('flops', 1e9)
        flops_norm = 1.0 / (1.0 + flops / 1e9)  # Normalize to [0, 1]
        
        # Normalize and invert latency (lower is better)
        latency = results.get('latency', 100.0)
        latency_norm = 1.0 / (1.0 + latency / 100.0)  # Normalize to [0, 1]
        
        # Combine metrics with weights
        weights = {
            'accuracy': 0.5,    # Accuracy is the most important
            'loss': 0.2,        # Loss is also important
            'params': 0.1,      # Parameter count affects model size
            'flops': 0.1,       # FLOPs affect computational cost
            'latency': 0.1      # Latency affects real-time performance
        }
        
        score = (
            weights['accuracy'] * accuracy_norm +
            weights['loss'] * loss_norm +
            weights['params'] * params_norm +
            weights['flops'] * flops_norm +
            weights['latency'] * latency_norm
        )
        
        return score


# ------------------------------------------------------------------------------
# Search Strategies
# ------------------------------------------------------------------------------

class SearchStrategy(ABC):
    """Abstract base class for architecture search strategies."""
    
    @abstractmethod
    def initialize(self, search_space: SearchSpace, evaluator: ModelEvaluator):
        """
        Initialize the search strategy.
        
        Args:
            search_space: The architecture search space
            evaluator: The model evaluator
        """
        pass
    
    @abstractmethod
    def next_batch(self, batch_size: int = 10) -> List[NeuralArchitecture]:
        """
        Generate the next batch of architectures to evaluate.
        
        Args:
            batch_size: Number of architectures to generate
            
        Returns:
            List[NeuralArchitecture]: A batch of architectures
        """
        pass
    
    @abstractmethod
    def update(self, architectures: List[NeuralArchitecture], evaluations: List[Dict[str, float]]):
        """
        Update the search strategy based on evaluation results.
        
        Args:
            architectures: The evaluated architectures
            evaluations: The evaluation results
        """
        pass
    
    @abstractmethod
    def get_best_architecture(self) -> Tuple[NeuralArchitecture, Dict[str, float]]:
        """
        Get the best architecture found so far.
        
        Returns:
            Tuple[NeuralArchitecture, Dict[str, float]]: The best architecture and its evaluation
        """
        pass
    
    @abstractmethod
    def is_finished(self) -> bool:
        """
        Check if the search is finished.
        
        Returns:
            bool: Whether the search is finished
        """
        pass


class RandomSearch(SearchStrategy):
    """Random search strategy for neural architecture search."""
    
    def __init__(self, max_iterations: int = 100):
        """
        Initialize the random search strategy.
        
        Args:
            max_iterations: Maximum number of iterations
        """
        self.max_iterations = max_iterations
        self.search_space = None
        self.evaluator = None
        self.best_architecture = None
        self.best_evaluation = None
        self.iteration = 0
        self.history = []
    
    def initialize(self, search_space: SearchSpace, evaluator: ModelEvaluator):
        """Initialize the search strategy."""
        self.search_space = search_space
        self.evaluator = evaluator
        self.best_architecture = None
        self.best_evaluation = None
        self.iteration = 0
        self.history = []
    
    def next_batch(self, batch_size: int = 10) -> List[NeuralArchitecture]:
        """Generate the next batch of architectures to evaluate."""
        architectures = []
        
        for _ in range(batch_size):
            arch = self.search_space.sample_random_architecture()
            architectures.append(arch)
        
        return architectures
    
    def update(self, architectures: List[NeuralArchitecture], evaluations: List[Dict[str, float]]):
        """Update the search strategy based on evaluation results."""
        for arch, eval_result in zip(architectures, evaluations):
            # Update history
            self.history.append((arch, eval_result))
            
            # Update best architecture
            if self.best_evaluation is None or eval_result['score'] > self.best_evaluation['score']:
                self.best_architecture = arch
                self.best_evaluation = eval_result
        
        self.iteration += 1
    
    def get_best_architecture(self) -> Tuple[NeuralArchitecture, Dict[str, float]]:
        """Get the best architecture found so far."""
        if self.best_architecture is None:
            raise ValueError("No architectures evaluated yet")
        
        return self.best_architecture, self.best_evaluation
    
    def is_finished(self) -> bool:
        """Check if the search is finished."""
        return self.iteration >= self.max_iterations


class EvolutionarySearch(SearchStrategy):
    """Evolutionary search strategy for neural architecture search."""
    
    def __init__(self, 
                 population_size: int = 50, 
                 mutation_rate: float = 0.1, 
                 crossover_rate: float = 0.5,
                 tournament_size: int = 5,
                 max_iterations: int = 50):
        """
        Initialize the evolutionary search strategy.
        
        Args:
            population_size: Size of the population
            mutation_rate: Probability of mutation
            crossover_rate: Probability of crossover
            tournament_size: Size of tournament for selection
            max_iterations: Maximum number of iterations
        """
        self.population_size = population_size
        self.mutation_rate = mutation_rate
        self.crossover_rate = crossover_rate
        self.tournament_size = tournament_size
        self.max_iterations = max_iterations
        
        self.search_space = None
        self.evaluator = None
        self.population = []
        self.evaluations = []
        self.best_architecture = None
        self.best_evaluation = None
        self.iteration = 0
        self.history = []
    
    def initialize(self, search_space: SearchSpace, evaluator: ModelEvaluator):
        """Initialize the search strategy."""
        self.search_space = search_space
        self.evaluator = evaluator
        self.population = []
        self.evaluations = []
        self.best_architecture = None
        self.best_evaluation = None
        self.iteration = 0
        self.history = []
        
        # Generate initial population
        self.population = self.next_batch(self.population_size)
    
    def next_batch(self, batch_size: int = 10) -> List[NeuralArchitecture]:
        """Generate the next batch of architectures to evaluate."""
        if not self.population:
            # Initial population: sample random architectures
            architectures = []
            
            for _ in range(batch_size):
                arch = self.search_space.sample_random_architecture()
                architectures.append(arch)
            
            return architectures
        
        # Evolution: perform selection, crossover, and mutation
        architectures = []
        
        while len(architectures) < batch_size:
            # Selection
            parent1 = self._tournament_selection()
            parent2 = self._tournament_selection()
            
            # Crossover
            if np.random.rand() < self.crossover_rate:
                child = self._crossover(parent1, parent2)
            else:
                child = copy.deepcopy(parent1)
            
            # Mutation
            child = self._mutate(child)
            
            architectures.append(child)
        
        return architectures
    
    def update(self, architectures: List[NeuralArchitecture], evaluations: List[Dict[str, float]]):
        """Update the search strategy based on evaluation results."""
        # Add new architectures to the population
        for arch, eval_result in zip(architectures, evaluations):
            # Update history
            self.history.append((arch, eval_result))
            
            # Update best architecture
            if self.best_evaluation is None or eval_result['score'] > self.best_evaluation['score']:
                self.best_architecture = arch
                self.best_evaluation = eval_result
            
            # Add to population with evaluation
            self.population.append(arch)
            self.evaluations.append(eval_result)
        
        # If population exceeds size, keep only the best individuals
        if len(self.population) > self.population_size:
            # Sort by score (descending)
            sorted_indices = np.argsort([-e['score'] for e in self.evaluations])
            
            # Keep the best
            self.population = [self.population[i] for i in sorted_indices[:self.population_size]]
            self.evaluations = [self.evaluations[i] for i in sorted_indices[:self.population_size]]
        
        self.iteration += 1
    
    def get_best_architecture(self) -> Tuple[NeuralArchitecture, Dict[str, float]]:
        """Get the best architecture found so far."""
        if self.best_architecture is None:
            raise ValueError("No architectures evaluated yet")
        
        return self.best_architecture, self.best_evaluation
    
    def is_finished(self) -> bool:
        """Check if the search is finished."""
        return self.iteration >= self.max_iterations
    
    def _tournament_selection(self) -> NeuralArchitecture:
        """Select an architecture using tournament selection."""
        indices = np.random.choice(len(self.population), self.tournament_size, replace=False)
        scores = [self.evaluations[i]['score'] for i in indices]
        winner_idx = indices[np.argmax(scores)]
        return copy.deepcopy(self.population[winner_idx])
    
    def _crossover(self, parent1: NeuralArchitecture, parent2: NeuralArchitecture) -> NeuralArchitecture:
        """Perform crossover between two parent architectures."""
        # Create a new architecture
        child = NeuralArchitecture()
        
        # Copy input and output nodes from parent1
        child.input_nodes = copy.deepcopy(parent1.input_nodes)
        child.output_nodes = copy.deepcopy(parent1.output_nodes)
        
        # Crossover nodes
        nodes_to_keep = {}
        
        # Choose which parent's nodes to keep for each node ID
        for node_id in set(parent1.nodes.keys()) | set(parent2.nodes.keys()):
            if node_id in parent1.nodes and node_id in parent2.nodes:
                # Both parents have this node, choose randomly
                parent = np.random.choice([parent1, parent2])
                nodes_to_keep[node_id] = parent
            elif node_id in parent1.nodes:
                # Only parent1 has this node
                nodes_to_keep[node_id] = parent1
            else:
                # Only parent2 has this node
                nodes_to_keep[node_id] = parent2
        
        # Keep selected nodes
        for node_id, parent in nodes_to_keep.items():
            child.nodes[node_id] = copy.deepcopy(parent.nodes[node_id])
        
        # Crossover edges
        for parent in [parent1, parent2]:
            for edge in parent.edges:
                # Include edge if both source and target nodes are in the child
                if edge.source_id in child.nodes and edge.target_id in child.nodes:
                    # With some probability, include the edge
                    if np.random.rand() < 0.5:
                        child.edges.append(copy.deepcopy(edge))
        
        # Remove duplicate edges
        unique_edges = {}
        for edge in child.edges:
            key = (edge.source_id, edge.target_id)
            unique_edges[key] = edge
        
        child.edges = list(unique_edges.values())
        
        # Crossover global parameters
        for param_name in set(parent1.global_params.keys()) | set(parent2.global_params.keys()):
            if np.random.rand() < 0.5 and param_name in parent1.global_params:
                child.global_params[param_name] = parent1.global_params[param_name]
            elif param_name in parent2.global_params:
                child.global_params[param_name] = parent2.global_params[param_name]
        
        # Copy metadata from parent1
        child.metadata = copy.deepcopy(parent1.metadata)
        
        # Validate and fix the child if needed
        valid, messages = child.validate()
        if not valid:
            # Fix common issues
            if 'cycles' in ' '.join(messages):
                # Fix cycles by removing problematic edges
                child = self._fix_cycles(child)
            
            # Ensure all nodes have connections
            child = self._ensure_connectivity(child)
        
        return child
    
    def _mutate(self, architecture: NeuralArchitecture) -> NeuralArchitecture:
        """Mutate an architecture."""
        # Create a copy to mutate
        mutated = copy.deepcopy(architecture)
        
        # Mutate operations
        for node_id in mutated.nodes:
            # Skip input and output nodes
            if node_id in mutated.input_nodes or node_id in mutated.output_nodes:
                continue
            
            # With some probability, mutate the operation
            if np.random.rand() < self.mutation_rate:
                node = mutated.nodes[node_id]
                
                # Choose a random operation type
                op_types = list(OperationType)
                node.operation.op_type = np.random.choice(op_types)
                
                # Update parameters for the new operation type
                node.operation.params = self._sample_operation_params(node.operation.op_type)
        
        # Mutate connections
        for i, edge in enumerate(mutated.edges):
            # With some probability, mutate the connection type
            if np.random.rand() < self.mutation_rate:
                conn_types = list(ConnectionType)
                mutated.edges[i].connection_type = np.random.choice(conn_types)
            
            # With some probability, mutate the weight
            if np.random.rand() < self.mutation_rate:
                mutated.edges[i].weight = np.random.uniform(0.1, 1.0)
        
        # Add new nodes (with low probability)
        if np.random.rand() < self.mutation_rate * 0.2:
            # Add a new node between two existing ones
            if mutated.edges:
                # Choose a random edge to split
                edge_idx = np.random.randint(0, len(mutated.edges))
                edge = mutated.edges[edge_idx]
                
                # Create a new node
                node_id = f"node_{int(time.time() * 1000) % 10000}_{np.random.randint(0, 10000)}"
                op_types = list(OperationType)
                op_type = np.random.choice(op_types)
                
                mutated.nodes[node_id] = NodeConfig(
                    node_id=node_id,
                    operation=OperationConfig(
                        op_type=op_type,
                        params=self._sample_operation_params(op_type)
                    ),
                    metadata={"layer": 1, "type": "hidden"}
                )
                
                # Create new edges
                source_id = edge.source_id
                target_id = edge.target_id
                conn_types = list(ConnectionType)
                
                mutated.edges.append(EdgeConfig(
                    source_id=source_id,
                    target_id=node_id,
                    connection_type=np.random.choice(conn_types),
                    weight=np.random.uniform(0.1, 1.0)
                ))
                
                mutated.edges.append(EdgeConfig(
                    source_id=node_id,
                    target_id=target_id,
                    connection_type=np.random.choice(conn_types),
                    weight=np.random.uniform(0.1, 1.0)
                ))
                
                # Remove the original edge
                mutated.edges.pop(edge_idx)
        
        # Remove nodes (with low probability)
        if np.random.rand() < self.mutation_rate * 0.1:
            removable_nodes = [node_id for node_id in mutated.nodes
                               if node_id not in mutated.input_nodes
                               and node_id not in mutated.output_nodes]
            
            if removable_nodes:
                # Choose a random node to remove
                node_id = np.random.choice(removable_nodes)
                
                # Find all edges connected to this node
                in_edges = [i for i, e in enumerate(mutated.edges) if e.target_id == node_id]
                out_edges = [i for i, e in enumerate(mutated.edges) if e.source_id == node_id]
                
                # Create bypass connections
                for in_idx in in_edges:
                    source_id = mutated.edges[in_idx].source_id
                    
                    for out_idx in out_edges:
                        target_id = mutated.edges[out_idx].target_id
                        
                        # Add bypass edge
                        conn_types = list(ConnectionType)
                        mutated.edges.append(EdgeConfig(
                            source_id=source_id,
                            target_id=target_id,
                            connection_type=np.random.choice(conn_types),
                            weight=np.random.uniform(0.1, 1.0)
                        ))
                
                # Remove edges connected to the node
                for idx in sorted(in_edges + out_edges, reverse=True):
                    if idx < len(mutated.edges):
                        mutated.edges.pop(idx)
                
                # Remove the node
                del mutated.nodes[node_id]
        
        # Add new edges (with some probability)
        if np.random.rand() < self.mutation_rate * 0.3:
            # Choose random source and target nodes
            source_candidates = list(mutated.nodes.keys())
            target_candidates = [node_id for node_id in mutated.nodes 
                                if node_id not in mutated.input_nodes]
            
            if source_candidates and target_candidates:
                source_id = np.random.choice(source_candidates)
                target_id = np.random.choice(target_candidates)
                
                # Avoid self-loops
                if source_id != target_id:
                    conn_types = list(ConnectionType)
                    mutated.edges.append(EdgeConfig(
                        source_id=source_id,
                        target_id=target_id,
                        connection_type=np.random.choice(conn_types),
                        weight=np.random.uniform(0.1, 1.0)
                    ))
        
        # Remove edges (with some probability)
        if np.random.rand() < self.mutation_rate * 0.2 and len(mutated.edges) > 1:
            # Choose a random edge to remove
            edge_idx = np.random.randint(0, len(mutated.edges))
            mutated.edges.pop(edge_idx)
        
        # Mutate global parameters
        for param_name in mutated.global_params:
            if np.random.rand() < self.mutation_rate:
                # Perturb the parameter value
                value = mutated.global_params[param_name]
                
                if isinstance(value, float):
                    # Multiply by a random factor between 0.5 and 2.0
                    factor = np.exp(np.random.uniform(-0.7, 0.7))
                    mutated.global_params[param_name] = value * factor
                elif isinstance(value, int):
                    # Add or subtract a random value
                    delta = np.random.randint(-value // 4, value // 4 + 1)
                    mutated.global_params[param_name] = max(1, value + delta)
                elif isinstance(value, bool):
                    # Flip the value
                    mutated.global_params[param_name] = not value
        
        # Validate and fix the mutated architecture
        valid, messages = mutated.validate()
        if not valid:
            # Fix common issues
            if 'cycles' in ' '.join(messages):
                mutated = self._fix_cycles(mutated)
            
            # Ensure all nodes have connections
            mutated = self._ensure_connectivity(mutated)
        
        return mutated
    
    def _fix_cycles(self, architecture: NeuralArchitecture) -> NeuralArchitecture:
        """Fix cycles in the architecture."""
        # Create a graph to detect cycles
        G = nx.DiGraph()
        
        # Add nodes
        for node_id in architecture.nodes:
            G.add_node(node_id)
        
        # Add edges one by one, checking for cycles
        fixed_edges = []
        
        for edge in architecture.edges:
            G.add_edge(edge.source_id, edge.target_id)
            
            # Check for cycles
            try:
                cycles = list(nx.simple_cycles(G))
                if cycles:
                    # Remove the edge that created the cycle
                    G.remove_edge(edge.source_id, edge.target_id)
                else:
                    fixed_edges.append(edge)
            except Exception:
                # Keep the edge if there's an error in cycle detection
                fixed_edges.append(edge)
        
        # Create a new architecture with fixed edges
        fixed_arch = copy.deepcopy(architecture)
        fixed_arch.edges = fixed_edges
        
        return fixed_arch
    
    def _ensure_connectivity(self, architecture: NeuralArchitecture) -> NeuralArchitecture:
        """Ensure all nodes are connected in the graph."""
        # Find nodes without incoming edges (except input nodes)
        nodes_without_incoming = set(architecture.nodes.keys()) - set(architecture.input_nodes)
        
        for edge in architecture.edges:
            if edge.target_id in nodes_without_incoming:
                nodes_without_incoming.remove(edge.target_id)
        
        # Connect these nodes to input nodes
        for node_id in nodes_without_incoming:
            if architecture.input_nodes:
                source_id = np.random.choice(architecture.input_nodes)
                conn_types = list(ConnectionType)
                
                architecture.edges.append(EdgeConfig(
                    source_id=source_id,
                    target_id=node_id,
                    connection_type=np.random.choice(conn_types),
                    weight=np.random.uniform(0.1, 1.0)
                ))
        
        # Find nodes without outgoing edges (except output nodes)
        nodes_without_outgoing = set(architecture.nodes.keys()) - set(architecture.output_nodes)
        
        for edge in architecture.edges:
            if edge.source_id in nodes_without_outgoing:
                nodes_without_outgoing.remove(edge.source_id)
        
        # Connect these nodes to output nodes
        for node_id in nodes_without_outgoing:
            if architecture.output_nodes:
                target_id = np.random.choice(architecture.output_nodes)
                conn_types = list(ConnectionType)
                
                architecture.edges.append(EdgeConfig(
                    source_id=node_id,
                    target_id=target_id,
                    connection_type=np.random.choice(conn_types),
                    weight=np.random.uniform(0.1, 1.0)
                ))
        
        return architecture
    
    def _sample_operation_params(self, op_type: OperationType) -> Dict[str, Any]:
        """Sample parameters for a given operation type."""
        params = {}
        
        if op_type == OperationType.LINEAR:
            params = {
                'in_features': np.random.randint(32, 1024),
                'out_features': np.random.randint(32, 1024),
                'bias': bool(np.random.choice([True, False]))
            }
        elif op_type in [OperationType.CONV1D, OperationType.CONV2D, OperationType.CONV3D]:
            params = {
                'in_channels': np.random.randint(1, 256),
                'out_channels': np.random.randint(1, 256),
                'kernel_size': np.random.randint(1, 7),
                'stride': np.random.randint(1, 3),
                'padding': np.random.randint(0, 3)
            }
        elif op_type in [OperationType.LSTM, OperationType.GRU]:
            params = {
                'input_size': np.random.randint(32, 1024),
                'hidden_size': np.random.randint(32, 1024),
                'num_layers': np.random.randint(1, 4),
                'bidirectional': bool(np.random.choice([True, False]))
            }
        elif op_type == OperationType.ATTENTION or op_type == OperationType.MULTI_HEAD_ATTENTION:
            params = {
                'embed_dim': np.random.randint(32, 512),
                'num_heads': np.random.randint(1, 16),
                'dropout': np.random.uniform(0.0, 0.5)
            }
        elif op_type == OperationType.TRANSFORMER:
            params = {
                'd_model': np.random.randint(32, 1024),
                'nhead': np.random.randint(1, 16),
                'dim_feedforward': np.random.randint(128, 2048),
                'dropout': np.random.uniform(0.0, 0.5)
            }
        elif op_type in [OperationType.BATCH_NORM, OperationType.LAYER_NORM]:
            params = {
                'num_features': np.random.randint(32, 1024)
            }
        elif op_type == OperationType.DROPOUT:
            params = {
                'p': np.random.uniform(0.0, 0.7)
            }
        elif op_type in [OperationType.POOLING_MAX, OperationType.POOLING_AVG, OperationType.POOLING_ADAPTIVE]:
            params = {
                'kernel_size': np.random.randint(1, 5),
                'stride': np.random.randint(1, 3)
            }
        
        return params


class BayesianOptimizationSearch(SearchStrategy):
    """Bayesian optimization search strategy for neural architecture search."""
    
    def __init__(self, 
                 initial_points: int = 10, 
                 acquisition_function: str = 'ei', 
                 exploration_weight: float = 0.1,
                 max_iterations: int = 50):
        """
        Initialize the Bayesian optimization search strategy.
        
        Args:
            initial_points: Number of random points to sample initially
            acquisition_function: Acquisition function ('ei', 'ucb', 'pi')
            exploration_weight: Weight for exploration in UCB
            max_iterations: Maximum number of iterations
        """
        self.initial_points = initial_points
        self.acquisition_function = acquisition_function
        self.exploration_weight = exploration_weight
        self.max_iterations = max_iterations
        
        self.search_space = None
        self.evaluator = None
        self.architectures = []
        self.evaluations = []
        self.best_architecture = None
        self.best_evaluation = None
        self.iteration = 0
        self.history = []
        
        # Check if required packages are available
        try:
            import scipy
            import sklearn
            from sklearn.gaussian_process import GaussianProcessRegressor
            from sklearn.gaussian_process.kernels import Matern
            
            self.GaussianProcessRegressor = GaussianProcessRegressor
            self.Matern = Matern
        except ImportError:
            logger.error("scikit-learn and scipy are required for BayesianOptimizationSearch")
            raise
            
        self.surrogate_model = None
        self.X = None  # Feature representation of architectures
        self.y = None  # Scores
    
    def initialize(self, search_space: SearchSpace, evaluator: ModelEvaluator):
        """Initialize the search strategy."""
        self.search_space = search_space
        self.evaluator = evaluator
        self.architectures = []
        self.evaluations = []
        self.best_architecture = None
        self.best_evaluation = None
        self.iteration = 0
        self.history = []
        
        # Initialize surrogate model
        kernel = self.Matern(nu=2.5)
        self.surrogate_model = self.GaussianProcessRegressor(
            kernel=kernel,
            n_restarts_optimizer=10,
            normalize_y=True,
            random_state=42
        )
    
    def next_batch(self, batch_size: int = 10) -> List[NeuralArchitecture]:
        """Generate the next batch of architectures to evaluate."""
        if len(self.architectures) < self.initial_points:
            # Initial exploration: generate random architectures
            architectures = []
            
            for _ in range(batch_size):
                arch = self.search_space.sample_random_architecture()
                architectures.append(arch)
            
            return architectures
        
        # Update surrogate model
        self._update_surrogate_model()
        
        # Generate candidates using acquisition function
        architectures = []
        
        for _ in range(batch_size):
            # Generate a pool of random architectures to evaluate
            pool_size = 100
            candidate_pool = []
            
            for _ in range(pool_size):
                arch = self.search_space.sample_random_architecture()
                candidate_pool.append(arch)
            
            # Evaluate each candidate with the acquisition function
            acquisition_values = []
            
            for arch in candidate_pool:
                value = self._evaluate_acquisition(arch)
                acquisition_values.append(value)
            
            # Select the architecture with the highest acquisition value
            best_idx = np.argmax(acquisition_values)
            architectures.append(candidate_pool[best_idx])
        
        return architectures
    
    def update(self, architectures: List[NeuralArchitecture], evaluations: List[Dict[str, float]]):
        """Update the search strategy based on evaluation results."""
        for arch, eval_result in zip(architectures, evaluations):
            # Update history
            self.history.append((arch, eval_result))
            
            # Update best architecture
            if self.best_evaluation is None or eval_result['score'] > self.best_evaluation['score']:
                self.best_architecture = arch
                self.best_evaluation = eval_result
            
            # Add to population with evaluation
            self.architectures.append(arch)
            self.evaluations.append(eval_result)
        
        self.iteration += 1
    
    def get_best_architecture(self) -> Tuple[NeuralArchitecture, Dict[str, float]]:
        """Get the best architecture found so far."""
        if self.best_architecture is None:
            raise ValueError("No architectures evaluated yet")
        
        return self.best_architecture, self.best_evaluation
    
    def is_finished(self) -> bool:
        """Check if the search is finished."""
        return self.iteration >= self.max_iterations
    
    def _update_surrogate_model(self):
        """Update the surrogate model with the current data."""
        # Convert architectures to feature vectors
        X = np.array([self._get_features(arch) for arch in self.architectures])
        y = np.array([e['score'] for e in self.evaluations])
        
        # Fit the model
        self.surrogate_model.fit(X, y)
        self.X = X
        self.y = y
    
    def _get_features(self, architecture: NeuralArchitecture) -> np.ndarray:
        """
        Extract features from an architecture.
        This is a simplified feature extraction. A complete implementation would
        use a more sophisticated representation.
        """
        # Extract basic features
        num_nodes = len(architecture.nodes)
        num_edges = len(architecture.edges)
        num_input_nodes = len(architecture.input_nodes)
        num_output_nodes = len(architecture.output_nodes)
        
        # Count node types
        node_types = Counter(node.operation.op_type for node in architecture.nodes.values())
        
        # Count edge types
        edge_types = Counter(edge.connection_type for edge in architecture.edges)
        
        # Global parameters
        learning_rate = architecture.global_params.get('learning_rate', 0.001)
        weight_decay = architecture.global_params.get('weight_decay', 1e-4)
        batch_size = architecture.global_params.get('batch_size', 64)
        
        # Graph features
        adj_matrix, _ = architecture.to_adjacency_matrix()
        avg_degree = np.mean(np.sum(adj_matrix, axis=1))
        max_degree = np.max(np.sum(adj_matrix, axis=1))
        density = np.sum(adj_matrix > 0) / (num_nodes * num_nodes) if num_nodes > 0 else 0
        
        # Create feature vector
        features = [
            num_nodes,
            num_edges,
            num_input_nodes,
            num_output_nodes,
            avg_degree,
            max_degree,
            density,
            learning_rate,
            weight_decay,
            batch_size
        ]
        
        # Add node type counts
        for op_type in OperationType:
            features.append(node_types.get(op_type, 0))
        
        # Add edge type counts
        for conn_type in ConnectionType:
            features.append(edge_types.get(conn_type, 0))
        
        return np.array(features)
    
    def _evaluate_acquisition(self, architecture: NeuralArchitecture) -> float:
        """Evaluate the acquisition function for an architecture."""
        # Extract features
        X = np.array([self._get_features(architecture)])
        
        # Predict mean and std with the surrogate model
        mu, sigma = self.surrogate_model.predict(X, return_std=True)
        mu = mu[0]
        sigma = sigma[0]
        
        # Get the best observed value
        best_score = max(e['score'] for e in self.evaluations) if self.evaluations else 0.0
        
        # Expected Improvement
        if self.acquisition_function == 'ei':
            if sigma == 0:
                return 0
            
            z = (mu - best_score) / sigma
            from scipy.stats import norm
            return (mu - best_score) * norm.cdf(z) + sigma * norm.pdf(z)
        
        # Upper Confidence Bound
        elif self.acquisition_function == 'ucb':
            return mu + self.exploration_weight * sigma
        
        # Probability of Improvement
        elif self.acquisition_function == 'pi':
            if sigma == 0:
                return 0
            
            z = (mu - best_score) / sigma
            from scipy.stats import norm
            return norm.cdf(z)
        
        # Default to UCB
        else:
            return mu + self.exploration_weight * sigma


# ------------------------------------------------------------------------------
# Neural Architecture Search
# ------------------------------------------------------------------------------

class NeuralArchitectureSearch:
    """Neural Architecture Search (NAS) component for finding optimal architectures."""
    
    def __init__(self, 
                 search_strategy: SearchStrategy = None,
                 evaluator: ModelEvaluator = None,
                 search_space: SearchSpace = None,
                 batch_size: int = 10,
                 num_workers: int = 1,
                 save_dir: str = "./nas_results"):
        """
        Initialize the Neural Architecture Search component.
        
        Args:
            search_strategy: Strategy for searching architectures
            evaluator: Evaluator for architectures
            search_space: Search space for architectures
            batch_size: Batch size for evaluations
            num_workers: Number of workers for parallel evaluation
            save_dir: Directory to save results
        """
        self.search_strategy = search_strategy or RandomSearch()
        self.evaluator = evaluator or ModelEvaluator()
        self.search_space = search_space
        self.batch_size = batch_size
        self.num_workers = num_workers
        self.save_dir = save_dir
        
        # Create save directory if it doesn't exist
        os.makedirs(save_dir, exist_ok=True)
        
        # Search results
        self.best_architecture = None
        self.best_evaluation = None
        self.search_history = []
    
    def search(self, input_shape: Any = None, output_shape: Any = None, task_data: Any = None) -> Tuple[NeuralArchitecture, Dict[str, float]]:
        """
        Run neural architecture search.
        
        Args:
            input_shape: Shape of the input data
            output_shape: Shape of the output data
            task_data: Data for the task to evaluate on
            
        Returns:
            Tuple[NeuralArchitecture, Dict[str, float]]: The best architecture and its evaluation
        """
        # If search space is not provided, create a default one
        if self.search_space is None:
            if input_shape is None or output_shape is None:
                raise ValueError("Input shape and output shape must be provided if search space is not")
            
            logger.info(f"Creating default search space for input shape {input_shape} and output shape {output_shape}")
            self.search_space = SearchSpace.create_default_search_space(input_shape, output_shape)
        
        # If task data is not provided but evaluator has it, use that
        if task_data is not None:
            self.evaluator.task_data = task_data
        
        # Initialize the search strategy
        logger.info("Initializing search strategy")
        self.search_strategy.initialize(self.search_space, self.evaluator)
        
        # Run the search
        logger.info("Starting neural architecture search")
        start_time = time.time()
        
        while not self.search_strategy.is_finished():
            iteration_start = time.time()
            
            # Generate the next batch of architectures
            architectures = self.search_strategy.next_batch(self.batch_size)
            
            # Evaluate architectures
            if self.num_workers > 1:
                evaluations = self._parallel_evaluate(architectures)
            else:
                evaluations = [self.evaluator.evaluate(arch) for arch in architectures]
            
            # Update the search strategy
            self.search_strategy.update(architectures, evaluations)
            
            # Update the best architecture
            for arch, eval_result in zip(architectures, evaluations):
                if self.best_evaluation is None or eval_result['score'] > self.best_evaluation['score']:
                    self.best_architecture = arch
                    self.best_evaluation = eval_result
                    
                    # Save the best architecture
                    self._save_architecture(arch, eval_result, "best")
            
            # Record search history
            self.search_history.append({
                'iteration': len(self.search_history),
                'time': time.time() - start_time,
                'architectures': len(architectures),
                'best_score': self.best_evaluation['score'] if self.best_evaluation else None,
                'mean_score': np.mean([e['score'] for e in evaluations]),
                'max_score': max([e['score'] for e in evaluations])
            })
            
            # Log progress
            iteration_time = time.time() - iteration_start
            logger.info(f"Iteration {len(self.search_history)}: Best score = {self.best_evaluation['score']:.4f}, "
                        f"Mean score = {np.mean([e['score'] for e in evaluations]):.4f}, "
                        f"Time = {iteration_time:.2f}s")
        
        # Get the final best architecture
        self.best_architecture, self.best_evaluation = self.search_strategy.get_best_architecture()
        
        # Save final results
        self._save_search_results()
        
        logger.info(f"Neural architecture search completed. Best score: {self.best_evaluation['score']:.4f}")
        logger.info(f"Total time: {time.time() - start_time:.2f}s")
        
        return self.best_architecture, self.best_evaluation
    
    def _parallel_evaluate(self, architectures: List[NeuralArchitecture]) -> List[Dict[str, float]]:
        """Evaluate architectures in parallel."""
        with concurrent.futures.ProcessPoolExecutor(max_workers=self.num_workers) as executor:
            evaluations = list(executor.map(self.evaluator.evaluate, architectures))
        
        return evaluations
    
    def _save_architecture(self, architecture: NeuralArchitecture, evaluation: Dict[str, float], suffix: str = ""):
        """Save an architecture to disk."""
        timestamp = int(time.time())
        
        # Create a unique filename
        if suffix:
            filename = f"architecture_{suffix}_{timestamp}.json"
        else:
            filename = f"architecture_{timestamp}.json"
        
        filepath = os.path.join(self.save_dir, filename)
        
        # Save the architecture and evaluation
        with open(filepath, 'w') as f:
            json.dump({
                'architecture': architecture.to_dict(),
                'evaluation': evaluation
            }, f, indent=2)
    
    def _save_search_results(self):
        """Save search results to disk."""
        results = {
            'best_architecture': self.best_architecture.to_dict() if self.best_architecture else None,
            'best_evaluation': self.best_evaluation,
            'search_history': self.search_history,
            'timestamp': time.time()
        }
        
        filepath = os.path.join(self.save_dir, "search_results.json")
        
        with open(filepath, 'w') as f:
            json.dump(results, f, indent=2)
    
    @classmethod
    def load_architecture(cls, filepath: str) -> Tuple[NeuralArchitecture, Dict[str, float]]:
        """
        Load an architecture from disk.
        
        Args:
            filepath: Path to the architecture file
            
        Returns:
            Tuple[NeuralArchitecture, Dict[str, float]]: The architecture and its evaluation
        """
        with open(filepath, 'r') as f:
            data = json.load(f)
            
        architecture = NeuralArchitecture.from_dict(data['architecture'])
        evaluation = data['evaluation']
        
        return architecture, evaluation
    
    def visualize_results(self, save_path: str = None) -> None:
        """
        Visualize search results.
        
        Args:
            save_path: Path to save the visualization
        """
        try:
            import matplotlib.pyplot as plt
            
            # Plot search history
            fig, axs = plt.subplots(2, 1, figsize=(10, 12))
            
            # Plot scores
            iterations = [h['iteration'] for h in self.search_history]
            best_scores = [h['best_score'] for h in self.search_history]
            mean_scores = [h['mean_score'] for h in self.search_history]
            max_scores = [h['max_score'] for h in self.search_history]
            
            axs[0].plot(iterations, best_scores, 'r-', label='Best Score Overall')
            axs[0].plot(iterations, mean_scores, 'b-', label='Mean Score')
            axs[0].plot(iterations, max_scores, 'g-', label='Max Score per Iteration')
            axs[0].set_xlabel('Iteration')
            axs[0].set_ylabel('Score')
            axs[0].set_title('Neural Architecture Search Progress')
            axs[0].legend()
            axs[0].grid(True)
            
            # Plot time
            times = [h['time'] for h in self.search_history]
            
            axs[1].plot(iterations, times, 'k-')
            axs[1].set_xlabel('Iteration')
            axs[1].set_ylabel('Time (s)')
            axs[1].set_title('Cumulative Search Time')
            axs[1].grid(True)
            
            plt.tight_layout()
            
            if save_path:
                plt.savefig(save_path)
            else:
                plt.show()
                
        except ImportError:
            logger.warning("matplotlib is required for visualization")
    
    def visualize_architecture(self, architecture: NeuralArchitecture = None, save_path: str = None) -> None:
        """
        Visualize an architecture.
        
        Args:
            architecture: Architecture to visualize (defaults to best architecture)
            save_path: Path to save the visualization
        """
        if architecture is None:
            architecture = self.best_architecture
            
        if architecture is None:
            logger.warning("No architecture to visualize")
            return
            
        try:
            import matplotlib.pyplot as plt
            
            # Create a NetworkX graph from the architecture
            G = architecture.to_networkx()
            
            # Get node type information for coloring
            node_colors = []
            for node_id in G.nodes():
                if node_id in architecture.input_nodes:
                    node_colors.append('lightblue')
                elif node_id in architecture.output_nodes:
                    node_colors.append('lightgreen')
                else:
                    # Color based on operation type
                    op_type = architecture.nodes[node_id].operation.op_type
                    if 'CONV' in op_type.name:
                        node_colors.append('orange')
                    elif 'LINEAR' in op_type.name:
                        node_colors.append('yellow')
                    elif 'ATTENTION' in op_type.name or 'TRANSFORMER' in op_type.name:
                        node_colors.append('purple')
                    elif 'ACTIVATION' in op_type.name:
                        node_colors.append('red')
                    elif 'POOL' in op_type.name:
                        node_colors.append('cyan')
                    else:
                        node_colors.append('lightgray')
            
            # Get edge type information for coloring
            edge_colors = []
            for u, v in G.edges():
                # Get the edge type from the architecture
                edge_type = None
                for edge in architecture.edges:
                    if edge.source_id == u and edge.target_id == v:
                        edge_type = edge.connection_type
                        break
                
                if edge_type == ConnectionType.FORWARD:
                    edge_colors.append('black')
                elif edge_type == ConnectionType.SKIP:
                    edge_colors.append('red')
                elif edge_type == ConnectionType.RESIDUAL:
                    edge_colors.append('blue')
                elif edge_type == ConnectionType.ATTENTION:
                    edge_colors.append('purple')
                else:
                    edge_colors.append('gray')
            
            # Set up the plot
            plt.figure(figsize=(12, 8))
            
            # Use hierarchical layout
            pos = nx.nx_agraph.graphviz_layout(G, prog='dot', args='-Grankdir=LR')
            
            # Draw the graph
            nx.draw_networkx_nodes(G, pos, node_color=node_colors, node_size=500)
            nx.draw_networkx_edges(G, pos, edge_color=edge_colors, width=1.5, arrows=True)
            nx.draw_networkx_labels(G, pos, font_size=8)
            
            plt.title(f"Neural Architecture: {architecture.metadata.get('template_name', 'Custom')}")
            plt.axis('off')
            
            if save_path:
                plt.savefig(save_path)
            else:
                plt.show()
                
        except ImportError:
            logger.warning("matplotlib and pygraphviz are required for visualization")


# ------------------------------------------------------------------------------
# Main functions
# ------------------------------------------------------------------------------

def get_default_search_strategy(strategy_name: str = 'evolutionary', **kwargs) -> SearchStrategy:
    """
    Get a default search strategy.
    
    Args:
        strategy_name: Name of the strategy ('random', 'evolutionary', 'bayesian')
        **kwargs: Additional arguments for the strategy
        
    Returns:
        SearchStrategy: A search strategy
    """
    if strategy_name == 'random':
        return RandomSearch(**kwargs)
    elif strategy_name == 'evolutionary':
        return EvolutionarySearch(**kwargs)
    elif strategy_name == 'bayesian':
        return BayesianOptimizationSearch(**kwargs)
    else:
        logger.warning(f"Unknown search strategy: {strategy_name}. Using random search.")
        return RandomSearch(**kwargs)


def find_optimal_architecture(task_name: str, 
                             input_shape: Any, 
                             output_shape: Any, 
                             task_data: Any = None,
                             strategy_name: str = 'evolutionary',
                             max_iterations: int = 50,
                             batch_size: int = 10,
                             num_workers: int = 1,
                             save_dir: str = None,
                             **kwargs) -> Tuple[NeuralArchitecture, Dict[str, float]]:
    """
    Find an optimal neural architecture for a task.
    
    Args:
        task_name: Name of the task
        input_shape: Shape of the input data
        output_shape: Shape of the output data
        task_data: Data for the task
        strategy_name: Name of the search strategy
        max_iterations: Maximum number of iterations
        batch_size: Batch size for evaluations
        num_workers: Number of workers for parallel evaluation
        save_dir: Directory to save results
        **kwargs: Additional arguments for the search
        
    Returns:
        Tuple[NeuralArchitecture, Dict[str, float]]: The best architecture and its evaluation
    """
    # Set up save directory
    if save_dir is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        save_dir = f"./nas_results/{task_name}_{timestamp}"
    
    os.makedirs(save_dir, exist_ok=True)
    
    # Create evaluator
    evaluator = ModelEvaluator(task_data=task_data)
    
    # Create search space
    search_space = SearchSpace.create_default_search_space(input_shape, output_shape)
    
    # Create search strategy
    strategy_kwargs = {k: v for k, v in kwargs.items() if k != 'strategy_name'}
    strategy_kwargs['max_iterations'] = max_iterations
    search_strategy = get_default_search_strategy(strategy_name, **strategy_kwargs)
    
    # Create NAS
    nas = NeuralArchitectureSearch(
        search_strategy=search_strategy,
        evaluator=evaluator,
        search_space=search_space,
        batch_size=batch_size,
        num_workers=num_workers,
        save_dir=save_dir
    )
    
    # Run search
    best_architecture, best_evaluation = nas.search()
    
    # Visualize results
    nas.visualize_results(save_path=os.path.join(save_dir, "search_results.png"))
    nas.visualize_architecture(save_path=os.path.join(save_dir, "best_architecture.png"))
    
    return best_architecture, best_evaluation

# ------------------------------------------------------------------------------
# Utility Functions
# ------------------------------------------------------------------------------

def save_architecture(architecture: NeuralArchitecture, filename: str):
    """
    Save an architecture to a file.
    
    Args:
        architecture: The architecture to save
        filename: The file to save to
    """
    os.makedirs(os.path.dirname(os.path.abspath(filename)), exist_ok=True)
    
    with open(filename, 'w') as f:
        json.dump(architecture.to_dict(), f, indent=2)


def load_architecture(filename: str) -> NeuralArchitecture:
    """
    Load an architecture from a file.
    
    Args:
        filename: The file to load from
        
    Returns:
        NeuralArchitecture: The loaded architecture
    """
    with open(filename, 'r') as f:
        data = json.load(f)
    
    return NeuralArchitecture.from_dict(data)


def visualize_architecture(architecture: NeuralArchitecture, figsize=(12, 8)):
    """
    Visualize an architecture.
    
    Args:
        architecture: The architecture to visualize
        figsize: Figure size
        
    Returns:
        Matplotlib figure if matplotlib is available
    """
    try:
        import matplotlib.pyplot as plt
        import matplotlib.patches as mpatches
        import networkx as nx
        
        # Convert to NetworkX graph
        G = architecture.to_networkx()
        
        # Get layout
        pos = nx.nx_agraph.graphviz_layout(G, prog='dot') if nx.nx_agraph is not None else nx.shell_layout(G)
        
        # Create figure
        fig, ax = plt.subplots(figsize=figsize)
        
        # Define colors for node types
        node_colors = {
            'input': 'lightblue',
            'output': 'lightgreen',
            'conv': 'salmon',
            'linear': 'gold',
            'attention': 'purple',
            'transformer': 'magenta',
            'activation': 'tan',
            'norm': 'lightgray',
            'pooling': 'pink',
            'dropout': 'white'
        }
        
        # Node colors
        node_color_map = []
        for node in G.nodes():
            if node in architecture.input_nodes:
                color = node_colors['input']
            elif node in architecture.output_nodes:
                color = node_colors['output']
            else:
                # Determine color based on operation type
                op_type = G.nodes[node]['operation']
                
                if 'CONV' in op_type:
                    color = node_colors['conv']
                elif 'LINEAR' in op_type:
                    color = node_colors['linear']
                elif 'ATTENTION' in op_type:
                    color = node_colors['attention']
                elif 'TRANSFORMER' in op_type:
                    color = node_colors['transformer']
                elif 'ACTIVATION' in op_type:
                    color = node_colors['activation']
                elif 'NORM' in op_type:
                    color = node_colors['norm']
                elif 'POOLING' in op_type:
                    color = node_colors['pooling']
                elif 'DROPOUT' in op_type:
                    color = node_colors['dropout']
                else:
                    color = 'white'
            
            node_color_map.append(color)
        
        # Edge colors
        edge_color_map = []
        for u, v in G.edges():
            conn_type = G.edges[u, v]['connection_type']
            
            if conn_type == 'FORWARD':
                color = 'black'
            elif conn_type == 'SKIP':
                color = 'blue'
            elif conn_type == 'RESIDUAL':
                color = 'red'
            else:
                color = 'gray'
            
            edge_color_map.append(color)
        
        # Draw nodes
        nx.draw_networkx_nodes(G, pos, ax=ax, node_size=500, node_color=node_color_map, alpha=0.8)
        
        # Draw edges
        nx.draw_networkx_edges(G, pos, ax=ax, width=1.5, edge_color=edge_color_map, alpha=0.7,
                              arrowstyle='->', arrowsize=15)
        
        # Draw labels
        labels = {}
        for node in G.nodes():
            if node in architecture.input_nodes:
                labels[node] = f"Input: {node}"
            elif node in architecture.output_nodes:
                labels[node] = f"Output: {node}"
            else:
                op_type = G.nodes[node]['operation']
                labels[node] = f"{node}\n({op_type})"
        
        nx.draw_networkx_labels(G, pos, labels, ax=ax, font_size=8)
        
        # Add legend
        legend_patches = []
        for name, color in node_colors.items():
            patch = mpatches.Patch(color=color, label=name.capitalize())
            legend_patches.append(patch)
        
        plt.legend(handles=legend_patches, loc='upper right')
        
        # Set title
        meta_str = ""
        if architecture.metadata:
            meta_items = []
            if 'template_name' in architecture.metadata:
                meta_items.append(f"Template: {architecture.metadata['template_name']}")
            if 'num_nodes' in architecture.metadata:
                meta_items.append(f"Nodes: {architecture.metadata['num_nodes']}")
            if 'num_edges' in architecture.metadata:
                meta_items.append(f"Edges: {architecture.metadata['num_edges']}")
            
            meta_str = " (" + ", ".join(meta_items) + ")"
            
        plt.title(f"Neural Architecture{meta_str}")
        
        # Adjust layout and margins
        plt.tight_layout()
        plt.axis('off')
        
        return fig
    
    except ImportError:
        logger.warning("matplotlib and/or networkx is required for visualization")
        return None


