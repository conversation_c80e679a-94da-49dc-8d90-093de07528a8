"""
ULTRA API: Interface for the Ultimate Learning & Thought Reasoning Architecture
------------------------------------------------------------------------------

This module provides a RESTful API and programmatic interfaces for interacting with
the ULTRA system. It enables external applications to leverage ULTRA's capabilities
through standardized interfaces, including:

- RESTful HTTP endpoints for web and service integration
- WebSocket connections for real-time interaction
- Python client library for direct integration
- Authentication and access control mechanisms
- Input/output validation and serialization
- Rate limiting and resource management
- Asynchronous task processing

The API exposes ULTRA's core functionalities:
- Text processing and generation
- Image and multimodal analysis
- Reasoning and problem-solving
- Knowledge representation and retrieval
- Learning and adaptation
- Self-improvement and evolution

This implementation follows REST principles, OpenAPI specifications, and
industry best practices for API design and security.
"""

import os
import sys
import time
import uuid
import json
import base64
import hashlib
import logging
import asyncio
import datetime
import threading
from enum import Enum
from typing import Dict, List, Any, Optional, Union, Callable, Tuple, Type
from pathlib import Path
from functools import wraps
from dataclasses import dataclass, field, asdict

# Third-party imports
import numpy as np
import torch
import fastapi
from fastapi import FastAPI, Request, Response, Depends, HTTPException, BackgroundTasks, File, UploadFile, Form, Body
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials, OAuth2PasswordBearer
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, StreamingResponse
from fastapi.staticfiles import StaticFiles
from fastapi.openapi.utils import get_openapi
from fastapi.encoders import jsonable_encoder
import pydantic
from pydantic import BaseModel, Field, validator, root_validator
import jwt
from jwt.exceptions import InvalidTokenError
import uvicorn
import redis
from redis.exceptions import RedisError
import aiofiles
import aiohttp
import websockets
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type

# Configure logger
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("ultra.api")

# Import ULTRA components
try:
    from ultra.ultra import ULTRASystem, get_system
    from ultra.config import get_config, ConfigManager
except ImportError as e:
    logger.error(f"Failed to import ULTRA components: {e}")
    raise ImportError(f"ULTRA core modules not available: {e}")

# API Version
API_VERSION = "v1"
DEFAULT_TIMEOUT = 60  # seconds

# Define models for request/response
class ModelFormat(str, Enum):
    """Supported serialization formats for models."""
    JSON = "json"
    BINARY = "binary"
    ONNX = "onnx"
    PYTORCH = "pytorch"
    TENSORFLOW = "tensorflow"

class TaskStatus(str, Enum):
    """Status values for asynchronous tasks."""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELED = "canceled"

class InputType(str, Enum):
    """Supported input types for processing."""
    TEXT = "text"
    IMAGE = "image"
    AUDIO = "audio"
    VIDEO = "video"
    MULTIMODAL = "multimodal"
    JSON = "json"

class ProcessingMode(str, Enum):
    """Processing modes for ULTRA system."""
    REASONING = "reasoning"  # Meta-cognitive reasoning
    DIFFUSION = "diffusion"  # Diffusion-based reasoning
    TRANSFORMER = "transformer"  # Transformer-based processing
    NEURAL = "neural"  # Core neural processing
    SYMBOLIC = "symbolic"  # Neuro-symbolic processing
    HYBRID = "hybrid"  # Combined processing approach
    AUTO = "auto"  # Automatically select best approach

class ProcessingRequest(BaseModel):
    """Base model for processing requests."""
    input_type: InputType
    mode: ProcessingMode = ProcessingMode.AUTO
    content: Any
    parameters: Dict[str, Any] = Field(default_factory=dict)
    timeout: Optional[int] = None
    
    @validator('content')
    def validate_content(cls, v, values):
        input_type = values.get('input_type')
        if input_type == InputType.TEXT and not isinstance(v, str):
            raise ValueError("Content must be a string for text input type")
        elif input_type == InputType.JSON and not isinstance(v, (dict, list)):
            raise ValueError("Content must be a dict or list for JSON input type")
        return v

class ProcessingResponse(BaseModel):
    """Base model for processing responses."""
    request_id: str
    timestamp: datetime.datetime
    status: str
    result: Optional[Any] = None
    error: Optional[str] = None
    metrics: Dict[str, Any] = Field(default_factory=dict)

class AsyncTaskResponse(BaseModel):
    """Response model for asynchronous task creation."""
    task_id: str
    status: TaskStatus
    estimated_time: Optional[float] = None
    status_url: str

class TaskStatusResponse(BaseModel):
    """Response model for task status checking."""
    task_id: str
    status: TaskStatus
    progress: Optional[float] = None
    result: Optional[Any] = None
    error: Optional[str] = None
    created_at: datetime.datetime
    updated_at: datetime.datetime

class ReasoningRequest(ProcessingRequest):
    """Request model for reasoning tasks."""
    input_type: InputType = InputType.TEXT
    mode: ProcessingMode = ProcessingMode.REASONING
    reasoning_type: Optional[str] = None
    context: Optional[List[str]] = None
    max_depth: Optional[int] = None
    
    @validator('content')
    def validate_content(cls, v, values):
        if not isinstance(v, str):
            raise ValueError("Content must be a string for reasoning requests")
        if len(v) < 3:
            raise ValueError("Content must be at least 3 characters long")
        return v

class EvolutionRequest(BaseModel):
    """Request model for triggering system evolution."""
    target_components: List[str] = Field(default_factory=list)
    fitness_metrics: Dict[str, float] = Field(default_factory=dict)
    constraints: Dict[str, Any] = Field(default_factory=dict)
    timeout: Optional[int] = None

class EvolutionResponse(BaseModel):
    """Response model for evolution results."""
    request_id: str
    timestamp: datetime.datetime
    success: bool
    changes: List[Dict[str, Any]] = Field(default_factory=list)
    metrics_before: Dict[str, float] = Field(default_factory=dict)
    metrics_after: Dict[str, float] = Field(default_factory=dict)
    error: Optional[str] = None

class SystemInfoResponse(BaseModel):
    """Response model for system information."""
    version: str
    api_version: str
    subsystems: List[str]
    capabilities: Dict[str, bool]
    status: str
    uptime: float
    hardware_info: Dict[str, Any]

class ModelInfoResponse(BaseModel):
    """Response model for model information."""
    model_id: str
    description: str
    version: str
    parameters: int
    architecture: str
    capabilities: List[str]
    formats: List[ModelFormat]

# Authentication and security
security = HTTPBearer()
oauth2_scheme = OAuth2PasswordBearer(tokenUrl=f"/{API_VERSION}/auth/token")

# Secret key for JWT tokens - in production, this should be properly secured
# and loaded from environment variables or a secure key management system
JWT_SECRET_KEY = os.environ.get("ULTRA_JWT_SECRET", "")
if not JWT_SECRET_KEY:
    # Generate a secret key if not provided
    logger.warning("No JWT secret key provided, generating a random one")
    JWT_SECRET_KEY = base64.b64encode(os.urandom(32)).decode('utf-8')

JWT_ALGORITHM = "HS256"
TOKEN_EXPIRE_MINUTES = 30

# Use Redis for rate limiting and task storage if available
try:
    redis_client = redis.Redis.from_url(
        os.environ.get("ULTRA_REDIS_URL", "redis://localhost:6379/0"),
        decode_responses=True
    )
    redis_available = True
    logger.info("Redis connected successfully")
except (redis.ConnectionError, redis.exceptions.ConnectionError) as e:
    redis_client = None
    redis_available = False
    logger.warning(f"Redis connection failed: {e}. Using in-memory storage.")

# In-memory storage for when Redis is not available
in_memory_tasks = {}
in_memory_rate_limits = {}

# API application
app = FastAPI(
    title="ULTRA API",
    description="API for the Ultimate Learning & Thought Reasoning Architecture",
    version=API_VERSION,
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=os.environ.get("ULTRA_CORS_ORIGINS", "*").split(","),
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# ULTRA system instance
system = get_system()

#---------------------------------------------------------------------------
# Authentication and authorization functions
#---------------------------------------------------------------------------

class User(BaseModel):
    username: str
    email: Optional[str] = None
    full_name: Optional[str] = None
    disabled: Optional[bool] = None
    permissions: List[str] = Field(default_factory=list)

# In production, this would be replaced with a proper user database
USERS_DB = {
    "admin": User(
        username="admin",
        email="<EMAIL>",
        full_name="Admin User",
        disabled=False,
        permissions=["admin", "read", "write", "execute"]
    ),
    "test": User(
        username="test",
        email="<EMAIL>",
        full_name="Test User",
        disabled=False,
        permissions=["read", "write"]
    )
}

class Token(BaseModel):
    access_token: str
    token_type: str
    expires_at: int

class TokenData(BaseModel):
    username: Optional[str] = None
    permissions: List[str] = Field(default_factory=list)
    exp: Optional[int] = None

def create_access_token(data: dict, expires_delta: Optional[datetime.timedelta] = None):
    """Create a JWT access token."""
    to_encode = data.copy()
    
    if expires_delta:
        expire = datetime.datetime.utcnow() + expires_delta
    else:
        expire = datetime.datetime.utcnow() + datetime.timedelta(minutes=TOKEN_EXPIRE_MINUTES)
        
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, JWT_SECRET_KEY, algorithm=JWT_ALGORITHM)
    return encoded_jwt

def verify_token(token: str) -> Optional[TokenData]:
    """Verify a JWT token and return the token data."""
    try:
        payload = jwt.decode(token, JWT_SECRET_KEY, algorithms=[JWT_ALGORITHM])
        username = payload.get("sub")
        permissions = payload.get("permissions", [])
        exp = payload.get("exp")
        
        if username is None:
            return None
            
        return TokenData(username=username, permissions=permissions, exp=exp)
    except InvalidTokenError:
        return None

async def verify_user_token(credentials: HTTPAuthorizationCredentials = Depends(security)) -> TokenData:
    """Verify user token from HTTP Authorization header."""
    token = credentials.credentials
    token_data = verify_token(token)
    
    if token_data is None:
        raise HTTPException(
            status_code=401,
            detail="Invalid authentication token",
            headers={"WWW-Authenticate": "Bearer"},
        )
        
    # Check if token is expired
    if token_data.exp and token_data.exp < datetime.datetime.utcnow().timestamp():
        raise HTTPException(
            status_code=401,
            detail="Token has expired",
            headers={"WWW-Authenticate": "Bearer"},
        )
        
    return token_data

async def get_current_user(token_data: TokenData = Depends(verify_user_token)) -> User:
    """Get current user from token data."""
    user = USERS_DB.get(token_data.username)
    
    if user is None:
        raise HTTPException(
            status_code=401,
            detail="User not found",
            headers={"WWW-Authenticate": "Bearer"},
        )
        
    if user.disabled:
        raise HTTPException(
            status_code=403,
            detail="User account is disabled",
            headers={"WWW-Authenticate": "Bearer"},
        )
        
    return user

def check_permission(required_permission: str):
    """Decorator to check if user has required permission."""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, token_data: TokenData = Depends(verify_user_token), **kwargs):
            if required_permission not in token_data.permissions and "admin" not in token_data.permissions:
                raise HTTPException(
                    status_code=403,
                    detail=f"Permission denied: {required_permission} required",
                )
            return await func(*args, **kwargs)
        return wrapper
    return decorator

#---------------------------------------------------------------------------
# Rate limiting functions
#---------------------------------------------------------------------------

def get_rate_limit_key(request: Request, limit_type: str) -> str:
    """Generate a key for rate limiting."""
    client_ip = request.client.host
    # In a real implementation, you would want to use the authenticated user if available
    return f"ratelimit:{limit_type}:{client_ip}"

async def check_rate_limit(request: Request, limit_type: str, max_requests: int, window_seconds: int) -> bool:
    """Check if request is within rate limits."""
    key = get_rate_limit_key(request, limit_type)
    current_time = int(time.time())
    window_key = f"{key}:{current_time // window_seconds}"
    
    if redis_available:
        try:
            current = redis_client.incr(window_key)
            if current == 1:
                # Set expiration for new keys
                redis_client.expire(window_key, window_seconds)
            return current <= max_requests
        except RedisError as e:
            logger.error(f"Redis error in rate limiting: {e}")
            # Fall back to in-memory
            return check_in_memory_rate_limit(window_key, max_requests, window_seconds)
    else:
        return check_in_memory_rate_limit(window_key, max_requests, window_seconds)

def check_in_memory_rate_limit(key: str, max_requests: int, window_seconds: int) -> bool:
    """In-memory fallback for rate limiting."""
    current_time = int(time.time())
    
    # Clean up expired entries
    expired_keys = [k for k, (count, expire_time) in in_memory_rate_limits.items() if expire_time < current_time]
    for k in expired_keys:
        in_memory_rate_limits.pop(k, None)
    
    # Check or initialize current key
    if key not in in_memory_rate_limits:
        in_memory_rate_limits[key] = (1, current_time + window_seconds)
        return True
        
    count, expire_time = in_memory_rate_limits[key]
    if count >= max_requests:
        return False
        
    in_memory_rate_limits[key] = (count + 1, expire_time)
    return True

#---------------------------------------------------------------------------
# Task management functions
#---------------------------------------------------------------------------

async def store_task(task_id: str, status: TaskStatus, metadata: dict) -> bool:
    """Store task information in Redis or in-memory."""
    task_data = {
        "task_id": task_id,
        "status": status.value,
        "metadata": json.dumps(metadata),
        "created_at": datetime.datetime.utcnow().isoformat(),
        "updated_at": datetime.datetime.utcnow().isoformat(),
    }
    
    if redis_available:
        try:
            task_key = f"task:{task_id}"
            redis_client.hmset(task_key, task_data)
            redis_client.expire(task_key, 86400)  # Expire after 24 hours
            return True
        except RedisError as e:
            logger.error(f"Redis error storing task: {e}")
            # Fall back to in-memory
            in_memory_tasks[task_id] = task_data
            return True
    else:
        in_memory_tasks[task_id] = task_data
        return True

async def update_task_status(task_id: str, status: TaskStatus, result: Optional[Any] = None, error: Optional[str] = None) -> bool:
    """Update task status in Redis or in-memory."""
    if redis_available:
        try:
            task_key = f"task:{task_id}"
            
            # Check if task exists
            if not redis_client.exists(task_key):
                return False
                
            update_data = {
                "status": status.value,
                "updated_at": datetime.datetime.utcnow().isoformat(),
            }
            
            if result is not None:
                update_data["result"] = json.dumps(result)
                
            if error is not None:
                update_data["error"] = error
                
            redis_client.hmset(task_key, update_data)
            return True
        except RedisError as e:
            logger.error(f"Redis error updating task: {e}")
            # Fall back to in-memory
            if task_id in in_memory_tasks:
                in_memory_tasks[task_id].update({
                    "status": status.value,
                    "updated_at": datetime.datetime.utcnow().isoformat(),
                })
                
                if result is not None:
                    in_memory_tasks[task_id]["result"] = json.dumps(result)
                    
                if error is not None:
                    in_memory_tasks[task_id]["error"] = error
                    
                return True
            return False
    else:
        if task_id in in_memory_tasks:
            in_memory_tasks[task_id].update({
                "status": status.value,
                "updated_at": datetime.datetime.utcnow().isoformat(),
            })
            
            if result is not None:
                in_memory_tasks[task_id]["result"] = json.dumps(result)
                
            if error is not None:
                in_memory_tasks[task_id]["error"] = error
                
            return True
        return False

async def get_task_status(task_id: str) -> Optional[dict]:
    """Get task status from Redis or in-memory."""
    if redis_available:
        try:
            task_key = f"task:{task_id}"
            
            # Check if task exists
            if not redis_client.exists(task_key):
                return None
                
            task_data = redis_client.hgetall(task_key)
            
            # Parse JSON fields
            if "metadata" in task_data:
                task_data["metadata"] = json.loads(task_data["metadata"])
                
            if "result" in task_data:
                try:
                    task_data["result"] = json.loads(task_data["result"])
                except json.JSONDecodeError:
                    # If result isn't valid JSON, keep as string
                    pass
                    
            return task_data
        except RedisError as e:
            logger.error(f"Redis error getting task: {e}")
            # Fall back to in-memory
            return in_memory_tasks.get(task_id)
    else:
        return in_memory_tasks.get(task_id)

async def run_task(task_id: str, func: Callable, *args, **kwargs):
    """Run a task and update its status."""
    try:
        await update_task_status(task_id, TaskStatus.PROCESSING)
        
        # Run the function
        if asyncio.iscoroutinefunction(func):
            result = await func(*args, **kwargs)
        else:
            result = func(*args, **kwargs)
            
        # Update task with result
        await update_task_status(task_id, TaskStatus.COMPLETED, result=result)
        return result
    except Exception as e:
        logger.error(f"Task {task_id} failed: {str(e)}", exc_info=True)
        await update_task_status(task_id, TaskStatus.FAILED, error=str(e))
        raise

#---------------------------------------------------------------------------
# Helper functions
#---------------------------------------------------------------------------

def serialize_result(result: Any) -> Any:
    """Serialize result to JSON-compatible format."""
    if isinstance(result, np.ndarray):
        return result.tolist()
    elif isinstance(result, torch.Tensor):
        return result.cpu().numpy().tolist()
    elif hasattr(result, "to_dict"):
        return result.to_dict()
    elif hasattr(result, "__dict__"):
        return {k: serialize_result(v) for k, v in result.__dict__.items() 
                if not k.startswith("_")}
    elif isinstance(result, (list, tuple)):
        return [serialize_result(x) for x in result]
    elif isinstance(result, dict):
        return {k: serialize_result(v) for k, v in result.items()}
    else:
        return result

def get_system_info() -> Dict[str, Any]:
    """Get system information."""
    # Get system configuration
    config = get_config() if "get_config" in globals() else None
    
    # Get hardware info
    import platform
    import psutil
    
    # Check for GPU
    gpu_info = {}
    if torch.cuda.is_available():
        gpu_info["cuda_available"] = True
        gpu_info["device_count"] = torch.cuda.device_count()
        gpu_info["current_device"] = torch.cuda.current_device()
        gpu_info["device_name"] = torch.cuda.get_device_name(torch.cuda.current_device())
    else:
        gpu_info["cuda_available"] = False
    
    # System info
    memory_info = psutil.virtual_memory()
    
    hardware_info = {
        "platform": platform.platform(),
        "processor": platform.processor(),
        "python_version": platform.python_version(),
        "cpu_count": psutil.cpu_count(logical=False),
        "logical_cpu_count": psutil.cpu_count(logical=True),
        "memory_total": memory_info.total,
        "memory_available": memory_info.available,
        "gpu": gpu_info
    }
    
    # Get subsystem info
    subsystems = list(system.subsystems.keys()) if hasattr(system, "subsystems") else []
    
    # Determine capabilities
    capabilities = {
        "text_processing": "hyper_transformer" in subsystems,
        "reasoning": "meta_cognitive" in subsystems,
        "diffusion_reasoning": "diffusion_reasoning" in subsystems,
        "neuromorphic": "neuromorphic_processing" in subsystems,
        "self_evolution": "self_evolution" in subsystems,
        "neuro_symbolic": "neuro_symbolic" in subsystems,
    }
    
    # System status
    status = "ready" if getattr(system, "ready", False) else "initializing"
    
    # Calculate uptime
    start_time = getattr(system, "start_time", time.time())
    uptime = time.time() - start_time
    
    return {
        "version": getattr(system, "config", {}).get("version", "1.0.0"),
        "api_version": API_VERSION,
        "subsystems": subsystems,
        "capabilities": capabilities,
        "status": status,
        "uptime": uptime,
        "hardware_info": hardware_info
    }

def process_error_handler(error: Exception) -> Tuple[int, Dict[str, Any]]:
    """Handle different types of errors and return appropriate responses."""
    if isinstance(error, ValueError):
        status_code = 400
        error_type = "ValidationError"
    elif isinstance(error, NotImplementedError):
        status_code = 501
        error_type = "NotImplementedError"
    elif isinstance(error, TimeoutError):
        status_code = 408
        error_type = "TimeoutError"
    elif isinstance(error, PermissionError):
        status_code = 403
        error_type = "PermissionError"
    else:
        status_code = 500
        error_type = "InternalServerError"
        
    return status_code, {
        "error": str(error),
        "error_type": error_type,
        "timestamp": datetime.datetime.utcnow().isoformat()
    }

#---------------------------------------------------------------------------
# API Routes: Authentication
#---------------------------------------------------------------------------

class UserCredentials(BaseModel):
    username: str
    password: str

@app.post(f"/{API_VERSION}/auth/token", response_model=Token)
async def login_for_access_token(request: Request, credentials: UserCredentials):
    """Generate an access token for authentication."""
    # Rate limit login attempts
    if not await check_rate_limit(request, "login", 10, 60):
        raise HTTPException(
            status_code=429,
            detail="Too many login attempts, please try again later"
        )
    
    # In a production system, this would validate against a secure database
    # with properly hashed passwords and security measures
    
    # For demonstration purposes, we accept fixed credentials
    # NEVER USE THIS IN PRODUCTION
    if credentials.username != "admin" and credentials.username != "test":
        raise HTTPException(
            status_code=401,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # Simple password verification - would use proper hashing in production
    if credentials.username == "admin" and credentials.password != "admin":
        raise HTTPException(
            status_code=401,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    if credentials.username == "test" and credentials.password != "test":
        raise HTTPException(
            status_code=401,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # Get user from fake DB
    user = USERS_DB.get(credentials.username)
    if not user:
        raise HTTPException(
            status_code=401,
            detail="User not found",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # Create access token with user data
    access_token_expires = datetime.timedelta(minutes=TOKEN_EXPIRE_MINUTES)
    expires_at = int((datetime.datetime.utcnow() + access_token_expires).timestamp())
    
    access_token = create_access_token(
        data={"sub": user.username, "permissions": user.permissions},
        expires_delta=access_token_expires
    )
    
    return Token(
        access_token=access_token,
        token_type="bearer",
        expires_at=expires_at
    )

@app.get(f"/{API_VERSION}/auth/me", response_model=User)
async def read_users_me(current_user: User = Depends(get_current_user)):
    """Get current user information."""
    return current_user

#---------------------------------------------------------------------------
# API Routes: System Information
#---------------------------------------------------------------------------

@app.get(f"/{API_VERSION}/system/info", response_model=SystemInfoResponse)
@check_permission("read")
async def get_system_information(request: Request):
    """Get system information."""
    if not await check_rate_limit(request, "api", 60, 60):
        raise HTTPException(
            status_code=429,
            detail="Rate limit exceeded"
        )
    
    return get_system_info()

@app.get(f"/{API_VERSION}/system/status")
@check_permission("read")
async def get_system_status(request: Request):
    """Get current system status."""
    if not await check_rate_limit(request, "api", 60, 60):
        raise HTTPException(
            status_code=429,
            detail="Rate limit exceeded"
        )
    
    status = "ready" if getattr(system, "ready", False) else "initializing"
    subsystems = {}
    
    # Get status of each subsystem
    if hasattr(system, "subsystems"):
        for name, subsystem in system.subsystems.items():
            if hasattr(subsystem, "is_ready") and callable(subsystem.is_ready):
                subsystems[name] = "ready" if subsystem.is_ready() else "initializing"
            else:
                subsystems[name] = "unknown"
    
    return {
        "status": status,
        "subsystems": subsystems,
        "version": getattr(system, "config", {}).get("version", "1.0.0"),
        "timestamp": datetime.datetime.utcnow().isoformat()
    }

@app.get(f"/{API_VERSION}/system/models", response_model=List[ModelInfoResponse])
@check_permission("read")
async def list_available_models(request: Request):
    """List available models in the system."""
    if not await check_rate_limit(request, "api", 60, 60):
        raise HTTPException(
            status_code=429,
            detail="Rate limit exceeded"
        )
    
    # This would typically query the system for available models
    # For now, return a static list based on system components
    models = []
    
    if hasattr(system, "subsystems"):
        # Check for transformer models
        if "hyper_transformer" in system.subsystems:
            models.append(
                ModelInfoResponse(
                    model_id="ultra-transformer",
                    description="ULTRA Hyper-Dimensional Transformer",
                    version="1.0.0",
                    parameters=1_000_000_000,  # 1B parameters
                    architecture="transformer",
                    capabilities=["text-generation", "text-understanding", "reasoning"],
                    formats=[ModelFormat.PYTORCH, ModelFormat.ONNX]
                )
            )
        
        # Check for diffusion models
        if "diffusion_reasoning" in system.subsystems:
            models.append(
                ModelInfoResponse(
                    model_id="ultra-diffusion",
                    description="ULTRA Diffusion-Based Reasoning Model",
                    version="1.0.0",
                    parameters=500_000_000,  # 500M parameters
                    architecture="diffusion",
                    capabilities=["reasoning", "creative-generation", "problem-solving"],
                    formats=[ModelFormat.PYTORCH]
                )
            )
        
        # Check for neuromorphic models
        if "neuromorphic_processing" in system.subsystems:
            models.append(
                ModelInfoResponse(
                    model_id="ultra-neuromorphic",
                    description="ULTRA Neuromorphic Processing Model",
                    version="1.0.0",
                    parameters=100_000_000,  # 100M parameters
                    architecture="spiking-neural-network",
                    capabilities=["pattern-recognition", "temporal-processing"],
                    formats=[ModelFormat.PYTORCH, ModelFormat.BINARY]
                )
            )
    
    return models

#---------------------------------------------------------------------------
# API Routes: Processing
#---------------------------------------------------------------------------

@app.post(f"/{API_VERSION}/process", response_model=ProcessingResponse)
@check_permission("execute")
async def process_input(request: Request, req: ProcessingRequest):
    """Process input through the ULTRA system."""
    if not await check_rate_limit(request, "processing", 10, 60):
        raise HTTPException(
            status_code=429,
            detail="Rate limit exceeded for processing requests"
        )
    
    request_id = str(uuid.uuid4())
    start_time = time.time()
    
    try:
        # Make sure system is initialized
        if not getattr(system, "initialized", False):
            system.initialize()
        
        # Process the input
        timeout = req.timeout or DEFAULT_TIMEOUT
        
        try:
            if req.input_type == InputType.TEXT:
                # Process text input
                if req.mode == ProcessingMode.REASONING:
                    result = system.reason(req.content, **req.parameters)
                else:
                    result = system.process(req.content, **req.parameters)
            elif req.input_type == InputType.JSON:
                # Process JSON input
                result = system.process(req.content, **req.parameters)
            else:
                raise NotImplementedError(f"Input type {req.input_type} not implemented")
        
            # Calculate processing time and metrics
            processing_time = time.time() - start_time
            
            # Serialize result to ensure JSON compatibility
            serialized_result = serialize_result(result)
            
            # Create response
            response = ProcessingResponse(
                request_id=request_id,
                timestamp=datetime.datetime.utcnow(),
                status="success",
                result=serialized_result,
                metrics={
                    "processing_time": processing_time,
                }
            )
            
            return response
            
        except Exception as e:
            logger.error(f"Error processing request: {e}", exc_info=True)
            status_code, error_response = process_error_handler(e)
            
            # Create error response
            response = ProcessingResponse(
                request_id=request_id,
                timestamp=datetime.datetime.utcnow(),
                status="error",
                error=str(e),
                metrics={
                    "processing_time": time.time() - start_time,
                }
            )
            
            return JSONResponse(
                status_code=status_code,
                content=jsonable_encoder(response)
            )
    
    except Exception as e:
        logger.error(f"Unexpected error processing request: {e}", exc_info=True)
        return JSONResponse(
            status_code=500,
            content={
                "request_id": request_id,
                "timestamp": datetime.datetime.utcnow().isoformat(),
                "status": "error",
                "error": f"Unexpected error: {str(e)}",
                "metrics": {
                    "processing_time": time.time() - start_time,
                }
            }
        )

@app.post(f"/{API_VERSION}/process/async", response_model=AsyncTaskResponse)
@check_permission("execute")
async def process_input_async(request: Request, req: ProcessingRequest, background_tasks: BackgroundTasks):
    """Process input asynchronously through the ULTRA system."""
    if not await check_rate_limit(request, "processing", 10, 60):
        raise HTTPException(
            status_code=429,
            detail="Rate limit exceeded for processing requests"
        )
    
    task_id = str(uuid.uuid4())
    
    # Store initial task
    task_metadata = {
        "request": req.dict(),
        "created_by": request.client.host,
    }
    
    await store_task(task_id, TaskStatus.PENDING, task_metadata)
    
    # Function to run in background
    async def process_task(task_id: str, req: ProcessingRequest):
        try:
            # Make sure system is initialized
            if not getattr(system, "initialized", False):
                system.initialize()
            
            # Process the input
            if req.input_type == InputType.TEXT:
                # Process text input
                if req.mode == ProcessingMode.REASONING:
                    result = system.reason(req.content, **req.parameters)
                else:
                    result = system.process(req.content, **req.parameters)
            elif req.input_type == InputType.JSON:
                # Process JSON input
                result = system.process(req.content, **req.parameters)
            else:
                raise NotImplementedError(f"Input type {req.input_type} not implemented")
            
            # Serialize result to ensure JSON compatibility
            serialized_result = serialize_result(result)
            
            # Update task with result
            await update_task_status(task_id, TaskStatus.COMPLETED, result=serialized_result)
            
        except Exception as e:
            logger.error(f"Error processing async task {task_id}: {e}", exc_info=True)
            await update_task_status(task_id, TaskStatus.FAILED, error=str(e))
    
    # Schedule the task
    background_tasks.add_task(process_task, task_id, req)
    
    # Estimate processing time based on input type and mode
    estimated_time = 5.0  # Default 5 seconds
    if req.input_type == InputType.TEXT and req.mode == ProcessingMode.REASONING:
        # Reasoning takes longer
        estimated_time = len(req.content.split()) * 0.1  # Rough estimate based on word count
    
    # Return task ID and status endpoint
    return AsyncTaskResponse(
        task_id=task_id,
        status=TaskStatus.PENDING,
        estimated_time=estimated_time,
        status_url=f"/{API_VERSION}/tasks/{task_id}"
    )

@app.post(f"/{API_VERSION}/reason", response_model=ProcessingResponse)
@check_permission("execute")
async def reasoning_endpoint(request: Request, req: ReasoningRequest):
    """Specialized endpoint for reasoning tasks."""
    if not await check_rate_limit(request, "processing", 10, 60):
        raise HTTPException(
            status_code=429,
            detail="Rate limit exceeded for processing requests"
        )
    
    request_id = str(uuid.uuid4())
    start_time = time.time()
    
    try:
        # Make sure system is initialized
        if not getattr(system, "initialized", False):
            system.initialize()
        
        # Prepare reasoning parameters
        params = req.parameters.copy()
        if req.reasoning_type:
            params["reasoning_type"] = req.reasoning_type
        if req.context:
            params["context"] = req.context
        if req.max_depth:
            params["max_depth"] = req.max_depth
        
        # Call the reasoning function
        result = system.reason(req.content, **params)
        
        # Calculate processing time
        processing_time = time.time() - start_time
        
        # Serialize result to ensure JSON compatibility
        serialized_result = serialize_result(result)
        
        # Create response
        response = ProcessingResponse(
            request_id=request_id,
            timestamp=datetime.datetime.utcnow(),
            status="success",
            result=serialized_result,
            metrics={
                "processing_time": processing_time,
                "word_count": len(req.content.split()),
                "reasoning_type": req.reasoning_type or "default"
            }
        )
        
        return response
        
    except Exception as e:
        logger.error(f"Error in reasoning: {e}", exc_info=True)
        status_code, error_response = process_error_handler(e)
        
        # Create error response
        response = ProcessingResponse(
            request_id=request_id,
            timestamp=datetime.datetime.utcnow(),
            status="error",
            error=str(e),
            metrics={
                "processing_time": time.time() - start_time,
            }
        )
        
        return JSONResponse(
            status_code=status_code,
            content=jsonable_encoder(response)
        )

@app.post(f"/{API_VERSION}/evolve", response_model=EvolutionResponse)
@check_permission("admin")
async def evolve_system(request: Request, req: EvolutionRequest):
    """Trigger system evolution."""
    if not await check_rate_limit(request, "evolution", 1, 3600):  # Limit to 1 per hour
        raise HTTPException(
            status_code=429,
            detail="Rate limit exceeded for evolution requests"
        )
    
    request_id = str(uuid.uuid4())
    start_time = time.time()
    
    try:
        # Make sure system is initialized
        if not getattr(system, "initialized", False):
            system.initialize()
        
        # Get metrics before evolution
        metrics_before = {}
        if hasattr(system, "_collect_metrics") and callable(system._collect_metrics):
            system._collect_metrics()
            metrics_before = serialize_result(getattr(system, "metrics", {}))
        
        # Define fitness function based on request metrics
        def fitness_metric(subsystem=None):
            if not req.fitness_metrics:
                return None
                
            # If subsystem is specified and target components are provided,
            # only apply metrics to targeted components
            if subsystem and req.target_components:
                subsystem_name = subsystem.__class__.__name__.lower()
                if subsystem_name not in req.target_components:
                    return None
            
            # Return weighted metrics
            return req.fitness_metrics
        
        # Trigger evolution
        result = system.evolve(fitness_metric=fitness_metric)
        
        # Get metrics after evolution
        metrics_after = {}
        if hasattr(system, "_collect_metrics") and callable(system._collect_metrics):
            system._collect_metrics()
            metrics_after = serialize_result(getattr(system, "metrics", {}))
        
        # Get changes (in a real system, this would track actual changes)
        changes = [
            {
                "component": "system",
                "type": "evolution",
                "timestamp": datetime.datetime.utcnow().isoformat(),
                "success": result
            }
        ]
        
        # Create response
        response = EvolutionResponse(
            request_id=request_id,
            timestamp=datetime.datetime.utcnow(),
            success=result,
            changes=changes,
            metrics_before=metrics_before,
            metrics_after=metrics_after
        )
        
        return response
        
    except Exception as e:
        logger.error(f"Error in evolution: {e}", exc_info=True)
        
        # Create error response
        response = EvolutionResponse(
            request_id=request_id,
            timestamp=datetime.datetime.utcnow(),
            success=False,
            error=str(e),
            metrics_before={},
            metrics_after={}
        )
        
        return JSONResponse(
            status_code=500,
            content=jsonable_encoder(response)
        )

#---------------------------------------------------------------------------
# API Routes: Task Management
#---------------------------------------------------------------------------

@app.get(f"/{API_VERSION}/tasks/{{task_id}}", response_model=TaskStatusResponse)
@check_permission("read")
async def get_task(request: Request, task_id: str):
    """Get the status of an asynchronous task."""
    if not await check_rate_limit(request, "api", 60, 60):
        raise HTTPException(
            status_code=429,
            detail="Rate limit exceeded"
        )
    
    task_data = await get_task_status(task_id)
    
    if not task_data:
        raise HTTPException(
            status_code=404,
            detail=f"Task {task_id} not found"
        )
    
    # Parse timestamps
    created_at = datetime.datetime.fromisoformat(task_data.get("created_at", datetime.datetime.utcnow().isoformat()))
    updated_at = datetime.datetime.fromisoformat(task_data.get("updated_at", datetime.datetime.utcnow().isoformat()))
    
    # Convert status string to enum
    status = TaskStatus(task_data.get("status", "pending"))
    
    # Calculate progress (would be more sophisticated in a real implementation)
    progress = None
    if status == TaskStatus.PROCESSING:
        # Estimate progress based on elapsed time
        elapsed = (updated_at - created_at).total_seconds()
        # Assuming tasks take about 10 seconds on average
        progress = min(0.95, elapsed / 10)
    elif status == TaskStatus.COMPLETED:
        progress = 1.0
    elif status == TaskStatus.FAILED:
        progress = 1.0
    
    # Extract result if available
    result = task_data.get("result")
    
    # Extract error if available
    error = task_data.get("error")
    
    return TaskStatusResponse(
        task_id=task_id,
        status=status,
        progress=progress,
        result=result,
        error=error,
        created_at=created_at,
        updated_at=updated_at
    )

@app.delete(f"/{API_VERSION}/tasks/{{task_id}}")
@check_permission("write")
async def cancel_task(request: Request, task_id: str):
    """Cancel an asynchronous task."""
    if not await check_rate_limit(request, "api", 60, 60):
        raise HTTPException(
            status_code=429,
            detail="Rate limit exceeded"
        )
    
    task_data = await get_task_status(task_id)
    
    if not task_data:
        raise HTTPException(
            status_code=404,
            detail=f"Task {task_id} not found"
        )
    
    # Check if task can be canceled
    status = TaskStatus(task_data.get("status", "pending"))
    if status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELED]:
        raise HTTPException(
            status_code=400,
            detail=f"Task {task_id} is already in final state: {status.value}"
        )
    
    # Update task status to canceled
    success = await update_task_status(task_id, TaskStatus.CANCELED, error="Task canceled by user")
    
    if not success:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to cancel task {task_id}"
        )
    
    return {"status": "canceled", "task_id": task_id}

#---------------------------------------------------------------------------
# WebSocket API
#---------------------------------------------------------------------------

@app.websocket(f"/{API_VERSION}/ws")
async def websocket_endpoint(websocket: fastapi.WebSocket):
    """WebSocket endpoint for real-time communication."""
    await websocket.accept()
    
    # Authenticate connection
    try:
        auth_message = await websocket.receive_text()
        auth_data = json.loads(auth_message)
        
        if "token" not in auth_data:
            await websocket.send_json({"error": "Authentication required", "code": "auth_required"})
            await websocket.close(code=1008)
            return
        
        # Verify token
        token_data = verify_token(auth_data["token"])
        if token_data is None:
            await websocket.send_json({"error": "Invalid authentication token", "code": "invalid_token"})
            await websocket.close(code=1008)
            return
        
        # Send acknowledgment
        await websocket.send_json({"status": "connected", "user": token_data.username})
        
        # Process messages
        while True:
            try:
                message = await websocket.receive_text()
                data = json.loads(message)
                
                # Check message type
                if "type" not in data:
                    await websocket.send_json({"error": "Message type required", "code": "invalid_message"})
                    continue
                
                # Process different message types
                if data["type"] == "ping":
                    await websocket.send_json({"type": "pong", "time": time.time()})
                
                elif data["type"] == "process":
                    # Process request
                    request_id = str(uuid.uuid4())
                    
                    try:
                        # Validate request
                        if "input" not in data or "mode" not in data:
                            await websocket.send_json({
                                "type": "error",
                                "request_id": request_id, 
                                "error": "Invalid process request"
                            })
                            continue
                        
                        # Prepare processing task
                        input_data = data["input"]
                        mode = data.get("mode", "auto")
                        params = data.get("parameters", {})
                        
                        # Send acknowledgment
                        await websocket.send_json({
                            "type": "processing",
                            "request_id": request_id,
                            "status": "started"
                        })
                        
                        # Process in background to not block the websocket
                        async def process_ws_request():
                            try:
                                # Process input
                                if isinstance(input_data, str):
                                    if mode == "reasoning":
                                        result = system.reason(input_data, **params)
                                    else:
                                        result = system.process(input_data, **params)
                                else:
                                    result = system.process(input_data, **params)
                                
                                # Serialize result
                                serialized_result = serialize_result(result)
                                
                                # Send result
                                await websocket.send_json({
                                    "type": "result",
                                    "request_id": request_id,
                                    "status": "completed",
                                    "result": serialized_result
                                })
                                
                            except Exception as e:
                                logger.error(f"Error processing WebSocket request: {e}", exc_info=True)
                                await websocket.send_json({
                                    "type": "error",
                                    "request_id": request_id,
                                    "error": str(e)
                                })
                        
                        # Start processing task
                        asyncio.create_task(process_ws_request())
                    
                    except Exception as e:
                        logger.error(f"Error handling WebSocket process request: {e}", exc_info=True)
                        await websocket.send_json({
                            "type": "error",
                            "request_id": request_id, 
                            "error": str(e)
                        })
                
                elif data["type"] == "system_info":
                    # Send system info
                    system_info = get_system_info()
                    await websocket.send_json({
                        "type": "system_info",
                        "data": system_info
                    })
                
                else:
                    await websocket.send_json({
                        "error": f"Unsupported message type: {data['type']}",
                        "code": "unsupported_type"
                    })
            
            except fastapi.WebSocketDisconnect:
                logger.info("WebSocket client disconnected")
                break
            except json.JSONDecodeError:
                await websocket.send_json({"error": "Invalid JSON", "code": "json_error"})
            except Exception as e:
                logger.error(f"WebSocket error: {e}", exc_info=True)
                await websocket.send_json({"error": str(e), "code": "server_error"})
    
    except fastapi.WebSocketDisconnect:
        logger.info("WebSocket client disconnected before authentication")
    except Exception as e:
        logger.error(f"WebSocket error during handshake: {e}", exc_info=True)
        try:
            await websocket.close(code=1011)
        except:
            pass

#---------------------------------------------------------------------------
# API Documentation customization
#---------------------------------------------------------------------------

def custom_openapi():
    """Generate custom OpenAPI schema."""
    if app.openapi_schema:
        return app.openapi_schema
    
    openapi_schema = get_openapi(
        title="ULTRA API",
        version=API_VERSION,
        description="API for the Ultimate Learning & Thought Reasoning Architecture",
        routes=app.routes,
    )
    
    # Add security schemes
    openapi_schema["components"]["securitySchemes"] = {
        "bearerAuth": {
            "type": "http",
            "scheme": "bearer",
            "bearerFormat": "JWT"
        }
    }
    
    # Apply security to all operations
    for path in openapi_schema["paths"].values():
        for operation in path.values():
            operation["security"] = [{"bearerAuth": []}]
    
    # Add custom documentation
    openapi_schema["info"]["x-logo"] = {
        "url": "https://example.com/logo.png"
    }
    
    openapi_schema["info"]["contact"] = {
        "name": "ULTRA Support",
        "url": "https://example.com/support",
        "email": "<EMAIL>"
    }
    
    app.openapi_schema = openapi_schema
    return app.openapi_schema

app.openapi = custom_openapi

#---------------------------------------------------------------------------
# Additional routes for completeness
#---------------------------------------------------------------------------

@app.get("/")
async def root():
    """Root endpoint providing basic information."""
    return {
        "name": "ULTRA API",
        "version": API_VERSION,
        "documentation": f"/docs",
        "status": "available"
    }

@app.get("/health")
async def health_check():
    """Health check endpoint for monitoring."""
    # Check system status
    system_ready = getattr(system, "ready", False)
    
    # Check Redis if used
    redis_status = "available" if redis_available else "unavailable"
    
    status = "healthy" if system_ready else "initializing"
    
    return {
        "status": status,
        "timestamp": datetime.datetime.utcnow().isoformat(),
        "version": getattr(system, "config", {}).get("version", "1.0.0"),
        "components": {
            "redis": redis_status,
            "system": "ready" if system_ready else "initializing"
        }
    }

#---------------------------------------------------------------------------
# Client functions for easy programmatic usage
#---------------------------------------------------------------------------

class ULTRAClient:
    """
    Python client for interacting with the ULTRA API.
    
    This class provides a convenient interface for applications to interact
    with the ULTRA system programmatically, handling authentication,
    serialization, and error handling.
    """
    
    def __init__(self, base_url: str = "http://localhost:8000", api_version: str = API_VERSION):
        """
        Initialize the ULTRA API client.
        
        Args:
            base_url: Base URL of the ULTRA API
            api_version: API version to use
        """
        self.base_url = base_url.rstrip("/")
        self.api_version = api_version
        self.token = None
        self.session = None
    
    async def __aenter__(self):
        """Initialize async session when used as context manager."""
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Clean up async session when exiting context."""
        if self.session:
            await self.session.close()
            self.session = None
    
    async def login(self, username: str, password: str) -> bool:
        """
        Authenticate with the ULTRA API.
        
        Args:
            username: Username for authentication
            password: Password for authentication
            
        Returns:
            True if authentication was successful
        """
        if not self.session:
            self.session = aiohttp.ClientSession()
        
        auth_url = f"{self.base_url}/{self.api_version}/auth/token"
        
        try:
            async with self.session.post(
                auth_url,
                json={"username": username, "password": password}
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    self.token = data["access_token"]
                    return True
                else:
                    error_data = await response.json()
                    logger.error(f"Authentication failed: {error_data.get('detail', 'Unknown error')}")
                    return False
        except Exception as e:
            logger.error(f"Error during authentication: {e}")
            return False
    
    async def process(self, content: str, mode: str = "auto", **parameters) -> Dict[str, Any]:
        """
        Process content through the ULTRA system.
        
        Args:
            content: Content to process
            mode: Processing mode
            **parameters: Additional processing parameters
            
        Returns:
            Processing result
        """
        if not self.token:
            raise ValueError("Not authenticated. Call login() first.")
        
        if not self.session:
            self.session = aiohttp.ClientSession()
        
        process_url = f"{self.base_url}/{self.api_version}/process"
        
        # Determine input type based on content
        input_type = "text"
        if not isinstance(content, str):
            input_type = "json"
        
        # Prepare request
        request_data = {
            "input_type": input_type,
            "mode": mode,
            "content": content,
            "parameters": parameters
        }
        
        try:
            async with self.session.post(
                process_url,
                json=request_data,
                headers={"Authorization": f"Bearer {self.token}"}
            ) as response:
                data = await response.json()
                
                if response.status != 200:
                    error = data.get("error", "Unknown error")
                    raise RuntimeError(f"Processing failed: {error}")
                
                return data
        except Exception as e:
            logger.error(f"Error during processing: {e}")
            raise
    
    async def reason(self, query: str, **parameters) -> Dict[str, Any]:
        """
        Perform reasoning on a query.
        
        Args:
            query: Query to reason about
            **parameters: Additional reasoning parameters
            
        Returns:
            Reasoning result
        """
        if not self.token:
            raise ValueError("Not authenticated. Call login() first.")
        
        if not self.session:
            self.session = aiohttp.ClientSession()
        
        reason_url = f"{self.base_url}/{self.api_version}/reason"
        
        # Prepare request
        request_data = {
            "input_type": "text",
            "mode": "reasoning",
            "content": query,
            "parameters": parameters
        }
        
        if "reasoning_type" in parameters:
            request_data["reasoning_type"] = parameters.pop("reasoning_type")
            
        if "context" in parameters:
            request_data["context"] = parameters.pop("context")
            
        if "max_depth" in parameters:
            request_data["max_depth"] = parameters.pop("max_depth")
        
        try:
            async with self.session.post(
                reason_url,
                json=request_data,
                headers={"Authorization": f"Bearer {self.token}"}
            ) as response:
                data = await response.json()
                
                if response.status != 200:
                    error = data.get("error", "Unknown error")
                    raise RuntimeError(f"Reasoning failed: {error}")
                
                return data
        except Exception as e:
            logger.error(f"Error during reasoning: {e}")
            raise
    
    async def get_system_info(self) -> Dict[str, Any]:
        """
        Get system information.
        
        Returns:
            System information
        """
        if not self.token:
            raise ValueError("Not authenticated. Call login() first.")
        
        if not self.session:
            self.session = aiohttp.ClientSession()
        
        info_url = f"{self.base_url}/{self.api_version}/system/info"
        
        try:
            async with self.session.get(
                info_url,
                headers={"Authorization": f"Bearer {self.token}"}
            ) as response:
                data = await response.json()
                
                if response.status != 200:
                    error = data.get("error", "Unknown error")
                    raise RuntimeError(f"Failed to get system info: {error}")
                
                return data
        except Exception as e:
            logger.error(f"Error getting system info: {e}")
            raise
    
    async def close(self):
        """Close the client session."""
        if self.session:
            await self.session.close()
            self.session = None

#---------------------------------------------------------------------------
# Server startup and shutdown events
#---------------------------------------------------------------------------

@app.on_event("startup")
async def startup_event():
    """Initialize system on server startup."""
    logger.info("Starting ULTRA API server...")
    
    # Initialize ULTRA system if not already done
    if not getattr(system, "initialized", False):
        try:
            system.initialize()
            logger.info("ULTRA system initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize ULTRA system: {e}", exc_info=True)
            logger.warning("API will operate with limited functionality until system is initialized")

@app.on_event("shutdown")
async def shutdown_event():
    """Clean up resources on server shutdown."""
    logger.info("Shutting down ULTRA API server...")
    
    # Shut down ULTRA system
    try:
        if hasattr(system, "shutdown") and callable(system.shutdown):
            system.shutdown()
            logger.info("ULTRA system shut down successfully")
    except Exception as e:
        logger.error(f"Error shutting down ULTRA system: {e}", exc_info=True)

#---------------------------------------------------------------------------
# Main entry point for running the server directly
#---------------------------------------------------------------------------

def run_server(host: str = "0.0.0.0", port: int = 8000, reload: bool = False):
    """Run the API server."""
    uvicorn.run(
        "ultra.api:app",
        host=host,
        port=port,
        reload=reload,
        log_level="info"
    )

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Run the ULTRA API server")
    parser.add_argument("--host", type=str, default="0.0.0.0", help="Host to bind to")
    parser.add_argument("--port", type=int, default=8000, help="Port to bind to")
    parser.add_argument("--reload", action="store_true", help="Enable auto-reload for development")
    
    args = parser.parse_args()
    
    run_server(host=args.host, port=args.port, reload=args.reload)