#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Attentional Awareness Module for ULTRA

This module implements the Attentional Awareness component of the Emergent Consciousness Lattice,
which selects and focuses on the most relevant information from both external inputs and internal states.
It creates a form of "spotlight of attention" that enhances processing of selected information.

Mathematical formulations:
- Salience Calculation: S(x_i) = w_1 · N(x_i) + w_2 · R(x_i) + w_3 · U(x_i) + w_4 · G(x_i)
- Attention Allocation: A(x_i) = exp(S(x_i)/τ) / ∑_j exp(S(x_j)/τ)
- Processing Enhancement: P(x_i) = P_0(x_i) · (1 + γ · A(x_i))
- Attention Dynamics: dA(x_i, t)/dt = α · (S(x_i, t) - A(x_i, t)) - β · ∫_0^t A(x_i, τ) exp(-(t-τ)/τ_h) dτ

Author: ULTRA Development Team
"""

import numpy as np
import scipy.stats as stats
import logging
import time
import uuid
from typing import Dict, List, Tuple, Set, Optional, Union, Callable, Any
from dataclasses import dataclass, field
from collections import defaultdict, deque
import torch
import threading
import heapq
import math
import json

# Setup logging
logger = logging.getLogger(__name__)


@dataclass
class AttentionEvent:
    """Data class for attention events"""
    source_id: str
    timestamp: float
    old_value: float
    new_value: float
    event_type: str  # 'shift', 'habituation', 'reset', etc.
    magnitude: float
    metadata: Dict[str, Any] = field(default_factory=dict)


class AttentionalAwareness:
    """
    Implements mechanisms for selecting and focusing on the most relevant information,
    creating a "spotlight of attention" that enhances processing of selected information.
    
    This class calculates salience based on novelty, relevance, uncertainty, and potential gain,
    allocates attention resources based on salience, enhances processing of attended information,
    and models attention dynamics including habituation and recovery.
    """
    
    def __init__(
        self, 
        dimension: int = 128,
        weights: Dict[str, float] = None,
        temperature: float = 0.1,
        enhancement_factor: float = 2.0,
        habituation_time_const: float = 10.0,
        recovery_time_const: float = 20.0,
        max_sources: int = 100,
        alpha: float = 0.5,
        beta: float = 0.1,
        gamma: float = 0.3,
        attention_threshold: float = 0.2,
        min_attention: float = 0.01,
        history_window: int = 100,
        history_pruning_interval: int = 1000,
        enable_multithreading: bool = False
    ):
        """
        Initialize the Attentional Awareness component.
        
        Args:
            dimension: Dimension of the information space
            weights: Weights for different salience factors (novelty, relevance, uncertainty, gain)
            temperature: Temperature parameter for attention allocation softmax
            enhancement_factor: Factor for enhancing processing of attended information
            habituation_time_const: Time constant for attention habituation
            recovery_time_const: Time constant for recovery from habituation
            max_sources: Maximum number of sources to track
            alpha: Learning rate for attention dynamics
            beta: Habituation factor
            gamma: Integration factor for attention updates
            attention_threshold: Threshold for significant attention
            min_attention: Minimum attention value
            history_window: Number of attention history entries to retain
            history_pruning_interval: Interval for pruning attention history
            enable_multithreading: Whether to enable multithreaded processing
        """
        self.dimension = dimension
        self.weights = weights or {
            "novelty": 0.3,
            "relevance": 0.4,
            "uncertainty": 0.2,
            "gain": 0.1,
            "emotional_valence": 0.1,
            "goal_congruence": 0.3,
            "surprise": 0.2,
            "perceptual_salience": 0.15,
            "contextual_relevance": 0.25
        }
        
        # Normalize weights to sum to 1
        weight_sum = sum(self.weights.values())
        if weight_sum > 0:
            self.weights = {k: v / weight_sum for k, v in self.weights.items()}
        
        self.temperature = temperature
        self.enhancement_factor = enhancement_factor
        self.habituation_time_const = habituation_time_const
        self.recovery_time_const = recovery_time_const
        self.max_sources = max_sources
        self.alpha = alpha
        self.beta = beta
        self.gamma = gamma
        self.attention_threshold = attention_threshold
        self.min_attention = min_attention
        self.history_window = history_window
        self.history_pruning_interval = history_pruning_interval
        self.enable_multithreading = enable_multithreading
        
        # Attention state
        self.attention_values = {}  # Maps source_id -> attention value
        self.attention_history = defaultdict(list)  # Maps source_id -> list of (timestamp, value) tuples
        self.last_values = {}  # Maps source_id -> last observed value
        self.last_update_time = {}  # Maps source_id -> last update timestamp
        self.habituation_levels = {}  # Maps source_id -> habituation level
        
        # Focus tracking
        self.current_focus = []  # List of source_ids in current focus
        self.focus_history = []  # List of (timestamp, focus_set) tuples
        
        # Event tracking
        self.attention_events = []  # List of AttentionEvent objects
        
        # Performance metrics
        self.metrics = {
            "process_count": 0,
            "avg_processing_time": 0.0,
            "attention_shifts": 0,
            "habituation_events": 0,
            "recovery_events": 0
        }
        
        # Threading resources
        self.lock = threading.RLock()
        self.processing_threads = {}
        
        # Counter for operation calls
        self.operation_count = 0
        
        logger.info(f"Initialized AttentionalAwareness with dimension={dimension}, " + 
                   f"temperature={temperature}, enhancement_factor={enhancement_factor}")
    
    def calculate_salience(
        self, 
        source_id: str, 
        value: np.ndarray, 
        salience_factors: Optional[Dict[str, float]] = None
    ) -> float:
        """
        Calculate the salience of an information source.
        
        S(x_i) = w_1 · N(x_i) + w_2 · R(x_i) + w_3 · U(x_i) + w_4 · G(x_i) + ...
        
        Args:
            source_id: Identifier for the information source
            value: Current value of the information source
            salience_factors: Dictionary of salience factors (relevance, gain, etc.)
            
        Returns:
            Salience score between 0 and 1
        """
        salience_factors = salience_factors or {}
        
        # Initialize all factors with default values
        all_factors = {
            "novelty": 0.0,
            "relevance": 0.5,
            "uncertainty": 0.5,
            "gain": 0.5,
            "emotional_valence": 0.5,
            "goal_congruence": 0.5,
            "surprise": 0.0,
            "perceptual_salience": 0.5,
            "contextual_relevance": 0.5
        }
        
        # Update with provided factors
        all_factors.update(salience_factors)
        
        # Calculate novelty (difference from last observed value)
        if source_id in self.last_values:
            last_value = self.last_values[source_id]
            try:
                # For vector values
                novelty = np.linalg.norm(value - last_value) / np.sqrt(self.dimension)
            except (TypeError, ValueError):
                # For scalar values or incompatible shapes
                try:
                    novelty = abs(float(value) - float(last_value))
                except (TypeError, ValueError):
                    # If conversion fails, use a default value
                    novelty = 0.5
        else:
            novelty = 1.0  # Maximum novelty for first observation
        
        # Normalize novelty to [0, 1]
        novelty = min(1.0, novelty)
        all_factors["novelty"] = novelty
        
        # Calculate surprise (statistical unexpectedness)
        if source_id in self.attention_history and len(self.attention_history[source_id]) > 5:
            # Get recent values
            recent_values = []
            for ts, val in self.attention_history[source_id][-5:]:
                if isinstance(val, (int, float)):
                    recent_values.append(val)
                
            if recent_values:
                # Calculate z-score for current value
                try:
                    mean = np.mean(recent_values)
                    std = max(np.std(recent_values), 1e-6)  # Avoid division by zero
                    z_score = abs(float(value) - mean) / std
                    surprise = min(1.0, z_score / 3.0)  # Normalize, assuming z-score > 3 is very surprising
                    all_factors["surprise"] = surprise
                except (TypeError, ValueError):
                    pass
        
        # Update last value
        try:
            self.last_values[source_id] = value.copy() if hasattr(value, 'copy') else value
        except AttributeError:
            self.last_values[source_id] = value
        
        # Calculate weighted salience
        salience = 0.0
        for factor, weight in self.weights.items():
            if factor in all_factors:
                salience += weight * all_factors[factor]
        
        # Ensure salience is in [0, 1]
        return max(0.0, min(1.0, salience))
    
    def allocate_attention(
        self, 
        salience_values: Dict[str, float],
        current_attention: Optional[Dict[str, float]] = None
    ) -> Dict[str, float]:
        """
        Allocate attention resources based on salience.
        
        A(x_i) = exp(S(x_i)/τ) / ∑_j exp(S(x_j)/τ)
        
        Args:
            salience_values: Dictionary mapping source_id to salience value
            current_attention: Current attention values for smoothing
            
        Returns:
            Dictionary mapping source_id to allocated attention
        """
        if not salience_values:
            return {}
        
        # Apply habituation to salience values
        adjusted_salience = {}
        for source_id, salience in salience_values.items():
            habituation = self.habituation_levels.get(source_id, 0.0)
            adjusted_salience[source_id] = salience * (1.0 - habituation)
        
        # Convert to array for numerical stability
        source_ids = list(adjusted_salience.keys())
        salience_array = np.array([adjusted_salience[sid] for sid in source_ids])
        
        # Apply softmax with temperature
        scaled_salience = salience_array / self.temperature
        # Subtract max for numerical stability
        max_salience = np.max(scaled_salience) if scaled_salience.size > 0 else 0
        exp_salience = np.exp(scaled_salience - max_salience)
        sum_exp = np.sum(exp_salience)
        
        if sum_exp > 0:
            attention_values = exp_salience / sum_exp
        else:
            attention_values = np.ones_like(salience_array) / len(salience_array)
        
        # Convert back to dictionary
        new_attention = {sid: float(av) for sid, av in zip(source_ids, attention_values)}
        
        # Apply smoothing if current attention values are available
        if current_attention:
            for source_id, attention in new_attention.items():
                if source_id in current_attention:
                    current = current_attention[source_id]
                    # Apply exponential smoothing
                    new_attention[source_id] = (1 - self.gamma) * current + self.gamma * attention
        
        # Ensure all attention values have at least min_attention
        return {sid: max(self.min_attention, att) for sid, att in new_attention.items()}
    
    def enhance_processing(
        self, 
        values: Dict[str, Any], 
        attention_values: Dict[str, float]
    ) -> Dict[str, Any]:
        """
        Enhance the processing of attended information.
        
        P(x_i) = P_0(x_i) · (1 + γ · A(x_i))
        
        Args:
            values: Dictionary mapping source_id to information value
            attention_values: Dictionary mapping source_id to attention value
            
        Returns:
            Dictionary mapping source_id to enhanced value
        """
        enhanced_values = {}
        
        for source_id, value in values.items():
            attention = attention_values.get(source_id, 0.0)
            enhancement = 1.0 + self.enhancement_factor * attention
            
            try:
                # For numpy arrays or torch tensors
                if isinstance(value, np.ndarray):
                    enhanced_values[source_id] = value * enhancement
                elif isinstance(value, torch.Tensor):
                    enhanced_values[source_id] = value * enhancement
                # For scalar values
                elif isinstance(value, (int, float)):
                    enhanced_values[source_id] = value * enhancement
                # For complex values, just pass through
                else:
                    enhanced_values[source_id] = value
            except Exception as e:
                logger.warning(f"Error enhancing value for {source_id}: {str(e)}")
                enhanced_values[source_id] = value
            
        return enhanced_values
    
    def update_habituation(
        self, 
        attention_values: Dict[str, float], 
        dt: float = 1.0
    ) -> None:
        """
        Update habituation levels based on attention.
        
        dH(x_i)/dt = β · A(x_i) - H(x_i)/τ_r
        
        Args:
            attention_values: Dictionary mapping source_id to attention value
            dt: Time step for the update
        """
        for source_id, attention in attention_values.items():
            current_habituation = self.habituation_levels.get(source_id, 0.0)
            
            # Increase habituation based on attention
            habituation_increase = self.beta * attention * dt
            
            # Recovery from habituation
            recovery = current_habituation * dt / self.recovery_time_const
            
            # Update habituation level
            new_habituation = current_habituation + habituation_increase - recovery
            new_habituation = max(0.0, min(1.0, new_habituation))
            
            # Record habituation event if significant change
            if abs(new_habituation - current_habituation) > 0.1:
                event_type = 'habituation' if new_habituation > current_habituation else 'recovery'
                self.attention_events.append(AttentionEvent(
                    source_id=source_id,
                    timestamp=time.time(),
                    old_value=current_habituation,
                    new_value=new_habituation,
                    event_type=event_type,
                    magnitude=abs(new_habituation - current_habituation)
                ))
                
                if event_type == 'habituation':
                    self.metrics["habituation_events"] += 1
                else:
                    self.metrics["recovery_events"] += 1
            
            self.habituation_levels[source_id] = new_habituation
    
    def update_attention_dynamics(
        self,
        salience_values: Dict[str, float],
        dt: float = 1.0
    ) -> Dict[str, float]:
        """
        Update the temporal dynamics of attention, including habituation and shifts.
        
        dA(x_i, t)/dt = α · (S(x_i, t) - A(x_i, t)) - β · ∫_0^t A(x_i, τ) exp(-(t-τ)/τ_h) dτ
        
        Args:
            salience_values: Dictionary mapping source_id to salience
            dt: Time step for the update
            
        Returns:
            Dictionary mapping source_id to updated attention value
        """
        current_time = time.time()
        updated_attention = {}
        
        for source_id, attention_value in self.attention_values.items():
            # Get current salience or use default
            current_salience = salience_values.get(source_id, 0.0)
            
            # Calculate habituation term using historical values
            habituation = 0.0
            if source_id in self.attention_history:
                history = self.attention_history[source_id]
                
                for timestamp, value in history:
                    time_diff = current_time - timestamp
                    if time_diff > 0:
                        habituation += value * np.exp(-time_diff / self.habituation_time_const) * dt
            
            # Calculate the change in attention
            dA = self.alpha * (current_salience - attention_value) - self.beta * habituation
            
            # Update attention value
            new_attention = max(0.0, min(1.0, attention_value + dA))
            updated_attention[source_id] = new_attention
            
            # Record attention shift if significant
            if abs(new_attention - attention_value) > 0.2:
                self.attention_events.append(AttentionEvent(
                    source_id=source_id,
                    timestamp=current_time,
                    old_value=attention_value,
                    new_value=new_attention,
                    event_type='shift',
                    magnitude=abs(new_attention - attention_value)
                ))
                self.metrics["attention_shifts"] += 1
        
        return updated_attention
    
    def _update_attention_history(
        self, 
        source_id: str, 
        attention_value: float
    ) -> None:
        """
        Update the attention history for a source.
        
        Args:
            source_id: Identifier for the information source
            attention_value: Current attention value
        """
        current_time = time.time()
        
        # Add to attention history
        self.attention_history[source_id].append((current_time, attention_value))
        
        # Limit history size
        if len(self.attention_history[source_id]) > self.history_window:
            self.attention_history[source_id] = self.attention_history[source_id][-self.history_window:]
    
    def update_focus(self) -> List[str]:
        """
        Update the current focus based on attention values.
        
        Returns:
            List of source IDs in the current focus
        """
        if not self.attention_values:
            self.current_focus = []
            return []
        
        # Sort sources by attention value
        sorted_sources = sorted(
            self.attention_values.items(), 
            key=lambda x: x[1], 
            reverse=True
        )
        
        # Determine focus threshold (adaptive)
        if sorted_sources:
            max_attention = sorted_sources[0][1]
            focus_threshold = max(self.attention_threshold, max_attention * 0.5)
        else:
            focus_threshold = self.attention_threshold
        
        # Update focus to include sources above threshold
        new_focus = [sid for sid, att in sorted_sources if att >= focus_threshold]
        
        # Record focus shift if changed
        if set(new_focus) != set(self.current_focus):
            self.focus_history.append((time.time(), set(new_focus)))
            # Limit history size
            if len(self.focus_history) > self.history_window:
                self.focus_history = self.focus_history[-self.history_window:]
        
        self.current_focus = new_focus
        return new_focus
    
    def process_information(
        self,
        information_sources: Dict[str, Any],
        salience_factors: Optional[Dict[str, Dict[str, float]]] = None,
        dt: float = 0.1
    ) -> Dict[str, Any]:
        """
        Process information through the attentional awareness system.
        
        Args:
            information_sources: Dictionary mapping source_id to information value
            salience_factors: Optional dictionary mapping source_id to factor dictionary
            dt: Time step for attention dynamics update
            
        Returns:
            Dictionary mapping source_id to enhanced information value
        """
        start_time = time.time()
        salience_factors = salience_factors or {}
        
        with self.lock:
            # Increment operation counter
            self.operation_count += 1
            
            # If we have too many sources, prune least recently attended
            if len(self.attention_values) >= self.max_sources:
                self._prune_sources()
            
            # Periodically prune history
            if self.operation_count % self.history_pruning_interval == 0:
                self._prune_history()
            
            # Calculate salience for each information source
            salience_values = {}
            for source_id, value in information_sources.items():
                factors = salience_factors.get(source_id, {})
                try:
                    # Convert value to numpy array if possible
                    if isinstance(value, list):
                        value = np.array(value)
                    salience_values[source_id] = self.calculate_salience(source_id, value, factors)
                except Exception as e:
                    logger.warning(f"Error calculating salience for {source_id}: {str(e)}")
                    salience_values[source_id] = 0.5  # Default value
            
            # Allocate attention
            updated_attention = self.allocate_attention(salience_values, self.attention_values)
            
            # Update habituation
            self.update_habituation(updated_attention, dt)
            
            # Update attention dynamics
            dynamic_attention = self.update_attention_dynamics(salience_values, dt)
            
            # Combine allocated and dynamic attention
            for source_id, attention in dynamic_attention.items():
                if source_id in updated_attention:
                    updated_attention[source_id] = 0.5 * (updated_attention[source_id] + attention)
            
            # Update attention state
            self.attention_values = updated_attention
            
            # Update attention history
            for source_id, attention in updated_attention.items():
                self._update_attention_history(source_id, attention)
            
            # Update focus
            self.update_focus()
            
            # Enhance processing
            enhanced_values = self.enhance_processing(information_sources, updated_attention)
            
            # Update metrics
            self.metrics["process_count"] += 1
            process_time = time.time() - start_time
            self.metrics["avg_processing_time"] = (
                (self.metrics["avg_processing_time"] * (self.metrics["process_count"] - 1) + process_time) / 
                self.metrics["process_count"]
            )
            
            return enhanced_values
    
    def _prune_sources(self) -> None:
        """
        Prune least recently attended sources to stay within max_sources limit.
        """
        # Skip if we're under the limit
        if len(self.attention_values) < self.max_sources:
            return
        
        # Find least recently attended sources
        source_timestamps = []
        for source_id in self.attention_values:
            if source_id in self.last_update_time:
                source_timestamps.append((source_id, self.last_update_time[source_id]))
            else:
                source_timestamps.append((source_id, 0))
        
        # Sort by timestamp (oldest first)
        source_timestamps.sort(key=lambda x: x[1])
        
        # Get IDs of sources to remove
        num_to_remove = len(self.attention_values) - self.max_sources + 10  # Remove extra to avoid frequent pruning
        sources_to_remove = [x[0] for x in source_timestamps[:num_to_remove]]
        
        # Remove sources
        for source_id in sources_to_remove:
            if source_id in self.attention_values:
                del self.attention_values[source_id]
            if source_id in self.habituation_levels:
                del self.habituation_levels[source_id]
            if source_id in self.last_update_time:
                del self.last_update_time[source_id]
            if source_id in self.last_values:
                del self.last_values[source_id]
            # We keep attention history for potential future reference
    
    def _prune_history(self) -> None:
        """
        Prune attention history to limit memory usage.
        """
        current_time = time.time()
        cutoff_time = current_time - (self.history_window * 10)  # Keep ~10x window worth of history
        
        for source_id in list(self.attention_history.keys()):
            history = self.attention_history[source_id]
            # Remove old entries
            self.attention_history[source_id] = [(ts, val) for ts, val in history if ts >= cutoff_time]
            
            # Remove empty histories
            if not self.attention_history[source_id]:
                del self.attention_history[source_id]
        
        # Prune event history
        if len(self.attention_events) > self.history_window:
            self.attention_events = self.attention_events[-self.history_window:]
            
        # Prune focus history
        if len(self.focus_history) > self.history_window:
            self.focus_history = self.focus_history[-self.history_window:]

    def process_information_async(
        self,
        information_sources: Dict[str, Any],
        salience_factors: Optional[Dict[str, Dict[str, float]]] = None,
        callback: Optional[Callable[[Dict[str, Any]], None]] = None
    ) -> str:
        """
        Process information asynchronously.
        
        Args:
            information_sources: Dictionary mapping source_id to information value
            salience_factors: Optional dictionary mapping source_id to factor dictionary
            callback: Optional callback function to call with results
            
        Returns:
            ID of the processing thread
        """
        if not self.enable_multithreading:
            # If multithreading disabled, process synchronously
            result = self.process_information(information_sources, salience_factors)
            if callback:
                callback(result)
            return "sync"
        
        # Generate a thread ID
        thread_id = str(uuid.uuid4())
        
        # Define the thread function
        def process_thread():
            try:
                result = self.process_information(information_sources, salience_factors)
                if callback:
                    callback(result)
            except Exception as e:
                logger.error(f"Error in attention processing thread: {str(e)}")
            finally:
                with self.lock:
                    if thread_id in self.processing_threads:
                        del self.processing_threads[thread_id]
        
        # Create and start the thread
        thread = threading.Thread(target=process_thread)
        thread.daemon = True
        
        with self.lock:
            self.processing_threads[thread_id] = thread
        
        thread.start()
        return thread_id
    
    def wait_for_processing(self, timeout: Optional[float] = None) -> bool:
        """
        Wait for all processing threads to complete.
        
        Args:
            timeout: Maximum time to wait (in seconds), or None to wait indefinitely
            
        Returns:
            True if all threads completed, False if timeout occurred
        """
        if not self.enable_multithreading:
            return True
        
        start_time = time.time()
        
        while True:
            with self.lock:
                active_threads = list(self.processing_threads.values())
            
            if not active_threads:
                return True
            
            # Check timeout
            if timeout is not None and time.time() - start_time > timeout:
                return False
            
            # Wait a short time before checking again
            time.sleep(0.01)
    
    def get_attention_state(self) -> Dict[str, float]:
        """
        Get the current attention state for all sources.
        
        Returns:
            Dictionary mapping source_id to attention value
        """
        with self.lock:
            return self.attention_values.copy()
    
    def get_attention_history(
        self, 
        source_id: str,
        window: Optional[int] = None
    ) -> List[Tuple[float, float]]:
        """
        Get the attention history for a specific source.
        
        Args:
            source_id: Identifier for the information source
            window: Optional number of history entries to return
            
        Returns:
            List of (timestamp, attention_value) tuples
        """
        with self.lock:
            history = self.attention_history.get(source_id, [])
            if window:
                return history[-window:]
            return history.copy()
    
    def get_top_attended_sources(self, k: int = 3) -> List[Tuple[str, float]]:
        """
        Get the top k attended information sources.
        
        Args:
            k: Number of sources to return
            
        Returns:
            List of (source_id, attention_value) tuples
        """
        with self.lock:
            sorted_sources = sorted(
                self.attention_values.items(), 
                key=lambda x: x[1], 
                reverse=True
            )
            return sorted_sources[:k]
    
    def get_current_focus(self) -> List[str]:
        """
        Get the current attentional focus.
        
        Returns:
            List of source IDs in current focus
        """
        with self.lock:
            return self.current_focus.copy()
    
    def get_focus_stability(self, window: int = 5) -> float:
        """
        Calculate the stability of attentional focus over recent history.
        
        Args:
            window: Number of focus snapshots to consider
            
        Returns:
            Stability score (0-1), higher means more stable
        """
        with self.lock:
            if len(self.focus_history) < 2:
                return 1.0  # Default to max stability if not enough data
            
            # Get recent focus snapshots
            recent_snapshots = [focus_set for _, focus_set in self.focus_history[-window:]]
            if len(recent_snapshots) < 2:
                return 1.0
            
            # Calculate Jaccard similarity between consecutive snapshots
            similarities = []
            for i in range(len(recent_snapshots) - 1):
                set1 = recent_snapshots[i]
                set2 = recent_snapshots[i + 1]
                
                if not set1 or not set2:
                    similarities.append(0.0)
                    continue
                
                # Jaccard similarity: |A ∩ B| / |A ∪ B|
                intersection = len(set1.intersection(set2))
                union = len(set1.union(set2))
                
                if union > 0:
                    similarities.append(intersection / union)
                else:
                    similarities.append(0.0)
            
            # Average similarity is the stability score
            return sum(similarities) / len(similarities) if similarities else 1.0
    
    def get_attention_events(
        self, 
        event_type: Optional[str] = None,
        max_events: int = 100
    ) -> List[AttentionEvent]:
        """
        Get recent attention events.
        
        Args:
            event_type: Optional event type to filter ('shift', 'habituation', 'recovery')
            max_events: Maximum number of events to return
            
        Returns:
            List of AttentionEvent objects
        """
        with self.lock:
            if event_type:
                events = [e for e in self.attention_events if e.event_type == event_type]
            else:
                events = self.attention_events.copy()
            
            # Sort by timestamp (most recent first)
            events.sort(key=lambda e: e.timestamp, reverse=True)
            
            return events[:max_events]
    
    def get_attention_metrics(self) -> Dict[str, Any]:
        """
        Get metrics about the attention system's performance.
        
        Returns:
            Dictionary of metrics
        """
        with self.lock:
            metrics = self.metrics.copy()
            metrics.update({
                "num_sources": len(self.attention_values),
                "num_focus_sources": len(self.current_focus),
                "focus_stability": self.get_focus_stability(),
                "avg_attention": np.mean(list(self.attention_values.values())) if self.attention_values else 0.0,
                "max_attention": max(self.attention_values.values()) if self.attention_values else 0.0
            })
            return metrics
    
    def reset_source(self, source_id: str) -> None:
        """
        Reset the attention state for a specific source.
        
        Args:
            source_id: Identifier for the information source
        """
        with self.lock:
            old_attention = self.attention_values.get(source_id, 0.0)
            
            # Reset attention value
            self.attention_values[source_id] = 0.0
            
            # Reset habituation
            self.habituation_levels[source_id] = 0.0
            
            # Update history
            self._update_attention_history(source_id, 0.0)
            
            # Record reset event
            self.attention_events.append(AttentionEvent(
                source_id=source_id,
                timestamp=time.time(),
                old_value=old_attention,
                new_value=0.0,
                event_type='reset',
                magnitude=old_attention
            ))
    
    def boost_attention(
        self, 
        source_id: str, 
        boost_factor: float = 1.5
    ) -> float:
        """
        Temporarily boost attention for a specific source.
        
        Args:
            source_id: Identifier for the information source
            boost_factor: Factor to multiply current attention by
            
        Returns:
            New attention value
        """
        with self.lock:
            if source_id in self.attention_values:
                old_attention = self.attention_values[source_id]
                new_attention = min(1.0, old_attention * boost_factor)
                self.attention_values[source_id] = new_attention
                
                # Update history
                self._update_attention_history(source_id, new_attention)
                
                # Record boost event
                self.attention_events.append(AttentionEvent(
                    source_id=source_id,
                    timestamp=time.time(),
                    old_value=old_attention,
                    new_value=new_attention,
                    event_type='boost',
                    magnitude=new_attention - old_attention
                ))
                
                return new_attention
            else:
                # If source not found, add it with default attention
                self.attention_values[source_id] = 0.5
                self._update_attention_history(source_id, 0.5)
                return 0.5
    
    def adjust_weights(
        self, 
        new_weights: Dict[str, float],
        normalize: bool = True
    ) -> Dict[str, float]:
        """
        Adjust the weights for different salience factors.
        
        Args:
            new_weights: Dictionary mapping factor to new weight
            normalize: Whether to normalize weights to sum to 1
            
        Returns:
            Updated weights dictionary
        """
        with self.lock:
            # Update weights
            self.weights.update(new_weights)
            
            # Normalize if requested
            if normalize:
                weight_sum = sum(self.weights.values())
                if weight_sum > 0:
                    self.weights = {k: v / weight_sum for k, v in self.weights.items()}
            
            return self.weights.copy()
    
    def save_state(self) -> Dict[str, Any]:
        """
        Save the current state of the attentional awareness system.
        
        Returns:
            Dictionary containing the state data
        """
        with self.lock:
            # Convert history to serializable format
            serialized_history = {}
            for source_id, history in self.attention_history.items():
                serialized_history[source_id] = history
                
            # Convert focus history to serializable format
            serialized_focus_history = [
                (ts, list(focus_set)) for ts, focus_set in self.focus_history
            ]
            
            # Convert attention events to serializable format
            serialized_events = []
            for event in self.attention_events:
                serialized_events.append({
                    'source_id': event.source_id,
                    'timestamp': event.timestamp,
                    'old_value': event.old_value,
                    'new_value': event.new_value,
                    'event_type': event.event_type,
                    'magnitude': event.magnitude,
                    'metadata': event.metadata
                })
            
            return {
                'dimension': self.dimension,
                'weights': self.weights,
                'temperature': self.temperature,
                'enhancement_factor': self.enhancement_factor,
                'habituation_time_const': self.habituation_time_const,
                'recovery_time_const': self.recovery_time_const,
                'max_sources': self.max_sources,
                'alpha': self.alpha,
                'beta': self.beta,
                'gamma': self.gamma,
                'attention_threshold': self.attention_threshold,
                'min_attention': self.min_attention,
                'history_window': self.history_window,
                'attention_values': self.attention_values,
                'attention_history': serialized_history,
                'last_update_time': self.last_update_time,
                'habituation_levels': self.habituation_levels,
                'current_focus': self.current_focus,
                'focus_history': serialized_focus_history,
                'metrics': self.metrics,
                'attention_events': serialized_events
            }
    
    def load_state(self, state: Dict[str, Any]) -> None:
        """
        Load a previously saved state.
        
        Args:
            state: Dictionary containing the state data
        """
        with self.lock:
            self.dimension = state.get('dimension', self.dimension)
            self.weights = state.get('weights', self.weights)
            self.temperature = state.get('temperature', self.temperature)
            self.enhancement_factor = state.get('enhancement_factor', self.enhancement_factor)
            self.habituation_time_const = state.get('habituation_time_const', self.habituation_time_const)
            self.recovery_time_const = state.get('recovery_time_const', self.recovery_time_const)
            self.max_sources = state.get('max_sources', self.max_sources)
            self.alpha = state.get('alpha', self.alpha)
            self.beta = state.get('beta', self.beta)
            self.gamma = state.get('gamma', self.gamma)
            self.attention_threshold = state.get('attention_threshold', self.attention_threshold)
            self.min_attention = state.get('min_attention', self.min_attention)
            self.history_window = state.get('history_window', self.history_window)
            
            self.attention_values = state.get('attention_values', {})
            
            # Load attention history
            self.attention_history = defaultdict(list)
            for source_id, history in state.get('attention_history', {}).items():
                self.attention_history[source_id] = history
                
            self.last_update_time = state.get('last_update_time', {})
            self.habituation_levels = state.get('habituation_levels', {})
            self.current_focus = state.get('current_focus', [])
            
            # Load focus history
            self.focus_history = []
            for ts, focus_list in state.get('focus_history', []):
                self.focus_history.append((ts, set(focus_list)))
                
            self.metrics = state.get('metrics', self.metrics)
            
            # Load attention events
            self.attention_events = []
            for event_data in state.get('attention_events', []):
                self.attention_events.append(AttentionEvent(
                    source_id=event_data['source_id'],
                    timestamp=event_data['timestamp'],
                    old_value=event_data['old_value'],
                    new_value=event_data['new_value'],
                    event_type=event_data['event_type'],
                    magnitude=event_data['magnitude'],
                    metadata=event_data.get('metadata', {})
                ))
            
            logger.info(f"Loaded attentional awareness state with {len(self.attention_values)} sources")


class AttentionalAwarenessModule:
    """
    Module wrapper for the AttentionalAwareness class that exposes a standardized API
    for integration with the ULTRA system.
    """
    
    def __init__(
        self,
        config: Optional[Dict[str, Any]] = None,
        enable_events: bool = True,
        log_level: int = logging.INFO
    ):
        """
        Initialize the AttentionalAwarenessModule.
        
        Args:
            config: Configuration for the AttentionalAwareness system
            enable_events: Whether to enable event tracking
            log_level: Logging level
        """
        logger.setLevel(log_level)
        
        # Initialize the attention system
        self.config = config or {}
        self.attention_system = AttentionalAwareness(**self.config)
        
        # Event handling
        self.enable_events = enable_events
        self.event_listeners = {}
        
        # Setup event processing thread if events enabled
        if enable_events:
            self.event_queue = deque(maxlen=1000)
            self.event_processing_active = True
            self.event_thread = threading.Thread(target=self._process_events)
            self.event_thread.daemon = True
            self.event_thread.start()
    
    def process(
        self,
        inputs: Dict[str, Any],
        metadata: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Process inputs through the attention system.
        
        Args:
            inputs: Dictionary mapping source_id to input value
            metadata: Optional metadata including salience factors
            
        Returns:
            Dictionary with processed outputs and attention information
        """
        metadata = metadata or {}
        salience_factors = metadata.get('salience_factors', {})
        
        # Process through attention system
        enhanced_values = self.attention_system.process_information(inputs, salience_factors)
        
        # Get current attention state
        attention_state = self.attention_system.get_attention_state()
        current_focus = self.attention_system.get_current_focus()
        
        # Collect attention events if enabled
        if self.enable_events:
            events = self.attention_system.get_attention_events(max_events=10)
            for event in events:
                self.event_queue.append(event)
        
        return {
            'enhanced_values': enhanced_values,
            'attention_state': attention_state,
            'current_focus': current_focus,
            'metrics': self.attention_system.get_attention_metrics()
        }
    
    def register_event_listener(
        self, 
        event_type: str,
        listener: Callable[[AttentionEvent], None]
    ) -> str:
        """
        Register a listener for attention events.
        
        Args:
            event_type: Type of event to listen for, or '*' for all events
            listener: Callback function that takes an AttentionEvent
            
        Returns:
            Listener ID
        """
        if not self.enable_events:
            logger.warning("Event handling is disabled, listener will not receive events")
        
        listener_id = str(uuid.uuid4())
        self.event_listeners[listener_id] = (event_type, listener)
        return listener_id
    
    def unregister_event_listener(self, listener_id: str) -> bool:
        """
        Unregister an event listener.
        
        Args:
            listener_id: ID of the listener to remove
            
        Returns:
            True if listener was removed, False if not found
        """
        if listener_id in self.event_listeners:
            del self.event_listeners[listener_id]
            return True
        return False
    
    def _process_events(self) -> None:
        """
        Process events in the background.
        """
        while self.event_processing_active:
            try:
                # Process events in queue
                while self.event_queue:
                    event = self.event_queue.popleft()
                    
                    # Notify listeners
                    for listener_id, (event_type, listener) in list(self.event_listeners.items()):
                        if event_type == '*' or event_type == event.event_type:
                            try:
                                listener(event)
                            except Exception as e:
                                logger.error(f"Error in event listener {listener_id}: {str(e)}")
            except Exception as e:
                logger.error(f"Error in event processing: {str(e)}")
            
            # Sleep briefly
            time.sleep(0.05)
    
    def set_config(self, config: Dict[str, Any]) -> None:
        """
        Update configuration of the attention system.
        
        Args:
            config: Dictionary of configuration parameters
        """
        for key, value in config.items():
            if hasattr(self.attention_system, key):
                setattr(self.attention_system, key, value)
                
        logger.info(f"Updated attention system configuration with parameters: {list(config.keys())}")
    
    def get_status(self) -> Dict[str, Any]:
        """
        Get status information about the attention system.
        
        Returns:
            Dictionary with status information
        """
        top_sources = self.attention_system.get_top_attended_sources(5)
        metrics = self.attention_system.get_attention_metrics()
        
        return {
            'top_sources': top_sources,
            'focus': self.attention_system.get_current_focus(),
            'focus_stability': self.attention_system.get_focus_stability(),
            'metrics': metrics,
            'num_event_listeners': len(self.event_listeners)
        }
    
    def save_state(self) -> Dict[str, Any]:
        """
        Save the state of the attention system.
        
        Returns:
            Dictionary with state data
        """
        return {
            'attention_system': self.attention_system.save_state(),
            'config': self.config,
            'enable_events': self.enable_events
        }
    
    def load_state(self, state: Dict[str, Any]) -> None:
        """
        Load a previously saved state.
        
        Args:
            state: Dictionary with state data
        """
        if 'attention_system' in state:
            self.attention_system.load_state(state['attention_system'])
        
        self.config = state.get('config', self.config)
        self.enable_events = state.get('enable_events', self.enable_events)
        
        logger.info("Loaded attention system state")
    
    def shutdown(self) -> None:
        """
        Shut down the module, stopping any background threads.
        """
        if self.enable_events:
            self.event_processing_active = False
            if hasattr(self, 'event_thread') and self.event_thread.is_alive():
                self.event_thread.join(timeout=1.0)
            
        # Wait for any processing to complete
        self.attention_system.wait_for_processing(timeout=2.0)
        
        logger.info("Attention system shut down")


# Create default module instance
default_module = AttentionalAwarenessModule()

# Export public classes
__all__ = [
    'AttentionalAwareness',
    'AttentionalAwarenessModule',
    'AttentionEvent',
    'default_module'
]