"""
Integrated Information Matrix for ULTRA
This module implements the Integrated Information Matrix (IIM) based on principles from 
Integrated Information Theory (IIT), creating a unified information space within the 
ULTRA system where subsystems can share and combine information effectively.

The IIM is a key component of the Emergent Consciousness Lattice subsystem, enabling
the emergence of system-level awareness and information integration.
"""

import numpy as np
import time
import matplotlib.pyplot as plt
import networkx as nx
import json
import logging
from scipy.stats import entropy
from scipy.special import rel_entr
from scipy.optimize import linprog, minimize
from scipy.linalg import block_diag
from scipy.spatial.distance import pdist, squareform
from typing import Dict, List, Tuple, Any, Optional, Union, Set, Callable
import itertools
from multiprocessing import Pool, cpu_count
from functools import partial
from tqdm import tqdm

from ultra.emergent_consciousness.integrated_information_matrix import create_integrated_information_matrix

# Create IIM for ULTRA's core subsystems
subsystems = ["Core_Neural", "Hyper_Transformer", "Diffusion_Reasoning", 
              "Meta_Cognitive", "Neuromorphic_Processing", "Global_Workspace", 
              "Neuro_Symbolic", "Self_Evolution"]

iim = create_integrated_information_matrix(
    num_subsystems=8,
    state_dimension=64,
    subsystem_names=subsystems,
    optimize=True
)

# Measure and optimize integration
phi = iim.compute_phi()
print(f"System Φ = {phi:.6f}")

# Visualize the integrated network
iim.visualize_integration_network()

# Compute comprehensive integration profile
metrics = iim.compute_information_integration_profile()




# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class IntegratedInformationMatrix:
    """
    Implementation of the Integrated Information Matrix based on principles
    from Integrated Information Theory (IIT). This system aims to maximize
    internal information integration to create a unified information space
    within the ULTRA architecture.
    
    This class provides methods for:
    1. Computing integrated information (Φ) for the system and subsystems
    2. Optimizing connectivity to maximize integration
    3. Propagating information through the integrated network
    4. Visualizing the integration network and metrics
    5. Finding the minimum information partition
    
    The IIM serves as the foundation for emergent consciousness-like properties
    in the ULTRA system.
    """
    
    def __init__(self, num_subsystems: int = 8, state_dimension: int = 64, 
                optimization_iterations: int = 50, parallel: bool = True):
        """
        Initialize the Integrated Information Matrix.
        
        Args:
            num_subsystems: Number of subsystems to model in the integrated matrix
            state_dimension: Dimension of each subsystem's state representation
            optimization_iterations: Default number of iterations for optimization
            parallel: Whether to use parallel processing for computationally intensive operations
        """
        self.num_subsystems = num_subsystems
        self.state_dimension = state_dimension
        self.optimization_iterations = optimization_iterations
        self.parallel = parallel and cpu_count() > 1
        
        logger.info(f"Initializing Integrated Information Matrix with {num_subsystems} subsystems "
                   f"and {state_dimension} dimensions per state")
        
        # Subsystem states - current state of each subsystem
        self.subsystem_states = np.random.randn(num_subsystems, state_dimension)
        
        # Normalize states to unit length for consistent computations
        for i in range(num_subsystems):
            self.subsystem_states[i] = self._normalize(self.subsystem_states[i])
            
        # Connectivity matrix - connection strengths between subsystems (directed)
        self.connectivity = np.zeros((num_subsystems, num_subsystems))
        
        # Initialize with weak random connections (avoiding self-connections)
        for i in range(num_subsystems):
            for j in range(num_subsystems):
                if i != j:
                    self.connectivity[i, j] = 0.1 * np.random.rand()
                    
        # Information flow matrix - represents directed information flow between subsystems
        self.information_flow = np.zeros((num_subsystems, num_subsystems))
        self._update_information_flow()
        
        # Integrated information (Φ) values for each subsystem pair
        self.phi_values = np.zeros((num_subsystems, num_subsystems))
        
        # System-level Φ (initialized to 0, computed on demand)
        self.system_phi = 0.0
        
        # Subsystem names and descriptions (default naming)
        self.subsystem_names = [f"Subsystem_{i}" for i in range(num_subsystems)]
        
        # History of Φ values for tracking integration over time
        self.phi_history = []
        
        # Subsystem metadata and properties (extended information about each subsystem)
        self.subsystem_metadata = [{
            'id': i,
            'name': f"Subsystem_{i}",
            'description': f"ULTRA Subsystem {i}",
            'creation_time': time.time(),
            'last_modified': time.time(),
            'properties': {}
        } for i in range(num_subsystems)]
        
        # Integration parameters for optimization
        self.integration_params = {
            'learning_rate': 0.1,
            'entropy_weight': 0.5,
            'coherence_weight': 0.3,
            'sparsity_weight': 0.2,
            'momentum': 0.9,
            'adaptivity': 0.01,
            'min_connectivity': 0.01,
            'max_connectivity': 1.0,
            'temperature': 1.0,
            'decay_rate': 0.995
        }
        
        # Previous gradient for momentum calculations
        self.prev_gradient = np.zeros_like(self.connectivity)
        
        # Adaptive learning rates for each connection
        self.adaptive_rates = np.ones_like(self.connectivity) * self.integration_params['learning_rate']
        
        # Last update time
        self.last_update_time = time.time()
        
        # Compute initial system Φ
        self.compute_phi()
        
        # Record initial state in history
        self._record_history()
        
        logger.info(f"Integrated Information Matrix initialized with system Φ = {self.system_phi:.4f}")
        
    def set_subsystem_names(self, names: List[str]) -> None:
        """
        Set custom names for subsystems.
        
        Args:
            names: List of names for subsystems (must match the number of subsystems)
        
        Raises:
            ValueError: If the number of names doesn't match the number of subsystems
        """
        if len(names) != self.num_subsystems:
            raise ValueError(f"Expected {self.num_subsystems} names, got {len(names)}")
            
        self.subsystem_names = names.copy()
        
        # Update metadata
        for i, name in enumerate(names):
            self.subsystem_metadata[i]['name'] = name
            self.subsystem_metadata[i]['last_modified'] = time.time()
            
        logger.info(f"Updated subsystem names: {', '.join(names[:3])}{'...' if len(names) > 3 else ''}")
        
    def set_subsystem_metadata(self, subsystem_idx: int, metadata: Dict[str, Any]) -> None:
        """
        Set metadata for a specific subsystem.
        
        Args:
            subsystem_idx: Index of the subsystem to update
            metadata: Dictionary of metadata properties to update
            
        Raises:
            ValueError: If the subsystem index is out of range
        """
        if subsystem_idx < 0 or subsystem_idx >= self.num_subsystems:
            raise ValueError(f"Subsystem index {subsystem_idx} out of range")
            
        # Update only provided keys, preserving existing ones
        for key, value in metadata.items():
            # Don't override id or creation_time
            if key not in ['id', 'creation_time']:
                self.subsystem_metadata[subsystem_idx][key] = value
                
        # Update last modified time
        self.subsystem_metadata[subsystem_idx]['last_modified'] = time.time()
        
        logger.debug(f"Updated metadata for subsystem {subsystem_idx}")
        
    def update_subsystem_state(self, subsystem_idx: int, new_state: np.ndarray) -> None:
        """
        Update the state of a specific subsystem.
        
        Args:
            subsystem_idx: Index of the subsystem to update
            new_state: New state vector for the subsystem
            
        Raises:
            ValueError: If subsystem index is out of range or state dimensions don't match
        """
        if subsystem_idx < 0 or subsystem_idx >= self.num_subsystems:
            raise ValueError(f"Subsystem index {subsystem_idx} out of range")
            
        if len(new_state) != self.state_dimension:
            raise ValueError(f"Expected state dimension {self.state_dimension}, got {len(new_state)}")
            
        # Normalize state to unit length for consistent computations
        new_state = self._normalize(new_state)
        
        # Check if state is significantly different before updating
        state_diff = np.linalg.norm(new_state - self.subsystem_states[subsystem_idx])
        
        # Update state if different
        if state_diff > 1e-6:
            self.subsystem_states[subsystem_idx] = new_state
            
            # Update information flow as states have changed
            self._update_information_flow()
            
            # Update metadata
            self.subsystem_metadata[subsystem_idx]['last_modified'] = time.time()
            
            logger.debug(f"Updated state for subsystem {subsystem_idx} with diff {state_diff:.6f}")
            
    def _normalize(self, vector: np.ndarray) -> np.ndarray:
        """
        Normalize a vector to unit length to ensure consistent computations.
        
        Args:
            vector: Vector to normalize
            
        Returns:
            Normalized vector (unit length)
        """
        norm = np.linalg.norm(vector)
        if norm > 1e-10:  # Avoid division by near-zero
            return vector / norm
        return np.ones_like(vector) / np.sqrt(len(vector))  # Fallback to uniform unit vector
        
    def update_connection(self, source_idx: int, target_idx: int, strength: float) -> None:
        """
        Update the connection strength between two subsystems.
        
        Args:
            source_idx: Index of source subsystem
            target_idx: Index of target subsystem
            strength: New connection strength (0-1)
            
        Raises:
            ValueError: If indices are out of range or refer to the same subsystem
        """
        if source_idx < 0 or source_idx >= self.num_subsystems:
            raise ValueError(f"Source index {source_idx} out of range")
            
        if target_idx < 0 or target_idx >= self.num_subsystems:
            raise ValueError(f"Target index {target_idx} out of range")
            
        if source_idx == target_idx:
            raise ValueError("Cannot create self-connection (source and target are the same)")
            
        # Clamp strength to valid range [0, 1]
        strength = max(0.0, min(1.0, strength))
        
        # Only update if there's a meaningful change
        if abs(self.connectivity[source_idx, target_idx] - strength) > 1e-6:
            self.connectivity[source_idx, target_idx] = strength
            
            # Update information flow as connectivity has changed
            self._update_information_flow()
            
            logger.debug(f"Updated connection from {source_idx} to {target_idx} with strength {strength:.4f}")
            
    def update_connections(self, connections: List[Tuple[int, int, float]]) -> None:
        """
        Update multiple connections at once.
        
        Args:
            connections: List of (source_idx, target_idx, strength) tuples
            
        Raises:
            ValueError: If any indices are invalid
        """
        # Validate all connections first
        for source_idx, target_idx, _ in connections:
            if source_idx < 0 or source_idx >= self.num_subsystems:
                raise ValueError(f"Source index {source_idx} out of range")
                
            if target_idx < 0 or target_idx >= self.num_subsystems:
                raise ValueError(f"Target index {target_idx} out of range")
                
            if source_idx == target_idx:
                raise ValueError(f"Cannot create self-connection (source and target {source_idx} are the same)")
                
        # Update all connections
        updated = False
        for source_idx, target_idx, strength in connections:
            strength = max(0.0, min(1.0, strength))
            
            if abs(self.connectivity[source_idx, target_idx] - strength) > 1e-6:
                self.connectivity[source_idx, target_idx] = strength
                updated = True
                
        # Update information flow once after all connections are updated
        if updated:
            self._update_information_flow()
            
        logger.debug(f"Updated {len(connections)} connections")
        
    def set_connectivity_matrix(self, connectivity_matrix: np.ndarray) -> None:
        """
        Set the entire connectivity matrix at once.
        
        Args:
            connectivity_matrix: New connectivity matrix (num_subsystems × num_subsystems)
            
        Raises:
            ValueError: If matrix dimensions don't match or contains self-connections
        """
        if connectivity_matrix.shape != (self.num_subsystems, self.num_subsystems):
            raise ValueError(f"Expected matrix shape ({self.num_subsystems}, {self.num_subsystems}), "
                           f"got {connectivity_matrix.shape}")
                           
        # Check for self-connections
        if not np.allclose(np.diag(connectivity_matrix), np.zeros(self.num_subsystems)):
            raise ValueError("Connectivity matrix contains self-connections (non-zero diagonal elements)")
            
        # Ensure values are in [0, 1]
        connectivity_matrix = np.clip(connectivity_matrix, 0.0, 1.0)
        
        # Only update if there are meaningful changes
        if not np.allclose(self.connectivity, connectivity_matrix, atol=1e-6):
            self.connectivity = connectivity_matrix.copy()
            
            # Update information flow
            self._update_information_flow()
            
            logger.info(f"Set new connectivity matrix with avg strength {np.mean(connectivity_matrix[connectivity_matrix > 0]):.4f}")
            
    def _update_information_flow(self) -> None:
        """
        Update the information flow matrix based on current states and connectivity.
        Information flow represents the effective transfer of information between subsystems,
        considering both connection strength and state similarity.
        """
        # Compute state similarity matrix
        similarity_matrix = np.abs(np.dot(self.subsystem_states, self.subsystem_states.T))
        
        # Information flow depends on connection strength and state similarity
        self.information_flow = self.connectivity * similarity_matrix
        
        # Ensure no self-flow
        np.fill_diagonal(self.information_flow, 0.0)
        
    def compute_phi(self) -> float:
        """
        Compute integrated information (Φ) for the entire system using principles from IIT.
        This implementation finds the minimum information partition (MIP) and computes
        the effective information across this partition.
        
        Returns:
            System-level Φ value
        """
        # Step 1: Compute mutual information between all subsystem pairs
        for i in range(self.num_subsystems):
            for j in range(self.num_subsystems):
                if i != j:
                    # Compute Φ for this pair using mutual information and connectivity
                    mi = self._mutual_information(i, j)
                    self.phi_values[i, j] = mi
                    
        # Step 2: Find the minimum information partition (MIP)
        mip, mip_phi = self._find_minimum_information_partition()
        
        # Store system Φ
        self.system_phi = mip_phi
        
        # Record in history
        self._record_history()
        
        logger.debug(f"Computed system Φ = {self.system_phi:.6f}")
        
        return self.system_phi
        
    def _record_history(self) -> None:
        """
        Record current state in the Φ history.
        """
        current_time = time.time()
        
        # Compute average and max phi values
        nonzero_phis = self.phi_values[self.phi_values > 0]
        avg_phi = np.mean(nonzero_phis) if len(nonzero_phis) > 0 else 0.0
        max_phi = np.max(self.phi_values) if np.max(self.phi_values) > 0 else 0.0
        
        self.phi_history.append({
            'timestamp': current_time,
            'phi': self.system_phi,
            'avg_phi': avg_phi,
            'max_phi': max_phi,
            'information_flow_sum': np.sum(self.information_flow),
            'connectivity_density': np.mean(self.connectivity > 0.01),
            'connectivity_strength': np.mean(self.connectivity)
        })
        
    def _mutual_information(self, i: int, j: int) -> float:
        """
        Compute mutual information between two subsystems, a measure of
        shared information content.
        
        Args:
            i: Index of first subsystem
            j: Index of second subsystem
            
        Returns:
            Mutual information value
        """
        # Compute direct information flow in both directions
        flow_ij = self.information_flow[i, j]
        flow_ji = self.information_flow[j, i]
        
        # Compute state similarity
        state_sim = np.abs(np.dot(self.subsystem_states[i], self.subsystem_states[j]))
        
        # Compute a path-based measure of information sharing
        # (information that can flow through other subsystems)
        indirect_paths = 0.0
        for k in range(self.num_subsystems):
            if k != i and k != j:
                indirect_paths += self.information_flow[i, k] * self.information_flow[k, j]
        
        # Scale indirect paths by the number of possible intermediaries
        if self.num_subsystems > 2:
            indirect_paths /= (self.num_subsystems - 2)
        
        # Total information sharing is a combination of direct and indirect flows
        direct_flow = (flow_ij + flow_ji) / 2
        
        # Weight direct flow higher than indirect
        total_flow = 0.8 * direct_flow + 0.2 * indirect_paths
        
        # Adjust for coherence - if flows are asymmetric, reduce MI
        coherence = 1.0 - abs(flow_ij - flow_ji) / max(1e-10, flow_ij + flow_ji)
        
        # Combine factors for final MI calculation
        mi = total_flow * coherence * state_sim
        
        return mi
    
    def _find_minimum_information_partition(self) -> Tuple[Tuple[Set[int], Set[int]], float]:
        """
        Find the minimum information partition (MIP) of the system.
        The MIP is the partition that, when divided, minimizes the effective information
        normalized by partition size.
        
        Returns:
            Tuple containing ((set1, set2), phi_value) where set1 and set2 are the partition elements
        """
        n = self.num_subsystems
        min_phi = float('inf')
        mip = (set(), set())
        
        # For small systems, evaluate all possible bipartitions
        if n <= 20:
            partitions = []
            
            # Generate all possible bipartitions
            for k in range(1, n):
                for subset in itertools.combinations(range(n), k):
                    set1 = set(subset)
                    set2 = set(range(n)) - set1
                    partitions.append((set1, set2))
                    
            # Use parallel processing for larger systems
            if self.parallel and n > 8:
                with Pool(processes=min(cpu_count(), 8)) as pool:
                    phi_values = pool.map(partial(self._evaluate_partition), partitions)
                    
                # Find minimum phi value and corresponding partition
                min_idx = np.argmin(phi_values)
                min_phi = phi_values[min_idx]
                mip = partitions[min_idx]
            else:
                # Sequential processing for smaller systems
                for partition in partitions:
                    phi = self._evaluate_partition(partition)
                    if phi < min_phi:
                        min_phi = phi
                        mip = partition
        else:
            # For larger systems, use a greedy search approach
            mip, min_phi = self._greedy_partition_search()
            
        # If no valid partition found, use the whole system Φ
        if min_phi == float('inf') or not mip[0] or not mip[1]:
            mip = (set(range(n//2)), set(range(n//2, n)))
            min_phi = self._evaluate_partition(mip)
            
        logger.debug(f"Found MIP with Φ = {min_phi:.6f}, sizes: {len(mip[0])}, {len(mip[1])}")
        
        return mip, min_phi
    
    def _evaluate_partition(self, partition: Tuple[Set[int], Set[int]]) -> float:
        """
        Evaluate a partition by computing normalized effective information.
        
        Args:
            partition: Tuple (set1, set2) representing the partition
            
        Returns:
            Normalized Φ value for the partition
        """
        set1, set2 = partition
        
        # Skip invalid partitions
        if not set1 or not set2:
            return float('inf')
            
        # Compute effective information across the partition
        ei = self._compute_effective_information(set1, set2)
        
        # Normalize by the size of the smaller part to penalize uneven partitions
        normalization = min(len(set1), len(set2))
        normalized_phi = ei / normalization if normalization > 0 else float('inf')
        
        return normalized_phi
    
    def _compute_effective_information(self, source_set: Set[int], target_set: Set[int]) -> float:
        """
        Compute effective information from a set of source subsystems to target subsystems.
        This measures the causal influence of the source set on the target set.
        
        Args:
            source_set: Set of source subsystem indices
            target_set: Set of target subsystem indices
            
        Returns:
            Effective information value
        """
        if not source_set or not target_set:
            return 0.0
            
        # Compute total information flow from sources to targets
        total_flow = 0.0
        connection_count = 0
        
        for src in source_set:
            for tgt in target_set:
                if src != tgt:  # Avoid self-connections
                    total_flow += self.information_flow[src, tgt]
                    connection_count += 1
                    
        # Get average flow (if no connections, return 0)
        avg_flow = total_flow / connection_count if connection_count > 0 else 0.0
        
        return avg_flow * len(source_set) * len(target_set)
    
    def _greedy_partition_search(self) -> Tuple[Tuple[Set[int], Set[int]], float]:
        """
        Use a greedy search algorithm to find a good partition for large systems.
        
        Returns:
            Tuple containing ((set1, set2), phi_value) for the best partition found
        """
        n = self.num_subsystems
        best_partition = (set(range(n//2)), set(range(n//2, n)))
        best_phi = self._evaluate_partition(best_partition)
        
        # Start with random initial partition
        current_partition = (set(np.random.choice(n, n//2, replace=False)), 
                           set(range(n)) - best_partition[0])
        current_phi = self._evaluate_partition(current_partition)
        
        if current_phi < best_phi:
            best_partition = current_partition
            best_phi = current_phi
            
        # Perform local search
        improved = True
        max_iterations = min(100, 2**n)
        iteration = 0
        
        while improved and iteration < max_iterations:
            improved = False
            iteration += 1
            
            # Try swapping elements between sets
            for i in current_partition[0]:
                for j in current_partition[1]:
                    # Create new partition by swapping i and j
                    new_set1 = current_partition[0] - {i} | {j}
                    new_set2 = current_partition[1] - {j} | {i}
                    new_partition = (new_set1, new_set2)
                    
                    # Evaluate new partition
                    new_phi = self._evaluate_partition(new_partition)
                    
                    # Update if better
                    if new_phi < current_phi:
                        current_partition = new_partition
                        current_phi = new_phi
                        improved = True
                        
                        if new_phi < best_phi:
                            best_partition = new_partition
                            best_phi = new_phi
                        
                        # Break early if significant improvement found
                        break
                
                if improved:
                    break
                    
        logger.debug(f"Greedy search completed in {iteration} iterations, best Φ = {best_phi:.6f}")
        
        return best_partition, best_phi
    
    def optimize_integration(self, iterations: Optional[int] = None, 
                            learning_rate: Optional[float] = None) -> float:
        """
        Optimize the connectivity matrix to maximize integrated information (Φ).
        This procedure uses gradient-based optimization with several regularization terms
        to find an optimal connectivity pattern.
        
        Args:
            iterations: Number of optimization iterations (defaults to self.optimization_iterations)
            learning_rate: Learning rate for optimization (defaults to integration_params['learning_rate'])
            
        Returns:
            New system Φ value after optimization
        """
        iterations = iterations if iterations is not None else self.optimization_iterations
        
        if learning_rate is not None:
            self.integration_params['learning_rate'] = learning_rate
            self.adaptive_rates = np.ones_like(self.connectivity) * learning_rate
            
        logger.info(f"Starting integration optimization with {iterations} iterations")
        
        initial_phi = self.compute_phi()
        best_phi = initial_phi
        best_connectivity = self.connectivity.copy()
        
        # Initialize optimization variables
        learning_rate = self.integration_params['learning_rate']
        momentum = self.integration_params['momentum']
        adaptivity = self.integration_params['adaptivity']
        temperature = self.integration_params['temperature']
        
        # Initialize progress tracking
        phi_progress = []
        
        for iter_idx in range(iterations):
            # Compute gradient approximation using combined analytic and numerical methods
            grad = self._compute_integration_gradient()
            
            # Apply regularization terms
            entropy_reg = self.integration_params['entropy_weight'] * self._entropy_gradient()
            coherence_reg = self.integration_params['coherence_weight'] * self._coherence_gradient()
            sparsity_reg = self.integration_params['sparsity_weight'] * self._sparsity_gradient()
            
            # Combined gradient
            combined_grad = grad + entropy_reg + coherence_reg + sparsity_reg
            
            # Apply momentum
            update = momentum * self.prev_gradient + (1 - momentum) * combined_grad
            self.prev_gradient = update.copy()
            
            # Apply adaptive learning rates
            self.adaptive_rates = self.adaptive_rates * (1 + adaptivity * np.sign(self.prev_gradient * combined_grad))
            self.adaptive_rates = np.clip(self.adaptive_rates, learning_rate * 0.1, learning_rate * 10)
            
            # Update connectivity with temperature-scaled update
            self.connectivity += update * self.adaptive_rates * temperature
            
            # Ensure values are in [0, 1] and no self-connections
            self.connectivity = np.clip(self.connectivity, 0.0, 1.0)
            np.fill_diagonal(self.connectivity, 0.0)
            
            # Remove very weak connections
            min_conn = self.integration_params['min_connectivity']
            self.connectivity[self.connectivity < min_conn] = 0.0
            
            # Update information flow
            self._update_information_flow()
            
            # Compute new Φ
            new_phi = self.compute_phi()
            phi_progress.append(new_phi)
            
            # Save best connectivity
            if new_phi > best_phi:
                best_phi = new_phi
                best_connectivity = self.connectivity.copy()
                logger.debug(f"Iteration {iter_idx+1}: New best Φ = {best_phi:.6f}")
            
            # Decrease temperature
            temperature *= self.integration_params['decay_rate']
            
            # Early stopping if no improvement for many iterations
            if iter_idx > 10 and iter_idx % 10 == 0:
                recent_improvement = max(phi_progress[-10:]) - phi_progress[-10]
                if recent_improvement < 0.0001 * initial_phi:
                    logger.info(f"Early stopping at iteration {iter_idx+1} due to negligible improvement")
                    break
                    
        # Restore best connectivity
        if best_phi > initial_phi:
            self.connectivity = best_connectivity
            self._update_information_flow()
            final_phi = self.compute_phi()
            
            logger.info(f"Optimization complete: Φ increased from {initial_phi:.6f} to {final_phi:.6f}")
        else:
            logger.info(f"Optimization complete: No improvement over initial Φ = {initial_phi:.6f}")
            final_phi = initial_phi
            
        return final_phi
    
    def _compute_integration_gradient(self) -> np.ndarray:
        """
        Compute gradient for optimizing integration.
        Uses a combination of analytic and numerical approaches.
        
        Returns:
            Gradient matrix for the connectivity
        """
        gradient = np.zeros_like(self.connectivity)
        
        # Analytical component: increase information flow paths
        for i in range(self.num_subsystems):
            for j in range(self.num_subsystems):
                if i != j:
                    # State similarity for these subsystems
                    state_sim = np.abs(np.dot(self.subsystem_states[i], self.subsystem_states[j]))
                    
                    # Flow contribution to system Φ (approximation)
                    flow_contribution = 0.0
                    
                    # Check information flow through this connection to others
                    for k in range(self.num_subsystems):
                        if k != i and k != j:
                            flow_contribution += self.information_flow[j, k]
                            
                    # Gradient is higher for connections that enable more information flow
                    gradient[i, j] = state_sim * flow_contribution
                    
        # Use numerical gradient estimation for a subset of connections
        # This helps explore the landscape more effectively
        num_samples = min(20, self.num_subsystems)
        
        # Sample connections to evaluate numerically
        sample_i = np.random.choice(self.num_subsystems, num_samples)
        sample_j = np.random.choice(self.num_subsystems, num_samples)
        
        # Small perturbation for gradient estimation
        epsilon = 0.01
        
        for i, j in zip(sample_i, sample_j):
            if i != j:
                # Save original value
                orig_val = self.connectivity[i, j]
                
                # Increase connection and measure Φ
                self.connectivity[i, j] = min(1.0, orig_val + epsilon)
                self._update_information_flow()
                phi_plus = self.compute_phi()
                
                # Decrease connection and measure Φ
                self.connectivity[i, j] = max(0.0, orig_val - epsilon)
                self._update_information_flow()
                phi_minus = self.compute_phi()
                
                # Estimate gradient
                numerical_grad = (phi_plus - phi_minus) / (2 * epsilon)
                
                # Restore original value
                self.connectivity[i, j] = orig_val
                
                # Update gradient with numerical estimate
                gradient[i, j] = 0.5 * gradient[i, j] + 0.5 * numerical_grad
                
        # Restore information flow
        self._update_information_flow()
        
        # Normalize gradient to avoid too large updates
        gradient_norm = np.linalg.norm(gradient)
        if gradient_norm > 1e-10:
            gradient = gradient / gradient_norm
            
        return gradient
    
    def _entropy_gradient(self) -> np.ndarray:
        """
        Compute gradient for entropy regularization.
        Encourages more uniform connectivity across subsystems to increase diversity.
        
        Returns:
            Gradient matrix for entropy regularization
        """
        # Compute mean connectivity for each subsystem (outgoing)
        out_conn_mean = np.mean(self.connectivity, axis=1, keepdims=True)
        
        # Gradient pushes connections toward subsystem mean (balancing connections)
        grad = np.zeros_like(self.connectivity)
        
        for i in range(self.num_subsystems):
            # Encourage connections from less connected subsystems
            # and discourage from highly connected ones
            for j in range(self.num_subsystems):
                if i != j:
                    grad[i, j] = out_conn_mean[i] - self.connectivity[i, j]
                    
        # No gradient for diagonal elements (self-connections)
        np.fill_diagonal(grad, 0.0)
        
        return grad
    
    def _coherence_gradient(self) -> np.ndarray:
        """
        Compute gradient for coherence regularization.
        Encourages similar subsystems to have stronger connections and
        dissimilar subsystems to have weaker connections.
        
        Returns:
            Gradient matrix for coherence regularization
        """
        grad = np.zeros_like(self.connectivity)
        
        # Compute state similarities
        state_sim_matrix = np.abs(np.dot(self.subsystem_states, self.subsystem_states.T))
        
        for i in range(self.num_subsystems):
            for j in range(self.num_subsystems):
                if i != j:
                    # Similarity between states
                    sim = state_sim_matrix[i, j]
                    
                    # If states are similar, increase connection
                    # If states are dissimilar, decrease connection
                    target_conn = sim
                    
                    # Gradient pushes connection strength toward target based on similarity
                    grad[i, j] = 2.0 * (target_conn - self.connectivity[i, j])
                    
        return grad
    
    def _sparsity_gradient(self) -> np.ndarray:
        """
        Compute gradient for sparsity regularization.
        Encourages a sparse connectivity pattern with a few strong connections
        rather than many weak ones.
        
        Returns:
            Gradient matrix for sparsity regularization
        """
        # L1 regularization gradient (encourages sparsity)
        l1_grad = -np.sign(self.connectivity)
        
        # L2 regularization component (encourages some connections to be strong)
        l2_grad = -2.0 * self.connectivity
        
        # Combine gradients with weighted average
        grad = 0.7 * l1_grad + 0.3 * l2_grad
        
        # No gradient for diagonal elements
        np.fill_diagonal(grad, 0.0)
        
        # Scale gradient to be similar magnitude to other gradients
        grad_norm = np.linalg.norm(grad)
        if grad_norm > 1e-10:
            grad = grad / grad_norm
            
        return grad
    
    def estimate_conscious_access(self, information: np.ndarray) -> float:
        """
        Estimate the degree to which information has 'conscious access'
        in the integrated system. This is based on its integration with
        the overall information space and system Φ.
        
        Args:
            information: Vector representing the information to be evaluated
            
        Returns:
            Conscious access score (0-1) indicating integration level
            
        Raises:
            ValueError: If information dimension doesn't match state_dimension
        """
        if len(information) != self.state_dimension:
            raise ValueError(f"Expected information dimension {self.state_dimension}, got {len(information)}")
            
        # Normalize information vector
        information = self._normalize(information)
        
        # Compute similarity with each subsystem
        similarities = np.array([np.abs(np.dot(information, self.subsystem_states[i])) 
                              for i in range(self.num_subsystems)])
                              
        # Weight by subsystem importance in the integration network
        # (measured by sum of incoming and outgoing connections)
        in_connectivity = np.sum(self.connectivity, axis=0)
        out_connectivity = np.sum(self.connectivity, axis=1)
        
        # Total connectivity for each subsystem
        total_connectivity = in_connectivity + out_connectivity
        
        # Normalize weights
        weights = total_connectivity / np.sum(total_connectivity) if np.sum(total_connectivity) > 0 else \
                 np.ones(self.num_subsystems) / self.num_subsystems
                 
        # Compute weighted similarity
        weighted_sim = np.dot(similarities, weights)
        
        # Scale by system Φ - higher system integration means more effective access
        access_score = weighted_sim * (self.system_phi / (self.system_phi + 1.0))
        
        # Ensure result is in [0, 1]
        return max(0.0, min(1.0, access_score))
    
    def propagate_information(self, source_idx: int, 
                             information: np.ndarray) -> Dict[int, np.ndarray]:
        """
        Propagate information from a source subsystem to connected subsystems.
        Models how information flows through the integrated network.
        
        Args:
            source_idx: Index of source subsystem
            information: Information vector to propagate
            
        Returns:
            Dictionary mapping subsystem indices to received information vectors
            
        Raises:
            ValueError: If source_idx is invalid or information dimension is wrong
        """
        if source_idx < 0 or source_idx >= self.num_subsystems:
            raise ValueError(f"Source index {source_idx} out of range")
            
        if len(information) != self.state_dimension:
            raise ValueError(f"Expected information dimension {self.state_dimension}, got {len(information)}")
            
        # Normalize information
        information = self._normalize(information)
        
        # Update source subsystem state
        original_state = self.subsystem_states[source_idx].copy()
        self.subsystem_states[source_idx] = information
        
        # Track the propagated information to each target
        received_info = {}
        
        # First-order propagation - direct connections from source
        for target_idx in range(self.num_subsystems):
            if target_idx != source_idx:
                # Amount of information transferred depends on connection strength
                transfer_strength = self.connectivity[source_idx, target_idx]
                
                if transfer_strength > 0:
                    # Save original target state for later
                    original_target_state = self.subsystem_states[target_idx].copy()
                    
                    # Compute transferred information
                    transferred = transfer_strength * information
                    
                    # Combine with existing state
                    combined = (1.0 - transfer_strength) * original_target_state + transferred
                    
                    # Normalize
                    combined = self._normalize(combined)
                    
                    # Update target state
                    self.subsystem_states[target_idx] = combined
                    
                    # Record received information
                    received_info[target_idx] = transferred
        
        # Second-order propagation - information passing through intermediaries
        # This models the "echoing" of information through the network
        second_order_received = {}
        
        for intermediate_idx in range(self.num_subsystems):
            if intermediate_idx != source_idx and intermediate_idx in received_info:
                # This subsystem received information from the source
                for target_idx in range(self.num_subsystems):
                    if target_idx != source_idx and target_idx != intermediate_idx:
                        # Transfer from intermediate to target
                        relay_strength = self.connectivity[intermediate_idx, target_idx] * 0.5  # Reduced strength for relayed info
                        
                        if relay_strength > 0:
                            # Compute relayed information
                            relayed_info = relay_strength * received_info[intermediate_idx]
                            
                            # Add to second-order received info
                            if target_idx in second_order_received:
                                second_order_received[target_idx] += relayed_info
                            else:
                                second_order_received[target_idx] = relayed_info
        
        # Apply second-order updates
        for target_idx, relayed_info in second_order_received.items():
            # Only apply if not already directly received
            if target_idx not in received_info:
                # Normalize relayed info
                relayed_info = self._normalize(relayed_info)
                
                # Compute influence strength (weaker than direct)
                influence_strength = np.linalg.norm(relayed_info) * 0.3
                
                # Combine with existing state
                combined = (1.0 - influence_strength) * self.subsystem_states[target_idx] + influence_strength * relayed_info
                
                # Normalize
                combined = self._normalize(combined)
                
                # Update target state
                self.subsystem_states[target_idx] = combined
                
                # Record received information
                received_info[target_idx] = relayed_info
                
        # Update information flow matrix after all propagation
        self._update_information_flow()
        
        return received_info
    
    def compute_effective_information(self, sources: Set[int], 
                                     targets: Set[int]) -> float:
        """
        Compute effective information from a set of source subsystems
        to a set of target subsystems. This measures the causal influence
        between subsystem groups.
        
        Args:
            sources: Set of source subsystem indices
            targets: Set of target subsystem indices
            
        Returns:
            Effective information value
            
        Raises:
            ValueError: If any subsystem index is invalid
        """
        if not sources or not targets:
            return 0.0
            
        # Check indices
        for idx in sources.union(targets):
            if idx < 0 or idx >= self.num_subsystems:
                raise ValueError(f"Subsystem index {idx} out of range")
                
        # Compute average information flow from sources to targets
        total_flow = 0.0
        connection_count = 0
        
        for src in sources:
            for tgt in targets:
                if src != tgt:
                    total_flow += self.information_flow[src, tgt]
                    connection_count += 1
                    
        # Compute average flow
        if connection_count > 0:
            avg_flow = total_flow / connection_count
        else:
            avg_flow = 0.0
            
        # Scale by sizes of both sets
        effective_info = avg_flow * len(sources) * len(targets)
        
        logger.debug(f"Effective information from {sources} to {targets}: {effective_info:.6f}")
        
        return effective_info
    
    def find_minimum_information_partition(self) -> Tuple[Set[int], Set[int], float]:
        """
        Find the minimum information partition (MIP) of the system.
        The MIP is the partition that minimizes normalized integrated information.
        This is a key concept from IIT.
        
        Returns:
            Tuple containing (set1, set2, phi_value) representing the partition
            and its corresponding phi value
        """
        mip, phi_value = self._find_minimum_information_partition()
        return (mip[0], mip[1], phi_value)
    
    def measure_causal_density(self) -> float:
        """
        Compute the causal density of the network, measuring
        the overall causal interactions between subsystems.
        
        Returns:
            Causal density value (0-1)
        """
        # Count significant connections (above threshold)
        threshold = 0.05
        significant_connections = np.sum(self.information_flow > threshold)
        
        # Maximum possible connections
        max_connections = self.num_subsystems * (self.num_subsystems - 1)
        
        # Compute density
        if max_connections > 0:
            density = significant_connections / max_connections
        else:
            density = 0.0
            
        logger.debug(f"Causal density: {density:.4f} ({significant_connections} significant connections)")
        
        return density
    
    def measure_integration_complexity(self) -> float:
        """
        Compute the integration complexity of the system, measuring
        how the system balances integration and segregation.
        
        Returns:
            Integration complexity value
        """
        # Create a similarity/covariance matrix based on information flow
        flow_matrix = self.information_flow + self.information_flow.T
        np.fill_diagonal(flow_matrix, 1.0)  # Self-similarity
        
        try:
            # Compute determinant-based complexity
            whole_entropy = 0.5 * np.log(max(1e-10, np.linalg.det(flow_matrix)))
            
            # Average entropy of individual subsystems
            individual_entropy = 0.5 * np.mean(np.log(np.diagonal(flow_matrix)))
            
            # Integration complexity
            complexity = whole_entropy - individual_entropy
            
            # Ensure non-negative
            complexity = max(0.0, complexity)
            
            # Normalize by system size
            complexity = complexity / self.num_subsystems
            
        except np.linalg.LinAlgError:
            # Fallback if determinant calculation fails
            complexity = 0.0
            
        logger.debug(f"Integration complexity: {complexity:.6f}")
        
        return complexity
    
    def compute_information_integration_profile(self) -> Dict[str, float]:
        """
        Compute a comprehensive information integration profile
        with multiple metrics that characterize the integrated system.
        
        Returns:
            Dictionary of integration metrics
        """
        # Compute various integration metrics
        metrics = {
            'phi': self.system_phi,
            'causal_density': self.measure_causal_density(),
            'integration_complexity': self.measure_integration_complexity(),
            'connectivity_density': np.mean(self.connectivity > 0.01),
            'avg_connectivity': np.mean(self.connectivity),
            'information_flow_total': np.sum(self.information_flow),
            'information_flow_avg': np.mean(self.information_flow),
            'integration_asymmetry': self._compute_integration_asymmetry(),
            'modularity': self._compute_modularity(),
            'hub_centrality': self._compute_hub_centrality(),
            'small_world_index': self._compute_small_world_index()
        }
        
        logger.info(f"Computed information integration profile: phi={metrics['phi']:.4f}, "
                  f"complexity={metrics['integration_complexity']:.4f}, "
                  f"density={metrics['causal_density']:.4f}")
        
        return metrics
    
    def _compute_integration_asymmetry(self) -> float:
        """
        Compute asymmetry in information flow, indicating
        directional biases in the integrated system.
        
        Returns:
            Asymmetry value (0-1)
        """
        # Compute difference between information flow directions
        flow_diff = np.abs(self.information_flow - self.information_flow.T)
        
        # Normalize by total flow
        total_flow = np.sum(self.information_flow) + np.sum(self.information_flow.T)
        
        if total_flow > 1e-10:
            asymmetry = np.sum(flow_diff) / total_flow
        else:
            asymmetry = 0.0
            
        return asymmetry
    
    def _compute_modularity(self) -> float:
        """
        Compute modularity of the information flow network,
        measuring the degree to which it divides into communities.
        
        Returns:
            Modularity value
        """
        # Create a NetworkX graph from the information flow matrix
        G = nx.DiGraph()
        
        # Add nodes and edges
        for i in range(self.num_subsystems):
            G.add_node(i)
            
        for i in range(self.num_subsystems):
            for j in range(self.num_subsystems):
                if i != j and self.information_flow[i, j] > 0.01:
                    G.add_edge(i, j, weight=self.information_flow[i, j])
        
        try:
            # Find communities using the Louvain method
            import community as community_louvain
            
            # Convert to undirected for community detection
            G_undirected = G.to_undirected()
            
            # Compute communities
            partition = community_louvain.best_partition(G_undirected)
            
            # Compute modularity
            modularity = community_louvain.modularity(partition, G_undirected)
            
        except ImportError:
            # Fallback: use a simpler approximation
            modularity = 0.0
            
            # Compute a basic clustering coefficient as an approximation
            if G.number_of_edges() > 0:
                try:
                    modularity = nx.transitivity(G)
                except:
                    # Further fallback
                    modularity = 0.0
        
        return modularity
    
    def _compute_hub_centrality(self) -> float:
        """
        Compute the hub centrality of the information flow network,
        measuring how much the network is organized around key hubs.
        
        Returns:
            Hub centrality value
        """
        # Create a NetworkX graph from the information flow matrix
        G = nx.DiGraph()
        
        # Add nodes and edges
        for i in range(self.num_subsystems):
            G.add_node(i)
            
        for i in range(self.num_subsystems):
            for j in range(self.num_subsystems):
                if i != j and self.information_flow[i, j] > 0.01:
                    G.add_edge(i, j, weight=self.information_flow[i, j])
        
        try:
            # Compute eigenvector centrality
            centrality = nx.eigenvector_centrality(G, weight='weight', max_iter=1000)
            
            # Compute variance of centrality as a measure of hub organization
            centrality_values = list(centrality.values())
            if len(centrality_values) > 1:
                hub_centrality = np.var(centrality_values) * self.num_subsystems
            else:
                hub_centrality = 0.0
                
        except:
            # Fallback: use a simpler approach
            in_degrees = np.sum(self.information_flow > 0.01, axis=0)
            out_degrees = np.sum(self.information_flow > 0.01, axis=1)
            
            total_degrees = in_degrees + out_degrees
            if len(total_degrees) > 1:
                hub_centrality = np.var(total_degrees) / np.mean(total_degrees) if np.mean(total_degrees) > 0 else 0.0
            else:
                hub_centrality = 0.0
        
        return hub_centrality
    
    def _compute_small_world_index(self) -> float:
        """
        Compute the small-world index of the information flow network,
        measuring how efficiently the network connects subsystems.
        
        Returns:
            Small world index value
        """
        # Create a NetworkX graph from the information flow matrix
        G = nx.DiGraph()
        
        # Add nodes and edges
        for i in range(self.num_subsystems):
            G.add_node(i)
            
        for i in range(self.num_subsystems):
            for j in range(self.num_subsystems):
                if i != j and self.information_flow[i, j] > 0.01:
                    G.add_edge(i, j, weight=self.information_flow[i, j])
        
        try:
            # Convert to undirected for small-world calculation
            G_undirected = G.to_undirected()
            
            # Compute clustering coefficient
            clustering = nx.average_clustering(G_undirected)
            
            # Compute average shortest path length
            if nx.is_connected(G_undirected):
                path_length = nx.average_shortest_path_length(G_undirected)
            else:
                # Handle disconnected graphs by computing over largest component
                largest_cc = max(nx.connected_components(G_undirected), key=len)
                path_length = nx.average_shortest_path_length(G_undirected.subgraph(largest_cc))
            
            # Generate an equivalent random graph
            random_G = nx.gnm_random_graph(G.number_of_nodes(), G.number_of_edges())
            
            # Compute metrics for random graph
            random_clustering = nx.average_clustering(random_G)
            
            if nx.is_connected(random_G):
                random_path_length = nx.average_shortest_path_length(random_G)
            else:
                largest_cc = max(nx.connected_components(random_G), key=len)
                random_path_length = nx.average_shortest_path_length(random_G.subgraph(largest_cc))
            
            # Compute small-world index
            if random_clustering > 0 and random_path_length > 0:
                small_world_index = (clustering / random_clustering) / (path_length / random_path_length)
            else:
                small_world_index = 0.0
                
        except:
            # Fallback: use a simpler approximation
            small_world_index = 0.0
            
            # Compute a basic approximation based on connectivity pattern
            avg_connections = np.mean(np.sum(self.information_flow > 0.01, axis=1))
            if avg_connections > 1 and self.num_subsystems > 2:
                small_world_index = avg_connections / np.log(self.num_subsystems)
        
        return small_world_index
    
    def visualize_integration_network(self, show_labels: bool = True, 
                                     min_edge_weight: float = 0.05, 
                                     filename: Optional[str] = None) -> None:
        """
        Visualize the integrated information network as a directed graph.
        
        Args:
            show_labels: Whether to show subsystem labels
            min_edge_weight: Minimum edge weight to display
            filename: If provided, save the visualization to this file instead of displaying
            
        Raises:
            ImportError: If required visualization libraries are not installed
        """
        try:
            # Create directed graph
            G = nx.DiGraph()
            
            # Add nodes
            for i in range(self.num_subsystems):
                G.add_node(i, name=self.subsystem_names[i])
                
            # Add edges with weights
            edges = []
            weights = []
            
            for i in range(self.num_subsystems):
                for j in range(self.num_subsystems):
                    if i != j and self.connectivity[i, j] > min_edge_weight:
                        G.add_edge(i, j, weight=self.connectivity[i, j])
                        edges.append((i, j))
                        weights.append(self.connectivity[i, j])
                        
            # Create position layout
            pos = nx.spring_layout(G, seed=42)
            
            # Create plot
            plt.figure(figsize=(12, 10))
            
            # Draw nodes with size proportional to their integrated information
            node_sizes = []
            node_colors = []
            
            for i in range(self.num_subsystems):
                # Node size based on total information flow
                flow = np.sum(self.information_flow[i, :]) + np.sum(self.information_flow[:, i])
                node_sizes.append(300 + 2000 * flow)
                
                # Node color based on net information flow (source vs sink)
                net_flow = np.sum(self.information_flow[i, :]) - np.sum(self.information_flow[:, i])
                # Map to color: sources are red, sinks are blue, balanced nodes are purple
                if net_flow > 0:
                    # Source: more outgoing than incoming
                    node_colors.append((min(1.0, 0.5 + net_flow), 0.0, max(0.0, 0.5 - net_flow)))
                else:
                    # Sink: more incoming than outgoing
                    node_colors.append((max(0.0, 0.5 + net_flow), 0.0, min(1.0, 0.5 - net_flow)))
                    
            # Draw nodes
            nx.draw_networkx_nodes(G, pos, node_size=node_sizes, node_color=node_colors, alpha=0.8)
            
            # Draw edges with varying width and alpha based on weight
            if edges:
                # Normalize weights for visualization
                max_weight = max(weights)
                edge_widths = [w * 5 / max_weight for w in weights]
                edge_alphas = [max(0.2, min(0.9, w)) for w in weights]
                
                # Create a list of edges for drawing
                edge_list = list(zip(edges, edge_widths, edge_alphas))
                
                # Draw edges
                for (u, v), width, alpha in edge_list:
                    nx.draw_networkx_edges(
                        G, pos,
                        edgelist=[(u, v)],
                        width=width,
                        alpha=alpha,
                        edge_color='gray',
                        arrows=True,
                        arrowsize=10 + width * 2,
                        arrowstyle='-|>',
                        connectionstyle='arc3,rad=0.1'
                    )
                    
            # Draw labels if requested
            if show_labels:
                nx.draw_networkx_labels(G, pos, font_size=10, font_family='sans-serif',
                                     labels={i: G.nodes[i]['name'] for i in G.nodes})
                                     
            # Add title
            plt.title(f'Integrated Information Network (Φ = {self.system_phi:.4f})')
            plt.axis('off')
            plt.tight_layout()
            
            # Save or display
            if filename:
                plt.savefig(filename, dpi=300, bbox_inches='tight')
                logger.info(f"Saved network visualization to {filename}")
            else:
                plt.show()
                
        except ImportError as e:
            logger.error(f"Visualization libraries not available: {e}")
            raise ImportError(f"Required visualization libraries not installed: {e}")
            
    def visualize_phi_history(self, filename: Optional[str] = None) -> None:
        """
        Visualize the history of Φ values to track integration changes over time.
        
        Args:
            filename: If provided, save the visualization to this file instead of displaying
            
        Raises:
            ValueError: If no history is available
        """
        if not self.phi_history:
            logger.warning("No Φ history available for visualization")
            raise ValueError("No Φ history available")
            
        # Extract timestamps and Φ values
        timestamps = [entry['timestamp'] for entry in self.phi_history]
        phi_values = [entry['phi'] for entry in self.phi_history]
        
        # Convert timestamps to relative times (hours since first entry)
        start_time = timestamps[0]
        rel_times = [(t - start_time) / 3600 for t in timestamps]
        
        # Extract additional metrics if available
        avg_phis = []
        max_phis = []
        info_flows = []
        
        for entry in self.phi_history:
            if 'avg_phi' in entry:
                avg_phis.append(entry['avg_phi'])
            if 'max_phi' in entry:
                max_phis.append(entry['max_phi'])
            if 'information_flow_sum' in entry:
                info_flows.append(entry['information_flow_sum'])
                
        # Create plot
        plt.figure(figsize=(12, 8))
        
        # Plot system Φ
        plt.subplot(2, 1, 1)
        plt.plot(rel_times, phi_values, 'o-', linewidth=2, color='purple', label='System Φ')
        
        # Plot additional metrics if available
        if avg_phis:
            plt.plot(rel_times, avg_phis, '--', color='blue', alpha=0.7, label='Avg Φ')
        if max_phis:
            plt.plot(rel_times, max_phis, ':', color='red', alpha=0.7, label='Max Φ')
            
        plt.xlabel('Time (hours)')
        plt.ylabel('Integrated Information (Φ)')
        plt.title('Evolution of Integrated Information')
        plt.grid(True, linestyle='--', alpha=0.7)
        plt.legend()
        
        # Plot information flow in second subplot
        plt.subplot(2, 1, 2)
        
        if info_flows:
            plt.plot(rel_times, info_flows, 'o-', linewidth=2, color='teal', label='Information Flow')
            plt.xlabel('Time (hours)')
            plt.ylabel('Total Information Flow')
            plt.title('Evolution of Information Flow')
            plt.grid(True, linestyle='--', alpha=0.7)
            plt.legend()
            
        plt.tight_layout()
        
        # Save or display
        if filename:
            plt.savefig(filename, dpi=300, bbox_inches='tight')
            logger.info(f"Saved Φ history visualization to {filename}")
        else:
            plt.show()
            
    def visualize_subsystem_integration(self, filename: Optional[str] = None) -> None:
        """
        Visualize integration levels for each subsystem to identify key integrators.
        
        Args:
            filename: If provided, save the visualization to this file instead of displaying
        """
        # Compute integration level for each subsystem
        integration_levels = []
        in_connections = []
        out_connections = []
        
        for i in range(self.num_subsystems):
            # Incoming connections
            in_conn = np.sum(self.connectivity[:, i])
            in_connections.append(in_conn)
            
            # Outgoing connections
            out_conn = np.sum(self.connectivity[i, :])
            out_connections.append(out_conn)
            
            # Total integration level
            integration = (in_conn + out_conn) / (2 * (self.num_subsystems - 1))
            integration_levels.append(integration)
            
        # Create bar plot
        plt.figure(figsize=(14, 8))
        
        # Main integration plot
        plt.subplot(2, 1, 1)
        bars = plt.bar(range(self.num_subsystems), integration_levels, color='teal', alpha=0.7)
        
        # Add value labels
        for i, bar in enumerate(bars):
            height = bar.get_height()
            plt.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                   f'{integration_levels[i]:.2f}',
                   ha='center', va='bottom', fontsize=8)
                   
        plt.xlabel('Subsystem')
        plt.ylabel('Integration Level')
        plt.title('Subsystem Integration Levels')
        
        plt.xticks(range(self.num_subsystems), self.subsystem_names, rotation=45, ha='right')
        plt.ylim(0, max(integration_levels) * 1.2)
        
        # In/Out connection plot
        plt.subplot(2, 1, 2)
        x = np.arange(self.num_subsystems)
        width = 0.35
        
        plt.bar(x - width/2, in_connections, width, label='Incoming', color='darkblue', alpha=0.7)
        plt.bar(x + width/2, out_connections, width, label='Outgoing', color='darkred', alpha=0.7)
        
        plt.xlabel('Subsystem')
        plt.ylabel('Connection Strength')
        plt.title('Incoming vs Outgoing Connections')
        plt.xticks(range(self.num_subsystems), self.subsystem_names, rotation=45, ha='right')
        plt.legend()
        
        plt.tight_layout()
        
        # Save or display
        if filename:
            plt.savefig(filename, dpi=300, bbox_inches='tight')
            logger.info(f"Saved subsystem integration visualization to {filename}")
        else:
            plt.show()
            
    def visualize_integration_metrics(self, filename: Optional[str] = None) -> None:
        """
        Visualize multiple integration metrics to provide a comprehensive view
        of the system's integration profile.
        
        Args:
            filename: If provided, save the visualization to this file instead of displaying
        """
        # Compute integration metrics
        metrics = self.compute_information_integration_profile()
        
        # Select key metrics to visualize
        key_metrics = [
            'phi', 'causal_density', 'integration_complexity',
            'connectivity_density', 'information_flow_avg',
            'integration_asymmetry', 'modularity', 'small_world_index'
        ]
        
        # Filter available metrics
        available_metrics = [(k, metrics[k]) for k in key_metrics if k in metrics]
        
        if not available_metrics:
            logger.warning("No metrics available for visualization")
            return
            
        # Create radar plot
        labels = [k.replace('_', ' ').title() for k, _ in available_metrics]
        values = [v for _, v in available_metrics]
        
        # Normalize values to 0-1 range for radar plot
        max_vals = {
            'phi': 1.0,
            'causal_density': 1.0,
            'integration_complexity': 1.0,
            'connectivity_density': 1.0,
            'information_flow_avg': 1.0,
            'integration_asymmetry': 1.0,
            'modularity': 1.0,
            'small_world_index': 5.0,
            'hub_centrality': 5.0
        }
        
        normalized_values = []
        for (k, v) in available_metrics:
            if k in max_vals:
                normalized_values.append(min(1.0, v / max_vals[k]))
            else:
                normalized_values.append(min(1.0, v))
                
        # Set up the radar plot
        angles = np.linspace(0, 2*np.pi, len(labels), endpoint=False).tolist()
        
        # Close the loop
        values = normalized_values + [normalized_values[0]]
        angles = angles + [angles[0]]
        labels = labels + [labels[0]]
        
        # Create plot
        plt.figure(figsize=(10, 10))
        ax = plt.subplot(111, polar=True)
        
        # Draw the outline
        ax.plot(angles, values, 'o-', linewidth=2, color='blue')
        ax.fill(angles, values, alpha=0.25, color='blue')
        
        # Set category labels
        ax.set_xticks(angles[:-1])
        ax.set_xticklabels(labels[:-1])
        
        # Set radial ticks
        ax.set_yticks([0.2, 0.4, 0.6, 0.8, 1.0])
        ax.set_yticklabels(['0.2', '0.4', '0.6', '0.8', '1.0'])
        
        # Add title
        plt.title('Information Integration Profile', size=15)
        
        # Add a legend with actual values
        legend_text = "\n".join([f"{k.replace('_', ' ').title()}: {v:.4f}" for k, v in metrics.items()])
        plt.figtext(1.0, 0.5, legend_text, fontsize=10, wrap=True)
        
        plt.tight_layout()
        
        # Save or display
        if filename:
            plt.savefig(filename, dpi=300, bbox_inches='tight')
            logger.info(f"Saved integration metrics visualization to {filename}")
        else:
            plt.show()
            
    def save_state(self, filepath: str) -> None:
        """
        Save the current state to a file for later restoration.
        
        Args:
            filepath: Path to save the state
            
        Raises:
            IOError: If unable to write to the file
        """
        state = {
            'subsystem_states': self.subsystem_states.tolist(),
            'connectivity': self.connectivity.tolist(),
            'information_flow': self.information_flow.tolist(),
            'phi_values': self.phi_values.tolist(),
            'system_phi': float(self.system_phi),
            'subsystem_names': self.subsystem_names,
            'subsystem_metadata': self.subsystem_metadata,
            'integration_params': self.integration_params,
            'phi_history': self.phi_history,
            'last_update_time': self.last_update_time,
            'num_subsystems': self.num_subsystems,
            'state_dimension': self.state_dimension,
            'version': '1.0'
        }
        
        try:
            with open(filepath, 'w') as f:
                json.dump(state, f, indent=2)
                
            logger.info(f"Saved state to {filepath}")
            
        except IOError as e:
            logger.error(f"Failed to save state to {filepath}: {e}")
            raise IOError(f"Failed to save state: {e}")
            
    def load_state(self, filepath: str) -> None:
        """
        Load state from a file.
        
        Args:
            filepath: Path to load the state from
            
        Raises:
            IOError: If unable to read from the file
            ValueError: If state file is invalid or incompatible
        """
        try:
            with open(filepath, 'r') as f:
                state = json.load(f)
                
            # Check version compatibility if available
            if 'version' in state:
                version = state['version']
                if version != '1.0':
                    logger.warning(f"Loading state from different version: {version}")
                    
            # Load system dimensions
            if 'num_subsystems' in state and 'state_dimension' in state:
                if state['num_subsystems'] != self.num_subsystems or state['state_dimension'] != self.state_dimension:
                    logger.warning(f"Dimensionality mismatch: saved state has {state['num_subsystems']} subsystems "
                                 f"with {state['state_dimension']} dimensions")
                    
                    # Reinitialize with new dimensions
                    self.__init__(state['num_subsystems'], state['state_dimension'], 
                                self.optimization_iterations, self.parallel)
                    
            # Load state vectors and matrices
            self.subsystem_states = np.array(state['subsystem_states'])
            self.connectivity = np.array(state['connectivity'])
            self.information_flow = np.array(state['information_flow'])
            self.phi_values = np.array(state['phi_values'])
            
            # Load scalar values
            self.system_phi = state['system_phi']
            self.subsystem_names = state['subsystem_names']
            
            # Load metadata if available
            if 'subsystem_metadata' in state:
                self.subsystem_metadata = state['subsystem_metadata']
            else:
                # Create default metadata
                self.subsystem_metadata = [{
                    'id': i,
                    'name': name,
                    'description': f"ULTRA Subsystem {i}",
                    'creation_time': time.time(),
                    'last_modified': time.time(),
                    'properties': {}
                } for i, name in enumerate(self.subsystem_names)]
                
            # Load parameters
            if 'integration_params' in state:
                self.integration_params = state['integration_params']
                
            # Load history if available
            if 'phi_history' in state:
                self.phi_history = state['phi_history']
                
            # Load timestamp
            if 'last_update_time' in state:
                self.last_update_time = state['last_update_time']
            else:
                self.last_update_time = time.time()
                
            # Update derived values
            self.num_subsystems = self.subsystem_states.shape[0]
            self.state_dimension = self.subsystem_states.shape[1]
            
            # Reset optimization variables
            self.prev_gradient = np.zeros_like(self.connectivity)
            self.adaptive_rates = np.ones_like(self.connectivity) * self.integration_params['learning_rate']
            
            logger.info(f"Loaded state from {filepath} with {self.num_subsystems} subsystems and Φ = {self.system_phi:.4f}")
            
        except IOError as e:
            logger.error(f"Failed to load state from {filepath}: {e}")
            raise IOError(f"Failed to load state: {e}")
            
        except (json.JSONDecodeError, KeyError, ValueError) as e:
            logger.error(f"Invalid state file {filepath}: {e}")
            raise ValueError(f"Invalid state file: {e}")


class InformationIntegrationTheory:
    """
    Implementation of core concepts from Integrated Information Theory (IIT)
    for rigorous analysis of information integration in complex systems.
    
    This class provides a more formal implementation of IIT concepts and
    can be used for theoretical analysis or as a complement to the main 
    IntegratedInformationMatrix implementation.
    """
    
    def __init__(self, num_elements: int, state_dimension: int = 2):
        """
        Initialize the IIT system.
        
        Args:
            num_elements: Number of elements in the system
            state_dimension: Dimension of each element's state space (discrete states)
        """
        self.num_elements = num_elements
        self.state_dimension = state_dimension
        
        logger.info(f"Initializing IIT system with {num_elements} elements "
                   f"and {state_dimension} state dimensions")
        
        # Probability distributions for each element
        self.probability_distributions = [
            np.ones(state_dimension) / state_dimension 
            for _ in range(num_elements)
        ]
        
        # Transition probability matrices (TPMs)
        # P(next state | current state) for each element
        self.transition_matrices = [
            np.ones((state_dimension, state_dimension)) / state_dimension
            for _ in range(num_elements)
        ]
        
        # Causal connections between elements
        self.causal_connections = np.zeros((num_elements, num_elements))
        
        # System state (current state of each element)
        self.current_state = np.zeros(num_elements, dtype=int)
        
        # System-level integrated information (Φ)
        self.phi = 0.0
        
        # Minimum Information Partition (MIP)
        self.mip = None
        
        # Set of concepts (Φ-atomic mechanisms)
        self.concepts = []
        
    def set_probability_distribution(self, element_idx: int, 
                                    distribution: np.ndarray) -> None:
        """
        Set the probability distribution for an element.
        
        Args:
            element_idx: Index of the element
            distribution: Probability distribution over states
            
        Raises:
            ValueError: If element_idx is invalid or distribution is not valid
        """
        if element_idx < 0 or element_idx >= self.num_elements:
            raise ValueError(f"Element index {element_idx} out of range")
            
        if len(distribution) != self.state_dimension:
            raise ValueError(f"Expected distribution dimension {self.state_dimension}, got {len(distribution)}")
            
        if not np.isclose(np.sum(distribution), 1.0):
            raise ValueError("Distribution must sum to 1.0")
            
        self.probability_distributions[element_idx] = distribution
        
        logger.debug(f"Set probability distribution for element {element_idx}")
        
    def set_transition_matrix(self, element_idx: int, matrix: np.ndarray) -> None:
        """
        Set the transition probability matrix for an element.
        
        Args:
            element_idx: Index of the element
            matrix: Transition probability matrix
            
        Raises:
            ValueError: If element_idx is invalid or matrix is not valid
        """
        if element_idx < 0 or element_idx >= self.num_elements:
            raise ValueError(f"Element index {element_idx} out of range")
            
        if matrix.shape != (self.state_dimension, self.state_dimension):
            raise ValueError(f"Expected matrix shape {(self.state_dimension, self.state_dimension)}, got {matrix.shape}")
            
        # Check that each row sums to 1
        if not np.allclose(np.sum(matrix, axis=1), np.ones(self.state_dimension)):
            raise ValueError("Each row of transition matrix must sum to 1.0")
            
        self.transition_matrices[element_idx] = matrix
        
        logger.debug(f"Set transition matrix for element {element_idx}")
        
    def set_causal_connection(self, source_idx: int, target_idx: int, 
                             strength: float) -> None:
        """
        Set the causal connection strength between two elements.
        
        Args:
            source_idx: Index of source element
            target_idx: Index of target element
            strength: Connection strength (0-1)
            
        Raises:
            ValueError: If indices are invalid
        """
        if source_idx < 0 or source_idx >= self.num_elements:
            raise ValueError(f"Source index {source_idx} out of range")
            
        if target_idx < 0 or target_idx >= self.num_elements:
            raise ValueError(f"Target index {target_idx} out of range")
            
        self.causal_connections[source_idx, target_idx] = max(0.0, min(1.0, strength))
        
        logger.debug(f"Set causal connection from {source_idx} to {target_idx} with strength {strength:.4f}")
        
    def generate_system_tpm(self) -> np.ndarray:
        """
        Generate the system-level transition probability matrix (TPM)
        based on element TPMs and causal connections.
        
        Returns:
            System-level TPM
        """
        # For a system with n elements, each with s states,
        # the system TPM has s^n × s^n elements
        num_system_states = self.state_dimension ** self.num_elements
        system_tpm = np.zeros((num_system_states, num_system_states))
        
        # Generate all possible state combinations
        state_combinations = list(itertools.product(range(self.state_dimension), repeat=self.num_elements))
        
        # For each current state and potential next state
        for i, current_state in enumerate(state_combinations):
            for j, next_state in enumerate(state_combinations):
                # Probability of this transition
                prob = 1.0
                
                for elem_idx, (curr_elem_state, next_elem_state) in enumerate(zip(current_state, next_state)):
                    # Base transition probability for this element
                    elem_prob = self.transition_matrices[elem_idx][curr_elem_state, next_elem_state]
                    
                    # Adjust based on causal influences from other elements
                    for source_idx, source_state in enumerate(current_state):
                        if source_idx != elem_idx and self.causal_connections[source_idx, elem_idx] > 0:
                            # Compute influence from source to target
                            influence = self.causal_connections[source_idx, elem_idx]
                            
                            # Modify transition probability based on influence
                            # (simplified model of causal influence)
                            source_effect = 0.0
                            for potential_next in range(self.state_dimension):
                                source_effect += self.transition_matrices[source_idx][source_state, potential_next] * \
                                              (1.0 if potential_next == next_elem_state else 0.0)
                                
                            # Blend original probability with influenced probability
                            elem_prob = (1.0 - influence) * elem_prob + influence * source_effect
                            
                    # Accumulate probability for this element's transition
                    prob *= elem_prob
                    
                # Set system TPM entry
                system_tpm[i, j] = prob
                
        return system_tpm
        
    def update_state(self) -> np.ndarray:
        """
        Update the system state based on transition probabilities and causal connections.
        
        Returns:
            New system state
        """
        new_state = np.zeros(self.num_elements, dtype=int)
        
        for i in range(self.num_elements):
            # Compute influence from other elements
            influence = np.zeros(self.state_dimension)
            base_prob = self.transition_matrices[i][self.current_state[i]]
            
            influence += base_prob
            
            for j in range(self.num_elements):
                if j != i and self.causal_connections[j, i] > 0:
                    # Element j influences element i
                    connection_strength = self.causal_connections[j, i]
                    j_influence = self.transition_matrices[j][self.current_state[j]]
                    
                    # Combine influences
                    influence = (1.0 - connection_strength) * influence + connection_strength * j_influence
                    
            # Normalize
            influence = influence / np.sum(influence)
            
            # Sample new state
            new_state[i] = np.random.choice(self.state_dimension, p=influence)
            
        self.current_state = new_state
        
        logger.debug(f"Updated system state to {new_state}")
        
        return new_state
        
    def compute_effective_information(self, partition: Tuple[Set[int], Set[int]]) -> float:
        """
        Compute effective information for a partition of the system.
        
        Args:
            partition: Tuple of two sets representing a partition
            
        Returns:
            Effective information
        """
        set1, set2 = partition
        
        if not set1 or not set2:
            return 0.0
            
        # Compute mutual information between sets
        # This is a simplified approximation of the full IIT calculation
        
        # Calculate average causal influence from set1 to set2
        influence_1to2 = 0.0
        count = 0
        
        for i in set1:
            for j in set2:
                influence_1to2 += self.causal_connections[i, j]
                count += 1
                
        if count > 0:
            influence_1to2 /= count
            
        # Calculate average causal influence from set2 to set1
        influence_2to1 = 0.0
        count = 0
        
        for i in set2:
            for j in set1:
                influence_2to1 += self.causal_connections[i, j]
                count += 1
                
        if count > 0:
            influence_2to1 /= count
            
        # Effective information is the sum of influences
        ei = influence_1to2 + influence_2to1
        
        return ei
        
    def compute_phi(self) -> float:
        """
        Compute integrated information (Φ) for the system.
        
        Returns:
            Integrated information value
        """
        # Find minimum information partition (MIP)
        min_ei = float('inf')
        self.mip = None
        
        # Iterate through all possible bipartitions
        for i in range(1, 2**(self.num_elements-1)):
            # Create partition
            set1 = set()
            set2 = set()
            
            for j in range(self.num_elements):
                if (i >> j) & 1:
                    set1.add(j)
                else:
                    set2.add(j)
                    
            # Compute effective information for this partition
            ei = self.compute_effective_information((set1, set2))
            
            # Normalize by minimum set size
            normalization = min(len(set1), len(set2))
            if normalization > 0:
                normalized_ei = ei / normalization
            else:
                normalized_ei = 0.0
                
            # Update MIP if new minimum found
            if normalized_ei < min_ei:
                min_ei = normalized_ei
                self.mip = (set1, set2)
                
        # Φ is the effective information of the MIP
        if self.mip:
            self.phi = self.compute_effective_information(self.mip)
        else:
            self.phi = 0.0
            
        logger.info(f"Computed system Φ = {self.phi:.6f}")
        
        return self.phi
        
    def mutual_information(self, set1: Set[int], set2: Set[int]) -> float:
        """
        Compute mutual information between two sets of elements.
        
        Args:
            set1: First set of element indices
            set2: Second set of element indices
            
        Returns:
            Mutual information
        """
        # This is a simplified approximation of mutual information
        
        if not set1 or not set2:
            return 0.0
            
        # Compute average influence in both directions
        influence_1to2 = 0.0
        influence_2to1 = 0.0
        count_1to2 = 0
        count_2to1 = 0
        
        for i in set1:
            for j in set2:
                influence_1to2 += self.causal_connections[i, j]
                count_1to2 += 1
                
        for i in set2:
            for j in set1:
                influence_2to1 += self.causal_connections[i, j]
                count_2to1 += 1
                
        if count_1to2 > 0:
            influence_1to2 /= count_1to2
            
        if count_2to1 > 0:
            influence_2to1 /= count_2to1
            
        # Mutual information is approximated by the geometric mean of influences
        mi = np.sqrt(influence_1to2 * influence_2to1)
        
        return mi
        
    def generate_concepts(self) -> List[Dict]:
        """
        Generate the set of concepts (mechanisms with non-zero Φ) for the system.
        
        Returns:
            List of concepts with their Φ values and purviews
        """
        concepts = []
        
        # Consider all subsets of elements as potential mechanisms
        for size in range(1, self.num_elements + 1):
            for mechanism in itertools.combinations(range(self.num_elements), size):
                mechanism_set = set(mechanism)
                
                # Find maximally irreducible cause and effect purviews
                max_cause_purview = self._find_max_irreducible_purview(mechanism_set, True)
                max_effect_purview = self._find_max_irreducible_purview(mechanism_set, False)
                
                # Calculate concept Φ as minimum of cause and effect Φ
                concept_phi = min(max_cause_purview['phi'], max_effect_purview['phi'])
                
                # If Φ > 0, this is a concept
                if concept_phi > 0:
                    concept = {
                        'mechanism': mechanism_set,
                        'phi': concept_phi,
                        'cause_purview': max_cause_purview['purview'],
                        'cause_phi': max_cause_purview['phi'],
                        'effect_purview': max_effect_purview['purview'],
                        'effect_phi': max_effect_purview['phi']
                    }
                    concepts.append(concept)
                    
        # Sort concepts by Φ value
        self.concepts = sorted(concepts, key=lambda c: c['phi'], reverse=True)
        
        logger.info(f"Generated {len(self.concepts)} concepts")
        
        return self.concepts
        
    def _find_max_irreducible_purview(self, mechanism: Set[int], is_cause: bool) -> Dict:
        """
        Find the maximally irreducible purview for a mechanism.
        
        Args:
            mechanism: Set of element indices forming the mechanism
            is_cause: Whether to find cause (True) or effect (False) purview
            
        Returns:
            Dictionary with purview, phi, and distribution
        """
        max_phi = 0.0
        max_purview = set()
        
        # Try all possible purviews
        for purview_size in range(1, self.num_elements + 1):
            for purview in itertools.combinations(range(self.num_elements), purview_size):
                purview_set = set(purview)
                
                # Compute Φ for this mechanism-purview pair
                phi = self._compute_phi_cause_effect(mechanism, purview_set, is_cause)
                
                if phi > max_phi:
                    max_phi = phi
                    max_purview = purview_set
                    
        return {
            'purview': max_purview,
            'phi': max_phi,
            'distribution': None  # Placeholder for full distribution
        }
        
    def _compute_phi_cause_effect(self, mechanism: Set[int], purview: Set[int], is_cause: bool) -> float:
        """
        Compute Φ for a mechanism-purview pair.
        
        Args:
            mechanism: Set of element indices forming the mechanism
            purview: Set of element indices forming the purview
            is_cause: Whether to compute cause (True) or effect (False) Φ
            
        Returns:
            Φ value for mechanism-purview pair
        """
        # This is a simplified approximation of the full IIT calculation
        
        # For cause, compute how purview influences mechanism
        # For effect, compute how mechanism influences purview
        if is_cause:
            source, target = purview, mechanism
        else:
            source, target = mechanism, purview
            
        # Compute average causal influence
        total_influence = 0.0
        count = 0
        
        for i in source:
            for j in target:
                total_influence += self.causal_connections[i, j]
                count += 1
                
        if count > 0:
            avg_influence = total_influence / count
        else:
            avg_influence = 0.0
            
        # Try to partition the mechanism-purview pair and find MIP
        min_diff = float('inf')
        
        # Try all partitions of the mechanism
        if len(mechanism) > 1:
            for i in range(1, 2**(len(mechanism)-1)):
                # Create partition of mechanism
                mech_part1 = set()
                mech_part2 = set()
                
                for j, elem in enumerate(mechanism):
                    if (i >> j) & 1:
                        mech_part1.add(elem)
                    else:
                        mech_part2.add(elem)
                        
                # Compute Φ for the partitioned mechanism
                if is_cause:
                    part1_phi = self._compute_phi_cause_effect(mech_part1, purview, is_cause)
                    part2_phi = self._compute_phi_cause_effect(mech_part2, purview, is_cause)
                else:
                    part1_phi = self._compute_phi_cause_effect(mech_part1, purview, is_cause)
                    part2_phi = self._compute_phi_cause_effect(mech_part2, purview, is_cause)
                
                # Difference between whole and parts is the irreducibility
                diff = avg_influence - (part1_phi + part2_phi)
                
                if diff < min_diff:
                    min_diff = diff
        
        # Try all partitions of the purview
        if len(purview) > 1:
            for i in range(1, 2**(len(purview)-1)):
                # Create partition of purview
                purv_part1 = set()
                purv_part2 = set()
                
                for j, elem in enumerate(purview):
                    if (i >> j) & 1:
                        purv_part1.add(elem)
                    else:
                        purv_part2.add(elem)
                        
                # Compute Φ for the partitioned purview
                if is_cause:
                    part1_phi = self._compute_phi_cause_effect(mechanism, purv_part1, is_cause)
                    part2_phi = self._compute_phi_cause_effect(mechanism, purv_part2, is_cause)
                else:
                    part1_phi = self._compute_phi_cause_effect(mechanism, purv_part1, is_cause)
                    part2_phi = self._compute_phi_cause_effect(mechanism, purv_part2, is_cause)
                
                # Difference between whole and parts is the irreducibility
                diff = avg_influence - (part1_phi + part2_phi)
                
                if diff < min_diff:
                    min_diff = diff
        
        # If no partition found, min_diff remains infinity
        if min_diff == float('inf'):
            min_diff = avg_influence
            
        # Φ is the irreducibility of the mechanism-purview pair
        phi = max(0.0, min_diff)
        
        return phi


class IntegratedInformationMeasure:
    """
    Provides various measures of integrated information and complexity
    for dynamical systems. This class implements static methods for
    computing different metrics related to information integration.
    """
    
    @staticmethod
    def mutual_information(p_xy: np.ndarray, p_x: np.ndarray, p_y: np.ndarray) -> float:
        """
        Compute mutual information between two variables.
        
        Args:
            p_xy: Joint probability distribution
            p_x: Marginal distribution of X
            p_y: Marginal distribution of Y
            
        Returns:
            Mutual information I(X;Y)
            
        Raises:
            ValueError: If inputs are not valid probability distributions
        """
        # Ensure valid probability distributions
        if not np.isclose(np.sum(p_xy), 1.0) or not np.isclose(np.sum(p_x), 1.0) or not np.isclose(np.sum(p_y), 1.0):
            raise ValueError("Inputs must be valid probability distributions")
            
        # Calculate mutual information
        mi = 0.0
        
        for i in range(len(p_x)):
            for j in range(len(p_y)):
                if p_xy[i, j] > 0 and p_x[i] > 0 and p_y[j] > 0:
                    mi += p_xy[i, j] * np.log2(p_xy[i, j] / (p_x[i] * p_y[j]))
                    
        return mi
    
    @staticmethod
    def kl_divergence(p: np.ndarray, q: np.ndarray) -> float:
        """
        Compute Kullback-Leibler divergence between two distributions.
        
        Args:
            p: First probability distribution
            q: Second probability distribution
            
        Returns:
            KL divergence D_KL(P||Q)
            
        Raises:
            ValueError: If inputs are not valid probability distributions
            
        Note:
            Handles cases where p[i] > 0 but q[i] = 0 by adding a small epsilon to q.
        """
        # Ensure valid probability distributions
        if not np.isclose(np.sum(p), 1.0) or not np.isclose(np.sum(q), 1.0):
            raise ValueError("Inputs must be valid probability distributions")
            
        # Add small epsilon to q to avoid division by zero
        q_safe = q.copy()
        q_safe[q_safe < 1e-10] = 1e-10
        
        # Renormalize q_safe
        q_safe = q_safe / np.sum(q_safe)
        
        # Use SciPy's implementation with safe values
        return sum(rel_entr(p, q_safe))
    
    @staticmethod
    def jensen_shannon_divergence(p: np.ndarray, q: np.ndarray) -> float:
        """
        Compute Jensen-Shannon divergence between two distributions.
        This is a symmetrized version of KL divergence with better numerical properties.
        
        Args:
            p: First probability distribution
            q: Second probability distribution
            
        Returns:
            Jensen-Shannon divergence JS(P||Q)
            
        Raises:
            ValueError: If inputs are not valid probability distributions
        """
        # Ensure valid probability distributions
        if not np.isclose(np.sum(p), 1.0) or not np.isclose(np.sum(q), 1.0):
            raise ValueError("Inputs must be valid probability distributions")
            
        # Compute the mean distribution
        m = 0.5 * (p + q)
        
        # Compute JS divergence
        js_div = 0.5 * IntegratedInformationMeasure.kl_divergence(p, m) + \
                0.5 * IntegratedInformationMeasure.kl_divergence(q, m)
                
        return js_div
    
    @staticmethod
    def wasserstein_distance(p: np.ndarray, q: np.ndarray, 
                            distance_matrix: Optional[np.ndarray] = None) -> float:
        """
        Compute Wasserstein distance (Earth Mover's Distance) between distributions.
        
        Args:
            p: First probability distribution
            q: Second probability distribution
            distance_matrix: Optional matrix of distances between states
                            (if None, Euclidean distance in index space is used)
            
        Returns:
            Wasserstein distance
            
        Raises:
            ValueError: If inputs are not valid probability distributions
        """
        # Ensure valid probability distributions
        if not np.isclose(np.sum(p), 1.0) or not np.isclose(np.sum(q), 1.0):
            raise ValueError("Inputs must be valid probability distributions")
            
        # Check distribution dimensions match
        if len(p) != len(q):
            raise ValueError(f"Distribution dimensions must match: got {len(p)} and {len(q)}")
            
        try:
            from scipy.optimize import linprog
            
            n = len(p)
            
            # Create distance matrix if not provided
            if distance_matrix is None:
                # Use Euclidean distance in index space
                indices = np.arange(n)
                distance_matrix = np.abs(indices[:, np.newaxis] - indices[np.newaxis, :])
                
            # Flatten the problem for linprog
            c = distance_matrix.flatten()
            
            # Build equality constraints for row and column sums
            A_eq = []
            b_eq = []
            
            # Constraints for p (row sums)
            for i in range(n):
                row_constraint = np.zeros(n*n)
                row_constraint[i*n:(i+1)*n] = 1.0
                A_eq.append(row_constraint)
                b_eq.append(p[i])
                
            # Constraints for q (column sums)
            for j in range(n):
                col_constraint = np.zeros(n*n)
                col_constraint[j::n] = 1.0
                A_eq.append(col_constraint)
                b_eq.append(q[j])
                
            # Convert to numpy arrays
            A_eq = np.array(A_eq)
            b_eq = np.array(b_eq)
            
            # Solve the linear programming problem
            # (find the optimal transport plan)
            bounds = [(0, None) for _ in range(n*n)]
            result = linprog(c, A_eq=A_eq, b_eq=b_eq, bounds=bounds, method='interior-point')
            
            if not result.success:
                logger.warning("Wasserstein distance computation failed. Using fallback method.")
                # Fallback: compute 1-Wasserstein distance (absolute difference)
                return np.sum(np.abs(np.cumsum(p) - np.cumsum(q)))
                
            # Extract optimal distance
            return result.fun
            
        except (ImportError, ValueError, np.linalg.LinAlgError) as e:
            logger.warning(f"Error in Wasserstein distance computation: {e}. Using fallback method.")
            
            # Fallback: compute 1-Wasserstein distance for 1D distributions
            # (absolute difference between cumulative distributions)
            return np.sum(np.abs(np.cumsum(p) - np.cumsum(q)))
    
    @staticmethod
    def earth_movers_distance(p: np.ndarray, q: np.ndarray) -> float:
        """
        Compute Earth Mover's Distance (1-Wasserstein distance) for 1D distributions.
        This is a faster special case for 1D distributions.
        
        Args:
            p: First probability distribution
            q: Second probability distribution
            
        Returns:
            Earth Mover's Distance
            
        Raises:
            ValueError: If inputs are not valid probability distributions
        """
        # Ensure valid probability distributions
        if not np.isclose(np.sum(p), 1.0) or not np.isclose(np.sum(q), 1.0):
            raise ValueError("Inputs must be valid probability distributions")
            
        # Compute cumulative distributions
        p_cum = np.cumsum(p)
        q_cum = np.cumsum(q)
        
        # Earth Mover's Distance is the L1 distance between cumulative distributions
        emd = np.sum(np.abs(p_cum - q_cum))
        
        return emd
    
    @staticmethod
    def effective_information(transition_matrix: np.ndarray, 
                            current_state: np.ndarray) -> float:
        """
        Compute effective information for a state transition.
        Measures the information gained by knowing the current state.
        
        Args:
            transition_matrix: Matrix of transition probabilities
            current_state: Current state distribution
            
        Returns:
            Effective information
            
        Raises:
            ValueError: If inputs are not valid probability distributions
        """
        # Ensure valid probability distributions
        if not np.isclose(np.sum(current_state), 1.0):
            raise ValueError("Current state must be a valid probability distribution")
            
        for row in transition_matrix:
            if not np.isclose(np.sum(row), 1.0):
                raise ValueError("Transition matrix rows must be valid probability distributions")
                
        # Maximum entropy distribution
        n = len(current_state)
        uniform_dist = np.ones(n) / n
        
        # Forward distribution given current state
        forward_dist = np.dot(current_state, transition_matrix)
        
        # Forward distribution given uniform distribution
        forward_uniform = np.dot(uniform_dist, transition_matrix)
        
        # Effective information is KL divergence between the distributions
        return IntegratedInformationMeasure.kl_divergence(forward_dist, forward_uniform)
    
    @staticmethod
    def causal_density(causal_matrix: np.ndarray) -> float:
        """
        Compute causal density of a network, measuring the overall
        causal interactions normalized by network size.
        
        Args:
            causal_matrix: Matrix of causal influences (should be normalized 0-1)
            
        Returns:
            Causal density value (0-1)
        """
        n = causal_matrix.shape[0]
        
        if n <= 1:
            return 0.0
            
        # Count significant connections (above threshold)
        threshold = 0.01
        significant_connections = np.sum(causal_matrix > threshold) - np.sum(np.diag(causal_matrix) > threshold)
        
        # Maximum possible connections
        max_connections = n * (n - 1)
        
        # Compute average connection strength for significant connections
        if significant_connections > 0:
            # Create a mask for significant off-diagonal connections
            mask = (causal_matrix > threshold) & ~np.eye(n, dtype=bool)
            avg_strength = np.sum(causal_matrix * mask) / significant_connections
        else:
            avg_strength = 0.0
            
        # Causal density combines connection density and strength
        density = (significant_connections / max_connections) * avg_strength
        
        return density
    
    @staticmethod
    def neural_complexity(covariance_matrix: np.ndarray) -> float:
        """
        Compute neural complexity measure (Tononi, Sporns, Edelman).
        This measures the balance between integration and segregation.
        
        Args:
            covariance_matrix: Covariance matrix of system elements
            
        Returns:
            Neural complexity value
            
        Raises:
            ValueError: If covariance matrix is not valid
        """
        n = covariance_matrix.shape[0]
        
        if n <= 1:
            return 0.0
            
        if covariance_matrix.shape[0] != covariance_matrix.shape[1]:
            raise ValueError("Covariance matrix must be square")
            
        if not np.allclose(covariance_matrix, covariance_matrix.T):
            raise ValueError("Covariance matrix must be symmetric")
            
        try:
            # Compute determinant of full covariance matrix for joint entropy
            whole_entropy = 0.5 * np.log(max(1e-10, np.linalg.det(covariance_matrix)))
            
            # Compute average entropy of all subsystems of size k
            # (Full implementation would compute for all subsystem sizes)
            total_complexity = 0.0
            
            # Compute for individual elements (k=1)
            individual_entropy = 0.0
            for i in range(n):
                individual_entropy += 0.5 * np.log(max(1e-10, covariance_matrix[i, i]))
                
            individual_entropy /= n
            
            # Integrate over all subsystem sizes (simplified)
            complexity_k1 = whole_entropy - individual_entropy
            
            # For a complete implementation, we would compute entropy for all subsystem sizes
            # Here, we approximate with a scaling based on system size
            for k in range(2, n):
                # Approximate complexity contribution for subsystems of size k
                complexity_k = complexity_k1 * k * (n - k) / (n - 1)
                total_complexity += complexity_k / (n - 1)
                
            # Normalize by system size
            normalized_complexity = total_complexity / (0.5 * n * (n - 1))
            
            return max(0.0, normalized_complexity)
            
        except np.linalg.LinAlgError as e:
            logger.warning(f"Error in complexity computation: {e}. Returning 0.")
            return 0.0
    
    @staticmethod
    def phi_empirical(time_series: np.ndarray, lag: int = 1) -> float:
        """
        Compute empirical Φ from time series data.
        
        Args:
            time_series: Time series data as (n_timepoints, n_variables) array
            lag: Lag for computing temporal dependencies
            
        Returns:
            Empirical Φ value
            
        Raises:
            ValueError: If time series format is invalid
        """
        if time_series.ndim != 2:
            raise ValueError("Time series must be 2D array with shape (n_timepoints, n_variables)")
            
        n_timepoints, n_variables = time_series.shape
        
        if n_timepoints <= lag:
            raise ValueError(f"Time series must be longer than lag {lag}")
            
        if n_variables < 2:
            raise ValueError("Time series must have at least 2 variables")
            
        try:
            # Compute covariance matrices for present and past states
            past_time_series = time_series[:-lag]
            present_time_series = time_series[lag:]
            
            # Center the data
            past_centered = past_time_series - np.mean(past_time_series, axis=0)
            present_centered = present_time_series - np.mean(present_time_series, axis=0)
            
            # Compute covariance matrices
            past_cov = np.dot(past_centered.T, past_centered) / (n_timepoints - lag)
            present_cov = np.dot(present_centered.T, present_centered) / (n_timepoints - lag)
            
            # Compute cross-covariance
            cross_cov = np.dot(past_centered.T, present_centered) / (n_timepoints - lag)
            
            # Compute entropy of present state
            present_entropy = 0.5 * np.log(max(1e-10, np.linalg.det(present_cov)))
            
            # Compute conditional entropy of present given past
            # (this is an approximation based on linear projection)
            try:
                past_inv = np.linalg.inv(past_cov)
                conditional_cov = present_cov - np.dot(cross_cov.T, np.dot(past_inv, cross_cov))
                conditional_entropy = 0.5 * np.log(max(1e-10, np.linalg.det(conditional_cov)))
            except np.linalg.LinAlgError:
                # Fallback if matrix inversion fails
                conditional_entropy = present_entropy
                
            # Mutual information between past and present
            mutual_info = present_entropy - conditional_entropy
            
            # Compute Φ by finding minimum partition
            min_phi = mutual_info
            
            # Try all bipartitions
            for k in range(1, n_variables):
                for subset_indices in itertools.combinations(range(n_variables), k):
                    subset1 = list(subset_indices)
                    subset2 = [i for i in range(n_variables) if i not in subset1]
                    
                    # Get data for each partition
                    past1 = past_centered[:, subset1]
                    past2 = past_centered[:, subset2]
                    present1 = present_centered[:, subset1]
                    present2 = present_centered[:, subset2]
                    
                    # Compute mutual info for each partition
                    try:
                        # Partition 1
                        past1_cov = np.dot(past1.T, past1) / (n_timepoints - lag)
                        present1_cov = np.dot(present1.T, present1) / (n_timepoints - lag)
                        cross1_cov = np.dot(past1.T, present1) / (n_timepoints - lag)
                        
                        present1_entropy = 0.5 * np.log(max(1e-10, np.linalg.det(present1_cov)))
                        
                        past1_inv = np.linalg.inv(past1_cov)
                        cond1_cov = present1_cov - np.dot(cross1_cov.T, np.dot(past1_inv, cross1_cov))
                        cond1_entropy = 0.5 * np.log(max(1e-10, np.linalg.det(cond1_cov)))
                        
                        mi1 = present1_entropy - cond1_entropy
                        
                        # Partition 2
                        past2_cov = np.dot(past2.T, past2) / (n_timepoints - lag)
                        present2_cov = np.dot(present2.T, present2) / (n_timepoints - lag)
                        cross2_cov = np.dot(past2.T, present2) / (n_timepoints - lag)
                        
                        present2_entropy = 0.5 * np.log(max(1e-10, np.linalg.det(present2_cov)))
                        
                        past2_inv = np.linalg.inv(past2_cov)
                        cond2_cov = present2_cov - np.dot(cross2_cov.T, np.dot(past2_inv, cross2_cov))
                        cond2_entropy = 0.5 * np.log(max(1e-10, np.linalg.det(cond2_cov)))
                        
                        mi2 = present2_entropy - cond2_entropy
                        
                        # Sum of partition mutual information
                        partition_mi = mi1 + mi2
                        
                        # Φ is the difference between whole and partitioned mutual info
                        partition_phi = mutual_info - partition_mi
                        
                        min_phi = min(min_phi, partition_phi)
                        
                    except np.linalg.LinAlgError:
                        # Skip this partition if there's a linear algebra error
                        continue
                        
            # Ensure non-negative
            phi = max(0.0, min_phi)
            
            return phi
            
        except Exception as e:
            logger.warning(f"Error in empirical Φ computation: {e}. Returning 0.")
            return 0.0
    
    @staticmethod
    def integrated_stochastic_interaction(transition_matrix: np.ndarray) -> float:
        """
        Compute integrated stochastic interaction (Φ_SI) from a transition matrix.
        This is a measure of integrated information for discrete Markov processes.
        
        Args:
            transition_matrix: Transition probability matrix
            
        Returns:
            Integrated stochastic interaction (Φ_SI)
            
        Raises:
            ValueError: If transition matrix is not valid
        """
        n = transition_matrix.shape[0]
        
        if transition_matrix.shape[0] != transition_matrix.shape[1]:
            raise ValueError("Transition matrix must be square")
            
        # Check that rows sum to 1
        for row in transition_matrix:
            if not np.isclose(np.sum(row), 1.0):
                raise ValueError("Transition matrix rows must sum to 1")
                
        try:
            # Compute stationary distribution
            eigenvalues, eigenvectors = np.linalg.eig(transition_matrix.T)
            stationary_idx = np.argmin(np.abs(eigenvalues - 1.0))
            stationary_dist = eigenvectors[:, stationary_idx].real
            stationary_dist = stationary_dist / np.sum(stationary_dist)
            
            # Ensure valid probability distribution
            if not np.isclose(np.sum(stationary_dist), 1.0) or np.any(stationary_dist < 0):
                # Fallback method if eigenvector approach fails
                stationary_dist = np.ones(n) / n
                for _ in range(1000):
                    stationary_dist = np.dot(stationary_dist, transition_matrix)
                    
            # Compute joint distribution of successive states
            joint_dist = np.zeros((n, n))
            for i in range(n):
                for j in range(n):
                    joint_dist[i, j] = stationary_dist[i] * transition_matrix[i, j]
                    
            # Compute marginal distribution of next state
            next_dist = np.sum(joint_dist, axis=0)
            
            # Compute mutual information between successive states
            mutual_info = 0.0
            for i in range(n):
                for j in range(n):
                    if joint_dist[i, j] > 0 and stationary_dist[i] > 0 and next_dist[j] > 0:
                        mutual_info += joint_dist[i, j] * np.log2(joint_dist[i, j] / (stationary_dist[i] * next_dist[j]))
                        
            # Minimum information partition search
            min_phi = mutual_info
            
            # Try all bipartitions
            for k in range(1, n):
                for subset_indices in itertools.combinations(range(n), k):
                    subset1 = list(subset_indices)
                    subset2 = [i for i in range(n) if i not in subset1]
                    
                    # Create partitioned transition matrices
                    # (simplified approach - full IIT is more complex)
                    
                    # Compute MI for partition 1
                    joint1 = joint_dist[np.ix_(subset1, subset1)]
                    stat1 = stationary_dist[subset1]
                    next1 = next_dist[subset1]
                    
                    # Normalize if needed
                    if np.sum(joint1) > 0:
                        joint1 = joint1 / np.sum(joint1)
                    if np.sum(stat1) > 0:
                        stat1 = stat1 / np.sum(stat1)
                    if np.sum(next1) > 0:
                        next1 = next1 / np.sum(next1)
                        
                    mi1 = 0.0
                    for i in range(len(subset1)):
                        for j in range(len(subset1)):
                            if joint1[i, j] > 0 and stat1[i] > 0 and next1[j] > 0:
                                mi1 += joint1[i, j] * np.log2(joint1[i, j] / (stat1[i] * next1[j]))
                                
                    # Compute MI for partition 2
                    joint2 = joint_dist[np.ix_(subset2, subset2)]
                    stat2 = stationary_dist[subset2]
                    next2 = next_dist[subset2]
                    
                    # Normalize if needed
                    if np.sum(joint2) > 0:
                        joint2 = joint2 / np.sum(joint2)
                    if np.sum(stat2) > 0:
                        stat2 = stat2 / np.sum(stat2)
                    if np.sum(next2) > 0:
                        next2 = next2 / np.sum(next2)
                        
                    mi2 = 0.0
                    for i in range(len(subset2)):
                        for j in range(len(subset2)):
                            if joint2[i, j] > 0 and stat2[i] > 0 and next2[j] > 0:
                                mi2 += joint2[i, j] * np.log2(joint2[i, j] / (stat2[i] * next2[j]))
                                
                    # Sum of partition mutual information
                    partition_mi = mi1 + mi2
                    
                    # Φ is the difference between whole and partitioned mutual info
                    partition_phi = mutual_info - partition_mi
                    
                    # Update minimum Φ
                    min_phi = min(min_phi, partition_phi)
                    
            # Ensure non-negative
            phi_si = max(0.0, min_phi)
            
            return phi_si
            
        except Exception as e:
            logger.warning(f"Error in integrated stochastic interaction computation: {e}. Returning 0.")
            return 0.0
    
    @staticmethod
    def small_world_index(adjacency_matrix: np.ndarray) -> float:
        """
        Compute the small-world index of a network.
        
        Args:
            adjacency_matrix: Binary adjacency matrix (connections > 0 are considered present)
            
        Returns:
            Small-world index value
            
        Raises:
            ValueError: If adjacency matrix is not valid
        """
        n = adjacency_matrix.shape[0]
        
        if n <= 2:
            return 0.0
            
        if adjacency_matrix.shape[0] != adjacency_matrix.shape[1]:
            raise ValueError("Adjacency matrix must be square")
            
        # Convert to binary adjacency
        binary_adj = adjacency_matrix > 0
        np.fill_diagonal(binary_adj, 0)  # Remove self-connections
        
        try:
            # Create NetworkX graph
            G = nx.from_numpy_array(binary_adj)
            
            # Compute clustering coefficient
            if nx.is_connected(G):
                clustering = nx.average_clustering(G)
                path_length = nx.average_shortest_path_length(G)
            else:
                # Handle disconnected graphs - compute over largest component
                largest_cc = max(nx.connected_components(G), key=len)
                subgraph = G.subgraph(largest_cc)
                clustering = nx.average_clustering(subgraph)
                path_length = nx.average_shortest_path_length(subgraph)
                
            # Generate equivalent random graph with same number of nodes and edges
            edge_count = G.number_of_edges()
            rand_G = nx.gnm_random_graph(n, edge_count, seed=42)
            
            # Compute metrics for random graph
            if nx.is_connected(rand_G):
                rand_clustering = nx.average_clustering(rand_G)
                rand_path_length = nx.average_shortest_path_length(rand_G)
            else:
                # Handle disconnected random graph
                largest_cc = max(nx.connected_components(rand_G), key=len)
                rand_subgraph = rand_G.subgraph(largest_cc)
                rand_clustering = nx.average_clustering(rand_subgraph)
                rand_path_length = nx.average_shortest_path_length(rand_subgraph)
                
            # Calculate small-world index
            if rand_clustering > 0 and rand_path_length > 0:
                small_world_index = (clustering / rand_clustering) / (path_length / rand_path_length)
            else:
                small_world_index = 0.0
                
            return small_world_index
            
        except (ImportError, ValueError, ZeroDivisionError) as e:
            logger.warning(f"Error in small-world index computation: {e}. Using fallback method.")
            
            # Fallback method if NetworkX is not available or calculation fails
            # Approximate clustering coefficient using triad count
            n_triads = 0
            for i in range(n):
                for j in range(i + 1, n):
                    if binary_adj[i, j]:
                        for k in range(j + 1, n):
                            if binary_adj[i, k] and binary_adj[j, k]:
                                n_triads += 1
                                
            # Count possible triads
            n_connections = np.sum(binary_adj) / 2  # Undirected connections
            possible_triads = max(1, n * (n - 1) * (n - 2) / 6)
            
            clustering_approx = n_triads / possible_triads if possible_triads > 0 else 0
            
            # Approximate path length using network density
            density = n_connections / (n * (n - 1) / 2)
            path_length_approx = 1 / max(0.01, density)
            
            # Random graph approximations
            rand_clustering_approx = density
            rand_path_length_approx = np.log(n) / np.log(n * density)
            
            # Calculate approximate small-world index
            if rand_clustering_approx > 0 and rand_path_length_approx > 0:
                small_world_approx = (clustering_approx / rand_clustering_approx) / (path_length_approx / rand_path_length_approx)
            else:
                small_world_approx = 0.0
                
            return small_world_approx
    
    @staticmethod
    def modularity(adjacency_matrix: np.ndarray, 
                  communities: Optional[List[List[int]]] = None) -> float:
        """
        Compute the modularity of a network with respect to a given community structure.
        
        Args:
            adjacency_matrix: Weighted adjacency matrix
            communities: Optional list of communities (lists of node indices)
                        If None, communities will be detected automatically
            
        Returns:
            Modularity value
            
        Raises:
            ValueError: If adjacency matrix is not valid
        """
        n = adjacency_matrix.shape[0]
        
        if n <= 1:
            return 0.0
            
        if adjacency_matrix.shape[0] != adjacency_matrix.shape[1]:
            raise ValueError("Adjacency matrix must be square")
            
        try:
            # Create NetworkX graph
            G = nx.from_numpy_array(adjacency_matrix, create_using=nx.Graph)
            
            if communities is None:
                # Detect communities if not provided
                try:
                    import community as community_louvain
                    partition = community_louvain.best_partition(G)
                    
                    # Convert partition dict to list of communities
                    max_community = max(partition.values())
                    communities = [[] for _ in range(max_community + 1)]
                    
                    for node, comm_id in partition.items():
                        communities[comm_id].append(node)
                        
                    # Remove empty communities
                    communities = [comm for comm in communities if comm]
                    
                except ImportError:
                    # Fallback if community detection library is not available
                    # Use simple spectral clustering
                    from sklearn.cluster import SpectralClustering
                    
                    # Ensure matrix is symmetric
                    sym_adj = (adjacency_matrix + adjacency_matrix.T) / 2
                    
                    # Estimate number of communities
                    est_communities = min(int(np.sqrt(n)), 10)
                    
                    # Perform spectral clustering
                    clustering = SpectralClustering(n_clusters=est_communities, 
                                                affinity='precomputed', 
                                                random_state=42)
                    labels = clustering.fit_predict(sym_adj)
                    
                    # Convert labels to communities
                    communities = [[] for _ in range(est_communities)]
                    for i, label in enumerate(labels):
                        communities[label].append(i)
            
            # Compute modularity using NetworkX
            # Convert communities to a dictionary mapping nodes to community IDs
            community_dict = {}
            for i, comm in enumerate(communities):
                for node in comm:
                    community_dict[node] = i
                    
            return nx.community.modularity(G, communities)
            
        except (ImportError, ValueError) as e:
            logger.warning(f"Error in modularity computation: {e}. Using fallback method.")
            
            # Fallback: compute modularity manually
            if communities is None:
                # Simple community detection using hierarchical clustering
                from scipy.cluster.hierarchy import linkage, fcluster
                from scipy.spatial.distance import squareform
                
                # Convert adjacency to distance (1 - normalized adjacency)
                # First ensure adjacency is symmetric
                sym_adj = (adjacency_matrix + adjacency_matrix.T) / 2
                
                max_val = np.max(sym_adj)
                if max_val > 0:
                    norm_adj = sym_adj / max_val
                else:
                    norm_adj = sym_adj
                    
                distance = 1 - norm_adj
                np.fill_diagonal(distance, 0)
                
                # Convert to condensed distance matrix
                condensed_dist = squareform(distance)
                
                # Perform hierarchical clustering
                est_communities = min(int(np.sqrt(n)), 10)
                Z = linkage(condensed_dist, method='ward')
                labels = fcluster(Z, est_communities, criterion='maxclust') - 1
                
                # Convert labels to communities
                communities = [[] for _ in range(est_communities)]
                for i, label in enumerate(labels):
                    communities[label].append(i)
                    
            # Manual modularity calculation
            # Create a matrix where 1 indicates nodes are in the same community
            same_community = np.zeros((n, n))
            for comm in communities:
                for i in comm:
                    for j in comm:
                        same_community[i, j] = 1
                        
            # Calculate total edge weight
            total_weight = np.sum(adjacency_matrix)
            
            if total_weight == 0:
                return 0.0
                
            # Calculate degree for each node
            degrees = np.sum(adjacency_matrix, axis=1)
            
            # Calculate expected number of edges
            expected = np.outer(degrees, degrees) / total_weight
            
            # Calculate modularity
            modularity = np.sum((adjacency_matrix - expected) * same_community) / total_weight
            
            return modularity


def create_integrated_information_matrix(num_subsystems: int = 8, 
                                       state_dimension: int = 64,
                                       optimization_iterations: int = 50,
                                       subsystem_names: Optional[List[str]] = None,
                                       optimize: bool = True) -> IntegratedInformationMatrix:
    """
    Factory function to create and initialize an Integrated Information Matrix
    with common configuration. This provides an easy way to create the IIM
    with optimal settings for the ULTRA system.
    
    Args:
        num_subsystems: Number of subsystems to model
        state_dimension: Dimension of each subsystem's state representation
        optimization_iterations: Default iterations for optimization
        subsystem_names: Optional list of names for subsystems (defaults to standard naming)
        optimize: Whether to perform initial optimization to improve integration
        
    Returns:
        Configured IntegratedInformationMatrix instance
        
    Raises:
        ValueError: If configuration parameters are invalid
    """
    # Validate parameters
    if num_subsystems < 2:
        raise ValueError("Must have at least 2 subsystems")
        
    if state_dimension < 1:
        raise ValueError("State dimension must be positive")
        
    if subsystem_names is not None and len(subsystem_names) != num_subsystems:
        raise ValueError(f"Expected {num_subsystems} subsystem names, got {len(subsystem_names)}")
        
    # Create matrix with default configuration
    logger.info(f"Creating Integrated Information Matrix with {num_subsystems} subsystems")
    
    # Initialize with parallel processing if available
    parallel = cpu_count() > 1
    matrix = IntegratedInformationMatrix(num_subsystems, state_dimension, 
                                      optimization_iterations, parallel)
    
    # Set custom subsystem names if provided
    if subsystem_names:
        matrix.set_subsystem_names(subsystem_names)
        
    # Perform initial optimization if requested
    if optimize:
        logger.info("Performing initial integration optimization")
        initial_phi = matrix.system_phi
        matrix.optimize_integration(max(10, optimization_iterations // 5))
        final_phi = matrix.system_phi
        
        logger.info(f"Initial optimization complete: Φ improved from {initial_phi:.6f} to {final_phi:.6f}")
        
    return matrix