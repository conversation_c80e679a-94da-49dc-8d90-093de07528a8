#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Intentionality System Module for ULTRA

This module implements the Intentionality System component of the ULTRA
architecture's Emergent Consciousness Lattice. It enables goal-directed behavior
and planning, allowing the system to maintain and pursue hierarchical objectives
over time, representing a form of artificial intentionality.

The Intentionality System provides:
- Goal creation and hierarchical organization
- Goal prioritization and resource allocation
- Plan generation and monitoring
- Temporal reasoning about goals and deadlines
- Progress tracking and assessment
- Dependency management and handling
- Self-optimization through automated maintenance

Authors: <AUTHORS>
"""

import numpy as np
import logging
import time
import uuid
import heapq
import math
from typing import Dict, List, Tuple, Set, Optional, Union, Any, Callable
from dataclasses import dataclass, field
from collections import defaultdict, deque
import datetime
import json
import os

# Configure logging
logger = logging.getLogger(__name__)

@dataclass
class Goal:
    """Data class representing a goal in the system"""
    id: str
    description: str
    priority: float  # 0.0 to 1.0
    status: str  # 'active', 'pending', 'complete', 'failed', 'abandoned'
    created_at: float  # timestamp
    deadline: Optional[float] = None  # timestamp or None
    parent_id: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    completion: float = 0.0  # 0.0 to 1.0
    
    # Additional fields
    expected_duration: Optional[float] = None  # estimated time to complete in seconds
    last_updated: float = field(default_factory=time.time)  # timestamp of last update
    attention_score: float = 0.5  # current attention allocation (0.0 to 1.0)
    dependencies: List[str] = field(default_factory=list)  # list of goal IDs this goal depends on
    blockers: List[str] = field(default_factory=list)  # list of goal IDs blocking this goal
    tags: List[str] = field(default_factory=list)  # categorization tags
    urgency: float = 0.5  # calculated urgency score (0.0 to 1.0)
    importance: float = 0.5  # long-term importance (0.0 to 1.0)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert Goal to a dictionary for serialization"""
        return {
            'id': self.id,
            'description': self.description,
            'priority': self.priority,
            'status': self.status,
            'created_at': self.created_at,
            'deadline': self.deadline,
            'parent_id': self.parent_id,
            'metadata': self.metadata,
            'completion': self.completion,
            'expected_duration': self.expected_duration,
            'last_updated': self.last_updated,
            'attention_score': self.attention_score,
            'dependencies': self.dependencies,
            'blockers': self.blockers,
            'tags': self.tags,
            'urgency': self.urgency,
            'importance': self.importance
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Goal':
        """Create a Goal from a dictionary"""
        return cls(
            id=data['id'],
            description=data['description'],
            priority=data['priority'],
            status=data['status'],
            created_at=data['created_at'],
            deadline=data.get('deadline'),
            parent_id=data.get('parent_id'),
            metadata=data.get('metadata', {}),
            completion=data.get('completion', 0.0),
            expected_duration=data.get('expected_duration'),
            last_updated=data.get('last_updated', time.time()),
            attention_score=data.get('attention_score', 0.5),
            dependencies=data.get('dependencies', []),
            blockers=data.get('blockers', []),
            tags=data.get('tags', []),
            urgency=data.get('urgency', 0.5),
            importance=data.get('importance', 0.5)
        )
    
    def get_time_remaining(self) -> Optional[float]:
        """Calculate time remaining until deadline in seconds"""
        if self.deadline is None:
            return None
        
        return max(0.0, self.deadline - time.time())
    
    def get_time_pressure(self) -> float:
        """Calculate time pressure (0.0 to 1.0)
        
        Returns 0.0 if no deadline, 1.0 if past deadline,
        and a value between based on remaining time vs. expected duration
        """
        time_remaining = self.get_time_remaining()
        
        if time_remaining is None:
            return 0.0
        
        if time_remaining <= 0:
            return 1.0
        
        if self.expected_duration is None or self.expected_duration <= 0:
            # If no expected duration, use a heuristic based on remaining time
            # Scale: 1.0 if < 1 hour, 0.0 if > 1 week
            one_hour = 3600
            one_week = 7 * 24 * 3600
            
            if time_remaining < one_hour:
                return 1.0
            elif time_remaining > one_week:
                return 0.0
            else:
                # Linear interpolation
                return 1.0 - (time_remaining - one_hour) / (one_week - one_hour)
        else:
            # Calculate based on remaining time vs. expected duration
            remaining_work = 1.0 - self.completion
            required_time = remaining_work * self.expected_duration
            
            if required_time <= 0:
                return 0.0
            
            time_pressure = required_time / time_remaining
            
            # Clamp between 0 and 1
            return max(0.0, min(1.0, time_pressure))
    
    def calculate_urgency(self) -> float:
        """Calculate the urgency of this goal based on multiple factors
        
        Returns:
            Urgency score from 0.0 (not urgent) to 1.0 (extremely urgent)
        """
        # Start with base priority
        base_urgency = self.priority
        
        # Adjust for time pressure
        time_pressure = self.get_time_pressure()
        time_factor = time_pressure * 0.4  # Weight time pressure at 40%
        
        # Adjust for completion
        completion_factor = (1.0 - self.completion) * 0.3  # Weight remaining work at 30%
        
        # Adjust for blockers
        blocker_factor = min(1.0, len(self.blockers) * 0.2)  # Each blocker adds urgency
        
        # Adjust for dependencies
        dependency_factor = 0.0
        if self.dependencies:
            # More dependencies might delay goal completion, reducing urgency
            dependency_factor = -0.1 * min(1.0, len(self.dependencies) * 0.1)
        
        # Calculate final urgency
        urgency = base_urgency + time_factor + completion_factor + blocker_factor + dependency_factor
        
        # Clamp between 0 and 1
        self.urgency = max(0.0, min(1.0, urgency))
        return self.urgency
    
    def is_overdue(self) -> bool:
        """Check if the goal is past its deadline"""
        if self.deadline is None:
            return False
        return time.time() > self.deadline
    
    def is_stalled(self, stall_threshold_days: float = 7.0) -> bool:
        """Check if a goal appears to be stalled (old with little progress)
        
        Args:
            stall_threshold_days: Number of days with little progress to consider stalled
            
        Returns:
            True if the goal appears stalled
        """
        if self.status != 'active':
            return False
            
        stall_threshold_seconds = stall_threshold_days * 86400
        age = time.time() - self.created_at
        
        # Consider stalled if more than threshold days old with < 20% progress
        return age > stall_threshold_seconds and self.completion < 0.2


@dataclass
class Plan:
    """Data class representing a plan to achieve a goal"""
    id: str
    goal_id: str
    actions: List[Dict[str, Any]]  # List of action steps with details
    expected_start: float  # timestamp
    expected_end: float  # timestamp
    status: str  # 'planned', 'in_progress', 'completed', 'failed', 'abandoned'
    current_action_index: int = 0  # Index of current action in execution
    actual_start: Optional[float] = None  # timestamp of actual start
    actual_end: Optional[float] = None  # timestamp of actual end
    progress: float = 0.0  # overall progress (0.0 to 1.0)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert Plan to a dictionary for serialization"""
        return {
            'id': self.id,
            'goal_id': self.goal_id,
            'actions': self.actions,
            'expected_start': self.expected_start,
            'expected_end': self.expected_end,
            'status': self.status,
            'current_action_index': self.current_action_index,
            'actual_start': self.actual_start,
            'actual_end': self.actual_end,
            'progress': self.progress,
            'metadata': self.metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Plan':
        """Create a Plan from a dictionary"""
        return cls(
            id=data['id'],
            goal_id=data['goal_id'],
            actions=data['actions'],
            expected_start=data['expected_start'],
            expected_end=data['expected_end'],
            status=data['status'],
            current_action_index=data.get('current_action_index', 0),
            actual_start=data.get('actual_start'),
            actual_end=data.get('actual_end'),
            progress=data.get('progress', 0.0),
            metadata=data.get('metadata', {})
        )
    
    def is_on_schedule(self) -> bool:
        """Determine if the plan is on schedule based on progress vs. time elapsed"""
        if self.status != 'in_progress' or self.actual_start is None:
            return True  # Not started yet or already finished
        
        now = time.time()
        
        # Calculate expected progress based on time elapsed
        total_duration = self.expected_end - self.expected_start
        elapsed = now - self.actual_start
        
        if total_duration <= 0:
            return True  # Avoid division by zero
        
        expected_progress = min(1.0, elapsed / total_duration)
        
        # Allow for some slack (20% behind schedule is still considered on schedule)
        return self.progress >= (expected_progress - 0.2)
    
    def get_next_action(self) -> Optional[Dict[str, Any]]:
        """Get the next action to execute"""
        if self.current_action_index >= len(self.actions):
            return None
        
        return self.actions[self.current_action_index]
    
    def advance_action(self) -> bool:
        """Advance to the next action
        
        Returns:
            True if successfully advanced, False if at the end
        """
        if self.current_action_index >= len(self.actions) - 1:
            return False
        
        self.current_action_index += 1
        
        # Update progress
        self.progress = (self.current_action_index) / max(1, len(self.actions))
        
        return True
    
    def update_progress(self, action_progress: float) -> None:
        """Update overall progress based on current action progress
        
        Args:
            action_progress: Progress of current action (0.0 to 1.0)
        """
        if len(self.actions) == 0:
            self.progress = 0.0
            return
        
        # Calculate overall progress
        # Completed actions + partial completion of current action
        completed_actions = self.current_action_index
        current_contribution = action_progress / len(self.actions)
        
        self.progress = (completed_actions + current_contribution) / len(self.actions)
        
        # Update the action's progress in the list
        if self.current_action_index < len(self.actions):
            self.actions[self.current_action_index]['progress'] = action_progress
    
    def get_estimated_completion_time(self) -> Optional[float]:
        """Calculate estimated completion time based on current progress rate
        
        Returns:
            Estimated completion timestamp or None if insufficient data
        """
        if self.status != 'in_progress' or self.actual_start is None or self.progress == 0:
            return self.expected_end
        
        now = time.time()
        elapsed = now - self.actual_start
        
        if elapsed <= 0 or self.progress <= 0:
            return self.expected_end
        
        # Calculate rate of progress (progress per second)
        progress_rate = self.progress / elapsed
        
        # Estimate remaining time
        if progress_rate > 0:
            remaining_progress = 1.0 - self.progress
            remaining_time = remaining_progress / progress_rate
            return now + remaining_time
        else:
            return self.expected_end


class IntentionalitySystem:
    """
    Implements goal-directed behavior and planning, allowing the system to maintain
    and pursue hierarchical objectives over time.
    
    This represents a form of artificial intentionality, where the system's processes
    are directed toward achieving specific outcomes.
    """
    
    def __init__(
        self,
        max_active_goals: int = 10,
        planning_horizon: int = 5,
        goal_pruning_threshold: float = 0.2,
        prioritization_temperature: float = 0.5,
        urgency_update_interval: float = 60.0,  # seconds
        plan_update_interval: float = 300.0,  # seconds
        goal_persistence_file: Optional[str] = None,
        urgency_factor: float = 1.2,
        subgoal_inheritance_factor: float = 0.9,
        resource_allocation_factor: float = 0.8
    ):
        """
        Initialize the Intentionality System.
        
        Args:
            max_active_goals: Maximum number of simultaneously active goals
            planning_horizon: Number of planning steps to look ahead
            goal_pruning_threshold: Priority threshold for pruning low-priority goals
            prioritization_temperature: Temperature parameter for softmax priority calculation
            urgency_update_interval: How often to update urgency scores (seconds)
            plan_update_interval: How often to update plan statuses (seconds)
            goal_persistence_file: Optional file path to persist goals and plans
            urgency_factor: Factor for increasing priority as deadlines approach
            subgoal_inheritance_factor: Factor for priority inheritance from parent to child
            resource_allocation_factor: Factor determining resource allocation strategy
        """
        # Core parameters
        self.max_active_goals = max_active_goals
        self.planning_horizon = planning_horizon
        self.goal_pruning_threshold = goal_pruning_threshold
        self.prioritization_temperature = prioritization_temperature
        self.urgency_update_interval = urgency_update_interval
        self.plan_update_interval = plan_update_interval
        self.goal_persistence_file = goal_persistence_file
        self.urgency_factor = urgency_factor
        self.subgoal_inheritance_factor = subgoal_inheritance_factor
        self.resource_allocation_factor = resource_allocation_factor
        
        # State
        self.current_goals: Dict[str, Goal] = {}
        self.goal_hierarchy: Dict[str, Dict[str, Union[str, List[str]]]] = {}
        self.plans: Dict[str, Plan] = {}
        self.plan_history: List[Dict[str, Any]] = []
        self.goal_dependencies: Dict[str, Set[str]] = defaultdict(set)
        
        # Goal attention distribution
        self.goal_attention: Dict[str, float] = {}
        
        # Resource allocation
        self.allocated_resources: Dict[str, float] = {}
        
        # Metrics
        self.goals_created = 0
        self.goals_completed = 0
        self.goals_failed = 0
        
        # Temporal tracking
        self._last_urgency_update = time.time()
        self._last_plan_update = time.time()
        self._last_goal_check = time.time()
        self._next_plan_id = 0
        
        # Load persisted goals if file exists
        if goal_persistence_file:
            self._load_from_file()
        
        logger.info("Intentionality System initialized with max_active_goals=%d, planning_horizon=%d",
                    max_active_goals, planning_horizon)
    
    def set_goal(
        self, 
        description: str, 
        priority: float = 0.5, 
        deadline: Optional[float] = None,
        expected_duration: Optional[float] = None,
        metadata: Optional[Dict[str, Any]] = None,
        tags: Optional[List[str]] = None,
        importance: Optional[float] = None,
        dependencies: Optional[List[str]] = None
    ) -> str:
        """
        Add a goal to the current goals.
        
        Args:
            description: Description of the goal
            priority: Priority of the goal (0 to 1)
            deadline: Optional deadline (timestamp)
            expected_duration: Estimated time to complete in seconds
            metadata: Optional additional information about the goal
            tags: Optional list of tags for categorization
            importance: Long-term importance of this goal (0 to 1)
            dependencies: Optional list of goal IDs this goal depends on
            
        Returns:
            Goal ID
        """
        # Input validation
        priority = max(0.0, min(1.0, priority))
        if deadline is not None and deadline < time.time():
            logger.warning("Creating a goal with a deadline in the past: %s", description)
        
        # Generate unique ID
        goal_id = str(uuid.uuid4())
        
        # Use priority as importance if not specified
        if importance is None:
            importance = priority
        
        # Create goal object
        goal = Goal(
            id=goal_id,
            description=description,
            priority=priority,
            status='active',
            created_at=time.time(),
            deadline=deadline,
            parent_id=None,
            metadata=metadata or {},
            expected_duration=expected_duration,
            tags=tags or [],
            importance=importance,
            dependencies=[] if dependencies is None else dependencies.copy()
        )
        
        # Calculate initial urgency
        goal.calculate_urgency()
        
        # Add to current goals
        self.current_goals[goal_id] = goal
        
        # Initialize in goal hierarchy
        self.goal_hierarchy[goal_id] = {
            'parent': None,
            'children': []
        }
        
        # Store dependencies in the dependency map
        if dependencies:
            for dep_id in dependencies:
                if dep_id in self.current_goals:
                    self.goal_dependencies[goal_id].add(dep_id)
                    # Add this goal as a blocker for the dependency
                    if dep_id not in self.current_goals[dep_id].blockers:
                        self.current_goals[dep_id].blockers.append(goal_id)
        
        # Initialize attention
        self.goal_attention[goal_id] = priority
        
        # Update metrics
        self.goals_created += 1
        
        # Check if we need to prune goals
        self._prune_goals_if_needed()
        
        # Persist goals if file specified
        if self.goal_persistence_file:
            self._save_to_file()
        
        logger.info("Created new goal: %s, priority=%.2f", description, priority)
        return goal_id
    
    def set_subgoal(
        self, 
        parent_goal_id: str, 
        description: str, 
        priority: Optional[float] = None,
        deadline: Optional[float] = None,
        expected_duration: Optional[float] = None,
        metadata: Optional[Dict[str, Any]] = None,
        tags: Optional[List[str]] = None,
        importance: Optional[float] = None,
        dependencies: Optional[List[str]] = None
    ) -> str:
        """
        Add a subgoal to an existing goal.
        
        Args:
            parent_goal_id: ID of the parent goal
            description: Description of the subgoal
            priority: Priority of the subgoal (0 to 1), if None inherits from parent
            deadline: Optional deadline (timestamp)
            expected_duration: Estimated time to complete in seconds
            metadata: Optional additional information about the subgoal
            tags: Optional list of tags for categorization
            importance: Long-term importance of this goal (0 to 1)
            dependencies: Optional list of goal IDs this subgoal depends on
            
        Returns:
            Subgoal ID
            
        Raises:
            ValueError: If parent goal not found
        """
        # Find parent goal
        if parent_goal_id not in self.current_goals:
            raise ValueError(f"Parent goal with ID {parent_goal_id} not found")
            
        parent_goal = self.current_goals[parent_goal_id]
        
        # Apply inheritance factors
        if priority is None:
            # Inherit priority with a dampening factor
            priority = parent_goal.priority * self.subgoal_inheritance_factor
        else:
            # Ensure priority is valid
            priority = max(0.0, min(1.0, priority))
            
        # Use parent values if not specified
        if deadline is None and parent_goal.deadline is not None:
            deadline = parent_goal.deadline
            
        # Subgoal deadline shouldn't be later than parent deadline
        if deadline is not None and parent_goal.deadline is not None:
            deadline = min(deadline, parent_goal.deadline)
            
        if importance is None:
            importance = parent_goal.importance * self.subgoal_inheritance_factor
            
        # If parent has tags, include them in subgoal
        combined_tags = list(set((tags or []) + parent_goal.tags))
        
        # Create subgoal
        subgoal_id = str(uuid.uuid4())
        
        subgoal = Goal(
            id=subgoal_id,
            description=description,
            priority=priority,
            status='active',
            created_at=time.time(),
            deadline=deadline,
            parent_id=parent_goal_id,
            metadata=metadata or {},
            expected_duration=expected_duration,
            tags=combined_tags,
            importance=importance,
            dependencies=[] if dependencies is None else dependencies.copy()
        )
        
        # Calculate initial urgency
        subgoal.calculate_urgency()
        
        # Add to current goals
        self.current_goals[subgoal_id] = subgoal
        
        # Update goal hierarchy
        self.goal_hierarchy[subgoal_id] = {
            'parent': parent_goal_id,
            'children': []
        }
        self.goal_hierarchy[parent_goal_id]['children'].append(subgoal_id)
        
        # Initialize attention
        self.goal_attention[subgoal_id] = priority
        
        # Store dependencies in the dependency map
        if dependencies:
            for dep_id in dependencies:
                if dep_id in self.current_goals:
                    if not self._would_create_circular_dependency(subgoal_id, dep_id):
                        self.goal_dependencies[subgoal_id].add(dep_id)
                        # Add this goal as a blocker for the dependency
                        if subgoal_id not in self.current_goals[dep_id].blockers:
                            self.current_goals[dep_id].blockers.append(subgoal_id)
        
        # Automatic dependency on parent
        # Adding parent as dependency would create circular references,
        # but we handle parent-child relationships separately
        
        # Update metrics
        self.goals_created += 1
        
        # Persist goals if file specified
        if self.goal_persistence_file:
            self._save_to_file()
        
        logger.info("Created subgoal '%s' under parent '%s'", 
                   description, parent_goal.description)
        return subgoal_id
    
    def add_goal_dependency(self, goal_id: str, dependency_id: str) -> bool:
        """
        Add a dependency between goals, making one goal dependent on another.
        
        Args:
            goal_id: ID of the dependent goal
            dependency_id: ID of the goal that must be completed first
            
        Returns:
            True if successfully added, False otherwise
            
        Raises:
            ValueError: If either goal not found
        """
        # Check both goals exist
        if goal_id not in self.current_goals:
            raise ValueError(f"Goal with ID {goal_id} not found")
            
        if dependency_id not in self.current_goals:
            raise ValueError(f"Dependency goal with ID {dependency_id} not found")
            
        # Check for circular dependencies
        if self._would_create_circular_dependency(goal_id, dependency_id):
            logger.warning("Circular dependency detected: %s depends on %s which depends on %s",
                          goal_id, dependency_id, goal_id)
            return False
            
        # Add dependency
        goal = self.current_goals[goal_id]
        if dependency_id not in goal.dependencies:
            goal.dependencies.append(dependency_id)
            self.goal_dependencies[goal_id].add(dependency_id)
            
        # Add as blocker
        dependency = self.current_goals[dependency_id]
        if goal_id not in dependency.blockers:
            dependency.blockers.append(goal_id)
            
        # Update goal
        goal.last_updated = time.time()
        
        # Recalculate urgency
        goal.calculate_urgency()
        
        # Persist goals if file specified
        if self.goal_persistence_file:
            self._save_to_file()
            
        logger.debug("Added dependency: %s depends on %s", goal_id, dependency_id)
        return True
    
    def _would_create_circular_dependency(self, goal_id: str, dependency_id: str) -> bool:
        """
        Check if adding a dependency would create a circular dependency.
        
        Args:
            goal_id: ID of the dependent goal
            dependency_id: ID of the goal that must be completed first
            
        Returns:
            True if a circular dependency would be created, False otherwise
        """
        # Direct circular dependency
        if dependency_id == goal_id:
            return True
            
        # Check parent-child relationships
        current_id = dependency_id
        while current_id is not None:
            parent_id = self.goal_hierarchy.get(current_id, {}).get('parent')
            if parent_id == goal_id:
                return True
            current_id = parent_id
        
        # Check transitive dependencies using DFS
        visited = set()
        stack = [dependency_id]
        
        while stack:
            current = stack.pop()
            if current == goal_id:
                return True
                
            if current in visited:
                continue
                
            visited.add(current)
            
            # Add dependencies to stack
            dependencies = self.goal_dependencies.get(current, set())
            for dep in dependencies:
                if dep not in visited:
                    stack.append(dep)
                    
            # Also check child goals as potential dependencies
            for child_id in self.goal_hierarchy.get(current, {}).get('children', []):
                if child_id not in visited:
                    stack.append(child_id)
                    
        return False
    
    def remove_goal_dependency(self, goal_id: str, dependency_id: str) -> bool:
        """
        Remove a dependency between goals.
        
        Args:
            goal_id: ID of the dependent goal
            dependency_id: ID of the goal that was a dependency
            
        Returns:
            True if successfully removed, False if dependency didn't exist
            
        Raises:
            ValueError: If either goal not found
        """
        # Check both goals exist
        if goal_id not in self.current_goals:
            raise ValueError(f"Goal with ID {goal_id} not found")
            
        if dependency_id not in self.current_goals:
            raise ValueError(f"Dependency goal with ID {dependency_id} not found")
            
        # Remove dependency
        goal = self.current_goals[goal_id]
        dependency_removed = False
        
        if dependency_id in goal.dependencies:
            goal.dependencies.remove(dependency_id)
            dependency_removed = True
            
        if dependency_id in self.goal_dependencies.get(goal_id, set()):
            self.goal_dependencies[goal_id].remove(dependency_id)
            dependency_removed = True
            
        if dependency_removed:
            goal.last_updated = time.time()
            
            # Remove as blocker
            dependency = self.current_goals[dependency_id]
            if goal_id in dependency.blockers:
                dependency.blockers.remove(goal_id)
                
            # Recalculate urgency
            goal.calculate_urgency()
                
            # Persist goals if file specified
            if self.goal_persistence_file:
                self._save_to_file()
                
            logger.debug("Removed dependency: %s no longer depends on %s", goal_id, dependency_id)
            return True
        else:
            return False
    
    def update_goal_progress(self, goal_id: str, completion: float) -> None:
        """
        Update the completion progress of a goal.
        
        Args:
            goal_id: ID of the goal
            completion: Completion percentage (0.0 to 1.0)
            
        Raises:
            ValueError: If goal not found
        """
        if goal_id not in self.current_goals:
            raise ValueError(f"Goal with ID {goal_id} not found")
            
        # Update completion
        goal = self.current_goals[goal_id]
        old_completion = goal.completion
        goal.completion = max(0.0, min(1.0, completion))
        goal.last_updated = time.time()
        
        # If goal is complete, mark it
        if completion >= 0.99 and old_completion < 0.99:
            self.mark_goal_complete(goal_id)
        else:
            # Recalculate urgency
            goal.calculate_urgency()
            
            # Update parent goal progress
            self._update_parent_goal_progress(goal_id)
        
        # Persist goals if file specified
        if self.goal_persistence_file:
            self._save_to_file()
            
        logger.debug("Updated goal '%s' progress to %.2f", goal.description, completion)
    
    def _update_parent_goal_progress(self, goal_id: str) -> None:
        """
        Update parent goal progress based on children's progress.
        
        Args:
            goal_id: ID of the child goal
        """
        parent_id = self.goal_hierarchy[goal_id].get('parent')
        if parent_id is None or parent_id not in self.current_goals:
            return
            
        # Calculate parent progress as weighted average of children's progress
        children = self.goal_hierarchy[parent_id]['children']
        if not children:
            return
            
        total_weight = 0.0
        weighted_progress = 0.0
        
        for child_id in children:
            if child_id in self.current_goals:
                child = self.current_goals[child_id]
                # Use importance as weight for averaging
                weight = max(0.1, child.importance)
                weighted_progress += weight * child.completion
                total_weight += weight
                
        if total_weight > 0:
            parent_completion = weighted_progress / total_weight
            parent = self.current_goals[parent_id]
            old_completion = parent.completion
            parent.completion = parent_completion
            parent.last_updated = time.time()
            
            # If parent became complete, mark it
            if parent.completion >= 0.99 and old_completion < 0.99:
                self.mark_goal_complete(parent_id)
            else:
                # Recalculate urgency
                parent.calculate_urgency()
                
                # Recursively update grandparent
                self._update_parent_goal_progress(parent_id)
    
    def mark_goal_complete(self, goal_id: str) -> None:
        """
        Mark a goal as complete.
        
        Args:
            goal_id: ID of the goal
            
        Raises:
            ValueError: If goal not found
        """
        if goal_id not in self.current_goals:
            raise ValueError(f"Goal with ID {goal_id} not found")
            
        # Mark as complete
        goal = self.current_goals[goal_id]
        goal.status = 'complete'
        goal.completion = 1.0
        goal.last_updated = time.time()
        goal.metadata['completion_time'] = time.time()
        
        # Update any plans for this goal
        for plan in self.plans.values():
            if plan.goal_id == goal_id and plan.status in ['planned', 'in_progress']:
                plan.status = 'completed'
                plan.progress = 1.0
                plan.actual_end = time.time()
        
        # Unblock any dependent goals
        for blocked_id in goal.blockers:
            if blocked_id in self.current_goals:
                blocked_goal = self.current_goals[blocked_id]
                if goal_id in blocked_goal.dependencies:
                    blocked_goal.dependencies.remove(goal_id)
                    # Also remove from dependency map
                    if blocked_id in self.goal_dependencies and goal_id in self.goal_dependencies[blocked_id]:
                        self.goal_dependencies[blocked_id].remove(goal_id)
        
        # Remove from dependency map
        if goal_id in self.goal_dependencies:
            self.goal_dependencies.pop(goal_id)
        
        # Check if parent goal should be complete
        parent_id = self.goal_hierarchy[goal_id].get('parent')
        if parent_id is not None:
            self._check_parent_goal_completion(parent_id)
        
        # Update metrics
        self.goals_completed += 1
            
        # Persist goals if file specified
        if self.goal_persistence_file:
            self._save_to_file()
            
        logger.info("Marked goal '%s' as complete", goal.description)
    
    def mark_goal_failed(self, goal_id: str, reason: Optional[str] = None) -> None:
        """
        Mark a goal as failed.
        
        Args:
            goal_id: ID of the goal
            reason: Optional reason for failure
            
        Raises:
            ValueError: If goal not found
        """
        if goal_id not in self.current_goals:
            raise ValueError(f"Goal with ID {goal_id} not found")
            
        # Mark as failed
        goal = self.current_goals[goal_id]
        goal.status = 'failed'
        goal.last_updated = time.time()
        goal.metadata['failure_time'] = time.time()
        
        if reason:
            goal.metadata['failure_reason'] = reason
        
        # Update any plans for this goal
        for plan in self.plans.values():
            if plan.goal_id == goal_id and plan.status in ['planned', 'in_progress']:
                plan.status = 'failed'
                plan.actual_end = time.time()
                if reason:
                    plan.metadata['failure_reason'] = reason
        
        # Mark child goals as failed
        children = self.goal_hierarchy[goal_id]['children']
        for child_id in children:
            if child_id in self.current_goals and self.current_goals[child_id].status == 'active':
                self.mark_goal_failed(child_id, reason="Parent goal failed")
        
        # Handle blocked goals
        self._handle_failed_dependency(goal_id)
        
        # Update parent progress
        parent_id = self.goal_hierarchy[goal_id].get('parent')
        if parent_id is not None:
            self._update_parent_goal_progress(parent_id)
        
        # Update metrics
        self.goals_failed += 1
            
        # Persist goals if file specified
        if self.goal_persistence_file:
            self._save_to_file()
            
        logger.info("Marked goal '%s' as failed%s", goal.description, 
                   f": {reason}" if reason else "")
    
    def _handle_failed_dependency(self, failed_goal_id: str) -> None:
        """
        Handle the failure of a goal that other goals may depend on.
        
        Args:
            failed_goal_id: ID of the failed goal
        """
        affected_goals = []
        
        # Find all goals that depend on the failed goal
        for goal_id, dependencies in self.goal_dependencies.items():
            if failed_goal_id in dependencies and goal_id in self.current_goals:
                goal = self.current_goals[goal_id]
                
                # Only affect active goals
                if goal.status != 'active':
                    continue
                    
                # Check if this is a critical dependency
                is_critical_dependency = goal.metadata.get('critical_dependencies', False)
                
                if is_critical_dependency:
                    # Mark as failed if critical
                    affected_goals.append((goal_id, "Critical dependency failed"))
                else:
                    # Otherwise, just remove the dependency and update status
                    if failed_goal_id in goal.dependencies:
                        goal.dependencies.remove(failed_goal_id)
                        
                    if goal_id in self.goal_dependencies and failed_goal_id in self.goal_dependencies[goal_id]:
                        self.goal_dependencies[goal_id].remove(failed_goal_id)
                        
                    goal.metadata['affected_by_failed_dependency'] = True
                    goal.calculate_urgency()
                    logger.warning("Goal '%s' depends on failed goal '%s' but is not marked as critical",
                                 goal.description, self.current_goals[failed_goal_id].description)
        
        # Mark affected goals as failed (separate loop to avoid modifying during iteration)
        for goal_id, reason in affected_goals:
            self.mark_goal_failed(goal_id, reason=reason)
    
    def abandon_goal(self, goal_id: str, reason: Optional[str] = None) -> None:
        """
        Abandon a goal (intentionally discontinued without completion or failure).
        
        Args:
            goal_id: ID of the goal
            reason: Optional reason for abandonment
            
        Raises:
            ValueError: If goal not found
        """
        if goal_id not in self.current_goals:
            raise ValueError(f"Goal with ID {goal_id} not found")
            
        # Mark as abandoned
        goal = self.current_goals[goal_id]
        goal.status = 'abandoned'
        goal.last_updated = time.time()
        goal.metadata['abandonment_time'] = time.time()
        
        if reason:
            goal.metadata['abandonment_reason'] = reason
        
        # Update any plans for this goal
        for plan in self.plans.values():
            if plan.goal_id == goal_id and plan.status in ['planned', 'in_progress']:
                plan.status = 'abandoned'
                plan.actual_end = time.time()
                if reason:
                    plan.metadata['abandonment_reason'] = reason
        
        # Mark all subgoals as abandoned as well
        children = self.goal_hierarchy[goal_id]['children']
        for child_id in children:
            if child_id in self.current_goals:
                child_goal = self.current_goals[child_id]
                if child_goal.status in ['active', 'pending']:
                    self.abandon_goal(child_id, reason=f"Parent goal abandoned: {reason}" if reason else "Parent goal abandoned")
        
        # Handle dependencies
        self._handle_abandoned_dependency(goal_id)
        
        # Update parent progress
        parent_id = self.goal_hierarchy[goal_id].get('parent')
        if parent_id is not None:
            self._update_parent_goal_progress(parent_id)
            
        # Persist goals if file specified
        if self.goal_persistence_file:
            self._save_to_file()
            
        logger.info("Abandoned goal '%s'%s", goal.description, 
                   f": {reason}" if reason else "")
    
    def _handle_abandoned_dependency(self, abandoned_goal_id: str) -> None:
        """
        Handle the abandonment of a goal that other goals may depend on.
        
        Args:
            abandoned_goal_id: ID of the abandoned goal
        """
        # Find all goals that depend on the abandoned goal
        for goal_id, dependencies in self.goal_dependencies.items():
            if abandoned_goal_id in dependencies and goal_id in self.current_goals:
                goal = self.current_goals[goal_id]
                
                # Only affect active goals
                if goal.status != 'active':
                    continue
                    
                # Remove the dependency
                if abandoned_goal_id in goal.dependencies:
                    goal.dependencies.remove(abandoned_goal_id)
                    
                if goal_id in self.goal_dependencies and abandoned_goal_id in self.goal_dependencies[goal_id]:
                    self.goal_dependencies[goal_id].remove(abandoned_goal_id)
                    
                goal.metadata['affected_by_abandoned_dependency'] = True
                goal.calculate_urgency()
                logger.info("Removed dependency on abandoned goal '%s' from goal '%s'",
                          self.current_goals[abandoned_goal_id].description,
                          goal.description)
    
    def _check_parent_goal_completion(self, parent_id: str) -> None:
        """
        Check if all children of parent goal are complete.
        
        Args:
            parent_id: ID of the parent goal
        """
        if parent_id not in self.current_goals or parent_id not in self.goal_hierarchy:
            return
            
        # Get children
        children = self.goal_hierarchy[parent_id]['children']
        if not children:
            return
            
        # Check if all children are complete
        all_complete = True
        for child_id in children:
            if child_id in self.current_goals and self.current_goals[child_id].status != 'complete':
                all_complete = False
                break
                
        # Mark parent as complete if all children are complete and no external dependencies
        if all_complete and len(self.goal_dependencies.get(parent_id, set())) == 0:
            self.mark_goal_complete(parent_id)
    
    def _prune_goals_if_needed(self) -> None:
        """
        Prune low-priority goals if we exceed the maximum number of active goals.
        """
        active_goals = [g for g in self.current_goals.values() 
                       if g.status == 'active']
                       
        if len(active_goals) <= self.max_active_goals:
            return
            
        # Sort by priority (ascending) and filter non-critical goals
        candidates_for_pruning = [g for g in active_goals 
                                 if not g.metadata.get('critical', False)]
        candidates_for_pruning.sort(key=lambda g: g.priority)
        
        # Prune lowest priority goals
        num_to_prune = len(active_goals) - self.max_active_goals
        pruned_count = 0
        
        for goal in candidates_for_pruning:
            if pruned_count >= num_to_prune:
                break
                
            if goal.priority < self.goal_pruning_threshold:
                self.abandon_goal(goal.id, reason="Pruned due to low priority")
                logger.info("Pruned low-priority goal '%s' (priority=%.2f)", 
                           goal.description, goal.priority)
                pruned_count += 1
    
    def create_plan(
        self, 
        goal_id: str, 
        actions: List[Dict[str, Any]], 
        expected_duration: float,
        expected_start: Optional[float] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Create a plan to achieve a goal.
        
        Args:
            goal_id: ID of the goal
            actions: List of actions, each a dict with at least 'description' and 'expected_duration'
            expected_duration: Expected total duration in seconds
            expected_start: Expected start timestamp, defaults to now if None
            metadata: Optional additional metadata for the plan
            
        Returns:
            Plan ID
            
        Raises:
            ValueError: If goal not found or actions invalid
        """
        if goal_id not in self.current_goals:
            raise ValueError(f"Goal with ID {goal_id} not found")
            
        if not actions:
            raise ValueError("Plan must include at least one action")
        
        # Validate actions format
        for i, action in enumerate(actions):
            if 'description' not in action:
                raise ValueError(f"Action {i} missing 'description'")
                
            if 'expected_duration' not in action:
                raise ValueError(f"Action {i} missing 'expected_duration'")
                
            if action.get('expected_duration', 0) <= 0:
                raise ValueError(f"Action {i} has invalid 'expected_duration': {action.get('expected_duration')}")
                
            # Initialize progress for each action
            if 'progress' not in action:
                action['progress'] = 0.0
        
        # Generate plan ID
        plan_id = f"plan_{self._next_plan_id}"
        self._next_plan_id += 1
        
        # Calculate expected start and end times
        now = time.time()
        if expected_start is None:
            expected_start = now
            
        expected_end = expected_start + expected_duration
        
        # Validate against goal deadline
        goal = self.current_goals[goal_id]
        if goal.deadline is not None and expected_end > goal.deadline:
            logger.warning("Plan expected end (%s) is after goal deadline (%s)",
                         time.ctime(expected_end), time.ctime(goal.deadline))
        
        # Create plan
        plan = Plan(
            id=plan_id,
            goal_id=goal_id,
            actions=actions,
            expected_start=expected_start,
            expected_end=expected_end,
            status='planned',
            metadata=metadata or {}
        )
        
        # Add to plans
        self.plans[plan_id] = plan
        
        # Update goal's expected duration if not already set
        if goal.expected_duration is None:
            goal.expected_duration = expected_duration
            goal.calculate_urgency()  # Recalculate urgency with new duration
            
        # Update goal metadata to reflect planning
        goal.metadata['has_plan'] = True
        goal.metadata['plan_id'] = plan_id
        
        # Persist goals if file specified
        if self.goal_persistence_file:
            self._save_to_file()
        
        logger.info("Created plan for goal '%s' with %d actions", 
                   self.current_goals[goal_id].description, len(actions))
        return plan_id
    
    def start_plan(self, plan_id: str) -> bool:
        """
        Start executing a plan.
        
        Args:
            plan_id: ID of the plan
            
        Returns:
            True if successfully started, False if already started or completed
            
        Raises:
            ValueError: If plan not found
        """
        if plan_id not in self.plans:
            raise ValueError(f"Plan with ID {plan_id} not found")
            
        plan = self.plans[plan_id]
        
        if plan.status != 'planned':
            logger.warning("Cannot start plan %s: already in status %s", plan_id, plan.status)
            return False
            
        # Mark as in progress
        plan.status = 'in_progress'
        plan.actual_start = time.time()
        
        # Persist goals if file specified
        if self.goal_persistence_file:
            self._save_to_file()
            
        logger.info("Started executing plan %s for goal '%s'", 
                   plan_id, self.current_goals[plan.goal_id].description)
        return True
    
    def update_plan_progress(self, plan_id: str, action_index: int, action_progress: float) -> None:
        """
        Update progress on a specific action in a plan.
        
        Args:
            plan_id: ID of the plan
            action_index: Index of the action being updated
            action_progress: Progress of the current action (0.0 to 1.0)
            
        Raises:
            ValueError: If plan not found or action index invalid
        """
        if plan_id not in self.plans:
            raise ValueError(f"Plan with ID {plan_id} not found")
            
        plan = self.plans[plan_id]
        
        if action_index < 0 or action_index >= len(plan.actions):
            raise ValueError(f"Invalid action index {action_index}, plan has {len(plan.actions)} actions")
            
        if plan.status != 'in_progress':
            plan.status = 'in_progress'
            plan.actual_start = time.time()
            
        # Update current action index if different
        if plan.current_action_index != action_index:
            plan.current_action_index = action_index
            
        # Update action progress
        action_progress = max(0.0, min(1.0, action_progress))
        plan.actions[action_index]['progress'] = action_progress
        
        # Update overall plan progress
        plan.update_progress(action_progress)
        
        # Update goal progress based on plan progress
        goal_id = plan.goal_id
        if goal_id in self.current_goals:
            self.update_goal_progress(goal_id, plan.progress)
            
        # Advance to next action if complete
        if action_progress >= 0.99 and action_index < len(plan.actions) - 1:
            plan.advance_action()
            
        # Mark plan as complete if all actions finished
        if action_index == len(plan.actions) - 1 and action_progress >= 0.99:
            plan.status = 'completed'
            plan.progress = 1.0
            plan.actual_end = time.time()
            
        # Persist goals if file specified
        if self.goal_persistence_file:
            self._save_to_file()
            
        logger.debug("Updated plan %s progress: action %d at %.2f%%, overall %.2f%%",
                    plan_id, action_index, action_progress * 100, plan.progress * 100)
    
    def complete_plan_action(self, plan_id: str) -> bool:
        """
        Mark the current action in a plan as complete and advance to the next action.
        
        Args:
            plan_id: ID of the plan
            
        Returns:
            True if successfully advanced to next action, False if at end of plan
            
        Raises:
            ValueError: If plan not found
        """
        if plan_id not in self.plans:
            raise ValueError(f"Plan with ID {plan_id} not found")
            
        plan = self.plans[plan_id]
        
        if plan.status != 'in_progress':
            if plan.status == 'planned':
                # Start the plan if it's not started yet
                self.start_plan(plan_id)
            else:
                logger.warning("Cannot advance plan %s: in status %s", plan_id, plan.status)
                return False
                
        # Mark current action as complete
        if plan.current_action_index < len(plan.actions):
            plan.actions[plan.current_action_index]['progress'] = 1.0
            
        # Try to advance to next action
        if plan.current_action_index >= len(plan.actions) - 1:
            # At the end of the plan, mark as complete
            plan.status = 'completed'
            plan.progress = 1.0
            plan.actual_end = time.time()
            
            # Mark goal as complete
            goal_id = plan.goal_id
            if goal_id in self.current_goals:
                self.mark_goal_complete(goal_id)
                
            # Persist goals if file specified
            if self.goal_persistence_file:
                self._save_to_file()
                
            logger.info("Completed plan %s for goal '%s'", 
                       plan_id, self.current_goals[plan.goal_id].description)
            return False
        else:
            # Advance to next action
            result = plan.advance_action()
            
            # Update goal progress
            goal_id = plan.goal_id
            if goal_id in self.current_goals:
                self.update_goal_progress(goal_id, plan.progress)
                
            # Persist goals if file specified
            if self.goal_persistence_file:
                self._save_to_file()
                
            logger.debug("Advanced plan %s to action %d of %d",
                        plan_id, plan.current_action_index + 1, len(plan.actions))
            return result
    
    def fail_plan(self, plan_id: str, reason: Optional[str] = None) -> None:
        """
        Mark a plan as failed.
        
        Args:
            plan_id: ID of the plan
            reason: Optional reason for failure
            
        Raises:
            ValueError: If plan not found
        """
        if plan_id not in self.plans:
            raise ValueError(f"Plan with ID {plan_id} not found")
            
        plan = self.plans[plan_id]
        
        # Mark as failed
        plan.status = 'failed'
        plan.actual_end = time.time()
        
        if reason:
            plan.metadata['failure_reason'] = reason
        
        # Add to plan history
        self._archive_plan(plan_id, 'failed')
            
        # Update metrics for the goal
        goal_id = plan.goal_id
        if goal_id in self.current_goals:
            failed_plans = self.current_goals[goal_id].metadata.get('failed_plans', 0)
            self.current_goals[goal_id].metadata['failed_plans'] = failed_plans + 1
            
            # If too many failed plans, mark goal as at risk
            if failed_plans >= 2:
                self.current_goals[goal_id].metadata['at_risk'] = True
                logger.warning("Goal '%s' marked as at risk due to multiple failed plans",
                             self.current_goals[goal_id].description)
                
        # Persist goals if file specified
        if self.goal_persistence_file:
            self._save_to_file()
            
        # Note: We don't automatically mark the goal as failed, as another plan might be created
        logger.info("Marked plan %s as failed%s", plan_id, f": {reason}" if reason else "")
    
    def _archive_plan(self, plan_id: str, final_status: str) -> None:
        """
        Archive a completed or failed plan for history tracking.
        
        Args:
            plan_id: ID of the plan
            final_status: Final status of the plan ('completed', 'failed', 'abandoned')
        """
        if plan_id not in self.plans:
            return
            
        plan = self.plans[plan_id]
        
        # Create archive record
        archived_plan = {
            'id': plan.id,
            'goal_id': plan.goal_id,
            'status': final_status,
            'expected_start': plan.expected_start,
            'expected_end': plan.expected_end,
            'actual_start': plan.actual_start or plan.expected_start,
            'actual_end': plan.actual_end or time.time(),
            'action_count': len(plan.actions),
            'progress': plan.progress,
            'metadata': plan.metadata
        }
        
        # Add to history
        self.plan_history.append(archived_plan)
        
        # Trim history if too long
        max_history = 100
        if len(self.plan_history) > max_history:
            self.plan_history = self.plan_history[-max_history:]
            
        logger.debug("Archived plan %s with final status %s", plan_id, final_status)
    
    def update_goal_priority(self, goal_id: str, priority: float) -> None:
        """
        Update the priority of a goal.
        
        Args:
            goal_id: ID of the goal
            priority: New priority value (0.0 to 1.0)
            
        Raises:
            ValueError: If goal not found
        """
        if goal_id not in self.current_goals:
            raise ValueError(f"Goal with ID {goal_id} not found")
            
        # Update priority
        goal = self.current_goals[goal_id]
        goal.priority = max(0.0, min(1.0, priority))
        goal.last_updated = time.time()
        
        # Recalculate urgency
        goal.calculate_urgency()
        
        # Update goal attention
        self._update_goal_attention()
        
        # Update children priorities if using inheritance
        self._update_children_priorities(goal_id)
        
        # Persist goals if file specified
        if self.goal_persistence_file:
            self._save_to_file()
            
        logger.debug("Updated goal '%s' priority to %.2f", goal.description, priority)
    
    def _update_children_priorities(self, goal_id: str) -> None:
        """
        Update priorities of child goals based on parent priority.
        
        Args:
            goal_id: ID of the parent goal
        """
        if goal_id not in self.goal_hierarchy:
            return
            
        children = self.goal_hierarchy[goal_id].get('children', [])
        if not children:
            return
            
        parent_priority = self.current_goals[goal_id].priority
        
        for child_id in children:
            if child_id in self.current_goals:
                child = self.current_goals[child_id]
                
                # Only update if child priority was derived from parent
                if child.metadata.get('inherited_priority', False):
                    # Apply inheritance factor
                    child_priority = parent_priority * self.subgoal_inheritance_factor
                    child.priority = child_priority
                    child.last_updated = time.time()
                    child.calculate_urgency()
                    
                    # Recursively update grandchildren
                    self._update_children_priorities(child_id)
    
    def update_goal_deadline(self, goal_id: str, deadline: Optional[float]) -> None:
        """
        Update the deadline of a goal.
        
        Args:
            goal_id: ID of the goal
            deadline: New deadline timestamp or None to remove deadline
            
        Raises:
            ValueError: If goal not found
        """
        if goal_id not in self.current_goals:
            raise ValueError(f"Goal with ID {goal_id} not found")
            
        # Update deadline
        goal = self.current_goals[goal_id]
        goal.deadline = deadline
        goal.last_updated = time.time()
        
        # Recalculate urgency
        goal.calculate_urgency()
        
        # Update deadlines for children if needed
        if deadline is not None:
            self._update_child_deadlines(goal_id, deadline)
        
        # Persist goals if file specified
        if self.goal_persistence_file:
            self._save_to_file()
            
        if deadline:
            deadline_str = datetime.datetime.fromtimestamp(deadline).strftime('%Y-%m-%d %H:%M:%S')
            logger.debug("Updated goal '%s' deadline to %s", goal.description, deadline_str)
        else:
            logger.debug("Removed deadline from goal '%s'", goal.description)
    
    def _update_child_deadlines(self, goal_id: str, parent_deadline: float) -> None:
        """
        Update deadlines of child goals based on parent deadline.
        
        Args:
            goal_id: ID of the parent goal
            parent_deadline: Parent goal deadline
        """
        if goal_id not in self.goal_hierarchy:
            return
            
        children = self.goal_hierarchy[goal_id].get('children', [])
        
        for child_id in children:
            if child_id not in self.current_goals:
                continue
                
            child = self.current_goals[child_id]
            
            # If child has a later deadline or no deadline, update it
            if child.deadline is None or child.deadline > parent_deadline:
                child.deadline = parent_deadline
                child.last_updated = time.time()
                child.calculate_urgency()
                
                # Recursively update grandchildren
                self._update_child_deadlines(child_id, parent_deadline)
    
    def update_goal_attributes(
        self, 
        goal_id: str, 
        attributes: Dict[str, Any]
    ) -> bool:
        """
        Update multiple attributes of a goal at once.
        
        Args:
            goal_id: ID of the goal
            attributes: Dictionary of attribute name -> new value
            
        Returns:
            True if successful, False if goal not found
            
        Valid attribute names:
            - 'description'
            - 'priority'
            - 'deadline'
            - 'expected_duration'
            - 'tags'
            - 'importance'
            - 'metadata' (will be merged, not replaced)
        """
        if goal_id not in self.current_goals:
            logger.warning(f"Cannot update attributes: Goal with ID {goal_id} not found")
            return False
            
        goal = self.current_goals[goal_id]
        changed = False
        
        # Update permitted attributes
        if 'description' in attributes:
            goal.description = attributes['description']
            changed = True
            
        if 'priority' in attributes:
            new_priority = max(0.0, min(1.0, attributes['priority']))
            if new_priority != goal.priority:
                goal.priority = new_priority
                self._update_children_priorities(goal_id)
                changed = True
                
        if 'deadline' in attributes:
            new_deadline = attributes['deadline']
            if new_deadline != goal.deadline:
                goal.deadline = new_deadline
                self._update_child_deadlines(goal_id, new_deadline)
                changed = True
                
        if 'expected_duration' in attributes:
            goal.expected_duration = attributes['expected_duration']
            changed = True
            
        if 'tags' in attributes:
            goal.tags = attributes['tags']
            changed = True
            
        if 'importance' in attributes:
            goal.importance = max(0.0, min(1.0, attributes['importance']))
            changed = True
            
        if 'metadata' in attributes and isinstance(attributes['metadata'], dict):
            # Merge metadata rather than replace
            goal.metadata.update(attributes['metadata'])
            changed = True
            
        if changed:
            goal.last_updated = time.time()
            goal.calculate_urgency()
            
            # Persist goals if file specified
            if self.goal_persistence_file:
                self._save_to_file()
                
            logger.debug("Updated attributes for goal '%s'", goal.description)
            
        return changed
    
    def get_current_intention(self) -> Optional[Dict[str, Any]]:
        """
        Get the highest priority active goal, representing the current intention.
        
        Returns:
            Dictionary with goal information or None if no active goals
        """
        # Update urgency scores if needed
        self._update_urgency_scores()
        
        active_goals = [g for g in self.current_goals.values() 
                       if g.status == 'active']
                       
        if not active_goals:
            return None
            
        # Apply adjustments based on urgency, importance, and other factors
        adjusted_goals = []
        now = time.time()
        
        for goal in active_goals:
            adjusted_score = goal.urgency
            
            # Adjust for dependencies
            if goal.dependencies:
                # Lower score for goals with unmet dependencies
                unmet_dependencies = sum(1 for dep_id in goal.dependencies 
                                        if dep_id in self.current_goals and 
                                        self.current_goals[dep_id].status != 'complete')
                if unmet_dependencies > 0:
                    dependency_factor = 1.0 / (1.0 + 0.3 * unmet_dependencies)
                    adjusted_score *= dependency_factor
            
            # Adjust for long-term importance
            importance_weight = 0.2  # Weight to give to importance vs urgency
            adjusted_score = (1 - importance_weight) * adjusted_score + importance_weight * goal.importance
            
            adjusted_goals.append((goal, adjusted_score))
            
        # Get highest scoring goal
        if not adjusted_goals:
            return None
            
        current_goal, _ = max(adjusted_goals, key=lambda x: x[1])
        
        # Get related plan if exists
        related_plans = [p for p in self.plans.values() 
                        if p.goal_id == current_goal.id and
                        p.status in ['planned', 'in_progress']]
        current_plan = related_plans[0] if related_plans else None
        
        # Get child goals
        children = []
        for child_id in self.goal_hierarchy[current_goal.id].get('children', []):
            if (child_id in self.current_goals and 
                self.current_goals[child_id].status == 'active'):
                children.append(self.current_goals[child_id])
                
        # Check if there are direct blockers
        active_blockers = []
        for dependency_id in current_goal.dependencies:
            if (dependency_id in self.current_goals and 
                self.current_goals[dependency_id].status != 'complete'):
                active_blockers.append(self.current_goals[dependency_id])
        
        return {
            'goal': current_goal,
            'plan': current_plan,
            'children': children,
            'blockers': active_blockers,
            'urgency': current_goal.urgency,
            'time_pressure': current_goal.get_time_pressure(),
            'attention_score': current_goal.attention_score
        }
    
    def get_goal_by_id(self, goal_id: str) -> Optional[Goal]:
        """
        Get a goal by its ID.
        
        Args:
            goal_id: ID of the goal
            
        Returns:
            Goal object or None if not found
        """
        return self.current_goals.get(goal_id)
    
    def get_all_active_goals(self) -> List[Goal]:
        """
        Get all currently active goals.
        
        Returns:
            List of Goal objects
        """
        return [g for g in self.current_goals.values() if g.status == 'active']
    
    def get_goals_by_tag(self, tag: str) -> List[Goal]:
        """
        Get all goals with a specific tag.
        
        Args:
            tag: Tag to search for
            
        Returns:
            List of Goal objects with the specified tag
        """
        return [g for g in self.current_goals.values() if tag in g.tags]
    
    def get_goal_tree(self, root_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Get a tree representation of goals starting from a root.
        
        Args:
            root_id: ID of the root goal, or None to get all top-level goals
            
        Returns:
            Dictionary representing the goal tree
        """
        def build_tree(goal_id):
            if goal_id not in self.current_goals:
                return None
                
            goal = self.current_goals[goal_id]
            children = self.goal_hierarchy[goal_id].get('children', [])
            dependencies = list(self.goal_dependencies.get(goal_id, set()))
            
            return {
                'goal': goal,
                'children': [build_tree(child_id) for child_id in children
                            if child_id in self.current_goals],
                'dependencies': dependencies,
                'has_plan': any(p.goal_id == goal_id and p.status in ['planned', 'in_progress'] 
                              for p in self.plans.values())
            }
            
        if root_id is not None:
            return build_tree(root_id)
            
        # Get all top-level goals
        top_level_goals = [goal_id for goal_id, hierarchy in self.goal_hierarchy.items()
                          if hierarchy.get('parent') is None and goal_id in self.current_goals]
                          
        return {
            'goals': [build_tree(goal_id) for goal_id in top_level_goals]
        }
    
    def get_prioritized_goals(self, n: int = 5) -> List[Goal]:
        """
        Get prioritized list of active goals.
        
        Args:
            n: Number of goals to return
            
        Returns:
            List of Goal objects
        """
        # Update urgency scores
        self._update_urgency_scores()
        
        active_goals = self.get_all_active_goals()
        
        # Sort by urgency (descending)
        active_goals.sort(key=lambda g: g.urgency, reverse=True)
        
        # Return top n goals
        return active_goals[:n]
    
    def get_blocked_goals(self) -> List[Dict[str, Any]]:
        """
        Get all goals that are blocked by dependencies.
        
        Returns:
            List of dictionaries with goal and its blockers
        """
        blocked_goals = []
        
        for goal in self.current_goals.values():
            if goal.status != 'active' or not goal.dependencies:
                continue
                
            # Check if any dependencies are not complete
            active_blockers = []
            for dependency_id in goal.dependencies:
                if (dependency_id in self.current_goals and 
                    self.current_goals[dependency_id].status != 'complete'):
                    active_blockers.append(self.current_goals[dependency_id])
                    
            if active_blockers:
                blocked_goals.append({
                    'goal': goal,
                    'blockers': active_blockers
                })
                
        return blocked_goals
    
    def get_urgent_goals(self, threshold: float = 0.7) -> List[Goal]:
        """
        Get goals with high urgency.
        
        Args:
            threshold: Urgency threshold (0.0 to 1.0)
            
        Returns:
            List of Goal objects with urgency above threshold
        """
        # Update urgency scores
        self._update_urgency_scores()
        
        urgent_goals = [g for g in self.current_goals.values() 
                       if g.status == 'active' and g.urgency >= threshold]
                       
        # Sort by urgency (descending)
        urgent_goals.sort(key=lambda g: g.urgency, reverse=True)
        
        return urgent_goals
    
    def get_active_plans(self) -> List[Plan]:
        """
        Get all plans that are currently planned or in progress.
        
        Returns:
            List of Plan objects
        """
        return [p for p in self.plans.values() 
               if p.status in ['planned', 'in_progress']]
    
    def allocate_resources(self, total_resources: float = 1.0) -> Dict[str, float]:
        """
        Allocate resources to active goals.
        
        Uses a softmax-based allocation strategy based on adjusted priorities.
        
        Args:
            total_resources: Total resources to allocate (typically 1.0)
            
        Returns:
            Dictionary mapping goal IDs to allocated resources
        """
        # Update urgency scores first
        self._update_urgency_scores()
        
        active_goals = self.get_all_active_goals()
        
        if not active_goals:
            return {}
            
        # Get adjusted priorities based on urgency and dependencies
        goal_priorities = []
        
        for goal in active_goals:
            # Start with urgency
            priority = goal.urgency
            
            # Adjust for dependencies
            dependency_count = len(goal.dependencies)
            if dependency_count > 0:
                # Reduce priority for goals with unmet dependencies
                unmet_dependencies = sum(1 for dep_id in goal.dependencies 
                                        if dep_id in self.current_goals and 
                                        self.current_goals[dep_id].status != 'complete')
                
                if unmet_dependencies > 0:
                    dependency_factor = 1.0 / (1.0 + 0.2 * unmet_dependencies)
                    priority *= dependency_factor
            
            # Adjust for resource efficiency
            if goal.expected_duration and goal.expected_duration > 0:
                # Favor goals with higher progress per time unit
                remaining_work = 1.0 - goal.completion
                if remaining_work > 0:
                    efficiency = min(1.0, self.resource_allocation_factor / (remaining_work * goal.expected_duration))
                    priority *= (1.0 + efficiency)
            
            goal_priorities.append((goal.id, priority))
            
        # Apply softmax to get resource allocation
        priorities = np.array([p for _, p in goal_priorities])
        exp_priorities = np.exp(priorities / self.prioritization_temperature)
        softmax_priorities = exp_priorities / np.sum(exp_priorities)
        
        # Allocate resources proportionally
        allocated_resources = {}
        for i, (goal_id, _) in enumerate(goal_priorities):
            allocated_resources[goal_id] = float(softmax_priorities[i] * total_resources)
            
        # Update internal state
        self.allocated_resources = allocated_resources
        
        # Update attention scores
        for i, (goal_id, _) in enumerate(goal_priorities):
            if goal_id in self.current_goals:
                self.current_goals[goal_id].attention_score = float(softmax_priorities[i])
                
        logger.debug("Allocated resources across %d active goals", len(active_goals))
        return allocated_resources
    
    def _update_urgency_scores(self) -> None:
        """
        Update urgency scores for all active goals.
        Only runs if sufficient time has passed since last update.
        """
        # Calculate time elapsed since last update
        current_time = time.time()
        dt = current_time - self._last_urgency_update
        
        # Only update if enough time has passed
        if dt < self.urgency_update_interval:
            return
            
        self._last_urgency_update = current_time
        
        # Update urgency for all active goals
        for goal in self.current_goals.values():
            if goal.status == 'active':
                goal.calculate_urgency()
                
        logger.debug("Updated urgency scores for all active goals")
    
    def _update_goal_attention(self) -> None:
        """
        Update attention distribution across goals based on urgency and priority.
        """
        # First, allocate resources which also updates attention scores
        self.allocate_resources()
        
        # Update the attention distribution dictionary
        for goal_id, goal in self.current_goals.items():
            if goal.status == 'active':
                self.goal_attention[goal_id] = goal.attention_score
    
    def automated_goal_maintenance(self) -> Dict[str, Any]:
        """
        Perform automated goal maintenance tasks.
        
        This includes:
        - Checking for missed deadlines
        - Updating priorities based on deadlines
        - Pruning completed goals older than threshold
        - Marking stalled goals
        
        Returns:
            Dictionary with maintenance results
        """
        now = time.time()
        results = {
            'missed_deadlines': 0,
            'priorities_updated': 0,
            'goals_pruned': 0,
            'stalled_goals': 0
        }
        
        # Skip if recently checked
        if now - self._last_goal_check < 600:  # 10 minutes
            return results
            
        self._last_goal_check = now
        
        # 1. Check for missed deadlines
        for goal_id, goal in list(self.current_goals.items()):
            if goal.status != 'active':
                continue
                
            if goal.deadline is not None and now > goal.deadline:
                # Mark as failed due to missed deadline
                self.mark_goal_failed(goal_id, reason="Missed deadline")
                results['missed_deadlines'] += 1
                
        # 2. Update priorities based on deadlines
        for goal_id, goal in self.current_goals.items():
            if goal.status != 'active' or goal.deadline is None:
                continue
                
            time_remaining = goal.deadline - now
            if 0 < time_remaining < 48 * 3600:  # Less than 48 hours
                # Calculate new priority
                urgency_boost = min(0.3, 12 * 3600 / max(3600, time_remaining))
                new_priority = min(1.0, goal.priority + urgency_boost)
                
                if new_priority > goal.priority + 0.05:  # Only update if significant change
                    self.update_goal_priority(goal_id, new_priority)
                    results['priorities_updated'] += 1
                    
        # 3. Prune very old completed/failed goals
        prune_threshold = now - 30 * 86400  # 30 days
        for goal_id, goal in list(self.current_goals.items()):
            if goal.status in ['complete', 'failed', 'abandoned'] and goal.metadata.get('completion_time', now) < prune_threshold:
                # Remove from current goals
                del self.current_goals[goal_id]
                
                # Remove from hierarchy
                if goal_id in self.goal_hierarchy:
                    # If it has a parent, remove from parent's children
                    parent_id = self.goal_hierarchy[goal_id].get('parent')
                    if parent_id and parent_id in self.goal_hierarchy:
                        if goal_id in self.goal_hierarchy[parent_id].get('children', []):
                            self.goal_hierarchy[parent_id]['children'].remove(goal_id)
                    
                    # Remove the goal's hierarchy entry
                    del self.goal_hierarchy[goal_id]
                
                # Remove from dependencies
                if goal_id in self.goal_dependencies:
                    del self.goal_dependencies[goal_id]
                
                # Remove from all other goals' dependencies
                for other_id, dependencies in self.goal_dependencies.items():
                    if goal_id in dependencies:
                        dependencies.remove(goal_id)
                
                # Remove from all goals' blockers
                for other_goal in self.current_goals.values():
                    if goal_id in other_goal.blockers:
                        other_goal.blockers.remove(goal_id)
                    if goal_id in other_goal.dependencies:
                        other_goal.dependencies.remove(goal_id)
                        
                results['goals_pruned'] += 1
                
        # 4. Check for stalled goals
        for goal_id, goal in self.current_goals.items():
            if goal.status != 'active':
                continue
                
            # Check if goal is stalled
            if goal.is_stalled():
                # Mark as stalled in metadata
                goal.metadata['stalled'] = True
                
                # Log the stalled goal
                logger.warning("Goal '%s' appears to be stalled (age: %.1f days, progress: %.1f%%)",
                             goal.description, (now - goal.created_at) / 86400, goal.completion * 100)
                             
                results['stalled_goals'] += 1
                
        # Persist changes if file specified
        if self.goal_persistence_file and results['goals_pruned'] > 0:
            self._save_to_file()
                
        return results
    
    def check_deadlines(self) -> List[Dict[str, Any]]:
        """
        Check for approaching or missed deadlines.
        
        Returns:
            List of dictionaries with deadline information
        """
        now = time.time()
        deadline_info = []
        
        for goal in self.current_goals.values():
            if goal.status != 'active' or goal.deadline is None:
                continue
                
            time_remaining = goal.deadline - now
            
            if time_remaining <= 0:
                # Missed deadline
                deadline_info.append({
                    'goal_id': goal.id,
                    'description': goal.description,
                    'status': 'missed',
                    'overdue_by': -time_remaining,
                    'completion': goal.completion,
                    'urgency': goal.urgency
                })
            elif time_remaining < 24 * 3600:  # Less than 24 hours
                # Approaching deadline
                deadline_info.append({
                    'goal_id': goal.id,
                    'description': goal.description,
                    'status': 'approaching',
                    'time_remaining': time_remaining,
                    'completion': goal.completion,
                    'urgency': goal.urgency
                })
                
        # Sort by urgency (missed deadlines first, then by time remaining)
        deadline_info.sort(key=lambda x: float('inf') if x['status'] == 'approaching' else x['overdue_by'], 
                          reverse=True)
        
        return deadline_info
    
    def plan_urgent_goals(self) -> List[Dict[str, Any]]:
        """
        Check deadlines and prioritize urgent goals.
        
        Returns:
            List of dictionaries with urgent goals and their time pressure
        """
        # Skip if recently updated
        now = time.time()
        if now - self._last_plan_update < self.plan_update_interval:
            return []
            
        self._last_plan_update = now
        
        # Find goals with approaching deadlines
        active_goals = self.get_all_active_goals()
        urgent_goals = []
        
        for goal in active_goals:
            if goal.deadline is None:
                continue
                
            time_remaining = goal.get_time_remaining()
            
            if time_remaining is not None and 0 < time_remaining < 24*3600:
                time_pressure = goal.get_time_pressure()
                
                # Check if there's an active plan
                has_active_plan = any(p.goal_id == goal.id and p.status in ['planned', 'in_progress'] 
                                     for p in self.plans.values())
                
                if time_pressure > 0.7 or not has_active_plan:
                    urgent_goals.append({
                        'goal': goal,
                        'time_remaining': time_remaining,
                        'time_pressure': time_pressure,
                        'has_plan': has_active_plan,
                        'urgency': goal.urgency
                    })
                    
                    # Update goal metadata to mark as urgent
                    goal.metadata['urgent'] = True
                    goal.metadata['time_remaining'] = time_remaining
        
        # Log urgent goals
        if urgent_goals:
            goals_str = ", ".join([f"'{g['goal'].description}'" for g in urgent_goals])
            logger.info("Identified %d urgent goals with approaching deadlines: %s", 
                       len(urgent_goals), goals_str)
            
        # Sort by time pressure (descending)
        urgent_goals.sort(key=lambda x: x['time_pressure'], reverse=True)
        
        return urgent_goals
    
    def find_goal_by_description(self, description_fragment: str, active_only: bool = True) -> List[Goal]:
        """
        Find goals that match a description fragment.
        
        Args:
            description_fragment: Text to search for in goal descriptions
            active_only: If True, only returns active goals
            
        Returns:
            List of matched Goal objects
        """
        if active_only:
            return [g for g in self.current_goals.values() 
                  if description_fragment.lower() in g.description.lower() and g.status == 'active']
        else:
            return [g for g in self.current_goals.values() 
                  if description_fragment.lower() in g.description.lower()]
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        Get statistics about the current goals and plans.
        
        Returns:
            Dictionary with statistics
        """
        active_goals = self.get_all_active_goals()
        
        # Count goals by status
        status_counts = defaultdict(int)
        for goal in self.current_goals.values():
            status_counts[goal.status] += 1
            
        # Count goals by tag
        tag_counts = defaultdict(int)
        for goal in self.current_goals.values():
            for tag in goal.tags:
                tag_counts[tag] += 1
                
        # Calculate average completion
        avg_completion = sum(g.completion for g in active_goals) / len(active_goals) if active_goals else 0.0
        
        # Count plans by status
        plan_status_counts = defaultdict(int)
        for plan in self.plans.values():
            plan_status_counts[plan.status] += 1
            
        # Calculate average plan progress
        active_plans = [p for p in self.plans.values() if p.status == 'in_progress']
        avg_plan_progress = sum(p.progress for p in active_plans) / len(active_plans) if active_plans else 0.0
        
        # Calculate goal tree depth
        max_depth = 0
        for goal_id in self.current_goals:
            depth = 0
            current_id = goal_id
            while True:
                parent_id = self.goal_hierarchy.get(current_id, {}).get('parent')
                if parent_id is None:
                    break
                depth += 1
                current_id = parent_id
            max_depth = max(max_depth, depth)
        
        # Calculate success rate
        success_rate = self.goals_completed / max(1, self.goals_completed + self.goals_failed)
        
        return {
            'total_goals': len(self.current_goals),
            'active_goals': len(active_goals),
            'total_plans': len(self.plans),
            'active_plans': len(active_plans),
            'goals_created': self.goals_created,
            'goals_completed': self.goals_completed,
            'goals_failed': self.goals_failed,
            'success_rate': success_rate,
            'goal_status_counts': dict(status_counts),
            'plan_status_counts': dict(plan_status_counts),
            'avg_completion': avg_completion,
            'avg_plan_progress': avg_plan_progress,
            'tag_counts': dict(tag_counts),
            'max_goal_tree_depth': max_depth,
            'deadlines': {
                'upcoming_24h': sum(1 for g in active_goals if g.deadline and 0 < (g.deadline - time.time()) < 24*3600),
                'upcoming_week': sum(1 for g in active_goals if g.deadline and 24*3600 < (g.deadline - time.time()) < 7*24*3600),
                'past_deadline': sum(1 for g in active_goals if g.deadline and (g.deadline - time.time()) <= 0)
            },
            'dependency_metrics': {
                'total_dependencies': sum(len(deps) for deps in self.goal_dependencies.values()),
                'avg_dependencies': sum(len(deps) for deps in self.goal_dependencies.values()) / max(1, len(self.goal_dependencies)),
                'blocked_goals': sum(1 for g in active_goals if g.dependencies)
            }
        }
    
    def _save_to_file(self) -> None:
        """
        Save goals and plans to a persistence file.
        """
        if not self.goal_persistence_file:
            return
            
        try:
            # Create directory if it doesn't exist
            directory = os.path.dirname(self.goal_persistence_file)
            if directory and not os.path.exists(directory):
                os.makedirs(directory, exist_ok=True)
            
            # Convert goals to dictionaries
            goals_dict = {goal_id: goal.to_dict() for goal_id, goal in self.current_goals.items()}
            
            # Convert plans to dictionaries
            plans_dict = {plan_id: plan.to_dict() for plan_id, plan in self.plans.items()}
            
            # Convert dependencies to serializable format
            dependencies_dict = {}
            for goal_id, deps in self.goal_dependencies.items():
                dependencies_dict[goal_id] = list(deps)
            
            # Create save data
            save_data = {
                'goals': goals_dict,
                'plans': plans_dict,
                'goal_hierarchy': self.goal_hierarchy,
                'goal_dependencies': dependencies_dict,
                'goal_attention': self.goal_attention,
                'allocated_resources': self.allocated_resources,
                'plan_history': self.plan_history,
                'goals_created': self.goals_created,
                'goals_completed': self.goals_completed,
                'goals_failed': self.goals_failed,
                '_next_plan_id': self._next_plan_id,
                '_last_urgency_update': self._last_urgency_update,
                '_last_plan_update': self._last_plan_update,
                '_last_goal_check': self._last_goal_check,
                'timestamp': time.time()
            }
            
            # Save to file
            with open(self.goal_persistence_file, 'w') as f:
                json.dump(save_data, f, indent=2)
                
            logger.debug("Saved intentionality system state to %s", self.goal_persistence_file)
        except Exception as e:
            logger.error("Failed to save intentionality system state: %s", str(e))
    
    def _load_from_file(self) -> bool:
        """
        Load goals and plans from a persistence file.
        
        Returns:
            True if successfully loaded, False otherwise
        """
        if not self.goal_persistence_file:
            return False
            
        try:
            with open(self.goal_persistence_file, 'r') as f:
                data = json.load(f)
                
            # Load goals
            self.current_goals = {}
            for goal_id, goal_dict in data.get('goals', {}).items():
                self.current_goals[goal_id] = Goal.from_dict(goal_dict)
                
            # Load plans
            self.plans = {}
            for plan_id, plan_dict in data.get('plans', {}).items():
                self.plans[plan_id] = Plan.from_dict(plan_dict)
                
            # Load goal hierarchy
            self.goal_hierarchy = data.get('goal_hierarchy', {})
            
            # Load goal dependencies
            dependencies_dict = data.get('goal_dependencies', {})
            self.goal_dependencies = defaultdict(set)
            for goal_id, deps in dependencies_dict.items():
                self.goal_dependencies[goal_id] = set(deps)
            
            # Load goal attention
            self.goal_attention = data.get('goal_attention', {})
            
            # Load allocated resources
            self.allocated_resources = data.get('allocated_resources', {})
            
            # Load plan history
            self.plan_history = data.get('plan_history', [])
            
            # Load metrics
            self.goals_created = data.get('goals_created', 0)
            self.goals_completed = data.get('goals_completed', 0)
            self.goals_failed = data.get('goals_failed', 0)
            
            # Load internal state
            self._next_plan_id = data.get('_next_plan_id', 0)
            self._last_urgency_update = data.get('_last_urgency_update', time.time())
            self._last_plan_update = data.get('_last_plan_update', time.time())
            self._last_goal_check = data.get('_last_goal_check', time.time())
            
            logger.info("Loaded intentionality system state from %s: %d goals, %d plans",
                       self.goal_persistence_file, len(self.current_goals), len(self.plans))
            return True
        except FileNotFoundError:
            logger.info("No persistence file found at %s, starting with empty state", 
                       self.goal_persistence_file)
            return False
        except Exception as e:
            logger.error("Failed to load intentionality system state: %s", str(e))
            return False
    
    def save_state(self) -> Dict[str, Any]:
        """
        Save the current state of the intentionality system.
        
        Returns:
            Dictionary containing the complete state
        """
        # Convert Goal objects to dicts
        goals_dict = {}
        for goal_id, goal in self.current_goals.items():
            goals_dict[goal_id] = goal.to_dict()
            
        # Convert Plan objects to dicts
        plans_dict = {}
        for plan_id, plan in self.plans.items():
            plans_dict[plan_id] = plan.to_dict()
            
        # Convert dependencies to serializable format
        dependencies_dict = {}
        for goal_id, deps in self.goal_dependencies.items():
            dependencies_dict[goal_id] = list(deps)
            
        return {
            'current_goals': goals_dict,
            'goal_hierarchy': self.goal_hierarchy,
            'plans': plans_dict,
            'plan_history': self.plan_history,
            'goal_dependencies': dependencies_dict,
            'goal_attention': self.goal_attention,
            'allocated_resources': self.allocated_resources,
            'goals_created': self.goals_created,
            'goals_completed': self.goals_completed,
            'goals_failed': self.goals_failed,
            'max_active_goals': self.max_active_goals,
            'planning_horizon': self.planning_horizon,
            'goal_pruning_threshold': self.goal_pruning_threshold,
            'prioritization_temperature': self.prioritization_temperature,
            'urgency_update_interval': self.urgency_update_interval,
            'plan_update_interval': self.plan_update_interval,
            'urgency_factor': self.urgency_factor,
            'subgoal_inheritance_factor': self.subgoal_inheritance_factor,
            'resource_allocation_factor': self.resource_allocation_factor,
            '_last_urgency_update': self._last_urgency_update,
            '_last_plan_update': self._last_plan_update,
            '_last_goal_check': self._last_goal_check,
            '_next_plan_id': self._next_plan_id
        }
    
    def load_state(self, state: Dict[str, Any]) -> None:
        """
        Load a previously saved state.
        
        Args:
            state: Dictionary containing the state to load
        """
        # Load configuration parameters
        self.max_active_goals = state.get('max_active_goals', self.max_active_goals)
        self.planning_horizon = state.get('planning_horizon', self.planning_horizon)
        self.goal_pruning_threshold = state.get('goal_pruning_threshold', self.goal_pruning_threshold)
        self.prioritization_temperature = state.get('prioritization_temperature', self.prioritization_temperature)
        self.urgency_update_interval = state.get('urgency_update_interval', self.urgency_update_interval)
        self.plan_update_interval = state.get('plan_update_interval', self.plan_update_interval)
        self.urgency_factor = state.get('urgency_factor', self.urgency_factor)
        self.subgoal_inheritance_factor = state.get('subgoal_inheritance_factor', self.subgoal_inheritance_factor)
        self.resource_allocation_factor = state.get('resource_allocation_factor', self.resource_allocation_factor)
        
        # Load internal state
        self._last_urgency_update = state.get('_last_urgency_update', time.time())
        self._last_plan_update = state.get('_last_plan_update', time.time())
        self._last_goal_check = state.get('_last_goal_check', time.time())
        self._next_plan_id = state.get('_next_plan_id', 0)
        
        # Load metrics
        self.goals_created = state.get('goals_created', 0)
        self.goals_completed = state.get('goals_completed', 0)
        self.goals_failed = state.get('goals_failed', 0)
        
        # Load goal hierarchy
        self.goal_hierarchy = state.get('goal_hierarchy', {})
        
        # Load goal attention
        self.goal_attention = state.get('goal_attention', {})
        
        # Load allocated resources
        self.allocated_resources = state.get('allocated_resources', {})
        
        # Load plan history
        self.plan_history = state.get('plan_history', [])
        
        # Convert goal dicts to Goal objects
        self.current_goals = {}
        for goal_id, goal_dict in state.get('current_goals', {}).items():
            self.current_goals[goal_id] = Goal.from_dict(goal_dict)
            
        # Convert plan dicts to Plan objects
        self.plans = {}
        for plan_id, plan_dict in state.get('plans', {}).items():
            self.plans[plan_id] = Plan.from_dict(plan_dict)
            
        # Convert dependencies to set
        dependencies_dict = state.get('goal_dependencies', {})
        self.goal_dependencies = defaultdict(set)
        for goal_id, deps in dependencies_dict.items():
            self.goal_dependencies[goal_id] = set(deps)
            
        logger.info("Loaded intentionality system state with %d goals, %d plans, and %d dependencies",
                   len(self.current_goals), len(self.plans), 
                   sum(len(deps) for deps in self.goal_dependencies.values()))


# Export the classes
__all__ = ['Goal', 'Plan', 'IntentionalitySystem']