#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Emergent Consciousness Lattice Module for ULTRA

This module implements the Emergent Consciousness Lattice component of the ULTRA
architecture, which creates a global workspace with self-modeling capabilities.
Components include:
- Self-Awareness Module
- Intentionality System
- Integrated Information Matrix
- Attentional Awareness
- Global Workspace Theory Implementation

The Emergent Consciousness Lattice is experimental in nature and inspired by 
theories of consciousness from neuroscience and philosophy, such as Integrated 
Information Theory and Global Workspace Theory.

Authors: <AUTHORS>
"""

import numpy as np
import scipy.stats as stats
import torch
import torch.nn as nn
import torch.nn.functional as F
import networkx as nx
import logging
from typing import Dict, List, Tuple, Set, Optional, Union, Callable, Any
from dataclasses import dataclass, field
import time
import uuid
import math
import heapq
from collections import defaultdict, deque

# Configure logging
logger = logging.getLogger(__name__)

class SelfAwarenessModule:
    """
    Maintains an evolving model of the system's own capabilities, limitations, and knowledge state.
    
    This enables more effective self-monitoring, error detection, and adaptive behavior.
    Implements metacognition about the system's own strengths and weaknesses.
    """
    
    def __init__(
        self, 
        capability_domains: List[str] = None,
        knowledge_domains: List[str] = None,
        learning_rate_capability: float = 0.1,
        learning_rate_uncertainty: float = 0.2,
        learning_rate_coverage: float = 0.05,
        learning_rate_expertise: float = 0.05,
        memory_capacity: int = 1000
    ):
        """
        Initialize the Self-Awareness Module.
        
        Args:
            capability_domains: List of capability domains to track (e.g., 'language', 'reasoning')
            knowledge_domains: List of knowledge domains to track (e.g., 'physics', 'mathematics')
            learning_rate_capability: Learning rate for capability estimates
            learning_rate_uncertainty: Learning rate for uncertainty estimates
            learning_rate_coverage: Learning rate for knowledge coverage updates
            learning_rate_expertise: Learning rate for expertise level updates
            memory_capacity: Maximum number of performance records to store
        """
        # Set default domains if none provided
        self.capability_domains = capability_domains or [
            'language', 'reasoning', 'vision', 'knowledge_breadth', 'memory', 'learning_rate',
            'problem_solving', 'creativity', 'adaptation', 'planning'
        ]
        
        self.knowledge_domains = knowledge_domains or [
            'physics', 'mathematics', 'computer_science', 'biology', 'chemistry',
            'economics', 'psychology', 'philosophy', 'history', 'linguistics'
        ]
        
        # Initialize capability model: dict mapping capability -> (performance, uncertainty)
        self.capability_model = {
            domain: {'performance': 0.5, 'uncertainty': 0.5} 
            for domain in self.capability_domains
        }
        
        # Initialize knowledge state: dict mapping domain -> (coverage, expertise)
        self.knowledge_state = {
            domain: {'coverage': 0.5, 'expertise': 0.5} 
            for domain in self.knowledge_domains
        }
        
        # Performance history: list of dicts with task_type, performance, timestamp
        self.performance_history = []
        
        # Confidence model: dict mapping query_type -> (predicted_confidences, actual_correctness)
        self.confidence_model = defaultdict(lambda: {'predicted_confidence': [], 'actual_correctness': []})
        
        # Store learning rates
        self.alpha = learning_rate_capability
        self.beta = learning_rate_uncertainty
        self.gamma = learning_rate_coverage
        self.delta = learning_rate_expertise
        
        # Performance memory capacity
        self.memory_capacity = memory_capacity
        
        # Last self-assessment timestamp
        self.last_assessment_time = time.time()
        
        logger.info("Self-Awareness Module initialized with %d capability domains and %d knowledge domains",
                    len(self.capability_domains), len(self.knowledge_domains))
        
    def update_capability_model(self, task_result: Dict[str, Any]) -> Dict[str, Dict[str, float]]:
        """
        Update self-model based on task performance.
        
        Args:
            task_result: Dictionary containing 'task_type', 'performance', 'timestamp' (optional)
            
        Returns:
            Updated capability model
        """
        task_type = task_result['task_type']
        performance = task_result['performance']
        timestamp = task_result.get('timestamp', time.time())
        
        if task_type in self.capability_model:
            # Get current estimates
            old_estimate = self.capability_model[task_type]['performance']
            old_uncertainty = self.capability_model[task_type]['uncertainty']
            
            # Compute new performance estimate using exponential moving average
            new_estimate = (1 - self.alpha) * old_estimate + self.alpha * performance
            
            # Update uncertainty based on deviation between predicted and actual performance
            deviation = abs(old_estimate - performance)
            new_uncertainty = (1 - self.beta) * old_uncertainty + self.beta * deviation
            
            # Update capability model
            self.capability_model[task_type]['performance'] = new_estimate
            self.capability_model[task_type]['uncertainty'] = new_uncertainty
        else:
            # If new capability, add it with initial values
            self.capability_model[task_type] = {
                'performance': performance,
                'uncertainty': 0.5
            }
            self.capability_domains.append(task_type)
        
        # Add to performance history
        self.performance_history.append({
            'task_type': task_type,
            'performance': performance,
            'timestamp': timestamp
        })
        
        # Trim history if exceeds capacity
        if len(self.performance_history) > self.memory_capacity:
            self.performance_history = self.performance_history[-self.memory_capacity:]
            
        logger.debug("Updated capability model for %s: perf=%.3f, uncertainty=%.3f", 
                    task_type, self.capability_model[task_type]['performance'],
                    self.capability_model[task_type]['uncertainty'])
        
        return self.capability_model
    
    def update_knowledge_state(self, domain_updates: Dict[str, Dict[str, float]]) -> Dict[str, Dict[str, float]]:
        """
        Update knowledge state based on new information or learning experiences.
        
        Args:
            domain_updates: Dict mapping domain -> {'coverage_delta': float, 'expertise_delta': float}
            
        Returns:
            Updated knowledge state
        """
        for domain, updates in domain_updates.items():
            if domain in self.knowledge_state:
                # Get current values
                current_coverage = self.knowledge_state[domain]['coverage']
                current_expertise = self.knowledge_state[domain]['expertise']
                
                # Update with deltas
                coverage_delta = updates.get('coverage_delta', 0)
                expertise_delta = updates.get('expertise_delta', 0)
                
                # Apply updates with learning rates
                new_coverage = current_coverage + self.gamma * coverage_delta
                new_expertise = current_expertise + self.delta * expertise_delta
                
                # Clamp values to [0, 1]
                new_coverage = max(0.0, min(1.0, new_coverage))
                new_expertise = max(0.0, min(1.0, new_expertise))
                
                self.knowledge_state[domain]['coverage'] = new_coverage
                self.knowledge_state[domain]['expertise'] = new_expertise
            else:
                # If new domain, add it with initial values
                coverage_delta = updates.get('coverage_delta', 0)
                expertise_delta = updates.get('expertise_delta', 0)
                
                initial_coverage = max(0.0, min(1.0, 0.5 + self.gamma * coverage_delta))
                initial_expertise = max(0.0, min(1.0, 0.5 + self.delta * expertise_delta))
                
                self.knowledge_state[domain] = {
                    'coverage': initial_coverage,
                    'expertise': initial_expertise
                }
                self.knowledge_domains.append(domain)
                
        logger.debug("Updated knowledge state for %d domains", len(domain_updates))
        return self.knowledge_state
    
    def update_confidence_model(self, query_type: str, confidence: float, actual_correctness: float) -> None:
        """
        Update confidence calibration model.
        
        Args:
            query_type: Type of query or task
            confidence: Predicted confidence (0 to 1)
            actual_correctness: Actual correctness (0 to 1)
        """
        self.confidence_model[query_type]['predicted_confidence'].append(confidence)
        self.confidence_model[query_type]['actual_correctness'].append(actual_correctness)
        
        # Trim if too many entries
        max_entries = 1000
        if len(self.confidence_model[query_type]['predicted_confidence']) > max_entries:
            self.confidence_model[query_type]['predicted_confidence'] = \
                self.confidence_model[query_type]['predicted_confidence'][-max_entries:]
            self.confidence_model[query_type]['actual_correctness'] = \
                self.confidence_model[query_type]['actual_correctness'][-max_entries:]
                
        logger.debug("Updated confidence model for %s", query_type)
    
    def get_calibrated_confidence(self, query_type: str, raw_confidence: float) -> float:
        """
        Adjust raw confidence based on calibration model.
        
        Args:
            query_type: Type of query or task
            raw_confidence: Raw confidence estimate (0 to 1)
            
        Returns:
            Calibrated confidence (0 to 1)
        """
        if query_type not in self.confidence_model or \
           len(self.confidence_model[query_type]['predicted_confidence']) < 10:
            return raw_confidence
            
        # Get historical data
        predicted = np.array(self.confidence_model[query_type]['predicted_confidence'])
        actual = np.array(self.confidence_model[query_type]['actual_correctness'])
        
        if len(predicted) < 10:
            return raw_confidence
            
        try:
            # Use isotonic regression for calibration
            from sklearn.isotonic import IsotonicRegression
            calibrator = IsotonicRegression(out_of_bounds='clip')
            calibrator.fit(predicted, actual)
            
            # Predict calibrated confidence
            calibrated_confidence = calibrator.predict([raw_confidence])[0]
            return float(calibrated_confidence)
        except ImportError:
            # Fall back to simpler method if sklearn not available
            # Find most similar confidences and their outcomes
            similar_indices = np.argsort(np.abs(predicted - raw_confidence))[:10]
            if len(similar_indices) > 0:
                return float(np.mean(actual[similar_indices]))
            return raw_confidence
    
    def identify_limitations(self, threshold: float = 0.6) -> List[Dict[str, Any]]:
        """
        Identify system limitations based on capability model.
        
        Args:
            threshold: Performance threshold below which a capability is considered limited
            
        Returns:
            List of limitation dictionaries with capability, level, description
        """
        limitations = []
        
        for capability, values in self.capability_model.items():
            performance = values['performance']
            uncertainty = values['uncertainty']
            
            if performance < threshold:
                limitations.append({
                    'capability': capability,
                    'level': performance,
                    'uncertainty': uncertainty,
                    'description': f"Limited {capability} capability"
                })
                
        # Sort by performance (ascending)
        limitations.sort(key=lambda x: x['level'])
        
        return limitations
    
    def identify_strengths(self, threshold: float = 0.8) -> List[Dict[str, Any]]:
        """
        Identify system strengths based on capability model.
        
        Args:
            threshold: Performance threshold above which a capability is considered a strength
            
        Returns:
            List of strength dictionaries with capability, level, description
        """
        strengths = []
        
        for capability, values in self.capability_model.items():
            performance = values['performance']
            uncertainty = values['uncertainty']
            
            if performance > threshold:
                strengths.append({
                    'capability': capability,
                    'level': performance,
                    'uncertainty': uncertainty,
                    'description': f"Strong {capability} capability"
                })
                
        # Sort by performance (descending)
        strengths.sort(key=lambda x: x['level'], reverse=True)
        
        return strengths
    
    def knowledge_gaps(self, threshold: float = 0.4) -> List[Dict[str, Any]]:
        """
        Identify knowledge gaps based on knowledge state.
        
        Args:
            threshold: Coverage threshold below which a domain is considered a gap
            
        Returns:
            List of gap dictionaries with domain, coverage, expertise
        """
        gaps = []
        
        for domain, values in self.knowledge_state.items():
            coverage = values['coverage']
            expertise = values['expertise']
            
            if coverage < threshold:
                gaps.append({
                    'domain': domain,
                    'coverage': coverage,
                    'expertise': expertise,
                    'description': f"Limited knowledge in {domain}"
                })
                
        # Sort by coverage (ascending)
        gaps.sort(key=lambda x: x['coverage'])
        
        return gaps
    
    def self_assess(self, force: bool = False) -> Dict[str, Any]:
        """
        Perform a comprehensive self-assessment.
        Only runs if sufficient time has passed since last assessment or if forced.
        
        Args:
            force: Force assessment even if recent assessment exists
            
        Returns:
            Dictionary containing self-assessment results
        """
        current_time = time.time()
        time_since_last = current_time - self.last_assessment_time
        
        # Only reassess if more than 1 hour passed or forced
        if time_since_last < 3600 and not force:
            logger.debug("Skipping self-assessment (last one was %.2f minutes ago)", 
                       time_since_last / 60)
            return {}
            
        self.last_assessment_time = current_time
        
        # Perform comprehensive assessment
        assessment = {
            'timestamp': current_time,
            'capabilities': {
                'all': self.capability_model,
                'top_strengths': self.identify_strengths()[:5],
                'main_limitations': self.identify_limitations()[:5],
                'average_performance': np.mean([v['performance'] for v in self.capability_model.values()]),
                'average_uncertainty': np.mean([v['uncertainty'] for v in self.capability_model.values()])
            },
            'knowledge': {
                'all': self.knowledge_state,
                'top_domains': sorted([(d, v['expertise']) for d, v in self.knowledge_state.items()], 
                                     key=lambda x: x[1], reverse=True)[:5],
                'knowledge_gaps': self.knowledge_gaps()[:5],
                'average_coverage': np.mean([v['coverage'] for v in self.knowledge_state.values()]),
                'average_expertise': np.mean([v['expertise'] for v in self.knowledge_state.values()])
            },
            'confidence': {
                'calibration_domains': list(self.confidence_model.keys()),
                'overall_calibration': self._calculate_overall_calibration()
            },
            'performance_trends': self._analyze_performance_trends()
        }
        
        logger.info("Completed self-assessment: avg_perf=%.3f, avg_knowledge=%.3f", 
                   assessment['capabilities']['average_performance'],
                   assessment['knowledge']['average_expertise'])
        
        return assessment
    
    def _calculate_overall_calibration(self) -> float:
        """
        Calculate overall confidence calibration score.
        
        Returns:
            Calibration score between 0 (poor) and 1 (perfect)
        """
        all_predicted = []
        all_actual = []
        
        for domain_data in self.confidence_model.values():
            all_predicted.extend(domain_data['predicted_confidence'])
            all_actual.extend(domain_data['actual_correctness'])
            
        if not all_predicted:
            return 0.5  # Default value if no data
            
        # Calculate Brier score: mean squared difference between 
        # predicted probabilities and actual outcomes
        brier_score = np.mean([(p - a)**2 for p, a in zip(all_predicted, all_actual)])
        
        # Convert to calibration score (0 to 1)
        # Brier score of 0 is perfect, 0.25 is worst for binary outcomes
        calibration_score = 1.0 - min(1.0, 4.0 * brier_score)
        
        return float(calibration_score)
    
    def _analyze_performance_trends(self) -> Dict[str, Any]:
        """
        Analyze trends in performance history.
        
        Returns:
            Dictionary with trend analysis
        """
        if len(self.performance_history) < 10:
            return {'sufficient_data': False}
            
        # Group by task type
        task_performances = defaultdict(list)
        task_timestamps = defaultdict(list)
        
        for entry in self.performance_history:
            task_type = entry['task_type']
            task_performances[task_type].append(entry['performance'])
            task_timestamps[task_type].append(entry['timestamp'])
            
        trends = {}
        
        for task_type, performances in task_performances.items():
            if len(performances) < 5:
                continue
                
            # Calculate simple linear trend
            timestamps = task_timestamps[task_type]
            timestamps_normalized = [(t - timestamps[0]) / (3600 * 24) for t in timestamps]  # Convert to days
            
            if len(set(timestamps_normalized)) < 2:
                continue
                
            try:
                slope, intercept, r_value, p_value, std_err = stats.linregress(
                    timestamps_normalized, performances
                )
                
                trends[task_type] = {
                    'slope': slope,  # Change per day
                    'r_value': r_value,
                    'p_value': p_value,
                    'improvement': slope > 0,
                    'significant': p_value < 0.05,
                    'data_points': len(performances)
                }
            except:
                logger.warning("Failed to calculate trend for %s", task_type)
                
        return {
            'sufficient_data': True,
            'task_trends': trends,
            'improving_areas': [t for t, v in trends.items() if v['improvement'] and v['significant']],
            'declining_areas': [t for t, v in trends.items() if not v['improvement'] and v['significant']]
        }
    
    def get_self_description(self) -> str:
        """
        Generate a human-readable description of the system's capabilities and limitations.
        
        Returns:
            String description
        """
        # Get top capabilities and limitations
        strengths = self.identify_strengths()[:3]
        limitations = self.identify_limitations()[:3]
        
        # Format description
        description = "System Self-Assessment:\n\n"
        description += "Primary capabilities:\n"
        
        for strength in strengths:
            description += f"- {strength['capability'].replace('_', ' ').title()}: {strength['level']:.1f}/1.0\n"
            
        if limitations:
            description += "\nLimitations:\n"
            for limitation in limitations:
                description += f"- {limitation['capability'].replace('_', ' ').title()}: {limitation['level']:.1f}/1.0\n"
                
        # Add knowledge domains
        top_domains = sorted([(d, v['expertise']) for d, v in self.knowledge_state.items()], 
                            key=lambda x: x[1], reverse=True)[:3]
        
        description += "\nKnowledge strengths:\n"
        for domain, expertise in top_domains:
            description += f"- {domain.replace('_', ' ').title()}: {expertise:.1f}/1.0\n"
            
        return description
    
    def save_state(self) -> Dict[str, Any]:
        """
        Save the current state of the self-awareness module.
        
        Returns:
            Dictionary containing the complete state
        """
        return {
            'capability_model': self.capability_model,
            'knowledge_state': self.knowledge_state, 
            'performance_history': self.performance_history,
            'confidence_model': dict(self.confidence_model),
            'last_assessment_time': self.last_assessment_time
        }
    
    def load_state(self, state: Dict[str, Any]) -> None:
        """
        Load a previously saved state.
        
        Args:
            state: Dictionary containing the state to load
        """
        self.capability_model = state.get('capability_model', self.capability_model)
        self.knowledge_state = state.get('knowledge_state', self.knowledge_state)
        self.performance_history = state.get('performance_history', self.performance_history)
        
        # Convert confidence model back to defaultdict
        conf_model = state.get('confidence_model', {})
        self.confidence_model = defaultdict(lambda: {'predicted_confidence': [], 'actual_correctness': []})
        for k, v in conf_model.items():
            self.confidence_model[k] = v
            
        self.last_assessment_time = state.get('last_assessment_time', time.time())
        
        logger.info("Loaded self-awareness state with %d capability domains and %d knowledge domains",
                    len(self.capability_model), len(self.knowledge_state))


class IntentionalitySystem:
    """
    Implements goal-directed behavior and planning, allowing the system to maintain
    and pursue hierarchical objectives over time.
    
    This represents a form of artificial intentionality, where the system's processes
    are directed toward achieving specific outcomes.
    """
    
    @dataclass
    class Goal:
        """Data class representing a goal in the system"""
        id: str
        description: str
        priority: float
        status: str  # 'active', 'pending', 'complete', 'failed', 'abandoned'
        created_at: float
        deadline: Optional[float] = None
        parent_id: Optional[str] = None
        metadata: Dict[str, Any] = field(default_factory=dict)
        completion: float = 0.0  # 0.0 to 1.0
        
    @dataclass
    class Plan:
        """Data class representing a plan to achieve a goal"""
        id: str
        goal_id: str
        actions: List[Dict[str, Any]]
        expected_start: float
        expected_end: float
        status: str  # 'planned', 'in_progress', 'completed', 'failed'
        metadata: Dict[str, Any] = field(default_factory=dict)
        
    def __init__(
        self,
        max_active_goals: int = 10,
        planning_horizon: int = 5,
        goal_pruning_threshold: float = 0.2,
        prioritization_temperature: float = 0.5
    ):
        """
        Initialize the Intentionality System.
        
        Args:
            max_active_goals: Maximum number of simultaneously active goals
            planning_horizon: Number of planning steps to look ahead
            goal_pruning_threshold: Priority threshold for pruning low-priority goals
            prioritization_temperature: Temperature parameter for softmax priority calculation
        """
        self.current_goals: Dict[str, IntentionalitySystem.Goal] = {}
        self.goal_hierarchy: Dict[str, Dict[str, Union[str, List[str]]]] = {}
        self.plans: Dict[str, IntentionalitySystem.Plan] = {}
        
        self.max_active_goals = max_active_goals
        self.planning_horizon = planning_horizon
        self.goal_pruning_threshold = goal_pruning_threshold
        self.prioritization_temperature = prioritization_temperature
        
        # Internal state
        self._last_plan_update = time.time()
        self._next_plan_id = 0
        
        logger.info("Intentionality System initialized with max_active_goals=%d, planning_horizon=%d",
                    max_active_goals, planning_horizon)
        
    def set_goal(
        self, 
        description: str, 
        priority: float = 0.5, 
        deadline: Optional[float] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Add a goal to the current goals.
        
        Args:
            description: Description of the goal
            priority: Priority of the goal (0 to 1)
            deadline: Optional deadline (timestamp)
            metadata: Optional additional information about the goal
            
        Returns:
            Goal ID
        """
        # Generate unique ID
        goal_id = str(uuid.uuid4())
        
        # Create goal object
        goal = self.Goal(
            id=goal_id,
            description=description,
            priority=priority,
            status='active',
            created_at=time.time(),
            deadline=deadline,
            parent_id=None,
            metadata=metadata or {}
        )
        
        # Add to current goals
        self.current_goals[goal_id] = goal
        
        # Initialize in goal hierarchy
        self.goal_hierarchy[goal_id] = {
            'parent': None,
            'children': []
        }
        
        # Check if we need to prune goals
        self._prune_goals_if_needed()
        
        logger.info("Created new goal: %s, priority=%.2f", description, priority)
        return goal_id
    
    def set_subgoal(
        self, 
        parent_goal_id: str, 
        description: str, 
        priority: Optional[float] = None,
        deadline: Optional[float] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Add a subgoal to an existing goal.
        
        Args:
            parent_goal_id: ID of the parent goal
            description: Description of the subgoal
            priority: Priority of the subgoal (0 to 1), if None inherits from parent
            deadline: Optional deadline (timestamp)
            metadata: Optional additional information about the subgoal
            
        Returns:
            Subgoal ID
            
        Raises:
            ValueError: If parent goal not found
        """
        # Find parent goal
        if parent_goal_id not in self.current_goals:
            raise ValueError(f"Parent goal with ID {parent_goal_id} not found")
            
        parent_goal = self.current_goals[parent_goal_id]
        
        # Use parent priority if not specified
        if priority is None:
            priority = parent_goal.priority
            
        # Use parent deadline if not specified
        if deadline is None and parent_goal.deadline is not None:
            deadline = parent_goal.deadline
        
        # Create subgoal
        subgoal_id = str(uuid.uuid4())
        
        subgoal = self.Goal(
            id=subgoal_id,
            description=description,
            priority=priority,
            status='active',
            created_at=time.time(),
            deadline=deadline,
            parent_id=parent_goal_id,
            metadata=metadata or {}
        )
        
        # Add to current goals
        self.current_goals[subgoal_id] = subgoal
        
        # Update goal hierarchy
        self.goal_hierarchy[subgoal_id] = {
            'parent': parent_goal_id,
            'children': []
        }
        self.goal_hierarchy[parent_goal_id]['children'].append(subgoal_id)
        
        logger.info("Created subgoal '%s' under parent '%s'", 
                   description, parent_goal.description)
        return subgoal_id
    
    def update_goal_progress(self, goal_id: str, completion: float) -> None:
        """
        Update the completion progress of a goal.
        
        Args:
            goal_id: ID of the goal
            completion: Completion percentage (0.0 to 1.0)
            
        Raises:
            ValueError: If goal not found
        """
        if goal_id not in self.current_goals:
            raise ValueError(f"Goal with ID {goal_id} not found")
            
        # Update completion
        self.current_goals[goal_id].completion = max(0.0, min(1.0, completion))
        
        # If goal is complete, mark it
        if completion >= 0.99:
            self.mark_goal_complete(goal_id)
            
        # Update parent goal progress
        self._update_parent_goal_progress(goal_id)
        
        logger.debug("Updated goal '%s' progress to %.2f", 
                    self.current_goals[goal_id].description, completion)
        
    def _update_parent_goal_progress(self, goal_id: str) -> None:
        """
        Update parent goal progress based on children's progress.
        
        Args:
            goal_id: ID of the child goal
        """
        parent_id = self.goal_hierarchy[goal_id].get('parent')
        if parent_id is None:
            return
            
        # Calculate parent progress as average of children's progress
        children = self.goal_hierarchy[parent_id]['children']
        if not children:
            return
            
        children_progress = [self.current_goals[child_id].completion 
                            for child_id in children
                            if child_id in self.current_goals]
                            
        if children_progress:
            parent_completion = sum(children_progress) / len(children_progress)
            self.current_goals[parent_id].completion = parent_completion
            
            # Recursively update grandparent
            self._update_parent_goal_progress(parent_id)
    
    def mark_goal_complete(self, goal_id: str) -> None:
        """
        Mark a goal as complete.
        
        Args:
            goal_id: ID of the goal
            
        Raises:
            ValueError: If goal not found
        """
        if goal_id not in self.current_goals:
            raise ValueError(f"Goal with ID {goal_id} not found")
            
        # Mark as complete
        self.current_goals[goal_id].status = 'complete'
        self.current_goals[goal_id].completion = 1.0
        
        # Check if parent goal is complete
        parent_id = self.goal_hierarchy[goal_id].get('parent')
        if parent_id is not None:
            self._check_parent_goal_completion(parent_id)
            
        logger.info("Marked goal '%s' as complete", self.current_goals[goal_id].description)
    
    def mark_goal_failed(self, goal_id: str) -> None:
        """
        Mark a goal as failed.
        
        Args:
            goal_id: ID of the goal
            
        Raises:
            ValueError: If goal not found
        """
        if goal_id not in self.current_goals:
            raise ValueError(f"Goal with ID {goal_id} not found")
            
        # Mark as failed
        self.current_goals[goal_id].status = 'failed'
        
        # Update parent progress
        parent_id = self.goal_hierarchy[goal_id].get('parent')
        if parent_id is not None:
            self._update_parent_goal_progress(parent_id)
            
        logger.info("Marked goal '%s' as failed", self.current_goals[goal_id].description)
    
    def _check_parent_goal_completion(self, parent_id: str) -> None:
        """
        Check if all children of parent goal are complete.
        
        Args:
            parent_id: ID of the parent goal
        """
        # Get children
        children = self.goal_hierarchy[parent_id]['children']
        if not children:
            return
            
        # Check if all children are complete
        all_complete = True
        for child_id in children:
            if child_id in self.current_goals and self.current_goals[child_id].status != 'complete':
                all_complete = False
                break
                
        # Mark parent as complete if all children are complete
        if all_complete:
            self.mark_goal_complete(parent_id)
    
    def _prune_goals_if_needed(self) -> None:
        """
        Prune low-priority goals if we exceed the maximum number of active goals.
        """
        active_goals = [g for g in self.current_goals.values() 
                       if g.status == 'active']
                       
        if len(active_goals) <= self.max_active_goals:
            return
            
        # Sort by priority (ascending)
        active_goals.sort(key=lambda g: g.priority)
        
        # Prune lowest priority goals
        num_to_prune = len(active_goals) - self.max_active_goals
        for i in range(num_to_prune):
            if i < len(active_goals) and active_goals[i].priority < self.goal_pruning_threshold:
                goal_id = active_goals[i].id
                self.current_goals[goal_id].status = 'abandoned'
                logger.info("Pruned low-priority goal '%s' (priority=%.2f)", 
                           active_goals[i].description, active_goals[i].priority)
    
    def create_plan(self, goal_id: str, actions: List[Dict[str, Any]], 
                   expected_duration: float) -> str:
        """
        Create a plan to achieve a goal.
        
        Args:
            goal_id: ID of the goal
            actions: List of actions, each a dict with at least 'description' and 'expected_duration'
            expected_duration: Expected total duration in seconds
            
        Returns:
            Plan ID
            
        Raises:
            ValueError: If goal not found
        """
        if goal_id not in self.current_goals:
            raise ValueError(f"Goal with ID {goal_id} not found")
            
        # Generate plan ID
        plan_id = f"plan_{self._next_plan_id}"
        self._next_plan_id += 1
        
        # Calculate expected start and end times
        now = time.time()
        expected_end = now + expected_duration
        
        # Create plan
        plan = self.Plan(
            id=plan_id,
            goal_id=goal_id,
            actions=actions,
            expected_start=now,
            expected_end=expected_end,
            status='planned'
        )
        
        # Add to plans
        self.plans[plan_id] = plan
        
        logger.info("Created plan for goal '%s' with %d actions", 
                   self.current_goals[goal_id].description, len(actions))
        return plan_id
    
    def update_plan_status(self, plan_id: str, status: str) -> None:
        """
        Update the status of a plan.
        
        Args:
            plan_id: ID of the plan
            status: New status ('planned', 'in_progress', 'completed', 'failed')
            
        Raises:
            ValueError: If plan not found or status invalid
        """
        if plan_id not in self.plans:
            raise ValueError(f"Plan with ID {plan_id} not found")
            
        valid_statuses = ['planned', 'in_progress', 'completed', 'failed']
        if status not in valid_statuses:
            raise ValueError(f"Invalid status: {status}. Must be one of {valid_statuses}")
            
        # Update status
        self.plans[plan_id].status = status
        
        # If completed or failed, update goal progress
        if status == 'completed':
            goal_id = self.plans[plan_id].goal_id
            self.update_goal_progress(goal_id, 1.0)
        elif status == 'failed':
            goal_id = self.plans[plan_id].goal_id
            self.mark_goal_failed(goal_id)
            
        logger.debug("Updated plan %s status to %s", plan_id, status)
    
    def get_current_intention(self) -> Optional[Dict[str, Any]]:
        """
        Get the highest priority active goal, representing the current intention.
        
        Returns:
            Dictionary with goal information or None if no active goals
        """
        active_goals = [g for g in self.current_goals.values() 
                       if g.status == 'active']
                       
        if not active_goals:
            return None
            
        # Get highest priority goal
        current_intention = max(active_goals, key=lambda g: g.priority)
        
        # Get related plan if exists
        related_plans = [p for p in self.plans.values() 
                        if p.goal_id == current_intention.id and
                        p.status in ['planned', 'in_progress']]
        current_plan = related_plans[0] if related_plans else None
        
        return {
            'goal': current_intention,
            'plan': current_plan,
            'children': [self.current_goals[child_id] 
                        for child_id in self.goal_hierarchy[current_intention.id]['children']
                        if child_id in self.current_goals and
                        self.current_goals[child_id].status == 'active']
        }
    
    def get_all_active_goals(self) -> List[Goal]:
        """
        Get all currently active goals.
        
        Returns:
            List of Goal objects
        """
        return [g for g in self.current_goals.values() if g.status == 'active']
    
    def get_goal_tree(self, root_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Get a tree representation of goals starting from a root.
        
        Args:
            root_id: ID of the root goal, or None to get all top-level goals
            
        Returns:
            Dictionary representing the goal tree
        """
        def build_tree(goal_id):
            if goal_id not in self.current_goals:
                return None
                
            goal = self.current_goals[goal_id]
            children = self.goal_hierarchy[goal_id]['children']
            
            return {
                'goal': goal,
                'children': [build_tree(child_id) for child_id in children
                            if child_id in self.current_goals]
            }
            
        if root_id is not None:
            return build_tree(root_id)
            
        # Get all top-level goals
        top_level_goals = [goal_id for goal_id, hierarchy in self.goal_hierarchy.items()
                          if hierarchy['parent'] is None and goal_id in self.current_goals]
                          
        return {
            'goals': [build_tree(goal_id) for goal_id in top_level_goals]
        }
    
    def get_prioritized_goals(self, n: int = 5) -> List[Goal]:
        """
        Get prioritized list of active goals.
        
        Args:
            n: Number of goals to return
            
        Returns:
            List of Goal objects
        """
        active_goals = self.get_all_active_goals()
        
        # Apply deadline adjustments
        now = time.time()
        adjusted_goals = []
        
        for goal in active_goals:
            # Adjust priority based on deadline proximity
            adjusted_priority = goal.priority
            
            if goal.deadline is not None:
                time_remaining = goal.deadline - now
                if time_remaining > 0:
                    # Increase priority as deadline approaches
                    urgency_factor = 1.0 + max(0, min(2.0, 24*3600 / time_remaining))
                    adjusted_priority *= urgency_factor
                else:
                    # Past deadline, maximum priority
                    adjusted_priority *= 3.0
                    
            adjusted_goals.append((goal, adjusted_priority))
            
        # Sort by adjusted priority (descending)
        adjusted_goals.sort(key=lambda x: x[1], reverse=True)
        
        # Return top n goals
        return [g[0] for g in adjusted_goals[:n]]
    
    def plan_urgent_goals(self) -> None:
        """
        Check deadlines and prioritize urgent goals.
        """
        # Skip if recently updated
        now = time.time()
        if now - self._last_plan_update < 300:  # 5 minutes
            return
            
        self._last_plan_update = now
        
        # Find goals with approaching deadlines
        active_goals = self.get_all_active_goals()
        urgent_goals = []
        
        for goal in active_goals:
            if goal.deadline is None:
                continue
                
            time_remaining = goal.deadline - now
            
            # If less than 24 hours remaining and no active plan
            if 0 < time_remaining < 24*3600:
                has_active_plan = any(p.goal_id == goal.id and p.status in ['planned', 'in_progress'] 
                                     for p in self.plans.values())
                                     
                if not has_active_plan:
                    urgent_goals.append(goal)
                    
        # Log urgent goals
        if urgent_goals:
            goals_str = ", ".join([f"'{g.description}'" for g in urgent_goals])
            logger.info("Identified %d urgent goals with approaching deadlines: %s", 
                       len(urgent_goals), goals_str)
    
    def save_state(self) -> Dict[str, Any]:
        """
        Save the current state of the intentionality system.
        
        Returns:
            Dictionary containing the complete state
        """
        # Convert Goal objects to dicts
        goals_dict = {}
        for goal_id, goal in self.current_goals.items():
            goals_dict[goal_id] = {
                'id': goal.id,
                'description': goal.description,
                'priority': goal.priority,
                'status': goal.status,
                'created_at': goal.created_at,
                'deadline': goal.deadline,
                'parent_id': goal.parent_id,
                'metadata': goal.metadata,
                'completion': goal.completion
            }
            
        # Convert Plan objects to dicts
        plans_dict = {}
        for plan_id, plan in self.plans.items():
            plans_dict[plan_id] = {
                'id': plan.id,
                'goal_id': plan.goal_id,
                'actions': plan.actions,
                'expected_start': plan.expected_start,
                'expected_end': plan.expected_end,
                'status': plan.status,
                'metadata': plan.metadata
            }
            
        return {
            'current_goals': goals_dict,
            'goal_hierarchy': self.goal_hierarchy,
            'plans': plans_dict,
            'max_active_goals': self.max_active_goals,
            'planning_horizon': self.planning_horizon,
            'goal_pruning_threshold': self.goal_pruning_threshold,
            'prioritization_temperature': self.prioritization_temperature,
            '_last_plan_update': self._last_plan_update,
            '_next_plan_id': self._next_plan_id
        }
    
    def load_state(self, state: Dict[str, Any]) -> None:
        """
        Load a previously saved state.
        
        Args:
            state: Dictionary containing the state to load
        """
        # Load configuration parameters
        self.max_active_goals = state.get('max_active_goals', self.max_active_goals)
        self.planning_horizon = state.get('planning_horizon', self.planning_horizon)
        self.goal_pruning_threshold = state.get('goal_pruning_threshold', self.goal_pruning_threshold)
        self.prioritization_temperature = state.get('prioritization_temperature', self.prioritization_temperature)
        
        # Load internal state
        self._last_plan_update = state.get('_last_plan_update', time.time())
        self._next_plan_id = state.get('_next_plan_id', 0)
        
        # Load goal hierarchy
        self.goal_hierarchy = state.get('goal_hierarchy', {})
        
        # Convert goal dicts to Goal objects
        self.current_goals = {}
        for goal_id, goal_dict in state.get('current_goals', {}).items():
            self.current_goals[goal_id] = self.Goal(
                id=goal_dict['id'],
                description=goal_dict['description'],
                priority=goal_dict['priority'],
                status=goal_dict['status'],
                created_at=goal_dict['created_at'],
                deadline=goal_dict.get('deadline'),
                parent_id=goal_dict.get('parent_id'),
                metadata=goal_dict.get('metadata', {}),
                completion=goal_dict.get('completion', 0.0)
            )
            
        # Convert plan dicts to Plan objects
        self.plans = {}
        for plan_id, plan_dict in state.get('plans', {}).items():
            self.plans[plan_id] = self.Plan(
                id=plan_dict['id'],
                goal_id=plan_dict['goal_id'],
                actions=plan_dict['actions'],
                expected_start=plan_dict['expected_start'],
                expected_end=plan_dict['expected_end'],
                status=plan_dict['status'],
                metadata=plan_dict.get('metadata', {})
            )
            
        logger.info("Loaded intentionality system state with %d goals and %d plans",
                   len(self.current_goals), len(self.plans))


class IntegratedInformationMatrix:
    """
    Implements principles from Integrated Information Theory to maximize
    the system's internal information integration.
    
    Creates a unified information space where different parts of the system
    can share and combine information effectively.
    """
    
    def __init__(
        self,
        num_modules: int = 10,
        phi_threshold: float = 0.1,
        connection_density: float = 0.3,
        integration_learning_rate: float = 0.01
    ):
        """
        Initialize the Integrated Information Matrix.
        
        Args:
            num_modules: Number of subsystems/modules
            phi_threshold: Threshold for significant integrated information
            connection_density: Initial density of connections between modules
            integration_learning_rate: Learning rate for adjusting integration
        """
        self.num_modules = num_modules
        self.phi_threshold = phi_threshold
        self.integration_learning_rate = integration_learning_rate
        
        # Generate initial random connectivity matrix with specified density
        self.connectivity = np.random.random((num_modules, num_modules))
        self.connectivity = (self.connectivity < connection_density).astype(float)
        np.fill_diagonal(self.connectivity, 0)  # No self-connections
        
        # Initialize module states
        self.module_states = np.zeros(num_modules)
        
        # Initialize module information content
        self.information_content = np.ones(num_modules)
        
        # Track history of Φ values
        self.phi_history = []
        
        # Create directed graph for information flow
        self.flow_graph = nx.DiGraph()
        for i in range(num_modules):
            self.flow_graph.add_node(i, state=0.0, info_content=1.0)
        
        for i in range(num_modules):
            for j in range(num_modules):
                if self.connectivity[i, j] > 0:
                    self.flow_graph.add_edge(i, j, weight=self.connectivity[i, j])
                    
        logger.info("Integrated Information Matrix initialized with %d modules and %.2f connection density",
                    num_modules, connection_density)
    
    def compute_mutual_information(self, p_xy: np.ndarray) -> float:
        """
        Compute mutual information between two sets of variables.
        
        Args:
            p_xy: Joint probability distribution
            
        Returns:
            Mutual information value
        """
        # Compute marginal distributions
        p_x = np.sum(p_xy, axis=1)
        p_y = np.sum(p_xy, axis=0)
        
        # Compute mutual information
        mi = 0.0
        for i in range(p_xy.shape[0]):
            for j in range(p_xy.shape[1]):
                if p_xy[i, j] > 0:
                    mi += p_xy[i, j] * np.log2(p_xy[i, j] / (p_x[i] * p_y[j]))
                    
        return max(0.0, mi)  # Ensure non-negative
    
    def compute_conditional_mutual_information(
        self, 
        x: np.ndarray, 
        y: np.ndarray, 
        z: np.ndarray
    ) -> float:
        """
        Compute conditional mutual information I(X;Y|Z).
        
        Args:
            x: Values of variable X
            y: Values of variable Y
            z: Values of conditioning variable Z
            
        Returns:
            Conditional mutual information value
        """
        # For computational simplicity, we'll use a discrete estimation approach
        # First, discretize the continuous values if needed
        bins = min(10, len(x) // 5)  # Rule of thumb for bin count
        
        if bins < 2:
            return 0.0
            
        x_binned = np.digitize(x, np.linspace(min(x), max(x), bins))
        y_binned = np.digitize(y, np.linspace(min(y), max(y), bins))
        z_binned = np.digitize(z, np.linspace(min(z), max(z), bins))
        
        # Compute conditional mutual information
        cmi = 0.0
        
        # Get unique values of Z
        z_values = np.unique(z_binned)
        
        for z_val in z_values:
            # Get indices where Z = z_val
            indices = (z_binned == z_val)
            
            if np.sum(indices) < 2:
                continue
                
            # Calculate P(Z = z_val)
            p_z = np.mean(indices)
            
            # Calculate mutual information for this Z value
            x_given_z = x_binned[indices]
            y_given_z = y_binned[indices]
            
            # Create joint probability table
            joint_counts = np.zeros((bins, bins))
            for i, j in zip(x_given_z, y_given_z):
                joint_counts[i-1, j-1] += 1
                
            # Normalize to get probability
            joint_prob = joint_counts / max(1, np.sum(joint_counts))
            
            # Compute mutual information for this z value
            mi_given_z = self.compute_mutual_information(joint_prob)
            
            # Add weighted contribution to conditional MI
            cmi += p_z * mi_given_z
            
        return max(0.0, cmi)  # Ensure non-negative
    
    def compute_phi(self, states: np.ndarray, partition: Tuple[List[int], List[int]]) -> float:
        """
        Compute integrated information Φ for a given partition.
        
        Args:
            states: Current states of all modules
            partition: Tuple of two lists containing indices for partition
            
        Returns:
            Φ value for the partition
        """
        s1, s2 = partition
        
        if len(s1) == 0 or len(s2) == 0:
            return 0.0
            
        # Extract states for each partition
        x1 = states[s1]
        x2 = states[s2]
        
        # Generate time-series data for estimation
        # In a real system, this would use actual system data
        # Here we'll simulate with a simple AR process
        num_samples = 1000
        x1_ts = np.zeros((len(s1), num_samples))
        x2_ts = np.zeros((len(s2), num_samples))
        
        # Initial values
        x1_ts[:, 0] = x1
        x2_ts[:, 0] = x2
        
        # Generate samples
        for t in range(1, num_samples):
            # Update based on connectivity
            for i, idx1 in enumerate(s1):
                x1_ts[i, t] = 0.5 * x1_ts[i, t-1]  # Auto-correlation
                
                # Add influence from connected modules
                for j, idx2 in enumerate(s2):
                    x1_ts[i, t] += 0.1 * self.connectivity[idx2, idx1] * x2_ts[j, t-1]
                    
            for i, idx2 in enumerate(s2):
                x2_ts[i, t] = 0.5 * x2_ts[i, t-1]  # Auto-correlation
                
                # Add influence from connected modules
                for j, idx1 in enumerate(s1):
                    x2_ts[i, t] += 0.1 * self.connectivity[idx1, idx2] * x1_ts[j, t-1]
                    
            # Add noise
            x1_ts[:, t] += 0.1 * np.random.randn(len(s1))
            x2_ts[:, t] += 0.1 * np.random.randn(len(s2))
        
        # Compute mutual information between current and past states
        # For simplicity, we'll use one timestamp lag
        x1_current = x1_ts[:, 1:]
        x1_past = x1_ts[:, :-1]
        x2_current = x2_ts[:, 1:]
        x2_past = x2_ts[:, :-1]
        
        # Project to 1D for simplicity in MI calculation
        x1_current_1d = np.mean(x1_current, axis=0)
        x1_past_1d = np.mean(x1_past, axis=0)
        x2_current_1d = np.mean(x2_current, axis=0)
        x2_past_1d = np.mean(x2_past, axis=0)
        
        # Calculate effective information as conditional mutual information
        ei = self.compute_conditional_mutual_information(
            x1_current_1d, x2_current_1d, np.concatenate([x1_past_1d, x2_past_1d])
        )
        
        return ei
    
    def compute_minimum_information_partition(self, states: np.ndarray) -> Tuple[float, Tuple[List[int], List[int]]]:
        """
        Find the partition that minimizes normalized integrated information.
        
        Args:
            states: Current states of all modules
            
        Returns:
            Tuple of (Φ value, partition)
        """
        n = self.num_modules
        if n <= 1:
            return 0.0, ([0], [])
            
        min_phi = float('inf')
        min_partition = None
        
        # For small systems, enumerate all partitions
        if n <= 4:
            for k in range(1, n//2 + 1):
                for partition1 in itertools.combinations(range(n), k):
                    partition1 = list(partition1)
                    partition2 = [i for i in range(n) if i not in partition1]
                    
                    phi = self.compute_phi(states, (partition1, partition2))
                    
                    # Normalize by size of the smaller partition
                    normalization = min(len(partition1), len(partition2))
                    norm_phi = phi / max(1, normalization)
                    
                    if norm_phi < min_phi:
                        min_phi = norm_phi
                        min_partition = (partition1, partition2)
        else:
            # For larger systems, use a greedy approach
            # Start with random balanced partition
            all_indices = list(range(n))
            random.shuffle(all_indices)
            mid = n // 2
            best_partition = (all_indices[:mid], all_indices[mid:])
            best_phi = self.compute_phi(states, best_partition)
            
            # Try swapping elements between partitions
            improved = True
            while improved:
                improved = False
                for i in range(n):
                    for j in range(n):
                        if i in best_partition[0] and j in best_partition[1]:
                            # Try swapping i and j
                            new_p1 = [x if x != i else j for x in best_partition[0]]
                            new_p2 = [x if x != j else i for x in best_partition[1]]
                            new_partition = (new_p1, new_p2)
                            
                            new_phi = self.compute_phi(states, new_partition)
                            
                            if new_phi < best_phi:
                                best_phi = new_phi
                                best_partition = new_partition
                                improved = True
                                break
                    if improved:
                        break
            
            min_phi = best_phi
            min_partition = best_partition
            
        return min_phi, min_partition
    
    def update_module_states(self, inputs: Dict[int, float]) -> np.ndarray:
        """
        Update the states of modules based on inputs and current connectivity.
        
        Args:
            inputs: Dictionary mapping module index to input value
            
        Returns:
            Updated states
        """
        # Copy current states
        new_states = np.copy(self.module_states)
        
        # Apply inputs
        for module_idx, input_val in inputs.items():
            if 0 <= module_idx < self.num_modules:
                new_states[module_idx] += input_val
        
        # Update states based on connectivity
        influence = np.zeros(self.num_modules)
        for i in range(self.num_modules):
            for j in range(self.num_modules):
                influence[i] += self.connectivity[j, i] * self.module_states[j]
        
        # Apply influence and decay
        decay_rate = 0.1
        new_states = (1 - decay_rate) * new_states + influence
        
        # Apply nonlinearity (tanh)
        new_states = np.tanh(new_states)
        
        # Update internal states
        self.module_states = new_states
        
        # Update graph node attributes
        for i in range(self.num_modules):
            self.flow_graph.nodes[i]['state'] = new_states[i]
            
        logger.debug("Updated module states with %d inputs", len(inputs))
        return new_states
    
    def compute_integrated_information(self) -> float:
        """
        Compute the integrated information (Φ) of the current system state.
        
        Returns:
            Φ value
        """
        phi, partition = self.compute_minimum_information_partition(self.module_states)
        self.phi_history.append(phi)
        
        logger.debug("Computed integrated information: Φ=%.4f", phi)
        return phi
    
    def optimize_integration(self, target_phi: Optional[float] = None) -> None:
        """
        Adjust connectivity to optimize integration.
        
        Args:
            target_phi: Target Φ value, if None maximize Φ
        """
        # Skip optimization with probability 0.5 to avoid constant updates
        if np.random.random() < 0.5:
            return
            
        current_phi = self.compute_integrated_information()
        
        # Determine if we should increase or decrease Φ
        increase_phi = target_phi is None or current_phi < target_phi
        
        # Choose a random connection to modify
        i, j = np.random.randint(0, self.num_modules, size=2)
        if i == j:
            return  # Skip self-connections
            
        # Store original value
        original_value = self.connectivity[i, j]
        
        # Modify connection
        if increase_phi:
            # Strengthen connection
            delta = self.integration_learning_rate * (1.0 - original_value)
            self.connectivity[i, j] = min(1.0, original_value + delta)
        else:
            # Weaken connection
            delta = self.integration_learning_rate * original_value
            self.connectivity[i, j] = max(0.0, original_value - delta)
            
        # Compute new Φ
        new_phi = self.compute_integrated_information()
        
        # Keep change if it moved Φ in the desired direction
        if (increase_phi and new_phi <= current_phi) or (not increase_phi and new_phi >= current_phi):
            # Revert change
            self.connectivity[i, j] = original_value
            
        # Update graph edge weights
        if (i, j) in self.flow_graph.edges:
            self.flow_graph.edges[i, j]['weight'] = self.connectivity[i, j]
        else:
            if self.connectivity[i, j] > 0:
                self.flow_graph.add_edge(i, j, weight=self.connectivity[i, j])
                
        if self.connectivity[i, j] == 0 and (i, j) in self.flow_graph.edges:
            self.flow_graph.remove_edge(i, j)
            
        logger.debug("Optimized integration: Φ changed from %.4f to %.4f", current_phi, new_phi)
    
    def regulate_information_flow(self, alpha: float = 0.5, beta: float = 0.3) -> Dict[Tuple[int, int], float]:
        """
        Regulate the flow of information between subsystems.
        
        Args:
            alpha: Weight for direct mutual information term
            beta: Weight for redundancy penalty term
            
        Returns:
            Dictionary mapping module pairs to flow values
        """
        # Initialize flow dict
        flows = {}
        
        for i in range(self.num_modules):
            for j in range(self.num_modules):
                if i == j or self.connectivity[i, j] == 0:
                    continue
                    
                # Compute direct mutual information (simplified)
                # In a real system, this would use actual information theory calculations
                direct_mi = self.connectivity[i, j] * abs(self.module_states[i])
                
                # Compute redundancy with other modules
                redundancy = 0.0
                for k in range(self.num_modules):
                    if k != i and k != j and self.connectivity[k, j] > 0:
                        # Simplified redundancy estimate
                        redundancy += self.connectivity[k, j] * abs(self.module_states[k])
                        
                # Compute regulated flow
                flow = alpha * direct_mi - beta * redundancy
                flow = max(0.0, flow)  # Ensure non-negative
                
                flows[(i, j)] = flow
                
        return flows
    
    def get_module_criticality(self) -> List[float]:
        """
        Compute criticality of each module based on graph centrality.
        
        Returns:
            List of criticality values
        """
        # Use eigenvector centrality as a measure of criticality
        try:
            centrality = nx.eigenvector_centrality(self.flow_graph, weight='weight')
            return [centrality.get(i, 0.0) for i in range(self.num_modules)]
        except:
            # Fall back to degree centrality if eigenvector centrality fails
            in_degree = self.flow_graph.in_degree(weight='weight')
            out_degree = self.flow_graph.out_degree(weight='weight')
            
            # Combine in and out degree
            criticality = []
            for i in range(self.num_modules):
                in_val = dict(in_degree).get(i, 0.0)
                out_val = dict(out_degree).get(i, 0.0)
                criticality.append(in_val + out_val)
                
            # Normalize
            if max(criticality) > 0:
                criticality = [c / max(criticality) for c in criticality]
                
            return criticality
    
    def get_integrated_core(self) -> List[int]:
        """
        Identify the integrated core - modules that contribute most to integration.
        
        Returns:
            List of module indices in the integrated core
        """
        if not self.phi_history:
            return list(range(self.num_modules))
            
        # Compute impact of removing each module
        impact = []
        baseline_phi = self.compute_integrated_information()
        
        for i in range(self.num_modules):
            # Temporarily zero out connections to/from this module
            orig_in = self.connectivity[:, i].copy()
            orig_out = self.connectivity[i, :].copy()
            
            self.connectivity[:, i] = 0
            self.connectivity[i, :] = 0
            
            # Compute Φ without this module
            reduced_phi = self.compute_integrated_information()
            
            # Restore connections
            self.connectivity[:, i] = orig_in
            self.connectivity[i, :] = orig_out
            
            # Impact is the reduction in Φ
            impact.append(baseline_phi - reduced_phi)
            
        # Identify core modules as those with above-average impact
        avg_impact = sum(impact) / len(impact)
        core_indices = [i for i, imp in enumerate(impact) if imp > avg_impact]
        
        # Ensure at least one module in core
        if not core_indices:
            core_indices = [np.argmax(impact)]
            
        logger.debug("Identified integrated core with %d modules", len(core_indices))
        return core_indices
    
    def save_state(self) -> Dict[str, Any]:
        """
        Save the current state of the integrated information matrix.
        
        Returns:
            Dictionary containing the complete state
        """
        return {
            'num_modules': self.num_modules,
            'phi_threshold': self.phi_threshold,
            'integration_learning_rate': self.integration_learning_rate,
            'connectivity': self.connectivity.tolist(),
            'module_states': self.module_states.tolist(),
            'information_content': self.information_content.tolist(),
            'phi_history': self.phi_history
            # Note: Graph structure will be recreated from connectivity
        }
    
    def load_state(self, state: Dict[str, Any]) -> None:
        """
        Load a previously saved state.
        
        Args:
            state: Dictionary containing the state to load
        """
        self.num_modules = state.get('num_modules', self.num_modules)
        self.phi_threshold = state.get('phi_threshold', self.phi_threshold)
        self.integration_learning_rate = state.get('integration_learning_rate', self.integration_learning_rate)
        
        # Load numpy arrays
        self.connectivity = np.array(state.get('connectivity', np.zeros((self.num_modules, self.num_modules))))
        self.module_states = np.array(state.get('module_states', np.zeros(self.num_modules)))
        self.information_content = np.array(state.get('information_content', np.ones(self.num_modules)))
        self.phi_history = state.get('phi_history', [])
        
        # Recreate flow graph
        self.flow_graph = nx.DiGraph()
        for i in range(self.num_modules):
            self.flow_graph.add_node(i, state=self.module_states[i], info_content=self.information_content[i])
        
        for i in range(self.num_modules):
            for j in range(self.num_modules):
                if self.connectivity[i, j] > 0:
                    self.flow_graph.add_edge(i, j, weight=self.connectivity[i, j])
                    
        logger.info("Loaded integrated information matrix state with %d modules", self.num_modules)


class AttentionalAwareness:
    """
    Implements mechanisms for selecting and focusing on the most relevant information,
    from both external inputs and internal states. Creates a form of "spotlight of
    attention" that enhances processing of selected information.
    """
    
    def __init__(
        self,
        salience_weights: Dict[str, float] = None,
        temperature: float = 0.5,
        habituation_rate: float = 0.05,
        recovery_rate: float = 0.01,
        enhancement_factor: float = 2.0,
        max_focus_items: int = 5
    ):
        """
        Initialize the Attentional Awareness system.
        
        Args:
            salience_weights: Dictionary mapping salience factor to weight
            temperature: Temperature parameter for softmax attention allocation
            habituation_rate: Rate at which attention habituates to stimuli
            recovery_rate: Rate at which habituated attention recovers
            enhancement_factor: Factor by which attention enhances processing
            max_focus_items: Maximum number of items to focus on simultaneously
        """
        # Set default weights if none provided
        self.salience_weights = salience_weights or {
            'novelty': 1.0,
            'relevance': 1.5,
            'uncertainty': 0.8,
            'potential_gain': 1.2,
            'emotional_valence': 0.7,
            'goal_congruence': 1.3,
            'surprise': 0.9
        }
        
        self.temperature = temperature
        self.habituation_rate = habituation_rate
        self.recovery_rate = recovery_rate
        self.enhancement_factor = enhancement_factor
        self.max_focus_items = max_focus_items
        
        # Current attention state
        self.attention = {}  # Maps item ID to attention value (0-1)
        
        # Habituation state for each item
        self.habituation = {}  # Maps item ID to habituation value (0-1)
        
        # Attention history
        self.attention_history = defaultdict(list)  # Maps item ID to list of (timestamp, attention) tuples
        
        # Current items in focus
        self.current_focus = []
        
        # Last update time
        self.last_update_time = time.time()
        
        logger.info("Attentional Awareness initialized with weights: %s", self.salience_weights)
    
    def calculate_salience(self, items: Dict[str, Dict[str, float]]) -> Dict[str, float]:
        """
        Calculate salience scores for a set of items.
        
        Args:
            items: Dictionary mapping item ID to attributes
                  Each attribute dict should contain keys that match salience_weights
            
        Returns:
            Dictionary mapping item ID to salience score
        """
        salience_scores = {}
        
        for item_id, attributes in items.items():
            # Initialize score
            score = 0.0
            
            # Calculate weighted sum of attributes
            for factor, weight in self.salience_weights.items():
                if factor in attributes:
                    score += weight * attributes[factor]
                    
            salience_scores[item_id] = score
            
        return salience_scores
    
    def allocate_attention(self, salience_scores: Dict[str, float]) -> Dict[str, float]:
        """
        Allocate attention based on salience scores.
        
        Args:
            salience_scores: Dictionary mapping item ID to salience score
            
        Returns:
            Dictionary mapping item ID to allocated attention
        """
        # Apply habituation to salience scores
        adjusted_scores = {}
        for item_id, score in salience_scores.items():
            habituation_factor = self.habituation.get(item_id, 0.0)
            adjusted_scores[item_id] = score * (1.0 - habituation_factor)
            
        # Apply softmax to get attention distribution
        attention_values = {}
        
        if not adjusted_scores:
            return attention_values
            
        # Extract scores
        items = list(adjusted_scores.keys())
        scores = np.array([adjusted_scores[item] for item in items])
        
        # Apply softmax with temperature
        scaled_scores = scores / self.temperature
        exp_scores = np.exp(scaled_scores - np.max(scaled_scores))  # Subtract max for numerical stability
        softmax_scores = exp_scores / np.sum(exp_scores)
        
        # Assign attention values
        for i, item_id in enumerate(items):
            attention_values[item_id] = float(softmax_scores[i])
            
        return attention_values
    
    def update_habituation(self, dt: float) -> None:
        """
        Update habituation states for all items.
        
        Args:
            dt: Time elapsed since last update
        """
        for item_id, attention_value in self.attention.items():
            current_habituation = self.habituation.get(item_id, 0.0)
            
            # Increase habituation based on attention
            habituation_increase = self.habituation_rate * attention_value * dt
            
            # Recovery from habituation
            habituation_recovery = self.recovery_rate * current_habituation * dt
            
            # Update habituation
            new_habituation = current_habituation + habituation_increase - habituation_recovery
            new_habituation = max(0.0, min(1.0, new_habituation))
            
            self.habituation[item_id] = new_habituation
    
    def enhance_processing(self, item_values: Dict[str, float]) -> Dict[str, float]:
        """
        Enhance processing of attended items.
        
        Args:
            item_values: Dictionary mapping item ID to baseline processing value
            
        Returns:
            Dictionary mapping item ID to enhanced processing value
        """
        enhanced_values = {}
        
        for item_id, baseline_value in item_values.items():
            attention_value = self.attention.get(item_id, 0.0)
            
            # Apply enhancement based on attention
            enhancement = 1.0 + self.enhancement_factor * attention_value
            enhanced_values[item_id] = baseline_value * enhancement
            
        return enhanced_values
    
    def update_focus(self) -> List[str]:
        """
        Update the current focus based on attention values.
        
        Returns:
            List of item IDs in focus
        """
        if not self.attention:
            self.current_focus = []
            return []
            
        # Sort items by attention value
        sorted_items = sorted(self.attention.items(), key=lambda x: x[1], reverse=True)
        
        # Take top N items
        self.current_focus = [item_id for item_id, _ in sorted_items[:self.max_focus_items]]
        
        return self.current_focus
    
    def update(self, items: Dict[str, Dict[str, float]]) -> Dict[str, float]:
        """
        Perform a full attention update cycle.
        
        Args:
            items: Dictionary mapping item ID to attributes
            
        Returns:
            Dictionary mapping item ID to attention value
        """
        # Calculate time elapsed
        current_time = time.time()
        dt = current_time - self.last_update_time
        self.last_update_time = current_time
        
        # Calculate salience
        salience_scores = self.calculate_salience(items)
        
        # Update habituation
        self.update_habituation(dt)
        
        # Allocate attention
        self.attention = self.allocate_attention(salience_scores)
        
        # Update focus
        self.update_focus()
        
        # Record attention history
        for item_id, attention_value in self.attention.items():
            self.attention_history[item_id].append((current_time, attention_value))
            
            # Trim history to last 100 entries
            if len(self.attention_history[item_id]) > 100:
                self.attention_history[item_id] = self.attention_history[item_id][-100:]
                
        logger.debug("Updated attention for %d items, focus on %d items", 
                    len(items), len(self.current_focus))
        return self.attention
    
    def get_attention_dynamics(self, item_id: str, window_size: int = 50) -> Dict[str, Any]:
        """
        Get dynamics of attention for a specific item.
        
        Args:
            item_id: ID of the item
            window_size: Number of recent attention values to analyze
            
        Returns:
            Dictionary with attention dynamics information
        """
        if item_id not in self.attention_history or not self.attention_history[item_id]:
            return {
                'available': False,
                'current': 0.0
            }
            
        # Get attention history
        history = self.attention_history[item_id][-window_size:]
        timestamps = [entry[0] for entry in history]
        attention_values = [entry[1] for entry in history]
        
        if len(history) < 2:
            return {
                'available': True,
                'current': attention_values[-1],
                'mean': attention_values[-1],
                'trend': 0.0
            }
            
        # Calculate statistics
        mean_attention = np.mean(attention_values)
        
        # Calculate trend (simple linear regression)
        normalized_times = [(t - timestamps[0]) for t in timestamps]
        if len(set(normalized_times)) > 1:  # Ensure multiple time points
            slope, _, _, _, _ = stats.linregress(normalized_times, attention_values)
        else:
            slope = 0.0
            
        # Calculate variability
        std_attention = np.std(attention_values) if len(attention_values) > 1 else 0.0
        
        return {
            'available': True,
            'current': attention_values[-1],
            'mean': mean_attention,
            'std': std_attention,
            'trend': slope,
            'increasing': slope > 0.01,
            'decreasing': slope < -0.01,
            'stable': abs(slope) <= 0.01,
            'habituation': self.habituation.get(item_id, 0.0)
        }
    
    def detect_attention_shifts(self, threshold: float = 0.2) -> List[Dict[str, Any]]:
        """
        Detect significant shifts in attention.
        
        Args:
            threshold: Minimum change to consider a shift significant
            
        Returns:
            List of attention shift events
        """
        shifts = []
        
        for item_id, history in self.attention_history.items():
            if len(history) < 2:
                continue
                
            # Get recent attention values
            recent_values = [entry[1] for entry in history[-2:]]
            
            # Check for significant change
            change = recent_values[1] - recent_values[0]
            if abs(change) >= threshold:
                shifts.append({
                    'item_id': item_id,
                    'from': recent_values[0],
                    'to': recent_values[1],
                    'change': change,
                    'timestamp': history[-1][0]
                })
                
        # Sort by recency (newest first)
        shifts.sort(key=lambda x: x['timestamp'], reverse=True)
        
        return shifts
    
    def get_focus_stability(self, window_size: int = 5) -> float:
        """
        Calculate stability of the attentional focus.
        
        Args:
            window_size: Number of recent focus updates to consider
            
        Returns:
            Stability score (0-1), higher means more stable
        """
        # Get history of focus updates
        focus_history = []
        
        for item_id, history in self.attention_history.items():
            for timestamp, attention in history:
                focus_history.append((timestamp, item_id, attention))
                
        # Sort by timestamp
        focus_history.sort()
        
        if len(focus_history) < window_size:
            return 1.0  # Not enough data
            
        # Get recent focus snapshots
        recent_focus = []
        current_snapshot = set()
        last_timestamp = None
        
        for timestamp, item_id, attention in reversed(focus_history):
            if last_timestamp is None:
                last_timestamp = timestamp
                
            # If this is a new timestamp, save the current snapshot
            if timestamp < last_timestamp:
                recent_focus.append(current_snapshot)
                current_snapshot = set()
                last_timestamp = timestamp
                
                if len(recent_focus) >= window_size:
                    break
                    
            # Add high-attention items to snapshot
            if attention > 0.2:  # Threshold for considering an item in focus
                current_snapshot.add(item_id)
                
        # Add the last snapshot if not empty
        if current_snapshot and len(recent_focus) < window_size:
            recent_focus.append(current_snapshot)
            
        if len(recent_focus) < 2:
            return 1.0  # Not enough snapshots
            
        # Calculate Jaccard similarity between consecutive snapshots
        similarities = []
        for i in range(len(recent_focus) - 1):
            set1 = recent_focus[i]
            set2 = recent_focus[i + 1]
            
            if not set1 or not set2:
                similarities.append(0.0)
                continue
                
            # Jaccard similarity: |A ∩ B| / |A ∪ B|
            intersection = len(set1.intersection(set2))
            union = len(set1.union(set2))
            
            if union > 0:
                similarities.append(intersection / union)
            else:
                similarities.append(0.0)
                
        # Average similarity is the stability score
        return sum(similarities) / len(similarities) if similarities else 1.0
    
    def save_state(self) -> Dict[str, Any]:
        """
        Save the current state of the attentional awareness system.
        
        Returns:
            Dictionary containing the complete state
        """
        # Convert attention history to serializable format
        serialized_history = {}
        for item_id, history in self.attention_history.items():
            serialized_history[item_id] = history
            
        return {
            'salience_weights': self.salience_weights,
            'temperature': self.temperature,
            'habituation_rate': self.habituation_rate,
            'recovery_rate': self.recovery_rate,
            'enhancement_factor': self.enhancement_factor,
            'max_focus_items': self.max_focus_items,
            'attention': self.attention,
            'habituation': self.habituation,
            'attention_history': serialized_history,
            'current_focus': self.current_focus,
            'last_update_time': self.last_update_time
        }
    
    def load_state(self, state: Dict[str, Any]) -> None:
        """
        Load a previously saved state.
        
        Args:
            state: Dictionary containing the state to load
        """
        self.salience_weights = state.get('salience_weights', self.salience_weights)
        self.temperature = state.get('temperature', self.temperature)
        self.habituation_rate = state.get('habituation_rate', self.habituation_rate)
        self.recovery_rate = state.get('recovery_rate', self.recovery_rate)
        self.enhancement_factor = state.get('enhancement_factor', self.enhancement_factor)
        self.max_focus_items = state.get('max_focus_items', self.max_focus_items)
        
        self.attention = state.get('attention', {})
        self.habituation = state.get('habituation', {})
        
        # Load attention history
        history_data = state.get('attention_history', {})
        self.attention_history = defaultdict(list)
        for item_id, history in history_data.items():
            self.attention_history[item_id] = history
            
        self.current_focus = state.get('current_focus', [])
        self.last_update_time = state.get('last_update_time', time.time())
        
        logger.info("Loaded attentional awareness state for %d items with %d in focus",
                    len(self.attention), len(self.current_focus))


class GlobalWorkspace:
    """
    Implements a centralized information exchange inspired by Global Workspace Theory.
    
    Creates a "conscious broadcast" mechanism where only the most salient information 
    enters a global workspace that is accessible to all subsystems.
    """
    
    def __init__(
        self,
        capacity: int = 7,
        competition_temperature: float = 0.1,
        decay_rate: float = 0.2,
        broadcast_threshold: float = 0.5,
        broadcast_strength: float = 1.0,
        integration_factor: float = 0.3
    ):
        """
        Initialize the Global Workspace.
        
        Args:
            capacity: Maximum number of items in workspace (Miller's 7±2)
            competition_temperature: Temperature for competition softmax
            decay_rate: Rate at which workspace contents decay
            broadcast_threshold: Minimum competitive strength for broadcast
            broadcast_strength: Strength of broadcast signal
            integration_factor: Factor for integrating new with existing content
        """
        self.capacity = capacity
        self.competition_temperature = competition_temperature
        self.decay_rate = decay_rate
        self.broadcast_threshold = broadcast_threshold
        self.broadcast_strength = broadcast_strength
        self.integration_factor = integration_factor
        
        # Current workspace contents: {content_id: {strength, content, timestamp, metadata}}
        self.workspace_contents = {}
        
        # Access history: {content_id: [timestamps]}
        self.access_history = defaultdict(list)
        
        # Current broadcast: {content_id: strength}
        self.current_broadcast = {}
        
        # Subsystem registry: {subsystem_id: {influence_factor, last_update}}
        self.subsystems = {}
        
        # Information state for each subsystem: {subsystem_id: {content_id: strength}}
        self.subsystem_states = defaultdict(dict)
        
        # Last update time
        self.last_update_time = time.time()
        
        logger.info("Global Workspace initialized with capacity=%d, broadcast_threshold=%.2f",
                    capacity, broadcast_threshold)
    
    def register_subsystem(
        self, 
        subsystem_id: str, 
        influence_factor: float = 1.0
    ) -> None:
        """
        Register a subsystem with the global workspace.
        
        Args:
            subsystem_id: Unique identifier for the subsystem
            influence_factor: Factor determining influence of broadcasts on this subsystem
        """
        self.subsystems[subsystem_id] = {
            'influence_factor': influence_factor,
            'last_update': time.time()
        }
        self.subsystem_states[subsystem_id] = {}
        
        logger.debug("Registered subsystem %s with influence_factor=%.2f", 
                    subsystem_id, influence_factor)
    
    def competition_for_access(
        self, 
        candidates: Dict[str, Dict[str, Any]]
    ) -> Dict[str, float]:
        """
        Calculate which candidate contents gain access to the global workspace.
        
        Args:
            candidates: Dictionary mapping content_id to content metadata
                       Each metadata dict should have 'competitive_strength' and 'content'
            
        Returns:
            Dictionary mapping content_id to access probability
        """
        if not candidates:
            return {}
            
        # Extract competitive strengths
        content_ids = list(candidates.keys())
        strengths = np.array([candidates[cid].get('competitive_strength', 0.0) for cid in content_ids])
        
        # Apply softmax with temperature
        scaled_strengths = strengths / self.competition_temperature
        exp_strengths = np.exp(scaled_strengths - np.max(scaled_strengths))  # Subtract max for numerical stability
        softmax_strengths = exp_strengths / np.sum(exp_strengths)
        
        # Create access probabilities dict
        access_probs = {}
        for i, cid in enumerate(content_ids):
            access_probs[cid] = float(softmax_strengths[i])
            
        return access_probs
    
    def update_workspace(self, candidates: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """
        Update the global workspace based on competition among candidates.
        
        Args:
            candidates: Dictionary mapping content_id to content metadata
                       Each metadata dict should have 'competitive_strength' and 'content'
            
        Returns:
            Dictionary describing the updated workspace state
        """
        # Calculate time elapsed since last update
        current_time = time.time()
        dt = current_time - self.last_update_time
        self.last_update_time = current_time
        
        # Apply decay to existing workspace contents
        for content_id, content_data in list(self.workspace_contents.items()):
            # Calculate decay factor
            time_in_workspace = current_time - content_data['timestamp']
            decay_factor = math.exp(-self.decay_rate * time_in_workspace)
            
            # Update strength
            content_data['strength'] *= decay_factor
            
            # Remove if below threshold
            if content_data['strength'] < 0.1:
                del self.workspace_contents[content_id]
                
        # Calculate access probabilities
        access_probs = self.competition_for_access(candidates)
        
        # Determine which candidates enter the workspace
        entered_workspace = []
        
        for content_id, access_prob in access_probs.items():
            # Check if content should enter workspace
            if access_prob > self.broadcast_threshold:
                # Add to workspace if capacity allows or replace weakest content
                if len(self.workspace_contents) < self.capacity:
                    # Add to workspace
                    self.workspace_contents[content_id] = {
                        'strength': access_prob,
                        'content': candidates[content_id]['content'],
                        'timestamp': current_time,
                        'metadata': candidates[content_id].get('metadata', {})
                    }
                    entered_workspace.append(content_id)
                else:
                    # Find weakest content
                    weakest_id = min(self.workspace_contents, 
                                    key=lambda cid: self.workspace_contents[cid]['strength'])
                    weakest_strength = self.workspace_contents[weakest_id]['strength']
                    
                    # Replace if new content is stronger
                    if access_prob > weakest_strength:
                        del self.workspace_contents[weakest_id]
                        self.workspace_contents[content_id] = {
                            'strength': access_prob,
                            'content': candidates[content_id]['content'],
                            'timestamp': current_time,
                            'metadata': candidates[content_id].get('metadata', {})
                        }
                        entered_workspace.append(content_id)
                        
                # Record access
                self.access_history[content_id].append(current_time)
                
                # Trim history if too long
                if len(self.access_history[content_id]) > 100:
                    self.access_history[content_id] = self.access_history[content_id][-100:]
        
        # Prepare broadcast
        broadcast_contents = {}
        for content_id, content_data in self.workspace_contents.items():
            # Only broadcast contents with sufficient strength
            if content_data['strength'] > self.broadcast_threshold:
                broadcast_contents[content_id] = content_data['strength']
                
        self.current_broadcast = broadcast_contents
        
        # Log workspace update
        logger.debug("Updated workspace: %d candidates, %d contents entered, %d contents broadcasting",
                    len(candidates), len(entered_workspace), len(broadcast_contents))
        
        return {
            'workspace_size': len(self.workspace_contents),
            'entered_workspace': entered_workspace,
            'broadcasting': list(broadcast_contents.keys())
        }
    
    def broadcast_to_subsystems(self) -> Dict[str, Any]:
        """
        Broadcast current workspace contents to all registered subsystems.
        
        Returns:
            Dictionary with information about the broadcast results
        """
        if not self.current_broadcast:
            return {'broadcast_size': 0, 'subsystems_updated': 0}
            
        # Record which subsystems were updated
        updated_subsystems = []
        
        for subsystem_id, subsystem_data in self.subsystems.items():
            influence_factor = subsystem_data['influence_factor']
            
            # Calculate effective broadcast strength for this subsystem
            effective_strength = self.broadcast_strength * influence_factor
            
            # Update subsystem state
            for content_id, strength in self.current_broadcast.items():
                # Skip if broadcast strength is too low
                if strength * effective_strength < 0.1:
                    continue
                    
                content_data = self.workspace_contents[content_id]
                
                # Update or initialize content in subsystem state
                if content_id in self.subsystem_states[subsystem_id]:
                    # Integrate new with existing content
                    current_strength = self.subsystem_states[subsystem_id][content_id]
                    new_strength = strength * effective_strength
                    integrated_strength = (1 - self.integration_factor) * current_strength + self.integration_factor * new_strength
                    self.subsystem_states[subsystem_id][content_id] = integrated_strength
                else:
                    # Initialize content
                    self.subsystem_states[subsystem_id][content_id] = strength * effective_strength
                    
            # Mark subsystem as updated
            self.subsystems[subsystem_id]['last_update'] = time.time()
            updated_subsystems.append(subsystem_id)
            
        return {
            'broadcast_size': len(self.current_broadcast),
            'subsystems_updated': len(updated_subsystems),
            'updated_subsystems': updated_subsystems
        }
    
    def get_subsystem_state(self, subsystem_id: str) -> Dict[str, float]:
        """
        Get the current information state of a subsystem.
        
        Args:
            subsystem_id: ID of the subsystem
            
        Returns:
            Dictionary mapping content_id to content strength
            
        Raises:
            ValueError: If subsystem not found
        """
        if subsystem_id not in self.subsystems:
            raise ValueError(f"Subsystem with ID {subsystem_id} not found")
            
        return self.subsystem_states[subsystem_id]
    
    def get_workspace_contents(self) -> Dict[str, Dict[str, Any]]:
        """
        Get the current contents of the global workspace.
        
        Returns:
            Dictionary mapping content_id to content data
        """
        return self.workspace_contents
    
    def get_most_active_content(self, n: int = 3) -> List[Dict[str, Any]]:
        """
        Get the most active contents in the workspace.
        
        Args:
            n: Number of contents to return
            
        Returns:
            List of content data dictionaries
        """
        if not self.workspace_contents:
            return []
            
        # Sort contents by strength
        sorted_contents = sorted(
            [(cid, data) for cid, data in self.workspace_contents.items()],
            key=lambda x: x[1]['strength'],
            reverse=True
        )
        
        # Return top n contents
        return [
            {
                'id': cid,
                'content': data['content'],
                'strength': data['strength'],
                'age': time.time() - data['timestamp'],
                'metadata': data['metadata']
            }
            for cid, data in sorted_contents[:n]
        ]
    
    def get_content_access_frequency(self, window_size: float = 3600) -> Dict[str, float]:
        """
        Calculate access frequency for contents over a time window.
        
        Args:
            window_size: Time window in seconds
            
        Returns:
            Dictionary mapping content_id to access frequency
        """
        current_time = time.time()
        cutoff_time = current_time - window_size
        
        # Calculate access frequency for each content
        frequencies = {}
        
        for content_id, timestamps in self.access_history.items():
            # Count accesses within window
            recent_accesses = sum(1 for t in timestamps if t >= cutoff_time)
            
            # Calculate frequency in accesses per hour
            frequency = recent_accesses * 3600 / window_size
            frequencies[content_id] = frequency
            
        return frequencies
    
    def analyze_workspace_dynamics(self) -> Dict[str, Any]:
        """
        Analyze dynamics of the global workspace over time.
        
        Returns:
            Dictionary with analysis results
        """
        current_time = time.time()
        
        # Calculate content stability (average time in workspace)
        content_ages = [current_time - data['timestamp'] for data in self.workspace_contents.values()]
        avg_age = np.mean(content_ages) if content_ages else 0.0
        
        # Calculate workspace utilization
        utilization = len(self.workspace_contents) / self.capacity
        
        # Calculate content turnover (average number of new contents per minute)
        # Use access history from last 10 minutes
        cutoff_time = current_time - 600  # 10 minutes
        recent_accesses = sum(
            1 for timestamps in self.access_history.values()
            for t in timestamps if t >= cutoff_time
        )
        turnover = recent_accesses / 10  # per minute
        
        # Calculate broadcast intensity (average strength of broadcast)
        broadcast_intensity = np.mean(list(self.current_broadcast.values())) if self.current_broadcast else 0.0
        
        return {
            'avg_content_age': avg_age,
            'workspace_utilization': utilization,
            'content_turnover': turnover,
            'broadcast_intensity': broadcast_intensity,
            'num_broadcasting': len(self.current_broadcast),
            'num_registered_subsystems': len(self.subsystems)
        }
    
    def save_state(self) -> Dict[str, Any]:
        """
        Save the current state of the global workspace.
        
        Returns:
            Dictionary containing the complete state
        """
        return {
            'capacity': self.capacity,
            'competition_temperature': self.competition_temperature,
            'decay_rate': self.decay_rate,
            'broadcast_threshold': self.broadcast_threshold,
            'broadcast_strength': self.broadcast_strength,
            'integration_factor': self.integration_factor,
            'workspace_contents': self.workspace_contents,
            'access_history': dict(self.access_history),
            'current_broadcast': self.current_broadcast,
            'subsystems': self.subsystems,
            'subsystem_states': dict(self.subsystem_states),
            'last_update_time': self.last_update_time
        }
    
    def load_state(self, state: Dict[str, Any]) -> None:
        """
        Load a previously saved state.
        
        Args:
            state: Dictionary containing the state to load
        """
        self.capacity = state.get('capacity', self.capacity)
        self.competition_temperature = state.get('competition_temperature', self.competition_temperature)
        self.decay_rate = state.get('decay_rate', self.decay_rate)
        self.broadcast_threshold = state.get('broadcast_threshold', self.broadcast_threshold)
        self.broadcast_strength = state.get('broadcast_strength', self.broadcast_strength)
        self.integration_factor = state.get('integration_factor', self.integration_factor)
        
        self.workspace_contents = state.get('workspace_contents', {})
        
        # Convert to defaultdict for access history
        self.access_history = defaultdict(list)
        for content_id, timestamps in state.get('access_history', {}).items():
            self.access_history[content_id] = timestamps
            
        self.current_broadcast = state.get('current_broadcast', {})
        self.subsystems = state.get('subsystems', {})
        
        # Convert to defaultdict for subsystem states
        self.subsystem_states = defaultdict(dict)
        for subsystem_id, content_state in state.get('subsystem_states', {}).items():
            self.subsystem_states[subsystem_id] = content_state
            
        self.last_update_time = state.get('last_update_time', time.time())
        
        logger.info("Loaded global workspace state with %d contents and %d registered subsystems",
                   len(self.workspace_contents), len(self.subsystems))


class EmergentConsciousnessLattice:
    """
    Integrates the Emergent Consciousness components into a coherent system.
    
    This class serves as the main interface to the Emergent Consciousness Lattice,
    coordinating the interaction between Self-Awareness, Intentionality, Integrated
    Information, Attentional Awareness, and Global Workspace components.
    """
    
    def __init__(
        self,
        self_awareness_config: Dict[str, Any] = None,
        intentionality_config: Dict[str, Any] = None,
        integrated_info_config: Dict[str, Any] = None,
        attentional_awareness_config: Dict[str, Any] = None,
        global_workspace_config: Dict[str, Any] = None,
        enable_integration: bool = True
    ):
        """
        Initialize the Emergent Consciousness Lattice.
        
        Args:
            self_awareness_config: Configuration for Self-Awareness Module
            intentionality_config: Configuration for Intentionality System
            integrated_info_config: Configuration for Integrated Information Matrix
            attentional_awareness_config: Configuration for Attentional Awareness
            global_workspace_config: Configuration for Global Workspace
            enable_integration: Whether to enable integration between components
        """
        # Initialize components
        self.self_awareness = SelfAwarenessModule(**(self_awareness_config or {}))
        self.intentionality = IntentionalitySystem(**(intentionality_config or {}))
        self.integrated_info = IntegratedInformationMatrix(**(integrated_info_config or {}))
        self.attentional_awareness = AttentionalAwareness(**(attentional_awareness_config or {}))
        self.global_workspace = GlobalWorkspace(**(global_workspace_config or {}))
        
        # Integration settings
        self.enable_integration = enable_integration
        
        # Register components as subsystems in the global workspace
        self.global_workspace.register_subsystem('self_awareness', influence_factor=1.2)
        self.global_workspace.register_subsystem('intentionality', influence_factor=1.5)
        self.global_workspace.register_subsystem('integrated_info', influence_factor=0.8)
        self.global_workspace.register_subsystem('attentional_awareness', influence_factor=1.0)
        
        # Last update timestamp
        self.last_update = time.time()
        
        # Integration cycle counter
        self.integration_cycles = 0
        
        # System state metrics
        self.system_metrics = {
            'coherence': 0.0,
            'integration': 0.0,
            'awareness': 0.0,
            'intentionality': 0.0,
            'attentional_focus': 0.0
        }
        
        logger.info("Emergent Consciousness Lattice initialized with integration=%s",
                   "enabled" if enable_integration else "disabled")
    
    def update(self, inputs: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Perform a full update cycle of the Emergent Consciousness Lattice.
        
        Args:
            inputs: Dictionary with inputs for different components
            
        Returns:
            Dictionary with update results
        """
        inputs = inputs or {}
        current_time = time.time()
        dt = current_time - self.last_update
        self.last_update = current_time
        
        results = {}
        
        # 1. Update integrated information matrix with module states
        module_inputs = inputs.get('integrated_info_inputs', {})
        self.integrated_info.update_module_states(module_inputs)
        
        # Calculate integrated information
        phi_value = self.integrated_info.compute_integrated_information()
        results['phi'] = phi_value
        
        # Try to optimize integration if enabled
        if self.enable_integration:
            self.integrated_info.optimize_integration()
        
        # 2. Update intentionality system
        # Process goal-related inputs
        if 'goals' in inputs:
            for goal_input in inputs['goals']:
                if 'parent_id' in goal_input:
                    # This is a subgoal
                    self.intentionality.set_subgoal(
                        goal_input['parent_id'],
                        goal_input['description'],
                        priority=goal_input.get('priority'),
                        deadline=goal_input.get('deadline'),
                        metadata=goal_input.get('metadata')
                    )
                else:
                    # This is a top-level goal
                    self.intentionality.set_goal(
                        goal_input['description'],
                        priority=goal_input.get('priority', 0.5),
                        deadline=goal_input.get('deadline'),
                        metadata=goal_input.get('metadata')
                    )
        
        # Update goal progress if provided
        if 'goal_progress' in inputs:
            for goal_id, progress in inputs['goal_progress'].items():
                try:
                    self.intentionality.update_goal_progress(goal_id, progress)
                except ValueError:
                    logger.warning("Attempt to update non-existent goal: %s", goal_id)
        
        # Check for urgent goals
        self.intentionality.plan_urgent_goals()
        
        # Get current intention
        current_intention = self.intentionality.get_current_intention()
        results['current_intention'] = current_intention
        
        # 3. Update attentional awareness
        attention_items = inputs.get('attention_items', {})
        
        # If integration enabled, add current intention to attention items
        if self.enable_integration and current_intention:
            goal = current_intention['goal']
            attention_items[f"goal_{goal.id}"] = {
                'novelty': 0.3,
                'relevance': goal.priority * 1.5,
                'uncertainty': 0.5,
                'potential_gain': goal.priority * 1.2,
                'goal_congruence': 1.0
            }
            
        # Update attention
        attention_values = self.attentional_awareness.update(attention_items)
        results['attention'] = {
            'focus': self.attentional_awareness.current_focus,
            'stability': self.attentional_awareness.get_focus_stability()
        }
        
        # 4. Update global workspace
        # Prepare candidate contents for global workspace
        workspace_candidates = {}
        
        # Add attended items as candidates
        for item_id in self.attentional_awareness.current_focus:
            if item_id in attention_items:
                workspace_candidates[item_id] = {
                    'competitive_strength': attention_values.get(item_id, 0.5),
                    'content': attention_items[item_id],
                    'metadata': {'source': 'attention'}
                }
                
        # Add custom workspace inputs if provided
        if 'workspace_inputs' in inputs:
            for content_id, content_data in inputs['workspace_inputs'].items():
                workspace_candidates[content_id] = {
                    'competitive_strength': content_data.get('strength', 0.7),
                    'content': content_data.get('content', {}),
                    'metadata': content_data.get('metadata', {'source': 'external'})
                }
                
        # Add current intention to workspace candidates
        if current_intention:
            goal = current_intention['goal']
            workspace_candidates[f"intention_{goal.id}"] = {
                'competitive_strength': goal.priority * 0.8,
                'content': {
                    'type': 'intention',
                    'description': goal.description,
                    'priority': goal.priority,
                    'completion': goal.completion
                },
                'metadata': {'source': 'intentionality'}
            }
            
        # Update workspace
        workspace_update = self.global_workspace.update_workspace(workspace_candidates)
        
        # Broadcast workspace contents
        broadcast_results = self.global_workspace.broadcast_to_subsystems()
        
        # Get most active workspace contents
        active_contents = self.global_workspace.get_most_active_content()
        results['workspace'] = {
            'update': workspace_update,
            'broadcast': broadcast_results,
            'active_contents': active_contents
        }
        
        # 5. Update self-awareness based on task results
        if 'task_results' in inputs:
            for task_result in inputs['task_results']:
                self.self_awareness.update_capability_model(task_result)
                
        if 'knowledge_updates' in inputs:
            self.self_awareness.update_knowledge_state(inputs['knowledge_updates'])
            
        # Perform self-assessment if enough time has passed
        self_assessment = self.self_awareness.self_assess()
        if self_assessment:
            results['self_assessment'] = self_assessment
            
        # Calculate system metrics
        self._update_system_metrics(phi_value)
        results['system_metrics'] = self.system_metrics
        
        # Increment integration cycle counter
        self.integration_cycles += 1
        
        logger.info("Completed ECL update cycle #%d: phi=%.3f, workspace_size=%d, attention_focus=%d",
                   self.integration_cycles, phi_value, len(active_contents), 
                   len(self.attentional_awareness.current_focus))
        
        return results
    
    def _update_system_metrics(self, phi_value: float) -> None:
        """
        Update system-level metrics based on component states.
        
        Args:
            phi_value: Current integrated information (Φ) value
        """
        # 1. Calculate coherence based on workspace dynamics
        workspace_dynamics = self.global_workspace.analyze_workspace_dynamics()
        coherence = min(1.0, workspace_dynamics['broadcast_intensity'] * 2.0)
        
        # 2. Integration metric based on Φ value
        # Scale Φ to [0, 1] range assuming typical range of [0, 2] for Φ values
        integration = min(1.0, phi_value / 2.0)
        
        # 3. Awareness based on self-awareness module
        awareness = 0.0
        capabilities = self.self_awareness.capability_model
        if capabilities:
            avg_performance = np.mean([v['performance'] for v in capabilities.values()])
            avg_uncertainty = np.mean([v['uncertainty'] for v in capabilities.values()])
            awareness = avg_performance * (1.0 - avg_uncertainty)
            
        # 4. Intentionality based on goal pursuit clarity
        intentionality_score = 0.5  # Default value
        current_intention = self.intentionality.get_current_intention()
        if current_intention:
            goal = current_intention['goal']
            intentionality_score = goal.priority * (1.0 + goal.completion) / 2.0
            
        # 5. Attentional focus based on focus stability
        attentional_focus = self.attentional_awareness.get_focus_stability()
        
        # Update system metrics
        self.system_metrics = {
            'coherence': coherence,
            'integration': integration,
            'awareness': awareness,
            'intentionality': intentionality_score,
            'attentional_focus': attentional_focus,
            'overall': np.mean([coherence, integration, awareness, intentionality_score, attentional_focus])
        }
    
    def get_self_description(self) -> Dict[str, Any]:
        """
        Get a self-description of the current system state.
        
        Returns:
            Dictionary with system self-description
        """
        # Get self-description from self-awareness module
        self_desc_text = self.self_awareness.get_self_description()
        
        # Get current intention
        current_intention = self.intentionality.get_current_intention()
        intention_desc = "No current intention"
        if current_intention:
            goal = current_intention['goal']
            completion_pct = int(goal.completion * 100)
            intention_desc = f"Current goal: {goal.description} (Priority: {goal.priority:.2f}, Completion: {completion_pct}%)"
            
        # Get focus items
        focus_items = []
        for item_id in self.attentional_awareness.current_focus:
            item_attention = self.attentional_awareness.attention.get(item_id, 0.0)
            focus_items.append((item_id, item_attention))
            
        focus_items.sort(key=lambda x: x[1], reverse=True)
        focus_desc = [f"{item_id}: {attention:.2f}" for item_id, attention in focus_items[:3]]
        
        # Get workspace contents
        active_contents = self.global_workspace.get_most_active_content(3)
        workspace_desc = []
        for content in active_contents:
            content_type = content.get('content', {}).get('type', 'unknown')
            content_desc = str(content.get('content', {}))
            if len(content_desc) > 50:
                content_desc = content_desc[:47] + "..."
            workspace_desc.append(f"{content_type}: {content_desc}")
            
        # Get integration value
        phi_value = self.integrated_info.compute_integrated_information()
        
        return {
            'self_description': self_desc_text,
            'current_intention': intention_desc,
            'attentional_focus': focus_desc,
            'workspace_contents': workspace_desc,
            'integration_value': phi_value,
            'system_metrics': self.system_metrics,
            'integration_cycles': self.integration_cycles
        }
    
    def save_state(self) -> Dict[str, Any]:
        """
        Save the current state of the entire Emergent Consciousness Lattice.
        
        Returns:
            Dictionary containing the complete state
        """
        return {
            'self_awareness': self.self_awareness.save_state(),
            'intentionality': self.intentionality.save_state(),
            'integrated_info': self.integrated_info.save_state(),
            'attentional_awareness': self.attentional_awareness.save_state(),
            'global_workspace': self.global_workspace.save_state(),
            'enable_integration': self.enable_integration,
            'last_update': self.last_update,
            'integration_cycles': self.integration_cycles,
            'system_metrics': self.system_metrics
        }
    
    def load_state(self, state: Dict[str, Any]) -> None:
        """
        Load a previously saved state.
        
        Args:
            state: Dictionary containing the state to load
        """
        if 'self_awareness' in state:
            self.self_awareness.load_state(state['self_awareness'])
            
        if 'intentionality' in state:
            self.intentionality.load_state(state['intentionality'])
            
        if 'integrated_info' in state:
            self.integrated_info.load_state(state['integrated_info'])
            
        if 'attentional_awareness' in state:
            self.attentional_awareness.load_state(state['attentional_awareness'])
            
        if 'global_workspace' in state:
            self.global_workspace.load_state(state['global_workspace'])
            
        self.enable_integration = state.get('enable_integration', self.enable_integration)
        self.last_update = state.get('last_update', time.time())
        self.integration_cycles = state.get('integration_cycles', 0)
        self.system_metrics = state.get('system_metrics', self.system_metrics)
        
        logger.info("Loaded Emergent Consciousness Lattice state with %d integration cycles",
                   self.integration_cycles)


# Export public classes
__all__ = [
    'SelfAwarenessModule',
    'IntentionalitySystem',
    'IntegratedInformationMatrix',
    'AttentionalAwareness',
    'GlobalWorkspace',
    'EmergentConsciousnessLattice'
]