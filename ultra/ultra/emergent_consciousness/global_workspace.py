#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Global Workspace Module for ULTRA

This module implements the Global Workspace component of the Emergent Consciousness Lattice,
which creates a centralized information exchange where only the most salient information
enters a global workspace that is accessible to all subsystems.

Mathematical formulations:
- Competition for access: P(x_i ∈ GW) = exp(C(x_i)/τ) / ∑_j exp(C(x_j)/τ)
- Broadcast mechanism: I_j(t+1) = I_j(t) + α_j · GW(t)
- Workspace dynamics: dGW(t)/dt = -λ · GW(t) + ∑_i β_i · x_i · 1_{x_i ∈ GW}
- Access monitoring: M(x_i, t+1) = M(x_i, t) + γ · (P(x_i ∈ GW) - M(x_i, t))

Author: ULTRA Development Team
"""

import numpy as np
import scipy.stats as stats
import logging
import time
import uuid
import threading
import json
import heapq
import math
from typing import Dict, List, Tuple, Set, Optional, Union, Callable, Any
from dataclasses import dataclass, field
from collections import defaultdict, deque
import concurrent.futures

# Setup logging
logger = logging.getLogger(__name__)


@dataclass
class WorkspaceContent:
    """Data class for content in the global workspace"""
    content_id: str
    content: Any
    strength: float
    source_subsystem: str
    timestamp: float
    last_update: float
    access_count: int = 0
    broadcast_count: int = 0
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class WorkspaceEvent:
    """Data class for global workspace events"""
    event_id: str
    event_type: str  # 'admission', 'broadcast', 'expulsion', 'decay'
    content_id: str
    timestamp: float
    strength: float
    source_subsystem: str
    affected_subsystems: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)


class SubsystemInterface:
    """Interface for subsystems to interact with the global workspace"""
    
    def __init__(self, subsystem_id: str, global_workspace: 'GlobalWorkspace'):
        """
        Initialize the subsystem interface.
        
        Args:
            subsystem_id: Identifier for the subsystem
            global_workspace: Reference to the global workspace
        """
        self.subsystem_id = subsystem_id
        self.global_workspace = global_workspace
        self.influence_factor = 1.0
        self.received_content = {}
        self.last_update = time.time()
        
    def submit_content(
        self, 
        content_id: str, 
        content: Any, 
        strength: float,
        metadata: Optional[Dict[str, Any]] = None
    ) -> float:
        """
        Submit content to the global workspace.
        
        Args:
            content_id: Identifier for the content
            content: The content itself
            strength: Strength of the content (0-1)
            metadata: Optional metadata about the content
            
        Returns:
            Competition strength for the content
        """
        return self.global_workspace.submit_candidate(
            content_id, content, strength, self.subsystem_id, metadata
        )
        
    def receive_broadcast(
        self, 
        content: Dict[str, Dict[str, Any]]
    ) -> None:
        """
        Receive broadcast content from the global workspace.
        
        Args:
            content: Dictionary mapping content_id to content information
        """
        self.received_content = content
        self.last_update = time.time()
        
    def set_influence_factor(self, factor: float) -> None:
        """
        Set the influence factor for this subsystem.
        
        Args:
            factor: Influence factor (0-2)
        """
        self.influence_factor = max(0.0, min(2.0, factor))
        self.global_workspace.update_subsystem_influence(
            self.subsystem_id, self.influence_factor
        )
        
    def get_received_content(self) -> Dict[str, Dict[str, Any]]:
        """
        Get the content received from the global workspace.
        
        Returns:
            Dictionary mapping content_id to content information
        """
        return self.received_content
        
    def get_strongest_content(self, k: int = 1) -> List[Dict[str, Any]]:
        """
        Get the k strongest content items received from the global workspace.
        
        Args:
            k: Number of items to return
            
        Returns:
            List of content items, sorted by strength
        """
        if not self.received_content:
            return []
            
        sorted_content = sorted(
            [
                {
                    'content_id': content_id,
                    'content': info['content'],
                    'strength': info['strength'],
                    **info.get('metadata', {})
                }
                for content_id, info in self.received_content.items()
            ],
            key=lambda x: x['strength'],
            reverse=True
        )
        
        return sorted_content[:k]


class GlobalWorkspace:
    """
    Implements a centralized information exchange inspired by Global Workspace Theory.
    
    Only the most salient information enters a global workspace that is accessible to all subsystems.
    This creates a "conscious broadcast" mechanism that facilitates information integration across
    the system.
    """
    
    def __init__(
        self, 
        capacity: int = 7,
        temperature: float = 0.1,
        decay_rate: float = 0.2,
        broadcast_threshold: float = 0.3,
        broadcast_strength: float = 1.0,
        integration_factor: float = 0.3,
        memory_length: int = 1000,
        learning_rate: float = 0.1,
        content_lifetime: float = 30.0,
        enable_threading: bool = False,
        max_events: int = 1000,
        metrics_window: int = 100
    ):
        """
        Initialize the Global Workspace.
        
        Args:
            capacity: Maximum number of items in workspace (Miller's 7±2)
            temperature: Temperature parameter for competition softmax
            decay_rate: Rate at which workspace contents decay
            broadcast_threshold: Minimum strength threshold for broadcasting
            broadcast_strength: Base strength of broadcast signal
            integration_factor: Factor for integrating new content with existing state
            memory_length: Maximum length of history
            learning_rate: Learning rate for access pattern learning
            content_lifetime: Maximum lifetime of content in seconds
            enable_threading: Whether to enable multi-threaded processing
            max_events: Maximum number of events to track
            metrics_window: Window size for metrics calculations
        """
        self.capacity = capacity
        self.temperature = temperature
        self.decay_rate = decay_rate
        self.broadcast_threshold = broadcast_threshold
        self.broadcast_strength = broadcast_strength
        self.integration_factor = integration_factor
        self.memory_length = memory_length
        self.learning_rate = learning_rate
        self.content_lifetime = content_lifetime
        self.enable_threading = enable_threading
        self.max_events = max_events
        self.metrics_window = metrics_window
        
        # Workspace state
        self.workspace_contents: Dict[str, WorkspaceContent] = {}
        
        # History of workspace contents
        self.workspace_history = deque(maxlen=memory_length)
        
        # Events
        self.events = deque(maxlen=max_events)
        
        # Access statistics
        self.access_patterns = {}  # Maps content_id -> access frequency
        
        # Subsystem registrations
        self.subsystems: Dict[str, SubsystemInterface] = {}
        self.subsystem_influence: Dict[str, float] = {}
        
        # Content candidates for next update
        self.candidates: Dict[str, Tuple[Any, float, str, Dict[str, Any]]] = {}
        
        # Metrics
        self.metrics = {
            'updates': 0,
            'admissions': 0,
            'broadcasts': 0,
            'expulsions': 0,
            'avg_occupancy': 0.0,
            'avg_strength': 0.0,
            'subsystem_submissions': defaultdict(int),
            'subsystem_admissions': defaultdict(int),
        }
        
        # Threading resources
        self.lock = threading.RLock()
        self.update_thread = None
        self.update_interval = 0.1  # seconds
        self.running = False
        self.executor = None
        
        if enable_threading:
            self.start_update_thread()
            self.executor = concurrent.futures.ThreadPoolExecutor(max_workers=4)
        
        logger.info(f"Initialized GlobalWorkspace with capacity={capacity}, "
                   f"temperature={temperature}, decay_rate={decay_rate}")
    
    def register_subsystem(
        self, 
        subsystem_id: str,
        influence_factor: float = 1.0
    ) -> SubsystemInterface:
        """
        Register a subsystem with the global workspace.
        
        Args:
            subsystem_id: Identifier for the subsystem
            influence_factor: Factor determining influence on/from the workspace
            
        Returns:
            Interface for the subsystem to interact with the workspace
        """
        with self.lock:
            if subsystem_id in self.subsystems:
                logger.warning(f"Subsystem {subsystem_id} already registered, returning existing interface")
                return self.subsystems[subsystem_id]
            
            # Create interface
            interface = SubsystemInterface(subsystem_id, self)
            interface.set_influence_factor(influence_factor)
            
            # Store interface and influence
            self.subsystems[subsystem_id] = interface
            self.subsystem_influence[subsystem_id] = influence_factor
            
            logger.info(f"Registered subsystem: {subsystem_id} with influence={influence_factor}")
            
            return interface
    
    def update_subsystem_influence(
        self, 
        subsystem_id: str, 
        influence_factor: float
    ) -> None:
        """
        Update the influence factor for a subsystem.
        
        Args:
            subsystem_id: Identifier for the subsystem
            influence_factor: New influence factor
        """
        with self.lock:
            self.subsystem_influence[subsystem_id] = influence_factor
    
    def submit_candidate(
        self, 
        content_id: str, 
        content: Any, 
        strength: float, 
        source_subsystem: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> float:
        """
        Submit a candidate for the global workspace.
        
        Args:
            content_id: Identifier for the content
            content: The content itself
            strength: Base strength of the content (0-1)
            source_subsystem: Identifier for the source subsystem
            metadata: Optional metadata about the content
            
        Returns:
            Computed competition strength
        """
        with self.lock:
            # Normalize strength to [0, 1]
            strength = max(0.0, min(1.0, strength))
            
            # Track subsystem submissions
            self.metrics['subsystem_submissions'][source_subsystem] += 1
            
            # Compute competition strength
            competition_strength = self.compute_competition_strength(
                content_id, content, strength, source_subsystem
            )
            
            # Store candidate for next update
            self.candidates[content_id] = (content, strength, source_subsystem, metadata or {})
            
            return competition_strength
    
    def compute_competition_strength(
        self, 
        content_id: str, 
        content: Any, 
        base_strength: float,
        source_subsystem: str
    ) -> float:
        """
        Compute the competitive strength of content for workspace access.
        
        This incorporates:
        1. Base strength of the content
        2. Influence factor of the source subsystem
        3. Historical access patterns
        4. Content novelty
        
        Args:
            content_id: Identifier for the content
            content: The actual content
            base_strength: Base strength value (0-1)
            source_subsystem: The subsystem that generated the content
            
        Returns:
            Competition strength score (0-1)
        """
        # Apply subsystem influence factor
        subsystem_factor = self.subsystem_influence.get(source_subsystem, 1.0)
        
        # Consider access history (giving boost to content types that frequently get access)
        history_factor = 1.0
        if content_id in self.access_patterns:
            history_factor = 1.0 + 0.2 * self.access_patterns[content_id]
        
        # Calculate novelty factor
        novelty_factor = 1.0
        if content_id in self.workspace_contents:
            # Content already in workspace gets a small penalty
            novelty_factor = 0.9
        elif self.workspace_history:
            # Check if content recently left the workspace
            recent_history = list(self.workspace_history)[-10:]
            for snapshot in recent_history:
                if content_id in snapshot['contents']:
                    # Recently seen content gets a moderate penalty
                    novelty_factor = 0.95
                    break
        
        # Calculate final strength
        strength = base_strength * subsystem_factor * history_factor * novelty_factor
        
        # Ensure within range [0, 1]
        return max(0.0, min(1.0, strength))
    
    def compete_for_access(
        self, 
        candidates: Dict[str, Tuple[Any, float, str, Dict[str, Any]]]
    ) -> Dict[str, float]:
        """
        Run competition for workspace access.
        
        P(x_i ∈ GW) = exp(C(x_i)/τ) / ∑_j exp(C(x_j)/τ)
        
        Args:
            candidates: Dictionary mapping content_id -> (content, strength, source_subsystem, metadata)
            
        Returns:
            Dictionary mapping content_id -> access probability
        """
        if not candidates:
            return {}
        
        # Compute competition strength for each candidate
        competition_strengths = {}
        for content_id, (content, strength, subsystem, metadata) in candidates.items():
            competition_strengths[content_id] = self.compute_competition_strength(
                content_id, content, strength, subsystem
            )
        
        # Apply softmax with temperature
        content_ids = list(competition_strengths.keys())
        strengths = np.array([competition_strengths[cid] for cid in content_ids])
        
        # Apply softmax with temperature
        scaled_strengths = strengths / self.temperature
        max_strength = np.max(scaled_strengths) if scaled_strengths.size > 0 else 0
        exp_strengths = np.exp(scaled_strengths - max_strength)  # Subtract max for numerical stability
        
        if np.sum(exp_strengths) > 0:
            access_probs = exp_strengths / np.sum(exp_strengths)
        else:
            # If all strengths are very low, assign equal probabilities
            access_probs = np.ones_like(strengths) / len(strengths)
        
        # Create result dictionary
        result = {
            content_id: float(prob) 
            for content_id, prob in zip(content_ids, access_probs)
        }
        
        return result
    
    def update_workspace(
        self, 
        candidates: Optional[Dict[str, Tuple[Any, float, str, Dict[str, Any]]]] = None
    ) -> Set[str]:
        """
        Update workspace content based on competition results.
        
        Args:
            candidates: Dictionary mapping content_id -> (content, strength, source_subsystem, metadata)
            
        Returns:
            Set of content_ids that were admitted to the workspace
        """
        with self.lock:
            # Use provided candidates or stored ones
            candidates = candidates or self.candidates
            
            # Clear stored candidates after using them
            self.candidates = {}
            
            # Run competition
            access_probs = self.compete_for_access(candidates)
            
            # Sort candidates by access probability
            sorted_candidates = sorted(
                [(cid, prob) for cid, prob in access_probs.items()],
                key=lambda x: x[1],
                reverse=True
            )
            
            # Track admitted content
            admitted = set()
            current_time = time.time()
            
            # First, decay existing content strengths
            for content_id in list(self.workspace_contents.keys()):
                content = self.workspace_contents[content_id]
                time_in_workspace = current_time - content.timestamp
                
                # Check if content expired
                if time_in_workspace > self.content_lifetime:
                    self._remove_content(content_id, 'expiration')
                    continue
                
                # Apply decay factor
                decay_factor = math.exp(-self.decay_rate * time_in_workspace)
                old_strength = content.strength
                new_strength = old_strength * decay_factor
                
                # Update content strength
                content.strength = new_strength
                content.last_update = current_time
                
                # Remove content if strength falls below threshold
                if new_strength < 0.1:
                    self._remove_content(content_id, 'decay')
            
            # Then, admit new content up to capacity
            for content_id, prob in sorted_candidates:
                # Get candidate details
                content_data = candidates[content_id]
                content, base_strength, subsystem, metadata = content_data
                
                # Skip if probability too low
                if prob < 0.1:
                    continue
                
                # If content already in workspace, just update its strength
                if content_id in self.workspace_contents:
                    workspace_content = self.workspace_contents[content_id]
                    old_strength = workspace_content.strength
                    
                    # Blend old and new strengths
                    new_strength = (
                        (1 - self.integration_factor) * old_strength + 
                        self.integration_factor * base_strength * prob
                    )
                    
                    workspace_content.strength = new_strength
                    workspace_content.last_update = current_time
                    workspace_content.access_count += 1
                    
                    admitted.add(content_id)
                    continue
                
                # If we have space, add the content
                if len(self.workspace_contents) < self.capacity:
                    self._add_content(
                        content_id, content, base_strength * prob, 
                        subsystem, current_time, metadata
                    )
                    admitted.add(content_id)
                else:
                    # Find the weakest item in the workspace
                    min_content_id = min(
                        self.workspace_contents.items(),
                        key=lambda x: x[1].strength
                    )[0]
                    
                    # If this candidate is stronger than the weakest item, replace it
                    min_strength = self.workspace_contents[min_content_id].strength
                    candidate_strength = base_strength * prob
                    
                    if candidate_strength > min_strength:
                        # Remove weakest
                        self._remove_content(min_content_id, 'replacement')
                        
                        # Add new content
                        self._add_content(
                            content_id, content, candidate_strength, 
                            subsystem, current_time, metadata
                        )
                        admitted.add(content_id)
            
            # Update workspace history
            self._update_history(admitted)
            
            # Update metrics
            self.metrics['updates'] += 1
            self.metrics['admissions'] += len(admitted)
            self.metrics['avg_occupancy'] = (
                (self.metrics['avg_occupancy'] * (self.metrics['updates'] - 1) + 
                 len(self.workspace_contents)) / self.metrics['updates']
            )
            
            if self.workspace_contents:
                self.metrics['avg_strength'] = (
                    (self.metrics['avg_strength'] * (self.metrics['updates'] - 1) + 
                     np.mean([c.strength for c in self.workspace_contents.values()])) / 
                    self.metrics['updates']
                )
            
            # Track admissions by subsystem
            for content_id in admitted:
                if content_id in self.workspace_contents:
                    subsystem = self.workspace_contents[content_id].source_subsystem
                    self.metrics['subsystem_admissions'][subsystem] += 1
            
            return admitted
    
    def _add_content(
        self, 
        content_id: str, 
        content: Any, 
        strength: float, 
        source_subsystem: str,
        timestamp: float,
        metadata: Dict[str, Any]
    ) -> None:
        """
        Add content to the workspace.
        
        Args:
            content_id: Identifier for the content
            content: The content itself
            strength: Strength of the content
            source_subsystem: Source subsystem identifier
            timestamp: Timestamp for the content
            metadata: Metadata for the content
        """
        workspace_content = WorkspaceContent(
            content_id=content_id,
            content=content,
            strength=strength,
            source_subsystem=source_subsystem,
            timestamp=timestamp,
            last_update=timestamp,
            access_count=1,
            broadcast_count=0,
            metadata=metadata
        )
        
        self.workspace_contents[content_id] = workspace_content
        
        # Update access pattern
        if content_id not in self.access_patterns:
            self.access_patterns[content_id] = 0.0
        
        self.access_patterns[content_id] += strength
        
        # Record event
        self._record_event(
            'admission',
            content_id,
            timestamp,
            strength,
            source_subsystem,
            [],
            {'metadata': metadata}
        )
    
    def _remove_content(self, content_id: str, reason: str) -> None:
        """
        Remove content from the workspace.
        
        Args:
            content_id: Identifier for the content
            reason: Reason for removal ('decay', 'replacement', 'expiration')
        """
        if content_id not in self.workspace_contents:
            return
        
        content = self.workspace_contents[content_id]
        del self.workspace_contents[content_id]
        
        # Record event
        self._record_event(
            'expulsion',
            content_id,
            time.time(),
            content.strength,
            content.source_subsystem,
            [],
            {'reason': reason}
        )
        
        self.metrics['expulsions'] += 1
    
    def _update_history(self, admitted: Set[str]) -> None:
        """
        Update the workspace history.
        
        Args:
            admitted: Set of content IDs admitted in this update
        """
        current_time = time.time()
        
        # Create a snapshot of current workspace
        snapshot = {
            'timestamp': current_time,
            'contents': {
                cid: {
                    'content': content.content,
                    'strength': content.strength,
                    'source': content.source_subsystem
                }
                for cid, content in self.workspace_contents.items()
            },
            'admitted': admitted
        }
        
        # Add to history
        self.workspace_history.append(snapshot)
    
    def _record_event(
        self, 
        event_type: str, 
        content_id: str, 
        timestamp: float,
        strength: float,
        source_subsystem: str,
        affected_subsystems: List[str],
        metadata: Dict[str, Any]
    ) -> None:
        """
        Record a workspace event.
        
        Args:
            event_type: Type of event
            content_id: Identifier for the content
            timestamp: Event timestamp
            strength: Content strength
            source_subsystem: Source subsystem
            affected_subsystems: List of affected subsystems
            metadata: Event metadata
        """
        event = WorkspaceEvent(
            event_id=str(uuid.uuid4()),
            event_type=event_type,
            content_id=content_id,
            timestamp=timestamp,
            strength=strength,
            source_subsystem=source_subsystem,
            affected_subsystems=affected_subsystems,
            metadata=metadata
        )
        
        self.events.append(event)
    
    def broadcast_to_subsystems(
        self,
        subsystem_ids: Optional[List[str]] = None
    ) -> Dict[str, Dict[str, Dict[str, Any]]]:
        """
        Broadcast workspace contents to registered subsystems.
        
        I_j(t+1) = I_j(t) + α_j · GW(t)
        
        Args:
            subsystem_ids: Optional list of subsystem IDs to broadcast to
            
        Returns:
            Dictionary mapping subsystem_id to received content
        """
        with self.lock:
            if not self.workspace_contents:
                return {}
            
            # Determine which subsystems to broadcast to
            if subsystem_ids is None:
                target_subsystems = list(self.subsystems.keys())
            else:
                target_subsystems = [sid for sid in subsystem_ids if sid in self.subsystems]
            
            if not target_subsystems:
                return {}
            
            current_time = time.time()
            broadcast_results = {}
            affected_subsystems = []
            
            # Prepare broadcast for each subsystem
            for subsystem_id in target_subsystems:
                subsystem = self.subsystems[subsystem_id]
                influence_factor = self.subsystem_influence.get(subsystem_id, 1.0)
                
                # Filter content based on broadcast threshold
                broadcast_content = {}
                
                for content_id, content in self.workspace_contents.items():
                    # Only broadcast content above threshold
                    if content.strength >= self.broadcast_threshold:
                        # Apply influence factor to strength
                        broadcast_strength = content.strength * self.broadcast_strength * influence_factor
                        
                        if broadcast_strength >= 0.1:  # Minimum threshold
                            # Increment broadcast count
                            content.broadcast_count += 1
                            
                            # Create content entry
                            broadcast_content[content_id] = {
                                'content': content.content,
                                'strength': broadcast_strength,
                                'source_subsystem': content.source_subsystem,
                                'timestamp': current_time,
                                'metadata': content.metadata
                            }
                
                # Only send broadcast if there's content to send
                if broadcast_content:
                    # Send to subsystem
                    subsystem.receive_broadcast(broadcast_content)
                    broadcast_results[subsystem_id] = broadcast_content
                    affected_subsystems.append(subsystem_id)
            
            # Record broadcast event for each broadcasted content
            for content_id, content in self.workspace_contents.items():
                if content.strength >= self.broadcast_threshold:
                    self._record_event(
                        'broadcast',
                        content_id,
                        current_time,
                        content.strength,
                        content.source_subsystem,
                        affected_subsystems,
                        {'broadcast_strength': self.broadcast_strength}
                    )
            
            # Update metrics
            self.metrics['broadcasts'] += 1
            
            return broadcast_results
    
    def update_workspace_dynamics(self, dt: float = 1.0) -> None:
        """
        Update the dynamics of the workspace over time.
        
        dGW(t)/dt = -λ · GW(t) + ∑_i β_i · x_i · 1_{x_i ∈ GW}
        
        Args:
            dt: Time step for the update
        """
        with self.lock:
            current_time = time.time()
            
            # Apply decay to all content strengths
            for content_id in list(self.workspace_contents.keys()):
                content = self.workspace_contents[content_id]
                old_strength = content.strength
                
                # Apply exponential decay
                time_since_update = current_time - content.last_update
                decay_factor = math.exp(-self.decay_rate * time_since_update * dt)
                new_strength = old_strength * decay_factor
                
                # Update strength and timestamp
                content.strength = new_strength
                content.last_update = current_time
                
                # Remove if strength falls below threshold
                if new_strength < 0.1:
                    self._remove_content(content_id, 'decay')
    
    def monitor_access_patterns(self, learning_rate: Optional[float] = None) -> None:
        """
        Monitor and learn from patterns of access to the global workspace.
        
        M(x_i, t+1) = M(x_i, t) + γ · (P(x_i ∈ GW) - M(x_i, t))
        
        Args:
            learning_rate: Optional override for the learning rate
        """
        with self.lock:
            if not self.workspace_history:
                return
            
            # Use provided learning rate or default
            lr = learning_rate or self.learning_rate
            
            # Analyze recent history
            history_length = min(20, len(self.workspace_history))
            recent_history = list(self.workspace_history)[-history_length:]
            
            # Count access frequencies
            access_counts = defaultdict(int)
            total_updates = len(recent_history)
            
            for update in recent_history:
                for content_id in update.get('admitted', []):
                    access_counts[content_id] += 1
            
            # Update access patterns using learning rate
            for content_id, count in access_counts.items():
                frequency = count / total_updates
                
                if content_id not in self.access_patterns:
                    self.access_patterns[content_id] = frequency
                else:
                    self.access_patterns[content_id] = (
                        (1 - lr) * self.access_patterns[content_id] + 
                        lr * frequency
                    )
    
    def get_workspace_contents(self) -> Dict[str, Dict[str, Any]]:
        """
        Get the current contents of the global workspace.
        
        Returns:
            Dictionary mapping content_id to content information
        """
        with self.lock:
            return {
                content_id: {
                    'content': content.content,
                    'strength': content.strength,
                    'source_subsystem': content.source_subsystem,
                    'timestamp': content.timestamp,
                    'access_count': content.access_count,
                    'broadcast_count': content.broadcast_count,
                    'metadata': content.metadata
                }
                for content_id, content in self.workspace_contents.items()
            }
    
    def get_strongest_content(self, k: int = 1) -> List[Dict[str, Any]]:
        """
        Get the k strongest contents in the workspace.
        
        Args:
            k: Number of items to return
            
        Returns:
            List of content information dictionaries, sorted by strength
        """
        with self.lock:
            if not self.workspace_contents:
                return []
            
            sorted_contents = sorted(
                [
                    {
                        'content_id': content_id,
                        'content': content.content,
                        'strength': content.strength,
                        'source_subsystem': content.source_subsystem,
                        'timestamp': content.timestamp,
                        'age': time.time() - content.timestamp,
                        'access_count': content.access_count,
                        'broadcast_count': content.broadcast_count,
                        'metadata': content.metadata
                    }
                    for content_id, content in self.workspace_contents.items()
                ],
                key=lambda x: x['strength'],
                reverse=True
            )
            
            return sorted_contents[:k]
    
    def get_workspace_activity(self, window: int = 10) -> Dict[str, Any]:
        """
        Get statistics about workspace activity.
        
        Args:
            window: Number of recent history entries to analyze
            
        Returns:
            Dictionary with activity statistics
        """
        with self.lock:
            window = min(window, len(self.workspace_history))
            
            if window == 0:
                return {
                    'total_updates': 0,
                    'avg_contents': 0,
                    'avg_admissions': 0,
                    'content_frequency': {},
                    'source_frequency': {}
                }
            
            recent_history = list(self.workspace_history)[-window:]
            
            # Count content occurrences
            content_counts = defaultdict(int)
            source_counts = defaultdict(int)
            total_admissions = 0
            total_contents = 0
            
            for snapshot in recent_history:
                contents = snapshot.get('contents', {})
                total_contents += len(contents)
                
                for content_id, info in contents.items():
                    content_counts[content_id] += 1
                    source_counts[info.get('source', 'unknown')] += 1
                
                total_admissions += len(snapshot.get('admitted', []))
            
            # Calculate frequencies
            content_frequency = {
                content_id: count / window 
                for content_id, count in content_counts.items()
            }
            
            source_frequency = {
                source: count / window 
                for source, count in source_counts.items()
            }
            
            return {
                'total_updates': window,
                'avg_contents': total_contents / window,
                'avg_admissions': total_admissions / window,
                'content_frequency': dict(sorted(
                    content_frequency.items(), 
                    key=lambda x: x[1], 
                    reverse=True
                )),
                'source_frequency': dict(sorted(
                    source_frequency.items(), 
                    key=lambda x: x[1], 
                    reverse=True
                ))
            }
    
    def get_access_patterns(self) -> Dict[str, float]:
        """
        Get the learned access patterns.
        
        Returns:
            Dictionary mapping content_id to access frequency
        """
        with self.lock:
            return self.access_patterns.copy()
    
    def get_recent_events(
        self, 
        event_type: Optional[str] = None, 
        max_events: int = 10
    ) -> List[Dict[str, Any]]:
        """
        Get recent workspace events.
        
        Args:
            event_type: Optional event type to filter
            max_events: Maximum number of events to return
            
        Returns:
            List of event dictionaries
        """
        with self.lock:
            events = list(self.events)
            
            # Filter by event type if specified
            if event_type:
                events = [e for e in events if e.event_type == event_type]
            
            # Sort by timestamp (most recent first)
            events.sort(key=lambda e: e.timestamp, reverse=True)
            
            # Convert to dictionaries
            event_dicts = [
                {
                    'event_id': e.event_id,
                    'event_type': e.event_type,
                    'content_id': e.content_id,
                    'timestamp': e.timestamp,
                    'age': time.time() - e.timestamp,
                    'strength': e.strength,
                    'source_subsystem': e.source_subsystem,
                    'affected_subsystems': e.affected_subsystems,
                    'metadata': e.metadata
                }
                for e in events[:max_events]
            ]
            
            return event_dicts
    
    def get_metrics(self) -> Dict[str, Any]:
        """
        Get metrics about workspace performance.
        
        Returns:
            Dictionary with metrics
        """
        with self.lock:
            metrics = self.metrics.copy()
            
            # Convert defaultdicts to regular dicts for JSON serialization
            metrics['subsystem_submissions'] = dict(metrics['subsystem_submissions'])
            metrics['subsystem_admissions'] = dict(metrics['subsystem_admissions'])
            
            # Add current state information
            metrics['current_occupancy'] = len(self.workspace_contents)
            metrics['current_candidates'] = len(self.candidates)
            metrics['registered_subsystems'] = len(self.subsystems)
            
            # Calculate admission rate
            total_submissions = sum(metrics['subsystem_submissions'].values())
            total_admissions = sum(metrics['subsystem_admissions'].values())
            
            metrics['admission_rate'] = (
                total_admissions / total_submissions if total_submissions > 0 else 0.0
            )
            
            # Calculate broadcast rate
            metrics['broadcast_rate'] = (
                metrics['broadcasts'] / metrics['updates'] if metrics['updates'] > 0 else 0.0
            )
            
            return metrics
    
    def process_update_cycle(self, dt: float = 0.1) -> Dict[str, Any]:
        """
        Process a complete global workspace update cycle.
        
        Args:
            dt: Time step for dynamics update
            
        Returns:
            Dictionary with update results
        """
        with self.lock:
            # Update workspace with candidates
            admitted = self.update_workspace()
            
            # Update workspace dynamics
            self.update_workspace_dynamics(dt)
            
            # Monitor access patterns
            self.monitor_access_patterns()
            
            # Broadcast to subsystems
            broadcast_results = self.broadcast_to_subsystems()
            
            return {
                'admitted': admitted,
                'broadcast_results': broadcast_results,
                'workspace_size': len(self.workspace_contents),
                'strongest_content': self.get_strongest_content(3)
            }
    
    def start_update_thread(self) -> None:
        """
        Start a background thread that regularly updates the workspace.
        """
        if self.update_thread is not None and self.update_thread.is_alive():
            logger.warning("Update thread already running")
            return
        
        self.running = True
        
        def update_loop():
            while self.running:
                try:
                    self.process_update_cycle()
                except Exception as e:
                    logger.error(f"Error in workspace update loop: {str(e)}")
                
                time.sleep(self.update_interval)
        
        self.update_thread = threading.Thread(target=update_loop)
        self.update_thread.daemon = True
        self.update_thread.start()
        
        logger.info("Started global workspace update thread")
    
    def stop_update_thread(self) -> None:
        """
        Stop the background update thread.
        """
        self.running = False
        
        if self.update_thread and self.update_thread.is_alive():
            self.update_thread.join(timeout=1.0)
            
        self.update_thread = None
        logger.info("Stopped global workspace update thread")
    
    def force_admission(
        self, 
        content_id: str, 
        content: Any, 
        strength: float, 
        source_subsystem: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> None:
        """
        Force admission of content to the workspace, bypassing competition.
        
        Args:
            content_id: Identifier for the content
            content: The content itself
            strength: Strength of the content
            source_subsystem: Source subsystem identifier
            metadata: Optional metadata
        """
        with self.lock:
            current_time = time.time()
            
            # Remove existing content if needed to make room
            if len(self.workspace_contents) >= self.capacity:
                # Find the weakest item
                min_content_id = min(
                    self.workspace_contents.items(),
                    key=lambda x: x[1].strength
                )[0]
                
                # Remove it
                self._remove_content(min_content_id, 'forced_replacement')
            
            # Add the content
            self._add_content(
                content_id, content, strength, 
                source_subsystem, current_time, metadata or {}
            )
            
            # Update history
            self._update_history({content_id})
            
            logger.info(f"Forced admission of content {content_id} from {source_subsystem}")
    
    def clear_workspace(self) -> None:
        """
        Clear all contents from the workspace.
        """
        with self.lock:
            # Record expulsion events for all contents
            for content_id, content in self.workspace_contents.items():
                self._record_event(
                    'expulsion',
                    content_id,
                    time.time(),
                    content.strength,
                    content.source_subsystem,
                    [],
                    {'reason': 'clear_workspace'}
                )
            
            # Clear workspace
            self.workspace_contents.clear()
            
            # Update history
            self._update_history(set())
            
            logger.info("Cleared global workspace")
    
    def save_state(self) -> Dict[str, Any]:
        """
        Save the current state of the global workspace.
        
        Returns:
            Dictionary with state data
        """
        with self.lock:
            # Convert WorkspaceContent objects to dictionaries
            contents_dict = {}
            for content_id, content in self.workspace_contents.items():
                # Handle content serialization
                serialized_content = content.content
                if hasattr(content.content, 'tolist'):
                    # Convert numpy arrays
                    serialized_content = content.content.tolist()
                elif hasattr(content.content, '__dict__'):
                    # Handle objects with __dict__
                    try:
                        serialized_content = vars(content.content)
                    except:
                        # Fallback: just use string representation
                        serialized_content = str(content.content)
                
                contents_dict[content_id] = {
                    'content': serialized_content,
                    'strength': content.strength,
                    'source_subsystem': content.source_subsystem,
                    'timestamp': content.timestamp,
                    'last_update': content.last_update,
                    'access_count': content.access_count,
                    'broadcast_count': content.broadcast_count,
                    'metadata': content.metadata
                }
            
            # Convert workspace history to serializable format
            serialized_history = []
            for snapshot in self.workspace_history:
                # Convert contents in snapshot
                serialized_contents = {}
                for cid, info in snapshot.get('contents', {}).items():
                    serialized_info = dict(info)  # Make a copy
                    
                    # Handle content serialization
                    content = info.get('content')
                    if hasattr(content, 'tolist'):
                        serialized_info['content'] = content.tolist()
                    elif hasattr(content, '__dict__'):
                        try:
                            serialized_info['content'] = vars(content)
                        except:
                            serialized_info['content'] = str(content)
                    
                    serialized_contents[cid] = serialized_info
                
                serialized_history.append({
                    'timestamp': snapshot['timestamp'],
                    'contents': serialized_contents,
                    'admitted': list(snapshot.get('admitted', []))
                })
            
            # Convert events to serializable format
            serialized_events = []
            for event in self.events:
                serialized_events.append({
                    'event_id': event.event_id,
                    'event_type': event.event_type,
                    'content_id': event.content_id,
                    'timestamp': event.timestamp,
                    'strength': event.strength,
                    'source_subsystem': event.source_subsystem,
                    'affected_subsystems': event.affected_subsystems,
                    'metadata': event.metadata
                })
            
            return {
                'capacity': self.capacity,
                'temperature': self.temperature,
                'decay_rate': self.decay_rate,
                'broadcast_threshold': self.broadcast_threshold,
                'broadcast_strength': self.broadcast_strength,
                'integration_factor': self.integration_factor,
                'memory_length': self.memory_length,
                'learning_rate': self.learning_rate,
                'content_lifetime': self.content_lifetime,
                'workspace_contents': contents_dict,
                'workspace_history': serialized_history,
                'access_patterns': self.access_patterns,
                'subsystem_influence': self.subsystem_influence,
                'metrics': self.metrics,
                'events': serialized_events
            }
    
    def load_state(self, state: Dict[str, Any]) -> None:
        """
        Load a previously saved state.
        
        Args:
            state: Dictionary with state data
        """
        with self.lock:
            # Stop update thread if running
            was_running = False
            if self.running:
                was_running = True
                self.stop_update_thread()
            
            # Load configuration parameters
            self.capacity = state.get('capacity', self.capacity)
            self.temperature = state.get('temperature', self.temperature)
            self.decay_rate = state.get('decay_rate', self.decay_rate)
            self.broadcast_threshold = state.get('broadcast_threshold', self.broadcast_threshold)
            self.broadcast_strength = state.get('broadcast_strength', self.broadcast_strength)
            self.integration_factor = state.get('integration_factor', self.integration_factor)
            self.memory_length = state.get('memory_length', self.memory_length)
            self.learning_rate = state.get('learning_rate', self.learning_rate)
            self.content_lifetime = state.get('content_lifetime', self.content_lifetime)
            
            # Load workspace contents
            self.workspace_contents = {}
            for content_id, content_dict in state.get('workspace_contents', {}).items():
                self.workspace_contents[content_id] = WorkspaceContent(
                    content_id=content_id,
                    content=content_dict['content'],
                    strength=content_dict['strength'],
                    source_subsystem=content_dict['source_subsystem'],
                    timestamp=content_dict['timestamp'],
                    last_update=content_dict.get('last_update', content_dict['timestamp']),
                    access_count=content_dict.get('access_count', 1),
                    broadcast_count=content_dict.get('broadcast_count', 0),
                    metadata=content_dict.get('metadata', {})
                )
            
            # Load workspace history
            self.workspace_history = deque(maxlen=self.memory_length)
            for snapshot in state.get('workspace_history', []):
                self.workspace_history.append(snapshot)
            
            # Load access patterns
            self.access_patterns = state.get('access_patterns', {})
            
            # Load subsystem influence
            self.subsystem_influence = state.get('subsystem_influence', {})
            
            # Load metrics
            metrics_dict = state.get('metrics', {})
            self.metrics = defaultdict(int)
            for k, v in metrics_dict.items():
                if k in ('subsystem_submissions', 'subsystem_admissions'):
                    self.metrics[k] = defaultdict(int)
                    for sub_k, sub_v in v.items():
                        self.metrics[k][sub_k] = sub_v
                else:
                    self.metrics[k] = v
            
            # Load events
            self.events = deque(maxlen=self.max_events)
            for event_dict in state.get('events', []):
                self.events.append(WorkspaceEvent(
                    event_id=event_dict['event_id'],
                    event_type=event_dict['event_type'],
                    content_id=event_dict['content_id'],
                    timestamp=event_dict['timestamp'],
                    strength=event_dict['strength'],
                    source_subsystem=event_dict['source_subsystem'],
                    affected_subsystems=event_dict.get('affected_subsystems', []),
                    metadata=event_dict.get('metadata', {})
                ))
            
            # Restart update thread if it was running
            if was_running:
                self.start_update_thread()
            
            logger.info(f"Loaded global workspace state with {len(self.workspace_contents)} contents")
    
    def __del__(self):
        """Cleanup when the object is deleted."""
        self.running = False
        
        if self.executor:
            self.executor.shutdown(wait=False)


class GlobalWorkspaceModule:
    """
    Module wrapper for the GlobalWorkspace class that provides a standardized
    interface for integration with the ULTRA system.
    """
    
    def __init__(
        self,
        config: Optional[Dict[str, Any]] = None,
        log_level: int = logging.INFO
    ):
        """
        Initialize the GlobalWorkspaceModule.
        
        Args:
            config: Configuration dictionary for the GlobalWorkspace
            log_level: Logging level
        """
        logger.setLevel(log_level)
        
        # Initialize the global workspace
        self.config = config or {}
        self.workspace = GlobalWorkspace(**self.config)
        
        # Keep track of registered callbacks
        self.event_callbacks = {}
        
        logger.info("Initialized GlobalWorkspaceModule")
    
    def register_subsystem(
        self, 
        subsystem_id: str,
        influence_factor: float = 1.0
    ) -> SubsystemInterface:
        """
        Register a subsystem with the global workspace.
        
        Args:
            subsystem_id: Identifier for the subsystem
            influence_factor: Influence factor for the subsystem
            
        Returns:
            Interface for the subsystem
        """
        return self.workspace.register_subsystem(subsystem_id, influence_factor)
    
    def submit_content(
        self, 
        content_id: str, 
        content: Any, 
        strength: float, 
        source_subsystem: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> float:
        """
        Submit content to the global workspace.
        
        Args:
            content_id: Identifier for the content
            content: The content itself
            strength: Strength of the content
            source_subsystem: Source subsystem identifier
            metadata: Optional metadata
            
        Returns:
            Competition strength
        """
        return self.workspace.submit_candidate(
            content_id, content, strength, source_subsystem, metadata
        )
    
    def process_cycle(self) -> Dict[str, Any]:
        """
        Process a complete global workspace cycle.
        
        Returns:
            Dictionary with cycle results
        """
        return self.workspace.process_update_cycle()
    
    def get_contents(self) -> Dict[str, Dict[str, Any]]:
        """
        Get the current contents of the global workspace.
        
        Returns:
            Dictionary with content information
        """
        return self.workspace.get_workspace_contents()
    
    def get_strongest_content(self, k: int = 1) -> List[Dict[str, Any]]:
        """
        Get the strongest content in the workspace.
        
        Args:
            k: Number of items to return
            
        Returns:
            List of content dictionaries
        """
        return self.workspace.get_strongest_content(k)
    
    def get_metrics(self) -> Dict[str, Any]:
        """
        Get metrics about the global workspace.
        
        Returns:
            Dictionary with metrics
        """
        return self.workspace.get_metrics()
    
    def register_event_callback(
        self, 
        event_type: str,
        callback: Callable[[Dict[str, Any]], None]
    ) -> str:
        """
        Register a callback for workspace events.
        
        Args:
            event_type: Type of event to listen for
            callback: Function to call when event occurs
            
        Returns:
            Callback ID
        """
        callback_id = str(uuid.uuid4())
        self.event_callbacks[callback_id] = (event_type, callback)
        
        return callback_id
    
    def unregister_event_callback(self, callback_id: str) -> bool:
        """
        Unregister an event callback.
        
        Args:
            callback_id: ID of the callback to remove
            
        Returns:
            True if removed, False if not found
        """
        if callback_id in self.event_callbacks:
            del self.event_callbacks[callback_id]
            return True
        return False
    
    def process_events(self) -> None:
        """
        Process events and trigger callbacks.
        """
        # Get recent events
        events = self.workspace.get_recent_events(max_events=10)
        
        # Trigger callbacks
        for event in events:
            event_type = event['event_type']
            
            for callback_id, (callback_event_type, callback) in list(self.event_callbacks.items()):
                if callback_event_type == '*' or callback_event_type == event_type:
                    try:
                        callback(event)
                    except Exception as e:
                        logger.error(f"Error in event callback {callback_id}: {str(e)}")
    
    def set_config(self, config: Dict[str, Any]) -> None:
        """
        Update configuration parameters.
        
        Args:
            config: Configuration dictionary
        """
        for key, value in config.items():
            if hasattr(self.workspace, key):
                setattr(self.workspace, key, value)
        
        self.config.update(config)
        logger.info(f"Updated global workspace config with keys: {list(config.keys())}")
    
    def start_update_thread(self) -> None:
        """
        Start the background update thread.
        """
        self.workspace.start_update_thread()
    
    def stop_update_thread(self) -> None:
        """
        Stop the background update thread.
        """
        self.workspace.stop_update_thread()
    
    def save_state(self) -> Dict[str, Any]:
        """
        Save the current state.
        
        Returns:
            State dictionary
        """
        return {
            'workspace': self.workspace.save_state(),
            'config': self.config
        }
    
    def load_state(self, state: Dict[str, Any]) -> None:
        """
        Load a previously saved state.
        
        Args:
            state: State dictionary
        """
        if 'workspace' in state:
            self.workspace.load_state(state['workspace'])
        
        if 'config' in state:
            self.config = state['config']
        
        logger.info("Loaded global workspace module state")
    
    def shutdown(self) -> None:
        """
        Shut down the module.
        """
        self.workspace.stop_update_thread()
        logger.info("Global workspace module shut down")


# Create default module instance
default_module = GlobalWorkspaceModule()

# Export public classes
__all__ = [
    'GlobalWorkspace',
    'GlobalWorkspaceModule',
    'SubsystemInterface',
    'WorkspaceContent',
    'WorkspaceEvent',
    'default_module'
]