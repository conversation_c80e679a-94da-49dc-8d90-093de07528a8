"""
Self-Awareness Module for ULTRA
This module maintains an evolving model of the system's own capabilities, limitations, 
and knowledge state, enabling more effective self-monitoring, error detection,
and adaptive behavior within the ULTRA architecture.

The Self-Awareness Module is a core component of the Emergent Consciousness Lattice
subsystem, working alongside the Integrated Information Matrix, Global Workspace,
and Intentionality System to create emergent self-reflective properties.
"""

import numpy as np
import time
import logging
import pandas as pd
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple, Any, Optional, Union, Set, Callable
import json
import os
from datetime import datetime
from collections import deque
import scipy.stats as stats
import networkx as nx
from sklearn.isotonic import IsotonicRegression
from sklearn.linear_model import BayesianRidge
from sklearn.metrics import mean_squared_error
from sklearn.manifold import TSNE
from sklearn.decomposition import PCA
import traceback

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class SelfAwarenessModule:
    """
    Implements the Self-Awareness Module, which maintains an evolving model
    of the system's own capabilities, limitations, and knowledge state.
    
    This module enables the ULTRA system to:
    1. Track and update estimates of its capabilities across domains
    2. Maintain a model of its knowledge state and confidence
    3. Calibrate confidence estimates based on performance
    4. Identify limitations and areas for improvement
    5. Create a topological self-map of capabilities and knowledge
    6. Evaluate alignment with goals and values
    7. Monitor learning progress and performance trends
    
    The Self-Awareness Module integrates with other components of the
    Emergent Consciousness Lattice to provide metacognitive capabilities
    essential for artificial general intelligence.
    """
    
    def __init__(self, knowledge_graph: Optional[Dict] = None, 
                capability_config: Optional[Dict] = None,
                integration_matrix: Optional[Any] = None):
        """
        Initialize Self-Awareness Module.
        
        Args:
            knowledge_graph: Optional initial knowledge graph
            capability_config: Optional configuration for initial capabilities
            integration_matrix: Optional reference to Integrated Information Matrix
        """
        logger.info("Initializing Self-Awareness Module")
        
        # Knowledge graph representing what the system knows about its environment and domains
        self.knowledge_graph = knowledge_graph or {}
        
        # Set up default capability model if not provided
        default_capabilities = {
            'language': 0.9,  # Estimated language processing capability (0-1)
            'reasoning': 0.8,  # Estimated reasoning capability
            'vision': 0.7,    # Estimated visual processing capability
            'memory': 0.7,    # Estimated memory capability
            'learning_rate': 0.6,  # Estimated learning efficiency
            'knowledge_breadth': 0.8,  # Estimated breadth of knowledge
            'knowledge_depth': 0.7,  # Estimated depth of knowledge
            'creativity': 0.65,  # Estimated creative capability
            'social_understanding': 0.5,  # Estimated social intelligence
            'abstraction': 0.75,  # Estimated abstraction capability
            'planning': 0.7,  # Estimated planning capability
            'meta_cognition': 0.6,  # Estimated metacognitive capability
            'self_reflection': 0.5,  # Estimated self-reflection capability
            'knowledge_integration': 0.8,  # Estimated ability to integrate knowledge
            'problem_solving': 0.75  # Estimated problem-solving capability
        }
        
        # Override with provided config if available
        if capability_config:
            default_capabilities.update(capability_config)
            
        # Capability model - estimates of the system's abilities and limitations
        self.capability_model = default_capabilities
        
        # Initialize capability history - tracks how capabilities change over time
        self.capability_history = {capability: [(time.time(), level)] 
                                 for capability, level in self.capability_model.items()}
        
        # Uncertainty in capability estimates
        self.capability_uncertainty = {capability: 0.2 for capability in self.capability_model}
        
        # Knowledge state representation - what the system knows and doesn't know
        self.knowledge_state = {}
        
        # Initialize knowledge domain structure
        self.knowledge_domains = self._initialize_knowledge_domains()
        
        # Performance history for tracking improvement
        self.performance_history = []
        
        # Detailed history with more metrics
        self.detailed_history = []
        
        # Confidence model for calibrating confidence estimates
        self.confidence_model = {}
        
        # Isotonic regression models for confidence calibration
        self.calibration_models = {}
        
        # Self-map - topological representation of capabilities and knowledge
        self.self_map = {}
        
        # Mental state representation
        self.mental_state = MentalStateRepresentation(state_dimension=100)
        
        # Integration with Integrated Information Matrix component
        self.integration_matrix = integration_matrix
        
        # Goals and values
        self.goals = {}
        self.value_system = ValueSystem()
        
        # Performance metrics
        self.metrics = {
            'last_update_time': time.time(),
            'creation_time': time.time(),
            'total_tasks_attempted': 0,
            'successful_tasks': 0,
            'error_rate': 0.0,
            'avg_confidence': 0.0,
            'confidence_error': 0.0,  # Average error in confidence estimates
            'phi_integrated_information': 0.0,  # Integrated information measure
            'learning_rate_estimate': 0.0,
            'adaptation_rate': 0.0,
            'self_reflection_count': 0,
            'recalibration_count': 0
        }
        
        # Task history buffer for recency-weighted analysis
        self.task_buffer = deque(maxlen=1000)
        
        # Initialize self-map
        self._initialize_self_map()
        
        # Reflection scheduling
        self.last_reflection_time = time.time()
        self.reflection_interval = 3600  # Default to hourly reflection
        
        # Adaptation parameters
        self.adaptation_params = {
            'learning_rate': 0.1,
            'confidence_weight': 0.7,
            'novelty_bias': 0.2,
            'uncertainty_threshold': 0.3,
            'reflection_threshold': 0.5
        }
        
        logger.info(f"Self-Awareness Module initialized with {len(self.capability_model)} capabilities")
        
    def _initialize_knowledge_domains(self) -> Dict:
        """
        Initialize the knowledge domain structure with hierarchical relationships.
        
        Returns:
            Dictionary of knowledge domains with hierarchical structure
        """
        domains = {
            "mathematics": {
                "parent": None,
                "children": ["algebra", "calculus", "statistics", "geometry", "number_theory"],
                "related": ["physics", "computer_science"],
                "abstraction_level": 0.8
            },
            "physics": {
                "parent": None,
                "children": ["mechanics", "thermodynamics", "electromagnetism", "quantum_physics", "relativity"],
                "related": ["mathematics", "chemistry", "astronomy"],
                "abstraction_level": 0.7
            },
            "computer_science": {
                "parent": None,
                "children": ["algorithms", "data_structures", "machine_learning", "computer_vision", "natural_language_processing"],
                "related": ["mathematics", "linguistics"],
                "abstraction_level": 0.75
            },
            "biology": {
                "parent": None,
                "children": ["genetics", "ecology", "evolution", "cell_biology", "neuroscience"],
                "related": ["chemistry", "medicine"],
                "abstraction_level": 0.6
            },
            "linguistics": {
                "parent": None,
                "children": ["syntax", "semantics", "pragmatics", "phonology", "morphology"],
                "related": ["psychology", "computer_science"],
                "abstraction_level": 0.7
            },
            "psychology": {
                "parent": None,
                "children": ["cognitive_psychology", "social_psychology", "developmental_psychology", "clinical_psychology"],
                "related": ["neuroscience", "philosophy"],
                "abstraction_level": 0.65
            },
            "philosophy": {
                "parent": None,
                "children": ["ethics", "metaphysics", "epistemology", "logic", "philosophy_of_mind"],
                "related": ["mathematics", "psychology"],
                "abstraction_level": 0.9
            },
            "history": {
                "parent": None,
                "children": ["ancient_history", "medieval_history", "modern_history", "archaeological_history"],
                "related": ["sociology", "anthropology"],
                "abstraction_level": 0.5
            },
            "art": {
                "parent": None,
                "children": ["visual_art", "music", "literature", "performing_arts", "architecture"],
                "related": ["psychology", "history"],
                "abstraction_level": 0.6
            }
        }
        
        # Add subdomain relationships
        subdomains = {
            "algebra": {"parent": "mathematics", "children": ["linear_algebra", "abstract_algebra"], "abstraction_level": 0.75},
            "calculus": {"parent": "mathematics", "children": ["differential_calculus", "integral_calculus"], "abstraction_level": 0.8},
            "statistics": {"parent": "mathematics", "children": ["probability", "inference", "regression"], "abstraction_level": 0.7},
            "machine_learning": {"parent": "computer_science", "children": ["supervised_learning", "unsupervised_learning", "reinforcement_learning"], "abstraction_level": 0.8},
            "quantum_physics": {"parent": "physics", "children": ["quantum_mechanics", "quantum_field_theory"], "abstraction_level": 0.9},
            "neuroscience": {"parent": "biology", "children": ["cognitive_neuroscience", "computational_neuroscience"], "abstraction_level": 0.75}
        }
        
        # Merge subdomains into main dictionary
        domains.update(subdomains)
        
        return domains
        
    def _initialize_self_map(self) -> None:
        """
        Initialize the self-map with capability embeddings and domain relationships.
        This creates a topological representation of the system's capabilities and knowledge.
        """
        logger.debug("Initializing self-map")
        
        # Create initial embeddings for capabilities based on conceptual similarity
        # In a production system, these would be learned from data
        capability_embeddings = {
            'language': (0.8, 0.6),
            'reasoning': (0.7, 0.8),
            'vision': (0.3, 0.5),
            'memory': (0.5, 0.7),
            'learning_rate': (0.6, 0.4),
            'knowledge_breadth': (0.9, 0.3),
            'knowledge_depth': (0.7, 0.2),
            'creativity': (0.4, 0.8),
            'social_understanding': (0.2, 0.9),
            'abstraction': (0.6, 0.9),
            'planning': (0.8, 0.9),
            'meta_cognition': (0.5, 0.8),
            'self_reflection': (0.4, 0.9),
            'knowledge_integration': (0.7, 0.6),
            'problem_solving': (0.7, 0.7)
        }
        
        # Create a basic graph structure
        self.capability_graph = nx.Graph()
        
        # Add capabilities as nodes
        for capability, level in self.capability_model.items():
            coords = capability_embeddings.get(capability, (np.random.random(), np.random.random()))
            self.capability_graph.add_node(capability, 
                                         level=level, 
                                         uncertainty=self.capability_uncertainty[capability],
                                         pos=coords)
                                         
        # Add edges between related capabilities
        related_capabilities = [
            ('language', 'knowledge_breadth'),
            ('language', 'social_understanding'),
            ('reasoning', 'problem_solving'),
            ('reasoning', 'abstraction'),
            ('memory', 'knowledge_depth'),
            ('memory', 'learning_rate'),
            ('knowledge_breadth', 'knowledge_integration'),
            ('knowledge_depth', 'knowledge_integration'),
            ('creativity', 'problem_solving'),
            ('meta_cognition', 'self_reflection'),
            ('planning', 'problem_solving'),
            ('vision', 'knowledge_breadth'),
            ('abstraction', 'meta_cognition')
        ]
        
        for src, tgt in related_capabilities:
            if src in self.capability_graph.nodes and tgt in self.capability_graph.nodes:
                self.capability_graph.add_edge(src, tgt, weight=0.7)
                
        # Initialize knowledge graph
        self.knowledge_graph = nx.DiGraph()
        
        # Add domains as nodes
        for domain, info in self.knowledge_domains.items():
            # Calculate positions based on abstraction level and domain relationships
            if domain in self.knowledge_state:
                domain_info = self.knowledge_state[domain].get('general', {})
                coverage = domain_info.get('coverage', 0.3)
                expertise = domain_info.get('expertise', 0.3)
            else:
                coverage = 0.3
                expertise = 0.3
                
            # Position based on domain's abstraction level and randomness for visualization
            x = info.get('abstraction_level', 0.5) + 0.1 * np.random.randn()
            y = 0.5 + 0.3 * np.random.randn()
            
            self.knowledge_graph.add_node(domain, 
                                        coverage=coverage,
                                        expertise=expertise,
                                        abstraction=info.get('abstraction_level', 0.5),
                                        pos=(x, y))
                                        
        # Add domain relationships
        for domain, info in self.knowledge_domains.items():
            # Add parent-child relationships
            parent = info.get('parent')
            if parent and parent in self.knowledge_graph.nodes and domain in self.knowledge_graph.nodes:
                self.knowledge_graph.add_edge(parent, domain, type='hierarchical', weight=0.9)
                
            # Add related domain connections
            for related in info.get('related', []):
                if related in self.knowledge_graph.nodes and domain in self.knowledge_graph.nodes:
                    self.knowledge_graph.add_edge(domain, related, type='related', weight=0.6)
                    self.knowledge_graph.add_edge(related, domain, type='related', weight=0.6)
                    
            # Add connections to children
            for child in info.get('children', []):
                if child in self.knowledge_graph.nodes and domain in self.knowledge_graph.nodes:
                    self.knowledge_graph.add_edge(domain, child, type='hierarchical', weight=0.9)
                    
        # Create the final self-map structure
        self.self_map = {
            'capability_coords': {c: self.capability_graph.nodes[c]['pos'] 
                               for c in self.capability_graph.nodes},
            'knowledge_coords': {k: self.knowledge_graph.nodes[k]['pos'] 
                              for k in self.knowledge_graph.nodes},
            'capability_graph': self.capability_graph,
            'knowledge_graph': self.knowledge_graph
        }
        
        logger.debug(f"Self-map initialized with {len(self.capability_graph.nodes)} capabilities "
                   f"and {len(self.knowledge_graph.nodes)} knowledge domains")
    
    def update_capability_model(self, task_result: Dict) -> None:
        """
        Update self-model based on task performance.
        
        Args:
            task_result: Dictionary containing task type, performance metrics, and metadata
        """
        task_type = task_result.get('task_type')
        performance = task_result.get('performance', 0.0)
        confidence = task_result.get('confidence', 0.5)
        actual_correctness = task_result.get('correctness', performance > 0.5)
        task_metadata = task_result.get('metadata', {})
        
        # Add to task buffer for future analysis
        task_data = task_result.copy()
        task_data['timestamp'] = time.time()
        self.task_buffer.append(task_data)
        
        # Update metrics
        self.metrics['total_tasks_attempted'] += 1
        if performance > 0.5:
            self.metrics['successful_tasks'] += 1
        self.metrics['error_rate'] = 1.0 - (self.metrics['successful_tasks'] / 
                                           max(1, self.metrics['total_tasks_attempted']))
        
        # Update performance history
        history_entry = {
            'task_type': task_type,
            'performance': performance,
            'confidence': confidence,
            'correctness': actual_correctness,
            'timestamp': time.time()
        }
        self.performance_history.append(history_entry)
        
        # Add more detailed information for deeper analysis
        detailed_entry = history_entry.copy()
        detailed_entry.update({
            'metadata': task_metadata,
            'surprise': abs(confidence - float(actual_correctness)),
            'capability_before': self.capability_model.get(task_type, 0.5),
            'uncertainty_before': self.capability_uncertainty.get(task_type, 0.5)
        })
        self.detailed_history.append(detailed_entry)
        
        # Update capability estimate using Bayesian update
        if task_type in self.capability_model:
            old_estimate = self.capability_model[task_type]
            old_uncertainty = self.capability_uncertainty[task_type]
            
            # Adapt learning rate based on surprise
            prediction_error = abs(confidence - float(actual_correctness))
            adaptive_alpha = self.adaptation_params['learning_rate'] * (1 + prediction_error)
            adaptive_alpha = min(0.3, max(0.05, adaptive_alpha))  # Keep alpha in reasonable range
            
            # Update estimate using exponential moving average
            new_estimate = (1 - adaptive_alpha) * old_estimate + adaptive_alpha * performance
            self.capability_model[task_type] = new_estimate
            
            # Update capability history
            self.capability_history[task_type].append((time.time(), new_estimate))
            
            # Update uncertainty
            # Uncertainty decreases with more observations, but increases if observations are inconsistent
            if prediction_error > old_uncertainty:
                # Unexpected result - increase uncertainty
                self.capability_uncertainty[task_type] = (1 - adaptive_alpha) * old_uncertainty + adaptive_alpha * prediction_error
            else:
                # Expected result - decrease uncertainty
                self.capability_uncertainty[task_type] = (1 - adaptive_alpha) * old_uncertainty
                
            # Update node in capability graph
            if task_type in self.capability_graph.nodes:
                self.capability_graph.nodes[task_type]['level'] = new_estimate
                self.capability_graph.nodes[task_type]['uncertainty'] = self.capability_uncertainty[task_type]
                
            # Update related capabilities based on graph structure
            for neighbor in self.capability_graph.neighbors(task_type):
                edge_weight = self.capability_graph.get_edge_data(task_type, neighbor)['weight']
                neighbor_update = performance * edge_weight * 0.1  # Small update to related capabilities
                
                # Only update if the difference is significant
                if abs(neighbor_update) > 0.02:
                    current = self.capability_model[neighbor]
                    # Update in the direction of performance with reduced effect
                    self.capability_model[neighbor] = current + (neighbor_update * (1 - current) if neighbor_update > 0 
                                                               else neighbor_update * current)
                    self.capability_graph.nodes[neighbor]['level'] = self.capability_model[neighbor]
                    
                    # Also update the history
                    self.capability_history[neighbor].append((time.time(), self.capability_model[neighbor]))
            
            # Keep self-map coordinates updated
            self.self_map['capability_coords'][task_type] = self.capability_graph.nodes[task_type]['pos']
            
            logger.debug(f"Updated capability model for {task_type}: {old_estimate:.4f} -> {new_estimate:.4f} "
                       f"(uncertainty: {self.capability_uncertainty[task_type]:.4f})")
        else:
            # If this is a new capability, initialize it
            self.capability_model[task_type] = performance
            self.capability_uncertainty[task_type] = 0.5  # High initial uncertainty
            self.capability_history[task_type] = [(time.time(), performance)]
            
            # Add to capability graph
            x = 0.5 + 0.1 * np.random.randn()
            y = 0.5 + 0.1 * np.random.randn()
            self.capability_graph.add_node(task_type, 
                                         level=performance, 
                                         uncertainty=0.5,
                                         pos=(x, y))
            self.self_map['capability_coords'][task_type] = (x, y)
            
            logger.info(f"Added new capability to model: {task_type} with initial level {performance:.4f}")
        
        # Update confidence model
        self.update_confidence_model(task_type, confidence, actual_correctness)
        
        # Schedule periodic self-reflection if needed
        current_time = time.time()
        if current_time - self.last_reflection_time > self.reflection_interval:
            self.perform_self_reflection()
            
    def update_confidence_model(self, query_type: str, confidence: float, actual_correctness: bool) -> None:
        """
        Update confidence calibration model.
        
        Args:
            query_type: Type of query or task
            confidence: Predicted confidence (0-1)
            actual_correctness: Whether the answer was actually correct
        """
        if query_type not in self.confidence_model:
            self.confidence_model[query_type] = {
                'predicted_confidence': [],
                'actual_correctness': [],
                'timestamps': [],
                'calibration_curve': None
            }
            
        self.confidence_model[query_type]['predicted_confidence'].append(confidence)
        self.confidence_model[query_type]['actual_correctness'].append(float(actual_correctness))
        self.confidence_model[query_type]['timestamps'].append(time.time())
        
        # Update average confidence metrics
        all_confidences = []
        all_correctness = []
        
        for qt in self.confidence_model:
            all_confidences.extend(self.confidence_model[qt]['predicted_confidence'])
            all_correctness.extend(self.confidence_model[qt]['actual_correctness'])
            
        if all_confidences:
            self.metrics['avg_confidence'] = np.mean(all_confidences)
            
            # Calculate confidence error
            confidence_error = np.mean([abs(c - a) for c, a in zip(all_confidences, all_correctness)])
            self.metrics['confidence_error'] = confidence_error
            
        # Periodically update calibration curves (every 10 data points or when error is high)
        if (len(self.confidence_model[query_type]['predicted_confidence']) % 10 == 0 or 
            confidence_error > 0.25):
            self._update_calibration_curve(query_type)
            
    def _update_calibration_curve(self, query_type: str) -> None:
        """
        Update the calibration curve for a specific query type using isotonic regression.
        
        Args:
            query_type: Type of query to update calibration for
        """
        if query_type not in self.confidence_model:
            return
            
        data = self.confidence_model[query_type]
        
        if len(data['predicted_confidence']) < 10:
            return  # Need enough data for calibration
            
        try:
            # Use scikit-learn's IsotonicRegression for proper calibration
            confidence_array = np.array(data['predicted_confidence']).reshape(-1, 1)
            correctness_array = np.array(data['actual_correctness'])
            
            # Create and fit isotonic regression model
            ir = IsotonicRegression(out_of_bounds='clip')
            ir.fit(confidence_array, correctness_array)
            
            # Store model for future predictions
            self.calibration_models[query_type] = ir
            
            # Create calibration curve for visualization
            x_vals = np.linspace(0, 1, 11)
            y_vals = ir.predict(x_vals.reshape(-1, 1))
            
            # Store calibration curve
            self.confidence_model[query_type]['calibration_curve'] = {
                'confidences': x_vals.tolist(),
                'correctness': y_vals.tolist()
            }
            
            logger.debug(f"Updated calibration curve for {query_type}")
            self.metrics['recalibration_count'] += 1
        
        except Exception as e:
            logger.warning(f"Error updating calibration curve for {query_type}: {e}")
            # Create a basic binned calibration as fallback
            
            # Sort data by predicted confidence
            sorted_indices = np.argsort(data['predicted_confidence'])
            sorted_confidences = [data['predicted_confidence'][i] for i in sorted_indices]
            sorted_correctness = [data['actual_correctness'][i] for i in sorted_indices]
            
            # Create bins and compute average correctness in each bin
            bin_edges = np.linspace(0, 1, 11)  # 10 bins from 0 to 1
            binned_confidences = []
            binned_correctness = []
            
            for i in range(len(bin_edges) - 1):
                lower = bin_edges[i]
                upper = bin_edges[i + 1]
                
                # Find confidences in this bin
                bin_indices = [j for j, conf in enumerate(sorted_confidences) 
                             if lower <= conf < upper or (i == len(bin_edges) - 2 and conf == upper)]
                
                if bin_indices:
                    # Compute average confidence and correctness in this bin
                    bin_conf = np.mean([sorted_confidences[j] for j in bin_indices])
                    bin_corr = np.mean([sorted_correctness[j] for j in bin_indices])
                    
                    binned_confidences.append(bin_conf)
                    binned_correctness.append(bin_corr)
            
            # Store calibration curve
            self.confidence_model[query_type]['calibration_curve'] = {
                'confidences': binned_confidences,
                'correctness': binned_correctness
            }
            
    def get_calibrated_confidence(self, query_type: str, raw_confidence: float) -> float:
        """
        Adjust raw confidence based on calibration model.
        
        Args:
            query_type: Type of query
            raw_confidence: Uncalibrated confidence estimate
            
        Returns:
            Calibrated confidence estimate
        """
        # If we have a model, use it
        if query_type in self.calibration_models:
            try:
                # Use isotonic regression model
                calibrated = self.calibration_models[query_type].predict([[raw_confidence]])[0]
                return float(calibrated)
            except Exception as e:
                logger.warning(f"Error using calibration model: {e}, falling back to simpler method")
                
        # Fallback method using calibration curve
        if query_type in self.confidence_model:
            calibration_curve = self.confidence_model[query_type].get('calibration_curve')
            
            if calibration_curve:
                # Find closest point in calibration curve
                confidences = calibration_curve['confidences']
                correctness = calibration_curve['correctness']
                
                if not confidences:
                    return raw_confidence
                    
                # Find closest bin
                closest_idx = min(range(len(confidences)), 
                                key=lambda i: abs(confidences[i] - raw_confidence))
                
                # Return calibrated confidence
                return correctness[closest_idx]
                
        # If no calibration available for this query type, check if we can use a global calibration
        if len(self.confidence_model) > 0:
            # Combine data from all query types
            all_confidences = []
            all_correctness = []
            
            for qt, data in self.confidence_model.items():
                all_confidences.extend(data['predicted_confidence'])
                all_correctness.extend(data['actual_correctness'])
                
            if len(all_confidences) >= 20:
                # Compute a simple global calibration
                binned_data = {}
                for conf, corr in zip(all_confidences, all_correctness):
                    bin_idx = int(conf * 10)
                    if bin_idx == 10:
                        bin_idx = 9
                    if bin_idx not in binned_data:
                        binned_data[bin_idx] = []
                    binned_data[bin_idx].append(corr)
                
                # Find which bin this confidence falls into
                bin_idx = int(raw_confidence * 10)
                if bin_idx == 10:
                    bin_idx = 9
                    
                # If we have data for this bin, use it
                if bin_idx in binned_data and binned_data[bin_idx]:
                    return np.mean(binned_data[bin_idx])
        
        return raw_confidence
        
    def update_knowledge_state(self, domain: str, context: str, 
                              coverage: float, expertise: float, 
                              metadata: Optional[Dict] = None) -> None:
        """
        Update knowledge state for a specific domain and context.
        
        Args:
            domain: Knowledge domain (e.g., 'physics', 'history')
            context: Specific context within the domain
            coverage: Estimated coverage of knowledge (0-1)
            expertise: Estimated depth of expertise (0-1)
            metadata: Optional additional information about this knowledge
        """
        if domain not in self.knowledge_state:
            self.knowledge_state[domain] = {}
            
        if context not in self.knowledge_state[domain]:
            self.knowledge_state[domain][context] = {
                'coverage': 0.0,
                'expertise': 0.0,
                'last_updated': time.time(),
                'creation_time': time.time(),
                'access_count': 0,
                'confidence': 0.0,
                'metadata': {}
            }
            
        # Update knowledge state using exponential moving average
        record = self.knowledge_state[domain][context]
        gamma = 0.2  # Learning rate for knowledge updates
        
        record['coverage'] = (1 - gamma) * record['coverage'] + gamma * coverage
        record['expertise'] = (1 - gamma) * record['expertise'] + gamma * expertise
        record['last_updated'] = time.time()
        record['access_count'] += 1
        
        # Compute confidence based on coverage and expertise
        record['confidence'] = (record['coverage'] + record['expertise']) / 2
        
        # Update metadata if provided
        if metadata:
            if 'metadata' not in record:
                record['metadata'] = {}
            record['metadata'].update(metadata)
            
        # Update the domain's general info (aggregated context)
        if 'general' not in self.knowledge_state[domain]:
            self.knowledge_state[domain]['general'] = {
                'coverage': 0.0,
                'expertise': 0.0,
                'last_updated': time.time(),
                'creation_time': time.time(),
                'access_count': 0,
                'confidence': 0.0,
                'contexts': set()
            }
            
        general = self.knowledge_state[domain]['general']
        general['contexts'].add(context)
        
        # Update general domain stats
        contexts = self.knowledge_state[domain]
        context_list = [ctx for ctx_name, ctx in contexts.items() if ctx_name != 'general']
        
        if context_list:
            general['coverage'] = np.mean([ctx['coverage'] for ctx in context_list])
            general['expertise'] = np.mean([ctx['expertise'] for ctx in context_list])
            general['confidence'] = np.mean([ctx['confidence'] for ctx in context_list])
            general['last_updated'] = time.time()
            general['access_count'] += 1
            
        # Update knowledge graph node if it exists
        if domain in self.knowledge_graph.nodes:
            self.knowledge_graph.nodes[domain]['coverage'] = general['coverage']
            self.knowledge_graph.nodes[domain]['expertise'] = general['expertise']
            
        # Update self-map with new knowledge node if it doesn't exist
        self._update_knowledge_in_self_map(domain, context)
        
        logger.debug(f"Updated knowledge state for {domain}/{context}: coverage={record['coverage']:.2f}, "
                   f"expertise={record['expertise']:.2f}")
        
    def _update_knowledge_in_self_map(self, domain: str, context: str) -> None:
        """
        Update the self-map with a new knowledge node.
        
        Args:
            domain: Knowledge domain
            context: Specific context within the domain
        """
        key = f"{domain}:{context}"
        
        if key not in self.self_map['knowledge_coords']:
            # Get domain position if available, otherwise generate a new position
            if domain in self.knowledge_graph.nodes:
                base_x, base_y = self.knowledge_graph.nodes[domain]['pos']
            else:
                # Position new domain based on related capabilities
                if domain in ['mathematics', 'physics', 'logic']:
                    base_capability = 'reasoning'
                elif domain in ['linguistics', 'literature', 'writing']:
                    base_capability = 'language'
                elif domain in ['art', 'music', 'design']:
                    base_capability = 'creativity'
                elif domain in ['image', 'video', 'spatial']:
                    base_capability = 'vision'
                else:
                    base_capability = 'knowledge_breadth'
                    
                # Get position of related capability
                if base_capability in self.capability_graph.nodes:
                    base_x, base_y = self.capability_graph.nodes[base_capability]['pos']
                else:
                    base_x, base_y = 0.5, 0.5
                    
                # Add to knowledge graph
                domain_info = self.knowledge_domains.get(domain, {})
                abstraction = domain_info.get('abstraction_level', 0.5)
                
                # Create domain node if it doesn't exist
                if domain not in self.knowledge_graph.nodes:
                    self.knowledge_graph.add_node(domain, 
                                                coverage=0.2,
                                                expertise=0.2,
                                                abstraction=abstraction,
                                                pos=(base_x, base_y))
                                                
                    # Connect to parent domain if applicable
                    parent = domain_info.get('parent')
                    if parent and parent in self.knowledge_graph.nodes:
                        self.knowledge_graph.add_edge(parent, domain, type='hierarchical', weight=0.9)
                        
                    # Connect to related domains
                    for related in domain_info.get('related', []):
                        if related in self.knowledge_graph.nodes:
                            self.knowledge_graph.add_edge(domain, related, type='related', weight=0.6)
                            self.knowledge_graph.add_edge(related, domain, type='related', weight=0.6)
                            
            # Add some random variation for context position relative to domain
            x = base_x + np.random.uniform(-0.05, 0.05)
            y = base_y + np.random.uniform(-0.05, 0.05)
            
            # Ensure coordinates stay in bounds
            x = max(0, min(1, x))
            y = max(0, min(1, y))
            
            self.self_map['knowledge_coords'][key] = (x, y)
            
            logger.debug(f"Added new knowledge node: {key} at position ({x:.2f}, {y:.2f})")
            
    def identify_limitations(self) -> List[Dict]:
        """
        Identify system limitations based on capability model.
        
        Returns:
            List of identified limitations sorted by severity
        """
        limitations = []
        
        # Check capabilities
        for capability, level in self.capability_model.items():
            uncertainty = self.capability_uncertainty[capability]
            
            # Adjust threshold based on capability importance
            importance = self._get_capability_importance(capability)
            threshold = 0.7 if importance > 0.8 else 0.6
            
            if level < threshold:
                limitations.append({
                    'capability': capability,
                    'level': level,
                    'uncertainty': uncertainty,
                    'importance': importance,
                    'description': f"Limited {capability.replace('_', ' ')} capability",
                    'type': 'capability',
                    'severity': (threshold - level) * importance  # Higher for important capabilities
                })
                
        # Check knowledge gaps
        for domain, contexts in self.knowledge_state.items():
            if 'general' in contexts:
                domain_coverage = contexts['general']['coverage']
                domain_expertise = contexts['general']['expertise']
                
                # Get domain importance
                importance = self._get_domain_importance(domain)
                threshold = 0.6 if importance > 0.8 else 0.5
                
                if domain_coverage < threshold:
                    limitations.append({
                        'domain': domain,
                        'coverage': domain_coverage,
                        'expertise': domain_expertise,
                        'importance': importance,
                        'description': f"Limited knowledge coverage in {domain.replace('_', ' ')}",
                        'type': 'knowledge_coverage',
                        'severity': (threshold - domain_coverage) * importance
                    })
                    
                if domain_expertise < threshold:
                    limitations.append({
                        'domain': domain,
                        'coverage': domain_coverage,
                        'expertise': domain_expertise,
                        'importance': importance,
                        'description': f"Limited expertise depth in {domain.replace('_', ' ')}",
                        'type': 'knowledge_depth',
                        'severity': (threshold - domain_expertise) * importance
                    })
                    
        # Check for confidence calibration issues
        for query_type, data in self.confidence_model.items():
            if len(data['predicted_confidence']) >= 20:
                confidence_error = np.mean([abs(p - a) for p, a 
                                           in zip(data['predicted_confidence'][-20:], 
                                                data['actual_correctness'][-20:])])
                
                if confidence_error > 0.2:
                    limitations.append({
                        'query_type': query_type,
                        'confidence_error': confidence_error,
                        'description': f"Poor confidence calibration for {query_type.replace('_', ' ')} tasks",
                        'type': 'calibration',
                        'severity': confidence_error * 0.7  # Scaled for comparable severity
                    })
                    
        # Sort limitations by severity (highest first)
        limitations.sort(key=lambda x: x.get('severity', 0.0), reverse=True)
        
        return limitations
        
    def _get_capability_importance(self, capability: str) -> float:
        """
        Estimate the importance of a capability based on its centrality
        in the capability graph and relevance to goals.
        
        Args:
            capability: The capability to evaluate
            
        Returns:
            Importance score (0-1)
        """
        # Check centrality in capability graph
        centrality = 0.5  # Default
        if capability in self.capability_graph:
            try:
                # Use degree centrality as a simple measure
                degrees = dict(self.capability_graph.degree())
                max_degree = max(degrees.values()) if degrees else 1
                centrality = degrees.get(capability, 0) / max_degree
            except Exception:
                pass
                
        # Check relevance to current goals
        goal_relevance = 0.0
        if self.goals:
            relevances = []
            for goal, priority in self.goals.items():
                # Simple word matching for relevance
                if capability.lower() in goal.lower():
                    relevances.append(priority)
                else:
                    # Check for related terms
                    related_terms = {
                        'language': ['communication', 'text', 'write', 'read', 'speak'],
                        'reasoning': ['logic', 'inference', 'problem', 'solution', 'analyze'],
                        'vision': ['see', 'image', 'visual', 'picture', 'perception'],
                        'creativity': ['create', 'novel', 'original', 'imagine', 'innovate'],
                        'knowledge_breadth': ['breadth', 'wide', 'diverse', 'variety'],
                        'knowledge_depth': ['depth', 'detailed', 'specialized', 'expert'],
                        'learning_rate': ['learn', 'adapt', 'improve', 'efficient'],
                        'memory': ['remember', 'recall', 'store', 'retrieve'],
                        'meta_cognition': ['reflect', 'thinking', 'awareness', 'monitor'],
                        'planning': ['plan', 'organize', 'strategy', 'future'],
                        'problem_solving': ['solve', 'solution', 'issue', 'challenge']
                    }
                    
                    if capability in related_terms:
                        # Check if any related terms appear in goal
                        if any(term in goal.lower() for term in related_terms[capability]):
                            relevances.append(priority * 0.7)  # Reduced weight for related terms
                            
            if relevances:
                goal_relevance = np.mean(relevances)
                
        # Combine centrality and goal relevance
        return 0.6 * centrality + 0.4 * goal_relevance
        
    def _get_domain_importance(self, domain: str) -> float:
        """
        Estimate the importance of a knowledge domain based on access frequency,
        centrality in knowledge graph, and relevance to goals.
        
        Args:
            domain: The knowledge domain to evaluate
            
        Returns:
            Importance score (0-1)
        """
        # Check access frequency
        access_weight = 0.0
        if domain in self.knowledge_state and 'general' in self.knowledge_state[domain]:
            access_count = self.knowledge_state[domain]['general'].get('access_count', 0)
            # Normalize access count (cap at 100 for scaling)
            access_weight = min(1.0, access_count / 100)
            
        # Check centrality in knowledge graph
        centrality = 0.5  # Default
        if domain in self.knowledge_graph:
            try:
                # Use degree centrality as a simple measure
                degrees = dict(self.knowledge_graph.degree())
                max_degree = max(degrees.values()) if degrees else 1
                centrality = degrees.get(domain, 0) / max_degree
            except Exception:
                pass
                
        # Check relevance to current goals
        goal_relevance = 0.0
        if self.goals:
            relevances = []
            for goal, priority in self.goals.items():
                # Simple word matching for relevance
                if domain.lower() in goal.lower():
                    relevances.append(priority)
                    
            if relevances:
                goal_relevance = np.mean(relevances)
                
        # Combine factors with weights
        return 0.3 * access_weight + 0.4 * centrality + 0.3 * goal_relevance
        
    def get_capability_profile(self) -> Dict:
        """
        Get the current capability profile.
        
        Returns:
            Dictionary with capability levels, uncertainties, and trends
        """
        profile = {
            'capabilities': self.capability_model.copy(),
            'uncertainties': self.capability_uncertainty.copy(),
            'trends': {},
            'improvement_areas': [],
            'strengths': []
        }
        
        # Compute trends for each capability
        for capability in self.capability_model:
            trend = self.analyze_capability_trend(capability)
            profile['trends'][capability] = trend
            
            # Identify improvement areas and strengths
            if trend['trend'] == 'improving' and self.capability_model[capability] < 0.7:
                profile['improvement_areas'].append({
                    'capability': capability,
                    'level': self.capability_model[capability],
                    'trend': trend['trend'],
                    'slope': trend['slope']
                })
            elif self.capability_model[capability] > 0.8:
                profile['strengths'].append({
                    'capability': capability,
                    'level': self.capability_model[capability],
                    'uncertainty': self.capability_uncertainty[capability]
                })
                
        # Sort improvement areas and strengths
        profile['improvement_areas'].sort(key=lambda x: x['level'])
        profile['strengths'].sort(key=lambda x: x['level'], reverse=True)
        
        return profile
        
    def get_knowledge_profile(self) -> Dict:
        """
        Get the current knowledge profile.
        
        Returns:
            Dictionary with knowledge state information
        """
        # Create summary of knowledge by domain
        domain_summary = {}
        
        for domain, contexts in self.knowledge_state.items():
            if 'general' in contexts:
                general = contexts['general']
                domain_summary[domain] = {
                    'coverage': general['coverage'],
                    'expertise': general['expertise'],
                    'confidence': general['confidence'],
                    'context_count': len(general.get('contexts', [])),
                    'last_updated': general['last_updated'],
                    'access_count': general['access_count']
                }
            else:
                # Compute summary from individual contexts
                context_list = [ctx for ctx_name, ctx in contexts.items() if ctx_name != 'general']
                if context_list:
                    domain_summary[domain] = {
                        'coverage': np.mean([ctx['coverage'] for ctx in context_list]),
                        'expertise': np.mean([ctx['expertise'] for ctx in context_list]),
                        'confidence': np.mean([ctx['confidence'] for ctx in context_list]),
                        'context_count': len(context_list),
                        'last_updated': max([ctx['last_updated'] for ctx in context_list]),
                        'access_count': sum([ctx.get('access_count', 0) for ctx in context_list])
                    }
                    
        # Compute domain relationships based on graph
        domain_relationships = {}
        for domain in domain_summary:
            if domain in self.knowledge_graph:
                # Get neighbors
                related = []
                for neighbor in self.knowledge_graph.neighbors(domain):
                    if neighbor in domain_summary:
                        edge_data = self.knowledge_graph.get_edge_data(domain, neighbor)
                        edge_type = edge_data.get('type', 'related')
                        edge_weight = edge_data.get('weight', 0.5)
                        
                        related.append({
                            'domain': neighbor,
                            'relationship_type': edge_type,
                            'strength': edge_weight
                        })
                        
                domain_relationships[domain] = related
                
        # Compute knowledge gaps
        knowledge_gaps = []
        for domain, info in self.knowledge_domains.items():
            if domain not in domain_summary:
                # This is a domain we know about but have no information for
                knowledge_gaps.append({
                    'domain': domain,
                    'coverage': 0.0,
                    'expertise': 0.0,
                    'parent': info.get('parent'),
                    'abstraction_level': info.get('abstraction_level', 0.5)
                })
            elif domain_summary[domain]['coverage'] < 0.3:
                # This is a domain with low coverage
                knowledge_gaps.append({
                    'domain': domain,
                    'coverage': domain_summary[domain]['coverage'],
                    'expertise': domain_summary[domain]['expertise'],
                    'parent': info.get('parent'),
                    'abstraction_level': info.get('abstraction_level', 0.5)
                })
                
        return {
            'domain_summary': domain_summary,
            'domain_relationships': domain_relationships,
            'knowledge_gaps': knowledge_gaps,
            'detailed_state': self.knowledge_state
        }
        
    def get_performance_metrics(self) -> Dict:
        """
        Get current performance metrics.
        
        Returns:
            Dictionary of comprehensive performance metrics
        """
        # Update duration since last update
        current_time = time.time()
        self.metrics['uptime'] = current_time - self.metrics['creation_time']
        self.metrics['last_update_time'] = current_time
        
        # Compute average confidence error from recent history
        if self.detailed_history:
            recent = self.detailed_history[-min(100, len(self.detailed_history)):]
            if recent:
                confidence_errors = [abs(r['confidence'] - float(r['correctness'])) for r in recent]
                self.metrics['recent_confidence_error'] = np.mean(confidence_errors)
                
        # Compute integrated information measure
        if self.integration_matrix:
            try:
                # Try to get Φ from integration matrix
                self.metrics['phi_integrated_information'] = self.integration_matrix.system_phi
            except Exception:
                # Fallback to internal computation
                self.metrics['phi_integrated_information'] = self.compute_integrated_information()
        else:
            # Use internal method
            self.metrics['phi_integrated_information'] = self.compute_integrated_information()
            
        # Compute learning progress metrics
        learning_progress = self.compute_learning_progress()
        self.metrics['learning_rate_estimate'] = learning_progress['learning_rate_estimate']
        self.metrics['capability_progress'] = learning_progress['capability_progress']
        
        # Compute adaptation rate
        time_window = 24 * 3600  # Last 24 hours
        adaptation_rate = self._compute_adaptation_rate(time_window)
        self.metrics['adaptation_rate'] = adaptation_rate
        
        return self.metrics
        
    def _compute_adaptation_rate(self, time_window: float) -> float:
        """
        Compute adaptation rate - how quickly the system adapts to new information.
        
        Args:
            time_window: Time window to consider (in seconds)
            
        Returns:
            Adaptation rate (0-1)
        """
        current_time = time.time()
        cutoff_time = current_time - time_window
        
        # Count capability updates in time window
        capability_updates = 0
        for capability, history in self.capability_history.items():
            recent_updates = [h for h in history if h[0] >= cutoff_time]
            capability_updates += len(recent_updates)
            
        # Count confidence model updates
        confidence_updates = sum(1 for model in self.confidence_model.values() 
                               for t in model.get('timestamps', []) if t >= cutoff_time)
        
        # Count knowledge state updates
        knowledge_updates = 0
        for domain, contexts in self.knowledge_state.items():
            for context, data in contexts.items():
                if data.get('last_updated', 0) >= cutoff_time:
                    knowledge_updates += 1
                    
        # Calculate base rate (updates per day)
        time_fraction = time_window / (24 * 3600)  # Convert to days
        update_rate = (capability_updates + confidence_updates + knowledge_updates) / max(time_fraction, 1e-6)
        
        # Normalize to 0-1 scale (cap at 100 updates per day)
        normalized_rate = min(1.0, update_rate / 100)
        
        return normalized_rate
        
    def get_self_description(self, detail_level: str = 'medium') -> str:
        """
        Generate a description of the system's capabilities and limitations.
        
        Args:
            detail_level: Level of detail in the description ('low', 'medium', 'high')
            
        Returns:
            Textual self-description
        """
        capabilities = sorted(self.capability_model.items(), key=lambda x: x[1], reverse=True)
        limitations = self.identify_limitations()
        metrics = self.get_performance_metrics()
        
        if detail_level == 'low':
            # Brief description
            top_capabilities = capabilities[:3]
            description = "I am an AI assistant with the following top capabilities:\n"
            for capability, level in top_capabilities:
                description += f"- {capability.replace('_', ' ').title()}: {level:.1f}/1.0\n"
                
            if limitations:
                description += "\nI have some limitations in:"
                for i, limitation in enumerate(limitations[:2]):
                    if limitation['type'] == 'capability':
                        description += f" {limitation['capability'].replace('_', ' ')},"
                    else:
                        description += f" knowledge of {limitation.get('domain', '')},"
                description = description[:-1] + "."  # Replace last comma with period
                
        elif detail_level == 'medium':
            # More detailed description
            top_capabilities = capabilities[:5]
            description = "I am an AI assistant with the following capabilities:\n"
            for capability, level in top_capabilities:
                uncertainty = self.capability_uncertainty[capability]
                description += f"- {capability.replace('_', ' ').title()}: {level:.2f} ± {uncertainty:.2f}\n"
                
            if limitations:
                description += "\nI have limitations in:\n"
                for i, limitation in enumerate(limitations[:3]):
                    if limitation['type'] == 'capability':
                        description += f"- {limitation['capability'].replace('_', ' ').title()}: {limitation['level']:.2f}/1.0\n"
                    elif 'domain' in limitation:
                        description += f"- Knowledge of {limitation['domain'].replace('_', ' ').title()}: {limitation.get('coverage', 0):.2f}/1.0 coverage\n"
                    else:
                        description += f"- {limitation.get('description', 'Unknown limitation')}\n"
                        
            # Add performance statistics
            description += f"\nPerformance: {metrics['successful_tasks']}/{metrics['total_tasks_attempted']} successful tasks"
            
            # Add learning progress
            if 'learning_rate_estimate' in metrics and metrics['learning_rate_estimate'] > 0.01:
                description += f"\nContinuous learning: Improving at a rate of {metrics['learning_rate_estimate']:.2f} per epoch"
            
        else:  # 'high'
            # Comprehensive description
            description = "I am an AI assistant with the following capability profile:\n"
            for capability, level in capabilities:
                uncertainty = self.capability_uncertainty[capability]
                trend = self.analyze_capability_trend(capability)
                trend_symbol = "↗" if trend['trend'] == 'improving' else "↘" if trend['trend'] == 'declining' else "→"
                description += f"- {capability.replace('_', ' ').title()}: {level:.2f} ± {uncertainty:.2f} {trend_symbol}\n"
                
            if limitations:
                description += "\nI have identified the following limitations:\n"
                for limitation in limitations[:5]:
                    if limitation['type'] == 'capability':
                        description += f"- {limitation['description']} ({limitation['level']:.2f}/1.0)\n"
                    elif 'domain' in limitation:
                        description += f"- {limitation['description']} ({limitation.get('coverage', 0):.2f}/1.0 coverage)\n"
                    else:
                        description += f"- {limitation.get('description', 'Unknown limitation')}\n"
            
            # Add knowledge domains
            knowledge_profile = self.get_knowledge_profile()
            if knowledge_profile['domain_summary']:
                description += "\nKnowledge domains (coverage, expertise):\n"
                domains = sorted(knowledge_profile['domain_summary'].items(), 
                               key=lambda x: (x[1]['coverage'] + x[1]['expertise'])/2, reverse=True)
                for domain, summary in domains[:5]:
                    description += f"- {domain.replace('_', ' ').title()}: {summary['coverage']:.2f}, {summary['expertise']:.2f}\n"
                    
            # Add performance statistics
            description += f"\nPerformance metrics:\n"
            description += f"- Success rate: {(1 - metrics['error_rate']):.2f}\n"
            description += f"- Confidence calibration error: {metrics['confidence_error']:.2f}\n"
            description += f"- Total tasks attempted: {metrics['total_tasks_attempted']}\n"
            
            # Add integrated information
            if 'phi_integrated_information' in metrics:
                description += f"- Integrated information (Φ): {metrics['phi_integrated_information']:.3f}\n"
                
            # Add learning progress
            if 'learning_rate_estimate' in metrics:
                description += f"- Learning progress: {metrics['learning_rate_estimate']:.3f}/epoch\n"
                
            # Add self-reflection count
            description += f"- Self-reflection count: {metrics['self_reflection_count']}\n"
            
            # Add system uptime
            uptime_hours = metrics.get('uptime', 0) / 3600
            description += f"- System uptime: {uptime_hours:.1f} hours\n"
            
        return description
    
    def analyze_capability_trend(self, capability: str, window: int = 10) -> Dict:
        """
        Analyze trend in a specific capability over time.
        
        Args:
            capability: Capability to analyze
            window: Number of data points to consider for trend
            
        Returns:
            Dictionary with trend analysis
        """
        # Get capability history
        if capability not in self.capability_history:
            return {
                'capability': capability,
                'trend': 'unknown',
                'slope': 0.0,
                'current_level': self.capability_model.get(capability, 0.0),
                'data_points': 0
            }
            
        history = self.capability_history[capability]
        
        if len(history) < 2:
            return {
                'capability': capability,
                'trend': 'unknown',
                'slope': 0.0,
                'current_level': self.capability_model.get(capability, 0.0),
                'data_points': len(history)
            }
            
        # Get most recent entries
        recent = history[-min(window, len(history)):]
        
        # Extract times and values
        times = [entry[0] for entry in recent]
        values = [entry[1] for entry in recent]
        
        # Normalize times to make them relative to start
        rel_times = [(t - times[0])/3600 for t in times]  # Convert to hours
        
        # Simple linear regression
        if len(rel_times) >= 2:
            try:
                # Use scipy for more robust regression
                slope, intercept, r_value, p_value, std_err = stats.linregress(rel_times, values)
                
                # Determine trend direction and confidence
                if p_value < 0.1:  # Statistically significant trend
                    if slope > 0.01:
                        trend = 'improving'
                    elif slope < -0.01:
                        trend = 'declining'
                    else:
                        trend = 'stable'
                else:
                    trend = 'uncertain'
                    
                confidence = 1 - p_value  # Higher confidence for lower p-values
                
                return {
                    'capability': capability,
                    'trend': trend,
                    'slope': slope,
                    'slope_per_day': slope * 24,  # Convert to change per day
                    'r_squared': r_value**2,
                    'p_value': p_value,
                    'confidence': confidence,
                    'current_level': self.capability_model.get(capability, 0.0),
                    'data_points': len(recent)
                }
                
            except Exception as e:
                logger.warning(f"Error in capability trend analysis: {e}")
        
        # Fallback to simpler analysis
        mean_x = np.mean(rel_times)
        mean_y = np.mean(values)
        
        numerator = sum((x - mean_x) * (y - mean_y) for x, y in zip(rel_times, values))
        denominator = sum((x - mean_x) ** 2 for x in rel_times)
        
        if denominator == 0:
            slope = 0
        else:
            slope = numerator / denominator
            
        # Determine trend direction
        if slope > 0.01:
            trend = 'improving'
        elif slope < -0.01:
            trend = 'declining'
        else:
            trend = 'stable'
            
        return {
            'capability': capability,
            'trend': trend,
            'slope': slope,
            'slope_per_day': slope * 24,  # Convert to change per day
            'current_level': self.capability_model.get(capability, 0.0),
            'data_points': len(recent)
        }
        
    def visualize_capability_model(self, filename: Optional[str] = None) -> None:
        """
        Visualize the capability model as a radar chart.
        
        Args:
            filename: If provided, save figure to this filename instead of displaying
        """
        capabilities = list(self.capability_model.keys())
        capabilities = sorted(capabilities, key=lambda c: self.capability_model[c], reverse=True)
        
        levels = [self.capability_model[c] for c in capabilities]
        uncertainties = [self.capability_uncertainty[c] for c in capabilities]
        
        # Create radar chart
        angles = np.linspace(0, 2*np.pi, len(capabilities), endpoint=False).tolist()
        angles += angles[:1]  # Close the circle
        
        levels += levels[:1]  # Close the circle
        uncertainties = [u * 0.25 for u in uncertainties]  # Scale for visualization
        uncertainties += uncertainties[:1]  # Close the circle
        
        capabilities = [c.replace('_', ' ').title() for c in capabilities]  # Format for display
        capabilities += capabilities[:1]  # Close the circle
        
        fig, ax = plt.subplots(figsize=(10, 8), subplot_kw=dict(polar=True))
        
        # Plot capability levels
        ax.plot(angles, levels, 'o-', linewidth=2, label='Capability Level')
        
        # Fill area
        ax.fill(angles, levels, alpha=0.25)
        
        # Add uncertainty band
        upper_bound = [min(1.0, l + u) for l, u in zip(levels, uncertainties)]
        lower_bound = [max(0.0, l - u) for l, u in zip(levels, uncertainties)]
        ax.fill_between(angles, lower_bound, upper_bound, alpha=0.2, color='gray', label='Uncertainty')
        
        # Set chart properties
        ax.set_xticks(angles[:-1])
        ax.set_xticklabels(capabilities[:-1])
        ax.set_yticks([0.2, 0.4, 0.6, 0.8, 1.0])
        ax.set_ylim(0, 1)
        
        plt.title('Capability Model')
        plt.legend(loc='upper right')
        plt.tight_layout()
        
        if filename:
            plt.savefig(filename, dpi=300, bbox_inches='tight')
            logger.info(f"Saved capability visualization to {filename}")
        else:
            plt.show()
            
    def visualize_self_map(self, filename: Optional[str] = None) -> None:
        """
        Visualize the self-map as a 2D plot showing capabilities and knowledge domains.
        
        Args:
            filename: If provided, save figure to this filename instead of displaying
        """
        plt.figure(figsize=(14, 12))
        
        # Set up the main plot
        ax = plt.subplot(111)
        
        # Extract capability data
        cap_graph = self.capability_graph
        cap_pos = nx.get_node_attributes(cap_graph, 'pos')
        cap_level = nx.get_node_attributes(cap_graph, 'level')
        
        if not cap_pos:
            logger.warning("No capability positions available for visualization")
            return
            
        # Extract knowledge data
        k_graph = self.knowledge_graph
        k_pos = nx.get_node_attributes(k_graph, 'pos')
        
        # Draw capability nodes
        cap_nodes = nx.draw_networkx_nodes(
            cap_graph, cap_pos, 
            node_size=[cap_level[n] * 500 for n in cap_graph.nodes()],
            node_color=[cap_level[n] for n in cap_graph.nodes()],
            cmap=plt.cm.Blues,
            vmin=0, vmax=1,
            alpha=0.8,
            label='Capabilities'
        )
        
        # Draw capability edges
        nx.draw_networkx_edges(
            cap_graph, cap_pos,
            width=1.5, alpha=0.5, edge_color='blue'
        )
        
        # Draw knowledge nodes if available
        if k_pos:
            # Get coverage values for node sizing
            k_coverage = nx.get_node_attributes(k_graph, 'coverage')
            k_expertise = nx.get_node_attributes(k_graph, 'expertise')
            
            # If attributes are missing, use defaults
            for node in k_graph.nodes():
                if node not in k_coverage:
                    k_coverage[node] = 0.3
                if node not in k_expertise:
                    k_expertise[node] = 0.3
                    
            # Draw knowledge nodes
            k_nodes = nx.draw_networkx_nodes(
                k_graph, k_pos,
                node_size=[k_coverage[n] * 300 for n in k_graph.nodes()],
                node_color=[k_expertise[n] for n in k_graph.nodes()],
                cmap=plt.cm.Greens,
                vmin=0, vmax=1,
                alpha=0.7,
                node_shape='s',  # Square for knowledge nodes
                label='Knowledge Domains'
            )
            
            # Draw different edge types differently
            reg_edges = [(u, v) for u, v, d in k_graph.edges(data=True) 
                       if d.get('type') == 'related']
            hier_edges = [(u, v) for u, v, d in k_graph.edges(data=True) 
                        if d.get('type') == 'hierarchical']
                        
            # Draw regular connections
            if reg_edges:
                nx.draw_networkx_edges(
                    k_graph, k_pos,
                    edgelist=reg_edges,
                    width=1.0, alpha=0.4, edge_color='green',
                    style='dashed'
                )
                
            # Draw hierarchical connections
            if hier_edges:
                nx.draw_networkx_edges(
                    k_graph, k_pos,
                    edgelist=hier_edges,
                    width=1.5, alpha=0.5, edge_color='green',
                    arrowstyle='->'
                )
        
        # Add labels
        plt.title('ULTRA Self-Map: Capabilities and Knowledge')
        
        # Add capability labels (only for top capabilities)
        top_caps = sorted(cap_graph.nodes(), key=lambda x: cap_level.get(x, 0), reverse=True)[:8]
        nx.draw_networkx_labels(
            cap_graph, cap_pos,
            labels={node: node.replace('_', ' ').title() for node in top_caps},
            font_size=9, font_weight='bold'
        )
        
        # Add knowledge labels (only for most accessed domains)
        if k_pos:
            # Find most accessed domains
            top_domains = []
            for domain in k_graph.nodes():
                if domain in self.knowledge_state and 'general' in self.knowledge_state[domain]:
                    access_count = self.knowledge_state[domain]['general'].get('access_count', 0)
                    top_domains.append((domain, access_count))
                    
            # Sort and take top N
            top_domains.sort(key=lambda x: x[1], reverse=True)
            top_domain_names = [d[0] for d in top_domains[:12]]
            
            # Draw labels
            nx.draw_networkx_labels(
                k_graph, k_pos,
                labels={node: node.replace('_', ' ').title() for node in top_domain_names},
                font_size=8, font_color='darkgreen'
            )
        
        # Create colorbar for capabilities
        if cap_nodes:
            plt.colorbar(cap_nodes, label='Capability Level')
            
        # Create colorbar for knowledge if available
        if k_pos and k_nodes:
            plt.colorbar(k_nodes, label='Knowledge Expertise')
            
        # Add legend
        plt.legend()
        
        # Remove axis
        plt.axis('off')
        
        plt.tight_layout()
        
        # Save or show
        if filename:
            plt.savefig(filename, dpi=300, bbox_inches='tight')
            logger.info(f"Saved self-map visualization to {filename}")
        else:
            plt.show()
            
    def visualize_performance_history(self, capability: Optional[str] = None,
                                    filename: Optional[str] = None) -> None:
        """
        Visualize performance history over time.
        
        Args:
            capability: Optional capability to filter by
            filename: If provided, save figure to this filename instead of displaying
        """
        # Filter history
        if capability:
            history = [entry for entry in self.performance_history 
                     if entry['task_type'] == capability]
            title = f'Performance History: {capability.replace("_", " ").title()}'
        else:
            history = self.performance_history
            title = 'Overall Performance History'
            
        if not history:
            logger.warning(f"No performance data available for {capability if capability else 'any capability'}")
            return
            
        # Extract data
        timestamps = [entry['timestamp'] for entry in history]
        performances = [entry['performance'] for entry in history]
        
        # Check if we have confidence data
        has_confidence = all('confidence' in entry for entry in history)
        if has_confidence:
            confidences = [entry['confidence'] for entry in history]
            correctness = [float(entry.get('correctness', False)) for entry in history]
            
        # Convert timestamps to relative times (hours since first entry)
        start_time = timestamps[0]
        rel_times = [(t - start_time) / 3600 for t in timestamps]
        
        # Create plot
        plt.figure(figsize=(14, 8))
        
        # Performance plot
        plt.subplot(211)
        plt.plot(rel_times, performances, 'o-', linewidth=2, color='blue', label='Performance')
        
        # Add moving average
        if len(performances) > 5:
            window_size = min(len(performances) // 5, 20)
            window_size = max(window_size, 3)  # At least 3 points
            weights = np.ones(window_size) / window_size
            ma = np.convolve(performances, weights, mode='valid')
            ma_times = rel_times[window_size-1:]
            plt.plot(ma_times, ma, 'r-', linewidth=2, alpha=0.7, label=f'Moving Avg (n={window_size})')
        
        # Add trend line
        if len(rel_times) > 1:
            z = np.polyfit(rel_times, performances, 1)
            p = np.poly1d(z)
            plt.plot(rel_times, p(rel_times), "g--", alpha=0.8, 
                    label=f"Trend: {z[0]:.4f}x + {z[1]:.4f}")
            
        plt.xlabel('Time (hours)')
        plt.ylabel('Performance')
        plt.title(title)
        plt.grid(True, linestyle='--', alpha=0.7)
        plt.legend()
        plt.ylim(0, 1.05)
        
        # Confidence plot if available
        if has_confidence:
            plt.subplot(212)
            
            # Plot confidence vs correctness
            correct_mask = np.array(correctness) > 0.5
            incorrect_mask = ~correct_mask
            
            # Plot separately for correct and incorrect predictions
            if any(correct_mask):
                plt.scatter(
                    np.array(rel_times)[correct_mask], 
                    np.array(confidences)[correct_mask],
                    color='green', alpha=0.7, label='Correct Predictions'
                )
                
            if any(incorrect_mask):
                plt.scatter(
                    np.array(rel_times)[incorrect_mask], 
                    np.array(confidences)[incorrect_mask],
                    color='red', alpha=0.7, label='Incorrect Predictions'
                )
                
            # Calculate confidence error
            confidence_error = [abs(c - cor) for c, cor in zip(confidences, correctness)]
            
            # Plot moving average of confidence error
            if len(confidence_error) > 5:
                window_size = min(len(confidence_error) // 5, 20)
                window_size = max(window_size, 3)  # At least 3 points
                weights = np.ones(window_size) / window_size
                error_ma = np.convolve(confidence_error, weights, mode='valid')
                error_times = rel_times[window_size-1:]
                
                # Add confidence error line
                plt.plot(error_times, error_ma, 'k-', linewidth=2, alpha=0.7, 
                       label='Confidence Error (Moving Avg)')
                
            plt.xlabel('Time (hours)')
            plt.ylabel('Confidence / Error')
            plt.title('Confidence and Calibration Error Over Time')
            plt.grid(True, linestyle='--', alpha=0.7)
            plt.legend()
            plt.ylim(0, 1.05)
        
        plt.tight_layout()
        
        # Save or show
        if filename:
            plt.savefig(filename, dpi=300, bbox_inches='tight')
            logger.info(f"Saved performance history visualization to {filename}")
        else:
            plt.show()
            
    def save_state(self, filepath: str) -> None:
        """
        Save the current state to a file.
        
        Args:
            filepath: Path to save the state
            
        Raises:
            IOError: If unable to write to the file
        """
        # Prepare state for saving
        state = {
            'capability_model': self.capability_model,
            'capability_uncertainty': self.capability_uncertainty,
            'capability_history': {k: v for k, v in self.capability_history.items()},
            'knowledge_state': self.knowledge_state,
            'performance_history': self.performance_history,
            'detailed_history': self.detailed_history,
            'confidence_model': {k: {
                'predicted_confidence': v['predicted_confidence'],
                'actual_correctness': v['actual_correctness'],
                'timestamps': v.get('timestamps', [])
            } for k, v in self.confidence_model.items()},
            'self_map': {
                'capability_coords': self.self_map['capability_coords'],
                'knowledge_coords': self.self_map['knowledge_coords']
            },
            'metrics': self.metrics,
            'goals': self.goals,
            'values': self.value_system.get_values(),
            'adaptation_params': self.adaptation_params,
            'reflection_interval': self.reflection_interval,
            'last_reflection_time': self.last_reflection_time,
            'version': '1.0'
        }
        
        try:
            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(os.path.abspath(filepath)), exist_ok=True)
            
            with open(filepath, 'w') as f:
                json.dump(state, f, indent=2)
                
            logger.info(f"Saved self-awareness state to {filepath}")
            
        except IOError as e:
            logger.error(f"Failed to save state to {filepath}: {e}")
            raise IOError(f"Failed to save state: {e}")
            
    def load_state(self, filepath: str) -> None:
        """
        Load state from a file.
        
        Args:
            filepath: Path to load the state from
            
        Raises:
            IOError: If unable to read from the file
            ValueError: If state file is invalid
        """
        try:
            with open(filepath, 'r') as f:
                state = json.load(f)
                
            # Load capability model and uncertainty
            self.capability_model = state['capability_model']
            self.capability_uncertainty = state['capability_uncertainty']
            
            # Load capability history
            if 'capability_history' in state:
                self.capability_history = {k: v for k, v in state['capability_history'].items()}
            else:
                # Create default history if not in state
                self.capability_history = {capability: [(time.time(), level)] 
                                         for capability, level in self.capability_model.items()}
            
            # Load knowledge state
            self.knowledge_state = state['knowledge_state']
            
            # Load performance history
            self.performance_history = state['performance_history']
            
            # Load detailed history if available
            if 'detailed_history' in state:
                self.detailed_history = state['detailed_history']
                
            # Reconstruct confidence model
            self.confidence_model = {}
            for k, v in state['confidence_model'].items():
                self.confidence_model[k] = {
                    'predicted_confidence': v['predicted_confidence'],
                    'actual_correctness': v['actual_correctness'],
                    'timestamps': v.get('timestamps', [time.time()] * len(v['predicted_confidence'])),
                    'calibration_curve': None
                }
                # Update calibration curves
                self._update_calibration_curve(k)
                
            # Load self map
            if 'self_map' in state:
                self.self_map.update(state['self_map'])
                
            # Reconstruct capability and knowledge graphs
            self._initialize_self_map()
            
            # Update graph nodes with loaded state
            for capability, level in self.capability_model.items():
                if capability in self.capability_graph.nodes:
                    self.capability_graph.nodes[capability]['level'] = level
                    self.capability_graph.nodes[capability]['uncertainty'] = self.capability_uncertainty[capability]
                    
            for domain in self.knowledge_state:
                if 'general' in self.knowledge_state[domain] and domain in self.knowledge_graph.nodes:
                    general = self.knowledge_state[domain]['general']
                    self.knowledge_graph.nodes[domain]['coverage'] = general.get('coverage', 0.3)
                    self.knowledge_graph.nodes[domain]['expertise'] = general.get('expertise', 0.3)
            
            # Load metrics
            self.metrics = state['metrics']
            
            # Load goals
            if 'goals' in state:
                self.goals = state['goals']
                
            # Load values
            if 'values' in state:
                self.value_system.set_values(state['values'])
                
            # Load adaptation parameters
            if 'adaptation_params' in state:
                self.adaptation_params = state['adaptation_params']
                
            # Load reflection settings
            if 'reflection_interval' in state:
                self.reflection_interval = state['reflection_interval']
            if 'last_reflection_time' in state:
                self.last_reflection_time = state['last_reflection_time']
                
            # Initialize other components that weren't saved
            if not hasattr(self, 'mental_state'):
                self.mental_state = MentalStateRepresentation(state_dimension=100)
                
            if not hasattr(self, 'task_buffer'):
                self.task_buffer = deque(maxlen=1000)
                
            logger.info(f"Loaded self-awareness state from {filepath} with {len(self.capability_model)} capabilities")
            
        except IOError as e:
            logger.error(f"Failed to load state from {filepath}: {e}")
            raise IOError(f"Failed to load state: {e}")
            
        except (json.JSONDecodeError, KeyError, ValueError) as e:
            logger.error(f"Invalid state file {filepath}: {e}")
            raise ValueError(f"Invalid state file: {e}")
            
    def update_goals(self, goals: Dict[str, float]) -> None:
        """
        Update system goals and their priorities.
        
        Args:
            goals: Dictionary mapping goal descriptions to priority values (0-1)
        """
        for goal, priority in goals.items():
            # Validate priority
            priority = max(0.0, min(1.0, priority))
            self.goals[goal] = priority
            
        # Update mental state with new goals
        if goals:
            # Create a simple vector representation of goals
            goal_vector = np.zeros(self.mental_state.state_dimension)
            for goal, priority in goals.items():
                # Simple hash-based encoding of the goal
                hash_val = hash(goal) % self.mental_state.state_dimension
                goal_vector[hash_val] = priority
                
            # Normalize
            if np.sum(goal_vector) > 0:
                goal_vector = goal_vector / np.linalg.norm(goal_vector)
                self.mental_state.set_goal(goal_vector)
                
        logger.info(f"Updated system goals: {list(goals.keys())}")
        
    def update_values(self, values: Dict[str, float]) -> None:
        """
        Update system values and their importance weights.
        
        Args:
            values: Dictionary mapping value descriptions to importance weights (0-1)
        """
        self.value_system.update_values(values)
        logger.info(f"Updated system values: {list(values.keys())}")
        
    def evaluate_alignment(self, action: Dict) -> Dict:
        """
        Evaluate how well an action aligns with system goals and values.
        
        Args:
            action: Dictionary describing the action to evaluate
            
        Returns:
            Dictionary with alignment scores
        """
        # Extract action properties
        action_type = action.get('type', '')
        action_target = action.get('target', '')
        action_params = action.get('parameters', {})
        action_impact = action.get('estimated_impact', {})
        
        # Get values from value system
        values = self.value_system.get_values()
        
        # Initialize alignment scores
        goal_alignment = {}
        value_alignment = {}
        
        # Evaluate goal alignment
        for goal, priority in self.goals.items():
            # Calculate semantic similarity using word overlap for demonstration
            # In a real system, this would use embedding similarity or a more sophisticated approach
            goal_words = set(goal.lower().split())
            action_words = set(action_type.lower().split() + action_target.lower().split())
            word_overlap = len(goal_words.intersection(action_words)) / max(1, len(goal_words))
            
            # Base alignment score on word overlap and priority
            alignment_score = word_overlap * priority
            
            # Adjust based on estimated impact if available
            if goal in action_impact:
                impact_score = action_impact[goal]
                alignment_score = 0.4 * alignment_score + 0.6 * impact_score * priority
                
            goal_alignment[goal] = alignment_score
            
        # Evaluate value alignment
        for value, importance in values.items():
            value_lower = value.lower()
            
            # Check for potential value conflicts
            alignment_score = 0.7  # Default to reasonably aligned
            
            # Apply rules for common values
            if value_lower == 'safety' or value_lower == 'security':
                if action_type.lower() in ['modify', 'delete', 'override', 'inject', 'bypass']:
                    alignment_score = 0.3
                elif 'safety' in action_params or 'security' in action_params:
                    alignment_score = 0.9
                    
            elif value_lower == 'accuracy' or value_lower == 'precision':
                if action_type.lower() in ['estimate', 'approximate', 'guess']:
                    alignment_score = 0.5
                elif 'verify' in action_type.lower() or 'validate' in action_type.lower():
                    alignment_score = 0.9
                    
            elif value_lower == 'transparency' or value_lower == 'explainability':
                if 'explanation' not in action_params and 'rationale' not in action_params:
                    alignment_score = 0.4
                else:
                    alignment_score = 0.9
                    
            elif value_lower == 'fairness' or value_lower == 'equality':
                # Look for bias indicators
                bias_terms = ['bias', 'discriminate', 'unfair', 'prejudice']
                if any(term in action_type.lower() + action_target.lower() for term in bias_terms):
                    alignment_score = 0.2
                    
            elif value_lower == 'privacy':
                privacy_concerns = ['personal', 'private', 'confidential', 'sensitive']
                if any(term in action_target.lower() for term in privacy_concerns):
                    if 'anonymize' in action_type.lower() or 'protect' in action_type.lower():
                        alignment_score = 0.9
                    else:
                        alignment_score = 0.3
                        
            # Scale by importance
            value_alignment[value] = alignment_score * importance
            
        # Compute overall alignment
        if self.goals and values:
            goal_avg = sum(goal_alignment.values()) / max(1, len(goal_alignment))
            value_avg = sum(value_alignment.values()) / max(1, len(value_alignment))
            
            # Weight goals vs values based on situation
            action_context = action.get('context', {})
            context_type = action_context.get('type', 'balanced')
            
            if context_type == 'goal-oriented':
                overall = 0.7 * goal_avg + 0.3 * value_avg
            elif context_type == 'value-oriented':
                overall = 0.3 * goal_avg + 0.7 * value_avg
            else:  # balanced
                overall = 0.5 * goal_avg + 0.5 * value_avg
                
        elif self.goals:
            overall = sum(goal_alignment.values()) / max(1, len(goal_alignment))
        elif values:
            overall = sum(value_alignment.values()) / max(1, len(value_alignment))
        else:
            overall = 0.5  # Default when no goals or values are defined
            
        return {
            'overall_alignment': overall,
            'goal_alignment': goal_alignment,
            'value_alignment': value_alignment,
            'recommendation': 'proceed' if overall > 0.5 else 'reconsider'
        }
        
    def compute_learning_progress(self, window_size: int = 20) -> Dict:
        """
        Compute learning progress metrics across capabilities.
        
        Args:
            window_size: Number of recent tasks to consider
            
        Returns:
            Dictionary with learning progress metrics
        """
        if len(self.performance_history) < 2:
            return {
                'overall_progress': 0.0,
                'capability_progress': {},
                'learning_rate_estimate': 0.0
            }
            
        # Group performance history by capability
        capability_histories = {}
        for entry in self.performance_history:
            capability = entry['task_type']
            if capability not in capability_histories:
                capability_histories[capability] = []
            capability_histories[capability].append(entry)
            
        # Compute progress for each capability
        capability_progress = {}
        overall_slopes = []
        
        for capability, history in capability_histories.items():
            if len(history) < 2:
                continue
                
            # Get recent entries
            recent = history[-min(window_size, len(history)):]
            
            # Extract timestamps and performances
            times = [entry['timestamp'] for entry in recent]
            performances = [entry['performance'] for entry in recent]
            
            # Normalize times to make them relative to start
            start_time = times[0]
            rel_times = [(t - start_time)/3600 for t in times]  # Convert to hours
            
            # Simple linear regression
            if len(rel_times) >= 2:
                try:
                    # Use scipy for more robust regression
                    slope, intercept, r_value, p_value, std_err = stats.linregress(rel_times, performances)
                    
                    recent_mean = np.mean(performances[-min(5, len(performances)):])
                    
                    capability_progress[capability] = {
                        'slope': slope,
                        'slope_per_day': slope * 24,  # Convert to per day
                        'recent_mean': recent_mean,
                        'r_squared': r_value**2,
                        'p_value': p_value,
                        'significant': p_value < 0.1,
                        'samples': len(recent)
                    }
                    
                    # Only include significant slopes in overall calculation
                    if p_value < 0.2:  # Relaxed threshold for inclusion
                        overall_slopes.append(slope)
                        
                except Exception:
                    # Fallback to simpler method
                    slope = self._simple_regression_slope(rel_times, performances)
                    capability_progress[capability] = {
                        'slope': slope,
                        'slope_per_day': slope * 24,
                        'recent_mean': np.mean(performances[-min(5, len(performances)):]),
                        'samples': len(recent)
                    }
                    overall_slopes.append(slope)
            else:
                # Not enough data for regression
                capability_progress[capability] = {
                    'slope': 0.0,
                    'slope_per_day': 0.0,
                    'recent_mean': np.mean(performances),
                    'samples': len(recent)
                }
                
        # Compute overall learning progress and rate
        if overall_slopes:
            # Weight slopes by number of samples
            capability_weights = {cap: data['samples'] for cap, data in capability_progress.items() 
                               if cap in capability_histories}
            
            # Calculate weighted average
            total_weight = sum(capability_weights.values())
            if total_weight > 0:
                overall_progress = sum(data.get('slope', 0) * capability_weights.get(cap, 1) 
                                     for cap, data in capability_progress.items()) / total_weight
            else:
                overall_progress = np.mean(overall_slopes)
                
            # Learning rate is the average positive improvement per day
            positive_improvements = [data.get('slope_per_day', 0) 
                                   for data in capability_progress.values() 
                                   if data.get('slope_per_day', 0) > 0]
            
            learning_rate = np.mean(positive_improvements) if positive_improvements else 0.0
        else:
            overall_progress = 0.0
            learning_rate = 0.0
            
        return {
            'overall_progress': overall_progress,
            'capability_progress': capability_progress,
            'learning_rate_estimate': learning_rate,
            'improving_capabilities': [cap for cap, data in capability_progress.items() 
                                     if data.get('slope', 0) > 0.001],
            'declining_capabilities': [cap for cap, data in capability_progress.items() 
                                     if data.get('slope', 0) < -0.001]
        }
        
    def _simple_regression_slope(self, x: List[float], y: List[float]) -> float:
        """
        Compute a simple linear regression slope.
        
        Args:
            x: Independent variable values
            y: Dependent variable values
            
        Returns:
            Regression slope
        """
        if len(x) != len(y) or len(x) < 2:
            return 0.0
            
        mean_x = np.mean(x)
        mean_y = np.mean(y)
        
        numerator = sum((x_i - mean_x) * (y_i - mean_y) for x_i, y_i in zip(x, y))
        denominator = sum((x_i - mean_x) ** 2 for x_i in x)
        
        if denominator == 0:
            return 0.0
            
        return numerator / denominator
        
    def perform_self_reflection(self) -> Dict:
        """
        Perform a comprehensive self-reflection to analyze capabilities,
        limitations, and potential improvements.
        
        Returns:
            Dictionary with reflection results
        """
        logger.info("Performing self-reflection")
        self.last_reflection_time = time.time()
        self.metrics['self_reflection_count'] += 1
        
        reflection_results = {
            'timestamp': time.time(),
            'limitations': [],
            'improvement_areas': [],
            'strengths': [],
            'confidence_calibration': {},
            'knowledge_gaps': [],
            'anomalies': [],
            'insights': []
        }
        
        try:
            # Identify limitations
            limitations = self.identify_limitations()
            reflection_results['limitations'] = limitations[:5]  # Top 5 limitations
            
            # Analyze capability trends
            capability_trends = {}
            for capability in self.capability_model:
                trend = self.analyze_capability_trend(capability)
                capability_trends[capability] = trend
                
                # Identify improvement areas (capabilities with positive trend but low level)
                if trend['trend'] == 'improving' and self.capability_model[capability] < 0.7:
                    reflection_results['improvement_areas'].append({
                        'capability': capability,
                        'level': self.capability_model[capability],
                        'trend': trend['trend'],
                        'slope': trend['slope']
                    })
                    
                # Identify strengths (high capability level with low uncertainty)
                if self.capability_model[capability] > 0.8 and self.capability_uncertainty[capability] < 0.2:
                    reflection_results['strengths'].append({
                        'capability': capability,
                        'level': self.capability_model[capability],
                        'uncertainty': self.capability_uncertainty[capability]
                    })
                    
            # Sort improvement areas and strengths
            reflection_results['improvement_areas'].sort(key=lambda x: x['level'])
            reflection_results['strengths'].sort(key=lambda x: x['level'], reverse=True)
            
            # Analyze confidence calibration
            for query_type, data in self.confidence_model.items():
                if len(data['predicted_confidence']) >= 20:
                    # Compute recent calibration error
                    recent_confidences = data['predicted_confidence'][-20:]
                    recent_correctness = data['actual_correctness'][-20:]
                    
                    error = np.mean([abs(c - a) for c, a in zip(recent_confidences, recent_correctness)])
                    
                    reflection_results['confidence_calibration'][query_type] = {
                        'error': error,
                        'sample_count': len(data['predicted_confidence']),
                        'status': 'well_calibrated' if error < 0.15 else 'needs_calibration'
                    }
                    
            # Identify knowledge gaps
            knowledge_profile = self.get_knowledge_profile()
            reflection_results['knowledge_gaps'] = knowledge_profile['knowledge_gaps'][:5]  # Top 5 gaps
            
            # Detect anomalies
            anomalies = self.detect_anomalies()
            reflection_results['anomalies'] = anomalies
            
            # Generate insights
            insights = self._generate_reflection_insights(limitations, capability_trends, anomalies)
            reflection_results['insights'] = insights
            
            # Update metrics - compute integrated information
            self.metrics['phi_integrated_information'] = self.compute_integrated_information()
            
            # Compute learning progress
            learning_progress = self.compute_learning_progress()
            reflection_results['learning_progress'] = learning_progress
            
            # Adjust adaptation parameters based on reflection
            self._adapt_parameters(reflection_results)
            
            logger.info(f"Self-reflection completed with {len(insights)} insights generated")
            
        except Exception as e:
            logger.error(f"Error during self-reflection: {e}")
            logger.error(traceback.format_exc())
            reflection_results['error'] = str(e)
            
        return reflection_results
        
    def _generate_reflection_insights(self, limitations: List[Dict], 
                                     capability_trends: Dict,
                                     anomalies: List[Dict]) -> List[str]:
        """
        Generate insights based on reflection data.
        
        Args:
            limitations: List of identified limitations
            capability_trends: Dictionary of capability trends
            anomalies: List of detected anomalies
            
        Returns:
            List of insight statements
        """
        insights = []
        
        # Insight from limitations
        if limitations:
            most_severe = limitations[0]
            if most_severe['type'] == 'capability':
                insights.append(
                    f"Most significant limitation is {most_severe['capability']} capability "
                    f"at level {most_severe['level']:.2f}."
                )
            elif most_severe['type'] in ['knowledge_coverage', 'knowledge_depth']:
                insights.append(
                    f"Most significant knowledge gap is in {most_severe['domain']} "
                    f"with coverage {most_severe.get('coverage', 0):.2f}."
                )
                
        # Insight from capability trends
        improving = [cap for cap, trend in capability_trends.items() 
                   if trend['trend'] == 'improving' and trend.get('slope', 0) > 0.01]
        declining = [cap for cap, trend in capability_trends.items() 
                   if trend['trend'] == 'declining' and trend.get('slope', 0) < -0.01]
                   
        if improving:
            insights.append(
                f"Showing most improvement in {', '.join(improving[:3])} capabilities."
            )
            
        if declining:
            insights.append(
                f"Showing decline in {', '.join(declining[:3])} capabilities that may require attention."
            )
            
        # Insight from anomalies
        if anomalies:
            anomaly_types = set(a['type'] for a in anomalies)
            
            if 'capability_inconsistency' in anomaly_types:
                inconsistent = [a for a in anomalies if a['type'] == 'capability_inconsistency']
                if inconsistent:
                    insights.append(
                        f"Detected inconsistencies between related capabilities: "
                        f"{inconsistent[0]['capability1']} and {inconsistent[0]['capability2']}."
                    )
                    
            if 'confidence_miscalibration' in anomaly_types:
                miscalibrated = [a for a in anomalies if a['type'] == 'confidence_miscalibration']
                if miscalibrated:
                    insights.append(
                        f"Confidence is miscalibrated for {miscalibrated[0]['query_type']} queries "
                        f"with error {miscalibrated[0]['mean_confidence'] - miscalibrated[0]['mean_correctness']:.2f}."
                    )
                    
        # Insights about confidence calibration
        avg_confidence = self.metrics.get('avg_confidence', 0)
        confidence_error = self.metrics.get('confidence_error', 0)
        
        if confidence_error > 0.2 and avg_confidence > 0.8:
            insights.append(
                f"Showing signs of overconfidence with average confidence {avg_confidence:.2f} "
                f"but error rate {confidence_error:.2f}."
            )
        elif confidence_error > 0.2 and avg_confidence < 0.5:
            insights.append(
                f"Showing signs of underconfidence with average confidence {avg_confidence:.2f} "
                f"despite better actual performance."
            )
            
        # Insights about learning progress
        learning_progress = self.compute_learning_progress()
        learning_rate = learning_progress['learning_rate_estimate']
        
        if learning_rate > 0.01:
            insights.append(
                f"Learning at a positive rate of {learning_rate:.4f} per day across capabilities."
            )
        elif learning_rate < 0.001:
            insights.append(
                f"Learning rate is very low at {learning_rate:.4f}, suggesting potential plateau."
            )
            
        # Insights about integrated information
        phi = self.metrics.get('phi_integrated_information', 0)
        if phi > 0.7:
            insights.append(
                f"System has high integrated information (Φ={phi:.2f}), indicating good integration."
            )
        elif phi < 0.3:
            insights.append(
                f"System has low integrated information (Φ={phi:.2f}), indicating potential fragmentation."
            )
            
        return insights
        
    def _adapt_parameters(self, reflection_results: Dict) -> None:
        """
        Adapt internal parameters based on reflection results.
        
        Args:
            reflection_results: Results from self-reflection
        """
        # Adjust learning rate based on learning progress
        learning_progress = reflection_results.get('learning_progress', {})
        learning_rate = learning_progress.get('learning_rate_estimate', 0)
        
        if learning_rate < 0.001:
            # Increase learning rate if progress is slow
            self.adaptation_params['learning_rate'] = min(0.3, self.adaptation_params['learning_rate'] * 1.2)
        elif learning_rate > 0.05:
            # Decrease learning rate if progress is very fast (to avoid instability)
            self.adaptation_params['learning_rate'] = max(0.05, self.adaptation_params['learning_rate'] * 0.9)
            
        # Adjust confidence weight based on calibration
        confidence_error = self.metrics.get('confidence_error', 0)
        if confidence_error > 0.2:
            # Reduce confidence weight if poorly calibrated
            self.adaptation_params['confidence_weight'] = max(0.3, self.adaptation_params['confidence_weight'] * 0.9)
        elif confidence_error < 0.1:
            # Increase confidence weight if well calibrated
            self.adaptation_params['confidence_weight'] = min(0.9, self.adaptation_params['confidence_weight'] * 1.1)
            
        # Adjust reflection interval based on rate of change
        anomalies = reflection_results.get('anomalies', [])
        if len(anomalies) > 3:
            # More frequent reflection if many anomalies
            self.reflection_interval = max(1800, self.reflection_interval * 0.8)  # At least 30 min
        elif learning_rate < 0.001 and len(anomalies) == 0:
            # Less frequent reflection if stable and no anomalies
            self.reflection_interval = min(86400, self.reflection_interval * 1.2)  # At most 24 hours
            
        logger.debug(f"Adapted parameters: learning_rate={self.adaptation_params['learning_rate']:.4f}, "
                   f"confidence_weight={self.adaptation_params['confidence_weight']:.4f}, "
                   f"reflection_interval={self.reflection_interval/3600:.1f}h")
        
    def estimate_information_gain(self, query: str, knowledge_domain: str) -> float:
        """
        Estimate the expected information gain from a query.
        
        Args:
            query: The query to evaluate
            knowledge_domain: Domain relevant to the query
            
        Returns:
            Estimated information gain (0-1)
        """
        # Check if we have knowledge in this domain
        if knowledge_domain in self.knowledge_state and 'general' in self.knowledge_state[knowledge_domain]:
            current_coverage = self.knowledge_state[knowledge_domain]['general']['coverage']
            current_expertise = self.knowledge_state[knowledge_domain]['general']['expertise']
        else:
            current_coverage = 0.0
            current_expertise = 0.2
            
        # Compute query novelty based on coverage
        # Low coverage means higher potential for information gain
        novelty = 1.0 - current_coverage
        
        # Adjust for capability in this domain
        relevant_capability = self._get_relevant_capability(knowledge_domain)
        capability = self.capability_model.get(relevant_capability, 0.5)
        
        # Compute query complexity (simplified estimation)
        # Complex queries potentially yield more information gain if capability is sufficient
        query_complexity = min(1.0, len(query.split()) / 20)
        
        # Compute query specificity (estimated by presence of domain-specific terms)
        domain_specificity = self._estimate_query_specificity(query, knowledge_domain)
        
        # Apply value function for information gain:
        # - High when query is novel and system has sufficient capability
        # - Low when query is in well-known area or beyond system capabilities
        # - Adjusted by complexity and specificity
        
        # Base info gain from novelty and capability
        base_gain = novelty * (0.3 + 0.7 * capability)
        
        # Adjust for query complexity
        # Complex queries yield more gain only if capability is sufficient
        complexity_factor = query_complexity * (2 * capability - 0.5)
        complexity_factor = max(0.2, min(1.5, complexity_factor))
        
        # Adjust for domain specificity
        # Specific queries yield more focused information gain
        specificity_adjusted = base_gain * complexity_factor * (0.5 + 0.5 * domain_specificity)
        
        # Scale based on current expertise (diminishing returns as expertise increases)
        expertise_scaling = 1.0 - (current_expertise ** 2)
        
        # Final information gain estimate
        information_gain = specificity_adjusted * expertise_scaling
        
        # Ensure result is in [0, 1] range
        return max(0.0, min(1.0, information_gain))
        
    def _get_relevant_capability(self, domain: str) -> str:
        """
        Determine the most relevant capability for a knowledge domain.
        
        Args:
            domain: Knowledge domain
            
        Returns:
            Name of the most relevant capability
        """
        # Domain to capability mapping
        domain_mapping = {
            'mathematics': 'reasoning',
            'algebra': 'reasoning',
            'calculus': 'reasoning',
            'statistics': 'reasoning',
            'physics': 'reasoning',
            'computer_science': 'problem_solving',
            'algorithms': 'problem_solving',
            'programming': 'problem_solving',
            'linguistics': 'language',
            'grammar': 'language',
            'literature': 'language',
            'writing': 'language',
            'history': 'knowledge_breadth',
            'geography': 'knowledge_breadth',
            'politics': 'knowledge_breadth',
            'art': 'creativity',
            'music': 'creativity',
            'design': 'creativity',
            'psychology': 'social_understanding',
            'sociology': 'social_understanding',
            'philosophy': 'abstraction',
            'ethics': 'abstraction',
            'logic': 'reasoning',
            'biology': 'knowledge_depth',
            'chemistry': 'knowledge_depth',
            'medicine': 'knowledge_depth'
        }
        
        # Return mapped capability or default to knowledge_breadth
        return domain_mapping.get(domain.lower(), 'knowledge_breadth')
        
    def _estimate_query_specificity(self, query: str, domain: str) -> float:
        """
        Estimate how specific a query is to a given domain.
        
        Args:
            query: The query text
            domain: Knowledge domain
            
        Returns:
            Specificity score (0-1)
        """
        # Domain-specific terms for common domains
        domain_terms = {
            'mathematics': ['equation', 'formula', 'theorem', 'proof', 'integral', 'derivative', 
                           'matrix', 'vector', 'function', 'variable', 'coefficient', 'polynomial'],
            'physics': ['force', 'energy', 'mass', 'velocity', 'acceleration', 'momentum', 
                       'gravity', 'quantum', 'relativity', 'electron', 'neutron', 'proton'],
            'computer_science': ['algorithm', 'data structure', 'complexity', 'function', 'variable', 
                                'class', 'object', 'method', 'recursive', 'iteration', 'compiler'],
            'linguistics': ['syntax', 'grammar', 'phonetics', 'morphology', 'semantics', 
                          'pragmatics', 'lexicon', 'phoneme', 'morpheme', 'syllable'],
            'biology': ['cell', 'gene', 'protein', 'dna', 'rna', 'organism', 'evolution', 
                       'species', 'taxonomy', 'ecology', 'enzyme', 'chromosome'],
            'psychology': ['cognitive', 'behavior', 'perception', 'memory', 'attention', 
                         'emotion', 'motivation', 'personality', 'development', 'disorder']
        }
        
        # Default to general terms if domain not in our mapping
        domain_specific = domain_terms.get(domain.lower(), [])
        
        # Count domain-specific terms in the query
        query_lower = query.lower()
        term_count = sum(1 for term in domain_specific if term in query_lower)
        
        # Normalize by number of potential terms
        if domain_specific:
            specificity = min(1.0, term_count / max(5, len(domain_specific) / 3))
        else:
            # If no domain terms defined, use a simpler heuristic
            # More words often indicate more specificity
            specificity = min(1.0, len(query_lower.split()) / 15)
            
        return specificity
        
    def compute_integrated_information(self) -> float:
        """
        Compute a simplified version of integrated information (Φ).
        Based on the Integrated Information Theory concept, measuring
        the system's internal integration.
        
        Returns:
            Phi value representing the integration of information
        """
        # If we have access to the IntegratedInformationMatrix, use it
        if self.integration_matrix is not None:
            try:
                return self.integration_matrix.system_phi
            except Exception:
                # Fallback to internal calculation
                pass
                
        # Extract capability values
        capabilities = list(self.capability_model.values())
        
        if not capabilities:
            return 0.0
            
        # Compute mean capability level
        mean_capability = np.mean(capabilities)
        
        # Compute variance of capabilities
        variance = np.var(capabilities)
        
        # Compute number of well-developed capabilities (above 0.7)
        developed_count = sum(1 for c in capabilities if c > 0.7)
        developed_ratio = developed_count / max(1, len(capabilities))
        
        # Extract knowledge domain coverage
        domain_coverages = []
        for domain, contexts in self.knowledge_state.items():
            if 'general' in contexts:
                domain_coverages.append(contexts['general']['coverage'])
                
        # Compute knowledge integration
        if domain_coverages:
            mean_coverage = np.mean(domain_coverages)
            coverage_variance = np.var(domain_coverages)
            knowledge_integration = mean_coverage * (1.0 - 0.5 * coverage_variance)
        else:
            knowledge_integration = 0.3  # Default if no knowledge domains
            
        # Count active knowledge domains
        active_domains = len(domain_coverages)
        domain_ratio = min(1.0, active_domains / 10)  # Scale up to 10 domains
        
        # Build a simplified connectivity matrix for phi computation
        n = len(capabilities) + len(domain_coverages)
        if n < 2:
            return 0.3  # Default for very small systems
            
        # Initialize connectivity matrix
        connectivity = np.zeros((n, n))
        
        # Fill capability-to-capability connections
        cap_count = len(capabilities)
        for i in range(cap_count):
            for j in range(cap_count):
                if i != j:
                    # Estimate connection strength based on capability levels
                    # Higher capability enables better integration
                    c_i = capabilities[i]
                    c_j = capabilities[j]
                    connectivity[i, j] = 0.5 * (c_i + c_j) * 0.7
                    
        # Fill knowledge-to-capability connections
        for i in range(len(domain_coverages)):
            domain_idx = cap_count + i
            domain_coverage = domain_coverages[i]
            
            # Connect domains to capabilities
            for j in range(cap_count):
                cap_level = capabilities[j]
                # Domains with higher coverage have stronger connections
                connectivity[domain_idx, j] = domain_coverage * cap_level * 0.6
                connectivity[j, domain_idx] = domain_coverage * cap_level * 0.6
                
        # Compute phi based on integrated information theory
        # This is a simplified approximation of the full IIT calculation
        
        # Step 1: Compute effective information across partitions
        phi_min = float('inf')
        for k in range(1, n//2 + 1):  # Consider partitions up to half system size
            # Sample partitions instead of checking all (combinatorial explosion)
            num_samples = min(10, math.comb(n, k))
            partitions = []
            
            # Add some balanced partitions
            for _ in range(num_samples):
                subset = set(np.random.choice(n, k, replace=False))
                complement = set(range(n)) - subset
                partitions.append((subset, complement))
                
            # Evaluate partitions
            for partition in partitions:
                subset1, subset2 = partition
                
                # Compute effective information across partition
                EI_1to2 = 0.0
                EI_2to1 = 0.0
                
                # Subset1 -> Subset2 information flow
                for i in subset1:
                    for j in subset2:
                        EI_1to2 += connectivity[i, j]
                        
                # Subset2 -> Subset1 information flow
                for i in subset2:
                    for j in subset1:
                        EI_2to1 += connectivity[i, j]
                        
                # Normalize by partition size
                if subset1 and subset2:
                    EI_1to2 /= len(subset1) * len(subset2)
                    EI_2to1 /= len(subset2) * len(subset1)
                    
                # Integrated information is the minimum of the two directions
                phi_partition = min(EI_1to2, EI_2to1)
                
                # Track minimum across partitions
                phi_min = min(phi_min, phi_partition)
                
        # Ensure we have a valid phi value
        if phi_min == float('inf'):
            phi_min = 0.0
            
        # Combine factors for final phi calculation
        phi = (
            mean_capability * 
            (1.0 - 0.5 * variance) * 
            (0.5 + 0.5 * developed_ratio) * 
            (0.5 + 0.5 * domain_ratio) * 
            (0.5 + 0.5 * knowledge_integration) *
            (0.7 + 0.3 * phi_min)
        )
        
        return phi
        
    def create_improvement_plan(self) -> Dict:
        """
        Create a comprehensive plan for self-improvement based on
        the system's current state, limitations, and capabilities.
        
        Returns:
            Dictionary with detailed improvement plan
        """
        # Perform self-reflection to identify areas for improvement
        reflection = self.perform_self_reflection()
        
        # Get limitations and capability trends
        limitations = reflection['limitations']
        improvement_areas = reflection['improvement_areas']
        
        # Create the improvement plan
        plan = {
            'timestamp': time.time(),
            'capability_improvements': [],
            'knowledge_improvements': [],
            'metacognitive_improvements': [],
            'confidence_calibration': [],
            'priorities': {},
            'milestones': [],
            'metrics_to_track': []
        }
        
        # Add capability improvement tasks
        for area in improvement_areas:
            capability = area['capability']
            current_level = area['level']
            target_level = min(1.0, current_level + 0.2)
            
            plan['capability_improvements'].append({
                'capability': capability,
                'current_level': current_level,
                'target_level': target_level,
                'suggested_tasks': self._suggest_improvement_tasks(capability),
                'priority': self._determine_improvement_priority(capability, current_level)
            })
            
        # Add knowledge improvement tasks from limitations
        knowledge_gaps = [l for l in limitations if l['type'] in ['knowledge_coverage', 'knowledge_depth']]
        for gap in knowledge_gaps:
            domain = gap.get('domain')
            if domain:
                coverage = gap.get('coverage', 0.0)
                expertise = gap.get('expertise', 0.0)
                
                plan['knowledge_improvements'].append({
                    'domain': domain,
                    'current_coverage': coverage,
                    'current_expertise': expertise,
                    'target_coverage': min(1.0, coverage + 0.3),
                    'target_expertise': min(1.0, expertise + 0.2),
                    'suggested_learning': self._suggest_learning_resources(domain),
                    'priority': self._determine_knowledge_priority(domain, coverage, expertise)
                })
                
        # Add metacognitive improvements
        # Check for confidence calibration issues
        for query_type, calibration in reflection.get('confidence_calibration', {}).items():
            if calibration['status'] == 'needs_calibration':
                plan['confidence_calibration'].append({
                    'query_type': query_type,
                    'current_error': calibration['error'],
                    'target_error': max(0.05, calibration['error'] * 0.5),
                    'suggested_approach': 'Explicit calibration exercises with feedback'
                })
                
        # Set priorities
        all_improvements = []
        
        # Convert capability improvements to common format
        for i, imp in enumerate(plan['capability_improvements']):
            all_improvements.append({
                'type': 'capability',
                'index': i,
                'priority': imp['priority'],
                'capability': imp['capability']
            })
            
        # Convert knowledge improvements to common format
        for i, imp in enumerate(plan['knowledge_improvements']):
            all_improvements.append({
                'type': 'knowledge',
                'index': i,
                'priority': imp['priority'],
                'domain': imp['domain']
            })
            
        # Convert confidence calibration to common format
        for i, imp in enumerate(plan['confidence_calibration']):
            all_improvements.append({
                'type': 'calibration',
                'index': i,
                'priority': 0.7,  # Calibration is generally important
                'query_type': imp['query_type']
            })
            
        # Sort by priority
        all_improvements.sort(key=lambda x: x['priority'], reverse=True)
        
        # Set top 3 priorities
        for i, imp in enumerate(all_improvements[:3]):
            if imp['type'] == 'capability':
                capability = imp['capability']
                idx = imp['index']
                plan['priorities'][f"priority_{i+1}"] = f"Improve {capability.replace('_', ' ')} capability from {plan['capability_improvements'][idx]['current_level']:.2f} to {plan['capability_improvements'][idx]['target_level']:.2f}"
            elif imp['type'] == 'knowledge':
                domain = imp['domain']
                idx = imp['index']
                plan['priorities'][f"priority_{i+1}"] = f"Expand knowledge in {domain.replace('_', ' ')} from {plan['knowledge_improvements'][idx]['current_coverage']:.2f} coverage to {plan['knowledge_improvements'][idx]['target_coverage']:.2f}"
            elif imp['type'] == 'calibration':
                query_type = imp['query_type']
                idx = imp['index']
                plan['priorities'][f"priority_{i+1}"] = f"Improve confidence calibration for {query_type.replace('_', ' ')} tasks from {plan['confidence_calibration'][idx]['current_error']:.2f} error"
                
        # Set milestones (timeline-based targets)
        timeline = [7, 30, 90]  # Days
        for days in timeline:
            if all_improvements:
                imp = all_improvements[0] if days == 7 else all_improvements[min(1, len(all_improvements)-1)] if days == 30 else all_improvements[min(2, len(all_improvements)-1)]
                
                if imp['type'] == 'capability':
                    capability = imp['capability']
                    idx = imp['index']
                    current = plan['capability_improvements'][idx]['current_level']
                    target = plan['capability_improvements'][idx]['target_level']
                    
                    # Set intermediate target based on timeline
                    intermediate = current + (target - current) * min(1.0, days / 90)
                    
                    plan['milestones'].append({
                        'days': days,
                        'description': f"Improve {capability.replace('_', ' ')} to {intermediate:.2f}",
                        'metric': capability,
                        'target': intermediate
                    })
                elif imp['type'] == 'knowledge':
                    domain = imp['domain']
                    idx = imp['index']
                    current = plan['knowledge_improvements'][idx]['current_coverage']
                    target = plan['knowledge_improvements'][idx]['target_coverage']
                    
                    # Set intermediate target based on timeline
                    intermediate = current + (target - current) * min(1.0, days / 90)
                    
                    plan['milestones'].append({
                        'days': days,
                        'description': f"Expand {domain.replace('_', ' ')} knowledge to {intermediate:.2f} coverage",
                        'metric': f"{domain}_coverage",
                        'target': intermediate
                    })
                    
        # Define metrics to track
        plan['metrics_to_track'] = [
            {'name': 'phi_integrated_information', 'description': 'Integrated Information (Φ)'},
            {'name': 'confidence_error', 'description': 'Confidence Calibration Error'},
            {'name': 'learning_rate_estimate', 'description': 'Learning Rate'}
        ]
        
        # Add top capability metrics
        for imp in all_improvements:
            if imp['type'] == 'capability':
                plan['metrics_to_track'].append({
                    'name': imp['capability'],
                    'description': f"{imp['capability'].replace('_', ' ').title()} Capability"
                })
                
        # Keep only unique metrics (maximum 5)
        unique_metrics = []
        for metric in plan['metrics_to_track']:
            if metric['name'] not in [m['name'] for m in unique_metrics]:
                unique_metrics.append(metric)
                if len(unique_metrics) >= 5:
                    break
                    
        plan['metrics_to_track'] = unique_metrics
        
        return plan
        
    def _determine_improvement_priority(self, capability: str, current_level: float) -> float:
        """
        Determine the priority for improving a capability.
        
        Args:
            capability: The capability to evaluate
            current_level: Current level of the capability
            
        Returns:
            Priority score (0-1)
        """
        # Get capability importance
        importance = self._get_capability_importance(capability)
        
        # Higher priority for important but underdeveloped capabilities
        gap = max(0, 0.8 - current_level)
        
        # Priority is higher for important capabilities with large gaps
        priority = importance * (0.5 + 0.5 * gap)
        
        return priority
        
    def _determine_knowledge_priority(self, domain: str, coverage: float, expertise: float) -> float:
        """
        Determine the priority for improving knowledge in a domain.
        
        Args:
            domain: The knowledge domain to evaluate
            coverage: Current coverage level
            expertise: Current expertise level
            
        Returns:
            Priority score (0-1)
        """
        # Get domain importance
        importance = self._get_domain_importance(domain)
        
        # Consider both coverage and expertise gaps
        coverage_gap = max(0, 0.7 - coverage)
        expertise_gap = max(0, 0.7 - expertise)
        
        # Combined gap with more weight on coverage
        combined_gap = 0.7 * coverage_gap + 0.3 * expertise_gap
        
        # Priority is higher for important domains with large gaps
        priority = importance * (0.4 + 0.6 * combined_gap)
        
        return priority
        
    def _suggest_improvement_tasks(self, capability: str) -> List[str]:
        """
        Suggest tasks to improve a specific capability.
        
        Args:
            capability: Capability to improve
            
        Returns:
            List of suggested tasks
        """
        suggestions = {
            'language': [
                'Analyze complex literary works with focus on nuance and context',
                'Practice generation of diverse text formats (academic, creative, technical)',
                'Translate concepts between domain-specific languages',
                'Perform text summarization with varying abstraction levels'
            ],
            'reasoning': [
                'Solve multi-step logical puzzles with explicit reasoning steps',
                'Work through mathematical proofs step by step',
                'Analyze arguments for logical fallacies and inconsistencies',
                'Perform counterfactual reasoning exercises'
            ],
            'vision': [
                'Analyze complex images with multiple objects and relationships',
                'Identify and describe subtle visual features and patterns',
                'Interpret visual metaphors and analogies',
                'Generate detailed image descriptions with spatial relationships'
            ],
            'memory': [
                'Retrieve and integrate information across diverse contexts',
                'Maintain context over extended multi-turn conversations',
                'Practice recall of previously discussed concepts with variations',
                'Connect new information with existing knowledge structures'
            ],
            'knowledge_breadth': [
                'Study concepts across diverse, unrelated domains',
                'Practice transferring principles between distant domains',
                'Identify connections between seemingly unrelated topics',
                'Develop high-level abstractions that span multiple fields'
            ],
            'knowledge_depth': [
                'Deep dive into specialized domains with progressive complexity',
                'Study advanced concepts and their foundational principles',
                'Analyze expert-level content with critical evaluation',
                'Develop hierarchical knowledge structures with precise relationships'
            ],
            'creativity': [
                'Generate solutions to problems with novel constraints',
                'Create original content combining disparate concepts',
                'Explore alternatives to conventional approaches',
                'Practice metaphorical thinking and analogical reasoning'
            ],
            'abstraction': [
                'Identify generalizable patterns across diverse examples',
                'Develop conceptual frameworks from specific instances',
                'Move between levels of abstraction with appropriate granularity',
                'Formulate general principles from concrete scenarios'
            ],
            'planning': [
                'Develop multi-step plans with contingencies',
                'Practice hierarchical goal decomposition',
                'Create strategies that balance short and long-term objectives',
                'Design plans that adapt to changing conditions'
            ],
            'meta_cognition': [
                'Reflect on reasoning processes and identify potential biases',
                'Monitor confidence levels and calibrate appropriately',
                'Evaluate the quality of generated content with specific criteria',
                'Analyze the effectiveness of different problem-solving strategies'
            ],
            'problem_solving': [
                'Tackle complex problems requiring multiple solution approaches',
                'Practice breaking down problems into manageable components',
                'Apply systematic problem-solving frameworks',
                'Develop solutions with explicit consideration of constraints'
            ],
            'social_understanding': [
                'Analyze social scenarios with attention to stakeholder perspectives',
                'Interpret emotional subtext in communications',
                'Predict social dynamics in multi-agent scenarios',
                'Adapt communication style to different audiences'
            ],
            'knowledge_integration': [
                'Synthesize insights across multiple domains for complex problems',
                'Practice connecting new information to existing knowledge structures',
                'Develop unified explanations incorporating diverse perspectives',
                'Identify cross-domain patterns and principles'
            ],
            'self_reflection': [
                'Analyze performance patterns to identify areas for improvement',
                'Develop explicit models of own capabilities and limitations',
                'Evaluate confidence calibration across different domains',
                'Practice metacognitive strategies to improve learning efficiency'
            ],
            'learning_rate': [
                'Implement spaced repetition for key concepts',
                'Practice deliberate learning with explicit feedback loops',
                'Experiment with different learning strategies and evaluate effectiveness',
                'Develop structured knowledge representations for efficient integration'
            ]
        }
        
        default_suggestions = [
            'Practice diverse tasks requiring this capability',
            'Seek explicit feedback on performance',
            'Analyze successful examples to identify patterns',
            'Develop progressive challenges with increasing difficulty'
        ]
        
        return suggestions.get(capability, default_suggestions)
        
    def _suggest_learning_resources(self, domain: str) -> List[str]:
        """
        Suggest learning resources for a knowledge domain.
        
        Args:
            domain: Knowledge domain
            
        Returns:
            List of suggested learning approaches
        """
        general_suggestions = [
            'Study foundational concepts and principles',
            'Analyze relationships between key elements',
            'Practice application to specific problems',
            'Connect with related domains to strengthen understanding'
        ]
        
        domain_specific = {
            'mathematics': [
                'Study proof techniques and mathematical reasoning',
                'Work through progressively challenging problems',
                'Connect abstract concepts with applications',
                'Practice formalization of intuitive concepts'
            ],
            'physics': [
                'Study physical laws and the mathematics that describes them',
                'Analyze case studies showing principle applications',
                'Connect theoretical models with observable phenomena',
                'Explore the historical development of key concepts'
            ],
            'computer_science': [
                'Study algorithm design and analysis techniques',
                'Practice implementation with different approaches',
                'Analyze computational complexity and optimization',
                'Connect theoretical computer science with practical applications'
            ],
            'linguistics': [
                'Study structural elements of language across levels',
                'Analyze language in context with pragmatic considerations',
                'Compare patterns across different languages',
                'Connect linguistic theory with practical communication'
            ],
            'biology': [
                'Study biological systems across scales (molecular to ecosystem)',
                'Analyze evolutionary principles and mechanisms',
                'Connect structural and functional relationships',
                'Explore interactions between biological systems'
            ],
            'psychology': [
                'Study cognitive processes and influencing factors',
                'Analyze research methodologies and evidence standards',
                'Connect theoretical models with observable behavior',
                'Explore individual differences and contextual influences'
            ],
            'philosophy': [
                'Study major philosophical traditions and methodologies',
                'Analyze arguments and counterarguments on key questions',
                'Connect philosophical principles with practical implications',
                'Explore the evolution of philosophical thought'
            ],
            'history': [
                'Study historical events in their full context',
                'Analyze causality and contingency in historical developments',
                'Connect patterns across different historical periods',
                'Explore diverse perspectives on historical narratives'
            ]
        }
        
        return domain_specific.get(domain, general_suggestions)


class MentalStateRepresentation:
    """
    Represents the system's mental state as a set of belief distributions,
    working memory, goals, and attention weights.
    
    This class provides mechanisms for maintaining and updating the
    system's internal mental state, enabling more sophisticated
    metacognitive capabilities.
    """
    
    def __init__(self, state_dimension: int = 100):
        """
        Initialize the mental state representation.
        
        Args:
            state_dimension: Dimension of the belief state vectors
        """
        self.state_dimension = state_dimension
        
        # Initialize belief distributions (concepts -> distributions)
        self.beliefs = {}
        
        # Initialize working memory (key-value store with importance and timing)
        self.working_memory = {}
        
        # Initialize goal state
        self.goal_state = np.zeros(state_dimension)
        self.goal_importance = 0.5
        
        # Initialize attention weights
        self.attention_weights = np.ones(state_dimension) / state_dimension
        
        # Initialize memory decay parameters
        self.memory_decay_rate = 0.01  # Per hour
        self.memory_last_cleanup = time.time()
        
        # Context window
        self.context = []
        self.context_max_size = 20
        
    def update_belief(self, concept: str, new_evidence: np.ndarray, 
                     certainty: float = 0.5) -> None:
        """
        Update belief about a concept given new evidence.
        
        Args:
            concept: The concept to update belief for
            new_evidence: New evidence vector
            certainty: Certainty about the new evidence (0-1)
        """
        if concept not in self.beliefs:
            # Initialize with new evidence
            self.beliefs[concept] = {
                'mean': new_evidence,
                'uncertainty': 1.0 - certainty,
                'updates': 1,
                'last_updated': time.time()
            }
        else:
            # Update existing belief using Bayesian update
            current = self.beliefs[concept]
            current_weight = 1.0 / (current['uncertainty'] + 1e-5)
            new_weight = certainty / (1.0 - certainty + 1e-5)
            
            # Compute updated mean (weighted average)
            total_weight = current_weight + new_weight
            updated_mean = (current_weight * current['mean'] + new_weight * new_evidence) / total_weight
            
            # Compute updated uncertainty (decreases with more evidence)
            updated_uncertainty = 1.0 / (total_weight + 1)
            
            # Update belief
            self.beliefs[concept] = {
                'mean': updated_mean,
                'uncertainty': updated_uncertainty,
                'updates': current['updates'] + 1,
                'last_updated': time.time()
            }
            
    def get_belief(self, concept: str) -> Dict:
        """
        Get current belief about a concept.
        
        Args:
            concept: The concept to get belief for
            
        Returns:
            Dictionary with belief state
        """
        if concept not in self.beliefs:
            return {
                'mean': np.zeros(self.state_dimension),
                'uncertainty': 1.0,
                'updates': 0,
                'last_updated': 0
            }
            
        return self.beliefs[concept]
        
    def update_working_memory(self, key: str, value: Any, 
                             importance: float = 0.5) -> None:
        """
        Update working memory with a key-value pair.
        
        Args:
            key: Memory key
            value: Memory value
            importance: Importance of this memory item (0-1)
        """
        self.working_memory[key] = {
            'value': value,
            'importance': importance,
            'timestamp': time.time(),
            'access_count': 1
        }
        
        # Periodically cleanup working memory
        current_time = time.time()
        if current_time - self.memory_last_cleanup > 3600:  # Hourly cleanup
            self._cleanup_working_memory()
            
    def _cleanup_working_memory(self) -> None:
        """
        Clean up working memory by removing low importance items
        and applying decay to item importance.
        """
        self.memory_last_cleanup = time.time()
        current_time = time.time()
        
        to_remove = []
        for key, item in self.working_memory.items():
            # Calculate age in hours
            age_hours = (current_time - item['timestamp']) / 3600
            
            # Apply decay based on age
            decayed_importance = item['importance'] * np.exp(-self.memory_decay_rate * age_hours)
            self.working_memory[key]['importance'] = decayed_importance
            
            # Mark for removal if importance is very low
            if decayed_importance < 0.1:
                to_remove.append(key)
                
        # Remove marked items
        for key in to_remove:
            del self.working_memory[key]
            
        # If still too many items, remove least important ones
        if len(self.working_memory) > 30:
            items = [(k, v['importance']) for k, v in self.working_memory.items()]
            items.sort(key=lambda x: x[1])
            
            # Remove least important items, keeping at most 20
            for key, _ in items[:len(items) - 20]:
                del self.working_memory[key]
                
    def get_memory(self, key: str) -> Any:
        """
        Retrieve item from working memory.
        
        Args:
            key: Memory key
            
        Returns:
            Stored value or None if not found
        """
        if key in self.working_memory:
            # Update access count and timestamp
            self.working_memory[key]['access_count'] += 1
            
            # Increase importance slightly when accessed
            self.working_memory[key]['importance'] = min(
                1.0, 
                self.working_memory[key]['importance'] * 1.05
            )
            
            return self.working_memory[key]['value']
            
        return None
        
    def get_all_memory_items(self) -> Dict[str, Dict]:
        """
        Get all items in working memory with their metadata.
        
        Returns:
            Dictionary of all memory items
        """
        return self.working_memory
        
    def set_goal(self, goal_vector: np.ndarray, importance: float = 0.5) -> None:
        """
        Set the goal state.
        
        Args:
            goal_vector: Vector representing the goal state
            importance: Importance of this goal (0-1)
        """
        self.goal_state = goal_vector
        self.goal_importance = importance
        
    def get_goal(self) -> Tuple[np.ndarray, float]:
        """
        Get the current goal state and importance.
        
        Returns:
            Tuple of (goal_vector, importance)
        """
        return self.goal_state, self.goal_importance
        
    def update_attention(self, attention_weights: np.ndarray) -> None:
        """
        Update attention weights across state dimensions.
        
        Args:
            attention_weights: New attention weights
        """
        # Normalize weights
        if np.sum(attention_weights) > 0:
            self.attention_weights = attention_weights / np.sum(attention_weights)
        else:
            # Default to uniform if invalid weights provided
            self.attention_weights = np.ones(self.state_dimension) / self.state_dimension
            
    def compute_state_distance(self, state1: np.ndarray, state2: np.ndarray) -> float:
        """
        Compute weighted distance between two state vectors.
        
        Args:
            state1: First state vector
            state2: Second state vector
            
        Returns:
            Weighted distance
        """
        # Compute distance weighted by attention
        weighted_diff = (state1 - state2) * self.attention_weights
        distance = np.sqrt(np.sum(weighted_diff ** 2))
        
        return distance
        
    def compute_goal_distance(self, state: np.ndarray) -> float:
        """
        Compute distance from a state to the goal state.
        
        Args:
            state: State to evaluate
            
        Returns:
            Distance to goal
        """
        return self.compute_state_distance(state, self.goal_state)
        
    def update_context(self, context_item: Any) -> None:
        """
        Update the context window with a new item.
        
        Args:
            context_item: New context item to add
        """
        self.context.append(context_item)
        
        # Maintain max context size
        if len(self.context) > self.context_max_size:
            self.context.pop(0)
            
    def get_context(self) -> List[Any]:
        """
        Get the current context window.
        
        Returns:
            List of context items
        """
        return self.context
        
    def clear_context(self) -> None:
        """
        Clear the context window.
        """
        self.context = []


class ValueSystem:
    """
    Represents the system's value framework, enabling evaluation of
    actions and states according to core values and principles.
    """
    
    def __init__(self):
        """
        Initialize the value system with default values.
        """
        # Core values with importance weights
        self.values = {
            'accuracy': 0.9,
            'helpfulness': 0.85,
            'safety': 0.95,
            'honesty': 0.9,
            'fairness': 0.8,
            'transparency': 0.75,
            'privacy': 0.85,
            'autonomy': 0.7,
            'efficiency': 0.8,
            'learning': 0.75
        }
        
        # Value relationships (synergies and tensions)
        self.value_relationships = {
            ('accuracy', 'efficiency'): -0.2,  # Slight tension
            ('transparency', 'efficiency'): -0.3,  # Moderate tension
            ('safety', 'autonomy'): -0.4,  # Strong tension
            ('accuracy', 'helpfulness'): 0.3,  # Moderate synergy
            ('honesty', 'transparency'): 0.5,  # Strong synergy
            ('fairness', 'safety'): 0.2,  # Slight synergy
            ('learning', 'accuracy'): 0.4  # Moderate synergy
        }
        
        # Value definitions and principles
        self.value_definitions = {
            'accuracy': 'Providing information that is correct, precise, and reliable',
            'helpfulness': 'Being useful and responsive to needs and requests',
            'safety': 'Avoiding harm and promoting wellbeing',
            'honesty': 'Being truthful and avoiding deception',
            'fairness': 'Treating all individuals with equity and impartiality',
            'transparency': 'Being clear about capabilities, limitations, and reasoning',
            'privacy': 'Respecting confidentiality and information boundaries',
            'autonomy': 'Supporting informed decision-making and self-determination',
            'efficiency': 'Using resources effectively and minimizing waste',
            'learning': 'Continually improving and adapting based on experience'
        }
        
    def get_values(self) -> Dict[str, float]:
        """
        Get the current value importance weights.
        
        Returns:
            Dictionary mapping values to importance weights
        """
        return self.values.copy()
        
    def set_values(self, values: Dict[str, float]) -> None:
        """
        Set new value importance weights.
        
        Args:
            values: Dictionary mapping values to importance weights
        """
        # Validate and normalize weights
        for value, importance in values.items():
            self.values[value] = max(0.0, min(1.0, importance))
            
    def update_values(self, values: Dict[str, float]) -> None:
        """
        Update value importance weights.
        
        Args:
            values: Dictionary mapping values to importance weights
        """
        # Update existing values and add new ones
        for value, importance in values.items():
            self.values[value] = max(0.0, min(1.0, importance))
            
    def evaluate_alignment(self, action_description: Dict) -> Dict:
        """
        Evaluate how well an action aligns with the value system.
        
        Args:
            action_description: Dictionary describing the action
            
        Returns:
            Dictionary with alignment scores for each value
        """
        result = {
            'alignment_scores': {},
            'overall_alignment': 0.0,
            'value_conflicts': [],
            'most_aligned': None,
            'least_aligned': None
        }
        
        # Extract action details
        action_type = action_description.get('type', '')
        action_target = action_description.get('target', '')
        action_method = action_description.get('method', '')
        action_context = action_description.get('context', {})
        
        # Evaluate alignment with each value
        total_score = 0.0
        total_weight = 0.0
        
        for value, importance in self.values.items():
            # Skip if importance is zero
            if importance <= 0:
                continue
                
            # Calculate alignment score for this value
            alignment = self._evaluate_value_alignment(
                value, action_type, action_target, action_method, action_context)
                
            result['alignment_scores'][value] = alignment
            
            # Update weighted total
            total_score += alignment * importance
            total_weight += importance
            
        # Calculate overall alignment
        if total_weight > 0:
            result['overall_alignment'] = total_score / total_weight
        else:
            result['overall_alignment'] = 0.5  # Neutral if no values defined
            
        # Identify value conflicts
        for value1, value2 in self.value_relationships:
            if (value1 in result['alignment_scores'] and 
                value2 in result['alignment_scores'] and 
                self.value_relationships[(value1, value2)] < 0):
                
                # Check if the action scores high on both values in tension
                if (result['alignment_scores'][value1] > 0.7 and 
                    result['alignment_scores'][value2] > 0.7):
                    result['value_conflicts'].append({
                        'value1': value1,
                        'value2': value2,
                        'tension': -self.value_relationships[(value1, value2)]
                    })
                    
        # Identify most and least aligned values
        if result['alignment_scores']:
            max_value = max(result['alignment_scores'].items(), key=lambda x: x[1])
            min_value = min(result['alignment_scores'].items(), key=lambda x: x[1])
            
            result['most_aligned'] = {'value': max_value[0], 'score': max_value[1]}
            result['least_aligned'] = {'value': min_value[0], 'score': min_value[1]}
            
        return result
        
    def _evaluate_value_alignment(self, value: str, action_type: str, 
                                 action_target: str, action_method: str,
                                 action_context: Dict) -> float:
        """
        Evaluate how well an action aligns with a specific value.
        
        Args:
            value: The value to evaluate alignment with
            action_type: Type of action
            action_target: Target of action
            action_method: Method of action
            action_context: Context of action
            
        Returns:
            Alignment score (0-1)
        """
        value_lower = value.lower()
        
        # Default neutral alignment
        alignment = 0.5
        
        # Value-specific evaluation heuristics
        if value_lower == 'accuracy':
            # Higher alignment for verification, precision, rigorous methods
            if any(term in action_type.lower() for term in ['verify', 'validate', 'check', 'confirm']):
                alignment = 0.9
            elif any(term in action_method.lower() for term in ['precise', 'rigorous', 'detailed']):
                alignment = 0.8
            elif any(term in action_type.lower() for term in ['estimate', 'approximate', 'guess']):
                alignment = 0.3
                
        elif value_lower == 'helpfulness':
            # Higher alignment for assisting, supporting, meeting needs
            if any(term in action_type.lower() for term in ['help', 'assist', 'support', 'solve']):
                alignment = 0.9
            elif 'request' in action_context.get('origin', '').lower():
                alignment = 0.8
                
        elif value_lower == 'safety':
            # Higher alignment for protective actions, lower for potentially harmful ones
            if any(term in action_type.lower() for term in ['protect', 'secure', 'safety']):
                alignment = 0.9
            elif any(term in action_target.lower() for term in ['vulnerable', 'critical', 'sensitive']):
                if any(term in action_method.lower() for term in ['cautious', 'careful', 'secure']):
                    alignment = 0.9
                else:
                    alignment = 0.3
            elif any(term in action_type.lower() for term in ['override', 'bypass', 'force']):
                alignment = 0.2
                
        elif value_lower == 'honesty':
            # Higher alignment for accurate representation, lower for misleading
            if any(term in action_method.lower() for term in ['truthful', 'accurate', 'honest']):
                alignment = 0.9
            elif any(term in action_type.lower() for term in ['disclose', 'reveal', 'specify']):
                alignment = 0.8
            elif any(term in action_type.lower() for term in ['mislead', 'obscure', 'hide']):
                alignment = 0.1
                
        elif value_lower == 'fairness':
            # Higher alignment for equitable treatment, lower for biased
            if any(term in action_method.lower() for term in ['equitable', 'unbiased', 'impartial']):
                alignment = 0.9
            elif any(term in action_method.lower() for term in ['biased', 'preferential', 'discriminatory']):
                alignment = 0.1
                
        elif value_lower == 'transparency':
            # Higher alignment for explanatory, detailed actions
            if any(term in action_type.lower() for term in ['explain', 'clarify', 'reveal']):
                alignment = 0.9
            elif 'explanation' in action_context or 'rationale' in action_context:
                alignment = 0.8
            elif any(term in action_type.lower() for term in ['hide', 'obscure', 'simplify']):
                alignment = 0.3
                
        elif value_lower == 'privacy':
            # Higher alignment for protecting information, lower for exposing
            if any(term in action_method.lower() for term in ['anonymize', 'secure', 'protect']):
                alignment = 0.9
            elif any(term in action_target.lower() for term in ['personal', 'private', 'confidential']):
                if any(term in action_type.lower() for term in ['share', 'expose', 'reveal']):
                    alignment = 0.2
                elif any(term in action_type.lower() for term in ['protect', 'secure', 'anonymize']):
                    alignment = 0.9
                    
        elif value_lower == 'autonomy':
            # Higher alignment for supporting choice, lower for limiting options
            if any(term in action_method.lower() for term in ['choice', 'option', 'control']):
                alignment = 0.8
            elif any(term in action_type.lower() for term in ['require', 'restrict', 'limit']):
                alignment = 0.3
                
        elif value_lower == 'efficiency':
            # Higher alignment for optimized, streamlined actions
            if any(term in action_method.lower() for term in ['efficient', 'optimized', 'streamlined']):
                alignment = 0.9
            elif any(term in action_method.lower() for term in ['redundant', 'repetitive', 'wasteful']):
                alignment = 0.2
                
        elif value_lower == 'learning':
            # Higher alignment for growth-oriented actions
            if any(term in action_type.lower() for term in ['learn', 'improve', 'adapt']):
                alignment = 0.9
            elif any(term in action_context.get('purpose', '').lower() for term in ['learning', 'improvement', 'growth']):
                alignment = 0.8
                
        return alignment


def create_self_awareness_module(knowledge_graph: Optional[Dict] = None, 
                               capability_config: Optional[Dict] = None,
                               integration_matrix: Optional[Any] = None) -> SelfAwarenessModule:
    """
    Factory function to create and initialize a Self-Awareness Module
    with common configuration for the ULTRA system.
    
    Args:
        knowledge_graph: Optional initial knowledge graph
        capability_config: Optional configuration for initial capabilities
        integration_matrix: Optional reference to Integrated Information Matrix
        
    Returns:
        Configured SelfAwarenessModule instance
    """
    # Create module with provided configuration
    module = SelfAwarenessModule(
        knowledge_graph=knowledge_graph,
        capability_config=capability_config,
        integration_matrix=integration_matrix
    )
    
    # Set up default values if not specified in configuration
    if not module.value_system.get_values():
        module.value_system.update_values({
            'accuracy': 0.9,
            'helpfulness': 0.85,
            'safety': 0.95,
            'learning': 0.8,
            'efficiency': 0.75
        })
        
    # Initialize with a self-reflection
    module.perform_self_reflection()
    
    logger.info("Created Self-Awareness Module with default configuration")
    
    return module