#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ULTRA Integration Module

This module contains all the integration bridges that connect different
ULTRA subsystems, creating a unified AGI architecture.

Integration phases:
1. Neuromorphic-Transformer Bridge
2. Diffusion-Neuromorphic Bridge  
3. Meta-Cognitive Bridge
4. Consciousness Lattice Bridge
5. Neuro-Symbolic Bridge
6. Self-Evolution Bridge
"""

try:
    from .neuromorphic_transformer_bridge import NeuromorphicTransformerBridge
except ImportError as e:
    print(f"Warning: Could not import NeuromorphicTransformerBridge: {e}")
    NeuromorphicTransformerBridge = None

try:
    from .diffusion_neuromorphic_bridge import DiffusionNeuromorphicBridge
except ImportError as e:
    print(f"Warning: Could not import DiffusionNeuromorphicBridge: {e}")
    DiffusionNeuromorphicBridge = None

try:
    from .meta_cognitive_bridge import MetaCognitiveController
except ImportError as e:
    print(f"Warning: Could not import MetaCognitiveController: {e}")
    MetaCognitiveController = None

try:
    from .consciousness_lattice_bridge import ConsciousnessLatticeIntegrator
except ImportError as e:
    print(f"Warning: Could not import ConsciousnessLatticeIntegrator: {e}")
    ConsciousnessLatticeIntegrator = None

try:
    from .neuro_symbolic_bridge import NeuroSymbolicIntegrationBridge
except ImportError as e:
    print(f"Warning: Could not import NeuroSymbolicIntegrationBridge: {e}")
    NeuroSymbolicIntegrationBridge = None

try:
    from .self_evolution_bridge import ULTRAMasterController
except ImportError as e:
    print(f"Warning: Could not import ULTRAMasterController: {e}")
    ULTRAMasterController = None

# Additional integration bridges
try:
    from .hyper_transformer_bridge import HyperTransformerBridge
except ImportError as e:
    print(f"Warning: Could not import HyperTransformerBridge: {e}")
    HyperTransformerBridge = None

try:
    from .knowledge_management_bridge import KnowledgeManagementBridge
except ImportError as e:
    print(f"Warning: Could not import KnowledgeManagementBridge: {e}")
    KnowledgeManagementBridge = None

try:
    from .autonomous_learning_bridge import AutonomousLearningBridge
except ImportError as e:
    print(f"Warning: Could not import AutonomousLearningBridge: {e}")
    AutonomousLearningBridge = None

try:
    from .input_processing_bridge import InputProcessingBridge
except ImportError as e:
    print(f"Warning: Could not import InputProcessingBridge: {e}")
    InputProcessingBridge = None

try:
    from .output_generation_bridge import OutputGenerationBridge
except ImportError as e:
    print(f"Warning: Could not import OutputGenerationBridge: {e}")
    OutputGenerationBridge = None

try:
    from .safety_monitoring_bridge import SafetyMonitoringBridge
except ImportError as e:
    print(f"Warning: Could not import SafetyMonitoringBridge: {e}")
    SafetyMonitoringBridge = None

__all__ = [
    'NeuromorphicTransformerBridge',
    'DiffusionNeuromorphicBridge',
    'MetaCognitiveController',
    'ConsciousnessLatticeIntegrator',
    'NeuroSymbolicIntegrationBridge',
    'ULTRAMasterController',
    'HyperTransformerBridge',
    'KnowledgeManagementBridge',
    'AutonomousLearningBridge',
    'InputProcessingBridge',
    'OutputGenerationBridge',
    'SafetyMonitoringBridge'
]

# Version info
__version__ = '1.0.0'
