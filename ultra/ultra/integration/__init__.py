#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ULTRA Integration Module

This module contains all the integration bridges that connect different
ULTRA subsystems, creating a unified AGI architecture.

Integration phases:
1. Neuromorphic-Transformer Bridge
2. Diffusion-Neuromorphic Bridge  
3. Meta-Cognitive Bridge
4. Consciousness Lattice Bridge
5. Neuro-Symbolic Bridge
6. Self-Evolution Bridge
"""

try:
    from ultra.neuromorphic_transformer_bridge import NeuromorphicTransformerBridge
except ImportError as e:
    print(f"Warning: Could not import NeuromorphicTransformerBridge: {e}")
    NeuromorphicTransformerBridge = None

try:
    from ultra.diffusion_neuromorphic_bridge import DiffusionNeuromorphicBridge
except ImportError as e:
    print(f"Warning: Could not import DiffusionNeuromorphicBridge: {e}")
    DiffusionNeuromorphicBridge = None

try:
    from ultra.meta_cognitive_bridge import MetaCognitiveBridge
except ImportError as e:
    print(f"Warning: Could not import MetaCognitiveBridge: {e}")
    MetaCognitiveBridge = None

try:
    from ultra.consciousness_lattice_bridge import ConsciousnessLatticeBridge
except ImportError as e:
    print(f"Warning: Could not import ConsciousnessLatticeBridge: {e}")
    ConsciousnessLatticeBridge = None

try:
    from ultra.neuro_symbolic_bridge import NeuroSymbolicBridge
except ImportError as e:
    print(f"Warning: Could not import NeuroSymbolicBridge: {e}")
    NeuroSymbolicBridge = None

try:
    from ultra.self_evolution_bridge import SelfEvolutionBridge, ULTRAMasterController
except ImportError as e:
    print(f"Warning: Could not import SelfEvolutionBridge: {e}")
    SelfEvolutionBridge = None
    ULTRAMasterController = None

__all__ = [
    'NeuromorphicTransformerBridge',
    'DiffusionNeuromorphicBridge', 
    'MetaCognitiveBridge',
    'ConsciousnessLatticeBridge',
    'NeuroSymbolicBridge',
    'SelfEvolutionBridge',
    'ULTRAMasterController'
]

# Version info
__version__ = '1.0.0'
