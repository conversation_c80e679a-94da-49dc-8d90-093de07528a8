#!/usr/bin/env python3
"""
Neuromorphic-Transformer Bridge for ULTRA
==========================================

This module provides the integration layer between the neuromorphic core
and the hyper-dimensional transformer, enabling seamless information flow
and coordinated processing between biological and symbolic AI components.

Key Features:
- Real-time neuron state -> transformer embedding conversion
- Attention-guided neuromorphic stimulation
- Shared episodic memory between systems
- Dynamic resource allocation based on task complexity
"""

import torch
import torch.nn as nn
import numpy as np
import logging
from typing import Dict, List, Tuple, Optional, Any, Union

# Configure logging
logger = logging.getLogger(__name__)
from dataclasses import dataclass
from collections import defaultdict
import threading
import queue
import time

# ULTRA imports
from ultra.config import get_config
from ultra.core_neural.neuromorphic_core import NeuromorphicCore, NeuronType, OscillationType
from ultra.hyper_transformer import HyperDimensionalTransformer, HyperDimensionalTransformerConfig
from ultra.utils.ultra_logging import get_logger

logger = get_logger(__name__)

@dataclass
class BridgeConfig:
    """Configuration for neuromorphic-transformer bridge."""
    # Embedding parameters
    neuron_embedding_dim: int = 512
    max_neurons_per_batch: int = 1000
    temporal_window_ms: float = 100.0
    
    # Attention parameters
    attention_guided_stimulation: bool = True
    attention_feedback_strength: float = 0.1
    
    # Memory parameters
    episodic_memory_size: int = 10000
    memory_decay_rate: float = 0.95
    
    # Performance parameters
    update_frequency_hz: float = 100.0
    async_processing: bool = True


class NeuronStateEncoder(nn.Module):
    """Encodes neuromorphic state into transformer-compatible embeddings."""
    
    def __init__(self, config: BridgeConfig):
        super().__init__()
        self.config = config
        
        # State feature encoding
        self.voltage_encoder = nn.Linear(1, 64)
        self.activity_encoder = nn.Linear(1, 64)
        self.calcium_encoder = nn.Linear(1, 64)
        self.position_encoder = nn.Linear(3, 64)  # 3D position
        
        # Neuron type embedding
        self.type_embedding = nn.Embedding(10, 64)  # Support for various neuron types
        
        # Temporal encoding
        self.temporal_encoder = nn.Linear(1, 64)
        
        # Output projection
        self.output_projection = nn.Linear(64 * 6, config.neuron_embedding_dim)
        self.layer_norm = nn.LayerNorm(config.neuron_embedding_dim)
        
    def forward(self, neuron_states: Dict[str, torch.Tensor]) -> torch.Tensor:
        """
        Encode neuron states into embeddings.
        
        Args:
            neuron_states: Dictionary containing:
                - voltages: [batch, num_neurons]
                - activities: [batch, num_neurons] 
                - calcium_levels: [batch, num_neurons]
                - positions: [batch, num_neurons, 3]
                - types: [batch, num_neurons]
                - timestamps: [batch, num_neurons]
                
        Returns:
            embeddings: [batch, num_neurons, embedding_dim]
        """
        batch_size, num_neurons = neuron_states['voltages'].shape
        
        # Encode individual features
        v_emb = self.voltage_encoder(neuron_states['voltages'].unsqueeze(-1))
        a_emb = self.activity_encoder(neuron_states['activities'].unsqueeze(-1))
        c_emb = self.calcium_encoder(neuron_states['calcium_levels'].unsqueeze(-1))
        p_emb = self.position_encoder(neuron_states['positions'])
        t_emb = self.type_embedding(neuron_states['types'].long())
        time_emb = self.temporal_encoder(neuron_states['timestamps'].unsqueeze(-1))
        
        # Concatenate all features
        combined = torch.cat([v_emb, a_emb, c_emb, p_emb, t_emb, time_emb], dim=-1)
        
        # Project to final embedding dimension
        embeddings = self.output_projection(combined)
        embeddings = self.layer_norm(embeddings)
        
        return embeddings


class AttentionGuidedStimulator:
    """Uses transformer attention to guide neuromorphic stimulation."""
    
    def __init__(self, config: BridgeConfig):
        self.config = config
        self.attention_history = []
        
    def compute_stimulation_pattern(self, 
                                  attention_weights: torch.Tensor,
                                  neuron_ids: List[int]) -> Dict[int, float]:
        """
        Convert attention weights to neuromorphic stimulation.
        
        Args:
            attention_weights: [batch, heads, seq_len, seq_len]
            neuron_ids: List of neuron IDs corresponding to sequence positions
            
        Returns:
            stimulation_pattern: Dict mapping neuron_id to stimulation current
        """
        # Average attention across heads and batch
        avg_attention = attention_weights.mean(dim=(0, 1))  # [seq_len, seq_len]
        
        # Compute importance score for each neuron (sum of attention received)
        importance_scores = avg_attention.sum(dim=0)  # [seq_len]
        
        # Convert to stimulation currents
        stimulation_pattern = {}
        for i, neuron_id in enumerate(neuron_ids):
            if i < len(importance_scores):
                stimulation_current = float(importance_scores[i]) * self.config.attention_feedback_strength
                stimulation_pattern[neuron_id] = stimulation_current
                
        return stimulation_pattern


class EpisodicMemoryManager:
    """Manages shared episodic memory between neuromorphic and transformer systems."""
    
    def __init__(self, config: BridgeConfig):
        self.config = config
        self.memory_buffer = []
        self.memory_lock = threading.Lock()
        
    def store_episode(self, 
                     neuron_states: np.ndarray,
                     transformer_outputs: torch.Tensor,
                     attention_patterns: torch.Tensor,
                     timestamp: float,
                     context: Optional[Dict] = None):
        """Store an episode in shared memory."""
        with self.memory_lock:
            episode = {
                'timestamp': timestamp,
                'neuron_states': neuron_states.copy(),
                'transformer_outputs': transformer_outputs.detach().cpu().numpy(),
                'attention_patterns': attention_patterns.detach().cpu().numpy(),
                'context': context or {}
            }
            
            self.memory_buffer.append(episode)
            
            # Maintain buffer size
            if len(self.memory_buffer) > self.config.episodic_memory_size:
                self.memory_buffer.pop(0)
                
    def retrieve_relevant_episodes(self, 
                                  query_state: np.ndarray,
                                  k: int = 5) -> List[Dict]:
        """Retrieve k most relevant episodes based on state similarity."""
        with self.memory_lock:
            if not self.memory_buffer:
                return []
                
            # Compute similarities (simplified cosine similarity)
            similarities = []
            for episode in self.memory_buffer:
                state_sim = np.dot(query_state.flatten(), 
                                 episode['neuron_states'].flatten())
                similarities.append((state_sim, episode))
                
            # Sort by similarity and return top k
            similarities.sort(key=lambda x: x[0], reverse=True)
            return [episode for _, episode in similarities[:k]]


class NeuromorphicTransformerBridge:
    """Main bridge class coordinating neuromorphic and transformer systems."""
    
    def __init__(self, 
                 neuromorphic_core: NeuromorphicCore,
                 transformer: HyperDimensionalTransformer,
                 config: Optional[BridgeConfig] = None):
        self.neuromorphic_core = neuromorphic_core
        self.transformer = transformer
        self.config = config or BridgeConfig()
        
        # Initialize components
        self.state_encoder = NeuronStateEncoder(self.config)
        self.stimulator = AttentionGuidedStimulator(self.config)
        self.memory_manager = EpisodicMemoryManager(self.config)
        
        # Runtime state
        self.is_running = False
        self.processing_thread = None
        self.state_queue = queue.Queue(maxsize=100)
        
        # Performance monitoring
        self.performance_stats = {
            'bridge_latency_ms': [],
            'encoding_time_ms': [],
            'attention_time_ms': [],
            'stimulation_time_ms': []
        }
        
    def start_async_processing(self):
        """Start asynchronous processing loop."""
        if self.config.async_processing and not self.is_running:
            self.is_running = True
            self.processing_thread = threading.Thread(target=self._processing_loop)
            self.processing_thread.start()
            logger.info("Neuromorphic-Transformer bridge started in async mode")
            
    def stop_async_processing(self):
        """Stop asynchronous processing."""
        if self.is_running:
            self.is_running = False
            if self.processing_thread:
                self.processing_thread.join()
            logger.info("Neuromorphic-Transformer bridge stopped")
            
    def _processing_loop(self):
        """Main asynchronous processing loop."""
        dt = 1.0 / self.config.update_frequency_hz
        
        while self.is_running:
            start_time = time.time()
            
            try:
                # Get current neuromorphic state
                neuron_states = self._extract_neuron_states()
                
                # Process through bridge
                self._process_state_sync(neuron_states)
                
                # Sleep to maintain frequency
                elapsed = time.time() - start_time
                if elapsed < dt:
                    time.sleep(dt - elapsed)
                    
            except Exception as e:
                logger.error(f"Error in bridge processing loop: {e}")
                
    def _extract_neuron_states(self) -> Dict[str, Any]:
        """Extract current states from neuromorphic core."""
        neuron_states = {
            'voltages': [],
            'activities': [],
            'calcium_levels': [],
            'positions': [],
            'types': [],
            'timestamps': [],
            'neuron_ids': []
        }
        
        current_time = self.neuromorphic_core.current_time
        
        for neuron_id, neuron in self.neuromorphic_core.neurons.items():
            neuron_states['voltages'].append(neuron.v)
            
            # Activity level (spike rate over recent window)
            recent_spikes = [t for t in neuron.spike_times 
                           if current_time - t < self.config.temporal_window_ms]
            activity = len(recent_spikes) / (self.config.temporal_window_ms / 1000.0)
            neuron_states['activities'].append(activity)
            
            # Enhanced calcium dynamics if available
            if hasattr(neuron, 'calcium_concentration'):
                neuron_states['calcium_levels'].append(neuron.calcium_concentration)
            else:
                neuron_states['calcium_levels'].append(0.1)  # Default value
                
            # 3D position
            if neuron_id in self.neuromorphic_core.neuron_positions:
                pos = self.neuromorphic_core.neuron_positions[neuron_id]
                neuron_states['positions'].append(list(pos))
            else:
                neuron_states['positions'].append([0.0, 0.0, 0.0])
                
            # Neuron type
            neuron_type = self.neuromorphic_core.neuron_types.get(neuron_id, NeuronType.EXCITATORY)
            neuron_states['types'].append(neuron_type.value)
            
            # Timestamp
            neuron_states['timestamps'].append(current_time)
            neuron_states['neuron_ids'].append(neuron_id)
            
        # Convert to tensors
        for key in ['voltages', 'activities', 'calcium_levels', 'types', 'timestamps']:
            neuron_states[key] = torch.tensor(neuron_states[key], dtype=torch.float32).unsqueeze(0)
            
        neuron_states['positions'] = torch.tensor(neuron_states['positions'], dtype=torch.float32).unsqueeze(0)
        
        return neuron_states
        
    def _process_state_sync(self, neuron_states: Dict[str, Any]):
        """Process states synchronously through the bridge."""
        start_time = time.time()
        
        # 1. Encode neuron states for transformer
        encoding_start = time.time()
        neuron_embeddings = self.state_encoder(neuron_states)
        encoding_time = (time.time() - encoding_start) * 1000
        
        # 2. Process through transformer
        attention_start = time.time()
        transformer_output, attention_weights = self.transformer(
            input_ids=None,  # We're providing embeddings directly
            embeddings=neuron_embeddings,
            return_attention_weights=True
        )
        attention_time = (time.time() - attention_start) * 1000
        
        # 3. Generate stimulation pattern from attention
        stimulation_start = time.time()
        if self.config.attention_guided_stimulation:
            stimulation_pattern = self.stimulator.compute_stimulation_pattern(
                attention_weights, neuron_states['neuron_ids']
            )
            # Apply stimulation to neuromorphic core
            for neuron_id, current in stimulation_pattern.items():
                if neuron_id in self.neuromorphic_core.external_currents:
                    self.neuromorphic_core.external_currents[neuron_id] += current
                else:
                    self.neuromorphic_core.external_currents[neuron_id] = current
                    
        stimulation_time = (time.time() - stimulation_start) * 1000
        
        # 4. Store episode in memory
        self.memory_manager.store_episode(
            neuron_states['voltages'].numpy(),
            transformer_output,
            attention_weights,
            self.neuromorphic_core.current_time
        )
        
        # Update performance stats
        total_time = (time.time() - start_time) * 1000
        self.performance_stats['bridge_latency_ms'].append(total_time)
        self.performance_stats['encoding_time_ms'].append(encoding_time)
        self.performance_stats['attention_time_ms'].append(attention_time)
        self.performance_stats['stimulation_time_ms'].append(stimulation_time)
        
        # Keep only recent stats
        for key in self.performance_stats:
            if len(self.performance_stats[key]) > 1000:
                self.performance_stats[key] = self.performance_stats[key][-1000:]
                
    def process_joint_step(self, dt: float, external_input: Optional[Dict] = None) -> Dict[str, Any]:
        """Process one joint step of neuromorphic and transformer systems."""
        # Step neuromorphic core
        neuron_spikes = self.neuromorphic_core.step_accelerated(dt, external_input)
        
        # Extract and process states
        neuron_states = self._extract_neuron_states()
        self._process_state_sync(neuron_states)
        
        return {
            'neuron_spikes': neuron_spikes,
            'neuron_states': neuron_states,
            'bridge_performance': self.get_performance_summary()
        }
        
    def get_performance_summary(self) -> Dict[str, float]:
        """Get performance summary statistics."""
        summary = {}
        for key, values in self.performance_stats.items():
            if values:
                summary[f"{key}_mean"] = np.mean(values[-100:])  # Last 100 measurements
                summary[f"{key}_std"] = np.std(values[-100:])
                summary[f"{key}_max"] = np.max(values[-100:])
                
        return summary
        
    def query_episodic_memory(self, query_description: str, k: int = 5) -> List[Dict]:
        """Query episodic memory with natural language."""
        # Simple implementation - in practice, this could use embedding similarity
        current_state = self._extract_neuron_states()
        state_vector = current_state['voltages'].numpy()
        
        return self.memory_manager.retrieve_relevant_episodes(state_vector, k)
        
    def get_integration_status(self) -> Dict[str, Any]:
        """Get current integration status and health metrics."""
        return {
            'is_running': self.is_running,
            'neuromorphic_neurons': len(self.neuromorphic_core.neurons),
            'transformer_parameters': sum(p.numel() for p in self.transformer.parameters()),
            'memory_episodes': len(self.memory_manager.memory_buffer),
            'performance_stats': self.get_performance_summary(),
            'hardware_acceleration': {
                'neuromorphic': self.neuromorphic_core.get_hardware_info(),
                'transformer': {
                    'device': next(self.transformer.parameters()).device,
                    'dtype': next(self.transformer.parameters()).dtype
                }
            }
        }


def create_integrated_system(config: Optional[Dict] = None) -> NeuromorphicTransformerBridge:
    """Factory function to create an integrated neuromorphic-transformer system."""
    if config is None:
        config = get_config()
        
    # Initialize neuromorphic core
    neuromorphic_config = config.get('neuromorphic_core', {})
    neuromorphic_core = NeuromorphicCore(
        dimensions=neuromorphic_config.get('dimensions', (20, 20, 20)),
        config=neuromorphic_config
    )
    
    # Initialize transformer
    transformer_config = HyperDimensionalTransformerConfig()
    if 'hyper_transformer' in config:
        # Update transformer config from global config
        for key, value in config['hyper_transformer'].items():
            if hasattr(transformer_config, key):
                setattr(transformer_config, key, value)
                
    transformer = HyperDimensionalTransformer(transformer_config)
    
    # Initialize bridge
    bridge_config = BridgeConfig()
    if 'bridge' in config:
        for key, value in config['bridge'].items():
            if hasattr(bridge_config, key):
                setattr(bridge_config, key, value)
                
    bridge = NeuromorphicTransformerBridge(
        neuromorphic_core=neuromorphic_core,
        transformer=transformer,
        config=bridge_config
    )
    
    logger.info("Integrated neuromorphic-transformer system created successfully")
    return bridge


# Example usage and testing
if __name__ == "__main__":
    # Create integrated system
    bridge = create_integrated_system()
    
    # Create some neurons
    neuron_ids = bridge.neuromorphic_core.create_neurons(100, neuron_type='EXCITATORY')
    
    # Start async processing
    bridge.start_async_processing()
    
    # Run simulation
    try:
        for step in range(100):
            external_input = {0: 10.0}  # Stimulate first neuron
            results = bridge.process_joint_step(0.1, external_input)
            
            if step % 10 == 0:
                status = bridge.get_integration_status()
                print(f"Step {step}: {len(results['neuron_spikes'][results['neuron_spikes']])} spikes")
                print(f"Bridge latency: {status['performance_stats'].get('bridge_latency_ms_mean', 0):.2f}ms")
                
    finally:
        bridge.stop_async_processing()
        
    print("Integration test completed successfully!")
