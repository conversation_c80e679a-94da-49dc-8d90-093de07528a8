#!/usr/bin/env python3
"""
Neuromorphic-Transformer Bridge for ULTRA
==========================================

This module provides the integration layer between the neuromorphic core
and the hyper-dimensional transformer, enabling seamless information flow
and coordinated processing between biological and symbolic AI components.

Key Features:
- Real-time neuron state -> transformer embedding conversion
- Attention-guided neuromorphic stimulation
- Shared episodic memory between systems
- Dynamic resource allocation based on task complexity
"""

import torch
import torch.nn as nn
import numpy as np
from typing import Dict, List, Tuple, Optional, Any, Union
from dataclasses import dataclass
from collections import defaultdict
import threading
import queue
import time

# ULTRA imports
from ultra.config import get_config
from ultra.core_neural.neuromorphic_core import NeuromorphicCore, NeuronType, OscillationType
from ultra.hyper_transformer import HyperDimensionalTransformer, HyperDimensionalTransformerConfig
from ultra.utils.ultra_logging import get_logger

# Configure logging
logger = get_logger(__name__)

@dataclass
class BridgeConfig:
    """Configuration for neuromorphic-transformer bridge."""
    # Embedding parameters
    neuron_embedding_dim: int = 512
    max_neurons_per_batch: int = 1000
    temporal_window_ms: float = 100.0
    
    # Attention parameters
    attention_guided_stimulation: bool = True
    attention_feedback_strength: float = 0.1
    
    # Memory parameters
    episodic_memory_size: int = 10000
    memory_decay_rate: float = 0.95
    
    # Performance parameters
    update_frequency_hz: float = 100.0
    async_processing: bool = True


class NeuronStateEncoder(nn.Module):
    """Encodes neuromorphic state into transformer-compatible embeddings."""
    
    def __init__(self, config: BridgeConfig):
        super().__init__()
        self.config = config
        
        # State feature encoding
        self.voltage_encoder = nn.Linear(1, 64)
        self.activity_encoder = nn.Linear(1, 64)
        self.calcium_encoder = nn.Linear(1, 64)
        self.position_encoder = nn.Linear(3, 64)  # 3D position
        
        # Neuron type embedding
        self.type_embedding = nn.Embedding(10, 64)  # Support for various neuron types
        
        # Temporal encoding
        self.temporal_encoder = nn.Linear(1, 64)
        
        # Output projection
        self.output_projection = nn.Linear(64 * 6, config.neuron_embedding_dim)
        self.layer_norm = nn.LayerNorm(config.neuron_embedding_dim)
        
    def forward(self, neuron_states: Dict[str, torch.Tensor]) -> torch.Tensor:
        """
        Encode neuron states into embeddings.
        
        Args:
            neuron_states: Dictionary containing:
                - voltages: [batch, num_neurons]
                - activities: [batch, num_neurons] 
                - calcium_levels: [batch, num_neurons]
                - positions: [batch, num_neurons, 3]
                - types: [batch, num_neurons]
                - timestamps: [batch, num_neurons]
                
        Returns:
            embeddings: [batch, num_neurons, embedding_dim]
        """
        batch_size, num_neurons = neuron_states['voltages'].shape
        
        # Encode individual features
        v_emb = self.voltage_encoder(neuron_states['voltages'].unsqueeze(-1))
        a_emb = self.activity_encoder(neuron_states['activities'].unsqueeze(-1))
        c_emb = self.calcium_encoder(neuron_states['calcium_levels'].unsqueeze(-1))
        p_emb = self.position_encoder(neuron_states['positions'])
        t_emb = self.type_embedding(neuron_states['types'].long())
        time_emb = self.temporal_encoder(neuron_states['timestamps'].unsqueeze(-1))
        
        # Concatenate all features
        combined = torch.cat([v_emb, a_emb, c_emb, p_emb, t_emb, time_emb], dim=-1)
        
        # Project to final embedding dimension
        embeddings = self.output_projection(combined)
        embeddings = self.layer_norm(embeddings)
        
        return embeddings


class AttentionGuidedStimulator:
    """Uses transformer attention to guide neuromorphic stimulation."""
    
    def __init__(self, config: BridgeConfig):
        self.config = config
        self.attention_history = []
        
    def compute_stimulation_pattern(self, 
                                  attention_weights: torch.Tensor,
                                  neuron_ids: List[int]) -> Dict[int, float]:
        """
        Convert attention weights to neuromorphic stimulation.
        
        Args:
            attention_weights: [batch, heads, seq_len, seq_len]
            neuron_ids: List of neuron IDs corresponding to sequence positions
            
        Returns:
            stimulation_pattern: Dict mapping neuron_id to stimulation current
        """
        # Average attention across heads and batch
        avg_attention = attention_weights.mean(dim=(0, 1))  # [seq_len, seq_len]
        
        # Compute importance score for each neuron (sum of attention received)
        importance_scores = avg_attention.sum(dim=0)  # [seq_len]
        
        # Convert to stimulation currents with biologically realistic dynamics
        stimulation_pattern = {}
        
        # Apply temporal smoothing to prevent sudden stimulation changes
        current_time = time.time()
        if hasattr(self, 'last_stimulation_time'):
            dt = current_time - self.last_stimulation_time
            smoothing_factor = min(1.0, dt / 0.1)  # 100ms smoothing window
        else:
            smoothing_factor = 1.0
        
        self.last_stimulation_time = current_time
        
        for i, neuron_id in enumerate(neuron_ids):
            if i < len(importance_scores):
                # Base stimulation from attention
                base_current = float(importance_scores[i]) * self.config.attention_feedback_strength
                
                # Apply biological constraints
                # Maximum stimulation current (prevent damage)
                max_current = 50.0  # pA
                min_current = -20.0  # pA (inhibition)
                
                # Clamp to biological range
                clamped_current = np.clip(base_current, min_current, max_current)
                
                # Apply temporal smoothing
                if hasattr(self, 'previous_stimulation') and neuron_id in self.previous_stimulation:
                    previous_current = self.previous_stimulation[neuron_id]
                    smoothed_current = (
                        previous_current * (1 - smoothing_factor) +
                        clamped_current * smoothing_factor
                    )
                else:
                    smoothed_current = clamped_current
                
                stimulation_pattern[neuron_id] = smoothed_current
        
        # Store for next iteration
        self.previous_stimulation = stimulation_pattern.copy()
        
        # Store attention history for analysis
        self.attention_history.append({
            'timestamp': current_time,
            'attention_weights': avg_attention.detach().cpu().numpy(),
            'stimulation_pattern': stimulation_pattern.copy()
        })
        
        # Maintain history size
        if len(self.attention_history) > 1000:
            self.attention_history.pop(0)
                
        return stimulation_pattern


class EpisodicMemoryManager:
    """Manages shared episodic memory between neuromorphic and transformer systems."""
    
    def __init__(self, config: BridgeConfig):
        self.config = config
        self.memory_buffer = []
        self.memory_lock = threading.Lock()
        
    def store_episode(self, 
                     neuron_states: np.ndarray,
                     transformer_outputs: torch.Tensor,
                     attention_patterns: torch.Tensor,
                     timestamp: float,
                     context: Optional[Dict] = None):
        """Store an episode in shared memory."""
        with self.memory_lock:
            episode = {
                'timestamp': timestamp,
                'neuron_states': neuron_states.copy(),
                'transformer_outputs': transformer_outputs.detach().cpu().numpy(),
                'attention_patterns': attention_patterns.detach().cpu().numpy(),
                'context': context or {}
            }
            
            self.memory_buffer.append(episode)
            
            # Maintain buffer size
            if len(self.memory_buffer) > self.config.episodic_memory_size:
                self.memory_buffer.pop(0)
                
    def retrieve_relevant_episodes(self, 
                                  query_state: np.ndarray,
                                  k: int = 5,
                                  similarity_threshold: float = 0.1) -> List[Dict]:
        """Retrieve k most relevant episodes using advanced similarity metrics."""
        with self.memory_lock:
            if not self.memory_buffer:
                return []
            
            # Normalize query state
            query_norm = np.linalg.norm(query_state.flatten())
            if query_norm == 0:
                return []
            
            normalized_query = query_state.flatten() / query_norm
            
            # Compute multiple similarity metrics
            similarities = []
            current_time = time.time()
            
            for episode in self.memory_buffer:
                # Temporal decay factor
                time_diff = current_time - episode['timestamp']
                temporal_weight = np.exp(-time_diff / (3600.0 * self.config.memory_decay_rate))
                
                # Neural state similarity (cosine similarity)
                episode_state = episode['neuron_states'].flatten()
                episode_norm = np.linalg.norm(episode_state)
                
                if episode_norm > 0:
                    normalized_episode = episode_state / episode_norm
                    cosine_sim = np.dot(normalized_query, normalized_episode)
                else:
                    cosine_sim = 0.0
                
                # Transformer output similarity
                if 'transformer_outputs' in episode:
                    transformer_sim = self._calculate_transformer_similarity(
                        query_state, episode['transformer_outputs']
                    )
                else:
                    transformer_sim = 0.0
                
                # Attention pattern similarity
                if 'attention_patterns' in episode:
                    attention_sim = self._calculate_attention_similarity(
                        query_state, episode['attention_patterns']
                    )
                else:
                    attention_sim = 0.0
                
                # Combined similarity score
                combined_similarity = (
                    cosine_sim * 0.5 +
                    transformer_sim * 0.3 +
                    attention_sim * 0.2
                ) * temporal_weight
                
                if combined_similarity >= similarity_threshold:
                    similarities.append((combined_similarity, episode))
            
            # Sort by similarity and return top k
            similarities.sort(key=lambda x: x[0], reverse=True)
            return [episode for _, episode in similarities[:k]]
        
    def _calculate_transformer_similarity(self, query_state: np.ndarray, 
                                        transformer_outputs: np.ndarray) -> float:
        """Calculate similarity between query state and transformer outputs."""
        try:
            # Project neural state to transformer space (simplified)
            query_projection = np.mean(query_state.reshape(-1, 4), axis=1)  # Assume 4D neural state
            
            # Compare with transformer outputs
            if transformer_outputs.ndim > 1:
                output_mean = np.mean(transformer_outputs, axis=0)
            else:
                output_mean = transformer_outputs
            
            # Ensure same dimensionality
            min_dim = min(len(query_projection), len(output_mean))
            if min_dim == 0:
                return 0.0
            
            query_trunc = query_projection[:min_dim]
            output_trunc = output_mean[:min_dim]
            
            # Cosine similarity
            query_norm = np.linalg.norm(query_trunc)
            output_norm = np.linalg.norm(output_trunc)
            
            if query_norm > 0 and output_norm > 0:
                return np.dot(query_trunc, output_trunc) / (query_norm * output_norm)
            else:
                return 0.0
                
        except Exception as e:
            logger.error(f"Error calculating transformer similarity: {e}")
            return 0.0
    
    def _calculate_attention_similarity(self, query_state: np.ndarray,
                                      attention_patterns: np.ndarray) -> float:
        """Calculate similarity between query state and attention patterns."""
        try:
            # Extract attention statistics
            if attention_patterns.ndim >= 2:
                attention_entropy = -np.sum(
                    attention_patterns * np.log(attention_patterns + 1e-10),
                    axis=-1
                )
                attention_stats = np.array([
                    np.mean(attention_entropy),
                    np.std(attention_entropy),
                    np.max(attention_patterns),
                    np.mean(attention_patterns)
                ])
            else:
                attention_stats = attention_patterns.flatten()[:4]
            
            # Extract neural state statistics
            neural_stats = np.array([
                np.mean(query_state),
                np.std(query_state),
                np.max(query_state),
                np.min(query_state)
            ])
            
            # Normalize statistics
            attention_norm = np.linalg.norm(attention_stats)
            neural_norm = np.linalg.norm(neural_stats)
            
            if attention_norm > 0 and neural_norm > 0:
                return np.dot(attention_stats, neural_stats) / (attention_norm * neural_norm)
            else:
                return 0.0
                
        except Exception as e:
            logger.error(f"Error calculating attention similarity: {e}")
            return 0.0


class NeuromorphicTransformerBridge:
    """Main bridge class coordinating neuromorphic and transformer systems."""
    
    def __init__(self, 
                 neuromorphic_core: NeuromorphicCore,
                 transformer: HyperDimensionalTransformer,
                 config: Optional[BridgeConfig] = None):
        self.neuromorphic_core = neuromorphic_core
        self.transformer = transformer
        self.config = config or BridgeConfig()
        
        # Initialize components
        self.state_encoder = NeuronStateEncoder(self.config)
        self.stimulator = AttentionGuidedStimulator(self.config)
        self.memory_manager = EpisodicMemoryManager(self.config)
        
        # Runtime state
        self.is_running = False
        self.processing_thread = None
        self.state_queue = queue.Queue(maxsize=100)
        
        # Performance monitoring
        self.performance_stats = {
            'bridge_latency_ms': [],
            'encoding_time_ms': [],
            'attention_time_ms': [],
            'stimulation_time_ms': []
        }
        
    def start_async_processing(self):
        """Start asynchronous processing loop."""
        if self.config.async_processing and not self.is_running:
            self.is_running = True
            self.processing_thread = threading.Thread(target=self._processing_loop)
            self.processing_thread.start()
            logger.info("Neuromorphic-Transformer bridge started in async mode")
            
    def stop_async_processing(self):
        """Stop asynchronous processing."""
        if self.is_running:
            self.is_running = False
            if self.processing_thread:
                self.processing_thread.join()
            logger.info("Neuromorphic-Transformer bridge stopped")
            
    def _processing_loop(self):
        """Main asynchronous processing loop."""
        dt = 1.0 / self.config.update_frequency_hz
        
        while self.is_running:
            start_time = time.time()
            
            try:
                # Get current neuromorphic state
                neuron_states = self._extract_neuron_states()
                
                # Process through bridge
                self._process_state_sync(neuron_states)
                
                # Sleep to maintain frequency
                elapsed = time.time() - start_time
                if elapsed < dt:
                    time.sleep(dt - elapsed)
                    
            except Exception as e:
                logger.error(f"Error in bridge processing loop: {e}")
                
    def _extract_neuron_states(self) -> Dict[str, Any]:
        """Extract current states from neuromorphic core."""
        neuron_states = {
            'voltages': [],
            'activities': [],
            'calcium_levels': [],
            'positions': [],
            'types': [],
            'timestamps': [],
            'neuron_ids': []
        }
        
        current_time = self.neuromorphic_core.current_time
        
        for neuron_id, neuron in self.neuromorphic_core.neurons.items():
            neuron_states['voltages'].append(neuron.v)
            
            # Activity level (spike rate over recent window)
            recent_spikes = [t for t in neuron.spike_times 
                           if current_time - t < self.config.temporal_window_ms]
            activity = len(recent_spikes) / (self.config.temporal_window_ms / 1000.0)
            neuron_states['activities'].append(activity)
            
            # Enhanced calcium dynamics if available
            if hasattr(neuron, 'calcium_concentration'):
                neuron_states['calcium_levels'].append(neuron.calcium_concentration)
            else:
                neuron_states['calcium_levels'].append(0.1)  # Default value
                
            # 3D position
            if neuron_id in self.neuromorphic_core.neuron_positions:
                pos = self.neuromorphic_core.neuron_positions[neuron_id]
                neuron_states['positions'].append(list(pos))
            else:
                neuron_states['positions'].append([0.0, 0.0, 0.0])
                
            # Neuron type
            neuron_type = self.neuromorphic_core.neuron_types.get(neuron_id, NeuronType.EXCITATORY)
            neuron_states['types'].append(neuron_type.value)
            
            # Timestamp
            neuron_states['timestamps'].append(current_time)
            neuron_states['neuron_ids'].append(neuron_id)
            
        # Convert to tensors
        for key in ['voltages', 'activities', 'calcium_levels', 'types', 'timestamps']:
            neuron_states[key] = torch.tensor(neuron_states[key], dtype=torch.float32).unsqueeze(0)
            
        neuron_states['positions'] = torch.tensor(neuron_states['positions'], dtype=torch.float32).unsqueeze(0)
        
        return neuron_states
        
    def _process_state_sync(self, neuron_states: Dict[str, Any]):
        """Process states synchronously through the bridge."""
        start_time = time.time()
        
        # 1. Encode neuron states for transformer
        encoding_start = time.time()
        neuron_embeddings = self.state_encoder(neuron_states)
        self.performance_stats['encoding_time_ms'].append(
            (time.time() - encoding_start) * 1000
        )
        
        # 2. Process through transformer
        transformer_start = time.time()
        transformer_outputs, attention_weights = self._process_through_transformer(
            neuron_embeddings
        )
        self.performance_stats['attention_time_ms'].append(
            (time.time() - transformer_start) * 1000
        )
        
        # 3. Generate attention-guided stimulation
        stimulation_start = time.time()
        stimulation_pattern = self.stimulator.compute_stimulation_pattern(
            attention_weights, neuron_states['neuron_ids']
        )
        self.performance_stats['stimulation_time_ms'].append(
            (time.time() - stimulation_start) * 1000
        )
        
        # 4. Apply stimulation to neuromorphic core
        self._apply_stimulation(stimulation_pattern)
        
        # 5. Store episode in memory
        self.memory_manager.store_episode(
            neuron_states['voltages'].numpy(),
            transformer_outputs,
            attention_weights,
            time.time(),
            context={'processing_type': 'bridge_sync'}
        )
        
        # 6. Update performance statistics
        total_time = (time.time() - start_time) * 1000
        self.performance_stats['bridge_latency_ms'].append(total_time)
        
        # Maintain stats history
        for key in self.performance_stats:
            if len(self.performance_stats[key]) > 1000:
                self.performance_stats[key] = self.performance_stats[key][-1000:]
    
    def _process_through_transformer(self, embeddings: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """Process neuron embeddings through transformer and extract attention."""
        try:
            # Create input tokens for transformer
            batch_size, seq_len, embed_dim = embeddings.shape
            
            # Add positional encodings if needed
            if hasattr(self.transformer, 'positional_encoding'):
                embeddings = self.transformer.positional_encoding(embeddings)
            
            # Process through transformer layers
            output = embeddings
            attention_weights = None
            
            if hasattr(self.transformer, 'layers'):
                for layer in self.transformer.layers:
                    if hasattr(layer, 'self_attention'):
                        output, attention = layer.self_attention(output, output, output)
                        if attention_weights is None:
                            attention_weights = attention
                        else:
                            attention_weights = torch.cat([attention_weights, attention], dim=1)
                    
                    if hasattr(layer, 'forward'):
                        output = layer.forward(output)
            else:
                # Fallback: use transformer directly
                output = self.transformer(embeddings)
                # Create dummy attention weights
                attention_weights = torch.ones(batch_size, 1, seq_len, seq_len) / seq_len
            
            return output, attention_weights
            
        except Exception as e:
            logger.error(f"Error processing through transformer: {e}")
            # Return dummy outputs
            batch_size, seq_len, embed_dim = embeddings.shape
            dummy_output = torch.zeros_like(embeddings)
            dummy_attention = torch.ones(batch_size, 1, seq_len, seq_len) / seq_len
            return dummy_output, dummy_attention
    
    def _apply_stimulation(self, stimulation_pattern: Dict[int, float]):
        """Apply stimulation currents to neuromorphic neurons."""
        try:
            for neuron_id, current in stimulation_pattern.items():
                if neuron_id in self.neuromorphic_core.neurons:
                    neuron = self.neuromorphic_core.neurons[neuron_id]
                    
                    # Apply current injection
                    if hasattr(neuron, 'inject_current'):
                        neuron.inject_current(current)
                    elif hasattr(neuron, 'external_current'):
                        neuron.external_current += current
                    else:
                        # Fallback: modify voltage directly (simplified)
                        neuron.v += current * 0.01  # Scale factor
                        
        except Exception as e:
            logger.error(f"Error applying stimulation: {e}")
    
    def process_batch(self, batch_data: Dict[str, torch.Tensor]) -> Dict[str, Any]:
        """Process a batch of neuromorphic data through the bridge."""
        start_time = time.time()
        
        try:
            # Encode neural states
            embeddings = self.state_encoder(batch_data)
            
            # Process through transformer
            outputs, attention = self._process_through_transformer(embeddings)
            
            # Generate stimulation patterns
            stimulation_patterns = []
            for i in range(embeddings.size(0)):  # For each item in batch
                pattern = self.stimulator.compute_stimulation_pattern(
                    attention[i:i+1], batch_data['neuron_ids']
                )
                stimulation_patterns.append(pattern)
            
            return {
                'embeddings': embeddings,
                'transformer_outputs': outputs,
                'attention_weights': attention,
                'stimulation_patterns': stimulation_patterns,
                'processing_time_ms': (time.time() - start_time) * 1000
            }
            
        except Exception as e:
            logger.error(f"Error in batch processing: {e}")
            return {'error': str(e)}
    
    def get_performance_summary(self) -> Dict[str, float]:
        """Get performance statistics summary."""
        summary = {}
        for key, values in self.performance_stats.items():
            if values:
                summary[f"{key}_mean"] = np.mean(values)
                summary[f"{key}_std"] = np.std(values)
                summary[f"{key}_min"] = np.min(values)
                summary[f"{key}_max"] = np.max(values)
                summary[f"{key}_p95"] = np.percentile(values, 95)
            else:
                summary[f"{key}_mean"] = 0.0
                summary[f"{key}_std"] = 0.0
                summary[f"{key}_min"] = 0.0
                summary[f"{key}_max"] = 0.0
                summary[f"{key}_p95"] = 0.0
        
        return summary
    
    def get_memory_insights(self, query_state: np.ndarray, k: int = 10) -> Dict[str, Any]:
        """Get insights from episodic memory based on current state."""
        relevant_episodes = self.memory_manager.retrieve_relevant_episodes(query_state, k)
        
        if not relevant_episodes:
            return {'episodes': [], 'insights': 'No relevant episodes found'}
        
        # Analyze patterns in relevant episodes
        attention_patterns = []
        neural_patterns = []
        
        for episode in relevant_episodes:
            if 'attention_patterns' in episode:
                attention_patterns.append(episode['attention_patterns'])
            neural_patterns.append(episode['neuron_states'])
        
        insights = {
            'episodes': relevant_episodes,
            'num_relevant': len(relevant_episodes),
            'temporal_span': relevant_episodes[0]['timestamp'] - relevant_episodes[-1]['timestamp'],
            'average_attention_entropy': np.mean([
                -np.sum(pattern * np.log(pattern + 1e-10)) 
                for pattern in attention_patterns
            ]) if attention_patterns else 0.0,
            'neural_variability': np.std([
                np.std(pattern) for pattern in neural_patterns
            ]) if neural_patterns else 0.0
        }
        
        return insights


# Export main classes
__all__ = [
    'NeuromorphicTransformerBridge',
    'BridgeConfig',
    'NeuronStateEncoder',
    'AttentionGuidedStimulator',
    'EpisodicMemoryManager'
]
