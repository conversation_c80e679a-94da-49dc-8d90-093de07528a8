#!/usr/bin/env python3
"""
ULTRA Bridge Performance Optimizer
=================================

This module provides comprehensive performance optimization for ULTRA integration bridges,
including monitoring, analysis, and automatic optimization strategies.

Key Features:
- Real-time performance monitoring
- Automatic optimization strategies
- Resource usage optimization
- Latency reduction techniques
- Memory management optimization
"""

import asyncio
import time
import psutil
import threading
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, field
from collections import defaultdict, deque
import statistics
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class PerformanceMetrics:
    """Performance metrics for bridge operations"""
    operation_count: int = 0
    total_latency: float = 0.0
    min_latency: float = float('inf')
    max_latency: float = 0.0
    error_count: int = 0
    success_rate: float = 1.0
    memory_usage_mb: float = 0.0
    cpu_usage_percent: float = 0.0
    throughput_ops_per_sec: float = 0.0
    last_updated: float = field(default_factory=time.time)

@dataclass
class OptimizationStrategy:
    """Optimization strategy configuration"""
    name: str
    enabled: bool = True
    threshold: float = 0.0
    action: Callable = None
    priority: int = 1
    description: str = ""

class BridgePerformanceMonitor:
    """Real-time performance monitoring for bridges"""
    
    def __init__(self, bridge_name: str, monitoring_interval: float = 1.0):
        self.bridge_name = bridge_name
        self.monitoring_interval = monitoring_interval
        self.metrics = PerformanceMetrics()
        self.operation_history = deque(maxlen=1000)
        self.monitoring_active = False
        self.monitor_thread = None
        self.lock = threading.Lock()
        
        # Performance thresholds
        self.thresholds = {
            'max_latency': 1.0,  # seconds
            'min_success_rate': 0.95,
            'max_memory_mb': 500,
            'max_cpu_percent': 80
        }
        
        logger.info(f"Performance monitor initialized for {bridge_name}")
    
    def start_monitoring(self):
        """Start performance monitoring"""
        if not self.monitoring_active:
            self.monitoring_active = True
            self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
            self.monitor_thread.start()
            logger.info(f"Performance monitoring started for {self.bridge_name}")
    
    def stop_monitoring(self):
        """Stop performance monitoring"""
        self.monitoring_active = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=2.0)
        logger.info(f"Performance monitoring stopped for {self.bridge_name}")
    
    def record_operation(self, operation_name: str, latency: float, success: bool, memory_mb: float = 0.0):
        """Record a bridge operation"""
        with self.lock:
            self.metrics.operation_count += 1
            self.metrics.total_latency += latency
            self.metrics.min_latency = min(self.metrics.min_latency, latency)
            self.metrics.max_latency = max(self.metrics.max_latency, latency)
            
            if not success:
                self.metrics.error_count += 1
            
            # Update success rate
            self.metrics.success_rate = (self.metrics.operation_count - self.metrics.error_count) / self.metrics.operation_count
            
            # Update memory usage
            if memory_mb > 0:
                self.metrics.memory_usage_mb = memory_mb
            
            # Calculate throughput
            time_window = time.time() - self.metrics.last_updated
            if time_window > 0:
                recent_ops = len([op for op in self.operation_history if time.time() - op['timestamp'] < 60])
                self.metrics.throughput_ops_per_sec = recent_ops / 60.0
            
            # Store operation in history
            self.operation_history.append({
                'operation': operation_name,
                'latency': latency,
                'success': success,
                'timestamp': time.time(),
                'memory_mb': memory_mb
            })
            
            self.metrics.last_updated = time.time()
    
    def _monitor_loop(self):
        """Background monitoring loop"""
        while self.monitoring_active:
            try:
                # Update system metrics
                process = psutil.Process()
                self.metrics.cpu_usage_percent = process.cpu_percent()
                self.metrics.memory_usage_mb = process.memory_info().rss / 1024 / 1024
                
                # Check thresholds and trigger alerts
                self._check_thresholds()
                
                time.sleep(self.monitoring_interval)
                
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                time.sleep(self.monitoring_interval)
    
    def _check_thresholds(self):
        """Check performance thresholds and trigger alerts"""
        alerts = []
        
        if self.metrics.max_latency > self.thresholds['max_latency']:
            alerts.append(f"High latency detected: {self.metrics.max_latency:.3f}s")
        
        if self.metrics.success_rate < self.thresholds['min_success_rate']:
            alerts.append(f"Low success rate: {self.metrics.success_rate:.2%}")
        
        if self.metrics.memory_usage_mb > self.thresholds['max_memory_mb']:
            alerts.append(f"High memory usage: {self.metrics.memory_usage_mb:.1f}MB")
        
        if self.metrics.cpu_usage_percent > self.thresholds['max_cpu_percent']:
            alerts.append(f"High CPU usage: {self.metrics.cpu_usage_percent:.1f}%")
        
        for alert in alerts:
            logger.warning(f"[{self.bridge_name}] Performance Alert: {alert}")
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get current performance metrics"""
        with self.lock:
            avg_latency = self.metrics.total_latency / max(1, self.metrics.operation_count)
            
            return {
                'bridge_name': self.bridge_name,
                'operation_count': self.metrics.operation_count,
                'average_latency': avg_latency,
                'min_latency': self.metrics.min_latency if self.metrics.min_latency != float('inf') else 0,
                'max_latency': self.metrics.max_latency,
                'success_rate': self.metrics.success_rate,
                'error_count': self.metrics.error_count,
                'memory_usage_mb': self.metrics.memory_usage_mb,
                'cpu_usage_percent': self.metrics.cpu_usage_percent,
                'throughput_ops_per_sec': self.metrics.throughput_ops_per_sec,
                'last_updated': self.metrics.last_updated
            }

class BridgeOptimizer:
    """Automatic optimization for bridge performance"""
    
    def __init__(self, bridge_name: str):
        self.bridge_name = bridge_name
        self.monitor = BridgePerformanceMonitor(bridge_name)
        self.optimization_strategies = self._initialize_strategies()
        self.optimization_active = False
        self.optimization_history = []
        
        logger.info(f"Bridge optimizer initialized for {bridge_name}")
    
    def _initialize_strategies(self) -> List[OptimizationStrategy]:
        """Initialize optimization strategies"""
        return [
            OptimizationStrategy(
                name="memory_cleanup",
                threshold=400.0,  # MB
                action=self._optimize_memory,
                priority=1,
                description="Clean up memory when usage exceeds threshold"
            ),
            OptimizationStrategy(
                name="operation_caching",
                threshold=0.1,  # seconds average latency
                action=self._enable_caching,
                priority=2,
                description="Enable caching for operations with high latency"
            ),
            OptimizationStrategy(
                name="async_optimization",
                threshold=0.05,  # seconds
                action=self._optimize_async_operations,
                priority=3,
                description="Optimize asynchronous operation handling"
            ),
            OptimizationStrategy(
                name="resource_pooling",
                threshold=50,  # operations per second
                action=self._enable_resource_pooling,
                priority=4,
                description="Enable resource pooling for high-throughput scenarios"
            )
        ]
    
    def start_optimization(self):
        """Start automatic optimization"""
        self.optimization_active = True
        self.monitor.start_monitoring()
        
        # Start optimization loop
        asyncio.create_task(self._optimization_loop())
        logger.info(f"Automatic optimization started for {self.bridge_name}")
    
    def stop_optimization(self):
        """Stop automatic optimization"""
        self.optimization_active = False
        self.monitor.stop_monitoring()
        logger.info(f"Automatic optimization stopped for {self.bridge_name}")
    
    async def _optimization_loop(self):
        """Main optimization loop"""
        while self.optimization_active:
            try:
                await self._run_optimization_cycle()
                await asyncio.sleep(5.0)  # Check every 5 seconds
            except Exception as e:
                logger.error(f"Error in optimization loop: {e}")
                await asyncio.sleep(5.0)
    
    async def _run_optimization_cycle(self):
        """Run one optimization cycle"""
        metrics = self.monitor.get_metrics()
        
        # Sort strategies by priority
        strategies = sorted(self.optimization_strategies, key=lambda s: s.priority)
        
        for strategy in strategies:
            if not strategy.enabled:
                continue
            
            should_optimize = False
            
            # Check if optimization is needed based on strategy
            if strategy.name == "memory_cleanup":
                should_optimize = metrics['memory_usage_mb'] > strategy.threshold
            elif strategy.name == "operation_caching":
                should_optimize = metrics['average_latency'] > strategy.threshold
            elif strategy.name == "async_optimization":
                should_optimize = metrics['average_latency'] > strategy.threshold
            elif strategy.name == "resource_pooling":
                should_optimize = metrics['throughput_ops_per_sec'] > strategy.threshold
            
            if should_optimize:
                logger.info(f"Applying optimization strategy: {strategy.name}")
                try:
                    await strategy.action()
                    self.optimization_history.append({
                        'strategy': strategy.name,
                        'timestamp': time.time(),
                        'metrics_before': metrics.copy()
                    })
                except Exception as e:
                    logger.error(f"Error applying optimization {strategy.name}: {e}")
    
    async def _optimize_memory(self):
        """Optimize memory usage"""
        import gc
        gc.collect()
        logger.info("Memory cleanup performed")
    
    async def _enable_caching(self):
        """Enable operation caching"""
        # Placeholder for caching optimization
        logger.info("Operation caching optimization applied")
    
    async def _optimize_async_operations(self):
        """Optimize asynchronous operations"""
        # Placeholder for async optimization
        logger.info("Async operation optimization applied")
    
    async def _enable_resource_pooling(self):
        """Enable resource pooling"""
        # Placeholder for resource pooling
        logger.info("Resource pooling optimization applied")
    
    def get_optimization_report(self) -> Dict[str, Any]:
        """Get optimization report"""
        return {
            'bridge_name': self.bridge_name,
            'optimization_active': self.optimization_active,
            'strategies': [
                {
                    'name': s.name,
                    'enabled': s.enabled,
                    'threshold': s.threshold,
                    'priority': s.priority,
                    'description': s.description
                }
                for s in self.optimization_strategies
            ],
            'optimization_history': self.optimization_history[-10:],  # Last 10 optimizations
            'current_metrics': self.monitor.get_metrics()
        }

class BridgePerformanceCoordinator:
    """Coordinates performance optimization across multiple bridges"""
    
    def __init__(self):
        self.bridge_optimizers = {}
        self.global_metrics = {}
        self.coordination_active = False
        
        logger.info("Bridge performance coordinator initialized")
    
    def register_bridge(self, bridge_name: str) -> BridgeOptimizer:
        """Register a bridge for performance optimization"""
        optimizer = BridgeOptimizer(bridge_name)
        self.bridge_optimizers[bridge_name] = optimizer
        logger.info(f"Bridge {bridge_name} registered for optimization")
        return optimizer
    
    def start_coordination(self):
        """Start coordinated optimization"""
        self.coordination_active = True
        
        # Start optimization for all registered bridges
        for optimizer in self.bridge_optimizers.values():
            optimizer.start_optimization()
        
        logger.info("Coordinated optimization started for all bridges")
    
    def stop_coordination(self):
        """Stop coordinated optimization"""
        self.coordination_active = False
        
        # Stop optimization for all bridges
        for optimizer in self.bridge_optimizers.values():
            optimizer.stop_optimization()
        
        logger.info("Coordinated optimization stopped for all bridges")
    
    def get_global_report(self) -> Dict[str, Any]:
        """Get global performance report"""
        bridge_reports = {}
        total_operations = 0
        total_errors = 0
        total_memory = 0.0
        
        for bridge_name, optimizer in self.bridge_optimizers.items():
            report = optimizer.get_optimization_report()
            bridge_reports[bridge_name] = report
            
            metrics = report['current_metrics']
            total_operations += metrics['operation_count']
            total_errors += metrics['error_count']
            total_memory += metrics['memory_usage_mb']
        
        global_success_rate = (total_operations - total_errors) / max(1, total_operations)
        
        return {
            'coordination_active': self.coordination_active,
            'total_bridges': len(self.bridge_optimizers),
            'global_metrics': {
                'total_operations': total_operations,
                'total_errors': total_errors,
                'global_success_rate': global_success_rate,
                'total_memory_mb': total_memory
            },
            'bridge_reports': bridge_reports,
            'timestamp': time.time()
        }

# Global performance coordinator instance
_global_coordinator = None

def get_performance_coordinator() -> BridgePerformanceCoordinator:
    """Get global performance coordinator instance"""
    global _global_coordinator
    if _global_coordinator is None:
        _global_coordinator = BridgePerformanceCoordinator()
    return _global_coordinator

# Decorator for automatic performance monitoring
def monitor_performance(bridge_name: str):
    """Decorator to automatically monitor bridge performance"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            coordinator = get_performance_coordinator()
            
            if bridge_name not in coordinator.bridge_optimizers:
                coordinator.register_bridge(bridge_name)
            
            optimizer = coordinator.bridge_optimizers[bridge_name]
            
            start_time = time.time()
            success = True
            
            try:
                result = await func(*args, **kwargs)
                return result
            except Exception as e:
                success = False
                raise e
            finally:
                latency = time.time() - start_time
                memory_mb = psutil.Process().memory_info().rss / 1024 / 1024
                
                optimizer.monitor.record_operation(
                    func.__name__, latency, success, memory_mb
                )
        
        return wrapper
    return decorator

# Export all classes and functions
__all__ = [
    'PerformanceMetrics',
    'OptimizationStrategy',
    'BridgePerformanceMonitor',
    'BridgeOptimizer',
    'BridgePerformanceCoordinator',
    'get_performance_coordinator',
    'monitor_performance'
]
