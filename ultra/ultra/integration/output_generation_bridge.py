#!/usr/bin/env python3
"""
ULTRA Output Generation Integration Bridge
=========================================

This bridge integrates the output generation system with other ULTRA components,
enabling multimodal output coordination and synthesis across all ULTRA systems.

Key Features:
- Text output generation integration
- Visual output synthesis coordination
- Action output planning integration
- Multimodal synthesis coordination
- Output quality optimization
"""

import asyncio
import numpy as np
from typing import Dict, List, Any, Optional, Tuple, Union
from dataclasses import dataclass
import logging

# ULTRA imports
from ultra.utils.ultra_logging import get_ultra_logger
from ultra.utils.config import ConfigurationManager

# Output generation imports
try:
    from ultra.output_generation.text_output import TextGenerator
    from ultra.output_generation.visual_output import VisualGenerator
    from ultra.output_generation.action_output import ActionGenerator
    from ultra.output_generation.multimodal_synthesis import MultiModalSynthesizer
    HAS_OUTPUT_GENERATION = True
except ImportError as e:
    print(f"Warning: Output generation modules not available: {e}")
    HAS_OUTPUT_GENERATION = False

# Knowledge management for context
try:
    from ultra.knowledge_management.knowledge_integration import KnowledgeIntegrator
    HAS_KNOWLEDGE = True
except ImportError:
    HAS_KNOWLEDGE = False

@dataclass
class OutputState:
    """State information for output generation integration"""
    generated_texts: Dict[str, str]
    generated_visuals: Dict[str, Any]
    planned_actions: Dict[str, Any]
    multimodal_outputs: Dict[str, Any]
    output_quality_scores: Dict[str, float]
    generation_pipeline: List[str]

class OutputGenerationBridge:
    """
    Integration bridge for ULTRA's output generation system.
    
    This bridge coordinates output generation across all modalities and integrates
    with other ULTRA components for comprehensive output synthesis.
    """
    
    def __init__(self, config: Optional[Dict] = None):
        """Initialize the output generation bridge"""
        self.config = config or {}
        self.logger = get_ultra_logger(f"{__name__}.OutputGenerationBridge")
        
        # Initialize components
        self.text_generator = None
        self.visual_generator = None
        self.action_planner = None
        self.multimodal_synthesizer = None
        
        # State management
        self.current_state = None
        self.integration_active = False
        
        # Performance metrics
        self.generation_quality = 0.0
        self.synthesis_coherence = 0.0
        self.output_latency = 0.0
        
        self.logger.info("OutputGenerationBridge initialized")
    
    async def initialize_bridge(self) -> bool:
        """Initialize the output generation bridge with all components"""
        try:
            if not HAS_OUTPUT_GENERATION:
                self.logger.warning("Output generation modules not available")
                return False
            
            # Initialize output generation components
            self.text_generator = TextGenerator()
            self.visual_generator = VisualGenerator()
            self.action_planner = ActionGenerator()
            self.multimodal_synthesizer = MultiModalSynthesizer()
            
            # Initialize state
            self.current_state = OutputState(
                generated_texts={},
                generated_visuals={},
                planned_actions={},
                multimodal_outputs={},
                output_quality_scores={},
                generation_pipeline=[]
            )
            
            self.integration_active = True
            self.logger.info("Output generation bridge initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize output generation bridge: {e}")
            return False
    
    async def generate_text_output(self, prompt: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Generate text output based on prompt and context"""
        if not self.integration_active:
            return {"error": "Bridge not initialized"}
        
        try:
            # Generate text
            text_result = await self.text_generator.generate(prompt, context)
            
            # Store generated text
            text_id = f"text_{len(self.current_state.generated_texts)}"
            if 'generated_text' in text_result:
                self.current_state.generated_texts[text_id] = text_result['generated_text']
                
                # Calculate quality score
                quality_score = self._calculate_text_quality(text_result)
                self.current_state.output_quality_scores[text_id] = quality_score
            
            # Update generation pipeline
            self.current_state.generation_pipeline.append(f"text_generation_{text_id}")
            
            return {
                "text_result": text_result,
                "text_id": text_id,
                "quality_score": self.current_state.output_quality_scores.get(text_id, 0.0)
            }
            
        except Exception as e:
            self.logger.error(f"Text generation failed: {e}")
            return {"error": str(e)}
    
    async def generate_visual_output(self, description: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Generate visual output based on description and context"""
        if not self.integration_active:
            return {"error": "Bridge not initialized"}
        
        try:
            # Generate visual
            visual_result = await self.visual_generator.generate(description, context)
            
            # Store generated visual
            visual_id = f"visual_{len(self.current_state.generated_visuals)}"
            if 'generated_visual' in visual_result:
                self.current_state.generated_visuals[visual_id] = visual_result['generated_visual']
                
                # Calculate quality score
                quality_score = self._calculate_visual_quality(visual_result)
                self.current_state.output_quality_scores[visual_id] = quality_score
            
            # Update generation pipeline
            self.current_state.generation_pipeline.append(f"visual_generation_{visual_id}")
            
            return {
                "visual_result": visual_result,
                "visual_id": visual_id,
                "quality_score": self.current_state.output_quality_scores.get(visual_id, 0.0)
            }
            
        except Exception as e:
            self.logger.error(f"Visual generation failed: {e}")
            return {"error": str(e)}
    
    async def plan_action_output(self, goal: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Plan action output based on goal and context"""
        if not self.integration_active:
            return {"error": "Bridge not initialized"}
        
        try:
            # Plan actions
            action_result = await self.action_planner.plan(goal, context)
            
            # Store planned actions
            action_id = f"action_{len(self.current_state.planned_actions)}"
            if 'action_plan' in action_result:
                self.current_state.planned_actions[action_id] = action_result['action_plan']
                
                # Calculate quality score
                quality_score = self._calculate_action_quality(action_result)
                self.current_state.output_quality_scores[action_id] = quality_score
            
            # Update generation pipeline
            self.current_state.generation_pipeline.append(f"action_planning_{action_id}")
            
            return {
                "action_result": action_result,
                "action_id": action_id,
                "quality_score": self.current_state.output_quality_scores.get(action_id, 0.0)
            }
            
        except Exception as e:
            self.logger.error(f"Action planning failed: {e}")
            return {"error": str(e)}
    
    async def synthesize_multimodal_output(self, requirements: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """Synthesize multimodal output combining text, visual, and action components"""
        if not self.integration_active:
            return {"error": "Bridge not initialized"}
        
        try:
            # Generate individual components if needed
            components = {}
            
            if 'text_prompt' in requirements:
                text_result = await self.generate_text_output(
                    requirements['text_prompt'], context
                )
                components['text'] = text_result
            
            if 'visual_description' in requirements:
                visual_result = await self.generate_visual_output(
                    requirements['visual_description'], context
                )
                components['visual'] = visual_result
            
            if 'action_goal' in requirements:
                action_result = await self.plan_action_output(
                    requirements['action_goal'], context
                )
                components['action'] = action_result
            
            # Synthesize multimodal output
            synthesis_result = await self.multimodal_synthesizer.synthesize(
                components, requirements, context
            )
            
            # Store multimodal output
            multimodal_id = f"multimodal_{len(self.current_state.multimodal_outputs)}"
            if 'synthesized_output' in synthesis_result:
                self.current_state.multimodal_outputs[multimodal_id] = synthesis_result['synthesized_output']
                
                # Calculate synthesis coherence
                self.synthesis_coherence = self._calculate_synthesis_coherence(synthesis_result)
            
            return {
                "synthesis_result": synthesis_result,
                "components": components,
                "multimodal_id": multimodal_id,
                "coherence_score": self.synthesis_coherence
            }
            
        except Exception as e:
            self.logger.error(f"Multimodal synthesis failed: {e}")
            return {"error": str(e)}
    
    async def optimize_output_quality(self, output_id: str, optimization_criteria: Dict[str, Any]) -> Dict[str, Any]:
        """Optimize output quality based on criteria"""
        if not self.integration_active:
            return {"error": "Bridge not initialized"}
        
        try:
            # Get original output
            original_output = None
            output_type = None
            
            if output_id in self.current_state.generated_texts:
                original_output = self.current_state.generated_texts[output_id]
                output_type = "text"
            elif output_id in self.current_state.generated_visuals:
                original_output = self.current_state.generated_visuals[output_id]
                output_type = "visual"
            elif output_id in self.current_state.planned_actions:
                original_output = self.current_state.planned_actions[output_id]
                output_type = "action"
            elif output_id in self.current_state.multimodal_outputs:
                original_output = self.current_state.multimodal_outputs[output_id]
                output_type = "multimodal"
            
            if original_output is None:
                return {"error": f"Output {output_id} not found"}
            
            # Apply optimization based on type
            if output_type == "text":
                optimized_output = await self.text_generator.optimize(
                    original_output, optimization_criteria
                )
            elif output_type == "visual":
                optimized_output = await self.visual_generator.optimize(
                    original_output, optimization_criteria
                )
            elif output_type == "action":
                optimized_output = await self.action_planner.optimize(
                    original_output, optimization_criteria
                )
            elif output_type == "multimodal":
                optimized_output = await self.multimodal_synthesizer.optimize(
                    original_output, optimization_criteria
                )
            
            # Update quality score
            new_quality_score = self._calculate_quality_by_type(optimized_output, output_type)
            self.current_state.output_quality_scores[output_id] = new_quality_score
            
            return {
                "optimized_output": optimized_output,
                "original_output": original_output,
                "output_type": output_type,
                "quality_improvement": new_quality_score - self.current_state.output_quality_scores.get(f"original_{output_id}", 0.0)
            }
            
        except Exception as e:
            self.logger.error(f"Output optimization failed: {e}")
            return {"error": str(e)}
    
    async def get_output_summary(self) -> Dict[str, Any]:
        """Get summary of all generated outputs"""
        if not self.integration_active:
            return {"error": "Bridge not initialized"}
        
        try:
            # Calculate overall generation quality
            if self.current_state.output_quality_scores:
                self.generation_quality = np.mean(list(self.current_state.output_quality_scores.values()))
            
            return {
                "total_outputs": {
                    "text": len(self.current_state.generated_texts),
                    "visual": len(self.current_state.generated_visuals),
                    "action": len(self.current_state.planned_actions),
                    "multimodal": len(self.current_state.multimodal_outputs)
                },
                "average_quality": self.generation_quality,
                "synthesis_coherence": self.synthesis_coherence,
                "generation_pipeline": self.current_state.generation_pipeline[-10:],  # Last 10 operations
                "quality_scores": self.current_state.output_quality_scores
            }
            
        except Exception as e:
            self.logger.error(f"Output summary generation failed: {e}")
            return {"error": str(e)}
    
    def _calculate_text_quality(self, text_result: Any) -> float:
        """Calculate text quality score"""
        try:
            if hasattr(text_result, 'quality_score'):
                return text_result.quality_score
            elif isinstance(text_result, dict) and 'quality_score' in text_result:
                return text_result['quality_score']
            return 0.8
        except:
            return 0.8
    
    def _calculate_visual_quality(self, visual_result: Any) -> float:
        """Calculate visual quality score"""
        try:
            if hasattr(visual_result, 'quality_score'):
                return visual_result.quality_score
            elif isinstance(visual_result, dict) and 'quality_score' in visual_result:
                return visual_result['quality_score']
            return 0.75
        except:
            return 0.75
    
    def _calculate_action_quality(self, action_result: Any) -> float:
        """Calculate action quality score"""
        try:
            if hasattr(action_result, 'feasibility_score'):
                return action_result.feasibility_score
            elif isinstance(action_result, dict) and 'feasibility_score' in action_result:
                return action_result['feasibility_score']
            return 0.85
        except:
            return 0.85
    
    def _calculate_synthesis_coherence(self, synthesis_result: Any) -> float:
        """Calculate synthesis coherence score"""
        try:
            if hasattr(synthesis_result, 'coherence_score'):
                return synthesis_result.coherence_score
            elif isinstance(synthesis_result, dict) and 'coherence_score' in synthesis_result:
                return synthesis_result['coherence_score']
            return 0.8
        except:
            return 0.8
    
    def _calculate_quality_by_type(self, output: Any, output_type: str) -> float:
        """Calculate quality score based on output type"""
        if output_type == "text":
            return self._calculate_text_quality(output)
        elif output_type == "visual":
            return self._calculate_visual_quality(output)
        elif output_type == "action":
            return self._calculate_action_quality(output)
        else:
            return 0.8
    
    def get_bridge_status(self) -> Dict[str, Any]:
        """Get current bridge status and metrics"""
        return {
            "integration_active": self.integration_active,
            "has_output_generation": HAS_OUTPUT_GENERATION,
            "generation_quality": self.generation_quality,
            "synthesis_coherence": self.synthesis_coherence,
            "output_latency": self.output_latency,
            "current_state": {
                "generated_texts_count": len(self.current_state.generated_texts) if self.current_state else 0,
                "generated_visuals_count": len(self.current_state.generated_visuals) if self.current_state else 0,
                "planned_actions_count": len(self.current_state.planned_actions) if self.current_state else 0,
                "multimodal_outputs_count": len(self.current_state.multimodal_outputs) if self.current_state else 0,
                "generation_pipeline_length": len(self.current_state.generation_pipeline) if self.current_state else 0
            }
        }
    
    async def shutdown_bridge(self):
        """Shutdown the output generation bridge"""
        self.integration_active = False
        self.logger.info("Output generation bridge shutdown complete")

# Bridge initialization function
async def initialize_output_generation_bridge(config: Optional[Dict] = None) -> OutputGenerationBridge:
    """Initialize and return an output generation bridge instance"""
    bridge = OutputGenerationBridge(config)
    await bridge.initialize_bridge()
    return bridge
