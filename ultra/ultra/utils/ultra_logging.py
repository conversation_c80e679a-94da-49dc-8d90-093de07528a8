"""
Logging utilities for the ULTRA system.

This module provides functions for setting up logging for the ULTRA system.
"""

import os
import logging
import logging.config
import yaml
import time

# Default logging configuration file path
DEFAULT_LOGGING_CONFIG_PATH = os.path.join(
    os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))),
    'config', 'logging_config.yaml'
)

def setup_logging(config_path=None, default_level=logging.INFO):
    """
    Set up logging configuration from a YAML file.
    
    Args:
        config_path (str, optional): Path to logging configuration file.
                                      If None, use default.
        default_level (int, optional): Default logging level if configuration
                                        file cannot be loaded.
    
    Returns:
        logger: Root logger
    """
    if config_path is None:
        config_path = DEFAULT_LOGGING_CONFIG_PATH
    
    if os.path.exists(config_path):
        with open(config_path, 'rt') as f:
            try:
                config = yaml.safe_load(f.read())
                
                # Ensure logs directory exists
                for handler in config.get('handlers', {}).values():
                    if 'filename' in handler:
                        log_dir = os.path.dirname(handler['filename'])
                        os.makedirs(log_dir, exist_ok=True)
                
                logging.config.dictConfig(config)
                logger = logging.getLogger()
                logger.info("Logging configuration loaded from %s", config_path)
                return logger
            except Exception as e:
                print(f"Error loading logging configuration: {e}")
                print("Using default logging configuration")
                logging.basicConfig(level=default_level)
                return logging.getLogger()
    else:
        print(f"Logging configuration file not found at {config_path}")
        print("Using default logging configuration")
        logging.basicConfig(level=default_level)
        return logging.getLogger()

class TimingLogger:
    """
    Utility class for timing and logging execution times.
    
    Example:
        with TimingLogger("Function X"):
            # Code to time
            function_x()
    """
    
    def __init__(self, name, logger=None):
        """
        Initialize the TimingLogger.
        
        Args:
            name (str): Name of the operation being timed
            logger (logging.Logger, optional): Logger to use. 
                If None, create a new logger.
        """
        self.name = name
        self.logger = logger or logging.getLogger(__name__)
    
    def __enter__(self):
        """Start timing."""
        self.start_time = time.time()
        self.logger.debug(f"Starting {self.name}")
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """End timing and log the duration."""
        duration = time.time() - self.start_time
        
        if exc_type is not None:
            self.logger.error(f"{self.name} failed after {duration:.4f}s: {exc_val}")
        else:
            self.logger.debug(f"{self.name} completed in {duration:.4f}s")

def get_module_logger(module_name):
    """
    Get a logger for a specific module.
    
    Args:
        module_name (str): Name of the module
    
    Returns:
        logging.Logger: Logger for the module
    """
    return logging.getLogger(f"ultra.{module_name}")

def get_logger(name=None):
    """
    Get a logger instance.
    
    Args:
        name (str, optional): Name for the logger. If None, returns root logger.
    
    Returns:
        logging.Logger: Logger instance
    """
    if name is None:
        return logging.getLogger()
    return logging.getLogger(name)