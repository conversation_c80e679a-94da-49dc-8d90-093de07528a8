"""
Ultra System Utilities Module

This module provides comprehensive utilities for the ULTRA (Ultimate Learning & Thought Reasoning Architecture) system.
It implements production-grade utilities for logging, visualization, monitoring, mathematical operations,
signal processing, optimization, device management, and performance profiling.

The utilities are designed to support all eight core subsystems of ULTRA:
1. Core Neural Architecture
2. Hyper-Dimensional Transformer  
3. Diffusion-Based Reasoning
4. Meta-Cognitive System
5. Neuromorphic Processing Layer
6. Emergent Consciousness Lattice
7. Neuro-Symbolic Integration
8. Self-Evolution System

All implementations are based on the mathematical foundations and equations specified in the ULTRA documentation.
"""

"""
ULTRA System Utilities Module
"""

import os
import sys
import time
import psutil
import threading
import multiprocessing
import functools
import warnings
import traceback
import gc
import inspect
import json
import pickle
import hashlib
import uuid
import socket
import shutil
import tempfile
import contextlib
import concurrent.futures
from typing import (
    Any, Dict, List, Optional, Union, Tuple, Callable, Iterator, 
    Type, TypeVar, Generic, Protocol, runtime_checkable, get_type_hints
)
from dataclasses import dataclass, field, asdict
from collections import deque, defaultdict, OrderedDict, Counter
from pathlib import Path
from datetime import datetime, timedelta
from enum import Enum, auto
from abc import ABC, abstractmethod
from functools import lru_cache, wraps, partial
from contextlib import contextmanager, nullcontext

import numpy as np
import scipy
import scipy.signal
import scipy.optimize
import scipy.stats
import scipy.spatial
import scipy.sparse
import scipy.linalg
import matplotlib.pyplot as plt
import matplotlib.animation as animation
from matplotlib.patches import Circle, Rectangle, Polygon
import seaborn as sns
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import pandas as pd
import networkx as nx
from sklearn.metrics import (
    accuracy_score, precision_score, recall_score, f1_score,
    confusion_matrix, classification_report, roc_auc_score,
    mean_squared_error, mean_absolute_error, r2_score
)
from sklearn.decomposition import PCA, FastICA, TruncatedSVD
from sklearn.manifold import TSNE
try:
    import umap
    UMAP = umap.UMAP
except ImportError:
    try:
        from umap import UMAP
    except ImportError:
        print("Warning: UMAP not available. Install with: pip install umap-learn")
        UMAP = None

from sklearn.cluster import KMeans, DBSCAN, SpectralClustering
from sklearn.preprocessing import StandardScaler, MinMaxScaler, RobustScaler
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.distributed as dist
from torch.profiler import profile, record_function, ProfilerActivity
import torch.multiprocessing as mp

# Import configuration - FIXED WITH COMPLETE FALLBACK
try:
    from .config import get_config, ConfigEnvironment, SystemConfig
except ImportError:
    try:
        from ultra.config import get_config, ConfigEnvironment, SystemConfig
    except ImportError:
        try:
            # Try absolute path
            import sys
            import os
            sys.path.append(os.path.dirname(os.path.dirname(__file__)))
            from config import get_config, ConfigEnvironment, SystemConfig
        except ImportError:
            # Create comprehensive fallback
            print("Warning: Config module not available, using comprehensive fallback")
            
            class ConfigEnvironment:
                DEVELOPMENT = "development"
                PRODUCTION = "production" 
                TESTING = "testing"
            
            class FallbackConfig:
                """Complete fallback config with all expected attributes"""
                def __init__(self):
                    # Basic system config
                    self.version = "1.0.0"
                    self.environment = ConfigEnvironment.DEVELOPMENT
                    self.device = "cpu"
                    self.precision = "float32"
                    self.random_seed = 42
                    self.log_level = "INFO"
                    
                    # System-wide parameters
                    self.max_memory_usage = "8GB"
                    self.distributed_training = False
                    self.checkpoint_interval = 1000
                    self.checkpoint_dir = "checkpoints"
                    self.monitoring_interval = 100
                    self.profiling_enabled = False
                    
                    # Core neural
                    self.core_neural = type('CoreNeural', (), {
                        'enabled': True,
                        'neuron_model': 'LIF',
                        'network_size': (100, 100, 100),
                        'excitatory_ratio': 0.8
                    })()
                    
                    # Hyper transformer
                    self.hyper_transformer = type('HyperTransformer', (), {
                        'enabled': True,
                        'transformer_params': type('TransformerParams', (), {
                            'd_model': 512,
                            'n_heads': 8,
                            'max_seq_length': 1024,
                            'dropout': 0.1
                        })()
                    })()
                    
                    # Other subsystems
                    self.diffusion_reasoning = type('DiffusionReasoning', (), {'enabled': True})()
                    self.meta_cognitive = type('MetaCognitive', (), {'enabled': True})()
                    self.neuromorphic_processing = type('NeuromorphicProcessing', (), {'enabled': True})()
                    self.emergent_consciousness = type('EmergentConsciousness', (), {'enabled': True})()
                    self.neuro_symbolic = type('NeuroSymbolic', (), {'enabled': True})()
                    self.self_evolution = type('SelfEvolution', (), {'enabled': True})()
            
            def get_config():
                return FallbackConfig()
            
            SystemConfig = FallbackConfig

# Configure basic Python logging (not our custom logging)
import logging as python_logging
python_logging.basicConfig(
    level=python_logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = python_logging.getLogger(__name__)



# Constants
EPSILON = 1e-8
PI = np.pi
E = np.e
GOLDEN_RATIO = (1 + np.sqrt(5)) / 2
EULER_GAMMA = 0.5772156649015329

T = TypeVar('T')
F = TypeVar('F', bound=Callable)

# ============================================================================
# Core Utility Classes and Enums
# ============================================================================

class Priority(Enum):
    """Priority levels for logging and monitoring."""
    CRITICAL = 50
    ERROR = 40
    WARNING = 30
    INFO = 20
    DEBUG = 10

class DeviceType(Enum):
    """Supported device types."""
    CPU = "cpu"
    CUDA = "cuda"
    TPU = "tpu"
    NEUROMORPHIC = "neuromorphic"

class MemoryUnit(Enum):
    """Memory units for memory management."""
    BYTES = 1
    KB = 1024
    MB = 1024**2
    GB = 1024**3
    TB = 1024**4

class SignalType(Enum):
    """Types of neural signals."""
    SPIKE_TRAIN = "spike_train"
    CONTINUOUS = "continuous"
    EVENT_BASED = "event_based"
    OSCILLATORY = "oscillatory"

@runtime_checkable
class Monitorable(Protocol):
    """Protocol for objects that can be monitored."""
    def get_metrics(self) -> Dict[str, Any]:
        """Return current metrics."""
        ...

@runtime_checkable
class Configurable(Protocol):
    """Protocol for objects that can be configured."""
    def update_config(self, config: Dict[str, Any]) -> None:
        """Update configuration."""
        ...

# ============================================================================
# Mathematical Utilities
# ============================================================================

class MathUtils:
    """Mathematical utilities for ULTRA system computations."""
    
    @staticmethod
    def sigmoid(x: Union[float, np.ndarray], 
               temperature: float = 1.0, 
               offset: float = 0.0) -> Union[float, np.ndarray]:
        """
        Compute sigmoid function with temperature and offset.
        
        σ(x) = 1 / (1 + exp(-(x - offset) / temperature))
        """
        safe_x = np.clip((x - offset) / temperature, -500, 500)  # Prevent overflow
        return 1.0 / (1.0 + np.exp(-safe_x))
    
    @staticmethod
    def tanh_derivative(x: Union[float, np.ndarray]) -> Union[float, np.ndarray]:
        """Compute derivative of tanh function: d/dx tanh(x) = 1 - tanh²(x)"""
        tanh_x = np.tanh(x)
        return 1.0 - tanh_x**2
    
    @staticmethod
    def gaussian(x: Union[float, np.ndarray], 
                mu: float = 0.0, 
                sigma: float = 1.0) -> Union[float, np.ndarray]:
        """
        Compute Gaussian/normal distribution.
        
        N(x; μ, σ) = (1 / (σ√(2π))) * exp(-0.5 * ((x - μ) / σ)²)
        """
        coefficient = 1.0 / (sigma * np.sqrt(2 * PI))
        exponent = -0.5 * ((x - mu) / sigma)**2
        return coefficient * np.exp(exponent)
    
    @staticmethod
    def gabor_filter(x: np.ndarray, y: np.ndarray,
                    theta: float, sigma: float, frequency: float,
                    phase: float = 0.0) -> np.ndarray:
        """
        Compute 2D Gabor filter for visual processing.
        
        G(x,y) = exp(-(x'² + γ²y'²) / (2σ²)) * cos(2πfx' + φ)
        where x' = x*cos(θ) + y*sin(θ), y' = -x*sin(θ) + y*cos(θ)
        """
        x_rot = x * np.cos(theta) + y * np.sin(theta)
        y_rot = -x * np.sin(theta) + y * np.cos(theta)
        
        gaussian_term = np.exp(-(x_rot**2 + y_rot**2) / (2 * sigma**2))
        sinusoidal_term = np.cos(2 * PI * frequency * x_rot + phase)
        
        return gaussian_term * sinusoidal_term
    
    @staticmethod
    def mutual_information(x: np.ndarray, y: np.ndarray, bins: int = 50) -> float:
        """
        Compute mutual information I(X;Y) = H(X) + H(Y) - H(X,Y)
        """
        # Discretize continuous variables
        x_disc = np.digitize(x, np.histogram_bin_edges(x, bins=bins)[:-1])
        y_disc = np.digitize(y, np.histogram_bin_edges(y, bins=bins)[:-1])
        
        # Compute joint histogram
        joint_hist = np.histogram2d(x_disc, y_disc, bins=bins)[0]
        joint_hist = joint_hist / np.sum(joint_hist)  # Normalize
        
        # Compute marginal histograms
        px = np.sum(joint_hist, axis=1)
        py = np.sum(joint_hist, axis=0)
        
        # Compute entropies
        h_x = -np.sum(px * np.log2(px + EPSILON))
        h_y = -np.sum(py * np.log2(py + EPSILON))
        h_xy = -np.sum(joint_hist * np.log2(joint_hist + EPSILON))
        
        return h_x + h_y - h_xy
    
    @staticmethod
    def integrated_information_phi(joint_dist: np.ndarray, 
                                  marginal_dist: np.ndarray) -> float:
        """
        Compute integrated information Φ measure.
        
        Φ = min_P D(p(X) || p(X₁)p(X₂))
        where D is KL divergence
        """
        # Ensure distributions are normalized
        joint_dist = joint_dist / np.sum(joint_dist)
        marginal_dist = marginal_dist / np.sum(marginal_dist)
        
        # Compute KL divergence
        kl_div = np.sum(joint_dist * np.log2((joint_dist + EPSILON) / (marginal_dist + EPSILON)))
        
        return max(0.0, kl_div)  # Φ cannot be negative
    
    @staticmethod
    def stdp_weight_change(dt: Union[float, np.ndarray],
                          a_plus: float = 0.005,
                          a_minus: float = 0.0025,
                          tau_plus: float = 20.0,
                          tau_minus: float = 20.0) -> Union[float, np.ndarray]:
        """
        Compute STDP weight change based on spike timing difference.
        
        Δw = A⁺ * exp(-Δt/τ⁺) if Δt > 0 (LTP)
        Δw = -A⁻ * exp(Δt/τ⁻) if Δt < 0 (LTD)
        """
        dw = np.zeros_like(dt)
        
        # Long-term potentiation (LTP) for positive timing differences
        ltp_mask = dt > 0
        dw[ltp_mask] = a_plus * np.exp(-dt[ltp_mask] / tau_plus)
        
        # Long-term depression (LTD) for negative timing differences
        ltd_mask = dt < 0
        dw[ltd_mask] = -a_minus * np.exp(dt[ltd_mask] / tau_minus)
        
        return dw
    
    @staticmethod
    def diffusion_forward_process(x0: np.ndarray, 
                                 t: int, 
                                 beta_schedule: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        Forward diffusion process for conceptual diffusion reasoning.
        
        q(x_t | x_0) = N(x_t; √ᾱ_t * x_0, (1 - ᾱ_t) * I)
        """
        alpha_bar_t = np.prod(1 - beta_schedule[:t+1])
        
        mean = np.sqrt(alpha_bar_t) * x0
        variance = 1 - alpha_bar_t
        
        noise = np.random.randn(*x0.shape)
        x_t = mean + np.sqrt(variance) * noise
        
        return x_t, noise
    
    @staticmethod
    def attention_weights(query: np.ndarray, 
                         key: np.ndarray,
                         mask: Optional[np.ndarray] = None,
                         temperature: float = 1.0) -> np.ndarray:
        """
        Compute attention weights with optional mask and temperature.
        
        Attention(Q,K) = softmax(QK^T / √d_k · M)
        """
        d_k = query.shape[-1]
        scores = np.dot(query, key.T) / (np.sqrt(d_k) * temperature)
        
        if mask is not None:
            scores = scores + mask  # Add mask (use -inf for masked positions)
            
        # Stable softmax implementation
        scores_max = np.max(scores, axis=-1, keepdims=True)
        scores_exp = np.exp(scores - scores_max)
        attention_weights = scores_exp / np.sum(scores_exp, axis=-1, keepdims=True)
        
        return attention_weights
    
    @staticmethod
    def neuromodulator_dynamics(m_t: float, 
                               input_signal: float,
                               tau_m: float,
                               dt: float = 1.0) -> float:
        """
        Update neuromodulator concentration using first-order dynamics.
        
        dm/dt = (-m(t) + I(t)) / τ_m
        """
        dm_dt = (-m_t + input_signal) / tau_m
        return m_t + dm_dt * dt
    
    @staticmethod
    def oscillator_phase_update(phi: float,
                               phi_neighbors: np.ndarray,
                               omega: float,
                               kappa: float,
                               dt: float = 1.0) -> float:
        """
        Update oscillator phase with coupling.
        
        dφ/dt = ω + κ * Σ sin(φ_j - φ)
        """
        coupling_term = kappa * np.sum(np.sin(phi_neighbors - phi))
        dphi_dt = omega + coupling_term
        return (phi + dphi_dt * dt) % (2 * PI)
    
    @staticmethod
    def memristor_resistance_update(w: float,
                                   current: float,
                                   k: float = 1e-4,
                                   dt: float = 1.0) -> float:
        """
        Update memristor state using simplified Strukov model.
        
        dw/dt = k * i(t) * f(w)
        """
        # Window function to keep w in bounds [0, 1]
        if w <= 0:
            f_w = 1.0 if current > 0 else 0.0
        elif w >= 1:
            f_w = -1.0 if current < 0 else 0.0
        else:
            f_w = 1.0 - 2 * w  # Linear window function
            
        dw_dt = k * current * f_w
        new_w = w + dw_dt * dt
        
        return np.clip(new_w, 0.0, 1.0)
    
    @staticmethod
    def euclidean_distance_matrix(X: np.ndarray, Y: Optional[np.ndarray] = None) -> np.ndarray:
        """Compute pairwise Euclidean distances efficiently using broadcasting."""
        if Y is None:
            Y = X
            
        # Use broadcasting for efficient computation
        X_sqnorms = np.sum(X**2, axis=1, keepdims=True)
        Y_sqnorms = np.sum(Y**2, axis=1)
        XY = np.dot(X, Y.T)
        
        distances = np.sqrt(np.maximum(0, X_sqnorms - 2*XY + Y_sqnorms))
        return distances
    
    @staticmethod
    def cosine_similarity_matrix(X: np.ndarray, Y: Optional[np.ndarray] = None) -> np.ndarray:
        """Compute pairwise cosine similarities."""
        if Y is None:
            Y = X
            
        # Normalize vectors
        X_norm = X / (np.linalg.norm(X, axis=1, keepdims=True) + EPSILON)
        Y_norm = Y / (np.linalg.norm(Y, axis=1, keepdims=True) + EPSILON)
        
        return np.dot(X_norm, Y_norm.T)

# ============================================================================
# Signal Processing Utilities
# ============================================================================

class SignalProcessor:
    """Signal processing utilities for neural signals and oscillations."""
    
    def __init__(self, sampling_rate: float = 1000.0):
        """Initialize with sampling rate in Hz."""
        self.sampling_rate = sampling_rate
        self.dt = 1.0 / sampling_rate
    
    def generate_oscillation(self, 
                           frequency: float,
                           amplitude: float = 1.0,
                           phase: float = 0.0,
                           duration: float = 1.0,
                           noise_std: float = 0.0) -> Tuple[np.ndarray, np.ndarray]:
        """Generate oscillatory signal."""
        t = np.arange(0, duration, self.dt)
        signal = amplitude * np.sin(2 * PI * frequency * t + phase)
        
        if noise_std > 0:
            noise = np.random.normal(0, noise_std, len(signal))
            signal += noise
            
        return t, signal
    
    def phase_amplitude_coupling(self,
                               low_freq_signal: np.ndarray,
                               high_freq_signal: np.ndarray,
                               method: str = "modulation_index") -> float:
        """
        Compute phase-amplitude coupling between two signals.
        """
        if method == "modulation_index":
            # Extract phase of low frequency signal
            analytic_low = scipy.signal.hilbert(low_freq_signal)
            phase_low = np.angle(analytic_low)
            
            # Extract amplitude of high frequency signal
            analytic_high = scipy.signal.hilbert(high_freq_signal)
            amplitude_high = np.abs(analytic_high)
            
            # Compute modulation index
            n_bins = 18
            phase_bins = np.linspace(-PI, PI, n_bins + 1)
            
            # Bin amplitude by phase
            amplitude_by_phase = np.zeros(n_bins)
            for i in range(n_bins):
                mask = (phase_low >= phase_bins[i]) & (phase_low < phase_bins[i + 1])
                if np.any(mask):
                    amplitude_by_phase[i] = np.mean(amplitude_high[mask])
            
            # Normalize to create probability distribution
            p = amplitude_by_phase / np.sum(amplitude_by_phase)
            
            # Compute entropy
            entropy = -np.sum(p * np.log(p + EPSILON))
            
            # Modulation index (normalized by maximum possible entropy)
            max_entropy = np.log(n_bins)
            modulation_index = (max_entropy - entropy) / max_entropy
            
            return modulation_index
        
        else:
            raise ValueError(f"Unknown PAC method: {method}")
    
    def spike_train_to_rate(self,
                           spike_times: np.ndarray,
                           time_window: float = 0.05,
                           total_time: Optional[float] = None) -> Tuple[np.ndarray, np.ndarray]:
        """Convert spike train to instantaneous firing rate."""
        if total_time is None:
            total_time = np.max(spike_times) + time_window
            
        t_bins = np.arange(0, total_time, time_window)
        spike_counts, _ = np.histogram(spike_times, bins=t_bins)
        firing_rate = spike_counts / time_window  # spikes per second
        
        t_centers = t_bins[:-1] + time_window / 2
        
        return t_centers, firing_rate
    
    def coherence_analysis(self,
                          signal1: np.ndarray,
                          signal2: np.ndarray,
                          nperseg: Optional[int] = None) -> Tuple[np.ndarray, np.ndarray]:
        """Compute coherence between two signals."""
        if nperseg is None:
            nperseg = min(len(signal1) // 8, 256)
            
        freqs, coherence = scipy.signal.coherence(
            signal1, signal2,
            fs=self.sampling_rate,
            nperseg=nperseg
        )
        
        return freqs, coherence
    
    def spectral_power(self,
                      signal: np.ndarray,
                      frequency_bands: Dict[str, Tuple[float, float]]) -> Dict[str, float]:
        """Compute power in specific frequency bands."""
        freqs, psd = scipy.signal.welch(signal, fs=self.sampling_rate)
        
        band_powers = {}
        for band_name, (low_freq, high_freq) in frequency_bands.items():
            band_mask = (freqs >= low_freq) & (freqs <= high_freq)
            band_power = np.trapz(psd[band_mask], freqs[band_mask])
            band_powers[band_name] = band_power
            
        return band_powers
    
    def event_related_potential(self,
                               signals: np.ndarray,
                               event_times: np.ndarray,
                               pre_time: float = 0.2,
                               post_time: float = 0.8) -> np.ndarray:
        """Compute event-related potential (ERP)."""
        pre_samples = int(pre_time * self.sampling_rate)
        post_samples = int(post_time * self.sampling_rate)
        total_samples = pre_samples + post_samples
        
        erps = []
        for event_time in event_times:
            event_sample = int(event_time * self.sampling_rate)
            start_idx = event_sample - pre_samples
            end_idx = event_sample + post_samples
            
            if start_idx >= 0 and end_idx < len(signals):
                erp = signals[start_idx:end_idx]
                erps.append(erp)
        
        if erps:
            return np.mean(erps, axis=0)
        else:
            return np.zeros(total_samples)
    
    def cross_correlation(self,
                         signal1: np.ndarray,
                         signal2: np.ndarray,
                         max_lag: Optional[int] = None) -> Tuple[np.ndarray, np.ndarray]:
        """Compute cross-correlation between two signals."""
        if max_lag is None:
            max_lag = min(len(signal1), len(signal2)) // 4
            
        correlation = scipy.signal.correlate(signal1, signal2, mode='full')
        lags = scipy.signal.correlation_lags(len(signal1), len(signal2), mode='full')
        
        # Limit to maximum lag
        center = len(correlation) // 2
        start_idx = max(0, center - max_lag)
        end_idx = min(len(correlation), center + max_lag + 1)
        
        return lags[start_idx:end_idx], correlation[start_idx:end_idx]

# ============================================================================
# Logging Utilities
# ============================================================================

class UltraLogger:
    """Enhanced logging system for ULTRA with structured logging and performance tracking."""
    
    def __init__(self, name: str, level: str = "INFO"):
        """Initialize logger with specified name and level."""
        self.name = name
        self.logger = logging.getLogger(name)
        self.logger.setLevel(getattr(logging, level.upper()))
        
        # Performance tracking
        self._performance_data = deque(maxlen=10000)
        self._metrics_lock = threading.Lock()
        
        # Setup structured logging handler
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
            )
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
    
    def log_with_context(self, 
                        level: str, 
                        message: str, 
                        context: Optional[Dict[str, Any]] = None,
                        performance_data: Optional[Dict[str, float]] = None) -> None:
        """Log message with additional context and performance data."""
        
        # Format message with context
        if context:
            context_str = " | ".join([f"{k}={v}" for k, v in context.items()])
            full_message = f"{message} | Context: {context_str}"
        else:
            full_message = message
            
        # Log the message
        log_func = getattr(self.logger, level.lower())
        log_func(full_message)
        
        # Store performance data
        if performance_data:
            with self._metrics_lock:
                perf_entry = {
                    'timestamp': time.time(),
                    'level': level,
                    'message': message,
                    **performance_data
                }
                self._performance_data.append(perf_entry)
    
    def info(self, message: str, **kwargs) -> None:
        """Log info message."""
        self.log_with_context("INFO", message, **kwargs)
    
    def debug(self, message: str, **kwargs) -> None:
        """Log debug message."""
        self.log_with_context("DEBUG", message, **kwargs)
    
    def warning(self, message: str, **kwargs) -> None:
        """Log warning message."""
        self.log_with_context("WARNING", message, **kwargs)
    
    def error(self, message: str, **kwargs) -> None:
        """Log error message."""
        self.log_with_context("ERROR", message, **kwargs)
    
    def critical(self, message: str, **kwargs) -> None:
        """Log critical message."""
        self.log_with_context("CRITICAL", message, **kwargs)
    
    @contextmanager
    def timed_operation(self, operation_name: str):
        """Context manager for timing operations."""
        start_time = time.time()
        start_memory = psutil.Process().memory_info().rss
        
        try:
            yield
        finally:
            end_time = time.time()
            end_memory = psutil.Process().memory_info().rss
            
            duration = end_time - start_time
            memory_delta = end_memory - start_memory
            
            self.log_with_context(
                "INFO",
                f"Operation '{operation_name}' completed",
                performance_data={
                    'duration_seconds': duration,
                    'memory_delta_bytes': memory_delta,
                    'operation': operation_name
                }
            )
    
    def get_performance_summary(self, last_n_minutes: int = 60) -> Dict[str, Any]:
        """Get performance summary for the last N minutes."""
        cutoff_time = time.time() - (last_n_minutes * 60)
        
        with self._metrics_lock:
            recent_data = [
                entry for entry in self._performance_data 
                if entry['timestamp'] > cutoff_time
            ]
        
        if not recent_data:
            return {}
        
        # Compute statistics
        durations = [entry.get('duration_seconds', 0) for entry in recent_data]
        memory_deltas = [entry.get('memory_delta_bytes', 0) for entry in recent_data]
        
        return {
            'total_operations': len(recent_data),
            'avg_duration': np.mean(durations) if durations else 0,
            'max_duration': np.max(durations) if durations else 0,
            'total_memory_allocated': sum(d for d in memory_deltas if d > 0),
            'operations_by_level': Counter(entry['level'] for entry in recent_data)
        }

# ============================================================================
# Performance Monitoring
# ============================================================================

class SystemMonitor:
    """Comprehensive system monitoring for ULTRA components."""
    
    def __init__(self, update_interval: float = 1.0):
        """Initialize system monitor."""
        self.update_interval = update_interval
        self.metrics = {}
        self._monitoring = False
        self._monitor_thread = None
        self._metrics_lock = threading.Lock()
        self._callbacks = []
        
        # Initialize metrics storage
        self._initialize_metrics()
    
    def _initialize_metrics(self) -> None:
        """Initialize metrics storage."""
        self.metrics = {
            'system': {
                'cpu_percent': deque(maxlen=1000),
                'memory_percent': deque(maxlen=1000),
                'disk_usage': deque(maxlen=1000),
                'network_io': deque(maxlen=1000),
                'timestamps': deque(maxlen=1000)
            },
            'gpu': {
                'utilization': deque(maxlen=1000),
                'memory_used': deque(maxlen=1000),
                'temperature': deque(maxlen=1000),
                'timestamps': deque(maxlen=1000)
            } if torch.cuda.is_available() else {},
            'neural': {
                'spike_rates': deque(maxlen=1000),
                'synaptic_updates': deque(maxlen=1000),
                'plasticity_changes': deque(maxlen=1000),
                'timestamps': deque(maxlen=1000)
            },
            'cognitive': {
                'reasoning_paths': deque(maxlen=1000),
                'attention_entropy': deque(maxlen=1000),
                'working_memory_load': deque(maxlen=1000),
                'timestamps': deque(maxlen=1000)
            }
        }
    
    def start_monitoring(self) -> None:
        """Start background monitoring."""
        if self._monitoring:
            return
            
        self._monitoring = True
        self._monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self._monitor_thread.start()
        logger.info("System monitoring started")
    
    def stop_monitoring(self) -> None:
        """Stop background monitoring."""
        self._monitoring = False
        if self._monitor_thread:
            self._monitor_thread.join(timeout=5.0)
        logger.info("System monitoring stopped")
    
    def _monitor_loop(self) -> None:
        """Main monitoring loop."""
        while self._monitoring:
            try:
                self._collect_system_metrics()
                if torch.cuda.is_available():
                    self._collect_gpu_metrics()
                    
                # Trigger callbacks
                for callback in self._callbacks:
                    try:
                        callback(self.get_current_metrics())
                    except Exception as e:
                        logger.error(f"Monitor callback error: {e}")
                        
                time.sleep(self.update_interval)
                
            except Exception as e:
                logger.error(f"Monitoring error: {e}")
                time.sleep(self.update_interval)
    
    def _collect_system_metrics(self) -> None:
        """Collect system-level metrics."""
        timestamp = time.time()
        
        # CPU and memory
        cpu_percent = psutil.cpu_percent()
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        # Network I/O
        net_io = psutil.net_io_counters()
        net_total = net_io.bytes_sent + net_io.bytes_recv
        
        with self._metrics_lock:
            self.metrics['system']['cpu_percent'].append(cpu_percent)
            self.metrics['system']['memory_percent'].append(memory.percent)
            self.metrics['system']['disk_usage'].append(disk.percent)
            self.metrics['system']['network_io'].append(net_total)
            self.metrics['system']['timestamps'].append(timestamp)
    
    def _collect_gpu_metrics(self) -> None:
        """Collect GPU metrics if available."""
        if not torch.cuda.is_available():
            return
            
        timestamp = time.time()
        
        try:
            # Get GPU utilization and memory
            gpu_util = torch.cuda.utilization()
            memory_allocated = torch.cuda.memory_allocated() / torch.cuda.max_memory_allocated()
            
            # Temperature (if available)
            try:
                import pynvml
                pynvml.nvmlInit()
                handle = pynvml.nvmlDeviceGetHandleByIndex(0)
                temperature = pynvml.nvmlDeviceGetTemperature(handle, pynvml.NVML_TEMPERATURE_GPU)
            except:
                temperature = 0.0
            
            with self._metrics_lock:
                self.metrics['gpu']['utilization'].append(gpu_util)
                self.metrics['gpu']['memory_used'].append(memory_allocated * 100)
                self.metrics['gpu']['temperature'].append(temperature)
                self.metrics['gpu']['timestamps'].append(timestamp)
                
        except Exception as e:
            logger.warning(f"GPU metrics collection failed: {e}")
    
    def add_neural_metrics(self, 
                          spike_rate: float,
                          synaptic_updates: int,
                          plasticity_changes: float) -> None:
        """Add neural-specific metrics."""
        timestamp = time.time()
        
        with self._metrics_lock:
            self.metrics['neural']['spike_rates'].append(spike_rate)
            self.metrics['neural']['synaptic_updates'].append(synaptic_updates)
            self.metrics['neural']['plasticity_changes'].append(plasticity_changes)
            self.metrics['neural']['timestamps'].append(timestamp)
    
    def add_cognitive_metrics(self,
                            reasoning_paths: int,
                            attention_entropy: float,
                            working_memory_load: float) -> None:
        """Add cognitive-specific metrics."""
        timestamp = time.time()
        
        with self._metrics_lock:
            self.metrics['cognitive']['reasoning_paths'].append(reasoning_paths)
            self.metrics['cognitive']['attention_entropy'].append(attention_entropy)
            self.metrics['cognitive']['working_memory_load'].append(working_memory_load)
            self.metrics['cognitive']['timestamps'].append(timestamp)
    
    def get_current_metrics(self) -> Dict[str, Any]:
        """Get current system metrics."""
        with self._metrics_lock:
            current = {}
            for category, metrics in self.metrics.items():
                if not metrics:
                    continue
                    
                current[category] = {}
                for metric_name, values in metrics.items():
                    if values and metric_name != 'timestamps':
                        current[category][metric_name] = values[-1]
                        
            return current
    
    def get_metrics_history(self, 
                          category: str,
                          last_n_points: int = 100) -> Dict[str, List]:
        """Get historical metrics for a category."""
        with self._metrics_lock:
            if category not in self.metrics:
                return {}
                
            history = {}
            for metric_name, values in self.metrics[category].items():
                history[metric_name] = list(values)[-last_n_points:]
                
            return history
    
    def add_callback(self, callback: Callable[[Dict[str, Any]], None]) -> None:
        """Add callback function to be called on each monitoring update."""
        self._callbacks.append(callback)
    
    def get_system_health_score(self) -> float:
        """Compute overall system health score (0-1)."""
        current = self.get_current_metrics()
        
        if not current:
            return 1.0
            
        health_factors = []
        
        # System health factors
        if 'system' in current:
            cpu_health = 1.0 - (current['system'].get('cpu_percent', 0) / 100.0)
            memory_health = 1.0 - (current['system'].get('memory_percent', 0) / 100.0)
            disk_health = 1.0 - (current['system'].get('disk_usage', 0) / 100.0)
            
            health_factors.extend([cpu_health, memory_health, disk_health])
        
        # GPU health factors
        if 'gpu' in current and current['gpu']:
            gpu_util_health = 1.0 - (current['gpu'].get('utilization', 0) / 100.0)
            gpu_memory_health = 1.0 - (current['gpu'].get('memory_used', 0) / 100.0)
            temp_health = 1.0 - max(0, (current['gpu'].get('temperature', 50) - 50) / 50.0)
            
            health_factors.extend([gpu_util_health, gpu_memory_health, temp_health])
        
        if health_factors:
            return max(0.0, min(1.0, np.mean(health_factors)))
        else:
            return 1.0

# ============================================================================
# Device Management
# ============================================================================

class DeviceManager:
    """Manage computing devices and resource allocation."""
    
    def __init__(self):
        """Initialize device manager."""
        self.available_devices = self._detect_devices()
        self.current_device = self._select_default_device()
        self.device_utilization = defaultdict(float)
        self._lock = threading.Lock()
    
    def _detect_devices(self) -> Dict[str, Dict[str, Any]]:
        """Detect available computing devices."""
        devices = {}
        
        # CPU
        devices['cpu'] = {
            'type': DeviceType.CPU,
            'cores': psutil.cpu_count(),
            'memory_gb': psutil.virtual_memory().total / (1024**3),
            'available': True
        }
        
        # CUDA GPUs
        if torch.cuda.is_available():
            for i in range(torch.cuda.device_count()):
                device_name = torch.cuda.get_device_name(i)
                memory_total = torch.cuda.get_device_properties(i).total_memory / (1024**3)
                
                devices[f'cuda:{i}'] = {
                    'type': DeviceType.CUDA,
                    'name': device_name,
                    'memory_gb': memory_total,
                    'compute_capability': torch.cuda.get_device_properties(i).major,
                    'available': True
                }
        
        # TPU (basic detection)
        try:
            import torch_xla
            import torch_xla.core.xla_model as xm
            devices['tpu'] = {
                'type': DeviceType.TPU,
                'available': True
            }
        except ImportError:
            pass
        
        return devices
    
    def _select_default_device(self) -> str:
        """Select the best available device."""
        config = get_config()
        preferred_device = config.device
        
        if preferred_device in self.available_devices:
            return preferred_device
        elif torch.cuda.is_available():
            return 'cuda:0'
        else:
            return 'cpu'
    
    def get_device(self, device_hint: Optional[str] = None) -> torch.device:
        """Get torch device object."""
        if device_hint and device_hint in self.available_devices:
            device_str = device_hint
        else:
            device_str = self.current_device
            
        return torch.device(device_str)
    
    def get_optimal_device_for_tensor(self, tensor_size_mb: float) -> str:
        """Select optimal device based on tensor size and current utilization."""
        with self._lock:
            best_device = self.current_device
            best_score = float('-inf')
            
            for device_name, device_info in self.available_devices.items():
                if not device_info['available']:
                    continue
                    
                # Calculate device score based on memory and utilization
                memory_gb = device_info.get('memory_gb', 1.0)
                utilization = self.device_utilization[device_name]
                
                # Check if tensor fits in memory (with safety margin)
                if tensor_size_mb / 1024 > memory_gb * 0.8:
                    continue
                    
                # Score = available_memory / utilization
                available_memory = memory_gb * (1.0 - utilization)
                score = available_memory / (utilization + 0.1)
                
                if score > best_score:
                    best_score = score
                    best_device = device_name
                    
            return best_device
    
    def update_utilization(self, device_name: str, utilization: float) -> None:
        """Update device utilization (0.0 to 1.0)."""
        with self._lock:
            self.device_utilization[device_name] = max(0.0, min(1.0, utilization))
    
    def get_device_info(self) -> Dict[str, Any]:
        """Get comprehensive device information."""
        info = {
            'current_device': self.current_device,
            'available_devices': self.available_devices.copy(),
            'utilization': dict(self.device_utilization)
        }
        
        # Add runtime device statistics
        if torch.cuda.is_available():
            for i in range(torch.cuda.device_count()):
                device_key = f'cuda:{i}'
                if device_key in info['available_devices']:
                    info['available_devices'][device_key].update({
                        'memory_allocated': torch.cuda.memory_allocated(i) / (1024**3),
                        'memory_reserved': torch.cuda.memory_reserved(i) / (1024**3),
                        'utilization_percent': torch.cuda.utilization(i) if hasattr(torch.cuda, 'utilization') else 0
                    })
        
        return info
    
    @contextmanager
    def device_context(self, device_name: str):
        """Context manager for temporary device switching."""
        old_device = self.current_device
        self.current_device = device_name
        
        try:
            yield torch.device(device_name)
        finally:
            self.current_device = old_device

# ============================================================================
# Memory Management
# ============================================================================

class MemoryManager:
    """Advanced memory management for ULTRA system."""
    
    def __init__(self, max_memory_gb: float = 16.0):
        """Initialize memory manager."""
        self.max_memory_bytes = max_memory_gb * (1024**3)
        self.current_usage = 0
        self.allocations = {}
        self._lock = threading.Lock()
        self._gc_threshold = 0.8  # Trigger GC at 80% usage
        
    def allocate(self, size_bytes: int, tag: str = "default") -> str:
        """Allocate memory with tracking."""
        allocation_id = str(uuid.uuid4())
        
        with self._lock:
            if self.current_usage + size_bytes > self.max_memory_bytes:
                self._garbage_collect()
                
                if self.current_usage + size_bytes > self.max_memory_bytes:
                    raise MemoryError(f"Cannot allocate {size_bytes} bytes. "
                                    f"Current usage: {self.current_usage}, "
                                    f"Max: {self.max_memory_bytes}")
            
            self.allocations[allocation_id] = {
                'size': size_bytes,
                'tag': tag,
                'timestamp': time.time(),
                'active': True
            }
            self.current_usage += size_bytes
            
        return allocation_id
    
    def deallocate(self, allocation_id: str) -> None:
        """Deallocate tracked memory."""
        with self._lock:
            if allocation_id in self.allocations:
                allocation = self.allocations[allocation_id]
                if allocation['active']:
                    self.current_usage -= allocation['size']
                    allocation['active'] = False
    
    def get_memory_stats(self) -> Dict[str, Any]:
        """Get memory usage statistics."""
        with self._lock:
            active_allocations = {
                aid: alloc for aid, alloc in self.allocations.items() 
                if alloc['active']
            }
            
            stats = {
                'total_allocated': self.current_usage,
                'max_memory': self.max_memory_bytes,
                'usage_percent': (self.current_usage / self.max_memory_bytes) * 100,
                'num_allocations': len(active_allocations),
                'allocations_by_tag': defaultdict(int)
            }
            
            for alloc in active_allocations.values():
                stats['allocations_by_tag'][alloc['tag']] += alloc['size']
                
            return stats
    
    def _garbage_collect(self) -> None:
        """Trigger garbage collection and cleanup."""
        # Python garbage collection
        collected = gc.collect()
        
        # PyTorch CUDA memory cleanup
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
            
        # Clean up old inactive allocations
        cutoff_time = time.time() - 3600  # 1 hour
        old_allocations = [
            aid for aid, alloc in self.allocations.items()
            if not alloc['active'] and alloc['timestamp'] < cutoff_time
        ]
        
        for aid in old_allocations:
            del self.allocations[aid]
            
        logger.info(f"Garbage collection: {collected} objects collected, "
                   f"{len(old_allocations)} old allocations cleaned")
    
    @contextmanager
    def managed_allocation(self, size_bytes: int, tag: str = "temp"):
        """Context manager for automatic memory deallocation."""
        allocation_id = self.allocate(size_bytes, tag)
        try:
            yield allocation_id
        finally:
            self.deallocate(allocation_id)
    
    def should_trigger_gc(self) -> bool:
        """Check if garbage collection should be triggered."""
        usage_ratio = self.current_usage / self.max_memory_bytes
        return usage_ratio > self._gc_threshold

# ============================================================================
# Visualization Utilities
# ============================================================================

class VisualizationManager:
    """Comprehensive visualization utilities for ULTRA system analysis."""
    
    def __init__(self, style: str = "seaborn-v0_8-darkgrid"):
        """Initialize visualization manager."""
        try:
            plt.style.use(style)
        except:
            plt.style.use('default')
            
        self.color_palette = sns.color_palette("husl", 12)
        self.figure_cache = {}
        
    def plot_neural_activity(self,
                           spike_times: np.ndarray,
                           neuron_ids: np.ndarray,
                           time_window: Tuple[float, float] = (0, 1),
                           figsize: Tuple[int, int] = (12, 8)) -> plt.Figure:
        """Plot neural spike raster."""
        fig, ax = plt.subplots(figsize=figsize)
        
        # Filter spikes in time window
        mask = (spike_times >= time_window[0]) & (spike_times <= time_window[1])
        filtered_times = spike_times[mask]
        filtered_ids = neuron_ids[mask]
        
        # Create raster plot
        ax.scatter(filtered_times, filtered_ids, s=1, alpha=0.7, c='black')
        
        ax.set_xlabel('Time (s)')
        ax.set_ylabel('Neuron ID')
        ax.set_title('Neural Spike Raster')
        ax.set_xlim(time_window)
        
        plt.tight_layout()
        return fig
    
    def plot_oscillations(self,
                         signals: Dict[str, np.ndarray],
                         time_axis: np.ndarray,
                         figsize: Tuple[int, int] = (12, 10)) -> plt.Figure:
        """Plot multiple oscillatory signals."""
        n_signals = len(signals)
        fig, axes = plt.subplots(n_signals, 1, figsize=figsize, sharex=True)
        
        if n_signals == 1:
            axes = [axes]
            
        for i, (name, signal) in enumerate(signals.items()):
            axes[i].plot(time_axis, signal, color=self.color_palette[i % len(self.color_palette)])
            axes[i].set_ylabel(f'{name} Amplitude')
            axes[i].grid(True, alpha=0.3)
        
        axes[-1].set_xlabel('Time (s)')
        axes[0].set_title('Neural Oscillations')
        
        plt.tight_layout()
        return fig
    
    def plot_attention_matrix(self,
                            attention_weights: np.ndarray,
                            token_labels: Optional[List[str]] = None,
                            figsize: Tuple[int, int] = (10, 8)) -> plt.Figure:
        """Visualize attention weight matrix."""
        fig, ax = plt.subplots(figsize=figsize)
        
        im = ax.imshow(attention_weights, cmap='Blues', aspect='auto')
        
        if token_labels:
            ax.set_xticks(range(len(token_labels)))
            ax.set_yticks(range(len(token_labels)))
            ax.set_xticklabels(token_labels, rotation=45, ha='right')
            ax.set_yticklabels(token_labels)
        
        ax.set_xlabel('Key Tokens')
        ax.set_ylabel('Query Tokens')
        ax.set_title('Attention Weight Matrix')
        
        plt.colorbar(im, ax=ax, label='Attention Weight')
        plt.tight_layout()
        return fig
    
    def plot_reasoning_tree(self,
                          tree_structure: Dict[str, Any],
                          figsize: Tuple[int, int] = (15, 10)) -> plt.Figure:
        """Visualize reasoning tree structure."""
        # Create networkx graph
        G = nx.DiGraph()
        
        def add_nodes_edges(node_data, parent_id=None):
            node_id = node_data.get('id', str(uuid.uuid4()))
            G.add_node(node_id, label=node_data.get('label', 'Node'))
            
            if parent_id:
                G.add_edge(parent_id, node_id)
                
            for child in node_data.get('children', []):
                add_nodes_edges(child, node_id)
        
        add_nodes_edges(tree_structure)
        
        # Create visualization
        fig, ax = plt.subplots(figsize=figsize)
        
        pos = nx.spring_layout(G, k=2, iterations=50)
        
        # Draw nodes
        nx.draw_networkx_nodes(G, pos, ax=ax, node_color='lightblue',
                              node_size=1000, alpha=0.7)
        
        # Draw edges
        nx.draw_networkx_edges(G, pos, ax=ax, edge_color='gray',
                              arrows=True, arrowsize=20, alpha=0.6)
        
        # Draw labels
        labels = nx.get_node_attributes(G, 'label')
        nx.draw_networkx_labels(G, pos, labels, ax=ax, font_size=8)
        
        ax.set_title('Reasoning Tree Structure')
        ax.axis('off')
        
        plt.tight_layout()
        return fig
    
    def plot_system_metrics(self,
                           metrics_history: Dict[str, List[float]],
                           time_axis: Optional[np.ndarray] = None,
                           figsize: Tuple[int, int] = (12, 8)) -> plt.Figure:
        """Plot system performance metrics over time."""
        n_metrics = len(metrics_history)
        if n_metrics == 0:
            return plt.figure(figsize=figsize)
            
        fig, axes = plt.subplots(n_metrics, 1, figsize=figsize, sharex=True)
        
        if n_metrics == 1:
            axes = [axes]
        
        if time_axis is None:
            max_len = max(len(values) for values in metrics_history.values())
            time_axis = np.arange(max_len)
        
        for i, (metric_name, values) in enumerate(metrics_history.items()):
            if len(values) > 0:
                t_vals = time_axis[:len(values)]
                axes[i].plot(t_vals, values, 
                           color=self.color_palette[i % len(self.color_palette)],
                           linewidth=2)
                axes[i].set_ylabel(metric_name.replace('_', ' ').title())
                axes[i].grid(True, alpha=0.3)
                
                # Add trend line
                if len(values) > 10:
                    z = np.polyfit(t_vals, values, 1)
                    p = np.poly1d(z)
                    axes[i].plot(t_vals, p(t_vals), '--', alpha=0.7,
                               color=self.color_palette[i % len(self.color_palette)])
        
        axes[-1].set_xlabel('Time')
        axes[0].set_title('System Performance Metrics')
        
        plt.tight_layout()
        return fig
    
    def plot_diffusion_trajectory(self,
                                trajectory: np.ndarray,
                                concept_labels: Optional[List[str]] = None,
                                figsize: Tuple[int, int] = (10, 8)) -> plt.Figure:
        """Visualize diffusion-based reasoning trajectory."""
        fig, ax = plt.subplots(figsize=figsize)
        
        # Use PCA to reduce dimensionality if needed
        if trajectory.shape[1] > 2:
            pca = PCA(n_components=2)
            trajectory_2d = pca.fit_transform(trajectory)
        else:
            trajectory_2d = trajectory
        
        # Plot trajectory
        ax.plot(trajectory_2d[:, 0], trajectory_2d[:, 1], 'o-', 
               alpha=0.7, markersize=8, linewidth=2)
        
        # Highlight start and end points
        ax.scatter(trajectory_2d[0, 0], trajectory_2d[0, 1], 
                  c='green', s=100, label='Start', zorder=5)
        ax.scatter(trajectory_2d[-1, 0], trajectory_2d[-1, 1], 
                  c='red', s=100, label='End', zorder=5)
        
        # Add annotations if provided
        if concept_labels:
            for i, label in enumerate(concept_labels[:len(trajectory_2d)]):
                ax.annotate(label, (trajectory_2d[i, 0], trajectory_2d[i, 1]),
                           xytext=(5, 5), textcoords='offset points',
                           fontsize=8, alpha=0.8)
        
        ax.set_xlabel('Principal Component 1')
        ax.set_ylabel('Principal Component 2')
        ax.set_title('Diffusion-Based Reasoning Trajectory')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        return fig
    
    def create_interactive_dashboard(self,
                                   data_sources: Dict[str, Callable[[], Dict]]) -> go.Figure:
        """Create interactive Plotly dashboard."""
        # Create subplots
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=list(data_sources.keys()),
            specs=[[{"secondary_y": True}, {"secondary_y": True}],
                   [{"secondary_y": True}, {"secondary_y": True}]]
        )
        
        colors = px.colors.qualitative.Set3
        
        for i, (source_name, data_func) in enumerate(data_sources.items()):
            try:
                data = data_func()
                row = (i // 2) + 1
                col = (i % 2) + 1
                
                # Add traces based on data structure
                if isinstance(data, dict):
                    for j, (key, values) in enumerate(data.items()):
                        if isinstance(values, list) and len(values) > 0:
                            fig.add_trace(
                                go.Scatter(
                                    y=values,
                                    mode='lines',
                                    name=f"{source_name}_{key}",
                                    line=dict(color=colors[j % len(colors)])
                                ),
                                row=row, col=col
                            )
                            
            except Exception as e:
                logger.warning(f"Failed to add data source {source_name}: {e}")
        
        fig.update_layout(
            title="ULTRA System Dashboard",
            showlegend=True,
            height=800
        )
        
        return fig

# ============================================================================
# Optimization Utilities
# ============================================================================

class OptimizationUtils:
    """Advanced optimization utilities for ULTRA system components."""
    
    @staticmethod
    def adaptive_learning_rate(current_lr: float,
                             loss_history: List[float],
                             patience: int = 10,
                             factor: float = 0.5,
                             min_lr: float = 1e-8) -> float:
        """Adaptive learning rate based on loss history."""
        if len(loss_history) < patience + 1:
            return current_lr
            
        # Check if loss has plateaued
        recent_losses = loss_history[-patience:]
        if len(set(recent_losses)) == 1:  # All identical
            return max(min_lr, current_lr * factor)
            
        # Check for improvement
        current_loss = loss_history[-1]
        best_recent = min(recent_losses[:-1])
        
        if current_loss >= best_recent:
            return max(min_lr, current_lr * factor)
        else:
            return current_lr
    
    @staticmethod
    def gradient_clipping(gradients: List[torch.Tensor],
                         max_norm: float = 1.0,
                         norm_type: float = 2.0) -> float:
        """Clip gradients by norm."""
        if not gradients:
            return 0.0
            
        device = gradients[0].device
        total_norm = torch.norm(
            torch.stack([torch.norm(g.detach(), norm_type).to(device) for g in gradients]),
            norm_type
        )
        
        clip_coef = max_norm / (total_norm + EPSILON)
        clip_coef_clamped = torch.clamp(clip_coef, max=1.0)
        
        for g in gradients:
            g.detach().mul_(clip_coef_clamped.to(g.device))
            
        return total_norm.item()
    
    @staticmethod
    def cosine_annealing_schedule(epoch: int,
                                max_epochs: int,
                                min_lr: float,
                                max_lr: float) -> float:
        """Cosine annealing learning rate schedule."""
        return min_lr + (max_lr - min_lr) * 0.5 * (
            1 + np.cos(np.pi * epoch / max_epochs)
        )
    
    @staticmethod
    def warmup_schedule(step: int,
                       warmup_steps: int,
                       peak_lr: float) -> float:
        """Linear warmup learning rate schedule."""
        if step < warmup_steps:
            return peak_lr * (step / warmup_steps)
        else:
            return peak_lr
    
    @staticmethod
    def evolutionary_optimization(objective_func: Callable[[np.ndarray], float],
                                bounds: List[Tuple[float, float]],
                                population_size: int = 50,
                                generations: int = 100,
                                mutation_rate: float = 0.1,
                                crossover_rate: float = 0.8) -> Tuple[np.ndarray, float]:
        """Simple evolutionary optimization algorithm."""
        dim = len(bounds)
        
        # Initialize population
        population = np.random.rand(population_size, dim)
        for i, (low, high) in enumerate(bounds):
            population[:, i] = population[:, i] * (high - low) + low
        
        best_individual = None
        best_fitness = float('inf')
        fitness_history = []
        
        for generation in range(generations):
            # Evaluate fitness
            fitness = np.array([objective_func(ind) for ind in population])
            
            # Track best
            min_idx = np.argmin(fitness)
            if fitness[min_idx] < best_fitness:
                best_fitness = fitness[min_idx]
                best_individual = population[min_idx].copy()
            
            fitness_history.append(best_fitness)
            
            # Selection (tournament)
            new_population = []
            for _ in range(population_size):
                # Tournament selection
                tournament_size = 3
                tournament_indices = np.random.choice(population_size, tournament_size)
                tournament_fitness = fitness[tournament_indices]
                winner_idx = tournament_indices[np.argmin(tournament_fitness)]
                new_population.append(population[winner_idx].copy())
            
            population = np.array(new_population)
            
            # Crossover
            for i in range(0, population_size - 1, 2):
                if np.random.rand() < crossover_rate:
                    # Single-point crossover
                    crossover_point = np.random.randint(1, dim)
                    temp = population[i, crossover_point:].copy()
                    population[i, crossover_point:] = population[i + 1, crossover_point:]
                    population[i + 1, crossover_point:] = temp
            
            # Mutation
            for i in range(population_size):
                for j in range(dim):
                    if np.random.rand() < mutation_rate:
                        low, high = bounds[j]
                        population[i, j] = np.random.rand() * (high - low) + low
        
        return best_individual, best_fitness
    
    @staticmethod
    def bayesian_optimization_step(X_observed: np.ndarray,
                                  y_observed: np.ndarray,
                                  bounds: np.ndarray,
                                  acquisition_func: str = "ei") -> np.ndarray:
        """Single step of Bayesian optimization."""
        try:
            from sklearn.gaussian_process import GaussianProcessRegressor
            from sklearn.gaussian_process.kernels import RBF, ConstantKernel
        except ImportError:
            logger.error("Scikit-learn required for Bayesian optimization")
            return np.random.rand(len(bounds))
        
        # Fit Gaussian Process
        kernel = ConstantKernel(1.0) * RBF(length_scale=1.0)
        gp = GaussianProcessRegressor(kernel=kernel, alpha=1e-6)
        gp.fit(X_observed, y_observed)
        
        # Optimize acquisition function
        def acquisition(x):
            x = x.reshape(1, -1)
            mu, std = gp.predict(x, return_std=True)
            
            if acquisition_func == "ei":  # Expected Improvement
                improvement = y_observed.min() - mu
                z = improvement / (std + EPSILON)
                return improvement * scipy.stats.norm.cdf(z) + std * scipy.stats.norm.pdf(z)
            elif acquisition_func == "ucb":  # Upper Confidence Bound
                beta = 2.0
                return mu + beta * std
            else:
                return mu  # Posterior mean
        
        # Find maximum of acquisition function
        best_x = None
        best_acq = float('-inf')
        
        for _ in range(100):  # Random search for simplicity
            x_candidate = np.random.rand(len(bounds))
            for i, (low, high) in enumerate(bounds):
                x_candidate[i] = x_candidate[i] * (high - low) + low
            
            acq_value = acquisition(x_candidate)
            if acq_value > best_acq:
                best_acq = acq_value
                best_x = x_candidate
        
        return best_x

# ============================================================================
# Configuration Utilities
# ============================================================================

class ConfigurationValidator:
    """Advanced configuration validation utilities."""
    
    @staticmethod
    def validate_neural_parameters(config: Dict[str, Any]) -> List[str]:
        """Validate neural architecture parameters."""
        errors = []
        
        # Check neuron parameters
        if 'neuron_params' in config:
            params = config['neuron_params']
            
            if params.get('tau_m', 0) <= 0:
                errors.append("Membrane time constant (tau_m) must be positive")
            
            if params.get('v_threshold', 0) <= params.get('v_rest', -70):
                errors.append("Threshold potential must be greater than resting potential")
            
            if params.get('refractory_period', 0) < 0:
                errors.append("Refractory period cannot be negative")
        
        # Check STDP parameters
        if 'stdp_params' in config:
            stdp = config['stdp_params']
            
            if stdp.get('a_plus', 0) <= 0 or stdp.get('a_minus', 0) <= 0:
                errors.append("STDP amplitudes must be positive")
            
            if stdp.get('tau_plus', 0) <= 0 or stdp.get('tau_minus', 0) <= 0:
                errors.append("STDP time constants must be positive")
        
        return errors
    
    @staticmethod
    def validate_transformer_parameters(config: Dict[str, Any]) -> List[str]:
        """Validate transformer architecture parameters."""
        errors = []
        
        if 'transformer_params' in config:
            params = config['transformer_params']
            
            d_model = params.get('d_model', 512)
            n_heads = params.get('n_heads', 8)
            
            if d_model <= 0:
                errors.append("Model dimension (d_model) must be positive")
            
            if n_heads <= 0:
                errors.append("Number of attention heads must be positive")
            
            if d_model % n_heads != 0:
                errors.append("Model dimension must be divisible by number of heads")
            
            if params.get('dropout', 0) < 0 or params.get('dropout', 0) >= 1:
                errors.append("Dropout rate must be in [0, 1)")
        
        return errors
    
    @staticmethod
    def validate_diffusion_parameters(config: Dict[str, Any]) -> List[str]:
        """Validate diffusion model parameters."""
        errors = []
        
        if 'diffusion_params' in config:
            params = config['diffusion_params']
            
            if params.get('num_timesteps', 0) <= 0:
                errors.append("Number of timesteps must be positive")
            
            beta_start = params.get('beta_start', 1e-4)
            beta_end = params.get('beta_end', 0.02)
            
            if beta_start <= 0 or beta_end <= 0:
                errors.append("Beta values must be positive")
            
            if beta_start >= beta_end:
                errors.append("Beta start must be less than beta end")
        
        return errors
    
    @staticmethod
    def check_resource_requirements(config: Dict[str, Any]) -> Dict[str, Any]:
        """Estimate resource requirements from configuration."""
        requirements = {
            'estimated_memory_gb': 0.0,
            'estimated_compute_units': 0.0,
            'warnings': []
        }
        
        # Estimate memory for neural networks
        if 'core_neural' in config:
            network_size = config['core_neural'].get('network_size', (100, 100, 100))
            total_neurons = np.prod(network_size)
            
            # Rough estimate: 1KB per neuron
            neural_memory = total_neurons * 1e-6  # GB
            requirements['estimated_memory_gb'] += neural_memory
            
            if total_neurons > 1e6:
                requirements['warnings'].append("Large neural network may require significant memory")
        
        # Estimate memory for transformer
        if 'hyper_transformer' in config:
            d_model = config['hyper_transformer'].get('transformer_params', {}).get('d_model', 512)
            max_seq_len = config['hyper_transformer'].get('transformer_params', {}).get('max_seq_length', 1024)
            
            # Rough estimate for attention matrices
            attention_memory = (max_seq_len ** 2 * d_model * 4) * 1e-9  # GB
            requirements['estimated_memory_gb'] += attention_memory
        
        # Add safety margin
        requirements['estimated_memory_gb'] *= 1.5
        
        return requirements

# ============================================================================
# Safety and Validation Utilities
# ============================================================================

class SafetyValidator:
    """Safety validation utilities for ULTRA system operations."""
    
    @staticmethod
    def validate_tensor_operations(tensor: torch.Tensor,
                                 operation: str,
                                 **kwargs) -> Tuple[bool, str]:
        """Validate tensor operations for safety."""
        try:
            # Check for NaN or Inf values
            if torch.isnan(tensor).any():
                return False, f"Tensor contains NaN values in {operation}"
            
            if torch.isinf(tensor).any():
                return False, f"Tensor contains Inf values in {operation}"
            
            # Check tensor shapes for specific operations
            if operation == "matmul":
                other = kwargs.get('other')
                if other is not None:
                    if tensor.shape[-1] != other.shape[-2]:
                        return False, f"Incompatible shapes for matmul: {tensor.shape} x {other.shape}"
            
            elif operation == "softmax":
                dim = kwargs.get('dim', -1)
                if abs(dim) > len(tensor.shape):
                    return False, f"Invalid dimension {dim} for tensor with {len(tensor.shape)} dimensions"
            
            # Check for reasonable value ranges
            if operation in ["sigmoid", "tanh", "softmax"]:
                if tensor.abs().max() > 100:
                    return False, f"Extreme values detected in {operation}: max={tensor.abs().max()}"
            
            return True, "Safe"
            
        except Exception as e:
            return False, f"Validation error in {operation}: {str(e)}"
    
    @staticmethod
    def validate_neural_dynamics(membrane_potential: float,
                                threshold: float,
                                time_step: float) -> Tuple[bool, str]:
        """Validate neural dynamics parameters."""
        if not np.isfinite(membrane_potential):
            return False, "Invalid membrane potential value"
        
        if not np.isfinite(threshold):
            return False, "Invalid threshold value"
        
        if time_step <= 0:
            return False, "Time step must be positive"
        
        if abs(membrane_potential) > 1000:  # mV
            return False, f"Extreme membrane potential: {membrane_potential} mV"
        
        return True, "Safe"
    
    @staticmethod
    def validate_attention_weights(weights: np.ndarray) -> Tuple[bool, str]:
        """Validate attention weight matrices."""
        if not np.allclose(np.sum(weights, axis=-1), 1.0, atol=1e-6):
            return False, "Attention weights do not sum to 1"
        
        if np.any(weights < 0):
            return False, "Negative attention weights detected"
        
        if np.any(weights > 1):
            return False, "Attention weights exceed 1"
        
        return True, "Safe"
    
    @staticmethod
    def validate_plasticity_update(weight_change: float,
                                 current_weight: float,
                                 weight_bounds: Tuple[float, float] = (0.0, 1.0)) -> Tuple[bool, str]:
        """Validate synaptic plasticity updates."""
        new_weight = current_weight + weight_change
        
        if not np.isfinite(weight_change):
            return False, "Invalid weight change value"
        
        if new_weight < weight_bounds[0] or new_weight > weight_bounds[1]:
            return False, f"Weight update violates bounds: {new_weight} not in {weight_bounds}"
        
        if abs(weight_change) > abs(current_weight) * 10:
            return False, f"Extreme weight change: {weight_change} (current: {current_weight})"
        
        return True, "Safe"

# ============================================================================
# Profiling and Performance Analysis
# ============================================================================

class PerformanceProfiler:
    """Advanced performance profiling for ULTRA system components."""
    
    def __init__(self):
        """Initialize performance profiler."""
        self.profiles = {}
        self.active_profiles = {}
        self._lock = threading.Lock()
    
    @contextmanager
    def profile_operation(self, operation_name: str, 
                         profile_memory: bool = True,
                         profile_cuda: bool = None):
        """Context manager for profiling operations."""
        if profile_cuda is None:
            profile_cuda = torch.cuda.is_available()
        
        activities = [ProfilerActivity.CPU]
        if profile_cuda:
            activities.append(ProfilerActivity.CUDA)
        
        profile_data = {
            'start_time': time.time(),
            'start_memory': psutil.Process().memory_info().rss if profile_memory else 0,
            'start_cuda_memory': torch.cuda.memory_allocated() if profile_cuda else 0
        }
        
        with profile(activities=activities, record_shapes=True) as prof:
            try:
                yield prof
            finally:
                profile_data['end_time'] = time.time()
                profile_data['duration'] = profile_data['end_time'] - profile_data['start_time']
                
                if profile_memory:
                    profile_data['end_memory'] = psutil.Process().memory_info().rss
                    profile_data['memory_delta'] = profile_data['end_memory'] - profile_data['start_memory']
                
                if profile_cuda:
                    profile_data['end_cuda_memory'] = torch.cuda.memory_allocated()
                    profile_data['cuda_memory_delta'] = profile_data['end_cuda_memory'] - profile_data['start_cuda_memory']
                
                profile_data['profiler_output'] = prof.key_averages().table(sort_by="cpu_time_total", row_limit=10)
                
                with self._lock:
                    if operation_name not in self.profiles:
                        self.profiles[operation_name] = []
                    self.profiles[operation_name].append(profile_data)
    
    def get_profile_summary(self, operation_name: str) -> Dict[str, Any]:
        """Get summary statistics for a profiled operation."""
        with self._lock:
            if operation_name not in self.profiles:
                return {}
            
            profiles = self.profiles[operation_name]
            
            durations = [p['duration'] for p in profiles]
            memory_deltas = [p.get('memory_delta', 0) for p in profiles]
            cuda_memory_deltas = [p.get('cuda_memory_delta', 0) for p in profiles]
            
            return {
                'operation': operation_name,
                'num_runs': len(profiles),
                'avg_duration': np.mean(durations),
                'std_duration': np.std(durations),
                'min_duration': np.min(durations),
                'max_duration': np.max(durations),
                'avg_memory_delta': np.mean(memory_deltas),
                'max_memory_delta': np.max(memory_deltas),
                'avg_cuda_memory_delta': np.mean(cuda_memory_deltas),
                'max_cuda_memory_delta': np.max(cuda_memory_deltas),
                'latest_profiler_output': profiles[-1].get('profiler_output', '')
            }
    
    def get_all_profiles_summary(self) -> Dict[str, Any]:
        """Get summary of all profiled operations."""
        with self._lock:
            summary = {}
            for operation_name in self.profiles.keys():
                summary[operation_name] = self.get_profile_summary(operation_name)
            return summary
    
    def clear_profiles(self, operation_name: Optional[str] = None) -> None:
        """Clear profiling data."""
        with self._lock:
            if operation_name:
                self.profiles.pop(operation_name, None)
            else:
                self.profiles.clear()

# ============================================================================
# Distributed Computing Utilities
# ============================================================================

class DistributedManager:
    """Distributed computing management for ULTRA system."""
    
    def __init__(self):
        """Initialize distributed manager."""
        self.is_distributed = False
        self.world_size = 1
        self.rank = 0
        self.local_rank = 0
        self.device = torch.device('cpu')
        
    def init_distributed(self, 
                        backend: str = 'nccl',
                        init_method: str = 'env://') -> bool:
        """Initialize distributed training."""
        try:
            if 'WORLD_SIZE' in os.environ:
                self.world_size = int(os.environ['WORLD_SIZE'])
                self.rank = int(os.environ['RANK'])
                self.local_rank = int(os.environ.get('LOCAL_RANK', 0))
                
                if self.world_size > 1:
                    dist.init_process_group(
                        backend=backend,
                        init_method=init_method,
                        world_size=self.world_size,
                        rank=self.rank
                    )
                    
                    if torch.cuda.is_available():
                        torch.cuda.set_device(self.local_rank)
                        self.device = torch.device(f'cuda:{self.local_rank}')
                    
                    self.is_distributed = True
                    logger.info(f"Distributed training initialized: rank {self.rank}/{self.world_size}")
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"Failed to initialize distributed training: {e}")
            return False
    
    def cleanup_distributed(self) -> None:
        """Cleanup distributed training."""
        if self.is_distributed:
            try:
                dist.destroy_process_group()
                logger.info("Distributed training cleaned up")
            except Exception as e:
                logger.error(f"Error cleaning up distributed training: {e}")
    
    def all_reduce_tensor(self, tensor: torch.Tensor) -> torch.Tensor:
        """All-reduce tensor across all processes."""
        if self.is_distributed:
            dist.all_reduce(tensor)
            tensor /= self.world_size
        return tensor
    
    def broadcast_tensor(self, tensor: torch.Tensor, src: int = 0) -> torch.Tensor:
        """Broadcast tensor from source rank to all ranks."""
        if self.is_distributed:
            dist.broadcast(tensor, src=src)
        return tensor
    
    def gather_tensors(self, tensor: torch.Tensor) -> List[torch.Tensor]:
        """Gather tensors from all processes."""
        if self.is_distributed:
            gathered = [torch.zeros_like(tensor) for _ in range(self.world_size)]
            dist.all_gather(gathered, tensor)
            return gathered
        else:
            return [tensor]
    
    def is_main_process(self) -> bool:
        """Check if current process is the main process."""
        return self.rank == 0
    
    def barrier(self) -> None:
        """Synchronization barrier."""
        if self.is_distributed:
            dist.barrier()

# ============================================================================
# Main Utility Functions
# ============================================================================

# Global instances
_system_monitor = None
_device_manager = None
_memory_manager = None
_visualization_manager = None
_performance_profiler = None
_distributed_manager = None

def get_system_monitor() -> SystemMonitor:
    """Get global system monitor instance."""
    global _system_monitor
    if _system_monitor is None:
        _system_monitor = SystemMonitor()
    return _system_monitor

def get_device_manager() -> DeviceManager:
    """Get global device manager instance."""
    global _device_manager
    if _device_manager is None:
        _device_manager = DeviceManager()
    return _device_manager

def get_memory_manager() -> MemoryManager:
    """Get global memory manager instance."""
    global _memory_manager
    if _memory_manager is None:
        config = get_config()
        max_memory_str = config.max_memory_usage
        max_memory_gb = float(max_memory_str.replace('GB', '').replace('gb', ''))
        _memory_manager = MemoryManager(max_memory_gb)
    return _memory_manager

def get_visualization_manager() -> VisualizationManager:
    """Get global visualization manager instance."""
    global _visualization_manager
    if _visualization_manager is None:
        _visualization_manager = VisualizationManager()
    return _visualization_manager

def get_performance_profiler() -> PerformanceProfiler:
    """Get global performance profiler instance."""
    global _performance_profiler
    if _performance_profiler is None:
        _performance_profiler = PerformanceProfiler()
    return _performance_profiler

def get_distributed_manager() -> DistributedManager:
    """Get global distributed manager instance."""
    global _distributed_manager
    if _distributed_manager is None:
        _distributed_manager = DistributedManager()
    return _distributed_manager

def setup_ultra_environment() -> None:
    """Set up the complete ULTRA environment."""
    config = get_config()
    
    # Set random seeds for reproducibility
    np.random.seed(config.random_seed)
    torch.manual_seed(config.random_seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed_all(config.random_seed)
    
    # Initialize distributed training if configured
    dist_manager = get_distributed_manager()
    if config.distributed_training:
        dist_manager.init_distributed()
    
    # Start system monitoring
    monitor = get_system_monitor()
    monitor.start_monitoring()
    
    # Set device preferences
    device_manager = get_device_manager()
    logger.info(f"ULTRA environment initialized - Device: {device_manager.current_device}")

def cleanup_ultra_environment() -> None:
    """Clean up ULTRA environment."""
    # Stop monitoring
    if _system_monitor:
        _system_monitor.stop_monitoring()
    
    # Cleanup distributed
    if _distributed_manager:
        _distributed_manager.cleanup_distributed()
    
    # Clear caches
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
    
    gc.collect()
    logger.info("ULTRA environment cleaned up")

# Decorators for common functionality
def ultra_profile(operation_name: str):
    """Decorator for profiling operations."""
    def decorator(func: F) -> F:
        @wraps(func)
        def wrapper(*args, **kwargs):
            profiler = get_performance_profiler()
            with profiler.profile_operation(operation_name):
                return func(*args, **kwargs)
        return wrapper
    return decorator

def ultra_monitor(metric_type: str = "neural"):
    """Decorator for monitoring operations."""
    def decorator(func: F) -> F:
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            result = func(*args, **kwargs)
            duration = time.time() - start_time
            
            monitor = get_system_monitor()
            
            if metric_type == "neural":
                # Extract neural metrics from result if available
                if hasattr(result, 'spike_count'):
                    monitor.add_neural_metrics(
                        spike_rate=getattr(result, 'spike_count', 0) / duration,
                        synaptic_updates=getattr(result, 'synaptic_updates', 0),
                        plasticity_changes=getattr(result, 'plasticity_changes', 0.0)
                    )
                else:
                    monitor.add_neural_metrics(0.0, 0, 0.0)
                    
            elif metric_type == "cognitive":
                if hasattr(result, 'reasoning_paths'):
                    monitor.add_cognitive_metrics(
                        reasoning_paths=getattr(result, 'reasoning_paths', 0),
                        attention_entropy=getattr(result, 'attention_entropy', 0.0),
                        working_memory_load=getattr(result, 'working_memory_load', 0.0)
                    )
                else:
                    monitor.add_cognitive_metrics(0, 0.0, 0.0)
            
            return result
        return wrapper
    return decorator

def ultra_safe_operation(validate_inputs: bool = True, validate_outputs: bool = True):
    """Decorator for safe tensor operations with validation."""
    def decorator(func: F) -> F:
        @wraps(func)
        def wrapper(*args, **kwargs):
            if validate_inputs:
                # Validate tensor inputs
                for arg in args:
                    if isinstance(arg, torch.Tensor):
                        is_safe, message = SafetyValidator.validate_tensor_operations(
                            arg, func.__name__
                        )
                        if not is_safe:
                            raise ValueError(f"Input validation failed: {message}")
            
            result = func(*args, **kwargs)
            
            if validate_outputs and isinstance(result, torch.Tensor):
                is_safe, message = SafetyValidator.validate_tensor_operations(
                    result, func.__name__
                )
                if not is_safe:
                    logger.warning(f"Output validation warning: {message}")
            
            return result
        return wrapper
    return decorator

def ultra_memory_managed(tag: str = "default"):
    """Decorator for automatic memory management."""
    def decorator(func: F) -> F:
        @wraps(func)
        def wrapper(*args, **kwargs):
            memory_manager = get_memory_manager()
            
            # Estimate memory usage
            estimated_memory = 0
            for arg in args:
                if isinstance(arg, torch.Tensor):
                    estimated_memory += arg.numel() * arg.element_size()
                elif isinstance(arg, np.ndarray):
                    estimated_memory += arg.nbytes
            
            if estimated_memory > 0:
                with memory_manager.managed_allocation(estimated_memory, tag):
                    return func(*args, **kwargs)
            else:
                return func(*args, **kwargs)
        return wrapper
    return decorator

# ============================================================================
# Data Processing Utilities
# ============================================================================

class DataProcessor:
    """Advanced data processing utilities for ULTRA system."""
    
    def __init__(self):
        """Initialize data processor."""
        self.scalers = {}
        self.encoders = {}
        
    def normalize_data(self, 
                      data: np.ndarray,
                      method: str = "standard",
                      fit: bool = True,
                      scaler_key: str = "default") -> np.ndarray:
        """Normalize data using various methods."""
        if method == "standard":
            if scaler_key not in self.scalers or fit:
                self.scalers[scaler_key] = StandardScaler()
                normalized = self.scalers[scaler_key].fit_transform(data)
            else:
                normalized = self.scalers[scaler_key].transform(data)
                
        elif method == "minmax":
            if scaler_key not in self.scalers or fit:
                self.scalers[scaler_key] = MinMaxScaler()
                normalized = self.scalers[scaler_key].fit_transform(data)
            else:
                normalized = self.scalers[scaler_key].transform(data)
                
        elif method == "robust":
            if scaler_key not in self.scalers or fit:
                self.scalers[scaler_key] = RobustScaler()
                normalized = self.scalers[scaler_key].fit_transform(data)
            else:
                normalized = self.scalers[scaler_key].transform(data)
                
        elif method == "unit_vector":
            norms = np.linalg.norm(data, axis=1, keepdims=True)
            normalized = data / (norms + EPSILON)
            
        else:
            raise ValueError(f"Unknown normalization method: {method}")
            
        return normalized
    
    def encode_spike_trains(self,
                          spike_times: List[np.ndarray],
                          time_window: float,
                          bin_size: float = 0.001) -> np.ndarray:
        """Encode spike trains into rate vectors."""
        n_bins = int(time_window / bin_size)
        n_neurons = len(spike_times)
        
        rate_matrix = np.zeros((n_neurons, n_bins))
        
        for i, spikes in enumerate(spike_times):
            if len(spikes) > 0:
                # Filter spikes within time window
                valid_spikes = spikes[spikes < time_window]
                
                # Convert to bin indices
                bin_indices = (valid_spikes / bin_size).astype(int)
                bin_indices = np.clip(bin_indices, 0, n_bins - 1)
                
                # Count spikes per bin
                spike_counts = np.bincount(bin_indices, minlength=n_bins)
                rate_matrix[i, :] = spike_counts / bin_size
        
        return rate_matrix
    
    def decode_population_vector(self,
                               population_activity: np.ndarray,
                               preferred_directions: np.ndarray) -> float:
        """Decode population vector to estimate direction."""
        # Compute weighted sum of preferred directions
        weighted_sum = np.sum(population_activity[:, np.newaxis] * 
                            np.array([np.cos(preferred_directions), 
                                    np.sin(preferred_directions)]).T, axis=0)
        
        # Compute angle
        decoded_angle = np.arctan2(weighted_sum[1], weighted_sum[0])
        
        return decoded_angle
    
    def extract_features_from_signals(self,
                                    signals: np.ndarray,
                                    sampling_rate: float,
                                    feature_types: List[str]) -> Dict[str, np.ndarray]:
        """Extract various features from neural signals."""
        features = {}
        
        for feature_type in feature_types:
            if feature_type == "power_spectral_density":
                freqs, psd = scipy.signal.welch(signals, fs=sampling_rate, axis=-1)
                features["psd_freqs"] = freqs
                features["psd"] = psd
                
            elif feature_type == "spectral_entropy":
                freqs, psd = scipy.signal.welch(signals, fs=sampling_rate, axis=-1)
                # Normalize PSD to get probability distribution
                psd_norm = psd / np.sum(psd, axis=-1, keepdims=True)
                entropy = -np.sum(psd_norm * np.log2(psd_norm + EPSILON), axis=-1)
                features["spectral_entropy"] = entropy
                
            elif feature_type == "zero_crossings":
                zero_crossings = np.sum(np.diff(np.sign(signals), axis=-1) != 0, axis=-1)
                features["zero_crossings"] = zero_crossings
                
            elif feature_type == "rms":
                rms = np.sqrt(np.mean(signals**2, axis=-1))
                features["rms"] = rms
                
            elif feature_type == "peak_frequency":
                freqs, psd = scipy.signal.welch(signals, fs=sampling_rate, axis=-1)
                peak_indices = np.argmax(psd, axis=-1)
                peak_freqs = freqs[peak_indices]
                features["peak_frequency"] = peak_freqs
                
            elif feature_type == "hjorth_parameters":
                # Hjorth parameters: Activity, Mobility, Complexity
                activity = np.var(signals, axis=-1)
                
                # First derivative
                diff1 = np.diff(signals, axis=-1)
                mobility = np.sqrt(np.var(diff1, axis=-1) / (activity + EPSILON))
                
                # Second derivative
                diff2 = np.diff(diff1, axis=-1)
                complexity = np.sqrt(np.var(diff2, axis=-1) / (np.var(diff1, axis=-1) + EPSILON)) / (mobility + EPSILON)
                
                features["hjorth_activity"] = activity
                features["hjorth_mobility"] = mobility
                features["hjorth_complexity"] = complexity
        
        return features
    
    def apply_spatial_filter(self,
                           data: np.ndarray,
                           filter_type: str = "laplacian",
                           **kwargs) -> np.ndarray:
        """Apply spatial filtering to multi-channel data."""
        if filter_type == "laplacian":
            # Simple Laplacian filter
            filtered = np.copy(data)
            n_channels = data.shape[0]
            
            for i in range(1, n_channels - 1):
                filtered[i] = data[i] - 0.5 * (data[i-1] + data[i+1])
                
        elif filter_type == "car":  # Common Average Reference
            common_avg = np.mean(data, axis=0, keepdims=True)
            filtered = data - common_avg
            
        elif filter_type == "bipolar":
            # Bipolar montage
            filtered = np.diff(data, axis=0)
            
        elif filter_type == "spatial_ica":
            # Independent Component Analysis
            n_components = kwargs.get('n_components', min(data.shape))
            ica = FastICA(n_components=n_components, random_state=42)
            filtered = ica.fit_transform(data.T).T
            
        else:
            raise ValueError(f"Unknown spatial filter type: {filter_type}")
            
        return filtered
    
    def temporal_filter(self,
                       data: np.ndarray,
                       sampling_rate: float,
                       filter_type: str = "bandpass",
                       **kwargs) -> np.ndarray:
        """Apply temporal filtering to signals."""
        nyquist = sampling_rate / 2
        
        if filter_type == "bandpass":
            low_freq = kwargs.get('low_freq', 1.0)
            high_freq = kwargs.get('high_freq', 100.0)
            order = kwargs.get('order', 4)
            
            low = low_freq / nyquist
            high = high_freq / nyquist
            
            b, a = scipy.signal.butter(order, [low, high], btype='band')
            filtered = scipy.signal.filtfilt(b, a, data, axis=-1)
            
        elif filter_type == "lowpass":
            cutoff_freq = kwargs.get('cutoff_freq', 100.0)
            order = kwargs.get('order', 4)
            
            cutoff = cutoff_freq / nyquist
            b, a = scipy.signal.butter(order, cutoff, btype='low')
            filtered = scipy.signal.filtfilt(b, a, data, axis=-1)
            
        elif filter_type == "highpass":
            cutoff_freq = kwargs.get('cutoff_freq', 1.0)
            order = kwargs.get('order', 4)
            
            cutoff = cutoff_freq / nyquist
            b, a = scipy.signal.butter(order, cutoff, btype='high')
            filtered = scipy.signal.filtfilt(b, a, data, axis=-1)
            
        elif filter_type == "notch":
            notch_freq = kwargs.get('notch_freq', 50.0)
            quality_factor = kwargs.get('quality_factor', 30.0)
            
            notch = notch_freq / nyquist
            b, a = scipy.signal.iirnotch(notch, quality_factor)
            filtered = scipy.signal.filtfilt(b, a, data, axis=-1)
            
        else:
            raise ValueError(f"Unknown temporal filter type: {filter_type}")
            
        return filtered

# ============================================================================
# Network Analysis Utilities
# ============================================================================

class NetworkAnalyzer:
    """Network analysis utilities for neural connectivity and reasoning graphs."""
    
    def __init__(self):
        """Initialize network analyzer."""
        self.graphs = {}
        
    def create_connectivity_matrix(self,
                                 positions: np.ndarray,
                                 connection_probability: Callable[[float], float],
                                 directed: bool = True) -> np.ndarray:
        """Create connectivity matrix based on spatial positions."""
        n_nodes = len(positions)
        connectivity = np.zeros((n_nodes, n_nodes))
        
        for i in range(n_nodes):
            for j in range(n_nodes):
                if i != j:
                    distance = np.linalg.norm(positions[i] - positions[j])
                    prob = connection_probability(distance)
                    
                    if np.random.rand() < prob:
                        connectivity[i, j] = 1.0
                        
                        if not directed:
                            connectivity[j, i] = 1.0
        
        return connectivity
    
    def analyze_network_properties(self, 
                                 adjacency_matrix: np.ndarray,
                                 weighted: bool = False) -> Dict[str, Any]:
        """Analyze network topological properties."""
        # Create NetworkX graph
        if weighted:
            G = nx.from_numpy_array(adjacency_matrix, create_using=nx.DiGraph())
        else:
            G = nx.from_numpy_array((adjacency_matrix > 0).astype(int), create_using=nx.DiGraph())
        
        properties = {}
        
        # Basic properties
        properties['num_nodes'] = G.number_of_nodes()
        properties['num_edges'] = G.number_of_edges()
        properties['density'] = nx.density(G)
        
        # Degree statistics
        in_degrees = [d for n, d in G.in_degree()]
        out_degrees = [d for n, d in G.out_degree()]
        
        properties['avg_in_degree'] = np.mean(in_degrees)
        properties['avg_out_degree'] = np.mean(out_degrees)
        properties['std_in_degree'] = np.std(in_degrees)
        properties['std_out_degree'] = np.std(out_degrees)
        
        # Centrality measures
        try:
            properties['betweenness_centrality'] = nx.betweenness_centrality(G)
            properties['closeness_centrality'] = nx.closeness_centrality(G)
            properties['eigenvector_centrality'] = nx.eigenvector_centrality(G, max_iter=1000)
            properties['pagerank'] = nx.pagerank(G)
        except:
            logger.warning("Failed to compute centrality measures")
        
        # Clustering
        try:
            properties['clustering_coefficient'] = nx.average_clustering(G.to_undirected())
        except:
            properties['clustering_coefficient'] = 0.0
        
        # Path lengths
        try:
            if nx.is_strongly_connected(G):
                properties['avg_shortest_path_length'] = nx.average_shortest_path_length(G)
            else:
                # For disconnected graphs, compute for largest component
                largest_cc = max(nx.strongly_connected_components(G), key=len)
                subgraph = G.subgraph(largest_cc)
                if len(largest_cc) > 1:
                    properties['avg_shortest_path_length'] = nx.average_shortest_path_length(subgraph)
                else:
                    properties['avg_shortest_path_length'] = 0.0
        except:
            properties['avg_shortest_path_length'] = float('inf')
        
        # Small-world properties
        if properties['clustering_coefficient'] > 0 and properties['avg_shortest_path_length'] < float('inf'):
            # Compare with random graph
            random_clustering = properties['avg_out_degree'] / (properties['num_nodes'] - 1)
            random_path_length = np.log(properties['num_nodes']) / np.log(properties['avg_out_degree'])
            
            if random_clustering > 0 and random_path_length > 0:
                properties['small_world_coefficient'] = (
                    (properties['clustering_coefficient'] / random_clustering) /
                    (properties['avg_shortest_path_length'] / random_path_length)
                )
            else:
                properties['small_world_coefficient'] = 0.0
        else:
            properties['small_world_coefficient'] = 0.0
        
        # Rich club coefficient
        try:
            properties['rich_club_coefficient'] = nx.rich_club_coefficient(G.to_undirected())
        except:
            properties['rich_club_coefficient'] = {}
        
        return properties
    
    def detect_communities(self,
                          adjacency_matrix: np.ndarray,
                          method: str = "louvain") -> Tuple[np.ndarray, float]:
        """Detect communities in the network."""
        G = nx.from_numpy_array(adjacency_matrix, create_using=nx.Graph())
        
        if method == "louvain":
            try:
                import community as community_louvain
                partition = community_louvain.best_partition(G)
                communities = np.array([partition[i] for i in range(len(adjacency_matrix))])
                modularity = community_louvain.modularity(partition, G)
            except ImportError:
                logger.warning("python-louvain not available, using spectral clustering")
                method = "spectral"
        
        if method == "spectral":
            n_clusters = min(10, adjacency_matrix.shape[0] // 10)  # Heuristic
            clustering = SpectralClustering(n_clusters=n_clusters, affinity='precomputed')
            communities = clustering.fit_predict(adjacency_matrix)
            
            # Compute modularity
            modularity = 0.0
            m = np.sum(adjacency_matrix) / 2  # Total number of edges
            if m > 0:
                for i in range(adjacency_matrix.shape[0]):
                    for j in range(adjacency_matrix.shape[1]):
                        if communities[i] == communities[j]:
                            ki = np.sum(adjacency_matrix[i, :])
                            kj = np.sum(adjacency_matrix[j, :])
                            modularity += adjacency_matrix[i, j] - (ki * kj) / (2 * m)
                modularity /= (2 * m)
        
        elif method == "infomap":
            try:
                import infomap
                im = infomap.Infomap()
                
                # Add edges to infomap
                for i in range(adjacency_matrix.shape[0]):
                    for j in range(adjacency_matrix.shape[1]):
                        if adjacency_matrix[i, j] > 0:
                            im.add_link(i, j, adjacency_matrix[i, j])
                
                im.run()
                communities = np.array([node.module_id for node in im.tree if node.is_leaf])
                modularity = im.codelength  # Note: this is actually code length, not modularity
                
            except ImportError:
                logger.warning("infomap not available, using spectral clustering")
                return self.detect_communities(adjacency_matrix, method="spectral")
        
        return communities, modularity
    
    def compute_network_efficiency(self, adjacency_matrix: np.ndarray) -> Dict[str, float]:
        """Compute global and local efficiency of the network."""
        G = nx.from_numpy_array(adjacency_matrix, create_using=nx.Graph())
        
        # Global efficiency
        global_efficiency = nx.global_efficiency(G)
        
        # Local efficiency
        local_efficiency = nx.local_efficiency(G)
        
        # Nodal efficiency
        nodal_efficiency = {}
        for node in G.nodes():
            neighbors = list(G.neighbors(node))
            if len(neighbors) > 1:
                subgraph = G.subgraph(neighbors)
                nodal_efficiency[node] = nx.global_efficiency(subgraph)
            else:
                nodal_efficiency[node] = 0.0
        
        return {
            'global_efficiency': global_efficiency,
            'local_efficiency': local_efficiency,
            'nodal_efficiency': nodal_efficiency,
            'avg_nodal_efficiency': np.mean(list(nodal_efficiency.values()))
        }
    
    def identify_hub_nodes(self,
                          adjacency_matrix: np.ndarray,
                          hub_criteria: str = "degree",
                          threshold_percentile: float = 90.0) -> List[int]:
        """Identify hub nodes based on various criteria."""
        G = nx.from_numpy_array(adjacency_matrix, create_using=nx.Graph())
        
        if hub_criteria == "degree":
            centrality = dict(G.degree())
        elif hub_criteria == "betweenness":
            centrality = nx.betweenness_centrality(G)
        elif hub_criteria == "eigenvector":
            centrality = nx.eigenvector_centrality(G, max_iter=1000)
        elif hub_criteria == "closeness":
            centrality = nx.closeness_centrality(G)
        else:
            raise ValueError(f"Unknown hub criteria: {hub_criteria}")
        
        # Find nodes above threshold percentile
        values = list(centrality.values())
        threshold = np.percentile(values, threshold_percentile)
        
        hub_nodes = [node for node, value in centrality.items() if value >= threshold]
        
        return hub_nodes
    
    def analyze_motifs(self, adjacency_matrix: np.ndarray, motif_size: int = 3) -> Dict[str, int]:
        """Analyze network motifs (small subgraph patterns)."""
        G = nx.from_numpy_array(adjacency_matrix, create_using=nx.DiGraph())
        
        motif_counts = {}
        
        if motif_size == 3:
            # Count 3-node motifs
            motif_types = {
                'feed_forward_loop': 0,
                'feedback_loop': 0,
                'mutual_dyad': 0,
                'chain': 0
            }
            
            for nodes in itertools.combinations(G.nodes(), 3):
                subgraph = G.subgraph(nodes)
                edges = list(subgraph.edges())
                
                if len(edges) == 3:
                    # Check for specific motif patterns
                    # This is a simplified check - full motif analysis is more complex
                    if subgraph.number_of_edges() == 3:
                        # Could be feed-forward loop or other 3-edge motif
                        motif_types['feed_forward_loop'] += 1
                elif len(edges) == 2:
                    motif_types['chain'] += 1
            
            motif_counts = motif_types
        
        return motif_counts

# ============================================================================
# Experiment Management Utilities
# ============================================================================

class ExperimentManager:
    """Comprehensive experiment management for ULTRA system."""
    
    def __init__(self, experiment_dir: str = "experiments"):
        """Initialize experiment manager."""
        self.experiment_dir = Path(experiment_dir)
        self.experiment_dir.mkdir(exist_ok=True)
        
        self.current_experiment = None
        self.experiments = {}
        self.results_cache = {}
        
    def create_experiment(self,
                         name: str,
                         description: str = "",
                         config_overrides: Optional[Dict[str, Any]] = None) -> str:
        """Create a new experiment."""
        experiment_id = f"{name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        experiment_path = self.experiment_dir / experiment_id
        experiment_path.mkdir(exist_ok=True)
        
        # Create subdirectories
        (experiment_path / "checkpoints").mkdir(exist_ok=True)
        (experiment_path / "logs").mkdir(exist_ok=True)
        (experiment_path / "results").mkdir(exist_ok=True)
        (experiment_path / "visualizations").mkdir(exist_ok=True)
        
        # Save experiment metadata
        metadata = {
            'experiment_id': experiment_id,
            'name': name,
            'description': description,
            'created_at': datetime.now().isoformat(),
            'config_overrides': config_overrides or {},
            'status': 'created'
        }
        
        with open(experiment_path / "metadata.json", 'w') as f:
            json.dump(metadata, f, indent=2)
        
        # Save configuration
        config = get_config()
        if config_overrides:
            # Apply overrides
            config_dict = asdict(config)
            self._deep_update(config_dict, config_overrides)
        else:
            config_dict = asdict(config)
            
        with open(experiment_path / "config.json", 'w') as f:
            json.dump(config_dict, f, indent=2, default=str)
        
        self.experiments[experiment_id] = {
            'metadata': metadata,
            'path': experiment_path,
            'config': config_dict
        }
        
        logger.info(f"Created experiment: {experiment_id}")
        return experiment_id
    
    def _deep_update(self, base_dict: Dict[str, Any], update_dict: Dict[str, Any]) -> None:
        """Deep update dictionary."""
        for key, value in update_dict.items():
            if key in base_dict and isinstance(base_dict[key], dict) and isinstance(value, dict):
                self._deep_update(base_dict[key], value)
            else:
                base_dict[key] = value
    
    def start_experiment(self, experiment_id: str) -> None:
        """Start an experiment."""
        if experiment_id not in self.experiments:
            raise ValueError(f"Experiment {experiment_id} not found")
        
        self.current_experiment = experiment_id
        
        # Update status
        metadata = self.experiments[experiment_id]['metadata']
        metadata['status'] = 'running'
        metadata['started_at'] = datetime.now().isoformat()
        
        # Save updated metadata
        experiment_path = self.experiments[experiment_id]['path']
        with open(experiment_path / "metadata.json", 'w') as f:
            json.dump(metadata, f, indent=2)
        
        logger.info(f"Started experiment: {experiment_id}")
    
    def log_result(self,
                   experiment_id: str,
                   result_type: str,
                   data: Any,
                   step: Optional[int] = None) -> None:
        """Log experiment result."""
        if experiment_id not in self.experiments:
            raise ValueError(f"Experiment {experiment_id} not found")
        
        experiment_path = self.experiments[experiment_id]['path']
        results_path = experiment_path / "results"
        
        # Create timestamped result entry
        timestamp = datetime.now().isoformat()
        result_entry = {
            'timestamp': timestamp,
            'step': step,
            'type': result_type,
            'data': data
        }
        
        # Save to JSON file
        result_filename = f"{result_type}_{timestamp.replace(':', '-')}.json"
        with open(results_path / result_filename, 'w') as f:
            json.dump(result_entry, f, indent=2, default=str)
        
        # Cache result
        if experiment_id not in self.results_cache:
            self.results_cache[experiment_id] = {}
        if result_type not in self.results_cache[experiment_id]:
            self.results_cache[experiment_id][result_type] = []
        
        self.results_cache[experiment_id][result_type].append(result_entry)
    
    def save_checkpoint(self,
                       experiment_id: str,
                       model_state: Dict[str, Any],
                       step: int,
                       additional_data: Optional[Dict[str, Any]] = None) -> str:
        """Save experiment checkpoint."""
        if experiment_id not in self.experiments:
            raise ValueError(f"Experiment {experiment_id} not found")
        
        experiment_path = self.experiments[experiment_id]['path']
        checkpoint_path = experiment_path / "checkpoints"
        
        checkpoint_filename = f"checkpoint_step_{step}.pkl"
        checkpoint_filepath = checkpoint_path / checkpoint_filename
        
        checkpoint_data = {
            'step': step,
            'timestamp': datetime.now().isoformat(),
            'model_state': model_state,
            'additional_data': additional_data or {}
        }
        
        with open(checkpoint_filepath, 'wb') as f:
            pickle.dump(checkpoint_data, f)
        
        logger.info(f"Saved checkpoint for experiment {experiment_id} at step {step}")
        return str(checkpoint_filepath)
    
    def load_checkpoint(self, experiment_id: str, step: Optional[int] = None) -> Dict[str, Any]:
        """Load experiment checkpoint."""
        if experiment_id not in self.experiments:
            raise ValueError(f"Experiment {experiment_id} not found")
        
        experiment_path = self.experiments[experiment_id]['path']
        checkpoint_path = experiment_path / "checkpoints"
        
        if step is None:
            # Load latest checkpoint
            checkpoint_files = list(checkpoint_path.glob("checkpoint_step_*.pkl"))
            if not checkpoint_files:
                raise FileNotFoundError(f"No checkpoints found for experiment {experiment_id}")
            
            # Sort by step number
            checkpoint_files.sort(key=lambda x: int(x.stem.split('_')[-1]))
            checkpoint_file = checkpoint_files[-1]
        else:
            checkpoint_file = checkpoint_path / f"checkpoint_step_{step}.pkl"
            if not checkpoint_file.exists():
                raise FileNotFoundError(f"Checkpoint at step {step} not found")
        
        with open(checkpoint_file, 'rb') as f:
            checkpoint_data = pickle.load(f)
        
        logger.info(f"Loaded checkpoint for experiment {experiment_id} at step {checkpoint_data['step']}")
        return checkpoint_data
    
    def finish_experiment(self, experiment_id: str, success: bool = True) -> None:
        """Finish an experiment."""
        if experiment_id not in self.experiments:
            raise ValueError(f"Experiment {experiment_id} not found")
        
        # Update status
        metadata = self.experiments[experiment_id]['metadata']
        metadata['status'] = 'completed' if success else 'failed'
        metadata['finished_at'] = datetime.now().isoformat()
        
        # Save updated metadata
        experiment_path = self.experiments[experiment_id]['path']
        with open(experiment_path / "metadata.json", 'w') as f:
            json.dump(metadata, f, indent=2)
        
        if self.current_experiment == experiment_id:
            self.current_experiment = None
        
        logger.info(f"Finished experiment: {experiment_id} ({'success' if success else 'failed'})")
    
    def get_experiment_summary(self, experiment_id: str) -> Dict[str, Any]:
        """Get experiment summary."""
        if experiment_id not in self.experiments:
            raise ValueError(f"Experiment {experiment_id} not found")
        
        experiment_data = self.experiments[experiment_id]
        experiment_path = experiment_data['path']
        
        # Count checkpoints
        checkpoint_files = list((experiment_path / "checkpoints").glob("*.pkl"))
        
        # Count results
        result_files = list((experiment_path / "results").glob("*.json"))
        
        # Get latest results from cache
        latest_results = {}
        if experiment_id in self.results_cache:
            for result_type, results in self.results_cache[experiment_id].items():
                if results:
                    latest_results[result_type] = results[-1]['data']
        
        summary = {
            'experiment_id': experiment_id,
            'metadata': experiment_data['metadata'],
            'num_checkpoints': len(checkpoint_files),
            'num_results': len(result_files),
            'latest_results': latest_results,
            'path': str(experiment_path)
        }
        
        return summary
    
    def list_experiments(self) -> List[Dict[str, Any]]:
        """List all experiments."""
        summaries = []
        for experiment_id in self.experiments:
            try:
                summary = self.get_experiment_summary(experiment_id)
                summaries.append(summary)
            except Exception as e:
                logger.warning(f"Failed to get summary for experiment {experiment_id}: {e}")
        
        return summaries
    
    def compare_experiments(self, experiment_ids: List[str], metric: str) -> Dict[str, Any]:
        """Compare experiments on a specific metric."""
        comparison = {
            'metric': metric,
            'experiments': {},
            'statistics': {}
        }
        
        values = []
        for exp_id in experiment_ids:
            if exp_id in self.results_cache and metric in self.results_cache[exp_id]:
                results = self.results_cache[exp_id][metric]
                if results:
                    latest_value = results[-1]['data']
                    comparison['experiments'][exp_id] = latest_value
                    values.append(latest_value)
        
        if values:
            comparison['statistics'] = {
                'mean': np.mean(values),
                'std': np.std(values),
                'min': np.min(values),
                'max': np.max(values),
                'best_experiment': max(comparison['experiments'].items(), key=lambda x: x[1])[0]
            }
        
        return comparison

# ============================================================================
# Testing and Validation Utilities
# ============================================================================

class TestingFramework:
    """Comprehensive testing framework for ULTRA system components."""
    
    def __init__(self):
        """Initialize testing framework."""
        self.test_results = {}
        self.test_suite = {}
        
    def register_test(self,
                     test_name: str,
                     test_function: Callable[[], bool],
                     description: str = "",
                     category: str = "general") -> None:
        """Register a test function."""
        if category not in self.test_suite:
            self.test_suite[category] = {}
        
        self.test_suite[category][test_name] = {
            'function': test_function,
            'description': description
        }
    
    def run_test(self, test_name: str, category: str = "general") -> Dict[str, Any]:
        """Run a specific test."""
        if category not in self.test_suite or test_name not in self.test_suite[category]:
            raise ValueError(f"Test {category}/{test_name} not found")
        
        test_info = self.test_suite[category][test_name]
        
        start_time = time.time()
        try:
            result = test_info['function']()
            success = bool(result)
            error_message = None
        except Exception as e:
            success = False
            error_message = str(e)
            result = None
        
        end_time = time.time()
        
        test_result = {
            'test_name': test_name,
            'category': category,
            'success': success,
            'result': result,
            'error_message': error_message,
            'duration': end_time - start_time,
            'timestamp': datetime.now().isoformat()
        }
        
        # Store result
        if category not in self.test_results:
            self.test_results[category] = {}
        self.test_results[category][test_name] = test_result
        
        return test_result
    
    def run_test_suite(self, category: Optional[str] = None) -> Dict[str, Any]:
        """Run all tests in a category or all categories."""
        if category:
            categories = [category]
        else:
            categories = list(self.test_suite.keys())
        
        suite_results = {
            'categories': {},
            'summary': {
                'total_tests': 0,
                'passed': 0,
                'failed': 0,
                'total_duration': 0.0
            }
        }
        
        for cat in categories:
            if cat not in self.test_suite:
                continue
                
            category_results = {
                'tests': {},
                'summary': {
                    'total_tests': 0,
                    'passed': 0,
                    'failed': 0,
                    'total_duration': 0.0
                }
            }
            
            for test_name in self.test_suite[cat]:
                test_result = self.run_test(test_name, cat)
                category_results['tests'][test_name] = test_result
                
                category_results['summary']['total_tests'] += 1
                if test_result['success']:
                    category_results['summary']['passed'] += 1
                else:
                    category_results['summary']['failed'] += 1
                category_results['summary']['total_duration'] += test_result['duration']
            
            suite_results['categories'][cat] = category_results
            
            # Update overall summary
            suite_results['summary']['total_tests'] += category_results['summary']['total_tests']
            suite_results['summary']['passed'] += category_results['summary']['passed']
            suite_results['summary']['failed'] += category_results['summary']['failed']
            suite_results['summary']['total_duration'] += category_results['summary']['total_duration']
        
        return suite_results
    
    def validate_neural_computation(self,
                                  membrane_potential: float,
                                  spike_threshold: float,
                                  refractory_time: float) -> bool:
        """Validate neural computation parameters."""
        try:
            # Check membrane potential is reasonable
            if not (-100 <= membrane_potential <= 100):  # mV
                return False
            
            # Check spike threshold is above resting potential
            if spike_threshold <= membrane_potential:
                return False
            
            # Check refractory time is positive
            if refractory_time <= 0:
                return False
            
            return True
        except:
            return False
    
    def validate_attention_computation(self,
                                     query: np.ndarray,
                                     key: np.ndarray,
                                     value: np.ndarray) -> bool:
        """Validate attention computation."""
        try:
            # Check dimensions
            if query.shape[-1] != key.shape[-1]:
                return False
            
            if key.shape[0] != value.shape[0]:
                return False
            
            # Check for valid values
            if not (np.isfinite(query).all() and 
                   np.isfinite(key).all() and 
                   np.isfinite(value).all()):
                return False
            
            # Compute attention weights
            attention_weights = MathUtils.attention_weights(query, key)
            
            # Validate attention weights
            return SafetyValidator.validate_attention_weights(attention_weights)[0]
            
        except:
            return False
    
    def validate_diffusion_step(self,
                               x_t: np.ndarray,
                               noise: np.ndarray,
                               beta_t: float) -> bool:
        """Validate diffusion process step."""
        try:
            # Check shapes match
            if x_t.shape != noise.shape:
                return False
            
            # Check beta is in valid range
            if not (0 < beta_t < 1):
                return False
            
            # Check for valid values
            if not (np.isfinite(x_t).all() and np.isfinite(noise).all()):
                return False
            
            # Check noise is approximately normal
            if len(noise.flatten()) > 10:
                # Kolmogorov-Smirnov test for normality
                stat, p_value = scipy.stats.kstest(noise.flatten(), 'norm')
                if p_value < 0.01:  # Very strict normality check
                    return False
            
            return True
        except:
            return False

# ============================================================================
# Global Utility Functions and Initialization
# ============================================================================

# Global instances (continued)
_data_processor = None
_network_analyzer = None
_experiment_manager = None
_testing_framework = None

def get_data_processor() -> DataProcessor:
    """Get global data processor instance."""
    global _data_processor
    if _data_processor is None:
        _data_processor = DataProcessor()
    return _data_processor

def get_network_analyzer() -> NetworkAnalyzer:
    """Get global network analyzer instance."""
    global _network_analyzer
    if _network_analyzer is None:
        _network_analyzer = NetworkAnalyzer()
    return _network_analyzer

def get_experiment_manager() -> ExperimentManager:
    """Get global experiment manager instance."""
    global _experiment_manager
    if _experiment_manager is None:
        _experiment_manager = ExperimentManager()
    return _experiment_manager

def get_testing_framework() -> TestingFramework:
    """Get global testing framework instance."""
    global _testing_framework
    if _testing_framework is None:
        _testing_framework = TestingFramework()
    return _testing_framework

def validate_ultra_installation() -> Dict[str, Any]:
    """Validate ULTRA system installation and dependencies."""
    validation_results = {
        'overall_status': 'unknown',
        'components': {},
        'dependencies': {},
        'warnings': [],
        'errors': []
    }
    
    # Check critical dependencies
    dependencies = {
        'numpy': np,
        'scipy': scipy,
        'torch': torch,
        'matplotlib': plt,
        'sklearn': None,
        'networkx': nx,
        'pandas': pd
    }
    
    for dep_name, dep_module in dependencies.items():
        try:
            if dep_module is None:
                import importlib
                dep_module = importlib.import_module(dep_name)
            
            version = getattr(dep_module, '__version__', 'unknown')
            validation_results['dependencies'][dep_name] = {
                'available': True,
                'version': version
            }
        except ImportError:
            validation_results['dependencies'][dep_name] = {
                'available': False,
                'version': None
            }
            validation_results['errors'].append(f"Missing dependency: {dep_name}")
    
    # Check system components
    try:
        config = get_config()
        validation_results['components']['config'] = {'status': 'ok'}
    except Exception as e:
        validation_results['components']['config'] = {'status': 'error', 'message': str(e)}
        validation_results['errors'].append(f"Config system error: {e}")
    
    try:
        device_manager = get_device_manager()
        device_info = device_manager.get_device_info()
        validation_results['components']['device_manager'] = {
            'status': 'ok',
            'devices': device_info
        }
    except Exception as e:
        validation_results['components']['device_manager'] = {'status': 'error', 'message': str(e)}
        validation_results['errors'].append(f"Device manager error: {e}")
    
    try:
        memory_manager = get_memory_manager()
        memory_stats = memory_manager.get_memory_stats()
        validation_results['components']['memory_manager'] = {
            'status': 'ok',
            'stats': memory_stats
        }
    except Exception as e:
        validation_results['components']['memory_manager'] = {'status': 'error', 'message': str(e)}
        validation_results['errors'].append(f"Memory manager error: {e}")
    
    # Check GPU availability
    if torch.cuda.is_available():
        validation_results['components']['cuda'] = {
            'status': 'ok',
            'device_count': torch.cuda.device_count(),
            'current_device': torch.cuda.current_device()
        }
    else:
        validation_results['components']['cuda'] = {'status': 'unavailable'}
        validation_results['warnings'].append("CUDA not available - using CPU only")
    
    # Determine overall status
    if validation_results['errors']:
        validation_results['overall_status'] = 'error'
    elif validation_results['warnings']:
        validation_results['overall_status'] = 'warning'
    else:
        validation_results['overall_status'] = 'ok'
    
    return validation_results

def run_ultra_diagnostics() -> Dict[str, Any]:
    """Run comprehensive ULTRA system diagnostics."""
    diagnostics = {
        'timestamp': datetime.now().isoformat(),
        'installation': validate_ultra_installation(),
        'performance': {},
        'system_health': {}
    }
    
    # Performance diagnostics
    try:
        # Test basic math operations
        start_time = time.time()
        test_array = np.random.randn(1000, 1000)
        result = np.dot(test_array, test_array.T)
        numpy_time = time.time() - start_time
        
        diagnostics['performance']['numpy_matmul_1000x1000'] = {
            'duration_seconds': numpy_time,
            'status': 'ok' if numpy_time < 1.0 else 'slow'
        }
        
        # Test PyTorch operations
        if torch.cuda.is_available():
            start_time = time.time()
            test_tensor = torch.randn(1000, 1000, device='cuda')
            result = torch.mm(test_tensor, test_tensor.t())
            torch.cuda.synchronize()
            torch_time = time.time() - start_time
            
            diagnostics['performance']['torch_matmul_1000x1000_cuda'] = {
                'duration_seconds': torch_time,
                'status': 'ok' if torch_time < 0.1 else 'slow'
            }
        
    except Exception as e:
        diagnostics['performance']['error'] = str(e)
    
    # System health diagnostics
    try:
        monitor = get_system_monitor()
        health_score = monitor.get_system_health_score()
        diagnostics['system_health'] = {
            'health_score': health_score,
            'status': 'ok' if health_score > 0.8 else 'warning' if health_score > 0.5 else 'critical'
        }
    except Exception as e:
        diagnostics['system_health']['error'] = str(e)
    
    return diagnostics

# Module initialization
def initialize_ultra_utils() -> None:
    """Initialize ULTRA utilities module."""
    try:
        # Setup environment
        setup_ultra_environment()
        
        # Run basic validation
        validation = validate_ultra_installation()
        if validation['overall_status'] == 'error':
            logger.error("ULTRA installation validation failed")
            for error in validation['errors']:
                logger.error(f"  - {error}")
        elif validation['overall_status'] == 'warning':
            logger.warning("ULTRA installation has warnings")
            for warning in validation['warnings']:
                logger.warning(f"  - {warning}")
        else:
            logger.info("ULTRA utilities initialized successfully")
        
        # Register basic tests
        testing_framework = get_testing_framework()
        
        # Neural computation tests
        testing_framework.register_test(
            "neural_dynamics_validation",
            lambda: testing_framework.validate_neural_computation(-70.0, -50.0, 2.0),
            "Validate neural dynamics parameters",
            "neural"
        )
        
        # Attention computation tests
        testing_framework.register_test(
            "attention_computation_validation",
            lambda: testing_framework.validate_attention_computation(
                np.random.randn(10, 64),
                np.random.randn(20, 64),
                np.random.randn(20, 64)
            ),
            "Validate attention computation",
            "transformer"
        )
        
        # Diffusion step tests
        testing_framework.register_test(
            "diffusion_step_validation",
            lambda: testing_framework.validate_diffusion_step(
                np.random.randn(100),
                np.random.randn(100),
                0.01
            ),
            "Validate diffusion process step",
            "diffusion"
        )
        
        logger.info("ULTRA utilities test suite registered")
        
    except Exception as e:
        logger.error(f"Failed to initialize ULTRA utilities: {e}")
        raise

# Initialize on import
try:
    initialize_ultra_utils()
except Exception as e:
    logger.warning(f"ULTRA utilities initialization warning: {e}")

# Export all public components
__all__ = [
    # Core classes
    'MathUtils',
    'SignalProcessor',
    'UltraLogger',
    'SystemMonitor',
    'DeviceManager',
    'MemoryManager',
    'VisualizationManager',
    'OptimizationUtils',
    'ConfigurationValidator',
    'SafetyValidator',
    'PerformanceProfiler',
    'DistributedManager',
    'DataProcessor',
    'NetworkAnalyzer',
    'ExperimentManager',
    'TestingFramework',
    
    # Enums
    'Priority',
    'DeviceType',
    'MemoryUnit',
    'SignalType',
    
    # Protocols
    'Monitorable',
    'Configurable',
    
    # Global getters
    'get_system_monitor',
    'get_device_manager',
    'get_memory_manager',
    'get_visualization_manager',
    'get_performance_profiler',
    'get_distributed_manager',
    'get_data_processor',
    'get_network_analyzer',
    'get_experiment_manager',
    'get_testing_framework',
    
    # Utility functions
    'setup_ultra_environment',
    'cleanup_ultra_environment',
    'validate_ultra_installation',
    'run_ultra_diagnostics',
    'initialize_ultra_utils',
    
    # Decorators
    'ultra_profile',
    'ultra_monitor',
    'ultra_safe_operation',
    'ultra_memory_managed',
    
    # Constants
    'EPSILON',
    'PI',
    'E',
    'GOLDEN_RATIO',
    'EULER_GAMMA'
]