#!/usr/bin/env python3
"""
ULTRA Utils Module - Complete Implementation
====================

This module provides comprehensive utility functions, classes, and algorithms
for the ULTRA (Ultimate Learning & Thought Reasoning Architecture) system.
It implements core mathematical operations, data structures, neural computation
utilities, and system-wide helper functions based on the mathematical
formulations and algorithms described in the ULTRA framework.

Author: ULTRA Development Team
License: MIT
Version: 1.0.0
"""

import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
import scipy.sparse
from scipy.special import expit, softmax
from scipy.optimize import minimize
from scipy.stats import multivariate_normal, entropy
from scipy.signal import convolve2d
from scipy.integrate import odeint
import networkx as nx
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.decomposition import PCA
from sklearn.manifold import TSNE
from sklearn.cluster import KMeans
from sklearn.metrics import mutual_info_score
from sklearn.isotonic import IsotonicRegression
import warnings
import logging
import time
import uuid
import json
import pickle
import hashlib
from typing import Dict, List, Tuple, Optional, Union, Callable, Any
from dataclasses import dataclass, field
from enum import Enum
from collections import defaultdict, deque
import threading
import multiprocessing as mp
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
import functools
import inspect
from abc import ABC, abstractmethod

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('ultra_utils.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Suppress warnings for cleaner output
warnings.filterwarnings('ignore', category=RuntimeWarning)
warnings.filterwarnings('ignore', category=UserWarning)

# =============================================================================
# Core Mathematical Utilities
# =============================================================================

class MathematicalOperations:
    """
    Advanced mathematical operations for neural computation and optimization.
    Implements core mathematical functions used throughout the ULTRA system.
    """
    
    @staticmethod
    def sigmoid(x: Union[float, np.ndarray], temperature: float = 1.0) -> Union[float, np.ndarray]:
        """
        Temperature-controlled sigmoid function.
        
        Args:
            x: Input value(s)
            temperature: Controls the steepness of the sigmoid
            
        Returns:
            Sigmoid output with temperature scaling
        """
        return expit(x / temperature)
    
    @staticmethod
    def softmax_with_temperature(x: np.ndarray, temperature: float = 1.0, axis: int = -1) -> np.ndarray:
        """
        Temperature-controlled softmax function.
        
        Args:
            x: Input array
            temperature: Controls the sharpness of the distribution
            axis: Axis along which to compute softmax
            
        Returns:
            Softmax probabilities with temperature scaling
        """
        x_scaled = x / temperature
        x_shifted = x_scaled - np.max(x_scaled, axis=axis, keepdims=True)
        exp_x = np.exp(x_shifted)
        return exp_x / np.sum(exp_x, axis=axis, keepdims=True)
    
    @staticmethod
    def gaussian_kernel(size: int, sigma: float) -> np.ndarray:
        """
        Generate 2D Gaussian kernel for spatial operations.
        
        Args:
            size: Kernel size (should be odd)
            sigma: Standard deviation of Gaussian
            
        Returns:
            2D Gaussian kernel
        """
        kernel_1d = np.linspace(-(size // 2), size // 2, size)
        x, y = np.meshgrid(kernel_1d, kernel_1d)
        kernel = np.exp(-(x**2 + y**2) / (2 * sigma**2))
        return kernel / np.sum(kernel)
    
    @staticmethod
    def gabor_filter(theta: float, sigma: float, frequency: float, size: int = 21) -> np.ndarray:
        """
        Generate Gabor filter for edge detection (used in visual cortex emulation).
        
        Args:
            theta: Orientation angle in radians
            sigma: Standard deviation of Gaussian envelope
            frequency: Frequency of sinusoidal component
            size: Filter size
            
        Returns:
            2D Gabor filter
        """
        x = np.linspace(-(size // 2), size // 2, size)
        y = np.linspace(-(size // 2), size // 2, size)
        X, Y = np.meshgrid(x, y)
        
        # Rotate coordinates
        x_rot = X * np.cos(theta) + Y * np.sin(theta)
        y_rot = -X * np.sin(theta) + Y * np.cos(theta)
        
        # Gabor function
        gaussian = np.exp(-(x_rot**2 + y_rot**2) / (2 * sigma**2))
        sinusoid = np.cos(2 * np.pi * frequency * x_rot)
        
        return gaussian * sinusoid
    
    @staticmethod
    def mutual_information(X: np.ndarray, Y: np.ndarray, bins: int = 50) -> float:
        """
        Calculate mutual information between two variables.
        Used in integrated information calculations.
        
        Args:
            X: First variable
            Y: Second variable
            bins: Number of bins for discretization
            
        Returns:
            Mutual information in bits
        """
        # Discretize continuous variables
        X_discrete = np.digitize(X, np.histogram(X, bins=bins)[1])
        Y_discrete = np.digitize(Y, np.histogram(Y, bins=bins)[1])
        
        return mutual_info_score(X_discrete, Y_discrete)
    
    @staticmethod
    def integrated_information_phi(connectivity_matrix: np.ndarray, 
                                  states: np.ndarray) -> float:
        """
        Calculate integrated information Φ based on connectivity and states.
        Implements core IIT (Integrated Information Theory) measure.
        
        Args:
            connectivity_matrix: Network connectivity matrix
            states: Current states of network nodes
            
        Returns:
            Φ (phi) value representing integrated information
        """
        n_nodes = connectivity_matrix.shape[0]
        if n_nodes < 2:
            return 0.0
        
        # Calculate effective information for the whole system
        total_ei = MathematicalOperations.effective_information(
            connectivity_matrix, states
        )
        
        # Find minimum information partition (MIP)
        min_phi = float('inf')
        
        # Try all possible bipartitions
        for partition_size in range(1, n_nodes):
            for partition in itertools.combinations(range(n_nodes), partition_size):
                subset1 = list(partition)
                subset2 = [i for i in range(n_nodes) if i not in subset1]
                
                # Calculate cross-partition information
                cross_info = MathematicalOperations.cross_partition_information(
                    connectivity_matrix, states, subset1, subset2
                )
                
                min_phi = min(min_phi, cross_info)
        
        return max(0, min_phi)
    
    @staticmethod
    def effective_information(connectivity_matrix: np.ndarray, 
                            states: np.ndarray) -> float:
        """
        Calculate effective information of a network.
        
        Args:
            connectivity_matrix: Network connectivity
            states: Node states
            
        Returns:
            Effective information value
        """
        # Implement transition probability matrix
        n_states = 2 ** len(states)  # Binary states assumption
        transition_matrix = np.zeros((n_states, n_states))
        
        # Calculate transition probabilities based on connectivity
        for i in range(n_states):
            current_state = [(i >> j) & 1 for j in range(len(states))]
            next_state_probs = MathematicalOperations.next_state_probabilities(
                connectivity_matrix, current_state
            )
            
            for j in range(n_states):
                next_state = [(j >> k) & 1 for k in range(len(states))]
                transition_matrix[i, j] = np.prod([
                    next_state_probs[k] if next_state[k] else (1 - next_state_probs[k])
                    for k in range(len(states))
                ])
        
        # Calculate effective information as entropy difference
        marginal_entropy = entropy(np.mean(transition_matrix, axis=0))
        conditional_entropy = np.mean([
            entropy(transition_matrix[i, :]) for i in range(n_states)
        ])
        
        return marginal_entropy - conditional_entropy
    
    @staticmethod
    def next_state_probabilities(connectivity_matrix: np.ndarray, 
                               current_state: List[int]) -> np.ndarray:
        """
        Calculate next state probabilities given current state and connectivity.
        
        Args:
            connectivity_matrix: Network connectivity
            current_state: Current binary state vector
            
        Returns:
            Probabilities for next state
        """
        current_state = np.array(current_state)
        inputs = np.dot(connectivity_matrix, current_state)
        
        # Use sigmoid activation for probabilistic transitions
        return MathematicalOperations.sigmoid(inputs)
    
    @staticmethod
    def cross_partition_information(connectivity_matrix: np.ndarray,
                                  states: np.ndarray,
                                  subset1: List[int],
                                  subset2: List[int]) -> float:
        """
        Calculate information flow across a partition.
        
        Args:
            connectivity_matrix: Network connectivity
            states: Current states
            subset1: First subset of nodes
            subset2: Second subset of nodes
            
        Returns:
            Cross-partition information
        """
        # Extract cross-partition connections
        cross_connections = connectivity_matrix[np.ix_(subset1, subset2)]
        
        # Calculate mutual information between partitions
        subset1_activity = np.mean(states[subset1])
        subset2_activity = np.mean(states[subset2])
        
        # Simplified cross-partition information measure
        connection_strength = np.sum(np.abs(cross_connections))
        activity_correlation = subset1_activity * subset2_activity
        
        return connection_strength * activity_correlation

# =============================================================================
# Neuromorphic Computation Utilities
# =============================================================================

class NeuromorphicUtils:
    """
    Utilities for neuromorphic computation including spiking neural networks,
    STDP learning, and biological timing mechanisms.
    """
    
    @staticmethod
    def lif_dynamics(V: float, I: float, tau: float = 20.0, V_rest: float = 0.0, 
                    R: float = 1.0, dt: float = 1.0) -> float:
        """
        Leaky Integrate-and-Fire neuron dynamics.
        
        Implements: τ dV/dt = -(V - V_rest) + R*I
        
        Args:
            V: Current membrane potential
            I: Input current
            tau: Membrane time constant
            V_rest: Resting potential
            R: Membrane resistance
            dt: Time step
            
        Returns:
            Updated membrane potential
        """
        dV = (-(V - V_rest) + R * I) / tau
        return V + dV * dt
    
    @staticmethod
    def adex_dynamics(V: float, w: float, I: float, 
                     tau_m: float = 20.0, tau_w: float = 30.0,
                     E_L: float = -70.0, V_T: float = -50.0,
                     Delta_T: float = 2.0, a: float = 2.0,
                     R: float = 1.0, dt: float = 1.0) -> Tuple[float, float]:
        """
        Adaptive Exponential Integrate-and-Fire neuron dynamics.
        
        Implements:
        τ_m dV/dt = -(V - E_L) + Δ_T exp((V - V_T)/Δ_T) - w + R*I
        τ_w dw/dt = a(V - E_L) - w
        
        Args:
            V: Membrane potential
            w: Adaptation variable
            I: Input current
            tau_m: Membrane time constant
            tau_w: Adaptation time constant
            E_L: Leak reversal potential
            V_T: Threshold potential
            Delta_T: Slope factor
            a: Subthreshold adaptation
            R: Membrane resistance
            dt: Time step
            
        Returns:
            Updated (V, w) values
        """
        # Exponential term with numerical stability
        exp_term = Delta_T * np.exp(min((V - V_T) / Delta_T, 20))
        
        dV = (-(V - E_L) + exp_term - w + R * I) / tau_m
        dw = (a * (V - E_L) - w) / tau_w
        
        return V + dV * dt, w + dw * dt
    
    @staticmethod
    def izhikevich_dynamics(v: float, u: float, I: float,
                           a: float = 0.02, b: float = 0.2,
                           c: float = -65.0, d: float = 8.0,
                           dt: float = 1.0) -> Tuple[float, float]:
        """
        Izhikevich neuron model dynamics.
        
        Implements:
        dv/dt = 0.04v² + 5v + 140 - u + I
        du/dt = a(bv - u)
        
        Args:
            v: Membrane potential
            u: Recovery variable
            I: Input current
            a, b, c, d: Model parameters
            dt: Time step
            
        Returns:
            Updated (v, u) values
        """
        dv = (0.04 * v**2 + 5 * v + 140 - u + I) * dt
        du = a * (b * v - u) * dt
        
        return v + dv, u + du
    
    @staticmethod
    def stdp_weight_update(delta_t: float, A_plus: float = 1.0, A_minus: float = 1.0,
                          tau_plus: float = 20.0, tau_minus: float = 20.0) -> float:
        """
        Spike-Timing-Dependent Plasticity weight update.
        
        Implements:
        Δw = A_+ exp(-Δt/τ_+) if Δt > 0
        Δw = -A_- exp(Δt/τ_-) if Δt < 0
        
        Args:
            delta_t: Time difference (t_post - t_pre)
            A_plus: LTP amplitude
            A_minus: LTD amplitude
            tau_plus: LTP time constant
            tau_minus: LTD time constant
            
        Returns:
            Weight change
        """
        if delta_t > 0:
            return A_plus * np.exp(-delta_t / tau_plus)
        elif delta_t < 0:
            return -A_minus * np.exp(delta_t / tau_minus)
        else:
            return 0.0
    
    @staticmethod
    def homeostatic_scaling(weights: np.ndarray, target_activity: float,
                           current_activity: float, eta: float = 0.01) -> np.ndarray:
        """
        Homeostatic synaptic scaling to maintain target activity.
        
        Args:
            weights: Current synaptic weights
            target_activity: Desired activity level
            current_activity: Current activity level
            eta: Learning rate
            
        Returns:
            Scaled weights
        """
        scaling_factor = 1 + eta * (target_activity - current_activity)
        return weights * scaling_factor
    
    @staticmethod
    def synaptic_response(t: float, tau: float = 5.0) -> float:
        """
        Synaptic response function (alpha function).
        
        Implements: α(t) = (t/τ) * exp(1 - t/τ) for t > 0
        
        Args:
            t: Time since spike
            tau: Synaptic time constant
            
        Returns:
            Synaptic response amplitude
        """
        if t <= 0:
            return 0.0
        return (t / tau) * np.exp(1 - t / tau)
    
    @staticmethod
    def generate_poisson_spikes(rate: float, duration: float, dt: float = 1.0) -> np.ndarray:
        """
        Generate Poisson spike train.
        
        Args:
            rate: Firing rate in Hz
            duration: Duration in ms
            dt: Time step in ms
            
        Returns:
            Binary spike train
        """
        n_steps = int(duration / dt)
        prob = rate * dt / 1000.0  # Convert to probability per time step
        return np.random.random(n_steps) < prob
    
    @staticmethod
    def membrane_noise(sigma: float, dt: float = 1.0) -> float:
        """
        Generate membrane noise for realistic neural dynamics.
        
        Args:
            sigma: Noise standard deviation
            dt: Time step
            
        Returns:
            Noise value
        """
        return np.random.normal(0, sigma * np.sqrt(dt))

# =============================================================================
# Diffusion Process Utilities
# =============================================================================

class DiffusionUtils:
    """
    Utilities for diffusion-based reasoning and conceptual space navigation.
    Implements the mathematical foundation for thought latent space operations.
    """
    
    @staticmethod
    def forward_diffusion_step(x_0: np.ndarray, t: int, beta_schedule: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        Single step of forward diffusion process.
        
        Implements: q(x_t|x_0) = N(x_t; √ᾱ_t x_0, (1-ᾱ_t)I)
        
        Args:
            x_0: Original concept representation
            t: Time step
            beta_schedule: Noise schedule
            
        Returns:
            Noisy concept and noise added
        """
        alpha_t = 1 - beta_schedule[t]
        alpha_cumprod_t = np.prod(1 - beta_schedule[:t+1])
        
        noise = np.random.normal(size=x_0.shape)
        sqrt_alpha_cumprod = np.sqrt(alpha_cumprod_t)
        sqrt_one_minus_alpha_cumprod = np.sqrt(1 - alpha_cumprod_t)
        
        x_t = sqrt_alpha_cumprod * x_0 + sqrt_one_minus_alpha_cumprod * noise
        
        return x_t, noise
    
    @staticmethod
    def reverse_diffusion_step(x_t: np.ndarray, t: int, predicted_noise: np.ndarray,
                             beta_schedule: np.ndarray) -> np.ndarray:
        """
        Single step of reverse diffusion process.
        
        Args:
            x_t: Current noisy concept
            t: Time step
            predicted_noise: Predicted noise from model
            beta_schedule: Noise schedule
            
        Returns:
            Denoised concept
        """
        beta_t = beta_schedule[t]
        alpha_t = 1 - beta_t
        alpha_cumprod_t = np.prod(1 - beta_schedule[:t+1])
        alpha_cumprod_prev = np.prod(1 - beta_schedule[:t]) if t > 0 else 1.0
        
        # Calculate mean of reverse process
        sqrt_alpha_t = np.sqrt(alpha_t)
        sqrt_one_minus_alpha_cumprod_t = np.sqrt(1 - alpha_cumprod_t)
        
        x_0_pred = (x_t - sqrt_one_minus_alpha_cumprod_t * predicted_noise) / np.sqrt(alpha_cumprod_t)
        
        # Calculate posterior mean
        posterior_mean_coeff1 = np.sqrt(alpha_cumprod_prev) * beta_t / (1 - alpha_cumprod_t)
        posterior_mean_coeff2 = np.sqrt(alpha_t) * (1 - alpha_cumprod_prev) / (1 - alpha_cumprod_t)
        
        mean = posterior_mean_coeff1 * x_0_pred + posterior_mean_coeff2 * x_t
        
        # Add noise for sampling
        if t > 0:
            posterior_variance = beta_t * (1 - alpha_cumprod_prev) / (1 - alpha_cumprod_t)
            noise = np.random.normal(size=x_t.shape)
            return mean + np.sqrt(posterior_variance) * noise
        else:
            return mean
    
    @staticmethod
    def create_beta_schedule(num_timesteps: int, schedule_type: str = "linear",
                           beta_start: float = 1e-4, beta_end: float = 0.02) -> np.ndarray:
        """
        Create noise schedule for diffusion process.
        
        Args:
            num_timesteps: Number of diffusion steps
            schedule_type: Type of schedule ('linear', 'cosine', 'sqrt')
            beta_start: Initial noise level
            beta_end: Final noise level
            
        Returns:
            Beta schedule array
        """
        if schedule_type == "linear":
            return np.linspace(beta_start, beta_end, num_timesteps)
        elif schedule_type == "cosine":
            s = 0.008
            steps = np.arange(num_timesteps + 1)
            alphas_cumprod = np.cos(((steps / num_timesteps) + s) / (1 + s) * np.pi * 0.5) ** 2
            alphas_cumprod = alphas_cumprod / alphas_cumprod[0]
            betas = 1 - (alphas_cumprod[1:] / alphas_cumprod[:-1])
            return np.clip(betas, 0, 0.999)
        elif schedule_type == "sqrt":
            return np.sqrt(np.linspace(beta_start**2, beta_end**2, num_timesteps))
        else:
            raise ValueError(f"Unknown schedule type: {schedule_type}")
    
    @staticmethod
    def thought_space_distance(concept1: np.ndarray, concept2: np.ndarray,
                              metric: str = "cosine") -> float:
        """
        Calculate distance between concepts in thought space.
        
        Args:
            concept1: First concept vector
            concept2: Second concept vector
            metric: Distance metric ('cosine', 'euclidean', 'manhattan')
            
        Returns:
            Distance value
        """
        if metric == "cosine":
            return 1 - np.dot(concept1, concept2) / (np.linalg.norm(concept1) * np.linalg.norm(concept2))
        elif metric == "euclidean":
            return np.linalg.norm(concept1 - concept2)
        elif metric == "manhattan":
            return np.sum(np.abs(concept1 - concept2))
        else:
            raise ValueError(f"Unknown metric: {metric}")
    
    @staticmethod
    def concept_interpolation(concept1: np.ndarray, concept2: np.ndarray,
                            alpha: float) -> np.ndarray:
        """
        Interpolate between two concepts in thought space.
        
        Args:
            concept1: First concept
            concept2: Second concept
            alpha: Interpolation factor (0 = concept1, 1 = concept2)
            
        Returns:
            Interpolated concept
        """
        # Spherical linear interpolation for normalized vectors
        if np.allclose(np.linalg.norm(concept1), 1.0) and np.allclose(np.linalg.norm(concept2), 1.0):
            omega = np.arccos(np.clip(np.dot(concept1, concept2), -1.0, 1.0))
            if np.abs(omega) < 1e-6:
                return concept1
            sin_omega = np.sin(omega)
            return (np.sin((1-alpha) * omega) * concept1 + np.sin(alpha * omega) * concept2) / sin_omega
        else:
            # Linear interpolation for non-normalized vectors
            return (1 - alpha) * concept1 + alpha * concept2
    
    @staticmethod
    def concept_composition(concepts: List[np.ndarray], weights: Optional[np.ndarray] = None,
                          operation: str = "weighted_sum") -> np.ndarray:
        """
        Compose multiple concepts into a new concept.
        
        Args:
            concepts: List of concept vectors
            weights: Composition weights (default: equal weights)
            operation: Composition operation ('weighted_sum', 'product', 'max')
            
        Returns:
            Composed concept
        """
        if weights is None:
            weights = np.ones(len(concepts)) / len(concepts)
        
        concepts_array = np.array(concepts)
        
        if operation == "weighted_sum":
            return np.sum(weights[:, np.newaxis] * concepts_array, axis=0)
        elif operation == "product":
            result = concepts_array[0].copy()
            for i in range(1, len(concepts)):
                result = result * concepts_array[i] * weights[i]
            return result
        elif operation == "max":
            return np.max(weights[:, np.newaxis] * concepts_array, axis=0)
        else:
            raise ValueError(f"Unknown operation: {operation}")

# =============================================================================
# Attention and Transformer Utilities
# =============================================================================

class AttentionUtils:
    """
    Utilities for attention mechanisms, transformers, and dynamic attention.
    """
    
    @staticmethod
    def scaled_dot_product_attention(query: np.ndarray, key: np.ndarray, value: np.ndarray,
                                   mask: Optional[np.ndarray] = None,
                                   temperature: float = 1.0) -> Tuple[np.ndarray, np.ndarray]:
        """
        Scaled dot-product attention with temperature control.
        
        Implements: Attention(Q,K,V) = softmax(QK^T/√d_k)V
        
        Args:
            query: Query matrix [seq_len, d_k]
            key: Key matrix [seq_len, d_k]
            value: Value matrix [seq_len, d_v]
            mask: Attention mask (optional)
            temperature: Temperature for softmax
            
        Returns:
            Attention output and attention weights
        """
        d_k = query.shape[-1]
        scores = np.dot(query, key.T) / (np.sqrt(d_k) * temperature)
        
        if mask is not None:
            scores = np.where(mask, scores, -np.inf)
        
        attention_weights = softmax(scores, axis=-1)
        output = np.dot(attention_weights, value)
        
        return output, attention_weights
    
    @staticmethod
    def multi_head_attention(query: np.ndarray, key: np.ndarray, value: np.ndarray,
                           num_heads: int, d_model: int,
                           mask: Optional[np.ndarray] = None) -> np.ndarray:
        """
        Multi-head attention mechanism.
        
        Args:
            query: Query tensor
            key: Key tensor
            value: Value tensor
            num_heads: Number of attention heads
            d_model: Model dimension
            mask: Attention mask
            
        Returns:
            Multi-head attention output
        """
        d_k = d_model // num_heads
        seq_len = query.shape[0]
        
        # Reshape for multi-head processing
        Q = query.reshape(seq_len, num_heads, d_k).transpose(1, 0, 2)
        K = key.reshape(seq_len, num_heads, d_k).transpose(1, 0, 2)
        V = value.reshape(seq_len, num_heads, d_k).transpose(1, 0, 2)
        
        # Apply attention to each head
        head_outputs = []
        for h in range(num_heads):
            head_output, _ = AttentionUtils.scaled_dot_product_attention(
                Q[h], K[h], V[h], mask
            )
            head_outputs.append(head_output)
        
        # Concatenate heads
        concat_output = np.concatenate(head_outputs, axis=-1)
        
        return concat_output
    
    @staticmethod
    def dynamic_attention_mask(query: np.ndarray, key: np.ndarray,
                             context: np.ndarray, threshold: float = 0.5) -> np.ndarray:
        """
        Generate dynamic attention mask based on context.
        
        Args:
            query: Query vectors
            key: Key vectors
            context: Context information
            threshold: Masking threshold
            
        Returns:
            Dynamic attention mask
        """
        # Calculate context-dependent relevance scores
        context_scores = np.dot(key, context) / np.linalg.norm(context)
        
        # Create mask based on threshold
        mask = context_scores > threshold
        
        return mask
    
    @staticmethod
    def attention_entropy(attention_weights: np.ndarray) -> float:
        """
        Calculate entropy of attention distribution.
        
        Args:
            attention_weights: Attention weight matrix
            
        Returns:
            Attention entropy
        """
        # Calculate entropy for each query position
        entropies = []
        for i in range(attention_weights.shape[0]):
            weights = attention_weights[i]
            weights = weights[weights > 1e-12]  # Avoid log(0)
            entropies.append(-np.sum(weights * np.log(weights)))
        
        return np.mean(entropies)
    
    @staticmethod
    def sparse_attention(query: np.ndarray, key: np.ndarray, value: np.ndarray,
                        sparsity_pattern: str = "local", window_size: int = 32) -> np.ndarray:
        """
        Sparse attention for computational efficiency.
        
        Args:
            query: Query matrix
            key: Key matrix
            value: Value matrix
            sparsity_pattern: Pattern type ('local', 'strided', 'random')
            window_size: Size of attention window
            
        Returns:
            Sparse attention output
        """
        seq_len = query.shape[0]
        
        if sparsity_pattern == "local":
            # Local windowed attention
            mask = np.zeros((seq_len, seq_len), dtype=bool)
            for i in range(seq_len):
                start = max(0, i - window_size // 2)
                end = min(seq_len, i + window_size // 2 + 1)
                mask[i, start:end] = True
        
        elif sparsity_pattern == "strided":
            # Strided attention
            mask = np.zeros((seq_len, seq_len), dtype=bool)
            stride = window_size
            for i in range(seq_len):
                mask[i, ::stride] = True
                mask[i, i] = True  # Always attend to self
        
        elif sparsity_pattern == "random":
            # Random sparse attention
            mask = np.random.random((seq_len, seq_len)) < (window_size / seq_len)
            np.fill_diagonal(mask, True)  # Always attend to self
        
        else:
            raise ValueError(f"Unknown sparsity pattern: {sparsity_pattern}")
        
        output, _ = AttentionUtils.scaled_dot_product_attention(
            query, key, value, mask
        )
        
        return output

# =============================================================================
# Graph and Network Utilities
# =============================================================================

class GraphUtils:
    """
    Utilities for graph-based reasoning, network analysis, and connectivity patterns.
    """
    
    @staticmethod
    def create_small_world_network(n_nodes: int, k: int, p: float) -> np.ndarray:
        """
        Create small-world network using Watts-Strogatz model.
        
        Args:
            n_nodes: Number of nodes
            k: Each node connected to k nearest neighbors
            p: Probability of rewiring each edge
            
        Returns:
            Adjacency matrix
        """
        # Start with regular ring lattice
        adj_matrix = np.zeros((n_nodes, n_nodes))
        
        for i in range(n_nodes):
            for j in range(1, k // 2 + 1):
                adj_matrix[i, (i + j) % n_nodes] = 1
                adj_matrix[i, (i - j) % n_nodes] = 1
        
        # Rewire edges with probability p
        for i in range(n_nodes):
            neighbors = np.where(adj_matrix[i] == 1)[0]
            for neighbor in neighbors:
                if np.random.random() < p:
                    # Remove original edge
                    adj_matrix[i, neighbor] = 0
                    adj_matrix[neighbor, i] = 0
                    
                    # Add new random edge
                    possible_targets = [j for j in range(n_nodes) 
                                      if j != i and adj_matrix[i, j] == 0]
                    if possible_targets:
                        new_target = np.random.choice(possible_targets)
                        adj_matrix[i, new_target] = 1
                        adj_matrix[new_target, i] = 1
        
        return adj_matrix
    
    @staticmethod
    def create_scale_free_network(n_nodes: int, m: int = 2) -> np.ndarray:
        """
        Create scale-free network using Barabási-Albert model.
        
        Args:
            n_nodes: Number of nodes
            m: Number of edges to attach from a new node
            
        Returns:
            Adjacency matrix
        """
        G = nx.barabasi_albert_graph(n_nodes, m)
        return nx.adjacency_matrix(G).toarray()
    
    @staticmethod
    def calculate_network_metrics(adj_matrix: np.ndarray) -> Dict[str, float]:
        """
        Calculate various network topology metrics.
        
        Args:
            adj_matrix: Adjacency matrix
            
        Returns:
            Dictionary of network metrics
        """
        G = nx.from_numpy_array(adj_matrix)
        
        metrics = {}
        
        # Basic metrics
        metrics['density'] = nx.density(G)
        metrics['average_clustering'] = nx.average_clustering(G)
        
        # Path-based metrics
        if nx.is_connected(G):
            metrics['average_path_length'] = nx.average_shortest_path_length(G)
            metrics['diameter'] = nx.diameter(G)
        else:
            # For disconnected graphs, use largest component
            largest_cc = max(nx.connected_components(G), key=len)
            subgraph = G.subgraph(largest_cc)
            metrics['average_path_length'] = nx.average_shortest_path_length(subgraph)
            metrics['diameter'] = nx.diameter(subgraph)
        
        # Centrality measures
        degree_centrality = nx.degree_centrality(G)
        betweenness_centrality = nx.betweenness_centrality(G)
        closeness_centrality = nx.closeness_centrality(G)
        
        metrics['max_degree_centrality'] = max(degree_centrality.values())
        metrics['max_betweenness_centrality'] = max(betweenness_centrality.values())
        metrics['max_closeness_centrality'] = max(closeness_centrality.values())
        
        # Small-world metrics
        random_graph = nx.erdos_renyi_graph(len(G), nx.density(G))
        random_clustering = nx.average_clustering(random_graph)
        random_path_length = nx.average_shortest_path_length(random_graph) if nx.is_connected(random_graph) else float('inf')
        
        if random_clustering > 0 and random_path_length < float('inf'):
            metrics['small_worldness'] = (metrics['average_clustering'] / random_clustering) / (metrics['average_path_length'] / random_path_length)
        else:
            metrics['small_worldness'] = 0.0
        
        return metrics
    
    @staticmethod
    def detect_communities(adj_matrix: np.ndarray, method: str = "modularity") -> np.ndarray:
        """
        Detect communities in the network.
        
        Args:
            adj_matrix: Adjacency matrix
            method: Community detection method
            
        Returns:
            Community assignment array
        """
        G = nx.from_numpy_array(adj_matrix)
        
        if method == "modularity":
            communities = nx.community.greedy_modularity_communities(G)
        elif method == "louvain":
            # Simplified Louvain-like algorithm
            communities = nx.community.label_propagation_communities(G)
        else:
            raise ValueError(f"Unknown method: {method}")
        
        # Convert to array format
        community_assignment = np.zeros(len(G))
        for i, community in enumerate(communities):
            for node in community:
                community_assignment[node] = i
        
        return community_assignment
    
    @staticmethod
    def calculate_graph_spectrum(adj_matrix: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        Calculate the spectral properties of the graph.
        
        Args:
            adj_matrix: Adjacency matrix
            
        Returns:
            Eigenvalues and eigenvectors
        """
        eigenvalues, eigenvectors = np.linalg.eigh(adj_matrix)
        
        # Sort by eigenvalue magnitude
        idx = np.argsort(np.abs(eigenvalues))[::-1]
        eigenvalues = eigenvalues[idx]
        eigenvectors = eigenvectors[:, idx]
        
        return eigenvalues, eigenvectors
    
    @staticmethod
    def propagate_activation(adj_matrix: np.ndarray, initial_activation: np.ndarray,
                           steps: int, decay: float = 0.9) -> np.ndarray:
        """
        Propagate activation through the network.
        
        Args:
            adj_matrix: Network connectivity
            initial_activation: Initial node activations
            steps: Number of propagation steps
            decay: Activation decay factor
            
        Returns:
            Final activation pattern
        """
        # Normalize adjacency matrix
        degree = np.sum(adj_matrix, axis=1)
        degree[degree == 0] = 1  # Avoid division by zero
        normalized_adj = adj_matrix / degree[:, np.newaxis]
        
        activation = initial_activation.copy()
        
        for _ in range(steps):
            new_activation = decay * np.dot(normalized_adj, activation)
            activation = new_activation
        
        return activation

# =============================================================================
# Optimization and Learning Utilities
# =============================================================================

class OptimizationUtils:
    """
    Utilities for optimization, meta-learning, and adaptive algorithms.
    """
    
    @staticmethod
    def adam_optimizer(params: np.ndarray, grads: np.ndarray, m: np.ndarray, v: np.ndarray,
                      t: int, lr: float = 0.001, beta1: float = 0.9, beta2: float = 0.999,
                      epsilon: float = 1e-8) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """
        Adam optimizer implementation.
        
        Args:
            params: Parameters to optimize
            grads: Gradients
            m: First moment estimate
            v: Second moment estimate
            t: Time step
            lr: Learning rate
            beta1: First moment decay
            beta2: Second moment decay
            epsilon: Numerical stability constant
            
        Returns:
            Updated parameters, m, and v
        """
        # Update biased first moment estimate
        m = beta1 * m + (1 - beta1) * grads
        
        # Update biased second moment estimate
        v = beta2 * v + (1 - beta2) * (grads ** 2)
        
        # Compute bias-corrected estimates
        m_hat = m / (1 - beta1 ** t)
        v_hat = v / (1 - beta2 ** t)
        
        # Update parameters
        params = params - lr * m_hat / (np.sqrt(v_hat) + epsilon)
        
        return params, m, v
    
    @staticmethod
    def rmsprop_optimizer(params: np.ndarray, grads: np.ndarray, v: np.ndarray,
                         lr: float = 0.001, alpha: float = 0.99, epsilon: float = 1e-8) -> Tuple[np.ndarray, np.ndarray]:
        """
        RMSprop optimizer implementation.
        
        Args:
            params: Parameters to optimize
            grads: Gradients
            v: Moving average of squared gradients
            lr: Learning rate
            alpha: Smoothing constant
            epsilon: Numerical stability constant
            
        Returns:
            Updated parameters and v
        """
        # Update moving average of squared gradients
        v = alpha * v + (1 - alpha) * (grads ** 2)
        
        # Update parameters
        params = params - lr * grads / (np.sqrt(v) + epsilon)
        
        return params, v
    
    @staticmethod
    def evolutionary_search(population: np.ndarray, fitness_scores: np.ndarray,
                          mutation_rate: float = 0.01, crossover_rate: float = 0.7,
                          selection_pressure: float = 2.0) -> np.ndarray:
        """
        Evolutionary algorithm for optimization.
        
        Args:
            population: Current population
            fitness_scores: Fitness scores for each individual
            mutation_rate: Probability of mutation
            crossover_rate: Probability of crossover
            selection_pressure: Selection pressure parameter
            
        Returns:
            Next generation population
        """
        pop_size, genome_length = population.shape
        
        # Selection using tournament selection
        new_population = np.zeros_like(population)
        
        for i in range(pop_size):
            # Tournament selection
            tournament_size = max(2, int(pop_size / selection_pressure))
            tournament_indices = np.random.choice(pop_size, tournament_size, replace=False)
            tournament_fitness = fitness_scores[tournament_indices]
            winner_idx = tournament_indices[np.argmax(tournament_fitness)]
            
            new_population[i] = population[winner_idx].copy()
        
        # Crossover
        for i in range(0, pop_size - 1, 2):
            if np.random.random() < crossover_rate:
                crossover_point = np.random.randint(1, genome_length)
                
                # Single-point crossover
                child1 = new_population[i].copy()
                child2 = new_population[i + 1].copy()
                
                child1[crossover_point:] = new_population[i + 1][crossover_point:]
                child2[crossover_point:] = new_population[i][crossover_point:]
                
                new_population[i] = child1
                new_population[i + 1] = child2
        
        # Mutation
        for i in range(pop_size):
            for j in range(genome_length):
                if np.random.random() < mutation_rate:
                    new_population[i, j] += np.random.normal(0, 0.1)
        
        return new_population
    
    @staticmethod
    def bayesian_optimization_acquisition(mu: np.ndarray, sigma: np.ndarray,
                                        best_value: float, xi: float = 0.01,
                                        acquisition_type: str = "ei") -> np.ndarray:
        """
        Bayesian optimization acquisition function.
        
        Args:
            mu: Posterior mean
            sigma: Posterior standard deviation
            best_value: Best observed value so far
            xi: Exploration parameter
            acquisition_type: Type of acquisition function
            
        Returns:
            Acquisition values
        """
        if acquisition_type == "ei":  # Expected Improvement
            improvement = mu - best_value - xi
            Z = improvement / (sigma + 1e-9)
            ei = improvement * norm.cdf(Z) + sigma * norm.pdf(Z)
            return ei
        
        elif acquisition_type == "ucb":  # Upper Confidence Bound
            return mu + 2.0 * sigma
        
        elif acquisition_type == "pi":  # Probability of Improvement
            improvement = mu - best_value - xi
            Z = improvement / (sigma + 1e-9)
            return norm.cdf(Z)
        
        else:
            raise ValueError(f"Unknown acquisition type: {acquisition_type}")
    
    @staticmethod
    def meta_learning_maml_update(params: Dict[str, np.ndarray], 
                                 support_grads: Dict[str, np.ndarray],
                                 query_grads: Dict[str, np.ndarray],
                                 alpha: float = 0.01, beta: float = 0.001) -> Dict[str, np.ndarray]:
        """
        Model-Agnostic Meta-Learning (MAML) parameter update.
        
        Args:
            params: Current parameters
            support_grads: Gradients from support set
            query_grads: Gradients from query set
            alpha: Inner loop learning rate
            beta: Outer loop learning rate
            
        Returns:
            Updated parameters
        """
        updated_params = {}
        
        for key in params:
            # Inner loop update
            adapted_params = params[key] - alpha * support_grads[key]
            
            # Outer loop update
            updated_params[key] = params[key] - beta * query_grads[key]
        
        return updated_params
    
    @staticmethod
    def learning_rate_schedule(step: int, schedule_type: str = "cosine",
                              initial_lr: float = 0.001, final_lr: float = 1e-6,
                              total_steps: int = 10000) -> float:
        """
        Learning rate scheduling.
        
        Args:
            step: Current step
            schedule_type: Type of schedule
            initial_lr: Initial learning rate
            final_lr: Final learning rate
            total_steps: Total number of steps
            
        Returns:
            Current learning rate
        """
        if schedule_type == "cosine":
            progress = step / total_steps
            lr = final_lr + (initial_lr - final_lr) * (1 + np.cos(np.pi * progress)) / 2
        
        elif schedule_type == "exponential":
            decay_rate = (final_lr / initial_lr) ** (1 / total_steps)
            lr = initial_lr * (decay_rate ** step)
        
        elif schedule_type == "linear":
            progress = step / total_steps
            lr = initial_lr * (1 - progress) + final_lr * progress
        
        elif schedule_type == "polynomial":
            power = 2.0
            progress = min(step / total_steps, 1.0)
            lr = (initial_lr - final_lr) * (1 - progress) ** power + final_lr
        
        else:
            raise ValueError(f"Unknown schedule type: {schedule_type}")
        
        return max(lr, final_lr)

# =============================================================================
# Data Structures and Memory Management
# =============================================================================

class MemoryManager:
    """
    Advanced memory management for episodic, semantic, and procedural knowledge.
    """
    
    def __init__(self, capacity: int = 10000, decay_rate: float = 0.01):
        self.capacity = capacity
        self.decay_rate = decay_rate
        self.memories = {}
        self.access_counts = {}
        self.last_access = {}
        self.memory_strength = {}
        self.timestamp = 0
    
    def store_memory(self, key: str, data: Any, memory_type: str = "episodic",
                    importance: float = 1.0) -> None:
        """
        Store a memory with associated metadata.
        
        Args:
            key: Memory identifier
            data: Memory content
            memory_type: Type of memory ('episodic', 'semantic', 'procedural')
            importance: Initial importance score
        """
        if len(self.memories) >= self.capacity:
            self._forget_least_important()
        
        self.memories[key] = {
            'data': data,
            'type': memory_type,
            'created': self.timestamp,
            'importance': importance
        }
        
        self.access_counts[key] = 1
        self.last_access[key] = self.timestamp
        self.memory_strength[key] = importance
        
        self.timestamp += 1
    
    def retrieve_memory(self, key: str) -> Optional[Any]:
        """
        Retrieve a memory and update access statistics.
        
        Args:
            key: Memory identifier
            
        Returns:
            Memory data if found, None otherwise
        """
        if key not in self.memories:
            return None
        
        # Update access statistics
        self.access_counts[key] += 1
        self.last_access[key] = self.timestamp
        
        # Strengthen memory based on retrieval
        self.memory_strength[key] *= (1 + 0.1)  # 10% strengthening
        
        self.timestamp += 1
        
        return self.memories[key]['data']
    
    def associative_recall(self, query: np.ndarray, top_k: int = 5) -> List[Tuple[str, float]]:
        """
        Retrieve memories based on content similarity.
        
        Args:
            query: Query vector for similarity matching
            top_k: Number of top matches to return
            
        Returns:
            List of (key, similarity) tuples
        """
        similarities = []
        
        for key, memory in self.memories.items():
            if isinstance(memory['data'], np.ndarray):
                similarity = np.dot(query, memory['data']) / (
                    np.linalg.norm(query) * np.linalg.norm(memory['data'])
                )
                similarities.append((key, similarity))
        
        # Sort by similarity and return top k
        similarities.sort(key=lambda x: x[1], reverse=True)
        return similarities[:top_k]
    
    def decay_memories(self) -> None:
        """
        Apply forgetting curve to reduce memory strength over time.
        """
        current_time = self.timestamp
        
        for key in list(self.memory_strength.keys()):
            time_since_access = current_time - self.last_access[key]
            decay_factor = np.exp(-self.decay_rate * time_since_access)
            
            self.memory_strength[key] *= decay_factor
            
            # Remove very weak memories
            if self.memory_strength[key] < 0.01:
                self._remove_memory(key)
    
    def _forget_least_important(self) -> None:
        """
        Remove the least important memory to make space.
        """
        if not self.memories:
            return
        
        # Calculate composite importance score
        importance_scores = {}
        for key in self.memories:
            recency = 1.0 / (1.0 + self.timestamp - self.last_access[key])
            frequency = np.log(1 + self.access_counts[key])
            strength = self.memory_strength[key]
            
            importance_scores[key] = recency * frequency * strength
        
        # Remove least important memory
        least_important = min(importance_scores, key=importance_scores.get)
        self._remove_memory(least_important)
    
    def _remove_memory(self, key: str) -> None:
        """
        Remove a memory and all associated metadata.
        """
        if key in self.memories:
            del self.memories[key]
            del self.access_counts[key]
            del self.last_access[key]
            del self.memory_strength[key]
    
    def get_memory_statistics(self) -> Dict[str, Any]:
        """
        Get statistics about the memory system.
        
        Returns:
            Dictionary containing memory statistics
        """
        if not self.memories:
            return {}
        
        memory_types = [mem['type'] for mem in self.memories.values()]
        type_counts = {t: memory_types.count(t) for t in set(memory_types)}
        
        avg_strength = np.mean(list(self.memory_strength.values()))
        avg_access_count = np.mean(list(self.access_counts.values()))
        
        return {
            'total_memories': len(self.memories),
            'type_distribution': type_counts,
            'average_strength': avg_strength,
            'average_access_count': avg_access_count,
            'capacity_utilization': len(self.memories) / self.capacity
        }

# =============================================================================
# Evaluation and Metrics
# =============================================================================

class EvaluationMetrics:
    """
    Comprehensive evaluation metrics for ULTRA system components.
    """
    
    @staticmethod
    def reasoning_quality_score(reasoning_path: List[str], 
                              ground_truth: Optional[str] = None) -> Dict[str, float]:
        """
        Evaluate the quality of a reasoning path.
        
        Args:
            reasoning_path: List of reasoning steps
            ground_truth: Correct answer (optional)
            
        Returns:
            Dictionary of quality metrics
        """
        metrics = {}
        
        # Coherence: measure logical flow between steps
        coherence_scores = []
        for i in range(len(reasoning_path) - 1):
            # Simplified coherence based on word overlap
            words1 = set(reasoning_path[i].lower().split())
            words2 = set(reasoning_path[i + 1].lower().split())
            overlap = len(words1.intersection(words2))
            total_words = len(words1.union(words2))
            coherence = overlap / total_words if total_words > 0 else 0
            coherence_scores.append(coherence)
        
        metrics['coherence'] = np.mean(coherence_scores) if coherence_scores else 0.0
        
        # Completeness: ratio of reasoning steps to expected complexity
        expected_steps = max(3, len(reasoning_path[0].split()) // 10)  # Heuristic
        metrics['completeness'] = min(1.0, len(reasoning_path) / expected_steps)
        
        # Depth: measure of reasoning complexity
        avg_step_length = np.mean([len(step.split()) for step in reasoning_path])
        metrics['depth'] = min(1.0, avg_step_length / 20)  # Normalize to [0,1]
        
        # Correctness (if ground truth available)
        if ground_truth:
            final_answer = reasoning_path[-1] if reasoning_path else ""
            # Simplified correctness check
            metrics['correctness'] = 1.0 if ground_truth.lower() in final_answer.lower() else 0.0
        
        # Overall quality score
        quality_weights = {'coherence': 0.3, 'completeness': 0.3, 'depth': 0.2}
        if 'correctness' in metrics:
            quality_weights['correctness'] = 0.2
        
        total_weight = sum(quality_weights.values())
        metrics['overall_quality'] = sum(
            metrics[key] * weight for key, weight in quality_weights.items()
        ) / total_weight
        
        return metrics
    
    @staticmethod
    def attention_analysis(attention_weights: np.ndarray) -> Dict[str, float]:
        """
        Analyze attention patterns for interpretability.
        
        Args:
            attention_weights: Attention weight matrix [seq_len, seq_len]
            
        Returns:
            Dictionary of attention metrics
        """
        metrics = {}
        
        # Entropy: measure of attention dispersion
        entropy_values = []
        for i in range(attention_weights.shape[0]):
            weights = attention_weights[i]
            weights = weights[weights > 1e-12]  # Avoid log(0)
            if len(weights) > 0:
                entropy_values.append(-np.sum(weights * np.log(weights)))
        
        metrics['average_entropy'] = np.mean(entropy_values) if entropy_values else 0.0
        
        # Sparsity: measure of attention concentration
        threshold = 0.1
        sparse_count = np.sum(attention_weights > threshold)
        total_count = attention_weights.size
        metrics['sparsity'] = 1.0 - (sparse_count / total_count)
        
        # Locality: measure of local vs global attention
        seq_len = attention_weights.shape[0]
        local_attention = 0
        for i in range(seq_len):
            local_window = max(1, seq_len // 10)  # 10% window
            start = max(0, i - local_window)
            end = min(seq_len, i + local_window + 1)
            local_attention += np.sum(attention_weights[i, start:end])
        
        metrics['locality'] = local_attention / np.sum(attention_weights)
        
        # Diagonal dominance: self-attention strength
        diagonal_sum = np.sum(np.diag(attention_weights))
        total_sum = np.sum(attention_weights)
        metrics['self_attention_ratio'] = diagonal_sum / total_sum if total_sum > 0 else 0.0
        
        return metrics
    
    @staticmethod
    def network_efficiency(adj_matrix: np.ndarray, 
                          activations: Optional[np.ndarray] = None) -> Dict[str, float]:
        """
        Evaluate neural network efficiency metrics.
        
        Args:
            adj_matrix: Network adjacency matrix
            activations: Node activation values (optional)
            
        Returns:
            Dictionary of efficiency metrics
        """
        metrics = {}
        G = nx.from_numpy_array(adj_matrix)
        
        # Structural efficiency
        if nx.is_connected(G):
            metrics['global_efficiency'] = nx.global_efficiency(G)
        else:
            # Handle disconnected graphs
            components = list(nx.connected_components(G))
            component_efficiencies = []
            for component in components:
                subgraph = G.subgraph(component)
                component_efficiencies.append(nx.global_efficiency(subgraph))
            metrics['global_efficiency'] = np.mean(component_efficiencies)
        
        metrics['local_efficiency'] = nx.local_efficiency(G)
        
        # Economic efficiency (cost-benefit ratio)
        total_edges = G.number_of_edges()
        total_possible_edges = G.number_of_nodes() * (G.number_of_nodes() - 1) / 2
        connection_cost = total_edges / total_possible_edges
        
        benefit = metrics['global_efficiency']
        metrics['economic_efficiency'] = benefit / (connection_cost + 1e-6)
        
        # Information processing efficiency (if activations provided)
        if activations is not None:
            # Measure how well the network processes information
            active_nodes = np.sum(activations > 0.1)  # Threshold for activity
            total_nodes = len(activations)
            activity_ratio = active_nodes / total_nodes
            
            # Information flow efficiency
            info_flow = 0
            for i in range(len(activations)):
                for j in range(len(activations)):
                    if adj_matrix[i, j] > 0:
                        info_flow += activations[i] * activations[j] * adj_matrix[i, j]
            
            metrics['activity_efficiency'] = activity_ratio
            metrics['information_flow'] = info_flow / (np.sum(activations)**2 + 1e-6)
        
        return metrics
    
    @staticmethod
    def learning_curve_analysis(loss_history: List[float], 
                              accuracy_history: Optional[List[float]] = None) -> Dict[str, float]:
        """
        Analyze learning curves for training insights.
        
        Args:
            loss_history: List of loss values over time
            accuracy_history: List of accuracy values over time (optional)
            
        Returns:
            Dictionary of learning metrics
        """
        metrics = {}
        
        if len(loss_history) < 2:
            return metrics
        
        loss_array = np.array(loss_history)
        
        # Convergence rate
        if len(loss_history) > 10:
            early_loss = np.mean(loss_array[:10])
            late_loss = np.mean(loss_array[-10:])
            metrics['convergence_rate'] = (early_loss - late_loss) / early_loss
        
        # Learning stability (variance in recent loss)
        if len(loss_history) > 20:
            recent_loss = loss_array[-20:]
            metrics['stability'] = 1.0 / (1.0 + np.var(recent_loss))
        
        # Smoothness (rate of change)
        loss_gradients = np.diff(loss_array)
        metrics['smoothness'] = 1.0 / (1.0 + np.var(loss_gradients))
        
        # Plateau detection
        if len(loss_history) > 50:
            recent_gradient = np.mean(loss_gradients[-50:])
            metrics['plateau_indicator'] = abs(recent_gradient)
        
        # Overfitting indicator (if accuracy provided)
        if accuracy_history and len(accuracy_history) == len(loss_history):
            acc_array = np.array(accuracy_history)
            if len(acc_array) > 20:
                recent_acc_trend = np.polyfit(range(20), acc_array[-20:], 1)[0]
                recent_loss_trend = np.polyfit(range(20), loss_array[-20:], 1)[0]
                
                # Overfitting if loss increases while accuracy decreases
                metrics['overfitting_indicator'] = max(0, recent_loss_trend - recent_acc_trend)
        
        return metrics

# =============================================================================
# Utility Classes and Enums
# =============================================================================

class ActivationFunction(Enum):
    """Enumeration of activation functions."""
    SIGMOID = "sigmoid"
    TANH = "tanh"
    RELU = "relu"
    LEAKY_RELU = "leaky_relu"
    ELU = "elu"
    SWISH = "swish"
    GELU = "gelu"

class NeuronType(Enum):
    """Enumeration of neuron types."""
    EXCITATORY = "excitatory"
    INHIBITORY = "inhibitory"
    ADAPTIVE = "adaptive"
    NEUROMODULATORY = "neuromodulatory"

class PlasticityType(Enum):
    """Enumeration of plasticity mechanisms."""
    STDP = "stdp"
    HOMEOSTATIC = "homeostatic"
    STRUCTURAL = "structural"
    METAPLASTIC = "metaplastic"

@dataclass
class NeuronConfig:
    """Configuration for neuron parameters."""
    neuron_type: NeuronType
    activation_function: ActivationFunction
    tau_m: float = 20.0  # Membrane time constant
    V_rest: float = 0.0  # Resting potential
    V_threshold: float = 1.0  # Firing threshold
    V_reset: float = 0.0  # Reset potential
    refractory_period: float = 5.0  # Refractory period
    noise_level: float = 0.1  # Membrane noise

@dataclass
class NetworkConfig:
    """Configuration for network topology."""
    n_nodes: int
    connection_probability: float
    weight_distribution: str = "normal"  # "normal", "uniform", "exponential"
    weight_mean: float = 0.5
    weight_std: float = 0.1
    topology_type: str = "random"  # "random", "small_world", "scale_free"

# =============================================================================
# System Monitoring and Diagnostics
# =============================================================================

class SystemMonitor:
    """
    Monitor system performance and generate diagnostic reports.
    """
    
    def __init__(self):
        self.metrics_history = defaultdict(list)
        self.alerts = []
        self.start_time = time.time()
    
    def log_metric(self, metric_name: str, value: Union[float, int, str]) -> None:
        """
        Log a system metric with timestamp.
        
        Args:
            metric_name: Name of the metric
            value: Metric value
        """
        timestamp = time.time() - self.start_time
        self.metrics_history[metric_name].append((timestamp, value))
        
        # Check for alerts
        self._check_alerts(metric_name, value)
    
    def _check_alerts(self, metric_name: str, value: Union[float, int]) -> None:
        """
        Check if metric value triggers any alerts.
        
        Args:
            metric_name: Name of the metric
            value: Current metric value
        """
        # Define alert thresholds
        alert_thresholds = {
            'memory_usage': 0.9,  # 90% memory usage
            'cpu_usage': 0.8,     # 80% CPU usage
            'error_rate': 0.1,    # 10% error rate
            'response_time': 1.0   # 1 second response time
        }
        
        if metric_name in alert_thresholds and isinstance(value, (int, float)):
            if value > alert_thresholds[metric_name]:
                alert = {
                    'timestamp': time.time(),
                    'metric': metric_name,
                    'value': value,
                    'threshold': alert_thresholds[metric_name],
                    'message': f"{metric_name} exceeded threshold: {value} > {alert_thresholds[metric_name]}"
                }
                self.alerts.append(alert)
                logger.warning(f"Alert: {alert['message']}")
    
    def get_system_health(self) -> Dict[str, Any]:
        """
        Generate comprehensive system health report.
        
        Returns:
            Dictionary containing system health metrics
        """
        health_report = {
            'uptime': time.time() - self.start_time,
            'total_metrics_logged': sum(len(history) for history in self.metrics_history.values()),
            'active_alerts': len(self.alerts),
            'recent_alerts': [alert for alert in self.alerts if time.time() - alert['timestamp'] < 3600],  # Last hour
            'metric_summaries': {}
        }
        
        # Generate summaries for each metric
        for metric_name, history in self.metrics_history.items():
            if history and all(isinstance(h[1], (int, float)) for h in history):
                values = [h[1] for h in history]
                health_report['metric_summaries'][metric_name] = {
                    'count': len(values),
                    'mean': np.mean(values),
                    'std': np.std(values),
                    'min': np.min(values),
                    'max': np.max(values),
                    'latest': values[-1] if values else None
                }
        
        return health_report
    
    def generate_performance_report(self) -> str:
        """
        Generate a human-readable performance report.
        
        Returns:
            Formatted performance report string
        """
        health = self.get_system_health()
        
        report = f"""
ULTRA System Performance Report
==============================
Uptime: {health['uptime']:.2f} seconds
Total Metrics Logged: {health['total_metrics_logged']}
Active Alerts: {health['active_alerts']}

Metric Summaries:
"""
        
        for metric, summary in health['metric_summaries'].items():
            report += f"""
{metric}:
  Count: {summary['count']}
  Mean: {summary['mean']:.4f}
  Std: {summary['std']:.4f}
  Range: [{summary['min']:.4f}, {summary['max']:.4f}]
  Latest: {summary['latest']:.4f}
"""
        
        if health['recent_alerts']:
            report += "\nRecent Alerts:\n"
            for alert in health['recent_alerts'][-5:]:  # Last 5 alerts
                report += f"  - {alert['message']} (at {alert['timestamp']:.2f}s)\n"
        
        return report

# =============================================================================
# Concurrency and Parallel Processing Utilities
# =============================================================================

class ParallelProcessor:
    """
    Utilities for parallel processing and concurrent computation.
    """
    
    @staticmethod
    def parallel_map(func: Callable, items: List[Any], 
                    num_workers: Optional[int] = None,
                    use_processes: bool = False) -> List[Any]:
        """
        Apply function to items in parallel.
        
        Args:
            func: Function to apply
            items: Items to process
            num_workers: Number of worker threads/processes
            use_processes: Use processes instead of threads
            
        Returns:
            List of results
        """
        if num_workers is None:
            num_workers = min(mp.cpu_count(), len(items))
        
        if use_processes:
            with ProcessPoolExecutor(max_workers=num_workers) as executor:
                results = list(executor.map(func, items))
        else:
            with ThreadPoolExecutor(max_workers=num_workers) as executor:
                results = list(executor.map(func, items))
        
        return results
    
    @staticmethod
    def batch_process(items: List[Any], batch_size: int, 
                     processor_func: Callable) -> List[Any]:
        """
        Process items in batches.
        
        Args:
            items: Items to process
            batch_size: Size of each batch
            processor_func: Function to process each batch
            
        Returns:
            Concatenated results from all batches
        """
        results = []
        
        for i in range(0, len(items), batch_size):
            batch = items[i:i + batch_size]
            batch_results = processor_func(batch)
            results.extend(batch_results)
        
        return results
    
    @staticmethod
    def async_compute(computation_func: Callable, *args, **kwargs) -> Callable:
        """
        Wrap a computation for asynchronous execution.
        
        Args:
            computation_func: Function to execute asynchronously
            *args: Arguments for the function
            **kwargs: Keyword arguments for the function
            
        Returns:
            Function that returns a future object
        """
        def async_wrapper():
            with ThreadPoolExecutor(max_workers=1) as executor:
                future = executor.submit(computation_func, *args, **kwargs)
                return future
        
        return async_wrapper

# =============================================================================
# Configuration and Serialization Utilities
# =============================================================================

class ConfigManager:
    """
    Manage system configuration and serialization.
    """
    
    @staticmethod
    def save_config(config: Dict[str, Any], filepath: str) -> None:
        """
        Save configuration to file.
        
        Args:
            config: Configuration dictionary
            filepath: Path to save file
        """
        with open(filepath, 'w') as f:
            json.dump(config, f, indent=2, default=ConfigManager._json_serializer)
    
    @staticmethod
    def load_config(filepath: str) -> Dict[str, Any]:
        """
        Load configuration from file.
        
        Args:
            filepath: Path to config file
            
        Returns:
            Configuration dictionary
        """
        with open(filepath, 'r') as f:
            return json.load(f)
    
    @staticmethod
    def _json_serializer(obj: Any) -> Any:
        """
        Custom JSON serializer for complex objects.
        
        Args:
            obj: Object to serialize
            
        Returns:
            Serializable representation
        """
        if isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, np.number):
            return obj.item()
        elif isinstance(obj, Enum):
            return obj.value
        elif hasattr(obj, '__dict__'):
            return obj.__dict__
        else:
            return str(obj)
    
    @staticmethod
    def serialize_state(state: Dict[str, Any], filepath: str) -> None:
        """
        Serialize system state to file using pickle.
        
        Args:
            state: State dictionary
            filepath: Path to save file
        """
        with open(filepath, 'wb') as f:
            pickle.dump(state, f)
    
    @staticmethod
    def deserialize_state(filepath: str) -> Dict[str, Any]:
        """
        Deserialize system state from file.
        
        Args:
            filepath: Path to state file
            
        Returns:
            State dictionary
        """
        with open(filepath, 'rb') as f:
            return pickle.load(f)
    
    @staticmethod
    def create_default_config() -> Dict[str, Any]:
        """
        Create default system configuration.
        
        Returns:
            Default configuration dictionary
        """
        return {
            'system': {
                'name': 'ULTRA',
                'version': '1.0.0',
                'debug_mode': False,
                'log_level': 'INFO'
            },
            'core_neural': {
                'neuron_types': {
                    'excitatory_ratio': 0.8,
                    'inhibitory_ratio': 0.1,
                    'adaptive_ratio': 0.05,
                    'neuromodulatory_ratio': 0.05
                },
                'plasticity': {
                    'stdp_enabled': True,
                    'homeostatic_enabled': True,
                    'structural_enabled': True
                }
            },
            'transformer': {
                'num_heads': 8,
                'hidden_dim': 512,
                'num_layers': 6,
                'dropout': 0.1,
                'attention_dropout': 0.1
            },
            'diffusion': {
                'num_timesteps': 1000,
                'beta_schedule': 'linear',
                'beta_start': 1e-4,
                'beta_end': 0.02
            },
            'memory': {
                'capacity': 10000,
                'decay_rate': 0.01
            },
            'optimization': {
                'learning_rate': 0.001,
                'optimizer': 'adam',
                'weight_decay': 1e-4
            }
        }

# =============================================================================
# Import necessary dependencies for complex operations
# =============================================================================

try:
    from scipy.stats import norm
    import itertools
except ImportError as e:
    logger.warning(f"Optional dependency not available: {e}")
    # Provide fallbacks for missing dependencies
    class norm:
        @staticmethod
        def cdf(x):
            return 0.5 * (1 + np.tanh(x * np.sqrt(2 / np.pi)))
        
        @staticmethod
        def pdf(x):
            return np.exp(-0.5 * x**2) / np.sqrt(2 * np.pi)
    
    import itertools

# =============================================================================
# Module Initialization and Exports
# =============================================================================

# Initialize default configuration
DEFAULT_CONFIG = ConfigManager.create_default_config()

# Initialize global system monitor
SYSTEM_MONITOR = SystemMonitor()

# Initialize global memory manager
MEMORY_MANAGER = MemoryManager()

# Log module initialization
logger.info("ULTRA Utils module initialized successfully")
logger.info(f"Available utility classes: {[cls.__name__ for cls in [MathematicalOperations, NeuromorphicUtils, DiffusionUtils, AttentionUtils, GraphUtils, OptimizationUtils, MemoryManager, EvaluationMetrics, SystemMonitor, ParallelProcessor, ConfigManager]]}")

# Export all utility classes and functions
__all__ = [
    # Core utility classes
    'MathematicalOperations',
    'NeuromorphicUtils', 
    'DiffusionUtils',
    'AttentionUtils',
    'GraphUtils',
    'OptimizationUtils',
    'MemoryManager',
    'EvaluationMetrics',
    'SystemMonitor',
    'ParallelProcessor',
    'ConfigManager',
    
    # Enums and data classes
    'ActivationFunction',
    'NeuronType',
    'PlasticityType',
    'NeuronConfig',
    'NetworkConfig',
    
    # Global instances
    'DEFAULT_CONFIG',
    'SYSTEM_MONITOR',
    'MEMORY_MANAGER',
    
    # Commonly used functions
    'logger'
]

# Ensure all required mathematical operations are available
def validate_mathematical_operations():
    """Validate that all mathematical operations work correctly."""
    try:
        # Test basic operations
        x = np.array([1, 2, 3, 4, 5])
        sigmoid_result = MathematicalOperations.sigmoid(x)
        softmax_result = MathematicalOperations.softmax_with_temperature(x)
        
        # Test neuromorphic operations
        v_new = NeuromorphicUtils.lif_dynamics(0.5, 1.0)
        stdp_result = NeuromorphicUtils.stdp_weight_update(10.0)
        
        # Test diffusion operations
        beta_schedule = DiffusionUtils.create_beta_schedule(100)
        
        logger.info("Mathematical operations validation passed")
        return True
        
    except Exception as e:
        logger.error(f"Mathematical operations validation failed: {e}")
        return False

# Run validation on import
if validate_mathematical_operations():
    logger.info("ULTRA Utils module ready for production use")
else:
    logger.warning("ULTRA Utils module validation failed - some operations may not work correctly")