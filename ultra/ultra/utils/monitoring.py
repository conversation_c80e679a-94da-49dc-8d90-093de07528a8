#!/usr/bin/env python3
"""
ULTRA Comprehensive Monitoring System
====================================

This module provides comprehensive real-time monitoring, performance profiling,
health checking, and alerting for the ULTRA (Ultimate Learning & Thought Reasoning
Architecture) system. It implements mathematical metrics monitoring based on
ULTRA equations, component-specific monitoring for all 8 subsystems, and
production-grade observability features.

The monitoring system includes:
- Real-time system resource monitoring (CPU, memory, GPU, neuromorphic hardware)
- Component-specific metrics for all ULTRA subsystems
- Mathematical metrics based on ULTRA equations (Φ, STDP, attention entropy, etc.)
- Performance profiling and bottleneck detection
- Intelligent alerting with configurable thresholds
- Time-series data collection and storage
- Distributed monitoring for multi-node deployments
- Integration with external monitoring systems (Prometheus, Grafana)
- Security and privacy monitoring
- Automated health checking and diagnostics

Author: ULTRA Development Team
License: MIT
Version: 1.0.0
"""

import numpy as np
import torch
import time
import threading
import multiprocessing as mp
import queue
import logging
import warnings
import psutil
import GPUtil
import os
import sys
import json
import pickle
import sqlite3
import redis
from pathlib import Path
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Tuple, Optional, Union, Any, Callable, NamedTuple
from dataclasses import dataclass, field
from enum import Enum, auto
from abc import ABC, abstractmethod
from collections import defaultdict, deque, OrderedDict
import asyncio
import aiohttp
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
import functools
import inspect
import traceback
import socket
import platform
import subprocess
from contextlib import contextmanager
import tempfile
import shutil
import zipfile
import gc

# Scientific computing and visualization
import scipy.stats as stats
import scipy.signal as signal
from scipy.integrate import simpson
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots

# Time series and statistical analysis
from sklearn.metrics import mean_squared_error, mean_absolute_error
from sklearn.preprocessing import StandardScaler
from sklearn.decomposition import PCA
from sklearn.cluster import DBSCAN
import pandas as pd

# Network and distributed monitoring
import requests
from prometheus_client import CollectorRegistry, Gauge, Counter, Histogram, Summary, start_http_server
import influxdb_client
from influxdb_client.client.write_api import SYNCHRONOUS

# Configuration system integration
from .config import ULTRAConfig, Environment, HardwareType, ConfigurationManager

# Configure logging for monitoring system
monitor_logger = logging.getLogger('ultra.monitoring')
monitor_logger.setLevel(logging.INFO)

# Suppress warnings for cleaner output
warnings.filterwarnings('ignore', category=RuntimeWarning)
warnings.filterwarnings('ignore', category=UserWarning)

# =============================================================================
# Monitoring Data Structures and Enums
# =============================================================================

class MonitoringLevel(Enum):
    """Monitoring detail levels."""
    MINIMAL = "minimal"       # Basic system metrics only
    STANDARD = "standard"     # Standard component metrics
    DETAILED = "detailed"     # Detailed mathematical metrics
    COMPREHENSIVE = "comprehensive"  # All available metrics
    DEBUG = "debug"          # Debug-level monitoring with traces

class AlertSeverity(Enum):
    """Alert severity levels."""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"
    EMERGENCY = "emergency"

class MetricType(Enum):
    """Types of metrics that can be monitored."""
    GAUGE = "gauge"           # Point-in-time value
    COUNTER = "counter"       # Monotonically increasing
    HISTOGRAM = "histogram"   # Distribution of values
    SUMMARY = "summary"       # Statistical summary
    RATE = "rate"            # Rate of change

class ComponentType(Enum):
    """ULTRA system component types."""
    CORE_NEURAL = "core_neural"
    TRANSFORMER = "transformer"
    DIFFUSION = "diffusion"
    META_COGNITIVE = "meta_cognitive"
    NEUROMORPHIC = "neuromorphic"
    CONSCIOUSNESS = "consciousness"
    NEURO_SYMBOLIC = "neuro_symbolic"
    SELF_EVOLUTION = "self_evolution"
    SYSTEM = "system"

@dataclass
class MetricValue:
    """Container for metric values with metadata."""
    value: Union[float, int, str, bool, np.ndarray]
    timestamp: datetime
    component: ComponentType
    metric_name: str
    metric_type: MetricType
    tags: Dict[str, str] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            'value': self.value if not isinstance(self.value, np.ndarray) else self.value.tolist(),
            'timestamp': self.timestamp.isoformat(),
            'component': self.component.value,
            'metric_name': self.metric_name,
            'metric_type': self.metric_type.value,
            'tags': self.tags,
            'metadata': self.metadata
        }

@dataclass
class Alert:
    """Alert data structure."""
    alert_id: str
    severity: AlertSeverity
    component: ComponentType
    metric_name: str
    message: str
    value: float
    threshold: float
    timestamp: datetime
    resolved: bool = False
    resolved_at: Optional[datetime] = None
    tags: Dict[str, str] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            'alert_id': self.alert_id,
            'severity': self.severity.value,
            'component': self.component.value,
            'metric_name': self.metric_name,
            'message': self.message,
            'value': self.value,
            'threshold': self.threshold,
            'timestamp': self.timestamp.isoformat(),
            'resolved': self.resolved,
            'resolved_at': self.resolved_at.isoformat() if self.resolved_at else None,
            'tags': self.tags
        }

# =============================================================================
# Mathematical Metrics Calculators
# =============================================================================

class NeuromorphicMetrics:
    """Mathematical metrics for neuromorphic components based on ULTRA equations."""
    
    @staticmethod
    def calculate_firing_rate(spike_times: np.ndarray, window_size: float = 1000.0) -> float:
        """
        Calculate instantaneous firing rate.
        
        Args:
            spike_times: Array of spike timestamps (ms)
            window_size: Time window for rate calculation (ms)
            
        Returns:
            Firing rate in Hz
        """
        if len(spike_times) < 2:
            return 0.0
        
        current_time = spike_times[-1]
        window_start = current_time - window_size
        recent_spikes = spike_times[spike_times >= window_start]
        
        return len(recent_spikes) / (window_size / 1000.0)  # Convert to Hz
    
    @staticmethod
    def calculate_stdp_strength(pre_times: np.ndarray, post_times: np.ndarray,
                               A_plus: float = 0.1, A_minus: float = 0.105,
                               tau_plus: float = 20.0, tau_minus: float = 20.0) -> float:
        """
        Calculate STDP-based synaptic strength change.
        
        Implements: Δw = A_+exp(-Δt/τ_+) if Δt > 0, -A_-exp(Δt/τ_-) if Δt < 0
        
        Args:
            pre_times: Presynaptic spike times
            post_times: Postsynaptic spike times
            A_plus: LTP amplitude
            A_minus: LTD amplitude
            tau_plus: LTP time constant
            tau_minus: LTD time constant
            
        Returns:
            Net synaptic strength change
        """
        if len(pre_times) == 0 or len(post_times) == 0:
            return 0.0
        
        total_change = 0.0
        
        for post_time in post_times:
            for pre_time in pre_times:
                delta_t = post_time - pre_time
                
                if delta_t > 0:  # Post after pre -> LTP
                    total_change += A_plus * np.exp(-delta_t / tau_plus)
                elif delta_t < 0:  # Pre after post -> LTD
                    total_change -= A_minus * np.exp(delta_t / tau_minus)
        
        return total_change
    
    @staticmethod
    def calculate_membrane_dynamics(V: np.ndarray, dt: float = 0.1) -> Dict[str, float]:
        """
        Calculate membrane potential dynamics metrics.
        
        Args:
            V: Membrane potential time series
            dt: Time step (ms)
            
        Returns:
            Dictionary of dynamics metrics
        """
        if len(V) < 2:
            return {'mean_potential': 0.0, 'potential_variance': 0.0, 'depolarization_rate': 0.0}
        
        # Basic statistics
        mean_potential = np.mean(V)
        potential_variance = np.var(V)
        
        # Depolarization rate (positive derivatives)
        dV_dt = np.diff(V) / dt
        depolarization_events = dV_dt[dV_dt > 0]
        depolarization_rate = np.mean(depolarization_events) if len(depolarization_events) > 0 else 0.0
        
        # Oscillation analysis
        frequencies, power_spectrum = signal.periodogram(V, fs=1000.0/dt)
        dominant_freq = frequencies[np.argmax(power_spectrum)]
        spectral_entropy = -np.sum(power_spectrum * np.log(power_spectrum + 1e-12))
        
        return {
            'mean_potential': float(mean_potential),
            'potential_variance': float(potential_variance),
            'depolarization_rate': float(depolarization_rate),
            'dominant_frequency': float(dominant_freq),
            'spectral_entropy': float(spectral_entropy)
        }
    
    @staticmethod
    def calculate_network_synchrony(spike_trains: List[np.ndarray], 
                                  window_size: float = 50.0) -> float:
        """
        Calculate network synchronization level.
        
        Args:
            spike_trains: List of spike time arrays for different neurons
            window_size: Time window for synchrony calculation (ms)
            
        Returns:
            Synchrony index (0-1)
        """
        if len(spike_trains) < 2:
            return 0.0
        
        # Create binned spike trains
        max_time = max(max(train) if len(train) > 0 else 0 for train in spike_trains)
        bin_size = 1.0  # 1 ms bins
        n_bins = int(max_time / bin_size) + 1
        
        binned_trains = []
        for train in spike_trains:
            binned = np.histogram(train, bins=n_bins, range=(0, max_time))[0]
            binned_trains.append(binned)
        
        binned_trains = np.array(binned_trains)
        
        # Calculate cross-correlation based synchrony
        n_neurons = len(spike_trains)
        correlations = []
        
        for i in range(n_neurons):
            for j in range(i + 1, n_neurons):
                correlation = np.corrcoef(binned_trains[i], binned_trains[j])[0, 1]
                if not np.isnan(correlation):
                    correlations.append(correlation)
        
        return np.mean(correlations) if correlations else 0.0

class AttentionMetrics:
    """Mathematical metrics for attention mechanisms."""
    
    @staticmethod
    def calculate_attention_entropy(attention_weights: np.ndarray) -> float:
        """
        Calculate entropy of attention distribution.
        
        Args:
            attention_weights: Attention weight matrix [seq_len, seq_len]
            
        Returns:
            Average attention entropy
        """
        if attention_weights.size == 0:
            return 0.0
        
        entropies = []
        for i in range(attention_weights.shape[0]):
            weights = attention_weights[i]
            weights = weights[weights > 1e-12]  # Avoid log(0)
            if len(weights) > 0:
                entropy = -np.sum(weights * np.log(weights))
                entropies.append(entropy)
        
        return np.mean(entropies) if entropies else 0.0
    
    @staticmethod
    def calculate_attention_sparsity(attention_weights: np.ndarray, threshold: float = 0.01) -> float:
        """
        Calculate sparsity of attention patterns.
        
        Args:
            attention_weights: Attention weight matrix
            threshold: Threshold for considering attention as active
            
        Returns:
            Sparsity ratio (0-1)
        """
        if attention_weights.size == 0:
            return 1.0
        
        active_connections = np.sum(attention_weights > threshold)
        total_connections = attention_weights.size
        
        return 1.0 - (active_connections / total_connections)
    
    @staticmethod
    def calculate_attention_locality(attention_weights: np.ndarray) -> float:
        """
        Calculate locality of attention patterns.
        
        Args:
            attention_weights: Attention weight matrix
            
        Returns:
            Locality index (0-1, higher = more local)
        """
        if attention_weights.shape[0] < 2:
            return 1.0
        
        seq_len = attention_weights.shape[0]
        local_attention = 0.0
        total_attention = np.sum(attention_weights)
        
        for i in range(seq_len):
            # Calculate weighted distance
            distances = np.abs(np.arange(seq_len) - i)
            weighted_distances = attention_weights[i] * distances
            if np.sum(attention_weights[i]) > 0:
                avg_distance = np.sum(weighted_distances) / np.sum(attention_weights[i])
                local_attention += 1.0 / (1.0 + avg_distance)
        
        return local_attention / seq_len if seq_len > 0 else 0.0
    
    @staticmethod
    def calculate_attention_dynamics(attention_history: List[np.ndarray]) -> Dict[str, float]:
        """
        Calculate attention dynamics over time.
        
        Args:
            attention_history: List of attention matrices over time
            
        Returns:
            Dictionary of dynamics metrics
        """
        if len(attention_history) < 2:
            return {'stability': 1.0, 'drift_rate': 0.0, 'oscillation_strength': 0.0}
        
        # Calculate stability (inverse of variance)
        flattened_history = [attn.flatten() for attn in attention_history]
        attention_variance = np.var(flattened_history, axis=0)
        stability = 1.0 / (1.0 + np.mean(attention_variance))
        
        # Calculate drift rate
        first_half = flattened_history[:len(flattened_history)//2]
        second_half = flattened_history[len(flattened_history)//2:]
        drift_rate = np.mean(np.abs(np.mean(second_half, axis=0) - np.mean(first_half, axis=0)))
        
        # Calculate oscillation strength using FFT
        mean_attention = np.mean(flattened_history, axis=1)
        fft_values = np.abs(np.fft.fft(mean_attention))
        oscillation_strength = np.std(fft_values[1:len(fft_values)//2])  # Exclude DC component
        
        return {
            'stability': float(stability),
            'drift_rate': float(drift_rate),
            'oscillation_strength': float(oscillation_strength)
        }

class DiffusionMetrics:
    """Mathematical metrics for diffusion-based reasoning."""
    
    @staticmethod
    def calculate_diffusion_convergence(loss_history: List[float], window_size: int = 10) -> float:
        """
        Calculate convergence rate of diffusion process.
        
        Args:
            loss_history: History of diffusion loss values
            window_size: Window for convergence calculation
            
        Returns:
            Convergence rate (higher = faster convergence)
        """
        if len(loss_history) < window_size:
            return 0.0
        
        recent_losses = loss_history[-window_size:]
        if len(set(recent_losses)) == 1:  # All values identical
            return 1.0
        
        # Calculate rate of loss decrease
        x = np.arange(len(recent_losses))
        slope, _, r_value, _, _ = stats.linregress(x, recent_losses)
        
        # Negative slope indicates convergence, r² indicates consistency
        convergence_rate = max(0, -slope) * (r_value ** 2)
        
        return float(convergence_rate)
    
    @staticmethod
    def calculate_thought_space_coherence(concepts: np.ndarray, 
                                        relationships: np.ndarray) -> float:
        """
        Calculate coherence of thought space organization.
        
        Args:
            concepts: Concept embeddings [n_concepts, dim]
            relationships: Relationship matrix [n_concepts, n_concepts]
            
        Returns:
            Coherence score (0-1)
        """
        if concepts.shape[0] < 2:
            return 1.0
        
        # Calculate embedding similarities
        concept_similarities = np.dot(concepts, concepts.T)
        np.fill_diagonal(concept_similarities, 0)  # Remove self-similarities
        
        # Normalize similarities to [0, 1]
        concept_similarities = (concept_similarities + 1) / 2
        
        # Calculate coherence as alignment between similarities and relationships
        relationship_strengths = np.abs(relationships)
        np.fill_diagonal(relationship_strengths, 0)
        
        # Pearson correlation between similarities and relationships
        sim_flat = concept_similarities.flatten()
        rel_flat = relationship_strengths.flatten()
        
        if np.std(sim_flat) == 0 or np.std(rel_flat) == 0:
            return 0.0
        
        coherence = np.corrcoef(sim_flat, rel_flat)[0, 1]
        return max(0.0, coherence)  # Return 0 if negative correlation
    
    @staticmethod
    def calculate_uncertainty_calibration(predicted_uncertainties: np.ndarray,
                                        actual_errors: np.ndarray,
                                        n_bins: int = 10) -> float:
        """
        Calculate uncertainty calibration quality.
        
        Args:
            predicted_uncertainties: Predicted uncertainty values
            actual_errors: Actual prediction errors
            n_bins: Number of bins for calibration analysis
            
        Returns:
            Calibration error (lower is better)
        """
        if len(predicted_uncertainties) != len(actual_errors):
            return float('inf')
        
        if len(predicted_uncertainties) == 0:
            return 0.0
        
        # Create bins based on predicted uncertainty
        bin_boundaries = np.linspace(0, np.max(predicted_uncertainties), n_bins + 1)
        bin_indices = np.digitize(predicted_uncertainties, bin_boundaries) - 1
        bin_indices = np.clip(bin_indices, 0, n_bins - 1)
        
        calibration_error = 0.0
        total_samples = 0
        
        for bin_idx in range(n_bins):
            bin_mask = bin_indices == bin_idx
            if np.sum(bin_mask) == 0:
                continue
            
            bin_uncertainties = predicted_uncertainties[bin_mask]
            bin_errors = actual_errors[bin_mask]
            
            avg_uncertainty = np.mean(bin_uncertainties)
            avg_error = np.mean(bin_errors)
            bin_size = np.sum(bin_mask)
            
            calibration_error += bin_size * np.abs(avg_uncertainty - avg_error)
            total_samples += bin_size
        
        return calibration_error / total_samples if total_samples > 0 else 0.0

class ConsciousnessMetrics:
    """Mathematical metrics for consciousness and integrated information."""
    
    @staticmethod
    def calculate_phi(connectivity_matrix: np.ndarray, states: np.ndarray,
                     max_partitions: int = 10) -> float:
        """
        Calculate integrated information Φ (phi) based on IIT.
        
        Implements: Φ(X) = min_P∈P I(X_1;X_2|X_P)/min{H(X_1|X_P),H(X_2|X_P)}
        
        Args:
            connectivity_matrix: Network connectivity [n_nodes, n_nodes]
            states: Current node states [n_nodes]
            max_partitions: Maximum partitions to consider for computational efficiency
            
        Returns:
            Φ value (integrated information)
        """
        n_nodes = len(states)
        if n_nodes < 2:
            return 0.0
        
        # Calculate system-level effective information
        system_ei = ConsciousnessMetrics._calculate_effective_information(
            connectivity_matrix, states
        )
        
        min_phi = float('inf')
        partition_count = 0
        
        # Try different bipartitions
        for partition_size in range(1, n_nodes):
            if partition_count >= max_partitions:
                break
            
            # Generate all possible partitions of this size
            from itertools import combinations
            for partition in combinations(range(n_nodes), partition_size):
                if partition_count >= max_partitions:
                    break
                
                subset1 = list(partition)
                subset2 = [i for i in range(n_nodes) if i not in subset1]
                
                # Calculate cross-partition information
                cross_info = ConsciousnessMetrics._calculate_cross_partition_info(
                    connectivity_matrix, states, subset1, subset2
                )
                
                min_phi = min(min_phi, cross_info)
                partition_count += 1
        
        return max(0.0, min_phi) if min_phi != float('inf') else 0.0
    
    @staticmethod
    def _calculate_effective_information(connectivity_matrix: np.ndarray, 
                                       states: np.ndarray) -> float:
        """Calculate effective information of the system."""
        # Simplified implementation for computational efficiency
        # In practice, this would involve more complex calculations
        
        # Calculate state transition probabilities
        n_nodes = len(states)
        if n_nodes == 0:
            return 0.0
        
        # Use connectivity to estimate state transitions
        next_state_probs = np.zeros(n_nodes)
        for i in range(n_nodes):
            input_sum = np.sum(connectivity_matrix[i] * states)
            next_state_probs[i] = 1.0 / (1.0 + np.exp(-input_sum))  # Sigmoid
        
        # Calculate entropy
        # Avoid log(0) by adding small epsilon
        epsilon = 1e-12
        next_state_probs = np.clip(next_state_probs, epsilon, 1 - epsilon)
        
        entropy = -np.sum(next_state_probs * np.log(next_state_probs) + 
                         (1 - next_state_probs) * np.log(1 - next_state_probs))
        
        return entropy
    
    @staticmethod
    def _calculate_cross_partition_info(connectivity_matrix: np.ndarray,
                                      states: np.ndarray,
                                      subset1: List[int],
                                      subset2: List[int]) -> float:
        """Calculate information flow across partition."""
        if len(subset1) == 0 or len(subset2) == 0:
            return 0.0
        
        # Extract cross-partition connections
        cross_connections = connectivity_matrix[np.ix_(subset1, subset2)]
        
        # Calculate weighted information transfer
        subset1_activity = np.mean(states[subset1])
        subset2_activity = np.mean(states[subset2])
        
        connection_strength = np.sum(np.abs(cross_connections))
        information_transfer = connection_strength * subset1_activity * subset2_activity
        
        return information_transfer
    
    @staticmethod
    def calculate_global_workspace_access(candidate_contents: List[float],
                                        access_threshold: float = 0.7) -> Dict[str, float]:
        """
        Calculate global workspace access metrics.
        
        Args:
            candidate_contents: Salience values of candidate contents
            access_threshold: Threshold for workspace access
            
        Returns:
            Dictionary of workspace metrics
        """
        if not candidate_contents:
            return {'access_rate': 0.0, 'competition_strength': 0.0, 'winner_dominance': 0.0}
        
        contents_array = np.array(candidate_contents)
        
        # Access rate (fraction above threshold)
        access_rate = np.mean(contents_array > access_threshold)
        
        # Competition strength (variance in salience)
        competition_strength = np.var(contents_array)
        
        # Winner dominance (max relative to mean)
        if np.mean(contents_array) > 0:
            winner_dominance = np.max(contents_array) / np.mean(contents_array)
        else:
            winner_dominance = 1.0
        
        return {
            'access_rate': float(access_rate),
            'competition_strength': float(competition_strength),
            'winner_dominance': float(winner_dominance)
        }

# =============================================================================
# Component-Specific Monitors
# =============================================================================

class ComponentMonitor(ABC):
    """Abstract base class for component-specific monitors."""
    
    def __init__(self, component_type: ComponentType, monitoring_level: MonitoringLevel):
        self.component_type = component_type
        self.monitoring_level = monitoring_level
        self.metrics: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        self.last_update = datetime.now(timezone.utc)
        self._lock = threading.RLock()
    
    @abstractmethod
    def collect_metrics(self, component_state: Dict[str, Any]) -> Dict[str, MetricValue]:
        """Collect metrics from component state."""
        pass
    
    def get_recent_metrics(self, metric_name: str, window_size: int = 100) -> List[MetricValue]:
        """Get recent values for a specific metric."""
        with self._lock:
            if metric_name in self.metrics:
                return list(self.metrics[metric_name])[-window_size:]
            return []
    
    def get_metric_statistics(self, metric_name: str) -> Dict[str, float]:
        """Calculate statistics for a metric."""
        recent_values = self.get_recent_metrics(metric_name)
        if not recent_values:
            return {}
        
        values = [mv.value for mv in recent_values if isinstance(mv.value, (int, float))]
        if not values:
            return {}
        
        return {
            'mean': np.mean(values),
            'std': np.std(values),
            'min': np.min(values),
            'max': np.max(values),
            'median': np.median(values),
            'percentile_95': np.percentile(values, 95),
            'percentile_99': np.percentile(values, 99)
        }

class CoreNeuralMonitor(ComponentMonitor):
    """Monitor for Core Neural Architecture."""
    
    def __init__(self, monitoring_level: MonitoringLevel):
        super().__init__(ComponentType.CORE_NEURAL, monitoring_level)
        self.neuromorphic_metrics = NeuromorphicMetrics()
    
    def collect_metrics(self, component_state: Dict[str, Any]) -> Dict[str, MetricValue]:
        """Collect core neural architecture metrics."""
        metrics = {}
        timestamp = datetime.now(timezone.utc)
        
        # Basic neural metrics
        if 'firing_rates' in component_state:
            firing_rates = component_state['firing_rates']
            avg_firing_rate = np.mean(firing_rates)
            
            metrics['avg_firing_rate'] = MetricValue(
                value=avg_firing_rate,
                timestamp=timestamp,
                component=self.component_type,
                metric_name='avg_firing_rate',
                metric_type=MetricType.GAUGE,
                tags={'unit': 'Hz'}
            )
            
            # Firing rate distribution
            if self.monitoring_level in [MonitoringLevel.DETAILED, MonitoringLevel.COMPREHENSIVE]:
                rate_std = np.std(firing_rates)
                rate_cv = rate_std / avg_firing_rate if avg_firing_rate > 0 else 0
                
                metrics['firing_rate_cv'] = MetricValue(
                    value=rate_cv,
                    timestamp=timestamp,
                    component=self.component_type,
                    metric_name='firing_rate_cv',
                    metric_type=MetricType.GAUGE,
                    tags={'unit': 'coefficient_of_variation'}
                )
        
        # Synaptic weight dynamics
        if 'synaptic_weights' in component_state:
            weights = component_state['synaptic_weights']
            
            # Average synaptic strength
            avg_weight = np.mean(np.abs(weights))
            metrics['avg_synaptic_strength'] = MetricValue(
                value=avg_weight,
                timestamp=timestamp,
                component=self.component_type,
                metric_name='avg_synaptic_strength',
                metric_type=MetricType.GAUGE
            )
            
            # Weight distribution entropy
            if self.monitoring_level >= MonitoringLevel.DETAILED:
                weight_hist, _ = np.histogram(weights.flatten(), bins=50, density=True)
                weight_hist = weight_hist[weight_hist > 0]
                weight_entropy = -np.sum(weight_hist * np.log(weight_hist))
                
                metrics['weight_entropy'] = MetricValue(
                    value=weight_entropy,
                    timestamp=timestamp,
                    component=self.component_type,
                    metric_name='weight_entropy',
                    metric_type=MetricType.GAUGE,
                    tags={'unit': 'nats'}
                )
        
        # STDP dynamics
        if 'stdp_changes' in component_state and self.monitoring_level >= MonitoringLevel.DETAILED:
            stdp_changes = component_state['stdp_changes']
            
            # STDP magnitude
            stdp_magnitude = np.mean(np.abs(stdp_changes))
            metrics['stdp_magnitude'] = MetricValue(
                value=stdp_magnitude,
                timestamp=timestamp,
                component=self.component_type,
                metric_name='stdp_magnitude',
                metric_type=MetricType.GAUGE
            )
            
            # LTP/LTD ratio
            ltp_changes = stdp_changes[stdp_changes > 0]
            ltd_changes = stdp_changes[stdp_changes < 0]
            
            if len(ltd_changes) > 0:
                ltp_ltd_ratio = len(ltp_changes) / len(ltd_changes)
            else:
                ltp_ltd_ratio = float('inf') if len(ltp_changes) > 0 else 1.0
            
            metrics['ltp_ltd_ratio'] = MetricValue(
                value=ltp_ltd_ratio,
                timestamp=timestamp,
                component=self.component_type,
                metric_name='ltp_ltd_ratio',
                metric_type=MetricType.GAUGE
            )
        
        # Network synchrony
        if 'spike_trains' in component_state and self.monitoring_level >= MonitoringLevel.DETAILED:
            spike_trains = component_state['spike_trains']
            synchrony = self.neuromorphic_metrics.calculate_network_synchrony(spike_trains)
            
            metrics['network_synchrony'] = MetricValue(
                value=synchrony,
                timestamp=timestamp,
                component=self.component_type,
                metric_name='network_synchrony',
                metric_type=MetricType.GAUGE,
                tags={'range': '0-1'}
            )
        
        # Neuromodulator levels
        if 'neuromodulators' in component_state:
            modulators = component_state['neuromodulators']
            
            for modulator_name, level in modulators.items():
                metrics[f'neuromodulator_{modulator_name}'] = MetricValue(
                    value=level,
                    timestamp=timestamp,
                    component=self.component_type,
                    metric_name=f'neuromodulator_{modulator_name}',
                    metric_type=MetricType.GAUGE,
                    tags={'modulator': modulator_name, 'range': '0-1'}
                )
        
        # Oscillation metrics
        if 'oscillations' in component_state and self.monitoring_level >= MonitoringLevel.COMPREHENSIVE:
            oscillations = component_state['oscillations']
            
            for freq_band, amplitude in oscillations.items():
                metrics[f'oscillation_{freq_band}'] = MetricValue(
                    value=amplitude,
                    timestamp=timestamp,
                    component=self.component_type,
                    metric_name=f'oscillation_{freq_band}',
                    metric_type=MetricType.GAUGE,
                    tags={'frequency_band': freq_band}
                )
        
        # Store metrics in history
        with self._lock:
            for metric_name, metric_value in metrics.items():
                self.metrics[metric_name].append(metric_value)
        
        return metrics

class TransformerMonitor(ComponentMonitor):
    """Monitor for Hyper-Dimensional Transformer."""
    
    def __init__(self, monitoring_level: MonitoringLevel):
        super().__init__(ComponentType.TRANSFORMER, monitoring_level)
        self.attention_metrics = AttentionMetrics()
        self.attention_history = deque(maxlen=100)
    
    def collect_metrics(self, component_state: Dict[str, Any]) -> Dict[str, MetricValue]:
        """Collect transformer metrics."""
        metrics = {}
        timestamp = datetime.now(timezone.utc)
        
        # Attention metrics
        if 'attention_weights' in component_state:
            attention_weights = component_state['attention_weights']
            self.attention_history.append(attention_weights)
            
            # Attention entropy
            attention_entropy = self.attention_metrics.calculate_attention_entropy(attention_weights)
            metrics['attention_entropy'] = MetricValue(
                value=attention_entropy,
                timestamp=timestamp,
                component=self.component_type,
                metric_name='attention_entropy',
                metric_type=MetricType.GAUGE,
                tags={'unit': 'nats'}
            )
            
            # Attention sparsity
            attention_sparsity = self.attention_metrics.calculate_attention_sparsity(attention_weights)
            metrics['attention_sparsity'] = MetricValue(
                value=attention_sparsity,
                timestamp=timestamp,
                component=self.component_type,
                metric_name='attention_sparsity',
                metric_type=MetricType.GAUGE,
                tags={'range': '0-1'}
            )
            
            # Attention locality
            if self.monitoring_level >= MonitoringLevel.DETAILED:
                attention_locality = self.attention_metrics.calculate_attention_locality(attention_weights)
                metrics['attention_locality'] = MetricValue(
                    value=attention_locality,
                    timestamp=timestamp,
                    component=self.component_type,
                    metric_name='attention_locality',
                    metric_type=MetricType.GAUGE,
                    tags={'range': '0-1'}
                )
        
        # Attention dynamics
        if len(self.attention_history) >= 2 and self.monitoring_level >= MonitoringLevel.DETAILED:
            dynamics = self.attention_metrics.calculate_attention_dynamics(list(self.attention_history))
            
            for metric_name, value in dynamics.items():
                metrics[f'attention_{metric_name}'] = MetricValue(
                    value=value,
                    timestamp=timestamp,
                    component=self.component_type,
                    metric_name=f'attention_{metric_name}',
                    metric_type=MetricType.GAUGE
                )
        
        # Recursive depth metrics
        if 'recursive_depths' in component_state:
            depths = component_state['recursive_depths']
            
            avg_depth = np.mean(depths)
            max_depth = np.max(depths)
            depth_variance = np.var(depths)
            
            metrics['avg_recursive_depth'] = MetricValue(
                value=avg_depth,
                timestamp=timestamp,
                component=self.component_type,
                metric_name='avg_recursive_depth',
                metric_type=MetricType.GAUGE
            )
            
            if self.monitoring_level >= MonitoringLevel.DETAILED:
                metrics['max_recursive_depth'] = MetricValue(
                    value=max_depth,
                    timestamp=timestamp,
                    component=self.component_type,
                    metric_name='max_recursive_depth',
                    metric_type=MetricType.GAUGE
                )
                
                metrics['recursive_depth_variance'] = MetricValue(
                    value=depth_variance,
                    timestamp=timestamp,
                    component=self.component_type,
                    metric_name='recursive_depth_variance',
                    metric_type=MetricType.GAUGE
                )
        
        # Temperature adaptation
        if 'attention_temperature' in component_state:
            temperature = component_state['attention_temperature']
            
            metrics['attention_temperature'] = MetricValue(
                value=temperature,
                timestamp=timestamp,
                component=self.component_type,
                metric_name='attention_temperature',
                metric_type=MetricType.GAUGE
            )
        
        # Layer utilization
        if 'layer_activations' in component_state and self.monitoring_level >= MonitoringLevel.COMPREHENSIVE:
            activations = component_state['layer_activations']
            
            for layer_idx, activation in enumerate(activations):
                layer_norm = np.linalg.norm(activation)
                metrics[f'layer_{layer_idx}_activation_norm'] = MetricValue(
                    value=layer_norm,
                    timestamp=timestamp,
                    component=self.component_type,
                    metric_name=f'layer_{layer_idx}_activation_norm',
                    metric_type=MetricType.GAUGE,
                    tags={'layer': str(layer_idx)}
                )
        
        # Store metrics
        with self._lock:
            for metric_name, metric_value in metrics.items():
                self.metrics[metric_name].append(metric_value)
        
        return metrics

class DiffusionMonitor(ComponentMonitor):
    """Monitor for Diffusion-Based Reasoning."""
    
    def __init__(self, monitoring_level: MonitoringLevel):
        super().__init__(ComponentType.DIFFUSION, monitoring_level)
        self.diffusion_metrics = DiffusionMetrics()
        self.loss_history = deque(maxlen=1000)
    
    def collect_metrics(self, component_state: Dict[str, Any]) -> Dict[str, MetricValue]:
        """Collect diffusion reasoning metrics."""
        metrics = {}
        timestamp = datetime.now(timezone.utc)
        
        # Diffusion loss tracking
        if 'diffusion_loss' in component_state:
            loss = component_state['diffusion_loss']
            self.loss_history.append(loss)
            
            metrics['diffusion_loss'] = MetricValue(
                value=loss,
                timestamp=timestamp,
                component=self.component_type,
                metric_name='diffusion_loss',
                metric_type=MetricType.GAUGE
            )
            
            # Convergence rate
            if len(self.loss_history) >= 10:
                convergence_rate = self.diffusion_metrics.calculate_diffusion_convergence(
                    list(self.loss_history)
                )
                metrics['convergence_rate'] = MetricValue(
                    value=convergence_rate,
                    timestamp=timestamp,
                    component=self.component_type,
                    metric_name='convergence_rate',
                    metric_type=MetricType.GAUGE
                )
        
        # Thought space coherence
        if ('concept_embeddings' in component_state and 
            'concept_relationships' in component_state and
            self.monitoring_level >= MonitoringLevel.DETAILED):
            
            concepts = component_state['concept_embeddings']
            relationships = component_state['concept_relationships']
            
            coherence = self.diffusion_metrics.calculate_thought_space_coherence(
                concepts, relationships
            )
            metrics['thought_space_coherence'] = MetricValue(
                value=coherence,
                timestamp=timestamp,
                component=self.component_type,
                metric_name='thought_space_coherence',
                metric_type=MetricType.GAUGE,
                tags={'range': '0-1'}
            )
        
        # Uncertainty calibration
        if ('predicted_uncertainties' in component_state and 
            'actual_errors' in component_state and
            self.monitoring_level >= MonitoringLevel.DETAILED):
            
            uncertainties = component_state['predicted_uncertainties']
            errors = component_state['actual_errors']
            
            calibration_error = self.diffusion_metrics.calculate_uncertainty_calibration(
                uncertainties, errors
            )
            metrics['uncertainty_calibration_error'] = MetricValue(
                value=calibration_error,
                timestamp=timestamp,
                component=self.component_type,
                metric_name='uncertainty_calibration_error',
                metric_type=MetricType.GAUGE
            )
        
        # Diffusion step metrics
        if 'current_timestep' in component_state:
            timestep = component_state['current_timestep']
            
            metrics['current_diffusion_timestep'] = MetricValue(
                value=timestep,
                timestamp=timestamp,
                component=self.component_type,
                metric_name='current_diffusion_timestep',
                metric_type=MetricType.GAUGE
            )
        
        # Reverse process quality
        if 'denoising_quality' in component_state and self.monitoring_level >= MonitoringLevel.COMPREHENSIVE:
            quality = component_state['denoising_quality']
            
            metrics['denoising_quality'] = MetricValue(
                value=quality,
                timestamp=timestamp,
                component=self.component_type,
                metric_name='denoising_quality',
                metric_type=MetricType.GAUGE,
                tags={'range': '0-1'}
            )
        
        # Concept navigation success rate
        if 'navigation_successes' in component_state and 'navigation_attempts' in component_state:
            successes = component_state['navigation_successes']
            attempts = component_state['navigation_attempts']
            
            success_rate = successes / attempts if attempts > 0 else 0.0
            metrics['concept_navigation_success_rate'] = MetricValue(
                value=success_rate,
                timestamp=timestamp,
                component=self.component_type,
                metric_name='concept_navigation_success_rate',
                metric_type=MetricType.GAUGE,
                tags={'range': '0-1'}
            )
        
        # Store metrics
        with self._lock:
            for metric_name, metric_value in metrics.items():
                self.metrics[metric_name].append(metric_value)
        
        return metrics

class ConsciousnessMonitor(ComponentMonitor):
    """Monitor for Emergent Consciousness Lattice."""
    
    def __init__(self, monitoring_level: MonitoringLevel):
        super().__init__(ComponentType.CONSCIOUSNESS, monitoring_level)
        self.consciousness_metrics = ConsciousnessMetrics()
    
    def collect_metrics(self, component_state: Dict[str, Any]) -> Dict[str, MetricValue]:
        """Collect consciousness metrics."""
        metrics = {}
        timestamp = datetime.now(timezone.utc)
        
        # Integrated Information (Φ)
        if ('connectivity_matrix' in component_state and 
            'node_states' in component_state and
            self.monitoring_level >= MonitoringLevel.DETAILED):
            
            connectivity = component_state['connectivity_matrix']
            states = component_state['node_states']
            
            phi = self.consciousness_metrics.calculate_phi(connectivity, states)
            metrics['integrated_information_phi'] = MetricValue(
                value=phi,
                timestamp=timestamp,
                component=self.component_type,
                metric_name='integrated_information_phi',
                metric_type=MetricType.GAUGE,
                tags={'unit': 'phi'}
            )
        
        # Global workspace metrics
        if 'workspace_contents' in component_state:
            contents = component_state['workspace_contents']
            
            workspace_metrics = self.consciousness_metrics.calculate_global_workspace_access(contents)
            
            for metric_name, value in workspace_metrics.items():
                metrics[f'workspace_{metric_name}'] = MetricValue(
                    value=value,
                    timestamp=timestamp,
                    component=self.component_type,
                    metric_name=f'workspace_{metric_name}',
                    metric_type=MetricType.GAUGE
                )
        
        # Self-awareness metrics
        if 'self_model_confidence' in component_state:
            confidence = component_state['self_model_confidence']
            
            metrics['self_model_confidence'] = MetricValue(
                value=confidence,
                timestamp=timestamp,
                component=self.component_type,
                metric_name='self_model_confidence',
                metric_type=MetricType.GAUGE,
                tags={'range': '0-1'}
            )
        
        # Intentionality strength
        if 'goal_coherence' in component_state and self.monitoring_level >= MonitoringLevel.COMPREHENSIVE:
            coherence = component_state['goal_coherence']
            
            metrics['goal_coherence'] = MetricValue(
                value=coherence,
                timestamp=timestamp,
                component=self.component_type,
                metric_name='goal_coherence',
                metric_type=MetricType.GAUGE,
                tags={'range': '0-1'}
            )
        
        # Attentional awareness
        if 'attention_focus_strength' in component_state:
            focus_strength = component_state['attention_focus_strength']
            
            metrics['attention_focus_strength'] = MetricValue(
                value=focus_strength,
                timestamp=timestamp,
                component=self.component_type,
                metric_name='attention_focus_strength',
                metric_type=MetricType.GAUGE
            )
        
        # Store metrics
        with self._lock:
            for metric_name, metric_value in metrics.items():
                self.metrics[metric_name].append(metric_value)
        
        return metrics

# =============================================================================
# System Resource Monitors
# =============================================================================

class SystemResourceMonitor:
    """Monitor system resources (CPU, memory, GPU, network)."""
    
    def __init__(self, monitoring_interval: float = 1.0):
        self.monitoring_interval = monitoring_interval
        self.metrics = defaultdict(lambda: deque(maxlen=1000))
        self._stop_event = threading.Event()
        self._monitor_thread = None
        self._lock = threading.RLock()
        
        # GPU monitoring setup
        self.gpu_available = self._check_gpu_availability()
        
    def start_monitoring(self):
        """Start continuous resource monitoring."""
        if self._monitor_thread is None or not self._monitor_thread.is_alive():
            self._stop_event.clear()
            self._monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
            self._monitor_thread.start()
            monitor_logger.info("System resource monitoring started")
    
    def stop_monitoring(self):
        """Stop resource monitoring."""
        if self._monitor_thread and self._monitor_thread.is_alive():
            self._stop_event.set()
            self._monitor_thread.join(timeout=5.0)
            monitor_logger.info("System resource monitoring stopped")
    
    def _check_gpu_availability(self) -> bool:
        """Check if GPU monitoring is available."""
        try:
            GPUtil.getGPUs()
            return True
        except:
            return False
    
    def _monitor_loop(self):
        """Main monitoring loop."""
        while not self._stop_event.is_set():
            try:
                metrics = self.collect_system_metrics()
                
                with self._lock:
                    for metric_name, metric_value in metrics.items():
                        self.metrics[metric_name].append(metric_value)
                
                self._stop_event.wait(self.monitoring_interval)
                
            except Exception as e:
                monitor_logger.error(f"Error in system monitoring loop: {e}")
                self._stop_event.wait(self.monitoring_interval)
    
    def collect_system_metrics(self) -> Dict[str, MetricValue]:
        """Collect comprehensive system metrics."""
        metrics = {}
        timestamp = datetime.now(timezone.utc)
        
        # CPU metrics
        cpu_percent = psutil.cpu_percent(interval=None)
        cpu_count = psutil.cpu_count()
        cpu_freq = psutil.cpu_freq()
        
        metrics['cpu_usage_percent'] = MetricValue(
            value=cpu_percent,
            timestamp=timestamp,
            component=ComponentType.SYSTEM,
            metric_name='cpu_usage_percent',
            metric_type=MetricType.GAUGE,
            tags={'unit': 'percent'}
        )
        
        metrics['cpu_count'] = MetricValue(
            value=cpu_count,
            timestamp=timestamp,
            component=ComponentType.SYSTEM,
            metric_name='cpu_count',
            metric_type=MetricType.GAUGE
        )
        
        if cpu_freq:
            metrics['cpu_frequency_mhz'] = MetricValue(
                value=cpu_freq.current,
                timestamp=timestamp,
                component=ComponentType.SYSTEM,
                metric_name='cpu_frequency_mhz',
                metric_type=MetricType.GAUGE,
                tags={'unit': 'MHz'}
            )
        
        # Memory metrics
        memory = psutil.virtual_memory()
        metrics['memory_usage_percent'] = MetricValue(
            value=memory.percent,
            timestamp=timestamp,
            component=ComponentType.SYSTEM,
            metric_name='memory_usage_percent',
            metric_type=MetricType.GAUGE,
            tags={'unit': 'percent'}
        )
        
        metrics['memory_available_gb'] = MetricValue(
            value=memory.available / (1024**3),
            timestamp=timestamp,
            component=ComponentType.SYSTEM,
            metric_name='memory_available_gb',
            metric_type=MetricType.GAUGE,
            tags={'unit': 'GB'}
        )
        
        metrics['memory_total_gb'] = MetricValue(
            value=memory.total / (1024**3),
            timestamp=timestamp,
            component=ComponentType.SYSTEM,
            metric_name='memory_total_gb',
            metric_type=MetricType.GAUGE,
            tags={'unit': 'GB'}
        )
        
        # Swap memory
        swap = psutil.swap_memory()
        metrics['swap_usage_percent'] = MetricValue(
            value=swap.percent,
            timestamp=timestamp,
            component=ComponentType.SYSTEM,
            metric_name='swap_usage_percent',
            metric_type=MetricType.GAUGE,
            tags={'unit': 'percent'}
        )
        
        # Disk I/O
        disk_io = psutil.disk_io_counters()
        if disk_io:
            metrics['disk_read_mb_per_sec'] = MetricValue(
                value=disk_io.read_bytes / (1024**2),
                timestamp=timestamp,
                component=ComponentType.SYSTEM,
                metric_name='disk_read_mb_per_sec',
                metric_type=MetricType.RATE,
                tags={'unit': 'MB/s'}
            )
            
            metrics['disk_write_mb_per_sec'] = MetricValue(
                value=disk_io.write_bytes / (1024**2),
                timestamp=timestamp,
                component=ComponentType.SYSTEM,
                metric_name='disk_write_mb_per_sec',
                metric_type=MetricType.RATE,
                tags={'unit': 'MB/s'}
            )
        
        # Network I/O
        network_io = psutil.net_io_counters()
        if network_io:
            metrics['network_bytes_sent_per_sec'] = MetricValue(
                value=network_io.bytes_sent,
                timestamp=timestamp,
                component=ComponentType.SYSTEM,
                metric_name='network_bytes_sent_per_sec',
                metric_type=MetricType.RATE,
                tags={'unit': 'bytes/s'}
            )
            
            metrics['network_bytes_recv_per_sec'] = MetricValue(
                value=network_io.bytes_recv,
                timestamp=timestamp,
                component=ComponentType.SYSTEM,
                metric_name='network_bytes_recv_per_sec',
                metric_type=MetricType.RATE,
                tags={'unit': 'bytes/s'}
            )
        
        # GPU metrics
        if self.gpu_available:
            try:
                gpus = GPUtil.getGPUs()
                for i, gpu in enumerate(gpus):
                    metrics[f'gpu_{i}_usage_percent'] = MetricValue(
                        value=gpu.load * 100,
                        timestamp=timestamp,
                        component=ComponentType.SYSTEM,
                        metric_name=f'gpu_{i}_usage_percent',
                        metric_type=MetricType.GAUGE,
                        tags={'gpu_id': str(i), 'unit': 'percent'}
                    )
                    
                    metrics[f'gpu_{i}_memory_usage_percent'] = MetricValue(
                        value=gpu.memoryUtil * 100,
                        timestamp=timestamp,
                        component=ComponentType.SYSTEM,
                        metric_name=f'gpu_{i}_memory_usage_percent',
                        metric_type=MetricType.GAUGE,
                        tags={'gpu_id': str(i), 'unit': 'percent'}
                    )
                    
                    metrics[f'gpu_{i}_temperature_c'] = MetricValue(
                        value=gpu.temperature,
                        timestamp=timestamp,
                        component=ComponentType.SYSTEM,
                        metric_name=f'gpu_{i}_temperature_c',
                        metric_type=MetricType.GAUGE,
                        tags={'gpu_id': str(i), 'unit': 'celsius'}
                    )
            except Exception as e:
                monitor_logger.warning(f"GPU monitoring error: {e}")
        
        # Process-specific metrics
        current_process = psutil.Process()
        
        metrics['process_memory_rss_mb'] = MetricValue(
            value=current_process.memory_info().rss / (1024**2),
            timestamp=timestamp,
            component=ComponentType.SYSTEM,
            metric_name='process_memory_rss_mb',
            metric_type=MetricType.GAUGE,
            tags={'unit': 'MB'}
        )
        
        metrics['process_cpu_percent'] = MetricValue(
            value=current_process.cpu_percent(),
            timestamp=timestamp,
            component=ComponentType.SYSTEM,
            metric_name='process_cpu_percent',
            metric_type=MetricType.GAUGE,
            tags={'unit': 'percent'}
        )
        
        # Python-specific metrics
        if hasattr(gc, 'get_stats'):
            gc_stats = gc.get_stats()
            total_objects = sum(stat['collections'] for stat in gc_stats)
            
            metrics['gc_total_collections'] = MetricValue(
                value=total_objects,
                timestamp=timestamp,
                component=ComponentType.SYSTEM,
                metric_name='gc_total_collections',
                metric_type=MetricType.COUNTER
            )
        
        # PyTorch-specific metrics
        if torch.cuda.is_available():
            for i in range(torch.cuda.device_count()):
                allocated = torch.cuda.memory_allocated(i) / (1024**3)  # GB
                cached = torch.cuda.memory_reserved(i) / (1024**3)  # GB
                
                metrics[f'torch_gpu_{i}_memory_allocated_gb'] = MetricValue(
                    value=allocated,
                    timestamp=timestamp,
                    component=ComponentType.SYSTEM,
                    metric_name=f'torch_gpu_{i}_memory_allocated_gb',
                    metric_type=MetricType.GAUGE,
                    tags={'gpu_id': str(i), 'unit': 'GB'}
                )
                
                metrics[f'torch_gpu_{i}_memory_cached_gb'] = MetricValue(
                    value=cached,
                    timestamp=timestamp,
                    component=ComponentType.SYSTEM,
                    metric_name=f'torch_gpu_{i}_memory_cached_gb',
                    metric_type=MetricType.GAUGE,
                    tags={'gpu_id': str(i), 'unit': 'GB'}
                )
        
        return metrics
    
    def get_system_summary(self) -> Dict[str, Any]:
        """Get a summary of current system state."""
        latest_metrics = {}
        
        with self._lock:
            for metric_name, metric_deque in self.metrics.items():
                if metric_deque:
                    latest_metrics[metric_name] = metric_deque[-1].value
        
        return {
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'metrics': latest_metrics,
            'gpu_available': self.gpu_available,
            'monitoring_active': self._monitor_thread and self._monitor_thread.is_alive()
        }

# =============================================================================
# Alert Management System
# =============================================================================

class AlertManager:
    """Intelligent alert management with escalation and notification."""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or self._default_config()
        self.active_alerts: Dict[str, Alert] = {}
        self.alert_history: deque = deque(maxlen=10000)
        self.notification_channels: List[Callable] = []
        self._lock = threading.RLock()
        
        # Alert thresholds
        self.thresholds = self.config.get('thresholds', {})
        
        # Escalation rules
        self.escalation_rules = self.config.get('escalation_rules', {})
        
        monitor_logger.info("Alert manager initialized")
    
    def _default_config(self) -> Dict[str, Any]:
        """Default alert configuration."""
        return {
            'thresholds': {
                'cpu_usage_percent': {'warning': 80.0, 'critical': 95.0},
                'memory_usage_percent': {'warning': 85.0, 'critical': 95.0},
                'gpu_usage_percent': {'warning': 90.0, 'critical': 98.0},
                'attention_entropy': {'warning': 0.1, 'critical': 0.05},
                'firing_rate_cv': {'warning': 2.0, 'critical': 3.0},
                'integrated_information_phi': {'warning': 0.01, 'critical': 0.005},
                'diffusion_loss': {'warning': 10.0, 'critical': 50.0}
            },
            'escalation_rules': {
                'cpu_usage_percent': {'repeat_interval': 300, 'max_repeats': 5},
                'memory_usage_percent': {'repeat_interval': 180, 'max_repeats': 10},
                'critical_system_failure': {'immediate_escalation': True}
            },
            'notification_settings': {
                'email_enabled': False,
                'slack_enabled': False,
                'webhook_enabled': False
            }
        }
    
    def add_notification_channel(self, channel: Callable[[Alert], None]):
        """Add a notification channel."""
        self.notification_channels.append(channel)
        monitor_logger.info(f"Added notification channel: {channel.__name__}")
    
    def check_metric_thresholds(self, metric: MetricValue) -> Optional[Alert]:
        """Check if metric violates thresholds and create alert if needed."""
        metric_thresholds = self.thresholds.get(metric.metric_name)
        if not metric_thresholds or not isinstance(metric.value, (int, float)):
            return None
        
        severity = None
        threshold_value = None
        
        # Check thresholds in order of severity
        if 'emergency' in metric_thresholds and metric.value >= metric_thresholds['emergency']:
            severity = AlertSeverity.EMERGENCY
            threshold_value = metric_thresholds['emergency']
        elif 'critical' in metric_thresholds and metric.value >= metric_thresholds['critical']:
            severity = AlertSeverity.CRITICAL
            threshold_value = metric_thresholds['critical']
        elif 'error' in metric_thresholds and metric.value >= metric_thresholds['error']:
            severity = AlertSeverity.ERROR
            threshold_value = metric_thresholds['error']
        elif 'warning' in metric_thresholds and metric.value >= metric_thresholds['warning']:
            severity = AlertSeverity.WARNING
            threshold_value = metric_thresholds['warning']
        
        if severity:
            alert_id = f"{metric.component.value}_{metric.metric_name}_{severity.value}"
            
            # Check if alert already exists
            if alert_id in self.active_alerts:
                # Update existing alert
                self.active_alerts[alert_id].value = metric.value
                self.active_alerts[alert_id].timestamp = metric.timestamp
                return None  # Don't create duplicate alert
            
            # Create new alert
            alert = Alert(
                alert_id=alert_id,
                severity=severity,
                component=metric.component,
                metric_name=metric.metric_name,
                message=f"{metric.metric_name} ({metric.value:.2f}) exceeded {severity.value} threshold ({threshold_value:.2f})",
                value=metric.value,
                threshold=threshold_value,
                timestamp=metric.timestamp,
                tags=metric.tags
            )
            
            return alert
        
        return None
    
    def trigger_alert(self, alert: Alert):
        """Trigger an alert and handle notifications."""
        with self._lock:
            # Add to active alerts
            self.active_alerts[alert.alert_id] = alert
            
            # Add to history
            self.alert_history.append(alert)
            
            monitor_logger.warning(f"Alert triggered: {alert.alert_id} - {alert.message}")
            
            # Send notifications
            self._send_notifications(alert)
            
            # Handle escalation
            self._handle_escalation(alert)
    
    def resolve_alert(self, alert_id: str):
        """Resolve an active alert."""
        with self._lock:
            if alert_id in self.active_alerts:
                alert = self.active_alerts[alert_id]
                alert.resolved = True
                alert.resolved_at = datetime.now(timezone.utc)
                
                # Remove from active alerts
                del self.active_alerts[alert_id]
                
                monitor_logger.info(f"Alert resolved: {alert_id}")
                
                # Notify resolution
                self._send_resolution_notifications(alert)
    
    def _send_notifications(self, alert: Alert):
        """Send alert notifications through configured channels."""
        for channel in self.notification_channels:
            try:
                channel(alert)
            except Exception as e:
                monitor_logger.error(f"Failed to send notification via {channel.__name__}: {e}")
    
    def _send_resolution_notifications(self, alert: Alert):
        """Send alert resolution notifications."""
        # Implementation would depend on notification channels
        pass
    
    def _handle_escalation(self, alert: Alert):
        """Handle alert escalation based on rules."""
        escalation_rule = self.escalation_rules.get(alert.metric_name)
        if not escalation_rule:
            return
        
        if escalation_rule.get('immediate_escalation') and alert.severity == AlertSeverity.CRITICAL:
            # Immediate escalation for critical alerts
            self._escalate_alert(alert)
    
    def _escalate_alert(self, alert: Alert):
        """Escalate alert to higher severity or additional channels."""
        monitor_logger.critical(f"Escalating alert: {alert.alert_id}")
        # Implementation would depend on escalation targets
    
    def get_alert_summary(self) -> Dict[str, Any]:
        """Get summary of current alert state."""
        with self._lock:
            severity_counts = defaultdict(int)
            component_counts = defaultdict(int)
            
            for alert in self.active_alerts.values():
                severity_counts[alert.severity.value] += 1
                component_counts[alert.component.value] += 1
            
            return {
                'active_alerts_count': len(self.active_alerts),
                'severity_breakdown': dict(severity_counts),
                'component_breakdown': dict(component_counts),
                'total_alerts_today': len([
                    alert for alert in self.alert_history 
                    if alert.timestamp.date() == datetime.now().date()
                ])
            }
    
    def get_active_alerts(self) -> List[Alert]:
        """Get list of currently active alerts."""
        with self._lock:
            return list(self.active_alerts.values())

# =============================================================================
# Performance Profiler
# =============================================================================

class PerformanceProfiler:
    """Comprehensive performance profiling for ULTRA components."""
    
    def __init__(self):
        self.profiles: Dict[str, Dict[str, Any]] = {}
        self.execution_times: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        self.memory_snapshots: Dict[str, deque] = defaultdict(lambda: deque(maxlen=100))
        self._active_profiles: Dict[str, Dict[str, Any]] = {}
        self._lock = threading.RLock()
    
    @contextmanager
    def profile_execution(self, component_name: str, operation_name: str):
        """Context manager for profiling execution time and memory usage."""
        profile_key = f"{component_name}_{operation_name}"
        
        # Start profiling
        start_time = time.perf_counter()
        start_memory = self._get_memory_usage()
        
        with self._lock:
            self._active_profiles[profile_key] = {
                'start_time': start_time,
                'start_memory': start_memory,
                'component': component_name,
                'operation': operation_name
            }
        
        try:
            yield
        finally:
            # End profiling
            end_time = time.perf_counter()
            end_memory = self._get_memory_usage()
            
            execution_time = end_time - start_time
            memory_delta = end_memory - start_memory
            
            with self._lock:
                # Store execution time
                self.execution_times[profile_key].append(execution_time)
                
                # Store memory snapshot
                self.memory_snapshots[profile_key].append({
                    'timestamp': datetime.now(timezone.utc),
                    'memory_delta': memory_delta,
                    'peak_memory': end_memory
                })
                
                # Clean up active profile
                if profile_key in self._active_profiles:
                    del self._active_profiles[profile_key]
                
                monitor_logger.debug(f"Profile completed: {profile_key} - {execution_time:.4f}s, {memory_delta:.2f}MB")
    
    def _get_memory_usage(self) -> float:
        """Get current memory usage in MB."""
        process = psutil.Process()
        return process.memory_info().rss / (1024**2)
    
    def get_performance_statistics(self, component_name: str, operation_name: str) -> Dict[str, Any]:
        """Get performance statistics for a specific operation."""
        profile_key = f"{component_name}_{operation_name}"
        
        with self._lock:
            execution_times = list(self.execution_times[profile_key])
            memory_snapshots = list(self.memory_snapshots[profile_key])
        
        if not execution_times:
            return {}
        
        stats = {
            'execution_time': {
                'mean': np.mean(execution_times),
                'std': np.std(execution_times),
                'min': np.min(execution_times),
                'max': np.max(execution_times),
                'median': np.median(execution_times),
                'p95': np.percentile(execution_times, 95),
                'p99': np.percentile(execution_times, 99),
                'count': len(execution_times)
            }
        }
        
        if memory_snapshots:
            memory_deltas = [snapshot['memory_delta'] for snapshot in memory_snapshots]
            peak_memories = [snapshot['peak_memory'] for snapshot in memory_snapshots]
            
            stats['memory'] = {
                'mean_delta': np.mean(memory_deltas),
                'max_delta': np.max(memory_deltas),
                'mean_peak': np.mean(peak_memories),
                'max_peak': np.max(peak_memories)
            }
        
        return stats
    
    def get_bottlenecks(self, top_n: int = 10) -> List[Dict[str, Any]]:
        """Identify performance bottlenecks."""
        bottlenecks = []
        
        with self._lock:
            for profile_key, times in self.execution_times.items():
                if len(times) < 5:  # Need enough samples
                    continue
                
                mean_time = np.mean(times)
                p95_time = np.percentile(times, 95)
                variability = np.std(times) / mean_time if mean_time > 0 else 0
                
                component, operation = profile_key.split('_', 1)
                
                bottlenecks.append({
                    'component': component,
                    'operation': operation,
                    'mean_execution_time': mean_time,
                    'p95_execution_time': p95_time,
                    'variability': variability,
                    'sample_count': len(times)
                })
        
        # Sort by mean execution time
        bottlenecks.sort(key=lambda x: x['mean_execution_time'], reverse=True)
        
        return bottlenecks[:top_n]
    
    def generate_performance_report(self) -> str:
        """Generate a comprehensive performance report."""
        bottlenecks = self.get_bottlenecks()
        
        report = "ULTRA Performance Analysis Report\n"
        report += "=" * 50 + "\n\n"
        
        report += f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
        
        report += "Top Performance Bottlenecks:\n"
        report += "-" * 30 + "\n"
        
        for i, bottleneck in enumerate(bottlenecks[:5], 1):
            report += f"{i}. {bottleneck['component']}.{bottleneck['operation']}\n"
            report += f"   Mean Time: {bottleneck['mean_execution_time']:.4f}s\n"
            report += f"   P95 Time: {bottleneck['p95_execution_time']:.4f}s\n"
            report += f"   Variability: {bottleneck['variability']:.3f}\n"
            report += f"   Samples: {bottleneck['sample_count']}\n\n"
        
        # Overall statistics
        with self._lock:
            total_operations = sum(len(times) for times in self.execution_times.values())
            total_components = len(set(key.split('_')[0] for key in self.execution_times.keys()))
        
        report += f"Overall Statistics:\n"
        report += f"Total Operations Profiled: {total_operations}\n"
        report += f"Components Monitored: {total_components}\n"
        
        return report

# =============================================================================
# Data Storage and Export
# =============================================================================

class MetricsStorage:
    """Storage backend for metrics data with multiple export formats."""
    
    def __init__(self, storage_type: str = "sqlite", config: Optional[Dict[str, Any]] = None):
        self.storage_type = storage_type
        self.config = config or {}
        self._lock = threading.RLock()
        
        if storage_type == "sqlite":
            self._init_sqlite()
        elif storage_type == "redis":
            self._init_redis()
        elif storage_type == "influxdb":
            self._init_influxdb()
        else:
            raise ValueError(f"Unsupported storage type: {storage_type}")
    
    def _init_sqlite(self):
        """Initialize SQLite storage."""
        db_path = self.config.get('db_path', 'ultra_metrics.db')
        self.connection = sqlite3.connect(db_path, check_same_thread=False)
        
        # Create tables
        self.connection.execute('''
            CREATE TABLE IF NOT EXISTS metrics (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT NOT NULL,
                component TEXT NOT NULL,
                metric_name TEXT NOT NULL,
                metric_type TEXT NOT NULL,
                value REAL,
                tags TEXT,
                metadata TEXT
            )
        ''')
        
        self.connection.execute('''
            CREATE INDEX IF NOT EXISTS idx_metrics_timestamp 
            ON metrics(timestamp)
        ''')
        
        self.connection.execute('''
            CREATE INDEX IF NOT EXISTS idx_metrics_component_metric 
            ON metrics(component, metric_name)
        ''')
        
        self.connection.commit()
    
    def _init_redis(self):
        """Initialize Redis storage."""
        redis_config = self.config.get('redis', {})
        self.redis_client = redis.Redis(
            host=redis_config.get('host', 'localhost'),
            port=redis_config.get('port', 6379),
            db=redis_config.get('db', 0),
            decode_responses=True
        )
    
    def _init_influxdb(self):
        """Initialize InfluxDB storage."""
        influx_config = self.config.get('influxdb', {})
        self.influx_client = influxdb_client.InfluxDBClient(
            url=influx_config.get('url', 'http://localhost:8086'),
            token=influx_config.get('token'),
            org=influx_config.get('org', 'ultra')
        )
        self.write_api = self.influx_client.write_api(write_options=SYNCHRONOUS)
        self.bucket = influx_config.get('bucket', 'ultra_metrics')
    
    def store_metric(self, metric: MetricValue):
        """Store a single metric."""
        with self._lock:
            if self.storage_type == "sqlite":
                self._store_metric_sqlite(metric)
            elif self.storage_type == "redis":
                self._store_metric_redis(metric)
            elif self.storage_type == "influxdb":
                self._store_metric_influxdb(metric)
    
    def store_metrics(self, metrics: List[MetricValue]):
        """Store multiple metrics efficiently."""
        with self._lock:
            if self.storage_type == "sqlite":
                self._store_metrics_sqlite(metrics)
            elif self.storage_type == "redis":
                for metric in metrics:
                    self._store_metric_redis(metric)
            elif self.storage_type == "influxdb":
                self._store_metrics_influxdb(metrics)
    
    def _store_metric_sqlite(self, metric: MetricValue):
        """Store metric in SQLite."""
        self.connection.execute('''
            INSERT INTO metrics (timestamp, component, metric_name, metric_type, value, tags, metadata)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', (
            metric.timestamp.isoformat(),
            metric.component.value,
            metric.metric_name,
            metric.metric_type.value,
            metric.value if isinstance(metric.value, (int, float)) else str(metric.value),
            json.dumps(metric.tags),
            json.dumps(metric.metadata)
        ))
        self.connection.commit()
    
    def _store_metrics_sqlite(self, metrics: List[MetricValue]):
        """Store multiple metrics in SQLite efficiently."""
        data = []
        for metric in metrics:
            data.append((
                metric.timestamp.isoformat(),
                metric.component.value,
                metric.metric_name,
                metric.metric_type.value,
                metric.value if isinstance(metric.value, (int, float)) else str(metric.value),
                json.dumps(metric.tags),
                json.dumps(metric.metadata)
            ))
        
        self.connection.executemany('''
            INSERT INTO metrics (timestamp, component, metric_name, metric_type, value, tags, metadata)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', data)
        self.connection.commit()
    
    def _store_metric_redis(self, metric: MetricValue):
        """Store metric in Redis."""
        key = f"metric:{metric.component.value}:{metric.metric_name}"
        value = {
            'timestamp': metric.timestamp.isoformat(),
            'value': metric.value,
            'metric_type': metric.metric_type.value,
            'tags': json.dumps(metric.tags),
            'metadata': json.dumps(metric.metadata)
        }
        
        # Store as hash
        self.redis_client.hset(key, mapping=value)
        
        # Set expiration (default 7 days)
        ttl = self.config.get('ttl', 7 * 24 * 3600)
        self.redis_client.expire(key, ttl)
        
        # Also add to time series
        ts_key = f"ts:{metric.component.value}:{metric.metric_name}"
        timestamp_ms = int(metric.timestamp.timestamp() * 1000)
        self.redis_client.zadd(ts_key, {json.dumps(value): timestamp_ms})
    
    def _store_metric_influxdb(self, metric: MetricValue):
        """Store metric in InfluxDB."""
        point = {
            "measurement": metric.metric_name,
            "tags": {
                "component": metric.component.value,
                "metric_type": metric.metric_type.value,
                **metric.tags
            },
            "fields": {"value": metric.value},
            "time": metric.timestamp
        }
        
        self.write_api.write(bucket=self.bucket, record=point)
    
    def _store_metrics_influxdb(self, metrics: List[MetricValue]):
        """Store multiple metrics in InfluxDB."""
        points = []
        for metric in metrics:
            point = {
                "measurement": metric.metric_name,
                "tags": {
                    "component": metric.component.value,
                    "metric_type": metric.metric_type.value,
                    **metric.tags
                },
                "fields": {"value": metric.value},
                "time": metric.timestamp
            }
            points.append(point)
        
        self.write_api.write(bucket=self.bucket, record=points)
    
    def query_metrics(self, component: Optional[ComponentType] = None,
                     metric_name: Optional[str] = None,
                     start_time: Optional[datetime] = None,
                     end_time: Optional[datetime] = None,
                     limit: int = 1000) -> List[MetricValue]:
        """Query metrics from storage."""
        with self._lock:
            if self.storage_type == "sqlite":
                return self._query_metrics_sqlite(component, metric_name, start_time, end_time, limit)
            elif self.storage_type == "redis":
                return self._query_metrics_redis(component, metric_name, start_time, end_time, limit)
            elif self.storage_type == "influxdb":
                return self._query_metrics_influxdb(component, metric_name, start_time, end_time, limit)
        
        return []
    
    def _query_metrics_sqlite(self, component: Optional[ComponentType],
                            metric_name: Optional[str],
                            start_time: Optional[datetime],
                            end_time: Optional[datetime],
                            limit: int) -> List[MetricValue]:
        """Query metrics from SQLite."""
        query = "SELECT * FROM metrics WHERE 1=1"
        params = []
        
        if component:
            query += " AND component = ?"
            params.append(component.value)
        
        if metric_name:
            query += " AND metric_name = ?"
            params.append(metric_name)
        
        if start_time:
            query += " AND timestamp >= ?"
            params.append(start_time.isoformat())
        
        if end_time:
            query += " AND timestamp <= ?"
            params.append(end_time.isoformat())
        
        query += " ORDER BY timestamp DESC LIMIT ?"
        params.append(limit)
        
        cursor = self.connection.execute(query, params)
        rows = cursor.fetchall()
        
        metrics = []
        for row in rows:
            metric = MetricValue(
                value=row[5],  # value
                timestamp=datetime.fromisoformat(row[1]),  # timestamp
                component=ComponentType(row[2]),  # component
                metric_name=row[3],  # metric_name
                metric_type=MetricType(row[4]),  # metric_type
                tags=json.loads(row[6]) if row[6] else {},  # tags
                metadata=json.loads(row[7]) if row[7] else {}  # metadata
            )
            metrics.append(metric)
        
        return metrics
    
    def export_metrics(self, format_type: str, output_path: str,
                      start_time: Optional[datetime] = None,
                      end_time: Optional[datetime] = None):
        """Export metrics to various formats."""
        metrics = self.query_metrics(start_time=start_time, end_time=end_time, limit=100000)
        
        if format_type.lower() == 'csv':
            self._export_csv(metrics, output_path)
        elif format_type.lower() == 'json':
            self._export_json(metrics, output_path)
        elif format_type.lower() == 'parquet':
            self._export_parquet(metrics, output_path)
        else:
            raise ValueError(f"Unsupported export format: {format_type}")
    
    def _export_csv(self, metrics: List[MetricValue], output_path: str):
        """Export metrics to CSV."""
        import csv
        
        with open(output_path, 'w', newline='') as csvfile:
            fieldnames = ['timestamp', 'component', 'metric_name', 'metric_type', 'value', 'tags', 'metadata']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            
            writer.writeheader()
            for metric in metrics:
                writer.writerow({
                    'timestamp': metric.timestamp.isoformat(),
                    'component': metric.component.value,
                    'metric_name': metric.metric_name,
                    'metric_type': metric.metric_type.value,
                    'value': metric.value,
                    'tags': json.dumps(metric.tags),
                    'metadata': json.dumps(metric.metadata)
                })
    
    def _export_json(self, metrics: List[MetricValue], output_path: str):
        """Export metrics to JSON."""
        data = [metric.to_dict() for metric in metrics]
        
        with open(output_path, 'w') as jsonfile:
            json.dump(data, jsonfile, indent=2, default=str)
    
    def _export_parquet(self, metrics: List[MetricValue], output_path: str):
        """Export metrics to Parquet format."""
        try:
            import pyarrow as pa
            import pyarrow.parquet as pq
        except ImportError:
            raise ImportError("pyarrow is required for Parquet export")
        
        # Convert to pandas DataFrame first
        data = []
        for metric in metrics:
            data.append({
                'timestamp': metric.timestamp,
                'component': metric.component.value,
                'metric_name': metric.metric_name,
                'metric_type': metric.metric_type.value,
                'value': metric.value,
                'tags': json.dumps(metric.tags),
                'metadata': json.dumps(metric.metadata)
            })
        
        df = pd.DataFrame(data)
        table = pa.Table.from_pandas(df)
        pq.write_table(table, output_path)

# =============================================================================
# Main Monitoring System
# =============================================================================

class ULTRAMonitoringSystem:
    """Main monitoring system that coordinates all monitoring components."""
    
    def __init__(self, config: Optional[ULTRAConfig] = None):
        self.config = config or ULTRAConfig()
        
        # Initialize monitoring level
        self.monitoring_level = MonitoringLevel.STANDARD
        
        # Initialize component monitors
        self.component_monitors = {
            ComponentType.CORE_NEURAL: CoreNeuralMonitor(self.monitoring_level),
            ComponentType.TRANSFORMER: TransformerMonitor(self.monitoring_level),
            ComponentType.DIFFUSION: DiffusionMonitor(self.monitoring_level),
            ComponentType.CONSCIOUSNESS: ConsciousnessMonitor(self.monitoring_level)
        }
        
        # Initialize system monitors
        self.system_monitor = SystemResourceMonitor()
        self.alert_manager = AlertManager()
        self.performance_profiler = PerformanceProfiler()
        
        # Initialize storage
        storage_config = {
            'db_path': self.config.logging_config.log_file_path.replace('.log', '_metrics.db')
        }
        self.metrics_storage = MetricsStorage('sqlite', storage_config)
        
        # Monitoring state
        self._monitoring_active = False
        self._monitor_thread = None
        self._stop_event = threading.Event()
        self._lock = threading.RLock()
        
        # Metrics aggregation
        self.metrics_buffer = deque(maxlen=10000)
        self.last_aggregation = datetime.now(timezone.utc)
        
        # Setup default notification channels
        self._setup_default_notifications()
        
        monitor_logger.info("ULTRA Monitoring System initialized")
    
    def _setup_default_notifications(self):
        """Setup default notification channels."""
        def log_alert(alert: Alert):
            """Log alert to monitoring logger."""
            level_map = {
                AlertSeverity.INFO: logging.INFO,
                AlertSeverity.WARNING: logging.WARNING,
                AlertSeverity.ERROR: logging.ERROR,
                AlertSeverity.CRITICAL: logging.CRITICAL,
                AlertSeverity.EMERGENCY: logging.CRITICAL
            }
            
            level = level_map.get(alert.severity, logging.WARNING)
            monitor_logger.log(level, f"ALERT: {alert.message}")
        
        self.alert_manager.add_notification_channel(log_alert)
    
    def start_monitoring(self):
        """Start the monitoring system."""
        if self._monitoring_active:
            monitor_logger.warning("Monitoring already active")
            return
        
        with self._lock:
            self._monitoring_active = True
            self._stop_event.clear()
            
            # Start system resource monitoring
            self.system_monitor.start_monitoring()
            
            # Start main monitoring loop
            self._monitor_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
            self._monitor_thread.start()
            
            monitor_logger.info("ULTRA Monitoring System started")
    
    def stop_monitoring(self):
        """Stop the monitoring system."""
        if not self._monitoring_active:
            return
        
        with self._lock:
            self._monitoring_active = False
            self._stop_event.set()
            
            # Stop system resource monitoring
            self.system_monitor.stop_monitoring()
            
            # Wait for monitoring thread to finish
            if self._monitor_thread and self._monitor_thread.is_alive():
                self._monitor_thread.join(timeout=5.0)
            
            monitor_logger.info("ULTRA Monitoring System stopped")
    
    def _monitoring_loop(self):
        """Main monitoring loop."""
        while not self._stop_event.is_set() and self._monitoring_active:
            try:
                # Process metrics buffer
                self._process_metrics_buffer()
                
                # Check for alerts
                self._check_alerts()
                
                # Aggregate metrics periodically
                if (datetime.now(timezone.utc) - self.last_aggregation).total_seconds() > 60:
                    self._aggregate_metrics()
                    self.last_aggregation = datetime.now(timezone.utc)
                
                # Sleep for monitoring interval
                self._stop_event.wait(1.0)
                
            except Exception as e:
                monitor_logger.error(f"Error in monitoring loop: {e}")
                self._stop_event.wait(5.0)  # Wait longer on error
    
    def update_component_state(self, component_type: ComponentType, state: Dict[str, Any]):
        """Update component state and collect metrics."""
        if not self._monitoring_active:
            return
        
        monitor = self.component_monitors.get(component_type)
        if monitor:
            try:
                metrics = monitor.collect_metrics(state)
                
                # Add metrics to buffer
                with self._lock:
                    for metric in metrics.values():
                        self.metrics_buffer.append(metric)
                
            except Exception as e:
                monitor_logger.error(f"Error collecting metrics for {component_type.value}: {e}")
    
    def _process_metrics_buffer(self):
        """Process metrics from buffer."""
        if not self.metrics_buffer:
            return
        
        with self._lock:
            # Get metrics from buffer
            metrics_to_process = list(self.metrics_buffer)
            self.metrics_buffer.clear()
        
        # Store metrics in persistent storage
        try:
            self.metrics_storage.store_metrics(metrics_to_process)
        except Exception as e:
            monitor_logger.error(f"Error storing metrics: {e}")
        
        # Check each metric for alert conditions
        for metric in metrics_to_process:
            alert = self.alert_manager.check_metric_thresholds(metric)
            if alert:
                self.alert_manager.trigger_alert(alert)
    
    def _check_alerts(self):
        """Check for alert conditions and auto-resolution."""
        current_time = datetime.now(timezone.utc)
        
        # Check for alerts that should auto-resolve
        for alert_id, alert in list(self.alert_manager.active_alerts.items()):
            # Auto-resolve alerts older than 5 minutes if metric improved
            if (current_time - alert.timestamp).total_seconds() > 300:
                # Get latest metric value
                recent_metrics = self.metrics_storage.query_metrics(
                    component=alert.component,
                    metric_name=alert.metric_name,
                    start_time=current_time - timedelta(minutes=1),
                    limit=1
                )
                
                if recent_metrics:
                    latest_metric = recent_metrics[0]
                    if isinstance(latest_metric.value, (int, float)):
                        if latest_metric.value < alert.threshold:
                            self.alert_manager.resolve_alert(alert_id)
    
    def _aggregate_metrics(self):
        """Aggregate metrics for better performance and storage efficiency."""
        current_time = datetime.now(timezone.utc)
        start_time = current_time - timedelta(hours=1)
        
        # Get metrics from last hour
        metrics = self.metrics_storage.query_metrics(
            start_time=start_time,
            end_time=current_time,
            limit=100000
        )
        
        # Group metrics by component and name
        grouped_metrics = defaultdict(list)
        for metric in metrics:
            key = f"{metric.component.value}_{metric.metric_name}"
            grouped_metrics[key].append(metric)
        
        # Create aggregated metrics
        aggregated_metrics = []
        for key, metric_group in grouped_metrics.items():
            if len(metric_group) < 2:
                continue
            
            numeric_values = [m.value for m in metric_group if isinstance(m.value, (int, float))]
            if not numeric_values:
                continue
            
            # Create aggregated metric
            component_name, metric_name = key.split('_', 1)
            aggregated_metric = MetricValue(
                value=np.mean(numeric_values),
                timestamp=current_time,
                component=ComponentType(component_name),
                metric_name=f"{metric_name}_hourly_avg",
                metric_type=MetricType.GAUGE,
                metadata={
                    'aggregation': 'hourly_average',
                    'sample_count': len(numeric_values),
                    'min': float(np.min(numeric_values)),
                    'max': float(np.max(numeric_values)),
                    'std': float(np.std(numeric_values))
                }
            )
            aggregated_metrics.append(aggregated_metric)
        
        # Store aggregated metrics
        if aggregated_metrics:
            try:
                self.metrics_storage.store_metrics(aggregated_metrics)
                monitor_logger.debug(f"Stored {len(aggregated_metrics)} aggregated metrics")
            except Exception as e:
                monitor_logger.error(f"Error storing aggregated metrics: {e}")
    
    def get_system_health_score(self) -> float:
        """Calculate overall system health score (0-1)."""
        health_factors = []
        
        # System resource health
        system_summary = self.system_monitor.get_system_summary()
        if 'metrics' in system_summary:
            metrics = system_summary['metrics']
            
            # CPU health (inverted usage)
            if 'cpu_usage_percent' in metrics:
                cpu_health = max(0, 1 - metrics['cpu_usage_percent'] / 100)
                health_factors.append(('cpu', cpu_health, 0.2))
            
            # Memory health (inverted usage)
            if 'memory_usage_percent' in metrics:
                memory_health = max(0, 1 - metrics['memory_usage_percent'] / 100)
                health_factors.append(('memory', memory_health, 0.2))
            
            # GPU health (if available)
            gpu_healths = []
            for key, value in metrics.items():
                if 'gpu_' in key and 'usage_percent' in key:
                    gpu_health = max(0, 1 - value / 100)
                    gpu_healths.append(gpu_health)
            
            if gpu_healths:
                avg_gpu_health = np.mean(gpu_healths)
                health_factors.append(('gpu', avg_gpu_health, 0.15))
        
        # Component health based on recent metrics
        for component_type, monitor in self.component_monitors.items():
            component_health = self._calculate_component_health(monitor)
            weight = 0.45 / len(self.component_monitors)  # Split remaining weight
            health_factors.append((component_type.value, component_health, weight))
        
        # Alert impact on health
        alert_summary = self.alert_manager.get_alert_summary()
        alert_count = alert_summary['active_alerts_count']
        alert_health = max(0, 1 - min(alert_count / 10, 1))  # Max 10 alerts for 0 health
        health_factors.append(('alerts', alert_health, 0.1))
        
        # Calculate weighted average
        if not health_factors:
            return 1.0
        
        total_weight = sum(weight for _, _, weight in health_factors)
        weighted_sum = sum(health * weight for _, health, weight in health_factors)
        
        return weighted_sum / total_weight if total_weight > 0 else 1.0
    
    def _calculate_component_health(self, monitor: ComponentMonitor) -> float:
        """Calculate health score for a specific component."""
        health_scores = []
        
        # Check recent metric trends
        for metric_name in monitor.metrics.keys():
            stats = monitor.get_metric_statistics(metric_name)
            if not stats:
                continue
            
            # Health based on stability (low variability is good)
            if 'std' in stats and 'mean' in stats and stats['mean'] != 0:
                cv = stats['std'] / abs(stats['mean'])  # Coefficient of variation
                stability_health = max(0, 1 - min(cv, 1))  # CV > 1 means very unstable
                health_scores.append(stability_health)
        
        return np.mean(health_scores) if health_scores else 0.5  # Neutral if no data
    
    def get_monitoring_dashboard_data(self) -> Dict[str, Any]:
        """Get comprehensive data for monitoring dashboard."""
        current_time = datetime.now(timezone.utc)
        
        # System overview
        system_summary = self.system_monitor.get_system_summary()
        health_score = self.get_system_health_score()
        alert_summary = self.alert_manager.get_alert_summary()
        
        # Component metrics
        component_data = {}
        for component_type, monitor in self.component_monitors.items():
            component_metrics = {}
            for metric_name in monitor.metrics.keys():
                recent_metrics = monitor.get_recent_metrics(metric_name, 50)
                if recent_metrics:
                    values = [m.value for m in recent_metrics if isinstance(m.value, (int, float))]
                    timestamps = [m.timestamp.isoformat() for m in recent_metrics if isinstance(m.value, (int, float))]
                    
                    component_metrics[metric_name] = {
                        'values': values,
                        'timestamps': timestamps,
                        'latest': values[-1] if values else None,
                        'statistics': monitor.get_metric_statistics(metric_name)
                    }
            
            component_data[component_type.value] = component_metrics
        
        # Performance data
        bottlenecks = self.performance_profiler.get_bottlenecks(5)
        
        return {
            'timestamp': current_time.isoformat(),
            'system_health_score': health_score,
            'system_resources': system_summary,
            'alerts': {
                'summary': alert_summary,
                'active': [alert.to_dict() for alert in self.alert_manager.get_active_alerts()]
            },
            'components': component_data,
            'performance': {
                'bottlenecks': bottlenecks
            },
            'monitoring_status': {
                'active': self._monitoring_active,
                'level': self.monitoring_level.value,
                'uptime_seconds': (current_time - self.last_aggregation).total_seconds()
            }
        }
    
    def generate_health_report(self) -> str:
        """Generate comprehensive health report."""
        dashboard_data = self.get_monitoring_dashboard_data()
        
        report = "ULTRA System Health Report\n"
        report += "=" * 50 + "\n\n"
        
        report += f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
        report += f"Overall Health Score: {dashboard_data['system_health_score']:.2f}/1.00\n\n"
        
        # System Resources
        report += "System Resources:\n"
        report += "-" * 20 + "\n"
        if 'metrics' in dashboard_data['system_resources']:
            metrics = dashboard_data['system_resources']['metrics']
            report += f"CPU Usage: {metrics.get('cpu_usage_percent', 0):.1f}%\n"
            report += f"Memory Usage: {metrics.get('memory_usage_percent', 0):.1f}%\n"
            report += f"Available Memory: {metrics.get('memory_available_gb', 0):.1f} GB\n"
            
            # GPU info
            gpu_metrics = {k: v for k, v in metrics.items() if 'gpu_' in k and 'usage_percent' in k}
            if gpu_metrics:
                report += "GPU Usage:\n"
                for gpu_metric, usage in gpu_metrics.items():
                    gpu_id = gpu_metric.split('_')[1]
                    report += f"  GPU {gpu_id}: {usage:.1f}%\n"
        
        report += "\n"
        
        # Alerts
        alert_summary = dashboard_data['alerts']['summary']
        report += f"Active Alerts: {alert_summary['active_alerts_count']}\n"
        if alert_summary['severity_breakdown']:
            report += "Alert Breakdown:\n"
            for severity, count in alert_summary['severity_breakdown'].items():
                report += f"  {severity.title()}: {count}\n"
        
        report += "\n"
        
        # Component Health
        report += "Component Status:\n"
        report += "-" * 20 + "\n"
        for component_name, component_data in dashboard_data['components'].items():
            if component_data:
                report += f"{component_name.replace('_', ' ').title()}:\n"
                
                # Show key metrics for each component
                key_metrics = list(component_data.keys())[:3]  # Show top 3 metrics
                for metric_name in key_metrics:
                    metric_data = component_data[metric_name]
                    if metric_data['latest'] is not None:
                        report += f"  {metric_name}: {metric_data['latest']:.3f}\n"
                
                report += "\n"
        
        # Performance Bottlenecks
        if dashboard_data['performance']['bottlenecks']:
            report += "Performance Bottlenecks:\n"
            report += "-" * 25 + "\n"
            for i, bottleneck in enumerate(dashboard_data['performance']['bottlenecks'][:3], 1):
                report += f"{i}. {bottleneck['component']}.{bottleneck['operation']}\n"
                report += f"   Average Time: {bottleneck['mean_execution_time']:.4f}s\n"
                report += f"   Samples: {bottleneck['sample_count']}\n\n"
        
        return report
    
    def set_monitoring_level(self, level: MonitoringLevel):
        """Change monitoring detail level."""
        self.monitoring_level = level
        
        # Update all component monitors
        for monitor in self.component_monitors.values():
            monitor.monitoring_level = level
        
        monitor_logger.info(f"Monitoring level changed to: {level.value}")
    
    def add_custom_metric(self, component_type: ComponentType, metric_name: str, 
                         value: Union[float, int], tags: Optional[Dict[str, str]] = None):
        """Add a custom metric."""
        if not self._monitoring_active:
            return
        
        custom_metric = MetricValue(
            value=value,
            timestamp=datetime.now(timezone.utc),
            component=component_type,
            metric_name=metric_name,
            metric_type=MetricType.GAUGE,
            tags=tags or {},
            metadata={'source': 'custom'}
        )
        
        with self._lock:
            self.metrics_buffer.append(custom_metric)
    
    def export_monitoring_data(self, format_type: str, output_path: str,
                              start_time: Optional[datetime] = None,
                              end_time: Optional[datetime] = None):
        """Export monitoring data to file."""
        try:
            self.metrics_storage.export_metrics(format_type, output_path, start_time, end_time)
            monitor_logger.info(f"Monitoring data exported to: {output_path}")
        except Exception as e:
            monitor_logger.error(f"Failed to export monitoring data: {e}")
            raise
    
    def cleanup_old_data(self, retention_days: int = 7):
        """Clean up old monitoring data."""
        cutoff_time = datetime.now(timezone.utc) - timedelta(days=retention_days)
        
        # This would need to be implemented based on storage backend
        if hasattr(self.metrics_storage, 'connection'):  # SQLite
            try:
                self.metrics_storage.connection.execute(
                    "DELETE FROM metrics WHERE timestamp < ?",
                    (cutoff_time.isoformat(),)
                )
                self.metrics_storage.connection.commit()
                monitor_logger.info(f"Cleaned up monitoring data older than {retention_days} days")
            except Exception as e:
                monitor_logger.error(f"Failed to cleanup old data: {e}")
    
    def __enter__(self):
        """Context manager entry."""
        self.start_monitoring()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.stop_monitoring()

# =============================================================================
# Visualization and Dashboard Components
# =============================================================================

class MonitoringVisualizer:
    """Create visualizations for monitoring data."""
    
    def __init__(self, monitoring_system: ULTRAMonitoringSystem):
        self.monitoring_system = monitoring_system
        
    def create_system_overview_plot(self, hours: int = 24) -> go.Figure:
        """Create system overview plot showing key metrics over time."""
        end_time = datetime.now(timezone.utc)
        start_time = end_time - timedelta(hours=hours)
        
        # Get system metrics
        cpu_metrics = self.monitoring_system.metrics_storage.query_metrics(
            component=ComponentType.SYSTEM,
            metric_name='cpu_usage_percent',
            start_time=start_time,
            end_time=end_time,
            limit=1000
        )
        
        memory_metrics = self.monitoring_system.metrics_storage.query_metrics(
            component=ComponentType.SYSTEM,
            metric_name='memory_usage_percent',
            start_time=start_time,
            end_time=end_time,
            limit=1000
        )
        
        # Create subplot figure
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=('CPU Usage', 'Memory Usage', 'System Health Score', 'Active Alerts'),
            specs=[[{"secondary_y": False}, {"secondary_y": False}],
                   [{"secondary_y": False}, {"secondary_y": False}]]
        )
        
        # CPU Usage
        if cpu_metrics:
            timestamps = [m.timestamp for m in cpu_metrics]
            values = [m.value for m in cpu_metrics]
            
            fig.add_trace(
                go.Scatter(x=timestamps, y=values, name='CPU %', line=dict(color='blue')),
                row=1, col=1
            )
        
        # Memory Usage
        if memory_metrics:
            timestamps = [m.timestamp for m in memory_metrics]
            values = [m.value for m in memory_metrics]
            
            fig.add_trace(
                go.Scatter(x=timestamps, y=values, name='Memory %', line=dict(color='red')),
                row=1, col=2
            )
        
        # System Health Score (would need to be stored as metric)
        health_score = self.monitoring_system.get_system_health_score()
        fig.add_trace(
            go.Indicator(
                mode="gauge+number",
                value=health_score,
                domain={'x': [0, 1], 'y': [0, 1]},
                title={'text': "Health Score"},
                gauge={'axis': {'range': [None, 1]},
                       'bar': {'color': "darkblue"},
                       'steps': [
                           {'range': [0, 0.5], 'color': "lightgray"},
                           {'range': [0.5, 0.8], 'color': "yellow"},
                           {'range': [0.8, 1], 'color': "green"}
                       ],
                       'threshold': {'line': {'color': "red", 'width': 4},
                                   'thickness': 0.75, 'value': 0.9}}
            ),
            row=2, col=1
        )
        
        # Active Alerts
        alert_summary = self.monitoring_system.alert_manager.get_alert_summary()
        severity_counts = alert_summary['severity_breakdown']
        
        if severity_counts:
            fig.add_trace(
                go.Bar(
                    x=list(severity_counts.keys()),
                    y=list(severity_counts.values()),
                    name='Alerts',
                    marker_color=['green', 'yellow', 'orange', 'red', 'purple']
                ),
                row=2, col=2
            )
        
        fig.update_layout(
            title='ULTRA System Overview',
            showlegend=True,
            height=600,
            template='plotly_white'
        )
        
        return fig
    
    def create_component_metrics_plot(self, component_type: ComponentType, 
                                    metric_names: List[str], hours: int = 6) -> go.Figure:
        """Create plot for specific component metrics."""
        end_time = datetime.now(timezone.utc)
        start_time = end_time - timedelta(hours=hours)
        
        fig = go.Figure()
        
        colors = ['blue', 'red', 'green', 'orange', 'purple', 'brown', 'pink', 'gray']
        
        for i, metric_name in enumerate(metric_names):
            metrics = self.monitoring_system.metrics_storage.query_metrics(
                component=component_type,
                metric_name=metric_name,
                start_time=start_time,
                end_time=end_time,
                limit=1000
            )
            
            if metrics:
                timestamps = [m.timestamp for m in metrics]
                values = [m.value for m in metrics if isinstance(m.value, (int, float))]
                
                if values:
                    fig.add_trace(go.Scatter(
                        x=timestamps[:len(values)],  # Ensure same length
                        y=values,
                        mode='lines+markers',
                        name=metric_name,
                        line=dict(color=colors[i % len(colors)])
                    ))
        
        fig.update_layout(
            title=f'{component_type.value.replace("_", " ").title()} Metrics',
            xaxis_title='Time',
            yaxis_title='Value',
            template='plotly_white',
            height=400
        )
        
        return fig
    
    def create_performance_heatmap(self) -> go.Figure:
        """Create performance heatmap showing bottlenecks."""
        bottlenecks = self.monitoring_system.performance_profiler.get_bottlenecks(20)
        
        if not bottlenecks:
            # Return empty figure if no data
            fig = go.Figure()
            fig.add_annotation(
                text="No performance data available",
                xref="paper", yref="paper",
                x=0.5, y=0.5, xanchor='center', yanchor='middle',
                showarrow=False, font=dict(size=16)
            )
            return fig
        
        # Prepare data for heatmap
        components = list(set(b['component'] for b in bottlenecks))
        operations = [f"{b['component']}.{b['operation']}" for b in bottlenecks]
        execution_times = [b['mean_execution_time'] for b in bottlenecks]
        
        # Create matrix for heatmap
        matrix = np.array(execution_times).reshape(-1, 1)
        
        fig = go.Figure(data=go.Heatmap(
            z=matrix,
            y=operations,
            x=['Execution Time'],
            colorscale='Reds',
            showscale=True,
            colorbar=dict(title="Time (seconds)")
        ))
        
        fig.update_layout(
            title='Performance Bottlenecks Heatmap',
            xaxis_title='Metric',
            yaxis_title='Component.Operation',
            height=max(400, len(operations) * 20),
            template='plotly_white'
        )
        
        return fig
    
    def create_alert_timeline(self, days: int = 7) -> go.Figure:
        """Create timeline showing alert history."""
        end_time = datetime.now(timezone.utc)
        start_time = end_time - timedelta(days=days)
        
        # Get alert history (this would need to be implemented in AlertManager)
        alert_history = list(self.monitoring_system.alert_manager.alert_history)
        
        # Filter alerts within time range
        filtered_alerts = [
            alert for alert in alert_history
            if start_time <= alert.timestamp <= end_time
        ]
        
        if not filtered_alerts:
            fig = go.Figure()
            fig.add_annotation(
                text="No alerts in selected time range",
                xref="paper", yref="paper",
                x=0.5, y=0.5, xanchor='center', yanchor='middle',
                showarrow=False, font=dict(size=16)
            )
            return fig
        
        # Prepare data
        timestamps = [alert.timestamp for alert in filtered_alerts]
        components = [alert.component.value for alert in filtered_alerts]
        severities = [alert.severity.value for alert in filtered_alerts]
        messages = [alert.message for alert in filtered_alerts]
        
        # Color mapping for severities
        severity_colors = {
            'info': 'blue',
            'warning': 'yellow', 
            'error': 'orange',
            'critical': 'red',
            'emergency': 'purple'
        }
        
        colors = [severity_colors.get(sev, 'gray') for sev in severities]
        
        fig = go.Figure()
        
        fig.add_trace(go.Scatter(
            x=timestamps,
            y=components,
            mode='markers',
            marker=dict(
                color=colors,
                size=10,
                line=dict(width=1, color='black')
            ),
            text=messages,
            hovertemplate='<b>%{y}</b><br>%{x}<br>%{text}<extra></extra>',
            name='Alerts'
        ))
        
        fig.update_layout(
            title='Alert Timeline',
            xaxis_title='Time',
            yaxis_title='Component',
            template='plotly_white',
            height=400
        )
        
        return fig
    
    def save_dashboard_html(self, output_path: str):
        """Save complete monitoring dashboard as HTML."""
        from plotly.offline import plot
        import plotly.graph_objects as go
        from plotly.subplots import make_subplots
        
        # Create comprehensive dashboard
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>ULTRA Monitoring Dashboard</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .header {{ background-color: #f0f0f0; padding: 20px; margin-bottom: 20px; }}
                .section {{ margin-bottom: 30px; }}
                .plot-container {{ margin-bottom: 20px; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>ULTRA System Monitoring Dashboard</h1>
                <p>Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                <p>Health Score: {self.monitoring_system.get_system_health_score():.2f}/1.00</p>
            </div>
        """
        
        # Add system overview
        system_plot = self.create_system_overview_plot()
        system_html = plot(system_plot, output_type='div', include_plotlyjs=False)
        html_content += f'<div class="section"><h2>System Overview</h2><div class="plot-container">{system_html}</div></div>'
        
        # Add component plots for each component
        for component_type in self.monitoring_system.component_monitors.keys():
            monitor = self.monitoring_system.component_monitors[component_type]
            metric_names = list(monitor.metrics.keys())[:4]  # Top 4 metrics
            
            if metric_names:
                component_plot = self.create_component_metrics_plot(component_type, metric_names)
                component_html = plot(component_plot, output_type='div', include_plotlyjs=False)
                html_content += f'<div class="section"><h2>{component_type.value.replace("_", " ").title()}</h2><div class="plot-container">{component_html}</div></div>'
        
        # Add performance heatmap
        perf_plot = self.create_performance_heatmap()
        perf_html = plot(perf_plot, output_type='div', include_plotlyjs=False)
        html_content += f'<div class="section"><h2>Performance Analysis</h2><div class="plot-container">{perf_html}</div></div>'
        
        # Add alert timeline
        alert_plot = self.create_alert_timeline()
        alert_html = plot(alert_plot, output_type='div', include_plotlyjs=False)
        html_content += f'<div class="section"><h2>Alert History</h2><div class="plot-container">{alert_html}</div></div>'
        
        html_content += """
            <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
        </body>
        </html>
        """
        
        with open(output_path, 'w') as f:
            f.write(html_content)
        
        monitor_logger.info(f"Dashboard saved to: {output_path}")

# =============================================================================
# Integration with External Systems
# =============================================================================

class PrometheusExporter:
    """Export metrics to Prometheus format."""
    
    def __init__(self, monitoring_system: ULTRAMonitoringSystem, port: int = 8000):
        self.monitoring_system = monitoring_system
        self.port = port
        self.registry = CollectorRegistry()
        self._setup_prometheus_metrics()
        
    def _setup_prometheus_metrics(self):
        """Setup Prometheus metric objects."""
        self.cpu_usage = Gauge('ultra_cpu_usage_percent', 'CPU usage percentage', registry=self.registry)
        self.memory_usage = Gauge('ultra_memory_usage_percent', 'Memory usage percentage', registry=self.registry)
        self.health_score = Gauge('ultra_health_score', 'Overall system health score', registry=self.registry)
        self.active_alerts = Gauge('ultra_active_alerts_total', 'Number of active alerts', registry=self.registry)
        
        # Component metrics
        self.component_metrics = {}
        for component_type in ComponentType:
            metric_name = f'ultra_{component_type.value}_metric'
            self.component_metrics[component_type] = Gauge(
                metric_name, f'{component_type.value} metrics', 
                ['metric_name'], registry=self.registry
            )
    
    def start_server(self):
        """Start Prometheus metrics server."""
        start_http_server(self.port, registry=self.registry)
        monitor_logger.info(f"Prometheus metrics server started on port {self.port}")
    
    def update_metrics(self):
        """Update Prometheus metrics with current values."""
        # System metrics
        system_summary = self.monitoring_system.system_monitor.get_system_summary()
        if 'metrics' in system_summary:
            metrics = system_summary['metrics']
            
            if 'cpu_usage_percent' in metrics:
                self.cpu_usage.set(metrics['cpu_usage_percent'])
            if 'memory_usage_percent' in metrics:
                self.memory_usage.set(metrics['memory_usage_percent'])
        
        # Health score
        health_score = self.monitoring_system.get_system_health_score()
        self.health_score.set(health_score)
        
        # Alert count
        alert_summary = self.monitoring_system.alert_manager.get_alert_summary()
        self.active_alerts.set(alert_summary['active_alerts_count'])
        
        # Component metrics
        for component_type, monitor in self.monitoring_system.component_monitors.items():
            component_gauge = self.component_metrics[component_type]
            
            for metric_name in monitor.metrics.keys():
                recent_metrics = monitor.get_recent_metrics(metric_name, 1)
                if recent_metrics and isinstance(recent_metrics[0].value, (int, float)):
                    component_gauge.labels(metric_name=metric_name).set(recent_metrics[0].value)

# =============================================================================
# Configuration and Factory Functions
# =============================================================================

def create_monitoring_system(config: Optional[ULTRAConfig] = None,
                            monitoring_level: MonitoringLevel = MonitoringLevel.STANDARD,
                            storage_type: str = "sqlite",
                            enable_prometheus: bool = False,
                            prometheus_port: int = 8000) -> ULTRAMonitoringSystem:
    """Factory function to create a fully configured monitoring system."""
    
    # Create monitoring system
    monitoring_system = ULTRAMonitoringSystem(config)
    monitoring_system.set_monitoring_level(monitoring_level)
    
    # Setup Prometheus export if enabled
    if enable_prometheus:
        prometheus_exporter = PrometheusExporter(monitoring_system, prometheus_port)
        prometheus_exporter.start_server()
        
        # Add periodic update of Prometheus metrics
        def update_prometheus_metrics():
            while monitoring_system._monitoring_active:
                try:
                    prometheus_exporter.update_metrics()
                    time.sleep(15)  # Update every 15 seconds
                except Exception as e:
                    monitor_logger.error(f"Error updating Prometheus metrics: {e}")
                    time.sleep(60)  # Wait longer on error
        
        prometheus_thread = threading.Thread(target=update_prometheus_metrics, daemon=True)
        prometheus_thread.start()
    
    return monitoring_system

def setup_alert_notifications(monitoring_system: ULTRAMonitoringSystem,
                             email_config: Optional[Dict[str, Any]] = None,
                             slack_config: Optional[Dict[str, Any]] = None,
                             webhook_config: Optional[Dict[str, Any]] = None):
    """Setup various alert notification channels."""
    
    # Email notifications
    if email_config:
        def email_notification(alert: Alert):
            """Send email notification for alerts."""
            try:
                import smtplib
                from email.mime.text import MIMEText
                from email.mime.multipart import MIMEMultipart
                
                if alert.severity in [AlertSeverity.CRITICAL, AlertSeverity.EMERGENCY]:
                    msg = MIMEMultipart()
                    msg['From'] = email_config['from_email']
                    msg['To'] = ', '.join(email_config['to_emails'])
                    msg['Subject'] = f"ULTRA Alert: {alert.severity.value.upper()} - {alert.metric_name}"
                    
                    body = f"""
                    Alert Details:
                    - Component: {alert.component.value}
                    - Metric: {alert.metric_name}
                    - Value: {alert.value}
                    - Threshold: {alert.threshold}
                    - Message: {alert.message}
                    - Time: {alert.timestamp.strftime('%Y-%m-%d %H:%M:%S')}
                    """
                    
                    msg.attach(MIMEText(body, 'plain'))
                    
                    server = smtplib.SMTP(email_config['smtp_server'], email_config['smtp_port'])
                    server.starttls()
                    server.login(email_config['username'], email_config['password'])
                    text = msg.as_string()
                    server.sendmail(email_config['from_email'], email_config['to_emails'], text)
                    server.quit()
                    
            except Exception as e:
                monitor_logger.error(f"Failed to send email notification: {e}")
        
        monitoring_system.alert_manager.add_notification_channel(email_notification)
    
    # Slack notifications
    if slack_config:
        def slack_notification(alert: Alert):
            """Send Slack notification for alerts."""
            try:
                import requests
                
                color_map = {
                    AlertSeverity.INFO: 'good',
                    AlertSeverity.WARNING: 'warning',
                    AlertSeverity.ERROR: 'danger',
                    AlertSeverity.CRITICAL: 'danger',
                    AlertSeverity.EMERGENCY: 'danger'
                }
                
                payload = {
                    'channel': slack_config['channel'],
                    'username': 'ULTRA Monitor',
                    'attachments': [{
                        'color': color_map.get(alert.severity, 'warning'),
                        'title': f'ULTRA Alert: {alert.severity.value.upper()}',
                        'fields': [
                            {'title': 'Component', 'value': alert.component.value, 'short': True},
                            {'title': 'Metric', 'value': alert.metric_name, 'short': True},
                            {'title': 'Value', 'value': str(alert.value), 'short': True},
                            {'title': 'Threshold', 'value': str(alert.threshold), 'short': True},
                            {'title': 'Message', 'value': alert.message, 'short': False}
                        ],
                        'timestamp': int(alert.timestamp.timestamp())
                    }]
                }
                
                response = requests.post(slack_config['webhook_url'], json=payload)
                response.raise_for_status()
                
            except Exception as e:
                monitor_logger.error(f"Failed to send Slack notification: {e}")
        
        monitoring_system.alert_manager.add_notification_channel(slack_notification)
    
    # Webhook notifications
    if webhook_config:
        def webhook_notification(alert: Alert):
            """Send webhook notification for alerts."""
            try:
                import requests
                
                payload = alert.to_dict()
                response = requests.post(
                    webhook_config['url'],
                    json=payload,
                    headers=webhook_config.get('headers', {}),
                    timeout=webhook_config.get('timeout', 30)
                )
                response.raise_for_status()
                
            except Exception as e:
                monitor_logger.error(f"Failed to send webhook notification: {e}")
        
        monitoring_system.alert_manager.add_notification_channel(webhook_notification)

# =============================================================================
# Module Exports and Initialization
# =============================================================================

# Global monitoring system instance (optional)
_global_monitoring_system: Optional[ULTRAMonitoringSystem] = None

def get_global_monitoring_system() -> Optional[ULTRAMonitoringSystem]:
    """Get the global monitoring system instance."""
    return _global_monitoring_system

def set_global_monitoring_system(monitoring_system: ULTRAMonitoringSystem):
    """Set the global monitoring system instance."""
    global _global_monitoring_system
    _global_monitoring_system = monitoring_system

# Export all classes and functions
__all__ = [
    # Enums
    'MonitoringLevel', 'AlertSeverity', 'MetricType', 'ComponentType',
    
    # Data structures
    'MetricValue', 'Alert',
    
    # Metric calculators
    'NeuromorphicMetrics', 'AttentionMetrics', 'DiffusionMetrics', 'ConsciousnessMetrics',
    
    # Component monitors
    'ComponentMonitor', 'CoreNeuralMonitor', 'TransformerMonitor', 
    'DiffusionMonitor', 'ConsciousnessMonitor',
    
    # System monitors
    'SystemResourceMonitor', 'AlertManager', 'PerformanceProfiler',
    
    # Storage and visualization
    'MetricsStorage', 'MonitoringVisualizer',
    
    # Integration
    'PrometheusExporter',
    
    # Main system
    'ULTRAMonitoringSystem',
    
    # Factory functions
    'create_monitoring_system', 'setup_alert_notifications',
    
    # Global system access
    'get_global_monitoring_system', 'set_global_monitoring_system',
]

monitor_logger.info("ULTRA Monitoring System module initialized successfully")
monitor_logger.info(f"Available monitoring classes: {len(__all__)} total")
monitor_logger.info("Monitoring system ready for production use")