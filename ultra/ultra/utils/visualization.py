"""
Visualization utilities for the ULTRA system.

This module provides functions for visualizing various aspects of the ULTRA system,
such as neuron activity, network connectivity, etc.
"""

import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import networkx as nx
from matplotlib.animation import FuncAni<PERSON>

def plot_neuron_activity(activity_data, neuron_ids=None, time_range=None, title="Neuron Activity", 
                        figsize=(10, 6), save_path=None):
    """
    Plot neuron activity over time.
    
    Args:
        activity_data (ndarray): 2D array with shape (n_neurons, n_timesteps)
        neuron_ids (list, optional): IDs of neurons to plot. If None, plot all neurons.
        time_range (tuple, optional): (start, end) time range to plot. If None, plot all.
        title (str, optional): Title of the plot.
        figsize (tuple, optional): Figure size.
        save_path (str, optional): Path to save the figure. If None, display only.
    """
    plt.figure(figsize=figsize)
    
    if neuron_ids is None:
        neuron_ids = range(activity_data.shape[0])
    
    if time_range is None:
        time_range = (0, activity_data.shape[1])
    
    for i, neuron_id in enumerate(neuron_ids):
        plt.plot(range(time_range[0], time_range[1]), 
                activity_data[neuron_id, time_range[0]:time_range[1]], 
                label=f"Neuron {neuron_id}")
    
    plt.title(title)
    plt.xlabel("Time Step")
    plt.ylabel("Activity")
    
    if len(neuron_ids) <= 10:
        plt.legend()
    
    plt.grid(True)
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
    
    plt.show()

def plot_spike_raster(spike_data, neuron_ids=None, time_range=None, title="Spike Raster Plot", 
                      figsize=(12, 8), marker='|', markersize=2, save_path=None):
    """
    Create a raster plot of neuron spikes.
    
    Args:
        spike_data (ndarray): 2D binary array with shape (n_neurons, n_timesteps)
        neuron_ids (list, optional): IDs of neurons to plot. If None, plot all neurons.
        time_range (tuple, optional): (start, end) time range to plot. If None, plot all.
        title (str, optional): Title of the plot.
        figsize (tuple, optional): Figure size.
        marker (str, optional): Marker style for spikes.
        markersize (int, optional): Size of markers.
        save_path (str, optional): Path to save the figure. If None, display only.
    """
    plt.figure(figsize=figsize)
    
    if neuron_ids is None:
        neuron_ids = range(spike_data.shape[0])
    
    if time_range is None:
        time_range = (0, spike_data.shape[1])
    
    for i, neuron_id in enumerate(neuron_ids):
        spike_times = np.where(spike_data[neuron_id, time_range[0]:time_range[1]])[0] + time_range[0]
        plt.plot(spike_times, np.ones_like(spike_times) * i, marker, markersize=markersize, 
                color='black', label=f"Neuron {neuron_id}" if i == 0 else "")
    
    plt.title(title)
    plt.xlabel("Time Step")
    plt.ylabel("Neuron ID")
    plt.yticks(range(len(neuron_ids)), neuron_ids)
    plt.ylim(-1, len(neuron_ids))
    plt.xlim(time_range)
    
    plt.grid(True, axis='x', linestyle='--', alpha=0.7)
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
    
    plt.show()

def plot_connectivity(connectivity_matrix, neuron_types=None, title="Network Connectivity", 
                     figsize=(10, 8), cmap='viridis', save_path=None):
    """
    Plot connectivity matrix of the neural network.
    
    Args:
        connectivity_matrix (ndarray): 2D array with shape (n_neurons, n_neurons)
        neuron_types (list, optional): Type of each neuron (to color-code).
        title (str, optional): Title of the plot.
        figsize (tuple, optional): Figure size.
        cmap (str, optional): Colormap for the plot.
        save_path (str, optional): Path to save the figure. If None, display only.
    """
    plt.figure(figsize=figsize)
    
    plt.imshow(connectivity_matrix, cmap=cmap, interpolation='nearest')
    plt.colorbar(label="Connection Strength")
    
    plt.title(title)
    plt.xlabel("Target Neuron")
    plt.ylabel("Source Neuron")
    
    # Add type boundaries if neuron types are provided
    if neuron_types is not None:
        neuron_type_ids = np.unique(neuron_types)
        boundaries = [0]
        
        for type_id in neuron_type_ids:
            boundaries.append(boundaries[-1] + np.sum(neuron_types == type_id))
        
        for boundary in boundaries[1:-1]:
            plt.axhline(y=boundary - 0.5, color='red', linestyle='-')
            plt.axvline(x=boundary - 0.5, color='red', linestyle='-')
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
    
    plt.show()

def plot_3d_neurons(positions, activities=None, neuron_types=None, title="3D Neuron Positions", 
                   figsize=(12, 10), marker_size=50, save_path=None):
    """
    Plot 3D positions of neurons in the neuromorphic core.
    
    Args:
        positions (ndarray): Array with shape (n_neurons, 3) containing x, y, z coordinates.
        activities (ndarray, optional): Array with shape (n_neurons,) for color coding by activity.
        neuron_types (list, optional): Type of each neuron (to use different markers).
        title (str, optional): Title of the plot.
        figsize (tuple, optional): Figure size.
        marker_size (int, optional): Size of markers.
        save_path (str, optional): Path to save the figure. If None, display only.
    """
    fig = plt.figure(figsize=figsize)
    ax = fig.add_subplot(111, projection='3d')
    
    if activities is not None:
        scatter = ax.scatter(positions[:, 0], positions[:, 1], positions[:, 2], 
                           c=activities, cmap='plasma', s=marker_size)
        plt.colorbar(scatter, label="Activity")
    elif neuron_types is not None:
        unique_types = np.unique(neuron_types)
        colors = plt.cm.tab10(np.linspace(0, 1, len(unique_types)))
        
        for i, t in enumerate(unique_types):
            mask = neuron_types == t
            ax.scatter(positions[mask, 0], positions[mask, 1], positions[mask, 2], 
                     color=colors[i], label=f"Type {t}", s=marker_size)
        
        plt.legend()
    else:
        ax.scatter(positions[:, 0], positions[:, 1], positions[:, 2], s=marker_size)
    
    ax.set_xlabel("X")
    ax.set_ylabel("Y")
    ax.set_zlabel("Z")
    ax.set_title(title)
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
    
    plt.show()

def plot_graph_network(adjacency_matrix, node_positions=None, node_types=None, 
                      title="Network Graph", figsize=(12, 10), save_path=None):
    """
    Plot the neural network as a graph.
    
    Args:
        adjacency_matrix (ndarray): 2D binary array with shape (n_neurons, n_neurons)
        node_positions (dict, optional): Dictionary mapping node index to (x,y) positions.
        node_types (list, optional): Type of each neuron (to color-code).
        title (str, optional): Title of the plot.
        figsize (tuple, optional): Figure size.
        save_path (str, optional): Path to save the figure. If None, display only.
    """
    plt.figure(figsize=figsize)
    
    # Create graph from adjacency matrix
    G = nx.from_numpy_array(adjacency_matrix, create_using=nx.DiGraph)
    
    # Set node colors based on types
    if node_types is not None:
        unique_types = np.unique(node_types)
        colors = plt.cm.tab10(np.linspace(0, 1, len(unique_types)))
        node_colors = [colors[node_types[i] % len(colors)] for i in range(len(node_types))]
    else:
        node_colors = 'skyblue'
    
    # Draw network
    if node_positions is None:
        node_positions = nx.spring_layout(G)
    
    nx.draw_networkx_nodes(G, node_positions, node_color=node_colors, 
                          node_size=100, alpha=0.8)
    
    # Draw edges with weights affecting transparency
    edges = G.edges(data=True)
    weights = [G[u][v]['weight'] for u, v in G.edges()]
    
    if weights:
        max_weight = max(weights)
        edge_colors = [(0.1, 0.1, 0.1, min(w / max_weight + 0.2, 1.0)) for w in weights]
    else:
        edge_colors = 'gray'
    
    nx.draw_networkx_edges(G, node_positions, width=1.0, alpha=0.5, 
                          edge_color=edge_colors, arrows=True, arrowsize=10)
    
    plt.title(title)
    plt.axis('off')
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
    
    plt.show()

def animate_neural_activity(activity_data, positions, time_steps=None, interval=100, 
                          title="Neural Activity Animation", figsize=(12, 10), 
                          save_path=None):
    """
    Create an animation of neural activity in 3D space.
    
    Args:
        activity_data (ndarray): 2D array with shape (n_neurons, n_timesteps)
        positions (ndarray): Array with shape (n_neurons, 3) containing x, y, z coordinates.
        time_steps (list, optional): Time steps to include in animation. If None, use all.
        interval (int, optional): Interval between frames in milliseconds.
        title (str, optional): Title of the animation.
        figsize (tuple, optional): Figure size.
        save_path (str, optional): Path to save the animation (as mp4). If None, display only.
    
    Returns:
        FuncAnimation: Animation object
    """
    fig = plt.figure(figsize=figsize)
    ax = fig.add_subplot(111, projection='3d')
    
    if time_steps is None:
        time_steps = range(activity_data.shape[1])
    
    scatter = ax.scatter(positions[:, 0], positions[:, 1], positions[:, 2], 
                       c=activity_data[:, 0], cmap='plasma', s=50, vmin=0, 
                       vmax=np.max(activity_data))
    
    time_text = ax.text2D(0.05, 0.95, "", transform=ax.transAxes)
    fig.colorbar(scatter, ax=ax, label="Activity")
    
    ax.set_xlabel("X")
    ax.set_ylabel("Y")
    ax.set_zlabel("Z")
    ax.set_title(title)
    
    def update(frame):
        scatter.set_array(activity_data[:, frame])
        time_text.set_text(f"Time step: {time_steps[frame]}")
        return scatter, time_text
    
    ani = FuncAnimation(fig, update, frames=len(time_steps), interval=interval, blit=True)
    
    if save_path:
        ani.save(save_path, writer='ffmpeg', dpi=200)
    
    plt.close()
    return ani

def plot_neuromodulator_levels(neuromodulator_data, time_range=None, 
                             title="Neuromodulator Levels", figsize=(10, 6), save_path=None):
    """
    Plot neuromodulator levels over time.
    
    Args:
        neuromodulator_data (dict): Dictionary mapping neuromodulator names to time series data.
        time_range (tuple, optional): (start, end) time range to plot. If None, plot all.
        title (str, optional): Title of the plot.
        figsize (tuple, optional): Figure size.
        save_path (str, optional): Path to save the figure. If None, display only.
    """
    plt.figure(figsize=figsize)
    
    colors = plt.cm.tab10(np.linspace(0, 1, len(neuromodulator_data)))
    
    for i, (name, data) in enumerate(neuromodulator_data.items()):
        if time_range is not None:
            plt.plot(range(time_range[0], time_range[1]), 
                   data[time_range[0]:time_range[1]], 
                   label=name, color=colors[i])
        else:
            plt.plot(data, label=name, color=colors[i])
    
    plt.title(title)
    plt.xlabel("Time Step")
    plt.ylabel("Level")
    plt.legend()
    plt.grid(True)
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
    
    plt.show()

def plot_oscillator_activity(oscillator_data, time_range=None, 
                           title="Neural Oscillations", figsize=(12, 8), save_path=None):
    """
    Plot neural oscillator activity over time.
    
    Args:
        oscillator_data (dict): Dictionary mapping oscillator names to time series data.
        time_range (tuple, optional): (start, end) time range to plot. If None, plot all.
        title (str, optional): Title of the plot.
        figsize (tuple, optional): Figure size.
        save_path (str, optional): Path to save the figure. If None, display only.
    """
    plt.figure(figsize=figsize)
    
    n_oscillators = len(oscillator_data)
    colors = plt.cm.tab10(np.linspace(0, 1, n_oscillators))
    
    for i, (name, data) in enumerate(oscillator_data.items()):
        if time_range is not None:
            t = range(time_range[0], time_range[1])
            d = data[time_range[0]:time_range[1]]
        else:
            t = range(len(data))
            d = data
        
        plt.subplot(n_oscillators, 1, i+1)
        plt.plot(t, d, color=colors[i])
        plt.ylabel(name)
        plt.grid(True)
        
        if i == 0:
            plt.title(title)
        
        if i == n_oscillators - 1:
            plt.xlabel("Time Step")
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
    
    plt.show()