#!/usr/bin/env python3
"""
ULTRA Advanced Visualization System
===================================

Comprehensive visualization framework for the ULTRA (Ultimate Learning & 
Thought Reasoning Architecture) system. This module provides specialized 
visualization capabilities for all 8 core ULTRA components with mathematical 
precision, real-time monitoring, and interactive analysis tools.

The visualization system implements:
- Neuromorphic network 3D visualization with spike dynamics
- Attention mechanism analysis and pattern visualization
- Diffusion process trajectories and thought space navigation
- Consciousness metrics (Φ calculation) and emergence visualization
- Meta-cognitive reasoning path exploration and graph networks
- Real-time performance monitoring dashboards
- Mathematical precision tracking and numerical analysis
- Interactive parameter exploration and sensitivity analysis
- Publication-quality scientific plots and animations
- Integration with ULTRA logging and configuration systems

Mathematical Foundation:
- Based on ULTRA equations: τ(dV_i(t)/dt) = -V_i(t) + Σ_j w_ij·S_j(t) + I_i^ext(t)
- STDP visualization: Δw_ij = A_+exp(-Δt/τ_+) if Δt > 0
- Diffusion processes: q(z_t|z_0) = N(z_t; √ᾱ_t z_0, (1-ᾱ_t)I)
- IIT consciousness: Φ(X) = min_P∈P I(X_1;X_2|X_P)/min{H(X_1|X_P),H(X_2|X_P)}
- Attention mechanisms: Attention(Q,K,V) = softmax(QK^T/√d_k)V

Author: ULTRA Development Team
License: MIT
Version: 1.0.0
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.animation as animation
from matplotlib.patches import Circle, Rectangle, FancyBboxPatch
from matplotlib.collections import LineCollection, PolyCollection
import matplotlib.patches as mpatches
import seaborn as sns
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import plotly.figure_factory as ff
import networkx as nx
from scipy import stats, signal, interpolate, spatial
from scipy.ndimage import gaussian_filter, gaussian_filter1d
from sklearn.manifold import TSNE
from sklearn.decomposition import PCA
from sklearn.cluster import DBSCAN, KMeans
from sklearn.preprocessing import StandardScaler, MinMaxScaler
import colorcet as cc
import warnings
import threading
import queue
import time
import json
import logging
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Union, Any, Callable, Iterator
from dataclasses import dataclass, field
from enum import Enum, auto
from abc import ABC, abstractmethod
from collections import defaultdict, deque
import contextlib
import io
import base64
from PIL import Image, ImageDraw, ImageFont


# 3D visualization libraries
try:
    from mayavi import mlab
    from mayavi.core.ui.api import MayaviScene, MlabSceneModel, SceneEditor
    MAYAVI_AVAILABLE = True
except ImportError:
    MAYAVI_AVAILABLE = False
    mlab = None

try:
    import vtk
    from vtk.util import numpy_support
    VTK_AVAILABLE = True
except ImportError:
    VTK_AVAILABLE = False
    vtk = None

# Optional imports with fallbacks
try:
    import plotly.graph_objects as go
    import plotly.express as px
    from plotly.subplots import make_subplots
    import plotly.figure_factory as ff
    PLOTLY_AVAILABLE = True
except ImportError:
    PLOTLY_AVAILABLE = False
    go = None
    px = None

try:
    import colorcet as cc
    COLORCET_AVAILABLE = True
except ImportError:
    COLORCET_AVAILABLE = False
    cc = None
try:
    from PIL import Image, ImageDraw, ImageFont
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False

try:
    import cv2
    CV2_AVAILABLE = True
except ImportError:
    CV2_AVAILABLE = False

try:
    import umap
    UMAP_AVAILABLE = True
except ImportError:
    UMAP_AVAILABLE = False

# Interactive visualization libraries
try:
    from bokeh.plotting import figure, show, save
    from bokeh.models import ColumnDataSource, HoverTool, ColorBar
    from bokeh.layouts import gridplot, column, row
    from bokeh.io import curdoc
    from bokeh.server.server import Server
    BOKEH_AVAILABLE = True
except ImportError:
    BOKEH_AVAILABLE = False

# Suppress warnings for cleaner output
warnings.filterwarnings('ignore', category=RuntimeWarning)
warnings.filterwarnings('ignore', category=UserWarning)
warnings.filterwarnings('ignore', category=FutureWarning)

# Configure matplotlib for high-quality output
plt.rcParams['figure.dpi'] = 150
plt.rcParams['savefig.dpi'] = 300
plt.rcParams['font.size'] = 10
plt.rcParams['axes.linewidth'] = 0.8
plt.rcParams['grid.alpha'] = 0.3

# Configure logging
viz_logger = logging.getLogger('ultra.visualization')

# =============================================================================
# Visualization Configuration and Constants
# =============================================================================

class VisualizationMode(Enum):
    """Visualization rendering modes."""
    STATIC = "static"           # Static matplotlib plots
    INTERACTIVE = "interactive" # Interactive plotly plots
    REAL_TIME = "real_time"    # Real-time updating plots
    ANIMATION = "animation"     # Animated sequences
    VR_3D = "vr_3d"           # VR/3D immersive visualization

class ColorScheme(Enum):
    """Color schemes for different visualization types."""
    NEUROMORPHIC = "viridis"    # Neural activity
    ATTENTION = "plasma"        # Attention weights
    DIFFUSION = "coolwarm"      # Diffusion processes
    CONSCIOUSNESS = "inferno"   # Consciousness metrics
    PERFORMANCE = "RdYlBu_r"   # Performance monitoring
    SCIENTIFIC = "tab10"       # Scientific plots
    CUSTOM_ULTRA = "custom"     # ULTRA-specific colors

class PlotType(Enum):
    """Types of plots available."""
    LINE_PLOT = "line"
    SCATTER_PLOT = "scatter"
    HEATMAP = "heatmap"
    SURFACE_3D = "surface_3d"
    NETWORK_GRAPH = "network"
    VIOLIN_PLOT = "violin"
    BOX_PLOT = "box"
    HISTOGRAM = "histogram"
    CONTOUR_PLOT = "contour"
    STREAMPLOT = "stream"
    RASTER_PLOT = "raster"
    PHASE_PLOT = "phase"

# Mathematical and visual constants
ULTRA_VIZ_CONSTANTS = {
    # Neural visualization
    'NEURON_SIZE_RANGE': (2, 20),       # Min/max neuron sizes
    'SYNAPSE_WIDTH_RANGE': (0.1, 3.0),  # Min/max synapse widths
    'SPIKE_DURATION_MS': 2.0,           # Spike visualization duration
    'MEMBRANE_POTENTIAL_RANGE': (-100, 50),  # mV range for coloring
    
    # Network layout
    'NETWORK_LAYOUT_ITERATIONS': 100,   # Force-directed layout iterations
    'EDGE_BUNDLING_STRENGTH': 0.8,     # Edge bundling parameter
    'NODE_REPULSION_STRENGTH': 1000,   # Node repulsion force
    
    # Attention visualization
    'ATTENTION_ALPHA_MIN': 0.1,        # Minimum attention opacity
    'ATTENTION_ALPHA_MAX': 1.0,        # Maximum attention opacity
    'ATTENTION_THRESHOLD': 0.01,       # Minimum attention to display
    
    # Diffusion visualization
    'DIFFUSION_TRAIL_LENGTH': 50,      # Length of trajectory trails
    'NOISE_VISUALIZATION_SCALE': 0.1,  # Scale for noise visualization
    'CONVERGENCE_WINDOW': 20,          # Window for convergence analysis
    
    # Consciousness visualization
    'PHI_COLOR_RANGE': (0, 5.0),      # Φ value range for coloring
    'INTEGRATION_THRESHOLD': 0.1,      # Minimum integration to display
    'WORKSPACE_COMPETITION_SCALE': 10, # Scale for competition visualization
    
    # Performance visualization
    'PERFORMANCE_HISTORY_LENGTH': 1000, # Number of performance points
    'ALERT_THRESHOLD_COLOR': '#FF6B6B', # Color for alert thresholds
    'NORMAL_RANGE_COLOR': '#4ECDC4',    # Color for normal ranges
    
    # Animation parameters
    'ANIMATION_FPS': 30,               # Frames per second
    'ANIMATION_INTERVAL_MS': 33,       # Milliseconds between frames
    'TRAIL_FADE_RATE': 0.95,          # Trail fading rate per frame
}

# Custom ULTRA color palettes
ULTRA_COLOR_PALETTES = {
    'neural_activity': ['#0d47a1', '#1976d2', '#42a5f5', '#90caf9', '#e3f2fd'],
    'attention_flow': ['#4a148c', '#7b1fa2', '#ab47bc', '#ce93d8', '#f3e5f5'],
    'diffusion_process': ['#b71c1c', '#d32f2f', '#f44336', '#ef5350', '#ffebee'],
    'consciousness_levels': ['#1b5e20', '#388e3c', '#66bb6a', '#a5d6a7', '#e8f5e9'],
    'system_health': ['#ff5722', '#ff9800', '#ffc107', '#8bc34a', '#4caf50'],
    'ultra_gradient': ['#667eea', '#764ba2', '#f093fb', '#f5576c', '#4facfe']
}

# =============================================================================
# Base Visualization Classes
# =============================================================================

@dataclass
class VisualizationConfig:
    """Configuration for visualization parameters."""
    mode: VisualizationMode = VisualizationMode.STATIC
    color_scheme: ColorScheme = ColorScheme.SCIENTIFIC
    figure_size: Tuple[int, int] = (12, 8)
    dpi: int = 150
    save_format: str = 'png'
    quality: int = 95
    interactive: bool = False
    animation_enabled: bool = False
    real_time_update: bool = False
    update_interval_ms: int = 100
    max_data_points: int = 1000
    enable_3d: bool = True
    enable_gpu_acceleration: bool = False
    custom_style: Dict[str, Any] = field(default_factory=dict)

class BaseVisualizer(ABC):
    """Abstract base class for all ULTRA visualizers."""
    
    def __init__(self, config: VisualizationConfig, logger: Optional[logging.Logger] = None):
        self.config = config
        self.logger = logger or viz_logger
        self.figure_cache = {}
        self.animation_cache = {}
        self.data_buffer = deque(maxlen=config.max_data_points)
        self.update_lock = threading.Lock()
        self.last_update_time = time.time()
        
        # Initialize color palette
        self.colors = self._setup_color_palette()
        
        # Setup matplotlib style
        self._setup_matplotlib_style()
    
    def _setup_color_palette(self) -> Dict[str, Any]:
        """Setup color palette based on configuration."""
        if self.config.color_scheme == ColorScheme.CUSTOM_ULTRA:
            return ULTRA_COLOR_PALETTES
        else:
            return {
                'primary': plt.cm.get_cmap(self.config.color_scheme.value),
                'discrete': sns.color_palette(self.config.color_scheme.value, 10)
            }
    
    def _setup_matplotlib_style(self) -> None:
        """Setup matplotlib style parameters."""
        style_params = {
            'figure.figsize': self.config.figure_size,
            'figure.dpi': self.config.dpi,
            'savefig.dpi': 300,
            'font.size': 10,
            'axes.linewidth': 0.8,
            'grid.alpha': 0.3,
            'lines.linewidth': 1.5,
            'patch.linewidth': 0.5,
            'xtick.major.size': 4,
            'ytick.major.size': 4,
            'legend.frameon': True,
            'legend.framealpha': 0.8
        }
        
        # Apply custom style parameters
        style_params.update(self.config.custom_style)
        plt.rcParams.update(style_params)
    
    @abstractmethod
    def create_visualization(self, data: Dict[str, Any], **kwargs) -> Union[plt.Figure, go.Figure]:
        """Create the main visualization."""
        pass
    
    @abstractmethod
    def update_visualization(self, new_data: Dict[str, Any]) -> None:
        """Update existing visualization with new data."""
        pass
    
    def save_visualization(self, figure: Union[plt.Figure, go.Figure], 
                          filename: str, **kwargs) -> str:
        """Save visualization to file."""
        filepath = Path(filename)
        filepath.parent.mkdir(parents=True, exist_ok=True)
        
        if isinstance(figure, plt.Figure):
            figure.savefig(
                filepath,
                format=self.config.save_format,
                dpi=self.config.dpi,
                bbox_inches='tight',
                facecolor='white',
                edgecolor='none',
                **kwargs
            )
        elif hasattr(figure, 'write_image'):  # Plotly figure
            figure.write_image(
                str(filepath),
                format=self.config.save_format,
                width=self.config.figure_size[0] * self.config.dpi // 72,
                height=self.config.figure_size[1] * self.config.dpi // 72
            )
        
        self.logger.info(f"Visualization saved to {filepath}")
        return str(filepath)
    
    def create_animation(self, data_sequence: List[Dict[str, Any]], 
                        duration_seconds: float = 10.0) -> animation.FuncAnimation:
        """Create animated visualization."""
        fig = plt.figure(figsize=self.config.figure_size)
        
        def animate(frame):
            plt.clf()
            frame_data = data_sequence[frame % len(data_sequence)]
            self.create_visualization(frame_data)
            plt.tight_layout()
        
        frames = len(data_sequence)
        interval = (duration_seconds * 1000) / frames
        
        anim = animation.FuncAnimation(
            fig, animate, frames=frames, interval=interval, 
            blit=False, repeat=True
        )
        
        return anim
    
    def save_animation(self, anim: animation.FuncAnimation, filename: str,
                      writer: str = 'pillow', fps: int = 30) -> str:
        """Save animation to file."""
        filepath = Path(filename)
        filepath.parent.mkdir(parents=True, exist_ok=True)
        
        anim.save(str(filepath), writer=writer, fps=fps, 
                 savefig_kwargs={'facecolor': 'white', 'edgecolor': 'none'})
        
        self.logger.info(f"Animation saved to {filepath}")
        return str(filepath)

# =============================================================================
# Neuromorphic Visualization Components
# =============================================================================

class NeuromorphicNetworkVisualizer(BaseVisualizer):
    """Visualizer for neuromorphic neural networks with 3D spatial organization."""
    
    def __init__(self, config: VisualizationConfig, logger: Optional[logging.Logger] = None):
        super().__init__(config, logger)
        self.network_layout = None
        self.neuron_positions = None
        self.connection_matrix = None
        self.spike_history = defaultdict(list)
        self.weight_history = defaultdict(list)
        
    def create_visualization(self, data: Dict[str, Any], **kwargs) -> plt.Figure:
        """Create comprehensive neuromorphic network visualization."""
        network_data = data.get('network_structure', {})
        spike_data = data.get('spike_events', [])
        weight_data = data.get('weight_changes', {})
        
        if self.config.enable_3d and MAYAVI_AVAILABLE:
            return self._create_3d_network_visualization(network_data, spike_data, weight_data)
        else:
            return self._create_2d_network_visualization(network_data, spike_data, weight_data)
    
    def _create_3d_network_visualization(self, network_data: Dict[str, Any],
                                       spike_data: List[Dict[str, Any]],
                                       weight_data: Dict[str, Any]) -> plt.Figure:
        """Create 3D neuromorphic network visualization."""
        if not MAYAVI_AVAILABLE:
            self.logger.warning("Mayavi not available, falling back to 2D visualization")
            return self._create_2d_network_visualization(network_data, spike_data, weight_data)
        
        # Clear previous scene
        mlab.clf()
        
        # Extract network structure
        positions = network_data.get('positions', np.random.rand(100, 3))
        connections = network_data.get('adjacency_matrix', np.random.rand(100, 100) > 0.9)
        neuron_types = network_data.get('neuron_types', np.zeros(len(positions)))
        membrane_potentials = network_data.get('membrane_potentials', np.random.randn(len(positions)))
        
        # Normalize membrane potentials for coloring
        v_min, v_max = ULTRA_VIZ_CONSTANTS['MEMBRANE_POTENTIAL_RANGE']
        membrane_potentials_norm = np.clip(
            (membrane_potentials - v_min) / (v_max - v_min), 0, 1
        )
        
        # Create 3D scatter plot for neurons
        neuron_plot = mlab.points3d(
            positions[:, 0], positions[:, 1], positions[:, 2],
            membrane_potentials_norm,
            scale_factor=0.05,
            colormap='plasma',
            resolution=20
        )
        
        # Add connections as lines
        if np.any(connections):
            connection_lines = []
            connection_weights = []
            
            for i in range(len(positions)):
                for j in range(len(positions)):
                    if connections[i, j]:
                        start = positions[i]
                        end = positions[j]
                        connection_lines.extend([start, end])
                        weight = weight_data.get(f'{i}_{j}', 0.1)
                        connection_weights.append(weight)
            
            if connection_lines:
                connection_lines = np.array(connection_lines).reshape(-1, 2, 3)
                
                # Create tubes for connections
                for line, weight in zip(connection_lines, connection_weights):
                    mlab.plot3d(
                        line[:, 0], line[:, 1], line[:, 2],
                        tube_radius=weight * 0.005,
                        color=(0.7, 0.7, 0.7),
                        opacity=0.3
                    )
        
        # Add spike events as glowing spheres
        current_time = time.time()
        for spike in spike_data:
            if current_time - spike.get('timestamp', 0) < ULTRA_VIZ_CONSTANTS['SPIKE_DURATION_MS'] / 1000:
                neuron_id = spike.get('neuron_id', 0)
                if neuron_id < len(positions):
                    pos = positions[neuron_id]
                    mlab.points3d(
                        [pos[0]], [pos[1]], [pos[2]],
                        scale_factor=0.1,
                        color=(1, 1, 0),  # Yellow for spikes
                        opacity=0.8
                    )
        
        # Add colorbar and labels
        mlab.colorbar(neuron_plot, title='Membrane Potential (normalized)', orientation='vertical')
        mlab.title('ULTRA Neuromorphic Network - 3D View')
        
        # Set viewing angle
        mlab.view(azimuth=45, elevation=60, distance='auto')
        
        # Convert to matplotlib figure for consistency
        fig = plt.figure(figsize=self.config.figure_size)
        
        # Take screenshot of Mayavi scene
        img = mlab.screenshot(antialiased=True)
        plt.imshow(img)
        plt.axis('off')
        plt.title('ULTRA Neuromorphic Network - 3D Visualization')
        
        return fig
    
    def _create_2d_network_visualization(self, network_data: Dict[str, Any],
                                       spike_data: List[Dict[str, Any]],
                                       weight_data: Dict[str, Any]) -> plt.Figure:
        """Create comprehensive 2D neuromorphic network visualization."""
        fig = plt.figure(figsize=(20, 16))
        
        # Create subplot layout
        gs = fig.add_gridspec(3, 3, height_ratios=[2, 1, 1], width_ratios=[2, 1, 1])
        
        # Main network plot
        ax_main = fig.add_subplot(gs[0, :2])
        self._plot_network_topology(ax_main, network_data, spike_data, weight_data)
        
        # Spike raster plot
        ax_raster = fig.add_subplot(gs[1, :2])
        self._plot_spike_raster(ax_raster, spike_data)
        
        # Membrane potential distribution
        ax_membrane = fig.add_subplot(gs[2, :2])
        self._plot_membrane_potential_distribution(ax_membrane, network_data)
        
        # Weight evolution
        ax_weights = fig.add_subplot(gs[0, 2])
        self._plot_weight_evolution(ax_weights, weight_data)
        
        # STDP curve
        ax_stdp = fig.add_subplot(gs[1, 2])
        self._plot_stdp_curve(ax_stdp)
        
        # Network statistics
        ax_stats = fig.add_subplot(gs[2, 2])
        self._plot_network_statistics(ax_stats, network_data, spike_data)
        
        plt.tight_layout()
        return fig
    
    def _plot_network_topology(self, ax: plt.Axes, network_data: Dict[str, Any],
                             spike_data: List[Dict[str, Any]],
                             weight_data: Dict[str, Any]) -> None:
        """Plot network topology with neurons and connections."""
        positions = network_data.get('positions', None)
        adjacency_matrix = network_data.get('adjacency_matrix', None)
        neuron_types = network_data.get('neuron_types', None)
        membrane_potentials = network_data.get('membrane_potentials', None)
        
        if positions is None or len(positions) == 0:
            ax.text(0.5, 0.5, 'No network data available', 
                   ha='center', va='center', transform=ax.transAxes)
            return
        
        # Create NetworkX graph for layout if needed
        if self.network_layout is None or positions.shape != self.neuron_positions.shape:
            self.neuron_positions = positions
            if positions.shape[1] == 3:
                # Project 3D to 2D for plotting
                self.neuron_positions = positions[:, :2]
            elif positions.shape[1] == 2:
                self.neuron_positions = positions
            else:
                # Generate 2D layout
                G = nx.Graph()
                G.add_nodes_from(range(len(positions)))
                if adjacency_matrix is not None:
                    edges = np.where(adjacency_matrix > 0)
                    G.add_edges_from(zip(edges[0], edges[1]))
                self.neuron_positions = np.array(list(nx.spring_layout(G, 
                    iterations=ULTRA_VIZ_CONSTANTS['NETWORK_LAYOUT_ITERATIONS']).values()))
        
        # Plot connections
        if adjacency_matrix is not None:
            for i in range(len(self.neuron_positions)):
                for j in range(len(self.neuron_positions)):
                    if adjacency_matrix[i, j] > 0:
                        x_coords = [self.neuron_positions[i, 0], self.neuron_positions[j, 0]]
                        y_coords = [self.neuron_positions[i, 1], self.neuron_positions[j, 1]]
                        
                        # Get connection weight for line width
                        weight = weight_data.get(f'{i}_{j}', adjacency_matrix[i, j])
                        line_width = np.clip(
                            weight * 3, 
                            ULTRA_VIZ_CONSTANTS['SYNAPSE_WIDTH_RANGE'][0],
                            ULTRA_VIZ_CONSTANTS['SYNAPSE_WIDTH_RANGE'][1]
                        )
                        
                        ax.plot(x_coords, y_coords, 'gray', 
                               alpha=0.3, linewidth=line_width, zorder=1)
        
        # Color neurons by membrane potential
        if membrane_potentials is not None:
            v_min, v_max = ULTRA_VIZ_CONSTANTS['MEMBRANE_POTENTIAL_RANGE']
            norm = plt.Normalize(v_min, v_max)
            colors = plt.cm.plasma(norm(membrane_potentials))
        else:
            colors = 'blue'
        
        # Size neurons by type
        if neuron_types is not None:
            sizes = []
            for ntype in neuron_types:
                if ntype == 0:  # Excitatory
                    sizes.append(50)
                elif ntype == 1:  # Inhibitory
                    sizes.append(30)
                elif ntype == 2:  # Adaptive
                    sizes.append(40)
                else:  # Neuromodulatory
                    sizes.append(60)
        else:
            sizes = 50
        
        # Plot neurons
        scatter = ax.scatter(
            self.neuron_positions[:, 0], self.neuron_positions[:, 1],
            c=colors, s=sizes, alpha=0.8, edgecolors='black', linewidth=0.5, zorder=2
        )
        
        # Highlight recent spikes
        current_time = time.time()
        for spike in spike_data:
            if current_time - spike.get('timestamp', 0) < ULTRA_VIZ_CONSTANTS['SPIKE_DURATION_MS'] / 1000:
                neuron_id = spike.get('neuron_id', 0)
                if neuron_id < len(self.neuron_positions):
                    pos = self.neuron_positions[neuron_id]
                    circle = Circle((pos[0], pos[1]), 0.05, 
                                  color='yellow', alpha=0.8, zorder=3)
                    ax.add_patch(circle)
        
        # Add colorbar for membrane potentials
        if membrane_potentials is not None:
            cbar = plt.colorbar(scatter, ax=ax)
            cbar.set_label('Membrane Potential (mV)')
        
        ax.set_title('Neuromorphic Network Topology')
        ax.set_xlabel('X Position')
        ax.set_ylabel('Y Position')
        ax.grid(True, alpha=0.3)
    
    def _plot_spike_raster(self, ax: plt.Axes, spike_data: List[Dict[str, Any]]) -> None:
        """Plot spike raster showing neuron firing patterns."""
        if not spike_data:
            ax.text(0.5, 0.5, 'No spike data available', 
                   ha='center', va='center', transform=ax.transAxes)
            return
        
        # Extract spike times and neuron IDs
        spike_times = []
        neuron_ids = []
        
        current_time = time.time()
        time_window = 1.0  # 1 second window
        
        for spike in spike_data:
            spike_time = spike.get('timestamp', current_time)
            if current_time - spike_time <= time_window:
                relative_time = (spike_time - (current_time - time_window)) * 1000  # Convert to ms
                spike_times.append(relative_time)
                neuron_ids.append(spike.get('neuron_id', 0))
        
        if spike_times:
            # Create raster plot
            ax.scatter(spike_times, neuron_ids, c='red', s=1, alpha=0.7)
            
            # Add firing rate analysis
            if len(spike_times) > 1:
                # Calculate instantaneous firing rate
                time_bins = np.linspace(0, time_window * 1000, 50)
                firing_rate, _ = np.histogram(spike_times, bins=time_bins)
                firing_rate = firing_rate / (time_bins[1] - time_bins[0]) * 1000  # Hz
                
                # Plot firing rate on secondary y-axis
                ax2 = ax.twinx()
                ax2.plot(time_bins[:-1], firing_rate, 'blue', alpha=0.7, linewidth=2)
                ax2.set_ylabel('Firing Rate (Hz)', color='blue')
                ax2.tick_params(axis='y', labelcolor='blue')
        
        ax.set_title('Spike Raster Plot (Last 1s)')
        ax.set_xlabel('Time (ms)')
        ax.set_ylabel('Neuron ID')
        ax.grid(True, alpha=0.3)
    
    def _plot_membrane_potential_distribution(self, ax: plt.Axes, 
                                            network_data: Dict[str, Any]) -> None:
        """Plot distribution of membrane potentials across neurons."""
        membrane_potentials = network_data.get('membrane_potentials', None)
        
        if membrane_potentials is None:
            ax.text(0.5, 0.5, 'No membrane potential data available', 
                   ha='center', va='center', transform=ax.transAxes)
            return
        
        # Create histogram
        ax.hist(membrane_potentials, bins=30, alpha=0.7, color='skyblue', 
               edgecolor='black', density=True)
        
        # Add statistics
        mean_v = np.mean(membrane_potentials)
        std_v = np.std(membrane_potentials)
        
        ax.axvline(mean_v, color='red', linestyle='--', alpha=0.8, 
                  label=f'Mean: {mean_v:.2f} mV')
        ax.axvline(mean_v + std_v, color='orange', linestyle='--', alpha=0.8,
                  label=f'+1σ: {mean_v + std_v:.2f} mV')
        ax.axvline(mean_v - std_v, color='orange', linestyle='--', alpha=0.8,
                  label=f'-1σ: {mean_v - std_v:.2f} mV')
        
        # Add threshold line
        threshold = network_data.get('firing_threshold', -50.0)
        ax.axvline(threshold, color='green', linestyle='-', alpha=0.8,
                  label=f'Threshold: {threshold} mV')
        
        ax.set_title('Membrane Potential Distribution')
        ax.set_xlabel('Membrane Potential (mV)')
        ax.set_ylabel('Probability Density')
        ax.legend()
        ax.grid(True, alpha=0.3)
    
    def _plot_weight_evolution(self, ax: plt.Axes, weight_data: Dict[str, Any]) -> None:
        """Plot evolution of synaptic weights over time."""
        if not weight_data:
            ax.text(0.5, 0.5, 'No weight data available', 
                   ha='center', va='center', transform=ax.transAxes)
            return
        
        # Extract weight time series
        weight_history = {}
        for connection, weights in weight_data.items():
            if isinstance(weights, list) and len(weights) > 1:
                weight_history[connection] = weights
        
        if not weight_history:
            ax.text(0.5, 0.5, 'No weight evolution data', 
                   ha='center', va='center', transform=ax.transAxes)
            return
        
        # Plot weight evolution for top connections
        colors = plt.cm.tab10(np.linspace(0, 1, min(10, len(weight_history))))
        
        for i, (connection, weights) in enumerate(list(weight_history.items())[:10]):
            time_points = np.arange(len(weights))
            ax.plot(time_points, weights, color=colors[i], 
                   label=f'Synapse {connection}', alpha=0.7, linewidth=1.5)
        
        ax.set_title('Synaptic Weight Evolution')
        ax.set_xlabel('Time Steps')
        ax.set_ylabel('Synaptic Weight')
        ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        ax.grid(True, alpha=0.3)
    
    def _plot_stdp_curve(self, ax: plt.Axes) -> None:
        """Plot STDP learning curve based on spike timing differences."""
        # STDP parameters (from ULTRA constants)
        A_plus = 0.1   # LTP amplitude
        A_minus = 0.105  # LTD amplitude  
        tau_plus = 20.0   # LTP time constant
        tau_minus = 20.0  # LTD time constant
        
        # Generate time differences
        delta_t = np.linspace(-100, 100, 1000)
        
        # Calculate STDP curve: Δw_ij = A_+exp(-Δt/τ_+) if Δt > 0, -A_-exp(Δt/τ_-) if Δt < 0
        weight_change = np.zeros_like(delta_t)
        
        # LTP (post before pre, Δt > 0)
        ltp_mask = delta_t > 0
        weight_change[ltp_mask] = A_plus * np.exp(-delta_t[ltp_mask] / tau_plus)
        
        # LTD (pre before post, Δt < 0)
        ltd_mask = delta_t < 0
        weight_change[ltd_mask] = -A_minus * np.exp(delta_t[ltd_mask] / tau_minus)
        
        # Plot STDP curve
        ax.plot(delta_t, weight_change, 'purple', linewidth=2)
        ax.axhline(0, color='black', linestyle='--', alpha=0.5)
        ax.axvline(0, color='black', linestyle='--', alpha=0.5)
        
        # Highlight LTP and LTD regions
        ax.fill_between(delta_t[ltp_mask], 0, weight_change[ltp_mask], 
                       alpha=0.3, color='red', label='LTP')
        ax.fill_between(delta_t[ltd_mask], 0, weight_change[ltd_mask], 
                       alpha=0.3, color='blue', label='LTD')
        
        ax.set_title('STDP Learning Window')
        ax.set_xlabel('Δt = t_post - t_pre (ms)')
        ax.set_ylabel('Δw (weight change)')
        ax.legend()
        ax.grid(True, alpha=0.3)
    
    def _plot_network_statistics(self, ax: plt.Axes, network_data: Dict[str, Any],
                               spike_data: List[Dict[str, Any]]) -> None:
        """Plot network statistics and metrics."""
        # Calculate network statistics
        stats = {}
        
        # Network connectivity
        adjacency_matrix = network_data.get('adjacency_matrix', None)
        if adjacency_matrix is not None:
            stats['Total Connections'] = np.sum(adjacency_matrix > 0)
            stats['Connection Density'] = np.mean(adjacency_matrix > 0)
            stats['Avg Connection Strength'] = np.mean(adjacency_matrix[adjacency_matrix > 0])
        
        # Neuron activity
        membrane_potentials = network_data.get('membrane_potentials', None)
        if membrane_potentials is not None:
            threshold = network_data.get('firing_threshold', -50.0)
            stats['Active Neurons'] = np.sum(membrane_potentials > threshold)
            stats['Avg Membrane Potential'] = np.mean(membrane_potentials)
        
        # Spike statistics
        if spike_data:
            current_time = time.time()
            recent_spikes = [s for s in spike_data if current_time - s.get('timestamp', 0) < 1.0]
            stats['Spikes (last 1s)'] = len(recent_spikes)
            if recent_spikes:
                unique_neurons = len(set(s.get('neuron_id', 0) for s in recent_spikes))
                stats['Active Spiking Neurons'] = unique_neurons
        
        # Plot statistics as bar chart
        if stats:
            keys = list(stats.keys())
            values = list(stats.values())
            
            bars = ax.bar(range(len(keys)), values, color='skyblue', alpha=0.7)
            ax.set_xticks(range(len(keys)))
            ax.set_xticklabels(keys, rotation=45, ha='right')
            ax.set_ylabel('Value')
            ax.set_title('Network Statistics')
            
            # Add value labels on bars
            for bar, value in zip(bars, values):
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height,
                       f'{value:.3f}' if isinstance(value, float) else str(value),
                       ha='center', va='bottom', fontsize=8)
        else:
            ax.text(0.5, 0.5, 'No statistics available', 
                   ha='center', va='center', transform=ax.transAxes)
        
        ax.grid(True, alpha=0.3)
    
    def update_visualization(self, new_data: Dict[str, Any]) -> None:
        """Update visualization with new neuromorphic data."""
        with self.update_lock:
            # Update spike history
            spike_data = new_data.get('spike_events', [])
            for spike in spike_data:
                neuron_id = spike.get('neuron_id', 0)
                self.spike_history[neuron_id].append(spike)
                
                # Limit history length
                if len(self.spike_history[neuron_id]) > 1000:
                    self.spike_history[neuron_id] = self.spike_history[neuron_id][-1000:]
            
            # Update weight history
            weight_data = new_data.get('weight_changes', {})
            for connection, weight in weight_data.items():
                if connection not in self.weight_history:
                    self.weight_history[connection] = []
                self.weight_history[connection].append(weight)
                
                # Limit history length
                if len(self.weight_history[connection]) > 1000:
                    self.weight_history[connection] = self.weight_history[connection][-1000:]
            
            self.last_update_time = time.time()

class SynapticPlasticityVisualizer(BaseVisualizer):
    """Specialized visualizer for STDP and synaptic plasticity mechanisms."""
    
    def __init__(self, config: VisualizationConfig, logger: Optional[logging.Logger] = None):
        super().__init__(config, logger)
        self.stdp_events = deque(maxlen=1000)
        self.weight_matrices = deque(maxlen=100)
        
    def create_visualization(self, data: Dict[str, Any], **kwargs) -> plt.Figure:
        """Create comprehensive synaptic plasticity visualization."""
        fig = plt.figure(figsize=(16, 12))
        
        # Create subplot layout
        gs = fig.add_gridspec(3, 3)
        
        # STDP event scatter plot
        ax_stdp = fig.add_subplot(gs[0, :2])
        self._plot_stdp_events(ax_stdp, data.get('stdp_events', []))
        
        # Weight matrix heatmap
        ax_weights = fig.add_subplot(gs[1, :2])
        self._plot_weight_matrix(ax_weights, data.get('weight_matrix', None))
        
        # Weight distribution
        ax_dist = fig.add_subplot(gs[2, 0])
        self._plot_weight_distribution(ax_dist, data.get('weight_matrix', None))
        
        # STDP statistics
        ax_stats = fig.add_subplot(gs[2, 1])
        self._plot_stdp_statistics(ax_stats, data.get('stdp_events', []))
        
        # Homeostatic mechanisms
        ax_homeo = fig.add_subplot(gs[0, 2])
        self._plot_homeostatic_scaling(ax_homeo, data.get('homeostatic_data', {}))
        
        # Synaptic competition
        ax_comp = fig.add_subplot(gs[1, 2])
        self._plot_synaptic_competition(ax_comp, data.get('competition_data', {}))
        
        # Pruning dynamics
        ax_prune = fig.add_subplot(gs[2, 2])
        self._plot_pruning_dynamics(ax_prune, data.get('pruning_events', []))
        
        plt.tight_layout()
        return fig
    
    def _plot_stdp_events(self, ax: plt.Axes, stdp_events: List[Dict[str, Any]]) -> None:
        """Plot STDP events as scatter plot of Δt vs Δw."""
        if not stdp_events:
            ax.text(0.5, 0.5, 'No STDP events available', 
                   ha='center', va='center', transform=ax.transAxes)
            return
        
        # Extract delta_t and weight_change from events
        delta_ts = []
        weight_changes = []
        event_types = []
        
        for event in stdp_events:
            delta_t = event.get('delta_t', 0)
            weight_change = event.get('weight_change', 0)
            
            delta_ts.append(delta_t)
            weight_changes.append(weight_change)
            event_types.append('LTP' if delta_t > 0 and weight_change > 0 else 'LTD')
        
        # Separate LTP and LTD events
        ltp_mask = np.array([et == 'LTP' for et in event_types])
        ltd_mask = ~ltp_mask
        
        # Plot LTP events
        if np.any(ltp_mask):
            ax.scatter(np.array(delta_ts)[ltp_mask], np.array(weight_changes)[ltp_mask], 
                      c='red', alpha=0.6, s=20, label='LTP')
        
        # Plot LTD events
        if np.any(ltd_mask):
            ax.scatter(np.array(delta_ts)[ltd_mask], np.array(weight_changes)[ltd_mask], 
                      c='blue', alpha=0.6, s=20, label='LTD')
        
        # Overlay theoretical STDP curve
        delta_t_theory = np.linspace(-100, 100, 1000)
        A_plus, A_minus = 0.1, 0.105
        tau_plus, tau_minus = 20.0, 20.0
        
        weight_change_theory = np.zeros_like(delta_t_theory)
        ltp_mask_theory = delta_t_theory > 0
        ltd_mask_theory = delta_t_theory < 0
        
        weight_change_theory[ltp_mask_theory] = A_plus * np.exp(-delta_t_theory[ltp_mask_theory] / tau_plus)
        weight_change_theory[ltd_mask_theory] = -A_minus * np.exp(delta_t_theory[ltd_mask_theory] / tau_minus)
        
        ax.plot(delta_t_theory, weight_change_theory, 'black', linewidth=2, 
               alpha=0.8, label='Theoretical STDP')
        
        ax.set_title('STDP Events: Δt vs Δw')
        ax.set_xlabel('Δt = t_post - t_pre (ms)')
        ax.set_ylabel('Δw (weight change)')
        ax.legend()
        ax.grid(True, alpha=0.3)
        ax.axhline(0, color='black', linestyle='--', alpha=0.5)
        ax.axvline(0, color='black', linestyle='--', alpha=0.5)
    
    def _plot_weight_matrix(self, ax: plt.Axes, weight_matrix: Optional[np.ndarray]) -> None:
        """Plot synaptic weight matrix as heatmap."""
        if weight_matrix is None:
            ax.text(0.5, 0.5, 'No weight matrix available', 
                   ha='center', va='center', transform=ax.transAxes)
            return
        
        # Create heatmap
        im = ax.imshow(weight_matrix, cmap='RdBu_r', aspect='auto', 
                      vmin=-np.max(np.abs(weight_matrix)), 
                      vmax=np.max(np.abs(weight_matrix)))
        
        # Add colorbar
        cbar = plt.colorbar(im, ax=ax)
        cbar.set_label('Synaptic Weight')
        
        ax.set_title('Synaptic Weight Matrix')
        ax.set_xlabel('Post-synaptic Neuron')
        ax.set_ylabel('Pre-synaptic Neuron')
    
    def _plot_weight_distribution(self, ax: plt.Axes, weight_matrix: Optional[np.ndarray]) -> None:
        """Plot distribution of synaptic weights."""
        if weight_matrix is None:
            ax.text(0.5, 0.5, 'No weight data', ha='center', va='center', transform=ax.transAxes)
            return
        
        # Extract non-zero weights
        weights = weight_matrix[weight_matrix != 0]
        
        if len(weights) == 0:
            ax.text(0.5, 0.5, 'No connections', ha='center', va='center', transform=ax.transAxes)
            return
        
        # Plot histogram
        ax.hist(weights, bins=50, alpha=0.7, color='skyblue', density=True)
        
        # Add statistics
        mean_w = np.mean(weights)
        std_w = np.std(weights)
        
        ax.axvline(mean_w, color='red', linestyle='--', 
                  label=f'Mean: {mean_w:.4f}')
        ax.axvline(0, color='black', linestyle='-', alpha=0.5)
        
        ax.set_title('Weight Distribution')
        ax.set_xlabel('Synaptic Weight')
        ax.set_ylabel('Density')
        ax.legend()
        ax.grid(True, alpha=0.3)
    
    def _plot_stdp_statistics(self, ax: plt.Axes, stdp_events: List[Dict[str, Any]]) -> None:
        """Plot STDP statistics and trends."""
        if not stdp_events:
            ax.text(0.5, 0.5, 'No STDP data', ha='center', va='center', transform=ax.transAxes)
            return
        
        # Calculate statistics
        delta_ts = [event.get('delta_t', 0) for event in stdp_events]
        weight_changes = [event.get('weight_change', 0) for event in stdp_events]
        
        ltp_events = sum(1 for dt, dw in zip(delta_ts, weight_changes) if dt > 0 and dw > 0)
        ltd_events = sum(1 for dt, dw in zip(delta_ts, weight_changes) if dt < 0 and dw < 0)
        total_events = len(stdp_events)
        
        # Create bar plot
        categories = ['LTP Events', 'LTD Events', 'Total Events']
        values = [ltp_events, ltd_events, total_events]
        colors = ['red', 'blue', 'gray']
        
        bars = ax.bar(categories, values, color=colors, alpha=0.7)
        
        # Add value labels
        for bar, value in zip(bars, values):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height,
                   str(value), ha='center', va='bottom')
        
        ax.set_title('STDP Event Statistics')
        ax.set_ylabel('Count')
        ax.grid(True, alpha=0.3)
    
    def _plot_homeostatic_scaling(self, ax: plt.Axes, homeostatic_data: Dict[str, Any]) -> None:
        """Plot homeostatic scaling mechanisms."""
        target_rates = homeostatic_data.get('target_rates', [])
        actual_rates = homeostatic_data.get('actual_rates', [])
        scaling_factors = homeostatic_data.get('scaling_factors', [])
        
        if not target_rates:
            ax.text(0.5, 0.5, 'No homeostatic data', ha='center', va='center', transform=ax.transAxes)
            return
        
        time_points = np.arange(len(target_rates))
        
        # Plot target vs actual rates
        ax.plot(time_points, target_rates, 'green', label='Target Rate', linewidth=2)
        ax.plot(time_points, actual_rates, 'red', label='Actual Rate', linewidth=2)
        
        # Plot scaling factors on secondary axis
        ax2 = ax.twinx()
        ax2.plot(time_points, scaling_factors, 'blue', alpha=0.7, 
                label='Scaling Factor', linewidth=1.5)
        ax2.set_ylabel('Scaling Factor', color='blue')
        ax2.tick_params(axis='y', labelcolor='blue')
        
        ax.set_title('Homeostatic Scaling')
        ax.set_xlabel('Time Steps')
        ax.set_ylabel('Firing Rate (Hz)')
        ax.legend(loc='upper left')
        ax2.legend(loc='upper right')
        ax.grid(True, alpha=0.3)
    
    def _plot_synaptic_competition(self, ax: plt.Axes, competition_data: Dict[str, Any]) -> None:
        """Plot synaptic competition dynamics."""
        synaptic_strengths = competition_data.get('synaptic_strengths', {})
        
        if not synaptic_strengths:
            ax.text(0.5, 0.5, 'No competition data', ha='center', va='center', transform=ax.transAxes)
            return
        
        # Plot evolution of top synapses
        colors = plt.cm.tab10(np.linspace(0, 1, min(10, len(synaptic_strengths))))
        
        for i, (synapse_id, strengths) in enumerate(list(synaptic_strengths.items())[:10]):
            if isinstance(strengths, list) and len(strengths) > 1:
                time_points = np.arange(len(strengths))
                ax.plot(time_points, strengths, color=colors[i], 
                       label=f'Synapse {synapse_id}', alpha=0.7)
        
        ax.set_title('Synaptic Competition')
        ax.set_xlabel('Time Steps')
        ax.set_ylabel('Synaptic Strength')
        ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        ax.grid(True, alpha=0.3)
    
    def _plot_pruning_dynamics(self, ax: plt.Axes, pruning_events: List[Dict[str, Any]]) -> None:
        """Plot synaptic pruning dynamics."""
        if not pruning_events:
            ax.text(0.5, 0.5, 'No pruning events', ha='center', va='center', transform=ax.transAxes)
            return
        
        # Extract pruning statistics over time
        timestamps = [event.get('timestamp', 0) for event in pruning_events]
        connections_removed = [event.get('connections_removed', 0) for event in pruning_events]
        
        # Create cumulative pruning plot
        cumulative_pruned = np.cumsum(connections_removed)
        
        ax.plot(timestamps, cumulative_pruned, 'red', linewidth=2, 
               label='Cumulative Pruned')
        ax.scatter(timestamps, connections_removed, c='blue', s=20, alpha=0.7,
                  label='Per-Event Pruning')
        
        ax.set_title('Synaptic Pruning Dynamics')
        ax.set_xlabel('Time')
        ax.set_ylabel('Connections Pruned')
        ax.legend()
        ax.grid(True, alpha=0.3)
    
    def update_visualization(self, new_data: Dict[str, Any]) -> None:
        """Update plasticity visualization with new data."""
        with self.update_lock:
            # Update STDP events
            stdp_events = new_data.get('stdp_events', [])
            self.stdp_events.extend(stdp_events)
            
            # Update weight matrices
            weight_matrix = new_data.get('weight_matrix', None)
            if weight_matrix is not None:
                self.weight_matrices.append(weight_matrix.copy())
            
            self.last_update_time = time.time()

# =============================================================================
# Attention and Transformer Visualization
# =============================================================================

class AttentionVisualizer(BaseVisualizer):
    """Comprehensive visualizer for attention mechanisms and transformer dynamics."""
    
    def __init__(self, config: VisualizationConfig, logger: Optional[logging.Logger] = None):
        super().__init__(config, logger)
        self.attention_history = defaultdict(list)
        self.head_statistics = defaultdict(dict)
        
    def create_visualization(self, data: Dict[str, Any], **kwargs) -> plt.Figure:
        """Create comprehensive attention mechanism visualization."""
        fig = plt.figure(figsize=(20, 16))
        
        # Create complex subplot layout
        gs = fig.add_gridspec(4, 4, height_ratios=[1, 1, 1, 0.8])
        
        # Attention weight matrix
        ax_attn = fig.add_subplot(gs[0, :2])
        self._plot_attention_matrix(ax_attn, data.get('attention_weights', None))
        
        # Multi-head attention comparison
        ax_heads = fig.add_subplot(gs[1, :2])
        self._plot_multihead_attention(ax_heads, data.get('multihead_attention', {}))
        
        # Attention entropy analysis
        ax_entropy = fig.add_subplot(gs[2, 0])
        self._plot_attention_entropy(ax_entropy, data.get('attention_weights', None))
        
        # Attention sparsity
        ax_sparsity = fig.add_subplot(gs[2, 1])
        self._plot_attention_sparsity(ax_sparsity, data.get('attention_weights', None))
        
        # Dynamic attention evolution
        ax_evolution = fig.add_subplot(gs[0, 2:])
        self._plot_attention_evolution(ax_evolution, data.get('attention_history', []))
        
        # Attention flow patterns
        ax_flow = fig.add_subplot(gs[1, 2:])
        self._plot_attention_flow(ax_flow, data.get('attention_weights', None))
        
        # Head specialization analysis
        ax_special = fig.add_subplot(gs[2, 2])
        self._plot_head_specialization(ax_special, data.get('multihead_attention', {}))
        
        # Recursive depth analysis
        ax_recursive = fig.add_subplot(gs[2, 3])
        self._plot_recursive_depth(ax_recursive, data.get('recursive_data', {}))
        
        # Summary statistics
        ax_stats = fig.add_subplot(gs[3, :])
        self._plot_attention_statistics(ax_stats, data)
        
        plt.tight_layout()
        return fig
    
    def _plot_attention_matrix(self, ax: plt.Axes, attention_weights: Optional[np.ndarray]) -> None:
        """Plot attention weight matrix as heatmap."""
        if attention_weights is None:
            ax.text(0.5, 0.5, 'No attention weights available', 
                   ha='center', va='center', transform=ax.transAxes)
            return
        
        # Create heatmap
        im = ax.imshow(attention_weights, cmap='plasma', aspect='auto')
        
        # Add colorbar
        cbar = plt.colorbar(im, ax=ax)
        cbar.set_label('Attention Weight')
        
        # Highlight diagonal (self-attention)
        ax.plot([0, attention_weights.shape[1]-1], [0, attention_weights.shape[0]-1], 
               'white', linewidth=2, alpha=0.7, linestyle='--')
        
        ax.set_title('Attention Weight Matrix')
        ax.set_xlabel('Key Position')
        ax.set_ylabel('Query Position')
    
    def _plot_multihead_attention(self, ax: plt.Axes, multihead_data: Dict[str, Any]) -> None:
        """Plot multi-head attention comparison."""
        if not multihead_data:
            ax.text(0.5, 0.5, 'No multi-head data available', 
                   ha='center', va='center', transform=ax.transAxes)
            return
        
        num_heads = len(multihead_data)
        
        if num_heads == 0:
            ax.text(0.5, 0.5, 'No attention heads', ha='center', va='center', transform=ax.transAxes)
            return
        
        # Create subplot grid for heads
        cols = min(4, num_heads)
        rows = (num_heads + cols - 1) // cols
        
        for i, (head_id, head_data) in enumerate(multihead_data.items()):
            if i >= 16:  # Limit to 16 heads for visualization
                break
                
            row = i // cols
            col = i % cols
            
            # Calculate subplot position
            x_pos = col / cols
            y_pos = 1 - (row + 1) / rows
            width = 1 / cols * 0.9
            height = 1 / rows * 0.9
            
            # Create inset axes
            inset_ax = ax.inset_axes([x_pos, y_pos, width, height])
            
            attention_weights = head_data.get('attention_weights', None)
            if attention_weights is not None:
                inset_ax.imshow(attention_weights, cmap='plasma', aspect='auto')
                inset_ax.set_title(f'Head {head_id}', fontsize=8)
                inset_ax.set_xticks([])
                inset_ax.set_yticks([])
        
        ax.set_title('Multi-Head Attention Patterns')
        ax.set_xlim(0, 1)
        ax.set_ylim(0, 1)
        ax.set_xticks([])
        ax.set_yticks([])
    
    def _plot_attention_entropy(self, ax: plt.Axes, attention_weights: Optional[np.ndarray]) -> None:
        """Plot attention entropy distribution."""
        if attention_weights is None:
            ax.text(0.5, 0.5, 'No attention data', ha='center', va='center', transform=ax.transAxes)
            return
        
        # Calculate entropy for each query position
        entropies = []
        for i in range(attention_weights.shape[0]):
            weights = attention_weights[i]
            weights = weights[weights > 1e-12]  # Avoid log(0)
            if len(weights) > 0:
                entropy = -np.sum(weights * np.log(weights))
                entropies.append(entropy)
        
        if entropies:
            ax.hist(entropies, bins=20, alpha=0.7, color='skyblue', density=True)
            
            # Add statistics
            mean_entropy = np.mean(entropies)
            ax.axvline(mean_entropy, color='red', linestyle='--', 
                      label=f'Mean: {mean_entropy:.3f}')
            
            ax.set_title('Attention Entropy Distribution')
            ax.set_xlabel('Entropy (nats)')
            ax.set_ylabel('Density')
            ax.legend()
            ax.grid(True, alpha=0.3)
        else:
            ax.text(0.5, 0.5, 'No entropy data', ha='center', va='center', transform=ax.transAxes)
    
    def _plot_attention_sparsity(self, ax: plt.Axes, attention_weights: Optional[np.ndarray]) -> None:
        """Plot attention sparsity analysis."""
        if attention_weights is None:
            ax.text(0.5, 0.5, 'No attention data', ha='center', va='center', transform=ax.transAxes)
            return
        
        # Calculate sparsity at different thresholds
        thresholds = np.linspace(0, 0.1, 50)
        sparsities = []
        
        for threshold in thresholds:
            sparse_count = np.sum(attention_weights < threshold)
            total_count = attention_weights.size
            sparsity = sparse_count / total_count
            sparsities.append(sparsity)
        
        ax.plot(thresholds, sparsities, 'blue', linewidth=2)
        ax.axhline(0.5, color='red', linestyle='--', alpha=0.7, label='50% Sparsity')
        ax.axhline(0.9, color='orange', linestyle='--', alpha=0.7, label='90% Sparsity')
        
        ax.set_title('Attention Sparsity Analysis')
        ax.set_xlabel('Threshold')
        ax.set_ylabel('Sparsity Ratio')
        ax.legend()
        ax.grid(True, alpha=0.3)
    
    def _plot_attention_evolution(self, ax: plt.Axes, attention_history: List[Dict[str, Any]]) -> None:
        """Plot evolution of attention patterns over time."""
        if not attention_history:
            ax.text(0.5, 0.5, 'No attention history', ha='center', va='center', transform=ax.transAxes)
            return
        
        # Extract metrics over time
        time_points = []
        entropies = []
        sparsities = []
        max_attentions = []
        
        for i, attn_data in enumerate(attention_history):
            time_points.append(i)
            
            weights = attn_data.get('attention_weights', None)
            if weights is not None:
                # Calculate entropy
                entropy = 0
                for row in weights:
                    row_weights = row[row > 1e-12]
                    if len(row_weights) > 0:
                        entropy += -np.sum(row_weights * np.log(row_weights))
                entropies.append(entropy / weights.shape[0])  # Average entropy
                
                # Calculate sparsity (fraction below threshold)
                threshold = 0.01
                sparsity = np.sum(weights < threshold) / weights.size
                sparsities.append(sparsity)
                
                # Maximum attention
                max_attentions.append(np.max(weights))
            else:
                entropies.append(0)
                sparsities.append(0)
                max_attentions.append(0)
        
        # Plot metrics
        ax.plot(time_points, entropies, 'blue', label='Avg Entropy', linewidth=2)
        
        # Secondary y-axis for sparsity
        ax2 = ax.twinx()
        ax2.plot(time_points, sparsities, 'red', label='Sparsity', linewidth=2, alpha=0.7)
        ax2.plot(time_points, max_attentions, 'green', label='Max Attention', linewidth=2, alpha=0.7)
        
        ax.set_title('Attention Evolution Over Time')
        ax.set_xlabel('Time Step')
        ax.set_ylabel('Average Entropy', color='blue')
        ax2.set_ylabel('Sparsity / Max Attention', color='red')
        
        ax.legend(loc='upper left')
        ax2.legend(loc='upper right')
        ax.grid(True, alpha=0.3)
    
    def _plot_attention_flow(self, ax: plt.Axes, attention_weights: Optional[np.ndarray]) -> None:
        """Plot attention flow patterns using streamlines."""
        if attention_weights is None:
            ax.text(0.5, 0.5, 'No attention data', ha='center', va='center', transform=ax.transAxes)
            return
        
        # Create coordinate grids
        seq_len = attention_weights.shape[0]
        y, x = np.mgrid[0:seq_len:complex(seq_len), 0:seq_len:complex(seq_len)]
        
        # Calculate flow field from attention gradients
        dy, dx = np.gradient(attention_weights)
        
        # Normalize flow vectors
        magnitude = np.sqrt(dx**2 + dy**2)
        magnitude[magnitude == 0] = 1  # Avoid division by zero
        
        u = dx / magnitude
        v = dy / magnitude
        
        # Create streamplot
        ax.streamplot(x, y, u, v, density=2, color=attention_weights, 
                     cmap='plasma', alpha=0.7)
        
        # Overlay attention weights as background
        im = ax.imshow(attention_weights, cmap='plasma', alpha=0.3, aspect='auto')
        
        ax.set_title('Attention Flow Patterns')
        ax.set_xlabel('Key Position')
        ax.set_ylabel('Query Position')
    
    def _plot_head_specialization(self, ax: plt.Axes, multihead_data: Dict[str, Any]) -> None:
        """Plot head specialization metrics."""
        if not multihead_data:
            ax.text(0.5, 0.5, 'No head data', ha='center', va='center', transform=ax.transAxes)
            return
        
        # Calculate specialization metrics for each head
        head_ids = []
        specializations = []
        
        for head_id, head_data in multihead_data.items():
            attention_weights = head_data.get('attention_weights', None)
            if attention_weights is not None:
                # Calculate specialization as inverse of entropy
                total_entropy = 0
                for row in attention_weights:
                    row_weights = row[row > 1e-12]
                    if len(row_weights) > 0:
                        total_entropy += -np.sum(row_weights * np.log(row_weights))
                
                # Normalize by sequence length
                avg_entropy = total_entropy / attention_weights.shape[0]
                specialization = 1.0 / (1.0 + avg_entropy)  # Higher specialization = lower entropy
                
                head_ids.append(head_id)
                specializations.append(specialization)
        
        if head_ids:
            colors = plt.cm.viridis(np.linspace(0, 1, len(head_ids)))
            bars = ax.bar(head_ids, specializations, color=colors, alpha=0.7)
            
            # Add value labels
            for bar, spec in zip(bars, specializations):
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height,
                       f'{spec:.3f}', ha='center', va='bottom', fontsize=8)
            
            ax.set_title('Head Specialization')
            ax.set_xlabel('Head ID')
            ax.set_ylabel('Specialization Score')
            ax.set_xticklabels(head_ids, rotation=45)
            ax.grid(True, alpha=0.3)
        else:
            ax.text(0.5, 0.5, 'No specialization data', ha='center', va='center', transform=ax.transAxes)
    
    def _plot_recursive_depth(self, ax: plt.Axes, recursive_data: Dict[str, Any]) -> None:
        """Plot recursive processing depth analysis."""
        halting_probs = recursive_data.get('halting_probabilities', [])
        computation_steps = recursive_data.get('computation_steps', [])
        
        if not halting_probs:
            ax.text(0.5, 0.5, 'No recursive data', ha='center', va='center', transform=ax.transAxes)
            return
        
        # Plot halting probabilities over depth
        depths = np.arange(len(halting_probs))
        ax.plot(depths, halting_probs, 'blue', marker='o', linewidth=2, markersize=6)
        
        # Add threshold line
        threshold = 0.9
        ax.axhline(threshold, color='red', linestyle='--', alpha=0.7, 
                  label=f'Halting Threshold: {threshold}')
        
        # Highlight actual halting point
        if computation_steps:
            avg_steps = np.mean(computation_steps)
            ax.axvline(avg_steps, color='green', linestyle='--', alpha=0.7,
                      label=f'Avg Steps: {avg_steps:.1f}')
        
        ax.set_title('Recursive Processing Depth')
        ax.set_xlabel('Recursion Depth')
        ax.set_ylabel('Halting Probability')
        ax.legend()
        ax.grid(True, alpha=0.3)
    
    def _plot_attention_statistics(self, ax: plt.Axes, data: Dict[str, Any]) -> None:
        """Plot comprehensive attention statistics."""
        attention_weights = data.get('attention_weights', None)
        multihead_data = data.get('multihead_attention', {})
        
        if attention_weights is None and not multihead_data:
            ax.text(0.5, 0.5, 'No attention data for statistics', 
                   ha='center', va='center', transform=ax.transAxes)
            return
        
        stats = {}
        
        # Single attention head statistics
        if attention_weights is not None:
            stats['Mean Attention'] = np.mean(attention_weights)
            stats['Max Attention'] = np.max(attention_weights)
            stats['Attention Std'] = np.std(attention_weights)
            
            # Diagonal dominance (self-attention strength)
            diagonal_sum = np.sum(np.diag(attention_weights))
            total_sum = np.sum(attention_weights)
            stats['Self-Attention Ratio'] = diagonal_sum / total_sum if total_sum > 0 else 0
            
            # Sparsity (fraction below 1% of max)
            threshold = np.max(attention_weights) * 0.01
            sparsity = np.sum(attention_weights < threshold) / attention_weights.size
            stats['Sparsity (1% threshold)'] = sparsity
        
        # Multi-head statistics
        if multihead_data:
            stats['Number of Heads'] = len(multihead_data)
            
            # Calculate inter-head similarity
            head_similarities = []
            head_weights = []
            
            for head_data in multihead_data.values():
                if 'attention_weights' in head_data:
                    head_weights.append(head_data['attention_weights'].flatten())
            
            if len(head_weights) > 1:
                for i in range(len(head_weights)):
                    for j in range(i+1, len(head_weights)):
                        corr = np.corrcoef(head_weights[i], head_weights[j])[0, 1]
                        if not np.isnan(corr):
                            head_similarities.append(corr)
                
                if head_similarities:
                    stats['Avg Inter-Head Similarity'] = np.mean(head_similarities)
        
        # Create horizontal bar chart
        if stats:
            stat_names = list(stats.keys())
            stat_values = list(stats.values())
            
            y_pos = np.arange(len(stat_names))
            bars = ax.barh(y_pos, stat_values, alpha=0.7, color='skyblue')
            
            # Add value labels
            for i, (bar, value) in enumerate(zip(bars, stat_values)):
                width = bar.get_width()
                ax.text(width, bar.get_y() + bar.get_height()/2,
                       f'{value:.4f}' if isinstance(value, float) else str(value),
                       ha='left', va='center', fontsize=9)
            
            ax.set_yticks(y_pos)
            ax.set_yticklabels(stat_names)
            ax.set_xlabel('Value')
            ax.set_title('Attention Mechanism Statistics')
            ax.grid(True, alpha=0.3, axis='x')
        else:
            ax.text(0.5, 0.5, 'No statistics available', 
                   ha='center', va='center', transform=ax.transAxes)
    
    def update_visualization(self, new_data: Dict[str, Any]) -> None:
        """Update attention visualization with new data."""
        with self.update_lock:
            # Update attention history
            attention_weights = new_data.get('attention_weights', None)
            if attention_weights is not None:
                layer_id = new_data.get('layer_id', 0)
                head_id = new_data.get('head_id', 0)
                key = f"layer_{layer_id}_head_{head_id}"
                
                self.attention_history[key].append({
                    'attention_weights': attention_weights.copy(),
                    'timestamp': time.time()
                })
                
                # Limit history length
                if len(self.attention_history[key]) > 100:
                    self.attention_history[key] = self.attention_history[key][-100:]
            
            # Update head statistics
            multihead_data = new_data.get('multihead_attention', {})
            for head_id, head_data in multihead_data.items():
                if head_id not in self.head_statistics:
                    self.head_statistics[head_id] = {'entropy_history': [], 'sparsity_history': []}
                
                # Calculate and store entropy
                weights = head_data.get('attention_weights', None)
                if weights is not None:
                    entropy = 0
                    for row in weights:
                        row_weights = row[row > 1e-12]
                        if len(row_weights) > 0:
                            entropy += -np.sum(row_weights * np.log(row_weights))
                    avg_entropy = entropy / weights.shape[0]
                    
                    self.head_statistics[head_id]['entropy_history'].append(avg_entropy)
                    
                    # Calculate sparsity
                    threshold = 0.01
                    sparsity = np.sum(weights < threshold) / weights.size
                    self.head_statistics[head_id]['sparsity_history'].append(sparsity)
                    
                    # Limit history
                    for metric in ['entropy_history', 'sparsity_history']:
                        if len(self.head_statistics[head_id][metric]) > 1000:
                            self.head_statistics[head_id][metric] = self.head_statistics[head_id][metric][-1000:]
            
            self.last_update_time = time.time()

# =============================================================================
# Diffusion Process Visualization
# =============================================================================

class DiffusionProcessVisualizer(BaseVisualizer):
    """Comprehensive visualizer for diffusion-based reasoning processes."""
    
    def __init__(self, config: VisualizationConfig, logger: Optional[logging.Logger] = None):
        super().__init__(config, logger)
        self.diffusion_trajectories = defaultdict(list)
        self.convergence_history = defaultdict(list)
        self.thought_space_cache = None
        
    def create_visualization(self, data: Dict[str, Any], **kwargs) -> plt.Figure:
        """Create comprehensive diffusion process visualization."""
        fig = plt.figure(figsize=(20, 16))
        
        # Create complex subplot layout
        gs = fig.add_gridspec(4, 4, height_ratios=[1.2, 1, 1, 0.8])
        
        # Thought space 2D/3D projection
        ax_space = fig.add_subplot(gs[0, :2])
        self._plot_thought_space_projection(ax_space, data.get('thought_space_data', {}))
        
        # Diffusion trajectory
        ax_traj = fig.add_subplot(gs[0, 2:])
        self._plot_diffusion_trajectory(ax_traj, data.get('diffusion_trajectory', []))
        
        # Convergence analysis
        ax_conv = fig.add_subplot(gs[1, :2])
        self._plot_convergence_analysis(ax_conv, data.get('convergence_data', {}))
        
        # Noise schedule visualization
        ax_noise = fig.add_subplot(gs[1, 2])
        self._plot_noise_schedule(ax_noise, data.get('noise_schedule', {}))
        
        # Reverse diffusion quality
        ax_reverse = fig.add_subplot(gs[1, 3])
        self._plot_reverse_diffusion_quality(ax_reverse, data.get('reverse_diffusion_data', {}))
        
        # Conceptual landscape
        ax_landscape = fig.add_subplot(gs[2, :2])
        self._plot_conceptual_landscape(ax_landscape, data.get('concept_landscape', {}))
        
        # Guidance analysis
        ax_guidance = fig.add_subplot(gs[2, 2])
        self._plot_guidance_analysis(ax_guidance, data.get('guidance_data', {}))
        
        # Uncertainty quantification
        ax_uncertainty = fig.add_subplot(gs[2, 3])
        self._plot_uncertainty_quantification(ax_uncertainty, data.get('uncertainty_data', {}))
        
        # Summary metrics
        ax_metrics = fig.add_subplot(gs[3, :])
        self._plot_diffusion_metrics(ax_metrics, data)
        
        plt.tight_layout()
        return fig
    
    def _plot_thought_space_projection(self, ax: plt.Axes, thought_space_data: Dict[str, Any]) -> None:
        """Plot 2D projection of high-dimensional thought space."""
        concept_embeddings = thought_space_data.get('concept_embeddings', None)
        concept_labels = thought_space_data.get('concept_labels', [])
        current_state = thought_space_data.get('current_state', None)
        
        if concept_embeddings is None:
            ax.text(0.5, 0.5, 'No thought space data available', 
                   ha='center', va='center', transform=ax.transAxes)
            return
        
        # Perform dimensionality reduction if needed
        if concept_embeddings.shape[1] > 2:
            if concept_embeddings.shape[1] > 50:
                # Use PCA first to reduce dimensionality, then t-SNE
                pca = PCA(n_components=50)
                embeddings_pca = pca.fit_transform(concept_embeddings)
                tsne = TSNE(n_components=2, random_state=42, perplexity=min(30, len(concept_embeddings)-1))
                embeddings_2d = tsne.fit_transform(embeddings_pca)
            else:
                tsne = TSNE(n_components=2, random_state=42, perplexity=min(30, len(concept_embeddings)-1))
                embeddings_2d = tsne.fit_transform(concept_embeddings)
        else:
            embeddings_2d = concept_embeddings
        
        # Cache the projection for consistency
        self.thought_space_cache = embeddings_2d
        
        # Plot concept points
        scatter = ax.scatter(embeddings_2d[:, 0], embeddings_2d[:, 1], 
                           c=range(len(embeddings_2d)), cmap='viridis', 
                           alpha=0.6, s=50)
        
        # Add labels if available
        if concept_labels and len(concept_labels) == len(embeddings_2d):
            for i, label in enumerate(concept_labels[:20]):  # Limit to 20 labels
                ax.annotate(label, (embeddings_2d[i, 0], embeddings_2d[i, 1]),
                          xytext=(5, 5), textcoords='offset points',
                          fontsize=8, alpha=0.7)
        
        # Highlight current state if available
        if current_state is not None and len(current_state) == concept_embeddings.shape[1]:
            # Project current state to 2D
            if concept_embeddings.shape[1] > 2:
                # Use the same transformation as for concept embeddings
                current_state_2d = tsne.transform([current_state])[0]
            else:
                current_state_2d = current_state[:2]
            
            ax.scatter(current_state_2d[0], current_state_2d[1], 
                      c='red', s=200, marker='*', edgecolors='black',
                      label='Current State', zorder=10)
        
        # Add colorbar
        cbar = plt.colorbar(scatter, ax=ax)
        cbar.set_label('Concept Index')
        
        ax.set_title('Thought Space Projection (t-SNE)')
        ax.set_xlabel('Dimension 1')
        ax.set_ylabel('Dimension 2')
        if current_state is not None:
            ax.legend()
    
    def _plot_diffusion_trajectory(self, ax: plt.Axes, trajectory_data: List[Dict[str, Any]]) -> None:
        """Plot diffusion process trajectory through thought space."""
        if not trajectory_data:
            ax.text(0.5, 0.5, 'No trajectory data available', 
                   ha='center', va='center', transform=ax.transAxes)
            return
        
        # Extract trajectory points
        states = []
        noise_levels = []
        steps = []
        
        for step_data in trajectory_data:
            state = step_data.get('concept_state', None)
            if state is not None:
                states.append(state)
                noise_levels.append(step_data.get('noise_level', 0))
                steps.append(step_data.get('step', 0))
        
        if not states:
            ax.text(0.5, 0.5, 'No valid trajectory states', 
                   ha='center', va='center', transform=ax.transAxes)
            return
        
        states = np.array(states)
        
        # Project to 2D if needed
        if states.shape[1] > 2:
            # Use PCA for trajectory visualization
            pca = PCA(n_components=2)
            states_2d = pca.fit_transform(states)
        else:
            states_2d = states
        
        # Plot trajectory path
        trajectory_line = ax.plot(states_2d[:, 0], states_2d[:, 1], 
                                'blue', linewidth=2, alpha=0.7, label='Trajectory')[0]
        
        # Color points by noise level
        scatter = ax.scatter(states_2d[:, 0], states_2d[:, 1], 
                           c=noise_levels, cmap='coolwarm', 
                           s=60, alpha=0.8, edgecolors='black', linewidth=0.5)
        
        # Highlight start and end points
        ax.scatter(states_2d[0, 0], states_2d[0, 1], 
                  c='green', s=200, marker='o', edgecolors='black',
                  label='Start', zorder=10)
        ax.scatter(states_2d[-1, 0], states_2d[-1, 1], 
                  c='red', s=200, marker='s', edgecolors='black',
                  label='End', zorder=10)
        
        # Add arrows to show direction
        for i in range(0, len(states_2d)-1, max(1, len(states_2d)//20)):
            dx = states_2d[i+1, 0] - states_2d[i, 0]
            dy = states_2d[i+1, 1] - states_2d[i, 1]
            ax.arrow(states_2d[i, 0], states_2d[i, 1], dx*0.3, dy*0.3,
                    head_width=0.02, head_length=0.03, fc='black', ec='black',
                    alpha=0.6)
        
        # Add colorbar for noise levels
        cbar = plt.colorbar(scatter, ax=ax)
        cbar.set_label('Noise Level')
        
        ax.set_title('Diffusion Process Trajectory')
        ax.set_xlabel('PCA Dimension 1')
        ax.set_ylabel('PCA Dimension 2')
        ax.legend()
        ax.grid(True, alpha=0.3)
    
    def _plot_convergence_analysis(self, ax: plt.Axes, convergence_data: Dict[str, Any]) -> None:
        """Plot convergence analysis of diffusion process."""
        convergence_history = convergence_data.get('convergence_history', [])
        entropy_history = convergence_data.get('entropy_history', [])
        
        if not convergence_history:
            ax.text(0.5, 0.5, 'No convergence data available', 
                   ha='center', va='center', transform=ax.transAxes)
            return
        
        steps = np.arange(len(convergence_history))
        
        # Plot convergence metric
        ax.semilogy(steps, convergence_history, 'blue', linewidth=2, label='Convergence')
        
        # Add convergence threshold
        threshold = ULTRA_VIZ_CONSTANTS['CONVERGENCE_TOLERANCE']
        ax.axhline(threshold, color='red', linestyle='--', alpha=0.7,
                  label=f'Threshold: {threshold:.2e}')
        
        # Plot entropy on secondary axis
        if entropy_history and len(entropy_history) == len(convergence_history):
            ax2 = ax.twinx()
            ax2.plot(steps, entropy_history, 'green', linewidth=2, alpha=0.7, label='Entropy')
            ax2.set_ylabel('Entropy', color='green')
            ax2.tick_params(axis='y', labelcolor='green')
            ax2.legend(loc='upper right')
        
        # Detect convergence point
        converged_step = None
        for i, conv in enumerate(convergence_history):
            if conv < threshold:
                converged_step = i
                break
        
        if converged_step is not None:
            ax.axvline(converged_step, color='orange', linestyle=':', alpha=0.8,
                      label=f'Converged at step {converged_step}')
        
        # Add exponential fit if convergence is detected
        if len(convergence_history) > 10:
            # Fit exponential decay: y = A * exp(-r * x)
            try:
                log_conv = np.log(np.maximum(convergence_history, 1e-16))
                fit_steps = steps[log_conv > -30]  # Avoid extreme values
                fit_log_conv = log_conv[log_conv > -30]
                
                if len(fit_steps) > 5:
                    slope, intercept = np.polyfit(fit_steps, fit_log_conv, 1)
                    convergence_rate = -slope
                    
                    # Plot fit
                    fit_y = np.exp(intercept + slope * steps)
                    ax.plot(steps, fit_y, 'red', linestyle=':', alpha=0.7,
                           label=f'Exp fit (rate: {convergence_rate:.4f})')
            except:
                pass  # Skip fitting if it fails
        
        ax.set_title('Diffusion Convergence Analysis')
        ax.set_xlabel('Diffusion Step')
        ax.set_ylabel('Convergence Metric (log scale)')
        ax.legend(loc='upper left')
        ax.grid(True, alpha=0.3)
    
    def _plot_noise_schedule(self, ax: plt.Axes, noise_schedule_data: Dict[str, Any]) -> None:
        """Plot noise schedule for diffusion process."""
        schedule_type = noise_schedule_data.get('schedule_type', 'linear')
        num_timesteps = noise_schedule_data.get('num_timesteps', 1000)
        beta_start = noise_schedule_data.get('beta_start', 1e-4)
        beta_end = noise_schedule_data.get('beta_end', 0.02)
        
        # Generate noise schedule
        timesteps = np.arange(num_timesteps)
        
        if schedule_type == 'linear':
            betas = np.linspace(beta_start, beta_end, num_timesteps)
        elif schedule_type == 'cosine':
            s = 0.008
            steps = timesteps / num_timesteps
            alphas_cumprod = np.cos(((steps + s) / (1 + s)) * np.pi * 0.5) ** 2
            alphas_cumprod = alphas_cumprod / alphas_cumprod[0]
            betas = 1 - (alphas_cumprod[1:] / alphas_cumprod[:-1])
            betas = np.concatenate([[beta_start], betas])
            betas = np.clip(betas, 0, 0.999)
        elif schedule_type == 'sqrt':
            betas = np.sqrt(np.linspace(beta_start**2, beta_end**2, num_timesteps))
        else:
            betas = np.linspace(beta_start, beta_end, num_timesteps)
        
        # Calculate cumulative alpha values
        alphas = 1 - betas
        alphas_cumprod = np.cumprod(alphas)
        
        # Plot beta schedule
        ax.plot(timesteps, betas, 'blue', linewidth=2, label='β(t)')
        ax.plot(timesteps, alphas_cumprod, 'red', linewidth=2, label='ᾱ(t)')
        
        ax.set_title(f'Noise Schedule ({schedule_type})')
        ax.set_xlabel('Timestep')
        ax.set_ylabel('Value')
        ax.legend()
        ax.grid(True, alpha=0.3)
        ax.set_yscale('log')
    
    def _plot_reverse_diffusion_quality(self, ax: plt.Axes, reverse_data: Dict[str, Any]) -> None:
        """Plot quality metrics for reverse diffusion process."""
        denoising_quality = reverse_data.get('denoising_quality', [])
        noise_prediction_accuracy = reverse_data.get('noise_prediction_accuracy', [])
        
        if not denoising_quality:
            ax.text(0.5, 0.5, 'No reverse diffusion data', 
                   ha='center', va='center', transform=ax.transAxes)
            return
        
        steps = np.arange(len(denoising_quality))
        
        # Plot denoising quality
        ax.plot(steps, denoising_quality, 'blue', linewidth=2, label='Denoising Quality')
        
        # Plot noise prediction accuracy if available
        if noise_prediction_accuracy and len(noise_prediction_accuracy) == len(denoising_quality):
            ax.plot(steps, noise_prediction_accuracy, 'red', linewidth=2, 
                   label='Noise Prediction Accuracy')
        
        # Add quality thresholds
        ax.axhline(0.8, color='green', linestyle='--', alpha=0.7, label='Good Quality')
        ax.axhline(0.6, color='orange', linestyle='--', alpha=0.7, label='Fair Quality')
        
        ax.set_title('Reverse Diffusion Quality')
        ax.set_xlabel('Reverse Step')
        ax.set_ylabel('Quality Score')
        ax.legend()
        ax.grid(True, alpha=0.3)
        ax.set_ylim(0, 1)
    
    def _plot_conceptual_landscape(self, ax: plt.Axes, landscape_data: Dict[str, Any]) -> None:
        """Plot conceptual landscape as contour/surface plot."""
        concept_grid = landscape_data.get('concept_grid', None)
        energy_surface = landscape_data.get('energy_surface', None)
        
        if concept_grid is None or energy_surface is None:
            ax.text(0.5, 0.5, 'No landscape data available', 
                   ha='center', va='center', transform=ax.transAxes)
            return
        
        # Create contour plot of energy surface
        X, Y = concept_grid
        contour = ax.contour(X, Y, energy_surface, levels=20, colors='black', alpha=0.6, linewidths=0.5)
        contourf = ax.contourf(X, Y, energy_surface, levels=20, cmap='viridis', alpha=0.8)
        
        # Add colorbar
        cbar = plt.colorbar(contourf, ax=ax)
        cbar.set_label('Energy')
        
        # Add critical points if available
        minima = landscape_data.get('energy_minima', [])
        maxima = landscape_data.get('energy_maxima', [])
        saddle_points = landscape_data.get('saddle_points', [])
        
        for minimum in minima:
            ax.plot(minimum[0], minimum[1], 'ro', markersize=8, label='Minima')
        
        for maximum in maxima:
            ax.plot(maximum[0], maximum[1], 'r^', markersize=8, label='Maxima')
        
        for saddle in saddle_points:
            ax.plot(saddle[0], saddle[1], 'rs', markersize=8, label='Saddle Points')
        
        ax.set_title('Conceptual Energy Landscape')
        ax.set_xlabel('Concept Dimension 1')
        ax.set_ylabel('Concept Dimension 2')
        
        # Remove duplicate legend entries
        handles, labels = ax.get_legend_handles_labels()
        by_label = dict(zip(labels, handles))
        if by_label:
            ax.legend(by_label.values(), by_label.keys())
    
    def _plot_guidance_analysis(self, ax: plt.Axes, guidance_data: Dict[str, Any]) -> None:
        """Plot guidance effectiveness analysis."""
        guidance_scales = guidance_data.get('guidance_scales', [])
        guidance_effectiveness = guidance_data.get('guidance_effectiveness', [])
        
        if not guidance_scales:
            ax.text(0.5, 0.5, 'No guidance data', ha='center', va='center', transform=ax.transAxes)
            return
        
        # Plot guidance effectiveness vs scale
        ax.plot(guidance_scales, guidance_effectiveness, 'blue', marker='o', 
               linewidth=2, markersize=6)
        
        # Highlight optimal guidance scale
        if guidance_effectiveness:
            optimal_idx = np.argmax(guidance_effectiveness)
            optimal_scale = guidance_scales[optimal_idx]
            optimal_effectiveness = guidance_effectiveness[optimal_idx]
            
            ax.plot(optimal_scale, optimal_effectiveness, 'red', marker='*', 
                   markersize=12, label=f'Optimal: {optimal_scale:.2f}')
        
        ax.set_title('Guidance Scale Analysis')
        ax.set_xlabel('Guidance Scale')
        ax.set_ylabel('Effectiveness')
        ax.legend()
        ax.grid(True, alpha=0.3)
    
    def _plot_uncertainty_quantification(self, ax: plt.Axes, uncertainty_data: Dict[str, Any]) -> None:
        """Plot uncertainty quantification metrics."""
        epistemic_uncertainty = uncertainty_data.get('epistemic_uncertainty', [])
        aleatoric_uncertainty = uncertainty_data.get('aleatoric_uncertainty', [])
        
        if not epistemic_uncertainty and not aleatoric_uncertainty:
            ax.text(0.5, 0.5, 'No uncertainty data', ha='center', va='center', transform=ax.transAxes)
            return
        
        steps = np.arange(max(len(epistemic_uncertainty), len(aleatoric_uncertainty)))
        
        # Plot different types of uncertainty
        if epistemic_uncertainty:
            ax.plot(steps[:len(epistemic_uncertainty)], epistemic_uncertainty, 
                   'blue', linewidth=2, label='Epistemic')
        
        if aleatoric_uncertainty:
            ax.plot(steps[:len(aleatoric_uncertainty)], aleatoric_uncertainty, 
                   'red', linewidth=2, label='Aleatoric')
        
        # Calculate total uncertainty if both are available
        if epistemic_uncertainty and aleatoric_uncertainty and len(epistemic_uncertainty) == len(aleatoric_uncertainty):
            total_uncertainty = np.sqrt(np.array(epistemic_uncertainty)**2 + np.array(aleatoric_uncertainty)**2)
            ax.plot(steps[:len(total_uncertainty)], total_uncertainty, 
                   'green', linewidth=2, linestyle='--', label='Total')
        
        ax.set_title('Uncertainty Quantification')
        ax.set_xlabel('Diffusion Step')
        ax.set_ylabel('Uncertainty')
        ax.legend()
        ax.grid(True, alpha=0.3)
    
    def _plot_diffusion_metrics(self, ax: plt.Axes, data: Dict[str, Any]) -> None:
        """Plot comprehensive diffusion process metrics."""
        # Collect metrics from various data sources
        metrics = {}
        
        # Convergence metrics
        convergence_data = data.get('convergence_data', {})
        if convergence_data:
            conv_history = convergence_data.get('convergence_history', [])
            if conv_history:
                metrics['Final Convergence'] = conv_history[-1]
                metrics['Convergence Rate'] = self._estimate_convergence_rate(conv_history)
        
        # Trajectory metrics
        trajectory_data = data.get('diffusion_trajectory', [])
        if trajectory_data:
            metrics['Trajectory Length'] = len(trajectory_data)
            
            # Calculate trajectory smoothness
            if len(trajectory_data) > 2:
                states = np.array([step.get('concept_state', np.zeros(10)) for step in trajectory_data])
                if states.size > 0:
                    diffs = np.diff(states, axis=0)
                    smoothness = 1.0 / (1.0 + np.mean(np.linalg.norm(diffs, axis=1)))
                    metrics['Trajectory Smoothness'] = smoothness
        
        # Noise schedule metrics
        noise_data = data.get('noise_schedule', {})
        if noise_data:
            metrics['Schedule Type'] = noise_data.get('schedule_type', 'unknown')
            metrics['Num Timesteps'] = noise_data.get('num_timesteps', 0)
        
        # Uncertainty metrics
        uncertainty_data = data.get('uncertainty_data', {})
        if uncertainty_data:
            epistemic = uncertainty_data.get('epistemic_uncertainty', [])
            aleatoric = uncertainty_data.get('aleatoric_uncertainty', [])
            
            if epistemic:
                metrics['Avg Epistemic Uncertainty'] = np.mean(epistemic)
            if aleatoric:
                metrics['Avg Aleatoric Uncertainty'] = np.mean(aleatoric)
        
        # Create horizontal bar chart for numerical metrics
        numerical_metrics = {k: v for k, v in metrics.items() if isinstance(v, (int, float))}
        string_metrics = {k: v for k, v in metrics.items() if isinstance(v, str)}
        
        if numerical_metrics:
            metric_names = list(numerical_metrics.keys())
            metric_values = list(numerical_metrics.values())
            
            y_pos = np.arange(len(metric_names))
            bars = ax.barh(y_pos, metric_values, alpha=0.7, color='skyblue')
            
            # Add value labels
            for i, (bar, value) in enumerate(zip(bars, metric_values)):
                width = bar.get_width()
                ax.text(width, bar.get_y() + bar.get_height()/2,
                       f'{value:.4f}' if isinstance(value, float) else str(value),
                       ha='left', va='center', fontsize=9)
            
            ax.set_yticks(y_pos)
            ax.set_yticklabels(metric_names)
            ax.set_xlabel('Value')
            ax.set_title('Diffusion Process Metrics')
            ax.grid(True, alpha=0.3, axis='x')
            
            # Add string metrics as text
            if string_metrics:
                text_str = ', '.join([f'{k}: {v}' for k, v in string_metrics.items()])
                ax.text(0.02, 0.98, text_str, transform=ax.transAxes, 
                       fontsize=9, verticalalignment='top',
                       bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.5))
        else:
            ax.text(0.5, 0.5, 'No metrics available', 
                   ha='center', va='center', transform=ax.transAxes)
    
    def _estimate_convergence_rate(self, convergence_history: List[float]) -> float:
        """Estimate convergence rate from history."""
        if len(convergence_history) < 5:
            return 0.0
        
        try:
            # Fit exponential decay: conv(t) = A * exp(-r * t)
            x = np.arange(len(convergence_history))
            y = np.maximum(convergence_history, 1e-16)
            log_y = np.log(y)
            
            # Linear fit in log space
            slope, _ = np.polyfit(x, log_y, 1)
            return float(-slope)  # Convergence rate
        except:
            return 0.0
    
    def update_visualization(self, new_data: Dict[str, Any]) -> None:
        """Update diffusion visualization with new data."""
        with self.update_lock:
            # Update diffusion trajectories
            process_id = new_data.get('process_id', 'default')
            trajectory_step = new_data.get('trajectory_step', None)
            
            if trajectory_step is not None:
                self.diffusion_trajectories[process_id].append(trajectory_step)
                
                # Limit trajectory length
                max_length = ULTRA_VIZ_CONSTANTS['DIFFUSION_TRAIL_LENGTH']
                if len(self.diffusion_trajectories[process_id]) > max_length:
                    self.diffusion_trajectories[process_id] = self.diffusion_trajectories[process_id][-max_length:]
            
            # Update convergence history
            convergence_metric = new_data.get('convergence_metric', None)
            if convergence_metric is not None:
                self.convergence_history[process_id].append(convergence_metric)
                
                # Limit history length
                if len(self.convergence_history[process_id]) > 1000:
                    self.convergence_history[process_id] = self.convergence_history[process_id][-1000:]
            
            self.last_update_time = time.time()

# =============================================================================
# Consciousness and Meta-Cognitive Visualization
# =============================================================================

class ConsciousnessMetricsVisualizer(BaseVisualizer):
    """Comprehensive visualizer for consciousness metrics and IIT analysis."""
    
    def __init__(self, config: VisualizationConfig, logger: Optional[logging.Logger] = None):
        super().__init__(config, logger)
        self.phi_history = deque(maxlen=1000)
        self.integration_history = deque(maxlen=1000)
        self.workspace_history = deque(maxlen=500)
        
    def create_visualization(self, data: Dict[str, Any], **kwargs) -> plt.Figure:
        """Create comprehensive consciousness metrics visualization."""
        fig = plt.figure(figsize=(20, 16))
        
        # Create complex subplot layout
        gs = fig.add_gridspec(4, 4, height_ratios=[1.2, 1, 1, 0.8])
        
        # Phi (Φ) evolution and distribution
        ax_phi = fig.add_subplot(gs[0, :2])
        self._plot_phi_evolution(ax_phi, data.get('phi_data', {}))
        
        # Integration matrix visualization
        ax_integration = fig.add_subplot(gs[0, 2:])
        self._plot_integration_matrix(ax_integration, data.get('integration_data', {}))
        
        # Global workspace dynamics
        ax_workspace = fig.add_subplot(gs[1, :2])
        self._plot_global_workspace(ax_workspace, data.get('workspace_data', {}))
        
        # Consciousness state transitions
        ax_states = fig.add_subplot(gs[1, 2])
        self._plot_consciousness_states(ax_states, data.get('state_data', {}))
        
        # Attention and awareness
        ax_attention = fig.add_subplot(gs[1, 3])
        self._plot_attentional_awareness(ax_attention, data.get('attention_data', {}))
        
        # Information geometry
        ax_geometry = fig.add_subplot(gs[2, :2])
        self._plot_information_geometry(ax_geometry, data.get('geometry_data', {}))
        
        # Emergence indicators
        ax_emergence = fig.add_subplot(gs[2, 2])
        self._plot_emergence_indicators(ax_emergence, data.get('emergence_data', {}))
        
        # Causal structure
        ax_causal = fig.add_subplot(gs[2, 3])
        self._plot_causal_structure(ax_causal, data.get('causal_data', {}))
        
        # Summary consciousness metrics
        ax_summary = fig.add_subplot(gs[3, :])
        self._plot_consciousness_summary(ax_summary, data)
        
        plt.tight_layout()
        return fig
    
    def _plot_phi_evolution(self, ax: plt.Axes, phi_data: Dict[str, Any]) -> None:
        """Plot evolution of Φ (phi) values over time."""
        phi_values = phi_data.get('phi_history', [])
        timestamps = phi_data.get('timestamps', [])
        network_sizes = phi_data.get('network_sizes', [])
        
        if not phi_values:
            ax.text(0.5, 0.5, 'No Φ data available', 
                   ha='center', va='center', transform=ax.transAxes)
            return
        
        # Use indices if no timestamps provided
        if not timestamps:
            timestamps = np.arange(len(phi_values))
        
        # Plot Φ evolution
        ax.plot(timestamps, phi_values, 'purple', linewidth=2, label='Φ (phi)')
        
        # Add consciousness threshold
        phi_threshold = ULTRA_VIZ_CONSTANTS['PHI_COLOR_RANGE'][0]
        ax.axhline(phi_threshold, color='red', linestyle='--', alpha=0.7,
                  label=f'Consciousness Threshold: {phi_threshold}')
        
        # Color background based on consciousness levels
        if len(phi_values) > 1:
            phi_array = np.array(phi_values)
            
            # Define consciousness levels
            minimal = phi_array < 0.01
            low = (phi_array >= 0.01) & (phi_array < 0.1)
            moderate = (phi_array >= 0.1) & (phi_array < 1.0)
            high = phi_array >= 1.0
            
            # Color regions
            ax.fill_between(timestamps, 0, np.max(phi_values)*1.1, 
                           where=minimal, alpha=0.2, color='gray', label='Minimal')
            ax.fill_between(timestamps, 0, np.max(phi_values)*1.1, 
                           where=low, alpha=0.2, color='yellow', label='Low')
            ax.fill_between(timestamps, 0, np.max(phi_values)*1.1, 
                           where=moderate, alpha=0.2, color='orange', label='Moderate')
            ax.fill_between(timestamps, 0, np.max(phi_values)*1.1, 
                           where=high, alpha=0.2, color='red', label='High')
        
        # Plot network size on secondary axis if available
        if network_sizes and len(network_sizes) == len(phi_values):
            ax2 = ax.twinx()
            ax2.plot(timestamps, network_sizes, 'blue', alpha=0.7, 
                    linewidth=1.5, label='Network Size')
            ax2.set_ylabel('Network Size', color='blue')
            ax2.tick_params(axis='y', labelcolor='blue')
            ax2.legend(loc='upper right')
        
        ax.set_title('Φ (Integrated Information) Evolution')
        ax.set_xlabel('Time')
        ax.set_ylabel('Φ Value')
        ax.legend(loc='upper left')
        ax.grid(True, alpha=0.3)
    
    def _plot_integration_matrix(self, ax: plt.Axes, integration_data: Dict[str, Any]) -> None:
        """Plot information integration matrix."""
        integration_matrix = integration_data.get('integration_matrix', None)
        node_labels = integration_data.get('node_labels', [])
        
        if integration_matrix is None:
            ax.text(0.5, 0.5, 'No integration matrix available', 
                   ha='center', va='center', transform=ax.transAxes)
            return
        
        # Create heatmap
        im = ax.imshow(integration_matrix, cmap='inferno', aspect='auto')
        
        # Add colorbar
        cbar = plt.colorbar(im, ax=ax)
        cbar.set_label('Integration Strength')
        
        # Add node labels if available
        if node_labels and len(node_labels) == integration_matrix.shape[0]:
            ax.set_xticks(range(len(node_labels)))
            ax.set_yticks(range(len(node_labels)))
            ax.set_xticklabels(node_labels, rotation=45, ha='right')
            ax.set_yticklabels(node_labels)
        
        # Highlight strong integration connections
        threshold = np.percentile(integration_matrix, 90)
        strong_connections = np.where(integration_matrix > threshold)
        
        for i, j in zip(strong_connections[0], strong_connections[1]):
            if i != j:  # Skip diagonal
                circle = Circle((j, i), 0.3, fill=False, color='white', linewidth=2)
                ax.add_patch(circle)
        
        ax.set_title('Information Integration Matrix')
        ax.set_xlabel('Node')
        ax.set_ylabel('Node')
    
    def _plot_global_workspace(self, ax: plt.Axes, workspace_data: Dict[str, Any]) -> None:
        """Plot global workspace competition and broadcast dynamics."""
        contents = workspace_data.get('contents', [])
        competition_scores = workspace_data.get('competition_scores', [])
        broadcast_strength = workspace_data.get('broadcast_strength', 0)
        access_history = workspace_data.get('access_history', [])
        
        if not contents and not access_history:
            ax.text(0.5, 0.5, 'No workspace data available', 
                   ha='center', va='center', transform=ax.transAxes)
            return
        
        # Plot current workspace contents if available
        if contents and competition_scores:
            # Create bar plot of competition scores
            y_pos = np.arange(len(contents))
            bars = ax.barh(y_pos, competition_scores, alpha=0.7)
            
            # Color bars based on competition strength
            norm = plt.Normalize(vmin=min(competition_scores), vmax=max(competition_scores))
            colors = plt.cm.viridis(norm(competition_scores))
            
            for bar, color in zip(bars, colors):
                bar.set_color(color)
            
            # Highlight winner (highest score)
            winner_idx = np.argmax(competition_scores)
            bars[winner_idx].set_edgecolor('red')
            bars[winner_idx].set_linewidth(3)
            
            ax.set_yticks(y_pos)
            ax.set_yticklabels([f'Content {i+1}' for i in range(len(contents))])
            ax.set_xlabel('Competition Score')
            ax.set_title(f'Global Workspace Competition (Broadcast: {broadcast_strength:.3f})')
            
            # Add broadcast strength indicator
            ax.axvline(broadcast_strength * max(competition_scores), 
                      color='red', linestyle='--', alpha=0.7,
                      label=f'Broadcast Threshold')
            ax.legend()
            
        # Plot access history over time
        elif access_history:
            timestamps = [entry.get('timestamp', i) for i, entry in enumerate(access_history)]
            access_counts = [entry.get('access_count', 0) for entry in access_history]
            
            ax.plot(timestamps, access_counts, 'blue', linewidth=2)
            ax.set_title('Workspace Access History')
            ax.set_xlabel('Time')
            ax.set_ylabel('Access Count')
            ax.grid(True, alpha=0.3)
        
        else:
            ax.text(0.5, 0.5, 'No valid workspace data', 
                   ha='center', va='center', transform=ax.transAxes)
    
    def _plot_consciousness_states(self, ax: plt.Axes, state_data: Dict[str, Any]) -> None:
        """Plot consciousness state transitions."""
        state_history = state_data.get('state_history', [])
        transition_matrix = state_data.get('transition_matrix', None)
        
        if not state_history and transition_matrix is None:
            ax.text(0.5, 0.5, 'No state data', ha='center', va='center', transform=ax.transAxes)
            return
        
        # If we have transition matrix, plot it as heatmap
        if transition_matrix is not None:
            states = ['Minimal', 'Low', 'Moderate', 'High']
            im = ax.imshow(transition_matrix, cmap='Blues', aspect='auto')
            
            # Add text annotations
            for i in range(len(states)):
                for j in range(len(states)):
                    text = ax.text(j, i, f'{transition_matrix[i, j]:.3f}',
                                 ha="center", va="center", color="black")
            
            ax.set_xticks(range(len(states)))
            ax.set_yticks(range(len(states)))
            ax.set_xticklabels(states)
            ax.set_yticklabels(states)
            ax.set_xlabel('To State')
            ax.set_ylabel('From State')
            ax.set_title('State Transition Probabilities')
            
            # Add colorbar
            cbar = plt.colorbar(im, ax=ax)
            cbar.set_label('Transition Probability')
        
        # Otherwise plot state history as timeline
        elif state_history:
            timestamps = [entry.get('timestamp', i) for i, entry in enumerate(state_history)]
            states = [entry.get('state', 0) for entry in state_history]
            
            # Convert state names to numbers if needed
            state_mapping = {'minimal': 0, 'low': 1, 'moderate': 2, 'high': 3}
            numeric_states = []
            for state in states:
                if isinstance(state, str):
                    numeric_states.append(state_mapping.get(state.lower(), 0))
                else:
                    numeric_states.append(state)
            
            ax.plot(timestamps, numeric_states, 'purple', linewidth=2, marker='o')
            ax.set_yticks([0, 1, 2, 3])
            ax.set_yticklabels(['Minimal', 'Low', 'Moderate', 'High'])
            ax.set_title('Consciousness State Timeline')
            ax.set_xlabel('Time')
            ax.set_ylabel('Consciousness State')
            ax.grid(True, alpha=0.3)
    
    def _plot_attentional_awareness(self, ax: plt.Axes, attention_data: Dict[str, Any]) -> None:
        """Plot attentional awareness metrics."""
        salience_map = attention_data.get('salience_map', None)
        attention_allocation = attention_data.get('attention_allocation', [])
        focus_intensity = attention_data.get('focus_intensity', 0)
        
        if salience_map is None and not attention_allocation:
            ax.text(0.5, 0.5, 'No attention data', ha='center', va='center', transform=ax.transAxes)
            return
        
        # Plot salience map if available
        if salience_map is not None:
            im = ax.imshow(salience_map, cmap='hot', aspect='auto')
            cbar = plt.colorbar(im, ax=ax)
            cbar.set_label('Salience')
            ax.set_title(f'Attention Salience Map (Focus: {focus_intensity:.3f})')
        
        # Plot attention allocation over time
        elif attention_allocation:
            timestamps = np.arange(len(attention_allocation))
            ax.plot(timestamps, attention_allocation, 'red', linewidth=2)
            
            # Add focus intensity as horizontal line
            ax.axhline(focus_intensity, color='blue', linestyle='--', 
                      label=f'Focus Intensity: {focus_intensity:.3f}')
            
            ax.set_title('Attention Allocation Over Time')
            ax.set_xlabel('Time')
            ax.set_ylabel('Attention Strength')
            ax.legend()
            ax.grid(True, alpha=0.3)
    
    def _plot_information_geometry(self, ax: plt.Axes, geometry_data: Dict[str, Any]) -> None:
        """Plot information geometry of consciousness space."""
        information_manifold = geometry_data.get('information_manifold', None)
        geodesics = geometry_data.get('geodesics', [])
        curvature_map = geometry_data.get('curvature_map', None)
        
        if information_manifold is None:
            ax.text(0.5, 0.5, 'No geometry data', ha='center', va='center', transform=ax.transAxes)
            return
        
        # Plot information manifold (2D projection)
        if information_manifold.shape[1] >= 2:
            x, y = information_manifold[:, 0], information_manifold[:, 1]
            
            # Color points by curvature if available
            if curvature_map is not None and len(curvature_map) == len(x):
                scatter = ax.scatter(x, y, c=curvature_map, cmap='RdYlBu_r', 
                                   s=50, alpha=0.7, edgecolors='black', linewidth=0.5)
                cbar = plt.colorbar(scatter, ax=ax)
                cbar.set_label('Curvature')
            else:
                ax.scatter(x, y, c='blue', s=50, alpha=0.7, edgecolors='black', linewidth=0.5)
            
            # Plot geodesics
            for geodesic in geodesics:
                if len(geodesic) >= 2:
                    geodesic_array = np.array(geodesic)
                    ax.plot(geodesic_array[:, 0], geodesic_array[:, 1], 
                           'red', linewidth=2, alpha=0.7)
            
            ax.set_title('Information Geometry of Consciousness')
            ax.set_xlabel('Information Dimension 1')
            ax.set_ylabel('Information Dimension 2')
            ax.grid(True, alpha=0.3)
        else:
            ax.text(0.5, 0.5, 'Insufficient dimensions for geometry plot', 
                   ha='center', va='center', transform=ax.transAxes)
    
    def _plot_emergence_indicators(self, ax: plt.Axes, emergence_data: Dict[str, Any]) -> None:
        """Plot consciousness emergence indicators."""
        emergence_score = emergence_data.get('emergence_score', 0)
        phi_increases = emergence_data.get('phi_increases', [])
        coherence_measures = emergence_data.get('coherence_measures', [])
        
        # Create gauge plot for emergence score
        theta = np.linspace(0, np.pi, 100)
        
        # Background arc
        ax.plot(np.cos(theta), np.sin(theta), 'lightgray', linewidth=8)
        
        # Emergence score arc
        score_theta = theta[:int(emergence_score * len(theta))]
        ax.plot(np.cos(score_theta), np.sin(score_theta), 'red', linewidth=8)
        
        # Add needle
        needle_angle = emergence_score * np.pi
        ax.arrow(0, 0, 0.8 * np.cos(needle_angle), 0.8 * np.sin(needle_angle),
                head_width=0.05, head_length=0.05, fc='black', ec='black')
        
        # Add labels
        ax.text(0, -0.3, f'Emergence Score\n{emergence_score:.3f}', 
               ha='center', va='center', fontsize=12, fontweight='bold')
        
        # Add threshold markers
        threshold_angles = [0.3 * np.pi, 0.7 * np.pi]
        threshold_labels = ['Weak', 'Strong']
        
        for angle, label in zip(threshold_angles, threshold_labels):
            x, y = 1.1 * np.cos(angle), 1.1 * np.sin(angle)
            ax.plot([0.9 * np.cos(angle), 1.1 * np.cos(angle)], 
                   [0.9 * np.sin(angle), 1.1 * np.sin(angle)], 'black', linewidth=2)
            ax.text(x, y, label, ha='center', va='center', fontsize=10)
        
        ax.set_xlim(-1.3, 1.3)
        ax.set_ylim(-0.5, 1.3)
        ax.set_aspect('equal')
        ax.axis('off')
        ax.set_title('Consciousness Emergence')
    
    def _plot_causal_structure(self, ax: plt.Axes, causal_data: Dict[str, Any]) -> None:
        """Plot causal structure of consciousness."""
        causal_graph = causal_data.get('causal_graph', None)
        causal_strengths = causal_data.get('causal_strengths', {})
        
        if causal_graph is None:
            ax.text(0.5, 0.5, 'No causal data', ha='center', va='center', transform=ax.transAxes)
            return
        
        # Create NetworkX graph
        G = nx.DiGraph()
        
        # Add nodes and edges
        for source, targets in causal_graph.items():
            for target in targets:
                strength = causal_strengths.get(f'{source}_{target}', 1.0)
                G.add_edge(source, target, weight=strength)
        
        # Generate layout
        pos = nx.spring_layout(G, k=3, iterations=50)
        
        # Draw nodes
        node_sizes = [300 for _ in G.nodes()]
        nx.draw_networkx_nodes(G, pos, node_size=node_sizes, 
                              node_color='lightblue', alpha=0.7, ax=ax)
        
        # Draw edges with thickness based on causal strength
        edges = G.edges()
        weights = [G[u][v]['weight'] for u, v in edges]
        
        # Normalize weights for edge thickness
        if weights:
            max_weight = max(weights)
            edge_widths = [w / max_weight * 5 for w in weights]
            
            nx.draw_networkx_edges(G, pos, width=edge_widths, 
                                  edge_color='gray', alpha=0.6, 
                                  arrows=True, arrowsize=20, ax=ax)
        
        # Draw labels
        nx.draw_networkx_labels(G, pos, font_size=10, ax=ax)
        
        ax.set_title('Causal Structure')
        ax.axis('off')
    
    def _plot_consciousness_summary(self, ax: plt.Axes, data: Dict[str, Any]) -> None:
        """Plot summary of consciousness metrics."""
        # Collect metrics from all data sources
        metrics = {}
        
        # Phi metrics
        phi_data = data.get('phi_data', {})
        phi_values = phi_data.get('phi_history', [])
        if phi_values:
            metrics['Current Φ'] = phi_values[-1]
            metrics['Max Φ'] = max(phi_values)
            metrics['Avg Φ'] = np.mean(phi_values)
        
        # Integration metrics
        integration_data = data.get('integration_data', {})
        integration_matrix = integration_data.get('integration_matrix', None)
        if integration_matrix is not None:
            metrics['Integration Density'] = np.mean(integration_matrix)
            metrics['Max Integration'] = np.max(integration_matrix)
        
        # Workspace metrics
        workspace_data = data.get('workspace_data', {})
        if workspace_data:
            competition_scores = workspace_data.get('competition_scores', [])
            if competition_scores:
                metrics['Workspace Competition'] = max(competition_scores)
            
            broadcast_strength = workspace_data.get('broadcast_strength', 0)
            metrics['Broadcast Strength'] = broadcast_strength
        
        # Emergence metrics
        emergence_data = data.get('emergence_data', {})
        if emergence_data:
            emergence_score = emergence_data.get('emergence_score', 0)
            metrics['Emergence Score'] = emergence_score
        
        # Create radar chart
        if metrics:
            metric_names = list(metrics.keys())
            metric_values = list(metrics.values())
            
            # Normalize values to 0-1 range for radar chart
            max_values = {
                'Current Φ': 5.0, 'Max Φ': 5.0, 'Avg Φ': 5.0,
                'Integration Density': 1.0, 'Max Integration': 1.0,
                'Workspace Competition': 1.0, 'Broadcast Strength': 1.0,
                'Emergence Score': 1.0
            }
            
            normalized_values = []
            for name, value in zip(metric_names, metric_values):
                max_val = max_values.get(name, max(metric_values))
                normalized_values.append(min(value / max_val, 1.0))
            
            # Create radar chart
            angles = np.linspace(0, 2*np.pi, len(metric_names), endpoint=False)
            
            # Close the polygon
            normalized_values += [normalized_values[0]]
            angles = np.concatenate((angles, [angles[0]]))
            
            ax.plot(angles, normalized_values, 'o-', linewidth=2, color='purple')
            ax.fill(angles, normalized_values, alpha=0.25, color='purple')
            
            # Add labels
            ax.set_xticks(angles[:-1])
            ax.set_xticklabels(metric_names, fontsize=10)
            ax.set_ylim(0, 1)
            ax.set_title('Consciousness Metrics Summary', fontsize=14, fontweight='bold')
            ax.grid(True, alpha=0.3)
            
            # Add value annotations
            for angle, value, name in zip(angles[:-1], normalized_values[:-1], metric_names):
                original_value = metrics[name]
                ax.text(angle, value + 0.1, f'{original_value:.3f}', 
                       ha='center', va='center', fontsize=8)
        else:
            ax.text(0.5, 0.5, 'No consciousness metrics available', 
                   ha='center', va='center', transform=ax.transAxes)
    
    def update_visualization(self, new_data: Dict[str, Any]) -> None:
        """Update consciousness visualization with new data."""
        with self.update_lock:
            # Update Φ history
            phi_value = new_data.get('phi_value', None)
            if phi_value is not None:
                self.phi_history.append(phi_value)
            
            # Update integration history
            integration_measure = new_data.get('integration_measure', None)
            if integration_measure is not None:
                self.integration_history.append(integration_measure)
            
            # Update workspace history
            workspace_data = new_data.get('workspace_data', None)
            if workspace_data is not None:
                workspace_entry = {
                    'timestamp': time.time(),
                    'data': workspace_data
                }
                self.workspace_history.append(workspace_entry)
            
            self.last_update_time = time.time()

# =============================================================================
# Performance and System Monitoring Visualization
# =============================================================================

class PerformanceDashboardVisualizer(BaseVisualizer):
    """Real-time performance monitoring dashboard."""
    
    def __init__(self, config: VisualizationConfig, logger: Optional[logging.Logger] = None):
        super().__init__(config, logger)
        self.performance_buffer = defaultdict(lambda: deque(maxlen=1000))
        self.alert_history = deque(maxlen=100)
        self.system_stats = {}
        
    def create_visualization(self, data: Dict[str, Any], **kwargs) -> plt.Figure:
        """Create comprehensive performance dashboard."""
        fig = plt.figure(figsize=(20, 16))
        
        # Create dashboard layout
        gs = fig.add_gridspec(4, 4, height_ratios=[1, 1, 1, 0.8])
        
        # System resource utilization
        ax_resources = fig.add_subplot(gs[0, :2])
        self._plot_system_resources(ax_resources, data.get('system_metrics', {}))
        
        # Neural network performance
        ax_neural = fig.add_subplot(gs[0, 2:])
        self._plot_neural_performance(ax_neural, data.get('neural_metrics', {}))
        
        # Memory usage breakdown
        ax_memory = fig.add_subplot(gs[1, :2])
        self._plot_memory_usage(ax_memory, data.get('memory_metrics', {}))
        
        # Processing latency
        ax_latency = fig.add_subplot(gs[1, 2])
        self._plot_processing_latency(ax_latency, data.get('latency_metrics', {}))
        
        # Throughput metrics
        ax_throughput = fig.add_subplot(gs[1, 3])
        self._plot_throughput_metrics(ax_throughput, data.get('throughput_metrics', {}))
        
        # Error rates and alerts
        ax_errors = fig.add_subplot(gs[2, :2])
        self._plot_error_rates(ax_errors, data.get('error_metrics', {}))
        
        # Component health status
        ax_health = fig.add_subplot(gs[2, 2])
        self._plot_component_health(ax_health, data.get('health_metrics', {}))
        
        # Network activity
        ax_network = fig.add_subplot(gs[2, 3])
        self._plot_network_activity(ax_network, data.get('network_metrics', {}))
        
        # System summary
        ax_summary = fig.add_subplot(gs[3, :])
        self._plot_system_summary(ax_summary, data)
        
        plt.tight_layout()
        return fig
    
    def _plot_system_resources(self, ax: plt.Axes, metrics: Dict[str, Any]) -> None:
        """Plot system resource utilization."""
        cpu_usage = metrics.get('cpu_usage_history', [])
        memory_usage = metrics.get('memory_usage_history', [])
        gpu_usage = metrics.get('gpu_usage_history', [])
        
        if not any([cpu_usage, memory_usage, gpu_usage]):
            ax.text(0.5, 0.5, 'No system metrics available', 
                   ha='center', va='center', transform=ax.transAxes)
            return
        
        # Create time axis
        max_length = max(len(cpu_usage), len(memory_usage), len(gpu_usage))
        time_axis = np.arange(max_length)
        
        # Plot CPU usage
        if cpu_usage:
            ax.plot(time_axis[:len(cpu_usage)], cpu_usage, 'blue', 
                   linewidth=2, label='CPU Usage')
        
        # Plot memory usage
        if memory_usage:
            ax.plot(time_axis[:len(memory_usage)], memory_usage, 'green', 
                   linewidth=2, label='Memory Usage')
        
        # Plot GPU usage
        if gpu_usage:
            ax.plot(time_axis[:len(gpu_usage)], gpu_usage, 'red', 
                   linewidth=2, label='GPU Usage')
        
        # Add warning thresholds
        ax.axhline(80, color='orange', linestyle='--', alpha=0.7, label='Warning (80%)')
        ax.axhline(90, color='red', linestyle='--', alpha=0.7, label='Critical (90%)')
        
        ax.set_title('System Resource Utilization')
        ax.set_xlabel('Time')
        ax.set_ylabel('Usage (%)')
        ax.set_ylim(0, 100)
        ax.legend()
        ax.grid(True, alpha=0.3)
    
    def _plot_neural_performance(self, ax: plt.Axes, metrics: Dict[str, Any]) -> None:
        """Plot neural network performance metrics."""
        spike_rates = metrics.get('spike_rates', [])
        firing_rates = metrics.get('firing_rates', [])
        connection_weights = metrics.get('avg_connection_weights', [])
        
        if not any([spike_rates, firing_rates, connection_weights]):
            ax.text(0.5, 0.5, 'No neural metrics available', 
                   ha='center', va='center', transform=ax.transAxes)
            return
        
        # Plot spike rates
        if spike_rates:
            time_axis = np.arange(len(spike_rates))
            ax.plot(time_axis, spike_rates, 'purple', linewidth=2, label='Spike Rate')
        
        # Plot firing rates on secondary axis
        if firing_rates:
            ax2 = ax.twinx()
            time_axis = np.arange(len(firing_rates))
            ax2.plot(time_axis, firing_rates, 'orange', linewidth=2, label='Firing Rate')
            ax2.set_ylabel('Firing Rate (Hz)', color='orange')
            ax2.tick_params(axis='y', labelcolor='orange')
            ax2.legend(loc='upper right')
        
        # Plot connection weights on third axis if available
        if connection_weights:
            ax3 = ax.twinx()
            ax3.spines['right'].set_position(('outward', 60))
            time_axis = np.arange(len(connection_weights))
            ax3.plot(time_axis, connection_weights, 'green', linewidth=2, 
                    alpha=0.7, label='Avg Weight')
            ax3.set_ylabel('Avg Connection Weight', color='green')
            ax3.tick_params(axis='y', labelcolor='green')
        
        ax.set_title('Neural Network Performance')
        ax.set_xlabel('Time')
        ax.set_ylabel('Spike Rate (spikes/s)', color='purple')
        ax.tick_params(axis='y', labelcolor='purple')
        ax.legend(loc='upper left')
        ax.grid(True, alpha=0.3)
    
    def _plot_memory_usage(self, ax: plt.Axes, metrics: Dict[str, Any]) -> None:
        """Plot detailed memory usage breakdown."""
        memory_breakdown = metrics.get('memory_breakdown', {})
        memory_timeline = metrics.get('memory_timeline', [])
        
        if not memory_breakdown and not memory_timeline:
            ax.text(0.5, 0.5, 'No memory metrics available', 
                   ha='center', va='center', transform=ax.transAxes)
            return
        
        # Plot memory breakdown as pie chart if available
        if memory_breakdown:
            labels = list(memory_breakdown.keys())
            sizes = list(memory_breakdown.values())
            colors = plt.cm.Set3(np.linspace(0, 1, len(labels)))
            
            wedges, texts, autotexts = ax.pie(sizes, labels=labels, colors=colors, 
                                             autopct='%1.1f%%', startangle=90)
            
            # Enhance text readability
            for autotext in autotexts:
                autotext.set_color('white')
                autotext.set_fontweight('bold')
            
            ax.set_title('Memory Usage Breakdown')
        
        # Plot memory timeline if breakdown not available
        elif memory_timeline:
            time_axis = np.arange(len(memory_timeline))
            ax.plot(time_axis, memory_timeline, 'blue', linewidth=2)
            
            # Add memory limit if available
            memory_limit = metrics.get('memory_limit', None)
            if memory_limit:
                ax.axhline(memory_limit, color='red', linestyle='--', 
                          alpha=0.7, label=f'Limit: {memory_limit:.1f} GB')
                ax.legend()
            
            ax.set_title('Memory Usage Timeline')
            ax.set_xlabel('Time')
            ax.set_ylabel('Memory Usage (GB)')
            ax.grid(True, alpha=0.3)
    
    def _plot_processing_latency(self, ax: plt.Axes, metrics: Dict[str, Any]) -> None:
        """Plot processing latency distribution."""
        latencies = metrics.get('latency_distribution', [])
        avg_latency = metrics.get('avg_latency', 0)
        p95_latency = metrics.get('p95_latency', 0)
        
        if not latencies:
            ax.text(0.5, 0.5, 'No latency data', ha='center', va='center', transform=ax.transAxes)
            return
        
        # Create histogram
        ax.hist(latencies, bins=30, alpha=0.7, color='skyblue', density=True)
        
        # Add statistical markers
        ax.axvline(avg_latency, color='red', linestyle='--', 
                  label=f'Avg: {avg_latency:.3f}s')
        ax.axvline(p95_latency, color='orange', linestyle='--', 
                  label=f'P95: {p95_latency:.3f}s')
        
        # Add SLA threshold if available
        sla_threshold = metrics.get('sla_threshold', None)
        if sla_threshold:
            ax.axvline(sla_threshold, color='green', linestyle='-', 
                      label=f'SLA: {sla_threshold:.3f}s')
        
        ax.set_title('Processing Latency Distribution')
        ax.set_xlabel('Latency (seconds)')
        ax.set_ylabel('Density')
        ax.legend()
        ax.grid(True, alpha=0.3)
    
    def _plot_throughput_metrics(self, ax: plt.Axes, metrics: Dict[str, Any]) -> None:
        """Plot throughput metrics."""
        throughput_history = metrics.get('throughput_history', [])
        target_throughput = metrics.get('target_throughput', None)
        
        if not throughput_history:
            ax.text(0.5, 0.5, 'No throughput data', ha='center', va='center', transform=ax.transAxes)
            return
        
        time_axis = np.arange(len(throughput_history))
        ax.plot(time_axis, throughput_history, 'green', linewidth=2, marker='o')
        
        # Add target throughput line
        if target_throughput:
            ax.axhline(target_throughput, color='red', linestyle='--', 
                      alpha=0.7, label=f'Target: {target_throughput:.1f}')
            ax.legend()
        
        # Add moving average
        if len(throughput_history) > 5:
            window_size = min(10, len(throughput_history) // 3)
            moving_avg = np.convolve(throughput_history, 
                                   np.ones(window_size)/window_size, mode='valid')
            ma_axis = np.arange(window_size-1, len(throughput_history))
            ax.plot(ma_axis, moving_avg, 'blue', linewidth=2, alpha=0.7, 
                   label=f'MA({window_size})')
            ax.legend()
        
        ax.set_title('System Throughput')
        ax.set_xlabel('Time')
        ax.set_ylabel('Throughput (ops/sec)')
        ax.grid(True, alpha=0.3)
    
    def _plot_error_rates(self, ax: plt.Axes, metrics: Dict[str, Any]) -> None:
        """Plot error rates and alert history."""
        error_rates = metrics.get('error_rate_history', [])
        error_types = metrics.get('error_type_breakdown', {})
        alert_timeline = metrics.get('alert_timeline', [])
        
        if not any([error_rates, error_types, alert_timeline]):
            ax.text(0.5, 0.5, 'No error metrics available', 
                   ha='center', va='center', transform=ax.transAxes)
            return
        
        # Plot error rate timeline
        if error_rates:
            time_axis = np.arange(len(error_rates))
            ax.plot(time_axis, error_rates, 'red', linewidth=2, label='Error Rate')
            
            # Add error rate threshold
            error_threshold = metrics.get('error_threshold', 0.1)
            ax.axhline(error_threshold, color='orange', linestyle='--', 
                      alpha=0.7, label=f'Threshold: {error_threshold:.3f}')
        
        # Plot alert events as vertical lines
        if alert_timeline:
            for alert in alert_timeline:
                timestamp = alert.get('timestamp', 0)
                severity = alert.get('severity', 'info')
                color = {'critical': 'red', 'warning': 'orange', 'info': 'blue'}.get(severity, 'gray')
                ax.axvline(timestamp, color=color, alpha=0.7, linestyle=':', linewidth=2)
        
        ax.set_title('Error Rates and Alerts')
        ax.set_xlabel('Time')
        ax.set_ylabel('Error Rate')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # Add error type breakdown as text if available
        if error_types:
            error_text = '\n'.join([f'{error_type}: {count}' 
                                   for error_type, count in error_types.items()])
            ax.text(0.02, 0.98, f'Error Types:\n{error_text}', 
                   transform=ax.transAxes, fontsize=8,
                   verticalalignment='top',
                   bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    def _plot_component_health(self, ax: plt.Axes, metrics: Dict[str, Any]) -> None:
        """Plot health status of system components."""
        component_health = metrics.get('component_health', {})
        
        if not component_health:
            ax.text(0.5, 0.5, 'No health data', ha='center', va='center', transform=ax.transAxes)
            return
        
        components = list(component_health.keys())
        health_scores = list(component_health.values())
        
        # Define color mapping for health scores
        colors = []
        for score in health_scores:
            if score >= 0.8:
                colors.append('green')
            elif score >= 0.6:
                colors.append('yellow')
            elif score >= 0.4:
                colors.append('orange')
            else:
                colors.append('red')
        
        # Create horizontal bar chart
        y_pos = np.arange(len(components))
        bars = ax.barh(y_pos, health_scores, color=colors, alpha=0.7)
        
        # Add value labels
        for i, (bar, score) in enumerate(zip(bars, health_scores)):
            width = bar.get_width()
            ax.text(width + 0.02, bar.get_y() + bar.get_height()/2,
                   f'{score:.3f}', ha='left', va='center', fontsize=9)
        
        ax.set_yticks(y_pos)
        ax.set_yticklabels(components)
        ax.set_xlabel('Health Score')
        ax.set_title('Component Health Status')
        ax.set_xlim(0, 1)
        ax.grid(True, alpha=0.3, axis='x')
        
        # Add health level indicators
        ax.axvline(0.8, color='green', linestyle='--', alpha=0.5, label='Healthy')
        ax.axvline(0.6, color='yellow', linestyle='--', alpha=0.5, label='Warning')
        ax.axvline(0.4, color='orange', linestyle='--', alpha=0.5, label='Critical')
        ax.legend(loc='lower right', fontsize=8)
    
    def _plot_network_activity(self, ax: plt.Axes, metrics: Dict[str, Any]) -> None:
        """Plot network I/O activity."""
        bytes_sent = metrics.get('bytes_sent_history', [])
        bytes_received = metrics.get('bytes_received_history', [])
        connection_count = metrics.get('connection_count_history', [])
        
        if not any([bytes_sent, bytes_received, connection_count]):
            ax.text(0.5, 0.5, 'No network data', ha='center', va='center', transform=ax.transAxes)
            return
        
        # Plot network throughput
        if bytes_sent and bytes_received:
            time_axis = np.arange(len(bytes_sent))
            ax.plot(time_axis, bytes_sent, 'blue', linewidth=2, label='Bytes Sent')
            ax.plot(time_axis, bytes_received, 'green', linewidth=2, label='Bytes Received')
        
        # Plot connection count on secondary axis
        if connection_count:
            ax2 = ax.twinx()
            time_axis = np.arange(len(connection_count))
            ax2.plot(time_axis, connection_count, 'red', linewidth=2, 
                    alpha=0.7, label='Connections')
            ax2.set_ylabel('Active Connections', color='red')
            ax2.tick_params(axis='y', labelcolor='red')
            ax2.legend(loc='upper right')
        
        ax.set_title('Network Activity')
        ax.set_xlabel('Time')
        ax.set_ylabel('Bytes/sec')
        ax.legend(loc='upper left')
        ax.grid(True, alpha=0.3)
    
    def _plot_system_summary(self, ax: plt.Axes, data: Dict[str, Any]) -> None:
        """Plot system performance summary."""
        # Collect summary metrics from all categories
        summary_metrics = {}
        
        # System metrics
        system_metrics = data.get('system_metrics', {})
        if system_metrics:
            cpu_usage = system_metrics.get('cpu_usage_history', [])
            memory_usage = system_metrics.get('memory_usage_history', [])
            
            if cpu_usage:
                summary_metrics['Avg CPU Usage (%)'] = np.mean(cpu_usage[-10:])  # Last 10 points
            if memory_usage:
                summary_metrics['Avg Memory Usage (%)'] = np.mean(memory_usage[-10:])
        
        # Neural metrics
        neural_metrics = data.get('neural_metrics', {})
        if neural_metrics:
            spike_rates = neural_metrics.get('spike_rates', [])
            if spike_rates:
                summary_metrics['Avg Spike Rate'] = np.mean(spike_rates[-10:])
        
        # Latency metrics
        latency_metrics = data.get('latency_metrics', {})
        if latency_metrics:
            avg_latency = latency_metrics.get('avg_latency', 0)
            summary_metrics['Avg Latency (ms)'] = avg_latency * 1000
        
        # Throughput metrics
        throughput_metrics = data.get('throughput_metrics', {})
        if throughput_metrics:
            throughput_history = throughput_metrics.get('throughput_history', [])
            if throughput_history:
                summary_metrics['Avg Throughput'] = np.mean(throughput_history[-10:])
        
        # Error metrics
        error_metrics = data.get('error_metrics', {})
        if error_metrics:
            error_rates = error_metrics.get('error_rate_history', [])
            if error_rates:
                summary_metrics['Avg Error Rate'] = np.mean(error_rates[-10:])
        
        # Create summary table
        if summary_metrics:
            # Create table data
            table_data = []
            for metric, value in summary_metrics.items():
                table_data.append([metric, f'{value:.3f}'])
            
            # Create table
            table = ax.table(cellText=table_data,
                           colLabels=['Metric', 'Value'],
                           cellLoc='center',
                           loc='center',
                           bbox=[0, 0, 1, 1])
            
            # Style the table
            table.auto_set_font_size(False)
            table.set_fontsize(10)
            table.scale(1, 2)
            
            # Color cells based on values (simplified)
            for i, (metric, value) in enumerate(summary_metrics.items()):
                if 'Error' in metric:
                    color = 'lightcoral' if value > 0.1 else 'lightgreen'
                elif 'CPU' in metric or 'Memory' in metric:
                    color = 'lightcoral' if value > 80 else 'lightgreen'
                else:
                    color = 'lightblue'
                
                table[(i+1, 1)].set_facecolor(color)
            
            # Header styling
            for j in range(2):
                table[(0, j)].set_facecolor('lightgray')
                table[(0, j)].set_text_props(weight='bold')
            
            ax.set_title('System Performance Summary', fontsize=14, fontweight='bold')
            ax.axis('off')
        else:
            ax.text(0.5, 0.5, 'No summary metrics available', 
                   ha='center', va='center', transform=ax.transAxes)
    
    def update_visualization(self, new_data: Dict[str, Any]) -> None:
        """Update performance dashboard with new data."""
        with self.update_lock:
            # Update performance buffers
            timestamp = time.time()
            
            # System metrics
            system_metrics = new_data.get('system_metrics', {})
            for metric_name, value in system_metrics.items():
                self.performance_buffer[f'system_{metric_name}'].append((timestamp, value))
            
            # Neural metrics
            neural_metrics = new_data.get('neural_metrics', {})
            for metric_name, value in neural_metrics.items():
                self.performance_buffer[f'neural_{metric_name}'].append((timestamp, value))
            
            # Add alerts if any
            alerts = new_data.get('alerts', [])
            for alert in alerts:
                alert['timestamp'] = timestamp
                self.alert_history.append(alert)
            
            self.last_update_time = timestamp

# =============================================================================
# Interactive Visualization Manager
# =============================================================================

class InteractiveVisualizationManager:
    """
    Manager for interactive visualizations with real-time updates,
    user controls, and dynamic parameter adjustment.
    """
    
    def __init__(self, config: VisualizationConfig, logger: Optional[logging.Logger] = None):
        self.config = config
        self.logger = logger or viz_logger
        self.visualizers = {}
        self.active_sessions = {}
        self.update_threads = {}
        self.data_queue = queue.Queue()
        self.is_running = False
        
        # Initialize visualizers
        self._initialize_visualizers()
        
        # Setup update system
        self.update_lock = threading.Lock()
        
    def _initialize_visualizers(self) -> None:
        """Initialize all specialized visualizers."""
        self.visualizers = {
            'neuromorphic': NeuromorphicNetworkVisualizer(self.config, self.logger),
            'plasticity': SynapticPlasticityVisualizer(self.config, self.logger),
            'attention': AttentionVisualizer(self.config, self.logger),
            'diffusion': DiffusionProcessVisualizer(self.config, self.logger),
            'consciousness': ConsciousnessMetricsVisualizer(self.config, self.logger),
            'performance': PerformanceDashboardVisualizer(self.config, self.logger)
        }
        
        self.logger.info(f"Initialized {len(self.visualizers)} specialized visualizers")
    
    def start_real_time_session(self, session_id: str, visualizer_types: List[str],
                               update_interval: float = 1.0) -> str:
        """Start a real-time visualization session."""
        if session_id in self.active_sessions:
            raise ValueError(f"Session {session_id} already active")
        
        # Validate visualizer types
        invalid_types = set(visualizer_types) - set(self.visualizers.keys())
        if invalid_types:
            raise ValueError(f"Invalid visualizer types: {invalid_types}")
        
        # Create session
        session = {
            'id': session_id,
            'visualizer_types': visualizer_types,
            'update_interval': update_interval,
            'start_time': time.time(),
            'last_update': time.time(),
            'data_buffer': defaultdict(list),
            'is_active': True
        }
        
        self.active_sessions[session_id] = session
        
        # Start update thread
        update_thread = threading.Thread(
            target=self._session_update_loop,
            args=(session_id,),
            daemon=True
        )
        update_thread.start()
        self.update_threads[session_id] = update_thread
        
        self.logger.info(f"Started real-time session {session_id} with visualizers: {visualizer_types}")
        return session_id
    
    def stop_real_time_session(self, session_id: str) -> None:
        """Stop a real-time visualization session."""
        if session_id not in self.active_sessions:
            raise ValueError(f"Session {session_id} not found")
        
        # Mark session as inactive
        self.active_sessions[session_id]['is_active'] = False
        
        # Wait for update thread to finish
        if session_id in self.update_threads:
            self.update_threads[session_id].join(timeout=5.0)
            del self.update_threads[session_id]
        
        # Clean up session
        del self.active_sessions[session_id]
        
        self.logger.info(f"Stopped real-time session {session_id}")
    
    def update_session_data(self, session_id: str, data: Dict[str, Any]) -> None:
        """Update data for a real-time session."""
        if session_id not in self.active_sessions:
            self.logger.warning(f"Attempted to update inactive session {session_id}")
            return
        
        with self.update_lock:
            session = self.active_sessions[session_id]
            
            # Add timestamp
            data['timestamp'] = time.time()
            
            # Store data for each visualizer type
            for viz_type in session['visualizer_types']:
                if viz_type in data or 'global' in data:
                    viz_data = data.get(viz_type, data.get('global', {}))
                    session['data_buffer'][viz_type].append(viz_data)
                    
                    # Limit buffer size
                    max_buffer_size = self.config.max_data_points
                    if len(session['data_buffer'][viz_type]) > max_buffer_size:
                        session['data_buffer'][viz_type] = session['data_buffer'][viz_type][-max_buffer_size:]
            
            session['last_update'] = time.time()
    
    def _session_update_loop(self, session_id: str) -> None:
        """Update loop for real-time session."""
        session = self.active_sessions[session_id]
        
        while session['is_active']:
            try:
                # Update visualizers with latest data
                with self.update_lock:
                    for viz_type in session['visualizer_types']:
                        if viz_type in session['data_buffer'] and session['data_buffer'][viz_type]:
                            latest_data = session['data_buffer'][viz_type][-1]
                            
                            if viz_type in self.visualizers:
                                self.visualizers[viz_type].update_visualization(latest_data)
                
                # Sleep for update interval
                time.sleep(session['update_interval'])
                
            except Exception as e:
                self.logger.error(f"Error in session {session_id} update loop: {e}")
                time.sleep(session['update_interval'])
    
    def create_interactive_plot(self, visualizer_type: str, data: Dict[str, Any],
                              controls: Optional[Dict[str, Any]] = None) -> Union[plt.Figure, go.Figure]:
        """Create an interactive plot with user controls."""
        if visualizer_type not in self.visualizers:
            raise ValueError(f"Unknown visualizer type: {visualizer_type}")
        
        visualizer = self.visualizers[visualizer_type]
        
        if self.config.mode == VisualizationMode.INTERACTIVE and BOKEH_AVAILABLE:
            return self._create_bokeh_interactive_plot(visualizer, data, controls)
        elif self.config.mode == VisualizationMode.INTERACTIVE:
            return self._create_plotly_interactive_plot(visualizer, data, controls)
        else:
            return visualizer.create_visualization(data)
    
    def _create_plotly_interactive_plot(self, visualizer: BaseVisualizer, 
                                      data: Dict[str, Any],
                                      controls: Optional[Dict[str, Any]] = None) -> go.Figure:
        """Create interactive Plotly visualization."""
        # This is a simplified implementation
        # In practice, you would convert matplotlib plots to Plotly
        matplotlib_fig = visualizer.create_visualization(data)
        
        # Convert matplotlib to plotly (simplified)
        plotly_fig = go.Figure()
        
        # Add some basic interactivity
        plotly_fig.update_layout(
            title="Interactive ULTRA Visualization",
            showlegend=True,
            hovermode='closest'
        )
        
        return plotly_fig
    
    def _create_bokeh_interactive_plot(self, visualizer: BaseVisualizer,
                                     data: Dict[str, Any],
                                     controls: Optional[Dict[str, Any]] = None):
        """Create interactive Bokeh visualization."""
        # Simplified Bokeh implementation
        from bokeh.plotting import figure
        from bokeh.models import ColumnDataSource
        
        p = figure(title="Interactive ULTRA Visualization", 
                  width=800, height=600,
                  tools="pan,wheel_zoom,box_zoom,reset,save")
        
        # Add some basic plot elements
        if 'x_data' in data and 'y_data' in data:
            source = ColumnDataSource(data=dict(
                x=data['x_data'],
                y=data['y_data']
            ))
            
            p.line('x', 'y', source=source, line_width=2)
            p.circle('x', 'y', source=source, size=6, alpha=0.6)
        
        return p
    
    def export_visualization(self, visualizer_type: str, data: Dict[str, Any],
                           export_format: str = 'png', **kwargs) -> str:
        """Export visualization to file."""
        if visualizer_type not in self.visualizers:
            raise ValueError(f"Unknown visualizer type: {visualizer_type}")
        
        visualizer = self.visualizers[visualizer_type]
        figure = visualizer.create_visualization(data)
        
        # Generate filename
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"ultra_{visualizer_type}_{timestamp}.{export_format}"
        
        filepath = visualizer.save_visualization(figure, filename, **kwargs)
        
        self.logger.info(f"Exported {visualizer_type} visualization to {filepath}")
        return filepath
    
    def create_animation_sequence(self, visualizer_type: str, 
                                data_sequence: List[Dict[str, Any]],
                                duration_seconds: float = 10.0) -> str:
        """Create animation sequence from data."""
        if visualizer_type not in self.visualizers:
            raise ValueError(f"Unknown visualizer type: {visualizer_type}")
        
        visualizer = self.visualizers[visualizer_type]
        
        # Create animation
        anim = visualizer.create_animation(data_sequence, duration_seconds)
        
        # Save animation
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"ultra_{visualizer_type}_animation_{timestamp}.gif"
        
        filepath = visualizer.save_animation(anim, filename)
        
        self.logger.info(f"Created animation for {visualizer_type}: {filepath}")
        return filepath
    
    def get_session_status(self, session_id: str) -> Dict[str, Any]:
        """Get status of real-time session."""
        if session_id not in self.active_sessions:
            return {'status': 'not_found'}
        
        session = self.active_sessions[session_id]
        current_time = time.time()
        
        return {
            'status': 'active' if session['is_active'] else 'inactive',
            'session_id': session_id,
            'visualizer_types': session['visualizer_types'],
            'uptime_seconds': current_time - session['start_time'],
            'last_update_seconds_ago': current_time - session['last_update'],
            'update_interval': session['update_interval'],
            'data_buffer_sizes': {
                viz_type: len(session['data_buffer'][viz_type])
                for viz_type in session['visualizer_types']
            }
        }
    
    def list_active_sessions(self) -> List[str]:
        """List all active session IDs."""
        return [sid for sid, session in self.active_sessions.items() if session['is_active']]
    
    def cleanup_inactive_sessions(self) -> int:
        """Clean up inactive sessions and return count cleaned."""
        inactive_sessions = [
            sid for sid, session in self.active_sessions.items() 
            if not session['is_active']
        ]
        
        for session_id in inactive_sessions:
            if session_id in self.active_sessions:
                del self.active_sessions[session_id]
            if session_id in self.update_threads:
                del self.update_threads[session_id]
        
        return len(inactive_sessions)

# =============================================================================
# Main ULTRA Visualization Factory
# =============================================================================

class ULTRAVisualizationFactory:
    """
    Main factory class for creating and managing ULTRA visualizations.
    Provides unified interface for all visualization components.
    """
    
    def __init__(self, config: Optional[VisualizationConfig] = None,
                 logger: Optional[logging.Logger] = None):
        self.config = config or VisualizationConfig()
        self.logger = logger or viz_logger
        
        # Initialize interactive manager
        self.interactive_manager = InteractiveVisualizationManager(self.config, self.logger)
        
        # Initialize individual visualizers
        self.visualizers = self.interactive_manager.visualizers
        
        # Visualization cache
        self.visualization_cache = {}
        self.cache_lock = threading.Lock()
        
        self.logger.info("ULTRA Visualization Factory initialized")
    
    def create_visualization(self, viz_type: str, data: Dict[str, Any],
                           mode: Optional[VisualizationMode] = None,
                           **kwargs) -> Union[plt.Figure, go.Figure]:
        """Create visualization of specified type."""
        if viz_type not in self.visualizers:
            available_types = list(self.visualizers.keys())
            raise ValueError(f"Unknown visualization type: {viz_type}. Available: {available_types}")
        
        # Override config mode if specified
        if mode is not None:
            original_mode = self.config.mode
            self.config.mode = mode
        
        try:
            if mode == VisualizationMode.INTERACTIVE:
                return self.interactive_manager.create_interactive_plot(viz_type, data, **kwargs)
            else:
                return self.visualizers[viz_type].create_visualization(data, **kwargs)
        finally:
            # Restore original mode
            if mode is not None:
                self.config.mode = original_mode
    
    def create_dashboard(self, dashboard_type: str = 'comprehensive',
                        data: Dict[str, Any] = None) -> plt.Figure:
        """Create comprehensive dashboard with multiple visualizations."""
        if data is None:
            data = {}
        
        if dashboard_type == 'comprehensive':
            return self._create_comprehensive_dashboard(data)
        elif dashboard_type == 'neuromorphic':
            return self._create_neuromorphic_dashboard(data)
        elif dashboard_type == 'consciousness':
            return self._create_consciousness_dashboard(data)
        elif dashboard_type == 'performance':
            return self._create_performance_dashboard(data)
        else:
            raise ValueError(f"Unknown dashboard type: {dashboard_type}")
    
    def _create_comprehensive_dashboard(self, data: Dict[str, Any]) -> plt.Figure:
        """Create comprehensive dashboard with all ULTRA components."""
        fig = plt.figure(figsize=(24, 20))
        
        # Create complex grid layout
        gs = fig.add_gridspec(5, 4, height_ratios=[1.2, 1, 1, 1, 0.8])
        
        # Row 1: Neural network and attention
        if 'neuromorphic' in data:
            ax_neural = fig.add_subplot(gs[0, :2])
            neural_viz = self.visualizers['neuromorphic']
            # Create simplified version for dashboard
            self._create_dashboard_neural_plot(ax_neural, data['neuromorphic'])
        
        if 'attention' in data:
            ax_attention = fig.add_subplot(gs[0, 2:])
            attention_viz = self.visualizers['attention']
            self._create_dashboard_attention_plot(ax_attention, data['attention'])
        
        # Row 2: Diffusion and consciousness
        if 'diffusion' in data:
            ax_diffusion = fig.add_subplot(gs[1, :2])
            diffusion_viz = self.visualizers['diffusion']
            self._create_dashboard_diffusion_plot(ax_diffusion, data['diffusion'])
        
        if 'consciousness' in data:
            ax_consciousness = fig.add_subplot(gs[1, 2:])
            consciousness_viz = self.visualizers['consciousness']
            self._create_dashboard_consciousness_plot(ax_consciousness, data['consciousness'])
        
        # Row 3: Performance metrics
        if 'performance' in data:
            ax_performance = fig.add_subplot(gs[2, :])
            performance_viz = self.visualizers['performance']
            self._create_dashboard_performance_plot(ax_performance, data['performance'])
        
        # Row 4: Additional metrics
        if 'plasticity' in data:
            ax_plasticity = fig.add_subplot(gs[3, :2])
            plasticity_viz = self.visualizers['plasticity']
            self._create_dashboard_plasticity_plot(ax_plasticity, data['plasticity'])
        
        # System status
        ax_status = fig.add_subplot(gs[3, 2:])
        self._create_system_status_plot(ax_status, data)
        
        # Summary
        ax_summary = fig.add_subplot(gs[4, :])
        self._create_dashboard_summary(ax_summary, data)
        
        plt.tight_layout()
        plt.suptitle('ULTRA System Comprehensive Dashboard', fontsize=16, fontweight='bold', y=0.98)
        
        return fig
    
    def _create_dashboard_neural_plot(self, ax: plt.Axes, data: Dict[str, Any]) -> None:
        """Create simplified neural plot for dashboard."""
        # Simplified version of neural network visualization
        spike_data = data.get('spike_events', [])
        
        if spike_data:
            # Plot recent spike activity
            recent_spikes = spike_data[-100:]  # Last 100 spikes
            neuron_ids = [spike.get('neuron_id', 0) for spike in recent_spikes]
            timestamps = [spike.get('timestamp', 0) for spike in recent_spikes]
            
            ax.scatter(timestamps, neuron_ids, c='red', s=10, alpha=0.7)
            ax.set_title('Neural Activity (Recent Spikes)')
            ax.set_xlabel('Time')
            ax.set_ylabel('Neuron ID')
        else:
            ax.text(0.5, 0.5, 'No neural data', ha='center', va='center', transform=ax.transAxes)
    
    def _create_dashboard_attention_plot(self, ax: plt.Axes, data: Dict[str, Any]) -> None:
        """Create simplified attention plot for dashboard."""
        attention_weights = data.get('attention_weights', None)
        
        if attention_weights is not None:
            im = ax.imshow(attention_weights, cmap='plasma', aspect='auto')
            ax.set_title('Attention Weights')
        else:
            ax.text(0.5, 0.5, 'No attention data', ha='center', va='center', transform=ax.transAxes)
    
    def _create_dashboard_diffusion_plot(self, ax: plt.Axes, data: Dict[str, Any]) -> None:
        """Create simplified diffusion plot for dashboard."""
        convergence_data = data.get('convergence_data', {})
        convergence_history = convergence_data.get('convergence_history', [])
        
        if convergence_history:
            ax.semilogy(convergence_history, 'blue', linewidth=2)
            ax.set_title('Diffusion Convergence')
            ax.set_xlabel('Step')
            ax.set_ylabel('Convergence Metric')
        else:
            ax.text(0.5, 0.5, 'No diffusion data', ha='center', va='center', transform=ax.transAxes)
    
    def _create_dashboard_consciousness_plot(self, ax: plt.Axes, data: Dict[str, Any]) -> None:
        """Create simplified consciousness plot for dashboard."""
        phi_data = data.get('phi_data', {})
        phi_values = phi_data.get('phi_history', [])
        
        if phi_values:
            ax.plot(phi_values, 'purple', linewidth=2)
            ax.set_title('Consciousness (Φ) Evolution')
            ax.set_xlabel('Time')
            ax.set_ylabel('Φ Value')
        else:
            ax.text(0.5, 0.5, 'No consciousness data', ha='center', va='center', transform=ax.transAxes)
    
    def _create_dashboard_performance_plot(self, ax: plt.Axes, data: Dict[str, Any]) -> None:
        """Create simplified performance plot for dashboard."""
        system_metrics = data.get('system_metrics', {})
        cpu_usage = system_metrics.get('cpu_usage_history', [])
        memory_usage = system_metrics.get('memory_usage_history', [])
        
        if cpu_usage or memory_usage:
            if cpu_usage:
                ax.plot(cpu_usage, 'blue', label='CPU Usage', linewidth=2)
            if memory_usage:
                ax.plot(memory_usage, 'green', label='Memory Usage', linewidth=2)
            
            ax.set_title('System Performance')
            ax.set_xlabel('Time')
            ax.set_ylabel('Usage (%)')
            ax.legend()
            ax.set_ylim(0, 100)
        else:
            ax.text(0.5, 0.5, 'No performance data', ha='center', va='center', transform=ax.transAxes)
    
    def _create_dashboard_plasticity_plot(self, ax: plt.Axes, data: Dict[str, Any]) -> None:
        """Create simplified plasticity plot for dashboard."""
        stdp_events = data.get('stdp_events', [])
        
        if stdp_events:
            delta_ts = [event.get('delta_t', 0) for event in stdp_events]
            weight_changes = [event.get('weight_change', 0) for event in stdp_events]
            
            ax.scatter(delta_ts, weight_changes, alpha=0.6, s=20)
            ax.set_title('STDP Events')
            ax.set_xlabel('Δt (ms)')
            ax.set_ylabel('Δw')
        else:
            ax.text(0.5, 0.5, 'No plasticity data', ha='center', va='center', transform=ax.transAxes)
    
    def _create_system_status_plot(self, ax: plt.Axes, data: Dict[str, Any]) -> None:
        """Create system status indicators."""
        # Create status indicators for different components
        components = ['Core Neural', 'Transformer', 'Diffusion', 'Consciousness', 'Performance']
        statuses = []
        
        for component in components:
            comp_key = component.lower().replace(' ', '_')
            if comp_key in data:
                statuses.append('Active')
            else:
                statuses.append('Inactive')
        
        # Create status visualization
        colors = ['green' if status == 'Active' else 'red' for status in statuses]
        y_pos = np.arange(len(components))
        
        bars = ax.barh(y_pos, [1]*len(components), color=colors, alpha=0.7)
        
        ax.set_yticks(y_pos)
        ax.set_yticklabels(components)
        ax.set_xlim(0, 1)
        ax.set_title('Component Status')
        ax.set_xticks([])
        
        # Add status labels
        for i, (bar, status) in enumerate(zip(bars, statuses)):
            ax.text(0.5, bar.get_y() + bar.get_height()/2, status,
                   ha='center', va='center', fontweight='bold', color='white')
    
    def _create_dashboard_summary(self, ax: plt.Axes, data: Dict[str, Any]) -> None:
        """Create dashboard summary with key metrics."""
        summary_text = "ULTRA System Dashboard Summary\n\n"
        
        # Count active components
        active_components = len([k for k in data.keys() if k in self.visualizers])
        summary_text += f"Active Components: {active_components}/{len(self.visualizers)}\n"
        
        # Add component-specific summaries
        if 'neuromorphic' in data:
            spike_data = data['neuromorphic'].get('spike_events', [])
            summary_text += f"Neural Activity: {len(spike_data)} recent spikes\n"
        
        if 'performance' in data:
            system_metrics = data['performance'].get('system_metrics', {})
            cpu_usage = system_metrics.get('cpu_usage_history', [])
            if cpu_usage:
                summary_text += f"Current CPU Usage: {cpu_usage[-1]:.1f}%\n"
        
        if 'consciousness' in data:
            phi_data = data['consciousness'].get('phi_data', {})
            phi_values = phi_data.get('phi_history', [])
            if phi_values:
                summary_text += f"Current Φ: {phi_values[-1]:.4f}\n"
        
        summary_text += f"\nDashboard Generated: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        
        ax.text(0.02, 0.98, summary_text, transform=ax.transAxes, 
               fontsize=12, verticalalignment='top', fontfamily='monospace',
               bbox=dict(boxstyle='round', facecolor='lightgray', alpha=0.8))
        ax.axis('off')
    
    def start_real_time_monitoring(self, session_id: str, components: List[str],
                                 update_interval: float = 1.0) -> str:
        """Start real-time monitoring session."""
        return self.interactive_manager.start_real_time_session(
            session_id, components, update_interval
        )
    
    def stop_real_time_monitoring(self, session_id: str) -> None:
        """Stop real-time monitoring session."""
        self.interactive_manager.stop_real_time_session(session_id)
    
    def update_real_time_data(self, session_id: str, data: Dict[str, Any]) -> None:
        """Update data for real-time monitoring session."""
        self.interactive_manager.update_session_data(session_id, data)
    
    def export_all_visualizations(self, data: Dict[str, Any], 
                                 export_dir: str = "./exports") -> List[str]:
        """Export all available visualizations to files."""
        export_dir = Path(export_dir)
        export_dir.mkdir(parents=True, exist_ok=True)
        
        exported_files = []
        
        for viz_type, viz_data in data.items():
            if viz_type in self.visualizers:
                try:
                    filepath = self.interactive_manager.export_visualization(
                        viz_type, {viz_type: viz_data}
                    )
                    exported_files.append(filepath)
                except Exception as e:
                    self.logger.error(f"Failed to export {viz_type}: {e}")
        
        self.logger.info(f"Exported {len(exported_files)} visualizations to {export_dir}")
        return exported_files
    
    def create_visualization_report(self, data: Dict[str, Any], 
                                  report_type: str = 'comprehensive',
                                  output_format: str = 'html') -> str:
        """Create comprehensive visualization report."""
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        
        if report_type == 'comprehensive':
            # Create comprehensive dashboard
            dashboard_fig = self.create_dashboard('comprehensive', data)
            
            # Save dashboard
            dashboard_path = f"ultra_comprehensive_report_{timestamp}.png"
            dashboard_fig.savefig(dashboard_path, dpi=300, bbox_inches='tight')
            
            # Create HTML report if requested
            if output_format == 'html':
                return self._create_html_report(data, dashboard_path, timestamp)
            else:
                return dashboard_path
        
        elif report_type == 'summary':
            return self._create_summary_report(data, timestamp)
        
        else:
            raise ValueError(f"Unknown report type: {report_type}")
    
    def _create_html_report(self, data: Dict[str, Any], dashboard_path: str, 
                           timestamp: str) -> str:
        """Create comprehensive HTML report."""
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>ULTRA System Visualization Report</title>
            <style>
                body {{
                    font-family: Arial, sans-serif;
                    margin: 20px;
                    background-color: #f5f5f5;
                }}
                .header {{
                    background-color: #2c3e50;
                    color: white;
                    padding: 20px;
                    text-align: center;
                    border-radius: 10px;
                }}
                .section {{
                    background-color: white;
                    margin: 20px 0;
                    padding: 20px;
                    border-radius: 10px;
                    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
                }}
                .metric {{
                    display: inline-block;
                    margin: 10px;
                    padding: 10px;
                    background-color: #ecf0f1;
                    border-radius: 5px;
                    min-width: 150px;
                    text-align: center;
                }}
                .status-active {{
                    color: #27ae60;
                    font-weight: bold;
                }}
                .status-inactive {{
                    color: #e74c3c;
                    font-weight: bold;
                }}
                img {{
                    max-width: 100%;
                    height: auto;
                    border-radius: 5px;
                }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>ULTRA System Visualization Report</h1>
                <p>Generated on: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            </div>
            
            <div class="section">
                <h2>System Overview Dashboard</h2>
                <img src="{dashboard_path}" alt="ULTRA System Dashboard">
            </div>
            
            <div class="section">
                <h2>Component Status</h2>
                {self._generate_component_status_html(data)}
            </div>
            
            <div class="section">
                <h2>Key Metrics Summary</h2>
                {self._generate_metrics_summary_html(data)}
            </div>
            
            <div class="section">
                <h2>Performance Analysis</h2>
                {self._generate_performance_analysis_html(data)}
            </div>
            
            <div class="section">
                <h2>Recommendations</h2>
                {self._generate_recommendations_html(data)}
            </div>
        </body>
        </html>
        """
        
        # Save HTML report
        html_path = f"ultra_report_{timestamp}.html"
        with open(html_path, 'w') as f:
            f.write(html_content)
        
        self.logger.info(f"Generated HTML report: {html_path}")
        return html_path
    
    def _generate_component_status_html(self, data: Dict[str, Any]) -> str:
        """Generate HTML for component status."""
        html = ""
        
        for component_name in self.visualizers.keys():
            if component_name in data:
                status_class = "status-active"
                status_text = "Active"
                
                # Get component-specific metrics
                component_data = data[component_name]
                metrics_info = ""
                
                if component_name == 'neuromorphic':
                    spike_events = component_data.get('spike_events', [])
                    metrics_info = f"({len(spike_events)} recent spikes)"
                elif component_name == 'consciousness':
                    phi_data = component_data.get('phi_data', {})
                    phi_values = phi_data.get('phi_history', [])
                    if phi_values:
                        metrics_info = f"(Φ = {phi_values[-1]:.4f})"
                elif component_name == 'performance':
                    system_metrics = component_data.get('system_metrics', {})
                    cpu_usage = system_metrics.get('cpu_usage_history', [])
                    if cpu_usage:
                        metrics_info = f"(CPU: {cpu_usage[-1]:.1f}%)"
            else:
                status_class = "status-inactive"
                status_text = "Inactive"
                metrics_info = ""
            
            html += f"""
            <div class="metric">
                <strong>{component_name.title()}</strong><br>
                <span class="{status_class}">{status_text}</span><br>
                <small>{metrics_info}</small>
            </div>
            """
        
        return html
    
    def _generate_metrics_summary_html(self, data: Dict[str, Any]) -> str:
        """Generate HTML for metrics summary."""
        html = ""
        
        # Collect key metrics from all components
        key_metrics = {}
        
        # Neural metrics
        if 'neuromorphic' in data:
            neural_data = data['neuromorphic']
            spike_events = neural_data.get('spike_events', [])
            key_metrics['Active Neurons'] = len(set(s.get('neuron_id', 0) for s in spike_events))
            key_metrics['Total Spikes'] = len(spike_events)
        
        # Consciousness metrics
        if 'consciousness' in data:
            consciousness_data = data['consciousness']
            phi_data = consciousness_data.get('phi_data', {})
            phi_values = phi_data.get('phi_history', [])
            if phi_values:
                key_metrics['Current Φ'] = f"{phi_values[-1]:.4f}"
                key_metrics['Max Φ'] = f"{max(phi_values):.4f}"
        
        # Performance metrics
        if 'performance' in data:
            performance_data = data['performance']
            system_metrics = performance_data.get('system_metrics', {})
            cpu_usage = system_metrics.get('cpu_usage_history', [])
            memory_usage = system_metrics.get('memory_usage_history', [])
            
            if cpu_usage:
                key_metrics['CPU Usage'] = f"{cpu_usage[-1]:.1f}%"
            if memory_usage:
                key_metrics['Memory Usage'] = f"{memory_usage[-1]:.1f}%"
        
        # Attention metrics
        if 'attention' in data:
            attention_data = data['attention']
            multihead_data = attention_data.get('multihead_attention', {})
            key_metrics['Attention Heads'] = len(multihead_data)
        
        # Generate HTML for metrics
        for metric_name, metric_value in key_metrics.items():
            html += f"""
            <div class="metric">
                <strong>{metric_name}</strong><br>
                <span style="font-size: 1.2em; color: #3498db;">{metric_value}</span>
            </div>
            """
        
        return html
    
    def _generate_performance_analysis_html(self, data: Dict[str, Any]) -> str:
        """Generate HTML for performance analysis."""
        html = "<h3>Performance Insights</h3><ul>"
        
        # Analyze performance data
        if 'performance' in data:
            performance_data = data['performance']
            system_metrics = performance_data.get('system_metrics', {})
            
            # CPU analysis
            cpu_usage = system_metrics.get('cpu_usage_history', [])
            if cpu_usage:
                avg_cpu = np.mean(cpu_usage)
                if avg_cpu > 80:
                    html += f"<li><strong>High CPU Usage:</strong> Average CPU usage is {avg_cpu:.1f}%. Consider optimization.</li>"
                elif avg_cpu < 20:
                    html += f"<li><strong>Low CPU Usage:</strong> Average CPU usage is {avg_cpu:.1f}%. System is underutilized.</li>"
                else:
                    html += f"<li><strong>Optimal CPU Usage:</strong> Average CPU usage is {avg_cpu:.1f}%.</li>"
            
            # Memory analysis
            memory_usage = system_metrics.get('memory_usage_history', [])
            if memory_usage:
                avg_memory = np.mean(memory_usage)
                if avg_memory > 90:
                    html += f"<li><strong>High Memory Usage:</strong> Average memory usage is {avg_memory:.1f}%. Risk of memory overflow.</li>"
                else:
                    html += f"<li><strong>Memory Usage:</strong> Average memory usage is {avg_memory:.1f}%.</li>"
        
        # Neural performance analysis
        if 'neuromorphic' in data:
            neural_data = data['neuromorphic']
            spike_events = neural_data.get('spike_events', [])
            
            if spike_events:
                recent_spikes = [s for s in spike_events if time.time() - s.get('timestamp', 0) < 60]
                spike_rate = len(recent_spikes) / 60  # spikes per second
                
                if spike_rate > 100:
                    html += f"<li><strong>High Neural Activity:</strong> {spike_rate:.1f} spikes/sec. Network highly active.</li>"
                elif spike_rate < 1:
                    html += f"<li><strong>Low Neural Activity:</strong> {spike_rate:.1f} spikes/sec. Network may be underactive.</li>"
                else:
                    html += f"<li><strong>Normal Neural Activity:</strong> {spike_rate:.1f} spikes/sec.</li>"
        
        # Consciousness analysis
        if 'consciousness' in data:
            consciousness_data = data['consciousness']
            phi_data = consciousness_data.get('phi_data', {})
            phi_values = phi_data.get('phi_history', [])
            
            if phi_values:
                current_phi = phi_values[-1]
                if current_phi > 1.0:
                    html += f"<li><strong>High Consciousness:</strong> Current Φ = {current_phi:.4f}. Strong integrated information.</li>"
                elif current_phi > 0.1:
                    html += f"<li><strong>Moderate Consciousness:</strong> Current Φ = {current_phi:.4f}.</li>"
                else:
                    html += f"<li><strong>Low Consciousness:</strong> Current Φ = {current_phi:.4f}. Limited integration.</li>"
        
        html += "</ul>"
        return html
    
    def _generate_recommendations_html(self, data: Dict[str, Any]) -> str:
        """Generate HTML for system recommendations."""
        html = "<h3>System Recommendations</h3><ul>"
        
        recommendations = []
        
        # Performance-based recommendations
        if 'performance' in data:
            performance_data = data['performance']
            system_metrics = performance_data.get('system_metrics', {})
            
            cpu_usage = system_metrics.get('cpu_usage_history', [])
            memory_usage = system_metrics.get('memory_usage_history', [])
            
            if cpu_usage and np.mean(cpu_usage) > 85:
                recommendations.append("Consider scaling computational resources or optimizing algorithms to reduce CPU load.")
            
            if memory_usage and np.mean(memory_usage) > 85:
                recommendations.append("Memory usage is high. Consider increasing memory allocation or implementing memory optimization.")
        
        # Neural network recommendations
        if 'neuromorphic' in data:
            neural_data = data['neuromorphic']
            spike_events = neural_data.get('spike_events', [])
            
            if len(spike_events) < 10:
                recommendations.append("Low neural activity detected. Consider adjusting input stimulation or network parameters.")
            
            network_data = neural_data.get('network_structure', {})
            adjacency_matrix = network_data.get('adjacency_matrix', None)
            if adjacency_matrix is not None:
                connection_density = np.mean(adjacency_matrix > 0)
                if connection_density < 0.05:
                    recommendations.append("Network connectivity is sparse. Consider increasing connection density for better information flow.")
                elif connection_density > 0.3:
                    recommendations.append("Network connectivity is dense. Consider pruning weak connections for efficiency.")
        
        # Consciousness recommendations
        if 'consciousness' in data:
            consciousness_data = data['consciousness']
            phi_data = consciousness_data.get('phi_data', {})
            phi_values = phi_data.get('phi_history', [])
            
            if phi_values and phi_values[-1] < 0.01:
                recommendations.append("Low consciousness levels detected. Consider increasing network integration or adjusting Φ calculation parameters.")
        
        # Attention recommendations
        if 'attention' in data:
            attention_data = data['attention']
            attention_weights = attention_data.get('attention_weights', None)
            
            if attention_weights is not None:
                attention_entropy = -np.sum(attention_weights * np.log(attention_weights + 1e-12))
                if attention_entropy > 5:
                    recommendations.append("Attention is highly dispersed. Consider focusing mechanisms or attention regularization.")
        
        # Default recommendations if none specific
        if not recommendations:
            recommendations = [
                "System is operating within normal parameters.",
                "Consider enabling real-time monitoring for continuous optimization.",
                "Regular performance benchmarking is recommended for optimal operation."
            ]
        
        for rec in recommendations:
            html += f"<li>{rec}</li>"
        
        html += "</ul>"
        return html
    
    def _create_summary_report(self, data: Dict[str, Any], timestamp: str) -> str:
        """Create summary text report."""
        report_lines = [
            "="*60,
            "ULTRA System Summary Report",
            "="*60,
            f"Generated: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            "",
            "COMPONENT STATUS:",
            "-"*20
        ]
        
        # Component status
        for component_name in self.visualizers.keys():
            status = "ACTIVE" if component_name in data else "INACTIVE"
            report_lines.append(f"{component_name.upper():.<20} {status}")
        
        report_lines.extend(["", "KEY METRICS:", "-"*20])
        
        # Key metrics
        total_components = len(self.visualizers)
        active_components = len([k for k in data.keys() if k in self.visualizers])
        report_lines.append(f"Active Components: {active_components}/{total_components}")
        
        if 'neuromorphic' in data:
            spike_events = data['neuromorphic'].get('spike_events', [])
            report_lines.append(f"Neural Activity: {len(spike_events)} recent spikes")
        
        if 'consciousness' in data:
            phi_data = data['consciousness'].get('phi_data', {})
            phi_values = phi_data.get('phi_history', [])
            if phi_values:
                report_lines.append(f"Current Φ: {phi_values[-1]:.4f}")
        
        if 'performance' in data:
            system_metrics = data['performance'].get('system_metrics', {})
            cpu_usage = system_metrics.get('cpu_usage_history', [])
            memory_usage = system_metrics.get('memory_usage_history', [])
            
            if cpu_usage:
                report_lines.append(f"CPU Usage: {cpu_usage[-1]:.1f}%")
            if memory_usage:
                report_lines.append(f"Memory Usage: {memory_usage[-1]:.1f}%")
        
        report_lines.extend(["", "="*60])
        
        # Save summary report
        summary_path = f"ultra_summary_{timestamp}.txt"
        with open(summary_path, 'w') as f:
            f.write('\n'.join(report_lines))
        
        self.logger.info(f"Generated summary report: {summary_path}")
        return summary_path
    
    def get_available_visualizations(self) -> Dict[str, Dict[str, Any]]:
        """Get information about available visualizations."""
        available = {}
        
        for viz_type, visualizer in self.visualizers.items():
            available[viz_type] = {
                'class': visualizer.__class__.__name__,
                'description': visualizer.__class__.__doc__.split('\n')[0] if visualizer.__class__.__doc__ else "",
                'modes_supported': ['static', 'interactive'] if hasattr(visualizer, 'create_interactive_plot') else ['static'],
                'export_formats': ['png', 'pdf', 'svg'],
                'animation_support': True,
                'real_time_support': True
            }
        
        return available
    
    def validate_data_format(self, viz_type: str, data: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """Validate data format for specific visualization type."""
        if viz_type not in self.visualizers:
            return False, [f"Unknown visualization type: {viz_type}"]
        
        errors = []
        
        # Define expected data structures for each visualization type
        expected_structures = {
            'neuromorphic': {
                'required': [],
                'optional': ['network_structure', 'spike_events', 'weight_changes']
            },
            'plasticity': {
                'required': [],
                'optional': ['stdp_events', 'weight_matrix', 'homeostatic_data']
            },
            'attention': {
                'required': [],
                'optional': ['attention_weights', 'multihead_attention', 'attention_history']
            },
            'diffusion': {
                'required': [],
                'optional': ['diffusion_trajectory', 'convergence_data', 'noise_schedule']
            },
            'consciousness': {
                'required': [],
                'optional': ['phi_data', 'integration_data', 'workspace_data']
            },
            'performance': {
                'required': [],
                'optional': ['system_metrics', 'neural_metrics', 'latency_metrics']
            }
        }
        
        if viz_type in expected_structures:
            structure = expected_structures[viz_type]
            
            # Check required fields
            for required_field in structure['required']:
                if required_field not in data:
                    errors.append(f"Missing required field: {required_field}")
            
            # Validate data types for known fields
            if viz_type == 'neuromorphic':
                if 'spike_events' in data and not isinstance(data['spike_events'], list):
                    errors.append("spike_events must be a list")
                
                if 'network_structure' in data:
                    network_data = data['network_structure']
                    if 'adjacency_matrix' in network_data:
                        adj_matrix = network_data['adjacency_matrix']
                        if not isinstance(adj_matrix, np.ndarray):
                            errors.append("adjacency_matrix must be a numpy array")
            
            elif viz_type == 'attention':
                if 'attention_weights' in data:
                    attention_weights = data['attention_weights']
                    if not isinstance(attention_weights, np.ndarray):
                        errors.append("attention_weights must be a numpy array")
                    elif attention_weights.ndim != 2:
                        errors.append("attention_weights must be a 2D array")
            
            elif viz_type == 'consciousness':
                if 'phi_data' in data:
                    phi_data = data['phi_data']
                    if 'phi_history' in phi_data and not isinstance(phi_data['phi_history'], list):
                        errors.append("phi_history must be a list")
        
        is_valid = len(errors) == 0
        return is_valid, errors
    
    def get_visualization_config(self) -> VisualizationConfig:
        """Get current visualization configuration."""
        return self.config
    
    def update_visualization_config(self, **kwargs) -> None:
        """Update visualization configuration."""
        for key, value in kwargs.items():
            if hasattr(self.config, key):
                setattr(self.config, key, value)
            else:
                self.logger.warning(f"Unknown configuration parameter: {key}")
        
        # Update all visualizers with new config
        for visualizer in self.visualizers.values():
            visualizer.config = self.config
            visualizer._setup_matplotlib_style()
    
    def cleanup_cache(self) -> int:
        """Clean up visualization cache and return number of items removed."""
        with self.cache_lock:
            cache_size = len(self.visualization_cache)
            self.visualization_cache.clear()
            
            # Also cleanup interactive manager
            cleaned_sessions = self.interactive_manager.cleanup_inactive_sessions()
            
            self.logger.info(f"Cleaned up {cache_size} cached visualizations and {cleaned_sessions} inactive sessions")
            return cache_size + cleaned_sessions

# =============================================================================
# Utility Functions and Module Exports
# =============================================================================

def create_ultra_visualizer(config: Optional[Dict[str, Any]] = None) -> ULTRAVisualizationFactory:
    """
    Create ULTRA visualization factory with optional configuration.
    
    Args:
        config: Optional configuration dictionary
        
    Returns:
        ULTRAVisualizationFactory instance
    """
    if config is None:
        viz_config = VisualizationConfig()
    else:
        viz_config = VisualizationConfig(**config)
    
    return ULTRAVisualizationFactory(viz_config)

def generate_sample_data(component_type: str, size: int = 100) -> Dict[str, Any]:
    """
    Generate sample data for testing visualizations.
    
    Args:
        component_type: Type of component to generate data for
        size: Size parameter for data generation
        
    Returns:
        Sample data dictionary
    """
    np.random.seed(42)  # For reproducible results
    
    if component_type == 'neuromorphic':
        return {
            'network_structure': {
                'positions': np.random.rand(size, 3),
                'adjacency_matrix': (np.random.rand(size, size) > 0.9).astype(float),
                'neuron_types': np.random.randint(0, 4, size),
                'membrane_potentials': np.random.randn(size) * 20 - 50,
                'firing_threshold': -50.0
            },
            'spike_events': [
                {
                    'neuron_id': np.random.randint(0, size),
                    'timestamp': time.time() - np.random.exponential(0.1),
                    'voltage': np.random.randn() * 5 - 45
                }
                for _ in range(min(size, 200))
            ],
            'weight_changes': {
                f'{i}_{j}': np.random.rand() * 0.1
                for i, j in zip(np.random.randint(0, size, 20), np.random.randint(0, size, 20))
            }
        }
    
    elif component_type == 'attention':
        seq_len = min(size, 128)
        return {
            'attention_weights': np.random.rand(seq_len, seq_len),
            'multihead_attention': {
                f'head_{i}': {
                    'attention_weights': np.random.rand(seq_len, seq_len)
                }
                for i in range(8)
            },
            'attention_history': [
                {
                    'attention_weights': np.random.rand(seq_len, seq_len),
                    'timestamp': time.time() - i * 0.1
                }
                for i in range(20)
            ]
        }
    
    elif component_type == 'diffusion':
        return {
            'diffusion_trajectory': [
                {
                    'step': i,
                    'concept_state': np.random.randn(min(size, 512)),
                    'noise_level': 0.1 * (1 - i/100),
                    'guidance_scale': 7.5,
                    'timestamp': time.time() - (100-i) * 0.01
                }
                for i in range(100)
            ],
            'convergence_data': {
                'convergence_history': np.exp(-np.arange(100) * 0.05) + np.random.randn(100) * 0.01,
                'entropy_history': np.random.rand(100) * 5
            },
            'noise_schedule': {
                'schedule_type': 'linear',
                'num_timesteps': 1000,
                'beta_start': 1e-4,
                'beta_end': 0.02
            }
        }
    
    elif component_type == 'consciousness':
        return {
            'phi_data': {
                'phi_history': np.random.exponential(0.5, size),
                'timestamps': [time.time() - i * 0.1 for i in range(size)],
                'network_sizes': np.random.randint(50, 200, size)
            },
            'integration_data': {
                'integration_matrix': np.random.rand(20, 20),
                'node_labels': [f'Node_{i}' for i in range(20)]
            },
            'workspace_data': {
                'contents': [f'Content_{i}' for i in range(5)],
                'competition_scores': np.random.rand(5),
                'broadcast_strength': np.random.rand()
            }
        }
    
    elif component_type == 'performance':
        return {
            'system_metrics': {
                'cpu_usage_history': np.random.rand(size) * 80 + 10,
                'memory_usage_history': np.random.rand(size) * 70 + 20,
                'gpu_usage_history': np.random.rand(size) * 90 + 5
            },
            'neural_metrics': {
                'spike_rates': np.random.exponential(50, size),
                'firing_rates': np.random.exponential(10, size),
                'avg_connection_weights': np.random.rand(size) * 0.5
            },
            'latency_metrics': {
                'latency_distribution': np.random.exponential(0.1, size),
                'avg_latency': 0.1,
                'p95_latency': 0.2
            }
        }
    
    else:
        raise ValueError(f"Unknown component type: {component_type}")

def validate_visualization_environment() -> Dict[str, bool]:
    """
    Validate visualization environment and available libraries.
    
    Returns:
        Dictionary of library availability
    """
    environment_status = {
        'matplotlib': True,  # Always available as it's imported
        'plotly': False,
        'bokeh': BOKEH_AVAILABLE,
        'mayavi': MAYAVI_AVAILABLE,
        'vtk': VTK_AVAILABLE,
        'seaborn': True,  # Imported at top
        'networkx': True,  # Imported at top
        'sklearn': True,  # Imported at top
    }
    
    # Check plotly availability
    try:
        import plotly
        environment_status['plotly'] = True
    except ImportError:
        pass
    
    return environment_status

def setup_visualization_logging(log_level: str = 'INFO') -> logging.Logger:
    """
    Setup logging for visualization module.
    
    Args:
        log_level: Logging level
        
    Returns:
        Configured logger
    """
    logger = logging.getLogger('ultra.visualization')
    logger.setLevel(getattr(logging, log_level.upper()))
    
    if not logger.handlers:
        handler = logging.StreamHandler()
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        handler.setFormatter(formatter)
        logger.addHandler(handler)
    
    return logger

# Module initialization
_environment_status = validate_visualization_environment()
_logger = setup_visualization_logging()

# Log module initialization
_logger.info("ULTRA Visualization System initialized successfully")
_logger.info(f"Available visualization libraries: {[k for k, v in _environment_status.items() if v]}")

if not MAYAVI_AVAILABLE:
    _logger.warning("Mayavi not available - 3D neuromorphic visualizations will fall back to 2D")

if not BOKEH_AVAILABLE:
    _logger.warning("Bokeh not available - interactive visualizations will use Plotly fallback")

# Export all classes and functions
__all__ = [
    # Configuration classes
    'VisualizationConfig', 'VisualizationMode', 'ColorScheme', 'PlotType',
    
    # Base classes
    'BaseVisualizer',
    
    # Specialized visualizers
    'NeuromorphicNetworkVisualizer', 'SynapticPlasticityVisualizer',
    'AttentionVisualizer', 'DiffusionProcessVisualizer',
    'ConsciousnessMetricsVisualizer', 'PerformanceDashboardVisualizer',
    
    # Management classes
    'InteractiveVisualizationManager', 'ULTRAVisualizationFactory',
    
    # Utility functions
    'create_ultra_visualizer', 'generate_sample_data', 
    'validate_visualization_environment', 'setup_visualization_logging',
    
    # Constants
    'ULTRA_VIZ_CONSTANTS', 'ULTRA_COLOR_PALETTES',
    
    # Environment status
    'MAYAVI_AVAILABLE', 'BOKEH_AVAILABLE', 'VTK_AVAILABLE'
]

# Performance optimization: Precompile frequently used mathematical operations
def _precompile_math_operations():
    """Precompile mathematical operations for better performance."""
    # Precompile numpy operations by running them once
    dummy_data = np.random.rand(100, 100)
    _ = np.corrcoef(dummy_data)
    _ = np.linalg.svd(dummy_data)
    _ = signal.find_peaks(dummy_data[0])

# Run precompilation
try:
    _precompile_math_operations()
    _logger.debug("Mathematical operations precompiled for optimal performance")
except Exception as e:
    _logger.warning(f"Failed to precompile mathematical operations: {e}")

_logger.info("ULTRA Visualization System ready for production use")
_logger.info(f"Module exports {len(__all__)} classes and functions")