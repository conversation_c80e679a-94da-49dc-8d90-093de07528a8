"""
ULTRA Configuration Utilities Module

This module provides advanced configuration management utilities for the ULTRA system.
It extends the base configuration system with additional tools for:

- Advanced validation and transformation
- Runtime configuration management
- Configuration templating and generation
- Environment-specific handling
- Performance optimization
- Security and encryption
- Migration and versioning
- Integration helpers
- Monitoring and analysis

All implementations follow production-grade standards and integrate with the core ULTRA architecture.
"""

import os
import sys
import json
import yaml
import toml
import logging
import hashlib
import base64
import secrets
import threading
import multiprocessing
import time
import copy
import re
import ast
import inspect
import importlib
import tempfile
import shutil
import pickle
import gzip
import sqlite3
from typing import (
    Any, Dict, List, Optional, Union, Tuple, Callable, Type, TypeVar, 
    Generic, Protocol, runtime_checkable, get_type_hints, Iterator,
    Set, FrozenSet, DefaultDict, OrderedDict as OrderedDictType
)
from dataclasses import dataclass, field, fields, asdict, is_dataclass
from collections import defaultdict, OrderedDict, deque, ChainMap
from pathlib import Path
from datetime import datetime, timedelta, timezone
from enum import Enum, auto, IntEnum
from abc import ABC, abstractmethod
from functools import wraps, lru_cache, partial, singledispatch
from contextlib import contextmanager, nullcontext
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
from threading import Lock, RLock, Condition, Event
from queue import Queue, Empty
import weakref

import numpy as np
import pandas as pd
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from cryptography.hazmat.primitives.asymmetric import rsa, padding
import jsonschema
from jsonschema import validate, ValidationError, Draft7Validator
import cerberus
from cerberus import Validator
import marshmallow
from marshmallow import Schema, fields as ma_fields, ValidationError as MaValidationError

# Try to import the main config system
try:
    from ..config import (
        get_config, SystemConfig, ConfigManager, ConfigEnvironment,
        ConfigurationError, ValidationError as ConfigValidationError
    )
except ImportError:
    try:
        from config import (
            get_config, SystemConfig, ConfigManager, ConfigEnvironment,
            ConfigurationError, ValidationError as ConfigValidationError
        )
    except ImportError:
        # Fallback minimal definitions if config not available
        class ConfigEnvironment(Enum):
            DEVELOPMENT = "development"
            TESTING = "testing" 
            PRODUCTION = "production"
        
        class ConfigurationError(Exception):
            pass
        
        class ConfigValidationError(Exception):
            pass
        
        def get_config():
            return {}

# Configure logging
logger = logging.getLogger(__name__)

# Type definitions
T = TypeVar('T')
ConfigValue = Union[str, int, float, bool, Dict[str, Any], List[Any]]
ConfigDict = Dict[str, ConfigValue]
ConfigPath = Union[str, Path]
ValidationRule = Callable[[Any], bool]
TransformFunction = Callable[[Any], Any]

# Constants
DEFAULT_CACHE_SIZE = 1000
DEFAULT_CACHE_TTL = 3600  # 1 hour
DEFAULT_ENCRYPTION_ITERATIONS = 100000
CONFIG_VERSION_PATTERN = r'^(\d+)\.(\d+)\.(\d+)$'
MAX_CONFIG_DEPTH = 10
SENSITIVE_KEYS = {
    'password', 'secret', 'key', 'token', 'credential', 
    'auth', 'private', 'secure', 'encrypted'
}

# ============================================================================
# Base Classes and Protocols
# ============================================================================

class ConfigChangeType(Enum):
    """Types of configuration changes."""
    ADDED = "added"
    MODIFIED = "modified"
    REMOVED = "removed"
    MOVED = "moved"

class ConfigFormat(Enum):
    """Supported configuration formats."""
    YAML = "yaml"
    JSON = "json"
    TOML = "toml"
    INI = "ini"
    PYTHON = "python"
    ENVIRONMENT = "environment"

class ValidationSeverity(Enum):
    """Validation severity levels."""
    ERROR = "error"
    WARNING = "warning"
    INFO = "info"

class CachePolicy(Enum):
    """Configuration caching policies."""
    NO_CACHE = "no_cache"
    MEMORY_CACHE = "memory_cache"
    PERSISTENT_CACHE = "persistent_cache"
    DISTRIBUTED_CACHE = "distributed_cache"

@runtime_checkable
class ConfigSerializable(Protocol):
    """Protocol for objects that can be serialized to configuration."""
    def to_config_dict(self) -> Dict[str, Any]:
        """Convert object to configuration dictionary."""
        ...
    
    @classmethod
    def from_config_dict(cls, config_dict: Dict[str, Any]) -> 'ConfigSerializable':
        """Create object from configuration dictionary."""
        ...

@runtime_checkable
class ConfigValidatable(Protocol):
    """Protocol for objects that can validate configuration."""
    def validate_config(self, config: Dict[str, Any]) -> List[str]:
        """Validate configuration and return list of errors."""
        ...

@dataclass
class ConfigChange:
    """Represents a change in configuration."""
    path: str
    change_type: ConfigChangeType
    old_value: Optional[Any] = None
    new_value: Optional[Any] = None
    timestamp: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class ValidationResult:
    """Result of configuration validation."""
    is_valid: bool
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    info: List[str] = field(default_factory=list)
    validation_time: float = 0.0
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class ConfigTemplate:
    """Configuration template definition."""
    name: str
    description: str
    template_data: Dict[str, Any]
    variables: Set[str] = field(default_factory=set)
    required_variables: Set[str] = field(default_factory=set)
    version: str = "1.0.0"
    tags: Set[str] = field(default_factory=set)
    created_at: datetime = field(default_factory=lambda: datetime.now(timezone.utc))

@dataclass
class ConfigProfile:
    """Configuration profile for different environments/use cases."""
    name: str
    description: str
    base_config: Dict[str, Any]
    overrides: Dict[str, Any] = field(default_factory=dict)
    environment: Optional[ConfigEnvironment] = None
    tags: Set[str] = field(default_factory=set)
    active: bool = True
    priority: int = 0

# ============================================================================
# Configuration Path Utilities
# ============================================================================

class ConfigPath:
    """Advanced configuration path manipulation utilities."""
    
    SEPARATOR = "."
    ARRAY_PATTERN = re.compile(r'^(.+)\[(\d+)\]$')
    WILDCARD_PATTERN = re.compile(r'^(.+)\[\*\]$')
    
    @staticmethod
    def normalize_path(path: str) -> str:
        """Normalize configuration path."""
        if not path:
            return ""
        
        # Remove leading/trailing separators
        path = path.strip(ConfigPath.SEPARATOR)
        
        # Replace multiple separators with single
        while ConfigPath.SEPARATOR * 2 in path:
            path = path.replace(ConfigPath.SEPARATOR * 2, ConfigPath.SEPARATOR)
        
        return path
    
    @staticmethod
    def split_path(path: str) -> List[str]:
        """Split configuration path into components."""
        normalized = ConfigPath.normalize_path(path)
        if not normalized:
            return []
        return normalized.split(ConfigPath.SEPARATOR)
    
    @staticmethod
    def join_path(*components: str) -> str:
        """Join path components."""
        filtered = [comp for comp in components if comp]
        return ConfigPath.SEPARATOR.join(filtered)
    
    @staticmethod
    def parent_path(path: str) -> str:
        """Get parent path."""
        components = ConfigPath.split_path(path)
        if len(components) <= 1:
            return ""
        return ConfigPath.join_path(*components[:-1])
    
    @staticmethod
    def path_exists(config: Dict[str, Any], path: str) -> bool:
        """Check if path exists in configuration."""
        try:
            ConfigPath.get_value(config, path)
            return True
        except (KeyError, IndexError, TypeError):
            return False
    
    @staticmethod
    def get_value(config: Dict[str, Any], path: str, default: Any = None) -> Any:
        """Get value at configuration path."""
        if not path:
            return config
        
        current = config
        components = ConfigPath.split_path(path)
        
        for component in components:
            # Handle array access
            array_match = ConfigPath.ARRAY_PATTERN.match(component)
            if array_match:
                key, index_str = array_match.groups()
                index = int(index_str)
                
                if isinstance(current, dict) and key in current:
                    current = current[key]
                    if isinstance(current, (list, tuple)) and 0 <= index < len(current):
                        current = current[index]
                    else:
                        raise IndexError(f"Array index {index} out of bounds")
                else:
                    raise KeyError(f"Key '{key}' not found")
            else:
                if isinstance(current, dict) and component in current:
                    current = current[component]
                else:
                    if default is not None:
                        return default
                    raise KeyError(f"Key '{component}' not found in path '{path}'")
        
        return current
    
    @staticmethod
    def set_value(config: Dict[str, Any], path: str, value: Any) -> None:
        """Set value at configuration path."""
        if not path:
            raise ValueError("Cannot set value at empty path")
        
        components = ConfigPath.split_path(path)
        current = config
        
        # Navigate to parent
        for component in components[:-1]:
            array_match = ConfigPath.ARRAY_PATTERN.match(component)
            if array_match:
                key, index_str = array_match.groups()
                index = int(index_str)
                
                if key not in current:
                    current[key] = []
                
                # Extend list if necessary
                while len(current[key]) <= index:
                    current[key].append({})
                
                current = current[key][index]
            else:
                if component not in current:
                    current[component] = {}
                current = current[component]
        
        # Set final value
        final_component = components[-1]
        array_match = ConfigPath.ARRAY_PATTERN.match(final_component)
        if array_match:
            key, index_str = array_match.groups()
            index = int(index_str)
            
            if key not in current:
                current[key] = []
            
            # Extend list if necessary
            while len(current[key]) <= index:
                current[key].append(None)
            
            current[key][index] = value
        else:
            current[final_component] = value
    
    @staticmethod
    def delete_value(config: Dict[str, Any], path: str) -> bool:
        """Delete value at configuration path."""
        if not ConfigPath.path_exists(config, path):
            return False
        
        components = ConfigPath.split_path(path)
        if len(components) == 1:
            if components[0] in config:
                del config[components[0]]
                return True
            return False
        
        parent_path = ConfigPath.join_path(*components[:-1])
        parent = ConfigPath.get_value(config, parent_path)
        
        final_component = components[-1]
        array_match = ConfigPath.ARRAY_PATTERN.match(final_component)
        if array_match:
            key, index_str = array_match.groups()
            index = int(index_str)
            
            if isinstance(parent, dict) and key in parent:
                if isinstance(parent[key], list) and 0 <= index < len(parent[key]):
                    parent[key].pop(index)
                    return True
        else:
            if isinstance(parent, dict) and final_component in parent:
                del parent[final_component]
                return True
        
        return False
    
    @staticmethod
    def find_paths(config: Dict[str, Any], 
                   predicate: Callable[[str, Any], bool],
                   max_depth: int = MAX_CONFIG_DEPTH) -> List[str]:
        """Find all paths matching predicate."""
        paths = []
        
        def _search(obj: Any, current_path: str, depth: int) -> None:
            if depth > max_depth:
                return
            
            if predicate(current_path, obj):
                paths.append(current_path)
            
            if isinstance(obj, dict):
                for key, value in obj.items():
                    new_path = ConfigPath.join_path(current_path, key) if current_path else key
                    _search(value, new_path, depth + 1)
            elif isinstance(obj, (list, tuple)):
                for i, value in enumerate(obj):
                    new_path = f"{current_path}[{i}]" if current_path else f"[{i}]"
                    _search(value, new_path, depth + 1)
        
        _search(config, "", 0)
        return paths

# ============================================================================
# Advanced Configuration Validators
# ============================================================================

class ConfigValidator:
    """Advanced configuration validation with mathematical constraints."""
    
    def __init__(self):
        """Initialize validator."""
        self.rules = {}
        self.custom_validators = {}
        self.schemas = {}
        
    def add_rule(self, path: str, rule: ValidationRule, message: str = "") -> None:
        """Add validation rule for a specific path."""
        if path not in self.rules:
            self.rules[path] = []
        self.rules[path].append({
            'rule': rule,
            'message': message or f"Validation failed for {path}"
        })
    
    def add_mathematical_constraint(self, 
                                  path: str,
                                  constraint_type: str,
                                  **kwargs) -> None:
        """Add mathematical constraint validation."""
        if constraint_type == "range":
            min_val = kwargs.get('min_value')
            max_val = kwargs.get('max_value')
            
            def range_validator(value):
                try:
                    num_val = float(value)
                    return (min_val is None or num_val >= min_val) and \
                           (max_val is None or num_val <= max_val)
                except (ValueError, TypeError):
                    return False
            
            message = f"Value must be in range [{min_val}, {max_val}]"
            self.add_rule(path, range_validator, message)
            
        elif constraint_type == "probability":
            def prob_validator(value):
                try:
                    num_val = float(value)
                    return 0.0 <= num_val <= 1.0
                except (ValueError, TypeError):
                    return False
            
            self.add_rule(path, prob_validator, "Value must be a probability in [0, 1]")
            
        elif constraint_type == "positive":
            def positive_validator(value):
                try:
                    return float(value) > 0
                except (ValueError, TypeError):
                    return False
            
            self.add_rule(path, positive_validator, "Value must be positive")
            
        elif constraint_type == "non_negative":
            def non_negative_validator(value):
                try:
                    return float(value) >= 0
                except (ValueError, TypeError):
                    return False
            
            self.add_rule(path, non_negative_validator, "Value must be non-negative")
            
        elif constraint_type == "power_of_two":
            def power_of_two_validator(value):
                try:
                    num_val = int(value)
                    return num_val > 0 and (num_val & (num_val - 1)) == 0
                except (ValueError, TypeError):
                    return False
            
            self.add_rule(path, power_of_two_validator, "Value must be a power of 2")
            
        elif constraint_type == "divisible":
            divisor = kwargs.get('divisor', 1)
            
            def divisible_validator(value):
                try:
                    return int(value) % divisor == 0
                except (ValueError, TypeError):
                    return False
            
            self.add_rule(path, divisible_validator, f"Value must be divisible by {divisor}")
            
        elif constraint_type == "vector_dimension":
            expected_dim = kwargs.get('dimension')
            
            def dimension_validator(value):
                if isinstance(value, (list, tuple)):
                    return len(value) == expected_dim
                elif isinstance(value, np.ndarray):
                    return value.shape[-1] == expected_dim
                return False
            
            self.add_rule(path, dimension_validator, f"Vector must have dimension {expected_dim}")
            
        elif constraint_type == "matrix_shape":
            expected_shape = kwargs.get('shape')
            
            def shape_validator(value):
                if isinstance(value, (list, tuple)):
                    # Check nested list structure
                    if len(expected_shape) == 2:
                        return (len(value) == expected_shape[0] and
                               all(len(row) == expected_shape[1] for row in value))
                elif isinstance(value, np.ndarray):
                    return value.shape == expected_shape
                return False
            
            self.add_rule(path, shape_validator, f"Matrix must have shape {expected_shape}")
            
        elif constraint_type == "sum_to_one":
            tolerance = kwargs.get('tolerance', 1e-6)
            
            def sum_validator(value):
                try:
                    if isinstance(value, (list, tuple)):
                        total = sum(float(x) for x in value)
                    elif isinstance(value, np.ndarray):
                        total = float(np.sum(value))
                    else:
                        return False
                    return abs(total - 1.0) <= tolerance
                except (ValueError, TypeError):
                    return False
            
            self.add_rule(path, sum_validator, f"Values must sum to 1 (tolerance: {tolerance})")
            
        elif constraint_type == "correlation_matrix":
            def correlation_validator(value):
                try:
                    if isinstance(value, (list, tuple)):
                        matrix = np.array(value)
                    elif isinstance(value, np.ndarray):
                        matrix = value
                    else:
                        return False
                    
                    # Check square matrix
                    if matrix.ndim != 2 or matrix.shape[0] != matrix.shape[1]:
                        return False
                    
                    # Check symmetric
                    if not np.allclose(matrix, matrix.T):
                        return False
                    
                    # Check diagonal is ones
                    if not np.allclose(np.diag(matrix), 1.0):
                        return False
                    
                    # Check values in [-1, 1]
                    if not np.all((-1 <= matrix) & (matrix <= 1)):
                        return False
                    
                    # Check positive semi-definite
                    eigenvals = np.linalg.eigvals(matrix)
                    return np.all(eigenvals >= -1e-8)  # Allow small numerical errors
                    
                except (ValueError, TypeError, np.linalg.LinAlgError):
                    return False
            
            self.add_rule(path, correlation_validator, "Must be a valid correlation matrix")
    
    def add_neural_constraints(self) -> None:
        """Add common neural network parameter constraints."""
        # Membrane time constants
        self.add_mathematical_constraint(
            "core_neural.neuron_params.tau_m",
            "positive"
        )
        
        # Threshold and reset potentials
        self.add_rule(
            "core_neural.neuron_params",
            lambda params: params.get('v_threshold', 0) > params.get('v_rest', -70),
            "Threshold potential must be greater than resting potential"
        )
        
        # STDP parameters
        self.add_mathematical_constraint(
            "core_neural.stdp_params.a_plus",
            "positive"
        )
        self.add_mathematical_constraint(
            "core_neural.stdp_params.a_minus", 
            "positive"
        )
        
        # Network size constraints
        self.add_rule(
            "core_neural.network_size",
            lambda size: isinstance(size, (list, tuple)) and len(size) == 3 and all(x > 0 for x in size),
            "Network size must be 3D tuple with positive dimensions"
        )
        
        # Excitatory ratio
        self.add_mathematical_constraint(
            "core_neural.excitatory_ratio",
            "probability"
        )
    
    def add_transformer_constraints(self) -> None:
        """Add transformer architecture constraints."""
        # Model dimension divisibility
        self.add_rule(
            "hyper_transformer.transformer_params",
            lambda params: params.get('d_model', 512) % params.get('n_heads', 8) == 0,
            "Model dimension must be divisible by number of attention heads"
        )
        
        # Attention heads
        self.add_mathematical_constraint(
            "hyper_transformer.transformer_params.n_heads",
            "positive"
        )
        
        # Dropout rate
        self.add_mathematical_constraint(
            "hyper_transformer.transformer_params.dropout",
            "probability"
        )
        
        # Recursion depth
        self.add_mathematical_constraint(
            "hyper_transformer.recursive_transformer_params.max_recursion_depth",
            "positive"
        )
        
        # Confidence threshold
        self.add_mathematical_constraint(
            "hyper_transformer.recursive_transformer_params.confidence_threshold",
            "probability"
        )
    
    def add_diffusion_constraints(self) -> None:
        """Add diffusion model constraints."""
        # Beta schedule
        self.add_rule(
            "diffusion_reasoning.diffusion_params",
            lambda params: params.get('beta_start', 1e-4) < params.get('beta_end', 0.02),
            "Beta start must be less than beta end"
        )
        
        # Timesteps
        self.add_mathematical_constraint(
            "diffusion_reasoning.diffusion_params.num_timesteps",
            "positive"
        )
        
        # Latent space dimension
        self.add_mathematical_constraint(
            "diffusion_reasoning.latent_space_params.dimension",
            "positive"
        )
        
        # Hierarchical levels
        self.add_mathematical_constraint(
            "diffusion_reasoning.latent_space_params.hierarchical_levels",
            "positive"
        )
    
    def validate(self, config: Dict[str, Any]) -> ValidationResult:
        """Validate configuration against all rules."""
        start_time = time.time()
        errors = []
        warnings = []
        info = []
        
        # Validate each rule
        for path, path_rules in self.rules.items():
            try:
                if ConfigPath.path_exists(config, path):
                    value = ConfigPath.get_value(config, path)
                    
                    for rule_info in path_rules:
                        try:
                            if not rule_info['rule'](value):
                                errors.append(f"{path}: {rule_info['message']}")
                        except Exception as e:
                            errors.append(f"{path}: Validation error - {str(e)}")
                else:
                    # Check if path is required
                    info.append(f"Optional path '{path}' not present in configuration")
            except Exception as e:
                errors.append(f"Error validating path '{path}': {str(e)}")
        
        # Additional semantic validation
        try:
            semantic_errors = self._validate_semantic_constraints(config)
            errors.extend(semantic_errors)
        except Exception as e:
            errors.append(f"Semantic validation error: {str(e)}")
        
        validation_time = time.time() - start_time
        
        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings,
            info=info,
            validation_time=validation_time
        )
    
    def _validate_semantic_constraints(self, config: Dict[str, Any]) -> List[str]:
        """Validate semantic constraints across multiple configuration paths."""
        errors = []
        
        try:
            # Neural network consistency checks
            if ConfigPath.path_exists(config, "core_neural"):
                neural_config = ConfigPath.get_value(config, "core_neural")
                
                # Check connectivity constraints
                network_size = neural_config.get('network_size', (100, 100, 100))
                connectivity_density = neural_config.get('connectivity_density', 0.1)
                total_neurons = np.prod(network_size)
                
                if connectivity_density * total_neurons > 1e6:
                    errors.append("Network connectivity may be too dense for practical computation")
                
                # Check plasticity parameter relationships
                if ConfigPath.path_exists(config, "core_neural.stdp_params"):
                    stdp = ConfigPath.get_value(config, "core_neural.stdp_params")
                    if stdp.get('a_plus', 0.005) < stdp.get('a_minus', 0.0025):
                        errors.append("STDP: LTP amplitude should typically be larger than LTD amplitude")
        
            # Transformer dimension checks
            if ConfigPath.path_exists(config, "hyper_transformer.transformer_params"):
                transformer = ConfigPath.get_value(config, "hyper_transformer.transformer_params")
                d_model = transformer.get('d_model', 512)
                d_ff = transformer.get('d_ff', 2048)
                
                if d_ff < d_model:
                    errors.append("Feed-forward dimension should typically be larger than model dimension")
        
            # Memory and computational constraints
            if ConfigPath.path_exists(config, "max_memory_usage"):
                max_memory = config.get('max_memory_usage', '16GB')
                memory_gb = float(max_memory.replace('GB', '').replace('gb', ''))
                
                # Estimate memory requirements
                estimated_memory = self._estimate_memory_usage(config)
                if estimated_memory > memory_gb * 0.9:  # 90% threshold
                    errors.append(f"Estimated memory usage ({estimated_memory:.1f}GB) "
                                f"exceeds available memory ({memory_gb}GB)")
                
        except Exception as e:
            errors.append(f"Semantic validation internal error: {str(e)}")
        
        return errors
    
    def _estimate_memory_usage(self, config: Dict[str, Any]) -> float:
        """Estimate memory usage from configuration."""
        total_memory_gb = 0.0
        
        try:
            # Neural network memory
            if ConfigPath.path_exists(config, "core_neural.network_size"):
                network_size = ConfigPath.get_value(config, "core_neural.network_size")
                total_neurons = np.prod(network_size)
                # Rough estimate: 1KB per neuron for basic parameters
                neural_memory = total_neurons * 1e-6  # GB
                total_memory_gb += neural_memory
            
            # Transformer memory
            if ConfigPath.path_exists(config, "hyper_transformer.transformer_params"):
                transformer = ConfigPath.get_value(config, "hyper_transformer.transformer_params")
                d_model = transformer.get('d_model', 512)
                max_seq_len = transformer.get('max_seq_length', 1024)
                n_heads = transformer.get('n_heads', 8)
                
                # Attention matrices memory
                attention_memory = (max_seq_len ** 2 * n_heads * 4) * 1e-9  # GB (float32)
                # Model parameters memory
                model_memory = (d_model * d_model * 4) * 1e-9  # GB
                
                total_memory_gb += attention_memory + model_memory
        
        except Exception:
            # Return conservative estimate if calculation fails
            total_memory_gb = 8.0
        
        return max(total_memory_gb * 1.5, 2.0)  # Safety margin

# ============================================================================
# Configuration Templates and Generation
# ============================================================================

class ConfigTemplateEngine:
    """Advanced configuration template engine with variable substitution."""
    
    def __init__(self):
        """Initialize template engine."""
        self.templates = {}
        self.variables = {}
        self.functions = {}
        self._setup_builtin_functions()
    
    def _setup_builtin_functions(self) -> None:
        """Setup built-in template functions."""
        self.functions.update({
            'range': lambda start, end, step=1: list(range(start, end, step)),
            'linspace': lambda start, end, num: np.linspace(start, end, num).tolist(),
            'logspace': lambda start, end, num: np.logspace(start, end, num).tolist(),
            'random_uniform': lambda low, high, size=1: np.random.uniform(low, high, size).tolist(),
            'random_normal': lambda mean, std, size=1: np.random.normal(mean, std, size).tolist(),
            'zeros': lambda *shape: np.zeros(shape).tolist(),
            'ones': lambda *shape: np.ones(shape).tolist(),
            'eye': lambda n: np.eye(n).tolist(),
            'calculate_stdp_window': self._calculate_stdp_window,
            'generate_connectivity_matrix': self._generate_connectivity_matrix,
            'calculate_diffusion_schedule': self._calculate_diffusion_schedule,
            'generate_oscillation_frequencies': self._generate_oscillation_frequencies
        })
    
    def _calculate_stdp_window(self, tau_plus: float = 20.0, tau_minus: float = 20.0, 
                              window_size: float = 100.0, resolution: float = 1.0) -> Dict[str, List[float]]:
        """Calculate STDP learning window."""
        dt_values = np.arange(-window_size, window_size + resolution, resolution)
        stdp_values = np.zeros_like(dt_values)
        
        # LTP for positive dt
        positive_mask = dt_values > 0
        stdp_values[positive_mask] = np.exp(-dt_values[positive_mask] / tau_plus)
        
        # LTD for negative dt
        negative_mask = dt_values < 0
        stdp_values[negative_mask] = -np.exp(dt_values[negative_mask] / tau_minus)
        
        return {
            'dt_values': dt_values.tolist(),
            'stdp_values': stdp_values.tolist(),
            'tau_plus': tau_plus,
            'tau_minus': tau_minus
        }
    
    def _generate_connectivity_matrix(self, n_neurons: int, connection_prob: float = 0.1,
                                    spatial_decay: float = 0.1) -> List[List[float]]:
        """Generate connectivity matrix with spatial decay."""
        # Create spatial positions
        positions = np.random.rand(n_neurons, 2)  # 2D positions
        
        # Calculate distances
        distances = np.linalg.norm(positions[:, np.newaxis] - positions[np.newaxis, :], axis=2)
        
        # Calculate connection probabilities with spatial decay
        prob_matrix = connection_prob * np.exp(-distances / spatial_decay)
        
        # Remove self-connections
        np.fill_diagonal(prob_matrix, 0)
        
        # Generate binary connectivity
        connectivity = (np.random.rand(n_neurons, n_neurons) < prob_matrix).astype(float)
        
        return connectivity.tolist()
    
    def _calculate_diffusion_schedule(self, num_timesteps: int = 1000, 
                                    schedule_type: str = "linear",
                                    beta_start: float = 1e-4, 
                                    beta_end: float = 0.02) -> Dict[str, List[float]]:
        """Calculate diffusion noise schedule."""
        if schedule_type == "linear":
            betas = np.linspace(beta_start, beta_end, num_timesteps)
        elif schedule_type == "quadratic":
            betas = np.linspace(beta_start**0.5, beta_end**0.5, num_timesteps)**2
        elif schedule_type == "cosine":
            def cosine_schedule(t):
                return np.cos((t / num_timesteps + 0.008) / 1.008 * np.pi / 2)**2
            
            alphas_cumprod = np.array([cosine_schedule(t) for t in range(num_timesteps)])
            betas = 1 - alphas_cumprod / np.concatenate([[1], alphas_cumprod[:-1]])
            betas = np.clip(betas, beta_start, beta_end)
        else:
            raise ValueError(f"Unknown schedule type: {schedule_type}")
        
        alphas = 1 - betas
        alphas_cumprod = np.cumprod(alphas)
        
        return {
            'betas': betas.tolist(),
            'alphas': alphas.tolist(),
            'alphas_cumprod': alphas_cumprod.tolist(),
            'schedule_type': schedule_type,
            'num_timesteps': num_timesteps
        }
    
    def _generate_oscillation_frequencies(self, base_frequencies: Dict[str, float],
                                        harmonics: int = 3,
                                        jitter: float = 0.1) -> Dict[str, List[float]]:
        """Generate oscillation frequency sets with harmonics."""
        result = {}
        
        for band_name, base_freq in base_frequencies.items():
            frequencies = [base_freq]
            
            # Add harmonics
            for h in range(2, harmonics + 1):
                harmonic_freq = base_freq * h
                # Add jitter
                jittered_freq = harmonic_freq * (1 + np.random.uniform(-jitter, jitter))
                frequencies.append(jittered_freq)
            
            result[f"{band_name}_frequencies"] = frequencies
        
        return result
    
    def register_template(self, template: ConfigTemplate) -> None:
        """Register a configuration template."""
        self.templates[template.name] = template
    
    def create_template_from_config(self, name: str, config: Dict[str, Any],
                                  variable_patterns: List[str]) -> ConfigTemplate:
        """Create template from existing configuration."""
        template_data = copy.deepcopy(config)
        variables = set()
        required_variables = set()
        
        # Extract variables based on patterns
        for pattern in variable_patterns:
            paths = ConfigPath.find_paths(
                template_data,
                lambda path, value: isinstance(value, str) and pattern in str(value)
            )
            
            for path in paths:
                # Replace with template variable
                original_value = ConfigPath.get_value(template_data, path)
                if isinstance(original_value, str):
                    # Extract variable name from pattern
                    import re
                    var_matches = re.findall(r'\{(\w+)\}', pattern)
                    for var_name in var_matches:
                        variables.add(var_name)
                        template_var = f"${{{var_name}}}"
                        new_value = original_value.replace(pattern, template_var)
                        ConfigPath.set_value(template_data, path, new_value)
        
        template = ConfigTemplate(
            name=name,
            description=f"Generated template from configuration",
            template_data=template_data,
            variables=variables,
            required_variables=required_variables
        )
        
        self.register_template(template)
        return template
    
    def instantiate_template(self, template_name: str, 
                           variables: Dict[str, Any]) -> Dict[str, Any]:
        """Instantiate template with provided variables."""
        if template_name not in self.templates:
            raise ValueError(f"Template '{template_name}' not found")
        
        template = self.templates[template_name]
        
        # Check required variables
        missing_vars = template.required_variables - set(variables.keys())
        if missing_vars:
            raise ValueError(f"Missing required variables: {missing_vars}")
        
        # Deep copy template data
        result = copy.deepcopy(template.template_data)
        
        # Perform variable substitution
        self._substitute_variables(result, variables)
        
        return result
    
    def _substitute_variables(self, obj: Any, variables: Dict[str, Any]) -> Any:
        """Recursively substitute variables in configuration object."""
        if isinstance(obj, dict):
            for key, value in obj.items():
                obj[key] = self._substitute_variables(value, variables)
        elif isinstance(obj, list):
            for i, item in enumerate(obj):
                obj[i] = self._substitute_variables(item, variables)
        elif isinstance(obj, str):
            # Substitute template variables
            result = obj
            for var_name, var_value in variables.items():
                placeholder = f"${{{var_name}}}"
                if placeholder in result:
                    result = result.replace(placeholder, str(var_value))
            
            # Evaluate function calls
            result = self._evaluate_template_functions(result)
            
            return result
        
        return obj
    
    def _evaluate_template_functions(self, text: str) -> Any:
        """Evaluate template function calls in text."""
        import re
        
        # Pattern to match function calls: #{function_name(args)}
        function_pattern = r'#\{(\w+)\((.*?)\)\}'
        
        def replace_function(match):
            func_name = match.group(1)
            args_str = match.group(2)
            
            if func_name in self.functions:
                try:
                    # Parse arguments
                    if args_str.strip():
                        # Simple argument parsing (supports numbers, strings, lists)
                        args = []
                        for arg in args_str.split(','):
                            arg = arg.strip()
                            try:
                                # Try to evaluate as literal
                                args.append(ast.literal_eval(arg))
                            except:
                                # Treat as string
                                args.append(arg.strip('"\''))
                    else:
                        args = []
                    
                    # Call function
                    result = self.functions[func_name](*args)
                    return json.dumps(result) if not isinstance(result, str) else str(result)
                    
                except Exception as e:
                    logger.warning(f"Template function error: {func_name} - {e}")
                    return match.group(0)  # Return original if evaluation fails
            
            return match.group(0)  # Return original if function not found
        
        result = re.sub(function_pattern, replace_function, text)
        
        # Try to parse result as JSON if it looks like structured data
        if result.startswith(('{', '[')):
            try:
                return json.loads(result)
            except:
                pass
        
        return result
    
    def generate_neural_config_template(self) -> ConfigTemplate:
        """Generate neural network configuration template."""
        template_data = {
            "core_neural": {
                "enabled": True,
                "neuron_model": "${neuron_model}",
                "neuron_params": {
                    "tau_m": "${tau_m}",
                    "v_rest": "${v_rest}",
                    "v_threshold": "${v_threshold}",
                    "v_reset": "${v_reset}",
                    "refractory_period": "${refractory_period}"
                },
                "network_size": "#{range(${network_dim}, ${network_dim}, 1)}",
                "excitatory_ratio": "${excitatory_ratio}",
                "stdp_params": "#{calculate_stdp_window(${tau_plus}, ${tau_minus})}",
                "connectivity_matrix": "#{generate_connectivity_matrix(${total_neurons}, ${connection_prob})}"
            }
        }
        
        template = ConfigTemplate(
            name="neural_network_basic",
            description="Basic neural network configuration template",
            template_data=template_data,
            variables={
                'neuron_model', 'tau_m', 'v_rest', 'v_threshold', 'v_reset',
                'refractory_period', 'network_dim', 'excitatory_ratio',
                'tau_plus', 'tau_minus', 'total_neurons', 'connection_prob'
            },
            required_variables={
                'neuron_model', 'tau_m', 'v_threshold', 'network_dim'
            },
            tags={'neural', 'basic', 'stdp'}
        )
        
        self.register_template(template)
        return template
    
    def generate_transformer_config_template(self) -> ConfigTemplate:
        """Generate transformer configuration template."""
        template_data = {
            "hyper_transformer": {
                "enabled": True,
                "transformer_params": {
                    "d_model": "${d_model}",
                    "n_heads": "${n_heads}",
                    "d_ff": "#{int(${d_model} * ${ff_multiplier})}",
                    "max_seq_length": "${max_seq_length}",
                    "dropout": "${dropout}",
                    "layer_norm_eps": 1e-5,
                    "attention_temperature": "${attention_temperature}"
                },
                "recursive_transformer_params": {
                    "max_recursion_depth": "${max_recursion_depth}",
                    "confidence_threshold": "${confidence_threshold}",
                    "halting_regularization": 0.01
                },
                "dynamic_attention_learning_rate": "${attention_lr}"
            }
        }
        
        template = ConfigTemplate(
            name="transformer_advanced",
            description="Advanced transformer configuration template",
            template_data=template_data,
            variables={
                'd_model', 'n_heads', 'ff_multiplier', 'max_seq_length',
                'dropout', 'attention_temperature', 'max_recursion_depth',
                'confidence_threshold', 'attention_lr'
            },
            required_variables={
                'd_model', 'n_heads', 'max_seq_length'
            },
            tags={'transformer', 'attention', 'recursive'}
        )
        
        self.register_template(template)
        return template

# ============================================================================
# Configuration Monitoring and Change Detection
# ============================================================================

class ConfigMonitor:
    """Advanced configuration monitoring with change detection."""
    
    def __init__(self, check_interval: float = 1.0):
        """Initialize configuration monitor."""
        self.check_interval = check_interval
        self.watched_configs = {}
        self.change_handlers = defaultdict(list)
        self.monitoring_thread = None
        self.monitoring_active = False
        self._lock = threading.Lock()
        self.change_history = deque(maxlen=1000)
    
    def watch_config(self, config_id: str, config_source: Union[Path, Callable]) -> None:
        """Start watching a configuration source for changes."""
        with self._lock:
            if isinstance(config_source, (str, Path)):
                config_path = Path(config_source)
                self.watched_configs[config_id] = {
                    'type': 'file',
                    'source': config_path,
                    'last_modified': config_path.stat().st_mtime if config_path.exists() else 0,
                    'last_content_hash': self._compute_file_hash(config_path),
                    'last_config': self._load_config_file(config_path)
                }
            elif callable(config_source):
                self.watched_configs[config_id] = {
                    'type': 'function',
                    'source': config_source,
                    'last_content_hash': None,
                    'last_config': config_source()
                }
            else:
                raise ValueError(f"Unsupported config source type: {type(config_source)}")
    
    def add_change_handler(self, config_id: str, 
                          handler: Callable[[str, List[ConfigChange]], None]) -> None:
        """Add handler for configuration changes."""
        self.change_handlers[config_id].append(handler)
    
    def start_monitoring(self) -> None:
        """Start background monitoring."""
        if self.monitoring_active:
            return
        
        self.monitoring_active = True
        self.monitoring_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self.monitoring_thread.start()
        logger.info("Configuration monitoring started")
    
    def stop_monitoring(self) -> None:
        """Stop background monitoring."""
        self.monitoring_active = False
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=5.0)
        logger.info("Configuration monitoring stopped")
    
    def _monitoring_loop(self) -> None:
        """Main monitoring loop."""
        while self.monitoring_active:
            try:
                with self._lock:
                    for config_id, config_info in self.watched_configs.items():
                        changes = self._check_for_changes(config_id, config_info)
                        if changes:
                            self._handle_changes(config_id, changes)
                
                time.sleep(self.check_interval)
                
            except Exception as e:
                logger.error(f"Configuration monitoring error: {e}")
                time.sleep(self.check_interval)
    
    def _check_for_changes(self, config_id: str, config_info: Dict[str, Any]) -> List[ConfigChange]:
        """Check for changes in a watched configuration."""
        changes = []
        
        try:
            if config_info['type'] == 'file':
                config_path = config_info['source']
                
                if not config_path.exists():
                    if config_info['last_config'] is not None:
                        # File was deleted
                        changes.append(ConfigChange(
                            path="",
                            change_type=ConfigChangeType.REMOVED,
                            old_value=config_info['last_config'],
                            new_value=None
                        ))
                        config_info['last_config'] = None
                else:
                    current_modified = config_path.stat().st_mtime
                    current_hash = self._compute_file_hash(config_path)
                    
                    if (current_modified != config_info['last_modified'] or
                        current_hash != config_info['last_content_hash']):
                        
                        # File changed
                        try:
                            current_config = self._load_config_file(config_path)
                            changes.extend(self._compute_config_diff(
                                config_info['last_config'], current_config
                            ))
                            
                            config_info['last_modified'] = current_modified
                            config_info['last_content_hash'] = current_hash
                            config_info['last_config'] = current_config
                            
                        except Exception as e:
                            logger.error(f"Error loading changed config {config_id}: {e}")
            
            elif config_info['type'] == 'function':
                try:
                    current_config = config_info['source']()
                    changes.extend(self._compute_config_diff(
                        config_info['last_config'], current_config
                    ))
                    config_info['last_config'] = current_config
                    
                except Exception as e:
                    logger.error(f"Error getting config from function {config_id}: {e}")
        
        except Exception as e:
            logger.error(f"Error checking changes for {config_id}: {e}")
        
        return changes
    
    def _compute_file_hash(self, file_path: Path) -> str:
        """Compute hash of file content."""
        if not file_path.exists():
            return ""
        
        try:
            with open(file_path, 'rb') as f:
                content = f.read()
            return hashlib.sha256(content).hexdigest()
        except Exception:
            return ""
    
    def _load_config_file(self, file_path: Path) -> Optional[Dict[str, Any]]:
        """Load configuration from file."""
        if not file_path.exists():
            return None
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                if file_path.suffix.lower() in ['.yml', '.yaml']:
                    return yaml.safe_load(f)
                elif file_path.suffix.lower() == '.json':
                    return json.load(f)
                elif file_path.suffix.lower() == '.toml':
                    return toml.load(f)
                else:
                    return yaml.safe_load(f)  # Default to YAML
        except Exception as e:
            logger.error(f"Error loading config file {file_path}: {e}")
            return None
    
    def _compute_config_diff(self, old_config: Optional[Dict[str, Any]], 
                           new_config: Optional[Dict[str, Any]]) -> List[ConfigChange]:
        """Compute differences between two configurations."""
        changes = []
        
        if old_config is None and new_config is None:
            return changes
        
        if old_config is None:
            # All new values added
            all_paths = ConfigPath.find_paths(new_config, lambda p, v: True)
            for path in all_paths:
                changes.append(ConfigChange(
                    path=path,
                    change_type=ConfigChangeType.ADDED,
                    old_value=None,
                    new_value=ConfigPath.get_value(new_config, path)
                ))
            return changes
        
        if new_config is None:
            # All values removed
            all_paths = ConfigPath.find_paths(old_config, lambda p, v: True)
            for path in all_paths:
                changes.append(ConfigChange(
                    path=path,
                    change_type=ConfigChangeType.REMOVED,
                    old_value=ConfigPath.get_value(old_config, path),
                    new_value=None
                ))
            return changes
        
        # Find all paths in both configurations
        old_paths = set(ConfigPath.find_paths(old_config, lambda p, v: True))
        new_paths = set(ConfigPath.find_paths(new_config, lambda p, v: True))
        
        # Added paths
        for path in new_paths - old_paths:
            changes.append(ConfigChange(
                path=path,
                change_type=ConfigChangeType.ADDED,
                old_value=None,
                new_value=ConfigPath.get_value(new_config, path)
            ))
        
        # Removed paths
        for path in old_paths - new_paths:
            changes.append(ConfigChange(
                path=path,
                change_type=ConfigChangeType.REMOVED,
                old_value=ConfigPath.get_value(old_config, path),
                new_value=None
            ))
        
        # Modified paths
        for path in old_paths & new_paths:
            old_value = ConfigPath.get_value(old_config, path)
            new_value = ConfigPath.get_value(new_config, path)
            
            if not self._values_equal(old_value, new_value):
                changes.append(ConfigChange(
                    path=path,
                    change_type=ConfigChangeType.MODIFIED,
                    old_value=old_value,
                    new_value=new_value
                ))
        
        return changes
    
    def _values_equal(self, val1: Any, val2: Any) -> bool:
        """Check if two values are equal, handling numpy arrays and floating point precision."""
        if type(val1) != type(val2):
            return False
        
        if isinstance(val1, (list, tuple)):
            if len(val1) != len(val2):
                return False
            return all(self._values_equal(v1, v2) for v1, v2 in zip(val1, val2))
        
        if isinstance(val1, dict):
            if set(val1.keys()) != set(val2.keys()):
                return False
            return all(self._values_equal(val1[k], val2[k]) for k in val1.keys())
        
        if isinstance(val1, (int, float)) and isinstance(val2, (int, float)):
            return abs(val1 - val2) < 1e-10
        
        if isinstance(val1, np.ndarray) and isinstance(val2, np.ndarray):
            return np.allclose(val1, val2, rtol=1e-10, atol=1e-10)
        
        return val1 == val2
    
    def _handle_changes(self, config_id: str, changes: List[ConfigChange]) -> None:
        """Handle detected configuration changes."""
        # Record changes in history
        for change in changes:
            self.change_history.append(change)
        
        # Call registered handlers
        for handler in self.change_handlers[config_id]:
            try:
                handler(config_id, changes)
            except Exception as e:
                logger.error(f"Error in change handler for {config_id}: {e}")
        
        logger.info(f"Configuration {config_id} changed: {len(changes)} changes detected")

# ============================================================================
# Configuration Security and Encryption
# ============================================================================

class ConfigSecurity:
    """Advanced configuration security with encryption and access control."""
    
    def __init__(self, master_key: Optional[bytes] = None):
        """Initialize configuration security."""
        self.master_key = master_key or self._generate_master_key()
        self.fernet = Fernet(base64.urlsafe_b64encode(self.master_key[:32]))
        self.access_rules = {}
        self.audit_log = deque(maxlen=10000)
        self._lock = threading.Lock()
    
    @staticmethod
    def _generate_master_key() -> bytes:
        """Generate cryptographically secure master key."""
        return secrets.token_bytes(32)
    
    def derive_key_from_password(self, password: str, salt: Optional[bytes] = None) -> bytes:
        """Derive encryption key from password using PBKDF2."""
        if salt is None:
            salt = secrets.token_bytes(16)
        
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=DEFAULT_ENCRYPTION_ITERATIONS,
        )
        
        key = kdf.derive(password.encode('utf-8'))
        return key
    
    def encrypt_value(self, value: Any) -> str:
        """Encrypt a configuration value."""
        try:
            # Serialize value
            if isinstance(value, (dict, list)):
                serialized = json.dumps(value, default=str)
            else:
                serialized = str(value)
            
            # Encrypt
            encrypted_bytes = self.fernet.encrypt(serialized.encode('utf-8'))
            encrypted_str = base64.urlsafe_b64encode(encrypted_bytes).decode('utf-8')
            
            # Add encryption marker
            return f"ENCRYPTED:{encrypted_str}"
            
        except Exception as e:
            logger.error(f"Encryption error: {e}")
            raise
    
    def decrypt_value(self, encrypted_value: str) -> Any:
        """Decrypt a configuration value."""
        try:
            if not encrypted_value.startswith("ENCRYPTED:"):
                return encrypted_value  # Not encrypted
            
            # Remove encryption marker
            encrypted_str = encrypted_value[10:]  # Remove "ENCRYPTED:" prefix
            
            # Decode and decrypt
            encrypted_bytes = base64.urlsafe_b64decode(encrypted_str.encode('utf-8'))
            decrypted_bytes = self.fernet.decrypt(encrypted_bytes)
            serialized = decrypted_bytes.decode('utf-8')
            
            # Try to deserialize as JSON
            try:
                return json.loads(serialized)
            except json.JSONDecodeError:
                return serialized
                
        except Exception as e:
            logger.error(f"Decryption error: {e}")
            raise
    
    def encrypt_sensitive_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Encrypt sensitive values in configuration."""
        encrypted_config = copy.deepcopy(config)
        
        def encrypt_sensitive_paths(obj: Any, path: str = "") -> Any:
            if isinstance(obj, dict):
                for key, value in obj.items():
                    current_path = ConfigPath.join_path(path, key) if path else key
                    
                    # Check if this key contains sensitive information
                    if any(sensitive_key in key.lower() for sensitive_key in SENSITIVE_KEYS):
                        obj[key] = self.encrypt_value(value)
                    else:
                        obj[key] = encrypt_sensitive_paths(value, current_path)
            elif isinstance(obj, list):
                for i, item in enumerate(obj):
                    current_path = f"{path}[{i}]" if path else f"[{i}]"
                    obj[i] = encrypt_sensitive_paths(item, current_path)
            
            return obj
        
        return encrypt_sensitive_paths(encrypted_config)
    
    def decrypt_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Decrypt all encrypted values in configuration."""
        decrypted_config = copy.deepcopy(config)
        
        def decrypt_values(obj: Any) -> Any:
            if isinstance(obj, dict):
                for key, value in obj.items():
                    obj[key] = decrypt_values(value)
            elif isinstance(obj, list):
                for i, item in enumerate(obj):
                    obj[i] = decrypt_values(item)
            elif isinstance(obj, str) and obj.startswith("ENCRYPTED:"):
                return self.decrypt_value(obj)
            
            return obj
        
        return decrypt_values(decrypted_config)
    
    def add_access_rule(self, path_pattern: str, 
                       allowed_operations: Set[str],
                       user_roles: Set[str] = None) -> None:
        """Add access control rule."""
        self.access_rules[path_pattern] = {
            'operations': allowed_operations,
            'roles': user_roles or {'admin'},
            'created_at': datetime.now(timezone.utc)
        }
    
    def check_access(self, path: str, operation: str, user_role: str = 'user') -> bool:
        """Check if access is allowed for a configuration path."""
        for pattern, rule in self.access_rules.items():
            if self._path_matches_pattern(path, pattern):
                if operation in rule['operations'] and user_role in rule['roles']:
                    return True
                else:
                    self._log_access_attempt(path, operation, user_role, False)
                    return False
        
        # Default: allow all access if no rules match
        return True
    
    def _path_matches_pattern(self, path: str, pattern: str) -> bool:
        """Check if path matches access control pattern."""
        # Simple glob-style pattern matching
        import fnmatch
        return fnmatch.fnmatch(path, pattern)
    
    def _log_access_attempt(self, path: str, operation: str, 
                          user_role: str, allowed: bool) -> None:
        """Log access attempt for auditing."""
        with self._lock:
            self.audit_log.append({
                'timestamp': datetime.now(timezone.utc),
                'path': path,
                'operation': operation,
                'user_role': user_role,
                'allowed': allowed
            })
    
    def get_audit_log(self, since: Optional[datetime] = None) -> List[Dict[str, Any]]:
        """Get audit log entries."""
        with self._lock:
            if since is None:
                return list(self.audit_log)
            else:
                return [
                    entry for entry in self.audit_log
                    if entry['timestamp'] >= since
                ]
    
    def create_secure_config_file(self, config: Dict[str, Any], 
                                 file_path: Path,
                                 password: Optional[str] = None) -> None:
        """Create encrypted configuration file."""
        # Encrypt sensitive values
        encrypted_config = self.encrypt_sensitive_config(config)
        
        # Add metadata
        secure_config = {
            'version': '1.0',
            'encrypted_at': datetime.now(timezone.utc).isoformat(),
            'config': encrypted_config
        }
        
        # Serialize to JSON
        config_json = json.dumps(secure_config, indent=2, default=str)
        
        if password:
            # Encrypt entire file with password
            key = self.derive_key_from_password(password)
            file_cipher = Fernet(base64.urlsafe_b64encode(key))
            encrypted_content = file_cipher.encrypt(config_json.encode('utf-8'))
            
            with open(file_path, 'wb') as f:
                f.write(encrypted_content)
        else:
            # Save as plain JSON (with encrypted sensitive values)
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(config_json)
    
    def load_secure_config_file(self, file_path: Path, 
                               password: Optional[str] = None) -> Dict[str, Any]:
        """Load encrypted configuration file."""
        if password:
            # Decrypt entire file
            key = self.derive_key_from_password(password)
            file_cipher = Fernet(base64.urlsafe_b64encode(key))
            
            with open(file_path, 'rb') as f:
                encrypted_content = f.read()
            
            decrypted_content = file_cipher.decrypt(encrypted_content)
            config_json = decrypted_content.decode('utf-8')
        else:
            # Load as plain JSON
            with open(file_path, 'r', encoding='utf-8') as f:
                config_json = f.read()
        
        # Parse JSON
        secure_config = json.loads(config_json)
        encrypted_config = secure_config['config']
        
        # Decrypt sensitive values
        return self.decrypt_config(encrypted_config)

# ============================================================================
# Configuration Performance and Caching
# ============================================================================

class ConfigCache:
    """High-performance configuration caching with TTL and invalidation."""
    
    def __init__(self, max_size: int = DEFAULT_CACHE_SIZE, 
                 default_ttl: float = DEFAULT_CACHE_TTL):
        """Initialize configuration cache."""
        self.max_size = max_size
        self.default_ttl = default_ttl
        self.cache = OrderedDict()
        self.ttl_data = {}
        self.hit_count = 0
        self.miss_count = 0
        self._lock = threading.RLock()
        
        # Start cleanup thread
        self.cleanup_thread = threading.Thread(target=self._cleanup_loop, daemon=True)
        self.cleanup_active = True
        self.cleanup_thread.start()
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get value from cache."""
        with self._lock:
            current_time = time.time()
            
            if key in self.cache:
                # Check TTL
                if key in self.ttl_data:
                    if current_time > self.ttl_data[key]:
                        # Expired
                        del self.cache[key]
                        del self.ttl_data[key]
                        self.miss_count += 1
                        return default
                
                # Cache hit
                value = self.cache[key]
                # Move to end (LRU)
                self.cache.move_to_end(key)
                self.hit_count += 1
                return value
            else:
                # Cache miss
                self.miss_count += 1
                return default
    
    def set(self, key: str, value: Any, ttl: Optional[float] = None) -> None:
        """Set value in cache."""
        with self._lock:
            current_time = time.time()
            
            # Remove oldest items if cache is full
            while len(self.cache) >= self.max_size:
                oldest_key = next(iter(self.cache))
                del self.cache[oldest_key]
                self.ttl_data.pop(oldest_key, None)
            
            # Set value
            self.cache[key] = value
            
            # Set TTL
            if ttl is not None:
                self.ttl_data[key] = current_time + ttl
            else:
                self.ttl_data[key] = current_time + self.default_ttl
    
    def delete(self, key: str) -> bool:
        """Delete value from cache."""
        with self._lock:
            if key in self.cache:
                del self.cache[key]
                self.ttl_data.pop(key, None)
                return True
            return False
    
    def clear(self) -> None:
        """Clear all cache entries."""
        with self._lock:
            self.cache.clear()
            self.ttl_data.clear()
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        with self._lock:
            total_requests = self.hit_count + self.miss_count
            hit_rate = self.hit_count / total_requests if total_requests > 0 else 0.0
            
            return {
                'size': len(self.cache),
                'max_size': self.max_size,
                'hit_count': self.hit_count,
                'miss_count': self.miss_count,
                'hit_rate': hit_rate,
                'total_requests': total_requests
            }
    
    def _cleanup_loop(self) -> None:
        """Background cleanup of expired entries."""
        while self.cleanup_active:
            try:
                current_time = time.time()
                expired_keys = []
                
                with self._lock:
                    for key, expiry_time in self.ttl_data.items():
                        if current_time > expiry_time:
                            expired_keys.append(key)
                    
                    for key in expired_keys:
                        self.cache.pop(key, None)
                        self.ttl_data.pop(key, None)
                
                time.sleep(60)  # Cleanup every minute
                
            except Exception as e:
                logger.error(f"Cache cleanup error: {e}")
                time.sleep(60)

class PerformantConfigManager:
    """High-performance configuration manager with advanced caching."""
    
    def __init__(self, cache_policy: CachePolicy = CachePolicy.MEMORY_CACHE):
        """Initialize performance-optimized config manager."""
        self.cache_policy = cache_policy
        self.cache = ConfigCache() if cache_policy != CachePolicy.NO_CACHE else None
        self.access_stats = defaultdict(int)
        self.performance_metrics = {}
        self._lock = threading.RLock()
    
    def get_config_value(self, path: str, 
                        config_source: Optional[Callable[[], Dict[str, Any]]] = None,
                        use_cache: bool = True) -> Any:
        """Get configuration value with caching."""
        cache_key = f"config:{path}"
        
        # Try cache first
        if use_cache and self.cache:
            cached_value = self.cache.get(cache_key)
            if cached_value is not None:
                self.access_stats[path] += 1
                return cached_value
        
        # Get from source
        start_time = time.time()
        
        try:
            if config_source:
                config = config_source()
            else:
                config = get_config()
                if hasattr(config, '__dict__'):
                    config = asdict(config) if is_dataclass(config) else config.__dict__
            
            value = ConfigPath.get_value(config, path)
            
            # Cache the value
            if use_cache and self.cache:
                self.cache.set(cache_key, value)
            
            # Record performance metrics
            access_time = time.time() - start_time
            self.performance_metrics[path] = {
                'last_access_time': access_time,
                'access_count': self.access_stats[path] + 1
            }
            self.access_stats[path] += 1
            
            return value
            
        except Exception as e:
            logger.error(f"Error getting config value {path}: {e}")
            raise
    
    def batch_get_config_values(self, paths: List[str],
                               config_source: Optional[Callable[[], Dict[str, Any]]] = None) -> Dict[str, Any]:
        """Get multiple configuration values efficiently."""
        results = {}
        uncached_paths = []
        
        # Check cache for all paths
        if self.cache:
            for path in paths:
                cache_key = f"config:{path}"
                cached_value = self.cache.get(cache_key)
                if cached_value is not None:
                    results[path] = cached_value
                    self.access_stats[path] += 1
                else:
                    uncached_paths.append(path)
        else:
            uncached_paths = paths
        
        # Get uncached values in batch
        if uncached_paths:
            start_time = time.time()
            
            try:
                if config_source:
                    config = config_source()
                else:
                    config = get_config()
                    if hasattr(config, '__dict__'):
                        config = asdict(config) if is_dataclass(config) else config.__dict__
                
                for path in uncached_paths:
                    try:
                        value = ConfigPath.get_value(config, path)
                        results[path] = value
                        
                        # Cache the value
                        if self.cache:
                            cache_key = f"config:{path}"
                            self.cache.set(cache_key, value)
                        
                        self.access_stats[path] += 1
                        
                    except Exception as e:
                        logger.warning(f"Error getting config value {path}: {e}")
                        results[path] = None
                
                # Record batch performance
                batch_time = time.time() - start_time
                logger.debug(f"Batch config access: {len(uncached_paths)} paths in {batch_time:.4f}s")
                
            except Exception as e:
                logger.error(f"Error in batch config access: {e}")
                raise
        
        return results
    
    def invalidate_cache(self, path_pattern: Optional[str] = None) -> None:
        """Invalidate cache entries matching pattern."""
        if not self.cache:
            return
        
        if path_pattern is None:
            # Clear all cache
            self.cache.clear()
        else:
            # Clear matching entries
            import fnmatch
            keys_to_delete = []
            
            with self.cache._lock:
                for key in list(self.cache.cache.keys()):
                    if key.startswith("config:"):
                        config_path = key[7:]  # Remove "config:" prefix
                        if fnmatch.fnmatch(config_path, path_pattern):
                            keys_to_delete.append(key)
            
            for key in keys_to_delete:
                self.cache.delete(key)
    
    def get_performance_report(self) -> Dict[str, Any]:
        """Get configuration access performance report."""
        with self._lock:
            # Most accessed paths
            most_accessed = sorted(
                self.access_stats.items(),
                key=lambda x: x[1],
                reverse=True
            )[:10]
            
            # Slowest paths
            slowest_paths = sorted(
                [(path, metrics['last_access_time']) 
                 for path, metrics in self.performance_metrics.items()],
                key=lambda x: x[1],
                reverse=True
            )[:10]
            
            # Cache statistics
            cache_stats = self.cache.get_stats() if self.cache else {}
            
            return {
                'total_unique_paths': len(self.access_stats),
                'total_accesses': sum(self.access_stats.values()),
                'most_accessed_paths': most_accessed,
                'slowest_paths': slowest_paths,
                'cache_stats': cache_stats,
                'cache_policy': self.cache_policy.value
            }

# ============================================================================
# Integration Utilities
# ============================================================================

class ConfigIntegrator:
    """Integration utilities for ULTRA subsystems."""
    
    def __init__(self):
        """Initialize config integrator."""
        self.subsystem_configs = {}
        self.validation_schemas = {}
        self.transformation_rules = {}
    
    def register_subsystem_config(self, subsystem_name: str,
                                 config_schema: Dict[str, Any],
                                 validator: Optional[ConfigValidator] = None) -> None:
        """Register configuration schema for a subsystem."""
        self.subsystem_configs[subsystem_name] = {
            'schema': config_schema,
            'validator': validator,
            'registered_at': datetime.now(timezone.utc)
        }
    
    def generate_subsystem_config(self, subsystem_name: str,
                                 base_config: Dict[str, Any],
                                 overrides: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Generate configuration for a specific subsystem."""
        if subsystem_name not in self.subsystem_configs:
            raise ValueError(f"Subsystem '{subsystem_name}' not registered")
        
        schema = self.subsystem_configs[subsystem_name]['schema']
        
        # Extract relevant configuration
        subsystem_config = {}
        
        def extract_config(obj: Any, schema_obj: Any, path: str = "") -> Any:
            if isinstance(schema_obj, dict):
                result = {}
                for key, value in schema_obj.items():
                    current_path = ConfigPath.join_path(path, key) if path else key
                    
                    if isinstance(obj, dict) and key in obj:
                        result[key] = extract_config(obj[key], value, current_path)
                    elif 'default' in value if isinstance(value, dict) else False:
                        result[key] = value['default']
                    # Skip missing non-default values
                
                return result
            else:
                return obj
        
        subsystem_config = extract_config(base_config, schema)
        
        # Apply overrides
        if overrides:
            subsystem_config = self._deep_merge(subsystem_config, overrides)
        
        # Validate if validator is available
        validator = self.subsystem_configs[subsystem_name]['validator']
        if validator:
            validation_result = validator.validate(subsystem_config)
            if not validation_result.is_valid:
                raise ConfigValidationError(
                    f"Subsystem config validation failed: {validation_result.errors}"
                )
        
        return subsystem_config
    
    def create_neural_subsystem_config(self, base_config: Dict[str, Any]) -> Dict[str, Any]:
        """Create neural subsystem specific configuration."""
        neural_schema = {
            'enabled': {'type': bool, 'default': True},
            'neuron_model': {'type': str, 'default': 'LIF'},
            'neuron_params': {
                'tau_m': {'type': float, 'default': 20.0},
                'v_rest': {'type': float, 'default': -70.0},
                'v_threshold': {'type': float, 'default': -50.0},
                'v_reset': {'type': float, 'default': -65.0},
                'refractory_period': {'type': float, 'default': 2.0}
            },
            'network_size': {'type': tuple, 'default': (100, 100, 100)},
            'excitatory_ratio': {'type': float, 'default': 0.8},
            'stdp_params': {
                'a_plus': {'type': float, 'default': 0.005},
                'a_minus': {'type': float, 'default': 0.0025},
                'tau_plus': {'type': float, 'default': 20.0},
                'tau_minus': {'type': float, 'default': 20.0}
            }
        }
        
        # Register if not already registered
        if 'neural' not in self.subsystem_configs:
            validator = ConfigValidator()
            validator.add_neural_constraints()
            self.register_subsystem_config('neural', neural_schema, validator)
        
        return self.generate_subsystem_config('neural', base_config.get('core_neural', {}))
    
    def create_transformer_subsystem_config(self, base_config: Dict[str, Any]) -> Dict[str, Any]:
        """Create transformer subsystem specific configuration."""
        transformer_schema = {
            'enabled': {'type': bool, 'default': True},
            'transformer_params': {
                'd_model': {'type': int, 'default': 512},
                'n_heads': {'type': int, 'default': 8},
                'd_ff': {'type': int, 'default': 2048},
                'max_seq_length': {'type': int, 'default': 1024},
                'dropout': {'type': float, 'default': 0.1}
            },
            'recursive_transformer_params': {
                'max_recursion_depth': {'type': int, 'default': 5},
                'confidence_threshold': {'type': float, 'default': 0.8}
            }
        }
        
        if 'transformer' not in self.subsystem_configs:
            validator = ConfigValidator()
            validator.add_transformer_constraints()
            self.register_subsystem_config('transformer', transformer_schema, validator)
        
        return self.generate_subsystem_config('transformer', base_config.get('hyper_transformer', {}))
    
    def create_diffusion_subsystem_config(self, base_config: Dict[str, Any]) -> Dict[str, Any]:
        """Create diffusion subsystem specific configuration."""
        diffusion_schema = {
            'enabled': {'type': bool, 'default': True},
            'diffusion_params': {
                'num_timesteps': {'type': int, 'default': 1000},
                'beta_schedule': {'type': str, 'default': 'linear'},
                'beta_start': {'type': float, 'default': 1e-4},
                'beta_end': {'type': float, 'default': 0.02}
            },
            'latent_space_params': {
                'dimension': {'type': int, 'default': 1024},
                'hierarchical_levels': {'type': int, 'default': 3}
            }
        }
        
        if 'diffusion' not in self.subsystem_configs:
            validator = ConfigValidator()
            validator.add_diffusion_constraints()
            self.register_subsystem_config('diffusion', diffusion_schema, validator)
        
        return self.generate_subsystem_config('diffusion', base_config.get('diffusion_reasoning', {}))
    
    def validate_cross_subsystem_compatibility(self, config: Dict[str, Any]) -> ValidationResult:
        """Validate compatibility across subsystems."""
        errors = []
        warnings = []
        
        try:
            # Check neural-transformer compatibility
            if ('core_neural' in config and 'hyper_transformer' in config and
                config['core_neural'].get('enabled', True) and 
                config['hyper_transformer'].get('enabled', True)):
                
                neural_size = np.prod(config['core_neural'].get('network_size', (100, 100, 100)))
                transformer_dim = config['hyper_transformer'].get('transformer_params', {}).get('d_model', 512)
                
                # Check if neural output can interface with transformer
                if neural_size > transformer_dim * 10:
                    warnings.append("Large neural network may bottleneck at transformer interface")
            
            # Check memory compatibility
            total_estimated_memory = 0.0
            for subsystem_name in ['core_neural', 'hyper_transformer', 'diffusion_reasoning']:
                if subsystem_name in config and config[subsystem_name].get('enabled', True):
                    subsystem_memory = self._estimate_subsystem_memory(subsystem_name, config[subsystem_name])
                    total_estimated_memory += subsystem_memory
            
            max_memory_str = config.get('max_memory_usage', '16GB')
            max_memory_gb = float(max_memory_str.replace('GB', '').replace('gb', ''))
            
            if total_estimated_memory > max_memory_gb:
                errors.append(f"Total estimated memory ({total_estimated_memory:.1f}GB) "
                             f"exceeds limit ({max_memory_gb}GB)")
            
            # Check device compatibility
            device = config.get('device', 'cpu')
            if device.startswith('cuda'):
                # Check CUDA-specific configurations
                for subsystem_name in config:
                    if isinstance(config[subsystem_name], dict):
                        batch_sizes = self._find_batch_sizes(config[subsystem_name])
                        if any(batch_size > 128 for batch_size in batch_sizes):
                            warnings.append(f"Large batch sizes in {subsystem_name} may exceed GPU memory")
        
        except Exception as e:
            errors.append(f"Cross-subsystem validation error: {str(e)}")
        
        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings
        )
    
    def _deep_merge(self, base: Dict[str, Any], override: Dict[str, Any]) -> Dict[str, Any]:
        """Deep merge two dictionaries."""
        result = copy.deepcopy(base)
        
        for key, value in override.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._deep_merge(result[key], value)
            else:
                result[key] = copy.deepcopy(value)
        
        return result
    
    def _estimate_subsystem_memory(self, subsystem_name: str, config: Dict[str, Any]) -> float:
        """Estimate memory usage for a subsystem."""
        memory_gb = 0.0
        
        try:
            if subsystem_name == 'core_neural':
                network_size = config.get('network_size', (100, 100, 100))
                total_neurons = np.prod(network_size)
                memory_gb = total_neurons * 1e-6  # 1KB per neuron estimate
                
            elif subsystem_name == 'hyper_transformer':
                params = config.get('transformer_params', {})
                d_model = params.get('d_model', 512)
                max_seq_len = params.get('max_seq_length', 1024)
                n_heads = params.get('n_heads', 8)
                
                # Attention memory
                attention_memory = (max_seq_len ** 2 * n_heads * 4) * 1e-9
                # Parameter memory
                param_memory = (d_model ** 2 * 4) * 1e-9
                memory_gb = attention_memory + param_memory
                
            elif subsystem_name == 'diffusion_reasoning':
                params = config.get('latent_space_params', {})
                dimension = params.get('dimension', 1024)
                memory_gb = (dimension ** 2 * 4) * 1e-9  # Rough estimate
        
        except Exception:
            memory_gb = 1.0  # Conservative default
        
        return memory_gb
    
    def _find_batch_sizes(self, config: Dict[str, Any]) -> List[int]:
        """Find all batch size configurations."""
        batch_sizes = []
        
        def search_batch_sizes(obj: Any) -> None:
            if isinstance(obj, dict):
                for key, value in obj.items():
                    if 'batch' in key.lower() and isinstance(value, int):
                        batch_sizes.append(value)
                    else:
                        search_batch_sizes(value)
            elif isinstance(obj, list):
                for item in obj:
                    search_batch_sizes(item)
        
        search_batch_sizes(config)
        return batch_sizes

# ============================================================================
# Main Configuration Utilities Class
# ============================================================================

class ConfigUtils:
    """Main configuration utilities class providing all advanced functionality."""
    
    def __init__(self):
        """Initialize configuration utilities."""
        self.validator = ConfigValidator()
        self.template_engine = ConfigTemplateEngine()
        self.monitor = ConfigMonitor()
        self.security = ConfigSecurity()
        self.cache_manager = PerformantConfigManager()
        self.integrator = ConfigIntegrator()
        
        # Setup default validators
        self._setup_default_validators()
        
        # Setup default templates
        self._setup_default_templates()
    
    def _setup_default_validators(self) -> None:
        """Setup default validation rules."""
        self.validator.add_neural_constraints()
        self.validator.add_transformer_constraints()
        self.validator.add_diffusion_constraints()
    
    def _setup_default_templates(self) -> None:
        """Setup default configuration templates."""
        self.template_engine.generate_neural_config_template()
        self.template_engine.generate_transformer_config_template()
    
    def validate_config(self, config: Dict[str, Any]) -> ValidationResult:
        """Validate configuration with all available rules."""
        return self.validator.validate(config)
    
    def generate_config_from_template(self, template_name: str, 
                                    variables: Dict[str, Any]) -> Dict[str, Any]:
        """Generate configuration from template."""
        return self.template_engine.instantiate_template(template_name, variables)
    
    def encrypt_sensitive_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Encrypt sensitive configuration values."""
        return self.security.encrypt_sensitive_config(config)
    
    def start_config_monitoring(self, config_sources: Dict[str, Union[Path, Callable]]) -> None:
        """Start monitoring configuration sources."""
        for config_id, source in config_sources.items():
            self.monitor.watch_config(config_id, source)
        self.monitor.start_monitoring()
    
    def get_performance_config_value(self, path: str) -> Any:
        """Get configuration value with performance optimization."""
        return self.cache_manager.get_config_value(path)
    
    def create_subsystem_config(self, subsystem_name: str, 
                               base_config: Dict[str, Any]) -> Dict[str, Any]:
        """Create subsystem-specific configuration."""
        if subsystem_name == 'neural':
            return self.integrator.create_neural_subsystem_config(base_config)
        elif subsystem_name == 'transformer':
            return self.integrator.create_transformer_subsystem_config(base_config)
        elif subsystem_name == 'diffusion':
            return self.integrator.create_diffusion_subsystem_config(base_config)
        else:
            raise ValueError(f"Unknown subsystem: {subsystem_name}")
    
    def get_system_report(self) -> Dict[str, Any]:
        """Get comprehensive system configuration report."""
        return {
            'validation_summary': {
                'total_rules': len(self.validator.rules),
                'custom_validators': len(self.validator.custom_validators)
            },
            'template_summary': {
                'total_templates': len(self.template_engine.templates),
                'available_functions': len(self.template_engine.functions)
            },
            'monitoring_summary': {
                'watched_configs': len(self.monitor.watched_configs),
                'change_history_size': len(self.monitor.change_history),
                'monitoring_active': self.monitor.monitoring_active
            },
            'security_summary': {
                'access_rules': len(self.security.access_rules),
                'audit_log_size': len(self.security.audit_log)
            },
            'performance_summary': self.cache_manager.get_performance_report(),
            'integration_summary': {
                'registered_subsystems': len(self.integrator.subsystem_configs)
            }
        }

# ============================================================================
# Global Instance and Convenience Functions
# ============================================================================

# Global configuration utilities instance
_config_utils = None

def get_config_utils() -> ConfigUtils:
    """Get global configuration utilities instance."""
    global _config_utils
    if _config_utils is None:
        _config_utils = ConfigUtils()
    return _config_utils

def validate_ultra_config(config: Dict[str, Any]) -> ValidationResult:
    """Validate ULTRA configuration."""
    utils = get_config_utils()
    return utils.validate_config(config)

def create_neural_config_template(variables: Dict[str, Any]) -> Dict[str, Any]:
    """Create neural configuration from template."""
    utils = get_config_utils()
    return utils.generate_config_from_template('neural_network_basic', variables)

def create_transformer_config_template(variables: Dict[str, Any]) -> Dict[str, Any]:
    """Create transformer configuration from template."""
    utils = get_config_utils()
    return utils.generate_config_from_template('transformer_advanced', variables)

def secure_config_file(config: Dict[str, Any], file_path: Path, 
                      password: Optional[str] = None) -> None:
    """Create secure configuration file."""
    utils = get_config_utils()
    utils.security.create_secure_config_file(config, file_path, password)

def load_secure_config_file(file_path: Path, 
                           password: Optional[str] = None) -> Dict[str, Any]:
    """Load secure configuration file."""
    utils = get_config_utils()
    return utils.security.load_secure_config_file(file_path, password)

# ============================================================================
# Module Exports
# ============================================================================

__all__ = [
    # Core classes
    'ConfigPath',
    'ConfigValidator', 
    'ConfigTemplateEngine',
    'ConfigMonitor',
    'ConfigSecurity',
    'ConfigCache',
    'PerformantConfigManager',
    'ConfigIntegrator',
    'ConfigUtils',
    
    # Data classes
    'ConfigChange',
    'ValidationResult',
    'ConfigTemplate',
    'ConfigProfile',
    
    # Enums
    'ConfigChangeType',
    'ConfigFormat',
    'ValidationSeverity',
    'CachePolicy',
    
    # Protocols
    'ConfigSerializable',
    'ConfigValidatable',
    
    # Global functions
    'get_config_utils',
    'validate_ultra_config',
    'create_neural_config_template',
    'create_transformer_config_template',
    'secure_config_file',
    'load_secure_config_file',
    
    # Utility functions
    'deep_merge_configs',
    'flatten_config',
    'unflatten_config',
    'config_diff',
    'config_patch',
    'normalize_config_types',
    'extract_config_metadata',
    'generate_config_schema',
    'validate_config_schema',
    'convert_config_format',
    'optimize_config_for_environment',
    'create_config_backup',
    'restore_config_backup',
    'migrate_config_version',
    'analyze_config_usage',
    'benchmark_config_performance',
    'generate_config_documentation',
    
    # Constants
    'DEFAULT_CACHE_SIZE',
    'DEFAULT_CACHE_TTL',
    'DEFAULT_ENCRYPTION_ITERATIONS',
    'CONFIG_VERSION_PATTERN',
    'MAX_CONFIG_DEPTH',
    'SENSITIVE_KEYS'
]

# ============================================================================
# Additional Utility Functions
# ============================================================================

def deep_merge_configs(*configs: Dict[str, Any]) -> Dict[str, Any]:
    """Deep merge multiple configuration dictionaries."""
    if not configs:
        return {}
    
    result = copy.deepcopy(configs[0])
    
    for config in configs[1:]:
        result = _deep_merge_dict(result, config)
    
    return result

def _deep_merge_dict(base: Dict[str, Any], override: Dict[str, Any]) -> Dict[str, Any]:
    """Helper function for deep merging dictionaries."""
    result = copy.deepcopy(base)
    
    for key, value in override.items():
        if key in result:
            if isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = _deep_merge_dict(result[key], value)
            elif isinstance(result[key], list) and isinstance(value, list):
                # Extend lists
                result[key] = result[key] + value
            else:
                # Override value
                result[key] = copy.deepcopy(value)
        else:
            result[key] = copy.deepcopy(value)
    
    return result

def flatten_config(config: Dict[str, Any], separator: str = ".") -> Dict[str, Any]:
    """Flatten nested configuration dictionary."""
    result = {}
    
    def _flatten(obj: Any, prefix: str = "") -> None:
        if isinstance(obj, dict):
            for key, value in obj.items():
                new_key = f"{prefix}{separator}{key}" if prefix else key
                _flatten(value, new_key)
        elif isinstance(obj, list):
            for i, item in enumerate(obj):
                new_key = f"{prefix}[{i}]"
                _flatten(item, new_key)
        else:
            result[prefix] = obj
    
    _flatten(config)
    return result

def unflatten_config(flat_config: Dict[str, Any], separator: str = ".") -> Dict[str, Any]:
    """Unflatten configuration dictionary back to nested structure."""
    result = {}
    
    for key, value in flat_config.items():
        ConfigPath.set_value(result, key.replace(separator, ConfigPath.SEPARATOR), value)
    
    return result

def config_diff(config1: Dict[str, Any], config2: Dict[str, Any]) -> List[ConfigChange]:
    """Compute differences between two configurations."""
    monitor = ConfigMonitor()
    return monitor._compute_config_diff(config1, config2)

def config_patch(base_config: Dict[str, Any], changes: List[ConfigChange]) -> Dict[str, Any]:
    """Apply configuration changes to base configuration."""
    result = copy.deepcopy(base_config)
    
    for change in changes:
        if change.change_type == ConfigChangeType.ADDED:
            ConfigPath.set_value(result, change.path, change.new_value)
        elif change.change_type == ConfigChangeType.MODIFIED:
            ConfigPath.set_value(result, change.path, change.new_value)
        elif change.change_type == ConfigChangeType.REMOVED:
            ConfigPath.delete_value(result, change.path)
    
    return result

def normalize_config_types(config: Dict[str, Any]) -> Dict[str, Any]:
    """Normalize configuration value types for consistency."""
    result = copy.deepcopy(config)
    
    def _normalize(obj: Any) -> Any:
        if isinstance(obj, dict):
            return {key: _normalize(value) for key, value in obj.items()}
        elif isinstance(obj, list):
            return [_normalize(item) for item in obj]
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, (np.int64, np.int32)):
            return int(obj)
        elif isinstance(obj, (np.float64, np.float32)):
            return float(obj)
        elif isinstance(obj, np.bool_):
            return bool(obj)
        elif isinstance(obj, (datetime, timedelta)):
            return obj.isoformat()
        elif isinstance(obj, Path):
            return str(obj)
        else:
            return obj
    
    return _normalize(result)

def extract_config_metadata(config: Dict[str, Any]) -> Dict[str, Any]:
    """Extract metadata about configuration structure."""
    metadata = {
        'total_keys': 0,
        'max_depth': 0,
        'data_types': defaultdict(int),
        'array_sizes': [],
        'string_lengths': [],
        'numeric_ranges': {'min': float('inf'), 'max': float('-inf')},
        'boolean_distribution': {'true': 0, 'false': 0},
        'null_count': 0
    }
    
    def _analyze(obj: Any, depth: int = 0) -> None:
        metadata['max_depth'] = max(metadata['max_depth'], depth)
        
        if isinstance(obj, dict):
            metadata['total_keys'] += len(obj)
            metadata['data_types']['dict'] += 1
            for value in obj.values():
                _analyze(value, depth + 1)
        elif isinstance(obj, list):
            metadata['data_types']['list'] += 1
            metadata['array_sizes'].append(len(obj))
            for item in obj:
                _analyze(item, depth + 1)
        elif isinstance(obj, str):
            metadata['data_types']['string'] += 1
            metadata['string_lengths'].append(len(obj))
        elif isinstance(obj, (int, float)):
            metadata['data_types']['numeric'] += 1
            metadata['numeric_ranges']['min'] = min(metadata['numeric_ranges']['min'], obj)
            metadata['numeric_ranges']['max'] = max(metadata['numeric_ranges']['max'], obj)
        elif isinstance(obj, bool):
            metadata['data_types']['boolean'] += 1
            if obj:
                metadata['boolean_distribution']['true'] += 1
            else:
                metadata['boolean_distribution']['false'] += 1
        elif obj is None:
            metadata['null_count'] += 1
        else:
            metadata['data_types']['other'] += 1
    
    _analyze(config)
    
    # Calculate summary statistics
    if metadata['array_sizes']:
        metadata['array_stats'] = {
            'mean_size': np.mean(metadata['array_sizes']),
            'max_size': np.max(metadata['array_sizes']),
            'min_size': np.min(metadata['array_sizes'])
        }
    
    if metadata['string_lengths']:
        metadata['string_stats'] = {
            'mean_length': np.mean(metadata['string_lengths']),
            'max_length': np.max(metadata['string_lengths']),
            'min_length': np.min(metadata['string_lengths'])
        }
    
    # Fix numeric ranges if no numbers found
    if metadata['numeric_ranges']['min'] == float('inf'):
        metadata['numeric_ranges'] = {'min': 0, 'max': 0}
    
    return dict(metadata)

def generate_config_schema(config: Dict[str, Any]) -> Dict[str, Any]:
    """Generate JSON schema from configuration example."""
    def _infer_schema(obj: Any) -> Dict[str, Any]:
        if isinstance(obj, dict):
            properties = {}
            required = []
            
            for key, value in obj.items():
                properties[key] = _infer_schema(value)
                required.append(key)
            
            return {
                "type": "object",
                "properties": properties,
                "required": required,
                "additionalProperties": False
            }
        elif isinstance(obj, list):
            if obj:
                # Infer schema from first element
                item_schema = _infer_schema(obj[0])
                return {
                    "type": "array",
                    "items": item_schema,
                    "minItems": 0
                }
            else:
                return {
                    "type": "array",
                    "items": {}
                }
        elif isinstance(obj, str):
            return {
                "type": "string",
                "minLength": 0
            }
        elif isinstance(obj, int):
            return {
                "type": "integer"
            }
        elif isinstance(obj, float):
            return {
                "type": "number"
            }
        elif isinstance(obj, bool):
            return {
                "type": "boolean"
            }
        elif obj is None:
            return {
                "type": "null"
            }
        else:
            return {
                "type": "string",
                "description": f"Serialized {type(obj).__name__}"
            }
    
    schema = _infer_schema(config)
    schema["$schema"] = "http://json-schema.org/draft-07/schema#"
    schema["title"] = "ULTRA Configuration Schema"
    schema["description"] = "Auto-generated schema for ULTRA system configuration"
    
    return schema

def validate_config_schema(config: Dict[str, Any], schema: Dict[str, Any]) -> ValidationResult:
    """Validate configuration against JSON schema."""
    try:
        jsonschema.validate(config, schema)
        return ValidationResult(is_valid=True)
    except ValidationError as e:
        return ValidationResult(
            is_valid=False,
            errors=[f"Schema validation error: {e.message}"]
        )
    except Exception as e:
        return ValidationResult(
            is_valid=False,
            errors=[f"Schema validation internal error: {str(e)}"]
        )

def convert_config_format(config: Dict[str, Any], 
                         source_format: ConfigFormat,
                         target_format: ConfigFormat) -> str:
    """Convert configuration between different formats."""
    # Normalize config first
    normalized_config = normalize_config_types(config)
    
    if target_format == ConfigFormat.JSON:
        return json.dumps(normalized_config, indent=2, default=str)
    elif target_format == ConfigFormat.YAML:
        return yaml.dump(normalized_config, default_flow_style=False, sort_keys=False)
    elif target_format == ConfigFormat.TOML:
        return toml.dumps(normalized_config)
    elif target_format == ConfigFormat.PYTHON:
        return f"config = {repr(normalized_config)}"
    else:
        raise ValueError(f"Unsupported target format: {target_format}")

def optimize_config_for_environment(config: Dict[str, Any], 
                                  environment: ConfigEnvironment) -> Dict[str, Any]:
    """Optimize configuration for specific environment."""
    optimized = copy.deepcopy(config)
    
    if environment == ConfigEnvironment.PRODUCTION:
        # Production optimizations
        optimizations = {
            'log_level': 'WARNING',
            'debug': False,
            'profiling_enabled': False,
            'monitoring_interval': 300,  # 5 minutes
            'checkpoint_interval': 5000,
            'validation_frequency': 'startup_only'
        }
        
        # Apply memory optimizations
        if 'max_memory_usage' in optimized:
            # Reduce memory usage by 10% for safety
            current_memory = optimized['max_memory_usage']
            if isinstance(current_memory, str) and 'GB' in current_memory:
                memory_gb = float(current_memory.replace('GB', ''))
                optimized['max_memory_usage'] = f"{memory_gb * 0.9:.1f}GB"
        
        # Reduce batch sizes for stability
        _reduce_batch_sizes(optimized, factor=0.8)
        
    elif environment == ConfigEnvironment.DEVELOPMENT:
        # Development optimizations
        optimizations = {
            'log_level': 'DEBUG',
            'debug': True,
            'profiling_enabled': True,
            'monitoring_interval': 30,  # 30 seconds
            'checkpoint_interval': 1000,
            'validation_frequency': 'always'
        }
        
        # Use smaller models for faster iteration
        _scale_model_sizes(optimized, factor=0.5)
        
    elif environment == ConfigEnvironment.TESTING:
        # Testing optimizations
        optimizations = {
            'log_level': 'INFO',
            'debug': False,
            'profiling_enabled': False,
            'monitoring_interval': 60,
            'checkpoint_interval': 100,
            'validation_frequency': 'always',
            'random_seed': 42  # Ensure reproducibility
        }
        
        # Use minimal configurations for fast tests
        _scale_model_sizes(optimized, factor=0.1)
    
    # Apply optimizations
    for key, value in optimizations.items():
        if ConfigPath.path_exists(optimized, key):
            ConfigPath.set_value(optimized, key, value)
    
    return optimized

def _reduce_batch_sizes(config: Dict[str, Any], factor: float) -> None:
    """Reduce batch sizes in configuration."""
    def _process_obj(obj: Any) -> Any:
        if isinstance(obj, dict):
            for key, value in obj.items():
                if 'batch' in key.lower() and isinstance(value, int):
                    obj[key] = max(1, int(value * factor))
                else:
                    _process_obj(value)
        elif isinstance(obj, list):
            for item in obj:
                _process_obj(item)
    
    _process_obj(config)

def _scale_model_sizes(config: Dict[str, Any], factor: float) -> None:
    """Scale model sizes in configuration."""
    size_keys = ['d_model', 'd_ff', 'hidden_size', 'embedding_dim', 'dimension']
    
    def _process_obj(obj: Any) -> Any:
        if isinstance(obj, dict):
            for key, value in obj.items():
                if key in size_keys and isinstance(value, int):
                    obj[key] = max(8, int(value * factor))  # Minimum size of 8
                else:
                    _process_obj(value)
        elif isinstance(obj, list):
            for item in obj:
                _process_obj(item)
    
    _process_obj(config)

def create_config_backup(config: Dict[str, Any], 
                        backup_dir: Path = Path("config_backups")) -> Path:
    """Create backup of configuration."""
    backup_dir.mkdir(exist_ok=True, parents=True)
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_filename = f"config_backup_{timestamp}.json"
    backup_path = backup_dir / backup_filename
    
    # Add metadata
    backup_data = {
        'metadata': {
            'created_at': datetime.now(timezone.utc).isoformat(),
            'version': '1.0',
            'backup_type': 'full'
        },
        'config': normalize_config_types(config)
    }
    
    with open(backup_path, 'w', encoding='utf-8') as f:
        json.dump(backup_data, f, indent=2, default=str)
    
    logger.info(f"Configuration backup created: {backup_path}")
    return backup_path

def restore_config_backup(backup_path: Path) -> Dict[str, Any]:
    """Restore configuration from backup."""
    if not backup_path.exists():
        raise FileNotFoundError(f"Backup file not found: {backup_path}")
    
    with open(backup_path, 'r', encoding='utf-8') as f:
        backup_data = json.load(f)
    
    if 'config' not in backup_data:
        raise ValueError(f"Invalid backup format: {backup_path}")
    
    logger.info(f"Configuration restored from backup: {backup_path}")
    return backup_data['config']

def migrate_config_version(config: Dict[str, Any], 
                          from_version: str,
                          to_version: str) -> Dict[str, Any]:
    """Migrate configuration between versions."""
    migration_rules = {
        ('1.0.0', '1.1.0'): _migrate_1_0_to_1_1,
        ('1.1.0', '1.2.0'): _migrate_1_1_to_1_2,
        ('1.2.0', '2.0.0'): _migrate_1_2_to_2_0
    }
    
    migration_key = (from_version, to_version)
    if migration_key not in migration_rules:
        raise ValueError(f"No migration path from {from_version} to {to_version}")
    
    migrated_config = migration_rules[migration_key](config)
    
    # Update version metadata
    if 'version' in migrated_config:
        migrated_config['version'] = to_version
    
    logger.info(f"Configuration migrated from {from_version} to {to_version}")
    return migrated_config

def _migrate_1_0_to_1_1(config: Dict[str, Any]) -> Dict[str, Any]:
    """Migrate configuration from v1.0.0 to v1.1.0."""
    migrated = copy.deepcopy(config)
    
    # Example migration: rename old parameter names
    if 'old_parameter_name' in migrated:
        migrated['new_parameter_name'] = migrated.pop('old_parameter_name')
    
    # Add new default parameters
    if 'new_feature' not in migrated:
        migrated['new_feature'] = {
            'enabled': False,
            'setting': 'default_value'
        }
    
    return migrated

def _migrate_1_1_to_1_2(config: Dict[str, Any]) -> Dict[str, Any]:
    """Migrate configuration from v1.1.0 to v1.2.0."""
    migrated = copy.deepcopy(config)
    
    # Example: restructure nested configuration
    if 'old_section' in migrated:
        old_section = migrated.pop('old_section')
        migrated['new_section'] = {
            'subsection_a': old_section.get('param_a', {}),
            'subsection_b': old_section.get('param_b', {})
        }
    
    return migrated

def _migrate_1_2_to_2_0(config: Dict[str, Any]) -> Dict[str, Any]:
    """Migrate configuration from v1.2.0 to v2.0.0."""
    migrated = copy.deepcopy(config)
    
    # Major version change: significant restructuring
    if 'legacy_format' in migrated:
        legacy_data = migrated.pop('legacy_format')
        migrated['modern_format'] = {
            'version': '2.0',
            'data': legacy_data,
            'compatibility_mode': True
        }
    
    return migrated

def analyze_config_usage(config: Dict[str, Any], 
                        access_patterns: Optional[Dict[str, int]] = None) -> Dict[str, Any]:
    """Analyze configuration usage patterns."""
    analysis = {
        'total_parameters': 0,
        'unused_parameters': [],
        'frequently_accessed': [],
        'parameter_categories': defaultdict(int),
        'complexity_score': 0.0,
        'optimization_suggestions': []
    }
    
    # Count total parameters
    flat_config = flatten_config(config)
    analysis['total_parameters'] = len(flat_config)
    
    # Analyze access patterns if provided
    if access_patterns:
        accessed_paths = set(access_patterns.keys())
        config_paths = set(flat_config.keys())
        
        analysis['unused_parameters'] = list(config_paths - accessed_paths)
        analysis['frequently_accessed'] = [
            (path, count) for path, count in 
            sorted(access_patterns.items(), key=lambda x: x[1], reverse=True)[:10]
        ]
    
    # Categorize parameters
    for path in flat_config.keys():
        if any(neural_term in path.lower() for neural_term in ['neuron', 'synap', 'spike', 'membrane']):
            analysis['parameter_categories']['neural'] += 1
        elif any(tf_term in path.lower() for tf_term in ['transformer', 'attention', 'head']):
            analysis['parameter_categories']['transformer'] += 1
        elif any(diff_term in path.lower() for diff_term in ['diffusion', 'beta', 'timestep']):
            analysis['parameter_categories']['diffusion'] += 1
        else:
            analysis['parameter_categories']['general'] += 1
    
    # Calculate complexity score
    metadata = extract_config_metadata(config)
    analysis['complexity_score'] = (
        metadata['max_depth'] * 0.3 +
        metadata['total_keys'] * 0.0001 +
        len(metadata['data_types']) * 0.1
    )
    
    # Generate optimization suggestions
    if analysis['complexity_score'] > 5.0:
        analysis['optimization_suggestions'].append("Consider simplifying configuration structure")
    
    if len(analysis['unused_parameters']) > analysis['total_parameters'] * 0.2:
        analysis['optimization_suggestions'].append("Remove unused parameters to reduce complexity")
    
    return dict(analysis)

def benchmark_config_performance(config: Dict[str, Any], 
                               operations: int = 1000) -> Dict[str, float]:
    """Benchmark configuration access performance."""
    flat_config = flatten_config(config)
    paths = list(flat_config.keys())
    
    benchmarks = {}
    
    # Benchmark direct access
    start_time = time.time()
    for _ in range(operations):
        path = np.random.choice(paths)
        ConfigPath.get_value(config, path)
    benchmarks['direct_access_ms'] = (time.time() - start_time) * 1000 / operations
    
    # Benchmark cached access
    cache = ConfigCache()
    start_time = time.time()
    for _ in range(operations):
        path = np.random.choice(paths)
        cached_value = cache.get(path)
        if cached_value is None:
            value = ConfigPath.get_value(config, path)
            cache.set(path, value)
    benchmarks['cached_access_ms'] = (time.time() - start_time) * 1000 / operations
    
    # Benchmark batch access
    batch_paths = paths[:min(10, len(paths))]
    manager = PerformantConfigManager()
    start_time = time.time()
    for _ in range(operations // 10):
        manager.batch_get_config_values(batch_paths, lambda: config)
    benchmarks['batch_access_ms'] = (time.time() - start_time) * 1000 / (operations // 10)
    
    # Benchmark validation
    validator = ConfigValidator()
    validator.add_neural_constraints()
    start_time = time.time()
    for _ in range(10):  # Validation is expensive
        validator.validate(config)
    benchmarks['validation_ms'] = (time.time() - start_time) * 1000 / 10
    
    return benchmarks

def generate_config_documentation(config: Dict[str, Any], 
                                output_format: str = "markdown") -> str:
    """Generate documentation for configuration."""
    metadata = extract_config_metadata(config)
    flat_config = flatten_config(config)
    
    if output_format.lower() == "markdown":
        doc = "# Configuration Documentation\n\n"
        doc += f"**Generated on:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
        
        # Summary
        doc += "## Summary\n\n"
        doc += f"- **Total Parameters:** {metadata['total_keys']}\n"
        doc += f"- **Maximum Nesting Depth:** {metadata['max_depth']}\n"
        doc += f"- **Configuration Complexity:** {metadata.get('complexity_score', 'N/A')}\n\n"
        
        # Parameter breakdown
        doc += "## Parameter Breakdown\n\n"
        for data_type, count in metadata['data_types'].items():
            doc += f"- **{data_type.title()}:** {count}\n"
        doc += "\n"
        
        # Detailed parameters
        doc += "## Parameter Details\n\n"
        doc += "| Path | Type | Value | Description |\n"
        doc += "|------|------|-------|-------------|\n"
        
        for path, value in flat_config.items():
            value_type = type(value).__name__
            value_str = str(value)[:50] + "..." if len(str(value)) > 50 else str(value)
            doc += f"| `{path}` | {value_type} | `{value_str}` | Auto-generated |\n"
        
        return doc
    
    elif output_format.lower() == "html":
        doc = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>Configuration Documentation</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 40px; }
                table { border-collapse: collapse; width: 100%; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                th { background-color: #f2f2f2; }
                code { background-color: #f4f4f4; padding: 2px 4px; border-radius: 3px; }
            </style>
        </head>
        <body>
        """
        
        doc += f"<h1>Configuration Documentation</h1>"
        doc += f"<p><strong>Generated on:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>"
        
        doc += "<h2>Summary</h2>"
        doc += f"<ul>"
        doc += f"<li><strong>Total Parameters:</strong> {metadata['total_keys']}</li>"
        doc += f"<li><strong>Maximum Nesting Depth:</strong> {metadata['max_depth']}</li>"
        doc += f"</ul>"
        
        doc += "<h2>Parameter Details</h2>"
        doc += "<table>"
        doc += "<tr><th>Path</th><th>Type</th><th>Value</th></tr>"
        
        for path, value in flat_config.items():
            value_type = type(value).__name__
            value_str = str(value)[:100] + "..." if len(str(value)) > 100 else str(value)
            doc += f"<tr><td><code>{path}</code></td><td>{value_type}</td><td><code>{value_str}</code></td></tr>"
        
        doc += "</table>"
        doc += "</body></html>"
        
        return doc
    
    else:
        raise ValueError(f"Unsupported documentation format: {output_format}")

# ============================================================================
# Module Initialization
# ============================================================================

def initialize_config_utils() -> None:
    """Initialize configuration utilities module."""
    try:
        logger.info("Initializing ULTRA configuration utilities...")
        
        # Initialize global utilities
        utils = get_config_utils()
        
        # Setup default configurations
        logger.info("Configuration utilities initialized successfully")
        
    except Exception as e:
        logger.error(f"Failed to initialize configuration utilities: {e}")
        raise

# Initialize on import
try:
    initialize_config_utils()
except Exception as e:
    logger.warning(f"Configuration utilities initialization warning: {e}")

logger.info("ULTRA configuration utilities module loaded successfully")
    