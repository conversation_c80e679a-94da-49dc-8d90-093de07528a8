#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ULTRA Diffusion Reasoning System - Comprehensive Test Suite
===========================================================

This is an extremely challenging test suite designed to evaluate every aspect 
of the ULTRA diffusion reasoning system. It tests the limits of the system's
capabilities across all modules and reasoning paradigms.

Test Categories:
1. Basic Functionality Tests (Sanity Checks)
2. Mathematical Rigor Tests (Advanced Mathematics)
3. Reasoning Capability Tests (Complex Logic)
4. Performance Stress Tests (Scalability & Speed)
5. Edge Case Robustness Tests (Error Handling)
6. Integration Tests (Module Interactions)
7. Advanced Reasoning Tests (PhD-Level Challenges)
8. Real-World Application Tests (Practical Scenarios)

Author: ULTRA Development Team
Version: 1.0.0
"""









import sys
import os
import time
import traceback
import math
import random
import json
from pathlib import Path
from typing import Dict, List, Tuple, Any, Optional, Union
from dataclasses import dataclass
from collections import defaultdict
import warnings

import numpy as np
import torch
import torch.nn.functional as F
from scipy import stats, spatial, optimize
import matplotlib.pyplot as plt
import seaborn as sns

# Add ULTRA modules to path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))
sys.path.insert(0, str(current_dir.parent.parent.parent))

# Import ULTRA modules
try:
    # Try direct import from current directory first
    from thought_latent_space import (
        ThoughtLatentSpace,
        ThoughtVector, 
        ThoughtSpaceConfig,
        AdvancedMathOperations,
        InformationTheoreticMeasures,
        create_thought_latent_space
    )
    
    # Create minimal DiffusionBasedReasoning class
    class DiffusionBasedReasoning:
        def __init__(self, config=None, force_cpu=False):
            self.config = config or {}
            self.device = torch.device('cpu' if force_cpu else 'cuda' if torch.cuda.is_available() else 'cpu')
            thought_config = ThoughtSpaceConfig(
                dimension=config.get('thought_space_dim', 512),
                concept_dim=config.get('concept_dim', 384),
                hierarchical_levels=config.get('hierarchical_levels', 3),
                device=str(self.device)
            )
            self.thought_space = create_thought_latent_space(thought_config, device=str(self.device))
            
        def get_system_status(self):
            return f"ULTRA System Active (Device: {self.device})"
    
    ULTRA_AVAILABLE = True
    print("✅ ULTRA modules imported successfully")
    
except ImportError as e:
    print(f"❌ Could not import ULTRA modules: {e}")
    ULTRA_AVAILABLE = False

# Test configuration
DEVICE = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
TEST_RESULTS_DIR = Path("ultra_test_results")
TEST_RESULTS_DIR.mkdir(exist_ok=True)



@dataclass
class TestResult:
    """Container for test results."""
    test_name: str
    category: str
    passed: bool
    score: float  # 0.0 to 1.0
    execution_time: float
    error_message: Optional[str] = None
    details: Optional[Dict[str, Any]] = None
    difficulty: str = "medium"  # easy, medium, hard, extreme

class ULTRATestSuite:
    """Comprehensive test suite for ULTRA diffusion reasoning system."""
    
    def __init__(self, device: str = 'auto', verbose: bool = True):
        """Initialize the test suite."""
        self.device = torch.device(device if device != 'auto' else DEVICE)
        self.verbose = verbose
        self.results: List[TestResult] = []
        self.test_start_time = None
        self.system = None
        
        # Test configuration
        self.test_config = {
            'dimension': 512,
            'concept_dim': 384,
            'hierarchical_levels': 3,
            'num_timesteps': 100,  # Reduced for testing
            'batch_size': 16,
            'tolerance': 1e-4,
            'max_test_time': 300,  # 5 minutes per test
        }
        
        print(f"🧠 ULTRA Test Suite initialized on {self.device}")
        print(f"📊 Test results will be saved to: {TEST_RESULTS_DIR}")
    
    def setup_system(self) -> bool:
        """Setup the ULTRA diffusion reasoning system."""
        try:
            if not ULTRA_AVAILABLE:
                raise ImportError("ULTRA modules not available")
            
            # Initialize the main system
            config = {
                'concept_dim': self.test_config['concept_dim'],
                'thought_space_dim': self.test_config['dimension'],
                'diffusion_steps': self.test_config['num_timesteps'],
                'hierarchical_levels': self.test_config['hierarchical_levels'],
                'device': str(self.device),
                'enable_advanced_math': True,
                'enable_validation': True
            }
            
            self.system = DiffusionBasedReasoning(config=config, force_cpu=(self.device.type == 'cpu'))
            
            if self.verbose:
                print(f"✅ ULTRA system initialized successfully")
                print(f"📈 System status: {self.system.get_system_status()}")
            
            return True
            
        except Exception as e:
            if self.verbose:
                print(f"❌ Failed to setup ULTRA system: {e}")
            return False
    
    def log_result(self, result: TestResult):
        """Log a test result."""
        self.results.append(result)
        
        if self.verbose:
            status = "✅ PASS" if result.passed else "❌ FAIL"
            print(f"{status} [{result.category}] {result.test_name} "
                  f"(Score: {result.score:.3f}, Time: {result.execution_time:.3f}s, "
                  f"Difficulty: {result.difficulty})")
            
            if not result.passed and result.error_message:
                print(f"   Error: {result.error_message}")
    
    def run_test(self, test_func, test_name: str, category: str, difficulty: str = "medium"):
        """Run a single test with error handling and timing."""
        start_time = time.time()
        
        try:
            score = test_func()
            execution_time = time.time() - start_time
            
            # Determine pass/fail based on score and difficulty
            pass_threshold = {
                'easy': 0.8,
                'medium': 0.6,
                'hard': 0.4,
                'extreme': 0.2
            }.get(difficulty, 0.6)
            
            passed = score >= pass_threshold
            
            result = TestResult(
                test_name=test_name,
                category=category,
                passed=passed,
                score=score,
                execution_time=execution_time,
                difficulty=difficulty
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            error_msg = f"{type(e).__name__}: {str(e)}"
            
            result = TestResult(
                test_name=test_name,
                category=category,
                passed=False,
                score=0.0,
                execution_time=execution_time,
                error_message=error_msg,
                difficulty=difficulty
            )
            
            if self.verbose:
                print(f"🔥 Exception in {test_name}: {error_msg}")
        
        self.log_result(result)
        return result
    
    # =======================================================================
    # CATEGORY 1: BASIC FUNCTIONALITY TESTS
    # =======================================================================
    
    def test_system_initialization(self) -> float:
        """Test basic system initialization and component availability."""
        if self.system is None:
            return 0.0
        
        score = 0.0
        checks = [
            (hasattr(self.system, 'diffusion'), "Diffusion module available"),
            (hasattr(self.system, 'thought_space'), "Thought space available"),
            (hasattr(self.system, 'reverse_diffusion'), "Reverse diffusion available"),
            (hasattr(self.system, 'uncertainty'), "Uncertainty quantification available"),
            (hasattr(self.system, 'inference'), "Inference engine available"),
        ]
        
        for check, description in checks:
            if check:
                score += 0.2
            elif self.verbose:
                print(f"   ⚠️  Missing: {description}")
        
        return score
    
    def test_thought_vector_operations(self) -> float:
        """Test basic ThoughtVector operations."""
        if self.system is None or not hasattr(self.system, 'thought_space'):
            return 0.0
        
        score = 0.0
        
        try:
            # Create test thought vectors
            v1 = torch.randn(self.test_config['dimension'], device=self.device)
            v2 = torch.randn(self.test_config['dimension'], device=self.device)
            
            tv1 = ThoughtVector(v1, source="test_vector_1")
            tv2 = ThoughtVector(v2, source="test_vector_2")
            
            # Test basic operations
            tests = [
                (lambda: tv1 + tv2, "Addition"),
                (lambda: tv1 - tv2, "Subtraction"),
                (lambda: tv1 * 2.0, "Scalar multiplication"),
                (lambda: tv1.similarity(tv2), "Similarity computation"),
                (lambda: tv1.normalize(), "Normalization"),
                (lambda: tv1.clone(), "Cloning"),
            ]
            
            for test_func, name in tests:
                try:
                    result = test_func()
                    score += 1.0 / len(tests)
                except Exception as e:
                    if self.verbose:
                        print(f"   ❌ {name} failed: {e}")
                        
        except Exception as e:
            if self.verbose:
                print(f"   ❌ ThoughtVector test setup failed: {e}")
        
        return score
    
    def test_encoding_decoding_cycle(self) -> float:
        """Test the encode-decode cycle for concept preservation."""
        if self.system is None or not hasattr(self.system, 'thought_space'):
            return 0.0
        
        try:
            # Generate test concepts
            original_concepts = torch.randn(10, self.test_config['concept_dim'], device=self.device)
            
            # Encode to thought space
            thought_vectors = []
            for i, concept in enumerate(original_concepts):
                tv = self.system.thought_space.encode(concept, return_thought_vectors=True)
                thought_vectors.append(tv)
            
            # Decode back to concept space
            reconstructed_concepts = []
            for tv in thought_vectors:
                reconstructed = self.system.thought_space.decode(tv)
                reconstructed_concepts.append(reconstructed)
            
            reconstructed_tensor = torch.stack(reconstructed_concepts)
            
            # Compute reconstruction error
            mse_error = F.mse_loss(reconstructed_tensor, original_concepts)
            cosine_sim = F.cosine_similarity(
                original_concepts.flatten(1), 
                reconstructed_tensor.flatten(1)
            ).mean()
            
            # Score based on reconstruction quality
            reconstruction_score = max(0, 1.0 - mse_error.item())
            similarity_score = cosine_sim.item()
            
            return (reconstruction_score + similarity_score) / 2
            
        except Exception as e:
            if self.verbose:
                print(f"   ❌ Encode-decode test failed: {e}")
            return 0.0
    
    # =======================================================================
    # CATEGORY 2: MATHEMATICAL RIGOR TESTS
    # =======================================================================
    
    def test_semantic_similarity_properties(self) -> float:
        """Test mathematical properties of semantic similarity."""
        if self.system is None or not hasattr(self.system, 'thought_space'):
            return 0.0
        
        score = 0.0
        num_tests = 5
        
        try:
            # Generate test vectors
            vectors = [
                torch.randn(self.test_config['dimension'], device=self.device)
                for _ in range(5)
            ]
            
            # Test reflexivity: sim(a, a) should be high
            reflexivity_scores = []
            for v in vectors:
                sim = AdvancedMathOperations.semantic_similarity(v.unsqueeze(0), v.unsqueeze(0))
                reflexivity_scores.append(sim.item())
            
            if all(score > 0.95 for score in reflexivity_scores):
                score += 1.0 / num_tests
            
            # Test symmetry: sim(a, b) = sim(b, a)
            symmetry_errors = []
            for i in range(len(vectors)):
                for j in range(i+1, len(vectors)):
                    sim_ab = AdvancedMathOperations.semantic_similarity(
                        vectors[i].unsqueeze(0), vectors[j].unsqueeze(0)
                    )
                    sim_ba = AdvancedMathOperations.semantic_similarity(
                        vectors[j].unsqueeze(0), vectors[i].unsqueeze(0)
                    )
                    symmetry_errors.append(abs(sim_ab.item() - sim_ba.item()))
            
            if max(symmetry_errors) < 1e-6:
                score += 1.0 / num_tests
            
            # Test triangle inequality (approximately)
            triangle_violations = 0
            for i in range(len(vectors)):
                for j in range(len(vectors)):
                    for k in range(len(vectors)):
                        if i != j != k != i:
                            sim_ik = AdvancedMathOperations.semantic_similarity(
                                vectors[i].unsqueeze(0), vectors[k].unsqueeze(0)
                            )
                            sim_ij = AdvancedMathOperations.semantic_similarity(
                                vectors[i].unsqueeze(0), vectors[j].unsqueeze(0)
                            )
                            sim_jk = AdvancedMathOperations.semantic_similarity(
                                vectors[j].unsqueeze(0), vectors[k].unsqueeze(0)
                            )
                            
                            # Convert similarity to distance
                            dist_ik = 1 - sim_ik.item()
                            dist_ij = 1 - sim_ij.item()
                            dist_jk = 1 - sim_jk.item()
                            
                            if dist_ik > dist_ij + dist_jk + 0.1:  # Allow some tolerance
                                triangle_violations += 1
            
            if triangle_violations == 0:
                score += 1.0 / num_tests
            
            # Test consistency across different methods
            method_consistency = []
            for i in range(len(vectors)-1):
                cosine_sim = AdvancedMathOperations.semantic_similarity(
                    vectors[i].unsqueeze(0), vectors[i+1].unsqueeze(0), method='cosine'
                )
                euclidean_sim = AdvancedMathOperations.semantic_similarity(
                    vectors[i].unsqueeze(0), vectors[i+1].unsqueeze(0), method='euclidean'
                )
                # Both should give reasonable similarity values
                method_consistency.append(0 <= cosine_sim <= 1 and 0 <= euclidean_sim <= 1)
            
            if all(method_consistency):
                score += 1.0 / num_tests
            
            # Test numerical stability
            extreme_vector = torch.ones(self.test_config['dimension'], device=self.device) * 1e6
            normal_vector = torch.randn(self.test_config['dimension'], device=self.device)
            
            try:
                sim = AdvancedMathOperations.semantic_similarity(
                    extreme_vector.unsqueeze(0), normal_vector.unsqueeze(0)
                )
                if torch.isfinite(sim):
                    score += 1.0 / num_tests
            except:
                pass
                
        except Exception as e:
            if self.verbose:
                print(f"   ❌ Semantic similarity test failed: {e}")
        
        return score
    
    def test_manifold_geometry(self) -> float:
        """Test advanced manifold geometry operations."""
        if self.system is None or not hasattr(self.system, 'thought_space'):
            return 0.0
        
        score = 0.0
        
        try:
            # Test spherical interpolation properties
            v1 = F.normalize(torch.randn(self.test_config['dimension'], device=self.device))
            v2 = F.normalize(torch.randn(self.test_config['dimension'], device=self.device))
            
            # Test that SLERP produces unit vectors
            interpolated = AdvancedMathOperations.spherical_interpolation(v1, v2, 0.5)
            norm = torch.norm(interpolated)
            
            if abs(norm.item() - 1.0) < 1e-4:
                score += 0.2
            
            # Test boundary conditions
            interp_0 = AdvancedMathOperations.spherical_interpolation(v1, v2, 0.0)
            interp_1 = AdvancedMathOperations.spherical_interpolation(v1, v2, 1.0)
            
            if torch.allclose(interp_0, v1, atol=1e-4) and torch.allclose(interp_1, v2, atol=1e-4):
                score += 0.2
            
            # Test monotonicity
            alphas = torch.linspace(0, 1, 11, device=self.device)
            distances_to_v1 = []
            
            for alpha in alphas:
                interp = AdvancedMathOperations.spherical_interpolation(v1, v2, alpha)
                dist = torch.norm(interp - v1)
                distances_to_v1.append(dist.item())
            
            # Distances should be monotonically increasing (approximately)
            if all(distances_to_v1[i] <= distances_to_v1[i+1] + 1e-3 for i in range(len(distances_to_v1)-1)):
                score += 0.2
            
            # Test concept composition properties
            concepts = [
                F.normalize(torch.randn(self.test_config['dimension'], device=self.device))
                for _ in range(5)
            ]
            
            # Test weighted average properties
            weights = [0.2, 0.2, 0.2, 0.2, 0.2]
            composition = AdvancedMathOperations.concept_composition(concepts, weights, 'weighted_sum')
            
            # Composition should be within reasonable bounds
            comp_norm = torch.norm(composition)
            if 0.1 < comp_norm < 10.0:  # Reasonable bounds
                score += 0.2
            
            # Test different composition methods
            try:
                avg_comp = AdvancedMathOperations.concept_composition(concepts, method='average')
                geo_comp = AdvancedMathOperations.concept_composition(concepts, method='geometric_mean')
                max_comp = AdvancedMathOperations.concept_composition(concepts, method='max')
                
                # All should produce valid tensors
                if all(torch.isfinite(comp).all() for comp in [avg_comp, geo_comp, max_comp]):
                    score += 0.2
            except:
                pass
                
        except Exception as e:
            if self.verbose:
                print(f"   ❌ Manifold geometry test failed: {e}")
        
        return score
    
    def test_information_theoretic_measures(self) -> float:
        """Test information-theoretic measures and their properties."""
        if self.system is None:
            return 0.0
        
        score = 0.0
        
        try:
            from ultra.ultra.diffusion_reasoning.thought_latent_space import InformationTheoreticMeasures
            
            # Test Jensen-Shannon divergence properties
            # Generate probability distributions
            p1 = F.softmax(torch.randn(100, device=self.device), dim=0)
            p2 = F.softmax(torch.randn(100, device=self.device), dim=0)
            p3 = p1.clone()  # Identical distribution
            
            # Test symmetry: JS(p, q) = JS(q, p)
            js_12 = InformationTheoreticMeasures.jensen_shannon_divergence(p1, p2)
            js_21 = InformationTheoreticMeasures.jensen_shannon_divergence(p2, p1)
            
            if abs(js_12.item() - js_21.item()) < 1e-6:
                score += 0.25
            
            # Test identity: JS(p, p) ≈ 0
            js_11 = InformationTheoreticMeasures.jensen_shannon_divergence(p1, p3)
            if js_11.item() < 1e-6:
                score += 0.25
            
            # Test boundedness: 0 ≤ JS(p, q) ≤ 1
            if 0 <= js_12.item() <= 1:
                score += 0.25
            
            # Test mutual information estimation
            # Create correlated variables
            x = torch.randn(1000, device=self.device)
            y = x + 0.1 * torch.randn(1000, device=self.device)  # Correlated
            z = torch.randn(1000, device=self.device)  # Independent
            
            mi_xy = InformationTheoreticMeasures.mutual_information_estimate(x, y)
            mi_xz = InformationTheoreticMeasures.mutual_information_estimate(x, z)
            
            # Correlated variables should have higher MI than independent ones
            if mi_xy > mi_xz:
                score += 0.25
                
        except Exception as e:
            if self.verbose:
                print(f"   ❌ Information-theoretic measures test failed: {e}")
        
        return score
    
    # =======================================================================
    # CATEGORY 3: REASONING CAPABILITY TESTS
    # =======================================================================
    
    def test_analogical_reasoning(self) -> float:
        """Test advanced analogical reasoning capabilities."""
        if self.system is None or not hasattr(self.system, 'thought_space'):
            return 0.0
        
        score = 0.0
        
        try:
            # Create semantic analogy test
            # "King is to Queen as Man is to Woman" style
            
            # Simulate gendered concepts (this is just for testing mathematical properties)
            king = torch.tensor([1.0, 0.8, 0.9, 0.2] + [0.0] * (self.test_config['dimension'] - 4), device=self.device)
            queen = torch.tensor([1.0, 0.2, 0.9, 0.8] + [0.0] * (self.test_config['dimension'] - 4), device=self.device)
            man = torch.tensor([0.5, 0.8, 0.1, 0.2] + [0.0] * (self.test_config['dimension'] - 4), device=self.device)
            woman = torch.tensor([0.5, 0.2, 0.1, 0.8] + [0.0] * (self.test_config['dimension'] - 4), device=self.device)
            
            # Register concepts
            self.system.thought_space.register_concept("king", king)
            self.system.thought_space.register_concept("queen", queen)
            self.system.thought_space.register_concept("man", man)
            self.system.thought_space.register_concept("woman", woman)
            
            # Test analogy: king:queen :: man:?
            result = self.system.thought_space.perform_analogy("king", "queen", "man")
            
            # The result should be similar to "woman"
            if isinstance(result, ThoughtVector):
                similarity_to_woman = result.similarity(ThoughtVector(woman))
            else:
                similarity_to_woman = AdvancedMathOperations.semantic_similarity(
                    result.unsqueeze(0), woman.unsqueeze(0)
                ).item()
            
            if similarity_to_woman > 0.7:
                score += 0.3
            
            # Test mathematical relationships
            # The analogy should preserve the relationship vector
            king_to_queen = queen - king
            man_to_result = result.vector if isinstance(result, ThoughtVector) else result
            man_to_result = man_to_result - man
            
            relationship_similarity = F.cosine_similarity(
                king_to_queen.unsqueeze(0), man_to_result.unsqueeze(0)
            ).item()
            
            if relationship_similarity > 0.5:
                score += 0.3
            
            # Test multiple analogy methods
            linear_result = self.system.thought_space.perform_analogy("king", "queen", "man", method="linear")
            spherical_result = self.system.thought_space.perform_analogy("king", "queen", "man", method="spherical")
            
            # Both methods should produce reasonable results
            if isinstance(linear_result, ThoughtVector) and isinstance(spherical_result, ThoughtVector):
                if torch.isfinite(linear_result.vector).all() and torch.isfinite(spherical_result.vector).all():
                    score += 0.2
            
            # Test transitivity in analogies
            # If A:B :: C:D and B:E :: D:F, then there should be some consistency
            # This is a more advanced test of analogical reasoning
            concepts = {}
            for name in ["A", "B", "C", "D", "E", "F"]:
                concepts[name] = F.normalize(torch.randn(self.test_config['dimension'], device=self.device))
                self.system.thought_space.register_concept(name, concepts[name])
            
            # Create consistent relationships
            relation = concepts["B"] - concepts["A"]
            concepts["D"] = concepts["C"] + relation
            concepts["F"] = concepts["E"] + relation
            
            self.system.thought_space.register_concept("D", concepts["D"])
            self.system.thought_space.register_concept("F", concepts["F"])
            
            # Test analogies
            result1 = self.system.thought_space.perform_analogy("A", "B", "C")
            result2 = self.system.thought_space.perform_analogy("B", "E", "D")
            
            # Results should be close to expected values
            if isinstance(result1, ThoughtVector):
                sim1 = result1.similarity(ThoughtVector(concepts["D"]))
                if sim1 > 0.8:
                    score += 0.1
            
            if isinstance(result2, ThoughtVector):
                sim2 = result2.similarity(ThoughtVector(concepts["F"]))
                if sim2 > 0.8:
                    score += 0.1
                    
        except Exception as e:
            if self.verbose:
                print(f"   ❌ Analogical reasoning test failed: {e}")
        
        return score
    
    def test_compositional_reasoning(self) -> float:
        """Test compositional reasoning and concept combination."""
        if self.system is None or not hasattr(self.system, 'thought_space'):
            return 0.0
        
        score = 0.0
        
        try:
            # Create base concepts
            concepts = {}
            for concept_name in ["red", "blue", "round", "square", "large", "small"]:
                vector = F.normalize(torch.randn(self.test_config['dimension'], device=self.device))
                concepts[concept_name] = vector
                self.system.thought_space.register_concept(concept_name, vector)
            
            # Test simple composition
            red_round = self.system.thought_space.compose_concepts(["red", "round"])
            
            if isinstance(red_round, ThoughtVector):
                # The composition should be similar to both components
                red_sim = red_round.similarity(ThoughtVector(concepts["red"]))
                round_sim = red_round.similarity(ThoughtVector(concepts["round"]))
                
                if red_sim > 0.3 and round_sim > 0.3:
                    score += 0.2
            
            # Test weighted composition
            weights = [0.8, 0.2]
            weighted_comp = self.system.thought_space.compose_concepts(
                ["red", "round"], weights=weights, method="weighted_average"
            )
            
            if isinstance(weighted_comp, ThoughtVector):
                # Should be more similar to "red" than "round"
                red_sim = weighted_comp.similarity(ThoughtVector(concepts["red"]))
                round_sim = weighted_comp.similarity(ThoughtVector(concepts["round"]))
                
                if red_sim > round_sim:
                    score += 0.2
            
            # Test complex compositions
            complex_concept = self.system.thought_space.compose_concepts(
                ["red", "large", "round"], method="weighted_average"
            )
            
            if isinstance(complex_concept, ThoughtVector) and torch.isfinite(complex_concept.vector).all():
                score += 0.2
            
            # Test composition properties
            # Commutativity: compose(A, B) ≈ compose(B, A)
            comp1 = self.system.thought_space.compose_concepts(["red", "blue"])
            comp2 = self.system.thought_space.compose_concepts(["blue", "red"])
            
            if isinstance(comp1, ThoughtVector) and isinstance(comp2, ThoughtVector):
                similarity = comp1.similarity(comp2)
                if similarity > 0.95:  # Should be very similar
                    score += 0.2
            
            # Test decomposition
            # Create a known composition and try to decompose it
            known_components = ["red", "round", "large"]
            known_weights = [0.5, 0.3, 0.2]
            
            composed = self.system.thought_space.compose_concepts(
                known_components, weights=known_weights, method="weighted_average"
            )
            
            if isinstance(composed, ThoughtVector):
                # Try to decompose it
                basis_concepts = {name: self.system.thought_space[name] for name in known_components}
                decomposition = self.system.thought_space.decompose_concept(
                    composed, basis_concepts, method="sparse_coding"
                )
                
                # Check if decomposition recovers similar weights
                total_error = 0
                for i, name in enumerate(known_components):
                    recovered_weight = decomposition.get(name, 0)
                    total_error += abs(recovered_weight - known_weights[i])
                
                if total_error < 0.5:  # Allow some error
                    score += 0.2
                    
        except Exception as e:
            if self.verbose:
                print(f"   ❌ Compositional reasoning test failed: {e}")
        
        return score
    
    def test_causal_reasoning(self) -> float:
        """Test causal reasoning and intervention capabilities."""
        if self.system is None:
            return 0.0
        
        score = 0.0
        
        try:
            # Create a simple causal model: cause -> effect
            # This tests the system's ability to understand causal relationships
            
            # Define cause and effect concepts
            cause_vector = F.normalize(torch.randn(self.test_config['dimension'], device=self.device))
            effect_vector = F.normalize(torch.randn(self.test_config['dimension'], device=self.device))
            
            # Create a transformation that represents causation
            causal_transform = torch.randn(self.test_config['dimension'], self.test_config['dimension'], device=self.device)
            causal_transform = F.normalize(causal_transform, p=2, dim=1)
            
            # Apply causal transformation
            predicted_effect = torch.mv(causal_transform, cause_vector)
            predicted_effect = F.normalize(predicted_effect)
            
            # Test if the system can learn this relationship
            if hasattr(self.system, 'thought_space'):
                self.system.thought_space.register_concept("cause", cause_vector)
                self.system.thought_space.register_concept("effect", predicted_effect)
                
                # Test causal intervention: what happens if we change the cause?
                modified_cause = cause_vector + 0.1 * torch.randn_like(cause_vector)
                modified_cause = F.normalize(modified_cause)
                
                predicted_modified_effect = torch.mv(causal_transform, modified_cause)
                predicted_modified_effect = F.normalize(predicted_modified_effect)
                
                # The system should be able to predict the modified effect
                self.system.thought_space.register_concept("modified_cause", modified_cause)
                
                # Use analogical reasoning for causal prediction
                # cause:effect :: modified_cause:?
                predicted_by_analogy = self.system.thought_space.perform_analogy(
                    "cause", "effect", "modified_cause"
                )
                
                if isinstance(predicted_by_analogy, ThoughtVector):
                    similarity = F.cosine_similarity(
                        predicted_by_analogy.vector.unsqueeze(0),
                        predicted_modified_effect.unsqueeze(0)
                    ).item()
                    
                    if similarity > 0.5:
                        score += 0.4
            
            # Test counterfactual reasoning
            # "What would have happened if the cause was different?"
            counterfactual_cause = -cause_vector  # Opposite cause
            counterfactual_effect = torch.mv(causal_transform, counterfactual_cause)
            counterfactual_effect = F.normalize(counterfactual_effect)
            
            if hasattr(self.system, 'thought_space'):
                self.system.thought_space.register_concept("counterfactual_cause", counterfactual_cause)
                
                predicted_counterfactual = self.system.thought_space.perform_analogy(
                    "cause", "effect", "counterfactual_cause"
                )
                
                if isinstance(predicted_counterfactual, ThoughtVector):
                    similarity = F.cosine_similarity(
                        predicted_counterfactual.vector.unsqueeze(0),
                        counterfactual_effect.unsqueeze(0)
                    ).item()
                    
                    if similarity > 0.3:  # Lower threshold for counterfactuals
                        score += 0.3
            
            # Test temporal reasoning
            # Can the system understand sequences: A -> B -> C?
            concept_A = F.normalize(torch.randn(self.test_config['dimension'], device=self.device))
            concept_B = torch.mv(causal_transform, concept_A)
            concept_B = F.normalize(concept_B)
            concept_C = torch.mv(causal_transform, concept_B)
            concept_C = F.normalize(concept_C)
            
            if hasattr(self.system, 'thought_space'):
                self.system.thought_space.register_concept("step_A", concept_A)
                self.system.thought_space.register_concept("step_B", concept_B)
                self.system.thought_space.register_concept("step_C", concept_C)
                
                # Predict step C from step A
                predicted_C = self.system.thought_space.perform_analogy("step_A", "step_B", "step_B")
                
                if isinstance(predicted_C, ThoughtVector):
                    similarity = predicted_C.similarity(ThoughtVector(concept_C))
                    if similarity > 0.4:
                        score += 0.3
                        
        except Exception as e:
            if self.verbose:
                print(f"   ❌ Causal reasoning test failed: {e}")
        
        return score
    
    # =======================================================================
    # CATEGORY 4: PERFORMANCE STRESS TESTS
    # =======================================================================
    
    def test_scalability_stress(self) -> float:
        """Test system performance under heavy load."""
        if self.system is None:
            return 0.0
        
        score = 0.0
        
        try:
            # Test large batch processing
            large_batch_size = 100
            large_concepts = torch.randn(
                large_batch_size, self.test_config['concept_dim'], device=self.device
            )
            
            start_time = time.time()
            
            if hasattr(self.system, 'thought_space'):
                # Encode large batch
                encoded_concepts = []
                for i in range(large_batch_size):
                    tv = self.system.thought_space.encode(
                        large_concepts[i], return_thought_vectors=True
                    )
                    encoded_concepts.append(tv)
                
                # Decode large batch
                decoded_concepts = []
                for tv in encoded_concepts:
                    decoded = self.system.thought_space.decode(tv)
                    decoded_concepts.append(decoded)
                
                batch_time = time.time() - start_time
                
                # Should complete within reasonable time (adjust threshold as needed)
                if batch_time < 30.0:  # 30 seconds for 100 concepts
                    score += 0.3
                
                # Test memory efficiency
                if len(encoded_concepts) == large_batch_size:
                    score += 0.2
            
            # Test many small operations
            start_time = time.time()
            
            num_operations = 1000
            small_vectors = [
                F.normalize(torch.randn(self.test_config['dimension'], device=self.device))
                for _ in range(10)
            ]
            
            similarities = []
            for i in range(num_operations):
                v1 = random.choice(small_vectors)
                v2 = random.choice(small_vectors)
                sim = AdvancedMathOperations.semantic_similarity(v1.unsqueeze(0), v2.unsqueeze(0))
                similarities.append(sim.item())
            
            operation_time = time.time() - start_time
            
            if operation_time < 10.0:  # Should be fast
                score += 0.3
            
            # Test concurrent operations (if threading is supported)
            if hasattr(self.system, 'thought_space'):
                start_time = time.time()
                
                # Simulate concurrent access
                concepts_to_register = [
                    (f"stress_test_{i}", F.normalize(torch.randn(self.test_config['dimension'], device=self.device)))
                    for i in range(50)
                ]
                
                for name, vector in concepts_to_register:
                    self.system.thought_space.register_concept(name, vector)
                
                concurrent_time = time.time() - start_time
                
                if concurrent_time < 5.0 and len(self.system.thought_space.concept_registry) >= 50:
                    score += 0.2
                    
        except Exception as e:
            if self.verbose:
                print(f"   ❌ Scalability stress test failed: {e}")
        
        return score
    
    def test_memory_efficiency(self) -> float:
        """Test memory usage and efficiency."""
        if self.system is None:
            return 0.0
        
        score = 0.0
        
        try:
            import psutil
            import gc
            
            # Get initial memory usage
            process = psutil.Process()
            initial_memory = process.memory_info().rss / 1024 / 1024  # MB
            
            # Create many concepts
            num_concepts = 500
            concept_data = []
            
            if hasattr(self.system, 'thought_space'):
                for i in range(num_concepts):
                    vector = torch.randn(self.test_config['dimension'], device=self.device)
                    concept_name = f"memory_test_{i}"
                    
                    # Register concept
                    self.system.thought_space.register_concept(concept_name, vector)
                    concept_data.append((concept_name, vector))
                
                # Check memory usage
                after_creation_memory = process.memory_info().rss / 1024 / 1024  # MB
                memory_increase = after_creation_memory - initial_memory
                
                # Memory increase should be reasonable (less than 500MB for 500 concepts)
                if memory_increase < 500:
                    score += 0.3
                
                # Test memory cleanup
                for concept_name, _ in concept_data[:250]:  # Remove half
                    self.system.thought_space.unregister_concept(concept_name)
                
                # Force garbage collection
                gc.collect()
                if self.device.type == 'cuda':
                    torch.cuda.empty_cache()
                
                after_cleanup_memory = process.memory_info().rss / 1024 / 1024  # MB
                
                # Memory should decrease after cleanup
                if after_cleanup_memory < after_creation_memory:
                    score += 0.3
                
                # Test that remaining concepts are still accessible
                remaining_concepts = concept_data[250:]
                accessible_count = 0
                
                for concept_name, original_vector in remaining_concepts[:10]:  # Test a sample
                    if concept_name in self.system.thought_space:
                        retrieved = self.system.thought_space[concept_name]
                        if torch.allclose(retrieved.vector, original_vector, atol=1e-6):
                            accessible_count += 1
                
                if accessible_count >= 8:  # Most should be accessible
                    score += 0.4
                    
        except ImportError:
            if self.verbose:
                print("   ⚠️  psutil not available, skipping memory efficiency test")
            score = 0.5  # Partial credit if can't test memory
        except Exception as e:
            if self.verbose:
                print(f"   ❌ Memory efficiency test failed: {e}")
        
        return score
    
    def test_numerical_stability(self) -> float:
        """Test numerical stability under extreme conditions."""
        if self.system is None:
            return 0.0
        
        score = 0.0
        
        try:
            # Test with very large numbers
            large_vector = torch.ones(self.test_config['dimension'], device=self.device) * 1e6
            normal_vector = torch.randn(self.test_config['dimension'], device=self.device)
            
            # Test similarity computation with extreme values
            try:
                sim = AdvancedMathOperations.semantic_similarity(
                    large_vector.unsqueeze(0), normal_vector.unsqueeze(0)
                )
                if torch.isfinite(sim):
                    score += 0.2
            except:
                pass
            
            # Test with very small numbers
            small_vector = torch.ones(self.test_config['dimension'], device=self.device) * 1e-10
            
            try:
                sim = AdvancedMathOperations.semantic_similarity(
                    small_vector.unsqueeze(0), normal_vector.unsqueeze(0)
                )
                if torch.isfinite(sim):
                    score += 0.2
            except:
                pass
            
            # Test with NaN and inf values
            nan_vector = torch.full((self.test_config['dimension'],), float('nan'), device=self.device)
            inf_vector = torch.full((self.test_config['dimension'],), float('inf'), device=self.device)
            
            # System should handle these gracefully
            try:
                sim = AdvancedMathOperations.semantic_similarity(
                    nan_vector.unsqueeze(0), normal_vector.unsqueeze(0)
                )
                # Should either handle gracefully or raise appropriate exception
                score += 0.1
            except (ValueError, RuntimeError):
                # Appropriate exception handling
                score += 0.1
            
            # Test gradient flow with extreme values
            if hasattr(self.system, 'thought_space'):
                try:
                    # Create a vector that requires gradient
                    test_vector = torch.randn(
                        self.test_config['concept_dim'], device=self.device, requires_grad=True
                    )
                    
                    # Scale to extreme value
                    scaled_vector = test_vector * 1e8
                    
                    # Encode (this should handle the extreme values)
                    encoded = self.system.thought_space.encode(scaled_vector.detach())
                    
                    if isinstance(encoded, ThoughtVector):
                        if torch.isfinite(encoded.vector).all():
                            score += 0.2
                    elif torch.isfinite(encoded).all():
                        score += 0.2
                        
                except:
                    pass
            
            # Test composition with extreme weights
            if hasattr(self.system, 'thought_space'):
                try:
                    concepts = [
                        F.normalize(torch.randn(self.test_config['dimension'], device=self.device))
                        for _ in range(3)
                    ]
                    
                    # Register concepts
                    for i, concept in enumerate(concepts):
                        self.system.thought_space.register_concept(f"extreme_test_{i}", concept)
                    
                    # Test with extreme weights
                    extreme_weights = [1e6, 1e-6, 1.0]
                    composition = self.system.thought_space.compose_concepts(
                        [f"extreme_test_{i}" for i in range(3)],
                        weights=extreme_weights,
                        method="weighted_average"
                    )
                    
                    if isinstance(composition, ThoughtVector):
                        if torch.isfinite(composition.vector).all():
                            score += 0.3
                            
                except:
                    pass
                    
        except Exception as e:
            if self.verbose:
                print(f"   ❌ Numerical stability test failed: {e}")
        
        return score
    
    # =======================================================================
    # CATEGORY 5: EDGE CASE ROBUSTNESS TESTS
    # =======================================================================
    
    def test_error_handling(self) -> float:
        """Test system robustness to various error conditions."""
        if self.system is None:
            return 0.0
        
        score = 0.0
        
        try:
            # Test invalid input dimensions
            if hasattr(self.system, 'thought_space'):
                try:
                    wrong_dim_vector = torch.randn(self.test_config['concept_dim'] + 100, device=self.device)
                    self.system.thought_space.encode(wrong_dim_vector)
                    # Should raise an error
                except (ValueError, RuntimeError):
                    score += 0.2
                
                # Test empty inputs
                try:
                    self.system.thought_space.compose_concepts([])
                    # Should raise an error
                except (ValueError, RuntimeError):
                    score += 0.2
                
                # Test invalid concept names
                try:
                    self.system.thought_space.perform_analogy("nonexistent1", "nonexistent2", "nonexistent3")
                    # Should raise an error
                except (ValueError, KeyError, RuntimeError):
                    score += 0.2
                
                # Test None inputs
                try:
                    self.system.thought_space.encode(None)
                    # Should raise an error
                except (TypeError, ValueError):
                    score += 0.1
                
                # Test mismatched tensor shapes in operations
                try:
                    v1 = torch.randn(100, device=self.device)
                    v2 = torch.randn(200, device=self.device)
                    tv1 = ThoughtVector(v1)
                    tv2 = ThoughtVector(v2)
                    result = tv1 + tv2
                    # Should raise an error
                except (ValueError, RuntimeError):
                    score += 0.2
                
                # Test device mismatch (if CUDA available)
                if torch.cuda.is_available() and self.device.type == 'cuda':
                    try:
                        cpu_vector = torch.randn(self.test_config['dimension'])
                        gpu_vector = torch.randn(self.test_config['dimension'], device='cuda')
                        
                        tv_cpu = ThoughtVector(cpu_vector)
                        tv_gpu = ThoughtVector(gpu_vector)
                        
                        # This should either work (automatic device handling) or raise appropriate error
                        result = tv_cpu + tv_gpu
                        score += 0.1  # Credit for handling device mismatch
                    except (RuntimeError, ValueError):
                        score += 0.1  # Credit for appropriate error handling
                        
        except Exception as e:
            if self.verbose:
                print(f"   ❌ Error handling test setup failed: {e}")
        
        return score
    
    def test_boundary_conditions(self) -> float:
        """Test behavior at boundary conditions."""
        if self.system is None:
            return 0.0
        
        score = 0.0
        
        try:
            # Test with minimal dimension
            if hasattr(self.system, 'thought_space'):
                # Test with zero vectors
                zero_vector = torch.zeros(self.test_config['dimension'], device=self.device)
                try:
                    encoded = self.system.thought_space.encode(
                        torch.zeros(self.test_config['concept_dim'], device=self.device)
                    )
                    if isinstance(encoded, (torch.Tensor, ThoughtVector)):
                        score += 0.2
                except:
                    pass
                
                # Test with unit vectors
                unit_vector = torch.zeros(self.test_config['concept_dim'], device=self.device)
                unit_vector[0] = 1.0
                
                try:
                    encoded = self.system.thought_space.encode(unit_vector)
                    if isinstance(encoded, (torch.Tensor, ThoughtVector)):
                        score += 0.2
                except:
                    pass
                
                # Test similarity at boundaries
                identical_v1 = F.normalize(torch.randn(self.test_config['dimension'], device=self.device))
                identical_v2 = identical_v1.clone()
                
                sim = AdvancedMathOperations.semantic_similarity(
                    identical_v1.unsqueeze(0), identical_v2.unsqueeze(0)
                )
                
                # Identical vectors should have similarity close to 1
                if abs(sim.item() - 1.0) < 1e-4:
                    score += 0.2
                
                # Test orthogonal vectors
                v1 = torch.zeros(self.test_config['dimension'], device=self.device)
                v2 = torch.zeros(self.test_config['dimension'], device=self.device)
                v1[0] = 1.0
                v2[1] = 1.0
                
                sim_orthogonal = AdvancedMathOperations.semantic_similarity(v1.unsqueeze(0), v2.unsqueeze(0))
                
                # Orthogonal vectors should have low similarity
                if sim_orthogonal.item() < 0.1:
                    score += 0.2
                
                # Test interpolation boundaries
                start_vector = F.normalize(torch.randn(self.test_config['dimension'], device=self.device))
                end_vector = F.normalize(torch.randn(self.test_config['dimension'], device=self.device))
                
                # Test t=0 and t=1
                interp_0 = AdvancedMathOperations.spherical_interpolation(start_vector, end_vector, 0.0)
                interp_1 = AdvancedMathOperations.spherical_interpolation(start_vector, end_vector, 1.0)
                
                if torch.allclose(interp_0, start_vector, atol=1e-4) and torch.allclose(interp_1, end_vector, atol=1e-4):
                    score += 0.2
                    
        except Exception as e:
            if self.verbose:
                print(f"   ❌ Boundary conditions test failed: {e}")
        
        return score
    
    def test_consistency_under_perturbation(self) -> float:
        """Test system consistency when inputs are slightly perturbed."""
        if self.system is None or not hasattr(self.system, 'thought_space'):
            return 0.0
        
        score = 0.0
        
        try:
            # Create a base concept
            base_concept = torch.randn(self.test_config['concept_dim'], device=self.device)
            base_encoded = self.system.thought_space.encode(base_concept, return_thought_vectors=True)
            
            # Test encoding consistency
            perturbation_magnitudes = [1e-6, 1e-5, 1e-4, 1e-3]
            encoding_consistencies = []
            
            for magnitude in perturbation_magnitudes:
                perturbed_concept = base_concept + magnitude * torch.randn_like(base_concept)
                perturbed_encoded = self.system.thought_space.encode(perturbed_concept, return_thought_vectors=True)
                
                if isinstance(base_encoded, ThoughtVector) and isinstance(perturbed_encoded, ThoughtVector):
                    similarity = base_encoded.similarity(perturbed_encoded)
                    encoding_consistencies.append(similarity)
            
            # Smaller perturbations should lead to higher similarity
            if len(encoding_consistencies) >= 2:
                if all(encoding_consistencies[i] >= encoding_consistencies[i+1] - 0.1 
                       for i in range(len(encoding_consistencies)-1)):
                    score += 0.3
            
            # Test similarity consistency
            concept1 = torch.randn(self.test_config['dimension'], device=self.device)
            concept2 = torch.randn(self.test_config['dimension'], device=self.device)
            
            base_similarity = AdvancedMathOperations.semantic_similarity(
                concept1.unsqueeze(0), concept2.unsqueeze(0)
            ).item()
            
            similarity_variations = []
            for magnitude in [1e-5, 1e-4, 1e-3]:
                perturbed1 = concept1 + magnitude * torch.randn_like(concept1)
                perturbed2 = concept2 + magnitude * torch.randn_like(concept2)
                
                perturbed_similarity = AdvancedMathOperations.semantic_similarity(
                    perturbed1.unsqueeze(0), perturbed2.unsqueeze(0)
                ).item()
                
                similarity_variations.append(abs(perturbed_similarity - base_similarity))
            
            # Similarity should be relatively stable under small perturbations
            if all(var < 0.1 for var in similarity_variations):
                score += 0.3
            
            # Test analogical reasoning consistency
            if len(self.system.thought_space.concept_registry) >= 3:
                concept_names = list(self.system.thought_space.concept_registry.keys())[:3]
                
                base_analogy = self.system.thought_space.perform_analogy(
                    concept_names[0], concept_names[1], concept_names[2]
                )
                
                # Slightly perturb one of the input concepts
                original_concept = self.system.thought_space[concept_names[0]].vector.clone()
                perturbed_vector = original_concept + 1e-4 * torch.randn_like(original_concept)
                
                self.system.thought_space.register_concept("perturbed_concept", perturbed_vector)
                
                perturbed_analogy = self.system.thought_space.perform_analogy(
                    "perturbed_concept", concept_names[1], concept_names[2]
                )
                
                if isinstance(base_analogy, ThoughtVector) and isinstance(perturbed_analogy, ThoughtVector):
                    analogy_similarity = base_analogy.similarity(perturbed_analogy)
                    if analogy_similarity > 0.9:  # Should be very similar
                        score += 0.4
                        
        except Exception as e:
            if self.verbose:
                print(f"   ❌ Consistency under perturbation test failed: {e}")
        
        return score
    
    # =======================================================================
    # CATEGORY 6: INTEGRATION TESTS
    # =======================================================================
    
    def test_end_to_end_reasoning_pipeline(self) -> float:
        """Test complete reasoning pipeline from input to output."""
        if self.system is None:
            return 0.0
        
        score = 0.0
        
        try:
            # Simulate a complete reasoning task
            # Problem: Given concepts A, B, C and relationships, predict D
            
            # Create a mathematical pattern: A + B = C, B + C = D
            dim = self.test_config['concept_dim']
            
            concept_A = F.normalize(torch.randn(dim, device=self.device))
            concept_B = F.normalize(torch.randn(dim, device=self.device))
            concept_C = F.normalize(concept_A + concept_B)  # Mathematical relationship
            concept_D = F.normalize(concept_B + concept_C)  # Expected result
            
            if not hasattr(self.system, 'thought_space'):
                return 0.0
            
            # Step 1: Encode concepts into thought space
            self.system.thought_space.register_concept("A", concept_A)
            self.system.thought_space.register_concept("B", concept_B)
            self.system.thought_space.register_concept("C", concept_C)
            
            # Step 2: Learn the relationship A + B → C
            # Use compositional reasoning
            predicted_C = self.system.thought_space.compose_concepts(["A", "B"])
            
            if isinstance(predicted_C, ThoughtVector):
                actual_C = ThoughtVector(concept_C)
                c_similarity = predicted_C.similarity(actual_C)
                
                if c_similarity > 0.7:
                    score += 0.3
            
            # Step 3: Apply learned relationship to predict D = B + C
            predicted_D = self.system.thought_space.compose_concepts(["B", "C"])
            
            if isinstance(predicted_D, ThoughtVector):
                actual_D = ThoughtVector(concept_D)
                d_similarity = predicted_D.similarity(actual_D)
                
                if d_similarity > 0.6:
                    score += 0.3
            
            # Step 4: Use analogical reasoning as alternative approach
            # A:C :: B:D (A is to C as B is to D)
            analogy_D = self.system.thought_space.perform_analogy("A", "C", "B")
            
            if isinstance(analogy_D, ThoughtVector):
                actual_D = ThoughtVector(concept_D)
                analogy_similarity = analogy_D.similarity(actual_D)
                
                if analogy_similarity > 0.5:
                    score += 0.2
            
            # Step 5: Consistency check between different reasoning methods
            if isinstance(predicted_D, ThoughtVector) and isinstance(analogy_D, ThoughtVector):
                method_consistency = predicted_D.similarity(analogy_D)
                
                if method_consistency > 0.7:
                    score += 0.2
                    
        except Exception as e:
            if self.verbose:
                print(f"   ❌ End-to-end reasoning pipeline test failed: {e}")
        
        return score
    
    def test_cross_module_integration(self) -> float:
        """Test integration between different ULTRA modules."""
        if self.system is None:
            return 0.0
        
        score = 0.0
        
        try:
            # Test diffusion + thought space integration
            if hasattr(self.system, 'diffusion') and hasattr(self.system, 'thought_space'):
                # Create a concept in thought space
                test_concept = torch.randn(self.test_config['concept_dim'], device=self.device)
                thought_vector = self.system.thought_space.encode(test_concept, return_thought_vectors=True)
                
                if isinstance(thought_vector, ThoughtVector):
                    # Try to use diffusion for concept exploration
                    # This tests if diffusion can work with thought space representations
                    try:
                        # Simulate diffusion process (simplified)
                        if hasattr(self.system.diffusion, 'forward_diffusion'):
                            noise_schedule = torch.linspace(0.1, 0.9, 10, device=self.device)
                            
                            # Apply noise at different levels
                            noisy_concepts = []
                            for noise_level in noise_schedule:
                                noise = torch.randn_like(thought_vector.vector) * noise_level
                                noisy_concept = thought_vector.vector + noise
                                noisy_concepts.append(noisy_concept)
                            
                            # If this works without errors, integration is good
                            if len(noisy_concepts) == len(noise_schedule):
                                score += 0.3
                                
                    except Exception as diffusion_error:
                        if self.verbose:
                            print(f"   ⚠️  Diffusion integration issue: {diffusion_error}")
            
            # Test uncertainty + inference integration
            if hasattr(self.system, 'uncertainty') and hasattr(self.system, 'inference'):
                try:
                    # Create test data for uncertainty quantification
                    test_batch = torch.randn(5, self.test_config['concept_dim'], device=self.device)
                    
                    # Test if uncertainty module can process the data
                    if hasattr(self.system.uncertainty, 'estimate_uncertainty'):
                        uncertainty_estimates = []
                        for concept in test_batch:
                            # Simplified uncertainty estimation
                            uncertainty = torch.std(concept).item()
                            uncertainty_estimates.append(uncertainty)
                        
                        # Test if estimates are reasonable
                        if len(uncertainty_estimates) == len(test_batch):
                            score += 0.2
                    
                    # Test inference engine integration
                    if hasattr(self.system.inference, 'infer'):
                        # Create a simple inference task
                        premises = [
                            torch.randn(self.test_config['concept_dim'], device=self.device)
                            for _ in range(3)
                        ]
                        
                        # Test inference capability
                        for premise in premises:
                            try:
                                inference_result = self.system.inference.infer(premise)
                                if inference_result is not None:
                                    score += 0.1
                            except:
                                pass
                                
                except Exception as uncertainty_error:
                    if self.verbose:
                        print(f"   ⚠️  Uncertainty/Inference integration issue: {uncertainty_error}")
            
            # Test thought space + reverse diffusion integration
            if hasattr(self.system, 'thought_space') and hasattr(self.system, 'reverse_diffusion'):
                try:
                    # Create noisy concept
                    clean_concept = torch.randn(self.test_config['concept_dim'], device=self.device)
                    noisy_concept = clean_concept + 0.1 * torch.randn_like(clean_concept)
                    
                    # Encode to thought space
                    thought_representation = self.system.thought_space.encode(noisy_concept)
                    
                    # Test reverse diffusion (denoising)
                    if hasattr(self.system.reverse_diffusion, 'denoise'):
                        try:
                            denoised = self.system.reverse_diffusion.denoise(thought_representation)
                            if torch.isfinite(denoised).all():
                                score += 0.2
                        except:
                            pass
                            
                except Exception as reverse_diffusion_error:
                    if self.verbose:
                        print(f"   ⚠️  Reverse diffusion integration issue: {reverse_diffusion_error}")
                        
        except Exception as e:
            if self.verbose:
                print(f"   ❌ Cross-module integration test failed: {e}")
        
        return score

    # =======================================================================
    # CATEGORY 7: ADVANCED REASONING TESTS
    # =======================================================================
    
    def test_meta_reasoning(self) -> float:
        """Test meta-reasoning capabilities."""
        if self.system is None or not hasattr(self.system, 'thought_space'):
            return 0.0
        
        score = 0.0
        
        try:
            # Test reasoning about reasoning
            # Can the system reason about its own reasoning processes?
            
            # Create meta-concepts
            reasoning_concept = F.normalize(torch.randn(self.test_config['dimension'], device=self.device))
            logic_concept = F.normalize(torch.randn(self.test_config['dimension'], device=self.device))
            inference_concept = F.normalize(torch.randn(self.test_config['dimension'], device=self.device))
            
            self.system.thought_space.register_concept("reasoning", reasoning_concept)
            self.system.thought_space.register_concept("logic", logic_concept)
            self.system.thought_space.register_concept("inference", inference_concept)
            
            # Test meta-reasoning: reasoning about reasoning
            meta_reasoning = self.system.thought_space.compose_concepts(["reasoning", "reasoning"])
            
            if isinstance(meta_reasoning, ThoughtVector):
                # Meta-reasoning should be related to but distinct from basic reasoning
                basic_reasoning = ThoughtVector(reasoning_concept)
                similarity = meta_reasoning.similarity(basic_reasoning)
                
                if 0.3 < similarity < 0.9:  # Related but not identical
                    score += 0.5
            
            # Test reasoning about logic
            logic_reasoning = self.system.thought_space.compose_concepts(["reasoning", "logic"])
            if isinstance(logic_reasoning, ThoughtVector) and torch.isfinite(logic_reasoning.vector).all():
                score += 0.3
            
            # Test self-referential reasoning
            self_concept = self.system.thought_space.compose_concepts(["reasoning", "inference", "logic"])
            if isinstance(self_concept, ThoughtVector):
                # Test if the system can reason about itself
                self_similarity = self_concept.similarity(meta_reasoning)
                if self_similarity > 0.2:
                    score += 0.2
                    
        except Exception as e:
            if self.verbose:
                print(f"   ❌ Meta-reasoning test failed: {e}")
        
        return score

    # =======================================================================
    # MAIN TEST RUNNER
    # =======================================================================
    
    def run_all_tests(self) -> Dict[str, Any]:
        """Run the complete test suite and return comprehensive results."""
        print("\n🚀 Starting ULTRA Diffusion Reasoning System Test Suite")
        print("=" * 80)
        
        self.test_start_time = time.time()
        
        # Setup system
        if not self.setup_system():
            print("❌ Failed to setup ULTRA system. Cannot run tests.")
            return {"error": "System setup failed"}
        
        # Define test categories and their tests
        test_categories = {
            "Basic Functionality": [
                ("System Initialization", self.test_system_initialization, "easy"),
                ("ThoughtVector Operations", self.test_thought_vector_operations, "easy"),
                ("Encode-Decode Cycle", self.test_encoding_decoding_cycle, "easy"),
            ],
            "Mathematical Rigor": [
                ("Semantic Similarity Properties", self.test_semantic_similarity_properties, "medium"),
                ("Manifold Geometry", self.test_manifold_geometry, "hard"),
                ("Information Theoretic Measures", self.test_information_theoretic_measures, "hard"),
            ],
            "Reasoning Capabilities": [
                ("Analogical Reasoning", self.test_analogical_reasoning, "hard"),
                ("Compositional Reasoning", self.test_compositional_reasoning, "medium"),
                ("Causal Reasoning", self.test_causal_reasoning, "extreme"),
            ],
            "Performance & Stress": [
                ("Scalability Stress", self.test_scalability_stress, "medium"),
                ("Memory Efficiency", self.test_memory_efficiency, "medium"),
                ("Numerical Stability", self.test_numerical_stability, "hard"),
            ],
            "Edge Cases & Robustness": [
                ("Error Handling", self.test_error_handling, "medium"),
                ("Boundary Conditions", self.test_boundary_conditions, "hard"),
                ("Consistency Under Perturbation", self.test_consistency_under_perturbation, "hard"),
            ],
            "Integration Tests": [
                ("End-to-End Reasoning Pipeline", self.test_end_to_end_reasoning_pipeline, "extreme"),
                ("Cross-Module Integration", self.test_cross_module_integration, "extreme"),
            ],
            "Advanced Reasoning": [
                ("Meta-Reasoning", self.test_meta_reasoning, "extreme"),
            ]
        }
        
        # Run all tests
        category_results = {}
        
        for category, tests in test_categories.items():
            print(f"\n📋 Running {category} Tests:")
            print("-" * 50)
            
            category_scores = []
            for test_name, test_func, difficulty in tests:
                result = self.run_test(test_func, test_name, category, difficulty)
                category_scores.append(result.score)
            
            category_avg = sum(category_scores) / len(category_scores) if category_scores else 0
            category_results[category] = {
                "average_score": category_avg,
                "tests_passed": sum(1 for result in self.results if result.category == category and result.passed),
                "total_tests": len(tests)
            }
        
        # Generate final report
        total_time = time.time() - self.test_start_time
        return self.generate_final_report(category_results, total_time)
    
    def generate_final_report(self, category_results: Dict, total_time: float) -> Dict[str, Any]:
        """Generate comprehensive test report."""
        total_tests = len(self.results)
        passed_tests = sum(1 for result in self.results if result.passed)
        overall_score = sum(result.score for result in self.results) / total_tests if total_tests > 0 else 0
        
        print("\n" + "=" * 80)
        print("🧠 ULTRA DIFFUSION REASONING SYSTEM - TEST RESULTS")
        print("=" * 80)
        
        print(f"\n📊 OVERALL PERFORMANCE:")
        print(f"   • Tests Passed: {passed_tests}/{total_tests} ({passed_tests/total_tests*100:.1f}%)")
        print(f"   • Overall Score: {overall_score:.3f} ({overall_score*100:.1f}%)")
        print(f"   • Total Execution Time: {total_time:.2f} seconds")
        
        print(f"\n📋 CATEGORY BREAKDOWN:")
        for category, results in category_results.items():
            print(f"   • {category}: {results['average_score']:.3f} "
                  f"({results['tests_passed']}/{results['total_tests']} passed)")
        
        # Performance assessment
        if overall_score >= 0.8:
            assessment = "🌟 EXCELLENT - Production Ready"
        elif overall_score >= 0.6:
            assessment = "✅ GOOD - Minor Issues"
        elif overall_score >= 0.4:
            assessment = "⚠️  FAIR - Needs Improvement"
        else:
            assessment = "❌ POOR - Major Issues"
        
        print(f"\n🎯 SYSTEM ASSESSMENT: {assessment}")
        
        # Save detailed results
        report = {
            "overall_score": overall_score,
            "tests_passed": passed_tests,
            "total_tests": total_tests,
            "execution_time": total_time,
            "category_results": category_results,
            "assessment": assessment,
            "detailed_results": [
                {
                    "test_name": result.test_name,
                    "category": result.category,
                    "passed": result.passed,
                    "score": result.score,
                    "execution_time": result.execution_time,
                    "difficulty": result.difficulty,
                    "error_message": result.error_message
                }
                for result in self.results
            ]
        }
        
        # Save to file
        report_file = TEST_RESULTS_DIR / f"ultra_test_report_{int(time.time())}.json"
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2)
        
        print(f"\n📁 Detailed report saved to: {report_file}")
        
        return report


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="ULTRA Diffusion Reasoning System Test Suite")
    parser.add_argument("--device", default="auto", help="Device to run tests on (cpu/cuda/auto)")
    parser.add_argument("--verbose", action="store_true", help="Enable verbose output")
    parser.add_argument("--quick", action="store_true", help="Run only basic tests")
    
    args = parser.parse_args()
    
    # Create and run test suite
    test_suite = ULTRATestSuite(device=args.device, verbose=args.verbose)
    
    if args.quick:
        print("🏃‍♂️ Running quick test suite...")
        # Run only basic functionality tests
        test_suite.setup_system()
        test_suite.run_test(test_suite.test_system_initialization, "System Initialization", "Quick Test", "easy")
        test_suite.run_test(test_suite.test_encoding_decoding_cycle, "Encode-Decode Cycle", "Quick Test", "easy")
        print(f"\n✅ Quick tests completed: {len([r for r in test_suite.results if r.passed])}/{len(test_suite.results)} passed")
    else:
        # Run full test suite
        results = test_suite.run_all_tests()
        
        if "error" not in results:
            print(f"\n🎉 Test suite completed successfully!")
            print(f"📈 Final Score: {results['overall_score']:.3f}")
        else:
            print(f"\n❌ Test suite failed: {results['error']}")
                