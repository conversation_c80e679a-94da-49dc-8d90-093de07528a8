#!/usr/bin/env python3
"""
ULTRA: Ultimate Learning & Thought Reasoning Architecture
Python Wrapper for CUDA Conceptual Diffusion

This module provides a Python interface to the CUDA conceptual diffusion implementation,
allowing seamless integration with Python workflows while maintaining high performance.

Mathematical Foundation:
- Forward diffusion: q(z_t | z_{t-1}) = N(z_t; sqrt(1 - β_t) z_{t-1}, β_t I)
- Reverse diffusion: p_θ(z_{t-1} | z_t) = N(z_{t-1}; μ_θ(z_t, t), Σ_θ(z_t, t))
- Thought space: Hierarchical latent representations with semantic constraints
- Uncertainty quantification: Epistemic and aleatoric uncertainty estimation

Author: ULTRA Development Team
Version: 1.0.0
License: MIT
"""

import ctypes
import numpy as np
import os
import sys
import subprocess
import logging
import time
import threading
import atexit
from pathlib import Path
from typing import Optional, Tuple, List, Dict, Any, Union
import platform
import tempfile
import shutil

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Mathematical constants
PI = 3.141592653589793
EULER = 2.718281828459045
EPS = 1e-8

class ConceptualDiffusionConfig(ctypes.Structure):
    """Python wrapper for ConceptualDiffusionConfig struct"""
    _fields_ = [
        ("concept_dim", ctypes.c_int),
        ("num_timesteps", ctypes.c_int),
        ("batch_size", ctypes.c_int),
        ("hierarchical_levels", ctypes.c_int),
        ("beta_start", ctypes.c_float),
        ("beta_end", ctypes.c_float),
        ("guidance_scale", ctypes.c_float),
        ("time_embedding_scale", ctypes.c_float),
        ("noise_schedule_type", ctypes.c_int),
        ("precision_mode", ctypes.c_int),
        ("use_flash_attention", ctypes.c_int),
        ("enable_caching", ctypes.c_int),
        ("use_cuda", ctypes.c_int),
    ]

class UncertaintyQuantification(ctypes.Structure):
    """Python wrapper for UncertaintyQuantification struct"""
    _fields_ = [
        ("epistemic_uncertainty", ctypes.c_float),
        ("aleatoric_uncertainty", ctypes.c_float),
        ("total_uncertainty", ctypes.c_float),
        ("confidence_score", ctypes.c_float),
        ("uncertainty_map", ctypes.POINTER(ctypes.c_float)),
        ("map_size", ctypes.c_int),
    ]

class ConceptualDiffusionError(Exception):
    """Custom exception for conceptual diffusion errors."""
    pass

class ConceptualDiffusionPython:
    """
    Python interface for ULTRA Conceptual Diffusion system.
    
    This class provides a high-level Python API for the CUDA-accelerated
    conceptual diffusion processes, handling library loading, memory management,
    and type conversions automatically.
    
    Example:
        >>> config = {'concept_dim': 512, 'batch_size': 4}
        >>> with ConceptualDiffusionPython(config) as diffusion:
        ...     concepts = np.random.randn(4, 512).astype(np.float32)
        ...     noisy = diffusion.forward_diffusion(concepts, timestep=50)
        ...     print(f"Generated noisy concepts: {noisy.shape}")
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None, auto_compile: bool = True):
        """
        Initialize the Conceptual Diffusion system.
        
        Args:
            config: Configuration dictionary for the diffusion system
            auto_compile: Whether to automatically compile the C++ library if needed
            
        Raises:
            ConceptualDiffusionError: If initialization fails
        """
        self.lib = None
        self.state = None
        self.config = None
        self._is_initialized = False
        self._cleanup_registered = False
        self._library_path = None
        
        # Thread safety
        self._lock = threading.RLock()
        
        # Default configuration
        default_config = {
            'concept_dim': 512,
            'num_timesteps': 1000,
            'batch_size': 4,
            'hierarchical_levels': 4,
            'beta_start': 1e-4,
            'beta_end': 0.02,
            'guidance_scale': 3.0,
            'time_embedding_scale': 1.0,
            'noise_schedule_type': 1,  # cosine
            'precision_mode': 0,       # fp32
            'use_flash_attention': 1,
            'enable_caching': 1,
            'use_cuda': 1,
        }
        
        if config:
            default_config.update(config)
        
        self._config_dict = default_config
        
        try:
            # Load or compile the library
            self._load_library(auto_compile)
            
            # Initialize the system
            if self.lib:
                self._initialize_system(default_config)
            else:
                raise ConceptualDiffusionError("Failed to load conceptual diffusion library")
                
        except Exception as e:
            logger.error(f"Failed to initialize ConceptualDiffusionPython: {e}")
            self.cleanup()
            raise ConceptualDiffusionError(f"Initialization failed: {e}")
    
    def _find_library_path(self) -> Optional[str]:
        """Find the compiled library in common locations."""
        script_dir = Path(__file__).parent
        
        # Common library names
        lib_names = [
            "libconceptual_diffusion.so",
            "conceptual_diffusion.so", 
            "libconceptual_diffusion.dylib",  # macOS
            "conceptual_diffusion.dll",       # Windows
        ]
        
        # Search locations
        search_paths = [
            script_dir,
            script_dir / "cuda",
            script_dir / "..",
            script_dir / "../cuda",
            Path("."),
            Path("./cuda"),
        ]
        
        for search_path in search_paths:
            for lib_name in lib_names:
                lib_path = search_path / lib_name
                if lib_path.exists():
                    logger.info(f"Found library at: {lib_path}")
                    return str(lib_path.resolve())
        
        return None
    
    def _compile_library(self) -> bool:
        """Attempt to compile the C++ library."""
        script_dir = Path(__file__).parent
        
        # Look for source file
        cu_files = [
            script_dir / "conceptual_diffusion.cu",
            script_dir / "../cuda/conceptual_diffusion.cu",
            script_dir / "cuda/conceptual_diffusion.cu",
        ]
        
        cu_file = None
        for f in cu_files:
            if f.exists():
                cu_file = f
                break
        
        if not cu_file:
            logger.error("Could not find conceptual_diffusion.cu file")
            return False
        
        # Output library path
        output_lib = script_dir / "libconceptual_diffusion.so"
        
        try:
            logger.info("🔨 Compiling ULTRA Conceptual Diffusion library...")
            
            # Try CUDA compilation first
            cuda_cmd = [
                "nvcc", "-O3", "-std=c++11", "--expt-relaxed-constexpr",
                "-shared", "-fPIC", "-Xcompiler", "-fPIC",
                str(cu_file), "-o", str(output_lib),
                "-lcublas", "-lcusparse", "-lcufft", "-lcurand"
            ]
            
            logger.info(f"Trying CUDA compilation: {' '.join(cuda_cmd)}")
            result = subprocess.run(cuda_cmd, capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                logger.info("✅ Successfully compiled with CUDA support")
                self._library_path = str(output_lib)
                return True
            else:
                logger.warning(f"CUDA compilation failed: {result.stderr}")
                
                # Fallback to CPU compilation
                logger.info("🔄 Attempting CPU compilation...")
                cpu_cmd = [
                    "g++", "-O3", "-fopenmp", "-std=c++11",
                    "-shared", "-fPIC",
                    str(cu_file), "-o", str(output_lib), "-lm"
                ]
                
                logger.info(f"Trying CPU compilation: {' '.join(cpu_cmd)}")
                result = subprocess.run(cpu_cmd, capture_output=True, text=True, timeout=300)
                
                if result.returncode == 0:
                    logger.info("✅ Successfully compiled with CPU support")
                    self._library_path = str(output_lib)
                    return True
                else:
                    logger.error(f"CPU compilation failed: {result.stderr}")
                    logger.error(f"Compilation stdout: {result.stdout}")
                    return False
                    
        except subprocess.TimeoutExpired:
            logger.error("Compilation timeout after 5 minutes")
            return False
        except FileNotFoundError as e:
            logger.error(f"Compiler not found: {e}")
            logger.error("Please install build tools: sudo apt install build-essential")
            return False
        except Exception as e:
            logger.error(f"Compilation error: {e}")
            return False
    
    def _load_library(self, auto_compile: bool = True):
        """Load the compiled C++ library."""
        lib_path = self._find_library_path()
        
        if not lib_path and auto_compile:
            logger.info("📚 Library not found, attempting to compile...")
            if self._compile_library():
                lib_path = self._find_library_path()
        
        if not lib_path:
            raise ConceptualDiffusionError(
                "Could not find or compile the conceptual diffusion library. "
                "Please ensure you have the required build tools installed."
            )
        
        try:
            self.lib = ctypes.CDLL(lib_path)
            self._library_path = lib_path
            self._setup_function_signatures()
            logger.info(f"✅ Successfully loaded library from {lib_path}")
        except Exception as e:
            raise ConceptualDiffusionError(f"Failed to load library: {e}")
    
    def _setup_function_signatures(self):
        """Setup function signatures for the C library."""
        if not self.lib:
            return
        
        try:
            # check_cuda_availability
            self.lib.check_cuda_availability.restype = ctypes.c_int
            self.lib.check_cuda_availability.argtypes = []
            
            # init_conceptual_diffusion
            self.lib.init_conceptual_diffusion.restype = ctypes.c_void_p
            self.lib.init_conceptual_diffusion.argtypes = [ctypes.POINTER(ConceptualDiffusionConfig)]
            
            # forward_diffusion
            self.lib.forward_diffusion.restype = None
            self.lib.forward_diffusion.argtypes = [
                ctypes.c_void_p,  # state
                ctypes.POINTER(ConceptualDiffusionConfig),  # config
                ctypes.POINTER(ctypes.c_float),  # z_t
                ctypes.POINTER(ctypes.c_float),  # z_0
                ctypes.c_int  # timestep
            ]
            
            # reverse_diffusion
            self.lib.reverse_diffusion.restype = None
            self.lib.reverse_diffusion.argtypes = [
                ctypes.c_void_p,  # state
                ctypes.POINTER(ConceptualDiffusionConfig),  # config
                ctypes.POINTER(ctypes.c_float),  # z_t_minus_1
                ctypes.POINTER(ctypes.c_float),  # z_t
                ctypes.POINTER(ctypes.c_float),  # predicted_noise
                ctypes.c_int  # timestep
            ]
            
            # compute_uncertainty
            self.lib.compute_uncertainty.restype = ctypes.POINTER(UncertaintyQuantification)
            self.lib.compute_uncertainty.argtypes = [
                ctypes.c_void_p,  # state
                ctypes.POINTER(ConceptualDiffusionConfig),  # config
                ctypes.POINTER(ctypes.c_float),  # predictions
                ctypes.c_int  # num_ensembles
            ]
            
            # cleanup functions
            self.lib.cleanup_conceptual_diffusion.restype = None
            self.lib.cleanup_conceptual_diffusion.argtypes = [ctypes.c_void_p]
            
            self.lib.cleanup_uncertainty.restype = None
            self.lib.cleanup_uncertainty.argtypes = [ctypes.POINTER(UncertaintyQuantification)]
            
            logger.debug("✅ Function signatures setup complete")
            
        except AttributeError as e:
            raise ConceptualDiffusionError(f"Library missing required functions: {e}")
    
    def _initialize_system(self, config_dict: Dict[str, Any]):
        """Initialize the conceptual diffusion system."""
        if not self.lib:
            raise ConceptualDiffusionError("Library not loaded, cannot initialize system")
        
        with self._lock:
            # Create configuration struct
            self.config = ConceptualDiffusionConfig()
            for key, value in config_dict.items():
                if hasattr(self.config, key):
                    setattr(self.config, key, int(value) if isinstance(value, bool) else value)
            
            # Initialize the system
            try:
                self.state = self.lib.init_conceptual_diffusion(ctypes.byref(self.config))
                
                if self.state:
                    self._is_initialized = True
                    cuda_available = self.is_cuda_available()
                    device = "GPU (CUDA)" if cuda_available else "CPU"
                    logger.info(f"✅ ULTRA Conceptual Diffusion initialized on {device}")
                    
                    # Register cleanup
                    if not self._cleanup_registered:
                        atexit.register(self.cleanup)
                        self._cleanup_registered = True
                else:
                    raise ConceptualDiffusionError("C library returned null state")
                    
            except Exception as e:
                raise ConceptualDiffusionError(f"System initialization failed: {e}")
    
    def is_cuda_available(self) -> bool:
        """Check if CUDA is available and being used."""
        if not self.lib:
            return False
        try:
            return bool(self.lib.check_cuda_availability())
        except:
            return False
    
    def get_system_info(self) -> Dict[str, Any]:
        """Get comprehensive system information."""
        info = {
            'initialized': self._is_initialized,
            'cuda_available': self.is_cuda_available(),
            'library_path': self._library_path,
            'platform': platform.platform(),
            'python_version': platform.python_version(),
        }
        
        if self._is_initialized and self.config:
            info['config'] = {
                'concept_dim': self.config.concept_dim,
                'batch_size': self.config.batch_size,
                'num_timesteps': self.config.num_timesteps,
                'hierarchical_levels': self.config.hierarchical_levels,
                'use_cuda': bool(self.config.use_cuda),
            }
        
        return info
    
    def forward_diffusion(self, z_0: np.ndarray, timestep: int) -> np.ndarray:
        """
        Apply forward diffusion to concept embeddings.
        
        This implements the forward diffusion process:
        q(z_t | z_0) = N(z_t; sqrt(α_t) z_0, (1 - α_t) I)
        
        Args:
            z_0: Initial concept embeddings [batch_size, concept_dim]
            timestep: Diffusion timestep (0 to num_timesteps-1)
            
        Returns:
            Noisy concept embeddings z_t
            
        Raises:
            ConceptualDiffusionError: If operation fails
        """
        if not self._is_initialized:
            raise ConceptualDiffusionError("System not initialized")
        
        # Validate input
        if not isinstance(z_0, np.ndarray):
            raise ConceptualDiffusionError("z_0 must be a numpy array")
        
        if z_0.dtype != np.float32:
            logger.warning(f"Converting z_0 from {z_0.dtype} to float32")
            z_0 = z_0.astype(np.float32)
        
        expected_shape = (self.config.batch_size, self.config.concept_dim)
        if z_0.shape != expected_shape:
            raise ConceptualDiffusionError(f"Expected shape {expected_shape}, got {z_0.shape}")
        
        if not (0 <= timestep < self.config.num_timesteps):
            raise ConceptualDiffusionError(f"Timestep {timestep} out of range [0, {self.config.num_timesteps})")
        
        with self._lock:
            try:
                # Prepare input/output arrays
                z_0_flat = z_0.flatten().astype(np.float32)
                z_t_flat = np.zeros_like(z_0_flat)
                
                # Convert to ctypes
                z_0_ptr = z_0_flat.ctypes.data_as(ctypes.POINTER(ctypes.c_float))
                z_t_ptr = z_t_flat.ctypes.data_as(ctypes.POINTER(ctypes.c_float))
                
                # Call C function
                self.lib.forward_diffusion(self.state, ctypes.byref(self.config), z_t_ptr, z_0_ptr, timestep)
                
                return z_t_flat.reshape(z_0.shape)
                
            except Exception as e:
                raise ConceptualDiffusionError(f"Forward diffusion failed: {e}")
    
    def reverse_diffusion(self, z_t: np.ndarray, predicted_noise: np.ndarray, timestep: int) -> np.ndarray:
        """
        Apply reverse diffusion step.
        
        This implements the reverse diffusion process:
        p_θ(z_{t-1} | z_t) = N(z_{t-1}; μ_θ(z_t, t), Σ_θ(z_t, t))
        
        Args:
            z_t: Current noisy concept embeddings [batch_size, concept_dim]
            predicted_noise: Predicted noise from denoising network [batch_size, concept_dim]
            timestep: Current timestep (1 to num_timesteps-1)
            
        Returns:
            Denoised concept embeddings z_{t-1}
            
        Raises:
            ConceptualDiffusionError: If operation fails
        """
        if not self._is_initialized:
            raise ConceptualDiffusionError("System not initialized")
        
        # Validate inputs
        expected_shape = (self.config.batch_size, self.config.concept_dim)
        if z_t.shape != expected_shape or predicted_noise.shape != expected_shape:
            raise ConceptualDiffusionError(f"Expected shape {expected_shape}")
        
        if not (1 <= timestep < self.config.num_timesteps):
            raise ConceptualDiffusionError(f"Timestep {timestep} out of range [1, {self.config.num_timesteps})")
        
        # Ensure float32
        z_t = z_t.astype(np.float32)
        predicted_noise = predicted_noise.astype(np.float32)
        
        with self._lock:
            try:
                # Prepare arrays
                z_t_flat = z_t.flatten()
                noise_flat = predicted_noise.flatten()
                z_t_minus_1_flat = np.zeros_like(z_t_flat)
                
                # Convert to ctypes
                z_t_ptr = z_t_flat.ctypes.data_as(ctypes.POINTER(ctypes.c_float))
                noise_ptr = noise_flat.ctypes.data_as(ctypes.POINTER(ctypes.c_float))
                z_t_minus_1_ptr = z_t_minus_1_flat.ctypes.data_as(ctypes.POINTER(ctypes.c_float))
                
                # Call C function
                self.lib.reverse_diffusion(self.state, ctypes.byref(self.config), z_t_minus_1_ptr, z_t_ptr, noise_ptr, timestep)
                
                return z_t_minus_1_flat.reshape(z_t.shape)
                
            except Exception as e:
                raise ConceptualDiffusionError(f"Reverse diffusion failed: {e}")
    
    def compute_uncertainty(self, ensemble_predictions: np.ndarray) -> Dict[str, Any]:
        """
        Compute uncertainty quantification from ensemble predictions.
        
        This computes both epistemic and aleatoric uncertainty:
        - Epistemic: Model uncertainty (variance across ensemble)
        - Aleatoric: Data uncertainty (inherent randomness)
        
        Args:
            ensemble_predictions: Predictions from multiple models [num_ensembles, batch_size, concept_dim]
            
        Returns:
            Dictionary with uncertainty measures:
            - epistemic_uncertainty: Model uncertainty
            - aleatoric_uncertainty: Data uncertainty  
            - total_uncertainty: Combined uncertainty
            - confidence_score: Confidence measure (0-1)
            - uncertainty_map: Per-element uncertainty [batch_size, concept_dim]
            
        Raises:
            ConceptualDiffusionError: If operation fails
        """
        if not self._is_initialized:
            raise ConceptualDiffusionError("System not initialized")
        
        # Validate input
        if len(ensemble_predictions.shape) != 3:
            raise ConceptualDiffusionError("ensemble_predictions must be 3D: [num_ensembles, batch_size, concept_dim]")
        
        num_ensembles, batch_size, concept_dim = ensemble_predictions.shape
        if batch_size != self.config.batch_size or concept_dim != self.config.concept_dim:
            raise ConceptualDiffusionError("Shape mismatch with configuration")
        
        ensemble_predictions = ensemble_predictions.astype(np.float32)
        
        with self._lock:
            try:
                # Prepare data
                predictions_flat = ensemble_predictions.flatten()
                predictions_ptr = predictions_flat.ctypes.data_as(ctypes.POINTER(ctypes.c_float))
                
                # Call C function
                uq_ptr = self.lib.compute_uncertainty(self.state, ctypes.byref(self.config), predictions_ptr, num_ensembles)
                
                if not uq_ptr:
                    raise ConceptualDiffusionError("Failed to compute uncertainty")
                
                # Extract results
                uq = uq_ptr.contents
                uncertainty_map = np.ctypeslib.as_array(uq.uncertainty_map, shape=(uq.map_size,))
                
                result = {
                    'epistemic_uncertainty': float(uq.epistemic_uncertainty),
                    'aleatoric_uncertainty': float(uq.aleatoric_uncertainty),
                    'total_uncertainty': float(uq.total_uncertainty),
                    'confidence_score': float(uq.confidence_score),
                    'uncertainty_map': uncertainty_map.copy().reshape(batch_size, concept_dim),
                    'num_ensembles': num_ensembles
                }
                
                # Cleanup
                self.lib.cleanup_uncertainty(uq_ptr)
                
                return result
                
            except Exception as e:
                raise ConceptualDiffusionError(f"Uncertainty computation failed: {e}")
    
    def generate_concepts(self, initial_concepts: np.ndarray, num_steps: int = 50, 
                         guidance_scale: Optional[float] = None, 
                         return_trajectory: bool = False) -> Union[np.ndarray, List[np.ndarray]]:
        """
        Generate concepts using the full diffusion process.
        
        This performs the complete generation pipeline:
        1. Forward diffusion to add noise
        2. Reverse diffusion to generate new concepts
        
        Args:
            initial_concepts: Starting concept embeddings [batch_size, concept_dim]
            num_steps: Number of diffusion steps to perform
            guidance_scale: Override default guidance scale
            return_trajectory: If True, return full trajectory; if False, return only final result
            
        Returns:
            If return_trajectory=False: Final generated concepts [batch_size, concept_dim]
            If return_trajectory=True: List of concept states at each step
            
        Raises:
            ConceptualDiffusionError: If generation fails
        """
        if guidance_scale is not None:
            original_scale = self.config.guidance_scale
            self.config.guidance_scale = guidance_scale
        
        concepts_trajectory = []
        current_concepts = initial_concepts.copy()
        
        try:
            # Forward diffusion to add noise
            start_timestep = min(num_steps, self.config.num_timesteps - 1)
            current_concepts = self.forward_diffusion(current_concepts, start_timestep)
            
            if return_trajectory:
                concepts_trajectory.append(current_concepts.copy())
            
            # Reverse diffusion to denoise
            for t in range(start_timestep, 0, -1):
                # In a real implementation, you'd have a trained denoising network
                # For demonstration, we'll use a simple noise prediction
                predicted_noise = np.random.normal(0, 0.1, current_concepts.shape).astype(np.float32)
                
                current_concepts = self.reverse_diffusion(current_concepts, predicted_noise, t)
                
                if return_trajectory:
                    concepts_trajectory.append(current_concepts.copy())
            
            if return_trajectory:
                return concepts_trajectory
            else:
                return current_concepts
                
        finally:
            if guidance_scale is not None:
                self.config.guidance_scale = original_scale
    
    def __enter__(self):
        """Context manager entry."""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit with cleanup."""
        self.cleanup()
    
    def cleanup(self):
        """Clean up resources."""
        with self._lock:
            if self.state and self.lib and self._is_initialized:
                try:
                    self.lib.cleanup_conceptual_diffusion(self.state)
                    logger.info("🧹 Cleaned up conceptual diffusion resources")
                except:
                    logger.warning("Failed to cleanup C resources properly")
                
                self.state = None
                self._is_initialized = False
    
    def __del__(self):
        """Destructor."""
        self.cleanup()

# ============================================================================
# Convenience Functions
# ============================================================================

def create_conceptual_diffusion(config: Optional[Dict[str, Any]] = None, 
                               auto_compile: bool = True) -> ConceptualDiffusionPython:
    """
    Factory function to create a ConceptualDiffusion instance.
    
    Args:
        config: Configuration dictionary
        auto_compile: Whether to auto-compile if needed
        
    Returns:
        ConceptualDiffusionPython instance
        
    Example:
        >>> diffusion = create_conceptual_diffusion({'concept_dim': 256})
        >>> concepts = np.random.randn(4, 256).astype(np.float32)
        >>> noisy = diffusion.forward_diffusion(concepts, 50)
    """
    return ConceptualDiffusionPython(config, auto_compile)

def check_system_capabilities() -> Dict[str, Any]:
    """
    Check system capabilities for ULTRA Conceptual Diffusion.
    
    Returns:
        Dictionary with system information and capabilities
    """
    capabilities = {
        'platform': platform.platform(),
        'python_version': platform.python_version(),
        'numpy_available': False,
        'compilers_available': {},
        'cuda_available': False,
    }
    
    # Check NumPy
    try:
        import numpy
        capabilities['numpy_available'] = True
        capabilities['numpy_version'] = numpy.__version__
    except ImportError:
        pass
    
    # Check compilers
    compilers = ['gcc', 'g++', 'nvcc', 'clang', 'clang++']
    for compiler in compilers:
        try:
            result = subprocess.run([compiler, '--version'], capture_output=True, text=True, timeout=5)
            capabilities['compilers_available'][compiler] = result.returncode == 0
        except:
            capabilities['compilers_available'][compiler] = False
    
    # Check CUDA
    try:
        result = subprocess.run(['nvidia-smi'], capture_output=True, text=True, timeout=5)
        capabilities['cuda_available'] = result.returncode == 0
        if capabilities['cuda_available']:
            capabilities['cuda_info'] = "nvidia-smi available"
    except:
        pass
    
    return capabilities

def quick_test(verbose: bool = False) -> bool:
    """
    Quick test of the conceptual diffusion system.
    
    Args:
        verbose: Whether to print detailed output
        
    Returns:
        True if test passes, False otherwise
    """
    if verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    print("🧪 ULTRA Conceptual Diffusion Python Wrapper Test")
    print("=" * 50)
    
    try:
        # Check system capabilities
        capabilities = check_system_capabilities()
        if verbose:
            print("💻 System Capabilities:")
            for key, value in capabilities.items():
                print(f"   {key}: {value}")
            print()
        
        # Create system with small configuration for testing
        config = {
            'concept_dim': 128,
            'batch_size': 2,
            'num_timesteps': 100,
            'hierarchical_levels': 2,
        }
        
        print("🔧 Initializing system...")
        with create_conceptual_diffusion(config) as diffusion:
            if not diffusion._is_initialized:
                print("❌ Failed to initialize system")
                return False
            
            sys_info = diffusion.get_system_info()
            device = "GPU (CUDA)" if sys_info['cuda_available'] else "CPU"
            print(f"✅ System initialized on {device}")
            
            if verbose:
                print("📊 System Info:")
                for key, value in sys_info.items():
                    print(f"   {key}: {value}")
                print()
            
            # Test forward diffusion
            print("🧪 Testing forward diffusion...")
            initial_concepts = np.random.randn(config['batch_size'], config['concept_dim']).astype(np.float32)
            
            start_time = time.time()
            noisy_concepts = diffusion.forward_diffusion(initial_concepts, 50)
            forward_time = time.time() - start_time
            
            print(f"   📊 Input shape: {initial_concepts.shape}")
            print(f"   📊 Output shape: {noisy_concepts.shape}")
            print(f"   📊 Noise level: {np.std(noisy_concepts - initial_concepts):.4f}")
            print(f"   📊 Time: {forward_time*1000:.2f}ms")
            
            # Test reverse diffusion
            print("🧪 Testing reverse diffusion...")
            predicted_noise = np.random.normal(0, 0.1, noisy_concepts.shape).astype(np.float32)
            
            start_time = time.time()
            denoised_concepts = diffusion.reverse_diffusion(noisy_concepts, predicted_noise, 50)
            reverse_time = time.time() - start_time
            
            print(f"   📊 Denoised shape: {denoised_concepts.shape}")
            print(f"   📊 Time: {reverse_time*1000:.2f}ms")
            
            # Test uncertainty quantification
            print("🧪 Testing uncertainty quantification...")
            num_ensembles = 3
            ensemble_predictions = np.random.randn(num_ensembles, config['batch_size'], config['concept_dim']).astype(np.float32)
            
            start_time = time.time()
            uncertainty = diffusion.compute_uncertainty(ensemble_predictions)
            uncertainty_time = time.time() - start_time
            
            print(f"   📊 Total uncertainty: {uncertainty['total_uncertainty']:.4f}")
            print(f"   📊 Confidence score: {uncertainty['confidence_score']:.4f}")
            print(f"   📊 Time: {uncertainty_time*1000:.2f}ms")
            
            # Test concept generation
            print("🧪 Testing concept generation...")
            start_time = time.time()
            final_concepts = diffusion.generate_concepts(initial_concepts, num_steps=10)
            generation_time = time.time() - start_time
            
            print(f"   📊 Generated shape: {final_concepts.shape}")
            print(f"   📊 Time: {generation_time:.2f}s")
            
            print("✅ All tests passed!")
            
            # Performance summary
            print("\n📊 Performance Summary:")
            print(f"   Forward diffusion: {forward_time*1000:.2f}ms")
            print(f"   Reverse diffusion: {reverse_time*1000:.2f}ms")
            print(f"   Uncertainty: {uncertainty_time*1000:.2f}ms")
            print(f"   Generation (10 steps): {generation_time:.2f}s")
            
            return True
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        if verbose:
            import traceback
            traceback.print_exc()
        return False

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="ULTRA Conceptual Diffusion Python Wrapper")
    parser.add_argument('--test', action='store_true', help='Run quick test')
    parser.add_argument('--verbose', action='store_true', help='Verbose output')
    parser.add_argument('--check-system', action='store_true', help='Check system capabilities')
    parser.add_argument('--config', type=str, help='Configuration mode (development, production)')
    
    args = parser.parse_args()
    
    if args.check_system:
        print("🔍 System Capabilities Check")
        print("=" * 30)
        capabilities = check_system_capabilities()
        for key, value in capabilities.items():
            print(f"{key}: {value}")
        sys.exit(0)
    
    if args.test or len(sys.argv) == 1:
        success = quick_test(args.verbose)
        sys.exit(0 if success else 1)
    
    print("🚀 ULTRA Conceptual Diffusion Python Wrapper")
    print("Use --test to run tests or --check-system to check capabilities")