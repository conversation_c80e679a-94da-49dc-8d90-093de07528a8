#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ULTRA: Ultimate Learning & Thought Reasoning Architecture
Conceptual Diffusion Process - Comprehensive Test Suite

This module implements exhaustive testing for the conceptual diffusion component
of the diffusion reasoning system. Tests all mathematical formulations, algorithms,
and production-grade implementations for diffusion processes in conceptual spaces.

Mathematical Foundation Testing:
- Forward Process: q(z_t|z_{t-1}) = N(z_t; √(1-β_t)z_{t-1}, β_tI)
- Closed Form: q(z_t|z_0) = N(z_t; √(α̅_t)z_0, (1-α̅_t)I)
- Reverse Process: p_θ(z_{t-1}|z_t) = N(z_{t-1}; μ_θ(z_t,t), Σ_θ(z_t,t))
- ELBO: L = E_q[log p_θ(x_0|z_0)] - D_KL(q(z_T|x) || p(z_T))
- Score Function: ∇_z log p(z_t) ≈ -ε_θ(z_t,t)/√(1-α̅_t)

Author: ULTRA Development Team
Version: 1.0.0
License: MIT
"""

import unittest
import pytest
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.distributions import MultivariateNormal, Normal
from torch.optim import Adam, AdamW
from torch.optim.lr_scheduler import CosineAnnealingLR
import scipy.stats as stats
import scipy.linalg as linalg
from sklearn.manifold import TSNE
from sklearn.decomposition import PCA
from sklearn.metrics import pairwise_distances
import matplotlib.pyplot as plt
import seaborn as sns
import time
import warnings
import logging
import json
import pickle
from typing import Dict, List, Tuple, Union, Optional, Any, Callable
from dataclasses import dataclass, field
from pathlib import Path
from abc import ABC, abstractmethod
from concurrent.futures import ThreadPoolExecutor
import multiprocessing as mp
from functools import partial
import math

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Constants
DEVICE = 'cuda' if torch.cuda.is_available() else 'cpu'
NUMERICAL_TOLERANCE = 1e-6
CONVERGENCE_TOLERANCE = 1e-4
MAX_TRAINING_STEPS = 1000
DEFAULT_TIMESTEPS = 1000
BETA_START = 0.0001 # 1e-4
BETA_END = 0.05      # 0.02

# =============================================================================
# Mathematical Utilities and Validation
# =============================================================================

class DiffusionMathUtils:
    """Mathematical utilities for diffusion process validation."""
    
    @staticmethod
    def create_beta_schedule(num_timesteps: int, schedule_type: str = 'linear') -> torch.Tensor:
        """Create noise schedule for diffusion process."""
        if schedule_type == 'linear':
            return torch.linspace(BETA_START, BETA_END, num_timesteps)
        elif schedule_type == 'cosine':
            # Cosine schedule from Nichol & Dhariwal
            s = 0.008
            steps = num_timesteps + 1
            x = torch.linspace(0, num_timesteps, steps)
            alphas_cumprod = torch.cos(((x / num_timesteps) + s) / (1 + s) * torch.pi * 0.5) ** 2
            alphas_cumprod = alphas_cumprod / alphas_cumprod[0]
            betas = 1 - (alphas_cumprod[1:] / alphas_cumprod[:-1])
            return torch.clip(betas, 0.0001, 0.9999)
        elif schedule_type == 'sigmoid':
            # Sigmoid schedule
            x = torch.linspace(-6, 6, num_timesteps)
            return torch.sigmoid(x) * (BETA_END - BETA_START) + BETA_START
        else:
            raise ValueError(f"Unknown schedule type: {schedule_type}")
    
    @staticmethod
    def validate_diffusion_coefficients(betas: torch.Tensor) -> Dict[str, bool]:
        """Validate diffusion schedule coefficients."""
        alphas = 1.0 - betas
        alphas_cumprod = torch.cumprod(alphas, dim=0)
        
        return {
            'betas_positive': torch.all(betas > 0).item(),
            'betas_less_than_one': torch.all(betas < 1).item(),
            'alphas_positive': torch.all(alphas > 0).item(),
            'alphas_cumprod_decreasing': torch.all(alphas_cumprod[1:] <= alphas_cumprod[:-1]).item(),
            'alphas_cumprod_bounded': torch.all((alphas_cumprod >= 0) & (alphas_cumprod <= 1)).item()
        }
    
    @staticmethod
    def compute_diffusion_metrics(z_0: torch.Tensor, z_t: torch.Tensor, 
                                 alphas_cumprod_t: torch.Tensor) -> Dict[str, float]:
        """Compute metrics for diffusion process validation."""
        signal_coeff = torch.sqrt(alphas_cumprod_t)
        noise_coeff = torch.sqrt(1 - alphas_cumprod_t)
        
        # Theoretical variance of z_t
        theoretical_var = (signal_coeff ** 2) * torch.var(z_0, dim=0).mean() + (noise_coeff ** 2)
        empirical_var = torch.var(z_t, dim=0).mean()
        
        return {
            'signal_coefficient': signal_coeff.mean().item(),
            'noise_coefficient': noise_coeff.mean().item(),
            'theoretical_variance': theoretical_var.item(),
            'empirical_variance': empirical_var.item(),
            'variance_ratio': (empirical_var / theoretical_var).item(),
            'signal_to_noise_ratio': (signal_coeff / noise_coeff).mean().item()
        }

@dataclass
class ConceptualSpace:
    """Representation of conceptual space for testing."""
    concepts: torch.Tensor
    relationships: torch.Tensor
    semantic_distances: torch.Tensor
    hierarchical_levels: List[int]
    metadata: Dict[str, Any] = field(default_factory=dict)

class ConceptGenerator:
    """Generator for structured conceptual data."""
    
    def __init__(self, concept_dim: int, device: str = DEVICE):
        self.concept_dim = concept_dim
        self.device = device
    
    def generate_hierarchical_concepts(self, num_concepts: int, 
                                     num_levels: int = 3) -> ConceptualSpace:
        """Generate hierarchical concept structure."""
        concepts = []
        relationships = []
        levels = []
        
        # Generate base concepts at each level
        for level in range(num_levels):
            level_concepts = num_concepts // num_levels
            if level == num_levels - 1:  # Last level gets remainder
                level_concepts = num_concepts - len(concepts)
            
            # Generate concepts with increasing abstraction
            abstraction_factor = (level + 1) / num_levels
            noise_level = 1.0 - abstraction_factor * 0.5
            
            level_concepts_tensor = torch.randn(level_concepts, self.concept_dim, device=self.device) * noise_level
            
            # Add hierarchical structure
            if level > 0:
                # Higher level concepts are combinations of lower level ones
                lower_concepts = torch.cat(concepts, dim=0)
                num_lower = lower_concepts.shape[0]
                
                for i in range(level_concepts):
                    # Sample 2-3 lower level concepts to combine
                    num_combine = torch.randint(2, 4, (1,)).item()
                    indices = torch.randperm(num_lower)[:num_combine]
                    combination = lower_concepts[indices].mean(dim=0)
                    
                    # Add some noise and the generated concept
                    level_concepts_tensor[i] = 0.7 * combination + 0.3 * level_concepts_tensor[i]
            
            concepts.append(level_concepts_tensor)
            levels.extend([level] * level_concepts)
        
        all_concepts = torch.cat(concepts, dim=0)
        
        # Compute semantic distances
        distances = torch.cdist(all_concepts, all_concepts)
        
        # Generate relationships (similarity-based)
        similarity_threshold = distances.median()
        relationships_matrix = (distances < similarity_threshold).float()
        
        return ConceptualSpace(
            concepts=all_concepts,
            relationships=relationships_matrix,
            semantic_distances=distances,
            hierarchical_levels=levels
        )
    
    def generate_semantic_clusters(self, num_clusters: int, concepts_per_cluster: int) -> ConceptualSpace:
        """Generate concepts organized in semantic clusters."""
        # Generate cluster centers
        cluster_centers = torch.randn(num_clusters, self.concept_dim, device=self.device)
        cluster_centers = F.normalize(cluster_centers, dim=-1) * 2.0  # Scale for separation
        
        concepts = []
        cluster_labels = []
        
        for cluster_id in range(num_clusters):
            center = cluster_centers[cluster_id]
            
            # Generate concepts around cluster center
            cluster_concepts = torch.randn(concepts_per_cluster, self.concept_dim, device=self.device) * 0.5
            cluster_concepts = cluster_concepts + center.unsqueeze(0)
            
            concepts.append(cluster_concepts)
            cluster_labels.extend([cluster_id] * concepts_per_cluster)
        
        all_concepts = torch.cat(concepts, dim=0)
        distances = torch.cdist(all_concepts, all_concepts)
        
        # Create relationship matrix based on cluster membership
        relationships = torch.zeros_like(distances)
        for i, label_i in enumerate(cluster_labels):
            for j, label_j in enumerate(cluster_labels):
                if label_i == label_j:
                    relationships[i, j] = 1.0
        
        return ConceptualSpace(
            concepts=all_concepts,
            relationships=relationships,
            semantic_distances=distances,
            hierarchical_levels=cluster_labels,
            metadata={'cluster_centers': cluster_centers, 'num_clusters': num_clusters}
        )

# =============================================================================
# Core Conceptual Diffusion Implementation
# =============================================================================

class ConceptualDiffusionModel(nn.Module):
    """
    Core implementation of conceptual diffusion for ULTRA.
    
    This model adapts standard diffusion processes to operate in conceptual
    spaces, enabling reasoning through controlled noise addition and removal.
    """
    
    def __init__(self, concept_dim: int, hidden_dim: int = 512, 
                 num_timesteps: int = DEFAULT_TIMESTEPS,
                 schedule_type: str = 'linear', device: str = DEVICE):
        super().__init__()
        self.concept_dim = concept_dim
        self.hidden_dim = hidden_dim
        self.num_timesteps = num_timesteps
        self.device = device
        
        # Create noise schedule
        self.register_buffer('betas', DiffusionMathUtils.create_beta_schedule(num_timesteps, schedule_type))
        self.register_buffer('alphas', 1.0 - self.betas)
        self.register_buffer('alphas_cumprod', torch.cumprod(self.alphas, dim=0))
        self.register_buffer('alphas_cumprod_prev', F.pad(self.alphas_cumprod[:-1], (1, 0), value=1.0))
        
        # Precompute useful coefficients
        self.register_buffer('sqrt_alphas_cumprod', torch.sqrt(self.alphas_cumprod))
        self.register_buffer('sqrt_one_minus_alphas_cumprod', torch.sqrt(1.0 - self.alphas_cumprod))
        self.register_buffer('log_one_minus_alphas_cumprod', torch.log(1.0 - self.alphas_cumprod))
        self.register_buffer('sqrt_recip_alphas_cumprod', torch.sqrt(1.0 / self.alphas_cumprod))
        self.register_buffer('sqrt_recipm1_alphas_cumprod', torch.sqrt(1.0 / self.alphas_cumprod - 1))
        
        # Time embedding
        self.time_embed_dim = hidden_dim // 4
        self.time_embedding = nn.Sequential(
            SinusoidalPositionEmbedding(self.time_embed_dim),
            nn.Linear(self.time_embed_dim, hidden_dim),
            nn.GELU(),
            nn.Linear(hidden_dim, hidden_dim)
        )
        
        # Noise prediction network (U-Net style)
        self.noise_predictor = NoisePredictor(
            concept_dim=concept_dim,
            hidden_dim=hidden_dim,
            time_embed_dim=hidden_dim
        )
        
        # Score network (alternative parameterization)
        self.score_network = ScoreNetwork(
            concept_dim=concept_dim,
            hidden_dim=hidden_dim,
            time_embed_dim=hidden_dim
        )
        
        self.to(device)
    
    def forward_diffusion(self, x_0: torch.Tensor, t: torch.Tensor, 
                         noise: torch.Tensor = None) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Forward diffusion process: q(z_t|z_0).
        
        Args:
            x_0: Initial concepts [batch_size, concept_dim]
            t: Timesteps [batch_size]
            noise: Optional noise tensor
            
        Returns:
            x_t: Noisy concepts at time t
            noise: Applied noise
        """
        if noise is None:
            noise = torch.randn_like(x_0)
        
        sqrt_alphas_cumprod_t = self.sqrt_alphas_cumprod[t].unsqueeze(-1)
        sqrt_one_minus_alphas_cumprod_t = self.sqrt_one_minus_alphas_cumprod[t].unsqueeze(-1)
        
        x_t = sqrt_alphas_cumprod_t * x_0 + sqrt_one_minus_alphas_cumprod_t * noise
        
        return x_t, noise
    
    def reverse_diffusion_step(self, x_t: torch.Tensor, t: torch.Tensor,
                              use_score: bool = False) -> torch.Tensor:
        """
        Single reverse diffusion step: p_θ(z_{t-1}|z_t).
        
        Args:
            x_t: Noisy concepts at time t
            t: Current timestep
            use_score: Whether to use score network instead of noise predictor
            
        Returns:
            x_{t-1}: Denoised concepts
        """
        batch_size = x_t.shape[0]
        
        # Embed time
        t_emb = self.time_embedding(t)
        
        if use_score:
            # Score-based approach
            score = self.score_network(x_t, t_emb)
            
            # Convert score to noise prediction
            sqrt_one_minus_alphas_cumprod_t = self.sqrt_one_minus_alphas_cumprod[t].unsqueeze(-1)
            predicted_noise = -sqrt_one_minus_alphas_cumprod_t * score
        else:
            # Direct noise prediction
            predicted_noise = self.noise_predictor(x_t, t_emb)
        
        # Compute reverse step parameters
        alpha_t = self.alphas[t].unsqueeze(-1)
        alpha_cumprod_t = self.alphas_cumprod[t].unsqueeze(-1)
        alpha_cumprod_prev_t = self.alphas_cumprod_prev[t].unsqueeze(-1)
        beta_t = self.betas[t].unsqueeze(-1)
        
        # Mean of reverse distribution
        coeff1 = torch.sqrt(alpha_cumprod_prev_t) * beta_t / (1 - alpha_cumprod_t)
        coeff2 = torch.sqrt(alpha_t) * (1 - alpha_cumprod_prev_t) / (1 - alpha_cumprod_t)
        
        mean = coeff1 * predicted_noise + coeff2 * x_t
        
        # Add noise for sampling (except at t=0)
        if t.min() > 0:
            noise = torch.randn_like(x_t)
            
            # Compute posterior variance
            posterior_variance = beta_t * (1 - alpha_cumprod_prev_t) / (1 - alpha_cumprod_t)
            posterior_log_variance = torch.log(torch.clamp(posterior_variance, min=1e-20))
            
            x_prev = mean + torch.exp(0.5 * posterior_log_variance) * noise
        else:
            x_prev = mean
        
        return x_prev
    
    def sample(self, batch_size: int, return_trajectory: bool = False) -> Union[torch.Tensor, List[torch.Tensor]]:
        """
        Sample from the learned distribution using reverse diffusion.
        
        Args:
            batch_size: Number of samples to generate
            return_trajectory: Whether to return full sampling trajectory
            
        Returns:
            Generated concepts or full trajectory
        """
        # Start from pure noise
        x_t = torch.randn(batch_size, self.concept_dim, device=self.device)
        
        trajectory = [x_t.clone()] if return_trajectory else None
        
        # Reverse diffusion
        for t in reversed(range(self.num_timesteps)):
            t_batch = torch.full((batch_size,), t, device=self.device, dtype=torch.long)
            x_t = self.reverse_diffusion_step(x_t, t_batch)
            
            if return_trajectory:
                trajectory.append(x_t.clone())
        
        if return_trajectory:
            return trajectory
        else:
            return x_t
    
    def compute_loss(self, x_0: torch.Tensor, reduction: str = 'mean') -> torch.Tensor:
        """
        Compute diffusion training loss.
        
        Args:
            x_0: Clean concepts
            reduction: Loss reduction method
            
        Returns:
            Diffusion loss
        """
        batch_size = x_0.shape[0]
        
        # Sample random timesteps
        t = torch.randint(0, self.num_timesteps, (batch_size,), device=self.device)
        
        # Sample noise
        noise = torch.randn_like(x_0)
        
        # Forward diffusion
        x_t, _ = self.forward_diffusion(x_0, t, noise)
        
        # Predict noise
        t_emb = self.time_embedding(t)
        predicted_noise = self.noise_predictor(x_t, t_emb)
        
        # Compute loss
        loss = F.mse_loss(predicted_noise, noise, reduction=reduction)
        
        return loss
    
    def compute_vlb_loss(self, x_0: torch.Tensor) -> torch.Tensor:
        """
        Compute variational lower bound (VLB) loss.
        
        Args:
            x_0: Clean concepts
            
        Returns:
            VLB loss
        """
        batch_size = x_0.shape[0]
        
        # Sample timesteps
        t = torch.randint(1, self.num_timesteps, (batch_size,), device=self.device)
        
        # Forward diffusion
        noise = torch.randn_like(x_0)
        x_t, _ = self.forward_diffusion(x_0, t, noise)
        
        # Predict noise
        t_emb = self.time_embedding(t)
        predicted_noise = self.noise_predictor(x_t, t_emb)
        
        # Compute mean and variance of reverse process
        alpha_t = self.alphas[t].unsqueeze(-1)
        alpha_cumprod_t = self.alphas_cumprod[t].unsqueeze(-1)
        alpha_cumprod_prev_t = self.alphas_cumprod_prev[t].unsqueeze(-1)
        beta_t = self.betas[t].unsqueeze(-1)
        
        # Predicted x_0
        predicted_x_0 = (x_t - torch.sqrt(1 - alpha_cumprod_t) * predicted_noise) / torch.sqrt(alpha_cumprod_t)
        
        # True posterior mean
        posterior_mean = (
            torch.sqrt(alpha_cumprod_prev_t) * beta_t / (1 - alpha_cumprod_t) * predicted_x_0 +
            torch.sqrt(alpha_t) * (1 - alpha_cumprod_prev_t) / (1 - alpha_cumprod_t) * x_t
        )
        
        # Predicted posterior mean
        predicted_mean = (
            torch.sqrt(alpha_cumprod_prev_t) * beta_t / (1 - alpha_cumprod_t) * predicted_x_0 +
            torch.sqrt(alpha_t) * (1 - alpha_cumprod_prev_t) / (1 - alpha_cumprod_t) * x_t
        )
        
        # Posterior variance
        posterior_variance = beta_t * (1 - alpha_cumprod_prev_t) / (1 - alpha_cumprod_t)
        posterior_log_variance = torch.log(torch.clamp(posterior_variance, min=1e-20))
        
        # KL divergence
        kl = 0.5 * (-1.0 - posterior_log_variance + posterior_variance + 
                   (posterior_mean - predicted_mean) ** 2 / posterior_variance)
        
        return kl.sum(dim=-1).mean()

class SinusoidalPositionEmbedding(nn.Module):
    """Sinusoidal position embedding for time encoding."""
    
    def __init__(self, embedding_dim: int):
        super().__init__()
        self.embedding_dim = embedding_dim
    
    def forward(self, timesteps: torch.Tensor) -> torch.Tensor:
        half_dim = self.embedding_dim // 2
        emb = math.log(10000) / (half_dim - 1)
        emb = torch.exp(torch.arange(half_dim, device=timesteps.device) * -emb)
        emb = timesteps.unsqueeze(-1) * emb.unsqueeze(0)
        emb = torch.cat([torch.sin(emb), torch.cos(emb)], dim=-1)
        return emb

class NoisePredictor(nn.Module):
    """Neural network for predicting noise in diffusion process."""
    
    def __init__(self, concept_dim: int, hidden_dim: int, time_embed_dim: int):
        super().__init__()
        self.concept_dim = concept_dim
        self.hidden_dim = hidden_dim
        
        # Input projection
        self.input_proj = nn.Linear(concept_dim, hidden_dim)
        
        # Time projection
        self.time_proj = nn.Linear(time_embed_dim, hidden_dim)
        
        # Main network
        self.network = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim * 2),
            nn.GELU(),
            nn.GroupNorm(8, hidden_dim * 2),
            nn.Linear(hidden_dim * 2, hidden_dim * 2),
            nn.GELU(),
            nn.GroupNorm(8, hidden_dim * 2),
            nn.Linear(hidden_dim * 2, hidden_dim),
            nn.GELU(),
            nn.Linear(hidden_dim, concept_dim)
        )
        
        # Residual connection
        self.residual_proj = nn.Linear(concept_dim, concept_dim)
    
    def forward(self, x: torch.Tensor, t_emb: torch.Tensor) -> torch.Tensor:
        # Project inputs
        x_proj = self.input_proj(x)
        t_proj = self.time_proj(t_emb)
        
        # Combine
        h = x_proj + t_proj
        
        # Main network
        noise_pred = self.network(h)
        
        # Residual connection
        noise_pred = noise_pred + self.residual_proj(x)
        
        return noise_pred

class ScoreNetwork(nn.Module):
    """Neural network for predicting score function ∇_x log p(x)."""
    
    def __init__(self, concept_dim: int, hidden_dim: int, time_embed_dim: int):
        super().__init__()
        self.concept_dim = concept_dim
        self.hidden_dim = hidden_dim
        
        # Input projection
        self.input_proj = nn.Linear(concept_dim, hidden_dim)
        
        # Time projection
        self.time_proj = nn.Linear(time_embed_dim, hidden_dim)
        
        # Main network with skip connections
        self.layer1 = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim),
            nn.GELU(),
            nn.GroupNorm(8, hidden_dim)
        )
        
        self.layer2 = nn.Sequential(
            nn.Linear(hidden_dim * 2, hidden_dim),
            nn.GELU(),
            nn.GroupNorm(8, hidden_dim)
        )
        
        self.layer3 = nn.Sequential(
            nn.Linear(hidden_dim * 2, hidden_dim),
            nn.GELU(),
            nn.GroupNorm(8, hidden_dim)
        )
        
        # Output projection
        self.output_proj = nn.Linear(hidden_dim, concept_dim)
        
        # Zero initialization for output layer
        nn.init.zeros_(self.output_proj.weight)
        nn.init.zeros_(self.output_proj.bias)
    
    def forward(self, x: torch.Tensor, t_emb: torch.Tensor) -> torch.Tensor:
        # Project inputs
        x_proj = self.input_proj(x)
        t_proj = self.time_proj(t_emb)
        
        # Initial hidden state
        h = x_proj + t_proj
        h0 = h
        
        # Layer 1
        h1 = self.layer1(h)
        
        # Layer 2 with skip connection
        h = torch.cat([h1, h0], dim=-1)
        h2 = self.layer2(h)
        
        # Layer 3 with skip connection
        h = torch.cat([h2, h1], dim=-1)
        h3 = self.layer3(h)
        
        # Output
        score = self.output_proj(h3)
        
        return score

# =============================================================================
# Comprehensive Test Cases
# =============================================================================

class TestConceptualDiffusion(unittest.TestCase):
    """Comprehensive test suite for conceptual diffusion."""
    
    def setUp(self):
        """Set up test environment."""
        self.device = DEVICE
        self.concept_dim = 64
        self.hidden_dim = 256
        self.batch_size = 8
        self.num_timesteps = 100  # Reduced for testing
        
        # Initialize model
        self.diffusion_model = ConceptualDiffusionModel(
            concept_dim=self.concept_dim,
            hidden_dim=self.hidden_dim,
            num_timesteps=self.num_timesteps,
            device=self.device
        )
        
        # Generate test data
        self.concept_generator = ConceptGenerator(self.concept_dim, self.device)
        self.test_concepts = self.concept_generator.generate_hierarchical_concepts(32, 3)
    
    def test_noise_schedule_properties(self):
        """Test properties of noise schedule."""
        logger.info("Testing noise schedule properties...")
        
        betas = self.diffusion_model.betas
        alphas = self.diffusion_model.alphas
        alphas_cumprod = self.diffusion_model.alphas_cumprod
        
        # Validate schedule properties
        validation = DiffusionMathUtils.validate_diffusion_coefficients(betas)
        
        for property_name, is_valid in validation.items():
            self.assertTrue(is_valid, f"Noise schedule property {property_name} failed")
        
        # Test monotonicity
        self.assertTrue(torch.all(betas[1:] >= betas[:-1]), "Betas should be non-decreasing")
        self.assertTrue(torch.all(alphas_cumprod[1:] <= alphas_cumprod[:-1]), 
                       "Alpha cumulative product should be non-increasing")
        
        # Test boundary conditions - allow for small beta_0
        # alphas_cumprod[0] = 1 - beta_0, so it should be close to but not exactly 1.0
        self.assertGreater(alphas_cumprod[0].item(), 0.999, "Alpha cumprod should start close to 1.0")
        self.assertLess(alphas_cumprod[0].item(), 1.001, "Alpha cumprod should not exceed 1.0")
        
        # Final alpha_cumprod should be significantly less than initial value
        # The threshold depends on the number of timesteps - with fewer steps, less decay
        final_threshold = 0.8 if self.num_timesteps <= 50 else 0.1
        self.assertLess(alphas_cumprod[-1].item(), final_threshold, 
                       f"Final alpha_cumprod should be < {final_threshold} with {self.num_timesteps} timesteps")
        
        # Should show clear decay from start to end
        decay_ratio = alphas_cumprod[-1] / alphas_cumprod[0]
        self.assertLess(decay_ratio.item(), 0.9, "Alpha cumprod should show significant decay")
        
        # Test mathematical consistency: alphas_cumprod[0] should equal (1 - betas[0])
        expected_first = (1.0 - betas[0]).item()
        self.assertAlmostEqual(alphas_cumprod[0].item(), expected_first, places=6,
                              msg="First alpha_cumprod should equal (1 - beta_0)")
        
        logger.info(f"Beta range: [{betas[0]:.6f}, {betas[-1]:.6f}]")
        logger.info(f"Alpha cumprod range: [{alphas_cumprod[0]:.6f}, {alphas_cumprod[-1]:.6f}]")
    
    def test_forward_diffusion_process(self):
        """Test forward diffusion process."""
        logger.info("Testing forward diffusion process...")
        
        x_0 = self.test_concepts.concepts[:self.batch_size]
        
        # Test at various timesteps
        timesteps = [0, 25, 50, 75, 99]
        
        for t_val in timesteps:
            t = torch.full((self.batch_size,), t_val, device=self.device)
            
            # Forward diffusion
            x_t, noise = self.diffusion_model.forward_diffusion(x_0, t)
            
            # Validate output shapes
            self.assertEqual(x_t.shape, x_0.shape)
            self.assertEqual(noise.shape, x_0.shape)
            
            # Validate mathematical properties
            alphas_cumprod_t = self.diffusion_model.alphas_cumprod[t_val]
            metrics = DiffusionMathUtils.compute_diffusion_metrics(x_0, x_t, alphas_cumprod_t)
            
            # Variance should increase with time
            if t_val > 0:
                expected_var_increase = 1.0 - alphas_cumprod_t.item()
                self.assertGreater(torch.var(x_t).item(), torch.var(x_0).item() * 0.5)
            
            # Check coefficient relationship
            signal_coeff = metrics['signal_coefficient']
            noise_coeff = metrics['noise_coefficient']
            coeff_sum_squared = signal_coeff ** 2 + noise_coeff ** 2
            
            self.assertAlmostEqual(coeff_sum_squared, 1.0, places=4, 
                                 msg=f"Coefficients should sum to 1 at t={t_val}")
        
        logger.info("Forward diffusion process tests passed")
    
    def test_reverse_diffusion_step(self):
        """Test reverse diffusion step."""
        logger.info("Testing reverse diffusion step...")
        
        x_0 = self.test_concepts.concepts[:self.batch_size]
        
        # Start with noisy concept
        t = torch.full((self.batch_size,), 50, device=self.device)
        x_t, _ = self.diffusion_model.forward_diffusion(x_0, t)
        
        # Reverse step
        x_prev = self.diffusion_model.reverse_diffusion_step(x_t, t)
        
        # Validate output shape
        self.assertEqual(x_prev.shape, x_t.shape)
        
        # Check for numerical stability
        self.assertTrue(torch.isfinite(x_prev).all(), "Reverse step output must be finite")
        self.assertFalse(torch.isnan(x_prev).any(), "Reverse step output must not contain NaN")
        
        # Test that reverse step reduces noise (on average)
        noise_level_before = torch.norm(x_t - x_0, dim=-1).mean()
        noise_level_after = torch.norm(x_prev - x_0, dim=-1).mean()
        
        # Note: This is probabilistic, so we allow some tolerance
        improvement_ratio = noise_level_after / noise_level_before
        self.assertLess(improvement_ratio, 1.2, "Reverse step should generally reduce noise")
        
        logger.info(f"Noise reduction ratio: {improvement_ratio:.3f}")
    
    def test_score_network_properties(self):
        """Test score network properties."""
        logger.info("Testing score network properties...")
        
        x = self.test_concepts.concepts[:self.batch_size]
        t = torch.randint(0, self.num_timesteps, (self.batch_size,), device=self.device)
        
        # Compute score
        t_emb = self.diffusion_model.time_embedding(t)
        score = self.diffusion_model.score_network(x, t_emb)
        
        # Validate output shape
        self.assertEqual(score.shape, x.shape)
        
        # Check numerical properties
        self.assertTrue(torch.isfinite(score).all(), "Score must be finite")
        self.assertFalse(torch.isnan(score).any(), "Score must not contain NaN")
        
        # Test score magnitude (should be reasonable)
        score_norm = torch.norm(score, dim=-1)
        self.assertLess(score_norm.max().item(), 100.0, "Score magnitude should be reasonable")
        
        # Test gradient computation (should be differentiable)
        x.requires_grad_(True)
        score = self.diffusion_model.score_network(x, t_emb)
        loss = score.sum()
        
        try:
            loss.backward()
            self.assertTrue(x.grad is not None, "Score network should be differentiable")
        except Exception as e:
            self.fail(f"Score network gradient computation failed: {e}")
        
        logger.info(f"Score network output range: [{score.min():.3f}, {score.max():.3f}]")
    
    def test_loss_computation(self):
        """Test loss computation."""
        logger.info("Testing loss computation...")
        
        x_0 = self.test_concepts.concepts[:self.batch_size]
        
        # Compute standard loss
        loss = self.diffusion_model.compute_loss(x_0)
        
        # Validate loss properties
        self.assertTrue(torch.isfinite(loss), "Loss must be finite")
        self.assertFalse(torch.isnan(loss), "Loss must not be NaN")
        self.assertGreaterEqual(loss.item(), 0.0, "MSE loss must be non-negative")
        
        # Test loss reduction
        loss_none = self.diffusion_model.compute_loss(x_0, reduction='none')
        loss_mean = self.diffusion_model.compute_loss(x_0, reduction='mean')
        loss_sum = self.diffusion_model.compute_loss(x_0, reduction='sum')
        
        self.assertEqual(loss_none.shape, x_0.shape)
        self.assertEqual(loss_mean.shape, ())
        self.assertEqual(loss_sum.shape, ())
        
        # Test VLB loss
        vlb_loss = self.diffusion_model.compute_vlb_loss(x_0)
        
        self.assertTrue(torch.isfinite(vlb_loss), "VLB loss must be finite")
        self.assertFalse(torch.isnan(vlb_loss), "VLB loss must not be NaN")
        
        logger.info(f"Standard loss: {loss.item():.6f}")
        logger.info(f"VLB loss: {vlb_loss.item():.6f}")
    
    def test_sampling_process(self):
        """Test sampling from the model."""
        logger.info("Testing sampling process...")
        
        # Sample without conditioning
        samples = self.diffusion_model.sample(batch_size=4)
        
        # Validate output
        self.assertEqual(samples.shape, (4, self.concept_dim))
        self.assertTrue(torch.isfinite(samples).all(), "Samples must be finite")
        self.assertFalse(torch.isnan(samples).any(), "Samples must not contain NaN")
        
        # Test trajectory sampling
        trajectory = self.diffusion_model.sample(batch_size=2, return_trajectory=True)
        
        self.assertEqual(len(trajectory), self.num_timesteps + 1)
        self.assertEqual(trajectory[0].shape, (2, self.concept_dim))
        self.assertEqual(trajectory[-1].shape, (2, self.concept_dim))
        
        # Test noise reduction over trajectory
        noise_levels = []
        for i, x_t in enumerate(trajectory):
            noise_level = torch.norm(x_t, dim=-1).mean().item()
            noise_levels.append(noise_level)
        
        # Generally, noise should decrease (though not monotonically due to stochasticity)
        initial_noise = noise_levels[0]
        final_noise = noise_levels[-1]
        
        logger.info(f"Initial noise level: {initial_noise:.3f}")
        logger.info(f"Final noise level: {final_noise:.3f}")
        logger.info(f"Noise reduction: {(initial_noise - final_noise) / initial_noise:.3f}")
    
    def test_mathematical_consistency(self):
        """Test mathematical consistency of diffusion process."""
        logger.info("Testing mathematical consistency...")
        
        x_0 = self.test_concepts.concepts[:self.batch_size]
        
        # Test forward-reverse consistency
        t = torch.full((self.batch_size,), 10, device=self.device)
        
        # Forward then reverse
        x_t, noise = self.diffusion_model.forward_diffusion(x_0, t)
        
        # Multiple reverse steps
        x_reconstructed = x_t.clone()
        for step in range(10):
            current_t = torch.full((self.batch_size,), 10 - step, device=self.device)
            if (10 - step) > 0:
                x_reconstructed = self.diffusion_model.reverse_diffusion_step(x_reconstructed, current_t)
        
        # Measure reconstruction quality
        reconstruction_error = F.mse_loss(x_reconstructed, x_0).item()
        
        # Error should be reasonable (perfect reconstruction not expected due to stochasticity)
        self.assertLess(reconstruction_error, 5.0, "Reconstruction error should be reasonable")
        
        # Test ELBO bounds
        with torch.no_grad():
            # Log likelihood lower bound
            vlb = self.diffusion_model.compute_vlb_loss(x_0)
            standard_loss = self.diffusion_model.compute_loss(x_0)
            
            # VLB should provide useful bound
            self.assertTrue(torch.isfinite(vlb), "VLB must be finite")
            
        logger.info(f"Reconstruction error: {reconstruction_error:.6f}")
    
    def test_conceptual_space_properties(self):
        """Test properties specific to conceptual spaces."""
        logger.info("Testing conceptual space properties...")
        
        # Generate semantic clusters with stronger structure
        clustered_concepts = self.concept_generator.generate_semantic_clusters(4, 8)
        x_0 = clustered_concepts.concepts
        
        # Test that semantic similarity is preserved through mild diffusion
        t_mild = torch.full((x_0.shape[0],), 5, device=self.device)  # Light noise
        x_t, _ = self.diffusion_model.forward_diffusion(x_0, t_mild)
        
        # Compute semantic similarities
        original_similarities = F.cosine_similarity(x_0.unsqueeze(1), x_0.unsqueeze(0), dim=-1)
        noisy_similarities = F.cosine_similarity(x_t.unsqueeze(1), x_t.unsqueeze(0), dim=-1)
        
        # Similarity preservation
        similarity_correlation = torch.corrcoef(torch.stack([
            original_similarities.flatten(),
            noisy_similarities.flatten()
        ]))[0, 1]
        
        self.assertGreater(similarity_correlation.item(), 0.7, 
                          "Semantic similarities should be largely preserved under mild noise")
        
        # Test hierarchical structure preservation using cluster-based concepts
        # Create more structured hierarchical concepts for this specific test
        structured_concepts = []
        structured_levels = []
        
        # Level 0: Base concepts (more specific)
        base_prototypes = [
            torch.tensor([1.0, 0.0, 0.0] + [0.0] * (self.concept_dim - 3), device=self.device),
            torch.tensor([0.0, 1.0, 0.0] + [0.0] * (self.concept_dim - 3), device=self.device),
            torch.tensor([0.0, 0.0, 1.0] + [0.0] * (self.concept_dim - 3), device=self.device),
        ]
        
        for i, prototype in enumerate(base_prototypes):
            for j in range(4):  # 4 concepts per prototype
                noise = torch.randn(self.concept_dim, device=self.device) * 0.1
                concept = prototype + noise
                structured_concepts.append(concept)
                structured_levels.append(0)
        
        # Level 1: Abstract concepts (combinations of base)
        for i in range(3):
            for j in range(2):  # 2 abstract concepts per base group
                # Combine two base prototypes
                combo = (base_prototypes[i] + base_prototypes[(i+1) % 3]) / 2
                noise = torch.randn(self.concept_dim, device=self.device) * 0.15
                concept = combo + noise
                structured_concepts.append(concept)
                structured_levels.append(1)
        
        hierarchical_concepts = torch.stack(structured_concepts)
        levels = structured_levels
        
        # Within-level similarities should be higher than across-level
        level_0_indices = [i for i, l in enumerate(levels) if l == 0]
        level_1_indices = [i for i, l in enumerate(levels) if l == 1]
        
        if len(level_0_indices) > 1 and len(level_1_indices) > 1:
            level_0_concepts = hierarchical_concepts[level_0_indices]
            level_1_concepts = hierarchical_concepts[level_1_indices]
            
            # Compute within-level similarity for level 0
            within_level_0_sims = []
            for i in range(len(level_0_concepts)):
                for j in range(i + 1, len(level_0_concepts)):
                    sim = F.cosine_similarity(
                        level_0_concepts[i].unsqueeze(0), level_0_concepts[j].unsqueeze(0)
                    ).item()
                    within_level_0_sims.append(sim)
            
            # Compute within-level similarity for level 1
            within_level_1_sims = []
            for i in range(len(level_1_concepts)):
                for j in range(i + 1, len(level_1_concepts)):
                    sim = F.cosine_similarity(
                        level_1_concepts[i].unsqueeze(0), level_1_concepts[j].unsqueeze(0)
                    ).item()
                    within_level_1_sims.append(sim)
            
            # Compute across-level similarities
            across_level_sims = []
            for concept_0 in level_0_concepts:
                for concept_1 in level_1_concepts:
                    sim = F.cosine_similarity(
                        concept_0.unsqueeze(0), concept_1.unsqueeze(0)
                    ).item()
                    across_level_sims.append(sim)
            
            within_level_sim = np.mean(within_level_0_sims + within_level_1_sims)
            across_level_sim = np.mean(across_level_sims)
            
            # With structured data, within-level should be higher
            # But allow some tolerance since we're using neural networks
            self.assertGreater(within_level_sim, across_level_sim - 0.1,
                             "Within-level similarity should generally exceed across-level similarity")
            
            logger.info(f"Within-level similarity: {within_level_sim:.3f}")
            logger.info(f"Across-level similarity: {across_level_sim:.3f}")
        
        logger.info(f"Semantic similarity preservation: {similarity_correlation.item():.3f}")
    
    def test_performance_benchmarks(self):
        """Test performance benchmarks."""
        logger.info("Testing performance benchmarks...")
        
        x_0 = self.test_concepts.concepts[:16]  # Larger batch
        
        # Measure forward diffusion time
        start_time = time.time()
        for _ in range(100):
            t = torch.randint(0, self.num_timesteps, (16,), device=self.device)
            _ = self.diffusion_model.forward_diffusion(x_0, t)
        forward_time = (time.time() - start_time) / 100
        
        # Measure reverse diffusion time
        start_time = time.time()
        t = torch.randint(1, self.num_timesteps, (16,), device=self.device)
        x_t, _ = self.diffusion_model.forward_diffusion(x_0, t)
        for _ in range(100):
            _ = self.diffusion_model.reverse_diffusion_step(x_t, t)
        reverse_time = (time.time() - start_time) / 100
        
        # Measure loss computation time
        start_time = time.time()
        for _ in range(100):
            _ = self.diffusion_model.compute_loss(x_0)
        loss_time = (time.time() - start_time) / 100
        
        # Performance assertions
        self.assertLess(forward_time, 0.01, "Forward diffusion should be fast")
        self.assertLess(reverse_time, 0.05, "Reverse diffusion should be reasonably fast")
        self.assertLess(loss_time, 0.05, "Loss computation should be reasonably fast")
        
        logger.info(f"Forward diffusion time: {forward_time*1000:.2f}ms")
        logger.info(f"Reverse diffusion time: {reverse_time*1000:.2f}ms")
        logger.info(f"Loss computation time: {loss_time*1000:.2f}ms")

# =============================================================================
# Integration Tests
# =============================================================================

class TestConceptualDiffusionIntegration(unittest.TestCase):
    """Integration tests for conceptual diffusion with other components."""
    
    def setUp(self):
        """Set up integration test environment."""
        self.device = DEVICE
        self.concept_dim = 64
        self.batch_size = 8
        
        self.diffusion_model = ConceptualDiffusionModel(
            concept_dim=self.concept_dim,
            num_timesteps=50,  # Reduced for testing
            device=self.device
        )
        
        self.concept_generator = ConceptGenerator(self.concept_dim, self.device)
    
    def test_training_integration(self):
        """Test integration with training procedures."""
        logger.info("Testing training integration...")
        
        # Generate training data
        train_concepts = self.concept_generator.generate_hierarchical_concepts(64, 3)
        train_data = train_concepts.concepts
        
        # Setup optimizer
        optimizer = Adam(self.diffusion_model.parameters(), lr=1e-4)
        
        # Training loop
        initial_loss = None
        final_loss = None
        
        for step in range(10):  # Short training for testing
            batch_indices = torch.randperm(train_data.shape[0])[:self.batch_size]
            batch_data = train_data[batch_indices]
            
            loss = self.diffusion_model.compute_loss(batch_data)
            
            if step == 0:
                initial_loss = loss.item()
            if step == 9:
                final_loss = loss.item()
            
            optimizer.zero_grad()
            loss.backward()
            
            # Gradient clipping
            torch.nn.utils.clip_grad_norm_(self.diffusion_model.parameters(), 1.0)
            
            optimizer.step()
        
        # Validate training progress
        self.assertTrue(torch.isfinite(torch.tensor(final_loss)), "Final loss must be finite")
        
        # Loss should generally decrease (though not guaranteed in short training)
        relative_change = abs(final_loss - initial_loss) / initial_loss
        self.assertLess(relative_change, 10.0, "Loss change should be reasonable")
        
        logger.info(f"Initial loss: {initial_loss:.6f}")
        logger.info(f"Final loss: {final_loss:.6f}")
        logger.info(f"Relative change: {relative_change:.3f}")
    
    def test_memory_efficiency(self):
        """Test memory efficiency."""
        logger.info("Testing memory efficiency...")
        
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
            initial_memory = torch.cuda.memory_allocated()
            
            # Large batch test
            large_batch = torch.randn(128, self.concept_dim, device=self.device)
            
            # Forward pass
            loss = self.diffusion_model.compute_loss(large_batch)
            loss.backward()
            
            peak_memory = torch.cuda.memory_allocated()
            memory_usage = (peak_memory - initial_memory) / 1024**2  # MB
            
            # Memory should be reasonable
            self.assertLess(memory_usage, 500, "Memory usage should be reasonable")
            
            logger.info(f"Memory usage for batch 128: {memory_usage:.2f}MB")
        else:
            logger.info("CUDA not available, skipping memory test")

# =============================================================================
# Test Suite Runner
# =============================================================================

def run_conceptual_diffusion_tests():
    """Run all conceptual diffusion tests."""
    
    # Create test suite
    test_classes = [
        TestConceptualDiffusion,
        TestConceptualDiffusionIntegration
    ]
    
    suite = unittest.TestSuite()
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Print summary
    logger.info("="*80)
    logger.info("CONCEPTUAL DIFFUSION TEST SUMMARY")
    logger.info("="*80)
    logger.info(f"Tests run: {result.testsRun}")
    logger.info(f"Failures: {len(result.failures)}")
    logger.info(f"Errors: {len(result.errors)}")
    logger.info(f"Success rate: {(result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun:.2%}")
    
    if result.failures:
        logger.error("FAILURES:")
        for test, traceback in result.failures:
            logger.error(f"  {test}: {traceback}")
    
    if result.errors:
        logger.error("ERRORS:")
        for test, traceback in result.errors:
            logger.error(f"  {test}: {traceback}")
    
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_conceptual_diffusion_tests()
    exit(0 if success else 1)