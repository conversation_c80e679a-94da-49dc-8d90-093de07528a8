#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ULTRA: Ultimate Learning & Thought Reasoning Architecture
Reverse Diffusion Reasoning Module

This module implements reasoning from desired outcomes back to potential solutions
using diffusion processes. It includes algorithms for guided diffusion, constraint
satisfaction, and goal-directed reasoning in the thought latent space.

Mathematical Foundation:
- Forward Process: q(z_t|z_{t-1}) = N(z_t; √(1-β_t)z_{t-1}, β_t I)
- Reverse Process: p_θ(z_{t-1}|z_t) = N(z_{t-1}; μ_θ(z_t,t), Σ_θ(z_t,t))
- Guided Process: μ_θ(z_t,t,y) = μ_θ(z_t,t) + γ_t∇_{z_t}log p(y|z_t)
- Multi-constraint: μ_θ(z_t,t,{y_i}) = μ_θ(z_t,t) + Σ_i γ_{t,i}∇_{z_t}log p(y_i|z_t)

The module contains:
1. Advanced guidance functions for directing diffusion toward specific goals
2. Reverse diffusion reasoning with multiple guidance strategies
3. Constraint-based reasoning methods with uncertainty quantification
4. Exploration and interpolation in reasoning spaces
5. Analogy completion and counterfactual reasoning capabilities
"""

import logging
import math
import time
import uuid
from typing import Dict, List, Tuple, Union, Optional, Callable, Any, TypeVar
from collections import defaultdict
from dataclasses import dataclass
from enum import Enum

import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.distributions import MultivariateNormal, Normal, Categorical
from tqdm.auto import tqdm
import scipy.stats
from scipy.optimize import minimize
from sklearn.decomposition import PCA
from sklearn.manifold import TSNE

# Replace the current import section with:

import sys
import os
from pathlib import Path

# Add the parent directory to the path for imports
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

logger = logging.getLogger(__name__)

# Try different import strategies
CONCEPTUAL_DIFFUSION_AVAILABLE = False
ConceptualDiffusion = None

try:
    from conceptual_diffusion import ConceptualDiffusion
    CONCEPTUAL_DIFFUSION_AVAILABLE = True
    logger.info("ConceptualDiffusion imported successfully (direct)")
except ImportError:
    try:
        from .conceptual_diffusion import ConceptualDiffusion  
        CONCEPTUAL_DIFFUSION_AVAILABLE = True
        logger.info("ConceptualDiffusion imported successfully (relative)")
    except ImportError as e:
        logger.warning(f"ConceptualDiffusion not available: {e}")

THOUGHT_LATENT_SPACE_AVAILABLE = False  
ThoughtLatentSpace = None

try:
    from thought_latent_space import ThoughtLatentSpace
    THOUGHT_LATENT_SPACE_AVAILABLE = True
    logger.info("ThoughtLatentSpace imported successfully (direct)")
except ImportError:
    try:
        from .thought_latent_space import ThoughtLatentSpace
        THOUGHT_LATENT_SPACE_AVAILABLE = True  
        logger.info("ThoughtLatentSpace imported successfully (relative)")
    except ImportError as e:
        logger.warning(f"ThoughtLatentSpace not available: {e}")

# Type hints
TensorType = TypeVar('TensorType', torch.Tensor, np.ndarray)
ConceptTensor = torch.Tensor
GuidanceTensor = torch.Tensor
TimestepTensor = torch.Tensor

# Constants and default parameters
DEFAULT_GUIDANCE_STRENGTH = 3.0
DEFAULT_NUM_STEPS = 50
DEFAULT_NUM_PATHS = 5
DEFAULT_TEMPERATURE = 1.0
DEFAULT_CLIP_RANGE = 10.0
DEFAULT_EPSILON = 1e-8
DEFAULT_MAX_GRAD_NORM = 1.0

# Guidance scheduling functions
GUIDANCE_SCHEDULES = {
    'linear': lambda t, T: 1.0 - (t / T),
    'cosine': lambda t, T: 0.5 * (1.0 + math.cos(math.pi * t / T)),
    'exponential': lambda t, T: math.exp(-5.0 * t / T),
    'sigmoid': lambda t, T: 1.0 / (1.0 + math.exp(10.0 * (t / T - 0.5))),
    'constant': lambda t, T: 1.0
}


class GuidanceType(Enum):
    """Enumeration of guidance types for reverse diffusion."""
    TARGET = "target"
    CONSTRAINT = "constraint"
    ANALYTIC = "analytic"
    PATH = "path"
    COMPOSITE = "composite"
    ADAPTIVE = "adaptive"
    ENERGY = "energy"


@dataclass
class DiffusionConfig:
    """Configuration for diffusion processes."""
    num_timesteps: int = 1000
    beta_start: float = 1e-4
    beta_end: float = 0.02
    beta_schedule: str = 'linear'
    clip_denoised: bool = True
    clip_range: float = DEFAULT_CLIP_RANGE
    use_ema: bool = True
    ema_decay: float = 0.9999


@dataclass
class GuidanceConfig:
    """Configuration for guidance functions."""
    strength: float = DEFAULT_GUIDANCE_STRENGTH
    schedule: str = 'linear'
    start_step: int = 0
    end_step: Optional[int] = None
    normalize_grads: bool = True
    max_grad_norm: float = DEFAULT_MAX_GRAD_NORM
    use_momentum: bool = False
    momentum_beta: float = 0.9


class GuidanceFunctionBase:
    """
    Base class for diffusion guidance functions with advanced mathematical foundations.
    
    Implements the general framework for computing guidance gradients:
    ∇_{z_t} log p(y|z_t) where y represents the desired outcome or constraint.
    """
    
    def __init__(self, config: Optional[GuidanceConfig] = None):
        """
        Initialize base guidance function.
        
        Args:
            config: Configuration for guidance behavior
        """
        self.config = config or GuidanceConfig()
        self.call_count = 0
        self.guidance_history = []
        self.momentum_buffer = None
        
        # Setup strength schedule
        if self.config.schedule in GUIDANCE_SCHEDULES:
            self.strength_schedule = GUIDANCE_SCHEDULES[self.config.schedule]
        else:
            self.strength_schedule = GUIDANCE_SCHEDULES['linear']
    
    def __call__(self, x_t: ConceptTensor, t: TimestepTensor, **kwargs) -> GuidanceTensor:
        """
        Compute guidance for the diffusion process.
        
        Args:
            x_t: Diffused concept tensor at time t [batch_size, concept_dim]
            t: Current timestep(s) [batch_size] or scalar
            **kwargs: Additional arguments for specific guidance types
            
        Returns:
            Guidance tensor [batch_size, concept_dim]
        """
        self.call_count += 1
        
        # Ensure proper tensor formatting
        if not isinstance(x_t, torch.Tensor):
            x_t = torch.tensor(x_t, dtype=torch.float32)
        if not isinstance(t, torch.Tensor):
            t = torch.tensor(t, dtype=torch.long)
            
        # Expand timestep if scalar
        if t.dim() == 0:
            t = t.expand(x_t.size(0))
        
        # Check if guidance should be applied
        t_mean = t.float().mean().item()
        if (self.config.start_step is not None and t_mean < self.config.start_step):
            return torch.zeros_like(x_t)
        if (self.config.end_step is not None and t_mean > self.config.end_step):
            return torch.zeros_like(x_t)
        
        # Compute base guidance
        guidance = self._compute_guidance(x_t, t, **kwargs)
        
        # Apply strength schedule
        strength_factor = self.strength_schedule(t_mean, self.config.num_timesteps if hasattr(self.config, 'num_timesteps') else 1000)
        guidance = guidance * self.config.strength * strength_factor
        
        # Apply gradient normalization if enabled
        if self.config.normalize_grads:
            guidance_norm = torch.norm(guidance, dim=-1, keepdim=True)
            guidance = guidance / (guidance_norm + DEFAULT_EPSILON)
            guidance = guidance * torch.clamp(guidance_norm, max=self.config.max_grad_norm)
        
        # Apply momentum if enabled
        if self.config.use_momentum:
            if self.momentum_buffer is None:
                self.momentum_buffer = torch.zeros_like(guidance)
            self.momentum_buffer = self.config.momentum_beta * self.momentum_buffer + (1 - self.config.momentum_beta) * guidance
            guidance = self.momentum_buffer
        
        # Store guidance history for analysis
        self.guidance_history.append({
            'timestep': t_mean,
            'guidance_norm': torch.norm(guidance).item(),
            'call_count': self.call_count
        })
        
        return guidance
    
    def _compute_guidance(self, x_t: ConceptTensor, t: TimestepTensor, **kwargs) -> GuidanceTensor:
        """
        Compute the core guidance direction. Must be implemented by subclasses.
        
        Args:
            x_t: Diffused concept tensor at time t
            t: Current timestep(s)
            **kwargs: Additional arguments
            
        Returns:
            Raw guidance tensor before strength and normalization
        """
        raise NotImplementedError("Guidance functions must implement _compute_guidance")
    
    def update(self, *args, **kwargs) -> None:
        """
        Update internal state of the guidance function.
        
        Default implementation does nothing. Subclasses should override
        if they need to update internal state during diffusion.
        """
        pass
    
    def reset(self) -> None:
        """Reset guidance function state."""
        self.call_count = 0
        self.guidance_history.clear()
        self.momentum_buffer = None
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get guidance statistics for analysis."""
        if not self.guidance_history:
            return {}
        
        norms = [h['guidance_norm'] for h in self.guidance_history]
        timesteps = [h['timestep'] for h in self.guidance_history]
        
        return {
            'call_count': self.call_count,
            'avg_guidance_norm': np.mean(norms),
            'max_guidance_norm': np.max(norms),
            'min_guidance_norm': np.min(norms),
            'avg_timestep': np.mean(timesteps),
            'guidance_variance': np.var(norms)
        }


class TargetGuidance(GuidanceFunctionBase):
    """
    Advanced target guidance with multiple similarity metrics and adaptive strength.
    
    Mathematical formulation:
    For target τ, guidance is: ∇_{z_t} [-||z_t - τ||²] = 2(τ - z_t)
    With cosine similarity: ∇_{z_t} [cos(z_t, τ)] = (τ - z_t·cos(z_t,τ))/||z_t||
    """
    
    def __init__(self, 
                 target: ConceptTensor, 
                 config: Optional[GuidanceConfig] = None,
                 similarity_type: str = 'euclidean',
                 adaptive_strength: bool = True,
                 target_annealing: bool = False):
        """
        Initialize target guidance with advanced features.
        
        Args:
            target: Target concept tensor [concept_dim] or [batch_size, concept_dim]
            config: Guidance configuration
            similarity_type: Type of similarity ('euclidean', 'cosine', 'mahalanobis', 'mixed')
            adaptive_strength: Whether to adapt strength based on distance to target
            target_annealing: Whether to gradually modify target during diffusion
        """
        super().__init__(config)
        
        # Ensure target is properly normalized based on similarity type
        if similarity_type in ['cosine', 'mixed']:
            self.target = F.normalize(target, p=2, dim=-1)
        else:
            self.target = target
        
        self.similarity_type = similarity_type
        self.adaptive_strength = adaptive_strength
        self.target_annealing = target_annealing
        self.original_target = self.target.clone()
        
        # For Mahalanobis distance
        self.precision_matrix = None
        if similarity_type == 'mahalanobis':
            dim = target.shape[-1]
            self.precision_matrix = torch.eye(dim, device=target.device, dtype=target.dtype)
        
        # Annealing schedule
        self.annealing_schedule = lambda t, T: 1.0 - 0.5 * (t / T)  # Linear annealing
        
        logger.debug(f"Initialized TargetGuidance with similarity_type={similarity_type}")
    
    def _compute_guidance(self, x_t: ConceptTensor, t: TimestepTensor, **kwargs) -> GuidanceTensor:
        """
        Compute target guidance using specified similarity metric.
        
        Args:
            x_t: Current concept tensor [batch_size, concept_dim]
            t: Current timestep(s)
            
        Returns:
            Guidance tensor pointing toward target
        """
        batch_size = x_t.size(0)
        
        # Handle target broadcasting
        if len(self.target.shape) == 1:
            target = self.target.unsqueeze(0).expand(batch_size, -1)
        else:
            if self.target.size(0) != batch_size:
                # Repeat target to match batch size
                target = self.target.repeat(batch_size // self.target.size(0) + 1, 1)[:batch_size]
            else:
                target = self.target
        
        # Apply target annealing if enabled
        if self.target_annealing:
            t_mean = t.float().mean().item()
            T = getattr(self.config, 'num_timesteps', 1000)
            annealing_factor = self.annealing_schedule(t_mean, T)
            target = annealing_factor * target + (1 - annealing_factor) * self.original_target
        
        # Compute guidance based on similarity type
        if self.similarity_type == 'euclidean':
            guidance = self._euclidean_guidance(x_t, target)
        elif self.similarity_type == 'cosine':
            guidance = self._cosine_guidance(x_t, target)
        elif self.similarity_type == 'mahalanobis':
            guidance = self._mahalanobis_guidance(x_t, target)
        elif self.similarity_type == 'mixed':
            guidance = self._mixed_guidance(x_t, target)
        else:
            raise ValueError(f"Unknown similarity type: {self.similarity_type}")
        
        # Apply adaptive strength if enabled
        if self.adaptive_strength:
            distance = torch.norm(x_t - target, dim=-1, keepdim=True)
            # Stronger guidance when farther from target
            adaptive_factor = torch.sigmoid(distance - 1.0) + 0.1
            guidance = guidance * adaptive_factor
        
        return guidance
    
    def _euclidean_guidance(self, x_t: ConceptTensor, target: ConceptTensor) -> GuidanceTensor:
        """Compute Euclidean distance-based guidance: ∇[-||x_t - target||²] = 2(target - x_t)"""
        return 2.0 * (target - x_t)
    
    def _cosine_guidance(self, x_t: ConceptTensor, target: ConceptTensor) -> GuidanceTensor:
        """
        Compute cosine similarity-based guidance.
        
        ∇[cos(x_t, target)] = (target - x_t·cos(x_t,target))/||x_t||
        """
        x_t_norm = F.normalize(x_t, p=2, dim=-1)
        target_norm = F.normalize(target, p=2, dim=-1)
        
        cosine_sim = torch.sum(x_t_norm * target_norm, dim=-1, keepdim=True)
        
        # Gradient of cosine similarity
        x_t_magnitude = torch.norm(x_t, dim=-1, keepdim=True) + DEFAULT_EPSILON
        guidance = (target_norm - cosine_sim * x_t_norm) / x_t_magnitude
        
        return guidance
    
    def _mahalanobis_guidance(self, x_t: ConceptTensor, target: ConceptTensor) -> GuidanceTensor:
        """
        Compute Mahalanobis distance-based guidance.
        
        ∇[-√((x_t-target)ᵀΣ⁻¹(x_t-target))] = -Σ⁻¹(x_t-target)/√(...)
        """
        diff = x_t - target
        
        # Apply precision matrix
        if self.precision_matrix is not None:
            transformed_diff = torch.matmul(diff, self.precision_matrix)
        else:
            transformed_diff = diff
        
        # Compute gradient
        mahalanobis_dist = torch.sqrt(torch.sum(diff * transformed_diff, dim=-1, keepdim=True) + DEFAULT_EPSILON)
        guidance = -transformed_diff / mahalanobis_dist
        
        return guidance
    
    def _mixed_guidance(self, x_t: ConceptTensor, target: ConceptTensor) -> GuidanceTensor:
        """Combine multiple guidance types with learnable weights."""
        euclidean_guidance = self._euclidean_guidance(x_t, target)
        cosine_guidance = self._cosine_guidance(x_t, target)
        
        # Adaptive mixing based on current similarity
        cosine_sim = F.cosine_similarity(x_t, target, dim=-1).abs().unsqueeze(-1)
        
        # When cosine similarity is high, focus more on Euclidean; when low, focus on cosine
        euclidean_weight = cosine_sim
        cosine_weight = 1.0 - cosine_sim
        
        return euclidean_weight * euclidean_guidance + cosine_weight * cosine_guidance
    
    def update_precision_matrix(self, samples: ConceptTensor) -> None:
        """Update precision matrix for Mahalanobis distance based on sample statistics."""
        if self.similarity_type == 'mahalanobis':
            # Compute sample covariance
            centered = samples - samples.mean(dim=0, keepdim=True)
            cov_matrix = torch.matmul(centered.t(), centered) / (samples.size(0) - 1)
            
            # Add regularization
            reg_term = DEFAULT_EPSILON * torch.eye(cov_matrix.size(0), device=cov_matrix.device)
            cov_matrix = cov_matrix + reg_term
            
            # Invert to get precision matrix
            try:
                self.precision_matrix = torch.inverse(cov_matrix)
            except RuntimeError:
                logger.warning("Failed to invert covariance matrix, using regularized version")
                self.precision_matrix = torch.inverse(cov_matrix + 0.1 * reg_term)


class ConstraintGuidance(GuidanceFunctionBase):
    """
    Advanced constraint-based guidance with multiple constraint types and optimization.
    
    Mathematical formulation:
    For constraint c(z), guidance is: ∇_{z_t} c(z_t)
    For multiple constraints: Σ_i λ_i ∇_{z_t} c_i(z_t)
    """
    
    def __init__(
        self, 
        constraint_fn: Callable[[ConceptTensor], torch.Tensor], 
        config: Optional[GuidanceConfig] = None,
        constraint_type: str = 'satisfaction',
        optimization_method: str = 'gradient',
        constraint_weights: Optional[torch.Tensor] = None,
        use_second_order: bool = False
    ):
        """
        Initialize constraint guidance with advanced optimization.
        
        Args:
            constraint_fn: Function computing constraint satisfaction [0, 1] or constraint violation
            config: Guidance configuration
            constraint_type: 'satisfaction' (maximize) or 'violation' (minimize)
            optimization_method: 'gradient', 'finite_diff', 'evolutionary'
            constraint_weights: Optional weights for multiple constraints
            use_second_order: Whether to use second-order information (Hessian)
        """
        super().__init__(config)
        
        self.constraint_fn = constraint_fn
        self.constraint_type = constraint_type
        self.optimization_method = optimization_method
        self.constraint_weights = constraint_weights
        self.use_second_order = use_second_order
        
        # Parameters for finite difference
        self.fd_epsilon = 1e-4
        self.fd_samples = 20
        
        # Parameters for evolutionary optimization
        self.evo_population_size = 10
        self.evo_mutation_rate = 0.1
        
        # Hessian computation
        self.hessian_reg = 1e-3
        
        logger.debug(f"Initialized ConstraintGuidance with method={optimization_method}")
    
    def _compute_guidance(self, x_t: ConceptTensor, t: TimestepTensor, **kwargs) -> GuidanceTensor:
        """
        Compute constraint-based guidance using specified optimization method.
        
        Args:
            x_t: Current concept tensor [batch_size, concept_dim]
            t: Current timestep(s)
            
        Returns:
            Guidance tensor based on constraint gradients
        """
        if self.optimization_method == 'gradient':
            return self._gradient_guidance(x_t)
        elif self.optimization_method == 'finite_diff':
            return self._finite_difference_guidance(x_t)
        elif self.optimization_method == 'evolutionary':
            return self._evolutionary_guidance(x_t)
        else:
            raise ValueError(f"Unknown optimization method: {self.optimization_method}")
    
    def _gradient_guidance(self, x_t: ConceptTensor) -> GuidanceTensor:
        """Compute guidance using automatic differentiation."""
        x_t_grad = x_t.detach().clone()
        x_t_grad.requires_grad_(True)
        
        try:
            # Evaluate constraint function
            constraint_values = self.constraint_fn(x_t_grad)
            
            # Handle different constraint outputs
            if constraint_values.dim() == 1:
                # Single constraint per sample
                if self.constraint_type == 'satisfaction':
                    loss = -torch.sum(constraint_values)  # Maximize satisfaction
                else:
                    loss = torch.sum(constraint_values)   # Minimize violation
            else:
                # Multiple constraints per sample
                if self.constraint_weights is not None:
                    weighted_constraints = constraint_values * self.constraint_weights
                else:
                    weighted_constraints = constraint_values
                
                if self.constraint_type == 'satisfaction':
                    loss = -torch.sum(weighted_constraints)
                else:
                    loss = torch.sum(weighted_constraints)
            
            # Compute gradient
            loss.backward()
            guidance = -x_t_grad.grad.clone()  # Negative for gradient ascent
            
            # Add second-order correction if enabled
            if self.use_second_order:
                guidance = guidance + self._compute_hessian_correction(x_t_grad, constraint_values)
            
        except Exception as e:
            logger.warning(f"Gradient computation failed: {e}, falling back to finite difference")
            guidance = self._finite_difference_guidance(x_t)
        finally:
            if x_t_grad.grad is not None:
                x_t_grad.grad.zero_()
        
        return guidance
    
    def _finite_difference_guidance(self, x_t: ConceptTensor) -> GuidanceTensor:
        """Compute guidance using finite difference approximation."""
        batch_size, dim = x_t.shape
        guidance = torch.zeros_like(x_t)
        
        # Evaluate baseline constraint
        baseline_scores = self.constraint_fn(x_t)
        
        # Sample random directions for finite difference
        for _ in range(self.fd_samples):
            # Sample random directions
            directions = torch.randn_like(x_t)
            directions = F.normalize(directions, dim=-1) * self.fd_epsilon
            
            # Forward difference
            perturbed_scores = self.constraint_fn(x_t + directions)
            
            # Compute gradient contribution
            if baseline_scores.dim() == 1:
                score_diff = perturbed_scores - baseline_scores
                if self.constraint_type == 'violation':
                    score_diff = -score_diff
                
                for i in range(batch_size):
                    guidance[i] += directions[i] * score_diff[i] / self.fd_epsilon
            else:
                # Multiple constraints
                score_diff = perturbed_scores - baseline_scores
                if self.constraint_type == 'violation':
                    score_diff = -score_diff
                
                if self.constraint_weights is not None:
                    score_diff = score_diff * self.constraint_weights
                
                total_diff = torch.sum(score_diff, dim=-1)
                for i in range(batch_size):
                    guidance[i] += directions[i] * total_diff[i] / self.fd_epsilon
        
        # Average gradients
        guidance = guidance / self.fd_samples
        
        return guidance
    
    def _evolutionary_guidance(self, x_t: ConceptTensor) -> GuidanceTensor:
        """Compute guidance using evolutionary optimization principles."""
        batch_size, dim = x_t.shape
        guidance = torch.zeros_like(x_t)
        
        for b in range(batch_size):
            current_sample = x_t[b:b+1]
            
            # Generate population around current sample
            population = current_sample.repeat(self.evo_population_size, 1)
            mutations = torch.randn_like(population) * self.evo_mutation_rate
            population = population + mutations
            
            # Evaluate fitness
            fitness_scores = self.constraint_fn(population)
            if fitness_scores.dim() > 1:
                fitness_scores = torch.sum(fitness_scores, dim=-1)
            
            if self.constraint_type == 'violation':
                fitness_scores = -fitness_scores
            
            # Select best individuals
            _, best_indices = torch.topk(fitness_scores, k=self.evo_population_size // 2)
            best_population = population[best_indices]
            
            # Compute guidance as direction toward population centroid
            centroid = torch.mean(best_population, dim=0)
            guidance[b] = centroid - current_sample.squeeze(0)
        
        return guidance
    
    def _compute_hessian_correction(self, x_t: ConceptTensor, constraint_values: torch.Tensor) -> GuidanceTensor:
        """Compute second-order correction using Hessian information."""
        try:
            # Compute Hessian using autograd
            grad_outputs = torch.ones_like(constraint_values)
            gradients = torch.autograd.grad(
                outputs=constraint_values,
                inputs=x_t,
                grad_outputs=grad_outputs,
                create_graph=True,
                retain_graph=True
            )[0]
            
            # Approximate Hessian diagonal (BFGS-style update would be more sophisticated)
            hessian_diag = torch.autograd.grad(
                outputs=gradients.sum(),
                inputs=x_t,
                retain_graph=False
            )[0]
            
            # Apply regularization and compute correction
            hessian_reg_diag = hessian_diag + self.hessian_reg
            correction = gradients / (hessian_reg_diag + DEFAULT_EPSILON)
            
            return correction
            
        except Exception as e:
            logger.debug(f"Hessian computation failed: {e}")
            return torch.zeros_like(x_t)


class EnergyGuidance(GuidanceFunctionBase):
    """
    Energy-based guidance using physical analogies and energy landscapes.
    
    Mathematical formulation:
    Energy function: E(z) = Σ_i V_i(z) where V_i are potential terms
    Guidance: ∇_{z_t} [-E(z_t)] = -∇_{z_t} E(z_t)
    """
    
    def __init__(self,
                 energy_fn: Callable[[ConceptTensor], torch.Tensor],
                 config: Optional[GuidanceConfig] = None,
                 potential_type: str = 'attractive',
                 temperature: float = 1.0,
                 damping: float = 0.1):
        """
        Initialize energy-based guidance.
        
        Args:
            energy_fn: Function computing energy landscape
            config: Guidance configuration
            potential_type: 'attractive', 'repulsive', or 'mixed'
            temperature: Temperature for energy scaling
            damping: Damping factor for stability
        """
        super().__init__(config)
        
        self.energy_fn = energy_fn
        self.potential_type = potential_type
        self.temperature = temperature
        self.damping = damping
        
        # Energy history for analysis
        self.energy_history = []
        
    def _compute_guidance(self, x_t: ConceptTensor, t: TimestepTensor, **kwargs) -> GuidanceTensor:
        """Compute energy-based guidance."""
        x_t_energy = x_t.detach().clone()
        x_t_energy.requires_grad_(True)
        
        try:
            # Compute energy
            energy = self.energy_fn(x_t_energy)
            self.energy_history.append(energy.mean().item())
            
            # Scale by temperature
            scaled_energy = energy / self.temperature
            
            # Compute energy gradient
            energy_grad = torch.autograd.grad(
                outputs=scaled_energy.sum(),
                inputs=x_t_energy,
                retain_graph=False
            )[0]
            
            # Apply potential type
            if self.potential_type == 'attractive':
                guidance = -energy_grad  # Move toward lower energy
            elif self.potential_type == 'repulsive':
                guidance = energy_grad   # Move toward higher energy
            else:  # mixed
                # Use energy magnitude to determine attraction/repulsion
                energy_sign = torch.sign(energy).unsqueeze(-1)
                guidance = -energy_sign * energy_grad
            
            # Apply damping
            guidance = guidance * (1.0 - self.damping)
            
        except Exception as e:
            logger.warning(f"Energy guidance computation failed: {e}")
            guidance = torch.zeros_like(x_t)
        finally:
            if x_t_energy.grad is not None:
                x_t_energy.grad.zero_()
        
        return guidance


class PathGuidance(GuidanceFunctionBase):
    """
    Advanced path guidance with smooth interpolation and dynamic waypoints.
    
    Guides diffusion along predefined paths with sophisticated interpolation.
    """
    
    def __init__(self, 
                 path_points: List[ConceptTensor],
                 config: Optional[GuidanceConfig] = None,
                 interpolation_method: str = 'cubic_spline',
                 path_smoothing: float = 0.5,
                 dynamic_waypoints: bool = True,
                 lookahead_distance: int = 2):
        """
        Initialize path guidance with advanced interpolation.
        
        Args:
            path_points: List of concept tensors defining the path
            config: Guidance configuration
            interpolation_method: 'linear', 'cubic_spline', 'bezier', 'spherical'
            path_smoothing: Smoothing factor for path following
            dynamic_waypoints: Whether to dynamically adjust waypoints
            lookahead_distance: Number of points to look ahead for smoothing
        """
        super().__init__(config)
        
        # Normalize path points
        self.path_points = [F.normalize(p, p=2, dim=-1) for p in path_points]
        self.interpolation_method = interpolation_method
        self.path_smoothing = path_smoothing
        self.dynamic_waypoints = dynamic_waypoints
        self.lookahead_distance = lookahead_distance
        
        # Path statistics
        self.path_length = len(path_points)
        self.current_progress = 0.0
        
        # Pre-compute path segments for efficiency
        self._precompute_path_segments()
        
    def _precompute_path_segments(self):
        """Pre-compute path segments for efficient interpolation."""
        self.path_segments = []
        
        for i in range(len(self.path_points) - 1):
            segment = {
                'start': self.path_points[i],
                'end': self.path_points[i + 1],
                'direction': F.normalize(self.path_points[i + 1] - self.path_points[i], p=2, dim=-1),
                'length': torch.norm(self.path_points[i + 1] - self.path_points[i]).item()
            }
            self.path_segments.append(segment)
    
    def _compute_guidance(self, x_t: ConceptTensor, t: TimestepTensor, **kwargs) -> GuidanceTensor:
        """Compute path guidance with advanced interpolation."""
        batch_size = x_t.shape[0]
        
        # Update progress based on timestep
        t_mean = t.float().mean().item()
        T = getattr(self.config, 'num_timesteps', 1000)
        
        # Progress along path (0 to 1)
        progress = 1.0 - (t_mean / T)
        self.current_progress = progress
        
        # Get current target point on path
        current_target = self._interpolate_path_position(progress)
        
        # Expand target for batch
        if len(current_target.shape) == 1:
            current_target = current_target.unsqueeze(0).expand(batch_size, -1)
        
        # Compute base direction toward target
        if self.interpolation_method == 'spherical':
            guidance = self._spherical_guidance(x_t, current_target)
        else:
            guidance = current_target - F.normalize(x_t, p=2, dim=-1)
        
        # Apply path smoothing with lookahead
        if self.path_smoothing > 0 and self.lookahead_distance > 0:
            lookahead_progress = min(1.0, progress + 0.1)  # Look 10% ahead
            lookahead_target = self._interpolate_path_position(lookahead_progress)
            
            if len(lookahead_target.shape) == 1:
                lookahead_target = lookahead_target.unsqueeze(0).expand(batch_size, -1)
            
            lookahead_guidance = lookahead_target - F.normalize(x_t, p=2, dim=-1)
            
            # Blend current and lookahead guidance
            guidance = (1 - self.path_smoothing) * guidance + self.path_smoothing * lookahead_guidance
        
        return guidance
    
    def _interpolate_path_position(self, progress: float) -> ConceptTensor:
        """Interpolate position on path based on progress."""
        progress = max(0.0, min(1.0, progress))
        
        if progress == 0.0:
            return self.path_points[0]
        elif progress == 1.0:
            return self.path_points[-1]
        
        # Find segment
        segment_progress = progress * (self.path_length - 1)
        segment_idx = int(segment_progress)
        local_progress = segment_progress - segment_idx
        
        segment_idx = min(segment_idx, len(self.path_segments) - 1)
        
        if self.interpolation_method == 'linear':
            return self._linear_interpolation(segment_idx, local_progress)
        elif self.interpolation_method == 'cubic_spline':
            return self._cubic_spline_interpolation(segment_idx, local_progress)
        elif self.interpolation_method == 'bezier':
            return self._bezier_interpolation(segment_idx, local_progress)
        elif self.interpolation_method == 'spherical':
            return self._spherical_interpolation(segment_idx, local_progress)
        else:
            return self._linear_interpolation(segment_idx, local_progress)
    
    def _linear_interpolation(self, segment_idx: int, local_progress: float) -> ConceptTensor:
        """Linear interpolation between path points."""
        start = self.path_points[segment_idx]
        end = self.path_points[segment_idx + 1]
        return (1 - local_progress) * start + local_progress * end
    
    def _cubic_spline_interpolation(self, segment_idx: int, local_progress: float) -> ConceptTensor:
        """Cubic spline interpolation for smooth path following."""
        # Get control points (with boundary handling)
        p0 = self.path_points[max(0, segment_idx - 1)]
        p1 = self.path_points[segment_idx]
        p2 = self.path_points[segment_idx + 1]
        p3 = self.path_points[min(len(self.path_points) - 1, segment_idx + 2)]
        
        # Catmull-Rom spline
        t = local_progress
        t2 = t * t
        t3 = t2 * t
        
        result = 0.5 * (
            (2 * p1) +
            (-p0 + p2) * t +
            (2 * p0 - 5 * p1 + 4 * p2 - p3) * t2 +
            (-p0 + 3 * p1 - 3 * p2 + p3) * t3
        )
        
        return F.normalize(result, p=2, dim=-1)
    
    def _bezier_interpolation(self, segment_idx: int, local_progress: float) -> ConceptTensor:
        """Bezier curve interpolation for smooth guidance."""
        p0 = self.path_points[segment_idx]
        p3 = self.path_points[segment_idx + 1]
        
        # Create control points
        direction = p3 - p0
        p1 = p0 + 0.33 * direction
        p2 = p0 + 0.67 * direction
        
        # Cubic Bezier
        t = local_progress
        inv_t = 1 - t
        
        result = (inv_t**3 * p0 + 
                 3 * inv_t**2 * t * p1 + 
                 3 * inv_t * t**2 * p2 + 
                 t**3 * p3)
        
        return F.normalize(result, p=2, dim=-1)
    
    def _spherical_interpolation(self, segment_idx: int, local_progress: float) -> ConceptTensor:
        """Spherical linear interpolation (SLERP)."""
        start = F.normalize(self.path_points[segment_idx], p=2, dim=-1)
        end = F.normalize(self.path_points[segment_idx + 1], p=2, dim=-1)
        
        # Compute angle between vectors
        dot_product = torch.sum(start * end, dim=-1, keepdim=True).clamp(-1, 1)
        omega = torch.acos(dot_product.abs())
        
        # Handle near-parallel vectors
        if omega.abs() < 1e-5:
            return (1 - local_progress) * start + local_progress * end
        
        # SLERP formula
        sin_omega = torch.sin(omega)
        result = (torch.sin((1 - local_progress) * omega) / sin_omega) * start + \
                (torch.sin(local_progress * omega) / sin_omega) * end
        
        return F.normalize(result, p=2, dim=-1)
    
    def _spherical_guidance(self, x_t: ConceptTensor, target: ConceptTensor) -> GuidanceTensor:
        """Compute guidance for spherical interpolation."""
        x_t_norm = F.normalize(x_t, p=2, dim=-1)
        target_norm = F.normalize(target, p=2, dim=-1)
        
        # Great circle direction
        cross_product = torch.cross(x_t_norm, target_norm, dim=-1)
        cross_norm = torch.norm(cross_product, dim=-1, keepdim=True) + DEFAULT_EPSILON
        
        # Tangent direction
        tangent = cross_product / cross_norm
        
        # Compute signed angle
        dot_product = torch.sum(x_t_norm * target_norm, dim=-1, keepdim=True)
        angle = torch.acos(dot_product.clamp(-1, 1))
        
        return angle * tangent


class CompositeGuidance(GuidanceFunctionBase):
    """
    Advanced composite guidance with dynamic weighting and conflict resolution.
    
    Combines multiple guidance functions with sophisticated weighting schemes.
    """
    
    def __init__(self, 
                 guidance_functions: List[Tuple[GuidanceFunctionBase, float]],
                 config: Optional[GuidanceConfig] = None,
                 weighting_method: str = 'static',
                 conflict_resolution: str = 'weighted_average',
                 normalization: str = 'l2'):
        """
        Initialize composite guidance with advanced combination methods.
        
        Args:
            guidance_functions: List of (guidance_function, weight) tuples
            config: Guidance configuration
            weighting_method: 'static', 'dynamic', 'adaptive', 'learned'
            conflict_resolution: Method for resolving conflicting guidance
            normalization: Normalization method for combined guidance
        """
        super().__init__(config)
        
        self.guidance_functions = guidance_functions
        self.weighting_method = weighting_method
        self.conflict_resolution = conflict_resolution
        self.normalization = normalization
        
        # Dynamic weighting parameters
        self.weight_history = []
        self.performance_history = []
        
        # Learned weighting (simple neural network)
        if weighting_method == 'learned':
            self.weight_network = nn.Sequential(
                nn.Linear(len(guidance_functions), 64),
                nn.ReLU(),
                nn.Linear(64, len(guidance_functions)),
                nn.Softmax(dim=-1)
            )
        
        logger.debug(f"Initialized CompositeGuidance with {len(guidance_functions)} functions")
    
    def _compute_guidance(self, x_t: ConceptTensor, t: TimestepTensor, **kwargs) -> GuidanceTensor:
        """Compute composite guidance with advanced combination."""
        guidance_vectors = []
        base_weights = []
        
        # Collect guidance from all functions
        for func, weight in self.guidance_functions:
            try:
                guidance = func(x_t, t, **kwargs)
                guidance_vectors.append(guidance)
                base_weights.append(weight)
            except Exception as e:
                logger.warning(f"Guidance function failed: {e}")
                guidance_vectors.append(torch.zeros_like(x_t))
                base_weights.append(0.0)
        
        if not guidance_vectors:
            return torch.zeros_like(x_t)
        
        # Stack guidance vectors
        stacked_guidance = torch.stack(guidance_vectors, dim=0)  # [num_functions, batch_size, concept_dim]
        
        # Compute dynamic weights
        weights = self._compute_weights(stacked_guidance, x_t, t, base_weights)
        
        # Resolve conflicts and combine
        combined_guidance = self._combine_guidance(stacked_guidance, weights)
        
        # Apply normalization
        if self.normalization == 'l2':
            guidance_norm = torch.norm(combined_guidance, dim=-1, keepdim=True) + DEFAULT_EPSILON
            combined_guidance = combined_guidance / guidance_norm * torch.clamp(guidance_norm, max=self.config.max_grad_norm)
        elif self.normalization == 'l1':
            guidance_norm = torch.sum(torch.abs(combined_guidance), dim=-1, keepdim=True) + DEFAULT_EPSILON
            combined_guidance = combined_guidance / guidance_norm
        
        # Store weights for analysis
        self.weight_history.append(weights.detach().cpu().numpy())
        
        return combined_guidance
    
    def _compute_weights(self, 
                        stacked_guidance: torch.Tensor, 
                        x_t: ConceptTensor, 
                        t: TimestepTensor, 
                        base_weights: List[float]) -> torch.Tensor:
        """Compute dynamic weights for guidance combination."""
        if self.weighting_method == 'static':
            return torch.tensor(base_weights, device=x_t.device, dtype=x_t.dtype)
        
        elif self.weighting_method == 'dynamic':
            # Weight based on guidance magnitude and alignment
            guidance_norms = torch.norm(stacked_guidance, dim=-1)  # [num_functions, batch_size]
            
            # Normalize weights by magnitude
            weights = guidance_norms / (torch.sum(guidance_norms, dim=0, keepdim=True) + DEFAULT_EPSILON)
            weights = torch.mean(weights, dim=1)  # Average across batch
            
            return weights
        
        elif self.weighting_method == 'adaptive':
            # Adapt weights based on historical performance
            if len(self.weight_history) > 10:
                # Use recent performance to adjust weights
                recent_weights = np.array(self.weight_history[-10:])
                performance_variance = np.var(recent_weights, axis=0)
                
                # Lower variance indicates more stable guidance
                stability_weights = 1.0 / (performance_variance + 1e-6)
                stability_weights = stability_weights / np.sum(stability_weights)
                
                return torch.tensor(stability_weights, device=x_t.device, dtype=x_t.dtype)
            else:
                return torch.tensor(base_weights, device=x_t.device, dtype=x_t.dtype)
        
        elif self.weighting_method == 'learned':
            # Use learned network to compute weights
            # Feature: guidance magnitudes
            guidance_norms = torch.norm(stacked_guidance, dim=-1).mean(dim=1)  # [num_functions]
            weights = self.weight_network(guidance_norms.unsqueeze(0)).squeeze(0)
            
            return weights
        
        else:
            return torch.tensor(base_weights, device=x_t.device, dtype=x_t.dtype)
    
    def _combine_guidance(self, stacked_guidance: torch.Tensor, weights: torch.Tensor) -> GuidanceTensor:
        """Combine guidance vectors using specified conflict resolution."""
        if self.conflict_resolution == 'weighted_average':
            # Standard weighted average
            weights_expanded = weights.view(-1, 1, 1).expand_as(stacked_guidance)
            return torch.sum(stacked_guidance * weights_expanded, dim=0)
        
        elif self.conflict_resolution == 'max_magnitude':
            # Select guidance with maximum magnitude
            guidance_norms = torch.norm(stacked_guidance, dim=-1)  # [num_functions, batch_size]
            max_indices = torch.argmax(guidance_norms, dim=0)  # [batch_size]
            
            batch_size = stacked_guidance.size(1)
            result = torch.zeros_like(stacked_guidance[0])
            
            for b in range(batch_size):
                result[b] = stacked_guidance[max_indices[b], b]
            
            return result
        
        elif self.conflict_resolution == 'consensus':
            # Use only guidance vectors that agree in direction
            num_functions, batch_size, concept_dim = stacked_guidance.shape
            
            # Compute pairwise cosine similarities
            normalized_guidance = F.normalize(stacked_guidance, p=2, dim=-1)
            
            consensus_guidance = torch.zeros(batch_size, concept_dim, device=stacked_guidance.device)
            
            for b in range(batch_size):
                similarities = torch.mm(
                    normalized_guidance[:, b, :], 
                    normalized_guidance[:, b, :].t()
                )
                
                # Find functions with high mutual agreement
                agreement_threshold = 0.5
                agreement_mask = similarities > agreement_threshold
                agreement_count = torch.sum(agreement_mask, dim=1).float()
                
                # Weight by agreement
                agreement_weights = agreement_count / num_functions
                agreement_weights = agreement_weights / (torch.sum(agreement_weights) + DEFAULT_EPSILON)
                
                consensus_guidance[b] = torch.sum(
                    stacked_guidance[:, b, :] * agreement_weights.unsqueeze(-1), 
                    dim=0
                )
            
            return consensus_guidance
        
        else:
            # Default to weighted average
            weights_expanded = weights.view(-1, 1, 1).expand_as(stacked_guidance)
            return torch.sum(stacked_guidance * weights_expanded, dim=0)
    
    def update(self, *args, **kwargs) -> None:
        """Update all contained guidance functions."""
        for func, _ in self.guidance_functions:
            func.update(*args, **kwargs)
    
    def get_detailed_statistics(self) -> Dict[str, Any]:
        """Get detailed statistics for all guidance functions."""
        stats = super().get_statistics()
        
        # Add function-specific statistics
        function_stats = []
        for i, (func, weight) in enumerate(self.guidance_functions):
            func_stats = func.get_statistics()
            func_stats['base_weight'] = weight
            func_stats['function_type'] = type(func).__name__
            function_stats.append(func_stats)
        
        stats['function_statistics'] = function_stats
        
        # Add weight history statistics
        if self.weight_history:
            weight_array = np.array(self.weight_history)
            stats['weight_statistics'] = {
                'mean_weights': np.mean(weight_array, axis=0).tolist(),
                'std_weights': np.std(weight_array, axis=0).tolist(),
                'weight_stability': 1.0 / (np.var(weight_array, axis=0) + 1e-6).tolist()
            }
        
        return stats


class AdaptiveGuidance(GuidanceFunctionBase):
    """
    Adaptive guidance that adjusts its behavior based on diffusion progress and performance.
    
    Uses reinforcement learning principles to optimize guidance effectiveness.
    """
    
    def __init__(self, 
                 base_guidance: GuidanceFunctionBase,
                 config: Optional[GuidanceConfig] = None,
                 adaptation_method: str = 'performance_based',
                 learning_rate: float = 0.01,
                 exploration_rate: float = 0.1,
                 memory_size: int = 1000):
        """
        Initialize adaptive guidance with learning capabilities.
        
        Args:
            base_guidance: Base guidance function to adapt
            config: Guidance configuration
            adaptation_method: Method for adaptation ('performance_based', 'gradient_based', 'rl_based')
            learning_rate: Learning rate for adaptation
            exploration_rate: Exploration rate for strategy discovery
            memory_size: Size of experience memory
        """
        super().__init__(config)
        
        self.base_guidance = base_guidance
        self.adaptation_method = adaptation_method
        self.learning_rate = learning_rate
        self.exploration_rate = exploration_rate
        self.memory_size = memory_size
        
        # Adaptive parameters
        self.strength_multiplier = 1.0
        self.direction_adjustment = 0.0
        
        # Experience memory
        self.experience_memory = []
        self.performance_history = []
        
        # Performance metrics
        self.success_rate = 0.0
        self.average_convergence_time = 0.0
        
        logger.debug(f"Initialized AdaptiveGuidance with method={adaptation_method}")
    
    def _compute_guidance(self, x_t: ConceptTensor, t: TimestepTensor, **kwargs) -> GuidanceTensor:
        """Compute adaptive guidance with learned adjustments."""
        # Get base guidance
        base_direction = self.base_guidance(x_t, t, **kwargs)
        
        # Apply adaptive modifications
        if self.adaptation_method == 'performance_based':
            adapted_guidance = self._performance_based_adaptation(base_direction, x_t, t)
        elif self.adaptation_method == 'gradient_based':
            adapted_guidance = self._gradient_based_adaptation(base_direction, x_t, t)
        elif self.adaptation_method == 'rl_based':
            adapted_guidance = self._rl_based_adaptation(base_direction, x_t, t)
        else:
            adapted_guidance = base_direction
        
        # Store experience
        self._store_experience(x_t, t, base_direction, adapted_guidance)
        
        return adapted_guidance
    
    def _performance_based_adaptation(self, 
                                    base_direction: GuidanceTensor, 
                                    x_t: ConceptTensor, 
                                    t: TimestepTensor) -> GuidanceTensor:
        """Adapt guidance based on historical performance."""
        # Estimate current performance
        current_performance = self._estimate_performance(x_t, t)
        
        # Update success rate (exponential moving average)
        self.success_rate = 0.9 * self.success_rate + 0.1 * current_performance
        
        # Adjust strength based on success rate
        if self.success_rate < 0.5:
            # Increase strength if performance is poor
            self.strength_multiplier = min(3.0, self.strength_multiplier * (1 + self.learning_rate))
        else:
            # Slightly decrease strength if performance is good
            self.strength_multiplier = max(0.1, self.strength_multiplier * (1 - 0.5 * self.learning_rate))
        
        # Apply strength adjustment
        adapted_guidance = base_direction * self.strength_multiplier
        
        # Add exploration noise
        if np.random.random() < self.exploration_rate:
            noise = torch.randn_like(adapted_guidance) * 0.1
            adapted_guidance = adapted_guidance + noise
        
        return adapted_guidance
    
    def _gradient_based_adaptation(self, 
                                 base_direction: GuidanceTensor, 
                                 x_t: ConceptTensor, 
                                 t: TimestepTensor) -> GuidanceTensor:
        """Adapt guidance using gradient information."""
        # Estimate gradient of performance with respect to guidance direction
        if len(self.experience_memory) > 10:
            # Use recent experiences to estimate performance gradient
            recent_experiences = self.experience_memory[-10:]
            
            # Simple finite difference approximation
            performance_changes = []
            guidance_changes = []
            
            for i in range(1, len(recent_experiences)):
                prev_exp = recent_experiences[i-1]
                curr_exp = recent_experiences[i]
                
                perf_change = curr_exp['performance'] - prev_exp['performance']
                guid_change = torch.norm(curr_exp['adapted_guidance'] - prev_exp['adapted_guidance']).item()
                
                if guid_change > 1e-6:  # Avoid division by zero
                    performance_changes.append(perf_change)
                    guidance_changes.append(guid_change)
            
            if performance_changes:
                # Estimate gradient
                avg_gradient = np.mean(np.array(performance_changes) / np.array(guidance_changes))
                
                # Adjust direction based on gradient
                if avg_gradient > 0:
                    # Performance improves with larger guidance changes
                    direction_noise = torch.randn_like(base_direction) * 0.1
                    adapted_guidance = base_direction + self.learning_rate * direction_noise
                else:
                    # Performance degrades with larger guidance changes
                    adapted_guidance = base_direction * (1 - 0.5 * self.learning_rate)
            else:
                adapted_guidance = base_direction
        else:
            adapted_guidance = base_direction
        
        return adapted_guidance
    
    def _rl_based_adaptation(self, 
                           base_direction: GuidanceTensor, 
                           x_t: ConceptTensor, 
                           t: TimestepTensor) -> GuidanceTensor:
        """Adapt guidance using reinforcement learning principles."""
        # State representation
        state_features = self._extract_state_features(x_t, t)
        
        # Action selection (modify guidance direction)
        if np.random.random() < self.exploration_rate:
            # Exploration: random modification
            action = torch.randn(base_direction.shape, device=base_direction.device) * 0.1
        else:
            # Exploitation: use learned policy
            action = self._select_action(state_features, base_direction)
        
        # Apply action to base guidance
        adapted_guidance = base_direction + action
        
        return adapted_guidance
    
    def _extract_state_features(self, x_t: ConceptTensor, t: TimestepTensor) -> torch.Tensor:
        """Extract features representing the current state."""
        batch_size = x_t.size(0)
        
        # Basic features
        features = []
        
        # Timestep features
        t_normalized = t.float() / 1000.0  # Assuming 1000 timesteps
        features.append(t_normalized.unsqueeze(-1))
        
        # Concept features
        concept_norm = torch.norm(x_t, dim=-1, keepdim=True)
        features.append(concept_norm)
        
        # Diversity features (if batch size > 1)
        if batch_size > 1:
            pairwise_distances = torch.cdist(x_t, x_t, p=2)
            avg_distance = torch.mean(pairwise_distances, dim=-1, keepdim=True)
            features.append(avg_distance)
        else:
            features.append(torch.zeros(batch_size, 1, device=x_t.device))
        
        # Historical performance feature
        if self.performance_history:
            recent_performance = torch.tensor(
                self.performance_history[-5:], 
                device=x_t.device, 
                dtype=x_t.dtype
            ).mean().unsqueeze(0).unsqueeze(0).expand(batch_size, 1)
            features.append(recent_performance)
        else:
            features.append(torch.zeros(batch_size, 1, device=x_t.device))
        
        return torch.cat(features, dim=-1)
    
    def _select_action(self, state_features: torch.Tensor, base_direction: GuidanceTensor) -> GuidanceTensor:
        """Select action using learned policy."""
        # Simple policy: linear transformation of state features
        # In practice, this could be a neural network
        
        # For now, use a simple heuristic based on state features
        feature_sum = torch.sum(state_features, dim=-1, keepdim=True)
        action_scale = torch.tanh(feature_sum) * 0.1
        
        action_direction = torch.randn_like(base_direction)
        action = action_scale * action_direction
        
        return action
    
    def _estimate_performance(self, x_t: ConceptTensor, t: TimestepTensor) -> float:
        """Estimate current performance based on various metrics."""
        # Simple performance estimation based on concept stability and progress
        
        # Stability: how much the concept is changing
        if len(self.experience_memory) > 0:
            prev_x = self.experience_memory[-1]['x_t']
            stability = 1.0 / (1.0 + torch.norm(x_t - prev_x).item())
        else:
            stability = 1.0
        
        # Progress: how much we've advanced in the diffusion process
        t_progress = 1.0 - (t.float().mean().item() / 1000.0)
        
        # Combined performance metric
        performance = 0.7 * stability + 0.3 * t_progress
        
        return performance
    
    def _store_experience(self, 
                         x_t: ConceptTensor, 
                         t: TimestepTensor, 
                         base_direction: GuidanceTensor, 
                         adapted_guidance: GuidanceTensor):
        """Store experience for learning."""
        performance = self._estimate_performance(x_t, t)
        
        experience = {
            'x_t': x_t.detach().clone(),
            't': t.clone(),
            'base_direction': base_direction.detach().clone(),
            'adapted_guidance': adapted_guidance.detach().clone(),
            'performance': performance,
            'timestamp': time.time()
        }
        
        self.experience_memory.append(experience)
        self.performance_history.append(performance)
        
        # Limit memory size
        if len(self.experience_memory) > self.memory_size:
            self.experience_memory.pop(0)
        if len(self.performance_history) > self.memory_size:
            self.performance_history.pop(0)
    
    def update(self, *args, **kwargs) -> None:
        """Update base guidance and adaptation parameters."""
        self.base_guidance.update(*args, **kwargs)
        
        # Update adaptation parameters based on recent performance
        if len(self.performance_history) > 50:
            recent_trend = np.mean(self.performance_history[-10:]) - np.mean(self.performance_history[-50:-10])
            
            if recent_trend > 0:
                # Performance is improving, maintain current strategy
                self.exploration_rate = max(0.01, self.exploration_rate * 0.99)
            else:
                # Performance is degrading, increase exploration
                self.exploration_rate = min(0.3, self.exploration_rate * 1.01)


class GoalDirectedDiffusion:
    """
    Advanced implementation of goal-directed diffusion with sophisticated guidance mechanisms.
    
    Integrates multiple guidance types and optimization strategies for robust goal achievement.
    """
    
    def __init__(
        self, 
        diffusion_model,
        thought_space=None,
        device: str = 'cpu',
        config: Optional[DiffusionConfig] = None
    ):
        """
        Initialize goal-directed diffusion with advanced capabilities.
        
        Args:
            diffusion_model: Trained conceptual diffusion model
            thought_space: Thought latent space for concept operations
            device: Device for computations
            config: Diffusion configuration
        """
        self.diffusion_model = diffusion_model
        self.thought_space = thought_space
        self.device = device
        self.config = config or DiffusionConfig()
        
        # Sampling statistics
        self.sampling_history = []
        self.convergence_metrics = []
        
        # Advanced features
        self.use_classifier_free_guidance = True
        self.use_dynamic_thresholding = True
        self.use_progress_monitoring = True
        
        logger.info("Initialized GoalDirectedDiffusion with advanced guidance")
    
    def sample_with_guidance(
        self,
        guidance_fn: GuidanceFunctionBase,
        batch_size: int = 1,
        num_steps: int = None,
        x_T: Optional[ConceptTensor] = None,
        temperature: float = 1.0,
        guidance_scale: float = DEFAULT_GUIDANCE_STRENGTH,
        return_trajectory: bool = False,
        return_metrics: bool = False,
        show_progress: bool = False,
        **kwargs
    ) -> Union[ConceptTensor, Tuple[ConceptTensor, ...]]:
        """
        Sample from diffusion model with sophisticated guidance.
        
        Args:
            guidance_fn: Guidance function for directing diffusion
            batch_size: Number of samples to generate
            num_steps: Number of diffusion steps
            x_T: Optional starting noise
            temperature: Sampling temperature
            guidance_scale: Scale factor for guidance
            return_trajectory: Whether to return full sampling trajectory
            return_metrics: Whether to return convergence metrics
            show_progress: Whether to show progress bar
            **kwargs: Additional arguments
            
        Returns:
            Final samples, optionally with trajectory and metrics
        """
        # Setup parameters
        if num_steps is None:
            num_steps = getattr(self.diffusion_model, 'num_timesteps', 1000) // 2
        
        # Validate diffusion model availability
        if self.diffusion_model is None:
            raise RuntimeError("ConceptualDiffusion model not available")
        
        # Generate starting noise
        if x_T is None:
            concept_dim = getattr(self.diffusion_model, 'concept_dim', 256)
            x_T = torch.randn(batch_size, concept_dim, device=self.device) * temperature
        
        # Setup timestep schedule
        num_timesteps = getattr(self.diffusion_model, 'num_timesteps', 1000)
        if hasattr(self.diffusion_model, 'get_timestep_schedule'):
            timesteps = self.diffusion_model.get_timestep_schedule(num_steps)
        else:
            timesteps = torch.linspace(
                num_timesteps - 1, 0, num_steps, 
                dtype=torch.long, device=self.device
            )
        
        # Initialize tracking
        trajectory = [] if return_trajectory else None
        metrics = {'convergence_scores': [], 'guidance_norms': [], 'step_sizes': []} if return_metrics else None
        
        # Initialize sample
        x_t = x_T.clone()
        
        # Setup progress tracking
        progress_bar = tqdm(timesteps, desc="Guided Diffusion", disable=not show_progress)
        
        # Main sampling loop
        for step_idx, t in enumerate(progress_bar):
            # Expand timestep for batch
            t_batch = t.expand(batch_size)
            
            # Store current state if tracking trajectory
            if return_trajectory:
                trajectory.append(x_t.detach().clone())
            
            # Compute guidance
            with torch.no_grad():
                guidance = guidance_fn(x_t, t_batch, **kwargs)
                
                # Scale guidance
                guidance = guidance * guidance_scale
                
                # Apply dynamic thresholding if enabled
                if self.use_dynamic_thresholding:
                    guidance = self._apply_dynamic_thresholding(guidance, step_idx, num_steps)
            
            # Perform denoising step with guidance
            x_t = self._guided_denoising_step(x_t, t, guidance, **kwargs)
            
            # Apply post-processing
            x_t = self._post_process_sample(x_t, t, **kwargs)
            
            # Compute metrics if tracking
            if return_metrics:
                convergence_score = self._compute_convergence_score(x_t, guidance)
                guidance_norm = torch.norm(guidance).item()
                
                metrics['convergence_scores'].append(convergence_score)
                metrics['guidance_norms'].append(guidance_norm)
                
                if len(trajectory) > 1:
                    step_size = torch.norm(x_t - trajectory[-1]).item()
                    metrics['step_sizes'].append(step_size)
            
            # Update progress bar
            if show_progress:
                progress_bar.set_postfix({
                    'guidance_norm': f"{torch.norm(guidance).item():.3f}",
                    'sample_norm': f"{torch.norm(x_t).item():.3f}"
                })
            
            # Update guidance function
            guidance_fn.update(x_t=x_t, t=t_batch, step=step_idx)
        
        # Final normalization
        x_0 = F.normalize(x_t, p=2, dim=-1)
        
        # Store sampling statistics
        self._store_sampling_statistics(guidance_fn, metrics)
        
        # Prepare return values
        returns = [x_0]
        if return_trajectory:
            returns.append(trajectory)
        if return_metrics:
            returns.append(metrics)
        
        return returns[0] if len(returns) == 1 else tuple(returns)
    
    def _guided_denoising_step(self, 
                              x_t: ConceptTensor, 
                              t: TimestepTensor, 
                              guidance: GuidanceTensor,
                              **kwargs) -> ConceptTensor:
        """Perform a single denoising step with guidance."""
        # Check if model has standard denoising interface
        if hasattr(self.diffusion_model, 'p_sample'):
            # Use model's built-in sampling method with guidance
            x_prev = self.diffusion_model.p_sample(x_t, t, guidance=guidance)
        elif hasattr(self.diffusion_model, 'noise_predictor'):
            # Manual denoising using noise predictor
            t_batch = t.expand(x_t.size(0)) if t.dim() == 0 else t
            
            # Predict noise
            predicted_noise = self.diffusion_model.noise_predictor(x_t, t_batch)
            
            # Apply guidance to noise prediction
            predicted_noise = predicted_noise - guidance
            
            # Compute previous sample using DDPM formula
            if hasattr(self.diffusion_model, 'alphas_cumprod'):
                alphas_cumprod = self.diffusion_model.alphas_cumprod
                if t.dim() == 0:
                    alpha_cumprod_t = alphas_cumprod[t]
                    alpha_cumprod_t_prev = alphas_cumprod[t-1] if t > 0 else torch.tensor(1.0)
                else:
                    alpha_cumprod_t = alphas_cumprod[t]
                    alpha_cumprod_t_prev = alphas_cumprod[torch.clamp(t-1, min=0)]
                
                # DDPM reverse process
                beta_t = 1 - alpha_cumprod_t / alpha_cumprod_t_prev
                x_prev = (x_t - beta_t * predicted_noise / torch.sqrt(1 - alpha_cumprod_t)) / torch.sqrt(1 - beta_t)
            else:
                # Fallback: simple Euler step
                x_prev = x_t - 0.01 * predicted_noise
        else:
            # Fallback: apply guidance directly
            x_prev = x_t + 0.01 * guidance
        
        return x_prev
    
    def _apply_dynamic_thresholding(self, 
                                  guidance: GuidanceTensor, 
                                  step_idx: int, 
                                  total_steps: int) -> GuidanceTensor:
        """Apply dynamic thresholding to guidance based on sampling progress."""
        # Compute adaptive threshold based on sampling progress
        progress = step_idx / total_steps
        
        # Start with higher threshold, decrease over time
        base_threshold = self.config.max_grad_norm
        adaptive_threshold = base_threshold * (1.0 - 0.5 * progress)
        
        # Apply thresholding
        guidance_norm = torch.norm(guidance, dim=-1, keepdim=True)
        guidance = guidance / (guidance_norm + DEFAULT_EPSILON) * torch.clamp(guidance_norm, max=adaptive_threshold)
        
        return guidance
    
    def _post_process_sample(self, 
                           x_t: ConceptTensor, 
                           t: TimestepTensor, 
                           **kwargs) -> ConceptTensor:
        """Apply post-processing to samples."""
        # Clip to valid range if specified
        if self.config.clip_denoised:
            x_t = torch.clamp(x_t, -self.config.clip_range, self.config.clip_range)
        
        # Apply thought space constraints if available
        if self.thought_space is not None and hasattr(self.thought_space, 'constrain_to_manifold'):
            x_t = self.thought_space.constrain_to_manifold(x_t)
        
        return x_t
    
    def _compute_convergence_score(self, 
                                 x_t: ConceptTensor, 
                                 guidance: GuidanceTensor) -> float:
        """Compute convergence score based on guidance alignment and magnitude."""
        # Normalize vectors
        x_t_norm = F.normalize(x_t, p=2, dim=-1)
        guidance_norm_vec = F.normalize(guidance, p=2, dim=-1)
        
        # Compute alignment (cosine similarity)
        alignment = torch.sum(x_t_norm * guidance_norm_vec, dim=-1).mean().item()
        
        # Compute guidance magnitude (normalized)
        guidance_magnitude = torch.norm(guidance, dim=-1).mean().item()
        normalized_magnitude = guidance_magnitude / (guidance_magnitude + 1.0)
        
        # Convergence score: high when guidance is aligned and decreasing in magnitude
        convergence_score = alignment * (1.0 - normalized_magnitude)
        
        return convergence_score
    
    def _store_sampling_statistics(self, 
                                 guidance_fn: GuidanceFunctionBase, 
                                 metrics: Optional[Dict[str, Any]]):
        """Store statistics for analysis and optimization."""
        stats = {
            'timestamp': time.time(),
            'guidance_type': type(guidance_fn).__name__,
            'guidance_stats': guidance_fn.get_statistics(),
            'convergence_metrics': metrics
        }
        
        self.sampling_history.append(stats)
        
        # Limit history size
        if len(self.sampling_history) > 100:
            self.sampling_history.pop(0)
    
    def multi_objective_sampling(
        self,
        objectives: List[Callable[[ConceptTensor], torch.Tensor]],
        objective_weights: Optional[List[float]] = None,
        batch_size: int = 1,
        num_steps: int = None,
        pareto_optimization: bool = False,
        **kwargs
    ) -> ConceptTensor:
        """
        Sample concepts optimizing multiple objectives simultaneously.
        
        Args:
            objectives: List of objective functions to optimize
            objective_weights: Optional weights for objectives
            batch_size: Number of samples to generate
            num_steps: Number of diffusion steps
            pareto_optimization: Whether to use Pareto optimization
            **kwargs: Additional sampling arguments
            
        Returns:
            Samples optimizing multiple objectives
        """
        if objective_weights is None:
            objective_weights = [1.0] * len(objectives)
        
        if pareto_optimization:
            return self._pareto_multi_objective_sampling(objectives, batch_size, num_steps, **kwargs)
        else:
            return self._weighted_multi_objective_sampling(objectives, objective_weights, batch_size, num_steps, **kwargs)
    
    def _weighted_multi_objective_sampling(self,
                                         objectives: List[Callable],
                                         weights: List[float],
                                         batch_size: int,
                                         num_steps: int,
                                         **kwargs) -> ConceptTensor:
        """Sample using weighted combination of objectives."""
        def combined_objective(x):
            scores = []
            for obj, weight in zip(objectives, weights):
                score = obj(x)
                if score.dim() > 1:
                    score = torch.sum(score, dim=-1)
                scores.append(weight * score)
            return torch.stack(scores, dim=-1).sum(dim=-1)
        
        # Create constraint guidance from combined objective
        guidance = ConstraintGuidance(
            constraint_fn=combined_objective,
            constraint_type='satisfaction'
        )
        
        return self.sample_with_guidance(
            guidance_fn=guidance,
            batch_size=batch_size,
            num_steps=num_steps,
            **kwargs
        )
    
    def _pareto_multi_objective_sampling(self,
                                       objectives: List[Callable],
                                       batch_size: int,
                                       num_steps: int,
                                       **kwargs) -> ConceptTensor:
        """Sample using Pareto optimization principles."""
        # Generate multiple samples for each objective
        samples_per_objective = max(1, batch_size // len(objectives))
        all_samples = []
        
        for obj in objectives:
            guidance = ConstraintGuidance(
                constraint_fn=obj,
                constraint_type='satisfaction'
            )
            
            samples = self.sample_with_guidance(
                guidance_fn=guidance,
                batch_size=samples_per_objective,
                num_steps=num_steps,
                **kwargs
            )
            all_samples.append(samples)
        
        # Combine samples
        combined_samples = torch.cat(all_samples, dim=0)
        
        # Select Pareto-optimal samples
        pareto_samples = self._select_pareto_optimal(combined_samples, objectives)
        
        # Pad or truncate to desired batch size
        if pareto_samples.size(0) < batch_size:
            # Pad with random selection from pareto samples
            indices = torch.randint(0, pareto_samples.size(0), (batch_size - pareto_samples.size(0),))
            additional_samples = pareto_samples[indices]
            pareto_samples = torch.cat([pareto_samples, additional_samples], dim=0)
        else:
            pareto_samples = pareto_samples[:batch_size]
        
        return pareto_samples
    
    def _select_pareto_optimal(self, 
                             samples: ConceptTensor, 
                             objectives: List[Callable]) -> ConceptTensor:
        """Select Pareto-optimal samples from a set."""
        num_samples = samples.size(0)
        
        # Evaluate all objectives for all samples
        objective_values = []
        for obj in objectives:
            values = obj(samples)
            if values.dim() > 1:
                values = torch.sum(values, dim=-1)
            objective_values.append(values)
        
        objective_matrix = torch.stack(objective_values, dim=-1)  # [num_samples, num_objectives]
        
        # Find Pareto-optimal samples
        pareto_mask = torch.zeros(num_samples, dtype=torch.bool)
        
        for i in range(num_samples):
            is_dominated = False
            for j in range(num_samples):
                if i != j:
                    # Check if sample j dominates sample i
                    # (j is better in all objectives)
                    if torch.all(objective_matrix[j] >= objective_matrix[i]) and \
                       torch.any(objective_matrix[j] > objective_matrix[i]):
                        is_dominated = True
                        break
            
            if not is_dominated:
                pareto_mask[i] = True
        
        return samples[pareto_mask]


# Utility functions for reverse diffusion reasoning

def constraint_guided_sampling(
    diffusion_model,
    constraint_fn: Callable[[ConceptTensor], torch.Tensor],
    batch_size: int = 10,
    guidance_scale: float = 1.0,
    temperature: float = 1.0,
    num_steps: int = None,
    optimization_method: str = 'gradient',
    return_best_only: bool = False,
    starting_point: Optional[ConceptTensor] = None,
    show_progress: bool = False,
    device: str = 'cpu',
    **kwargs
) -> Union[ConceptTensor, Tuple[ConceptTensor, torch.Tensor]]:
    """
    Sample concepts that satisfy specified constraints using advanced guidance.
    
    Args:
        diffusion_model: Trained diffusion model
        constraint_fn: Function computing constraint satisfaction (0-1)
        batch_size: Number of samples to generate
        guidance_scale: Scale factor for constraint guidance
        temperature: Sampling temperature
        num_steps: Number of diffusion steps
        optimization_method: Optimization method for constraint guidance
        return_best_only: Whether to return only the best sample
        starting_point: Optional starting point
        show_progress: Whether to show progress bar
        device: Device for computations
        **kwargs: Additional arguments
        
    Returns:
        Constrained samples, optionally with satisfaction scores
    """
    if diffusion_model is None:
        raise RuntimeError("Diffusion model not available")
    
    # Create advanced constraint guidance
    config = GuidanceConfig(strength=guidance_scale)
    guidance = ConstraintGuidance(
        constraint_fn=constraint_fn,
        config=config,
        optimization_method=optimization_method,
        constraint_type='satisfaction'
    )
    
    # Create goal-directed diffusion sampler
    goal_diffusion = GoalDirectedDiffusion(
        diffusion_model=diffusion_model,
        device=device
    )
    
    # Sample with guidance
    samples = goal_diffusion.sample_with_guidance(
        guidance_fn=guidance,
        batch_size=batch_size,
        num_steps=num_steps,
        x_T=starting_point,
        temperature=temperature,
        show_progress=show_progress,
        **kwargs
    )
    
    # Evaluate final constraint satisfaction
    with torch.no_grad():
        scores = constraint_fn(samples)
        if scores.dim() > 1:
            scores = torch.sum(scores, dim=-1)
    
    if return_best_only:
        best_idx = torch.argmax(scores)
        return samples[best_idx:best_idx+1], scores[best_idx].item()
    else:
        return samples, scores


def diffusion_bridge(
    diffusion_model,
    start_point: ConceptTensor,
    end_point: ConceptTensor,
    num_samples: int = 1,
    num_steps: int = None,
    bridge_type: str = 'linear',
    noise_schedule: Optional[Callable[[float], float]] = None,
    guidance_scale: float = 3.0,
    return_trajectory: bool = False,
    show_progress: bool = False,
    device: str = 'cpu',
    **kwargs
) -> Union[ConceptTensor, Tuple[ConceptTensor, List[ConceptTensor]]]:
    """
    Create sophisticated diffusion bridges between concept points.
    
    Args:
        diffusion_model: Trained diffusion model
        start_point: Starting concept tensor
        end_point: Ending concept tensor
        num_samples: Number of bridge samples
        num_steps: Number of diffusion steps
        bridge_type: Type of bridge ('linear', 'geodesic', 'optimized')
        noise_schedule: Optional noise scheduling function
        guidance_scale: Scale factor for guidance
        return_trajectory: Whether to return intermediate steps
        show_progress: Whether to show progress bar
        device: Device for computations
        **kwargs: Additional arguments
        
    Returns:
        Bridge samples, optionally with trajectory
    """
    if diffusion_model is None:
        raise RuntimeError("Diffusion model not available")
    
    # Setup parameters
    if num_steps is None:
        num_steps = getattr(diffusion_model, 'num_timesteps', 1000) // 2
    
    # Ensure points are on device and normalized
    start_point = F.normalize(start_point.to(device), p=2, dim=-1)
    end_point = F.normalize(end_point.to(device), p=2, dim=-1)
    
    # Create bridge guidance based on type
    if bridge_type == 'linear':
        guidance = TargetGuidance(
            target=end_point,
            similarity_type='euclidean'
        )
    elif bridge_type == 'geodesic':
        guidance = TargetGuidance(
            target=end_point,
            similarity_type='cosine'
        )
    elif bridge_type == 'optimized':
        # Create path with intermediate points
        num_path_points = 5
        path_points = []
        
        for i in range(num_path_points):
            alpha = i / (num_path_points - 1)
            
            if torch.dot(start_point, end_point) > 0.9:  # Nearly parallel
                # Use linear interpolation
                point = (1 - alpha) * start_point + alpha * end_point
            else:
                # Use spherical interpolation
                omega = torch.acos(torch.dot(start_point, end_point).clamp(-1, 1))
                sin_omega = torch.sin(omega)
                point = (torch.sin((1 - alpha) * omega) / sin_omega) * start_point + \
                       (torch.sin(alpha * omega) / sin_omega) * end_point
            
            path_points.append(F.normalize(point, p=2, dim=-1))
        
        guidance = PathGuidance(
            path_points=path_points,
            interpolation_method='spherical'
        )
    else:
        raise ValueError(f"Unknown bridge type: {bridge_type}")
    
    # Setup starting points with controlled noise
    if noise_schedule is None:
        noise_schedule = lambda p: 0.3 * (1.0 - p)  # Decrease noise over time
    
    concept_dim = start_point.shape[-1]
    noise = torch.randn(num_samples, concept_dim, device=device)
    x_T = start_point.unsqueeze(0).expand(num_samples, -1) + noise_schedule(0.0) * noise
    x_T = F.normalize(x_T, p=2, dim=-1)
    
    # Create goal-directed diffusion sampler
    goal_diffusion = GoalDirectedDiffusion(
        diffusion_model=diffusion_model,
        device=device
    )
    
    # Sample bridge
    result = goal_diffusion.sample_with_guidance(
        guidance_fn=guidance,
        batch_size=num_samples,
        num_steps=num_steps,
        x_T=x_T,
        guidance_scale=guidance_scale,
        return_trajectory=return_trajectory,
        show_progress=show_progress,
        **kwargs
    )
    
    return result


def reverse_process_with_guidance(
    diffusion_model,
    guidance_fn: GuidanceFunctionBase,
    x_T: ConceptTensor,
    num_steps: int = None,
    clip_samples: bool = True,
    intermediate_freq: int = 0,
    return_all: bool = False,
    show_progress: bool = False,
    adaptive_guidance: bool = True,
    convergence_threshold: float = 1e-4,
    **kwargs
) -> Union[ConceptTensor, Tuple[ConceptTensor, List[ConceptTensor]]]:
    """
    Run the reverse diffusion process with sophisticated guidance and monitoring.
    
    Args:
        diffusion_model: Trained diffusion model
        guidance_fn: Guidance function to direct the diffusion
        x_T: Starting noise [batch_size, concept_dim]
        num_steps: Number of diffusion steps
        clip_samples: Whether to clip intermediate samples
        intermediate_freq: Frequency to save intermediate samples (0 = no intermediates)
        return_all: Whether to return all intermediate steps
        show_progress: Whether to show progress bar
        adaptive_guidance: Whether to use adaptive guidance strength
        convergence_threshold: Threshold for early stopping
        **kwargs: Additional arguments
        
    Returns:
        Final sample tensor, optionally with intermediate samples
    """
    if diffusion_model is None:
        raise RuntimeError("Diffusion model not available")
    
    # Setup parameters
    if num_steps is None:
        num_steps = getattr(diffusion_model, 'num_timesteps', 1000)
    
    device = x_T.device
    batch_size = x_T.shape[0]
    
    # Initialize sample and tracking
    x_t = x_T.clone()
    intermediates = []
    convergence_history = []
    
    # Create timestep schedule
    num_timesteps = getattr(diffusion_model, 'num_timesteps', 1000)
    timesteps = torch.linspace(
        num_timesteps - 1, 0, num_steps, 
        dtype=torch.long, device=device
    )
    
    # Setup progress tracking
    progress_bar = tqdm(timesteps, desc="Reverse Diffusion", disable=not show_progress)
    
    # Previous sample for convergence checking
    x_prev = None
    
    # Main reverse diffusion loop
    for i, t in enumerate(progress_bar):
        t_batch = t.expand(batch_size)
        
        # Store intermediate if requested
        if return_all or (intermediate_freq > 0 and i % intermediate_freq == 0):
            intermediates.append(x_t.detach().clone())
        
        # Compute guidance
        with torch.no_grad():
            guidance = guidance_fn(x_t, t_batch, step=i, **kwargs)
            
            # Adaptive guidance scaling
            if adaptive_guidance:
                progress = i / num_steps
                adaptive_scale = 1.0 + 2.0 * (1.0 - progress)  # Stronger guidance early
                guidance = guidance * adaptive_scale
        
        # Perform denoising step
        try:
            if hasattr(diffusion_model, 'p_sample_guided'):
                x_t = diffusion_model.p_sample_guided(x_t, t_batch, guidance=guidance)
            elif hasattr(diffusion_model, 'noise_predictor'):
                # Manual guided denoising
                predicted_noise = diffusion_model.noise_predictor(x_t, t_batch)
                guided_noise = predicted_noise - guidance
                
                # DDPM reverse step
                if hasattr(diffusion_model, 'q_posterior_mean_variance'):
                    x_t = diffusion_model.q_posterior_mean_variance(x_t, guided_noise, t)
                else:
                    # Fallback Euler step
                    step_size = 1.0 / num_steps
                    x_t = x_t - step_size * guided_noise
            else:
                # Direct guidance application
                step_size = 0.01 * (1.0 - i / num_steps)  # Decreasing step size
                x_t = x_t + step_size * guidance
        
        except Exception as e:
            logger.warning(f"Denoising step failed at t={t}: {e}, using fallback")
            step_size = 0.01
            x_t = x_t + step_size * guidance
        
        # Apply clipping if enabled
        if clip_samples:
            clip_range = getattr(diffusion_model, 'clip_range', DEFAULT_CLIP_RANGE)
            x_t = torch.clamp(x_t, -clip_range, clip_range)
        
        # Check convergence
        if x_prev is not None and convergence_threshold > 0:
            convergence_metric = torch.norm(x_t - x_prev, dim=-1).mean().item()
            convergence_history.append(convergence_metric)
            
            if convergence_metric < convergence_threshold:
                logger.info(f"Early convergence at step {i}, metric: {convergence_metric}")
                break
        
        x_prev = x_t.clone()
        
        # Update guidance function
        guidance_fn.update(x_t=x_t, t=t_batch, step=i)
        
        # Update progress bar
        if show_progress:
            guidance_norm = torch.norm(guidance).item()
            sample_norm = torch.norm(x_t).item()
            progress_bar.set_postfix({
                'guidance': f"{guidance_norm:.3f}",
                'sample': f"{sample_norm:.3f}",
                'conv': f"{convergence_history[-1]:.4f}" if convergence_history else "N/A"
            })
    
    # Final normalization
    x_0 = F.normalize(x_t, p=2, dim=-1)
    
    # Return results
    if return_all:
        return x_0, intermediates
    else:
        return x_0


class ReverseDiffusionReasoning:
    """
    Advanced implementation of reasoning from desired outcomes back to potential solutions.
    
    Integrates sophisticated guidance mechanisms, multi-objective optimization,
    and advanced reasoning strategies for complex problem solving.
    """
    
    def __init__(
        self, 
        diffusion_model,
        thought_space=None,
        device: str = 'cpu',
        config: Optional[DiffusionConfig] = None
    ):
        """
        Initialize reverse diffusion reasoning with advanced capabilities.
        
        Args:
            diffusion_model: Trained conceptual diffusion model
            thought_space: Thought latent space for concept operations
            device: Device for computations
            config: Diffusion configuration
        """
        self.diffusion_model = diffusion_model
        self.thought_space = thought_space
        self.device = device
        self.config = config or DiffusionConfig()
        
        # Initialize goal-directed diffusion
        self.goal_diffusion = GoalDirectedDiffusion(
            diffusion_model=diffusion_model,
            thought_space=thought_space,
            device=device,
            config=config
        )
        
        # Reasoning history and analytics
        self.reasoning_history = []
        self.performance_metrics = {
            'success_rate': 0.0,
            'average_steps': 0.0,
            'convergence_rate': 0.0
        }
        
        # Advanced reasoning modes
        self.reasoning_modes = {
            'exploratory': {'exploration_weight': 0.8, 'exploitation_weight': 0.2},
            'focused': {'exploration_weight': 0.2, 'exploitation_weight': 0.8},
            'balanced': {'exploration_weight': 0.5, 'exploitation_weight': 0.5},
            'adaptive': {'exploration_weight': 'adaptive', 'exploitation_weight': 'adaptive'}
        }
        
        self.current_mode = 'balanced'
        
        logger.info("Initialized ReverseDiffusionReasoning with advanced capabilities")
    
    def goal_directed_reasoning(
        self, 
        source_concepts: Optional[List[ConceptTensor]] = None,
        goal_concept: Optional[ConceptTensor] = None,
        constraints: Optional[List[Callable]] = None,
        guidance_strength: float = DEFAULT_GUIDANCE_STRENGTH,
        num_reasoning_paths: int = DEFAULT_NUM_PATHS,
        num_steps: int = None,
        reasoning_mode: str = 'balanced',
        optimization_objective: str = 'goal_achievement',
        return_intermediate: bool = False,
        return_metrics: bool = False,
        show_progress: bool = False,
        **kwargs
    ) -> Union[ConceptTensor, Tuple[ConceptTensor, ...]]:
        """
        Generate sophisticated reasoning paths from sources to goals.
        
        Args:
            source_concepts: Optional starting concepts
            goal_concept: Target concept to reason toward
            constraints: Optional constraint functions
            guidance_strength: Strength of guidance
            num_reasoning_paths: Number of reasoning paths to explore
            num_steps: Number of diffusion steps
            reasoning_mode: Mode of reasoning ('exploratory', 'focused', 'balanced', 'adaptive')
            optimization_objective: Objective to optimize
            return_intermediate: Whether to return intermediate steps
            return_metrics: Whether to return detailed metrics
            show_progress: Whether to show progress
            **kwargs: Additional arguments
            
        Returns:
            Reasoning results with optional intermediate steps and metrics
        """
        start_time = time.time()
        
        # Validate inputs
        if goal_concept is None and not constraints:
            raise ValueError("Must provide either goal_concept or constraints")
        
        # Setup reasoning mode
        self.current_mode = reasoning_mode
        mode_params = self.reasoning_modes.get(reasoning_mode, self.reasoning_modes['balanced'])
        
        # Setup number of steps
        if num_steps is None:
            num_timesteps = getattr(self.diffusion_model, 'num_timesteps', 1000)
            num_steps = num_timesteps // 2
        
        # Create composite guidance function
        guidance_functions = []
        
        # Add goal-directed guidance if goal provided
        if goal_concept is not None:
            goal_guidance = TargetGuidance(
                target=goal_concept.to(self.device),
                config=GuidanceConfig(
                    strength=guidance_strength,
                    schedule='cosine',
                    normalize_grads=True
                ),
                similarity_type='mixed',
                adaptive_strength=True
            )
            
            weight = mode_params['exploitation_weight']
            if weight == 'adaptive':
                weight = self._compute_adaptive_weight('exploitation')
            
            guidance_functions.append((goal_guidance, weight))
        
        # Add constraint-based guidance
        if constraints:
            for i, constraint in enumerate(constraints):
                constraint_guidance = ConstraintGuidance(
                    constraint_fn=constraint,
                    config=GuidanceConfig(
                        strength=guidance_strength * 0.8,  # Slightly lower for constraints
                        schedule='linear',
                        normalize_grads=True
                    ),
                    optimization_method='gradient',
                    constraint_type='satisfaction'
                )
                guidance_functions.append((constraint_guidance, 0.5))
        
        # Add exploration guidance if in exploratory mode
        exploration_weight = mode_params['exploration_weight']
        if exploration_weight == 'adaptive':
            exploration_weight = self._compute_adaptive_weight('exploration')
        
        if exploration_weight > 0.1:
            exploration_guidance = self._create_exploration_guidance(
                source_concepts, 
                exploration_weight
            )
            guidance_functions.append((exploration_guidance, exploration_weight))
        
        # Create composite guidance
        if len(guidance_functions) == 1:
            composite_guidance = guidance_functions[0][0]
        else:
            composite_guidance = CompositeGuidance(
                guidance_functions=guidance_functions,
                config=GuidanceConfig(strength=1.0),
                weighting_method='adaptive',
                conflict_resolution='consensus'
            )
        
        # Determine starting points
        if source_concepts is None:
            # Start from random noise
            concept_dim = getattr(self.diffusion_model, 'concept_dim', 256)
            x_T = torch.randn(num_reasoning_paths, concept_dim, device=self.device)
        else:
            # Start from source concepts with variations
            if len(source_concepts) == 1:
                # Expand single source to multiple paths
                base_concept = source_concepts[0]
                x_T = base_concept.unsqueeze(0).expand(num_reasoning_paths, -1).clone()
                
                # Add controlled noise for exploration
                noise_scale = 0.1 if reasoning_mode == 'focused' else 0.3
                noise = torch.randn_like(x_T) * noise_scale
                x_T = x_T + noise
                x_T = F.normalize(x_T, p=2, dim=-1)
            else:
                # Use multiple source concepts
                x_T = torch.stack(source_concepts[:num_reasoning_paths], dim=0)
                if x_T.size(0) < num_reasoning_paths:
                    # Repeat and add noise to fill remaining paths
                    remaining = num_reasoning_paths - x_T.size(0)
                    repeated = x_T[:remaining]
                    noise = torch.randn_like(repeated) * 0.2
                    repeated = repeated + noise
                    x_T = torch.cat([x_T, repeated], dim=0)
        
        # Initialize tracking
        intermediate_steps = [] if return_intermediate else None
        reasoning_metrics = {
            'convergence_scores': [],
            'guidance_norms': [],
            'diversity_scores': [],
            'constraint_satisfaction': []
        } if return_metrics else None
        
        # Perform guided reasoning
        try:
            results = self.goal_diffusion.sample_with_guidance(
                guidance_fn=composite_guidance,
                batch_size=num_reasoning_paths,
                num_steps=num_steps,
                x_T=x_T,
                return_trajectory=return_intermediate,
                return_metrics=return_metrics,
                show_progress=show_progress,
                **kwargs
            )
            
            if return_intermediate or return_metrics:
                if return_intermediate and return_metrics:
                    reasoning_results, intermediate_steps, detailed_metrics = results
                    reasoning_metrics.update(detailed_metrics)
                elif return_intermediate:
                    reasoning_results, intermediate_steps = results
                else:
                    reasoning_results, detailed_metrics = results
                    reasoning_metrics.update(detailed_metrics)
            else:
                reasoning_results = results
        
        except Exception as e:
            logger.error(f"Reasoning failed: {e}")
            # Fallback to simple goal-directed sampling
            if goal_concept is not None:
                simple_guidance = TargetGuidance(target=goal_concept.to(self.device))
                reasoning_results = self.goal_diffusion.sample_with_guidance(
                    guidance_fn=simple_guidance,
                    batch_size=num_reasoning_paths,
                    num_steps=num_steps // 2,
                    x_T=x_T,
                    show_progress=show_progress
                )
            else:
                reasoning_results = F.normalize(x_T, p=2, dim=-1)
        
        # Post-process results
        reasoning_results = self._post_process_reasoning_results(
            reasoning_results, 
            goal_concept, 
            constraints
        )
        
        # Compute additional metrics if requested
        if return_metrics:
            end_time = time.time()
            reasoning_metrics.update({
                'reasoning_time': end_time - start_time,
                'num_paths': num_reasoning_paths,
                'mode': reasoning_mode,
                'final_diversity': self._compute_diversity_score(reasoning_results),
                'goal_alignment': self._compute_goal_alignment(reasoning_results, goal_concept) if goal_concept is not None else None
            })
            
            # Evaluate constraint satisfaction
            if constraints:
                constraint_scores = []
                for constraint in constraints:
                    scores = constraint(reasoning_results)
                    if scores.dim() > 1:
                        scores = torch.mean(scores, dim=-1)
                    constraint_scores.append(scores.mean().item())
                reasoning_metrics['final_constraint_satisfaction'] = constraint_scores
        
        # Store reasoning history
        self._store_reasoning_experience(
            source_concepts=source_concepts,
            goal_concept=goal_concept,
            results=reasoning_results,
            metrics=reasoning_metrics,
            mode=reasoning_mode
        )
        
        # Prepare return values
        returns = [reasoning_results]
        if return_intermediate:
            returns.append(intermediate_steps)
        if return_metrics:
            returns.append(reasoning_metrics)
        
        return returns[0] if len(returns) == 1 else tuple(returns)
    
    def _create_exploration_guidance(self, 
                                   source_concepts: Optional[List[ConceptTensor]], 
                                   exploration_weight: float) -> GuidanceFunctionBase:
        """Create guidance for exploration and diversity."""
        def exploration_energy(x):
            """Energy function that encourages exploration away from sources."""
            if source_concepts is None:
                # Encourage diversity within batch
                if x.size(0) > 1:
                    pairwise_distances = torch.cdist(x, x, p=2)
                    # Remove diagonal (self-distances)
                    mask = ~torch.eye(x.size(0), dtype=torch.bool, device=x.device)
                    distances = pairwise_distances[mask]
                    return -torch.mean(distances)  # Negative for repulsion
                else:
                    return torch.tensor(0.0, device=x.device)
            else:
                # Encourage movement away from source concepts
                source_stack = torch.stack(source_concepts, dim=0)
                source_center = torch.mean(source_stack, dim=0)
                
                distances_from_source = torch.norm(x - source_center.unsqueeze(0), dim=-1)
                return -torch.mean(distances_from_source)  # Negative for repulsion
        
        return EnergyGuidance(
            energy_fn=exploration_energy,
            config=GuidanceConfig(strength=exploration_weight),
            potential_type='repulsive',
            temperature=1.0
        )
    
    def _compute_adaptive_weight(self, weight_type: str) -> float:
        """Compute adaptive weights based on reasoning history."""
        if not self.reasoning_history:
            return 0.5  # Default
        
        # Analyze recent performance
        recent_history = self.reasoning_history[-10:]
        
        if weight_type == 'exploration':
            # Increase exploration if recent reasoning has low diversity
            diversity_scores = [h.get('diversity_score', 0.5) for h in recent_history]
            avg_diversity = np.mean(diversity_scores)
            
            # Inverse relationship: low diversity -> high exploration
            exploration_weight = 1.0 - avg_diversity
            return np.clip(exploration_weight, 0.1, 0.8)
        
        elif weight_type == 'exploitation':
            # Increase exploitation if goal alignment is improving
            alignment_scores = [h.get('goal_alignment', 0.5) for h in recent_history]
            if len(alignment_scores) > 5:
                recent_trend = np.mean(alignment_scores[-3:]) - np.mean(alignment_scores[-6:-3])
                exploitation_weight = 0.5 + 0.3 * np.tanh(recent_trend * 5)
                return np.clip(exploitation_weight, 0.2, 0.9)
        
        return 0.5  # Default
    
    def _post_process_reasoning_results(self, 
                                      results: ConceptTensor, 
                                      goal_concept: Optional[ConceptTensor], 
                                      constraints: Optional[List[Callable]]) -> ConceptTensor:
        """Post-process reasoning results for optimization."""
        # Ensure normalization
        results = F.normalize(results, p=2, dim=-1)
        
        # Apply thought space constraints if available
        if self.thought_space is not None:
            if hasattr(self.thought_space, 'project_to_manifold'):
                results = self.thought_space.project_to_manifold(results)
            elif hasattr(self.thought_space, 'constrain_concepts'):
                results = self.thought_space.constrain_concepts(results)
        
        # Rank results by quality if goal or constraints provided
        if goal_concept is not None or constraints:
            quality_scores = self._compute_result_quality(results, goal_concept, constraints)
            
            # Sort by quality (descending)
            sorted_indices = torch.argsort(quality_scores, descending=True)
            results = results[sorted_indices]
        
        return results
    
    def _compute_result_quality(self, 
                              results: ConceptTensor, 
                              goal_concept: Optional[ConceptTensor], 
                              constraints: Optional[List[Callable]]) -> torch.Tensor:
        """Compute quality scores for reasoning results."""
        quality_scores = torch.zeros(results.size(0), device=results.device)
        
        # Goal alignment component
        if goal_concept is not None:
            goal_alignment = F.cosine_similarity(results, goal_concept.unsqueeze(0), dim=-1)
            quality_scores += 0.6 * goal_alignment
        
        # Constraint satisfaction component
        if constraints:
            constraint_satisfaction = torch.zeros_like(quality_scores)
            for constraint in constraints:
                scores = constraint(results)
                if scores.dim() > 1:
                    scores = torch.mean(scores, dim=-1)
                constraint_satisfaction += scores
            
            constraint_satisfaction /= len(constraints)
            quality_scores += 0.4 * constraint_satisfaction
        
        return quality_scores
    
    def _compute_diversity_score(self, results: ConceptTensor) -> float:
        """Compute diversity score for a set of results."""
        if results.size(0) <= 1:
            return 0.0
        
        # Compute pairwise distances
        pairwise_distances = torch.cdist(results, results, p=2)
        
        # Remove diagonal and compute mean distance
        mask = ~torch.eye(results.size(0), dtype=torch.bool, device=results.device)
        distances = pairwise_distances[mask]
        
        return torch.mean(distances).item()
    
    def _compute_goal_alignment(self, 
                              results: ConceptTensor, 
                              goal_concept: ConceptTensor) -> float:
        """Compute average goal alignment for results."""
        if goal_concept is None:
            return 0.0
        
        alignments = F.cosine_similarity(results, goal_concept.unsqueeze(0), dim=-1)
        return torch.mean(alignments).item()
    
    def analogy_completion(
        self,
        a: ConceptTensor,
        b: ConceptTensor,
        c: ConceptTensor,
        num_samples: int = 5,
        num_steps: int = None,
        guidance_strength: float = DEFAULT_GUIDANCE_STRENGTH,
        analogy_type: str = 'vector_arithmetic',
        exploration_scale: float = 0.1,
        return_confidence: bool = False,
        show_progress: bool = False
    ) -> Union[ConceptTensor, Tuple[ConceptTensor, torch.Tensor]]:
        """
        Complete analogies using sophisticated reasoning: "A is to B as C is to ?"
        
        Args:
            a: First concept in analogy
            b: Second concept in analogy  
            c: Third concept in analogy
            num_samples: Number of completion candidates
            num_steps: Number of diffusion steps
            guidance_strength: Guidance strength
            analogy_type: Type of analogy reasoning
            exploration_scale: Scale for exploration around target
            return_confidence: Whether to return confidence scores
            show_progress: Whether to show progress
            
        Returns:
            Analogy completions, optionally with confidence scores
        """
        # Normalize inputs
        a = F.normalize(a.to(self.device), p=2, dim=-1)
        b = F.normalize(b.to(self.device), p=2, dim=-1)
        c = F.normalize(c.to(self.device), p=2, dim=-1)
        
        if analogy_type == 'vector_arithmetic':
            # Simple vector arithmetic: D = C + (B - A)
            relation_vector = b - a
            target_d = c + relation_vector
            target_d = F.normalize(target_d, p=2, dim=-1)
            
        elif analogy_type == 'proportional':
            # Proportional relationship: D/C = B/A
            # Implemented as: D = C * (B·C) / (A·C) normalized
            a_c_sim = torch.dot(a, c).clamp(min=1e-6)
            b_c_sim = torch.dot(b, c)
            
            scaling_factor = b_c_sim / a_c_sim
            target_d = c * scaling_factor
            target_d = F.normalize(target_d, p=2, dim=-1)
            
        elif analogy_type == 'relational':
            # Learn the transformation from A to B, apply to C
            # Use the thought space if available
            if self.thought_space is not None and hasattr(self.thought_space, 'compute_transformation'):
                transformation = self.thought_space.compute_transformation(a, b)
                target_d = self.thought_space.apply_transformation(c, transformation)
            else:
                # Fallback to vector arithmetic
                relation_vector = b - a
                target_d = c + relation_vector
                target_d = F.normalize(target_d, p=2, dim=-1)
        else:
            raise ValueError(f"Unknown analogy type: {analogy_type}")
        
        # Create starting points with exploration
        x_T = target_d.unsqueeze(0).expand(num_samples, -1).clone()
        if exploration_scale > 0:
            noise = torch.randn_like(x_T) * exploration_scale
            x_T = x_T + noise
            x_T = F.normalize(x_T, p=2, dim=-1)
        
        # Create guidance toward analogy target
        guidance = TargetGuidance(
            target=target_d,
            config=GuidanceConfig(
                strength=guidance_strength,
                schedule='cosine'
            ),
            similarity_type='cosine',
            adaptive_strength=True
        )
        
        # Setup number of steps
        if num_steps is None:
            num_steps = getattr(self.diffusion_model, 'num_timesteps', 1000) // 3
        
        # Generate analogy completions
        completions = self.goal_diffusion.sample_with_guidance(
            guidance_fn=guidance,
            batch_size=num_samples,
            num_steps=num_steps,
            x_T=x_T,
            show_progress=show_progress
        )
        
        # Compute confidence scores if requested
        if return_confidence:
            confidence_scores = self._compute_analogy_confidence(a, b, c, completions)
            return completions, confidence_scores
        else:
            return completions
    
    def _compute_analogy_confidence(self, 
                                  a: ConceptTensor, 
                                  b: ConceptTensor, 
                                  c: ConceptTensor, 
                                  d_candidates: ConceptTensor) -> torch.Tensor:
        """Compute confidence scores for analogy completions."""
        confidence_scores = torch.zeros(d_candidates.size(0), device=d_candidates.device)
        
        for i, d in enumerate(d_candidates):
            # Check consistency of relationships
            # Measure ||(B-A) - (D-C)||
            relation_ab = b - a
            relation_cd = d - c
            relation_consistency = 1.0 / (1.0 + torch.norm(relation_ab - relation_cd).item())
            
            # Check proportional consistency
            # Measure similarity of ratios
            ab_magnitude = torch.norm(relation_ab).item()
            cd_magnitude = torch.norm(relation_cd).item()
            magnitude_ratio = min(ab_magnitude, cd_magnitude) / max(ab_magnitude, cd_magnitude)
            
            # Directional consistency
            if ab_magnitude > 1e-6 and cd_magnitude > 1e-6:
                direction_similarity = F.cosine_similarity(
                    relation_ab.unsqueeze(0), 
                    relation_cd.unsqueeze(0), 
                    dim=-1
                ).item()
            else:
                direction_similarity = 0.0
            
            # Combined confidence
            confidence = 0.4 * relation_consistency + 0.3 * magnitude_ratio + 0.3 * direction_similarity
            confidence_scores[i] = confidence
        
        return confidence_scores
    
    def counterfactual_reasoning(
        self,
        factual_concept: ConceptTensor,
        counterfactual_direction: Union[ConceptTensor, str],
        num_samples: int = 5,
        num_steps: int = None,
        guidance_strength: float = DEFAULT_GUIDANCE_STRENGTH,
        intervention_strength: float = 0.5,
        preserve_similarity: float = 0.3,
        show_progress: bool = False
    ) -> ConceptTensor:
        """
        Generate counterfactual variations with controlled interventions.
        
        Args:
            factual_concept: Base concept for counterfactual generation
            counterfactual_direction: Direction of counterfactual change
            num_samples: Number of counterfactual samples
            num_steps: Number of diffusion steps
            guidance_strength: Strength of guidance
            intervention_strength: Strength of counterfactual intervention
            preserve_similarity: How much to preserve from original
            show_progress: Whether to show progress
            
        Returns:
            Counterfactual concept variations
        """
        factual_concept = F.normalize(factual_concept.to(self.device), p=2, dim=-1)
        
        # Handle different types of counterfactual directions
        if isinstance(counterfactual_direction, str):
            # Semantic direction (would need semantic space mappings)
            if self.thought_space is not None and hasattr(self.thought_space, 'get_semantic_direction'):
                direction_vector = self.thought_space.get_semantic_direction(counterfactual_direction)
            else:
                # Generate random orthogonal direction as fallback
                direction_vector = torch.randn_like(factual_concept)
                # Make orthogonal to factual concept
                direction_vector = direction_vector - torch.dot(direction_vector, factual_concept) * factual_concept
                direction_vector = F.normalize(direction_vector, p=2, dim=-1)
        else:
            direction_vector = F.normalize(counterfactual_direction.to(self.device), p=2, dim=-1)
        
        # Create counterfactual targets
        counterfactual_targets = []
        for i in range(num_samples):
            # Vary intervention strength across samples
            strength_variation = intervention_strength * (0.5 + i / (num_samples - 1)) if num_samples > 1 else intervention_strength
            
            # Apply intervention
            intervened_concept = factual_concept + strength_variation * direction_vector
            
            # Preserve some similarity to original
            preserved_concept = preserve_similarity * factual_concept + (1 - preserve_similarity) * intervened_concept
            preserved_concept = F.normalize(preserved_concept, p=2, dim=-1)
            
            counterfactual_targets.append(preserved_concept)
        
        # Create composite guidance for counterfactual generation
        target_guidance_functions = []
        
        for i, target in enumerate(counterfactual_targets):
            target_guidance = TargetGuidance(
                target=target,
                config=GuidanceConfig(strength=guidance_strength / num_samples),
                similarity_type='mixed'
            )
            target_guidance_functions.append((target_guidance, 1.0 / num_samples))
        
        # Add constraint to maintain some similarity to original
        def similarity_constraint(x):
            similarities = F.cosine_similarity(x, factual_concept.unsqueeze(0), dim=-1)
            return similarities  # Higher is better
        
        similarity_guidance = ConstraintGuidance(
            constraint_fn=similarity_constraint,
            config=GuidanceConfig(strength=preserve_similarity * guidance_strength),
            constraint_type='satisfaction'
        )
        target_guidance_functions.append((similarity_guidance, preserve_similarity))
        
        # Create composite guidance
        composite_guidance = CompositeGuidance(
            guidance_functions=target_guidance_functions,
            weighting_method='static',
            conflict_resolution='weighted_average'
        )
        
        # Starting points: variations of the factual concept
        exploration_noise = 0.2
        x_T = factual_concept.unsqueeze(0).expand(num_samples, -1).clone()
        x_T = x_T + exploration_noise * torch.randn_like(x_T)
        x_T = F.normalize(x_T, p=2, dim=-1)
        
        # Setup number of steps
        if num_steps is None:
            num_steps = getattr(self.diffusion_model, 'num_timesteps', 1000) // 3
        
        # Generate counterfactuals
        counterfactuals = self.goal_diffusion.sample_with_guidance(
            guidance_fn=composite_guidance,
            batch_size=num_samples,
            num_steps=num_steps,
            x_T=x_T,
            show_progress=show_progress
        )
        
        return counterfactuals
    
    def multi_step_reasoning(
        self,
        initial_concepts: List[ConceptTensor],
        reasoning_steps: List[Dict[str, Any]],
        num_paths: int = 3,
        path_selection_strategy: str = 'diverse',
        show_progress: bool = False
    ) -> Tuple[ConceptTensor, List[List[ConceptTensor]]]:
        """
        Perform multi-step reasoning with intermediate goals.
        
        Args:
            initial_concepts: Starting concepts for reasoning
            reasoning_steps: List of reasoning step specifications
            num_paths: Number of reasoning paths to maintain
            path_selection_strategy: Strategy for selecting paths ('diverse', 'best', 'random')
            show_progress: Whether to show progress
            
        Returns:
            Final reasoning results and full reasoning paths
        """
        # Initialize paths with initial concepts
        if len(initial_concepts) >= num_paths:
            current_concepts = initial_concepts[:num_paths]
        else:
            # Replicate and add noise to create more paths
            current_concepts = []
            for i in range(num_paths):
                base_idx = i % len(initial_concepts)
                concept = initial_concepts[base_idx].clone()
                
                if i >= len(initial_concepts):
                    # Add noise for diversity
                    noise = torch.randn_like(concept) * 0.1
                    concept = F.normalize(concept + noise, p=2, dim=-1)
                
                current_concepts.append(concept)
        
        # Track full reasoning paths
        reasoning_paths = [[concept] for concept in current_concepts]
        
        # Process each reasoning step
        for step_idx, step_spec in enumerate(reasoning_steps):
            if show_progress:
                print(f"Processing reasoning step {step_idx + 1}/{len(reasoning_steps)}")
            
            step_type = step_spec.get('type', 'goal_directed')
            step_goal = step_spec.get('goal')
            step_constraints = step_spec.get('constraints', [])
            step_params = step_spec.get('params', {})
            
            new_concepts = []
            
            # Process each current concept through this step
            for concept in current_concepts:
                if step_type == 'goal_directed':
                    if step_goal is not None:
                        step_result = self.goal_directed_reasoning(
                            source_concepts=[concept],
                            goal_concept=step_goal,
                            constraints=step_constraints,
                            num_reasoning_paths=1,
                            show_progress=False,
                            **step_params
                        )
                        new_concepts.append(step_result[0])
                    else:
                        new_concepts.append(concept)  # No change if no goal
                
                elif step_type == 'analogy':
                    # Analogy completion step
                    a, b, c = step_spec['analogy_terms']
                    completion = self.analogy_completion(
                        a=a, b=b, c=concept,
                        num_samples=1,
                        show_progress=False,
                        **step_params
                    )
                    new_concepts.append(completion[0])
                
                elif step_type == 'counterfactual':
                    # Counterfactual reasoning step
                    direction = step_spec['direction']
                    counterfactual = self.counterfactual_reasoning(
                        factual_concept=concept,
                        counterfactual_direction=direction,
                        num_samples=1,
                        show_progress=False,
                        **step_params
                    )
                    new_concepts.append(counterfactual[0])
                
                else:
                    # Unknown step type, keep concept unchanged
                    new_concepts.append(concept)
            
            # Path selection for next step
            if len(new_concepts) > num_paths:
                if path_selection_strategy == 'diverse':
                    # Select most diverse concepts
                    selected_indices = self._select_diverse_concepts(new_concepts, num_paths)
                elif path_selection_strategy == 'best':
                    # Select best concepts (would need evaluation criteria)
                    if step_goal is not None:
                        scores = [F.cosine_similarity(c.unsqueeze(0), step_goal.unsqueeze(0)).item() 
                                for c in new_concepts]
                        selected_indices = torch.topk(torch.tensor(scores), num_paths).indices.tolist()
                    else:
                        selected_indices = list(range(num_paths))
                else:  # random
                    selected_indices = torch.randperm(len(new_concepts))[:num_paths].tolist()
                
                current_concepts = [new_concepts[i] for i in selected_indices]
                
                # Update reasoning paths
                new_reasoning_paths = []
                for i, selected_idx in enumerate(selected_indices):
                    original_path_idx = selected_idx % len(reasoning_paths)
                    new_path = reasoning_paths[original_path_idx] + [new_concepts[selected_idx]]
                    new_reasoning_paths.append(new_path)
                reasoning_paths = new_reasoning_paths
            else:
                current_concepts = new_concepts
                # Update all paths
                for i, concept in enumerate(current_concepts):
                    if i < len(reasoning_paths):
                        reasoning_paths[i].append(concept)
        
        # Return final concepts and complete paths
        final_concepts = torch.stack(current_concepts, dim=0)
        return final_concepts, reasoning_paths
    
    def _select_diverse_concepts(self, concepts: List[ConceptTensor], num_select: int) -> List[int]:
        """Select most diverse concepts using greedy algorithm."""
        if len(concepts) <= num_select:
            return list(range(len(concepts)))
        
        # Convert to tensor for easier computation
        concept_stack = torch.stack(concepts, dim=0)
        
        selected_indices = []
        remaining_indices = list(range(len(concepts)))
        
        # Select first concept randomly
        first_idx = np.random.choice(remaining_indices)
        selected_indices.append(first_idx)
        remaining_indices.remove(first_idx)
        
        # Greedily select most diverse remaining concepts
        for _ in range(num_select - 1):
            if not remaining_indices:
                break
            
            best_idx = None
            best_min_distance = -1
            
            for candidate_idx in remaining_indices:
                # Find minimum distance to already selected concepts
                candidate_concept = concept_stack[candidate_idx]
                min_distance = float('inf')
                
                for selected_idx in selected_indices:
                    selected_concept = concept_stack[selected_idx]
                    distance = torch.norm(candidate_concept - selected_concept).item()
                    min_distance = min(min_distance, distance)
                
                # Select candidate with maximum minimum distance
                if min_distance > best_min_distance:
                    best_min_distance = min_distance
                    best_idx = candidate_idx
            
            if best_idx is not None:
                selected_indices.append(best_idx)
                remaining_indices.remove(best_idx)
        
        return selected_indices
    
    def _store_reasoning_experience(self,
                                  source_concepts: Optional[List[ConceptTensor]],
                                  goal_concept: Optional[ConceptTensor],
                                  results: ConceptTensor,
                                  metrics: Optional[Dict[str, Any]],
                                  mode: str):
        """Store reasoning experience for learning and analysis."""
        experience = {
            'timestamp': time.time(),
            'source_concepts': [c.detach().cpu() for c in source_concepts] if source_concepts else None,
            'goal_concept': goal_concept.detach().cpu() if goal_concept is not None else None,
            'results': results.detach().cpu(),
            'metrics': metrics,
            'mode': mode,
            'diversity_score': self._compute_diversity_score(results),
            'goal_alignment': self._compute_goal_alignment(results, goal_concept) if goal_concept is not None else None
        }
        
        self.reasoning_history.append(experience)
        
        # Limit history size
        if len(self.reasoning_history) > 1000:
            self.reasoning_history.pop(0)
        
        # Update performance metrics
        self._update_performance_metrics()
    
    def _update_performance_metrics(self):
        """Update aggregate performance metrics."""
        if not self.reasoning_history:
            return
        
        recent_history = self.reasoning_history[-50:]  # Last 50 reasoning sessions
        
        # Success rate (based on goal alignment > threshold)
        successes = 0
        goal_sessions = 0
        total_steps = 0
        convergence_count = 0
        
        for exp in recent_history:
            if exp['goal_alignment'] is not None:
                goal_sessions += 1
                if exp['goal_alignment'] > 0.7:  # Success threshold
                    successes += 1
            
            if exp['metrics'] and 'reasoning_time' in exp['metrics']:
                total_steps += exp['metrics'].get('num_paths', 1)
                
            if exp['metrics'] and 'convergence_scores' in exp['metrics']:
                convergence_scores = exp['metrics']['convergence_scores']
                if convergence_scores and convergence_scores[-1] > 0.8:
                    convergence_count += 1
        
        # Update metrics
        self.performance_metrics['success_rate'] = successes / max(goal_sessions, 1)
        self.performance_metrics['average_steps'] = total_steps / max(len(recent_history), 1)
        self.performance_metrics['convergence_rate'] = convergence_count / max(len(recent_history), 1)
    
    def get_reasoning_analytics(self) -> Dict[str, Any]:
        """Get comprehensive analytics about reasoning performance."""
        if not self.reasoning_history:
            return {'message': 'No reasoning history available'}
        
        analytics = {
            'total_reasoning_sessions': len(self.reasoning_history),
            'performance_metrics': self.performance_metrics.copy(),
            'mode_usage': {},
            'average_diversity': 0.0,
            'average_goal_alignment': 0.0,
            'reasoning_time_stats': {},
            'recent_trends': {}
        }
        
        # Mode usage statistics
        modes = [exp['mode'] for exp in self.reasoning_history]
        for mode in set(modes):
            analytics['mode_usage'][mode] = modes.count(mode) / len(modes)
        
        # Diversity and alignment statistics
        diversity_scores = [exp['diversity_score'] for exp in self.reasoning_history if exp['diversity_score'] is not None]
        alignment_scores = [exp['goal_alignment'] for exp in self.reasoning_history if exp['goal_alignment'] is not None]
        
        if diversity_scores:
            analytics['average_diversity'] = np.mean(diversity_scores)
            analytics['diversity_std'] = np.std(diversity_scores)
        
        if alignment_scores:
            analytics['average_goal_alignment'] = np.mean(alignment_scores)
            analytics['alignment_std'] = np.std(alignment_scores)
        
        # Timing statistics
        reasoning_times = []
        for exp in self.reasoning_history:
            if exp['metrics'] and 'reasoning_time' in exp['metrics']:
                reasoning_times.append(exp['metrics']['reasoning_time'])
        
        if reasoning_times:
            analytics['reasoning_time_stats'] = {
                'mean': np.mean(reasoning_times),
                'std': np.std(reasoning_times),
                'min': np.min(reasoning_times),
                'max': np.max(reasoning_times)
            }
        
        # Recent trends (last 20 sessions vs previous 20)
        if len(self.reasoning_history) >= 40:
            recent_20 = self.reasoning_history[-20:]
            previous_20 = self.reasoning_history[-40:-20]
            
            recent_alignment = np.mean([exp['goal_alignment'] for exp in recent_20 if exp['goal_alignment'] is not None])
            previous_alignment = np.mean([exp['goal_alignment'] for exp in previous_20 if exp['goal_alignment'] is not None])
            
            recent_diversity = np.mean([exp['diversity_score'] for exp in recent_20 if exp['diversity_score'] is not None])
            previous_diversity = np.mean([exp['diversity_score'] for exp in previous_20 if exp['diversity_score'] is not None])
            
            analytics['recent_trends'] = {
                'alignment_change': recent_alignment - previous_alignment,
                'diversity_change': recent_diversity - previous_diversity,
                'improvement_trend': recent_alignment > previous_alignment
            }
        
        return analytics


# Export all public classes and functions
__all__ = [
    'GuidanceFunctionBase',
    'TargetGuidance', 
    'ConstraintGuidance',
    'EnergyGuidance',
    'PathGuidance',
    'CompositeGuidance',
    'AdaptiveGuidance',
    'GoalDirectedDiffusion',
    'ReverseDiffusionReasoning',
    'constraint_guided_sampling',
    'diffusion_bridge',
    'reverse_process_with_guidance',
    'GuidanceType',
    'DiffusionConfig',
    'GuidanceConfig'
]