#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ULTRA: Ultimate Learning & Thought Reasoning Architecture
Reverse Diffusion Reasoning Module

This module implements reasoning from desired outcomes back to potential solutions
using diffusion processes. It includes algorithms for guided diffusion, constraint
satisfaction, and goal-directed reasoning in the thought latent space.

The module contains:
1. Guidance functions for directing diffusion toward specific goals
2. Reverse diffusion reasoning with various guidance strategies
3. Constraint-based reasoning methods
4. Exploration and interpolation in reasoning spaces
"""

import logging
import math
from typing import Dict, List, Tuple, Union, Optional, Callable, Any, TypeVar

import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.distributions import MultivariateNormal, Normal
from tqdm.auto import tqdm

# Import with error handling
try:
    from ultra.ultra.diffusion_reasoning.conceptual_diffusion import conceptual_diffusion
except ImportError:
    logger = logging.getLogger(__name__)
    logger.warning("ConceptualDiffusion not available - creating placeholder")
    ConceptualDiffusion = None

try:
    from ultra.ultra.diffusion_reasoning.thought_latent_space import thought_Latent_space
except ImportError:
    logger = logging.getLogger(__name__)
    logger.warning("ThoughtLatentSpace not available - creating placeholder")
    ThoughtLatentSpace = None

# Configure logger
logger = logging.getLogger(__name__)

# Type hint for tensors or arrays
TensorType = TypeVar('TensorType', torch.Tensor, np.ndarray)

# Default parameters
DEFAULT_GUIDANCE_STRENGTH = 3.0
DEFAULT_NUM_STEPS = 50
DEFAULT_NUM_PATHS = 5
DEFAULT_TEMPERATURE = 1.0


class GuidanceFunctionBase:
    """
    Base class for diffusion guidance functions.
    
    Guidance functions direct the reverse diffusion process toward
    desired outcomes by modifying the predicted noise or gradients.
    """
    
    def __call__(self, x_t: torch.Tensor, t: torch.Tensor) -> torch.Tensor:
        """
        Compute guidance for the diffusion process.
        
        Args:
            x_t: Diffused concept tensor at time t [batch_size, concept_dim]
            t: Current timestep(s) [batch_size]
            
        Returns:
            Guidance tensor [batch_size, concept_dim]
        """
        raise NotImplementedError("Guidance functions must implement __call__")
    
    def update(self, *args, **kwargs) -> None:
        """
        Update internal state of the guidance function.
        
        Default implementation does nothing. Subclasses should override
        if they need to update internal state during diffusion.
        """
        pass


class TargetGuidance(GuidanceFunctionBase):
    """
    Guides the diffusion process toward a specific target concept.
    """
    
    def __init__(self, 
                 target: torch.Tensor, 
                 strength_schedule: Optional[Callable[[float], float]] = None,
                 cosine_similarity_weight: float = 0.5):
        """
        Initialize target guidance.
        
        Args:
            target: Target concept tensor [concept_dim] or [batch_size, concept_dim]
            strength_schedule: Optional function mapping timestep to guidance strength
            cosine_similarity_weight: Weight for cosine similarity vs Euclidean distance
        """
        # Ensure target is properly normalized
        self.target = F.normalize(target, p=2, dim=0) if len(target.shape) == 1 else F.normalize(target, p=2, dim=1)
        
        # Default strength schedule increases as t decreases
        if strength_schedule is None:
            self.strength_schedule = lambda t: 1.0 - (t / 1000.0)  # Default 1000-step schedule
        else:
            self.strength_schedule = strength_schedule
            
        self.cosine_similarity_weight = cosine_similarity_weight
        
    def __call__(self, x_t: torch.Tensor, t: torch.Tensor) -> torch.Tensor:
        """
        Compute guidance toward the target concept.
        
        Args:
            x_t: Diffused concept tensor at time t [batch_size, concept_dim]
            t: Current timestep(s) [batch_size]
            
        Returns:
            Guidance tensor [batch_size, concept_dim]
        """
        # Normalize x_t for cosine similarity calculations
        x_t_norm = F.normalize(x_t, p=2, dim=1)
        
        # Get strength for current timestep
        if isinstance(t, torch.Tensor):
            t_float = t.float().mean().item()  # Use mean for batch
        else:
            t_float = float(t)
            
        strength = self.strength_schedule(t_float)
        
        # Compute direction toward target
        if len(self.target.shape) == 1:
            # Single target for all batch elements
            target_expanded = self.target.unsqueeze(0).expand(x_t.size(0), -1)
            
            # Compute both Euclidean and cosine directions
            euclidean_direction = target_expanded - x_t
            cosine_similarity = torch.sum(x_t_norm * target_expanded, dim=1, keepdim=True)
            cosine_direction = target_expanded - cosine_similarity * x_t_norm
        else:
            # Batch of targets
            if self.target.size(0) != x_t.size(0):
                raise ValueError(f"Target batch size ({self.target.size(0)}) must match x_t batch size ({x_t.size(0)})")
            
            # Compute both Euclidean and cosine directions
            euclidean_direction = self.target - x_t
            cosine_similarity = torch.sum(x_t_norm * self.target, dim=1, keepdim=True)
            cosine_direction = self.target - cosine_similarity * x_t_norm
            
        # Combine directions with weighting
        direction = (1 - self.cosine_similarity_weight) * euclidean_direction + \
                    self.cosine_similarity_weight * cosine_direction
            
        return strength * direction


class ConstraintGuidance(GuidanceFunctionBase):
    """
    Guides the diffusion process based on constraint satisfaction.
    """
    
    def __init__(
        self, 
        constraint_fn: Callable[[torch.Tensor], torch.Tensor], 
        strength: float = 1.0,
        strength_schedule: Optional[Callable[[float], float]] = None,
        use_autograd: bool = True,
        grad_sample_size: int = 10
    ):
        """
        Initialize constraint guidance.
        
        Args:
            constraint_fn: Function that computes satisfaction score for each sample
                          (returns tensor [batch_size] with values in [0, 1])
            strength: Base guidance strength
            strength_schedule: Optional function mapping timestep to relative strength
            use_autograd: Whether to use autograd for computing gradients
            grad_sample_size: Number of samples for finite difference if not using autograd
        """
        self.constraint_fn = constraint_fn
        self.strength = strength
        self.use_autograd = use_autograd
        self.grad_sample_size = grad_sample_size
        
        # Default strength is constant
        if strength_schedule is None:
            self.strength_schedule = lambda t: 1.0
        else:
            self.strength_schedule = strength_schedule
        
    def __call__(self, x_t: torch.Tensor, t: torch.Tensor) -> torch.Tensor:
        """
        Compute guidance based on constraint satisfaction.
        
        Args:
            x_t: Diffused concept tensor at time t [batch_size, concept_dim]
            t: Current timestep(s) [batch_size]
            
        Returns:
            Guidance tensor [batch_size, concept_dim]
        """
        # Get strength for current timestep
        if isinstance(t, torch.Tensor):
            t_float = t.float().mean().item()  # Use mean for batch
        else:
            t_float = float(t)
            
        schedule_factor = self.strength_schedule(t_float)
        
        if self.use_autograd:
            # Compute gradient using autograd
            guidance = self._compute_autograd_guidance(x_t)
        else:
            # Compute gradient using finite difference approximation
            guidance = self._compute_numerical_guidance(x_t)
        
        # Apply strength factor
        guidance = guidance * self.strength * schedule_factor
        
        return guidance
    
    def _compute_autograd_guidance(self, x_t: torch.Tensor) -> torch.Tensor:
        """
        Compute guidance gradient using autograd.
        
        Args:
            x_t: Diffused concept tensor [batch_size, concept_dim]
            
        Returns:
            Guidance gradient [batch_size, concept_dim]
        """
        x_t_detached = x_t.detach().clone()
        x_t_detached.requires_grad_(True)
        
        # Evaluate constraint function
        constraint_scores = self.constraint_fn(x_t_detached)
        
        # We want to maximize the constraint satisfaction, so take mean and negate for gradient descent
        constraint_loss = -torch.mean(constraint_scores)
        
        # Compute gradient
        constraint_loss.backward()
        gradient = x_t_detached.grad.clone()
        
        # Clean up
        x_t_detached.grad = None
        
        return -gradient  # Negative because we want to ascend, not descend
    
    def _compute_numerical_guidance(self, x_t: torch.Tensor) -> torch.Tensor:
        """
        Compute guidance gradient using finite difference approximation.
        
        Args:
            x_t: Diffused concept tensor [batch_size, concept_dim]
            
        Returns:
            Guidance gradient [batch_size, concept_dim]
        """
        batch_size, dim = x_t.shape
        epsilon = 1e-4
        
        # Initialize gradient
        gradient = torch.zeros_like(x_t)
        
        # Evaluate baseline constraint satisfaction
        baseline_scores = self.constraint_fn(x_t)
        
        # Sample random directions for finite difference
        for _ in range(self.grad_sample_size):
            # Sample a random direction for each batch element
            directions = torch.randn_like(x_t)
            directions = F.normalize(directions, dim=1) * epsilon
            
            # Evaluate constraints at perturbed points
            perturbed_scores = self.constraint_fn(x_t + directions)
            
            # Compute gradient contribution
            score_diff = perturbed_scores - baseline_scores
            for i in range(batch_size):
                gradient[i] += directions[i] * score_diff[i] / epsilon
        
        # Average gradients
        gradient = gradient / self.grad_sample_size
        
        return gradient


class AnalyticGuidance(GuidanceFunctionBase):
    """
    Guides the diffusion process using analytic functions in the latent space.
    
    This guidance can use explicit mathematical functions that define
    directions or regions in the thought latent space.
    """
    
    def __init__(
        self, 
        guidance_fn: Callable[[torch.Tensor], torch.Tensor],
        strength: float = 1.0,
        strength_schedule: Optional[Callable[[float], float]] = None
    ):
        """
        Initialize analytic guidance.
        
        Args:
            guidance_fn: Function that computes guidance direction for each sample
                        (returns tensor [batch_size, concept_dim])
            strength: Base guidance strength
            strength_schedule: Optional function mapping timestep to relative strength
        """
        self.guidance_fn = guidance_fn
        self.strength = strength
        
        # Default strength schedule is constant
        if strength_schedule is None:
            self.strength_schedule = lambda t: 1.0
        else:
            self.strength_schedule = strength_schedule
        
    def __call__(self, x_t: torch.Tensor, t: torch.Tensor) -> torch.Tensor:
        """
        Compute analytic guidance.
        
        Args:
            x_t: Diffused concept tensor at time t [batch_size, concept_dim]
            t: Current timestep(s) [batch_size]
            
        Returns:
            Guidance tensor [batch_size, concept_dim]
        """
        # Get strength for current timestep
        if isinstance(t, torch.Tensor):
            t_float = t.float().mean().item()
        else:
            t_float = float(t)
            
        schedule_factor = self.strength_schedule(t_float)
        
        # Compute guidance direction
        guidance = self.guidance_fn(x_t)
        
        # Apply strength factor
        guidance = guidance * self.strength * schedule_factor
        
        return guidance


class PathGuidance(GuidanceFunctionBase):
    """
    Guides the diffusion process along a predefined path in the latent space.
    
    This guidance gradually moves along a sequence of concept points,
    creating a guided trajectory through the thought space.
    """
    
    def __init__(
        self, 
        path_points: List[torch.Tensor],
        strength: float = 1.0,
        window_size: int = 2,
        path_smoothing: float = 0.5
    ):
        """
        Initialize path guidance.
        
        Args:
            path_points: List of concept tensors defining the path [num_points, concept_dim]
            strength: Base guidance strength
            window_size: Number of path points to consider at each step
            path_smoothing: Smoothing factor for path interpolation (0-1)
        """
        # Normalize path points
        self.path_points = [F.normalize(p, p=2, dim=0) for p in path_points]
        self.strength = strength
        self.window_size = min(window_size, len(path_points))
        self.path_smoothing = path_smoothing
        
        # Progress along the path (0 to 1)
        self.progress = 0.0
        
    def __call__(self, x_t: torch.Tensor, t: torch.Tensor) -> torch.Tensor:
        """
        Compute path guidance.
        
        Args:
            x_t: Diffused concept tensor at time t [batch_size, concept_dim]
            t: Current timestep(s) [batch_size]
            
        Returns:
            Guidance tensor [batch_size, concept_dim]
        """
        batch_size = x_t.shape[0]
        
        # Update progress based on timestep
        if isinstance(t, torch.Tensor):
            t_float = t.float().mean().item()
        else:
            t_float = float(t)
            
        # Compute current position in the path
        path_length = len(self.path_points) - 1
        path_pos = path_length * (1.0 - t_float / 1000.0)  # Assuming 1000 diffusion steps
        
        # Get indices of nearest path points
        idx_low = max(0, min(path_length, int(path_pos)))
        idx_high = min(path_length, idx_low + 1)
        
        # Interpolation weight
        alpha = path_pos - idx_low
        
        # Get current target by interpolating between path points
        if idx_low == idx_high:
            current_target = self.path_points[idx_low]
        else:
            current_target = (1 - alpha) * self.path_points[idx_low] + alpha * self.path_points[idx_high]
            
        # Normalize and expand target for batch
        current_target = F.normalize(current_target, p=2, dim=0)
        current_target = current_target.unsqueeze(0).expand(batch_size, -1)
        
        # Normalize inputs for directional guidance
        x_t_norm = F.normalize(x_t, p=2, dim=1)
        
        # Compute direction toward current target
        direction = current_target - x_t_norm
        
        # Apply smoothing if enabled
        if self.path_smoothing > 0:
            # Look ahead to smooth the path
            lookahead_idx = min(path_length, idx_high + 1)
            if lookahead_idx != idx_high:
                lookahead_target = self.path_points[lookahead_idx]
                lookahead_target = F.normalize(lookahead_target, p=2, dim=0)
                lookahead_target = lookahead_target.unsqueeze(0).expand(batch_size, -1)
                
                # Blend current and lookahead directions
                lookahead_direction = lookahead_target - x_t_norm
                direction = (1 - self.path_smoothing) * direction + self.path_smoothing * lookahead_direction
        
        return self.strength * direction
    
    def update(self, progress: float = None) -> None:
        """
        Update progress along the path.
        
        Args:
            progress: Optional explicit progress value (0 to 1)
        """
        if progress is not None:
            self.progress = max(0.0, min(1.0, progress))


class CompositeGuidance(GuidanceFunctionBase):
    """
    Combines multiple guidance functions with weightings.
    """
    
    def __init__(self, guidance_functions: List[Tuple[GuidanceFunctionBase, float]]):
        """
        Initialize composite guidance.
        
        Args:
            guidance_functions: List of (guidance_function, weight) tuples
        """
        self.guidance_functions = guidance_functions
        
    def __call__(self, x_t: torch.Tensor, t: torch.Tensor) -> torch.Tensor:
        """
        Compute combined guidance.
        
        Args:
            x_t: Diffused concept tensor at time t [batch_size, concept_dim]
            t: Current timestep(s) [batch_size]
            
        Returns:
            Guidance tensor [batch_size, concept_dim]
        """
        # Sum weighted guidance from all functions
        guidance = torch.zeros_like(x_t)
        for func, weight in self.guidance_functions:
            guidance = guidance + weight * func(x_t, t)
            
        return guidance
    
    def update(self, *args, **kwargs) -> None:
        """
        Update all contained guidance functions.
        
        Args:
            *args, **kwargs: Arguments to pass to guidance function update methods
        """
        for func, _ in self.guidance_functions:
            func.update(*args, **kwargs)


class AdaptiveGuidance(GuidanceFunctionBase):
    """
    Adapts guidance strength based on diffusion progress and satisfaction measures.
    
    This guidance dynamically adjusts its strength based on how well
    constraints are being satisfied during the diffusion process.
    """
    
    def __init__(
        self, 
        base_guidance: GuidanceFunctionBase,
        satisfaction_fn: Callable[[torch.Tensor], torch.Tensor],
        min_strength: float = 0.1,
        max_strength: float = 5.0,
        adaptation_rate: float = 0.1
    ):
        """
        Initialize adaptive guidance.
        
        Args:
            base_guidance: Base guidance function to adapt
            satisfaction_fn: Function that measures constraint satisfaction (0-1)
            min_strength: Minimum guidance strength
            max_strength: Maximum guidance strength
            adaptation_rate: Rate at which strength adapts
        """
        self.base_guidance = base_guidance
        self.satisfaction_fn = satisfaction_fn
        self.min_strength = min_strength
        self.max_strength = max_strength
        self.adaptation_rate = adaptation_rate
        
        # Current adaptive strength multiplier
        self.current_strength = 1.0
        
        # History for tracking
        self.strength_history = []
        self.satisfaction_history = []
        
    def __call__(self, x_t: torch.Tensor, t: torch.Tensor) -> torch.Tensor:
        """
        Compute adaptive guidance.
        
        Args:
            x_t: Diffused concept tensor at time t [batch_size, concept_dim]
            t: Current timestep(s) [batch_size]
            
        Returns:
            Guidance tensor [batch_size, concept_dim]
        """
        # Get base guidance
        base_direction = self.base_guidance(x_t, t)
        
        # Measure current satisfaction
        satisfaction = self.satisfaction_fn(x_t).mean().item()
        self.satisfaction_history.append(satisfaction)
        
        # Adapt strength based on satisfaction
        target_strength = self.min_strength + (self.max_strength - self.min_strength) * (1.0 - satisfaction)
        self.current_strength = (1 - self.adaptation_rate) * self.current_strength + self.adaptation_rate * target_strength
        self.strength_history.append(self.current_strength)
        
        # Apply adaptive strength
        return self.current_strength * base_direction
    
    def update(self, *args, **kwargs) -> None:
        """
        Update base guidance function.
        
        Args:
            *args, **kwargs: Arguments to pass to base guidance update method
        """
        self.base_guidance.update(*args, **kwargs)


class GoalDirectedDiffusion:
    """
    Implements diffusion processes directed towards specific goals.
    
    This class provides specialized goal-directed sampling methods
    for the concept diffusion model.
    """
    
    def __init__(
        self, 
        diffusion_model,  # Changed from ConceptualDiffusion type hint
        device: str = 'cpu'
    ):
        """
        Initialize goal-directed diffusion.
        
        Args:
            diffusion_model: Trained diffusion model
            device: Device to run computations on ('cpu' or 'cuda')
        """
        self.diffusion_model = diffusion_model
        self.device = device
        
    def sample_with_guidance(
        self,
        guidance_fn: GuidanceFunctionBase,
        batch_size: int = 1,
        steps: int = None,
        x_T: Optional[torch.Tensor] = None,
        temperature: float = 1.0,
        return_trajectory: bool = False,
        show_progress: bool = False
    ) -> Union[torch.Tensor, Tuple[torch.Tensor, List[torch.Tensor]]]:
        """
        Sample from the diffusion model with guidance.
        
        Args:
            guidance_fn: Guidance function to direct the diffusion
            batch_size: Number of samples to generate
            steps: Number of diffusion steps (defaults to model's num_timesteps)
            x_T: Optional starting noise (if None, use random)
            temperature: Temperature for sampling (higher = more diverse)
            return_trajectory: Whether to return intermediate steps
            show_progress: Whether to show progress bar
            
        Returns:
            If return_trajectory is False: Tensor of samples [batch_size, concept_dim]
            If return_trajectory is True: Tuple of (samples, trajectory)
        """
        if self.diffusion_model is None:
            raise RuntimeError("ConceptualDiffusion model not available")
            
        # Set number of steps
        if steps is None:
            steps = getattr(self.diffusion_model, 'num_timesteps', 1000)
            
        # Generate starting noise if not provided
        if x_T is None:
            concept_dim = getattr(self.diffusion_model, 'concept_dim', 256)
            x_T = torch.randn(batch_size, concept_dim, device=self.device)
            
        # Scale noise by temperature
        x_T = x_T * temperature
        
        # Setup to track trajectory if requested
        trajectory = [] if return_trajectory else None
        
        # Initialize current sample
        x_t = x_T.clone()
        
        # Create timestep schedule (evenly spaced)
        num_timesteps = getattr(self.diffusion_model, 'num_timesteps', 1000)
        timesteps = torch.linspace(
            num_timesteps - 1, 0, steps, 
            dtype=torch.long, device=self.device
        )
        
        # Setup progress bar if requested
        timestep_iter = tqdm(timesteps, desc="Sampling", disable=not show_progress)
        
        # Sampling loop
        for t in timestep_iter:
            # Expand t for batch
            t_batch = t.expand(batch_size)
            
            # Get guidance direction
            guidance = guidance_fn(x_t, t)
            
            # Record current state if tracking trajectory
            if return_trajectory:
                trajectory.append(x_t.detach().clone())
            
            # Denoise one step with guidance
            with torch.no_grad():
                # Check if model has noise_predictor method
                if hasattr(self.diffusion_model, 'noise_predictor'):
                    # Predict noise
                    predicted_noise = self.diffusion_model.noise_predictor(x_t, t_batch)
                    
                    # Apply guidance to predicted noise
                    predicted_noise = predicted_noise - guidance
                    
                    # Denoise one step
                    if hasattr(self.diffusion_model, 'denoise_step'):
                        x_t = self.diffusion_model.denoise_step(x_t, predicted_noise, t)
                    else:
                        # Fallback: simple denoising
                        x_t = x_t - predicted_noise * 0.01
                else:
                    # Fallback: apply guidance directly
                    x_t = x_t + guidance * 0.01
                    
                # Optional: clip to ensure valid range
                if hasattr(self.diffusion_model, 'clip_range') and self.diffusion_model.clip_range:
                    x_t = torch.clamp(x_t, -self.diffusion_model.clip_range, self.diffusion_model.clip_range)
                    
            # Update guidance
            guidance_fn.update()
                
        # Normalize final samples
        x_0 = F.normalize(x_t, p=2, dim=1)
        
        if return_trajectory:
            return x_0, trajectory
        else:
            return x_0
        
    def conditional_sampling(
        self,
        conditions: Dict[int, torch.Tensor],
        batch_size: int = 1,
        steps: int = None,
        guidance_scale: float = 3.0,
        temperature: float = 1.0,
        return_trajectory: bool = False,
        show_progress: bool = False
    ) -> Union[torch.Tensor, Tuple[torch.Tensor, List[torch.Tensor]]]:
        """
        Sample with conditioning on specific dimensions.
        
        Args:
            conditions: Dictionary mapping dimension indices to values
            batch_size: Number of samples to generate
            steps: Number of diffusion steps (defaults to model's num_timesteps)
            guidance_scale: Scale factor for conditioning guidance
            temperature: Temperature for sampling (higher = more diverse)
            return_trajectory: Whether to return intermediate steps
            show_progress: Whether to show progress bar
            
        Returns:
            If return_trajectory is False: Tensor of samples [batch_size, concept_dim]
            If return_trajectory is True: Tuple of (samples, trajectory)
        """
        # Set number of steps
        if steps is None:
            steps = self.diffusion_model.num_timesteps
            
        # Prepare condition mask
        condition_mask = torch.zeros(self.diffusion_model.concept_dim, device=self.device)
        for dim in conditions:
            condition_mask[dim] = 1.0
            
        # Prepare condition values
        condition_values = torch.zeros(batch_size, self.diffusion_model.concept_dim, device=self.device)
        for dim, value in conditions.items():
            if isinstance(value, torch.Tensor):
                if value.numel() == 1:
                    # Single value for all batch elements
                    condition_values[:, dim] = value.item()
                else:
                    # Batch of values
                    condition_values[:, dim] = value
            else:
                condition_values[:, dim] = value
                
        # Create conditional guidance function
        def conditional_guidance_fn(x_t, t):
            # Extract values at conditioned dimensions
            x_t_cond = x_t * condition_mask
            
            # Compute direction toward conditioned values
            direction = (condition_values - x_t_cond) * condition_mask
            
            # Scale guidance by timestep (stronger as t decreases)
            if isinstance(t, torch.Tensor):
                t_float = t.float().mean().item()
            else:
                t_float = float(t)
                
            scale = guidance_scale * (1.0 - t_float / self.diffusion_model.num_timesteps)
            
            return scale * direction
        
        # Create guidance function
        guidance = lambda x_t, t: conditional_guidance_fn(x_t, t)
        
        # Sample with guidance
        return self.sample_with_guidance(
            guidance_fn=guidance,
            batch_size=batch_size,
            steps=steps,
            temperature=temperature,
            return_trajectory=return_trajectory,
            show_progress=show_progress
        )


def constraint_guided_sampling(
    diffusion_model,  # Changed from ConceptualDiffusion type hint
    constraint_fn: Callable[[torch.Tensor], torch.Tensor],
    batch_size: int = 10,
    guidance_scale: float = 1.0,
    temperature: float = 1.0,
    num_steps: int = None,
    return_best_only: bool = False,
    starting_point: Optional[torch.Tensor] = None,
    show_progress: bool = False,
    device: str = 'cpu'
) -> Union[torch.Tensor, Tuple[torch.Tensor, torch.Tensor]]:
    """
    Sample concepts that satisfy specified constraints.
    
    Args:
        diffusion_model: Trained diffusion model
        constraint_fn: Function that computes constraint satisfaction (0-1)
        batch_size: Number of samples to generate
        guidance_scale: Scale factor for constraint guidance
        temperature: Temperature for sampling (higher = more diverse)
        num_steps: Number of diffusion steps (defaults to model's num_timesteps)
        return_best_only: Whether to return only the best sample
        starting_point: Optional starting point (if None, use random noise)
        show_progress: Whether to show progress bar
        device: Device to run computations on ('cpu' or 'cuda')
        
    Returns:
        If return_best_only is False: Tensor of samples [batch_size, concept_dim]
        If return_best_only is True: Tuple of (best_sample, best_score)
    """
    if diffusion_model is None:
        raise RuntimeError("Diffusion model not available")
        
    # Create constraint guidance
    num_timesteps = getattr(diffusion_model, 'num_timesteps', 1000)
    guidance = ConstraintGuidance(
        constraint_fn=constraint_fn,
        strength=guidance_scale,
        strength_schedule=lambda t: 1.0 - (t / num_timesteps)
    )
    
    # Create goal-directed diffusion sampler
    goal_diffusion = GoalDirectedDiffusion(
        diffusion_model=diffusion_model,
        device=device
    )
    
    # Sample with guidance
    samples = goal_diffusion.sample_with_guidance(
        guidance_fn=guidance,
        batch_size=batch_size,
        steps=num_steps,
        x_T=starting_point,
        temperature=temperature,
        show_progress=show_progress
    )
    
    # Evaluate constraint satisfaction for each sample
    scores = constraint_fn(samples)
    
    if return_best_only:
        # Find best sample
        best_idx = torch.argmax(scores)
        return samples[best_idx:best_idx+1], scores[best_idx].item()
    else:
        return samples


def diffusion_bridge(
    diffusion_model,  # Changed from ConceptualDiffusion type hint
    start_point: torch.Tensor,
    end_point: torch.Tensor,
    num_samples: int = 1,
    num_steps: int = None,
    noise_schedule: Optional[Callable[[float], float]] = None,
    guidance_scale: float = 3.0,
    return_trajectory: bool = False,
    show_progress: bool = False,
    device: str = 'cpu'
) -> Union[torch.Tensor, Tuple[torch.Tensor, List[torch.Tensor]]]:
    """
    Create a diffusion bridge between two points in concept space.
    
    Args:
        diffusion_model: Trained diffusion model
        start_point: Starting concept tensor [concept_dim]
        end_point: Ending concept tensor [concept_dim]
        num_samples: Number of bridge samples to generate
        num_steps: Number of diffusion steps (defaults to model's num_timesteps)
        noise_schedule: Optional function mapping progress (0-1) to noise level
        guidance_scale: Scale factor for guidance
        return_trajectory: Whether to return intermediate steps
        show_progress: Whether to show progress bar
        device: Device to run computations on ('cpu' or 'cuda')
        
    Returns:
        If return_trajectory is False: Tensor of bridge samples [num_samples, concept_dim]
        If return_trajectory is True: Tuple of (samples, trajectory)
    """
    if diffusion_model is None:
        raise RuntimeError("Diffusion model not available")
        
    # Set number of steps
    if num_steps is None:
        num_steps = getattr(diffusion_model, 'num_timesteps', 1000)
        
    # Default noise schedule
    if noise_schedule is None:
        noise_schedule = lambda p: 0.5 * (1.0 - p)  # Linear decrease from 0.5 to 0
        
    # Ensure tensors are on the correct device
    start_point = start_point.to(device)
    end_point = end_point.to(device)
    
    # Normalize points
    start_point = F.normalize(start_point, p=2, dim=0)
    end_point = F.normalize(end_point, p=2, dim=0)
    
    # Create target guidance
    num_timesteps = getattr(diffusion_model, 'num_timesteps', 1000)
    target_guidance = TargetGuidance(
        target=end_point,
        strength_schedule=lambda t: guidance_scale * (1.0 - t / num_timesteps)
    )
    
    # Create goal-directed diffusion sampler
    goal_diffusion = GoalDirectedDiffusion(
        diffusion_model=diffusion_model,
        device=device
    )
    
    # Prepare starting point with noise
    # Start closer to the starting point but with some randomness
    concept_dim = getattr(diffusion_model, 'concept_dim', start_point.shape[0])
    noise = torch.randn(num_samples, concept_dim, device=device)
    x_T = start_point.unsqueeze(0).expand(num_samples, -1) + noise_schedule(0.0) * noise
    x_T = F.normalize(x_T, p=2, dim=1)
    
    # Sample with guidance
    return goal_diffusion.sample_with_guidance(
        guidance_fn=target_guidance,
        batch_size=num_samples,
        steps=num_steps,
        x_T=x_T,
        return_trajectory=return_trajectory,
        show_progress=show_progress
    )


def reverse_process_with_guidance(
    diffusion_model,  # Changed from ConceptualDiffusion type hint
    guidance_fn: GuidanceFunctionBase,
    x_T: torch.Tensor,
    num_steps: int = None,
    clip_samples: bool = True,
    intermediate_freq: int = 0,
    return_all: bool = False,
    show_progress: bool = False
) -> Union[torch.Tensor, Tuple[torch.Tensor, List[torch.Tensor]]]:
    """
    Run the reverse diffusion process with guidance.
    
    Args:
        diffusion_model: Trained diffusion model
        guidance_fn: Guidance function to direct the diffusion
        x_T: Starting noise [batch_size, concept_dim]
        num_steps: Number of diffusion steps (defaults to model's num_timesteps)
        clip_samples: Whether to clip intermediate samples
        intermediate_freq: Frequency to save intermediate samples (0 = no intermediates)
        return_all: Whether to return all intermediate steps
        show_progress: Whether to show progress bar
        
    Returns:
        If return_all is False: Final sample tensor [batch_size, concept_dim]
        If return_all is True: Tuple of (final_sample, intermediate_samples)
    """
    if diffusion_model is None:
        raise RuntimeError("Diffusion model not available")
        
    # Set number of steps
    if num_steps is None:
        num_steps = getattr(diffusion_model, 'num_timesteps', 1000)
        
    device = x_T.device
    batch_size = x_T.shape[0]
    
    # Initialize current sample
    x_t = x_T.clone()
    
    # Create timestep schedule (evenly spaced)
    num_timesteps = getattr(diffusion_model, 'num_timesteps', 1000)
    timesteps = torch.linspace(
        num_timesteps - 1, 0, num_steps, 
        dtype=torch.long, device=device
    )
    
    # Setup to track intermediates if requested
    intermediates = []
    
    # Setup progress bar if requested
    timestep_iter = tqdm(timesteps, desc="Reverse Diffusion", disable=not show_progress)
    
    # Sampling loop
    for i, t in enumerate(timestep_iter):
        # Expand t for batch
        t_batch = t.expand(batch_size)
        
        # Get guidance direction
        guidance = guidance_fn(x_t, t)
        
        # Save intermediate if requested
        if return_all or (intermediate_freq > 0 and i % intermediate_freq == 0):
            intermediates.append(x_t.detach().clone())
        
        # Denoise one step with guidance
        with torch.no_grad():
            # Check if model has noise_predictor method
            if hasattr(diffusion_model, 'noise_predictor'):
                # Predict noise
                predicted_noise = diffusion_model.noise_predictor(x_t, t_batch)
                
                # Apply guidance to predicted noise
                predicted_noise = predicted_noise - guidance
                
                # Denoise one step
                if hasattr(diffusion_model, 'denoise_step'):
                    x_t = diffusion_model.denoise_step(x_t, predicted_noise, t)
                else:
                    # Fallback: simple denoising
                    x_t = x_t - predicted_noise * 0.01
            else:
                # Fallback: apply guidance directly
                x_t = x_t + guidance * 0.01
                
            # Clip if requested
            if clip_samples and hasattr(diffusion_model, 'clip_range') and diffusion_model.clip_range:
                x_t = torch.clamp(x_t, -diffusion_model.clip_range, diffusion_model.clip_range)
                
        # Update guidance
        guidance_fn.update()
    
    # Normalize final samples
    x_0 = F.normalize(x_t, p=2, dim=1)
    
    if return_all:
        return x_0, intermediates
    else:
        return x_0


class ReverseDiffusionReasoning:
    """
    Implements reasoning from desired outcomes back to potential solutions using guided diffusion.
    """
    
    def __init__(
        self, 
        diffusion_model,  # Changed from ConceptualDiffusion type hint
        thought_space,    # Changed from ThoughtLatentSpace type hint
        device: str = 'cpu'
    ):
        """
        Initialize reverse diffusion reasoning.
        
        Args:
            diffusion_model: Trained diffusion model
            thought_space: Thought latent space for concept operations
            device: Device to run computations on ('cpu' or 'cuda')
        """
        self.diffusion_model = diffusion_model
        self.thought_space = thought_space
        self.device = device
        
        logger.info("Initialized ReverseDiffusionReasoning")
    
    def goal_directed_reasoning(
        self, 
        start_state: Optional[torch.Tensor] = None,
        goal_state: torch.Tensor = None,
        constraints: Optional[List[Callable]] = None,
        guidance_strength: float = 1.0,
        num_reasoning_paths: int = 5,
        num_steps: int = None,
        return_intermediate: bool = False,
        show_progress: bool = False
    ) -> Union[torch.Tensor, Tuple[torch.Tensor, List[torch.Tensor]]]:
        """
        Generate reasoning paths from start to goal using guided diffusion.
        
        Args:
            start_state: Optional starting concept (if None, use random)
            goal_state: Goal concept to reason toward
            constraints: Optional list of constraint functions
            guidance_strength: Strength of guidance
            num_reasoning_paths: Number of paths to generate
            num_steps: Number of diffusion steps (defaults to half of diffusion_model's steps)
            return_intermediate: Whether to return intermediate steps
            show_progress: Whether to show progress bar
            
        Returns:
            If return_intermediate is False: Tensor of solution concepts
            If return_intermediate is True: Tuple of (solution concepts, intermediate steps)
        """
        if self.diffusion_model is None:
            raise RuntimeError("Diffusion model not available")
            
        if goal_state is None and not constraints:
            raise ValueError("Must provide either goal_state or constraints")
            
        # Set up number of steps
        if num_steps is None:
            num_timesteps = getattr(self.diffusion_model, 'num_timesteps', 1000)
            num_steps = num_timesteps // 2
            
        # Prepare guidance functions
        guidance_functions = []
        
        if goal_state is not None:
            # Add goal-directed guidance
            num_timesteps = getattr(self.diffusion_model, 'num_timesteps', 1000)
            goal_guidance = TargetGuidance(
                target=goal_state.to(self.device), 
                strength_schedule=lambda t: 1.0 - (t / num_timesteps)  # Stronger as t decreases
            )
            guidance_functions.append((goal_guidance, 1.0))
            
        if constraints:
            # Add constraint-based guidance
            for i, constraint in enumerate(constraints):
                constraint_guidance = ConstraintGuidance(
                    constraint_fn=constraint,
                    strength=1.0,
                    strength_schedule=lambda t: 1.0  # Constant strength
                )
                guidance_functions.append((constraint_guidance, 1.0))
                
        # Create composite guidance
        guidance = CompositeGuidance(guidance_functions)
        
        # Start from random noise or provided start_state
        if start_state is None:
            # Start from random noise
            concept_dim = getattr(self.diffusion_model, 'concept_dim', 256)
            x_T = torch.randn(num_reasoning_paths, concept_dim, device=self.device)
        else:
            # Start with variations of the start state
            x_T = start_state.unsqueeze(0).expand(num_reasoning_paths, -1).clone()
            
            # Add small noise to create variations
            noise_scale = 0.1
            x_T = x_T + noise_scale * torch.randn_like(x_T)
            
        # Setup to track intermediate steps if requested
        intermediates = [] if return_intermediate else None
        
        # Sample with guidance using GoalDirectedDiffusion
        goal_diffusion = GoalDirectedDiffusion(
            diffusion_model=self.diffusion_model,
            device=self.device
        )
        
        results = goal_diffusion.sample_with_guidance(
            guidance_fn=guidance,
            batch_size=num_reasoning_paths,
            steps=num_steps,
            x_T=x_T,
            show_progress=show_progress
        )
        
        # Normalize results
        results = F.normalize(results, p=2, dim=1)
        
        if return_intermediate:
            return results, intermediates
        else:
            return results
    
    def reason_with_constraints(
        self, 
        constraints: List[Callable],
        start_embedding: Optional[torch.Tensor] = None,
        batch_size: int = 10,
        num_steps: int = None,
        guidance_strength: float = 1.0,
        show_progress: bool = False
    ) -> torch.Tensor:
        """
        Find concepts that satisfy multiple constraints through guided diffusion.
        
        Args:
            constraints: List of constraint functions (each returns satisfaction scores [0, 1])
            start_embedding: Optional starting point (if None, use random noise)
            batch_size: Number of samples to generate
            num_steps: Number of diffusion steps (defaults to half of diffusion_model's steps)
            guidance_strength: Strength of guidance
            show_progress: Whether to show progress bar
            
        Returns:
            Tensor of concepts that satisfy constraints [batch_size, dimension]
        """
        # Create composite constraint guidance
        guidance_functions = []
        for constraint in constraints:
            constraint_guidance = ConstraintGuidance(
                constraint_fn=constraint,
                strength=1.0
            )
            guidance_functions.append((constraint_guidance, 1.0))
            
        guidance = CompositeGuidance(guidance_functions)
        
        # Set up number of steps
        if num_steps is None:
            num_steps = self.diffusion_model.num_timesteps // 2
            
        # Generate samples with guidance
        results = self.diffusion_model.sample(
            batch_size=batch_size,
            steps=num_steps,
            x_T=start_embedding,
            guide_scale=guidance_strength,
            guidance_fn=guidance,
            show_progress=show_progress
        )
        
        # Normalize results
        results = F.normalize(results, p=2, dim=1)
        
        return results
    
    def interpolate_reasoning_path(
        self, 
        start_state: torch.Tensor,
        goal_state: torch.Tensor,
        num_steps: int = 10,
        smoothing: float = 0.0
    ) -> List[torch.Tensor]:
        """
        Generate a reasoning path by interpolating between start and goal states.
        
        Args:
            start_state: Starting concept embedding
            goal_state: Goal concept embedding
            num_steps: Number of interpolation steps
            smoothing: Amount of noise to add for exploration (0.0 = linear path)
            
        Returns:
            List of concept embeddings forming a reasoning path
        """
        # Normalize inputs
        start_state = F.normalize(start_state, p=2, dim=0)
        goal_state = F.normalize(goal_state, p=2, dim=0)
        
        path = []
        for i in range(num_steps):
            # Interpolation weight
            t = i / (num_steps - 1)
            
            # Spherical linear interpolation (SLERP)
            omega = torch.acos(torch.dot(start_state, goal_state).clamp(-1, 1))
            if omega.abs() < 1e-5:
                # Start and goal are too close; use linear interpolation
                step = (1 - t) * start_state + t * goal_state
            else:
                so = torch.sin(omega)
                step = (torch.sin((1 - t) * omega) / so) * start_state + (torch.sin(t * omega) / so) * goal_state
                
            # Add noise if requested
            if smoothing > 0:
                noise = torch.randn_like(step) * smoothing
                step = step + noise
                step = F.normalize(step, p=2, dim=0)
                
            path.append(step)
            
        return path
    
    def generate_exploration_paths(
        self, 
        concept: torch.Tensor,
        num_paths: int = 5,
        path_length: int = 10,
        exploration_scale: float = 0.2,
        guidance_strength: float = 0.5,
        constraint: Optional[Callable] = None
    ) -> List[List[torch.Tensor]]:
        """
        Generate multiple exploration paths from a concept for divergent thinking.
        
        Args:
            concept: Starting concept embedding
            num_paths: Number of paths to generate
            path_length: Number of steps in each path
            exploration_scale: Scale of exploration (higher = more diverse)
            guidance_strength: Strength of constraint guidance (if provided)
            constraint: Optional constraint function to guide exploration
            
        Returns:
            List of exploration paths (each path is a list of concept embeddings)
        """
        # Normalize concept
        concept = F.normalize(concept, p=2, dim=0)
        
        # Set up guidance if constraint provided
        guidance = None
        if constraint is not None:
            guidance = ConstraintGuidance(constraint_fn=constraint, strength=guidance_strength)
        
        paths = []
        for _ in range(num_paths):
            path = [concept]
            current = concept.clone().unsqueeze(0)
            
            for step in range(path_length):
                # Add noise for exploration
                noise = torch.randn_like(current) * exploration_scale * (step + 1) / path_length
                noisy = current + noise
                noisy = F.normalize(noisy, p=2, dim=1)
                
                # Apply constraint guidance if provided
                if guidance is not None:
                    t = torch.zeros(1, device=self.device)  # Dummy timestep
                    direction = guidance(noisy, t)
                    noisy = noisy + direction * guidance_strength
                    noisy = F.normalize(noisy, p=2, dim=1)
                
                # Add to path
                path.append(noisy.squeeze(0))
                current = noisy
                
            paths.append(path)
            
        return paths
    
    def analogy_completion(
        self,
        a: torch.Tensor,
        b: torch.Tensor,
        c: torch.Tensor,
        num_samples: int = 5,
        num_steps: int = None,
        guidance_strength: float = 1.0,
        exploration_scale: float = 0.1,
        show_progress: bool = False
    ) -> torch.Tensor:
        """
        Complete analogies of the form "A is to B as C is to ?"
        
        Args:
            a: First concept in the analogy
            b: Second concept in the analogy
            c: Third concept in the analogy
            num_samples: Number of samples to generate
            num_steps: Number of diffusion steps
            guidance_strength: Strength of analogy guidance
            exploration_scale: Scale of exploration variation
            show_progress: Whether to show progress bar
            
        Returns:
            Tensor of analogy completion candidates [num_samples, dimension]
        """
        # Normalize inputs
        a = F.normalize(a, p=2, dim=0)
        b = F.normalize(b, p=2, dim=0)
        c = F.normalize(c, p=2, dim=0)
        
        # Compute analogy direction (relationship between a and b)
        relation_vector = b - a
        
        # Create target by applying relation to c
        target = c + relation_vector
        target = F.normalize(target, p=2, dim=0)
        
        # Add exploration scale to create variations in starting points
        x_T = c.unsqueeze(0).expand(num_samples, -1) + exploration_scale * torch.randn(num_samples, c.size(0), device=c.device)
        x_T = F.normalize(x_T, p=2, dim=1)
        
        # Create target guidance
        guidance = TargetGuidance(
            target=target,
            strength_schedule=lambda t: guidance_strength * (1.0 - t / self.diffusion_model.num_timesteps)
        )
        
        # Set up number of steps
        if num_steps is None:
            num_steps = self.diffusion_model.num_timesteps // 2
            
        # Sample with guidance
        results = self.diffusion_model.sample(
            batch_size=num_samples,
            steps=num_steps,
            x_T=x_T,
            guide_scale=guidance_strength,
            guidance_fn=guidance,
            show_progress=show_progress
        )
        
        # Normalize results
        results = F.normalize(results, p=2, dim=1)
        
        return results
    
    def counterfactual_reasoning(
        self,
        factual_concept: torch.Tensor,
        modification_fn: Callable[[torch.Tensor], torch.Tensor],
        num_samples: int = 5,
        num_steps: int = None,
        guidance_strength: float = 1.0,
        exploration_scale: float = 0.1,
        show_progress: bool = False
    ) -> torch.Tensor:
        """
        Generate counterfactual variations of a concept through guided diffusion.
        
        Args:
            factual_concept: Base concept to generate counterfactuals for
            modification_fn: Function that modifies concepts toward counterfactual
            num_samples: Number of samples to generate
            num_steps: Number of diffusion steps
            guidance_strength: Strength of modification guidance
            exploration_scale: Scale of exploration variation
            show_progress: Whether to show progress bar
            
        Returns:
            Tensor of counterfactual concepts [num_samples, dimension]
        """
        # Normalize input
        factual_concept = F.normalize(factual_concept, p=2, dim=0)
        
        # Create variations of starting point
        x_T = factual_concept.unsqueeze(0).expand(num_samples, -1) + exploration_scale * torch.randn(num_samples, factual_concept.size(0), device=factual_concept.device)
        x_T = F.normalize(x_T, p=2, dim=1)
        
        # Create modification guidance
        guidance = AnalyticGuidance(
            guidance_fn=modification_fn,
            strength=guidance_strength,
            strength_schedule=lambda t: (1.0 - t / self.diffusion_model.num_timesteps)
        )
        
        # Set up number of steps
        if num_steps is None:
            num_steps = self.diffusion_model.num_timesteps // 2
            
        # Sample with guidance
        results = self.diffusion_model.sample(
            batch_size=num_samples,
            steps=num_steps,
            x_T=x_T,
            guide_scale=guidance_strength,
            guidance_fn=guidance,
            show_progress=show_progress
        )
        
        # Normalize results
        results = F.normalize(results, p=2, dim=1)
        
        return results