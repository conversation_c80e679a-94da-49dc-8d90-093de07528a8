#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ULTRA: Ultimate Learning & Thought Reasoning Architecture
Master Test Runner for Diffusion Reasoning Module - FIXED VERSION

This script coordinates and executes all test suites for the diffusion reasoning
module, providing comprehensive testing coverage, detailed reporting, and 
production-grade validation of the entire system.

FIXES APPLIED:
1. Corrected module import mechanism
2. Fixed test suite module names
3. Improved error handling for missing modules
4. Added fallback test execution methods

Author: ULTRA Development Team
Version: 1.0.1 (Fixed)
License: MIT
"""

import os
import sys
import time
import json
import logging
import traceback
import subprocess
import importlib
from pathlib import Path
from typing import Dict, List, Tuple, Any, Optional
from dataclasses import dataclass, field
import argparse
import multiprocessing as mp
from concurrent.futures import ProcessPoolExecutor, ThreadPoolExecutor
import psutil
import torch

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('diffusion_reasoning_tests.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Constants
TIMEOUT_SECONDS = 1800  # 30 minutes per test suite
MAX_PARALLEL_SUITES = 4
MEMORY_LIMIT_GB = 16
CPU_LIMIT_PERCENT = 90

@dataclass
class TestSuiteResult:
    """Container for test suite results."""
    suite_name: str
    success: bool
    execution_time: float
    tests_run: int = 0
    tests_passed: int = 0
    tests_failed: int = 0
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    performance_metrics: Dict[str, float] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class MasterTestConfiguration:
    """Configuration for master test execution."""
    run_parallel: bool = True
    max_workers: int = MAX_PARALLEL_SUITES
    timeout_per_suite: int = TIMEOUT_SECONDS
    generate_detailed_report: bool = True
    run_stress_tests: bool = True
    run_integration_tests: bool = True
    save_intermediate_results: bool = True
    device_preference: str = 'auto'  # 'auto', 'cpu', 'cuda'
    memory_limit_gb: float = MEMORY_LIMIT_GB

class DiffusionReasoningTestOrchestrator:
    """
    Master orchestrator for all diffusion reasoning tests.
    
    This class coordinates the execution of all test suites, manages resources,
    handles failures, and generates comprehensive reports.
    """
    
    def __init__(self, config: MasterTestConfiguration):
        self.config = config
        self.results: List[TestSuiteResult] = []
        self.start_time = time.time()
        
        # Setup test environment
        self.setup_test_environment()
        
        # Define test suites
        self.test_suites = self.define_test_suites()
        
        logger.info(f"Initialized test orchestrator with {len(self.test_suites)} test suites")
    
    def setup_test_environment(self):
        """Setup the test environment."""
        logger.info("Setting up test environment...")
        
        # Check system resources
        system_info = self.get_system_info()
        logger.info(f"System info: {system_info}")
        
        # Configure device
        if self.config.device_preference == 'auto':
            self.device = 'cuda' if torch.cuda.is_available() else 'cpu'
        else:
            self.device = self.config.device_preference
        
        logger.info(f"Using device: {self.device}")
        
        # Set environment variables
        os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'max_split_size_mb:512'
        if self.device == 'cpu':
            os.environ['CUDA_VISIBLE_DEVICES'] = ''
        
        # Create results directory
        self.results_dir = Path('test_results')
        self.results_dir.mkdir(exist_ok=True)
        
        logger.info("Test environment setup complete")
    
    def define_test_suites(self) -> List[Dict[str, Any]]:
        """Define all test suites to be executed."""
        suites = [
            {
                'name': 'bayesian_uncertainty',
                'module': 'test_bayesian_uncertainty',  # FIXED: Corrected module name
                'function': 'main',  # FIXED: Standard unittest main function
                'description': 'Bayesian Uncertainty Quantification Tests',
                'priority': 1,
                'estimated_time': 300,  # 5 minutes
                'memory_intensive': False,
                'use_subprocess': True  # ADDED: Fallback to subprocess execution
            },
            {
                'name': 'conceptual_diffusion',
                'module': 'test_conceptual_diffusion',
                'function': 'main',  # FIXED: Standard unittest main function
                'description': 'Conceptual Diffusion Process Tests',
                'priority': 1,
                'estimated_time': 400,  # 6.7 minutes
                'memory_intensive': True,
                'use_subprocess': True
            },
            {
                'name': 'thought_latent_space',
                'module': 'test_thought_latent_space',
                'function': 'main',  # FIXED: Standard unittest main function
                'description': 'Thought Latent Space Tests',
                'priority': 1,
                'estimated_time': 350,  # 5.8 minutes
                'memory_intensive': True,
                'use_subprocess': True
            },
            {
                'name': 'reverse_diffusion_reasoning',
                'module': 'test_reverse_diffusion_reasoning',
                'function': 'main',  # FIXED: Standard unittest main function
                'description': 'Reverse Diffusion Reasoning Tests',
                'priority': 2,
                'estimated_time': 450,  # 7.5 minutes
                'memory_intensive': True,
                'use_subprocess': True
            },
            {
                'name': 'probabilistic_inference',
                'module': 'test_probabilistic_inference_engine',
                'function': 'main',  # FIXED: Standard unittest main function
                'description': 'Probabilistic Inference Engine Tests',
                'priority': 2,
                'estimated_time': 500,  # 8.3 minutes
                'memory_intensive': True,
                'use_subprocess': True
            },
            {
                'name': 'comprehensive_integration',
                'module': 'test_comprehensive_integration',
                'function': 'main',  # FIXED: Standard unittest main function
                'description': 'Comprehensive Integration Tests',
                'priority': 3,
                'estimated_time': 600,  # 10 minutes
                'memory_intensive': True,
                'requires_all_components': True,
                'use_subprocess': True
            }
        ]
        
        # Filter based on configuration
        if not self.config.run_integration_tests:
            suites = [s for s in suites if s['name'] != 'comprehensive_integration']
        
        return suites
    
    def run_all_tests(self) -> Dict[str, Any]:
        """Execute all test suites and return comprehensive results."""
        logger.info("="*80)
        logger.info("STARTING ULTRA DIFFUSION REASONING MODULE TESTS")
        logger.info("="*80)
        
        total_estimated_time = sum(suite['estimated_time'] for suite in self.test_suites)
        logger.info(f"Estimated total execution time: {total_estimated_time // 60} minutes {total_estimated_time % 60} seconds")
        
        # Execute test suites
        if self.config.run_parallel:
            logger.info(f"Running tests in parallel with {self.config.max_workers} workers")
            self.run_parallel_tests()
        else:
            logger.info("Running tests sequentially")
            self.run_sequential_tests()
        
        # Generate comprehensive report
        final_report = self.generate_final_report()
        
        # Log summary
        self.log_test_summary()
        
        return final_report
    
    def run_sequential_tests(self):
        """Run test suites sequentially."""
        for suite_info in sorted(self.test_suites, key=lambda x: x['priority']):
            try:
                logger.info(f"Running {suite_info['description']}...")
                result = self.execute_test_suite(suite_info)
                self.results.append(result)
                
                # Save intermediate results
                if self.config.save_intermediate_results:
                    self.save_intermediate_result(result)
                
                # Memory cleanup
                self.cleanup_memory()
                
            except Exception as e:
                logger.error(f"Failed to execute {suite_info['name']}: {e}")
                failed_result = TestSuiteResult(
                    suite_name=suite_info['name'],
                    success=False,
                    execution_time=0.0,
                    errors=[str(e)]
                )
                self.results.append(failed_result)
    
    def run_parallel_tests(self):
        """Run test suites in parallel where possible."""
        # Group suites by priority and memory requirements
        priority_groups = {}
        for suite in self.test_suites:
            priority = suite['priority']
            if priority not in priority_groups:
                priority_groups[priority] = []
            priority_groups[priority].append(suite)
        
        # Execute priority groups in order, but parallelize within groups
        for priority in sorted(priority_groups.keys()):
            group_suites = priority_groups[priority]
            logger.info(f"Executing priority {priority} tests ({len(group_suites)} suites)")
            
            # Separate memory-intensive and regular suites
            memory_intensive = [s for s in group_suites if s.get('memory_intensive', False)]
            regular_suites = [s for s in group_suites if not s.get('memory_intensive', False)]
            
            # Run memory-intensive suites first, with limited parallelism
            if memory_intensive:
                max_memory_workers = min(2, self.config.max_workers)
                with ProcessPoolExecutor(max_workers=max_memory_workers) as executor:
                    future_to_suite = {
                        executor.submit(self.execute_test_suite_wrapper, suite): suite
                        for suite in memory_intensive
                    }
                    
                    for future in future_to_suite:
                        try:
                            result = future.result(timeout=self.config.timeout_per_suite)
                            self.results.append(result)
                        except Exception as e:
                            suite = future_to_suite[future]
                            logger.error(f"Suite {suite['name']} failed: {e}")
                            failed_result = TestSuiteResult(
                                suite_name=suite['name'],
                                success=False,
                                execution_time=0.0,
                                errors=[str(e)]
                            )
                            self.results.append(failed_result)
            
            # Run regular suites with full parallelism
            if regular_suites:
                with ThreadPoolExecutor(max_workers=self.config.max_workers) as executor:
                    future_to_suite = {
                        executor.submit(self.execute_test_suite_wrapper, suite): suite
                        for suite in regular_suites
                    }
                    
                    for future in future_to_suite:
                        try:
                            result = future.result(timeout=self.config.timeout_per_suite)
                            self.results.append(result)
                        except Exception as e:
                            suite = future_to_suite[future]
                            logger.error(f"Suite {suite['name']} failed: {e}")
                            failed_result = TestSuiteResult(
                                suite_name=suite['name'],
                                success=False,
                                execution_time=0.0,
                                errors=[str(e)]
                            )
                            self.results.append(failed_result)
            
            # Cleanup between priority groups
            self.cleanup_memory()
    
    def execute_test_suite_wrapper(self, suite_info: Dict[str, Any]) -> TestSuiteResult:
        """Wrapper for executing test suite in separate process."""
        return self.execute_test_suite(suite_info)
    
    def execute_test_suite(self, suite_info: Dict[str, Any]) -> TestSuiteResult:
        """Execute a single test suite with improved error handling."""
        suite_name = suite_info['name']
        logger.info(f"Executing test suite: {suite_name}")
        
        start_time = time.time()
        
        try:
            # Try multiple execution methods
            success = False
            error_messages = []
            
            # Method 1: Direct import and execution
            try:
                success = self._execute_direct_import(suite_info)
                if success:
                    logger.info(f"Suite {suite_name} executed successfully via direct import")
                else:
                    error_messages.append("Direct import execution returned False")
            except Exception as e:
                error_messages.append(f"Direct import failed: {str(e)}")
                logger.warning(f"Direct import failed for {suite_name}: {e}")
            
            # Method 2: Subprocess execution (fallback)
            if not success and suite_info.get('use_subprocess', False):
                try:
                    success = self._execute_subprocess(suite_info)
                    if success:
                        logger.info(f"Suite {suite_name} executed successfully via subprocess")
                    else:
                        error_messages.append("Subprocess execution returned False")
                except Exception as e:
                    error_messages.append(f"Subprocess execution failed: {str(e)}")
                    logger.warning(f"Subprocess execution failed for {suite_name}: {e}")
            
            # Method 3: Unittest discovery (last resort)
            if not success:
                try:
                    success = self._execute_unittest_discovery(suite_info)
                    if success:
                        logger.info(f"Suite {suite_name} executed successfully via unittest discovery")
                    else:
                        error_messages.append("Unittest discovery returned False")
                except Exception as e:
                    error_messages.append(f"Unittest discovery failed: {str(e)}")
                    logger.warning(f"Unittest discovery failed for {suite_name}: {e}")
            
            execution_time = time.time() - start_time
            
            # Create result
            result = TestSuiteResult(
                suite_name=suite_name,
                success=success,
                execution_time=execution_time,
                performance_metrics={
                    'execution_time': execution_time,
                    'memory_usage': self.get_memory_usage()
                },
                errors=error_messages if not success else [],
                metadata={
                    'execution_methods_tried': ['direct_import', 'subprocess', 'unittest_discovery'],
                    'final_method': 'unknown' if not success else 'successful'
                }
            )
            
            logger.info(f"Suite {suite_name} completed in {execution_time:.2f}s - {'PASSED' if success else 'FAILED'}")
            
            return result
            
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"Suite {suite_name} failed with exception: {e}")
            logger.error(traceback.format_exc())
            
            return TestSuiteResult(
                suite_name=suite_name,
                success=False,
                execution_time=execution_time,
                errors=[str(e), traceback.format_exc()]
            )
    
    def _execute_direct_import(self, suite_info: Dict[str, Any]) -> bool:
        """Execute test suite via direct import - FIXED VERSION."""
        module_name = suite_info['module']
        function_name = suite_info['function']
        
        try:
            # Method 1: Try importlib (preferred)
            try:
                module = importlib.import_module(module_name)
                logger.info(f"Successfully imported {module_name} via importlib")
            except ImportError:
                # Method 2: Try adding current directory to path and importing
                current_dir = os.getcwd()
                if current_dir not in sys.path:
                    sys.path.insert(0, current_dir)
                
                # Try different potential paths
                potential_paths = [
                    '.',
                    'test_diffusion_reasoning',
                    'ultra/diffusion_reasoning',
                    'diffusion_reasoning',
                ]
                
                module = None
                for path in potential_paths:
                    try:
                        if path != '.':
                            full_path = os.path.join(current_dir, path)
                            if full_path not in sys.path:
                                sys.path.insert(0, full_path)
                        
                        module = importlib.import_module(module_name)
                        logger.info(f"Successfully imported {module_name} from path: {path}")
                        break
                    except ImportError:
                        continue
                
                if module is None:
                    raise ImportError(f"Could not import {module_name} from any path")
            
            # Execute the test function
            if hasattr(module, function_name):
                test_function = getattr(module, function_name)
                result = test_function()
                return bool(result) if result is not None else True
            else:
                # Try to run as unittest module
                import unittest
                loader = unittest.TestLoader()
                suite = loader.loadTestsFromModule(module)
                runner = unittest.TextTestRunner(verbosity=2)
                test_result = runner.run(suite)
                return test_result.wasSuccessful()
                
        except Exception as e:
            logger.error(f"Direct import execution failed: {e}")
            raise
    
    def _execute_subprocess(self, suite_info: Dict[str, Any]) -> bool:
        """Execute test suite via subprocess."""
        module_name = suite_info['module']
        
        try:
            # Try to run as a Python module
            cmd = [sys.executable, '-m', module_name]
            result = subprocess.run(
                cmd,
                cwd=os.getcwd(),
                capture_output=True,
                text=True,
                timeout=self.config.timeout_per_suite
            )
            
            if result.returncode == 0:
                return True
            
            # If module execution failed, try running as script
            script_name = f"{module_name}.py"
            if os.path.exists(script_name):
                cmd = [sys.executable, script_name]
                result = subprocess.run(
                    cmd,
                    cwd=os.getcwd(),
                    capture_output=True,
                    text=True,
                    timeout=self.config.timeout_per_suite
                )
                return result.returncode == 0
            
            return False
            
        except Exception as e:
            logger.error(f"Subprocess execution failed: {e}")
            return False
    
    def _execute_unittest_discovery(self, suite_info: Dict[str, Any]) -> bool:
        """Execute test suite via unittest discovery."""
        try:
            import unittest
            
            # Try unittest discovery
            loader = unittest.TestLoader()
            start_dir = '.'
            pattern = f"{suite_info['module']}.py"
            
            suite = loader.discover(start_dir, pattern=pattern)
            runner = unittest.TextTestRunner(verbosity=2)
            result = runner.run(suite)
            
            return result.wasSuccessful()
            
        except Exception as e:
            logger.error(f"Unittest discovery failed: {e}")
            return False
    
    def generate_final_report(self) -> Dict[str, Any]:
        """Generate comprehensive final report."""
        logger.info("Generating final test report...")
        
        total_execution_time = time.time() - self.start_time
        total_suites = len(self.results)
        passed_suites = sum(1 for r in self.results if r.success)
        failed_suites = total_suites - passed_suites
        
        # Compile detailed metrics
        suite_metrics = {}
        total_tests = 0
        total_passed = 0
        total_failed = 0
        
        for result in self.results:
            suite_metrics[result.suite_name] = {
                'success': result.success,
                'execution_time': result.execution_time,
                'tests_run': result.tests_run,
                'tests_passed': result.tests_passed,
                'tests_failed': result.tests_failed,
                'error_count': len(result.errors),
                'warning_count': len(result.warnings)
            }
            
            total_tests += result.tests_run
            total_passed += result.tests_passed
            total_failed += result.tests_failed
        
        # System performance analysis
        performance_analysis = {
            'total_execution_time': total_execution_time,
            'average_suite_time': total_execution_time / total_suites if total_suites > 0 else 0,
            'fastest_suite': min(self.results, key=lambda r: r.execution_time).suite_name if self.results else None,
            'slowest_suite': max(self.results, key=lambda r: r.execution_time).suite_name if self.results else None,
            'system_resources': self.get_system_info()
        }
        
        # Error analysis
        all_errors = []
        for result in self.results:
            all_errors.extend(result.errors)
        
        error_analysis = {
            'total_errors': len(all_errors),
            'unique_errors': len(set(all_errors)),
            'error_categories': self.categorize_errors(all_errors),
            'most_common_errors': self.get_most_common_errors(all_errors)
        }
        
        # Generate recommendations
        recommendations = self.generate_recommendations()
        
        # Compile final report
        final_report = {
            'test_execution_summary': {
                'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
                'total_execution_time': total_execution_time,
                'total_test_suites': total_suites,
                'passed_suites': passed_suites,
                'failed_suites': failed_suites,
                'suite_success_rate': passed_suites / total_suites if total_suites > 0 else 0,
                'total_individual_tests': total_tests,
                'total_passed_tests': total_passed,
                'total_failed_tests': total_failed,
                'individual_test_success_rate': total_passed / total_tests if total_tests > 0 else 0
            },
            'suite_details': suite_metrics,
            'performance_analysis': performance_analysis,
            'error_analysis': error_analysis,
            'recommendations': recommendations,
            'configuration': {
                'run_parallel': self.config.run_parallel,
                'max_workers': self.config.max_workers,
                'device': self.device,
                'memory_limit_gb': self.config.memory_limit_gb
            }
        }
        
        # Save report
        if self.config.generate_detailed_report:
            report_path = self.results_dir / 'final_test_report.json'
            with open(report_path, 'w') as f:
                json.dump(final_report, f, indent=2)
            
            logger.info(f"Detailed report saved to {report_path}")
        
        return final_report
    
    def save_intermediate_result(self, result: TestSuiteResult):
        """Save intermediate test result."""
        result_path = self.results_dir / f'{result.suite_name}_result.json'
        result_data = {
            'suite_name': result.suite_name,
            'success': result.success,
            'execution_time': result.execution_time,
            'tests_run': result.tests_run,
            'tests_passed': result.tests_passed,
            'tests_failed': result.tests_failed,
            'errors': result.errors,
            'warnings': result.warnings,
            'performance_metrics': result.performance_metrics,
            'metadata': result.metadata
        }
        
        with open(result_path, 'w') as f:
            json.dump(result_data, f, indent=2)
    
    def cleanup_memory(self):
        """Clean up memory between test suites."""
        import gc
        gc.collect()
        
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
            torch.cuda.synchronize()
    
    def get_system_info(self) -> Dict[str, Any]:
        """Get system information."""
        return {
            'cpu_count': mp.cpu_count(),
            'cpu_percent': psutil.cpu_percent(),
            'memory_total_gb': psutil.virtual_memory().total / (1024**3),
            'memory_available_gb': psutil.virtual_memory().available / (1024**3),
            'memory_percent': psutil.virtual_memory().percent,
            'gpu_available': torch.cuda.is_available(),
            'gpu_count': torch.cuda.device_count() if torch.cuda.is_available() else 0,
            'gpu_memory_gb': torch.cuda.get_device_properties(0).total_memory / (1024**3) if torch.cuda.is_available() else 0
        }
    
    def get_memory_usage(self) -> float:
        """Get current memory usage in GB."""
        return psutil.virtual_memory().used / (1024**3)
    
    def categorize_errors(self, errors: List[str]) -> Dict[str, int]:
        """Categorize errors by type."""
        categories = {
            'mathematical': 0,
            'performance': 0,
            'memory': 0,
            'integration': 0,
            'cuda': 0,
            'import': 0,
            'other': 0
        }
        
        for error in errors:
            error_lower = error.lower()
            if any(keyword in error_lower for keyword in ['nan', 'inf', 'numerical', 'mathematical', 'consistency']):
                categories['mathematical'] += 1
            elif any(keyword in error_lower for keyword in ['timeout', 'slow', 'performance', 'time']):
                categories['performance'] += 1
            elif any(keyword in error_lower for keyword in ['memory', 'out of memory', 'oom']):
                categories['memory'] += 1
            elif any(keyword in error_lower for keyword in ['integration', 'component', 'workflow']):
                categories['integration'] += 1
            elif any(keyword in error_lower for keyword in ['cuda', 'gpu', 'device']):
                categories['cuda'] += 1
            elif any(keyword in error_lower for keyword in ['import', 'module', 'attribute']):
                categories['import'] += 1
            else:
                categories['other'] += 1
        
        return categories
    
    def get_most_common_errors(self, errors: List[str]) -> List[Tuple[str, int]]:
        """Get most common errors."""
        from collections import Counter
        error_counts = Counter(errors)
        return error_counts.most_common(5)
    
    def generate_recommendations(self) -> List[str]:
        """Generate recommendations based on test results."""
        recommendations = []
        
        if not self.results:
            recommendations.append("No test results to analyze")
            return recommendations
        
        # Analyze success rates
        success_rate = sum(1 for r in self.results if r.success) / len(self.results)
        
        if success_rate < 0.8:
            recommendations.append("Low test success rate - investigate failing components")
        elif success_rate < 0.9:
            recommendations.append("Good test success rate - consider addressing remaining failures")
        else:
            recommendations.append("Excellent test success rate - system is well-validated")
        
        # Analyze performance
        avg_execution_time = sum(r.execution_time for r in self.results) / len(self.results)
        
        if avg_execution_time > 600:  # 10 minutes
            recommendations.append("High average execution time - consider performance optimizations")
        elif avg_execution_time > 300:  # 5 minutes
            recommendations.append("Moderate execution time - acceptable for comprehensive testing")
        else:
            recommendations.append("Fast execution time - efficient test implementation")
        
        # Analyze errors
        all_errors = []
        for result in self.results:
            all_errors.extend(result.errors)
        
        if all_errors:
            error_categories = self.categorize_errors(all_errors)
            if error_categories['mathematical'] > 5:
                recommendations.append("Multiple mathematical errors detected - review numerical stability")
            if error_categories['memory'] > 0:
                recommendations.append("Memory issues detected - implement memory optimization")
            if error_categories['performance'] > 0:
                recommendations.append("Performance issues detected - optimize critical paths")
            if error_categories['import'] > 0:
                recommendations.append("Import errors detected - check module paths and dependencies")
        
        # Memory analysis
        system_info = self.get_system_info()
        if system_info['memory_percent'] > 80:
            recommendations.append("High memory usage - consider reducing memory footprint")
        
        return recommendations
    
    def log_test_summary(self):
        """Log comprehensive test summary."""
        logger.info("="*80)
        logger.info("ULTRA DIFFUSION REASONING MODULE - TEST EXECUTION COMPLETE")
        logger.info("="*80)
        
        total_suites = len(self.results)
        passed_suites = sum(1 for r in self.results if r.success)
        failed_suites = total_suites - passed_suites
        total_time = time.time() - self.start_time
        
        logger.info(f"Execution Summary:")
        logger.info(f"  Total Test Suites: {total_suites}")
        logger.info(f"  Passed Suites: {passed_suites}")
        logger.info(f"  Failed Suites: {failed_suites}")
        logger.info(f"  Success Rate: {passed_suites / total_suites:.1%}")
        logger.info(f"  Total Execution Time: {total_time // 60:.0f}m {total_time % 60:.0f}s")
        
        logger.info(f"\nSuite Details:")
        for result in self.results:
            status = "PASSED" if result.success else "FAILED"
            logger.info(f"  {result.suite_name}: {status} ({result.execution_time:.1f}s)")
        
        if failed_suites > 0:
            logger.warning(f"\nFailed Suites:")
            for result in self.results:
                if not result.success:
                    logger.warning(f"  {result.suite_name}: {len(result.errors)} errors")
                    for error in result.errors[:3]:  # Show first 3 errors
                        logger.warning(f"    - {error[:100]}...")
        
        logger.info("="*80)
        
        # IMPROVED: Add specific fix suggestions
        if failed_suites > 0:
            logger.info("SUGGESTED FIXES:")
            for result in self.results:
                if not result.success and 'import' in str(result.errors).lower():
                    logger.info(f"  For {result.suite_name}:")
                    logger.info(f"    - Check if test file exists: {result.suite_name}.py")
                    logger.info(f"    - Verify module is in Python path")
                    logger.info(f"    - Try running: python -m {result.suite_name}")

def create_argument_parser() -> argparse.ArgumentParser:
    """Create command-line argument parser."""
    parser = argparse.ArgumentParser(
        description="ULTRA Diffusion Reasoning Module - Master Test Runner (FIXED)",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python run_all_diffusion_reasoning_tests.py --parallel --max-workers 4
  python run_all_diffusion_reasoning_tests.py --sequential --device cpu
  python run_all_diffusion_reasoning_tests.py --quick --no-integration
        """
    )
    
    # Execution mode
    execution_group = parser.add_mutually_exclusive_group()
    execution_group.add_argument('--parallel', action='store_true', default=True,
                                help='Run tests in parallel (default)')
    execution_group.add_argument('--sequential', action='store_true',
                                help='Run tests sequentially')
    
    # Configuration options
    parser.add_argument('--max-workers', type=int, default=MAX_PARALLEL_SUITES,
                       help=f'Maximum parallel workers (default: {MAX_PARALLEL_SUITES})')
    parser.add_argument('--timeout', type=int, default=TIMEOUT_SECONDS,
                       help=f'Timeout per test suite in seconds (default: {TIMEOUT_SECONDS})')
    parser.add_argument('--device', choices=['auto', 'cpu', 'cuda'], default='auto',
                       help='Device preference (default: auto)')
    parser.add_argument('--memory-limit', type=float, default=MEMORY_LIMIT_GB,
                       help=f'Memory limit in GB (default: {MEMORY_LIMIT_GB})')
    
    # Test selection
    parser.add_argument('--no-integration', action='store_true',
                       help='Skip integration tests')
    parser.add_argument('--no-stress', action='store_true',
                       help='Skip stress tests')
    parser.add_argument('--quick', action='store_true',
                       help='Run only essential tests (no integration or stress)')
    
    # Output options
    parser.add_argument('--no-report', action='store_true',
                       help='Skip detailed report generation')
    parser.add_argument('--no-intermediate', action='store_true',
                       help='Skip saving intermediate results')
    parser.add_argument('--output-dir', type=str, default='test_results',
                       help='Output directory for results (default: test_results)')
    
    # Debugging
    parser.add_argument('--verbose', action='store_true',
                       help='Enable verbose logging')
    parser.add_argument('--debug', action='store_true',
                       help='Enable debug mode')
    
    return parser

def main():
    """Main execution function."""
    parser = create_argument_parser()
    args = parser.parse_args()
    
    # Configure logging based on arguments
    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)
    elif args.verbose:
        logging.getLogger().setLevel(logging.INFO)
    
    # Create configuration
    config = MasterTestConfiguration(
        run_parallel=args.parallel and not args.sequential,
        max_workers=args.max_workers,
        timeout_per_suite=args.timeout,
        generate_detailed_report=not args.no_report,
        run_integration_tests=not args.no_integration and not args.quick,
        run_stress_tests=not args.no_stress and not args.quick,
        save_intermediate_results=not args.no_intermediate,
        device_preference=args.device,
        memory_limit_gb=args.memory_limit
    )
    
    # Create orchestrator
    orchestrator = DiffusionReasoningTestOrchestrator(config)
    
    try:
        # Run all tests
        final_report = orchestrator.run_all_tests()
        
        # Determine exit code
        success_rate = final_report['test_execution_summary']['suite_success_rate']
        
        if success_rate >= 0.9:
            logger.info("All tests completed successfully!")
            return 0
        elif success_rate >= 0.7:
            logger.warning("Most tests passed, but some failures detected")
            return 1
        else:
            logger.error("Significant test failures detected")
            return 2
            
    except KeyboardInterrupt:
        logger.info("Test execution interrupted by user")
        return 130
    except Exception as e:
        logger.error(f"Fatal error during test execution: {e}")
        logger.error(traceback.format_exc())
        return 1

if __name__ == "__main__":
    exit(main())