#!/usr/bin/env python3
"""
ULTRA: Ultimate Learning & Thought Reasoning Architecture
Performance Benchmark for Conceptual Diffusion

This script provides comprehensive performance benchmarking for the CUDA
conceptual diffusion implementation, comparing CPU vs GPU performance
and measuring various aspects of the system.

Author: ULTRA Development Team
Version: 1.0.0
License: MIT
"""

import time
import argparse
import numpy as np
import sys
import os
from pathlib import Path
import json
import psutil
import platform
from typing import Dict, List, Tuple, Optional
import logging

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

try:
    from conceptual_diffusion_wrapper import ConceptualDiffusionPython, create_conceptual_diffusion
except ImportError as e:
    print(f"❌ Failed to import conceptual diffusion wrapper: {e}")
    print("Make sure the library is built. Run: make library")
    sys.exit(1)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class PerformanceBenchmark:
    """
    Comprehensive performance benchmark for ULTRA Conceptual Diffusion.
    """
    
    def __init__(self, config: Optional[Dict] = None):
        """Initialize the benchmark with given configuration."""
        self.config = config or {}
        self.results = {}
        self.system_info = self._get_system_info()
        
    def _get_system_info(self) -> Dict:
        """Collect system information for benchmarking context."""
        info = {
            'platform': platform.platform(),
            'processor': platform.processor(),
            'cpu_count': psutil.cpu_count(),
            'memory_total_gb': psutil.virtual_memory().total / (1024**3),
            'python_version': platform.python_version(),
        }
        
        # Try to get GPU information
        try:
            import subprocess
            result = subprocess.run(['nvidia-smi', '-q'], capture_output=True, text=True)
            if result.returncode == 0:
                # Extract basic GPU info
                lines = result.stdout.split('\n')
                gpu_info = {}
                for line in lines:
                    if 'Product Name' in line:
                        gpu_info['name'] = line.split(':')[1].strip()
                    elif 'Total' in line and 'MiB' in line:
                        gpu_info['memory'] = line.split(':')[1].strip()
                info['gpu'] = gpu_info
            else:
                info['gpu'] = 'Not available'
        except:
            info['gpu'] = 'nvidia-smi not found'
            
        return info
    
    def benchmark_initialization(self, force_cpu: bool = False) -> Dict:
        """Benchmark system initialization time."""
        logger.info(f"🔧 Benchmarking initialization ({'CPU' if force_cpu else 'Auto'})")
        
        config = {
            'concept_dim': self.config.get('concept_dim', 512),
            'batch_size': self.config.get('batch_size', 4),
            'num_timesteps': self.config.get('num_timesteps', 1000),
            'hierarchical_levels': self.config.get('hierarchical_levels', 4),
            'use_cuda': 0 if force_cpu else 1,
        }
        
        start_time = time.time()
        
        try:
            with create_conceptual_diffusion(config, auto_compile=True) as diffusion:
                init_time = time.time() - start_time
                
                device_type = 'CPU' if force_cpu or not diffusion.is_cuda_available() else 'GPU'
                
                result = {
                    'device': device_type,
                    'initialization_time': init_time,
                    'success': diffusion._is_initialized,
                    'config': config
                }
                
                logger.info(f"   ✅ {device_type} initialization: {init_time:.3f}s")
                return result
                
        except Exception as e:
            logger.error(f"   ❌ Initialization failed: {e}")
            return {
                'device': 'CPU' if force_cpu else 'Auto',
                'initialization_time': time.time() - start_time,
                'success': False,
                'error': str(e),
                'config': config
            }
    
    def benchmark_forward_diffusion(self, diffusion: ConceptualDiffusionPython, 
                                   num_iterations: int = 100) -> Dict:
        """Benchmark forward diffusion performance."""
        logger.info(f"🧪 Benchmarking forward diffusion ({num_iterations} iterations)")
        
        batch_size = diffusion.config.batch_size
        concept_dim = diffusion.config.concept_dim
        
        # Prepare test data
        concepts = np.random.randn(batch_size, concept_dim).astype(np.float32)
        timesteps = np.random.randint(0, diffusion.config.num_timesteps, num_iterations)
        
        # Warmup
        for _ in range(5):
            diffusion.forward_diffusion(concepts, 50)
        
        # Benchmark
        start_time = time.time()
        memory_before = psutil.Process().memory_info().rss / 1024 / 1024  # MB
        
        for i, timestep in enumerate(timesteps):
            result = diffusion.forward_diffusion(concepts, int(timestep))
            if i == 0:
                output_shape = result.shape
        
        end_time = time.time()
        memory_after = psutil.Process().memory_info().rss / 1024 / 1024  # MB
        
        total_time = end_time - start_time
        avg_time = total_time / num_iterations
        throughput = num_iterations / total_time
        
        result = {
            'total_time': total_time,
            'avg_time_per_iteration': avg_time,
            'throughput_ops_per_sec': throughput,
            'memory_usage_mb': memory_after - memory_before,
            'input_shape': concepts.shape,
            'output_shape': output_shape,
            'num_iterations': num_iterations
        }
        
        logger.info(f"   📊 Avg time: {avg_time*1000:.2f}ms, Throughput: {throughput:.1f} ops/sec")
        return result
    
    def benchmark_reverse_diffusion(self, diffusion: ConceptualDiffusionPython, 
                                   num_iterations: int = 100) -> Dict:
        """Benchmark reverse diffusion performance."""
        logger.info(f"🔄 Benchmarking reverse diffusion ({num_iterations} iterations)")
        
        batch_size = diffusion.config.batch_size
        concept_dim = diffusion.config.concept_dim
        
        # Prepare test data
        noisy_concepts = np.random.randn(batch_size, concept_dim).astype(np.float32)
        predicted_noise = np.random.randn(batch_size, concept_dim).astype(np.float32) * 0.1
        timesteps = np.random.randint(1, diffusion.config.num_timesteps, num_iterations)
        
        # Warmup
        for _ in range(5):
            diffusion.reverse_diffusion(noisy_concepts, predicted_noise, 50)
        
        # Benchmark
        start_time = time.time()
        memory_before = psutil.Process().memory_info().rss / 1024 / 1024
        
        for i, timestep in enumerate(timesteps):
            result = diffusion.reverse_diffusion(noisy_concepts, predicted_noise, int(timestep))
            if i == 0:
                output_shape = result.shape
        
        end_time = time.time()
        memory_after = psutil.Process().memory_info().rss / 1024 / 1024
        
        total_time = end_time - start_time
        avg_time = total_time / num_iterations
        throughput = num_iterations / total_time
        
        result = {
            'total_time': total_time,
            'avg_time_per_iteration': avg_time,
            'throughput_ops_per_sec': throughput,
            'memory_usage_mb': memory_after - memory_before,
            'input_shape': noisy_concepts.shape,
            'output_shape': output_shape,
            'num_iterations': num_iterations
        }
        
        logger.info(f"   📊 Avg time: {avg_time*1000:.2f}ms, Throughput: {throughput:.1f} ops/sec")
        return result
    
    def benchmark_uncertainty_quantification(self, diffusion: ConceptualDiffusionPython, 
                                           num_iterations: int = 50) -> Dict:
        """Benchmark uncertainty quantification performance."""
        logger.info(f"📊 Benchmarking uncertainty quantification ({num_iterations} iterations)")
        
        batch_size = diffusion.config.batch_size
        concept_dim = diffusion.config.concept_dim
        num_ensembles = 5
        
        # Prepare test data
        ensemble_predictions = np.random.randn(num_ensembles, batch_size, concept_dim).astype(np.float32)
        
        # Warmup
        for _ in range(3):
            diffusion.compute_uncertainty(ensemble_predictions)
        
        # Benchmark
        start_time = time.time()
        memory_before = psutil.Process().memory_info().rss / 1024 / 1024
        
        uncertainty_results = []
        for i in range(num_iterations):
            uncertainty = diffusion.compute_uncertainty(ensemble_predictions)
            uncertainty_results.append(uncertainty)
        
        end_time = time.time()
        memory_after = psutil.Process().memory_info().rss / 1024 / 1024
        
        total_time = end_time - start_time
        avg_time = total_time / num_iterations
        throughput = num_iterations / total_time
        
        # Analyze uncertainty statistics
        avg_total_uncertainty = np.mean([r['total_uncertainty'] for r in uncertainty_results])
        avg_confidence = np.mean([r['confidence_score'] for r in uncertainty_results])
        
        result = {
            'total_time': total_time,
            'avg_time_per_iteration': avg_time,
            'throughput_ops_per_sec': throughput,
            'memory_usage_mb': memory_after - memory_before,
            'avg_total_uncertainty': avg_total_uncertainty,
            'avg_confidence_score': avg_confidence,
            'num_ensembles': num_ensembles,
            'num_iterations': num_iterations
        }
        
        logger.info(f"   📊 Avg time: {avg_time*1000:.2f}ms, Uncertainty: {avg_total_uncertainty:.4f}")
        return result
    
    def benchmark_concept_generation(self, diffusion: ConceptualDiffusionPython, 
                                   num_steps: int = 20) -> Dict:
        """Benchmark full concept generation pipeline."""
        logger.info(f"🎯 Benchmarking concept generation ({num_steps} steps)")
        
        batch_size = diffusion.config.batch_size
        concept_dim = diffusion.config.concept_dim
        
        # Prepare initial concepts
        initial_concepts = np.random.randn(batch_size, concept_dim).astype(np.float32)
        
        # Benchmark
        start_time = time.time()
        memory_before = psutil.Process().memory_info().rss / 1024 / 1024
        
        trajectory = diffusion.generate_concepts(initial_concepts, num_steps=num_steps)
        
        end_time = time.time()
        memory_after = psutil.Process().memory_info().rss / 1024 / 1024
        
        total_time = end_time - start_time
        avg_time_per_step = total_time / num_steps
        
        result = {
            'total_time': total_time,
            'avg_time_per_step': avg_time_per_step,
            'num_steps': num_steps,
            'trajectory_length': len(trajectory),
            'memory_usage_mb': memory_after - memory_before,
            'concepts_per_second': (num_steps * batch_size) / total_time
        }
        
        logger.info(f"   📊 Total time: {total_time:.2f}s, {result['concepts_per_second']:.1f} concepts/sec")
        return result
    
    def run_comprehensive_benchmark(self, num_iterations: int = 100) -> Dict:
        """Run a comprehensive benchmark suite."""
        logger.info("🚀 Starting comprehensive ULTRA Conceptual Diffusion benchmark")
        logger.info("=" * 60)
        
        # Print system information
        logger.info("💻 System Information:")
        for key, value in self.system_info.items():
            logger.info(f"   {key}: {value}")
        logger.info("")
        
        results = {
            'system_info': self.system_info,
            'timestamp': time.time(),
            'config': self.config
        }
        
        # Test CPU performance
        logger.info("🖥️  CPU Performance Tests")
        logger.info("-" * 30)
        
        cpu_init = self.benchmark_initialization(force_cpu=True)
        results['cpu_initialization'] = cpu_init
        
        if cpu_init['success']:
            config_cpu = dict(self.config)
            config_cpu['use_cuda'] = 0
            
            with create_conceptual_diffusion(config_cpu, auto_compile=True) as diffusion_cpu:
                results['cpu_forward_diffusion'] = self.benchmark_forward_diffusion(
                    diffusion_cpu, num_iterations)
                results['cpu_reverse_diffusion'] = self.benchmark_reverse_diffusion(
                    diffusion_cpu, num_iterations)
                results['cpu_uncertainty'] = self.benchmark_uncertainty_quantification(
                    diffusion_cpu, num_iterations // 2)
                results['cpu_concept_generation'] = self.benchmark_concept_generation(
                    diffusion_cpu, 10)
        
        # Test GPU performance (if available)
        logger.info("\n🚀 GPU Performance Tests")
        logger.info("-" * 30)
        
        gpu_init = self.benchmark_initialization(force_cpu=False)
        results['gpu_initialization'] = gpu_init
        
        if gpu_init['success'] and gpu_init['device'] == 'GPU':
            config_gpu = dict(self.config)
            config_gpu['use_cuda'] = 1
            
            with create_conceptual_diffusion(config_gpu, auto_compile=True) as diffusion_gpu:
                results['gpu_forward_diffusion'] = self.benchmark_forward_diffusion(
                    diffusion_gpu, num_iterations)
                results['gpu_reverse_diffusion'] = self.benchmark_reverse_diffusion(
                    diffusion_gpu, num_iterations)
                results['gpu_uncertainty'] = self.benchmark_uncertainty_quantification(
                    diffusion_gpu, num_iterations // 2)
                results['gpu_concept_generation'] = self.benchmark_concept_generation(
                    diffusion_gpu, 10)
        else:
            logger.info("   ℹ️  GPU not available or initialization failed")
        
        # Performance comparison
        self._print_performance_summary(results)
        
        return results
    
    def _print_performance_summary(self, results: Dict):
        """Print a summary of performance results."""
        logger.info("\n📊 Performance Summary")
        logger.info("=" * 60)
        
        # Initialization comparison
        cpu_init_time = results.get('cpu_initialization', {}).get('initialization_time', 0)
        gpu_init_time = results.get('gpu_initialization', {}).get('initialization_time', 0)
        
        logger.info(f"Initialization Time:")
        logger.info(f"   CPU: {cpu_init_time:.3f}s")
        if gpu_init_time > 0:
            logger.info(f"   GPU: {gpu_init_time:.3f}s")
            speedup = cpu_init_time / gpu_init_time if gpu_init_time > 0 else 0
            logger.info(f"   GPU Speedup: {speedup:.2f}x")
        
        # Forward diffusion comparison
        cpu_forward = results.get('cpu_forward_diffusion', {}).get('throughput_ops_per_sec', 0)
        gpu_forward = results.get('gpu_forward_diffusion', {}).get('throughput_ops_per_sec', 0)
        
        logger.info(f"\nForward Diffusion Throughput:")
        logger.info(f"   CPU: {cpu_forward:.1f} ops/sec")
        if gpu_forward > 0:
            logger.info(f"   GPU: {gpu_forward:.1f} ops/sec")
            speedup = gpu_forward / cpu_forward if cpu_forward > 0 else 0
            logger.info(f"   GPU Speedup: {speedup:.2f}x")
        
        # Memory usage
        cpu_memory = results.get('cpu_forward_diffusion', {}).get('memory_usage_mb', 0)
        gpu_memory = results.get('gpu_forward_diffusion', {}).get('memory_usage_mb', 0)
        
        logger.info(f"\nMemory Usage (Forward Diffusion):")
        logger.info(f"   CPU: {cpu_memory:.1f} MB")
        if gpu_memory > 0:
            logger.info(f"   GPU: {gpu_memory:.1f} MB")
    
    def save_results(self, results: Dict, filename: str = None):
        """Save benchmark results to a JSON file."""
        if filename is None:
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            filename = f"benchmark_results_{timestamp}.json"
        
        try:
            with open(filename, 'w') as f:
                json.dump(results, f, indent=2, default=str)
            logger.info(f"💾 Results saved to {filename}")
        except Exception as e:
            logger.error(f"❌ Failed to save results: {e}")

def main():
    """Main benchmark execution."""
    parser = argparse.ArgumentParser(description="ULTRA Conceptual Diffusion Benchmark")
    parser.add_argument('--batch-size', type=int, default=4, 
                       help='Batch size for testing')
    parser.add_argument('--concept-dim', type=int, default=512, 
                       help='Concept dimension')
    parser.add_argument('--num-iterations', type=int, default=100, 
                       help='Number of iterations for each test')
    parser.add_argument('--num-timesteps', type=int, default=1000,
                       help='Number of diffusion timesteps')
    parser.add_argument('--save-results', action='store_true',
                       help='Save results to JSON file')
    parser.add_argument('--output-file', type=str,
                       help='Output filename for results')
    parser.add_argument('--quick', action='store_true',
                       help='Run quick benchmark with fewer iterations')
    
    args = parser.parse_args()
    
    # Adjust for quick mode
    if args.quick:
        args.num_iterations = 20
        logger.info("🏃 Running in quick mode")
    
    # Configuration
    config = {
        'batch_size': args.batch_size,
        'concept_dim': args.concept_dim,
        'num_timesteps': args.num_timesteps,
        'hierarchical_levels': 4,
        'beta_start': 1e-4,
        'beta_end': 0.02,
        'guidance_scale': 3.0,
    }
    
    # Run benchmark
    benchmark = PerformanceBenchmark(config)
    results = benchmark.run_comprehensive_benchmark(args.num_iterations)
    
    # Save results if requested
    if args.save_results:
        benchmark.save_results(results, args.output_file)
    
    logger.info("\n🎉 Benchmark completed successfully!")

if __name__ == "__main__":
    main()