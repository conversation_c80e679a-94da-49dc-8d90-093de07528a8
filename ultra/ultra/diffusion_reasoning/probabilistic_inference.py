#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ULTRA: Ultimate Learning & Thought Reasoning Architecture
Probabilistic Inference Engine Module

This module implements Bayesian inference techniques to reason under uncertainty,
allowing the system to infer concepts that satisfy constraints or have specific
relationships with known concepts. The module includes:

1. BayesianInference: Core Bayesian inference operations
2. ConceptPosterior: Representation of posterior distributions over concepts
3. EvidenceEstimation: Methods for estimating evidence terms in Bayesian inference
4. ProbabilisticInferenceEngine: Main class that integrates all inference components

Mathematical foundation:
- <PERSON><PERSON>' rule: p(θ|D) = p(D|θ)p(θ)/p(D)
- Marginal likelihood: p(D) = ∫ p(D|θ)p(θ)dθ
- Monte Carlo sampling: E[f(x)] ≈ (1/N)∑_i f(x_i) where x_i ~ p(x)
- Importance sampling: E_p[f(x)] ≈ ∑_i w_i f(x_i) where x_i ~ q(x) and w_i = p(x_i)/q(x_i)
"""

import os
import math
import logging
import time
from pathlib import Path
from typing import Dict, List, Tuple, Union, Optional, Callable, Any, Set

import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.distributions import MultivariateNormal, Normal, kl_divergence

# Import from other ULTRA modules
try:
    from .bayesian_uncertainty import (
        GaussianDistribution, 
        BayesianUncertaintyQuantification,
        estimate_uncertainty_bounds
    )
    from .thought_latent_space import ThoughtVector
except ImportError:
    # Handle case where this module is imported separately
    logger = logging.getLogger(__name__)
    logger.warning("Could not import from other ULTRA modules. Using standalone mode.")
    # Define minimal versions of required classes if needed

# Configure module logger
logger = logging.getLogger(__name__)

# Default values
DEFAULT_DIMENSION = 512
DEFAULT_NUM_SAMPLES = 100
DEFAULT_MAX_ITERATIONS = 20
DEFAULT_LIKELIHOOD_TEMP = 1.0
DEFAULT_PRIOR_VARIANCE = 1.0


class ConceptPosterior:
    """
    Represents a posterior distribution over concepts.
    
    This class maintains and manipulates the posterior distribution
    that results from Bayesian inference over concepts.
    """
    
    def __init__(
        self, 
        distribution: GaussianDistribution,
        log_evidence: float = 0.0,
        metadata: Optional[Dict[str, Any]] = None
    ):
        """
        Initialize a concept posterior.
        
        Args:
            distribution: Distribution representing the posterior
            log_evidence: Log evidence (marginal likelihood) for the posterior
            metadata: Optional metadata about inference process
        """
        self.distribution = distribution
        self.log_evidence = log_evidence
        self.metadata = metadata or {}
        self.device = distribution.mean.device
        
        # Performance metrics
        self.metrics = {
            'confidence': self._compute_confidence(),
            'entropy': self.distribution.entropy() if hasattr(self.distribution, 'entropy') else 0.0,
            'log_evidence': log_evidence
        }
        
    def _compute_confidence(self) -> float:
        """
        Compute a confidence score based on the sharpness of the posterior.
        
        Returns:
            Confidence score in [0, 1]
        """
        if hasattr(self.distribution, 'diagonal') and self.distribution.diagonal:
            # For diagonal covariance, use the inverse of the average variance
            var_mean = torch.mean(self.distribution.variance).item()
            confidence = 1.0 / (1.0 + var_mean)
        else:
            # For full covariance, use the trace of the covariance matrix
            var_trace = torch.trace(self.distribution.covariance_matrix).item()
            confidence = 1.0 / (1.0 + var_trace / self.distribution.dimension)
            
        return confidence
        
    def mean(self) -> torch.Tensor:
        """
        Get the mean of the posterior distribution.
        
        Returns:
            Mean concept embedding
        """
        return self.distribution.mean
    
    def mode(self) -> torch.Tensor:
        """
        Get the mode of the posterior distribution.
        
        For Gaussian distributions, the mode equals the mean.
        
        Returns:
            Mode concept embedding
        """
        return self.distribution.mean
    
    def sample(self, n_samples: int = 1) -> torch.Tensor:
        """
        Sample from the posterior distribution.
        
        Args:
            n_samples: Number of samples to draw
            
        Returns:
            Sampled concept embeddings [n_samples, dimension]
        """
        return self.distribution.sample(n_samples)
    
    def confidence_interval(
        self, 
        confidence_level: float = 0.95,
        method: str = 'gaussian'
    ) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Compute confidence interval for the posterior.
        
        Args:
            confidence_level: Confidence level (e.g., 0.95 for 95% confidence)
            method: Method for interval computation ('gaussian' or 'sampling')
            
        Returns:
            Tuple of (lower_bound, upper_bound) tensors
        """
        if method == 'gaussian':
            import scipy.stats as stats
            
            # For Gaussian distribution, use analytical form
            z_score = stats.norm.ppf(1 - (1 - confidence_level) / 2)
            
            if hasattr(self.distribution, 'diagonal') and self.distribution.diagonal:
                std_dev = torch.sqrt(self.distribution.variance)
                lower_bound = self.distribution.mean - z_score * std_dev
                upper_bound = self.distribution.mean + z_score * std_dev
            else:
                # For full covariance, we need the eigendecomposition
                eigenvalues, eigenvectors = torch.linalg.eigh(self.distribution.covariance_matrix)
                std_dev_matrix = torch.matmul(
                    eigenvectors, 
                    torch.diag(torch.sqrt(torch.clamp(eigenvalues, min=1e-6)))
                )
                
                # Generate unit vector in each direction
                directions = eigenvectors
                
                # Compute intervals for each direction
                lower_bound = self.distribution.mean.clone()
                upper_bound = self.distribution.mean.clone()
                
                for i in range(self.distribution.dimension):
                    direction = directions[:, i]
                    std_in_direction = torch.sqrt(eigenvalues[i])
                    
                    lower_bound -= z_score * std_in_direction * direction
                    upper_bound += z_score * std_in_direction * direction
        else:
            # Sampling-based method
            samples = self.distribution.sample(1000)
            lower_bound, upper_bound = estimate_uncertainty_bounds(
                samples=samples,
                confidence_level=confidence_level,
                method='percentile'
            )
            
        return lower_bound, upper_bound
    
    def to_thought_vector(
        self,
        source: Optional[str] = None
    ) -> ThoughtVector:
        """
        Convert posterior to a ThoughtVector.
        
        Args:
            source: Optional source identifier for the ThoughtVector
            
        Returns:
            ThoughtVector representing the posterior mean
        """
        # Import here to avoid circular imports
        try:
            from .thought_latent_space import ThoughtVector
            
            # Create metadata for ThoughtVector
            metadata = {
                'posterior': {
                    'confidence': self.metrics['confidence'],
                    'entropy': self.metrics['entropy'],
                    'log_evidence': self.metrics['log_evidence']
                }
            }
            
            # Add original metadata
            for key, value in self.metadata.items():
                metadata[key] = value
                
            # Create ThoughtVector with mean of posterior
            return ThoughtVector(
                vector=self.distribution.mean,
                metadata=metadata,
                source=source or "posterior_inference"
            )
        except ImportError:
            logger.warning("Could not import ThoughtVector. Returning tensor instead.")
            return self.distribution.mean
    
    def update(
        self, 
        likelihood: GaussianDistribution
    ) -> 'ConceptPosterior':
        """
        Update posterior with new likelihood information.
        
        Args:
            likelihood: Likelihood distribution to incorporate
            
        Returns:
            Updated posterior
        """
        # Compute updated distribution
        if hasattr(self.distribution, 'diagonal') and self.distribution.diagonal:
            # Simple update for diagonal case
            current_var = self.distribution.variance
            current_mean = self.distribution.mean
            
            likelihood_var = likelihood.variance if hasattr(likelihood, 'diagonal') and likelihood.diagonal else torch.diag(likelihood.covariance_matrix)
            likelihood_mean = likelihood.mean
            
            # Compute precision-weighted update
            current_precision = 1.0 / current_var
            likelihood_precision = 1.0 / likelihood_var
            posterior_precision = current_precision + likelihood_precision
            posterior_var = 1.0 / posterior_precision
            
            posterior_mean = posterior_var * (
                current_precision * current_mean + 
                likelihood_precision * likelihood_mean
            )
            
            # Create updated distribution
            updated_distribution = GaussianDistribution(
                mean=posterior_mean,
                covariance=posterior_var,
                diagonal=True
            )
        else:
            # Full matrix update using precision form
            current_cov = self.distribution.covariance_matrix
            current_prec = self.distribution.precision_matrix
            current_mean = self.distribution.mean
            
            likelihood_cov = likelihood.covariance_matrix
            likelihood_prec = likelihood.precision_matrix
            likelihood_mean = likelihood.mean
            
            # Compute posterior precision and mean
            posterior_prec = current_prec + likelihood_prec
            
            # Compute posterior covariance using Cholesky decomposition for stability
            try:
                L = torch.linalg.cholesky(posterior_prec)
                posterior_cov = torch.cholesky_inverse(L)
            except:
                # Fallback to direct inversion with ridge regularization
                ridge = torch.eye(self.distribution.dimension, device=self.device) * 1e-6
                posterior_cov = torch.inverse(posterior_prec + ridge)
                
            posterior_mean = torch.matmul(
                posterior_cov,
                torch.matmul(current_prec, current_mean) + 
                torch.matmul(likelihood_prec, likelihood_mean)
            )
            
            # Create updated distribution
            updated_distribution = GaussianDistribution(
                mean=posterior_mean,
                covariance=posterior_cov,
                diagonal=False
            )
            
        # Compute new log evidence (marginal likelihood)
        # log p(D) = log p(D|θ) + log p(θ) - log p(θ|D)
        # We simplify by computing KL divergence between posterior and prior
        log_likelihood = self._compute_log_likelihood(likelihood, self.distribution.mean)
        kl_div = updated_distribution.kl_divergence(self.distribution)
        log_evidence = self.log_evidence + log_likelihood - kl_div
        
        # Create updated posterior
        return ConceptPosterior(
            distribution=updated_distribution,
            log_evidence=log_evidence,
            metadata=self.metadata
        )
    
    def _compute_log_likelihood(
        self, 
        likelihood: GaussianDistribution, 
        point: torch.Tensor
    ) -> float:
        """
        Compute log likelihood of a point under the likelihood distribution.
        
        Args:
            likelihood: Likelihood distribution
            point: Point to evaluate
            
        Returns:
            Log likelihood value
        """
        return likelihood.log_prob(point).item()
    
    def to(self, device: str) -> 'ConceptPosterior':
        """
        Move posterior to specified device.
        
        Args:
            device: Target device
            
        Returns:
            Posterior on target device
        """
        return ConceptPosterior(
            distribution=self.distribution.to(device),
            log_evidence=self.log_evidence,
            metadata=self.metadata
        )


class EvidenceEstimation:
    """
    Methods for estimating evidence (marginal likelihood) in Bayesian inference.
    """
    
    def __init__(
        self, 
        dimension: int = DEFAULT_DIMENSION,
        device: str = 'cuda' if torch.cuda.is_available() else 'cpu'
    ):
        """
        Initialize evidence estimation.
        
        Args:
            dimension: Dimensionality of the concept space
            device: Device for computations
        """
        self.dimension = dimension
        self.device = device
        
    def importance_sampling(
        self, 
        likelihood_fn: Callable[[torch.Tensor], torch.Tensor],
        prior: GaussianDistribution,
        proposal: Optional[GaussianDistribution] = None,
        num_samples: int = 1000
    ) -> float:
        """
        Estimate evidence using importance sampling.
        
        p(D) ≈ (1/N) ∑_i p(D|θ_i) * p(θ_i) / q(θ_i), θ_i ~ q(θ)
        
        Args:
            likelihood_fn: Function mapping parameter to log likelihood
            prior: Prior distribution
            proposal: Proposal distribution (defaults to prior)
            num_samples: Number of samples for estimation
            
        Returns:
            Estimated log evidence
        """
        # Default to prior if no proposal is provided
        if proposal is None:
            proposal = prior
            
        # Draw samples from proposal
        samples = proposal.sample(num_samples)
        
        # Compute importance weights
        log_prior = prior.log_prob(samples)
        log_proposal = proposal.log_prob(samples)
        log_likelihood = likelihood_fn(samples)
        
        # Compute importance weights in log space
        log_weights = log_likelihood + log_prior - log_proposal
        
        # Use log-sum-exp trick for numerical stability
        log_max = torch.max(log_weights)
        log_evidence = log_max + torch.log(torch.mean(torch.exp(log_weights - log_max)))
        
        return log_evidence.item()
    
    def harmonic_mean(
        self, 
        posterior_samples: torch.Tensor,
        likelihood_fn: Callable[[torch.Tensor], torch.Tensor],
        prior: GaussianDistribution
    ) -> float:
        """
        Estimate evidence using the harmonic mean estimator.
        
        1/p(D) ≈ (1/N) ∑_i 1 / [p(D|θ_i) * p(θ_i) / p(θ_i|D)], θ_i ~ p(θ|D)
        
        Args:
            posterior_samples: Samples from posterior distribution
            likelihood_fn: Function mapping parameter to log likelihood
            prior: Prior distribution
            
        Returns:
            Estimated log evidence
        """
        # Compute likelihood and prior for each sample
        log_likelihood = likelihood_fn(posterior_samples)
        log_prior = prior.log_prob(posterior_samples)
        
        # For harmonic mean, we need log of reciprocal (negative log)
        neg_log_terms = -log_likelihood - log_prior
        
        # Use log-sum-exp trick for numerical stability
        log_min = torch.min(neg_log_terms)
        log_reciprocal = log_min + torch.log(torch.mean(torch.exp(neg_log_terms - log_min)))
        
        # Take negative to get log evidence
        log_evidence = -log_reciprocal
        
        return log_evidence.item()
    
    def thermodynamic_integration(
        self, 
        likelihood_fn: Callable[[torch.Tensor], torch.Tensor],
        prior: GaussianDistribution,
        num_temperatures: int = 20,
        num_samples: int = 100
    ) -> float:
        """
        Estimate evidence using thermodynamic integration (path sampling).
        
        log p(D) = ∫_0^1 E_β[log p(D|θ)] dβ, where β is temperature
        
        Args:
            likelihood_fn: Function mapping parameter to log likelihood
            prior: Prior distribution
            num_temperatures: Number of temperature points
            num_samples: Number of samples per temperature
            
        Returns:
            Estimated log evidence
        """
        # Define temperature schedule
        temperatures = torch.linspace(0, 1, num_temperatures, device=self.device)
        
        # Storage for expected log likelihoods
        expected_log_likelihoods = torch.zeros(num_temperatures, device=self.device)
        
        # Simple MCMC to sample from tempered posterior at each temperature
        current_sample = prior.sample(1).squeeze(0)
        
        for i, beta in enumerate(temperatures):
            samples = []
            
            # Simple Metropolis algorithm
            for _ in range(num_samples + 100):  # 100 burn-in samples
                # Propose new sample
                proposal = current_sample + 0.01 * torch.randn_like(current_sample)
                
                # Compute acceptance ratio
                current_log_likelihood = likelihood_fn(current_sample.unsqueeze(0)).squeeze()
                proposal_log_likelihood = likelihood_fn(proposal.unsqueeze(0)).squeeze()
                
                current_log_prior = prior.log_prob(current_sample.unsqueeze(0)).squeeze()
                proposal_log_prior = prior.log_prob(proposal.unsqueeze(0)).squeeze()
                
                # Tempered posterior
                current_log_posterior = beta * current_log_likelihood + current_log_prior
                proposal_log_posterior = beta * proposal_log_likelihood + proposal_log_prior
                
                # Metropolis acceptance
                log_alpha = proposal_log_posterior - current_log_posterior
                log_u = torch.log(torch.rand(1, device=self.device))
                
                if log_u < log_alpha:
                    current_sample = proposal
                    
                # Store sample (after burn-in)
                if _ >= 100:
                    samples.append(current_sample.clone())
            
            # Compute expected log likelihood at this temperature
            samples_tensor = torch.stack(samples)
            log_likelihoods = likelihood_fn(samples_tensor)
            expected_log_likelihoods[i] = torch.mean(log_likelihoods)
            
        # Integrate over temperatures using trapezoid rule
        # log p(D) = ∫_0^1 E_β[log p(D|θ)] dβ
        log_evidence = torch.trapz(expected_log_likelihoods, temperatures).item()
        
        return log_evidence
    
    def nested_sampling(
        self, 
        likelihood_fn: Callable[[torch.Tensor], torch.Tensor],
        prior: GaussianDistribution,
        num_live_points: int = 100,
        max_iterations: int = 1000
    ) -> float:
        """
        Estimate evidence using nested sampling.
        
        Args:
            likelihood_fn: Function mapping parameter to log likelihood
            prior: Prior distribution
            num_live_points: Number of live points in the algorithm
            max_iterations: Maximum number of iterations
            
        Returns:
            Estimated log evidence
        """
        # Initialize live points from prior
        live_points = prior.sample(num_live_points)
        live_log_likelihoods = likelihood_fn(live_points)
        
        # Initialize evidence accumulator and prior volume
        log_evidence = -float('inf')  # Start with log(0)
        log_width = math.log(1.0 - math.exp(-1.0 / num_live_points))
        
        # Storage for computational history
        log_likelihoods = []
        log_widths = []
        
        for i in range(max_iterations):
            # Find point with lowest likelihood
            idx = torch.argmin(live_log_likelihoods)
            log_likelihood_i = live_log_likelihoods[idx].item()
            
            # Store likelihood and width
            log_likelihoods.append(log_likelihood_i)
            log_widths.append(log_width)
            
            # Update evidence
            log_evidence_contribution = log_likelihood_i + log_width
            log_evidence = self._log_sum_exp(log_evidence, log_evidence_contribution)
            
            # Update prior volume
            log_width += math.log(1.0 - math.exp(-1.0 / num_live_points))
            
            # Stop if remaining evidence too small to contribute significantly
            max_remaining = torch.max(live_log_likelihoods).item() + log_width
            if self._log_sum_exp(log_evidence, max_remaining) - log_evidence < math.log(1.0001):
                break
                
            # Replace lowest-likelihood point with a new sample
            # Draw from prior and ensure it has higher likelihood
            new_point = None
            while new_point is None:
                candidate = prior.sample(1).squeeze(0)
                candidate_log_likelihood = likelihood_fn(candidate.unsqueeze(0)).squeeze()
                if candidate_log_likelihood > log_likelihood_i:
                    new_point = candidate
                    new_log_likelihood = candidate_log_likelihood
                    
            # Update live points
            live_points[idx] = new_point
            live_log_likelihoods[idx] = new_log_likelihood
            
        # Add remaining evidence from live points
        for i in range(num_live_points):
            log_evidence_contribution = live_log_likelihoods[i] + log_width
            log_evidence = self._log_sum_exp(log_evidence, log_evidence_contribution)
            
        return log_evidence
    
    def _log_sum_exp(self, log_a: float, log_b: float) -> float:
        """
        Compute log(exp(log_a) + exp(log_b)) in a numerically stable way.
        
        Args:
            log_a: First log value
            log_b: Second log value
            
        Returns:
            log(exp(log_a) + exp(log_b))
        """
        if log_a == -float('inf'):
            return log_b
        if log_b == -float('inf'):
            return log_a
            
        # Use the log-sum-exp trick
        max_val = max(log_a, log_b)
        min_val = min(log_a, log_b)
        return max_val + math.log(1 + math.exp(min_val - max_val))


def marginal_likelihood(
    likelihood_fn: Callable[[torch.Tensor], torch.Tensor],
    prior: GaussianDistribution,
    method: str = 'importance_sampling',
    num_samples: int = 1000,
    **kwargs
) -> float:
    """
    Compute marginal likelihood (evidence) p(D).
    
    Args:
        likelihood_fn: Function mapping parameter to log likelihood
        prior: Prior distribution
        method: Method for evidence estimation
        num_samples: Number of samples for estimation
        **kwargs: Additional arguments for specific methods
        
    Returns:
        Estimated log evidence
    """
    estimator = EvidenceEstimation(
        dimension=prior.dimension,
        device=prior.mean.device
    )
    
    if method == 'importance_sampling':
        proposal = kwargs.get('proposal', None)
        return estimator.importance_sampling(
            likelihood_fn=likelihood_fn,
            prior=prior,
            proposal=proposal,
            num_samples=num_samples
        )
    elif method == 'harmonic_mean':
        posterior_samples = kwargs.get('posterior_samples')
        if posterior_samples is None:
            raise ValueError("Harmonic mean estimator requires posterior samples")
        return estimator.harmonic_mean(
            posterior_samples=posterior_samples,
            likelihood_fn=likelihood_fn,
            prior=prior
        )
    elif method == 'thermodynamic_integration':
        num_temperatures = kwargs.get('num_temperatures', 20)
        return estimator.thermodynamic_integration(
            likelihood_fn=likelihood_fn,
            prior=prior,
            num_temperatures=num_temperatures,
            num_samples=num_samples
        )
    elif method == 'nested_sampling':
        num_live_points = kwargs.get('num_live_points', 100)
        max_iterations = kwargs.get('max_iterations', 1000)
        return estimator.nested_sampling(
            likelihood_fn=likelihood_fn,
            prior=prior,
            num_live_points=num_live_points,
            max_iterations=max_iterations
        )
    else:
        raise ValueError(f"Unknown evidence estimation method: {method}")


def conditional_sampling(
    conditional_density: Callable[[torch.Tensor], torch.Tensor],
    proposal: GaussianDistribution,
    num_samples: int = DEFAULT_NUM_SAMPLES,
    proposal_stdev_factor: float = 1.0,
    max_iterations: int = DEFAULT_MAX_ITERATIONS
) -> Tuple[torch.Tensor, torch.Tensor]:
    """
    Generate samples from a conditional distribution p(x|y) using MCMC.
    
    Args:
        conditional_density: Function proportional to p(x|y)
        proposal: Initial proposal distribution
        num_samples: Number of samples to generate
        proposal_stdev_factor: Factor to scale proposal standard deviation
        max_iterations: Maximum number of iterations
        
    Returns:
        Tuple of (samples, log_weights)
    """
    device = proposal.mean.device
    dimension = proposal.dimension
    
    # Initialize current sample at proposal mean
    current_sample = proposal.mean
    current_log_density = conditional_density(current_sample.unsqueeze(0)).squeeze()
    
    # Store accepted samples
    accepted_samples = []
    acceptance_rate = 0.0
    
    # Determine proposal standard deviation
    if hasattr(proposal, 'diagonal') and proposal.diagonal:
        proposal_stdev = torch.sqrt(proposal.variance) * proposal_stdev_factor
    else:
        # For full covariance, use eigendecomposition
        eigenvalues, _ = torch.linalg.eigh(proposal.covariance_matrix)
        proposal_stdev = torch.sqrt(torch.max(eigenvalues)) * torch.ones(dimension, device=device) * proposal_stdev_factor
    
    # Perform Metropolis-Hastings sampling
    num_accepted = 0
    for i in range(max_iterations):
        # Propose new sample
        proposal_noise = torch.randn_like(current_sample) * proposal_stdev
        proposed_sample = current_sample + proposal_noise
        
        # Compute log density at proposed point
        proposed_log_density = conditional_density(proposed_sample.unsqueeze(0)).squeeze()
        
        # Compute log acceptance ratio
        log_alpha = proposed_log_density - current_log_density
        
        # Accept or reject
        log_u = torch.log(torch.rand(1, device=device))
        if log_u < log_alpha:
            current_sample = proposed_sample
            current_log_density = proposed_log_density
            num_accepted += 1
            
        # Store samples
        if i >= max_iterations - num_samples:
            accepted_samples.append(current_sample.clone())
            
    # Compute acceptance rate
    acceptance_rate = num_accepted / max_iterations
    logger.debug(f"MCMC acceptance rate: {acceptance_rate:.4f}")
    
    # Stack samples
    samples = torch.stack(accepted_samples)
    
    # Compute log weights for each sample
    log_weights = conditional_density(samples)
    
    return samples, log_weights


class BayesianInference:
    """
    Core Bayesian inference operations for concept reasoning.
    """
    
    def __init__(
        self, 
        dimension: int = DEFAULT_DIMENSION,
        default_prior_variance: float = DEFAULT_PRIOR_VARIANCE,
        device: str = 'cuda' if torch.cuda.is_available() else 'cpu'
    ):
        """
        Initialize Bayesian inference.
        
        Args:
            dimension: Dimensionality of the concept space
            default_prior_variance: Default variance for prior distributions
            device: Device for computations
        """
        self.dimension = dimension
        self.default_prior_variance = default_prior_variance
        self.device = device
        
        # Default uninformative prior
        self.default_prior = GaussianDistribution(
            mean=torch.zeros(dimension, device=device),
            covariance=torch.ones(dimension, device=device) * default_prior_variance,
            diagonal=True
        )
        
        # Evidence estimation utility
        self.evidence_estimator = EvidenceEstimation(dimension=dimension, device=device)
        
    def create_prior(
        self, 
        mean: Optional[torch.Tensor] = None,
        variance: Optional[Union[torch.Tensor, float]] = None,
        diagonal: bool = True
    ) -> GaussianDistribution:
        """
        Create a prior distribution.
        
        Args:
            mean: Optional mean vector (defaults to zeros)
            variance: Optional variance (scalar or vector, defaults to default_prior_variance)
            diagonal: Whether to use diagonal covariance
            
        Returns:
            Prior distribution
        """
        if mean is None:
            mean = torch.zeros(self.dimension, device=self.device)
        else:
            mean = mean.to(self.device)
            
        if variance is None:
            variance = torch.ones(self.dimension, device=self.device) * self.default_prior_variance
        elif isinstance(variance, float):
            variance = torch.ones(self.dimension, device=self.device) * variance
        else:
            variance = variance.to(self.device)
            
        return GaussianDistribution(
            mean=mean,
            covariance=variance,
            diagonal=diagonal
        )
    
    def create_likelihood(
        self, 
        concept: torch.Tensor,
        uncertainty: Union[torch.Tensor, float],
        diagonal: bool = True
    ) -> GaussianDistribution:
        """
        Create a likelihood distribution centered at a concept.
        
        Args:
            concept: Concept embedding
            uncertainty: Uncertainty (variance) of the likelihood
            diagonal: Whether to use diagonal covariance
            
        Returns:
            Likelihood distribution
        """
        concept = concept.to(self.device)
        
        if isinstance(uncertainty, float):
            uncertainty = torch.ones(self.dimension, device=self.device) * uncertainty
        else:
            uncertainty = uncertainty.to(self.device)
            
        return GaussianDistribution(
            mean=concept,
            covariance=uncertainty,
            diagonal=diagonal
        )
    
    def update_posterior(
        self, 
        prior: GaussianDistribution,
        likelihood: GaussianDistribution
    ) -> ConceptPosterior:
        """
        Update posterior distribution with Bayes rule.
        
        Args:
            prior: Prior distribution
            likelihood: Likelihood distribution
            
        Returns:
            Posterior distribution
        """
        # First convert to ConceptPosterior if needed
        if isinstance(prior, GaussianDistribution):
            prior_posterior = ConceptPosterior(
                distribution=prior,
                log_evidence=0.0
            )
        else:
            prior_posterior = prior
            
        # Update with likelihood
        return prior_posterior.update(likelihood)
    
    def infer_from_examples(
        self, 
        examples: torch.Tensor,
        prior: Optional[GaussianDistribution] = None,
        uncertainty: float = 0.1
    ) -> ConceptPosterior:
        """
        Infer a concept from examples.
        
        Args:
            examples: Example concept embeddings [num_examples, dimension]
            prior: Optional prior distribution
            uncertainty: Uncertainty (variance) for each example
            
        Returns:
            Posterior distribution over the concept
        """
        examples = examples.to(self.device)
        
        # Use default prior if not provided
        if prior is None:
            prior = self.default_prior
            
        # Initialize posterior with prior
        posterior = ConceptPosterior(
            distribution=prior,
            log_evidence=0.0
        )
        
        # Update with each example
        for i in range(examples.shape[0]):
            example = examples[i]
            likelihood = self.create_likelihood(example, uncertainty)
            posterior = posterior.update(likelihood)
            
        return posterior
    
    def infer_with_constraints(
        self, 
        constraints: List[Tuple[torch.Tensor, Callable, float]],
        prior: Optional[GaussianDistribution] = None,
        num_samples: int = DEFAULT_NUM_SAMPLES,
        max_iterations: int = DEFAULT_MAX_ITERATIONS
    ) -> ConceptPosterior:
        """
        Infer a concept that satisfies constraints.
        
        Args:
            constraints: List of (concept, constraint_fn, strength) tuples
                         constraint_fn maps (query, concept) to a score in [0, 1]
            prior: Optional prior distribution
            num_samples: Number of samples for inference
            max_iterations: Maximum number of iterations
            
        Returns:
            Posterior distribution over the concept
        """
        # Use default prior if not provided
        if prior is None:
            prior = self.default_prior
            
        # Define log density function based on constraints
        def log_density(x):
            log_prior = prior.log_prob(x)
            
            # Evaluate all constraints
            log_likelihood = torch.zeros_like(log_prior)
            for concept, constraint_fn, strength in constraints:
                # Compute constraint score
                scores = constraint_fn(x, concept.to(self.device).unsqueeze(0))
                
                # Convert to log likelihood with strength
                constraint_log_likelihood = torch.log(scores + 1e-10) * strength
                log_likelihood += constraint_log_likelihood
                
            return log_prior + log_likelihood
        
        # Use MCMC to sample from posterior
        samples, log_weights = conditional_sampling(
            conditional_density=log_density,
            proposal=prior,
            num_samples=num_samples,
            max_iterations=max_iterations
        )
        
        # Compute weighted mean and covariance
        weights = F.softmax(log_weights, dim=0)
        mean = torch.sum(samples * weights.unsqueeze(1), dim=0)
        
        # Compute covariance
        centered = samples - mean
        cov = torch.zeros((self.dimension, self.dimension), device=self.device)
        for i in range(samples.shape[0]):
            c = centered[i].unsqueeze(1)
            cov += weights[i] * torch.matmul(c, c.t())
            
        # Create posterior distribution
        posterior_distribution = GaussianDistribution(
            mean=mean,
            covariance=cov,
            diagonal=False
        )
        
        # Estimate log evidence
        log_evidence = torch.logsumexp(log_weights, dim=0).item() - math.log(samples.shape[0])
        
        return ConceptPosterior(
            distribution=posterior_distribution,
            log_evidence=log_evidence,
            metadata={
                'num_samples': num_samples,
                'max_iterations': max_iterations,
                'constraints': len(constraints)
            }
        )
    
    def compare_hypotheses(
        self, 
        data: torch.Tensor,
        hypotheses: List[GaussianDistribution],
        prior_probs: Optional[List[float]] = None
    ) -> List[float]:
        """
        Compare hypotheses using Bayesian model comparison.
        
        Args:
            data: Observed data
            hypotheses: List of hypothesis distributions
            prior_probs: Optional prior probabilities for hypotheses
            
        Returns:
            Posterior probabilities for each hypothesis
        """
        data = data.to(self.device)
        
        # Default to uniform prior over hypotheses
        if prior_probs is None:
            prior_probs = [1.0 / len(hypotheses)] * len(hypotheses)
            
        # Convert to tensor
        prior_probs = torch.tensor(prior_probs, device=self.device)
        
        # Compute log likelihoods
        log_likelihoods = torch.zeros(len(hypotheses), device=self.device)
        for i, hypothesis in enumerate(hypotheses):
            log_likelihoods[i] = hypothesis.log_prob(data).sum()
            
        # Compute log posterior (unnormalized)
        log_prior = torch.log(prior_probs)
        log_posterior_unnorm = log_likelihoods + log_prior
        
        # Normalize (using log-sum-exp trick)
        log_posterior = log_posterior_unnorm - torch.logsumexp(log_posterior_unnorm, dim=0)
        posterior_probs = torch.exp(log_posterior)
        
        return posterior_probs.tolist()
    
    def infer_relation(
        self, 
        source: torch.Tensor,
        target: torch.Tensor,
        relation_types: List[str],
        uncertainty: float = 0.1
    ) -> Dict[str, float]:
        """
        Infer the most likely relation between two concepts.
        
        Args:
            source: Source concept embedding
            target: Target concept embedding
            relation_types: List of relation types to consider
            uncertainty: Uncertainty in concept representations
            
        Returns:
            Dictionary mapping relation types to probabilities
        """
        source = source.to(self.device)
        target = target.to(self.device)
        
        # Compute similarity
        similarity = F.cosine_similarity(source.unsqueeze(0), target.unsqueeze(0)).item()
        
        # Define likelihood functions for each relation type
        likelihoods = {}
        
        for relation in relation_types:
            if relation == "similar":
                # High similarity -> high likelihood of "similar"
                likelihood = (similarity + 1) / 2
            elif relation == "different":
                # Low similarity -> high likelihood of "different"
                likelihood = 1 - (similarity + 1) / 2
            elif relation == "orthogonal":
                # Similarity near zero -> high likelihood of "orthogonal"
                likelihood = 1 - abs(similarity)
            elif relation == "subsumes":
                # Target should be similar to source, but source may have additional features
                # Simple approximation: higher likelihood if similarity is in [0.5, 0.9]
                sim_centered = max(0, min(1, (similarity + 1) / 2))
                if sim_centered < 0.5:
                    likelihood = 0.0
                elif sim_centered > 0.9:
                    likelihood = 0.5  # High similarity could also be "similar"
                else:
                    likelihood = (sim_centered - 0.5) / 0.4  # Scale to [0, 1]
            elif relation == "subsumed_by":
                # Source should be similar to target, but target may have additional features
                # Simple approximation: higher likelihood if similarity is in [0.5, 0.9]
                sim_centered = max(0, min(1, (similarity + 1) / 2))
                if sim_centered < 0.5:
                    likelihood = 0.0
                elif sim_centered > 0.9:
                    likelihood = 0.5  # High similarity could also be "similar"
                else:
                    likelihood = (sim_centered - 0.5) / 0.4  # Scale to [0, 1]
            else:
                # Unknown relation type
                likelihood = 0.5  # Uninformative
                
            likelihoods[relation] = likelihood
            
        # Convert likelihoods to probabilities (normalize)
        total_likelihood = sum(likelihoods.values())
        if total_likelihood > 0:
            probabilities = {rel: lik / total_likelihood for rel, lik in likelihoods.items()}
        else:
            # Uniform if all likelihoods are zero
            probabilities = {rel: 1.0 / len(relation_types) for rel in relation_types}
            
        return probabilities
    
    def infer_common_concept(
        self, 
        concepts: List[torch.Tensor],
        weights: Optional[List[float]] = None,
        uncertainty: float = 0.1
    ) -> ConceptPosterior:
        """
        Infer a common concept that is similar to all given concepts.
        
        Args:
            concepts: List of concept embeddings
            weights: Optional weights for each concept
            uncertainty: Uncertainty in concept representations
            
        Returns:
            Posterior distribution over the common concept
        """
        concepts = [c.to(self.device) for c in concepts]
        
        # Default to uniform weights
        if weights is None:
            weights = [1.0 / len(concepts)] * len(concepts)
            
        # Initialize with uninformative prior
        prior = self.default_prior
        posterior = ConceptPosterior(
            distribution=prior,
            log_evidence=0.0
        )
        
        # Update with each concept
        for i, concept in enumerate(concepts):
            # Create likelihood centered at this concept
            likelihood = self.create_likelihood(
                concept=concept,
                uncertainty=uncertainty / weights[i]  # Higher weight = lower uncertainty
            )
            
            # Update posterior
            posterior = posterior.update(likelihood)
            
        return posterior
    
    def infer_missing_concept(
        self, 
        observed_pairs: List[Tuple[torch.Tensor, torch.Tensor]],
        known_concept: torch.Tensor,
        relation_fn: Callable[[torch.Tensor, torch.Tensor], torch.Tensor],
        prior: Optional[GaussianDistribution] = None,
        num_samples: int = DEFAULT_NUM_SAMPLES
    ) -> ConceptPosterior:
        """
        Infer a missing concept to complete an analogical relationship.
        
        A:B :: C:?
        
        Args:
            observed_pairs: List of (A, B) pairs demonstrating the relation
            known_concept: C concept
            relation_fn: Function mapping (source, target) to relation vector
            prior: Optional prior distribution
            num_samples: Number of samples for inference
            
        Returns:
            Posterior distribution over the missing concept
        """
        # Move inputs to device
        observed_pairs = [(a.to(self.device), b.to(self.device)) for a, b in observed_pairs]
        known_concept = known_concept.to(self.device)
        
        # Use default prior if not provided
        if prior is None:
            prior = self.default_prior
            
        # Extract relation vectors from observed pairs
        relation_vectors = []
        for source, target in observed_pairs:
            rel_vec = relation_fn(source, target)
            relation_vectors.append(rel_vec)
            
        # Average relation vector
        avg_relation = torch.mean(torch.stack(relation_vectors), dim=0)
        
        # Compute expected missing concept
        expected_missing = known_concept + avg_relation
        
        # Create likelihood centered at expected missing concept
        # Uncertainty is higher with fewer observed pairs
        uncertainty = 0.1 / math.sqrt(len(observed_pairs))
        likelihood = self.create_likelihood(
            concept=expected_missing,
            uncertainty=uncertainty
        )
        
        # Update posterior
        posterior = self.update_posterior(prior, likelihood)
        
        return posterior


class ProbabilisticInferenceEngine:
    """
    Applies Bayesian inference to reasoning under uncertainty.
    
    This engine enables reasoning about concepts and relationships while 
    accounting for uncertainty in the concept representations.
    """
    
    def __init__(
        self, 
        thought_latent_space: Any = None,
        uncertainty_model: Optional[BayesianUncertaintyQuantification] = None,
        concept_dim: int = DEFAULT_DIMENSION,
        likelihood_temp: float = DEFAULT_LIKELIHOOD_TEMP,
        device: str = 'cuda' if torch.cuda.is_available() else 'cpu'
    ):
        """
        Initialize the Probabilistic Inference Engine.
        
        Args:
            thought_latent_space: Optional ThoughtLatentSpace object
            uncertainty_model: Optional BayesianUncertaintyQuantification object
            concept_dim: Dimensionality of the concept space
            likelihood_temp: Temperature parameter for likelihood functions
            device: Device for computations
        """
        self.thought_latent_space = thought_latent_space
        self.uncertainty_model = uncertainty_model
        self.concept_dim = concept_dim
        self.likelihood_temp = likelihood_temp
        self.device = device
        
        # Core inference utilities
        self.bayesian_inference = BayesianInference(
            dimension=concept_dim,
            device=device
        )
        
        # Cache for likelihood evaluations
        self._likelihood_cache = {}
        
        # Performance statistics
        self.stats = {
            'num_inferences': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'total_time': 0.0
        }
        
        logger.info(f"Initialized ProbabilisticInferenceEngine with concept_dim={concept_dim}")
    
    def infer_concept(
        self, 
        constraints: List[Tuple[str, str, float]], 
        prior_distribution: Optional[GaussianDistribution] = None,
        num_samples: int = DEFAULT_NUM_SAMPLES,
        max_iterations: int = DEFAULT_MAX_ITERATIONS
    ) -> Tuple[torch.Tensor, float]:
        """
        Infer a concept that satisfies given constraints.
        
        Args:
            constraints: List of (concept, relation, strength) tuples
                         e.g., [("cat", "similar", 0.8), ("dog", "different", 0.6)]
            prior_distribution: Optional prior distribution over concepts
            num_samples: Number of samples for inference
            max_iterations: Maximum number of iterations
            
        Returns:
            Tuple of (inferred concept embedding, confidence)
        """
        start_time = time.time()
        self.stats['num_inferences'] += 1
        
        # Set up prior distribution
        if prior_distribution is None:
            # Use uniform prior over concept space
            prior_mean = torch.zeros(self.concept_dim, device=self.device)
            prior_var = torch.ones(self.concept_dim, device=self.device)
            prior = GaussianDistribution(mean=prior_mean, covariance=prior_var, diagonal=True)
        else:
            prior = prior_distribution
            
        # Initialize samples from prior
        samples = prior.sample(num_samples)
        weights = torch.ones(num_samples, device=self.device) / num_samples
        
        # Iteratively apply constraints
        for iteration in range(max_iterations):
            # Evaluate all constraints
            for concept, relation, strength in constraints:
                # Get concept embedding from uncertainty model
                concept_belief = self.uncertainty_model.get_belief(concept) if self.uncertainty_model else None
                
                if concept_belief is None:
                    # If no belief, try to get from thought_latent_space
                    if self.thought_latent_space is not None and hasattr(self.thought_latent_space, 'get_concept'):
                        concept_vec = self.thought_latent_space.get_concept(concept)
                        if concept_vec is not None:
                            # Create belief with default uncertainty
                            concept_belief = GaussianDistribution(
                                mean=concept_vec,
                                covariance=torch.ones_like(concept_vec) * 0.1,
                                diagonal=True
                            )
                            
                if concept_belief is None:
                    logger.warning(f"No belief found for concept: {concept}")
                    continue
                    
                # Compute relation scores for all samples
                scores = self._compute_relation_score(samples, concept_belief, relation)
                
                # Apply temperature to make distribution sharper/smoother
                scores = torch.pow(scores, 1.0 / self.likelihood_temp)
                
                # Update weights based on how well samples satisfy constraint
                # Higher strength means the constraint has more influence
                weights = weights * torch.pow(scores, strength)
                
            # Check if weights are all zero or NaN
            if torch.all(torch.isnan(weights)) or torch.sum(weights) < 1e-10:
                logger.warning("All weights became zero or NaN, resetting to uniform")
                weights = torch.ones(num_samples, device=self.device) / num_samples
            else:
                # Normalize weights
                weights = weights / weights.sum()
                
            # If weights are concentrated enough, we can stop early
            max_weight = torch.max(weights)
            if max_weight > 0.5:  # One sample has >50% of the weight
                break
                
        # Compute weighted mean of samples
        inferred_concept = torch.sum(samples * weights.unsqueeze(1), dim=0)
        
        # Normalize to unit sphere
        inferred_concept = F.normalize(inferred_concept, p=2, dim=0)
        
        # Compute confidence as negative entropy of weight distribution
        # Higher entropy = lower confidence
        confidence = -torch.sum(weights * torch.log(weights + 1e-10)).item()
        confidence = 1.0 - confidence / np.log(num_samples)  # Scale to [0, 1]
        
        # Update statistics
        self.stats['total_time'] += time.time() - start_time
        
        return inferred_concept, confidence
    
    def _compute_relation_score(
        self, 
        samples: torch.Tensor, 
        concept_belief: GaussianDistribution, 
        relation: str
    ) -> torch.Tensor:
        """
        Compute relation scores between samples and a concept.
        
        Args:
            samples: Concept embeddings to evaluate [num_samples, concept_dim]
            concept_belief: Belief distribution for the concept
            relation: Type of relation to evaluate
            
        Returns:
            Scores for each sample [num_samples]
        """
        # Check cache
        cache_key = (relation, concept_belief.mean.sum().item())
        if cache_key in self._likelihood_cache:
            # Create function that computes similarity to cached mean
            cached_mean = self._likelihood_cache[cache_key]
            samples_norm = F.normalize(samples, p=2, dim=1)
            similarities = F.cosine_similarity(samples_norm, cached_mean.unsqueeze(0))
            
            self.stats['cache_hits'] += 1
        else:
            # Compute similarity to concept mean
            concept_mean = concept_belief.mean
            concept_mean_norm = F.normalize(concept_mean, p=2, dim=0)
            
            samples_norm = F.normalize(samples, p=2, dim=1)
            similarities = F.cosine_similarity(samples_norm, concept_mean_norm.unsqueeze(0))
            
            # Cache the normalized mean
            self._likelihood_cache[cache_key] = concept_mean_norm
            self.stats['cache_misses'] += 1
        
        if relation == "similar":
            # Higher similarity = higher score
            scores = (similarities + 1) / 2  # Scale from [-1, 1] to [0, 1]
            
        elif relation == "different":
            # Lower similarity = higher score
            scores = 1 - (similarities + 1) / 2  # Scale from [-1, 1] to [0, 1]
            
        elif relation == "orthogonal":
            # Orthogonal means similarity close to 0
            # Higher orthogonality = higher score
            scores = 1 - torch.abs(similarities)
            
        elif relation == "contains" or relation == "subsumes":
            # Concept should be a subset/part of samples
            # Implementation will depend on specific ontology
            # For now, use high but not perfect similarity as an approximation
            similarities_scaled = (similarities + 1) / 2  # Scale to [0, 1]
            
            # Peak likelihood at similarity = 0.8
            scores = 1.0 - torch.abs(similarities_scaled - 0.8) * 2.5
            scores = torch.clamp(scores, min=0.0, max=1.0)
            
        elif relation == "contained_by" or relation == "subsumed_by" or relation == "within":
            # Samples should be a subset/part of concept
            # For now, use high but not perfect similarity as an approximation
            similarities_scaled = (similarities + 1) / 2  # Scale to [0, 1]
            
            # Peak likelihood at similarity = 0.8
            scores = 1.0 - torch.abs(similarities_scaled - 0.8) * 2.5
            scores = torch.clamp(scores, min=0.0, max=1.0)
            
        elif relation == "antonym" or relation == "opposite":
            # Opposite concepts should have strong negative similarity
            scores = (1 - similarities) / 2  # Scale from [-1, 1] to [0, 1]
            
        elif relation == "related":
            # Related concepts should have moderate similarity
            similarities_scaled = (similarities + 1) / 2  # Scale to [0, 1]
            
            # Peak likelihood at similarity = 0.5
            scores = 1.0 - torch.abs(similarities_scaled - 0.5) * 2.0
            scores = torch.clamp(scores, min=0.0, max=1.0)
            
        else:
            logger.warning(f"Unknown relation: {relation}, using similarity as fallback")
            scores = (similarities + 1) / 2
            
        return scores
    
    def infer_relation(
        self, 
        concept1_name: str, 
        concept2_name: str, 
        relation_types: List[str] = ["similar", "different", "orthogonal"]
    ) -> Dict[str, float]:
        """
        Infer the most likely relation between two concepts.
        
        Args:
            concept1_name: Name of first concept
            concept2_name: Name of second concept
            relation_types: List of relation types to consider
            
        Returns:
            Dictionary mapping relation types to probabilities
        """
        start_time = time.time()
        
        # Get concept beliefs
        belief1 = self.uncertainty_model.get_belief(concept1_name) if self.uncertainty_model else None
        belief2 = self.uncertainty_model.get_belief(concept2_name) if self.uncertainty_model else None
        
        # Try to get from thought_latent_space if not found in uncertainty_model
        if belief1 is None and self.thought_latent_space is not None:
            if hasattr(self.thought_latent_space, 'get_concept'):
                concept1_vec = self.thought_latent_space.get_concept(concept1_name)
                if concept1_vec is not None:
                    belief1 = GaussianDistribution(
                        mean=concept1_vec,
                        covariance=torch.ones_like(concept1_vec) * 0.1,
                        diagonal=True
                    )
                    
        if belief2 is None and self.thought_latent_space is not None:
            if hasattr(self.thought_latent_space, 'get_concept'):
                concept2_vec = self.thought_latent_space.get_concept(concept2_name)
                if concept2_vec is not None:
                    belief2 = GaussianDistribution(
                        mean=concept2_vec,
                        covariance=torch.ones_like(concept2_vec) * 0.1,
                        diagonal=True
                    )
        
        if belief1 is None or belief2 is None:
            logger.warning(f"Missing belief for concept: {concept1_name if belief1 is None else concept2_name}")
            return {rel: 1.0 / len(relation_types) for rel in relation_types}
            
        # Compute mean similarity
        mean1_norm = F.normalize(belief1.mean, p=2, dim=0)
        mean2_norm = F.normalize(belief2.mean, p=2, dim=0)
        similarity = F.cosine_similarity(mean1_norm.unsqueeze(0), mean2_norm.unsqueeze(0)).item()
        
        # Initialize relation probabilities
        relation_probs = {}
        
        # Compute probability for each relation based on similarity
        for relation in relation_types:
            if relation == "similar":
                # High similarity -> high probability of "similar"
                prob = (similarity + 1) / 2
            elif relation == "different":
                # Low similarity -> high probability of "different"
                prob = 1 - (similarity + 1) / 2
            elif relation == "orthogonal":
                # Similarity near zero -> high probability of "orthogonal"
                prob = 1 - abs(similarity)
            elif relation == "contains" or relation == "subsumes":
                # Concept1 contains concept2
                # Implementation depends on ontology
                # For now, use moderate-high similarity as approximation
                sim_scaled = (similarity + 1) / 2  # Scale to [0, 1]
                
                # Peak likelihood at similarity = 0.8
                prob = 1.0 - abs(sim_scaled - 0.8) * 2.5
                prob = max(0.0, min(1.0, prob))
            elif relation == "contained_by" or relation == "subsumed_by" or relation == "within":
                # Concept2 contains concept1
                # Implementation depends on ontology
                # For now, use moderate-high similarity as approximation
                sim_scaled = (similarity + 1) / 2  # Scale to [0, 1]
                
                # Peak likelihood at similarity = 0.8
                prob = 1.0 - abs(sim_scaled - 0.8) * 2.5
                prob = max(0.0, min(1.0, prob))
            elif relation == "antonym" or relation == "opposite":
                # Opposite concepts should have strong negative similarity
                prob = (1 - similarity) / 2
            elif relation == "related":
                # Related concepts should have moderate similarity
                sim_scaled = (similarity + 1) / 2  # Scale to [0, 1]
                
                # Peak likelihood at similarity = 0.5
                prob = 1.0 - abs(sim_scaled - 0.5) * 2.0
                prob = max(0.0, min(1.0, prob))
            else:
                logger.warning(f"Unknown relation type: {relation}, assigning uniform probability")
                prob = 1.0 / len(relation_types)
                
            relation_probs[relation] = prob
            
        # Normalize probabilities
        total_prob = sum(relation_probs.values())
        relation_probs = {rel: prob / total_prob for rel, prob in relation_probs.items()}
        
        # Update statistics
        self.stats['total_time'] += time.time() - start_time
        
        return relation_probs
    
    def infer_missing_concepts(
        self, 
        concept_graph: Dict[str, Dict[str, str]],
        missing_concept: str,
        num_samples: int = DEFAULT_NUM_SAMPLES
    ) -> Tuple[torch.Tensor, float]:
        """
        Infer a missing concept in a graph of concept relationships.
        
        Args:
            concept_graph: Dictionary mapping concept names to their relations
                           e.g., {"dog": {"cat": "different", "wolf": "similar"}}
            missing_concept: Name of the concept to infer
            num_samples: Number of samples to draw for inference
            
        Returns:
            Tuple of (inferred concept embedding, confidence)
        """
        start_time = time.time()
        
        # Extract constraints involving the missing concept
        constraints = []
        
        # Check relations where missing concept is the subject
        if missing_concept in concept_graph:
            for other_concept, relation in concept_graph[missing_concept].items():
                # Check if we have a belief for the other concept
                if self.uncertainty_model and self.uncertainty_model.get_belief(other_concept) is not None:
                    constraints.append((other_concept, relation, 1.0))
                elif self.thought_latent_space and hasattr(self.thought_latent_space, 'get_concept'):
                    concept_vec = self.thought_latent_space.get_concept(other_concept)
                    if concept_vec is not None:
                        # Create temporary belief
                        self.uncertainty_model.initialize_belief(
                            other_concept,
                            mean=concept_vec,
                            variance=torch.ones_like(concept_vec) * 0.1
                        )
                        constraints.append((other_concept, relation, 1.0))
                
        # Check relations where missing concept is the object
        for other_concept, relations in concept_graph.items():
            if other_concept == missing_concept:
                continue
                
            if missing_concept in relations:
                relation = relations[missing_concept]
                
                # Check if we have a belief for the other concept
                if self.uncertainty_model and self.uncertainty_model.get_belief(other_concept) is not None:
                    # Invert relation if necessary
                    if relation == "similar":
                        # Similarity is symmetric
                        constraints.append((other_concept, "similar", 1.0))
                    elif relation == "different":
                        # Difference is symmetric
                        constraints.append((other_concept, "different", 1.0))
                    elif relation == "contains" or relation == "subsumes":
                        # Invert contains -> contained_by
                        constraints.append((other_concept, "contained_by", 1.0))
                    elif relation == "contained_by" or relation == "subsumed_by" or relation == "within":
                        # Invert contained_by -> contains
                        constraints.append((other_concept, "contains", 1.0))
                    elif relation == "antonym" or relation == "opposite":
                        # Antonym is symmetric
                        constraints.append((other_concept, "antonym", 1.0))
                    else:
                        # For unknown relations, use as is
                        constraints.append((other_concept, relation, 1.0))
                elif self.thought_latent_space and hasattr(self.thought_latent_space, 'get_concept'):
                    concept_vec = self.thought_latent_space.get_concept(other_concept)
                    if concept_vec is not None:
                        # Create temporary belief
                        if self.uncertainty_model:
                            self.uncertainty_model.initialize_belief(
                                other_concept,
                                mean=concept_vec,
                                variance=torch.ones_like(concept_vec) * 0.1
                            )
                        
                        # Apply same relation logic as above
                        if relation == "similar":
                            constraints.append((other_concept, "similar", 1.0))
                        elif relation == "different":
                            constraints.append((other_concept, "different", 1.0))
                        elif relation == "contains" or relation == "subsumes":
                            constraints.append((other_concept, "contained_by", 1.0))
                        elif relation == "contained_by" or relation == "subsumed_by" or relation == "within":
                            constraints.append((other_concept, "contains", 1.0))
                        elif relation == "antonym" or relation == "opposite":
                            constraints.append((other_concept, "antonym", 1.0))
                        else:
                            constraints.append((other_concept, relation, 1.0))
        
        if not constraints:
            logger.warning(f"No constraints found for concept: {missing_concept}")
            # Return zero vector with zero confidence
            return torch.zeros(self.concept_dim, device=self.device), 0.0
            
        # Infer concept using constraints
        result = self.infer_concept(constraints, num_samples=num_samples)
        
        # Update statistics
        self.stats['total_time'] += time.time() - start_time
        
        return result
    
    def compute_joint_probability(
        self, 
        concept_names: List[str],
        relation_graph: Dict[Tuple[int, int], str]
    ) -> float:
        """
        Compute joint probability of a set of concepts with specified relations.
        
        Args:
            concept_names: List of concept names
            relation_graph: Dictionary mapping (index1, index2) to relation type
                           e.g., {(0, 1): "similar", (1, 2): "different"}
            
        Returns:
            Joint probability
        """
        start_time = time.time()
        
        # Get concept beliefs
        beliefs = []
        for name in concept_names:
            if self.uncertainty_model:
                belief = self.uncertainty_model.get_belief(name)
                if belief is not None:
                    beliefs.append(belief)
                    continue
                    
            # Try to get from thought_latent_space if not found in uncertainty_model
            if self.thought_latent_space and hasattr(self.thought_latent_space, 'get_concept'):
                concept_vec = self.thought_latent_space.get_concept(name)
                if concept_vec is not None:
                    belief = GaussianDistribution(
                        mean=concept_vec,
                        covariance=torch.ones_like(concept_vec) * 0.1,
                        diagonal=True
                    )
                    beliefs.append(belief)
                    continue
                    
            # If still not found, log warning and return 0
            logger.warning(f"Missing belief for concept: {name}")
            return 0.0
            
        # Compute pairwise likelihoods
        log_likelihood = 0.0
        for (i, j), relation in relation_graph.items():
            if i >= len(beliefs) or j >= len(beliefs):
                logger.warning(f"Invalid indices in relation graph: ({i}, {j})")
                continue
                
            # Create cache key for this relation evaluation
            cache_key = (concept_names[i], concept_names[j], relation)
            
            # Check if we've already computed this likelihood
            if cache_key in self._likelihood_cache:
                likelihood = self._likelihood_cache[cache_key]
                self.stats['cache_hits'] += 1
            else:
                # Compute likelihood based on relation
                belief_i = beliefs[i]
                belief_j = beliefs[j]
                
                # Get means for quick computation
                mean_i = F.normalize(belief_i.mean, p=2, dim=0)
                mean_j = F.normalize(belief_j.mean, p=2, dim=0)
                
                # Compute similarity between means
                similarity = F.cosine_similarity(mean_i.unsqueeze(0), mean_j.unsqueeze(0)).item()
                
                # Compute likelihood based on relation
                if relation == "similar":
                    likelihood = (similarity + 1) / 2
                elif relation == "different":
                    likelihood = 1 - (similarity + 1) / 2
                elif relation == "orthogonal":
                    likelihood = 1 - abs(similarity)
                elif relation == "contains" or relation == "subsumes":
                    # Concept i contains concept j
                    # Implementation depends on ontology
                    # For now, use moderate-high similarity as approximation
                    sim_scaled = (similarity + 1) / 2  # Scale to [0, 1]
                    
                    # Peak likelihood at similarity = 0.8
                    likelihood = 1.0 - abs(sim_scaled - 0.8) * 2.5
                    likelihood = max(0.0, min(1.0, likelihood))
                elif relation == "contained_by" or relation == "subsumed_by" or relation == "within":
                    # Concept j contains concept i
                    # Implementation depends on ontology
                    # For now, use moderate-high similarity as approximation
                    sim_scaled = (similarity + 1) / 2  # Scale to [0, 1]
                    
                    # Peak likelihood at similarity = 0.8
                    likelihood = 1.0 - abs(sim_scaled - 0.8) * 2.5
                    likelihood = max(0.0, min(1.0, likelihood))
                elif relation == "antonym" or relation == "opposite":
                    # Opposite concepts should have strong negative similarity
                    likelihood = (1 - similarity) / 2
                else:
                    logger.warning(f"Unknown relation: {relation}, using 0.5 as fallback")
                    likelihood = 0.5
                    
                # Cache the result
                self._likelihood_cache[cache_key] = likelihood
                self.stats['cache_misses'] += 1
                
            # Accumulate log likelihood (avoid log(0) by adding a small constant)
            log_likelihood += np.log(likelihood + 1e-10)
            
        # Convert log likelihood to probability
        joint_prob = np.exp(log_likelihood)
        
        # Update statistics
        self.stats['total_time'] += time.time() - start_time
        
        return joint_prob
    
    def generate_possible_worlds(
        self, 
        concept_names: List[str],
        num_samples: int = 10
    ) -> List[Dict[str, torch.Tensor]]:
        """
        Generate possible worlds by sampling from belief distributions.
        
        Args:
            concept_names: List of concept names to include
            num_samples: Number of possible worlds to generate
            
        Returns:
            List of dictionaries mapping concept names to embeddings
        """
        start_time = time.time()
        
        # Get beliefs for all concepts
        beliefs = {}
        for name in concept_names:
            if self.uncertainty_model:
                belief = self.uncertainty_model.get_belief(name)
                if belief is not None:
                    beliefs[name] = belief
                    continue
                    
            # Try to get from thought_latent_space if not found in uncertainty_model
            if self.thought_latent_space and hasattr(self.thought_latent_space, 'get_concept'):
                concept_vec = self.thought_latent_space.get_concept(name)
                if concept_vec is not None:
                    belief = GaussianDistribution(
                        mean=concept_vec,
                        covariance=torch.ones_like(concept_vec) * 0.1,
                        diagonal=True
                    )
                    beliefs[name] = belief
                    continue
                    
            # If still not found, log warning and skip
            logger.warning(f"Missing belief for concept: {name}")
            
        if not beliefs:
            logger.warning("No beliefs found for any concepts")
            return []
            
        # Generate possible worlds by sampling
        worlds = []
        for _ in range(num_samples):
            world = {}
            for name, belief in beliefs.items():
                world[name] = belief.sample(1).squeeze(0)
                
            worlds.append(world)
            
        # Update statistics
        self.stats['total_time'] += time.time() - start_time
        
        return worlds
    
    def infer_concept_from_examples(
        self, 
        examples: List[torch.Tensor],
        uncertainty: float = 0.1,
        prior: Optional[GaussianDistribution] = None
    ) -> Tuple[torch.Tensor, float]:
        """
        Infer a concept from examples using Bayesian inference.
        
        Args:
            examples: List of example concept embeddings
            uncertainty: Uncertainty in the examples
            prior: Optional prior distribution
            
        Returns:
            Tuple of (inferred concept, confidence)
        """
        start_time = time.time()
        
        # Move examples to device
        examples = [e.to(self.device) for e in examples]
        
        # Stack examples
        stacked_examples = torch.stack(examples)
        
        # Use Bayesian inference to infer concept
        posterior = self.bayesian_inference.infer_from_examples(
            examples=stacked_examples,
            prior=prior,
            uncertainty=uncertainty
        )
        
        # Get mean and confidence
        inferred_concept = posterior.mean()
        confidence = posterior.metrics['confidence']
        
        # Update statistics
        self.stats['total_time'] += time.time() - start_time
        
        return inferred_concept, confidence
    
    def infer_analogy(
        self, 
        a: Union[str, torch.Tensor],
        b: Union[str, torch.Tensor],
        c: Union[str, torch.Tensor],
        num_samples: int = DEFAULT_NUM_SAMPLES
    ) -> Tuple[torch.Tensor, float]:
        """
        Infer analogical relationship: a is to b as c is to ?
        
        Args:
            a: First concept in the analogy
            b: Second concept in the analogy
            c: Third concept in the analogy
            num_samples: Number of samples for inference
            
        Returns:
            Tuple of (inferred concept, confidence)
        """
        start_time = time.time()
        
        # Get concept embeddings
        embeddings = []
        for concept in [a, b, c]:
            if isinstance(concept, str):
                # Try to get from uncertainty model
                if self.uncertainty_model:
                    belief = self.uncertainty_model.get_belief(concept)
                    if belief is not None:
                        embeddings.append(belief.mean)
                        continue
                        
                # Try to get from thought latent space
                if self.thought_latent_space and hasattr(self.thought_latent_space, 'get_concept'):
                    concept_vec = self.thought_latent_space.get_concept(concept)
                    if concept_vec is not None:
                        embeddings.append(concept_vec)
                        continue
                        
                logger.warning(f"Could not find concept: {concept}")
                return torch.zeros(self.concept_dim, device=self.device), 0.0
            else:
                # Already a tensor
                embeddings.append(concept.to(self.device))
                
        # Extract vectors
        a_vec, b_vec, c_vec = embeddings
        
        # Compute relation vector from a to b
        relation = b_vec - a_vec
        
        # Apply relation to c
        d_vec = c_vec + relation
        
        # Normalize
        d_vec = F.normalize(d_vec, p=2, dim=0)
        
        # Compute confidence based on consistency of relation
        # Higher confidence if a->b and c->d are similar
        a_to_b = F.normalize(relation, p=2, dim=0)
        
        # Sample possible d vectors
        d_samples = []
        for _ in range(num_samples):
            # Add noise to relation
            noisy_relation = relation + torch.randn_like(relation) * 0.1
            d_sample = c_vec + noisy_relation
            d_sample = F.normalize(d_sample, p=2, dim=0)
            d_samples.append(d_sample)
            
        # Compute variance of samples
        d_samples_tensor = torch.stack(d_samples)
        d_variance = torch.var(d_samples_tensor, dim=0).mean().item()
        
        # Convert variance to confidence
        confidence = 1.0 / (1.0 + d_variance)
        
        # Update statistics
        self.stats['total_time'] += time.time() - start_time
        
        return d_vec, confidence
    
    def infer_relation_strength(
        self, 
        source: Union[str, torch.Tensor],
        target: Union[str, torch.Tensor],
        relation: str
    ) -> float:
        """
        Infer the strength of a specific relation between concepts.
        
        Args:
            source: Source concept
            target: Target concept
            relation: Relation type
            
        Returns:
            Strength of the relation [0, 1]
        """
        start_time = time.time()
        
        # Get concept embeddings
        source_embedding = None
        target_embedding = None
        
        if isinstance(source, str):
            if self.uncertainty_model:
                belief = self.uncertainty_model.get_belief(source)
                if belief is not None:
                    source_embedding = belief.mean
                    
            # Try thought_latent_space if needed
            if source_embedding is None and self.thought_latent_space and hasattr(self.thought_latent_space, 'get_concept'):
                concept_vec = self.thought_latent_space.get_concept(source)
                if concept_vec is not None:
                    source_embedding = concept_vec
        else:
            # Already a tensor
            source_embedding = source
            
        if isinstance(target, str):
            if self.uncertainty_model:
                belief = self.uncertainty_model.get_belief(target)
                if belief is not None:
                    target_embedding = belief.mean
                    
            # Try thought_latent_space if needed
            if target_embedding is None and self.thought_latent_space and hasattr(self.thought_latent_space, 'get_concept'):
                concept_vec = self.thought_latent_space.get_concept(target)
                if concept_vec is not None:
                    target_embedding = concept_vec
        else:
            # Already a tensor
            target_embedding = target
            
        # Check if embeddings were found
        if source_embedding is None or target_embedding is None:
            logger.warning(f"Could not find embeddings for concepts")
            return 0.0
            
        # Ensure tensors are on the right device
        source_embedding = source_embedding.to(self.device)
        target_embedding = target_embedding.to(self.device)
        
        # Normalize embeddings
        source_norm = F.normalize(source_embedding, p=2, dim=0)
        target_norm = F.normalize(target_embedding, p=2, dim=0)
        
        # Compute similarity
        similarity = F.cosine_similarity(source_norm.unsqueeze(0), target_norm.unsqueeze(0)).item()
        
        # Compute relation strength based on relation type
        if relation == "similar":
            # Higher similarity = higher strength
            strength = (similarity + 1) / 2
        elif relation == "different":
            # Lower similarity = higher strength
            strength = 1 - (similarity + 1) / 2
        elif relation == "orthogonal":
            # Similarity near zero = higher strength
            strength = 1 - abs(similarity)
        elif relation == "contains" or relation == "subsumes":
            # Implementation depends on ontology
            # For now, use moderate-high similarity as approximation
            sim_scaled = (similarity + 1) / 2  # Scale to [0, 1]
            
            # Peak strength at similarity = 0.8
            strength = 1.0 - abs(sim_scaled - 0.8) * 2.5
            strength = max(0.0, min(1.0, strength))
        elif relation == "contained_by" or relation == "subsumed_by" or relation == "within":
            # Implementation depends on ontology
            # For now, use moderate-high similarity as approximation
            sim_scaled = (similarity + 1) / 2  # Scale to [0, 1]
            
            # Peak strength at similarity = 0.8
            strength = 1.0 - abs(sim_scaled - 0.8) * 2.5
            strength = max(0.0, min(1.0, strength))
        elif relation == "antonym" or relation == "opposite":
            # Lower similarity = higher strength
            strength = (1 - similarity) / 2
        elif relation == "related":
            # Moderate similarity = higher strength
            sim_scaled = (similarity + 1) / 2  # Scale to [0, 1]
            
            # Peak strength at similarity = 0.5
            strength = 1.0 - abs(sim_scaled - 0.5) * 2.0
            strength = max(0.0, min(1.0, strength))
        else:
            logger.warning(f"Unknown relation: {relation}, using similarity as fallback")
            strength = (similarity + 1) / 2
            
        # Update statistics
        self.stats['total_time'] += time.time() - start_time
        
        return strength
    
    def infer_common_concept(
        self, 
        concepts: List[Union[str, torch.Tensor]],
        weights: Optional[List[float]] = None,
        uncertainty: float = 0.1
    ) -> Tuple[torch.Tensor, float]:
        """
        Infer a common concept that is similar to all given concepts.
        
        Args:
            concepts: List of concepts (names or embeddings)
            weights: Optional weights for each concept
            uncertainty: Uncertainty in concept representations
            
        Returns:
            Tuple of (inferred concept, confidence)
        """
        start_time = time.time()
        
        # Get concept embeddings
        embeddings = []
        for concept in concepts:
            if isinstance(concept, str):
                # Try to get from uncertainty model
                if self.uncertainty_model:
                    belief = self.uncertainty_model.get_belief(concept)
                    if belief is not None:
                        embeddings.append(belief.mean)
                        continue
                        
                # Try to get from thought latent space
                if self.thought_latent_space and hasattr(self.thought_latent_space, 'get_concept'):
                    concept_vec = self.thought_latent_space.get_concept(concept)
                    if concept_vec is not None:
                        embeddings.append(concept_vec)
                        continue
                        
                logger.warning(f"Could not find concept: {concept}")
            else:
                # Already a tensor
                embeddings.append(concept.to(self.device))
                
        if not embeddings:
            logger.warning("No valid concepts provided")
            return torch.zeros(self.concept_dim, device=self.device), 0.0
            
        # Default to uniform weights
        if weights is None:
            weights = [1.0 / len(embeddings)] * len(embeddings)
        elif len(weights) != len(embeddings):
            logger.warning(f"Number of weights ({len(weights)}) doesn't match number of concepts ({len(embeddings)})")
            weights = [1.0 / len(embeddings)] * len(embeddings)
            
        # Use Bayesian inference for common concept
        posterior = self.bayesian_inference.infer_common_concept(
            concepts=embeddings,
            weights=weights,
            uncertainty=uncertainty
        )
        
        # Extract mean and confidence
        common_concept = posterior.mean()
        confidence = posterior.metrics['confidence']
        
        # Update statistics
        self.stats['total_time'] += time.time() - start_time
        
        return common_concept, confidence
    
    def recommend_concepts(
        self, 
        query: Union[str, torch.Tensor], 
        candidate_concepts: Dict[str, torch.Tensor],
        relation: str = "similar",
        top_k: int = 5,
        diversity_weight: float = 0.2
    ) -> List[Tuple[str, float]]:
        """
        Recommend concepts related to a query based on the specified relation.
        
        Args:
            query: Query concept name or embedding
            candidate_concepts: Dictionary of candidate concept names to embeddings
            relation: Relation to consider
            top_k: Number of recommendations to return
            diversity_weight: Weight for diversity in recommendations
            
        Returns:
            List of (concept_name, score) tuples
        """
        start_time = time.time()
        
        # Get query embedding
        query_embedding = None
        
        if isinstance(query, str):
            if self.uncertainty_model:
                belief = self.uncertainty_model.get_belief(query)
                if belief is not None:
                    query_embedding = belief.mean
                    
            # Try thought_latent_space if needed
            if query_embedding is None and self.thought_latent_space and hasattr(self.thought_latent_space, 'get_concept'):
                concept_vec = self.thought_latent_space.get_concept(query)
                if concept_vec is not None:
                    query_embedding = concept_vec
        else:
            # Already a tensor
            query_embedding = query
            
        if query_embedding is None:
            logger.warning(f"Could not find embedding for query: {query}")
            return []
            
        # Ensure query is on the right device
        query_embedding = query_embedding.to(self.device)
        
        # Compute relation strength with each candidate
        scores = {}
        for name, embedding in candidate_concepts.items():
            # Skip if query and candidate are the same
            if isinstance(query, str) and name == query:
                continue
                
            # Compute relation strength
            strength = self.infer_relation_strength(
                source=query_embedding,
                target=embedding.to(self.device),
                relation=relation
            )
            
            scores[name] = strength
            
        # No valid candidates
        if not scores:
            return []
            
        # Select top-k with diversity
        selected = []
        remaining = list(scores.keys())
        
        # First select the highest scoring candidate
        first = max(remaining, key=lambda x: scores[x])
        selected.append(first)
        remaining.remove(first)
        
        # Select the rest with diversity consideration
        while len(selected) < top_k and remaining:
            # Compute diversity term for each remaining candidate
            diversity_scores = {}
            
            for candidate in remaining:
                # Compute average similarity with already selected concepts
                avg_sim = 0.0
                for sel in selected:
                    sim = F.cosine_similarity(
                        F.normalize(candidate_concepts[candidate].to(self.device), p=2, dim=0).unsqueeze(0),
                        F.normalize(candidate_concepts[sel].to(self.device), p=2, dim=0).unsqueeze(0)
                    ).item()
                    avg_sim += (sim + 1) / 2  # Scale from [-1, 1] to [0, 1]
                
                if selected:
                    avg_sim /= len(selected)
                    
                # Diversity score is inverse of similarity
                diversity = 1.0 - avg_sim
                
                # Combined score
                combined_score = (1.0 - diversity_weight) * scores[candidate] + diversity_weight * diversity
                diversity_scores[candidate] = combined_score
                
            # Select the candidate with highest combined score
            next_candidate = max(remaining, key=lambda x: diversity_scores[x])
            selected.append(next_candidate)
            remaining.remove(next_candidate)
            
        # Create result list with scores
        result = [(name, scores[name]) for name in selected]
        
        # Update statistics
        self.stats['total_time'] += time.time() - start_time
        
        return result
    
    def evaluate_consistency(
        self, 
        concept_names: List[str],
        relation_graph: Dict[Tuple[int, int], str]
    ) -> Dict[str, Any]:
        """
        Evaluate the consistency of a concept graph.
        
        Args:
            concept_names: List of concept names
            relation_graph: Dictionary mapping (index1, index2) to relation type
            
        Returns:
            Dictionary with consistency metrics
        """
        start_time = time.time()
        
        # Get joint probability as overall consistency measure
        joint_prob = self.compute_joint_probability(concept_names, relation_graph)
        
        # Evaluate pairwise consistencies
        pairwise = {}
        for (i, j), relation in relation_graph.items():
            if i >= len(concept_names) or j >= len(concept_names):
                continue
                
            # Get strength of the relation
            strength = self.infer_relation_strength(
                source=concept_names[i],
                target=concept_names[j],
                relation=relation
            )
            
            pairwise[(concept_names[i], concept_names[j], relation)] = strength
            
        # Compute transitivity violations
        transitivity_violations = []
        
        # Check transitivity patterns
        # e.g., If A similar B and B similar C, then A should be similar to C
        edges = {}
        for (i, j), relation in relation_graph.items():
            if i < len(concept_names) and j < len(concept_names):
                edges[(i, j)] = relation
                
        for i in range(len(concept_names)):
            for j in range(len(concept_names)):
                if i == j or (i, j) not in edges:
                    continue
                    
                for k in range(len(concept_names)):
                    if j == k or k == i or (j, k) not in edges:
                        continue
                        
                    # Check if i->k exists
                    if (i, k) not in edges:
                        continue
                        
                    # We have a transitivity pattern: i->j->k and i->k
                    rel_ij = edges[(i, j)]
                    rel_jk = edges[(j, k)]
                    rel_ik = edges[(i, k)]
                    
                    # Check for potential violations
                    violation = False
                    
                    # Similarity should be transitive
                    if rel_ij == "similar" and rel_jk == "similar" and rel_ik != "similar":
                        violation = True
                        
                    # Containment should be transitive
                    if rel_ij == "contains" and rel_jk == "contains" and rel_ik != "contains":
                        violation = True
                        
                    if violation:
                        transitivity_violations.append({
                            'path': [
                                concept_names[i],
                                concept_names[j],
                                concept_names[k]
                            ],
                            'relations': [rel_ij, rel_jk, rel_ik]
                        })
        
        # Compute average consistency
        avg_consistency = sum(pairwise.values()) / max(1, len(pairwise))
        
        result = {
            'joint_probability': joint_prob,
            'average_consistency': avg_consistency,
            'pairwise_consistencies': pairwise,
            'transitivity_violations': transitivity_violations,
            'num_transitivity_violations': len(transitivity_violations)
        }
        
        # Update statistics
        self.stats['total_time'] += time.time() - start_time
        
        return result
    
    def clear_cache(self):
        """Clear the likelihood evaluation cache."""
        self._likelihood_cache = {}
        logger.info("Cleared likelihood cache")
        
    def get_stats(self) -> Dict[str, Any]:
        """
        Get performance statistics for the inference engine.
        
        Returns:
            Dictionary with performance statistics
        """
        # Compute cache hit rate
        total_cache_accesses = self.stats['cache_hits'] + self.stats['cache_misses']
        if total_cache_accesses > 0:
            hit_rate = self.stats['cache_hits'] / total_cache_accesses
        else:
            hit_rate = 0.0
            
        # Compute average time per inference
        if self.stats['num_inferences'] > 0:
            avg_time = self.stats['total_time'] / self.stats['num_inferences']
        else:
            avg_time = 0.0
            
        return {
            'num_inferences': self.stats['num_inferences'],
            'cache_hits': self.stats['cache_hits'],
            'cache_misses': self.stats['cache_misses'],
            'cache_hit_rate': hit_rate,
            'total_time': self.stats['total_time'],
            'avg_time_per_inference': avg_time,
            'cache_size': len(self._likelihood_cache)
        }
    
    def to(self, device: str) -> 'ProbabilisticInferenceEngine':
        """
        Move the engine to the specified device.
        
        Args:
            device: Target device ('cuda', 'cpu', etc.)
            
        Returns:
            Self for chaining
        """
        self.device = device
        self.bayesian_inference.device = device
        
        return self

# Module exports
__all__ = [
    'ConceptPosterior',
    'EvidenceEstimation',
    'BayesianInference',
    'ProbabilisticInferenceEngine',
    'marginal_likelihood',
    'conditional_sampling'
]