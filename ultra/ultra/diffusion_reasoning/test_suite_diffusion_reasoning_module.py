#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ULTRA: Ultimate Learning & Thought Reasoning Architecture
Comprehensive Test Suite for Diffusion-Based Reasoning Module

This comprehensive test suite validates all components of the diffusion-based reasoning
module, including mathematical correctness, integration capabilities, performance
characteristics, and robustness under various conditions.

Test Coverage:
1. Conceptual Diffusion Process - Forward/reverse diffusion mathematics
2. Thought Latent Space - Encoding/decoding and semantic operations  
3. Reverse Diffusion Reasoning - Goal-directed reasoning and constraint satisfaction
4. Bayesian Uncertainty Quantification - Epistemic/aleatoric uncertainty decomposition
5. Probabilistic Inference - Bayesian inference and evidence estimation
6. Integration Testing - Cross-component interaction validation
7. Performance Benchmarking - Computational efficiency and scalability
8. Robustness Testing - Edge cases and failure mode analysis

Mathematical Validation:
- Diffusion process mathematical properties
- Uncertainty quantification accuracy
- Inference convergence guarantees
- Semantic similarity preservation
- Optimization landscape properties
"""

import os
import sys
import unittest
import logging
import warnings
import time
import threading
import signal  # Add this import
from pathlib import Path
from typing import Dict, List, Tuple, Union, Optional, Callable, Any
from dataclasses import dataclass, field
from enum import Enum, auto
import traceback

import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.distributions import MultivariateNormal, Normal, kl_divergence
from torch.utils.data import DataLoader, TensorDataset
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from scipy.spatial.distance import pdist, squareform

# Fix sklearn imports
from sklearn.metrics import brier_score_loss
try:
    from sklearn.calibration import calibration_curve
except ImportError:
    # Fallback for older sklearn versions
    from sklearn.metrics import calibration_curve

from sklearn.datasets import make_classification
from sklearn.model_selection import train_test_split

# Suppress warnings for cleaner test output
warnings.filterwarnings('ignore', category=UserWarning)
warnings.filterwarnings('ignore', category=FutureWarning)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('ultra_diffusion_test_suite.log')
    ]
)
logger = logging.getLogger(__name__)

# Test constants
TEST_DEVICE = 'cuda' if torch.cuda.is_available() else 'cpu'
TEST_CONCEPT_DIM = 128
TEST_THOUGHT_SPACE_DIM = 256
TEST_DIFFUSION_STEPS = 100
TEST_BATCH_SIZE = 16
TEST_NUM_SAMPLES = 32
NUMERICAL_TOLERANCE = 1e-5
PERFORMANCE_TOLERANCE = 2.0  # seconds
MEMORY_LIMIT_GB = 8.0

# =============================================================================
# Test Data Structures and Utilities
# =============================================================================

class TestResult(Enum):
    """Test result enumeration."""
    PASS = auto()
    FAIL = auto()
    SKIP = auto()
    ERROR = auto()

@dataclass
class TestCase:
    """Test case data structure."""
    name: str
    description: str
    test_function: Callable
    expected_result: TestResult = TestResult.PASS
    timeout_seconds: float = 30.0
    requires_gpu: bool = False
    memory_intensive: bool = False

@dataclass
class TestMetrics:
    """Test execution metrics."""
    execution_time: float = 0.0
    memory_usage_mb: float = 0.0
    gpu_memory_mb: float = 0.0
    numerical_error: float = 0.0
    convergence_steps: int = 0
    
@dataclass
class TestReport:
    """Comprehensive test report."""
    test_name: str
    result: TestResult
    metrics: TestMetrics
    error_message: str = ""
    details: Dict[str, Any] = field(default_factory=dict)

class TestDataGenerator:
    """Generates synthetic test data for validation."""
    
    @staticmethod
    def generate_concept_embeddings(num_concepts: int, concept_dim: int, 
                                   device: str = 'cpu') -> torch.Tensor:
        """Generate diverse concept embeddings with known semantic structure."""
        # Create structured concept space with semantic clusters
        cluster_centers = torch.randn(5, concept_dim, device=device) * 2.0
        concepts = []
        
        for i in range(num_concepts):
            cluster_idx = i % 5
            noise = torch.randn(concept_dim, device=device) * 0.5
            concept = cluster_centers[cluster_idx] + noise
            concept = F.normalize(concept, dim=0)  # Normalize to unit sphere
            concepts.append(concept)
        
        return torch.stack(concepts)
    
    @staticmethod
    def generate_reasoning_paths(start_concepts: torch.Tensor, 
                               goal_concepts: torch.Tensor,
                               path_length: int = 5) -> List[List[torch.Tensor]]:
        """Generate synthetic reasoning paths from start to goal concepts."""
        paths = []
        for start, goal in zip(start_concepts, goal_concepts):
            path = []
            for t in range(path_length + 1):
                alpha = t / path_length
                interpolated = (1 - alpha) * start + alpha * goal
                interpolated = F.normalize(interpolated, dim=0)
                path.append(interpolated)
            paths.append(path)
        return paths
    
    @staticmethod
    def generate_test_uncertainty_data(num_samples: int, feature_dim: int, 
                                     noise_level: float = 0.1) -> Tuple[torch.Tensor, torch.Tensor]:
        """Generate data with known uncertainty characteristics."""
        # Create nonlinear function with heteroscedastic noise
        X = torch.randn(num_samples, feature_dim)
        # Nonlinear target with input-dependent noise
        y_mean = torch.sin(X.sum(dim=1)) + 0.5 * (X**2).sum(dim=1)
        noise_std = noise_level * (1 + 0.5 * torch.abs(X.sum(dim=1)))
        y = y_mean + noise_std * torch.randn(num_samples)
        return X, y.unsqueeze(1)

class MathematicalValidator:
    """Validates mathematical properties of diffusion processes."""
    
    @staticmethod
    def validate_diffusion_forward_process(x0: torch.Tensor, 
                                         alphas_cumprod: torch.Tensor,
                                         tolerance: float = 1e-4) -> bool:
        """Validate mathematical properties of forward diffusion process."""
        try:
            T = len(alphas_cumprod)
            batch_size = x0.shape[0]
            
            # Test: Forward process preserves expected value initially
            t = torch.randint(0, T//4, (batch_size,))
            noise = torch.randn_like(x0)
            
            # Compute noisy samples
            sqrt_alpha_cumprod_t = alphas_cumprod[t].sqrt().unsqueeze(-1)
            sqrt_one_minus_alpha_cumprod_t = (1 - alphas_cumprod[t]).sqrt().unsqueeze(-1)
            
            x_t = sqrt_alpha_cumprod_t * x0 + sqrt_one_minus_alpha_cumprod_t * noise
            
            # Validate variance schedule monotonicity
            alpha_diffs = torch.diff(alphas_cumprod)
            monotonic = torch.all(alpha_diffs <= 0)
            
            # Validate noise scale
            expected_var = 1 - alphas_cumprod[t]
            actual_var = torch.var(x_t, dim=0, unbiased=True)
            var_error = torch.abs(expected_var.unsqueeze(-1) - actual_var).max()
            
            return monotonic and var_error < tolerance
            
        except Exception as e:
            logger.error(f"Forward process validation failed: {e}")
            return False
    
    @staticmethod
    def validate_uncertainty_decomposition(epistemic: torch.Tensor,
                                         aleatoric: torch.Tensor,
                                         total: torch.Tensor,
                                         tolerance: float = 1e-3) -> bool:
        """Validate uncertainty decomposition mathematical properties."""
        try:
            # Test: Total uncertainty >= epistemic uncertainty
            condition1 = torch.all(total >= epistemic - tolerance)
            
            # Test: Total uncertainty >= aleatoric uncertainty  
            condition2 = torch.all(total >= aleatoric - tolerance)
            
            # Test: Uncertainties are non-negative
            condition3 = torch.all(epistemic >= -tolerance)
            condition4 = torch.all(aleatoric >= -tolerance)
            condition5 = torch.all(total >= -tolerance)
            
            # Test: Approximate additivity (may not be exact due to interactions)
            sum_uncertainty = epistemic + aleatoric
            additivity_error = torch.abs(total - sum_uncertainty).max()
            condition6 = additivity_error < tolerance * 10  # Allow more tolerance for additivity
            
            return all([condition1, condition2, condition3, condition4, condition5, condition6])
            
        except Exception as e:
            logger.error(f"Uncertainty decomposition validation failed: {e}")
            return False
    
    @staticmethod
    def validate_semantic_consistency(embeddings: torch.Tensor,
                                    similarity_matrix: torch.Tensor,
                                    tolerance: float = 1e-3) -> bool:
        """Validate semantic similarity mathematical properties."""
        try:
            n = embeddings.shape[0]
            
            # Test: Similarity matrix is symmetric
            symmetry_error = torch.abs(similarity_matrix - similarity_matrix.T).max()
            condition1 = symmetry_error < tolerance
            
            # Test: Diagonal elements are maximum (self-similarity)
            diag_vals = torch.diag(similarity_matrix)
            max_vals = torch.max(similarity_matrix, dim=1)[0]
            condition2 = torch.all(torch.abs(diag_vals - max_vals) < tolerance)
            
            # Test: Similarity values in valid range [-1, 1] for cosine similarity
            condition3 = torch.all(similarity_matrix >= -1 - tolerance)
            condition4 = torch.all(similarity_matrix <= 1 + tolerance)
            
            # Test: Triangle inequality (approximately)
            # For cosine similarity: d(a,c) <= d(a,b) + d(b,c) where d = 1 - similarity
            distances = 1 - similarity_matrix
            triangle_violations = 0
            for i in range(n):
                for j in range(n):
                    for k in range(n):
                        if distances[i,k] > distances[i,j] + distances[j,k] + tolerance:
                            triangle_violations += 1
            
            condition5 = triangle_violations < n * 0.1  # Allow some violations due to numerical precision
            
            return all([condition1, condition2, condition3, condition4, condition5])
            
        except Exception as e:
            logger.error(f"Semantic consistency validation failed: {e}")
            return False

# =============================================================================
# Conceptual Diffusion Process Tests
# =============================================================================

class ConceptualDiffusionTester:
    """Comprehensive test suite for conceptual diffusion processes."""
    
    def __init__(self, device: str = TEST_DEVICE):
        self.device = device
        self.concept_dim = TEST_CONCEPT_DIM
        self.diffusion_steps = TEST_DIFFUSION_STEPS
        self.batch_size = TEST_BATCH_SIZE
        self.setup_diffusion_parameters()
    
    def setup_diffusion_parameters(self):
        """Initialize diffusion process parameters."""
        # Linear beta schedule
        self.betas = torch.linspace(1e-4, 0.02, self.diffusion_steps, device=self.device)
        self.alphas = 1.0 - self.betas
        self.alphas_cumprod = torch.cumprod(self.alphas, dim=0)
        self.alphas_cumprod_prev = torch.cat([torch.tensor([1.0], device=self.device), 
                                             self.alphas_cumprod[:-1]])
        
        # Derived parameters
        self.sqrt_alphas_cumprod = torch.sqrt(self.alphas_cumprod)
        self.sqrt_one_minus_alphas_cumprod = torch.sqrt(1.0 - self.alphas_cumprod)
        self.sqrt_recip_alphas = torch.sqrt(1.0 / self.alphas)
        
        # Posterior variance
        self.posterior_variance = self.betas * (1.0 - self.alphas_cumprod_prev) / (1.0 - self.alphas_cumprod)
        
        # Posterior mean coefficients
        self.posterior_mean_coef1 = self.betas * torch.sqrt(self.alphas_cumprod_prev) / (1.0 - self.alphas_cumprod)
        self.posterior_mean_coef2 = (1.0 - self.alphas_cumprod_prev) * torch.sqrt(self.alphas) / (1.0 - self.alphas_cumprod)
    
    def test_forward_diffusion_process(self) -> TestReport:
        """Test forward diffusion process mathematical properties."""
        start_time = time.time()
        
        try:
            # Generate test concepts
            x0 = TestDataGenerator.generate_concept_embeddings(
                self.batch_size, self.concept_dim, self.device
            )
            
            # Test forward process at multiple timesteps
            timesteps = [10, 50, 90, self.diffusion_steps - 1]
            forward_errors = []
            
            for t in timesteps:
                # Sample noise
                noise = torch.randn_like(x0)
                
                # Apply forward process
                sqrt_alpha_cumprod_t = self.sqrt_alphas_cumprod[t]
                sqrt_one_minus_alpha_cumprod_t = self.sqrt_one_minus_alphas_cumprod[t]
                
                x_t = sqrt_alpha_cumprod_t * x0 + sqrt_one_minus_alpha_cumprod_t * noise
                
                # Validate properties
                # 1. Mean preservation (approximately)
                expected_mean = sqrt_alpha_cumprod_t * x0.mean(dim=0)
                actual_mean = x_t.mean(dim=0)
                mean_error = torch.abs(expected_mean - actual_mean).max().item()
                
                # 2. Variance schedule
                expected_var = 1 - self.alphas_cumprod[t]
                actual_var = torch.var(x_t.flatten())
                var_error = abs(expected_var.item() - actual_var.item())
                
                forward_errors.append(mean_error + var_error)
            
            # Validate mathematical properties
            math_valid = MathematicalValidator.validate_diffusion_forward_process(
                x0, self.alphas_cumprod
            )
            
            execution_time = time.time() - start_time
            avg_error = np.mean(forward_errors)
            
            # Determine test result
            result = TestResult.PASS if (math_valid and avg_error < NUMERICAL_TOLERANCE) else TestResult.FAIL
            
            return TestReport(
                test_name="forward_diffusion_process",
                result=result,
                metrics=TestMetrics(
                    execution_time=execution_time,
                    numerical_error=avg_error
                ),
                details={
                    'mathematical_validity': math_valid,
                    'average_error': avg_error,
                    'timestep_errors': forward_errors,
                    'beta_schedule_monotonic': torch.all(torch.diff(self.betas) >= 0).item()
                }
            )
            
        except Exception as e:
            return TestReport(
                test_name="forward_diffusion_process",
                result=TestResult.ERROR,
                metrics=TestMetrics(execution_time=time.time() - start_time),
                error_message=str(e)
            )
    
    def test_reverse_diffusion_sampling(self) -> TestReport:
        """Test reverse diffusion sampling process."""
        start_time = time.time()
        
        try:
            # Around line 590 - Fix the first SimpleNoisePredictor
            class SimpleNoisePredictor(nn.Module):
                def __init__(self, concept_dim, diffusion_steps):  # Add diffusion_steps parameter
                    super().__init__()
                    self.diffusion_steps = diffusion_steps  # Store as instance variable
                    self.net = nn.Sequential(
                        nn.Linear(concept_dim + 1, concept_dim * 2),  # +1 for timestep
                        nn.ReLU(),
                        nn.Linear(concept_dim * 2, concept_dim * 2),
                        nn.ReLU(),
                        nn.Linear(concept_dim * 2, concept_dim)
                    )
                
                def forward(self, x_t, t):
                    # Embed timestep
                    t_emb = t.float().unsqueeze(-1) / self.diffusion_steps
                    t_emb = t_emb.expand(-1, 1)
                    
                    # Concatenate input with timestep
                    input_tensor = torch.cat([x_t, t_emb], dim=-1)
                    return self.net(input_tensor)

            # Update the initialization around line 630:
            noise_predictor = SimpleNoisePredictor(self.concept_dim, self.diffusion_steps).to(self.device)
            
            # Generate training data for noise predictor
            x0_train = TestDataGenerator.generate_concept_embeddings(
                200, self.concept_dim, self.device
            )
            
            # Quick training loop for noise predictor
            optimizer = torch.optim.Adam(noise_predictor.parameters(), lr=1e-3)
            
            for epoch in range(50):
                # Sample random timesteps and noise
                t = torch.randint(0, self.diffusion_steps, (x0_train.shape[0],), device=self.device)
                noise = torch.randn_like(x0_train)
                
                # Forward process
                sqrt_alpha_cumprod_t = self.sqrt_alphas_cumprod[t].unsqueeze(-1)
                sqrt_one_minus_alpha_cumprod_t = self.sqrt_one_minus_alphas_cumprod[t].unsqueeze(-1)
                x_t = sqrt_alpha_cumprod_t * x0_train + sqrt_one_minus_alpha_cumprod_t * noise
                
                # Predict noise
                predicted_noise = noise_predictor(x_t, t)
                
                # Compute loss
                loss = F.mse_loss(predicted_noise, noise)
                
                # Optimize
                optimizer.zero_grad()
                loss.backward()
                optimizer.step()
            
            # Test reverse sampling
            noise_predictor.eval()
            with torch.no_grad():
                # Start from pure noise
                x_t = torch.randn(self.batch_size, self.concept_dim, device=self.device)
                
                # Reverse diffusion
                sampling_errors = []
                for t in reversed(range(self.diffusion_steps)):
                    t_tensor = torch.full((self.batch_size,), t, device=self.device)
                    
                    # Predict noise
                    predicted_noise = noise_predictor(x_t, t_tensor)
                    
                    # Compute x_{t-1}
                    if t > 0:
                        # Compute mean
                        mean = (1.0 / torch.sqrt(self.alphas[t])) * (
                            x_t - (self.betas[t] / self.sqrt_one_minus_alphas_cumprod[t]) * predicted_noise
                        )
                        
                        # Add noise
                        variance = self.posterior_variance[t]
                        if variance > 0:
                            noise = torch.randn_like(x_t)
                            x_t = mean + torch.sqrt(variance) * noise
                        else:
                            x_t = mean
                    else:
                        # Final step
                        x_t = (1.0 / torch.sqrt(self.alphas[t])) * (
                            x_t - (self.betas[t] / self.sqrt_one_minus_alphas_cumprod[t]) * predicted_noise
                        )
                    
                    # Track sampling quality
                    if t % 20 == 0:
                        # Check if samples are reasonable (finite and bounded)
                        is_finite = torch.isfinite(x_t).all()
                        is_bounded = torch.abs(x_t).max() < 10.0
                        sampling_errors.append(0.0 if (is_finite and is_bounded) else 1.0)
                
                # Final validation
                final_samples = x_t
                
                # Check sample quality
                sample_mean = final_samples.mean()
                sample_std = final_samples.std()
                sample_finite = torch.isfinite(final_samples).all()
                
                # Check diversity (samples should not be identical)
                pairwise_distances = torch.cdist(final_samples, final_samples)
                min_distance = pairwise_distances[pairwise_distances > 0].min()
                diversity_score = min_distance.item()
            
            execution_time = time.time() - start_time
            avg_sampling_error = np.mean(sampling_errors)
            
            # Determine test result
            result = TestResult.PASS if (
                sample_finite and avg_sampling_error < 0.1 and diversity_score > 0.01
            ) else TestResult.FAIL
            
            return TestReport(
                test_name="reverse_diffusion_sampling",
                result=result,
                metrics=TestMetrics(
                    execution_time=execution_time,
                    numerical_error=avg_sampling_error
                ),
                details={
                    'sample_finite': sample_finite.item(),
                    'sample_mean': sample_mean.item(),
                    'sample_std': sample_std.item(),
                    'diversity_score': diversity_score,
                    'avg_sampling_error': avg_sampling_error
                }
            )
            
        except Exception as e:
            return TestReport(
                test_name="reverse_diffusion_sampling",
                result=TestResult.ERROR,
                metrics=TestMetrics(execution_time=time.time() - start_time),
                error_message=str(e)
            )
    
    def test_diffusion_loss_function(self) -> TestReport:
        """Test diffusion training loss function properties."""
        start_time = time.time()
        
        try:
            # Generate test data
            x0 = TestDataGenerator.generate_concept_embeddings(
                self.batch_size, self.concept_dim, self.device
            )
            
            # Test loss function at different timesteps
            loss_values = []
            loss_gradients = []
            
            for t_val in [0, self.diffusion_steps//4, self.diffusion_steps//2, 
                         3*self.diffusion_steps//4, self.diffusion_steps-1]:
                
                t = torch.full((self.batch_size,), t_val, device=self.device)
                noise = torch.randn_like(x0)
                
                # Forward process
                sqrt_alpha_cumprod_t = self.sqrt_alphas_cumprod[t].unsqueeze(-1)
                sqrt_one_minus_alpha_cumprod_t = self.sqrt_one_minus_alphas_cumprod[t].unsqueeze(-1)
                x_t = sqrt_alpha_cumprod_t * x0 + sqrt_one_minus_alpha_cumprod_t * noise
                
                # Simple noise prediction (identity for testing)
                predicted_noise = torch.zeros_like(noise)
                predicted_noise.requires_grad_(True)
                
                # Compute loss
                loss = F.mse_loss(predicted_noise, noise)
                loss_values.append(loss.item())
                
                # Compute gradient
                loss.backward()
                if predicted_noise.grad is not None:
                    grad_norm = predicted_noise.grad.norm().item()
                    loss_gradients.append(grad_norm)
                else:
                    loss_gradients.append(0.0)
            
            # Validate loss properties
            # 1. Loss should be finite and positive
            all_finite = all(np.isfinite(loss_values))
            all_positive = all(l >= 0 for l in loss_values)
            
            # 2. Gradients should be reasonable
            grad_finite = all(np.isfinite(loss_gradients))
            grad_reasonable = all(g < 100.0 for g in loss_gradients)
            
            # 3. Loss should vary with timestep (not constant)
            loss_variance = np.var(loss_values)
            has_variance = loss_variance > 1e-6
            
            execution_time = time.time() - start_time
            avg_loss = np.mean(loss_values)
            avg_grad = np.mean(loss_gradients)
            
            # Determine test result
            result = TestResult.PASS if (
                all_finite and all_positive and grad_finite and grad_reasonable
            ) else TestResult.FAIL
            
            return TestReport(
                test_name="diffusion_loss_function",
                result=result,
                metrics=TestMetrics(
                    execution_time=execution_time,
                    numerical_error=avg_loss
                ),
                details={
                    'loss_values': loss_values,
                    'gradient_norms': loss_gradients,
                    'loss_variance': loss_variance,
                    'all_finite': all_finite,
                    'all_positive': all_positive,
                    'grad_finite': grad_finite,
                    'grad_reasonable': grad_reasonable,
                    'has_variance': has_variance
                }
            )
            
        except Exception as e:
            return TestReport(
                test_name="diffusion_loss_function",
                result=TestResult.ERROR,
                metrics=TestMetrics(execution_time=time.time() - start_time),
                error_message=str(e)
            )

# =============================================================================
# Thought Latent Space Tests
# =============================================================================

class ThoughtLatentSpaceTester:
    """Comprehensive test suite for thought latent space operations."""
    
    def __init__(self, device: str = TEST_DEVICE):
        self.device = device
        self.concept_dim = TEST_CONCEPT_DIM
        self.thought_space_dim = TEST_THOUGHT_SPACE_DIM
        self.batch_size = TEST_BATCH_SIZE
        self.hierarchical_levels = 3
    
    def test_concept_encoding_decoding(self) -> TestReport:
        """Test concept encoding and decoding consistency."""
        start_time = time.time()
        
        try:
            # Create encoder-decoder architecture
            class ConceptEncoder(nn.Module):
                def __init__(self, input_dim, output_dim, num_levels):
                    super().__init__()
                    self.num_levels = num_levels
                    self.encoders = nn.ModuleList([
                        nn.Sequential(
                            nn.Linear(input_dim if i == 0 else output_dim, output_dim),
                            nn.ReLU(),
                            nn.Linear(output_dim, output_dim),
                            nn.LayerNorm(output_dim)
                        ) for i in range(num_levels)
                    ])
                
                def forward(self, x):
                    encodings = []
                    h = x
                    for encoder in self.encoders:
                        h = encoder(h)
                        encodings.append(h)
                    return encodings
            
            class ConceptDecoder(nn.Module):
                def __init__(self, input_dim, output_dim, num_levels):
                    super().__init__()
                    self.num_levels = num_levels
                    self.decoders = nn.ModuleList([
                        nn.Sequential(
                            nn.Linear(input_dim, input_dim),
                            nn.ReLU(),
                            nn.Linear(input_dim, output_dim if i == num_levels-1 else input_dim),
                            nn.LayerNorm(output_dim if i == num_levels-1 else input_dim)
                        ) for i in range(num_levels)
                    ])
                
                def forward(self, encodings):
                    h = encodings[-1]  # Start from highest level
                    for i, decoder in enumerate(reversed(self.decoders)):
                        h = decoder(h)
                        if i < len(encodings) - 1:
                            # Add skip connection from corresponding encoding level
                            skip_idx = len(encodings) - 2 - i
                            if skip_idx >= 0 and h.shape == encodings[skip_idx].shape:
                                h = h + encodings[skip_idx]
                    return h
            
            # Initialize models
            encoder = ConceptEncoder(self.concept_dim, self.thought_space_dim, 
                                   self.hierarchical_levels).to(self.device)
            decoder = ConceptDecoder(self.thought_space_dim, self.concept_dim,
                                   self.hierarchical_levels).to(self.device)
            
            # Generate test concepts
            test_concepts = TestDataGenerator.generate_concept_embeddings(
                self.batch_size, self.concept_dim, self.device
            )
            
            # Test encoding-decoding cycle
            encoder.eval()
            decoder.eval()
            
            with torch.no_grad():
                # Encode concepts
                encodings = encoder(test_concepts)
                
                # Decode back to concept space
                reconstructed = decoder(encodings)
                
                # Compute reconstruction error
                reconstruction_error = F.mse_loss(reconstructed, test_concepts).item()
                
                # Test encoding properties
                # 1. Encodings should be finite
                all_finite = all(torch.isfinite(enc).all() for enc in encodings)
                
                # 2. Hierarchical property (higher levels should be more abstract)
                level_norms = [enc.norm(dim=-1).mean().item() for enc in encodings]
                
                # 3. Encoding diversity (different concepts should have different encodings)
                final_encoding = encodings[-1]
                pairwise_distances = torch.cdist(final_encoding, final_encoding)
                min_distance = pairwise_distances[pairwise_distances > 0].min().item()
                
                # 4. Dimensionality consistency
                correct_dimensions = all(
                    enc.shape[-1] == self.thought_space_dim for enc in encodings
                )
            
            execution_time = time.time() - start_time
            
            # Determine test result
            result = TestResult.PASS if (
                all_finite and reconstruction_error < 1.0 and min_distance > 0.01 and correct_dimensions
            ) else TestResult.FAIL
            
            return TestReport(
                test_name="concept_encoding_decoding",
                result=result,
                metrics=TestMetrics(
                    execution_time=execution_time,
                    numerical_error=reconstruction_error
                ),
                details={
                    'reconstruction_error': reconstruction_error,
                    'all_finite': all_finite,
                    'level_norms': level_norms,
                    'min_distance': min_distance,
                    'correct_dimensions': correct_dimensions,
                    'num_levels': len(encodings)
                }
            )
            
        except Exception as e:
            return TestReport(
                test_name="concept_encoding_decoding",
                result=TestResult.ERROR,
                metrics=TestMetrics(execution_time=time.time() - start_time),
                error_message=str(e)
            )
    
    def test_semantic_similarity_computation(self) -> TestReport:
        """Test semantic similarity computation and properties."""
        start_time = time.time()
        
        try:
            # Generate test concepts with known semantic structure
            concepts = TestDataGenerator.generate_concept_embeddings(
                20, self.concept_dim, self.device
            )
            
            # Compute semantic similarities using cosine similarity
            similarities = F.cosine_similarity(
                concepts.unsqueeze(1), concepts.unsqueeze(0), dim=-1
            )
            
            # Validate mathematical properties
            math_valid = MathematicalValidator.validate_semantic_consistency(
                concepts, similarities
            )
            
            # Test specific properties
            # 1. Self-similarity should be maximal
            diag_vals = torch.diag(similarities)
            self_similarity_correct = torch.allclose(diag_vals, torch.ones_like(diag_vals), atol=1e-4)
            
            # 2. Similarity matrix should be symmetric
            symmetry_error = torch.abs(similarities - similarities.T).max().item()
            
            # 3. Similar concepts should have higher similarity
            # Create pairs of similar concepts by adding small noise
            similar_concepts = concepts + 0.1 * torch.randn_like(concepts)
            
            similar_sims = F.cosine_similarity(concepts, similar_concepts, dim=-1)
            random_sims = F.cosine_similarity(concepts, concepts[torch.randperm(concepts.shape[0])], dim=-1)
            
            similar_higher = (similar_sims.mean() > random_sims.mean()).item()
            
            # 4. Transitivity (approximate)
            # If A similar to B and B similar to C, then A should be somewhat similar to C
            transitivity_score = 0.0
            transitivity_count = 0
            
            for i in range(min(10, concepts.shape[0])):
                for j in range(min(10, concepts.shape[0])):
                    for k in range(min(10, concepts.shape[0])):
                        if i != j and j != k and i != k:
                            sim_ij = similarities[i, j]
                            sim_jk = similarities[j, k]
                            sim_ik = similarities[i, k]
                            
                            if sim_ij > 0.5 and sim_jk > 0.5:
                                # Expect sim_ik to be reasonably high
                                transitivity_score += sim_ik.item()
                                transitivity_count += 1
            
            avg_transitivity = transitivity_score / max(transitivity_count, 1)
            
            execution_time = time.time() - start_time
            
            # Determine test result
            result = TestResult.PASS if (
                math_valid and self_similarity_correct and symmetry_error < 1e-4 and similar_higher
            ) else TestResult.FAIL
            
            return TestReport(
                test_name="semantic_similarity_computation",
                result=result,
                metrics=TestMetrics(
                    execution_time=execution_time,
                    numerical_error=symmetry_error
                ),
                details={
                    'mathematical_validity': math_valid,
                    'self_similarity_correct': self_similarity_correct,
                    'symmetry_error': symmetry_error,
                    'similar_higher_similarity': similar_higher,
                    'avg_transitivity': avg_transitivity,
                    'similarity_range': [similarities.min().item(), similarities.max().item()]
                }
            )
            
        except Exception as e:
            return TestReport(
                test_name="semantic_similarity_computation",
                result=TestResult.ERROR,
                metrics=TestMetrics(execution_time=time.time() - start_time),
                error_message=str(e)
            )
    
    def test_concept_composition_operations(self) -> TestReport:
        """Test concept composition and decomposition operations."""
        start_time = time.time()
        
        try:
            # Generate base concepts
            base_concepts = TestDataGenerator.generate_concept_embeddings(
                5, self.concept_dim, self.device
            )
            
            # Test composition operations
            composition_errors = []
            
            # 1. Linear composition
            weights = torch.softmax(torch.randn(2), dim=0).to(self.device)
            concept_a, concept_b = base_concepts[0], base_concepts[1]
            
            linear_composition = weights[0] * concept_a + weights[1] * concept_b
            linear_composition = F.normalize(linear_composition, dim=0)
            
            # Validate composition properties
            # Composed concept should be finite
            composition_finite = torch.isfinite(linear_composition).all()
            
            # Composed concept should be different from components
            diff_from_a = F.cosine_similarity(linear_composition, concept_a, dim=0).item()
            diff_from_b = F.cosine_similarity(linear_composition, concept_b, dim=0).item()
            
            # Should be between the components in similarity space
            similarity_reasonable = 0.3 < diff_from_a < 0.9 and 0.3 < diff_from_b < 0.9
            
            # 2. Nonlinear composition (element-wise product + normalization)
            nonlinear_composition = concept_a * concept_b
            nonlinear_composition = F.normalize(nonlinear_composition, dim=0)
            
            nonlinear_finite = torch.isfinite(nonlinear_composition).all()
            
            # 3. Hierarchical composition using attention
            attention_weights = F.softmax(torch.randn(3, device=self.device), dim=0)
            hierarchical_concepts = base_concepts[:3]
            
            hierarchical_composition = torch.sum(
                attention_weights.unsqueeze(-1) * hierarchical_concepts, dim=0
            )
            hierarchical_composition = F.normalize(hierarchical_composition, dim=0)
            
            hierarchical_finite = torch.isfinite(hierarchical_composition).all()
            
            # Test decomposition (approximate inverse)
            # For linear composition, try to recover weights
            composed = linear_composition
            
            # Solve least squares problem: composed ≈ w_a * concept_a + w_b * concept_b
            A = torch.stack([concept_a, concept_b], dim=1)
            recovered_weights, _ = torch.linalg.lstsq(A, composed)
            
            # Normalize recovered weights
            recovered_weights = F.softmax(recovered_weights, dim=0)
            
            # Compute reconstruction error
            reconstructed = recovered_weights[0] * concept_a + recovered_weights[1] * concept_b
            reconstruction_error = F.mse_loss(F.normalize(reconstructed, dim=0), 
                                           linear_composition).item()
            
            composition_errors.append(reconstruction_error)
            
            execution_time = time.time() - start_time
            avg_error = np.mean(composition_errors)
            
            # Determine test result
            result = TestResult.PASS if (
                composition_finite and nonlinear_finite and hierarchical_finite and 
                similarity_reasonable and avg_error < 0.1
            ) else TestResult.FAIL
            
            return TestReport(
                test_name="concept_composition_operations",
                result=result,
                metrics=TestMetrics(
                    execution_time=execution_time,
                    numerical_error=avg_error
                ),
                details={
                    'composition_finite': composition_finite.item(),
                    'nonlinear_finite': nonlinear_finite.item(),
                    'hierarchical_finite': hierarchical_finite.item(),
                    'similarity_reasonable': similarity_reasonable,
                    'reconstruction_error': reconstruction_error,
                    'original_weights': weights.cpu().numpy().tolist(),
                    'recovered_weights': recovered_weights.cpu().numpy().tolist()
                }
            )
            
        except Exception as e:
            return TestReport(
                test_name="concept_composition_operations",
                result=TestResult.ERROR,
                metrics=TestMetrics(execution_time=time.time() - start_time),
                error_message=str(e)
            )
    
    def test_latent_space_interpolation(self) -> TestReport:
        """Test interpolation in thought latent space."""
        start_time = time.time()
        
        try:
            # Generate endpoint concepts
            concept_a = TestDataGenerator.generate_concept_embeddings(1, self.concept_dim, self.device)[0]
            concept_b = TestDataGenerator.generate_concept_embeddings(1, self.concept_dim, self.device)[0]
            
            # Test different interpolation methods
            interpolation_steps = 10
            alphas = torch.linspace(0, 1, interpolation_steps, device=self.device)
            
            interpolation_errors = []
            
            # 1. Linear interpolation in concept space
            linear_interpolations = []
            for alpha in alphas:
                interpolated = (1 - alpha) * concept_a + alpha * concept_b
                interpolated = F.normalize(interpolated, dim=0)
                linear_interpolations.append(interpolated)
            
            linear_interpolations = torch.stack(linear_interpolations)
            
            # 2. Spherical linear interpolation (SLERP)
            slerp_interpolations = []
            dot_product = torch.dot(concept_a, concept_b)
            # Clamp to avoid numerical issues
            dot_product = torch.clamp(dot_product, -1.0 + 1e-6, 1.0 - 1e-6)
            omega = torch.acos(torch.abs(dot_product))
            
            for alpha in alphas:
                if omega.abs() < 1e-6:
                    # Vectors are nearly parallel, use linear interpolation
                    slerp_result = (1 - alpha) * concept_a + alpha * concept_b
                else:
                    slerp_result = (torch.sin((1 - alpha) * omega) * concept_a + 
                                  torch.sin(alpha * omega) * concept_b) / torch.sin(omega)
                
                slerp_result = F.normalize(slerp_result, dim=0)
                slerp_interpolations.append(slerp_result)
            
            slerp_interpolations = torch.stack(slerp_interpolations)
            
            # Validate interpolation properties
            # 1. Endpoints should match original concepts
            linear_start_error = F.mse_loss(linear_interpolations[0], concept_a).item()
            linear_end_error = F.mse_loss(linear_interpolations[-1], concept_b).item()
            
            slerp_start_error = F.mse_loss(slerp_interpolations[0], concept_a).item()
            slerp_end_error = F.mse_loss(slerp_interpolations[-1], concept_b).item()
            
            endpoint_errors = [linear_start_error, linear_end_error, 
                             slerp_start_error, slerp_end_error]
            
            # 2. Interpolation should be smooth (consecutive points should be similar)
            linear_smoothness = torch.mean(torch.sum(
                (linear_interpolations[1:] - linear_interpolations[:-1])**2, dim=-1
            )).item()
            
            slerp_smoothness = torch.mean(torch.sum(
                (slerp_interpolations[1:] - slerp_interpolations[:-1])**2, dim=-1
            )).item()
            
            # 3. All interpolated points should be finite
            linear_finite = torch.isfinite(linear_interpolations).all()
            slerp_finite = torch.isfinite(slerp_interpolations).all()
            
            # 4. Interpolation should maintain unit norm (for normalized concepts)
            linear_norms = torch.norm(linear_interpolations, dim=-1)
            slerp_norms = torch.norm(slerp_interpolations, dim=-1)
            
            linear_unit_norm = torch.allclose(linear_norms, torch.ones_like(linear_norms), atol=1e-3)
            slerp_unit_norm = torch.allclose(slerp_norms, torch.ones_like(slerp_norms), atol=1e-3)
            
            interpolation_errors.extend(endpoint_errors)
            
            execution_time = time.time() - start_time
            avg_error = np.mean(interpolation_errors)
            
            # Determine test result
            result = TestResult.PASS if (
                linear_finite and slerp_finite and avg_error < 0.1 and 
                linear_unit_norm and slerp_unit_norm
            ) else TestResult.FAIL
            
            return TestReport(
                test_name="latent_space_interpolation",
                result=result,
                metrics=TestMetrics(
                    execution_time=execution_time,
                    numerical_error=avg_error
                ),
                details={
                    'linear_finite': linear_finite.item(),
                    'slerp_finite': slerp_finite.item(),
                    'linear_smoothness': linear_smoothness,
                    'slerp_smoothness': slerp_smoothness,
                    'linear_unit_norm': linear_unit_norm.item(),
                    'slerp_unit_norm': slerp_unit_norm.item(),
                    'endpoint_errors': endpoint_errors,
                    'interpolation_steps': interpolation_steps
                }
            )
            
        except Exception as e:
            return TestReport(
                test_name="latent_space_interpolation",
                result=TestResult.ERROR,
                metrics=TestMetrics(execution_time=time.time() - start_time),
                error_message=str(e)
            )

# =============================================================================
# Reverse Diffusion Reasoning Tests
# =============================================================================

class ReverseDiffusionReasoningTester:
    """Comprehensive test suite for reverse diffusion reasoning."""
    
    def __init__(self, device: str = TEST_DEVICE):
        self.device = device
        self.concept_dim = TEST_CONCEPT_DIM
        self.diffusion_steps = TEST_DIFFUSION_STEPS
        self.batch_size = TEST_BATCH_SIZE
        self.setup_diffusion_parameters()
    
    def setup_diffusion_parameters(self):
        """Initialize diffusion parameters for reverse reasoning."""
        # Reuse parameters from ConceptualDiffusionTester
        self.betas = torch.linspace(1e-4, 0.02, self.diffusion_steps, device=self.device)
        self.alphas = 1.0 - self.betas
        self.alphas_cumprod = torch.cumprod(self.alphas, dim=0)
        self.sqrt_alphas_cumprod = torch.sqrt(self.alphas_cumprod)
        self.sqrt_one_minus_alphas_cumprod = torch.sqrt(1.0 - self.alphas_cumprod)
    
    def test_goal_directed_diffusion(self) -> TestReport:
        """Test goal-directed diffusion for reasoning toward specific outcomes."""
        start_time = time.time()
        
        try:
            # Generate source and goal concepts
            source_concepts = TestDataGenerator.generate_concept_embeddings(
                self.batch_size, self.concept_dim, self.device
            )
            goal_concepts = TestDataGenerator.generate_concept_embeddings(
                self.batch_size, self.concept_dim, self.device
            )
            
            # Simple goal-directed guidance function
            def goal_guidance_score(x_t, goal, strength=1.0):
                """Compute guidance score toward goal concept."""
                similarity = F.cosine_similarity(x_t, goal.unsqueeze(0), dim=-1)
                return strength * similarity
            
            # Simple noise predictor for testing
            class GoalDirectedNoisePredictor(nn.Module):
                def __init__(self, concept_dim, diffusion_steps):  # Add diffusion_steps parameter
                    super().__init__()
                    self.concept_dim = concept_dim
                    self.diffusion_steps = diffusion_steps  # Store as instance variable
                    self.net = nn.Sequential(
                        nn.Linear(concept_dim + 1, concept_dim * 2),
                        nn.ReLU(),
                        nn.Linear(concept_dim * 2, concept_dim)
                    )
                
                def forward(self, x_t, t, goal=None):
                    # Embed timestep
                    t_emb = t.float().unsqueeze(-1) / self.diffusion_steps
                    
                    # Concatenate input with timestep
                    input_tensor = torch.cat([x_t, t_emb], dim=-1)
                    base_prediction = self.net(input_tensor)
                    
                    if goal is not None:
                        # Apply goal guidance
                        def goal_guidance_score(x_t, goal, strength=1.0):
                            similarity = F.cosine_similarity(x_t, goal.unsqueeze(0), dim=-1)
                            return strength * similarity
                        
                        guidance = goal_guidance_score(x_t, goal, strength=0.1)
                        goal_direction = goal.unsqueeze(0) - x_t
                        guided_prediction = base_prediction - 0.1 * guidance.unsqueeze(-1) * goal_direction
                        return guided_prediction
                    
                    return base_prediction

            # Update initialization around line 1860:
            noise_predictor = GoalDirectedNoisePredictor(self.concept_dim, self.diffusion_steps).to(self.device)
            
            # Test goal-directed sampling
            noise_predictor.eval()
            reasoning_errors = []
            
            with torch.no_grad():
                for i in range(min(3, self.batch_size)):  # Test a few examples
                    source = source_concepts[i]
                    goal = goal_concepts[i]
                    
                    # Start from source concept with some noise
                    x_t = source + 0.3 * torch.randn_like(source)
                    
                    # Track trajectory toward goal
                    trajectory = [x_t.clone()]
                    initial_distance = F.mse_loss(x_t, goal).item()
                    
                    # Goal-directed reverse diffusion
                    for t in reversed(range(0, self.diffusion_steps, 10)):  # Sample every 10 steps
                        t_tensor = torch.full((1,), t, device=self.device)
                        
                        # Predict noise with goal guidance
                        predicted_noise = noise_predictor(x_t.unsqueeze(0), t_tensor, goal)
                        predicted_noise = predicted_noise.squeeze(0)
                        
                        # Apply denoising step
                        if t > 0:
                            alpha_t = self.alphas[t]
                            beta_t = self.betas[t]
                            sqrt_one_minus_alpha_cumprod_t = self.sqrt_one_minus_alphas_cumprod[t]
                            
                            # Compute predicted x_0
                            pred_x0 = (x_t - sqrt_one_minus_alpha_cumprod_t * predicted_noise) / torch.sqrt(alpha_t)
                            
                            # Apply guidance toward goal
                            guidance_strength = 0.1 * (t / self.diffusion_steps)  # Stronger guidance early
                            goal_direction = F.normalize(goal - pred_x0, dim=0)
                            pred_x0 = pred_x0 + guidance_strength * goal_direction
                            
                            # Compute x_{t-1}
                            noise = torch.randn_like(x_t) * 0.1  # Reduced noise for stability
                            x_t = torch.sqrt(alpha_t) * pred_x0 + torch.sqrt(1 - alpha_t) * noise
                        else:
                            # Final step
                            x_t = (x_t - self.sqrt_one_minus_alphas_cumprod[t] * predicted_noise) / torch.sqrt(self.alphas[t])
                        
                        trajectory.append(x_t.clone())
                    
                    # Evaluate goal-directed reasoning
                    final_distance = F.mse_loss(x_t, goal).item()
                    distance_reduction = initial_distance - final_distance
                    
                    # Check if we moved toward the goal
                    moved_toward_goal = distance_reduction > 0
                    
                    # Check trajectory consistency
                    trajectory_finite = all(torch.isfinite(step).all() for step in trajectory)
                    
                    reasoning_errors.append(final_distance)
                    
                    # Test constraint satisfaction
                    # Ensure final concept is reasonable (bounded)
                    final_bounded = torch.abs(x_t).max() < 5.0
            
            execution_time = time.time() - start_time
            avg_error = np.mean(reasoning_errors)
            
            # Determine test result
            result = TestResult.PASS if (
                trajectory_finite and avg_error < 2.0 and final_bounded
            ) else TestResult.FAIL
            
            return TestReport(
                test_name="goal_directed_diffusion",
                result=result,
                metrics=TestMetrics(
                    execution_time=execution_time,
                    numerical_error=avg_error
                ),
                details={
                    'trajectory_finite': trajectory_finite,
                    'final_bounded': final_bounded,
                    'avg_final_distance': avg_error,
                    'distance_reduction': distance_reduction,
                    'moved_toward_goal': moved_toward_goal,
                    'trajectory_length': len(trajectory)
                }
            )
            
        except Exception as e:
            return TestReport(
                test_name="goal_directed_diffusion",
                result=TestResult.ERROR,
                metrics=TestMetrics(execution_time=time.time() - start_time),
                error_message=str(e)
            )
    
    def test_constraint_guided_sampling(self) -> TestReport:
        """Test constraint-guided sampling for reasoning with constraints."""
        start_time = time.time()
        
        try:
            # Define constraint functions
            def constraint_function(x):
                """Example constraint: concepts should have positive mean."""
                return x.mean(dim=-1) > 0
            
            def soft_constraint_loss(x):
                """Soft constraint loss for guidance."""
                target_mean = 0.5
                actual_mean = x.mean(dim=-1)
                return F.mse_loss(actual_mean, torch.full_like(actual_mean, target_mean))
            
            # Generate initial concepts
            initial_concepts = TestDataGenerator.generate_concept_embeddings(
                self.batch_size, self.concept_dim, self.device
            )
            
            # Simple constraint-guided noise predictor
            class ConstraintGuidedPredictor(nn.Module):
                def __init__(self, concept_dim, diffusion_steps):
                    super().__init__()
                    self.concept_dim = concept_dim
                    self.diffusion_steps = diffusion_steps
                    self.base_predictor = nn.Sequential(
                        nn.Linear(concept_dim + 1, concept_dim * 2),
                        nn.ReLU(),
                        nn.Linear(concept_dim * 2, concept_dim)
                    )
                
                def forward(self, x_t, t, constraint_guidance=True):
                    # Basic noise prediction
                    t_emb = t.float().unsqueeze(-1) / self.diffusion_steps
                    if len(x_t.shape) == 1:
                        x_t = x_t.unsqueeze(0)
                        t_emb = t_emb.unsqueeze(0)
                    
                    input_tensor = torch.cat([x_t, t_emb], dim=-1)
                    base_noise = self.base_predictor(input_tensor)
                    
                    if constraint_guidance:
                        try:
                            # Create a copy that requires gradients - fix the gradient issue
                            x_t_copy = x_t.detach().clone().requires_grad_(True)
                            
                            # Ensure proper shape for constraint loss
                            if len(x_t_copy.shape) == 1:
                                x_t_copy = x_t_copy.unsqueeze(0)
                            
                            # Compute constraint loss
                            target_mean = 0.5
                            actual_mean = x_t_copy.mean(dim=-1)
                            target_tensor = torch.full_like(actual_mean, target_mean, device=x_t.device)
                            constraint_loss = F.mse_loss(actual_mean, target_tensor)
                            
                            # Only compute gradient if loss requires grad
                            if constraint_loss.requires_grad:
                                constraint_gradient = torch.autograd.grad(
                                    constraint_loss, x_t_copy, 
                                    create_graph=False, retain_graph=False,
                                    allow_unused=True
                                )[0]
                                
                                if constraint_gradient is not None:
                                    # Match dimensions with base_noise
                                    if constraint_gradient.shape != base_noise.shape:
                                        if len(constraint_gradient.shape) > len(base_noise.shape):
                                            constraint_gradient = constraint_gradient.squeeze(0)
                                        elif len(constraint_gradient.shape) < len(base_noise.shape):
                                            constraint_gradient = constraint_gradient.unsqueeze(0)
                                    
                                    # Apply guidance
                                    guidance_strength = 0.1
                                    guided_noise = base_noise - guidance_strength * constraint_gradient
                                    return guided_noise
                        
                        except Exception as grad_error:
                            logger.warning(f"Gradient computation error: {grad_error}")
                            pass
                    
                    return base_noise
            
            # Initialize predictor
            predictor = ConstraintGuidedPredictor(self.concept_dim, self.diffusion_steps).to(self.device)
            
            # Test constraint-guided sampling
            predictor.eval()
            constraint_violations = []
            sampling_errors = []
            
            with torch.no_grad():
                for i in range(min(3, self.batch_size)):
                    # Start from initial concept
                    x_t = initial_concepts[i]
                    
                    # Apply constraint-guided reverse diffusion
                    for t in reversed(range(0, self.diffusion_steps, 20)):
                        t_tensor = torch.full((1,), t, device=self.device)
                        
                        try:
                            # Predict noise with constraint guidance
                            predicted_noise = predictor(x_t, t_tensor[0], constraint_guidance=True)
                            if len(predicted_noise.shape) > 1:
                                predicted_noise = predicted_noise.squeeze(0)
                            
                            # Apply denoising step
                            if t > 0:
                                alpha_t = self.alphas[t]
                                sqrt_one_minus_alpha_cumprod_t = self.sqrt_one_minus_alphas_cumprod[t]
                                
                                # Denoise
                                denoised = (x_t - sqrt_one_minus_alpha_cumprod_t * predicted_noise) / torch.sqrt(alpha_t)
                                
                                # Add small amount of noise for next step
                                noise = torch.randn_like(x_t) * 0.05
                                x_t = torch.sqrt(alpha_t) * denoised + torch.sqrt(1 - alpha_t) * noise
                            else:
                                x_t = (x_t - self.sqrt_one_minus_alphas_cumprod[t] * predicted_noise) / torch.sqrt(self.alphas[t])
                            
                        except Exception as step_error:
                            # Handle numerical instability gracefully
                            logger.warning(f"Step error at t={t}: {step_error}")
                            break
                    
                    # Evaluate constraint satisfaction
                    constraint_satisfied = constraint_function(x_t)
                    constraint_violations.append(0.0 if constraint_satisfied else 1.0)
                    
                    # Check for numerical stability
                    is_finite = torch.isfinite(x_t).all()
                    is_bounded = torch.abs(x_t).max() < 10.0
                    
                    sampling_errors.append(0.0 if (is_finite and is_bounded) else 1.0)
            
            execution_time = time.time() - start_time
            avg_violations = np.mean(constraint_violations)
            avg_sampling_errors = np.mean(sampling_errors)
            
            # Determine test result
            result = TestResult.PASS if (
                avg_violations < 0.5 and avg_sampling_errors < 0.3
            ) else TestResult.FAIL
            
            return TestReport(
                test_name="constraint_guided_sampling",
                result=result,
                metrics=TestMetrics(
                    execution_time=execution_time,
                    numerical_error=avg_sampling_errors
                ),
                details={
                    'avg_constraint_violations': avg_violations,
                    'avg_sampling_errors': avg_sampling_errors,
                    'constraint_satisfaction_rate': 1.0 - avg_violations,
                    'numerical_stability_rate': 1.0 - avg_sampling_errors
                }
            )
            
        except Exception as e:
            return TestReport(
                test_name="constraint_guided_sampling",
                result=TestResult.ERROR,
                metrics=TestMetrics(execution_time=time.time() - start_time),
                error_message=str(e)
            )
    
    def test_diffusion_bridge_reasoning(self) -> TestReport:
        """Test diffusion bridge for reasoning between concept pairs."""
        start_time = time.time()
        
        try:
            # Generate concept pairs
            source_concepts = TestDataGenerator.generate_concept_embeddings(
                5, self.concept_dim, self.device
            )
            target_concepts = TestDataGenerator.generate_concept_embeddings(
                5, self.concept_dim, self.device
            )
            
            # Implement simplified diffusion bridge
            def diffusion_bridge_step(x_s, x_t, s, t, total_steps):
                """Single step of diffusion bridge between source and target."""
                # Linear interpolation schedule
                alpha = s / total_steps
                beta = t / total_steps
                
                if alpha < beta:
                    # Forward bridge: move from source toward target
                    interpolation_weight = (beta - alpha) / (1 - alpha)
                    bridge_state = (1 - interpolation_weight) * x_s + interpolation_weight * x_t
                else:
                    # Reverse bridge: move from target toward source  
                    interpolation_weight = (alpha - beta) / alpha
                    bridge_state = (1 - interpolation_weight) * x_t + interpolation_weight * x_s
                
                # Add small amount of noise for stochasticity
                noise_scale = 0.01 * np.sin(np.pi * alpha)  # Maximum noise at middle
                bridge_state += noise_scale * torch.randn_like(bridge_state)
                
                return F.normalize(bridge_state, dim=0)
            
            bridge_errors = []
            path_qualities = []
            
            # Test diffusion bridge for each concept pair
            for i in range(len(source_concepts)):
                source = source_concepts[i]
                target = target_concepts[i]
                
                # Generate bridge path
                bridge_steps = 20
                bridge_path = []
                
                current_state = source.clone()
                bridge_path.append(current_state.clone())
                
                for step in range(1, bridge_steps):
                    s = step - 1
                    t = step
                    
                    # Apply bridge step
                    current_state = diffusion_bridge_step(
                        source, target, s, t, bridge_steps
                    )
                    bridge_path.append(current_state.clone())
                
                # Add final target
                bridge_path.append(target.clone())
                bridge_path = torch.stack(bridge_path)
                
                # Evaluate bridge quality
                # 1. Path should start at source and end at target
                start_error = F.mse_loss(bridge_path[0], source).item()
                end_error = F.mse_loss(bridge_path[-1], target).item()
                
                # 2. Path should be smooth (consecutive steps similar)
                path_differences = torch.sum((bridge_path[1:] - bridge_path[:-1])**2, dim=-1)
                path_smoothness = torch.mean(path_differences).item()
                
                # 3. Path should make progress toward target
                distances_to_target = torch.sum((bridge_path - target.unsqueeze(0))**2, dim=-1)
                progress_made = distances_to_target[0] > distances_to_target[-1]
                
                # 4. All points should be finite and bounded
                path_finite = torch.isfinite(bridge_path).all()
                path_bounded = torch.abs(bridge_path).max() < 5.0
                
                bridge_errors.append(start_error + end_error)
                path_qualities.append(1.0 if (progress_made and path_finite and path_bounded) else 0.0)
            
            execution_time = time.time() - start_time
            avg_bridge_error = np.mean(bridge_errors)
            avg_path_quality = np.mean(path_qualities)
            
            # Determine test result
            result = TestResult.PASS if (
                avg_bridge_error < 0.1 and avg_path_quality > 0.8
            ) else TestResult.FAIL
            
            return TestReport(
                test_name="diffusion_bridge_reasoning",
                result=result,
                metrics=TestMetrics(
                    execution_time=execution_time,
                    numerical_error=avg_bridge_error
                ),
                details={
                    'avg_bridge_error': avg_bridge_error,
                    'avg_path_quality': avg_path_quality,
                    'path_smoothness': path_smoothness,
                    'start_error': start_error,
                    'end_error': end_error,
                    'progress_made': progress_made.item() if hasattr(progress_made, 'item') else progress_made
                }
            )
            
        except Exception as e:
            return TestReport(
                test_name="diffusion_bridge_reasoning",
                result=TestResult.ERROR,
                metrics=TestMetrics(execution_time=time.time() - start_time),
                error_message=str(e)
            )

# =============================================================================
# Bayesian Uncertainty Quantification Tests
# =============================================================================

class BayesianUncertaintyTester:
    """Comprehensive test suite for Bayesian uncertainty quantification."""
    
    def __init__(self, device: str = TEST_DEVICE):
        self.device = device
        self.concept_dim = TEST_CONCEPT_DIM
        self.batch_size = TEST_BATCH_SIZE
        self.num_mc_samples = 32
    
    def test_epistemic_uncertainty_estimation(self) -> TestReport:
        """Test epistemic uncertainty estimation using Monte Carlo dropout."""
        start_time = time.time()
        
        try:
            # Create model with MC dropout for epistemic uncertainty
            class MCDropoutModel(nn.Module):
                def __init__(self, input_dim, output_dim, dropout_rate=0.2):
                    super().__init__()
                    self.network = nn.Sequential(
                        nn.Linear(input_dim, input_dim * 2),
                        nn.ReLU(),
                        nn.Dropout(dropout_rate),
                        nn.Linear(input_dim * 2, input_dim),
                        nn.ReLU(),
                        nn.Dropout(dropout_rate),
                        nn.Linear(input_dim, output_dim)
                    )
                    self.dropout_rate = dropout_rate
                
                def forward(self, x):
                    return self.network(x)
                
                def enable_mc_dropout(self):
                    """Enable Monte Carlo dropout for uncertainty estimation."""
                    for module in self.modules():
                        if isinstance(module, nn.Dropout):
                            module.train()
                
                def estimate_epistemic_uncertainty(self, x, num_samples=32):
                    """Estimate epistemic uncertainty using MC dropout."""
                    self.enable_mc_dropout()
                    
                    samples = []
                    with torch.no_grad():
                        for _ in range(num_samples):
                            output = self.forward(x)
                            samples.append(output)
                    
                    samples = torch.stack(samples)  # [num_samples, batch_size, output_dim]
                    
                    # Compute mean and variance
                    mean_prediction = torch.mean(samples, dim=0)
                    epistemic_variance = torch.var(samples, dim=0, unbiased=True)
                    epistemic_uncertainty = torch.mean(epistemic_variance, dim=-1)
                    
                    return mean_prediction, epistemic_uncertainty, samples
            
            # Initialize model
            model = MCDropoutModel(self.concept_dim, self.concept_dim).to(self.device)
            
            # Generate test data
            test_input = TestDataGenerator.generate_concept_embeddings(
                self.batch_size, self.concept_dim, self.device
            )
            
            # Estimate epistemic uncertainty
            mean_pred, epistemic_unc, mc_samples = model.estimate_epistemic_uncertainty(
                test_input, self.num_mc_samples
            )
            
            # Validate uncertainty properties
            # 1. Uncertainty should be non-negative
            uncertainty_positive = torch.all(epistemic_unc >= 0)
            
            # 2. Predictions should be finite
            predictions_finite = torch.isfinite(mean_pred).all()
            uncertainty_finite = torch.isfinite(epistemic_unc).all()
            
            # 3. Uncertainty should vary across different inputs
            uncertainty_variance = torch.var(epistemic_unc)
            has_uncertainty_variance = uncertainty_variance > 1e-6
            
            # 4. MC samples should be consistent
            sample_means = torch.mean(mc_samples, dim=0)
            mean_consistency = torch.allclose(sample_means, mean_pred, atol=1e-3)
            
            # 5. Test uncertainty calibration (simplified)
            # Higher variance in MC samples should correspond to higher uncertainty
            sample_variances = torch.var(mc_samples, dim=0).mean(dim=-1)
            try:
                uncertainty_correlation = torch.corrcoef(torch.stack([epistemic_unc, sample_variances]))[0, 1]
                good_correlation = torch.abs(uncertainty_correlation) > 0.5 if torch.isfinite(uncertainty_correlation) else False
            except (RuntimeError, IndexError):
                uncertainty_correlation = torch.tensor(0.0)
                good_correlation = False
            
            execution_time = time.time() - start_time
            avg_uncertainty = epistemic_unc.mean().item()
            
            # Determine test result
            result = TestResult.PASS if (
                uncertainty_positive and predictions_finite and uncertainty_finite and 
                has_uncertainty_variance and mean_consistency
            ) else TestResult.FAIL
            
            return TestReport(
                test_name="epistemic_uncertainty_estimation",
                result=result,
                metrics=TestMetrics(
                    execution_time=execution_time,
                    numerical_error=0.0
                ),
                details={
                    'uncertainty_positive': uncertainty_positive.item(),
                    'predictions_finite': predictions_finite.item(),
                    'uncertainty_finite': uncertainty_finite.item(),
                    'has_uncertainty_variance': has_uncertainty_variance.item(),
                    'mean_consistency': mean_consistency.item(),
                    'good_correlation': good_correlation if isinstance(good_correlation, bool) else good_correlation.item(),
                    'avg_uncertainty': avg_uncertainty,
                    'uncertainty_range': [epistemic_unc.min().item(), epistemic_unc.max().item()]
                }
            )
            
        except Exception as e:
            return TestReport(
                test_name="epistemic_uncertainty_estimation",
                result=TestResult.ERROR,
                metrics=TestMetrics(execution_time=time.time() - start_time),
                error_message=str(e)
            )
    
    def test_aleatoric_uncertainty_estimation(self) -> TestReport:
        """Test aleatoric uncertainty estimation for inherent data noise."""
        start_time = time.time()
        
        try:
            # Create model that predicts both mean and variance (heteroscedastic)
            class AleatoricModel(nn.Module):
                def __init__(self, input_dim, output_dim):
                    super().__init__()
                    hidden_dim = input_dim * 2
                    
                    # Shared feature extractor
                    self.feature_extractor = nn.Sequential(
                        nn.Linear(input_dim, hidden_dim),
                        nn.ReLU(),
                        nn.Linear(hidden_dim, hidden_dim),
                        nn.ReLU()
                    )
                    
                    # Mean prediction head
                    self.mean_head = nn.Linear(hidden_dim, output_dim)
                    
                    # Log variance prediction head (predict log variance for numerical stability)
                    self.log_var_head = nn.Linear(hidden_dim, output_dim)
                
                def forward(self, x):
                    features = self.feature_extractor(x)
                    mean = self.mean_head(features)
                    log_var = self.log_var_head(features)
                    
                    # Clamp log variance to prevent numerical issues
                    log_var = torch.clamp(log_var, min=-10, max=2)
                    var = torch.exp(log_var)
                    
                    return mean, var
                
                def estimate_aleatoric_uncertainty(self, x):
                    """Estimate aleatoric uncertainty."""
                    mean, var = self.forward(x)
                    aleatoric_uncertainty = torch.mean(var, dim=-1)
                    return mean, aleatoric_uncertainty, var
            
            # Initialize model
            model = AleatoricModel(self.concept_dim, self.concept_dim).to(self.device)
            
            # Generate test data with known heteroscedastic noise
            X, y = TestDataGenerator.generate_test_uncertainty_data(
                self.batch_size, self.concept_dim, noise_level=0.2
            )
            X, y = X.to(self.device), y.to(self.device)
            
            # Quick training to learn the noise pattern
            optimizer = torch.optim.Adam(model.parameters(), lr=1e-3)
            model.train()
            
            for epoch in range(100):
                mean_pred, var_pred = model(X)
                
                # Negative log likelihood loss (includes both accuracy and uncertainty)
                nll_loss = 0.5 * (torch.log(var_pred) + (y - mean_pred)**2 / var_pred).mean()
                
                optimizer.zero_grad()
                nll_loss.backward()
                optimizer.step()
            
            # Test aleatoric uncertainty estimation
            model.eval()
            with torch.no_grad():
                mean_pred, aleatoric_unc, var_pred = model.estimate_aleatoric_uncertainty(X)
                
                # Validate aleatoric uncertainty properties
                # 1. Uncertainty should be non-negative
                uncertainty_positive = torch.all(aleatoric_unc >= 0)
                
                # 2. Predictions should be finite
                predictions_finite = torch.isfinite(mean_pred).all()
                uncertainty_finite = torch.isfinite(aleatoric_unc).all()
                variance_finite = torch.isfinite(var_pred).all()
                
                # 3. Uncertainty should correlate with true noise level
                # For test data, noise increases with |x|
                input_magnitudes = torch.norm(X, dim=-1)
                try:
                    uncertainty_correlation = torch.corrcoef(
                        torch.stack([aleatoric_unc, input_magnitudes])
                    )[0, 1] if len(aleatoric_unc) > 1 else torch.tensor(1.0)
                    reasonable_correlation = torch.abs(uncertainty_correlation) > 0.2 if torch.isfinite(uncertainty_correlation) else False
                except (RuntimeError, IndexError):
                    uncertainty_correlation = torch.tensor(0.0)
                    reasonable_correlation = False
                
                # 4. Variance should be reasonable scale
                var_scale_reasonable = torch.all(var_pred < 10.0) and torch.all(var_pred > 1e-6)
                
                # 5. Test heteroscedastic property
                var_differences = torch.var(var_pred.mean(dim=-1))
                is_heteroscedastic = var_differences > 1e-6
            
            execution_time = time.time() - start_time
            avg_uncertainty = aleatoric_unc.mean().item()
            
            # Determine test result
            result = TestResult.PASS if (
                uncertainty_positive and predictions_finite and uncertainty_finite and 
                variance_finite and var_scale_reasonable
            ) else TestResult.FAIL
            
            return TestReport(
                test_name="aleatoric_uncertainty_estimation",
                result=result,
                metrics=TestMetrics(
                    execution_time=execution_time,
                    numerical_error=0.0
                ),
                details={
                    'uncertainty_positive': uncertainty_positive.item(),
                    'predictions_finite': predictions_finite.item(),
                    'uncertainty_finite': uncertainty_finite.item(),
                    'variance_finite': variance_finite.item(),
                    'var_scale_reasonable': var_scale_reasonable.item(),
                    'reasonable_correlation': reasonable_correlation if isinstance(reasonable_correlation, bool) else reasonable_correlation.item(),
                    'is_heteroscedastic': is_heteroscedastic.item(),
                    'avg_uncertainty': avg_uncertainty,
                    'correlation_value': uncertainty_correlation.item() if torch.isfinite(uncertainty_correlation) else 0.0
                }
            )
            
        except Exception as e:
            return TestReport(
                test_name="aleatoric_uncertainty_estimation",
                result=TestResult.ERROR,
                metrics=TestMetrics(execution_time=time.time() - start_time),
                error_message=str(e)
            )
    
    def test_uncertainty_decomposition(self) -> TestReport:
        """Test decomposition of total uncertainty into epistemic and aleatoric components."""
        start_time = time.time()
        
        try:
            # Combined model for both epistemic and aleatoric uncertainty
            class CombinedUncertaintyModel(nn.Module):
                def __init__(self, input_dim, output_dim, dropout_rate=0.2):
                    super().__init__()
                    hidden_dim = input_dim * 2
                    
                    self.feature_extractor = nn.Sequential(
                        nn.Linear(input_dim, hidden_dim),
                        nn.ReLU(),
                        nn.Dropout(dropout_rate),
                        nn.Linear(hidden_dim, hidden_dim),
                        nn.ReLU(),
                        nn.Dropout(dropout_rate)
                    )
                    
                    self.mean_head = nn.Linear(hidden_dim, output_dim)
                    self.log_var_head = nn.Linear(hidden_dim, output_dim)
                
                def forward(self, x):
                    features = self.feature_extractor(x)
                    mean = self.mean_head(features)
                    log_var = torch.clamp(self.log_var_head(features), min=-10, max=2)
                    var = torch.exp(log_var)
                    return mean, var
                
                def decompose_uncertainty(self, x, num_mc_samples=32):
                    """Decompose uncertainty into epistemic and aleatoric components."""
                    # Enable dropout for epistemic uncertainty
                    for module in self.modules():
                        if isinstance(module, nn.Dropout):
                            module.train()
                    
                    # Collect MC samples
                    mean_samples = []
                    var_samples = []
                    
                    with torch.no_grad():
                        for _ in range(num_mc_samples):
                            mean, var = self.forward(x)
                            mean_samples.append(mean)
                            var_samples.append(var)
                    
                    mean_samples = torch.stack(mean_samples)  # [num_samples, batch_size, output_dim]
                    var_samples = torch.stack(var_samples)
                    
                    # Epistemic uncertainty: variance across MC samples
                    epistemic_uncertainty = torch.var(mean_samples, dim=0, unbiased=True)
                    epistemic_uncertainty = torch.mean(epistemic_uncertainty, dim=-1)
                    
                    # Aleatoric uncertainty: average predicted variance
                    aleatoric_uncertainty = torch.mean(var_samples, dim=0)
                    aleatoric_uncertainty = torch.mean(aleatoric_uncertainty, dim=-1)
                    
                    # Total uncertainty (approximation)
                    total_uncertainty = epistemic_uncertainty + aleatoric_uncertainty
                    
                    # Predictive mean
                    predictive_mean = torch.mean(mean_samples, dim=0)
                    
                    return epistemic_uncertainty, aleatoric_uncertainty, total_uncertainty, predictive_mean
            
            # Initialize model
            model = CombinedUncertaintyModel(self.concept_dim, self.concept_dim).to(self.device)
            
            # Generate test data
            X, y = TestDataGenerator.generate_test_uncertainty_data(
                self.batch_size, self.concept_dim, noise_level=0.1
            )
            X, y = X.to(self.device), y.to(self.device)
            
            # Quick training
            optimizer = torch.optim.Adam(model.parameters(), lr=1e-3)
            model.train()
            
            for epoch in range(50):
                mean_pred, var_pred = model(X)
                nll_loss = 0.5 * (torch.log(var_pred) + (y - mean_pred)**2 / var_pred).mean()
                
                optimizer.zero_grad()
                nll_loss.backward()
                optimizer.step()
            
            # Test uncertainty decomposition
            epistemic, aleatoric, total, pred_mean = model.decompose_uncertainty(X, self.num_mc_samples)
            
            # Validate decomposition properties
            math_valid = MathematicalValidator.validate_uncertainty_decomposition(
                epistemic, aleatoric, total
            )
            
            # Additional validation
            # 1. All uncertainties should be non-negative
            all_positive = (torch.all(epistemic >= 0) and 
                          torch.all(aleatoric >= 0) and 
                          torch.all(total >= 0))
            
            # 2. All values should be finite
            all_finite = (torch.isfinite(epistemic).all() and 
                         torch.isfinite(aleatoric).all() and 
                         torch.isfinite(total).all())
            
            # 3. Total uncertainty should be at least as large as components
            total_bounds_correct = (torch.all(total >= epistemic - 1e-3) and 
                                  torch.all(total >= aleatoric - 1e-3))
            
            # 4. Check if uncertainties vary across samples
            epistemic_varies = torch.var(epistemic) > 1e-6
            aleatoric_varies = torch.var(aleatoric) > 1e-6
            
            # 5. Test relative magnitudes (both should contribute)
            epistemic_contribution = epistemic.mean() / total.mean()
            aleatoric_contribution = aleatoric.mean() / total.mean()
            balanced_contributions = (0.1 < epistemic_contribution < 0.9 and 
                                    0.1 < aleatoric_contribution < 0.9)
            
            execution_time = time.time() - start_time
            
            # Determine test result
            result = TestResult.PASS if (
                math_valid and all_positive and all_finite and total_bounds_correct
            ) else TestResult.FAIL
            
            return TestReport(
                test_name="uncertainty_decomposition",
                result=result,
                metrics=TestMetrics(
                    execution_time=execution_time,
                    numerical_error=0.0
                ),
                details={
                    'mathematical_validity': math_valid,
                    'all_positive': all_positive,
                    'all_finite': all_finite,
                    'total_bounds_correct': total_bounds_correct,
                    'epistemic_varies': epistemic_varies.item(),
                    'aleatoric_varies': aleatoric_varies.item(),
                    'balanced_contributions': balanced_contributions,
                    'epistemic_contribution': epistemic_contribution.item(),
                    'aleatoric_contribution': aleatoric_contribution.item(),
                    'avg_epistemic': epistemic.mean().item(),
                    'avg_aleatoric': aleatoric.mean().item(),
                    'avg_total': total.mean().item()
                }
            )
            
        except Exception as e:
            return TestReport(
                test_name="uncertainty_decomposition",
                result=TestResult.ERROR,
                metrics=TestMetrics(execution_time=time.time() - start_time),
                error_message=str(e)
            )
    
    def test_uncertainty_calibration(self) -> TestReport:
        """Test uncertainty calibration for reliable confidence estimates."""
        start_time = time.time()
        
        try:
            # Generate synthetic classification data for calibration testing
            try:
                from sklearn.datasets import make_classification
                from sklearn.model_selection import train_test_split
                
                # Create classification problem
                X_np, y_np = make_classification(
                    n_samples=200, n_features=self.concept_dim, n_classes=2, 
                    n_redundant=0, n_informative=min(self.concept_dim//2, self.concept_dim),
                    random_state=42
                )
            except ImportError:
                # Fallback: create simple synthetic classification data
                logger.warning("sklearn not available, using simple synthetic data")
                X_np = np.random.randn(200, self.concept_dim)
                y_np = (X_np.sum(axis=1) > 0).astype(int)
            
            X = torch.FloatTensor(X_np).to(self.device)
            y = torch.LongTensor(y_np).to(self.device)
            
            # Split data
            train_size = int(0.7 * len(X))
            X_train, X_cal = X[:train_size], X[train_size:]
            y_train, y_cal = y[:train_size], y[train_size:]
            
            # Simple classification model with uncertainty
            class UncertaintyClassifier(nn.Module):
                def __init__(self, input_dim, num_classes=2, dropout_rate=0.3):
                    super().__init__()
                    self.network = nn.Sequential(
                        nn.Linear(input_dim, input_dim),
                        nn.ReLU(),
                        nn.Dropout(dropout_rate),
                        nn.Linear(input_dim, input_dim//2),
                        nn.ReLU(),
                        nn.Dropout(dropout_rate),
                        nn.Linear(input_dim//2, num_classes)
                    )
                
                def forward(self, x):
                    logits = self.network(x)
                    return logits
                
                def predict_with_uncertainty(self, x, num_mc_samples=32):
                    """Predict with uncertainty using MC dropout."""
                    # Enable dropout
                    for module in self.modules():
                        if isinstance(module, nn.Dropout):
                            module.train()
                    
                    predictions = []
                    with torch.no_grad():
                        for _ in range(num_mc_samples):
                            logits = self.forward(x)
                            probs = F.softmax(logits, dim=-1)
                            predictions.append(probs)
                    
                    predictions = torch.stack(predictions)  # [num_samples, batch_size, num_classes]
                    
                    # Compute mean and uncertainty
                    mean_probs = torch.mean(predictions, dim=0)
                    
                    # Predictive entropy as uncertainty measure
                    predictive_entropy = -torch.sum(mean_probs * torch.log(mean_probs + 1e-8), dim=-1)
                    
                    # Variance across samples
                    prediction_variance = torch.var(predictions, dim=0, unbiased=True)
                    prediction_uncertainty = torch.mean(prediction_variance, dim=-1)
                    
                    return mean_probs, predictive_entropy, prediction_uncertainty
            
            # Initialize and train model
            model = UncertaintyClassifier(self.concept_dim).to(self.device)
            optimizer = torch.optim.Adam(model.parameters(), lr=1e-3)
            
            # Training loop
            model.train()
            for epoch in range(100):
                logits = model(X_train)
                loss = F.cross_entropy(logits, y_train)
                
                optimizer.zero_grad()
                loss.backward()
                optimizer.step()
            
            # Test calibration on calibration set
            probs, entropy_unc, variance_unc = model.predict_with_uncertainty(X_cal, 32)
            predicted_probs = torch.max(probs, dim=-1)[0]  # Confidence scores
            predicted_classes = torch.argmax(probs, dim=-1)
            accuracy = (predicted_classes == y_cal).float()
            
            # Compute calibration metrics
            # 1. Expected Calibration Error (ECE)
            def compute_ece(confidences, accuracies, num_bins=10):
                """Compute Expected Calibration Error."""
                bin_boundaries = torch.linspace(0, 1, num_bins + 1)
                bin_lowers = bin_boundaries[:-1]
                bin_uppers = bin_boundaries[1:]
                
                ece = 0.0
                for bin_lower, bin_upper in zip(bin_lowers, bin_uppers):
                    in_bin = (confidences > bin_lower) & (confidences <= bin_upper)
                    prop_in_bin = in_bin.float().mean()
                    
                    if prop_in_bin > 0:
                        accuracy_in_bin = accuracies[in_bin].mean()
                        avg_confidence_in_bin = confidences[in_bin].mean()
                        ece += torch.abs(avg_confidence_in_bin - accuracy_in_bin) * prop_in_bin
                
                return ece.item()
            
            # 2. Reliability diagrams data
            ece = compute_ece(predicted_probs, accuracy)
            
            # 3. Test correlation between uncertainty and errors
            errors = 1.0 - accuracy  # Error indicator
            
            # Correlation between entropy uncertainty and errors
            if len(entropy_unc) > 1 and torch.var(entropy_unc) > 1e-6:
                try:
                    entropy_error_corr = torch.corrcoef(torch.stack([entropy_unc, errors]))[0, 1]
                    entropy_error_corr = entropy_error_corr if torch.isfinite(entropy_error_corr) else torch.tensor(0.0)
                except (RuntimeError, IndexError):
                    entropy_error_corr = torch.tensor(0.0)
            else:
                entropy_error_corr = torch.tensor(0.0)
            
            # Correlation between variance uncertainty and errors
            if len(variance_unc) > 1 and torch.var(variance_unc) > 1e-6:
                try:
                    variance_error_corr = torch.corrcoef(torch.stack([variance_unc, errors]))[0, 1]
                    variance_error_corr = variance_error_corr if torch.isfinite(variance_error_corr) else torch.tensor(0.0)
                except (RuntimeError, IndexError):
                    variance_error_corr = torch.tensor(0.0)
            else:
                variance_error_corr = torch.tensor(0.0)
            
            # 4. Test uncertainty-accuracy trade-off
            # Higher uncertainty should correspond to lower accuracy
            good_entropy_correlation = entropy_error_corr > 0.1
            good_variance_correlation = variance_error_corr > 0.1
            
            # 5. Validate calibration quality
            well_calibrated = ece < 0.2  # ECE should be low for well-calibrated models
            
            execution_time = time.time() - start_time
            
            # Determine test result
            result = TestResult.PASS if (
                torch.isfinite(torch.tensor(ece)) and well_calibrated and 
                (good_entropy_correlation or good_variance_correlation)
            ) else TestResult.FAIL
            
            return TestReport(
                test_name="uncertainty_calibration",
                result=result,
                metrics=TestMetrics(
                    execution_time=execution_time,
                    numerical_error=ece
                ),
                details={
                    'expected_calibration_error': ece,
                    'well_calibrated': well_calibrated,
                    'entropy_error_correlation': entropy_error_corr.item(),
                    'variance_error_correlation': variance_error_corr.item(),
                    'good_entropy_correlation': good_entropy_correlation.item() if hasattr(good_entropy_correlation, 'item') else good_entropy_correlation,
                    'good_variance_correlation': good_variance_correlation.item() if hasattr(good_variance_correlation, 'item') else good_variance_correlation,
                    'avg_confidence': predicted_probs.mean().item(),
                    'avg_accuracy': accuracy.mean().item()
                }
            )
            
        except Exception as e:
            return TestReport(
                test_name="uncertainty_calibration",
                result=TestResult.ERROR,
                metrics=TestMetrics(execution_time=time.time() - start_time),
                error_message=str(e)
            )

# =============================================================================
# Probabilistic Inference Tests
# =============================================================================

class ProbabilisticInferenceTester:
    """Comprehensive test suite for probabilistic inference engine."""
    
    def __init__(self, device: str = TEST_DEVICE):
        self.device = device
        self.concept_dim = TEST_CONCEPT_DIM
        self.batch_size = TEST_BATCH_SIZE
        self.num_inference_steps = 50
    
    def test_bayesian_inference_convergence(self) -> TestReport:
        """Test Bayesian inference convergence properties."""
        start_time = time.time()
        
        try:
            # Simple Bayesian inference problem: estimate concept posterior
            class BayesianConceptModel(nn.Module):
                def __init__(self, concept_dim, latent_dim=32):
                    super().__init__()
                    self.concept_dim = concept_dim
                    self.latent_dim = latent_dim
                    
                    # Prior parameters (learnable)
                    self.prior_mean = nn.Parameter(torch.zeros(latent_dim))
                    self.prior_log_var = nn.Parameter(torch.zeros(latent_dim))
                    
                    # Likelihood network
                    self.likelihood_net = nn.Sequential(
                        nn.Linear(latent_dim, concept_dim),
                        nn.ReLU(),
                        nn.Linear(concept_dim, concept_dim)
                    )
                
                def sample_prior(self, num_samples):
                    """Sample from prior distribution."""
                    prior_std = torch.exp(0.5 * self.prior_log_var)
                    eps = torch.randn(num_samples, self.latent_dim, device=self.device)
                    return self.prior_mean + prior_std * eps
                
                def likelihood(self, z, x):
                    """Compute likelihood p(x|z)."""
                    reconstructed = self.likelihood_net(z)
                    likelihood_score = -F.mse_loss(reconstructed, x, reduction='none').sum(dim=-1)
                    return likelihood_score
                
                def posterior_sampling(self, x, num_samples=100, num_steps=50):
                    """Sample from posterior using MCMC-like procedure."""
                    # Initialize samples from prior
                    samples = self.sample_prior(num_samples)
                    
                    # Simple Metropolis-Hastings sampler
                    acceptance_count = 0
                    sample_history = []
                    
                    for step in range(num_steps):
                        # Propose new samples
                        proposal_noise = 0.1 * torch.randn_like(samples)
                        proposed_samples = samples + proposal_noise
                        
                        # Compute acceptance probabilities
                        current_likelihood = self.likelihood(samples, x.unsqueeze(0).expand(num_samples, -1))
                        proposed_likelihood = self.likelihood(proposed_samples, x.unsqueeze(0).expand(num_samples, -1))
                        
                        # Prior ratios
                        current_prior = -0.5 * torch.sum((samples - self.prior_mean)**2 / torch.exp(self.prior_log_var), dim=-1)
                        proposed_prior = -0.5 * torch.sum((proposed_samples - self.prior_mean)**2 / torch.exp(self.prior_log_var), dim=-1)
                        
                        # Acceptance probability
                        log_alpha = (proposed_likelihood + proposed_prior) - (current_likelihood + current_prior)
                        alpha = torch.exp(torch.clamp(log_alpha, max=0))
                        
                        # Accept/reject
                        uniform_samples = torch.rand(num_samples, device=self.device)
                        accept_mask = uniform_samples < alpha
                        
                        samples[accept_mask] = proposed_samples[accept_mask]
                        acceptance_count += accept_mask.sum().item()
                        
                        # Store samples periodically
                        if step % 10 == 0:
                            sample_history.append(samples.clone())
                    
                    acceptance_rate = acceptance_count / (num_samples * num_steps)
                    return samples, sample_history, acceptance_rate
            
            # Initialize model
            model = BayesianConceptModel(self.concept_dim).to(self.device)
            
            # Generate test observation
            test_concept = TestDataGenerator.generate_concept_embeddings(1, self.concept_dim, self.device)[0]
            
            # Run Bayesian inference
            posterior_samples, sample_history, acceptance_rate = model.posterior_sampling(
                test_concept, num_samples=50, num_steps=self.num_inference_steps
            )
            
            # Test convergence properties
            # 1. Samples should be finite
            samples_finite = torch.isfinite(posterior_samples).all()
            
            # 2. Acceptance rate should be reasonable (not too high or low)
            reasonable_acceptance = 0.1 < acceptance_rate < 0.8
            
            # 3. Test convergence by comparing early vs late samples
            if len(sample_history) >= 2:
                early_samples = sample_history[0]
                late_samples = sample_history[-1]
                
                # Compute sample statistics
                early_mean = early_samples.mean(dim=0)
                late_mean = late_samples.mean(dim=0)
                
                early_var = early_samples.var(dim=0, unbiased=True)
                late_var = late_samples.var(dim=0, unbiased=True)
                
                # Convergence indicators
                mean_diff = F.mse_loss(early_mean, late_mean).item()
                var_diff = F.mse_loss(early_var, late_var).item()
                
                # Samples should stabilize (but allow some difference)
                mean_converged = mean_diff < 1.0
                var_converged = var_diff < 1.0
            else:
                mean_converged = True
                var_converged = True
                mean_diff = 0.0
                var_diff = 0.0
            
            # 4. Test sample diversity
            pairwise_distances = torch.cdist(posterior_samples, posterior_samples)
            min_distance = pairwise_distances[pairwise_distances > 0].min().item()
            good_diversity = min_distance > 1e-4
            
            # 5. Test that samples concentrate around reasonable values
            sample_mean = posterior_samples.mean(dim=0)
            sample_std = posterior_samples.std(dim=0)
            reasonable_concentration = torch.all(sample_std < 5.0) and torch.all(sample_std > 1e-3)
            
            execution_time = time.time() - start_time
            
            # Determine test result
            result = TestResult.PASS if (
                samples_finite and reasonable_acceptance and mean_converged and 
                var_converged and good_diversity and reasonable_concentration
            ) else TestResult.FAIL
            
            return TestReport(
                test_name="bayesian_inference_convergence",
                result=result,
                metrics=TestMetrics(
                    execution_time=execution_time,
                    convergence_steps=len(sample_history)
                ),
                details={
                    'samples_finite': samples_finite.item(),
                    'acceptance_rate': acceptance_rate,
                    'reasonable_acceptance': reasonable_acceptance,
                    'mean_converged': mean_converged,
                    'var_converged': var_converged,
                    'good_diversity': good_diversity,
                    'reasonable_concentration': reasonable_concentration.item(),
                    'mean_difference': mean_diff,
                    'variance_difference': var_diff,
                    'min_sample_distance': min_distance,
                    'sample_std_range': [sample_std.min().item(), sample_std.max().item()]
                }
            )
            
        except Exception as e:
            return TestReport(
                test_name="bayesian_inference_convergence",
                result=TestResult.ERROR,
                metrics=TestMetrics(execution_time=time.time() - start_time),
                error_message=str(e)
            )
    
    def test_evidence_estimation(self) -> TestReport:
        """Test marginal likelihood (evidence) estimation."""
        start_time = time.time()
        
        try:
            # Simple evidence estimation using importance sampling
            class EvidenceEstimator:
                def __init__(self, concept_dim, latent_dim=16):
                    self.concept_dim = concept_dim
                    self.latent_dim = latent_dim
                    self.device = TEST_DEVICE
                
                def log_prior(self, z):
                    """Standard normal prior."""
                    return -0.5 * torch.sum(z**2, dim=-1) - 0.5 * self.latent_dim * np.log(2 * np.pi)
                
                def log_likelihood(self, z, x):
                    """Simple Gaussian likelihood."""
                    # Simple linear model: x = W*z + noise
                    W = torch.randn(self.concept_dim, self.latent_dim, device=self.device)
                    mean_x = torch.matmul(z, W.T)
                    
                    # Assume unit variance noise
                    log_prob = -0.5 * torch.sum((x - mean_x)**2, dim=-1) - 0.5 * self.concept_dim * np.log(2 * np.pi)
                    return log_prob
                
                def estimate_evidence_importance_sampling(self, x, num_samples=1000):
                    """Estimate evidence using importance sampling."""
                    # Sample from prior (proposal distribution)
                    z_samples = torch.randn(num_samples, self.latent_dim, device=self.device)
                    
                    # Compute log weights: log p(x|z) + log p(z) - log q(z)
                    log_likelihood_vals = self.log_likelihood(z_samples, x.unsqueeze(0).expand(num_samples, -1))
                    log_prior_vals = self.log_prior(z_samples)
                    log_proposal_vals = self.log_prior(z_samples)  # Using prior as proposal
                    
                    log_weights = log_likelihood_vals + log_prior_vals - log_proposal_vals
                    log_weights = log_weights - torch.max(log_weights)  # Numerical stability
                    
                    # Estimate evidence
                    weights = torch.exp(log_weights)
                    evidence_estimate = torch.mean(weights)
                    log_evidence = torch.log(evidence_estimate + 1e-8)
                    
                    # Compute effective sample size
                    normalized_weights = weights / torch.sum(weights)
                    ess = 1.0 / torch.sum(normalized_weights**2)
                    
                    return log_evidence, ess, weights
                
                def estimate_evidence_harmonic_mean(self, x, num_samples=1000):
                    """Estimate evidence using harmonic mean estimator."""
                    # Sample from prior
                    z_samples = torch.randn(num_samples, self.latent_dim, device=self.device)
                    
                    # Compute likelihood values
                    likelihood_vals = torch.exp(self.log_likelihood(z_samples, x.unsqueeze(0).expand(num_samples, -1)))
                    
                    # Harmonic mean estimator (biased but often used)
                    harmonic_mean = num_samples / torch.sum(1.0 / (likelihood_vals + 1e-8))
                    log_evidence_hm = torch.log(harmonic_mean + 1e-8)
                    
                    return log_evidence_hm
            
            # Initialize estimator
            estimator = EvidenceEstimator(self.concept_dim)
            
            # Generate test data
            test_concepts = TestDataGenerator.generate_concept_embeddings(3, self.concept_dim, self.device)
            
            evidence_estimates = []
            ess_values = []
            estimation_errors = []
            
            for concept in test_concepts:
                # Estimate evidence using importance sampling
                log_ev_is, ess, weights = estimator.estimate_evidence_importance_sampling(concept, 500)
                
                # Estimate evidence using harmonic mean
                log_ev_hm = estimator.estimate_evidence_harmonic_mean(concept, 500)
                
                # Validate estimates
                # 1. Evidence estimates should be finite
                is_finite = torch.isfinite(log_ev_is) and torch.isfinite(log_ev_hm)
                
                # 2. Effective sample size should be reasonable
                reasonable_ess = ess > 10.0  # At least 10 effective samples
                
                # 3. Weights should be positive and finite
                weights_valid = torch.isfinite(weights).all() and torch.all(weights >= 0)
                
                # 4. Different methods should give similar results (roughly)
                estimate_consistency = torch.abs(log_ev_is - log_ev_hm) < 5.0  # Allow some difference
                
                evidence_estimates.append([log_ev_is.item(), log_ev_hm.item()])
                ess_values.append(ess.item())
                
                estimation_error = 0.0 if (is_finite and reasonable_ess and weights_valid) else 1.0
                estimation_errors.append(estimation_error)
            
            # Overall validation
            avg_estimation_error = np.mean(estimation_errors)
            avg_ess = np.mean(ess_values)
            
            # Test variance across different concepts
            is_estimates = [est[0] for est in evidence_estimates]
            hm_estimates = [est[1] for est in evidence_estimates]
            
            is_variance = np.var(is_estimates)
            hm_variance = np.var(hm_estimates)
            
            # Evidence should vary across different concepts
            reasonable_variance = is_variance > 0.1 and hm_variance > 0.1
            
            execution_time = time.time() - start_time
            
            # Determine test result
            result = TestResult.PASS if (
                avg_estimation_error < 0.5 and avg_ess > 5.0 and reasonable_variance
            ) else TestResult.FAIL
            
            return TestReport(
                test_name="evidence_estimation",
                result=result,
                metrics=TestMetrics(
                    execution_time=execution_time,
                    numerical_error=avg_estimation_error
                ),
                details={
                    'avg_estimation_error': avg_estimation_error,
                    'avg_effective_sample_size': avg_ess,
                    'reasonable_variance': reasonable_variance,
                    'is_variance': is_variance,
                    'hm_variance': hm_variance,
                    'evidence_estimates': evidence_estimates,
                    'ess_values': ess_values
                }
            )
            
        except Exception as e:
            return TestReport(
                test_name="evidence_estimation",
                result=TestResult.ERROR,
                metrics=TestMetrics(execution_time=time.time() - start_time),
                error_message=str(e)
            )
    
    def test_conditional_sampling(self) -> TestReport:
        """Test conditional sampling from posterior distributions."""
        start_time = time.time()
        
        try:
            # Conditional sampling test using rejection sampling
            class ConditionalSampler:
                def __init__(self, concept_dim):
                    self.concept_dim = concept_dim
                    self.device = TEST_DEVICE
                
                def sample_conditional(self, conditioning_concepts, target_properties, num_samples=100):
                    """Sample concepts conditional on target properties."""
                    samples = []
                    attempts = 0
                    max_attempts = num_samples * 10
                    
                    while len(samples) < num_samples and attempts < max_attempts:
                        # Generate candidate sample
                        candidate = torch.randn(self.concept_dim, device=self.device)
                        
                        # Check if candidate satisfies conditions
                        satisfies_conditions = True
                        
                        # Condition 1: Similarity to conditioning concepts
                        if len(conditioning_concepts) > 0:
                            similarities = F.cosine_similarity(
                                candidate.unsqueeze(0), 
                                conditioning_concepts, 
                                dim=-1
                            )
                            min_similarity = target_properties.get('min_similarity', 0.3)
                            if similarities.max() < min_similarity:
                                satisfies_conditions = False
                        
                        # Condition 2: Magnitude constraint
                        if 'magnitude_range' in target_properties:
                            magnitude = torch.norm(candidate)
                            min_mag, max_mag = target_properties['magnitude_range']
                            if magnitude < min_mag or magnitude > max_mag:
                                satisfies_conditions = False
                        
                        # Condition 3: Mean constraint
                        if 'mean_constraint' in target_properties:
                            concept_mean = candidate.mean()
                            target_mean, tolerance = target_properties['mean_constraint']
                            if torch.abs(concept_mean - target_mean) > tolerance:
                                satisfies_conditions = False
                        
                        if satisfies_conditions:
                            samples.append(candidate)
                        
                        attempts += 1
                    
                    if len(samples) > 0:
                        return torch.stack(samples)
                    else:
                        return torch.empty(0, self.concept_dim, device=self.device)
                
                def gibbs_sampling(self, initial_concept, conditioning_mask, num_steps=50):
                    """Gibbs sampling for conditional generation."""
                    current_concept = initial_concept.clone()
                    sample_history = []
                    
                    for step in range(num_steps):
                        # Update each dimension conditionally
                        for i in range(self.concept_dim):
                            if not conditioning_mask[i]:  # Only update unconditioned dimensions
                                # Simple conditional update: sample from normal distribution
                                # centered at weighted average of neighbors
                                neighbors = []
                                if i > 0:
                                    neighbors.append(current_concept[i-1])
                                if i < self.concept_dim - 1:
                                    neighbors.append(current_concept[i+1])
                                
                                if neighbors:
                                    neighbor_mean = torch.stack(neighbors).mean()
                                    # Sample new value
                                    current_concept[i] = torch.normal(neighbor_mean, 0.1)
                                else:
                                    current_concept[i] = torch.normal(0.0, 1.0)
                        
                        sample_history.append(current_concept.clone())
                    
                    return current_concept, sample_history
            
            # Initialize sampler
            sampler = ConditionalSampler(self.concept_dim)
            
            # Test conditional sampling
            conditioning_concepts = TestDataGenerator.generate_concept_embeddings(2, self.concept_dim, self.device)
            
            # Define target properties
            target_properties = {
                'min_similarity': 0.4,
                'magnitude_range': (0.5, 2.0),
                'mean_constraint': (0.1, 0.3)
            }
            
            # Test rejection sampling
            conditional_samples = sampler.sample_conditional(
                conditioning_concepts, target_properties, num_samples=20
            )
            
            # Validate conditional samples
            rejection_success = len(conditional_samples) > 0
            
            if rejection_success:
                # Test that samples satisfy conditions
                # 1. Similarity condition
                similarities = F.cosine_similarity(
                    conditional_samples.unsqueeze(1),
                    conditioning_concepts.unsqueeze(0),
                    dim=-1
                )
                max_similarities = similarities.max(dim=-1)[0]
                similarity_satisfied = torch.all(max_similarities >= target_properties['min_similarity'])
                
                # 2. Magnitude condition
                magnitudes = torch.norm(conditional_samples, dim=-1)
                min_mag, max_mag = target_properties['magnitude_range']
                magnitude_satisfied = torch.all((magnitudes >= min_mag) & (magnitudes <= max_mag))
                
                # 3. Mean condition
                sample_means = conditional_samples.mean(dim=-1)
                target_mean, tolerance = target_properties['mean_constraint']
                mean_satisfied = torch.all(torch.abs(sample_means - target_mean) <= tolerance)
                
                conditions_satisfied = similarity_satisfied and magnitude_satisfied and mean_satisfied
            else:
                conditions_satisfied = False
            
            # Test Gibbs sampling
            initial_concept = conditioning_concepts[0].clone()
            conditioning_mask = torch.zeros(self.concept_dim, dtype=torch.bool, device=self.device)
            conditioning_mask[:self.concept_dim//4] = True  # Condition first quarter of dimensions
            
            gibbs_result, gibbs_history = sampler.gibbs_sampling(
                initial_concept, conditioning_mask, num_steps=30
            )
            
            # Validate Gibbs sampling
            # 1. Conditioned dimensions should remain unchanged
            conditioned_preserved = torch.allclose(
                gibbs_result[conditioning_mask], 
                initial_concept[conditioning_mask], 
                atol=1e-6
            )
            
            # 2. Unconditioned dimensions should change
            unconditioned_changed = not torch.allclose(
                gibbs_result[~conditioning_mask],
                initial_concept[~conditioning_mask],
                atol=0.1
            )
            
            # 3. Sampling should converge (later samples should be similar)
            if len(gibbs_history) >= 2:
                late_samples = torch.stack(gibbs_history[-5:])
                convergence_variance = torch.var(late_samples, dim=0).mean()
                converged = convergence_variance < 0.5
            else:
                converged = True
            
            execution_time = time.time() - start_time
            
            # Determine test result
            result = TestResult.PASS if (
                rejection_success and conditions_satisfied and 
                conditioned_preserved and unconditioned_changed and converged
            ) else TestResult.FAIL
            
            return TestReport(
                test_name="conditional_sampling",
                result=result,
                metrics=TestMetrics(
                    execution_time=execution_time,
                    numerical_error=0.0
                ),
                details={
                    'rejection_success': rejection_success,
                    'conditions_satisfied': conditions_satisfied.item() if hasattr(conditions_satisfied, 'item') else conditions_satisfied,
                    'conditioned_preserved': conditioned_preserved.item(),
                    'unconditioned_changed': unconditioned_changed.item(),
                    'converged': converged.item() if hasattr(converged, 'item') else converged,
                    'num_conditional_samples': len(conditional_samples),
                    'convergence_variance': convergence_variance.item() if 'convergence_variance' in locals() else 0.0
                }
            )
            
        except Exception as e:
            return TestReport(
                test_name="conditional_sampling",
                result=TestResult.ERROR,
                metrics=TestMetrics(execution_time=time.time() - start_time),
                error_message=str(e)
            )

# =============================================================================
# Integration Tests
# =============================================================================

class IntegrationTester:
    """Test integration between diffusion reasoning components."""
    
    def __init__(self, device: str = TEST_DEVICE):
        self.device = device
        self.concept_dim = TEST_CONCEPT_DIM
        self.thought_space_dim = TEST_THOUGHT_SPACE_DIM
        self.diffusion_steps = TEST_DIFFUSION_STEPS
    
    def test_end_to_end_reasoning_pipeline(self) -> TestReport:
        """Test complete reasoning pipeline from concepts to goals."""
        start_time = time.time()
        
        try:
            # Create integrated reasoning system
            class IntegratedReasoningSystem:
                def __init__(self, concept_dim, thought_space_dim, diffusion_steps, device):
                    self.concept_dim = concept_dim
                    self.thought_space_dim = thought_space_dim
                    self.diffusion_steps = diffusion_steps
                    self.device = device
                    
                    # Initialize components
                    self.setup_diffusion_parameters()
                    self.setup_encoder_decoder()
                    self.setup_noise_predictor()
                    self.setup_uncertainty_estimator()
                
                def setup_diffusion_parameters(self):
                    """Setup diffusion process parameters."""
                    self.betas = torch.linspace(1e-4, 0.02, self.diffusion_steps, device=self.device)
                    self.alphas = 1.0 - self.betas
                    self.alphas_cumprod = torch.cumprod(self.alphas, dim=0)
                    self.sqrt_alphas_cumprod = torch.sqrt(self.alphas_cumprod)
                    self.sqrt_one_minus_alphas_cumprod = torch.sqrt(1.0 - self.alphas_cumprod)
                
                def setup_encoder_decoder(self):
                    """Setup concept encoder and decoder."""
                    self.encoder = nn.Sequential(
                        nn.Linear(self.concept_dim, self.thought_space_dim),
                        nn.ReLU(),
                        nn.Linear(self.thought_space_dim, self.thought_space_dim)
                    ).to(self.device)
                    
                    self.decoder = nn.Sequential(
                        nn.Linear(self.thought_space_dim, self.concept_dim),
                        nn.ReLU(),
                        nn.Linear(self.concept_dim, self.concept_dim)
                    ).to(self.device)
                
                def setup_noise_predictor(self):
                    """Setup noise prediction network."""
                    self.noise_predictor = nn.Sequential(
                        nn.Linear(self.thought_space_dim + 1, self.thought_space_dim * 2),
                        nn.ReLU(),
                        nn.Linear(self.thought_space_dim * 2, self.thought_space_dim)
                    ).to(self.device)
                
                def setup_uncertainty_estimator(self):
                    """Setup uncertainty estimation."""
                    self.uncertainty_net = nn.Sequential(
                        nn.Linear(self.thought_space_dim, self.thought_space_dim),
                        nn.ReLU(),
                        nn.Dropout(0.2),
                        nn.Linear(self.thought_space_dim, 1),
                        nn.Sigmoid()
                    ).to(self.device)
                
                def encode_concepts(self, concepts):
                    """Encode concepts to thought space."""
                    return self.encoder(concepts)
                
                def decode_thoughts(self, thoughts):
                    """Decode thoughts back to concept space."""
                    return self.decoder(thoughts)
                
                def predict_noise(self, thought_state, timestep):
                    """Predict noise for diffusion step."""
                    t_emb = timestep.float().unsqueeze(-1) / self.diffusion_steps
                    if len(thought_state.shape) == 1:
                        thought_state = thought_state.unsqueeze(0)
                        t_emb = t_emb.unsqueeze(0)
                    
                    input_tensor = torch.cat([thought_state, t_emb], dim=-1)
                    return self.noise_predictor(input_tensor)
                
                def estimate_uncertainty(self, thought_state):
                    """Estimate reasoning uncertainty."""
                    return self.uncertainty_net(thought_state)
                
                def reason_to_goal(self, source_concepts, goal_concept, num_reasoning_steps=20):
                    """Complete reasoning pipeline from source to goal."""
                    reasoning_path = []
                    uncertainty_path = []
                    
                    # 1. Encode source concepts to thought space
                    source_thoughts = self.encode_concepts(source_concepts)
                    goal_thought = self.encode_concepts(goal_concept.unsqueeze(0))[0]
                    
                    # 2. Initialize reasoning state (average of source thoughts)
                    current_thought = source_thoughts.mean(dim=0)
                    reasoning_path.append(self.decode_thoughts(current_thought.unsqueeze(0))[0])
                    
                    # 3. Reasoning loop using guided diffusion
                    for step in range(num_reasoning_steps):
                        # Add some noise for exploration
                        noise_scale = 0.1 * (1 - step / num_reasoning_steps)  # Reduce noise over time
                        exploration_noise = noise_scale * torch.randn_like(current_thought)
                        noisy_thought = current_thought + exploration_noise
                        
                        # Predict denoising direction
                        t = torch.tensor(step, device=self.device)
                        noise_pred = self.predict_noise(noisy_thought, t)[0]
                        
                        # Apply goal guidance
                        goal_direction = F.normalize(goal_thought - noisy_thought, dim=0)
                        guidance_strength = 0.2 * (step / num_reasoning_steps)  # Increase guidance over time
                        
                        # Update thought state
                        denoised = noisy_thought - 0.1 * noise_pred
                        guided = denoised + guidance_strength * goal_direction
                        current_thought = F.normalize(guided, dim=0)
                        
                        # Estimate uncertainty
                        uncertainty = self.estimate_uncertainty(current_thought.unsqueeze(0))[0]
                        uncertainty_path.append(uncertainty.item())
                        
                        # Decode back to concept space for path tracking
                        concept_state = self.decode_thoughts(current_thought.unsqueeze(0))[0]
                        reasoning_path.append(concept_state)
                    
                    return reasoning_path, uncertainty_path
            
            # Initialize integrated system
            system = IntegratedReasoningSystem(
                self.concept_dim, self.thought_space_dim, 
                self.diffusion_steps, self.device
            )
            
            # Generate test scenario
            source_concepts = TestDataGenerator.generate_concept_embeddings(3, self.concept_dim, self.device)
            goal_concept = TestDataGenerator.generate_concept_embeddings(1, self.concept_dim, self.device)[0]
            
            # Run end-to-end reasoning
            reasoning_path, uncertainty_path = system.reason_to_goal(
                source_concepts, goal_concept, num_reasoning_steps=15
            )
            
            # Validate reasoning pipeline
            # 1. Reasoning path should be complete
            complete_path = len(reasoning_path) > 0 and len(uncertainty_path) > 0
            
            # 2. All states should be finite
            path_finite = all(torch.isfinite(state).all() for state in reasoning_path)
            uncertainty_finite = all(np.isfinite(unc) for unc in uncertainty_path)
            
            # 3. Should make progress toward goal
            if len(reasoning_path) >= 2:
                initial_distance = F.mse_loss(reasoning_path[0], goal_concept).item()
                final_distance = F.mse_loss(reasoning_path[-1], goal_concept).item()
                progress_made = final_distance < initial_distance
            else:
                progress_made = False
            
            # 4. Uncertainty should be reasonable (not constant)
            uncertainty_variance = np.var(uncertainty_path)
            uncertainty_varying = uncertainty_variance > 1e-6
            
            # 5. Path should be smooth (consecutive steps similar)
            if len(reasoning_path) >= 2:
                path_differences = [
                    F.mse_loss(reasoning_path[i], reasoning_path[i+1]).item()
                    for i in range(len(reasoning_path)-1)
                ]
                path_smooth = all(diff < 1.0 for diff in path_differences)
            else:
                path_smooth = True
            
            # 6. Test encoding-decoding consistency
            test_concept = source_concepts[0]
            encoded = system.encode_concepts(test_concept.unsqueeze(0))[0]
            decoded = system.decode_thoughts(encoded.unsqueeze(0))[0]
            reconstruction_error = F.mse_loss(decoded, test_concept).item()
            good_reconstruction = reconstruction_error < 0.5
            
            execution_time = time.time() - start_time
            
            # Determine test result
            result = TestResult.PASS if (
                complete_path and path_finite and uncertainty_finite and 
                progress_made and uncertainty_varying and path_smooth and good_reconstruction
            ) else TestResult.FAIL
            
            return TestReport(
                test_name="end_to_end_reasoning_pipeline",
                result=result,
                metrics=TestMetrics(
                    execution_time=execution_time,
                    numerical_error=reconstruction_error
                ),
                details={
                    'complete_path': complete_path,
                    'path_finite': path_finite,
                    'uncertainty_finite': uncertainty_finite,
                    'progress_made': progress_made,
                    'uncertainty_varying': uncertainty_varying,
                    'path_smooth': path_smooth,
                    'good_reconstruction': good_reconstruction,
                    'initial_distance': initial_distance if 'initial_distance' in locals() else 0,
                    'final_distance': final_distance if 'final_distance' in locals() else 0,
                    'reconstruction_error': reconstruction_error,
                    'path_length': len(reasoning_path),
                    'uncertainty_variance': uncertainty_variance
                }
            )
            
        except Exception as e:
            return TestReport(
                test_name="end_to_end_reasoning_pipeline",
                result=TestResult.ERROR,
                metrics=TestMetrics(execution_time=time.time() - start_time),
                error_message=str(e)
            )
    
    def test_cross_component_communication(self) -> TestReport:
        """Test communication between different reasoning components."""
        start_time = time.time()
        
        try:
            # Test data flow between components
            communication_errors = []
            
            # 1. Test concept space <-> thought space communication
            test_concepts = TestDataGenerator.generate_concept_embeddings(5, self.concept_dim, self.device)
            
            # Simple encoder/decoder
            encoder = nn.Linear(self.concept_dim, self.thought_space_dim).to(self.device)
            decoder = nn.Linear(self.thought_space_dim, self.concept_dim).to(self.device)
            
            # Test encoding
            thoughts = encoder(test_concepts)
            reconstructed = decoder(thoughts)
            
            # Validate communication
            thoughts_finite = torch.isfinite(thoughts).all()
            reconstructed_finite = torch.isfinite(reconstructed).all()
            
            # Test information preservation
            concept_similarities = F.cosine_similarity(
                test_concepts.unsqueeze(1), test_concepts.unsqueeze(0), dim=-1
            )
            thought_similarities = F.cosine_similarity(
                thoughts.unsqueeze(1), thoughts.unsqueeze(0), dim=-1
            )
            
            # Similarity structure should be approximately preserved
            similarity_preservation = F.mse_loss(concept_similarities, thought_similarities).item()
            good_preservation = similarity_preservation < 0.5
            
            communication_errors.append(0.0 if (thoughts_finite and reconstructed_finite and good_preservation) else 1.0)
            
            # 2. Test diffusion <-> uncertainty communication
            # Simulate diffusion process uncertainty feedback
            diffusion_states = torch.randn(10, self.thought_space_dim, device=self.device)
            
            # Simple uncertainty predictor
            uncertainty_predictor = nn.Sequential(
                nn.Linear(self.thought_space_dim, self.thought_space_dim // 2),
                nn.ReLU(),
                nn.Linear(self.thought_space_dim // 2, 1),
                nn.Sigmoid()
            ).to(self.device)
            
            uncertainties = uncertainty_predictor(diffusion_states).squeeze(-1)
            
            # Validate uncertainty communication
            uncertainties_finite = torch.isfinite(uncertainties).all()
            uncertainties_bounded = torch.all(uncertainties >= 0) and torch.all(uncertainties <= 1)
            uncertainties_varying = torch.var(uncertainties) > 1e-6
            
            communication_errors.append(0.0 if (uncertainties_finite and uncertainties_bounded and uncertainties_varying) else 1.0)
            
            # 3. Test goal guidance communication
            source_thought = torch.randn(self.thought_space_dim, device=self.device)
            goal_thought = torch.randn(self.thought_space_dim, device=self.device)
            
            # Simple guidance computation
            def compute_guidance(current, goal, strength=1.0):
                direction = F.normalize(goal - current, dim=0)
                guidance = strength * direction
                return guidance
            
            guidance = compute_guidance(source_thought, goal_thought)
            guided_thought = source_thought + 0.1 * guidance
            
            # Validate guidance communication
            guidance_finite = torch.isfinite(guidance).all()
            guided_finite = torch.isfinite(guided_thought).all()
            
            # Guidance should move toward goal
            original_distance = F.mse_loss(source_thought, goal_thought).item()
            guided_distance = F.mse_loss(guided_thought, goal_thought).item()
            moved_toward_goal = guided_distance < original_distance
            
            communication_errors.append(0.0 if (guidance_finite and guided_finite and moved_toward_goal) else 1.0)
            
            # 4. Test inference <-> sampling communication
            # Test that inference results can guide sampling
            inference_result = torch.randn(self.thought_space_dim, device=self.device)
            
            # Use inference result to guide sampling
            sampling_candidates = []
            for _ in range(5):
                noise = torch.randn(self.thought_space_dim, device=self.device)
                # Bias sampling toward inference result
                biased_sample = 0.7 * inference_result + 0.3 * noise
                sampling_candidates.append(biased_sample)
            
            sampling_candidates = torch.stack(sampling_candidates)
            
            # Validate sampling communication
            candidates_finite = torch.isfinite(sampling_candidates).all()
            
            # Candidates should be closer to inference result than random samples
            distances_to_result = torch.norm(sampling_candidates - inference_result.unsqueeze(0), dim=-1)
            random_samples = torch.randn_like(sampling_candidates)
            distances_random = torch.norm(random_samples - inference_result.unsqueeze(0), dim=-1)
            
            guided_sampling = distances_to_result.mean() < distances_random.mean()
            
            communication_errors.append(0.0 if (candidates_finite and guided_sampling) else 1.0)
            
            execution_time = time.time() - start_time
            avg_communication_error = np.mean(communication_errors)
            
            # Determine test result
            result = TestResult.PASS if avg_communication_error < 0.25 else TestResult.FAIL
            
            return TestReport(
                test_name="cross_component_communication",
                result=result,
                metrics=TestMetrics(
                    execution_time=execution_time,
                    numerical_error=avg_communication_error
                ),
                details={
                    'avg_communication_error': avg_communication_error,
                    'concept_thought_preservation': good_preservation,
                    'uncertainty_communication_valid': uncertainties_finite and uncertainties_bounded and uncertainties_varying,
                    'guidance_communication_valid': guidance_finite and guided_finite and moved_toward_goal,
                    'sampling_communication_valid': candidates_finite and guided_sampling,
                    'similarity_preservation_error': similarity_preservation,
                    'communication_errors': communication_errors
                }
            )
            
        except Exception as e:
            return TestReport(
                test_name="cross_component_communication",
                result=TestResult.ERROR,
                metrics=TestMetrics(execution_time=time.time() - start_time),
                error_message=str(e)
            )

# =============================================================================
# Performance and Scalability Tests
# =============================================================================

class PerformanceTester:
    """Test performance and scalability characteristics."""
    
    def __init__(self, device: str = TEST_DEVICE):
        self.device = device
        
    def test_computational_efficiency(self) -> TestReport:
        """Test computational efficiency of diffusion reasoning operations."""
        start_time = time.time()
        
        try:
            performance_metrics = {}
            
            # Test different scale operations
            scales = [32, 64, 128, 256]
            operations = ['encoding', 'diffusion_step', 'uncertainty_estimation']
            
            for scale in scales:
                scale_metrics = {}
                
                # Generate test data
                test_data = torch.randn(16, scale, device=self.device)
                
                # Test encoding operation
                encoder = nn.Linear(scale, scale).to(self.device)
                
                try:
                    torch.cuda.synchronize() if self.device == 'cuda' else None
                except:
                    pass  # Ignore synchronization errors
                    
                encoding_start = time.time()
                
                with torch.no_grad():
                    encoded = encoder(test_data)
                
                try:
                    torch.cuda.synchronize() if self.device == 'cuda' else None
                except:
                    pass  # Ignore synchronization errors
                    
                encoding_time = time.time() - encoding_start
                
                scale_metrics['encoding'] = encoding_time
                
                # Test diffusion step operation
                timesteps = torch.randint(0, 100, (16,), device=self.device)
                
                try:
                    torch.cuda.synchronize() if self.device == 'cuda' else None
                except:
                    pass
                    
                diffusion_start = time.time()
                
                with torch.no_grad():
                    # Simulate diffusion step
                    noise = torch.randn_like(test_data)
                    alpha_t = torch.rand(16, 1, device=self.device)
                    noisy_data = torch.sqrt(alpha_t) * test_data + torch.sqrt(1 - alpha_t) * noise
                
                try:
                    torch.cuda.synchronize() if self.device == 'cuda' else None
                except:
                    pass
                    
                diffusion_time = time.time() - diffusion_start
                
                scale_metrics['diffusion_step'] = diffusion_time
                
                # Test uncertainty estimation
                uncertainty_net = nn.Sequential(
                    nn.Linear(scale, scale // 2),
                    nn.ReLU(),
                    nn.Linear(scale // 2, 1)
                ).to(self.device)
                
                try:
                    torch.cuda.synchronize() if self.device == 'cuda' else None
                except:
                    pass
                    
                uncertainty_start = time.time()
                
                with torch.no_grad():
                    uncertainties = uncertainty_net(test_data)
                
                try:
                    torch.cuda.synchronize() if self.device == 'cuda' else None
                except:
                    pass
                    
                uncertainty_time = time.time() - uncertainty_start
                
                scale_metrics['uncertainty_estimation'] = uncertainty_time
                
                performance_metrics[scale] = scale_metrics
            
            # Analyze scalability
            scalability_analysis = {}
            
            for operation in operations:
                times = [performance_metrics[scale][operation] for scale in scales]
                
                # Check if time scales reasonably with dimension
                # Expected: roughly quadratic for matrix operations
                time_ratios = [times[i] / times[0] for i in range(len(times))]
                scale_ratios = [scales[i] / scales[0] for i in range(len(scales))]
                
                # Linear fit to log-log data
                log_times = np.log(np.array(times) + 1e-8)  # Add small epsilon to avoid log(0)
                log_scales = np.log(np.array(scale_ratios) + 1e-8)
                
                if len(log_times) > 1 and np.var(log_scales) > 1e-6:
                    try:
                        correlation_matrix = np.corrcoef(log_scales, log_times)
                        slope = correlation_matrix[0, 1] if correlation_matrix.shape == (2, 2) else 1.0
                        slope = slope if np.isfinite(slope) else 1.0
                    except (ValueError, np.linalg.LinAlgError):
                        slope = 1.0
                    
                    scalability_analysis[operation] = {
                        'slope': slope,
                        'times': times,
                        'reasonable_scaling': abs(slope) < 3.0  # Should not scale worse than cubic
                    }
                else:
                    scalability_analysis[operation] = {
                        'slope': 1.0,
                        'times': times,
                        'reasonable_scaling': True
                    }
            
            # Check absolute performance
            baseline_scale = 128
            baseline_metrics = performance_metrics[baseline_scale]
            
            # Performance should be reasonable (< 1 second for basic operations)
            reasonable_performance = all(
                time < PERFORMANCE_TOLERANCE for time in baseline_metrics.values()
            )
            
            # Check memory usage (simplified)
            if self.device == 'cuda':
                try:
                    memory_allocated = torch.cuda.memory_allocated() / 1024**3  # GB
                    memory_reasonable = memory_allocated < MEMORY_LIMIT_GB
                except:
                    memory_reasonable = True
                    memory_allocated = 0.0
            else:
                memory_reasonable = True
                memory_allocated = 0.0
            
            execution_time = time.time() - start_time
            
            # Determine test result
            all_scaling_reasonable = all(
                analysis['reasonable_scaling'] for analysis in scalability_analysis.values()
            )
            
            result = TestResult.PASS if (
                reasonable_performance and all_scaling_reasonable and memory_reasonable
            ) else TestResult.FAIL
            
            return TestReport(
                test_name="computational_efficiency",
                result=result,
                metrics=TestMetrics(
                    execution_time=execution_time,
                    memory_usage_mb=memory_allocated * 1024
                ),
                details={
                    'performance_metrics': performance_metrics,
                    'scalability_analysis': scalability_analysis,
                    'reasonable_performance': reasonable_performance,
                    'all_scaling_reasonable': all_scaling_reasonable,
                    'memory_reasonable': memory_reasonable,
                    'memory_allocated_gb': memory_allocated,
                    'baseline_metrics': baseline_metrics
                }
            )
            
        except Exception as e:
            return TestReport(
                test_name="computational_efficiency",
                result=TestResult.ERROR,
                metrics=TestMetrics(execution_time=time.time() - start_time),
                error_message=str(e)
            )

# =============================================================================
# Comprehensive Test Suite Runner
# =============================================================================

class UltraDiffusionTestSuite:
    """Main test suite runner for Ultra diffusion reasoning module."""
    
    def __init__(self, device: str = TEST_DEVICE, verbose: bool = True):
        self.device = device
        self.verbose = verbose
        self.test_results = []
        
        # Initialize test components
        self.conceptual_diffusion_tester = ConceptualDiffusionTester(device)
        self.thought_latent_space_tester = ThoughtLatentSpaceTester(device)
        self.reverse_diffusion_tester = ReverseDiffusionReasoningTester(device)
        self.bayesian_uncertainty_tester = BayesianUncertaintyTester(device)
        self.probabilistic_inference_tester = ProbabilisticInferenceTester(device)
        self.integration_tester = IntegrationTester(device)
        self.performance_tester = PerformanceTester(device)
        
        logger.info(f"Ultra Diffusion Test Suite initialized on {device}")
    
    def run_all_tests(self) -> Dict[str, Any]:
        """Run complete test suite and return comprehensive results."""
        logger.info("Starting Ultra Diffusion Reasoning Test Suite...")
        suite_start_time = time.time()
        
        # Define test cases
        test_cases = [
            # Conceptual Diffusion Tests
            TestCase("Forward Diffusion Process", "Test mathematical properties of forward diffusion",
                    self.conceptual_diffusion_tester.test_forward_diffusion_process),
            TestCase("Reverse Diffusion Sampling", "Test reverse diffusion sampling quality",
                    self.conceptual_diffusion_tester.test_reverse_diffusion_sampling),
            TestCase("Diffusion Loss Function", "Test diffusion training loss properties",
                    self.conceptual_diffusion_tester.test_diffusion_loss_function),
            
            # Thought Latent Space Tests
            TestCase("Concept Encoding/Decoding", "Test concept space transformations",
                    self.thought_latent_space_tester.test_concept_encoding_decoding),
            TestCase("Semantic Similarity", "Test semantic similarity computation",
                    self.thought_latent_space_tester.test_semantic_similarity_computation),
            TestCase("Concept Composition", "Test concept composition operations",
                    self.thought_latent_space_tester.test_concept_composition_operations),
            TestCase("Latent Space Interpolation", "Test interpolation in thought space",
                    self.thought_latent_space_tester.test_latent_space_interpolation),
            
            # Reverse Diffusion Reasoning Tests
            TestCase("Goal-Directed Diffusion", "Test goal-directed reasoning",
                    self.reverse_diffusion_tester.test_goal_directed_diffusion),
            TestCase("Constraint-Guided Sampling", "Test constraint satisfaction in reasoning",
                    self.reverse_diffusion_tester.test_constraint_guided_sampling),
            TestCase("Diffusion Bridge Reasoning", "Test reasoning between concept pairs",
                    self.reverse_diffusion_tester.test_diffusion_bridge_reasoning),
            
            # Bayesian Uncertainty Tests
            TestCase("Epistemic Uncertainty", "Test model uncertainty estimation",
                    self.bayesian_uncertainty_tester.test_epistemic_uncertainty_estimation),
            TestCase("Aleatoric Uncertainty", "Test data uncertainty estimation",
                    self.bayesian_uncertainty_tester.test_aleatoric_uncertainty_estimation),
            TestCase("Uncertainty Decomposition", "Test uncertainty component separation",
                    self.bayesian_uncertainty_tester.test_uncertainty_decomposition),
            TestCase("Uncertainty Calibration", "Test uncertainty reliability",
                    self.bayesian_uncertainty_tester.test_uncertainty_calibration),
            
            # Probabilistic Inference Tests
            TestCase("Bayesian Inference Convergence", "Test inference convergence properties",
                    self.probabilistic_inference_tester.test_bayesian_inference_convergence),
            TestCase("Evidence Estimation", "Test marginal likelihood estimation",
                    self.probabilistic_inference_tester.test_evidence_estimation),
            TestCase("Conditional Sampling", "Test conditional posterior sampling",
                    self.probabilistic_inference_tester.test_conditional_sampling),
            
            # Integration Tests
            TestCase("End-to-End Reasoning", "Test complete reasoning pipeline",
                    self.integration_tester.test_end_to_end_reasoning_pipeline),
            TestCase("Cross-Component Communication", "Test component interaction",
                    self.integration_tester.test_cross_component_communication),
            
            # Performance Tests
            TestCase("Computational Efficiency", "Test performance and scalability",
                    self.performance_tester.test_computational_efficiency)
        ]
        
        # Run tests
        for i, test_case in enumerate(test_cases):
            if self.verbose:
                logger.info(f"Running test {i+1}/{len(test_cases)}: {test_case.name}")
            
            try:
                # Run test with timeout
                test_result = self._run_test_with_timeout(test_case)
                self.test_results.append(test_result)
                
                if self.verbose:
                    status = "PASS" if test_result.result == TestResult.PASS else "FAIL"
                    logger.info(f"  {status} - {test_result.metrics.execution_time:.3f}s")
                    
            except Exception as e:
                error_result = TestReport(
                    test_name=test_case.name.lower().replace(' ', '_'),
                    result=TestResult.ERROR,
                    metrics=TestMetrics(),
                    error_message=str(e)
                )
                self.test_results.append(error_result)
                logger.error(f"  ERROR - {test_case.name}: {e}")
        
        suite_execution_time = time.time() - suite_start_time
        
        # Generate comprehensive report
        report = self._generate_comprehensive_report(suite_execution_time)
        
        if self.verbose:
            self._print_summary_report(report)
        
        return report
    
    def _run_test_with_timeout(self, test_case: TestCase) -> TestReport:
        """Run a single test with timeout protection."""
        
        class TimeoutException(Exception):
            pass
        
        def timeout_handler(signum, frame):
            raise TimeoutException("Test timeout")
        
        # Set timeout (only on Unix systems)
        if hasattr(signal, 'SIGALRM'):
            signal.signal(signal.SIGALRM, timeout_handler)
            signal.alarm(int(test_case.timeout_seconds))
        
        try:
            result = test_case.test_function()
            return result
        except TimeoutException:
            return TestReport(
                test_name=test_case.name.lower().replace(' ', '_'),
                result=TestResult.ERROR,
                metrics=TestMetrics(),
                error_message=f"Test timeout after {test_case.timeout_seconds} seconds"
            )
        finally:
            if hasattr(signal, 'SIGALRM'):
                signal.alarm(0)  # Cancel timeout
    
    def _generate_comprehensive_report(self, suite_execution_time: float) -> Dict[str, Any]:
        """Generate comprehensive test report."""
        # Count results by type
        result_counts = {
            TestResult.PASS: sum(1 for r in self.test_results if r.result == TestResult.PASS),
            TestResult.FAIL: sum(1 for r in self.test_results if r.result == TestResult.FAIL),
            TestResult.ERROR: sum(1 for r in self.test_results if r.result == TestResult.ERROR),
            TestResult.SKIP: sum(1 for r in self.test_results if r.result == TestResult.SKIP)
        }
        
        total_tests = len(self.test_results)
        pass_rate = result_counts[TestResult.PASS] / total_tests if total_tests > 0 else 0.0
        
        # Performance statistics
        execution_times = [r.metrics.execution_time for r in self.test_results]
        total_execution_time = sum(execution_times)
        avg_execution_time = total_execution_time / len(execution_times) if execution_times else 0.0
        
        # Numerical error statistics
        numerical_errors = [r.metrics.numerical_error for r in self.test_results if r.metrics.numerical_error > 0]
        avg_numerical_error = np.mean(numerical_errors) if numerical_errors else 0.0
        
        # Component-wise analysis
        component_analysis = {
            'conceptual_diffusion': [r for r in self.test_results if 'diffusion' in r.test_name and 'reverse' not in r.test_name],
            'thought_latent_space': [r for r in self.test_results if any(kw in r.test_name for kw in ['encoding', 'similarity', 'composition', 'interpolation'])],
            'reverse_diffusion': [r for r in self.test_results if 'reverse' in r.test_name or 'goal' in r.test_name or 'constraint' in r.test_name or 'bridge' in r.test_name],
            'bayesian_uncertainty': [r for r in self.test_results if 'uncertainty' in r.test_name or 'calibration' in r.test_name],
            'probabilistic_inference': [r for r in self.test_results if any(kw in r.test_name for kw in ['inference', 'evidence', 'conditional'])],
            'integration': [r for r in self.test_results if any(kw in r.test_name for kw in ['end_to_end', 'communication'])],
            'performance': [r for r in self.test_results if 'efficiency' in r.test_name]
        }
        
        component_pass_rates = {}
        for component, results in component_analysis.items():
            if results:
                passes = sum(1 for r in results if r.result == TestResult.PASS)
                component_pass_rates[component] = passes / len(results)
            else:
                component_pass_rates[component] = 0.0
        
        # Critical failures (tests that must pass for basic functionality)
        critical_tests = [
            'forward_diffusion_process',
            'concept_encoding_decoding',
            'semantic_similarity_computation',
            'epistemic_uncertainty_estimation',
            'end_to_end_reasoning_pipeline'
        ]
        
        critical_failures = [
            r for r in self.test_results 
            if r.test_name in critical_tests and r.result != TestResult.PASS
        ]
        
        return {
            'summary': {
                'total_tests': total_tests,
                'passed': result_counts[TestResult.PASS],
                'failed': result_counts[TestResult.FAIL],
                'errors': result_counts[TestResult.ERROR],
                'skipped': result_counts[TestResult.SKIP],
                'pass_rate': pass_rate,
                'suite_execution_time': suite_execution_time,
                'total_test_time': total_execution_time,
                'avg_test_time': avg_execution_time
            },
            'performance': {
                'avg_numerical_error': avg_numerical_error,
                'execution_times': execution_times,
                'slowest_tests': sorted(
                    [(r.test_name, r.metrics.execution_time) for r in self.test_results],
                    key=lambda x: x[1], reverse=True
                )[:5]
            },
            'component_analysis': {
                'pass_rates': component_pass_rates,
                'component_results': {k: len(v) for k, v in component_analysis.items()}
            },
            'critical_failures': [r.test_name for r in critical_failures],
            'detailed_results': [
                {
                    'test_name': r.test_name,
                    'result': r.result.name,
                    'execution_time': r.metrics.execution_time,
                    'numerical_error': r.metrics.numerical_error,
                    'error_message': r.error_message,
                    'details': r.details
                }
                for r in self.test_results
            ],
            'recommendations': self._generate_recommendations(critical_failures, component_pass_rates),
            'system_info': {
                'device': self.device,
                'concept_dim': TEST_CONCEPT_DIM,
                'thought_space_dim': TEST_THOUGHT_SPACE_DIM,
                'diffusion_steps': TEST_DIFFUSION_STEPS,
                'batch_size': TEST_BATCH_SIZE
            }
        }
    
    def _generate_recommendations(self, critical_failures, component_pass_rates) -> List[str]:
        """Generate recommendations based on test results."""
        recommendations = []
        
        if critical_failures:
            recommendations.append(f"CRITICAL: {len(critical_failures)} critical tests failed. System may not function properly.")
            recommendations.extend([f"  - Fix {failure.test_name}" for failure in critical_failures])
        
        for component, pass_rate in component_pass_rates.items():
            if pass_rate < 0.7:
                recommendations.append(f"Component '{component}' has low pass rate ({pass_rate:.1%}). Review implementation.")
            elif pass_rate < 0.9:
                recommendations.append(f"Component '{component}' has moderate issues ({pass_rate:.1%}). Consider improvements.")
        
        if not critical_failures and all(rate > 0.8 for rate in component_pass_rates.values()):
            recommendations.append("Overall system health is good. Consider optimizing performance.")
        
        return recommendations
    
    def _print_summary_report(self, report: Dict[str, Any]):
        """Print human-readable summary report."""
        print("\n" + "="*80)
        print("ULTRA DIFFUSION REASONING TEST SUITE - SUMMARY REPORT")
        print("="*80)
        
        summary = report['summary']
        print(f"Total Tests: {summary['total_tests']}")
        print(f"Passed: {summary['passed']} ({summary['pass_rate']:.1%})")
        print(f"Failed: {summary['failed']}")
        print(f"Errors: {summary['errors']}")
        print(f"Execution Time: {summary['suite_execution_time']:.2f}s")
        
        print(f"\nComponent Pass Rates:")
        for component, pass_rate in report['component_analysis']['pass_rates'].items():
            status = "✓" if pass_rate > 0.8 else "⚠" if pass_rate > 0.5 else "✗"
            print(f"  {status} {component.replace('_', ' ').title()}: {pass_rate:.1%}")
        
        if report['critical_failures']:
            print(f"\nCRITICAL FAILURES:")
            for failure in report['critical_failures']:
                print(f"  ✗ {failure}")
        
        if report['recommendations']:
            print(f"\nRECOMMENDATIONS:")
            for rec in report['recommendations']:
                print(f"  • {rec}")
        
        print("\n" + "="*80)
    
    def _generate_recommendations(self, critical_failures, component_pass_rates) -> List[str]:
        """Generate recommendations based on test results."""
        recommendations = []
        
        if critical_failures:
            recommendations.append(f"CRITICAL: {len(critical_failures)} critical tests failed. System may not function properly.")
            recommendations.extend([f"  - Fix {failure.test_name}" for failure in critical_failures])
        
        for component, pass_rate in component_pass_rates.items():
            if pass_rate < 0.7:
                recommendations.append(f"Component '{component}' has low pass rate ({pass_rate:.1%}). Review implementation.")
            elif pass_rate < 0.9:
                recommendations.append(f"Component '{component}' has moderate issues ({pass_rate:.1%}). Consider improvements.")
        
        if not critical_failures and all(rate > 0.8 for rate in component_pass_rates.values()):
            recommendations.append("Overall system health is good. Consider optimizing performance.")
        
        return recommendations

# =============================================================================
# Main Execution
# =============================================================================

def main():
    """Main function to run the test suite."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Ultra Diffusion Reasoning Test Suite')
    parser.add_argument('--device', default='auto', choices=['auto', 'cpu', 'cuda'],
                       help='Device to run tests on')
    parser.add_argument('--verbose', action='store_true', help='Verbose output')
    parser.add_argument('--save-report', action='store_true', help='Save detailed report to file')
    
    args = parser.parse_args()
    
    # Determine device
    if args.device == 'auto':
        device = 'cuda' if torch.cuda.is_available() else 'cpu'
    else:
        device = args.device
    
    print(f"Ultra Diffusion Reasoning Test Suite")
    print(f"Device: {device}")
    print(f"PyTorch Version: {torch.__version__}")
    print("-" * 50)
    
    # Run test suite
    test_suite = UltraDiffusionTestSuite(device=device, verbose=args.verbose)
    report = test_suite.run_all_tests()
    
    # Save report if requested
    if args.save_report:
        test_suite.save_report(report)
    
    # Return exit code based on results
    if report['critical_failures']:
        return 1  # Critical failures
    elif report['summary']['pass_rate'] < 0.8:
        return 2  # Low pass rate
    else:
        return 0  # Success

if __name__ == "__main__":
    exit(main())