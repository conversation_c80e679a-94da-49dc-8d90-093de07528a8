#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ULTRA: Ultimate Learning & Thought Reasoning Architecture
Bayesian Uncertainty Quantification Module

This module provides a framework for reasoning under uncertainty, allowing the system
to explicitly represent and quantify its uncertainty about concepts, relations, and reasoning
paths. It employs Bayesian methods to maintain distributions over possible values rather
than point estimates.

Key components:
1. GaussianDistribution: For representing concept uncertainty
2. BayesianLinearRegression: For modeling relationships with uncertainty
3. EpistemicUncertainty: For knowledge-based uncertainty quantification 
4. AleatoricUncertainty: For inherent data uncertainty
5. VariationalInference: For approximating complex posteriors
6. UncertaintyDecomposition: For separating different sources of uncertainty
7. BayesianUncertaintyQuantification: Main class integrating all uncertainty components

Mathematical foundation:
- Bayesian inference: p(θ|D) ∝ p(D|θ)p(θ)
- Epistemic uncertainty: Uncertainty due to limited knowledge/data
- Aleatoric uncertainty: Inherent, irreducible randomness
- Variational inference: q_φ(θ) ≈ p(θ|D)
- KL divergence: KL(q||p) = ∫ q(x) log(q(x)/p(x)) dx
"""

import os
import math
import logging
from pathlib import Path
from typing import Dict, List, Tuple, Union, Optional, Callable, Any

import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.distributions import MultivariateNormal, Normal, kl_divergence

# Configure module logger
logger = logging.getLogger(__name__)

# Constants
DEFAULT_PRECISION = 1e-5
DEFAULT_MC_SAMPLES = 32
DEFAULT_DIMENSION = 512


class GaussianDistribution:
    """
    Represents a multivariate Gaussian distribution over concept embeddings.
    """
    
    def __init__(
        self, 
        mean: torch.Tensor, 
        covariance: Optional[torch.Tensor] = None,
        precision: Optional[torch.Tensor] = None,
        diagonal: bool = True
    ):
        """
        Initialize a Gaussian distribution over concept space.
        
        Args:
            mean: Mean vector of the distribution [dimension]
            covariance: Optional covariance matrix [dimension, dimension]
            precision: Optional precision matrix (inverse of covariance) [dimension, dimension]
            diagonal: Whether to use diagonal covariance (for efficiency)
        """
        if len(mean.shape) > 1:
            mean = mean.squeeze()
            
        self.mean = mean
        self.dimension = mean.shape[0]
        self.diagonal = diagonal
        self.device = mean.device
        
        if diagonal:
            # Diagonal covariance for efficiency
            if covariance is not None:
                if len(covariance.shape) == 2:
                    # Extract diagonal elements
                    self.variance = torch.diag(covariance)
                else:
                    # Already a vector of variances
                    self.variance = covariance
            elif precision is not None:
                if len(precision.shape) == 2:
                    # Extract diagonal elements and invert
                    self.variance = 1.0 / torch.diag(precision)
                else:
                    # Already a vector of precisions
                    self.variance = 1.0 / precision
            else:
                # Default to identity covariance
                self.variance = torch.ones_like(mean)
                
            # Ensure positive variances
            self.variance = torch.clamp(self.variance, min=1e-6)
            
        else:
            # Full covariance matrix
            if covariance is not None:
                self.covariance = covariance
                # Compute precision using Cholesky decomposition for stability
                try:
                    L = torch.linalg.cholesky(covariance)
                    self.precision = torch.cholesky_inverse(L)
                except:
                    # Fallback to direct inversion with ridge regularization
                    ridge = torch.eye(self.dimension, device=self.device) * 1e-6
                    self.precision = torch.inverse(covariance + ridge)
            elif precision is not None:
                self.precision = precision
                # Compute covariance using Cholesky decomposition for stability
                try:
                    L = torch.linalg.cholesky(precision)
                    self.covariance = torch.cholesky_inverse(L)
                except:
                    # Fallback to direct inversion with ridge regularization
                    ridge = torch.eye(self.dimension, device=self.device) * 1e-6
                    self.covariance = torch.inverse(precision + ridge)
            else:
                # Default to identity covariance
                self.covariance = torch.eye(self.dimension, device=self.device)
                self.precision = torch.eye(self.dimension, device=self.device)
    
    @property
    def covariance_matrix(self) -> torch.Tensor:
        """Get the full covariance matrix."""
        if self.diagonal:
            return torch.diag(self.variance)
        else:
            return self.covariance
    
    @property
    def precision_matrix(self) -> torch.Tensor:
        """Get the full precision matrix."""
        if self.diagonal:
            return torch.diag(1.0 / self.variance)
        else:
            return self.precision
            
    def log_prob(self, x: torch.Tensor) -> torch.Tensor:
        """
        Compute log probability density at x.
        
        Args:
            x: Point(s) to evaluate [batch_size, dimension]
            
        Returns:
            Log probability density [batch_size]
        """
        if len(x.shape) == 1:
            # Single point
            x = x.unsqueeze(0)
            
        batch_size = x.shape[0]
        
        if self.diagonal:
            # Efficient computation for diagonal covariance
            delta = x - self.mean
            log_det = torch.sum(torch.log(self.variance))
            mahalanobis = torch.sum(delta * delta / self.variance, dim=1)
        else:
            # Full covariance computation
            delta = x - self.mean
            log_det = torch.logdet(self.covariance)
            mahalanobis = torch.sum(delta * torch.matmul(delta, self.precision), dim=1)
            
        log_prob = -0.5 * (self.dimension * np.log(2 * np.pi) + log_det + mahalanobis)
        
        return log_prob
    
    def sample(self, n_samples: int = 1) -> torch.Tensor:
        """
        Sample from the distribution.
        
        Args:
            n_samples: Number of samples to draw
            
        Returns:
            Samples [n_samples, dimension]
        """
        if self.diagonal:
            # Efficient sampling for diagonal covariance
            standard_normal = torch.randn(n_samples, self.dimension, device=self.device)
            std_dev = torch.sqrt(self.variance)
            samples = self.mean + standard_normal * std_dev
        else:
            # Full covariance sampling using Cholesky decomposition
            try:
                L = torch.linalg.cholesky(self.covariance)
                standard_normal = torch.randn(n_samples, self.dimension, device=self.device)
                samples = self.mean + torch.matmul(standard_normal, L.T)
            except:
                # Fallback to eigendecomposition for numerical stability
                eigvals, eigvecs = torch.linalg.eigh(self.covariance)
                eigvals = torch.clamp(eigvals, min=1e-6)  # Ensure positive eigenvalues
                std_dev_matrix = torch.matmul(eigvecs, torch.diag(torch.sqrt(eigvals)))
                standard_normal = torch.randn(n_samples, self.dimension, device=self.device)
                samples = self.mean + torch.matmul(standard_normal, std_dev_matrix)
                
        return samples
    
    def kl_divergence(self, other: 'GaussianDistribution') -> float:
        """
        Compute KL divergence from this distribution to another.
        
        Args:
            other: Other Gaussian distribution
            
        Returns:
            KL divergence
        """
        if self.diagonal and other.diagonal:
            # KL divergence for diagonal Gaussians
            ratio = self.variance / other.variance
            trace_term = torch.sum(ratio)
            delta = other.mean - self.mean
            quadratic_term = torch.sum((delta**2) / other.variance)
            log_det_ratio = torch.sum(torch.log(other.variance)) - torch.sum(torch.log(self.variance))
            
            kl = 0.5 * (trace_term + quadratic_term - self.dimension + log_det_ratio)
            
        else:
            # Get full matrices
            cov1 = self.covariance_matrix
            cov2 = other.covariance_matrix
            prec2 = other.precision_matrix
            
            # KL divergence for full Gaussians
            delta = other.mean - self.mean
            quadratic_term = torch.sum(delta * torch.matmul(prec2, delta))
            trace_term = torch.trace(torch.matmul(prec2, cov1))
            log_det_ratio = torch.logdet(cov2) - torch.logdet(cov1)
            
            kl = 0.5 * (trace_term + quadratic_term - self.dimension + log_det_ratio)
            
        return kl.item()
    
    def entropy(self) -> float:
        """
        Compute the entropy of the distribution.
        
        Returns:
            Entropy value
        """
        if self.diagonal:
            # Entropy for diagonal Gaussian
            log_det = torch.sum(torch.log(self.variance))
        else:
            # Entropy for full Gaussian
            log_det = torch.logdet(self.covariance)
            
        entropy = 0.5 * (self.dimension * (1.0 + np.log(2 * np.pi)) + log_det)
        
        return entropy.item()
    
    def mahalanobis_distance(self, x: torch.Tensor) -> torch.Tensor:
        """
        Compute the Mahalanobis distance from points to the distribution mean.
        
        Args:
            x: Points to evaluate [batch_size, dimension]
            
        Returns:
            Mahalanobis distances [batch_size]
        """
        if len(x.shape) == 1:
            # Single point
            x = x.unsqueeze(0)
            
        delta = x - self.mean
        
        if self.diagonal:
            # Efficient computation for diagonal covariance
            mahalanobis = torch.sqrt(torch.sum(delta * delta / self.variance, dim=1))
        else:
            # Full covariance computation
            mahalanobis = torch.sqrt(torch.sum(delta * torch.matmul(delta, self.precision), dim=1))
            
        return mahalanobis
    
    def confidence_region(self, confidence_level: float = 0.95) -> float:
        """
        Compute the radius of the confidence region for a given confidence level.
        
        Args:
            confidence_level: Confidence level (e.g., 0.95 for 95% confidence)
            
        Returns:
            Radius of the confidence region
        """
        # For a multivariate Gaussian, the squared Mahalanobis distance follows a chi-squared distribution
        from scipy import stats
        
        chi2_value = stats.chi2.ppf(confidence_level, df=self.dimension)
        radius = np.sqrt(chi2_value)
        
        return radius
    
    def to(self, device: str) -> 'GaussianDistribution':
        """
        Move the distribution to the specified device.
        
        Args:
            device: Target device
            
        Returns:
            Distribution on the target device
        """
        if self.diagonal:
            return GaussianDistribution(
                mean=self.mean.to(device),
                covariance=self.variance.to(device),
                diagonal=True
            )
        else:
            return GaussianDistribution(
                mean=self.mean.to(device),
                covariance=self.covariance.to(device),
                diagonal=False
            )


class BayesianLinearRegression:
    """
    Bayesian linear regression for modeling relationships in concept space.
    """
    
    def __init__(
        self, 
        input_dim: int, 
        output_dim: int, 
        alpha: float = 1.0, 
        beta: float = 1.0,
        device: str = 'cpu'
    ):
        """
        Initialize Bayesian linear regression.
        
        Args:
            input_dim: Input dimension
            output_dim: Output dimension
            alpha: Prior precision
            beta: Noise precision
            device: Device for computations
        """
        self.input_dim = input_dim
        self.output_dim = output_dim
        self.alpha = alpha
        self.beta = beta
        self.device = device
        
        # Initialize prior distribution
        self.w_mean = torch.zeros(output_dim, input_dim, device=device)
        self.w_precision = alpha * torch.eye(input_dim, device=device)
        self.w_covariance = (1.0 / alpha) * torch.eye(input_dim, device=device)
        
        self.fitted = False
        
    def fit(self, X: torch.Tensor, Y: torch.Tensor):
        """
        Fit the model to data.
        
        Args:
            X: Input features [n_samples, input_dim]
            Y: Target values [n_samples, output_dim]
        """
        n_samples = X.shape[0]
        
        # Compute posterior precision: prior precision + beta * X^T X
        posterior_precision = self.w_precision + self.beta * torch.matmul(X.T, X)
        
        # Compute posterior covariance using Cholesky decomposition for stability
        try:
            L = torch.linalg.cholesky(posterior_precision)
            posterior_covariance = torch.cholesky_inverse(L)
        except:
            # Fallback to direct inversion with ridge regularization
            ridge = torch.eye(self.input_dim, device=self.device) * 1e-6
            posterior_covariance = torch.inverse(posterior_precision + ridge)
            
        # Compute posterior mean
        temp = torch.matmul(X.T, Y)  # [input_dim, output_dim]
        posterior_mean = self.beta * torch.matmul(posterior_covariance, temp).T  # [output_dim, input_dim]
        
        # Update model parameters
        self.w_mean = posterior_mean
        self.w_covariance = posterior_covariance
        self.w_precision = posterior_precision
        
        self.fitted = True
        
    def predict(
        self, 
        X: torch.Tensor, 
        return_std: bool = False, 
        n_samples: int = 0
    ) -> Union[torch.Tensor, Tuple[torch.Tensor, torch.Tensor]]:
        """
        Make predictions for new inputs.
        
        Args:
            X: Input features [n_samples, input_dim]
            return_std: Whether to return standard deviations
            n_samples: Number of Monte Carlo samples (0 = use analytic mean)
            
        Returns:
            If return_std is False: Mean predictions [n_samples, output_dim]
            If return_std is True: Tuple of (mean predictions, std deviations)
        """
        if not self.fitted:
            raise RuntimeError("Model must be fitted before making predictions")
            
        if n_samples > 0:
            # Monte Carlo sampling for predictions
            predictions = []
            for _ in range(n_samples):
                # Sample from weight posterior
                weight_samples = []
                for i in range(self.output_dim):
                    weight_sample = torch.distributions.MultivariateNormal(
                        loc=self.w_mean[i],
                        covariance_matrix=self.w_covariance
                    ).sample()
                    weight_samples.append(weight_sample)
                    
                weight_samples = torch.stack(weight_samples)  # [output_dim, input_dim]
                
                # Make predictions with sampled weights
                pred = torch.matmul(X, weight_samples.T)  # [n_samples, output_dim]
                predictions.append(pred)
                
            # Stack all predictions
            all_predictions = torch.stack(predictions)  # [n_mc_samples, n_samples, output_dim]
            
            # Compute mean and std across Monte Carlo samples
            mean_pred = torch.mean(all_predictions, dim=0)
            std_pred = torch.std(all_predictions, dim=0)
            
        else:
            # Analytic mean prediction
            mean_pred = torch.matmul(X, self.w_mean.T)  # [n_samples, output_dim]
            
            if return_std:
                # Compute analytic standard deviation
                std_pred = torch.zeros_like(mean_pred)
                for i in range(X.shape[0]):
                    x_i = X[i].unsqueeze(0)  # [1, input_dim]
                    var_i = torch.matmul(torch.matmul(x_i, self.w_covariance), x_i.T).diagonal()
                    std_pred[i] = torch.sqrt(var_i + 1.0/self.beta)
            
        if return_std:
            return mean_pred, std_pred
        else:
            return mean_pred
    
    def predictive_distribution(
        self, 
        x: torch.Tensor
    ) -> GaussianDistribution:
        """
        Compute the predictive distribution for a single input.
        
        Args:
            x: Input feature vector [input_dim]
            
        Returns:
            Gaussian predictive distribution
        """
        if not self.fitted:
            raise RuntimeError("Model must be fitted before computing predictive distribution")
            
        # Reshape x if needed
        if len(x.shape) == 2 and x.shape[0] == 1:
            x = x.squeeze(0)
        
        # Compute mean prediction
        mean = torch.matmul(self.w_mean, x)  # [output_dim]
        
        # Compute predictive variance: x^T Σ x + 1/β
        predictive_var = torch.zeros(self.output_dim, device=self.device)
        for i in range(self.output_dim):
            w_var = torch.matmul(torch.matmul(x.unsqueeze(0), self.w_covariance), x.unsqueeze(1)).item()
            predictive_var[i] = w_var + 1.0 / self.beta
            
        # Create Gaussian distribution
        return GaussianDistribution(mean=mean, covariance=predictive_var, diagonal=True)
    
    def log_marginal_likelihood(self, X: torch.Tensor, Y: torch.Tensor) -> float:
        """
        Compute the log marginal likelihood log p(Y|X).
        
        Args:
            X: Input features [n_samples, input_dim]
            Y: Target values [n_samples, output_dim]
            
        Returns:
            Log marginal likelihood
        """
        n_samples = X.shape[0]
        
        # Compute posterior precision and covariance
        posterior_precision = self.w_precision + self.beta * torch.matmul(X.T, X)
        
        try:
            L = torch.linalg.cholesky(posterior_precision)
            posterior_covariance = torch.cholesky_inverse(L)
            log_det_posterior = -2 * torch.sum(torch.log(torch.diag(L)))
        except:
            ridge = torch.eye(self.input_dim, device=self.device) * 1e-6
            posterior_covariance = torch.inverse(posterior_precision + ridge)
            log_det_posterior = -torch.logdet(posterior_precision)
        
        # Compute log determinants
        log_det_prior = -torch.logdet(self.w_precision)
        
        # Compute data fit term
        Y_flat = Y.reshape(-1, self.output_dim)  # Ensure Y is [n_samples, output_dim]
        
        # Compute mismatch between predictions and targets
        mean_pred = torch.matmul(X, self.w_mean.T)  # [n_samples, output_dim]
        residuals = Y_flat - mean_pred
        data_fit = -0.5 * self.beta * torch.sum(residuals**2)
        
        # Compute log marginal likelihood
        lml = (data_fit 
               + 0.5 * (log_det_posterior - log_det_prior) 
               - 0.5 * n_samples * self.output_dim * np.log(2*np.pi) 
               + 0.5 * n_samples * self.output_dim * np.log(self.beta))
        
        return lml.item()
    
    def sample_functions(
        self, 
        X: torch.Tensor, 
        n_functions: int = 5
    ) -> torch.Tensor:
        """
        Sample functions from the posterior.
        
        Args:
            X: Input grid for function evaluation [n_points, input_dim]
            n_functions: Number of functions to sample
            
        Returns:
            Sampled function values [n_functions, n_points, output_dim]
        """
        if not self.fitted:
            raise RuntimeError("Model must be fitted before sampling functions")
            
        # Sample weights from posterior
        sampled_functions = []
        for _ in range(n_functions):
            # Sample weights for each output dimension
            weight_samples = []
            for i in range(self.output_dim):
                weight_sample = torch.distributions.MultivariateNormal(
                    loc=self.w_mean[i],
                    covariance_matrix=self.w_covariance
                ).sample()
                weight_samples.append(weight_sample)
                
            weight_samples = torch.stack(weight_samples)  # [output_dim, input_dim]
            
            # Evaluate function on grid
            function_values = torch.matmul(X, weight_samples.T)  # [n_points, output_dim]
            sampled_functions.append(function_values)
            
        return torch.stack(sampled_functions)
    
    def optimize_expected_improvement(
        self, 
        X_observed: torch.Tensor, 
        Y_observed: torch.Tensor,
        bounds: torch.Tensor,
        n_restarts: int = 10,
        maximize: bool = True
    ) -> Tuple[torch.Tensor, float]:
        """
        Find the input that maximizes expected improvement.
        
        Args:
            X_observed: Observed inputs [n_observed, input_dim]
            Y_observed: Observed outputs [n_observed, output_dim]
            bounds: Bounds for optimization [2, input_dim]
            n_restarts: Number of random restarts for optimization
            maximize: Whether to maximize (True) or minimize (False) the objective
            
        Returns:
            Tuple of (optimal input, expected improvement)
        """
        if not self.fitted:
            # Fit the model to observed data
            self.fit(X_observed, Y_observed)
            
        # Define expected improvement function
        def expected_improvement(x):
            x_tensor = torch.tensor(x, dtype=torch.float32, device=self.device).unsqueeze(0)
            mean, std = self.predict(x_tensor, return_std=True)
            
            # Get best observed value
            if maximize:
                f_best = torch.max(Y_observed)
            else:
                f_best = torch.min(Y_observed)
                mean = -mean  # Convert to maximization problem
                f_best = -f_best
            
            # Compute improvement
            improvement = mean - f_best
            
            # Compute expected improvement
            z = improvement / (std + 1e-6)
            ei = improvement * torch.distributions.Normal(0, 1).cdf(z) + std * torch.exp(torch.distributions.Normal(0, 1).log_prob(z))
            
            if not maximize:
                ei = -ei  # Convert back to minimization
                
            return ei.item()
        
        # Perform optimization with multiple restarts
        from scipy.optimize import minimize
        
        best_x = None
        best_ei = -float('inf')
        
        for _ in range(n_restarts):
            # Random starting point
            x0 = np.random.uniform(bounds[0].cpu().numpy(), bounds[1].cpu().numpy())
            
            # Run optimization
            result = minimize(
                lambda x: -expected_improvement(x),  # Negative for minimization
                x0,
                bounds=[(bounds[0][i].item(), bounds[1][i].item()) for i in range(self.input_dim)],
                method='L-BFGS-B'
            )
            
            if -result.fun > best_ei:
                best_ei = -result.fun
                best_x = result.x
        
        return torch.tensor(best_x, device=self.device), best_ei
    
    def to(self, device: str) -> 'BayesianLinearRegression':
        """
        Move the model to the specified device.
        
        Args:
            device: Target device
            
        Returns:
            Model on the target device
        """
        new_model = BayesianLinearRegression(
            input_dim=self.input_dim,
            output_dim=self.output_dim,
            alpha=self.alpha,
            beta=self.beta,
            device=device
        )
        
        # Copy parameters
        new_model.w_mean = self.w_mean.to(device)
        new_model.w_precision = self.w_precision.to(device)
        new_model.w_covariance = self.w_covariance.to(device)
        new_model.fitted = self.fitted
        
        return new_model


class EpistemicUncertainty:
    """
    Specialized methods for quantifying epistemic uncertainty (uncertainty due to limited knowledge).
    """
    
    def __init__(
        self, 
        thought_space_dim: int = DEFAULT_DIMENSION,
        device: str = 'cuda' if torch.cuda.is_available() else 'cpu'
    ):
        """
        Initialize epistemic uncertainty quantification.
        
        Args:
            thought_space_dim: Dimensionality of the thought latent space
            device: Device for computations
        """
        self.thought_space_dim = thought_space_dim
        self.device = device
        
        # Prior distribution over concept embeddings
        self.prior_mean = torch.zeros(thought_space_dim, device=device)
        self.prior_precision = DEFAULT_PRECISION * torch.eye(thought_space_dim, device=device)
        
        # Model for learning concept relations
        self.relation_model = None
        
    def estimate(
        self, 
        concept_distribution: GaussianDistribution
    ) -> float:
        """
        Estimate epistemic uncertainty for a concept.
        
        Args:
            concept_distribution: Gaussian distribution representing the concept
            
        Returns:
            Epistemic uncertainty score
        """
        # Epistemic uncertainty is captured by the variance of the distribution
        if concept_distribution.diagonal:
            epistemic = torch.sum(concept_distribution.variance).item()
        else:
            epistemic = torch.trace(concept_distribution.covariance).item()
            
        return epistemic
    
    def estimate_from_samples(
        self, 
        samples: torch.Tensor, 
        return_distribution: bool = False
    ) -> Union[float, Tuple[float, GaussianDistribution]]:
        """
        Estimate epistemic uncertainty from samples.
        
        Args:
            samples: Samples from a distribution [n_samples, dimension]
            return_distribution: Whether to return the fitted distribution
            
        Returns:
            Epistemic uncertainty score or tuple with distribution
        """
        # Fit a Gaussian to the samples
        mean = torch.mean(samples, dim=0)
        covariance = torch.cov(samples.T)
        
        # Create distribution
        distribution = GaussianDistribution(mean=mean, covariance=covariance, diagonal=False)
        
        # Compute uncertainty
        uncertainty = self.estimate(distribution)
        
        if return_distribution:
            return uncertainty, distribution
        else:
            return uncertainty
    
    def kl_from_prior(self, distribution: GaussianDistribution) -> float:
        """
        Compute KL divergence from the prior to quantify epistemic uncertainty.
        
        Args:
            distribution: Concept distribution
            
        Returns:
            KL divergence from prior
        """
        # Create prior distribution
        prior = GaussianDistribution(
            mean=self.prior_mean,
            precision=self.prior_precision,
            diagonal=False
        )
        
        # Compute KL divergence
        kl = distribution.kl_divergence(prior)
        
        return kl
    
    def mutual_information(
        self, 
        X: torch.Tensor, 
        model: BayesianLinearRegression, 
        n_samples: int = 100
    ) -> torch.Tensor:
        """
        Compute mutual information I(w, y|x) for estimating epistemic uncertainty.
        
        Args:
            X: Input points to evaluate [n_points, input_dim]
            model: Bayesian model
            n_samples: Number of MC samples
            
        Returns:
            Mutual information for each input [n_points]
        """
        # Get predictive mean and variance
        mean_pred, var_pred = model.predict(X, return_std=True)
        var_pred = var_pred**2  # Convert std to variance
        
        # Sample functions from posterior
        function_samples = model.sample_functions(X, n_functions=n_samples)  # [n_samples, n_points, output_dim]
        
        # Compute variance of the mean predictions across samples
        predictive_variance = torch.var(function_samples, dim=0)  # [n_points, output_dim]
        
        # Expected noise variance
        noise_variance = 1.0 / model.beta
        
        # Mutual information = total variance - noise variance
        mutual_info = torch.mean(predictive_variance, dim=1)
        
        return mutual_info
    
    def information_gain(
        self, 
        candidate_points: torch.Tensor,
        observed_points: torch.Tensor,
        observed_values: torch.Tensor,
        model: BayesianLinearRegression
    ) -> torch.Tensor:
        """
        Compute expected information gain for candidate points.
        
        Args:
            candidate_points: Candidate input points [n_candidates, input_dim]
            observed_points: Already observed inputs [n_observed, input_dim]
            observed_values: Observed outputs [n_observed, output_dim]
            model: Bayesian model
            
        Returns:
            Expected information gain for each candidate [n_candidates]
        """
        # Fit model to observed data
        model.fit(observed_points, observed_values)
        
        # Compute entropy of current posterior
        current_entropy = -model.log_marginal_likelihood(observed_points, observed_values)
        
        # Compute expected information gain for each candidate
        info_gains = []
        
        for i in range(candidate_points.shape[0]):
            x_i = candidate_points[i:i+1]  # [1, input_dim]
            
            # Get predictive distribution for this point
            mean, var = model.predict(x_i, return_std=True)
            var = var**2  # Convert std to variance
            
            # Sample potential observations from predictive distribution
            y_samples = torch.distributions.Normal(mean, torch.sqrt(var)).sample((10,))
            
            # Compute average entropy reduction
            entropy_reduction = 0.0
            for y_sample in y_samples:
                # Add this point to observed data
                new_X = torch.cat([observed_points, x_i], dim=0)
                new_Y = torch.cat([observed_values, y_sample.unsqueeze(0)], dim=0)
                
                # Compute new posterior entropy
                model.fit(new_X, new_Y)
                new_entropy = -model.log_marginal_likelihood(new_X, new_Y)
                
                # Information gain is reduction in entropy
                entropy_reduction += (current_entropy - new_entropy)
                
            # Average over samples
            info_gains.append(entropy_reduction / 10.0)
            
            # Reset model to original state
            model.fit(observed_points, observed_values)
            
        return torch.tensor(info_gains, device=self.device)
    
    def bald_score(
        self, 
        model: Callable, 
        x: torch.Tensor, 
        n_samples: int = 30
    ) -> torch.Tensor:
        """
        Compute BALD (Bayesian Active Learning by Disagreement) score for uncertainty estimation.
        
        Args:
            model: Function that returns MC dropout samples
            x: Input to evaluate [batch_size, input_dim]
            n_samples: Number of MC samples
            
        Returns:
            BALD scores [batch_size]
        """
        # Get samples from model
        samples = model(x, n_samples)  # [n_samples, batch_size, output_dim]
        
        # Compute mean prediction probability across samples
        mean_probs = torch.mean(torch.softmax(samples, dim=2), dim=0)  # [batch_size, output_dim]
        
        # Compute entropy of mean prediction
        entropy_mean = -torch.sum(mean_probs * torch.log(mean_probs + 1e-10), dim=1)  # [batch_size]
        
        # Compute mean entropy of individual predictions
        entropy_samples = -torch.sum(
            torch.softmax(samples, dim=2) * torch.log(torch.softmax(samples, dim=2) + 1e-10),
            dim=2
        )  # [n_samples, batch_size]
        mean_entropy = torch.mean(entropy_samples, dim=0)  # [batch_size]
        
        # BALD score = entropy_mean - mean_entropy
        bald = entropy_mean - mean_entropy
        
        return bald


class AleatoricUncertainty:
    """
    Specialized methods for quantifying aleatoric uncertainty (intrinsic data uncertainty).
    """
    
    def __init__(self, device: str = 'cuda' if torch.cuda.is_available() else 'cpu'):
        """
        Initialize aleatoric uncertainty quantification.
        
        Args:
            device: Device for computations
        """
        self.device = device
        
    def estimate_heteroscedastic(
        self, 
        model: Callable, 
        x: torch.Tensor
    ) -> torch.Tensor:
        """
        Estimate heteroscedastic aleatoric uncertainty from a model that predicts both mean and variance.
        
        Args:
            model: Function that returns mean and variance predictions
            x: Input to evaluate [batch_size, input_dim]
            
        Returns:
            Aleatoric uncertainty estimates [batch_size]
        """
        # Get mean and variance predictions
        mean, var = model(x)
        
        # Aleatoric uncertainty is directly predicted by the model
        return var
    
    def estimate_from_residuals(
        self, 
        y_pred: torch.Tensor, 
        y_true: torch.Tensor, 
        window_size: int = 10
    ) -> torch.Tensor:
        """
        Estimate aleatoric uncertainty from prediction residuals.
        
        Args:
            y_pred: Predicted values [n_samples, output_dim]
            y_true: True values [n_samples, output_dim]
            window_size: Window size for local variance estimation
            
        Returns:
            Aleatoric uncertainty estimates [n_samples, output_dim]
        """
        # Compute residuals
        residuals = y_true - y_pred  # [n_samples, output_dim]
        
        # Compute local variance using a sliding window
        n_samples = y_pred.shape[0]
        uncertainty = torch.zeros_like(y_pred)
        
        for i in range(n_samples):
            # Determine window boundaries
            start = max(0, i - window_size // 2)
            end = min(n_samples, i + window_size // 2)
            
            # Compute local variance of residuals
            local_residuals = residuals[start:end]
            uncertainty[i] = torch.var(local_residuals, dim=0)
            
        return uncertainty
    
    def calibrate_uncertainty(
        self, 
        model_uncertainties: torch.Tensor, 
        residuals: torch.Tensor
    ) -> Tuple[torch.Tensor, float, float]:
        """
        Calibrate model uncertainty estimates using observed residuals.
        
        Args:
            model_uncertainties: Uncertainty estimates from model [n_samples]
            residuals: Observed residuals [n_samples]
            
        Returns:
            Tuple of (calibrated uncertainties, scale, offset)
        """
        # Compute squared residuals
        squared_residuals = residuals**2
        
        # Learn calibration parameters (scale and offset)
        # Minimize: E[(scale*uncertainty + offset - squared_residual)^2]
        X = torch.stack([model_uncertainties, torch.ones_like(model_uncertainties)], dim=1)
        y = squared_residuals
        
        # Solve least squares problem
        beta, _ = torch.lstsq(y.unsqueeze(1), X)
        scale, offset = beta[0].item(), beta[1].item()
        
        # Apply calibration
        calibrated = scale * model_uncertainties + offset
        
        # Ensure positive uncertainties
        calibrated = torch.clamp(calibrated, min=1e-6)
        
        return calibrated, scale, offset
    
    def heteroscedastic_loss(
        self, 
        y_true: torch.Tensor, 
        y_pred: torch.Tensor, 
        y_var: torch.Tensor
    ) -> torch.Tensor:
        """
        Compute heteroscedastic loss that adapts to predicted uncertainty.
        
        Args:
            y_true: True values [batch_size, output_dim]
            y_pred: Predicted means [batch_size, output_dim]
            y_var: Predicted variances [batch_size, output_dim]
            
        Returns:
            Loss value
        """
        # Ensure minimum variance for numerical stability
        y_var = torch.clamp(y_var, min=1e-6)
        
        # Negative log likelihood of Gaussian
        loss = 0.5 * torch.log(y_var) + 0.5 * (y_true - y_pred)**2 / y_var
        
        return torch.mean(loss)
    
    def estimate_distribution_shift(
        self, 
        reference_data: torch.Tensor, 
        new_data: torch.Tensor
    ) -> float:
        """
        Estimate distribution shift between reference and new data.
        
        Args:
            reference_data: Reference data distribution [n_ref, dim]
            new_data: New data distribution [n_new, dim]
            
        Returns:
            Distribution shift score
        """
        # Compute means and covariances
        ref_mean = torch.mean(reference_data, dim=0)
        new_mean = torch.mean(new_data, dim=0)
        
        ref_cov = torch.cov(reference_data.T)
        new_cov = torch.cov(new_data.T)
        
        # Compute Frechet distance between distributions
        mean_diff = torch.sum((ref_mean - new_mean)**2)
        
        # Compute square root of product of covariances
        cov_prod_sqrt = torch.matrix_power(torch.matmul(torch.matmul(ref_cov, new_cov), ref_cov), 0.25)
        
        # Compute trace term
        trace_term = torch.trace(ref_cov + new_cov - 2 * cov_prod_sqrt)
        
        # Frechet distance
        frechet_distance = mean_diff + trace_term
        
        return frechet_distance.item()


class VariationalInference:
    """
    Implements variational inference methods for approximating complex posterior distributions.
    """
    
    def __init__(
        self, 
        latent_dim: int, 
        data_dim: int,
        hidden_dim: int = 256,
        device: str = 'cuda' if torch.cuda.is_available() else 'cpu'
    ):
        """
        Initialize variational inference.
        
        Args:
            latent_dim: Dimensionality of the latent space
            data_dim: Dimensionality of the data space
            hidden_dim: Dimensionality of hidden layers
            device: Device for computations
        """
        self.latent_dim = latent_dim
        self.data_dim = data_dim
        self.hidden_dim = hidden_dim
        self.device = device
        
        # Define the encoder network (approximates posterior q(z|x))
        self.encoder = nn.Sequential(
            nn.Linear(data_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU()
        ).to(device)
        
        # Mean and log variance outputs
        self.fc_mean = nn.Linear(hidden_dim, latent_dim).to(device)
        self.fc_logvar = nn.Linear(hidden_dim, latent_dim).to(device)
        
        # Define the decoder network (likelihood p(x|z))
        self.decoder = nn.Sequential(
            nn.Linear(latent_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, data_dim)
        ).to(device)
        
        # Define optimizer
        self.optimizer = torch.optim.Adam(
            list(self.encoder.parameters()) + 
            list(self.fc_mean.parameters()) + 
            list(self.fc_logvar.parameters()) + 
            list(self.decoder.parameters()),
            lr=1e-3
        )
        
    def encode(self, x: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Encode data to latent distribution parameters.
        
        Args:
            x: Input data [batch_size, data_dim]
            
        Returns:
            Tuple of (mean, log_variance) of the latent distribution
        """
        h = self.encoder(x)
        mean = self.fc_mean(h)
        log_var = self.fc_logvar(h)
        return mean, log_var
    
    def reparameterize(
        self, 
        mean: torch.Tensor, 
        log_var: torch.Tensor
    ) -> torch.Tensor:
        """
        Reparameterization trick: z = mean + std * eps
        
        Args:
            mean: Mean of the latent distribution [batch_size, latent_dim]
            log_var: Log variance of the latent distribution [batch_size, latent_dim]
            
        Returns:
            Sampled latent vectors [batch_size, latent_dim]
        """
        std = torch.exp(0.5 * log_var)
        eps = torch.randn_like(std)
        return mean + eps * std
    
    def decode(self, z: torch.Tensor) -> torch.Tensor:
        """
        Decode latent vectors to data space.
        
        Args:
            z: Latent vectors [batch_size, latent_dim]
            
        Returns:
            Reconstructed data [batch_size, data_dim]
        """
        return self.decoder(z)
    
    def forward(self, x: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        Forward pass through the VAE.
        
        Args:
            x: Input data [batch_size, data_dim]
            
        Returns:
            Tuple of (reconstruction, mean, log_variance)
        """
        mean, log_var = self.encode(x)
        z = self.reparameterize(mean, log_var)
        reconstruction = self.decode(z)
        return reconstruction, mean, log_var
    
    def loss_function(
        self, 
        recon_x: torch.Tensor, 
        x: torch.Tensor, 
        mean: torch.Tensor, 
        log_var: torch.Tensor, 
        beta: float = 1.0
    ) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        Compute VAE loss: reconstruction loss + beta * KL divergence
        
        Args:
            recon_x: Reconstructed data [batch_size, data_dim]
            x: Original data [batch_size, data_dim]
            mean: Mean of the latent distribution [batch_size, latent_dim]
            log_var: Log variance of the latent distribution [batch_size, latent_dim]
            beta: Weight of the KL divergence term
            
        Returns:
            Tuple of (total_loss, reconstruction_loss, kl_divergence)
        """
        # Reconstruction loss (mean squared error)
        recon_loss = F.mse_loss(recon_x, x, reduction='sum')
        
        # KL divergence
        kl_div = -0.5 * torch.sum(1 + log_var - mean.pow(2) - log_var.exp())
        
        # Total loss
        total_loss = recon_loss + beta * kl_div
        
        return total_loss, recon_loss, kl_div
    
    def train_epoch(
        self, 
        dataloader: torch.utils.data.DataLoader, 
        beta: float = 1.0
    ) -> Tuple[float, float, float]:
        """
        Train for one epoch.
        
        Args:
            dataloader: DataLoader for training data
            beta: Weight of the KL divergence term
            
        Returns:
            Tuple of (average total loss, average reconstruction loss, average KL divergence)
        """
        self.encoder.train()
        self.decoder.train()
        
        total_loss = 0
        total_recon_loss = 0
        total_kl_div = 0
        
        for data in dataloader:
            self.optimizer.zero_grad()
            
            # Move data to device
            if isinstance(data, list) or isinstance(data, tuple):
                x = data[0].to(self.device)
            else:
                x = data.to(self.device)
            
            # Forward pass
            recon_x, mean, log_var = self.forward(x)
            
            # Compute loss
            loss, recon_loss, kl_div = self.loss_function(recon_x, x, mean, log_var, beta)
            
            # Backward pass and optimize
            loss.backward()
            self.optimizer.step()
            
            # Accumulate losses
            total_loss += loss.item()
            total_recon_loss += recon_loss.item()
            total_kl_div += kl_div.item()
            
        # Compute averages
        avg_loss = total_loss / len(dataloader.dataset)
        avg_recon_loss = total_recon_loss / len(dataloader.dataset)
        avg_kl_div = total_kl_div / len(dataloader.dataset)
        
        return avg_loss, avg_recon_loss, avg_kl_div
    
    def fit(
        self, 
        dataloader: torch.utils.data.DataLoader, 
        num_epochs: int = 10, 
        beta: float = 1.0,
        beta_warmup: bool = False,
        verbose: bool = True
    ) -> List[Dict[str, float]]:
        """
        Fit the VAE to data.
        
        Args:
            dataloader: DataLoader for training data
            num_epochs: Number of training epochs
            beta: Weight of the KL divergence term
            beta_warmup: Whether to use beta warmup schedule
            verbose: Whether to print progress
            
        Returns:
            List of dictionaries with training metrics
        """
        training_history = []
        
        for epoch in range(num_epochs):
            # Beta warmup
            if beta_warmup:
                current_beta = beta * min(1.0, epoch / (num_epochs // 2))
            else:
                current_beta = beta
                
            # Train for one epoch
            avg_loss, avg_recon_loss, avg_kl_div = self.train_epoch(
                dataloader=dataloader, 
                beta=current_beta
            )
            
            # Record metrics
            metrics = {
                'epoch': epoch,
                'loss': avg_loss,
                'recon_loss': avg_recon_loss,
                'kl_div': avg_kl_div,
                'beta': current_beta
            }
            
            training_history.append(metrics)
            
            if verbose and (epoch + 1) % max(1, num_epochs // 10) == 0:
                print(f"Epoch {epoch+1}/{num_epochs}: loss={avg_loss:.4f}, recon_loss={avg_recon_loss:.4f}, kl_div={avg_kl_div:.4f}")
                
        return training_history
    
    def get_latent_representation(
        self, 
        x: torch.Tensor, 
        return_params: bool = False
    ) -> Union[torch.Tensor, Tuple[torch.Tensor, torch.Tensor, torch.Tensor]]:
        """
        Get latent representation for data.
        
        Args:
            x: Input data [batch_size, data_dim]
            return_params: Whether to return distribution parameters
            
        Returns:
            If return_params is False: Latent vectors [batch_size, latent_dim]
            If return_params is True: Tuple of (latent_vectors, mean, log_variance)
        """
        self.encoder.eval()
        with torch.no_grad():
            x = x.to(self.device)
            mean, log_var = self.encode(x)
            z = self.reparameterize(mean, log_var)
            
        if return_params:
            return z, mean, log_var
        else:
            return z
    
    def generate(
        self, 
        z: Optional[torch.Tensor] = None, 
        num_samples: int = 1
    ) -> torch.Tensor:
        """
        Generate data from latent vectors.
        
        Args:
            z: Optional latent vectors [batch_size, latent_dim]
            num_samples: Number of samples to generate (if z is None)
            
        Returns:
            Generated data [batch_size, data_dim]
        """
        self.decoder.eval()
        with torch.no_grad():
            if z is None:
                z = torch.randn(num_samples, self.latent_dim, device=self.device)
            else:
                z = z.to(self.device)
                
            samples = self.decode(z)
            
        return samples
    
    def reconstruct(
        self, 
        x: torch.Tensor, 
        num_samples: int = 1
    ) -> torch.Tensor:
        """
        Reconstruct data through the VAE.
        
        Args:
            x: Input data [batch_size, data_dim]
            num_samples: Number of samples to generate per input
            
        Returns:
            Reconstructed data [batch_size, num_samples, data_dim]
        """
        self.encoder.eval()
        self.decoder.eval()
        
        reconstructions = []
        with torch.no_grad():
            x = x.to(self.device)
            mean, log_var = self.encode(x)
            
            for _ in range(num_samples):
                z = self.reparameterize(mean, log_var)
                recon_x = self.decode(z)
                reconstructions.append(recon_x)
                
        return torch.stack(reconstructions, dim=1)
    
    def approximate_posterior(
        self, 
        x: torch.Tensor
    ) -> GaussianDistribution:
        """
        Get approximate posterior distribution for data.
        
        Args:
            x: Input data [batch_size, data_dim]
            
        Returns:
            Gaussian distribution representing the approximate posterior
        """
        self.encoder.eval()
        with torch.no_grad():
            x = x.to(self.device)
            mean, log_var = self.encode(x)
            var = torch.exp(log_var)
            
        # Create Gaussian distribution
        return GaussianDistribution(mean=mean.squeeze(0), covariance=var.squeeze(0), diagonal=True)
    
    def save(self, path: str):
        """Save the model to a file."""
        torch.save({
            'encoder_state': self.encoder.state_dict(),
            'fc_mean_state': self.fc_mean.state_dict(),
            'fc_logvar_state': self.fc_logvar.state_dict(),
            'decoder_state': self.decoder.state_dict(),
            'latent_dim': self.latent_dim,
            'data_dim': self.data_dim,
            'hidden_dim': self.hidden_dim
        }, path)
        
    @classmethod
    def load(
        cls, 
        path: str, 
        device: str = 'cuda' if torch.cuda.is_available() else 'cpu'
    ) -> 'VariationalInference':
        """
        Load a model from a file.
        
        Args:
            path: File path
            device: Device for computations
            
        Returns:
            Loaded model
        """
        checkpoint = torch.load(path, map_location=device)
        model = cls(
            latent_dim=checkpoint['latent_dim'],
            data_dim=checkpoint['data_dim'],
            hidden_dim=checkpoint['hidden_dim'],
            device=device
        )
        
        model.encoder.load_state_dict(checkpoint['encoder_state'])
        model.fc_mean.load_state_dict(checkpoint['fc_mean_state'])
        model.fc_logvar.load_state_dict(checkpoint['fc_logvar_state'])
        model.decoder.load_state_dict(checkpoint['decoder_state'])
        
        return model


class UncertaintyDecomposition:
    """
    Decomposes uncertainty into its epistemic and aleatoric components.
    """
    
    def __init__(
        self, 
        thought_space_dim: int = DEFAULT_DIMENSION,
        device: str = 'cuda' if torch.cuda.is_available() else 'cpu'
    ):
        """
        Initialize uncertainty decomposition.
        
        Args:
            thought_space_dim: Dimensionality of the thought latent space
            device: Device for computations
        """
        self.thought_space_dim = thought_space_dim
        self.device = device
        
        # Create uncertainty components
        self.epistemic = EpistemicUncertainty(
            thought_space_dim=thought_space_dim,
            device=device
        )
        
        self.aleatoric = AleatoricUncertainty(device=device)
        
    def decompose(
        self, 
        model_fn: Callable,
        inputs: torch.Tensor,
        num_samples: int = 20,
        return_distributions: bool = False
    ) -> Union[Dict[str, torch.Tensor], Dict[str, Union[torch.Tensor, GaussianDistribution]]]:
        """
        Decompose total uncertainty into epistemic and aleatoric components.
        
        Args:
            model_fn: Function that returns samples from model given inputs and n_samples
            inputs: Input data [batch_size, input_dim]
            num_samples: Number of Monte Carlo samples
            return_distributions: Whether to return fitted distributions
            
        Returns:
            Dictionary with uncertainty components
        """
        # Get predictive samples from the model
        predictive_samples = model_fn(inputs, num_samples)  # [num_samples, batch_size, output_dim]
        
        # Compute mean prediction (across samples)
        mean_prediction = torch.mean(predictive_samples, dim=0)  # [batch_size, output_dim]
        
        # Compute total variance (across samples)
        total_variance = torch.var(predictive_samples, dim=0)  # [batch_size, output_dim]
        
        # Compute aleatoric uncertainty (mean of sample variances)
        sample_variances = []
        for i in range(num_samples):
            sample = predictive_samples[i]  # [batch_size, output_dim]
            sample_var = model_fn.predict_variance(inputs)  # Model's predicted variance
            sample_variances.append(sample_var)
            
        aleatoric_uncertainty = torch.mean(torch.stack(sample_variances), dim=0)  # [batch_size, output_dim]
        
        # Compute epistemic uncertainty (variance of means)
        sample_means = []
        for i in range(num_samples):
            sample = predictive_samples[i]  # [batch_size, output_dim]
            sample_means.append(sample)
            
        epistemic_uncertainty = torch.var(torch.stack(sample_means), dim=0)  # [batch_size, output_dim]
        
        # Verify decomposition: total = epistemic + aleatoric
        # They may not exactly match due to finite sampling
        
        # Create result dictionary
        result = {
            'total': total_variance,
            'epistemic': epistemic_uncertainty,
            'aleatoric': aleatoric_uncertainty,
            'mean': mean_prediction
        }
        
        if return_distributions:
            # Create distributions for each component
            batch_size = inputs.shape[0]
            distributions = {}
            
            for i in range(batch_size):
                mean_i = mean_prediction[i]
                epistemic_i = epistemic_uncertainty[i]
                aleatoric_i = aleatoric_uncertainty[i]
                
                distributions[i] = {
                    'predictive': GaussianDistribution(
                        mean=mean_i,
                        covariance=total_variance[i],
                        diagonal=True
                    ),
                    'epistemic': GaussianDistribution(
                        mean=mean_i,
                        covariance=epistemic_i,
                        diagonal=True
                    ),
                    'aleatoric': GaussianDistribution(
                        mean=mean_i,
                        covariance=aleatoric_i,
                        diagonal=True
                    )
                }
                
            result['distributions'] = distributions
            
        return result
    
    def reliability_diagram(
        self, 
        confidence: torch.Tensor,
        accuracy: torch.Tensor,
        num_bins: int = 10
    ) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        Compute reliability diagram for calibration assessment.
        
        Args:
            confidence: Confidence scores [n_samples]
            accuracy: Accuracy indicators (0 or 1) [n_samples]
            num_bins: Number of bins for diagram
            
        Returns:
            Tuple of (bin_confidence, bin_accuracy, bin_counts)
        """
        # Ensure tensors are on the correct device
        confidence = confidence.to(self.device)
        accuracy = accuracy.to(self.device)
        
        # Create bins
        bin_size = 1.0 / num_bins
        bin_indices = torch.floor(confidence / bin_size).long()
        bin_indices = torch.clamp(bin_indices, max=num_bins-1)
        
        # Initialize bin statistics
        bin_confidence = torch.zeros(num_bins, device=self.device)
        bin_accuracy = torch.zeros(num_bins, device=self.device)
        bin_counts = torch.zeros(num_bins, device=self.device)
        
        # Accumulate statistics
        for i in range(confidence.shape[0]):
            bin_idx = bin_indices[i]
            bin_confidence[bin_idx] += confidence[i]
            bin_accuracy[bin_idx] += accuracy[i]
            bin_counts[bin_idx] += 1
            
        # Compute averages
        valid_bins = bin_counts > 0
        bin_confidence[valid_bins] /= bin_counts[valid_bins]
        bin_accuracy[valid_bins] /= bin_counts[valid_bins]
        
        return bin_confidence, bin_accuracy, bin_counts
    
    def expected_calibration_error(
        self, 
        confidence: torch.Tensor,
        accuracy: torch.Tensor,
        num_bins: int = 10
    ) -> float:
        """
        Compute Expected Calibration Error (ECE).
        
        Args:
            confidence: Confidence scores [n_samples]
            accuracy: Accuracy indicators (0 or 1) [n_samples]
            num_bins: Number of bins for calculation
            
        Returns:
            Expected Calibration Error
        """
        # Compute reliability diagram
        bin_confidence, bin_accuracy, bin_counts = self.reliability_diagram(
            confidence=confidence,
            accuracy=accuracy,
            num_bins=num_bins
        )
        
        # Compute ECE
        n_samples = torch.sum(bin_counts)
        ece = torch.sum(bin_counts * torch.abs(bin_accuracy - bin_confidence)) / n_samples
        
        return ece.item()
    
    def predictive_entropy(
        self, 
        probs: torch.Tensor
    ) -> torch.Tensor:
        """
        Compute predictive entropy from probability distributions.
        
        Args:
            probs: Probability distributions [batch_size, num_classes]
            
        Returns:
            Entropy values [batch_size]
        """
        # Ensure minimum probability for numerical stability
        probs = torch.clamp(probs, min=1e-10)
        
        # Compute entropy
        entropy = -torch.sum(probs * torch.log(probs), dim=1)
        
        return entropy
    
    def mutual_information(
        self, 
        probs_samples: torch.Tensor
    ) -> torch.Tensor:
        """
        Compute mutual information between predictions and model parameters.
        
        Args:
            probs_samples: Probability samples from model [num_samples, batch_size, num_classes]
            
        Returns:
            Mutual information values [batch_size]
        """
        # Compute mean probabilities across samples
        mean_probs = torch.mean(probs_samples, dim=0)  # [batch_size, num_classes]
        
        # Compute entropy of mean probabilities
        entropy_mean = self.predictive_entropy(mean_probs)  # [batch_size]
        
        # Compute mean entropy across samples
        entropy_samples = -torch.sum(
            probs_samples * torch.log(torch.clamp(probs_samples, min=1e-10)),
            dim=2
        )  # [num_samples, batch_size]
        
        mean_entropy = torch.mean(entropy_samples, dim=0)  # [batch_size]
        
        # Mutual information = entropy of mean - mean of entropies
        mutual_info = entropy_mean - mean_entropy
        
        return mutual_info


def estimate_uncertainty_bounds(
    samples: torch.Tensor,
    confidence_level: float = 0.95,
    method: str = 'percentile'
) -> Tuple[torch.Tensor, torch.Tensor]:
    """
    Estimate uncertainty bounds from samples.
    
    Args:
        samples: Samples from a distribution [num_samples, dimension]
        confidence_level: Confidence level (e.g., 0.95 for 95% confidence)
        method: Method for bound estimation ('percentile', 'bootstrap', or 'gaussian')
        
    Returns:
        Tuple of (lower_bounds, upper_bounds)
    """
    if method == 'percentile':
        # Simple percentile method
        alpha = (1 - confidence_level) / 2
        lower_idx = int(alpha * samples.shape[0])
        upper_idx = int((1 - alpha) * samples.shape[0])
        
        # Sort samples
        sorted_samples, _ = torch.sort(samples, dim=0)
        
        # Get bounds
        lower_bounds = sorted_samples[lower_idx]
        upper_bounds = sorted_samples[upper_idx]
        
    elif method == 'bootstrap':
        # Bootstrap method
        n_bootstrap = 1000
        n_samples = samples.shape[0]
        
        # Generate bootstrap samples
        bootstrap_means = []
        for _ in range(n_bootstrap):
            # Sample with replacement
            indices = torch.randint(0, n_samples, (n_samples,), device=samples.device)
            bootstrap_sample = samples[indices]
            bootstrap_means.append(torch.mean(bootstrap_sample, dim=0))
            
        bootstrap_means = torch.stack(bootstrap_means)
        
        # Compute percentiles of bootstrap means
        alpha = (1 - confidence_level) / 2
        lower_idx = int(alpha * n_bootstrap)
        upper_idx = int((1 - alpha) * n_bootstrap)
        
        # Sort bootstrap means
        sorted_means, _ = torch.sort(bootstrap_means, dim=0)
        
        # Get bounds
        lower_bounds = sorted_means[lower_idx]
        upper_bounds = sorted_means[upper_idx]
        
    elif method == 'gaussian':
        # Gaussian approximation
        import scipy.stats as stats
        
        # Compute mean and standard deviation
        mean = torch.mean(samples, dim=0)
        std = torch.std(samples, dim=0)
        
        # Compute z-score for confidence level
        z = stats.norm.ppf(1 - (1 - confidence_level) / 2)
        
        # Compute bounds
        lower_bounds = mean - z * std
        upper_bounds = mean + z * std
        
    else:
        raise ValueError(f"Unknown method: {method}")
        
    return lower_bounds, upper_bounds


class BayesianUncertaintyQuantification:
    """
    Main class for Bayesian uncertainty quantification in the ULTRA system.
    
    This class integrates all uncertainty components into a comprehensive framework
    for reasoning under uncertainty in the thought latent space.
    """
    
    def __init__(
        self, 
        thought_latent_space: Any = None,
        thought_space_dim: int = DEFAULT_DIMENSION,
        num_mc_samples: int = DEFAULT_MC_SAMPLES,
        latent_precision: float = DEFAULT_PRECISION,
        device: str = 'cuda' if torch.cuda.is_available() else 'cpu'
    ):
        """
        Initialize Bayesian Uncertainty Quantification.
        
        Args:
            thought_latent_space: Optional ThoughtLatentSpace object
            thought_space_dim: Dimensionality of the thought latent space
            num_mc_samples: Number of Monte Carlo samples for uncertainty estimation
            latent_precision: Precision parameter for latent distributions
            device: Device for computations
        """
        self.thought_latent_space = thought_latent_space
        self.thought_space_dim = thought_space_dim
        self.num_mc_samples = num_mc_samples
        self.latent_precision = latent_precision
        self.device = device
        
        # Dictionary to store belief distributions over concepts
        self.beliefs = {}
        
        # Create components
        self.epistemic = EpistemicUncertainty(
            thought_space_dim=thought_space_dim,
            device=device
        )
        
        self.aleatoric = AleatoricUncertainty(device=device)
        
        self.decomposition = UncertaintyDecomposition(
            thought_space_dim=thought_space_dim,
            device=device
        )
        
        logger.info(f"Initialized BayesianUncertaintyQuantification with dimension={thought_space_dim}")
    
    def initialize_belief(
        self, 
        concept_name: str, 
        mean: Optional[torch.Tensor] = None,
        variance: Optional[torch.Tensor] = None
    ) -> GaussianDistribution:
        """
        Initialize a belief distribution for a concept.
        
        Args:
            concept_name: Name identifier for the concept
            mean: Optional mean vector (defaults to zero)
            variance: Optional variance vector (defaults to ones)
            
        Returns:
            Gaussian distribution representing initial belief
        """
        if mean is None:
            mean = torch.zeros(self.thought_space_dim, device=self.device)
        else:
            mean = mean.to(self.device)
            
        if variance is None:
            variance = torch.ones(self.thought_space_dim, device=self.device)
        else:
            variance = variance.to(self.device)
            
        # Create belief distribution
        belief = GaussianDistribution(mean=mean, covariance=variance, diagonal=True)
        
        # Store belief
        self.beliefs[concept_name] = belief
        
        return belief
    
    def update_belief(
        self, 
        concept_name: str, 
        evidence_mean: torch.Tensor, 
        evidence_var: torch.Tensor
    ) -> GaussianDistribution:
        """
        Update a belief distribution with new evidence.
        
        Args:
            concept_name: Name identifier for the concept
            evidence_mean: Mean of evidence distribution
            evidence_var: Variance of evidence distribution
            
        Returns:
            Updated belief distribution
        """
        # Get current belief
        if concept_name not in self.beliefs:
            # Initialize with standard prior
            current_belief = self.initialize_belief(concept_name)
        else:
            current_belief = self.beliefs[concept_name]
            
        # Ensure inputs are on the right device
        evidence_mean = evidence_mean.to(self.device)
        evidence_var = evidence_var.to(self.device)
        
        if current_belief.diagonal:
            # Simple update for diagonal case
            current_var = current_belief.variance
            current_mean = current_belief.mean
            
            # Compute precision-weighted update
            current_precision = 1.0 / current_var
            evidence_precision = 1.0 / evidence_var
            posterior_precision = current_precision + evidence_precision
            posterior_var = 1.0 / posterior_precision
            
            posterior_mean = posterior_var * (
                current_precision * current_mean + evidence_precision * evidence_mean
            )
            
            # Create updated belief
            updated_belief = GaussianDistribution(
                mean=posterior_mean,
                covariance=posterior_var,
                diagonal=True
            )
            
        else:
            # Full matrix update using precision form
            current_cov = current_belief.covariance_matrix
            current_prec = current_belief.precision_matrix
            current_mean = current_belief.mean
            
            # Convert evidence to full matrices
            if len(evidence_var.shape) == 1:
                evidence_prec = torch.diag(1.0 / evidence_var)
            else:
                # Already a full matrix
                evidence_prec = torch.inverse(evidence_var)
                
            # Compute posterior precision and mean
            posterior_prec = current_prec + evidence_prec
            
            # Compute posterior covariance using Cholesky decomposition for stability
            try:
                L = torch.linalg.cholesky(posterior_prec)
                posterior_cov = torch.cholesky_inverse(L)
            except:
                # Fallback to direct inversion with ridge regularization
                ridge = torch.eye(self.thought_space_dim, device=self.device) * 1e-6
                posterior_cov = torch.inverse(posterior_prec + ridge)
                
            posterior_mean = torch.matmul(
                posterior_cov,
                torch.matmul(current_prec, current_mean) + 
                torch.matmul(evidence_prec, evidence_mean)
            )
            
            # Create updated belief
            updated_belief = GaussianDistribution(
                mean=posterior_mean,
                covariance=posterior_cov,
                diagonal=False
            )
            
        # Store updated belief
        self.beliefs[concept_name] = updated_belief
        
        return updated_belief
    
    def get_belief(self, concept_name: str) -> Optional[GaussianDistribution]:
        """
        Get the current belief distribution for a concept.
        
        Args:
            concept_name: Name identifier for the concept
            
        Returns:
            Belief distribution if it exists, None otherwise
        """
        return self.beliefs.get(concept_name)
    
    def sample_from_belief(
        self, 
        concept_name: str, 
        n_samples: int = 1
    ) -> Optional[torch.Tensor]:
        """
        Sample from the belief distribution for a concept.
        
        Args:
            concept_name: Name identifier for the concept
            n_samples: Number of samples to draw
            
        Returns:
            Samples from belief distribution, or None if belief doesn't exist
        """
        belief = self.get_belief(concept_name)
        if belief is None:
            return None
            
        return belief.sample(n_samples)
    
    def get_uncertainty(self, concept_name: str) -> Optional[float]:
        """
        Compute uncertainty for a concept as trace of covariance matrix.
        
        Args:
            concept_name: Name identifier for the concept
            
        Returns:
            Uncertainty score, or None if belief doesn't exist
        """
        belief = self.get_belief(concept_name)
        if belief is None:
            return None
            
        if belief.diagonal:
            uncertainty = torch.sum(belief.variance).item()
        else:
            uncertainty = torch.trace(belief.covariance).item()
            
        return uncertainty
    
    def quantify_reasoning_uncertainty(
        self, 
        reasoning_path: List[torch.Tensor]
    ) -> Dict[str, torch.Tensor]:
        """
        Quantify uncertainty in a reasoning path.
        
        Args:
            reasoning_path: List of concept embeddings forming a reasoning path
            
        Returns:
            Dictionary with different uncertainty measures
        """
        # Convert list to tensor if needed
        if isinstance(reasoning_path, list):
            reasoning_path = torch.stack([t.to(self.device) for t in reasoning_path])
            
        # Ensure tensor is on the correct device
        reasoning_path = reasoning_path.to(self.device)
        
        # Number of steps in reasoning path
        n_steps = reasoning_path.shape[0]
        
        # Compute step-wise uncertainties
        step_uncertainties = torch.zeros(n_steps, device=self.device)
        for i in range(n_steps):
            # Monte Carlo sampling for uncertainty estimation
            samples = []
            for _ in range(self.num_mc_samples):
                # Add noise proportional to step index (more uncertainty in later steps)
                noise_scale = 1e-3 * (i + 1)
                noisy_vector = reasoning_path[i] + torch.randn_like(reasoning_path[i]) * noise_scale
                samples.append(noisy_vector)
                
            samples = torch.stack(samples)
            
            # Compute variance across samples
            step_uncertainties[i] = torch.mean(torch.var(samples, dim=0))
        
        # Compute cumulative uncertainty (increases with each step)
        cumulative_uncertainty = torch.cumsum(step_uncertainties, dim=0)
        
        # Compute transition uncertainties (uncertainty between steps)
        transition_uncertainties = torch.zeros(n_steps - 1, device=self.device)
        for i in range(n_steps - 1):
            # Compute uncertainty of transition from step i to i+1
            step_i = reasoning_path[i]
            step_i_plus_1 = reasoning_path[i+1]
            
            # Monte Carlo sampling for transition
            transition_samples = []
            for _ in range(self.num_mc_samples):
                # Sample intermediate points
                alpha = torch.rand(1, device=self.device).item()
                noisy_transition = alpha * step_i + (1 - alpha) * step_i_plus_1
                noisy_transition += torch.randn_like(noisy_transition) * 1e-3
                transition_samples.append(noisy_transition)
                
            transition_samples = torch.stack(transition_samples)
            
            # Compute variance across samples
            transition_uncertainties[i] = torch.mean(torch.var(transition_samples, dim=0))
            
        # Compute overall path uncertainty
        path_mean = torch.mean(reasoning_path, dim=0)
        path_var = torch.var(reasoning_path, dim=0)
        overall_uncertainty = torch.mean(path_var)
        
        # Compute uncertainty decomposition
        # - Epistemic: Uncertainty due to limited knowledge
        # - Aleatoric: Inherent uncertainty in the reasoning process
        
        # Use increasing noise for aleatoric uncertainty (later steps more uncertain)
        step_weights = torch.arange(1, n_steps + 1, device=self.device) / n_steps
        aleatoric_uncertainty = torch.sum(step_uncertainties * step_weights) / torch.sum(step_weights)
        
        # Epistemic uncertainty is related to the variance of the whole path
        epistemic_uncertainty = overall_uncertainty - aleatoric_uncertainty
        
        # Ensure non-negative uncertainties
        epistemic_uncertainty = max(0.0, epistemic_uncertainty)
        
        # Return all uncertainty measures
        return {
            'step_uncertainties': step_uncertainties,
            'cumulative_uncertainty': cumulative_uncertainty,
            'transition_uncertainties': transition_uncertainties,
            'overall_uncertainty': overall_uncertainty,
            'epistemic_uncertainty': epistemic_uncertainty,
            'aleatoric_uncertainty': aleatoric_uncertainty
        }
    
    def quantify_concept_uncertainty(
        self, 
        concept: torch.Tensor,
        num_samples: int = None,
        return_distribution: bool = False
    ) -> Union[Dict[str, float], Tuple[Dict[str, float], GaussianDistribution]]:
        """
        Quantify uncertainty for a concept embedding.
        
        Args:
            concept: Concept embedding
            num_samples: Number of MC samples (defaults to self.num_mc_samples)
            return_distribution: Whether to return fitted distribution
            
        Returns:
            Dictionary with uncertainty measures or tuple with distribution
        """
        if num_samples is None:
            num_samples = self.num_mc_samples
            
        # Ensure concept is on the right device
        concept = concept.to(self.device)
        
        # If concept is batched, use first vector
        if len(concept.shape) > 1 and concept.shape[0] > 1:
            concept = concept[0]
            
        # Ensure concept is 1D
        concept = concept.squeeze()
        
        # Generate perturbed samples
        samples = []
        for _ in range(num_samples):
            # Add small amount of noise
            noise = torch.randn_like(concept) * 1e-2
            perturbed = concept + noise
            samples.append(perturbed)
            
        samples = torch.stack(samples)
        
        # Fit a Gaussian to the samples
        mean = torch.mean(samples, dim=0)
        covariance = torch.cov(samples.T)
        
        # Create distribution
        distribution = GaussianDistribution(mean=mean, covariance=covariance, diagonal=False)
        
        # Compute different uncertainty measures
        
        # Total uncertainty (trace of covariance)
        total_uncertainty = torch.trace(covariance).item()
        
        # Entropy of distribution
        entropy = distribution.entropy()
        
        # Confidence (inverse of uncertainty)
        confidence = 1.0 / (1.0 + total_uncertainty)
        
        # Compute uncertainty measures
        result = {
            'total_uncertainty': total_uncertainty,
            'entropy': entropy,
            'confidence': confidence
        }
        
        if return_distribution:
            return result, distribution
        else:
            return result
    
    def concept_confidence_region(
        self, 
        concept: torch.Tensor,
        confidence_level: float = 0.95,
        num_samples: int = None
    ) -> Tuple[float, torch.Tensor]:
        """
        Compute confidence region for a concept.
        
        Args:
            concept: Concept embedding
            confidence_level: Confidence level (e.g., 0.95 for 95% confidence)
            num_samples: Number of MC samples (defaults to self.num_mc_samples)
            
        Returns:
            Tuple of (radius, center) of confidence region
        """
        # Quantify uncertainty and get distribution
        result, distribution = self.quantify_concept_uncertainty(
            concept=concept,
            num_samples=num_samples,
            return_distribution=True
        )
        
        # Compute confidence region
        radius = distribution.confidence_region(confidence_level)
        center = distribution.mean
        
        return radius, center
    
    def compare_concepts_uncertainty(
        self, 
        concept_a: torch.Tensor,
        concept_b: torch.Tensor,
        num_samples: int = None
    ) -> Dict[str, float]:
        """
        Compare uncertainty between two concepts.
        
        Args:
            concept_a: First concept embedding
            concept_b: Second concept embedding
            num_samples: Number of MC samples
            
        Returns:
            Dictionary with comparison results
        """
        # Quantify uncertainty for both concepts
        uncertainty_a = self.quantify_concept_uncertainty(concept_a, num_samples)
        uncertainty_b = self.quantify_concept_uncertainty(concept_b, num_samples)
        
        # Compute difference in uncertainty
        uncertainty_diff = uncertainty_a['total_uncertainty'] - uncertainty_b['total_uncertainty']
        
        # Compute relative uncertainty
        if uncertainty_b['total_uncertainty'] > 0:
            relative_uncertainty = uncertainty_a['total_uncertainty'] / uncertainty_b['total_uncertainty']
        else:
            relative_uncertainty = float('inf')
            
        # Determine which concept is more uncertain
        more_uncertain = 'A' if uncertainty_diff > 0 else 'B'
        
        # Compute confidence in comparison
        confidence_diff = abs(uncertainty_diff) / (uncertainty_a['total_uncertainty'] + uncertainty_b['total_uncertainty'])
        
        return {
            'uncertainty_a': uncertainty_a['total_uncertainty'],
            'uncertainty_b': uncertainty_b['total_uncertainty'],
            'uncertainty_diff': uncertainty_diff,
            'relative_uncertainty': relative_uncertainty,
            'more_uncertain': more_uncertain,
            'confidence_diff': confidence_diff
        }
    
    def quantify_relation_uncertainty(
        self, 
        source: torch.Tensor,
        target: torch.Tensor,
        num_samples: int = None
    ) -> Dict[str, float]:
        """
        Quantify uncertainty in a relation between concepts.
        
        Args:
            source: Source concept embedding
            target: Target concept embedding
            num_samples: Number of MC samples
            
        Returns:
            Dictionary with uncertainty measures
        """
        if num_samples is None:
            num_samples = self.num_mc_samples
            
        # Ensure vectors are on the right device
        source = source.to(self.device)
        target = target.to(self.device)
        
        # Compute relation vector
        relation = target - source
        
        # Generate perturbed samples of relation
        relation_samples = []
        for _ in range(num_samples):
            # Add noise to both source and target
            source_noise = torch.randn_like(source) * 1e-2
            target_noise = torch.randn_like(target) * 1e-2
            
            perturbed_source = source + source_noise
            perturbed_target = target + target_noise
            
            perturbed_relation = perturbed_target - perturbed_source
            relation_samples.append(perturbed_relation)
            
        relation_samples = torch.stack(relation_samples)
        
        # Compute statistics
        mean_relation = torch.mean(relation_samples, dim=0)
        var_relation = torch.var(relation_samples, dim=0)
        
        # Compute total uncertainty (trace of covariance)
        total_uncertainty = torch.sum(var_relation).item()
        
        # Compute relation consistency (normalized dot product with mean)
        consistency_scores = []
        for sample in relation_samples:
            dot_product = torch.dot(sample, mean_relation)
            norm_product = torch.norm(sample) * torch.norm(mean_relation)
            consistency = dot_product / (norm_product + 1e-8)
            consistency_scores.append(consistency.item())
            
        mean_consistency = np.mean(consistency_scores)
        var_consistency = np.var(consistency_scores)
        
        return {
            'total_uncertainty': total_uncertainty,
            'mean_consistency': mean_consistency,
            'var_consistency': var_consistency,
            'relation_norm': torch.norm(relation).item(),
            'mean_relation_norm': torch.norm(mean_relation).item()
        }
    
    def most_certain_concept(
        self, 
        concepts: List[torch.Tensor],
        return_index: bool = False
    ) -> Union[torch.Tensor, Tuple[torch.Tensor, int]]:
        """
        Find the concept with lowest uncertainty.
        
        Args:
            concepts: List of concept embeddings
            return_index: Whether to return the index
            
        Returns:
            Most certain concept or tuple with index
        """
        if not concepts:
            raise ValueError("Empty concepts list")
            
        # Compute uncertainty for each concept
        uncertainties = []
        for concept in concepts:
            uncertainty = self.quantify_concept_uncertainty(concept)
            uncertainties.append(uncertainty['total_uncertainty'])
            
        # Find index of most certain concept
        min_idx = np.argmin(uncertainties)
        
        if return_index:
            return concepts[min_idx], min_idx
        else:
            return concepts[min_idx]
    
    def save_beliefs(self, path: str):
        """Save belief distributions to a file."""
        torch.save(self.beliefs, path)
        logger.info(f"Saved {len(self.beliefs)} belief distributions to {path}")
        
    def load_beliefs(self, path: str):
        """Load belief distributions from a file."""
        self.beliefs = torch.load(path, map_location=self.device)
        logger.info(f"Loaded {len(self.beliefs)} belief distributions from {path}")
        
    def clear_beliefs(self):
        """Clear all stored belief distributions."""
        self.beliefs = {}
        logger.info("Cleared all belief distributions")
    
    def to(self, device: str) -> 'BayesianUncertaintyQuantification':
        """
        Move the entire module to the specified device.
        
        Args:
            device: Target device
            
        Returns:
            Self for chaining
        """
        self.device = device
        
        # Move beliefs to the new device
        new_beliefs = {}
        for name, belief in self.beliefs.items():
            new_beliefs[name] = belief.to(device)
            
        self.beliefs = new_beliefs
        
        # Move components to the new device
        if hasattr(self, 'epistemic'):
            self.epistemic.device = device
            
        if hasattr(self, 'aleatoric'):
            self.aleatoric.device = device
            
        if hasattr(self, 'decomposition'):
            self.decomposition.device = device
            
        logger.info(f"Moved BayesianUncertaintyQuantification to device: {device}")
        
        return self


# Initialize module variables
__version__ = '0.1.0'