#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ULTRA: Ultimate Learning & Thought Reasoning Architecture
Conceptual Diffusion Process Implementation

This module adapts diffusion models to operate in latent spaces that represent concepts
rather than raw data, enabling exploration of conceptual spaces and creative reasoning.

The implementation follows the mathematical formalism of diffusion models (<PERSON> et al., 2020)
but applies it to abstract concept representations in high-dimensional spaces.

Mathematical Foundation:
- Forward diffusion: q(z_t | z_{t-1}) = N(z_t; sqrt(1 - β_t) z_{t-1}, β_t I)
- In terms of z_0: q(z_t | z_0) = N(z_t; sqrt(α_t) z_0, (1 - α_t) I)
- Reverse process: p_θ(z_{t-1} | z_t) = N(z_{t-1}; μ_θ(z_t, t), Σ_θ(z_t, t))

Author: ULTRA Development Team
Version: 1.0.0
License: MIT
"""

import math
import logging
import os
import time
import pickle
from typing import Dict, List, Tuple, Union, Optional, Callable, Any, NamedTuple
from dataclasses import dataclass, field
from enum import Enum, auto
from pathlib import Path
import warnings

import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.optim import Adam, AdamW, SGD
from torch.optim.lr_scheduler import CosineAnnealingLR, ExponentialLR, ReduceLROnPlateau
from torch.utils.data import DataLoader, Dataset, TensorDataset
from torch.distributions import MultivariateNormal, Normal, Categorical, Dirichlet
from torch.distributions.kl import kl_divergence
import torch.distributed as dist
from torch.nn.parallel import DistributedDataParallel as DDP

# Scientific computing
import scipy
from scipy import optimize, stats, special
from scipy.linalg import solve, inv, det, cholesky
from scipy.sparse import csr_matrix, diags

# Configure module logger
logger = logging.getLogger(__name__)

# Mathematical constants
NUMERICAL_STABILITY_EPS = 1e-12
MAX_GRADIENT_NORM = 1.0
DEFAULT_LEARNING_RATE = 1e-4

# ============================================================================
# Utility Classes and Data Structures
# ============================================================================

@dataclass
class DiffusionConfig:
    """Configuration for diffusion models."""
    concept_dim: int = 512
    num_timesteps: int = 1000
    beta_schedule: str = 'cosine'  # 'linear', 'cosine', 'sigmoid', 'quadratic'
    beta_start: float = 1e-4
    beta_end: float = 0.02
    time_embedding_dim: int = 512
    hidden_dim: int = 2048
    num_layers: int = 6
    dropout: float = 0.1
    use_layer_norm: bool = True
    attention_heads: int = 8
    device: str = 'cuda' if torch.cuda.is_available() else 'cpu'
    precision: str = 'fp32'  # 'fp16', 'fp32', 'bf16'
    compile_model: bool = False

@dataclass
class ThoughtSpaceConfig:
    """Configuration for thought latent space."""
    thought_space_dim: int = 1024
    hierarchical_levels: int = 4
    semantic_dim: int = 256
    relational_dim: int = 128
    compositional_dim: int = 64
    use_attention: bool = True
    normalize_embeddings: bool = True
    distance_metric: str = 'cosine'  # 'euclidean', 'cosine', 'manhattan', 'mahalanobis'

class NoiseScheduleType(Enum):
    """Types of noise schedules for diffusion."""
    LINEAR = "linear"
    COSINE = "cosine"
    SIGMOID = "sigmoid"
    QUADRATIC = "quadratic"
    EXPONENTIAL = "exponential"

class ReasoningMode(Enum):
    """Different reasoning modes."""
    FORWARD = "forward"
    REVERSE = "reverse"
    BIDIRECTIONAL = "bidirectional"
    GOAL_DIRECTED = "goal_directed"
    CONSTRAINT_GUIDED = "constraint_guided"

# ============================================================================
# Mathematical Utilities for Diffusion
# ============================================================================

class DiffusionMath:
    """Mathematical utilities for diffusion processes."""
    
    @staticmethod
    def create_noise_schedule(
        schedule_type: str,
        num_timesteps: int,
        beta_start: float = 1e-4,
        beta_end: float = 0.02,
        s: float = 0.008  # For cosine schedule
    ) -> torch.Tensor:
        """
        Create various noise schedules for the forward diffusion process.
        
        Args:
            schedule_type: Type of beta schedule
            num_timesteps: Number of diffusion steps
            beta_start: Starting beta value
            beta_end: Ending beta value
            s: Offset parameter for cosine schedule
            
        Returns:
            Tensor of beta values for each timestep
        """
        if schedule_type == 'linear':
            betas = torch.linspace(beta_start, beta_end, num_timesteps, dtype=torch.float32)
            
        elif schedule_type == 'quadratic':
            betas = torch.linspace(beta_start**0.5, beta_end**0.5, num_timesteps, dtype=torch.float32) ** 2
            
        elif schedule_type == 'cosine':
            steps = num_timesteps + 1
            x = torch.linspace(0, num_timesteps, steps, dtype=torch.float32)
            alphas_cumprod = torch.cos((x / num_timesteps + s) / (1 + s) * math.pi * 0.5) ** 2
            alphas_cumprod = alphas_cumprod / alphas_cumprod[0]
            betas = 1 - (alphas_cumprod[1:] / alphas_cumprod[:-1])
            betas = torch.clamp(betas, 0, 0.999)
            
        elif schedule_type == "sigmoid":
            betas = torch.linspace(-6, 6, num_timesteps, dtype=torch.float32)
            betas = torch.sigmoid(betas) * (beta_end - beta_start) + beta_start
            
        elif schedule_type == "exponential":
            # Exponential decay schedule
            gamma = -math.log(beta_start / beta_end) / num_timesteps
            timesteps = torch.arange(num_timesteps, dtype=torch.float32)
            betas = beta_start * torch.exp(gamma * timesteps)
            
        else:
            raise ValueError(f"Unknown beta schedule: {schedule_type}")
        
        return betas

    @staticmethod
    def compute_diffusion_constants(betas: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        Compute all necessary constants for diffusion process.
        
        Args:
            betas: Beta schedule
            
        Returns:
            Dictionary containing all diffusion constants
        """
        alphas = 1.0 - betas
        alphas_cumprod = torch.cumprod(alphas, dim=0)
        alphas_cumprod_prev = F.pad(alphas_cumprod[:-1], (1, 0), value=1.0)
        
        sqrt_alphas_cumprod = torch.sqrt(alphas_cumprod)
        sqrt_one_minus_alphas_cumprod = torch.sqrt(1.0 - alphas_cumprod)
        sqrt_recip_alphas = torch.sqrt(1.0 / alphas)
        
        # Posterior variance for reverse process
        posterior_variance = betas * (1.0 - alphas_cumprod_prev) / (1.0 - alphas_cumprod)
        
        # Coefficients for posterior mean
        posterior_mean_coef1 = betas * torch.sqrt(alphas_cumprod_prev) / (1.0 - alphas_cumprod)
        posterior_mean_coef2 = (1.0 - alphas_cumprod_prev) * torch.sqrt(alphas) / (1.0 - alphas_cumprod)
        
        return {
            'betas': betas,
            'alphas': alphas,
            'alphas_cumprod': alphas_cumprod,
            'alphas_cumprod_prev': alphas_cumprod_prev,
            'sqrt_alphas_cumprod': sqrt_alphas_cumprod,
            'sqrt_one_minus_alphas_cumprod': sqrt_one_minus_alphas_cumprod,
            'sqrt_recip_alphas': sqrt_recip_alphas,
            'posterior_variance': posterior_variance,
            'posterior_mean_coef1': posterior_mean_coef1,
            'posterior_mean_coef2': posterior_mean_coef2,
        }

    @staticmethod
    def q_sample(x_start: torch.Tensor, t: torch.Tensor, noise: torch.Tensor,
                sqrt_alphas_cumprod: torch.Tensor, 
                sqrt_one_minus_alphas_cumprod: torch.Tensor) -> torch.Tensor:
        """
        Sample from q(x_t | x_0) - forward diffusion process.
        
        Args:
            x_start: Clean data
            t: Timesteps
            noise: Random noise
            sqrt_alphas_cumprod: Precomputed constants
            sqrt_one_minus_alphas_cumprod: Precomputed constants
            
        Returns:
            Noisy samples at timestep t
        """
        return (
            sqrt_alphas_cumprod[t].view(-1, 1) * x_start +
            sqrt_one_minus_alphas_cumprod[t].view(-1, 1) * noise
        )

    @staticmethod
    def predict_start_from_noise(x_t: torch.Tensor, t: torch.Tensor, noise: torch.Tensor,
                                sqrt_alphas_cumprod: torch.Tensor,
                                sqrt_one_minus_alphas_cumprod: torch.Tensor) -> torch.Tensor:
        """
        Predict x_0 from x_t and predicted noise.
        
        Formula: x_0 = (x_t - sqrt(1-α_t) * ε) / sqrt(α_t)
        """
        return (
            x_t - sqrt_one_minus_alphas_cumprod[t].view(-1, 1) * noise
        ) / sqrt_alphas_cumprod[t].view(-1, 1)

# ============================================================================
# Sinusoidal Time Embedding
# ============================================================================

class SinusoidalPositionEmbeddings(nn.Module):
    """
    Embeds diffusion timesteps using sinusoidal positional embeddings,
    similar to the positional encodings in the Transformer architecture.
    """
    def __init__(self, dim: int, max_period: int = 10000):
        super().__init__()
        self.dim = dim
        self.max_period = max_period

    def forward(self, time: torch.Tensor) -> torch.Tensor:
        """
        Compute sinusoidal embeddings for timesteps.
        
        Args:
            time: Tensor of diffusion timesteps [batch_size]
            
        Returns:
            Time embeddings [batch_size, dim]
        """
        half_dim = self.dim // 2
        embeddings = math.log(self.max_period) / (half_dim - 1)
        embeddings = torch.exp(torch.arange(half_dim, device=time.device) * -embeddings)
        embeddings = time[:, None].float() * embeddings[None, :]
        embeddings = torch.cat((torch.sin(embeddings), torch.cos(embeddings)), dim=-1)
        
        # Handle odd dimensions
        if self.dim % 2 == 1:
            embeddings = F.pad(embeddings, (0, 1, 0, 0))
            
        return embeddings

# ============================================================================
# Advanced Neural Network Components
# ============================================================================

class ResidualBlock(nn.Module):
    """Residual block with time conditioning and normalization."""
    
    def __init__(self, dim: int, time_emb_dim: int, dropout: float = 0.1):
        super().__init__()
        self.norm1 = nn.LayerNorm(dim)
        self.linear1 = nn.Linear(dim, dim * 4)
        self.time_emb_proj = nn.Linear(time_emb_dim, dim * 4)
        self.activation = nn.SiLU()
        self.dropout = nn.Dropout(dropout)
        self.norm2 = nn.LayerNorm(dim * 4)
        self.linear2 = nn.Linear(dim * 4, dim)
        
    def forward(self, x: torch.Tensor, time_emb: torch.Tensor) -> torch.Tensor:
        """
        Args:
            x: Input tensor [batch_size, dim]
            time_emb: Time embedding [batch_size, time_emb_dim]
            
        Returns:
            Output tensor [batch_size, dim]
        """
        residual = x
        
        # First transformation
        x = self.norm1(x)
        x = self.linear1(x)
        
        # Add time conditioning
        time_proj = self.time_emb_proj(time_emb)
        x = x + time_proj
        
        x = self.activation(x)
        x = self.norm2(x)
        x = self.dropout(x)
        x = self.linear2(x)
        
        return x + residual

class AttentionBlock(nn.Module):
    """Multi-head attention block with time conditioning."""
    
    def __init__(self, dim: int, num_heads: int = 8, dropout: float = 0.1):
        super().__init__()
        self.attention = nn.MultiheadAttention(
            embed_dim=dim,
            num_heads=num_heads,
            dropout=dropout,
            batch_first=True
        )
        self.norm = nn.LayerNorm(dim)
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, x: torch.Tensor, time_emb: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        Args:
            x: Input tensor [batch_size, seq_len, dim] or [batch_size, dim]
            time_emb: Optional time embedding
            
        Returns:
            Output tensor with same shape as input
        """
        # Handle 2D input by adding sequence dimension
        if x.dim() == 2:
            x = x.unsqueeze(1)
            squeeze_output = True
        else:
            squeeze_output = False
            
        residual = x
        x = self.norm(x)
        
        # Self-attention
        attn_output, _ = self.attention(x, x, x)
        x = residual + self.dropout(attn_output)
        
        if squeeze_output:
            x = x.squeeze(1)
            
        return x

# ============================================================================
# Concept Noise Predictor (Core Neural Network)
# ============================================================================

class ConceptNoisePredictor(nn.Module):
    """
    Neural network that predicts the noise component in diffused concepts.
    This is the core learnable component of the diffusion model.
    """
    def __init__(
        self, 
        concept_dim: int, 
        time_embedding_dim: int = None, 
        hidden_dim: int = None,
        num_layers: int = 6,
        num_attention_layers: int = 2,
        attention_heads: int = 8,
        dropout: float = 0.1,
        use_layer_norm: bool = True,
        use_attention: bool = True
    ):
        super().__init__()
        
        self.concept_dim = concept_dim
        self.time_embedding_dim = time_embedding_dim or concept_dim
        self.hidden_dim = hidden_dim or (concept_dim * 4)
        self.use_attention = use_attention
        
        # Time embeddings
        self.time_mlp = nn.Sequential(
            SinusoidalPositionEmbeddings(self.time_embedding_dim),
            nn.Linear(self.time_embedding_dim, self.hidden_dim),
            nn.SiLU(),
            nn.Linear(self.hidden_dim, self.hidden_dim),
        )
        
        # Initial projection
        self.input_proj = nn.Linear(concept_dim, self.hidden_dim)
        
        # Residual layers
        self.layers = nn.ModuleList()
        for i in range(num_layers):
            self.layers.append(ResidualBlock(
                dim=self.hidden_dim,
                time_emb_dim=self.hidden_dim,
                dropout=dropout
            ))
        
        # Attention layers (if enabled)
        if use_attention:
            self.attention_layers = nn.ModuleList()
            for i in range(num_attention_layers):
                self.attention_layers.append(AttentionBlock(
                    dim=self.hidden_dim,
                    num_heads=attention_heads,
                    dropout=dropout
                ))
        
        # Output projection
        self.output_norm = nn.LayerNorm(self.hidden_dim) if use_layer_norm else nn.Identity()
        self.output_proj = nn.Linear(self.hidden_dim, concept_dim)
        
        # Initialize weights
        self._init_weights()
        
        logger.info(f"Initialized ConceptNoisePredictor: concept_dim={concept_dim}, "
                   f"hidden_dim={self.hidden_dim}, layers={num_layers}, attention={use_attention}")
        
    def _init_weights(self):
        """Initialize model weights for better convergence."""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.kaiming_normal_(module.weight, nonlinearity='relu')
                if module.bias is not None:
                    nn.init.zeros_(module.bias)
            elif isinstance(module, nn.LayerNorm):
                nn.init.ones_(module.weight)
                nn.init.zeros_(module.bias)
    
    def forward(self, x: torch.Tensor, t: torch.Tensor) -> torch.Tensor:
        """
        Predict the noise component in diffused concepts.
        
        Args:
            x: Diffused concept tensor [batch_size, concept_dim]
            t: Timestep tensor [batch_size]
            
        Returns:
            Predicted noise tensor [batch_size, concept_dim]
        """
        # Get time embeddings
        t_emb = self.time_mlp(t)
        
        # Process input
        h = self.input_proj(x)
        
        # Apply residual layers with time conditioning
        for layer in self.layers:
            h = layer(h, t_emb)
        
        # Apply attention layers if enabled
        if self.use_attention:
            for attn_layer in self.attention_layers:
                h = attn_layer(h, t_emb)
        
        # Output projection
        h = self.output_norm(h)
        noise_pred = self.output_proj(h)
        
        return noise_pred

# ============================================================================
# Thought Latent Space
# ============================================================================

class ThoughtLatentSpace(nn.Module):
    """
    Implements a hierarchical latent space for representing abstract thoughts and concepts.
    
    The space has the following properties:
    - Hierarchical structure with multiple abstraction levels
    - Semantic continuity (similar concepts are close)
    - Compositional properties (concepts can be combined)
    - Relational structure (relationships preserved as vector operations)
    """
    
    def __init__(self, config: ThoughtSpaceConfig):
        super().__init__()
        
        self.config = config
        self.thought_space_dim = config.thought_space_dim
        self.hierarchical_levels = config.hierarchical_levels
        self.semantic_dim = config.semantic_dim
        self.relational_dim = config.relational_dim
        self.compositional_dim = config.compositional_dim
        
        # Compute level dimensions
        self.level_dims = []
        remaining_dim = self.thought_space_dim
        for i in range(self.hierarchical_levels):
            level_dim = remaining_dim // (self.hierarchical_levels - i)
            self.level_dims.append(level_dim)
            remaining_dim -= level_dim
        
        # Hierarchical encoders for each level
        self.level_encoders = nn.ModuleList()
        for i, level_dim in enumerate(self.level_dims):
            encoder = nn.Sequential(
                nn.Linear(self.semantic_dim, level_dim * 2),
                nn.LayerNorm(level_dim * 2),
                nn.SiLU(),
                nn.Dropout(0.1),
                nn.Linear(level_dim * 2, level_dim)
            )
            self.level_encoders.append(encoder)
        
        # Concept encoder
        self.concept_encoder = nn.Sequential(
            nn.Linear(self.thought_space_dim, self.semantic_dim * 2),
            nn.LayerNorm(self.semantic_dim * 2),
            nn.SiLU(),
            nn.Linear(self.semantic_dim * 2, self.semantic_dim)
        )
        
        # Relational encoder
        self.relational_encoder = nn.Sequential(
            nn.Linear(self.thought_space_dim * 2, self.relational_dim * 2),
            nn.LayerNorm(self.relational_dim * 2),
            nn.SiLU(),
            nn.Linear(self.relational_dim * 2, self.relational_dim)
        )
        
        # Compositional encoder
        self.compositional_encoder = nn.Sequential(
            nn.Linear(self.thought_space_dim * 3, self.compositional_dim * 2),
            nn.LayerNorm(self.compositional_dim * 2),
            nn.SiLU(),
            nn.Linear(self.compositional_dim * 2, self.compositional_dim)
        )
        
        # Distance metrics
        self.distance_functions = {
            'euclidean': self._euclidean_distance,
            'cosine': self._cosine_distance,
            'manhattan': self._manhattan_distance,
            'mahalanobis': self._mahalanobis_distance
        }
        
        # Learnable covariance matrix for Mahalanobis distance
        self.register_parameter(
            'covariance_matrix',
            nn.Parameter(torch.eye(self.thought_space_dim))
        )
        
        # Initialize weights
        self.apply(self._init_weights)
        
    def _init_weights(self, module):
        """Initialize weights using Xavier initialization."""
        if isinstance(module, nn.Linear):
            torch.nn.init.xavier_uniform_(module.weight)
            if module.bias is not None:
                torch.nn.init.zeros_(module.bias)
        elif isinstance(module, nn.LayerNorm):
            torch.nn.init.ones_(module.weight)
            torch.nn.init.zeros_(module.bias)
    
    def encode_hierarchical(self, concept: torch.Tensor) -> List[torch.Tensor]:
        """
        Encode a concept into hierarchical representations.
        
        Args:
            concept: Input concept tensor [batch_size, thought_space_dim]
            
        Returns:
            List of hierarchical encodings for each level
        """
        semantic_repr = self.concept_encoder(concept)
        
        hierarchical_encodings = []
        for encoder in self.level_encoders:
            level_encoding = encoder(semantic_repr)
            hierarchical_encodings.append(level_encoding)
            
        return hierarchical_encodings
    
    def encode_relational(self, concept_a: torch.Tensor, concept_b: torch.Tensor) -> torch.Tensor:
        """
        Encode the relationship between two concepts.
        
        Args:
            concept_a: First concept [batch_size, thought_space_dim]
            concept_b: Second concept [batch_size, thought_space_dim]
            
        Returns:
            Relational encoding [batch_size, relational_dim]
        """
        combined = torch.cat([concept_a, concept_b], dim=-1)
        return self.relational_encoder(combined)
    
    def encode_compositional(self, concepts: List[torch.Tensor], 
                           relations: Optional[List[torch.Tensor]] = None) -> torch.Tensor:
        """
        Encode compositional combinations of concepts.
        
        Args:
            concepts: List of concept tensors to compose
            relations: Optional list of relational encodings
            
        Returns:
            Compositional encoding [batch_size, compositional_dim]
        """
        if len(concepts) < 2:
            raise ValueError("Need at least 2 concepts for composition")
        
        # Simple composition: concatenate first 3 concepts (pad if needed)
        if len(concepts) >= 3:
            composed = torch.cat(concepts[:3], dim=-1)
        elif len(concepts) == 2:
            # Pad with zeros for third concept
            zero_concept = torch.zeros_like(concepts[0])
            composed = torch.cat([concepts[0], concepts[1], zero_concept], dim=-1)
        
        return self.compositional_encoder(composed)
    
    def compute_distance(self, concept_a: torch.Tensor, concept_b: torch.Tensor,
                        metric: str = None) -> torch.Tensor:
        """
        Compute distance between concepts in the thought space.
        
        Args:
            concept_a: First concept
            concept_b: Second concept
            metric: Distance metric to use
            
        Returns:
            Distance tensor
        """
        metric = metric or self.config.distance_metric
        
        if metric not in self.distance_functions:
            raise ValueError(f"Unknown distance metric: {metric}")
            
        return self.distance_functions[metric](concept_a, concept_b)
    
    def _euclidean_distance(self, a: torch.Tensor, b: torch.Tensor) -> torch.Tensor:
        """Compute Euclidean distance."""
        return torch.norm(a - b, dim=-1)
    
    def _cosine_distance(self, a: torch.Tensor, b: torch.Tensor) -> torch.Tensor:
        """Compute cosine distance."""
        return 1 - F.cosine_similarity(a, b, dim=-1)
    
    def _manhattan_distance(self, a: torch.Tensor, b: torch.Tensor) -> torch.Tensor:
        """Compute Manhattan distance."""
        return torch.sum(torch.abs(a - b), dim=-1)
    
    def _mahalanobis_distance(self, a: torch.Tensor, b: torch.Tensor) -> torch.Tensor:
        """Compute Mahalanobis distance with learned covariance."""
        diff = a - b
        # Use learnable covariance matrix (simplified to diagonal for efficiency)
        cov_diag = torch.diagonal(self.covariance_matrix)
        return torch.sqrt(torch.sum(diff ** 2 / cov_diag.unsqueeze(0), dim=-1))
    
    def interpolate(self, concept_a: torch.Tensor, concept_b: torch.Tensor,
                   alpha: float, method: str = 'linear') -> torch.Tensor:
        """
        Interpolate between two concepts in the thought space.
        
        Args:
            concept_a: Starting concept
            concept_b: Ending concept
            alpha: Interpolation factor (0 = concept_a, 1 = concept_b)
            method: Interpolation method ('linear', 'spherical')
            
        Returns:
            Interpolated concept
        """
        if method == 'linear':
            return (1 - alpha) * concept_a + alpha * concept_b
        elif method == 'spherical':
            # Spherical linear interpolation (slerp)
            a_norm = F.normalize(concept_a, dim=-1)
            b_norm = F.normalize(concept_b, dim=-1)
            
            dot_product = torch.sum(a_norm * b_norm, dim=-1, keepdim=True)
            dot_product = torch.clamp(dot_product, -0.99999, 0.99999)
            
            omega = torch.acos(dot_product)
            sin_omega = torch.sin(omega)
            
            # Handle case where vectors are nearly parallel
            mask = sin_omega.abs() < 1e-6
            
            result = torch.where(
                mask,
                (1 - alpha) * concept_a + alpha * concept_b,  # Linear fallback
                (torch.sin((1 - alpha) * omega) / sin_omega) * concept_a + 
                (torch.sin(alpha * omega) / sin_omega) * concept_b
            )
            
            return result
        else:
            raise ValueError(f"Unknown interpolation method: {method}")

# ============================================================================
# Main Conceptual Diffusion Class
# ============================================================================

class ConceptualDiffusion(nn.Module):
    """
    Applies diffusion processes to concept representations, enabling exploration
    of concept spaces through noise addition and denoising.
    
    This class provides methods for:
    - Training the diffusion model
    - Adding noise to concepts (forward process)
    - Denoising concepts (reverse process)
    - Sampling new concepts
    - Interpolating between concepts
    - Guided sampling with external functions
    """
    
    def __init__(
        self, 
        config: DiffusionConfig,
        noise_predictor: Optional[nn.Module] = None,
        thought_latent_space: Optional[ThoughtLatentSpace] = None
    ):
        super().__init__()
        
        self.config = config
        self.concept_dim = config.concept_dim
        self.num_timesteps = config.num_timesteps
        self.device = config.device
        
        # Create noise schedule
        betas = DiffusionMath.create_noise_schedule(
            config.beta_schedule,
            config.num_timesteps,
            config.beta_start,
            config.beta_end
        ).to(self.device)
        
        # Compute and register diffusion constants
        constants = DiffusionMath.compute_diffusion_constants(betas)
        for name, value in constants.items():
            self.register_buffer(name, value)
        
        # Create or use provided noise predictor
        if noise_predictor is not None:
            self.noise_predictor = noise_predictor.to(self.device)
        else:
            self.noise_predictor = ConceptNoisePredictor(
                concept_dim=config.concept_dim,
                time_embedding_dim=config.time_embedding_dim,
                hidden_dim=config.hidden_dim,
                num_layers=config.num_layers,
                dropout=config.dropout,
                use_layer_norm=config.use_layer_norm,
                attention_heads=config.attention_heads
            ).to(self.device)
        
        # Thought latent space (optional)
        self.thought_latent_space = thought_latent_space
        
        # Loss function
        self.loss_fn = nn.MSELoss(reduction='mean')
        
        # Model compilation (optional)
        if config.compile_model and hasattr(torch, 'compile'):
            self.noise_predictor = torch.compile(self.noise_predictor)
        
        logger.info(f"Initialized ConceptualDiffusion with {config.num_timesteps} timesteps "
                   f"using {config.beta_schedule} schedule on {self.device}")
    
    def forward_process(
        self, 
        x_0: torch.Tensor, 
        t: Union[int, torch.Tensor], 
        noise: Optional[torch.Tensor] = None
    ) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Run the forward diffusion process (adding noise).
        
        Args:
            x_0: Initial concept tensor [batch_size, concept_dim]
            t: Timestep(s) to diffuse to
            noise: Optional noise to add (if None, random noise is generated)
            
        Returns:
            Tuple of (diffused tensor, noise added)
        """
        batch_size = x_0.shape[0]
        
        # Handle t as int or tensor
        if isinstance(t, int):
            t = torch.ones(batch_size, device=self.device, dtype=torch.long) * t
        
        # Generate noise if not provided
        if noise is None:
            noise = torch.randn_like(x_0)
        
        # Apply forward diffusion
        x_t = DiffusionMath.q_sample(
            x_0, t, noise,
            self.sqrt_alphas_cumprod,
            self.sqrt_one_minus_alphas_cumprod
        )
        
        return x_t, noise
    
    def reverse_process_step(
        self, 
        x_t: torch.Tensor, 
        t: Union[int, torch.Tensor], 
        guidance_scale: float = 0.0,
        guidance_fn: Optional[Callable] = None,
        clip_denoised: bool = True
    ) -> torch.Tensor:
        """
        Run a single step of the reverse diffusion process (denoising).
        
        Args:
            x_t: Diffused concept tensor at time t [batch_size, concept_dim]
            t: Current timestep(s)
            guidance_scale: Scale factor for guidance (0.0 = no guidance)
            guidance_fn: Optional function for guided sampling
            clip_denoised: Whether to clip the denoised output
            
        Returns:
            Tensor containing previous step x_(t-1)
        """
        batch_size = x_t.shape[0]
        
        # Handle t as int or tensor
        if isinstance(t, int):
            t = torch.ones(batch_size, device=self.device, dtype=torch.long) * t
        
        # Predict noise
        with torch.no_grad():
            predicted_noise = self.noise_predictor(x_t, t)
        
        # Apply guidance if provided
        if guidance_scale > 0 and guidance_fn is not None:
            with torch.enable_grad():
                x_t_var = x_t.clone().detach().requires_grad_(True)
                guidance = guidance_fn(x_t_var, t)
                grad = torch.autograd.grad(guidance.sum(), x_t_var)[0]
                predicted_noise = predicted_noise - guidance_scale * grad
        
        # Predict x_0 from noise
        x_0_pred = DiffusionMath.predict_start_from_noise(
            x_t, t, predicted_noise,
            self.sqrt_alphas_cumprod,
            self.sqrt_one_minus_alphas_cumprod
        )
        
        # Clip predicted x_0 if requested
        if clip_denoised:
            x_0_pred = torch.clamp(x_0_pred, -1.0, 1.0)
        
        # Handle t=0 separately (final step)
        if (t == 0).all():
            return x_0_pred
        
        # Compute posterior mean
        posterior_mean = (
            self.posterior_mean_coef1[t].view(-1, 1) * x_0_pred +
            self.posterior_mean_coef2[t].view(-1, 1) * x_t
        )
        
        # Sample from posterior
        posterior_variance = self.posterior_variance[t].view(-1, 1)
        noise = torch.randn_like(x_t)
        
        return posterior_mean + torch.sqrt(posterior_variance) * noise
    
    def sample(
        self, 
        batch_size: int = 1, 
        shape: Optional[Tuple[int, ...]] = None,
        steps: Optional[int] = None, 
        x_T: Optional[torch.Tensor] = None,
        guidance_scale: float = 0.0,
        guidance_fn: Optional[Callable] = None,
        temperature: float = 1.0,
        return_trajectory: bool = False,
        show_progress: bool = False
    ) -> Union[torch.Tensor, List[torch.Tensor]]:
        """
        Sample from the diffusion model by running the reverse process from random noise.
        
        Args:
            batch_size: Number of concepts to sample
            shape: Optional shape override for samples
            steps: Number of denoising steps (default: num_timesteps)
            x_T: Optional starting point (if None, random noise is used)
            guidance_scale: Scale factor for guidance (0.0 = no guidance)
            guidance_fn: Optional function for guided sampling
            temperature: Temperature for sampling (controls randomness)
            return_trajectory: Whether to return the full trajectory of samples
            show_progress: Whether to show a progress bar
            
        Returns:
            Tensor containing sampled concepts [batch_size, concept_dim] or
            List of tensors if return_trajectory=True
        """
        steps = steps or self.num_timesteps
        
        # Determine sample shape
        if shape is not None:
            sample_shape = (batch_size,) + shape
        else:
            sample_shape = (batch_size, self.concept_dim)
        
        # Start from random noise or provided x_T
        if x_T is None:
            x_t = torch.randn(sample_shape, device=self.device) * temperature
        else:
            x_t = x_T.to(self.device)
            batch_size = x_t.shape[0]
        
        # Set up timesteps (can use fewer steps for faster sampling)
        if steps < self.num_timesteps:
            # DDIM-style sampling with fewer steps
            timesteps = torch.linspace(self.num_timesteps - 1, 0, steps, device=self.device).long()
        else:
            timesteps = torch.arange(self.num_timesteps - 1, -1, -1, device=self.device)
        
        # Initialize trajectory if needed
        trajectory = [x_t.detach().clone()] if return_trajectory else None
        
        # Create progress bar if requested
        iterator = timesteps
        if show_progress:
            try:
                from tqdm import tqdm
                iterator = tqdm(timesteps, desc="Sampling")
            except ImportError:
                logger.warning("tqdm not installed, not showing progress bar")
        
        # Set model to evaluation mode
        self.noise_predictor.eval()
        
        # Iteratively denoise
        with torch.no_grad():
            for t in iterator:
                # Apply single reverse step
                x_t = self.reverse_process_step(
                    x_t, 
                    t.expand(batch_size), 
                    guidance_scale=guidance_scale,
                    guidance_fn=guidance_fn
                )
                
                # Add to trajectory if needed
                if return_trajectory:
                    trajectory.append(x_t.detach().clone())
        
        if return_trajectory:
            return trajectory
        else:
            return x_t
    
    def train_step(
        self, 
        x_0: torch.Tensor, 
        optimizer: torch.optim.Optimizer,
        max_grad_norm: float = MAX_GRADIENT_NORM,
        loss_weighting: str = 'uniform'
    ) -> Dict[str, float]:
        """
        Perform a single training step on the diffusion model.
        
        Args:
            x_0: Training data tensor [batch_size, concept_dim]
            optimizer: PyTorch optimizer
            max_grad_norm: Maximum gradient norm for clipping
            loss_weighting: Type of loss weighting ('uniform', 'snr')
            
        Returns:
            Dictionary with loss information
        """
        batch_size = x_0.shape[0]
        
        # Generate random timesteps
        t = torch.randint(0, self.num_timesteps, (batch_size,), device=self.device)
        
        # Add noise to generate x_t
        noise = torch.randn_like(x_0)
        x_t, _ = self.forward_process(x_0, t, noise)
        
        # Predict noise
        predicted_noise = self.noise_predictor(x_t, t)
        
        # Compute loss with optional weighting
        if loss_weighting == 'uniform':
            loss = self.loss_fn(predicted_noise, noise)
        elif loss_weighting == 'snr':
            # Signal-to-noise ratio weighting
            snr = self.alphas_cumprod[t] / (1 - self.alphas_cumprod[t])
            weights = 1.0 / (snr + 1)
            loss = F.mse_loss(predicted_noise, noise, reduction='none')
            loss = (loss * weights.view(-1, 1)).mean()
        else:
            raise ValueError(f"Unknown loss weighting: {loss_weighting}")
        
        # Update model
        optimizer.zero_grad()
        loss.backward()
        
        # Gradient clipping
        if max_grad_norm > 0:
            grad_norm = nn.utils.clip_grad_norm_(
                self.noise_predictor.parameters(), 
                max_grad_norm
            )
        else:
            grad_norm = 0.0
        
        optimizer.step()
        
        return {
            'loss': loss.item(),
            'grad_norm': grad_norm.item() if isinstance(grad_norm, torch.Tensor) else grad_norm
        }
    
    def train(
        self,
        dataset: Union[Dataset, torch.Tensor, List[torch.Tensor]],
        batch_size: int = 32,
        num_epochs: int = 100,
        learning_rate: float = DEFAULT_LEARNING_RATE,
        weight_decay: float = 1e-6,
        scheduler_type: str = 'cosine',
        warmup_steps: int = 1000,
        save_dir: Optional[str] = None,
        save_interval: int = 10,
        eval_interval: int = 5,
        max_grad_norm: float = MAX_GRADIENT_NORM,
        loss_weighting: str = 'uniform',
        callback: Optional[Callable[[int, Dict[str, float]], None]] = None
    ) -> Dict[str, List[float]]:
        """
        Train the diffusion model on a dataset of concepts.
        
        Args:
            dataset: Dataset of concept embeddings or tensor of embeddings
            batch_size: Batch size for training
            num_epochs: Number of training epochs
            learning_rate: Learning rate for optimizer
            weight_decay: Weight decay for optimizer
            scheduler_type: Type of learning rate scheduler ('cosine', 'exponential', 'plateau')
            warmup_steps: Number of warmup steps for learning rate
            save_dir: Directory to save models (optional)
            save_interval: Interval (in epochs) for saving models
            eval_interval: Interval (in epochs) for evaluation
            max_grad_norm: Maximum gradient norm for clipping
            loss_weighting: Type of loss weighting
            callback: Optional callback function called after each epoch
            
        Returns:
            Dictionary with training history
        """
        # Convert tensor or list to dataset if needed
        if isinstance(dataset, torch.Tensor):
            dataset = TensorDataset(dataset)
        elif isinstance(dataset, list) and all(isinstance(x, torch.Tensor) for x in dataset):
            dataset = TensorDataset(torch.stack(dataset))
        
        # Create data loader
        dataloader = DataLoader(
            dataset,
            batch_size=batch_size,
            shuffle=True,
            drop_last=True,
            pin_memory=True,
            num_workers=4 if self.device.type == 'cuda' else 0
        )
        
        # Set up optimizer
        optimizer = AdamW(
            self.noise_predictor.parameters(),
            lr=learning_rate,
            weight_decay=weight_decay,
            betas=(0.9, 0.999),
            eps=1e-8
        )
        
        # Set up scheduler
        total_steps = len(dataloader) * num_epochs
        if scheduler_type == 'cosine':
            scheduler = CosineAnnealingLR(
                optimizer,
                T_max=total_steps - warmup_steps,
                eta_min=learning_rate / 100
            )
        elif scheduler_type == 'exponential':
            scheduler = ExponentialLR(optimizer, gamma=0.95)
        elif scheduler_type == 'plateau':
            scheduler = ReduceLROnPlateau(
                optimizer, 
                mode='min', 
                factor=0.5, 
                patience=5,
                verbose=True
            )
        else:
            scheduler = None
        
        # Training history
        history = {
            'loss': [],
            'grad_norm': [],
            'lr': [],
            'eval_loss': []
        }
        
        # Create save directory if needed
        if save_dir is not None:
            Path(save_dir).mkdir(parents=True, exist_ok=True)
        
        # Training loop
        global_step = 0
        
        for epoch in range(num_epochs):
            # Set model to training mode
            self.noise_predictor.train()
            
            # Initialize epoch metrics
            epoch_loss = 0.0
            epoch_grad_norm = 0.0
            num_batches = 0
            
            # Process batches
            for batch in dataloader:
                # Extract concept embeddings
                if isinstance(batch, (list, tuple)):
                    x_0 = batch[0].to(self.device)
                else:
                    x_0 = batch.to(self.device)
                
                # Learning rate warmup
                if global_step < warmup_steps and scheduler_type == 'cosine':
                    lr = learning_rate * global_step / warmup_steps
                    for param_group in optimizer.param_groups:
                        param_group['lr'] = lr
                
                # Single training step
                step_metrics = self.train_step(
                    x_0, 
                    optimizer, 
                    max_grad_norm=max_grad_norm,
                    loss_weighting=loss_weighting
                )
                
                # Update epoch statistics
                epoch_loss += step_metrics['loss']
                epoch_grad_norm += step_metrics['grad_norm']
                num_batches += 1
                global_step += 1
                
                # Update learning rate (except for plateau scheduler)
                if scheduler is not None and scheduler_type != 'plateau':
                    if global_step >= warmup_steps or scheduler_type != 'cosine':
                        scheduler.step()
            
            # Compute average epoch metrics
            avg_epoch_loss = epoch_loss / max(num_batches, 1)
            avg_grad_norm = epoch_grad_norm / max(num_batches, 1)
            current_lr = optimizer.param_groups[0]['lr']
            
            history['loss'].append(avg_epoch_loss)
            history['grad_norm'].append(avg_grad_norm)
            history['lr'].append(current_lr)
            
            # Evaluation
            if epoch % eval_interval == 0 or epoch == num_epochs - 1:
                eval_loss = self._evaluate(dataloader, loss_weighting)
                history['eval_loss'].append(eval_loss)
                
                logger.info(f"Epoch {epoch+1}/{num_epochs}: "
                           f"loss={avg_epoch_loss:.6f}, "
                           f"eval_loss={eval_loss:.6f}, "
                           f"grad_norm={avg_grad_norm:.6f}, "
                           f"lr={current_lr:.6e}")
                
                # Update plateau scheduler
                if scheduler_type == 'plateau':
                    scheduler.step(eval_loss)
            else:
                logger.info(f"Epoch {epoch+1}/{num_epochs}: "
                           f"loss={avg_epoch_loss:.6f}, "
                           f"grad_norm={avg_grad_norm:.6f}, "
                           f"lr={current_lr:.6e}")
            
            # Call callback if provided
            if callback is not None:
                callback(epoch, {
                    'loss': avg_epoch_loss,
                    'grad_norm': avg_grad_norm,
                    'lr': current_lr
                })
            
            # Save model if requested
            if save_dir is not None and (epoch % save_interval == 0 or epoch == num_epochs - 1):
                save_path = Path(save_dir) / f"diffusion_model_epoch_{epoch+1}.pt"
                self.save(save_path)
        
        return history
    
    def _evaluate(self, dataloader: DataLoader, loss_weighting: str = 'uniform') -> float:
        """
        Evaluate the model on the provided data.
        
        Args:
            dataloader: DataLoader with evaluation data
            loss_weighting: Type of loss weighting
            
        Returns:
            Average evaluation loss
        """
        # Set model to evaluation mode
        self.noise_predictor.eval()
        
        # Initialize evaluation metrics
        eval_loss = 0.0
        num_batches = 0
        
        # Process batches
        with torch.no_grad():
            for batch in dataloader:
                # Extract concept embeddings
                if isinstance(batch, (list, tuple)):
                    x_0 = batch[0].to(self.device)
                else:
                    x_0 = batch.to(self.device)
                
                batch_size = x_0.shape[0]
                t = torch.randint(0, self.num_timesteps, (batch_size,), device=self.device)
                
                # Add noise
                noise = torch.randn_like(x_0)
                x_t, _ = self.forward_process(x_0, t, noise)
                
                # Predict noise
                predicted_noise = self.noise_predictor(x_t, t)
                
                # Compute loss
                if loss_weighting == 'uniform':
                    loss = self.loss_fn(predicted_noise, noise)
                elif loss_weighting == 'snr':
                    snr = self.alphas_cumprod[t] / (1 - self.alphas_cumprod[t])
                    weights = 1.0 / (snr + 1)
                    loss = F.mse_loss(predicted_noise, noise, reduction='none')
                    loss = (loss * weights.view(-1, 1)).mean()
                
                # Update evaluation statistics
                eval_loss += loss.item()
                num_batches += 1
        
        # Compute average evaluation loss
        avg_eval_loss = eval_loss / max(num_batches, 1)
        
        return avg_eval_loss
    
    def interpolate_concepts(
        self,
        concept_start: torch.Tensor,
        concept_end: torch.Tensor,
        num_steps: int = 5,
        method: str = 'latent',
        timestep: Optional[int] = None,
        guidance_scale: float = 0.0,
        guidance_fn: Optional[Callable] = None
    ) -> List[torch.Tensor]:
        """
        Interpolate between two concept embeddings.
        
        Args:
            concept_start: Starting concept embedding
            concept_end: Ending concept embedding
            num_steps: Number of interpolation steps
            method: Interpolation method ('linear', 'slerp', 'latent')
            timestep: Optional specific timestep for latent interpolation
            guidance_scale: Guidance scale for latent interpolation
            guidance_fn: Guidance function for latent interpolation
            
        Returns:
            List of interpolated concept embeddings
        """
        # Ensure concepts are on the correct device
        concept_start = concept_start.to(self.device)
        concept_end = concept_end.to(self.device)
        
        # Ensure proper batch dimension
        if concept_start.dim() == 1:
            concept_start = concept_start.unsqueeze(0)
        if concept_end.dim() == 1:
            concept_end = concept_end.unsqueeze(0)
        
        # Set model to evaluation mode
        self.noise_predictor.eval()
        
        if method in ['linear', 'slerp']:
            # Direct interpolation in concept space
            interpolated = []
            for alpha in torch.linspace(0, 1, num_steps, device=self.device):
                if method == 'linear':
                    interp = (1 - alpha) * concept_start + alpha * concept_end
                elif method == 'slerp':
                    # Spherical linear interpolation
                    start_norm = F.normalize(concept_start, dim=1)
                    end_norm = F.normalize(concept_end, dim=1)
                    
                    cos_sim = (start_norm * end_norm).sum(dim=1, keepdim=True)
                    cos_sim = torch.clamp(cos_sim, -0.99999, 0.99999)
                    omega = torch.acos(cos_sim)
                    
                    sin_omega = torch.sin(omega)
                    
                    # Handle near-parallel vectors
                    mask = sin_omega.abs() < 1e-6
                    
                    if mask.any():
                        # Fallback to linear interpolation
                        interp = (1 - alpha) * concept_start + alpha * concept_end
                    else:
                        coef1 = torch.sin((1 - alpha) * omega) / sin_omega
                        coef2 = torch.sin(alpha * omega) / sin_omega
                        interp = coef1 * concept_start + coef2 * concept_end
                
                interpolated.append(interp)
            
            return interpolated
            
        elif method == 'latent':
            # Latent space interpolation using the diffusion model
            with torch.no_grad():
                # Set timestep for noise addition
                t_mid = timestep if timestep is not None else (self.num_timesteps // 2)
                
                # Forward diffusion to add noise
                noisy_start, _ = self.forward_process(concept_start, t_mid)
                noisy_end, _ = self.forward_process(concept_end, t_mid)
                
                # Interpolate in the noisy space
                interpolated = []
                for alpha in torch.linspace(0, 1, num_steps, device=self.device):
                    # Interpolate noisy concepts
                    noisy_interp = (1 - alpha) * noisy_start + alpha * noisy_end
                    
                    # Perform reverse diffusion from the interpolated noisy point
                    denoised = self.sample(
                        batch_size=1,
                        steps=t_mid + 1,
                        x_T=noisy_interp,
                        guidance_scale=guidance_scale,
                        guidance_fn=guidance_fn
                    )
                    
                    interpolated.append(denoised)
            
            return interpolated
            
        else:
            raise ValueError(f"Unknown interpolation method: {method}")
    
    def encode_concept_hierarchically(self, concept: torch.Tensor) -> Optional[List[torch.Tensor]]:
        """
        Encode a concept hierarchically using the thought latent space.
        
        Args:
            concept: Concept to encode
            
        Returns:
            Hierarchical encodings or None if no thought space is available
        """
        if self.thought_latent_space is None:
            logger.warning("No thought latent space available for hierarchical encoding")
            return None
        
        return self.thought_latent_space.encode_hierarchical(concept)
    
    def save(self, path: Union[str, Path]):
        """
        Save the model to a file.
        
        Args:
            path: Path to save the model
        """
        path = Path(path)
        
        checkpoint = {
            'model_state_dict': self.noise_predictor.state_dict(),
            'config': self.config,
            'constants': {
                name: getattr(self, name).cpu() 
                for name in ['betas', 'alphas', 'alphas_cumprod', 'alphas_cumprod_prev',
                           'sqrt_alphas_cumprod', 'sqrt_one_minus_alphas_cumprod',
                           'sqrt_recip_alphas', 'posterior_variance',
                           'posterior_mean_coef1', 'posterior_mean_coef2']
            }
        }
        
        if self.thought_latent_space is not None:
            checkpoint['thought_latent_space_state_dict'] = self.thought_latent_space.state_dict()
        
        torch.save(checkpoint, path)
        logger.info(f"Saved ConceptualDiffusion model to {path}")
        
    def load(self, path: Union[str, Path], device: Optional[str] = None):
        """
        Load the model from a file.
        
        Args:
            path: Path to load the model from
            device: Optional device to load the model to
            
        Returns:
            Self for chaining
        """
        path = Path(path)
        
        # Set device
        load_device = device or self.device
        
        # Load checkpoint
        checkpoint = torch.load(path, map_location=load_device)
        
        # Update device if specified
        if device is not None:
            self.device = device
            
            # Move constants to new device
            for name in ['betas', 'alphas', 'alphas_cumprod', 'alphas_cumprod_prev',
                        'sqrt_alphas_cumprod', 'sqrt_one_minus_alphas_cumprod',
                        'sqrt_recip_alphas', 'posterior_variance',
                        'posterior_mean_coef1', 'posterior_mean_coef2']:
                if hasattr(self, name):
                    getattr(self, name).data = checkpoint['constants'][name].to(device)
        
        # Restore model state
        self.noise_predictor.load_state_dict(checkpoint['model_state_dict'])
        self.noise_predictor = self.noise_predictor.to(self.device)
        
        # Restore thought latent space if available
        if 'thought_latent_space_state_dict' in checkpoint and self.thought_latent_space is not None:
            self.thought_latent_space.load_state_dict(checkpoint['thought_latent_space_state_dict'])
            self.thought_latent_space = self.thought_latent_space.to(self.device)
        
        logger.info(f"Loaded ConceptualDiffusion model from {path} to {self.device}")
        return self
        
    def to(self, device: str) -> 'ConceptualDiffusion':
        """
        Move the diffusion model to the specified device.
        
        Args:
            device: Device to move to
            
        Returns:
            Self for chaining
        """
        # Update device attribute
        self.device = device
        
        # Move model
        super().to(device)
        
        return self
    
    def get_model_info(self) -> Dict[str, Any]:
        """
        Get information about the model.
        
        Returns:
            Dictionary with model information
        """
        total_params = sum(p.numel() for p in self.noise_predictor.parameters())
        trainable_params = sum(p.numel() for p in self.noise_predictor.parameters() if p.requires_grad)
        
        info = {
            'concept_dim': self.concept_dim,
            'num_timesteps': self.num_timesteps,
            'device': str(self.device),
            'total_parameters': total_params,
            'trainable_parameters': trainable_params,
            'model_size_mb': total_params * 4 / (1024 * 1024),  # Assuming float32
            'noise_schedule': self.config.beta_schedule,
            'has_thought_space': self.thought_latent_space is not None
        }
        
        if self.thought_latent_space is not None:
            thought_params = sum(p.numel() for p in self.thought_latent_space.parameters())
            info['thought_space_parameters'] = thought_params
            info['total_parameters'] += thought_params
            info['model_size_mb'] += thought_params * 4 / (1024 * 1024)
        
        return info

# ============================================================================
# Factory Functions and Utilities
# ============================================================================

def create_conceptual_diffusion(
    concept_dim: int = 512,
    num_timesteps: int = 1000,
    beta_schedule: str = 'cosine',
    hidden_dim: Optional[int] = None,
    num_layers: int = 6,
    device: str = None,
    **kwargs
) -> ConceptualDiffusion:
    """
    Factory function to create a ConceptualDiffusion instance.
    
    Args:
        concept_dim: Dimensionality of concept embeddings
        num_timesteps: Number of diffusion timesteps
        beta_schedule: Type of noise schedule
        hidden_dim: Hidden dimension for the noise predictor
        num_layers: Number of layers in the noise predictor
        device: Device to use
        **kwargs: Additional configuration parameters
        
    Returns:
        Configured ConceptualDiffusion instance
    """
    device = device or ('cuda' if torch.cuda.is_available() else 'cpu')
    
    config = DiffusionConfig(
        concept_dim=concept_dim,
        num_timesteps=num_timesteps,
        beta_schedule=beta_schedule,
        hidden_dim=hidden_dim or (concept_dim * 4),
        num_layers=num_layers,
        device=device,
        **kwargs
    )
    
    return ConceptualDiffusion(config)

def create_thought_latent_space(
    thought_space_dim: int = 1024,
    hierarchical_levels: int = 4,
    semantic_dim: int = 256,
    **kwargs
) -> ThoughtLatentSpace:
    """
    Factory function to create a ThoughtLatentSpace instance.
    
    Args:
        thought_space_dim: Dimensionality of the thought space
        hierarchical_levels: Number of hierarchical levels
        semantic_dim: Dimensionality of semantic representations
        **kwargs: Additional configuration parameters
        
    Returns:
        Configured ThoughtLatentSpace instance
    """
    config = ThoughtSpaceConfig(
        thought_space_dim=thought_space_dim,
        hierarchical_levels=hierarchical_levels,
        semantic_dim=semantic_dim,
        **kwargs
    )
    
    return ThoughtLatentSpace(config)

# For backwards compatibility
DiffusionModel = ConceptNoisePredictor

# Export main classes and functions
__all__ = [
    'ConceptualDiffusion',
    'ConceptNoisePredictor', 
    'ThoughtLatentSpace',
    'DiffusionConfig',
    'ThoughtSpaceConfig',
    'NoiseScheduleType',
    'ReasoningMode',
    'SinusoidalPositionEmbeddings',
    'ResidualBlock',
    'AttentionBlock',
    'DiffusionMath',
    'create_conceptual_diffusion',
    'create_thought_latent_space',
    'DiffusionModel'  # Backwards compatibility
]