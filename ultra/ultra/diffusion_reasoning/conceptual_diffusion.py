#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ULTRA: Ultimate Learning & Thought Reasoning Architecture
Conceptual Diffusion Process Implementation

This module adapts diffusion models to operate in latent spaces that represent concepts
rather than raw data, enabling exploration of conceptual spaces and creative reasoning.

The implementation follows the mathematical formalism of diffusion models (<PERSON> et al., 2020)
but applies it to abstract concept representations in high-dimensional spaces. Key functions:

- Forward diffusion: q(z_t | z_{t-1}) = N(z_t; sqrt(1 - β_t) z_{t-1}, β_t I)
- In terms of z_0: q(z_t | z_0) = N(z_t; sqrt(α_t) z_0, (1 - α_t) I)
- Reverse process: p_θ(z_{t-1} | z_t) = N(z_{t-1}; μ_θ(z_t, t), Σ_θ(z_t, t))
"""

import math
import logging
import os
from typing import Dict, List, Tuple, Union, Optional, Callable, Any

import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.optim import Adam, Optimizer
from torch.optim.lr_scheduler import CosineAnnealing<PERSON>
from torch.utils.data import DataLoader, Dataset, TensorDataset

# Configure module logger
logger = logging.getLogger(__name__)

# ============================================================================
# Sinusoidal Time Embedding (for timestep conditioning)
# ============================================================================

class SinusoidalPositionEmbeddings(nn.Module):
    """
    Embeds diffusion timesteps using sinusoidal positional embeddings,
    similar to the positional encodings in the Transformer architecture.
    """
    def __init__(self, dim: int):
        """
        Initialize the positional embeddings.
        
        Args:
            dim: Dimensionality of the embeddings
        """
        super().__init__()
        self.dim = dim

    def forward(self, time: torch.Tensor) -> torch.Tensor:
        """
        Compute sinusoidal embeddings for timesteps.
        
        Args:
            time: Tensor of diffusion timesteps [batch_size]
            
        Returns:
            Time embeddings [batch_size, dim]
        """
        half_dim = self.dim // 2
        embeddings = np.log(10000) / (half_dim - 1)
        embeddings = torch.exp(torch.arange(half_dim, device=time.device) * -embeddings)
        embeddings = time[:, None] * embeddings[None, :]
        embeddings = torch.cat((torch.sin(embeddings), torch.cos(embeddings)), dim=-1)
        
        # Handle odd dimensions by extending with zeros
        if self.dim % 2 == 1:
            embeddings = F.pad(embeddings, (0, 1, 0, 0))
            
        return embeddings

# ============================================================================
# Diffusion Model (Noise Predictor)
# ============================================================================

class ConceptNoisePredictor(nn.Module):
    """
    Neural network that predicts the noise component in diffused concepts.
    This is the core learnable component of the diffusion model.
    """
    def __init__(
        self, 
        concept_dim: int, 
        time_embedding_dim: int = None, 
        hidden_dim: int = None,
        num_layers: int = 4,
        dropout: float = 0.1,
        use_layer_norm: bool = True
    ):
        """
        Initialize the noise prediction model.
        
        Args:
            concept_dim: Dimensionality of concept embeddings
            time_embedding_dim: Dimensionality of time embeddings (default: concept_dim)
            hidden_dim: Dimensionality of hidden layers (default: concept_dim * 4)
            num_layers: Number of hidden layers
            dropout: Dropout probability
            use_layer_norm: Whether to use layer normalization
        """
        super().__init__()
        
        self.concept_dim = concept_dim
        self.time_embedding_dim = time_embedding_dim or concept_dim
        self.hidden_dim = hidden_dim or (concept_dim * 4)
        
        # Time embeddings
        self.time_mlp = nn.Sequential(
            SinusoidalPositionEmbeddings(self.time_embedding_dim),
            nn.Linear(self.time_embedding_dim, self.hidden_dim),
            nn.SiLU(),
            nn.Linear(self.hidden_dim, self.hidden_dim),
        )
        
        # Initial projection
        self.input_proj = nn.Linear(concept_dim, self.hidden_dim)
        
        # Hidden layers with residual connections
        self.layers = nn.ModuleList()
        for _ in range(num_layers):
            layer = []
            layer.append(nn.Linear(self.hidden_dim, self.hidden_dim))
            if use_layer_norm:
                layer.append(nn.LayerNorm(self.hidden_dim))
            layer.append(nn.SiLU())
            if dropout > 0:
                layer.append(nn.Dropout(dropout))
                
            self.layers.append(nn.Sequential(*layer))
        
        # Output projection
        self.output_proj = nn.Linear(self.hidden_dim, concept_dim)
        
        # Initialize weights
        self._init_weights()
        
        logger.info(f"Initialized ConceptNoisePredictor with concept_dim={concept_dim}, hidden_dim={self.hidden_dim}")
        
    def _init_weights(self):
        """Initialize model weights for better convergence."""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.kaiming_normal_(module.weight, nonlinearity='relu')
                if module.bias is not None:
                    nn.init.zeros_(module.bias)
    
    def forward(self, x: torch.Tensor, t: torch.Tensor) -> torch.Tensor:
        """
        Predict the noise component in diffused concepts.
        
        Args:
            x: Diffused concept tensor [batch_size, concept_dim]
            t: Timestep tensor [batch_size]
            
        Returns:
            Predicted noise tensor [batch_size, concept_dim]
        """
        # Get time embeddings
        t_emb = self.time_mlp(t)
        
        # Process input
        h = self.input_proj(x)
        
        # Add time embeddings
        h = h + t_emb
        
        # Apply hidden layers with residual connections
        for layer in self.layers:
            h_prev = h
            h = layer(h)
            h = h + h_prev  # Residual connection
        
        # Output projection
        noise_pred = self.output_proj(h)
        
        return noise_pred

# ============================================================================
# Helper Functions for Diffusion Process
# ============================================================================

def create_beta_schedule(
    schedule: str = 'linear',
    num_timesteps: int = 1000,
    start: float = 1e-4,
    end: float = 0.02,
    s: float = 0.008  # For cosine schedule
) -> torch.Tensor:
    """
    Create various noise schedules for the forward diffusion process.
    
    Args:
        schedule: Type of beta schedule ('linear', 'quadratic', 'cosine', 'sigmoid')
        num_timesteps: Number of diffusion steps
        start: Starting beta value
        end: Ending beta value
        s: Offset parameter for cosine schedule
        
    Returns:
        Tensor of beta values for each timestep
    """
    if schedule == 'linear':
        betas = torch.linspace(start, end, num_timesteps, dtype=torch.float32)
    elif schedule == 'quadratic':
        betas = torch.linspace(start**0.5, end**0.5, num_timesteps, dtype=torch.float32) ** 2
    elif schedule == 'cosine':
        steps = num_timesteps + 1
        x = torch.linspace(0, num_timesteps, steps, dtype=torch.float32)
        alphas_cumprod = torch.cos((x / num_timesteps + s) / (1 + s) * math.pi * 0.5) ** 2
        alphas_cumprod = alphas_cumprod / alphas_cumprod[0]
        betas = 1 - (alphas_cumprod[1:] / alphas_cumprod[:-1])
        betas = torch.clamp(betas, 0, 0.999)
    elif schedule == "sigmoid":
        betas = torch.linspace(-6, 6, num_timesteps, dtype=torch.float32)
        betas = torch.sigmoid(betas) * (end - start) + start
    else:
        raise ValueError(f"Unknown beta schedule: {schedule}")
    
    return betas

def extract_concept_from_noise(
    x_t: torch.Tensor,
    t: Union[int, torch.Tensor],
    predicted_noise: torch.Tensor,
    alphas_cumprod: torch.Tensor
) -> torch.Tensor:
    """
    Extract original concept embeddings (x_0) from diffused concepts (x_t).
    
    Args:
        x_t: Diffused concept at timestep t [batch_size, concept_dim]
        t: Diffusion timestep(s) [batch_size] or int
        predicted_noise: Noise predicted by the model [batch_size, concept_dim]
        alphas_cumprod: Cumulative product of alphas
        
    Returns:
        Extracted original concept embedding (approximation of x_0)
    """
    # Handle scalar and tensor timesteps
    if isinstance(t, int):
        t_tensor = torch.tensor([t], device=x_t.device).repeat(x_t.shape[0])
    else:
        t_tensor = t
    
    # Extract corresponding alpha values
    alpha_cumprod = alphas_cumprod[t_tensor].view(-1, 1)
    
    # Formula: x_0 = (x_t - sqrt(1-α_t) * ε_θ) / sqrt(α_t)
    sqrt_one_minus_alpha = torch.sqrt(1 - alpha_cumprod)
    sqrt_alpha = torch.sqrt(alpha_cumprod)
    
    x_0 = (x_t - sqrt_one_minus_alpha * predicted_noise) / sqrt_alpha
    
    # Clamp the result to the valid range (-1, 1)
    x_0 = torch.clamp(x_0, -1.0, 1.0)
    
    return x_0

# ============================================================================
# Main Conceptual Diffusion Class
# ============================================================================

class ConceptualDiffusion:
    """
    Applies diffusion processes to concept representations, enabling exploration
    of concept spaces through noise addition and denoising.
    
    This class provides methods for:
    - Training the diffusion model
    - Adding noise to concepts (forward process)
    - Denoising concepts (reverse process)
    - Sampling new concepts
    - Interpolating between concepts
    - Guided sampling with external functions
    """
    
    def __init__(
        self, 
        concept_dim: int,
        noise_predictor: Optional[nn.Module] = None,
        num_timesteps: int = 1000, 
        beta_schedule: str = 'linear',
        beta_start: float = 1e-4,
        beta_end: float = 0.02,
        device: str = 'cuda' if torch.cuda.is_available() else 'cpu'
    ):
        """
        Initialize the Conceptual Diffusion process.
        
        Args:
            concept_dim: Dimensionality of the concept space
            noise_predictor: Optional pre-trained noise prediction model
            num_timesteps: Number of diffusion timesteps
            beta_schedule: Schedule for noise level ('linear', 'cosine', 'quadratic', 'sigmoid')
            beta_start: Starting noise level
            beta_end: Ending noise level
            device: Device to run computations on ('cpu' or 'cuda')
        """
        self.concept_dim = concept_dim
        self.num_timesteps = num_timesteps
        self.device = device
        
        # Create beta schedule
        self.betas = create_beta_schedule(
            schedule=beta_schedule,
            num_timesteps=num_timesteps,
            start=beta_start,
            end=beta_end
        ).to(device)
        
        # Compute diffusion parameters
        self.alphas = 1.0 - self.betas
        self.alphas_cumprod = torch.cumprod(self.alphas, dim=0)
        self.alphas_cumprod_prev = F.pad(self.alphas_cumprod[:-1], (1, 0), value=1.0)
        self.sqrt_alphas_cumprod = torch.sqrt(self.alphas_cumprod)
        self.sqrt_one_minus_alphas_cumprod = torch.sqrt(1.0 - self.alphas_cumprod)
        self.sqrt_recip_alphas = torch.sqrt(1.0 / self.alphas)
        self.posterior_variance = self.betas * (1.0 - self.alphas_cumprod_prev) / (1.0 - self.alphas_cumprod)
        self.posterior_mean_coef1 = self.betas * torch.sqrt(self.alphas_cumprod_prev) / (1.0 - self.alphas_cumprod)
        self.posterior_mean_coef2 = (1.0 - self.alphas_cumprod_prev) * torch.sqrt(self.alphas) / (1.0 - self.alphas_cumprod)
        
        # Create or use provided noise predictor
        if noise_predictor is not None:
            self.model = noise_predictor.to(device)
        else:
            self.model = ConceptNoisePredictor(concept_dim=concept_dim).to(device)
        
        logger.info(f"Initialized ConceptualDiffusion with {num_timesteps} timesteps using {beta_schedule} schedule on {device}")
    
    def forward_process(
        self, 
        x_0: torch.Tensor, 
        t: Union[int, torch.Tensor], 
        noise: Optional[torch.Tensor] = None
    ) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Run the forward diffusion process (adding noise).
        
        Args:
            x_0: Initial concept tensor [batch_size, concept_dim]
            t: Timestep(s) to diffuse to
            noise: Optional noise to add (if None, random noise is generated)
            
        Returns:
            Tuple of (diffused tensor, noise added)
        """
        batch_size = x_0.shape[0]
        
        # Handle t as int or tensor
        if isinstance(t, int):
            t = torch.ones(batch_size, device=self.device, dtype=torch.long) * t
        
        # Generate noise if not provided
        if noise is None:
            noise = torch.randn_like(x_0)
            
        # Get diffusion parameters for time t
        sqrt_alphas_cumprod_t = self.sqrt_alphas_cumprod[t].view(-1, 1)
        sqrt_one_minus_alphas_cumprod_t = self.sqrt_one_minus_alphas_cumprod[t].view(-1, 1)
        
        # Compute diffused concept: x_t = √(α_t) * x_0 + √(1-α_t) * ε
        x_t = sqrt_alphas_cumprod_t * x_0 + sqrt_one_minus_alphas_cumprod_t * noise
        
        return x_t, noise
    
    def reverse_process_step(
        self, 
        x_t: torch.Tensor, 
        t: Union[int, torch.Tensor], 
        guide_scale: float = 0.0,
        guidance_fn: Optional[Callable] = None
    ) -> torch.Tensor:
        """
        Run a single step of the reverse diffusion process (denoising).
        
        Args:
            x_t: Diffused concept tensor at time t [batch_size, concept_dim]
            t: Current timestep(s)
            guide_scale: Scale factor for guidance (0.0 = no guidance)
            guidance_fn: Optional function for guided sampling
            
        Returns:
            Tensor containing previous step x_(t-1)
        """
        batch_size = x_t.shape[0]
        
        # Handle t as int or tensor
        if isinstance(t, int):
            t = torch.ones(batch_size, device=self.device, dtype=torch.long) * t
        
        # Predict noise
        with torch.no_grad():
            predicted_noise = self.model(x_t, t)
        
        # Apply guidance if provided
        if guide_scale > 0 and guidance_fn is not None:
            guidance = guidance_fn(x_t, t)
            predicted_noise = predicted_noise + guide_scale * guidance
        
        # Handle t=0 separately (final step)
        if (t == 0).all():
            # For t=0, directly predict the original concept
            return extract_concept_from_noise(x_t, t, predicted_noise, self.alphas_cumprod)
        
        # Get diffusion parameters
        alpha_t = self.alphas[t].view(-1, 1)
        alpha_cumprod_t = self.alphas_cumprod[t].view(-1, 1)
        beta_t = self.betas[t].view(-1, 1)
        alpha_cumprod_prev_t = self.alphas_cumprod_prev[t].view(-1, 1)
            
        # First predict x_0 from x_t and predicted noise
        x_0_pred = extract_concept_from_noise(x_t, t, predicted_noise, self.alphas_cumprod)
        
        # Compute mean of posterior distribution q(x_{t-1} | x_t, x_0)
        posterior_mean = (
            self.posterior_mean_coef1[t].view(-1, 1) * x_0_pred + 
            self.posterior_mean_coef2[t].view(-1, 1) * x_t
        )
        
        # Sample from posterior
        posterior_variance_t = self.posterior_variance[t].view(-1, 1)
        noise = torch.randn_like(x_t)
        
        return posterior_mean + torch.sqrt(posterior_variance_t) * noise
    
    def sample(
        self, 
        batch_size: int = 1, 
        steps: Optional[int] = None, 
        x_T: Optional[torch.Tensor] = None,
        guide_scale: float = 0.0,
        guidance_fn: Optional[Callable] = None,
        temperature: float = 1.0,
        return_trajectory: bool = False,
        show_progress: bool = False
    ) -> Union[torch.Tensor, List[torch.Tensor]]:
        """
        Sample from the diffusion model by running the reverse process from random noise.
        
        Args:
            batch_size: Number of concepts to sample
            steps: Number of denoising steps (default: num_timesteps)
            x_T: Optional starting point (if None, random noise is used)
            guide_scale: Scale factor for guidance (0.0 = no guidance)
            guidance_fn: Optional function for guided sampling
            temperature: Temperature for sampling (controls randomness)
            return_trajectory: Whether to return the full trajectory of samples
            show_progress: Whether to show a progress bar
            
        Returns:
            Tensor containing sampled concepts [batch_size, concept_dim] or
            List of tensors if return_trajectory=True
        """
        steps = steps or self.num_timesteps
        
        # Start from random noise or provided x_T
        if x_T is None:
            x_t = torch.randn(batch_size, self.concept_dim, device=self.device) * temperature
        else:
            x_t = x_T
            
        # Set up timesteps
        timesteps = torch.linspace(self.num_timesteps - 1, 0, steps, device=self.device).long()
        
        # Initialize trajectory if needed
        trajectory = [x_t.detach().clone()] if return_trajectory else None
        
        # Create progress bar if requested
        iterator = timesteps
        if show_progress:
            try:
                from tqdm import tqdm
                iterator = tqdm(timesteps, desc="Sampling")
            except ImportError:
                logger.warning("tqdm not installed, not showing progress bar")
                
        # Iteratively denoise
        for t in iterator:
            # Apply single reverse step
            x_t = self.reverse_process_step(
                x_t, 
                t.expand(batch_size), 
                guide_scale=guide_scale,
                guidance_fn=guidance_fn
            )
            
            # Add to trajectory if needed
            if return_trajectory:
                trajectory.append(x_t.detach().clone())
        
        if return_trajectory:
            return trajectory
        else:
            return x_t
    
    def train(
        self,
        dataset: Union[Dataset, torch.Tensor, List[torch.Tensor]],
        batch_size: int = 32,
        num_epochs: int = 100,
        learning_rate: float = 1e-4,
        weight_decay: float = 1e-6,
        max_grad_norm: float = 1.0,
        save_dir: Optional[str] = None,
        save_interval: int = 10,
        eval_interval: int = 5,
        callback: Optional[Callable[[int, float], None]] = None
    ) -> Dict[str, List[float]]:
        """
        Train the diffusion model on a dataset of concepts.
        
        Args:
            dataset: Dataset of concept embeddings or tensor of embeddings
            batch_size: Batch size for training
            num_epochs: Number of training epochs
            learning_rate: Learning rate for optimizer
            weight_decay: Weight decay for optimizer
            max_grad_norm: Maximum gradient norm for clipping
            save_dir: Directory to save models (optional)
            save_interval: Interval (in epochs) for saving models
            eval_interval: Interval (in epochs) for evaluation
            callback: Optional callback function called after each epoch
            
        Returns:
            Dictionary with training history
        """
        # Convert tensor or list to dataset if needed
        if isinstance(dataset, torch.Tensor):
            dataset = TensorDataset(dataset)
        elif isinstance(dataset, list) and all(isinstance(x, torch.Tensor) for x in dataset):
            dataset = TensorDataset(torch.stack(dataset))
        
        # Create data loader
        dataloader = DataLoader(
            dataset,
            batch_size=batch_size,
            shuffle=True,
            drop_last=True,
            pin_memory=True
        )
        
        # Set up optimizer and scheduler
        optimizer = Adam(
            self.model.parameters(),
            lr=learning_rate,
            weight_decay=weight_decay
        )
        
        # Cosine annealing scheduler
        scheduler = CosineAnnealingLR(
            optimizer,
            T_max=num_epochs,
            eta_min=learning_rate / 10
        )
        
        # Training history
        history = {'loss': [], 'eval_loss': []}
        
        # Create save directory if needed
        if save_dir is not None:
            os.makedirs(save_dir, exist_ok=True)
        
        # Training loop
        for epoch in range(num_epochs):
            # Set model to training mode
            self.model.train()
            
            # Initialize epoch loss
            epoch_loss = 0.0
            num_batches = 0
            
            # Process batches
            for batch in dataloader:
                # Extract concept embeddings
                if isinstance(batch, (list, tuple)):
                    # Dataset returns tuples
                    x_0 = batch[0].to(self.device)
                else:
                    # Dataset returns tensors
                    x_0 = batch.to(self.device)
                
                # Single training step
                loss = self._train_step(x_0, optimizer, max_grad_norm)
                
                # Update epoch statistics
                epoch_loss += loss
                num_batches += 1
            
            # Compute average epoch loss
            avg_epoch_loss = epoch_loss / max(num_batches, 1)
            history['loss'].append(avg_epoch_loss)
            
            # Update learning rate
            scheduler.step()
            
            # Evaluation
            if epoch % eval_interval == 0 or epoch == num_epochs - 1:
                eval_loss = self._evaluate(dataloader)
                history['eval_loss'].append(eval_loss)
                
                logger.info(f"Epoch {epoch+1}/{num_epochs}: loss={avg_epoch_loss:.6f}, eval_loss={eval_loss:.6f}")
            else:
                logger.info(f"Epoch {epoch+1}/{num_epochs}: loss={avg_epoch_loss:.6f}")
            
            # Call callback if provided
            if callback is not None:
                callback(epoch, avg_epoch_loss)
            
            # Save model if requested
            if save_dir is not None and (epoch % save_interval == 0 or epoch == num_epochs - 1):
                save_path = os.path.join(save_dir, f"diffusion_model_epoch_{epoch+1}.pt")
                self.save(save_path)
        
        return history
    
    def _train_step(
        self, 
        x_0: torch.Tensor, 
        optimizer: Optimizer, 
        max_grad_norm: float = 1.0,
        t: Optional[torch.Tensor] = None
    ) -> float:
        """
        Perform a single training step on the diffusion model.
        
        Args:
            x_0: Training data tensor [batch_size, concept_dim]
            optimizer: PyTorch optimizer
            max_grad_norm: Maximum gradient norm for clipping
            t: Optional specific timesteps to use (if None, random timesteps are used)
            
        Returns:
            Loss value
        """
        batch_size = x_0.shape[0]
        
        # Generate random timesteps if not provided
        if t is None:
            t = torch.randint(0, self.num_timesteps, (batch_size,), device=self.device)
            
        # Add noise to generate x_t
        noise = torch.randn_like(x_0)
        x_t, _ = self.forward_process(x_0, t, noise)
        
        # Predict noise
        predicted_noise = self.model(x_t, t)
        
        # Compute loss
        loss = F.mse_loss(predicted_noise, noise)
        
        # Update model
        optimizer.zero_grad()
        loss.backward()
        
        # Gradient clipping
        if max_grad_norm > 0:
            nn.utils.clip_grad_norm_(self.model.parameters(), max_grad_norm)
            
        optimizer.step()
        
        return loss.item()
    
    def _evaluate(self, dataloader: DataLoader) -> float:
        """
        Evaluate the model on the provided data.
        
        Args:
            dataloader: DataLoader with evaluation data
            
        Returns:
            Average evaluation loss
        """
        # Set model to evaluation mode
        self.model.eval()
        
        # Initialize evaluation loss
        eval_loss = 0.0
        num_batches = 0
        
        # Process batches
        with torch.no_grad():
            for batch in dataloader:
                # Extract concept embeddings
                if isinstance(batch, (list, tuple)):
                    # Dataset returns tuples
                    x_0 = batch[0].to(self.device)
                else:
                    # Dataset returns tensors
                    x_0 = batch.to(self.device)
                
                batch_size = x_0.shape[0]
                t = torch.randint(0, self.num_timesteps, (batch_size,), device=self.device)
                
                # Add noise
                noise = torch.randn_like(x_0)
                x_t, _ = self.forward_process(x_0, t, noise)
                
                # Predict noise
                predicted_noise = self.model(x_t, t)
                
                # Compute loss
                loss = F.mse_loss(predicted_noise, noise)
                
                # Update evaluation statistics
                eval_loss += loss.item()
                num_batches += 1
        
        # Compute average evaluation loss
        avg_eval_loss = eval_loss / max(num_batches, 1)
        
        return avg_eval_loss
    
    def predict_original_concept(
        self,
        noisy_concept: torch.Tensor,
        timestep: int
    ) -> torch.Tensor:
        """
        Predict the original concept embedding from a noisy version.
        
        Args:
            noisy_concept: Noisy concept embedding
            timestep: Current diffusion timestep
            
        Returns:
            Predicted original concept embedding
        """
        # Ensure concept is on the correct device
        noisy_concept = noisy_concept.to(self.device)
        
        # Set model to evaluation mode
        self.model.eval()
        
        # Create timestep tensor
        t = torch.full((noisy_concept.size(0),), timestep, device=self.device, dtype=torch.long)
        
        # Predict noise
        with torch.no_grad():
            predicted_noise = self.model(noisy_concept, t)
        
        # Extract original concept
        x_0 = extract_concept_from_noise(
            noisy_concept, 
            t, 
            predicted_noise, 
            self.alphas_cumprod
        )
        
        return x_0
        
    def add_noise_to_concept(
        self,
        concept: torch.Tensor,
        timestep: int,
        noise: Optional[torch.Tensor] = None
    ) -> torch.Tensor:
        """
        Add noise to a concept embedding up to a specific timestep.
        
        Args:
            concept: Original concept embedding
            timestep: Target diffusion timestep
            noise: Optional specific noise to add
            
        Returns:
            Noisy concept embedding
        """
        # Ensure concept is on the correct device
        concept = concept.to(self.device)
        
        # Apply forward diffusion process
        noisy_concept, _ = self.forward_process(
            concept,
            timestep,
            noise=noise
        )
        
        return noisy_concept
    
    def interpolate_concepts(
        self,
        concept_start: torch.Tensor,
        concept_end: torch.Tensor,
        num_steps: int = 5,
        method: str = 'latent',
        timestep: int = None,
        temperature: float = 1.0
    ) -> List[torch.Tensor]:
        """
        Interpolate between two concept embeddings.
        
        Args:
            concept_start: Starting concept embedding
            concept_end: Ending concept embedding
            num_steps: Number of interpolation steps
            method: Interpolation method ('linear', 'slerp', 'latent')
            timestep: Optional specific timestep for latent interpolation
            temperature: Temperature for sampling
            
        Returns:
            List of interpolated concept embeddings
        """
        # Ensure concepts are on the correct device
        concept_start = concept_start.to(self.device)
        concept_end = concept_end.to(self.device)
        
        # If concepts are batched, use only the first one
        if len(concept_start.shape) > 1 and concept_start.shape[0] > 1:
            concept_start = concept_start[0]
        if len(concept_end.shape) > 1 and concept_end.shape[0] > 1:
            concept_end = concept_end[0]
            
        # Ensure concepts are properly shaped
        if len(concept_start.shape) == 1:
            concept_start = concept_start.unsqueeze(0)
        if len(concept_end.shape) == 1:
            concept_end = concept_end.unsqueeze(0)
        
        # Set model to evaluation mode
        self.model.eval()
        
        # Handle different interpolation methods
        if method == 'linear':
            # Simple linear interpolation in the original space
            interpolated = []
            for alpha in torch.linspace(0, 1, num_steps, device=self.device):
                interp = (1 - alpha) * concept_start + alpha * concept_end
                interpolated.append(interp)
            
            return interpolated
            
        elif method == 'slerp':
            # Spherical linear interpolation
            start_norm = concept_start / concept_start.norm()
            end_norm = concept_end / concept_end.norm()
            
            # Compute cosine similarity
            cos_sim = (start_norm * end_norm).sum().clamp(-0.99999, 0.99999)
            omega = torch.acos(cos_sim)
            
            interpolated = []
            for alpha in torch.linspace(0, 1, num_steps, device=self.device):
                if omega.abs() < 1e-5:
                    # Concepts are too similar, fall back to linear
                    interp = (1 - alpha) * concept_start + alpha * concept_end
                else:
                    # Spherical interpolation
                    sin_omega = torch.sin(omega)
                    coef1 = torch.sin((1 - alpha) * omega) / sin_omega
                    coef2 = torch.sin(alpha * omega) / sin_omega
                    interp = coef1 * concept_start + coef2 * concept_end
                
                interpolated.append(interp)
            
            return interpolated
            
        elif method == 'latent':
            # Latent space interpolation using the diffusion model
            with torch.no_grad():
                # Set timestep for noise addition
                t_mid = timestep if timestep is not None else (self.num_timesteps // 2)
                
                # Forward diffusion to add noise
                noisy_start, _ = self.forward_process(
                    concept_start,
                    t_mid
                )
                
                noisy_end, _ = self.forward_process(
                    concept_end,
                    t_mid
                )
                
                # Interpolate in the noisy space
                interpolated = []
                for alpha in torch.linspace(0, 1, num_steps, device=self.device):
                    # Interpolate noisy concepts
                    noisy_interp = (1 - alpha) * noisy_start + alpha * noisy_end
                    
                    # Perform reverse diffusion from the interpolated noisy point
                    denoised = self.sample(
                        batch_size=1,
                        steps=t_mid + 1,  # +1 to include the final step
                        x_T=noisy_interp,
                        temperature=temperature,
                    )
                    
                    interpolated.append(denoised)
            
            return interpolated
            
        else:
            raise ValueError(f"Unknown interpolation method: {method}")
    
    def save(self, path: str):
        """
        Save the model to a file.
        
        Args:
            path: Path to save the model
        """
        torch.save({
            'model_state_dict': self.model.state_dict(),
            'config': {
                'concept_dim': self.concept_dim,
                'num_timesteps': self.num_timesteps,
                'alphas': self.alphas,
                'betas': self.betas,
                'device': self.device
            }
        }, path)
        logger.info(f"Saved ConceptualDiffusion model to {path}")
        
    def load(self, path: str, device: Optional[str] = None):
        """
        Load the model from a file.
        
        Args:
            path: Path to load the model from
            device: Optional device to load the model to
            
        Returns:
            Self for chaining
        """
        # Set device
        load_device = device or self.device
        
        # Load checkpoint
        checkpoint = torch.load(path, map_location=load_device)
        
        # Update device if it was specified
        if device is not None:
            self.device = device
            
            # Move parameters to new device
            self.betas = self.betas.to(device)
            self.alphas = self.alphas.to(device)
            self.alphas_cumprod = self.alphas_cumprod.to(device)
            self.alphas_cumprod_prev = self.alphas_cumprod_prev.to(device)
            self.sqrt_alphas_cumprod = self.sqrt_alphas_cumprod.to(device)
            self.sqrt_one_minus_alphas_cumprod = self.sqrt_one_minus_alphas_cumprod.to(device)
            self.sqrt_recip_alphas = self.sqrt_recip_alphas.to(device)
            self.posterior_variance = self.posterior_variance.to(device)
            self.posterior_mean_coef1 = self.posterior_mean_coef1.to(device)
            self.posterior_mean_coef2 = self.posterior_mean_coef2.to(device)
        
        # Restore model state
        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.model = self.model.to(self.device)
        
        # Restore config if available
        config = checkpoint.get('config', {})
        if 'concept_dim' in config:
            assert config['concept_dim'] == self.concept_dim, "Concept dimension mismatch"
            
        logger.info(f"Loaded ConceptualDiffusion model from {path} to {self.device}")
        return self
        
    def to(self, device: str) -> 'ConceptualDiffusion':
        """
        Move the diffusion model to the specified device.
        
        Args:
            device: Device to move to
            
        Returns:
            Self for chaining
        """
        # Update device attribute
        self.device = device
        
        # Move model
        self.model = self.model.to(device)
        
        # Move tensors
        self.betas = self.betas.to(device)
        self.alphas = self.alphas.to(device)
        self.alphas_cumprod = self.alphas_cumprod.to(device)
        self.alphas_cumprod_prev = self.alphas_cumprod_prev.to(device)
        self.sqrt_alphas_cumprod = self.sqrt_alphas_cumprod.to(device)
        self.sqrt_one_minus_alphas_cumprod = self.sqrt_one_minus_alphas_cumprod.to(device)
        self.sqrt_recip_alphas = self.sqrt_recip_alphas.to(device)
        self.posterior_variance = self.posterior_variance.to(device)
        self.posterior_mean_coef1 = self.posterior_mean_coef1.to(device)
        self.posterior_mean_coef2 = self.posterior_mean_coef2.to(device)
        
        return self

# For backwards compatibility
DiffusionModel = ConceptNoisePredictor