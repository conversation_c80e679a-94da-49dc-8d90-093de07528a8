#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ULTRA: Ultimate Learning & Thought Reasoning Architecture
Hybrid Thought Latent Space Implementation

This module combines the best of both implementations:
- Simple, robust core architecture (from original)
- Advanced training and mathematical operations (enhanced)
- Production-ready error handling and logging
- Scalable and maintainable design

Mathematical Foundation:
- Vector space embedding: z = [z₁, z₂, ..., zₗ] where l is the abstraction level
- Semantic similarity: sim(c_i, c_j) ≈ exp(-d(z_i, z_j))
- Relation vectors: r_ij = z_j - z_i
- Composition functions: z_new = f(z₁, z₂, ..., zₙ, r₁, r₂, ..., rₘ)

Key Properties:
1. Hierarchical structure: Different regions correspond to different abstraction levels
2. Semantic continuity: Similar concepts are close to each other in the space
3. Relational structure: Analogical relationships are preserved as vector operations
4. Compositional properties: New concepts can be constructed through vector operations
"""

import math
import logging
import os
import time
import threading
from typing import Dict, List, Tuple, Union, Optional, Callable, Any, Set
from collections import defaultdict, deque
from dataclasses import dataclass, field
import uuid
import pickle
import json

import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.optim import Adam, AdamW
from torch.optim.lr_scheduler import CosineAnnealingLR, ReduceLROnPlateau
from torch.utils.data import DataLoader, Dataset, TensorDataset

# Advanced mathematical operations (optional imports)
try:
    from scipy.spatial.distance import pdist, squareform
    from scipy.cluster.hierarchy import linkage, fcluster
    from scipy.optimize import minimize, nnls
    SCIPY_AVAILABLE = True
except ImportError:
    SCIPY_AVAILABLE = False
    logging.warning("Scipy not available. Some advanced features will be disabled.")

try:
    from sklearn.decomposition import PCA, FastICA
    from sklearn.manifold import TSNE, UMAP
    from sklearn.cluster import KMeans, DBSCAN
    from sklearn.metrics import silhouette_score
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False
    logging.warning("Scikit-learn not available. Some clustering features will use fallback implementations.")

# Visualization imports with error handling
try:
    import matplotlib.pyplot as plt
    import seaborn as sns
    VISUALIZATION_AVAILABLE = True
except ImportError:
    VISUALIZATION_AVAILABLE = False

# Configure module logger
logger = logging.getLogger(__name__)

# Constants and default parameters
DEFAULT_THOUGHT_SPACE_DIM = 1024
DEFAULT_CONCEPT_DIM = 768
DEFAULT_HIERARCHICAL_LEVELS = 3
DEFAULT_LEARNING_RATE = 1e-4
DEFAULT_BATCH_SIZE = 32
DEFAULT_EPSILON = 1e-8
DEFAULT_SIMILARITY_THRESHOLD = 0.7
DEFAULT_SPARSITY_LAMBDA = 0.1

# Mathematical constants
GOLDEN_RATIO = (1 + math.sqrt(5)) / 2
EULER_MASCHERONI = 0.5772156649015329

@dataclass
class ThoughtSpaceConfig:
    """Configuration class for hybrid ThoughtLatentSpace."""
    dimension: int = DEFAULT_THOUGHT_SPACE_DIM
    concept_dim: int = DEFAULT_CONCEPT_DIM
    hierarchical_levels: int = DEFAULT_HIERARCHICAL_LEVELS
    learning_rate: float = DEFAULT_LEARNING_RATE
    batch_size: int = DEFAULT_BATCH_SIZE
    device: str = 'cuda' if torch.cuda.is_available() else 'cpu'
    
    # Advanced features (optional)
    enable_advanced_math: bool = True
    enable_manifold_learning: bool = False
    enable_comprehensive_logging: bool = True
    enable_validation: bool = True
    
    # Training parameters
    weight_decay: float = 1e-5
    gradient_clip_norm: float = 1.0
    early_stopping_patience: int = 10
    
    # Numerical stability
    numerical_epsilon: float = DEFAULT_EPSILON
    
    def __post_init__(self):
        """Validate configuration parameters."""
        if self.dimension <= 0:
            raise ValueError("Dimension must be positive")
        if self.hierarchical_levels <= 0:
            raise ValueError("Hierarchical levels must be positive")
        if self.learning_rate <= 0:
            raise ValueError("Learning rate must be positive")

# ============================================================================
# Advanced Mathematical Utilities (Enhanced from both implementations)
# ============================================================================

class AdvancedMathOperations:
    """Advanced mathematical operations for the thought latent space."""
    
    @staticmethod
    def semantic_similarity(
        v1: torch.Tensor, 
        v2: torch.Tensor, 
        method: str = 'cosine'
    ) -> torch.Tensor:
        """
        Compute semantic similarity between concept vectors.
        Enhanced version supporting multiple methods.
        """
        if method == 'cosine':
            # Normalize vectors
            v1_norm = F.normalize(v1, p=2, dim=-1)
            v2_norm = F.normalize(v2, p=2, dim=-1)
            # Compute cosine similarity
            sim = torch.sum(v1_norm * v2_norm, dim=-1)
            # Scale to [0, 1]
            sim = (sim + 1) / 2
        elif method == 'euclidean':
            # Compute Euclidean distance
            dist = torch.sqrt(torch.sum((v1 - v2) ** 2, dim=-1) + DEFAULT_EPSILON)
            # Convert to similarity
            sim = torch.exp(-dist)
        elif method == 'manhattan':
            # Manhattan distance similarity
            dist = torch.sum(torch.abs(v1 - v2), dim=-1)
            sim = torch.exp(-dist)
        elif method == 'dot':
            # Dot product similarity
            sim = torch.sum(v1 * v2, dim=-1)
            sim = torch.sigmoid(sim)
        else:
            raise ValueError(f"Unknown similarity method: {method}")
        
        return sim
    
    @staticmethod
    def riemannian_distance(
        v1: torch.Tensor, 
        v2: torch.Tensor, 
        metric_tensor: Optional[torch.Tensor] = None
    ) -> torch.Tensor:
        """Compute Riemannian distance between two points."""
        if metric_tensor is None:
            return torch.norm(v2 - v1, p=2, dim=-1)
        
        diff = v2 - v1
        if len(diff.shape) == 1:
            diff = diff.unsqueeze(0)
        
        if len(metric_tensor.shape) == 2:
            metric_tensor = metric_tensor.unsqueeze(0).expand(diff.shape[0], -1, -1)
        
        quadratic_form = torch.bmm(
            torch.bmm(diff.unsqueeze(1), metric_tensor),
            diff.unsqueeze(-1)
        ).squeeze()
        
        return torch.sqrt(torch.clamp(quadratic_form, min=DEFAULT_EPSILON))
    
    @staticmethod
    def spherical_interpolation(
        v1: torch.Tensor, 
        v2: torch.Tensor, 
        t: Union[float, torch.Tensor]
    ) -> torch.Tensor:
        """Spherical linear interpolation (SLERP) between two unit vectors."""
        # Ensure unit vectors
        v1 = F.normalize(v1, p=2, dim=-1)
        v2 = F.normalize(v2, p=2, dim=-1)
        
        # Compute angle between vectors
        dot_product = torch.clamp(
            torch.sum(v1 * v2, dim=-1), 
            -1.0 + DEFAULT_EPSILON, 
            1.0 - DEFAULT_EPSILON
        )
        omega = torch.acos(torch.abs(dot_product))
        
        # Handle parallel vectors
        parallel_mask = omega < DEFAULT_EPSILON
        
        # SLERP formula
        sin_omega = torch.sin(omega)
        sin_omega = torch.clamp(sin_omega, min=DEFAULT_EPSILON)
        
        if isinstance(t, float):
            t = torch.tensor(t, device=v1.device, dtype=v1.dtype)
        
        while len(omega.shape) < len(v1.shape):
            omega = omega.unsqueeze(-1)
            sin_omega = sin_omega.unsqueeze(-1)
        
        coeff1 = torch.sin((1 - t) * omega) / sin_omega
        coeff2 = torch.sin(t * omega) / sin_omega
        
        result = coeff1 * v1 + coeff2 * v2
        
        # Handle parallel vectors with linear interpolation
        if parallel_mask.any():
            linear_result = (1 - t) * v1 + t * v2
            result = torch.where(parallel_mask.unsqueeze(-1), linear_result, result)
        
        return F.normalize(result, p=2, dim=-1)
    
    @staticmethod
    def concept_composition(
        concepts: List[torch.Tensor], 
        weights: Optional[List[float]] = None,
        method: str = 'weighted_sum'
    ) -> torch.Tensor:
        """Enhanced concept composition with multiple methods."""
        if len(concepts) == 0:
            raise ValueError("Cannot compose empty list of concepts")
        
        concepts_tensor = torch.stack(concepts)
        
        if method == 'weighted_sum':
            if weights is None:
                weights = torch.ones(len(concepts), device=concepts[0].device)
            else:
                weights = torch.tensor(weights, device=concepts[0].device)
            
            weights = weights / weights.sum()
            return (concepts_tensor * weights.view(-1, 1)).sum(dim=0)
        
        elif method == 'average':
            return concepts_tensor.mean(dim=0)
        
        elif method == 'geometric_mean':
            # Use log-space for numerical stability
            log_concepts = torch.log(torch.abs(concepts_tensor) + DEFAULT_EPSILON)
            if weights is not None:
                weights = torch.tensor(weights, device=concepts[0].device)
                weights = weights / weights.sum()
                weighted_log_mean = torch.sum(log_concepts * weights.view(-1, 1), dim=0)
            else:
                weighted_log_mean = log_concepts.mean(dim=0)
            return torch.exp(weighted_log_mean) * torch.sign(concepts_tensor[0])
        
        elif method == 'max':
            return torch.max(concepts_tensor, dim=0).values
        
        else:
            raise ValueError(f"Unknown composition method: {method}")
    
    @staticmethod
    def iterative_soft_thresholding(
        target: torch.Tensor,
        dictionary: torch.Tensor,
        lambda_l1: float,
        initial_weights: Optional[torch.Tensor] = None,
        max_iterations: int = 100,
        tolerance: float = 1e-4
    ) -> torch.Tensor:
        """Enhanced iterative soft thresholding algorithm."""
        if initial_weights is None:
            weights = torch.zeros(dictionary.shape[0], device=target.device)
        else:
            weights = initial_weights.clone()
        
        gram = torch.mm(dictionary, dictionary.t())
        corr = torch.mv(dictionary, target)
        lipschitz = torch.linalg.norm(gram, ord=2)
        step_size = 1.0 / (lipschitz + DEFAULT_EPSILON)
        
        prev_weights = weights.clone()
        for i in range(max_iterations):
            grad = torch.mv(gram, weights) - corr
            weights = weights - step_size * grad
            weights = F.softshrink(weights, lambda_l1 * step_size)
            
            if torch.norm(weights - prev_weights) < tolerance:
                break
            prev_weights = weights.clone()
        
        return weights

class InformationTheoreticMeasures:
    """Information-theoretic measures for concept analysis."""
    
    @staticmethod
    def jensen_shannon_divergence(p: torch.Tensor, q: torch.Tensor) -> torch.Tensor:
        """Compute Jensen-Shannon divergence between two probability distributions."""
        p = F.softmax(p, dim=-1)
        q = F.softmax(q, dim=-1)
        
        m = 0.5 * (p + q)
        
        p = torch.clamp(p, min=DEFAULT_EPSILON)
        q = torch.clamp(q, min=DEFAULT_EPSILON)
        m = torch.clamp(m, min=DEFAULT_EPSILON)
        
        kl_pm = torch.sum(p * torch.log(p / m), dim=-1)
        kl_qm = torch.sum(q * torch.log(q / m), dim=-1)
        
        js_div = 0.5 * kl_pm + 0.5 * kl_qm
        return js_div
    
    @staticmethod
    def mutual_information_estimate(x: torch.Tensor, y: torch.Tensor, bins: int = 50) -> float:
        """Estimate mutual information between two variables using histograms."""
        try:
            x_np = x.detach().cpu().numpy().flatten()
            y_np = y.detach().cpu().numpy().flatten()
            
            joint_hist, x_edges, y_edges = np.histogram2d(x_np, y_np, bins=bins)
            x_hist, _ = np.histogram(x_np, bins=x_edges)
            y_hist, _ = np.histogram(y_np, bins=y_edges)
            
            joint_hist = joint_hist + DEFAULT_EPSILON
            x_hist = x_hist + DEFAULT_EPSILON
            y_hist = y_hist + DEFAULT_EPSILON
            
            joint_prob = joint_hist / joint_hist.sum()
            x_prob = x_hist / x_hist.sum()
            y_prob = y_hist / y_hist.sum()
            
            mi = 0.0
            for i in range(len(x_prob)):
                for j in range(len(y_prob)):
                    if joint_prob[i, j] > 0:
                        mi += joint_prob[i, j] * np.log(joint_prob[i, j] / (x_prob[i] * y_prob[j]))
            
            return float(mi)
        except Exception as e:
            logger.warning(f"Failed to compute mutual information: {e}")
            return 0.0

# ============================================================================
# Enhanced Core Architecture (Based on Original with Improvements)
# ============================================================================

class ConceptEncoder(nn.Module):
    """
    Enhanced concept encoder based on original architecture.
    Maintains simplicity while adding essential improvements.
    """
    
    def __init__(
        self,
        input_dim: int,
        output_dim: int,
        hidden_dims: List[int] = None,
        num_levels: int = 3,
        dropout: float = 0.1,
        use_layer_norm: bool = True,
        activation: str = 'gelu'
    ):
        super().__init__()
        
        self.input_dim = input_dim
        self.output_dim = output_dim
        self.num_levels = num_levels
        
        # Default hidden dimensions
        if hidden_dims is None:
            hidden_dims = [
                min(input_dim * 2, 1024),
                min(input_dim * 4, 2048),
                output_dim * 2
            ]
        
        # Activation function
        if activation == 'gelu':
            self.activation = nn.GELU()
        elif activation == 'swish':
            self.activation = nn.SiLU()
        elif activation == 'relu':
            self.activation = nn.ReLU()
        else:
            self.activation = nn.GELU()  # Default
        
        # Core encoder network (simplified from original)
        layers = []
        
        # Input projection
        layers.append(nn.Linear(input_dim, hidden_dims[0]))
        if use_layer_norm:
            layers.append(nn.LayerNorm(hidden_dims[0]))
        layers.append(self.activation)
        if dropout > 0:
            layers.append(nn.Dropout(dropout))
        
        # Hidden layers
        for i in range(len(hidden_dims) - 1):
            layers.append(nn.Linear(hidden_dims[i], hidden_dims[i+1]))
            if use_layer_norm:
                layers.append(nn.LayerNorm(hidden_dims[i+1]))
            layers.append(self.activation)
            if dropout > 0:
                layers.append(nn.Dropout(dropout))
        
        self.encoder = nn.Sequential(*layers)
        
        # Level-specific projections (from original)
        level_dims = output_dim // num_levels
        leftover_dim = output_dim - (level_dims * num_levels)
        
        self.level_projections = nn.ModuleList()
        for i in range(num_levels):
            if i == num_levels - 1:
                level_dim = level_dims + leftover_dim
            else:
                level_dim = level_dims
            
            self.level_projections.append(
                nn.Linear(hidden_dims[-1], level_dim)
            )
        
        # Initialize weights
        self._init_weights()
        
        logger.info(f"Initialized ConceptEncoder with {num_levels} abstraction levels")
    
    def _init_weights(self):
        """Initialize weights for better training dynamics."""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight)
                if module.bias is not None:
                    nn.init.zeros_(module.bias)
    
    def forward(self, x: torch.Tensor, return_levels: bool = False) -> Union[torch.Tensor, Tuple[torch.Tensor, List[torch.Tensor]]]:
        """Forward pass through encoder."""
        try:
            # Get shared representation
            shared = self.encoder(x)
            
            # Get level-specific projections
            level_outputs = [proj(shared) for proj in self.level_projections]
            
            # Concatenate level outputs
            full_embedding = torch.cat(level_outputs, dim=1)
            
            if return_levels:
                return full_embedding, level_outputs
            else:
                return full_embedding
                
        except Exception as e:
            logger.error(f"Error in ConceptEncoder forward pass: {e}")
            raise

class ConceptDecoder(nn.Module):
    """
    Enhanced concept decoder based on original architecture.
    Maintains simplicity while adding essential improvements.
    """
    
    def __init__(
        self,
        input_dim: int,
        output_dim: int,
        hidden_dims: List[int] = None,
        num_levels: int = 3,
        dropout: float = 0.1,
        use_layer_norm: bool = True,
        activation: str = 'gelu'
    ):
        super().__init__()
        
        self.input_dim = input_dim
        self.output_dim = output_dim
        self.num_levels = num_levels
        
        # Default hidden dimensions (reverse of encoder)
        if hidden_dims is None:
            hidden_dims = [
                input_dim // 2,
                min(output_dim * 4, 2048),
                min(output_dim * 2, 1024)
            ]
        
        # Activation function
        if activation == 'gelu':
            self.activation = nn.GELU()
        elif activation == 'swish':
            self.activation = nn.SiLU()
        elif activation == 'relu':
            self.activation = nn.ReLU()
        else:
            self.activation = nn.GELU()  # Default
        
        # Level-specific projections (from original)
        level_dims = input_dim // num_levels
        leftover_dim = input_dim - (level_dims * num_levels)
        
        self.level_projections = nn.ModuleList()
        for i in range(num_levels):
            if i == num_levels - 1:
                level_dim = level_dims + leftover_dim
            else:
                level_dim = level_dims
            
            self.level_projections.append(
                nn.Linear(level_dim, hidden_dims[0])
            )
        
        # Fusion layer
        self.fusion = nn.Linear(hidden_dims[0] * num_levels, hidden_dims[0])
        
        # Core decoder network
        layers = []
        
        # Hidden layers
        for i in range(len(hidden_dims) - 1):
            layers.append(nn.Linear(hidden_dims[i], hidden_dims[i+1]))
            if use_layer_norm:
                layers.append(nn.LayerNorm(hidden_dims[i+1]))
            layers.append(self.activation)
            if dropout > 0:
                layers.append(nn.Dropout(dropout))
        
        # Output projection
        layers.append(nn.Linear(hidden_dims[-1], output_dim))
        
        self.decoder = nn.Sequential(*layers)
        
        # Initialize weights
        self._init_weights()
        
        logger.info(f"Initialized ConceptDecoder with {num_levels} abstraction levels")
    
    def _init_weights(self):
        """Initialize weights for better training dynamics."""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight)
                if module.bias is not None:
                    nn.init.zeros_(module.bias)
    
    def forward(self, z: torch.Tensor, level_inputs: Optional[List[torch.Tensor]] = None) -> torch.Tensor:
        """Forward pass through decoder."""
        try:
            # Split input into levels if not provided separately
            if level_inputs is None:
                level_dims = self.input_dim // self.num_levels
                leftover_dim = self.input_dim - (level_dims * self.num_levels)
                
                level_inputs = []
                start_idx = 0
                for i in range(self.num_levels):
                    if i == self.num_levels - 1:
                        end_idx = start_idx + level_dims + leftover_dim
                    else:
                        end_idx = start_idx + level_dims
                    
                    level_inputs.append(z[:, start_idx:end_idx])
                    start_idx = end_idx
            
            # Process each level
            level_outputs = [proj(level) for proj, level in zip(self.level_projections, level_inputs)]
            
            # Concatenate and fuse level outputs
            combined = torch.cat(level_outputs, dim=1)
            fused = self.fusion(combined)
            
            # Decode to concept representation
            return self.decoder(fused)
            
        except Exception as e:
            logger.error(f"Error in ConceptDecoder forward pass: {e}")
            raise

# ============================================================================
# Enhanced ThoughtVector (Based on Original with Selected Improvements)
# ============================================================================

class ThoughtVector:
    """
    Enhanced ThoughtVector maintaining simplicity while adding essential tracking.
    Based on original implementation with selected improvements.
    """
    
    def __init__(
        self,
        vector: torch.Tensor,
        metadata: Optional[Dict[str, Any]] = None,
        source: Optional[str] = None,
        abstraction_level: Optional[int] = None,
        confidence: Optional[float] = None,
        timestamp: Optional[float] = None
    ):
        """Initialize ThoughtVector with essential tracking."""
        self.vector = vector.detach().clone() if hasattr(vector, 'detach') else vector
        self.metadata = metadata or {}
        self.source = source or "unknown"
        self.abstraction_level = abstraction_level
        self.confidence = confidence
        self.timestamp = timestamp or time.time()
        self.id = str(uuid.uuid4())
        
        # Basic validation
        self._validate()
    
    def _validate(self):
        """Basic validation of ThoughtVector state."""
        if not isinstance(self.vector, torch.Tensor):
            raise TypeError("Vector must be a torch.Tensor")
        if len(self.vector.shape) > 2:
            raise ValueError("Vector must be 1D or 2D")
        if self.confidence is not None and not (0 <= self.confidence <= 1):
            raise ValueError("Confidence must be between 0 and 1")
    
    @property
    def shape(self) -> torch.Size:
        """Get the shape of the vector."""
        return self.vector.shape
    
    @property
    def device(self) -> torch.device:
        """Get the device of the vector."""
        return self.vector.device
    
    @property
    def dtype(self) -> torch.dtype:
        """Get the dtype of the vector."""
        return self.vector.dtype
    
    def __add__(self, other: 'ThoughtVector') -> 'ThoughtVector':
        """Add two ThoughtVectors."""
        if not isinstance(other, ThoughtVector):
            return NotImplemented
        
        if self.vector.shape != other.vector.shape:
            raise ValueError(f"Shape mismatch: {self.vector.shape} vs {other.vector.shape}")
        
        result_vector = self.vector + other.vector
        
        combined_metadata = {
            'operation': 'addition',
            'operands': [self.metadata, other.metadata],
            'source_ids': [self.id, other.id]
        }
        
        result_confidence = None
        if self.confidence is not None and other.confidence is not None:
            result_confidence = min(self.confidence, other.confidence) * 0.9
        
        return ThoughtVector(
            vector=result_vector,
            metadata=combined_metadata,
            source=f"add({self.source}, {other.source})",
            abstraction_level=max(self.abstraction_level or 0, other.abstraction_level or 0),
            confidence=result_confidence
        )
    
    def __sub__(self, other: 'ThoughtVector') -> 'ThoughtVector':
        """Subtract ThoughtVectors."""
        if not isinstance(other, ThoughtVector):
            return NotImplemented
        
        if self.vector.shape != other.vector.shape:
            raise ValueError(f"Shape mismatch: {self.vector.shape} vs {other.vector.shape}")
        
        result_vector = self.vector - other.vector
        
        combined_metadata = {
            'operation': 'subtraction',
            'minuend': self.metadata,
            'subtrahend': other.metadata,
            'source_ids': [self.id, other.id]
        }
        
        result_confidence = None
        if self.confidence is not None and other.confidence is not None:
            result_confidence = min(self.confidence, other.confidence) * 0.85
        
        return ThoughtVector(
            vector=result_vector,
            metadata=combined_metadata,
            source=f"sub({self.source}, {other.source})",
            abstraction_level=max(self.abstraction_level or 0, other.abstraction_level or 0),
            confidence=result_confidence
        )
    
    def __mul__(self, scalar: Union[float, int, torch.Tensor]) -> 'ThoughtVector':
        """Multiply ThoughtVector by scalar."""
        if not isinstance(scalar, (int, float, torch.Tensor)):
            return NotImplemented
        
        result_vector = self.vector * scalar
        
        metadata = {
            'operation': 'scalar_multiplication',
            'scalar': float(scalar) if isinstance(scalar, (int, float)) else scalar.item(),
            'original': self.metadata,
            'source_id': self.id
        }
        
        result_confidence = self.confidence
        if result_confidence is not None and abs(float(scalar)) < 1.0:
            result_confidence *= abs(float(scalar))
        
        return ThoughtVector(
            vector=result_vector,
            metadata=metadata,
            source=f"scale({self.source}, {scalar})",
            abstraction_level=self.abstraction_level,
            confidence=result_confidence
        )
    
    def __rmul__(self, scalar: Union[float, int, torch.Tensor]) -> 'ThoughtVector':
        """Right multiplication."""
        return self.__mul__(scalar)
    
    def __truediv__(self, scalar: Union[float, int, torch.Tensor]) -> 'ThoughtVector':
        """Division by scalar."""
        if isinstance(scalar, (int, float)) and scalar == 0:
            raise ValueError("Division by zero")
        
        if isinstance(scalar, torch.Tensor) and torch.any(scalar == 0):
            raise ValueError("Division by zero in tensor")
        
        return self.__mul__(1.0 / scalar)
    
    def similarity(self, other: 'ThoughtVector', method: str = 'cosine') -> float:
        """Compute similarity with another ThoughtVector."""
        if not isinstance(other, ThoughtVector):
            raise TypeError("Can only compute similarity with another ThoughtVector")
        
        sim = AdvancedMathOperations.semantic_similarity(
            self.vector.unsqueeze(0), 
            other.vector.unsqueeze(0), 
            method=method
        )
        
        return sim.item()
    
    def normalize(self, p: int = 2) -> 'ThoughtVector':
        """Normalize the ThoughtVector."""
        norm = torch.norm(self.vector, p=p)
        
        if norm < DEFAULT_EPSILON:
            return self.clone()
        
        normalized_vector = self.vector / norm
        
        metadata = {
            'operation': 'normalization',
            'norm_type': p,
            'original_norm': float(norm),
            'original': self.metadata,
            'source_id': self.id
        }
        
        return ThoughtVector(
            vector=normalized_vector,
            metadata=metadata,
            source=f"norm({self.source})",
            abstraction_level=self.abstraction_level,
            confidence=self.confidence
        )
    
    def to(self, device: Union[str, torch.device]) -> 'ThoughtVector':
        """Move ThoughtVector to specified device."""
        if isinstance(device, str):
            device = torch.device(device)
        
        if self.vector.device == device:
            return self
        
        moved_vector = self.vector.to(device)
        
        return ThoughtVector(
            vector=moved_vector,
            metadata=self.metadata.copy(),
            source=self.source,
            abstraction_level=self.abstraction_level,
            confidence=self.confidence
        )
    
    def detach(self) -> 'ThoughtVector':
        """Detach ThoughtVector from computational graph."""
        detached_vector = self.vector.detach()
        
        return ThoughtVector(
            vector=detached_vector,
            metadata=self.metadata.copy(),
            source=self.source,
            abstraction_level=self.abstraction_level,
            confidence=self.confidence
        )
    
    def clone(self) -> 'ThoughtVector':
        """Create a deep copy of the ThoughtVector."""
        cloned_vector = self.vector.clone()
        
        return ThoughtVector(
            vector=cloned_vector,
            metadata=self.metadata.copy(),
            source=self.source,
            abstraction_level=self.abstraction_level,
            confidence=self.confidence
        )
    
    def __repr__(self) -> str:
        """String representation of ThoughtVector."""
        return (f"ThoughtVector(id={self.id[:8]}..., "
                f"shape={list(self.vector.shape)}, "
                f"norm={float(torch.norm(self.vector)):.4f}, "
                f"confidence={self.confidence}, "
                f"level={self.abstraction_level}, "
                f"source={self.source})")

# ============================================================================
# Enhanced Error Handling and Logging System
# ============================================================================

class ThoughtSpaceLogger:
    """Comprehensive logging system for thought space operations."""
    
    def __init__(self, enable_comprehensive_logging: bool = True):
        self.enable_comprehensive_logging = enable_comprehensive_logging
        self.operation_stats = defaultdict(int)
        self.timing_stats = defaultdict(list)
        self.error_log = []
        self.performance_metrics = {}
        
        # Setup structured logging
        if enable_comprehensive_logging:
            self._setup_detailed_logging()
    
    def _setup_detailed_logging(self):
        """Setup detailed logging configuration."""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler('ultra_thought_space.log')
            ]
        )
    
    def log_operation(self, operation: str, duration: float, success: bool = True, **kwargs):
        """Log an operation with timing and success status."""
        self.operation_stats[operation] += 1
        self.timing_stats[operation].append(duration)
        
        if self.enable_comprehensive_logging:
            status = "SUCCESS" if success else "FAILED"
            logger.info(f"Operation {operation} {status} in {duration:.4f}s")
            
            if kwargs:
                logger.debug(f"Operation details: {kwargs}")
    
    def log_error(self, operation: str, error: Exception, **context):
        """Log an error with context information."""
        error_record = {
            'operation': operation,
            'error': str(error),
            'error_type': type(error).__name__,
            'timestamp': time.time(),
            'context': context
        }
        
        self.error_log.append(error_record)
        
        if self.enable_comprehensive_logging:
            logger.error(f"Error in {operation}: {error}")
            if context:
                logger.error(f"Error context: {context}")
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get comprehensive performance summary."""
        summary = {
            'operation_counts': dict(self.operation_stats),
            'average_timings': {
                op: np.mean(times) if times else 0
                for op, times in self.timing_stats.items()
            },
            'error_count': len(self.error_log),
            'recent_errors': self.error_log[-5:] if self.error_log else []
        }
        
        return summary

# ============================================================================
# Main Hybrid ThoughtLatentSpace Implementation
# ============================================================================

class ThoughtLatentSpace:
    """
    Hybrid ThoughtLatentSpace combining the best of both implementations:
    - Simple, robust core architecture (original)
    - Advanced training and mathematical operations (enhanced)
    - Production-ready error handling and logging
    - Scalable and maintainable design
    """
    
    def __init__(
        self,
        config: Optional[ThoughtSpaceConfig] = None,
        encoder: Optional[ConceptEncoder] = None,
        decoder: Optional[ConceptDecoder] = None,
        device: Optional[str] = None
    ):
        """Initialize the hybrid ThoughtLatentSpace."""
        # Configuration
        self.config = config or ThoughtSpaceConfig()
        if device is not None:
            self.config.device = device
        
        self.device = torch.device(self.config.device)
        
        # Core dimensions
        self.dimension = self.config.dimension
        self.concept_dim = self.config.concept_dim
        self.hierarchical_levels = self.config.hierarchical_levels
        
        # Initialize logging system
        self.logger_system = ThoughtSpaceLogger(self.config.enable_comprehensive_logging)
        
        # Initialize encoder and decoder (simplified architecture)
        if encoder is not None:
            self.encoder = encoder.to(self.device)
        else:
            self.encoder = ConceptEncoder(
                input_dim=self.concept_dim,
                output_dim=self.dimension,
                num_levels=self.hierarchical_levels
            ).to(self.device)
        
        if decoder is not None:
            self.decoder = decoder.to(self.device)
        else:
            self.decoder = ConceptDecoder(
                input_dim=self.dimension,
                output_dim=self.concept_dim,
                num_levels=self.hierarchical_levels
            ).to(self.device)
        
        # Simple concept registry (from original)
        self.concept_registry: Dict[str, ThoughtVector] = {}
        self.concept_clusters: Dict[str, torch.Tensor] = {}
        self.relation_vectors: Dict[str, torch.Tensor] = {}
        
        # Advanced features (optional based on config)
        if self.config.enable_manifold_learning:
            self.manifold_structure = self._initialize_manifold_structure()
        else:
            self.manifold_structure = None
        
        # Training state
        self.training_history = {
            'loss': [],
            'reconstruction_loss': [],
            'contrastive_loss': [],
            'val_loss': [],
            'epoch': 0
        }
        
        # Initialize cluster centers for organized thought space
        self.cluster_centers = self._initialize_cluster_centers()
        
        logger.info(f"Initialized Hybrid ThoughtLatentSpace with dimension={self.dimension}, "
                   f"levels={self.hierarchical_levels}, device={self.device}")
    
    def _initialize_cluster_centers(self) -> torch.Tensor:
        """Initialize cluster centers in a structured pattern."""
        num_clusters = min(100, int(math.sqrt(self.dimension)))
        
        # Use geometric arrangement for better distribution
        if num_clusters <= 3:
            angles = torch.linspace(0, 2 * math.pi, num_clusters + 1)[:-1]
            centers = torch.stack([torch.cos(angles), torch.sin(angles)], dim=1)
            centers = F.pad(centers, (0, self.dimension - 2))
        else:
            # Random initialization with normalization
            centers = torch.randn(num_clusters, self.dimension)
        
        return F.normalize(centers, p=2, dim=1).to(self.device)
    
    def _initialize_manifold_structure(self) -> Dict[str, Any]:
        """Initialize manifold structure for advanced mathematical operations."""
        return {
            'cluster_centers': self.cluster_centers,
            'metric_tensor': torch.eye(self.dimension, device=self.device),
            'curvature_tensor': torch.zeros(
                (self.dimension, self.dimension, self.dimension, self.dimension),
                device=self.device
            )
        }
    
    def encode(
        self,
        concept_data: Union[str, torch.Tensor, Dict, List],
        external_encoder: Optional[Callable] = None,
        return_thought_vectors: bool = True,
        confidence: Optional[float] = None
    ) -> Union[torch.Tensor, ThoughtVector, List[ThoughtVector]]:
        """
        Encode concepts into the thought latent space with comprehensive error handling.
        """
        start_time = time.time()
        
        try:
            # Handle string keys from registry
            if isinstance(concept_data, str):
                if concept_data in self.concept_registry:
                    result = self.concept_registry[concept_data]
                    self.logger_system.log_operation('encode_from_registry', time.time() - start_time)
                    return result
                
                # Use external encoder for text
                if external_encoder is not None:
                    input_tensor = external_encoder(concept_data)
                    if isinstance(input_tensor, np.ndarray):
                        input_tensor = torch.from_numpy(input_tensor)
                    input_tensor = input_tensor.to(self.device)
                else:
                    raise ValueError(f"Cannot encode text '{concept_data}' without external encoder")
            
            # Handle tensor input
            elif isinstance(concept_data, torch.Tensor):
                input_tensor = concept_data.to(self.device)
            
            # Handle dictionary input
            elif isinstance(concept_data, dict):
                if 'embedding' in concept_data:
                    input_tensor = torch.tensor(concept_data['embedding'], device=self.device)
                elif 'features' in concept_data:
                    input_tensor = torch.tensor(concept_data['features'], device=self.device)
                else:
                    raise ValueError("Dictionary must contain 'embedding' or 'features' key")
            
            # Handle list input
            elif isinstance(concept_data, list):
                if all(isinstance(x, torch.Tensor) for x in concept_data):
                    input_tensor = torch.stack([x.to(self.device) for x in concept_data])
                elif all(isinstance(x, str) for x in concept_data):
                    if external_encoder is None:
                        raise ValueError("Cannot encode strings without external encoder")
                    
                    results = []
                    for item in concept_data:
                        result = self.encode(
                            item, external_encoder, return_thought_vectors, confidence
                        )
                        results.append(result)
                    self.logger_system.log_operation('encode_list', time.time() - start_time)
                    return results
                else:
                    raise TypeError("List must contain all tensors or all strings")
            
            else:
                raise TypeError(f"Unsupported input type: {type(concept_data)}")
            
            # Ensure proper tensor shape
            if len(input_tensor.shape) == 1:
                input_tensor = input_tensor.unsqueeze(0)
            
            # Validate input dimensions
            if input_tensor.shape[-1] != self.concept_dim:
                raise ValueError(f"Input dimension {input_tensor.shape[-1]} != expected {self.concept_dim}")
            
            # Encode using hierarchical encoder
            with torch.no_grad():
                self.encoder.eval()
                latent_representations, level_outputs = self.encoder(
                    input_tensor, return_levels=True
                )
            
            # Create ThoughtVector(s) if requested
            if return_thought_vectors:
                thought_vectors = []
                
                for i in range(latent_representations.shape[0]):
                    metadata = {
                        'encoding_method': 'hierarchical_encoder',
                        'input_type': type(concept_data).__name__,
                        'abstraction_levels': [level.shape[-1] for level in level_outputs],
                        'encoding_timestamp': time.time(),
                        'confidence': confidence
                    }
                    
                    if isinstance(concept_data, str):
                        source = f"encoded:{concept_data}"
                    else:
                        source = f"encoded:tensor[{i}]"
                    
                    tv = ThoughtVector(
                        vector=latent_representations[i],
                        metadata=metadata,
                        source=source,
                        confidence=confidence,
                        abstraction_level=self.hierarchical_levels
                    )
                    
                    thought_vectors.append(tv)
                
                # Return single ThoughtVector or list
                if len(thought_vectors) == 1 and not isinstance(concept_data, list):
                    result = thought_vectors[0]
                else:
                    result = thought_vectors
            else:
                # Return tensor(s)
                if latent_representations.shape[0] == 1 and not isinstance(concept_data, list):
                    result = latent_representations.squeeze(0)
                else:
                    result = latent_representations
            
            # Log successful operation
            self.logger_system.log_operation('encode', time.time() - start_time)
            return result
            
        except Exception as e:
            self.logger_system.log_error('encode', e, input_type=type(concept_data).__name__)
            raise
    
    def decode(
        self,
        latent_vectors: Union[torch.Tensor, ThoughtVector, List[ThoughtVector]]
    ) -> torch.Tensor:
        """
        Decode latent vectors back to concept representations with error handling.
        """
        start_time = time.time()
        
        try:
            # Handle different input types
            if isinstance(latent_vectors, ThoughtVector):
                vectors = latent_vectors.vector.unsqueeze(0)
            elif isinstance(latent_vectors, list) and all(isinstance(v, ThoughtVector) for v in latent_vectors):
                vectors = torch.stack([v.vector for v in latent_vectors])
            else:
                vectors = latent_vectors
            
            # Ensure proper tensor shape
            if len(vectors.shape) == 1:
                vectors = vectors.unsqueeze(0)
            
            # Validate dimensions
            if vectors.shape[-1] != self.dimension:
                raise ValueError(f"Latent dimension {vectors.shape[-1]} != expected {self.dimension}")
            
            # Decode using adaptive decoder
            with torch.no_grad():
                self.decoder.eval()
                decoded_concepts = self.decoder(vectors)
            
            # Return results
            if decoded_concepts.shape[0] == 1:
                decoded_concepts = decoded_concepts.squeeze(0)
            
            self.logger_system.log_operation('decode', time.time() - start_time)
            return decoded_concepts
            
        except Exception as e:
            self.logger_system.log_error('decode', e)
            raise
    
    def compute_semantic_similarity(
        self,
        concept1: Union[str, torch.Tensor, ThoughtVector],
        concept2: Union[str, torch.Tensor, ThoughtVector],
        method: str = 'cosine'
    ) -> torch.Tensor:
        """Compute semantic similarity with advanced methods."""
        try:
            v1 = self._extract_vector(concept1)
            v2 = self._extract_vector(concept2)
            
            if self.config.enable_advanced_math and self.manifold_structure:
                # Use advanced Riemannian distance if available
                distance = AdvancedMathOperations.riemannian_distance(
                    v1, v2, self.manifold_structure['metric_tensor']
                )
                similarity = torch.exp(-distance)
            else:
                # Use standard similarity methods
                similarity = AdvancedMathOperations.semantic_similarity(v1, v2, method)
            
            return similarity
            
        except Exception as e:
            self.logger_system.log_error('compute_similarity', e)
            raise
    
    def perform_analogy(
        self,
        a: Union[str, torch.Tensor, ThoughtVector],
        b: Union[str, torch.Tensor, ThoughtVector],
        c: Union[str, torch.Tensor, ThoughtVector],
        method: str = 'linear',
        return_thought_vector: bool = True
    ) -> Union[torch.Tensor, ThoughtVector]:
        """
        Perform analogical reasoning: A is to B as C is to D.
        Enhanced with multiple methods.
        """
        try:
            # Extract vectors
            va = self._extract_vector(a)
            vb = self._extract_vector(b)
            vc = self._extract_vector(c)
            
            if method == 'linear':
                # Classic vector arithmetic: D = B - A + C
                vd = vb - va + vc
            elif method == 'spherical':
                # Use spherical interpolation for analogy
                relation_magnitude = torch.norm(vb - va)
                direction = F.normalize(vc, p=2, dim=0)
                vd = vc + relation_magnitude * direction
            else:
                # Default to linear
                vd = vb - va + vc
            
            # Normalize result
            vd = F.normalize(vd, p=2, dim=0)
            
            if return_thought_vector:
                metadata = {
                    'operation': 'analogy',
                    'method': method,
                    'analogy_triple': [
                        getattr(a, 'source', str(a)),
                        getattr(b, 'source', str(b)),
                        getattr(c, 'source', str(c))
                    ]
                }
                
                return ThoughtVector(
                    vector=vd,
                    metadata=metadata,
                    source=f"analogy({a}, {b}, {c})",
                    abstraction_level=max(
                        getattr(a, 'abstraction_level', 0) or 0,
                        getattr(b, 'abstraction_level', 0) or 0,
                        getattr(c, 'abstraction_level', 0) or 0
                    )
                )
            else:
                return vd
                
        except Exception as e:
            self.logger_system.log_error('perform_analogy', e)
            raise
    
    def compose_concepts(
        self,
        concepts: List[Union[str, torch.Tensor, ThoughtVector]],
        weights: Optional[List[float]] = None,
        method: str = 'weighted_average',
        return_thought_vector: bool = True
    ) -> Union[torch.Tensor, ThoughtVector]:
        """Enhanced concept composition with multiple methods."""
        try:
            if not concepts:
                raise ValueError("Cannot compose empty list of concepts")
            
            # Extract vectors
            vectors = [self._extract_vector(concept) for concept in concepts]
            
            # Use enhanced composition method
            result = AdvancedMathOperations.concept_composition(
                vectors, weights, method
            )
            
            if return_thought_vector:
                metadata = {
                    'operation': 'composition',
                    'method': method,
                    'components': [getattr(concept, 'source', str(concept)) for concept in concepts],
                    'weights': weights
                }
                
                return ThoughtVector(
                    vector=result,
                    metadata=metadata,
                    source=f"compose({len(concepts)} concepts)",
                    abstraction_level=max(
                        (getattr(concept, 'abstraction_level', 0) or 0 for concept in concepts),
                        default=0
                    )
                )
            else:
                return result
                
        except Exception as e:
            self.logger_system.log_error('compose_concepts', e)
            raise
    
    def decompose_concept(
        self,
        concept: Union[str, torch.Tensor, ThoughtVector],
        basis_concepts: Union[Dict[str, ThoughtVector], List[ThoughtVector]],
        method: str = 'sparse_coding',
        sparsity_lambda: float = None,
        max_components: int = 10
    ) -> Dict[Union[str, int], float]:
        """Enhanced concept decomposition with multiple methods."""
        try:
            # Extract concept vector
            concept_vector = self._extract_vector(concept)
            
            # Prepare basis matrix
            if isinstance(basis_concepts, dict):
                basis_names = list(basis_concepts.keys())
                basis_matrix = torch.stack([
                    self._extract_vector(basis_concepts[name]) for name in basis_names
                ])
            else:
                basis_names = list(range(len(basis_concepts)))
                basis_matrix = torch.stack([
                    self._extract_vector(concept) for concept in basis_concepts
                ])
            
            # Transpose for proper matrix operations
            basis_matrix = basis_matrix.T
            
            if method == 'sparse_coding':
                sparsity_lambda = sparsity_lambda or DEFAULT_SPARSITY_LAMBDA
                weights = AdvancedMathOperations.iterative_soft_thresholding(
                    concept_vector, basis_matrix, sparsity_lambda
                )
            elif method == 'nnls' and SCIPY_AVAILABLE:
                # Non-negative least squares
                concept_np = concept_vector.detach().cpu().numpy()
                basis_np = basis_matrix.detach().cpu().numpy()
                weights_np, _ = nnls(basis_np, concept_np)
                weights = torch.from_numpy(weights_np).to(self.device)
            else:
                # Fallback to least squares
                weights = torch.linalg.lstsq(basis_matrix, concept_vector).solution
            
            # Convert to dictionary and filter top components
            result = {}
            for i, weight in enumerate(weights):
                if abs(weight) > DEFAULT_EPSILON:
                    result[basis_names[i]] = float(weight)
            
            # Sort by absolute weight and take top components
            sorted_components = sorted(result.items(), key=lambda x: abs(x[1]), reverse=True)
            
            return dict(sorted_components[:max_components])
            
        except Exception as e:
            self.logger_system.log_error('decompose_concept', e)
            raise
    
    def interpolate_concepts(
        self,
        concept1: Union[str, torch.Tensor, ThoughtVector],
        concept2: Union[str, torch.Tensor, ThoughtVector],
        steps: int = 10,
        method: str = 'spherical',
        return_thought_vectors: bool = True
    ) -> List[Union[torch.Tensor, ThoughtVector]]:
        """Enhanced concept interpolation with multiple methods."""
        try:
            # Extract vectors
            v1 = self._extract_vector(concept1)
            v2 = self._extract_vector(concept2)
            
            # Generate interpolation parameters
            alphas = torch.linspace(0, 1, steps, device=self.device)
            
            interpolated_vectors = []
            
            for alpha in alphas:
                if method == 'linear':
                    interpolated = (1 - alpha) * v1 + alpha * v2
                elif method == 'spherical':
                    interpolated = AdvancedMathOperations.spherical_interpolation(v1, v2, alpha)
                else:
                    # Default to linear
                    interpolated = (1 - alpha) * v1 + alpha * v2
                
                interpolated_vectors.append(interpolated)
            
            if return_thought_vectors:
                thought_vectors = []
                for i, vector in enumerate(interpolated_vectors):
                    alpha = float(alphas[i])
                    
                    metadata = {
                        'operation': 'interpolation',
                        'method': method,
                        'alpha': alpha,
                        'endpoints': [
                            getattr(concept1, 'source', str(concept1)),
                            getattr(concept2, 'source', str(concept2))
                        ]
                    }
                    
                    tv = ThoughtVector(
                        vector=vector,
                        metadata=metadata,
                        source=f"interp({concept1}, {concept2}, {alpha:.3f})",
                        abstraction_level=max(
                            getattr(concept1, 'abstraction_level', 0) or 0,
                            getattr(concept2, 'abstraction_level', 0) or 0
                        )
                    )
                    
                    thought_vectors.append(tv)
                
                return thought_vectors
            else:
                return interpolated_vectors
                
        except Exception as e:
            self.logger_system.log_error('interpolate_concepts', e)
            raise
    
    def find_nearest_concepts(
        self,
        query_concept: Union[str, torch.Tensor, ThoughtVector],
        concept_pool: Optional[Union[Dict[str, ThoughtVector], List[str]]] = None,
        k: int = 5,
        similarity_method: str = 'cosine',
        return_similarities: bool = False
    ) -> Union[List[Union[str, ThoughtVector]], Tuple[List[Union[str, ThoughtVector]], List[float]]]:
        """Find k nearest concepts with enhanced similarity methods."""
        try:
            # Extract query vector
            query_vector = self._extract_vector(query_concept)
            
            # Determine concept pool
            if concept_pool is None:
                concept_pool = self.concept_registry
            
            if isinstance(concept_pool, list):
                concept_pool = {name: self.concept_registry[name] for name in concept_pool if name in self.concept_registry}
            
            if not concept_pool:
                raise ValueError("No concepts available for search")
            
            # Compute similarities
            similarities = {}
            
            for name, concept_vector in concept_pool.items():
                if isinstance(concept_vector, ThoughtVector):
                    vector = concept_vector.vector
                else:
                    vector = concept_vector
                
                similarity = AdvancedMathOperations.semantic_similarity(
                    query_vector.unsqueeze(0), vector.unsqueeze(0), similarity_method
                ).item()
                
                similarities[name] = similarity
            
            # Sort by similarity and get top k
            sorted_concepts = sorted(similarities.items(), key=lambda x: x[1], reverse=True)[:k]
            
            # Extract results
            nearest_concepts = []
            similarity_scores = []
            
            for name, score in sorted_concepts:
                if isinstance(concept_pool[name], ThoughtVector):
                    nearest_concepts.append(concept_pool[name])
                else:
                    nearest_concepts.append(name)
                similarity_scores.append(score)
            
            if return_similarities:
                return nearest_concepts, similarity_scores
            else:
                return nearest_concepts
                
        except Exception as e:
            self.logger_system.log_error('find_nearest_concepts', e)
            raise
    
    def cluster_concepts(
        self,
        concepts: Optional[Union[Dict[str, ThoughtVector], List[str]]] = None,
        num_clusters: int = 5,
        method: str = 'kmeans',
        return_cluster_centers: bool = False
    ) -> Union[Dict[str, List[str]], Tuple[Dict[str, List[str]], torch.Tensor]]:
        """Enhanced concept clustering with multiple algorithms."""
        try:
            # Prepare concept data
            if concepts is None:
                concepts = self.concept_registry
            
            if isinstance(concepts, list):
                concepts = {name: self.concept_registry[name] for name in concepts if name in self.concept_registry}
            
            concept_names = list(concepts.keys())
            concept_vectors = torch.stack([
                self._extract_vector(concept) for concept in concepts.values()
            ])
            
            # Perform clustering
            if method == 'kmeans':
                cluster_labels, cluster_centers = self._kmeans_clustering(concept_vectors, num_clusters)
            elif method == 'dbscan' and SKLEARN_AVAILABLE:
                cluster_labels = self._dbscan_clustering(concept_vectors)
                num_clusters = len(set(cluster_labels)) - (1 if -1 in cluster_labels else 0)
                cluster_centers = self._compute_cluster_centers(concept_vectors, cluster_labels, num_clusters)
            else:
                # Fallback to kmeans
                cluster_labels, cluster_centers = self._kmeans_clustering(concept_vectors, num_clusters)
            
            # Group concepts by cluster
            clusters = {}
            for i in range(num_clusters):
                cluster_name = f"cluster_{i}"
                cluster_concepts = [concept_names[j] for j, label in enumerate(cluster_labels) if label == i]
                clusters[cluster_name] = cluster_concepts
            
            if return_cluster_centers:
                return clusters, cluster_centers
            else:
                return clusters
                
        except Exception as e:
            self.logger_system.log_error('cluster_concepts', e)
            raise
    
    def _kmeans_clustering(self, vectors: torch.Tensor, num_clusters: int) -> Tuple[List[int], torch.Tensor]:
        """Enhanced K-means clustering implementation."""
        n_vectors, dim = vectors.shape
        centers = vectors[torch.randperm(n_vectors)[:num_clusters]].clone()
        
        max_iterations = 100
        tolerance = 1e-4
        
        for iteration in range(max_iterations):
            # Compute distances to all centers
            distances = torch.cdist(vectors, centers)
            
            # Assign points to nearest center
            labels = torch.argmin(distances, dim=1)
            
            # Update centers
            new_centers = torch.zeros_like(centers)
            for k in range(num_clusters):
                mask = labels == k
                if mask.sum() > 0:
                    new_centers[k] = vectors[mask].mean(dim=0)
                else:
                    new_centers[k] = centers[k]
            
            # Check convergence
            center_shift = torch.norm(new_centers - centers)
            if center_shift < tolerance:
                break
            
            centers = new_centers
        
        return labels.cpu().numpy().tolist(), centers
    
    def _dbscan_clustering(self, vectors: torch.Tensor, eps: float = 0.5, min_samples: int = 5) -> List[int]:
        """DBSCAN clustering using sklearn if available."""
        if SKLEARN_AVAILABLE:
            from sklearn.cluster import DBSCAN
            vectors_np = vectors.detach().cpu().numpy()
            dbscan = DBSCAN(eps=eps, min_samples=min_samples, metric='cosine')
            labels = dbscan.fit_predict(vectors_np)
            return labels.tolist()
        else:
            # Fallback to simple distance-based clustering
            return self._simple_distance_clustering(vectors, eps)
    
    def _simple_distance_clustering(self, vectors: torch.Tensor, eps: float) -> List[int]:
        """Simple distance-based clustering fallback."""
        n_vectors = vectors.shape[0]
        labels = [-1] * n_vectors
        cluster_id = 0
        
        for i in range(n_vectors):
            if labels[i] != -1:
                continue
            
            distances = torch.norm(vectors - vectors[i].unsqueeze(0), dim=1)
            neighbors = torch.where(distances <= eps)[0].tolist()
            
            if len(neighbors) >= 2:
                for neighbor_idx in neighbors:
                    if labels[neighbor_idx] == -1:
                        labels[neighbor_idx] = cluster_id
                cluster_id += 1
        
        return labels
    
    def _compute_cluster_centers(self, vectors: torch.Tensor, labels: List[int], num_clusters: int) -> torch.Tensor:
        """Compute cluster centers from labels."""
        centers = torch.zeros(num_clusters, vectors.shape[1], device=vectors.device)
        
        for k in range(num_clusters):
            mask = torch.tensor([label == k for label in labels], device=vectors.device)
            if mask.sum() > 0:
                centers[k] = vectors[mask].mean(dim=0)
        
        return centers
    
    def _extract_vector(self, concept: Union[str, torch.Tensor, ThoughtVector]) -> torch.Tensor:
        """Extract tensor from various concept representations."""
        if isinstance(concept, str):
            if concept in self.concept_registry:
                return self.concept_registry[concept].vector
            else:
                raise ValueError(f"Unknown concept: {concept}")
        elif isinstance(concept, ThoughtVector):
            return concept.vector
        else:
            return concept.to(self.device)
    
    def register_concept(
        self,
        name: str,
        concept: Union[torch.Tensor, ThoughtVector],
        metadata: Optional[Dict[str, Any]] = None
    ) -> ThoughtVector:
        """Register a concept in the thought space."""
        try:
            if isinstance(concept, torch.Tensor):
                thought_vector = ThoughtVector(
                    vector=concept.to(self.device),
                    metadata=metadata or {},
                    source=f"registered:{name}"
                )
            else:
                thought_vector = concept.to(self.device)
                if metadata:
                    thought_vector.metadata.update(metadata)
            
            self.concept_registry[name] = thought_vector
            
            logger.info(f"Registered concept '{name}' in thought space")
            return thought_vector
            
        except Exception as e:
            self.logger_system.log_error('register_concept', e, concept_name=name)
            raise
    
    def unregister_concept(self, name: str) -> bool:
        """Remove a concept from the thought space."""
        try:
            if name in self.concept_registry:
                del self.concept_registry[name]
                logger.info(f"Unregistered concept '{name}' from thought space")
                return True
            return False
            
        except Exception as e:
            self.logger_system.log_error('unregister_concept', e, concept_name=name)
            raise
    
    def train(
        self,
        dataset: Union[torch.Tensor, DataLoader],
        num_epochs: int = 100,
        learning_rate: float = None,
        batch_size: int = None,
        validation_split: float = 0.1,
        early_stopping_patience: int = None,
        save_best_model: bool = True,
        save_path: Optional[str] = None
    ) -> Dict[str, List[float]]:
        """
        Enhanced training system with validation, early stopping, and advanced features.
        """
        try:
            # Set parameters from config if not provided
            learning_rate = learning_rate or self.config.learning_rate
            batch_size = batch_size or self.config.batch_size
            early_stopping_patience = early_stopping_patience or self.config.early_stopping_patience
            
            # Prepare data
            if isinstance(dataset, torch.Tensor):
                if self.config.enable_validation and validation_split > 0:
                    dataset_size = len(dataset)
                    val_size = int(validation_split * dataset_size)
                    train_size = dataset_size - val_size
                    
                    train_dataset, val_dataset = torch.utils.data.random_split(
                        TensorDataset(dataset), [train_size, val_size]
                    )
                    
                    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
                    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)
                else:
                    train_loader = DataLoader(TensorDataset(dataset), batch_size=batch_size, shuffle=True)
                    val_loader = None
            else:
                train_loader = dataset
                val_loader = None
            
            # Set up optimizer and scheduler
            optimizer = AdamW(
                list(self.encoder.parameters()) + list(self.decoder.parameters()),
                lr=learning_rate,
                weight_decay=self.config.weight_decay
            )
            
            scheduler = CosineAnnealingLR(optimizer, T_max=num_epochs)
            
            # Training history
            history = {
                'train_loss': [],
                'train_reconstruction_loss': [],
                'train_contrastive_loss': [],
                'val_loss': [],
                'val_reconstruction_loss': [],
                'learning_rate': []
            }
            
            best_val_loss = float('inf')
            patience_counter = 0
            
            logger.info(f"Starting training for {num_epochs} epochs...")
            
            # Training loop
            for epoch in range(num_epochs):
                start_time = time.time()
                
                # Training phase
                self.encoder.train()
                self.decoder.train()
                
                train_metrics = self._train_epoch(train_loader, optimizer)
                
                # Validation phase
                val_metrics = {}
                if val_loader is not None:
                    self.encoder.eval()
                    self.decoder.eval()
                    val_metrics = self._validate_epoch(val_loader)
                
                # Update learning rate
                scheduler.step()
                
                # Record metrics
                for key, value in train_metrics.items():
                    history[f'train_{key}'].append(value)
                
                for key, value in val_metrics.items():
                    history[f'val_{key}'].append(value)
                
                history['learning_rate'].append(optimizer.param_groups[0]['lr'])
                
                # Early stopping and model saving
                current_val_loss = val_metrics.get('loss', train_metrics['loss'])
                
                if current_val_loss < best_val_loss:
                    best_val_loss = current_val_loss
                    patience_counter = 0
                    
                    if save_best_model and save_path:
                        self.save_thought_space(save_path)
                else:
                    patience_counter += 1
                
                # Log progress
                epoch_time = time.time() - start_time
                if epoch % 10 == 0 or epoch == num_epochs - 1:
                    logger.info(
                        f"Epoch {epoch+1}/{num_epochs}: "
                        f"train_loss={train_metrics['loss']:.6f}, "
                        f"val_loss={current_val_loss:.6f}, "
                        f"lr={optimizer.param_groups[0]['lr']:.6f}, "
                        f"time={epoch_time:.2f}s"
                    )
                
                # Early stopping check
                if self.config.enable_validation and patience_counter >= early_stopping_patience:
                    logger.info(f"Early stopping at epoch {epoch+1}")
                    break
            
            # Update training history
            self.training_history['epoch'] = epoch + 1
            for key, values in history.items():
                if key in self.training_history:
                    self.training_history[key].extend(values)
                else:
                    self.training_history[key] = values
            
            self.logger_system.log_operation('train', time.time() - start_time, num_epochs=num_epochs)
            
            return history
            
        except Exception as e:
            self.logger_system.log_error('train', e)
            raise
    
    def _train_epoch(self, train_loader: DataLoader, optimizer: torch.optim.Optimizer) -> Dict[str, float]:
        """Enhanced training epoch with multiple loss components."""
        total_loss = 0.0
        total_reconstruction_loss = 0.0
        total_contrastive_loss = 0.0
        num_batches = 0
        
        for batch in train_loader:
            if isinstance(batch, (list, tuple)):
                x = batch[0].to(self.device)
            else:
                x = batch.to(self.device)
            
            # Forward pass
            latent_representations, level_outputs = self.encoder(x, return_levels=True)
            reconstructed = self.decoder(latent_representations)
            
            # Compute losses
            reconstruction_loss = F.mse_loss(reconstructed, x)
            
            # Contrastive loss for structure preservation
            contrastive_loss = self._compute_contrastive_loss(latent_representations, x)
            
            # Total loss
            total_loss_batch = reconstruction_loss + 0.1 * contrastive_loss
            
            # Backward pass
            optimizer.zero_grad()
            total_loss_batch.backward()
            
            # Gradient clipping
            torch.nn.utils.clip_grad_norm_(
                list(self.encoder.parameters()) + list(self.decoder.parameters()),
                self.config.gradient_clip_norm
            )
            
            optimizer.step()
            
            # Accumulate losses
            total_loss += total_loss_batch.item()
            total_reconstruction_loss += reconstruction_loss.item()
            total_contrastive_loss += contrastive_loss.item()
            num_batches += 1
        
        return {
            'loss': total_loss / max(num_batches, 1),
            'reconstruction_loss': total_reconstruction_loss / max(num_batches, 1),
            'contrastive_loss': total_contrastive_loss / max(num_batches, 1)
        }
    
    def _validate_epoch(self, val_loader: DataLoader) -> Dict[str, float]:
        """Enhanced validation epoch."""
        total_loss = 0.0
        total_reconstruction_loss = 0.0
        num_batches = 0
        
        with torch.no_grad():
            for batch in val_loader:
                if isinstance(batch, (list, tuple)):
                    x = batch[0].to(self.device)
                else:
                    x = batch.to(self.device)
                
                # Forward pass
                latent_representations = self.encoder(x)
                reconstructed = self.decoder(latent_representations)
                
                # Compute losses
                reconstruction_loss = F.mse_loss(reconstructed, x)
                
                total_loss += reconstruction_loss.item()
                total_reconstruction_loss += reconstruction_loss.item()
                num_batches += 1
        
        return {
            'loss': total_loss / max(num_batches, 1),
            'reconstruction_loss': total_reconstruction_loss / max(num_batches, 1)
        }
    
    def _compute_contrastive_loss(self, latent_vectors: torch.Tensor, original_vectors: torch.Tensor) -> torch.Tensor:
        """Compute contrastive loss for structure preservation."""
        # Compute similarity matrices
        original_sim = F.cosine_similarity(
            original_vectors.unsqueeze(1), 
            original_vectors.unsqueeze(0), 
            dim=2
        )
        
        latent_sim = F.cosine_similarity(
            latent_vectors.unsqueeze(1), 
            latent_vectors.unsqueeze(0), 
            dim=2
        )
        
        # Contrastive loss: preserve similarity structure
        contrastive_loss = F.mse_loss(latent_sim, original_sim)
        
        return contrastive_loss
    
    def save_thought_space(self, filepath: str, include_models: bool = True) -> None:
        """Save the complete thought space to disk with comprehensive data."""
        try:
            state_dict = {
                'config': {
                    'dimension': self.dimension,
                    'concept_dim': self.concept_dim,
                    'hierarchical_levels': self.hierarchical_levels,
                    'device': str(self.device)
                },
                'concept_registry': {
                    name: {
                        'vector': tv.vector.cpu(),
                        'metadata': tv.metadata,
                        'source': tv.source,
                        'abstraction_level': tv.abstraction_level,
                        'confidence': tv.confidence
                    }
                    for name, tv in self.concept_registry.items()
                },
                'concept_clusters': {k: v.cpu() for k, v in self.concept_clusters.items()},
                'relation_vectors': {k: v.cpu() for k, v in self.relation_vectors.items()},
                'training_history': self.training_history,
                'performance_stats': self.logger_system.get_performance_summary(),
                'timestamp': time.time()
            }
            
            if include_models:
                state_dict['encoder_state'] = self.encoder.state_dict()
                state_dict['decoder_state'] = self.decoder.state_dict()
            
            os.makedirs(os.path.dirname(filepath), exist_ok=True)
            torch.save(state_dict, filepath)
            
            logger.info(f"Saved ThoughtLatentSpace to {filepath}")
            
        except Exception as e:
            self.logger_system.log_error('save_thought_space', e, filepath=filepath)
            raise
    
    def load_thought_space(self, filepath: str, load_models: bool = True) -> None:
        """Load thought space from disk with comprehensive restoration."""
        try:
            state_dict = torch.load(filepath, map_location=self.device)
            
            # Restore configuration
            config_data = state_dict.get('config', {})
            self.dimension = config_data.get('dimension', self.dimension)
            self.concept_dim = config_data.get('concept_dim', self.concept_dim)
            self.hierarchical_levels = config_data.get('hierarchical_levels', self.hierarchical_levels)
            
            # Restore concept registry
            concept_registry_data = state_dict.get('concept_registry', {})
            self.concept_registry = {}
            
            for name, concept_data in concept_registry_data.items():
                tv = ThoughtVector(
                    vector=concept_data['vector'].to(self.device),
                    metadata=concept_data.get('metadata', {}),
                    source=concept_data.get('source', 'loaded'),
                    abstraction_level=concept_data.get('abstraction_level'),
                    confidence=concept_data.get('confidence')
                )
                self.concept_registry[name] = tv
            
            # Restore other components
            self.concept_clusters = {
                k: v.to(self.device) for k, v in state_dict.get('concept_clusters', {}).items()
            }
            self.relation_vectors = {
                k: v.to(self.device) for k, v in state_dict.get('relation_vectors', {}).items()
            }
            
            # Restore training history
            self.training_history = state_dict.get('training_history', self.training_history)
            
            # Load model states if available and requested
            if load_models:
                if 'encoder_state' in state_dict:
                    self.encoder.load_state_dict(state_dict['encoder_state'])
                if 'decoder_state' in state_dict:
                    self.decoder.load_state_dict(state_dict['decoder_state'])
            
            logger.info(f"Loaded ThoughtLatentSpace from {filepath}")
            
        except Exception as e:
            self.logger_system.log_error('load_thought_space', e, filepath=filepath)
            raise
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get comprehensive statistics about the thought space."""
        try:
            stats = {
                'configuration': {
                    'dimension': self.dimension,
                    'concept_dim': self.concept_dim,
                    'hierarchical_levels': self.hierarchical_levels,
                    'device': str(self.device),
                    'advanced_math_enabled': self.config.enable_advanced_math,
                    'manifold_learning_enabled': self.config.enable_manifold_learning
                },
                'content': {
                    'num_registered_concepts': len(self.concept_registry),
                    'num_concept_clusters': len(self.concept_clusters),
                    'num_relation_vectors': len(self.relation_vectors)
                },
                'training': {
                    'epochs_completed': self.training_history.get('epoch', 0),
                    'latest_loss': self.training_history['loss'][-1] if self.training_history['loss'] else None
                },
                'performance': self.logger_system.get_performance_summary()
            }
            
            return stats
            
        except Exception as e:
            self.logger_system.log_error('get_statistics', e)
            raise
    
    def __len__(self) -> int:
        """Return number of registered concepts."""
        return len(self.concept_registry)
    
    def __contains__(self, concept_name: str) -> bool:
        """Check if concept is registered."""
        return concept_name in self.concept_registry
    
    def __getitem__(self, concept_name: str) -> ThoughtVector:
        """Get registered concept by name."""
        if concept_name not in self.concept_registry:
            raise KeyError(f"Concept '{concept_name}' not found in thought space")
        return self.concept_registry[concept_name]
    
    def __setitem__(self, concept_name: str, concept: Union[torch.Tensor, ThoughtVector]) -> None:
        """Register concept with indexing syntax."""
        self.register_concept(concept_name, concept)
    
    def __repr__(self) -> str:
        """String representation of the thought space."""
        return (f"HybridThoughtLatentSpace(dimension={self.dimension}, "
                f"concepts={len(self.concept_registry)}, "
                f"device={self.device})")

# ============================================================================
# Factory Functions and Utilities
# ============================================================================

def create_thought_latent_space(
    config: Optional[ThoughtSpaceConfig] = None,
    device: Optional[str] = None
) -> ThoughtLatentSpace:
    """Factory function to create a ThoughtLatentSpace instance."""
    if config is None:
        config = ThoughtSpaceConfig()
    
    if device is not None:
        config.device = device
    
    return ThoughtLatentSpace(config=config)

def load_thought_space_from_file(filepath: str, device: Optional[str] = None) -> ThoughtLatentSpace:
    """Load a ThoughtLatentSpace from file."""
    config = ThoughtSpaceConfig()
    if device is not None:
        config.device = device
    
    thought_space = ThoughtLatentSpace(config=config)
    thought_space.load_thought_space(filepath)
    
    return thought_space

# Enhanced utility functions
def semantic_similarity(
    v1: torch.Tensor, 
    v2: torch.Tensor, 
    method: str = 'cosine'
) -> torch.Tensor:
    """Enhanced semantic similarity computation."""
    return AdvancedMathOperations.semantic_similarity(v1, v2, method)

def concept_composition(
    concepts: List[torch.Tensor],
    weights: Optional[List[float]] = None,
    method: str = 'weighted_sum'
) -> torch.Tensor:
    """Enhanced concept composition."""
    return AdvancedMathOperations.concept_composition(concepts, weights, method)

def concept_decomposition(
    concept: torch.Tensor,
    basis_concepts: torch.Tensor,
    l1_penalty: float = DEFAULT_SPARSITY_LAMBDA
) -> torch.Tensor:
    """Enhanced concept decomposition using iterative soft thresholding."""
    return AdvancedMathOperations.iterative_soft_thresholding(
        concept, basis_concepts, l1_penalty
    )

def latent_space_interpolation(
    start: torch.Tensor,
    end: torch.Tensor,
    steps: int = 10,
    method: str = 'spherical'
) -> List[torch.Tensor]:
    """Enhanced latent space interpolation."""
    alphas = torch.linspace(0, 1, steps, device=start.device)
    
    interpolated = []
    for alpha in alphas:
        if method == 'linear':
            point = (1 - alpha) * start + alpha * end
        elif method == 'spherical':
            point = AdvancedMathOperations.spherical_interpolation(start, end, alpha)
        else:
            raise ValueError(f"Unknown interpolation method: {method}")
        
        interpolated.append(point)
    
    return interpolated

# Module exports
__all__ = [
    'ThoughtLatentSpace',
    'ThoughtVector', 
    'ThoughtSpaceConfig',
    'ConceptEncoder',
    'ConceptDecoder',
    'AdvancedMathOperations',
    'InformationTheoreticMeasures',
    'ThoughtSpaceLogger',
    'create_thought_latent_space',
    'load_thought_space_from_file',
    'semantic_similarity',
    'concept_composition',
    'concept_decomposition',
    'latent_space_interpolation'
]

if __name__ == "__main__":
    # Example usage and testing
    print("ULTRA Hybrid Thought Latent Space - Production Ready")
    
    # Create configuration
    config = ThoughtSpaceConfig(
        dimension=512,
        concept_dim=384,
        hierarchical_levels=3,
        enable_advanced_math=True,
        enable_comprehensive_logging=True
    )
    
    # Create thought space
    thought_space = create_thought_latent_space(config)
    
    # Example operations
    print(f"Initialized: {thought_space}")
    print(f"Statistics: {thought_space.get_statistics()}")
    
    # Test basic functionality
    test_tensor = torch.randn(384)
    test_tv = thought_space.encode(test_tensor, return_thought_vectors=True)
    print(f"Encoded test vector: {test_tv}")
    
    decoded = thought_space.decode(test_tv)
    print(f"Decoded shape: {decoded.shape}")
    
    print("Hybrid implementation ready for production use!")