#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ULTRA: Ultimate Learning & Thought Reasoning Architecture
Thought Latent Space Implementation

This module provides a continuous mathematical space where abstract ideas, reasoning
paths, and solutions can be represented and manipulated. The space has specific
geometric properties that facilitate reasoning:

1. Hierarchical structure: Different regions correspond to different abstraction levels
2. Semantic continuity: Similar concepts are close to each other in the space
3. Relational structure: Analogical relationships are preserved as vector operations
4. Compositional properties: New concepts can be constructed through vector operations

Mathematical foundation:
- Vector space embedding: z = [z₁, z₂, ..., zₗ] where l is the abstraction level
- Semantic similarity: sim(c_i, c_j) ≈ exp(-d(z_i, z_j))
- Relation vectors: r_ij = z_j - z_i
- Composition functions: z_new = f(z₁, z₂, ..., zₙ, r₁, r₂, ..., rₘ)
"""

import math
import logging
import os
from typing import Dict, List, Tuple, Union, Optional, Callable, Any

import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.optim import Adam
from torch.utils.data import DataLoader, Dataset, TensorDataset

# Visualization imports - conditionally imported when needed
try:
    import matplotlib.pyplot as plt
    from sklearn.manifold import TSNE
    from sklearn.decomposition import PCA
    visualization_available = True
except ImportError:
    visualization_available = False

# Configure module logger
logger = logging.getLogger(__name__)

# ============================================================================
# Vector and Embedding Utilities
# ============================================================================

def semantic_similarity(
    v1: torch.Tensor, 
    v2: torch.Tensor, 
    method: str = 'cosine'
) -> torch.Tensor:
    """
    Compute semantic similarity between concept vectors.
    
    Args:
        v1: First concept vector(s) [..., dim]
        v2: Second concept vector(s) [..., dim]
        method: Similarity method ('cosine', 'euclidean', 'dot')
        
    Returns:
        Similarity scores [...] between 0 and 1
    """
    if method == 'cosine':
        # Normalize vectors
        v1_norm = F.normalize(v1, p=2, dim=-1)
        v2_norm = F.normalize(v2, p=2, dim=-1)
        # Compute cosine similarity
        sim = torch.sum(v1_norm * v2_norm, dim=-1)
        # Scale to [0, 1]
        sim = (sim + 1) / 2
    elif method == 'euclidean':
        # Compute Euclidean distance
        dist = torch.sqrt(torch.sum((v1 - v2) ** 2, dim=-1) + 1e-8)
        # Convert to similarity (1 when identical, approaches 0 as distance increases)
        sim = torch.exp(-dist)
    elif method == 'dot':
        # Compute dot product similarity
        sim = torch.sum(v1 * v2, dim=-1)
        # Scale to [0, 1] - this assumes vectors are roughly in the same magnitude range
        sim = torch.sigmoid(sim)
    else:
        raise ValueError(f"Unknown similarity method: {method}")
        
    return sim

def concept_composition(
    concepts: List[torch.Tensor], 
    weights: Optional[List[float]] = None,
    method: str = 'weighted_sum'
) -> torch.Tensor:
    """
    Compose multiple concepts into a new concept.
    
    Args:
        concepts: List of concept vectors to compose
        weights: Optional list of weights for weighted composition
        method: Composition method ('weighted_sum', 'average', 'max')
        
    Returns:
        Composed concept vector
    """
    if len(concepts) == 0:
        raise ValueError("Cannot compose empty list of concepts")
        
    # Stack concepts
    concepts_tensor = torch.stack(concepts)
    
    if method == 'weighted_sum':
        # Use provided weights or equal weights
        if weights is None:
            weights = torch.ones(len(concepts), device=concepts[0].device)
        else:
            weights = torch.tensor(weights, device=concepts[0].device)
            
        # Normalize weights
        weights = weights / weights.sum()
        
        # Compute weighted sum
        return (concepts_tensor * weights.view(-1, 1)).sum(dim=0)
    
    elif method == 'average':
        # Simple average
        return concepts_tensor.mean(dim=0)
        
    elif method == 'max':
        # Element-wise maximum
        return torch.max(concepts_tensor, dim=0).values
        
    else:
        raise ValueError(f"Unknown composition method: {method}")

def concept_decomposition(
    concept: torch.Tensor, 
    basis_concepts: torch.Tensor,
    l1_penalty: float = 0.1
) -> torch.Tensor:
    """
    Decompose a concept into a weighted combination of basis concepts.
    Uses sparse coding to find interpretable decompositions.
    
    Args:
        concept: Concept vector to decompose
        basis_concepts: Tensor of basis concepts [num_basis, dim]
        l1_penalty: Sparsity penalty for the decomposition
        
    Returns:
        Weights for basis concepts [num_basis]
    """
    # Normalize basis concepts
    basis_concepts_norm = F.normalize(basis_concepts, p=2, dim=1)
    concept_norm = F.normalize(concept, p=2, dim=0)
    
    # Initial weights with similarity scores
    weights = F.cosine_similarity(
        concept_norm.unsqueeze(0), 
        basis_concepts_norm,
        dim=1
    )
    
    # Make weights non-negative and apply threshold
    weights = F.relu(weights)
    
    # If sparsity is desired, apply iterative soft thresholding
    if l1_penalty > 0:
        weights = iterative_soft_thresholding(
            concept_norm, 
            basis_concepts_norm, 
            l1_penalty,
            initial_weights=weights,
            max_iterations=100
        )
    
    return weights

def iterative_soft_thresholding(
    target: torch.Tensor,
    dictionary: torch.Tensor,
    lambda_l1: float,
    initial_weights: Optional[torch.Tensor] = None,
    max_iterations: int = 100,
    tolerance: float = 1e-4
) -> torch.Tensor:
    """
    Iterative soft thresholding algorithm for sparse coding.
    
    Args:
        target: Target vector to approximate
        dictionary: Dictionary matrix [num_atoms, dim]
        lambda_l1: L1 penalty coefficient
        initial_weights: Initial weights
        max_iterations: Maximum number of iterations
        tolerance: Convergence tolerance
        
    Returns:
        Sparse weight vector
    """
    # Initialize weights
    if initial_weights is None:
        weights = torch.zeros(dictionary.shape[0], device=target.device)
    else:
        weights = initial_weights.clone()
    
    # Compute dictionary gram matrix
    gram = torch.mm(dictionary, dictionary.t())
    
    # Compute correlation of dictionary with target
    corr = torch.mv(dictionary, target)
    
    # Compute Lipschitz constant
    lipschitz = torch.linalg.norm(gram, ord=2)
    step_size = 1.0 / lipschitz
    
    # Iterative soft thresholding
    prev_weights = weights.clone()
    for i in range(max_iterations):
        # Gradient step
        grad = torch.mv(gram, weights) - corr
        weights = weights - step_size * grad
        
        # Soft thresholding
        weights = F.softshrink(weights, lambda_l1 * step_size)
        
        # Check convergence
        if torch.norm(weights - prev_weights) < tolerance:
            break
            
        prev_weights = weights.clone()
    
    return weights

def latent_space_interpolation(
    start: torch.Tensor,
    end: torch.Tensor,
    steps: int = 5,
    method: str = 'linear'
) -> List[torch.Tensor]:
    """
    Interpolate between two points in the latent space.
    
    Args:
        start: Starting point in latent space
        end: Ending point in latent space
        steps: Number of interpolation steps
        method: Interpolation method ('linear', 'spherical')
        
    Returns:
        List of interpolated points
    """
    # Ensure start and end are on the same device
    device = start.device
    
    # Create interpolation weights
    alphas = torch.linspace(0, 1, steps, device=device)
    
    # Initialize result
    interpolated = []
    
    if method == 'linear':
        # Simple linear interpolation
        for alpha in alphas:
            interp = (1 - alpha) * start + alpha * end
            interpolated.append(interp)
            
    elif method == 'spherical':
        # Spherical linear interpolation (slerp)
        # Normalize vectors
        start_norm = start / start.norm()
        end_norm = end / end.norm()
        
        # Compute cosine of angle between vectors
        cos_angle = torch.dot(start_norm, end_norm).clamp(-0.99999, 0.99999)
        angle = torch.acos(cos_angle)
        
        # If angle is very small, fall back to linear interpolation
        if angle < 1e-4:
            for alpha in alphas:
                interp = (1 - alpha) * start + alpha * end
                interpolated.append(interp)
        else:
            # Spherical interpolation
            sin_angle = torch.sin(angle)
            for alpha in alphas:
                t1 = torch.sin((1 - alpha) * angle) / sin_angle
                t2 = torch.sin(alpha * angle) / sin_angle
                interp = t1 * start + t2 * end
                interpolated.append(interp)
                
    else:
        raise ValueError(f"Unknown interpolation method: {method}")
        
    return interpolated

# ============================================================================
# Concept Embedding Models
# ============================================================================

class ConceptEmbedding(nn.Module):
    """
    Learns embeddings for concepts in a structured latent space.
    
    This module maps concepts from various input formats (text, structured data, etc.)
    to vectors in a continuous latent space designed to preserve semantic and
    relational properties.
    """
    
    def __init__(
        self, 
        input_dim: int, 
        latent_dim: int = 1024,
        num_layers: int = 3,
        hidden_dim: int = None,
        dropout: float = 0.1,
        activation: str = 'relu'
    ):
        """
        Initialize the concept embedding model.
        
        Args:
            input_dim: Dimension of input representations
            latent_dim: Dimension of the latent space
            num_layers: Number of layers in the embedding network
            hidden_dim: Dimension of hidden layers (default: 2*latent_dim)
            dropout: Dropout rate
            activation: Activation function ('relu', 'gelu', or 'swish')
        """
        super().__init__()
        self.input_dim = input_dim
        self.latent_dim = latent_dim
        self.hidden_dim = hidden_dim or (2 * latent_dim)
        
        # Select activation function
        if activation == 'relu':
            self.activation = nn.ReLU()
        elif activation == 'gelu':
            self.activation = nn.GELU()
        elif activation == 'swish':
            self.activation = lambda x: x * torch.sigmoid(x)  # Swish activation
        else:
            raise ValueError(f"Unknown activation function: {activation}")
        
        # Build layers
        layers = []
        # Input layer
        layers.append(nn.Linear(input_dim, self.hidden_dim))
        layers.append(self.activation)
        layers.append(nn.Dropout(dropout))
        
        # Hidden layers
        for _ in range(num_layers - 2):
            layers.append(nn.Linear(self.hidden_dim, self.hidden_dim))
            layers.append(self.activation)
            layers.append(nn.Dropout(dropout))
        
        # Output layer
        layers.append(nn.Linear(self.hidden_dim, latent_dim))
        
        self.network = nn.Sequential(*layers)
        
        logger.info(f"Initialized ConceptEmbedding with latent_dim={latent_dim}")
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Embed concepts into the latent space.
        
        Args:
            x: Input tensor [batch_size, input_dim]
            
        Returns:
            Concept embeddings [batch_size, latent_dim]
        """
        embeddings = self.network(x)
        # Normalize to unit sphere
        return F.normalize(embeddings, p=2, dim=1)
    
    def encode_batch(
        self, 
        inputs: List[torch.Tensor],
        normalize: bool = True
    ) -> torch.Tensor:
        """
        Encode a batch of inputs into the latent space.
        
        Args:
            inputs: List of input tensors
            normalize: Whether to normalize embeddings to unit sphere
            
        Returns:
            Batch of embeddings [batch_size, latent_dim]
        """
        if not inputs:
            return torch.zeros((0, self.latent_dim), device=self.device)
        
        # Stack inputs
        batch = torch.stack(inputs, dim=0)
        
        # Forward pass
        embeddings = self.network(batch)
        
        # Normalize if requested
        if normalize:
            embeddings = F.normalize(embeddings, p=2, dim=1)
            
        return embeddings
    
    @property
    def device(self):
        """Get the device of the model."""
        return next(self.parameters()).device


class ConceptEncoder(nn.Module):
    """
    Encodes concept representations into the latent thought space.
    Uses a hierarchical structure to capture multiple levels of abstraction.
    """
    def __init__(
        self,
        input_dim: int,
        output_dim: int,
        hidden_dims: List[int] = None,
        num_levels: int = 3,
        dropout: float = 0.1,
        use_layer_norm: bool = True,
        activation: str = 'gelu'
    ):
        """
        Initialize the concept encoder.
        
        Args:
            input_dim: Dimensionality of input concept representations
            output_dim: Dimensionality of the latent thought space
            hidden_dims: Dimensions of hidden layers
            num_levels: Number of hierarchical levels
            dropout: Dropout probability
            use_layer_norm: Whether to use layer normalization
            activation: Activation function ('relu', 'gelu', or 'swish')
        """
        super().__init__()
        
        self.input_dim = input_dim
        self.output_dim = output_dim
        self.num_levels = num_levels
        
        # Default hidden dimensions if not provided
        if hidden_dims is None:
            hidden_dims = [
                min(input_dim * 2, 1024),
                min(input_dim * 4, 2048),
                output_dim * 2
            ]
        
        # Ensure we have enough hidden dimensions
        if len(hidden_dims) < 1:
            hidden_dims = [output_dim * 2]
            
        # Select activation function
        if activation == 'relu':
            act_fn = nn.ReLU()
        elif activation == 'gelu':
            act_fn = nn.GELU()
        elif activation == 'swish':
            act_fn = lambda x: x * torch.sigmoid(x)  # Swish activation
        else:
            raise ValueError(f"Unknown activation function: {activation}")
            
        # Core encoder network
        layers = []
        
        # Input projection
        layers.append(nn.Linear(input_dim, hidden_dims[0]))
        if use_layer_norm:
            layers.append(nn.LayerNorm(hidden_dims[0]))
        layers.append(act_fn)
        if dropout > 0:
            layers.append(nn.Dropout(dropout))
            
        # Hidden layers
        for i in range(len(hidden_dims) - 1):
            layers.append(nn.Linear(hidden_dims[i], hidden_dims[i+1]))
            if use_layer_norm:
                layers.append(nn.LayerNorm(hidden_dims[i+1]))
            layers.append(act_fn)
            if dropout > 0:
                layers.append(nn.Dropout(dropout))
                
        # Core encoder
        self.encoder = nn.Sequential(*layers)
        
        # Level-specific projections
        # Each level has a different "view" of the concept
        level_dims = output_dim // num_levels
        leftover_dim = output_dim - (level_dims * num_levels)
        
        self.level_projections = nn.ModuleList()
        for i in range(num_levels):
            # Adjust last level dimension to account for division remainder
            if i == num_levels - 1:
                level_dim = level_dims + leftover_dim
            else:
                level_dim = level_dims
                
            self.level_projections.append(
                nn.Linear(hidden_dims[-1], level_dim)
            )
            
        # Initialize weights
        self._init_weights()
        
        logger.info(f"Initialized ConceptEncoder with {num_levels} abstraction levels")
        
    def _init_weights(self):
        """Initialize weights for better training dynamics."""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight)
                if module.bias is not None:
                    nn.init.zeros_(module.bias)
                    
    def forward(self, x: torch.Tensor, return_levels: bool = False) -> Union[torch.Tensor, Tuple[torch.Tensor, List[torch.Tensor]]]:
        """
        Encode concepts into the latent thought space.
        
        Args:
            x: Input concept tensor [batch_size, input_dim]
            return_levels: Whether to return individual level outputs
            
        Returns:
            Encoded latent vectors [batch_size, output_dim] or
            Tuple of (full_embedding, list_of_level_embeddings)
        """
        # Get shared representation
        shared = self.encoder(x)
        
        # Get level-specific projections
        level_outputs = [proj(shared) for proj in self.level_projections]
        
        # Concatenate level outputs for full embedding
        full_embedding = torch.cat(level_outputs, dim=1)
        
        if return_levels:
            return full_embedding, level_outputs
        else:
            return full_embedding


class ConceptDecoder(nn.Module):
    """
    Decodes latent thought space vectors back to concept representations.
    Mirrors the encoder structure.
    """
    def __init__(
        self,
        input_dim: int,
        output_dim: int,
        hidden_dims: List[int] = None,
        num_levels: int = 3,
        dropout: float = 0.1,
        use_layer_norm: bool = True,
        activation: str = 'gelu'
    ):
        """
        Initialize the concept decoder.
        
        Args:
            input_dim: Dimensionality of the latent thought space
            output_dim: Dimensionality of output concept representations
            hidden_dims: Dimensions of hidden layers (in reverse order of encoder)
            num_levels: Number of hierarchical levels
            dropout: Dropout probability
            use_layer_norm: Whether to use layer normalization
            activation: Activation function ('relu', 'gelu', or 'swish')
        """
        super().__init__()
        
        self.input_dim = input_dim
        self.output_dim = output_dim
        self.num_levels = num_levels
        
        # Default hidden dimensions if not provided
        if hidden_dims is None:
            hidden_dims = [
                input_dim // 2,
                min(output_dim * 4, 2048),
                min(output_dim * 2, 1024)
            ]
        
        # Ensure we have enough hidden dimensions
        if len(hidden_dims) < 1:
            hidden_dims = [input_dim // 2]
            
        # Select activation function
        if activation == 'relu':
            act_fn = nn.ReLU()
        elif activation == 'gelu':
            act_fn = nn.GELU()
        elif activation == 'swish':
            act_fn = lambda x: x * torch.sigmoid(x)  # Swish activation
        else:
            raise ValueError(f"Unknown activation function: {activation}")
            
        # Level-specific projections
        level_dims = input_dim // num_levels
        leftover_dim = input_dim - (level_dims * num_levels)
        
        self.level_projections = nn.ModuleList()
        for i in range(num_levels):
            # Adjust last level dimension to account for division remainder
            if i == num_levels - 1:
                level_dim = level_dims + leftover_dim
            else:
                level_dim = level_dims
                
            self.level_projections.append(
                nn.Linear(level_dim, hidden_dims[0])
            )
            
        # Fusion layer
        self.fusion = nn.Linear(hidden_dims[0] * num_levels, hidden_dims[0])
        
        # Core decoder network
        layers = []
        
        # Hidden layers
        for i in range(len(hidden_dims) - 1):
            layers.append(nn.Linear(hidden_dims[i], hidden_dims[i+1]))
            if use_layer_norm:
                layers.append(nn.LayerNorm(hidden_dims[i+1]))
            layers.append(act_fn)
            if dropout > 0:
                layers.append(nn.Dropout(dropout))
                
        # Output projection
        layers.append(nn.Linear(hidden_dims[-1], output_dim))
        
        # Core decoder
        self.decoder = nn.Sequential(*layers)
        
        # Initialize weights
        self._init_weights()
        
        logger.info(f"Initialized ConceptDecoder with {num_levels} abstraction levels")
        
    def _init_weights(self):
        """Initialize weights for better training dynamics."""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight)
                if module.bias is not None:
                    nn.init.zeros_(module.bias)
                    
    def forward(
        self, 
        z: torch.Tensor, 
        level_inputs: Optional[List[torch.Tensor]] = None
    ) -> torch.Tensor:
        """
        Decode latent vectors back to concept representations.
        
        Args:
            z: Latent vector [batch_size, input_dim]
            level_inputs: Optional separate level inputs
            
        Returns:
            Decoded concept tensor [batch_size, output_dim]
        """
        # Split input into levels if not provided separately
        if level_inputs is None:
            level_dims = self.input_dim // self.num_levels
            leftover_dim = self.input_dim - (level_dims * self.num_levels)
            
            level_inputs = []
            start_idx = 0
            for i in range(self.num_levels):
                # Adjust last level dimension
                if i == self.num_levels - 1:
                    end_idx = start_idx + level_dims + leftover_dim
                else:
                    end_idx = start_idx + level_dims
                    
                level_inputs.append(z[:, start_idx:end_idx])
                start_idx = end_idx
        
        # Process each level
        level_outputs = [proj(level) for proj, level in zip(self.level_projections, level_inputs)]
        
        # Concatenate and fuse level outputs
        combined = torch.cat(level_outputs, dim=1)
        fused = self.fusion(combined)
        
        # Decode to concept representation
        return self.decoder(fused)

# ============================================================================
# ThoughtVector Class
# ============================================================================

class ThoughtVector:
    """
    Represents a vector in the thought latent space with additional metadata.
    Supports vector operations and maintains provenance information.
    """
    def __init__(
        self, 
        vector: torch.Tensor, 
        metadata: Optional[Dict[str, Any]] = None,
        source: Optional[str] = None,
        abstraction_level: Optional[int] = None
    ):
        """
        Initialize a thought vector.
        
        Args:
            vector: The vector representation
            metadata: Optional metadata dictionary
            source: Optional source information
            abstraction_level: Optional abstraction level information
        """
        self.vector = vector
        self.metadata = metadata or {}
        self.source = source
        self.abstraction_level = abstraction_level
        
        # Store creation timestamp
        self.created_at = torch.cuda.Event(enable_timing=True) if torch.cuda.is_available() else None
        if self.created_at is not None:
            self.created_at.record()
        
    def __add__(self, other: 'ThoughtVector') -> 'ThoughtVector':
        """Add two thought vectors."""
        if not isinstance(other, ThoughtVector):
            return NotImplemented
            
        # Add vectors
        new_vector = self.vector + other.vector
        
        # Merge metadata
        new_metadata = {**self.metadata}
        for key, value in other.metadata.items():
            if key in new_metadata:
                # If key exists in both, create a list
                if isinstance(new_metadata[key], list):
                    if isinstance(value, list):
                        new_metadata[key].extend(value)
                    else:
                        new_metadata[key].append(value)
                else:
                    if isinstance(value, list):
                        new_metadata[key] = [new_metadata[key], *value]
                    else:
                        new_metadata[key] = [new_metadata[key], value]
            else:
                # If key only in other, copy it
                new_metadata[key] = value
                
        # Create new ThoughtVector
        return ThoughtVector(
            vector=new_vector,
            metadata=new_metadata,
            source=f"addition({self.source or 'unknown'}, {other.source or 'unknown'})",
            abstraction_level=max(self.abstraction_level or 0, other.abstraction_level or 0)
        )
        
    def __sub__(self, other: 'ThoughtVector') -> 'ThoughtVector':
        """Subtract one thought vector from another."""
        if not isinstance(other, ThoughtVector):
            return NotImplemented
            
        # Subtract vectors
        new_vector = self.vector - other.vector
        
        # Create new ThoughtVector
        return ThoughtVector(
            vector=new_vector,
            metadata={**self.metadata},  # Copy metadata from self
            source=f"subtraction({self.source or 'unknown'}, {other.source or 'unknown'})",
            abstraction_level=max(self.abstraction_level or 0, other.abstraction_level or 0)
        )
        
    def __mul__(self, scalar: float) -> 'ThoughtVector':
        """Multiply thought vector by a scalar."""
        if not isinstance(scalar, (int, float)):
            return NotImplemented
            
        # Multiply vector by scalar
        new_vector = self.vector * scalar
        
        # Create new ThoughtVector
        return ThoughtVector(
            vector=new_vector,
            metadata={**self.metadata},  # Copy metadata
            source=f"scaling({self.source or 'unknown'}, {scalar})",
            abstraction_level=self.abstraction_level
        )
        
    def __rmul__(self, scalar: float) -> 'ThoughtVector':
        """Right multiply thought vector by a scalar."""
        return self.__mul__(scalar)
        
    def __truediv__(self, scalar: float) -> 'ThoughtVector':
        """Divide thought vector by a scalar."""
        if not isinstance(scalar, (int, float)):
            return NotImplemented
            
        if scalar == 0:
            raise ValueError("Division by zero")
            
        # Divide vector by scalar
        return self.__mul__(1.0 / scalar)
        
    def similarity(self, other: 'ThoughtVector', method: str = 'cosine') -> float:
        """
        Compute similarity with another thought vector.
        
        Args:
            other: Another ThoughtVector
            method: Similarity method ('cosine', 'euclidean', 'dot')
            
        Returns:
            Similarity score between 0 and 1
        """
        if not isinstance(other, ThoughtVector):
            raise TypeError("Can only compute similarity with another ThoughtVector")
            
        # Compute similarity
        sim = semantic_similarity(
            self.vector.unsqueeze(0), 
            other.vector.unsqueeze(0), 
            method=method
        )
        
        return sim.item()
        
    def normalize(self) -> 'ThoughtVector':
        """
        Normalize the thought vector to unit length.
        
        Returns:
            Normalized ThoughtVector
        """
        # Compute norm
        norm = torch.norm(self.vector)
        
        # Avoid division by zero
        if norm < 1e-8:
            return self
            
        # Normalize
        new_vector = self.vector / norm
        
        # Create new ThoughtVector
        return ThoughtVector(
            vector=new_vector,
            metadata={**self.metadata},  # Copy metadata
            source=f"normalized({self.source or 'unknown'})",
            abstraction_level=self.abstraction_level
        )
        
    def to(self, device: str) -> 'ThoughtVector':
        """
        Move thought vector to the specified device.
        
        Args:
            device: Target device
            
        Returns:
            ThoughtVector on the target device
        """
        # Move vector to device
        new_vector = self.vector.to(device)
        
        # Create new ThoughtVector
        return ThoughtVector(
            vector=new_vector,
            metadata={**self.metadata},  # Copy metadata
            source=self.source,
            abstraction_level=self.abstraction_level
        )
        
    def detach(self) -> 'ThoughtVector':
        """
        Detach thought vector from computational graph.
        
        Returns:
            Detached ThoughtVector
        """
        # Detach vector
        new_vector = self.vector.detach()
        
        # Create new ThoughtVector
        return ThoughtVector(
            vector=new_vector,
            metadata={**self.metadata},  # Copy metadata
            source=self.source,
            abstraction_level=self.abstraction_level
        )
        
    def clone(self) -> 'ThoughtVector':
        """
        Create a deep copy of the thought vector.
        
        Returns:
            Cloned ThoughtVector
        """
        # Clone vector
        new_vector = self.vector.clone()
        
        # Create new ThoughtVector
        return ThoughtVector(
            vector=new_vector,
            metadata={**self.metadata},  # Copy metadata
            source=self.source,
            abstraction_level=self.abstraction_level
        )
        
    def __repr__(self) -> str:
        """String representation."""
        return f"ThoughtVector(dim={self.vector.shape}, source={self.source}, level={self.abstraction_level})"

# ============================================================================
# Main ThoughtLatentSpace Class
# ============================================================================

class ThoughtLatentSpace:
    """
    Manages a continuous space for representing and manipulating abstract concepts.
    
    The Thought Latent Space provides operations for concept manipulation,
    analogical reasoning, and exploration of the space of possible concepts.
    It organizes concepts in a structured space with meaningful geometric properties:
    
    1. Hierarchical Structure: Different levels of abstraction
    2. Semantic Continuity: Similar concepts are close in the space
    3. Relational Structure: Analogical relationships preserved as vector operations
    4. Compositional Properties: New concepts can be constructed through composition
    """
    
    def __init__(
        self, 
        dimension: int = 1024,
        concept_dim: int = 768,
        embedding_model: Optional[Union[ConceptEmbedding, ConceptEncoder]] = None,
        decoder_model: Optional[ConceptDecoder] = None,
        hierarchical_levels: int = 3,
        device: str = 'cuda' if torch.cuda.is_available() else 'cpu'
    ):
        """
        Initialize the Thought Latent Space.
        
        Args:
            dimension: Dimensionality of the latent space
            concept_dim: Dimensionality of concept representations
            embedding_model: Optional pre-trained embedding model
            decoder_model: Optional pre-trained decoder model
            hierarchical_levels: Number of hierarchical levels in the space
            device: Device to run computations on ('cpu' or 'cuda')
        """
        self.dimension = dimension
        self.concept_dim = concept_dim
        self.hierarchical_levels = hierarchical_levels
        self.device = device
        
        # Use provided embedding model or create defaults
        if embedding_model is not None:
            if isinstance(embedding_model, ConceptEmbedding):
                self.embedding_model = embedding_model.to(device)
                self.encoder = None
            elif isinstance(embedding_model, ConceptEncoder):
                self.encoder = embedding_model.to(device)
                self.embedding_model = None
            else:
                raise TypeError("embedding_model must be ConceptEmbedding or ConceptEncoder")
        else:
            # Create default encoder
            self.encoder = ConceptEncoder(
                input_dim=concept_dim,
                output_dim=dimension,
                num_levels=hierarchical_levels
            ).to(device)
            self.embedding_model = None
            
        # Use provided decoder model or create default
        if decoder_model is not None:
            self.decoder = decoder_model.to(device)
        elif self.encoder is not None:
            # Create matching decoder
            self.decoder = ConceptDecoder(
                input_dim=dimension,
                output_dim=concept_dim,
                num_levels=hierarchical_levels
            ).to(device)
        else:
            self.decoder = None
            
        # Dictionary to store known concept embeddings
        self.concept_registry = {}
        
        # Store concept clusters and relation vectors
        self.concept_clusters = {}  # Maps cluster name to centroid
        self.relation_vectors = {}  # Maps relation name to vector
        
        # Initialize cluster centers for organized thought space
        self.cluster_centers = torch.randn(100, dimension, device=device)
        self.cluster_centers = F.normalize(self.cluster_centers, p=2, dim=1)
        
        # Initialize evaluation metrics
        self.metrics = {
            'mse_loss': [],
            'cosine_similarity': []
        }
        
        logger.info(f"Initialized ThoughtLatentSpace with dimension={dimension}, hierarchical_levels={hierarchical_levels}")
    
    def encode(
        self, 
        concept_data: Union[str, torch.Tensor, Dict, List], 
        external_encoder: Optional[Callable] = None,
        return_thought_vectors: bool = False,
        metadata: Optional[List[Dict[str, Any]]] = None
    ) -> Union[torch.Tensor, List[ThoughtVector], ThoughtVector]:
        """
        Encode concepts into the latent thought space.
        
        Args:
            concept_data: Input representation (string, tensor, dict, or list)
            external_encoder: Optional function to encode text/structured data
            return_thought_vectors: Whether to return ThoughtVector objects
            metadata: Optional metadata for ThoughtVector creation
            
        Returns:
            Encoded latent vectors or ThoughtVector objects
        """
        # Handle string keys from concept registry
        if isinstance(concept_data, str) and concept_data in self.concept_registry:
            # Return cached embedding
            embedding = self.concept_registry[concept_data]
            
            if return_thought_vectors:
                return ThoughtVector(
                    vector=embedding,
                    metadata=metadata[0] if metadata else {'name': concept_data},
                    source=f"registry:{concept_data}"
                )
            return embedding
            
        # Handle different input types
        if isinstance(concept_data, torch.Tensor):
            # Direct tensor input
            input_tensor = concept_data.to(self.device)
            
        elif isinstance(concept_data, str):
            # Text input, use external encoder if provided
            if external_encoder is not None:
                input_tensor = external_encoder(concept_data).to(self.device)
            elif self.encoder is not None or self.embedding_model is not None:
                # If we have an encoder but no external encoder, 
                # this is an error as we can't process raw text
                raise ValueError("Cannot encode text without an external encoder")
            else:
                raise ValueError("Cannot encode text without an embedding model or external encoder")
                
        elif isinstance(concept_data, dict):
            # Structured data input, convert to tensor
            if 'embedding' in concept_data:
                # Direct embedding provided
                input_tensor = torch.tensor(concept_data['embedding'], device=self.device)
            elif 'features' in concept_data:
                # Features provided, use as is
                input_tensor = torch.tensor(concept_data['features'], device=self.device)
            else:
                raise ValueError("Dictionary input must contain 'embedding' or 'features' key")
                
        elif isinstance(concept_data, list):
            # List of inputs - could be tensors, dicts, or strings
            if all(isinstance(x, torch.Tensor) for x in concept_data):
                # List of tensors
                input_tensor = torch.stack([x.to(self.device) for x in concept_data])
            elif all(isinstance(x, dict) for x in concept_data):
                # List of dicts - extract embeddings or features
                if all('embedding' in x for x in concept_data):
                    input_tensor = torch.stack([
                        torch.tensor(x['embedding'], device=self.device) 
                        for x in concept_data
                    ])
                elif all('features' in x for x in concept_data):
                    input_tensor = torch.stack([
                        torch.tensor(x['features'], device=self.device) 
                        for x in concept_data
                    ])
                else:
                    raise ValueError("Dictionary inputs must contain 'embedding' or 'features' key")
            elif all(isinstance(x, str) for x in concept_data):
                # List of strings - need external encoder
                if external_encoder is not None:
                    # Check if encoder handles batches
                    try:
                        input_tensor = external_encoder(concept_data).to(self.device)
                    except:
                        # Fall back to individual encoding
                        input_tensor = torch.stack([
                            external_encoder(x).to(self.device) 
                            for x in concept_data
                        ])
                else:
                    raise ValueError("Cannot encode list of strings without an external encoder")
            else:
                raise TypeError("List inputs must be all tensors, all dicts, or all strings")
        else:
            raise TypeError(f"Unsupported concept_data type: {type(concept_data)}")
            
        # Ensure we're working with batched data
        if len(input_tensor.shape) == 1:
            input_tensor = input_tensor.unsqueeze(0)
            
        # Use encoder if available
        with torch.no_grad():
            if self.encoder is not None:
                latent_vectors = self.encoder(input_tensor)
            elif self.embedding_model is not None:
                latent_vectors = self.embedding_model(input_tensor)
            else:
                # If no model, ensure tensor has the right dimension and normalize
                if input_tensor.shape[1] != self.dimension:
                    raise ValueError(f"Input tensor must have second dimension = {self.dimension}")
                latent_vectors = F.normalize(input_tensor, p=2, dim=1)
                
        # Cache if it's a single string
        if isinstance(concept_data, str):
            self.concept_registry[concept_data] = latent_vectors.squeeze(0)
            
        # Return as ThoughtVector(s) if requested
        if return_thought_vectors:
            # Create metadata if not provided
            if metadata is None:
                if isinstance(concept_data, str):
                    metadata = [{'name': concept_data}]
                elif isinstance(concept_data, list) and all(isinstance(x, str) for x in concept_data):
                    metadata = [{'name': x} for x in concept_data]
                else:
                    metadata = [{}] * latent_vectors.shape[0]
            elif len(metadata) != latent_vectors.shape[0]:
                # Extend metadata if needed
                metadata = metadata + [{}] * (latent_vectors.shape[0] - len(metadata))
                
            # Create ThoughtVector objects
            thought_vectors = []
            for i in range(latent_vectors.shape[0]):
                tv = ThoughtVector(
                    vector=latent_vectors[i],
                    metadata=metadata[i],
                    source="encoded",
                    abstraction_level=None  # No specific level
                )
                thought_vectors.append(tv)
                
            # Return list or single ThoughtVector
            if len(thought_vectors) == 1:
                return thought_vectors[0]
            return thought_vectors
        else:
            # Return tensor (batch or single vector)
            if latent_vectors.shape[0] == 1 and not isinstance(concept_data, list):
                return latent_vectors.squeeze(0)
            return latent_vectors
            
    def decode(
        self, 
        latent_vectors: Union[torch.Tensor, ThoughtVector, List[ThoughtVector]]
    ) -> torch.Tensor:
        """
        Decode latent vectors back to concept representations.
        
        Args:
            latent_vectors: Latent vectors, ThoughtVector, or list of ThoughtVectors
            
        Returns:
            Decoded concept tensor(s)
        """
        if self.decoder is None:
            raise ValueError("No decoder model available")
            
        # Handle different input types
        if isinstance(latent_vectors, ThoughtVector):
            vectors = latent_vectors.vector.unsqueeze(0)
        elif isinstance(latent_vectors, list) and all(isinstance(v, ThoughtVector) for v in latent_vectors):
            vectors = torch.stack([v.vector for v in latent_vectors])
        else:
            vectors = latent_vectors
            
        # Ensure vectors are batched
        if len(vectors.shape) == 1:
            vectors = vectors.unsqueeze(0)
            
        # Ensure vectors are on the correct device
        vectors = vectors.to(self.device)
        
        # Decode vectors
        with torch.no_grad():
            decoded_concepts = self.decoder(vectors)
            
        # Return batch or single vector
        if decoded_concepts.shape[0] == 1 and (
            isinstance(latent_vectors, ThoughtVector) or 
            not isinstance(latent_vectors, list)
        ):
            return decoded_concepts.squeeze(0)
        return decoded_concepts
        
    def train(
        self,
        dataset: Union[Dataset, torch.Tensor, List[torch.Tensor]],
        batch_size: int = 32,
        num_epochs: int = 100,
        learning_rate: float = 1e-4,
        weight_decay: float = 1e-6,
        beta1: float = 0.9,
        beta2: float = 0.999,
        reconstruction_weight: float = 1.0,
        kl_weight: float = 0.1,
        contrastive_weight: float = 0.1,
        save_path: Optional[str] = None,
        eval_interval: int = 5,
        callback: Optional[Callable[[int, Dict[str, float]], None]] = None
    ) -> Dict[str, List[float]]:
        """
        Train the thought latent space encoder and decoder.
        
        Args:
            dataset: Dataset of concept embeddings or tensor of embeddings
            batch_size: Batch size for training
            num_epochs: Number of training epochs
            learning_rate: Learning rate for optimizer
            weight_decay: Weight decay for optimizer
            beta1: Adam beta1 parameter
            beta2: Adam beta2 parameter
            reconstruction_weight: Weight for reconstruction loss
            kl_weight: Weight for KL divergence loss (encourages smoother latent space)
            contrastive_weight: Weight for contrastive loss (encourages clustering)
            save_path: Path to save the model (optional)
            eval_interval: Interval (in epochs) for evaluation
            callback: Optional callback function called after each epoch
            
        Returns:
            Dictionary with training history
        """
        if self.encoder is None or self.decoder is None:
            raise ValueError("Both encoder and decoder must be available for training")
            
        # Convert tensor or list to dataset if needed
        if isinstance(dataset, torch.Tensor):
            dataset = TensorDataset(dataset)
        elif isinstance(dataset, list) and all(isinstance(x, torch.Tensor) for x in dataset):
            dataset = TensorDataset(torch.stack(dataset))
        
        # Create data loader
        dataloader = DataLoader(
            dataset,
            batch_size=batch_size,
            shuffle=True,
            drop_last=True,
            pin_memory=True
        )
        
        # Set up optimizer
        optimizer = Adam(
            list(self.encoder.parameters()) + list(self.decoder.parameters()),
            lr=learning_rate,
            weight_decay=weight_decay,
            betas=(beta1, beta2)
        )
        
        # Training history
        history = {
            'total_loss': [],
            'reconstruction_loss': [],
            'kl_loss': [],
            'contrastive_loss': []
        }
        
        # Training loop
        for epoch in range(num_epochs):
            # Set models to training mode
            self.encoder.train()
            self.decoder.train()
            
            # Initialize epoch losses
            epoch_total_loss = 0.0
            epoch_rec_loss = 0.0
            epoch_kl_loss = 0.0
            epoch_contrastive_loss = 0.0
            num_batches = 0
            
            # Process batches
            for batch in dataloader:
                # Extract concept embeddings
                if isinstance(batch, (list, tuple)):
                    # Dataset returns tuples
                    x = batch[0].to(self.device)
                else:
                    # Dataset returns tensors
                    x = batch.to(self.device)
                
                # Forward pass
                # Encode
                z, level_outputs = self.encoder(x, return_levels=True)
                
                # Decode
                x_reconstructed = self.decoder(z)
                
                # Compute losses
                # Reconstruction loss (MSE)
                rec_loss = F.mse_loss(x_reconstructed, x)
                
                # KL divergence loss (encourages smooth latent space)
                kl_loss = self._compute_kl_loss(z)
                
                # Contrastive loss (encourages clustering of similar concepts)
                contrastive_loss = self._compute_contrastive_loss(z, x)
                
                # Total loss
                total_loss = (
                    reconstruction_weight * rec_loss + 
                    kl_weight * kl_loss +
                    contrastive_weight * contrastive_loss
                )
                
                # Backward pass
                optimizer.zero_grad()
                total_loss.backward()
                optimizer.step()
                
                # Update epoch statistics
                epoch_total_loss += total_loss.item()
                epoch_rec_loss += rec_loss.item()
                epoch_kl_loss += kl_loss.item()
                epoch_contrastive_loss += contrastive_loss.item()
                num_batches += 1
            
            # Compute average epoch losses
            avg_total_loss = epoch_total_loss / max(num_batches, 1)
            avg_rec_loss = epoch_rec_loss / max(num_batches, 1)
            avg_kl_loss = epoch_kl_loss / max(num_batches, 1)
            avg_contrastive_loss = epoch_contrastive_loss / max(num_batches, 1)
            
            # Update history
            history['total_loss'].append(avg_total_loss)
            history['reconstruction_loss'].append(avg_rec_loss)
            history['kl_loss'].append(avg_kl_loss)
            history['contrastive_loss'].append(avg_contrastive_loss)
            
            # Logging
            if epoch % eval_interval == 0 or epoch == num_epochs - 1:
                # Evaluate model
                metrics = self._evaluate(dataloader)
                
                logger.info(
                    f"Epoch {epoch+1}/{num_epochs}: "
                    f"loss={avg_total_loss:.6f}, "
                    f"rec_loss={avg_rec_loss:.6f}, "
                    f"kl_loss={avg_kl_loss:.6f}, "
                    f"contrastive_loss={avg_contrastive_loss:.6f}, "
                    f"eval_mse={metrics['mse']:.6f}, "
                    f"eval_cosine={metrics['cosine']:.6f}"
                )
                
                # Update metrics
                self.metrics['mse_loss'].append(metrics['mse'])
                self.metrics['cosine_similarity'].append(metrics['cosine'])
            else:
                logger.info(
                    f"Epoch {epoch+1}/{num_epochs}: "
                    f"loss={avg_total_loss:.6f}, "
                    f"rec_loss={avg_rec_loss:.6f}, "
                    f"kl_loss={avg_kl_loss:.6f}, "
                    f"contrastive_loss={avg_contrastive_loss:.6f}"
                )
            
            # Call callback if provided
            if callback is not None:
                callback_metrics = {
                    'total_loss': avg_total_loss,
                    'reconstruction_loss': avg_rec_loss,
                    'kl_loss': avg_kl_loss,
                    'contrastive_loss': avg_contrastive_loss
                }
                callback(epoch, callback_metrics)
            
            # Save model if path provided
            if save_path is not None and (epoch % eval_interval == 0 or epoch == num_epochs - 1):
                self.save(save_path)
        
        return history
        
    def _compute_kl_loss(self, z: torch.Tensor) -> torch.Tensor:
        """
        Compute KL divergence loss to encourage a smooth, well-distributed latent space.
        
        Args:
            z: Latent vectors [batch_size, thought_space_dim]
            
        Returns:
            KL divergence loss
        """
        # Simple regularization to encourage near-zero mean and unit variance
        # This is a simplified version of the VAE KL loss
        z_mean = z.mean(dim=0)
        z_var = z.var(dim=0)
        
        # KL with standard normal: -0.5 * sum(1 + log(var) - mean^2 - var)
        kl_loss = -0.5 * torch.sum(1 + torch.log(z_var + 1e-6) - z_mean.pow(2) - z_var)
        
        return kl_loss
        
    def _compute_contrastive_loss(self, z: torch.Tensor, x: torch.Tensor) -> torch.Tensor:
        """
        Compute contrastive loss to encourage clustering of similar concepts.
        
        Args:
            z: Latent vectors [batch_size, thought_space_dim]
            x: Original concept vectors [batch_size, concept_dim]
            
        Returns:
            Contrastive loss
        """
        # Compute pairwise similarities in concept space
        x_norm = F.normalize(x, p=2, dim=1)
        concept_sim = torch.mm(x_norm, x_norm.t())
        
        # Compute pairwise similarities in latent space
        z_norm = F.normalize(z, p=2, dim=1)
        latent_sim = torch.mm(z_norm, z_norm.t())
        
        # Contrastive loss: encourage latent similarities to match concept similarities
        contrastive_loss = F.mse_loss(latent_sim, concept_sim)
        
        return contrastive_loss
        
    def _evaluate(self, dataloader: DataLoader) -> Dict[str, float]:
        """
        Evaluate the model on the provided data.
        
        Args:
            dataloader: DataLoader with evaluation data
            
        Returns:
            Dictionary with evaluation metrics
        """
        # Set models to evaluation mode
        self.encoder.eval()
        self.decoder.eval()
        
        # Initialize metrics
        mse_total = 0.0
        cosine_total = 0.0
        num_batches = 0
        
        # Process batches
        with torch.no_grad():
            for batch in dataloader:
                # Extract concept embeddings
                if isinstance(batch, (list, tuple)):
                    # Dataset returns tuples
                    x = batch[0].to(self.device)
                else:
                    # Dataset returns tensors
                    x = batch.to(self.device)
                
                # Forward pass
                z = self.encoder(x)
                x_reconstructed = self.decoder(z)
                
                # Compute metrics
                mse = F.mse_loss(x_reconstructed, x).item()
                
                # Compute cosine similarity
                x_norm = F.normalize(x, p=2, dim=1)
                x_rec_norm = F.normalize(x_reconstructed, p=2, dim=1)
                cosine = F.cosine_similarity(x_norm, x_rec_norm).mean().item()
                
                # Update totals
                mse_total += mse
                cosine_total += cosine
                num_batches += 1
        
        # Compute averages
        avg_mse = mse_total / max(num_batches, 1)
        avg_cosine = cosine_total / max(num_batches, 1)
        
        return {
            'mse': avg_mse,
            'cosine': avg_cosine
        }
    
    def find_nearest_concepts(
        self, 
        query: Union[torch.Tensor, ThoughtVector, str], 
        concepts: Union[Dict[str, torch.Tensor], List[Union[torch.Tensor, ThoughtVector]], torch.Tensor],
        k: int = 5,
        return_indices: bool = False,
        return_scores: bool = False
    ) -> Union[
        List[Union[str, torch.Tensor, ThoughtVector]],
        Tuple[List[Union[str, torch.Tensor, ThoughtVector]], List[int]],
        Tuple[List[Union[str, torch.Tensor, ThoughtVector]], List[float]]
    ]:
        """
        Find the k nearest concepts to a query.
        
        Args:
            query: Query concept/vector/name
            concepts: Concept dictionary, list, or tensor to search
            k: Number of nearest neighbors to return
            return_indices: Whether to return indices as well
            return_scores: Whether to return similarity scores
            
        Returns:
            List of k nearest concepts or tuple with indices/scores
        """
        # Handle query
        if isinstance(query, str):
            if query in self.concept_registry:
                query_embedding = self.concept_registry[query]
            else:
                raise ValueError(f"Unknown concept name: {query}")
        elif isinstance(query, ThoughtVector):
            query_embedding = query.vector
        else:
            query_embedding = query
            
        # Ensure query is properly shaped and on device
        query_embedding = query_embedding.to(self.device)
        if len(query_embedding.shape) == 1:
            query_embedding = query_embedding.unsqueeze(0)
        
        # Handle different concept collection types
        concept_list = []
        concept_embeddings_list = []
        
        if isinstance(concepts, dict):
            # Dictionary mapping names to embeddings
            concept_list = list(concepts.keys())
            concept_embeddings_list = [concepts[name].to(self.device) for name in concept_list]
            
        elif isinstance(concepts, list):
            # List of concepts
            concept_list = list(range(len(concepts)))
            
            if all(isinstance(c, ThoughtVector) for c in concepts):
                # List of ThoughtVectors
                concept_embeddings_list = [c.vector.to(self.device) for c in concepts]
            elif all(isinstance(c, torch.Tensor) for c in concepts):
                # List of tensors
                concept_embeddings_list = [c.to(self.device) for c in concepts]
            else:
                raise TypeError("List must contain all ThoughtVectors or all tensors")
                
        elif isinstance(concepts, torch.Tensor):
            # Tensor of embeddings
            concept_list = list(range(concepts.shape[0]))
            
            # Ensure concepts is 2D
            if len(concepts.shape) == 1:
                concept_embeddings_list = [concepts.to(self.device)]
            else:
                concept_embeddings_list = [concepts[i].to(self.device) for i in range(concepts.shape[0])]
                
        else:
            raise TypeError(f"Unsupported concepts type: {type(concepts)}")
            
        # Stack embeddings
        concept_embeddings = torch.stack([
            e.squeeze() if len(e.shape) > 1 else e 
            for e in concept_embeddings_list
        ])
            
        # Compute similarities
        similarities = semantic_similarity(
            query_embedding,
            concept_embeddings
        ).squeeze()
        
        # Get top-k similarities
        if k > len(concept_list):
            k = len(concept_list)
            
        topk_scores, topk_indices = torch.topk(similarities, k)
        
        # Get nearest concepts
        if isinstance(concepts, dict):
            # Dictionary case - return concept names
            nearest_concepts = [concept_list[i] for i in topk_indices.cpu().numpy()]
        elif isinstance(concepts, list):
            # List case - return original concepts
            nearest_concepts = [concepts[i] for i in topk_indices.cpu().numpy()]
        else:
            # Tensor case - return slices
            nearest_concepts = [concepts[i] for i in topk_indices.cpu().numpy()]
        
        # Return appropriate result
        if return_indices and return_scores:
            return nearest_concepts, topk_indices.cpu().numpy(), topk_scores.cpu().numpy()
        elif return_indices:
            return nearest_concepts, topk_indices.cpu().numpy()
        elif return_scores:
            return nearest_concepts, topk_scores.cpu().numpy()
        else:
            return nearest_concepts
            
    def vector_operation(
        self, 
        embeddings: List[Union[torch.Tensor, ThoughtVector]], 
        operation: str = 'analogy',
        weights: Optional[List[float]] = None,
        return_thought_vector: bool = False
    ) -> Union[torch.Tensor, ThoughtVector]:
        """
        Perform vector operations in the thought space.
        
        Args:
            embeddings: List of concept embeddings or ThoughtVectors
            operation: Operation type ('analogy', 'composition', 'negation', etc.)
            weights: Optional weights for the embeddings
            return_thought_vector: Whether to return a ThoughtVector
            
        Returns:
            Result embedding or ThoughtVector
        """
        if not embeddings:
            raise ValueError("Empty embeddings list")
            
        # Default to equal weights if not provided
        if weights is None:
            weights = [1.0] * len(embeddings)
        elif len(weights) != len(embeddings):
            raise ValueError("Number of weights must match number of embeddings")
        
        # Extract vectors from ThoughtVectors if needed
        vectors = []
        for emb in embeddings:
            if isinstance(emb, ThoughtVector):
                vectors.append(emb.vector.to(self.device))
            else:
                vectors.append(emb.to(self.device))
                
        # Ensure vectors are all 1D (no batch dimension)
        vectors = [v.squeeze() if len(v.shape) > 1 else v for v in vectors]
        
        # Perform the operation
        if operation == 'analogy':
            # A is to B as C is to ?
            # Formula: ? = B - A + C
            if len(vectors) != 3:
                raise ValueError("Analogy operation requires exactly 3 embeddings")
                
            result = vectors[1] - vectors[0] + vectors[2]
            
        elif operation == 'composition':
            # Weighted combination of concepts
            result = torch.zeros_like(vectors[0])
            for emb, w in zip(vectors, weights):
                result = result + w * emb
                
        elif operation == 'negation':
            # Negation of a concept
            if len(vectors) != 1:
                raise ValueError("Negation operation requires exactly 1 embedding")
                
            result = -vectors[0]
            
        elif operation == 'interpolation':
            # Interpolate between concepts
            result = torch.zeros_like(vectors[0])
            weight_sum = sum(weights)
            
            for emb, w in zip(vectors, weights):
                result = result + (w / weight_sum) * emb
                
        else:
            raise ValueError(f"Unknown operation: {operation}")
        
        # Normalize result to stay on hypersphere
        result = F.normalize(result, p=2, dim=0) if result.dim() > 0 else result
        
        if return_thought_vector:
            # Create metadata
            metadata = {
                'operation': {
                    'type': operation,
                    'weights': weights,
                    'sources': [
                        getattr(emb, 'metadata', {}) for emb in embeddings
                    ]
                }
            }
            
            # Create source string
            if isinstance(embeddings[0], ThoughtVector):
                source_names = [getattr(e, 'source', f'concept{i}') for i, e in enumerate(embeddings)]
                source_str = f"{operation}({', '.join(source_names)})"
            else:
                source_str = f"{operation}({len(embeddings)} concepts)"
                
            # Determine abstraction level (highest of input concepts)
            if isinstance(embeddings[0], ThoughtVector):
                abstraction_level = max((getattr(e, 'abstraction_level', 0) or 0) for e in embeddings)
            else:
                abstraction_level = None
                
            return ThoughtVector(
                vector=result,
                metadata=metadata,
                source=source_str,
                abstraction_level=abstraction_level
            )
        else:
            return result
    
    def compute_similarity(
        self, 
        concept1: Union[torch.Tensor, ThoughtVector, str], 
        concept2: Union[torch.Tensor, ThoughtVector, str],
        method: str = 'cosine'
    ) -> float:
        """
        Compute similarity between two concepts.
        
        Args:
            concept1: First concept/vector/name
            concept2: Second concept/vector/name
            method: Similarity method ('cosine', 'euclidean', 'dot')
            
        Returns:
            Similarity score between 0 and 1
        """
        # Handle string inputs (concept names)
        if isinstance(concept1, str):
            if concept1 in self.concept_registry:
                concept1 = self.concept_registry[concept1]
            else:
                raise ValueError(f"Unknown concept name: {concept1}")
                
        if isinstance(concept2, str):
            if concept2 in self.concept_registry:
                concept2 = self.concept_registry[concept2]
            else:
                raise ValueError(f"Unknown concept name: {concept2}")
        
        # Handle ThoughtVector inputs
        if isinstance(concept1, ThoughtVector):
            v1 = concept1.vector
        else:
            v1 = concept1
            
        if isinstance(concept2, ThoughtVector):
            v2 = concept2.vector
        else:
            v2 = concept2
            
        # Ensure vectors are on the correct device
        v1 = v1.to(self.device)
        v2 = v2.to(self.device)
        
        # Ensure vectors are properly shaped
        if len(v1.shape) == 1:
            v1 = v1.unsqueeze(0)
        if len(v2.shape) == 1:
            v2 = v2.unsqueeze(0)
            
        # Compute similarity
        sim = semantic_similarity(v1, v2, method=method)
        
        # For batched inputs, return the average similarity
        return sim.mean().item()
        
    def compute_analogy(
        self, 
        a: Union[torch.Tensor, ThoughtVector, str], 
        b: Union[torch.Tensor, ThoughtVector, str], 
        c: Union[torch.Tensor, ThoughtVector, str],
        return_thought_vector: bool = False
    ) -> Union[torch.Tensor, ThoughtVector]:
        """
        Compute analogical reasoning: a is to b as c is to ?
        
        Args:
            a: First concept/vector/name in the analogy
            b: Second concept/vector/name in the analogy
            c: Third concept/vector/name in the analogy
            return_thought_vector: Whether to return a ThoughtVector
            
        Returns:
            Result of the analogy (d in "a is to b as c is to d")
        """
        # Prepare embeddings
        embeddings = []
        for concept in [a, b, c]:
            if isinstance(concept, str):
                if concept in self.concept_registry:
                    embeddings.append(self.concept_registry[concept])
                else:
                    raise ValueError(f"Unknown concept name: {concept}")
            elif isinstance(concept, ThoughtVector):
                embeddings.append(concept)
            else:
                embeddings.append(concept)
                
        # Perform analogy operation
        return self.vector_operation(
            embeddings=embeddings,
            operation='analogy',
            return_thought_vector=return_thought_vector
        )
            
    def compose_concepts(
        self,
        concepts: List[Union[torch.Tensor, ThoughtVector, str]],
        weights: Optional[List[float]] = None,
        method: str = 'weighted_sum',
        return_thought_vector: bool = False
    ) -> Union[torch.Tensor, ThoughtVector]:
        """
        Compose multiple concepts into a new concept.
        
        Args:
            concepts: List of concepts/vectors/names to compose
            weights: Optional list of weights for weighted composition
            method: Composition method ('weighted_sum', 'average', 'max')
            return_thought_vector: Whether to return a ThoughtVector
            
        Returns:
            Composed concept/vector
        """
        if len(concepts) == 0:
            raise ValueError("Cannot compose empty list of concepts")
            
        # Prepare embeddings
        embeddings = []
        for concept in concepts:
            if isinstance(concept, str):
                if concept in self.concept_registry:
                    embeddings.append(self.concept_registry[concept])
                else:
                    raise ValueError(f"Unknown concept name: {concept}")
            elif isinstance(concept, ThoughtVector):
                embeddings.append(concept)
            else:
                embeddings.append(concept)
                
        # Use 'composition' operation (equivalent to method='weighted_sum')
        # or 'interpolation' (for method='average')
        op = 'composition' if method != 'average' else 'interpolation'
                
        # Perform composition operation
        return self.vector_operation(
            embeddings=embeddings,
            operation=op,
            weights=weights,
            return_thought_vector=return_thought_vector
        )
            
    def decompose_concept(
        self,
        concept: Union[torch.Tensor, ThoughtVector, str],
        basis_concepts: Union[Dict[str, torch.Tensor], List[Union[torch.Tensor, ThoughtVector]]],
        l1_penalty: float = 0.1,
        return_weights_only: bool = False,
        top_k: int = None
    ) -> Union[torch.Tensor, Dict[Union[str, int], float]]:
        """
        Decompose a concept into a weighted combination of basis concepts.
        
        Args:
            concept: Concept/vector/name to decompose
            basis_concepts: Dictionary or list of basis concepts
            l1_penalty: Sparsity penalty for the decomposition
            return_weights_only: Whether to return only the weights
            top_k: Optional number of top weights to return
            
        Returns:
            Weights tensor or dictionary mapping basis indices/names to weights
        """
        # Handle concept input
        if isinstance(concept, str):
            if concept in self.concept_registry:
                concept_vec = self.concept_registry[concept]
            else:
                raise ValueError(f"Unknown concept name: {concept}")
        elif isinstance(concept, ThoughtVector):
            concept_vec = concept.vector
        else:
            concept_vec = concept
            
        # Ensure concept is properly shaped
        concept_vec = concept_vec.to(self.device)
        if len(concept_vec.shape) > 1:
            concept_vec = concept_vec.squeeze()
            
        # Handle basis concepts
        basis_names = []
        basis_vecs = []
        
        if isinstance(basis_concepts, dict):
            # Dictionary mapping names to embeddings
            basis_names = list(basis_concepts.keys())
            
            for name in basis_names:
                if isinstance(basis_concepts[name], ThoughtVector):
                    basis_vecs.append(basis_concepts[name].vector.to(self.device))
                else:
                    basis_vecs.append(basis_concepts[name].to(self.device))
                    
        elif isinstance(basis_concepts, list):
            # List of concepts
            basis_names = list(range(len(basis_concepts)))
            
            for i, basis in enumerate(basis_concepts):
                if isinstance(basis, ThoughtVector):
                    basis_vecs.append(basis.vector.to(self.device))
                else:
                    basis_vecs.append(basis.to(self.device))
                    
        else:
            raise TypeError(f"Unsupported basis_concepts type: {type(basis_concepts)}")
            
        # Ensure basis vectors are properly shaped
        basis_vecs = [v.squeeze() if len(v.shape) > 1 else v for v in basis_vecs]
        
        # Stack basis vectors
        basis_matrix = torch.stack(basis_vecs)
        
        # Decompose concept
        weights = concept_decomposition(concept_vec, basis_matrix, l1_penalty)
        
        if return_weights_only:
            # Filter significant weights and return as dictionary
            significant_weights = {}
            
            # Apply top_k filter if specified
            if top_k is not None and top_k < len(weights):
                topk_values, topk_indices = torch.topk(weights, top_k)
                for i, idx in enumerate(topk_indices.cpu().numpy()):
                    basis_key = basis_names[idx]
                    significant_weights[basis_key] = topk_values[i].item()
            else:
                # Include all weights above threshold
                for i, w in enumerate(weights):
                    if w.item() > 0.01:  # Only include weights above threshold
                        basis_key = basis_names[i]
                        significant_weights[basis_key] = w.item()
                        
            return significant_weights
        else:
            return weights
    
    def interpolate(
        self,
        start: Union[torch.Tensor, ThoughtVector, str],
        end: Union[torch.Tensor, ThoughtVector, str],
        steps: int = 5,
        method: str = 'linear',
        return_thought_vectors: bool = False
    ) -> List[Union[torch.Tensor, ThoughtVector]]:
        """
        Interpolate between two points in the latent space.
        
        Args:
            start: Starting point/concept/name
            end: Ending point/concept/name
            steps: Number of interpolation steps
            method: Interpolation method ('linear', 'spherical')
            return_thought_vectors: Whether to return ThoughtVector objects
            
        Returns:
            List of interpolated points/concepts
        """
        # Handle string inputs (concept names)
        if isinstance(start, str):
            if start in self.concept_registry:
                start = self.concept_registry[start]
            else:
                raise ValueError(f"Unknown concept name: {start}")
                
        if isinstance(end, str):
            if end in self.concept_registry:
                end = self.concept_registry[end]
            else:
                raise ValueError(f"Unknown concept name: {end}")
        
        # Handle ThoughtVector inputs
        if isinstance(start, ThoughtVector):
            start_vec = start.vector
        else:
            start_vec = start
            
        if isinstance(end, ThoughtVector):
            end_vec = end.vector
        else:
            end_vec = end
            
        # Ensure vectors are on the correct device
        start_vec = start_vec.to(self.device)
        end_vec = end_vec.to(self.device)
        
        # Ensure vectors are properly shaped
        if len(start_vec.shape) > 1 and start_vec.shape[0] > 1:
            start_vec = start_vec[0]  # Take first vector if batched
        if len(end_vec.shape) > 1 and end_vec.shape[0] > 1:
            end_vec = end_vec[0]  # Take first vector if batched
            
        # Ensure vectors are 1D
        start_vec = start_vec.squeeze()
        end_vec = end_vec.squeeze()
            
        # Interpolate vectors
        interpolated = latent_space_interpolation(start_vec, end_vec, steps, method)
        
        if return_thought_vectors:
            # Create ThoughtVector objects
            thought_vectors = []
            
            for i, vec in enumerate(interpolated):
                # Compute interpolation parameter
                alpha = i / (steps - 1) if steps > 1 else 0
                
                # Create metadata
                metadata = {
                    'interpolation': {
                        'method': method,
                        'alpha': alpha,
                        'start': getattr(start, 'metadata', {}),
                        'end': getattr(end, 'metadata', {})
                    }
                }
                
                # Create source string
                if isinstance(start, str) and isinstance(end, str):
                    source_str = f"interpolation({start}, {end}, {alpha:.2f})"
                elif hasattr(start, 'source') and hasattr(end, 'source'):
                    source_str = f"interpolation({start.source}, {end.source}, {alpha:.2f})"
                else:
                    source_str = f"interpolation(start, end, {alpha:.2f})"
                
                # Determine abstraction level
                if isinstance(start, ThoughtVector) and isinstance(end, ThoughtVector):
                    abstraction_level = max(
                        getattr(start, 'abstraction_level', 0) or 0,
                        getattr(end, 'abstraction_level', 0) or 0
                    )
                else:
                    abstraction_level = None
                
                # Create ThoughtVector
                tv = ThoughtVector(
                    vector=vec,
                    metadata=metadata,
                    source=source_str,
                    abstraction_level=abstraction_level
                )
                
                thought_vectors.append(tv)
                
            return thought_vectors
        else:
            return interpolated
            
    def explore_concept_neighborhood(
        self,
        concept: Union[torch.Tensor, ThoughtVector, str],
        directions: Optional[List[torch.Tensor]] = None,
        num_samples: int = 5,
        noise_level: float = 0.1,
        return_thought_vectors: bool = False
    ) -> List[Union[torch.Tensor, ThoughtVector]]:
        """
        Explore the neighborhood of a concept in the thought latent space.
        
        Args:
            concept: Concept/vector/name to explore around
            directions: Optional specific directions to explore in
            num_samples: Number of samples to generate
            noise_level: Level of noise/exploration
            return_thought_vectors: Whether to return ThoughtVector objects
            
        Returns:
            List of generated concept samples
        """
        # Handle string input (concept name)
        if isinstance(concept, str):
            if concept in self.concept_registry:
                concept = self.concept_registry[concept]
            else:
                raise ValueError(f"Unknown concept name: {concept}")
        
        # Handle ThoughtVector input
        if isinstance(concept, ThoughtVector):
            concept_vec = concept.vector
        else:
            concept_vec = concept
            
        # Ensure vector is on the correct device
        concept_vec = concept_vec.to(self.device)
        
        # Ensure vector is properly shaped
        if len(concept_vec.shape) > 1 and concept_vec.shape[0] > 1:
            concept_vec = concept_vec[0]  # Take first vector if batched
            
        # Ensure vector is 1D
        concept_vec = concept_vec.squeeze()
        
        # Generate samples
        samples = []
        
        if directions is not None:
            # Explore along provided directions
            directions = [d.to(self.device) for d in directions]
            
            # Ensure directions are properly shaped
            directions = [d.squeeze() for d in directions]
            
            # Number of samples per direction
            samples_per_direction = max(1, num_samples // len(directions))
            
            for direction in directions:
                # Normalize direction
                if direction.norm() > 1e-8:
                    direction = direction / direction.norm()
                    
                # Generate samples along this direction
                for i in range(samples_per_direction):
                    # Scale factor (higher for samples further from center)
                    scale = noise_level * (i + 1) / samples_per_direction
                    
                    # Generate sample
                    sample = concept_vec + scale * direction
                    samples.append(sample)
                    
            # Fill remaining samples with random perturbations
            remaining = num_samples - len(samples)
            if remaining > 0:
                for _ in range(remaining):
                    # Random direction
                    rand_direction = torch.randn_like(concept_vec)
                    rand_direction = rand_direction / rand_direction.norm()
                    
                    # Random scale
                    scale = noise_level * torch.rand(1).item()
                    
                    # Generate sample
                    sample = concept_vec + scale * rand_direction
                    samples.append(sample)
        else:
            # Generate random samples around the concept
            for _ in range(num_samples):
                # Random direction
                rand_direction = torch.randn_like(concept_vec)
                rand_direction = rand_direction / rand_direction.norm()
                
                # Random scale
                scale = noise_level * torch.rand(1).item()
                
                # Generate sample
                sample = concept_vec + scale * rand_direction
                samples.append(sample)
                
        if return_thought_vectors:
            # Create ThoughtVector objects
            thought_vectors = []
            
            for i, sample in enumerate(samples):
                # Create metadata
                metadata = {
                    'exploration': {
                        'base_concept': getattr(concept, 'metadata', {}),
                        'noise_level': noise_level,
                        'sample_index': i
                    }
                }
                
                # Create source string
                if isinstance(concept, str):
                    source_str = f"exploration({concept}, {i})"
                elif hasattr(concept, 'source'):
                    source_str = f"exploration({concept.source}, {i})"
                else:
                    source_str = f"exploration(concept, {i})"
                
                # Create ThoughtVector
                tv = ThoughtVector(
                    vector=sample,
                    metadata=metadata,
                    source=source_str,
                    abstraction_level=getattr(concept, 'abstraction_level', None)
                )
                
                thought_vectors.append(tv)
                
            return thought_vectors
        else:
            return samples
            
    def register_concept_cluster(
        self,
        name: str,
        concepts: List[Union[torch.Tensor, ThoughtVector, str]]
    ) -> torch.Tensor:
        """
        Register a cluster of concepts under a named category.
        
        Args:
            name: Name of the concept cluster
            concepts: List of concepts/vectors/names in the cluster
            
        Returns:
            Cluster centroid vector
        """
        # Handle input concepts
        concept_vecs = []
        
        for concept in concepts:
            if isinstance(concept, str):
                if concept in self.concept_registry:
                    concept_vecs.append(self.concept_registry[concept])
                else:
                    raise ValueError(f"Unknown concept name: {concept}")
            elif isinstance(concept, ThoughtVector):
                concept_vecs.append(concept.vector)
            else:
                concept_vecs.append(concept)
                
        # Ensure vectors are on the correct device
        concept_vecs = [v.to(self.device) for v in concept_vecs]
        
        # Ensure vectors are properly shaped
        concept_vecs = [v.squeeze() for v in concept_vecs]
        
        # Compute cluster centroid
        centroid = torch.stack(concept_vecs).mean(dim=0)
        
        # Normalize centroid
        centroid = F.normalize(centroid, p=2, dim=0)
        
        # Register cluster
        self.concept_clusters[name] = centroid
        
        logger.info(f"Registered concept cluster '{name}' with {len(concepts)} concepts")
        
        return centroid
        
    def register_relation_vector(
        self,
        name: str,
        a: Union[torch.Tensor, ThoughtVector, str],
        b: Union[torch.Tensor, ThoughtVector, str]
    ) -> torch.Tensor:
        """
        Register a relation vector defined by the difference between two concepts.
        
        Args:
            name: Name of the relation
            a: First concept/vector/name
            b: Second concept/vector/name
            
        Returns:
            Relation vector (b - a)
        """
        # Handle input concepts
        if isinstance(a, str):
            if a in self.concept_registry:
                a_vec = self.concept_registry[a]
            else:
                raise ValueError(f"Unknown concept name: {a}")
        elif isinstance(a, ThoughtVector):
            a_vec = a.vector
        else:
            a_vec = a
            
        if isinstance(b, str):
            if b in self.concept_registry:
                b_vec = self.concept_registry[b]
            else:
                raise ValueError(f"Unknown concept name: {b}")
        elif isinstance(b, ThoughtVector):
            b_vec = b.vector
        else:
            b_vec = b
            
        # Ensure vectors are on the correct device
        a_vec = a_vec.to(self.device)
        b_vec = b_vec.to(self.device)
        
        # Ensure vectors are properly shaped
        a_vec = a_vec.squeeze()
        b_vec = b_vec.squeeze()
        
        # Compute relation vector
        relation = b_vec - a_vec
        
        # Register relation
        self.relation_vectors[name] = relation
        
        logger.info(f"Registered relation vector '{name}'")
        
        return relation
    
    def visualize_concepts(
        self, 
        concepts: Dict[str, Union[torch.Tensor, ThoughtVector]],
        method: str = 'tsne',
        perplexity: int = 30,
        n_iter: int = 1000,
        figsize: Tuple[int, int] = (10, 8),
        title: str = "Concept Space Visualization",
        show_labels: bool = True,
        alpha: float = 0.7,
        colors: Optional[List[str]] = None,
        groups: Optional[Dict[str, List[str]]] = None
    ) -> Any:
        """
        Visualize concepts in a 2D projection of the thought space.
        
        Args:
            concepts: Dictionary mapping concept names to embeddings/vectors
            method: Dimensionality reduction method ('tsne' or 'pca')
            perplexity: Perplexity parameter for t-SNE
            n_iter: Number of iterations for t-SNE
            figsize: Figure size
            title: Plot title
            show_labels: Whether to show concept labels
            alpha: Alpha transparency for points
            colors: Optional list of colors for points
            groups: Optional dictionary mapping group names to lists of concept names
            
        Returns:
            Matplotlib figure or None if visualization is not available
        """
        if not visualization_available:
            logger.warning("Visualization requires matplotlib and scikit-learn. Skipping.")
            return None
            
        # Extract embeddings and names
        concept_names = list(concepts.keys())
        embeddings = []
        
        for name in concept_names:
            if isinstance(concepts[name], ThoughtVector):
                embeddings.append(concepts[name].vector.cpu().numpy())
            else:
                embeddings.append(concepts[name].cpu().numpy())
                
        # Ensure embeddings are 2D
        embeddings = [e.squeeze() for e in embeddings]
        embeddings = np.stack(embeddings)
        
        # Reduce dimensionality
        if method == 'tsne':
            reducer = TSNE(n_components=2, perplexity=perplexity, n_iter=n_iter, random_state=42)
        elif method == 'pca':
            reducer = PCA(n_components=2)
        else:
            raise ValueError(f"Unknown visualization method: {method}")
            
        reduced_embeds = reducer.fit_transform(embeddings)
        
        # Create plot
        fig, ax = plt.subplots(figsize=figsize)
        
        if groups is not None:
            # Plot by groups with different colors
            group_names = list(groups.keys())
            if colors is None:
                # Generate colors if not provided
                cmap = plt.cm.get_cmap('tab10', len(group_names))
                colors = [cmap(i) for i in range(len(group_names))]
                
            # Plot each group
            for i, group_name in enumerate(group_names):
                # Get indices of concepts in this group
                indices = [concept_names.index(name) for name in groups[group_name] if name in concept_names]
                
                if indices:
                    # Plot points for this group
                    ax.scatter(
                        reduced_embeds[indices, 0], 
                        reduced_embeds[indices, 1], 
                        alpha=alpha,
                        color=colors[i % len(colors)],
                        label=group_name
                    )
                    
                    # Add labels if requested
                    if show_labels:
                        for idx in indices:
                            ax.annotate(
                                concept_names[idx], 
                                (reduced_embeds[idx, 0], reduced_embeds[idx, 1])
                            )
                            
            # Add legend
            ax.legend()
            
        else:
            # Plot all concepts with the same color
            ax.scatter(reduced_embeds[:, 0], reduced_embeds[:, 1], alpha=alpha, color=colors)
            
            # Add labels if requested
            if show_labels:
                for i, name in enumerate(concept_names):
                    ax.annotate(name, (reduced_embeds[i, 0], reduced_embeds[i, 1]))
            
        ax.set_title(title)
        ax.set_xlabel("Dimension 1")
        ax.set_ylabel("Dimension 2")
        ax.grid(alpha=0.3)
        
        plt.tight_layout()
        return fig
            
    def save(self, path: str):
        """
        Save the thought latent space model to a file.
        
        Args:
            path: Path to save the model
        """
        # Create directory if needed
        os.makedirs(os.path.dirname(path), exist_ok=True)
        
        # Prepare state dict
        state_dict = {
            'config': {
                'dimension': self.dimension,
                'concept_dim': self.concept_dim,
                'hierarchical_levels': self.hierarchical_levels
            },
            'concept_registry': {k: v.cpu() for k, v in self.concept_registry.items()},
            'concept_clusters': {k: v.cpu() for k, v in self.concept_clusters.items()},
            'relation_vectors': {k: v.cpu() for k, v in self.relation_vectors.items()},
            'metrics': self.metrics
        }
        
        # Add encoder state if available
        if self.encoder is not None:
            state_dict['encoder_state'] = self.encoder.state_dict()
            
        # Add decoder state if available
        if self.decoder is not None:
            state_dict['decoder_state'] = self.decoder.state_dict()
            
        # Add embedding model state if available
        if self.embedding_model is not None:
            state_dict['embedding_model_state'] = self.embedding_model.state_dict()
        
        # Save model
        torch.save(state_dict, path)
        
        logger.info(f"Saved ThoughtLatentSpace model to {path}")
        
    def load(self, path: str, device: Optional[str] = None):
        """
        Load the thought latent space model from a file.
        
        Args:
            path: Path to load the model from
            device: Optional device to load the model to
            
        Returns:
            Self for chaining
        """
        # Set device
        load_device = device or self.device
        if device is not None:
            self.device = device
        
        # Load state dict
        state_dict = torch.load(path, map_location=load_device)
        
        # Load configuration
        config = state_dict.get('config', {})
        self.dimension = config.get('dimension', self.dimension)
        self.concept_dim = config.get('concept_dim', self.concept_dim)
        self.hierarchical_levels = config.get('hierarchical_levels', self.hierarchical_levels)
        
        # Load encoder if state exists
        if 'encoder_state' in state_dict and self.encoder is not None:
            self.encoder.load_state_dict(state_dict['encoder_state'])
            self.encoder = self.encoder.to(self.device)
            
        # Load decoder if state exists
        if 'decoder_state' in state_dict and self.decoder is not None:
            self.decoder.load_state_dict(state_dict['decoder_state'])
            self.decoder = self.decoder.to(self.device)
            
        # Load embedding model if state exists
        if 'embedding_model_state' in state_dict and self.embedding_model is not None:
            self.embedding_model.load_state_dict(state_dict['embedding_model_state'])
            self.embedding_model = self.embedding_model.to(self.device)
        
        # Restore concept registry and relation vectors
        self.concept_registry = {k: v.to(self.device) for k, v in state_dict.get('concept_registry', {}).items()}
        self.concept_clusters = {k: v.to(self.device) for k, v in state_dict.get('concept_clusters', {}).items()}
        self.relation_vectors = {k: v.to(self.device) for k, v in state_dict.get('relation_vectors', {}).items()}
        
        # Restore metrics
        self.metrics = state_dict.get('metrics', {
            'mse_loss': [],
            'cosine_similarity': []
        })
        
        logger.info(f"Loaded ThoughtLatentSpace model from {path}")
        
        return self
        
    def to(self, device: str):
        """
        Move the entire model to the specified device.
        
        Args:
            device: Target device ('cpu', 'cuda', 'cuda:0', etc.)
            
        Returns:
            Self for chaining
        """
        # Update device
        self.device = device
        
        # Move encoder if available
        if self.encoder is not None:
            self.encoder = self.encoder.to(device)
            
        # Move decoder if available
        if self.decoder is not None:
            self.decoder = self.decoder.to(device)
            
        # Move embedding model if available
        if self.embedding_model is not None:
            self.embedding_model = self.embedding_model.to(device)
            
        # Move concept registry
        self.concept_registry = {k: v.to(device) for k, v in self.concept_registry.items()}
        self.concept_clusters = {k: v.to(device) for k, v in self.concept_clusters.items()}
        self.relation_vectors = {k: v.to(device) for k, v in self.relation_vectors.items()}
        
        # Move cluster centers
        self.cluster_centers = self.cluster_centers.to(device)
        
        logger.info(f"Moved ThoughtLatentSpace to device: {device}")
        
        return self
        
    def register_concept(
        self, 
        name: str, 
        vector: torch.Tensor, 
        metadata: Dict[str, Any] = None
    ) -> torch.Tensor:
        """
        Register a concept in the thought latent space.
        
        Args:
            name: Name of the concept
            vector: Concept vector
            metadata: Optional metadata for the concept
            
        Returns:
            Registered concept vector
        """
        # Ensure vector is on the correct device
        vector = vector.to(self.device)
        
        # Ensure vector is properly shaped
        if len(vector.shape) > 1:
            vector = vector.squeeze()
            
        # Normalize vector
        vector = F.normalize(vector, p=2, dim=0)
        
        # Register concept
        self.concept_registry[name] = vector
        
        # Store metadata if provided
        if metadata is not None:
            # If we have a ThoughtVector for this concept, update its metadata
            if name in self.concept_registry:
                tv = ThoughtVector(
                    vector=vector, 
                    metadata=metadata, 
                    source=f"registry:{name}"
                )
                # The registry only stores the tensor, but we keep a reference to the ThoughtVector
                # to retrieve metadata when needed
                self._thought_vectors = getattr(self, '_thought_vectors', {})
                self._thought_vectors[name] = tv
        
        logger.info(f"Registered concept '{name}' in thought latent space")
        
        return vector
        
    def remove_concept(self, name: str) -> bool:
        """
        Remove a concept from the thought latent space.
        
        Args:
            name: Name of the concept to remove
            
        Returns:
            Whether the concept was removed
        """
        if name in self.concept_registry:
            del self.concept_registry[name]
            
            # Remove from thought vectors if it exists
            if hasattr(self, '_thought_vectors') and name in self._thought_vectors:
                del self._thought_vectors[name]
                
            logger.info(f"Removed concept '{name}' from thought latent space")
            return True
        else:
            logger.warning(f"Cannot remove concept '{name}': not found in thought latent space")
            return False
            
    def get_concept(
        self, 
        name: str,
        return_thought_vector: bool = False
    ) -> Union[torch.Tensor, ThoughtVector, None]:
        """
        Get a concept from the registry by name.
        
        Args:
            name: Name of the concept
            return_thought_vector: Whether to return as ThoughtVector
            
        Returns:
            The concept vector, ThoughtVector, or None if not found
        """
        if name not in self.concept_registry:
            return None
            
        if return_thought_vector:
            # Return as ThoughtVector
            if hasattr(self, '_thought_vectors') and name in self._thought_vectors:
                # Return cached ThoughtVector
                return self._thought_vectors[name]
            else:
                # Create new ThoughtVector
                return ThoughtVector(
                    vector=self.concept_registry[name],
                    metadata={'name': name},
                    source=f"registry:{name}"
                )
        else:
            # Return as tensor
            return self.concept_registry[name]
            
    def list_concepts(self) -> List[str]:
        """
        List all registered concept names.
        
        Returns:
            List of concept names
        """
        return list(self.concept_registry.keys())
        
    def clear_registry(self) -> None:
        """
        Clear the concept registry.
        
        Returns:
            None
        """
        self.concept_registry = {}
        
        # Clear thought vectors if they exist
        if hasattr(self, '_thought_vectors'):
            self._thought_vectors = {}
            
        logger.info("Cleared concept registry")
        
    def evolve_latent_space(
        self,
        data: Union[torch.Tensor, List[torch.Tensor], Dict[str, torch.Tensor]],
        num_epochs: int = 10,
        batch_size: int = 32,
        learning_rate: float = 1e-4,
        callback: Optional[Callable[[int, Dict[str, float]], None]] = None
    ) -> Dict[str, List[float]]:
        """
        Evolve the latent space to better represent new data.
        
        Args:
            data: New data to incorporate (tensor, list, or dictionary)
            num_epochs: Number of training epochs
            batch_size: Batch size for training
            learning_rate: Learning rate for optimizer
            callback: Optional callback function for monitoring
            
        Returns:
            Dictionary with training history
        """
        # Prepare training data
        if isinstance(data, Dict):
            # Dictionary of named concepts
            train_data = torch.stack(list(data.values()))
        elif isinstance(data, List):
            # List of tensors
            train_data = torch.stack(data)
        else:
            # Tensor
            train_data = data
            
        # Ensure data is on the correct device
        train_data = train_data.to(self.device)
        
        # Train on the new data
        history = self.train(
            dataset=train_data,
            batch_size=batch_size,
            num_epochs=num_epochs,
            learning_rate=learning_rate,
            callback=callback
        )
        
        logger.info(f"Evolved latent space with {len(train_data)} new data points")
        
        return history
    
    def extract_relation_subspace(
        self, 
        relations: List[Union[Tuple[str, str], Tuple[torch.Tensor, torch.Tensor]]],
        subspace_dim: int = 10
    ) -> torch.Tensor:
        """
        Extract a subspace that captures specific relations between concepts.
        
        Args:
            relations: List of (source, target) pairs defining the relations
            subspace_dim: Dimensionality of the relation subspace
            
        Returns:
            Basis vectors spanning the relation subspace
        """
        # Extract relation vectors
        relation_vecs = []
        
        for src, tgt in relations:
            # Handle string inputs (concept names)
            if isinstance(src, str):
                if src in self.concept_registry:
                    src_vec = self.concept_registry[src]
                else:
                    raise ValueError(f"Unknown concept name: {src}")
            else:
                src_vec = src
                
            if isinstance(tgt, str):
                if tgt in self.concept_registry:
                    tgt_vec = self.concept_registry[tgt]
                else:
                    raise ValueError(f"Unknown concept name: {tgt}")
            else:
                tgt_vec = tgt
                
            # Ensure vectors are properly shaped and on device
            src_vec = src_vec.to(self.device).squeeze()
            tgt_vec = tgt_vec.to(self.device).squeeze()
            
            # Compute relation vector
            rel_vec = tgt_vec - src_vec
            relation_vecs.append(rel_vec)
        
        # Stack relation vectors
        relation_matrix = torch.stack(relation_vecs)
        
        # Perform PCA to extract principal components of the relation subspace
        # (Using SVD as a numerically stable way to compute PCA)
        U, S, V = torch.svd(relation_matrix)
        
        # Take top subspace_dim components
        subspace = V[:, :subspace_dim]
        
        return subspace
    
    def apply_relation(
        self,
        concept: Union[str, torch.Tensor, ThoughtVector],
        relation: Union[str, torch.Tensor, ThoughtVector],
        strength: float = 1.0,
        return_thought_vector: bool = False
    ) -> Union[torch.Tensor, ThoughtVector]:
        """
        Apply a relation vector to a concept.
        
        Args:
            concept: Source concept to apply the relation to
            relation: Relation vector or registered relation name
            strength: Strength of the relation application
            return_thought_vector: Whether to return as ThoughtVector
            
        Returns:
            Result of applying the relation
        """
        # Handle concept input
        if isinstance(concept, str):
            if concept in self.concept_registry:
                concept_vec = self.concept_registry[concept]
            else:
                raise ValueError(f"Unknown concept name: {concept}")
        elif isinstance(concept, ThoughtVector):
            concept_vec = concept.vector
        else:
            concept_vec = concept
            
        # Handle relation input
        if isinstance(relation, str):
            if relation in self.relation_vectors:
                relation_vec = self.relation_vectors[relation]
            else:
                raise ValueError(f"Unknown relation name: {relation}")
        elif isinstance(relation, ThoughtVector):
            relation_vec = relation.vector
        else:
            relation_vec = relation
            
        # Ensure vectors are properly shaped and on device
        concept_vec = concept_vec.to(self.device).squeeze()
        relation_vec = relation_vec.to(self.device).squeeze()
        
        # Apply relation with strength factor
        result = concept_vec + strength * relation_vec
        
        # Normalize result
        result = F.normalize(result, p=2, dim=0) if result.dim() > 0 else result
        
        if return_thought_vector:
            # Create source string
            if isinstance(concept, str) and isinstance(relation, str):
                source_str = f"apply_relation({concept}, {relation}, {strength})"
            elif hasattr(concept, 'source') and hasattr(relation, 'source'):
                source_str = f"apply_relation({concept.source}, {relation.source}, {strength})"
            else:
                source_str = f"apply_relation(concept, relation, {strength})"
                
            # Create metadata
            metadata = {
                'relation_application': {
                    'source_concept': getattr(concept, 'metadata', {}),
                    'relation': getattr(relation, 'metadata', {}),
                    'strength': strength
                }
            }
            
            # Create ThoughtVector
            return ThoughtVector(
                vector=result,
                metadata=metadata,
                source=source_str
            )
        else:
            return result
    
    def analyze_reasoning_path(
        self, 
        path: List[Union[torch.Tensor, ThoughtVector, str]],
        return_detailed: bool = False
    ) -> Union[Dict[str, float], Dict[str, Any]]:
        """
        Analyze a reasoning path for coherence, consistency, and other properties.
        
        Args:
            path: List of concepts/vectors/names forming a reasoning path
            return_detailed: Whether to return detailed analysis
            
        Returns:
            Dictionary with analysis results
        """
        if len(path) < 2:
            return {'coherence': 1.0, 'avg_step_size': 0.0, 'directness': 1.0}
            
        # Extract vectors from the path
        vectors = []
        
        for item in path:
            if isinstance(item, str):
                if item in self.concept_registry:
                    vectors.append(self.concept_registry[item])
                else:
                    raise ValueError(f"Unknown concept name: {item}")
            elif isinstance(item, ThoughtVector):
                vectors.append(item.vector)
            else:
                vectors.append(item)
                
        # Ensure vectors are properly shaped and on device
        vectors = [v.to(self.device).squeeze() for v in vectors]
        
        # Compute pairwise similarities between consecutive steps
        step_similarities = []
        for i in range(len(vectors) - 1):
            sim = semantic_similarity(
                vectors[i].unsqueeze(0),
                vectors[i+1].unsqueeze(0)
            ).item()
            step_similarities.append(sim)
            
        # Compute coherence (average similarity between consecutive steps)
        coherence = sum(step_similarities) / len(step_similarities)
        
        # Compute average step size
        step_sizes = []
        for i in range(len(vectors) - 1):
            step_size = torch.norm(vectors[i+1] - vectors[i]).item()
            step_sizes.append(step_size)
            
        avg_step_size = sum(step_sizes) / len(step_sizes)
        
        # Compute directness (similarity between start and end, normalized by path length)
        start_end_sim = semantic_similarity(
            vectors[0].unsqueeze(0),
            vectors[-1].unsqueeze(0)
        ).item()
        
        # Directness is higher when start-end similarity is proportional to path length
        # A direct path would have similarity inversely proportional to length
        path_length = len(vectors) - 1
        expected_sim = 1.0 / path_length if path_length > 0 else 1.0
        directness = 1.0 - abs(start_end_sim - expected_sim) / expected_sim
        directness = max(0.0, min(1.0, directness))  # Clamp to [0, 1]
        
        basic_results = {
            'coherence': coherence,
            'avg_step_size': avg_step_size,
            'directness': directness,
            'start_end_similarity': start_end_sim
        }
        
        if not return_detailed:
            return basic_results
            
        # Compute more detailed metrics
        
        # Smoothness (variance in step sizes)
        step_size_variance = torch.var(torch.tensor(step_sizes)).item() if step_sizes else 0.0
        smoothness = 1.0 / (1.0 + step_size_variance)  # Normalize to [0, 1]
        
        # Step variance (how much each step differs in direction)
        step_directions = []
        for i in range(len(vectors) - 1):
            direction = vectors[i+1] - vectors[i]
            direction = direction / (torch.norm(direction) + 1e-8)  # Normalize
            step_directions.append(direction)
            
        direction_similarities = []
        for i in range(len(step_directions) - 1):
            sim = torch.dot(step_directions[i], step_directions[i+1]).item()
            direction_similarities.append((sim + 1) / 2)  # Rescale from [-1, 1] to [0, 1]
            
        direction_consistency = sum(direction_similarities) / len(direction_similarities) if direction_similarities else 1.0
        
        # Analyze intermediate steps
        intermediate_analysis = []
        for i in range(1, len(vectors) - 1):
            # How much this step contributes to overall progress
            progress_contribution = semantic_similarity(
                vectors[i].unsqueeze(0),
                vectors[-1].unsqueeze(0)
            ).item() - semantic_similarity(
                vectors[i-1].unsqueeze(0),
                vectors[-1].unsqueeze(0)
            ).item()
            
            intermediate_analysis.append({
                'step': i,
                'similarity_to_prev': step_similarities[i-1],
                'similarity_to_next': step_similarities[i],
                'similarity_to_start': semantic_similarity(
                    vectors[0].unsqueeze(0),
                    vectors[i].unsqueeze(0)
                ).item(),
                'similarity_to_end': semantic_similarity(
                    vectors[i].unsqueeze(0),
                    vectors[-1].unsqueeze(0)
                ).item(),
                'step_size': step_sizes[i-1],
                'progress_contribution': progress_contribution
            })
        
        # Combine all results
        detailed_results = {
            **basic_results,
            'smoothness': smoothness,
            'direction_consistency': direction_consistency,
            'step_similarities': step_similarities,
            'step_sizes': step_sizes,
            'intermediate_analysis': intermediate_analysis
        }
        
        return detailed_results
    
    def find_concept_groups(
        self,
        concepts: Union[Dict[str, torch.Tensor], List[torch.Tensor]],
        num_clusters: int = 5,
        method: str = 'kmeans',
        return_centers: bool = False
    ) -> Union[List[List[Union[str, int]]], Tuple[List[List[Union[str, int]]], torch.Tensor]]:
        """
        Find groups/clusters of related concepts in the thought space.
        
        Args:
            concepts: Dictionary or list of concepts to cluster
            num_clusters: Number of clusters to find
            method: Clustering method ('kmeans' or 'hierarchical')
            return_centers: Whether to return cluster centers
            
        Returns:
            List of concept groups or tuple with centers
        """
        # Extract vectors and keys
        if isinstance(concepts, dict):
            keys = list(concepts.keys())
            vectors = [concepts[k].to(self.device) for k in keys]
        else:
            keys = list(range(len(concepts)))
            vectors = [v.to(self.device) for v in concepts]
            
        # Ensure vectors are properly shaped
        vectors = [v.squeeze() for v in vectors]
        
        # Stack vectors
        matrix = torch.stack(vectors)
        
        # Cluster vectors
        if method == 'kmeans':
            # Initialize cluster centers randomly
            centers = matrix[torch.randperm(matrix.shape[0])[:num_clusters]]
            
            # Run k-means clustering
            max_iters = 100
            for _ in range(max_iters):
                # Compute distances to cluster centers
                dists = torch.cdist(matrix, centers)
                
                # Assign points to nearest center
                assignments = torch.argmin(dists, dim=1)
                
                # Compute new centers
                new_centers = []
                for i in range(num_clusters):
                    cluster_points = matrix[assignments == i]
                    if len(cluster_points) > 0:
                        new_centers.append(cluster_points.mean(dim=0))
                    else:
                        # If cluster is empty, keep old center
                        new_centers.append(centers[i])
                        
                new_centers = torch.stack(new_centers)
                
                # Check for convergence
                if torch.allclose(centers, new_centers, rtol=1e-4):
                    break
                    
                centers = new_centers
                
            # Get final assignments
            dists = torch.cdist(matrix, centers)
            assignments = torch.argmin(dists, dim=1).cpu().numpy()
            
        elif method == 'hierarchical':
            # Compute pairwise distances
            dists = torch.cdist(matrix, matrix)
            
            # Initialize each point as its own cluster
            current_clusters = [[i] for i in range(len(vectors))]
            
            # Merge clusters until we have the desired number
            while len(current_clusters) > num_clusters:
                # Find the two closest clusters
                min_dist = float('inf')
                merge_i, merge_j = 0, 0
                
                for i in range(len(current_clusters)):
                    for j in range(i+1, len(current_clusters)):
                        # Compute average distance between clusters
                        cluster_i = current_clusters[i]
                        cluster_j = current_clusters[j]
                        
                        cluster_dist = 0.0
                        for idx_i in cluster_i:
                            for idx_j in cluster_j:
                                cluster_dist += dists[idx_i, idx_j].item()
                                
                        cluster_dist /= len(cluster_i) * len(cluster_j)
                        
                        if cluster_dist < min_dist:
                            min_dist = cluster_dist
                            merge_i, merge_j = i, j
                
                # Merge the two closest clusters
                current_clusters[merge_i].extend(current_clusters[merge_j])
                del current_clusters[merge_j]
                
            # Create assignments array
            assignments = torch.zeros(len(vectors), dtype=torch.long)
            for i, cluster in enumerate(current_clusters):
                for idx in cluster:
                    assignments[idx] = i
                    
            assignments = assignments.cpu().numpy()
            
            # Compute cluster centers
            centers = torch.zeros((num_clusters, matrix.shape[1]), device=matrix.device)
            for i in range(num_clusters):
                cluster_points = matrix[assignments == i]
                if len(cluster_points) > 0:
                    centers[i] = cluster_points.mean(dim=0)
                    
        else:
            raise ValueError(f"Unknown clustering method: {method}")
            
        # Group concepts by cluster
        groups = [[] for _ in range(num_clusters)]
        for i, cluster_idx in enumerate(assignments):
            groups[cluster_idx].append(keys[i])
            
        if return_centers:
            return groups, centers
        else:
            return groups
    
    def concept_arithmetic(
        self, 
        operations: List[Tuple[Union[str, torch.Tensor, ThoughtVector], float]],
        return_thought_vector: bool = False
    ) -> Union[torch.Tensor, ThoughtVector]:
        """
        Perform concept arithmetic (weighted combination of concepts).
        
        Args:
            operations: List of (concept, weight) tuples
            return_thought_vector: Whether to return as ThoughtVector
            
        Returns:
            Result of concept arithmetic
        """
        if not operations:
            raise ValueError("Empty operations list")
            
        result = None
        metadata = {'arithmetic': {'operations': []}}
        source_parts = []
        
        for concept, weight in operations:
            # Handle concept input
            if isinstance(concept, str):
                if concept in self.concept_registry:
                    concept_vec = self.concept_registry[concept]
                    concept_name = concept
                else:
                    raise ValueError(f"Unknown concept name: {concept}")
            elif isinstance(concept, ThoughtVector):
                concept_vec = concept.vector
                concept_name = getattr(concept, 'source', 'unknown')
                # Add metadata
                metadata['arithmetic']['operations'].append({
                    'concept': getattr(concept, 'metadata', {}),
                    'weight': weight
                })
            else:
                concept_vec = concept
                concept_name = 'tensor'
                
            # Ensure vector is properly shaped and on device
            concept_vec = concept_vec.to(self.device).squeeze()
            
            # Apply weight
            weighted_vec = weight * concept_vec
            
            # Add to result
            if result is None:
                result = weighted_vec
            else:
                result = result + weighted_vec
                
            # Add to source parts
            source_parts.append(f"{weight}*{concept_name}")
            
        # Normalize result
        if result is not None:
            result = F.normalize(result, p=2, dim=0) if result.dim() > 0 else result
            
        if return_thought_vector:
            # Create source string
            source_str = f"arithmetic({', '.join(source_parts)})"
            
            # Create ThoughtVector
            return ThoughtVector(
                vector=result,
                metadata=metadata,
                source=source_str
            )
        else:
            return result
    
    def concept_clustering(
        self,
        concepts: Dict[str, torch.Tensor],
        num_clusters: int = 10,
        save_to_registry: bool = True
    ) -> Dict[str, List[str]]:
        """
        Cluster concepts and optionally save cluster centroids to registry.
        
        Args:
            concepts: Dictionary mapping concept names to embeddings
            num_clusters: Number of clusters to create
            save_to_registry: Whether to save cluster centroids to registry
            
        Returns:
            Dictionary mapping cluster names to lists of concept names
        """
        # Find concept groups
        groups, centers = self.find_concept_groups(
            concepts=concepts,
            num_clusters=num_clusters,
            return_centers=True
        )
        
        # Create mapping from cluster index to list of concept names
        clusters = {}
        for i, group in enumerate(groups):
            cluster_name = f"cluster_{i}"
            clusters[cluster_name] = group
            
            # Save cluster centroid if requested
            if save_to_registry:
                self.register_concept_cluster(
                    name=cluster_name,
                    concepts=[concepts[name] for name in group]
                )
                
        return clusters
    
    def eval(self):
        """Set the model to evaluation mode."""
        if self.encoder is not None:
            self.encoder.eval()
        if self.decoder is not None:
            self.decoder.eval()
        if self.embedding_model is not None:
            self.embedding_model.eval()
        return self
    
    def train_mode(self):
        """Set the model to training mode."""
        if self.encoder is not None:
            self.encoder.train()
        if self.decoder is not None:
            self.decoder.train()
        if self.embedding_model is not None:
            self.embedding_model.train()
        return self