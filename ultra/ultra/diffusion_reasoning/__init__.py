#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ULTRA: Ultimate Learning & Thought Reasoning Architecture
Diffusion-Based Reasoning Module

This module implements the complete diffusion-based reasoning system for ULTRA,
providing novel approaches to abstract reasoning through diffusion processes in
continuous thought spaces. The implementation is based on rigorous mathematical
foundations derived from diffusion probabilistic models, adapted for conceptual
reasoning and knowledge manipulation.

Mathematical Foundation:
- Forward Diffusion: q(z_t|z_{t-1}) = N(z_t; √(1-β_t)z_{t-1}, β_tI)
- Reverse Diffusion: p_θ(z_{t-1}|z_t) = N(z_{t-1}; μ_θ(z_t,t), Σ_θ(z_t,t))
- Thought Space: z = [z_1, z_2, ..., z_L] with hierarchical structure
- Uncertainty: U_ep(z) = E_θ[(z - E_θ[z])²], U_al(z) = E_θ[σ_θ²(x)]

Author: ULTRA Development Team
Version: 1.0.0
License: MIT
"""

import os
import sys
import logging
import warnings
import time
import threading
import multiprocessing as mp
from pathlib import Path
from typing import Dict, List, Tuple, Union, Optional, Callable, Any, NamedTuple
from dataclasses import dataclass, field
from enum import Enum, auto
from collections import defaultdict, OrderedDict
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
import json
import pickle
from abc import ABC, abstractmethod

# Configure logging EARLY
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


# Replace lines 40-60 with this corrected version:

import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.distributions import MultivariateNormal, Normal, Categorical, Dirichlet
from torch.distributions.kl import kl_divergence
from torch.optim import Adam, AdamW, SGD
from torch.optim.lr_scheduler import CosineAnnealingLR, ExponentialLR
import torch.distributed as dist
from torch.nn.parallel import DistributedDataParallel as DDP

# Scientific computing
import scipy
from scipy import optimize, stats, special
from scipy.linalg import solve, inv, det, cholesky
from scipy.sparse import csr_matrix, diags
from sklearn.metrics import mutual_info_score
from sklearn.decomposition import PCA, FastICA
from sklearn.manifold import TSNE

# Try to import UMAP separately (it's not part of sklearn)
try:
    import umap.umap_ as umap
    UMAP = umap.UMAP
    UMAP_AVAILABLE = True
    logger.info("UMAP loaded successfully")
except ImportError:
    try:
        from umap import UMAP
        UMAP_AVAILABLE = True
        logger.info("UMAP loaded from umap package")
    except ImportError:
        logger.warning("UMAP not available. Install with: pip install umap-learn")
        UMAP = None
        UMAP_AVAILABLE = False

# Fix the import statements around lines 82-100
try:
    from .conceptual_diffusion import ConceptualDiffusion
    CONCEPTUAL_DIFFUSION_AVAILABLE = True
    logger.info("ConceptualDiffusion imported successfully (relative)")
except ImportError:
    try:
        # Fallback to absolute import when run as __main__
        import sys
        from pathlib import Path
        
        # Add the current directory to Python path
        current_dir = Path(__file__).parent
        if str(current_dir) not in sys.path:
            sys.path.insert(0, str(current_dir))
        
        from conceptual_diffusion import ConceptualDiffusion
        CONCEPTUAL_DIFFUSION_AVAILABLE = True
        logger.info("ConceptualDiffusion imported successfully (absolute)")
    except ImportError as e:
        logger.warning(f"ConceptualDiffusion not available: {e}")
        ConceptualDiffusion = None
        CONCEPTUAL_DIFFUSION_AVAILABLE = False

try:
    from .thought_latent_space import ThoughtLatentSpace
    THOUGHT_LATENT_SPACE_AVAILABLE = True
    logger.info("ThoughtLatentSpace imported successfully (relative)")
except ImportError:
    try:
        # Fallback to absolute import when run as __main__
        import sys
        from pathlib import Path
        
        # Add the current directory to Python path
        current_dir = Path(__file__).parent
        if str(current_dir) not in sys.path:
            sys.path.insert(0, str(current_dir))
        
        from thought_latent_space import ThoughtLatentSpace
        THOUGHT_LATENT_SPACE_AVAILABLE = True
        logger.info("ThoughtLatentSpace imported successfully (absolute)")
    except ImportError as e:
        logger.warning(f"ThoughtLatentSpace not available: {e}")
        ThoughtLatentSpace = None
        THOUGHT_LATENT_SPACE_AVAILABLE = False




# Version and metadata
__version__ = "1.0.0"
__author__ = "ULTRA Development Team"
__license__ = "MIT"

# Mathematical constants and configurations
DEFAULT_DIFFUSION_STEPS = 1000
DEFAULT_BETA_START = 1e-4
DEFAULT_BETA_END = 0.02
DEFAULT_CONCEPT_DIM = 512
DEFAULT_THOUGHT_SPACE_DIM = 1024
DEFAULT_HIERARCHICAL_LEVELS = 4
DEFAULT_LATENT_PRECISION = 1e-6
NUMERICAL_STABILITY_EPS = 1e-12
MAX_GRADIENT_NORM = 1.0
DEFAULT_LEARNING_RATE = 1e-4

# Device configuration
DEVICE = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
if torch.cuda.is_available():
    torch.backends.cudnn.benchmark = True
    torch.backends.cudnn.deterministic = False
    logger.info(f"CUDA available: Using {DEVICE}")
else:
    logger.info(f"CUDA not available: Using {DEVICE}")
# =============================================================================
# Core Mathematical Utilities
# =============================================================================

class DiffusionMath:
    """Mathematical utilities for diffusion processes."""
    
    @staticmethod
    def create_noise_schedule(schedule_type: str, num_timesteps: int, 
                            beta_start: float, beta_end: float) -> torch.Tensor:
        """
        Create noise schedule for diffusion process.
        
        Args:
            schedule_type: Type of schedule ('linear', 'cosine', 'sigmoid')
            num_timesteps: Number of diffusion timesteps
            beta_start: Starting noise level
            beta_end: Ending noise level
            
        Returns:
            Beta schedule tensor
        """
        if schedule_type == 'linear':
            return torch.linspace(beta_start, beta_end, num_timesteps)
        elif schedule_type == 'cosine':
            # Cosine schedule from "Improved Denoising Diffusion Probabilistic Models"
            s = 0.008
            timesteps = torch.arange(num_timesteps + 1) / num_timesteps
            alphas_cumprod = torch.cos((timesteps + s) / (1 + s) * torch.pi / 2) ** 2
            alphas_cumprod = alphas_cumprod / alphas_cumprod[0]
            betas = 1 - alphas_cumprod[1:] / alphas_cumprod[:-1]
            return torch.clip(betas, min=beta_start, max=beta_end)
        elif schedule_type == 'sigmoid':
            # Sigmoid schedule for smoother transitions
            t = torch.linspace(-6, 6, num_timesteps)
            sigmoid_vals = torch.sigmoid(t)
            betas = beta_start + (beta_end - beta_start) * sigmoid_vals
            return betas
        else:
            raise ValueError(f"Unknown schedule type: {schedule_type}")
    
    @staticmethod
    def compute_diffusion_constants(betas: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        Compute all necessary constants for diffusion process.
        
        Args:
            betas: Beta schedule tensor
            
        Returns:
            Dictionary of diffusion constants
        """
        alphas = 1.0 - betas
        alphas_cumprod = torch.cumprod(alphas, dim=0)
        alphas_cumprod_prev = torch.cat([torch.tensor([1.0]), alphas_cumprod[:-1]])
        
        sqrt_alphas_cumprod = torch.sqrt(alphas_cumprod)
        sqrt_one_minus_alphas_cumprod = torch.sqrt(1.0 - alphas_cumprod)
        sqrt_recip_alphas = torch.sqrt(1.0 / alphas)
        
        # Posterior variance for reverse process
        posterior_variance = betas * (1.0 - alphas_cumprod_prev) / (1.0 - alphas_cumprod)
        
        # Posterior mean coefficients
        posterior_mean_coef1 = betas * torch.sqrt(alphas_cumprod_prev) / (1.0 - alphas_cumprod)
        posterior_mean_coef2 = (1.0 - alphas_cumprod_prev) * torch.sqrt(alphas) / (1.0 - alphas_cumprod)
        
        return {
            'betas': betas,
            'alphas': alphas,
            'alphas_cumprod': alphas_cumprod,
            'alphas_cumprod_prev': alphas_cumprod_prev,
            'sqrt_alphas_cumprod': sqrt_alphas_cumprod,
            'sqrt_one_minus_alphas_cumprod': sqrt_one_minus_alphas_cumprod,
            'sqrt_recip_alphas': sqrt_recip_alphas,
            'posterior_variance': posterior_variance,
            'posterior_mean_coef1': posterior_mean_coef1,
            'posterior_mean_coef2': posterior_mean_coef2
        }
    
    @staticmethod
    def q_sample(x_start: torch.Tensor, t: torch.Tensor, 
                noise: torch.Tensor, sqrt_alphas_cumprod: torch.Tensor,
                sqrt_one_minus_alphas_cumprod: torch.Tensor) -> torch.Tensor:
        """
        Sample from q(x_t | x_0) - forward diffusion process.
        
        Args:
            x_start: Clean concept embedding
            t: Timestep
            noise: Random noise
            sqrt_alphas_cumprod: Precomputed sqrt(α̅_t)
            sqrt_one_minus_alphas_cumprod: Precomputed sqrt(1-α̅_t)
            
        Returns:
            Noisy concept at timestep t
        """
        return (
            sqrt_alphas_cumprod[t].view(-1, 1) * x_start +
            sqrt_one_minus_alphas_cumprod[t].view(-1, 1) * noise
        )
    
    @staticmethod
    def p_sample(model: nn.Module, x_t: torch.Tensor, t: torch.Tensor,
                constants: Dict[str, torch.Tensor], guidance_scale: float = 0.0,
                guidance_fn: Optional[Callable] = None) -> torch.Tensor:
        """
        Sample from p(x_{t-1} | x_t) - reverse diffusion process.
        
        Args:
            model: Noise prediction model
            x_t: Noisy concept at timestep t
            t: Current timestep
            constants: Diffusion constants
            guidance_scale: Scale for classifier-free guidance
            guidance_fn: Optional guidance function
            
        Returns:
            Denoised concept at timestep t-1
        """
        # Predict noise
        noise_pred = model(x_t, t)
        
        # Apply guidance if provided
        if guidance_fn is not None and guidance_scale > 0:
            with torch.enable_grad():
                x_t_var = x_t.clone().detach().requires_grad_(True)
                guidance = guidance_fn(x_t_var, t)
                grad = torch.autograd.grad(guidance.sum(), x_t_var)[0]
                noise_pred = noise_pred - guidance_scale * constants['sqrt_one_minus_alphas_cumprod'][t].view(-1, 1) * grad
        
        # Compute posterior mean
        alpha_t = constants['alphas'][t]
        beta_t = constants['betas'][t]
        sqrt_recip_alpha_t = constants['sqrt_recip_alphas'][t]
        sqrt_one_minus_alpha_cumprod_t = constants['sqrt_one_minus_alphas_cumprod'][t]
        
        model_mean = sqrt_recip_alpha_t.view(-1, 1) * (
            x_t - beta_t.view(-1, 1) / sqrt_one_minus_alpha_cumprod_t.view(-1, 1) * noise_pred
        )
        
        if t[0] == 0:
            return model_mean
        else:
            posterior_variance_t = constants['posterior_variance'][t]
            noise = torch.randn_like(x_t)
            return model_mean + torch.sqrt(posterior_variance_t).view(-1, 1) * noise

# =============================================================================
# Neural Network Architectures
# =============================================================================

class SinusoidalPositionalEncoding(nn.Module):
    """Sinusoidal positional encoding for time steps."""
    
    def __init__(self, dim: int, max_period: int = 10000):
        super().__init__()
        self.dim = dim
        self.max_period = max_period
        
    def forward(self, t: torch.Tensor) -> torch.Tensor:
        """
        Args:
            t: Time steps tensor of shape (batch_size,)
            
        Returns:
            Positional encodings of shape (batch_size, dim)
        """
        half_dim = self.dim // 2
        freqs = torch.exp(
            -np.log(self.max_period) * torch.arange(half_dim, device=t.device) / half_dim
        )
        args = t.unsqueeze(-1) * freqs.unsqueeze(0)
        embeddings = torch.cat([torch.cos(args), torch.sin(args)], dim=-1)
        
        if self.dim % 2 == 1:
            embeddings = torch.cat([embeddings, torch.zeros_like(embeddings[:, :1])], dim=-1)
            
        return embeddings

class ResidualBlock(nn.Module):
    """Residual block with time embedding."""
    
    def __init__(self, in_dim: int, out_dim: int, time_dim: int, 
                 dropout: float = 0.1, activation: str = 'swish'):
        super().__init__()
        self.in_dim = in_dim
        self.out_dim = out_dim
        
        # Main pathway
        self.norm1 = nn.LayerNorm(in_dim)
        self.linear1 = nn.Linear(in_dim, out_dim)
        
        # Time embedding
        self.time_mlp = nn.Sequential(
            nn.Linear(time_dim, out_dim),
            nn.SiLU() if activation == 'swish' else nn.ReLU(),
            nn.Linear(out_dim, out_dim)
        )
        
        self.norm2 = nn.LayerNorm(out_dim)
        self.linear2 = nn.Linear(out_dim, out_dim)
        
        # Residual connection
        self.residual = nn.Linear(in_dim, out_dim) if in_dim != out_dim else nn.Identity()
        
        self.dropout = nn.Dropout(dropout)
        self.activation = nn.SiLU() if activation == 'swish' else nn.ReLU()
        
    def forward(self, x: torch.Tensor, time_emb: torch.Tensor) -> torch.Tensor:
        """
        Args:
            x: Input tensor of shape (batch_size, in_dim)
            time_emb: Time embedding of shape (batch_size, time_dim)
            
        Returns:
            Output tensor of shape (batch_size, out_dim)
        """
        residual = self.residual(x)
        
        # First transformation
        h = self.norm1(x)
        h = self.linear1(h)
        h = self.activation(h)
        
        # Add time embedding
        time_h = self.time_mlp(time_emb)
        h = h + time_h
        
        # Second transformation
        h = self.norm2(h)
        h = self.dropout(h)
        h = self.linear2(h)
        
        return self.activation(h + residual)

class AttentionBlock(nn.Module):
    """Multi-head self-attention block."""
    
    def __init__(self, dim: int, num_heads: int = 8, dropout: float = 0.1):
        super().__init__()
        self.dim = dim
        self.num_heads = num_heads
        self.head_dim = dim // num_heads
        assert dim % num_heads == 0
        
        self.norm = nn.LayerNorm(dim)
        self.attention = nn.MultiheadAttention(
            embed_dim=dim,
            num_heads=num_heads,
            dropout=dropout,
            batch_first=True
        )
        self.ff = nn.Sequential(
            nn.Linear(dim, dim * 4),
            nn.SiLU(),
            nn.Dropout(dropout),
            nn.Linear(dim * 4, dim)
        )
        self.norm2 = nn.LayerNorm(dim)
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, x: torch.Tensor, mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        Args:
            x: Input tensor of shape (batch_size, seq_len, dim)
            mask: Optional attention mask
            
        Returns:
            Output tensor of shape (batch_size, seq_len, dim)
        """
        # Self-attention
        residual = x
        x = self.norm(x)
        attn_out, _ = self.attention(x, x, x, attn_mask=mask)
        x = residual + self.dropout(attn_out)
        
        # Feed-forward
        residual = x
        x = self.norm2(x)
        x = residual + self.dropout(self.ff(x))
        
        return x

class ConceptNoisePredictor(nn.Module):
    """
    Neural network for predicting noise in conceptual diffusion process.
    
    This network implements the ε_θ(z_t, t) function that predicts the noise
    added to a concept at timestep t, enabling the reverse diffusion process.
    """
    
    def __init__(self, concept_dim: int, hidden_dim: int = None, 
                 num_layers: int = 6, num_heads: int = 8, 
                 time_embedding_dim: int = None, dropout: float = 0.1):
        super().__init__()
        self.concept_dim = concept_dim
        self.hidden_dim = hidden_dim or concept_dim * 2
        self.time_embedding_dim = time_embedding_dim or concept_dim // 4
        
        # Time embedding
        self.time_encoder = SinusoidalPositionalEncoding(self.time_embedding_dim)
        
        # Input projection
        self.input_projection = nn.Linear(concept_dim, self.hidden_dim)
        
        # Residual blocks with time conditioning
        self.blocks = nn.ModuleList([
            ResidualBlock(
                in_dim=self.hidden_dim,
                out_dim=self.hidden_dim,
                time_dim=self.time_embedding_dim,
                dropout=dropout
            ) for _ in range(num_layers)
        ])
        
        # Attention blocks for global dependencies
        self.attention_blocks = nn.ModuleList([
            AttentionBlock(self.hidden_dim, num_heads, dropout)
            for _ in range(num_layers // 2)
        ])
        
        # Output projection
        self.output_projection = nn.Sequential(
            nn.LayerNorm(self.hidden_dim),
            nn.Linear(self.hidden_dim, concept_dim)
        )
        
        # Initialize weights
        self.apply(self._init_weights)
        
    def _init_weights(self, module):
        """Initialize weights using Xavier initialization."""
        if isinstance(module, nn.Linear):
            torch.nn.init.xavier_uniform_(module.weight)
            if module.bias is not None:
                torch.nn.init.zeros_(module.bias)
        elif isinstance(module, nn.LayerNorm):
            torch.nn.init.ones_(module.weight)
            torch.nn.init.zeros_(module.bias)
    
    def forward(self, z_t: torch.Tensor, t: torch.Tensor) -> torch.Tensor:
        """
        Predict noise for reverse diffusion process.
        
        Args:
            z_t: Noisy concept embeddings of shape (batch_size, concept_dim)
            t: Timesteps of shape (batch_size,)
            
        Returns:
            Predicted noise of shape (batch_size, concept_dim)
        """
        batch_size = z_t.shape[0]
        
        # Encode time
        time_emb = self.time_encoder(t)  # (batch_size, time_embedding_dim)
        
        # Project input
        h = self.input_projection(z_t)  # (batch_size, hidden_dim)
        
        # Apply residual blocks with attention
        attention_idx = 0
        for i, block in enumerate(self.blocks):
            h = block(h, time_emb)
            
            # Apply attention every other block
            if i % 2 == 1 and attention_idx < len(self.attention_blocks):
                h_reshaped = h.unsqueeze(1)  # (batch_size, 1, hidden_dim)
                h_attended = self.attention_blocks[attention_idx](h_reshaped)
                h = h_attended.squeeze(1)  # (batch_size, hidden_dim)
                attention_idx += 1
        
        # Output projection
        noise_pred = self.output_projection(h)
        
        return noise_pred

# =============================================================================
# Conceptual Diffusion Process
# =============================================================================

class ConceptualDiffusion(nn.Module):
    """
    Conceptual diffusion model for reasoning in abstract thought spaces.
    
    This class implements the core diffusion process adapted for conceptual
    reasoning, where concepts are represented as vectors in a continuous space
    and diffusion is used to explore and manipulate these concepts.
    
    Mathematical foundation:
    - Forward: q(z_t|z_{t-1}) = N(z_t; √(1-β_t)z_{t-1}, β_tI)
    - Reverse: p_θ(z_{t-1}|z_t) = N(z_{t-1}; μ_θ(z_t,t), Σ_θ(z_t,t))
    """
    
    def __init__(self, noise_predictor: ConceptNoisePredictor, 
                 concept_dim: int, num_timesteps: int = DEFAULT_DIFFUSION_STEPS,
                 noise_schedule: str = 'cosine', beta_start: float = DEFAULT_BETA_START,
                 beta_end: float = DEFAULT_BETA_END, device: str = None):
        super().__init__()
        self.noise_predictor = noise_predictor
        self.concept_dim = concept_dim
        self.num_timesteps = num_timesteps
        self.device = device or DEVICE
        
        # Create noise schedule
        betas = DiffusionMath.create_noise_schedule(
            noise_schedule, num_timesteps, beta_start, beta_end
        ).to(self.device)
        
        # Compute diffusion constants
        self.constants = DiffusionMath.compute_diffusion_constants(betas)
        for key, value in self.constants.items():
            self.register_buffer(key, value)
        
        # Loss function
        self.loss_fn = nn.MSELoss(reduction='mean')
        
    def forward_diffusion(self, concepts: torch.Tensor, 
                         timesteps: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Apply forward diffusion process q(z_t | z_0).
        
        Args:
            concepts: Clean concept embeddings (batch_size, concept_dim)
            timesteps: Timesteps for each concept (batch_size,)
            
        Returns:
            Tuple of (noisy_concepts, noise)
        """
        noise = torch.randn_like(concepts)
        noisy_concepts = DiffusionMath.q_sample(
            concepts, timesteps, noise,
            self.sqrt_alphas_cumprod, self.sqrt_one_minus_alphas_cumprod
        )
        return noisy_concepts, noise
    
    def reverse_diffusion_step(self, z_t: torch.Tensor, t: torch.Tensor,
                              guidance_scale: float = 0.0,
                              guidance_fn: Optional[Callable] = None) -> torch.Tensor:
        """
        Single step of reverse diffusion process p(z_{t-1} | z_t).
        
        Args:
            z_t: Noisy concepts at timestep t
            t: Current timestep
            guidance_scale: Scale for guidance
            guidance_fn: Optional guidance function
            
        Returns:
            Denoised concepts at timestep t-1
        """
        return DiffusionMath.p_sample(
            self.noise_predictor, z_t, t, self.constants,
            guidance_scale, guidance_fn
        )
    
    def sample_concepts(self, num_samples: int, guidance_scale: float = 0.0,
                       guidance_fn: Optional[Callable] = None,
                       return_trajectory: bool = False) -> Union[torch.Tensor, List[torch.Tensor]]:
        """
        Sample new concepts using the reverse diffusion process.
        
        Args:
            num_samples: Number of concepts to sample
            guidance_scale: Scale for classifier-free guidance
            guidance_fn: Optional guidance function
            return_trajectory: Whether to return full sampling trajectory
            
        Returns:
            Sampled concepts or full trajectory
        """
        # Start from pure noise
        z = torch.randn(num_samples, self.concept_dim, device=self.device)
        
        trajectory = [z.clone()] if return_trajectory else None
        
        # Reverse diffusion sampling
        for t in reversed(range(self.num_timesteps)):
            t_tensor = torch.full((num_samples,), t, device=self.device, dtype=torch.long)
            z = self.reverse_diffusion_step(z, t_tensor, guidance_scale, guidance_fn)
            
            if return_trajectory:
                trajectory.append(z.clone())
        
        if return_trajectory:
            return trajectory
        else:
            return z
    
    def compute_loss(self, concepts: torch.Tensor) -> torch.Tensor:
        """
        Compute training loss for the diffusion model.
        
        Args:
            concepts: Clean concept embeddings
            
        Returns:
            Diffusion loss
        """
        batch_size = concepts.shape[0]
        
        # Sample random timesteps
        t = torch.randint(0, self.num_timesteps, (batch_size,), device=self.device)
        
        # Forward diffusion
        noisy_concepts, noise = self.forward_diffusion(concepts, t)
        
        # Predict noise
        noise_pred = self.noise_predictor(noisy_concepts, t)
        
        # Compute loss
        loss = self.loss_fn(noise_pred, noise)
        
        return loss
    
    def interpolate_concepts(self, concept_a: torch.Tensor, concept_b: torch.Tensor,
                           num_steps: int = 10, interpolation_type: str = 'spherical') -> List[torch.Tensor]:
        """
        Interpolate between two concepts in the diffusion space.
        
        Args:
            concept_a: First concept
            concept_b: Second concept
            num_steps: Number of interpolation steps
            interpolation_type: Type of interpolation ('linear', 'spherical')
            
        Returns:
            List of interpolated concepts
        """
        if interpolation_type == 'linear':
            alphas = torch.linspace(0, 1, num_steps, device=self.device)
            interpolated = [(1 - alpha) * concept_a + alpha * concept_b for alpha in alphas]
        elif interpolation_type == 'spherical':
            # Spherical linear interpolation
            dot = torch.sum(concept_a * concept_b, dim=-1, keepdim=True)
            dot = torch.clamp(dot, -1.0 + NUMERICAL_STABILITY_EPS, 1.0 - NUMERICAL_STABILITY_EPS)
            
            theta = torch.acos(torch.abs(dot))
            sin_theta = torch.sin(theta)
            
            alphas = torch.linspace(0, 1, num_steps, device=self.device)
            interpolated = []
            
            for alpha in alphas:
                if torch.abs(sin_theta) < NUMERICAL_STABILITY_EPS:
                    # Linear interpolation fallback
                    result = (1 - alpha) * concept_a + alpha * concept_b
                else:
                    w1 = torch.sin((1 - alpha) * theta) / sin_theta
                    w2 = torch.sin(alpha * theta) / sin_theta
                    result = w1 * concept_a + w2 * concept_b
                interpolated.append(result)
        else:
            raise ValueError(f"Unknown interpolation type: {interpolation_type}")
        
        return interpolated

# =============================================================================
# Thought Latent Space
# =============================================================================

class HierarchicalEncoder(nn.Module):
    """Hierarchical encoder for multi-level concept representation."""
    
    def __init__(self, input_dim: int, output_dims: List[int], 
                 hidden_dim: int = None, num_levels: int = 4):
        super().__init__()
        self.input_dim = input_dim
        self.output_dims = output_dims
        self.num_levels = num_levels
        hidden_dim = hidden_dim or input_dim * 2
        
        # Level-specific encoders
        self.level_encoders = nn.ModuleList()
        current_dim = input_dim
        
        for i, out_dim in enumerate(output_dims):
            encoder = nn.Sequential(
                nn.Linear(current_dim, hidden_dim),
                nn.LayerNorm(hidden_dim),
                nn.SiLU(),
                nn.Dropout(0.1),
                nn.Linear(hidden_dim, hidden_dim),
                nn.LayerNorm(hidden_dim),
                nn.SiLU(),
                nn.Linear(hidden_dim, out_dim)
            )
            self.level_encoders.append(encoder)
            current_dim = out_dim
    
    def forward(self, x: torch.Tensor) -> List[torch.Tensor]:
        """
        Encode input to hierarchical representations.
        
        Args:
            x: Input tensor
            
        Returns:
            List of representations at different levels
        """
        representations = []
        current = x
        
        for encoder in self.level_encoders:
            current = encoder(current)
            representations.append(current)
        
        return representations

class HierarchicalDecoder(nn.Module):
    """Hierarchical decoder for reconstructing from multi-level representations."""
    
    def __init__(self, input_dims: List[int], output_dim: int, 
                 hidden_dim: int = None):
        super().__init__()
        self.input_dims = input_dims
        self.output_dim = output_dim
        hidden_dim = hidden_dim or output_dim * 2
        
        # Combine all levels
        total_input_dim = sum(input_dims)
        
        self.decoder = nn.Sequential(
            nn.Linear(total_input_dim, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.SiLU(),
            nn.Dropout(0.1),
            nn.Linear(hidden_dim, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.SiLU(),
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.LayerNorm(hidden_dim // 2),
            nn.SiLU(),
            nn.Linear(hidden_dim // 2, output_dim)
        )
    
    def forward(self, hierarchical_repr: List[torch.Tensor]) -> torch.Tensor:
        """
        Decode hierarchical representations to output.
        
        Args:
            hierarchical_repr: List of representations at different levels
            
        Returns:
            Reconstructed output
        """
        # Concatenate all levels
        combined = torch.cat(hierarchical_repr, dim=-1)
        return self.decoder(combined)

class ThoughtVector(NamedTuple):
    """Structured representation of a thought vector."""
    concept: torch.Tensor
    hierarchical_levels: List[torch.Tensor]
    semantic_features: torch.Tensor
    relational_features: torch.Tensor
    uncertainty: torch.Tensor
    metadata: Dict[str, Any]

class ThoughtLatentSpace(nn.Module):
    """
    Continuous latent space for representing and manipulating abstract thoughts.
    
    This implements a hierarchical, semantically organized latent space where:
    - Similar concepts are close in the space
    - Relationships are represented as vectors
    - Composition operations are supported
    - Uncertainty is explicitly modeled
    
    Mathematical properties:
    - Hierarchical: z = [z_1, z_2, ..., z_L] 
    - Semantic continuity: sim(c_i, c_j) ≈ exp(-d(z_i, z_j))
    - Relational structure: r_ij = z_j - z_i
    - Compositional: z_new = f(z_1, z_2, ..., z_n, r_1, r_2, ..., r_m)
    """
    
    def __init__(self, concept_dim: int, thought_space_dim: int, 
                 num_hierarchical_levels: int = DEFAULT_HIERARCHICAL_LEVELS,
                 semantic_dim: int = None, relational_dim: int = None,
                 device: str = None):
        super().__init__()
        self.concept_dim = concept_dim
        self.thought_space_dim = thought_space_dim
        self.num_levels = num_hierarchical_levels
        self.semantic_dim = semantic_dim or thought_space_dim // 4
        self.relational_dim = relational_dim or thought_space_dim // 4
        self.device = device or DEVICE
        
        # Hierarchical dimensions for each level
        level_dims = [
            thought_space_dim // (2 ** i) 
            for i in range(num_hierarchical_levels)
        ]
        level_dims = [max(dim, 64) for dim in level_dims]  # Minimum dimension
        
        # Encoders and decoders
        self.hierarchical_encoder = HierarchicalEncoder(
            input_dim=concept_dim,
            output_dims=level_dims,
            num_levels=num_hierarchical_levels
        )
        
        self.hierarchical_decoder = HierarchicalDecoder(
            input_dims=level_dims,
            output_dim=concept_dim
        )
        
        # Semantic feature extractor
        self.semantic_extractor = nn.Sequential(
            nn.Linear(concept_dim, thought_space_dim),
            nn.LayerNorm(thought_space_dim),
            nn.SiLU(),
            nn.Linear(thought_space_dim, self.semantic_dim)
        )
        
        # Relational feature extractor
        self.relational_extractor = nn.Sequential(
            nn.Linear(concept_dim * 2, thought_space_dim),
            nn.LayerNorm(thought_space_dim),
            nn.SiLU(),
            nn.Linear(thought_space_dim, self.relational_dim)
        )
        
        # Uncertainty estimator
        self.uncertainty_estimator = nn.Sequential(
            nn.Linear(sum(level_dims) + self.semantic_dim, thought_space_dim // 2),
            nn.LayerNorm(thought_space_dim // 2),
            nn.SiLU(),
            nn.Linear(thought_space_dim // 2, 2)  # Mean and log variance
        )
        
        # Composition network
        self.composition_network = nn.Sequential(
            nn.Linear(thought_space_dim * 3, thought_space_dim * 2),  # concepts + relations
            nn.LayerNorm(thought_space_dim * 2),
            nn.SiLU(),
            nn.Dropout(0.1),
            nn.Linear(thought_space_dim * 2, thought_space_dim),
            nn.LayerNorm(thought_space_dim),
            nn.SiLU(),
            nn.Linear(thought_space_dim, concept_dim)
        )
        
        # Distance metrics
        self.distance_metrics = {
            'euclidean': lambda a, b: torch.norm(a - b, dim=-1),
            'cosine': lambda a, b: 1 - F.cosine_similarity(a, b, dim=-1),
            'manhattan': lambda a, b: torch.sum(torch.abs(a - b), dim=-1),
            'mahalanobis': self._mahalanobis_distance
        }
        
        # Initialize weights
        self.apply(self._init_weights)
        
    def _init_weights(self, module):
        """Initialize weights using Xavier initialization."""
        if isinstance(module, nn.Linear):
            torch.nn.init.xavier_uniform_(module.weight)
            if module.bias is not None:
                torch.nn.init.zeros_(module.bias)
        elif isinstance(module, nn.LayerNorm):
            torch.nn.init.ones_(module.weight)
            torch.nn.init.zeros_(module.bias)
    
    def _mahalanobis_distance(self, a: torch.Tensor, b: torch.Tensor) -> torch.Tensor:
        """Compute Mahalanobis distance with learned covariance."""
        diff = a - b
        # Use identity covariance for simplicity - could be learned
        return torch.sqrt(torch.sum(diff ** 2, dim=-1))
    
    def encode_concept(self, concept: torch.Tensor) -> ThoughtVector:
        """
        Encode a concept into the thought latent space.
        
        Args:
            concept: Concept embedding tensor
            
        Returns:
            ThoughtVector with hierarchical representation
        """
        # Hierarchical encoding
        hierarchical_levels = self.hierarchical_encoder(concept)
        
        # Semantic features
        semantic_features = self.semantic_extractor(concept)
        
        # Estimate uncertainty
        combined_features = torch.cat(hierarchical_levels + [semantic_features], dim=-1)
        uncertainty_params = self.uncertainty_estimator(combined_features)
        uncertainty_mean, uncertainty_logvar = uncertainty_params.chunk(2, dim=-1)
        uncertainty = torch.exp(0.5 * uncertainty_logvar)
        
        # For relational features, use zero tensor (will be computed when needed)
        relational_features = torch.zeros_like(semantic_features)
        
        return ThoughtVector(
            concept=concept,
            hierarchical_levels=hierarchical_levels,
            semantic_features=semantic_features,
            relational_features=relational_features,
            uncertainty=uncertainty,
            metadata={'encoding_time': time.time()}
        )
    
    def decode_concept(self, thought_vector: ThoughtVector) -> torch.Tensor:
        """
        Decode a thought vector back to concept space.
        
        Args:
            thought_vector: ThoughtVector to decode
            
        Returns:
            Reconstructed concept embedding
        """
        return self.hierarchical_decoder(thought_vector.hierarchical_levels)
    
    def compute_semantic_similarity(self, concept_a: torch.Tensor, 
                                  concept_b: torch.Tensor,
                                  metric: str = 'cosine') -> torch.Tensor:
        """
        Compute semantic similarity between concepts.
        
        Args:
            concept_a: First concept
            concept_b: Second concept
            metric: Distance metric to use
            
        Returns:
            Similarity score
        """
        if metric not in self.distance_metrics:
            raise ValueError(f"Unknown metric: {metric}")
        
        # Encode both concepts
        thought_a = self.encode_concept(concept_a)
        thought_b = self.encode_concept(concept_b)
        
        # Compute distance using semantic features
        distance = self.distance_metrics[metric](
            thought_a.semantic_features, 
            thought_b.semantic_features
        )
        
        # Convert distance to similarity
        similarity = torch.exp(-distance)
        
        return similarity
    
    def extract_relationship(self, concept_a: torch.Tensor, 
                           concept_b: torch.Tensor) -> torch.Tensor:
        """
        Extract relational features between two concepts.
        
        Args:
            concept_a: First concept
            concept_b: Second concept
            
        Returns:
            Relational feature vector
        """
        # Concatenate concepts for relational processing
        combined = torch.cat([concept_a, concept_b], dim=-1)
        relational_features = self.relational_extractor(combined)
        
        return relational_features
    
    def compose_concepts(self, concepts: List[torch.Tensor], 
                        relationships: Optional[List[torch.Tensor]] = None,
                        weights: Optional[List[float]] = None) -> torch.Tensor:
        """
        Compose multiple concepts using learned composition rules.
        
        Args:
            concepts: List of concept embeddings
            relationships: Optional list of relational features
            weights: Optional weights for concept combination
            
        Returns:
            Composed concept
        """
        if len(concepts) < 2:
            return concepts[0] if concepts else torch.zeros(self.concept_dim, device=self.device)
        
        # Default weights
        if weights is None:
            weights = [1.0 / len(concepts)] * len(concepts)
        
        # Weighted combination of concepts
        weighted_concepts = torch.stack([
            w * concept for w, concept in zip(weights, concepts)
        ]).sum(dim=0)
        
        # Encode to thought space
        thought_vectors = [self.encode_concept(concept) for concept in concepts]
        
        # Combine hierarchical levels
        combined_hierarchical = []
        for level_idx in range(self.num_levels):
            level_features = torch.stack([
                tv.hierarchical_levels[level_idx] for tv in thought_vectors
            ])
            combined_level = torch.mean(level_features, dim=0)
            combined_hierarchical.append(combined_level)
        
        # Extract relationships if not provided
        if relationships is None:
            relationships = []
            for i in range(len(concepts) - 1):
                rel = self.extract_relationship(concepts[i], concepts[i + 1])
                relationships.append(rel)
        
        # Combine relationships
        if relationships:
            combined_relations = torch.stack(relationships).mean(dim=0)
        else:
            combined_relations = torch.zeros(self.relational_dim, device=self.device)
        
        # Prepare input for composition network
        combined_thought = torch.cat(combined_hierarchical, dim=-1)
        composition_input = torch.cat([
            weighted_concepts,
            combined_thought,
            combined_relations
        ], dim=-1)
        
        # Apply composition network
        composed_concept = self.composition_network(composition_input)
        
        return composed_concept
    
    def decompose_concept(self, concept: torch.Tensor, 
                         num_components: int = 3) -> List[torch.Tensor]:
        """
        Decompose a concept into constituent components.
        
        Args:
            concept: Concept to decompose
            num_components: Number of components to extract
            
        Returns:
            List of component concepts
        """
        # Encode concept
        thought_vector = self.encode_concept(concept)
        
        # Use PCA to find principal components in the hierarchical space
        combined_features = torch.cat(thought_vector.hierarchical_levels, dim=-1)
        
        if combined_features.dim() == 1:
            combined_features = combined_features.unsqueeze(0)
        
        # Simplified decomposition using learned projections
        components = []
        for i in range(num_components):
            # Create projection direction
            direction = torch.randn_like(combined_features)
            direction = F.normalize(direction, dim=-1)
            
            # Project onto direction
            projection = torch.sum(combined_features * direction, dim=-1, keepdim=True) * direction
            
            # Decode back to concept space
            # Create mock hierarchical levels for decoding
            proj_dim = projection.shape[-1]
            total_dim = sum(len(level) for level in thought_vector.hierarchical_levels)
            
            if proj_dim == total_dim:
                # Split projection back into hierarchical levels
                split_sizes = [len(level) for level in thought_vector.hierarchical_levels]
                projected_levels = torch.split(projection, split_sizes, dim=-1)
                component_concept = self.hierarchical_decoder(list(projected_levels))
            else:
                # Fallback: linear projection
                component_concept = F.linear(
                    projection, 
                    torch.randn(self.concept_dim, proj_dim, device=self.device)
                )
            
            components.append(component_concept.squeeze(0) if component_concept.dim() > 1 else component_concept)
        
        return components
    
    def interpolate_concepts(self, concept_a: torch.Tensor, concept_b: torch.Tensor,
                           num_steps: int = 10, interpolation_type: str = 'spherical') -> List[torch.Tensor]:
        """
        Interpolate between concepts in the thought latent space.
        
        Args:
            concept_a: First concept
            concept_b: Second concept
            num_steps: Number of interpolation steps
            interpolation_type: Type of interpolation
            
        Returns:
            List of interpolated concepts
        """
        # Encode both concepts
        thought_a = self.encode_concept(concept_a)
        thought_b = self.encode_concept(concept_b)
        
        # Interpolate in each hierarchical level
        interpolated_thoughts = []
        alphas = torch.linspace(0, 1, num_steps, device=self.device)
        
        for alpha in alphas:
            interpolated_levels = []
            
            for level_a, level_b in zip(thought_a.hierarchical_levels, thought_b.hierarchical_levels):
                if interpolation_type == 'linear':
                    interpolated_level = (1 - alpha) * level_a + alpha * level_b
                elif interpolation_type == 'spherical':
                    # Spherical linear interpolation
                    dot = torch.sum(level_a * level_b, dim=-1, keepdim=True)
                    dot = torch.clamp(dot, -1.0 + NUMERICAL_STABILITY_EPS, 1.0 - NUMERICAL_STABILITY_EPS)
                    
                    theta = torch.acos(torch.abs(dot))
                    sin_theta = torch.sin(theta)
                    
                    if torch.abs(sin_theta) < NUMERICAL_STABILITY_EPS:
                        interpolated_level = (1 - alpha) * level_a + alpha * level_b
                    else:
                        w1 = torch.sin((1 - alpha) * theta) / sin_theta
                        w2 = torch.sin(alpha * theta) / sin_theta
                        interpolated_level = w1 * level_a + w2 * level_b
                else:
                    raise ValueError(f"Unknown interpolation type: {interpolation_type}")
                
                interpolated_levels.append(interpolated_level)
            
            # Decode back to concept space
            interpolated_concept = self.hierarchical_decoder(interpolated_levels)
            interpolated_thoughts.append(interpolated_concept)
        
        return interpolated_thoughts
    
    def explore_concept_neighborhood(self, concept: torch.Tensor, 
                                   num_samples: int = 5, noise_level: float = 0.1,
                                   exploration_type: str = 'gaussian') -> List[torch.Tensor]:
        """
        Explore the neighborhood around a concept.
        
        Args:
            concept: Central concept
            num_samples: Number of samples to generate
            noise_level: Level of noise for exploration
            exploration_type: Type of exploration noise
            
        Returns:
            List of neighboring concepts
        """
        # Encode concept
        thought_vector = self.encode_concept(concept)
        
        neighbors = []
        for _ in range(num_samples):
            # Add noise to hierarchical levels
            noisy_levels = []
            for level in thought_vector.hierarchical_levels:
                if exploration_type == 'gaussian':
                    noise = torch.randn_like(level) * noise_level
                elif exploration_type == 'uniform':
                    noise = (torch.rand_like(level) - 0.5) * 2 * noise_level
                else:
                    raise ValueError(f"Unknown exploration type: {exploration_type}")
                
                noisy_level = level + noise
                noisy_levels.append(noisy_level)
            
            # Decode back to concept space
            neighbor_concept = self.hierarchical_decoder(noisy_levels)
            neighbors.append(neighbor_concept)
        
        return neighbors

# =============================================================================
# Reverse Diffusion Reasoning
# =============================================================================

class GoalGuidanceFunction:
    """Function for guiding diffusion toward specific goals."""
    
    def __init__(self, goal_concept: torch.Tensor, guidance_strength: float = 1.0):
        self.goal_concept = goal_concept
        self.guidance_strength = guidance_strength
    
    def __call__(self, z_t: torch.Tensor, t: torch.Tensor) -> torch.Tensor:
        """
        Compute guidance toward goal concept.
        
        Args:
            z_t: Current noisy concept
            t: Current timestep
            
        Returns:
            Guidance signal
        """
        # Distance to goal
        distance = torch.norm(z_t - self.goal_concept, dim=-1)
        
        # Exponential decay with timestep (stronger guidance early in process)
        time_weight = torch.exp(-t.float() / 100.0)
        
        # Guidance signal (negative gradient of distance)
        guidance = -self.guidance_strength * time_weight.view(-1, 1) * distance.view(-1, 1)
        
        return guidance

class ConstraintFunction:
    """Function for enforcing constraints during diffusion."""
    
    def __init__(self, constraint_type: str, target_value: float, 
                 feature_extractor: Optional[Callable] = None):
        self.constraint_type = constraint_type
        self.target_value = target_value
        self.feature_extractor = feature_extractor or (lambda x: torch.norm(x, dim=-1))
    
    def __call__(self, z_t: torch.Tensor) -> torch.Tensor:
        """
        Evaluate constraint satisfaction.
        
        Args:
            z_t: Current concept
            
        Returns:
            Constraint satisfaction score
        """
        feature_value = self.feature_extractor(z_t)
        
        if self.constraint_type == 'equality':
            return -torch.abs(feature_value - self.target_value)
        elif self.constraint_type == 'inequality_gt':
            return torch.relu(feature_value - self.target_value)
        elif self.constraint_type == 'inequality_lt':
            return torch.relu(self.target_value - feature_value)
        else:
            raise ValueError(f"Unknown constraint type: {self.constraint_type}")

class ReverseDiffusionReasoning:
    """
    Reverse diffusion reasoning system for goal-directed concept manipulation.
    
    This class implements reasoning that works backward from desired outcomes
    to construct solution pathways using guided diffusion processes.
    
    Key capabilities:
    - Goal-directed reasoning from start to target concepts
    - Constraint-guided concept generation
    - Multi-objective optimization in concept space
    - Uncertainty-aware reasoning path construction
    """
    
    def __init__(self, conceptual_diffusion: ConceptualDiffusion,
                 thought_latent_space: ThoughtLatentSpace,
                 guidance_scale: float = 3.0, device: str = None):
        self.conceptual_diffusion = conceptual_diffusion
        self.thought_latent_space = thought_latent_space
        self.guidance_scale = guidance_scale
        self.device = device or DEVICE
        
        # Reasoning history for learning
        self.reasoning_history = []
        self.success_patterns = {}
        
    def goal_directed_reasoning(self, source_concepts: List[torch.Tensor],
                              goal_concept: torch.Tensor, num_steps: int = 10,
                              guidance_scale: float = None,
                              return_confidences: bool = True) -> Tuple[List[torch.Tensor], Optional[List[float]]]:
        """
        Perform goal-directed reasoning from source concepts to goal concept.
        
        Args:
            source_concepts: List of starting concepts
            goal_concept: Target goal concept
            num_steps: Number of reasoning steps
            guidance_scale: Guidance strength (uses default if None)
            return_confidences: Whether to return confidence scores
            
        Returns:
            Tuple of (reasoning_path, confidence_scores)
        """
        guidance_scale = guidance_scale or self.guidance_scale
        
        # Compose source concepts into starting point
        if len(source_concepts) > 1:
            current_concept = self.thought_latent_space.compose_concepts(source_concepts)
        else:
            current_concept = source_concepts[0]
        
        reasoning_path = [current_concept.clone()]
        confidences = [] if return_confidences else None
        
        # Create goal guidance function
        goal_guidance = GoalGuidanceFunction(goal_concept, guidance_scale)
        
        for step in range(num_steps):
            # Add noise to current concept (partial forward diffusion)
            noise_level = (num_steps - step) / num_steps  # Decreasing noise
            timestep = int(noise_level * self.conceptual_diffusion.num_timesteps * 0.3)  # Max 30% of total steps
            
            if timestep > 0:
                timestep_tensor = torch.tensor([timestep], device=self.device)
                noise = torch.randn_like(current_concept)
                
                # Apply partial forward diffusion
                noisy_concept = DiffusionMath.q_sample(
                    current_concept.unsqueeze(0), timestep_tensor, noise.unsqueeze(0),
                    self.conceptual_diffusion.sqrt_alphas_cumprod,
                    self.conceptual_diffusion.sqrt_one_minus_alphas_cumprod
                ).squeeze(0)
                
                # Apply reverse diffusion with goal guidance
                next_concept = self.conceptual_diffusion.reverse_diffusion_step(
                    noisy_concept.unsqueeze(0), timestep_tensor,
                    guidance_scale=guidance_scale,
                    guidance_fn=goal_guidance
                ).squeeze(0)
            else:
                # Direct interpolation for final steps
                alpha = step / num_steps
                next_concept = (1 - alpha) * current_concept + alpha * goal_concept
            
            reasoning_path.append(next_concept)
            
            if return_confidences:
                # Compute confidence based on distance to goal and path smoothness
                goal_distance = torch.norm(next_concept - goal_concept).item()
                if len(reasoning_path) > 1:
                    path_smoothness = torch.norm(next_concept - reasoning_path[-2]).item()
                    confidence = 1.0 / (1.0 + goal_distance + 0.1 * path_smoothness)
                else:
                    confidence = 1.0 / (1.0 + goal_distance)
                confidences.append(confidence)
            
            current_concept = next_concept
        
        # Store reasoning episode for learning
        self.reasoning_history.append({
            'source_concepts': source_concepts,
            'goal_concept': goal_concept,
            'reasoning_path': reasoning_path,
            'success_score': confidences[-1] if confidences else None
        })
        
        return reasoning_path, confidences
    
    def constraint_guided_generation(self, constraints: List[ConstraintFunction],
                                   num_samples: int = 5, num_steps: int = None,
                                   optimization_steps: int = 10) -> List[torch.Tensor]:
        """
        Generate concepts that satisfy given constraints.
        
        Args:
            constraints: List of constraint functions
            num_samples: Number of concepts to generate
            num_steps: Number of diffusion steps (uses default if None)
            optimization_steps: Number of constraint optimization steps
            
        Returns:
            List of constraint-satisfying concepts
        """
        num_steps = num_steps or self.conceptual_diffusion.num_timesteps // 4
        
        # Define constraint guidance function
        def constraint_guidance(z_t: torch.Tensor, t: torch.Tensor) -> torch.Tensor:
            guidance = torch.zeros_like(z_t)
            
            for constraint in constraints:
                constraint_score = constraint(z_t)
                if constraint_score.dim() == 1:
                    constraint_score = constraint_score.unsqueeze(-1)
                guidance += constraint_score
            
            return guidance
        
        # Generate initial samples
        samples = self.conceptual_diffusion.sample_concepts(
            num_samples=num_samples,
            guidance_fn=constraint_guidance,
            guidance_scale=self.guidance_scale
        )
        
        # Refine samples with gradient-based optimization
        optimized_samples = []
        for sample in samples:
            optimized_sample = self._optimize_constraints(sample, constraints, optimization_steps)
            optimized_samples.append(optimized_sample)
        
        return optimized_samples
    
    def _optimize_constraints(self, initial_sample: torch.Tensor,
                            constraints: List[ConstraintFunction],
                            num_steps: int) -> torch.Tensor:
        """Optimize a sample to better satisfy constraints."""
        sample = initial_sample.clone().requires_grad_(True)
        optimizer = Adam([sample], lr=0.01)
        
        for _ in range(num_steps):
            optimizer.zero_grad()
            
            # Compute constraint loss
            total_constraint_score = torch.tensor(0.0, device=self.device)
            for constraint in constraints:
                score = constraint(sample.unsqueeze(0)).squeeze(0)
                total_constraint_score += -score.mean()  # Negative because we want to maximize
            
            total_constraint_score.backward()
            optimizer.step()
        
        return sample.detach()
    
    def multi_objective_reasoning(self, source_concept: torch.Tensor,
                                objectives: List[torch.Tensor],
                                objective_weights: Optional[List[float]] = None,
                                num_steps: int = 15) -> Tuple[List[torch.Tensor], List[Dict[str, float]]]:
        """
        Perform reasoning toward multiple objectives simultaneously.
        
        Args:
            source_concept: Starting concept
            objectives: List of target concepts
            objective_weights: Weights for each objective
            num_steps: Number of reasoning steps
            
        Returns:
            Tuple of (reasoning_path, objective_scores_per_step)
        """
        if objective_weights is None:
            objective_weights = [1.0 / len(objectives)] * len(objectives)
        
        # Create multi-objective guidance function
        def multi_objective_guidance(z_t: torch.Tensor, t: torch.Tensor) -> torch.Tensor:
            total_guidance = torch.zeros_like(z_t)
            
            for obj, weight in zip(objectives, objective_weights):
                obj_guidance = GoalGuidanceFunction(obj, weight * self.guidance_scale)
                total_guidance += obj_guidance(z_t, t)
            
            return total_guidance
        
        reasoning_path = [source_concept.clone()]
        objective_scores = []
        current_concept = source_concept
        
        for step in range(num_steps):
            # Progressive noise application
            noise_level = (num_steps - step) / num_steps
            timestep = int(noise_level * self.conceptual_diffusion.num_timesteps * 0.4)
            
            if timestep > 0:
                timestep_tensor = torch.tensor([timestep], device=self.device)
                noise = torch.randn_like(current_concept)
                
                # Apply forward then reverse diffusion with multi-objective guidance
                noisy_concept = DiffusionMath.q_sample(
                    current_concept.unsqueeze(0), timestep_tensor, noise.unsqueeze(0),
                    self.conceptual_diffusion.sqrt_alphas_cumprod,
                    self.conceptual_diffusion.sqrt_one_minus_alphas_cumprod
                ).squeeze(0)
                
                next_concept = self.conceptual_diffusion.reverse_diffusion_step(
                    noisy_concept.unsqueeze(0), timestep_tensor,
                    guidance_fn=multi_objective_guidance
                ).squeeze(0)
            else:
                # Direct weighted combination for final steps
                weighted_objectives = torch.stack([
                    weight * obj for obj, weight in zip(objectives, objective_weights)
                ]).sum(dim=0)
                
                alpha = step / num_steps
                next_concept = (1 - alpha) * current_concept + alpha * weighted_objectives
            
            reasoning_path.append(next_concept)
            
            # Compute scores for each objective
            step_scores = {}
            for i, obj in enumerate(objectives):
                distance = torch.norm(next_concept - obj).item()
                score = 1.0 / (1.0 + distance)
                step_scores[f'objective_{i}'] = score
            
            # Weighted average score
            step_scores['weighted_average'] = sum(
                weight * step_scores[f'objective_{i}']
                for i, weight in enumerate(objective_weights)
            )
            
            objective_scores.append(step_scores)
            current_concept = next_concept
        
        return reasoning_path, objective_scores
    
    def adaptive_reasoning(self, source_concept: torch.Tensor,
                          goal_concept: torch.Tensor,
                          success_threshold: float = 0.8,
                          max_attempts: int = 5) -> Tuple[List[torch.Tensor], Dict[str, Any]]:
        """
        Adaptive reasoning that adjusts strategy based on progress.
        
        Args:
            source_concept: Starting concept
            goal_concept: Target concept
            success_threshold: Threshold for considering reasoning successful
            max_attempts: Maximum number of reasoning attempts
            
        Returns:
            Tuple of (best_reasoning_path, performance_metrics)
        """
        best_path = None
        best_score = 0.0
        attempt_results = []
        
        for attempt in range(max_attempts):
            # Adjust parameters based on previous attempts
            if attempt == 0:
                guidance_scale = self.guidance_scale
                num_steps = 10
            else:
                # Analyze previous attempts to adjust strategy
                avg_score = np.mean([result['final_score'] for result in attempt_results])
                
                if avg_score < 0.3:
                    # Poor performance, increase guidance and steps
                    guidance_scale = min(self.guidance_scale * 1.5, 10.0)
                    num_steps = min(15, 10 + attempt * 2)
                elif avg_score < 0.6:
                    # Moderate performance, slight adjustments
                    guidance_scale = self.guidance_scale * 1.2
                    num_steps = 12
                else:
                    # Good performance, fine-tune
                    guidance_scale = self.guidance_scale
                    num_steps = 10
            
            # Perform reasoning
            reasoning_path, confidences = self.goal_directed_reasoning(
                source_concepts=[source_concept],
                goal_concept=goal_concept,
                num_steps=num_steps,
                guidance_scale=guidance_scale,
                return_confidences=True
            )
            
            final_score = confidences[-1] if confidences else 0.0
            
            attempt_results.append({
                'attempt': attempt,
                'final_score': final_score,
                'guidance_scale': guidance_scale,
                'num_steps': num_steps,
                'path_length': len(reasoning_path)
            })
            
            # Update best result
            if final_score > best_score:
                best_score = final_score
                best_path = reasoning_path
            
            # Early stopping if success threshold reached
            if final_score >= success_threshold:
                break
        
        performance_metrics = {
            'best_score': best_score,
            'attempts_made': len(attempt_results),
            'success': best_score >= success_threshold,
            'attempt_details': attempt_results,
            'average_score': np.mean([r['final_score'] for r in attempt_results]),
            'score_improvement': attempt_results[-1]['final_score'] - attempt_results[0]['final_score']
        }
        
        return best_path, performance_metrics
    
    def extract_reasoning_patterns(self) -> Dict[str, Any]:
        """
        Extract patterns from reasoning history for meta-learning.
        
        Returns:
            Dictionary of extracted patterns
        """
        if len(self.reasoning_history) < 5:
            return {'insufficient_data': True}
        
        # Analyze success patterns
        successful_episodes = [
            episode for episode in self.reasoning_history
            if episode.get('success_score', 0) > 0.7
        ]
        
        patterns = {
            'total_episodes': len(self.reasoning_history),
            'successful_episodes': len(successful_episodes),
            'success_rate': len(successful_episodes) / len(self.reasoning_history),
            'average_path_length': np.mean([
                len(episode['reasoning_path']) for episode in self.reasoning_history
            ]),
            'successful_path_length': np.mean([
                len(episode['reasoning_path']) for episode in successful_episodes
            ]) if successful_episodes else 0
        }
        
        # Extract common successful patterns
        if successful_episodes:
            # Analyze concept similarities in successful episodes
            source_similarities = []
            goal_similarities = []
            
            for i, episode1 in enumerate(successful_episodes):
                for episode2 in successful_episodes[i+1:]:
                    # Compute similarities between source concepts
                    source_sim = self.thought_latent_space.compute_semantic_similarity(
                        episode1['source_concepts'][0], episode2['source_concepts'][0]
                    ).item()
                    source_similarities.append(source_sim)
                    
                    # Compute similarities between goal concepts
                    goal_sim = self.thought_latent_space.compute_semantic_similarity(
                        episode1['goal_concept'], episode2['goal_concept']
                    ).item()
                    goal_similarities.append(goal_sim)
            
            patterns['source_concept_similarity'] = np.mean(source_similarities) if source_similarities else 0
            patterns['goal_concept_similarity'] = np.mean(goal_similarities) if goal_similarities else 0
        
        return patterns

# =============================================================================
# Bayesian Uncertainty Quantification
# =============================================================================

class VariationalDistribution(nn.Module):
    """Variational distribution for Bayesian inference."""
    
    def __init__(self, input_dim: int, latent_dim: int):
        super().__init__()
        self.input_dim = input_dim
        self.latent_dim = latent_dim
        
        self.encoder = nn.Sequential(
            nn.Linear(input_dim, input_dim * 2),
            nn.SiLU(),
            nn.Linear(input_dim * 2, latent_dim * 2)  # Mean and log variance
        )
        
    def forward(self, x: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Encode input to variational parameters.
        
        Args:
            x: Input tensor
            
        Returns:
            Tuple of (mean, log_variance)
        """
        params = self.encoder(x)
        mean, log_var = params.chunk(2, dim=-1)
        return mean, log_var
    
    def sample(self, mean: torch.Tensor, log_var: torch.Tensor, 
              num_samples: int = 1) -> torch.Tensor:
        """
        Sample from the variational distribution.
        
        Args:
            mean: Mean parameters
            log_var: Log variance parameters
            num_samples: Number of samples to draw
            
        Returns:
            Samples from the distribution
        """
        std = torch.exp(0.5 * log_var)
        eps = torch.randn(num_samples, *mean.shape, device=mean.device)
        return mean.unsqueeze(0) + std.unsqueeze(0) * eps

class UncertaintyEstimator(nn.Module):
    """Neural network for estimating different types of uncertainty."""
    
    def __init__(self, input_dim: int, hidden_dim: int = None):
        super().__init__()
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim or input_dim * 2
        
        # Epistemic uncertainty estimator
        self.epistemic_estimator = nn.Sequential(
            nn.Linear(input_dim, self.hidden_dim),
            nn.LayerNorm(self.hidden_dim),
            nn.SiLU(),
            nn.Dropout(0.1),
            nn.Linear(self.hidden_dim, self.hidden_dim // 2),
            nn.SiLU(),
            nn.Linear(self.hidden_dim // 2, 1)
        )
        
        # Aleatoric uncertainty estimator
        self.aleatoric_estimator = nn.Sequential(
            nn.Linear(input_dim, self.hidden_dim),
            nn.LayerNorm(self.hidden_dim),
            nn.SiLU(),
            nn.Dropout(0.1),
            nn.Linear(self.hidden_dim, self.hidden_dim // 2),
            nn.SiLU(),
            nn.Linear(self.hidden_dim // 2, 1)
        )
        
        # Combined uncertainty estimator
        self.combined_estimator = nn.Sequential(
            nn.Linear(input_dim + 2, self.hidden_dim),  # +2 for epistemic and aleatoric
            nn.LayerNorm(self.hidden_dim),
            nn.SiLU(),
            nn.Linear(self.hidden_dim, 1)
        )
        
    def forward(self, x: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        Estimate different types of uncertainty.
        
        Args:
            x: Input concept or reasoning state
            
        Returns:
            Dictionary with uncertainty estimates
        """
        epistemic = torch.sigmoid(self.epistemic_estimator(x))
        aleatoric = torch.sigmoid(self.aleatoric_estimator(x))
        
        # Combined input for total uncertainty
        combined_input = torch.cat([x, epistemic, aleatoric], dim=-1)
        total = torch.sigmoid(self.combined_estimator(combined_input))
        
        return {
            'epistemic': epistemic,
            'aleatoric': aleatoric,
            'total': total
        }

class BayesianUncertaintyQuantification:
    """
    Bayesian framework for quantifying uncertainty in reasoning processes.
    
    This class implements multiple uncertainty estimation techniques:
    - Epistemic uncertainty (model uncertainty)
    - Aleatoric uncertainty (data uncertainty)
    - Predictive uncertainty
    - Confidence calibration
    
    Mathematical foundation:
    - Epistemic: U_ep(z) = E_θ[(z - E_θ[z])²]
    - Aleatoric: U_al(z) = E_θ[σ_θ²(x)]
    - Total: U_total = U_ep + U_al
    """
    
    def __init__(self, thought_latent_space: ThoughtLatentSpace,
                 concept_dim: int, num_mc_samples: int = 50,
                 device: str = None):
        self.thought_latent_space = thought_latent_space
        self.concept_dim = concept_dim
        self.num_mc_samples = num_mc_samples
        self.device = device or DEVICE
        
        # Variational distribution for Bayesian inference
        self.variational_dist = VariationalDistribution(
            input_dim=concept_dim,
            latent_dim=concept_dim // 2
        ).to(self.device)
        
        # Uncertainty estimator
        self.uncertainty_estimator = UncertaintyEstimator(
            input_dim=concept_dim
        ).to(self.device)
        
        # Calibration data storage
        self.calibration_data = {
            'predictions': [],
            'uncertainties': [],
            'ground_truth': []
        }
        
    def estimate_epistemic_uncertainty(self, concept: torch.Tensor,
                                     num_samples: int = None) -> torch.Tensor:
        """
        Estimate epistemic uncertainty using Monte Carlo dropout.
        
        Args:
            concept: Input concept
            num_samples: Number of MC samples
            
        Returns:
            Epistemic uncertainty estimate
        """
        num_samples = num_samples or self.num_mc_samples
        
        # Enable dropout for MC sampling
        self.thought_latent_space.train()
        
        samples = []
        for _ in range(num_samples):
            # Forward pass with dropout
            thought_vector = self.thought_latent_space.encode_concept(concept)
            reconstructed = self.thought_latent_space.decode_concept(thought_vector)
            samples.append(reconstructed)
        
        # Restore evaluation mode
        self.thought_latent_space.eval()
        
        # Compute variance across samples
        samples_tensor = torch.stack(samples)
        epistemic_uncertainty = torch.var(samples_tensor, dim=0).mean()
        
        return epistemic_uncertainty
    
    def estimate_aleatoric_uncertainty(self, concept: torch.Tensor) -> torch.Tensor:
        """
        Estimate aleatoric uncertainty using the uncertainty estimator.
        
        Args:
            concept: Input concept
            
        Returns:
            Aleatoric uncertainty estimate
        """
        uncertainty_estimates = self.uncertainty_estimator(concept.unsqueeze(0))
        return uncertainty_estimates['aleatoric'].squeeze(0)
    
    def quantify_reasoning_uncertainty(self, reasoning_path: List[torch.Tensor]) -> Dict[str, torch.Tensor]:
        """
        Quantify uncertainty along a reasoning path.
        
        Args:
            reasoning_path: List of concepts in reasoning sequence
            
        Returns:
            Dictionary with uncertainty measures
        """
        if not reasoning_path:
            return {}
        
        # Stack concepts for batch processing
        concepts = torch.stack(reasoning_path)
        
        # Estimate uncertainties for each step
        epistemic_uncertainties = []
        aleatoric_uncertainties = []
        
        for concept in concepts:
            epistemic = self.estimate_epistemic_uncertainty(concept)
            aleatoric = self.estimate_aleatoric_uncertainty(concept)
            
            epistemic_uncertainties.append(epistemic)
            aleatoric_uncertainties.append(aleatoric)
        
        epistemic_tensor = torch.stack(epistemic_uncertainties)
        aleatoric_tensor = torch.stack(aleatoric_uncertainties)
        total_uncertainty = epistemic_tensor + aleatoric_tensor
        
        # Compute path-level statistics
        uncertainty_measures = {
            'epistemic_per_step': epistemic_tensor,
            'aleatoric_per_step': aleatoric_tensor,
            'total_per_step': total_uncertainty,
            'epistemic_mean': epistemic_tensor.mean(),
            'aleatoric_mean': aleatoric_tensor.mean(),
            'total_mean': total_uncertainty.mean(),
            'epistemic_std': epistemic_tensor.std(),
            'aleatoric_std': aleatoric_tensor.std(),
            'total_std': total_uncertainty.std(),
            'uncertainty_trend': self._compute_uncertainty_trend(total_uncertainty),
            'peak_uncertainty_step': torch.argmax(total_uncertainty),
            'min_uncertainty_step': torch.argmin(total_uncertainty)
        }
        
        # Add confidence estimates
        confidence_scores = 1.0 / (1.0 + total_uncertainty)
        uncertainty_measures['confidence_per_step'] = confidence_scores
        uncertainty_measures['confidence_mean'] = confidence_scores.mean()
        
        return uncertainty_measures
    
    def _compute_uncertainty_trend(self, uncertainties: torch.Tensor) -> str:
        """Analyze the trend of uncertainty along the reasoning path."""
        if len(uncertainties) < 3:
            return "insufficient_data"
        
        # Compute differences between consecutive steps
        diffs = uncertainties[1:] - uncertainties[:-1]
        
        # Classify trend
        increasing_steps = (diffs > 0).sum().item()
        decreasing_steps = (diffs < 0).sum().item()
        
        if increasing_steps > decreasing_steps * 1.5:
            return "increasing"
        elif decreasing_steps > increasing_steps * 1.5:
            return "decreasing"
        else:
            return "stable"
    
    def compute_predictive_uncertainty(self, concept: torch.Tensor,
                                     prediction_function: Callable,
                                     num_samples: int = None) -> Dict[str, torch.Tensor]:
        """
        Compute predictive uncertainty for a given prediction function.
        
        Args:
            concept: Input concept
            prediction_function: Function that makes predictions
            num_samples: Number of MC samples
            
        Returns:
            Dictionary with predictive uncertainty measures
        """
        num_samples = num_samples or self.num_mc_samples
        
        # Generate samples from variational distribution
        mean, log_var = self.variational_dist(concept.unsqueeze(0))
        samples = self.variational_dist.sample(mean, log_var, num_samples).squeeze(1)
        
        # Compute predictions for each sample
        predictions = []
        for sample in samples:
            pred = prediction_function(sample)
            predictions.append(pred)
        
        predictions_tensor = torch.stack(predictions)
        
        # Compute uncertainty measures
        predictive_mean = predictions_tensor.mean(dim=0)
        predictive_std = predictions_tensor.std(dim=0)
        predictive_var = predictions_tensor.var(dim=0)
        
        # Entropy of prediction distribution
        if predictions_tensor.dim() > 1 and predictions_tensor.shape[-1] > 1:
            # For categorical predictions
            pred_probs = F.softmax(predictions_tensor, dim=-1).mean(dim=0)
            entropy = -torch.sum(pred_probs * torch.log(pred_probs + NUMERICAL_STABILITY_EPS))
        else:
            # For continuous predictions, use variance as uncertainty proxy
            entropy = torch.log(2 * np.pi * np.e * predictive_var).sum()
        
        return {
            'predictive_mean': predictive_mean,
            'predictive_std': predictive_std,
            'predictive_variance': predictive_var,
            'entropy': entropy,
            'samples': predictions_tensor
        }
    
    def calibrate_uncertainty(self, concepts: List[torch.Tensor],
                            ground_truth: List[torch.Tensor],
                            predictions: List[torch.Tensor]) -> Dict[str, float]:
        """
        Calibrate uncertainty estimates using ground truth data.
        
        Args:
            concepts: Input concepts
            ground_truth: Ground truth values
            predictions: Model predictions
            
        Returns:
            Calibration metrics
        """
        uncertainties = []
        errors = []
        
        for concept, gt, pred in zip(concepts, ground_truth, predictions):
            # Estimate uncertainty
            uncertainty_dict = self.uncertainty_estimator(concept.unsqueeze(0))
            total_uncertainty = uncertainty_dict['total'].item()
            uncertainties.append(total_uncertainty)
            
            # Compute prediction error
            error = torch.norm(pred - gt).item()
            errors.append(error)
        
        # Store calibration data
        self.calibration_data['predictions'].extend(predictions)
        self.calibration_data['uncertainties'].extend(uncertainties)
        self.calibration_data['ground_truth'].extend(ground_truth)
        
        # Compute calibration metrics
        uncertainties = np.array(uncertainties)
        errors = np.array(errors)
        
        # Correlation between uncertainty and error
        correlation = np.corrcoef(uncertainties, errors)[0, 1]
        
        # Calibration bins
        n_bins = 10
        bin_boundaries = np.linspace(0, 1, n_bins + 1)
        bin_lowers = bin_boundaries[:-1]
        bin_uppers = bin_boundaries[1:]
        
        calibration_error = 0.0
        for bin_lower, bin_upper in zip(bin_lowers, bin_uppers):
            # Find samples in this bin
            in_bin = (uncertainties > bin_lower) & (uncertainties <= bin_upper)
            prop_in_bin = in_bin.mean()
            
            if prop_in_bin > 0:
                # Average error in this bin
                avg_error_in_bin = errors[in_bin].mean()
                bin_center = (bin_lower + bin_upper) / 2
                calibration_error += np.abs(avg_error_in_bin - bin_center) * prop_in_bin
        
        return {
            'correlation': correlation,
            'calibration_error': calibration_error,
            'mean_uncertainty': np.mean(uncertainties),
            'mean_error': np.mean(errors),
            'uncertainty_std': np.std(uncertainties),
            'error_std': np.std(errors)
        }
    
    def uncertainty_aware_selection(self, concepts: List[torch.Tensor],
                                  selection_strategy: str = 'high_uncertainty') -> torch.Tensor:
        """
        Select concepts based on uncertainty estimates.
        
        Args:
            concepts: List of candidate concepts
            selection_strategy: Strategy for selection ('high_uncertainty', 'low_uncertainty', 'balanced')
            
        Returns:
            Selected concept
        """
        if not concepts:
            raise ValueError("No concepts provided for selection")
        
        if len(concepts) == 1:
            return concepts[0]
        
        # Estimate uncertainties for all concepts
        uncertainties = []
        for concept in concepts:
            uncertainty_dict = self.uncertainty_estimator(concept.unsqueeze(0))
            total_uncertainty = uncertainty_dict['total'].item()
            uncertainties.append(total_uncertainty)
        
        uncertainties = torch.tensor(uncertainties)
        
        # Select based on strategy
        if selection_strategy == 'high_uncertainty':
            # Select concept with highest uncertainty (for exploration)
            selected_idx = torch.argmax(uncertainties)
        elif selection_strategy == 'low_uncertainty':
            # Select concept with lowest uncertainty (for exploitation)
            selected_idx = torch.argmin(uncertainties)
        elif selection_strategy == 'balanced':
            # Select based on uncertainty-weighted sampling
            weights = F.softmax(uncertainties, dim=0)
            selected_idx = torch.multinomial(weights, 1).item()
        else:
            raise ValueError(f"Unknown selection strategy: {selection_strategy}")
        
        return concepts[selected_idx]

# =============================================================================
# Probabilistic Inference Engine
# =============================================================================

class ConceptPosterior:
    """Posterior distribution over concepts given evidence."""
    
    def __init__(self, mean: torch.Tensor, covariance: torch.Tensor,
                 precision: Optional[torch.Tensor] = None):
        self.mean = mean
        self.covariance = covariance
        self.precision = precision if precision is not None else torch.inverse(covariance)
        self.dimension = mean.shape[-1]
        
        # Precompute constants for efficiency
        self.log_det_cov = torch.logdet(covariance)
        self.normalizing_constant = -0.5 * (
            self.dimension * np.log(2 * np.pi) + self.log_det_cov
        )
    
    def log_prob(self, concepts: torch.Tensor) -> torch.Tensor:
        """Compute log probability of concepts under posterior."""
        diff = concepts - self.mean
        mahalanobis = torch.sum(diff * torch.matmul(diff, self.precision), dim=-1)
        return self.normalizing_constant - 0.5 * mahalanobis
    
    def sample(self, num_samples: int = 1) -> torch.Tensor:
        """Sample from the posterior distribution."""
        # Cholesky decomposition for sampling
        try:
            chol = torch.cholesky(self.covariance)
        except RuntimeError:
            # Fallback to eigenvalue decomposition if Cholesky fails
            eigenvals, eigenvecs = torch.symeig(self.covariance, eigenvectors=True)
            eigenvals = torch.clamp(eigenvals, min=NUMERICAL_STABILITY_EPS)
            chol = eigenvecs @ torch.diag(torch.sqrt(eigenvals))
        
        # Generate samples
        noise = torch.randn(num_samples, self.dimension, device=self.mean.device)
        samples = self.mean.unsqueeze(0) + torch.matmul(noise, chol.T)
        
        return samples.squeeze(0) if num_samples == 1 else samples
    
    def entropy(self) -> torch.Tensor:
        """Compute entropy of the posterior distribution."""
        return 0.5 * (self.dimension * (1 + np.log(2 * np.pi)) + self.log_det_cov)

class EvidenceFunction:
    """Function for computing evidence/likelihood of concepts."""
    
    def __init__(self, evidence_type: str, target_features: torch.Tensor,
                 feature_extractor: Optional[Callable] = None,
                 noise_variance: float = 1.0):
        self.evidence_type = evidence_type
        self.target_features = target_features
        self.feature_extractor = feature_extractor or (lambda x: x)
        self.noise_variance = noise_variance
    
    def __call__(self, concepts: torch.Tensor) -> torch.Tensor:
        """
        Compute evidence for given concepts.
        
        Args:
            concepts: Concept embeddings
            
        Returns:
            Log evidence/likelihood
        """
        features = self.feature_extractor(concepts)
        
        if self.evidence_type == 'gaussian':
            # Gaussian likelihood
            diff = features - self.target_features
            log_prob = -0.5 * torch.sum(diff**2, dim=-1) / self.noise_variance
            log_prob -= 0.5 * features.shape[-1] * np.log(2 * np.pi * self.noise_variance)
        elif self.evidence_type == 'cosine_similarity':
            # Cosine similarity as evidence
            similarity = F.cosine_similarity(features, self.target_features, dim=-1)
            log_prob = torch.log(torch.clamp(similarity, min=NUMERICAL_STABILITY_EPS))
        elif self.evidence_type == 'exponential_distance':
            # Exponential of negative distance
            distance = torch.norm(features - self.target_features, dim=-1)
            log_prob = -distance / self.noise_variance
        else:
            raise ValueError(f"Unknown evidence type: {self.evidence_type}")
        
        return log_prob

class ProbabilisticInferenceEngine:
    """
    Probabilistic inference engine for reasoning under uncertainty.
    
    This class implements Bayesian inference over concepts, allowing for:
    - Posterior inference given partial evidence
    - Maximum a posteriori (MAP) estimation
    - Marginal likelihood computation
    - Evidence accumulation over multiple observations
    
    Mathematical foundation:
    - Posterior: p(θ|D) ∝ p(D|θ)p(θ)
    - MAP estimate: θ* = argmax p(θ|D)
    - Marginal likelihood: p(D) = ∫ p(D|θ)p(θ)dθ
    """
    
    def __init__(self, thought_latent_space: ThoughtLatentSpace,
                 conceptual_diffusion: ConceptualDiffusion,
                 device: str = None):
        self.thought_latent_space = thought_latent_space
        self.conceptual_diffusion = conceptual_diffusion
        self.device = device or DEVICE
        
        # Prior parameters
        self.prior_mean = torch.zeros(thought_latent_space.concept_dim, device=self.device)
        self.prior_covariance = torch.eye(thought_latent_space.concept_dim, device=self.device)
        self.prior_precision = torch.eye(thought_latent_space.concept_dim, device=self.device)
        
        # Inference history
        self.inference_history = []
        self.evidence_accumulation = []
    
    def set_prior(self, mean: torch.Tensor, covariance: torch.Tensor):
        """
        Set prior distribution parameters.
        
        Args:
            mean: Prior mean
            covariance: Prior covariance matrix
        """
        self.prior_mean = mean.to(self.device)
        self.prior_covariance = covariance.to(self.device)
        self.prior_precision = torch.inverse(covariance).to(self.device)
    
    def infer_concept(self, evidence_functions: List[EvidenceFunction],
                     partial_concepts: Optional[List[torch.Tensor]] = None,
                     num_samples: int = 100, max_iterations: int = 1000,
                     convergence_threshold: float = 1e-6) -> Tuple[torch.Tensor, float]:
        """
        Infer concepts that maximize the posterior given evidence.
        
        Args:
            evidence_functions: List of evidence/likelihood functions
            partial_concepts: Optional partial concept observations
            num_samples: Number of samples for Monte Carlo estimation
            max_iterations: Maximum optimization iterations
            convergence_threshold: Convergence threshold for optimization
            
        Returns:
            Tuple of (inferred_concept, confidence_score)
        """
        # Initialize with prior mean
        concept = self.prior_mean.clone().requires_grad_(True)
        optimizer = Adam([concept], lr=0.01)
        
        best_concept = concept.clone()
        best_log_prob = float('-inf')
        
        # Optimization loop
        for iteration in range(max_iterations):
            optimizer.zero_grad()
            
            # Compute log posterior
            log_posterior = self._compute_log_posterior(
                concept.unsqueeze(0), evidence_functions, partial_concepts
            ).squeeze(0)
            
            # Maximize log posterior (minimize negative log posterior)
            loss = -log_posterior
            loss.backward()
            
            # Gradient clipping for stability
            torch.nn.utils.clip_grad_norm_([concept], MAX_GRADIENT_NORM)
            
            optimizer.step()
            
            # Track best solution
            if log_posterior.item() > best_log_prob:
                best_log_prob = log_posterior.item()
                best_concept = concept.clone().detach()
            
            # Check convergence
            if iteration > 0 and abs(log_posterior.item() - prev_log_prob) < convergence_threshold:
                break
            
            prev_log_prob = log_posterior.item()
        
        # Compute confidence as normalized log probability
        confidence = torch.exp(torch.tensor(best_log_prob)).item()
        confidence = min(confidence, 1.0)  # Clamp to reasonable range
        
        # Store inference result
        self.inference_history.append({
            'inferred_concept': best_concept.clone(),
            'log_probability': best_log_prob,
            'confidence': confidence,
            'num_iterations': iteration + 1,
            'evidence_functions': evidence_functions
        })
        
        return best_concept.detach(), confidence
    
    def _compute_log_posterior(self, concepts: torch.Tensor,
                              evidence_functions: List[EvidenceFunction],
                              partial_concepts: Optional[List[torch.Tensor]] = None) -> torch.Tensor:
        """Compute log posterior probability."""
        batch_size = concepts.shape[0]
        
        # Log prior
        diff = concepts - self.prior_mean
        log_prior = -0.5 * torch.sum(
            diff * torch.matmul(diff, self.prior_precision), dim=-1
        )
        log_prior -= 0.5 * (
            concepts.shape[-1] * np.log(2 * np.pi) + 
            torch.logdet(self.prior_covariance)
        )
        
        # Log likelihood from evidence functions
        log_likelihood = torch.zeros(batch_size, device=self.device)
        for evidence_fn in evidence_functions:
            log_likelihood += evidence_fn(concepts)
        
        # Additional likelihood from partial concepts
        if partial_concepts:
            for partial_concept in partial_concepts:
                # Compute similarity-based likelihood
                similarity = F.cosine_similarity(concepts, partial_concept.unsqueeze(0), dim=-1)
                log_likelihood += torch.log(torch.clamp(similarity, min=NUMERICAL_STABILITY_EPS))
        
        return log_prior + log_likelihood
    
    def compute_marginal_likelihood(self, evidence_functions: List[EvidenceFunction],
                                  num_samples: int = 1000) -> torch.Tensor:
        """
        Compute marginal likelihood p(D) using Monte Carlo integration.
        
        Args:
            evidence_functions: Evidence functions
            num_samples: Number of Monte Carlo samples
            
        Returns:
            Log marginal likelihood estimate
        """
        # Sample from prior
        prior_samples = MultivariateNormal(
            self.prior_mean, self.prior_covariance
        ).sample((num_samples,))
        
        # Compute likelihood for each sample
        log_likelihoods = torch.zeros(num_samples, device=self.device)
        for evidence_fn in evidence_functions:
            log_likelihoods += evidence_fn(prior_samples)
        
        # Compute log marginal likelihood using log-sum-exp trick
        max_log_likelihood = torch.max(log_likelihoods)
        log_marginal = max_log_likelihood + torch.log(
            torch.mean(torch.exp(log_likelihoods - max_log_likelihood))
        )
        
        return log_marginal
    
    def posterior_predictive_sampling(self, evidence_functions: List[EvidenceFunction],
                                    num_posterior_samples: int = 50,
                                    num_predictive_samples: int = 10) -> List[torch.Tensor]:
        """
        Generate samples from the posterior predictive distribution.
        
        Args:
            evidence_functions: Evidence functions defining the posterior
            num_posterior_samples: Number of posterior samples
            num_predictive_samples: Number of predictive samples per posterior sample
            
        Returns:
            List of predictive samples
        """
        predictive_samples = []
        
        # Sample from posterior
        for _ in range(num_posterior_samples):
            # Infer concept from evidence
            posterior_concept, _ = self.infer_concept(
                evidence_functions, num_samples=10, max_iterations=100
            )
            
            # Generate predictive samples from this posterior concept
            for _ in range(num_predictive_samples):
                # Add noise to create predictive sample
                noise = torch.randn_like(posterior_concept) * 0.1
                predictive_sample = posterior_concept + noise
                predictive_samples.append(predictive_sample)
        
        return predictive_samples
    
    def bayesian_model_comparison(self, model_evidence_functions: List[List[EvidenceFunction]],
                                model_names: Optional[List[str]] = None) -> Dict[str, float]:
        """
        Compare different models using Bayesian model selection.
        
        Args:
            model_evidence_functions: List of evidence functions for each model
            model_names: Optional names for the models
            
        Returns:
            Dictionary of model probabilities
        """
        if model_names is None:
            model_names = [f"model_{i}" for i in range(len(model_evidence_functions))]
        
        # Compute marginal likelihood for each model
        log_marginals = []
        for evidence_fns in model_evidence_functions:
            log_marginal = self.compute_marginal_likelihood(evidence_fns)
            log_marginals.append(log_marginal.item())
        
        # Convert to probabilities using softmax
        log_marginals_tensor = torch.tensor(log_marginals)
        model_probs = F.softmax(log_marginals_tensor, dim=0)
        
        return {
            name: prob.item() 
            for name, prob in zip(model_names, model_probs)
        }
    
    def sequential_inference(self, evidence_sequence: List[EvidenceFunction],
                           update_method: str = 'analytical') -> List[ConceptPosterior]:
        """
        Perform sequential Bayesian inference as evidence arrives.
        
        Args:
            evidence_sequence: Sequence of evidence functions
            update_method: Method for updating ('analytical', 'sampling')
            
        Returns:
            List of posterior distributions at each step
        """
        posteriors = []
        current_mean = self.prior_mean.clone()
        current_cov = self.prior_covariance.clone()
        
        for evidence_fn in evidence_sequence:
            if update_method == 'analytical':
                # Analytical update (assuming Gaussian)
                current_mean, current_cov = self._analytical_bayesian_update(
                    current_mean, current_cov, evidence_fn
                )
            elif update_method == 'sampling':
                # Sampling-based update
                current_mean, current_cov = self._sampling_bayesian_update(
                    current_mean, current_cov, evidence_fn
                )
            else:
                raise ValueError(f"Unknown update method: {update_method}")
            
            posterior = ConceptPosterior(current_mean, current_cov)
            posteriors.append(posterior)
            
            # Store evidence accumulation
            self.evidence_accumulation.append({
                'evidence_function': evidence_fn,
                'posterior_mean': current_mean.clone(),
                'posterior_covariance': current_cov.clone(),
                'entropy': posterior.entropy()
            })
        
        return posteriors
    
    def _analytical_bayesian_update(self, prior_mean: torch.Tensor, 
                                  prior_cov: torch.Tensor,
                                  evidence_fn: EvidenceFunction) -> Tuple[torch.Tensor, torch.Tensor]:
        """Analytical Bayesian update for Gaussian distributions."""
        # For Gaussian likelihood with known variance
        if evidence_fn.evidence_type == 'gaussian':
            observation_precision = torch.eye(
                prior_mean.shape[0], device=self.device
            ) / evidence_fn.noise_variance
            
            # Posterior precision = prior precision + observation precision
            prior_precision = torch.inverse(prior_cov)
            posterior_precision = prior_precision + observation_precision
            posterior_cov = torch.inverse(posterior_precision)
            
            # Posterior mean
            posterior_mean = torch.matmul(
                posterior_cov,
                torch.matmul(prior_precision, prior_mean) +
                torch.matmul(observation_precision, evidence_fn.target_features)
            )
            
            return posterior_mean, posterior_cov
        else:
            # Fallback to sampling for non-Gaussian cases
            return self._sampling_bayesian_update(prior_mean, prior_cov, evidence_fn)
    
    def _sampling_bayesian_update(self, prior_mean: torch.Tensor,
                                prior_cov: torch.Tensor,
                                evidence_fn: EvidenceFunction,
                                num_samples: int = 1000) -> Tuple[torch.Tensor, torch.Tensor]:
        """Sampling-based Bayesian update."""
        # Sample from prior
        prior_dist = MultivariateNormal(prior_mean, prior_cov)
        samples = prior_dist.sample((num_samples,))
        
        # Compute importance weights
        log_weights = evidence_fn(samples)
        weights = F.softmax(log_weights, dim=0)
        
        # Compute weighted posterior statistics
        posterior_mean = torch.sum(weights.unsqueeze(-1) * samples, dim=0)
        
        # Compute weighted covariance
        centered_samples = samples - posterior_mean
        posterior_cov = torch.sum(
            weights.unsqueeze(-1).unsqueeze(-1) * 
            torch.bmm(centered_samples.unsqueeze(-1), centered_samples.unsqueeze(-2)),
            dim=0
        )
        
        # Add small regularization for numerical stability
        posterior_cov += torch.eye(posterior_cov.shape[0], device=self.device) * NUMERICAL_STABILITY_EPS
        
        return posterior_mean, posterior_cov

# =============================================================================
# Main Diffusion-Based Reasoning System
# =============================================================================

class DiffusionBasedReasoning:
    """
    Main unified interface for diffusion-based reasoning capabilities.
    
    This class provides a comprehensive wrapper around all diffusion reasoning
    components, offering a clean API for:
    - Conceptual diffusion processes
    - Thought latent space operations
    - Reverse diffusion reasoning
    - Bayesian uncertainty quantification
    - Probabilistic inference
    
    The class automatically initializes and manages all subcomponents,
    providing both high-level convenience methods and direct access to
    underlying components for advanced usage.
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None, 
                 force_cpu: bool = False, distributed: bool = False):
        """
        Initialize the DiffusionBasedReasoning system.
        
        Args:
            config: Optional configuration dictionary
            force_cpu: Whether to force CPU usage
            distributed: Whether to enable distributed processing
        """
        # Configuration setup
        self.config = self._setup_configuration(config, force_cpu)
        self.distributed = distributed
        self.device = self.config['device']
        
        # System state
        self._initialized = False
        self._initialization_errors = []
        self._performance_metrics = {}
        self._reasoning_cache = {}
        
        # Core components
        self.conceptual_diffusion = None
        self.thought_latent_space = None
        self.reverse_diffusion = None
        self.bayesian_uncertainty = None
        self.probabilistic_inference = None
        
        # Initialize distributed processing if requested
        if self.distributed:
            self._setup_distributed_processing()
        
        # Initialize all components
        self._initialize_all_components()
        
        # Setup monitoring and logging
        self._setup_monitoring()
        
        logger.info(f"DiffusionBasedReasoning initialized on {self.device} "
                   f"with {len(self._initialization_errors)} errors")
    
    def _setup_configuration(self, config: Optional[Dict[str, Any]], 
                           force_cpu: bool) -> Dict[str, Any]:
        """Setup system configuration."""
        device = 'cpu' if force_cpu else ('cuda' if torch.cuda.is_available() else 'cpu')
        
        default_config = {
            'device': device,
            'concept_dim': DEFAULT_CONCEPT_DIM,
            'thought_space_dim': DEFAULT_THOUGHT_SPACE_DIM,
            'diffusion_steps': DEFAULT_DIFFUSION_STEPS,
            'hierarchical_levels': DEFAULT_HIERARCHICAL_LEVELS,
            'beta_start': DEFAULT_BETA_START,
            'beta_end': DEFAULT_BETA_END,
            'noise_schedule': 'cosine',
            'guidance_scale': 3.0,
            'num_mc_samples': 50,
            'learning_rate': DEFAULT_LEARNING_RATE,
            'batch_size': 32,
            'enable_caching': True,
            'cache_size': 1000,
            'precision': 'float32'
        }
        
        if config:
            default_config.update(config)
        
        # Create diffusion constants
        betas = DiffusionMath.create_noise_schedule(
            default_config['noise_schedule'],
            default_config['diffusion_steps'],
            default_config['beta_start'],
            default_config['beta_end']
        ).to(device)
        
        diffusion_constants = DiffusionMath.compute_diffusion_constants(betas)
        default_config.update(diffusion_constants)
        
        return default_config
    
    def _setup_distributed_processing(self):
        """Setup distributed processing if enabled."""
        try:
            if not dist.is_initialized():
                # Initialize process group for distributed training
                dist.init_process_group(backend='nccl' if torch.cuda.is_available() else 'gloo')
            
            self.rank = dist.get_rank()
            self.world_size = dist.get_world_size()
            
            if torch.cuda.is_available():
                torch.cuda.set_device(self.rank)
                self.device = f'cuda:{self.rank}'
                self.config['device'] = self.device
            
            logger.info(f"Distributed processing initialized: rank {self.rank}, world size {self.world_size}")
        except Exception as e:
            logger.warning(f"Failed to initialize distributed processing: {e}")
            self.distributed = False
    
    def _initialize_all_components(self):
        """Initialize all diffusion reasoning components."""
        try:
            # Initialize thought latent space first (core dependency)
            self._initialize_thought_latent_space()
            
            # Initialize conceptual diffusion
            self._initialize_conceptual_diffusion()
            
            # Initialize dependent components
            if self.thought_latent_space and self.conceptual_diffusion:
                self._initialize_reverse_diffusion()
                self._initialize_bayesian_uncertainty()
                self._initialize_probabilistic_inference()
            
            # Wrap with DDP if distributed
            if self.distributed:
                self._wrap_with_ddp()
            
            # Validate initialization
            self._validate_initialization()
            
            self._initialized = True
            logger.info("All diffusion reasoning components initialized successfully")
            
        except Exception as e:
            self._initialization_errors.append(f"Component initialization failed: {e}")
            logger.error(f"Failed to initialize components: {e}")
    
    def _initialize_thought_latent_space(self):
        """Initialize the thought latent space component."""
        try:
            self.thought_latent_space = ThoughtLatentSpace(
                concept_dim=self.config['concept_dim'],
                thought_space_dim=self.config['thought_space_dim'],
                num_hierarchical_levels=self.config['hierarchical_levels'],
                device=self.device
            ).to(self.device)
            
            logger.debug("ThoughtLatentSpace initialized")
        except Exception as e:
            self._initialization_errors.append(f"ThoughtLatentSpace: {e}")
            self.thought_latent_space = None
    
    def _initialize_conceptual_diffusion(self):
        """Initialize the conceptual diffusion component."""
        try:
            # Create noise predictor
            noise_predictor = ConceptNoisePredictor(
                concept_dim=self.config['concept_dim'],
                hidden_dim=self.config['concept_dim'] * 2,
                num_layers=6,
                num_heads=8,
                dropout=0.1
            ).to(self.device)
            
            # Create conceptual diffusion model
            self.conceptual_diffusion = ConceptualDiffusion(
                noise_predictor=noise_predictor,
                concept_dim=self.config['concept_dim'],
                num_timesteps=self.config['diffusion_steps'],
                noise_schedule=self.config['noise_schedule'],
                beta_start=self.config['beta_start'],
                beta_end=self.config['beta_end'],
                device=self.device
            ).to(self.device)
            
            logger.debug("ConceptualDiffusion initialized")
        except Exception as e:
            self._initialization_errors.append(f"ConceptualDiffusion: {e}")
            self.conceptual_diffusion = None
    
    def _initialize_reverse_diffusion(self):
        """Initialize reverse diffusion reasoning component."""
        try:
            self.reverse_diffusion = ReverseDiffusionReasoning(
                conceptual_diffusion=self.conceptual_diffusion,
                thought_latent_space=self.thought_latent_space,
                guidance_scale=self.config['guidance_scale'],
                device=self.device
            )
            
            logger.debug("ReverseDiffusionReasoning initialized")
        except Exception as e:
            self._initialization_errors.append(f"ReverseDiffusionReasoning: {e}")
            self.reverse_diffusion = None
    
    def _initialize_bayesian_uncertainty(self):
        """Initialize Bayesian uncertainty quantification component."""
        try:
            self.bayesian_uncertainty = BayesianUncertaintyQuantification(
                thought_latent_space=self.thought_latent_space,
                concept_dim=self.config['concept_dim'],
                num_mc_samples=self.config['num_mc_samples'],
                device=self.device
            )
            
            logger.debug("BayesianUncertaintyQuantification initialized")
        except Exception as e:
            self._initialization_errors.append(f"BayesianUncertaintyQuantification: {e}")
            self.bayesian_uncertainty = None
    
    def _initialize_probabilistic_inference(self):
        """Initialize probabilistic inference engine component."""
        try:
            self.probabilistic_inference = ProbabilisticInferenceEngine(
                thought_latent_space=self.thought_latent_space,
                conceptual_diffusion=self.conceptual_diffusion,
                device=self.device
            )
            
            logger.debug("ProbabilisticInferenceEngine initialized")
        except Exception as e:
            self._initialization_errors.append(f"ProbabilisticInferenceEngine: {e}")
            self.probabilistic_inference = None
    
    def _wrap_with_ddp(self):
        """Wrap models with DistributedDataParallel."""
        if self.conceptual_diffusion:
            self.conceptual_diffusion = DDP(self.conceptual_diffusion, device_ids=[self.rank])
        if self.thought_latent_space:
            self.thought_latent_space = DDP(self.thought_latent_space, device_ids=[self.rank])
    
    def _validate_initialization(self):
        """Validate that components are properly initialized."""
        required_components = [
            ('conceptual_diffusion', self.conceptual_diffusion),
            ('thought_latent_space', self.thought_latent_space)
        ]
        
        for name, component in required_components:
            if component is None:
                raise RuntimeError(f"Required component {name} failed to initialize")
    
    def _setup_monitoring(self):
        """Setup performance monitoring and metrics collection."""
        self._performance_metrics = {
            'reasoning_calls': 0,
            'successful_reasoning': 0,
            'average_reasoning_time': 0.0,
            'cache_hits': 0,
            'cache_misses': 0,
            'uncertainty_estimates': [],
            'confidence_scores': []
        }
        
        self._start_time = time.time()
    
    # =============================================================================
    # High-Level Reasoning Methods
    # =============================================================================
    
    def reason_to_goal(self, source_concepts: Union[List[torch.Tensor], torch.Tensor],
                      goal_concept: torch.Tensor, num_steps: int = 10,
                      guidance_scale: Optional[float] = None,
                      use_cache: bool = True,
                      return_intermediate: bool = False) -> Union[torch.Tensor, Tuple[List[torch.Tensor], List[float]]]:
        """
        Perform goal-directed reasoning from source concepts to a goal concept.
        
        Args:
            source_concepts: Source concept embeddings
            goal_concept: Target goal concept embedding
            num_steps: Number of reasoning steps
            guidance_scale: Guidance strength (uses default if None)
            use_cache: Whether to use reasoning cache
            return_intermediate: Whether to return intermediate steps
            
        Returns:
            Final reasoned concept or tuple of (reasoning_path, confidence_scores)
        """
        start_time = time.time()
        self._performance_metrics['reasoning_calls'] += 1
        
        # Input validation
        if isinstance(source_concepts, torch.Tensor):
            source_concepts = [source_concepts]
        
        if not source_concepts:
            raise ValueError("No source concepts provided")
        
        # Check cache
        cache_key = self._generate_cache_key(source_concepts, goal_concept, num_steps)
        if use_cache and self.config['enable_caching'] and cache_key in self._reasoning_cache:
            self._performance_metrics['cache_hits'] += 1
            cached_result = self._reasoning_cache[cache_key]
            if return_intermediate:
                return cached_result['reasoning_path'], cached_result['confidence_scores']
            else:
                return cached_result['final_concept']
        else:
            self._performance_metrics['cache_misses'] += 1
        
        try:
            if self.reverse_diffusion is None:
                raise RuntimeError("Reverse diffusion reasoning not initialized")
            
            # Perform reasoning
            reasoning_path, confidence_scores = self.reverse_diffusion.goal_directed_reasoning(
                source_concepts=source_concepts,
                goal_concept=goal_concept,
                num_steps=num_steps,
                guidance_scale=guidance_scale,
                return_confidences=True
            )
            
            final_concept = reasoning_path[-1] if reasoning_path else None
            final_confidence = confidence_scores[-1] if confidence_scores else 0.0
            
            # Update metrics
            self._performance_metrics['successful_reasoning'] += 1
            self._performance_metrics['confidence_scores'].append(final_confidence)
            
            reasoning_time = time.time() - start_time
            self._update_average_time(reasoning_time)
            
            # Cache result
            if use_cache and self.config['enable_caching']:
                self._cache_reasoning_result(cache_key, {
                    'reasoning_path': reasoning_path,
                    'confidence_scores': confidence_scores,
                    'final_concept': final_concept
                })
            
            if return_intermediate:
                return reasoning_path, confidence_scores
            else:
                return final_concept
                
        except Exception as e:
            logger.error(f"Error in goal-directed reasoning: {e}")
            self._performance_metrics['reasoning_calls'] -= 1  # Don't count failed attempts
            raise
    
    def explore_concept_space(self, concept: torch.Tensor, exploration_radius: float = 0.3,
                            num_samples: int = 5, exploration_strategy: str = 'gaussian',
                            return_similarities: bool = False) -> Union[List[torch.Tensor], Tuple[List[torch.Tensor], List[float]]]:
        """
        Explore the conceptual neighborhood around a given concept.
        
        Args:
            concept: Central concept for exploration
            exploration_radius: Radius of exploration
            num_samples: Number of samples to generate
            exploration_strategy: Strategy for exploration ('gaussian', 'uniform', 'diffusion')
            return_similarities: Whether to return similarity scores
            
        Returns:
            List of neighboring concepts, optionally with similarity scores
        """
        if self.thought_latent_space is None:
            raise RuntimeError("Thought latent space not initialized")
        
        try:
            if exploration_strategy == 'diffusion':
                # Use diffusion process for exploration
                neighbors = self._diffusion_based_exploration(concept, num_samples, exploration_radius)
            else:
                # Use thought latent space exploration
                neighbors = self.thought_latent_space.explore_concept_neighborhood(
                    concept=concept,
                    num_samples=num_samples,
                    noise_level=exploration_radius,
                    exploration_type=exploration_strategy
                )
            
            if return_similarities:
                similarities = []
                for neighbor in neighbors:
                    sim = self.thought_latent_space.compute_semantic_similarity(
                        concept, neighbor, metric='cosine'
                    ).item()
                    similarities.append(sim)
                return neighbors, similarities
            else:
                return neighbors
                
        except Exception as e:
            logger.error(f"Error in concept space exploration: {e}")
            raise
    
    def _diffusion_based_exploration(self, concept: torch.Tensor, 
                                   num_samples: int, radius: float) -> List[torch.Tensor]:
        """Explore using the diffusion process."""
        neighbors = []
        
        for _ in range(num_samples):
            # Add controlled noise and reverse diffuse
            noise_level = radius * self.config['diffusion_steps']
            timestep = min(int(noise_level), self.config['diffusion_steps'] - 1)
            
            if timestep > 0:
                t_tensor = torch.tensor([timestep], device=self.device)
                noise = torch.randn_like(concept)
                
                # Forward diffusion
                noisy_concept = DiffusionMath.q_sample(
                    concept.unsqueeze(0), t_tensor, noise.unsqueeze(0),
                    self.conceptual_diffusion.sqrt_alphas_cumprod,
                    self.conceptual_diffusion.sqrt_one_minus_alphas_cumprod
                ).squeeze(0)
                
                # Reverse diffusion
                neighbor = self.conceptual_diffusion.reverse_diffusion_step(
                    noisy_concept.unsqueeze(0), t_tensor
                ).squeeze(0)
            else:
                # Direct noise addition for small radius
                neighbor = concept + torch.randn_like(concept) * radius
            
            neighbors.append(neighbor)
        
        return neighbors
    
    def quantify_uncertainty(self, reasoning_path: List[torch.Tensor],
                           uncertainty_types: List[str] = None,
                           return_detailed: bool = False) -> Dict[str, torch.Tensor]:
        """
        Quantify uncertainty in a reasoning path.
        
        Args:
            reasoning_path: List of concepts in reasoning sequence
            uncertainty_types: Types of uncertainty to compute
            return_detailed: Whether to return detailed analysis
            
        Returns:
            Dictionary with uncertainty measures
        """
        if self.bayesian_uncertainty is None:
            raise RuntimeError("Bayesian uncertainty quantification not initialized")
        
        if not reasoning_path:
            return {}
        
        uncertainty_types = uncertainty_types or ['epistemic', 'aleatoric', 'total']
        
        try:
            # Basic uncertainty quantification
            uncertainty_measures = self.bayesian_uncertainty.quantify_reasoning_uncertainty(
                reasoning_path
            )
            
            # Filter requested types
            filtered_measures = {}
            for key, value in uncertainty_measures.items():
                if any(utype in key for utype in uncertainty_types):
                    filtered_measures[key] = value
            
            # Add detailed analysis if requested
            if return_detailed:
                detailed_analysis = self._detailed_uncertainty_analysis(reasoning_path)
                filtered_measures.update(detailed_analysis)
            
            # Update metrics
            if 'total_mean' in filtered_measures:
                self._performance_metrics['uncertainty_estimates'].append(
                    filtered_measures['total_mean'].item()
                )
            
            return filtered_measures
            
        except Exception as e:
            logger.error(f"Error in uncertainty quantification: {e}")
            raise
    
    def _detailed_uncertainty_analysis(self, reasoning_path: List[torch.Tensor]) -> Dict[str, Any]:
        """Perform detailed uncertainty analysis."""
        analysis = {}
        
        # Path coherence analysis
        if len(reasoning_path) > 1:
            coherence_scores = []
            for i in range(len(reasoning_path) - 1):
                coherence = F.cosine_similarity(
                    reasoning_path[i], reasoning_path[i + 1], dim=0
                ).item()
                coherence_scores.append(coherence)
            
            analysis['path_coherence'] = torch.tensor(coherence_scores)
            analysis['coherence_mean'] = torch.tensor(coherence_scores).mean()
            analysis['coherence_std'] = torch.tensor(coherence_scores).std()
        
        # Concept stability analysis
        if len(reasoning_path) > 2:
            stability_scores = []
            for i in range(1, len(reasoning_path) - 1):
                # Measure how much the concept changed from previous step
                change_magnitude = torch.norm(reasoning_path[i] - reasoning_path[i-1]).item()
                stability_scores.append(1.0 / (1.0 + change_magnitude))  # Higher = more stable
            
            analysis['concept_stability'] = torch.tensor(stability_scores)
            analysis['stability_mean'] = torch.tensor(stability_scores).mean()
        
        return analysis
    
    def constrained_inference(self, constraints: List[Callable],
                            num_samples: int = 100, max_iterations: int = 500,
                            optimization_method: str = 'gradient_descent') -> Tuple[torch.Tensor, float]:
        """
        Perform inference with explicit constraints.
        
        Args:
            constraints: List of constraint functions
            num_samples: Number of samples for optimization
            max_iterations: Maximum optimization iterations
            optimization_method: Optimization method to use
            
        Returns:
            Tuple of (inferred_concept, confidence)
        """
        if self.probabilistic_inference is None:
            raise RuntimeError("Probabilistic inference engine not initialized")
        
        try:
            # Convert constraints to evidence functions
            evidence_functions = []
            for constraint in constraints:
                evidence_fn = EvidenceFunction(
                    evidence_type='exponential_distance',
                    target_features=torch.zeros(self.config['concept_dim'], device=self.device),
                    feature_extractor=constraint,
                    noise_variance=1.0
                )
                evidence_functions.append(evidence_fn)
            
            # Perform inference
            inferred_concept, confidence = self.probabilistic_inference.infer_concept(
                evidence_functions=evidence_functions,
                num_samples=num_samples,
                max_iterations=max_iterations
            )
            
            return inferred_concept, confidence
            
        except Exception as e:
            logger.error(f"Error in constrained inference: {e}")
            raise
    
    # =============================================================================
    # Advanced Reasoning Methods
    # =============================================================================
    
    def multi_objective_reasoning(self, source_concept: torch.Tensor,
                                objectives: List[torch.Tensor],
                                objective_weights: Optional[List[float]] = None,
                                strategy: str = 'weighted_guidance') -> Tuple[torch.Tensor, Dict[str, float]]:
        """
        Perform reasoning toward multiple objectives simultaneously.
        
        Args:
            source_concept: Starting concept
            objectives: List of target concepts
            objective_weights: Weights for each objective
            strategy: Multi-objective strategy to use
            
        Returns:
            Tuple of (final_concept, objective_scores)
        """
        if self.reverse_diffusion is None:
            raise RuntimeError("Reverse diffusion reasoning not initialized")
        
        try:
            if strategy == 'weighted_guidance':
                reasoning_path, objective_scores = self.reverse_diffusion.multi_objective_reasoning(
                    source_concept=source_concept,
                    objectives=objectives,
                    objective_weights=objective_weights
                )
                
                final_concept = reasoning_path[-1] if reasoning_path else source_concept
                final_scores = objective_scores[-1] if objective_scores else {}
                
                return final_concept, final_scores
            
            elif strategy == 'pareto_optimization':
                return self._pareto_multi_objective_reasoning(
                    source_concept, objectives, objective_weights
                )
            else:
                raise ValueError(f"Unknown multi-objective strategy: {strategy}")
                
        except Exception as e:
            logger.error(f"Error in multi-objective reasoning: {e}")
            raise
    
    def _pareto_multi_objective_reasoning(self, source_concept: torch.Tensor,
                                        objectives: List[torch.Tensor],
                                        objective_weights: Optional[List[float]]) -> Tuple[torch.Tensor, Dict[str, float]]:
        """Pareto optimization for multi-objective reasoning."""
        # Generate multiple reasoning paths with different emphasis
        pareto_solutions = []
        
        num_objectives = len(objectives)
        
        # Generate weight vectors for different trade-offs
        for i in range(num_objectives):
            # Create weight vector that emphasizes objective i
            weights = [0.1] * num_objectives
            weights[i] = 0.8
            
            reasoning_path, _ = self.reverse_diffusion.multi_objective_reasoning(
                source_concept=source_concept,
                objectives=objectives,
                objective_weights=weights
            )
            
            final_concept = reasoning_path[-1] if reasoning_path else source_concept
            
            # Compute scores for all objectives
            scores = {}
            for j, obj in enumerate(objectives):
                distance = torch.norm(final_concept - obj).item()
                scores[f'objective_{j}'] = 1.0 / (1.0 + distance)
            
            pareto_solutions.append((final_concept, scores))
        
        # Select best solution based on dominated criteria
        best_solution = max(pareto_solutions, key=lambda x: sum(x[1].values()))
        
        return best_solution[0], best_solution[1]
    
    def adaptive_reasoning(self, source_concept: torch.Tensor, goal_concept: torch.Tensor,
                          success_threshold: float = 0.8, max_attempts: int = 5,
                          adaptation_strategy: str = 'performance_based') -> Tuple[torch.Tensor, Dict[str, Any]]:
        """
        Adaptive reasoning that adjusts strategy based on performance.
        
        Args:
            source_concept: Starting concept
            goal_concept: Target concept
            success_threshold: Success threshold
            max_attempts: Maximum reasoning attempts
            adaptation_strategy: Strategy for adaptation
            
        Returns:
            Tuple of (best_result, performance_metrics)
        """
        if self.reverse_diffusion is None:
            raise RuntimeError("Reverse diffusion reasoning not initialized")
        
        try:
            if adaptation_strategy == 'performance_based':
                return self.reverse_diffusion.adaptive_reasoning(
                    source_concept=source_concept,
                    goal_concept=goal_concept,
                    success_threshold=success_threshold,
                    max_attempts=max_attempts
                )
            elif adaptation_strategy == 'uncertainty_guided':
                return self._uncertainty_guided_adaptive_reasoning(
                    source_concept, goal_concept, success_threshold, max_attempts
                )
            else:
                raise ValueError(f"Unknown adaptation strategy: {adaptation_strategy}")
                
        except Exception as e:
            logger.error(f"Error in adaptive reasoning: {e}")
            raise
    
    def _uncertainty_guided_adaptive_reasoning(self, source_concept: torch.Tensor,
                                             goal_concept: torch.Tensor,
                                             success_threshold: float,
                                             max_attempts: int) -> Tuple[torch.Tensor, Dict[str, Any]]:
        """Adaptive reasoning guided by uncertainty estimates."""
        best_path = None
        best_score = 0.0
        attempt_results = []
        
        for attempt in range(max_attempts):
            # Perform reasoning
            reasoning_path, confidence_scores = self.reason_to_goal(
                source_concepts=[source_concept],
                goal_concept=goal_concept,
                return_intermediate=True
            )
            
            # Quantify uncertainty
            uncertainty_measures = self.quantify_uncertainty(reasoning_path)
            
            # Compute adaptive score combining confidence and uncertainty
            final_confidence = confidence_scores[-1] if confidence_scores else 0.0
            avg_uncertainty = uncertainty_measures.get('total_mean', torch.tensor(1.0)).item()
            
            # Score that balances confidence and low uncertainty
            adaptive_score = final_confidence * (1.0 - avg_uncertainty)
            
            attempt_results.append({
                'attempt': attempt,
                'confidence': final_confidence,
                'uncertainty': avg_uncertainty,
                'adaptive_score': adaptive_score,
                'path_length': len(reasoning_path)
            })
            
            # Update best result
            if adaptive_score > best_score:
                best_score = adaptive_score
                best_path = reasoning_path
            
            # Early stopping
            if adaptive_score >= success_threshold:
                break
        
        performance_metrics = {
            'best_adaptive_score': best_score,
            'attempts_made': len(attempt_results),
            'success': best_score >= success_threshold,
            'attempt_details': attempt_results
        }
        
        final_concept = best_path[-1] if best_path else source_concept
        return final_concept, performance_metrics
    
    # =============================================================================
    # Utility and Management Methods
    # =============================================================================
    
    def _generate_cache_key(self, source_concepts: List[torch.Tensor],
                          goal_concept: torch.Tensor, num_steps: int) -> str:
        """Generate cache key for reasoning results."""
        # Create a hash of the input tensors and parameters
        import hashlib
        
        hash_input = ""
        for concept in source_concepts:
            hash_input += str(concept.flatten().tolist())
        hash_input += str(goal_concept.flatten().tolist())
        hash_input += str(num_steps)
        
        return hashlib.md5(hash_input.encode()).hexdigest()
    
    def _cache_reasoning_result(self, cache_key: str, result: Dict[str, Any]):
        """Cache reasoning result with size management."""
        if len(self._reasoning_cache) >= self.config['cache_size']:
            # Remove oldest entry (FIFO)
            oldest_key = next(iter(self._reasoning_cache))
            del self._reasoning_cache[oldest_key]
        
        self._reasoning_cache[cache_key] = result
    
    def _update_average_time(self, new_time: float):
        """Update average reasoning time with exponential moving average."""
        alpha = 0.1  # Smoothing factor
        current_avg = self._performance_metrics['average_reasoning_time']
        self._performance_metrics['average_reasoning_time'] = (
            alpha * new_time + (1 - alpha) * current_avg
        )
    
    def clear_cache(self):
        """Clear the reasoning cache."""
        self._reasoning_cache.clear()
        logger.info("Reasoning cache cleared")
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get comprehensive performance metrics."""
        uptime = time.time() - self._start_time
        
        metrics = self._performance_metrics.copy()
        metrics.update({
            'uptime_seconds': uptime,
            'cache_size': len(self._reasoning_cache),
            'cache_hit_rate': (
                metrics['cache_hits'] / max(metrics['cache_hits'] + metrics['cache_misses'], 1)
            ),
            'success_rate': (
                metrics['successful_reasoning'] / max(metrics['reasoning_calls'], 1)
            ),
            'initialized': self._initialized,
            'initialization_errors': self._initialization_errors,
            'device': self.device,
            'distributed': self.distributed
        })
        
        return metrics
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system status."""
        component_status = {
            'conceptual_diffusion': self.conceptual_diffusion is not None,
            'thought_latent_space': self.thought_latent_space is not None,
            'reverse_diffusion': self.reverse_diffusion is not None,
            'bayesian_uncertainty': self.bayesian_uncertainty is not None,
            'probabilistic_inference': self.probabilistic_inference is not None
        }
        
        return {
            'initialized': self._initialized,
            'component_status': component_status,
            'available_components': sum(component_status.values()),
            'total_components': len(component_status),
            'device': self.device,
            'distributed': self.distributed,
            'configuration': {
                'concept_dim': self.config['concept_dim'],
                'thought_space_dim': self.config['thought_space_dim'],
                'diffusion_steps': self.config['diffusion_steps'],
                'hierarchical_levels': self.config['hierarchical_levels']
            },
            'performance_summary': {
                'reasoning_calls': self._performance_metrics.get('reasoning_calls', 0),
                'cache_hit_rate': (
                    self._performance_metrics.get('cache_hits', 0) / 
                    max(self._performance_metrics.get('cache_hits', 0) + 
                        self._performance_metrics.get('cache_misses', 0), 1)
                ),
                'average_reasoning_time': self._performance_metrics.get('average_reasoning_time', 0.0)
            }
        }
    
    def save_state(self, filepath: str):
        """Save system state to file."""
        state = {
            'config': self.config,
            'performance_metrics': self._performance_metrics,
            'model_states': {}
        }
        
        # Save model states
        if self.conceptual_diffusion:
            model = self.conceptual_diffusion.module if self.distributed else self.conceptual_diffusion
            state['model_states']['conceptual_diffusion'] = model.state_dict()
        
        if self.thought_latent_space:
            model = self.thought_latent_space.module if self.distributed else self.thought_latent_space
            state['model_states']['thought_latent_space'] = model.state_dict()
        
        torch.save(state, filepath)
        logger.info(f"System state saved to {filepath}")
    
    def load_state(self, filepath: str):
        """Load system state from file."""
        state = torch.load(filepath, map_location=self.device)
        
        # Load configuration
        self.config.update(state.get('config', {}))
        
        # Load performance metrics
        self._performance_metrics.update(state.get('performance_metrics', {}))
        
        # Load model states
        model_states = state.get('model_states', {})
        
        if 'conceptual_diffusion' in model_states and self.conceptual_diffusion:
            model = self.conceptual_diffusion.module if self.distributed else self.conceptual_diffusion
            model.load_state_dict(model_states['conceptual_diffusion'])
        
        if 'thought_latent_space' in model_states and self.thought_latent_space:
            model = self.thought_latent_space.module if self.distributed else self.thought_latent_space
            model.load_state_dict(model_states['thought_latent_space'])
        
        logger.info(f"System state loaded from {filepath}")
    
    def __str__(self) -> str:
        """String representation of the system."""
        status = self.get_system_status()
        return (f"DiffusionBasedReasoning("
               f"device={status['device']}, "
               f"components={status['available_components']}/{status['total_components']}, "
               f"initialized={status['initialized']})")
    
    def __repr__(self) -> str:
        """Detailed representation of the system."""
        return self.__str__()

# =============================================================================
# Factory Functions and Utilities
# =============================================================================

def create_diffusion_reasoning_system(
    concept_dim: int = DEFAULT_CONCEPT_DIM,
    thought_space_dim: int = DEFAULT_THOUGHT_SPACE_DIM,
    diffusion_steps: int = DEFAULT_DIFFUSION_STEPS,
    device: str = None,
    distributed: bool = False,
    **kwargs
) -> DiffusionBasedReasoning:
    """
    Factory function to create a complete diffusion reasoning system.
    
    Args:
        concept_dim: Dimensionality of concept embeddings
        thought_space_dim: Dimensionality of thought latent space
        diffusion_steps: Number of diffusion timesteps
        device: Device to use ('cpu', 'cuda', or specific GPU)
        distributed: Whether to enable distributed processing
        **kwargs: Additional configuration parameters
        
    Returns:
        Configured DiffusionBasedReasoning system
    """
    config = {
        'concept_dim': concept_dim,
        'thought_space_dim': thought_space_dim,
        'diffusion_steps': diffusion_steps,
        'device': device or DEVICE,
        **kwargs
    }
    
    return DiffusionBasedReasoning(config=config, distributed=distributed)

def validate_reasoning_system(system: DiffusionBasedReasoning) -> Dict[str, bool]:
    """
    Validate that a reasoning system is properly configured and functional.
    
    Args:
        system: DiffusionBasedReasoning system to validate
        
    Returns:
        Dictionary of validation results
    """
    validation_results = {}
    
    try:
        # Test basic functionality
        test_concept = torch.randn(system.config['concept_dim'], device=system.device)
        
        # Test concept space exploration
        try:
            neighbors = system.explore_concept_space(test_concept, num_samples=2)
            validation_results['concept_exploration'] = len(neighbors) == 2
        except Exception:
            validation_results['concept_exploration'] = False
        
        # Test goal-directed reasoning
        try:
            goal_concept = torch.randn(system.config['concept_dim'], device=system.device)
            result = system.reason_to_goal([test_concept], goal_concept, num_steps=3)
            validation_results['goal_reasoning'] = result is not None
        except Exception:
            validation_results['goal_reasoning'] = False
        
        # Test uncertainty quantification
        try:
            reasoning_path = [test_concept, test_concept + 0.1]
            uncertainty = system.quantify_uncertainty(reasoning_path)
            validation_results['uncertainty_quantification'] = len(uncertainty) > 0
        except Exception:
            validation_results['uncertainty_quantification'] = False
        
        # Test system status
        status = system.get_system_status()
        validation_results['system_status'] = status['initialized']
        
        # Overall validation
        validation_results['overall'] = all(validation_results.values())
        
    except Exception as e:
        logger.error(f"Validation failed: {e}")
        validation_results['overall'] = False
    
    return validation_results

def get_reasoning_system_info() -> Dict[str, Any]:
    """
    Get information about the diffusion reasoning system capabilities.
    
    Returns:
        Dictionary with system information
    """
    return {
        'version': __version__,
        'author': __author__,
        'license': __license__,
        'components': [
            'ConceptualDiffusion',
            'ThoughtLatentSpace', 
            'ReverseDiffusionReasoning',
            'BayesianUncertaintyQuantification',
            'ProbabilisticInferenceEngine'
        ],
        'capabilities': [
            'Goal-directed reasoning',
            'Concept space exploration',
            'Multi-objective reasoning',
            'Uncertainty quantification',
            'Constrained inference',
            'Adaptive reasoning',
            'Probabilistic inference'
        ],
        'supported_devices': ['cpu', 'cuda'],
        'distributed_support': True,
        'default_config': {
            'concept_dim': DEFAULT_CONCEPT_DIM,
            'thought_space_dim': DEFAULT_THOUGHT_SPACE_DIM,
            'diffusion_steps': DEFAULT_DIFFUSION_STEPS,
            'hierarchical_levels': DEFAULT_HIERARCHICAL_LEVELS
        }
    }

# =============================================================================
# Module Exports
# =============================================================================

__all__ = [
    # Main system class
    'DiffusionBasedReasoning',
    
    # Core components
    'ConceptualDiffusion',
    'ConceptNoisePredictor',
    'ThoughtLatentSpace',
    'ThoughtVector',
    'ReverseDiffusionReasoning',
    'BayesianUncertaintyQuantification',
    'ProbabilisticInferenceEngine',
    
    # Neural network components
    'SinusoidalPositionalEncoding',
    'ResidualBlock',
    'AttentionBlock',
    'HierarchicalEncoder',
    'HierarchicalDecoder',
    'UncertaintyEstimator',
    'VariationalDistribution',
    
    # Utility classes
    'GoalGuidanceFunction',
    'ConstraintFunction',
    'ConceptPosterior',
    'EvidenceFunction',
    'DiffusionMath',
    
    # Factory functions
    'create_diffusion_reasoning_system',
    'validate_reasoning_system',
    'get_reasoning_system_info',
    
    # Constants
    'DEFAULT_DIFFUSION_STEPS',
    'DEFAULT_CONCEPT_DIM',
    'DEFAULT_THOUGHT_SPACE_DIM',
    'DEFAULT_HIERARCHICAL_LEVELS',
    'DEFAULT_BETA_START',
    'DEFAULT_BETA_END',
    'NUMERICAL_STABILITY_EPS',
    
    # Version info
    '__version__',
    '__author__',
    '__license__'
]

# Initialize logging
logger.info(f"ULTRA Diffusion-Based Reasoning module loaded (v{__version__})")
logger.info(f"Available components: {len(__all__)} classes and functions")
logger.info(f"Default device: {DEVICE}")

# Validate imports and components
try:
    # Test basic functionality
    _test_config = {
        'concept_dim': 64,
        'thought_space_dim': 128,
        'diffusion_steps': 100
    }
    _test_system = DiffusionBasedReasoning(config=_test_config, force_cpu=True)
    
    if _test_system._initialized:
        logger.info("Diffusion reasoning system validation: PASSED")
    else:
        logger.warning("Diffusion reasoning system validation: FAILED - Some components not initialized")
        
except Exception as e:
    logger.error(f"Diffusion reasoning system validation: FAILED - {e}")

logger.info("ULTRA Diffusion-Based Reasoning module initialization complete")    