# ULTRA Conceptual Diffusion Makefile
# Handles both CUDA and CPU compilation automatically

# Compiler settings
CXX = g++
NVCC = nvcc
CXXFLAGS = -O3 -fopenmp -std=c++11 -fPIC
NVCCFLAGS = -O3 -std=c++11 --expt-relaxed-constexpr -Xcompiler -fPIC
LIBS = -lm
CUDA_LIBS = -lcublas -lcusparse -lcufft -lcurand

# Source files
SOURCE = conceptual_diffusion.cu
TARGET = conceptual_diffusion
LIB_TARGET = libconceptual_diffusion.so
TEST_TARGET = test_diffusion

# Detect CUDA availability
CUDA_AVAILABLE := $(shell which nvcc >/dev/null 2>&1 && echo 1 || echo 0)

# Set compiler and flags based on CUDA availability
ifeq ($(CUDA_AVAILABLE),1)
    COMPILER = $(NVCC)
    FLAGS = $(NVCCFLAGS)
    LIBRARIES = $(LIBS) $(CUDA_LIBS)
    BUILD_TYPE = CUDA
else
    COMPILER = $(CXX)
    FLAGS = $(CXXFLAGS)
    LIBRARIES = $(LIBS)
    BUILD_TYPE = CPU
endif

# Default target
all: info $(TARGET)

# Display build information
info:
	@echo "=============================================="
	@echo "ULTRA Conceptual Diffusion Build System"
	@echo "=============================================="
	@echo "Build Type: $(BUILD_TYPE)"
	@echo "Compiler: $(COMPILER)"
	@echo "Flags: $(FLAGS)"
	@echo "Libraries: $(LIBRARIES)"
	@echo "=============================================="

# Build executable
$(TARGET): $(SOURCE)
	@echo "🔨 Building executable..."
	$(COMPILER) $(FLAGS) $< -o $@ $(LIBRARIES)
	@echo "✅ Built executable: $(TARGET)"

# Build shared library for Python integration
library: $(LIB_TARGET)

$(LIB_TARGET): $(SOURCE)
	@echo "🔨 Building shared library..."
	$(COMPILER) $(FLAGS) -shared $< -o $@ $(LIBRARIES)
	@echo "✅ Built library: $(LIB_TARGET)"

# Build test executable
test: $(TEST_TARGET)

$(TEST_TARGET): $(SOURCE)
	@echo "🔨 Building test executable..."
	$(COMPILER) $(FLAGS) -DTEST_CONCEPTUAL_DIFFUSION $< -o $@ $(LIBRARIES)
	@echo "✅ Built test: $(TEST_TARGET)"

# Run tests
run-test: $(TEST_TARGET)
	@echo "🧪 Running tests..."
	./$(TEST_TARGET)

# Python integration
python: $(LIB_TARGET)
	@echo "🐍 Setting up Python integration..."
	@if [ -f "conceptual_diffusion_wrapper.py" ]; then \
		echo "✅ Python wrapper ready"; \
		echo "Run: python3 conceptual_diffusion_wrapper.py"; \
	else \
		echo "❌ Python wrapper not found"; \
		echo "Create conceptual_diffusion_wrapper.py first"; \
	fi

# Quick demo
demo: $(TARGET)
	@echo "🎮 Running quick demo..."
	@echo "System Information:"
	@echo "  CUDA Available: $(CUDA_AVAILABLE)"
	@if [ $(CUDA_AVAILABLE) -eq 1 ]; then \
		echo "  GPU Count: $$(nvidia-smi -L 2>/dev/null | wc -l)"; \
	fi
	@echo "Running executable..."
	./$(TARGET) || echo "ℹ️  Note: This runs a basic version. Use 'make test' for full test suite."

# Install dependencies (Ubuntu/Debian)
install-deps:
	@echo "📦 Installing dependencies..."
	@if command -v apt >/dev/null 2>&1; then \
		echo "Installing via apt..."; \
		sudo apt update && sudo apt install -y build-essential libomp-dev; \
		echo "✅ Dependencies installed"; \
	elif command -v yum >/dev/null 2>&1; then \
		echo "Installing via yum..."; \
		sudo yum groupinstall -y "Development Tools" && sudo yum install -y libomp-devel; \
	else \
		echo "❌ Unsupported package manager. Install manually:"; \
		echo "  - build-essential or equivalent"; \
		echo "  - OpenMP development libraries"; \
	fi

# Check system capabilities
check-system:
	@echo "🔍 System Capability Check"
	@echo "=========================="
	@echo "C++ Compiler:"
	@which g++ >/dev/null 2>&1 && echo "  ✅ g++ found: $$(g++ --version | head -n1)" || echo "  ❌ g++ not found"
	@echo "CUDA Compiler:"
	@which nvcc >/dev/null 2>&1 && echo "  ✅ nvcc found: $$(nvcc --version | grep 'release')" || echo "  ❌ nvcc not found"
	@echo "OpenMP Support:"
	@echo '#include <omp.h>' | g++ -fopenmp -x c++ - -o /tmp/omp_test 2>/dev/null && echo "  ✅ OpenMP supported" || echo "  ❌ OpenMP not supported"
	@rm -f /tmp/omp_test
	@echo "GPU Information:"
	@if command -v nvidia-smi >/dev/null 2>&1; then \
		nvidia-smi -L | sed 's/^/  /' || echo "  ❌ nvidia-smi failed"; \
	else \
		echo "  ❌ nvidia-smi not found"; \
	fi

# Clean build artifacts
clean:
	@echo "🧹 Cleaning build artifacts..."
	rm -f $(TARGET) $(LIB_TARGET) $(TEST_TARGET)
	rm -f *.o *.so
	@echo "✅ Clean complete"

# Help target
help:
	@echo "ULTRA Conceptual Diffusion Build System"
	@echo "======================================="
	@echo ""
	@echo "Available targets:"
	@echo "  all          - Build main executable (default)"
	@echo "  test         - Build and optionally run tests"
	@echo "  run-test     - Build and run test suite"
	@echo "  library      - Build shared library for Python"
	@echo "  python       - Setup Python integration"
	@echo "  demo         - Run quick demonstration"
	@echo "  check-system - Check system capabilities"
	@echo "  install-deps - Install system dependencies"
	@echo "  clean        - Remove build artifacts"
	@echo "  help         - Show this help message"
	@echo ""
	@echo "Examples:"
	@echo "  make              # Build executable"
	@echo "  make test         # Build test version"
	@echo "  make run-test     # Run tests"
	@echo "  make library      # Build for Python"
	@echo "  make demo         # Quick demo"

# Prevent make from treating these as file targets
.PHONY: all info library test run-test python demo install-deps check-system clean help