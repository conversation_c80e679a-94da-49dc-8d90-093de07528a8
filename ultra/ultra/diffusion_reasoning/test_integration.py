#!/usr/bin/env python3
"""
ULTRA: Ultimate Learning & Thought Reasoning Architecture
Integration Test Suite

This module provides comprehensive integration tests for the ULTRA
conceptual diffusion system, testing the interaction between Python
wrapper and C++ backend.

Author: ULTRA Development Team
Version: 1.0.0
License: MIT
"""

import sys
import os
import unittest
import numpy as np
import time
import tempfile
import shutil
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import logging

# Add the CUDA directory to Python path
cuda_dir = Path(__file__).parent / "ultra" / "ultra" / "diffusion_reasoning" / "cuda"
if cuda_dir.exists():
    sys.path.insert(0, str(cuda_dir))
else:
    # Alternative path for when run from different location
    cuda_dir = Path(__file__).parent / "cuda"
    if cuda_dir.exists():
        sys.path.insert(0, str(cuda_dir))

try:
    from conceptual_diffusion_wrapper import ConceptualDiffusionPython, create_conceptual_diffusion
    WRAPPER_AVAILABLE = True
except ImportError as e:
    print(f"⚠️  Warning: Could not import conceptual diffusion wrapper: {e}")
    WRAPPER_AVAILABLE = False

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TestConceptualDiffusionIntegration(unittest.TestCase):
    """Integration tests for ULTRA Conceptual Diffusion system."""
    
    @classmethod
    def setUpClass(cls):
        """Set up test class."""
        cls.test_config = {
            'concept_dim': 128,
            'batch_size': 2,
            'num_timesteps': 100,
            'hierarchical_levels': 2,
            'beta_start': 1e-4,
            'beta_end': 0.02,
            'guidance_scale': 3.0,
            'time_embedding_scale': 1.0,
            'noise_schedule_type': 1,  # cosine
            'precision_mode': 0,       # fp32
            'use_flash_attention': 1,
            'enable_caching': 1,
            'use_cuda': 1,
        }
        
    def setUp(self):
        """Set up each test."""
        if not WRAPPER_AVAILABLE:
            self.skipTest("Conceptual diffusion wrapper not available")
    
    def test_system_initialization_cpu(self):
        """Test system initialization in CPU mode."""
        logger.info("🧪 Testing CPU initialization")
        
        config = self.test_config.copy()
        config['use_cuda'] = 0
        
        with create_conceptual_diffusion(config, auto_compile=True) as diffusion:
            self.assertIsNotNone(diffusion)
            self.assertTrue(diffusion._is_initialized)
            self.assertFalse(diffusion.is_cuda_available() and diffusion.state.is_gpu_mode)
            logger.info("   ✅ CPU initialization successful")
    
    def test_system_initialization_gpu(self):
        """Test system initialization in GPU mode (if available)."""
        logger.info("🧪 Testing GPU initialization")
        
        config = self.test_config.copy()
        config['use_cuda'] = 1
        
        with create_conceptual_diffusion(config, auto_compile=True) as diffusion:
            self.assertIsNotNone(diffusion)
            self.assertTrue(diffusion._is_initialized)
            
            if diffusion.is_cuda_available():
                logger.info("   ✅ GPU initialization successful")
            else:
                logger.info("   ℹ️  GPU not available, using CPU fallback")
    
    def test_forward_diffusion_basic(self):
        """Test basic forward diffusion functionality."""
        logger.info("🧪 Testing forward diffusion")
        
        with create_conceptual_diffusion(self.test_config, auto_compile=True) as diffusion:
            batch_size = diffusion.config.batch_size
            concept_dim = diffusion.config.concept_dim
            
            # Create test input
            initial_concepts = np.random.randn(batch_size, concept_dim).astype(np.float32)
            timestep = 50
            
            # Apply forward diffusion
            noisy_concepts = diffusion.forward_diffusion(initial_concepts, timestep)
            
            # Verify output
            self.assertEqual(noisy_concepts.shape, initial_concepts.shape)
            self.assertFalse(np.array_equal(noisy_concepts, initial_concepts))
            self.assertTrue(np.isfinite(noisy_concepts).all())
            
            # Verify noise was added
            noise_level = np.std(noisy_concepts - initial_concepts)
            self.assertGreater(noise_level, 0.1)  # Should have significant noise
            
            logger.info(f"   ✅ Forward diffusion successful, noise level: {noise_level:.4f}")
    
    def test_reverse_diffusion_basic(self):
        """Test basic reverse diffusion functionality."""
        logger.info("🧪 Testing reverse diffusion")
        
        with create_conceptual_diffusion(self.test_config, auto_compile=True) as diffusion:
            batch_size = diffusion.config.batch_size
            concept_dim = diffusion.config.concept_dim
            
            # Create test input
            noisy_concepts = np.random.randn(batch_size, concept_dim).astype(np.float32)
            predicted_noise = np.random.randn(batch_size, concept_dim).astype(np.float32) * 0.1
            timestep = 50
            
            # Apply reverse diffusion
            denoised_concepts = diffusion.reverse_diffusion(noisy_concepts, predicted_noise, timestep)
            
            # Verify output
            self.assertEqual(denoised_concepts.shape, noisy_concepts.shape)
            self.assertTrue(np.isfinite(denoised_concepts).all())
            
            # Should be different from input
            self.assertFalse(np.array_equal(denoised_concepts, noisy_concepts))
            
            logger.info("   ✅ Reverse diffusion successful")
    
    def test_uncertainty_quantification(self):
        """Test uncertainty quantification functionality."""
        logger.info("🧪 Testing uncertainty quantification")
        
        with create_conceptual_diffusion(self.test_config, auto_compile=True) as diffusion:
            batch_size = diffusion.config.batch_size
            concept_dim = diffusion.config.concept_dim
            num_ensembles = 3
            
            # Create ensemble predictions with varying uncertainty
            ensemble_predictions = np.random.randn(num_ensembles, batch_size, concept_dim).astype(np.float32)
            
            # Add some variation to create uncertainty
            for i in range(num_ensembles):
                ensemble_predictions[i] += np.random.randn(batch_size, concept_dim).astype(np.float32) * 0.1
            
            # Compute uncertainty
            uncertainty = diffusion.compute_uncertainty(ensemble_predictions)
            
            # Verify results
            self.assertIsInstance(uncertainty, dict)
            self.assertIn('epistemic_uncertainty', uncertainty)
            self.assertIn('total_uncertainty', uncertainty)
            self.assertIn('confidence_score', uncertainty)
            self.assertIn('uncertainty_map', uncertainty)
            
            # Verify values are reasonable
            self.assertGreater(uncertainty['total_uncertainty'], 0)
            self.assertLessEqual(uncertainty['confidence_score'], 1.0)
            self.assertGreaterEqual(uncertainty['confidence_score'], 0.0)
            
            # Verify uncertainty map shape
            self.assertEqual(uncertainty['uncertainty_map'].shape, (batch_size, concept_dim))
            
            logger.info(f"   ✅ Uncertainty quantification successful")
            logger.info(f"      Total uncertainty: {uncertainty['total_uncertainty']:.4f}")
            logger.info(f"      Confidence score: {uncertainty['confidence_score']:.4f}")
    
    def test_concept_generation_pipeline(self):
        """Test the full concept generation pipeline."""
        logger.info("🧪 Testing concept generation pipeline")
        
        with create_conceptual_diffusion(self.test_config, auto_compile=True) as diffusion:
            batch_size = diffusion.config.batch_size
            concept_dim = diffusion.config.concept_dim
            
            # Create initial concepts
            initial_concepts = np.random.randn(batch_size, concept_dim).astype(np.float32)
            num_steps = 10
            
            # Generate concept trajectory
            trajectory = diffusion.generate_concepts(initial_concepts, num_steps=num_steps)
            
            # Verify trajectory
            self.assertEqual(len(trajectory), num_steps + 1)  # Including initial state
            
            for i, concepts in enumerate(trajectory):
                self.assertEqual(concepts.shape, (batch_size, concept_dim))
                self.assertTrue(np.isfinite(concepts).all())
            
            # Verify progression
            self.assertFalse(np.array_equal(trajectory[0], trajectory[-1]))
            
            logger.info(f"   ✅ Concept generation successful, {len(trajectory)} steps")
    
    def test_different_batch_sizes(self):
        """Test system with different batch sizes."""
        logger.info("🧪 Testing different batch sizes")
        
        batch_sizes = [1, 2, 4, 8]
        
        for batch_size in batch_sizes:
            with self.subTest(batch_size=batch_size):
                config = self.test_config.copy()
                config['batch_size'] = batch_size
                
                with create_conceptual_diffusion(config, auto_compile=True) as diffusion:
                    concept_dim = diffusion.config.concept_dim
                    
                    # Test forward diffusion
                    concepts = np.random.randn(batch_size, concept_dim).astype(np.float32)
                    noisy_concepts = diffusion.forward_diffusion(concepts, 25)
                    
                    self.assertEqual(noisy_concepts.shape, (batch_size, concept_dim))
                    
                    logger.info(f"   ✅ Batch size {batch_size} successful")
    
    def test_different_concept_dimensions(self):
        """Test system with different concept dimensions."""
        logger.info("🧪 Testing different concept dimensions")
        
        concept_dims = [64, 128, 256, 512]
        
        for concept_dim in concept_dims:
            with self.subTest(concept_dim=concept_dim):
                config = self.test_config.copy()
                config['concept_dim'] = concept_dim
                
                with create_conceptual_diffusion(config, auto_compile=True) as diffusion:
                    batch_size = diffusion.config.batch_size
                    
                    # Test forward diffusion
                    concepts = np.random.randn(batch_size, concept_dim).astype(np.float32)
                    noisy_concepts = diffusion.forward_diffusion(concepts, 25)
                    
                    self.assertEqual(noisy_concepts.shape, (batch_size, concept_dim))
                    
                    logger.info(f"   ✅ Concept dim {concept_dim} successful")
    
    def test_numerical_stability(self):
        """Test numerical stability with edge cases."""
        logger.info("🧪 Testing numerical stability")
        
        with create_conceptual_diffusion(self.test_config, auto_compile=True) as diffusion:
            batch_size = diffusion.config.batch_size
            concept_dim = diffusion.config.concept_dim
            
            # Test with large values
            large_concepts = np.random.randn(batch_size, concept_dim).astype(np.float32) * 100
            result = diffusion.forward_diffusion(large_concepts, 50)
            self.assertTrue(np.isfinite(result).all())
            
            # Test with small values
            small_concepts = np.random.randn(batch_size, concept_dim).astype(np.float32) * 0.001
            result = diffusion.forward_diffusion(small_concepts, 50)
            self.assertTrue(np.isfinite(result).all())
            
            # Test with zeros
            zero_concepts = np.zeros((batch_size, concept_dim), dtype=np.float32)
            result = diffusion.forward_diffusion(zero_concepts, 50)
            self.assertTrue(np.isfinite(result).all())
            
            logger.info("   ✅ Numerical stability tests passed")
    
    def test_timestep_boundaries(self):
        """Test behavior at timestep boundaries."""
        logger.info("🧪 Testing timestep boundaries")
        
        with create_conceptual_diffusion(self.test_config, auto_compile=True) as diffusion:
            batch_size = diffusion.config.batch_size
            concept_dim = diffusion.config.concept_dim
            max_timesteps = diffusion.config.num_timesteps
            
            concepts = np.random.randn(batch_size, concept_dim).astype(np.float32)
            
            # Test first timestep
            result = diffusion.forward_diffusion(concepts, 0)
            self.assertTrue(np.isfinite(result).all())
            
            # Test middle timestep
            result = diffusion.forward_diffusion(concepts, max_timesteps // 2)
            self.assertTrue(np.isfinite(result).all())
            
            # Test near last timestep
            result = diffusion.forward_diffusion(concepts, max_timesteps - 1)
            self.assertTrue(np.isfinite(result).all())
            
            logger.info("   ✅ Timestep boundary tests passed")
    
    def test_performance_consistency(self):
        """Test that performance is consistent across multiple runs."""
        logger.info("🧪 Testing performance consistency")
        
        with create_conceptual_diffusion(self.test_config, auto_compile=True) as diffusion:
            batch_size = diffusion.config.batch_size
            concept_dim = diffusion.config.concept_dim
            
            concepts = np.random.randn(batch_size, concept_dim).astype(np.float32)
            
            # Run multiple times and measure consistency
            times = []
            for _ in range(10):
                start_time = time.time()
                diffusion.forward_diffusion(concepts, 50)
                end_time = time.time()
                times.append(end_time - start_time)
            
            # Check that times are reasonably consistent
            mean_time = np.mean(times)
            std_time = np.std(times)
            cv = std_time / mean_time  # Coefficient of variation
            
            self.assertLess(cv, 0.5)  # Should be reasonably consistent
            
            logger.info(f"   ✅ Performance consistency: {mean_time:.4f}±{std_time:.4f}s (CV: {cv:.3f})")

class TestSystemCapabilities(unittest.TestCase):
    """Test system capabilities and environment."""
    
    def test_library_compilation(self):
        """Test that the library can be compiled."""
        logger.info("🧪 Testing library compilation")
        
        # This test will be skipped if wrapper is not available
        if not WRAPPER_AVAILABLE:
            self.skipTest("Wrapper not available for compilation test")
        
        # Try to create a simple diffusion system
        simple_config = {
            'concept_dim': 64,
            'batch_size': 1,
            'num_timesteps': 10,
            'hierarchical_levels': 1,
        }
        
        try:
            with create_conceptual_diffusion(simple_config, auto_compile=True) as diffusion:
                self.assertIsNotNone(diffusion)
                logger.info("   ✅ Library compilation successful")
        except Exception as e:
            self.fail(f"Library compilation failed: {e}")
    
    def test_memory_cleanup(self):
        """Test that memory is properly cleaned up."""
        logger.info("🧪 Testing memory cleanup")
        
        if not WRAPPER_AVAILABLE:
            self.skipTest("Wrapper not available for memory test")
        
        import psutil
        process = psutil.Process()
        
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Create and destroy multiple diffusion systems
        for i in range(5):
            with create_conceptual_diffusion(self.test_config, auto_compile=True) as diffusion:
                concepts = np.random.randn(2, 128).astype(np.float32)
                diffusion.forward_diffusion(concepts, 25)
        
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory
        
        # Allow some memory increase, but not excessive
        self.assertLess(memory_increase, 500)  # Less than 500MB increase
        
        logger.info(f"   ✅ Memory cleanup: {memory_increase:.1f}MB increase")

def run_integration_tests(test_cuda: bool = True, test_cpu: bool = True, 
                         compare_performance: bool = False, verbose: bool = False):
    """Run the complete integration test suite."""
    
    if verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    logger.info("🚀 ULTRA Conceptual Diffusion Integration Tests")
    logger.info("=" * 60)
    
    # Create test suite
    suite = unittest.TestSuite()
    
    # Add basic functionality tests
    suite.addTest(TestConceptualDiffusionIntegration('test_system_initialization_cpu'))
    if test_cuda:
        suite.addTest(TestConceptualDiffusionIntegration('test_system_initialization_gpu'))
    
    suite.addTest(TestConceptualDiffusionIntegration('test_forward_diffusion_basic'))
    suite.addTest(TestConceptualDiffusionIntegration('test_reverse_diffusion_basic'))
    suite.addTest(TestConceptualDiffusionIntegration('test_uncertainty_quantification'))
    suite.addTest(TestConceptualDiffusionIntegration('test_concept_generation_pipeline'))
    
    # Add robustness tests
    suite.addTest(TestConceptualDiffusionIntegration('test_different_batch_sizes'))
    suite.addTest(TestConceptualDiffusionIntegration('test_different_concept_dimensions'))
    suite.addTest(TestConceptualDiffusionIntegration('test_numerical_stability'))
    suite.addTest(TestConceptualDiffusionIntegration('test_timestep_boundaries'))
    suite.addTest(TestConceptualDiffusionIntegration('test_performance_consistency'))
    
    # Add system tests
    suite.addTest(TestSystemCapabilities('test_library_compilation'))
    suite.addTest(TestSystemCapabilities('test_memory_cleanup'))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2 if verbose else 1)
    result = runner.run(suite)
    
    # Print summary
    logger.info("\n📊 Test Summary")
    logger.info("=" * 60)
    logger.info(f"Tests run: {result.testsRun}")
    logger.info(f"Failures: {len(result.failures)}")
    logger.info(f"Errors: {len(result.errors)}")
    logger.info(f"Success rate: {((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100):.1f}%")
    
    if result.failures:
        logger.error("\n❌ Failures:")
        for test, traceback in result.failures:
            logger.error(f"   {test}: {traceback}")
    
    if result.errors:
        logger.error("\n💥 Errors:")
        for test, traceback in result.errors:
            logger.error(f"   {test}: {traceback}")
    
    success = len(result.failures) == 0 and len(result.errors) == 0
    
    if success:
        logger.info("\n🎉 All tests passed successfully!")
    else:
        logger.error("\n💔 Some tests failed")
    
    return success

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="ULTRA Conceptual Diffusion Integration Tests")
    parser.add_argument('--test-cuda', action='store_true', default=True,
                       help='Test CUDA functionality')
    parser.add_argument('--test-cpu', action='store_true', default=True,
                       help='Test CPU functionality')
    parser.add_argument('--compare-performance', action='store_true',
                       help='Compare CPU vs GPU performance')
    parser.add_argument('--verbose', action='store_true',
                       help='Verbose output')
    
    args = parser.parse_args()
    
    # Set test config for the class
    TestConceptualDiffusionIntegration.test_config = {
        'concept_dim': 128,
        'batch_size': 2,
        'num_timesteps': 100,
        'hierarchical_levels': 2,
        'beta_start': 1e-4,
        'beta_end': 0.02,
        'guidance_scale': 3.0,
        'time_embedding_scale': 1.0,
        'noise_schedule_type': 1,
        'precision_mode': 0,
        'use_flash_attention': 1,
        'enable_caching': 1,
        'use_cuda': 1,
    }
    
    success = run_integration_tests(
        test_cuda=args.test_cuda,
        test_cpu=args.test_cpu,
        compare_performance=args.compare_performance,
        verbose=args.verbose
    )
    
    sys.exit(0 if success else 1)