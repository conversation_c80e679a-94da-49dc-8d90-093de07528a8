#!/usr/bin/env python3
"""
ULTRA Semantic Knowledge Base
============================

Semantic knowledge management system for the ULTRA framework.
This module provides semantic knowledge storage and retrieval capabilities.

Author: ULTRA Development Team
License: MIT
Version: 1.0.0
"""

import logging
import time
import uuid
from typing import Dict, List, Any, Optional, Tuple, Set
from dataclasses import dataclass, field
import numpy as np

# Set up logging
logger = logging.getLogger(__name__)

@dataclass
class SemanticConcept:
    """Represents a semantic concept."""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    name: str = ""
    description: str = ""
    properties: Dict[str, Any] = field(default_factory=dict)
    relationships: Dict[str, List[str]] = field(default_factory=dict)  # relation_type -> [concept_ids]
    embedding: Optional[np.ndarray] = None
    confidence: float = 1.0
    created_at: float = field(default_factory=time.time)
    updated_at: float = field(default_factory=time.time)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'properties': self.properties,
            'relationships': self.relationships,
            'embedding': self.embedding.tolist() if self.embedding is not None else None,
            'confidence': self.confidence,
            'created_at': self.created_at,
            'updated_at': self.updated_at
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SemanticConcept':
        """Create from dictionary."""
        if 'embedding' in data and data['embedding'] is not None:
            data['embedding'] = np.array(data['embedding'])
        return cls(**data)


class SemanticKnowledge:
    """
    Semantic knowledge base for storing and retrieving semantic concepts.
    
    This class manages semantic concepts with relationship modeling and
    embedding-based similarity search.
    """
    
    def __init__(self, embedding_dim: int = 512):
        """
        Initialize semantic knowledge base.
        
        Args:
            embedding_dim: Dimension of concept embeddings
        """
        self.embedding_dim = embedding_dim
        self.concepts: Dict[str, SemanticConcept] = {}
        self.name_index: Dict[str, str] = {}  # name -> concept_id
        self.relationship_index: Dict[str, Set[str]] = {}  # relation_type -> {concept_ids}
        
        logger.info(f"SemanticKnowledge initialized with embedding_dim={embedding_dim}")
    
    def add_concept(self, name: str, description: str = "", 
                   properties: Optional[Dict[str, Any]] = None,
                   embedding: Optional[np.ndarray] = None) -> str:
        """
        Add a semantic concept.
        
        Args:
            name: Concept name
            description: Concept description
            properties: Concept properties
            embedding: Concept embedding vector
            
        Returns:
            Concept ID
        """
        concept = SemanticConcept(
            name=name,
            description=description,
            properties=properties or {},
            embedding=embedding
        )
        
        # Store concept
        self.concepts[concept.id] = concept
        self.name_index[name.lower()] = concept.id
        
        logger.debug(f"Added concept '{name}' with ID {concept.id}")
        return concept.id
    
    def get_concept(self, concept_id: str) -> Optional[SemanticConcept]:
        """
        Get a concept by ID.
        
        Args:
            concept_id: Concept identifier
            
        Returns:
            Concept if found, None otherwise
        """
        return self.concepts.get(concept_id)
    
    def get_concept_by_name(self, name: str) -> Optional[SemanticConcept]:
        """
        Get a concept by name.
        
        Args:
            name: Concept name
            
        Returns:
            Concept if found, None otherwise
        """
        concept_id = self.name_index.get(name.lower())
        if concept_id:
            return self.concepts.get(concept_id)
        return None
    
    def add_relationship(self, concept1_id: str, relation_type: str, concept2_id: str) -> bool:
        """
        Add a relationship between two concepts.
        
        Args:
            concept1_id: First concept ID
            relation_type: Type of relationship
            concept2_id: Second concept ID
            
        Returns:
            True if successful, False otherwise
        """
        if concept1_id not in self.concepts or concept2_id not in self.concepts:
            return False
        
        # Add relationship to first concept
        concept1 = self.concepts[concept1_id]
        if relation_type not in concept1.relationships:
            concept1.relationships[relation_type] = []
        if concept2_id not in concept1.relationships[relation_type]:
            concept1.relationships[relation_type].append(concept2_id)
        
        # Update relationship index
        if relation_type not in self.relationship_index:
            self.relationship_index[relation_type] = set()
        self.relationship_index[relation_type].add(concept1_id)
        
        concept1.updated_at = time.time()
        
        logger.debug(f"Added relationship {concept1_id} --{relation_type}--> {concept2_id}")
        return True
    
    def get_related_concepts(self, concept_id: str, relation_type: Optional[str] = None) -> List[SemanticConcept]:
        """
        Get concepts related to a given concept.
        
        Args:
            concept_id: Concept ID
            relation_type: Specific relation type (optional)
            
        Returns:
            List of related concepts
        """
        if concept_id not in self.concepts:
            return []
        
        concept = self.concepts[concept_id]
        related_concepts = []
        
        if relation_type:
            # Get concepts for specific relation type
            if relation_type in concept.relationships:
                for related_id in concept.relationships[relation_type]:
                    if related_id in self.concepts:
                        related_concepts.append(self.concepts[related_id])
        else:
            # Get all related concepts
            for rel_type, related_ids in concept.relationships.items():
                for related_id in related_ids:
                    if related_id in self.concepts:
                        related_concepts.append(self.concepts[related_id])
        
        return related_concepts
    
    def search_concepts(self, query: str, limit: int = 10) -> List[Tuple[SemanticConcept, float]]:
        """
        Search concepts by name and description.
        
        Args:
            query: Search query
            limit: Maximum number of results
            
        Returns:
            List of (concept, similarity_score) tuples
        """
        query_lower = query.lower()
        results = []
        
        for concept in self.concepts.values():
            score = 0.0
            
            # Name matching
            if query_lower in concept.name.lower():
                score += 1.0
            
            # Description matching
            if query_lower in concept.description.lower():
                score += 0.5
            
            # Property matching
            for prop_value in concept.properties.values():
                if isinstance(prop_value, str) and query_lower in prop_value.lower():
                    score += 0.3
            
            if score > 0:
                results.append((concept, score))
        
        # Sort by score and return top results
        results.sort(key=lambda x: x[1], reverse=True)
        return results[:limit]
    
    def find_similar_concepts(self, concept_id: str, limit: int = 10) -> List[Tuple[SemanticConcept, float]]:
        """
        Find concepts similar to a given concept using embeddings.
        
        Args:
            concept_id: Reference concept ID
            limit: Maximum number of results
            
        Returns:
            List of (concept, similarity_score) tuples
        """
        if concept_id not in self.concepts:
            return []
        
        reference_concept = self.concepts[concept_id]
        if reference_concept.embedding is None:
            return []
        
        similarities = []
        ref_embedding = reference_concept.embedding
        
        for other_concept in self.concepts.values():
            if other_concept.id == concept_id or other_concept.embedding is None:
                continue
            
            # Compute cosine similarity
            similarity = np.dot(ref_embedding, other_concept.embedding) / (
                np.linalg.norm(ref_embedding) * np.linalg.norm(other_concept.embedding)
            )
            similarities.append((other_concept, float(similarity)))
        
        # Sort by similarity and return top results
        similarities.sort(key=lambda x: x[1], reverse=True)
        return similarities[:limit]
    
    def get_concept_statistics(self) -> Dict[str, Any]:
        """
        Get statistics about the knowledge base.
        
        Returns:
            Statistics dictionary
        """
        if not self.concepts:
            return {
                'total_concepts': 0,
                'total_relationships': 0,
                'relationship_types': 0,
                'concepts_with_embeddings': 0
            }
        
        total_relationships = sum(
            len(relationships) 
            for concept in self.concepts.values() 
            for relationships in concept.relationships.values()
        )
        
        concepts_with_embeddings = sum(
            1 for concept in self.concepts.values() 
            if concept.embedding is not None
        )
        
        return {
            'total_concepts': len(self.concepts),
            'total_relationships': total_relationships,
            'relationship_types': len(self.relationship_index),
            'concepts_with_embeddings': concepts_with_embeddings,
            'avg_relationships_per_concept': total_relationships / len(self.concepts) if self.concepts else 0
        }
    
    def export_concepts(self) -> List[Dict[str, Any]]:
        """Export all concepts as dictionaries."""
        return [concept.to_dict() for concept in self.concepts.values()]
    
    def import_concepts(self, concept_data: List[Dict[str, Any]]) -> int:
        """
        Import concepts from dictionaries.
        
        Args:
            concept_data: List of concept dictionaries
            
        Returns:
            Number of concepts imported
        """
        imported_count = 0
        
        for data in concept_data:
            try:
                concept = SemanticConcept.from_dict(data)
                self.concepts[concept.id] = concept
                self.name_index[concept.name.lower()] = concept.id
                
                # Update relationship index
                for relation_type in concept.relationships.keys():
                    if relation_type not in self.relationship_index:
                        self.relationship_index[relation_type] = set()
                    self.relationship_index[relation_type].add(concept.id)
                
                imported_count += 1
            except Exception as e:
                logger.warning(f"Failed to import concept: {e}")
        
        logger.info(f"Imported {imported_count} concepts")
        return imported_count


# Create default instance
default_semantic_kb = SemanticKnowledge()

logger.info("ULTRA Semantic Knowledge module initialized successfully")
