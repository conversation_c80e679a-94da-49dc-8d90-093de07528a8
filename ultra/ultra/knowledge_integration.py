#!/usr/bin/env python3
"""
ULTRA Knowledge Integration
===========================

Knowledge integration system for the ULTRA framework.
This module provides integration capabilities across different knowledge types.

Author: ULTRA Development Team
License: MIT
Version: 1.0.0
"""

import logging
import time
from typing import Dict, List, Any, Optional, Tuple
import numpy as np

# Set up logging
logger = logging.getLogger(__name__)

class KnowledgeIntegrator:
    """
    Knowledge integrator for combining different types of knowledge.
    
    This class integrates episodic, semantic, and procedural knowledge
    to provide unified knowledge access and reasoning capabilities.
    """
    
    def __init__(self):
        """Initialize knowledge integrator."""
        self.episodic_kb = None
        self.semantic_kb = None
        self.procedural_kb = None
        self.integration_weights = {
            'episodic': 0.4,
            'semantic': 0.4,
            'procedural': 0.2
        }
        
        logger.info("KnowledgeIntegrator initialized")
    
    def set_knowledge_bases(self, episodic_kb=None, semantic_kb=None, procedural_kb=None):
        """Set knowledge bases for integration."""
        if episodic_kb:
            self.episodic_kb = episodic_kb
        if semantic_kb:
            self.semantic_kb = semantic_kb
        if procedural_kb:
            self.procedural_kb = procedural_kb
    
    def integrate_query(self, query: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Integrate knowledge from multiple sources for a query.
        
        Args:
            query: Query string
            context: Optional context
            
        Returns:
            Integrated knowledge response
        """
        results = {
            'episodic': [],
            'semantic': [],
            'procedural': [],
            'integrated_response': None
        }
        
        # Query episodic knowledge
        if self.episodic_kb:
            try:
                episodic_results = self.episodic_kb.search_memories(
                    query={'content': query}, 
                    context_filter=context,
                    limit=5
                )
                results['episodic'] = [m.to_dict() for m in episodic_results]
            except Exception as e:
                logger.warning(f"Error querying episodic knowledge: {e}")
        
        # Query semantic knowledge
        if self.semantic_kb:
            try:
                semantic_results = self.semantic_kb.search_concepts(query, limit=5)
                results['semantic'] = [(c.to_dict(), score) for c, score in semantic_results]
            except Exception as e:
                logger.warning(f"Error querying semantic knowledge: {e}")
        
        # Query procedural knowledge
        if self.procedural_kb:
            try:
                procedural_results = self.procedural_kb.search_procedures(query, limit=5)
                results['procedural'] = [p.to_dict() for p in procedural_results]
            except Exception as e:
                logger.warning(f"Error querying procedural knowledge: {e}")
        
        # Integrate results
        results['integrated_response'] = self._integrate_results(results)
        
        return results
    
    def _integrate_results(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """Integrate results from different knowledge sources."""
        integrated = {
            'confidence': 0.0,
            'sources': [],
            'recommendations': [],
            'summary': ''
        }
        
        # Calculate confidence based on available sources
        total_weight = 0.0
        weighted_confidence = 0.0
        
        if results['episodic']:
            weight = self.integration_weights['episodic']
            confidence = min(1.0, len(results['episodic']) / 5.0)
            weighted_confidence += weight * confidence
            total_weight += weight
            integrated['sources'].append('episodic')
        
        if results['semantic']:
            weight = self.integration_weights['semantic']
            confidence = min(1.0, len(results['semantic']) / 5.0)
            weighted_confidence += weight * confidence
            total_weight += weight
            integrated['sources'].append('semantic')
        
        if results['procedural']:
            weight = self.integration_weights['procedural']
            confidence = min(1.0, len(results['procedural']) / 5.0)
            weighted_confidence += weight * confidence
            total_weight += weight
            integrated['sources'].append('procedural')
        
        if total_weight > 0:
            integrated['confidence'] = weighted_confidence / total_weight
        
        # Generate summary
        summary_parts = []
        if results['episodic']:
            summary_parts.append(f"Found {len(results['episodic'])} relevant memories")
        if results['semantic']:
            summary_parts.append(f"Found {len(results['semantic'])} related concepts")
        if results['procedural']:
            summary_parts.append(f"Found {len(results['procedural'])} applicable procedures")
        
        integrated['summary'] = "; ".join(summary_parts) if summary_parts else "No relevant knowledge found"
        
        return integrated
    
    def learn_from_interaction(self, query: str, response: str, feedback: float):
        """Learn from user interaction to improve integration."""
        # Simple learning mechanism - adjust weights based on feedback
        if feedback > 0.5:  # Positive feedback
            # Slightly increase weights for sources that contributed
            adjustment = 0.01 * feedback
            for source in ['episodic', 'semantic', 'procedural']:
                if getattr(self, f"{source}_kb") is not None:
                    self.integration_weights[source] += adjustment
        else:  # Negative feedback
            # Slightly decrease weights
            adjustment = 0.01 * (1.0 - feedback)
            for source in ['episodic', 'semantic', 'procedural']:
                if getattr(self, f"{source}_kb") is not None:
                    self.integration_weights[source] -= adjustment
        
        # Normalize weights
        total = sum(self.integration_weights.values())
        if total > 0:
            for key in self.integration_weights:
                self.integration_weights[key] /= total
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get integration statistics."""
        stats = {
            'integration_weights': self.integration_weights.copy(),
            'available_sources': [],
            'total_knowledge_items': 0
        }
        
        if self.episodic_kb:
            stats['available_sources'].append('episodic')
            try:
                ep_stats = self.episodic_kb.get_memory_statistics()
                stats['episodic_memories'] = ep_stats.get('total_memories', 0)
                stats['total_knowledge_items'] += stats['episodic_memories']
            except:
                pass
        
        if self.semantic_kb:
            stats['available_sources'].append('semantic')
            try:
                sem_stats = self.semantic_kb.get_concept_statistics()
                stats['semantic_concepts'] = sem_stats.get('total_concepts', 0)
                stats['total_knowledge_items'] += stats['semantic_concepts']
            except:
                pass
        
        if self.procedural_kb:
            stats['available_sources'].append('procedural')
            try:
                proc_stats = self.procedural_kb.get_execution_statistics()
                stats['procedural_procedures'] = proc_stats.get('total_procedures', 0)
                stats['total_knowledge_items'] += stats['procedural_procedures']
            except:
                pass
        
        return stats


# Create default instance
default_integrator = KnowledgeIntegrator()

logger.info("ULTRA Knowledge Integration module initialized successfully")
