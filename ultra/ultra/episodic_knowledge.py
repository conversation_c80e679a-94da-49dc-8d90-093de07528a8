#!/usr/bin/env python3
"""
ULTRA Episodic Knowledge Base
============================

Episodic knowledge management system for the ULTRA framework.
This module provides episodic memory storage and retrieval capabilities.

Author: ULTRA Development Team
License: MIT
Version: 1.0.0
"""

import logging
import time
import uuid
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field
from datetime import datetime
import numpy as np

# Set up logging
logger = logging.getLogger(__name__)

@dataclass
class EpisodicMemory:
    """Represents an episodic memory entry."""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    timestamp: float = field(default_factory=time.time)
    content: Dict[str, Any] = field(default_factory=dict)
    context: Dict[str, Any] = field(default_factory=dict)
    importance: float = 0.5
    access_count: int = 0
    last_accessed: float = field(default_factory=time.time)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            'id': self.id,
            'timestamp': self.timestamp,
            'content': self.content,
            'context': self.context,
            'importance': self.importance,
            'access_count': self.access_count,
            'last_accessed': self.last_accessed
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'EpisodicMemory':
        """Create from dictionary."""
        return cls(**data)


class EpisodicKnowledgeBase:
    """
    Episodic knowledge base for storing and retrieving episodic memories.
    
    This class manages episodic memories with temporal and contextual indexing.
    """
    
    def __init__(self, max_memories: int = 10000):
        """
        Initialize episodic knowledge base.
        
        Args:
            max_memories: Maximum number of memories to store
        """
        self.max_memories = max_memories
        self.memories: Dict[str, EpisodicMemory] = {}
        self.temporal_index: List[Tuple[float, str]] = []  # (timestamp, memory_id)
        self.context_index: Dict[str, List[str]] = {}  # context_key -> [memory_ids]
        
        logger.info(f"EpisodicKnowledgeBase initialized with max_memories={max_memories}")
    
    def store_memory(self, content: Dict[str, Any], context: Optional[Dict[str, Any]] = None,
                    importance: float = 0.5) -> str:
        """
        Store an episodic memory.
        
        Args:
            content: Memory content
            context: Contextual information
            importance: Importance score (0.0-1.0)
            
        Returns:
            Memory ID
        """
        memory = EpisodicMemory(
            content=content,
            context=context or {},
            importance=importance
        )
        
        # Store memory
        self.memories[memory.id] = memory
        
        # Update temporal index
        self.temporal_index.append((memory.timestamp, memory.id))
        self.temporal_index.sort(key=lambda x: x[0])
        
        # Update context index
        for key, value in memory.context.items():
            context_key = f"{key}:{value}"
            if context_key not in self.context_index:
                self.context_index[context_key] = []
            self.context_index[context_key].append(memory.id)
        
        # Manage memory limit
        self._manage_memory_limit()
        
        logger.debug(f"Stored memory {memory.id} with importance {importance}")
        return memory.id
    
    def retrieve_memory(self, memory_id: str) -> Optional[EpisodicMemory]:
        """
        Retrieve a specific memory by ID.
        
        Args:
            memory_id: Memory identifier
            
        Returns:
            Memory if found, None otherwise
        """
        if memory_id in self.memories:
            memory = self.memories[memory_id]
            memory.access_count += 1
            memory.last_accessed = time.time()
            return memory
        return None
    
    def search_memories(self, query: Optional[Dict[str, Any]] = None,
                       context_filter: Optional[Dict[str, Any]] = None,
                       time_range: Optional[Tuple[float, float]] = None,
                       limit: int = 10) -> List[EpisodicMemory]:
        """
        Search memories based on various criteria.
        
        Args:
            query: Content query
            context_filter: Context filter
            time_range: Time range (start, end)
            limit: Maximum number of results
            
        Returns:
            List of matching memories
        """
        candidates = list(self.memories.values())
        
        # Apply time filter
        if time_range:
            start_time, end_time = time_range
            candidates = [m for m in candidates 
                         if start_time <= m.timestamp <= end_time]
        
        # Apply context filter
        if context_filter:
            filtered_candidates = []
            for memory in candidates:
                match = True
                for key, value in context_filter.items():
                    if key not in memory.context or memory.context[key] != value:
                        match = False
                        break
                if match:
                    filtered_candidates.append(memory)
            candidates = filtered_candidates
        
        # Apply content query (simple keyword matching)
        if query:
            filtered_candidates = []
            for memory in candidates:
                match = True
                for key, value in query.items():
                    if key not in memory.content:
                        match = False
                        break
                    # Simple string matching
                    if isinstance(value, str) and isinstance(memory.content[key], str):
                        if value.lower() not in memory.content[key].lower():
                            match = False
                            break
                    elif memory.content[key] != value:
                        match = False
                        break
                if match:
                    filtered_candidates.append(memory)
            candidates = filtered_candidates
        
        # Sort by relevance (importance + recency)
        candidates.sort(key=lambda m: m.importance + (1.0 / (time.time() - m.timestamp + 1)), 
                       reverse=True)
        
        # Update access counts
        for memory in candidates[:limit]:
            memory.access_count += 1
            memory.last_accessed = time.time()
        
        return candidates[:limit]
    
    def get_recent_memories(self, count: int = 10) -> List[EpisodicMemory]:
        """
        Get most recent memories.
        
        Args:
            count: Number of memories to retrieve
            
        Returns:
            List of recent memories
        """
        recent_ids = [memory_id for _, memory_id in self.temporal_index[-count:]]
        return [self.memories[memory_id] for memory_id in reversed(recent_ids)]
    
    def get_memory_statistics(self) -> Dict[str, Any]:
        """
        Get statistics about the knowledge base.
        
        Returns:
            Statistics dictionary
        """
        if not self.memories:
            return {
                'total_memories': 0,
                'avg_importance': 0.0,
                'avg_access_count': 0.0,
                'oldest_memory': None,
                'newest_memory': None
            }
        
        importances = [m.importance for m in self.memories.values()]
        access_counts = [m.access_count for m in self.memories.values()]
        timestamps = [m.timestamp for m in self.memories.values()]
        
        return {
            'total_memories': len(self.memories),
            'avg_importance': np.mean(importances),
            'avg_access_count': np.mean(access_counts),
            'oldest_memory': min(timestamps),
            'newest_memory': max(timestamps),
            'context_keys': len(self.context_index)
        }
    
    def _manage_memory_limit(self) -> None:
        """Manage memory limit by removing least important/accessed memories."""
        if len(self.memories) <= self.max_memories:
            return
        
        # Calculate removal score (lower is more likely to be removed)
        memory_scores = []
        current_time = time.time()
        
        for memory in self.memories.values():
            # Score based on importance, access count, and recency
            recency_score = 1.0 / (current_time - memory.last_accessed + 1)
            score = memory.importance * 0.4 + memory.access_count * 0.3 + recency_score * 0.3
            memory_scores.append((score, memory.id))
        
        # Sort by score and remove lowest scoring memories
        memory_scores.sort(key=lambda x: x[0])
        memories_to_remove = len(self.memories) - self.max_memories
        
        for i in range(memories_to_remove):
            _, memory_id = memory_scores[i]
            self._remove_memory(memory_id)
    
    def _remove_memory(self, memory_id: str) -> None:
        """Remove a memory and update indices."""
        if memory_id not in self.memories:
            return
        
        memory = self.memories[memory_id]
        
        # Remove from main storage
        del self.memories[memory_id]
        
        # Remove from temporal index
        self.temporal_index = [(t, mid) for t, mid in self.temporal_index if mid != memory_id]
        
        # Remove from context index
        for key, value in memory.context.items():
            context_key = f"{key}:{value}"
            if context_key in self.context_index:
                self.context_index[context_key] = [
                    mid for mid in self.context_index[context_key] if mid != memory_id
                ]
                if not self.context_index[context_key]:
                    del self.context_index[context_key]
        
        logger.debug(f"Removed memory {memory_id}")


# Create default instance
default_episodic_kb = EpisodicKnowledgeBase()

logger.info("ULTRA Episodic Knowledge module initialized successfully")
