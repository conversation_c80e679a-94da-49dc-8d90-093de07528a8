# ultra/knowledge_management/episodic_knowledge.py

"""
ULTRA: Ultimate Learning & Thought Reasoning Architecture
Episodic Knowledge Management Module

This module implements a biologically-inspired episodic memory system that stores
and retrieves temporally-organized experiences, events, and interactions. The
implementation combines <PERSON> and <PERSON><PERSON>'s Temporal Context Model with advanced
indexing mechanisms and neurally-inspired consolidation processes.

Classes:
    TemporalContext: Implements Howard and <PERSON><PERSON>'s Temporal Context Model
    EpisodicEvent: Represents a single episodic memory event
    EpisodicMemoryTrace: Represents a connection between related episodic events
    MemoryContext: Contains contextual information for memory encoding and retrieval
    TemporalIndex: Indexes memories based on temporal relationships
    ConceptualIndex: Indexes memories based on conceptual content
    SpatialIndex: Indexes memories based on spatial relationships
    MemoryConsolidation: Handles memory consolidation and restructuring
    EpisodicKnowledgeBase: Main class orchestrating the episodic memory system
"""

import os
import uuid
import json
import logging
import numpy as np
import pandas as pd
import threading
import time
import heapq
from typing import Dict, List, Tuple, Set, Any, Optional, Union, Callable
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from collections import defaultdict, deque
import networkx as nx
from scipy import spatial
from sklearn.metrics.pairwise import cosine_similarity

# Try to import FAISS for efficient similarity search
try:
    import faiss
    FAISS_AVAILABLE = True
except ImportError:
    FAISS_AVAILABLE = False
    logging.warning("FAISS not available, falling back to scipy for similarity search")

# Try to import torch for GPU acceleration
try:
    import torch
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False

# Configure logging
logger = logging.getLogger(__name__)


class TemporalContext:
    """
    Represents the temporal context of an episodic memory.
    
    Implements Howard and Kahana's Temporal Context Model (TCM) for 
    episodic memory, where temporal context evolves gradually and 
    binds to experienced events.
    
    Attributes:
        timestamp (pd.Timestamp): When the event occurred
        duration (timedelta): How long the event lasted
        context_vector (np.ndarray): Temporal context vector
        decay_rate (float): Rate at which context influence decays
    """
    
    def __init__(self, 
                 timestamp: Optional[pd.Timestamp] = None, 
                 duration: Optional[timedelta] = None,
                 context_dim: int = 256,
                 decay_rate: float = 0.05):
        """
        Initialize a new temporal context.
        
        Args:
            timestamp (pd.Timestamp, optional): When the event occurred
            duration (timedelta, optional): How long the event lasted
            context_dim (int): Dimension of context vector
            decay_rate (float): Rate at which context influence decays
        """
        self.timestamp = timestamp or pd.Timestamp.now()
        self.duration = duration or timedelta(seconds=0)
        self.context_vector = np.random.normal(0, 1, size=context_dim)
        self.context_vector = self.context_vector / np.linalg.norm(self.context_vector)
        self.decay_rate = decay_rate
        
    def update(self, new_info: Dict[str, Any]) -> None:
        """
        Update the temporal context based on new information.
        
        Args:
            new_info (Dict[str, Any]): New temporal information
        """
        if 'timestamp' in new_info:
            self.timestamp = pd.Timestamp(new_info['timestamp'])
        
        if 'duration' in new_info:
            if isinstance(new_info['duration'], timedelta):
                self.duration = new_info['duration']
            else:
                self.duration = timedelta(seconds=float(new_info['duration']))
        
        if 'context_vector' in new_info:
            new_context = np.array(new_info['context_vector'])
            new_context = new_context / np.linalg.norm(new_context)
            
            # Gradually shift context (TCM model)
            self.context_vector = (1 - self.decay_rate) * self.context_vector + self.decay_rate * new_context
            self.context_vector = self.context_vector / np.linalg.norm(self.context_vector)
    
    def similarity(self, other: 'TemporalContext') -> float:
        """
        Calculate temporal similarity to another context.
        
        Similarity is based on both the proximity in time and 
        the overlap in context vectors.
        
        Args:
            other (TemporalContext): The other temporal context
            
        Returns:
            float: Similarity score (0-1)
        """
        # Time-based similarity (exponential decay with time difference)
        time_diff = abs((self.timestamp - other.timestamp).total_seconds())
        time_sim = np.exp(-time_diff / (24 * 3600))  # 1-day time constant
        
        # Context vector similarity (cosine similarity)
        context_sim = np.dot(self.context_vector, other.context_vector)
        
        # Combine time and context similarity
        return 0.5 * time_sim + 0.5 * context_sim
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert temporal context to dictionary.
        
        Returns:
            Dict[str, Any]: Dictionary representation
        """
        return {
            'timestamp': self.timestamp.isoformat(),
            'duration': self.duration.total_seconds(),
            'context_vector': self.context_vector.tolist(),
            'decay_rate': self.decay_rate
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TemporalContext':
        """
        Create a temporal context from dictionary.
        
        Args:
            data (Dict[str, Any]): Dictionary representation
            
        Returns:
            TemporalContext: Created temporal context
        """
        context = cls()
        context.timestamp = pd.Timestamp(data['timestamp'])
        context.duration = timedelta(seconds=data['duration'])
        context.context_vector = np.array(data['context_vector'])
        context.decay_rate = data.get('decay_rate', 0.05)
        return context


@dataclass
class MemoryContext:
    """
    Contains contextual information for memory encoding and retrieval.
    
    Attributes:
        temporal (Dict[str, Any]): Temporal context information
        spatial (Dict[str, Any]): Spatial context information
        semantic (Dict[str, Any]): Semantic context information
        emotional (Dict[str, Any]): Emotional context information
        perceptual (Dict[str, Any]): Perceptual context information
        social (Dict[str, Any]): Social context information
        task (Dict[str, Any]): Task context information
        attention (Dict[str, Any]): Attention context information
        embedding (Optional[np.ndarray]): Context embedding vector
    """
    temporal: Dict[str, Any] = field(default_factory=dict)
    spatial: Dict[str, Any] = field(default_factory=dict)
    semantic: Dict[str, Any] = field(default_factory=dict)
    emotional: Dict[str, Any] = field(default_factory=dict)
    perceptual: Dict[str, Any] = field(default_factory=dict)
    social: Dict[str, Any] = field(default_factory=dict)
    task: Dict[str, Any] = field(default_factory=dict)
    attention: Dict[str, Any] = field(default_factory=dict)
    embedding: Optional[np.ndarray] = None
    
    def __post_init__(self):
        """Initialize missing fields with empty dicts."""
        for field_name in ['temporal', 'spatial', 'semantic', 'emotional', 
                          'perceptual', 'social', 'task', 'attention']:
            if getattr(self, field_name) is None:
                setattr(self, field_name, {})
        
        # Normalize embedding if provided
        if self.embedding is not None:
            norm = np.linalg.norm(self.embedding)
            if norm > 0:
                self.embedding = self.embedding / norm
    
    def get_current_time(self) -> float:
        """
        Get current time from context or system time.
        
        Returns:
            Current time as float timestamp
        """
        return self.temporal.get('timestamp', time.time())
    
    def similarity(self, other: 'MemoryContext') -> float:
        """
        Calculate similarity between this context and another one.
        
        Args:
            other: Another MemoryContext instance
            
        Returns:
            Similarity score between 0 and 1
        """
        if self.embedding is not None and other.embedding is not None:
            # If both have embeddings, use cosine similarity
            return float(cosine_similarity(
                self.embedding.reshape(1, -1), 
                other.embedding.reshape(1, -1)
            )[0, 0])
        
        # Otherwise calculate similarity based on component overlap
        component_sims = []
        
        # Temporal similarity (based on time difference)
        if 'timestamp' in self.temporal and 'timestamp' in other.temporal:
            time_diff = abs(self.temporal['timestamp'] - other.temporal['timestamp'])
            # Convert to days and use decay function
            days_diff = time_diff / (24 * 3600)
            temporal_sim = np.exp(-0.1 * days_diff)  # Decay constant of 0.1 per day
            component_sims.append(temporal_sim)
        
        # Calculate similarity for each context type
        for context_type in ['spatial', 'semantic', 'emotional', 'perceptual', 
                           'social', 'task', 'attention']:
            self_context = getattr(self, context_type)
            other_context = getattr(other, context_type)
            
            if self_context and other_context:
                # Calculate overlap in context elements
                common_keys = set(self_context.keys()) & set(other_context.keys())
                if common_keys:
                    matches = sum(1 for k in common_keys if self_context[k] == other_context[k])
                    context_sim = matches / len(common_keys)
                    component_sims.append(context_sim)
        
        # If no similarities could be calculated, return 0
        if not component_sims:
            return 0.0
            
        # Return average similarity
        return float(np.mean(component_sims))
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert context to dictionary for serialization.
        
        Returns:
            Dictionary representation of the context
        """
        result = {
            'temporal': self.temporal,
            'spatial': self.spatial,
            'semantic': self.semantic,
            'emotional': self.emotional,
            'perceptual': self.perceptual,
            'social': self.social,
            'task': self.task,
            'attention': self.attention
        }
        
        if self.embedding is not None:
            result['embedding'] = self.embedding.tolist()
            
        return result
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'MemoryContext':
        """
        Create context from dictionary.
        
        Args:
            data: Dictionary representation of context
            
        Returns:
            MemoryContext instance
        """
        embedding = None
        if 'embedding' in data:
            embedding = np.array(data['embedding'], dtype=np.float32)
            
        return cls(
            temporal=data.get('temporal', {}),
            spatial=data.get('spatial', {}),
            semantic=data.get('semantic', {}),
            emotional=data.get('emotional', {}),
            perceptual=data.get('perceptual', {}),
            social=data.get('social', {}),
            task=data.get('task', {}),
            attention=data.get('attention', {}),
            embedding=embedding
        )


class EpisodicEvent:
    """
    Represents a single episodic memory event.
    
    Implements an episodic memory representation inspired by 
    neuroscientific models of hippocampal memory encoding.
    
    Attributes:
        id (str): Unique identifier for the event
        content (Any): The content of the memory
        temporal_context (TemporalContext): When the event occurred
        spatial_context (Dict[str, Any]): Where the event occurred
        perceptual_context (Dict[str, Any]): Sensory details of the event
        emotional_context (Dict[str, Any]): Emotional aspects of the event
        social_context (Dict[str, Any]): Social aspects of the event
        event_type (str): Type of event
        salience (float): Importance/prominence of the memory
        retrieval_count (int): How many times this memory has been retrieved
        last_retrieved (pd.Timestamp): When this memory was last retrieved
        embedding (np.ndarray): Vector embedding of the event
        associations (Set[str]): IDs of associated events
        memory_strength (float): Overall strength of the memory
        activation (float): Current activation level
        retention_score (float): Long-term retention probability
        tags (List[str]): Tags for categorization
        metadata (Dict[str, Any]): Additional metadata
    """
    
    def __init__(self, 
                 id: Optional[str] = None, 
                 content: Any = None,
                 temporal_context: Optional[TemporalContext] = None,
                 event_type: str = "general",
                 embedding_dim: int = 512):
        """
        Initialize a new episodic event.
        
        Args:
            id (str, optional): Unique identifier for the event
            content (Any, optional): The content of the memory
            temporal_context (TemporalContext, optional): When the event occurred
            event_type (str): Type of event
            embedding_dim (int): Dimension of the embedding vector
        """
        self.id = id or str(uuid.uuid4())
        self.content = content
        self.temporal_context = temporal_context or TemporalContext()
        self.spatial_context = {}
        self.perceptual_context = {}
        self.emotional_context = {}
        self.social_context = {}
        self.event_type = event_type
        self.salience = 0.5  # Default middle salience
        self.importance = 0.5  # Synonym for salience, for compatibility
        self.retrieval_count = 0
        self.last_retrieved = None
        self.created_at = pd.Timestamp.now()
        self.modified_at = self.created_at
        self.embedding = np.zeros(embedding_dim)
        self.associations = set()
        self.memory_strength = 0.5  # Default middle strength
        self.activation = 1.0  # New memories start fully activated
        self.retention_score = 1.0  # New memories start with maximum retention
        self.tags = []
        self.metadata = {}
        
        # Store timestamp for compatibility with indices
        self.timestamp = self.temporal_context.timestamp.timestamp()
    
    def update_embedding(self, embedding: np.ndarray) -> None:
        """
        Update the embedding vector for this event.
        
        Args:
            embedding (np.ndarray): New embedding vector
        """
        self.embedding = embedding
        # Normalize embedding to unit length
        norm = np.linalg.norm(self.embedding)
        if norm > 0:
            self.embedding = self.embedding / norm
        self.modified_at = pd.Timestamp.now()
    
    def update_content(self, content: Any, update_embedding_func: Optional[Callable] = None) -> None:
        """
        Update the content of this event.
        
        Args:
            content (Any): New content
            update_embedding_func (Callable, optional): Function to update embedding
        """
        self.content = content
        self.modified_at = pd.Timestamp.now()
        
        # Update embedding if function provided
        if update_embedding_func:
            new_embedding = update_embedding_func(content)
            self.update_embedding(new_embedding)
    
    def update_contexts(self, 
                        temporal: Optional[Dict[str, Any]] = None,
                        spatial: Optional[Dict[str, Any]] = None,
                        perceptual: Optional[Dict[str, Any]] = None,
                        emotional: Optional[Dict[str, Any]] = None,
                        social: Optional[Dict[str, Any]] = None) -> None:
        """
        Update context information for this event.
        
        Args:
            temporal (Dict[str, Any], optional): Temporal context updates
            spatial (Dict[str, Any], optional): Spatial context updates
            perceptual (Dict[str, Any], optional): Perceptual context updates
            emotional (Dict[str, Any], optional): Emotional context updates
            social (Dict[str, Any], optional): Social context updates
        """
        if temporal:
            self.temporal_context.update(temporal)
            # Update timestamp for compatibility with indices
            self.timestamp = self.temporal_context.timestamp.timestamp()
            
        if spatial:
            self.spatial_context.update(spatial)
            
        if perceptual:
            self.perceptual_context.update(perceptual)
            
        if emotional:
            self.emotional_context.update(emotional)
            
        if social:
            self.social_context.update(social)
            
        self.modified_at = pd.Timestamp.now()
    
    def add_association(self, event_id: str) -> None:
        """
        Add an association to another event.
        
        Args:
            event_id (str): ID of the associated event
        """
        self.associations.add(event_id)
        self.modified_at = pd.Timestamp.now()
    
    def remove_association(self, event_id: str) -> None:
        """
        Remove an association to another event.
        
        Args:
            event_id (str): ID of the associated event
        """
        if event_id in self.associations:
            self.associations.remove(event_id)
            self.modified_at = pd.Timestamp.now()
    
    def record_retrieval(self) -> None:
        """
        Record that this event has been retrieved.
        
        Updates retrieval count, last retrieved timestamp, and 
        increases memory strength according to the spacing effect.
        """
        now = pd.Timestamp.now()
        
        # Update retrieval metadata
        self.retrieval_count += 1
        
        # Apply spacing effect if previously retrieved
        if self.last_retrieved is not None:
            time_since_last = (now - self.last_retrieved).total_seconds()
            
            # Optimal spacing effect occurs when retrieval happens at moderate intervals
            # Too soon: minimal benefit, too late: some forgetting
            hours_since_last = time_since_last / 3600
            
            # Implement Bjork & Bjork's New Theory of Disuse spacing effect
            if hours_since_last < 1:  # Too soon
                strength_increase = 0.01
            elif hours_since_last < 24:  # Good spacing
                strength_increase = 0.05
            elif hours_since_last < 72:  # Optimal spacing
                strength_increase = 0.1
            else:  # Long delay, some forgetting but still beneficial
                strength_increase = 0.03
                
            self.memory_strength = min(1.0, self.memory_strength + strength_increase)
        
        # Reactivate the memory
        self.reactivate(0.5)  # Medium activation boost
        
        self.last_retrieved = now
        self.modified_at = now
    
    def calculate_retrieval_probability(self) -> float:
        """
        Calculate probability of retrieving this memory based on current state.
        
        Implements a model based on memory strength, salience, and recency.
        
        Returns:
            float: Retrieval probability (0-1)
        """
        # Base probability from memory strength
        probability = self.memory_strength
        
        # Adjust for salience
        probability *= (0.5 + 0.5 * self.salience)
        
        # Adjust for recency if previously retrieved
        if self.last_retrieved is not None:
            hours_since = (pd.Timestamp.now() - self.last_retrieved).total_seconds() / 3600
            recency_factor = np.exp(-hours_since / 168)  # 1-week time constant
            probability *= (0.7 + 0.3 * recency_factor)
        
        return min(1.0, probability)
    
    def apply_forgetting(self, time_delta: Union[timedelta, float]) -> None:
        """
        Apply forgetting over time using the Ebbinghaus forgetting curve.
        
        Args:
            time_delta (Union[timedelta, float]): Time elapsed since last access
                                                 (as timedelta or seconds)
        """
        # Convert to hours if needed
        if isinstance(time_delta, timedelta):
            hours = time_delta.total_seconds() / 3600
        else:
            hours = time_delta / 3600
        
        # Scale based on memory strength (stronger memories decay slower)
        strength_factor = 24 * (0.5 + self.memory_strength)  # 12-48 hour range
        
        # Calculate retention
        retention = np.exp(-hours / strength_factor)
        
        # Update memory strength and activation
        self.memory_strength *= retention
        self.memory_strength = max(0.1, self.memory_strength)  # Floor at 0.1
        
        # Decay activation
        self.decay_activation(0.1, hours)
    
    def decay_activation(self, decay_rate: float = 0.1, time_elapsed: float = 1.0) -> None:
        """
        Decay the activation of this memory based on time elapsed.
        
        Args:
            decay_rate: Rate of activation decay (higher = faster decay)
            time_elapsed: Time elapsed since last activation, in arbitrary units
        """
        # Exponential decay of activation
        self.activation *= np.exp(-decay_rate * time_elapsed)
        # Ensure activation stays in range [0,1]
        self.activation = max(0.0, min(1.0, self.activation))
    
    def reactivate(self, activation_boost: float = 0.5) -> None:
        """
        Reactivate this memory with the given activation boost.
        
        Args:
            activation_boost: Amount to boost activation by
        """
        self.activation = min(1.0, self.activation + activation_boost)
    
    def update_retention(self, consolidation_factor: float = 0.1) -> None:
        """
        Update the retention score based on importance and consolidation.
        
        Args:
            consolidation_factor: Factor representing memory consolidation strength
        """
        # Retention increases based on importance and activation
        consolidation_boost = consolidation_factor * self.salience * self.activation
        self.retention_score = min(1.0, self.retention_score + consolidation_boost)
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert event to dictionary for serialization.
        
        Returns:
            Dictionary representation of the event
        """
        return {
            'id': self.id,
            'content': self.content,
            'temporal_context': self.temporal_context.to_dict(),
            'spatial_context': self.spatial_context,
            'perceptual_context': self.perceptual_context,
            'emotional_context': self.emotional_context,
            'social_context': self.social_context,
            'event_type': self.event_type,
            'salience': self.salience,
            'importance': self.importance,
            'retrieval_count': self.retrieval_count,
            'last_retrieved': self.last_retrieved.isoformat() if self.last_retrieved else None,
            'created_at': self.created_at.isoformat(),
            'modified_at': self.modified_at.isoformat(),
            'embedding': self.embedding.tolist(),
            'associations': list(self.associations),
            'memory_strength': self.memory_strength,
            'activation': self.activation,
            'retention_score': self.retention_score,
            'tags': self.tags,
            'metadata': self.metadata,
            'timestamp': self.timestamp
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'EpisodicEvent':
        """
        Create an episodic event from dictionary.
        
        Args:
            data (Dict[str, Any]): Dictionary representation
            
        Returns:
            EpisodicEvent: Created episodic event
        """
        event = cls(id=data['id'], content=data['content'])
        
        # Restore all fields
        event.temporal_context = TemporalContext.from_dict(data['temporal_context'])
        event.spatial_context = data.get('spatial_context', {})
        event.perceptual_context = data.get('perceptual_context', {})
        event.emotional_context = data.get('emotional_context', {})
        event.social_context = data.get('social_context', {})
        event.event_type = data.get('event_type', 'general')
        event.salience = data.get('salience', 0.5)
        event.importance = data.get('importance', event.salience)
        event.retrieval_count = data.get('retrieval_count', 0)
        
        if data.get('last_retrieved'):
            event.last_retrieved = pd.Timestamp(data['last_retrieved'])
            
        event.created_at = pd.Timestamp(data['created_at'])
        event.modified_at = pd.Timestamp(data['modified_at'])
        event.embedding = np.array(data['embedding'])
        event.associations = set(data.get('associations', []))
        event.memory_strength = data.get('memory_strength', 0.5)
        event.activation = data.get('activation', 1.0)
        event.retention_score = data.get('retention_score', 1.0)
        event.tags = data.get('tags', [])
        event.metadata = data.get('metadata', {})
        event.timestamp = data.get('timestamp', event.temporal_context.timestamp.timestamp())
        
        return event


class EpisodicMemoryTrace:
    """
    Represents a connection (trace) between related episodic memory events.
    
    Attributes:
        id (str): Unique identifier for the trace
        source_id (str): ID of the source event
        target_id (str): ID of the target event
        relation_type (str): Type of relationship
        strength (float): Strength of the connection
        bidirectional (bool): Whether the connection is bidirectional
        metadata (Dict[str, Any]): Additional metadata
    """
    
    def __init__(self, 
                id: Optional[str] = None,
                source_id: str = "",
                target_id: str = "",
                relation_type: str = "association",
                strength: float = 0.5,
                bidirectional: bool = False,
                metadata: Optional[Dict[str, Any]] = None):
        """
        Initialize a new memory trace.
        
        Args:
            id (str, optional): Unique identifier for the trace
            source_id (str): ID of the source event
            target_id (str): ID of the target event
            relation_type (str): Type of relationship
            strength (float): Strength of the connection
            bidirectional (bool): Whether the connection is bidirectional
            metadata (Dict[str, Any], optional): Additional metadata
        """
        self.id = id or str(uuid.uuid4())
        self.source_id = source_id
        self.target_id = target_id
        self.relation_type = relation_type
        self.strength = max(0.0, min(1.0, strength))  # Ensure in range [0,1]
        self.bidirectional = bidirectional
        self.metadata = metadata or {}
        self.created_at = pd.Timestamp.now()
        self.modified_at = self.created_at
    
    def decay_strength(self, decay_rate: float = 0.05, time_elapsed: float = 1.0) -> None:
        """
        Decay the strength of this memory trace based on time elapsed.
        
        Args:
            decay_rate: Rate of strength decay (higher = faster decay)
            time_elapsed: Time elapsed since last activation, in arbitrary units
        """
        # Exponential decay of strength
        self.strength *= np.exp(-decay_rate * time_elapsed)
        # Ensure strength stays in range [0,1]
        self.strength = max(0.0, min(1.0, self.strength))
        self.modified_at = pd.Timestamp.now()
    
    def reinforce(self, reinforcement: float = 0.1) -> None:
        """
        Reinforce this memory trace with the given reinforcement.
        
        Args:
            reinforcement: Amount to reinforce the trace by
        """
        self.strength = min(1.0, self.strength + reinforcement)
        self.modified_at = pd.Timestamp.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert trace to dictionary for serialization.
        
        Returns:
            Dictionary representation of the trace
        """
        return {
            'id': self.id,
            'source_id': self.source_id,
            'target_id': self.target_id,
            'relation_type': self.relation_type,
            'strength': self.strength,
            'bidirectional': self.bidirectional,
            'metadata': self.metadata,
            'created_at': self.created_at.isoformat(),
            'modified_at': self.modified_at.isoformat()
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'EpisodicMemoryTrace':
        """
        Create trace from dictionary.
        
        Args:
            data: Dictionary representation of trace
            
        Returns:
            EpisodicMemoryTrace instance
        """
        trace = cls(
            id=data['id'],
            source_id=data['source_id'],
            target_id=data['target_id'],
            relation_type=data['relation_type'],
            strength=data.get('strength', 0.5),
            bidirectional=data.get('bidirectional', False),
            metadata=data.get('metadata', {})
        )
        
        if 'created_at' in data:
            trace.created_at = pd.Timestamp(data['created_at'])
        
        if 'modified_at' in data:
            trace.modified_at = pd.Timestamp(data['modified_at'])
            
        return trace


class TemporalIndex:
    """
    Indexes memories based on temporal relationships for efficient retrieval.
    """
    
    def __init__(self, resolution_levels: int = 5):
        """
        Initialize temporal index with specified resolution levels.
        
        Args:
            resolution_levels: Number of temporal resolution levels to maintain
        """
        self.resolution_levels = resolution_levels
        # Maps from time interval to event IDs for each resolution level
        self.time_indices = [defaultdict(set) for _ in range(resolution_levels)]
        # Resolutions in seconds (1min, 1hr, 1day, 1week, 1month)
        self.resolutions = [60, 3600, 86400, 604800, 2592000][:resolution_levels]
        # Mapping from event ID to timestamp
        self.event_timestamps = {}
        
    def add_event(self, event_id: str, timestamp: float) -> None:
        """
        Add an event to the temporal index.
        
        Args:
            event_id: ID of the event to add
            timestamp: Timestamp of the event
        """
        self.event_timestamps[event_id] = timestamp
        
        # Add to each resolution level
        for level in range(min(self.resolution_levels, len(self.resolutions))):
            resolution = self.resolutions[level]
            # Calculate time bin for this resolution
            time_bin = int(timestamp / resolution)
            self.time_indices[level][time_bin].add(event_id)
            
    def remove_event(self, event_id: str) -> None:
        """
        Remove an event from the temporal index.
        
        Args:
            event_id: ID of the event to remove
        """
        if event_id not in self.event_timestamps:
            return
            
        timestamp = self.event_timestamps[event_id]
        
        # Remove from each resolution level
        for level in range(min(self.resolution_levels, len(self.resolutions))):
            resolution = self.resolutions[level]
            time_bin = int(timestamp / resolution)
            if event_id in self.time_indices[level][time_bin]:
                self.time_indices[level][time_bin].remove(event_id)
                # Clean up empty bins
                if not self.time_indices[level][time_bin]:
                    del self.time_indices[level][time_bin]
                    
        # Remove from timestamp mapping
        del self.event_timestamps[event_id]
        
    def get_events_in_range(self, start_time: float, end_time: float) -> Set[str]:
        """
        Get events that occurred within the specified time range.
        
        Args:
            start_time: Start timestamp of the range
            end_time: End timestamp of the range
            
        Returns:
            Set of event IDs within the time range
        """
        # Find the appropriate resolution level based on the range size
        range_size = end_time - start_time
        
        for level in range(min(self.resolution_levels, len(self.resolutions))):
            if range_size <= self.resolutions[level] * 100:  # If range is <= 100 bins of this resolution
                # Use this resolution level
                return self._get_events_in_range_at_level(start_time, end_time, level)
                
        # If range is too large for any level, use the highest level
        return self._get_events_in_range_at_level(start_time, end_time, self.resolution_levels - 1)
        
    def _get_events_in_range_at_level(self, start_time: float, end_time: float, level: int) -> Set[str]:
        """
        Get events within time range at a specific resolution level.
        
        Args:
            start_time: Start timestamp of the range
            end_time: End timestamp of the range
            level: Resolution level to use
            
        Returns:
            Set of event IDs within the time range
        """
        resolution = self.resolutions[level]
        start_bin = int(start_time / resolution)
        end_bin = int(end_time / resolution)
        
        # Gather all events in the relevant bins
        candidates = set()
        for bin_idx in range(start_bin, end_bin + 1):
            candidates.update(self.time_indices[level].get(bin_idx, set()))
            
        # Filter to ensure events are actually within the range
        result = set()
        for event_id in candidates:
            timestamp = self.event_timestamps.get(event_id)
            if timestamp is not None and start_time <= timestamp <= end_time:
                result.add(event_id)
                
        return result
        
    def get_events_before(self, reference_time: float, max_events: int = 10) -> List[str]:
        """
        Get events that occurred before the reference time.
        
        Args:
            reference_time: Reference timestamp
            max_events: Maximum number of events to return
            
        Returns:
            List of event IDs occurring before the reference time, 
            ordered from most to least recent
        """
        # Create a list of (timestamp, event_id) for events before reference_time
        candidates = []
        for event_id, timestamp in self.event_timestamps.items():
            if timestamp < reference_time:
                candidates.append((timestamp, event_id))
                
        # Sort by timestamp (most recent first) and take the top max_events
        candidates.sort(reverse=True)
        return [event_id for _, event_id in candidates[:max_events]]
        
    def get_events_after(self, reference_time: float, max_events: int = 10) -> List[str]:
        """
        Get events that occurred after the reference time.
        
        Args:
            reference_time: Reference timestamp
            max_events: Maximum number of events to return
            
        Returns:
            List of event IDs occurring after the reference time,
            ordered from earliest to latest
        """
        # Create a list of (timestamp, event_id) for events after reference_time
        candidates = []
        for event_id, timestamp in self.event_timestamps.items():
            if timestamp > reference_time:
                candidates.append((timestamp, event_id))
                
        # Sort by timestamp (earliest first) and take the top max_events
        candidates.sort()
        return [event_id for _, event_id in candidates[:max_events]]
        
    def get_nearest_events(self, reference_time: float, max_events: int = 10) -> List[Tuple[str, float]]:
        """
        Get events nearest in time to the reference time.
        
        Args:
            reference_time: Reference timestamp
            max_events: Maximum number of events to return
            
        Returns:
            List of (event_id, distance) tuples ordered by temporal proximity
        """
        # Calculate temporal distance for all events
        distance_events = []
        for event_id, timestamp in self.event_timestamps.items():
            distance = abs(timestamp - reference_time)
            distance_events.append((distance, event_id))
            
        # Sort by distance and take the top max_events
        distance_events.sort()
        return [(event_id, distance) for distance, event_id in distance_events[:max_events]]
        
    def get_event_count(self) -> int:
        """
        Get the total number of events in the index.
        
        Returns:
            Count of indexed events
        """
        return len(self.event_timestamps)


class ConceptualIndex:
    """
    Indexes memories based on conceptual content for semantic retrieval.
    
    Supports both FAISS (for high performance) and fallback to scipy for
    environments where FAISS is not available.
    """
    
    def __init__(self, embedding_dim: int = 768, use_gpu: bool = False):
        """
        Initialize conceptual index with specified embedding dimension.
        
        Args:
            embedding_dim: Dimension of memory embeddings
            use_gpu: Whether to use GPU for similarity search (if available)
        """
        self.embedding_dim = embedding_dim
        self.use_gpu = use_gpu and TORCH_AVAILABLE and FAISS_AVAILABLE
        
        # Mapping from position in index to event ID
        self.idx_to_id = []
        # Mapping from event ID to position in index
        self.id_to_idx = {}
        
        # Initialize index based on available libraries
        if FAISS_AVAILABLE:
            self._init_faiss_index()
        else:
            self._init_scipy_index()
        
    def _init_faiss_index(self) -> None:
        """Initialize FAISS index for similarity search."""
        # Initialize FAISS index based on available resources
        if self.use_gpu:
            # GPU index
            res = faiss.StandardGpuResources()
            config = faiss.GpuIndexFlatConfig()
            config.device = 0  # Use first GPU
            self.index = faiss.GpuIndexFlatIP(res, self.embedding_dim, config)
            self.using_faiss = True
            logger.info("Using FAISS GPU index for similarity search")
        else:
            # CPU index - using inner product (cosine similarity for normalized vectors)
            self.index = faiss.IndexFlatIP(self.embedding_dim)
            self.using_faiss = True
            logger.info("Using FAISS CPU index for similarity search")
    
    def _init_scipy_index(self) -> None:
        """Initialize scipy-based fallback for similarity search."""
        # Store embeddings in a list
        self.embeddings = []
        self.using_faiss = False
        logger.info("Using scipy-based similarity search (FAISS not available)")
        
    def add_event(self, event_id: str, embedding: np.ndarray) -> None:
        """
        Add an event to the conceptual index.
        
        Args:
            event_id: ID of the event to add
            embedding: Embedding vector of the event
        """
        # Check if event already exists
        if event_id in self.id_to_idx:
            self.remove_event(event_id)
            
        # Ensure embedding is correct shape and type
        embedding = np.array(embedding, dtype=np.float32).reshape(1, -1)
        
        # Check dimension
        if embedding.shape[1] != self.embedding_dim:
            raise ValueError(f"Embedding dimension mismatch: expected {self.embedding_dim}, "
                           f"got {embedding.shape[1]}")
        
        # Add to appropriate index
        if self.using_faiss:
            # Add to FAISS index
            self.index.add(embedding)
            
            # Update mappings
            idx = len(self.idx_to_id)
            self.idx_to_id.append(event_id)
            self.id_to_idx[event_id] = idx
        else:
            # Add to scipy-based index
            self.embeddings.append(embedding.flatten())
            
            # Update mappings
            idx = len(self.idx_to_id)
            self.idx_to_id.append(event_id)
            self.id_to_idx[event_id] = idx
        
    def remove_event(self, event_id: str) -> None:
        """
        Remove an event from the conceptual index.
        
        Args:
            event_id: ID of the event to remove
        """
        if event_id not in self.id_to_idx:
            return
        
        if self.using_faiss:
            # FAISS doesn't support direct removal, so we rebuild the index
            idx_to_remove = self.id_to_idx[event_id]
            keep_indices = [i for i in range(len(self.idx_to_id)) if i != idx_to_remove]
            
            if not keep_indices:
                # If removing the only event, just reset the index
                if self.use_gpu:
                    self._init_faiss_index()
                else:
                    self.index = faiss.IndexFlatIP(self.embedding_dim)
                self.idx_to_id = []
                self.id_to_idx = {}
                return
                
            # Get vectors to keep
            vectors = self.index.reconstruct_batch(keep_indices)
            
            # Create new index
            if self.use_gpu:
                res = faiss.StandardGpuResources()
                config = faiss.GpuIndexFlatConfig()
                config.device = 0
                new_index = faiss.GpuIndexFlatIP(res, self.embedding_dim, config)
            else:
                new_index = faiss.IndexFlatIP(self.embedding_dim)
                
            # Add vectors to new index
            new_index.add(vectors)
            
            # Update mappings
            new_idx_to_id = []
            new_id_to_idx = {}
            
            for i, old_idx in enumerate(keep_indices):
                event_id = self.idx_to_id[old_idx]
                new_idx_to_id.append(event_id)
                new_id_to_idx[event_id] = i
                
            # Replace old index and mappings
            self.index = new_index
            self.idx_to_id = new_idx_to_id
            self.id_to_idx = new_id_to_idx
        else:
            # Remove from scipy-based index
            idx_to_remove = self.id_to_idx[event_id]
            
            # Remove embedding
            self.embeddings.pop(idx_to_remove)
            
            # Update mappings
            self.idx_to_id.pop(idx_to_remove)
            del self.id_to_idx[event_id]
            
            # Update indices for events after the removed one
            for i, eid in enumerate(self.idx_to_id):
                self.id_to_idx[eid] = i
        
    def search(self, query_embedding: np.ndarray, k: int = 10) -> List[Tuple[str, float]]:
        """
        Search for most similar events to the query embedding.
        
        Args:
            query_embedding: Query embedding vector
            k: Number of results to return
            
        Returns:
            List of (event_id, similarity) tuples
        """
        if len(self.idx_to_id) == 0:
            return []  # No events in index
            
        # Ensure query embedding is correct shape and type
        query_embedding = np.array(query_embedding, dtype=np.float32).reshape(1, -1)
        
        # Check dimension
        if query_embedding.shape[1] != self.embedding_dim:
            raise ValueError(f"Query embedding dimension mismatch: expected {self.embedding_dim}, "
                           f"got {query_embedding.shape[1]}")
            
        # Limit k to number of indexed events
        k = min(k, len(self.idx_to_id))
        
        if self.using_faiss:
            # Search FAISS index
            similarities, indices = self.index.search(query_embedding, k)
            
            # Convert to (event_id, similarity) tuples
            results = []
            for i in range(similarities.shape[1]):
                idx = indices[0, i]
                sim = similarities[0, i]
                event_id = self.idx_to_id[idx]
                results.append((event_id, float(sim)))
        else:
            # Search scipy-based index
            query = query_embedding.flatten()
            
            # Compute similarities
            similarities = []
            for i, emb in enumerate(self.embeddings):
                # Compute cosine similarity
                sim = np.dot(query, emb) / (np.linalg.norm(query) * np.linalg.norm(emb))
                similarities.append((sim, i))
                
            # Sort by similarity (descending)
            similarities.sort(reverse=True)
            
            # Convert to (event_id, similarity) tuples
            results = []
            for sim, idx in similarities[:k]:
                event_id = self.idx_to_id[idx]
                results.append((event_id, float(sim)))
                
        return results
        
    def get_events_by_similarity(self, reference_event_id: str, k: int = 10) -> List[Tuple[str, float]]:
        """
        Get events most similar to a reference event.
        
        Args:
            reference_event_id: ID of the reference event
            k: Number of results to return
            
        Returns:
            List of (event_id, similarity) tuples
        """
        if reference_event_id not in self.id_to_idx:
            return []  # Reference event not found
            
        # Get reference embedding
        if self.using_faiss:
            idx = self.id_to_idx[reference_event_id]
            reference_embedding = self.index.reconstruct(idx)
        else:
            idx = self.id_to_idx[reference_event_id]
            reference_embedding = self.embeddings[idx]
            
        # Search for similar events
        results = self.search(reference_embedding, k + 1)  # +1 because reference will be included
        
        # Filter out the reference event itself
        results = [(event_id, sim) for event_id, sim in results if event_id != reference_event_id]
        
        return results[:k]  # Limit to k results
        
    def get_centroid_event(self, event_ids: List[str]) -> Optional[str]:
        """
        Find the event closest to the centroid of a group of events.
        
        Args:
            event_ids: List of event IDs to consider
            
        Returns:
            ID of the event closest to the centroid, or None if no events are valid
        """
        # Filter to events that exist in the index
        valid_ids = [eid for eid in event_ids if eid in self.id_to_idx]
        
        if not valid_ids:
            return None
            
        # Get embeddings for all valid events
        embeddings = []
        
        if self.using_faiss:
            for event_id in valid_ids:
                idx = self.id_to_idx[event_id]
                embedding = self.index.reconstruct(idx)
                embeddings.append(embedding)
        else:
            for event_id in valid_ids:
                idx = self.id_to_idx[event_id]
                embeddings.append(self.embeddings[idx])
                
        # Calculate centroid
        centroid = np.mean(embeddings, axis=0).reshape(1, -1)
        
        # Find closest event to centroid
        if self.using_faiss:
            similarities, indices = self.index.search(centroid, 1)
            closest_idx = indices[0, 0]
            return self.idx_to_id[closest_idx]
        else:
            # Compute distances to centroid
            centroid_flat = centroid.flatten()
            distances = []
            
            for i, embedding in enumerate(self.embeddings):
                # Compute cosine similarity
                sim = np.dot(centroid_flat, embedding) / (np.linalg.norm(centroid_flat) * np.linalg.norm(embedding))
                distances.append((1 - sim, i))  # Convert to distance
                
            # Find closest
            distances.sort()  # Sort by distance (ascending)
            closest_idx = distances[0][1]
            
            return self.idx_to_id[closest_idx]
        
    def get_event_count(self) -> int:
        """
        Get the total number of events in the index.
        
        Returns:
            Count of indexed events
        """
        return len(self.idx_to_id)


class SpatialIndex:
    """
    Indexes memories based on spatial relationships.
    """
    
    def __init__(self, dimensions: int = 2):
        """
        Initialize spatial index with specified number of dimensions.
        
        Args:
            dimensions: Number of spatial dimensions to index
        """
        self.dimensions = dimensions
        # Maps from location key to event IDs
        self.location_index = defaultdict(set)
        # Maps from region key to event IDs
        self.region_index = defaultdict(set)
        # Maps from event ID to location
        self.event_locations = {}
        
    def add_event(self, event_id: str, location: Union[Tuple, List, Dict]) -> None:
        """
        Add an event to the spatial index.
        
        Args:
            event_id: ID of the event to add
            location: Spatial location data (coordinates, region, etc.)
        """
        if isinstance(location, (tuple, list)):
            # Coordinate-based location
            # Convert to tuple for hashability
            coords = tuple(location[:self.dimensions])
            self.event_locations[event_id] = {'coordinates': coords}
            
            # Add to location index (exact match)
            self.location_index[coords].add(event_id)
            
            # Add to grid cells for approximate search
            grid_cells = self._get_grid_cells(coords)
            for cell in grid_cells:
                self.region_index[cell].add(event_id)
                
        elif isinstance(location, dict):
            # Dictionary-based location with named fields
            self.event_locations[event_id] = location.copy()
            
            # Add to appropriate indices based on available location data
            if 'coordinates' in location:
                coords = tuple(location['coordinates'][:self.dimensions])
                self.location_index[coords].add(event_id)
                
                # Add to grid cells for approximate search
                grid_cells = self._get_grid_cells(coords)
                for cell in grid_cells:
                    self.region_index[cell].add(event_id)
                    
            if 'region' in location:
                region = location['region']
                if isinstance(region, (list, tuple)):
                    # Multiple regions
                    for r in region:
                        self.region_index[('region', r)].add(event_id)
                else:
                    # Single region
                    self.region_index[('region', region)].add(event_id)
                    
            if 'place' in location:
                place = location['place']
                self.region_index[('place', place)].add(event_id)
                
            if 'address' in location:
                address = location['address']
                self.region_index[('address', address)].add(event_id)
        else:
            # Unsupported location format
            logger.warning(f"Unsupported location format: {type(location)}")
            
    def remove_event(self, event_id: str) -> None:
        """
        Remove an event from the spatial index.
        
        Args:
            event_id: ID of the event to remove
        """
        if event_id not in self.event_locations:
            return
            
        location = self.event_locations[event_id]
        
        # Remove from location index
        if 'coordinates' in location:
            coords = tuple(location['coordinates'][:self.dimensions])
            if coords in self.location_index and event_id in self.location_index[coords]:
                self.location_index[coords].remove(event_id)
                # Clean up empty sets
                if not self.location_index[coords]:
                    del self.location_index[coords]
                    
            # Remove from grid cells
            grid_cells = self._get_grid_cells(coords)
            for cell in grid_cells:
                if cell in self.region_index and event_id in self.region_index[cell]:
                    self.region_index[cell].remove(event_id)
                    # Clean up empty sets
                    if not self.region_index[cell]:
                        del self.region_index[cell]
                        
        # Remove from region index
        if 'region' in location:
            region = location['region']
            if isinstance(region, (list, tuple)):
                # Multiple regions
                for r in region:
                    key = ('region', r)
                    if key in self.region_index and event_id in self.region_index[key]:
                        self.region_index[key].remove(event_id)
                        # Clean up empty sets
                        if not self.region_index[key]:
                            del self.region_index[key]
            else:
                # Single region
                key = ('region', region)
                if key in self.region_index and event_id in self.region_index[key]:
                    self.region_index[key].remove(event_id)
                    # Clean up empty sets
                    if not self.region_index[key]:
                        del self.region_index[key]
                        
        # Remove from other indices
        for field in ['place', 'address']:
            if field in location:
                key = (field, location[field])
                if key in self.region_index and event_id in self.region_index[key]:
                    self.region_index[key].remove(event_id)
                    # Clean up empty sets
                    if not self.region_index[key]:
                        del self.region_index[key]
                        
        # Remove from event locations
        del self.event_locations[event_id]
        
    def _get_grid_cells(self, coords: Tuple) -> List[Tuple]:
        """
        Get grid cells that a location belongs to, for approximate search.
        
        Args:
            coords: Coordinates as tuple
            
        Returns:
            List of grid cell identifiers
        """
        # Use a multi-resolution grid for efficient spatial queries
        # Define grid cell sizes
        cell_sizes = [0.001, 0.01, 0.1, 1.0, 10.0]  # Multiple resolution levels
        
        cells = []
        for size in cell_sizes:
            # Calculate grid cell at this resolution
            cell = tuple(('grid', size, int(c / size)) for c in coords)
            cells.append(cell)
            
        return cells
        
    def get_events_at_location(self, location: Union[Tuple, List], radius: float = 0.0) -> Set[str]:
        """
        Get events that occurred at or near the specified location.
        
        Args:
            location: Target location coordinates
            radius: Search radius around the location
            
        Returns:
            Set of event IDs at or near the location
        """
        # Convert to tuple for hashability
        coords = tuple(location[:self.dimensions])
        
        if radius <= 0:
            # Exact match
            return self.location_index.get(coords, set()).copy()
            
        # Approximate search using grid cells
        candidates = set()
        grid_cells = self._get_grid_cells(coords)
        
        # Find the best grid resolution based on radius
        best_size_idx = 0
        for i, cell in enumerate(grid_cells):
            size = cell[0][1]  # Extract grid cell size
            if size <= radius:
                best_size_idx = i
                break
                
        # Get events from the best grid cell and its neighbors
        best_cell = grid_cells[best_size_idx]
        size = best_cell[0][1]  # Extract grid cell size
        
        # Generate neighbor cells
        neighbor_offsets = []
        for d in range(self.dimensions):
            for offset in [-1, 0, 1]:
                if offset == 0:
                    continue
                neighbor = list(best_cell)
                cell_id = list(neighbor[d])
                cell_id[2] += offset  # Modify grid index for this dimension
                neighbor[d] = tuple(cell_id)
                neighbor_offsets.append(tuple(neighbor))
                
        # Add events from the best cell and neighbors
        candidates.update(self.region_index.get(best_cell, set()))
        for neighbor in neighbor_offsets:
            candidates.update(self.region_index.get(neighbor, set()))
            
        # Filter candidates by actual distance
        result = set()
        for event_id in candidates:
            if event_id in self.event_locations:
                event_location = self.event_locations[event_id]
                if 'coordinates' in event_location:
                    event_coords = event_location['coordinates']
                    # Calculate Euclidean distance
                    dist = np.sqrt(sum((c1 - c2) ** 2 for c1, c2 
                                     in zip(coords, event_coords[:self.dimensions])))
                    if dist <= radius:
                        result.add(event_id)
                        
        return result
        
    def get_events_in_region(self, region: str) -> Set[str]:
        """
        Get events that occurred in the specified region.
        
        Args:
            region: Target region name
            
        Returns:
            Set of event IDs in the region
        """
        return self.region_index.get(('region', region), set()).copy()
        
    def get_events_at_place(self, place: str) -> Set[str]:
        """
        Get events that occurred at the specified place.
        
        Args:
            place: Target place name
            
        Returns:
            Set of event IDs at the place
        """
        return self.region_index.get(('place', place), set()).copy()
        
    def get_nearest_events(self, location: Union[Tuple, List], max_events: int = 10) -> List[Tuple[str, float]]:
        """
        Get events nearest to the specified location.
        
        Args:
            location: Target location coordinates
            max_events: Maximum number of events to return
            
        Returns:
            List of (event_id, distance) tuples ordered by proximity
        """
        # Convert to tuple for consistency
        coords = tuple(location[:self.dimensions])
        
        # Calculate distance for all events with coordinates
        distance_events = []
        for event_id, event_location in self.event_locations.items():
            if 'coordinates' in event_location:
                event_coords = event_location['coordinates']
                # Calculate Euclidean distance
                dist = np.sqrt(sum((c1 - c2) ** 2 for c1, c2 
                                 in zip(coords, event_coords[:self.dimensions])))
                distance_events.append((dist, event_id))
                
        # Sort by distance and take the top max_events
        distance_events.sort()
        return [(event_id, dist) for dist, event_id in distance_events[:max_events]]
        
    def get_event_count(self) -> int:
        """
        Get the total number of events in the index.
        
        Returns:
            Count of indexed events
        """
        return len(self.event_locations)


class MemoryConsolidation:
    """
    Handles memory consolidation and restructuring.
    
    Implements mechanisms inspired by the complementary learning systems theory
    of hippocampal-neocortical interaction.
    
    Attributes:
        memory_selection_threshold (float): Threshold for selecting memories to consolidate
        memory_compression_rate (float): Rate at which memories are compressed
        context_shift_rate (float): Rate at which context evolves during consolidation
        spatial_generalization_factor (float): Factor for generalizing spatial information
        consolidation_rate (float): Rate of memory consolidation (0-1)
        forgetting_rate (float): Rate of forgetting for inactive memories (0-1)
        trace_formation_threshold (float): Threshold for forming memory traces
    """
    
    def __init__(self,
                 memory_selection_threshold: float = 0.6,
                 memory_compression_rate: float = 0.8,
                 context_shift_rate: float = 0.05,
                 spatial_generalization_factor: float = 0.3,
                 consolidation_rate: float = 0.1,
                 forgetting_rate: float = 0.01,
                 trace_formation_threshold: float = 0.6):
        """
        Initialize memory consolidation.
        
        Args:
            memory_selection_threshold (float): Threshold for selecting memories to consolidate
            memory_compression_rate (float): Rate at which memories are compressed
            context_shift_rate (float): Rate at which context evolves during consolidation
            spatial_generalization_factor (float): Factor for generalizing spatial information
            consolidation_rate (float): Rate of memory consolidation (0-1)
            forgetting_rate (float): Rate of forgetting for inactive memories (0-1)
            trace_formation_threshold (float): Threshold for forming memory traces
        """
        self.memory_selection_threshold = memory_selection_threshold
        self.memory_compression_rate = memory_compression_rate
        self.context_shift_rate = context_shift_rate
        self.spatial_generalization_factor = spatial_generalization_factor
        self.consolidation_rate = consolidation_rate
        self.forgetting_rate = forgetting_rate
        self.trace_formation_threshold = trace_formation_threshold
    
    def select_memories_for_consolidation(self, 
                                         episodes: Dict[str, EpisodicEvent],
                                         max_to_select: int = 100) -> List[str]:
        """
        Select memories for consolidation based on their properties.
        
        Args:
            episodes (Dict[str, EpisodicEvent]): All episodic memories
            max_to_select (int): Maximum number of memories to select
            
        Returns:
            List[str]: IDs of selected memories
        """
        # Calculate consolidation scores for each memory
        consolidation_scores = []
        
        for memory_id, memory in episodes.items():
            # Compute score based on memory factors
            score = self._compute_consolidation_score(memory)
            
            # Only consider memories above threshold
            if score >= self.memory_selection_threshold:
                consolidation_scores.append((memory_id, score))
        
        # Sort by consolidation score (descending)
        consolidation_scores.sort(key=lambda x: x[1], reverse=True)
        
        # Return top memories (up to max_to_select)
        return [memory_id for memory_id, _ in consolidation_scores[:max_to_select]]
    
    def _compute_consolidation_score(self, memory: EpisodicEvent) -> float:
        """
        Compute consolidation score for a memory.
        
        Args:
            memory (EpisodicEvent): Memory to evaluate
            
        Returns:
            float: Consolidation score (0-1)
        """
        # Start with base strength
        score = memory.memory_strength
        
        # Adjust for salience/importance
        score = 0.7 * score + 0.3 * memory.salience
        
        # Adjust for retrieval count (memories accessed more frequently get higher priority)
        retrieval_factor = min(1.0, memory.retrieval_count / 5)  # Cap at 5 retrievals
        score = 0.8 * score + 0.2 * retrieval_factor
        
        # Adjust for recency (newer memories get higher priority)
        age_hours = (pd.Timestamp.now() - memory.created_at).total_seconds() / 3600
        
        # 24-hour optimum, with decay for very new and very old memories
        if age_hours < 4:  # Very recent, not ready for full consolidation
            recency_factor = age_hours / 4
        elif age_hours < 24:  # Optimal time window
            recency_factor = 1.0
        else:  # Older memories
            recency_factor = np.exp(-(age_hours - 24) / 168)  # 1-week decay
            
        score = 0.7 * score + 0.3 * recency_factor
        
        return score
    
    def consolidate_memories(self, 
                            episodes: Dict[str, EpisodicEvent],
                            selected_ids: List[str]) -> Tuple[Dict[str, EpisodicEvent], List[str]]:
        """
        Consolidate selected memories.
        
        Args:
            episodes (Dict[str, EpisodicEvent]): All episodic memories
            selected_ids (List[str]): IDs of memories to consolidate
            
        Returns:
            Tuple[Dict[str, EpisodicEvent], List[str]]: Consolidated memories and their IDs
        """
        if not selected_ids:
            return {}, []
            
        # Group selected memories by temporal proximity
        memory_clusters = self._cluster_by_time(
            {memory_id: episodes[memory_id] for memory_id in selected_ids}
        )
        
        # Process each cluster
        consolidated_memories = {}
        consolidated_ids = []
        
        for cluster in memory_clusters:
            if len(cluster) == 1:
                # Single memory, just strengthen it
                memory_id = cluster[0]
                episodes[memory_id].memory_strength = min(1.0, episodes[memory_id].memory_strength * 1.1)
                episodes[memory_id].modified_at = pd.Timestamp.now()
                consolidated_ids.append(memory_id)
            else:
                # Multiple memories, create a consolidated version
                consolidated_memory = self._create_consolidated_memory(
                    {memory_id: episodes[memory_id] for memory_id in cluster}
                )
                consolidated_memories[consolidated_memory.id] = consolidated_memory
                consolidated_ids.extend(cluster)
        
        # Return consolidated memories and IDs
        return consolidated_memories, consolidated_ids
    
    def _cluster_by_time(self, 
                        selected_memories: Dict[str, EpisodicEvent],
                        max_time_diff_hours: float = 12.0) -> List[List[str]]:
        """
        Cluster memories by temporal proximity.
        
        Args:
            selected_memories (Dict[str, EpisodicEvent]): Memories to cluster
            max_time_diff_hours (float): Maximum time difference for clustering
            
        Returns:
            List[List[str]]: Clusters of memory IDs
        """
        # Sort memories by timestamp
        sorted_memories = sorted(
            selected_memories.items(),
            key=lambda x: x[1].temporal_context.timestamp
        )
        
        # Initialize clusters
        clusters = []
        current_cluster = []
        
        for memory_id, memory in sorted_memories:
            if not current_cluster:
                # Start new cluster
                current_cluster.append(memory_id)
            else:
                # Get last memory in current cluster
                last_memory = selected_memories[current_cluster[-1]]
                
                # Check time difference
                time_diff = (memory.temporal_context.timestamp - 
                            last_memory.temporal_context.timestamp).total_seconds() / 3600
                
                if time_diff <= max_time_diff_hours:
                    # Add to current cluster
                    current_cluster.append(memory_id)
                else:
                    # Start new cluster
                    clusters.append(current_cluster)
                    current_cluster = [memory_id]
        
        # Add the last cluster if not empty
        if current_cluster:
            clusters.append(current_cluster)
        
        return clusters
    
    def _create_consolidated_memory(self, 
                                  memories: Dict[str, EpisodicEvent]) -> EpisodicEvent:
        """
        Create a consolidated memory from multiple episodic events.
        
        Args:
            memories (Dict[str, EpisodicEvent]): Memories to consolidate
            
        Returns:
            EpisodicEvent: Consolidated memory
        """
        if not memories:
            return None
        
        memory_list = list(memories.values())
        
        # Sort by timestamp
        memory_list.sort(key=lambda x: x.temporal_context.timestamp)
        
        # Find temporal bounds
        start_time = memory_list[0].temporal_context.timestamp
        end_time = memory_list[-1].temporal_context.timestamp
        
        # Compute duration
        duration = end_time - start_time + memory_list[-1].temporal_context.duration
        
        # Compute average embedding weighted by salience
        total_embedding = np.zeros_like(memory_list[0].embedding)
        total_weight = 0
        
        for memory in memory_list:
            weight = memory.salience * memory.memory_strength
            total_embedding += weight * memory.embedding
            total_weight += weight
        
        if total_weight > 0:
            avg_embedding = total_embedding / total_weight
        else:
            avg_embedding = total_embedding
        
        # Normalize the embedding
        if np.linalg.norm(avg_embedding) > 0:
            avg_embedding = avg_embedding / np.linalg.norm(avg_embedding)
        
        # Create temporal context for consolidated memory
        consolidated_temporal = TemporalContext(
            timestamp=start_time,
            duration=duration,
            context_dim=memory_list[0].temporal_context.context_vector.shape[0]
        )
        
        # Set context vector to merged vector
        consolidated_temporal.context_vector = self._merge_context_vectors(memory_list)
        
        # Create consolidated memory
        consolidated = EpisodicEvent(
            content=[memory.content for memory in memory_list],
            temporal_context=consolidated_temporal,
            event_type="consolidated"
        )
        
        # Update embedding
        consolidated.update_embedding(avg_embedding)
        
        # Set contexts
        consolidated.spatial_context = self._merge_spatial_contexts(memory_list)
        consolidated.emotional_context = self._merge_emotional_contexts(memory_list)
        consolidated.perceptual_context = self._merge_perceptual_contexts(memory_list)
        consolidated.social_context = self._merge_social_contexts(memory_list)
        
        # Set metadata
        consolidated.salience = max(memory.salience for memory in memory_list)
        consolidated.importance = consolidated.salience
        consolidated.memory_strength = np.mean([memory.memory_strength for memory in memory_list]) * 1.2
        consolidated.tags = list(set(sum([memory.tags for memory in memory_list], [])))
        consolidated.metadata = {
            'source_ids': list(memories.keys()),
            'consolidation_time': pd.Timestamp.now().isoformat(),
            'memory_count': len(memories)
        }
        
        # Set associations (union of all memory associations)
        for memory in memory_list:
            for assoc_id in memory.associations:
                consolidated.add_association(assoc_id)
                
        return consolidated
    
    def _merge_context_vectors(self, memories: List[EpisodicEvent]) -> np.ndarray:
        """
        Merge temporal context vectors from multiple memories.
        
        Args:
            memories (List[EpisodicEvent]): Memories to merge
            
        Returns:
            np.ndarray: Merged context vector
        """
        if not memories:
            return None
        
        # Extract context vectors
        context_vectors = [memory.temporal_context.context_vector for memory in memories]
        
        # Weight by memory strength and salience
        weights = [memory.memory_strength * memory.salience for memory in memories]
        
        # Calculate weighted average
        merged_vector = np.zeros_like(context_vectors[0])
        for i, vector in enumerate(context_vectors):
            merged_vector += weights[i] * vector
        
        # Normalize
        if np.linalg.norm(merged_vector) > 0:
            merged_vector = merged_vector / np.linalg.norm(merged_vector)
        
        return merged_vector
    
    def _merge_spatial_contexts(self, memories: List[EpisodicEvent]) -> Dict[str, Any]:
        """
        Merge spatial contexts from multiple memories.
        
        Args:
            memories (List[EpisodicEvent]): Memories to merge
            
        Returns:
            Dict[str, Any]: Merged spatial context
        """
        if not memories:
            return {}
        
        # Extract spatial contexts
        spatial_contexts = [memory.spatial_context for memory in memories if memory.spatial_context]
        
        if not spatial_contexts:
            return {}
        
        # Find the most common location
        location_counts = defaultdict(float)
        
        for i, context in enumerate(spatial_contexts):
            if 'location' in context:
                memory = memories[i]
                weight = memory.memory_strength * memory.salience
                location_counts[context['location']] += weight
        
        # Select the most weighted location
        merged_context = {}
        if location_counts:
            merged_context['location'] = max(location_counts.items(), key=lambda x: x[1])[0]
        
        # Merge other spatial attributes
        for attr in ['coordinates', 'region', 'place', 'environment', 'landmarks']:
            values = [context.get(attr) for context in spatial_contexts if attr in context]
            if values:
                # For simplicity, take the most common value
                merged_context[attr] = max(set(values), key=values.count)
        
        return merged_context
    
    def _merge_emotional_contexts(self, memories: List[EpisodicEvent]) -> Dict[str, Any]:
        """
        Merge emotional contexts from multiple memories.
        
        Args:
            memories (List[EpisodicEvent]): Memories to merge
            
        Returns:
            Dict[str, Any]: Merged emotional context
        """
        if not memories:
            return {}
        
        # Extract emotional contexts
        emotional_contexts = [memory.emotional_context for memory in memories if memory.emotional_context]
        
        if not emotional_contexts:
            return {}
        
        # Handle valence and arousal numerically if present
        valence_values = [
            context.get('valence') for context in emotional_contexts 
            if 'valence' in context and isinstance(context['valence'], (int, float))
        ]
        
        arousal_values = [
            context.get('arousal') for context in emotional_contexts 
            if 'arousal' in context and isinstance(context['arousal'], (int, float))
        ]
        
        merged_context = {}
        
        if valence_values:
            merged_context['valence'] = sum(valence_values) / len(valence_values)
            
        if arousal_values:
            merged_context['arousal'] = sum(arousal_values) / len(arousal_values)
        
        # For emotions, aggregate the occurrences
        emotion_counts = defaultdict(int)
        for context in emotional_contexts:
            if 'emotions' in context and isinstance(context['emotions'], list):
                for emotion in context['emotions']:
                    emotion_counts[emotion] += 1
        
        # Keep the top 3 emotions
        if emotion_counts:
            top_emotions = heapq.nlargest(3, emotion_counts.items(), key=lambda x: x[1])
            merged_context['emotions'] = [emotion for emotion, _ in top_emotions]
        
        return merged_context
    
    def _merge_perceptual_contexts(self, memories: List[EpisodicEvent]) -> Dict[str, Any]:
        """
        Merge perceptual contexts from multiple memories.
        
        Args:
            memories (List[EpisodicEvent]): Memories to merge
            
        Returns:
            Dict[str, Any]: Merged perceptual context
        """
        if not memories:
            return {}
        
        # Extract perceptual contexts
        perceptual_contexts = [memory.perceptual_context for memory in memories if memory.perceptual_context]
        
        if not perceptual_contexts:
            return {}
        
        # For simplicity, merge by taking most common values for each attribute
        merged_context = {}
        
        # Find all attributes across all contexts
        all_attrs = set()
        for context in perceptual_contexts:
            all_attrs.update(context.keys())
            
        # Merge each attribute
        for attr in all_attrs:
            values = [context.get(attr) for context in perceptual_contexts if attr in context]
            if values:
                if all(isinstance(v, (int, float)) for v in values):
                    # Numeric values - take average
                    merged_context[attr] = sum(values) / len(values)
                else:
                    # Non-numeric values - take most common
                    merged_context[attr] = max(set(values), key=values.count)
                    
        return merged_context
    
    def _merge_social_contexts(self, memories: List[EpisodicEvent]) -> Dict[str, Any]:
        """
        Merge social contexts from multiple memories.
        
        Args:
            memories (List[EpisodicEvent]): Memories to merge
            
        Returns:
            Dict[str, Any]: Merged social context
        """
        if not memories:
            return {}
        
        # Extract social contexts
        social_contexts = [memory.social_context for memory in memories if memory.social_context]
        
        if not social_contexts:
            return {}
        
        merged_context = {}
        
        # For participants, gather all unique participants
        all_participants = set()
        for context in social_contexts:
            if 'participants' in context and isinstance(context['participants'], list):
                all_participants.update(context['participants'])
                
        if all_participants:
            merged_context['participants'] = list(all_participants)
            
        # For other attributes, take most common values
        all_attrs = set()
        for context in social_contexts:
            all_attrs.update(context.keys())
            
        for attr in all_attrs:
            if attr == 'participants':
                continue  # Already handled
                
            values = [context.get(attr) for context in social_contexts if attr in context]
            if values:
                if all(isinstance(v, (int, float)) for v in values):
                    # Numeric values - take average
                    merged_context[attr] = sum(values) / len(values)
                else:
                    # Non-numeric values - take most common
                    merged_context[attr] = max(set(values), key=values.count)
                    
        return merged_context
    
    def consolidate_memory(self, events: Dict[str, EpisodicEvent], 
                          time_elapsed: float = 1.0) -> Dict[str, EpisodicEvent]:
        """
        Consolidate memories by updating their retention scores based on
        importance and activation.
        
        Args:
            events: Dictionary mapping event IDs to events
            time_elapsed: Time elapsed since last consolidation, in arbitrary units
            
        Returns:
            Updated events dictionary
        """
        # Update retention scores for all events
        for event_id, event in events.items():
            # Retention increases based on importance and activation
            event.update_retention(self.consolidation_rate * time_elapsed)
            
        return events
        
    def apply_forgetting(self, events: Dict[str, EpisodicEvent], 
                        time_elapsed: float = 1.0) -> Dict[str, EpisodicEvent]:
        """
        Apply forgetting to memories based on activation and retention.
        
        Args:
            events: Dictionary mapping event IDs to events
            time_elapsed: Time elapsed since last forgetting, in arbitrary units
            
        Returns:
            Updated events dictionary with forgotten events removed
        """
        # Apply forgetting dynamics: decay activation
        events_to_remove = []
        
        for event_id, event in events.items():
            # Decay activation
            event.apply_forgetting(time_elapsed)
            
            # Calculate forgetting probability
            # Events with high retention and importance are less likely to be forgotten
            forget_probability = (1 - event.retention_score) * (1 - event.activation) * (1 - event.salience)
            forget_probability *= self.forgetting_rate * time_elapsed
            
            # Randomly determine if event should be forgotten
            if np.random.random() < forget_probability:
                events_to_remove.append(event_id)
                
        # Remove forgotten events
        for event_id in events_to_remove:
            events.pop(event_id)
            
        return events
        
    def form_traces(self, events: Dict[str, EpisodicEvent], 
                   existing_traces: Dict[str, EpisodicMemoryTrace]) -> Dict[str, EpisodicMemoryTrace]:
        """
        Form memory traces between related events.
        
        Args:
            events: Dictionary mapping event IDs to events
            existing_traces: Dictionary mapping trace IDs to existing traces
            
        Returns:
            Updated memory traces dictionary
        """
        # Create a copy of existing traces to modify
        traces = existing_traces.copy()
        
        # Get all event IDs
        event_ids = list(events.keys())
        
        # Create a mapping of existing traces (source_id, target_id) for quick lookup
        existing_connections = {
            (trace.source_id, trace.target_id): trace_id
            for trace_id, trace in traces.items()
        }
        
        # Create a mapping of event embeddings for efficient similarity computation
        embeddings = np.array([events[event_id].embedding for event_id in event_ids])
        
        # Compute pairwise similarities
        similarities = cosine_similarity(embeddings)
        
        # Identify potential traces to form
        for i in range(len(event_ids)):
            for j in range(i + 1, len(event_ids)):
                # Get event IDs
                source_id = event_ids[i]
                target_id = event_ids[j]
                
                # Skip if trace already exists
                if (source_id, target_id) in existing_connections or (target_id, source_id) in existing_connections:
                    continue
                    
                # Get similarity
                similarity = similarities[i, j]
                
                # Form trace if similarity exceeds threshold
                if similarity >= self.trace_formation_threshold:
                    # Create trace
                    trace_id = str(uuid.uuid4())
                    trace = EpisodicMemoryTrace(
                        id=trace_id,
                        source_id=source_id,
                        target_id=target_id,
                        relation_type="semantic",
                        strength=similarity,
                        bidirectional=True
                    )
                    
                    # Add to traces
                    traces[trace_id] = trace
                    
        return traces
        
    def strengthen_traces(self, traces: Dict[str, EpisodicMemoryTrace], 
                         co_activated_pairs: List[Tuple[str, str]],
                         reinforcement: float = 0.1) -> Dict[str, EpisodicMemoryTrace]:
        """
        Strengthen memory traces between co-activated events.
        
        Args:
            traces: Dictionary mapping trace IDs to traces
            co_activated_pairs: List of (source_id, target_id) pairs that were co-activated
            reinforcement: Reinforcement strength (0-1)
            
        Returns:
            Updated memory traces dictionary
        """
        # Create a mapping of existing traces (source_id, target_id) for quick lookup
        trace_map = {}
        for trace_id, trace in traces.items():
            trace_map[(trace.source_id, trace.target_id)] = trace_id
            if trace.bidirectional:
                trace_map[(trace.target_id, trace.source_id)] = trace_id
                
        # Strengthen existing traces
        for source_id, target_id in co_activated_pairs:
            # Check if trace exists
            trace_id = trace_map.get((source_id, target_id))
            if trace_id is not None:
                # Strengthen trace
                traces[trace_id].reinforce(reinforcement)
                
        return traces
        
    def decay_traces(self, traces: Dict[str, EpisodicMemoryTrace], 
                    time_elapsed: float = 1.0) -> Dict[str, EpisodicMemoryTrace]:
        """
        Decay memory trace strengths over time.
        
        Args:
            traces: Dictionary mapping trace IDs to traces
            time_elapsed: Time elapsed since last decay, in arbitrary units
            
        Returns:
            Updated memory traces dictionary with weak traces removed
        """
        # Decay trace strengths
        traces_to_remove = []
        
        for trace_id, trace in traces.items():
            # Decay strength
            trace.decay_strength(self.forgetting_rate, time_elapsed)
            
            # Remove very weak traces
            if trace.strength < 0.1:
                traces_to_remove.append(trace_id)
                
        # Remove weak traces
        for trace_id in traces_to_remove:
            traces.pop(trace_id)
            
        return traces


class EpisodicKnowledgeBase:
    """
    Manages episodic knowledge in the ULTRA system.
    
    This class provides storage, retrieval, and consolidation of episodic
    memories. It implements mechanisms inspired by human episodic memory,
    including forgetting, retrieval dynamics, and memory consolidation.
    
    Attributes:
        config (Dict[str, Any]): Configuration parameters
        episodes (Dict[str, EpisodicEvent]): Stored episodic events
        traces (Dict[str, EpisodicMemoryTrace]): Memory traces between events
        temporal_index (TemporalIndex): Index by time
        conceptual_index (ConceptualIndex): Index for similarity search
        spatial_index (SpatialIndex): Index by location
        consolidation (MemoryConsolidation): Memory consolidation handler
        consolidation_timer (threading.Timer): Timer for consolidation
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        Initialize the episodic knowledge base.
        
        Args:
            config (Dict[str, Any], optional): Configuration parameters
        """
        self.config = config or {}
        
        # Set default configuration values
        self.config.setdefault('embedding_dim', 512)
        self.config.setdefault('max_episodes', 100000)
        self.config.setdefault('forgetting_rate', 0.01)
        self.config.setdefault('consolidation_threshold', 0.6)
        self.config.setdefault('consolidation_interval', 3600)  # 1 hour
        self.config.setdefault('similarity_threshold', 0.7)
        self.config.setdefault('use_gpu', False)
        self.config.setdefault('temporal_index_granularity', '1D')  # Daily granularity
        
        # Initialize storage
        self.episodes = {}
        self.traces = {}
        
        # Initialize indices
        self.temporal_index = TemporalIndex()
        self.conceptual_index = ConceptualIndex(
            embedding_dim=self.config['embedding_dim'],
            use_gpu=self.config['use_gpu']
        )
        self.spatial_index = SpatialIndex()
        
        # Initialize consolidation
        self.consolidation = MemoryConsolidation(
            memory_selection_threshold=self.config['consolidation_threshold'],
            forgetting_rate=self.config['forgetting_rate']
        )
        
        # Initialize consolidation timer
        self.consolidation_timer = None
        self._start_consolidation_timer()
        
        # Track last update time
        self.last_update_time = time.time()
        
        logger.info(f"Initialized EpisodicKnowledgeBase with max_episodes={self.config['max_episodes']}")
    
    def add_episode(self, content: Any, metadata: Dict[str, Any] = None) -> str:
        """
        Add a new episodic event.
        
        Args:
            content (Any): The content of the episodic memory
            metadata (Dict[str, Any], optional): Additional metadata
            
        Returns:
            str: ID of the new episode
        """
        metadata = metadata or {}
        
        # Create temporal context if provided
        temporal_context = None
        if 'timestamp' in metadata:
            temporal_context = TemporalContext(
                timestamp=pd.Timestamp(metadata['timestamp']),
                duration=timedelta(seconds=metadata.get('duration', 0))
            )
        
        # Create episodic event
        event = EpisodicEvent(
            content=content,
            temporal_context=temporal_context,
            event_type=metadata.get('event_type', 'general'),
            embedding_dim=self.config['embedding_dim']
        )
        
        # Set contexts
        contexts = {}
        
        # Set spatial context if provided
        if 'location' in metadata or 'coordinates' in metadata or 'region' in metadata:
            spatial_context = {}
            for field in ['location', 'coordinates', 'region', 'place', 'environment', 'landmarks']:
                if field in metadata:
                    spatial_context[field] = metadata[field]
            contexts['spatial'] = spatial_context
        
        # Set emotional context if provided
        if 'emotions' in metadata or 'valence' in metadata or 'arousal' in metadata:
            emotional_context = {}
            for field in ['emotions', 'valence', 'arousal']:
                if field in metadata:
                    emotional_context[field] = metadata[field]
            contexts['emotional'] = emotional_context
        
        # Set perceptual context if provided
        if 'perceptual' in metadata and isinstance(metadata['perceptual'], dict):
            contexts['perceptual'] = metadata['perceptual']
        
        # Set social context if provided
        if 'participants' in metadata or 'social' in metadata:
            social_context = {}
            if 'participants' in metadata:
                social_context['participants'] = metadata['participants']
            if 'social' in metadata and isinstance(metadata['social'], dict):
                social_context.update(metadata['social'])
            contexts['social'] = social_context
        
        # Update contexts
        if contexts:
            event.update_contexts(**contexts)
        
        # Set tags if provided
        if 'tags' in metadata and isinstance(metadata['tags'], list):
            event.tags = metadata['tags']
        
        # Set salience/importance if provided
        if 'salience' in metadata:
            event.salience = metadata['salience']
            event.importance = event.salience
        elif 'importance' in metadata:
            event.importance = metadata['importance']
            event.salience = event.importance
        
        # Generate embedding if provided
        if 'embedding' in metadata:
            event.update_embedding(np.array(metadata['embedding']))
        elif callable(self.config.get('embedding_model')):
            embedding = self.config['embedding_model'](content)
            event.update_embedding(embedding)
        
        # Store the event
        self.episodes[event.id] = event
        
        # Update indices
        self.temporal_index.add_event(event.id, event.timestamp)
        self.conceptual_index.add_event(event.id, event.embedding)
        
        # Index spatial information if available
        if event.spatial_context:
            self.spatial_index.add_event(event.id, event.spatial_context)
        
        # Apply maintenance if needed
        self._apply_maintenance()
        
        # Check if we need to forget memories
        if len(self.episodes) > self.config['max_episodes']:
            self._forget_memories()
        
        logger.debug(f"Added episodic event {event.id}, total episodes: {len(self.episodes)}")
        
        return event.id
    
    def get_episode(self, episode_id: str) -> Optional[EpisodicEvent]:
        """
        Get a specific episode by ID.
        
        Args:
            episode_id (str): ID of the episode
            
        Returns:
            Optional[EpisodicEvent]: The episode if found, None otherwise
        """
        if episode_id in self.episodes:
            event = self.episodes[episode_id]
            event.record_retrieval()
            return event
        
        return None
    
    def get_episode_content(self, episode_id: str) -> Optional[Any]:
        """
        Get the content of a specific episode.
        
        Args:
            episode_id (str): ID of the episode
            
        Returns:
            Optional[Any]: Episode content or None if not found
        """
        event = self.get_episode(episode_id)
        return event.content if event else None
    
    def get_embedding(self, episode_id: str) -> np.ndarray:
        """
        Get the embedding of a specific episode.
        
        Args:
            episode_id (str): ID of the episode
            
        Returns:
            np.ndarray: Episode embedding
        """
        if episode_id in self.episodes:
            return self.episodes[episode_id].embedding
        
        return np.zeros(self.config['embedding_dim'])
    
    def update_episode(self, episode_id: str, content: Any, metadata: Dict[str, Any] = None) -> bool:
        """
        Update an existing episode.
        
        Args:
            episode_id (str): ID of the episode to update
            content (Any): New content
            metadata (Dict[str, Any], optional): Additional metadata
            
        Returns:
            bool: True if update was successful
        """
        if episode_id not in self.episodes:
            return False
        
        metadata = metadata or {}
        event = self.episodes[episode_id]
        
        # Update content
        embedding_func = None
        if callable(self.config.get('embedding_model')):
            embedding_func = self.config['embedding_model']
        
        event.update_content(content, embedding_func)
        
        # Update temporal context if provided
        temporal_context = {}
        if 'timestamp' in metadata:
            temporal_context['timestamp'] = metadata['timestamp']
        if 'duration' in metadata:
            temporal_context['duration'] = metadata['duration']
        
        # Update spatial context if provided
        spatial_context = {}
        if 'location' in metadata or 'coordinates' in metadata or 'region' in metadata:
            for field in ['location', 'coordinates', 'region', 'place', 'environment', 'landmarks']:
                if field in metadata:
                    spatial_context[field] = metadata[field]
        
        # Update emotional context if provided
        emotional_context = {}
        if 'emotions' in metadata or 'valence' in metadata or 'arousal' in metadata:
            for field in ['emotions', 'valence', 'arousal']:
                if field in metadata:
                    emotional_context[field] = metadata[field]
        
        # Update perceptual context if provided
        perceptual_context = {}
        if 'perceptual' in metadata and isinstance(metadata['perceptual'], dict):
            perceptual_context = metadata['perceptual']
        
        # Update social context if provided
        social_context = {}
        if 'participants' in metadata or 'social' in metadata:
            if 'participants' in metadata:
                social_context['participants'] = metadata['participants']
            if 'social' in metadata and isinstance(metadata['social'], dict):
                social_context.update(metadata['social'])
        
        # Apply context updates
        event.update_contexts(
            temporal=temporal_context if temporal_context else None,
            spatial=spatial_context if spatial_context else None,
            emotional=emotional_context if emotional_context else None,
            perceptual=perceptual_context if perceptual_context else None,
            social=social_context if social_context else None
        )
        
        # Update tags if provided
        if 'tags' in metadata and isinstance(metadata['tags'], list):
            event.tags = metadata['tags']
        
        # Update salience/importance if provided
        if 'salience' in metadata:
            event.salience = metadata['salience']
            event.importance = event.salience
        elif 'importance' in metadata:
            event.importance = metadata['importance']
            event.salience = event.importance
        
        # Update embedding if provided
        if 'embedding' in metadata:
            event.update_embedding(np.array(metadata['embedding']))
        
        # Update indices
        if temporal_context:
            self.temporal_index.remove_event(episode_id)
            self.temporal_index.add_event(episode_id, event.timestamp)
        
        self.conceptual_index.remove_event(episode_id)
        self.conceptual_index.add_event(episode_id, event.embedding)
        
        if spatial_context:
            self.spatial_index.remove_event(episode_id)
            self.spatial_index.add_event(episode_id, event.spatial_context)
        
        # Apply maintenance
        self._apply_maintenance()
        
        logger.debug(f"Updated episodic event {episode_id}")
        
        return True
    
    def retrieve(self, 
                query: Union[str, np.ndarray, Dict], 
                context: Optional[MemoryContext] = None,
                top_k: int = 5,
                threshold: float = None) -> List[Tuple[str, float]]:
        """
        Retrieve episodes similar to the query.
        
        Args:
            query (Union[str, np.ndarray, Dict]): Query string, embedding or dict
            context (MemoryContext, optional): Context to constrain the search
            top_k (int): Maximum number of results
            threshold (float, optional): Minimum similarity threshold
            
        Returns:
            List[Tuple[str, float]]: List of (episode_id, similarity) tuples
        """
        # Apply maintenance operations first
        self._apply_maintenance()
        
        threshold = threshold or self.config['similarity_threshold']
        
        # Convert query to embedding
        query_embedding = self._get_query_embedding(query)
        
        # Initialize results
        candidates = set()
        relevance_scores = {}
        
        # Search by embedding similarity
        if query_embedding is not None:
            similar_events = self.conceptual_index.search(query_embedding, top_k * 2)
            for event_id, similarity in similar_events:
                if similarity >= threshold:
                    candidates.add(event_id)
                    relevance_scores[event_id] = similarity
        
        # Apply context filters if provided
        if context is not None:
            # Filter by temporal context
            if 'timestamp' in context.temporal:
                timestamp = context.temporal['timestamp']
                if isinstance(timestamp, (str, pd.Timestamp)):
                    timestamp = pd.Timestamp(timestamp).timestamp()
                
                # Get events around the timestamp
                time_window = context.temporal.get('window', 3600 * 24)  # Default: 1 day
                start_time = timestamp - time_window/2
                end_time = timestamp + time_window/2
                
                time_events = self.temporal_index.get_events_in_range(start_time, end_time)
                
                if candidates:
                    # Intersect with existing candidates
                    candidates &= time_events
                else:
                    # Start with time events if no existing candidates
                    candidates = time_events
            
            # Filter by spatial context
            if context.spatial and 'coordinates' in context.spatial:
                coords = context.spatial['coordinates']
                radius = context.spatial.get('radius', 0.1)  # Default small radius
                
                space_events = self.spatial_index.get_events_at_location(coords, radius)
                
                if candidates:
                    # Intersect with existing candidates
                    candidates &= space_events
                else:
                    # Start with space events if no existing candidates
                    candidates = space_events
            
            # Calculate relevance based on context similarity
            for event_id in list(candidates):
                if event_id in self.episodes:
                    event = self.episodes[event_id]
                    
                    # Create an event context for comparison
                    event_context = MemoryContext(
                        temporal={'timestamp': event.timestamp},
                        spatial=event.spatial_context,
                        emotional=event.emotional_context,
                        perceptual=event.perceptual_context,
                        social=event.social_context,
                        embedding=event.embedding
                    )
                    
                    # Calculate similarity
                    context_similarity = context.similarity(event_context)
                    
                    # Update relevance score
                    if event_id in relevance_scores:
                        # Combine with existing score (70% embedding, 30% context)
                        relevance_scores[event_id] = 0.7 * relevance_scores[event_id] + 0.3 * context_similarity
                    else:
                        relevance_scores[event_id] = context_similarity
        
        # Prepare final results
        results = []
        for event_id in candidates:
            if event_id in self.episodes and event_id in relevance_scores:
                similarity = relevance_scores[event_id]
                if similarity >= threshold:
                    results.append((event_id, similarity))
                    
                    # Update activation for retrieved events
                    self.episodes[event_id].reactivate(0.3 * similarity)
        
        # Sort by similarity (descending)
        results.sort(key=lambda x: x[1], reverse=True)
        
        # Return top results
        return results[:top_k]
    
    def retrieve_events(self, 
                       query: Union[str, np.ndarray, Dict], 
                       context: Optional[MemoryContext] = None,
                       top_k: int = 5,
                       threshold: float = None) -> List[Tuple[EpisodicEvent, float]]:
        """
        Retrieve episodic events similar to the query.
        
        Args:
            query (Union[str, np.ndarray, Dict]): Query string, embedding or dict
            context (MemoryContext, optional): Context to constrain the search
            top_k (int): Maximum number of results
            threshold (float, optional): Minimum similarity threshold
            
        Returns:
            List[Tuple[EpisodicEvent, float]]: List of (event, similarity) tuples
        """
        # Get matching event IDs
        matches = self.retrieve(query, context, top_k, threshold)
        
        # Convert to event objects
        results = []
        for event_id, similarity in matches:
            if event_id in self.episodes:
                results.append((self.episodes[event_id], similarity))
        
        return results
    
    def retrieve_by_time(self, 
                        start_time: Optional[Union[float, pd.Timestamp]] = None,
                        end_time: Optional[Union[float, pd.Timestamp]] = None,
                        max_events: int = 100) -> List[str]:
        """
        Retrieve events within a time range.
        
        Args:
            start_time: Start timestamp of the range
            end_time: End timestamp of the range
            max_events: Maximum number of events to return
            
        Returns:
            List of event IDs within the time range, ordered by time
        """
        # Apply maintenance operations first
        self._apply_maintenance()
        
        # Convert timestamps if needed
        if start_time is not None and isinstance(start_time, pd.Timestamp):
            start_time = start_time.timestamp()
        
        if end_time is not None and isinstance(end_time, pd.Timestamp):
            end_time = end_time.timestamp()
        
        # Set default time range if not provided
        if not self.episodes:
            return []
        
        if start_time is None:
            # Find earliest event
            start_time = min(event.timestamp for event in self.episodes.values())
        
        if end_time is None:
            # Find latest event
            end_time = max(event.timestamp for event in self.episodes.values())
        
        # Get events from temporal index
        event_ids = list(self.temporal_index.get_events_in_range(start_time, end_time))
        
        # Sort by timestamp
        event_ids.sort(key=lambda eid: self.episodes[eid].timestamp if eid in self.episodes else float('inf'))
        
        # Record retrieval for events
        for event_id in event_ids[:max_events]:
            if event_id in self.episodes:
                self.episodes[event_id].reactivate(0.2)  # Small activation boost
        
        return event_ids[:max_events]
    
    def retrieve_by_location(self, 
                           location: Union[Tuple, List, Dict],
                           radius: float = 0.1,
                           max_events: int = 50) -> List[str]:
        """
        Retrieve events that occurred at or near a specific location.
        
        Args:
            location: Location coordinates or dictionary
            radius: Search radius around the location
            max_events: Maximum number of events to return
            
        Returns:
            List of event IDs at or near the location
        """
        # Apply maintenance operations first
        self._apply_maintenance()
        
        # Get events from spatial index
        event_ids = list(self.spatial_index.get_events_at_location(location, radius))
        
        # Sort by distance
        if isinstance(location, dict) and 'coordinates' in location:
            location_coords = location['coordinates']
        elif isinstance(location, (list, tuple)):
            location_coords = location
        else:
            # Can't sort by distance without coordinates
            # Record retrieval for events
            for event_id in event_ids[:max_events]:
                if event_id in self.episodes:
                    self.episodes[event_id].reactivate(0.2)  # Small activation boost
            return event_ids[:max_events]
        
        # Sort by distance
        distance_events = self.spatial_index.get_nearest_events(location_coords, len(event_ids))
        
        # Filter to events that are within the radius
        distance_events = [(eid, dist) for eid, dist in distance_events if dist <= radius]
        
        # Sort by distance (closest first)
        distance_events.sort(key=lambda x: x[1])
        
        # Extract event IDs
        sorted_event_ids = [eid for eid, _ in distance_events]
        
        # Record retrieval for events
        for event_id in sorted_event_ids[:max_events]:
            if event_id in self.episodes:
                self.episodes[event_id].reactivate(0.2)  # Small activation boost
        
        return sorted_event_ids[:max_events]
    
    def retrieve_by_tags(self, tags: List[str], require_all: bool = False, max_events: int = 50) -> List[str]:
        """
        Retrieve events with specific tags.
        
        Args:
            tags: List of tags to search for
            require_all: Whether all tags must be present (True) or any (False)
            max_events: Maximum number of events to return
            
        Returns:
            List of event IDs with matching tags
        """
        # Apply maintenance operations first
        self._apply_maintenance()
        
        matching_events = []
        
        for event_id, event in self.episodes.items():
            if require_all:
                # All tags must be present
                if all(tag in event.tags for tag in tags):
                    matching_events.append((event_id, event.timestamp))
            else:
                # Any tag can be present
                if any(tag in event.tags for tag in tags):
                    matching_events.append((event_id, event.timestamp))
        
        # Sort by timestamp (most recent first)
        matching_events.sort(key=lambda x: x[1], reverse=True)
        
        # Extract event IDs
        event_ids = [eid for eid, _ in matching_events[:max_events]]
        
        # Record retrieval for events
        for event_id in event_ids:
            self.episodes[event_id].reactivate(0.2)  # Small activation boost
        
        return event_ids
    
    def retrieve_associated(self, episode_id: str, max_events: int = 10) -> List[Tuple[str, str, float]]:
        """
        Retrieve events associated with a given episode.
        
        Args:
            episode_id: ID of the reference event
            max_events: Maximum number of associated events to return
            
        Returns:
            List of (event_id, relation_type, strength) tuples
        """
        # Apply maintenance operations first
        self._apply_maintenance()
        
        # Check if event exists
        if episode_id not in self.episodes:
            return []
        
        # Get directly associated events from the event itself
        direct_associations = list(self.episodes[episode_id].associations)
        
        # Get trace-based associations
        trace_associations = []
        for trace_id, trace in self.traces.items():
            if trace.source_id == episode_id:
                trace_associations.append((trace.target_id, trace.relation_type, trace.strength))
            elif trace.bidirectional and trace.target_id == episode_id:
                trace_associations.append((trace.source_id, trace.relation_type, trace.strength))
        
        # Combine associations
        all_associations = {}
        
        # Add direct associations (with default relation and strength)
        for assoc_id in direct_associations:
            if assoc_id in self.episodes:
                all_associations[assoc_id] = ("association", 0.8)
        
        # Add trace associations (overriding direct associations if stronger)
        for assoc_id, relation_type, strength in trace_associations:
            if assoc_id in self.episodes:
                if assoc_id not in all_associations or strength > all_associations[assoc_id][1]:
                    all_associations[assoc_id] = (relation_type, strength)
        
        # If we still need more associations, find similar events
        if len(all_associations) < max_events:
            needed = max_events - len(all_associations)
            if episode_id in self.episodes:
                similar_events = self.conceptual_index.get_events_by_similarity(episode_id, needed * 2)
                
                # Add as semantic associations
                for assoc_id, similarity in similar_events:
                    if assoc_id in self.episodes and assoc_id not in all_associations:
                        all_associations[assoc_id] = ("semantic", similarity)
                        
                        # Also create a bidirectional trace
                        trace_id = str(uuid.uuid4())
                        trace = EpisodicMemoryTrace(
                            id=trace_id,
                            source_id=episode_id,
                            target_id=assoc_id,
                            relation_type="semantic",
                            strength=similarity,
                            bidirectional=True
                        )
                        self.traces[trace_id] = trace
                        
                        # Update event associations
                        self.episodes[episode_id].add_association(assoc_id)
                        self.episodes[assoc_id].add_association(episode_id)
                        
                        if len(all_associations) >= max_events:
                            break
        
        # Convert to list and sort by strength
        result = [(assoc_id, relation_type, strength) 
                 for assoc_id, (relation_type, strength) in all_associations.items()]
        result.sort(key=lambda x: x[2], reverse=True)
        
        # Record retrieval for reference event and associated events
        self.episodes[episode_id].record_retrieval()
        for assoc_id, _, _ in result:
            self.episodes[assoc_id].reactivate(0.3)
        
        # Return limited results
        return result[:max_events]
    
    def retrieve_most_recent(self, max_events: int = 10, event_type: Optional[str] = None) -> List[str]:
        """
        Retrieve the most recent events.
        
        Args:
            max_events: Maximum number of events to return
            event_type: Optional filter by event type
            
        Returns:
            List of most recent event IDs
        """
        # Apply maintenance operations first
        self._apply_maintenance()
        
        # Get all events, potentially filtered by type
        if event_type:
            candidates = [(eid, event.timestamp) 
                         for eid, event in self.episodes.items() 
                         if event.event_type == event_type]
        else:
            candidates = [(eid, event.timestamp) for eid, event in self.episodes.items()]
        
        # Sort by timestamp (most recent first)
        candidates.sort(key=lambda x: x[1], reverse=True)
        
        # Extract event IDs
        result = [eid for eid, _ in candidates[:max_events]]
        
        # Record retrieval for returned events
        for event_id in result:
            self.episodes[event_id].reactivate(0.2)
        
        return result
    
    def retrieve_most_important(self, max_events: int = 10, event_type: Optional[str] = None) -> List[str]:
        """
        Retrieve the most important events.
        
        Args:
            max_events: Maximum number of events to return
            event_type: Optional filter by event type
            
        Returns:
            List of most important event IDs
        """
        # Apply maintenance operations first
        self._apply_maintenance()
        
        # Get all events, potentially filtered by type
        if event_type:
            candidates = [(eid, event.salience) 
                         for eid, event in self.episodes.items() 
                         if event.event_type == event_type]
        else:
            candidates = [(eid, event.salience) for eid, event in self.episodes.items()]
        
        # Sort by importance (highest first)
        candidates.sort(key=lambda x: x[1], reverse=True)
        
        # Extract event IDs
        result = [eid for eid, _ in candidates[:max_events]]
        
        # Record retrieval for returned events
        for event_id in result:
            self.episodes[event_id].reactivate(0.2)
        
        return result
    
    def retrieve_most_active(self, max_events: int = 10, event_type: Optional[str] = None) -> List[str]:
        """
        Retrieve the most active events.
        
        Args:
            max_events: Maximum number of events to return
            event_type: Optional filter by event type
            
        Returns:
            List of most active event IDs
        """
        # Apply maintenance operations first
        self._apply_maintenance()
        
        # Get all events, potentially filtered by type
        if event_type:
            candidates = [(eid, event.activation) 
                         for eid, event in self.episodes.items() 
                         if event.event_type == event_type]
        else:
            candidates = [(eid, event.activation) for eid, event in self.episodes.items()]
        
        # Sort by activation (highest first)
        candidates.sort(key=lambda x: x[1], reverse=True)
        
        # Extract event IDs
        result = [eid for eid, _ in candidates[:max_events]]
        
        # Don't update activation here as these are already the most active
        
        return result
    
    def create_memory_trace(self, 
                           source_id: str, 
                           target_id: str,
                           relation_type: str = "association",
                           strength: float = 0.8,
                           bidirectional: bool = True,
                           metadata: Optional[Dict[str, Any]] = None) -> Optional[str]:
        """
        Create a memory trace between two events.
        
        Args:
            source_id: ID of the source event
            target_id: ID of the target event
            relation_type: Type of relationship
            strength: Strength of the connection (0-1)
            bidirectional: Whether the connection is bidirectional
            metadata: Additional metadata for the trace
            
        Returns:
            ID of the created trace, or None if events not found
        """
        # Apply maintenance operations first
        self._apply_maintenance()
        
        # Check if events exist
        if source_id not in self.episodes or target_id not in self.episodes:
            return None
        
        # Check if trace already exists
        for trace in self.traces.values():
            if (trace.source_id == source_id and trace.target_id == target_id) or \
               (bidirectional and trace.source_id == target_id and trace.target_id == source_id):
                # Strengthen existing trace if needed
                if strength > trace.strength:
                    trace.strength = strength
                return trace.id
        
        # Create new trace
        trace_id = str(uuid.uuid4())
        trace = EpisodicMemoryTrace(
            id=trace_id,
            source_id=source_id,
            target_id=target_id,
            relation_type=relation_type,
            strength=strength,
            bidirectional=bidirectional,
            metadata=metadata or {}
        )
        
        # Store the trace
        self.traces[trace_id] = trace
        
        # Update event associations
        self.episodes[source_id].add_association(target_id)
        if bidirectional:
            self.episodes[target_id].add_association(source_id)
        
        return trace_id
    
    def consolidate(self) -> int:
        """
        Consolidate episodic memories.
        
        Returns:
            int: Number of consolidated memories
        """
        logger.info("Starting episodic memory consolidation")
        
        if not self.episodes:
            logger.debug("No episodes to consolidate")
            return 0
        
        # Apply forgetting before consolidation
        self._apply_forgetting()
        
        # Select memories for consolidation
        selected_ids = self.consolidation.select_memories_for_consolidation(
            self.episodes,
            max_to_select=min(100, len(self.episodes) // 10)
        )
        
        if not selected_ids:
            logger.debug("No episodes selected for consolidation")
            return 0
        
        # Consolidate selected memories
        consolidated_memories, consolidated_ids = self.consolidation.consolidate_memories(
            self.episodes,
            selected_ids
        )
        
        # Add consolidated memories to the system
        for event_id, event in consolidated_memories.items():
            # Store the event
            self.episodes[event_id] = event
            
            # Index the event
            self.temporal_index.add_event(event_id, event.timestamp)
            self.conceptual_index.add_event(event_id, event.embedding)
            
            if event.spatial_context:
                self.spatial_index.add_event(event_id, event.spatial_context)
            
            # Create traces to source events
            for source_id in event.metadata.get('source_ids', []):
                if source_id in self.episodes:
                    self.create_memory_trace(
                        source_id=source_id,
                        target_id=event_id,
                        relation_type="consolidated_from",
                        strength=0.9,
                        bidirectional=True
                    )
        
        logger.info(f"Consolidated {len(consolidated_memories)} memories from {len(consolidated_ids)} source memories")
        
        # Update indices
        if consolidated_memories:
            logger.debug("Rebuilding indices after consolidation")
            self._rebuild_indices()
        
        return len(consolidated_memories)
    
    def form_memory_traces(self) -> int:
        """
        Form memory traces between semantically related events.
        
        Returns:
            int: Number of traces formed
        """
        # Get current trace count
        initial_trace_count = len(self.traces)
        
        # Form traces
        self.traces = self.consolidation.form_traces(self.episodes, self.traces)
        
        # Update event associations based on traces
        for trace_id, trace in self.traces.items():
            if trace.source_id in self.episodes and trace.target_id in self.episodes:
                self.episodes[trace.source_id].add_association(trace.target_id)
                if trace.bidirectional:
                    self.episodes[trace.target_id].add_association(trace.source_id)
        
        # Return number of new traces
        return len(self.traces) - initial_trace_count
    
    def remove_episode(self, episode_id: str) -> bool:
        """
        Remove an episode and its associated traces.
        
        Args:
            episode_id (str): ID of the episode to remove
            
        Returns:
            bool: True if removal was successful
        """
        # Check if episode exists
        if episode_id not in self.episodes:
            return False
        
        # Remove from indices
        self.temporal_index.remove_event(episode_id)
        self.conceptual_index.remove_event(episode_id)
        self.spatial_index.remove_event(episode_id)
        
        # Find traces to remove
        traces_to_remove = []
        for trace_id, trace in self.traces.items():
            if trace.source_id == episode_id or trace.target_id == episode_id:
                traces_to_remove.append(trace_id)
                
                # Remove association from the other end
                other_id = trace.target_id if trace.source_id == episode_id else trace.source_id
                if other_id in self.episodes:
                    self.episodes[other_id].remove_association(episode_id)
        
        # Remove traces
        for trace_id in traces_to_remove:
            del self.traces[trace_id]
        
        # Remove episode
        del self.episodes[episode_id]
        
        return True
    
    def clear(self) -> None:
        """
        Clear all episodic memories, traces, and indices.
        """
        # Clear data structures
        self.episodes = {}
        self.traces = {}
        
        # Reset indices
        self.temporal_index = TemporalIndex()
        self.conceptual_index = ConceptualIndex(
            embedding_dim=self.config['embedding_dim'],
            use_gpu=self.config['use_gpu']
        )
        self.spatial_index = SpatialIndex()
        
        # Reset last update time
        self.last_update_time = time.time()
        
        logger.info("Cleared all episodic memories")
    
    def save_to_file(self, filepath: str) -> bool:
        """
        Save episodic knowledge base to a file.
        
        Args:
            filepath (str): Path to save file
            
        Returns:
            bool: True if successful
        """
        try:
            # Apply maintenance operations first
            self._apply_maintenance()
            
            # Convert episodes to dictionary format
            episodes_data = {
                event_id: event.to_dict() 
                for event_id, event in self.episodes.items()
            }
            
            # Convert traces to dictionary format
            traces_data = {
                trace_id: trace.to_dict()
                for trace_id, trace in self.traces.items()
            }
            
            # Prepare data to save
            save_data = {
                'config': self.config,
                'episodes': episodes_data,
                'traces': traces_data,
                'version': '1.0',
                'timestamp': pd.Timestamp.now().isoformat()
            }
            
            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(os.path.abspath(filepath)), exist_ok=True)
            
            # Save to file
            with open(filepath, 'w') as f:
                json.dump(save_data, f, indent=2)
                
            logger.info(f"Saved episodic knowledge base to {filepath}")
            return True
            
        except Exception as e:
            logger.error(f"Error saving episodic knowledge base: {e}")
            return False
    
    def load_from_file(self, filepath: str) -> bool:
        """
        Load episodic knowledge base from a file.
        
        Args:
            filepath (str): Path to load file
            
        Returns:
            bool: True if successful
        """
        try:
            # Load from file
            with open(filepath, 'r') as f:
                load_data = json.load(f)
            
            # Check version
            version = load_data.get('version', '1.0')
            if version != '1.0':
                logger.warning(f"Loading episodic knowledge base with different version: {version}")
            
            # Update config
            self.config.update(load_data.get('config', {}))
            
            # Clear current data
            self.clear()
            
            # Load episodes
            episodes_data = load_data.get('episodes', {})
            for event_id, event_data in episodes_data.items():
                self.episodes[event_id] = EpisodicEvent.from_dict(event_data)
            
            # Load traces
            traces_data = load_data.get('traces', {})
            for trace_id, trace_data in traces_data.items():
                self.traces[trace_id] = EpisodicMemoryTrace.from_dict(trace_data)
            
            # Rebuild indices
            self._rebuild_indices()
            
            logger.info(f"Loaded episodic knowledge base from {filepath} with {len(self.episodes)} episodes and {len(self.traces)} traces")
            return True
            
        except Exception as e:
            logger.error(f"Error loading episodic knowledge base: {e}")
            return False
    
    def get_count(self) -> int:
        """
        Get the number of episodes in the knowledge base.
        
        Returns:
            int: Number of episodes
        """
        return len(self.episodes)
    
    def get_trace_count(self) -> int:
        """
        Get the number of memory traces in the knowledge base.
        
        Returns:
            int: Number of traces
        """
        return len(self.traces)
    
    def get_stats(self) -> Dict[str, Any]:
        """
        Get statistics about the episodic memory system.
        
        Returns:
            Dict[str, Any]: Statistics
        """
        # Apply maintenance operations first
        self._apply_maintenance()
        
        # Count events by type
        event_types = {}
        for event in self.episodes.values():
            if event.event_type in event_types:
                event_types[event.event_type] += 1
            else:
                event_types[event.event_type] = 1
        
        # Count traces by relation type
        relation_types = {}
        for trace in self.traces.values():
            if trace.relation_type in relation_types:
                relation_types[trace.relation_type] += 1
            else:
                relation_types[trace.relation_type] = 1
        
        # Compute memory stats
        avg_importance = 0
        avg_memory_strength = 0
        avg_activation = 0
        
        if self.episodes:
            avg_importance = sum(event.salience for event in self.episodes.values()) / len(self.episodes)
            avg_memory_strength = sum(event.memory_strength for event in self.episodes.values()) / len(self.episodes)
            avg_activation = sum(event.activation for event in self.episodes.values()) / len(self.episodes)
        
        # Get time range
        time_range = (0, 0)
        if self.episodes:
            timestamps = [event.timestamp for event in self.episodes.values()]
            time_range = (min(timestamps), max(timestamps))
            time_range = (
                pd.Timestamp.fromtimestamp(time_range[0]).isoformat(),
                pd.Timestamp.fromtimestamp(time_range[1]).isoformat()
            )
        
        # Count index sizes
        index_stats = {
            'temporal_index_size': self.temporal_index.get_event_count(),
            'conceptual_index_size': self.conceptual_index.get_event_count(),
            'spatial_index_size': self.spatial_index.get_event_count()
        }
        
        return {
            'event_count': len(self.episodes),
            'trace_count': len(self.traces),
            'event_types': event_types,
            'relation_types': relation_types,
            'avg_importance': avg_importance,
            'avg_memory_strength': avg_memory_strength,
            'avg_activation': avg_activation,
            'time_range': time_range,
            'indices': index_stats
        }
    
    def build_memory_network(self) -> nx.Graph:
        """
        Build a network representation of episodic memory.
        
        Returns:
            NetworkX graph of episodic memory
        """
        # Apply maintenance operations first
        self._apply_maintenance()
        
        # Create graph
        G = nx.Graph()
        
        # Add events as nodes
        for event_id, event in self.episodes.items():
            G.add_node(event_id, 
                      type='event',
                      event_type=event.event_type,
                      timestamp=event.timestamp,
                      importance=event.salience,
                      activation=event.activation,
                      memory_strength=event.memory_strength)
        
        # Add traces as edges
        for trace_id, trace in self.traces.items():
            # Skip if either endpoint is missing
            if trace.source_id not in self.episodes or trace.target_id not in self.episodes:
                continue
                
            G.add_edge(trace.source_id, 
                      trace.target_id,
                      type='trace',
                      relation_type=trace.relation_type,
                      strength=trace.strength,
                      bidirectional=trace.bidirectional)
        
        # Add direct associations that aren't already covered by traces
        for event_id, event in self.episodes.items():
            for assoc_id in event.associations:
                if assoc_id in self.episodes and not G.has_edge(event_id, assoc_id):
                    G.add_edge(event_id, 
                              assoc_id,
                              type='association',
                              relation_type='association',
                              strength=0.5,
                              bidirectional=True)
        
        return G
    
    def sync_with_graph(self, knowledge_graph: Dict[str, Any]) -> None:
        """
        Synchronize with knowledge graph data.
        
        Args:
            knowledge_graph (Dict[str, Any]): Knowledge graph data
        """
        # Find episodic nodes in graph
        episodic_nodes = {}
        
        for node_id, node_data in knowledge_graph.get('nodes', {}).items():
            if node_data.get('knowledge_type') == 'episodic' and node_data.get('type') != 'root':
                episodic_nodes[node_id] = node_data
        
        # Update local episodes based on graph data
        for node_id, node_data in episodic_nodes.items():
            if node_id in self.episodes:
                # Update existing episode
                event = self.episodes[node_id]
                
                # Update embedding
                if 'embedding' in node_data:
                    event.embedding = np.array(node_data['embedding'])
                
                # Update metadata
                if 'metadata' in node_data:
                    metadata = node_data['metadata']
                    
                    # Update temporal context
                    if 'timestamp' in metadata:
                        event.temporal_context.update({
                            'timestamp': metadata['timestamp']
                        })
                    
                    # Update salience
                    if 'salience' in metadata:
                        event.salience = metadata['salience']
                        event.importance = event.salience
            else:
                # Create new episode from graph data
                if 'id' in node_data and node_data['id'] not in self.episodes:
                    try:
                        # Extract content
                        content = node_data.get('content')
                        
                        # Create temporal context
                        temporal_context = None
                        if 'metadata' in node_data and 'timestamp' in node_data['metadata']:
                            timestamp = pd.Timestamp(node_data['metadata']['timestamp'])
                            temporal_context = TemporalContext(timestamp=timestamp)
                        
                        # Create event
                        event = EpisodicEvent(
                            id=node_data['id'],
                            content=content,
                            temporal_context=temporal_context,
                            event_type=node_data.get('event_type', 'general'),
                            embedding_dim=self.config['embedding_dim']
                        )
                        
                        # Set embedding
                        if 'embedding' in node_data:
                            event.embedding = np.array(node_data['embedding'])
                        
                        # Set metadata
                        if 'metadata' in node_data:
                            metadata = node_data['metadata']
                            
                            # Set salience
                            if 'salience' in metadata:
                                event.salience = metadata['salience']
                                event.importance = event.salience
                            
                            # Set tags
                            if 'tags' in metadata and isinstance(metadata['tags'], list):
                                event.tags = metadata['tags']
                        
                        # Add to episodes
                        self.episodes[event.id] = event
                    except Exception as e:
                        logger.error(f"Error creating episode from graph node {node_id}: {e}")
        
        # Find associations in graph edges
        for edge_id, edge_data in knowledge_graph.get('edges', {}).items():
            source = edge_data.get('source')
            target = edge_data.get('target')
            
            if source in self.episodes and target in self.episodes:
                # Check if a trace already exists
                trace_exists = False
                for trace in self.traces.values():
                    if (trace.source_id == source and trace.target_id == target) or \
                       (trace.source_id == target and trace.target_id == source and trace.bidirectional):
                        trace_exists = True
                        break
                
                if not trace_exists:
                    # Create trace
                    trace_id = str(uuid.uuid4())
                    trace = EpisodicMemoryTrace(
                        id=trace_id,
                        source_id=source,
                        target_id=target,
                        relation_type=edge_data.get('type', 'association'),
                        strength=edge_data.get('weight', 0.5),
                        bidirectional=True  # Assume bidirectional by default
                    )
                    
                    # Add to traces
                    self.traces[trace_id] = trace
                
                # Add bidirectional association
                self.episodes[source].add_association(target)
                self.episodes[target].add_association(source)
        
        # Rebuild indices
        self._rebuild_indices()
        
        logger.info(f"Synchronized episodic knowledge base with graph, total episodes: {len(self.episodes)}")
    
    def _get_query_embedding(self, query: Union[str, np.ndarray, Dict]) -> Optional[np.ndarray]:
        """
        Convert a query to an embedding vector.
        
        Args:
            query: Query string, embedding, or dictionary
            
        Returns:
            Embedding vector or None if conversion not possible
        """
        # If query is already an embedding, use it
        if isinstance(query, np.ndarray):
            embedding = query
            # Normalize if needed
            norm = np.linalg.norm(embedding)
            if norm > 0 and abs(norm - 1.0) > 1e-6:
                embedding = embedding / norm
            return embedding.astype(np.float32)
        
        # If query is a dictionary with an embedding, use it
        if isinstance(query, dict) and 'embedding' in query:
            embedding = np.array(query['embedding'])
            # Normalize if needed
            norm = np.linalg.norm(embedding)
            if norm > 0 and abs(norm - 1.0) > 1e-6:
                embedding = embedding / norm
            return embedding.astype(np.float32)
        
        # If an embedding_model is provided, use it
        if callable(self.config.get('embedding_model')):
            # Adapt query format to the embedding model
            if isinstance(query, str):
                query_input = query
            else:
                query_input = query
                
            try:
                embedding = self.config['embedding_model'](query_input)
                
                # Normalize if needed
                norm = np.linalg.norm(embedding)
                if norm > 0 and abs(norm - 1.0) > 1e-6:
                    embedding = embedding / norm
                    
                return embedding.astype(np.float32)
            except Exception as e:
                logger.error(f"Error creating query embedding: {e}")
                return None
        
        # If no embedding can be created, return None
        logger.warning("Cannot create query embedding: no embedding model provided")
        return None
    
    def _apply_maintenance(self) -> None:
        """
        Apply maintenance operations based on time elapsed.
        """
        current_time = time.time()
        time_elapsed = current_time - self.last_update_time
        
        # Skip if little time has elapsed
        if time_elapsed < 1.0:  # Less than 1 second
            return
        
        # Apply memory consolidation (strengthen retention)
        self.consolidation.consolidate_memory(self.episodes, time_elapsed / 3600)  # Convert to hours
        
        # Update last update time
        self.last_update_time = current_time
    
    def _apply_forgetting(self) -> None:
        """
        Apply forgetting to all memories based on time since last access.
        """
        # Apply forgetting to events
        self.consolidation.apply_forgetting(self.episodes, 1.0)  # Use default time unit
        
        # Apply forgetting to traces
        self.consolidation.decay_traces(self.traces, 1.0)  # Use default time unit
        
        logger.debug(f"Applied forgetting to {len(self.episodes)} episodes and {len(self.traces)} traces")
    
    def _forget_memories(self) -> None:
        """
        Forget least important memories to stay within capacity limits.
        """
        # Calculate number of memories to forget
        target_count = int(self.config['max_episodes'] * 0.9)  # Aim for 90% of max
        forget_count = len(self.episodes) - target_count
        
        if forget_count <= 0:
            return
        
        logger.debug(f"Forgetting {forget_count} episodic memories to stay within capacity")
        
        # Calculate forgetting scores for each memory
        # Lower score = more likely to be forgotten
        memory_scores = []
        
        for memory_id, memory in self.episodes.items():
            # Compute score based on multiple factors
            
            # Memory strength is primary factor
            score = memory.memory_strength * 0.4
            
            # Importance/salience
            score += memory.salience * 0.3
            
            # Activation level
            score += memory.activation * 0.2
            
            # Retrieval count (normalized)
            retrieval_factor = min(1.0, memory.retrieval_count / 10)  # Cap at 10 retrievals
            score += retrieval_factor * 0.1
            
            # Age penalty (newer memories are less likely to be forgotten)
            age_hours = (pd.Timestamp.now() - memory.created_at).total_seconds() / 3600
            age_factor = min(1.0, age_hours / (24 * 30))  # Normalize over 30 days
            score -= age_factor * 0.1
            
            # Connectivity bonus (memories with many connections are less likely to be forgotten)
            connection_count = len(memory.associations)
            connection_factor = min(1.0, connection_count / 10)  # Cap at 10 connections
            score += connection_factor * 0.1
            
            memory_scores.append((memory_id, score))
        
        # Sort by score (ascending - lowest scores first)
        memory_scores.sort(key=lambda x: x[1])
        
        # Get IDs to forget
        forget_ids = [memory_id for memory_id, _ in memory_scores[:forget_count]]
        
        # Remove forgotten memories
        for memory_id in forget_ids:
            self.remove_episode(memory_id)
        
        logger.info(f"Forgot {len(forget_ids)} memories to stay within capacity")
    
    def _rebuild_indices(self) -> None:
        """
        Rebuild all indices based on current episodes.
        """
        logger.debug("Rebuilding episodic memory indices")
        
        # Clear indices
        self.temporal_index = TemporalIndex()
        self.conceptual_index = ConceptualIndex(
            embedding_dim=self.config['embedding_dim'],
            use_gpu=self.config['use_gpu']
        )
        self.spatial_index = SpatialIndex()
        
        # Rebuild indices
        for event_id, event in self.episodes.items():
            # Add to temporal index
            self.temporal_index.add_event(event_id, event.timestamp)
            
            # Add to conceptual index
            self.conceptual_index.add_event(event_id, event.embedding)
            
            # Add to spatial index if available
            if event.spatial_context:
                self.spatial_index.add_event(event_id, event.spatial_context)
    
    def _start_consolidation_timer(self) -> None:
        """
        Start the consolidation timer.
        """
        interval = self.config['consolidation_interval']
        
        def consolidation_task():
            try:
                logger.debug("Automatic consolidation triggered by timer")
                self.consolidate()
                self._start_consolidation_timer()
            except Exception as e:
                logger.error(f"Error in consolidation task: {e}")
        
        self.consolidation_timer = threading.Timer(interval, consolidation_task)
        self.consolidation_timer.daemon = True
        self.consolidation_timer.start()
        
        logger.debug(f"Started consolidation timer with interval {interval} seconds")


# Legacy class name for compatibility
EpisodicMemory = EpisodicKnowledgeBase

# Export main elements
__all__ = [
    'TemporalContext',
    'MemoryContext',
    'EpisodicEvent',
    'EpisodicMemoryTrace',
    'TemporalIndex',
    'ConceptualIndex',
    'SpatialIndex',
    'MemoryConsolidation',
    'EpisodicKnowledgeBase',
    'EpisodicMemory'  # Legacy alias
]