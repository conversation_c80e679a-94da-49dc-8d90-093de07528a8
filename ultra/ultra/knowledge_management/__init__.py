# ultra/knowledge_management/__init__.py

import os
import sys
import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Tuple, Optional, Union, Set, Callable

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Import knowledge management components
try:
    from ultra.episodic_knowledge import EpisodicKnowledgeBase
    from ultra.semantic_knowledge import SemanticKnowledge
    from ultra.procedural_knowledge import ProceduralKnowledgeBase
    from ultra.knowledge_integration import KnowledgeIntegrator as KnowledgeIntegration
except ImportError as e:
    logger.error(f"Error importing knowledge management components: {e}")
    raise

class KnowledgeManagementSystem:
    """
    Knowledge Management System for ULTRA architecture.
    
    This system integrates episodic, semantic, and procedural knowledge 
    into a unified framework that supports the ULTRA cognitive architecture.
    
    Attributes:
        episodic_kb (EpisodicKnowledgeBase): Stores experience-based memories and events
        semantic_kb (SemanticKnowledge): Manages factual and conceptual knowledge
        procedural_kb (ProceduralKnowledgeBase): Contains action sequences and skills
        integrator (KnowledgeIntegration): Integrates knowledge across different types
        config (Dict): Configuration parameters for the knowledge system
        knowledge_graph (Dict): Graph representation of knowledge interconnections
        retrieval_cache (Dict): Cache for frequently accessed knowledge elements
    """
    
    def __init__(self, 
                 config: Dict = None, 
                 episodic_config: Dict = None,
                 semantic_config: Dict = None,
                 procedural_config: Dict = None,
                 integration_config: Dict = None):
        """
        Initialize the Knowledge Management System.
        
        Args:
            config (Dict, optional): Global configuration for the knowledge system
            episodic_config (Dict, optional): Configuration for episodic knowledge base
            semantic_config (Dict, optional): Configuration for semantic knowledge
            procedural_config (Dict, optional): Configuration for procedural knowledge base
            integration_config (Dict, optional): Configuration for knowledge integration
        """
        logger.info("Initializing Knowledge Management System")
        
        # Set default configurations if not provided
        self.config = config or {
            'embedding_dim': 1024,
            'memory_capacity': 100000,
            'retrieval_threshold': 0.75,
            'consolidation_interval': 3600,  # seconds
            'integration_weight': 0.5,
            'cache_size': 1000,
            'distributed_storage': True,
            'compression_ratio': 0.8,
            'graph_density': 0.1
        }
        
        # Initialize knowledge components
        self.episodic_kb = EpisodicKnowledgeBase(episodic_config or {
            'temporal_resolution': 0.1,
            'context_window': 100,
            'max_episodes': 10000,
            'forgetting_rate': 0.01,
            'consolidation_threshold': 0.6,
            'embedding_dim': self.config['embedding_dim']
        })
        
        self.semantic_kb = SemanticKnowledge(semantic_config or {
            'ontology_depth': 10,
            'relation_types': ['is_a', 'part_of', 'has_property', 'causes', 'related_to'],
            'confidence_threshold': 0.7,
            'contradiction_resolution': 'bayesian',
            'embedding_dim': self.config['embedding_dim']
        })
        
        self.procedural_kb = ProceduralKnowledgeBase(procedural_config or {
            'max_action_steps': 50,
            'success_threshold': 0.8,
            'optimization_rate': 0.05,
            'generalization_factor': 0.3,
            'embedding_dim': self.config['embedding_dim']
        })
        
        self.integrator = KnowledgeIntegration(integration_config or {
            'cross_reference_weight': 0.7,
            'consistency_threshold': 0.6,
            'integration_methods': ['graph_fusion', 'bayesian_updating', 'temporal_alignment'],
            'embedding_dim': self.config['embedding_dim']
        })
        
        # Initialize internal structures
        self.knowledge_graph = {}  # Graph representation of knowledge interconnections
        self.retrieval_cache = {}  # Cache for frequently accessed knowledge
        self.activation_levels = {}  # Track activation of knowledge elements
        self.attention_weights = {}  # Weights for knowledge attention
        
        # Connection to other ULTRA components
        self.global_workspace_connection = None
        self.neuro_symbolic_connection = None
        self.meta_cognitive_connection = None
        
        # Initialize the knowledge graph structure
        self._initialize_knowledge_graph()
        
        logger.info("Knowledge Management System initialized successfully")
    
    def _initialize_knowledge_graph(self) -> None:
        """
        Initialize the knowledge graph structure that connects all knowledge types.
        
        The knowledge graph is a multi-layered representation that connects
        episodic, semantic, and procedural knowledge elements.
        """
        logger.debug("Initializing knowledge graph structure")
        
        self.knowledge_graph = {
            'nodes': {},
            'edges': {},
            'metadata': {
                'creation_time': pd.Timestamp.now(),
                'last_updated': pd.Timestamp.now(),
                'version': '1.0',
                'node_count': 0,
                'edge_count': 0
            },
            'indices': {
                'temporal': {},  # Index by time
                'semantic': {},  # Index by concept
                'spatial': {},   # Index by location
                'causal': {},    # Index by cause-effect
                'procedural': {} # Index by action sequence
            }
        }
        
        # Initialize base nodes for each knowledge type
        self._add_node('episodic_root', {
            'type': 'root',
            'knowledge_type': 'episodic',
            'embedding': np.zeros(self.config['embedding_dim']),
            'creation_time': pd.Timestamp.now()
        })
        
        self._add_node('semantic_root', {
            'type': 'root',
            'knowledge_type': 'semantic',
            'embedding': np.zeros(self.config['embedding_dim']),
            'creation_time': pd.Timestamp.now()
        })
        
        self._add_node('procedural_root', {
            'type': 'root',
            'knowledge_type': 'procedural',
            'embedding': np.zeros(self.config['embedding_dim']),
            'creation_time': pd.Timestamp.now()
        })
        
        # Connect root nodes
        self._add_edge('episodic_root', 'semantic_root', {
            'type': 'cross_reference',
            'weight': 0.5,
            'creation_time': pd.Timestamp.now()
        })
        
        self._add_edge('semantic_root', 'procedural_root', {
            'type': 'cross_reference',
            'weight': 0.5,
            'creation_time': pd.Timestamp.now()
        })
        
        self._add_edge('procedural_root', 'episodic_root', {
            'type': 'cross_reference',
            'weight': 0.5,
            'creation_time': pd.Timestamp.now()
        })
    
    def _add_node(self, node_id: str, attributes: Dict) -> None:
        """
        Add a node to the knowledge graph.
        
        Args:
            node_id (str): Unique identifier for the node
            attributes (Dict): Node attributes including type, embedding, etc.
        """
        if node_id in self.knowledge_graph['nodes']:
            logger.warning(f"Node {node_id} already exists in knowledge graph - updating")
            self.knowledge_graph['nodes'][node_id].update(attributes)
        else:
            self.knowledge_graph['nodes'][node_id] = attributes
            self.knowledge_graph['metadata']['node_count'] += 1
            
            # Update indices based on node type
            if 'knowledge_type' in attributes:
                knowledge_type = attributes['knowledge_type']
                if knowledge_type == 'episodic' and 'timestamp' in attributes:
                    self._update_temporal_index(node_id, attributes['timestamp'])
                elif knowledge_type == 'semantic' and 'concept' in attributes:
                    self._update_semantic_index(node_id, attributes['concept'])
                elif knowledge_type == 'procedural' and 'action_sequence' in attributes:
                    self._update_procedural_index(node_id, attributes['action_sequence'])
    
    def _add_edge(self, source_id: str, target_id: str, attributes: Dict) -> None:
        """
        Add an edge to the knowledge graph.
        
        Args:
            source_id (str): Source node ID
            target_id (str): Target node ID
            attributes (Dict): Edge attributes including type, weight, etc.
        """
        edge_id = f"{source_id}_{target_id}"
        
        if edge_id in self.knowledge_graph['edges']:
            logger.warning(f"Edge {edge_id} already exists in knowledge graph - updating")
            self.knowledge_graph['edges'][edge_id].update(attributes)
        else:
            self.knowledge_graph['edges'][edge_id] = {
                'source': source_id,
                'target': target_id,
                **attributes
            }
            self.knowledge_graph['metadata']['edge_count'] += 1
            
            # Update causal index if this is a causal relationship
            if attributes.get('type') == 'causal':
                self._update_causal_index(source_id, target_id, attributes.get('weight', 0.5))
    
    def _update_temporal_index(self, node_id: str, timestamp: pd.Timestamp) -> None:
        """
        Update the temporal index with a new node.
        
        Args:
            node_id (str): ID of the node to index
            timestamp (pd.Timestamp): Timestamp for indexing
        """
        time_key = timestamp.strftime('%Y-%m-%d %H:%M:%S')
        if time_key not in self.knowledge_graph['indices']['temporal']:
            self.knowledge_graph['indices']['temporal'][time_key] = []
        self.knowledge_graph['indices']['temporal'][time_key].append(node_id)
    
    def _update_semantic_index(self, node_id: str, concept: str) -> None:
        """
        Update the semantic index with a new node.
        
        Args:
            node_id (str): ID of the node to index
            concept (str): Concept for indexing
        """
        if concept not in self.knowledge_graph['indices']['semantic']:
            self.knowledge_graph['indices']['semantic'][concept] = []
        self.knowledge_graph['indices']['semantic'][concept].append(node_id)
    
    def _update_procedural_index(self, node_id: str, action_sequence: List[str]) -> None:
        """
        Update the procedural index with a new node.
        
        Args:
            node_id (str): ID of the node to index
            action_sequence (List[str]): Action sequence for indexing
        """
        # Use the first action as the index key
        if action_sequence and len(action_sequence) > 0:
            first_action = action_sequence[0]
            if first_action not in self.knowledge_graph['indices']['procedural']:
                self.knowledge_graph['indices']['procedural'][first_action] = []
            self.knowledge_graph['indices']['procedural'][first_action].append(node_id)
    
    def _update_causal_index(self, cause_id: str, effect_id: str, strength: float) -> None:
        """
        Update the causal index with a new cause-effect relationship.
        
        Args:
            cause_id (str): ID of the cause node
            effect_id (str): ID of the effect node
            strength (float): Strength of the causal relationship
        """
        if cause_id not in self.knowledge_graph['indices']['causal']:
            self.knowledge_graph['indices']['causal'][cause_id] = {}
        self.knowledge_graph['indices']['causal'][cause_id][effect_id] = strength
    
    def add_knowledge(self, 
                     knowledge_type: str, 
                     content: Any, 
                     metadata: Dict = None) -> str:
        """
        Add new knowledge to the appropriate knowledge base.
        
        Args:
            knowledge_type (str): Type of knowledge ('episodic', 'semantic', or 'procedural')
            content (Any): The knowledge content to add
            metadata (Dict, optional): Additional metadata for the knowledge
        
        Returns:
            str: ID of the added knowledge element
        """
        logger.debug(f"Adding {knowledge_type} knowledge: {str(content)[:100]}...")
        
        metadata = metadata or {}
        
        # Add to appropriate knowledge base
        if knowledge_type == 'episodic':
            knowledge_id = self.episodic_kb.add_episode(content, metadata)
        elif knowledge_type == 'semantic':
            knowledge_id = self.semantic_kb.add_concept(content, metadata)
        elif knowledge_type == 'procedural':
            knowledge_id = self.procedural_kb.add_procedure(content, metadata)
        else:
            raise ValueError(f"Unknown knowledge type: {knowledge_type}")
        
        # Update the knowledge graph
        self._add_knowledge_to_graph(knowledge_id, knowledge_type, content, metadata)
        
        # Trigger knowledge integration
        self._integrate_new_knowledge(knowledge_id, knowledge_type, content, metadata)
        
        return knowledge_id
    
    def _add_knowledge_to_graph(self, 
                               knowledge_id: str, 
                               knowledge_type: str, 
                               content: Any, 
                               metadata: Dict) -> None:
        """
        Add the new knowledge element to the knowledge graph.
        
        Args:
            knowledge_id (str): ID of the knowledge element
            knowledge_type (str): Type of knowledge
            content (Any): The knowledge content
            metadata (Dict): Additional metadata
        """
        # Create embedding for the knowledge
        if knowledge_type == 'episodic':
            embedding = self.episodic_kb.get_embedding(knowledge_id)
        elif knowledge_type == 'semantic':
            embedding = self.semantic_kb.get_embedding(knowledge_id)
        elif knowledge_type == 'procedural':
            embedding = self.procedural_kb.get_embedding(knowledge_id)
        
        # Add node to knowledge graph
        node_attributes = {
            'id': knowledge_id,
            'type': 'knowledge',
            'knowledge_type': knowledge_type,
            'embedding': embedding,
            'creation_time': pd.Timestamp.now(),
            'metadata': metadata
        }
        
        # Add type-specific attributes
        if knowledge_type == 'episodic':
            node_attributes.update({
                'timestamp': metadata.get('timestamp', pd.Timestamp.now()),
                'context': metadata.get('context', {}),
                'salience': metadata.get('salience', 0.5)
            })
        elif knowledge_type == 'semantic':
            node_attributes.update({
                'concept': metadata.get('concept', ''),
                'confidence': metadata.get('confidence', 0.8),
                'source': metadata.get('source', 'derived')
            })
        elif knowledge_type == 'procedural':
            node_attributes.update({
                'action_sequence': metadata.get('action_sequence', []),
                'success_rate': metadata.get('success_rate', 0.0),
                'execution_count': metadata.get('execution_count', 0)
            })
        
        self._add_node(knowledge_id, node_attributes)
        
        # Connect to appropriate root node
        root_id = f"{knowledge_type}_root"
        self._add_edge(root_id, knowledge_id, {
            'type': 'hierarchical',
            'weight': 1.0,
            'creation_time': pd.Timestamp.now()
        })
    
    def _integrate_new_knowledge(self, 
                                knowledge_id: str, 
                                knowledge_type: str, 
                                content: Any, 
                                metadata: Dict) -> None:
        """
        Integrate newly added knowledge with existing knowledge.
        
        This process identifies relationships, updates connections, and
        propagates changes throughout the knowledge graph.
        
        Args:
            knowledge_id (str): ID of the new knowledge element
            knowledge_type (str): Type of knowledge
            content (Any): The knowledge content
            metadata (Dict): Additional metadata
        """
        # Get the embedding of the new knowledge
        embedding = self.knowledge_graph['nodes'][knowledge_id]['embedding']
        
        # Find related knowledge elements
        related_nodes = self._find_related_knowledge(embedding, knowledge_type, k=5)
        
        # Create connections to related nodes
        for related_id, similarity in related_nodes:
            if similarity > self.config['retrieval_threshold']:
                related_type = self.knowledge_graph['nodes'][related_id]['knowledge_type']
                
                edge_type = 'association'
                if knowledge_type == related_type:
                    edge_type = 'intra_type'
                else:
                    edge_type = 'cross_type'
                
                self._add_edge(knowledge_id, related_id, {
                    'type': edge_type,
                    'weight': similarity,
                    'creation_time': pd.Timestamp.now()
                })
        
        # Call integrator to perform deeper integration
        self.integrator.integrate_knowledge(
            knowledge_id=knowledge_id,
            knowledge_type=knowledge_type,
            knowledge_graph=self.knowledge_graph,
            episodic_kb=self.episodic_kb,
            semantic_kb=self.semantic_kb,
            procedural_kb=self.procedural_kb
        )
        
        # Update the last modified time
        self.knowledge_graph['metadata']['last_updated'] = pd.Timestamp.now()
    
    def _find_related_knowledge(self, 
                               embedding: np.ndarray, 
                               exclude_type: str = None, 
                               k: int = 10) -> List[Tuple[str, float]]:
        """
        Find knowledge elements related to the given embedding.
        
        Args:
            embedding (np.ndarray): The reference embedding
            exclude_type (str, optional): Knowledge type to exclude
            k (int, optional): Number of related elements to return
        
        Returns:
            List[Tuple[str, float]]: List of (node_id, similarity) pairs
        """
        similarities = []
        
        for node_id, node_data in self.knowledge_graph['nodes'].items():
            # Skip root nodes and nodes of excluded type
            if node_data.get('type') == 'root':
                continue
            
            if exclude_type and node_data.get('knowledge_type') == exclude_type:
                continue
            
            if 'embedding' in node_data:
                # Calculate cosine similarity
                node_embedding = node_data['embedding']
                similarity = self._cosine_similarity(embedding, node_embedding)
                similarities.append((node_id, similarity))
        
        # Sort by similarity (descending) and return top k
        similarities.sort(key=lambda x: x[1], reverse=True)
        return similarities[:k]
    
    def _cosine_similarity(self, a: np.ndarray, b: np.ndarray) -> float:
        """
        Calculate cosine similarity between two vectors.
        
        Args:
            a (np.ndarray): First vector
            b (np.ndarray): Second vector
        
        Returns:
            float: Cosine similarity (0-1)
        """
        norm_a = np.linalg.norm(a)
        norm_b = np.linalg.norm(b)
        
        if norm_a == 0 or norm_b == 0:
            return 0
        
        return np.dot(a, b) / (norm_a * norm_b)
    
    def retrieve_knowledge(self, 
                          query: Union[str, np.ndarray], 
                          knowledge_type: str = None,
                          top_k: int = 5,
                          threshold: float = None) -> List[Dict]:
        """
        Retrieve knowledge elements based on the query.
        
        Args:
            query (Union[str, np.ndarray]): Query string or embedding
            knowledge_type (str, optional): Type of knowledge to retrieve
            top_k (int, optional): Number of results to return
            threshold (float, optional): Minimum similarity threshold
        
        Returns:
            List[Dict]: Retrieved knowledge elements with metadata
        """
        threshold = threshold or self.config['retrieval_threshold']
        
        # Convert string query to embedding if needed
        if isinstance(query, str):
            query_embedding = self._query_to_embedding(query)
        else:
            query_embedding = query
            
        # Check cache first
        cache_key = self._generate_cache_key(query_embedding, knowledge_type, top_k, threshold)
        if cache_key in self.retrieval_cache:
            logger.debug(f"Cache hit for query: {str(query)[:50]}...")
            return self.retrieval_cache[cache_key]
        
        # Search in appropriate knowledge base(s)
        results = []
        
        if knowledge_type == 'episodic' or knowledge_type is None:
            episodic_results = self.episodic_kb.retrieve(query_embedding, top_k=top_k, threshold=threshold)
            results.extend(self._format_retrieval_results(episodic_results, 'episodic'))
            
        if knowledge_type == 'semantic' or knowledge_type is None:
            semantic_results = self.semantic_kb.retrieve(query_embedding, top_k=top_k, threshold=threshold)
            results.extend(self._format_retrieval_results(semantic_results, 'semantic'))
            
        if knowledge_type == 'procedural' or knowledge_type is None:
            procedural_results = self.procedural_kb.retrieve(query_embedding, top_k=top_k, threshold=threshold)
            results.extend(self._format_retrieval_results(procedural_results, 'procedural'))
        
        # Sort by relevance and limit to top_k
        results.sort(key=lambda x: x['relevance'], reverse=True)
        results = results[:top_k]
        
        # Update cache with results
        self.retrieval_cache[cache_key] = results
        
        # Update activation levels for retrieved items
        for result in results:
            node_id = result['id']
            self._update_activation(node_id)
        
        return results
    
    def _query_to_embedding(self, query: str) -> np.ndarray:
        """
        Convert a text query to an embedding vector.
        
        Args:
            query (str): Text query
        
        Returns:
            np.ndarray: Embedding vector
        """
        # This would typically use a language model or embedding service
        # For now, we'll use a simple hash-based approach as a placeholder
        
        # In a production system, this would use a proper embedding model
        hash_value = hash(query) % 1000000
        np.random.seed(hash_value)
        
        # Generate a pseudo-random embedding
        embedding = np.random.randn(self.config['embedding_dim'])
        # Normalize to unit length
        embedding = embedding / np.linalg.norm(embedding)
        
        return embedding
    
    def _generate_cache_key(self, 
                           query_embedding: np.ndarray, 
                           knowledge_type: str,
                           top_k: int,
                           threshold: float) -> str:
        """
        Generate a cache key for the retrieval query.
        
        Args:
            query_embedding (np.ndarray): Query embedding
            knowledge_type (str): Type of knowledge
            top_k (int): Number of results
            threshold (float): Similarity threshold
        
        Returns:
            str: Cache key
        """
        # Use a hash of the embedding and parameters
        embedding_hash = hash(query_embedding.tobytes())
        return f"{embedding_hash}_{knowledge_type}_{top_k}_{threshold}"
    
    def _format_retrieval_results(self, 
                                 raw_results: List[Tuple], 
                                 knowledge_type: str) -> List[Dict]:
        """
        Format raw retrieval results into a standard format.
        
        Args:
            raw_results (List[Tuple]): Raw results from knowledge bases
            knowledge_type (str): Type of knowledge
        
        Returns:
            List[Dict]: Formatted results
        """
        formatted_results = []
        
        for result_id, similarity in raw_results:
            # Get complete node data from knowledge graph
            node_data = self.knowledge_graph['nodes'].get(result_id, {})
            
            # Get content based on knowledge type
            if knowledge_type == 'episodic':
                content = self.episodic_kb.get_episode(result_id)
            elif knowledge_type == 'semantic':
                content = self.semantic_kb.get_concept(result_id)
            elif knowledge_type == 'procedural':
                content = self.procedural_kb.get_procedure(result_id)
            else:
                content = None
            
            formatted_results.append({
                'id': result_id,
                'type': knowledge_type,
                'content': content,
                'relevance': similarity,
                'metadata': node_data.get('metadata', {}),
                'creation_time': node_data.get('creation_time', pd.Timestamp.now())
            })
        
        return formatted_results
    
    def _update_activation(self, node_id: str) -> None:
        """
        Update the activation level for a knowledge node.
        
        This increases the activation of the node and its neighbors,
        simulating spreading activation in associative memory.
        
        Args:
            node_id (str): ID of the node to activate
        """
        # Initialize activation if not present
        if node_id not in self.activation_levels:
            self.activation_levels[node_id] = 0.0
        
        # Increase activation (max 1.0)
        self.activation_levels[node_id] = min(1.0, self.activation_levels[node_id] + 0.3)
        
        # Spread activation to connected nodes (with decay)
        connected_nodes = self._get_connected_nodes(node_id)
        for connected_id, edge_weight in connected_nodes:
            if connected_id not in self.activation_levels:
                self.activation_levels[connected_id] = 0.0
            
            # Activation spreads proportionally to edge weight
            activation_delta = 0.3 * edge_weight
            self.activation_levels[connected_id] = min(1.0, 
                                                     self.activation_levels[connected_id] + activation_delta)
        
        # Schedule activation decay
        self._schedule_activation_decay()
    
    def _get_connected_nodes(self, node_id: str) -> List[Tuple[str, float]]:
        """
        Get nodes connected to the given node.
        
        Args:
            node_id (str): Source node ID
        
        Returns:
            List[Tuple[str, float]]: List of (connected_node_id, edge_weight) pairs
        """
        connected_nodes = []
        
        for edge_id, edge_data in self.knowledge_graph['edges'].items():
            if edge_data['source'] == node_id:
                connected_nodes.append((edge_data['target'], edge_data.get('weight', 0.5)))
            elif edge_data['target'] == node_id:
                connected_nodes.append((edge_data['source'], edge_data.get('weight', 0.5)))
        
        return connected_nodes
    
    def _schedule_activation_decay(self) -> None:
        """
        Schedule activation decay for all activated nodes.
        
        This simulates memory decay over time.
        """
        # In a real implementation, this would use a timer or background task
        # For now, we'll just apply decay immediately
        
        decay_factor = 0.05
        for node_id in list(self.activation_levels.keys()):
            new_activation = self.activation_levels[node_id] * (1 - decay_factor)
            
            if new_activation < 0.01:
                # Remove nodes with negligible activation
                del self.activation_levels[node_id]
            else:
                self.activation_levels[node_id] = new_activation
    
    def consolidate_knowledge(self) -> None:
        """
        Consolidate knowledge across all knowledge bases.
        
        This process strengthens important connections, prunes weak ones,
        and reorganizes knowledge for more efficient retrieval.
        """
        logger.info("Starting knowledge consolidation process")
        
        # Step 1: Identify high-activation knowledge nodes
        active_nodes = {node_id: activation 
                       for node_id, activation in self.activation_levels.items() 
                       if activation > 0.3}
        
        # Step 2: Strengthen connections between co-activated nodes
        self._strengthen_coactivated_connections(active_nodes)
        
        # Step 3: Prune weak connections
        self._prune_weak_connections()
        
        # Step 4: Consolidate episodic memories
        self.episodic_kb.consolidate()
        
        # Step 5: Update semantic knowledge
        self.semantic_kb.update_ontology()
        
        # Step 6: Optimize procedural knowledge
        self.procedural_kb.optimize()
        
        # Step 7: Run integrator's consolidation process
        self.integrator.consolidate_knowledge(
            knowledge_graph=self.knowledge_graph,
            episodic_kb=self.episodic_kb,
            semantic_kb=self.semantic_kb,
            procedural_kb=self.procedural_kb
        )
        
        # Step 8: Clear the retrieval cache
        self.retrieval_cache.clear()
        
        logger.info("Knowledge consolidation complete")
    
    def _strengthen_coactivated_connections(self, active_nodes: Dict[str, float]) -> None:
        """
        Strengthen connections between co-activated nodes.
        
        Args:
            active_nodes (Dict[str, float]): Map of node IDs to activation levels
        """
        active_node_ids = list(active_nodes.keys())
        
        for i, node_id1 in enumerate(active_node_ids):
            for node_id2 in active_node_ids[i+1:]:
                # Check if connection already exists
                edge_id1 = f"{node_id1}_{node_id2}"
                edge_id2 = f"{node_id2}_{node_id1}"
                
                if edge_id1 in self.knowledge_graph['edges']:
                    # Strengthen existing connection
                    current_weight = self.knowledge_graph['edges'][edge_id1].get('weight', 0.5)
                    activation_product = active_nodes[node_id1] * active_nodes[node_id2]
                    
                    # Increase weight based on co-activation
                    new_weight = current_weight + (1 - current_weight) * 0.1 * activation_product
                    self.knowledge_graph['edges'][edge_id1]['weight'] = new_weight
                    
                elif edge_id2 in self.knowledge_graph['edges']:
                    # Strengthen existing connection (reverse direction)
                    current_weight = self.knowledge_graph['edges'][edge_id2].get('weight', 0.5)
                    activation_product = active_nodes[node_id1] * active_nodes[node_id2]
                    
                    # Increase weight based on co-activation
                    new_weight = current_weight + (1 - current_weight) * 0.1 * activation_product
                    self.knowledge_graph['edges'][edge_id2]['weight'] = new_weight
                    
                else:
                    # Check if nodes are similar enough to create a new connection
                    embedding1 = self.knowledge_graph['nodes'][node_id1].get('embedding')
                    embedding2 = self.knowledge_graph['nodes'][node_id2].get('embedding')
                    
                    if embedding1 is not None and embedding2 is not None:
                        similarity = self._cosine_similarity(embedding1, embedding2)
                        
                        # Create new connection if similarity is above threshold
                        if similarity > 0.6:
                            self._add_edge(node_id1, node_id2, {
                                'type': 'association',
                                'weight': similarity,
                                'creation_time': pd.Timestamp.now()
                            })
    
    def _prune_weak_connections(self) -> None:
        """
        Prune weak connections from the knowledge graph.
        
        This removes connections with low weights to prevent graph explosion
        and maintain efficiency.
        """
        # Identify weak edges
        weak_edges = []
        
        for edge_id, edge_data in self.knowledge_graph['edges'].items():
            # Skip root connections
            if edge_data['source'].endswith('_root') or edge_data['target'].endswith('_root'):
                continue
                
            # Check edge weight
            if edge_data.get('weight', 0.5) < 0.2:
                weak_edges.append(edge_id)
        
        # Remove weak edges
        for edge_id in weak_edges:
            del self.knowledge_graph['edges'][edge_id]
            self.knowledge_graph['metadata']['edge_count'] -= 1
    
    def connect_global_workspace(self, global_workspace) -> None:
        """
        Connect to the Global Workspace component.
        
        Args:
            global_workspace: The Global Workspace component
        """
        self.global_workspace_connection = global_workspace
        logger.info("Connected to Global Workspace")
    
    def connect_neuro_symbolic(self, neuro_symbolic) -> None:
        """
        Connect to the Neuro-Symbolic Integration component.
        
        Args:
            neuro_symbolic: The Neuro-Symbolic Integration component
        """
        self.neuro_symbolic_connection = neuro_symbolic
        logger.info("Connected to Neuro-Symbolic Integration")
    
    def connect_meta_cognitive(self, meta_cognitive) -> None:
        """
        Connect to the Meta-Cognitive System component.
        
        Args:
            meta_cognitive: The Meta-Cognitive System component
        """
        self.meta_cognitive_connection = meta_cognitive
        logger.info("Connected to Meta-Cognitive System")
    
    def broadcast_to_global_workspace(self, content: Dict) -> None:
        """
        Broadcast knowledge to the Global Workspace.
        
        Args:
            content (Dict): Content to broadcast
        """
        if self.global_workspace_connection:
            self.global_workspace_connection.receive_from_knowledge(content)
            logger.debug(f"Broadcasted to Global Workspace: {str(content)[:50]}...")
    
    def receive_from_global_workspace(self, content: Dict) -> None:
        """
        Receive content from the Global Workspace.
        
        Args:
            content (Dict): Received content
        """
        logger.debug(f"Received from Global Workspace: {str(content)[:50]}...")
        
        # Process based on content type
        if content.get('type') == 'query':
            # Handle knowledge query
            results = self.retrieve_knowledge(
                query=content.get('query'),
                knowledge_type=content.get('knowledge_type'),
                top_k=content.get('top_k', 5),
                threshold=content.get('threshold', 0.7)
            )
            
            # Send results back to Global Workspace
            self.broadcast_to_global_workspace({
                'type': 'query_response',
                'query_id': content.get('query_id'),
                'results': results
            })
            
        elif content.get('type') == 'store':
            # Store new knowledge
            knowledge_id = self.add_knowledge(
                knowledge_type=content.get('knowledge_type'),
                content=content.get('content'),
                metadata=content.get('metadata', {})
            )
            
            # Confirm storage
            self.broadcast_to_global_workspace({
                'type': 'store_response',
                'store_id': content.get('store_id'),
                'knowledge_id': knowledge_id,
                'status': 'success'
            })
    
    def get_knowledge_stats(self) -> Dict:
        """
        Get statistics about the knowledge system.
        
        Returns:
            Dict: Knowledge system statistics
        """
        return {
            'total_nodes': self.knowledge_graph['metadata']['node_count'],
            'total_edges': self.knowledge_graph['metadata']['edge_count'],
            'episodic_count': self.episodic_kb.get_count(),
            'semantic_count': self.semantic_kb.get_count(),
            'procedural_count': self.procedural_kb.get_count(),
            'active_nodes': len(self.activation_levels),
            'cache_size': len(self.retrieval_cache),
            'last_updated': self.knowledge_graph['metadata']['last_updated'],
            'version': self.knowledge_graph['metadata']['version']
        }
    
    def export_knowledge_graph(self, format: str = 'dict') -> Union[Dict, str]:
        """
        Export the knowledge graph in the specified format.
        
        Args:
            format (str, optional): Export format ('dict', 'json', 'networkx')
        
        Returns:
            Union[Dict, str]: Exported knowledge graph
        """
        if format == 'dict':
            return self.knowledge_graph
        elif format == 'json':
            import json
            
            # Create a serializable copy
            serializable_graph = self._make_serializable(self.knowledge_graph)
            return json.dumps(serializable_graph)
        elif format == 'networkx':
            import networkx as nx
            
            # Create NetworkX graph
            G = nx.DiGraph()
            
            # Add nodes
            for node_id, node_data in self.knowledge_graph['nodes'].items():
                # Make node data serializable
                serializable_data = self._make_serializable(node_data)
                G.add_node(node_id, **serializable_data)
            
            # Add edges
            for edge_id, edge_data in self.knowledge_graph['edges'].items():
                source = edge_data['source']
                target = edge_data['target']
                
                # Remove source and target from edge attributes
                edge_attrs = {k: v for k, v in edge_data.items() if k not in ['source', 'target']}
                serializable_attrs = self._make_serializable(edge_attrs)
                
                G.add_edge(source, target, **serializable_attrs)
            
            return G
        else:
            raise ValueError(f"Unsupported export format: {format}")
    
    def _make_serializable(self, obj: Any) -> Any:
        """
        Make an object serializable for export.
        
        Args:
            obj (Any): Object to make serializable
        
        Returns:
            Any: Serializable object
        """
        if isinstance(obj, dict):
            result = {}
            for k, v in obj.items():
                result[k] = self._make_serializable(v)
            return result
        elif isinstance(obj, list):
            return [self._make_serializable(item) for item in obj]
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, pd.Timestamp):
            return obj.isoformat()
        elif np.issubdtype(type(obj), np.number):
            return float(obj)
        else:
            return obj
    
    def import_knowledge_graph(self, graph_data: Union[Dict, str]) -> None:
        """
        Import a knowledge graph.
        
        Args:
            graph_data (Union[Dict, str]): Knowledge graph data
        """
        logger.info("Importing knowledge graph")
        
        # Convert JSON string to dict if needed
        if isinstance(graph_data, str):
            import json
            graph_data = json.loads(graph_data)
        
        # Update local knowledge graph
        self.knowledge_graph = graph_data
        
        # Notify knowledge bases of the import
        self.episodic_kb.sync_with_graph(self.knowledge_graph)
        self.semantic_kb.sync_with_graph(self.knowledge_graph)
        self.procedural_kb.sync_with_graph(self.knowledge_graph)
        
        # Clear caches
        self.retrieval_cache.clear()
        self.activation_levels.clear()
        
        logger.info("Knowledge graph import complete")

# Make knowledge management components available at the module level
__all__ = [
    'KnowledgeManagementSystem',
    'EpisodicKnowledgeBase',
    'SemanticKnowledge',
    'ProceduralKnowledgeBase',
    'KnowledgeIntegration'
]