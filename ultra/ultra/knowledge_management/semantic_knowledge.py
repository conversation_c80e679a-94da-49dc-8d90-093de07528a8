#!/usr/bin/env python3
"""
ULTRA: Ultimate Learning & Thought Reasoning Architecture
Semantic Knowledge Management Module

This module implements a comprehensive semantic knowledge system that organizes
concepts, facts, relations and ontological structures. It supports efficient storage,
retrieval, and reasoning over semantic knowledge with support for knowledge graph
representation, embedding-based retrieval, and logical inference.

Classes:
    SemanticConcept: Represents a concept with properties and relations
    SemanticRelation: Represents a relation between concepts
    ConceptHierarchy: Implements a hierarchical organization of concepts
    KnowledgeTriple: Represents a subject-predicate-object statement
    OntologyClass: Represents a formal class definition
    KnowledgeEmbedding: Manages concept and relation embeddings
    ConceptIndex: Indexes concepts for efficient retrieval
    RelationIndex: Indexes relations for efficient retrieval
    SchemaValidator: Validates knowledge against a defined schema
    InferenceEngine: Performs logical inference over the knowledge base
    SemanticKnowledge: Main class orchestrating the semantic knowledge system

Mathematical basis:
    Concept similarity: sim(c1, c2) = cos(e_c1, e_c2) - Cosine similarity of embeddings
    Relation inference: P(r(c1, c2)) = sigmoid(e_c1 · M_r · e_c2) - Bilinear relation scoring
    Subsumption: sub(c1, c2) = 1 if c1 ∈ ancestors(c2) - Hierarchical subsumption
    Inference strength: S(c1, r, c2) = P(r(c1, c2)) · conf(r) - Inference with confidence
"""

import numpy as np
import time
import uuid
import logging
import json
import os
import re
from typing import Dict, List, Tuple, Set, Any, Optional, Union, Callable, Iterator
from dataclasses import dataclass, field
from collections import defaultdict, deque
import networkx as nx
from sklearn.metrics.pairwise import cosine_similarity
import torch
import faiss

# Configure logging
logger = logging.getLogger(__name__)


@dataclass
class SemanticConcept:
    """
    Represents a concept in the semantic knowledge base, with properties
    and references to related concepts.
    """
    id: str
    name: str
    description: str = ""
    category: str = "entity"
    properties: Dict[str, Any] = field(default_factory=dict)
    aliases: List[str] = field(default_factory=list)
    embedding: Optional[np.ndarray] = None
    data_type: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """Initialize and validate after creation."""
        # Ensure embedding is numpy array if provided
        if self.embedding is not None and not isinstance(self.embedding, np.ndarray):
            self.embedding = np.array(self.embedding, dtype=np.float32)
            # Normalize embedding to unit length
            norm = np.linalg.norm(self.embedding)
            if norm > 0:
                self.embedding = self.embedding / norm
                
        # Ensure name is string
        self.name = str(self.name)
        
        # Add name to aliases if not already present
        if self.name not in self.aliases:
            self.aliases.append(self.name)
            
        # Validate category
        valid_categories = {"entity", "event", "abstract", "physical", "agent", "process"}
        if self.category not in valid_categories and not self.category.startswith("custom:"):
            logger.warning(f"Concept category '{self.category}' is not in standard categories: {valid_categories}")
            
    def add_property(self, key: str, value: Any):
        """
        Add or update a property of the concept.
        
        Args:
            key: Property name
            value: Property value
        """
        self.properties[key] = value
        
    def add_alias(self, alias: str):
        """
        Add an alternative name/alias for the concept.
        
        Args:
            alias: Alternative name for the concept
        """
        if alias not in self.aliases:
            self.aliases.append(alias)
            
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert concept to dictionary for serialization.
        
        Returns:
            Dictionary representation of the concept
        """
        result = {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'category': self.category,
            'properties': self.properties,
            'aliases': self.aliases,
            'data_type': self.data_type,
            'metadata': self.metadata
        }
        
        if self.embedding is not None:
            result['embedding'] = self.embedding.tolist()
            
        return result
        
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SemanticConcept':
        """
        Create concept from dictionary.
        
        Args:
            data: Dictionary representation of concept
            
        Returns:
            SemanticConcept instance
        """
        # Prepare embedding if present
        embedding = None
        if 'embedding' in data:
            embedding = np.array(data['embedding'], dtype=np.float32)
            
        return cls(
            id=data['id'],
            name=data['name'],
            description=data.get('description', ''),
            category=data.get('category', 'entity'),
            properties=data.get('properties', {}),
            aliases=data.get('aliases', []),
            embedding=embedding,
            data_type=data.get('data_type'),
            metadata=data.get('metadata', {})
        )
        
    def __str__(self) -> str:
        return f"Concept({self.name}, id={self.id}, category={self.category})"
        
    def __repr__(self) -> str:
        return self.__str__()


@dataclass
class SemanticRelation:
    """
    Represents a semantic relation between concepts in the knowledge base.
    """
    id: str
    name: str
    source_id: str
    target_id: str
    relation_type: str
    weight: float = 1.0
    bidirectional: bool = False
    properties: Dict[str, Any] = field(default_factory=dict)
    embedding: Optional[np.ndarray] = None
    provenance: Optional[str] = None
    confidence: float = 1.0
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """Initialize and validate after creation."""
        # Ensure embedding is numpy array if provided
        if self.embedding is not None and not isinstance(self.embedding, np.ndarray):
            self.embedding = np.array(self.embedding, dtype=np.float32)
            # Normalize embedding to unit length
            norm = np.linalg.norm(self.embedding)
            if norm > 0:
                self.embedding = self.embedding / norm
                
        # Ensure name is string
        self.name = str(self.name)
        
        # Ensure weight is between 0 and 1
        self.weight = max(0.0, min(1.0, self.weight))
        
        # Ensure confidence is between 0 and 1
        self.confidence = max(0.0, min(1.0, self.confidence))
        
        # Validate relation_type
        valid_types = {"is_a", "part_of", "has_property", "related_to", "causes", 
                      "located_in", "occurs_before", "similar_to", "opposite_of", 
                      "derives_from", "instance_of"}
        if self.relation_type not in valid_types and not self.relation_type.startswith("custom:"):
            logger.warning(f"Relation type '{self.relation_type}' is not in standard types: {valid_types}")
            
    def add_property(self, key: str, value: Any):
        """
        Add or update a property of the relation.
        
        Args:
            key: Property name
            value: Property value
        """
        self.properties[key] = value
        
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert relation to dictionary for serialization.
        
        Returns:
            Dictionary representation of the relation
        """
        result = {
            'id': self.id,
            'name': self.name,
            'source_id': self.source_id,
            'target_id': self.target_id,
            'relation_type': self.relation_type,
            'weight': self.weight,
            'bidirectional': self.bidirectional,
            'properties': self.properties,
            'provenance': self.provenance,
            'confidence': self.confidence,
            'metadata': self.metadata
        }
        
        if self.embedding is not None:
            result['embedding'] = self.embedding.tolist()
            
        return result
        
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SemanticRelation':
        """
        Create relation from dictionary.
        
        Args:
            data: Dictionary representation of relation
            
        Returns:
            SemanticRelation instance
        """
        # Prepare embedding if present
        embedding = None
        if 'embedding' in data:
            embedding = np.array(data['embedding'], dtype=np.float32)
            
        return cls(
            id=data['id'],
            name=data['name'],
            source_id=data['source_id'],
            target_id=data['target_id'],
            relation_type=data['relation_type'],
            weight=data.get('weight', 1.0),
            bidirectional=data.get('bidirectional', False),
            properties=data.get('properties', {}),
            embedding=embedding,
            provenance=data.get('provenance'),
            confidence=data.get('confidence', 1.0),
            metadata=data.get('metadata', {})
        )
        
    def __str__(self) -> str:
        return f"Relation({self.name}, {self.source_id} -> {self.target_id}, type={self.relation_type})"
        
    def __repr__(self) -> str:
        return self.__str__()


class ConceptHierarchy:
    """
    Implements a hierarchical organization of concepts with support
    for taxonomic reasoning and subsumption.
    """
    
    def __init__(self, root_concept_id: Optional[str] = None):
        """
        Initialize concept hierarchy.
        
        Args:
            root_concept_id: Optional ID of root concept
        """
        # Directed graph representing the hierarchy
        self.graph = nx.DiGraph()
        
        # Store the root concept ID
        self.root_id = root_concept_id
        
        # If root concept is provided, add it to the graph
        if root_concept_id:
            self.graph.add_node(root_concept_id)
            
    def add_concept(self, concept_id: str, parent_id: Optional[str] = None):
        """
        Add a concept to the hierarchy.
        
        Args:
            concept_id: ID of the concept to add
            parent_id: Optional ID of parent concept
            
        Returns:
            True if added successfully, False otherwise
        """
        # Check if concept already exists
        if concept_id in self.graph:
            return False
            
        # Add concept node
        self.graph.add_node(concept_id)
        
        # If parent is provided, add edge
        if parent_id:
            if parent_id not in self.graph:
                logger.warning(f"Parent concept {parent_id} not found, adding it.")
                self.graph.add_node(parent_id)
                
            # Add edge from parent to concept
            self.graph.add_edge(parent_id, concept_id, relation_type='is_a')
        elif not self.root_id:
            # If no parent and no root exists, make this the root
            self.root_id = concept_id
            
        return True
        
    def add_subconcept(self, concept_id: str, parent_id: str):
        """
        Add a concept as a subconcept of another.
        
        Args:
            concept_id: ID of the concept to add
            parent_id: ID of parent concept
            
        Returns:
            True if added successfully, False otherwise
        """
        return self.add_concept(concept_id, parent_id)
        
    def remove_concept(self, concept_id: str, reconnect_children: bool = True):
        """
        Remove a concept from the hierarchy.
        
        Args:
            concept_id: ID of the concept to remove
            reconnect_children: Whether to reconnect children to parent
            
        Returns:
            True if removed successfully, False if not found
        """
        if concept_id not in self.graph:
            return False
            
        # Get parent and children before removal
        parents = list(self.graph.predecessors(concept_id))
        children = list(self.graph.successors(concept_id))
        
        # If root is being removed, update root
        if concept_id == self.root_id:
            if children:
                self.root_id = children[0]  # Arbitrary choice of new root
            else:
                self.root_id = None
                
        # Reconnect children to parent if requested
        if reconnect_children and parents and children:
            parent_id = parents[0]  # Use first parent
            for child_id in children:
                self.graph.add_edge(parent_id, child_id, relation_type='is_a')
                
        # Remove concept
        self.graph.remove_node(concept_id)
        
        return True
        
    def get_parent(self, concept_id: str) -> Optional[str]:
        """
        Get parent of a concept.
        
        Args:
            concept_id: ID of the concept
            
        Returns:
            ID of parent concept, or None if no parent or not found
        """
        if concept_id not in self.graph:
            return None
            
        parents = list(self.graph.predecessors(concept_id))
        return parents[0] if parents else None
        
    def get_children(self, concept_id: str) -> List[str]:
        """
        Get immediate children of a concept.
        
        Args:
            concept_id: ID of the concept
            
        Returns:
            List of child concept IDs
        """
        if concept_id not in self.graph:
            return []
            
        return list(self.graph.successors(concept_id))
        
    def get_ancestors(self, concept_id: str) -> List[str]:
        """
        Get all ancestors of a concept.
        
        Args:
            concept_id: ID of the concept
            
        Returns:
            List of ancestor concept IDs
        """
        if concept_id not in self.graph:
            return []
            
        # Perform breadth-first search for ancestors
        ancestors = []
        queue = deque([concept_id])
        visited = {concept_id}
        
        while queue:
            current = queue.popleft()
            
            # Skip the original concept
            if current != concept_id:
                ancestors.append(current)
                
            # Add predecessors to queue
            for pred in self.graph.predecessors(current):
                if pred not in visited:
                    visited.add(pred)
                    queue.append(pred)
                    
        return ancestors
        
    def get_descendants(self, concept_id: str) -> List[str]:
        """
        Get all descendants of a concept.
        
        Args:
            concept_id: ID of the concept
            
        Returns:
            List of descendant concept IDs
        """
        if concept_id not in self.graph:
            return []
            
        # Get all descendants using networkx
        descendants = nx.descendants(self.graph, concept_id)
        return list(descendants)
        
    def get_siblings(self, concept_id: str) -> List[str]:
        """
        Get siblings of a concept (concepts with same parent).
        
        Args:
            concept_id: ID of the concept
            
        Returns:
            List of sibling concept IDs
        """
        if concept_id not in self.graph:
            return []
            
        # Get parent of the concept
        parent_id = self.get_parent(concept_id)
        if not parent_id:
            return []
            
        # Get all children of the parent
        siblings = self.get_children(parent_id)
        
        # Remove the original concept
        if concept_id in siblings:
            siblings.remove(concept_id)
            
        return siblings
        
    def is_subclass_of(self, concept_id: str, ancestor_id: str) -> bool:
        """
        Check if concept is a subclass of another concept.
        
        Args:
            concept_id: ID of the concept to check
            ancestor_id: ID of potential ancestor
            
        Returns:
            True if concept is a subclass of ancestor, False otherwise
        """
        if concept_id not in self.graph or ancestor_id not in self.graph:
            return False
            
        # Check if there's a path from ancestor to concept
        # A path means there's an 'is_a' relationship
        return nx.has_path(self.graph, ancestor_id, concept_id)
        
    def get_lowest_common_ancestor(self, concept_id1: str, concept_id2: str) -> Optional[str]:
        """
        Find the lowest common ancestor of two concepts.
        
        Args:
            concept_id1: ID of first concept
            concept_id2: ID of second concept
            
        Returns:
            ID of lowest common ancestor, or None if not found
        """
        if concept_id1 not in self.graph or concept_id2 not in self.graph:
            return None
            
        # Get ancestors of both concepts
        ancestors1 = set(self.get_ancestors(concept_id1))
        ancestors2 = set(self.get_ancestors(concept_id2))
        
        # Find common ancestors
        common_ancestors = ancestors1.intersection(ancestors2)
        
        if not common_ancestors:
            return None
            
        # Find the lowest common ancestor (closest to concepts)
        # This is the one with the longest path to the root
        lca = None
        max_depth = -1
        
        for ancestor in common_ancestors:
            # Calculate depth as path length to root
            try:
                depth = len(nx.shortest_path(self.graph, self.root_id, ancestor)) - 1
                if depth > max_depth:
                    max_depth = depth
                    lca = ancestor
            except (nx.NetworkXNoPath, nx.NodeNotFound):
                continue
                
        return lca
        
    def get_depth(self, concept_id: str) -> int:
        """
        Get the depth of a concept in the hierarchy.
        
        Args:
            concept_id: ID of the concept
            
        Returns:
            Depth in the hierarchy (0 for root), -1 if not found
        """
        if concept_id not in self.graph:
            return -1
            
        if concept_id == self.root_id:
            return 0
            
        try:
            # Calculate depth as path length from root
            return len(nx.shortest_path(self.graph, self.root_id, concept_id)) - 1
        except (nx.NetworkXNoPath, nx.NodeNotFound):
            return -1
            
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert hierarchy to dictionary for serialization.
        
        Returns:
            Dictionary representation of the hierarchy
        """
        return {
            'root_id': self.root_id,
            'edges': [(src, dst) for src, dst in self.graph.edges()]
        }
        
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ConceptHierarchy':
        """
        Create hierarchy from dictionary.
        
        Args:
            data: Dictionary representation of hierarchy
            
        Returns:
            ConceptHierarchy instance
        """
        hierarchy = cls(root_concept_id=data.get('root_id'))
        
        # Add edges
        for src, dst in data.get('edges', []):
            hierarchy.graph.add_node(src)
            hierarchy.graph.add_node(dst)
            hierarchy.graph.add_edge(src, dst, relation_type='is_a')
            
        return hierarchy


@dataclass
class KnowledgeTriple:
    """
    Represents a subject-predicate-object statement in the knowledge base.
    """
    id: str
    subject_id: str
    predicate: str
    object_id: str
    confidence: float = 1.0
    provenance: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """Initialize and validate after creation."""
        # Ensure confidence is between 0 and 1
        self.confidence = max(0.0, min(1.0, self.confidence))
        
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert triple to dictionary for serialization.
        
        Returns:
            Dictionary representation of the triple
        """
        return {
            'id': self.id,
            'subject_id': self.subject_id,
            'predicate': self.predicate,
            'object_id': self.object_id,
            'confidence': self.confidence,
            'provenance': self.provenance,
            'metadata': self.metadata
        }
        
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'KnowledgeTriple':
        """
        Create triple from dictionary.
        
        Args:
            data: Dictionary representation of triple
            
        Returns:
            KnowledgeTriple instance
        """
        return cls(
            id=data['id'],
            subject_id=data['subject_id'],
            predicate=data['predicate'],
            object_id=data['object_id'],
            confidence=data.get('confidence', 1.0),
            provenance=data.get('provenance'),
            metadata=data.get('metadata', {})
        )
        
    def __str__(self) -> str:
        return f"Triple({self.subject_id}, {self.predicate}, {self.object_id})"
        
    def __repr__(self) -> str:
        return self.__str__()


@dataclass
class OntologyClass:
    """
    Represents a formal class definition in an ontology.
    """
    id: str
    name: str
    description: str = ""
    properties: Dict[str, Dict[str, Any]] = field(default_factory=dict)
    restrictions: Dict[str, Any] = field(default_factory=dict)
    equivalences: List[str] = field(default_factory=list)
    disjoint_with: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """Initialize and validate after creation."""
        # Ensure name is string
        self.name = str(self.name)
        
    def add_property(self, name: str, data_type: str, 
                   required: bool = False, 
                   restrictions: Optional[Dict[str, Any]] = None):
        """
        Add a property definition to the class.
        
        Args:
            name: Property name
            data_type: Data type of the property
            required: Whether the property is required
            restrictions: Optional restrictions on the property
        """
        self.properties[name] = {
            'data_type': data_type,
            'required': required,
            'restrictions': restrictions or {}
        }
        
    def add_restriction(self, relation: str, target_class: str, 
                       restriction_type: str,
                       cardinality: Optional[int] = None):
        """
        Add a property restriction to the class.
        
        Args:
            relation: Relation type (predicate)
            target_class: Target class ID
            restriction_type: Type of restriction (all, some, exactly, etc.)
            cardinality: Optional cardinality for the restriction
        """
        if relation not in self.restrictions:
            self.restrictions[relation] = []
            
        restriction = {
            'type': restriction_type,
            'target': target_class
        }
        
        if cardinality is not None:
            restriction['cardinality'] = cardinality
            
        self.restrictions[relation].append(restriction)
        
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert ontology class to dictionary for serialization.
        
        Returns:
            Dictionary representation of the ontology class
        """
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'properties': self.properties,
            'restrictions': self.restrictions,
            'equivalences': self.equivalences,
            'disjoint_with': self.disjoint_with,
            'metadata': self.metadata
        }
        
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'OntologyClass':
        """
        Create ontology class from dictionary.
        
        Args:
            data: Dictionary representation of ontology class
            
        Returns:
            OntologyClass instance
        """
        return cls(
            id=data['id'],
            name=data['name'],
            description=data.get('description', ''),
            properties=data.get('properties', {}),
            restrictions=data.get('restrictions', {}),
            equivalences=data.get('equivalences', []),
            disjoint_with=data.get('disjoint_with', []),
            metadata=data.get('metadata', {})
        )
        
    def __str__(self) -> str:
        return f"OntologyClass({self.name}, id={self.id})"
        
    def __repr__(self) -> str:
        return self.__str__()


class KnowledgeEmbedding:
    """
    Manages concept and relation embeddings for the knowledge base.
    """
    
    def __init__(self, embedding_dim: int = 768,
                concept_embeddings: Optional[Dict[str, np.ndarray]] = None,
                relation_embeddings: Optional[Dict[str, np.ndarray]] = None,
                embedding_model: Optional[Callable] = None,
                use_gpu: bool = False):
        """
        Initialize knowledge embedding manager.
        
        Args:
            embedding_dim: Dimension of embeddings
            concept_embeddings: Optional preloaded concept embeddings
            relation_embeddings: Optional preloaded relation embeddings
            embedding_model: Optional function for creating embeddings
            use_gpu: Whether to use GPU acceleration if available
        """
        self.embedding_dim = embedding_dim
        self.concept_embeddings = concept_embeddings or {}
        self.relation_embeddings = relation_embeddings or {}
        self.embedding_model = embedding_model
        self.use_gpu = use_gpu and torch.cuda.is_available()
        
        # Initialize FAISS index for similarity search
        if self.use_gpu:
            # GPU index
            res = faiss.StandardGpuResources()
            config = faiss.GpuIndexFlatConfig()
            config.device = 0  # Use first GPU
            self.index = faiss.GpuIndexFlatIP(res, embedding_dim, config)
        else:
            # CPU index - using inner product (cosine similarity for normalized vectors)
            self.index = faiss.IndexFlatIP(embedding_dim)
            
        # Mapping from position in index to concept ID
        self.idx_to_id = []
        # Mapping from concept ID to position in index
        self.id_to_idx = {}
        
        # Add existing concept embeddings to index
        self._build_index()
        
        # Relation matrices for bilinear scoring
        self.relation_matrices = {}
        
    def _build_index(self):
        """Build the FAISS index from existing embeddings."""
        if not self.concept_embeddings:
            return
            
        # Prepare embeddings for indexing
        embedding_array = np.vstack(list(self.concept_embeddings.values())).astype(np.float32)
        
        # Add to index
        self.index.add(embedding_array)
        
        # Update mappings
        self.idx_to_id = list(self.concept_embeddings.keys())
        self.id_to_idx = {concept_id: i for i, concept_id in enumerate(self.idx_to_id)}
        
    def _normalize_embedding(self, embedding: np.ndarray) -> np.ndarray:
        """
        Normalize embedding to unit length.
        
        Args:
            embedding: Embedding vector
            
        Returns:
            Normalized embedding
        """
        norm = np.linalg.norm(embedding)
        if norm > 0:
            embedding = embedding / norm
        return embedding.astype(np.float32)
        
    def add_concept_embedding(self, concept_id: str, embedding: np.ndarray):
        """
        Add or update a concept embedding.
        
        Args:
            concept_id: ID of the concept
            embedding: Embedding vector
        """
        # Normalize embedding
        embedding = self._normalize_embedding(embedding)
        
        # Check if concept already has an embedding
        if concept_id in self.concept_embeddings:
            # Update index
            old_idx = self.id_to_idx[concept_id]
            self.index.remove_ids(np.array([old_idx]))
            
        # Add to embeddings dictionary
        self.concept_embeddings[concept_id] = embedding
        
        # Add to index
        self.index.add(embedding.reshape(1, -1))
        
        # Update mappings
        idx = len(self.idx_to_id)
        self.idx_to_id.append(concept_id)
        self.id_to_idx[concept_id] = idx
        
    def add_relation_embedding(self, relation_id: str, embedding: np.ndarray):
        """
        Add or update a relation embedding.
        
        Args:
            relation_id: ID of the relation
            embedding: Embedding vector
        """
        # Normalize embedding
        embedding = self._normalize_embedding(embedding)
        
        # Add to embeddings dictionary
        self.relation_embeddings[relation_id] = embedding
        
        # Initialize relation matrix if needed
        if relation_id not in self.relation_matrices:
            # Initialize with identity matrix
            self.relation_matrices[relation_id] = np.eye(self.embedding_dim, dtype=np.float32)
            
    def create_concept_embedding(self, concept: SemanticConcept) -> np.ndarray:
        """
        Create an embedding for a concept.
        
        Args:
            concept: Concept to embed
            
        Returns:
            Embedding vector
        """
        # If concept already has an embedding, use it
        if concept.embedding is not None:
            embedding = self._normalize_embedding(concept.embedding)
            return embedding
            
        # If an embedding_model is provided, use it
        if self.embedding_model is not None:
            # Create input for embedding model
            embed_input = {
                'text': concept.name + ". " + concept.description,
                'name': concept.name,
                'aliases': concept.aliases
            }
            
            # Get embedding from model
            embedding = self.embedding_model(embed_input)
            return self._normalize_embedding(embedding)
            
        # Fallback: create random embedding
        logger.warning(f"Creating random embedding for concept {concept.name} - no embedding model available")
        embedding = np.random.randn(self.embedding_dim)
        return self._normalize_embedding(embedding)
        
    def create_relation_embedding(self, relation: SemanticRelation) -> np.ndarray:
        """
        Create an embedding for a relation.
        
        Args:
            relation: Relation to embed
            
        Returns:
            Embedding vector
        """
        # If relation already has an embedding, use it
        if relation.embedding is not None:
            embedding = self._normalize_embedding(relation.embedding)
            return embedding
            
        # If an embedding_model is provided, use it
        if self.embedding_model is not None:
            # Create input for embedding model
            embed_input = {
                'text': relation.name,
                'relation_type': relation.relation_type
            }
            
            # Get embedding from model
            embedding = self.embedding_model(embed_input)
            return self._normalize_embedding(embedding)
            
        # Fallback: create random embedding
        logger.warning(f"Creating random embedding for relation {relation.name} - no embedding model available")
        embedding = np.random.randn(self.embedding_dim)
        return self._normalize_embedding(embedding)
        
    def get_concept_embedding(self, concept_id: str) -> Optional[np.ndarray]:
        """
        Get embedding for a concept.
        
        Args:
            concept_id: ID of the concept
            
        Returns:
            Embedding vector, or None if not found
        """
        return self.concept_embeddings.get(concept_id)
        
    def get_relation_embedding(self, relation_id: str) -> Optional[np.ndarray]:
        """
        Get embedding for a relation.
        
        Args:
            relation_id: ID of the relation
            
        Returns:
            Embedding vector, or None if not found
        """
        return self.relation_embeddings.get(relation_id)
        
    def find_similar_concepts(self, query_embedding: np.ndarray, k: int = 10) -> List[Tuple[str, float]]:
        """
        Find concepts with embeddings similar to the query.
        
        Args:
            query_embedding: Query embedding vector
            k: Number of results to return
            
        Returns:
            List of (concept_id, similarity) tuples
        """
        if len(self.idx_to_id) == 0:
            return []  # No concepts in index
            
        # Normalize query embedding
        query_embedding = self._normalize_embedding(query_embedding)
        
        # Limit k to number of indexed concepts
        k = min(k, len(self.idx_to_id))
        
        # Search index
        similarities, indices = self.index.search(query_embedding.reshape(1, -1), k)
        
        # Convert to (concept_id, similarity) tuples
        results = []
        for i in range(similarities.shape[1]):
            idx = indices[0, i]
            sim = similarities[0, i]
            concept_id = self.idx_to_id[idx]
            results.append((concept_id, float(sim)))
            
        return results
        
    def get_most_similar_concepts(self, concept_id: str, k: int = 10) -> List[Tuple[str, float]]:
        """
        Get concepts most similar to a reference concept.
        
        Args:
            concept_id: ID of the reference concept
            k: Number of results to return
            
        Returns:
            List of (concept_id, similarity) tuples
        """
        # Get concept embedding
        embedding = self.get_concept_embedding(concept_id)
        if embedding is None:
            return []
            
        # Find similar concepts
        results = self.find_similar_concepts(embedding, k + 1)  # +1 to include the reference
        
        # Filter out the reference concept
        results = [(cid, sim) for cid, sim in results if cid != concept_id]
        
        return results[:k]
        
    def compute_relation_score(self, head_id: str, relation_id: str, tail_id: str) -> float:
        """
        Compute score for a triple using bilinear scoring.
        
        Args:
            head_id: ID of head concept
            relation_id: ID of relation
            tail_id: ID of tail concept
            
        Returns:
            Score between 0 and 1
        """
        # Get embeddings
        head_embedding = self.get_concept_embedding(head_id)
        relation_matrix = self.relation_matrices.get(relation_id)
        tail_embedding = self.get_concept_embedding(tail_id)
        
        if head_embedding is None or relation_matrix is None or tail_embedding is None:
            return 0.0
            
        # Compute bilinear score: head^T * M_r * tail
        score = head_embedding.dot(relation_matrix).dot(tail_embedding)
        
        # Apply sigmoid to get score in [0, 1]
        score = 1.0 / (1.0 + np.exp(-score))
        
        return float(score)
        
    def train_relation_matrix(self, relation_id: str, 
                            positive_pairs: List[Tuple[str, str]],
                            negative_pairs: Optional[List[Tuple[str, str]]] = None,
                            learning_rate: float = 0.01,
                            num_epochs: int = 50):
        """
        Train a relation matrix using positive and negative examples.
        
        Args:
            relation_id: ID of the relation
            positive_pairs: List of (head_id, tail_id) pairs that have the relation
            negative_pairs: Optional list of (head_id, tail_id) pairs that don't have the relation
            learning_rate: Learning rate for training
            num_epochs: Number of training epochs
        """
        # Get positive embeddings
        positive_heads = []
        positive_tails = []
        
        for head_id, tail_id in positive_pairs:
            head_embedding = self.get_concept_embedding(head_id)
            tail_embedding = self.get_concept_embedding(tail_id)
            
            if head_embedding is not None and tail_embedding is not None:
                positive_heads.append(head_embedding)
                positive_tails.append(tail_embedding)
                
        if not positive_heads:
            logger.warning(f"No valid positive pairs for relation {relation_id}")
            return
            
        # Get negative embeddings
        negative_heads = []
        negative_tails = []
        
        if negative_pairs:
            for head_id, tail_id in negative_pairs:
                head_embedding = self.get_concept_embedding(head_id)
                tail_embedding = self.get_concept_embedding(tail_id)
                
                if head_embedding is not None and tail_embedding is not None:
                    negative_heads.append(head_embedding)
                    negative_tails.append(tail_embedding)
                    
        # If no negative pairs provided, create random negatives
        if not negative_heads:
            # Create random negative pairs by shuffling positive tails
            import random
            negative_heads = positive_heads.copy()
            negative_tails = positive_tails.copy()
            random.shuffle(negative_tails)
            
        # Initialize relation matrix if not exists
        if relation_id not in self.relation_matrices:
            self.relation_matrices[relation_id] = np.eye(self.embedding_dim, dtype=np.float32)
            
        # Convert lists to arrays
        positive_heads = np.vstack(positive_heads)
        positive_tails = np.vstack(positive_tails)
        negative_heads = np.vstack(negative_heads)
        negative_tails = np.vstack(negative_tails)
        
        # Training loop
        matrix = self.relation_matrices[relation_id]
        
        for epoch in range(num_epochs):
            # Compute positive scores
            pos_scores = np.sum(positive_heads.dot(matrix) * positive_tails, axis=1)
            pos_probs = 1.0 / (1.0 + np.exp(-pos_scores))
            
            # Compute negative scores
            neg_scores = np.sum(negative_heads.dot(matrix) * negative_tails, axis=1)
            neg_probs = 1.0 / (1.0 + np.exp(-neg_scores))
            
            # Compute gradients
            pos_grad = np.zeros_like(matrix)
            for i in range(len(positive_heads)):
                h = positive_heads[i].reshape(-1, 1)
                t = positive_tails[i].reshape(1, -1)
                pos_grad += (1 - pos_probs[i]) * np.outer(h, t)
                
            neg_grad = np.zeros_like(matrix)
            for i in range(len(negative_heads)):
                h = negative_heads[i].reshape(-1, 1)
                t = negative_tails[i].reshape(1, -1)
                neg_grad += neg_probs[i] * np.outer(h, t)
                
            # Update matrix
            matrix += learning_rate * (pos_grad - neg_grad)
            
        # Update relation matrix
        self.relation_matrices[relation_id] = matrix
        
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert embeddings to dictionary for serialization.
        
        Returns:
            Dictionary representation of embeddings
        """
        return {
            'embedding_dim': self.embedding_dim,
            'concept_embeddings': {k: v.tolist() for k, v in self.concept_embeddings.items()},
            'relation_embeddings': {k: v.tolist() for k, v in self.relation_embeddings.items()},
            'relation_matrices': {k: v.tolist() for k, v in self.relation_matrices.items()}
        }
        
    @classmethod
    def from_dict(cls, data: Dict[str, Any], embedding_model: Optional[Callable] = None) -> 'KnowledgeEmbedding':
        """
        Create embeddings from dictionary.
        
        Args:
            data: Dictionary representation of embeddings
            embedding_model: Optional function for creating embeddings
            
        Returns:
            KnowledgeEmbedding instance
        """
        # Convert embeddings back to numpy arrays
        concept_embeddings = {k: np.array(v, dtype=np.float32) 
                             for k, v in data.get('concept_embeddings', {}).items()}
        relation_embeddings = {k: np.array(v, dtype=np.float32) 
                              for k, v in data.get('relation_embeddings', {}).items()}
        
        # Create instance
        embedding = cls(
            embedding_dim=data['embedding_dim'],
            concept_embeddings=concept_embeddings,
            relation_embeddings=relation_embeddings,
            embedding_model=embedding_model
        )
        
        # Add relation matrices
        for k, v in data.get('relation_matrices', {}).items():
            embedding.relation_matrices[k] = np.array(v, dtype=np.float32)
            
        return embedding


class ConceptIndex:
    """
    Indexes concepts for efficient retrieval based on various criteria.
    """
    
    def __init__(self):
        """Initialize concept index."""
        # Main index for concepts by ID
        self.concepts_by_id = {}
        
        # Index by name (normalized)
        self.concepts_by_name = defaultdict(list)
        
        # Index by alias (normalized)
        self.concepts_by_alias = defaultdict(list)
        
        # Index by category
        self.concepts_by_category = defaultdict(list)
        
        # Index by property value
        self.concepts_by_property = defaultdict(lambda: defaultdict(list))
        
    def _normalize_text(self, text: str) -> str:
        """
        Normalize text for consistent indexing.
        
        Args:
            text: Text to normalize
            
        Returns:
            Normalized text
        """
        return text.lower().strip()
        
    def add_concept(self, concept: SemanticConcept):
        """
        Add a concept to the index.
        
        Args:
            concept: Concept to add
        """
        # Add to ID index
        self.concepts_by_id[concept.id] = concept
        
        # Add to name index
        normalized_name = self._normalize_text(concept.name)
        self.concepts_by_name[normalized_name].append(concept.id)
        
        # Add to alias index
        for alias in concept.aliases:
            normalized_alias = self._normalize_text(alias)
            self.concepts_by_alias[normalized_alias].append(concept.id)
            
        # Add to category index
        self.concepts_by_category[concept.category].append(concept.id)
        
        # Add to property index
        for prop_name, prop_value in concept.properties.items():
            # Create a string representation of the property value for indexing
            if isinstance(prop_value, (str, int, float, bool)):
                str_value = str(prop_value)
                self.concepts_by_property[prop_name][str_value].append(concept.id)
                
    def remove_concept(self, concept_id: str):
        """
        Remove a concept from the index.
        
        Args:
            concept_id: ID of the concept to remove
        """
        # Check if concept exists
        if concept_id not in self.concepts_by_id:
            return
            
        concept = self.concepts_by_id[concept_id]
        
        # Remove from ID index
        del self.concepts_by_id[concept_id]
        
        # Remove from name index
        normalized_name = self._normalize_text(concept.name)
        if concept_id in self.concepts_by_name[normalized_name]:
            self.concepts_by_name[normalized_name].remove(concept_id)
            
        # Remove from alias index
        for alias in concept.aliases:
            normalized_alias = self._normalize_text(alias)
            if concept_id in self.concepts_by_alias[normalized_alias]:
                self.concepts_by_alias[normalized_alias].remove(concept_id)
                
        # Remove from category index
        if concept_id in self.concepts_by_category[concept.category]:
            self.concepts_by_category[concept.category].remove(concept_id)
            
        # Remove from property index
        for prop_name, prop_value in concept.properties.items():
            if isinstance(prop_value, (str, int, float, bool)):
                str_value = str(prop_value)
                if concept_id in self.concepts_by_property[prop_name][str_value]:
                    self.concepts_by_property[prop_name][str_value].remove(concept_id)
                    
    def update_concept(self, concept: SemanticConcept):
        """
        Update a concept in the index.
        
        Args:
            concept: Updated concept
        """
        # Remove old concept
        self.remove_concept(concept.id)
        
        # Add updated concept
        self.add_concept(concept)
        
    def get_concept(self, concept_id: str) -> Optional[SemanticConcept]:
        """
        Get a concept by ID.
        
        Args:
            concept_id: ID of the concept
            
        Returns:
            The concept if found, None otherwise
        """
        return self.concepts_by_id.get(concept_id)
        
    def find_by_name(self, name: str, exact_match: bool = True) -> List[SemanticConcept]:
        """
        Find concepts by name.
        
        Args:
            name: Name to search for
            exact_match: Whether to require exact match
            
        Returns:
            List of matching concepts
        """
        normalized_name = self._normalize_text(name)
        
        if exact_match:
            # Find concepts with exactly matching name
            concept_ids = self.concepts_by_name.get(normalized_name, [])
        else:
            # Find concepts with names containing the search term
            concept_ids = []
            for indexed_name, ids in self.concepts_by_name.items():
                if normalized_name in indexed_name:
                    concept_ids.extend(ids)
                    
        # Return concepts
        return [self.concepts_by_id[cid] for cid in concept_ids if cid in self.concepts_by_id]
        
    def find_by_alias(self, alias: str, exact_match: bool = True) -> List[SemanticConcept]:
        """
        Find concepts by alias.
        
        Args:
            alias: Alias to search for
            exact_match: Whether to require exact match
            
        Returns:
            List of matching concepts
        """
        normalized_alias = self._normalize_text(alias)
        
        if exact_match:
            # Find concepts with exactly matching alias
            concept_ids = self.concepts_by_alias.get(normalized_alias, [])
        else:
            # Find concepts with aliases containing the search term
            concept_ids = []
            for indexed_alias, ids in self.concepts_by_alias.items():
                if normalized_alias in indexed_alias:
                    concept_ids.extend(ids)
                    
        # Return concepts
        return [self.concepts_by_id[cid] for cid in concept_ids if cid in self.concepts_by_id]
        
    def find_by_category(self, category: str) -> List[SemanticConcept]:
        """
        Find concepts by category.
        
        Args:
            category: Category to search for
            
        Returns:
            List of matching concepts
        """
        concept_ids = self.concepts_by_category.get(category, [])
        return [self.concepts_by_id[cid] for cid in concept_ids if cid in self.concepts_by_id]
        
    def find_by_property(self, property_name: str, property_value: Any) -> List[SemanticConcept]:
        """
        Find concepts by property value.
        
        Args:
            property_name: Name of the property
            property_value: Value of the property
            
        Returns:
            List of matching concepts
        """
        str_value = str(property_value)
        concept_ids = self.concepts_by_property.get(property_name, {}).get(str_value, [])
        return [self.concepts_by_id[cid] for cid in concept_ids if cid in self.concepts_by_id]
        
    def search(self, query: str, match_threshold: float = 0.5) -> List[Tuple[SemanticConcept, float]]:
        """
        Search for concepts matching a text query.
        
        Args:
            query: Query string
            match_threshold: Minimum match score threshold
            
        Returns:
            List of (concept, score) tuples
        """
        normalized_query = self._normalize_text(query)
        results = []
        
        # Check for exact matches in names
        for concept_id in self.concepts_by_name.get(normalized_query, []):
            if concept_id in self.concepts_by_id:
                results.append((self.concepts_by_id[concept_id], 1.0))
                
        # Check for exact matches in aliases
        for concept_id in self.concepts_by_alias.get(normalized_query, []):
            if concept_id in self.concepts_by_id:
                # Avoid duplicates
                if not any(r[0].id == concept_id for r in results):
                    results.append((self.concepts_by_id[concept_id], 0.9))
                    
        # Check for partial matches
        words = normalized_query.split()
        for concept_id, concept in self.concepts_by_id.items():
            # Skip concepts already in results
            if any(r[0].id == concept_id for r in results):
                continue
                
            # Check name and aliases
            text_to_match = [self._normalize_text(concept.name)]
            text_to_match.extend([self._normalize_text(alias) for alias in concept.aliases])
            
            # Check description
            if concept.description:
                text_to_match.append(self._normalize_text(concept.description))
                
            # Calculate match score based on word overlap
            best_score = 0.0
            for text in text_to_match:
                text_words = set(text.split())
                query_words = set(words)
                
                if not text_words or not query_words:
                    continue
                    
                # Jaccard similarity
                intersection = len(text_words.intersection(query_words))
                union = len(text_words.union(query_words))
                score = intersection / union if union > 0 else 0.0
                
                best_score = max(best_score, score)
                
            # Add to results if score exceeds threshold
            if best_score >= match_threshold:
                results.append((concept, best_score))
                
        # Sort by score (descending)
        results.sort(key=lambda x: x[1], reverse=True)
        
        return results


class RelationIndex:
    """
    Indexes relations for efficient retrieval based on various criteria.
    """
    
    def __init__(self):
        """Initialize relation index."""
        # Main index for relations by ID
        self.relations_by_id = {}
        
        # Index by source concept ID
        self.relations_by_source = defaultdict(list)
        
        # Index by target concept ID
        self.relations_by_target = defaultdict(list)
        
        # Index by relation type
        self.relations_by_type = defaultdict(list)
        
        # Index by relation name
        self.relations_by_name = defaultdict(list)
        
    def _normalize_text(self, text: str) -> str:
        """
        Normalize text for consistent indexing.
        
        Args:
            text: Text to normalize
            
        Returns:
            Normalized text
        """
        return text.lower().strip()
        
    def add_relation(self, relation: SemanticRelation):
        """
        Add a relation to the index.
        
        Args:
            relation: Relation to add
        """
        # Add to ID index
        self.relations_by_id[relation.id] = relation
        
        # Add to source index
        self.relations_by_source[relation.source_id].append(relation.id)
        
        # Add to target index
        self.relations_by_target[relation.target_id].append(relation.id)
        
        # Add to type index
        self.relations_by_type[relation.relation_type].append(relation.id)
        
        # Add to name index
        normalized_name = self._normalize_text(relation.name)
        self.relations_by_name[normalized_name].append(relation.id)
        
        # If bidirectional, add reverse relation to source and target indices
        if relation.bidirectional:
            self.relations_by_source[relation.target_id].append(relation.id)
            self.relations_by_target[relation.source_id].append(relation.id)
            
    def remove_relation(self, relation_id: str):
        """
        Remove a relation from the index.
        
        Args:
            relation_id: ID of the relation to remove
        """
        # Check if relation exists
        if relation_id not in self.relations_by_id:
            return
            
        relation = self.relations_by_id[relation_id]
        
        # Remove from ID index
        del self.relations_by_id[relation_id]
        
        # Remove from source index
        if relation_id in self.relations_by_source[relation.source_id]:
            self.relations_by_source[relation.source_id].remove(relation_id)
            
        # Remove from target index
        if relation_id in self.relations_by_target[relation.target_id]:
            self.relations_by_target[relation.target_id].remove(relation_id)
            
        # Remove from type index
        if relation_id in self.relations_by_type[relation.relation_type]:
            self.relations_by_type[relation.relation_type].remove(relation_id)
            
        # Remove from name index
        normalized_name = self._normalize_text(relation.name)
        if relation_id in self.relations_by_name[normalized_name]:
            self.relations_by_name[normalized_name].remove(relation_id)
            
        # If bidirectional, remove reverse relation from source and target indices
        if relation.bidirectional:
            if relation_id in self.relations_by_source[relation.target_id]:
                self.relations_by_source[relation.target_id].remove(relation_id)
            if relation_id in self.relations_by_target[relation.source_id]:
                self.relations_by_target[relation.source_id].remove(relation_id)
                
    def update_relation(self, relation: SemanticRelation):
        """
        Update a relation in the index.
        
        Args:
            relation: Updated relation
        """
        # Remove old relation
        self.remove_relation(relation.id)
        
        # Add updated relation
        self.add_relation(relation)
        
    def get_relation(self, relation_id: str) -> Optional[SemanticRelation]:
        """
        Get a relation by ID.
        
        Args:
            relation_id: ID of the relation
            
        Returns:
            The relation if found, None otherwise
        """
        return self.relations_by_id.get(relation_id)
        
    def get_outgoing_relations(self, concept_id: str) -> List[SemanticRelation]:
        """
        Get relations where the concept is the source.
        
        Args:
            concept_id: ID of the source concept
            
        Returns:
            List of outgoing relations
        """
        relation_ids = self.relations_by_source.get(concept_id, [])
        relations = []
        
        for relation_id in relation_ids:
            relation = self.relations_by_id.get(relation_id)
            if relation and relation.source_id == concept_id:
                relations.append(relation)
                
        return relations
        
    def get_incoming_relations(self, concept_id: str) -> List[SemanticRelation]:
        """
        Get relations where the concept is the target.
        
        Args:
            concept_id: ID of the target concept
            
        Returns:
            List of incoming relations
        """
        relation_ids = self.relations_by_target.get(concept_id, [])
        relations = []
        
        for relation_id in relation_ids:
            relation = self.relations_by_id.get(relation_id)
            if relation and relation.target_id == concept_id:
                relations.append(relation)
                
        return relations
        
    def get_bidirectional_relations(self, concept_id: str) -> List[SemanticRelation]:
        """
        Get all relations connected to a concept (incoming or outgoing).
        
        Args:
            concept_id: ID of the concept
            
        Returns:
            List of connected relations
        """
        # Get outgoing and incoming relations
        outgoing = self.get_outgoing_relations(concept_id)
        incoming = self.get_incoming_relations(concept_id)
        
        # Combine unique relations
        relations = outgoing.copy()
        for relation in incoming:
            if not any(r.id == relation.id for r in relations):
                relations.append(relation)
                
        return relations
        
    def find_by_type(self, relation_type: str) -> List[SemanticRelation]:
        """
        Find relations by type.
        
        Args:
            relation_type: Type to search for
            
        Returns:
            List of matching relations
        """
        relation_ids = self.relations_by_type.get(relation_type, [])
        return [self.relations_by_id[rid] for rid in relation_ids if rid in self.relations_by_id]
        
    def find_by_name(self, name: str, exact_match: bool = True) -> List[SemanticRelation]:
        """
        Find relations by name.
        
        Args:
            name: Name to search for
            exact_match: Whether to require exact match
            
        Returns:
            List of matching relations
        """
        normalized_name = self._normalize_text(name)
        
        if exact_match:
            # Find relations with exactly matching name
            relation_ids = self.relations_by_name.get(normalized_name, [])
        else:
            # Find relations with names containing the search term
            relation_ids = []
            for indexed_name, ids in self.relations_by_name.items():
                if normalized_name in indexed_name:
                    relation_ids.extend(ids)
                    
        # Return relations
        return [self.relations_by_id[rid] for rid in relation_ids if rid in self.relations_by_id]
        
    def find_between_concepts(self, source_id: str, target_id: str,
                           relation_type: Optional[str] = None) -> List[SemanticRelation]:
        """
        Find relations between two concepts.
        
        Args:
            source_id: ID of source concept
            target_id: ID of target concept
            relation_type: Optional relation type filter
            
        Returns:
            List of matching relations
        """
        # Get relations from source
        source_relations = self.relations_by_source.get(source_id, [])
        
        # Filter relations
        relations = []
        for relation_id in source_relations:
            relation = self.relations_by_id.get(relation_id)
            if not relation:
                continue
                
            # Check if relation connects the two concepts
            if relation.target_id == target_id:
                # Apply type filter if specified
                if relation_type is None or relation.relation_type == relation_type:
                    relations.append(relation)
                    
        return relations
        
    def search_path(self, start_id: str, end_id: str, max_depth: int = 3) -> List[List[SemanticRelation]]:
        """
        Search for paths between two concepts.
        
        Args:
            start_id: ID of start concept
            end_id: ID of end concept
            max_depth: Maximum path length
            
        Returns:
            List of relation paths (each path is a list of relations)
        """
        # Check if concepts exist
        if start_id == end_id:
            return [[]]  # Empty path if start and end are the same
            
        # Use BFS to find paths
        visited = set()
        queue = deque([(start_id, [])])
        paths = []
        
        while queue:
            concept_id, path = queue.popleft()
            
            # Skip if concept already visited or path too long
            if concept_id in visited or len(path) >= max_depth:
                continue
                
            # Mark concept as visited
            visited.add(concept_id)
            
            # Get outgoing relations
            outgoing = self.get_outgoing_relations(concept_id)
            
            for relation in outgoing:
                # Create new path
                new_path = path + [relation]
                
                # Check if relation leads to end concept
                if relation.target_id == end_id:
                    paths.append(new_path)
                else:
                    # Add to queue for further exploration
                    queue.append((relation.target_id, new_path))
                    
        return paths


class SchemaValidator:
    """
    Validates knowledge against a defined schema, ensuring
    data integrity and consistency.
    """
    
    def __init__(self, ontology: Dict[str, OntologyClass] = None):
        """
        Initialize schema validator.
        
        Args:
            ontology: Optional dictionary mapping class IDs to ontology classes
        """
        self.ontology = ontology or {}
        
    def add_class(self, ontology_class: OntologyClass):
        """
        Add an ontology class to the schema.
        
        Args:
            ontology_class: Ontology class to add
        """
        self.ontology[ontology_class.id] = ontology_class
        
    def remove_class(self, class_id: str):
        """
        Remove an ontology class from the schema.
        
        Args:
            class_id: ID of the class to remove
        """
        if class_id in self.ontology:
            del self.ontology[class_id]
            
    def get_class(self, class_id: str) -> Optional[OntologyClass]:
        """
        Get an ontology class by ID.
        
        Args:
            class_id: ID of the class
            
        Returns:
            The ontology class if found, None otherwise
        """
        return self.ontology.get(class_id)
        
    def validate_concept(self, concept: SemanticConcept, class_id: str) -> Tuple[bool, List[str]]:
        """
        Validate a concept against an ontology class.
        
        Args:
            concept: Concept to validate
            class_id: ID of the ontology class
            
        Returns:
            Tuple of (is_valid, error_messages)
        """
        # Check if ontology class exists
        ontology_class = self.ontology.get(class_id)
        if not ontology_class:
            return False, [f"Ontology class '{class_id}' not found"]
            
        errors = []
        
        # Check required properties
        for prop_name, prop_def in ontology_class.properties.items():
            if prop_def.get('required', False) and prop_name not in concept.properties:
                errors.append(f"Required property '{prop_name}' is missing")
                
        # Check property types
        for prop_name, prop_value in concept.properties.items():
            if prop_name in ontology_class.properties:
                prop_def = ontology_class.properties[prop_name]
                
                # Check data type
                data_type = prop_def.get('data_type')
                if data_type:
                    type_valid = self._validate_data_type(prop_value, data_type)
                    if not type_valid:
                        errors.append(f"Property '{prop_name}' has incorrect data type, expected {data_type}")
                        
                # Check restrictions
                restrictions = prop_def.get('restrictions', {})
                for restriction_type, restriction_value in restrictions.items():
                    restriction_valid = self._validate_restriction(prop_value, restriction_type, restriction_value)
                    if not restriction_valid:
                        errors.append(f"Property '{prop_name}' violates {restriction_type} restriction")
                        
        # Determine validity
        is_valid = len(errors) == 0
        
        return is_valid, errors
        
    def validate_relation(self, relation: SemanticRelation, 
                        source_class_id: str, target_class_id: str,
                        relation_type: Optional[str] = None) -> Tuple[bool, List[str]]:
        """
        Validate a relation against ontology classes.
        
        Args:
            relation: Relation to validate
            source_class_id: ID of source ontology class
            target_class_id: ID of target ontology class
            relation_type: Optional specific relation type to validate against
            
        Returns:
            Tuple of (is_valid, error_messages)
        """
        errors = []
        
        # Check ontology classes
        source_class = self.ontology.get(source_class_id)
        target_class = self.ontology.get(target_class_id)
        
        if not source_class:
            errors.append(f"Source ontology class '{source_class_id}' not found")
        if not target_class:
            errors.append(f"Target ontology class '{target_class_id}' not found")
            
        if not source_class or not target_class:
            return False, errors
            
        # Check relation type
        if relation_type and relation.relation_type != relation_type:
            errors.append(f"Expected relation type '{relation_type}', got '{relation.relation_type}'")
            
        # Check relation restrictions
        if relation.relation_type in source_class.restrictions:
            restrictions = source_class.restrictions[relation.relation_type]
            
            # Check target class restrictions
            valid_target = False
            for restriction in restrictions:
                if restriction.get('target') == target_class_id:
                    valid_target = True
                    
                    # Check cardinality if specified
                    if 'cardinality' in restriction:
                        # This would need to check existing relations to validate cardinality
                        pass
                        
                    break
                    
            if not valid_target:
                errors.append(f"Relation to class '{target_class_id}' not allowed by restrictions")
                
        # Determine validity
        is_valid = len(errors) == 0
        
        return is_valid, errors
        
    def _validate_data_type(self, value: Any, expected_type: str) -> bool:
        """
        Validate a value against an expected data type.
        
        Args:
            value: Value to validate
            expected_type: Expected data type
            
        Returns:
            True if valid, False otherwise
        """
        if expected_type == 'string':
            return isinstance(value, str)
        elif expected_type == 'integer':
            return isinstance(value, int)
        elif expected_type == 'float':
            return isinstance(value, (int, float))
        elif expected_type == 'boolean':
            return isinstance(value, bool)
        elif expected_type == 'date':
            # Basic date validation
            if not isinstance(value, str):
                return False
            date_pattern = r'^\d{4}-\d{2}-\d{2}$'
            return bool(re.match(date_pattern, value))
        elif expected_type == 'datetime':
            # Basic datetime validation
            if not isinstance(value, str):
                return False
            datetime_pattern = r'^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}.*$'
            return bool(re.match(datetime_pattern, value))
        elif expected_type == 'list':
            return isinstance(value, list)
        elif expected_type == 'object':
            return isinstance(value, dict)
        else:
            # Unknown type, assume valid
            return True
            
    def _validate_restriction(self, value: Any, restriction_type: str, restriction_value: Any) -> bool:
        """
        Validate a value against a restriction.
        
        Args:
            value: Value to validate
            restriction_type: Type of restriction
            restriction_value: Value of restriction
            
        Returns:
            True if valid, False otherwise
        """
        if restriction_type == 'min':
            return value >= restriction_value
        elif restriction_type == 'max':
            return value <= restriction_value
        elif restriction_type == 'minLength':
            return len(value) >= restriction_value
        elif restriction_type == 'maxLength':
            return len(value) <= restriction_value
        elif restriction_type == 'pattern':
            if not isinstance(value, str):
                return False
            return bool(re.match(restriction_value, value))
        elif restriction_type == 'enum':
            return value in restriction_value
        else:
            # Unknown restriction type, assume valid
            return True
            
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert schema to dictionary for serialization.
        
        Returns:
            Dictionary representation of the schema
        """
        return {
            'ontology': {class_id: cls.to_dict() for class_id, cls in self.ontology.items()}
        }
        
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SchemaValidator':
        """
        Create schema from dictionary.
        
        Args:
            data: Dictionary representation of schema
            
        Returns:
            SchemaValidator instance
        """
        validator = cls()
        
        # Load ontology classes
        for class_id, class_data in data.get('ontology', {}).items():
            ontology_class = OntologyClass.from_dict(class_data)
            validator.add_class(ontology_class)
            
        return validator


class InferenceEngine:
    """
    Performs logical inference over the knowledge base, discovering
    implicit knowledge from explicit facts.
    """
    
    def __init__(self, concept_index: ConceptIndex,
                relation_index: RelationIndex,
                knowledge_embedding: Optional[KnowledgeEmbedding] = None,
                concept_hierarchy: Optional[ConceptHierarchy] = None,
                inference_rules: Optional[Dict[str, Callable]] = None):
        """
        Initialize inference engine.
        
        Args:
            concept_index: Index of concepts
            relation_index: Index of relations
            knowledge_embedding: Optional knowledge embedding component
            concept_hierarchy: Optional concept hierarchy component
            inference_rules: Optional dictionary mapping rule names to inference functions
        """
        self.concept_index = concept_index
        self.relation_index = relation_index
        self.knowledge_embedding = knowledge_embedding
        self.concept_hierarchy = concept_hierarchy
        self.inference_rules = inference_rules or {}
        
        # Add default inference rules
        if not inference_rules:
            self._add_default_rules()
            
        # Cache for inference results
        self.inference_cache = {}
        
    def _add_default_rules(self):
        """Add default inference rules."""
        self.inference_rules = {
            'transitive': self._transitive_inference,
            'inheritance': self._inheritance_inference,
            'symmetric': self._symmetric_inference,
            'inverse': self._inverse_inference,
            'compose': self._composition_inference
        }
        
    def clear_cache(self):
        """Clear the inference cache."""
        self.inference_cache = {}
        
    def infer_relations(self, concept_id: str,
                      relation_types: Optional[List[str]] = None,
                      max_depth: int = 2,
                      min_confidence: float = 0.5,
                      enable_rules: Optional[List[str]] = None) -> List[KnowledgeTriple]:
        """
        Infer relations for a concept.
        
        Args:
            concept_id: ID of the concept
            relation_types: Optional list of relation types to consider
            max_depth: Maximum inference depth
            min_confidence: Minimum confidence for inferred relations
            enable_rules: Optional list of rule names to enable
            
        Returns:
            List of inferred triples
        """
        # Get concept
        concept = self.concept_index.get_concept(concept_id)
        if not concept:
            return []
            
        # Set up enabled rules
        active_rules = {}
        if enable_rules:
            for rule_name in enable_rules:
                if rule_name in self.inference_rules:
                    active_rules[rule_name] = self.inference_rules[rule_name]
        else:
            active_rules = self.inference_rules
            
        # Get explicit relations
        explicit_relations = self.relation_index.get_bidirectional_relations(concept_id)
        
        # Filter by relation type if specified
        if relation_types:
            explicit_relations = [r for r in explicit_relations if r.relation_type in relation_types]
            
        # Convert to triples
        inferred_triples = []
        
        # Apply inference rules to depth
        current_depth = 0
        current_relations = explicit_relations
        
        while current_depth < max_depth and current_relations:
            next_relations = []
            
            for relation in current_relations:
                # Apply each inference rule
                for rule_name, rule_func in active_rules.items():
                    # Create cache key
                    cache_key = (rule_name, relation.id, current_depth)
                    
                    # Check cache
                    if cache_key in self.inference_cache:
                        new_triples = self.inference_cache[cache_key]
                    else:
                        # Apply inference rule
                        new_triples = rule_func(relation)
                        
                        # Cache results
                        self.inference_cache[cache_key] = new_triples
                        
                    # Filter by confidence
                    new_triples = [t for t in new_triples if t.confidence >= min_confidence]
                    
                    # Add to results
                    for triple in new_triples:
                        # Check if triple already exists
                        if not any(t.subject_id == triple.subject_id and 
                                  t.predicate == triple.predicate and 
                                  t.object_id == triple.object_id for t in inferred_triples):
                            inferred_triples.append(triple)
                            
                            # Get relation for next level
                            relation_id = self._find_relation_by_triple(triple)
                            if relation_id:
                                relation = self.relation_index.get_relation(relation_id)
                                if relation:
                                    next_relations.append(relation)
                                    
            current_relations = next_relations
            current_depth += 1
            
        return inferred_triples
        
    def _find_relation_by_triple(self, triple: KnowledgeTriple) -> Optional[str]:
        """
        Find a relation ID corresponding to a triple.
        
        Args:
            triple: Knowledge triple
            
        Returns:
            Relation ID if found, None otherwise
        """
        # Find relations between the subject and object
        relations = self.relation_index.find_between_concepts(
            triple.subject_id, triple.object_id, triple.predicate)
            
        if relations:
            return relations[0].id
            
        return None
        
    def infer_new_relations(self, min_confidence: float = 0.7,
                          max_results: int = 1000) -> List[KnowledgeTriple]:
        """
        Infer new relations across the entire knowledge base.
        
        Args:
            min_confidence: Minimum confidence for inferred relations
            max_results: Maximum number of results to return
            
        Returns:
            List of inferred triples
        """
        inferred_triples = []
        
        # Apply embedding-based inference if available
        if self.knowledge_embedding:
            embedding_triples = self._infer_from_embeddings(min_confidence, max_results // 2)
            inferred_triples.extend(embedding_triples)
            
        # Apply rule-based inference
        rule_triples = self._infer_from_rules(min_confidence, max_results // 2)
        inferred_triples.extend(rule_triples)
        
        # Sort by confidence (descending)
        inferred_triples.sort(key=lambda x: x.confidence, reverse=True)
        
        # Limit results
        return inferred_triples[:max_results]
        
    def _infer_from_embeddings(self, min_confidence: float,
                             max_results: int) -> List[KnowledgeTriple]:
        """
        Infer new relations using embeddings.
        
        Args:
            min_confidence: Minimum confidence for inferred relations
            max_results: Maximum number of results
            
        Returns:
            List of inferred triples
        """
        if not self.knowledge_embedding:
            return []
            
        inferred_triples = []
        
        # Get a sample of concepts
        concept_ids = list(self.concept_index.concepts_by_id.keys())
        if len(concept_ids) > 100:
            import random
            concept_ids = random.sample(concept_ids, 100)
            
        # Get relation types
        relation_types = list(self.relation_index.relations_by_type.keys())
        
        # For each concept pair and relation type, compute score
        for source_id in concept_ids:
            for target_id in concept_ids:
                if source_id == target_id:
                    continue
                    
                # Check if any direct relation exists between the concepts
                direct_relations = self.relation_index.find_between_concepts(source_id, target_id)
                direct_relation_types = set(r.relation_type for r in direct_relations)
                
                for relation_type in relation_types:
                    # Skip existing relations
                    if relation_type in direct_relation_types:
                        continue
                        
                    # Check if relation matrices exist
                    if relation_type not in self.knowledge_embedding.relation_matrices:
                        continue
                        
                    # Compute relation score
                    score = self.knowledge_embedding.compute_relation_score(
                        source_id, relation_type, target_id)
                        
                    # If score exceeds threshold, add to results
                    if score >= min_confidence:
                        triple = KnowledgeTriple(
                            id=str(uuid.uuid4()),
                            subject_id=source_id,
                            predicate=relation_type,
                            object_id=target_id,
                            confidence=score,
                            provenance="embedding_inference"
                        )
                        inferred_triples.append(triple)
                        
                        # Limit results
                        if len(inferred_triples) >= max_results:
                            return inferred_triples
                            
        return inferred_triples
        
    def _infer_from_rules(self, min_confidence: float,
                        max_results: int) -> List[KnowledgeTriple]:
        """
        Infer new relations using rules.
        
        Args:
            min_confidence: Minimum confidence for inferred relations
            max_results: Maximum number of results
            
        Returns:
            List of inferred triples
        """
        inferred_triples = []
        
        # Apply rules to each relation
        for relation_id, relation in self.relation_index.relations_by_id.items():
            for rule_name, rule_func in self.inference_rules.items():
                new_triples = rule_func(relation)
                
                # Filter by confidence
                new_triples = [t for t in new_triples if t.confidence >= min_confidence]
                
                # Add to results
                inferred_triples.extend(new_triples)
                
                # Limit results
                if len(inferred_triples) >= max_results:
                    return inferred_triples
                    
        return inferred_triples
        
    def _transitive_inference(self, relation: SemanticRelation) -> List[KnowledgeTriple]:
        """
        Apply transitive inference to a relation.
        
        Args:
            relation: Relation to infer from
            
        Returns:
            List of inferred triples
        """
        # Skip non-transitive relations
        transitive_types = {"is_a", "part_of", "located_in", "occurs_before"}
        if relation.relation_type not in transitive_types:
            return []
            
        inferred_triples = []
        
        # Get relations with the same type from the target
        target_relations = self.relation_index.get_outgoing_relations(relation.target_id)
        target_relations = [r for r in target_relations if r.relation_type == relation.relation_type]
        
        for target_relation in target_relations:
            # Create inferred triple
            triple = KnowledgeTriple(
                id=str(uuid.uuid4()),
                subject_id=relation.source_id,
                predicate=relation.relation_type,
                object_id=target_relation.target_id,
                confidence=relation.confidence * target_relation.confidence,
                provenance="transitive_inference"
            )
            inferred_triples.append(triple)
            
        return inferred_triples
        
    def _inheritance_inference(self, relation: SemanticRelation) -> List[KnowledgeTriple]:
        """
        Apply inheritance inference to a relation.
        
        Args:
            relation: Relation to infer from
            
        Returns:
            List of inferred triples
        """
        if not self.concept_hierarchy or relation.relation_type != "is_a":
            return []
            
        inferred_triples = []
        
        # Get ancestors of target
        ancestors = self.concept_hierarchy.get_ancestors(relation.target_id)
        
        for ancestor_id in ancestors:
            # Create inferred triple
            triple = KnowledgeTriple(
                id=str(uuid.uuid4()),
                subject_id=relation.source_id,
                predicate="is_a",
                object_id=ancestor_id,
                confidence=relation.confidence * 0.9,  # Slight reduction in confidence
                provenance="inheritance_inference"
            )
            inferred_triples.append(triple)
            
        return inferred_triples
        
    def _symmetric_inference(self, relation: SemanticRelation) -> List[KnowledgeTriple]:
        """
        Apply symmetric inference to a relation.
        
        Args:
            relation: Relation to infer from
            
        Returns:
            List of inferred triples
        """
        # Skip non-symmetric relations
        symmetric_types = {"similar_to", "related_to"}
        if relation.relation_type not in symmetric_types and not relation.bidirectional:
            return []
            
        inferred_triples = []
        
        # Create inferred triple in the opposite direction
        triple = KnowledgeTriple(
            id=str(uuid.uuid4()),
            subject_id=relation.target_id,
            predicate=relation.relation_type,
            object_id=relation.source_id,
            confidence=relation.confidence,
            provenance="symmetric_inference"
        )
        inferred_triples.append(triple)
        
        return inferred_triples
        
    def _inverse_inference(self, relation: SemanticRelation) -> List[KnowledgeTriple]:
        """
        Apply inverse inference to a relation.
        
        Args:
            relation: Relation to infer from
            
        Returns:
            List of inferred triples
        """
        # Define inverse relation pairs
        inverse_pairs = {
            "part_of": "has_part",
            "has_part": "part_of",
            "located_in": "contains",
            "contains": "located_in",
            "occurs_before": "occurs_after",
            "occurs_after": "occurs_before",
            "parent_of": "child_of",
            "child_of": "parent_of"
        }
        
        # Skip relations without inverses
        if relation.relation_type not in inverse_pairs:
            return []
            
        inferred_triples = []
        
        # Create inferred triple with inverse relation
        triple = KnowledgeTriple(
            id=str(uuid.uuid4()),
            subject_id=relation.target_id,
            predicate=inverse_pairs[relation.relation_type],
            object_id=relation.source_id,
            confidence=relation.confidence,
            provenance="inverse_inference"
        )
        inferred_triples.append(triple)
        
        return inferred_triples
        
    def _composition_inference(self, relation: SemanticRelation) -> List[KnowledgeTriple]:
        """
        Apply composition inference to a relation.
        
        Args:
            relation: Relation to infer from
            
        Returns:
            List of inferred triples
        """
        # Define composition rules
        # Format: (relation1, relation2) -> result_relation
        composition_rules = {
            ("located_in", "located_in"): "located_in",
            ("part_of", "part_of"): "part_of",
            ("part_of", "located_in"): "located_in",
            ("occurs_before", "occurs_before"): "occurs_before",
            ("is_a", "has_property"): "has_property"
        }
        
        inferred_triples = []
        
        # Get outgoing relations from the target
        target_relations = self.relation_index.get_outgoing_relations(relation.target_id)
        
        for target_relation in target_relations:
            # Check if there's a composition rule
            composition_key = (relation.relation_type, target_relation.relation_type)
            if composition_key in composition_rules:
                result_relation = composition_rules[composition_key]
                
                # Create inferred triple
                triple = KnowledgeTriple(
                    id=str(uuid.uuid4()),
                    subject_id=relation.source_id,
                    predicate=result_relation,
                    object_id=target_relation.target_id,
                    confidence=relation.confidence * target_relation.confidence * 0.9,
                    provenance="composition_inference"
                )
                inferred_triples.append(triple)
                
        return inferred_triples
        
    def infer_types(self, concept_id: str, 
                  min_confidence: float = 0.6) -> List[Tuple[str, float]]:
        """
        Infer types (classes) for a concept.
        
        Args:
            concept_id: ID of the concept
            min_confidence: Minimum confidence for inferred types
            
        Returns:
            List of (type_id, confidence) tuples
        """
        # Get explicit types
        explicit_types = []
        
        # Check "is_a" relations
        outgoing = self.relation_index.get_outgoing_relations(concept_id)
        for relation in outgoing:
            if relation.relation_type == "is_a":
                explicit_types.append((relation.target_id, relation.confidence))
                
        # Also check "instance_of" relations
        for relation in outgoing:
            if relation.relation_type == "instance_of":
                explicit_types.append((relation.target_id, relation.confidence))
                
        # Use hierarchy to infer additional types if available
        inferred_types = explicit_types.copy()
        
        if self.concept_hierarchy:
            for type_id, confidence in explicit_types:
                ancestors = self.concept_hierarchy.get_ancestors(type_id)
                for ancestor in ancestors:
                    # Reduce confidence for each step up the hierarchy
                    inferred_confidence = confidence * 0.9
                    if inferred_confidence >= min_confidence:
                        inferred_types.append((ancestor, inferred_confidence))
                        
        # Use embeddings to infer additional types if available
        if self.knowledge_embedding:
            # Get all classes
            classes = []
            for relation in self.relation_index.find_by_type("is_a"):
                classes.append(relation.target_id)
                
            for class_id in set(classes):
                # Skip if already inferred
                if any(t[0] == class_id for t in inferred_types):
                    continue
                    
                # Compute relation score
                score = self.knowledge_embedding.compute_relation_score(
                    concept_id, "is_a", class_id)
                    
                if score >= min_confidence:
                    inferred_types.append((class_id, score))
                    
        # Remove duplicates and sort by confidence
        unique_types = {}
        for type_id, confidence in inferred_types:
            if type_id not in unique_types or confidence > unique_types[type_id]:
                unique_types[type_id] = confidence
                
        result = [(type_id, confidence) for type_id, confidence in unique_types.items()]
        result.sort(key=lambda x: x[1], reverse=True)
        
        return result
        
    def infer_properties(self, concept_id: str,
                       min_confidence: float = 0.6) -> Dict[str, Tuple[Any, float]]:
        """
        Infer properties for a concept.
        
        Args:
            concept_id: ID of the concept
            min_confidence: Minimum confidence for inferred properties
            
        Returns:
            Dictionary mapping property names to (value, confidence) tuples
        """
        # Get concept
        concept = self.concept_index.get_concept(concept_id)
        if not concept:
            return {}
            
        # Start with explicit properties
        inferred_properties = {prop: (value, 1.0) for prop, value in concept.properties.items()}
        
        # Infer properties from types
        inferred_types = self.infer_types(concept_id, min_confidence)
        
        for type_id, type_confidence in inferred_types:
            type_concept = self.concept_index.get_concept(type_id)
            if not type_concept:
                continue
                
            # Get properties of the type
            for prop, value in type_concept.properties.items():
                # Skip if property already exists with higher confidence
                if prop in inferred_properties and inferred_properties[prop][1] >= type_confidence:
                    continue
                    
                # Add inferred property
                inferred_properties[prop] = (value, type_confidence)
                
        # Filter by confidence
        result = {prop: (value, conf) for prop, (value, conf) in inferred_properties.items()
                if conf >= min_confidence}
                
        return result
        
    def validate_fact(self, subject_id: str, predicate: str, object_id: str) -> float:
        """
        Validate a fact and return a confidence score.
        
        Args:
            subject_id: ID of subject concept
            predicate: Relation predicate
            object_id: ID of object concept
            
        Returns:
            Confidence score between 0 and 1
        """
        # Check if fact exists explicitly
        direct_relations = self.relation_index.find_between_concepts(
            subject_id, object_id, predicate)
            
        if direct_relations:
            return direct_relations[0].confidence
            
        # Try to infer
        if self.knowledge_embedding:
            # Use embedding-based inference
            score = self.knowledge_embedding.compute_relation_score(
                subject_id, predicate, object_id)
            return score
            
        # Infer relations for the subject
        inferred = self.infer_relations(
            subject_id, relation_types=[predicate], max_depth=2)
            
        # Check if the fact was inferred
        for triple in inferred:
            if triple.subject_id == subject_id and triple.predicate == predicate and triple.object_id == object_id:
                return triple.confidence
                
        # Not found
        return 0.0


class SemanticKnowledge:
    """
    Main class orchestrating the semantic knowledge system, providing a
    unified interface for knowledge storage, retrieval, and reasoning.
    """
    
    def __init__(self, 
                embedding_dim: int = 768,
                embedding_model: Optional[Callable] = None,
                use_gpu: bool = False,
                storage_dir: Optional[str] = None):
        """
        Initialize semantic knowledge system.
        
        Args:
            embedding_dim: Dimension of concept and relation embeddings
            embedding_model: Optional function for creating embeddings
            use_gpu: Whether to use GPU acceleration if available
            storage_dir: Optional directory for persistent storage
        """
        self.embedding_dim = embedding_dim
        self.embedding_model = embedding_model
        self.use_gpu = use_gpu
        self.storage_dir = storage_dir
        
        # Initialize components
        self.concept_index = ConceptIndex()
        self.relation_index = RelationIndex()
        self.concept_hierarchy = ConceptHierarchy()
        self.knowledge_embedding = KnowledgeEmbedding(
            embedding_dim=embedding_dim,
            embedding_model=embedding_model,
            use_gpu=use_gpu
        )
        self.schema_validator = SchemaValidator()
        self.inference_engine = InferenceEngine(
            concept_index=self.concept_index,
            relation_index=self.relation_index,
            knowledge_embedding=self.knowledge_embedding,
            concept_hierarchy=self.concept_hierarchy
        )
        
        # Knowledge statistics
        self.stats = {
            'concept_count': 0,
            'relation_count': 0,
            'triple_count': 0,
            'last_modified': time.time()
        }
        
        # For task-based access control
        self.active_contexts = []
        
        # Create storage directory if specified
        if storage_dir and not os.path.exists(storage_dir):
            os.makedirs(storage_dir)
            
        # Load configuration if available
        self.config = self._load_config()
        
    def _load_config(self) -> Dict[str, Any]:
        """
        Load configuration settings.
        
        Returns:
            Configuration dictionary
        """
        config = {
            'version': '1.0',
            'max_cache_size': 10000,
            'persistence_interval': 300,  # seconds
            'inference_enabled': True,
            'max_inference_depth': 3,
            'min_inference_confidence': 0.6,
            'embedding_update_strategy': 'incremental',
            'embedding_batch_size': 128
        }
        
        # Load from storage if available
        if self.storage_dir:
            config_path = os.path.join(self.storage_dir, 'config.json')
            if os.path.exists(config_path):
                try:
                    with open(config_path, 'r') as f:
                        stored_config = json.load(f)
                    config.update(stored_config)
                except Exception as e:
                    logger.error(f"Error loading configuration: {e}")
                    
        return config
        
    def save_config(self):
        """Save configuration settings to disk."""
        if not self.storage_dir:
            return
            
        config_path = os.path.join(self.storage_dir, 'config.json')
        try:
            with open(config_path, 'w') as f:
                json.dump(self.config, f, indent=2)
        except Exception as e:
            logger.error(f"Error saving configuration: {e}")
            
    # -------------------------------------------------------------------------
    # Concept Management
    # -------------------------------------------------------------------------
    
    def add_concept(self, 
                   name: str,
                   description: str = "",
                   category: str = "entity",
                   properties: Optional[Dict[str, Any]] = None,
                   aliases: Optional[List[str]] = None,
                   data_type: Optional[str] = None,
                   metadata: Optional[Dict[str, Any]] = None,
                   embedding: Optional[np.ndarray] = None,
                   concept_id: Optional[str] = None) -> str:
        """
        Add a new concept to the knowledge base.
        
        Args:
            name: Name of the concept
            description: Optional description
            category: Category of the concept
            properties: Optional dictionary of property name-value pairs
            aliases: Optional list of alternative names
            data_type: Optional data type for the concept
            metadata: Optional metadata
            embedding: Optional pre-computed embedding
            concept_id: Optional ID (generated if not provided)
            
        Returns:
            ID of the new concept
        """
        # Generate ID if not provided
        if not concept_id:
            concept_id = str(uuid.uuid4())
            
        # Create concept
        concept = SemanticConcept(
            id=concept_id,
            name=name,
            description=description,
            category=category,
            properties=properties or {},
            aliases=aliases or [],
            data_type=data_type,
            metadata=metadata or {},
            embedding=embedding
        )
        
        # Add to index
        self.concept_index.add_concept(concept)
        
        # Create embedding if not provided
        if embedding is None and self.knowledge_embedding:
            embedding = self.knowledge_embedding.create_concept_embedding(concept)
            concept.embedding = embedding
            
        # Add embedding
        if embedding is not None and self.knowledge_embedding:
            self.knowledge_embedding.add_concept_embedding(concept_id, embedding)
            
        # Update statistics
        self.stats['concept_count'] += 1
        self.stats['last_modified'] = time.time()
        
        return concept_id
        
    def get_concept(self, concept_id: str) -> Optional[SemanticConcept]:
        """
        Get a concept by ID.
        
        Args:
            concept_id: ID of the concept
            
        Returns:
            The concept if found, None otherwise
        """
        return self.concept_index.get_concept(concept_id)
        
    def update_concept(self,
                      concept_id: str,
                      name: Optional[str] = None,
                      description: Optional[str] = None,
                      category: Optional[str] = None,
                      properties: Optional[Dict[str, Any]] = None,
                      aliases: Optional[List[str]] = None,
                      data_type: Optional[str] = None,
                      metadata: Optional[Dict[str, Any]] = None,
                      embedding: Optional[np.ndarray] = None) -> bool:
        """
        Update an existing concept.
        
        Args:
            concept_id: ID of the concept to update
            name: Optional new name
            description: Optional new description
            category: Optional new category
            properties: Optional new properties (replaces existing)
            aliases: Optional new aliases (replaces existing)
            data_type: Optional new data type
            metadata: Optional new metadata (merged with existing)
            embedding: Optional new embedding
            
        Returns:
            True if updated successfully, False if concept not found
        """
        # Get existing concept
        concept = self.concept_index.get_concept(concept_id)
        if not concept:
            return False
            
        # Update fields if provided
        if name is not None:
            concept.name = name
        if description is not None:
            concept.description = description
        if category is not None:
            concept.category = category
        if properties is not None:
            concept.properties = properties
        if aliases is not None:
            concept.aliases = aliases
        if data_type is not None:
            concept.data_type = data_type
        if metadata is not None:
            # Merge with existing metadata
            concept.metadata.update(metadata)
        if embedding is not None:
            concept.embedding = embedding
            
        # Update in index
        self.concept_index.update_concept(concept)
        
        # Update embedding if provided
        if embedding is not None and self.knowledge_embedding:
            self.knowledge_embedding.add_concept_embedding(concept_id, embedding)
            
        # Update statistics
        self.stats['last_modified'] = time.time()
        
        return True
        
    def delete_concept(self, concept_id: str, 
                      cascade: bool = True) -> bool:
        """
        Delete a concept from the knowledge base.
        
        Args:
            concept_id: ID of the concept to delete
            cascade: Whether to also delete relations involving the concept
            
        Returns:
            True if deleted successfully, False if concept not found
        """
        # Get concept
        concept = self.concept_index.get_concept(concept_id)
        if not concept:
            return False
            
        # Get related relations if cascade is True
        if cascade:
            # Get relations involving this concept
            relations = self.relation_index.get_bidirectional_relations(concept_id)
            
            # Delete relations
            for relation in relations:
                self.delete_relation(relation.id)
                
        # Delete from hierarchy
        if self.concept_hierarchy:
            self.concept_hierarchy.remove_concept(concept_id)
            
        # Remove from concept index
        self.concept_index.remove_concept(concept_id)
        
        # Update statistics
        self.stats['concept_count'] -= 1
        self.stats['last_modified'] = time.time()
        
        return True
        
    def add_property(self, concept_id: str, 
                    property_name: str, 
                    property_value: Any) -> bool:
        """
        Add or update a property for a concept.
        
        Args:
            concept_id: ID of the concept
            property_name: Name of the property
            property_value: Value of the property
            
        Returns:
            True if added successfully, False if concept not found
        """
        # Get concept
        concept = self.concept_index.get_concept(concept_id)
        if not concept:
            return False
            
        # Add property
        concept.add_property(property_name, property_value)
        
        # Update in index
        self.concept_index.update_concept(concept)
        
        # Update statistics
        self.stats['last_modified'] = time.time()
        
        return True
        
    def search_concepts(self, query: str, 
                       match_threshold: float = 0.5,
                       limit: int = 10) -> List[Tuple[SemanticConcept, float]]:
        """
        Search for concepts matching a query.
        
        Args:
            query: Search query
            match_threshold: Minimum match score threshold
            limit: Maximum number of results
            
        Returns:
            List of (concept, score) tuples
        """
        results = self.concept_index.search(query, match_threshold)
        
        # Sort by score and limit results
        results.sort(key=lambda x: x[1], reverse=True)
        return results[:limit]
        
    def find_similar_concepts(self, 
                             concept_id: str, 
                             k: int = 10) -> List[Tuple[SemanticConcept, float]]:
        """
        Find concepts similar to a reference concept.
        
        Args:
            concept_id: ID of the reference concept
            k: Number of similar concepts to find
            
        Returns:
            List of (concept, similarity) tuples
        """
        if not self.knowledge_embedding:
            return []
            
        # Get similar concept IDs
        similar_ids = self.knowledge_embedding.get_most_similar_concepts(concept_id, k)
        
        # Convert to concepts
        results = []
        for similar_id, similarity in similar_ids:
            concept = self.concept_index.get_concept(similar_id)
            if concept:
                results.append((concept, similarity))
                
        return results
        
    # -------------------------------------------------------------------------
    # Relation Management
    # -------------------------------------------------------------------------
    
    def add_relation(self,
                    source_id: str,
                    target_id: str,
                    relation_type: str,
                    name: Optional[str] = None,
                    weight: float = 1.0,
                    bidirectional: bool = False,
                    properties: Optional[Dict[str, Any]] = None,
                    embedding: Optional[np.ndarray] = None,
                    provenance: Optional[str] = None,
                    confidence: float = 1.0,
                    metadata: Optional[Dict[str, Any]] = None,
                    relation_id: Optional[str] = None) -> Optional[str]:
        """
        Add a new relation between concepts.
        
        Args:
            source_id: ID of source concept
            target_id: ID of target concept
            relation_type: Type of relation
            name: Optional name for the relation
            weight: Weight of the relation
            bidirectional: Whether the relation is bidirectional
            properties: Optional properties for the relation
            embedding: Optional pre-computed embedding
            provenance: Optional provenance information
            confidence: Confidence score (0-1)
            metadata: Optional metadata
            relation_id: Optional ID (generated if not provided)
            
        Returns:
            ID of the new relation, or None if source or target not found
        """
        # Verify source and target concepts exist
        source = self.concept_index.get_concept(source_id)
        target = self.concept_index.get_concept(target_id)
        
        if not source or not target:
            return None
            
        # Generate ID if not provided
        if not relation_id:
            relation_id = str(uuid.uuid4())
            
        # Generate name if not provided
        if not name:
            name = f"{relation_type}_{source.name}_{target.name}"
            
        # Create relation
        relation = SemanticRelation(
            id=relation_id,
            name=name,
            source_id=source_id,
            target_id=target_id,
            relation_type=relation_type,
            weight=weight,
            bidirectional=bidirectional,
            properties=properties or {},
            embedding=embedding,
            provenance=provenance,
            confidence=confidence,
            metadata=metadata or {}
        )
        
        # Add to index
        self.relation_index.add_relation(relation)
        
        # Create embedding if not provided
        if embedding is None and self.knowledge_embedding:
            embedding = self.knowledge_embedding.create_relation_embedding(relation)
            relation.embedding = embedding
            
        # Add embedding
        if embedding is not None and self.knowledge_embedding:
            self.knowledge_embedding.add_relation_embedding(relation_id, embedding)
            
        # Add to hierarchy if is_a relation
        if relation_type == "is_a" and self.concept_hierarchy:
            self.concept_hierarchy.add_concept(source_id, target_id)
            
        # Update statistics
        self.stats['relation_count'] += 1
        self.stats['last_modified'] = time.time()
        
        # Clear inference cache
        if hasattr(self.inference_engine, 'clear_cache'):
            self.inference_engine.clear_cache()
            
        return relation_id
        
    def get_relation(self, relation_id: str) -> Optional[SemanticRelation]:
        """
        Get a relation by ID.
        
        Args:
            relation_id: ID of the relation
            
        Returns:
            The relation if found, None otherwise
        """
        return self.relation_index.get_relation(relation_id)
        
    def update_relation(self,
                       relation_id: str,
                       name: Optional[str] = None,
                       weight: Optional[float] = None,
                       bidirectional: Optional[bool] = None,
                       properties: Optional[Dict[str, Any]] = None,
                       embedding: Optional[np.ndarray] = None,
                       provenance: Optional[str] = None,
                       confidence: Optional[float] = None,
                       metadata: Optional[Dict[str, Any]] = None) -> bool:
        """
        Update an existing relation.
        
        Args:
            relation_id: ID of the relation to update
            name: Optional new name
            weight: Optional new weight
            bidirectional: Optional new bidirectional flag
            properties: Optional new properties (replaces existing)
            embedding: Optional new embedding
            provenance: Optional new provenance
            confidence: Optional new confidence score
            metadata: Optional new metadata (merged with existing)
            
        Returns:
            True if updated successfully, False if relation not found
        """
        # Get existing relation
        relation = self.relation_index.get_relation(relation_id)
        if not relation:
            return False
            
        # Update fields if provided
        if name is not None:
            relation.name = name
        if weight is not None:
            relation.weight = weight
        if bidirectional is not None:
            relation.bidirectional = bidirectional
        if properties is not None:
            relation.properties = properties
        if embedding is not None:
            relation.embedding = embedding
        if provenance is not None:
            relation.provenance = provenance
        if confidence is not None:
            relation.confidence = confidence
        if metadata is not None:
            # Merge with existing metadata
            relation.metadata.update(metadata)
            
        # Update in index
        self.relation_index.update_relation(relation)
        
        # Update embedding if provided
        if embedding is not None and self.knowledge_embedding:
            self.knowledge_embedding.add_relation_embedding(relation_id, embedding)
            
        # Update statistics
        self.stats['last_modified'] = time.time()
        
        # Clear inference cache
        if hasattr(self.inference_engine, 'clear_cache'):
            self.inference_engine.clear_cache()
            
        return True
        
    def delete_relation(self, relation_id: str) -> bool:
        """
        Delete a relation from the knowledge base.
        
        Args:
            relation_id: ID of the relation to delete
            
        Returns:
            True if deleted successfully, False if relation not found
        """
        # Get relation
        relation = self.relation_index.get_relation(relation_id)
        if not relation:
            return False
            
        # Remove from hierarchy if it's an is_a relation
        if relation.relation_type == "is_a" and self.concept_hierarchy:
            children = self.concept_hierarchy.get_children(relation.target_id)
            if relation.source_id in children:
                self.concept_hierarchy.remove_concept(relation.source_id)
                
        # Remove from relation index
        self.relation_index.remove_relation(relation_id)
        
        # Update statistics
        self.stats['relation_count'] -= 1
        self.stats['last_modified'] = time.time()
        
        # Clear inference cache
        if hasattr(self.inference_engine, 'clear_cache'):
            self.inference_engine.clear_cache()
            
        return True
        
    def get_related_concepts(self, concept_id: str,
                           relation_type: Optional[str] = None,
                           direction: str = 'both') -> List[Tuple[SemanticConcept, SemanticRelation]]:
        """
        Get concepts related to a given concept.
        
        Args:
            concept_id: ID of the concept
            relation_type: Optional type of relation to filter by
            direction: Direction of relations ('outgoing', 'incoming', or 'both')
            
        Returns:
            List of (related_concept, relation) tuples
        """
        # Get relations based on direction
        relations = []
        
        if direction in ('outgoing', 'both'):
            outgoing = self.relation_index.get_outgoing_relations(concept_id)
            if relation_type:
                outgoing = [r for r in outgoing if r.relation_type == relation_type]
            relations.extend(outgoing)
            
        if direction in ('incoming', 'both'):
            incoming = self.relation_index.get_incoming_relations(concept_id)
            if relation_type:
                incoming = [r for r in incoming if r.relation_type == relation_type]
            relations.extend(incoming)
            
        # Get related concepts
        results = []
        for relation in relations:
            # Determine the related concept ID
            related_id = relation.target_id if relation.source_id == concept_id else relation.source_id
            
            # Get the related concept
            related_concept = self.concept_index.get_concept(related_id)
            if related_concept:
                results.append((related_concept, relation))
                
        return results
        
    def find_path(self, source_id: str, target_id: str,
                max_depth: int = 3) -> List[List[SemanticRelation]]:
        """
        Find paths between two concepts.
        
        Args:
            source_id: ID of source concept
            target_id: ID of target concept
            max_depth: Maximum path length
            
        Returns:
            List of relation paths (each path is a list of relations)
        """
        return self.relation_index.search_path(source_id, target_id, max_depth)
        
    # -------------------------------------------------------------------------
    # Knowledge Hierarchy Management
    # -------------------------------------------------------------------------
    
    def add_to_hierarchy(self, concept_id: str, parent_id: Optional[str] = None) -> bool:
        """
        Add a concept to the hierarchy.
        
        Args:
            concept_id: ID of the concept to add
            parent_id: Optional ID of parent concept
            
        Returns:
            True if added successfully, False otherwise
        """
        if not self.concept_hierarchy:
            return False
            
        # Check if concepts exist
        if not self.concept_index.get_concept(concept_id):
            return False
            
        if parent_id and not self.concept_index.get_concept(parent_id):
            return False
            
        # Add to hierarchy
        success = self.concept_hierarchy.add_concept(concept_id, parent_id)
        
        # If successful and parent_id is provided, also create an is_a relation
        if success and parent_id:
            # Check if relation already exists
            existing = self.relation_index.find_between_concepts(
                concept_id, parent_id, "is_a")
                
            if not existing:
                self.add_relation(
                    source_id=concept_id,
                    target_id=parent_id,
                    relation_type="is_a",
                    name=f"is_a_{concept_id}_{parent_id}"
                )
                
        return success
        
    def get_parent_concept(self, concept_id: str) -> Optional[SemanticConcept]:
        """
        Get the parent concept of a concept.
        
        Args:
            concept_id: ID of the concept
            
        Returns:
            Parent concept, or None if no parent or not found
        """
        if not self.concept_hierarchy:
            return None
            
        # Get parent ID
        parent_id = self.concept_hierarchy.get_parent(concept_id)
        if not parent_id:
            return None
            
        # Get parent concept
        return self.concept_index.get_concept(parent_id)
        
    def get_child_concepts(self, concept_id: str) -> List[SemanticConcept]:
        """
        Get immediate child concepts of a concept.
        
        Args:
            concept_id: ID of the concept
            
        Returns:
            List of child concepts
        """
        if not self.concept_hierarchy:
            return []
            
        # Get child IDs
        child_ids = self.concept_hierarchy.get_children(concept_id)
        
        # Get child concepts
        return [self.concept_index.get_concept(cid) for cid in child_ids 
                if self.concept_index.get_concept(cid)]
                
    def get_ancestor_concepts(self, concept_id: str) -> List[SemanticConcept]:
        """
        Get all ancestor concepts of a concept.
        
        Args:
            concept_id: ID of the concept
            
        Returns:
            List of ancestor concepts
        """
        if not self.concept_hierarchy:
            return []
            
        # Get ancestor IDs
        ancestor_ids = self.concept_hierarchy.get_ancestors(concept_id)
        
        # Get ancestor concepts
        return [self.concept_index.get_concept(aid) for aid in ancestor_ids 
                if self.concept_index.get_concept(aid)]
                
    def get_descendant_concepts(self, concept_id: str) -> List[SemanticConcept]:
        """
        Get all descendant concepts of a concept.
        
        Args:
            concept_id: ID of the concept
            
        Returns:
            List of descendant concepts
        """
        if not self.concept_hierarchy:
            return []
            
        # Get descendant IDs
        descendant_ids = self.concept_hierarchy.get_descendants(concept_id)
        
        # Get descendant concepts
        return [self.concept_index.get_concept(did) for did in descendant_ids 
                if self.concept_index.get_concept(did)]
                
    def check_is_subclass(self, concept_id: str, ancestor_id: str) -> bool:
        """
        Check if a concept is a subclass of another concept.
        
        Args:
            concept_id: ID of the concept to check
            ancestor_id: ID of potential ancestor
            
        Returns:
            True if concept is a subclass of ancestor, False otherwise
        """
        if not self.concept_hierarchy:
            return False
            
        return self.concept_hierarchy.is_subclass_of(concept_id, ancestor_id)
        
    def get_lowest_common_ancestor(self, concept_id1: str, concept_id2: str) -> Optional[SemanticConcept]:
        """
        Find the lowest common ancestor of two concepts.
        
        Args:
            concept_id1: ID of first concept
            concept_id2: ID of second concept
            
        Returns:
            Lowest common ancestor concept, or None if not found
        """
        if not self.concept_hierarchy:
            return None
            
        # Get LCA ID
        lca_id = self.concept_hierarchy.get_lowest_common_ancestor(concept_id1, concept_id2)
        if not lca_id:
            return None
            
        # Get LCA concept
        return self.concept_index.get_concept(lca_id)
        
    # -------------------------------------------------------------------------
    # Knowledge Triple Management
    # -------------------------------------------------------------------------
    
    def add_triple(self, subject_id: str, predicate: str, object_id: str,
                 confidence: float = 1.0,
                 provenance: Optional[str] = None,
                 metadata: Optional[Dict[str, Any]] = None) -> Optional[str]:
        """
        Add a knowledge triple to the knowledge base.
        
        Args:
            subject_id: ID of subject concept
            predicate: Relation predicate
            object_id: ID of object concept
            confidence: Confidence score (0-1)
            provenance: Optional provenance information
            metadata: Optional metadata
            
        Returns:
            ID of the new triple (relation), or None if subject or object not found
        """
        # Create relation for the triple
        relation_id = self.add_relation(
            source_id=subject_id,
            target_id=object_id,
            relation_type=predicate,
            confidence=confidence,
            provenance=provenance,
            metadata=metadata
        )
        
        if relation_id:
            # Update statistics
            self.stats['triple_count'] += 1
            
        return relation_id
        
    def get_triples(self, subject_id: Optional[str] = None,
                  predicate: Optional[str] = None,
                  object_id: Optional[str] = None) -> List[KnowledgeTriple]:
        """
        Get knowledge triples matching the specified criteria.
        
        Args:
            subject_id: Optional subject concept ID
            predicate: Optional relation predicate
            object_id: Optional object concept ID
            
        Returns:
            List of matching triples
        """
        triples = []
        
        # Get relations based on criteria
        relations = []
        
        if subject_id and object_id:
            # Get relations between subject and object
            relations = self.relation_index.find_between_concepts(
                subject_id, object_id, predicate)
        elif subject_id:
            # Get outgoing relations from subject
            relations = self.relation_index.get_outgoing_relations(subject_id)
            if predicate:
                relations = [r for r in relations if r.relation_type == predicate]
        elif object_id:
            # Get incoming relations to object
            relations = self.relation_index.get_incoming_relations(object_id)
            if predicate:
                relations = [r for r in relations if r.relation_type == predicate]
        elif predicate:
            # Get relations of specified type
            relations = self.relation_index.find_by_type(predicate)
            
        # Convert relations to triples
        for relation in relations:
            triple = KnowledgeTriple(
                id=relation.id,
                subject_id=relation.source_id,
                predicate=relation.relation_type,
                object_id=relation.target_id,
                confidence=relation.confidence,
                provenance=relation.provenance,
                metadata=relation.metadata
            )
            triples.append(triple)
            
        return triples
        
    def delete_triple(self, triple_id: str) -> bool:
        """
        Delete a knowledge triple from the knowledge base.
        
        Args:
            triple_id: ID of the triple (relation) to delete
            
        Returns:
            True if deleted successfully, False if not found
        """
        # Delete the corresponding relation
        success = self.delete_relation(triple_id)
        
        if success:
            # Update statistics
            self.stats['triple_count'] -= 1
            
        return success
        
    # -------------------------------------------------------------------------
    # Inference and Reasoning
    # -------------------------------------------------------------------------
    
    def infer_relations(self, concept_id: str,
                      relation_types: Optional[List[str]] = None,
                      max_depth: int = 2,
                      min_confidence: float = 0.5) -> List[KnowledgeTriple]:
        """
        Infer relations for a concept.
        
        Args:
            concept_id: ID of the concept
            relation_types: Optional list of relation types to consider
            max_depth: Maximum inference depth
            min_confidence: Minimum confidence for inferred relations
            
        Returns:
            List of inferred triples
        """
        if not self.config.get('inference_enabled', True):
            return []
            
        # Use inference engine to infer relations
        return self.inference_engine.infer_relations(
            concept_id=concept_id,
            relation_types=relation_types,
            max_depth=max_depth,
            min_confidence=min_confidence
        )
        
    def infer_types(self, concept_id: str,
                  min_confidence: float = 0.6) -> List[Tuple[SemanticConcept, float]]:
        """
        Infer types (classes) for a concept.
        
        Args:
            concept_id: ID of the concept
            min_confidence: Minimum confidence for inferred types
            
        Returns:
            List of (type_concept, confidence) tuples
        """
        if not self.config.get('inference_enabled', True):
            return []
            
        # Get inferred type IDs and confidences
        type_results = self.inference_engine.infer_types(
            concept_id=concept_id,
            min_confidence=min_confidence
        )
        
        # Convert to concepts
        return [(self.concept_index.get_concept(tid), conf) 
                for tid, conf in type_results 
                if self.concept_index.get_concept(tid)]
                
    def infer_properties(self, concept_id: str,
                       min_confidence: float = 0.6) -> Dict[str, Tuple[Any, float]]:
        """
        Infer properties for a concept.
        
        Args:
            concept_id: ID of the concept
            min_confidence: Minimum confidence for inferred properties
            
        Returns:
            Dictionary mapping property names to (value, confidence) tuples
        """
        if not self.config.get('inference_enabled', True):
            return {}
            
        # Use inference engine to infer properties
        return self.inference_engine.infer_properties(
            concept_id=concept_id,
            min_confidence=min_confidence
        )
        
    def validate_fact(self, subject_id: str, predicate: str, object_id: str) -> float:
        """
        Validate a fact and return a confidence score.
        
        Args:
            subject_id: ID of subject concept
            predicate: Relation predicate
            object_id: ID of object concept
            
        Returns:
            Confidence score between 0 and 1
        """
        if not self.config.get('inference_enabled', True):
            # Check if the fact exists explicitly
            relations = self.relation_index.find_between_concepts(
                subject_id, object_id, predicate)
            return relations[0].confidence if relations else 0.0
            
        # Use inference engine to validate fact
        return self.inference_engine.validate_fact(
            subject_id=subject_id,
            predicate=predicate,
            object_id=object_id
        )
        
    def generate_new_knowledge(self, max_results: int = 100, 
                             min_confidence: float = 0.7) -> List[KnowledgeTriple]:
        """
        Generate new knowledge through inference.
        
        Args:
            max_results: Maximum number of results to return
            min_confidence: Minimum confidence for generated knowledge
            
        Returns:
            List of inferred knowledge triples
        """
        if not self.config.get('inference_enabled', True):
            return []
            
        # Use inference engine to infer new relations
        return self.inference_engine.infer_new_relations(
            min_confidence=min_confidence,
            max_results=max_results
        )
        
    # -------------------------------------------------------------------------
    # Schema Management
    # -------------------------------------------------------------------------
    
    def add_ontology_class(self, name: str, 
                         description: str = "",
                         properties: Optional[Dict[str, Dict[str, Any]]] = None,
                         restrictions: Optional[Dict[str, Any]] = None,
                         equivalences: Optional[List[str]] = None,
                         disjoint_with: Optional[List[str]] = None,
                         metadata: Optional[Dict[str, Any]] = None,
                         class_id: Optional[str] = None) -> str:
        """
        Add an ontology class definition.
        
        Args:
            name: Name of the class
            description: Optional description
            properties: Optional property definitions
            restrictions: Optional property restrictions
            equivalences: Optional equivalent class IDs
            disjoint_with: Optional disjoint class IDs
            metadata: Optional metadata
            class_id: Optional ID (generated if not provided)
            
        Returns:
            ID of the new ontology class
        """
        # Generate ID if not provided
        if not class_id:
            class_id = str(uuid.uuid4())
            
        # Create ontology class
        ontology_class = OntologyClass(
            id=class_id,
            name=name,
            description=description,
            properties=properties or {},
            restrictions=restrictions or {},
            equivalences=equivalences or [],
            disjoint_with=disjoint_with or [],
            metadata=metadata or {}
        )
        
        # Add to schema validator
        self.schema_validator.add_class(ontology_class)
        
        return class_id
        
    def get_ontology_class(self, class_id: str) -> Optional[OntologyClass]:
        """
        Get an ontology class by ID.
        
        Args:
            class_id: ID of the ontology class
            
        Returns:
            The ontology class if found, None otherwise
        """
        return self.schema_validator.get_class(class_id)
        
    def validate_concept_against_class(self, concept_id: str, 
                                     class_id: str) -> Tuple[bool, List[str]]:
        """
        Validate a concept against an ontology class.
        
        Args:
            concept_id: ID of the concept to validate
            class_id: ID of the ontology class
            
        Returns:
            Tuple of (is_valid, error_messages)
        """
        # Get concept
        concept = self.concept_index.get_concept(concept_id)
        if not concept:
            return False, ["Concept not found"]
            
        # Validate against class
        return self.schema_validator.validate_concept(concept, class_id)
        
    def validate_relation_against_schema(self, relation_id: str,
                                      source_class_id: str,
                                      target_class_id: str,
                                      relation_type: Optional[str] = None) -> Tuple[bool, List[str]]:
        """
        Validate a relation against ontology classes.
        
        Args:
            relation_id: ID of the relation to validate
            source_class_id: ID of source ontology class
            target_class_id: ID of target ontology class
            relation_type: Optional specific relation type to validate against
            
        Returns:
            Tuple of (is_valid, error_messages)
        """
        # Get relation
        relation = self.relation_index.get_relation(relation_id)
        if not relation:
            return False, ["Relation not found"]
            
        # Validate against classes
        return self.schema_validator.validate_relation(
            relation, source_class_id, target_class_id, relation_type)
            
    # -------------------------------------------------------------------------
    # Embedding Management
    # -------------------------------------------------------------------------
    
    def update_concept_embedding(self, concept_id: str, 
                               embedding: Optional[np.ndarray] = None) -> bool:
        """
        Update the embedding for a concept.
        
        Args:
            concept_id: ID of the concept
            embedding: New embedding, or None to recompute
            
        Returns:
            True if updated successfully, False if concept not found
        """
        if not self.knowledge_embedding:
            return False
            
        # Get concept
        concept = self.concept_index.get_concept(concept_id)
        if not concept:
            return False
            
        # Create new embedding if not provided
        if embedding is None:
            embedding = self.knowledge_embedding.create_concept_embedding(concept)
            concept.embedding = embedding
            
        # Update embedding
        self.knowledge_embedding.add_concept_embedding(concept_id, embedding)
        
        # Update concept
        concept.embedding = embedding
        self.concept_index.update_concept(concept)
        
        return True
        
    def update_relation_embedding(self, relation_id: str,
                                embedding: Optional[np.ndarray] = None) -> bool:
        """
        Update the embedding for a relation.
        
        Args:
            relation_id: ID of the relation
            embedding: New embedding, or None to recompute
            
        Returns:
            True if updated successfully, False if relation not found
        """
        if not self.knowledge_embedding:
            return False
            
        # Get relation
        relation = self.relation_index.get_relation(relation_id)
        if not relation:
            return False
            
        # Create new embedding if not provided
        if embedding is None:
            embedding = self.knowledge_embedding.create_relation_embedding(relation)
            relation.embedding = embedding
            
        # Update embedding
        self.knowledge_embedding.add_relation_embedding(relation_id, embedding)
        
        # Update relation
        relation.embedding = embedding
        self.relation_index.update_relation(relation)
        
        return True
        
    def train_relation_matrix(self, relation_type: str,
                           positive_examples: List[Tuple[str, str]],
                           negative_examples: Optional[List[Tuple[str, str]]] = None,
                           learning_rate: float = 0.01,
                           num_epochs: int = 50) -> bool:
        """
        Train a relation matrix using examples.
        
        Args:
            relation_type: Type of relation to train
            positive_examples: List of (head_id, tail_id) pairs with the relation
            negative_examples: Optional list of negative examples
            learning_rate: Learning rate for training
            num_epochs: Number of training epochs
            
        Returns:
            True if trained successfully, False otherwise
        """
        if not self.knowledge_embedding:
            return False
            
        # Train relation matrix
        self.knowledge_embedding.train_relation_matrix(
            relation_id=relation_type,
            positive_pairs=positive_examples,
            negative_pairs=negative_examples,
            learning_rate=learning_rate,
            num_epochs=num_epochs
        )
        
        # Clear inference cache
        if hasattr(self.inference_engine, 'clear_cache'):
            self.inference_engine.clear_cache()
            
        return True
        
    def bulk_update_embeddings(self, batch_size: int = 100) -> int:
        """
        Update embeddings for all concepts and relations.
        
        Args:
            batch_size: Batch size for updates
            
        Returns:
            Number of embeddings updated
        """
        if not self.knowledge_embedding or not self.embedding_model:
            return 0
            
        count = 0
        
        # Update concept embeddings
        concept_ids = list(self.concept_index.concepts_by_id.keys())
        for i in range(0, len(concept_ids), batch_size):
            batch = concept_ids[i:i+batch_size]
            for concept_id in batch:
                concept = self.concept_index.get_concept(concept_id)
                if concept:
                    embedding = self.knowledge_embedding.create_concept_embedding(concept)
                    self.knowledge_embedding.add_concept_embedding(concept_id, embedding)
                    concept.embedding = embedding
                    count += 1
                    
        # Update relation embeddings
        relation_ids = list(self.relation_index.relations_by_id.keys())
        for i in range(0, len(relation_ids), batch_size):
            batch = relation_ids[i:i+batch_size]
            for relation_id in batch:
                relation = self.relation_index.get_relation(relation_id)
                if relation:
                    embedding = self.knowledge_embedding.create_relation_embedding(relation)
                    self.knowledge_embedding.add_relation_embedding(relation_id, embedding)
                    relation.embedding = embedding
                    count += 1
                    
        return count
        
    # -------------------------------------------------------------------------
    # Import/Export and Persistence
    # -------------------------------------------------------------------------
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert knowledge base to dictionary for serialization.
        
        Returns:
            Dictionary representation of the knowledge base
        """
        # Create dictionary representation
        data = {
            'version': self.config.get('version', '1.0'),
            'stats': self.stats.copy(),
            'config': self.config.copy(),
            'concepts': {},
            'relations': {},
            'ontology_classes': {}
        }
        
        # Add concepts
        for concept_id, concept in self.concept_index.concepts_by_id.items():
            data['concepts'][concept_id] = concept.to_dict()
            
        # Add relations
        for relation_id, relation in self.relation_index.relations_by_id.items():
            data['relations'][relation_id] = relation.to_dict()
            
        # Add ontology classes
        if hasattr(self.schema_validator, 'ontology'):
            for class_id, cls in self.schema_validator.ontology.items():
                data['ontology_classes'][class_id] = cls.to_dict()
                
        # Add hierarchy
        if self.concept_hierarchy:
            data['hierarchy'] = self.concept_hierarchy.to_dict()
            
        # Add embeddings
        if self.knowledge_embedding:
            data['embeddings'] = self.knowledge_embedding.to_dict()
            
        return data
        
    @classmethod
    def from_dict(cls, data: Dict[str, Any],
                embedding_model: Optional[Callable] = None,
                use_gpu: bool = False,
                storage_dir: Optional[str] = None) -> 'SemanticKnowledge':
        """
        Create knowledge base from dictionary.
        
        Args:
            data: Dictionary representation of knowledge base
            embedding_model: Optional function for creating embeddings
            use_gpu: Whether to use GPU acceleration if available
            storage_dir: Optional directory for persistent storage
            
        Returns:
            SemanticKnowledge instance
        """
        # Extract embedding dimension
        embedding_dim = 768
        if 'embeddings' in data:
            embedding_dim = data['embeddings'].get('embedding_dim', 768)
            
        # Create instance
        kb = cls(
            embedding_dim=embedding_dim,
            embedding_model=embedding_model,
            use_gpu=use_gpu,
            storage_dir=storage_dir
        )
        
        # Set config
        kb.config.update(data.get('config', {}))
        kb.stats.update(data.get('stats', {}))
        
        # Add ontology classes
        for class_id, class_data in data.get('ontology_classes', {}).items():
            ontology_class = OntologyClass.from_dict(class_data)
            kb.schema_validator.add_class(ontology_class)
            
        # Add concepts
        for concept_id, concept_data in data.get('concepts', {}).items():
            concept = SemanticConcept.from_dict(concept_data)
            kb.concept_index.add_concept(concept)
            
        # Add embeddings
        if 'embeddings' in data and kb.knowledge_embedding:
            kb.knowledge_embedding = KnowledgeEmbedding.from_dict(
                data['embeddings'], embedding_model)
                
        # Restore hierarchy
        if 'hierarchy' in data and kb.concept_hierarchy:
            kb.concept_hierarchy = ConceptHierarchy.from_dict(data['hierarchy'])
            
        # Add relations
        for relation_id, relation_data in data.get('relations', {}).items():
            relation = SemanticRelation.from_dict(relation_data)
            kb.relation_index.add_relation(relation)
            
        # Reinitialize inference engine
        kb.inference_engine = InferenceEngine(
            concept_index=kb.concept_index,
            relation_index=kb.relation_index,
            knowledge_embedding=kb.knowledge_embedding,
            concept_hierarchy=kb.concept_hierarchy
        )
        
        return kb
        
    def save(self, path: Optional[str] = None) -> bool:
        """
        Save knowledge base to disk.
        
        Args:
            path: Path to save to, or None to use storage_dir/kb.json
            
        Returns:
            True if saved successfully, False otherwise
        """
        # Determine path
        if not path:
            if not self.storage_dir:
                return False
            path = os.path.join(self.storage_dir, 'kb.json')
            
        try:
            # Convert to dictionary
            data = self.to_dict()
            
            # Save to disk
            with open(path, 'w') as f:
                json.dump(data, f, indent=2)
                
            return True
        except Exception as e:
            logger.error(f"Error saving knowledge base: {e}")
            return False
            
    @classmethod
    def load(cls, path: str,
           embedding_model: Optional[Callable] = None,
           use_gpu: bool = False,
           storage_dir: Optional[str] = None) -> Optional['SemanticKnowledge']:
        """
        Load knowledge base from disk.
        
        Args:
            path: Path to load from
            embedding_model: Optional function for creating embeddings
            use_gpu: Whether to use GPU acceleration if available
            storage_dir: Optional directory for persistent storage
            
        Returns:
            SemanticKnowledge instance if loaded successfully, None otherwise
        """
        try:
            # Load from disk
            with open(path, 'r') as f:
                data = json.load(f)
                
            # Create instance
            kb = cls.from_dict(
                data=data,
                embedding_model=embedding_model,
                use_gpu=use_gpu,
                storage_dir=storage_dir
            )
            
            return kb
        except Exception as e:
            logger.error(f"Error loading knowledge base: {e}")
            return None
            
    def import_from_triples(self, triples: List[Tuple[str, str, str]],
                          create_missing: bool = True) -> int:
        """
        Import knowledge from a list of triples.
        
        Args:
            triples: List of (subject, predicate, object) string tuples
            create_missing: Whether to create concepts if they don't exist
            
        Returns:
            Number of triples imported
        """
        imported = 0
        
        # Process triples
        for subj, pred, obj in triples:
            # Normalize strings
            subj = subj.strip()
            pred = pred.strip()
            obj = obj.strip()
            
            # Skip empty strings
            if not subj or not pred or not obj:
                continue
                
            # Check if subject and object concepts exist
            subj_id = None
            obj_id = None
            
            # Find by name
            subj_concepts = self.concept_index.find_by_name(subj)
            obj_concepts = self.concept_index.find_by_name(obj)
            
            if subj_concepts:
                subj_id = subj_concepts[0].id
            elif create_missing:
                subj_id = self.add_concept(name=subj)
                
            if obj_concepts:
                obj_id = obj_concepts[0].id
            elif create_missing:
                obj_id = self.add_concept(name=obj)
                
            # Skip if can't resolve subject or object
            if not subj_id or not obj_id:
                continue
                
            # Add triple
            self.add_triple(
                subject_id=subj_id,
                predicate=pred,
                object_id=obj_id
            )
            
            imported += 1
            
        return imported
        
    def export_to_triples(self, include_names: bool = True) -> List[Tuple[str, str, str]]:
        """
        Export knowledge as a list of triples.
        
        Args:
            include_names: Whether to include concept names (instead of IDs)
            
        Returns:
            List of (subject, predicate, object) string tuples
        """
        triples = []
        
        # Process all relations
        for relation_id, relation in self.relation_index.relations_by_id.items():
            # Get subject and object
            subject_id = relation.source_id
            predicate = relation.relation_type
            object_id = relation.target_id
            
            if include_names:
                # Convert IDs to names
                subject = self.concept_index.get_concept(subject_id)
                obj = self.concept_index.get_concept(object_id)
                
                if subject and obj:
                    triples.append((subject.name, predicate, obj.name))
            else:
                # Use IDs
                triples.append((subject_id, predicate, object_id))
                
        return triples
        
    def import_from_csv(self, csv_path: str, 
                      subject_col: str = 'subject',
                      predicate_col: str = 'predicate',
                      object_col: str = 'object',
                      create_missing: bool = True) -> int:
        """
        Import knowledge from a CSV file.
        
        Args:
            csv_path: Path to CSV file
            subject_col: Column name for subjects
            predicate_col: Column name for predicates
            object_col: Column name for objects
            create_missing: Whether to create concepts if they don't exist
            
        Returns:
            Number of triples imported
        """
        try:
            import csv
            
            triples = []
            
            # Read CSV file
            with open(csv_path, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                
                for row in reader:
                    # Get subject, predicate, object
                    if subject_col in row and predicate_col in row and object_col in row:
                        subj = row[subject_col]
                        pred = row[predicate_col]
                        obj = row[object_col]
                        
                        triples.append((subj, pred, obj))
                        
            # Import triples
            return self.import_from_triples(triples, create_missing)
            
        except Exception as e:
            logger.error(f"Error importing from CSV: {e}")
            return 0
            
    def export_to_csv(self, csv_path: str,
                    include_names: bool = True,
                    include_metadata: bool = False) -> bool:
        """
        Export knowledge to a CSV file.
        
        Args:
            csv_path: Path to CSV file
            include_names: Whether to include concept names (instead of IDs)
            include_metadata: Whether to include confidence and metadata
            
        Returns:
            True if exported successfully, False otherwise
        """
        try:
            import csv
            
            # Get triples
            triples = self.export_to_triples(include_names)
            
            # Define columns
            columns = ['subject', 'predicate', 'object']
            if include_metadata:
                columns.extend(['confidence', 'provenance'])
                
            # Write CSV file
            with open(csv_path, 'w', encoding='utf-8', newline='') as f:
                writer = csv.writer(f)
                
                # Write header
                writer.writerow(columns)
                
                # Write triples
                for subj, pred, obj in triples:
                    row = [subj, pred, obj]
                    
                    if include_metadata:
                        # Find relation to get metadata
                        relation = None
                        
                        if include_names:
                            # Find by name
                            subj_concepts = self.concept_index.find_by_name(subj, exact_match=True)
                            obj_concepts = self.concept_index.find_by_name(obj, exact_match=True)
                            
                            if subj_concepts and obj_concepts:
                                relations = self.relation_index.find_between_concepts(
                                    subj_concepts[0].id, obj_concepts[0].id, pred)
                                if relations:
                                    relation = relations[0]
                        else:
                            # Find by ID
                            relations = self.relation_index.find_between_concepts(
                                subj, obj, pred)
                            if relations:
                                relation = relations[0]
                                
                        # Add metadata
                        if relation:
                            row.append(str(relation.confidence))
                            row.append(relation.provenance or '')
                        else:
                            row.extend(['', ''])
                            
                    writer.writerow(row)
                    
            return True
            
        except Exception as e:
            logger.error(f"Error exporting to CSV: {e}")
            return False
            
    # -------------------------------------------------------------------------
    # Utility Methods
    # -------------------------------------------------------------------------
    
    def get_stats(self) -> Dict[str, Any]:
        """
        Get knowledge base statistics.
        
        Returns:
            Dictionary of statistics
        """
        # Update counts
        self.stats['concept_count'] = len(self.concept_index.concepts_by_id)
        self.stats['relation_count'] = len(self.relation_index.relations_by_id)
        
        # Add more detailed stats
        detailed_stats = self.stats.copy()
        
        # Add category stats
        category_counts = {}
        for category, concept_ids in self.concept_index.concepts_by_category.items():
            category_counts[category] = len(concept_ids)
        detailed_stats['categories'] = category_counts
        
        # Add relation type stats
        relation_type_counts = {}
        for relation_type, relation_ids in self.relation_index.relations_by_type.items():
            relation_type_counts[relation_type] = len(relation_ids)
        detailed_stats['relation_types'] = relation_type_counts
        
        # Add hierarchy stats if available
        if self.concept_hierarchy:
            hierarchy_stats = {
                'depth': 0,
                'breadth': 0,
                'leaf_count': 0
            }
            
            # Count children of root
            if self.concept_hierarchy.root_id:
                root_children = self.concept_hierarchy.get_children(self.concept_hierarchy.root_id)
                hierarchy_stats['breadth'] = len(root_children)
                
                # Find maximum depth
                max_depth = 0
                leaf_count = 0
                
                for concept_id in self.concept_index.concepts_by_id:
                    depth = self.concept_hierarchy.get_depth(concept_id)
                    if depth > max_depth:
                        max_depth = depth
                        
                    # Check if leaf node
                    children = self.concept_hierarchy.get_children(concept_id)
                    if not children:
                        leaf_count += 1
                        
                hierarchy_stats['depth'] = max_depth
                hierarchy_stats['leaf_count'] = leaf_count
                
            detailed_stats['hierarchy'] = hierarchy_stats
            
        return detailed_stats
        
    def clear(self) -> None:
        """Clear all knowledge from the knowledge base."""
        # Reset components
        self.concept_index = ConceptIndex()
        self.relation_index = RelationIndex()
        self.concept_hierarchy = ConceptHierarchy()
        
        # Reinitialize knowledge embedding with same parameters
        self.knowledge_embedding = KnowledgeEmbedding(
            embedding_dim=self.embedding_dim,
            embedding_model=self.embedding_model,
            use_gpu=self.use_gpu
        )
        
        # Reset schema validator
        self.schema_validator = SchemaValidator()
        
        # Reinitialize inference engine
        self.inference_engine = InferenceEngine(
            concept_index=self.concept_index,
            relation_index=self.relation_index,
            knowledge_embedding=self.knowledge_embedding,
            concept_hierarchy=self.concept_hierarchy
        )
        
        # Reset statistics
        self.stats = {
            'concept_count': 0,
            'relation_count': 0,
            'triple_count': 0,
            'last_modified': time.time()
        }
        
    def search(self, query: str, include_related: bool = True, 
             match_threshold: float = 0.5,
             max_results: int = 10) -> Dict[str, Any]:
        """
        Perform a comprehensive search across the knowledge base.
        
        Args:
            query: Search query
            include_related: Whether to include related concepts
            match_threshold: Minimum match score threshold
            max_results: Maximum number of results
            
        Returns:
            Dictionary with search results
        """
        results = {
            'concepts': [],
            'relations': [],
            'related': []
        }
        
        # Search concepts
        concept_results = self.concept_index.search(query, match_threshold)
        
        # Sort by score and limit results
        concept_results.sort(key=lambda x: x[1], reverse=True)
        concept_results = concept_results[:max_results]
        
        for concept, score in concept_results:
            results['concepts'].append({
                'id': concept.id,
                'name': concept.name,
                'description': concept.description,
                'category': concept.category,
                'score': score
            })
            
            # Add related concepts if requested
            if include_related:
                related = self.get_related_concepts(concept.id)
                
                for related_concept, relation in related:
                    results['related'].append({
                        'source': concept.name,
                        'relation': relation.relation_type,
                        'target': related_concept.name,
                        'source_id': concept.id,
                        'relation_id': relation.id,
                        'target_id': related_concept.id
                    })
                    
        # Search relations
        relation_results = []
        
        # Search by name
        for relation_type in self.relation_index.relations_by_type:
            if query.lower() in relation_type.lower():
                relations = self.relation_index.find_by_type(relation_type)
                
                for relation in relations[:5]:  # Limit to 5 examples per type
                    source = self.concept_index.get_concept(relation.source_id)
                    target = self.concept_index.get_concept(relation.target_id)
                    
                    if source and target:
                        score = 1.0 if query.lower() == relation_type.lower() else 0.8
                        relation_results.append((relation, source, target, score))
                        
        # Sort by score and limit results
        relation_results.sort(key=lambda x: x[3], reverse=True)
        relation_results = relation_results[:max_results]
        
        for relation, source, target, score in relation_results:
            results['relations'].append({
                'id': relation.id,
                'type': relation.relation_type,
                'source': source.name,
                'target': target.name,
                'source_id': source.id,
                'target_id': target.id,
                'score': score
            })
            
        return results

# Additional utility functions

def create_knowledge_graph(
    semantic_knowledge: SemanticKnowledge,
    central_concept_id: Optional[str] = None,
    max_nodes: int = 100,
    max_depth: int = 2,
    relation_types: Optional[List[str]] = None
) -> Dict[str, Any]:
    """
    Create a knowledge graph representation suitable for visualization.
    
    Args:
        semantic_knowledge: SemanticKnowledge instance
        central_concept_id: Optional ID of central concept
        max_nodes: Maximum number of nodes in the graph
        max_depth: Maximum distance from central concept
        relation_types: Optional list of relation types to include
        
    Returns:
        Dictionary with nodes and edges for visualization
    """
    # Initialize graph
    graph = {
        'nodes': [],
        'edges': []
    }
    
    # Track processed nodes and edges
    processed_nodes = set()
    processed_edges = set()
    
    # If no central concept specified, use the most connected concept
    if not central_concept_id:
        concept_connections = {}
        for relation_id, relation in semantic_knowledge.relation_index.relations_by_id.items():
            if relation.source_id not in concept_connections:
                concept_connections[relation.source_id] = 0
            if relation.target_id not in concept_connections:
                concept_connections[relation.target_id] = 0
                
            concept_connections[relation.source_id] += 1
            concept_connections[relation.target_id] += 1
            
        if concept_connections:
            central_concept_id = max(concept_connections.items(), key=lambda x: x[1])[0]
        else:
            # If no connections, use first concept
            concept_ids = list(semantic_knowledge.concept_index.concepts_by_id.keys())
            if concept_ids:
                central_concept_id = concept_ids[0]
                
    # If still no central concept, return empty graph
    if not central_concept_id:
        return graph
        
    # Initialize queue with central concept
    queue = deque([(central_concept_id, 0)])  # (concept_id, depth)
    
    while queue and len(processed_nodes) < max_nodes:
        concept_id, depth = queue.popleft()
        
        # Skip if already processed or depth exceeded
        if concept_id in processed_nodes or depth > max_depth:
            continue
            
        # Mark as processed
        processed_nodes.add(concept_id)
        
        # Get concept
        concept = semantic_knowledge.concept_index.get_concept(concept_id)
        if not concept:
            continue
            
        # Add node
        node = {
            'id': concept_id,
            'label': concept.name,
            'category': concept.category,
            'depth': depth
        }
        graph['nodes'].append(node)
        
        # If at max depth, skip adding edges and neighbors
        if depth == max_depth:
            continue
            
        # Get related concepts
        relations = semantic_knowledge.relation_index.get_bidirectional_relations(concept_id)
        
        # Filter by relation type if specified
        if relation_types:
            relations = [r for r in relations if r.relation_type in relation_types]
            
        for relation in relations:
            # Determine the related concept ID
            related_id = relation.target_id if relation.source_id == concept_id else relation.source_id
            
            # Skip if would exceed max nodes
            if related_id not in processed_nodes and len(processed_nodes) >= max_nodes:
                continue
                
            # Create edge ID
            edge_id = f"{relation.source_id}_{relation.relation_type}_{relation.target_id}"
            
            # Skip if already processed
            if edge_id in processed_edges:
                continue
                
            # Mark edge as processed
            processed_edges.add(edge_id)
            
            # Add edge
            edge = {
                'id': edge_id,
                'source': relation.source_id,
                'target': relation.target_id,
                'label': relation.relation_type,
                'weight': relation.weight
            }
            graph['edges'].append(edge)
            
            # Add related concept to queue
            if related_id not in processed_nodes:
                queue.append((related_id, depth + 1))
                
    return graph