#!/usr/bin/env python3
"""
ULTRA: Knowledge Integration

This module implements mechanisms for integrating knowledge across different knowledge types
(episodic, semantic, procedural) and modalities. It provides conflict resolution, inference,
and contextual retrieval capabilities to create a unified knowledge representation system.

The key components include:
1. KnowledgeIntegrator: Coordinates integration across knowledge bases
2. CrossModalMapper: Maps between different representational modalities
3. KnowledgeConflictResolver: Detects and resolves conflicts between knowledge sources
4. InferenceEngine: Generates new knowledge through inference across sources
5. ContextualRetrieval: Retrieves knowledge based on contextual relevance

Mathematical foundations:
- Bayesian belief integration for combining information from multiple sources
- Knowledge graph embedding techniques for unified representation
- Probabilistic graphical models for coherence enforcement
- Information theory for measuring knowledge conflicts and redundancy
- Transformer-based contextual encoding for relevance determination

Integration with:
- Core Neural Architecture for neuroplasticity in knowledge representations
- Diffusion-Based Reasoning for exploring the space of knowledge combinations
- Meta-Cognitive System for monitoring integration quality and coherence
- Hyper-Dimensional Transformer for context-aware representation mapping
"""

import numpy as np
import networkx as nx
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import json
import uuid
import time
from datetime import datetime
import logging
import re
import scipy
from scipy.spatial import distance
from scipy.special import kl_div
from sklearn.metrics.pairwise import cosine_similarity
from typing import Dict, List, Tuple, Set, Any, Optional, Union, Callable, TypeVar, Generic, Iterable
from collections import defaultdict, Counter, deque
from enum import Enum
import heapq
from copy import deepcopy
import concurrent.futures
from dataclasses import dataclass, field

logger = logging.getLogger(__name__)

# Check if CUDA is available for GPU acceleration
DEVICE = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

# Import knowledge types from other modules
try:
    from .episodic_knowledge import EpisodicMemory, EpisodicEvent, TemporalContext
    from .semantic_knowledge import SemanticNetwork, ConceptNode, RelationEdge
    from .procedural_knowledge import ProceduralKnowledgeBase, Action, TaskSchema
except ImportError:
    logger.warning("Unable to import knowledge modules. Using placeholder types.")
    # Define placeholder types if actual modules are not available yet
    EpisodicMemory = Any
    EpisodicEvent = Any
    TemporalContext = Any
    SemanticNetwork = Any
    ConceptNode = Any
    RelationEdge = Any
    ProceduralKnowledgeBase = Any
    Action = Any
    TaskSchema = Any


class KnowledgeType(Enum):
    """Types of knowledge in the system."""
    EPISODIC = "episodic"
    SEMANTIC = "semantic"
    PROCEDURAL = "procedural"
    INTEGRATED = "integrated"


class ModalityType(Enum):
    """Representational modalities for knowledge."""
    SYMBOLIC = "symbolic"          # Explicit symbolic representations (e.g., rules, graphs)
    NEURAL = "neural"              # Neural network embeddings
    VISUAL = "visual"              # Visual representations (images, visual features)
    TEXTUAL = "textual"            # Text-based representations
    SPATIOTEMPORAL = "spatiotemporal"  # Spatial and temporal representations
    HYBRID = "hybrid"              # Combinations of multiple modalities


class ConflictType(Enum):
    """Types of knowledge conflicts."""
    FACTUAL_CONTRADICTION = "factual_contradiction"  # Contradictory facts
    TEMPORAL_INCONSISTENCY = "temporal_inconsistency"  # Inconsistent temporal ordering
    CAUSAL_CONTRADICTION = "causal_contradiction"  # Contradictory causal relationships
    VALUE_MISMATCH = "value_mismatch"  # Different values for the same attribute
    PROCEDURAL_CONFLICT = "procedural_conflict"  # Contradictory procedural steps
    UNCERTAINTY_DISAGREEMENT = "uncertainty_disagreement"  # Different uncertainty levels
    PRIORITY_CONFLICT = "priority_conflict"  # Conflicting priorities


class ResolutionStrategy(Enum):
    """Strategies for resolving knowledge conflicts."""
    RECENCY = "recency"            # Favor more recent knowledge
    CONFIDENCE = "confidence"      # Favor higher confidence knowledge
    SOURCE_RELIABILITY = "source_reliability"  # Favor more reliable sources
    MAJORITY_VOTE = "majority_vote"  # Use majority consensus
    INTEGRATE_ALL = "integrate_all"  # Maintain all perspectives with weights
    HIERARCHICAL = "hierarchical"  # Apply domain-specific hierarchical rules
    DEFER = "defer"                # Defer resolution for human intervention


class RepresentationSpace:
    """
    A mathematical space for representing knowledge entities where
    distances represent semantic or conceptual similarity.
    """
    
    def __init__(
        self,
        name: str,
        dimensions: int,
        modality: ModalityType,
        distance_metric: str = "cosine"
    ):
        """
        Initialize a representation space.
        
        Args:
            name: Space name
            dimensions: Dimensionality of the space
            modality: Type of modality this space represents
            distance_metric: Distance metric to use ("cosine", "euclidean", etc.)
        """
        self.name = name
        self.dimensions = dimensions
        self.modality = modality
        self.distance_metric = distance_metric
        
        # Storage for entity embeddings
        self.embeddings = {}  # entity_id -> embedding vector
        
        # Index for fast nearest neighbor search
        self.use_approximate_search = dimensions > 100
        self.index = None
        
        # Mapping from ids to metadata
        self.metadata = {}  # entity_id -> metadata dict
        
        logger.debug(f"Created RepresentationSpace: {name}, {dimensions}D, {modality.value}")
    
    def add_entity(
        self,
        entity_id: str,
        embedding: np.ndarray,
        metadata: Dict[str, Any] = None
    ) -> None:
        """
        Add an entity to the representation space.
        
        Args:
            entity_id: Unique entity identifier
            embedding: Vector embedding of the entity
            metadata: Additional entity metadata
        """
        if embedding.shape[0] != self.dimensions:
            raise ValueError(f"Embedding dimension {embedding.shape[0]} does not match space dimension {self.dimensions}")
        
        self.embeddings[entity_id] = embedding
        self.metadata[entity_id] = metadata or {}
        
        # Reset index when adding new entities
        self.index = None
    
    def remove_entity(self, entity_id: str) -> bool:
        """
        Remove an entity from the representation space.
        
        Args:
            entity_id: Entity to remove
            
        Returns:
            True if entity was removed, False if not found
        """
        if entity_id in self.embeddings:
            del self.embeddings[entity_id]
            
            if entity_id in self.metadata:
                del self.metadata[entity_id]
            
            # Reset index when removing entities
            self.index = None
            return True
        
        return False
    
    def get_embedding(self, entity_id: str) -> Optional[np.ndarray]:
        """
        Get the embedding for an entity.
        
        Args:
            entity_id: Entity identifier
            
        Returns:
            Embedding vector or None if not found
        """
        return self.embeddings.get(entity_id)
    
    def update_embedding(
        self,
        entity_id: str,
        embedding: np.ndarray,
        metadata: Dict[str, Any] = None
    ) -> bool:
        """
        Update an entity's embedding and optional metadata.
        
        Args:
            entity_id: Entity identifier
            embedding: New embedding vector
            metadata: New metadata (None to keep existing)
            
        Returns:
            True if entity was updated, False if not found
        """
        if entity_id not in self.embeddings:
            return False
        
        if embedding.shape[0] != self.dimensions:
            raise ValueError(f"Embedding dimension {embedding.shape[0]} does not match space dimension {self.dimensions}")
        
        self.embeddings[entity_id] = embedding
        
        if metadata is not None:
            self.metadata[entity_id] = metadata
        
        # Reset index when updating entities
        self.index = None
        return True
    
    def similarity(self, entity_id1: str, entity_id2: str) -> float:
        """
        Calculate similarity between two entities.
        
        Args:
            entity_id1: First entity
            entity_id2: Second entity
            
        Returns:
            Similarity score (higher means more similar)
        """
        embedding1 = self.get_embedding(entity_id1)
        embedding2 = self.get_embedding(entity_id2)
        
        if embedding1 is None or embedding2 is None:
            return 0.0
        
        return self._calculate_similarity(embedding1, embedding2)
    
    def _calculate_similarity(self, vec1: np.ndarray, vec2: np.ndarray) -> float:
        """
        Calculate similarity between two vectors.
        
        Args:
            vec1: First vector
            vec2: Second vector
            
        Returns:
            Similarity score (higher means more similar)
        """
        if self.distance_metric == "cosine":
            # Cosine similarity: 1 - cosine_distance
            return 1.0 - distance.cosine(vec1, vec2)
        elif self.distance_metric == "euclidean":
            # Convert Euclidean distance to similarity
            # Using exponential decay: e^(-distance)
            return np.exp(-distance.euclidean(vec1, vec2))
        elif self.distance_metric == "dot":
            # Simple dot product
            return np.dot(vec1, vec2)
        else:
            # Default to cosine
            return 1.0 - distance.cosine(vec1, vec2)
    
    def nearest_neighbors(
        self,
        query: Union[str, np.ndarray],
        k: int = 5,
        threshold: float = None
    ) -> List[Tuple[str, float]]:
        """
        Find nearest neighbors to a query entity or vector.
        
        Args:
            query: Entity ID or embedding vector
            k: Number of neighbors to return
            threshold: Minimum similarity threshold
            
        Returns:
            List of (entity_id, similarity) tuples
        """
        # Convert query to embedding if it's an entity ID
        if isinstance(query, str):
            query_embedding = self.get_embedding(query)
            if query_embedding is None:
                return []
        else:
            query_embedding = query
            
        if self.use_approximate_search and len(self.embeddings) > 1000:
            return self._approximate_nearest_neighbors(query_embedding, k, threshold)
        else:
            return self._exact_nearest_neighbors(query_embedding, k, threshold)
    
    def _exact_nearest_neighbors(
        self,
        query_embedding: np.ndarray,
        k: int,
        threshold: float = None
    ) -> List[Tuple[str, float]]:
        """
        Find exact nearest neighbors using brute force search.
        
        Args:
            query_embedding: Query vector
            k: Number of neighbors
            threshold: Minimum similarity threshold
            
        Returns:
            List of (entity_id, similarity) tuples
        """
        similarities = []
        
        for entity_id, embedding in self.embeddings.items():
            similarity = self._calculate_similarity(query_embedding, embedding)
            if threshold is None or similarity >= threshold:
                similarities.append((entity_id, similarity))
        
        # Sort by similarity (descending)
        similarities.sort(key=lambda x: x[1], reverse=True)
        
        # Return top k
        return similarities[:k]
    
    def _approximate_nearest_neighbors(
        self,
        query_embedding: np.ndarray,
        k: int,
        threshold: float = None
    ) -> List[Tuple[str, float]]:
        """
        Find approximate nearest neighbors using an index.
        
        Args:
            query_embedding: Query vector
            k: Number of neighbors
            threshold: Minimum similarity threshold
            
        Returns:
            List of (entity_id, similarity) tuples
        """
        # Lazy-initialize the index
        if self.index is None:
            self._build_index()
        
        try:
            # Use scikit-learn's NearestNeighbors for approximation
            from sklearn.neighbors import NearestNeighbors
            
            # Convert embeddings to matrix
            embedding_matrix = np.array(list(self.embeddings.values()))
            entity_ids = list(self.embeddings.keys())
            
            # Use appropriate metric
            metric = 'cosine' if self.distance_metric == 'cosine' else 'euclidean'
            
            # Create and query the index
            nn_index = NearestNeighbors(n_neighbors=min(k, len(entity_ids)), metric=metric, algorithm='auto')
            nn_index.fit(embedding_matrix)
            
            distances, indices = nn_index.kneighbors([query_embedding], min(k, len(entity_ids)))
            
            # Convert to similarities and filter by threshold
            results = []
            for i, idx in enumerate(indices[0]):
                # Convert distance to similarity
                if metric == 'cosine':
                    similarity = 1.0 - distances[0][i]
                else:  # euclidean
                    similarity = np.exp(-distances[0][i])
                
                if threshold is None or similarity >= threshold:
                    results.append((entity_ids[idx], similarity))
            
            return results
            
        except ImportError:
            logger.warning("scikit-learn not available, falling back to exact search")
            return self._exact_nearest_neighbors(query_embedding, k, threshold)
    
    def _build_index(self) -> None:
        """Build the nearest neighbor search index."""
        # This is a placeholder for a more sophisticated index
        # In a production system, you might use FAISS, Annoy, or similar libraries
        self.index = True  # Just mark that we've initialized the index
    
    def centroid(self, entity_ids: List[str]) -> np.ndarray:
        """
        Calculate the centroid of multiple entities.
        
        Args:
            entity_ids: List of entity IDs
            
        Returns:
            Centroid vector
        """
        embeddings = [self.get_embedding(eid) for eid in entity_ids if eid in self.embeddings]
        
        if not embeddings:
            raise ValueError("No valid embeddings found for the provided entity IDs")
        
        return np.mean(embeddings, axis=0)
    
    def interpolate(
        self,
        entity_id1: str,
        entity_id2: str,
        alpha: float = 0.5
    ) -> np.ndarray:
        """
        Interpolate between two entities.
        
        Args:
            entity_id1: First entity
            entity_id2: Second entity
            alpha: Interpolation parameter (0 = entity1, 1 = entity2)
            
        Returns:
            Interpolated vector
        """
        embedding1 = self.get_embedding(entity_id1)
        embedding2 = self.get_embedding(entity_id2)
        
        if embedding1 is None or embedding2 is None:
            raise ValueError("Both entities must exist in the space")
        
        return (1 - alpha) * embedding1 + alpha * embedding2
    
    def extrapolate(
        self,
        from_entity_id: str,
        to_entity_id: str,
        alpha: float = 2.0
    ) -> np.ndarray:
        """
        Extrapolate beyond two entities (continue the direction).
        
        Args:
            from_entity_id: Starting entity
            to_entity_id: Direction entity
            alpha: Extrapolation parameter (1 = to_entity, >1 = beyond)
            
        Returns:
            Extrapolated vector
        """
        embedding1 = self.get_embedding(from_entity_id)
        embedding2 = self.get_embedding(to_entity_id)
        
        if embedding1 is None or embedding2 is None:
            raise ValueError("Both entities must exist in the space")
        
        # Calculate direction vector
        direction = embedding2 - embedding1
        
        # Extrapolate
        return embedding1 + alpha * direction
    
    def analogy(
        self,
        a_id: str,
        b_id: str,
        c_id: str
    ) -> np.ndarray:
        """
        Solve analogies of the form "a is to b as c is to ?"
        
        Args:
            a_id: First entity in the analogy
            b_id: Second entity in the analogy
            c_id: Third entity in the analogy
            
        Returns:
            Vector representing the solution to the analogy
        """
        a_embedding = self.get_embedding(a_id)
        b_embedding = self.get_embedding(b_id)
        c_embedding = self.get_embedding(c_id)
        
        if a_embedding is None or b_embedding is None or c_embedding is None:
            raise ValueError("All entities must exist in the space")
        
        # a:b::c:d => d = c + (b - a)
        return c_embedding + (b_embedding - a_embedding)
    
    def export_to_tensor(self) -> Tuple[torch.Tensor, List[str]]:
        """
        Export all embeddings as a tensor with their IDs.
        
        Returns:
            Tuple of (embeddings_tensor, entity_ids)
        """
        entity_ids = list(self.embeddings.keys())
        embeddings = [self.embeddings[eid] for eid in entity_ids]
        
        return torch.tensor(embeddings, dtype=torch.float32), entity_ids
    
    def import_from_tensor(
        self,
        embeddings_tensor: torch.Tensor,
        entity_ids: List[str],
        metadata_list: List[Dict[str, Any]] = None
    ) -> None:
        """
        Import embeddings from a tensor with their IDs.
        
        Args:
            embeddings_tensor: Tensor of embeddings
            entity_ids: List of entity IDs
            metadata_list: Optional list of metadata dicts
        """
        if len(entity_ids) != embeddings_tensor.shape[0]:
            raise ValueError("Number of entity IDs must match number of embeddings")
        
        if embeddings_tensor.shape[1] != self.dimensions:
            raise ValueError(f"Embedding dimension {embeddings_tensor.shape[1]} does not match space dimension {self.dimensions}")
        
        for i, entity_id in enumerate(entity_ids):
            embedding = embeddings_tensor[i].cpu().numpy()
            metadata = metadata_list[i] if metadata_list and i < len(metadata_list) else None
            self.add_entity(entity_id, embedding, metadata)
    
    def pca_projection(self, dimensions: int = 2) -> Tuple[np.ndarray, List[str]]:
        """
        Project the space to lower dimensions using PCA.
        
        Args:
            dimensions: Target dimensionality
            
        Returns:
            Tuple of (projected_embeddings, entity_ids)
        """
        if len(self.embeddings) < 2:
            raise ValueError("Need at least 2 embeddings to perform PCA")
        
        entity_ids = list(self.embeddings.keys())
        embeddings = np.array([self.embeddings[eid] for eid in entity_ids])
        
        # Perform PCA
        from sklearn.decomposition import PCA
        pca = PCA(n_components=dimensions)
        projected = pca.fit_transform(embeddings)
        
        return projected, entity_ids
    
    def cluster(self, n_clusters: int = 5) -> Dict[int, List[str]]:
        """
        Cluster entities in the space.
        
        Args:
            n_clusters: Number of clusters
            
        Returns:
            Dictionary mapping cluster IDs to lists of entity IDs
        """
        if len(self.embeddings) < n_clusters:
            raise ValueError(f"Cannot create {n_clusters} clusters from {len(self.embeddings)} entities")
        
        entity_ids = list(self.embeddings.keys())
        embeddings = np.array([self.embeddings[eid] for eid in entity_ids])
        
        # Perform clustering
        from sklearn.cluster import KMeans
        kmeans = KMeans(n_clusters=n_clusters, random_state=42)
        clusters = kmeans.fit_predict(embeddings)
        
        # Group entities by cluster
        cluster_map = defaultdict(list)
        for i, cluster_id in enumerate(clusters):
            cluster_map[int(cluster_id)].append(entity_ids[i])
        
        return dict(cluster_map)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "name": self.name,
            "dimensions": self.dimensions,
            "modality": self.modality.value,
            "distance_metric": self.distance_metric,
            "entity_count": len(self.embeddings)
        }
    
    def save(self, file_path: str) -> None:
        """
        Save the representation space to a file.
        
        Args:
            file_path: Path to save file
        """
        data = {
            "name": self.name,
            "dimensions": self.dimensions,
            "modality": self.modality.value,
            "distance_metric": self.distance_metric,
            "embeddings": {eid: emb.tolist() for eid, emb in self.embeddings.items()},
            "metadata": self.metadata
        }
        
        with open(file_path, 'w') as f:
            json.dump(data, f)
    
    @classmethod
    def load(cls, file_path: str) -> 'RepresentationSpace':
        """
        Load a representation space from a file.
        
        Args:
            file_path: Path to load file
            
        Returns:
            Loaded RepresentationSpace
        """
        with open(file_path, 'r') as f:
            data = json.load(f)
        
        space = cls(
            name=data["name"],
            dimensions=data["dimensions"],
            modality=ModalityType(data["modality"]),
            distance_metric=data["distance_metric"]
        )
        
        for eid, emb_list in data["embeddings"].items():
            embedding = np.array(emb_list)
            metadata = data["metadata"].get(eid, {})
            space.add_entity(eid, embedding, metadata)
        
        return space


class EmbeddingModel(nn.Module):
    """
    Neural network for embedding various types of knowledge.
    """
    
    def __init__(
        self,
        input_dim: int,
        hidden_dims: List[int],
        output_dim: int,
        dropout: float = 0.1
    ):
        """
        Initialize the embedding model.
        
        Args:
            input_dim: Input dimension
            hidden_dims: List of hidden layer dimensions
            output_dim: Output dimension
            dropout: Dropout probability
        """
        super().__init__()
        
        # Build layers
        layers = []
        prev_dim = input_dim
        
        for hidden_dim in hidden_dims:
            layers.append(nn.Linear(prev_dim, hidden_dim))
            layers.append(nn.ReLU())
            layers.append(nn.Dropout(dropout))
            prev_dim = hidden_dim
        
        # Output layer
        layers.append(nn.Linear(prev_dim, output_dim))
        
        self.network = nn.Sequential(*layers)
        self.output_dim = output_dim
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Forward pass of the model.
        
        Args:
            x: Input tensor
            
        Returns:
            Embedding tensor
        """
        return self.network(x)
    
    def embed(self, x: torch.Tensor) -> torch.Tensor:
        """
        Embed input data.
        
        Args:
            x: Input tensor
            
        Returns:
            Normalized embedding tensor
        """
        # Get embeddings
        embeddings = self.forward(x)
        
        # Normalize embeddings
        return F.normalize(embeddings, p=2, dim=1)


class MultiModalEmbedding(nn.Module):
    """
    Neural network for embedding data from multiple modalities.
    """
    
    def __init__(
        self,
        modality_dims: Dict[str, int],
        shared_dim: int,
        output_dim: int,
        hidden_dims: List[int] = [256, 128],
        dropout: float = 0.1
    ):
        """
        Initialize the multi-modal embedding model.
        
        Args:
            modality_dims: Dict mapping modality names to input dimensions
            shared_dim: Dimension of shared representation space
            output_dim: Final output dimension
            hidden_dims: List of hidden layer dimensions for shared network
            dropout: Dropout probability
        """
        super().__init__()
        
        # Create encoders for each modality
        self.encoders = nn.ModuleDict()
        for modality, dim in modality_dims.items():
            self.encoders[modality] = nn.Sequential(
                nn.Linear(dim, shared_dim),
                nn.ReLU(),
                nn.Dropout(dropout)
            )
        
        # Shared network
        layers = []
        prev_dim = shared_dim
        
        for hidden_dim in hidden_dims:
            layers.append(nn.Linear(prev_dim, hidden_dim))
            layers.append(nn.ReLU())
            layers.append(nn.Dropout(dropout))
            prev_dim = hidden_dim
        
        # Output layer
        layers.append(nn.Linear(prev_dim, output_dim))
        
        self.shared_network = nn.Sequential(*layers)
        self.output_dim = output_dim
        self.modality_dims = modality_dims
    
    def forward(
        self,
        inputs: Dict[str, torch.Tensor]
    ) -> Tuple[torch.Tensor, Dict[str, torch.Tensor]]:
        """
        Forward pass of the model.
        
        Args:
            inputs: Dict mapping modality names to input tensors
            
        Returns:
            Tuple of (combined_embedding, modality_embeddings)
        """
        # Encode each modality
        modality_embeddings = {}
        for modality, encoder in self.encoders.items():
            if modality in inputs:
                modality_embeddings[modality] = encoder(inputs[modality])
        
        # Average the embeddings from available modalities
        if not modality_embeddings:
            raise ValueError("No valid modalities provided")
        
        combined = torch.stack(list(modality_embeddings.values())).mean(dim=0)
        
        # Pass through shared network
        output = self.shared_network(combined)
        
        return output, modality_embeddings
    
    def embed(
        self,
        inputs: Dict[str, torch.Tensor]
    ) -> Tuple[torch.Tensor, Dict[str, torch.Tensor]]:
        """
        Embed multi-modal input data.
        
        Args:
            inputs: Dict mapping modality names to input tensors
            
        Returns:
            Tuple of (normalized_combined_embedding, modality_embeddings)
        """
        # Get embeddings
        combined, modality_embeddings = self.forward(inputs)
        
        # Normalize combined embedding
        normalized = F.normalize(combined, p=2, dim=1)
        
        return normalized, modality_embeddings


@dataclass
class KnowledgeSource:
    """Information about a knowledge source."""
    source_id: str
    knowledge_type: KnowledgeType
    reliability: float = 1.0
    recency: float = 1.0
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class KnowledgeConflict:
    """Representation of a conflict between knowledge sources."""
    conflict_id: str
    conflict_type: ConflictType
    sources: List[KnowledgeSource]
    description: str
    severity: float  # 0-1 scale
    resolution_strategies: List[ResolutionStrategy]
    resolved: bool = False
    resolution: Optional[Dict[str, Any]] = None
    creation_time: datetime = field(default_factory=datetime.now)


class KnowledgeConflictResolver:
    """
    Detects and resolves conflicts between different knowledge sources.
    """
    
    def __init__(
        self,
        default_strategies: List[ResolutionStrategy] = None,
        confidence_threshold: float = 0.7,
        max_conflicts_stored: int = 1000
    ):
        """
        Initialize the conflict resolver.
        
        Args:
            default_strategies: Default conflict resolution strategies in priority order
            confidence_threshold: Threshold for confidence-based resolution
            max_conflicts_stored: Maximum number of conflicts to store in history
        """
        self.default_strategies = default_strategies or [
            ResolutionStrategy.CONFIDENCE,
            ResolutionStrategy.RECENCY,
            ResolutionStrategy.SOURCE_RELIABILITY,
            ResolutionStrategy.MAJORITY_VOTE,
            ResolutionStrategy.INTEGRATE_ALL
        ]
        self.confidence_threshold = confidence_threshold
        
        # Conflict history
        self.conflicts = {}  # conflict_id -> KnowledgeConflict
        self.conflict_history = deque(maxlen=max_conflicts_stored)
        
        # Custom resolution functions for different conflict types
        self.resolution_functions = {
            ConflictType.FACTUAL_CONTRADICTION: self._resolve_factual_contradiction,
            ConflictType.TEMPORAL_INCONSISTENCY: self._resolve_temporal_inconsistency,
            ConflictType.CAUSAL_CONTRADICTION: self._resolve_causal_contradiction,
            ConflictType.VALUE_MISMATCH: self._resolve_value_mismatch,
            ConflictType.PROCEDURAL_CONFLICT: self._resolve_procedural_conflict,
            ConflictType.UNCERTAINTY_DISAGREEMENT: self._resolve_uncertainty_disagreement,
            ConflictType.PRIORITY_CONFLICT: self._resolve_priority_conflict
        }
        
        # Domain-specific resolution rules
        self.domain_rules = {}
        
        logger.debug("Initialized KnowledgeConflictResolver")
    
    def detect_conflicts(
        self,
        knowledge_items: List[Dict[str, Any]],
        sources: List[KnowledgeSource],
        context: Dict[str, Any] = None
    ) -> List[KnowledgeConflict]:
        """
        Detect conflicts between knowledge items.
        
        Args:
            knowledge_items: List of knowledge items to check
            sources: Sources of the knowledge items
            context: Additional context for conflict detection
            
        Returns:
            List of detected conflicts
        """
        if len(knowledge_items) != len(sources):
            raise ValueError("Number of knowledge items must match number of sources")
        
        context = context or {}
        detected_conflicts = []
        
        # Check all pairs of knowledge items
        for i in range(len(knowledge_items)):
            for j in range(i+1, len(knowledge_items)):
                item1 = knowledge_items[i]
                item2 = knowledge_items[j]
                source1 = sources[i]
                source2 = sources[j]
                
                # Detect conflicts based on knowledge types
                type1 = source1.knowledge_type
                type2 = source2.knowledge_type
                
                conflicts = []
                
                if type1 == type2 == KnowledgeType.SEMANTIC:
                    conflicts.extend(self._detect_semantic_conflicts(item1, item2, source1, source2, context))
                
                elif type1 == type2 == KnowledgeType.EPISODIC:
                    conflicts.extend(self._detect_episodic_conflicts(item1, item2, source1, source2, context))
                
                elif type1 == type2 == KnowledgeType.PROCEDURAL:
                    conflicts.extend(self._detect_procedural_conflicts(item1, item2, source1, source2, context))
                
                else:
                    # Cross-type conflicts
                    conflicts.extend(self._detect_cross_type_conflicts(
                        item1, item2, source1, source2, context))
                
                # Add detected conflicts
                for conflict in conflicts:
                    # Save conflict
                    self.conflicts[conflict.conflict_id] = conflict
                    detected_conflicts.append(conflict)
        
        return detected_conflicts
    
    def _detect_semantic_conflicts(
        self,
        item1: Dict[str, Any],
        item2: Dict[str, Any],
        source1: KnowledgeSource,
        source2: KnowledgeSource,
        context: Dict[str, Any]
    ) -> List[KnowledgeConflict]:
        """
        Detect conflicts between semantic knowledge items.
        
        Args:
            item1: First knowledge item
            item2: Second knowledge item
            source1: Source of first item
            source2: Source of second item
            context: Additional context
            
        Returns:
            List of detected conflicts
        """
        conflicts = []
        
        # Check for factual contradictions
        if 'facts' in item1 and 'facts' in item2:
            facts1 = item1['facts']
            facts2 = item2['facts']
            
            for key in set(facts1.keys()) & set(facts2.keys()):
                if facts1[key] != facts2[key]:
                    conflict = KnowledgeConflict(
                        conflict_id=f"conflict_{str(uuid.uuid4())[:8]}",
                        conflict_type=ConflictType.FACTUAL_CONTRADICTION,
                        sources=[source1, source2],
                        description=f"Factual contradiction for attribute '{key}': {facts1[key]} vs {facts2[key]}",
                        severity=self._calculate_conflict_severity(facts1[key], facts2[key]),
                        resolution_strategies=self.default_strategies.copy()
                    )
                    conflicts.append(conflict)
        
        # Check for causal contradictions
        if 'causal_relations' in item1 and 'causal_relations' in item2:
            causal1 = item1['causal_relations']
            causal2 = item2['causal_relations']
            
            for relation in causal1:
                if relation['cause'] in [r['cause'] for r in causal2]:
                    matching = [r for r in causal2 if r['cause'] == relation['cause']]
                    for match in matching:
                        if relation['effect'] != match['effect']:
                            conflict = KnowledgeConflict(
                                conflict_id=f"conflict_{str(uuid.uuid4())[:8]}",
                                conflict_type=ConflictType.CAUSAL_CONTRADICTION,
                                sources=[source1, source2],
                                description=f"Causal contradiction: {relation['cause']} leads to {relation['effect']} vs {match['effect']}",
                                severity=0.8,  # Causal contradictions are severe
                                resolution_strategies=self.default_strategies.copy()
                            )
                            conflicts.append(conflict)
        
        return conflicts
    
    def _detect_episodic_conflicts(
        self,
        item1: Dict[str, Any],
        item2: Dict[str, Any],
        source1: KnowledgeSource,
        source2: KnowledgeSource,
        context: Dict[str, Any]
    ) -> List[KnowledgeConflict]:
        """
        Detect conflicts between episodic knowledge items.
        
        Args:
            item1: First knowledge item
            item2: Second knowledge item
            source1: Source of first item
            source2: Source of second item
            context: Additional context
            
        Returns:
            List of detected conflicts
        """
        conflicts = []
        
        # Check for temporal inconsistencies
        if 'timestamp' in item1 and 'timestamp' in item2 and 'event_id' in item1 and 'event_id' in item2:
            # Check if same event but different timestamps
            if item1['event_id'] == item2['event_id']:
                timestamp1 = item1['timestamp']
                timestamp2 = item2['timestamp']
                
                if isinstance(timestamp1, str):
                    timestamp1 = datetime.fromisoformat(timestamp1)
                if isinstance(timestamp2, str):
                    timestamp2 = datetime.fromisoformat(timestamp2)
                
                # Check for significant time difference
                time_diff = abs((timestamp1 - timestamp2).total_seconds())
                if time_diff > 60:  # More than 1 minute difference
                    conflict = KnowledgeConflict(
                        conflict_id=f"conflict_{str(uuid.uuid4())[:8]}",
                        conflict_type=ConflictType.TEMPORAL_INCONSISTENCY,
                        sources=[source1, source2],
                        description=f"Temporal inconsistency: Event {item1['event_id']} has different timestamps ({timestamp1} vs {timestamp2})",
                        severity=min(1.0, time_diff / (24 * 3600)),  # Scale by days, max at 1 day
                        resolution_strategies=self.default_strategies.copy()
                    )
                    conflicts.append(conflict)
        
        # Check for factual inconsistencies within episodic events
        if 'details' in item1 and 'details' in item2:
            details1 = item1['details']
            details2 = item2['details']
            
            if isinstance(details1, dict) and isinstance(details2, dict):
                for key in set(details1.keys()) & set(details2.keys()):
                    if details1[key] != details2[key]:
                        conflict = KnowledgeConflict(
                            conflict_id=f"conflict_{str(uuid.uuid4())[:8]}",
                            conflict_type=ConflictType.VALUE_MISMATCH,
                            sources=[source1, source2],
                            description=f"Detail inconsistency: '{key}' has different values ({details1[key]} vs {details2[key]})",
                            severity=self._calculate_conflict_severity(details1[key], details2[key]),
                            resolution_strategies=self.default_strategies.copy()
                        )
                        conflicts.append(conflict)
        
        return conflicts
    
    def _detect_procedural_conflicts(
        self,
        item1: Dict[str, Any],
        item2: Dict[str, Any],
        source1: KnowledgeSource,
        source2: KnowledgeSource,
        context: Dict[str, Any]
    ) -> List[KnowledgeConflict]:
        """
        Detect conflicts between procedural knowledge items.
        
        Args:
            item1: First knowledge item
            item2: Second knowledge item
            source1: Source of first item
            source2: Source of second item
            context: Additional context
            
        Returns:
            List of detected conflicts
        """
        conflicts = []
        
        # Check for procedural step conflicts
        if 'steps' in item1 and 'steps' in item2 and 'task_id' in item1 and 'task_id' in item2:
            # Check if same task but different steps
            if item1['task_id'] == item2['task_id']:
                steps1 = item1['steps']
                steps2 = item2['steps']
                
                # Check for different number of steps
                if len(steps1) != len(steps2):
                    conflict = KnowledgeConflict(
                        conflict_id=f"conflict_{str(uuid.uuid4())[:8]}",
                        conflict_type=ConflictType.PROCEDURAL_CONFLICT,
                        sources=[source1, source2],
                        description=f"Procedural conflict: Task {item1['task_id']} has different number of steps ({len(steps1)} vs {len(steps2)})",
                        severity=0.6,
                        resolution_strategies=self.default_strategies.copy()
                    )
                    conflicts.append(conflict)
                
                # Check for step order or content differences
                for i in range(min(len(steps1), len(steps2))):
                    if steps1[i] != steps2[i]:
                        conflict = KnowledgeConflict(
                            conflict_id=f"conflict_{str(uuid.uuid4())[:8]}",
                            conflict_type=ConflictType.PROCEDURAL_CONFLICT,
                            sources=[source1, source2],
                            description=f"Procedural conflict: Task {item1['task_id']} has different step {i+1} ({steps1[i]} vs {steps2[i]})",
                            severity=0.7,
                            resolution_strategies=self.default_strategies.copy()
                        )
                        conflicts.append(conflict)
        
        # Check for precondition conflicts
        if 'preconditions' in item1 and 'preconditions' in item2 and item1.get('action_id') == item2.get('action_id'):
            precond1 = set(item1['preconditions'])
            precond2 = set(item2['preconditions'])
            
            if precond1 != precond2:
                conflict = KnowledgeConflict(
                    conflict_id=f"conflict_{str(uuid.uuid4())[:8]}",
                    conflict_type=ConflictType.PROCEDURAL_CONFLICT,
                    sources=[source1, source2],
                    description=f"Precondition conflict: Action {item1.get('action_id')} has different preconditions",
                    severity=0.5,
                    resolution_strategies=self.default_strategies.copy()
                )
                conflicts.append(conflict)
        
        return conflicts
    
    def _detect_cross_type_conflicts(
        self,
        item1: Dict[str, Any],
        item2: Dict[str, Any],
        source1: KnowledgeSource,
        source2: KnowledgeSource,
        context: Dict[str, Any]
    ) -> List[KnowledgeConflict]:
        """
        Detect conflicts between different types of knowledge items.
        
        Args:
            item1: First knowledge item
            item2: Second knowledge item
            source1: Source of first item
            source2: Source of second item
            context: Additional context
            
        Returns:
            List of detected conflicts
        """
        conflicts = []
        
        # Semantic vs Episodic conflicts
        if (source1.knowledge_type == KnowledgeType.SEMANTIC and 
            source2.knowledge_type == KnowledgeType.EPISODIC):
            semantic_item = item1
            episodic_item = item2
            semantic_source = source1
            episodic_source = source2
        elif (source2.knowledge_type == KnowledgeType.SEMANTIC and 
              source1.knowledge_type == KnowledgeType.EPISODIC):
            semantic_item = item2
            episodic_item = item1
            semantic_source = source2
            episodic_source = source1
        else:
            semantic_item = None
            episodic_item = None
        
        # Check for semantic facts contradicted by episodic details
        if semantic_item and episodic_item and 'facts' in semantic_item and 'details' in episodic_item:
            facts = semantic_item['facts']
            details = episodic_item['details']
            
            if isinstance(facts, dict) and isinstance(details, dict):
                for key in set(facts.keys()) & set(details.keys()):
                    if facts[key] != details[key]:
                        conflict = KnowledgeConflict(
                            conflict_id=f"conflict_{str(uuid.uuid4())[:8]}",
                            conflict_type=ConflictType.FACTUAL_CONTRADICTION,
                            sources=[semantic_source, episodic_source],
                            description=f"Semantic-Episodic conflict: '{key}' has different values ({facts[key]} vs {details[key]})",
                            severity=self._calculate_conflict_severity(facts[key], details[key]),
                            resolution_strategies=self.default_strategies.copy()
                        )
                        conflicts.append(conflict)
        
        # Semantic vs Procedural conflicts
        if (source1.knowledge_type == KnowledgeType.SEMANTIC and 
            source2.knowledge_type == KnowledgeType.PROCEDURAL):
            semantic_item = item1
            procedural_item = item2
            semantic_source = source1
            procedural_source = source2
        elif (source2.knowledge_type == KnowledgeType.SEMANTIC and 
              source1.knowledge_type == KnowledgeType.PROCEDURAL):
            semantic_item = item2
            procedural_item = item1
            semantic_source = source2
            procedural_source = source1
        else:
            semantic_item = None
            procedural_item = None
        
        # Check for semantic facts contradicted by procedural knowledge
        if semantic_item and procedural_item and 'facts' in semantic_item and 'effects' in procedural_item:
            facts = semantic_item['facts']
            effects = procedural_item['effects']
            
            if isinstance(facts, dict) and isinstance(effects, dict):
                for key in set(facts.keys()) & set(effects.keys()):
                    if facts[key] != effects[key]:
                        conflict = KnowledgeConflict(
                            conflict_id=f"conflict_{str(uuid.uuid4())[:8]}",
                            conflict_type=ConflictType.FACTUAL_CONTRADICTION,
                            sources=[semantic_source, procedural_source],
                            description=f"Semantic-Procedural conflict: '{key}' has different values ({facts[key]} vs {effects[key]})",
                            severity=self._calculate_conflict_severity(facts[key], effects[key]),
                            resolution_strategies=self.default_strategies.copy()
                        )
                        conflicts.append(conflict)
        
        return conflicts
    
    def _calculate_conflict_severity(self, value1: Any, value2: Any) -> float:
        """
        Calculate the severity of a conflict between two values.
        
        Args:
            value1: First value
            value2: Second value
            
        Returns:
            Severity score (0-1)
        """
        # If types are different, it's a severe conflict
        if type(value1) != type(value2):
            return 0.9
        
        # For numeric values, calculate relative difference
        if isinstance(value1, (int, float)) and isinstance(value2, (int, float)):
            # Avoid division by zero
            avg = (abs(value1) + abs(value2)) / 2
            if avg == 0:
                return 0.5 if value1 != value2 else 0.0
            
            rel_diff = abs(value1 - value2) / avg
            return min(1.0, rel_diff)
        
        # For strings, use basic similarity
        if isinstance(value1, str) and isinstance(value2, str):
            # Calculate string similarity using Levenshtein distance
            try:
                import Levenshtein
                max_len = max(len(value1), len(value2))
                if max_len == 0:
                    return 0.0
                distance = Levenshtein.distance(value1, value2)
                return min(1.0, distance / max_len)
            except ImportError:
                # Fallback if Levenshtein library not available
                return 0.7 if value1 != value2 else 0.0
        
        # For boolean values
        if isinstance(value1, bool) and isinstance(value2, bool):
            return 1.0 if value1 != value2 else 0.0
        
        # For lists or sets, calculate Jaccard distance
        if isinstance(value1, (list, set)) and isinstance(value2, (list, set)):
            set1 = set(value1)
            set2 = set(value2)
            
            if not set1 and not set2:
                return 0.0
            
            intersection = len(set1.intersection(set2))
            union = len(set1.union(set2))
            
            return 1.0 - (intersection / union)
        
        # Default severity for other types
        return 0.7 if value1 != value2 else 0.0
    
    def resolve_conflict(
        self,
        conflict: KnowledgeConflict,
        context: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        Resolve a knowledge conflict.
        
        Args:
            conflict: The conflict to resolve
            context: Additional context for resolution
            
        Returns:
            Resolution information
        """
        context = context or {}
        
        # Check if conflict is already resolved
        if conflict.resolved and conflict.resolution:
            return conflict.resolution
        
        # Get resolution function for this conflict type
        resolve_func = self.resolution_functions.get(
            conflict.conflict_type, 
            self._resolve_generic_conflict
        )
        
        # Apply domain-specific rules if available
        domain = context.get('domain')
        if domain and domain in self.domain_rules:
            domain_func = self.domain_rules[domain].get(
                conflict.conflict_type,
                None
            )
            if domain_func:
                resolve_func = domain_func
        
        # Apply resolution function
        resolution = resolve_func(conflict, context)
        
        # Update conflict
        conflict.resolved = True
        conflict.resolution = resolution
        
        # Add to history
        self.conflict_history.append(conflict)
        
        return resolution
    
    def resolve_all_conflicts(
        self,
        conflicts: List[KnowledgeConflict],
        context: Dict[str, Any] = None
    ) -> Dict[str, Dict[str, Any]]:
        """
        Resolve multiple conflicts.
        
        Args:
            conflicts: List of conflicts to resolve
            context: Additional context for resolution
            
        Returns:
            Dictionary mapping conflict IDs to resolutions
        """
        resolutions = {}
        
        for conflict in conflicts:
            resolution = self.resolve_conflict(conflict, context)
            resolutions[conflict.conflict_id] = resolution
        
        return resolutions
    
    def _resolve_generic_conflict(
        self,
        conflict: KnowledgeConflict,
        context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Generic conflict resolution function.
        
        Args:
            conflict: The conflict to resolve
            context: Additional context
            
        Returns:
            Resolution information
        """
        resolution = {
            "conflict_id": conflict.conflict_id,
            "resolved_at": datetime.now().isoformat(),
            "resolution_type": None,
            "selected_value": None,
            "confidence": 0.0,
            "explanation": ""
        }
        
        # Try each resolution strategy in order
        for strategy in conflict.resolution_strategies:
            result = self._apply_strategy(strategy, conflict, context)
            if result["success"]:
                resolution.update({
                    "resolution_type": strategy.value,
                    "selected_value": result["value"],
                    "confidence": result["confidence"],
                    "explanation": result["explanation"],
                    "source_id": result.get("source_id")
                })
                return resolution
        
        # If no strategy succeeded, use DEFER
        resolution.update({
            "resolution_type": ResolutionStrategy.DEFER.value,
            "explanation": "Could not resolve with available strategies, human intervention needed",
            "confidence": 0.0
        })
        
        return resolution
    
    def _resolve_factual_contradiction(
        self,
        conflict: KnowledgeConflict,
        context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Resolve factual contradictions.
        
        Args:
            conflict: The conflict to resolve
            context: Additional context
            
        Returns:
            Resolution information
        """
        # Extract contradictory values from description
        description = conflict.description
        pattern = r"'([^']+)': ([^ ]+) vs ([^ )]+)"
        match = re.search(pattern, description)
        
        if not match:
            return self._resolve_generic_conflict(conflict, context)
        
        attribute, value1, value2 = match.groups()
        
        # Try to parse values to appropriate types
        try:
            # Try numeric
            value1 = float(value1)
            value2 = float(value2)
            # If they're whole numbers, convert to int
            if value1.is_integer():
                value1 = int(value1)
            if value2.is_integer():
                value2 = int(value2)
        except ValueError:
            # Try boolean
            if value1.lower() in ['true', 'false']:
                value1 = value1.lower() == 'true'
            if value2.lower() in ['true', 'false']:
                value2 = value2.lower() == 'true'
        
        # Default to generic resolution
        return self._resolve_generic_conflict(conflict, context)
    
    def _resolve_temporal_inconsistency(
        self,
        conflict: KnowledgeConflict,
        context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Resolve temporal inconsistencies.
        
        Args:
            conflict: The conflict to resolve
            context: Additional context
            
        Returns:
            Resolution information
        """
        # For temporal inconsistencies, recency is usually the best strategy
        strategies = [
            ResolutionStrategy.RECENCY,
            ResolutionStrategy.SOURCE_RELIABILITY,
            ResolutionStrategy.CONFIDENCE
        ]
        
        # Create new conflict with optimized strategies
        temp_conflict = KnowledgeConflict(
            conflict_id=conflict.conflict_id,
            conflict_type=conflict.conflict_type,
            sources=conflict.sources,
            description=conflict.description,
            severity=conflict.severity,
            resolution_strategies=strategies,
            creation_time=conflict.creation_time
        )
        
        return self._resolve_generic_conflict(temp_conflict, context)
    
    def _resolve_causal_contradiction(
        self,
        conflict: KnowledgeConflict,
        context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Resolve causal contradictions.
        
        Args:
            conflict: The conflict to resolve
            context: Additional context
            
        Returns:
            Resolution information
        """
        # For causal contradictions, reliability and context are important
        strategies = [
            ResolutionStrategy.SOURCE_RELIABILITY,
            ResolutionStrategy.CONFIDENCE,
            ResolutionStrategy.INTEGRATE_ALL
        ]
        
        # Create new conflict with optimized strategies
        temp_conflict = KnowledgeConflict(
            conflict_id=conflict.conflict_id,
            conflict_type=conflict.conflict_type,
            sources=conflict.sources,
            description=conflict.description,
            severity=conflict.severity,
            resolution_strategies=strategies,
            creation_time=conflict.creation_time
        )
        
        return self._resolve_generic_conflict(temp_conflict, context)
    
    def _resolve_value_mismatch(
        self,
        conflict: KnowledgeConflict,
        context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Resolve value mismatches.
        
        Args:
            conflict: The conflict to resolve
            context: Additional context
            
        Returns:
            Resolution information
        """
        # Similar to factual contradictions
        return self._resolve_factual_contradiction(conflict, context)
    
    def _resolve_procedural_conflict(
        self,
        conflict: KnowledgeConflict,
        context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Resolve procedural conflicts.
        
        Args:
            conflict: The conflict to resolve
            context: Additional context
            
        Returns:
            Resolution information
        """
        # For procedural conflicts, we may want to merge rather than select
        if ResolutionStrategy.INTEGRATE_ALL in conflict.resolution_strategies:
            # Move INTEGRATE_ALL to first position
            strategies = [ResolutionStrategy.INTEGRATE_ALL]
            for strategy in conflict.resolution_strategies:
                if strategy != ResolutionStrategy.INTEGRATE_ALL:
                    strategies.append(strategy)
            
            # Create new conflict with optimized strategies
            temp_conflict = KnowledgeConflict(
                conflict_id=conflict.conflict_id,
                conflict_type=conflict.conflict_type,
                sources=conflict.sources,
                description=conflict.description,
                severity=conflict.severity,
                resolution_strategies=strategies,
                creation_time=conflict.creation_time
            )
            
            return self._resolve_generic_conflict(temp_conflict, context)
        
        # Default to generic resolution
        return self._resolve_generic_conflict(conflict, context)
    
    def _resolve_uncertainty_disagreement(
        self,
        conflict: KnowledgeConflict,
        context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Resolve uncertainty disagreements.
        
        Args:
            conflict: The conflict to resolve
            context: Additional context
            
        Returns:
            Resolution information
        """
        # For uncertainty disagreements, take the more conservative (higher uncertainty) view
        resolution = {
            "conflict_id": conflict.conflict_id,
            "resolved_at": datetime.now().isoformat(),
            "resolution_type": "conservative_uncertainty",
            "confidence": 0.0,
            "explanation": "Adopted the higher uncertainty estimate for safety"
        }
        
        # Extract uncertainty values from sources if available
        uncertainties = []
        for source in conflict.sources:
            if 'uncertainty' in source.metadata:
                uncertainties.append((source.source_id, source.metadata['uncertainty']))
        
        if uncertainties:
            # Take the highest uncertainty
            source_id, max_uncertainty = max(uncertainties, key=lambda x: x[1])
            resolution.update({
                "selected_value": max_uncertainty,
                "source_id": source_id,
                "confidence": 0.8
            })
            return resolution
        
        # Default to generic resolution
        return self._resolve_generic_conflict(conflict, context)
    
    def _resolve_priority_conflict(
        self,
        conflict: KnowledgeConflict,
        context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Resolve priority conflicts.
        
        Args:
            conflict: The conflict to resolve
            context: Additional context
            
        Returns:
            Resolution information
        """
        # Use hierarchical resolution for priority conflicts
        strategies = [
            ResolutionStrategy.HIERARCHICAL,
            ResolutionStrategy.SOURCE_RELIABILITY,
            ResolutionStrategy.DEFER
        ]
        
        # Create new conflict with optimized strategies
        temp_conflict = KnowledgeConflict(
            conflict_id=conflict.conflict_id,
            conflict_type=conflict.conflict_type,
            sources=conflict.sources,
            description=conflict.description,
            severity=conflict.severity,
            resolution_strategies=strategies,
            creation_time=conflict.creation_time
        )
        
        return self._resolve_generic_conflict(temp_conflict, context)
    
    def _apply_strategy(
        self,
        strategy: ResolutionStrategy,
        conflict: KnowledgeConflict,
        context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Apply a specific resolution strategy.
        
        Args:
            strategy: Strategy to apply
            conflict: Conflict to resolve
            context: Additional context
            
        Returns:
            Dictionary with resolution results
        """
        result = {
            "success": False,
            "value": None,
            "confidence": 0.0,
            "explanation": "",
            "source_id": None
        }
        
        # Extract values and metadata from conflict sources
        sources = conflict.sources
        if not sources or len(sources) < 2:
            result["explanation"] = "Insufficient sources for resolution"
            return result
        
        values = []
        for i, source in enumerate(sources):
            # Extract value from conflict description
            value = self._extract_value_from_description(conflict.description, i)
            values.append((source, value))
        
        # Apply the chosen strategy
        if strategy == ResolutionStrategy.RECENCY:
            # Choose the most recent source
            sources_with_recency = [(s, s.recency) for s, _ in values]
            if any(r > 0 for _, r in sources_with_recency):
                most_recent = max(sources_with_recency, key=lambda x: x[1])
                source = most_recent[0]
                value = next(v for s, v in values if s.source_id == source.source_id)
                
                result.update({
                    "success": True,
                    "value": value,
                    "confidence": most_recent[1],  # Use recency as confidence
                    "explanation": f"Selected most recent value ({value}) with recency {most_recent[1]}",
                    "source_id": source.source_id
                })
                return result
        
        elif strategy == ResolutionStrategy.CONFIDENCE:
            # Choose the source with highest confidence
            confidences = []
            for source, value in values:
                confidence = source.metadata.get('confidence', 0.0)
                if confidence >= self.confidence_threshold:
                    confidences.append((source, value, confidence))
            
            if confidences:
                best = max(confidences, key=lambda x: x[2])
                result.update({
                    "success": True,
                    "value": best[1],
                    "confidence": best[2],
                    "explanation": f"Selected highest confidence value ({best[1]}) with confidence {best[2]}",
                    "source_id": best[0].source_id
                })
                return result
        
        elif strategy == ResolutionStrategy.SOURCE_RELIABILITY:
            # Choose the most reliable source
            reliabilities = [(s, v, s.reliability) for s, v in values]
            if any(r > 0 for _, _, r in reliabilities):
                most_reliable = max(reliabilities, key=lambda x: x[2])
                result.update({
                    "success": True,
                    "value": most_reliable[1],
                    "confidence": most_reliable[2],  # Use reliability as confidence
                    "explanation": f"Selected most reliable source value ({most_reliable[1]}) with reliability {most_reliable[2]}",
                    "source_id": most_reliable[0].source_id
                })
                return result
        
        elif strategy == ResolutionStrategy.MAJORITY_VOTE:
            # Choose the value with most support
            value_counts = {}
            for _, value in values:
                # Convert value to hashable type
                hashable_value = str(value)
                value_counts[hashable_value] = value_counts.get(hashable_value, 0) + 1
            
            if value_counts:
                # Find majority value
                majority_value_str, count = max(value_counts.items(), key=lambda x: x[1])
                
                # Convert back to original type if possible
                for _, orig_value in values:
                    if str(orig_value) == majority_value_str:
                        majority_value = orig_value
                        break
                else:
                    majority_value = majority_value_str
                
                # Calculate confidence based on consensus
                confidence = count / len(values)
                
                result.update({
                    "success": True,
                    "value": majority_value,
                    "confidence": confidence,
                    "explanation": f"Selected majority value ({majority_value}) with {count}/{len(values)} sources",
                })
                return result
        
        elif strategy == ResolutionStrategy.INTEGRATE_ALL:
            # Integrate all values
            if all(v is not None for _, v in values):
                integrated_value = self._integrate_values([v for _, v in values])
                
                result.update({
                    "success": True,
                    "value": integrated_value,
                    "confidence": 0.7,  # Moderate confidence for integration
                    "explanation": f"Integrated all values into {integrated_value}",
                    "all_sources": True
                })
                return result
        
        elif strategy == ResolutionStrategy.HIERARCHICAL:
            # Apply domain-specific hierarchical rules
            domain = context.get('domain')
            if domain and domain in self.domain_rules:
                # Try to apply domain-specific rules
                hierarchy_func = self.domain_rules[domain].get('hierarchy', None)
                if hierarchy_func:
                    hierarchy_result = hierarchy_func(conflict, values, context)
                    if hierarchy_result.get('success', False):
                        result.update(hierarchy_result)
                        return result
        
        elif strategy == ResolutionStrategy.DEFER:
            # Always succeeds but indicates human intervention needed
            result.update({
                "success": True,
                "value": None,
                "confidence": 0.0,
                "explanation": "Deferred for human resolution",
                "deferred": True
            })
            return result
        
        # Strategy was not successful
        result["explanation"] = f"Strategy {strategy.value} could not be applied"
        return result
    
    def _extract_value_from_description(
        self,
        description: str,
        index: int
    ) -> Optional[Any]:
        """
        Extract a value from the conflict description.
        
        Args:
            description: Conflict description
            index: Index of the value to extract (0 for first, 1 for second)
            
        Returns:
            Extracted value or None if not found
        """
        # Try to extract values from typical patterns
        patterns = [
            r"'[^']+': ([^ ]+) vs ([^ )]+)",  # 'key': value1 vs value2
            r"(\S+) vs (\S+)",                # value1 vs value2
            r"(\S+) leads to (\S+)",          # cause leads to effect1 vs effect2
        ]
        
        for pattern in patterns:
            match = re.search(pattern, description)
            if match and len(match.groups()) >= 2:
                try:
                    value_str = match.group(index + 1)
                    
                    # Try to parse the value
                    # First as number
                    try:
                        value = float(value_str)
                        # Convert to int if it's a whole number
                        if value.is_integer():
                            value = int(value)
                        return value
                    except ValueError:
                        pass
                    
                    # Then as boolean
                    if value_str.lower() in ['true', 'false']:
                        return value_str.lower() == 'true'
                    
                    # Otherwise keep as string
                    return value_str
                except (IndexError, ValueError):
                    pass
        
        return None
    
    def _integrate_values(self, values: List[Any]) -> Any:
        """
        Integrate multiple values into a single value.
        
        Args:
            values: List of values to integrate
            
        Returns:
            Integrated value
        """
        if not values:
            return None
        
        # If all values are of the same type, use type-specific integration
        value_types = [type(v) for v in values]
        if all(t == value_types[0] for t in value_types):
            value_type = value_types[0]
            
            # Numeric values: use weighted average
            if value_type in (int, float):
                return sum(values) / len(values)
            
            # Boolean values: use majority
            elif value_type == bool:
                return sum(values) > len(values) / 2
            
            # String values: use longest common substring or most frequent
            elif value_type == str:
                if len(set(values)) == 1:
                    return values[0]  # All strings are identical
                
                # Count frequencies
                counter = Counter(values)
                most_common = counter.most_common(1)[0][0]
                return most_common
            
            # Lists: union with frequencies
            elif value_type in (list, tuple):
                flat_list = [item for sublist in values for item in sublist]
                return list(flat_list)
            
            # Sets: union
            elif value_type == set:
                return set().union(*values)
            
            # Dictionaries: merge with conflict resolution
            elif value_type == dict:
                result = {}
                for d in values:
                    for k, v in d.items():
                        if k not in result:
                            result[k] = v
                        else:
                            # If conflict, recursively integrate values
                            result[k] = self._integrate_values([result[k], v])
                return result
        
        # For mixed types or unsupported types, return the most frequent value
        counter = Counter([str(v) for v in values])
        most_common_str = counter.most_common(1)[0][0]
        
        # Try to find the original value corresponding to this string
        for v in values:
            if str(v) == most_common_str:
                return v
        
        # Fallback to string representation
        return most_common_str
    
    def register_domain_rule(
        self,
        domain: str,
        conflict_type: ConflictType,
        resolution_function: Callable
    ) -> None:
        """
        Register a domain-specific resolution rule.
        
        Args:
            domain: Domain name
            conflict_type: Type of conflict this rule applies to
            resolution_function: Function to resolve conflicts of this type
        """
        if domain not in self.domain_rules:
            self.domain_rules[domain] = {}
        
        self.domain_rules[domain][conflict_type] = resolution_function
        
        logger.debug(f"Registered domain rule for {domain}, conflict type {conflict_type.value}")
    
    def get_unresolved_conflicts(self) -> List[KnowledgeConflict]:
        """
        Get all unresolved conflicts.
        
        Returns:
            List of unresolved conflicts
        """
        return [c for c in self.conflicts.values() if not c.resolved]
    
    def get_conflict_history(
        self,
        conflict_type: Optional[ConflictType] = None,
        source_id: Optional[str] = None,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None
    ) -> List[KnowledgeConflict]:
        """
        Get conflict history filtered by criteria.
        
        Args:
            conflict_type: Filter by conflict type
            source_id: Filter by source ID
            start_time: Filter by start time
            end_time: Filter by end time
            
        Returns:
            List of conflicts matching criteria
        """
        filtered = list(self.conflict_history)
        
        if conflict_type:
            filtered = [c for c in filtered if c.conflict_type == conflict_type]
        
        if source_id:
            filtered = [c for c in filtered if any(s.source_id == source_id for s in c.sources)]
        
        if start_time:
            filtered = [c for c in filtered if c.creation_time >= start_time]
        
        if end_time:
            filtered = [c for c in filtered if c.creation_time <= end_time]
        
        return filtered
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "unresolved_conflicts": len(self.get_unresolved_conflicts()),
            "resolved_conflicts": len([c for c in self.conflicts.values() if c.resolved]),
            "conflict_history_size": len(self.conflict_history),
            "registered_domains": list(self.domain_rules.keys())
        }


class InferenceEngine:
    """
    Generates new knowledge through inference across different knowledge sources.
    """
    
    def __init__(
        self,
        confidence_threshold: float = 0.6,
        max_inference_depth: int = 3,
        max_inferences_stored: int = 10000
    ):
        """
        Initialize the inference engine.
        
        Args:
            confidence_threshold: Minimum confidence for inferences
            max_inference_depth: Maximum depth of inference chains
            max_inferences_stored: Maximum number of inferences to store
        """
        self.confidence_threshold = confidence_threshold
        self.max_inference_depth = max_inference_depth
        
        # Store inferences
        self.inferences = {}  # inference_id -> inference
        self.inference_history = deque(maxlen=max_inferences_stored)
        
        # Inference rules
        self.rules = {}  # rule_id -> rule
        
        # Inference patterns (for rule mining)
        self.patterns = {}  # pattern_id -> pattern
        
        logger.debug("Initialized InferenceEngine")
    
    def add_rule(
        self,
        name: str,
        pattern: str,
        inference_function: Callable,
        description: str = "",
        domain: Optional[str] = None,
        confidence: float = 1.0
    ) -> str:
        """
        Add an inference rule.
        
        Args:
            name: Rule name
            pattern: Rule pattern (e.g., "A → B")
            inference_function: Function implementing the rule
            description: Detailed description
            domain: Knowledge domain this rule applies to
            confidence: Base confidence for this rule
            
        Returns:
            Rule ID
        """
        rule_id = f"rule_{str(uuid.uuid4())[:8]}"
        
        self.rules[rule_id] = {
            "id": rule_id,
            "name": name,
            "pattern": pattern,
            "description": description,
            "domain": domain,
            "confidence": confidence,
            "function": inference_function,
            "created_at": datetime.now().isoformat(),
            "usage_count": 0,
            "success_count": 0
        }
        
        logger.debug(f"Added inference rule {name} ({rule_id})")
        return rule_id
    
    def remove_rule(self, rule_id: str) -> bool:
        """
        Remove an inference rule.
        
        Args:
            rule_id: ID of rule to remove
            
        Returns:
            True if rule was removed, False if not found
        """
        if rule_id in self.rules:
            del self.rules[rule_id]
            return True
        return False
    
    @dataclass
    class InferenceResult:
        """Result of an inference operation."""
        inference_id: str
        sources: List[Dict[str, Any]]
        inferred_knowledge: Dict[str, Any]
        rule_id: str
        confidence: float
        explanation: str
        knowledge_type: KnowledgeType
        timestamp: datetime = field(default_factory=datetime.now)
        depth: int = 1
        parent_inferences: List[str] = field(default_factory=list)
    
    def infer(
        self,
        knowledge_items: List[Dict[str, Any]],
        sources: List[Dict[str, Any]],
        context: Dict[str, Any] = None,
        depth: int = 1
    ) -> List[InferenceResult]:
        """
        Apply inference rules to generate new knowledge.
        
        Args:
            knowledge_items: Knowledge items to infer from
            sources: Sources of the knowledge items
            context: Additional context for inference
            depth: Current inference depth
            
        Returns:
            List of inference results
        """
        if len(knowledge_items) != len(sources):
            raise ValueError("Number of knowledge items must match number of sources")
        
        if depth > self.max_inference_depth:
            return []
        
        context = context or {}
        inferences = []
        
        # Group knowledge by type
        knowledge_by_type = defaultdict(list)
        sources_by_type = defaultdict(list)
        
        for i, item in enumerate(knowledge_items):
            k_type = sources[i].get("knowledge_type", KnowledgeType.SEMANTIC.value)
            if isinstance(k_type, KnowledgeType):
                k_type = k_type.value
            
            knowledge_by_type[k_type].append(item)
            sources_by_type[k_type].append(sources[i])
        
        # Apply same-type inference rules
        for k_type, items in knowledge_by_type.items():
            k_sources = sources_by_type[k_type]
            
            # Apply rules specific to this knowledge type
            type_inferences = self._apply_same_type_rules(k_type, items, k_sources, context, depth)
            inferences.extend(type_inferences)
        
        # Apply cross-type inference rules
        cross_inferences = self._apply_cross_type_rules(knowledge_by_type, sources_by_type, context, depth)
        inferences.extend(cross_inferences)
        
        # Filter and store inferences
        filtered_inferences = [inf for inf in inferences if inf.confidence >= self.confidence_threshold]
        
        for inference in filtered_inferences:
            self.inferences[inference.inference_id] = inference
            self.inference_history.append(inference)
        
        return filtered_inferences
    
    def _apply_same_type_rules(
        self,
        knowledge_type: str,
        items: List[Dict[str, Any]],
        sources: List[Dict[str, Any]],
        context: Dict[str, Any],
        depth: int
    ) -> List[InferenceResult]:
        """
        Apply inference rules for a single knowledge type.
        
        Args:
            knowledge_type: Type of knowledge
            items: Knowledge items of this type
            sources: Sources of these items
            context: Additional context
            depth: Current inference depth
            
        Returns:
            List of inference results
        """
        inferences = []
        
        # Select applicable rules for this knowledge type
        applicable_rules = {
            rule_id: rule for rule_id, rule in self.rules.items()
            if not rule.get("domain") or rule.get("domain") == context.get("domain")
        }
        
        # Apply each rule
        for rule_id, rule in applicable_rules.items():
            rule_function = rule["function"]
            
            try:
                # Update usage count
                rule["usage_count"] += 1
                
                # Apply the rule
                rule_results = rule_function(items, sources, context)
                
                # Process results
                if rule_results:
                    if not isinstance(rule_results, list):
                        rule_results = [rule_results]
                    
                    for result in rule_results:
                        if isinstance(result, dict) and "inferred_knowledge" in result:
                            # Create inference result
                            inference = self.InferenceResult(
                                inference_id=f"inference_{str(uuid.uuid4())[:8]}",
                                sources=[{
                                    "source_id": source.get("source_id", "unknown"),
                                    "knowledge_type": source.get("knowledge_type", knowledge_type)
                                } for source in sources],
                                inferred_knowledge=result["inferred_knowledge"],
                                rule_id=rule_id,
                                confidence=result.get("confidence", rule["confidence"]),
                                explanation=result.get("explanation", f"Inferred using rule {rule['name']}"),
                                knowledge_type=KnowledgeType(knowledge_type) if isinstance(knowledge_type, str) else knowledge_type,
                                depth=depth,
                                parent_inferences=result.get("parent_inferences", [])
                            )
                            
                            inferences.append(inference)
                            
                            # Update success count
                            rule["success_count"] += 1
            except Exception as e:
                logger.error(f"Error applying inference rule {rule['name']}: {str(e)}")
        
        return inferences
    
    def _apply_cross_type_rules(
        self,
        knowledge_by_type: Dict[str, List[Dict[str, Any]]],
        sources_by_type: Dict[str, List[Dict[str, Any]]],
        context: Dict[str, Any],
        depth: int
    ) -> List[InferenceResult]:
        """
        Apply inference rules across different knowledge types.
        
        Args:
            knowledge_by_type: Dictionary mapping knowledge types to items
            sources_by_type: Dictionary mapping knowledge types to sources
            context: Additional context
            depth: Current inference depth
            
        Returns:
            List of inference results
        """
        inferences = []
        
        # Select cross-type rules
        cross_type_rules = {
            rule_id: rule for rule_id, rule in self.rules.items()
            if rule.get("pattern", "").count("→") > 0 and "->" in rule.get("pattern", "")
        }
        
        # Apply each rule
        for rule_id, rule in cross_type_rules.items():
            rule_function = rule["function"]
            
            try:
                # Update usage count
                rule["usage_count"] += 1
                
                # Apply the rule
                rule_results = rule_function(knowledge_by_type, sources_by_type, context)
                
                # Process results
                if rule_results:
                    if not isinstance(rule_results, list):
                        rule_results = [rule_results]
                    
                    for result in rule_results:
                        if isinstance(result, dict) and "inferred_knowledge" in result:
                            # Extract source information
                            result_sources = []
                            for k_type, sources in sources_by_type.items():
                                for source in sources:
                                    result_sources.append({
                                        "source_id": source.get("source_id", "unknown"),
                                        "knowledge_type": source.get("knowledge_type", k_type)
                                    })
                            
                            # Get inferred knowledge type
                            inferred_type = result.get("knowledge_type", KnowledgeType.SEMANTIC.value)
                            if isinstance(inferred_type, str):
                                try:
                                    inferred_type = KnowledgeType(inferred_type)
                                except ValueError:
                                    inferred_type = KnowledgeType.SEMANTIC
                            
                            # Create inference result
                            inference = self.InferenceResult(
                                inference_id=f"inference_{str(uuid.uuid4())[:8]}",
                                sources=result_sources,
                                inferred_knowledge=result["inferred_knowledge"],
                                rule_id=rule_id,
                                confidence=result.get("confidence", rule["confidence"]),
                                explanation=result.get("explanation", f"Inferred using cross-type rule {rule['name']}"),
                                knowledge_type=inferred_type,
                                depth=depth,
                                parent_inferences=result.get("parent_inferences", [])
                            )
                            
                            inferences.append(inference)
                            
                            # Update success count
                            rule["success_count"] += 1
            except Exception as e:
                logger.error(f"Error applying cross-type inference rule {rule['name']}: {str(e)}")
        
        return inferences
    
    def recursive_inference(
        self,
        knowledge_items: List[Dict[str, Any]],
        sources: List[Dict[str, Any]],
        context: Dict[str, Any] = None
    ) -> List[InferenceResult]:
        """
        Apply inference rules recursively to generate chains of inferences.
        
        Args:
            knowledge_items: Initial knowledge items
            sources: Sources of the knowledge items
            context: Additional context
            
        Returns:
            List of all inference results
        """
        context = context or {}
        all_inferences = []
        current_depth = 1
        
        # First-level inferences
        current_inferences = self.infer(knowledge_items, sources, context, current_depth)
        all_inferences.extend(current_inferences)
        
        # Continue inferring until max depth or no new inferences
        while current_inferences and current_depth < self.max_inference_depth:
            current_depth += 1
            next_inferences = []
            
            # Use current inferences as sources for the next level
            for inference in current_inferences:
                # Prepare inference as input for next iteration
                new_item = inference.inferred_knowledge
                new_source = {
                    "source_id": inference.inference_id,
                    "knowledge_type": inference.knowledge_type.value,
                    "confidence": inference.confidence,
                    "rule_id": inference.rule_id
                }
                
                # Record parent inference
                context["parent_inference"] = inference.inference_id
                
                # Apply inference rules
                results = self.infer([new_item], [new_source], context, current_depth)
                
                # Update parent info
                for result in results:
                    result.parent_inferences.append(inference.inference_id)
                    result.depth = current_depth
                
                next_inferences.extend(results)
            
            # Add to all inferences
            all_inferences.extend(next_inferences)
            
            # Update current inferences for next iteration
            current_inferences = next_inferences
        
        return all_inferences
    
    def mine_patterns(
        self,
        knowledge_base: Any,
        min_support: float = 0.1,
        min_confidence: float = 0.7,
        max_patterns: int = 100
    ) -> List[Dict[str, Any]]:
        """
        Mine inference patterns from knowledge base.
        
        Args:
            knowledge_base: Knowledge base to mine
            min_support: Minimum support for patterns
            min_confidence: Minimum confidence for patterns
            max_patterns: Maximum number of patterns to mine
            
        Returns:
            List of mined patterns
        """
        # This is a placeholder for a more sophisticated pattern mining algorithm
        # In a real implementation, this would use association rule mining
        # algorithms like Apriori or FP-Growth
        
        # Placeholder implementation
        patterns = []
        
        # Store patterns
        for i, pattern in enumerate(patterns):
            pattern_id = f"pattern_{str(uuid.uuid4())[:8]}"
            self.patterns[pattern_id] = {
                "id": pattern_id,
                "pattern": pattern["pattern"],
                "support": pattern["support"],
                "confidence": pattern["confidence"],
                "lift": pattern.get("lift", 1.0),
                "discovered_at": datetime.now().isoformat()
            }
            
            # Convert to rule if confidence is high enough
            if pattern["confidence"] >= min_confidence:
                self.add_rule(
                    name=f"Mined Rule {i+1}",
                    pattern=pattern["pattern"],
                    inference_function=self._create_rule_from_pattern(pattern),
                    description=f"Automatically mined rule with support {pattern['support']:.2f} and confidence {pattern['confidence']:.2f}",
                    confidence=pattern["confidence"]
                )
        
        return list(self.patterns.values())
    
    def _create_rule_from_pattern(self, pattern: Dict[str, Any]) -> Callable:
        """
        Create an inference rule function from a mined pattern.
        
        Args:
            pattern: The mined pattern
            
        Returns:
            Inference rule function
        """
        # This is a placeholder for creating a rule from a pattern
        def rule_function(items, sources, context):
            # To be implemented based on the specific pattern structure
            return []
        
        return rule_function
    
    def explain_inference(self, inference_id: str) -> Dict[str, Any]:
        """
        Generate an explanation for an inference.
        
        Args:
            inference_id: ID of the inference to explain
            
        Returns:
            Explanation dictionary
        """
        inference = self.inferences.get(inference_id)
        if not inference:
            return {"error": f"Inference with ID {inference_id} not found"}
        
        rule = self.rules.get(inference.rule_id)
        if not rule:
            return {
                "inference_id": inference_id,
                "explanation": inference.explanation,
                "confidence": inference.confidence,
                "rule": "Unknown rule"
            }
        
        # Build the explanation
        explanation = {
            "inference_id": inference_id,
            "rule_name": rule["name"],
            "rule_pattern": rule["pattern"],
            "explanation": inference.explanation,
            "confidence": inference.confidence,
            "sources": inference.sources,
            "timestamp": inference.timestamp.isoformat(),
            "knowledge_type": inference.knowledge_type.value
        }
        
        # Include parent inferences if any
        if inference.parent_inferences:
            explanation["parent_inferences"] = []
            for parent_id in inference.parent_inferences:
                parent = self.inferences.get(parent_id)
                if parent:
                    explanation["parent_inferences"].append({
                        "inference_id": parent_id,
                        "explanation": parent.explanation,
                        "confidence": parent.confidence
                    })
        
        return explanation
    
    def get_inference(self, inference_id: str) -> Optional[InferenceResult]:
        """
        Get an inference by ID.
        
        Args:
            inference_id: Inference ID
            
        Returns:
            Inference result or None if not found
        """
        return self.inferences.get(inference_id)
    
    def get_rule(self, rule_id: str) -> Optional[Dict[str, Any]]:
        """
        Get a rule by ID.
        
        Args:
            rule_id: Rule ID
            
        Returns:
            Rule dictionary or None if not found
        """
        return self.rules.get(rule_id)
    
    def get_inferences_by_sources(
        self,
        source_ids: List[str],
        require_all: bool = False
    ) -> List[InferenceResult]:
        """
        Get inferences that used specific sources.
        
        Args:
            source_ids: Source IDs to match
            require_all: If True, require all sources to be used
            
        Returns:
            List of matching inferences
        """
        results = []
        
        for inference in self.inferences.values():
            inference_sources = [s["source_id"] for s in inference.sources if "source_id" in s]
            
            if require_all:
                if all(sid in inference_sources for sid in source_ids):
                    results.append(inference)
            else:
                if any(sid in inference_sources for sid in source_ids):
                    results.append(inference)
        
        return results
    
    def get_inferences_by_type(
        self,
        knowledge_type: Union[KnowledgeType, str],
        min_confidence: float = None
    ) -> List[InferenceResult]:
        """
        Get inferences of a specific knowledge type.
        
        Args:
            knowledge_type: Knowledge type to match
            min_confidence: Minimum confidence threshold
            
        Returns:
            List of matching inferences
        """
        if isinstance(knowledge_type, str):
            try:
                knowledge_type = KnowledgeType(knowledge_type)
            except ValueError:
                return []
        
        results = []
        
        for inference in self.inferences.values():
            if inference.knowledge_type == knowledge_type:
                if min_confidence is None or inference.confidence >= min_confidence:
                    results.append(inference)
        
        return results
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "rule_count": len(self.rules),
            "inference_count": len(self.inferences),
            "pattern_count": len(self.patterns),
            "confidence_threshold": self.confidence_threshold,
            "max_inference_depth": self.max_inference_depth
        }


class ContextualRetrieval:
    """
    Retrieves knowledge based on contextual relevance.
    """
    
    def __init__(
        self,
        embedding_dim: int = 768,
        context_window_size: int = 10,
        relevance_threshold: float = 0.6
    ):
        """
        Initialize the contextual retrieval system.
        
        Args:
            embedding_dim: Dimension of context embeddings
            context_window_size: Size of context window for history
            relevance_threshold: Threshold for relevance scoring
        """
        self.embedding_dim = embedding_dim
        self.context_window_size = context_window_size
        self.relevance_threshold = relevance_threshold
        
        # Context embeddings
        try:
            # Try to use transformers for embeddings if available
            from transformers import AutoModel, AutoTokenizer
            
            model_name = "sentence-transformers/all-MiniLM-L6-v2"
            self.tokenizer = AutoTokenizer.from_pretrained(model_name)
            self.model = AutoModel.from_pretrained(model_name).to(DEVICE)
            self.use_transformers = True
            
            logger.debug(f"Using transformers model {model_name} for contextual embeddings")
        except ImportError:
            # Fallback to simpler embedding model
            self.use_transformers = False
            self.embedding_model = nn.Sequential(
                nn.Linear(300, embedding_dim),
                nn.ReLU(),
                nn.Dropout(0.1),
                nn.Linear(embedding_dim, embedding_dim)
            ).to(DEVICE)
            logger.debug("Using fallback embedding model for contextual embeddings")
        
        # Context history
        self.context_history = deque(maxlen=context_window_size)
        
        # Cached embeddings
        self.context_embedding_cache = {}
        self.query_embedding_cache = {}
        
        logger.debug("Initialized ContextualRetrieval")
    
    def encode_text(self, text: str) -> np.ndarray:
        """
        Encode text to embedding vector.
        
        Args:
            text: Text to encode
            
        Returns:
            Embedding vector
        """
        if self.use_transformers:
            # Use transformers model
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512).to(DEVICE)
            with torch.no_grad():
                outputs = self.model(**inputs)
            
            # Use mean of last hidden state
            embeddings = outputs.last_hidden_state.mean(dim=1).cpu().numpy()
            return embeddings[0]
        else:
            # Fallback: simple word averaging with random vectors
            words = text.lower().split()
            # Create random vectors (in a real system, use pre-trained word vectors)
            word_vectors = np.random.randn(len(words), 300).astype(np.float32)
            avg_vector = word_vectors.mean(axis=0)
            
            # Encode with simple model
            with torch.no_grad():
                embedding = self.embedding_model(torch.tensor(avg_vector).to(DEVICE))
                return embedding.cpu().numpy()
    
    def update_context(
        self,
        context_items: List[Dict[str, Any]],
        query: Optional[Dict[str, Any]] = None
    ) -> None:
        """
        Update the current context.
        
        Args:
            context_items: New context items
            query: Current query if available
        """
        # Add to context history
        for item in context_items:
            # Create context entry
            entry = {
                "id": item.get("id", str(uuid.uuid4())),
                "content": item,
                "timestamp": datetime.now().isoformat(),
                "embedding": None  # Will be computed lazily
            }
            
            # Check if we need to compute embedding immediately
            if "content_text" in item:
                text = item["content_text"]
                # Cache the embedding
                if text not in self.context_embedding_cache:
                    self.context_embedding_cache[text] = self.encode_text(text)
                entry["embedding"] = self.context_embedding_cache[text]
            
            self.context_history.append(entry)
        
        # Update with query if provided
        if query:
            entry = {
                "id": query.get("id", str(uuid.uuid4())),
                "content": query,
                "timestamp": datetime.now().isoformat(),
                "is_query": True
            }
            
            if "query_text" in query:
                text = query["query_text"]
                # Cache the embedding
                if text not in self.query_embedding_cache:
                    self.query_embedding_cache[text] = self.encode_text(text)
                entry["embedding"] = self.query_embedding_cache[text]
            
            self.context_history.append(entry)
    
    def get_current_context_embedding(self) -> np.ndarray:
        """
        Get embedding of the current context.
        
        Returns:
            Context embedding vector
        """
        # Collect embeddings from context history
        embeddings = []
        weights = []
        
        for i, entry in enumerate(self.context_history):
            if "embedding" in entry and entry["embedding"] is not None:
                # Use the pre-computed embedding
                embeddings.append(entry["embedding"])
                
                # Recency weighting: newer items have higher weight
                recency_weight = (i + 1) / len(self.context_history)
                weights.append(recency_weight)
                
                # Queries have higher weight
                if entry.get("is_query", False):
                    weights[-1] *= 2.0
        
        if not embeddings:
            # No embeddings available, return zeros
            return np.zeros(self.embedding_dim)
        
        # Normalize weights
        weights = np.array(weights)
        weights = weights / weights.sum()
        
        # Compute weighted average
        weighted_embedding = np.sum([w * e for w, e in zip(weights, embeddings)], axis=0)
        
        # Normalize
        norm = np.linalg.norm(weighted_embedding)
        if norm > 0:
            weighted_embedding = weighted_embedding / norm
        
        return weighted_embedding
    
    def retrieve(
        self,
        knowledge_items: List[Dict[str, Any]],
        query: Optional[Dict[str, Any]] = None,
        top_k: int = 10,
        context_weight: float = 0.7
    ) -> List[Tuple[Dict[str, Any], float]]:
        """
        Retrieve most relevant items based on context and query.
        
        Args:
            knowledge_items: Items to score and rank
            query: Current query if available
            top_k: Number of items to return
            context_weight: Weight for context vs. query relevance
            
        Returns:
            List of (item, relevance_score) tuples
        """
        # Update context with query if provided
        if query:
            self.update_context([], query)
        
        # Get context embedding
        context_embedding = self.get_current_context_embedding()
        
        # If we have a query, get query embedding
        query_embedding = None
        if query and "query_text" in query:
            query_text = query["query_text"]
            if query_text in self.query_embedding_cache:
                query_embedding = self.query_embedding_cache[query_text]
            else:
                query_embedding = self.encode_text(query_text)
                self.query_embedding_cache[query_text] = query_embedding
        
        # Score items
        scored_items = []
        
        for item in knowledge_items:
            # Get item embedding
            item_embedding = None
            
            # Try to find text to embed
            if "content_text" in item:
                text = item["content_text"]
                if text in self.context_embedding_cache:
                    item_embedding = self.context_embedding_cache[text]
                else:
                    item_embedding = self.encode_text(text)
                    self.context_embedding_cache[text] = item_embedding
            elif "text" in item:
                text = item["text"]
                if text in self.context_embedding_cache:
                    item_embedding = self.context_embedding_cache[text]
                else:
                    item_embedding = self.encode_text(text)
                    self.context_embedding_cache[text] = item_embedding
            elif "description" in item:
                text = item["description"]
                if text in self.context_embedding_cache:
                    item_embedding = self.context_embedding_cache[text]
                else:
                    item_embedding = self.encode_text(text)
                    self.context_embedding_cache[text] = item_embedding
            
            if item_embedding is None:
                # Skip items without embedding
                continue
            
            # Calculate relevance score
            context_relevance = self._similarity(context_embedding, item_embedding)
            
            if query_embedding is not None:
                # Calculate query relevance
                query_relevance = self._similarity(query_embedding, item_embedding)
                
                # Combine context and query relevance
                relevance = context_weight * context_relevance + (1 - context_weight) * query_relevance
            else:
                relevance = context_relevance
            
            # Add to scored items if above threshold
            if relevance >= self.relevance_threshold:
                scored_items.append((item, relevance))
        
        # Sort by relevance (descending)
        scored_items.sort(key=lambda x: x[1], reverse=True)
        
        # Return top-k
        return scored_items[:top_k]
    
    def _similarity(self, vec1: np.ndarray, vec2: np.ndarray) -> float:
        """
        Calculate cosine similarity between vectors.
        
        Args:
            vec1: First vector
            vec2: Second vector
            
        Returns:
            Cosine similarity
        """
        norm1 = np.linalg.norm(vec1)
        norm2 = np.linalg.norm(vec2)
        
        if norm1 == 0 or norm2 == 0:
            return 0.0
        
        return np.dot(vec1, vec2) / (norm1 * norm2)
    
    def retrieve_by_type(
        self,
        knowledge_by_type: Dict[str, List[Dict[str, Any]]],
        query: Optional[Dict[str, Any]] = None,
        top_k_per_type: int = 5
    ) -> Dict[str, List[Tuple[Dict[str, Any], float]]]:
        """
        Retrieve most relevant items for each knowledge type.
        
        Args:
            knowledge_by_type: Dictionary mapping knowledge types to items
            query: Current query if available
            top_k_per_type: Number of items to return per type
            
        Returns:
            Dictionary mapping knowledge types to lists of (item, relevance_score) tuples
        """
        results = {}
        
        for k_type, items in knowledge_by_type.items():
            type_results = self.retrieve(items, query, top_k=top_k_per_type)
            results[k_type] = type_results
        
        return results
    
    def clear_context(self) -> None:
        """Clear the current context history."""
        self.context_history.clear()
    
    def get_context_summary(self) -> Dict[str, Any]:
        """
        Get a summary of the current context.
        
        Returns:
            Summary dictionary
        """
        summary = {
            "context_size": len(self.context_history),
            "context_items": []
        }
        
        for entry in self.context_history:
            summary["context_items"].append({
                "id": entry["id"],
                "timestamp": entry["timestamp"],
                "is_query": entry.get("is_query", False),
                "has_embedding": "embedding" in entry and entry["embedding"] is not None
            })
        
        return summary
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "embedding_dim": self.embedding_dim,
            "context_window_size": self.context_window_size,
            "relevance_threshold": self.relevance_threshold,
            "current_context_size": len(self.context_history),
            "embedding_model": "transformers" if self.use_transformers else "fallback"
        }


class CrossModalMapper:
    """
    Maps between different representational modalities.
    """
    
    def __init__(
        self,
        modality_dims: Dict[str, int],
        joint_dim: int = 512,
        hidden_dims: List[int] = [1024, 512],
        use_gpu: bool = torch.cuda.is_available()
    ):
        """
        Initialize the cross-modal mapper.
        
        Args:
            modality_dims: Dict mapping modality names to dimensions
            joint_dim: Dimension of joint representation space
            hidden_dims: Dimensions of hidden layers
            use_gpu: Whether to use GPU acceleration
        """
        self.modality_dims = modality_dims
        self.joint_dim = joint_dim
        self.device = torch.device('cuda' if use_gpu and torch.cuda.is_available() else 'cpu')
        
        # Create representation spaces
        self.representation_spaces = {}
        for modality, dim in modality_dims.items():
            try:
                modality_type = ModalityType(modality)
            except ValueError:
                modality_type = ModalityType.HYBRID
            
            self.representation_spaces[modality] = RepresentationSpace(
                name=f"{modality}_space",
                dimensions=dim,
                modality=modality_type
            )
        
        # Create joint space
        self.joint_space = RepresentationSpace(
            name="joint_space",
            dimensions=joint_dim,
            modality=ModalityType.HYBRID
        )
        
        # Create neural mapping models
        self.encoders = {}  # modality -> joint
        self.decoders = {}  # joint -> modality
        
        for modality, dim in modality_dims.items():
            # Encoder: modality -> joint
            self.encoders[modality] = EmbeddingModel(
                input_dim=dim,
                hidden_dims=hidden_dims,
                output_dim=joint_dim
            ).to(self.device)
            
            # Decoder: joint -> modality
            decoder_hidden_dims = list(reversed(hidden_dims))
            self.decoders[modality] = EmbeddingModel(
                input_dim=joint_dim,
                hidden_dims=decoder_hidden_dims,
                output_dim=dim
            ).to(self.device)
        
        # Create multi-modal model
        self.multi_modal_encoder = MultiModalEmbedding(
            modality_dims=modality_dims,
            shared_dim=hidden_dims[0],
            output_dim=joint_dim,
            hidden_dims=hidden_dims[1:] if len(hidden_dims) > 1 else [128]
        ).to(self.device)
        
        # Alignment matrices
        self.alignment_matrices = {}
        for mod1 in modality_dims:
            for mod2 in modality_dims:
                if mod1 != mod2:
                    key = f"{mod1}_to_{mod2}"
                    self.alignment_matrices[key] = torch.eye(joint_dim).to(self.device)
        
        logger.debug(f"Initialized CrossModalMapper with {len(modality_dims)} modalities")
    
    def map_to_joint_space(
        self,
        modality: str,
        embedding: np.ndarray
    ) -> np.ndarray:
        """
        Map embedding from modality space to joint space.
        
        Args:
            modality: Source modality
            embedding: Source embedding
            
        Returns:
            Joint space embedding
        """
        if modality not in self.encoders:
            raise ValueError(f"Unknown modality: {modality}")
        
        # Convert to tensor
        tensor = torch.tensor(embedding, dtype=torch.float32).to(self.device)
        
        # Make sure it's 2D (batch dimension)
        if tensor.dim() == 1:
            tensor = tensor.unsqueeze(0)
        
        # Map to joint space
        with torch.no_grad():
            joint_embedding = self.encoders[modality](tensor)
            joint_embedding = F.normalize(joint_embedding, p=2, dim=1)
        
        # Convert back to numpy
        return joint_embedding.cpu().numpy()[0]
    
    def map_from_joint_space(
        self,
        target_modality: str,
        joint_embedding: np.ndarray
    ) -> np.ndarray:
        """
        Map embedding from joint space to target modality space.
        
        Args:
            target_modality: Target modality
            joint_embedding: Joint space embedding
            
        Returns:
            Target modality embedding
        """
        if target_modality not in self.decoders:
            raise ValueError(f"Unknown modality: {target_modality}")
        
        # Convert to tensor
        tensor = torch.tensor(joint_embedding, dtype=torch.float32).to(self.device)
        
        # Make sure it's 2D (batch dimension)
        if tensor.dim() == 1:
            tensor = tensor.unsqueeze(0)
        
        # Map from joint space
        with torch.no_grad():
            target_embedding = self.decoders[target_modality](tensor)
            target_embedding = F.normalize(target_embedding, p=2, dim=1)
        
        # Convert back to numpy
        return target_embedding.cpu().numpy()[0]
    
    def map_between_modalities(
        self,
        source_modality: str,
        target_modality: str,
        embedding: np.ndarray
    ) -> np.ndarray:
        """
        Map embedding from source modality to target modality.
        
        Args:
            source_modality: Source modality
            target_modality: Target modality
            embedding: Source embedding
            
        Returns:
            Target modality embedding
        """
        # Use direct alignment matrix if available
        key = f"{source_modality}_to_{target_modality}"
        if key in self.alignment_matrices:
            # Convert to tensor
            tensor = torch.tensor(embedding, dtype=torch.float32).to(self.device)
            
            # Make sure it's 2D (batch dimension)
            if tensor.dim() == 1:
                tensor = tensor.unsqueeze(0)
            
            # Apply alignment matrix
            with torch.no_grad():
                aligned = tensor @ self.alignment_matrices[key]
                aligned = F.normalize(aligned, p=2, dim=1)
            
            # Convert back to numpy
            return aligned.cpu().numpy()[0]
        
        # Otherwise go through joint space
        joint_embedding = self.map_to_joint_space(source_modality, embedding)
        return self.map_from_joint_space(target_modality, joint_embedding)
    
    def encode_multi_modal(
        self,
        inputs: Dict[str, np.ndarray]
    ) -> np.ndarray:
        """
        Encode inputs from multiple modalities into joint space.
        
        Args:
            inputs: Dict mapping modality names to embeddings
            
        Returns:
            Joint space embedding
        """
        # Convert inputs to tensors
        tensor_inputs = {}
        for modality, embedding in inputs.items():
            if modality in self.modality_dims:
                tensor = torch.tensor(embedding, dtype=torch.float32).to(self.device)
                # Make sure it's 2D (batch dimension)
                if tensor.dim() == 1:
                    tensor = tensor.unsqueeze(0)
                tensor_inputs[modality] = tensor
        
        # Check if we have any valid inputs
        if not tensor_inputs:
            raise ValueError("No valid modalities in inputs")
        
        # Encode with multi-modal model
        with torch.no_grad():
            joint_embedding, _ = self.multi_modal_encoder.embed(tensor_inputs)
        
        # Convert back to numpy
        return joint_embedding.cpu().numpy()[0]
    
    def add_entity(
        self,
        entity_id: str,
        modality_embeddings: Dict[str, np.ndarray],
        metadata: Dict[str, Any] = None
    ) -> None:
        """
        Add an entity with embeddings in multiple modalities.
        
        Args:
            entity_id: Entity identifier
            modality_embeddings: Dict mapping modalities to embeddings
            metadata: Additional metadata
        """
        metadata = metadata or {}
        
        # Add to each modality space
        for modality, embedding in modality_embeddings.items():
            if modality in self.representation_spaces:
                self.representation_spaces[modality].add_entity(
                    entity_id=entity_id,
                    embedding=embedding,
                    metadata=metadata
                )
        
        # If we have embeddings for multiple modalities, add to joint space
        if len(modality_embeddings) > 1:
            joint_embedding = self.encode_multi_modal(modality_embeddings)
            self.joint_space.add_entity(
                entity_id=entity_id,
                embedding=joint_embedding,
                metadata=metadata
            )
        # If we only have one modality, map it to joint space
        elif len(modality_embeddings) == 1:
            modality, embedding = next(iter(modality_embeddings.items()))
            joint_embedding = self.map_to_joint_space(modality, embedding)
            self.joint_space.add_entity(
                entity_id=entity_id,
                embedding=joint_embedding,
                metadata=metadata
            )
    
    def find_similar_entities(
        self,
        modality: str,
        embedding: np.ndarray,
        k: int = 5,
        threshold: float = None
    ) -> List[Tuple[str, float]]:
        """
        Find entities similar to embedding in specified modality.
        
        Args:
            modality: Modality to search in
            embedding: Query embedding
            k: Number of results to return
            threshold: Minimum similarity threshold
            
        Returns:
            List of (entity_id, similarity) tuples
        """
        if modality == "joint":
            return self.joint_space.nearest_neighbors(embedding, k, threshold)
        elif modality in self.representation_spaces:
            return self.representation_spaces[modality].nearest_neighbors(embedding, k, threshold)
        else:
            raise ValueError(f"Unknown modality: {modality}")
    
    def find_cross_modal_matches(
        self,
        source_modality: str,
        query_embedding: np.ndarray,
        target_modality: str,
        k: int = 5,
        threshold: float = None
    ) -> List[Tuple[str, float]]:
        """
        Find entities in target modality similar to query in source modality.
        
        Args:
            source_modality: Source query modality
            query_embedding: Query embedding
            target_modality: Target modality to search in
            k: Number of results to return
            threshold: Minimum similarity threshold
            
        Returns:
            List of (entity_id, similarity) tuples
        """
        # Map query to joint space
        if source_modality != "joint":
            joint_query = self.map_to_joint_space(source_modality, query_embedding)
        else:
            joint_query = query_embedding
        
        # If target is joint space, search directly
        if target_modality == "joint":
            return self.joint_space.nearest_neighbors(joint_query, k, threshold)
        
        # Otherwise, map query to target modality
        target_query = self.map_from_joint_space(target_modality, joint_query)
        
        # Search in target space
        return self.representation_spaces[target_modality].nearest_neighbors(target_query, k, threshold)
    
    def train_alignment(
        self,
        source_modality: str,
        target_modality: str,
        aligned_pairs: List[Tuple[np.ndarray, np.ndarray]],
        learning_rate: float = 0.01,
        batch_size: int = 32,
        num_epochs: int = 100
    ) -> Dict[str, Any]:
        """
        Train alignment between modalities using aligned pairs.
        
        Args:
            source_modality: Source modality
            target_modality: Target modality
            aligned_pairs: List of (source_embedding, target_embedding) pairs
            learning_rate: Learning rate for optimization
            batch_size: Batch size for training
            num_epochs: Number of training epochs
            
        Returns:
            Training statistics
        """
        key = f"{source_modality}_to_{target_modality}"
        
        # Set up alignment matrix for training
        alignment_matrix = nn.Parameter(self.alignment_matrices[key].clone())
        optimizer = torch.optim.Adam([alignment_matrix], lr=learning_rate)
        
        # Convert pairs to tensors
        source_tensors = []
        target_tensors = []
        
        for source, target in aligned_pairs:
            source_tensor = torch.tensor(source, dtype=torch.float32).to(self.device)
            target_tensor = torch.tensor(target, dtype=torch.float32).to(self.device)
            
            source_tensors.append(source_tensor)
            target_tensors.append(target_tensor)
        
        source_tensors = torch.stack(source_tensors)
        target_tensors = torch.stack(target_tensors)
        
        # Training loop
        losses = []
        
        for epoch in range(num_epochs):
            # Shuffle data
            indices = torch.randperm(len(aligned_pairs))
            
            epoch_loss = 0.0
            num_batches = 0
            
            # Process in batches
            for i in range(0, len(aligned_pairs), batch_size):
                # Get batch indices
                batch_indices = indices[i:i+batch_size]
                
                # Get batch data
                source_batch = source_tensors[batch_indices]
                target_batch = target_tensors[batch_indices]
                
                # Forward pass
                aligned = torch.matmul(source_batch, alignment_matrix)
                
                # Normalize
                aligned = F.normalize(aligned, p=2, dim=1)
                target_batch = F.normalize(target_batch, p=2, dim=1)
                
                # Compute loss (MSE)
                loss = torch.mean((aligned - target_batch)**2)
                
                # Backward pass
                optimizer.zero_grad()
                loss.backward()
                optimizer.step()
                
                epoch_loss += loss.item()
                num_batches += 1
            
            # Average loss for the epoch
            avg_loss = epoch_loss / num_batches
            losses.append(avg_loss)
            
            if (epoch + 1) % 10 == 0:
                logger.debug(f"Alignment training epoch {epoch+1}/{num_epochs}, loss: {avg_loss:.6f}")
        
        # Update alignment matrix
        self.alignment_matrices[key] = alignment_matrix.data
        
        return {
            "source_modality": source_modality,
            "target_modality": target_modality,
            "num_pairs": len(aligned_pairs),
            "final_loss": losses[-1],
            "losses": losses
        }
    
    def train_multimodal_encoders(
        self,
        multimodal_data: List[Dict[str, np.ndarray]],
        labels: Optional[np.ndarray] = None,
        learning_rate: float = 0.001,
        batch_size: int = 32,
        num_epochs: int = 100
    ) -> Dict[str, Any]:
        """
        Train multi-modal encoders using data with multiple modalities.
        
        Args:
            multimodal_data: List of dicts mapping modalities to embeddings
            labels: Optional labels for supervised training
            learning_rate: Learning rate for optimization
            batch_size: Batch size for training
            num_epochs: Number of training epochs
            
        Returns:
            Training statistics
        """
        # Convert data to tensors
        tensor_data = []
        
        for item in multimodal_data:
            tensor_item = {}
            for modality, embedding in item.items():
                if modality in self.modality_dims:
                    tensor = torch.tensor(embedding, dtype=torch.float32).to(self.device)
                    tensor_item[modality] = tensor
            
            if tensor_item:
                tensor_data.append(tensor_item)
        
        if not tensor_data:
            raise ValueError("No valid multimodal data")
        
        # Setup model for training
        model = self.multi_modal_encoder
        model.train()
        
        # Use contrastive loss for training
        temperature = 0.07
        
        # Optimizer
        optimizer = torch.optim.Adam(model.parameters(), lr=learning_rate)
        
        # Training loop
        losses = []
        
        for epoch in range(num_epochs):
            # Shuffle data
            indices = torch.randperm(len(tensor_data))
            
            epoch_loss = 0.0
            num_batches = 0
            
            # Process in batches
            for i in range(0, len(tensor_data), batch_size):
                # Get batch indices
                batch_indices = indices[i:i+batch_size].tolist()
                
                # Get batch data
                batch_data = [tensor_data[j] for j in batch_indices]
                
                # Forward pass
                batch_embeddings = []
                modality_embeddings = defaultdict(list)
                
                for item in batch_data:
                    # Encode each item
                    joint_embedding, item_modality_embeddings = model(item)
                    batch_embeddings.append(joint_embedding)
                    
                    # Collect modality embeddings
                    for modality, embedding in item_modality_embeddings.items():
                        modality_embeddings[modality].append(embedding)
                
                if batch_embeddings:
                    # Stack embeddings
                    batch_embeddings = torch.cat(batch_embeddings, dim=0)
                    
                    # Compute contrastive loss
                    batch_size = batch_embeddings.size(0)
                    similarity_matrix = torch.matmul(batch_embeddings, batch_embeddings.t()) / temperature
                    
                    # Mask out self-similarity
                    mask = torch.eye(batch_size, dtype=torch.bool).to(self.device)
                    similarity_matrix.masked_fill_(mask, -float('inf'))
                    
                    # Contrastive loss
                    labels = torch.arange(batch_size).to(self.device)
                    loss = F.cross_entropy(similarity_matrix, labels)
                    
                    # Add modality alignment loss
                    modality_loss = 0.0
                    num_modalities = 0
                    
                    for modality, embeddings in modality_embeddings.items():
                        if len(embeddings) > 1:
                            modality_embeddings_tensor = torch.cat(embeddings, dim=0)
                            modality_similarity = torch.matmul(modality_embeddings_tensor, modality_embeddings_tensor.t()) / temperature
                            modality_similarity.masked_fill_(mask, -float('inf'))
                            modality_loss += F.cross_entropy(modality_similarity, labels)
                            num_modalities += 1
                    
                    if num_modalities > 0:
                        modality_loss /= num_modalities
                        loss = 0.7 * loss + 0.3 * modality_loss
                    
                    # Backward pass
                    optimizer.zero_grad()
                    loss.backward()
                    optimizer.step()
                    
                    epoch_loss += loss.item()
                    num_batches += 1
            
            # Average loss for the epoch
            avg_loss = epoch_loss / num_batches if num_batches > 0 else float('inf')
            losses.append(avg_loss)
            
            if (epoch + 1) % 10 == 0:
                logger.debug(f"Multimodal training epoch {epoch+1}/{num_epochs}, loss: {avg_loss:.6f}")
        
        # Set model back to eval mode
        model.eval()
        
        return {
            "num_samples": len(tensor_data),
            "final_loss": losses[-1] if losses else None,
            "losses": losses
        }
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "modality_dims": self.modality_dims,
            "joint_dim": self.joint_dim,
            "num_modalities": len(self.modality_dims),
            "entity_counts": {
                modality: len(space.embeddings)
                for modality, space in self.representation_spaces.items()
            },
            "joint_space_entities": len(self.joint_space.embeddings),
            "device": str(self.device)
        }
    
    def save(self, directory: str) -> None:
        """
        Save the cross-modal mapper to directory.
        
        Args:
            directory: Directory to save to
        """
        import os
        os.makedirs(directory, exist_ok=True)
        
        # Save representation spaces
        for modality, space in self.representation_spaces.items():
            space.save(os.path.join(directory, f"{modality}_space.json"))
        
        self.joint_space.save(os.path.join(directory, "joint_space.json"))
        
        # Save neural models
        for modality, encoder in self.encoders.items():
            torch.save(encoder.state_dict(), os.path.join(directory, f"{modality}_encoder.pt"))
            torch.save(self.decoders[modality].state_dict(), os.path.join(directory, f"{modality}_decoder.pt"))
        
        torch.save(self.multi_modal_encoder.state_dict(), os.path.join(directory, "multimodal_encoder.pt"))
        
        # Save alignment matrices
        alignment_dict = {key: matrix.cpu().numpy() for key, matrix in self.alignment_matrices.items()}
        np.savez(os.path.join(directory, "alignment_matrices.npz"), **alignment_dict)
        
        # Save metadata
        metadata = {
            "modality_dims": self.modality_dims,
            "joint_dim": self.joint_dim,
            "version": "1.0"
        }
        
        with open(os.path.join(directory, "metadata.json"), 'w') as f:
            json.dump(metadata, f)
    
    @classmethod
    def load(cls, directory: str, use_gpu: bool = torch.cuda.is_available()) -> 'CrossModalMapper':
        """
        Load a cross-modal mapper from directory.
        
        Args:
            directory: Directory to load from
            use_gpu: Whether to use GPU
            
        Returns:
            Loaded CrossModalMapper
        """
        import os
        
        # Load metadata
        with open(os.path.join(directory, "metadata.json"), 'r') as f:
            metadata = json.load(f)
        
        modality_dims = metadata["modality_dims"]
        joint_dim = metadata["joint_dim"]
        
        # Create instance
        mapper = cls(modality_dims, joint_dim, use_gpu=use_gpu)
        
        # Load representation spaces
        for modality in modality_dims:
            space_path = os.path.join(directory, f"{modality}_space.json")
            if os.path.exists(space_path):
                mapper.representation_spaces[modality] = RepresentationSpace.load(space_path)
        
        joint_space_path = os.path.join(directory, "joint_space.json")
        if os.path.exists(joint_space_path):
            mapper.joint_space = RepresentationSpace.load(joint_space_path)
        
        # Load neural models
        for modality in modality_dims:
            encoder_path = os.path.join(directory, f"{modality}_encoder.pt")
            decoder_path = os.path.join(directory, f"{modality}_decoder.pt")
            
            if os.path.exists(encoder_path):
                mapper.encoders[modality].load_state_dict(torch.load(encoder_path, map_location=mapper.device))
            
            if os.path.exists(decoder_path):
                mapper.decoders[modality].load_state_dict(torch.load(decoder_path, map_location=mapper.device))
        
        multimodal_path = os.path.join(directory, "multimodal_encoder.pt")
        if os.path.exists(multimodal_path):
            mapper.multi_modal_encoder.load_state_dict(torch.load(multimodal_path, map_location=mapper.device))
        
        # Load alignment matrices
        alignment_path = os.path.join(directory, "alignment_matrices.npz")
        if os.path.exists(alignment_path):
            alignment_data = np.load(alignment_path)
            for key in alignment_data:
                mapper.alignment_matrices[key] = torch.tensor(alignment_data[key]).to(mapper.device)
        
        return mapper


class KnowledgeIntegrator:
    """
    Main class for integrating knowledge across different types and sources.
    """
    
    def __init__(
        self,
        episodic_memory: Optional[EpisodicMemory] = None,
        semantic_network: Optional[SemanticNetwork] = None,
        procedural_kb: Optional[ProceduralKnowledgeBase] = None,
        embedding_dim: int = 768,
        consolidation_threshold: float = 0.7,
        integration_weight: float = 0.5
    ):
        """
        Initialize the knowledge integrator.
        
        Args:
            episodic_memory: Episodic memory system
            semantic_network: Semantic knowledge network
            procedural_kb: Procedural knowledge base
            embedding_dim: Dimension of knowledge embeddings
            consolidation_threshold: Threshold for memory consolidation
            integration_weight: Weight for balancing old and new knowledge
        """
        self.episodic_memory = episodic_memory
        self.semantic_network = semantic_network
        self.procedural_kb = procedural_kb
        
        self.embedding_dim = embedding_dim
        self.consolidation_threshold = consolidation_threshold
        self.integration_weight = integration_weight
        
        # Create supporting components
        self.conflict_resolver = KnowledgeConflictResolver(
            confidence_threshold=consolidation_threshold
        )
        
        self.inference_engine = InferenceEngine(
            confidence_threshold=consolidation_threshold
        )
        
        # Create cross-modal mapper with common modalities
        modality_dims = {
            ModalityType.SYMBOLIC.value: embedding_dim,
            ModalityType.NEURAL.value: embedding_dim,
            ModalityType.TEXTUAL.value: embedding_dim
        }
        
        self.cross_modal_mapper = CrossModalMapper(
            modality_dims=modality_dims,
            joint_dim=embedding_dim
        )
        
        # Create contextual retrieval
        self.contextual_retrieval = ContextualRetrieval(
            embedding_dim=embedding_dim,
            relevance_threshold=0.5
        )
        
        # Integration history
        self.integration_history = deque(maxlen=1000)
        
        logger.debug("Initialized KnowledgeIntegrator")
    
    def integrate_knowledge(
        self,
        sources: List[Tuple[str, Any]],
        context: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        Integrate knowledge from multiple sources.
        
        Args:
            sources: List of (knowledge_type, content) tuples
            context: Additional context for integration
            
        Returns:
            Integration results
        """
        context = context or {}
        integration_id = str(uuid.uuid4())
        start_time = time.time()
        
        integration_result = {
            "integration_id": integration_id,
            "timestamp": datetime.now().isoformat(),
            "num_sources": len(sources),
            "conflicts": [],
            "inferences": [],
            "integrated_knowledge": {},
            "success": False
        }
        
        try:
            # Step 1: Prepare knowledge items and sources
            knowledge_items = []
            knowledge_sources = []
            
            for k_type, content in sources:
                # Convert string type to enum if needed
                if isinstance(k_type, str):
                    try:
                        k_type = KnowledgeType(k_type)
                    except ValueError:
                        logger.warning(f"Unknown knowledge type: {k_type}, defaulting to SEMANTIC")
                        k_type = KnowledgeType.SEMANTIC
                
                # Normalize content format
                if isinstance(content, dict):
                    knowledge_item = content
                else:
                    # Create simple content wrapper
                    knowledge_item = {"content": content}
                
                # Create source info
                source = KnowledgeSource(
                    source_id=knowledge_item.get("id", f"source_{str(uuid.uuid4())[:8]}"),
                    knowledge_type=k_type,
                    reliability=knowledge_item.get("reliability", 1.0),
                    recency=knowledge_item.get("recency", 1.0),
                    metadata=knowledge_item.get("metadata", {})
                )
                
                knowledge_items.append(knowledge_item)
                knowledge_sources.append(source)
            
            # Step 2: Detect conflicts
            conflicts = self.conflict_resolver.detect_conflicts(
                knowledge_items, knowledge_sources, context
            )
            
            # Record conflicts
            for conflict in conflicts:
                integration_result["conflicts"].append({
                    "conflict_id": conflict.conflict_id,
                    "conflict_type": conflict.conflict_type.value,
                    "description": conflict.description,
                    "severity": conflict.severity
                })
            
            # Step 3: Resolve conflicts
            resolutions = self.conflict_resolver.resolve_all_conflicts(conflicts, context)
            
            # Step 4: Apply resolutions to create consistent knowledge
            consistent_items = self._apply_conflict_resolutions(
                knowledge_items, knowledge_sources, conflicts, resolutions
            )
            
            # Step 5: Generate inferences
            inferences = self.inference_engine.infer(
                consistent_items, 
                [{"source_id": s.source_id, "knowledge_type": s.knowledge_type.value} 
                 for s in knowledge_sources],
                context
            )
            
            # Record inferences
            for inference in inferences:
                integration_result["inferences"].append({
                    "inference_id": inference.inference_id,
                    "rule_id": inference.rule_id,
                    "knowledge_type": inference.knowledge_type.value,
                    "confidence": inference.confidence,
                    "explanation": inference.explanation
                })
                
                # Add inferred knowledge to consistent items
                consistent_items.append(inference.inferred_knowledge)
            
            # Step 6: Integrate with existing knowledge
            integrated = self._integrate_with_existing(consistent_items, context)
            
            integration_result["integrated_knowledge"] = integrated
            integration_result["success"] = True
            
        except Exception as e:
            logger.error(f"Error in knowledge integration: {str(e)}")
            integration_result["error"] = str(e)
        
        # Record timing
        integration_result["processing_time"] = time.time() - start_time
        
        # Add to history
        self.integration_history.append(integration_result)
        
        return integration_result
    
    def _apply_conflict_resolutions(
        self,
        knowledge_items: List[Dict[str, Any]],
        sources: List[KnowledgeSource],
        conflicts: List[KnowledgeConflict],
        resolutions: Dict[str, Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """
        Apply conflict resolutions to create consistent knowledge.
        
        Args:
            knowledge_items: Original knowledge items
            sources: Knowledge sources
            conflicts: Detected conflicts
            resolutions: Conflict resolutions
            
        Returns:
            List of consistent knowledge items
        """
        if not conflicts:
            # No conflicts, return original items
            return knowledge_items.copy()
        
        # Create a copy of knowledge items
        consistent_items = deepcopy(knowledge_items)
        
        # Track items that are removed due to conflicts
        removed_indices = set()
        
        # Apply resolutions
        for conflict in conflicts:
            resolution = resolutions.get(conflict.conflict_id)
            if not resolution:
                continue
            
            resolution_type = resolution.get("resolution_type")
            if not resolution_type:
                continue
            
            # Get source indices for this conflict
            conflict_source_ids = [s.source_id for s in conflict.sources]
            source_indices = [i for i, s in enumerate(sources) if s.source_id in conflict_source_ids]
            
            if resolution_type == ResolutionStrategy.RECENCY.value:
                # Keep the most recent source
                source_id = resolution.get("source_id")
                if source_id:
                    # Mark all other sources as removed
                    for idx in source_indices:
                        if sources[idx].source_id != source_id:
                            removed_indices.add(idx)
            
            elif resolution_type == ResolutionStrategy.CONFIDENCE.value:
                # Keep the highest confidence source
                source_id = resolution.get("source_id")
                if source_id:
                    # Mark all other sources as removed
                    for idx in source_indices:
                        if sources[idx].source_id != source_id:
                            removed_indices.add(idx)
            
            elif resolution_type == ResolutionStrategy.SOURCE_RELIABILITY.value:
                # Keep the most reliable source
                source_id = resolution.get("source_id")
                if source_id:
                    # Mark all other sources as removed
                    for idx in source_indices:
                        if sources[idx].source_id != source_id:
                            removed_indices.add(idx)
            
            elif resolution_type == ResolutionStrategy.MAJORITY_VOTE.value:
                # Update items with the majority value
                selected_value = resolution.get("selected_value")
                if selected_value is not None:
                    # Extract the conflicting attribute
                    match = re.search(r"'([^']+)':", conflict.description)
                    if match:
                        attribute = match.group(1)
                        
                        # Update all items with the majority value
                        for idx in source_indices:
                            if isinstance(consistent_items[idx], dict):
                                # Find where to put the value
                                if attribute in consistent_items[idx]:
                                    consistent_items[idx][attribute] = selected_value
                                elif "facts" in consistent_items[idx] and attribute in consistent_items[idx]["facts"]:
                                    consistent_items[idx]["facts"][attribute] = selected_value
                                elif "details" in consistent_items[idx] and attribute in consistent_items[idx]["details"]:
                                    consistent_items[idx]["details"][attribute] = selected_value
            
            elif resolution_type == ResolutionStrategy.INTEGRATE_ALL.value:
                # Keep all sources but update with integrated value
                selected_value = resolution.get("selected_value")
                if selected_value is not None:
                    # Extract the conflicting attribute
                    match = re.search(r"'([^']+)':", conflict.description)
                    if match:
                        attribute = match.group(1)
                        
                        # Update all items with the integrated value
                        for idx in source_indices:
                            if isinstance(consistent_items[idx], dict):
                                # Find where to put the value
                                if attribute in consistent_items[idx]:
                                    consistent_items[idx][attribute] = selected_value
                                elif "facts" in consistent_items[idx] and attribute in consistent_items[idx]["facts"]:
                                    consistent_items[idx]["facts"][attribute] = selected_value
                                elif "details" in consistent_items[idx] and attribute in consistent_items[idx]["details"]:
                                    consistent_items[idx]["details"][attribute] = selected_value
            
            elif resolution_type == ResolutionStrategy.DEFER.value:
                # Mark all conflicting sources as removed to be conservative
                for idx in source_indices:
                    removed_indices.add(idx)
        
        # Create final consistent set by filtering out removed items
        return [item for i, item in enumerate(consistent_items) if i not in removed_indices]
    
    def _integrate_with_existing(
        self,
        consistent_items: List[Dict[str, Any]],
        context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Integrate consistent knowledge with existing knowledge.
        
        Args:
            consistent_items: Consistent knowledge items
            context: Integration context
            
        Returns:
            Integration results by knowledge type
        """
        # Group items by knowledge type
        items_by_type = defaultdict(list)
        
        for item in consistent_items:
            k_type = item.get("knowledge_type", KnowledgeType.SEMANTIC.value)
            if isinstance(k_type, KnowledgeType):
                k_type = k_type.value
            
            items_by_type[k_type].append(item)
        
        results = {}
        
        # Integrate by type
        for k_type, items in items_by_type.items():
            if k_type == KnowledgeType.EPISODIC.value:
                results[k_type] = self._integrate_episodic(items, context)
            elif k_type == KnowledgeType.SEMANTIC.value:
                results[k_type] = self._integrate_semantic(items, context)
            elif k_type == KnowledgeType.PROCEDURAL.value:
                results[k_type] = self._integrate_procedural(items, context)
            else:
                # Generic integration
                results[k_type] = {"integrated_items": items}
        
        return results
    
    def _integrate_episodic(
        self,
        items: List[Dict[str, Any]],
        context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Integrate episodic knowledge.
        
        Args:
            items: Episodic knowledge items
            context: Integration context
            
        Returns:
            Integration results
        """
        result = {
            "integrated_count": 0,
            "stored_events": []
        }
        
        # If no episodic memory, just return
        if self.episodic_memory is None:
            return result
        
        # Store each item as an episodic event
        for item in items:
            try:
                # Create event
                if isinstance(item, dict):
                    # Extract relevant fields
                    content = item.get("content", item)
                    timestamp = item.get("timestamp")
                    context_dict = item.get("context", {})
                    
                    # Store event
                    event_id = self.episodic_memory.store_event(content, context_dict)
                    result["stored_events"].append(event_id)
                    result["integrated_count"] += 1
                
            except Exception as e:
                logger.error(f"Error integrating episodic item: {str(e)}")
        
        return result
    
    def _integrate_semantic(
        self,
        items: List[Dict[str, Any]],
        context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Integrate semantic knowledge.
        
        Args:
            items: Semantic knowledge items
            context: Integration context
            
        Returns:
            Integration results
        """
        result = {
            "integrated_count": 0,
            "added_concepts": [],
            "updated_concepts": [],
            "added_relations": []
        }
        
        # If no semantic network, just return
        if self.semantic_network is None:
            return result
        
        # Process each item
        for item in items:
            try:
                if isinstance(item, dict):
                    # Check for concepts
                    if "concept" in item:
                        concept = item["concept"]
                        properties = item.get("properties", {})
                        
                        # Check if concept exists
                        existing = self.semantic_network.query({"concept": concept})
                        
                        if existing:
                            # Update existing concept
                            self.semantic_network.update_knowledge(existing[0], properties)
                            result["updated_concepts"].append(concept)
                        else:
                            # Add new concept
                            concept_id = self.semantic_network.add_knowledge(item)
                            result["added_concepts"].append(concept)
                        
                        result["integrated_count"] += 1
                    
                    # Check for relations
                    elif "source" in item and "target" in item and "relation" in item:
                        # Add relation
                        relation_id = self.semantic_network.add_relation(
                            source=item["source"],
                            target=item["target"],
                            relation_type=item["relation"],
                            properties=item.get("properties", {})
                        )
                        
                        result["added_relations"].append(relation_id)
                        result["integrated_count"] += 1
            
            except Exception as e:
                logger.error(f"Error integrating semantic item: {str(e)}")
        
        return result
    
    def _integrate_procedural(
        self,
        items: List[Dict[str, Any]],
        context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Integrate procedural knowledge.
        
        Args:
            items: Procedural knowledge items
            context: Integration context
            
        Returns:
            Integration results
        """
        result = {
            "integrated_count": 0,
            "added_actions": [],
            "added_tasks": [],
            "updated_procedures": []
        }
        
        # If no procedural KB, just return
        if self.procedural_kb is None:
            return result
        
        # Process each item
        for item in items:
            try:
                if isinstance(item, dict):
                    # Determine type and add to KB
                    item_id = self.procedural_kb.add_procedure(item, context)
                    
                    # Categorize by type
                    item_type = item.get("type", "unknown")
                    if item_type == "action":
                        result["added_actions"].append(item_id)
                    elif item_type == "task":
                        result["added_tasks"].append(item_id)
                    else:
                        result["updated_procedures"].append(item_id)
                    
                    result["integrated_count"] += 1
            
            except Exception as e:
                logger.error(f"Error integrating procedural item: {str(e)}")
        
        return result
    
    def consolidate_knowledge(
        self,
        context: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        Consolidate knowledge across different types (e.g., episodic to semantic).
        
        Args:
            context: Consolidation context
            
        Returns:
            Consolidation results
        """
        context = context or {}
        
        consolidation_result = {
            "timestamp": datetime.now().isoformat(),
            "episodic_to_semantic": {
                "consolidated_count": 0,
                "new_concepts": [],
                "new_relations": []
            },
            "semantic_to_procedural": {
                "consolidated_count": 0,
                "new_actions": [],
                "new_tasks": []
            }
        }
        
        # Episodic to semantic consolidation
        if self.episodic_memory is not None and self.semantic_network is not None:
            # Get recent episodic memories
            recent_events = self.episodic_memory.retrieve_events(
                {"recency": {"min_time": context.get("min_time"), "max_time": context.get("max_time")}},
                limit=context.get("event_limit", 100)
            )
            
            for event in recent_events:
                # Check if event meets consolidation threshold
                if event.get("importance", 0.0) >= self.consolidation_threshold:
                    # Extract semantic knowledge
                    semantic_items = self._extract_semantic_from_episodic(event, context)
                    
                    # Add to semantic network
                    for item in semantic_items:
                        try:
                            if "concept" in item:
                                concept_id = self.semantic_network.add_knowledge(item)
                                consolidation_result["episodic_to_semantic"]["new_concepts"].append(concept_id)
                                consolidation_result["episodic_to_semantic"]["consolidated_count"] += 1
                            elif "source" in item and "target" in item and "relation" in item:
                                relation_id = self.semantic_network.add_relation(
                                    source=item["source"],
                                    target=item["target"],
                                    relation_type=item["relation"],
                                    properties=item.get("properties", {})
                                )
                                consolidation_result["episodic_to_semantic"]["new_relations"].append(relation_id)
                                consolidation_result["episodic_to_semantic"]["consolidated_count"] += 1
                        except Exception as e:
                            logger.error(f"Error in episodic-to-semantic consolidation: {str(e)}")
        
        # Semantic to procedural consolidation
        if self.semantic_network is not None and self.procedural_kb is not None:
            # Look for procedural patterns in semantic knowledge
            procedural_candidates = self.semantic_network.query(
                {"relation_type": "precedes"}
            )
            
            for candidate in procedural_candidates:
                if candidate.get("confidence", 0.0) >= self.consolidation_threshold:
                    # Extract procedural knowledge
                    procedural_items = self._extract_procedural_from_semantic(candidate, context)
                    
                    # Add to procedural KB
                    for item in procedural_items:
                        try:
                            item_id = self.procedural_kb.add_procedure(item, context)
                            
                            # Categorize by type
                            item_type = item.get("type", "unknown")
                            if item_type == "action":
                                consolidation_result["semantic_to_procedural"]["new_actions"].append(item_id)
                            elif item_type == "task":
                                consolidation_result["semantic_to_procedural"]["new_tasks"].append(item_id)
                            
                            consolidation_result["semantic_to_procedural"]["consolidated_count"] += 1
                        except Exception as e:
                            logger.error(f"Error in semantic-to-procedural consolidation: {str(e)}")
        
        return consolidation_result
    
    def _extract_semantic_from_episodic(
        self,
        event: Dict[str, Any],
        context: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """
        Extract semantic knowledge from episodic event.
        
        Args:
            event: Episodic event
            context: Extraction context
            
        Returns:
            List of semantic knowledge items
        """
        # This is a placeholder for a more sophisticated extraction algorithm
        # In a real implementation, this would use natural language processing
        # and pattern recognition to identify concepts and relations
        
        semantic_items = []
        
        # Sample extraction logic
        if "entities" in event:
            # Extract entities as concepts
            for entity in event["entities"]:
                if isinstance(entity, dict) and "name" in entity:
                    semantic_items.append({
                        "concept": entity["name"],
                        "properties": entity.get("properties", {}),
                        "confidence": event.get("confidence", 0.8),
                        "source_event": event.get("id")
                    })
        
        if "actions" in event:
            # Extract actions and their participants
            for action in event["actions"]:
                if isinstance(action, dict) and "subject" in action and "object" in action and "action" in action:
                    semantic_items.append({
                        "source": action["subject"],
                        "target": action["object"],
                        "relation": action["action"],
                        "properties": action.get("properties", {}),
                        "confidence": event.get("confidence", 0.8),
                        "source_event": event.get("id")
                    })
        
        return semantic_items
    
    def _extract_procedural_from_semantic(
        self,
        semantic_item: Dict[str, Any],
        context: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """
        Extract procedural knowledge from semantic knowledge.
        
        Args:
            semantic_item: Semantic knowledge item
            context: Extraction context
            
        Returns:
            List of procedural knowledge items
        """
        # This is a placeholder for a more sophisticated extraction algorithm
        # In a real implementation, this would analyze semantic patterns
        # to identify procedures, actions, and tasks
        
        procedural_items = []
        
        # Sample extraction logic
        if "source" in semantic_item and "target" in semantic_item and semantic_item.get("relation") == "precedes":
            # Create an action sequence
            procedural_items.append({
                "type": "action_sequence",
                "name": f"Sequence of {semantic_item['source']} to {semantic_item['target']}",
                "steps": [semantic_item["source"], semantic_item["target"]],
                "confidence": semantic_item.get("confidence", 0.7),
                "source_semantic": semantic_item.get("id")
            })
        
        return procedural_items
    
    def retrieve_relevant_knowledge(
        self,
        query: Dict[str, Any],
        context: Dict[str, Any] = None,
        top_k: int = 10
    ) -> Dict[str, List[Tuple[Dict[str, Any], float]]]:
        """
        Retrieve knowledge relevant to a query across all types.
        
        Args:
            query: Query specification
            context: Retrieval context
            top_k: Number of items to return per type
            
        Returns:
            Dictionary mapping knowledge types to lists of (item, relevance) tuples
        """
        context = context or {}
        
        # Update context with query
        self.contextual_retrieval.update_context([], query)
        
        # Collect knowledge from different sources
        knowledge_by_type = {}
        
        # Episodic knowledge
        if self.episodic_memory is not None:
            if isinstance(query.get("text", ""), str):
                # Text query
                events = self.episodic_memory.retrieve_events(
                    {"text": query["text"]},
                    limit=50  # Get more than needed for ranking
                )
            else:
                # General query
                events = self.episodic_memory.retrieve_events(
                    query,
                    limit=50
                )
            
            # Add text content for ranking
            for event in events:
                if "description" in event:
                    event["content_text"] = event["description"]
                elif "details" in event and isinstance(event["details"], dict):
                    event["content_text"] = str(event["details"])
            
            knowledge_by_type[KnowledgeType.EPISODIC.value] = events
        
        # Semantic knowledge
        if self.semantic_network is not None:
            if isinstance(query.get("text", ""), str):
                # Text query
                concepts = self.semantic_network.query(
                    {"text": query["text"]},
                    limit=50
                )
            else:
                # General query
                concepts = self.semantic_network.query(
                    query,
                    limit=50
                )
            
            # Add text content for ranking
            for concept in concepts:
                if "name" in concept:
                    concept["content_text"] = concept["name"]
                elif "description" in concept:
                    concept["content_text"] = concept["description"]
                elif "properties" in concept and isinstance(concept["properties"], dict):
                    concept["content_text"] = str(concept["properties"])
            
            knowledge_by_type[KnowledgeType.SEMANTIC.value] = concepts
        
        # Procedural knowledge
        if self.procedural_kb is not None:
            if isinstance(query.get("text", ""), str):
                # Text query
                procedures = self.procedural_kb.retrieve_procedure(
                    {"query": query["text"]},
                    context
                )
            else:
                # General query
                procedures = self.procedural_kb.retrieve_procedure(
                    query,
                    context
                )
            
            # Convert to list if not already
            if not isinstance(procedures, list):
                procedures = [procedures]
            
            # Add text content for ranking
            for procedure in procedures:
                if "name" in procedure:
                    procedure["content_text"] = procedure["name"]
                elif "description" in procedure:
                    procedure["content_text"] = procedure["description"]
            
            knowledge_by_type[KnowledgeType.PROCEDURAL.value] = procedures
        
        # Rank by relevance
        results = self.contextual_retrieval.retrieve_by_type(
            knowledge_by_type,
            query,
            top_k_per_type=top_k
        )
        
        return results
    
    def save_cross_modal_mappings(self, directory: str) -> None:
        """
        Save cross-modal mappings to a directory.
        
        Args:
            directory: Directory to save to
        """
        self.cross_modal_mapper.save(directory)
    
    def load_cross_modal_mappings(self, directory: str) -> None:
        """
        Load cross-modal mappings from a directory.
        
        Args:
            directory: Directory to load from
        """
        self.cross_modal_mapper = CrossModalMapper.load(directory)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "has_episodic_memory": self.episodic_memory is not None,
            "has_semantic_network": self.semantic_network is not None,
            "has_procedural_kb": self.procedural_kb is not None,
            "embedding_dim": self.embedding_dim,
            "consolidation_threshold": self.consolidation_threshold,
            "integration_weight": self.integration_weight,
            "modalities": list(self.cross_modal_mapper.modality_dims.keys()),
            "integration_history_size": len(self.integration_history)
        }