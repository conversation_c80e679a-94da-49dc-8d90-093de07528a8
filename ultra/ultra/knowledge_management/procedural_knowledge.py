#!/usr/bin/env python3
"""
ULTRA: Procedural Knowledge Management

This module implements the procedural knowledge component of ULTRA's knowledge management system.
Procedural knowledge represents "how to" information - sequences of actions, skills, and rules
that allow the system to perform tasks and achieve goals. This is distinct from semantic knowledge
(facts about the world) and episodic knowledge (experiences and events).

The module implements:
1. Action representations with preconditions and effects
2. Action sequences for multi-step procedures
3. Task schemas with goals, constraints, and context
4. Hierarchical skill representations with primitive and composite actions
5. Condition-action rules for reactive behaviors
6. Learning algorithms for acquiring procedural knowledge
7. Execution monitoring and error recovery

Mathematical foundations:
- Markov Decision Processes for sequential action modeling
- Hierarchical Task Networks for decomposing complex tasks
- Partially Observable MDP extensions for incomplete information
- Dynamic Bayesian Networks for representing action effects and uncertainties

Integration with:
- Core Neural Architecture for neuroplasticity in skill learning
- Diffusion-Based Reasoning for planning through action space
- Meta-Cognitive System for monitoring execution and error handling
- Neuro-Symbolic Bridge for grounding symbolic actions in neural representations
"""

import numpy as np
import networkx as nx
import json
import uuid
import logging
import time
from datetime import datetime
from typing import Dict, List, Tuple, Set, Any, Optional, Union, Callable, TypeVar, Generic
from collections import defaultdict, deque
from enum import Enum
from dataclasses import dataclass, field
from copy import deepcopy

logger = logging.getLogger(__name__)

# Type definitions
T = TypeVar('T')
ActionID = str
TaskID = str
SkillID = str
RuleID = str
StateType = Dict[str, Any]
ParameterType = Dict[str, Any]
PreconditionType = Callable[[StateType, ParameterType], bool]
EffectType = Callable[[StateType, ParameterType], StateType]


class ActionStatus(Enum):
    """Status of an action during or after execution."""
    PENDING = "pending"
    EXECUTING = "executing"
    COMPLETED = "completed"
    FAILED = "failed"
    INTERRUPTED = "interrupted"


class ActionCategory(Enum):
    """Categories of actions based on their nature."""
    PHYSICAL = "physical"     # Actions that affect the physical world
    COGNITIVE = "cognitive"   # Mental operations like reasoning, calculation
    PERCEPTUAL = "perceptual" # Sensing and perceiving the environment
    COMMUNICATIVE = "communicative"  # Communication actions
    INTERNAL = "internal"     # System's internal operations


@dataclass
class ProbabilisticEffect:
    """Represents a probabilistic effect of an action."""
    effect: EffectType
    probability: float
    description: str = ""
    
    def apply(self, state: StateType, parameters: ParameterType) -> StateType:
        """
        Applies the effect to a state with given probability.
        
        Args:
            state: Current state
            parameters: Action parameters
            
        Returns:
            New state after applying effect (if probability check passes)
        """
        if np.random.random() < self.probability:
            return self.effect(state, parameters)
        return state.copy()


class Action:
    """
    Represents an atomic action with preconditions, effects, and metadata.
    Actions are the basic building blocks of procedural knowledge.
    """
    
    def __init__(
        self,
        name: str,
        description: str = "",
        category: ActionCategory = ActionCategory.COGNITIVE,
        preconditions: List[PreconditionType] = None,
        effects: List[Union[EffectType, ProbabilisticEffect]] = None,
        parameters: List[str] = None,
        duration: Optional[float] = None,
        cost: float = 0.0,
        reliability: float = 1.0,
        implementation: Optional[Callable] = None
    ):
        """
        Initialize an action.
        
        Args:
            name: Action name
            description: Detailed description
            category: Action category
            preconditions: List of functions checking if action can be executed
            effects: List of functions or probabilistic effects describing action's effects
            parameters: List of parameter names this action accepts
            duration: Expected duration in seconds (None for instantaneous)
            cost: Computational or resource cost
            reliability: Probability of success if preconditions are met (0-1)
            implementation: Actual function implementing the action
        """
        self.id = f"action_{str(uuid.uuid4())[:8]}"
        self.name = name
        self.description = description
        self.category = category
        self.preconditions = preconditions or []
        self.effects = effects or []
        self.parameters = parameters or []
        self.duration = duration
        self.cost = cost
        self.reliability = reliability
        self.implementation = implementation
        
        # Execution metadata
        self.execution_count = 0
        self.success_count = 0
        self.avg_execution_time = 0.0
        self.last_execution_time = None
        self.last_execution_status = None
        
        # For learning and adaptation
        self.parameter_history = []
        self.state_transition_history = []
        
        logger.debug(f"Action created: {self.name} ({self.id})")
    
    def check_preconditions(self, state: StateType, parameters: ParameterType) -> Tuple[bool, List[str]]:
        """
        Check if all preconditions are satisfied.
        
        Args:
            state: Current state of the world
            parameters: Parameters for this action instance
            
        Returns:
            Tuple of (all_satisfied, failed_preconditions)
        """
        failed = []
        
        # Check if all required parameters are provided
        for param in self.parameters:
            if param not in parameters:
                failed.append(f"Missing required parameter: {param}")
        
        # Check all preconditions
        for i, precond in enumerate(self.preconditions):
            try:
                if not precond(state, parameters):
                    failed.append(f"Precondition {i+1} not satisfied")
            except Exception as e:
                failed.append(f"Error checking precondition {i+1}: {str(e)}")
        
        return len(failed) == 0, failed
    
    def apply_effects(self, state: StateType, parameters: ParameterType) -> StateType:
        """
        Apply all effects to produce a new state.
        
        Args:
            state: Current state
            parameters: Parameters for this action instance
            
        Returns:
            New state after applying all effects
        """
        new_state = state.copy()
        
        for effect in self.effects:
            try:
                if isinstance(effect, ProbabilisticEffect):
                    new_state = effect.apply(new_state, parameters)
                else:
                    new_state = effect(new_state, parameters)
            except Exception as e:
                logger.error(f"Error applying effect in action {self.name}: {str(e)}")
        
        return new_state
    
    def execute(self, state: StateType, parameters: ParameterType) -> Tuple[StateType, ActionStatus, Dict[str, Any]]:
        """
        Execute the action in the given state with parameters.
        
        Args:
            state: Current state
            parameters: Parameters for this action instance
            
        Returns:
            Tuple of (new_state, status, execution_info)
        """
        # Start timing
        start_time = time.time()
        execution_info = {
            "action_id": self.id,
            "action_name": self.name,
            "parameters": parameters,
            "start_time": datetime.fromtimestamp(start_time).isoformat(),
            "preconditions_satisfied": False
        }
        
        # Check if all required parameters are present and preconditions are satisfied
        preconditions_satisfied, failed_preconditions = self.check_preconditions(state, parameters)
        execution_info["preconditions_satisfied"] = preconditions_satisfied
        
        if not preconditions_satisfied:
            execution_info["failed_preconditions"] = failed_preconditions
            execution_info["status"] = ActionStatus.FAILED.value
            self.last_execution_status = ActionStatus.FAILED
            return state, ActionStatus.FAILED, execution_info
        
        # Determine if action succeeds based on reliability
        succeeds = np.random.random() < self.reliability
        
        if not succeeds:
            execution_info["status"] = ActionStatus.FAILED.value
            execution_info["failure_reason"] = "Action failed due to reliability factor"
            self.last_execution_status = ActionStatus.FAILED
            self.execution_count += 1
            return state, ActionStatus.FAILED, execution_info
        
        # Execute implementation if available
        result = None
        if self.implementation:
            try:
                self.last_execution_status = ActionStatus.EXECUTING
                result = self.implementation(state, parameters)
                execution_info["implementation_result"] = result
            except Exception as e:
                logger.error(f"Error executing action {self.name}: {str(e)}")
                execution_info["status"] = ActionStatus.FAILED.value
                execution_info["failure_reason"] = f"Implementation error: {str(e)}"
                self.last_execution_status = ActionStatus.FAILED
                self.execution_count += 1
                return state, ActionStatus.FAILED, execution_info
        
        # Apply effects
        try:
            new_state = self.apply_effects(state, parameters)
        except Exception as e:
            logger.error(f"Error applying effects for action {self.name}: {str(e)}")
            execution_info["status"] = ActionStatus.FAILED.value
            execution_info["failure_reason"] = f"Effect application error: {str(e)}"
            self.last_execution_status = ActionStatus.FAILED
            self.execution_count += 1
            return state, ActionStatus.FAILED, execution_info
        
        # Finish timing and update statistics
        end_time = time.time()
        execution_time = end_time - start_time
        
        self.execution_count += 1
        self.success_count += 1
        
        # Update average execution time
        self.avg_execution_time = (self.avg_execution_time * (self.execution_count - 1) + execution_time) / self.execution_count
        self.last_execution_time = execution_time
        self.last_execution_status = ActionStatus.COMPLETED
        
        # Store history for learning
        self.parameter_history.append(parameters.copy())
        self.state_transition_history.append((state.copy(), new_state.copy()))
        
        # Finalize execution info
        execution_info.update({
            "status": ActionStatus.COMPLETED.value,
            "end_time": datetime.fromtimestamp(end_time).isoformat(),
            "execution_time": execution_time,
            "result": result
        })
        
        return new_state, ActionStatus.COMPLETED, execution_info
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert action to dictionary representation for storage."""
        # Serialize only serializable components
        result = {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "category": self.category.value,
            "parameters": self.parameters,
            "duration": self.duration,
            "cost": self.cost,
            "reliability": self.reliability,
            "execution_count": self.execution_count,
            "success_count": self.success_count,
            "avg_execution_time": self.avg_execution_time,
            "last_execution_time": self.last_execution_time,
            "last_execution_status": self.last_execution_status.value if self.last_execution_status else None
        }
        return result
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Action':
        """Create action from dictionary representation."""
        # Handle non-serializable components
        action = cls(
            name=data["name"],
            description=data.get("description", ""),
            category=ActionCategory(data.get("category", ActionCategory.COGNITIVE.value)),
            parameters=data.get("parameters", []),
            duration=data.get("duration"),
            cost=data.get("cost", 0.0),
            reliability=data.get("reliability", 1.0)
        )
        
        # Restore metadata
        action.id = data.get("id", action.id)
        action.execution_count = data.get("execution_count", 0)
        action.success_count = data.get("success_count", 0)
        action.avg_execution_time = data.get("avg_execution_time", 0.0)
        action.last_execution_time = data.get("last_execution_time")
        
        if data.get("last_execution_status"):
            action.last_execution_status = ActionStatus(data["last_execution_status"])
        
        return action
    
    def __str__(self) -> str:
        return f"Action({self.name}, {self.id})"
    
    def __repr__(self) -> str:
        return self.__str__()


@dataclass
class ActionInstance:
    """
    Represents a specific instance of an action with concrete parameters.
    Used in action sequences and task execution.
    """
    action: Action
    parameters: Dict[str, Any]
    status: ActionStatus = ActionStatus.PENDING
    start_time: Optional[float] = None
    end_time: Optional[float] = None
    result: Any = None
    
    def execute(self, state: StateType) -> Tuple[StateType, ActionStatus, Dict[str, Any]]:
        """Execute this action instance in the given state."""
        self.status = ActionStatus.EXECUTING
        self.start_time = time.time()
        
        new_state, status, info = self.action.execute(state, self.parameters)
        
        self.status = status
        self.end_time = time.time()
        self.result = info.get("result")
        
        return new_state, status, info


class ActionSequence:
    """
    Represents a sequence of actions to be executed in order.
    """
    
    def __init__(
        self,
        name: str,
        description: str = "",
        actions: List[Union[Action, ActionInstance, 'ActionSequence']] = None,
        parameters: Dict[str, Any] = None,
        atomic: bool = False  # If true, sequence is treated as atomic (all or nothing)
    ):
        """
        Initialize an action sequence.
        
        Args:
            name: Sequence name
            description: Detailed description
            actions: List of actions, action instances, or nested sequences
            parameters: Global parameters for all actions
            atomic: If True, sequence must be executed completely or not at all
        """
        self.id = f"seq_{str(uuid.uuid4())[:8]}"
        self.name = name
        self.description = description
        self.actions = actions or []
        self.parameters = parameters or {}
        self.atomic = atomic
        
        # Execution metadata
        self.current_index = 0
        self.status = ActionStatus.PENDING
        self.start_time = None
        self.end_time = None
        self.execution_history = []
    
    def add_action(self, action: Union[Action, ActionInstance, 'ActionSequence'], index: Optional[int] = None) -> int:
        """
        Add an action to the sequence.
        
        Args:
            action: Action to add
            index: Position to insert (None for append)
            
        Returns:
            Index where action was inserted
        """
        if index is None:
            self.actions.append(action)
            return len(self.actions) - 1
        else:
            self.actions.insert(index, action)
            return index
    
    def remove_action(self, index: int) -> Union[Action, ActionInstance, 'ActionSequence']:
        """
        Remove action at given index.
        
        Args:
            index: Index of action to remove
            
        Returns:
            Removed action
        """
        if 0 <= index < len(self.actions):
            return self.actions.pop(index)
        else:
            raise IndexError(f"Index {index} out of range for sequence with {len(self.actions)} actions")
    
    def execute(self, state: StateType, continue_on_failure: bool = False) -> Tuple[StateType, ActionStatus, List[Dict[str, Any]]]:
        """
        Execute the entire sequence.
        
        Args:
            state: Initial state
            continue_on_failure: Whether to continue execution if an action fails
            
        Returns:
            Tuple of (final_state, overall_status, execution_logs)
        """
        self.reset()
        self.status = ActionStatus.EXECUTING
        self.start_time = time.time()
        
        current_state = state.copy()
        execution_logs = []
        
        for i, action_item in enumerate(self.actions):
            self.current_index = i
            
            # Prepare action parameters (combining sequence parameters with instance parameters)
            if isinstance(action_item, ActionInstance):
                action_params = {**self.parameters, **action_item.parameters}
                action_instance = ActionInstance(
                    action=action_item.action,
                    parameters=action_params,
                    status=ActionStatus.PENDING
                )
            elif isinstance(action_item, Action):
                action_params = self.parameters.copy()
                action_instance = ActionInstance(
                    action=action_item,
                    parameters=action_params,
                    status=ActionStatus.PENDING
                )
            elif isinstance(action_item, ActionSequence):
                # Execute nested sequence
                nested_state, nested_status, nested_logs = action_item.execute(
                    current_state, 
                    continue_on_failure=continue_on_failure and not self.atomic
                )
                
                execution_logs.extend(nested_logs)
                
                if nested_status != ActionStatus.COMPLETED and not continue_on_failure:
                    self.status = ActionStatus.FAILED
                    self.end_time = time.time()
                    return current_state, ActionStatus.FAILED, execution_logs
                
                current_state = nested_state
                continue
            else:
                logger.error(f"Unknown action type in sequence: {type(action_item)}")
                continue
            
            # Execute the action
            try:
                new_state, action_status, action_log = action_instance.execute(current_state)
                execution_logs.append(action_log)
                
                # Update state if action succeeded
                if action_status == ActionStatus.COMPLETED:
                    current_state = new_state
                elif not continue_on_failure or self.atomic:
                    # Stop execution on failure if not continuing or sequence is atomic
                    self.status = ActionStatus.FAILED
                    self.end_time = time.time()
                    return current_state, ActionStatus.FAILED, execution_logs
            except Exception as e:
                logger.error(f"Error executing action {i} in sequence {self.name}: {str(e)}")
                execution_logs.append({
                    "action_id": action_instance.action.id,
                    "action_name": action_instance.action.name,
                    "status": ActionStatus.FAILED.value,
                    "error": str(e)
                })
                
                if not continue_on_failure or self.atomic:
                    self.status = ActionStatus.FAILED
                    self.end_time = time.time()
                    return current_state, ActionStatus.FAILED, execution_logs
        
        # All actions completed successfully
        self.status = ActionStatus.COMPLETED
        self.end_time = time.time()
        self.execution_history.append({
            "start_time": self.start_time,
            "end_time": self.end_time,
            "status": self.status.value,
            "execution_logs": execution_logs
        })
        
        return current_state, ActionStatus.COMPLETED, execution_logs
    
    def reset(self):
        """Reset execution state for a new run."""
        self.current_index = 0
        self.status = ActionStatus.PENDING
        self.start_time = None
        self.end_time = None
    
    def get_estimated_duration(self) -> float:
        """
        Estimate the total duration of the sequence.
        
        Returns:
            Estimated duration in seconds
        """
        total_duration = 0.0
        
        for action_item in self.actions:
            if isinstance(action_item, Action) and action_item.duration is not None:
                total_duration += action_item.duration
            elif isinstance(action_item, ActionInstance) and action_item.action.duration is not None:
                total_duration += action_item.action.duration
            elif isinstance(action_item, ActionSequence):
                total_duration += action_item.get_estimated_duration()
        
        return total_duration
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert sequence to dictionary representation."""
        action_dicts = []
        for action_item in self.actions:
            if isinstance(action_item, Action):
                action_dicts.append({"type": "action", "data": action_item.to_dict()})
            elif isinstance(action_item, ActionInstance):
                action_dicts.append({
                    "type": "instance",
                    "action": action_item.action.to_dict(),
                    "parameters": action_item.parameters
                })
            elif isinstance(action_item, ActionSequence):
                action_dicts.append({"type": "sequence", "data": action_item.to_dict()})
        
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "parameters": self.parameters,
            "atomic": self.atomic,
            "actions": action_dicts,
            "status": self.status.value,
            "execution_history": self.execution_history
        }
    
    def __str__(self) -> str:
        return f"ActionSequence({self.name}, {len(self.actions)} actions)"
    
    def __repr__(self) -> str:
        return self.__str__()


@dataclass
class TaskConstraint:
    """
    Represents a constraint on a task's execution.
    """
    name: str
    description: str
    check_function: Callable[[StateType, Dict[str, Any]], bool]
    severity: float = 1.0  # 0-1 scale, 1 being a hard constraint
    
    def check(self, state: StateType, context: Dict[str, Any]) -> bool:
        """Check if the constraint is satisfied."""
        try:
            return self.check_function(state, context)
        except Exception as e:
            logger.error(f"Error checking constraint {self.name}: {str(e)}")
            return False


class TaskSchema:
    """
    Represents a task schema with goals, constraints, and possible solution methods.
    A task schema is a template for solving a class of problems.
    """
    
    def __init__(
        self,
        name: str,
        description: str = "",
        goal_state: Union[StateType, Callable[[StateType], bool]] = None,
        constraints: List[TaskConstraint] = None,
        preconditions: List[PreconditionType] = None,
        solution_methods: List[Union[ActionSequence, Callable]] = None,
        parameters: List[str] = None,
        estimated_complexity: float = 0.0,  # 0-1 scale
        domain: str = "general"
    ):
        """
        Initialize a task schema.
        
        Args:
            name: Task name
            description: Detailed description
            goal_state: Target state or function to check if goal is achieved
            constraints: List of constraints on task execution
            preconditions: List of functions checking if task can be attempted
            solution_methods: List of action sequences or functions that can solve the task
            parameters: List of parameter names this task requires
            estimated_complexity: Estimated complexity (0-1 scale)
            domain: Knowledge domain this task belongs to
        """
        self.id = f"task_{str(uuid.uuid4())[:8]}"
        self.name = name
        self.description = description
        self.goal_state = goal_state or {}
        self.constraints = constraints or []
        self.preconditions = preconditions or []
        self.solution_methods = solution_methods or []
        self.parameters = parameters or []
        self.estimated_complexity = estimated_complexity
        self.domain = domain
        
        # Task execution history
        self.execution_count = 0
        self.success_count = 0
        self.avg_execution_time = 0.0
        self.execution_history = []
        
        # For learning and adaptation
        self.successful_methods = defaultdict(int)
        self.failed_methods = defaultdict(int)
        
        logger.debug(f"TaskSchema created: {self.name} ({self.id})")
    
    def check_goal_achieved(self, state: StateType) -> bool:
        """
        Check if the goal has been achieved.
        
        Args:
            state: Current state to check
            
        Returns:
            True if goal is achieved, False otherwise
        """
        if callable(self.goal_state):
            try:
                return self.goal_state(state)
            except Exception as e:
                logger.error(f"Error checking goal for task {self.name}: {str(e)}")
                return False
        else:
            # Compare state against goal_state dictionary
            for key, value in self.goal_state.items():
                if key not in state or state[key] != value:
                    return False
            return True
    
    def check_constraints(self, state: StateType, context: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """
        Check if all constraints are satisfied.
        
        Args:
            state: Current state
            context: Execution context
            
        Returns:
            Tuple of (all_satisfied, failed_constraints)
        """
        failed = []
        
        for constraint in self.constraints:
            try:
                if not constraint.check(state, context):
                    failed.append(constraint.name)
            except Exception as e:
                logger.error(f"Error checking constraint {constraint.name}: {str(e)}")
                failed.append(f"{constraint.name} (error: {str(e)})")
        
        return len(failed) == 0, failed
    
    def check_preconditions(self, state: StateType, parameters: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """
        Check if all preconditions are satisfied.
        
        Args:
            state: Current state
            parameters: Task parameters
            
        Returns:
            Tuple of (all_satisfied, failed_preconditions)
        """
        failed = []
        
        # Check if all required parameters are provided
        for param in self.parameters:
            if param not in parameters:
                failed.append(f"Missing required parameter: {param}")
        
        # Check all preconditions
        for i, precond in enumerate(self.preconditions):
            try:
                if not precond(state, parameters):
                    failed.append(f"Precondition {i+1} not satisfied")
            except Exception as e:
                failed.append(f"Error checking precondition {i+1}: {str(e)}")
        
        return len(failed) == 0, failed
    
    def execute(
        self, 
        state: StateType, 
        parameters: Dict[str, Any], 
        method_index: Optional[int] = None,
        context: Dict[str, Any] = None
    ) -> Tuple[StateType, bool, Dict[str, Any]]:
        """
        Execute the task using one of its solution methods.
        
        Args:
            state: Initial state
            parameters: Task parameters
            method_index: Index of solution method to use (None for auto-select)
            context: Additional execution context
            
        Returns:
            Tuple of (final_state, success, execution_info)
        """
        context = context or {}
        start_time = time.time()
        
        execution_info = {
            "task_id": self.id,
            "task_name": self.name,
            "parameters": parameters,
            "start_time": datetime.fromtimestamp(start_time).isoformat(),
            "method_index": method_index
        }
        
        # Check preconditions
        preconditions_satisfied, failed_preconditions = self.check_preconditions(state, parameters)
        execution_info["preconditions_satisfied"] = preconditions_satisfied
        
        if not preconditions_satisfied:
            execution_info["failed_preconditions"] = failed_preconditions
            execution_info["success"] = False
            return state, False, execution_info
        
        # Check constraints
        constraints_satisfied, failed_constraints = self.check_constraints(state, context)
        execution_info["constraints_satisfied"] = constraints_satisfied
        
        if not constraints_satisfied:
            execution_info["failed_constraints"] = failed_constraints
            execution_info["success"] = False
            return state, False, execution_info
        
        # No solution methods available
        if not self.solution_methods:
            execution_info["error"] = "No solution methods available"
            execution_info["success"] = False
            return state, False, execution_info
        
        # Auto-select method if none specified
        if method_index is None:
            method_index = self._select_best_method(state, parameters, context)
        
        # Validate method index
        if method_index < 0 or method_index >= len(self.solution_methods):
            execution_info["error"] = f"Invalid method index: {method_index}"
            execution_info["success"] = False
            return state, False, execution_info
        
        # Execute the selected method
        method = self.solution_methods[method_index]
        current_state = state.copy()
        
        try:
            if isinstance(method, ActionSequence):
                # Execute action sequence
                new_state, status, logs = method.execute(current_state)
                execution_info["action_logs"] = logs
                success = status == ActionStatus.COMPLETED
            elif callable(method):
                # Execute function
                result = method(current_state, parameters, context)
                
                if isinstance(result, tuple) and len(result) >= 2:
                    new_state, success = result[0], result[1]
                    if len(result) > 2:
                        execution_info["method_info"] = result[2]
                else:
                    new_state, success = result, True
            else:
                logger.error(f"Unknown method type: {type(method)}")
                execution_info["error"] = f"Unknown method type: {type(method)}"
                execution_info["success"] = False
                return current_state, False, execution_info
        except Exception as e:
            logger.error(f"Error executing method {method_index} for task {self.name}: {str(e)}")
            execution_info["error"] = f"Execution error: {str(e)}"
            execution_info["success"] = False
            
            # Record failure
            self.execution_count += 1
            if isinstance(method, ActionSequence):
                self.failed_methods[method.id] += 1
            else:
                self.failed_methods[f"method_{method_index}"] += 1
            
            return current_state, False, execution_info
        
        # Check if goal is achieved
        goal_achieved = self.check_goal_achieved(new_state)
        execution_info["goal_achieved"] = goal_achieved
        
        # Update execution statistics
        self.execution_count += 1
        end_time = time.time()
        execution_time = end_time - start_time
        
        if goal_achieved and success:
            self.success_count += 1
            if isinstance(method, ActionSequence):
                self.successful_methods[method.id] += 1
            else:
                self.successful_methods[f"method_{method_index}"] += 1
        else:
            if isinstance(method, ActionSequence):
                self.failed_methods[method.id] += 1
            else:
                self.failed_methods[f"method_{method_index}"] += 1
        
        # Update average execution time
        self.avg_execution_time = (self.avg_execution_time * (self.execution_count - 1) + execution_time) / self.execution_count
        
        # Record execution history
        history_entry = {
            "parameters": parameters,
            "method_index": method_index,
            "success": goal_achieved and success,
            "execution_time": execution_time,
            "timestamp": datetime.now().isoformat()
        }
        self.execution_history.append(history_entry)
        
        # Finalize execution info
        execution_info.update({
            "success": goal_achieved and success,
            "end_time": datetime.fromtimestamp(end_time).isoformat(),
            "execution_time": execution_time
        })
        
        return new_state, goal_achieved and success, execution_info
    
    def _select_best_method(self, state: StateType, parameters: Dict[str, Any], context: Dict[str, Any]) -> int:
        """
        Select the best solution method based on past performance.
        
        Args:
            state: Current state
            parameters: Task parameters
            context: Execution context
            
        Returns:
            Index of selected method
        """
        # Simple selection based on success rate
        if self.execution_count > 0:
            # Calculate success rates for each method
            success_rates = []
            
            for i, method in enumerate(self.solution_methods):
                if isinstance(method, ActionSequence):
                    method_id = method.id
                else:
                    method_id = f"method_{i}"
                
                successes = self.successful_methods[method_id]
                failures = self.failed_methods[method_id]
                total = successes + failures
                
                if total > 0:
                    success_rate = successes / total
                else:
                    # No data yet, assume 50% success rate
                    success_rate = 0.5
                
                success_rates.append((i, success_rate))
            
            # Select method with highest success rate
            if success_rates:
                best_method = max(success_rates, key=lambda x: x[1])
                return best_method[0]
        
        # No history or all methods failed, select random method
        return np.random.randint(0, len(self.solution_methods))
    
    def add_solution_method(self, method: Union[ActionSequence, Callable]) -> int:
        """
        Add a solution method.
        
        Args:
            method: Action sequence or function implementing the method
            
        Returns:
            Index of the added method
        """
        self.solution_methods.append(method)
        return len(self.solution_methods) - 1
    
    def remove_solution_method(self, index: int) -> Union[ActionSequence, Callable]:
        """
        Remove a solution method.
        
        Args:
            index: Index of method to remove
            
        Returns:
            Removed method
        """
        if 0 <= index < len(self.solution_methods):
            return self.solution_methods.pop(index)
        else:
            raise IndexError(f"Index {index} out of range for task with {len(self.solution_methods)} methods")
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert task schema to dictionary representation."""
        # Handle non-serializable components
        solution_methods = []
        for method in self.solution_methods:
            if isinstance(method, ActionSequence):
                solution_methods.append({"type": "action_sequence", "data": method.to_dict()})
            else:
                solution_methods.append({"type": "function", "data": f"Function: {method.__name__}"})
        
        constraints = []
        for constraint in self.constraints:
            constraints.append({
                "name": constraint.name,
                "description": constraint.description,
                "severity": constraint.severity
            })
        
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "parameters": self.parameters,
            "domain": self.domain,
            "estimated_complexity": self.estimated_complexity,
            "constraints": constraints,
            "solution_methods_count": len(self.solution_methods),
            "execution_count": self.execution_count,
            "success_count": self.success_count,
            "avg_execution_time": self.avg_execution_time,
            "successful_methods": dict(self.successful_methods),
            "failed_methods": dict(self.failed_methods)
        }
    
    def __str__(self) -> str:
        return f"TaskSchema({self.name}, {self.id})"
    
    def __repr__(self) -> str:
        return self.__str__()


class SkillHierarchy:
    """
    Represents a hierarchical structure of skills and subskills.
    Uses a directed acyclic graph to model skill dependencies and compositions.
    """
    
    def __init__(self, name: str, description: str = ""):
        """
        Initialize a skill hierarchy.
        
        Args:
            name: Hierarchy name
            description: Detailed description
        """
        self.id = f"skill_hier_{str(uuid.uuid4())[:8]}"
        self.name = name
        self.description = description
        
        # Using networkx for the skill graph
        self.skill_graph = nx.DiGraph()
        
        # Mapping of skill IDs to their implementations
        self.skill_implementations = {}
        
        # Skill metadata
        self.skill_metadata = {}
        
        logger.debug(f"SkillHierarchy created: {self.name} ({self.id})")
    
    def add_skill(
        self,
        skill_id: str,
        name: str,
        implementation: Union[Action, ActionSequence, TaskSchema],
        parent_skills: List[str] = None,
        metadata: Dict[str, Any] = None
    ) -> str:
        """
        Add a skill to the hierarchy.
        
        Args:
            skill_id: Unique identifier for the skill
            name: Skill name
            implementation: Action, sequence, or task implementing the skill
            parent_skills: List of parent skill IDs
            metadata: Additional skill metadata
            
        Returns:
            Skill ID
        """
        if skill_id in self.skill_graph:
            raise ValueError(f"Skill ID {skill_id} already exists in hierarchy")
        
        # Add node to graph
        self.skill_graph.add_node(skill_id, name=name)
        
        # Add implementation
        self.skill_implementations[skill_id] = implementation
        
        # Add metadata
        self.skill_metadata[skill_id] = metadata or {}
        self.skill_metadata[skill_id]["name"] = name
        
        # Add edges from parents
        if parent_skills:
            for parent_id in parent_skills:
                if parent_id not in self.skill_graph:
                    raise ValueError(f"Parent skill ID {parent_id} does not exist in hierarchy")
                self.skill_graph.add_edge(parent_id, skill_id)
        
        return skill_id
    
    def remove_skill(self, skill_id: str) -> Union[Action, ActionSequence, TaskSchema]:
        """
        Remove a skill from the hierarchy.
        
        Args:
            skill_id: ID of skill to remove
            
        Returns:
            Skill implementation
        """
        if skill_id not in self.skill_graph:
            raise ValueError(f"Skill ID {skill_id} does not exist in hierarchy")
        
        # Check for child skills
        children = list(self.skill_graph.successors(skill_id))
        if children:
            raise ValueError(f"Cannot remove skill {skill_id} with children: {children}")
        
        # Remove from graph
        self.skill_graph.remove_node(skill_id)
        
        # Remove implementation and metadata
        implementation = self.skill_implementations.pop(skill_id)
        self.skill_metadata.pop(skill_id)
        
        return implementation
    
    def get_skill(self, skill_id: str) -> Tuple[Union[Action, ActionSequence, TaskSchema], Dict[str, Any]]:
        """
        Get skill implementation and metadata.
        
        Args:
            skill_id: ID of skill to retrieve
            
        Returns:
            Tuple of (implementation, metadata)
        """
        if skill_id not in self.skill_graph:
            raise ValueError(f"Skill ID {skill_id} does not exist in hierarchy")
        
        implementation = self.skill_implementations.get(skill_id)
        metadata = self.skill_metadata.get(skill_id, {})
        
        return implementation, metadata
    
    def get_parent_skills(self, skill_id: str) -> List[str]:
        """
        Get parent skills of a skill.
        
        Args:
            skill_id: ID of skill
            
        Returns:
            List of parent skill IDs
        """
        if skill_id not in self.skill_graph:
            raise ValueError(f"Skill ID {skill_id} does not exist in hierarchy")
        
        return list(self.skill_graph.predecessors(skill_id))
    
    def get_child_skills(self, skill_id: str) -> List[str]:
        """
        Get child skills of a skill.
        
        Args:
            skill_id: ID of skill
            
        Returns:
            List of child skill IDs
        """
        if skill_id not in self.skill_graph:
            raise ValueError(f"Skill ID {skill_id} does not exist in hierarchy")
        
        return list(self.skill_graph.successors(skill_id))
    
    def get_root_skills(self) -> List[str]:
        """
        Get all root skills (skills with no parents).
        
        Returns:
            List of root skill IDs
        """
        return [node for node in self.skill_graph.nodes() if self.skill_graph.in_degree(node) == 0]
    
    def get_leaf_skills(self) -> List[str]:
        """
        Get all leaf skills (skills with no children).
        
        Returns:
            List of leaf skill IDs
        """
        return [node for node in self.skill_graph.nodes() if self.skill_graph.out_degree(node) == 0]
    
    def get_skill_path(self, from_skill: str, to_skill: str) -> List[str]:
        """
        Find shortest path between two skills.
        
        Args:
            from_skill: Starting skill ID
            to_skill: Target skill ID
            
        Returns:
            List of skill IDs forming a path
        """
        if from_skill not in self.skill_graph or to_skill not in self.skill_graph:
            raise ValueError(f"Skill IDs must exist in hierarchy")
        
        try:
            return nx.shortest_path(self.skill_graph, from_skill, to_skill)
        except nx.NetworkXNoPath:
            return []
    
    def get_prerequisites(self, skill_id: str) -> List[str]:
        """
        Get all prerequisite skills needed to learn the given skill.
        
        Args:
            skill_id: ID of skill
            
        Returns:
            List of prerequisite skill IDs
        """
        if skill_id not in self.skill_graph:
            raise ValueError(f"Skill ID {skill_id} does not exist in hierarchy")
        
        # Get all ancestors (prerequisite skills)
        ancestors = nx.ancestors(self.skill_graph, skill_id)
        return list(ancestors)
    
    def execute_skill(
        self,
        skill_id: str,
        state: StateType,
        parameters: Dict[str, Any],
        context: Dict[str, Any] = None
    ) -> Tuple[StateType, bool, Dict[str, Any]]:
        """
        Execute a skill in the given state.
        
        Args:
            skill_id: ID of skill to execute
            state: Current state
            parameters: Execution parameters
            context: Additional execution context
            
        Returns:
            Tuple of (new_state, success, execution_info)
        """
        implementation, metadata = self.get_skill(skill_id)
        
        execution_info = {
            "skill_id": skill_id,
            "skill_name": metadata.get("name", skill_id),
            "start_time": datetime.now().isoformat()
        }
        
        try:
            if isinstance(implementation, Action):
                new_state, status, action_info = implementation.execute(state, parameters)
                success = status == ActionStatus.COMPLETED
                execution_info.update(action_info)
            elif isinstance(implementation, ActionSequence):
                new_state, status, action_logs = implementation.execute(state)
                success = status == ActionStatus.COMPLETED
                execution_info["action_logs"] = action_logs
            elif isinstance(implementation, TaskSchema):
                new_state, success, task_info = implementation.execute(state, parameters, context=context)
                execution_info.update(task_info)
            else:
                logger.error(f"Unknown skill implementation type: {type(implementation)}")
                return state, False, {**execution_info, "error": "Unknown implementation type"}
        except Exception as e:
            logger.error(f"Error executing skill {skill_id}: {str(e)}")
            return state, False, {**execution_info, "error": str(e), "success": False}
        
        execution_info["end_time"] = datetime.now().isoformat()
        execution_info["success"] = success
        
        return new_state, success, execution_info
    
    def visualize(self, output_path: Optional[str] = None) -> Any:
        """
        Visualize the skill hierarchy as a graph.
        
        Args:
            output_path: Path to save visualization (None for display)
            
        Returns:
            Matplotlib figure object
        """
        try:
            import matplotlib.pyplot as plt
            from networkx.drawing.nx_agraph import graphviz_layout
            
            plt.figure(figsize=(12, 8))
            
            # Use hierarchical layout
            try:
                pos = graphviz_layout(self.skill_graph, prog="dot")
            except ImportError:
                # Fall back to spring layout if graphviz not available
                pos = nx.spring_layout(self.skill_graph)
            
            # Get node labels
            node_labels = {node: self.skill_metadata.get(node, {}).get("name", node) 
                          for node in self.skill_graph.nodes()}
            
            # Draw nodes and edges
            nx.draw_networkx_nodes(self.skill_graph, pos, node_size=3000, alpha=0.8)
            nx.draw_networkx_edges(self.skill_graph, pos, width=1.5, alpha=0.7, arrows=True)
            nx.draw_networkx_labels(self.skill_graph, pos, labels=node_labels, font_size=10)
            
            plt.title(f"Skill Hierarchy: {self.name}")
            plt.axis("off")
            
            if output_path:
                plt.savefig(output_path, bbox_inches="tight")
                logger.info(f"Saved hierarchy visualization to {output_path}")
            
            return plt.gcf()
        except ImportError:
            logger.warning("Matplotlib or networkx not available for visualization")
            return None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert skill hierarchy to dictionary representation."""
        nodes = []
        for node in self.skill_graph.nodes():
            implementation = self.skill_implementations.get(node)
            impl_type = "unknown"
            impl_id = "unknown"
            
            if isinstance(implementation, Action):
                impl_type = "action"
                impl_id = implementation.id
            elif isinstance(implementation, ActionSequence):
                impl_type = "sequence"
                impl_id = implementation.id
            elif isinstance(implementation, TaskSchema):
                impl_type = "task"
                impl_id = implementation.id
            
            nodes.append({
                "id": node,
                "name": self.skill_metadata.get(node, {}).get("name", node),
                "implementation_type": impl_type,
                "implementation_id": impl_id,
                "metadata": self.skill_metadata.get(node, {})
            })
        
        edges = []
        for src, dst in self.skill_graph.edges():
            edges.append({"source": src, "target": dst})
        
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "nodes": nodes,
            "edges": edges
        }
    
    def __str__(self) -> str:
        return f"SkillHierarchy({self.name}, {len(self.skill_graph.nodes())} skills)"
    
    def __repr__(self) -> str:
        return self.__str__()


class ConditionActionRule:
    """
    Represents a condition-action rule for reactive behavior.
    When the condition is met, the associated action is triggered.
    """
    
    def __init__(
        self,
        name: str,
        condition: Callable[[StateType, Dict[str, Any]], bool],
        action: Union[Action, ActionSequence, TaskSchema, Callable],
        description: str = "",
        priority: float = 0.0,  # Higher values indicate higher priority
        parameters: Dict[str, Any] = None,
        enabled: bool = True
    ):
        """
        Initialize a condition-action rule.
        
        Args:
            name: Rule name
            condition: Function to check if rule should trigger
            action: Action to execute when condition is met
            description: Detailed description
            priority: Rule priority (higher values = higher priority)
            parameters: Default parameters for action execution
            enabled: Whether the rule is initially enabled
        """
        self.id = f"rule_{str(uuid.uuid4())[:8]}"
        self.name = name
        self.description = description
        self.condition = condition
        self.action = action
        self.priority = priority
        self.parameters = parameters or {}
        self.enabled = enabled
        
        # Execution statistics
        self.trigger_count = 0
        self.success_count = 0
        self.last_triggered = None
        self.last_execution_time = None
        
        logger.debug(f"ConditionActionRule created: {self.name} ({self.id})")
    
    def check_condition(self, state: StateType, context: Dict[str, Any] = None) -> bool:
        """
        Check if the rule's condition is met.
        
        Args:
            state: Current state
            context: Additional context information
            
        Returns:
            True if condition is met, False otherwise
        """
        if not self.enabled:
            return False
        
        context = context or {}
        
        try:
            return self.condition(state, context)
        except Exception as e:
            logger.error(f"Error checking condition for rule {self.name}: {str(e)}")
            return False
    
    def execute(
        self,
        state: StateType,
        context: Dict[str, Any] = None,
        override_parameters: Dict[str, Any] = None
    ) -> Tuple[StateType, bool, Dict[str, Any]]:
        """
        Execute the rule's action.
        
        Args:
            state: Current state
            context: Additional context information
            override_parameters: Parameters that override defaults
            
        Returns:
            Tuple of (new_state, success, execution_info)
        """
        if not self.enabled:
            return state, False, {"error": "Rule is disabled"}
        
        context = context or {}
        start_time = time.time()
        
        # Merge default parameters with overrides
        parameters = {**self.parameters}
        if override_parameters:
            parameters.update(override_parameters)
        
        execution_info = {
            "rule_id": self.id,
            "rule_name": self.name,
            "parameters": parameters,
            "start_time": datetime.fromtimestamp(start_time).isoformat()
        }
        
        self.trigger_count += 1
        self.last_triggered = datetime.now()
        
        try:
            if isinstance(self.action, Action):
                new_state, status, action_info = self.action.execute(state, parameters)
                success = status == ActionStatus.COMPLETED
                execution_info.update(action_info)
            elif isinstance(self.action, ActionSequence):
                new_state, status, action_logs = self.action.execute(state)
                success = status == ActionStatus.COMPLETED
                execution_info["action_logs"] = action_logs
            elif isinstance(self.action, TaskSchema):
                new_state, success, task_info = self.action.execute(state, parameters, context=context)
                execution_info.update(task_info)
            elif callable(self.action):
                result = self.action(state, parameters, context)
                
                if isinstance(result, tuple) and len(result) >= 2:
                    new_state, success = result[0], result[1]
                    if len(result) > 2:
                        execution_info["action_info"] = result[2]
                else:
                    new_state, success = result, True
            else:
                logger.error(f"Unknown action type in rule: {type(self.action)}")
                execution_info["error"] = f"Unknown action type: {type(self.action)}"
                return state, False, execution_info
        except Exception as e:
            logger.error(f"Error executing action for rule {self.name}: {str(e)}")
            execution_info["error"] = str(e)
            execution_info["success"] = False
            return state, False, execution_info
        
        # Update statistics
        end_time = time.time()
        execution_time = end_time - start_time
        self.last_execution_time = execution_time
        
        if success:
            self.success_count += 1
        
        # Finalize execution info
        execution_info.update({
            "success": success,
            "end_time": datetime.fromtimestamp(end_time).isoformat(),
            "execution_time": execution_time
        })
        
        return new_state, success, execution_info
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert rule to dictionary representation."""
        action_info = {"type": "unknown"}
        
        if isinstance(self.action, Action):
            action_info = {"type": "action", "id": self.action.id, "name": self.action.name}
        elif isinstance(self.action, ActionSequence):
            action_info = {"type": "sequence", "id": self.action.id, "name": self.action.name}
        elif isinstance(self.action, TaskSchema):
            action_info = {"type": "task", "id": self.action.id, "name": self.action.name}
        elif callable(self.action):
            action_info = {"type": "function", "name": self.action.__name__}
        
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "priority": self.priority,
            "action": action_info,
            "enabled": self.enabled,
            "trigger_count": self.trigger_count,
            "success_count": self.success_count,
            "last_triggered": self.last_triggered.isoformat() if self.last_triggered else None,
            "last_execution_time": self.last_execution_time
        }
    
    def __str__(self) -> str:
        return f"ConditionActionRule({self.name}, {self.id}, priority={self.priority})"
    
    def __repr__(self) -> str:
        return self.__str__()


class ProceduralKnowledgeBase:
    """
    Central repository for procedural knowledge, managing actions, sequences,
    tasks, skills, and rules.
    """
    
    def __init__(self, name: str = "Main Procedural KB"):
        """
        Initialize the procedural knowledge base.
        
        Args:
            name: Knowledge base name
        """
        self.name = name
        self.id = f"pkb_{str(uuid.uuid4())[:8]}"
        
        # Storage for different knowledge types
        self.actions = {}          # ActionID -> Action
        self.sequences = {}        # ActionID -> ActionSequence
        self.tasks = {}            # TaskID -> TaskSchema
        self.skill_hierarchies = {}  # SkillID -> SkillHierarchy
        self.rules = {}            # RuleID -> ConditionActionRule
        
        # Indices for efficient retrieval
        self.action_name_index = {}  # name -> ActionID
        self.sequence_name_index = {}  # name -> ActionID
        self.task_name_index = {}  # name -> TaskID
        self.rule_name_index = {}  # name -> RuleID
        
        # Domain-specific knowledge collections
        self.domain_actions = defaultdict(set)  # domain -> set of ActionIDs
        self.domain_tasks = defaultdict(set)  # domain -> set of TaskIDs
        
        # For learning and adaptation
        self.execution_history = deque(maxlen=1000)  # Limit history size
        
        logger.info(f"Procedural Knowledge Base initialized: {self.name} ({self.id})")
    
    def add_action(self, action: Action) -> ActionID:
        """
        Add an action to the knowledge base.
        
        Args:
            action: Action to add
            
        Returns:
            Action ID
        """
        if action.id in self.actions:
            logger.warning(f"Action ID {action.id} already exists, overwriting")
        
        self.actions[action.id] = action
        self.action_name_index[action.name] = action.id
        
        # Add to domain index if category defined
        if hasattr(action, "category") and isinstance(action.category, ActionCategory):
            domain = action.category.value
            self.domain_actions[domain].add(action.id)
        
        return action.id
    
    def add_sequence(self, sequence: ActionSequence) -> ActionID:
        """
        Add an action sequence to the knowledge base.
        
        Args:
            sequence: Action sequence to add
            
        Returns:
            Action ID
        """
        if sequence.id in self.sequences:
            logger.warning(f"Sequence ID {sequence.id} already exists, overwriting")
        
        self.sequences[sequence.id] = sequence
        self.sequence_name_index[sequence.name] = sequence.id
        
        return sequence.id
    
    def add_task(self, task: TaskSchema) -> TaskID:
        """
        Add a task schema to the knowledge base.
        
        Args:
            task: Task schema to add
            
        Returns:
            Task ID
        """
        if task.id in self.tasks:
            logger.warning(f"Task ID {task.id} already exists, overwriting")
        
        self.tasks[task.id] = task
        self.task_name_index[task.name] = task.id
        
        # Add to domain index
        self.domain_tasks[task.domain].add(task.id)
        
        return task.id
    
    def add_skill_hierarchy(self, hierarchy: SkillHierarchy) -> SkillID:
        """
        Add a skill hierarchy to the knowledge base.
        
        Args:
            hierarchy: Skill hierarchy to add
            
        Returns:
            Skill ID
        """
        if hierarchy.id in self.skill_hierarchies:
            logger.warning(f"Skill hierarchy ID {hierarchy.id} already exists, overwriting")
        
        self.skill_hierarchies[hierarchy.id] = hierarchy
        
        return hierarchy.id
    
    def add_rule(self, rule: ConditionActionRule) -> RuleID:
        """
        Add a condition-action rule to the knowledge base.
        
        Args:
            rule: Condition-action rule to add
            
        Returns:
            Rule ID
        """
        if rule.id in self.rules:
            logger.warning(f"Rule ID {rule.id} already exists, overwriting")
        
        self.rules[rule.id] = rule
        self.rule_name_index[rule.name] = rule.id
        
        return rule.id
    
    def get_action(self, action_id: ActionID) -> Action:
        """
        Get an action by ID.
        
        Args:
            action_id: Action ID
            
        Returns:
            Action
        """
        if action_id not in self.actions:
            raise ValueError(f"Action ID {action_id} not found")
        
        return self.actions[action_id]
    
    def get_action_by_name(self, name: str) -> Action:
        """
        Get an action by name.
        
        Args:
            name: Action name
            
        Returns:
            Action
        """
        if name not in self.action_name_index:
            raise ValueError(f"Action named '{name}' not found")
        
        action_id = self.action_name_index[name]
        return self.actions[action_id]
    
    def get_sequence(self, sequence_id: ActionID) -> ActionSequence:
        """
        Get an action sequence by ID.
        
        Args:
            sequence_id: Sequence ID
            
        Returns:
            Action sequence
        """
        if sequence_id not in self.sequences:
            raise ValueError(f"Sequence ID {sequence_id} not found")
        
        return self.sequences[sequence_id]
    
    def get_task(self, task_id: TaskID) -> TaskSchema:
        """
        Get a task schema by ID.
        
        Args:
            task_id: Task ID
            
        Returns:
            Task schema
        """
        if task_id not in self.tasks:
            raise ValueError(f"Task ID {task_id} not found")
        
        return self.tasks[task_id]
    
    def get_skill_hierarchy(self, hierarchy_id: SkillID) -> SkillHierarchy:
        """
        Get a skill hierarchy by ID.
        
        Args:
            hierarchy_id: Hierarchy ID
            
        Returns:
            Skill hierarchy
        """
        if hierarchy_id not in self.skill_hierarchies:
            raise ValueError(f"Skill hierarchy ID {hierarchy_id} not found")
        
        return self.skill_hierarchies[hierarchy_id]
    
    def get_rule(self, rule_id: RuleID) -> ConditionActionRule:
        """
        Get a condition-action rule by ID.
        
        Args:
            rule_id: Rule ID
            
        Returns:
            Condition-action rule
        """
        if rule_id not in self.rules:
            raise ValueError(f"Rule ID {rule_id} not found")
        
        return self.rules[rule_id]
    
    def search_actions(
        self,
        query: str = None,
        category: ActionCategory = None,
        parameters: List[str] = None,
        max_results: int = 10
    ) -> List[Action]:
        """
        Search for actions matching criteria.
        
        Args:
            query: Text query to match in name or description
            category: Action category filter
            parameters: Required parameters filter
            max_results: Maximum number of results
            
        Returns:
            List of matching actions
        """
        results = []
        
        for action in self.actions.values():
            # Apply filters
            if query and query.lower() not in action.name.lower() and query.lower() not in action.description.lower():
                continue
            
            if category and (not hasattr(action, "category") or action.category != category):
                continue
            
            if parameters:
                if not all(param in action.parameters for param in parameters):
                    continue
            
            results.append(action)
            
            if len(results) >= max_results:
                break
        
        return results
    
    def search_tasks(
        self,
        query: str = None,
        domain: str = None,
        complexity_range: Tuple[float, float] = None,
        max_results: int = 10
    ) -> List[TaskSchema]:
        """
        Search for tasks matching criteria.
        
        Args:
            query: Text query to match in name or description
            domain: Task domain filter
            complexity_range: Range of complexity values (min, max)
            max_results: Maximum number of results
            
        Returns:
            List of matching tasks
        """
        results = []
        
        for task in self.tasks.values():
            # Apply filters
            if query and query.lower() not in task.name.lower() and query.lower() not in task.description.lower():
                continue
            
            if domain and task.domain != domain:
                continue
            
            if complexity_range:
                min_complexity, max_complexity = complexity_range
                if task.estimated_complexity < min_complexity or task.estimated_complexity > max_complexity:
                    continue
            
            results.append(task)
            
            if len(results) >= max_results:
                break
        
        return results
    
    def get_domain_actions(self, domain: str) -> List[Action]:
        """
        Get all actions in a specific domain.
        
        Args:
            domain: Domain name
            
        Returns:
            List of actions in the domain
        """
        action_ids = self.domain_actions.get(domain, set())
        return [self.actions[action_id] for action_id in action_ids if action_id in self.actions]
    
    def get_domain_tasks(self, domain: str) -> List[TaskSchema]:
        """
        Get all tasks in a specific domain.
        
        Args:
            domain: Domain name
            
        Returns:
            List of tasks in the domain
        """
        task_ids = self.domain_tasks.get(domain, set())
        return [self.tasks[task_id] for task_id in task_ids if task_id in self.tasks]
    
    def evaluate_rules(self, state: StateType, context: Dict[str, Any] = None) -> List[Tuple[ConditionActionRule, float]]:
        """
        Evaluate all rules against the current state and return matching rules.
        
        Args:
            state: Current state
            context: Additional context information
            
        Returns:
            List of (rule, priority) tuples for rules whose conditions are met, sorted by priority
        """
        context = context or {}
        matching_rules = []
        
        for rule in self.rules.values():
            if rule.check_condition(state, context):
                matching_rules.append((rule, rule.priority))
        
        # Sort by priority (descending)
        matching_rules.sort(key=lambda x: x[1], reverse=True)
        
        return matching_rules
    
    def execute_highest_priority_rule(
        self,
        state: StateType,
        context: Dict[str, Any] = None
    ) -> Tuple[StateType, bool, Dict[str, Any]]:
        """
        Evaluate rules and execute the highest priority matching rule.
        
        Args:
            state: Current state
            context: Additional context information
            
        Returns:
            Tuple of (new_state, success, execution_info)
        """
        matching_rules = self.evaluate_rules(state, context)
        
        if not matching_rules:
            return state, False, {"error": "No matching rules found"}
        
        # Execute highest priority rule
        rule, priority = matching_rules[0]
        return rule.execute(state, context)
    
    def execute_all_matching_rules(
        self,
        state: StateType,
        context: Dict[str, Any] = None,
        max_executions: int = None
    ) -> Tuple[StateType, List[Dict[str, Any]]]:
        """
        Execute all matching rules in priority order.
        
        Args:
            state: Current state
            context: Additional context information
            max_executions: Maximum number of rules to execute
            
        Returns:
            Tuple of (final_state, execution_infos)
        """
        matching_rules = self.evaluate_rules(state, context)
        
        if not matching_rules:
            return state, []
        
        # Limit number of executions if specified
        if max_executions is not None:
            matching_rules = matching_rules[:max_executions]
        
        current_state = state.copy()
        execution_infos = []
        
        for rule, _ in matching_rules:
            new_state, success, info = rule.execute(current_state, context)
            
            execution_infos.append(info)
            
            if success:
                current_state = new_state
        
        return current_state, execution_infos
    
    def add_procedure(
        self,
        content: Union[Action, ActionSequence, TaskSchema, SkillHierarchy, ConditionActionRule],
        context: Dict[str, Any] = None
    ) -> str:
        """
        Add procedural knowledge to the knowledge base.
        Generic method that handles different types of procedural knowledge.
        
        Args:
            content: Procedural knowledge to add
            context: Additional context information
            
        Returns:
            ID of added knowledge
        """
        if isinstance(content, Action):
            return self.add_action(content)
        elif isinstance(content, ActionSequence):
            return self.add_sequence(content)
        elif isinstance(content, TaskSchema):
            return self.add_task(content)
        elif isinstance(content, SkillHierarchy):
            return self.add_skill_hierarchy(content)
        elif isinstance(content, ConditionActionRule):
            return self.add_rule(content)
        else:
            raise ValueError(f"Unsupported knowledge type: {type(content)}")
    
    def retrieve_procedure(
        self,
        query: Any,
        context: Dict[str, Any] = None
    ) -> Union[Action, ActionSequence, TaskSchema, SkillHierarchy, ConditionActionRule, List[Any]]:
        """
        Retrieve procedural knowledge based on query.
        
        Args:
            query: Query specification (ID, name, or search criteria)
            context: Additional context information
            
        Returns:
            Retrieved knowledge or list of matches
        """
        context = context or {}
        
        # If query is a simple ID or name string
        if isinstance(query, str):
            # Try exact ID match first
            if query in self.actions:
                return self.actions[query]
            if query in self.sequences:
                return self.sequences[query]
            if query in self.tasks:
                return self.tasks[query]
            if query in self.skill_hierarchies:
                return self.skill_hierarchies[query]
            if query in self.rules:
                return self.rules[query]
            
            # Try name indices
            if query in self.action_name_index:
                return self.actions[self.action_name_index[query]]
            if query in self.sequence_name_index:
                return self.sequences[self.sequence_name_index[query]]
            if query in self.task_name_index:
                return self.tasks[self.task_name_index[query]]
            if query in self.rule_name_index:
                return self.rules[self.rule_name_index[query]]
            
            # Fallback to search
            results = []
            results.extend(self.search_actions(query=query))
            results.extend(self.search_tasks(query=query))
            return results
        
        # If query is a dictionary with specific search criteria
        elif isinstance(query, dict):
            knowledge_type = query.get("type", "").lower()
            
            if knowledge_type == "action":
                return self.search_actions(
                    query=query.get("query"),
                    category=ActionCategory(query["category"]) if "category" in query else None,
                    parameters=query.get("parameters"),
                    max_results=query.get("max_results", 10)
                )
            
            elif knowledge_type == "task":
                return self.search_tasks(
                    query=query.get("query"),
                    domain=query.get("domain"),
                    complexity_range=query.get("complexity_range"),
                    max_results=query.get("max_results", 10)
                )
                
            elif knowledge_type == "domain":
                domain = query.get("domain", "")
                results = []
                results.extend(self.get_domain_actions(domain))
                results.extend(self.get_domain_tasks(domain))
                return results
        
        # Unsupported query type
        raise ValueError(f"Unsupported query type: {type(query)}")
    
    def update_procedure(
        self,
        identifier: str,
        content: Any,
        context: Dict[str, Any] = None
    ) -> bool:
        """
        Update existing procedural knowledge.
        
        Args:
            identifier: ID of knowledge to update
            content: New content or attributes to update
            context: Additional context information
            
        Returns:
            True if update was successful, False otherwise
        """
        context = context or {}
        
        # Determine where the identifier exists
        if identifier in self.actions:
            # For actions, content can be a new Action object or dict of attributes
            if isinstance(content, Action):
                content.id = identifier  # Preserve ID
                self.actions[identifier] = content
                return True
            elif isinstance(content, dict):
                # Update specific attributes
                action = self.actions[identifier]
                for key, value in content.items():
                    if hasattr(action, key):
                        setattr(action, key, value)
                return True
        
        elif identifier in self.sequences:
            # For sequences, content can be a new ActionSequence object or dict of attributes
            if isinstance(content, ActionSequence):
                content.id = identifier  # Preserve ID
                self.sequences[identifier] = content
                return True
            elif isinstance(content, dict):
                # Update specific attributes
                sequence = self.sequences[identifier]
                for key, value in content.items():
                    if hasattr(sequence, key):
                        setattr(sequence, key, value)
                return True
        
        elif identifier in self.tasks:
            # For tasks, content can be a new TaskSchema object or dict of attributes
            if isinstance(content, TaskSchema):
                content.id = identifier  # Preserve ID
                self.tasks[identifier] = content
                return True
            elif isinstance(content, dict):
                # Update specific attributes
                task = self.tasks[identifier]
                for key, value in content.items():
                    if hasattr(task, key):
                        setattr(task, key, value)
                return True
        
        elif identifier in self.skill_hierarchies:
            # For skill hierarchies, content must be a new SkillHierarchy object
            if isinstance(content, SkillHierarchy):
                content.id = identifier  # Preserve ID
                self.skill_hierarchies[identifier] = content
                return True
        
        elif identifier in self.rules:
            # For rules, content can be a new ConditionActionRule object or dict of attributes
            if isinstance(content, ConditionActionRule):
                content.id = identifier  # Preserve ID
                self.rules[identifier] = content
                return True
            elif isinstance(content, dict):
                # Update specific attributes
                rule = self.rules[identifier]
                for key, value in content.items():
                    if hasattr(rule, key):
                        setattr(rule, key, value)
                return True
        
        return False
    
    def delete_procedure(self, identifier: str) -> bool:
        """
        Delete procedural knowledge by ID.
        
        Args:
            identifier: ID of knowledge to delete
            
        Returns:
            True if deletion was successful, False otherwise
        """
        if identifier in self.actions:
            action = self.actions.pop(identifier)
            # Clean up indices
            if action.name in self.action_name_index and self.action_name_index[action.name] == identifier:
                del self.action_name_index[action.name]
            # Clean up domain index
            if hasattr(action, "category") and isinstance(action.category, ActionCategory):
                domain = action.category.value
                if domain in self.domain_actions and identifier in self.domain_actions[domain]:
                    self.domain_actions[domain].remove(identifier)
            return True
        
        elif identifier in self.sequences:
            sequence = self.sequences.pop(identifier)
            # Clean up indices
            if sequence.name in self.sequence_name_index and self.sequence_name_index[sequence.name] == identifier:
                del self.sequence_name_index[sequence.name]
            return True
        
        elif identifier in self.tasks:
            task = self.tasks.pop(identifier)
            # Clean up indices
            if task.name in self.task_name_index and self.task_name_index[task.name] == identifier:
                del self.task_name_index[task.name]
            # Clean up domain index
            if task.domain in self.domain_tasks and identifier in self.domain_tasks[task.domain]:
                self.domain_tasks[task.domain].remove(identifier)
            return True
        
        elif identifier in self.skill_hierarchies:
            self.skill_hierarchies.pop(identifier)
            return True
        
        elif identifier in self.rules:
            rule = self.rules.pop(identifier)
            # Clean up indices
            if rule.name in self.rule_name_index and self.rule_name_index[rule.name] == identifier:
                del self.rule_name_index[rule.name]
            return True
        
        return False
    
    def load_from_file(self, file_path: str) -> int:
        """
        Load procedural knowledge from a JSON file.
        
        Args:
            file_path: Path to JSON file
            
        Returns:
            Number of loaded items
        """
        try:
            with open(file_path, 'r') as f:
                data = json.load(f)
            
            count = 0
            
            # Load actions
            for action_data in data.get("actions", []):
                try:
                    action = Action(
                        name=action_data["name"],
                        description=action_data.get("description", ""),
                        category=ActionCategory(action_data.get("category", ActionCategory.COGNITIVE.value)),
                        parameters=action_data.get("parameters", []),
                        duration=action_data.get("duration"),
                        cost=action_data.get("cost", 0.0),
                        reliability=action_data.get("reliability", 1.0)
                    )
                    
                    # Restore ID and metadata
                    action.id = action_data.get("id", action.id)
                    action.execution_count = action_data.get("execution_count", 0)
                    action.success_count = action_data.get("success_count", 0)
                    action.avg_execution_time = action_data.get("avg_execution_time", 0.0)
                    action.last_execution_time = action_data.get("last_execution_time")
                    
                    if action_data.get("last_execution_status"):
                        action.last_execution_status = ActionStatus(action_data["last_execution_status"])
                    
                    self.add_action(action)
                    count += 1
                except Exception as e:
                    logger.error(f"Error loading action: {str(e)}")
            
            # Load tasks
            for task_data in data.get("tasks", []):
                try:
                    task = TaskSchema(
                        name=task_data["name"],
                        description=task_data.get("description", ""),
                        parameters=task_data.get("parameters", []),
                        domain=task_data.get("domain", "general"),
                        estimated_complexity=task_data.get("estimated_complexity", 0.0)
                    )
                    
                    # Restore ID and metadata
                    task.id = task_data.get("id", task.id)
                    task.execution_count = task_data.get("execution_count", 0)
                    task.success_count = task_data.get("success_count", 0)
                    task.avg_execution_time = task_data.get("avg_execution_time", 0.0)
                    
                    self.add_task(task)
                    count += 1
                except Exception as e:
                    logger.error(f"Error loading task: {str(e)}")
            
            logger.info(f"Loaded {count} procedural knowledge items from {file_path}")
            return count
        
        except Exception as e:
            logger.error(f"Error loading procedural knowledge from {file_path}: {str(e)}")
            return 0
    
    def save_to_file(self, file_path: str) -> int:
        """
        Save procedural knowledge to a JSON file.
        
        Args:
            file_path: Path to JSON file
            
        Returns:
            Number of saved items
        """
        try:
            data = {
                "actions": [action.to_dict() for action in self.actions.values()],
                "action_sequences": [seq.to_dict() for seq in self.sequences.values()],
                "tasks": [task.to_dict() for task in self.tasks.values()],
                "skill_hierarchies": [hier.to_dict() for hier in self.skill_hierarchies.values()],
                "rules": [rule.to_dict() for rule in self.rules.values()]
            }
            
            with open(file_path, 'w') as f:
                json.dump(data, f, indent=2)
            
            total_items = (len(self.actions) + len(self.sequences) + len(self.tasks) + 
                          len(self.skill_hierarchies) + len(self.rules))
            
            logger.info(f"Saved {total_items} procedural knowledge items to {file_path}")
            return total_items
        
        except Exception as e:
            logger.error(f"Error saving procedural knowledge to {file_path}: {str(e)}")
            return 0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert knowledge base to dictionary representation."""
        return {
            "id": self.id,
            "name": self.name,
            "action_count": len(self.actions),
            "sequence_count": len(self.sequences),
            "task_count": len(self.tasks),
            "skill_hierarchy_count": len(self.skill_hierarchies),
            "rule_count": len(self.rules),
            "domains": list(set(list(self.domain_actions.keys()) + list(self.domain_tasks.keys())))
        }
    
    def __str__(self) -> str:
        return (f"ProceduralKnowledgeBase({self.name}, {len(self.actions)} actions, "
                f"{len(self.tasks)} tasks, {len(self.rules)} rules)")
    
    def __repr__(self) -> str:
        return self.__str__()


# Helper functions for creating common action patterns

def create_composite_action(
    name: str,
    component_actions: List[Union[Action, Dict[str, Any]]],
    description: str = "",
    parallel: bool = False
) -> ActionSequence:
    """
    Create a composite action from component actions.
    
    Args:
        name: Name for the composite action
        component_actions: List of actions or (action, parameters) dicts
        description: Description for the composite action
        parallel: Whether actions can be executed in parallel
        
    Returns:
        Action sequence
    """
    sequence = ActionSequence(name=name, description=description)
    
    for component in component_actions:
        if isinstance(component, Action):
            sequence.add_action(component)
        elif isinstance(component, dict) and "action" in component:
            action = component["action"]
            parameters = component.get("parameters", {})
            sequence.add_action(ActionInstance(action=action, parameters=parameters))
    
    return sequence


def create_conditional_action(
    name: str,
    condition: Callable[[StateType, Dict[str, Any]], bool],
    then_action: Union[Action, ActionSequence],
    else_action: Optional[Union[Action, ActionSequence]] = None,
    description: str = ""
) -> Callable[[StateType, Dict[str, Any], Dict[str, Any]], Tuple[StateType, bool, Dict[str, Any]]]:
    """
    Create a conditional action that executes different actions based on a condition.
    
    Args:
        name: Name for the conditional action
        condition: Function that evaluates the condition
        then_action: Action to execute if condition is true
        else_action: Action to execute if condition is false (optional)
        description: Description for the conditional action
        
    Returns:
        Function implementing the conditional action
    """
    def conditional_executor(
        state: StateType,
        parameters: Dict[str, Any],
        context: Dict[str, Any]
    ) -> Tuple[StateType, bool, Dict[str, Any]]:
        execution_info = {
            "name": name,
            "description": description,
            "start_time": datetime.now().isoformat()
        }
        
        try:
            condition_result = condition(state, parameters)
            execution_info["condition_result"] = condition_result
            
            if condition_result:
                if isinstance(then_action, Action):
                    new_state, status, info = then_action.execute(state, parameters)
                    success = status == ActionStatus.COMPLETED
                else:  # ActionSequence
                    new_state, status, info = then_action.execute(state)
                    success = status == ActionStatus.COMPLETED
                
                execution_info["branch"] = "then"
                execution_info["action_info"] = info
            elif else_action is not None:
                if isinstance(else_action, Action):
                    new_state, status, info = else_action.execute(state, parameters)
                    success = status == ActionStatus.COMPLETED
                else:  # ActionSequence
                    new_state, status, info = else_action.execute(state)
                    success = status == ActionStatus.COMPLETED
                
                execution_info["branch"] = "else"
                execution_info["action_info"] = info
            else:
                # No else action, just return original state
                new_state = state
                success = True
                execution_info["branch"] = "none"
            
            execution_info["success"] = success
            execution_info["end_time"] = datetime.now().isoformat()
            
            return new_state, success, execution_info
        
        except Exception as e:
            logger.error(f"Error executing conditional action {name}: {str(e)}")
            execution_info["error"] = str(e)
            execution_info["success"] = False
            execution_info["end_time"] = datetime.now().isoformat()
            
            return state, False, execution_info
    
    return conditional_executor


def create_iterative_action(
    name: str,
    action: Union[Action, ActionSequence],
    iteration_condition: Callable[[StateType, Dict[str, Any], int], bool],
    max_iterations: int = 100,
    description: str = ""
) -> Callable[[StateType, Dict[str, Any], Dict[str, Any]], Tuple[StateType, bool, Dict[str, Any]]]:
    """
    Create an action that iteratively executes another action while a condition holds.
    
    Args:
        name: Name for the iterative action
        action: Action to execute repeatedly
        iteration_condition: Function that determines whether to continue iterating
        max_iterations: Maximum number of iterations
        description: Description for the iterative action
        
    Returns:
        Function implementing the iterative action
    """
    def iterative_executor(
        state: StateType,
        parameters: Dict[str, Any],
        context: Dict[str, Any]
    ) -> Tuple[StateType, bool, Dict[str, Any]]:
        execution_info = {
            "name": name,
            "description": description,
            "start_time": datetime.now().isoformat(),
            "iterations": []
        }
        
        try:
            current_state = state.copy()
            iteration = 0
            
            while (iteration < max_iterations and 
                  iteration_condition(current_state, parameters, iteration)):
                
                iteration_start = time.time()
                
                if isinstance(action, Action):
                    new_state, status, info = action.execute(current_state, parameters)
                    success = status == ActionStatus.COMPLETED
                else:  # ActionSequence
                    new_state, status, info = action.execute(current_state)
                    success = status == ActionStatus.COMPLETED
                
                iteration_end = time.time()
                
                # Record iteration result
                iteration_info = {
                    "iteration": iteration,
                    "success": success,
                    "execution_time": iteration_end - iteration_start,
                    "action_info": info
                }
                execution_info["iterations"].append(iteration_info)
                
                if not success:
                    execution_info["success"] = False
                    execution_info["end_time"] = datetime.now().isoformat()
                    execution_info["iteration_count"] = iteration + 1
                    
                    return current_state, False, execution_info
                
                current_state = new_state
                iteration += 1
            
            execution_info["success"] = True
            execution_info["end_time"] = datetime.now().isoformat()
            execution_info["iteration_count"] = iteration
            
            return current_state, True, execution_info
        
        except Exception as e:
            logger.error(f"Error executing iterative action {name}: {str(e)}")
            execution_info["error"] = str(e)
            execution_info["success"] = False
            execution_info["end_time"] = datetime.now().isoformat()
            
            return state, False, execution_info
    
    return iterative_executor


def create_transactional_action(
    name: str,
    actions: List[Union[Action, ActionSequence]],
    description: str = ""
) -> Callable[[StateType, Dict[str, Any], Dict[str, Any]], Tuple[StateType, bool, Dict[str, Any]]]:
    """
    Create a transactional action that succeeds only if all component actions succeed,
    otherwise rolls back to the original state.
    
    Args:
        name: Name for the transactional action
        actions: List of actions to execute
        description: Description for the transactional action
        
    Returns:
        Function implementing the transactional action
    """
    def transactional_executor(
        state: StateType,
        parameters: Dict[str, Any],
        context: Dict[str, Any]
    ) -> Tuple[StateType, bool, Dict[str, Any]]:
        execution_info = {
            "name": name,
            "description": description,
            "start_time": datetime.now().isoformat(),
            "action_results": []
        }
        
        try:
            original_state = deepcopy(state)
            current_state = state.copy()
            
            for i, action in enumerate(actions):
                action_start = time.time()
                
                if isinstance(action, Action):
                    new_state, status, info = action.execute(current_state, parameters)
                    success = status == ActionStatus.COMPLETED
                else:  # ActionSequence
                    new_state, status, info = action.execute(current_state)
                    success = status == ActionStatus.COMPLETED
                
                action_end = time.time()
                
                # Record action result
                action_info = {
                    "action_index": i,
                    "success": success,
                    "execution_time": action_end - action_start,
                    "action_info": info
                }
                execution_info["action_results"].append(action_info)
                
                if not success:
                    # Transaction failed, rollback
                    execution_info["success"] = False
                    execution_info["rolled_back"] = True
                    execution_info["end_time"] = datetime.now().isoformat()
                    
                    return original_state, False, execution_info
                
                current_state = new_state
            
            # All actions succeeded
            execution_info["success"] = True
            execution_info["end_time"] = datetime.now().isoformat()
            
            return current_state, True, execution_info
        
        except Exception as e:
            logger.error(f"Error executing transactional action {name}: {str(e)}")
            execution_info["error"] = str(e)
            execution_info["success"] = False
            execution_info["rolled_back"] = True
            execution_info["end_time"] = datetime.now().isoformat()
            
            return original_state, False, execution_info
    
    return transactional_executor