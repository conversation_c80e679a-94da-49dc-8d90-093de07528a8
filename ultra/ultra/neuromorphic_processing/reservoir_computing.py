#!/usr/bin/env python3
"""
ULTRA: Neuromorphic Processing Layer - Reservoir Computing

This module implements reservoir computing approaches, which utilize fixed 
recurrent neural networks (the "reservoir") with trained readout layers.
Key advantages include simplified training (only output weights are trained)
while maintaining the powerful computational capabilities of recurrent networks
for processing temporal information.

Implemented approaches:
1. Echo State Networks (ESN) - Rate-based reservoirs
2. Liquid State Machines (LSM) - Spiking-based reservoirs
3. FORCE Learning - Real-time recurrent learning method
4. Reservoir topology optimization and analysis tools

This implementation follows the mathematical formulations from the ULTRA paper,
particularly the sections on reservoir computing and dynamical systems.
"""

import numpy as np
import scipy.sparse as sparse
import scipy.linalg as linalg
from scipy import signal
from scipy.stats import uniform
from typing import Dict, List, Tuple, Set, Any, Optional, Union, Callable, Type
import logging
import time
import matplotlib.pyplot as plt
from dataclasses import dataclass
from abc import ABC, abstractmethod
from enum import Enum, auto
import pickle
from tqdm import tqdm
import networkx as nx

# Import components from spiking_networks for LSM implementation
try:
    from ultra.spiking_networks import SpikingNeuron, LIFNeuron, SpikingNetwork
except ImportError:
    # If running as standalone module
    logging.warning("Could not import spiking_networks module. Some functionality may be limited.")
    SpikingNeuron, LIFNeuron, SpikingNetwork = None, None, None

logger = logging.getLogger(__name__)


class ReservoirType(Enum):
    """Types of reservoir computing implementations."""
    ESN = auto()           # Echo State Network
    LSM = auto()           # Liquid State Machine
    FORCE = auto()         # First-Order Reduced and Controlled Error
    HYBRID = auto()        # Hybrid reservoir (multiple types combined)
    PHYSICAL = auto()      # Physical reservoir (for hardware implementations)
    CUSTOM = auto()        # Custom reservoir type


class ReservoirTopology(Enum):
    """Network topology types for the reservoir."""
    RANDOM = auto()         # Random connectivity
    SMALL_WORLD = auto()    # Small-world network
    SCALE_FREE = auto()     # Scale-free network
    MODULAR = auto()        # Modular/hierarchical organization
    FEEDFORWARD = auto()    # Feedforward chains within reservoir
    CLUSTERED = auto()      # Clustered organization
    SPARSE = auto()         # Sparse random connectivity
    CUSTOM = auto()         # Custom topology


class ReservoirInitializationMethod(Enum):
    """Initialization methods for reservoir parameters."""
    RANDOM_UNIFORM = auto()       # Uniform random initialization
    RANDOM_NORMAL = auto()        # Normal random initialization
    ORTHOGONAL = auto()           # Orthogonal matrices
    SPECTRAL_NORMALIZED = auto()  # Spectral radius normalized random
    SPARSE_RANDOM = auto()        # Sparse random connectivity
    CONNECTIVITY_PATTERNED = auto() # Pattern-based connectivity
    PRETRAINED = auto()           # Load from pretrained model


class ActivationFunction(Enum):
    """Activation functions for reservoir nodes."""
    TANH = auto()          # Hyperbolic tangent
    SIGMOID = auto()       # Sigmoid function
    RELU = auto()          # Rectified Linear Unit
    LEAKY_RELU = auto()    # Leaky ReLU
    ELU = auto()           # Exponential Linear Unit
    LINEAR = auto()        # Linear (identity) function
    SIN = auto()           # Sine function
    CUSTOM = auto()        # Custom activation function


class RegularizationMethod(Enum):
    """Regularization methods for training."""
    NONE = auto()           # No regularization
    RIDGE = auto()          # Ridge regression (L2)
    LASSO = auto()          # Lasso regression (L1)
    ELASTIC_NET = auto()    # Elastic Net (L1 + L2)
    TIKHONOV = auto()       # Tikhonov regularization
    DROPOUT = auto()        # Dropout
    NOISE_INJECTION = auto() # Noise injection
    TEACHER_FORCING = auto() # Teacher forcing regularization


class ReadoutType(Enum):
    """Types of readout mechanisms."""
    LINEAR = auto()         # Linear readout
    MLP = auto()            # Multi-layer Perceptron
    RIDGE_REGRESSION = auto() # Ridge regression
    FORCE = auto()          # FORCE learning
    RECURSIVE_LEAST_SQUARES = auto() # RLS
    CUSTOM = auto()         # Custom readout


@dataclass
class ReservoirConfig:
    """Configuration parameters for a reservoir."""
    reservoir_type: ReservoirType = ReservoirType.ESN
    input_size: int = 1
    reservoir_size: int = 100
    output_size: int = 1
    spectral_radius: float = 0.95
    input_scaling: float = 1.0
    bias_scaling: float = 0.0
    leakage_rate: float = 1.0
    sparsity: float = 0.1
    activation_function: ActivationFunction = ActivationFunction.TANH
    topology: ReservoirTopology = ReservoirTopology.RANDOM
    noise_level: float = 0.0
    readout_type: ReadoutType = ReadoutType.LINEAR
    regularization: RegularizationMethod = RegularizationMethod.RIDGE
    regularization_strength: float = 1e-6
    random_seed: Optional[int] = None
    initialization_method: ReservoirInitializationMethod = ReservoirInitializationMethod.SPECTRAL_NORMALIZED
    washout_length: int = 100
    
    def __post_init__(self):
        """Validate configuration parameters."""
        if self.reservoir_size <= 0:
            raise ValueError("Reservoir size must be positive")
        if self.input_size <= 0:
            raise ValueError("Input size must be positive")
        if self.output_size <= 0:
            raise ValueError("Output size must be positive")
        if self.spectral_radius <= 0:
            raise ValueError("Spectral radius must be positive")
        if self.sparsity < 0 or self.sparsity > 1:
            raise ValueError("Sparsity must be in [0, 1]")
        if self.leakage_rate <= 0 or self.leakage_rate > 1:
            raise ValueError("Leakage rate must be in (0, 1]")


class ReservoirNode:
    """
    Base class for nodes in a reservoir.
    
    Implements the node dynamics and state update for various 
    reservoir computing approaches.
    """
    
    def __init__(self, 
                 node_id: int,
                 activation: ActivationFunction = ActivationFunction.TANH,
                 bias: float = 0.0,
                 leak_rate: float = 1.0):
        """
        Initialize the reservoir node.
        
        Args:
            node_id: Unique identifier for the node
            activation: Activation function type
            bias: Bias term
            leak_rate: Leakage rate for leaky integration
        """
        self.node_id = node_id
        self.activation_type = activation
        self.bias = bias
        self.leak_rate = leak_rate
        self.state = 0.0
        self.previous_state = 0.0
        
        # Set up activation function
        if activation == ActivationFunction.TANH:
            self.activation_func = np.tanh
        elif activation == ActivationFunction.SIGMOID:
            self.activation_func = lambda x: 1.0 / (1.0 + np.exp(-x))
        elif activation == ActivationFunction.RELU:
            self.activation_func = lambda x: max(0.0, x)
        elif activation == ActivationFunction.LEAKY_RELU:
            self.activation_func = lambda x: x if x > 0 else 0.01 * x
        elif activation == ActivationFunction.ELU:
            self.activation_func = lambda x: x if x > 0 else 0.01 * (np.exp(x) - 1)
        elif activation == ActivationFunction.LINEAR:
            self.activation_func = lambda x: x
        elif activation == ActivationFunction.SIN:
            self.activation_func = np.sin
        else:
            # Default to tanh
            self.activation_func = np.tanh
            
        # State history for analysis
        self.state_history = []
        
    def update(self, input_value: float) -> float:
        """
        Update node state based on input value.
        
        Args:
            input_value: Combined input to this node
            
        Returns:
            New node state after activation
        """
        # Store previous state
        self.previous_state = self.state
        
        # Apply leaky integration
        if self.leak_rate == 1.0:
            # No leakage
            self.state = self.activation_func(input_value + self.bias)
        else:
            # Leaky integration (ESN update equation)
            self.state = (1 - self.leak_rate) * self.state + \
                         self.leak_rate * self.activation_func(input_value + self.bias)
        
        # Record state history
        self.state_history.append(self.state)
        
        return self.state
    
    def reset(self) -> None:
        """Reset node state."""
        self.state = 0.0
        self.previous_state = 0.0
        self.state_history = []


class ReservoirNetwork(ABC):
    """
    Abstract base class for reservoir computing networks.
    
    Defines the interface and common functionality for all
    reservoir computing implementations.
    """
    
    def __init__(self, config: ReservoirConfig):
        """
        Initialize the reservoir network.
        
        Args:
            config: Configuration parameters for the reservoir
        """
        self.config = config
        self.initialized = False
        
        # Set random seed if provided
        if config.random_seed is not None:
            np.random.seed(config.random_seed)
        
        # Initialize weights
        self.W_in = None   # Input weights
        self.W = None      # Reservoir weights
        self.W_out = None  # Output weights
        self.bias = None   # Bias terms
        
        # Initialize state
        self.reservoir_state = None
        
        # Performance metrics
        self.metrics = {}
        
        # Training data
        self.training_data = {}
        
    @abstractmethod
    def initialize_weights(self) -> None:
        """Initialize network weights according to the configuration."""
        pass
    
    @abstractmethod
    def update_state(self, input_data: np.ndarray) -> np.ndarray:
        """
        Update reservoir state based on input data.
        
        Args:
            input_data: Input data array
            
        Returns:
            New reservoir state
        """
        pass
    
    @abstractmethod
    def train(self, inputs: np.ndarray, targets: np.ndarray) -> None:
        """
        Train the output weights for the reservoir.
        
        Args:
            inputs: Input data array
            targets: Target data array
        """
        pass
    
    @abstractmethod
    def predict(self, inputs: np.ndarray) -> np.ndarray:
        """
        Generate predictions for the given inputs.
        
        Args:
            inputs: Input data array
            
        Returns:
            Predicted outputs
        """
        pass
    
    def compute_spectral_radius(self, matrix: np.ndarray) -> float:
        """
        Compute the spectral radius of a matrix.
        
        Args:
            matrix: Input matrix
            
        Returns:
            Spectral radius (largest absolute eigenvalue)
        """
        try:
            if sparse.issparse(matrix):
                # For large sparse matrices, use power iteration method
                return sparse.linalg.eigs(matrix, k=1, return_eigenvectors=False)[0]
            else:
                # For dense matrices, use standard eigenvalue computation
                eigenvalues = np.linalg.eigvals(matrix)
                return np.max(np.abs(eigenvalues))
        except np.linalg.LinAlgError:
            # Fallback to a more robust method if the eigenvalue computation fails
            logging.warning("Eigenvalue computation failed, using power iteration method")
            n = matrix.shape[0]
            v = np.random.rand(n)
            for _ in range(100):  # Number of iterations
                v_new = matrix @ v
                v_new_norm = np.linalg.norm(v_new)
                if v_new_norm < 1e-10:
                    return 0.0
                v = v_new / v_new_norm
            return np.linalg.norm(matrix @ v) / np.linalg.norm(v)
    
    def scale_matrix_to_radius(self, matrix: np.ndarray, target_radius: float) -> np.ndarray:
        """
        Scale a matrix to have a specific spectral radius.
        
        Args:
            matrix: Input matrix
            target_radius: Target spectral radius
            
        Returns:
            Scaled matrix
        """
        current_radius = self.compute_spectral_radius(matrix)
        if current_radius < 1e-10:
            # Avoid division by zero
            logging.warning("Current spectral radius is near zero, cannot scale properly")
            return matrix
        
        # Scale matrix
        return matrix * (target_radius / current_radius)
    
    def reset_state(self) -> None:
        """Reset the reservoir state."""
        self.reservoir_state = np.zeros(self.config.reservoir_size)
    
    def get_state(self) -> np.ndarray:
        """
        Get the current reservoir state.
        
        Returns:
            Current state vector
        """
        return self.reservoir_state
    
    def save_model(self, filepath: str) -> None:
        """
        Save the trained model to a file.
        
        Args:
            filepath: Path to save the model
        """
        model_data = {
            'config': self.config,
            'W_in': self.W_in,
            'W': self.W,
            'W_out': self.W_out,
            'bias': self.bias,
            'metrics': self.metrics
        }
        
        with open(filepath, 'wb') as f:
            pickle.dump(model_data, f)
        
        logging.info(f"Model saved to {filepath}")
    
    @classmethod
    def load_model(cls, filepath: str) -> 'ReservoirNetwork':
        """
        Load a trained model from a file.
        
        Args:
            filepath: Path to load the model from
            
        Returns:
            Loaded reservoir network
        """
        with open(filepath, 'rb') as f:
            model_data = pickle.load(f)
        
        # Create a new instance with the loaded configuration
        instance = cls(model_data['config'])
        
        # Set the loaded weights
        instance.W_in = model_data['W_in']
        instance.W = model_data['W']
        instance.W_out = model_data['W_out']
        instance.bias = model_data['bias']
        instance.metrics = model_data.get('metrics', {})
        instance.initialized = True
        
        logging.info(f"Model loaded from {filepath}")
        
        return instance
    
    def compute_metrics(self, predictions: np.ndarray, targets: np.ndarray) -> Dict[str, float]:
        """
        Compute performance metrics.
        
        Args:
            predictions: Predicted values
            targets: Target values
            
        Returns:
            Dictionary of performance metrics
        """
        # Ensure both arrays have the same shape
        if predictions.shape != targets.shape:
            raise ValueError(f"Predictions and targets must have the same shape. "
                           f"Got {predictions.shape} and {targets.shape}")
        
        # Mean squared error
        mse = np.mean((predictions - targets) ** 2)
        
        # Root mean squared error
        rmse = np.sqrt(mse)
        
        # Normalized root mean squared error
        if np.std(targets) > 1e-8:
            nrmse = rmse / np.std(targets)
        else:
            nrmse = float('inf')
        
        # Mean absolute error
        mae = np.mean(np.abs(predictions - targets))
        
        # Coefficient of determination (R²)
        ss_total = np.sum((targets - np.mean(targets, axis=0)) ** 2, axis=0)
        ss_residual = np.sum((targets - predictions) ** 2, axis=0)
        
        if np.all(ss_total > 1e-8):
            r2 = 1 - np.sum(ss_residual) / np.sum(ss_total)
        else:
            r2 = float('nan')
        
        # Store metrics
        metrics = {
            'mse': float(mse),
            'rmse': float(rmse),
            'nrmse': float(nrmse),
            'mae': float(mae),
            'r2': float(r2)
        }
        
        self.metrics.update(metrics)
        
        return metrics
    
    def visualize_reservoir_states(self, 
                                   inputs: Optional[np.ndarray] = None,
                                   pca_components: int = 3,
                                   title: str = 'Reservoir States') -> None:
        """
        Visualize reservoir states using PCA.
        
        Args:
            inputs: Optional input data to generate states
            pca_components: Number of PCA components to visualize
            title: Plot title
        """
        if inputs is not None:
            # Generate states for the given inputs
            states = self.collect_states(inputs)
        elif hasattr(self, 'training_data') and 'states' in self.training_data:
            # Use states from training data
            states = self.training_data['states']
        else:
            logging.warning("No states available for visualization")
            return
        
        # Apply PCA
        from sklearn.decomposition import PCA
        
        # Ensure we don't try to extract more components than possible
        n_components = min(pca_components, states.shape[0], states.shape[1])
        
        pca = PCA(n_components=n_components)
        states_pca = pca.fit_transform(states)
        
        # Create plot
        if n_components >= 3:
            # 3D plot
            fig = plt.figure(figsize=(10, 8))
            ax = fig.add_subplot(111, projection='3d')
            
            # Color points by time
            colors = plt.cm.viridis(np.linspace(0, 1, states.shape[0]))
            
            ax.scatter(states_pca[:, 0], states_pca[:, 1], states_pca[:, 2], 
                     c=colors, alpha=0.7)
            
            ax.set_xlabel(f'PC1 ({pca.explained_variance_ratio_[0]:.2%})')
            ax.set_ylabel(f'PC2 ({pca.explained_variance_ratio_[1]:.2%})')
            ax.set_zlabel(f'PC3 ({pca.explained_variance_ratio_[2]:.2%})')
            
        elif n_components >= 2:
            # 2D plot
            plt.figure(figsize=(10, 8))
            
            # Color points by time
            colors = plt.cm.viridis(np.linspace(0, 1, states.shape[0]))
            
            plt.scatter(states_pca[:, 0], states_pca[:, 1], c=colors, alpha=0.7)
            
            plt.xlabel(f'PC1 ({pca.explained_variance_ratio_[0]:.2%})')
            plt.ylabel(f'PC2 ({pca.explained_variance_ratio_[1]:.2%})')
            
        else:
            # 1D plot
            plt.figure(figsize=(10, 6))
            
            # Color points by time
            colors = plt.cm.viridis(np.linspace(0, 1, states.shape[0]))
            
            plt.scatter(states_pca[:, 0], np.zeros_like(states_pca[:, 0]), 
                      c=colors, alpha=0.7)
            
            plt.xlabel(f'PC1 ({pca.explained_variance_ratio_[0]:.2%})')
            plt.ylabel('Dimension 2 (N/A)')
        
        plt.title(f'{title}\nVariance Explained: {sum(pca.explained_variance_ratio_):.2%}')
        plt.tight_layout()
        plt.show()
    
    def visualize_weights(self) -> None:
        """Visualize the weight matrices of the reservoir."""
        # Create subplots
        fig, axs = plt.subplots(1, 3, figsize=(18, 5))
        
        # Input weights
        if self.W_in is not None:
            im0 = axs[0].imshow(self.W_in, cmap='viridis', aspect='auto')
            axs[0].set_title('Input Weights')
            axs[0].set_xlabel('Input Units')
            axs[0].set_ylabel('Reservoir Units')
            plt.colorbar(im0, ax=axs[0])
        
        # Reservoir weights
        if self.W is not None:
            im1 = axs[1].imshow(self.W, cmap='coolwarm', aspect='auto')
            axs[1].set_title(f'Reservoir Weights\nSpectral Radius: {self.compute_spectral_radius(self.W):.3f}')
            axs[1].set_xlabel('Reservoir Units')
            axs[1].set_ylabel('Reservoir Units')
            plt.colorbar(im1, ax=axs[1])
        
        # Output weights
        if self.W_out is not None:
            im2 = axs[2].imshow(self.W_out, cmap='viridis', aspect='auto')
            axs[2].set_title('Output Weights')
            axs[2].set_xlabel('Reservoir Units')
            axs[2].set_ylabel('Output Units')
            plt.colorbar(im2, ax=axs[2])
        
        plt.tight_layout()
        plt.show()


class ESN(ReservoirNetwork):
    """
    Echo State Network implementation.
    
    Standard rate-based reservoir computing approach using the
    echo state property for RNN training.
    """
    
    def __init__(self, config: ReservoirConfig):
        """
        Initialize Echo State Network.
        
        Args:
            config: Configuration parameters for the ESN
        """
        super().__init__(config)
        
        # Initialize weights and state
        self.initialize_weights()
        self.reset_state()
    
    def initialize_weights(self) -> None:
        """Initialize network weights according to configuration."""
        # Get configuration parameters
        N_in = self.config.input_size
        N = self.config.reservoir_size
        N_out = self.config.output_size
        
        # Determine initialization method
        if self.config.initialization_method == ReservoirInitializationMethod.RANDOM_UNIFORM:
            # Random uniform initialization
            self.W_in = np.random.uniform(-self.config.input_scaling, 
                                        self.config.input_scaling, 
                                        size=(N, N_in))
            self.W = np.random.uniform(-1, 1, size=(N, N))
            self.bias = np.random.uniform(-self.config.bias_scaling, 
                                        self.config.bias_scaling, 
                                        size=(N, 1))
                                        
        elif self.config.initialization_method == ReservoirInitializationMethod.RANDOM_NORMAL:
            # Random normal initialization
            self.W_in = np.random.randn(N, N_in) * self.config.input_scaling
            self.W = np.random.randn(N, N)
            self.bias = np.random.randn(N, 1) * self.config.bias_scaling
            
        elif self.config.initialization_method == ReservoirInitializationMethod.SPARSE_RANDOM:
            # Sparse random connectivity
            self.W_in = np.random.uniform(-self.config.input_scaling, 
                                        self.config.input_scaling, 
                                        size=(N, N_in))
            
            # Sparse reservoir weight matrix
            self.W = np.zeros((N, N))
            # Generate random indices for connections
            n_connections = int(self.config.sparsity * N * N)
            i_indices = np.random.randint(0, N, size=n_connections)
            j_indices = np.random.randint(0, N, size=n_connections)
            values = np.random.uniform(-1, 1, size=n_connections)
            
            # Set the connections
            for i, j, val in zip(i_indices, j_indices, values):
                self.W[i, j] = val
                
            self.bias = np.random.uniform(-self.config.bias_scaling, 
                                        self.config.bias_scaling, 
                                        size=(N, 1))
                                        
        elif self.config.initialization_method == ReservoirInitializationMethod.CONNECTIVITY_PATTERNED:
            # Create patterned connectivity based on topology
            self.W_in = np.random.uniform(-self.config.input_scaling, 
                                        self.config.input_scaling, 
                                        size=(N, N_in))
            
            # Create reservoir weight matrix based on topology
            if self.config.topology == ReservoirTopology.SMALL_WORLD:
                # Create small-world network
                from scipy.sparse import csr_matrix
                
                # Create small-world graph
                G = nx.watts_strogatz_graph(N, k=int(self.config.sparsity * N), p=0.1)
                
                # Convert to adjacency matrix
                adj_matrix = nx.to_scipy_sparse_array(G)
                
                # Convert to dense and assign random weights where connections exist
                self.W = adj_matrix.toarray()
                
                # Assign random weights to existing connections
                mask = self.W > 0
                self.W[mask] = np.random.uniform(-1, 1, size=np.sum(mask))
                
            elif self.config.topology == ReservoirTopology.SCALE_FREE:
                # Create scale-free network (Barabási–Albert model)
                from scipy.sparse import csr_matrix
                
                # Create scale-free graph
                m = max(1, int(self.config.sparsity * N / 2))
                G = nx.barabasi_albert_graph(N, m=m)
                
                # Convert to adjacency matrix
                adj_matrix = nx.to_scipy_sparse_array(G)
                
                # Convert to dense and assign random weights where connections exist
                self.W = adj_matrix.toarray()
                
                # Assign random weights to existing connections
                mask = self.W > 0
                self.W[mask] = np.random.uniform(-1, 1, size=np.sum(mask))
                
            elif self.config.topology == ReservoirTopology.MODULAR:
                # Create modular network
                num_modules = 5  # Number of modules
                module_size = N // num_modules
                
                # Initialize empty matrix
                self.W = np.zeros((N, N))
                
                # Create dense connections within modules
                for i in range(num_modules):
                    start_idx = i * module_size
                    end_idx = start_idx + module_size
                    
                    # Dense connections within module
                    module_connections = np.random.rand(module_size, module_size) < 0.3
                    self.W[start_idx:end_idx, start_idx:end_idx] = module_connections * np.random.uniform(-1, 1, size=(module_size, module_size))
                    
                    # Sparse connections to other modules
                    for j in range(num_modules):
                        if i != j:
                            # Sparse between-module connections
                            other_start = j * module_size
                            other_end = other_start + module_size
                            
                            # 1% connection probability between modules
                            between_connections = np.random.rand(module_size, module_size) < 0.01
                            self.W[start_idx:end_idx, other_start:other_end] = between_connections * np.random.uniform(-0.5, 0.5, size=(module_size, module_size))
            else:
                # Default to random
                self.W = np.random.uniform(-1, 1, size=(N, N))
                
                # Apply sparsity
                mask = np.random.rand(N, N) >= self.config.sparsity
                self.W[mask] = 0
            
            self.bias = np.random.uniform(-self.config.bias_scaling, 
                                        self.config.bias_scaling, 
                                        size=(N, 1))
        else:
            # Default to spectral normalized
            self.W_in = np.random.uniform(-self.config.input_scaling, 
                                        self.config.input_scaling, 
                                        size=(N, N_in))
                                        
            # Random reservoir weights
            self.W = np.random.uniform(-1, 1, size=(N, N))
            
            # Apply sparsity
            mask = np.random.rand(N, N) >= self.config.sparsity
            self.W[mask] = 0
            
            self.bias = np.random.uniform(-self.config.bias_scaling, 
                                        self.config.bias_scaling, 
                                        size=(N, 1))
        
        # Scale reservoir weight matrix to desired spectral radius
        self.W = self.scale_matrix_to_radius(self.W, self.config.spectral_radius)
        
        # Initialize output weights to zeros
        self.W_out = np.zeros((N_out, N))
        
        self.initialized = True
        
        logging.info(f"ESN initialized with {N_in} inputs, {N} reservoir units, and {N_out} outputs")
        logging.info(f"Spectral radius: {self.compute_spectral_radius(self.W):.3f}")
        logging.info(f"Sparsity: {np.mean(self.W == 0):.3f}")
    
    def update_state(self, input_data: np.ndarray) -> np.ndarray:
        """
        Update reservoir state based on input data.
        
        Args:
            input_data: Input data vector or matrix
            
        Returns:
            New reservoir state
        """
        # Ensure input is 2D (add batch dimension if needed)
        if input_data.ndim == 1:
            input_data = input_data.reshape(1, -1)
        
        # Process each input sequentially
        states = []
        for i in range(input_data.shape[0]):
            u = input_data[i].reshape(-1, 1)  # Input at time t
            x = self.reservoir_state.reshape(-1, 1)  # Current state
            
            # Compute input contribution
            input_term = self.W_in @ u
            
            # Compute reservoir contribution
            reservoir_term = self.W @ x
            
            # Apply leaky integration to update state
            x_new = (1 - self.config.leakage_rate) * x + self.config.leakage_rate * \
                   np.tanh(input_term + reservoir_term + self.bias)
            
            # Add noise if specified
            if self.config.noise_level > 0:
                noise = np.random.randn(*x_new.shape) * self.config.noise_level
                x_new += noise
            
            # Update state
            self.reservoir_state = x_new.flatten()
            states.append(self.reservoir_state.copy())
        
        # Stack all states
        return np.vstack(states)
    
    def collect_states(self, inputs: np.ndarray, targets: Optional[np.ndarray] = None,
                      washout: int = None) -> np.ndarray:
        """
        Collect reservoir states for all inputs.
        
        Args:
            inputs: Input data (time_steps × input_size)
            targets: Optional target data (time_steps × output_size)
            washout: Number of initial states to discard (default: use config)
            
        Returns:
            Matrix of collected states (time_steps - washout) × reservoir_size
        """
        if washout is None:
            washout = self.config.washout_length
            
        # Reset reservoir state
        self.reset_state()
        
        # Initialize state matrix
        time_steps = inputs.shape[0]
        state_matrix = np.zeros((time_steps, self.config.reservoir_size))
        
        # Feed inputs through the reservoir
        for t in range(time_steps):
            # Update state
            state_matrix[t] = self.update_state(inputs[t])
        
        # Store for later use
        self.training_data['inputs'] = inputs
        self.training_data['states'] = state_matrix
        if targets is not None:
            self.training_data['targets'] = targets
        
        # Return states after washout
        return state_matrix[washout:]
    
    def train(self, inputs: np.ndarray, targets: np.ndarray) -> None:
        """
        Train the output weights using ridge regression.
        
        Args:
            inputs: Input training data (time_steps × input_size)
            targets: Target training data (time_steps × output_size)
        """
        washout = self.config.washout_length
        
        # Ensure inputs and targets have same number of time steps
        if inputs.shape[0] != targets.shape[0]:
            raise ValueError(f"Input and target sequences must have the same length. "
                           f"Got {inputs.shape[0]} and {targets.shape[0]}")
                           
        logging.info(f"Training ESN with {inputs.shape[0]} time steps, " 
                    f"washout={washout}")
                    
        # Collect reservoir states
        states = self.collect_states(inputs, targets, washout)
        
        # Use states after washout for training
        X = states
        Y = targets[washout:]
        
        # Train based on readout type
        if self.config.readout_type == ReadoutType.RIDGE_REGRESSION:
            # Ridge Regression (L2 regularization)
            if self.config.regularization_strength > 0:
                # Regularized training
                X_T = X.T
                self.W_out = Y.T @ X @ np.linalg.inv(X_T @ X + 
                                                  self.config.regularization_strength * 
                                                  np.eye(X.shape[1]))
            else:
                # Unregularized least squares (pseudo-inverse)
                self.W_out = Y.T @ np.linalg.pinv(X)
                
        elif self.config.readout_type == ReadoutType.LINEAR:
            # Simple linear regression
            self.W_out = np.linalg.lstsq(X, Y, rcond=None)[0].T
            
        else:
            # Default to ridge regression
            X_T = X.T
            self.W_out = Y.T @ X @ np.linalg.inv(X_T @ X + 
                                              self.config.regularization_strength * 
                                              np.eye(X.shape[1]))
        
        # Compute training metrics
        train_predictions = self.W_out @ X.T
        metrics = self.compute_metrics(train_predictions.T, Y)
        
        logging.info(f"Training completed. Metrics: {metrics}")
    
    def predict(self, inputs: np.ndarray, reset_state: bool = True) -> np.ndarray:
        """
        Generate predictions for the given inputs.
        
        Args:
            inputs: Input data (time_steps × input_size)
            reset_state: Whether to reset reservoir state before prediction
            
        Returns:
            Predicted outputs (time_steps × output_size)
        """
        if reset_state:
            self.reset_state()
        
        # Collect reservoir states for all inputs
        states = self.update_state(inputs)
        
        # Generate predictions
        predictions = (self.W_out @ states.T).T
        
        return predictions
    
    def generate(self, initial_input: np.ndarray, steps: int, 
                feedback_scaling: float = 1.0) -> np.ndarray:
        """
        Generate outputs autonomously using output feedback.
        
        Args:
            initial_input: Initial input to start generation
            steps: Number of steps to generate
            feedback_scaling: Scaling factor for feedback
            
        Returns:
            Generated outputs (steps × output_size)
        """
        # Reset reservoir state
        self.reset_state()
        
        # Initialize with the provided input
        self.update_state(initial_input)
        
        # Initialize output array
        outputs = np.zeros((steps, self.W_out.shape[0]))
        
        # Generate outputs
        for t in range(steps):
            # Get current state
            state = self.reservoir_state
            
            # Generate output
            output = self.W_out @ state
            outputs[t] = output
            
            # Prepare next input (use generated output as feedback)
            if t < steps - 1:
                # Create feedback input
                feedback_input = output * feedback_scaling
                
                # Update state
                self.update_state(feedback_input)
        
        return outputs


class LSM(ReservoirNetwork):
    """
    Liquid State Machine implementation.
    
    Spiking neuron-based reservoir computing approach.
    """
    
    def __init__(self, config: ReservoirConfig):
        """
        Initialize Liquid State Machine.
        
        Args:
            config: Configuration parameters for the LSM
        """
        super().__init__(config)
        
        # LSM specific parameters
        self.spiking_neurons = []
        self.spike_trains = []
        self.state_collection_method = 'rate'  # 'rate', 'spike_train', 'membrane'
        self.rate_window = 10.0  # ms window for rate estimation
        
        # Initialize weights and neurons
        self.initialize_weights()
        self.reset_state()
    
    def initialize_weights(self) -> None:
        """Initialize network weights and spiking neurons."""
        # Get configuration parameters
        N_in = self.config.input_size
        N = self.config.reservoir_size
        N_out = self.config.output_size
        
        # Initialize input weights
        self.W_in = np.random.uniform(-self.config.input_scaling, 
                                     self.config.input_scaling, 
                                     size=(N, N_in))
        
        # Initialize recurrent weights (based on distance)
        self.W = np.zeros((N, N))
        
        # Assign 3D positions to neurons in a cube
        positions = np.random.rand(N, 3)  # Random positions in [0, 1]^3
        
        # Compute connection probabilities and weights based on distance
        for i in range(N):
            for j in range(N):
                # Skip self-connections
                if i == j:
                    continue
                
                # Calculate Euclidean distance
                dist = np.linalg.norm(positions[i] - positions[j])
                
                # Connection probability based on distance
                # Higher probability for nearby neurons
                conn_prob = np.exp(-dist / 0.4)  # lambda=0.4 controls decay
                
                # Establish connection with probability based on distance
                if np.random.rand() < conn_prob:
                    # Connection established
                    # Weight based on distance and neuron types
                    # Randomly assign excitatory (80%) or inhibitory (20%) neurons
                    is_excitatory = np.random.rand() < 0.8
                    
                    if is_excitatory:
                        # Excitatory connection
                        weight = np.random.gamma(2.0, 0.2)  # Positive weight
                    else:
                        # Inhibitory connection
                        weight = -np.random.gamma(4.0, 0.3)  # Negative weight
                    
                    # Weight decreases with distance
                    weight *= np.exp(-dist / 0.2)
                    
                    self.W[i, j] = weight
        
        # Apply additional sparsity
        if self.config.sparsity < 1.0:
            mask = np.random.rand(N, N) >= self.config.sparsity
            self.W[mask] = 0
        
        # Scale recurrent weights to desired spectral radius
        if np.any(self.W != 0):  # Check if W is not all zeros
            self.W = self.scale_matrix_to_radius(self.W, self.config.spectral_radius)
        
        # Initialize output weights to zeros
        self.W_out = np.zeros((N_out, N))
        
        # Initialize bias
        self.bias = np.random.uniform(-self.config.bias_scaling, 
                                     self.config.bias_scaling, 
                                     size=(N, 1))
        
        # Create spiking neurons
        self.spiking_neurons = []
        for i in range(N):
            # Create LIF neuron with parameters
            neuron = LIFNeuron(
                tau_m=20.0,                        # Membrane time constant (ms)
                tau_ref=2.0,                       # Refractory period (ms)
                v_thresh=1.0,                      # Firing threshold
                v_reset=0.0,                       # Reset potential
                v_rest=0.0,                        # Resting potential
                resistance=1.0,                    # Membrane resistance
                dt=0.1                             # Simulation time step (ms)
            )
            self.spiking_neurons.append(neuron)
        
        self.initialized = True
        
        logging.info(f"LSM initialized with {N_in} inputs, {N} reservoir neurons, and {N_out} outputs")
        logging.info(f"Spectral radius: {self.compute_spectral_radius(self.W):.3f}")
        logging.info(f"Sparsity: {np.mean(self.W == 0):.3f}")
    
    def update_state(self, input_data: np.ndarray, duration: float = 100.0) -> np.ndarray:
        """
        Simulate the LSM for the given input data.
        
        Args:
            input_data: Input data vector or matrix
            duration: Simulation duration in milliseconds
            
        Returns:
            Reservoir state representation
        """
        # Ensure input is 2D (add batch dimension if needed)
        if input_data.ndim == 1:
            input_data = input_data.reshape(1, -1)
        
        # Number of neurons
        N = self.config.reservoir_size
        
        # Determine number of time steps
        dt = 0.1  # Time step in ms
        num_steps = int(duration / dt)
        
        # Initialize state matrix and spike trains
        state_matrix = np.zeros((input_data.shape[0], N))
        spike_trains = [[] for _ in range(N)]
        
        # Process each input sequentially
        for i in range(input_data.shape[0]):
            # Reset neurons for this input
            for neuron in self.spiking_neurons:
                neuron.reset()
            
            u = input_data[i].reshape(-1, 1)  # Current input
            
            # Initialize local spike trains
            local_spike_trains = [[] for _ in range(N)]
            
            # Simulate network dynamics
            current_time = 0.0
            
            for step in range(num_steps):
                current_time = step * dt
                
                # Compute input to each neuron
                neuron_inputs = self.W_in @ u  # Input contribution
                
                # Add contribution from other neurons (based on recent spikes)
                for j in range(N):
                    for k in range(N):
                        # Check if neuron k has spiked recently
                        if self.spiking_neurons[k].has_spiked:
                            # Apply connection weight
                            neuron_inputs[j] += self.W[j, k]
                
                # Update each neuron
                for j in range(N):
                    # Update neuron and check for spike
                    input_current = float(neuron_inputs[j] + self.bias[j])
                    spike = self.spiking_neurons[j].update(input_current, dt)
                    
                    # Record spike if occurred
                    if spike:
                        local_spike_trains[j].append(current_time)
            
            # Compute state representation based on specified method
            if self.state_collection_method == 'rate':
                # Compute firing rates over the last window
                window_start = max(0, current_time - self.rate_window)
                
                for j in range(N):
                    # Count spikes in the window
                    spikes_in_window = sum(1 for t in local_spike_trains[j] 
                                        if window_start <= t <= current_time)
                    
                    # Compute rate (spikes/second)
                    rate = spikes_in_window / (self.rate_window / 1000.0)
                    state_matrix[i, j] = rate
                    
            elif self.state_collection_method == 'spike_train':
                # Encode entire spike train (for simplicity, just use count)
                for j in range(N):
                    state_matrix[i, j] = len(local_spike_trains[j])
                    
            elif self.state_collection_method == 'membrane':
                # Use membrane potentials as state
                for j in range(N):
                    state_matrix[i, j] = self.spiking_neurons[j].v
            
            # Store spike trains
            spike_trains = local_spike_trains
        
        # Update reservoir state
        self.reservoir_state = state_matrix[-1]
        self.spike_trains = spike_trains
        
        return state_matrix
    
    def collect_states(self, inputs: np.ndarray, targets: Optional[np.ndarray] = None,
                      washout: int = None) -> np.ndarray:
        """
        Collect reservoir states for all inputs.
        
        Args:
            inputs: Input data (time_steps × input_size)
            targets: Optional target data
            washout: Number of initial states to discard
            
        Returns:
            Matrix of collected states
        """
        if washout is None:
            washout = self.config.washout_length
        
        # Initialize state matrix
        time_steps = inputs.shape[0]
        state_matrix = np.zeros((time_steps, self.config.reservoir_size))
        
        # Process input sequence
        for t in range(time_steps):
            # Simulate network for this input
            state = self.update_state(inputs[t:t+1])
            state_matrix[t] = state
        
        # Store for later use
        self.training_data['inputs'] = inputs
        self.training_data['states'] = state_matrix
        if targets is not None:
            self.training_data['targets'] = targets
        
        # Return states after washout
        return state_matrix[washout:]
    
    def train(self, inputs: np.ndarray, targets: np.ndarray) -> None:
        """
        Train the output weights using ridge regression.
        
        Args:
            inputs: Input training data (time_steps × input_size)
            targets: Target training data (time_steps × output_size)
        """
        washout = self.config.washout_length
        
        # Ensure inputs and targets have same number of time steps
        if inputs.shape[0] != targets.shape[0]:
            raise ValueError(f"Input and target sequences must have the same length. "
                           f"Got {inputs.shape[0]} and {targets.shape[0]}")
        
        logging.info(f"Training LSM with {inputs.shape[0]} time steps, "
                    f"washout={washout}")
        
        # Collect reservoir states
        states = self.collect_states(inputs, targets, washout)
        
        # Use states after washout for training
        X = states
        Y = targets[washout:]
        
        # Ridge Regression
        if self.config.regularization_strength > 0:
            # Regularized training
            X_T = X.T
            self.W_out = Y.T @ X @ np.linalg.inv(X_T @ X + 
                                              self.config.regularization_strength * 
                                              np.eye(X.shape[1]))
        else:
            # Unregularized least squares (pseudo-inverse)
            self.W_out = Y.T @ np.linalg.pinv(X)
        
        # Compute training metrics
        train_predictions = self.W_out @ X.T
        metrics = self.compute_metrics(train_predictions.T, Y)
        
        logging.info(f"Training completed. Metrics: {metrics}")
    
    def predict(self, inputs: np.ndarray, reset_state: bool = True) -> np.ndarray:
        """
        Generate predictions for the given inputs.
        
        Args:
            inputs: Input data (time_steps × input_size)
            reset_state: Whether to reset reservoir state before prediction
            
        Returns:
            Predicted outputs (time_steps × output_size)
        """
        if reset_state:
            self.reset_state()
        
        # Collect reservoir states for all inputs
        states = np.zeros((inputs.shape[0], self.config.reservoir_size))
        
        for t in range(inputs.shape[0]):
            # Simulate network for this input
            state = self.update_state(inputs[t:t+1])
            states[t] = state
        
        # Generate predictions
        predictions = (self.W_out @ states.T).T
        
        return predictions
    
    def reset_state(self) -> None:
        """Reset the reservoir state and neurons."""
        super().reset_state()
        
        # Reset all neurons
        for neuron in self.spiking_neurons:
            neuron.reset()
        
        # Clear spike trains
        self.spike_trains = [[] for _ in range(self.config.reservoir_size)]
    
    def visualize_spikes(self, duration: int = 100) -> None:
        """
        Visualize spike trains from the last simulation.
        
        Args:
            duration: Duration to show (ms)
        """
        if not self.spike_trains:
            logging.warning("No spike data available. Run simulation first.")
            return
        
        # Create plot
        plt.figure(figsize=(12, 8))
        
        # Plot spike trains
        for i, spike_train in enumerate(self.spike_trains):
            # Filter spikes within duration
            spikes = [t for t in spike_train if t <= duration]
            if spikes:
                plt.plot(spikes, [i] * len(spikes), 'k|', markersize=10)
        
        plt.xlabel('Time (ms)')
        plt.ylabel('Neuron ID')
        plt.title('LSM Spike Trains')
        plt.grid(True, linestyle='--', alpha=0.7)
        plt.tight_layout()
        plt.show()


class FORCE(ReservoirNetwork):
    """
    First-Order Reduced and Controlled Error (FORCE) learning implementation.
    
    Real-time recurrent learning approach for reservoir computing.
    """
    
    def __init__(self, config: ReservoirConfig):
        """
        Initialize FORCE learning network.
        
        Args:
            config: Configuration parameters for FORCE
        """
        super().__init__(config)
        
        # FORCE specific parameters
        self.P = None  # Inverse correlation matrix
        self.alpha = 1.0  # Learning rate
        
        # Initialize weights and state
        self.initialize_weights()
        self.reset_state()
    
    def initialize_weights(self) -> None:
        """Initialize network weights according to configuration."""
        # Get configuration parameters
        N_in = self.config.input_size
        N = self.config.reservoir_size
        N_out = self.config.output_size
        
        # Initialize input weights
        self.W_in = np.random.uniform(-self.config.input_scaling, 
                                     self.config.input_scaling, 
                                     size=(N, N_in))
        
        # Initialize reservoir weights (random sparse matrix)
        self.W = np.random.uniform(-1, 1, size=(N, N))
        
        # Apply sparsity
        mask = np.random.rand(N, N) >= self.config.sparsity
        self.W[mask] = 0
        
        # Scale reservoir weights to desired spectral radius
        self.W = self.scale_matrix_to_radius(self.W, self.config.spectral_radius)
        
        # Initialize output feedback weights
        self.W_fb = np.random.uniform(-1, 1, size=(N, N_out))
        
        # Initialize output weights to small random values
        self.W_out = np.random.uniform(-1e-3, 1e-3, size=(N_out, N))
        
        # Initialize bias
        self.bias = np.random.uniform(-self.config.bias_scaling, 
                                     self.config.bias_scaling, 
                                     size=(N, 1))
        
        # Initialize inverse correlation matrix P
        self.P = np.eye(N) / self.alpha
        
        self.initialized = True
        
        logging.info(f"FORCE initialized with {N_in} inputs, {N} reservoir units, and {N_out} outputs")
        logging.info(f"Spectral radius: {self.compute_spectral_radius(self.W):.3f}")
        logging.info(f"Sparsity: {np.mean(self.W == 0):.3f}")
    
    def update_state(self, input_data: np.ndarray, output_feedback: Optional[np.ndarray] = None) -> np.ndarray:
        """
        Update reservoir state based on input data and output feedback.
        
        Args:
            input_data: Input data vector or matrix
            output_feedback: Optional output feedback
            
        Returns:
            New reservoir state
        """
        # Ensure input is 2D (add batch dimension if needed)
        if input_data.ndim == 1:
            input_data = input_data.reshape(1, -1)
        
        # Process each input sequentially
        states = []
        for i in range(input_data.shape[0]):
            u = input_data[i].reshape(-1, 1)  # Input at time t
            x = self.reservoir_state.reshape(-1, 1)  # Current state
            
            # Get output feedback
            if output_feedback is not None:
                z = output_feedback[i].reshape(-1, 1)
            else:
                # Generate output from current state
                z = (self.W_out @ x).reshape(-1, 1)
            
            # Compute next state with feedback
            input_term = self.W_in @ u
            reservoir_term = self.W @ x
            feedback_term = self.W_fb @ z
            
            # Apply leaky integration to update state
            x_new = (1 - self.config.leakage_rate) * x + self.config.leakage_rate * \
                   np.tanh(input_term + reservoir_term + feedback_term + self.bias)
            
            # Add noise if specified
            if self.config.noise_level > 0:
                noise = np.random.randn(*x_new.shape) * self.config.noise_level
                x_new += noise
            
            # Update state
            self.reservoir_state = x_new.flatten()
            states.append(self.reservoir_state.copy())
        
        # Stack all states
        return np.vstack(states)
    
    def train_step(self, state: np.ndarray, target: np.ndarray) -> Tuple[np.ndarray, float]:
        """
        Perform a single FORCE learning step.
        
        Args:
            state: Current reservoir state
            target: Target output
            
        Returns:
            Tuple of (output, error)
        """
        # Reshape inputs
        x = state.reshape(-1, 1)
        target = target.reshape(-1, 1)
        
        # Generate current output
        z = self.W_out @ x
        
        # Compute error
        error = z - target
        
        # Update inverse correlation matrix P
        Px = self.P @ x
        k = Px / (1 + x.T @ Px)
        self.P = self.P - k @ Px.T
        
        # Update output weights
        dW = -error @ k.T
        self.W_out += dW
        
        return z.flatten(), np.linalg.norm(error)
    
    def train(self, inputs: np.ndarray, targets: np.ndarray, online: bool = True) -> None:
        """
        Train the output weights using FORCE learning.
        
        Args:
            inputs: Input training data (time_steps × input_size)
            targets: Target training data (time_steps × output_size)
            online: Whether to use online (true FORCE) or offline learning
        """
        washout = self.config.washout_length
        
        # Ensure inputs and targets have same number of time steps
        if inputs.shape[0] != targets.shape[0]:
            raise ValueError(f"Input and target sequences must have the same length. "
                           f"Got {inputs.shape[0]} and {targets.shape[0]}")
        
        logging.info(f"Training FORCE with {inputs.shape[0]} time steps, "
                    f"washout={washout}, online={online}")
        
        # Reset reservoir state
        self.reset_state()
        
        # Reset inverse correlation matrix
        N = self.config.reservoir_size
        self.P = np.eye(N) / self.alpha
        
        # Initialize arrays to store results
        time_steps = inputs.shape[0]
        outputs = np.zeros((time_steps, self.config.output_size))
        states = np.zeros((time_steps, self.config.reservoir_size))
        errors = np.zeros(time_steps)
        
        # Perform training
        for t in range(time_steps):
            # Get input
            u = inputs[t]
            
            # Update reservoir state
            prev_outputs = outputs[t-1] if t > 0 else np.zeros(self.config.output_size)
            x = self.update_state(u, prev_outputs)
            states[t] = x
            
            # Skip training during washout
            if t < washout:
                continue
            
            # Perform FORCE learning step
            if online:
                # Online learning (update weights after each step)
                z, error = self.train_step(x, targets[t])
                outputs[t] = z
                errors[t] = error
            else:
                # Just collect states for offline training
                outputs[t] = self.W_out @ x.reshape(-1, 1)
        
        # For offline learning, train weights after collecting all states
        if not online:
            # Use ridge regression on collected states
            X = states[washout:]
            Y = targets[washout:]
            
            # Ridge Regression
            if self.config.regularization_strength > 0:
                # Regularized training
                X_T = X.T
                self.W_out = Y.T @ X @ np.linalg.inv(X_T @ X + 
                                                  self.config.regularization_strength * 
                                                  np.eye(X.shape[1]))
            else:
                # Unregularized least squares (pseudo-inverse)
                self.W_out = Y.T @ np.linalg.pinv(X)
            
            # Compute outputs with new weights
            outputs[washout:] = X @ self.W_out.T
            
            # Compute errors
            errors[washout:] = np.linalg.norm(outputs[washout:] - Y, axis=1)
        
        # Store training data
        self.training_data['inputs'] = inputs
        self.training_data['targets'] = targets
        self.training_data['outputs'] = outputs
        self.training_data['states'] = states
        self.training_data['errors'] = errors
        
        # Compute final metrics
        train_predictions = outputs[washout:]
        metrics = self.compute_metrics(train_predictions, targets[washout:])
        
        logging.info(f"Training completed. Metrics: {metrics}")
    
    def predict(self, inputs: np.ndarray, reset_state: bool = True,
               use_feedback: bool = True) -> np.ndarray:
        """
        Generate predictions for the given inputs.
        
        Args:
            inputs: Input data (time_steps × input_size)
            reset_state: Whether to reset reservoir state before prediction
            use_feedback: Whether to use output feedback during prediction
            
        Returns:
            Predicted outputs (time_steps × output_size)
        """
        if reset_state:
            self.reset_state()
        
        # Initialize output array
        time_steps = inputs.shape[0]
        outputs = np.zeros((time_steps, self.config.output_size))
        
        # Generate predictions
        for t in range(time_steps):
            # Get input
            u = inputs[t]
            
            # Update reservoir state with or without feedback
            if use_feedback and t > 0:
                prev_outputs = outputs[t-1]
                state = self.update_state(u, prev_outputs)
            else:
                state = self.update_state(u)
            
            # Generate output
            outputs[t] = (self.W_out @ state.reshape(-1, 1)).flatten()
        
        return outputs
    
    def generate(self, initial_input: np.ndarray, steps: int) -> np.ndarray:
        """
        Generate outputs autonomously using output feedback.
        
        Args:
            initial_input: Initial input to start generation
            steps: Number of steps to generate
            
        Returns:
            Generated outputs (steps × output_size)
        """
        # Reset reservoir state
        self.reset_state()
        
        # Initialize with the provided input
        state = self.update_state(initial_input)
        
        # Generate initial output
        output = (self.W_out @ state.reshape(-1, 1)).flatten()
        
        # Initialize output array
        outputs = np.zeros((steps, self.config.output_size))
        outputs[0] = output
        
        # Generate outputs using feedback
        for t in range(1, steps):
            # Use zero input but feedback from previous output
            state = self.update_state(np.zeros(self.config.input_size), outputs[t-1])
            
            # Generate output
            outputs[t] = (self.W_out @ state.reshape(-1, 1)).flatten()
        
        return outputs
    
    def visualize_training_errors(self) -> None:
        """Visualize training errors over time."""
        if 'errors' not in self.training_data:
            logging.warning("No training error data available. Train the model first.")
            return
        
        errors = self.training_data['errors']
        washout = self.config.washout_length
        
        plt.figure(figsize=(12, 6))
        plt.semilogy(range(len(errors)), errors, 'b-', alpha=0.7)
        plt.axvline(x=washout, color='r', linestyle='--', 
                  label=f'Washout ({washout} steps)')
        
        plt.xlabel('Time step')
        plt.ylabel('Error (log scale)')
        plt.title('FORCE Learning Error')
        plt.legend()
        plt.grid(True, linestyle='--', alpha=0.7)
        plt.tight_layout()
        plt.show()


class ReadoutLayer:
    """
    Readout layer for reservoir computing.
    
    Implements different types of readout mechanisms for transforming
    reservoir states into outputs.
    """
    
    def __init__(self, 
                 input_size: int,
                 output_size: int,
                 readout_type: ReadoutType = ReadoutType.RIDGE_REGRESSION,
                 regularization: RegularizationMethod = RegularizationMethod.RIDGE,
                 regularization_strength: float = 1e-6):
        """
        Initialize readout layer.
        
        Args:
            input_size: Size of input (reservoir state)
            output_size: Size of output
            readout_type: Type of readout mechanism
            regularization: Regularization method
            regularization_strength: Regularization parameter
        """
        self.input_size = input_size
        self.output_size = output_size
        self.readout_type = readout_type
        self.regularization = regularization
        self.regularization_strength = regularization_strength
        
        # Initialize weights
        self.W = np.zeros((output_size, input_size))
        self.bias = np.zeros(output_size)
        
        # For MLP readout
        self.hidden_layer_size = 0
        self.W1 = None
        self.b1 = None
        self.W2 = None
        self.b2 = None
        
        # For RLS
        self.P = None
        
        # Training data
        self.X_train = None
        self.y_train = None
        
        # Performance metrics
        self.metrics = {}
    
    def configure_mlp(self, hidden_size: int) -> None:
        """
        Configure MLP readout parameters.
        
        Args:
            hidden_size: Size of hidden layer
        """
        self.hidden_layer_size = hidden_size
        
        # Initialize weights
        self.W1 = np.random.randn(self.input_size, hidden_size) * 0.1
        self.b1 = np.zeros(hidden_size)
        self.W2 = np.random.randn(hidden_size, self.output_size) * 0.1
        self.b2 = np.zeros(self.output_size)
    
    def train(self, X: np.ndarray, y: np.ndarray) -> None:
        """
        Train the readout layer.
        
        Args:
            X: Input data (states)
            y: Target data
        """
        # Store training data
        self.X_train = X
        self.y_train = y
        
        if self.readout_type == ReadoutType.LINEAR:
            # Simple linear regression
            self.W = np.linalg.lstsq(X, y, rcond=None)[0].T
            
        elif self.readout_type == ReadoutType.RIDGE_REGRESSION:
            # Ridge regression
            if self.regularization_strength > 0:
                # Regularized training
                X_T = X.T
                self.W = y.T @ X @ np.linalg.inv(X_T @ X + 
                                              self.regularization_strength * 
                                              np.eye(X.shape[1]))
            else:
                # Unregularized least squares (pseudo-inverse)
                self.W = y.T @ np.linalg.pinv(X)
                
        elif self.readout_type == ReadoutType.MLP:
            # Train MLP with backpropagation
            if self.hidden_layer_size == 0:
                # Configure default hidden layer
                self.configure_mlp(hidden_size=min(100, X.shape[1] * 2))
            
            self._train_mlp(X, y)
            
        elif self.readout_type == ReadoutType.RECURSIVE_LEAST_SQUARES:
            # Initialize RLS
            self.P = np.eye(self.input_size) / self.regularization_strength
            self.W = np.zeros((self.output_size, self.input_size))
            
            # Train with RLS
            self._train_rls(X, y)
            
        elif self.readout_type == ReadoutType.FORCE:
            # Similar to RLS but with specific implementation
            self.P = np.eye(self.input_size) / self.regularization_strength
            self.W = np.zeros((self.output_size, self.input_size))
            
            # Train with FORCE
            self._train_force(X, y)
            
        else:
            # Default to ridge regression
            X_T = X.T
            self.W = y.T @ X @ np.linalg.inv(X_T @ X + 
                                          self.regularization_strength * 
                                          np.eye(X.shape[1]))
        
        # Compute training metrics
        predictions = self.predict(X)
        self.compute_metrics(predictions, y)
    
    def _train_mlp(self, X: np.ndarray, y: np.ndarray, epochs: int = 1000,
                 learning_rate: float = 0.01, batch_size: int = 32) -> None:
        """
        Train MLP readout using backpropagation.
        
        Args:
            X: Input data
            y: Target data
            epochs: Number of training epochs
            learning_rate: Learning rate
            batch_size: Batch size
        """
        n_samples = X.shape[0]
        
        # Training loop
        for epoch in range(epochs):
            # Shuffle data
            indices = np.random.permutation(n_samples)
            X_shuffled = X[indices]
            y_shuffled = y[indices]
            
            # Process mini-batches
            for i in range(0, n_samples, batch_size):
                # Get mini-batch
                X_batch = X_shuffled[i:i+batch_size]
                y_batch = y_shuffled[i:i+batch_size]
                
                # Forward pass
                hidden = np.tanh(X_batch @ self.W1 + self.b1)
                output = hidden @ self.W2 + self.b2
                
                # Compute error
                error = output - y_batch
                
                # Backpropagation
                d_output = error
                d_hidden = (d_output @ self.W2.T) * (1 - hidden**2)
                
                # Update weights
                self.W2 -= learning_rate * (hidden.T @ d_output) / batch_size
                self.b2 -= learning_rate * np.mean(d_output, axis=0)
                self.W1 -= learning_rate * (X_batch.T @ d_hidden) / batch_size
                self.b1 -= learning_rate * np.mean(d_hidden, axis=0)
            
            # Compute training error periodically
            if epoch % 100 == 0:
                # Forward pass on all data
                hidden = np.tanh(X @ self.W1 + self.b1)
                output = hidden @ self.W2 + self.b2
                
                # Compute MSE
                mse = np.mean((output - y)**2)
                logging.info(f"Epoch {epoch}: MSE = {mse:.6f}")
    
    def _train_rls(self, X: np.ndarray, y: np.ndarray) -> None:
        """
        Train readout using Recursive Least Squares.
        
        Args:
            X: Input data
            y: Target data
        """
        n_samples = X.shape[0]
        
        # Process samples sequentially
        for i in range(n_samples):
            x = X[i].reshape(-1, 1)
            target = y[i].reshape(-1, 1)
            
            # Generate current output
            output = self.W @ x
            
            # Compute error
            error = output - target
            
            # Update inverse correlation matrix P
            Px = self.P @ x
            k = Px / (1 + x.T @ Px)
            self.P = self.P - k @ Px.T
            
            # Update output weights
            dW = -error @ k.T
            self.W += dW
    
    def _train_force(self, X: np.ndarray, y: np.ndarray) -> None:
        """
        Train readout using FORCE learning.
        
        Args:
            X: Input data
            y: Target data
        """
        n_samples = X.shape[0]
        
        # Process samples sequentially
        for i in range(n_samples):
            x = X[i].reshape(-1, 1)
            target = y[i].reshape(-1, 1)
            
            # Generate current output
            output = self.W @ x
            
            # Compute error
            error = output - target
            
            # Update inverse correlation matrix P
            Px = self.P @ x
            k = Px / (1 + x.T @ Px)
            self.P = self.P - k @ Px.T
            
            # Update output weights
            dW = -error @ k.T
            self.W += dW
    
    def predict(self, X: np.ndarray) -> np.ndarray:
        """
        Generate predictions for the given inputs.
        
        Args:
            X: Input data (states)
            
        Returns:
            Predicted outputs
        """
        if self.readout_type == ReadoutType.MLP:
            # Forward pass through MLP
            hidden = np.tanh(X @ self.W1 + self.b1)
            return hidden @ self.W2 + self.b2
        else:
            # Linear readout
            return X @ self.W.T
    
    def compute_metrics(self, predictions: np.ndarray, targets: np.ndarray) -> Dict[str, float]:
        """
        Compute performance metrics.
        
        Args:
            predictions: Predicted values
            targets: Target values
            
        Returns:
            Dictionary of performance metrics
        """
        # Ensure both arrays have the same shape
        if predictions.shape != targets.shape:
            raise ValueError(f"Predictions and targets must have the same shape. "
                           f"Got {predictions.shape} and {targets.shape}")
        
        # Mean squared error
        mse = np.mean((predictions - targets) ** 2)
        
        # Root mean squared error
        rmse = np.sqrt(mse)
        
        # Normalized root mean squared error
        if np.std(targets) > 1e-8:
            nrmse = rmse / np.std(targets)
        else:
            nrmse = float('inf')
        
        # Mean absolute error
        mae = np.mean(np.abs(predictions - targets))
        
        # Coefficient of determination (R²)
        ss_total = np.sum((targets - np.mean(targets, axis=0)) ** 2, axis=0)
        ss_residual = np.sum((targets - predictions) ** 2, axis=0)
        
        if np.all(ss_total > 1e-8):
            r2 = 1 - np.sum(ss_residual) / np.sum(ss_total)
        else:
            r2 = float('nan')
        
        # Store metrics
        metrics = {
            'mse': float(mse),
            'rmse': float(rmse),
            'nrmse': float(nrmse),
            'mae': float(mae),
            'r2': float(r2)
        }
        
        self.metrics.update(metrics)
        
        return metrics
    
    def save(self, filepath: str) -> None:
        """
        Save the trained readout to a file.
        
        Args:
            filepath: Path to save the readout
        """
        data = {
            'input_size': self.input_size,
            'output_size': self.output_size,
            'readout_type': self.readout_type,
            'regularization': self.regularization,
            'regularization_strength': self.regularization_strength,
            'W': self.W,
            'bias': self.bias,
            'hidden_layer_size': self.hidden_layer_size,
            'W1': self.W1,
            'b1': self.b1,
            'W2': self.W2,
            'b2': self.b2,
            'metrics': self.metrics
        }
        
        with open(filepath, 'wb') as f:
            pickle.dump(data, f)
    
    @classmethod
    def load(cls, filepath: str) -> 'ReadoutLayer':
        """
        Load a trained readout from a file.
        
        Args:
            filepath: Path to load the readout from
            
        Returns:
            Loaded readout layer
        """
        with open(filepath, 'rb') as f:
            data = pickle.load(f)
        
        # Create instance
        readout = cls(
            input_size=data['input_size'],
            output_size=data['output_size'],
            readout_type=data['readout_type'],
            regularization=data['regularization'],
            regularization_strength=data['regularization_strength']
        )
        
        # Set weights
        readout.W = data['W']
        readout.bias = data['bias']
        readout.hidden_layer_size = data['hidden_layer_size']
        readout.W1 = data['W1']
        readout.b1 = data['b1']
        readout.W2 = data['W2']
        readout.b2 = data['b2']
        readout.metrics = data.get('metrics', {})
        
        return readout


class RidgeRegression:
    """
    Standalone Ridge Regression implementation for reservoir computing.
    
    This class provides a dedicated ridge regression solver that can be used
    independently or as part of reservoir computing systems.
    """
    
    def __init__(self, alpha: float = 1e-6, fit_intercept: bool = True):
        """
        Initialize Ridge Regression.
        
        Args:
            alpha: Regularization strength (higher values = more regularization)
            fit_intercept: Whether to calculate the intercept for this model
        """
        self.alpha = alpha
        self.fit_intercept = fit_intercept
        self.coef_ = None
        self.intercept_ = None
        self.feature_names_ = None
        
        # Performance metrics
        self.training_score_ = None
        self.n_features_in_ = None
        
    def fit(self, X: np.ndarray, y: np.ndarray) -> 'RidgeRegression':
        """
        Fit the Ridge regression model.
        
        Args:
            X: Training data (n_samples, n_features)
            y: Target values (n_samples,) or (n_samples, n_targets)
            
        Returns:
            self: The fitted model instance
        """
        # Ensure X and y are numpy arrays
        X = np.asarray(X)
        y = np.asarray(y)
        
        # Store dimensions
        n_samples, n_features = X.shape
        self.n_features_in_ = n_features
        
        # Handle 1D target
        if y.ndim == 1:
            y = y.reshape(-1, 1)
        
        n_targets = y.shape[1]
        
        if self.fit_intercept:
            # Add bias column to X
            X_with_bias = np.column_stack([np.ones(n_samples), X])
            
            # Solve ridge regression with intercept
            # (X^T X + alpha * I) w = X^T y
            XtX = X_with_bias.T @ X_with_bias
            
            # Add regularization (don't regularize intercept)
            reg_matrix = np.eye(n_features + 1) * self.alpha
            reg_matrix[0, 0] = 0  # Don't regularize intercept
            
            # Solve normal equations
            try:
                coefficients = np.linalg.solve(XtX + reg_matrix, X_with_bias.T @ y)
            except np.linalg.LinAlgError:
                # Use pseudo-inverse for numerical stability
                coefficients = np.linalg.pinv(XtX + reg_matrix) @ X_with_bias.T @ y
            
            # Separate intercept and coefficients
            self.intercept_ = coefficients[0]
            self.coef_ = coefficients[1:]
            
        else:
            # No intercept
            XtX = X.T @ X
            reg_matrix = np.eye(n_features) * self.alpha
            
            # Solve normal equations
            try:
                self.coef_ = np.linalg.solve(XtX + reg_matrix, X.T @ y)
            except np.linalg.LinAlgError:
                # Use pseudo-inverse for numerical stability
                self.coef_ = np.linalg.pinv(XtX + reg_matrix) @ X.T @ y
            
            self.intercept_ = np.zeros(n_targets)
        
        # Reshape coefficients if single target
        if n_targets == 1:
            self.coef_ = self.coef_.flatten()
            self.intercept_ = float(self.intercept_[0])
        
        # Compute training score (R²)
        y_pred = self.predict(X)
        self.training_score_ = self.score(X, y.squeeze() if n_targets == 1 else y)
        
        return self
    
    def predict(self, X: np.ndarray) -> np.ndarray:
        """
        Predict using the ridge regression model.
        
        Args:
            X: Samples (n_samples, n_features)
            
        Returns:
            Predicted values (n_samples,) or (n_samples, n_targets)
        """
        if self.coef_ is None:
            raise ValueError("Model has not been fitted yet. Call fit() first.")
        
        X = np.asarray(X)
        
        # Compute predictions
        if self.fit_intercept:
            if self.coef_.ndim == 1:
                # Single target
                return X @ self.coef_ + self.intercept_
            else:
                # Multiple targets
                return X @ self.coef_ + self.intercept_
        else:
            return X @ self.coef_
    
    def score(self, X: np.ndarray, y: np.ndarray) -> float:
        """
        Return the coefficient of determination R² of the prediction.
        
        Args:
            X: Test samples
            y: True values
            
        Returns:
            R² score
        """
        y_pred = self.predict(X)
        y_true = np.asarray(y)
        
        # Handle multi-target case
        if y_true.ndim > 1 and y_true.shape[1] > 1:
            # For multiple targets, return mean R²
            scores = []
            for i in range(y_true.shape[1]):
                ss_tot = np.sum((y_true[:, i] - np.mean(y_true[:, i])) ** 2)
                ss_res = np.sum((y_true[:, i] - y_pred[:, i]) ** 2)
                if ss_tot > 1e-8:
                    scores.append(1 - ss_res / ss_tot)
                else:
                    scores.append(0.0)
            return np.mean(scores)
        else:
            # Single target
            if y_true.ndim > 1:
                y_true = y_true.flatten()
            
            ss_tot = np.sum((y_true - np.mean(y_true)) ** 2)
            ss_res = np.sum((y_true - y_pred) ** 2)
            
            if ss_tot > 1e-8:
                return 1 - ss_res / ss_tot
            else:
                return 0.0
    
    def get_params(self) -> Dict[str, Any]:
        """Get parameters for this estimator."""
        return {
            'alpha': self.alpha,
            'fit_intercept': self.fit_intercept
        }
    
    def set_params(self, **params) -> 'RidgeRegression':
        """Set the parameters of this estimator."""
        for param, value in params.items():
            if hasattr(self, param):
                setattr(self, param, value)
            else:
                raise ValueError(f"Invalid parameter {param}")
        return self
    
    def compute_metrics(self, X: np.ndarray, y: np.ndarray) -> Dict[str, float]:
        """
        Compute detailed performance metrics.
        
        Args:
            X: Input data
            y: True values
            
        Returns:
            Dictionary of performance metrics
        """
        y_pred = self.predict(X)
        y_true = np.asarray(y)
        
        # Ensure both are 1D for single target
        if y_true.ndim == 1:
            y_pred = y_pred.flatten() if y_pred.ndim > 1 else y_pred
        
        # Mean squared error
        mse = np.mean((y_true - y_pred) ** 2)
        
        # Root mean squared error
        rmse = np.sqrt(mse)
        
        # Mean absolute error
        mae = np.mean(np.abs(y_true - y_pred))
        
        # R² score
        r2 = self.score(X, y)
        
        # Normalized RMSE
        if np.std(y_true) > 1e-8:
            nrmse = rmse / np.std(y_true)
        else:
            nrmse = float('inf')
        
        return {
            'mse': float(mse),
            'rmse': float(rmse),
            'mae': float(mae),
            'r2': float(r2),
            'nrmse': float(nrmse),
            'n_features': int(self.n_features_in_) if self.n_features_in_ is not None else 0,
            'alpha': float(self.alpha)
        }
    
    def save(self, filepath: str) -> None:
        """Save the model to a file."""
        model_data = {
            'alpha': self.alpha,
            'fit_intercept': self.fit_intercept,
            'coef_': self.coef_,
            'intercept_': self.intercept_,
            'training_score_': self.training_score_,
            'n_features_in_': self.n_features_in_
        }
        
        with open(filepath, 'wb') as f:
            pickle.dump(model_data, f)
        
        logging.info(f"Ridge regression model saved to {filepath}")
    
    @classmethod
    def load(cls, filepath: str) -> 'RidgeRegression':
        """Load a model from a file."""
        with open(filepath, 'rb') as f:
            model_data = pickle.load(f)
        
        # Create instance
        instance = cls(
            alpha=model_data['alpha'],
            fit_intercept=model_data['fit_intercept']
        )
        
        # Set fitted parameters
        instance.coef_ = model_data['coef_']
        instance.intercept_ = model_data['intercept_']
        instance.training_score_ = model_data['training_score_']
        instance.n_features_in_ = model_data['n_features_in_']
        
        logging.info(f"Ridge regression model loaded from {filepath}")
        
        return instance
    
    def __repr__(self) -> str:
        """String representation of the model."""
        params = f"alpha={self.alpha}, fit_intercept={self.fit_intercept}"
        if self.coef_ is not None:
            n_features = self.n_features_in_ or "unknown"
            params += f", fitted=True, n_features={n_features}"
            if self.training_score_ is not None:
                params += f", training_score={self.training_score_:.4f}"
        else:
            params += ", fitted=False"
        
        return f"RidgeRegression({params})"


class EchoStateProperty:
    """
    Utility for analyzing the echo state property of reservoirs.
    
    The echo state property ensures that the effect of initial conditions
    diminishes over time, which is crucial for stable reservoir dynamics.
    """
    
    @staticmethod
    def check_echo_state(reservoir_matrix: np.ndarray, 
                        num_iterations: int = 100, 
                        num_initial_states: int = 10) -> Tuple[bool, float]:
        """
        Check if a reservoir weight matrix has the echo state property.
        
        Args:
            reservoir_matrix: Weight matrix of the reservoir
            num_iterations: Number of iterations to check convergence
            num_initial_states: Number of random initial states to test
            
        Returns:
            Tuple of (has_echo_state, separation_measure)
        """
        # Get reservoir size
        N = reservoir_matrix.shape[0]
        
        # Generate random initial states
        states1 = np.random.randn(num_initial_states, N) * 0.5
        states2 = np.random.randn(num_initial_states, N) * 0.5
        
        # Compute and track the separation measure over iterations
        separations = np.zeros(num_iterations)
        
        for i in range(num_iterations):
            # Update states
            for j in range(num_initial_states):
                # Apply state update (tanh nonlinearity)
                states1[j] = np.tanh(reservoir_matrix @ states1[j])
                states2[j] = np.tanh(reservoir_matrix @ states2[j])
            
            # Compute separation measure (average Euclidean distance)
            separations[i] = np.mean([np.linalg.norm(states1[j] - states2[j]) 
                                    for j in range(num_initial_states)])
        
        # Check if separation converges to zero
        is_convergent = separations[-1] < 1e-3
        
        # Return result and final separation
        return is_convergent, float(separations[-1])
    
    @staticmethod
    def estimate_stability_margin(reservoir_matrix: np.ndarray,
                                spectral_radius_range: Tuple[float, float] = (0.5, 1.5),
                                num_steps: int = 20) -> Tuple[float, np.ndarray, np.ndarray]:
        """
        Estimate the stability margin of the reservoir.
        
        The stability margin is the range of spectral radii where the
        reservoir exhibits the echo state property.
        
        Args:
            reservoir_matrix: Weight matrix of the reservoir
            spectral_radius_range: Range of spectral radii to test
            num_steps: Number of spectral radius values to test
            
        Returns:
            Tuple of (optimal_radius, spectral_radii, separation_measures)
        """
        # Compute current spectral radius
        current_radius = max(abs(np.linalg.eigvals(reservoir_matrix)))
        
        # Create range of spectral radii to test
        spectral_radii = np.linspace(spectral_radius_range[0], 
                                     spectral_radius_range[1], 
                                     num_steps)
        
        # Measure separation for each spectral radius
        separation_measures = np.zeros(num_steps)
        
        for i, radius in enumerate(spectral_radii):
            # Scale matrix to the target radius
            scaled_matrix = reservoir_matrix * (radius / current_radius)
            
            # Check echo state property
            _, separation = EchoStateProperty.check_echo_state(scaled_matrix)
            separation_measures[i] = separation
        
        # Find optimal spectral radius (minimum separation)
        optimal_idx = np.argmin(separation_measures)
        optimal_radius = spectral_radii[optimal_idx]
        
        return optimal_radius, spectral_radii, separation_measures
    
    @staticmethod
    def plot_stability_analysis(spectral_radii: np.ndarray, 
                               separation_measures: np.ndarray,
                               optimal_radius: float) -> None:
        """
        Plot stability analysis results.
        
        Args:
            spectral_radii: Array of tested spectral radii
            separation_measures: Corresponding separation measures
            optimal_radius: Optimal spectral radius
        """
        plt.figure(figsize=(10, 6))
        plt.semilogy(spectral_radii, separation_measures, 'b-o', alpha=0.7)
        plt.axvline(x=optimal_radius, color='r', linestyle='--', 
                  label=f'Optimal radius = {optimal_radius:.3f}')
        plt.axvline(x=1.0, color='g', linestyle=':', 
                  label='Spectral radius = 1.0')
        
        plt.xlabel('Spectral radius')
        plt.ylabel('Separation measure (log scale)')
        plt.title('Reservoir Stability Analysis')
        plt.grid(True, linestyle='--', alpha=0.7)
        plt.legend()
        plt.tight_layout()
        plt.show()


class MemoryCapacity:
    """
    Utility for measuring the memory capacity of reservoirs.
    
    Memory capacity quantifies how well the reservoir can recall
    past inputs, which is crucial for processing time series.
    """
    
    @staticmethod
    def measure_memory_capacity(reservoir: ReservoirNetwork, 
                              max_delay: int = 20,
                              sequence_length: int = 5000,
                              test_length: int = 1000) -> Tuple[float, np.ndarray]:
        """
        Measure the memory capacity of a reservoir.
        
        Args:
            reservoir: Reservoir network to test
            max_delay: Maximum delay to test
            sequence_length: Length of random sequence
            test_length: Length of test sequence
            
        Returns:
            Tuple of (total_memory_capacity, delay_capacities)
        """
        # Generate random input sequence
        input_seq = np.random.rand(sequence_length, 1) * 2 - 1
        
        # Reset reservoir
        reservoir.reset_state()
        
        # Run reservoir on input sequence
        states = np.zeros((sequence_length, reservoir.config.reservoir_size))
        for t in range(sequence_length):
            state = reservoir.update_state(input_seq[t])
            states[t] = state
        
        # Measure memory capacity for different delays
        memory_capacities = np.zeros(max_delay + 1)
        
        for k in range(max_delay + 1):
            # Define target: input with delay k
            target = input_seq[max_delay:-test_length+k]
            
            # Use states after max_delay
            X = states[max_delay:sequence_length-test_length+max_delay]
            
            # Train a linear readout to reconstruct delayed input
            if X.shape[0] > X.shape[1]:
                # Least squares solution
                W_k = np.linalg.lstsq(X, target, rcond=None)[0]
            else:
                # Ridge regression
                X_T = X.T
                W_k = target.T @ X @ np.linalg.inv(X_T @ X + 1e-8 * np.eye(X.shape[1]))
                W_k = W_k.T
            
            # Test on separate data
            X_test = states[sequence_length-test_length+max_delay:]
            target_test = input_seq[sequence_length-test_length+max_delay+k:] if k == 0 else input_seq[sequence_length-test_length+max_delay+k-1:-1]
            
            # Ensure matching lengths
            min_len = min(X_test.shape[0], target_test.shape[0])
            X_test = X_test[:min_len]
            target_test = target_test[:min_len]
            
            # Compute predictions
            predictions = X_test @ W_k
            
            # Compute correlation coefficient
            target_mean = np.mean(target_test)
            pred_mean = np.mean(predictions)
            
            numerator = np.sum((target_test - target_mean) * (predictions - pred_mean))
            denominator = np.sqrt(np.sum((target_test - target_mean)**2) * np.sum((predictions - pred_mean)**2))
            
            if denominator > 1e-10:
                corr = numerator / denominator
                memory_capacities[k] = corr**2
            else:
                memory_capacities[k] = 0
        
        # Total memory capacity
        total_mc = np.sum(memory_capacities)
        
        return total_mc, memory_capacities
    
    @staticmethod
    def plot_memory_capacity(delays: np.ndarray, 
                            memory_capacities: np.ndarray,
                            total_mc: float) -> None:
        """
        Plot memory capacity results.
        
        Args:
            delays: Array of delays
            memory_capacities: Memory capacity for each delay
            total_mc: Total memory capacity
        """
        plt.figure(figsize=(10, 6))
        plt.bar(delays, memory_capacities, alpha=0.7)
        plt.axhline(y=0.8, color='r', linestyle='--', 
                  label='Capacity threshold (0.8)')
        
        plt.text(0.5 * max(delays), 0.9, f'Total MC = {total_mc:.2f}', 
               horizontalalignment='center', fontsize=12, 
               bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.8))
        
        plt.xlabel('Delay (k)')
        plt.ylabel('Memory Capacity')
        plt.title('Reservoir Memory Capacity Analysis')
        plt.grid(True, linestyle='--', alpha=0.7)
        plt.legend()
        plt.tight_layout()
        plt.show()


class KernelQuality:
    """
    Utility for measuring the kernel quality of reservoirs.
    
    Kernel quality quantifies how well the reservoir separates different inputs,
    which is crucial for classification and pattern recognition tasks.
    """
    
    @staticmethod
    def measure_kernel_quality(reservoir: ReservoirNetwork,
                             num_samples: int = 1000,
                             input_dims: int = None) -> float:
        """
        Measure the kernel quality of a reservoir.
        
        Args:
            reservoir: Reservoir network to test
            num_samples: Number of random input samples
            input_dims: Input dimensionality (default: use reservoir config)
            
        Returns:
            Kernel quality measure
        """
        if input_dims is None:
            input_dims = reservoir.config.input_size
        
        # Generate random input patterns
        inputs = np.random.randn(num_samples, input_dims)
        
        # Normalize inputs
        inputs = inputs / np.linalg.norm(inputs, axis=1).reshape(-1, 1)
        
        # Get reservoir states for each input
        states = np.zeros((num_samples, reservoir.config.reservoir_size))
        
        for i in range(num_samples):
            reservoir.reset_state()
            state = reservoir.update_state(inputs[i])
            states[i] = state
        
        # Compute input and state correlation matrices
        input_corr = inputs @ inputs.T
        state_corr = states @ states.T
        
        # Compute rank of state correlation matrix
        rank = np.linalg.matrix_rank(state_corr)
        effective_dim = rank / reservoir.config.reservoir_size
        
        # Compute kernel quality (based on linear separability)
        # Higher values indicate better separation
        kernel_quality = effective_dim
        
        return kernel_quality
    
    @staticmethod
    def compare_kernel_qualities(reservoirs: Dict[str, ReservoirNetwork]) -> Dict[str, float]:
        """
        Compare kernel qualities of multiple reservoirs.
        
        Args:
            reservoirs: Dictionary mapping names to reservoir networks
            
        Returns:
            Dictionary mapping names to kernel qualities
        """
        qualities = {}
        
        for name, reservoir in reservoirs.items():
            quality = KernelQuality.measure_kernel_quality(reservoir)
            qualities[name] = quality
            
        return qualities
    
    @staticmethod
    def plot_kernel_comparison(qualities: Dict[str, float]) -> None:
        """
        Plot comparison of kernel qualities.
        
        Args:
            qualities: Dictionary mapping names to kernel qualities
        """
        names = list(qualities.keys())
        values = list(qualities.values())
        
        plt.figure(figsize=(10, 6))
        plt.bar(names, values, alpha=0.7)
        
        plt.xlabel('Reservoir')
        plt.ylabel('Kernel Quality')
        plt.title('Reservoir Kernel Quality Comparison')
        plt.ylim(0, 1)
        plt.grid(True, linestyle='--', alpha=0.7)
        plt.tight_layout()
        plt.show()


class SpectralRadius:
    """
    Utility for analyzing and optimizing spectral radius of reservoirs.
    
    The spectral radius affects the memory capacity, nonlinearity, and
    stability of the reservoir dynamics.
    """
    
    @staticmethod
    def optimize_spectral_radius(reservoir: ReservoirNetwork,
                                inputs: np.ndarray,
                                targets: np.ndarray,
                                radius_range: Tuple[float, float] = (0.1, 1.5),
                                num_steps: int = 10) -> Tuple[float, np.ndarray, np.ndarray]:
        """
        Find the optimal spectral radius for a given task.
        
        Args:
            reservoir: Reservoir network to optimize
            inputs: Input training data
            targets: Target training data
            radius_range: Range of spectral radii to test
            num_steps: Number of spectral radius values to test
            
        Returns:
            Tuple of (optimal_radius, spectral_radii, errors)
        """
        # Backup original weights
        original_W = reservoir.W.copy()
        
        # Get current spectral radius
        current_radius = reservoir.compute_spectral_radius(original_W)
        
        # Create range of spectral radii to test
        spectral_radii = np.linspace(radius_range[0], radius_range[1], num_steps)
        errors = np.zeros(num_steps)
        
        # Test each spectral radius
        for i, radius in enumerate(spectral_radii):
            # Scale reservoir weights
            scaling_factor = radius / current_radius
            reservoir.W = original_W * scaling_factor
            
            # Train reservoir
            reservoir.train(inputs, targets)
            
            # Test performance
            predictions = reservoir.predict(inputs)
            mse = np.mean((predictions - targets) ** 2)
            errors[i] = mse
            
            logging.info(f"Spectral radius: {radius:.3f}, MSE: {mse:.6f}")
        
        # Find optimal spectral radius (minimum error)
        optimal_idx = np.argmin(errors)
        optimal_radius = spectral_radii[optimal_idx]
        
        # Restore optimal weights
        scaling_factor = optimal_radius / current_radius
        reservoir.W = original_W * scaling_factor
        
        # Re-train with optimal radius
        reservoir.train(inputs, targets)
        
        return optimal_radius, spectral_radii, errors
    
    @staticmethod
    def plot_radius_optimization(spectral_radii: np.ndarray,
                               errors: np.ndarray,
                               optimal_radius: float) -> None:
        """
        Plot spectral radius optimization results.
        
        Args:
            spectral_radii: Array of tested spectral radii
            errors: Corresponding error measures
            optimal_radius: Optimal spectral radius
        """
        plt.figure(figsize=(10, 6))
        plt.semilogy(spectral_radii, errors, 'b-o', alpha=0.7)
        plt.axvline(x=optimal_radius, color='r', linestyle='--', 
                  label=f'Optimal radius = {optimal_radius:.3f}')
        
        plt.xlabel('Spectral radius')
        plt.ylabel('Mean Squared Error (log scale)')
        plt.title('Spectral Radius Optimization')
        plt.grid(True, linestyle='--', alpha=0.7)
        plt.legend()
        plt.tight_layout()
        plt.show()


class InputScaling:
    """
    Utility for analyzing and optimizing input scaling of reservoirs.
    
    Input scaling affects how strongly external inputs drive the reservoir,
    influencing the balance between input-driven and autonomous dynamics.
    """
    
    @staticmethod
    def optimize_input_scaling(reservoir: ReservoirNetwork,
                             inputs: np.ndarray,
                             targets: np.ndarray,
                             scaling_range: Tuple[float, float] = (0.01, 2.0),
                             num_steps: int = 10) -> Tuple[float, np.ndarray, np.ndarray]:
        """
        Find the optimal input scaling for a given task.
        
        Args:
            reservoir: Reservoir network to optimize
            inputs: Input training data
            targets: Target training data
            scaling_range: Range of input scaling values to test
            num_steps: Number of scaling values to test
            
        Returns:
            Tuple of (optimal_scaling, scaling_values, errors)
        """
        # Backup original input weights
        original_W_in = reservoir.W_in.copy()
        
        # Create range of scaling values to test
        scaling_values = np.logspace(np.log10(scaling_range[0]), 
                                    np.log10(scaling_range[1]), 
                                    num_steps)
        errors = np.zeros(num_steps)
        
        # Test each scaling value
        for i, scaling in enumerate(scaling_values):
            # Scale input weights
            reservoir.W_in = original_W_in * (scaling / reservoir.config.input_scaling)
            
            # Train reservoir
            reservoir.train(inputs, targets)
            
            # Test performance
            predictions = reservoir.predict(inputs)
            mse = np.mean((predictions - targets) ** 2)
            errors[i] = mse
            
            logging.info(f"Input scaling: {scaling:.3f}, MSE: {mse:.6f}")
        
        # Find optimal scaling (minimum error)
        optimal_idx = np.argmin(errors)
        optimal_scaling = scaling_values[optimal_idx]
        
        # Restore optimal weights
        reservoir.W_in = original_W_in * (optimal_scaling / reservoir.config.input_scaling)
        
        # Update configuration
        reservoir.config.input_scaling = optimal_scaling
        
        # Re-train with optimal scaling
        reservoir.train(inputs, targets)
        
        return optimal_scaling, scaling_values, errors
    
    @staticmethod
    def plot_scaling_optimization(scaling_values: np.ndarray,
                                errors: np.ndarray,
                                optimal_scaling: float) -> None:
        """
        Plot input scaling optimization results.
        
        Args:
            scaling_values: Array of tested scaling values
            errors: Corresponding error measures
            optimal_scaling: Optimal input scaling
        """
        plt.figure(figsize=(10, 6))
        plt.semilogx(scaling_values, errors, 'b-o', alpha=0.7)
        plt.axvline(x=optimal_scaling, color='r', linestyle='--', 
                  label=f'Optimal scaling = {optimal_scaling:.3f}')
        
        plt.xlabel('Input Scaling (log scale)')
        plt.ylabel('Mean Squared Error')
        plt.title('Input Scaling Optimization')
        plt.grid(True, linestyle='--', alpha=0.7)
        plt.legend()
        plt.tight_layout()
        plt.show()


class ReservoirTopologyBuilder:
    """
    Utility for creating and analyzing different reservoir topologies.
    
    Reservoir topology affects the connectivity patterns and information flow
    within the reservoir, influencing its computational capabilities.
    """
    
    @staticmethod
    def create_topology(topology_type: ReservoirTopology,
                       size: int,
                       sparsity: float = 0.1,
                       spectral_radius: float = 0.95) -> np.ndarray:
        """
        Create a reservoir weight matrix with specified topology.
        
        Args:
            topology_type: Type of topology to create
            size: Size of the reservoir
            sparsity: Connection sparsity
            spectral_radius: Target spectral radius
            
        Returns:
            Reservoir weight matrix
        """
        if topology_type == ReservoirTopology.RANDOM:
            # Random connectivity
            W = np.random.uniform(-1, 1, size=(size, size))
            
            # Apply sparsity
            mask = np.random.rand(size, size) >= sparsity
            W[mask] = 0
            
        elif topology_type == ReservoirTopology.SMALL_WORLD:
            # Create small-world network
            k = max(2, int(size * (1 - sparsity)))  # Average degree
            G = nx.watts_strogatz_graph(size, k, p=0.1)
            
            # Convert to adjacency matrix
            adj_matrix = nx.to_numpy_array(G)
            
            # Assign random weights
            W = adj_matrix * np.random.uniform(-1, 1, size=(size, size))
            
        elif topology_type == ReservoirTopology.SCALE_FREE:
            # Create scale-free network (Barabási–Albert model)
            m = max(1, int(size * (1 - sparsity) / 2))
            G = nx.barabasi_albert_graph(size, m)
            
            # Convert to adjacency matrix
            adj_matrix = nx.to_numpy_array(G)
            
            # Assign random weights
            W = adj_matrix * np.random.uniform(-1, 1, size=(size, size))
            
        elif topology_type == ReservoirTopology.MODULAR:
            # Create modular network
            num_modules = min(5, size // 10)  # Number of modules
            module_size = size // num_modules
            
            # Initialize empty matrix
            W = np.zeros((size, size))
            
            # Create dense connections within modules
            for i in range(num_modules):
                start_idx = i * module_size
                end_idx = start_idx + module_size
                
                # Dense connections within module
                module_connections = np.random.rand(module_size, module_size) < 0.3
                W[start_idx:end_idx, start_idx:end_idx] = module_connections * np.random.uniform(-1, 1, size=(module_size, module_size))
                
                # Sparse connections to other modules
                for j in range(num_modules):
                    if i != j:
                        # Sparse between-module connections
                        other_start = j * module_size
                        other_end = other_start + module_size
                        
                        # 1% connection probability between modules
                        between_connections = np.random.rand(module_size, module_size) < 0.01
                        W[start_idx:end_idx, other_start:other_end] = between_connections * np.random.uniform(-0.5, 0.5, size=(module_size, module_size))
                        
        elif topology_type == ReservoirTopology.FEEDFORWARD:
            # Create feedforward chain topology
            W = np.zeros((size, size))
            
            # Divide neurons into layers
            num_layers = min(10, size // 5)
            layer_size = size // num_layers
            
            # Connect each layer to the next
            for i in range(num_layers - 1):
                layer_start = i * layer_size
                layer_end = (i + 1) * layer_size
                next_layer_start = layer_end
                next_layer_end = min(next_layer_start + layer_size, size)
                
                # Connect each neuron in layer i to some neurons in layer i+1
                for j in range(layer_start, layer_end):
                    # Random number of connections
                    num_connections = np.random.randint(1, max(2, int(layer_size * (1 - sparsity))))
                    targets = np.random.choice(range(next_layer_start, next_layer_end), 
                                             size=num_connections, replace=False)
                    
                    # Add connections
                    for target in targets:
                        W[target, j] = np.random.uniform(-1, 1)
                        
        elif topology_type == ReservoirTopology.CLUSTERED:
            # Create clustered topology
            W = np.zeros((size, size))
            
            # Number of clusters
            num_clusters = min(5, size // 10)
            
            # Assign neurons to clusters
            cluster_assignments = np.random.randint(0, num_clusters, size=size)
            
            # Create connections
            for i in range(size):
                for j in range(size):
                    if i != j:
                        # Higher probability of connection within same cluster
                        if cluster_assignments[i] == cluster_assignments[j]:
                            if np.random.rand() < 0.3:  # 30% within cluster
                                W[i, j] = np.random.uniform(-1, 1)
                        else:
                            if np.random.rand() < 0.05:  # 5% between clusters
                                W[i, j] = np.random.uniform(-0.5, 0.5)
                                
        else:
            # Default to sparse random
            W = np.random.uniform(-1, 1, size=(size, size))
            
            # Apply sparsity
            mask = np.random.rand(size, size) >= sparsity
            W[mask] = 0
        
        # Scale to target spectral radius
        current_radius = np.max(np.abs(np.linalg.eigvals(W)))
        if current_radius > 0:
            W = W * (spectral_radius / current_radius)
        else:
            logging.warning("Matrix has zero spectral radius, cannot scale properly")
        
        return W
    
    @staticmethod
    def visualize_topology(W: np.ndarray, title: str = "Reservoir Topology") -> None:
        """
        Visualize the topology of a reservoir weight matrix.
        
        Args:
            W: Reservoir weight matrix
            title: Plot title
        """
        # Create network graph from weight matrix
        G = nx.DiGraph()
        
        # Add nodes
        for i in range(W.shape[0]):
            G.add_node(i)
        
        # Add edges with weights
        for i in range(W.shape[0]):
            for j in range(W.shape[1]):
                if W[i, j] != 0:
                    G.add_edge(j, i, weight=abs(W[i, j]), sign=np.sign(W[i, j]))
        
        # Create plot
        plt.figure(figsize=(12, 10))
        
        # Calculate node positions
        pos = nx.spring_layout(G, seed=42)
        
        # Get edge weights and signs for coloring
        edge_weights = [abs(W[i, j]) * 3 for i, j in G.edges()]
        edge_colors = ['red' if W[i, j] < 0 else 'blue' for i, j in G.edges()]
        
        # Draw network
        nx.draw_networkx_nodes(G, pos, node_size=100, node_color='lightblue', alpha=0.8)
        nx.draw_networkx_edges(G, pos, width=edge_weights, alpha=0.5, 
                              edge_color=edge_colors, arrows=True, 
                              arrowsize=10, arrowstyle='->')
        
        plt.title(title)
        plt.axis('off')
        plt.tight_layout()
        plt.show()


class ReservoirInitializer:
    """
    Utility for initializing reservoir weights using different methods.
    
    Proper initialization is crucial for achieving good performance
    in reservoir computing systems.
    """
    
    @staticmethod
    def initialize_reservoir(method: ReservoirInitializationMethod,
                           config: ReservoirConfig) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """
        Initialize reservoir weights using specified method.
        
        Args:
            method: Initialization method
            config: Reservoir configuration
            
        Returns:
            Tuple of (W_in, W, bias)
        """
        # Get configuration parameters
        N_in = config.input_size
        N = config.reservoir_size
        N_out = config.output_size
        
        # Initialize input weights
        if method == ReservoirInitializationMethod.RANDOM_UNIFORM:
            # Random uniform initialization
            W_in = np.random.uniform(-config.input_scaling, 
                                   config.input_scaling, 
                                   size=(N, N_in))
        elif method == ReservoirInitializationMethod.RANDOM_NORMAL:
            # Random normal initialization
            W_in = np.random.randn(N, N_in) * config.input_scaling
        else:
            # Default to uniform
            W_in = np.random.uniform(-config.input_scaling, 
                                   config.input_scaling, 
                                   size=(N, N_in))
        
        # Initialize reservoir weights
        if method == ReservoirInitializationMethod.RANDOM_UNIFORM:
            # Random uniform initialization
            W = np.random.uniform(-1, 1, size=(N, N))
            
            # Apply sparsity
            mask = np.random.rand(N, N) >= config.sparsity
            W[mask] = 0
            
        elif method == ReservoirInitializationMethod.RANDOM_NORMAL:
            # Random normal initialization
            W = np.random.randn(N, N)
            
            # Apply sparsity
            mask = np.random.rand(N, N) >= config.sparsity
            W[mask] = 0
            
        elif method == ReservoirInitializationMethod.ORTHOGONAL:
            # Orthogonal initialization
            rand_matrix = np.random.randn(N, N)
            q, r = np.linalg.qr(rand_matrix)
            W = q
            
            # Apply sparsity
            mask = np.random.rand(N, N) >= config.sparsity
            W[mask] = 0
            
        elif method == ReservoirInitializationMethod.SPARSE_RANDOM:
            # Sparse random connectivity
            W = np.zeros((N, N))
            
            # Generate random indices for connections
            n_connections = int(config.sparsity * N * N)
            i_indices = np.random.randint(0, N, size=n_connections)
            j_indices = np.random.randint(0, N, size=n_connections)
            values = np.random.uniform(-1, 1, size=n_connections)
            
            # Set the connections
            for i, j, val in zip(i_indices, j_indices, values):
                W[i, j] = val
                
        elif method == ReservoirInitializationMethod.CONNECTIVITY_PATTERNED:
            # Use topology builder to create structured connectivity
            W = ReservoirTopologyBuilder.create_topology(
                topology_type=config.topology,
                size=N,
                sparsity=config.sparsity,
                spectral_radius=1.0  # Will be scaled later
            )
            
        else:
            # Default to sparse random
            W = np.random.uniform(-1, 1, size=(N, N))
            
            # Apply sparsity
            mask = np.random.rand(N, N) >= config.sparsity
            W[mask] = 0
        
        # Scale reservoir weight matrix to desired spectral radius
        current_radius = np.max(np.abs(np.linalg.eigvals(W)))
        if current_radius > 0:
            W = W * (config.spectral_radius / current_radius)
        
        # Initialize bias
        bias = np.random.uniform(-config.bias_scaling, 
                               config.bias_scaling, 
                               size=(N, 1))
        
        return W_in, W, bias
    
    @staticmethod
    def initialize_for_task(task_type: str, config: ReservoirConfig) -> ReservoirNetwork:
        """
        Initialize a reservoir optimized for a specific task type.
        
        Args:
            task_type: Type of task ('prediction', 'classification', 'memory')
            config: Base reservoir configuration
            
        Returns:
            Initialized reservoir network
        """
        # Clone the configuration
        task_config = ReservoirConfig(
            reservoir_type=config.reservoir_type,
            input_size=config.input_size,
            reservoir_size=config.reservoir_size,
            output_size=config.output_size,
            spectral_radius=config.spectral_radius,
            input_scaling=config.input_scaling,
            bias_scaling=config.bias_scaling,
            leakage_rate=config.leakage_rate,
            sparsity=config.sparsity,
            activation_function=config.activation_function,
            topology=config.topology,
            noise_level=config.noise_level,
            readout_type=config.readout_type,
            regularization=config.regularization,
            regularization_strength=config.regularization_strength,
            random_seed=config.random_seed,
            initialization_method=config.initialization_method,
            washout_length=config.washout_length
        )
        
        # Adjust parameters based on task type
        if task_type == 'prediction':
            # Time series prediction benefits from longer memory
            task_config.spectral_radius = 0.95  # Near but below 1 for stability
            task_config.leakage_rate = 0.3  # Slower dynamics
            task_config.input_scaling = 0.6  # Moderate input influence
            task_config.sparsity = 0.05  # Sparse connectivity
            task_config.topology = ReservoirTopology.SMALL_WORLD
            
        elif task_type == 'classification':
            # Classification benefits from better separation
            task_config.spectral_radius = 0.8  # Less memory/echo
            task_config.leakage_rate = 0.8  # Faster dynamics
            task_config.input_scaling = 1.2  # Stronger input influence
            task_config.sparsity = 0.1  # Medium sparsity
            task_config.topology = ReservoirTopology.RANDOM
            
        elif task_type == 'memory':
            # Memory tasks benefit from longer, stable echoes
            task_config.spectral_radius = 0.99  # Very close to 1 for long memory
            task_config.leakage_rate = 0.1  # Very slow dynamics
            task_config.input_scaling = 0.3  # Weaker input influence
            task_config.sparsity = 0.02  # Very sparse connectivity
            task_config.topology = ReservoirTopology.SMALL_WORLD
            
        else:
            # Default configuration
            pass
        
        # Create appropriate reservoir type
        if task_config.reservoir_type == ReservoirType.ESN:
            return ESN(task_config)
        elif task_config.reservoir_type == ReservoirType.LSM:
            return LSM(task_config)
        elif task_config.reservoir_type == ReservoirType.FORCE:
            return FORCE(task_config)
        else:
            # Default to ESN
            return ESN(task_config)


class TimeSeriesPredictor:
    """
    Specialized reservoir computing model for time series prediction.
    
    This class provides convenient methods for time series prediction tasks,
    including preprocessing, evaluation, and visualization.
    """
    
    def __init__(self, 
                 reservoir_type: ReservoirType = ReservoirType.ESN,
                 input_size: int = 1,
                 reservoir_size: int = 100,
                 output_size: int = 1,
                 spectral_radius: float = 0.95,
                 prediction_horizon: int = 1,
                 multi_step: bool = False):
        """
        Initialize time series predictor.
        
        Args:
            reservoir_type: Type of reservoir to use
            input_size: Input dimensionality
            reservoir_size: Size of the reservoir
            output_size: Output dimensionality
            spectral_radius: Spectral radius of reservoir weights
            prediction_horizon: Number of steps ahead to predict
            multi_step: Whether to perform multi-step prediction
        """
        # Store parameters
        self.input_size = input_size
        self.output_size = output_size
        self.prediction_horizon = prediction_horizon
        self.multi_step = multi_step
        
        # Create configuration
        self.config = ReservoirConfig(
            reservoir_type=reservoir_type,
            input_size=input_size,
            reservoir_size=reservoir_size,
            output_size=output_size,
            spectral_radius=spectral_radius,
            input_scaling=0.5,
            bias_scaling=0.1,
            leakage_rate=0.3,
            sparsity=0.05,
            activation_function=ActivationFunction.TANH,
            topology=ReservoirTopology.SMALL_WORLD,
            noise_level=0.0001,
            readout_type=ReadoutType.RIDGE_REGRESSION,
            regularization=RegularizationMethod.RIDGE,
            regularization_strength=1e-6,
            initialization_method=ReservoirInitializationMethod.SPECTRAL_NORMALIZED,
            washout_length=100
        )
        
        # Create reservoir
        if reservoir_type == ReservoirType.ESN:
            self.reservoir = ESN(self.config)
        elif reservoir_type == ReservoirType.LSM:
            self.reservoir = LSM(self.config)
        elif reservoir_type == ReservoirType.FORCE:
            self.reservoir = FORCE(self.config)
        else:
            # Default to ESN
            self.reservoir = ESN(self.config)
        
        # Preprocessing parameters
        self.scaler_mean = None
        self.scaler_std = None
        
        # Performance metrics
        self.metrics = {}
    
    def preprocess(self, data: np.ndarray) -> np.ndarray:
        """
        Preprocess input data (standardization).
        
        Args:
            data: Input data to preprocess
            
        Returns:
            Preprocessed data
        """
        # Ensure data is 2D
        if data.ndim == 1:
            data = data.reshape(-1, 1)
        
        # Store preprocessing parameters if not already set
        if self.scaler_mean is None:
            self.scaler_mean = np.mean(data, axis=0)
            self.scaler_std = np.std(data, axis=0)
            
            # Avoid division by zero
            self.scaler_std[self.scaler_std < 1e-8] = 1.0
        
        # Apply standardization
        return (data - self.scaler_mean) / self.scaler_std
    
    def inverse_preprocess(self, data: np.ndarray) -> np.ndarray:
        """
        Inverse preprocess (un-standardize) data.
        
        Args:
            data: Standardized data
            
        Returns:
            Original scale data
        """
        if self.scaler_mean is None or self.scaler_std is None:
            logging.warning("Scaler parameters not set. Cannot inverse transform.")
            return data
        
        # Inverse standardization
        return data * self.scaler_std + self.scaler_mean
    
    def prepare_data(self, 
                    time_series: np.ndarray, 
                    train_size: float = 0.8,
                    validation_size: float = 0.1) -> Dict[str, np.ndarray]:
        """
        Prepare data for training and testing.
        
        Args:
            time_series: Input time series data
            train_size: Proportion of data for training
            validation_size: Proportion of data for validation
            
        Returns:
            Dictionary containing train, validation, and test sets
        """
        # Ensure time_series is 2D
        if time_series.ndim == 1:
            time_series = time_series.reshape(-1, 1)
        
        # Get dimensions
        n_samples = time_series.shape[0]
        
        # Preprocess data
        scaled_data = self.preprocess(time_series)
        
        # Split into train, validation, and test sets
        train_end = int(n_samples * train_size)
        val_end = train_end + int(n_samples * validation_size)
        
        train_data = scaled_data[:train_end]
        val_data = scaled_data[train_end:val_end]
        test_data = scaled_data[val_end:]
        
        # Prepare input-output pairs for training
        X_train, y_train = self._create_io_pairs(train_data, self.prediction_horizon)
        X_val, y_val = self._create_io_pairs(val_data, self.prediction_horizon)
        X_test, y_test = self._create_io_pairs(test_data, self.prediction_horizon)
        
        # Store data
        data_dict = {
            'X_train': X_train,
            'y_train': y_train,
            'X_val': X_val,
            'y_val': y_val,
            'X_test': X_test,
            'y_test': y_test,
            'train_data': train_data,
            'val_data': val_data,
            'test_data': test_data
        }
        
        return data_dict
    
    def _create_io_pairs(self, data: np.ndarray, horizon: int) -> Tuple[np.ndarray, np.ndarray]:
        """
        Create input-output pairs from time series.
        
        Args:
            data: Time series data
            horizon: Prediction horizon
            
        Returns:
            Tuple of (inputs, targets)
        """
        # Get dimensions
        n_samples = data.shape[0]
        
        # Create input-output pairs
        inputs = data[:-horizon]
        targets = data[horizon:]
        
        return inputs, targets
    
    def train(self, X_train: np.ndarray, y_train: np.ndarray,
             X_val: Optional[np.ndarray] = None, y_val: Optional[np.ndarray] = None) -> Dict[str, float]:
        """
        Train the time series predictor.
        
        Args:
            X_train: Training inputs
            y_train: Training targets
            X_val: Validation inputs
            y_val: Validation targets
            
        Returns:
            Dictionary of training metrics
        """
        # Train the reservoir
        self.reservoir.train(X_train, y_train)
        
        # Compute training metrics
        train_pred = self.reservoir.predict(X_train)
        train_metrics = self.compute_metrics(train_pred, y_train, prefix='train')
        
        # Compute validation metrics if provided
        if X_val is not None and y_val is not None:
            val_pred = self.reservoir.predict(X_val)
            val_metrics = self.compute_metrics(val_pred, y_val, prefix='val')
            self.metrics.update(val_metrics)
        
        return train_metrics
    
    def predict(self, X: np.ndarray, n_steps: Optional[int] = None) -> np.ndarray:
        """
        Generate predictions for the given inputs.
        
        Args:
            X: Input data
            n_steps: Number of steps to predict (for multi-step prediction)
            
        Returns:
            Predicted values
        """
        if not self.multi_step or n_steps is None:
            # Single-step prediction
            return self.reservoir.predict(X)
        else:
            # Multi-step prediction
            return self.predict_multi_step(X, n_steps)
    
    def predict_multi_step(self, X_init: np.ndarray, n_steps: int) -> np.ndarray:
        """
        Generate multi-step predictions.
        
        Args:
            X_init: Initial input
            n_steps: Number of steps to predict
            
        Returns:
            Predicted time series
        """
        # Ensure X_init is 2D
        if X_init.ndim == 1:
            X_init = X_init.reshape(1, -1)
        
        # Initialize output array
        predictions = np.zeros((n_steps, self.output_size))
        
        # Use the provided initial input
        current_input = X_init[-1].copy()
        
        # Reset reservoir state
        self.reservoir.reset_state()
        
        # Initial state update with provided input
        self.reservoir.update_state(current_input)
        
        # Generate predictions iteratively
        for t in range(n_steps):
            # Predict next step
            next_step = self.reservoir.predict(current_input.reshape(1, -1))
            predictions[t] = next_step
            
            # Update input for next iteration
            current_input = next_step.flatten()
        
        return predictions
    
    def compute_metrics(self, predictions: np.ndarray, targets: np.ndarray,
                      prefix: str = '') -> Dict[str, float]:
        """
        Compute performance metrics.
        
        Args:
            predictions: Predicted values
            targets: Target values
            prefix: Prefix for metric names
            
        Returns:
            Dictionary of performance metrics
        """
        # Ensure both arrays have the same shape
        if predictions.shape != targets.shape:
            raise ValueError(f"Predictions and targets must have the same shape. "
                           f"Got {predictions.shape} and {targets.shape}")
        
        # Mean squared error
        mse = np.mean((predictions - targets) ** 2)
        
        # Root mean squared error
        rmse = np.sqrt(mse)
        
        # Normalized root mean squared error
        if np.std(targets) > 1e-8:
            nrmse = rmse / np.std(targets)
        else:
            nrmse = float('inf')
        
        # Mean absolute error
        mae = np.mean(np.abs(predictions - targets))
        
        # Mean absolute percentage error
        if np.all(np.abs(targets) > 1e-8):
            mape = np.mean(np.abs((targets - predictions) / targets)) * 100
        else:
            mape = float('inf')
        
        # Coefficient of determination (R²)
        ss_total = np.sum((targets - np.mean(targets, axis=0)) ** 2, axis=0)
        ss_residual = np.sum((targets - predictions) ** 2, axis=0)
        
        if np.all(ss_total > 1e-8):
            r2 = 1 - np.sum(ss_residual) / np.sum(ss_total)
        else:
            r2 = float('nan')
        
        # Create metrics dictionary with prefix
        prefix = prefix + '_' if prefix else ''
        metrics = {
            f'{prefix}mse': float(mse),
            f'{prefix}rmse': float(rmse),
            f'{prefix}nrmse': float(nrmse),
            f'{prefix}mae': float(mae),
            f'{prefix}mape': float(mape),
            f'{prefix}r2': float(r2)
        }
        
        # Update stored metrics
        self.metrics.update(metrics)
        
        return metrics
    
    def plot_predictions(self, 
                        true_values: np.ndarray, 
                        predictions: np.ndarray,
                        title: str = "Time Series Prediction",
                        start_idx: int = 0,
                        n_points: Optional[int] = None) -> None:
        """
        Plot true values vs predictions.
        
        Args:
            true_values: True time series values
            predictions: Predicted values
            title: Plot title
            start_idx: Starting index for plotting
            n_points: Number of points to plot
        """
        # Ensure data is in the right shape
        if true_values.ndim == 1:
            true_values = true_values.reshape(-1, 1)
        if predictions.ndim == 1:
            predictions = predictions.reshape(-1, 1)
        
        # Determine how many points to plot
        if n_points is None:
            n_points = len(true_values) - start_idx
        
        end_idx = min(start_idx + n_points, len(true_values))
        
        # Create x-axis indices
        x = np.arange(start_idx, end_idx)
        
        # If multiple outputs, create subplots
        n_outputs = true_values.shape[1]
        
        if n_outputs > 1:
            fig, axs = plt.subplots(n_outputs, 1, figsize=(12, 3 * n_outputs), sharex=True)
            
            for i in range(n_outputs):
                axs[i].plot(x, true_values[start_idx:end_idx, i], 'b-', label='True')
                axs[i].plot(x, predictions[start_idx:end_idx, i], 'r--', label='Predicted')
                axs[i].set_ylabel(f'Output {i+1}')
                axs[i].grid(True, linestyle='--', alpha=0.7)
                axs[i].legend()
                
            axs[-1].set_xlabel('Time Step')
            
        else:
            # Single output
            plt.figure(figsize=(12, 6))
            plt.plot(x, true_values[start_idx:end_idx], 'b-', label='True')
            plt.plot(x, predictions[start_idx:end_idx], 'r--', label='Predicted')
            plt.xlabel('Time Step')
            plt.ylabel('Value')
            plt.grid(True, linestyle='--', alpha=0.7)
            plt.legend()
        
        plt.suptitle(title)
        plt.tight_layout()
        plt.show()
    
    def save(self, filepath: str) -> None:
        """
        Save the trained predictor to a file.
        
        Args:
            filepath: Path to save the predictor
        """
        # Save the reservoir
        self.reservoir.save_model(filepath + '_reservoir.pkl')
        
        # Save predictor parameters
        predictor_data = {
            'input_size': self.input_size,
            'output_size': self.output_size,
            'prediction_horizon': self.prediction_horizon,
            'multi_step': self.multi_step,
            'config': self.config,
            'scaler_mean': self.scaler_mean,
            'scaler_std': self.scaler_std,
            'metrics': self.metrics
        }
        
        with open(filepath + '_predictor.pkl', 'wb') as f:
            pickle.dump(predictor_data, f)
        
        logging.info(f"Time series predictor saved to {filepath}")
    
    @classmethod
    def load(cls, filepath: str) -> 'TimeSeriesPredictor':
        """
        Load a trained predictor from a file.
        
        Args:
            filepath: Path to load the predictor from
            
        Returns:
            Loaded time series predictor
        """
        # Load predictor parameters
        with open(filepath + '_predictor.pkl', 'rb') as f:
            predictor_data = pickle.load(f)
        
        # Create predictor instance
        predictor = cls(
            reservoir_type=predictor_data['config'].reservoir_type,
            input_size=predictor_data['input_size'],
            reservoir_size=predictor_data['config'].reservoir_size,
            output_size=predictor_data['output_size'],
            spectral_radius=predictor_data['config'].spectral_radius,
            prediction_horizon=predictor_data['prediction_horizon'],
            multi_step=predictor_data['multi_step']
        )
        
        # Load reservoir
        if predictor_data['config'].reservoir_type == ReservoirType.ESN:
            predictor.reservoir = ESN.load_model(filepath + '_reservoir.pkl')
        elif predictor_data['config'].reservoir_type == ReservoirType.LSM:
            predictor.reservoir = LSM.load_model(filepath + '_reservoir.pkl')
        elif predictor_data['config'].reservoir_type == ReservoirType.FORCE:
            predictor.reservoir = FORCE.load_model(filepath + '_reservoir.pkl')
        
        # Set predictor parameters
        predictor.config = predictor_data['config']
        predictor.scaler_mean = predictor_data['scaler_mean']
        predictor.scaler_std = predictor_data['scaler_std']
        predictor.metrics = predictor_data['metrics']
        
        logging.info(f"Time series predictor loaded from {filepath}")
        
        return predictor


class PatternClassifier:
    """
    Specialized reservoir computing model for pattern classification.
    
    This class provides convenient methods for classification tasks,
    including preprocessing, evaluation, and visualization.
    """
    
    def __init__(self, 
                 reservoir_type: ReservoirType = ReservoirType.ESN,
                 input_size: int = 10,
                 reservoir_size: int = 200,
                 n_classes: int = 2,
                 spectral_radius: float = 0.8):
        """
        Initialize pattern classifier.
        
        Args:
            reservoir_type: Type of reservoir to use
            input_size: Input dimensionality
            reservoir_size: Size of the reservoir
            n_classes: Number of classes
            spectral_radius: Spectral radius of reservoir weights
        """
        # Store parameters
        self.input_size = input_size
        self.n_classes = n_classes
        
        # Create configuration
        self.config = ReservoirConfig(
            reservoir_type=reservoir_type,
            input_size=input_size,
            reservoir_size=reservoir_size,
            output_size=n_classes,
            spectral_radius=spectral_radius,
            input_scaling=1.0,
            bias_scaling=0.1,
            leakage_rate=0.7,
            sparsity=0.1,
            activation_function=ActivationFunction.TANH,
            topology=ReservoirTopology.RANDOM,
            noise_level=0.0001,
            readout_type=ReadoutType.RIDGE_REGRESSION,
            regularization=RegularizationMethod.RIDGE,
            regularization_strength=1e-4,
            initialization_method=ReservoirInitializationMethod.SPECTRAL_NORMALIZED,
            washout_length=10
        )
        
        # Create reservoir
        if reservoir_type == ReservoirType.ESN:
            self.reservoir = ESN(self.config)
        elif reservoir_type == ReservoirType.LSM:
            self.reservoir = LSM(self.config)
        elif reservoir_type == ReservoirType.FORCE:
            self.reservoir = FORCE(self.config)
        else:
            # Default to ESN
            self.reservoir = ESN(self.config)
        
        # Preprocessing parameters
        self.scaler_mean = None
        self.scaler_std = None
        
        # Class mapping
        self.classes = None
        
        # Performance metrics
        self.metrics = {}
    
    def preprocess(self, X: np.ndarray) -> np.ndarray:
        """
        Preprocess input data (standardization).
        
        Args:
            X: Input data to preprocess
            
        Returns:
            Preprocessed data
        """
        # Store preprocessing parameters if not already set
        if self.scaler_mean is None:
            self.scaler_mean = np.mean(X, axis=0)
            self.scaler_std = np.std(X, axis=0)
            
            # Avoid division by zero
            self.scaler_std[self.scaler_std < 1e-8] = 1.0
        
        # Apply standardization
        return (X - self.scaler_mean) / self.scaler_std
    
    def prepare_labels(self, y: np.ndarray) -> np.ndarray:
        """
        Prepare class labels for training.
        
        Args:
            y: Class labels
            
        Returns:
            One-hot encoded labels
        """
        # Get unique classes if not already set
        if self.classes is None:
            self.classes = np.unique(y)
            
            # Check number of classes
            if len(self.classes) != self.n_classes:
                logging.warning(f"Number of unique classes ({len(self.classes)}) "
                                f"doesn't match n_classes ({self.n_classes})")
                
                # Update n_classes if needed
                self.n_classes = len(self.classes)
                
                # Update output size in reservoir
                if self.reservoir.config.output_size != self.n_classes:
                    self.reservoir.config.output_size = self.n_classes
                    self.reservoir.W_out = np.zeros((self.n_classes, self.reservoir.config.reservoir_size))
        
        # Convert labels to one-hot encoding
        y_onehot = np.zeros((len(y), self.n_classes))
        for i, label in enumerate(y):
            class_idx = np.where(self.classes == label)[0][0]
            y_onehot[i, class_idx] = 1
        
        return y_onehot
    
    def train(self, X_train: np.ndarray, y_train: np.ndarray,
             X_val: Optional[np.ndarray] = None, y_val: Optional[np.ndarray] = None) -> Dict[str, float]:
        """
        Train the pattern classifier.
        
        Args:
            X_train: Training features
            y_train: Training labels
            X_val: Validation features
            y_val: Validation labels
            
        Returns:
            Dictionary of training metrics
        """
        # Preprocess inputs
        X_train_prep = self.preprocess(X_train)
        
        # Prepare labels
        y_train_onehot = self.prepare_labels(y_train)
        
        # Train the reservoir
        self.reservoir.train(X_train_prep, y_train_onehot)
        
        # Compute training metrics
        train_pred = self.predict(X_train)
        train_metrics = self.compute_metrics(train_pred, y_train, prefix='train')
        
        # Compute validation metrics if provided
        if X_val is not None and y_val is not None:
            val_pred = self.predict(X_val)
            val_metrics = self.compute_metrics(val_pred, y_val, prefix='val')
            self.metrics.update(val_metrics)
        
        return train_metrics
    
    def predict(self, X: np.ndarray, return_proba: bool = False) -> np.ndarray:
        """
        Generate predictions for the given inputs.
        
        Args:
            X: Input features
            return_proba: Whether to return class probabilities
            
        Returns:
            Predicted labels or class probabilities
        """
        # Preprocess inputs
        X_prep = self.preprocess(X)
        
        # Get raw outputs from reservoir
        outputs = self.reservoir.predict(X_prep)
        
        if return_proba:
            # Convert to probabilities using softmax
            proba = np.zeros_like(outputs)
            for i in range(outputs.shape[0]):
                exp_outputs = np.exp(outputs[i] - np.max(outputs[i]))
                proba[i] = exp_outputs / np.sum(exp_outputs)
            return proba
        else:
            # Convert to class labels
            pred_classes = np.argmax(outputs, axis=1)
            return self.classes[pred_classes]
    
    def compute_metrics(self, y_pred: np.ndarray, y_true: np.ndarray,
                      prefix: str = '') -> Dict[str, float]:
        """
        Compute classification performance metrics.
        
        Args:
            y_pred: Predicted labels
            y_true: True labels
            prefix: Prefix for metric names
            
        Returns:
            Dictionary of performance metrics
        """
        # Compute accuracy
        accuracy = np.mean(y_pred == y_true)
        
        # Compute confusion matrix
        cm = np.zeros((self.n_classes, self.n_classes), dtype=int)
        for i in range(len(y_true)):
            true_idx = np.where(self.classes == y_true[i])[0][0]
            pred_idx = np.where(self.classes == y_pred[i])[0][0]
            cm[true_idx, pred_idx] += 1
        
        # Compute precision and recall for each class
        precision = np.zeros(self.n_classes)
        recall = np.zeros(self.n_classes)
        f1 = np.zeros(self.n_classes)
        
        for i in range(self.n_classes):
            # Precision: TP / (TP + FP)
            precision[i] = cm[i, i] / max(np.sum(cm[:, i]), 1)
            
            # Recall: TP / (TP + FN)
            recall[i] = cm[i, i] / max(np.sum(cm[i, :]), 1)
            
            # F1 score: 2 * (precision * recall) / (precision + recall)
            if precision[i] + recall[i] > 0:
                f1[i] = 2 * precision[i] * recall[i] / (precision[i] + recall[i])
            else:
                f1[i] = 0
        
        # Average metrics
        avg_precision = np.mean(precision)
        avg_recall = np.mean(recall)
        avg_f1 = np.mean(f1)
        
        # Create metrics dictionary with prefix
        prefix = prefix + '_' if prefix else ''
        metrics = {
            f'{prefix}accuracy': float(accuracy),
            f'{prefix}precision': float(avg_precision),
            f'{prefix}recall': float(avg_recall),
            f'{prefix}f1': float(avg_f1)
        }
        
        # Add per-class metrics
        for i in range(self.n_classes):
            metrics[f'{prefix}precision_class{i}'] = float(precision[i])
            metrics[f'{prefix}recall_class{i}'] = float(recall[i])
            metrics[f'{prefix}f1_class{i}'] = float(f1[i])
        
        # Store confusion matrix
        if prefix:
            self.metrics[f'{prefix}confusion_matrix'] = cm
        else:
            self.metrics['confusion_matrix'] = cm
        
        # Update stored metrics
        self.metrics.update(metrics)
        
        return metrics
    
    def plot_confusion_matrix(self, 
                             y_pred: np.ndarray, 
                             y_true: np.ndarray,
                             title: str = "Confusion Matrix",
                             class_names: Optional[List[str]] = None) -> None:
        """
        Plot confusion matrix.
        
        Args:
            y_pred: Predicted labels
            y_true: True labels
            title: Plot title
            class_names: Names for classes
        """
        # Compute confusion matrix
        cm = np.zeros((self.n_classes, self.n_classes), dtype=int)
        for i in range(len(y_true)):
            true_idx = np.where(self.classes == y_true[i])[0][0]
            pred_idx = np.where(self.classes == y_pred[i])[0][0]
            cm[true_idx, pred_idx] += 1
        
        # Create figure
        plt.figure(figsize=(8, 6))
        
        # Normalize confusion matrix
        cm_norm = cm.astype('float') / cm.sum(axis=1)[:, np.newaxis]
        
        # Create heatmap
        plt.imshow(cm_norm, interpolation='nearest', cmap=plt.cm.Blues)
        plt.colorbar()
        
        # Add labels
        if class_names is None:
            class_names = [str(c) for c in self.classes]
            
        tick_marks = np.arange(self.n_classes)
        plt.xticks(tick_marks, class_names, rotation=45)
        plt.yticks(tick_marks, class_names)
        
        # Add values to cells
        thresh = cm_norm.max() / 2.0
        for i in range(cm_norm.shape[0]):
            for j in range(cm_norm.shape[1]):
                plt.text(j, i, f"{cm[i, j]}\n({cm_norm[i, j]:.2f})",
                       horizontalalignment="center",
                       color="white" if cm_norm[i, j] > thresh else "black")
        
        plt.tight_layout()
        plt.ylabel('True label')
        plt.xlabel('Predicted label')
        plt.title(title)
        
        plt.show()
    
    def save(self, filepath: str) -> None:
        """
        Save the trained classifier to a file.
        
        Args:
            filepath: Path to save the classifier
        """
        # Save the reservoir
        self.reservoir.save_model(filepath + '_reservoir.pkl')
        
        # Save classifier parameters
        classifier_data = {
            'input_size': self.input_size,
            'n_classes': self.n_classes,
            'config': self.config,
            'scaler_mean': self.scaler_mean,
            'scaler_std': self.scaler_std,
            'classes': self.classes,
            'metrics': self.metrics
        }
        
        with open(filepath + '_classifier.pkl', 'wb') as f:
            pickle.dump(classifier_data, f)
        
        logging.info(f"Pattern classifier saved to {filepath}")
    
    @classmethod
    def load(cls, filepath: str) -> 'PatternClassifier':
        """
        Load a trained classifier from a file.
        
        Args:
            filepath: Path to load the classifier from
            
        Returns:
            Loaded pattern classifier
        """
        # Load classifier parameters
        with open(filepath + '_classifier.pkl', 'rb') as f:
            classifier_data = pickle.load(f)
        
        # Create classifier instance
        classifier = cls(
            reservoir_type=classifier_data['config'].reservoir_type,
            input_size=classifier_data['input_size'],
            reservoir_size=classifier_data['config'].reservoir_size,
            n_classes=classifier_data['n_classes'],
            spectral_radius=classifier_data['config'].spectral_radius
        )
        
        # Load reservoir
        if classifier_data['config'].reservoir_type == ReservoirType.ESN:
            classifier.reservoir = ESN.load_model(filepath + '_reservoir.pkl')
        elif classifier_data['config'].reservoir_type == ReservoirType.LSM:
            classifier.reservoir = LSM.load_model(filepath + '_reservoir.pkl')
        elif classifier_data['config'].reservoir_type == ReservoirType.FORCE:
            classifier.reservoir = FORCE.load_model(filepath + '_reservoir.pkl')
        
        # Set classifier parameters
        classifier.config = classifier_data['config']
        classifier.scaler_mean = classifier_data['scaler_mean']
        classifier.scaler_std = classifier_data['scaler_std']
        classifier.classes = classifier_data['classes']
        classifier.metrics = classifier_data['metrics']
        
        logging.info(f"Pattern classifier loaded from {filepath}")
        
        return classifier


def initialize_reservoir_computing(config=None):
    """
    Initialize the reservoir computing module with the given configuration.
    
    Args:
        config: Optional configuration dictionary
        
    Returns:
        Status dictionary
    """
    logger.info("Initializing Reservoir Computing System")
    
    status = {
        'reservoir_types_available': len(list(ReservoirType)),
        'topology_types_available': len(list(ReservoirTopology)),
        'initialization_complete': True
    }
    
    if config is not None:
        # Apply configuration if provided
        if 'logging_level' in config:
            logger.setLevel(config['logging_level'])
            
        # Additional configuration can be applied here
    
    return status