#!/usr/bin/env python3
"""
ULTRA: Neuromorphic Processing Layer - Event-Based Computing

This module implements asynchronous event-based computing mechanisms inspired by neuromorphic
systems like Dynamic Vision Sensors (DVS) and SpiNNaker. Event-based processing fundamentally 
differs from traditional frame-based approaches by only processing data when meaningful changes 
occur, leading to significant improvements in power efficiency, latency, and dynamic range.

Components:
1. Event representation and management
2. Event-based sensors and interfaces
3. Event-driven neural networks
4. Temporal coding and processing
5. Event-based filters and feature extractors
"""

import numpy as np
import heapq
from typing import Dict, List, Tuple, Set, Any, Optional, Union, Callable, Type, NamedTuple
from dataclasses import dataclass, field
from enum import Enum, auto
import logging
import time
import matplotlib.pyplot as plt
from collections import defaultdict, deque
import copy

logger = logging.getLogger(__name__)


class EventType(Enum):
    """Types of events in the event-based system."""
    SPIKE = auto()           # Neuron spike event
    CHANGE = auto()          # Value change event (e.g., pixel intensity)
    EDGE = auto()            # Edge detection event (ON/OFF)
    FEATURE = auto()         # Feature detection event
    TIMER = auto()           # Timer/clock event
    EXTERNAL = auto()        # External input event
    INTERNAL = auto()        # Internal system event
    CUSTOM = auto()          # Custom event type


@dataclass(order=True)
class Event:
    """
    Base class for events in the event-based system.
    
    Events are the fundamental unit of computation in event-based systems.
    Each event represents a significant change or occurrence at a specific
    time and location.
    """
    # Priority field for the priority queue (usually timestamp)
    priority: float
    
    # These fields are not used for ordering (only timestamp is)
    timestamp: float = field(compare=False)  # Event timestamp
    source_id: int = field(compare=False)    # Source ID (e.g., neuron, pixel)
    event_type: EventType = field(compare=False)  # Type of event
    polarity: int = field(compare=False, default=1)  # Event polarity (1: ON, -1: OFF)
    payload: Any = field(compare=False, default=None)  # Additional data payload
    
    def __post_init__(self):
        """Ensure priority is set to timestamp if not explicitly provided."""
        if hasattr(self, 'priority') and self.priority is None:
            self.priority = self.timestamp


class SpikeEvent(Event):
    """
    Spike event from a neuron.
    
    This represents the firing of a spiking neuron at a specific time.
    """
    
    def __init__(
        self,
        timestamp: float,
        neuron_id: int,
        payload: Any = None
    ):
        """
        Initialize spike event.
        
        Args:
            timestamp: Event timestamp
            neuron_id: ID of the neuron that fired
            payload: Additional data associated with the spike
        """
        super().__init__(
            priority=timestamp,
            timestamp=timestamp,
            source_id=neuron_id,
            event_type=EventType.SPIKE,
            polarity=1,
            payload=payload
        )


class ChangeEvent(Event):
    """
    Change detection event.
    
    This represents a significant change in value (e.g., pixel intensity).
    """
    
    def __init__(
        self,
        timestamp: float,
        source_id: int,
        polarity: int,
        magnitude: float,
        coordinates: Tuple[int, ...] = None,
        payload: Any = None
    ):
        """
        Initialize change event.
        
        Args:
            timestamp: Event timestamp
            source_id: ID of the source (e.g., pixel)
            polarity: Event polarity (1: increase, -1: decrease)
            magnitude: Change magnitude
            coordinates: Optional spatial coordinates
            payload: Additional data associated with the event
        """
        payload_dict = {
            'magnitude': magnitude,
            'coordinates': coordinates
        }
        if payload is not None:
            if isinstance(payload, dict):
                payload_dict.update(payload)
            else:
                payload_dict['data'] = payload
                
        super().__init__(
            priority=timestamp,
            timestamp=timestamp,
            source_id=source_id,
            event_type=EventType.CHANGE,
            polarity=polarity,
            payload=payload_dict
        )
        
    @property
    def magnitude(self) -> float:
        """Get change magnitude."""
        return self.payload.get('magnitude', 0.0)
        
    @property
    def coordinates(self) -> Optional[Tuple[int, ...]]:
        """Get spatial coordinates."""
        return self.payload.get('coordinates')


class EdgeEvent(Event):
    """
    Edge detection event.
    
    This represents the detection of an edge in visual data.
    """
    
    def __init__(
        self,
        timestamp: float,
        source_id: int,
        polarity: int,  # 1: ON edge, -1: OFF edge
        coordinates: Tuple[int, ...],
        orientation: Optional[float] = None,
        strength: float = 1.0,
        payload: Any = None
    ):
        """
        Initialize edge event.
        
        Args:
            timestamp: Event timestamp
            source_id: ID of the source
            polarity: Edge polarity (1: ON, -1: OFF)
            coordinates: Spatial coordinates of the edge
            orientation: Edge orientation in radians (if available)
            strength: Edge strength
            payload: Additional data associated with the event
        """
        payload_dict = {
            'coordinates': coordinates,
            'orientation': orientation,
            'strength': strength
        }
        if payload is not None:
            if isinstance(payload, dict):
                payload_dict.update(payload)
            else:
                payload_dict['data'] = payload
                
        super().__init__(
            priority=timestamp,
            timestamp=timestamp,
            source_id=source_id,
            event_type=EventType.EDGE,
            polarity=polarity,
            payload=payload_dict
        )


class TimerEvent(Event):
    """
    Timer event for scheduled operations.
    
    This represents a timed event that occurs at a specific time,
    useful for implementing delays and periodic operations.
    """
    
    def __init__(
        self,
        timestamp: float,
        source_id: int,
        callback: Optional[Callable] = None,
        payload: Any = None
    ):
        """
        Initialize timer event.
        
        Args:
            timestamp: Event timestamp (when to trigger)
            source_id: ID of the event source
            callback: Optional callback function to execute
            payload: Additional data for the callback
        """
        payload_dict = {'callback': callback}
        if payload is not None:
            if isinstance(payload, dict):
                payload_dict.update(payload)
            else:
                payload_dict['data'] = payload
                
        super().__init__(
            priority=timestamp,
            timestamp=timestamp,
            source_id=source_id,
            event_type=EventType.TIMER,
            payload=payload_dict
        )


class EventQueue:
    """
    Priority queue for event-based processing.
    
    This queue manages events ordered by their timestamps and
    provides efficient access to the earliest events.
    """
    
    def __init__(self):
        """Initialize event queue."""
        self.queue = []  # Priority queue
        self.current_time = 0.0
        self.event_counter = 0
    
    def push(self, event: Event) -> None:
        """
        Add event to the queue.
        
        Args:
            event: Event to add
        """
        # Ensure event timestamp is not earlier than current time
        if event.timestamp < self.current_time:
            logger.warning(f"Event timestamp {event.timestamp} is earlier than current time {self.current_time}. Adjusting to current time.")
            event.timestamp = self.current_time
            event.priority = self.current_time
            
        heapq.heappush(self.queue, event)
        self.event_counter += 1
    
    def pop(self) -> Optional[Event]:
        """
        Remove and return the earliest event.
        
        Returns:
            Earliest event or None if queue is empty
        """
        if not self.queue:
            return None
            
        event = heapq.heappop(self.queue)
        self.current_time = max(self.current_time, event.timestamp)
        return event
    
    def peek(self) -> Optional[Event]:
        """
        Return the earliest event without removing it.
        
        Returns:
            Earliest event or None if queue is empty
        """
        if not self.queue:
            return None
            
        return self.queue[0]
    
    def get_events_until(self, time: float) -> List[Event]:
        """
        Get all events up to the specified time.
        
        Args:
            time: Upper time limit
            
        Returns:
            List of events with timestamps <= time
        """
        events = []
        
        while self.queue and self.queue[0].timestamp <= time:
            events.append(heapq.heappop(self.queue))
            
        self.current_time = max(self.current_time, time)
        return events
    
    def clear(self) -> None:
        """Clear all events from the queue."""
        self.queue = []
    
    def __len__(self) -> int:
        """Return number of events in the queue."""
        return len(self.queue)
    
    def get_time_range(self) -> Tuple[float, float]:
        """
        Get time range of events in queue.
        
        Returns:
            Tuple of (earliest_time, latest_time)
        """
        if not self.queue:
            return (self.current_time, self.current_time)
            
        earliest = self.queue[0].timestamp
        latest = max(event.timestamp for event in self.queue)
        
        return (earliest, latest)
    
    def update_priorities(self) -> None:
        """Re-establish heap property after changes to event priorities."""
        heapq.heapify(self.queue)


class EventProcessor:
    """
    Base class for event-based processing components.
    
    This is the foundation for building event-based processing
    pipelines, where each component receives, processes, and
    potentially generates events.
    """
    
    def __init__(self, processor_id: int):
        """
        Initialize event processor.
        
        Args:
            processor_id: Unique identifier for this processor
        """
        self.processor_id = processor_id
        self.input_queue = EventQueue()
        self.output_events = []
        self.connected_processors = []
        self.enabled = True
        self.event_stats = {
            'processed': 0,
            'generated': 0,
            'dropped': 0
        }
        self.processing_time = 0.0
        
    def process_event(self, event: Event) -> List[Event]:
        """
        Process a single event.
        
        This method should be overridden by subclasses to implement
        specific event processing logic.
        
        Args:
            event: Event to process
            
        Returns:
            List of output events (can be empty)
        """
        # Base implementation just passes through the event
        return [event]
    
    def receive_event(self, event: Event) -> None:
        """
        Receive an event from another processor.
        
        Args:
            event: Event to receive
        """
        if not self.enabled:
            self.event_stats['dropped'] += 1
            return
            
        self.input_queue.push(event)
    
    def connect_to(self, processor: 'EventProcessor') -> None:
        """
        Connect this processor to another processor.
        
        Args:
            processor: Target processor to connect to
        """
        if processor not in self.connected_processors:
            self.connected_processors.append(processor)
    
    def send_events(self, events: List[Event]) -> None:
        """
        Send events to connected processors.
        
        Args:
            events: Events to send
        """
        for event in events:
            self.output_events.append(event)
            self.event_stats['generated'] += 1
            
            for processor in self.connected_processors:
                processor.receive_event(copy.deepcopy(event))
    
    def process_next_event(self) -> bool:
        """
        Process the next event in the input queue.
        
        Returns:
            True if an event was processed, False if queue is empty
        """
        event = self.input_queue.pop()
        if event is None:
            return False
            
        start_time = time.time()
        output_events = self.process_event(event)
        end_time = time.time()
        
        self.processing_time += (end_time - start_time)
        self.event_stats['processed'] += 1
        
        if output_events:
            self.send_events(output_events)
            
        return True
    
    def process_all_events(self) -> int:
        """
        Process all events in the input queue.
        
        Returns:
            Number of events processed
        """
        count = 0
        while self.process_next_event():
            count += 1
            
        return count
    
    def process_events_until(self, time: float) -> int:
        """
        Process all events up to the specified time.
        
        Args:
            time: Upper time limit
            
        Returns:
            Number of events processed
        """
        events = self.input_queue.get_events_until(time)
        count = 0
        
        for event in events:
            start_time = time.time()
            output_events = self.process_event(event)
            end_time = time.time()
            
            self.processing_time += (end_time - start_time)
            self.event_stats['processed'] += 1
            count += 1
            
            if output_events:
                self.send_events(output_events)
                
        return count
    
    def get_stats(self) -> Dict[str, Any]:
        """
        Get processor statistics.
        
        Returns:
            Dictionary of statistics
        """
        stats = self.event_stats.copy()
        stats['queue_size'] = len(self.input_queue)
        stats['processing_time'] = self.processing_time
        if stats['processed'] > 0:
            stats['avg_processing_time'] = self.processing_time / stats['processed']
        else:
            stats['avg_processing_time'] = 0.0
            
        return stats
    
    def reset(self) -> None:
        """Reset processor state."""
        self.input_queue.clear()
        self.output_events = []
        self.event_stats = {
            'processed': 0,
            'generated': 0,
            'dropped': 0
        }
        self.processing_time = 0.0


class EventFilter(EventProcessor):
    """
    Filter for selecting or rejecting events based on criteria.
    
    This component allows filtering events based on custom criteria,
    enabling the construction of event processing pipelines that focus
    on specific types or properties of events.
    """
    
    def __init__(
        self, 
        processor_id: int,
        filter_function: Optional[Callable[[Event], bool]] = None,
        event_types: Optional[List[EventType]] = None,
        time_window: Optional[Tuple[float, float]] = None,
        source_ids: Optional[List[int]] = None,
        polarity: Optional[int] = None
    ):
        """
        Initialize event filter.
        
        Args:
            processor_id: Unique identifier for this processor
            filter_function: Custom filtering function (returns True to keep event)
            event_types: List of event types to accept (None = accept all)
            time_window: (start_time, end_time) to accept (None = no time filter)
            source_ids: List of source IDs to accept (None = accept all)
            polarity: Event polarity to accept (None = accept all)
        """
        super().__init__(processor_id)
        self.filter_function = filter_function
        self.event_types = set(event_types) if event_types else None
        self.time_window = time_window
        self.source_ids = set(source_ids) if source_ids else None
        self.polarity = polarity
        
    def process_event(self, event: Event) -> List[Event]:
        """
        Filter event based on criteria.
        
        Args:
            event: Event to filter
            
        Returns:
            List containing the event if it passes filters, otherwise empty list
        """
        # Apply type filter
        if self.event_types is not None and event.event_type not in self.event_types:
            return []
            
        # Apply time window filter
        if self.time_window is not None:
            start_time, end_time = self.time_window
            if event.timestamp < start_time or event.timestamp >= end_time:
                return []
                
        # Apply source ID filter
        if self.source_ids is not None and event.source_id not in self.source_ids:
            return []
            
        # Apply polarity filter
        if self.polarity is not None and event.polarity != self.polarity:
            return []
            
        # Apply custom filter function
        if self.filter_function is not None and not self.filter_function(event):
            return []
            
        # Event passed all filters
        return [event]
    
    def set_filter_function(self, filter_function: Callable[[Event], bool]) -> None:
        """
        Set custom filter function.
        
        Args:
            filter_function: Function that returns True for events to keep
        """
        self.filter_function = filter_function
    
    def set_event_types(self, event_types: List[EventType]) -> None:
        """
        Set event types to accept.
        
        Args:
            event_types: List of event types to accept
        """
        self.event_types = set(event_types) if event_types else None
    
    def set_time_window(self, start_time: float, end_time: float) -> None:
        """
        Set time window for filtering.
        
        Args:
            start_time: Start time (inclusive)
            end_time: End time (exclusive)
        """
        self.time_window = (start_time, end_time)
    
    def set_source_ids(self, source_ids: List[int]) -> None:
        """
        Set source IDs to accept.
        
        Args:
            source_ids: List of source IDs to accept
        """
        self.source_ids = set(source_ids) if source_ids else None
    
    def set_polarity(self, polarity: Optional[int]) -> None:
        """
        Set polarity to accept.
        
        Args:
            polarity: Polarity to accept (None = accept all)
        """
        self.polarity = polarity


class TemporalIntegrator(EventProcessor):
    """
    Integrates events over time to extract temporal patterns.
    
    This component accumulates events within a sliding time window
    and can detect temporal patterns, compute statistics, or generate
    new events based on the accumulated data.
    """
    
    def __init__(
        self,
        processor_id: int,
        window_size: float,
        stride: Optional[float] = None,
        decay_function: Optional[Callable[[float], float]] = None,
        event_types: Optional[List[EventType]] = None,
        aggregation_function: Optional[Callable[[List[Event]], List[Event]]] = None
    ):
        """
        Initialize temporal integrator.
        
        Args:
            processor_id: Unique identifier for this processor
            window_size: Time window size in time units
            stride: Window stride for sliding window (if None, use trigger-based)
            decay_function: Function defining weight decay over time
            event_types: Types of events to integrate (None = all types)
            aggregation_function: Function to process accumulated events
        """
        super().__init__(processor_id)
        self.window_size = window_size
        self.stride = stride
        self.event_types = set(event_types) if event_types else None
        
        # Set default decay function if none provided
        if decay_function is None:
            # Default: linear decay from 1.0 to 0.0 over window
            self.decay_function = lambda dt: max(0.0, 1.0 - dt / window_size)
        else:
            self.decay_function = decay_function
            
        self.aggregation_function = aggregation_function
        
        # Event buffer: stores events in the current window
        self.event_buffer = []
        self.buffer_start_time = 0.0
        self.last_process_time = 0.0
        
        # Accumulated state
        self.state = {}  # source_id -> accumulated value
        
        # For sliding window
        if self.stride is not None:
            self.next_window_time = 0.0  # Time for next window processing
    
    def process_event(self, event: Event) -> List[Event]:
        """
        Process an event, integrating it into the temporal window.
        
        Args:
            event: Event to process
            
        Returns:
            List of output events (can be empty)
        """
        # Check if event type should be integrated
        if self.event_types is not None and event.event_type not in self.event_types:
            return [event]  # Pass through non-integrated event types
            
        # Update buffer time range
        current_time = event.timestamp
        
        # For sliding window approach
        if self.stride is not None:
            # Initialize next window time if needed
            if self.next_window_time == 0.0:
                self.next_window_time = current_time + self.stride
                self.buffer_start_time = current_time
                
            # Check if we need to process a window
            while current_time >= self.next_window_time:
                # Remove old events outside the window
                window_start = self.next_window_time - self.window_size
                self.event_buffer = [e for e in self.event_buffer 
                                   if e.timestamp >= window_start]
                
                # Process the window
                output_events = self._process_window(self.next_window_time)
                
                # Update for next window
                self.next_window_time += self.stride
                
                # Return events if any were generated
                if output_events:
                    return output_events
        
        # Add the event to the buffer
        self.event_buffer.append(event)
        
        # Update state based on the event
        self._update_state(event)
        
        # For trigger-based approach (no stride)
        if self.stride is None:
            # Remove events outside the window
            window_start = current_time - self.window_size
            self.event_buffer = [e for e in self.event_buffer 
                               if e.timestamp >= window_start]
            
            # Process accumulated events if they meet criteria
            # (Here, we're just using a time gap as a simple trigger)
            if current_time - self.last_process_time >= self.window_size / 4:
                output_events = self._process_window(current_time)
                self.last_process_time = current_time
                return output_events
                
        return []
    
    def _update_state(self, event: Event) -> None:
        """
        Update accumulated state based on event.
        
        Args:
            event: Event to incorporate into state
        """
        source_id = event.source_id
        
        # Initialize state for this source if needed
        if source_id not in self.state:
            self.state[source_id] = 0.0
            
        # Update state based on event
        # For CHANGE events, use magnitude and polarity
        if event.event_type == EventType.CHANGE:
            magnitude = event.payload.get('magnitude', 1.0)
            self.state[source_id] += event.polarity * magnitude
            
        # For SPIKE events, just add 1.0
        elif event.event_type == EventType.SPIKE:
            self.state[source_id] += 1.0
            
        # For other events, default to adding polarity
        else:
            self.state[source_id] += event.polarity
    
    def _process_window(self, window_end: float) -> List[Event]:
        """
        Process events in the current window.
        
        Args:
            window_end: End time of the current window
            
        Returns:
            List of output events
        """
        # If no events in buffer, nothing to process
        if not self.event_buffer:
            return []
            
        # If aggregation function provided, use it
        if self.aggregation_function is not None:
            return self.aggregation_function(self.event_buffer)
            
        # Default processing: generate summary event
        window_start = window_end - self.window_size
        
        # Count events by type and source
        counts = defaultdict(int)
        for event in self.event_buffer:
            key = (event.event_type, event.source_id)
            counts[key] += 1
            
        # Generate summary event for each active source
        output_events = []
        
        for (event_type, source_id), count in counts.items():
            if count > 0:
                # Create summary event
                summary_event = Event(
                    priority=window_end,
                    timestamp=window_end,
                    source_id=self.processor_id,
                    event_type=event_type,
                    payload={
                        'source_id': source_id,
                        'count': count,
                        'rate': count / self.window_size,
                        'window': (window_start, window_end),
                        'state': self.state.get(source_id, 0.0)
                    }
                )
                output_events.append(summary_event)
                
        return output_events
    
    def set_window_size(self, window_size: float) -> None:
        """
        Set the temporal window size.
        
        Args:
            window_size: New window size in time units
        """
        self.window_size = window_size
        
        # Update decay function if using default
        if not hasattr(self, 'custom_decay_function'):
            self.decay_function = lambda dt: max(0.0, 1.0 - dt / window_size)
    
    def set_decay_function(self, decay_function: Callable[[float], float]) -> None:
        """
        Set custom decay function.
        
        Args:
            decay_function: Function mapping time delta to weight (0.0-1.0)
        """
        self.decay_function = decay_function
        self.custom_decay_function = True
    
    def reset(self) -> None:
        """Reset processor state."""
        super().reset()
        self.event_buffer = []
        self.buffer_start_time = 0.0
        self.last_process_time = 0.0
        self.state = {}
        if self.stride is not None:
            self.next_window_time = 0.0


class FeatureExtractor(EventProcessor):
    """
    Extracts features from event streams.
    
    This component analyzes event patterns to extract higher-level
    features, such as motion direction, object boundaries, or temporal patterns.
    """
    
    def __init__(
        self,
        processor_id: int,
        feature_type: str,
        parameters: Dict[str, Any] = None,
        spatial_dimensions: Optional[Tuple[int, ...]] = None
    ):
        """
        Initialize feature extractor.
        
        Args:
            processor_id: Unique identifier for this processor
            feature_type: Type of feature to extract ('edge', 'motion', 'temporal', 'corner', etc.)
            parameters: Parameters for the feature extraction algorithm
            spatial_dimensions: Spatial dimensions (if applicable)
        """
        super().__init__(processor_id)
        self.feature_type = feature_type.lower()
        self.parameters = parameters if parameters else {}
        self.spatial_dimensions = spatial_dimensions
        
        # Feature-specific state
        self.state = {}
        
        # Initialize based on feature type
        if self.feature_type == 'edge':
            self._init_edge_detector()
        elif self.feature_type == 'motion':
            self._init_motion_detector()
        elif self.feature_type == 'corner':
            self._init_corner_detector()
        elif self.feature_type == 'temporal':
            self._init_temporal_detector()
            
    def _init_edge_detector(self) -> None:
        """Initialize edge detector state."""
        # Edge detector parameters
        self.threshold = self.parameters.get('threshold', 0.5)
        self.window_size = self.parameters.get('window_size', 10.0)  # ms
        self.min_events = self.parameters.get('min_events', 3)
        
        # Edge detector state
        if self.spatial_dimensions:
            dimensions = self.spatial_dimensions
            self.edge_map = np.zeros(dimensions, dtype=float)
            self.last_event_map = np.zeros(dimensions, dtype=float)
            self.edge_directions = np.zeros(dimensions + (2,), dtype=float)  # x, y components
            
    def _init_motion_detector(self) -> None:
        """Initialize motion detector state."""
        # Motion detector parameters
        self.time_surface_decay = self.parameters.get('time_surface_decay', 20.0)  # ms
        self.min_events_for_motion = self.parameters.get('min_events', 5)
        self.direction_bins = self.parameters.get('direction_bins', 8)
        
        # Motion detector state
        if self.spatial_dimensions:
            dimensions = self.spatial_dimensions
            self.time_surface = np.zeros(dimensions, dtype=float)
            self.event_count_map = np.zeros(dimensions, dtype=int)
            self.motion_vector_map = np.zeros(dimensions + (2,), dtype=float)  # vx, vy
            
    def _init_corner_detector(self) -> None:
        """Initialize corner detector state."""
        # Corner detector parameters
        self.corner_threshold = self.parameters.get('threshold', 0.7)
        self.event_window = self.parameters.get('window', 20.0)  # ms
        self.neighborhood_size = self.parameters.get('neighborhood', 3)
        
        # Corner detector state
        if self.spatial_dimensions:
            dimensions = self.spatial_dimensions
            self.event_count = np.zeros(dimensions, dtype=int)
            self.gradient_x = np.zeros(dimensions, dtype=float)
            self.gradient_y = np.zeros(dimensions, dtype=float)
            self.corner_score = np.zeros(dimensions, dtype=float)
            
    def _init_temporal_detector(self) -> None:
        """Initialize temporal pattern detector state."""
        # Temporal detector parameters
        self.pattern_window = self.parameters.get('window', 100.0)  # ms
        self.pattern_stride = self.parameters.get('stride', 20.0)  # ms
        self.min_correlation = self.parameters.get('min_correlation', 0.7)
        
        # Temporal detector state
        self.event_history = {}  # source_id -> list of (timestamp, polarity)
        self.pattern_templates = self.parameters.get('templates', {})
        self.last_window_end = 0.0
    
    def process_event(self, event: Event) -> List[Event]:
        """
        Process an event to extract features.
        
        Args:
            event: Event to process
            
        Returns:
            List of feature events
        """
        # Dispatch based on feature type
        if self.feature_type == 'edge':
            return self._process_for_edges(event)
        elif self.feature_type == 'motion':
            return self._process_for_motion(event)
        elif self.feature_type == 'corner':
            return self._process_for_corners(event)
        elif self.feature_type == 'temporal':
            return self._process_for_temporal(event)
        else:
            # Unknown feature type, pass through the event
            return [event]
    
    def _process_for_edges(self, event: Event) -> List[Event]:
        """
        Process event for edge detection.
        
        Args:
            event: Input event
            
        Returns:
            List of edge events
        """
        # Handle only change events with spatial coordinates
        if event.event_type != EventType.CHANGE or 'coordinates' not in event.payload:
            return []
            
        coords = event.payload['coordinates']
        if len(coords) != len(self.spatial_dimensions):
            return []
            
        # Extract coordinates
        x, y = coords[:2]  # First two dimensions (may have more)
        
        # Update last event time
        self.last_event_map[x, y] = event.timestamp
        
        # Collect events in neighborhood
        window_start = event.timestamp - self.window_size
        neighborhood_events = []
        
        # Simple 3x3 neighborhood check
        for dx in range(-1, 2):
            for dy in range(-1, 2):
                nx, ny = x + dx, y + dy
                if (nx >= 0 and nx < self.spatial_dimensions[0] and
                    ny >= 0 and ny < self.spatial_dimensions[1]):
                    last_time = self.last_event_map[nx, ny]
                    if last_time >= window_start:
                        neighborhood_events.append((nx, ny, last_time))
        
        # Not enough events for edge detection
        if len(neighborhood_events) < self.min_events:
            return []
            
        # Compute gradient direction (simple approach)
        # More sophisticated edge detection would use proper filters
        grad_x, grad_y = 0.0, 0.0
        center_polarity = event.polarity
        
        for nx, ny, _ in neighborhood_events:
            if nx != x or ny != y:  # Skip center
                dx, dy = nx - x, ny - y
                # Approximate gradient
                weight = 1.0 / max(1.0, np.sqrt(dx**2 + dy**2))
                grad_x += weight * dx * center_polarity
                grad_y += weight * dy * center_polarity
                
        # Normalize gradient
        grad_norm = np.sqrt(grad_x**2 + grad_y**2)
        if grad_norm > self.threshold:
            grad_x /= grad_norm
            grad_y /= grad_norm
            
            # Store edge direction
            self.edge_directions[x, y, 0] = grad_x
            self.edge_directions[x, y, 1] = grad_y
            
            # Create edge event
            orientation = np.arctan2(grad_y, grad_x)
            edge_event = EdgeEvent(
                timestamp=event.timestamp,
                source_id=self.processor_id,
                polarity=event.polarity,
                coordinates=coords,
                orientation=orientation,
                strength=grad_norm
            )
            
            return [edge_event]
            
        return []
    
    def _process_for_motion(self, event: Event) -> List[Event]:
        """
        Process event for motion detection.
        
        Args:
            event: Input event
            
        Returns:
            List of motion events
        """
        # Handle only change events with spatial coordinates
        if event.event_type != EventType.CHANGE or 'coordinates' not in event.payload:
            return []
            
        coords = event.payload['coordinates']
        if len(coords) != len(self.spatial_dimensions):
            return []
            
        # Extract coordinates
        x, y = coords[:2]  # First two dimensions
        
        # Update time surface
        current_time = event.timestamp
        
        # Decay time surface based on elapsed time
        time_diff = current_time - self.time_surface
        decay_mask = time_diff > 0
        self.time_surface[decay_mask] = current_time - (
            time_diff[decay_mask] * np.exp(-time_diff[decay_mask] / self.time_surface_decay)
        )
        
        # Update with new event
        self.time_surface[x, y] = current_time
        self.event_count_map[x, y] += 1
        
        # Check for motion
        if self.event_count_map[x, y] < self.min_events_for_motion:
            return []
            
        # Compute motion based on time surface gradients
        # Calculate gradient of time surface (higher values = older events)
        if x > 0 and x < self.spatial_dimensions[0] - 1 and y > 0 and y < self.spatial_dimensions[1] - 1:
            grad_x = (self.time_surface[x + 1, y] - self.time_surface[x - 1, y]) / 2.0
            grad_y = (self.time_surface[x, y + 1] - self.time_surface[x, y - 1]) / 2.0
            
            # Convert time gradient to motion vector (motion is opposite to time gradient)
            if abs(grad_x) > 1e-6 or abs(grad_y) > 1e-6:
                motion_x = -grad_x
                motion_y = -grad_y
                
                # Normalize
                motion_mag = np.sqrt(motion_x**2 + motion_y**2)
                if motion_mag > 0:
                    motion_x /= motion_mag
                    motion_y /= motion_mag
                    
                    # Update motion vector map
                    self.motion_vector_map[x, y, 0] = motion_x
                    self.motion_vector_map[x, y, 1] = motion_y
                    
                    # Create motion event
                    motion_event = Event(
                        priority=current_time,
                        timestamp=current_time,
                        source_id=self.processor_id,
                        event_type=EventType.FEATURE,
                        polarity=event.polarity,
                        payload={
                            'feature_type': 'motion',
                            'coordinates': coords,
                            'vector': (motion_x, motion_y),
                            'magnitude': motion_mag
                        }
                    )
                    
                    return [motion_event]
                    
        return []
    
    def _process_for_corners(self, event: Event) -> List[Event]:
        """
        Process event for corner detection.
        
        Args:
            event: Input event
            
        Returns:
            List of corner events
        """
        # Handle only change events with spatial coordinates
        if event.event_type != EventType.CHANGE or 'coordinates' not in event.payload:
            return []
            
        coords = event.payload['coordinates']
        if len(coords) != len(self.spatial_dimensions):
            return []
            
        # Extract coordinates
        x, y = coords[:2]  # First two dimensions
        
        # Update event count
        self.event_count[x, y] += 1
        
        # Need minimum number of events for reliable corner detection
        if self.event_count[x, y] < 3:
            return []
            
        # Compute gradients in neighborhood
        if x > self.neighborhood_size and x < self.spatial_dimensions[0] - self.neighborhood_size:
            if y > self.neighborhood_size and y < self.spatial_dimensions[1] - self.neighborhood_size:
                # Extract neighborhood
                neigh_count = self.event_count[
                    x-self.neighborhood_size:x+self.neighborhood_size+1,
                    y-self.neighborhood_size:y+self.neighborhood_size+1
                ]
                
                # Compute gradients using Sobel operators
                grad_x = np.array([[-1, 0, 1], [-2, 0, 2], [-1, 0, 1]])
                grad_y = np.array([[-1, -2, -1], [0, 0, 0], [1, 2, 1]])
                
                self.gradient_x[x, y] = np.sum(neigh_count * grad_x)
                self.gradient_y[x, y] = np.sum(neigh_count * grad_y)
                
                # Compute corner score using Harris corner response
                Ixx = self.gradient_x[x, y]**2
                Iyy = self.gradient_y[x, y]**2
                Ixy = self.gradient_x[x, y] * self.gradient_y[x, y]
                
                # Harris response: det(M) - k * trace(M)^2
                det_M = Ixx * Iyy - Ixy**2
                trace_M = Ixx + Iyy
                k = 0.04  # Harris parameter
                
                corner_score = det_M - k * trace_M**2
                self.corner_score[x, y] = corner_score
                
                # Check if this is a corner
                if corner_score > self.corner_threshold:
                    # Check if it's a local maximum
                    neighborhood = self.corner_score[
                        x-1:x+2,
                        y-1:y+2
                    ]
                    if corner_score >= np.max(neighborhood):
                        # Create corner event
                        corner_event = Event(
                            priority=event.timestamp,
                            timestamp=event.timestamp,
                            source_id=self.processor_id,
                            event_type=EventType.FEATURE,
                            polarity=event.polarity,
                            payload={
                                'feature_type': 'corner',
                                'coordinates': coords,
                                'score': corner_score
                            }
                        )
                        
                        return [corner_event]
                        
        return []
    
    def _process_for_temporal(self, event: Event) -> List[Event]:
        """
        Process event for temporal pattern detection.
        
        Args:
            event: Input event
            
        Returns:
            List of pattern events
        """
        # Update event history
        source_id = event.source_id
        if source_id not in self.event_history:
            self.event_history[source_id] = []
            
        self.event_history[source_id].append((event.timestamp, event.polarity))
        
        # Check if we need to process a window
        current_time = event.timestamp
        
        if current_time > self.last_window_end + self.pattern_stride:
            # Define window
            window_start = current_time - self.pattern_window
            window_end = current_time
            
            # Process temporal patterns in window
            pattern_events = []
            
            for source_id, history in self.event_history.items():
                # Filter events in window
                window_events = [(t, p) for t, p in history if window_start <= t < window_end]
                
                if len(window_events) > 2:  # Need at least a few events for pattern
                    # Check each template
                    for template_name, template_data in self.pattern_templates.items():
                        match_score = self._match_temporal_pattern(window_events, template_data)
                        
                        if match_score >= self.min_correlation:
                            # Create pattern event
                            pattern_event = Event(
                                priority=current_time,
                                timestamp=current_time,
                                source_id=self.processor_id,
                                event_type=EventType.FEATURE,
                                polarity=1,
                                payload={
                                    'feature_type': 'temporal_pattern',
                                    'source_id': source_id,
                                    'pattern_name': template_name,
                                    'match_score': match_score,
                                    'window': (window_start, window_end)
                                }
                            )
                            
                            pattern_events.append(pattern_event)
                            
            # Clean up old events
            for source_id in self.event_history:
                self.event_history[source_id] = [
                    (t, p) for t, p in self.event_history[source_id]
                    if t >= window_start
                ]
                
            self.last_window_end = window_end
            
            return pattern_events
                
        return []
    
    def _match_temporal_pattern(self, events, template_data):
        """
        Match events against a temporal pattern template.
        
        This is a simple implementation and would be more sophisticated
        in a real-world application, using techniques like DTW or
        correlation analysis.
        
        Args:
            events: List of (timestamp, polarity) tuples
            template_data: Template pattern data
            
        Returns:
            Match score (0.0 - 1.0)
        """
        # Simple approach: extract intervals between events
        if len(events) < 2:
            return 0.0
            
        # Extract time deltas
        times, polarities = zip(*events)
        
        # Compute intervals
        intervals = np.diff(times)
        
        # If template has intervals, compare them
        if 'intervals' in template_data:
            template_intervals = np.array(template_data['intervals'])
            
            # Normalize intervals for comparison
            if len(intervals) > 0 and len(template_intervals) > 0:
                norm_intervals = intervals / np.sum(intervals)
                norm_template = template_intervals / np.sum(template_intervals)
                
                # Pad the shorter sequence
                if len(norm_intervals) < len(norm_template):
                    norm_intervals = np.pad(norm_intervals, 
                                          (0, len(norm_template) - len(norm_intervals)))
                elif len(norm_template) < len(norm_intervals):
                    norm_template = np.pad(norm_template,
                                         (0, len(norm_intervals) - len(norm_template)))
                
                # Compute correlation
                correlation = np.corrcoef(norm_intervals, norm_template)[0, 1]
                return max(0.0, correlation)
                
        # If template has polarity pattern, check that
        if 'polarity_pattern' in template_data:
            template_polarities = np.array(template_data['polarity_pattern'])
            
            # Normalize to match length
            if len(polarities) > 0 and len(template_polarities) > 0:
                # Use a sliding window approach to find best match
                max_corr = 0.0
                pol_array = np.array(polarities)
                
                for i in range(max(1, len(pol_array) - len(template_polarities) + 1)):
                    window = pol_array[i:i+len(template_polarities)]
                    if len(window) == len(template_polarities):
                        corr = np.sum(window == template_polarities) / len(template_polarities)
                        max_corr = max(max_corr, corr)
                        
                return max_corr
                
        return 0.0


class EventBasedNeuron(EventProcessor):
    """
    Neuron model for event-based processing.
    
    This implements a simplified neuron model that operates on events
    rather than continuous time, making it suitable for event-based
    systems. It can generate spike events when its activation exceeds
    a threshold.
    """
    
    def __init__(
        self,
        processor_id: int,
        threshold: float = 1.0,
        reset_value: float = 0.0,
        decay_constant: float = 10.0,  # ms
        refractory_period: float = 2.0,  # ms
        weight_function: Optional[Callable[[Event], float]] = None
    ):
        """
        Initialize event-based neuron.
        
        Args:
            processor_id: Unique identifier for this neuron
            threshold: Firing threshold
            reset_value: Potential after spike
            decay_constant: Potential decay time constant (ms)
            refractory_period: Refractory period after spike (ms)
            weight_function: Function to determine input weight from event
        """
        super().__init__(processor_id)
        self.threshold = threshold
        self.reset_value = reset_value
        self.decay_constant = decay_constant
        self.refractory_period = refractory_period
        
        # If no weight function provided, use default
        if weight_function is None:
            # Default: use event polarity
            self.weight_function = lambda event: event.polarity
        else:
            self.weight_function = weight_function
            
        # Neuron state
        self.potential = 0.0
        self.last_update_time = 0.0
        self.last_spike_time = float('-inf')
        
        # Synaptic weights
        self.synaptic_weights = {}  # source_id -> weight
        
        # Neuron history for debugging/analysis
        self.potential_history = []
        self.spike_history = []
        self.input_history = []
    
    def set_weight(self, source_id: int, weight: float) -> None:
        """
        Set synaptic weight for an input source.
        
        Args:
            source_id: Source neuron ID
            weight: Synaptic weight
        """
        self.synaptic_weights[source_id] = weight
    
    def process_event(self, event: Event) -> List[Event]:
        """
        Process an input event, updating potential and potentially generating spike.
        
        Args:
            event: Input event
            
        Returns:
            List containing spike event if neuron fires, otherwise empty
        """
        current_time = event.timestamp
        
        # Update potential based on decay since last update
        self._update_decay(current_time)
        
        # Check if in refractory period
        if current_time < self.last_spike_time + self.refractory_period:
            # Still in refractory period, don't process input
            return []
            
        # Get input weight
        weight = self.synaptic_weights.get(event.source_id, None)
        
        if weight is None:
            # No explicit weight, use weight function
            weight = self.weight_function(event)
            
        # Update potential
        self.potential += weight
        
        # Record input for history
        self.input_history.append((current_time, event.source_id, weight))
        
        # Record potential for history
        self.potential_history.append((current_time, self.potential))
        
        # Check if potential exceeds threshold
        if self.potential >= self.threshold:
            # Generate spike
            self.last_spike_time = current_time
            self.potential = self.reset_value
            
            # Create spike event
            spike_event = SpikeEvent(
                timestamp=current_time,
                neuron_id=self.processor_id,
                payload={
                    'weight': weight,
                    'source_id': event.source_id
                }
            )
            
            # Record spike for history
            self.spike_history.append(current_time)
            
            # Record updated potential for history
            self.potential_history.append((current_time, self.potential))
            
            return [spike_event]
            
        return []
    
    def _update_decay(self, current_time: float) -> None:
        """
        Update potential based on exponential decay.
        
        Args:
            current_time: Current time
        """
        if self.last_update_time < 0:
            self.last_update_time = current_time
            return
            
        # Compute time since last update
        dt = current_time - self.last_update_time
        
        if dt > 0:
            # Apply exponential decay
            decay_factor = np.exp(-dt / self.decay_constant)
            self.potential *= decay_factor
            
        self.last_update_time = current_time
    
    def reset(self) -> None:
        """Reset neuron state."""
        super().reset()
        self.potential = 0.0
        self.last_update_time = 0.0
        self.last_spike_time = float('-inf')
        self.potential_history = []
        self.spike_history = []
        self.input_history = []
    
    def plot_potential(self) -> None:
        """Plot neuron potential history."""
        if not self.potential_history:
            logger.warning("No potential history to plot.")
            return
            
        # Extract times and potentials
        times, potentials = zip(*self.potential_history)
        
        plt.figure(figsize=(10, 6))
        plt.plot(times, potentials, 'b-', label='Membrane Potential')
        
        # Add threshold line
        plt.axhline(y=self.threshold, color='r', linestyle='--', label='Threshold')
        
        # Add spike markers
        if self.spike_history:
            spike_y = [self.threshold] * len(self.spike_history)
            plt.plot(self.spike_history, spike_y, 'ro', markersize=8, label='Spikes')
            
        plt.xlabel('Time (ms)')
        plt.ylabel('Potential')
        plt.title(f'Neuron {self.processor_id} Potential')
        plt.legend()
        plt.grid(True)
        plt.tight_layout()
        plt.show()


class EventBasedSynapse(EventProcessor):
    """
    Synapse model for event-based processing.
    
    This implements a connection between neurons in an event-based
    system, with configurable weight, delay, and plasticity.
    """
    
    def __init__(
        self,
        processor_id: int,
        pre_id: int,
        post_id: int,
        weight: float = 1.0,
        delay: float = 1.0,  # ms
        stdp_enabled: bool = False,
        stdp_params: Optional[Dict[str, float]] = None
    ):
        """
        Initialize event-based synapse.
        
        Args:
            processor_id: Unique identifier for this synapse
            pre_id: Presynaptic neuron ID
            post_id: Postsynaptic neuron ID
            weight: Synaptic weight
            delay: Synaptic delay in milliseconds
            stdp_enabled: Enable spike-timing dependent plasticity
            stdp_params: STDP parameters
        """
        super().__init__(processor_id)
        self.pre_id = pre_id
        self.post_id = post_id
        self.weight = weight
        self.delay = delay
        self.stdp_enabled = stdp_enabled
        
        # Set default STDP parameters if enabled but not provided
        if stdp_enabled:
            default_stdp_params = {
                'a_plus': 0.01,    # Potentiation factor
                'a_minus': 0.0105,  # Depression factor (slightly stronger for stability)
                'tau_plus': 20.0,   # Potentiation time constant (ms)
                'tau_minus': 20.0,  # Depression time constant (ms)
                'w_min': 0.0,       # Minimum weight
                'w_max': 2.0        # Maximum weight
            }
            
            if stdp_params is None:
                self.stdp_params = default_stdp_params
            else:
                # Update defaults with provided parameters
                self.stdp_params = default_stdp_params.copy()
                self.stdp_params.update(stdp_params)
        else:
            self.stdp_params = {}
            
        # STDP state
        self.pre_trace = 0.0
        self.post_trace = 0.0
        self.last_pre_spike = float('-inf')
        self.last_post_spike = float('-inf')
        self.weight_history = []
    
    def process_event(self, event: Event) -> List[Event]:
        """
        Process an event, implementing synaptic transmission and plasticity.
        
        Args:
            event: Input event
            
        Returns:
            List containing output event if applicable, otherwise empty
        """
        if event.event_type != EventType.SPIKE:
            return []
            
        current_time = event.timestamp
        source_id = event.source_id
        
        if source_id == self.pre_id:
            # Presynaptic spike
            self.last_pre_spike = current_time
            
            # Update STDP pre trace
            if self.stdp_enabled:
                # Decay trace based on time since last update
                dt = current_time - self.last_pre_spike
                self.pre_trace *= np.exp(-dt / self.stdp_params['tau_plus'])
                
                # Increment trace
                self.pre_trace += 1.0
                
                # Apply weight update based on post trace (pre after post: depression)
                if self.last_post_spike > float('-inf'):
                    dw = -self.stdp_params['a_minus'] * self.post_trace
                    self._update_weight(dw)
            
            # Create delayed event for postsynaptic neuron
            delay = self.delay
            
            # Add small random variation to delay for biological realism
            delay += np.random.normal(0, delay * 0.05)  # 5% variation
            
            # Create output event with delay
            output_time = current_time + max(0, delay)
            
            output_event = SpikeEvent(
                timestamp=output_time,
                neuron_id=self.post_id,
                payload={
                    'weight': self.weight,
                    'pre_id': self.pre_id,
                    'synapse_id': self.processor_id
                }
            )
            
            return [output_event]
            
        elif source_id == self.post_id and self.stdp_enabled:
            # Postsynaptic spike (only matters for STDP)
            self.last_post_spike = current_time
            
            # Update STDP post trace
            dt = current_time - self.last_post_spike
            self.post_trace *= np.exp(-dt / self.stdp_params['tau_minus'])
            
            # Increment trace
            self.post_trace += 1.0
            
            # Apply weight update based on pre trace (post after pre: potentiation)
            if self.last_pre_spike > float('-inf'):
                dw = self.stdp_params['a_plus'] * self.pre_trace
                self._update_weight(dw)
                
        return []
    
    def _update_weight(self, dw: float) -> None:
        """
        Update synaptic weight with bounds.
        
        Args:
            dw: Weight change amount
        """
        if self.stdp_enabled:
            old_weight = self.weight
            self.weight += dw
            
            # Apply weight bounds
            self.weight = max(self.stdp_params['w_min'], 
                            min(self.stdp_params['w_max'], self.weight))
            
            # Record weight change for history
            self.weight_history.append((time.time(), old_weight, self.weight))
    
    def reset(self) -> None:
        """Reset synapse state."""
        super().reset()
        self.pre_trace = 0.0
        self.post_trace = 0.0
        self.last_pre_spike = float('-inf')
        self.last_post_spike = float('-inf')
    
    def plot_weight_history(self) -> None:
        """Plot synapse weight history."""
        if not self.weight_history:
            logger.warning("No weight history to plot.")
            return
            
        # Extract times and weights
        times, old_weights, new_weights = zip(*self.weight_history)
        
        plt.figure(figsize=(10, 6))
        plt.plot(times, new_weights, 'b-', label='Synaptic Weight')
        
        # Add weight bounds if STDP enabled
        if self.stdp_enabled:
            plt.axhline(y=self.stdp_params['w_min'], color='r', linestyle='--', 
                      label='Min Weight')
            plt.axhline(y=self.stdp_params['w_max'], color='g', linestyle='--', 
                      label='Max Weight')
            
        plt.xlabel('Time (s)')
        plt.ylabel('Weight')
        plt.title(f'Synapse {self.processor_id} Weight History')
        plt.legend()
        plt.grid(True)
        plt.tight_layout()
        plt.show()


class EventRouter(EventProcessor):
    """
    Router for directing events based on rules.
    
    This component distributes incoming events to different processors
    based on configurable routing rules, enabling the construction of
    complex event processing networks.
    """
    
    def __init__(
        self,
        processor_id: int,
        routing_rules: Optional[Dict[str, List[int]]] = None
    ):
        """
        Initialize event router.
        
        Args:
            processor_id: Unique identifier for this processor
            routing_rules: Dictionary mapping rule patterns to target processor IDs
        """
        super().__init__(processor_id)
        self.routing_rules = routing_rules if routing_rules else {}
        self.target_processors = {}  # processor_id -> EventProcessor instance
        
        # Stats
        self.routing_counts = defaultdict(int)
    
    def add_rule(self, pattern: str, target_ids: List[int]) -> None:
        """
        Add a routing rule.
        
        Args:
            pattern: Rule pattern (e.g., 'event_type=SPIKE', 'source_id=5')
            target_ids: Target processor IDs
        """
        self.routing_rules[pattern] = target_ids
    
    def add_target_processor(self, processor_id: int, processor: EventProcessor) -> None:
        """
        Add a target processor for direct routing.
        
        Args:
            processor_id: Processor ID
            processor: EventProcessor instance
        """
        self.target_processors[processor_id] = processor
    
    def process_event(self, event: Event) -> List[Event]:
        """
        Route event based on rules.
        
        Args:
            event: Event to route
            
        Returns:
            Empty list (routing is handled separately)
        """
        # Find matching rules
        matched_targets = set()
        
        for pattern, target_ids in self.routing_rules.items():
            if self._match_pattern(event, pattern):
                matched_targets.update(target_ids)
                self.routing_counts[pattern] += 1
                
        # Route event to matched targets
        for target_id in matched_targets:
            if target_id in self.target_processors:
                # Direct routing
                self.target_processors[target_id].receive_event(copy.deepcopy(event))
            elif target_id in self.connected_processors:
                # Connected processors list (processors connected through connect_to)
                # Find processor in connected_processors
                for processor in self.connected_processors:
                    if hasattr(processor, 'processor_id') and processor.processor_id == target_id:
                        processor.receive_event(copy.deepcopy(event))
                        break
                        
        # Router doesn't produce output events itself
        return []
    
    def _match_pattern(self, event: Event, pattern: str) -> bool:
        """
        Check if event matches a routing pattern.
        
        Args:
            event: Event to check
            pattern: Pattern string (e.g., 'event_type=SPIKE')
            
        Returns:
            True if event matches pattern, False otherwise
        """
        # Parse pattern
        if '=' in pattern:
            key, value = pattern.split('=', 1)
            key = key.strip()
            value = value.strip()
            
            # Check attribute match
            if key == 'event_type':
                try:
                    return event.event_type == EventType[value]
                except KeyError:
                    return False
            elif key == 'source_id':
                try:
                    return event.source_id == int(value)
                except ValueError:
                    return False
            elif key == 'polarity':
                try:
                    return event.polarity == int(value)
                except ValueError:
                    return False
            elif key.startswith('payload.'):
                # Check payload field
                payload_key = key[8:]  # Remove 'payload.' prefix
                return (event.payload is not None and
                      payload_key in event.payload and
                      str(event.payload[payload_key]) == value)
        elif '>' in pattern:
            key, value = pattern.split('>', 1)
            key = key.strip()
            value = float(value.strip())
            
            if key == 'timestamp':
                return event.timestamp > value
                
        elif '<' in pattern:
            key, value = pattern.split('<', 1)
            key = key.strip()
            value = float(value.strip())
            
            if key == 'timestamp':
                return event.timestamp < value
                
        # Special case: 'all' matches everything
        elif pattern.strip() == 'all':
            return True
            
        return False
    
    def get_routing_stats(self) -> Dict[str, int]:
        """
        Get routing statistics.
        
        Returns:
            Dictionary mapping rule patterns to match counts
        """
        return dict(self.routing_counts)


class EventSimulator:
    """
    Simulator for event-based neural networks.
    
    This component manages the simulation of event-based networks,
    handling event scheduling, processing, and analysis.
    """
    
    def __init__(self):
        """Initialize event simulator."""
        self.processors = {}  # id -> processor
        self.global_time = 0.0
        self.event_queue = EventQueue()
        self.simulation_stats = {
            'total_events': 0,
            'processed_events': 0,
            'simulation_time': 0.0,
            'start_time': 0.0,
            'end_time': 0.0
        }
        
        # For tracking and visualization
        self.event_history = []
        self.processor_stats = {}
    
    def add_processor(self, processor: EventProcessor) -> None:
        """
        Add a processor to the simulation.
        
        Args:
            processor: EventProcessor instance
        """
        self.processors[processor.processor_id] = processor
    
    def add_connection(self, source_id: int, target_id: int) -> None:
        """
        Connect two processors.
        
        Args:
            source_id: Source processor ID
            target_id: Target processor ID
        """
        if source_id in self.processors and target_id in self.processors:
            self.processors[source_id].connect_to(self.processors[target_id])
    
    def add_event(self, event: Event) -> None:
        """
        Add an event to the simulation queue.
        
        Args:
            event: Event to add
        """
        self.event_queue.push(event)
        self.simulation_stats['total_events'] += 1
    
    def add_external_spike(
        self,
        timestamp: float,
        neuron_id: int,
        payload: Any = None
    ) -> None:
        """
        Add an external spike event.
        
        Args:
            timestamp: Event timestamp
            neuron_id: Target neuron ID
            payload: Optional event payload
        """
        spike = SpikeEvent(
            timestamp=timestamp,
            neuron_id=neuron_id,
            payload=payload
        )
        self.add_event(spike)
    
    def add_change_event(
        self,
        timestamp: float,
        source_id: int,
        polarity: int,
        magnitude: float,
        coordinates: Tuple[int, ...] = None,
        payload: Any = None
    ) -> None:
        """
        Add a change detection event.
        
        Args:
            timestamp: Event timestamp
            source_id: Source ID
            polarity: Event polarity (1: increase, -1: decrease)
            magnitude: Change magnitude
            coordinates: Spatial coordinates
            payload: Optional additional data
        """
        event = ChangeEvent(
            timestamp=timestamp,
            source_id=source_id,
            polarity=polarity,
            magnitude=magnitude,
            coordinates=coordinates,
            payload=payload
        )
        self.add_event(event)
    
    def run_simulation(self, duration: float) -> None:
        """
        Run simulation for a specified duration.
        
        Args:
            duration: Simulation duration
        """
        self.simulation_stats['start_time'] = self.global_time
        end_time = self.global_time + duration
        self.simulation_stats['end_time'] = end_time
        
        sim_start_wall_time = time.time()
        
        # Process events until queue is empty or we reach the end time
        while self.event_queue:
            event = self.event_queue.pop()
            
            if event is None or event.timestamp > end_time:
                break
                
            # Update global time
            self.global_time = event.timestamp
            
            # Find target processor
            if event.source_id in self.processors:
                processor = self.processors[event.source_id]
                
                # Process event
                processor.receive_event(event)
                processor.process_next_event()
                
                self.simulation_stats['processed_events'] += 1
                
            # Record event for history
            self.event_history.append((event.timestamp, event.source_id, 
                                      event.event_type, event.polarity))
        
        sim_end_wall_time = time.time()
        self.simulation_stats['simulation_time'] += (sim_end_wall_time - sim_start_wall_time)
        
        # Update global time to end of simulation
        self.global_time = end_time
        
        # Collect processor stats
        for proc_id, processor in self.processors.items():
            self.processor_stats[proc_id] = processor.get_stats()
    
    def run_until_empty(self, max_events: Optional[int] = None) -> bool:
        """
        Run simulation until event queue is empty or max events processed.
        
        Args:
            max_events: Maximum number of events to process (None = no limit)
            
        Returns:
            True if queue emptied, False if stopped due to max_events
        """
        sim_start_wall_time = time.time()
        events_processed = 0
        
        # Process events until queue is empty or we reach max_events
        while self.event_queue:
            if max_events is not None and events_processed >= max_events:
                sim_end_wall_time = time.time()
                self.simulation_stats['simulation_time'] += (sim_end_wall_time - sim_start_wall_time)
                return False
                
            event = self.event_queue.pop()
            
            if event is None:
                break
                
            # Update global time
            self.global_time = event.timestamp
            
            # Find target processor
            if event.source_id in self.processors:
                processor = self.processors[event.source_id]
                
                # Process event
                processor.receive_event(event)
                processor.process_next_event()
                
                self.simulation_stats['processed_events'] += 1
                events_processed += 1
                
            # Record event for history
            self.event_history.append((event.timestamp, event.source_id, 
                                      event.event_type, event.polarity))
        
        sim_end_wall_time = time.time()
        self.simulation_stats['simulation_time'] += (sim_end_wall_time - sim_start_wall_time)
        
        return True
    
    def step_simulation(self) -> Optional[Event]:
        """
        Process a single event from the queue.
        
        Returns:
            Processed event, or None if queue is empty
        """
        if not self.event_queue:
            return None
            
        event = self.event_queue.pop()
        
        if event is None:
            return None
            
        # Update global time
        self.global_time = event.timestamp
        
        # Find target processor
        if event.source_id in self.processors:
            processor = self.processors[event.source_id]
            
            # Process event
            processor.receive_event(event)
            processor.process_next_event()
            
            self.simulation_stats['processed_events'] += 1
            
        # Record event for history
        self.event_history.append((event.timestamp, event.source_id, 
                                  event.event_type, event.polarity))
        
        return event
    
    def reset(self) -> None:
        """Reset simulation state."""
        self.global_time = 0.0
        self.event_queue = EventQueue()
        self.simulation_stats = {
            'total_events': 0,
            'processed_events': 0,
            'simulation_time': 0.0,
            'start_time': 0.0,
            'end_time': 0.0
        }
        self.event_history = []
        self.processor_stats = {}
        
        # Reset all processors
        for processor in self.processors.values():
            processor.reset()
    
    def get_simulation_stats(self) -> Dict[str, Any]:
        """
        Get simulation statistics.
        
        Returns:
            Dictionary of statistics
        """
        stats = self.simulation_stats.copy()
        stats['processors'] = len(self.processors)
        stats['events_in_queue'] = len(self.event_queue)
        stats['global_time'] = self.global_time
        
        if stats['processed_events'] > 0 and stats['simulation_time'] > 0:
            stats['events_per_second'] = stats['processed_events'] / stats['simulation_time']
        else:
            stats['events_per_second'] = 0.0
            
        return stats
    
    def plot_event_raster(self, event_types: Optional[List[EventType]] = None) -> None:
        """
        Plot event raster diagram.
        
        Args:
            event_types: Types of events to include (None = all types)
        """
        if not self.event_history:
            logger.warning("No event history to plot.")
            return
            
        # Filter events by type if specified
        filtered_history = []
        for timestamp, source_id, event_type, polarity in self.event_history:
            if event_types is None or event_type in event_types:
                filtered_history.append((timestamp, source_id, event_type, polarity))
                
        if not filtered_history:
            logger.warning("No events of specified types in history.")
            return
            
        # Extract data
        timestamps, source_ids, event_types, polarities = zip(*filtered_history)
        
        # Get unique source IDs and assign y-coordinates
        unique_sources = sorted(set(source_ids))
        source_to_y = {source: i for i, source in enumerate(unique_sources)}
        
        # Get y-coordinates
        y_coords = [source_to_y[source] for source in source_ids]
        
        # Set colors based on event type and polarity
        colors = []
        for ev_type, pol in zip(event_types, polarities):
            if ev_type == EventType.SPIKE:
                colors.append('red')
            elif ev_type == EventType.CHANGE:
                colors.append('green' if pol > 0 else 'blue')
            elif ev_type == EventType.EDGE:
                colors.append('purple' if pol > 0 else 'magenta')
            elif ev_type == EventType.FEATURE:
                colors.append('orange')
            else:
                colors.append('black')
                
        plt.figure(figsize=(12, 8))
        plt.scatter(timestamps, y_coords, c=colors, s=10, alpha=0.7)
        
        plt.xlabel('Time (ms)')
        plt.ylabel('Source ID')
        plt.yticks(range(len(unique_sources)), unique_sources)
        plt.title('Event Raster Plot')
        plt.grid(True, linestyle='--', alpha=0.7)
        
        # Add legend
        from matplotlib.lines import Line2D
        legend_elements = [
            Line2D([0], [0], marker='o', color='w', markerfacecolor='red', 
                 markersize=8, label='Spike'),
            Line2D([0], [0], marker='o', color='w', markerfacecolor='green', 
                 markersize=8, label='Change (+)'),
            Line2D([0], [0], marker='o', color='w', markerfacecolor='blue', 
                 markersize=8, label='Change (-)'),
            Line2D([0], [0], marker='o', color='w', markerfacecolor='purple', 
                 markersize=8, label='Edge (+)'),
            Line2D([0], [0], marker='o', color='w', markerfacecolor='orange', 
                 markersize=8, label='Feature')
        ]
        plt.legend(handles=legend_elements)
        
        plt.tight_layout()
        plt.show()
    
    def plot_event_rates(self, bin_width: float = 10.0) -> None:
        """
        Plot event rates over time.
        
        Args:
            bin_width: Time bin width for rate calculation (ms)
        """
        if not self.event_history:
            logger.warning("No event history to plot.")
            return
            
        # Extract timestamps
        timestamps = [t for t, _, _, _ in self.event_history]
        
        # Create time bins
        min_time = min(timestamps)
        max_time = max(timestamps)
        bins = np.arange(min_time, max_time + bin_width, bin_width)
        
        # Count events in each bin
        hist, _ = np.histogram(timestamps, bins=bins)
        
        # Convert to rates (events per second)
        rates = hist / (bin_width / 1000.0)  # Convert ms to seconds
        
        # Plot
        plt.figure(figsize=(12, 6))
        plt.bar(bins[:-1], rates, width=bin_width * 0.9, alpha=0.7)
        
        plt.xlabel('Time (ms)')
        plt.ylabel('Event Rate (events/second)')
        plt.title('Event Rate Over Time')
        plt.grid(True, linestyle='--', alpha=0.7)
        
        plt.tight_layout()
        plt.show()


class EventBasedNeuralNetwork:
    """
    Event-based neural network implementation.
    
    This class provides a higher-level interface for building and
    simulating event-based neural networks, with convenient methods
    for creating neurons, synapses, and connections.
    """
    
    def __init__(self):
        """Initialize event-based neural network."""
        self.simulator = EventSimulator()
        self.neurons = {}  # id -> EventBasedNeuron
        self.synapses = {}  # id -> EventBasedSynapse
        self.next_id = 0
    
    def create_neuron(
        self,
        threshold: float = 1.0,
        reset_value: float = 0.0,
        decay_constant: float = 10.0,
        refractory_period: float = 2.0,
        weight_function: Optional[Callable[[Event], float]] = None
    ) -> int:
        """
        Create a neuron in the network.
        
        Args:
            threshold: Firing threshold
            reset_value: Potential after spike
            decay_constant: Potential decay time constant (ms)
            refractory_period: Refractory period after spike (ms)
            weight_function: Function to determine input weight from event
            
        Returns:
            Neuron ID
        """
        neuron_id = self.next_id
        self.next_id += 1
        
        neuron = EventBasedNeuron(
            processor_id=neuron_id,
            threshold=threshold,
            reset_value=reset_value,
            decay_constant=decay_constant,
            refractory_period=refractory_period,
            weight_function=weight_function
        )
        
        self.neurons[neuron_id] = neuron
        self.simulator.add_processor(neuron)
        
        return neuron_id
    
    def create_synapse(
        self,
        pre_id: int,
        post_id: int,
        weight: float = 1.0,
        delay: float = 1.0,
        stdp_enabled: bool = False,
        stdp_params: Optional[Dict[str, float]] = None
    ) -> int:
        """
        Create a synapse between neurons.
        
        Args:
            pre_id: Presynaptic neuron ID
            post_id: Postsynaptic neuron ID
            weight: Synaptic weight
            delay: Synaptic delay in milliseconds
            stdp_enabled: Enable spike-timing dependent plasticity
            stdp_params: STDP parameters
            
        Returns:
            Synapse ID
        """
        synapse_id = self.next_id
        self.next_id += 1
        
        synapse = EventBasedSynapse(
            processor_id=synapse_id,
            pre_id=pre_id,
            post_id=post_id,
            weight=weight,
            delay=delay,
            stdp_enabled=stdp_enabled,
            stdp_params=stdp_params
        )
        
        self.synapses[synapse_id] = synapse
        self.simulator.add_processor(synapse)
        
        # Connect pre-neuron to synapse
        self.simulator.add_connection(pre_id, synapse_id)
        
        return synapse_id
    
    def create_fully_connected(
        self,
        pre_ids: List[int],
        post_ids: List[int],
        weight: float = 1.0,
        delay: float = 1.0,
        stdp_enabled: bool = False
    ) -> List[int]:
        """
        Create fully connected synapses between two groups of neurons.
        
        Args:
            pre_ids: Presynaptic neuron IDs
            post_ids: Postsynaptic neuron IDs
            weight: Synaptic weight
            delay: Synaptic delay in milliseconds
            stdp_enabled: Enable spike-timing dependent plasticity
            
        Returns:
            List of created synapse IDs
        """
        synapse_ids = []
        
        for pre_id in pre_ids:
            for post_id in post_ids:
                # Skip self-connections
                if pre_id == post_id:
                    continue
                    
                synapse_id = self.create_synapse(
                    pre_id=pre_id,
                    post_id=post_id,
                    weight=weight,
                    delay=delay,
                    stdp_enabled=stdp_enabled
                )
                
                synapse_ids.append(synapse_id)
                
        return synapse_ids
    
    def create_random_connections(
        self,
        pre_ids: List[int],
        post_ids: List[int],
        connection_prob: float = 0.1,
        weight_range: Tuple[float, float] = (0.5, 1.5),
        delay_range: Tuple[float, float] = (0.5, 2.0),
        stdp_enabled: bool = False
    ) -> List[int]:
        """
        Create random connections between two groups of neurons.
        
        Args:
            pre_ids: Presynaptic neuron IDs
            post_ids: Postsynaptic neuron IDs
            connection_prob: Connection probability
            weight_range: (min_weight, max_weight)
            delay_range: (min_delay, max_delay) in milliseconds
            stdp_enabled: Enable spike-timing dependent plasticity
            
        Returns:
            List of created synapse IDs
        """
        synapse_ids = []
        
        for pre_id in pre_ids:
            for post_id in post_ids:
                # Skip self-connections
                if pre_id == post_id:
                    continue
                    
                # Random connection based on probability
                if np.random.random() < connection_prob:
                    # Random weight and delay
                    weight = np.random.uniform(*weight_range)
                    delay = np.random.uniform(*delay_range)
                    
                    synapse_id = self.create_synapse(
                        pre_id=pre_id,
                        post_id=post_id,
                        weight=weight,
                        delay=delay,
                        stdp_enabled=stdp_enabled
                    )
                    
                    synapse_ids.append(synapse_id)
                    
        return synapse_ids
    
    def add_spike(
        self,
        timestamp: float,
        neuron_id: int,
        payload: Any = None
    ) -> None:
        """
        Add a spike event to the simulation.
        
        Args:
            timestamp: Event timestamp
            neuron_id: Target neuron ID
            payload: Optional event payload
        """
        self.simulator.add_external_spike(
            timestamp=timestamp,
            neuron_id=neuron_id,
            payload=payload
        )
    
    def add_input_spikes(
        self,
        neuron_ids: List[int],
        rate: float = 10.0,  # Hz
        start_time: float = 0.0,
        end_time: float = 100.0,
        jitter: float = 1.0  # ms
    ) -> int:
        """
        Add Poisson-distributed input spikes to neurons.
        
        Args:
            neuron_ids: Target neuron IDs
            rate: Firing rate in Hz
            start_time: Start time in milliseconds
            end_time: End time in milliseconds
            jitter: Timing jitter in milliseconds
            
        Returns:
            Number of spikes added
        """
        duration = end_time - start_time  # ms
        interval = 1000.0 / rate  # ms
        count = 0
        
        for neuron_id in neuron_ids:
            # Generate Poisson-distributed spike times
            next_spike = start_time + np.random.exponential(interval)
            
            while next_spike < end_time:
                # Add jitter
                jittered_time = next_spike + np.random.normal(0, jitter)
                jittered_time = max(start_time, jittered_time)
                
                # Add spike
                self.add_spike(jittered_time, neuron_id)
                count += 1
                
                # Next spike
                next_spike += np.random.exponential(interval)
                
        return count
    
    def add_input_spike_train(
        self,
        neuron_id: int,
        spike_times: List[float],
        jitter: float = 0.0  # ms
    ) -> int:
        """
        Add a specific spike train to a neuron.
        
        Args:
            neuron_id: Target neuron ID
            spike_times: List of spike times in milliseconds
            jitter: Timing jitter in milliseconds
            
        Returns:
            Number of spikes added
        """
        count = 0
        
        for time in spike_times:
            # Add jitter
            jittered_time = time
            if jitter > 0:
                jittered_time += np.random.normal(0, jitter)
                
            # Add spike
            self.add_spike(jittered_time, neuron_id)
            count += 1
            
        return count
    
    def run(self, duration: float) -> Dict[str, Any]:
        """
        Run simulation for a specified duration.
        
        Args:
            duration: Simulation duration in milliseconds
            
        Returns:
            Simulation statistics
        """
        self.simulator.run_simulation(duration)
        return self.simulator.get_simulation_stats()
    
    def run_until_empty(self) -> Dict[str, Any]:
        """
        Run simulation until event queue is empty.
        
        Returns:
            Simulation statistics
        """
        self.simulator.run_until_empty()
        return self.simulator.get_simulation_stats()
    
    def reset(self) -> None:
        """Reset network state."""
        self.simulator.reset()
    
    def get_spike_trains(self) -> Dict[int, List[float]]:
        """
        Get spike trains for all neurons.
        
        Returns:
            Dictionary mapping neuron IDs to lists of spike times
        """
        spike_trains = {}
        
        for neuron_id, neuron in self.neurons.items():
            spike_trains[neuron_id] = neuron.spike_history
            
        return spike_trains
    
    def plot_raster(self) -> None:
        """Plot spike raster for all neurons."""
        # Filter for spike events only
        self.simulator.plot_event_raster([EventType.SPIKE])
    
    def plot_neuron_potential(self, neuron_id: int) -> None:
        """
        Plot membrane potential for a specific neuron.
        
        Args:
            neuron_id: Neuron ID
        """
        if neuron_id in self.neurons:
            self.neurons[neuron_id].plot_potential()
        else:
            logger.warning(f"Neuron with ID {neuron_id} not found.")
    
    def plot_synapse_weights(self, synapse_id: int) -> None:
        """
        Plot weight history for a specific synapse.
        
        Args:
            synapse_id: Synapse ID
        """
        if synapse_id in self.synapses:
            self.synapses[synapse_id].plot_weight_history()
        else:
            logger.warning(f"Synapse with ID {synapse_id} not found.")


def create_event_based_balanced_network(
    n_excitatory: int = 80,
    n_inhibitory: int = 20,
    connectivity: float = 0.1,
    sim_time: float = 100.0  # ms
) -> Tuple[EventBasedNeuralNetwork, List[int], List[int]]:
    """
    Create a balanced network of excitatory and inhibitory neurons.
    
    Args:
        n_excitatory: Number of excitatory neurons
        n_inhibitory: Number of inhibitory neurons
        connectivity: Connection probability
        sim_time: Simulation time
        
    Returns:
        Tuple of (network, excitatory_ids, inhibitory_ids)
    """
    network = EventBasedNeuralNetwork()
    
    # Create excitatory neurons
    exc_ids = []
    for i in range(n_excitatory):
        neuron_id = network.create_neuron(
            threshold=1.0,
            reset_value=0.0,
            decay_constant=20.0,
            refractory_period=2.0
        )
        exc_ids.append(neuron_id)
    
    # Create inhibitory neurons
    inh_ids = []
    for i in range(n_inhibitory):
        neuron_id = network.create_neuron(
            threshold=1.0,
            reset_value=0.0,
            decay_constant=10.0,  # Faster decay
            refractory_period=1.0  # Shorter refractory period
        )
        inh_ids.append(neuron_id)
    
    # Create connections
    # Excitatory to all
    network.create_random_connections(
        pre_ids=exc_ids,
        post_ids=exc_ids + inh_ids,
        connection_prob=connectivity,
        weight_range=(0.05, 0.15),
        delay_range=(1.0, 2.0)
    )
    
    # Inhibitory to all
    network.create_random_connections(
        pre_ids=inh_ids,
        post_ids=exc_ids + inh_ids,
        connection_prob=connectivity,
        weight_range=(-0.4, -0.2),  # Negative weights for inhibition
        delay_range=(1.0, 2.0)
    )
    
    # Add initial random spikes to excitatory neurons
    network.add_input_spikes(
        neuron_ids=exc_ids[:10],  # Stimulate a subset
        rate=50.0,  # Hz
        start_time=0.0,
        end_time=sim_time
    )
    
    return network, exc_ids, inh_ids


def create_event_based_coincidence_detector(
    n_inputs: int = 5,
    coincidence_window: float = 5.0  # ms
) -> Tuple[EventBasedNeuralNetwork, int, List[int]]:
    """
    Create a coincidence detector network.
    
    Args:
        n_inputs: Number of input neurons
        coincidence_window: Time window for coincidence detection
        
    Returns:
        Tuple of (network, output_id, input_ids)
    """
    network = EventBasedNeuralNetwork()
    
    # Create input neurons
    input_ids = []
    for i in range(n_inputs):
        neuron_id = network.create_neuron(
            threshold=0.1,  # Low threshold to ensure spikes propagate
            reset_value=0.0,
            decay_constant=10.0
        )
        input_ids.append(neuron_id)
    
    # Create output neuron with high threshold
    # It will only fire if multiple inputs are active within the coincidence window
    output_id = network.create_neuron(
        threshold=0.8,  # High threshold requiring multiple inputs
        reset_value=0.0,
        decay_constant=coincidence_window,  # Decay matches coincidence window
        refractory_period=1.0
    )
    
    # Connect inputs to output with appropriate weights
    # Each input contributes partially to reaching the threshold
    weight = 0.3  # Each input provides 30% of threshold
    for input_id in input_ids:
        network.create_synapse(
            pre_id=input_id,
            post_id=output_id,
            weight=weight,
            delay=0.5  # Short delay
        )
    
    return network, output_id, input_ids


def create_event_based_synfire_chain(
    chain_length: int = 10,
    neurons_per_group: int = 5,
    intra_weight: float = 0.25,
    inter_weight: float = 1.2,
    delay: float = 1.0
) -> Tuple[EventBasedNeuralNetwork, List[List[int]]]:
    """
    Create a synfire chain network (layered feed-forward chain).
    
    Args:
        chain_length: Number of groups in the chain
        neurons_per_group: Number of neurons per group
        intra_weight: Weight of connections within a group
        inter_weight: Weight of connections between groups
        delay: Synaptic delay
        
    Returns:
        Tuple of (network, neuron_groups)
    """
    network = EventBasedNeuralNetwork()
    
    # Create neuron groups
    neuron_groups = []
    for i in range(chain_length):
        group = []
        for j in range(neurons_per_group):
            # Create neuron with appropriate parameters
            neuron_id = network.create_neuron(
                threshold=1.0,
                reset_value=0.0,
                decay_constant=15.0,
                refractory_period=2.0
            )
            group.append(neuron_id)
        neuron_groups.append(group)
    
    # Create intra-group connections (within each group)
    for group in neuron_groups:
        network.create_random_connections(
            pre_ids=group,
            post_ids=group,
            connection_prob=0.3,
            weight_range=(intra_weight * 0.8, intra_weight * 1.2),
            delay_range=(delay * 0.8, delay * 1.2)
        )
    
    # Create inter-group connections (between adjacent groups)
    for i in range(chain_length - 1):
        # Connect each neuron in group i to each neuron in group i+1
        network.create_fully_connected(
            pre_ids=neuron_groups[i],
            post_ids=neuron_groups[i + 1],
            weight=inter_weight,
            delay=delay
        )
    
    return network, neuron_groups


def create_event_based_oscillator(
    n_excitatory: int = 20,
    n_inhibitory: int = 5,
    e_to_e_weight: float = 0.12,
    e_to_i_weight: float = 0.2,
    i_to_e_weight: float = -0.3,
    delay: float = 1.0
) -> Tuple[EventBasedNeuralNetwork, List[int], List[int]]:
    """
    Create an oscillating neural network.
    
    Args:
        n_excitatory: Number of excitatory neurons
        n_inhibitory: Number of inhibitory neurons
        e_to_e_weight: Weight from excitatory to excitatory
        e_to_i_weight: Weight from excitatory to inhibitory
        i_to_e_weight: Weight from inhibitory to excitatory (negative)
        delay: Synaptic delay
        
    Returns:
        Tuple of (network, excitatory_ids, inhibitory_ids)
    """
    network = EventBasedNeuralNetwork()
    
    # Create excitatory neurons
    excitatory_ids = []
    for i in range(n_excitatory):
        neuron_id = network.create_neuron(
            threshold=1.0,
            reset_value=0.0,
            decay_constant=20.0,
            refractory_period=2.0
        )
        excitatory_ids.append(neuron_id)
    
    # Create inhibitory neurons
    inhibitory_ids = []
    for i in range(n_inhibitory):
        neuron_id = network.create_neuron(
            threshold=1.0,
            reset_value=0.0,
            decay_constant=10.0,
            refractory_period=1.0
        )
        inhibitory_ids.append(neuron_id)
    
    # Create excitatory to excitatory connections
    network.create_random_connections(
        pre_ids=excitatory_ids,
        post_ids=excitatory_ids,
        connection_prob=0.3,
        weight_range=(e_to_e_weight * 0.8, e_to_e_weight * 1.2),
        delay_range=(delay * 0.8, delay * 1.2)
    )
    
    # Create excitatory to inhibitory connections
    network.create_random_connections(
        pre_ids=excitatory_ids,
        post_ids=inhibitory_ids,
        connection_prob=0.4,
        weight_range=(e_to_i_weight * 0.8, e_to_i_weight * 1.2),
        delay_range=(delay * 0.8, delay * 1.2)
    )
    
    # Create inhibitory to excitatory connections
    network.create_random_connections(
        pre_ids=inhibitory_ids,
        post_ids=excitatory_ids,
        connection_prob=0.5,
        weight_range=(i_to_e_weight * 1.2, i_to_e_weight * 0.8),  # Note: weight is negative
        delay_range=(delay * 0.8, delay * 1.2)
    )
    
    # Add initial spikes to some excitatory neurons to start oscillation
    initial_spikes = [10.0, 10.5, 11.0, 11.5, 12.0]
    for i, neuron_id in enumerate(excitatory_ids[:5]):
        network.add_spike(initial_spikes[i], neuron_id)
    
    return network, excitatory_ids, inhibitory_ids


def create_event_based_pattern_recognizer(
    input_size: int = 10,
    hidden_size: int = 5,
    output_size: int = 3,
    stdp_enabled: bool = True
) -> Tuple[EventBasedNeuralNetwork, List[int], List[int], List[int]]:
    """
    Create a pattern recognition network with STDP learning.
    
    Args:
        input_size: Number of input neurons
        hidden_size: Number of hidden neurons
        output_size: Number of output neurons
        stdp_enabled: Enable STDP plasticity
        
    Returns:
        Tuple of (network, input_ids, hidden_ids, output_ids)
    """
    network = EventBasedNeuralNetwork()
    
    # Create input neurons
    input_ids = []
    for i in range(input_size):
        neuron_id = network.create_neuron(
            threshold=0.5,
            reset_value=0.0,
            decay_constant=10.0,
            refractory_period=1.0
        )
        input_ids.append(neuron_id)
    
    # Create hidden neurons
    hidden_ids = []
    for i in range(hidden_size):
        neuron_id = network.create_neuron(
            threshold=1.0,
            reset_value=0.0,
            decay_constant=15.0,
            refractory_period=2.0
        )
        hidden_ids.append(neuron_id)
    
    # Create output neurons
    output_ids = []
    for i in range(output_size):
        neuron_id = network.create_neuron(
            threshold=1.0,
            reset_value=0.0,
            decay_constant=20.0,
            refractory_period=3.0
        )
        output_ids.append(neuron_id)
    
    # Create connections from input to hidden with STDP
    stdp_params = {
        'a_plus': 0.005,   # Smaller learning rate for stability
        'a_minus': 0.0055, # Slightly stronger depression
        'tau_plus': 20.0,
        'tau_minus': 20.0,
        'w_min': 0.0,
        'w_max': 1.5
    }
    
    for input_id in input_ids:
        for hidden_id in hidden_ids:
            network.create_synapse(
                pre_id=input_id,
                post_id=hidden_id,
                weight=np.random.uniform(0.3, 0.7),
                delay=np.random.uniform(0.8, 1.2),
                stdp_enabled=stdp_enabled,
                stdp_params=stdp_params if stdp_enabled else None
            )
    
    # Create connections from hidden to output
    for hidden_id in hidden_ids:
        for output_id in output_ids:
            network.create_synapse(
                pre_id=hidden_id,
                post_id=output_id,
                weight=np.random.uniform(0.3, 0.7),
                delay=np.random.uniform(0.8, 1.2),
                stdp_enabled=stdp_enabled,
                stdp_params=stdp_params if stdp_enabled else None
            )
    
    return network, input_ids, hidden_ids, output_ids


def encode_event_data_from_image(
    image: np.ndarray,
    threshold: float = 0.1,
    x_size: int = None,
    y_size: int = None,
    encoding_method: str = 'change',
    timestamp: float = 0.0
) -> List[Event]:
    """
    Convert an image to events using different encoding methods.
    
    Args:
        image: Input image as numpy array
        threshold: Threshold for event generation
        x_size: Target x size (resample if provided)
        y_size: Target y size (resample if provided)
        encoding_method: 'change', 'threshold', or 'gradient'
        timestamp: Base timestamp for events
        
    Returns:
        List of generated events
    """
    # Optional resizing
    if x_size is not None and y_size is not None:
        from skimage.transform import resize
        image = resize(image, (y_size, x_size), preserve_range=True)
    
    # Ensure image is normalized
    if image.max() > 1.0:
        image = image / 255.0
    
    events = []
    
    if encoding_method == 'change':
        # Generate events based on pixel intensity changes
        # Simulate changes by comparing with slightly blurred version
        from scipy.ndimage import gaussian_filter
        blurred = gaussian_filter(image, sigma=1.0)
        diff = image - blurred
        
        for y in range(image.shape[0]):
            for x in range(image.shape[1]):
                if abs(diff[y, x]) > threshold:
                    source_id = y * image.shape[1] + x
                    polarity = 1 if diff[y, x] > 0 else -1
                    magnitude = abs(diff[y, x])
                    
                    event = ChangeEvent(
                        timestamp=timestamp + np.random.uniform(0, 1.0), # Small time variation
                        source_id=source_id,
                        polarity=polarity,
                        magnitude=magnitude,
                        coordinates=(x, y)
                    )
                    events.append(event)
                    
    elif encoding_method == 'threshold':
        # Generate events when pixel exceeds threshold
        for y in range(image.shape[0]):
            for x in range(image.shape[1]):
                if image[y, x] > threshold:
                    source_id = y * image.shape[1] + x
                    event = ChangeEvent(
                        timestamp=timestamp + image[y, x], # Brighter pixels fire earlier
                        source_id=source_id,
                        polarity=1,
                        magnitude=image[y, x],
                        coordinates=(x, y)
                    )
                    events.append(event)
                    
    elif encoding_method == 'gradient':
        # Generate events based on image gradients
        from scipy.ndimage import sobel
        gradient_x = sobel(image, axis=1)
        gradient_y = sobel(image, axis=0)
        gradient_magnitude = np.sqrt(gradient_x**2 + gradient_y**2)
        
        for y in range(image.shape[0]):
            for x in range(image.shape[1]):
                if gradient_magnitude[y, x] > threshold:
                    source_id = y * image.shape[1] + x
                    # Compute gradient direction
                    angle = np.arctan2(gradient_y[y, x], gradient_x[y, x])
                    event = EdgeEvent(
                        timestamp=timestamp + np.random.uniform(0, 1.0),
                        source_id=source_id,
                        polarity=1,
                        coordinates=(x, y),
                        orientation=angle,
                        strength=gradient_magnitude[y, x]
                    )
                    events.append(event)
    
    # Sort events by timestamp
    events.sort(key=lambda e: e.timestamp)
    return events


def encode_event_data_from_time_series(
    time_series: np.ndarray,
    threshold: float = 0.1,
    base_timestamp: float = 0.0,
    time_factor: float = 1.0,
    encoding_method: str = 'change'
) -> List[Event]:
    """
    Convert a time series to events.
    
    Args:
        time_series: Input time series (samples × channels)
        threshold: Threshold for event generation
        base_timestamp: Starting timestamp
        time_factor: Scaling factor for time
        encoding_method: 'change', 'threshold', or 'zero_crossing'
        
    Returns:
        List of generated events
    """
    events = []
    
    if encoding_method == 'change':
        # Generate events when signal changes by more than threshold
        for channel in range(time_series.shape[1]):
            last_value = time_series[0, channel]
            for i in range(1, time_series.shape[0]):
                current_value = time_series[i, channel]
                change = current_value - last_value
                
                if abs(change) > threshold:
                    timestamp = base_timestamp + i * time_factor
                    polarity = 1 if change > 0 else -1
                    
                    event = ChangeEvent(
                        timestamp=timestamp,
                        source_id=channel,
                        polarity=polarity,
                        magnitude=abs(change)
                    )
                    events.append(event)
                    last_value = current_value
                    
    elif encoding_method == 'threshold':
        # Generate events when signal exceeds threshold
        for channel in range(time_series.shape[1]):
            for i in range(time_series.shape[0]):
                value = time_series[i, channel]
                
                if abs(value) > threshold:
                    timestamp = base_timestamp + i * time_factor
                    polarity = 1 if value > 0 else -1
                    
                    event = ChangeEvent(
                        timestamp=timestamp,
                        source_id=channel,
                        polarity=polarity,
                        magnitude=abs(value)
                    )
                    events.append(event)
                    
    elif encoding_method == 'zero_crossing':
        # Generate events at zero crossings
        for channel in range(time_series.shape[1]):
            last_sign = np.sign(time_series[0, channel])
            for i in range(1, time_series.shape[0]):
                current_sign = np.sign(time_series[i, channel])
                
                if current_sign != last_sign and current_sign != 0:
                    timestamp = base_timestamp + i * time_factor
                    polarity = 1 if current_sign > 0 else -1
                    
                    event = ChangeEvent(
                        timestamp=timestamp,
                        source_id=channel,
                        polarity=polarity,
                        magnitude=abs(time_series[i, channel])
                    )
                    events.append(event)
                    last_sign = current_sign
    
    # Sort events by timestamp
    events.sort(key=lambda e: e.timestamp)
    return events


def analyze_event_statistics(events: List[Event]) -> Dict[str, Any]:
    """
    Analyze statistical properties of event stream.
    
    Args:
        events: List of events
        
    Returns:
        Dictionary of statistics
    """
    if not events:
        return {"error": "No events to analyze"}
    
    # Extract timestamps
    timestamps = [event.timestamp for event in events]
    
    # Basic time statistics
    start_time = min(timestamps)
    end_time = max(timestamps)
    duration = end_time - start_time
    
    # Event count statistics
    total_events = len(events)
    event_rate = total_events / duration if duration > 0 else 0
    
    # Inter-event interval statistics
    intervals = np.diff(sorted(timestamps))
    mean_interval = np.mean(intervals) if len(intervals) > 0 else 0
    median_interval = np.median(intervals) if len(intervals) > 0 else 0
    min_interval = np.min(intervals) if len(intervals) > 0 else 0
    max_interval = np.max(intervals) if len(intervals) > 0 else 0
    
    # Source statistics
    source_ids = [event.source_id for event in events]
    unique_sources = len(set(source_ids))
    source_counts = {}
    for source_id in source_ids:
        if source_id in source_counts:
            source_counts[source_id] += 1
        else:
            source_counts[source_id] = 1
    
    most_active_source = max(source_counts.items(), key=lambda x: x[1]) if source_counts else (None, 0)
    
    # Event type statistics
    event_types = [event.event_type for event in events]
    type_counts = {}
    for event_type in event_types:
        type_name = event_type.name
        if type_name in type_counts:
            type_counts[type_name] += 1
        else:
            type_counts[type_name] = 1
    
    # Polarity statistics
    polarities = [event.polarity for event in events if hasattr(event, 'polarity')]
    positive_count = sum(1 for p in polarities if p > 0)
    negative_count = sum(1 for p in polarities if p < 0)
    
    # Compile statistics
    statistics = {
        "temporal": {
            "start_time": start_time,
            "end_time": end_time,
            "duration": duration,
            "total_events": total_events,
            "event_rate": event_rate,
            "mean_interval": mean_interval,
            "median_interval": median_interval,
            "min_interval": min_interval,
            "max_interval": max_interval
        },
        "spatial": {
            "unique_sources": unique_sources,
            "most_active_source": most_active_source[0],
            "most_active_count": most_active_source[1],
            "source_distribution": source_counts
        },
        "types": {
            "type_distribution": type_counts,
            "positive_events": positive_count,
            "negative_events": negative_count,
            "polarity_ratio": positive_count / negative_count if negative_count > 0 else float('inf')
        }
    }
    
    return statistics


def visualize_event_data(
    events: List[Event],
    plot_type: str = 'raster',
    title: str = 'Event Visualization',
    figsize: Tuple[int, int] = (12, 8),
    time_range: Optional[Tuple[float, float]] = None,
    source_range: Optional[Tuple[int, int]] = None
) -> None:
    """
    Visualize event data in different formats.
    
    Args:
        events: List of events
        plot_type: 'raster', 'heatmap', '3d', 'histogram'
        title: Plot title
        figsize: Figure size
        time_range: Optional (min_time, max_time)
        source_range: Optional (min_source, max_source)
    """
    if not events:
        print("No events to visualize")
        return
    
    # Extract data
    timestamps = np.array([event.timestamp for event in events])
    source_ids = np.array([event.source_id for event in events])
    
    # Apply time range filter if provided
    if time_range is not None:
        min_time, max_time = time_range
        time_mask = (timestamps >= min_time) & (timestamps <= max_time)
        timestamps = timestamps[time_mask]
        source_ids = source_ids[time_mask]
    
    # Apply source range filter if provided
    if source_range is not None:
        min_source, max_source = source_range
        source_mask = (source_ids >= min_source) & (source_ids <= max_source)
        timestamps = timestamps[source_mask]
        source_ids = source_ids[source_mask]
    
    # Create figure
    plt.figure(figsize=figsize)
    
    if plot_type == 'raster':
        # Raster plot (source vs time)
        plt.scatter(timestamps, source_ids, marker='|', s=10, alpha=0.7)
        plt.xlabel('Time (ms)')
        plt.ylabel('Source ID')
        plt.title(f'{title} - Raster Plot')
        plt.grid(True, linestyle='--', alpha=0.7)
        
    elif plot_type == 'heatmap':
        # Heatmap of event density
        from scipy.stats import binned_statistic_2d
        
        # Create 2D histogram
        counts, xedges, yedges = np.histogram2d(
            timestamps, source_ids, 
            bins=[50, min(100, len(set(source_ids)))]
        )
        
        # Plot heatmap
        plt.imshow(
            counts.T, 
            aspect='auto', 
            origin='lower',
            extent=[xedges[0], xedges[-1], yedges[0], yedges[-1]],
            interpolation='nearest',
            cmap='viridis'
        )
        plt.colorbar(label='Event Count')
        plt.xlabel('Time (ms)')
        plt.ylabel('Source ID')
        plt.title(f'{title} - Event Density Heatmap')
        
    elif plot_type == '3d':
        # 3D visualization for spatial events
        from mpl_toolkits.mplot3d import Axes3D
        
        # Check if events have coordinates
        coordinates = []
        for event in events:
            if hasattr(event, 'payload') and event.payload and 'coordinates' in event.payload:
                coords = event.payload['coordinates']
                if coords:
                    coordinates.append(coords + (event.timestamp,))
        
        if coordinates:
            fig = plt.figure(figsize=figsize)
            ax = fig.add_subplot(111, projection='3d')
            
            x, y, t = zip(*coordinates)
            polarities = [event.polarity for event in events[:len(coordinates)]]
            colors = ['red' if p > 0 else 'blue' for p in polarities]
            
            ax.scatter(x, y, t, c=colors, s=15, alpha=0.7)
            ax.set_xlabel('X')
            ax.set_ylabel('Y')
            ax.set_zlabel('Time (ms)')
            ax.set_title(f'{title} - 3D Spatio-temporal Visualization')
        else:
            plt.text(0.5, 0.5, 'No spatial coordinates available', 
                   horizontalalignment='center', verticalalignment='center')
            
    elif plot_type == 'histogram':
        # Histogram of interspike intervals
        intervals = np.diff(np.sort(timestamps))
        
        plt.hist(intervals, bins=50, alpha=0.7)
        plt.xlabel('Inter-event Interval (ms)')
        plt.ylabel('Count')
        plt.title(f'{title} - Inter-event Interval Histogram')
        plt.grid(True, linestyle='--', alpha=0.7)
        
        # Add stats
        mean_interval = np.mean(intervals)
        median_interval = np.median(intervals)
        plt.axvline(mean_interval, color='r', linestyle='--', label=f'Mean: {mean_interval:.2f} ms')
        plt.axvline(median_interval, color='g', linestyle='--', label=f'Median: {median_interval:.2f} ms')
        plt.legend()
    
    plt.tight_layout()
    plt.show()


def initialize_event_processing(config=None):
    """
    Initialize the event processing module with the given configuration.
    
    Args:
        config: Optional configuration dictionary
        
    Returns:
        Status dictionary
    """
    logger.info("Initializing Event-Based Computing System")
    
    status = {
        'event_types_registered': len(list(EventType)),
        'processors_ready': True,
        'initialization_complete': True
    }
    
    if config is not None:
        # Apply configuration if provided
        if 'logging_level' in config:
            logger.setLevel(config['logging_level'])
            
        if 'event_queue_limit' in config:
            # This would require modifying EventQueue to have a size limit
            status['queue_limit_set'] = config['event_queue_limit']
    
    return status