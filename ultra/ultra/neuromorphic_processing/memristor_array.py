#!/usr/bin/env python3
"""
ULTRA: Neuromorphic Processing Layer - Memristor Array

This module implements memristor-based computing elements for neuromorphic processing.
Memristors are non-volatile memory devices whose resistance changes based on the history
of applied voltage/current, making them ideal for implementing synaptic weights in 
hardware neural networks. The module includes models for individual memristors, 
crossbar arrays for vector-matrix multiplication, and various learning mechanisms.

Components:
1. Memristor models (linear, nonlinear, stochastic)
2. Crossbar arrays for efficient vector-matrix multiplication
3. Memristive neuromorphic circuit elements
4. Learning algorithms (supervised and unsupervised)
5. Non-ideality modeling and compensation mechanisms
"""

import numpy as np
import scipy.sparse as sparse
from typing import Dict, List, Tuple, Set, Any, Optional, Union, Callable, Type
from dataclasses import dataclass, field
from enum import Enum
import logging
import time
import matplotlib.pyplot as plt
from collections import defaultdict, deque
import warnings

logger = logging.getLogger(__name__)

# Constants for unit conversion
V_TO_MV = 1000.0  # Convert volts to millivolts
UA_TO_A = 1e-6    # Convert microamps to amps
NS_TO_S = 1e-9    # Convert nanoseconds to seconds
MS_TO_S = 1e-3    # Convert milliseconds to seconds


class MemristorType(Enum):
    """Types of memristor models supported."""
    LINEAR = "linear"
    NONLINEAR = "nonlinear"
    JOGLEKAR = "joglekar"
    SIMMONS_TUNNEL = "simmons_tunnel"
    YAKOPCIC = "yakopcic"
    DIGITAL = "digital"
    STOCHASTIC = "stochastic"
    DRIFT = "drift"
    CUSTOM = "custom"


@dataclass
class MemristorMaterialParameters:
    """
    Physical parameters for memristor models based on material properties.
    
    These parameters are used for more physically accurate memristor models.
    """
    # Device geometry
    D: float = 10.0e-9      # Device thickness (m)
    area: float = 100.0e-12  # Device area (m²)
    
    # Material properties
    mu_p: float = 1.0e-10    # Ion mobility (m²/V·s)
    rho_on: float = 1.0e-2   # Resistivity of conductive filament (Ω·m)
    rho_off: float = 1.0e+6  # Resistivity of insulating region (Ω·m)
    
    # Energy parameters
    E_h: float = 0.5         # Migration barrier height (eV)
    phi_0: float = 1.0       # Standard barrier height (eV)
    
    # Tunnel barrier parameters
    lambda_0: float = 0.25e-9  # Tunnel barrier width constant (m)
    beta: float = 10.0e+9     # Tunnel barrier parameter (m⁻¹)


class Memristor:
    """
    Base memristor device model.
    
    This class implements a general memristor model with state-dependent resistance
    that changes based on applied voltage history.
    """
    
    def __init__(
        self,
        r_on: float = 100.0,       # Resistance in low resistance state (ON) in ohms
        r_off: float = 10000.0,    # Resistance in high resistance state (OFF) in ohms
        r_init: float = 5000.0,    # Initial resistance in ohms
        model_type: MemristorType = MemristorType.LINEAR,
        window_fn: Optional[Callable] = None,
        threshold_voltage: float = 0.25,  # Voltage threshold for switching in volts
        write_factor: float = 1.0e-4,     # Write rate parameter
        material_params: Optional[MemristorMaterialParameters] = None
    ):
        """
        Initialize memristor model.
        
        Args:
            r_on: Resistance in low resistance state (ON) in ohms
            r_off: Resistance in high resistance state (OFF) in ohms
            r_init: Initial resistance in ohms
            model_type: Type of memristor model
            window_fn: Optional window function for nonlinear models
            threshold_voltage: Voltage threshold for switching in volts
            write_factor: Write rate parameter
            material_params: Optional physical material parameters
        """
        self.r_on = r_on
        self.r_off = r_off
        self.r = r_init
        self.model_type = model_type
        self.threshold_voltage = threshold_voltage
        self.write_factor = write_factor
        
        # Initialize state variable
        if r_init <= r_on:
            self.state = 1.0
        elif r_init >= r_off:
            self.state = 0.0
        else:
            self.state = (r_off - r_init) / (r_off - r_on)
        
        # Set window function for nonlinear dynamics
        if window_fn is None:
            self.window_fn = self._default_window_fn
        else:
            self.window_fn = window_fn
        
        # Set material parameters
        if material_params is None:
            self.material_params = MemristorMaterialParameters()
        else:
            self.material_params = material_params
        
        # History tracking
        self.history = {
            'time': [],
            'voltage': [],
            'current': [],
            'resistance': [],
            'state': []
        }
        
        self.time = 0.0  # Internal time tracking
    
    def _default_window_fn(self, x: float) -> float:
        """
        Default window function (Joglekar window function).
        
        The window function ensures state remains within [0, 1] and 
        models nonlinear dynamics near boundaries.
        
        Args:
            x: State variable (0 to 1)
            
        Returns:
            Window function value
        """
        p = 1  # Window function parameter
        return 1 - ((2 * x - 1) ** (2 * p))
    
    def _compute_resistance(self) -> float:
        """
        Compute resistance based on current state.
        
        Returns:
            Current resistance in ohms
        """
        return self.r_off - self.state * (self.r_off - self.r_on)
    
    def apply_voltage(self, voltage: float, dt: float) -> float:
        """
        Apply voltage across memristor for a time step and compute current.
        
        Args:
            voltage: Applied voltage in volts
            dt: Time step in seconds
            
        Returns:
            Current through memristor in amperes
        """
        # Update state based on voltage
        self._update_state(voltage, dt)
        
        # Compute current
        current = voltage / self.r
        
        # Update time
        self.time += dt
        
        # Update history
        if len(self.history['time']) < 1000:  # Limit history size
            self.history['time'].append(self.time)
            self.history['voltage'].append(voltage)
            self.history['current'].append(current)
            self.history['resistance'].append(self.r)
            self.history['state'].append(self.state)
        
        return current
    
    def _update_state(self, voltage: float, dt: float) -> None:
        """
        Update internal state based on applied voltage.
        
        Args:
            voltage: Applied voltage in volts
            dt: Time step in seconds
        """
        # Check if voltage exceeds threshold
        if abs(voltage) <= self.threshold_voltage:
            return
        
        # Different models use different update equations
        if self.model_type == MemristorType.LINEAR:
            # Simple linear model
            dw = self.write_factor * voltage * dt
            self.state = np.clip(self.state + dw, 0.0, 1.0)
            
        elif self.model_type == MemristorType.NONLINEAR:
            # Nonlinear model with window function
            dw = self.write_factor * voltage * self.window_fn(self.state) * dt
            self.state = np.clip(self.state + dw, 0.0, 1.0)
            
        elif self.model_type == MemristorType.JOGLEKAR:
            # Joglekar model
            dw = self.write_factor * voltage * self._joglekar_window(self.state) * dt
            self.state = np.clip(self.state + dw, 0.0, 1.0)
            
        elif self.model_type == MemristorType.DRIFT:
            # Drift model (memristor behavior changes over time)
            # Mimics physical phenomena like filament degradation
            drift_factor = np.exp(-self.time / 3600.0)  # Decay over hours
            effective_write_factor = self.write_factor * drift_factor
            dw = effective_write_factor * voltage * self.window_fn(self.state) * dt
            self.state = np.clip(self.state + dw, 0.0, 1.0)
            
        elif self.model_type == MemristorType.STOCHASTIC:
            # Stochastic model with random variations
            noise_amplitude = 0.05 * abs(voltage) / self.threshold_voltage
            noise = np.random.normal(0, noise_amplitude)
            dw = self.write_factor * voltage * self.window_fn(self.state) * dt
            dw += noise * dt
            self.state = np.clip(self.state + dw, 0.0, 1.0)
            
        elif self.model_type == MemristorType.DIGITAL:
            # Binary memristor that only switches between discrete states
            if voltage > self.threshold_voltage:
                self.state = 1.0
            elif voltage < -self.threshold_voltage:
                self.state = 0.0
            
        else:
            # Default to linear model
            dw = self.write_factor * voltage * dt
            self.state = np.clip(self.state + dw, 0.0, 1.0)
        
        # Update resistance based on new state
        self.r = self._compute_resistance()
    
    def _joglekar_window(self, x: float, p: float = 1.0) -> float:
        """
        Joglekar window function.
        
        Args:
            x: State variable (0 to 1)
            p: Window function parameter
            
        Returns:
            Window function value
        """
        return 1 - ((2 * x - 1) ** (2 * p))
    
    def reset(self) -> None:
        """Reset memristor to initial state."""
        self.state = (self.r_off - self.r) / (self.r_off - self.r_on)
        self.r = self._compute_resistance()
        self.time = 0.0
        
        # Clear history
        for key in self.history:
            self.history[key] = []
    
    def set_state(self, state: float) -> None:
        """
        Set memristor state directly.
        
        Args:
            state: New state value (0 to 1)
        """
        self.state = np.clip(state, 0.0, 1.0)
        self.r = self._compute_resistance()
    
    def plot_history(self) -> None:
        """Plot memristor behavior history."""
        if not self.history['time']:
            print("No history data available.")
            return
        
        fig, axes = plt.subplots(4, 1, figsize=(10, 12), sharex=True)
        
        # Voltage
        axes[0].plot(self.history['time'], self.history['voltage'])
        axes[0].set_ylabel('Voltage (V)')
        axes[0].grid(True)
        
        # Current
        axes[1].plot(self.history['time'], self.history['current'])
        axes[1].set_ylabel('Current (A)')
        axes[1].grid(True)
        
        # Resistance
        axes[2].plot(self.history['time'], self.history['resistance'])
        axes[2].set_ylabel('Resistance (Ω)')
        axes[2].grid(True)
        
        # State
        axes[3].plot(self.history['time'], self.history['state'])
        axes[3].set_ylabel('State')
        axes[3].set_xlabel('Time (s)')
        axes[3].grid(True)
        
        plt.tight_layout()
        plt.show()
    
    def hysteresis_loop(
        self,
        v_max: float = 1.0,
        freq: float = 1.0,
        cycles: int = 1,
        points_per_cycle: int = 100
    ) -> Tuple[List[float], List[float]]:
        """
        Simulate a voltage sweep to demonstrate memristor hysteresis.
        
        Args:
            v_max: Maximum voltage amplitude
            freq: Frequency of voltage oscillation (Hz)
            cycles: Number of cycles to simulate
            points_per_cycle: Number of sample points per cycle
            
        Returns:
            Tuple of (voltage_values, current_values)
        """
        # Save original state to restore later
        original_state = self.state
        original_r = self.r
        original_time = self.time
        
        # Reset for clean experiment
        self.reset()
        
        voltages = []
        currents = []
        
        # Generate sine wave voltage
        t_cycle = 1.0 / freq
        dt = t_cycle / points_per_cycle
        
        for cycle in range(cycles):
            for i in range(points_per_cycle):
                t = i * dt
                voltage = v_max * np.sin(2 * np.pi * freq * t)
                current = self.apply_voltage(voltage, dt)
                
                voltages.append(voltage)
                currents.append(current)
        
        # Restore original state
        self.state = original_state
        self.r = original_r
        self.time = original_time
        
        return voltages, currents
    
    def __repr__(self) -> str:
        """String representation of memristor."""
        return (f"Memristor({self.model_type.value}, "
                f"r={self.r:.2f}Ω, state={self.state:.3f}, "
                f"r_on={self.r_on}Ω, r_off={self.r_off}Ω)")


class StochasticMemristorModel(Memristor):
    """
    Stochastic memristor model with cycle-to-cycle and device-to-device variations.
    
    This model extends the base memristor model to include random variations that
    better reflect the behavior of real memristor devices.
    """
    
    def __init__(
        self,
        r_on: float = 100.0,
        r_off: float = 10000.0,
        r_init: float = 5000.0,
        c2c_variation: float = 0.1,  # Cycle-to-cycle variation coefficient
        d2d_variation: float = 0.05,  # Device-to-device variation coefficient
        switching_probability: float = 0.95,  # Probability of successful switching
        **kwargs
    ):
        """
        Initialize stochastic memristor model.
        
        Args:
            r_on: Resistance in low resistance state (ON) in ohms
            r_off: Resistance in high resistance state (OFF) in ohms
            r_init: Initial resistance in ohms
            c2c_variation: Cycle-to-cycle variation coefficient
            d2d_variation: Device-to-device variation coefficient
            switching_probability: Probability of successful switching
            **kwargs: Additional parameters for base Memristor class
        """
        super().__init__(
            r_on=r_on,
            r_off=r_off,
            r_init=r_init,
            model_type=MemristorType.STOCHASTIC,
            **kwargs
        )
        
        self.c2c_variation = c2c_variation
        self.d2d_variation = d2d_variation
        self.switching_probability = switching_probability
        
        # Apply device-to-device variation to r_on and r_off
        self.r_on = r_on * (1 + np.random.normal(0, d2d_variation))
        self.r_off = r_off * (1 + np.random.normal(0, d2d_variation))
        
        # Recalculate resistance
        self.r = self._compute_resistance()
    
    def _update_state(self, voltage: float, dt: float) -> None:
        """
        Update internal state with stochastic variations.
        
        Args:
            voltage: Applied voltage in volts
            dt: Time step in seconds
        """
        # Check if voltage exceeds threshold
        if abs(voltage) <= self.threshold_voltage:
            return
        
        # Apply cycle-to-cycle variation
        effective_voltage = voltage * (1 + np.random.normal(0, self.c2c_variation))
        
        # Determine if switching occurs based on probability
        if np.random.random() < self.switching_probability:
            # Compute state change with noise
            noise = np.random.normal(0, 0.05 * abs(effective_voltage) / self.threshold_voltage)
            dw = self.write_factor * effective_voltage * self.window_fn(self.state) * dt
            dw += noise * dt
            
            self.state = np.clip(self.state + dw, 0.0, 1.0)
            
            # Update resistance based on new state
            self.r = self._compute_resistance()


class DriftMemristorModel(Memristor):
    """
    Memristor model with state drift/decay over time.
    
    This model simulates the phenomenon where memristor state gradually
    changes (typically towards higher resistance) over time, even in the
    absence of applied voltage. This is common in real devices due to
    ion diffusion and other physical processes.
    """
    
    def __init__(
        self,
        r_on: float = 100.0,
        r_off: float = 10000.0,
        r_init: float = 5000.0,
        retention_time_constant: float = 3600.0,  # Time constant for state decay in seconds
        temperature_coefficient: float = 0.01,    # Temperature dependence coefficient
        reference_temperature: float = 300.0,     # Reference temperature in Kelvin
        current_temperature: float = 300.0,       # Current temperature in Kelvin
        **kwargs
    ):
        """
        Initialize drift memristor model.
        
        Args:
            r_on: Resistance in low resistance state (ON) in ohms
            r_off: Resistance in high resistance state (OFF) in ohms
            r_init: Initial resistance in ohms
            retention_time_constant: Time constant for state decay in seconds
            temperature_coefficient: Temperature dependence coefficient
            reference_temperature: Reference temperature in Kelvin
            current_temperature: Current temperature in Kelvin
            **kwargs: Additional parameters for base Memristor class
        """
        super().__init__(
            r_on=r_on,
            r_off=r_off,
            r_init=r_init,
            model_type=MemristorType.DRIFT,
            **kwargs
        )
        
        self.retention_time_constant = retention_time_constant
        self.temperature_coefficient = temperature_coefficient
        self.reference_temperature = reference_temperature
        self.current_temperature = current_temperature
        
        # Last update time for drift calculation
        self.last_update_time = 0.0
    
    def set_temperature(self, temperature: float) -> None:
        """
        Set current operating temperature.
        
        Args:
            temperature: Temperature in Kelvin
        """
        self.current_temperature = temperature
    
    def _update_state(self, voltage: float, dt: float) -> None:
        """
        Update state with both voltage-based switching and time-based drift.
        
        Args:
            voltage: Applied voltage in volts
            dt: Time step in seconds
        """
        # Calculate time since last update for drift
        time_since_last_update = self.time - self.last_update_time
        self.last_update_time = self.time
        
        # Apply time-based drift (accelerated by temperature)
        if time_since_last_update > 0:
            # Temperature acceleration factor
            temp_factor = np.exp(self.temperature_coefficient * 
                               (self.current_temperature - self.reference_temperature))
            
            # State drifts toward 0 (high resistance state)
            drift_rate = temp_factor / self.retention_time_constant
            drift_amount = drift_rate * time_since_last_update * self.state
            self.state = max(0.0, self.state - drift_amount)
        
        # Voltage-based switching (standard model)
        if abs(voltage) > self.threshold_voltage:
            dw = self.write_factor * voltage * self.window_fn(self.state) * dt
            self.state = np.clip(self.state + dw, 0.0, 1.0)
        
        # Update resistance based on new state
        self.r = self._compute_resistance()


class MemristorArray:
    """
    Array of memristor devices.
    
    This class implements a 2D array of memristors, which can be used
    for various neuromorphic computing applications.
    """
    
    def __init__(
        self,
        rows: int,
        cols: int,
        r_on: float = 100.0,
        r_off: float = 10000.0,
        r_init: Optional[Union[float, np.ndarray]] = None,
        model_type: MemristorType = MemristorType.LINEAR,
        **kwargs
    ):
        """
        Initialize memristor array.
        
        Args:
            rows: Number of rows in the array
            cols: Number of columns in the array
            r_on: Resistance in low resistance state (ON) in ohms
            r_off: Resistance in high resistance state (OFF) in ohms
            r_init: Initial resistance in ohms (scalar or array)
            model_type: Type of memristor model
            **kwargs: Additional parameters for Memristor class
        """
        self.rows = rows
        self.cols = cols
        
        # Initialize memristors
        self.memristors = np.empty((rows, cols), dtype=object)
        
        # Handle initial resistance specification
        if r_init is None:
            # Default to high resistance state
            r_init = r_off
        
        if np.isscalar(r_init):
            # Same initial resistance for all memristors
            for i in range(rows):
                for j in range(cols):
                    self.memristors[i, j] = Memristor(
                        r_on=r_on,
                        r_off=r_off,
                        r_init=r_init,
                        model_type=model_type,
                        **kwargs
                    )
        else:
            # Different initial resistance for each memristor
            if r_init.shape != (rows, cols):
                raise ValueError(f"r_init shape {r_init.shape} does not match array shape ({rows}, {cols})")
            
            for i in range(rows):
                for j in range(cols):
                    self.memristors[i, j] = Memristor(
                        r_on=r_on,
                        r_off=r_off,
                        r_init=r_init[i, j],
                        model_type=model_type,
                        **kwargs
                    )
        
        # Wire resistance (for realistic crossbar modeling)
        self.wire_resistance = kwargs.get('wire_resistance', 0.1)  # ohms
        
        # Sneak path current modeling
        self.model_sneak_paths = kwargs.get('model_sneak_paths', False)
    
    def get_resistance_matrix(self) -> np.ndarray:
        """
        Get the resistance values of all memristors in the array.
        
        Returns:
            2D array of resistance values in ohms
        """
        resistance_matrix = np.zeros((self.rows, self.cols))
        
        for i in range(self.rows):
            for j in range(self.cols):
                resistance_matrix[i, j] = self.memristors[i, j].r
        
        return resistance_matrix
    
    def get_state_matrix(self) -> np.ndarray:
        """
        Get the state values of all memristors in the array.
        
        Returns:
            2D array of state values
        """
        state_matrix = np.zeros((self.rows, self.cols))
        
        for i in range(self.rows):
            for j in range(self.cols):
                state_matrix[i, j] = self.memristors[i, j].state
        
        return state_matrix
    
    def set_state_matrix(self, state_matrix: np.ndarray) -> None:
        """
        Set the states of all memristors in the array.
        
        Args:
            state_matrix: 2D array of state values
        """
        if state_matrix.shape != (self.rows, self.cols):
            raise ValueError(f"State matrix shape {state_matrix.shape} does not match array shape ({self.rows}, {self.cols})")
        
        for i in range(self.rows):
            for j in range(self.cols):
                self.memristors[i, j].set_state(state_matrix[i, j])
    
    def apply_voltage_matrix(self, voltage_matrix: np.ndarray, dt: float) -> np.ndarray:
        """
        Apply voltages to individual memristors and get currents.
        
        Args:
            voltage_matrix: 2D array of voltages to apply
            dt: Time step in seconds
            
        Returns:
            2D array of currents through each memristor
        """
        if voltage_matrix.shape != (self.rows, self.cols):
            raise ValueError(f"Voltage matrix shape {voltage_matrix.shape} does not match array shape ({self.rows}, {self.cols})")
        
        current_matrix = np.zeros((self.rows, self.cols))
        
        for i in range(self.rows):
            for j in range(self.cols):
                current_matrix[i, j] = self.memristors[i, j].apply_voltage(voltage_matrix[i, j], dt)
        
        return current_matrix
    
    def apply_crossbar_voltages(self, row_voltages: np.ndarray, col_voltages: np.ndarray, dt: float) -> np.ndarray:
        """
        Apply voltages to rows and columns of the crossbar and compute output currents.
        
        This models a more realistic crossbar array operation where voltages
        are applied to rows and columns, and the effective voltage across each 
        memristor is the difference between its row and column voltages.
        
        Args:
            row_voltages: 1D array of voltages applied to each row
            col_voltages: 1D array of voltages applied to each column
            dt: Time step in seconds
            
        Returns:
            1D array of output currents measured at each column
        """
        if len(row_voltages) != self.rows:
            raise ValueError(f"Row voltages length {len(row_voltages)} does not match rows {self.rows}")
        
        if len(col_voltages) != self.cols:
            raise ValueError(f"Column voltages length {len(col_voltages)} does not match cols {self.cols}")
        
        # Compute voltage across each memristor
        voltage_matrix = np.zeros((self.rows, self.cols))
        for i in range(self.rows):
            for j in range(self.cols):
                voltage_matrix[i, j] = row_voltages[i] - col_voltages[j]
        
        # Simple model without sneak paths or wire resistance
        if not self.model_sneak_paths and self.wire_resistance == 0:
            # Apply voltages to each memristor
            current_matrix = self.apply_voltage_matrix(voltage_matrix, dt)
            
            # Sum currents for each column
            col_currents = np.sum(current_matrix, axis=0)
            
            return col_currents
        
        # Advanced model with sneak paths and wire resistance
        else:
            # This requires solving a system of linear equations
            # Here we'll use a simplified approach that includes wire resistance
            
            # Create conductance matrix (1/R)
            g_matrix = np.zeros((self.rows, self.cols))
            for i in range(self.rows):
                for j in range(self.cols):
                    g_matrix[i, j] = 1.0 / self.memristors[i, j].r
            
            # Wire conductance (1/wire_resistance)
            g_wire = 1.0 / max(self.wire_resistance, 1e-10)
            
            # Apply voltages and compute individual currents with wire resistance
            current_matrix = np.zeros((self.rows, self.cols))
            
            # Iterative solver for the network (simplified)
            # In a real implementation, use a proper circuit simulator
            for i in range(self.rows):
                for j in range(self.cols):
                    effective_voltage = voltage_matrix[i, j]
                    # Adjust for wire resistance (simplified)
                    effective_voltage -= current_matrix.sum() * self.wire_resistance
                    
                    # Apply voltage to memristor
                    current = self.memristors[i, j].apply_voltage(effective_voltage, dt)
                    current_matrix[i, j] = current
            
            # Sum currents for each column
            col_currents = np.sum(current_matrix, axis=0)
            
            return col_currents
    
    def vector_matrix_multiply(self, input_vector: np.ndarray) -> np.ndarray:
        """
        Perform vector-matrix multiplication using the memristor array.
        
        This is a key operation for neural network inference in crossbar arrays.
        
        Args:
            input_vector: Input vector (should have length equal to rows)
            
        Returns:
            Result vector (length equal to cols)
        """
        if len(input_vector) != self.rows:
            raise ValueError(f"Input vector length {len(input_vector)} does not match rows {self.rows}")
        
        # Convert input to voltages
        row_voltages = input_vector.copy()
        col_voltages = np.zeros(self.cols)
        
        # Apply voltages and get output currents
        dt = 1e-6  # Small time step to minimize state changes
        output_currents = self.apply_crossbar_voltages(row_voltages, col_voltages, dt)
        
        return output_currents
    
    def program_array(
        self,
        target_matrix: np.ndarray,
        max_iterations: int = 100,
        tolerance: float = 0.01,
        learning_rate: float = 0.1
    ) -> bool:
        """
        Program the memristor array to match target conductance values.
        
        Args:
            target_matrix: Target conductance values (1/R)
            max_iterations: Maximum programming iterations
            tolerance: Error tolerance for convergence
            learning_rate: Learning rate for programming
            
        Returns:
            True if programming was successful, False otherwise
        """
        if target_matrix.shape != (self.rows, self.cols):
            raise ValueError(f"Target matrix shape {target_matrix.shape} does not match array shape ({self.rows}, {self.cols})")
        
        # Convert target to conductance if it's not already
        if np.any(target_matrix > 1.0):
            warnings.warn("Target values > 1.0 detected. Converting from resistance to conductance (1/R).")
            target_conductance = 1.0 / target_matrix
        else:
            target_conductance = target_matrix.copy()
        
        # Normalize target to [0, 1] range for state mapping
        target_min = np.min(target_conductance)
        target_max = np.max(target_conductance)
        target_normalized = (target_conductance - target_min) / (target_max - target_min)
        
        # Initial state matrix
        current_state = self.get_state_matrix()
        
        # Programming loop
        for iteration in range(max_iterations):
            # Current conductance matrix (1/R)
            current_conductance = np.zeros((self.rows, self.cols))
            for i in range(self.rows):
                for j in range(self.cols):
                    current_conductance[i, j] = 1.0 / self.memristors[i, j].r
            
            # Normalize current conductance
            current_normalized = (current_conductance - target_min) / (target_max - target_min)
            
            # Compute error
            error = np.abs(target_normalized - current_normalized)
            max_error = np.max(error)
            
            logger.debug(f"Iteration {iteration}, Max Error: {max_error:.4f}")
            
            if max_error < tolerance:
                logger.info(f"Programming completed in {iteration} iterations with error {max_error:.4f}")
                return True
            
            # Adjust states based on error
            for i in range(self.rows):
                for j in range(self.cols):
                    if error[i, j] > tolerance:
                        # Direction of adjustment
                        if current_normalized[i, j] < target_normalized[i, j]:
                            # Increase conductance (decrease resistance)
                            new_state = current_state[i, j] + learning_rate * error[i, j]
                        else:
                            # Decrease conductance (increase resistance)
                            new_state = current_state[i, j] - learning_rate * error[i, j]
                        
                        # Set new state
                        self.memristors[i, j].set_state(new_state)
                        current_state[i, j] = new_state
        
        logger.warning(f"Programming failed to converge after {max_iterations} iterations with error {max_error:.4f}")
        return False
    
    def reset(self) -> None:
        """Reset all memristors in the array."""
        for i in range(self.rows):
            for j in range(self.cols):
                self.memristors[i, j].reset()


class CrossbarArray:
    """
    Crossbar array implementation optimized for vector-matrix multiplication.
    
    This class provides a more efficient implementation specifically for
    neural network operations, with features for handling non-idealities.
    """
    
    def __init__(
        self,
        rows: int,
        cols: int,
        r_on: float = 100.0,
        r_off: float = 10000.0,
        g_on: Optional[float] = None,
        g_off: Optional[float] = None,
        conductance_matrix: Optional[np.ndarray] = None,
        wire_resistance: float = 0.1,
        device_variations: float = 0.0,
        sneak_path_model: bool = False
    ):
        """
        Initialize crossbar array.
        
        Args:
            rows: Number of rows in the array
            cols: Number of columns in the array
            r_on: Resistance in low resistance state (ON) in ohms
            r_off: Resistance in high resistance state (OFF) in ohms
            g_on: Conductance in low resistance state (1/r_on) in siemens
            g_off: Conductance in high resistance state (1/r_off) in siemens
            conductance_matrix: Initial conductance matrix
            wire_resistance: Resistance of interconnect wires
            device_variations: Standard deviation of device-to-device variations
            sneak_path_model: Whether to model sneak paths
        """
        self.rows = rows
        self.cols = cols
        
        # Convert between resistance and conductance
        if g_on is not None:
            self.g_on = g_on
            self.r_on = 1.0 / g_on
        else:
            self.r_on = r_on
            self.g_on = 1.0 / r_on
        
        if g_off is not None:
            self.g_off = g_off
            self.r_off = 1.0 / g_off
        else:
            self.r_off = r_off
            self.g_off = 1.0 / r_off
        
        # Initialize conductance matrix
        if conductance_matrix is not None:
            if conductance_matrix.shape != (rows, cols):
                raise ValueError(f"Conductance matrix shape {conductance_matrix.shape} does not match array shape ({rows}, {cols})")
            self.conductance = conductance_matrix.copy()
        else:
            self.conductance = np.ones((rows, cols)) * self.g_off
        
        # Apply device variations if specified
        if device_variations > 0:
            variation = np.random.normal(1.0, device_variations, size=(rows, cols))
            self.conductance *= variation
        
        self.wire_resistance = wire_resistance
        self.sneak_path_model = sneak_path_model
        
        # ADC/DAC resolution for digital conversion
        self.adc_bits = 8
        self.dac_bits = 8
        
        # History for tracking
        self.history = {
            'operations': 0,
            'reads': 0,
            'writes': 0,
            'energy': 0.0
        }
    
    def vector_matrix_multiply(
        self,
        input_vector: np.ndarray,
        quantize_input: bool = False,
        quantize_output: bool = False
    ) -> np.ndarray:
        """
        Perform vector-matrix multiplication.
        
        Args:
            input_vector: Input vector
            quantize_input: Whether to quantize input (simulate DAC)
            quantize_output: Whether to quantize output (simulate ADC)
            
        Returns:
            Output vector
        """
        if len(input_vector) != self.rows:
            raise ValueError(f"Input vector length {len(input_vector)} does not match rows {self.rows}")
        
        # Quantize input if requested
        if quantize_input:
            input_vector = self._quantize(input_vector, self.dac_bits)
        
        # Simple matrix multiplication
        output = np.zeros(self.cols)
        
        if not self.sneak_path_model and self.wire_resistance == 0:
            # Ideal crossbar operation
            output = np.dot(input_vector, self.conductance)
        else:
            # Non-ideal model with wire resistance
            # This is a simplified model
            
            # Solve for node voltages (simplified)
            # In a full simulation, we would solve the full circuit equations
            
            # For each column, compute the effective conductance and current
            for j in range(self.cols):
                col_conductance = self.conductance[:, j]
                
                # Apply input voltages and sum resulting currents
                col_current = np.sum(input_vector * col_conductance)
                
                # Include IR drop on wires (simplified)
                if self.wire_resistance > 0:
                    # Approximate the average wire distance
                    avg_distance = (self.rows + 1) / 2
                    ir_drop = col_current * self.wire_resistance * avg_distance
                    # Adjust current for IR drop
                    col_current *= (1 - min(0.9, ir_drop / np.mean(input_vector)))
                
                output[j] = col_current
        
        # Quantize output if requested
        if quantize_output:
            output = self._quantize(output, self.adc_bits)
        
        # Update stats
        self.history['operations'] += 1
        self.history['reads'] += self.rows * self.cols
        
        # Estimate energy (simplified model)
        # E = V²/R * t for each cell
        avg_voltage = np.mean(np.abs(input_vector))
        avg_conductance = np.mean(self.conductance)
        avg_resistance = 1.0 / max(avg_conductance, 1e-10)
        energy = (avg_voltage**2 / avg_resistance) * 1e-9 * self.rows * self.cols
        self.history['energy'] += energy
        
        return output
    
    def _quantize(self, values: np.ndarray, bits: int) -> np.ndarray:
        """
        Quantize values to specified bit depth.
        
        Args:
            values: Values to quantize
            bits: Bit depth
            
        Returns:
            Quantized values
        """
        # Determine value range
        value_min = np.min(values)
        value_max = np.max(values)
        
        if value_min == value_max:
            return values.copy()
        
        # Quantize
        levels = 2**bits
        step = (value_max - value_min) / (levels - 1)
        quantized = np.round((values - value_min) / step) * step + value_min
        
        return quantized
    
    def program_conductance(
        self,
        target_conductance: np.ndarray,
        max_iterations: int = 10,
        tolerance: float = 0.01
    ) -> bool:
        """
        Program the array to match target conductance values.
        
        Args:
            target_conductance: Target conductance matrix
            max_iterations: Maximum programming iterations
            tolerance: Error tolerance
            
        Returns:
            True if programming was successful, False otherwise
        """
        if target_conductance.shape != (self.rows, self.cols):
            raise ValueError(f"Target matrix shape {target_conductance.shape} does not match array shape ({self.rows}, {self.cols})")
        
        # Ensure target conductance is within device limits
        target_conductance = np.clip(target_conductance, self.g_off, self.g_on)
        
        # Simple programming model (iterative adjustment)
        for iteration in range(max_iterations):
            # Compute error
            error = np.abs(self.conductance - target_conductance)
            max_error = np.max(error)
            
            logger.debug(f"Programming iteration {iteration}, Max Error: {max_error:.4e}")
            
            if max_error < tolerance:
                logger.info(f"Programming completed in {iteration} iterations with error {max_error:.4e}")
                return True
            
            # Update conductance (with potential device variations)
            adjustment = 0.5 * (target_conductance - self.conductance)
            self.conductance += adjustment
            
            # Ensure conductance stays within device limits
            self.conductance = np.clip(self.conductance, self.g_off, self.g_on)
            
            # Update stats
            self.history['writes'] += self.rows * self.cols
        
        logger.warning(f"Programming failed to converge after {max_iterations} iterations with error {max_error:.4e}")
        return False
    
    def map_weights_to_conductance(
        self,
        weights: np.ndarray,
        weight_max: float = 1.0,
        weight_min: float = -1.0,
        differential_pairs: bool = True
    ) -> Union[np.ndarray, Tuple[np.ndarray, np.ndarray]]:
        """
        Map neural network weights to conductance values.
        
        Args:
            weights: Weight matrix
            weight_max: Maximum weight value
            weight_min: Minimum weight value
            differential_pairs: Whether to use differential pairs for negative weights
            
        Returns:
            Conductance matrix (or pair of matrices if using differential pairs)
        """
        if weights.shape != (self.rows, self.cols) and not differential_pairs:
            raise ValueError(f"Weight matrix shape {weights.shape} does not match array shape ({self.rows}, {self.cols})")
        
        if differential_pairs and weights.shape != (self.rows, self.cols // 2):
            if self.cols % 2 != 0:
                raise ValueError("Number of columns must be even for differential pairs")
            raise ValueError(f"Weight matrix shape {weights.shape} does not match required shape ({self.rows}, {self.cols // 2})")
        
        # Normalize weights to [0, 1] range
        normalized_weights = (weights - weight_min) / (weight_max - weight_min)
        
        # Map to conductance range [g_off, g_on]
        g_range = self.g_on - self.g_off
        
        if not differential_pairs:
            # Direct mapping
            conductance = self.g_off + normalized_weights * g_range
            return conductance
        else:
            # Differential pairs for positive and negative weights
            pos_weights = np.maximum(0, weights)
            neg_weights = np.maximum(0, -weights)
            
            # Normalize to [0, 1]
            pos_normalized = pos_weights / weight_max
            neg_normalized = neg_weights / abs(weight_min)
            
            # Map to conductance
            g_pos = self.g_off + pos_normalized * g_range
            g_neg = self.g_off + neg_normalized * g_range
            
            return g_pos, g_neg
    
    def plot_conductance_matrix(self, title: str = "Conductance Matrix"):
        """
        Plot the conductance matrix as a heatmap.
        
        Args:
            title: Plot title
        """
        plt.figure(figsize=(10, 8))
        im = plt.imshow(self.conductance, cmap='viridis')
        plt.colorbar(im, label='Conductance (S)')
        plt.title(title)
        plt.xlabel('Column')
        plt.ylabel('Row')
        plt.tight_layout()
        plt.show()


class VectorMatrixMultiplier:
    """
    Optimized implementation of vector-matrix multiplier for neural networks.
    
    This class provides an efficient implementation of vector-matrix multiplication
    using memristor crossbars, with support for multi-bit precision, differential
    pairs, and various optimization techniques.
    """
    
    def __init__(
        self,
        rows: int,
        cols: int,
        precision_bits: int = 8,
        use_differential_pairs: bool = True,
        r_on: float = 100.0,
        r_off: float = 10000.0,
        wire_resistance: float = 0.1,
        adc_resolution: int = 8,
        dac_resolution: int = 8
    ):
        """
        Initialize vector-matrix multiplier.
        
        Args:
            rows: Number of input rows
            cols: Number of output columns
            precision_bits: Precision bits for weights
            use_differential_pairs: Whether to use differential pairs for signed weights
            r_on: Resistance in low resistance state (ON) in ohms
            r_off: Resistance in high resistance state (OFF) in ohms
            wire_resistance: Resistance of interconnect wires
            adc_resolution: ADC resolution in bits
            dac_resolution: DAC resolution in bits
        """
        self.rows = rows
        self.cols = cols
        self.precision_bits = precision_bits
        self.use_differential_pairs = use_differential_pairs
        
        # Actual array size depends on configuration
        array_cols = cols * (2 if use_differential_pairs else 1)
        
        # Create crossbar array
        self.crossbar = CrossbarArray(
            rows=rows,
            cols=array_cols,
            r_on=r_on,
            r_off=r_off,
            wire_resistance=wire_resistance
        )
        
        self.adc_resolution = adc_resolution
        self.dac_resolution = dac_resolution
        
        # Weight range
        self.weight_max = 1.0
        self.weight_min = -1.0 if use_differential_pairs else 0.0
        
        # Statistics
        self.total_operations = 0
        self.total_energy = 0.0
    
    def program_weights(self, weights: np.ndarray) -> bool:
        """
        Program the crossbar with neural network weights.
        
        Args:
            weights: Weight matrix
            
        Returns:
            True if programming was successful, False otherwise
        """
        expected_shape = (self.rows, self.cols)
        if weights.shape != expected_shape:
            raise ValueError(f"Weight matrix shape {weights.shape} does not match expected shape {expected_shape}")
        
        # Map weights to conductance
        if not self.use_differential_pairs:
            # Direct mapping
            conductance = self.crossbar.map_weights_to_conductance(
                weights=weights,
                weight_max=self.weight_max,
                weight_min=self.weight_min,
                differential_pairs=False
            )
            
            return self.crossbar.program_conductance(conductance)
        else:
            # Differential pairs
            g_pos, g_neg = self.crossbar.map_weights_to_conductance(
                weights=weights,
                weight_max=self.weight_max,
                weight_min=self.weight_min,
                differential_pairs=True
            )
            
            # Combine into single conductance matrix
            combined_conductance = np.zeros((self.rows, self.cols * 2))
            combined_conductance[:, 0::2] = g_pos
            combined_conductance[:, 1::2] = g_neg
            
            return self.crossbar.program_conductance(combined_conductance)
    
    def forward(
        self,
        input_vector: np.ndarray,
        apply_quantization: bool = True,
        apply_activation: Optional[Callable] = None
    ) -> np.ndarray:
        """
        Perform forward pass through the vector-matrix multiplier.
        
        Args:
            input_vector: Input vector
            apply_quantization: Whether to apply ADC/DAC quantization
            apply_activation: Optional activation function to apply
            
        Returns:
            Output vector
        """
        if len(input_vector) != self.rows:
            raise ValueError(f"Input vector length {len(input_vector)} does not match rows {self.rows}")
        
        # Perform vector-matrix multiplication
        output = self.crossbar.vector_matrix_multiply(
            input_vector=input_vector,
            quantize_input=apply_quantization,
            quantize_output=apply_quantization
        )
        
        # Process output based on configuration
        if self.use_differential_pairs:
            # Combine differential pairs
            result = np.zeros(self.cols)
            for i in range(self.cols):
                result[i] = output[i*2] - output[i*2+1]
        else:
            result = output
        
        # Apply activation function if provided
        if apply_activation is not None:
            result = apply_activation(result)
        
        # Update statistics
        self.total_operations += 1
        self.total_energy += self.crossbar.history['energy']
        
        return result
    
    def get_efficiency_metrics(self) -> Dict[str, float]:
        """
        Get efficiency metrics for the vector-matrix multiplier.
        
        Returns:
            Dictionary of efficiency metrics
        """
        return {
            'operations': self.total_operations,
            'reads': self.crossbar.history['reads'],
            'writes': self.crossbar.history['writes'],
            'energy': self.total_energy,
            'energy_per_op': self.total_energy / max(1, self.total_operations)
        }


class MemristiveNeuron:
    """
    Neuron implementation using memristive devices.
    
    This class implements a spiking neuron model using memristive elements
    for synaptic weights and threshold behavior.
    """
    
    def __init__(
        self,
        neuron_id: int,
        threshold: float = 1.0,
        leak_rate: float = 0.1,
        r_threshold: float = 1000.0,
        r_on: float = 100.0,
        r_off: float = 10000.0
    ):
        """
        Initialize memristive neuron.
        
        Args:
            neuron_id: Unique identifier for this neuron
            threshold: Firing threshold
            leak_rate: Membrane potential leak rate
            r_threshold: Threshold memristor resistance
            r_on: Memristor low resistance state
            r_off: Memristor high resistance state
        """
        self.neuron_id = neuron_id
        self.threshold = threshold
        self.leak_rate = leak_rate
        
        # Membrane potential memristor
        self.membrane_memristor = Memristor(
            r_on=r_on,
            r_off=r_off,
            r_init=r_off,
            model_type=MemristorType.NONLINEAR,
            threshold_voltage=0.1
        )
        
        # Threshold memristor
        self.threshold_memristor = Memristor(
            r_on=r_on,
            r_off=r_off,
            r_init=r_threshold,
            model_type=MemristorType.NONLINEAR,
            threshold_voltage=0.2
        )
        
        # State variables
        self.v = 0.0  # Membrane potential
        self.last_spike_time = -float('inf')
        self.refractory_until = -float('inf')
        
        # Refractory period duration
        self.refractory_period = 2.0  # ms
        
        # Synaptic inputs
        self.synaptic_inputs = []  # (pre_id, weight, delay)
        
        # Spike recording
        self.spike_times = []
    
    def add_synapse(self, pre_id: int, weight: float, delay: float = 1.0) -> None:
        """
        Add a synaptic connection to this neuron.
        
        Args:
            pre_id: ID of presynaptic neuron
            weight: Synaptic weight
            delay: Synaptic delay in ms
        """
        self.synaptic_inputs.append((pre_id, weight, delay))
    
    def receive_spike(self, pre_id: int, t: float) -> None:
        """
        Receive a spike from a presynaptic neuron.
        
        Args:
            pre_id: ID of presynaptic neuron
            t: Current time in ms
        """
        # Find matching synaptic input
        for synapse_pre_id, weight, delay in self.synaptic_inputs:
            if synapse_pre_id == pre_id:
                # Schedule spike delivery
                delivery_time = t + delay
                self._process_synaptic_input(weight, delivery_time)
                break
    
    def _process_synaptic_input(self, weight: float, t: float) -> None:
        """
        Process a synaptic input at the specified time.
        
        Args:
            weight: Synaptic weight
            t: Spike delivery time in ms
        """
        # Convert weight to voltage
        voltage = weight * 0.1  # Scale weight to voltage range
        
        # Apply voltage to membrane memristor
        dt = 1e-3  # 1 microsecond time step
        self.membrane_memristor.apply_voltage(voltage, dt)
        
        # Update membrane potential based on memristor state
        self.v += weight * (1.0 - self.membrane_memristor.state)
    
    def update(self, t: float, dt: float) -> bool:
        """
        Update neuron state and check for spike generation.
        
        Args:
            t: Current time in ms
            dt: Time step in ms
            
        Returns:
            True if neuron spiked, False otherwise
        """
        # Check if in refractory period
        if t < self.refractory_until:
            return False
        
        # Apply leak
        self.v -= self.leak_rate * self.v * dt
        
        # Update membrane potential memristor state
        v_voltage = self.v * 0.1  # Scale to voltage range
        self.membrane_memristor.apply_voltage(v_voltage, dt)
        
        # Check for spike
        threshold_current = self.threshold / self.threshold_memristor.r
        membrane_current = self.v / self.membrane_memristor.r
        
        if membrane_current > threshold_current:
            # Spike generated
            self.last_spike_time = t
            self.refractory_until = t + self.refractory_period
            self.v = 0.0  # Reset membrane potential
            
            # Apply reset to membrane memristor
            self.membrane_memristor.apply_voltage(-0.5, 1e-3)  # Strong negative pulse
            
            # Record spike
            self.spike_times.append(t)
            
            return True
        
        return False
    
    def reset(self) -> None:
        """Reset neuron state."""
        self.v = 0.0
        self.last_spike_time = -float('inf')
        self.refractory_until = -float('inf')
        self.membrane_memristor.reset()
        self.spike_times = []


class MemristiveWeightUpdate:
    """
    Weight update mechanisms for memristive synapses.
    
    This class implements various learning rules for adjusting
    memristive synaptic weights.
    """
    
    def __init__(
        self,
        learning_rule: str = "stdp",
        learning_rate: float = 0.01,
        a_plus: float = 0.01,
        a_minus: float = 0.01,
        tau_plus: float = 20.0,
        tau_minus: float = 20.0
    ):
        """
        Initialize weight update mechanism.
        
        Args:
            learning_rule: Learning rule type ("stdp", "oja", "hebbian", "anti_hebbian")
            learning_rate: Overall learning rate
            a_plus: STDP potentiation factor
            a_minus: STDP depression factor
            tau_plus: STDP potentiation time constant
            tau_minus: STDP depression time constant
        """
        self.learning_rule = learning_rule.lower()
        self.learning_rate = learning_rate
        
        # STDP parameters
        self.a_plus = a_plus
        self.a_minus = a_minus
        self.tau_plus = tau_plus
        self.tau_minus = tau_minus
        
        # State variables
        self.pre_trace = {}  # pre_id -> trace
        self.post_trace = {}  # post_id -> trace
    
    def update_trace(self, dt: float) -> None:
        """
        Update all synaptic traces (decay over time).
        
        Args:
            dt: Time step in ms
        """
        # Decay pre_trace
        for pre_id in self.pre_trace:
            self.pre_trace[pre_id] *= np.exp(-dt / self.tau_plus)
        
        # Decay post_trace
        for post_id in self.post_trace:
            self.post_trace[post_id] *= np.exp(-dt / self.tau_minus)
    
    def pre_spike(self, pre_id: int, t: float) -> None:
        """
        Process presynaptic spike.
        
        Args:
            pre_id: Presynaptic neuron ID
            t: Spike time in ms
        """
        # Initialize trace if needed
        if pre_id not in self.pre_trace:
            self.pre_trace[pre_id] = 0.0
        
        # Increment trace
        self.pre_trace[pre_id] += 1.0
    
    def post_spike(self, post_id: int, t: float) -> None:
        """
        Process postsynaptic spike.
        
        Args:
            post_id: Postsynaptic neuron ID
            t: Spike time in ms
        """
        # Initialize trace if needed
        if post_id not in self.post_trace:
            self.post_trace[post_id] = 0.0
        
        # Increment trace
        self.post_trace[post_id] += 1.0
    
    def compute_weight_update(self, pre_id: int, post_id: int, current_weight: float) -> float:
        """
        Compute weight update based on learning rule.
        
        Args:
            pre_id: Presynaptic neuron ID
            post_id: Postsynaptic neuron ID
            current_weight: Current weight value
            
        Returns:
            Weight change amount
        """
        # Get traces
        pre_trace_value = self.pre_trace.get(pre_id, 0.0)
        post_trace_value = self.post_trace.get(post_id, 0.0)
        
        if self.learning_rule == "stdp":
            # STDP rule
            # If post spike follows pre spike (pre_trace > 0): potentiation
            # If pre spike follows post spike (post_trace > 0): depression
            dw = self.a_plus * pre_trace_value - self.a_minus * post_trace_value
            
        elif self.learning_rule == "oja":
            # Oja's rule (normalized Hebbian)
            # dw = η * (y * x - y^2 * w)
            # where y is post activity, x is pre activity
            dw = self.learning_rate * (pre_trace_value * post_trace_value - 
                                      post_trace_value**2 * current_weight)
            
        elif self.learning_rule == "hebbian":
            # Basic Hebbian learning
            # dw = η * x * y
            dw = self.learning_rate * pre_trace_value * post_trace_value
            
        elif self.learning_rule == "anti_hebbian":
            # Anti-Hebbian learning
            # dw = -η * x * y
            dw = -self.learning_rate * pre_trace_value * post_trace_value
            
        else:
            # Default to simple STDP
            dw = self.a_plus * pre_trace_value - self.a_minus * post_trace_value
        
        return dw * self.learning_rate
    
    def apply_update_to_memristor(
        self,
        memristor: Memristor,
        weight_change: float,
        use_voltage_pulses: bool = True
    ) -> None:
        """
        Apply weight update to a memristor.
        
        Args:
            memristor: Memristor device to update
            weight_change: Amount of weight change
            use_voltage_pulses: Whether to use voltage pulses or direct state change
        """
        if use_voltage_pulses:
            # Convert weight change to voltage and apply
            voltage = weight_change * 2.0  # Scale to voltage range
            dt = 1e-3  # 1 ms pulse
            memristor.apply_voltage(voltage, dt)
        else:
            # Directly modify memristor state
            new_state = memristor.state + weight_change
            memristor.set_state(new_state)


class MemristorProgrammer:
    """
    Utility for programming memristors to desired states.
    
    This class provides methods for accurately programming memristors
    to target resistance values, handling device variations and non-idealities.
    """
    
    def __init__(
        self,
        max_iterations: int = 20,
        tolerance: float = 0.01,
        pulse_width: float = 1e-3,  # 1 ms
        v_set: float = 0.5,         # SET pulse voltage
        v_reset: float = -0.5,      # RESET pulse voltage
        v_read: float = 0.1         # Read pulse voltage
    ):
        """
        Initialize memristor programmer.
        
        Args:
            max_iterations: Maximum programming iterations
            tolerance: Error tolerance
            pulse_width: Programming pulse width in seconds
            v_set: SET pulse voltage (positive, decreases resistance)
            v_reset: RESET pulse voltage (negative, increases resistance)
            v_read: Read pulse voltage (small, non-perturbing)
        """
        self.max_iterations = max_iterations
        self.tolerance = tolerance
        self.pulse_width = pulse_width
        self.v_set = v_set
        self.v_reset = v_reset
        self.v_read = v_read
        
        # Adaptive parameters
        self.use_adaptive_pulse = True
        self.min_pulse_width = 1e-5  # 10 μs
        self.max_pulse_width = 1e-2  # 10 ms
    
    def read_resistance(self, memristor: Memristor) -> float:
        """
        Read memristor resistance using small read voltage.
        
        Args:
            memristor: Memristor to read
            
        Returns:
            Resistance in ohms
        """
        # Apply small read voltage
        current = memristor.apply_voltage(self.v_read, self.pulse_width / 10)
        
        # Calculate resistance
        resistance = self.v_read / current if current != 0 else float('inf')
        
        return resistance
    
    def program_resistance(
        self,
        memristor: Memristor,
        target_resistance: float,
        verify: bool = True
    ) -> bool:
        """
        Program a memristor to target resistance.
        
        Args:
            memristor: Memristor to program
            target_resistance: Target resistance in ohms
            verify: Whether to verify after programming
            
        Returns:
            True if programming was successful, False otherwise
        """
        # Ensure target is within device limits
        target_resistance = max(memristor.r_on, min(memristor.r_off, target_resistance))
        
        # Iterative programming
        for iteration in range(self.max_iterations):
            # Read current resistance
            current_resistance = self.read_resistance(memristor)
            
            # Compute error
            relative_error = abs(current_resistance - target_resistance) / target_resistance
            
            logger.debug(f"Programming iteration {iteration}, "
                       f"Current: {current_resistance:.1f}Ω, "
                       f"Target: {target_resistance:.1f}Ω, "
                       f"Error: {relative_error:.3f}")
            
            if relative_error < self.tolerance:
                logger.debug(f"Programming successful after {iteration} iterations")
                return True
            
            # Determine programming direction
            if current_resistance > target_resistance:
                # Need to decrease resistance (SET operation)
                pulse_voltage = self.v_set
            else:
                # Need to increase resistance (RESET operation)
                pulse_voltage = self.v_reset
            
            # Adaptive pulse width based on error
            if self.use_adaptive_pulse:
                # Scale pulse width by error magnitude
                adaptive_width = self.pulse_width * min(1.0, relative_error * 5)
                pulse_width = max(self.min_pulse_width, min(self.max_pulse_width, adaptive_width))
            else:
                pulse_width = self.pulse_width
            
            # Apply programming pulse
            memristor.apply_voltage(pulse_voltage, pulse_width)
        
        # Verify final result if requested
        if verify:
            final_resistance = self.read_resistance(memristor)
            final_error = abs(final_resistance - target_resistance) / target_resistance
            
            logger.warning(f"Programming failed to converge. "
                         f"Final resistance: {final_resistance:.1f}Ω, "
                         f"Target: {target_resistance:.1f}Ω, "
                         f"Error: {final_error:.3f}")
            
            return final_error < self.tolerance
        
        return False
    
    def program_conductance(
        self,
        memristor: Memristor,
        target_conductance: float,
        verify: bool = True
    ) -> bool:
        """
        Program a memristor to target conductance.
        
        Args:
            memristor: Memristor to program
            target_conductance: Target conductance in siemens
            verify: Whether to verify after programming
            
        Returns:
            True if programming was successful, False otherwise
        """
        # Convert conductance to resistance
        if target_conductance <= 0:
            target_resistance = memristor.r_off
        else:
            target_resistance = 1.0 / target_conductance
        
        return self.program_resistance(memristor, target_resistance, verify)
    
    def program_array(
        self,
        array: MemristorArray,
        target_resistance: np.ndarray,
        verify: bool = True
    ) -> float:
        """
        Program a memristor array to target resistance values.
        
        Args:
            array: Memristor array to program
            target_resistance: Target resistance matrix
            verify: Whether to verify after programming
            
        Returns:
            Maximum relative error
        """
        if target_resistance.shape != (array.rows, array.cols):
            raise ValueError(f"Target matrix shape {target_resistance.shape} does not match array shape ({array.rows}, {array.cols})")
        
        # Program each memristor
        success_count = 0
        for i in range(array.rows):
            for j in range(array.cols):
                success = self.program_resistance(
                    memristor=array.memristors[i, j],
                    target_resistance=target_resistance[i, j],
                    verify=verify
                )
                
                if success:
                    success_count += 1
        
        # Calculate success rate
        success_rate = success_count / (array.rows * array.cols)
        
        # Verify final result
        if verify:
            resistance_matrix = array.get_resistance_matrix()
            relative_error = np.abs(resistance_matrix - target_resistance) / target_resistance
            max_error = np.max(relative_error)
            
            logger.info(f"Array programming complete. "
                      f"Success rate: {success_rate:.2f}, "
                      f"Max error: {max_error:.3f}")
            
            return max_error
        
        return 0.0


class MemristorReadout:
    """
    Utility for reading and measuring memristor values.
    
    This class provides methods for reading memristor values with
    consideration for measurement noise and read disturbance.
    """
    
    def __init__(
        self,
        v_read: float = 0.1,          # Read voltage in volts
        pulse_width: float = 1e-4,    # Read pulse width in seconds
        read_noise_std: float = 0.02,  # Read noise standard deviation
        adc_bits: int = 10            # ADC resolution for readout
    ):
        """
        Initialize memristor readout.
        
        Args:
            v_read: Read voltage in volts
            pulse_width: Read pulse width in seconds
            read_noise_std: Read noise standard deviation (relative)
            adc_bits: ADC resolution for readout
        """
        self.v_read = v_read
        self.pulse_width = pulse_width
        self.read_noise_std = read_noise_std
        self.adc_bits = adc_bits
        
        # ADC quantization levels
        self.adc_levels = 2**adc_bits
    
    def read_resistance_with_noise(self, memristor: Memristor) -> float:
        """
        Read memristor resistance with realistic noise.
        
        Args:
            memristor: Memristor to read
            
        Returns:
            Measured resistance in ohms
        """
        # Apply read voltage
        current = memristor.apply_voltage(self.v_read, self.pulse_width)
        
        # Add noise to current measurement
        noise_factor = 1.0 + np.random.normal(0, self.read_noise_std)
        noisy_current = current * noise_factor
        
        # Calculate resistance
        resistance = self.v_read / noisy_current if noisy_current != 0 else float('inf')
        
        # Quantize resistance reading (ADC effect)
        resistance = self._quantize_reading(resistance, memristor.r_on, memristor.r_off)
        
        return resistance
    
    def read_array_with_noise(self, array: MemristorArray) -> np.ndarray:
        """
        Read all memristors in an array with realistic noise.
        
        Args:
            array: Memristor array to read
            
        Returns:
            Array of measured resistance values
        """
        resistance_matrix = np.zeros((array.rows, array.cols))
        
        for i in range(array.rows):
            for j in range(array.cols):
                resistance_matrix[i, j] = self.read_resistance_with_noise(array.memristors[i, j])
        
        return resistance_matrix
    
    def _quantize_reading(self, value: float, min_value: float, max_value: float) -> float:
        """
        Quantize a reading to simulate ADC conversion.
        
        Args:
            value: Value to quantize
            min_value: Minimum expected value
            max_value: Maximum expected value
            
        Returns:
            Quantized value
        """
        # Ensure value is within range
        value = max(min_value, min(max_value, value))
        
        # Normalize to [0, 1]
        normalized = (value - min_value) / (max_value - min_value)
        
        # Quantize
        quantized_level = round(normalized * (self.adc_levels - 1))
        
        # Convert back to original range
        quantized_value = min_value + (quantized_level / (self.adc_levels - 1)) * (max_value - min_value)
        
        return quantized_value
    
    def measure_resistance_statistics(
        self,
        memristor: Memristor,
        num_measurements: int = 100
    ) -> Dict[str, float]:
        """
        Measure resistance multiple times and compute statistics.
        
        Args:
            memristor: Memristor to measure
            num_measurements: Number of measurements
            
        Returns:
            Dictionary of statistics
        """
        measurements = []
        
        for _ in range(num_measurements):
            r = self.read_resistance_with_noise(memristor)
            measurements.append(r)
        
        # Compute statistics
        mean_resistance = np.mean(measurements)
        std_resistance = np.std(measurements)
        min_resistance = np.min(measurements)
        max_resistance = np.max(measurements)
        
        return {
            'mean': mean_resistance,
            'std': std_resistance,
            'min': min_resistance,
            'max': max_resistance,
            'coefficient_of_variation': std_resistance / mean_resistance
        }


class NonIdealityCompensation:
    """
    Compensation techniques for memristor array non-idealities.
    
    This class implements various techniques to compensate for
    non-idealities in memristor crossbar arrays, such as sneak paths,
    IR drop, device variations, and read/write non-idealities.
    """
    
    def __init__(
        self,
        compensation_methods: List[str] = None,
        device_variation_std: float = 0.1
    ):
        """
        Initialize non-ideality compensation.
        
        Args:
            compensation_methods: List of compensation methods to use
            device_variation_std: Standard deviation of device variations
        """
        if compensation_methods is None:
            compensation_methods = [
                'feedback',
                'calibration',
                'ir_drop'
            ]
        
        self.compensation_methods = [m.lower() for m in compensation_methods]
        self.device_variation_std = device_variation_std
        
        # Calibration data
        self.calibration_matrix = None
        self.calibrated = False
        
        # IR drop compensation
        self.ir_drop_model = None
        
        # Feedback control parameters
        self.feedback_gain = 0.5
        self.feedback_history = []
    
    def calibrate_array(
        self,
        array: Union[MemristorArray, CrossbarArray],
        target_values: np.ndarray,
        actual_values: np.ndarray
    ) -> None:
        """
        Calibrate array based on target and actual values.
        
        Args:
            array: Memristor array or crossbar
            target_values: Target values (weights or conductances)
            actual_values: Measured actual values
        """
        if target_values.shape != actual_values.shape:
            raise ValueError(f"Target shape {target_values.shape} does not match actual shape {actual_values.shape}")
        
        # Calculate calibration factors
        with np.errstate(divide='ignore', invalid='ignore'):
            calibration_factors = target_values / actual_values
        
        # Handle divisions by zero or infinity
        calibration_factors = np.nan_to_num(calibration_factors, nan=1.0, posinf=1.0, neginf=1.0)
        
        # Clip to reasonable range
        calibration_factors = np.clip(calibration_factors, 0.5, 2.0)
        
        self.calibration_matrix = calibration_factors
        self.calibrated = True
        
        logger.info(f"Calibration complete. Factors min: {np.min(calibration_factors):.3f}, "
                  f"max: {np.max(calibration_factors):.3f}, "
                  f"mean: {np.mean(calibration_factors):.3f}")
    
    def train_ir_drop_model(
        self,
        array: Union[MemristorArray, CrossbarArray],
        wire_resistance: float,
        input_patterns: np.ndarray,
        expected_outputs: np.ndarray,
        measured_outputs: np.ndarray
    ) -> None:
        """
        Train IR drop compensation model.
        
        Args:
            array: Memristor array or crossbar
            wire_resistance: Wire resistance in ohms
            input_patterns: Input voltage patterns
            expected_outputs: Expected output currents
            measured_outputs: Measured output currents
        """
        # Simple linear IR drop model
        # In more advanced implementations, this could use machine learning models
        
        rows, cols = array.rows, array.cols
        
        # Calculate average IR drop per row/column
        ir_drop_factors = np.zeros((rows, cols))
        
        for i, (inputs, expected, measured) in enumerate(zip(input_patterns, expected_outputs, measured_outputs)):
            with np.errstate(divide='ignore', invalid='ignore'):
                output_ratio = expected / measured
            
            output_ratio = np.nan_to_num(output_ratio, nan=1.0, posinf=1.0, neginf=1.0)
            output_ratio = np.clip(output_ratio, 0.5, 2.0)
            
            # Estimate IR drop based on row position
            for r in range(rows):
                row_factor = 1.0 + wire_resistance * r / rows
                for c in range(cols):
                    ir_drop_factors[r, c] += row_factor * inputs[r]
        
        # Normalize
        ir_drop_factors /= len(input_patterns)
        
        self.ir_drop_model = ir_drop_factors
        
        logger.info(f"IR drop model trained. Factors min: {np.min(ir_drop_factors):.3f}, "
                  f"max: {np.max(ir_drop_factors):.3f}, "
                  f"mean: {np.mean(ir_drop_factors):.3f}")
    
    def compensate_weights(
        self,
        weights: np.ndarray,
        array_shape: Tuple[int, int]
    ) -> np.ndarray:
        """
        Compensate weights for programming into array.
        
        Args:
            weights: Target weight matrix
            array_shape: Shape of the array (rows, cols)
            
        Returns:
            Compensated weight matrix
        """
        if weights.shape != array_shape:
            raise ValueError(f"Weight shape {weights.shape} does not match array shape {array_shape}")
        
        compensated_weights = weights.copy()
        
        # Apply calibration if available
        if 'calibration' in self.compensation_methods and self.calibrated:
            compensated_weights *= self.calibration_matrix
        
        # Apply IR drop compensation if model is available
        if 'ir_drop' in self.compensation_methods and self.ir_drop_model is not None:
            compensated_weights *= self.ir_drop_model
        
        # Device variation compensation
        if 'variation' in self.compensation_methods:
            # Estimate variation and compensate
            variation_matrix = np.random.normal(1.0, self.device_variation_std, size=array_shape)
            compensated_weights /= variation_matrix
        
        return compensated_weights
    
    def compensate_programming(
        self,
        programmer: MemristorProgrammer,
        array: MemristorArray,
        target_values: np.ndarray
    ) -> np.ndarray:
        """
        Compensate during programming with feedback.
        
        Args:
            programmer: Memristor programmer
            array: Memristor array
            target_values: Target values
            
        Returns:
            Achieved values after compensation
        """
        # Apply pre-compensation
        compensated_values = self.compensate_weights(target_values, (array.rows, array.cols))
        
        # Program array
        if 'feedback' in self.compensation_methods:
            # Iterative programming with feedback
            max_iterations = 3
            readout = MemristorReadout()
            
            for iteration in range(max_iterations):
                # Program array with current compensated values
                programmer.program_array(array, compensated_values, verify=True)
                
                # Read achieved values
                achieved_values = readout.read_array_with_noise(array)
                
                # Compute error
                error = target_values - achieved_values
                relative_error = np.abs(error / target_values)
                max_error = np.max(relative_error)
                
                logger.debug(f"Compensation iteration {iteration}, Max error: {max_error:.3f}")
                
                if max_error < 0.05:  # 5% tolerance
                    break
                
                # Update compensation based on error
                compensated_values += self.feedback_gain * error
            
            return achieved_values
        else:
            # Single-pass programming
            programmer.program_array(array, compensated_values, verify=True)
            
            # Read achieved values
            readout = MemristorReadout()
            achieved_values = readout.read_array_with_noise(array)
            
            return achieved_values


class MemristorArraySimulator:
    """
    Simulator for memristor array operation.
    
    This class provides methods for simulating the operation of
    memristor arrays, including device-level behaviors and circuit effects.
    """
    
    def __init__(
        self,
        simulation_params: Dict[str, Any] = None
    ):
        """
        Initialize memristor array simulator.
        
        Args:
            simulation_params: Simulation parameters
        """
        # Default simulation parameters
        default_params = {
            'wire_resistance': 0.1,       # Ohms
            'contact_resistance': 0.5,     # Ohms
            'sneak_paths': True,           # Model sneak paths
            'device_variations': 0.1,      # Device-to-device variation std
            'thermal_effects': False,      # Model thermal effects
            'read_noise': 0.02,            # Read noise std
            'write_variations': 0.05,      # Write operation variation std
            'simulation_dt': 1e-6          # Simulation time step (seconds)
        }
        
        if simulation_params is None:
            simulation_params = {}
        
        # Update with provided parameters
        self.params = {**default_params, **simulation_params}
        
        # Temperature modeling
        self.base_temperature = 300.0  # Kelvin
        self.temperature_map = None
        
        # Statistics tracking
        self.stats = {
            'read_operations': 0,
            'write_operations': 0,
            'energy_consumed': 0.0
        }
    
    def simulate_crossbar_operation(
        self,
        array: MemristorArray,
        input_voltages: np.ndarray,
        simulation_time: float = 1e-3,
        record_states: bool = False
    ) -> Tuple[np.ndarray, Optional[Dict[str, np.ndarray]]]:
        """
        Simulate crossbar operation with input voltages.
        
        Args:
            array: Memristor array
            input_voltages: Input voltages to apply to rows
            simulation_time: Total simulation time in seconds
            record_states: Whether to record state evolution
            
        Returns:
            Output currents and optionally state recordings
        """
        rows, cols = array.rows, array.cols
        
        if len(input_voltages) != rows:
            raise ValueError(f"Input voltage length {len(input_voltages)} does not match rows {rows}")
        
        # Column voltages (initially all grounded)
        col_voltages = np.zeros(cols)
        
        # Define simulation time steps
        dt = self.params['simulation_dt']
        steps = int(simulation_time / dt)
        
        # Initialize state recording if requested
        recordings = None
        if record_states:
            recordings = {
                'time': np.linspace(0, simulation_time, steps),
                'col_currents': np.zeros((steps, cols)),
                'memristor_states': np.zeros((steps, rows, cols))
            }
        
        # Thermal modeling
        if self.params['thermal_effects']:
            self._initialize_temperature_map(array)
        
        # Main simulation loop
        for step in range(steps):
            # Apply voltages and get currents
            col_currents = array.apply_crossbar_voltages(
                row_voltages=input_voltages,
                col_voltages=col_voltages,
                dt=dt
            )
            
            # Update temperature if modeling thermal effects
            if self.params['thermal_effects']:
                self._update_temperature_map(array, input_voltages, col_currents)
            
            # Record states if requested
            if record_states:
                recordings['col_currents'][step] = col_currents
                recordings['memristor_states'][step] = array.get_state_matrix()
        
        # Final readout
        col_currents = array.apply_crossbar_voltages(
            row_voltages=input_voltages,
            col_voltages=col_voltages,
            dt=dt
        )
        
        # Add read noise
        if self.params['read_noise'] > 0:
            noise = np.random.normal(0, self.params['read_noise'], size=cols)
            col_currents *= (1.0 + noise)
        
        # Update statistics
        self.stats['read_operations'] += rows * cols
        
        # Estimate energy consumption (V²/R * t)
        avg_voltage = np.mean(np.abs(input_voltages))
        avg_resistance = np.mean(array.get_resistance_matrix())
        energy = rows * cols * (avg_voltage**2 / avg_resistance) * simulation_time
        self.stats['energy_consumed'] += energy
        
        return col_currents, recordings
    
    def simulate_programming_operation(
        self,
        array: MemristorArray,
        target_resistances: np.ndarray,
        pulse_width: float = 1e-3,
        verify: bool = True
    ) -> Tuple[np.ndarray, float]:
        """
        Simulate programming operation to target resistance values.
        
        Args:
            array: Memristor array
            target_resistances: Target resistance matrix
            pulse_width: Programming pulse width in seconds
            verify: Whether to verify after programming
            
        Returns:
            Achieved resistance matrix and programming accuracy
        """
        rows, cols = array.rows, array.cols
        
        if target_resistances.shape != (rows, cols):
            raise ValueError(f"Target shape {target_resistances.shape} does not match array shape ({rows}, {cols})")
        
        # Create programmer
        programmer = MemristorProgrammer(
            pulse_width=pulse_width,
            max_iterations=10,
            tolerance=0.05
        )
        
        # Apply device-to-device variations to target values
        if self.params['write_variations'] > 0:
            variation = np.random.normal(1.0, self.params['write_variations'], size=(rows, cols))
            effective_targets = target_resistances * variation
        else:
            effective_targets = target_resistances.copy()
        
        # Program array
        max_error = programmer.program_array(array, effective_targets, verify)
        
        # Get achieved resistance values
        achieved_resistances = array.get_resistance_matrix()
        
        # Update statistics
        self.stats['write_operations'] += rows * cols
        
        # Estimate energy consumption (higher for programming)
        avg_voltage = 0.5  # Typical programming voltage
        avg_resistance = np.mean(achieved_resistances)
        energy = rows * cols * (avg_voltage**2 / avg_resistance) * pulse_width * 10  # Factor for multiple pulses
        self.stats['energy_consumed'] += energy
        
        # Calculate accuracy
        relative_error = np.abs(achieved_resistances - target_resistances) / target_resistances
        accuracy = 1.0 - np.mean(relative_error)
        
        return achieved_resistances, accuracy
    
    def _initialize_temperature_map(self, array: MemristorArray) -> None:
        """
        Initialize temperature map for thermal modeling.
        
        Args:
            array: Memristor array
        """
        rows, cols = array.rows, array.cols
        self.temperature_map = np.ones((rows, cols)) * self.base_temperature
    
    def _update_temperature_map(
        self,
        array: MemristorArray,
        input_voltages: np.ndarray,
        col_currents: np.ndarray
    ) -> None:
        """
        Update temperature map based on power dissipation.
        
        Args:
            array: Memristor array
            input_voltages: Input voltages
            col_currents: Output currents
        """
        if self.temperature_map is None:
            self._initialize_temperature_map(array)
        
        rows, cols = array.rows, array.cols
        
        # Estimate power dissipation
        resistance_matrix = array.get_resistance_matrix()
        power_matrix = np.zeros((rows, cols))
        
        # Simple model: distribute column current based on conductance
        conductance_matrix = 1.0 / resistance_matrix
        total_conductance = np.sum(conductance_matrix, axis=0)
        
        for c in range(cols):
            if total_conductance[c] > 0:
                for r in range(rows):
                    # Current through this memristor
                    current_fraction = conductance_matrix[r, c] / total_conductance[c]
                    current = col_currents[c] * current_fraction
                    
                    # Voltage across this memristor
                    voltage = input_voltages[r]
                    
                    # Power dissipation
                    power_matrix[r, c] = voltage * current
        
        # Update temperature
        # Simple thermal model: temperature rises proportional to power
        thermal_conductivity = 0.01  # W/K
        temperature_rise = power_matrix / thermal_conductivity
        
        # Apply thermal diffusion (simple average with neighbors)
        diffusion_factor = 0.2
        temp_diffused = np.zeros_like(self.temperature_map)
        
        for r in range(rows):
            for c in range(cols):
                # Get neighbor coordinates
                neighbors = []
                if r > 0:
                    neighbors.append((r-1, c))
                if r < rows-1:
                    neighbors.append((r+1, c))
                if c > 0:
                    neighbors.append((r, c-1))
                if c < cols-1:
                    neighbors.append((r, c+1))
                
                # Average temperature with neighbors
                neighbor_temp = np.mean([self.temperature_map[nr, nc] for nr, nc in neighbors])
                temp_diffused[r, c] = (1 - diffusion_factor) * self.temperature_map[r, c] + diffusion_factor * neighbor_temp
        
        # Update temperature map
        self.temperature_map = temp_diffused + temperature_rise
        
        # Apply cooling (Newton's law of cooling)
        cooling_rate = 0.1
        self.temperature_map = self.base_temperature + (self.temperature_map - self.base_temperature) * (1 - cooling_rate)
        
        # Update memristor models if they support temperature dependence
        for r in range(rows):
            for c in range(cols):
                if hasattr(array.memristors[r, c], 'set_temperature'):
                    array.memristors[r, c].set_temperature(self.temperature_map[r, c])


def initialize_memristor_arrays(config=None):
    """
    Initialize the memristor array module with the given configuration.
    
    Args:
        config: Optional configuration dictionary
        
    Returns:
        Initialization status dictionary
    """
    if config is None:
        config = {}
    
    status = {
        'initialized': True,
        'config': config
    }
    
    # Set up default parameters
    default_r_on = config.get('default_r_on', 100.0)
    default_r_off = config.get('default_r_off', 10000.0)
    
    # Initialize random number generator if seed is provided
    if 'random_seed' in config:
        np.random.seed(config['random_seed'])
    
    # Add supported memristor types to status
    status['supported_memristor_types'] = [mt.value for mt in MemristorType]
    
    # Configure backend
    compute_mode = config.get('compute_mode', 'accurate')
    if compute_mode == 'fast':
        status['compute_mode'] = 'fast'
        # In fast mode, we use simplified models
        logger.info("Using fast computation mode with simplified memristor models")
    else:
        status['compute_mode'] = 'accurate'
        logger.info("Using accurate computation mode with detailed memristor models")
    
    # Check for hardware acceleration
    has_gpu = False
    try:
        import torch
        has_gpu = torch.cuda.is_available()
        status['gpu_available'] = has_gpu
        if has_gpu:
            logger.info("GPU acceleration available for memristor array simulations")
        else:
            logger.info("No GPU acceleration available")
    except ImportError:
        status['gpu_available'] = False
        logger.info("Torch not available for GPU acceleration")
    
    # Log initialization
    logger.info(f"Memristor Array module initialized with compute mode: {status['compute_mode']}")
    
    return status


# Example usage and demo functions

def create_example_array(rows=8, cols=8, r_on=100.0, r_off=10000.0):
    """
    Create an example memristor array for testing.
    
    Args:
        rows: Number of rows
        cols: Number of columns
        r_on: Low resistance state
        r_off: High resistance state
        
    Returns:
        Configured MemristorArray
    """
    # Create array
    array = MemristorArray(
        rows=rows,
        cols=cols,
        r_on=r_on,
        r_off=r_off,
        model_type=MemristorType.NONLINEAR
    )
    
    # Program with checkerboard pattern
    checker_pattern = np.zeros((rows, cols))
    for i in range(rows):
        for j in range(cols):
            if (i + j) % 2 == 0:
                checker_pattern[i, j] = 1.0
    
    # Set states based on pattern
    array.set_state_matrix(checker_pattern)
    
    return array


def demo_hysteresis():
    """
    Demonstrate memristor hysteresis behavior.
    """
    memristor = Memristor(
        r_on=100.0,
        r_off=10000.0,
        r_init=5000.0,
        model_type=MemristorType.NONLINEAR,
        threshold_voltage=0.2
    )
    
    # Run hysteresis simulation
    voltages, currents = memristor.hysteresis_loop(
        v_max=0.5,
        freq=1.0,
        cycles=2,
        points_per_cycle=100
    )
    
    # Plot hysteresis loop
    plt.figure(figsize=(10, 6))
    plt.plot(voltages, currents)
    plt.xlabel('Voltage (V)')
    plt.ylabel('Current (A)')
    plt.title('Memristor Hysteresis Loop')
    plt.grid(True)
    plt.tight_layout()
    plt.show()


def demo_vector_matrix_multiply():
    """
    Demonstrate vector-matrix multiplication using a memristor crossbar.
    """
    rows, cols = 4, 4
    
    # Create crossbar
    crossbar = CrossbarArray(
        rows=rows,
        cols=cols,
        r_on=100.0,
        r_off=10000.0
    )
    
    # Set random weights
    weights = np.random.rand(rows, cols)
    
    # Map weights to conductance
    conductance = crossbar.map_weights_to_conductance(
        weights=weights,
        weight_max=1.0,
        weight_min=0.0,
        differential_pairs=False
    )
    
    # Program crossbar
    crossbar.program_conductance(target_conductance=conductance)
    
    # Generate random input vector
    input_vector = np.random.rand(rows)
    
    # Perform vector-matrix multiplication
    output = crossbar.vector_matrix_multiply(input_vector)
    
    # Compare with numpy result
    numpy_output = np.dot(input_vector, weights)
    
    print("Input vector:", input_vector)
    print("Crossbar output:", output)
    print("Numpy output:", numpy_output)
    print("Difference:", np.abs(output - numpy_output))


def demo_neural_network_layer():
    """
    Demonstrate a simple neural network layer using memristor crossbar.
    """
    # Network dimensions
    input_size = 10
    output_size = 5
    
    # Create vector-matrix multiplier
    vmm = VectorMatrixMultiplier(
        rows=input_size,
        cols=output_size,
        precision_bits=8,
        use_differential_pairs=True
    )
    
    # Generate random weights
    weights = np.random.uniform(-0.5, 0.5, (input_size, output_size))
    
    # Program weights
    vmm.program_weights(weights)
    
    # Define activation function (ReLU)
    def relu(x):
        return np.maximum(0, x)
    
    # Test with random input
    input_data = np.random.rand(input_size)
    
    # Forward pass
    output = vmm.forward(input_data, apply_activation=relu)
    
    # Compare with numpy
    numpy_output = relu(np.dot(input_data, weights))
    
    print("Input:", input_data)
    print("Memristor output:", output)
    print("Numpy output:", numpy_output)
    print("Mean absolute error:", np.mean(np.abs(output - numpy_output)))
    
    # Get efficiency metrics
    metrics = vmm.get_efficiency_metrics()
    print("Efficiency metrics:", metrics)


if __name__ == "__main__":
    # Simple demo of memristor behavior
    memristor = Memristor(
        r_on=100.0, 
        r_off=10000.0,
        model_type=MemristorType.NONLINEAR
    )
    
    # Apply voltage over time
    print("Demonstrating memristor behavior:")
    print(f"Initial resistance: {memristor.r:.1f} ohms, state: {memristor.state:.3f}")
    
    # Positive voltage (SET operation - decrease resistance)
    current = memristor.apply_voltage(0.5, 0.001)  # 0.5V for 1ms
    print(f"After +0.5V: R={memristor.r:.1f} ohms, state={memristor.state:.3f}, current={current*1e6:.2f}μA")
    
    # Negative voltage (RESET operation - increase resistance)
    current = memristor.apply_voltage(-0.5, 0.001)  # -0.5V for 1ms
    print(f"After -0.5V: R={memristor.r:.1f} ohms, state={memristor.state:.3f}, current={current*1e6:.2f}μA")
    
    # Run hysteresis demo if matplotlib is available
    try:
        demo_hysteresis()
    except Exception as e:
        print(f"Hysteresis demo failed: {e}")
    
    # Run vector-matrix multiplication demo
    try:
        demo_vector_matrix_multiply()
    except Exception as e:
        print(f"Vector-matrix multiplication demo failed: {e}")
    
    # Run neural network layer demo
    try:
        demo_neural_network_layer()
    except Exception as e:
        print(f"Neural network layer demo failed: {e}")